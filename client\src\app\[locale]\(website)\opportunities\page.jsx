import OpportunityCard from "@/features/opportunity/components/opportunityFrontOffice/OpportunityCard";
import initTranslations from "@/app/i18n";
import { axiosGetJsonSSR } from "@/config/axios";
import BannerComponents from "@/components/sections/BannerComponents";
import banner from "../../../../assets/images/industries/banner/bannerOpportunities.png";

export async function generateMetadata({ params: { locale }, searchParams }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }opportunities/`;
  const languages = {
    fr: `https://www.pentabell.com/fr/opportunities/`,
    en: `https://www.pentabell.com/opportunities/`,
    "x-default": `https://www.pentabell.com/opportunities/`,
  };
  const { t } = await initTranslations(locale, ["aboutUs", "global"]);

  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/opportunities`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[locale]?.metaTitle,
        description: res?.data?.data?.versions[locale]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("global:metaTitleOpportunity"),
    description: t("global:metaDescriptionOpportunity"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots:
      Object.keys(searchParams).length > 0
        ? "follow, noindex"
        : "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

export async function page({ searchParams, params }) {
  const { t } = await initTranslations(params.locale, [
    "opportunities",
    "global",
  ]);

  try {
    const countryResponse = await axiosGetJsonSSR.get("/countries");
    const countries = countryResponse.data || [];

    const isListView = searchParams.list === "Yes";

    const res = await axiosGetJsonSSR.get("/opportunities", {
      params: {
        pageSize: 10,
        pageNumber: searchParams.pageNumber,
        country: searchParams.country || "",
        keyWord: searchParams.keyWord || "",
        visibility: "Public",
        levelOfExperience: searchParams.levelOfExperience || "",
        industry: searchParams.industry?.replace("IT", "It") || "",
        contractType: searchParams.contractType || "",
        language: params.locale,
        jobDescriptionLanguages: searchParams.jobDescriptionLanguages || "",
        opportunityType:
          searchParams.opportunityType === "In House"
            ? searchParams.opportunityType
            : "",
        exclude: searchParams.opportunityType !== "In House" && "true",
      },
    });

    const initialOpportunities = res.data;

    return (
      <div id="opportunities-page">
        <BannerComponents
          bannerImg={banner}
          height={"70vh"}
          title={t("global:titleOpportunity")}
          altImg={t("global:altOpportunity")}
        />
        <OpportunityCard
          language={params.locale}
          countries={countries}
          initialOpportunities={initialOpportunities}
          searchParams={searchParams}
          typeCategory
          initialListView={isListView}
        />
      </div>
    );
  } catch (error) {
    console.log("error", error);
  }
}

export default page;
