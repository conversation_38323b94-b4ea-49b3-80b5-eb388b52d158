"use client";
import { useTranslation } from "react-i18next";
import HomePage from "../../../../../features/application/component/HomePage";
import { useGetUserData } from "@/features/user/hooks/updateProfile.hooks";

const page = ({locale}) => {
  const { t } = useTranslation();
  const { data } = useGetUserData(t);
 
  return (
    <>
      <div>
        
        <p className="heading-h2 semi-bold">Hello {data?.firstName } 👋🏼, </p>
      </div>
      <HomePage />
    </>
  );
};

export default page;
