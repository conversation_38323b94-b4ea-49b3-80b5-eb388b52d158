import { useMutation, useQuery, useQueryClient } from "react-query";

import {
  getArticleById,
  getSlugBySlug,
  getarchivedArticles,
  getGlossaryDashboard,
  updatearticleall,
  getArticleByIdAll,
  desarchiveversions,
  createGlossary,
  getGlossaryByUrlAndlanguage,
  updateGlossaryByLanguageAndId,
  archiveGlossary,
  deleteGlossary,
} from "../services/glossaries.service";
import { websiteRoutesList } from "@/helpers/routesList";

export const useCreateGlossary = () => {
  return useMutation({
    mutationFn: (body) => {
      return createGlossary(body);
    },
  });
};

export const useUpdateGlossaryByLanguageAndId = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (updateData) => {
      return updateGlossaryByLanguageAndId(updateData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries("glossary");
    },
    onError: (err) => {
      console.error("Error updating glossary:", err);
      err.message = "";
    },
  });
};

export const usearchivedarticle = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ language, id, archive }) => {
      return desarchiveversions(language, id, archive);
    },
    onError: (err) => {
      console.error("Error during mutation", err);
      err.message = "";
    },
  });
};

export const useUpdateArticleAll = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data, id) => {
      return updatearticleall(data, id);
    },
    onError: (err) => {
      err.message = "";
    },
  });
};

export const useGetarchivedArticles = (body) => {
  // const dispatch = useDispatch();
  // const navigate = useNavigate();
  return useQuery("article", async () => {
    try {
      const data = await getarchivedArticles(body);
      return data;
    } catch (err) {
      if (err.response.status === 401) {
        // dispatch(logout);
        localStorage.removeItem("user");
        window.location.href = "/";
      }
    }
  });
};

export const useGetGlossariesDashboard = (body) => {
  return useQuery(`articles${body.language}`, async () => {
    const data = await getGlossaryDashboard(body);
    return data;
  });
};

export const useGetGlossaryByUrlAndLanguage = (body, options = {}) => {
  return useQuery(
    ["glossary", body],
    async () => {
      try {
        const data = await getGlossaryByUrlAndlanguage(body);
        return data;
      } catch (error) {}
    },
    {
      onError: (error) => {
        console.error("Error fetching article:", error.message);
      },
      ...options,
    }
  );
};

export const useGetSlugBySlug = (body) => {
  // const navigate = useNavigate();
  return useQuery(
    ["article", body],
    async () => {
      try {
        const data = await getSlugBySlug(body);
        return data;
      } catch (error) {
        if (error.response && error.response.status === 404) {
          if (body.language === "en")
            window.location.href = `/${websiteRoutesList.blog.route}/${body.urlArticle}/`;
          else
            window.location.href = `/fr/${websiteRoutesList.blog.route}/${body.urlArticle}/`;
        }

        throw error;
      }
    },
    {
      onError: (error) => {
        console.error("Error fetching article:", error.message);
        // You can add more error handling logic here if needed
      },
    }
  );
};

export const useGetArticleById = (articleId, language) => {
  return useQuery(["article", articleId, language], async () => {
    const data = await getArticleById(articleId, language);
    return data;
  });
};

export const useGetArticleByIdAll = (articleId) => {
  return useQuery(["articleall", articleId], async () => {
    const data = await getArticleByIdAll(articleId);
    return data;
  });
};

/**
 * Hook for archiving/unarchiving a glossary
 * @returns {Object} Mutation object for archiving/unarchiving a glossary
 */
export const useArchiveGlossary = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ language, id }) => archiveGlossary(language, id),
    onSuccess: () => {
      // Invalidate and refetch glossaries list
      queryClient.invalidateQueries("glossaries");
    },
    onError: (error) => {
      console.error("Error archiving glossary:", error);
    },
  });
};

/**
 * Hook for deleting a glossary
 * @returns {Object} Mutation object for deleting a glossary
 */
export const useDeleteGlossary = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ language, id }) => deleteGlossary(language, id),
    onSuccess: () => {
      // Invalidate and refetch glossaries list
      queryClient.invalidateQueries("glossaries");
    },
    onError: (error) => {
      console.error("Error deleting glossary:", error);
    },
  });
};
