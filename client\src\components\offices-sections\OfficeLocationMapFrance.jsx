import { Contain<PERSON>, <PERSON><PERSON>, <PERSON> } from "@mui/material";
import CustomButton from "../ui/CustomButton";
import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";
import { websiteRoutesList } from "@/helpers/routesList";

function OfficeLocationMapFrance({ t }) {
  return (
    <Container id="office-location-map" className="custom-max-width">
      <Grid
        className="container"
        justifyContent="space-between"
        container
        spacing={0}
      >
        <Grid item xs={12} sm={6}>
          <div className="content">
            <p className="heading-h2 text-white">
              {t("france:officeLocation:label")}
            </p>
            <p className="sub-heading text-white">
              {t("france:officeLocation:title")}
            </p>
            <div>
              <p className="paragraph text-white" key={"tn"}>
                <span>
                  <SvglocationPin />
                </span>

                {t("france:officeLocation:address")}
              </p>
              <p className="paragraph text-white">
                <span>
                  <SvgcallUs />
                </span>

                {t("france:officeLocation:tel1")}
              </p>

              <p className="paragraph text-white">
                <span>
                  <Svgemail />
                </span>
                {t("france:officeLocation:mail")}
              </p>
            </div>
            <Link
              href={`#service-page-form`}
              className={"btn btn-outlined white"}
            >
              {t("france:officeLocation:talk")}
            </Link>
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <div className="map-frame">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d84032.69737423373!2d2.317292!3d48.838723!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47e671857129906d%3A0xecb063098b068e88!2sPentabell%20France!5e0!3m2!1sfr!2sus!4v1728638942092!5m2!1sfr!2sus"
              priority
              referrerpolicy="no-referrer-when-downgrade"
            ></iframe>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default OfficeLocationMapFrance;
