exports.id=3969,exports.ids=[3969],exports.modules={65302:(e,s,n)=>{var t={"./en/Algeria.json":[20937,937],"./en/Egypte.json":[14626,4626],"./en/ForumAfricaFrance.json":[97249,7249],"./en/HomeDashboard.json":[93595,3595],"./en/PentabellSalestraining.json":[36253,6253],"./en/ProfessionalInformations.json":[22333,2333],"./en/QHSEEXPO.json":[64200,4200],"./en/Tunisia.json":[58214,8214],"./en/aboutUs.json":[99433,9705],"./en/activation.json":[55968,5968],"./en/africa.json":[35767,5767],"./en/aiSourcingService.json":[36708,6708],"./en/application.json":[49647,9647],"./en/certification.json":[25410,5410],"./en/comments.json":[87462,6383],"./en/consultingServices.json":[18916,8916],"./en/contact.json":[97004,7004],"./en/contactUs.json":[54122,4122],"./en/country.json":[15267,5267],"./en/createArticle.json":[56995,6995],"./en/createOpportunity.json":[51173,1173],"./en/directHiringService.json":[77021,7021],"./en/dubai.json":[3185,3185],"./en/education.json":[81959,1959],"./en/europe.json":[7366,7366],"./en/event.json":[97450,7450],"./en/eventDetails.json":[22836,2836],"./en/eventDetailsGitex.json":[76641,6641],"./en/eventDetailsLeap.json":[53594,5727],"./en/eventDetailsLibya.json":[26930,6930],"./en/eventForm.json":[71938,1938],"./en/experience.json":[25346,5346],"./en/expertCare.json":[72461,2461],"./en/favourite.json":[76265,6265],"./en/footer.json":[65868,5868],"./en/forgotPassword.json":[23404,3404],"./en/france.json":[67979,7979],"./en/getInTouch.json":[65378,5378],"./en/global.json":[24709,4709],"./en/guides.json":[85166,5166],"./en/homePage.json":[17003,7003],"./en/iraq.json":[63526,3526],"./en/joinUs.json":[98827,8827],"./en/ksa.json":[7596,7596],"./en/libya.json":[72208,2208],"./en/listArticle.json":[80463,463],"./en/listCategory.json":[16328,6328],"./en/listCommentaire.json":[51460,1460],"./en/listopportunity.json":[80318,318],"./en/listusers.json":[55136,5136],"./en/login.json":[50499,499],"./en/mainService.json":[33925,3925],"./en/menu.json":[34055,4055],"./en/messages.json":[76218,5850],"./en/middleeast.json":[69069,7106],"./en/morocco.json":[75809,5809],"./en/opportunities.json":[23263,3263],"./en/payrollService.json":[91621,1621],"./en/personalinformation.json":[23758,3758],"./en/qatar.json":[65981,5981],"./en/register.json":[47593,7593],"./en/resetPassword.json":[44310,4310],"./en/resumes.json":[25489,5489],"./en/seoSettings.json":[85192,5192],"./en/servicesByCountry.json":[50962,962],"./en/settings.json":[57457,7457],"./en/sidebar.json":[65250,5250],"./en/sliders.json":[34187,4187],"./en/statisticsApp.json":[14941,4941],"./en/statisticsDash.json":[47299,7299],"./en/statsDash.json":[89166,9166],"./en/statsTotalNumbers.json":[62496,2496],"./en/steps.json":[51777,1777],"./en/technicalAssistanceService.json":[51831,1831],"./en/users.json":[57410,7410],"./en/validations.json":[17508,1306],"./fr/Algeria.json":[57885,7885],"./fr/Egypte.json":[84684,4684],"./fr/ForumAfricaFrance.json":[90802,802],"./fr/HomeDashboard.json":[59678,9678],"./fr/PentabellSalestraining.json":[73144,3144],"./fr/ProfessionalInformations.json":[93183,3183],"./fr/QHSEEXPO.json":[53662,3662],"./fr/Tunisia.json":[30890,890],"./fr/aboutUs.json":[40492,492],"./fr/activation.json":[11481,1481],"./fr/africa.json":[76187,6187],"./fr/aiSourcingService.json":[7526,7526],"./fr/application.json":[87800,7800],"./fr/certification.json":[28928,8928],"./fr/comments.json":[29589,9589],"./fr/consultingServices.json":[99595,9595],"./fr/contact.json":[58350,8350],"./fr/contactUs.json":[43851,3851],"./fr/country.json":[9061,9061],"./fr/createArticle.json":[7849,7849],"./fr/createOpportunity.json":[41465,1465],"./fr/directHiringService.json":[52400,2400],"./fr/dubai.json":[77592,7592],"./fr/education.json":[30230,230],"./fr/europe.json":[39248,9248],"./fr/event.json":[36133,6133],"./fr/eventDetails.json":[96101,6101],"./fr/eventDetailsGitex.json":[90514,514],"./fr/eventDetailsLeap.json":[94597,7140],"./fr/eventDetailsLibya.json":[8384,7633],"./fr/eventForm.json":[24881,4881],"./fr/experience.json":[87418,7418],"./fr/expertCare.json":[81173,4299],"./fr/favourite.json":[47911,7911],"./fr/footer.json":[17997,7997],"./fr/forgotPassword.json":[34914,4914],"./fr/france.json":[42768,2768],"./fr/getInTouch.json":[3013,3013],"./fr/global.json":[93156,3156],"./fr/guides.json":[53544,3544],"./fr/homePage.json":[43175,3175],"./fr/iraq.json":[48597,8597],"./fr/joinUs.json":[37070,7070],"./fr/ksa.json":[24098,4098],"./fr/libya.json":[72634,2634],"./fr/listArticle.json":[98225,8225],"./fr/listCategory.json":[61604,6881],"./fr/listCommentaire.json":[31623,1623],"./fr/listopportunity.json":[24901,5490],"./fr/listusers.json":[61464,1464],"./fr/login.json":[12052,2052],"./fr/mainService.json":[25651,5651],"./fr/menu.json":[25362,5362],"./fr/messages.json":[23021,3021],"./fr/middleeast.json":[16308,6308],"./fr/morocco.json":[94333,967],"./fr/opportunities.json":[98658,8658],"./fr/payrollService.json":[30235,235],"./fr/personalinformation.json":[70232,471],"./fr/qatar.json":[99880,9880],"./fr/register.json":[11123,1123],"./fr/resetPassword.json":[51730,1730],"./fr/resumes.json":[16095,6095],"./fr/seoSettings.json":[5056,5056],"./fr/servicesByCountry.json":[58078,6323],"./fr/settings.json":[94889,9731],"./fr/sidebar.json":[50134,134],"./fr/sliders.json":[83623,3623],"./fr/statisticsApp.json":[82576,2576],"./fr/statisticsDash.json":[49308,9308],"./fr/statsDash.json":[53739,3739],"./fr/statsTotalNumbers.json":[2692,6725],"./fr/steps.json":[25629,5629],"./fr/technicalAssistanceService.json":[98294,8294],"./fr/users.json":[72169,2169],"./fr/validations.json":[96489,6489]};function o(e){if(!n.o(t,e))return Promise.resolve().then(()=>{var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s});var s=t[e],o=s[0];return n.e(s[1]).then(()=>n.t(o,19))}o.keys=()=>Object.keys(t),o.id=65302,e.exports=o},89601:(e,s,n)=>{var t={"./en/Algeria.json":[12538,2538],"./en/Egypte.json":[84978,4978],"./en/ForumAfricaFrance.json":[20010,10],"./en/HomeDashboard.json":[12933,2933],"./en/PentabellSalestraining.json":[56448,6448],"./en/ProfessionalInformations.json":[71042,1042],"./en/QHSEEXPO.json":[73818,3818],"./en/Tunisia.json":[38134,8134],"./en/aboutUs.json":[94990,4990],"./en/activation.json":[95264,5264],"./en/africa.json":[58932,8932],"./en/aiSourcingService.json":[32843,2843],"./en/application.json":[30260,260],"./en/certification.json":[93848,3848],"./en/comments.json":[18520,8520],"./en/consultingServices.json":[74846,223],"./en/contact.json":[90414,414],"./en/contactUs.json":[99547,9547],"./en/country.json":[26113,6113],"./en/createArticle.json":[81763,1763],"./en/createOpportunity.json":[88490,8490],"./en/directHiringService.json":[94703,4703],"./en/dubai.json":[18414,8414],"./en/education.json":[5657,5657],"./en/europe.json":[25856,5856],"./en/event.json":[31813,1813],"./en/eventDetails.json":[8850,8850],"./en/eventDetailsGitex.json":[37882,7882],"./en/eventDetailsLeap.json":[76791,6791],"./en/eventDetailsLibya.json":[95572,5572],"./en/eventForm.json":[1671,1671],"./en/experience.json":[76824,6824],"./en/expertCare.json":[33726,3726],"./en/favourite.json":[43192,3192],"./en/footer.json":[11326,1326],"./en/forgotPassword.json":[50701,701],"./en/france.json":[91506,1506],"./en/getInTouch.json":[51842,2732],"./en/global.json":[21842,1842],"./en/guides.json":[54952,4952],"./en/homePage.json":[75132,8737],"./en/iraq.json":[11604,1604],"./en/joinUs.json":[5029,7508],"./en/ksa.json":[62697,2697],"./en/libya.json":[22374,2374],"./en/listArticle.json":[36597,6597],"./en/listCategory.json":[70577,577],"./en/listCommentaire.json":[4256,4256],"./en/listopportunity.json":[10347,347],"./en/listusers.json":[23423,3423],"./en/login.json":[89962,9962],"./en/mainService.json":[41718,1718],"./en/menu.json":[99255,9255],"./en/messages.json":[50261,261],"./en/middleeast.json":[24711,4711],"./en/morocco.json":[87083,7083],"./en/opportunities.json":[29437,9437],"./en/payrollService.json":[10978,978],"./en/personalinformation.json":[28455,8455],"./en/qatar.json":[5894,5894],"./en/register.json":[83491,3491],"./en/resetPassword.json":[68966,8966],"./en/resumes.json":[98521,8521],"./en/seoSettings.json":[80518,518],"./en/servicesByCountry.json":[64592,4592],"./en/settings.json":[93215,3215],"./en/sidebar.json":[52909,2909],"./en/sliders.json":[54286,4286],"./en/statisticsApp.json":[38374,8374],"./en/statisticsDash.json":[54297,4297],"./en/statsDash.json":[55555,5555],"./en/statsTotalNumbers.json":[1562,1562],"./en/steps.json":[57282,7282],"./en/technicalAssistanceService.json":[4260,4260],"./en/users.json":[52689,2689],"./en/validations.json":[19784,9784],"./fr/Algeria.json":[55248,5248],"./fr/Egypte.json":[54316,4316],"./fr/ForumAfricaFrance.json":[56430,6430],"./fr/HomeDashboard.json":[78747,8747],"./fr/PentabellSalestraining.json":[21823,1823],"./fr/ProfessionalInformations.json":[50843,7750],"./fr/QHSEEXPO.json":[73886,3886],"./fr/Tunisia.json":[27872,7872],"./fr/aboutUs.json":[10687,687],"./fr/activation.json":[38889,8889],"./fr/africa.json":[42650,2650],"./fr/aiSourcingService.json":[98575,8575],"./fr/application.json":[95318,5318],"./fr/certification.json":[34058,4058],"./fr/comments.json":[61352,1352],"./fr/consultingServices.json":[44235,4235],"./fr/contact.json":[84685,4685],"./fr/contactUs.json":[55603,5603],"./fr/country.json":[61048,1048],"./fr/createArticle.json":[65061,5061],"./fr/createOpportunity.json":[86767,6767],"./fr/directHiringService.json":[10983,983],"./fr/dubai.json":[75239,5239],"./fr/education.json":[26325,6325],"./fr/europe.json":[225,225],"./fr/event.json":[1587,1587],"./fr/eventDetails.json":[43259,3259],"./fr/eventDetailsGitex.json":[71514,1514],"./fr/eventDetailsLeap.json":[24394,1976],"./fr/eventDetailsLibya.json":[10065,65],"./fr/eventForm.json":[80544,544],"./fr/experience.json":[15086,5086],"./fr/expertCare.json":[79980,9980],"./fr/favourite.json":[17744,7744],"./fr/footer.json":[55053,5053],"./fr/forgotPassword.json":[98623,8623],"./fr/france.json":[26043,6043],"./fr/getInTouch.json":[23611,3611],"./fr/global.json":[96831,6831],"./fr/guides.json":[96698,6698],"./fr/homePage.json":[46592,6592],"./fr/iraq.json":[91679,1679],"./fr/joinUs.json":[8323,8323],"./fr/ksa.json":[41098,1098],"./fr/libya.json":[75596,5596],"./fr/listArticle.json":[43856,3856],"./fr/listCategory.json":[62845,2845],"./fr/listCommentaire.json":[49089,9089],"./fr/listopportunity.json":[18057,8057],"./fr/listusers.json":[27683,7683],"./fr/login.json":[46205,6205],"./fr/mainService.json":[72380,2380],"./fr/menu.json":[82578,2578],"./fr/messages.json":[62107,8029],"./fr/middleeast.json":[44195,4195],"./fr/morocco.json":[37701,7701],"./fr/opportunities.json":[15583,5583],"./fr/payrollService.json":[74219,4219],"./fr/personalinformation.json":[70217,217],"./fr/qatar.json":[3206,3206],"./fr/register.json":[6109,6109],"./fr/resetPassword.json":[16720,9866],"./fr/resumes.json":[99666,9666],"./fr/seoSettings.json":[88283,8283],"./fr/servicesByCountry.json":[67696,7696],"./fr/settings.json":[24346,4346],"./fr/sidebar.json":[22641,2641],"./fr/sliders.json":[80235,4136],"./fr/statisticsApp.json":[58850,9964],"./fr/statisticsDash.json":[96902,6902],"./fr/statsDash.json":[14410,3594],"./fr/statsTotalNumbers.json":[1639,1639],"./fr/steps.json":[49245,9245],"./fr/technicalAssistanceService.json":[74651,4651],"./fr/users.json":[4648,4648],"./fr/validations.json":[42277,2277]};function o(e){if(!n.o(t,e))return Promise.resolve().then(()=>{var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s});var s=t[e],o=s[0];return n.e(s[1]).then(()=>n.t(o,19))}o.keys=()=>Object.keys(t),o.id=89601,e.exports=o},58359:()=>{},93739:()=>{},38106:(e,s,n)=>{"use strict";n.d(s,{Z:()=>a});var t,o=n(95746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var n=arguments[s];for(var t in n)({}).hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e}).apply(null,arguments)}let a=e=>o.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),t||(t=o.createElement("path",{fill:"#E5F0FC",d:"m12 9 4.242 4.242-1.414 1.414L12 11.828 9.17 14.656l-1.414-1.414z"})))},16376:(e,s,n)=>{"use strict";n.d(s,{Z:()=>a});var t,o=n(95746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var n=arguments[s];for(var t in n)({}).hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e}).apply(null,arguments)}let a=e=>o.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),t||(t=o.createElement("path",{fill:"#E5F0FC",d:"m12 15-4.242-4.242 1.414-1.414 2.829 2.828 2.828-2.828 1.414 1.414z"})))},13516:e=>{"use strict";e.exports={locales:["en","fr"],defaultLocale:"en",localeDetection:!1}},81373:(e,s,n)=>{"use strict";n.d(s,{default:()=>d});var t=n(10326),o=n(52210),r=n(18562),a=n(55037),i=n(48173),l=n(13516),c=n.n(l);async function u(e,s,t,o){return(t=t||(0,r.Fs)()).use(a.D),o||t.use((0,i.Z)((e,s)=>n(89601)(`./${e}/${s}.json`))),await t.init({lng:e||c().defaultLocale,resources:o,fallbackLng:c().defaultLocale,supportedLngs:c().locales,defaultNS:s[0],fallbackNS:s[0],ns:s,preload:o?[]:c().locales}),{i18n:t,resources:t.services.resourceStore.data,t:t.t}}function d({children:e,locale:s,namespaces:n,resources:a}){let i=(0,r.Fs)();return u(s,n,i,a),t.jsx(o.a3,{i18n:i,children:e})}},67247:(e,s,n)=>{"use strict";n.d(s,{Z:()=>g});var t=n(10326),o=n(35047),r=n(52210),a=n(13516),i=n.n(a),l=n(50967),c=n(24521),u=n(23743),d=n(88441),j=n(77992),f=n(70580);let p={src:"/_next/static/media/fr.74833967.png",height:64,width:64,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAANlBMVEU7Qpvy+Pn/Q0xDSJv/Q0/8ho3/TVf9/v1MaXH/T1qChbv7g4qBhbs3PJT9+/jz+vqHi8RHTqZc5R8mAAAAEXRSTlP9+pya/via/QD9l5f1nJqa+mPEicQAAAAJcEhZcwAAAbsAAAG7ATrs4+IAAAArSURBVHicY+CAAgZmXi4+fm4mNgZBBgF2dlYWTgYGBh5GRhQGXAquGKYdAEKFAdc5P689AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},m={src:"/_next/static/media/en.99fa137c.png",height:64,width:64,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAQlBMVEVMaXF/iLnaJ0jATHLmaH3MUHHeKknBVnntXHADbczlYnfDT3IyVabUXXrPiqhigMOqut7ZYYHQW3+dptF3mdJ4mdJmSSWzAAAADnRSTlMArPyqp/z8/Pyr/KqtqYgUVz0AAAAJcEhZcwAAAdgAAAHYAfpcpnIAAAA6SURBVHicPcs3AgAgCASwswIW7P//qpNmD/B4Iy5LqWjax9y6QIGj5USgxDZyICw9c3RtqEWyE+P/vkYgAfJG1tb3AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8};function g({withFlag:e,onlyWebVersion:s}){let n=(0,u.Z)(),a=(0,d.Z)(n.breakpoints.down("md")),g=(0,d.Z)(n.breakpoints.down("sm")),{i18n:h}=(0,r.$G)(),v=h.language,A=(0,o.useRouter)(),b=(0,o.usePathname)(),y=b.split("/"),w=async e=>{document.cookie=`NEXT_LOCALE=${e};expires=${new Date(Date.now()+2592e6).toUTCString()};path=/`;let s=async(s,n,t)=>{try{let o=(await f.yX.get(`${s}/opposite/${v}/${n}`)).data.slug,r=o?`${"en"===e?"":`/${e}`}/${t}/${o}`:"en"===e?"":"/events";A.push(r)}catch(e){}},n=async(s,n,t)=>{try{let o=(await f.yX.get(`${s}/${v}/${t}/${n}`)).data.slug,r=o?`${"en"===e?"":`/${e}`}/blog/category/${o}`:b;A.push(r)}catch(e){}},t=(e,s)=>y[1]===e&&y.length===s||y[2]===e&&y.length===s+1;if(v!==e){if(t("opportunities",4))await s(l.Y.opportunity,y[y.length-2],"opportunities");else if(t("apply",4))await s(l.Y.opportunity,y[y.length-2],"apply");else if(t("blog",4))await s(l.Y.articles,y[y.length-2],"blog");else if(t("guides",4))await s(l.Y.guides,y[y.length-2],"guides");else{let s;(s="category",y[2]===s&&5===y.length||y[3]===s&&6===y.length)?await n(l.Y.category,y[y.length-2],"blog"):v!==i().defaultLocale||i().prefixDefault?A.push(b.replace(`/${v}`,`/${e}`)):A.push(`/${e}${b}`)}}A.refresh()},x=[{name:"En",onClick:()=>w("en"),flag:m,route:""},{name:"Fr",onClick:()=>w("fr"),flag:p,route:""}];return t.jsx(t.Fragment,{children:s?t.jsx(c.Z,{buttonLabel:"en"===v?"En":"Fr",selectedFlag:"en"===v?m:p,buttonHref:"",menuItems:x,withFlag:e}):g||a?t.jsx(j.Z,{buttonLabel:"en"===v?"En":"Fr",selectedFlag:"en"===v?m:p,buttonHref:"",locale:v,menuItems:x,withFlag:e}):t.jsx(c.Z,{buttonLabel:"en"===v?"En":"Fr",selectedFlag:"en"===v?p:m,buttonHref:"",menuItems:x,withFlag:e})})}},24521:(e,s,n)=>{"use strict";n.d(s,{Z:()=>m});var t=n(10326),o=n(17577),r=n(42265),a=n(16111),i=n(48467),l=n(37841),c=n(38106),u=n(16376),d=n(35047),j=n(28236),f=n(52210);let p=({buttonLabel:e,buttonHref:s,menuItems:n,subMenu:m,locale:g,withFlag:h,selectedFlag:v})=>{let A=(0,d.usePathname)(),{t:b}=(0,f.$G)(),[y,w]=(0,o.useState)(null),x=!!y,N=()=>A.includes("/blog/")&&!A.includes("/blog/category/")&&"/blog/"!==A&&"/fr/blog/"!==A,P=()=>{w(null)},E=(e,s)=>{e.stopPropagation(),s.onClick?s.onClick():void 0==s.subItems&&P()};return(0,t.jsxs)("div",{className:m?"sub-dropdown-menu":"dropdown-menu",children:[(0,t.jsxs)(r.Z,{className:A.includes(s)&&""!==s?"navbar-link dropdown-toggle active":N()?"navbar-link dropdown-toggle whiteBg":"navbar-link dropdown-toggle",id:"fade-button","aria-controls":x?"fade-menu":void 0,"aria-haspopup":"true","aria-expanded":x?"true":void 0,onClick:e=>{w(e.currentTarget)},children:[t.jsx(r.Z,{className:A.includes(s)&&""!==s?"dropdown-toggle-link active":N()?"dropdown-toggle-link whiteBg":"dropdown-toggle-link",href:s?(0,j.jJ)(g,s).replace("/en/","/"):A.replace("/en/","/"),locale:"en"===g?"en":"fr",children:h?t.jsx("img",{className:"flag-lang",src:v.src,width:26,height:22,disabled:!0,loading:"lazy"}):e}),x?t.jsx(c.Z,{}):t.jsx(u.Z,{})]}),t.jsx(a.Z,{id:"fade-menu",MenuListProps:{"aria-labelledby":"fade-button"},anchorEl:y,open:x,onClose:P,TransitionComponent:i.Z,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:m?"right":"left"},children:n.map((e,s)=>t.jsx(l.Z,{className:e.subItems?"sub-menu dropdown-item":"dropdown-item",onClick:s=>E(s,e),children:e.subItems?t.jsx(p,{buttonLabel:e?.i18nName?b(e?.i18nName):e.name,buttonHref:e.route,menuItems:e.subItems,subMenu:!0}):e.route?(0,t.jsxs)(r.Z,{href:(0,j.jJ)(g,e.route),locale:"en"===g?"en":"fr",className:A.includes(e.route)?"dropdown-item-link active":"dropdown-item-link",children:[e.icon??e.icon," ",e?.i18nName?b(e?.i18nName):e.name]}):t.jsx(r.Z,{className:"dropdown-item-link",href:"#",children:h?t.jsx("img",{className:"flag-lang",src:e.flag.src,width:26,height:22,loading:"lazy"}):e?.i18nName?b(e?.i18nName):e.name})},s))})]})},m=p},77992:(e,s,n)=>{"use strict";n.d(s,{Z:()=>h});var t=n(10326),o=n(17577),r=n(24003),a=n(25886),i=n(5041),l=n(84979),c=n(71411),u=n(38106),d=n(16376),j=n(90434),f=n(28236);n(46226);var p=n(52210),m=n(35047);let g=({buttonLabel:e,buttonHref:s,menuItems:n,locale:h,withFlag:v,selectedFlag:A,subMenu:b})=>{let[y,w]=(0,o.useState)(!1),{t:x}=(0,p.$G)();(0,m.usePathname)();let N=(e,s)=>{e.stopPropagation(),s.onClick?s.onClick():void 0==s.subItems&&w(!y)};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(r.Z,{className:b?"sub-menu dropdown-menu":"dropdown-menu",onClick:e=>N(e,{href:s}),children:[t.jsx(j.default,{href:(0,f.jJ)(h,s),locale:h,className:"menu-item",children:v?t.jsx("img",{className:"flag-lang",src:A?.src,width:26,height:22,loading:"lazy"}):t.jsx(a.Z,{primary:e})}),n&&n.length>0&&t.jsx("div",{className:"submenu-toggle",onClick:e=>{e.stopPropagation(),w(!y)},children:y?t.jsx(u.Z,{}):t.jsx(d.Z,{})})]}),t.jsx(i.Z,{in:y,timeout:"auto",unmountOnExit:!0,children:t.jsx(l.Z,{component:"div",disablePadding:!0,children:n.map((e,s)=>t.jsx(c.ZP,{className:e.subMenu?"sub-menu menu-item":"menu-item",sx:{pl:4},onClick:s=>N(s,e),children:e.subItems?t.jsx(g,{locale:h,buttonLabel:e?.i18nName?x(e?.i18nName):e.name,buttonHref:e.route,menuItems:e.subItems,withFlag:v,selectedFlag:e.flag,subMenu:!0}):(0,t.jsxs)(r.Z,{href:e.route?(0,f.jJ)(h,e.route).replace("/en/","/"):null,onClick:s=>N(s,e),className:"dropdown-item-link",children:[e.icon&&e.icon,v?t.jsx("img",{className:"flag-lang",src:e.flag?.src,width:26,height:22,loading:"lazy"}):e?.i18nName?x(e?.i18nName):e.name]})},s))})})]})},h=g},20325:(e,s,n)=>{"use strict";n.d(s,{Z:()=>v});var t=n(10326),o=n(17577),r=n(48260),a=n(33198),i=n(16111),l=n(37841),c=n(99207),u=n(78969);n(90434),n(46226),n(4230),n(38106),n(16376),n(95746);var d=n(22304),j=n(52210),f=n(28236),p=n(97980),m=n(5248),g=n(37365),h=n(82400);let v=function({locale:e}){let[s,n]=(0,o.useState)(null),v=!!s,{t:A}=(0,j.$G)(),{user:b}=(0,d.Z)(),[y,w]=(0,o.useState)(!1),x=()=>{n(null)},N=b?.roles?.includes(m.uU.CANDIDATE)?`/${p.pf.baseURL.route}`:`/${p.GW.baseURL.route}`;return(0,t.jsxs)("div",{className:"dropdown-menu",children:[(0,t.jsxs)(r.Z,{onClick:e=>{n(e.currentTarget)},size:"small",sx:{ml:2},"aria-controls":v?"account-menu":void 0,"aria-haspopup":"true","aria-expanded":v?"true":void 0,children:[(0,t.jsxs)(a.Z,{sx:{width:45,height:45,backgroundColor:"#dbe8f6"},src:`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${b?.profilePicture}`,children:[" ",(0,t.jsxs)("span",{style:{color:"#234791",fontFamily:"Proxima-Nova-SemiBold",textTransform:"uppercase"},children:[" ",b?.firstName?.split(" ")[0][0].toUpperCase()," "]})]})," "]}),(0,t.jsxs)(i.Z,{anchorEl:s,id:"account-menu",open:v,onClose:x,onClick:x,slotProps:{paper:{elevation:0,sx:{overflow:"visible",filter:"drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",mt:1.5,"& .MuiAvatar-root":{width:32,height:32,ml:-.5,mr:1},"&::before":{content:'""',display:"block",position:"absolute",top:0,right:14,width:10,height:10,bgcolor:"background.paper",transform:"translateY(-50%) rotate(45deg)",zIndex:0}}}},transformOrigin:{horizontal:"right",vertical:"top"},anchorOrigin:{horizontal:"right",vertical:"bottom"},children:[t.jsx(l.Z,{sx:{color:"#234791",fontFamily:"Proxima-Nova-Regular"},onClick:()=>{window.location.href=(0,f.jJ)(e,`${N}`)},children:A("global:dashboard")}),t.jsx(l.Z,{sx:{color:"#234791",fontFamily:"Proxima-Nova-Regular"},onClick:()=>{window.location.href=(0,f.jJ)(e,`${N}/${p.tF.myProfile.route}`)},children:A("global:myProfile")}),t.jsx(c.Z,{}),(0,t.jsxs)(l.Z,{sx:{color:"#234791",fontFamily:"Proxima-Nova-Regular"},onClick:()=>{window.location.href=(0,f.jJ)(e,`${N}/${p.tF.settings.route}`)},children:[t.jsx(u.Z,{children:t.jsx(g.Z,{fontSize:"small"})}),A("global:settings")]}),(0,t.jsxs)(l.Z,{sx:{color:"#234791",fontFamily:"Proxima-Nova-Regular"},onClick:()=>{window.location.href=(0,f.jJ)(e,`/${p.jb.logout.route}`)},children:[t.jsx(u.Z,{children:t.jsx(h.Z,{fontSize:"small"})}),A("global:logout")]})]})]})}},4230:(e,s,n)=>{"use strict";n.d(s,{Z:()=>x});var t,o=n(10326),r=n(17577),a=n(35047),i=n(57967),l=n.n(i),c=n(22304),u=n(70580),d=n(80057),j=n(28236),f=n(97980),p=n(5248),m=n(23743),g=n(88441),h=n(83708),v=n(16111),A=n(15082),b=n(95746);function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var n=arguments[s];for(var t in n)({}).hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e}).apply(null,arguments)}let w=e=>b.createElement("svg",y({xmlns:"http://www.w3.org/2000/svg",width:20,height:22,fill:"none"},e),t||(t=b.createElement("path",{fill:"#234791",fillRule:"evenodd",d:"M5.227 2.227A6.75 6.75 0 0 1 16.75 7c0 3.39.725 5.514 1.409 6.766.342.628.678 1.044.917 1.296a3 3 0 0 0 .338.312l.009.006A.75.75 0 0 1 19 16.75H1a.75.75 0 0 1-.423-1.37l.009-.006.063-.05c.06-.05.156-.136.275-.262.239-.252.575-.668.918-1.296C2.525 12.514 3.25 10.39 3.25 7a6.75 6.75 0 0 1 1.977-4.773M2.69 15.25h14.62a9 9 0 0 1-.468-.766C16.025 12.986 15.25 10.611 15.25 7a5.25 5.25 0 0 0-10.5 0c0 3.61-.775 5.986-1.592 7.484a9 9 0 0 1-.468.766m5.204 4.101a.75.75 0 0 1 1.025.273 1.25 1.25 0 0 0 2.162 0 .75.75 0 0 1 1.298.752 2.75 2.75 0 0 1-4.758 0 .75.75 0 0 1 .273-1.025",clipRule:"evenodd"}))),x=({locale:e,websiteHeaderIcon:s,pathname:n,isDetailBlogPath:t})=>{let[i,b]=(0,r.useState)(null),{user:y}=(0,c.Z)(),[x,N]=(0,r.useState)([]),[P,E]=(0,r.useState)(0),C=(0,r.useRef)(null),S=(0,a.useRouter)(),U=(0,m.Z)(),D=(0,g.Z)(U.breakpoints.down("sm")),k=()=>{b(null)};(0,r.useEffect)(()=>{D&&k()},[D]);let I=e=>{let s=l().duration(l()().diff(l()(e)));return s.asMonths()>=1?`${Math.floor(s.asMonths())}mois`:s.asDays()>=1?`${Math.floor(s.asDays())}j`:s.asHours()>=1?`${Math.floor(s.asHours())}h`:`${Math.floor(s.minutes())}min`},L=e=>{b(e.currentTarget)},T=async e=>{try{e.isRead=!0,await u.yX.put(`${process.env.NEXT_PUBLIC_BASE_API_URL}/notifications/${e._id}`,e),E(P-1)}catch(e){console.error("Error marking all notifications as read:",e)}e?.link&&e?.link!=="http://localhost:3000"&&(window.location.href=e.link)};(0,r.useEffect)(()=>(C.current=(0,d.io)(process.env.NEXT_PUBLIC_BASE_URL,{path:"/api/v1/socket.io",withCredentials:!0}),C.current.on("initial-notifications",e=>{let s=e.map(e=>({...e,receivedTime:I(e.receivedTime)}));N(s),E(s.filter(e=>!e.isRead).length)}),C.current.on("notification",e=>{let s={...e[0],receivedTime:I(e[0].receivedTime)};N(e=>[s,...e].slice(0,4)),E(e=>e+1)}),()=>{C.current.disconnect()}),[]);let _=async()=>{try{await u.yX.put(`${process.env.NEXT_PUBLIC_BASE_API_URL}/notifications`,{isRead:!0}),N(e=>e.map(e=>({...e,isRead:e.isRead=!0}))),E(0)}catch(e){console.error("Error marking all notifications as read:",e)}};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{className:"notif-section",children:[P&&P>0?o.jsx("span",{className:s?"notif-nbr-header":"notif-nbr",children:P>99?"+99":P}):null,s?o.jsx(h.Z,{title:"Notifications",children:o.jsx(A.default,{id:"Notifications",onClick:L,icon:o.jsx(w,{}),locale:e,className:n.includes("/notifications")?"btn btn-notif navbar-link active":t()?"btn btn-notif navbar-link whiteBg":"btn btn-notif navbar-link"})}):o.jsx(h.Z,{title:"Notifications",children:o.jsx(A.default,{onClick:L,id:"Notifications",icon:o.jsx(w,{}),locale:e,className:" btn btn-link"})})]}),o.jsx(v.Z,{id:"account-menu",anchorEl:i,open:!!i,onClose:k,slotProps:{paper:{elevation:0,sx:{backgroundColor:"#e4effc",overflow:"visible",filter:"drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",mt:1.5,"& .MuiAvatar-root":{width:32,height:32,ml:-.5,mr:1},"&::before":{content:'""',display:"block",position:"absolute",top:0,right:14,width:10,height:10,bgcolor:"#e4effc",transform:"translateY(-50%) rotate(45deg)",zIndex:0}}}},transformOrigin:{horizontal:"right",vertical:"top"},anchorOrigin:{horizontal:"right",vertical:"bottom"},children:o.jsx("div",{id:"notifications",children:o.jsx("div",{className:"container-alerts",children:(0,o.jsxs)("div",{className:"notifications-alerts",children:[(0,o.jsxs)("div",{className:"header-alert",children:[(0,o.jsxs)("h2",{children:[o.jsx("span",{className:"title",children:"Notifications"}),P>0&&o.jsx("span",{className:"unread-notification-number",children:P})]}),o.jsx("p",{onClick:_,children:"Mark all as read"})]}),o.jsx("div",{className:"body-alert",children:x.map(e=>(0,o.jsxs)("div",{className:`notification ${e.isRead?"readed":"unreaded"}`,onClick:()=>T(e),children:[o.jsx("div",{className:"avatar",children:(0,j.B8)(e.type)}),o.jsx("div",{className:"text",children:(0,o.jsxs)("div",{className:"text-top",children:[(0,o.jsxs)("p",{children:[o.jsx("span",{className:"profil-name",children:e.senderName}),o.jsx("span",{className:"message-notif",children:e.message}),o.jsx("span",{className:"notification-description-message",children:e.description})]}),(0,o.jsxs)("div",{className:"notif-details",children:[o.jsx("span",{className:"notification-timestamp",children:e.receivedTime}),!e.isRead&&o.jsx("span",{className:"unread-dot"})]})]})})]},e._id))}),o.jsx("button",{onClick:()=>{k(),y?.roles?.includes(p.uU.ADMIN)||y?.roles?.includes(p.uU.EDITOR)?S.push(`/${f.GW.baseURL.route}/${f.tF.notifications.route}/`):y?.roles?.includes(p.uU.CANDIDATE)&&S.push(`/${f.pf.baseURL.route}/${f.tF.notifications.route}/`)},className:"see-all",type:"button",children:"See All"})]})})})})]})}},70580:(e,s,n)=>{"use strict";n.d(s,{cU:()=>a,xk:()=>r,yX:()=>o});var t=n(44099);let o=t.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),r=t.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),a=t.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"});t.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))},22304:(e,s,n)=>{"use strict";n.d(s,{Z:()=>i});var t=n(44099);let o=t.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0});t.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},withCredentials:!0}),t.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e));let r=()=>new Promise(async(e,s)=>{try{let s=await o.get(`${process.env.NEXT_PUBLIC_BASE_API_URL}/account`);e(s.data)}catch(e){s(e)}});var a=n(2994);function i(e=!0){let{data:s,error:n,isLoading:t,refetch:o}=(0,a.useQuery)({queryKey:["currentUser"],queryFn:r,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1,retry:e});return{user:s,error:n,isLoading:t,refetch:o}}},20026:(e,s,n)=>{"use strict";n.d(s,{px:()=>A,V3:()=>N,kw:()=>P,PU:()=>y,ZC:()=>x,iQ:()=>w,vD:()=>v,NB:()=>b,fV:()=>E});var t=n(2994),o=n(31190),r=n(70580),a=n(48578),i=n(50967);let l=e=>{let s=e.t;return new Promise(async(n,t)=>{r.yX.put(`${i.Y.updateUser}/${e.user}`,e.nonEmptyValues).then(s=>{e.nonEmptyValues.profilePicture||o.Am.success("Personal information updated successfully."),s.data&&n(s.data)}).catch(e=>{e&&e.response&&e.response.data&&404===e.response.status&&o.Am.error(s("messages:userNotFound")),e&&t(e)})})},c=e=>new Promise(async(s,n)=>{r.yX.put(`${i.Y.users}/archive/${e.id}`,{archive:e.archive}).then(e=>{o.Am.success("user updated successfully"),e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&n(e)})}),u=e=>new Promise(async(s,n)=>{try{let n=await r.xk.get(`/users/${e}`);s(n.data)}catch(e){n(e)}}),d=e=>{let s=e.body.t;return new Promise(async(n,t)=>{r.yX.put(`${i.Y.candidate}`,e.body).then(e=>{e.data&&(n(e.data),o.Am.success(s("messages:professionalInfoUpdated")))}).catch(e=>{e&&e.response&&e.response.data&&404===e.response.status&&o.Am.error(s("messages:candidateNotFound")),e&&t(e)})})},j=e=>new Promise(async(e,s)=>{try{let s=await r.yX.get(i.Y.currentUser);e(s.data)}catch(e){e.response&&404===e.response.status&&o.Am.error(a.X.USER_NOT_FOUND),e.response&&401===e.response.status||o.Am.error(a.X.SERVER_ERROR),s(e)}}),f=e=>new Promise(async(s,n)=>{try{let n=await r.yX.get(`${i.Y.users}/${e}`);s(n.data)}catch(e){e.response&&404===e.response.status&&o.Am.error(a.X.USER_NOT_FOUND),e.response&&401===e.response.status||o.Am.error(a.X.SERVER_ERROR),n(e)}}),p=()=>new Promise(async(e,s)=>{try{let s=await r.yX.get(`${i.Y.candidate}/currentCandidate`);e(s.data)}catch(e){e.response&&404===e.response.status&&o.Am.error(a.X.CandidateNotFound),s(e)}}),m=(e,s)=>new Promise(async(n,t)=>{try{let t=e.includes(s)?e:[...e,s],o=await r.yX.get(`${i.Y.skills}?industries=${encodeURIComponent(t.length>0?t.join(","):[])}`);n(o.data)}catch(e){t(e)}});async function g(e){let s=e.t;try{let n=await r.yX.put(`${i.v}/account/password`,e.passwordData);return o.Am.success(s("settings:supdatepassword")),n.data}catch(e){throw e.response&&406===e.response.status?o.Am.error(s("messages:currentpassword")):o.Am.error(s("messages:errorupdatepassword")),e.response?e.response.data:e}}var h=n(22304);let v=()=>{let e=(0,t.useQueryClient)(),{refetch:s}=w();return(0,t.useMutation)({mutationFn:e=>l(e),onSuccess:n=>{e.invalidateQueries("userData"),s(),localStorage.setItem("user",JSON.stringify(n))},onError:e=>{e.message=""}})},A=()=>(0,t.useMutation)({mutationFn:({id:e,archive:s})=>c({id:e,archive:s}),onError:e=>{e.message="error on useArchiveduser"}}),b=()=>{let e=(0,t.useQueryClient)(),{user:s}=(0,h.Z)();return(0,t.useMutation)({mutationFn:e=>d({body:e}),onSuccess:s=>{e.invalidateQueries("candidateData")},onError:e=>{e.message=""}})},y=e=>(0,t.useQuery)(["user",e],async()=>await u(e)),w=()=>(0,t.useQuery)("userData",async()=>{try{return await j()}catch(e){}}),x=e=>(0,t.useQuery)("userData",async()=>{try{return await f(e)}catch(e){}}),N=()=>{let{user:e}=(0,h.Z)();return(0,t.useQuery)(["getCandidateData"],async()=>{try{return await p()}catch(e){}})},P=(e,s)=>(0,t.useQuery)("skills",async()=>{try{return await m(e,s)}catch(e){}}),E=()=>{let e=(0,t.useQueryClient)();return(0,t.useMutation)(g,{onSuccess:()=>{e.invalidateQueries("password")}})}},25788:(e,s,n)=>{"use strict";n.d(s,{ReactQueryProvider:()=>a});var t=n(10326),o=n(2994);let r=new o.QueryClient;function a({children:e}){return t.jsx(o.QueryClientProvider,{client:r,children:e})}},48578:(e,s,n)=>{"use strict";n.d(s,{X:()=>t});let t={INCORRECT_PASSWORD:"Password incorrect",EMAIL_NOT_FOUND:"There's no user with this email",InvalidEmail:"Invalid email address.",FailedUpdateFile:"failed to upload file try again !!",ACCOUNT_NOTACTIVATED:"Your account is not activated yet. Please check your email for the activation link.",EMAIL_EXIST:"Email already exist",FileExist:"File already exists",FileNotFound:"File not found!",error:"'An unknown error occurred'",ERROR:"An error occurred. Please try again later !",INVALID_SIGNUP_DATA:"Invalid signup data. Please check your information and try again.",CandidateNotFound:"Candidate not found",ResetPasswordLink:"Check your email. Link expires in 10 mins!",VALIDATIONS:{INVALID_EMAIL:"Invalid email",EMPTY_FIELD:"Please fill in the required fields!",END_DATE:"End date must be after start date",MIN_DATE:"Date of birth must be after 1950",MAX_DATE:"Date must be before 2005",MIN_LENGTH:"Field must be at least 3 characters",MAX_LENGTH:"Field must be at most 20 characters",REQUIRED:"This field is required!",INVALID_PASSWORD:"Password requires at least one uppercase, one lowercase letter, and one digit",PASSWORDMATCH:"password must match"}}},50967:(e,s,n)=>{"use strict";n.d(s,{Y:()=>o,v:()=>t});let t=process.env.NEXT_PUBLIC_BASE_API_URL,o={seo:"seoTags",auth:"/auth/signin",logout:"/auth/logout",candidatures:"/applications",signup:"/auth/signup",forgetPassword:"/auth/forgot-password",resetPassword:"/auth/reset-password",guides:"/guides",currentUser:"/users/current",updateUser:"/users",users:"/users",categoryGuides:"guidecategory",candidate:"/candidates",report:"/report",skills:"/skills",files:"/files",applications:"/applications",sliders:"/sliders",favoris:"/candidate/favourite",articles:"/articles",categories:"/categories",blog:"/blog",category:"/categories",opportunity:"/opportunities",seoOpportunity:"/seoOpportunity",newsletter:"/newsletter",contact:"/contact",favourite:"/favourite",contacts:"contacts",comments:"/comments",statistics:"/statistics",events:"/events",baseUrl:`${t}`}},4587:e=>{"use strict";e.exports={locales:["en","fr"],defaultLocale:"en",localeDetection:!1}},43207:(e,s,n)=>{"use strict";n.d(s,{Z:()=>l});var t=n(18177),o=n(66067),r=n(83803),a=n(4587),i=n.n(a);async function l(e,s,a,l){return(a=a||(0,t.Fs)()).use(o.D),l||a.use((0,r.Z)((e,s)=>n(65302)(`./${e}/${s}.json`))),await a.init({lng:e||i().defaultLocale,resources:l,fallbackLng:i().defaultLocale,supportedLngs:i().locales,defaultNS:s[0],fallbackNS:s[0],ns:s,preload:l?[]:i().locales}),{i18n:a,resources:a.services.resourceStore.data,t:a.t}}},80283:(e,s,n)=>{"use strict";n.d(s,{Z:()=>t});let t=(0,n(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\TranslationProvider.js#default`)},55367:(e,s,n)=>{"use strict";n.d(s,{m:()=>t});let t=(0,n(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\lib\react-query-client.js#ReactQueryProvider`)}};