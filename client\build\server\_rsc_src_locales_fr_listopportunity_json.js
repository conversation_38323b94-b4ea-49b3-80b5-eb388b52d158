"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_locales_fr_listopportunity_json";
exports.ids = ["_rsc_src_locales_fr_listopportunity_json"];
exports.modules = {

/***/ "(rsc)/./src/locales/fr/listopportunity.json":
/*!*********************************************!*\
  !*** ./src/locales/fr/listopportunity.json ***!
  \*********************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"oppotuhnities":"Opportunités","job":"Emploi","contratType":"Type de contrat","description":"Description","Publishdate":"Date de publication","actions":"Actions","availablelanguage":"Langue existante","visibility":"Visibilité","expirationDate":"Date d\'expiration","contract":"Contrat","industry":"Industrie","postingDate":"Date de publication"}');

/***/ })

};
;