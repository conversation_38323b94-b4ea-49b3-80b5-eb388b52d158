"use client";
import AuthLayout from "@/components/layouts/AuthLayout";
import Register from "@/features/auth/component/Register";

import { useTranslation } from "react-i18next";

const page = ({params}) => {
  const { t } = useTranslation();

  return (
    <AuthLayout
      id="auth-layout"
      title={t("register:register")}
      subTitle={t("register:registerMessage")}
    >
      <Register locale={params?.locale} t={t} />
    </AuthLayout>
  );
};

export default page;
