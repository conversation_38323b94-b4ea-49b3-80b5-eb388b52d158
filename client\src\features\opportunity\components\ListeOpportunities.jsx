"use client";
import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import SvgRefreshIcon from "@/assets/images/icons/refreshIcon.svg";
import { DataGrid } from "@mui/x-data-grid";
import { INDUSTRY } from "@/config/inustries";
import SvgSearchIcon from "@/assets/images/icons/searchIcon.svg";
import Svgpreview from "@/assets/images/icons/preview-icon.svg";
import {
  Grid,
  Select,
  InputBase,
  FormGroup,
  FormLabel,
  Autocomplete,
  TextField,
  InputAdornment,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import Svgedit from "@/assets/images/icons/edit-icon.svg";
import Loading from "../../../components/loading/Loading";
import { useGetOpportunities, useUpdateJobDescriptions } from "../hooks/opportunity.hooks";
import { findIndustryColoredIcon, formatDate } from "../../../utils/functions";
import {
  ContractType,
  Countries,
  OpportunityType,
} from "../../../utils/constants";
import { useRouter } from "next/navigation";
import CustomButton from "@/components/ui/CustomButton";
import {
  adminRoutes,
  baseUrlBackoffice,
  websiteRoutesList,
} from "@/helpers/routesList";

const ListOpportunities = ({ language }) => {
  const { t } = useTranslation();
  const router = useRouter();

  const [pageSize, setPageSize] = useState(12);
  const [isPublished, setIsPublished] = useState("");
  const [publishDate, setPublishDate] = useState("");
  const [createdAt, setCreatedAt] = useState(null);
  const [searchValue, setSearchValue] = useState("");
  const [reference, setReference] = useState("");
  const [contractType, setContractType] = useState("");
  const savedPagination = localStorage.getItem("PAGINATION_KEY_OPPORTUNITY");
  const savedSortOrder = localStorage.getItem("SortOrder_OPPORTUNITY");
  const savedCountry = localStorage.getItem("Country_OPPORTUNITY");
  const savedVisibility = localStorage.getItem("Visibility_OPPORTUNITY");
  const savedKeyword = localStorage.getItem("KeyWord_OPPORTUNITY");
  const savedIndustry = localStorage.getItem("Industry_OPPORTUNITY");
  const [opportunityType, setOpportunityType] = useState("");

  const [country, setCountry] = useState(
    savedCountry ? JSON.parse(savedCountry) : ""
  );
  const [keyWord, setKeyWord] = useState(
    savedKeyword ? JSON.parse(savedKeyword) : ""
  );
  const [industry, setIndustry] = useState(
    savedIndustry ? JSON.parse(savedIndustry) : ""
  );
  const [visibility, setVisibility] = useState(
    savedVisibility ? JSON.parse(savedVisibility) : ""
  );
  const [sortOrder, setSortOrder] = useState(
    savedSortOrder ? JSON.parse(savedSortOrder) : ""
  );
  const [paginationModel, setPaginationModel] = React.useState(
    savedPagination
      ? JSON.parse(savedPagination)
      : {
        page: 0,
        pageSize: 10,
      }
  );

  const { mutate: updateDescriptions } = useUpdateJobDescriptions();
  const handleUpdate = () => {
    updateDescriptions();
  };
  const [selectedLanguage, setSelectedLanguage] = useState(language || "");
  const [search, setSearch] = useState(false);
  const resetSearch = () => {
    setSortOrder("");
    setCountry("");
    setKeyWord("");
    setOpportunityType("");
    setVisibility("");
    setCreatedAt(null);
    setPublishDate();
    setIndustry("");
    setContractType([]);
    setSelectedLanguage(language || "");
    setSearch(!search);
    setPaginationModel({ page: 0, pageSize: 10 });
    setReference("");
    localStorage.setItem(
      "PAGINATION_KEY_OPPORTUNITY",
      JSON.stringify({
        page: 0,
        pageSize: 10,
      })
    );
    localStorage.setItem("Industry_OPPORTUNITY", JSON.stringify(""));
    localStorage.setItem("Country_OPPORTUNITY", JSON.stringify(""));
    localStorage.setItem("KeyWord_OPPORTUNITY", JSON.stringify(""));
    localStorage.setItem("Visibility_OPPORTUNITY", JSON.stringify(""));
    localStorage.setItem("SortOrder_OPPORTUNITY", JSON.stringify(""));
  };

  const getOpportunities = useGetOpportunities({
    language: selectedLanguage,
    pageSize: paginationModel.pageSize,
    pageNumber: paginationModel.page + 1,
    sortOrder,
    country,
    isPublished,
    createdAt,
    publishDate,
    visibility,
    keyWord,
    industry,
    reference,
    contractType: contractType.length > 0 ? contractType.join(",") : "",
    opportunityType,
  });

  useEffect(() => {
    getOpportunities.refetch();
  }, [search, paginationModel]);

  const truncateTitle = (title) => {
    const words = title?.split(" ");
    if (words?.length > 3) {
      return words.slice(0, 1).join(" ") + "...";
    } else {
      return title;
    }
  };
  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSearchClick();
    }
  };
  const handleSearchClick = () => {
    setSearch(!search);
    setKeyWord(searchValue);
    localStorage.setItem("KeyWord_OPPORTUNITY", JSON.stringify(searchValue));
  };
  const handleCountryChange = (value) => {
    setCountry(value);
    localStorage.setItem("Country_OPPORTUNITY", JSON.stringify(value));
  };
  const handleVisibilityChange = (e) => {
    setVisibility(e?.value || "");
    localStorage.setItem(
      "Visibility_OPPORTUNITY",
      JSON.stringify(e?.value || "")
    );
  };



  const handlePaginationChange = (newPaginationModel) => {
    setPaginationModel(newPaginationModel);
    localStorage.setItem(
      "PAGINATION_KEY_OPPORTUNITY",
      JSON.stringify(newPaginationModel)
    );
  };

  const handleSearchChange = (e) => {
    setSearchValue(e.target.value);
  };

  const handleActionChange = (e, id) => {
    const action = e.target.value;
    if (action === "edit") {
      handleEdit(id);
    }
  };
  const theme = useTheme();

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const rows =
    getOpportunities?.data?.opportunities?.map((item, index) => ({
      id: index,
      opportunityId: item._id,
      job: item?.versions[language]?.title,
      date: formatDate(item.createdAt),
      contractType: item.contractType,
      currentLaguage: item.versions[language].language,
      publishDate: formatDate(item.versions[language].publishDate),
      language: item?.existingLanguages.join("/"),
      actions: item._id,
      visibility: item.versions[language].visibility,
      url: item.versions[language].url,
      totalApplicants: item.applicants ? item.applicants.length : 0,
    })) || [];

  const visibilityOption = [
    { value: "Public", label: "Public" },
    { value: "Private", label: "Private" },
    { value: "Draft", label: "Draft" },
  ];

  const columns = [
    {
      field: "title",
      headerName: t("listopportunity:job"),
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      flex: 1,
      renderCell: (params) => {
        const item = getOpportunities?.data?.opportunities[params.id];
        return (
          <div className="job-title-cell">
            {findIndustryColoredIcon(item.industry)}
            <CustomButton
              text={truncateTitle(item?.versions[language]?.title)}
              leftIcon={true}
              link={`/${websiteRoutesList.opportunities.route}/${params.row.url}/`}
              className="btn btn-ghost edit-blog"
            />
          </div>
        );
      },
    },
    {
      field: "contractType",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      headerName: t("listopportunity:contratType"),
      flex: 1,
    },

    {
      field: "publishDate",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      headerName: t("listopportunity:Publishdate"),
      flex: 1,
      valueFormatter: formatDate,
    },
    {
      field: "language",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      headerName: t("listopportunity:availablelanguage"),
      flex: 1,
    },
    {
      field: "visibility",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      headerName: t("listopportunity:visibility"),
      flex: 1,
    },
    {
      field: "totalApplicants",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      headerName: t("listopportunity:applicants"),
      flex: 1,
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      renderCell: (params) => {
        const id = params.row.actions;
        return (
          <Select
            value=""
            onChange={(e) => handleActionChange(e, id)}
            displayEmpty
            input={<InputBase />}
            style={{ width: "100%" }}
            renderValue={() => t("listArticle:Actions")}
          >
            <CustomButton
              text={t("global:preview")}
              icon={<Svgpreview />}
              leftIcon={true}
              link={`/${websiteRoutesList.opportunities.route}/${params.row.url}/`}
              className="btn btn-ghost edit-blog"
            />
            <CustomButton
              text={t("global:edit")}
              icon={<Svgedit />}
              leftIcon={true}
              link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.opportunities.route}/${adminRoutes.edit.route}/${id}/`}
              className="btn btn-ghost edit-blog"
            />
            <CustomButton
              text={t("global:applications")}
              icon={<Svgpreview />}
              leftIcon={true}
              link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}/${adminRoutes.opportunity.route}/${id}`}
              className="btn btn-ghost edit-blog"
            />
          </Select>
        );
      },
    },
  ];

  if (getOpportunities.isLoading) {
    return <Loading />;
  }

  return (
    <>
      <div className="display-inline">
        <p className="heading-h2 semi-bold">
          {t("listopportunity:oppotuhnities")}
          <span className="opportunities-nbr">
            {getOpportunities?.data?.totalOpportunities}
          </span>
        </p>

        <CustomButton
          className="btn btn-filled"
          text={t("Update Descriptions")}
          onClick={handleUpdate}
        />
      </div>

      <div id="container" className="recent-application-pentabell">
        <div className={`main-content`}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Grid
                id="filter"
                container
                spacing={2}
                justifyContent="flex-start"
                alignItems="center"
              >
                <Grid item xs={12} sm={6}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("createOpportunity:search")}
                    </FormLabel>
                  </FormGroup>
                  <TextField
                    className="input-pentabell"
                    autoComplete="off"
                    slotProps={{
                      input: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <SvgSearchIcon />
                          </InputAdornment>
                        ),
                      },
                    }}
                    variant="standard"
                    type="text"
                    onKeyDown={handleKeyDown}
                    onChange={handleSearchChange}
                    value={searchValue}
                    placeholder={t("Search")}
                  />
                </Grid>
                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("global:contractType")}
                    </FormLabel>
                    <Autocomplete
                      className="input-pentabell"
                      multiple
                      id="sort-order-autocomplete"
                      options={ContractType}
                      value={contractType || []}
                      onChange={(event, newValue) => {
                        if (newValue) {
                          setContractType(newValue);
                        }
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          className="input-pentabell multiple-select"
                          variant="standard"
                        />
                      )}
                    />
                  </FormGroup>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("global:opportunityType")}
                    </FormLabel>
                    <Autocomplete
                      className="input-pentabell"
                      id="sort-order-autocomplete"
                      options={OpportunityType}
                      value={opportunityType || ""}
                      onChange={(event, newValue) => {
                        if (newValue) {
                          setOpportunityType(newValue);
                        }
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          className="input-pentabell multiple-select"
                          variant="standard"
                        />
                      )}
                    />
                  </FormGroup>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("global:sort")}
                    </FormLabel>
                    <Autocomplete
                      className="input-pentabell"
                      id="sort-order-autocomplete"
                      options={[
                        { value: "desc", label: t("global:newest") },
                        { value: "asc", label: t("global:oldest") },
                      ]}
                      getOptionLabel={(option) => option.label}
                      value={
                        sortOrder
                          ? {
                            value: sortOrder,
                            label: t(
                              sortOrder === "desc"
                                ? "global:newest"
                                : "global:oldest"
                            ),
                          }
                          : null
                      }
                      onChange={(event, newValue) => {
                        if (newValue) {
                          setSortOrder(newValue.value);
                          localStorage.setItem(
                            "SortOrder_OPPORTUNITY",
                            JSON.stringify(newValue.value)
                          );
                        }
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          className="input-pentabell multiple-select"
                          variant="standard"
                        />
                      )}
                    />
                  </FormGroup>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("listopportunity:visibility")}
                    </FormLabel>
                    <Autocomplete
                      className="input-pentabell"
                      id="visibility-autocomplete"
                      options={visibilityOption}
                      getOptionLabel={(option) => option.label}
                      value={
                        visibility
                          ? {
                            value: visibility,
                            label: visibilityOption.find(
                              (option) => option.value === visibility
                            )?.label,
                          }
                          : null
                      }
                      onChange={(e, value) => handleVisibilityChange(value)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          className="input-pentabell multiple-select"
                          variant="standard"
                        />
                      )}
                    />
                  </FormGroup>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">Reference</FormLabel>
                  </FormGroup>
                  <TextField
                    className="input-pentabell"
                    autoComplete="off"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SvgSearchIcon />
                        </InputAdornment>
                      ),
                    }}
                    variant="standard"
                    type="text"
                    name="ref"
                    value={reference}
                    onChange={(e) => setReference(e.target.value)}
                    placeholder="Reference"
                  />
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("createOpportunity:country")}
                    </FormLabel>
                    <Autocomplete
                      className="input-pentabell"
                      id="sort-order-autocomplete"
                      options={Countries}
                      getOptionLabel={(option) => option}
                      value={country}
                      onChange={(e, value) => handleCountryChange(value)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          className="input-pentabell multiple-select"
                          variant="standard"
                        />
                      )}
                    />
                  </FormGroup>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("listopportunity:industry")}
                    </FormLabel>
                    <Autocomplete
                      className="input-pentabell"
                      id="tags-standard"
                      options={INDUSTRY}
                      getOptionLabel={(option) => option}
                      name="industry"
                      value={industry}
                      onChange={(selectedOption, value) => {
                        setIndustry(value);
                        localStorage.setItem(
                          "Industry_OPPORTUNITY",
                          JSON.stringify(value)
                        );
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          className="input-pentabell multiple-select"
                          variant="standard"
                        />
                      )}
                    />
                  </FormGroup>
                </Grid>

                <Grid item xs={12} sm={1} className="btns-filter dashboard">
                  <CustomButton
                    icon={<SvgRefreshIcon />}
                    className={"btn btn-outlined btn-refresh"}
                    onClick={resetSearch}
                  />
                </Grid>
                <Grid item xs={12} sm={2} className="btns-filter dashboard">
                  <CustomButton
                    text={t("comments:search")}
                    onClick={handleSearchClick}
                    className={"btn btn-filled"}
                  />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <div style={{ height: "100%", width: "100%" }}>
                <DataGrid
                  rows={rows}
                  columns={columns}
                  pageSize={pageSize}
                  pagination
                  paginationMode="server"
                  paginationModel={paginationModel}
                  onPaginationModelChange={handlePaginationChange}
                  pageSizeOptions={[5, 10, 25]}
                  rowCount={getOpportunities?.data?.totalOpportunities || [0]}
                  autoHeight
                  disableSelectionOnClick
                  className="pentabell-table"
                  columnVisibilityModel={{
                    publishDate: !isMobile,
                    contractType: !isMobile,
                    visibility: !isMobile,
                    language: !isMobile,
                  }}
                />
              </div>
            </Grid>
          </Grid>
        </div>
      </div>
    </>
  );
};

export default ListOpportunities;
