"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/AddArticleEN.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! suneditor-react */ \"(app-pages-browser)/./node_modules/suneditor-react/dist/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(suneditor_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n/* harmony import */ var suneditor_dist_css_suneditor_min_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! suneditor/dist/css/suneditor.min.css */ \"(app-pages-browser)/./node_modules/suneditor/dist/css/suneditor.min.css\");\n/* harmony import */ var _feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @feelinglovelynow/slug */ \"(app-pages-browser)/./node_modules/@feelinglovelynow/slug/dist/index.js\");\n/* harmony import */ var suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! suneditor/src/plugins */ \"(app-pages-browser)/./node_modules/suneditor/src/plugins/index.js\");\n/* harmony import */ var _FaqSection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./FaqSection */ \"(app-pages-browser)/./src/features/blog/components/FaqSection.jsx\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/DatePicker/DatePicker.js\");\n/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/x-date-pickers/AdapterDayjs */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/AdapterDayjs/AdapterDayjs.js\");\n/* harmony import */ var _mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/x-date-pickers/internals/demo */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/internals/demo/DemoContainer.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var react_tag_input__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! react-tag-input */ \"(app-pages-browser)/./node_modules/react-tag-input/dist/index.js\");\n/* harmony import */ var _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomTextInput */ \"(app-pages-browser)/./src/components/ui/CustomTextInput.jsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AddArticleEN(param) {\n    let { errors, touched, setFieldValue, values, onImageSelect, filteredCategories, categories, onCategoriesSelect, debounce } = param;\n    _s();\n    const KeyCodes = {\n        comma: 188,\n        enter: 13\n    };\n    const delimiters = [\n        KeyCodes.comma,\n        KeyCodes.enter\n    ];\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [highlights, setHighlights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(values.descriptionEN || \"\");\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(values.urlEN || \"\");\n    const [publishNow, setPublishNow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const language = \"en\";\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedTitle = localStorage.getItem(\"title\");\n        return savedTitle ? JSON.parse(savedTitle) : \"\";\n    });\n    const [metatitle, setMetatitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetatitle = localStorage.getItem(\"metatitle\");\n        return savedMetatitle ? JSON.parse(savedMetatitle) : \"\";\n    });\n    const [metaDescription, setMetaDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetadescription = localStorage.getItem(\"metaDescription\");\n        return savedMetadescription ? JSON.parse(savedMetadescription) : \"\";\n    });\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedContent = localStorage.getItem(\"content\");\n        return savedContent ? JSON.parse(savedContent) : \"\";\n    });\n    const handlePaste = (event, cleanData, maxCharCount)=>{\n        let html = cleanData;\n        // Correction des balises non fermées\n        html = html.replace(/<strong>(.*?)$/g, \"<strong>$1</strong>\");\n        return html;\n    };\n    const handleEditorChange = (newContent)=>{\n        debounce();\n        setContent(newContent);\n        setFieldValue(\"contentEN\", newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (title) {\n            localStorage.setItem(\"title\", JSON.stringify(title));\n        }\n        if (content) {\n            localStorage.setItem(\"content\", JSON.stringify(content));\n        }\n        if (metatitle) {\n            localStorage.setItem(\"metatitle\", JSON.stringify(metatitle));\n        }\n        if (metaDescription) {\n            localStorage.setItem(\"metaDescription\", JSON.stringify(metaDescription));\n        }\n    }, [\n        title,\n        content,\n        metatitle,\n        metaDescription\n    ]);\n    const handlePhotoChange = async ()=>{\n        const selectedFile = imageInputRef.current.files[0];\n        setSelectedImage(imageInputRef.current.files[0]);\n        if (selectedFile) {\n            onImageSelect(selectedFile, language);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setFieldValue(\"titleEN\", title);\n        setFieldValue(\"descriptionEN\", description);\n        setFieldValue(\"urlEN\", url);\n        setFieldValue(\"keywordsEN\", tags.map((t)=>t.text));\n        setFieldValue(\"highlightsEN\", highlights.map((h)=>h.text));\n        setFieldValue(\"contentEN\", content);\n        setFieldValue(\"metaTitleEN\", metatitle);\n        setFieldValue(\"metaDescriptionEN\", metaDescription);\n    }, [\n        title,\n        description,\n        url,\n        tags,\n        highlights,\n        content,\n        metatitle,\n        metaDescription\n    ]);\n    const useSaveFileHook = (0,_features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__.useSaveFile)();\n    let uuidPhoto;\n    uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n    const handlePhotoBlogChange = async (file, info, core, uploadHandler)=>{\n        if (file instanceof HTMLImageElement) {\n            const src = file.src;\n            if (src.startsWith(\"data:image\")) {\n                const base64Data = src.split(\",\")[1];\n                const contentType = src.match(/data:(.*?);base64/)[1];\n                const byteCharacters = atob(base64Data);\n                const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i)=>byteCharacters.charCodeAt(i));\n                const byteArray = new Uint8Array(byteNumbers);\n                const blob = new Blob([\n                    byteArray\n                ], {\n                    type: contentType\n                });\n                const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                const selectedFile = new File([\n                    blob\n                ], fileName, {\n                    type: contentType\n                });\n                await uploadFile(selectedFile, uploadHandler, core, file);\n            } else {\n                fetch(src).then((response)=>response.blob()).then((blob)=>{\n                    const contentType = blob.type;\n                    const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                    const selectedFile = new File([\n                        blob\n                    ], fileName, {\n                        type: contentType\n                    });\n                    uploadFile(selectedFile, uploadHandler, core, file);\n                }).catch((error)=>console.error(\"Error converting image URL to Blob:\", error));\n            }\n        } else {\n            console.error(\"File is not an HTMLImageElement.\");\n        }\n    };\n    const uploadFile = (selectedFile, uploadHandler, core, originalImage)=>{\n        let uuidPhoto;\n        uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n        const formData = new FormData();\n        formData.append(\"file\", selectedFile);\n        const extension = selectedFile.name.split(\".\").pop();\n        const currentYear = new Date().getFullYear();\n        useSaveFileHook.mutate({\n            resource: \"blogs\",\n            folder: currentYear.toString(),\n            filename: uuidPhoto,\n            body: {\n                formData,\n                t\n            }\n        }, {\n            onSuccess: (dataUUID)=>{\n                const uuidPhotoFileName = dataUUID.message === \"uuid exist\" ? dataUUID.uuid : `${uuidPhoto}.${extension}`;\n                const imageUrl = `${\"http://localhost:4000/api/v1\"}/files/${uuidPhotoFileName}`;\n                originalImage.src = imageUrl;\n                uploadHandler({\n                    result: [\n                        {\n                            id: uuidPhotoFileName,\n                            url: imageUrl\n                        }\n                    ]\n                });\n            },\n            onError: (error)=>{\n                console.error(\"Error uploading file:\", error);\n            }\n        });\n    };\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleFileChange = async (event)=>{\n        const file = event.target.files[0];\n        if (!file || file.type !== \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\") {\n            setError(\"Please upload a valid .docx file.\");\n            return;\n        }\n        const reader = new FileReader();\n        reader.onload = async (event)=>{\n            const arrayBuffer = event.target.result;\n            try {\n                const result = await mammoth__WEBPACK_IMPORTED_MODULE_4__.convertToHtml({\n                    arrayBuffer\n                });\n                console.log(result.value);\n                setFieldValue(\"contentEN\", result.value);\n                setContent(result.value);\n                setError(null);\n            } catch (err) {\n                console.error(\"Mammoth conversion error:\", err);\n                setError(\"Failed to convert the DOCX file.\");\n            }\n        };\n        reader.readAsArrayBuffer(file);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"label-pentabell\",\n                children: \"Add article English : \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"file\",\n                accept: \".docx\",\n                onChange: handleFileChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: \"red\"\n                },\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 266,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    label: t(\"createArticle:title\"),\n                                    name: \"titleEN\",\n                                    value: title,\n                                    onChange: (e)=>{\n                                        const v = e.target.value;\n                                        setTitle(v);\n                                        setUrl((0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_10__.slug)(v));\n                                        debounce();\n                                    },\n                                    error: touched.titleEN && errors.titleEN\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:categories\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                multiple: true,\n                                                className: \"input-pentabell\",\n                                                id: \"tags-standard\",\n                                                options: filteredCategories.length > 0 ? filteredCategories : categories,\n                                                getOptionLabel: (option)=>option.name,\n                                                selected: values.categoryEN.length > 0 ? (filteredCategories.length > 0 ? filteredCategories : categories).filter((category)=>values.categoryEN.includes(category.id)) : [],\n                                                onChange: (event, selectedOptions)=>{\n                                                    const categoryIds = selectedOptions.map((category)=>category.id);\n                                                    setFieldValue(\"categoryEN\", categoryIds);\n                                                    onCategoriesSelect(categoryIds);\n                                                },\n                                                renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        ...params,\n                                                        className: \"input-pentabell  multiple-select\",\n                                                        variant: \"standard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            touched.category && errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"label-error\",\n                                children: errors.category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Description\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"descriptionEN\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: values.descriptionEN,\n                                    onChange: (e)=>{\n                                        const descriptionEN = e.target.value;\n                                        setFieldValue(\"descriptionEN\", descriptionEN);\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.descriptionEN && touched.descriptionEN ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"descriptionEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Highlights\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"tags\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                        tags: highlights,\n                                        className: \"input-pentabell\" + (errors.highlightsEN && touched.highlightsEN ? \" is-invalid\" : \"\"),\n                                        delimiters: delimiters,\n                                        handleDelete: (i)=>{\n                                            const updatedTags = highlights.filter((tag, index)=>index !== i);\n                                            setHighlights(updatedTags);\n                                            setFieldValue(\"highlightsEN\", updatedTags.map((tag)=>tag.text));\n                                        },\n                                        handleAddition: (tag)=>{\n                                            setHighlights([\n                                                ...highlights,\n                                                tag\n                                            ]);\n                                            setFieldValue(\"highlightsEN\", [\n                                                ...highlights,\n                                                tag\n                                            ].map((item)=>item.text));\n                                        },\n                                        inputFieldPosition: \"bottom\",\n                                        autocomplete: true,\n                                        allowDragDrop: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"keywordsEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 372,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 371,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((suneditor_react__WEBPACK_IMPORTED_MODULE_3___default()), {\n                setContents: content || values?.contentEN || \"\",\n                onChange: (newContent)=>{\n                    setContent(newContent);\n                    setFieldValue(\"contentEN\", newContent);\n                    debounce();\n                },\n                onPaste: handlePaste,\n                setOptions: {\n                    cleanHTML: false,\n                    disableHtmlSanitizer: true,\n                    addTagsWhitelist: \"h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button\",\n                    plugins: suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                    buttonList: [\n                        [\n                            \"undo\",\n                            \"redo\"\n                        ],\n                        [\n                            \"font\",\n                            \"fontSize\",\n                            \"formatBlock\"\n                        ],\n                        [\n                            \"bold\",\n                            \"underline\",\n                            \"italic\",\n                            \"strike\",\n                            \"subscript\",\n                            \"superscript\"\n                        ],\n                        [\n                            \"fontColor\",\n                            \"hiliteColor\"\n                        ],\n                        [\n                            \"align\",\n                            \"list\",\n                            \"lineHeight\"\n                        ],\n                        [\n                            \"outdent\",\n                            \"indent\"\n                        ],\n                        [\n                            \"table\",\n                            \"horizontalRule\",\n                            \"link\",\n                            \"image\",\n                            \"video\"\n                        ],\n                        [\n                            \"fullScreen\",\n                            \"showBlocks\",\n                            \"codeView\"\n                        ],\n                        [\n                            \"preview\",\n                            \"print\"\n                        ],\n                        [\n                            \"removeFormat\"\n                        ]\n                    ],\n                    imageUploadHandler: handlePhotoBlogChange,\n                    defaultTag: \"div\",\n                    minHeight: \"300px\",\n                    maxHeight: \"400px\",\n                    showPathLabel: false,\n                    font: [\n                        \"Proxima-Nova-Regular\",\n                        \"Proxima-Nova-Medium\",\n                        \"Proxima-Nova-Semibold\",\n                        \"Proxima-Nova-Bold\",\n                        \"Proxima-Nova-Extrabold\",\n                        \"Proxima-Nova-Black\",\n                        \"Proxima-Nova-Light\",\n                        \"Proxima-Nova-Thin\",\n                        \"Arial\",\n                        \"Times New Roman\",\n                        \"Sans-Serif\"\n                    ],\n                    charCounter: true,\n                    charCounterType: \"byte\",\n                    resizingBar: false,\n                    colorList: [\n                        // Standard Colors\n                        [\n                            \"#234791\",\n                            \"#d69b19\",\n                            \"#cc3233\",\n                            \"#009966\",\n                            \"#0b3051\",\n                            \"#2BBFAD\",\n                            \"#0b305100\",\n                            \"#0a305214\",\n                            \"#743794\",\n                            \"#ff0000\",\n                            \"#ff5e00\",\n                            \"#ffe400\",\n                            \"#abf200\",\n                            \"#00d8ff\",\n                            \"#0055ff\",\n                            \"#6600ff\",\n                            \"#ff00dd\",\n                            \"#000000\",\n                            \"#ffd8d8\",\n                            \"#fae0d4\",\n                            \"#faf4c0\",\n                            \"#e4f7ba\",\n                            \"#d4f4fa\",\n                            \"#d9e5ff\",\n                            \"#e8d9ff\",\n                            \"#ffd9fa\",\n                            \"#f1f1f1\",\n                            \"#ffa7a7\",\n                            \"#ffc19e\",\n                            \"#faed7d\",\n                            \"#cef279\",\n                            \"#b2ebf4\",\n                            \"#b2ccff\",\n                            \"#d1b2ff\",\n                            \"#ffb2f5\",\n                            \"#bdbdbd\",\n                            \"#f15f5f\",\n                            \"#f29661\",\n                            \"#e5d85c\",\n                            \"#bce55c\",\n                            \"#5cd1e5\",\n                            \"#6699ff\",\n                            \"#a366ff\",\n                            \"#f261df\",\n                            \"#8c8c8c\",\n                            \"#980000\",\n                            \"#993800\",\n                            \"#998a00\",\n                            \"#6b9900\",\n                            \"#008299\",\n                            \"#003399\",\n                            \"#3d0099\",\n                            \"#990085\",\n                            \"#353535\",\n                            \"#670000\",\n                            \"#662500\",\n                            \"#665c00\",\n                            \"#476600\",\n                            \"#005766\",\n                            \"#002266\",\n                            \"#290066\",\n                            \"#660058\",\n                            \"#222222\"\n                        ]\n                    ]\n                },\n                onImageUpload: handlePhotoBlogChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 540,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaqSection__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                values: values,\n                setFieldValue: setFieldValue,\n                errors: errors,\n                touched: touched,\n                language: \"EN\",\n                debounce: debounce\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 542,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 550,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:metaTitle\"),\n                                        \" (\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: values.metaTitleEN?.length > 65 ? \" text-danger\" : \"\",\n                                            children: [\n                                                \" \",\n                                                values.metaTitleEN?.length,\n                                                \" / 65\",\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        \")\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            variant: \"standard\",\n                                            name: \"metaTitleEN\",\n                                            type: \"text\",\n                                            value: metatitle || values.metaTitleEN,\n                                            onChange: (e)=>{\n                                                const metaTitleEN = e.target.value;\n                                                setFieldValue(\"metaTitleEN\", metaTitleEN);\n                                                setMetatitle(metaTitleEN);\n                                                debounce();\n                                            },\n                                            className: \"input-pentabell\" + (errors.metaTitleEN && touched.metaTitleEN ? \" is-invalid\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"metaTitleEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 554,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 552,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    label: t(\"createArticle:url\"),\n                                    name: \"urlEN\",\n                                    value: url,\n                                    onChange: (e)=>setUrl(e.target.value),\n                                    error: touched.urlEN && errors.urlEN\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 594,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 592,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 551,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:metaDescription\"),\n                                \" (\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: values.metaDescriptionEN?.length > 160 ? \" text-danger\" : \"\",\n                                    children: [\n                                        values.metaDescriptionEN?.length,\n                                        \" / 160\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                \")\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"metaDescriptionEN\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: metaDescription || values.metaDescriptionEN,\n                                    onChange: (e)=>{\n                                        const metaDescriptionEN = e.target.value;\n                                        setFieldValue(\"metaDescriptionEN\", metaDescriptionEN);\n                                        setMetaDescription(metaDescriptionEN);\n                                        debounce();\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.metaDescriptionEN && touched.metaDescriptionEN ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"metaDescriptionEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 608,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 607,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 606,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 605,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:featuredImage\"),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"upload-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: `image-upload-en`,\n                                        className: \"file-labels\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                id: `image-upload-en`,\n                                                name: \"imageEN\",\n                                                accept: \".png, .jpg, .jpeg, .webp\",\n                                                ref: imageInputRef,\n                                                onChange: (e)=>{\n                                                    setFieldValue(\"imageEN\", e.target.files[0]);\n                                                    handlePhotoChange();\n                                                },\n                                                className: \"file-input\" + (errors.imageEN && touched.imageEN ? \" is-invalid\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"upload-area\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"icon-pic\",\n                                                            style: {\n                                                                backgroundImage: `url(\"${selectedImage ? URL.createObjectURL(selectedImage) : values.imageEN ? `${process.env.REACT_APP_API_URL}${_utils_urls__WEBPACK_IMPORTED_MODULE_7__.API_URLS.files}/${values.imageEN}` : _assets_images_add_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"].src}\")`,\n                                                                backgroundSize: \"cover\",\n                                                                backgroundRepeat: \"no-repeat\",\n                                                                backgroundPosition: \"center\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-text\",\n                                                                children: t(\"createArticle:addFeatImg\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-description\",\n                                                                children: t(\"createArticle:clickBox\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                                name: \"image\",\n                                                component: \"div\",\n                                                className: \"invalid-feedback error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 651,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 650,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 649,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 648,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:alt\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"altEN\",\n                                        type: \"text\",\n                                        value: values.altEN,\n                                        onChange: (e)=>{\n                                            setFieldValue(\"altEN\", e.target.value);\n                                            debounce();\n                                        },\n                                        className: \"input-pentabell\" + (errors.altEN && touched.altEN ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"altEN\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 714,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 713,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 712,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:visibility\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"select-pentabell\",\n                                            variant: \"standard\",\n                                            value: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.Visibility.filter((option)=>values.visibilityEN === option),\n                                            selected: values.visibilityEN,\n                                            onChange: (event)=>{\n                                                setFieldValue(\"visibilityEN\", event.target.value);\n                                            },\n                                            children: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.Visibility.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    value: item,\n                                                    children: item\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"visibilityEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 740,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 738,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:keyword\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            id: \"tags\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                                tags: tags,\n                                                className: \"input-pentabell\" + (errors.keywordsEN && touched.keywordsEN ? \" is-invalid\" : \"\"),\n                                                delimiters: delimiters,\n                                                handleDelete: (i)=>{\n                                                    const updatedTags = tags.filter((tag, index)=>index !== i);\n                                                    setTags(updatedTags);\n                                                    setFieldValue(\"keywordsEN\", updatedTags.map((tag)=>tag.text));\n                                                },\n                                                handleAddition: (tag)=>{\n                                                    setTags([\n                                                        ...tags,\n                                                        tag\n                                                    ]);\n                                                    setFieldValue(\"keywordsEN\", [\n                                                        ...tags,\n                                                        tag\n                                                    ].map((item)=>item.text));\n                                                    debounce();\n                                                },\n                                                inputFieldPosition: \"bottom\",\n                                                autocomplete: true,\n                                                allowDragDrop: false\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"keywordsEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 774,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 773,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 771,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 711,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"label-form\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.Field, {\n                                    type: \"checkbox\",\n                                    name: \"publishNow\",\n                                    checked: publishNow,\n                                    onChange: (e)=>{\n                                        setPublishNow(e.target.checked);\n                                        if (e.target.checked) {\n                                            setFieldValue(\"publishDateEN\", new Date().toISOString());\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 13\n                                }, this),\n                                t(\"createArticle:publishNow\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 821,\n                            columnNumber: 11\n                        }, this),\n                        !publishNow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"label-form\",\n                                        children: [\n                                            t(\"createArticle:publishDate\"),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_26__.LocalizationProvider, {\n                                                dateAdapter: _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_27__.AdapterDayjs,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_28__.DemoContainer, {\n                                                    components: [\n                                                        \"DatePicker\"\n                                                    ],\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_29__.DatePicker, {\n                                                            variant: \"standard\",\n                                                            className: \"input-date\",\n                                                            format: \"DD/MM/YYYY\",\n                                                            value: dayjs__WEBPACK_IMPORTED_MODULE_12___default()(values.publishDateEN || new Date()),\n                                                            onChange: (date)=>{\n                                                                setFieldValue(\"publishDateEN\", dayjs__WEBPACK_IMPORTED_MODULE_12___default()(date).format(\"YYYY-MM-DD\"));\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                            lineNumber: 843,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 841,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"publishDateEN\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 858,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 838,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 837,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 820,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 819,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.Field, {\n                type: \"hidden\",\n                name: \"publishDateEN\",\n                value: publishNow ? new Date().toISOString() : publishDate.toISOString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 869,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AddArticleEN, \"nDc9FkDcP0selbZlc9Kghllvj14=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__.useSaveFile\n    ];\n});\n_c = AddArticleEN;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticleEN);\nvar _c;\n$RefreshReg$(_c, \"AddArticleEN\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9ibG9nL2NvbXBvbmVudHMvQWRkQXJ0aWNsZUVOLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQzZDO0FBQ087QUFDTDtBQUNQO0FBQ0w7QUFFbUI7QUFDRjtBQUNMO0FBQ0Y7QUFDQztBQUNBO0FBQ0Y7QUFDTjtBQVVmO0FBQ2dEO0FBQ1A7QUFDRztBQUN6QztBQUNpQztBQUNrQjtBQUN6QztBQUMwQjtBQUU5RCxTQUFTZ0MsYUFBYSxLQVVyQjtRQVZxQixFQUNwQkMsTUFBTSxFQUNOQyxPQUFPLEVBQ1BDLGFBQWEsRUFDYkMsTUFBTSxFQUNOQyxhQUFhLEVBQ2JDLGtCQUFrQixFQUNsQkMsVUFBVSxFQUNWQyxrQkFBa0IsRUFDbEJDLFFBQVEsRUFDVCxHQVZxQjs7SUFXcEIsTUFBTUMsV0FBVztRQUNmQyxPQUFPO1FBQ1BDLE9BQU87SUFDVDtJQUNBLE1BQU1DLGFBQWE7UUFBQ0gsU0FBU0MsS0FBSztRQUFFRCxTQUFTRSxLQUFLO0tBQUM7SUFDbkQsTUFBTSxDQUFDRSxNQUFNQyxRQUFRLEdBQUczQywrQ0FBUUEsQ0FBQyxFQUFFO0lBQ25DLE1BQU0sQ0FBQzRDLFlBQVlDLGNBQWMsR0FBRzdDLCtDQUFRQSxDQUFDLEVBQUU7SUFDL0MsTUFBTSxFQUFFOEMsQ0FBQyxFQUFFLEdBQUc3Qyw2REFBY0E7SUFDNUIsTUFBTSxDQUFDOEMsYUFBYUMsZUFBZSxHQUFHaEQsK0NBQVFBLENBQUNnQyxPQUFPaUIsYUFBYSxJQUFJO0lBQ3ZFLE1BQU0sQ0FBQ0MsS0FBS0MsT0FBTyxHQUFHbkQsK0NBQVFBLENBQUNnQyxPQUFPb0IsS0FBSyxJQUFJO0lBQy9DLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHdEQsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDdUQsYUFBYUMsZUFBZSxHQUFHeEQsK0NBQVFBLENBQUMsSUFBSXlEO0lBQ25ELE1BQU1DLGdCQUFnQjNELDZDQUFNQSxDQUFDO0lBQzdCLE1BQU0sQ0FBQzRELGVBQWVDLGlCQUFpQixHQUFHNUQsK0NBQVFBLENBQUM7SUFDbkQsTUFBTTZELFdBQVc7SUFDakIsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUcvRCwrQ0FBUUEsQ0FBQztRQUNqQyxNQUFNZ0UsYUFBYUMsYUFBYUMsT0FBTyxDQUFDO1FBQ3hDLE9BQU9GLGFBQWFHLEtBQUtDLEtBQUssQ0FBQ0osY0FBYztJQUMvQztJQUNBLE1BQU0sQ0FBQ0ssV0FBV0MsYUFBYSxHQUFHdEUsK0NBQVFBLENBQUM7UUFDekMsTUFBTXVFLGlCQUFpQk4sYUFBYUMsT0FBTyxDQUFDO1FBQzVDLE9BQU9LLGlCQUFpQkosS0FBS0MsS0FBSyxDQUFDRyxrQkFBa0I7SUFDdkQ7SUFDQSxNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUd6RSwrQ0FBUUEsQ0FBQztRQUNyRCxNQUFNMEUsdUJBQXVCVCxhQUFhQyxPQUFPLENBQUM7UUFDbEQsT0FBT1EsdUJBQXVCUCxLQUFLQyxLQUFLLENBQUNNLHdCQUF3QjtJQUNuRTtJQUVBLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHNUUsK0NBQVFBLENBQUM7UUFDckMsTUFBTTZFLGVBQWVaLGFBQWFDLE9BQU8sQ0FBQztRQUMxQyxPQUFPVyxlQUFlVixLQUFLQyxLQUFLLENBQUNTLGdCQUFnQjtJQUNuRDtJQUNBLE1BQU1DLGNBQWMsQ0FBQ0MsT0FBT0MsV0FBV0M7UUFDckMsSUFBSUMsT0FBT0Y7UUFFWCxxQ0FBcUM7UUFDckNFLE9BQU9BLEtBQUtDLE9BQU8sQ0FBQyxtQkFBbUI7UUFFdkMsT0FBT0Q7SUFDVDtJQUVBLE1BQU1FLHFCQUFxQixDQUFDQztRQUMxQmhEO1FBQ0F1QyxXQUFXUztRQUNYdEQsY0FBYyxhQUFhc0Q7SUFDN0I7SUFDQXZGLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSWdFLE9BQU87WUFDVEcsYUFBYXFCLE9BQU8sQ0FBQyxTQUFTbkIsS0FBS29CLFNBQVMsQ0FBQ3pCO1FBQy9DO1FBQ0EsSUFBSWEsU0FBUztZQUNYVixhQUFhcUIsT0FBTyxDQUFDLFdBQVduQixLQUFLb0IsU0FBUyxDQUFDWjtRQUNqRDtRQUNBLElBQUlOLFdBQVc7WUFDYkosYUFBYXFCLE9BQU8sQ0FBQyxhQUFhbkIsS0FBS29CLFNBQVMsQ0FBQ2xCO1FBQ25EO1FBQ0EsSUFBSUcsaUJBQWlCO1lBQ25CUCxhQUFhcUIsT0FBTyxDQUFDLG1CQUFtQm5CLEtBQUtvQixTQUFTLENBQUNmO1FBQ3pEO0lBQ0YsR0FBRztRQUFDVjtRQUFPYTtRQUFTTjtRQUFXRztLQUFnQjtJQUUvQyxNQUFNZ0Isb0JBQW9CO1FBQ3hCLE1BQU1DLGVBQWUvQixjQUFjZ0MsT0FBTyxDQUFDQyxLQUFLLENBQUMsRUFBRTtRQUNuRC9CLGlCQUFpQkYsY0FBY2dDLE9BQU8sQ0FBQ0MsS0FBSyxDQUFDLEVBQUU7UUFFL0MsSUFBSUYsY0FBYztZQUNoQnhELGNBQWN3RCxjQUFjNUI7UUFDOUI7SUFDRjtJQUNBL0QsZ0RBQVNBLENBQUM7UUFDUmlDLGNBQWMsV0FBVytCO1FBQ3pCL0IsY0FBYyxpQkFBaUJnQjtRQUMvQmhCLGNBQWMsU0FBU21CO1FBQ3ZCbkIsY0FDRSxjQUNBVyxLQUFLa0QsR0FBRyxDQUFDLENBQUM5QyxJQUFNQSxFQUFFK0MsSUFBSTtRQUV4QjlELGNBQ0UsZ0JBQ0FhLFdBQVdnRCxHQUFHLENBQUMsQ0FBQ0UsSUFBTUEsRUFBRUQsSUFBSTtRQUU5QjlELGNBQWMsYUFBYTRDO1FBQzNCNUMsY0FBYyxlQUFlc0M7UUFDN0J0QyxjQUFjLHFCQUFxQnlDO0lBQ3JDLEdBQUc7UUFDRFY7UUFDQWY7UUFDQUc7UUFDQVI7UUFDQUU7UUFDQStCO1FBQ0FOO1FBQ0FHO0tBQ0Q7SUFFRCxNQUFNdUIsa0JBQWtCdkUsMkZBQVdBO0lBRW5DLElBQUl3RTtJQUNKQSxZQUFZdEUsaURBQU1BLEdBQUd5RCxPQUFPLENBQUMsTUFBTTtJQUVuQyxNQUFNYyx3QkFBd0IsT0FBT0MsTUFBTUMsTUFBTUMsTUFBTUM7UUFDckQsSUFBSUgsZ0JBQWdCSSxrQkFBa0I7WUFDcEMsTUFBTUMsTUFBTUwsS0FBS0ssR0FBRztZQUVwQixJQUFJQSxJQUFJQyxVQUFVLENBQUMsZUFBZTtnQkFDaEMsTUFBTUMsYUFBYUYsSUFBSUcsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO2dCQUNwQyxNQUFNQyxjQUFjSixJQUFJSyxLQUFLLENBQUMsb0JBQW9CLENBQUMsRUFBRTtnQkFDckQsTUFBTUMsaUJBQWlCQyxLQUFLTDtnQkFDNUIsTUFBTU0sY0FBYyxJQUFJQyxNQUFNSCxlQUFlSSxNQUFNLEVBQ2hEQyxJQUFJLENBQUMsR0FDTHRCLEdBQUcsQ0FBQyxDQUFDdUIsR0FBR0MsSUFBTVAsZUFBZVEsVUFBVSxDQUFDRDtnQkFDM0MsTUFBTUUsWUFBWSxJQUFJQyxXQUFXUjtnQkFFakMsTUFBTVMsT0FBTyxJQUFJQyxLQUFLO29CQUFDSDtpQkFBVSxFQUFFO29CQUFFSSxNQUFNZjtnQkFBWTtnQkFDdkQsTUFBTWdCLFdBQVcsQ0FBQyxNQUFNLEVBQUVsRSxLQUFLbUUsR0FBRyxHQUFHLENBQUMsRUFBRWpCLFlBQVlELEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7Z0JBQ25FLE1BQU1qQixlQUFlLElBQUlvQyxLQUFLO29CQUFDTDtpQkFBSyxFQUFFRyxVQUFVO29CQUFFRCxNQUFNZjtnQkFBWTtnQkFFcEUsTUFBTW1CLFdBQVdyQyxjQUFjWSxlQUFlRCxNQUFNRjtZQUN0RCxPQUFPO2dCQUNMNkIsTUFBTXhCLEtBQ0h5QixJQUFJLENBQUMsQ0FBQ0MsV0FBYUEsU0FBU1QsSUFBSSxJQUNoQ1EsSUFBSSxDQUFDLENBQUNSO29CQUNMLE1BQU1iLGNBQWNhLEtBQUtFLElBQUk7b0JBQzdCLE1BQU1DLFdBQVcsQ0FBQyxNQUFNLEVBQUVsRSxLQUFLbUUsR0FBRyxHQUFHLENBQUMsRUFBRWpCLFlBQVlELEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7b0JBQ25FLE1BQU1qQixlQUFlLElBQUlvQyxLQUFLO3dCQUFDTDtxQkFBSyxFQUFFRyxVQUFVO3dCQUM5Q0QsTUFBTWY7b0JBQ1I7b0JBRUFtQixXQUFXckMsY0FBY1ksZUFBZUQsTUFBTUY7Z0JBQ2hELEdBQ0NnQyxLQUFLLENBQUMsQ0FBQ0MsUUFDTkMsUUFBUUQsS0FBSyxDQUFDLHVDQUF1Q0E7WUFFM0Q7UUFDRixPQUFPO1lBQ0xDLFFBQVFELEtBQUssQ0FBQztRQUNoQjtJQUNGO0lBRUEsTUFBTUwsYUFBYSxDQUFDckMsY0FBY1ksZUFBZUQsTUFBTWlDO1FBQ3JELElBQUlyQztRQUNKQSxZQUFZdEUsaURBQU1BLEdBQUd5RCxPQUFPLENBQUMsTUFBTTtRQUVuQyxNQUFNbUQsV0FBVyxJQUFJQztRQUNyQkQsU0FBU0UsTUFBTSxDQUFDLFFBQVEvQztRQUV4QixNQUFNZ0QsWUFBWWhELGFBQWFpRCxJQUFJLENBQUNoQyxLQUFLLENBQUMsS0FBS2lDLEdBQUc7UUFDbEQsTUFBTUMsY0FBYyxJQUFJbkYsT0FBT29GLFdBQVc7UUFFMUM5QyxnQkFBZ0IrQyxNQUFNLENBQ3BCO1lBQ0VDLFVBQVU7WUFDVkMsUUFBUUosWUFBWUssUUFBUTtZQUM1QkMsVUFBVWxEO1lBQ1ZtRCxNQUFNO2dCQUFFYjtnQkFBVXhGO1lBQUU7UUFDdEIsR0FDQTtZQUNFc0csV0FBVyxDQUFDQztnQkFDVixNQUFNQyxvQkFDSkQsU0FBU0UsT0FBTyxLQUFLLGVBQ2pCRixTQUFTRyxJQUFJLEdBQ2IsQ0FBQyxFQUFFeEQsVUFBVSxDQUFDLEVBQUV5QyxVQUFVLENBQUM7Z0JBRWpDLE1BQU1nQixXQUFXLENBQUMsRUFBRUMsOEJBQW9DLENBQUMsT0FBTyxFQUFFSixrQkFBa0IsQ0FBQztnQkFFckZqQixjQUFjOUIsR0FBRyxHQUFHa0Q7Z0JBRXBCcEQsY0FBYztvQkFDWndELFFBQVE7d0JBQ047NEJBQ0VDLElBQUlSOzRCQUNKcEcsS0FBS3VHO3dCQUNQO3FCQUNEO2dCQUNIO1lBQ0Y7WUFDQU0sU0FBUyxDQUFDNUI7Z0JBQ1JDLFFBQVFELEtBQUssQ0FBQyx5QkFBeUJBO1lBQ3pDO1FBQ0Y7SUFFSjtJQUVBLE1BQU0sQ0FBQ0EsT0FBTzZCLFNBQVMsR0FBR2hLLCtDQUFRQSxDQUFDO0lBRW5DLE1BQU1pSyxtQkFBbUIsT0FBT2xGO1FBQzlCLE1BQU1tQixPQUFPbkIsTUFBTW1GLE1BQU0sQ0FBQ3ZFLEtBQUssQ0FBQyxFQUFFO1FBQ2xDLElBQ0UsQ0FBQ08sUUFDREEsS0FBS3dCLElBQUksS0FDUCwyRUFDRjtZQUNBc0MsU0FBUztZQUNUO1FBQ0Y7UUFFQSxNQUFNRyxTQUFTLElBQUlDO1FBRW5CRCxPQUFPRSxNQUFNLEdBQUcsT0FBT3RGO1lBQ3JCLE1BQU11RixjQUFjdkYsTUFBTW1GLE1BQU0sQ0FBQ0wsTUFBTTtZQUV2QyxJQUFJO2dCQUNGLE1BQU1BLFNBQVMsTUFBTTFKLGtEQUFxQixDQUFDO29CQUFFbUs7Z0JBQVk7Z0JBQ3pEbEMsUUFBUW9DLEdBQUcsQ0FBQ1gsT0FBT1ksS0FBSztnQkFDeEIxSSxjQUFjLGFBQWE4SCxPQUFPWSxLQUFLO2dCQUN2QzdGLFdBQVdpRixPQUFPWSxLQUFLO2dCQUN2QlQsU0FBUztZQUNYLEVBQUUsT0FBT1UsS0FBSztnQkFDWnRDLFFBQVFELEtBQUssQ0FBQyw2QkFBNkJ1QztnQkFDM0NWLFNBQVM7WUFDWDtRQUNGO1FBRUFHLE9BQU9RLGlCQUFpQixDQUFDekU7SUFDM0I7SUFFQSxxQkFDRTs7MEJBQ0UsOERBQUMwRTtnQkFBRUMsV0FBVTswQkFBa0I7Ozs7OzswQkFDL0IsOERBQUNDO2dCQUFNcEQsTUFBSztnQkFBT3FELFFBQU87Z0JBQVFDLFVBQVVmOzs7Ozs7WUFDM0M5Qix1QkFBUyw4REFBQzhDO2dCQUFJQyxPQUFPO29CQUFFQyxPQUFPO2dCQUFNOzBCQUFJaEQ7Ozs7OzswQkFFekMsOERBQUM4QztnQkFBSUosV0FBVTs7a0NBQ2IsOERBQUNJOzs0QkFDRTswQ0FDRCw4REFBQ3RLLDZJQUFTQTswQ0FDUiw0RUFBQ2dCLHVFQUFlQTtvQ0FDZHlKLE9BQU90SSxFQUFFO29DQUNUNEYsTUFBSztvQ0FDTCtCLE9BQU8zRztvQ0FDUGtILFVBQVUsQ0FBQ0s7d0NBQ1QsTUFBTUMsSUFBSUQsRUFBRW5CLE1BQU0sQ0FBQ08sS0FBSzt3Q0FDeEIxRyxTQUFTdUg7d0NBQ1RuSSxPQUFPNUMsNkRBQUlBLENBQUMrSzt3Q0FDWmpKO29DQUNGO29DQUNBOEYsT0FBT3JHLFFBQVF5SixPQUFPLElBQUkxSixPQUFPMEosT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSzlDLDhEQUFDTjs7MENBQ0MsOERBQUN0Syw2SUFBU0E7MENBQ1IsNEVBQUNDLDZJQUFTQTtvQ0FBQ2lLLFdBQVU7O3dDQUNsQi9ILEVBQUU7c0RBQ0gsOERBQUMvQiw2SUFBS0E7c0RBQ0osNEVBQUNMLDZJQUFZQTtnREFDWDhLLFFBQVE7Z0RBQ1JYLFdBQVU7Z0RBQ1ZmLElBQUc7Z0RBQ0gyQixTQUNFdkosbUJBQW1CK0UsTUFBTSxHQUFHLElBQ3hCL0UscUJBQ0FDO2dEQUVOdUosZ0JBQWdCLENBQUNDLFNBQVdBLE9BQU9qRCxJQUFJO2dEQUN2Q2tELFVBQ0U1SixPQUFPNkosVUFBVSxDQUFDNUUsTUFBTSxHQUFHLElBQ3ZCLENBQUMvRSxtQkFBbUIrRSxNQUFNLEdBQUcsSUFDekIvRSxxQkFDQUMsVUFBUyxFQUNYMkosTUFBTSxDQUFDLENBQUNDLFdBQ1IvSixPQUFPNkosVUFBVSxDQUFDRyxRQUFRLENBQUNELFNBQVNqQyxFQUFFLEtBRXhDLEVBQUU7Z0RBRVJrQixVQUFVLENBQUNqRyxPQUFPa0g7b0RBQ2hCLE1BQU1DLGNBQWNELGdCQUFnQnJHLEdBQUcsQ0FDckMsQ0FBQ21HLFdBQWFBLFNBQVNqQyxFQUFFO29EQUUzQi9ILGNBQWMsY0FBY21LO29EQUM1QjlKLG1CQUFtQjhKO2dEQUNyQjtnREFDQUMsYUFBYSxDQUFDQyx1QkFDWiw4REFBQ3BMLDZJQUFTQTt3REFDUCxHQUFHb0wsTUFBTTt3REFDVnZCLFdBQVU7d0RBQ1Z3QixTQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBU25CdkssUUFBUWlLLFFBQVEsSUFBSWxLLE9BQU9rSyxRQUFRLGtCQUNsQyw4REFBQ2Q7Z0NBQUlKLFdBQVU7MENBQWVoSixPQUFPa0ssUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUluRCw4REFBQ2Q7Z0JBQUlKLFdBQVU7MEJBQ2IsNEVBQUNJOzhCQUNDLDRFQUFDdEssNklBQVNBO2tDQUNSLDRFQUFDQyw2SUFBU0E7NEJBQUNpSyxXQUFVOztnQ0FBYTs4Q0FFaEMsOERBQUM3Siw2SUFBU0E7b0NBQ1JxTCxTQUFRO29DQUNSM0QsTUFBSztvQ0FDTGhCLE1BQUs7b0NBQ0w0RSxTQUFTO29DQUNUQyxNQUFNO29DQUNOOUIsT0FBT3pJLE9BQU9pQixhQUFhO29DQUMzQitILFVBQVUsQ0FBQ0s7d0NBQ1QsTUFBTXBJLGdCQUFnQm9JLEVBQUVuQixNQUFNLENBQUNPLEtBQUs7d0NBQ3BDMUksY0FBYyxpQkFBaUJrQjtvQ0FDakM7b0NBQ0E0SCxXQUNFLHVCQUNDaEosQ0FBQUEsT0FBT29CLGFBQWEsSUFBSW5CLFFBQVFtQixhQUFhLEdBQzFDLGdCQUNBLEVBQUM7Ozs7OztnQ0FFTjs4Q0FDSCw4REFBQ3JELGlEQUFZQTtvQ0FDWGlMLFdBQVU7b0NBQ1ZuQyxNQUFLO29DQUNMOEQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1wQiw4REFBQ3ZCO2dCQUFJSixXQUFVOzBCQUNiLDRFQUFDSTs4QkFDQyw0RUFBQ3RLLDZJQUFTQTtrQ0FDUiw0RUFBQ0MsNklBQVNBOzRCQUFDaUssV0FBVTs7Z0NBQWE7OENBRWhDLDhEQUFDSTtvQ0FBSW5CLElBQUc7OENBQ04sNEVBQUN2SSx5REFBU0E7d0NBQ1JtQixNQUFNRTt3Q0FDTmlJLFdBQ0Usb0JBQ0NoSixDQUFBQSxPQUFPNEssWUFBWSxJQUFJM0ssUUFBUTJLLFlBQVksR0FDeEMsZ0JBQ0EsRUFBQzt3Q0FFUGhLLFlBQVlBO3dDQUNaaUssY0FBYyxDQUFDdEY7NENBQ2IsTUFBTXVGLGNBQWMvSixXQUFXa0osTUFBTSxDQUNuQyxDQUFDYyxLQUFLQyxRQUFVQSxVQUFVekY7NENBRTVCdkUsY0FBYzhKOzRDQUNkNUssY0FDRSxnQkFDQTRLLFlBQVkvRyxHQUFHLENBQUMsQ0FBQ2dILE1BQVFBLElBQUkvRyxJQUFJO3dDQUVyQzt3Q0FDQWlILGdCQUFnQixDQUFDRjs0Q0FDZi9KLGNBQWM7bURBQUlEO2dEQUFZZ0s7NkNBQUk7NENBQ2xDN0ssY0FDRSxnQkFDQTttREFBSWE7Z0RBQVlnSzs2Q0FBSSxDQUFDaEgsR0FBRyxDQUFDLENBQUNtSCxPQUFTQSxLQUFLbEgsSUFBSTt3Q0FFaEQ7d0NBQ0FtSCxvQkFBbUI7d0NBQ25CQyxZQUFZO3dDQUNaQyxlQUFlOzs7Ozs7Ozs7Ozs4Q0FHbkIsOERBQUN0TixpREFBWUE7b0NBQ1hpTCxXQUFVO29DQUNWbkMsTUFBSztvQ0FDTDhELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNcEIsOERBQUN0TSx3REFBU0E7Z0JBQ1JpTixhQUFheEksV0FBVzNDLFFBQVFvTCxhQUFhO2dCQUM3Q3BDLFVBQVUsQ0FBQzNGO29CQUNUVCxXQUFXUztvQkFDWHRELGNBQWMsYUFBYXNEO29CQUMzQmhEO2dCQUNGO2dCQUNBZ0wsU0FBU3ZJO2dCQUNUd0ksWUFBWTtvQkFDVkMsV0FBVztvQkFDWEMsc0JBQXNCO29CQUN0QkMsa0JBQ0U7b0JBQ0ZqTixTQUFTQSw4REFBT0E7b0JBQ2hCa04sWUFBWTt3QkFDVjs0QkFBQzs0QkFBUTt5QkFBTzt3QkFDaEI7NEJBQUM7NEJBQVE7NEJBQVk7eUJBQWM7d0JBQ25DOzRCQUNFOzRCQUNBOzRCQUNBOzRCQUNBOzRCQUNBOzRCQUNBO3lCQUNEO3dCQUNEOzRCQUFDOzRCQUFhO3lCQUFjO3dCQUM1Qjs0QkFBQzs0QkFBUzs0QkFBUTt5QkFBYTt3QkFDL0I7NEJBQUM7NEJBQVc7eUJBQVM7d0JBQ3JCOzRCQUFDOzRCQUFTOzRCQUFrQjs0QkFBUTs0QkFBUzt5QkFBUTt3QkFDckQ7NEJBQUM7NEJBQWM7NEJBQWM7eUJBQVc7d0JBQ3hDOzRCQUFDOzRCQUFXO3lCQUFRO3dCQUNwQjs0QkFBQzt5QkFBZTtxQkFDakI7b0JBQ0RDLG9CQUFvQjFIO29CQUNwQjJILFlBQVk7b0JBQ1pDLFdBQVc7b0JBQ1hDLFdBQVc7b0JBQ1hDLGVBQWU7b0JBQ2ZDLE1BQU07d0JBQ0o7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7cUJBQ0Q7b0JBQ0RDLGFBQWE7b0JBQ2JDLGlCQUFpQjtvQkFDakJDLGFBQWE7b0JBQ2JDLFdBQVc7d0JBQ1Qsa0JBQWtCO3dCQUNsQjs0QkFDRTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTt5QkFDRDtxQkFDRjtnQkFDSDtnQkFDQUMsZUFBZXBJOzs7Ozs7MEJBRWpCLDhEQUFDcUk7Ozs7OzBCQUVELDhEQUFDN04sb0RBQVVBO2dCQUNUdUIsUUFBUUE7Z0JBQ1JELGVBQWVBO2dCQUNmRixRQUFRQTtnQkFDUkMsU0FBU0E7Z0JBQ1QrQixVQUFTO2dCQUNUeEIsVUFBVUE7Ozs7OzswQkFFWiw4REFBQ2lNOzs7OzswQkFDRCw4REFBQ3JEO2dCQUFJSixXQUFVOztrQ0FDYiw4REFBQ0k7OzRCQUNFOzBDQUNELDhEQUFDdEssNklBQVNBOzBDQUNSLDRFQUFDQyw2SUFBU0E7b0NBQUNpSyxXQUFVOzt3Q0FDbEIvSCxFQUFFO3dDQUEyQjt3Q0FBRztzREFDakMsOERBQUN5TDs0Q0FDQzFELFdBQ0U3SSxPQUFPd00sV0FBVyxFQUFFdkgsU0FBUyxLQUFLLGlCQUFpQjs7Z0RBR3BEO2dEQUNBakYsT0FBT3dNLFdBQVcsRUFBRXZIO2dEQUFPO2dEQUFNOzs7Ozs7O3dDQUM1Qjt3Q0FBSTtzREFFWiw4REFBQ2pHLDZJQUFTQTs0Q0FDUnFMLFNBQVE7NENBQ1IzRCxNQUFLOzRDQUNMaEIsTUFBSzs0Q0FDTCtDLE9BQU9wRyxhQUFhckMsT0FBT3dNLFdBQVc7NENBQ3RDeEQsVUFBVSxDQUFDSztnREFDVCxNQUFNbUQsY0FBY25ELEVBQUVuQixNQUFNLENBQUNPLEtBQUs7Z0RBQ2xDMUksY0FBYyxlQUFleU07Z0RBQzdCbEssYUFBYWtLO2dEQUNibk07NENBQ0Y7NENBQ0F3SSxXQUNFLG9CQUNDaEosQ0FBQUEsT0FBTzJNLFdBQVcsSUFBSTFNLFFBQVEwTSxXQUFXLEdBQ3RDLGdCQUNBLEVBQUM7Ozs7Ozt3Q0FFTjtzREFDSCw4REFBQzVPLGlEQUFZQTs0Q0FDWGlMLFdBQVU7NENBQ1ZuQyxNQUFLOzRDQUNMOEQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS2xCLDhEQUFDdkI7OzRCQUNFOzBDQUNELDhEQUFDdEssNklBQVNBOzBDQUNSLDRFQUFDZ0IsdUVBQWVBO29DQUNkeUosT0FBT3RJLEVBQUU7b0NBQ1Q0RixNQUFLO29DQUNMK0IsT0FBT3ZIO29DQUNQOEgsVUFBVSxDQUFDSyxJQUFNbEksT0FBT2tJLEVBQUVuQixNQUFNLENBQUNPLEtBQUs7b0NBQ3RDdEMsT0FBT3JHLFFBQVFzQixLQUFLLElBQUl2QixPQUFPdUIsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBSzVDLDhEQUFDNkg7Z0JBQUlKLFdBQVU7MEJBQ2IsNEVBQUNJOzhCQUNDLDRFQUFDdEssNklBQVNBO2tDQUNSLDRFQUFDQyw2SUFBU0E7NEJBQUNpSyxXQUFVOztnQ0FDbEIvSCxFQUFFO2dDQUFpQztnQ0FBRzs4Q0FDdkMsOERBQUN5TDtvQ0FDQzFELFdBQ0U3SSxPQUFPeU0saUJBQWlCLEVBQUV4SCxTQUFTLE1BQU0saUJBQWlCOzt3Q0FHM0RqRixPQUFPeU0saUJBQWlCLEVBQUV4SDt3Q0FBTzs7Ozs7OztnQ0FDNUI7Z0NBQUk7OENBRVosOERBQUNqRyw2SUFBU0E7b0NBQ1JxTCxTQUFRO29DQUNSM0QsTUFBSztvQ0FDTGhCLE1BQUs7b0NBQ0w0RSxTQUFTO29DQUNUQyxNQUFNO29DQUNOOUIsT0FBT2pHLG1CQUFtQnhDLE9BQU95TSxpQkFBaUI7b0NBQ2xEekQsVUFBVSxDQUFDSzt3Q0FDVCxNQUFNb0Qsb0JBQW9CcEQsRUFBRW5CLE1BQU0sQ0FBQ08sS0FBSzt3Q0FDeEMxSSxjQUFjLHFCQUFxQjBNO3dDQUNuQ2hLLG1CQUFtQmdLO3dDQUNuQnBNO29DQUNGO29DQUNBd0ksV0FDRSx1QkFDQ2hKLENBQUFBLE9BQU80TSxpQkFBaUIsSUFBSTNNLFFBQVEyTSxpQkFBaUIsR0FDbEQsZ0JBQ0EsRUFBQzs7Ozs7O2dDQUVOOzhDQUNILDhEQUFDN08saURBQVlBO29DQUNYaUwsV0FBVTtvQ0FDVm5DLE1BQUs7b0NBQ0w4RCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3BCLDhEQUFDdkI7Z0JBQUlKLFdBQVU7MEJBQ2IsNEVBQUNJOzhCQUNDLDRFQUFDdEssNklBQVNBO2tDQUNSLDRFQUFDQyw2SUFBU0E7NEJBQUNpSyxXQUFVOztnQ0FDbEIvSCxFQUFFOzhDQUVILDhEQUFDbUk7b0NBQUlKLFdBQVU7OENBQ2IsNEVBQUNPO3dDQUFNc0QsU0FBUyxDQUFDLGVBQWUsQ0FBQzt3Q0FBRTdELFdBQVU7OzBEQUMzQyw4REFBQ0M7Z0RBQ0NwRCxNQUFLO2dEQUNMb0MsSUFBSSxDQUFDLGVBQWUsQ0FBQztnREFDckJwQixNQUFLO2dEQUNMcUMsUUFBTztnREFDUDRELEtBQUtqTDtnREFDTHNILFVBQVUsQ0FBQ0s7b0RBQ1R0SixjQUFjLFdBQVdzSixFQUFFbkIsTUFBTSxDQUFDdkUsS0FBSyxDQUFDLEVBQUU7b0RBQzFDSDtnREFDRjtnREFDQXFGLFdBQ0UsZUFDQ2hKLENBQUFBLE9BQU8rTSxPQUFPLElBQUk5TSxRQUFROE0sT0FBTyxHQUFHLGdCQUFnQixFQUFDOzs7Ozs7MERBRzFELDhEQUFDM0Q7Z0RBQUlKLFdBQVU7O2tFQUNiLDhEQUFDSTtrRUFDQyw0RUFBQ0E7NERBQ0NKLFdBQVU7NERBQ1ZLLE9BQU87Z0VBQ0wyRCxpQkFBaUIsQ0FBQyxLQUFLLEVBQ3JCbEwsZ0JBQ0ltTCxJQUFJQyxlQUFlLENBQUNwTCxpQkFDcEIzQixPQUFPNE0sT0FBTyxHQUNkLENBQUMsRUFBRWxGLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ3FGLGlCQUFpQixDQUFDLEVBQUUzTyxpREFBUUEsQ0FBQ3NGLEtBQUssQ0FBQyxDQUFDLEVBQUUzRCxPQUFPNE0sT0FBTyxDQUFDLENBQUMsR0FDckV0Tyw4REFBTUEsQ0FBQ2lHLEdBQUcsQ0FDZixFQUFFLENBQUM7Z0VBQ0owSSxnQkFBZ0I7Z0VBQ2hCQyxrQkFBa0I7Z0VBQ2xCQyxvQkFBb0I7NERBRXRCOzs7Ozs7Ozs7OztrRUFHSiw4REFBQ2xFOzswRUFDQyw4REFBQ0w7Z0VBQUVDLFdBQVU7MEVBQ1YvSCxFQUFFOzs7Ozs7MEVBRUwsOERBQUM4SDtnRUFBRUMsV0FBVTswRUFDVi9ILEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFJVCw4REFBQ2xELGlEQUFZQTtnREFDWDhJLE1BQUs7Z0RBQ0w4RCxXQUFVO2dEQUNWM0IsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU3hCLDhEQUFDSTtnQkFBSUosV0FBVTs7a0NBQ2IsOERBQUNJO2tDQUNDLDRFQUFDdEssNklBQVNBO3NDQUNSLDRFQUFDQyw2SUFBU0E7Z0NBQUNpSyxXQUFVOztvQ0FDbEIvSCxFQUFFO2tEQUNILDhEQUFDOUIsNklBQVNBO3dDQUNScUwsU0FBUTt3Q0FDUjNELE1BQUs7d0NBQ0xoQixNQUFLO3dDQUNMK0MsT0FBT3pJLE9BQU9vTixLQUFLO3dDQUNuQnBFLFVBQVUsQ0FBQ0s7NENBQ1R0SixjQUFjLFNBQVNzSixFQUFFbkIsTUFBTSxDQUFDTyxLQUFLOzRDQUNyQ3BJO3dDQUNGO3dDQUNBd0ksV0FDRSxvQkFDQ2hKLENBQUFBLE9BQU91TixLQUFLLElBQUl0TixRQUFRc04sS0FBSyxHQUFHLGdCQUFnQixFQUFDOzs7Ozs7b0NBRW5EO2tEQUNILDhEQUFDeFAsaURBQVlBO3dDQUNYaUwsV0FBVTt3Q0FDVm5DLE1BQUs7d0NBQ0w4RCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtsQiw4REFBQ3ZCOzs0QkFDRTswQ0FDRCw4REFBQ3RLLDZJQUFTQTswQ0FDUiw0RUFBQ0MsNklBQVNBO29DQUFDaUssV0FBVTs7d0NBQ2xCL0gsRUFBRTtzREFFSCw4REFBQ2hDLDZJQUFNQTs0Q0FDTCtKLFdBQVU7NENBQ1Z3QixTQUFROzRDQUNSNUIsT0FBT3JLLHdEQUFVQSxDQUFDMEwsTUFBTSxDQUN0QixDQUFDSCxTQUFXM0osT0FBT3FOLFlBQVksS0FBSzFEOzRDQUV0Q0MsVUFBVTVKLE9BQU9xTixZQUFZOzRDQUM3QnJFLFVBQVUsQ0FBQ2pHO2dEQUNUaEQsY0FBYyxnQkFBZ0JnRCxNQUFNbUYsTUFBTSxDQUFDTyxLQUFLOzRDQUNsRDtzREFFQ3JLLHdEQUFVQSxDQUFDd0YsR0FBRyxDQUFDLENBQUNtSCxNQUFNRixzQkFDckIsOERBQUNoTSw2SUFBUUE7b0RBQWE0SixPQUFPc0M7OERBQzFCQTttREFEWUY7Ozs7Ozs7Ozs7c0RBTW5CLDhEQUFDak4saURBQVlBOzRDQUNYaUwsV0FBVTs0Q0FDVm5DLE1BQUs7NENBQ0w4RCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNbEIsOERBQUN2Qjs7NEJBQ0U7MENBQ0QsOERBQUN0Syw2SUFBU0E7MENBQ1IsNEVBQUNDLDZJQUFTQTtvQ0FBQ2lLLFdBQVU7O3dDQUNsQi9ILEVBQUU7c0RBRUgsOERBQUNtSTs0Q0FBSW5CLElBQUc7c0RBQ04sNEVBQUN2SSx5REFBU0E7Z0RBQ1JtQixNQUFNQTtnREFDTm1JLFdBQ0Usb0JBQ0NoSixDQUFBQSxPQUFPeU4sVUFBVSxJQUFJeE4sUUFBUXdOLFVBQVUsR0FDcEMsZ0JBQ0EsRUFBQztnREFFUDdNLFlBQVlBO2dEQUNaaUssY0FBYyxDQUFDdEY7b0RBQ2IsTUFBTXVGLGNBQWNqSyxLQUFLb0osTUFBTSxDQUM3QixDQUFDYyxLQUFLQyxRQUFVQSxVQUFVekY7b0RBRTVCekUsUUFBUWdLO29EQUNSNUssY0FDRSxjQUNBNEssWUFBWS9HLEdBQUcsQ0FBQyxDQUFDZ0gsTUFBUUEsSUFBSS9HLElBQUk7Z0RBRXJDO2dEQUNBaUgsZ0JBQWdCLENBQUNGO29EQUNmakssUUFBUTsyREFBSUQ7d0RBQU1rSztxREFBSTtvREFDdEI3SyxjQUNFLGNBQ0E7MkRBQUlXO3dEQUFNa0s7cURBQUksQ0FBQ2hILEdBQUcsQ0FBQyxDQUFDbUgsT0FBU0EsS0FBS2xILElBQUk7b0RBRXhDeEQ7Z0RBQ0Y7Z0RBQ0EySyxvQkFBbUI7Z0RBQ25CQyxZQUFZO2dEQUNaQyxlQUFlOzs7Ozs7Ozs7OztzREFHbkIsOERBQUN0TixpREFBWUE7NENBQ1hpTCxXQUFVOzRDQUNWbkMsTUFBSzs0Q0FDTDhELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1wQiw4REFBQ3ZCO2dCQUFJSixXQUFVOzBCQUNiLDRFQUFDSTs7c0NBQ0MsOERBQUNHOzRCQUFNUCxXQUFVOzs4Q0FDZiw4REFBQ2hMLDBDQUFLQTtvQ0FDSjZILE1BQUs7b0NBQ0xnQixNQUFLO29DQUNMNkcsU0FBU2xNO29DQUNUMkgsVUFBVSxDQUFDSzt3Q0FDVC9ILGNBQWMrSCxFQUFFbkIsTUFBTSxDQUFDcUYsT0FBTzt3Q0FDOUIsSUFBSWxFLEVBQUVuQixNQUFNLENBQUNxRixPQUFPLEVBQUU7NENBQ3BCeE4sY0FBYyxpQkFBaUIsSUFBSTBCLE9BQU8rTCxXQUFXO3dDQUN2RDtvQ0FDRjs7Ozs7O2dDQUVEMU0sRUFBRTs7Ozs7Ozt3QkFHSixDQUFDTyw0QkFDQSw4REFBQzRIO3NDQUNDLDRFQUFDdEssNklBQVNBOztrREFDUiw4REFBQ0MsNklBQVNBO3dDQUFDaUssV0FBVTs7NENBQ2xCL0gsRUFBRTswREFDSCw4REFBQzVCLHNFQUFvQkE7Z0RBQUN1TyxhQUFhdE8sMkVBQVlBOzBEQUM3Qyw0RUFBQ0MsOEVBQWFBO29EQUFDc08sWUFBWTt3REFBQztxREFBYTs7c0VBQ3ZDLDhEQUFDek8sNERBQVVBOzREQUNUb0wsU0FBUTs0REFDUnhCLFdBQVU7NERBQ1Y4RSxRQUFPOzREQUNQbEYsT0FBT3BKLDZDQUFLQSxDQUFDVyxPQUFPNE4sYUFBYSxJQUFJLElBQUluTTs0REFDekN1SCxVQUFVLENBQUM2RTtnRUFDVDlOLGNBQ0UsaUJBQ0FWLDZDQUFLQSxDQUFDd08sTUFBTUYsTUFBTSxDQUFDOzREQUV2Qjs7Ozs7O3dEQUNDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBR0k7a0RBQ2IsOERBQUMvUCxpREFBWUE7d0NBQ1hpTCxXQUFVO3dDQUNWbkMsTUFBSzt3Q0FDTDhELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUXRCLDhEQUFDM00sMENBQUtBO2dCQUNKNkgsTUFBSztnQkFDTGdCLE1BQUs7Z0JBQ0wrQixPQUNFcEgsYUFBYSxJQUFJSSxPQUFPK0wsV0FBVyxLQUFLak0sWUFBWWlNLFdBQVc7Ozs7Ozs7O0FBS3pFO0dBMzBCUzVOOztRQWtCTzNCLHlEQUFjQTtRQXdGSnVCLHVGQUFXQTs7O0tBMUc1Qkk7QUE2MEJULCtEQUFlQSxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9mZWF0dXJlcy9ibG9nL2NvbXBvbmVudHMvQWRkQXJ0aWNsZUVOLmpzeD85NDA3Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgeyBFcnJvck1lc3NhZ2UsIEZpZWxkIH0gZnJvbSBcImZvcm1pa1wiO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tIFwicmVhY3QtaTE4bmV4dFwiO1xyXG5pbXBvcnQgU3VuRWRpdG9yIGZyb20gXCJzdW5lZGl0b3ItcmVhY3RcIjtcclxuaW1wb3J0ICogYXMgbWFtbW90aCBmcm9tIFwibWFtbW90aFwiO1xyXG5cclxuaW1wb3J0IHsgVmlzaWJpbGl0eSB9IGZyb20gXCIuLi8uLi8uLi91dGlscy9jb25zdGFudHNcIjtcclxuaW1wb3J0IFwicmVhY3QtZGF0ZXBpY2tlci9kaXN0L3JlYWN0LWRhdGVwaWNrZXIuY3NzXCI7XHJcbmltcG9ydCB7IEFQSV9VUkxTIH0gZnJvbSBcIi4uLy4uLy4uL3V0aWxzL3VybHNcIjtcclxuaW1wb3J0IHVwbG9hZCBmcm9tIFwiQC9hc3NldHMvaW1hZ2VzL2FkZC5wbmdcIjtcclxuaW1wb3J0IFwic3VuZWRpdG9yL2Rpc3QvY3NzL3N1bmVkaXRvci5taW4uY3NzXCI7XHJcbmltcG9ydCB7IHNsdWcgfSBmcm9tIFwiQGZlZWxpbmdsb3ZlbHlub3cvc2x1Z1wiO1xyXG5pbXBvcnQgcGx1Z2lucyBmcm9tIFwic3VuZWRpdG9yL3NyYy9wbHVnaW5zXCI7XHJcbmltcG9ydCBGYXFTZWN0aW9uIGZyb20gXCIuL0ZhcVNlY3Rpb25cIjtcclxuXHJcbmltcG9ydCB7XHJcbiAgQXV0b2NvbXBsZXRlLFxyXG4gIEZvcm1Hcm91cCxcclxuICBGb3JtTGFiZWwsXHJcbiAgTWVudUl0ZW0sXHJcbiAgU2VsZWN0LFxyXG4gIFN0YWNrLFxyXG4gIFRleHRGaWVsZCxcclxufSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyBEYXRlUGlja2VyLCBMb2NhbGl6YXRpb25Qcm92aWRlciB9IGZyb20gXCJAbXVpL3gtZGF0ZS1waWNrZXJzXCI7XHJcbmltcG9ydCB7IEFkYXB0ZXJEYXlqcyB9IGZyb20gXCJAbXVpL3gtZGF0ZS1waWNrZXJzL0FkYXB0ZXJEYXlqc1wiO1xyXG5pbXBvcnQgeyBEZW1vQ29udGFpbmVyIH0gZnJvbSBcIkBtdWkveC1kYXRlLXBpY2tlcnMvaW50ZXJuYWxzL2RlbW9cIjtcclxuaW1wb3J0IGRheWpzIGZyb20gXCJkYXlqc1wiO1xyXG5pbXBvcnQgeyBXaXRoQ29udGV4dCBhcyBSZWFjdFRhZ3MgfSBmcm9tIFwicmVhY3QtdGFnLWlucHV0XCI7XHJcbmltcG9ydCB7IHVzZVNhdmVGaWxlIH0gZnJvbSBcIkAvZmVhdHVyZXMvb3Bwb3J0dW5pdHkvaG9va3Mvb3Bwb3J0dW5pdHkuaG9va3NcIjtcclxuaW1wb3J0IHsgdjQgYXMgdXVpZHY0IH0gZnJvbSBcInV1aWRcIjtcclxuaW1wb3J0IEN1c3RvbVRleHRJbnB1dCBmcm9tIFwiQC9jb21wb25lbnRzL3VpL0N1c3RvbVRleHRJbnB1dFwiO1xyXG5cclxuZnVuY3Rpb24gQWRkQXJ0aWNsZUVOKHtcclxuICBlcnJvcnMsXHJcbiAgdG91Y2hlZCxcclxuICBzZXRGaWVsZFZhbHVlLFxyXG4gIHZhbHVlcyxcclxuICBvbkltYWdlU2VsZWN0LFxyXG4gIGZpbHRlcmVkQ2F0ZWdvcmllcyxcclxuICBjYXRlZ29yaWVzLFxyXG4gIG9uQ2F0ZWdvcmllc1NlbGVjdCxcclxuICBkZWJvdW5jZSxcclxufSkge1xyXG4gIGNvbnN0IEtleUNvZGVzID0ge1xyXG4gICAgY29tbWE6IDE4OCxcclxuICAgIGVudGVyOiAxMyxcclxuICB9O1xyXG4gIGNvbnN0IGRlbGltaXRlcnMgPSBbS2V5Q29kZXMuY29tbWEsIEtleUNvZGVzLmVudGVyXTtcclxuICBjb25zdCBbdGFncywgc2V0VGFnc10gPSB1c2VTdGF0ZShbXSk7XHJcbiAgY29uc3QgW2hpZ2hsaWdodHMsIHNldEhpZ2hsaWdodHNdID0gdXNlU3RhdGUoW10pO1xyXG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oKTtcclxuICBjb25zdCBbZGVzY3JpcHRpb24sIHNldERlc2NyaXB0aW9uXSA9IHVzZVN0YXRlKHZhbHVlcy5kZXNjcmlwdGlvbkVOIHx8IFwiXCIpO1xyXG4gIGNvbnN0IFt1cmwsIHNldFVybF0gPSB1c2VTdGF0ZSh2YWx1ZXMudXJsRU4gfHwgXCJcIik7XHJcbiAgY29uc3QgW3B1Ymxpc2hOb3csIHNldFB1Ymxpc2hOb3ddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtwdWJsaXNoRGF0ZSwgc2V0UHVibGlzaERhdGVdID0gdXNlU3RhdGUobmV3IERhdGUoKSk7XHJcbiAgY29uc3QgaW1hZ2VJbnB1dFJlZiA9IHVzZVJlZihudWxsKTtcclxuICBjb25zdCBbc2VsZWN0ZWRJbWFnZSwgc2V0U2VsZWN0ZWRJbWFnZV0gPSB1c2VTdGF0ZShudWxsKTtcclxuICBjb25zdCBsYW5ndWFnZSA9IFwiZW5cIjtcclxuICBjb25zdCBbdGl0bGUsIHNldFRpdGxlXSA9IHVzZVN0YXRlKCgpID0+IHtcclxuICAgIGNvbnN0IHNhdmVkVGl0bGUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcInRpdGxlXCIpO1xyXG4gICAgcmV0dXJuIHNhdmVkVGl0bGUgPyBKU09OLnBhcnNlKHNhdmVkVGl0bGUpIDogXCJcIjtcclxuICB9KTtcclxuICBjb25zdCBbbWV0YXRpdGxlLCBzZXRNZXRhdGl0bGVdID0gdXNlU3RhdGUoKCkgPT4ge1xyXG4gICAgY29uc3Qgc2F2ZWRNZXRhdGl0bGUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcIm1ldGF0aXRsZVwiKTtcclxuICAgIHJldHVybiBzYXZlZE1ldGF0aXRsZSA/IEpTT04ucGFyc2Uoc2F2ZWRNZXRhdGl0bGUpIDogXCJcIjtcclxuICB9KTtcclxuICBjb25zdCBbbWV0YURlc2NyaXB0aW9uLCBzZXRNZXRhRGVzY3JpcHRpb25dID0gdXNlU3RhdGUoKCkgPT4ge1xyXG4gICAgY29uc3Qgc2F2ZWRNZXRhZGVzY3JpcHRpb24gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcIm1ldGFEZXNjcmlwdGlvblwiKTtcclxuICAgIHJldHVybiBzYXZlZE1ldGFkZXNjcmlwdGlvbiA/IEpTT04ucGFyc2Uoc2F2ZWRNZXRhZGVzY3JpcHRpb24pIDogXCJcIjtcclxuICB9KTtcclxuXHJcbiAgY29uc3QgW2NvbnRlbnQsIHNldENvbnRlbnRdID0gdXNlU3RhdGUoKCkgPT4ge1xyXG4gICAgY29uc3Qgc2F2ZWRDb250ZW50ID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJjb250ZW50XCIpO1xyXG4gICAgcmV0dXJuIHNhdmVkQ29udGVudCA/IEpTT04ucGFyc2Uoc2F2ZWRDb250ZW50KSA6IFwiXCI7XHJcbiAgfSk7XHJcbiAgY29uc3QgaGFuZGxlUGFzdGUgPSAoZXZlbnQsIGNsZWFuRGF0YSwgbWF4Q2hhckNvdW50KSA9PiB7XHJcbiAgICBsZXQgaHRtbCA9IGNsZWFuRGF0YTtcclxuXHJcbiAgICAvLyBDb3JyZWN0aW9uIGRlcyBiYWxpc2VzIG5vbiBmZXJtw6llc1xyXG4gICAgaHRtbCA9IGh0bWwucmVwbGFjZSgvPHN0cm9uZz4oLio/KSQvZywgXCI8c3Ryb25nPiQxPC9zdHJvbmc+XCIpO1xyXG5cclxuICAgIHJldHVybiBodG1sO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUVkaXRvckNoYW5nZSA9IChuZXdDb250ZW50KSA9PiB7XHJcbiAgICBkZWJvdW5jZSgpO1xyXG4gICAgc2V0Q29udGVudChuZXdDb250ZW50KTtcclxuICAgIHNldEZpZWxkVmFsdWUoXCJjb250ZW50RU5cIiwgbmV3Q29udGVudCk7XHJcbiAgfTtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKHRpdGxlKSB7XHJcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwidGl0bGVcIiwgSlNPTi5zdHJpbmdpZnkodGl0bGUpKTtcclxuICAgIH1cclxuICAgIGlmIChjb250ZW50KSB7XHJcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwiY29udGVudFwiLCBKU09OLnN0cmluZ2lmeShjb250ZW50KSk7XHJcbiAgICB9XHJcbiAgICBpZiAobWV0YXRpdGxlKSB7XHJcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwibWV0YXRpdGxlXCIsIEpTT04uc3RyaW5naWZ5KG1ldGF0aXRsZSkpO1xyXG4gICAgfVxyXG4gICAgaWYgKG1ldGFEZXNjcmlwdGlvbikge1xyXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcIm1ldGFEZXNjcmlwdGlvblwiLCBKU09OLnN0cmluZ2lmeShtZXRhRGVzY3JpcHRpb24pKTtcclxuICAgIH1cclxuICB9LCBbdGl0bGUsIGNvbnRlbnQsIG1ldGF0aXRsZSwgbWV0YURlc2NyaXB0aW9uXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZVBob3RvQ2hhbmdlID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc3Qgc2VsZWN0ZWRGaWxlID0gaW1hZ2VJbnB1dFJlZi5jdXJyZW50LmZpbGVzWzBdO1xyXG4gICAgc2V0U2VsZWN0ZWRJbWFnZShpbWFnZUlucHV0UmVmLmN1cnJlbnQuZmlsZXNbMF0pO1xyXG5cclxuICAgIGlmIChzZWxlY3RlZEZpbGUpIHtcclxuICAgICAgb25JbWFnZVNlbGVjdChzZWxlY3RlZEZpbGUsIGxhbmd1YWdlKTtcclxuICAgIH1cclxuICB9O1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBzZXRGaWVsZFZhbHVlKFwidGl0bGVFTlwiLCB0aXRsZSk7XHJcbiAgICBzZXRGaWVsZFZhbHVlKFwiZGVzY3JpcHRpb25FTlwiLCBkZXNjcmlwdGlvbik7XHJcbiAgICBzZXRGaWVsZFZhbHVlKFwidXJsRU5cIiwgdXJsKTtcclxuICAgIHNldEZpZWxkVmFsdWUoXHJcbiAgICAgIFwia2V5d29yZHNFTlwiLFxyXG4gICAgICB0YWdzLm1hcCgodCkgPT4gdC50ZXh0KVxyXG4gICAgKTtcclxuICAgIHNldEZpZWxkVmFsdWUoXHJcbiAgICAgIFwiaGlnaGxpZ2h0c0VOXCIsXHJcbiAgICAgIGhpZ2hsaWdodHMubWFwKChoKSA9PiBoLnRleHQpXHJcbiAgICApO1xyXG4gICAgc2V0RmllbGRWYWx1ZShcImNvbnRlbnRFTlwiLCBjb250ZW50KTtcclxuICAgIHNldEZpZWxkVmFsdWUoXCJtZXRhVGl0bGVFTlwiLCBtZXRhdGl0bGUpO1xyXG4gICAgc2V0RmllbGRWYWx1ZShcIm1ldGFEZXNjcmlwdGlvbkVOXCIsIG1ldGFEZXNjcmlwdGlvbik7XHJcbiAgfSwgW1xyXG4gICAgdGl0bGUsXHJcbiAgICBkZXNjcmlwdGlvbixcclxuICAgIHVybCxcclxuICAgIHRhZ3MsXHJcbiAgICBoaWdobGlnaHRzLFxyXG4gICAgY29udGVudCxcclxuICAgIG1ldGF0aXRsZSxcclxuICAgIG1ldGFEZXNjcmlwdGlvbixcclxuICBdKTtcclxuXHJcbiAgY29uc3QgdXNlU2F2ZUZpbGVIb29rID0gdXNlU2F2ZUZpbGUoKTtcclxuXHJcbiAgbGV0IHV1aWRQaG90bztcclxuICB1dWlkUGhvdG8gPSB1dWlkdjQoKS5yZXBsYWNlKC8tL2csIFwiXCIpO1xyXG5cclxuICBjb25zdCBoYW5kbGVQaG90b0Jsb2dDaGFuZ2UgPSBhc3luYyAoZmlsZSwgaW5mbywgY29yZSwgdXBsb2FkSGFuZGxlcikgPT4ge1xyXG4gICAgaWYgKGZpbGUgaW5zdGFuY2VvZiBIVE1MSW1hZ2VFbGVtZW50KSB7XHJcbiAgICAgIGNvbnN0IHNyYyA9IGZpbGUuc3JjO1xyXG5cclxuICAgICAgaWYgKHNyYy5zdGFydHNXaXRoKFwiZGF0YTppbWFnZVwiKSkge1xyXG4gICAgICAgIGNvbnN0IGJhc2U2NERhdGEgPSBzcmMuc3BsaXQoXCIsXCIpWzFdO1xyXG4gICAgICAgIGNvbnN0IGNvbnRlbnRUeXBlID0gc3JjLm1hdGNoKC9kYXRhOiguKj8pO2Jhc2U2NC8pWzFdO1xyXG4gICAgICAgIGNvbnN0IGJ5dGVDaGFyYWN0ZXJzID0gYXRvYihiYXNlNjREYXRhKTtcclxuICAgICAgICBjb25zdCBieXRlTnVtYmVycyA9IG5ldyBBcnJheShieXRlQ2hhcmFjdGVycy5sZW5ndGgpXHJcbiAgICAgICAgICAuZmlsbCgwKVxyXG4gICAgICAgICAgLm1hcCgoXywgaSkgPT4gYnl0ZUNoYXJhY3RlcnMuY2hhckNvZGVBdChpKSk7XHJcbiAgICAgICAgY29uc3QgYnl0ZUFycmF5ID0gbmV3IFVpbnQ4QXJyYXkoYnl0ZU51bWJlcnMpO1xyXG5cclxuICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2J5dGVBcnJheV0sIHsgdHlwZTogY29udGVudFR5cGUgfSk7XHJcbiAgICAgICAgY29uc3QgZmlsZU5hbWUgPSBgaW1hZ2VfJHtEYXRlLm5vdygpfS4ke2NvbnRlbnRUeXBlLnNwbGl0KFwiL1wiKVsxXX1gO1xyXG4gICAgICAgIGNvbnN0IHNlbGVjdGVkRmlsZSA9IG5ldyBGaWxlKFtibG9iXSwgZmlsZU5hbWUsIHsgdHlwZTogY29udGVudFR5cGUgfSk7XHJcblxyXG4gICAgICAgIGF3YWl0IHVwbG9hZEZpbGUoc2VsZWN0ZWRGaWxlLCB1cGxvYWRIYW5kbGVyLCBjb3JlLCBmaWxlKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBmZXRjaChzcmMpXHJcbiAgICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHJlc3BvbnNlLmJsb2IoKSlcclxuICAgICAgICAgIC50aGVuKChibG9iKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGNvbnRlbnRUeXBlID0gYmxvYi50eXBlO1xyXG4gICAgICAgICAgICBjb25zdCBmaWxlTmFtZSA9IGBpbWFnZV8ke0RhdGUubm93KCl9LiR7Y29udGVudFR5cGUuc3BsaXQoXCIvXCIpWzFdfWA7XHJcbiAgICAgICAgICAgIGNvbnN0IHNlbGVjdGVkRmlsZSA9IG5ldyBGaWxlKFtibG9iXSwgZmlsZU5hbWUsIHtcclxuICAgICAgICAgICAgICB0eXBlOiBjb250ZW50VHlwZSxcclxuICAgICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgICB1cGxvYWRGaWxlKHNlbGVjdGVkRmlsZSwgdXBsb2FkSGFuZGxlciwgY29yZSwgZmlsZSk7XHJcbiAgICAgICAgICB9KVxyXG4gICAgICAgICAgLmNhdGNoKChlcnJvcikgPT5cclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGNvbnZlcnRpbmcgaW1hZ2UgVVJMIHRvIEJsb2I6XCIsIGVycm9yKVxyXG4gICAgICAgICAgKTtcclxuICAgICAgfVxyXG4gICAgfSBlbHNlIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkZpbGUgaXMgbm90IGFuIEhUTUxJbWFnZUVsZW1lbnQuXCIpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IHVwbG9hZEZpbGUgPSAoc2VsZWN0ZWRGaWxlLCB1cGxvYWRIYW5kbGVyLCBjb3JlLCBvcmlnaW5hbEltYWdlKSA9PiB7XHJcbiAgICBsZXQgdXVpZFBob3RvO1xyXG4gICAgdXVpZFBob3RvID0gdXVpZHY0KCkucmVwbGFjZSgvLS9nLCBcIlwiKTtcclxuXHJcbiAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpO1xyXG4gICAgZm9ybURhdGEuYXBwZW5kKFwiZmlsZVwiLCBzZWxlY3RlZEZpbGUpO1xyXG5cclxuICAgIGNvbnN0IGV4dGVuc2lvbiA9IHNlbGVjdGVkRmlsZS5uYW1lLnNwbGl0KFwiLlwiKS5wb3AoKTtcclxuICAgIGNvbnN0IGN1cnJlbnRZZWFyID0gbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpO1xyXG5cclxuICAgIHVzZVNhdmVGaWxlSG9vay5tdXRhdGUoXHJcbiAgICAgIHtcclxuICAgICAgICByZXNvdXJjZTogXCJibG9nc1wiLFxyXG4gICAgICAgIGZvbGRlcjogY3VycmVudFllYXIudG9TdHJpbmcoKSxcclxuICAgICAgICBmaWxlbmFtZTogdXVpZFBob3RvLFxyXG4gICAgICAgIGJvZHk6IHsgZm9ybURhdGEsIHQgfSxcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIG9uU3VjY2VzczogKGRhdGFVVUlEKSA9PiB7XHJcbiAgICAgICAgICBjb25zdCB1dWlkUGhvdG9GaWxlTmFtZSA9XHJcbiAgICAgICAgICAgIGRhdGFVVUlELm1lc3NhZ2UgPT09IFwidXVpZCBleGlzdFwiXHJcbiAgICAgICAgICAgICAgPyBkYXRhVVVJRC51dWlkXHJcbiAgICAgICAgICAgICAgOiBgJHt1dWlkUGhvdG99LiR7ZXh0ZW5zaW9ufWA7XHJcblxyXG4gICAgICAgICAgY29uc3QgaW1hZ2VVcmwgPSBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19CQVNFX0FQSV9VUkx9L2ZpbGVzLyR7dXVpZFBob3RvRmlsZU5hbWV9YDtcclxuXHJcbiAgICAgICAgICBvcmlnaW5hbEltYWdlLnNyYyA9IGltYWdlVXJsO1xyXG5cclxuICAgICAgICAgIHVwbG9hZEhhbmRsZXIoe1xyXG4gICAgICAgICAgICByZXN1bHQ6IFtcclxuICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICBpZDogdXVpZFBob3RvRmlsZU5hbWUsXHJcbiAgICAgICAgICAgICAgICB1cmw6IGltYWdlVXJsLFxyXG4gICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIF0sXHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHVwbG9hZGluZyBmaWxlOlwiLCBlcnJvcik7XHJcbiAgICAgICAgfSxcclxuICAgICAgfVxyXG4gICAgKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlKG51bGwpO1xyXG5cclxuICBjb25zdCBoYW5kbGVGaWxlQ2hhbmdlID0gYXN5bmMgKGV2ZW50KSA9PiB7XHJcbiAgICBjb25zdCBmaWxlID0gZXZlbnQudGFyZ2V0LmZpbGVzWzBdO1xyXG4gICAgaWYgKFxyXG4gICAgICAhZmlsZSB8fFxyXG4gICAgICBmaWxlLnR5cGUgIT09XHJcbiAgICAgICAgXCJhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudFwiXHJcbiAgICApIHtcclxuICAgICAgc2V0RXJyb3IoXCJQbGVhc2UgdXBsb2FkIGEgdmFsaWQgLmRvY3ggZmlsZS5cIik7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpO1xyXG5cclxuICAgIHJlYWRlci5vbmxvYWQgPSBhc3luYyAoZXZlbnQpID0+IHtcclxuICAgICAgY29uc3QgYXJyYXlCdWZmZXIgPSBldmVudC50YXJnZXQucmVzdWx0O1xyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBtYW1tb3RoLmNvbnZlcnRUb0h0bWwoeyBhcnJheUJ1ZmZlciB9KTtcclxuICAgICAgICBjb25zb2xlLmxvZyhyZXN1bHQudmFsdWUpO1xyXG4gICAgICAgIHNldEZpZWxkVmFsdWUoXCJjb250ZW50RU5cIiwgcmVzdWx0LnZhbHVlKTtcclxuICAgICAgICBzZXRDb250ZW50KHJlc3VsdC52YWx1ZSk7XHJcbiAgICAgICAgc2V0RXJyb3IobnVsbCk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJNYW1tb3RoIGNvbnZlcnNpb24gZXJyb3I6XCIsIGVycik7XHJcbiAgICAgICAgc2V0RXJyb3IoXCJGYWlsZWQgdG8gY29udmVydCB0aGUgRE9DWCBmaWxlLlwiKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICByZWFkZXIucmVhZEFzQXJyYXlCdWZmZXIoZmlsZSk7XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxwIGNsYXNzTmFtZT1cImxhYmVsLXBlbnRhYmVsbFwiPkFkZCBhcnRpY2xlIEVuZ2xpc2ggOiA8L3A+XHJcbiAgICAgIDxpbnB1dCB0eXBlPVwiZmlsZVwiIGFjY2VwdD1cIi5kb2N4XCIgb25DaGFuZ2U9e2hhbmRsZUZpbGVDaGFuZ2V9IC8+XHJcbiAgICAgIHtlcnJvciAmJiA8ZGl2IHN0eWxlPXt7IGNvbG9yOiBcInJlZFwiIH19PntlcnJvcn08L2Rpdj59XHJcblxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1ncm91cFwiPlxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICB7XCIgXCJ9XHJcbiAgICAgICAgICA8Rm9ybUdyb3VwPlxyXG4gICAgICAgICAgICA8Q3VzdG9tVGV4dElucHV0XHJcbiAgICAgICAgICAgICAgbGFiZWw9e3QoXCJjcmVhdGVBcnRpY2xlOnRpdGxlXCIpfVxyXG4gICAgICAgICAgICAgIG5hbWU9XCJ0aXRsZUVOXCJcclxuICAgICAgICAgICAgICB2YWx1ZT17dGl0bGV9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB2ID0gZS50YXJnZXQudmFsdWU7XHJcbiAgICAgICAgICAgICAgICBzZXRUaXRsZSh2KTtcclxuICAgICAgICAgICAgICAgIHNldFVybChzbHVnKHYpKTtcclxuICAgICAgICAgICAgICAgIGRlYm91bmNlKCk7XHJcbiAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICBlcnJvcj17dG91Y2hlZC50aXRsZUVOICYmIGVycm9ycy50aXRsZUVOfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9Gb3JtR3JvdXA+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICA8Rm9ybUdyb3VwPlxyXG4gICAgICAgICAgICA8Rm9ybUxhYmVsIGNsYXNzTmFtZT1cImxhYmVsLWZvcm1cIj5cclxuICAgICAgICAgICAgICB7dChcImNyZWF0ZUFydGljbGU6Y2F0ZWdvcmllc1wiKX1cclxuICAgICAgICAgICAgICA8U3RhY2s+XHJcbiAgICAgICAgICAgICAgICA8QXV0b2NvbXBsZXRlXHJcbiAgICAgICAgICAgICAgICAgIG11bHRpcGxlXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LXBlbnRhYmVsbFwiXHJcbiAgICAgICAgICAgICAgICAgIGlkPVwidGFncy1zdGFuZGFyZFwiXHJcbiAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e1xyXG4gICAgICAgICAgICAgICAgICAgIGZpbHRlcmVkQ2F0ZWdvcmllcy5sZW5ndGggPiAwXHJcbiAgICAgICAgICAgICAgICAgICAgICA/IGZpbHRlcmVkQ2F0ZWdvcmllc1xyXG4gICAgICAgICAgICAgICAgICAgICAgOiBjYXRlZ29yaWVzXHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgZ2V0T3B0aW9uTGFiZWw9eyhvcHRpb24pID0+IG9wdGlvbi5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICBzZWxlY3RlZD17XHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWVzLmNhdGVnb3J5RU4ubGVuZ3RoID4gMFxyXG4gICAgICAgICAgICAgICAgICAgICAgPyAoZmlsdGVyZWRDYXRlZ29yaWVzLmxlbmd0aCA+IDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IGZpbHRlcmVkQ2F0ZWdvcmllc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogY2F0ZWdvcmllc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICApLmZpbHRlcigoY2F0ZWdvcnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWVzLmNhdGVnb3J5RU4uaW5jbHVkZXMoY2F0ZWdvcnkuaWQpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgIDogW11cclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGV2ZW50LCBzZWxlY3RlZE9wdGlvbnMpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBjYXRlZ29yeUlkcyA9IHNlbGVjdGVkT3B0aW9ucy5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAoY2F0ZWdvcnkpID0+IGNhdGVnb3J5LmlkXHJcbiAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRGaWVsZFZhbHVlKFwiY2F0ZWdvcnlFTlwiLCBjYXRlZ29yeUlkcyk7XHJcbiAgICAgICAgICAgICAgICAgICAgb25DYXRlZ29yaWVzU2VsZWN0KGNhdGVnb3J5SWRzKTtcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgcmVuZGVySW5wdXQ9eyhwYXJhbXMpID0+IChcclxuICAgICAgICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICB7Li4ucGFyYW1zfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5wdXQtcGVudGFiZWxsICBtdWx0aXBsZS1zZWxlY3RcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cInN0YW5kYXJkXCJcclxuICAgICAgICAgICAgICAgICAgICAgIC8vIHBsYWNlaG9sZGVyPVwibmF0aW9uYWxpdGllc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9TdGFjaz5cclxuICAgICAgICAgICAgPC9Gb3JtTGFiZWw+XHJcbiAgICAgICAgICA8L0Zvcm1Hcm91cD5cclxuXHJcbiAgICAgICAgICB7dG91Y2hlZC5jYXRlZ29yeSAmJiBlcnJvcnMuY2F0ZWdvcnkgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxhYmVsLWVycm9yXCI+e2Vycm9ycy5jYXRlZ29yeX08L2Rpdj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1ncm91cFwiPlxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICA8Rm9ybUdyb3VwPlxyXG4gICAgICAgICAgICA8Rm9ybUxhYmVsIGNsYXNzTmFtZT1cImxhYmVsLWZvcm1cIj5cclxuICAgICAgICAgICAgICBEZXNjcmlwdGlvblxyXG4gICAgICAgICAgICAgIDxUZXh0RmllbGRcclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzdGFuZGFyZFwiXHJcbiAgICAgICAgICAgICAgICBuYW1lPVwiZGVzY3JpcHRpb25FTlwiXHJcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICBtdWx0aWxpbmVcclxuICAgICAgICAgICAgICAgIHJvd3M9ezN9XHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17dmFsdWVzLmRlc2NyaXB0aW9uRU59XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgY29uc3QgZGVzY3JpcHRpb25FTiA9IGUudGFyZ2V0LnZhbHVlO1xyXG4gICAgICAgICAgICAgICAgICBzZXRGaWVsZFZhbHVlKFwiZGVzY3JpcHRpb25FTlwiLCBkZXNjcmlwdGlvbkVOKTtcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xyXG4gICAgICAgICAgICAgICAgICBcInRleHRBcmVhLXBlbnRhYmVsbFwiICtcclxuICAgICAgICAgICAgICAgICAgKGVycm9ycy5kZXNjcmlwdGlvbkVOICYmIHRvdWNoZWQuZGVzY3JpcHRpb25FTlxyXG4gICAgICAgICAgICAgICAgICAgID8gXCIgaXMtaW52YWxpZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgOiBcIlwiKVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIC8+e1wiIFwifVxyXG4gICAgICAgICAgICAgIDxFcnJvck1lc3NhZ2VcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxhYmVsLWVycm9yXCJcclxuICAgICAgICAgICAgICAgIG5hbWU9XCJkZXNjcmlwdGlvbkVOXCJcclxuICAgICAgICAgICAgICAgIGNvbXBvbmVudD1cImRpdlwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9Gb3JtTGFiZWw+XHJcbiAgICAgICAgICA8L0Zvcm1Hcm91cD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWdyb3VwXCI+XHJcbiAgICAgICAgPGRpdj5cclxuICAgICAgICAgIDxGb3JtR3JvdXA+XHJcbiAgICAgICAgICAgIDxGb3JtTGFiZWwgY2xhc3NOYW1lPVwibGFiZWwtZm9ybVwiPlxyXG4gICAgICAgICAgICAgIEhpZ2hsaWdodHNcclxuICAgICAgICAgICAgICA8ZGl2IGlkPVwidGFnc1wiPlxyXG4gICAgICAgICAgICAgICAgPFJlYWN0VGFnc1xyXG4gICAgICAgICAgICAgICAgICB0YWdzPXtoaWdobGlnaHRzfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xyXG4gICAgICAgICAgICAgICAgICAgIFwiaW5wdXQtcGVudGFiZWxsXCIgK1xyXG4gICAgICAgICAgICAgICAgICAgIChlcnJvcnMuaGlnaGxpZ2h0c0VOICYmIHRvdWNoZWQuaGlnaGxpZ2h0c0VOXHJcbiAgICAgICAgICAgICAgICAgICAgICA/IFwiIGlzLWludmFsaWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgOiBcIlwiKVxyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIGRlbGltaXRlcnM9e2RlbGltaXRlcnN9XHJcbiAgICAgICAgICAgICAgICAgIGhhbmRsZURlbGV0ZT17KGkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkVGFncyA9IGhpZ2hsaWdodHMuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgICAgICAgKHRhZywgaW5kZXgpID0+IGluZGV4ICE9PSBpXHJcbiAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRIaWdobGlnaHRzKHVwZGF0ZWRUYWdzKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRGaWVsZFZhbHVlKFxyXG4gICAgICAgICAgICAgICAgICAgICAgXCJoaWdobGlnaHRzRU5cIixcclxuICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZWRUYWdzLm1hcCgodGFnKSA9PiB0YWcudGV4dClcclxuICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICBoYW5kbGVBZGRpdGlvbj17KHRhZykgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldEhpZ2hsaWdodHMoWy4uLmhpZ2hsaWdodHMsIHRhZ10pO1xyXG4gICAgICAgICAgICAgICAgICAgIHNldEZpZWxkVmFsdWUoXHJcbiAgICAgICAgICAgICAgICAgICAgICBcImhpZ2hsaWdodHNFTlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgWy4uLmhpZ2hsaWdodHMsIHRhZ10ubWFwKChpdGVtKSA9PiBpdGVtLnRleHQpXHJcbiAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgaW5wdXRGaWVsZFBvc2l0aW9uPVwiYm90dG9tXCJcclxuICAgICAgICAgICAgICAgICAgYXV0b2NvbXBsZXRlXHJcbiAgICAgICAgICAgICAgICAgIGFsbG93RHJhZ0Ryb3A9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8RXJyb3JNZXNzYWdlXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJsYWJlbC1lcnJvclwiXHJcbiAgICAgICAgICAgICAgICBuYW1lPVwia2V5d29yZHNFTlwiXHJcbiAgICAgICAgICAgICAgICBjb21wb25lbnQ9XCJkaXZcIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvRm9ybUxhYmVsPlxyXG4gICAgICAgICAgPC9Gb3JtR3JvdXA+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8U3VuRWRpdG9yXHJcbiAgICAgICAgc2V0Q29udGVudHM9e2NvbnRlbnQgfHwgdmFsdWVzPy5jb250ZW50RU4gfHwgXCJcIn1cclxuICAgICAgICBvbkNoYW5nZT17KG5ld0NvbnRlbnQpID0+IHtcclxuICAgICAgICAgIHNldENvbnRlbnQobmV3Q29udGVudCk7XHJcbiAgICAgICAgICBzZXRGaWVsZFZhbHVlKFwiY29udGVudEVOXCIsIG5ld0NvbnRlbnQpO1xyXG4gICAgICAgICAgZGVib3VuY2UoKTtcclxuICAgICAgICB9fVxyXG4gICAgICAgIG9uUGFzdGU9e2hhbmRsZVBhc3RlfVxyXG4gICAgICAgIHNldE9wdGlvbnM9e3tcclxuICAgICAgICAgIGNsZWFuSFRNTDogZmFsc2UsXHJcbiAgICAgICAgICBkaXNhYmxlSHRtbFNhbml0aXplcjogdHJ1ZSxcclxuICAgICAgICAgIGFkZFRhZ3NXaGl0ZWxpc3Q6XHJcbiAgICAgICAgICAgIFwiaDF8aDJ8aDN8aDR8aDV8aDZ8cHxkaXZ8c3BhbnxzdHJvbmd8ZW18dWx8bGl8b2x8YXxpbWd8dGFibGV8dGhlYWR8dGJvZHl8dHJ8dGR8YnJ8aHJ8YnV0dG9uXCIsXHJcbiAgICAgICAgICBwbHVnaW5zOiBwbHVnaW5zLFxyXG4gICAgICAgICAgYnV0dG9uTGlzdDogW1xyXG4gICAgICAgICAgICBbXCJ1bmRvXCIsIFwicmVkb1wiXSxcclxuICAgICAgICAgICAgW1wiZm9udFwiLCBcImZvbnRTaXplXCIsIFwiZm9ybWF0QmxvY2tcIl0sXHJcbiAgICAgICAgICAgIFtcclxuICAgICAgICAgICAgICBcImJvbGRcIixcclxuICAgICAgICAgICAgICBcInVuZGVybGluZVwiLFxyXG4gICAgICAgICAgICAgIFwiaXRhbGljXCIsXHJcbiAgICAgICAgICAgICAgXCJzdHJpa2VcIixcclxuICAgICAgICAgICAgICBcInN1YnNjcmlwdFwiLFxyXG4gICAgICAgICAgICAgIFwic3VwZXJzY3JpcHRcIixcclxuICAgICAgICAgICAgXSxcclxuICAgICAgICAgICAgW1wiZm9udENvbG9yXCIsIFwiaGlsaXRlQ29sb3JcIl0sXHJcbiAgICAgICAgICAgIFtcImFsaWduXCIsIFwibGlzdFwiLCBcImxpbmVIZWlnaHRcIl0sXHJcbiAgICAgICAgICAgIFtcIm91dGRlbnRcIiwgXCJpbmRlbnRcIl0sXHJcbiAgICAgICAgICAgIFtcInRhYmxlXCIsIFwiaG9yaXpvbnRhbFJ1bGVcIiwgXCJsaW5rXCIsIFwiaW1hZ2VcIiwgXCJ2aWRlb1wiXSxcclxuICAgICAgICAgICAgW1wiZnVsbFNjcmVlblwiLCBcInNob3dCbG9ja3NcIiwgXCJjb2RlVmlld1wiXSxcclxuICAgICAgICAgICAgW1wicHJldmlld1wiLCBcInByaW50XCJdLFxyXG4gICAgICAgICAgICBbXCJyZW1vdmVGb3JtYXRcIl0sXHJcbiAgICAgICAgICBdLFxyXG4gICAgICAgICAgaW1hZ2VVcGxvYWRIYW5kbGVyOiBoYW5kbGVQaG90b0Jsb2dDaGFuZ2UsXHJcbiAgICAgICAgICBkZWZhdWx0VGFnOiBcImRpdlwiLFxyXG4gICAgICAgICAgbWluSGVpZ2h0OiBcIjMwMHB4XCIsXHJcbiAgICAgICAgICBtYXhIZWlnaHQ6IFwiNDAwcHhcIixcclxuICAgICAgICAgIHNob3dQYXRoTGFiZWw6IGZhbHNlLFxyXG4gICAgICAgICAgZm9udDogW1xyXG4gICAgICAgICAgICBcIlByb3hpbWEtTm92YS1SZWd1bGFyXCIsXHJcbiAgICAgICAgICAgIFwiUHJveGltYS1Ob3ZhLU1lZGl1bVwiLFxyXG4gICAgICAgICAgICBcIlByb3hpbWEtTm92YS1TZW1pYm9sZFwiLFxyXG4gICAgICAgICAgICBcIlByb3hpbWEtTm92YS1Cb2xkXCIsXHJcbiAgICAgICAgICAgIFwiUHJveGltYS1Ob3ZhLUV4dHJhYm9sZFwiLFxyXG4gICAgICAgICAgICBcIlByb3hpbWEtTm92YS1CbGFja1wiLFxyXG4gICAgICAgICAgICBcIlByb3hpbWEtTm92YS1MaWdodFwiLFxyXG4gICAgICAgICAgICBcIlByb3hpbWEtTm92YS1UaGluXCIsXHJcbiAgICAgICAgICAgIFwiQXJpYWxcIixcclxuICAgICAgICAgICAgXCJUaW1lcyBOZXcgUm9tYW5cIixcclxuICAgICAgICAgICAgXCJTYW5zLVNlcmlmXCIsXHJcbiAgICAgICAgICBdLFxyXG4gICAgICAgICAgY2hhckNvdW50ZXI6IHRydWUsIC8vIFNob3cgY2hhcmFjdGVyIGNvdW50ZXJcclxuICAgICAgICAgIGNoYXJDb3VudGVyVHlwZTogXCJieXRlXCIsXHJcbiAgICAgICAgICByZXNpemluZ0JhcjogZmFsc2UsIC8vIEhpZGUgcmVzaXppbmcgYmFyIGZvciBhIGNsZWFuZXIgVUlcclxuICAgICAgICAgIGNvbG9yTGlzdDogW1xyXG4gICAgICAgICAgICAvLyBTdGFuZGFyZCBDb2xvcnNcclxuICAgICAgICAgICAgW1xyXG4gICAgICAgICAgICAgIFwiIzIzNDc5MVwiLFxyXG4gICAgICAgICAgICAgIFwiI2Q2OWIxOVwiLFxyXG4gICAgICAgICAgICAgIFwiI2NjMzIzM1wiLFxyXG4gICAgICAgICAgICAgIFwiIzAwOTk2NlwiLFxyXG4gICAgICAgICAgICAgIFwiIzBiMzA1MVwiLFxyXG4gICAgICAgICAgICAgIFwiIzJCQkZBRFwiLFxyXG4gICAgICAgICAgICAgIFwiIzBiMzA1MTAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjMGEzMDUyMTRcIixcclxuICAgICAgICAgICAgICBcIiM3NDM3OTRcIixcclxuICAgICAgICAgICAgICBcIiNmZjAwMDBcIixcclxuICAgICAgICAgICAgICBcIiNmZjVlMDBcIixcclxuICAgICAgICAgICAgICBcIiNmZmU0MDBcIixcclxuICAgICAgICAgICAgICBcIiNhYmYyMDBcIixcclxuICAgICAgICAgICAgICBcIiMwMGQ4ZmZcIixcclxuICAgICAgICAgICAgICBcIiMwMDU1ZmZcIixcclxuICAgICAgICAgICAgICBcIiM2NjAwZmZcIixcclxuICAgICAgICAgICAgICBcIiNmZjAwZGRcIixcclxuICAgICAgICAgICAgICBcIiMwMDAwMDBcIixcclxuICAgICAgICAgICAgICBcIiNmZmQ4ZDhcIixcclxuICAgICAgICAgICAgICBcIiNmYWUwZDRcIixcclxuICAgICAgICAgICAgICBcIiNmYWY0YzBcIixcclxuICAgICAgICAgICAgICBcIiNlNGY3YmFcIixcclxuICAgICAgICAgICAgICBcIiNkNGY0ZmFcIixcclxuICAgICAgICAgICAgICBcIiNkOWU1ZmZcIixcclxuICAgICAgICAgICAgICBcIiNlOGQ5ZmZcIixcclxuICAgICAgICAgICAgICBcIiNmZmQ5ZmFcIixcclxuICAgICAgICAgICAgICBcIiNmMWYxZjFcIixcclxuICAgICAgICAgICAgICBcIiNmZmE3YTdcIixcclxuICAgICAgICAgICAgICBcIiNmZmMxOWVcIixcclxuICAgICAgICAgICAgICBcIiNmYWVkN2RcIixcclxuICAgICAgICAgICAgICBcIiNjZWYyNzlcIixcclxuICAgICAgICAgICAgICBcIiNiMmViZjRcIixcclxuICAgICAgICAgICAgICBcIiNiMmNjZmZcIixcclxuICAgICAgICAgICAgICBcIiNkMWIyZmZcIixcclxuICAgICAgICAgICAgICBcIiNmZmIyZjVcIixcclxuICAgICAgICAgICAgICBcIiNiZGJkYmRcIixcclxuICAgICAgICAgICAgICBcIiNmMTVmNWZcIixcclxuICAgICAgICAgICAgICBcIiNmMjk2NjFcIixcclxuICAgICAgICAgICAgICBcIiNlNWQ4NWNcIixcclxuICAgICAgICAgICAgICBcIiNiY2U1NWNcIixcclxuICAgICAgICAgICAgICBcIiM1Y2QxZTVcIixcclxuICAgICAgICAgICAgICBcIiM2Njk5ZmZcIixcclxuICAgICAgICAgICAgICBcIiNhMzY2ZmZcIixcclxuICAgICAgICAgICAgICBcIiNmMjYxZGZcIixcclxuICAgICAgICAgICAgICBcIiM4YzhjOGNcIixcclxuICAgICAgICAgICAgICBcIiM5ODAwMDBcIixcclxuICAgICAgICAgICAgICBcIiM5OTM4MDBcIixcclxuICAgICAgICAgICAgICBcIiM5OThhMDBcIixcclxuICAgICAgICAgICAgICBcIiM2Yjk5MDBcIixcclxuICAgICAgICAgICAgICBcIiMwMDgyOTlcIixcclxuICAgICAgICAgICAgICBcIiMwMDMzOTlcIixcclxuICAgICAgICAgICAgICBcIiMzZDAwOTlcIixcclxuICAgICAgICAgICAgICBcIiM5OTAwODVcIixcclxuICAgICAgICAgICAgICBcIiMzNTM1MzVcIixcclxuICAgICAgICAgICAgICBcIiM2NzAwMDBcIixcclxuICAgICAgICAgICAgICBcIiM2NjI1MDBcIixcclxuICAgICAgICAgICAgICBcIiM2NjVjMDBcIixcclxuICAgICAgICAgICAgICBcIiM0NzY2MDBcIixcclxuICAgICAgICAgICAgICBcIiMwMDU3NjZcIixcclxuICAgICAgICAgICAgICBcIiMwMDIyNjZcIixcclxuICAgICAgICAgICAgICBcIiMyOTAwNjZcIixcclxuICAgICAgICAgICAgICBcIiM2NjAwNThcIixcclxuICAgICAgICAgICAgICBcIiMyMjIyMjJcIixcclxuICAgICAgICAgICAgXSwgLy8gRm9yIGJveCBzaGFkb3cgd2l0aCBvcGFjaXR5XHJcbiAgICAgICAgICBdLFxyXG4gICAgICAgIH19XHJcbiAgICAgICAgb25JbWFnZVVwbG9hZD17aGFuZGxlUGhvdG9CbG9nQ2hhbmdlfVxyXG4gICAgICAvPlxyXG4gICAgICA8YnI+PC9icj5cclxuXHJcbiAgICAgIDxGYXFTZWN0aW9uXHJcbiAgICAgICAgdmFsdWVzPXt2YWx1ZXN9XHJcbiAgICAgICAgc2V0RmllbGRWYWx1ZT17c2V0RmllbGRWYWx1ZX1cclxuICAgICAgICBlcnJvcnM9e2Vycm9yc31cclxuICAgICAgICB0b3VjaGVkPXt0b3VjaGVkfVxyXG4gICAgICAgIGxhbmd1YWdlPVwiRU5cIlxyXG4gICAgICAgIGRlYm91bmNlPXtkZWJvdW5jZX1cclxuICAgICAgLz5cclxuICAgICAgPGJyPjwvYnI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWdyb3VwXCI+XHJcbiAgICAgICAgPGRpdj5cclxuICAgICAgICAgIHtcIiBcIn1cclxuICAgICAgICAgIDxGb3JtR3JvdXA+XHJcbiAgICAgICAgICAgIDxGb3JtTGFiZWwgY2xhc3NOYW1lPVwibGFiZWwtZm9ybVwiPlxyXG4gICAgICAgICAgICAgIHt0KFwiY3JlYXRlQXJ0aWNsZTptZXRhVGl0bGVcIil9ICh7XCIgXCJ9XHJcbiAgICAgICAgICAgICAgPHNwYW5cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17XHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlcy5tZXRhVGl0bGVFTj8ubGVuZ3RoID4gNjUgPyBcIiB0ZXh0LWRhbmdlclwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtcIiBcIn1cclxuICAgICAgICAgICAgICAgIHt2YWx1ZXMubWV0YVRpdGxlRU4/Lmxlbmd0aH0gLyA2NXtcIiBcIn1cclxuICAgICAgICAgICAgICA8L3NwYW4+e1wiIFwifVxyXG4gICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwic3RhbmRhcmRcIlxyXG4gICAgICAgICAgICAgICAgbmFtZT1cIm1ldGFUaXRsZUVOXCJcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXttZXRhdGl0bGUgfHwgdmFsdWVzLm1ldGFUaXRsZUVOfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnN0IG1ldGFUaXRsZUVOID0gZS50YXJnZXQudmFsdWU7XHJcbiAgICAgICAgICAgICAgICAgIHNldEZpZWxkVmFsdWUoXCJtZXRhVGl0bGVFTlwiLCBtZXRhVGl0bGVFTik7XHJcbiAgICAgICAgICAgICAgICAgIHNldE1ldGF0aXRsZShtZXRhVGl0bGVFTik7XHJcbiAgICAgICAgICAgICAgICAgIGRlYm91bmNlKCk7XHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtcclxuICAgICAgICAgICAgICAgICAgXCJpbnB1dC1wZW50YWJlbGxcIiArXHJcbiAgICAgICAgICAgICAgICAgIChlcnJvcnMubWV0YVRpdGxlRU4gJiYgdG91Y2hlZC5tZXRhVGl0bGVFTlxyXG4gICAgICAgICAgICAgICAgICAgID8gXCIgaXMtaW52YWxpZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgOiBcIlwiKVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIC8+e1wiIFwifVxyXG4gICAgICAgICAgICAgIDxFcnJvck1lc3NhZ2VcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxhYmVsLWVycm9yXCJcclxuICAgICAgICAgICAgICAgIG5hbWU9XCJtZXRhVGl0bGVFTlwiXHJcbiAgICAgICAgICAgICAgICBjb21wb25lbnQ9XCJkaXZcIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvRm9ybUxhYmVsPlxyXG4gICAgICAgICAgPC9Gb3JtR3JvdXA+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPGRpdj5cclxuICAgICAgICAgIHtcIiBcIn1cclxuICAgICAgICAgIDxGb3JtR3JvdXA+XHJcbiAgICAgICAgICAgIDxDdXN0b21UZXh0SW5wdXRcclxuICAgICAgICAgICAgICBsYWJlbD17dChcImNyZWF0ZUFydGljbGU6dXJsXCIpfVxyXG4gICAgICAgICAgICAgIG5hbWU9XCJ1cmxFTlwiXHJcbiAgICAgICAgICAgICAgdmFsdWU9e3VybH1cclxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFVybChlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgZXJyb3I9e3RvdWNoZWQudXJsRU4gJiYgZXJyb3JzLnVybEVOfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9Gb3JtR3JvdXA+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1ncm91cFwiPlxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICA8Rm9ybUdyb3VwPlxyXG4gICAgICAgICAgICA8Rm9ybUxhYmVsIGNsYXNzTmFtZT1cImxhYmVsLWZvcm1cIj5cclxuICAgICAgICAgICAgICB7dChcImNyZWF0ZUFydGljbGU6bWV0YURlc2NyaXB0aW9uXCIpfSAoe1wiIFwifVxyXG4gICAgICAgICAgICAgIDxzcGFuXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xyXG4gICAgICAgICAgICAgICAgICB2YWx1ZXMubWV0YURlc2NyaXB0aW9uRU4/Lmxlbmd0aCA+IDE2MCA/IFwiIHRleHQtZGFuZ2VyXCIgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAge3ZhbHVlcy5tZXRhRGVzY3JpcHRpb25FTj8ubGVuZ3RofSAvIDE2MFxyXG4gICAgICAgICAgICAgIDwvc3Bhbj57XCIgXCJ9XHJcbiAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgIDxUZXh0RmllbGRcclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzdGFuZGFyZFwiXHJcbiAgICAgICAgICAgICAgICBuYW1lPVwibWV0YURlc2NyaXB0aW9uRU5cIlxyXG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgbXVsdGlsaW5lXHJcbiAgICAgICAgICAgICAgICByb3dzPXszfVxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e21ldGFEZXNjcmlwdGlvbiB8fCB2YWx1ZXMubWV0YURlc2NyaXB0aW9uRU59XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgY29uc3QgbWV0YURlc2NyaXB0aW9uRU4gPSBlLnRhcmdldC52YWx1ZTtcclxuICAgICAgICAgICAgICAgICAgc2V0RmllbGRWYWx1ZShcIm1ldGFEZXNjcmlwdGlvbkVOXCIsIG1ldGFEZXNjcmlwdGlvbkVOKTtcclxuICAgICAgICAgICAgICAgICAgc2V0TWV0YURlc2NyaXB0aW9uKG1ldGFEZXNjcmlwdGlvbkVOKTtcclxuICAgICAgICAgICAgICAgICAgZGVib3VuY2UoKTtcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xyXG4gICAgICAgICAgICAgICAgICBcInRleHRBcmVhLXBlbnRhYmVsbFwiICtcclxuICAgICAgICAgICAgICAgICAgKGVycm9ycy5tZXRhRGVzY3JpcHRpb25FTiAmJiB0b3VjaGVkLm1ldGFEZXNjcmlwdGlvbkVOXHJcbiAgICAgICAgICAgICAgICAgICAgPyBcIiBpcy1pbnZhbGlkXCJcclxuICAgICAgICAgICAgICAgICAgICA6IFwiXCIpXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgLz57XCIgXCJ9XHJcbiAgICAgICAgICAgICAgPEVycm9yTWVzc2FnZVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibGFiZWwtZXJyb3JcIlxyXG4gICAgICAgICAgICAgICAgbmFtZT1cIm1ldGFEZXNjcmlwdGlvbkVOXCJcclxuICAgICAgICAgICAgICAgIGNvbXBvbmVudD1cImRpdlwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9Gb3JtTGFiZWw+XHJcbiAgICAgICAgICA8L0Zvcm1Hcm91cD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1ncm91cFwiPlxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICA8Rm9ybUdyb3VwPlxyXG4gICAgICAgICAgICA8Rm9ybUxhYmVsIGNsYXNzTmFtZT1cImxhYmVsLWZvcm1cIj5cclxuICAgICAgICAgICAgICB7dChcImNyZWF0ZUFydGljbGU6ZmVhdHVyZWRJbWFnZVwiKX1cclxuXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ1cGxvYWQtY29udGFpbmVyXCI+XHJcbiAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj17YGltYWdlLXVwbG9hZC1lbmB9IGNsYXNzTmFtZT1cImZpbGUtbGFiZWxzXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJmaWxlXCJcclxuICAgICAgICAgICAgICAgICAgICBpZD17YGltYWdlLXVwbG9hZC1lbmB9XHJcbiAgICAgICAgICAgICAgICAgICAgbmFtZT1cImltYWdlRU5cIlxyXG4gICAgICAgICAgICAgICAgICAgIGFjY2VwdD1cIi5wbmcsIC5qcGcsIC5qcGVnLCAud2VicFwiXHJcbiAgICAgICAgICAgICAgICAgICAgcmVmPXtpbWFnZUlucHV0UmVmfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0RmllbGRWYWx1ZShcImltYWdlRU5cIiwgZS50YXJnZXQuZmlsZXNbMF0pO1xyXG4gICAgICAgICAgICAgICAgICAgICAgaGFuZGxlUGhvdG9DaGFuZ2UoKTtcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICBcImZpbGUtaW5wdXRcIiArXHJcbiAgICAgICAgICAgICAgICAgICAgICAoZXJyb3JzLmltYWdlRU4gJiYgdG91Y2hlZC5pbWFnZUVOID8gXCIgaXMtaW52YWxpZFwiIDogXCJcIilcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidXBsb2FkLWFyZWFcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpY29uLXBpY1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZEltYWdlOiBgdXJsKFwiJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkSW1hZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBVUkwuY3JlYXRlT2JqZWN0VVJMKHNlbGVjdGVkSW1hZ2UpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogdmFsdWVzLmltYWdlRU5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBgJHtwcm9jZXNzLmVudi5SRUFDVF9BUFBfQVBJX1VSTH0ke0FQSV9VUkxTLmZpbGVzfS8ke3ZhbHVlcy5pbWFnZUVOfWBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB1cGxvYWQuc3JjXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVwiKWAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZFNpemU6IFwiY292ZXJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kUmVwZWF0OiBcIm5vLXJlcGVhdFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRQb3NpdGlvbjogXCJjZW50ZXJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAvL2JhY2tncm91bmRDb2xvcjogXCIjNDBiZDM5MjFcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgID48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidXBsb2FkLXRleHRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge3QoXCJjcmVhdGVBcnRpY2xlOmFkZEZlYXRJbWdcIil9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ1cGxvYWQtZGVzY3JpcHRpb25cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge3QoXCJjcmVhdGVBcnRpY2xlOmNsaWNrQm94XCIpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPEVycm9yTWVzc2FnZVxyXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJpbWFnZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgY29tcG9uZW50PVwiZGl2XCJcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnZhbGlkLWZlZWRiYWNrIGVycm9yXCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvRm9ybUxhYmVsPlxyXG4gICAgICAgICAgPC9Gb3JtR3JvdXA+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZ3JvdXBcIj5cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPEZvcm1Hcm91cD5cclxuICAgICAgICAgICAgPEZvcm1MYWJlbCBjbGFzc05hbWU9XCJsYWJlbC1mb3JtXCI+XHJcbiAgICAgICAgICAgICAge3QoXCJjcmVhdGVBcnRpY2xlOmFsdFwiKX1cclxuICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwic3RhbmRhcmRcIlxyXG4gICAgICAgICAgICAgICAgbmFtZT1cImFsdEVOXCJcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXt2YWx1ZXMuYWx0RU59XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgc2V0RmllbGRWYWx1ZShcImFsdEVOXCIsIGUudGFyZ2V0LnZhbHVlKTtcclxuICAgICAgICAgICAgICAgICAgZGVib3VuY2UoKTtcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xyXG4gICAgICAgICAgICAgICAgICBcImlucHV0LXBlbnRhYmVsbFwiICtcclxuICAgICAgICAgICAgICAgICAgKGVycm9ycy5hbHRFTiAmJiB0b3VjaGVkLmFsdEVOID8gXCIgaXMtaW52YWxpZFwiIDogXCJcIilcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAvPntcIiBcIn1cclxuICAgICAgICAgICAgICA8RXJyb3JNZXNzYWdlXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJsYWJlbC1lcnJvclwiXHJcbiAgICAgICAgICAgICAgICBuYW1lPVwiYWx0RU5cIlxyXG4gICAgICAgICAgICAgICAgY29tcG9uZW50PVwiZGl2XCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0Zvcm1MYWJlbD5cclxuICAgICAgICAgIDwvRm9ybUdyb3VwPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICB7XCIgXCJ9XHJcbiAgICAgICAgICA8Rm9ybUdyb3VwPlxyXG4gICAgICAgICAgICA8Rm9ybUxhYmVsIGNsYXNzTmFtZT1cImxhYmVsLWZvcm1cIj5cclxuICAgICAgICAgICAgICB7dChcImNyZWF0ZUFydGljbGU6dmlzaWJpbGl0eVwiKX1cclxuXHJcbiAgICAgICAgICAgICAgPFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic2VsZWN0LXBlbnRhYmVsbFwiXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwic3RhbmRhcmRcIlxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e1Zpc2liaWxpdHkuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgICAob3B0aW9uKSA9PiB2YWx1ZXMudmlzaWJpbGl0eUVOID09PSBvcHRpb25cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICBzZWxlY3RlZD17dmFsdWVzLnZpc2liaWxpdHlFTn1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZXZlbnQpID0+IHtcclxuICAgICAgICAgICAgICAgICAgc2V0RmllbGRWYWx1ZShcInZpc2liaWxpdHlFTlwiLCBldmVudC50YXJnZXQudmFsdWUpO1xyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7VmlzaWJpbGl0eS5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgIDxNZW51SXRlbSBrZXk9e2luZGV4fSB2YWx1ZT17aXRlbX0+XHJcbiAgICAgICAgICAgICAgICAgICAge2l0ZW19XHJcbiAgICAgICAgICAgICAgICAgIDwvTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICA8L1NlbGVjdD5cclxuXHJcbiAgICAgICAgICAgICAgPEVycm9yTWVzc2FnZVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibGFiZWwtZXJyb3JcIlxyXG4gICAgICAgICAgICAgICAgbmFtZT1cInZpc2liaWxpdHlFTlwiXHJcbiAgICAgICAgICAgICAgICBjb21wb25lbnQ9XCJkaXZcIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvRm9ybUxhYmVsPlxyXG4gICAgICAgICAgPC9Gb3JtR3JvdXA+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICB7XCIgXCJ9XHJcbiAgICAgICAgICA8Rm9ybUdyb3VwPlxyXG4gICAgICAgICAgICA8Rm9ybUxhYmVsIGNsYXNzTmFtZT1cImxhYmVsLWZvcm1cIj5cclxuICAgICAgICAgICAgICB7dChcImNyZWF0ZUFydGljbGU6a2V5d29yZFwiKX1cclxuXHJcbiAgICAgICAgICAgICAgPGRpdiBpZD1cInRhZ3NcIj5cclxuICAgICAgICAgICAgICAgIDxSZWFjdFRhZ3NcclxuICAgICAgICAgICAgICAgICAgdGFncz17dGFnc31cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtcclxuICAgICAgICAgICAgICAgICAgICBcImlucHV0LXBlbnRhYmVsbFwiICtcclxuICAgICAgICAgICAgICAgICAgICAoZXJyb3JzLmtleXdvcmRzRU4gJiYgdG91Y2hlZC5rZXl3b3Jkc0VOXHJcbiAgICAgICAgICAgICAgICAgICAgICA/IFwiIGlzLWludmFsaWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgOiBcIlwiKVxyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIGRlbGltaXRlcnM9e2RlbGltaXRlcnN9XHJcbiAgICAgICAgICAgICAgICAgIGhhbmRsZURlbGV0ZT17KGkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkVGFncyA9IHRhZ3MuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgICAgICAgKHRhZywgaW5kZXgpID0+IGluZGV4ICE9PSBpXHJcbiAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRUYWdzKHVwZGF0ZWRUYWdzKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRGaWVsZFZhbHVlKFxyXG4gICAgICAgICAgICAgICAgICAgICAgXCJrZXl3b3Jkc0VOXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVkVGFncy5tYXAoKHRhZykgPT4gdGFnLnRleHQpXHJcbiAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgaGFuZGxlQWRkaXRpb249eyh0YWcpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBzZXRUYWdzKFsuLi50YWdzLCB0YWddKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRGaWVsZFZhbHVlKFxyXG4gICAgICAgICAgICAgICAgICAgICAgXCJrZXl3b3Jkc0VOXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICBbLi4udGFncywgdGFnXS5tYXAoKGl0ZW0pID0+IGl0ZW0udGV4dClcclxuICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgIGRlYm91bmNlKCk7XHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgIGlucHV0RmllbGRQb3NpdGlvbj1cImJvdHRvbVwiXHJcbiAgICAgICAgICAgICAgICAgIGF1dG9jb21wbGV0ZVxyXG4gICAgICAgICAgICAgICAgICBhbGxvd0RyYWdEcm9wPXtmYWxzZX1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPEVycm9yTWVzc2FnZVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibGFiZWwtZXJyb3JcIlxyXG4gICAgICAgICAgICAgICAgbmFtZT1cImtleXdvcmRzRU5cIlxyXG4gICAgICAgICAgICAgICAgY29tcG9uZW50PVwiZGl2XCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0Zvcm1MYWJlbD5cclxuICAgICAgICAgIDwvRm9ybUdyb3VwPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZ3JvdXBcIj5cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImxhYmVsLWZvcm1cIj5cclxuICAgICAgICAgICAgPEZpZWxkXHJcbiAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcclxuICAgICAgICAgICAgICBuYW1lPVwicHVibGlzaE5vd1wiXHJcbiAgICAgICAgICAgICAgY2hlY2tlZD17cHVibGlzaE5vd31cclxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgIHNldFB1Ymxpc2hOb3coZS50YXJnZXQuY2hlY2tlZCk7XHJcbiAgICAgICAgICAgICAgICBpZiAoZS50YXJnZXQuY2hlY2tlZCkge1xyXG4gICAgICAgICAgICAgICAgICBzZXRGaWVsZFZhbHVlKFwicHVibGlzaERhdGVFTlwiLCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIHt0KFwiY3JlYXRlQXJ0aWNsZTpwdWJsaXNoTm93XCIpfVxyXG4gICAgICAgICAgPC9sYWJlbD5cclxuXHJcbiAgICAgICAgICB7IXB1Ymxpc2hOb3cgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgIDxGb3JtR3JvdXA+XHJcbiAgICAgICAgICAgICAgICA8Rm9ybUxhYmVsIGNsYXNzTmFtZT1cImxhYmVsLWZvcm1cIj5cclxuICAgICAgICAgICAgICAgICAge3QoXCJjcmVhdGVBcnRpY2xlOnB1Ymxpc2hEYXRlXCIpfVxyXG4gICAgICAgICAgICAgICAgICA8TG9jYWxpemF0aW9uUHJvdmlkZXIgZGF0ZUFkYXB0ZXI9e0FkYXB0ZXJEYXlqc30+XHJcbiAgICAgICAgICAgICAgICAgICAgPERlbW9Db250YWluZXIgY29tcG9uZW50cz17W1wiRGF0ZVBpY2tlclwiXX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8RGF0ZVBpY2tlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwic3RhbmRhcmRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1kYXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgZm9ybWF0PVwiREQvTU0vWVlZWVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtkYXlqcyh2YWx1ZXMucHVibGlzaERhdGVFTiB8fCBuZXcgRGF0ZSgpKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhkYXRlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0RmllbGRWYWx1ZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwicHVibGlzaERhdGVFTlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF5anMoZGF0ZSkuZm9ybWF0KFwiWVlZWS1NTS1ERFwiKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAvPntcIiBcIn1cclxuICAgICAgICAgICAgICAgICAgICA8L0RlbW9Db250YWluZXI+XHJcbiAgICAgICAgICAgICAgICAgIDwvTG9jYWxpemF0aW9uUHJvdmlkZXI+XHJcbiAgICAgICAgICAgICAgICA8L0Zvcm1MYWJlbD57XCIgXCJ9XHJcbiAgICAgICAgICAgICAgICA8RXJyb3JNZXNzYWdlXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxhYmVsLWVycm9yXCJcclxuICAgICAgICAgICAgICAgICAgbmFtZT1cInB1Ymxpc2hEYXRlRU5cIlxyXG4gICAgICAgICAgICAgICAgICBjb21wb25lbnQ9XCJkaXZcIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L0Zvcm1Hcm91cD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIDxGaWVsZFxyXG4gICAgICAgIHR5cGU9XCJoaWRkZW5cIlxyXG4gICAgICAgIG5hbWU9XCJwdWJsaXNoRGF0ZUVOXCJcclxuICAgICAgICB2YWx1ZT17XHJcbiAgICAgICAgICBwdWJsaXNoTm93ID8gbmV3IERhdGUoKS50b0lTT1N0cmluZygpIDogcHVibGlzaERhdGUudG9JU09TdHJpbmcoKVxyXG4gICAgICAgIH1cclxuICAgICAgLz5cclxuICAgIDwvPlxyXG4gICk7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IEFkZEFydGljbGVFTjtcclxuIl0sIm5hbWVzIjpbIkVycm9yTWVzc2FnZSIsIkZpZWxkIiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlU3RhdGUiLCJ1c2VUcmFuc2xhdGlvbiIsIlN1bkVkaXRvciIsIm1hbW1vdGgiLCJWaXNpYmlsaXR5IiwiQVBJX1VSTFMiLCJ1cGxvYWQiLCJzbHVnIiwicGx1Z2lucyIsIkZhcVNlY3Rpb24iLCJBdXRvY29tcGxldGUiLCJGb3JtR3JvdXAiLCJGb3JtTGFiZWwiLCJNZW51SXRlbSIsIlNlbGVjdCIsIlN0YWNrIiwiVGV4dEZpZWxkIiwiRGF0ZVBpY2tlciIsIkxvY2FsaXphdGlvblByb3ZpZGVyIiwiQWRhcHRlckRheWpzIiwiRGVtb0NvbnRhaW5lciIsImRheWpzIiwiV2l0aENvbnRleHQiLCJSZWFjdFRhZ3MiLCJ1c2VTYXZlRmlsZSIsInY0IiwidXVpZHY0IiwiQ3VzdG9tVGV4dElucHV0IiwiQWRkQXJ0aWNsZUVOIiwiZXJyb3JzIiwidG91Y2hlZCIsInNldEZpZWxkVmFsdWUiLCJ2YWx1ZXMiLCJvbkltYWdlU2VsZWN0IiwiZmlsdGVyZWRDYXRlZ29yaWVzIiwiY2F0ZWdvcmllcyIsIm9uQ2F0ZWdvcmllc1NlbGVjdCIsImRlYm91bmNlIiwiS2V5Q29kZXMiLCJjb21tYSIsImVudGVyIiwiZGVsaW1pdGVycyIsInRhZ3MiLCJzZXRUYWdzIiwiaGlnaGxpZ2h0cyIsInNldEhpZ2hsaWdodHMiLCJ0IiwiZGVzY3JpcHRpb24iLCJzZXREZXNjcmlwdGlvbiIsImRlc2NyaXB0aW9uRU4iLCJ1cmwiLCJzZXRVcmwiLCJ1cmxFTiIsInB1Ymxpc2hOb3ciLCJzZXRQdWJsaXNoTm93IiwicHVibGlzaERhdGUiLCJzZXRQdWJsaXNoRGF0ZSIsIkRhdGUiLCJpbWFnZUlucHV0UmVmIiwic2VsZWN0ZWRJbWFnZSIsInNldFNlbGVjdGVkSW1hZ2UiLCJsYW5ndWFnZSIsInRpdGxlIiwic2V0VGl0bGUiLCJzYXZlZFRpdGxlIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkpTT04iLCJwYXJzZSIsIm1ldGF0aXRsZSIsInNldE1ldGF0aXRsZSIsInNhdmVkTWV0YXRpdGxlIiwibWV0YURlc2NyaXB0aW9uIiwic2V0TWV0YURlc2NyaXB0aW9uIiwic2F2ZWRNZXRhZGVzY3JpcHRpb24iLCJjb250ZW50Iiwic2V0Q29udGVudCIsInNhdmVkQ29udGVudCIsImhhbmRsZVBhc3RlIiwiZXZlbnQiLCJjbGVhbkRhdGEiLCJtYXhDaGFyQ291bnQiLCJodG1sIiwicmVwbGFjZSIsImhhbmRsZUVkaXRvckNoYW5nZSIsIm5ld0NvbnRlbnQiLCJzZXRJdGVtIiwic3RyaW5naWZ5IiwiaGFuZGxlUGhvdG9DaGFuZ2UiLCJzZWxlY3RlZEZpbGUiLCJjdXJyZW50IiwiZmlsZXMiLCJtYXAiLCJ0ZXh0IiwiaCIsInVzZVNhdmVGaWxlSG9vayIsInV1aWRQaG90byIsImhhbmRsZVBob3RvQmxvZ0NoYW5nZSIsImZpbGUiLCJpbmZvIiwiY29yZSIsInVwbG9hZEhhbmRsZXIiLCJIVE1MSW1hZ2VFbGVtZW50Iiwic3JjIiwic3RhcnRzV2l0aCIsImJhc2U2NERhdGEiLCJzcGxpdCIsImNvbnRlbnRUeXBlIiwibWF0Y2giLCJieXRlQ2hhcmFjdGVycyIsImF0b2IiLCJieXRlTnVtYmVycyIsIkFycmF5IiwibGVuZ3RoIiwiZmlsbCIsIl8iLCJpIiwiY2hhckNvZGVBdCIsImJ5dGVBcnJheSIsIlVpbnQ4QXJyYXkiLCJibG9iIiwiQmxvYiIsInR5cGUiLCJmaWxlTmFtZSIsIm5vdyIsIkZpbGUiLCJ1cGxvYWRGaWxlIiwiZmV0Y2giLCJ0aGVuIiwicmVzcG9uc2UiLCJjYXRjaCIsImVycm9yIiwiY29uc29sZSIsIm9yaWdpbmFsSW1hZ2UiLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiYXBwZW5kIiwiZXh0ZW5zaW9uIiwibmFtZSIsInBvcCIsImN1cnJlbnRZZWFyIiwiZ2V0RnVsbFllYXIiLCJtdXRhdGUiLCJyZXNvdXJjZSIsImZvbGRlciIsInRvU3RyaW5nIiwiZmlsZW5hbWUiLCJib2R5Iiwib25TdWNjZXNzIiwiZGF0YVVVSUQiLCJ1dWlkUGhvdG9GaWxlTmFtZSIsIm1lc3NhZ2UiLCJ1dWlkIiwiaW1hZ2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQkFTRV9BUElfVVJMIiwicmVzdWx0IiwiaWQiLCJvbkVycm9yIiwic2V0RXJyb3IiLCJoYW5kbGVGaWxlQ2hhbmdlIiwidGFyZ2V0IiwicmVhZGVyIiwiRmlsZVJlYWRlciIsIm9ubG9hZCIsImFycmF5QnVmZmVyIiwiY29udmVydFRvSHRtbCIsImxvZyIsInZhbHVlIiwiZXJyIiwicmVhZEFzQXJyYXlCdWZmZXIiLCJwIiwiY2xhc3NOYW1lIiwiaW5wdXQiLCJhY2NlcHQiLCJvbkNoYW5nZSIsImRpdiIsInN0eWxlIiwiY29sb3IiLCJsYWJlbCIsImUiLCJ2IiwidGl0bGVFTiIsIm11bHRpcGxlIiwib3B0aW9ucyIsImdldE9wdGlvbkxhYmVsIiwib3B0aW9uIiwic2VsZWN0ZWQiLCJjYXRlZ29yeUVOIiwiZmlsdGVyIiwiY2F0ZWdvcnkiLCJpbmNsdWRlcyIsInNlbGVjdGVkT3B0aW9ucyIsImNhdGVnb3J5SWRzIiwicmVuZGVySW5wdXQiLCJwYXJhbXMiLCJ2YXJpYW50IiwibXVsdGlsaW5lIiwicm93cyIsImNvbXBvbmVudCIsImhpZ2hsaWdodHNFTiIsImhhbmRsZURlbGV0ZSIsInVwZGF0ZWRUYWdzIiwidGFnIiwiaW5kZXgiLCJoYW5kbGVBZGRpdGlvbiIsIml0ZW0iLCJpbnB1dEZpZWxkUG9zaXRpb24iLCJhdXRvY29tcGxldGUiLCJhbGxvd0RyYWdEcm9wIiwic2V0Q29udGVudHMiLCJjb250ZW50RU4iLCJvblBhc3RlIiwic2V0T3B0aW9ucyIsImNsZWFuSFRNTCIsImRpc2FibGVIdG1sU2FuaXRpemVyIiwiYWRkVGFnc1doaXRlbGlzdCIsImJ1dHRvbkxpc3QiLCJpbWFnZVVwbG9hZEhhbmRsZXIiLCJkZWZhdWx0VGFnIiwibWluSGVpZ2h0IiwibWF4SGVpZ2h0Iiwic2hvd1BhdGhMYWJlbCIsImZvbnQiLCJjaGFyQ291bnRlciIsImNoYXJDb3VudGVyVHlwZSIsInJlc2l6aW5nQmFyIiwiY29sb3JMaXN0Iiwib25JbWFnZVVwbG9hZCIsImJyIiwic3BhbiIsIm1ldGFUaXRsZUVOIiwibWV0YURlc2NyaXB0aW9uRU4iLCJodG1sRm9yIiwicmVmIiwiaW1hZ2VFTiIsImJhY2tncm91bmRJbWFnZSIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsIlJFQUNUX0FQUF9BUElfVVJMIiwiYmFja2dyb3VuZFNpemUiLCJiYWNrZ3JvdW5kUmVwZWF0IiwiYmFja2dyb3VuZFBvc2l0aW9uIiwiYWx0RU4iLCJ2aXNpYmlsaXR5RU4iLCJrZXl3b3Jkc0VOIiwiY2hlY2tlZCIsInRvSVNPU3RyaW5nIiwiZGF0ZUFkYXB0ZXIiLCJjb21wb25lbnRzIiwiZm9ybWF0IiwicHVibGlzaERhdGVFTiIsImRhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\n"));

/***/ })

});