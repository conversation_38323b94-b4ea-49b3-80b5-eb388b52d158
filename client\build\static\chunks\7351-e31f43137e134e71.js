(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7351,4013],{94013:function(e,t,i){"use strict";i.d(t,{Z:function(){return N}});var l=i(2265),n=i(61994),o=i(53232),a=i(20801),r=i(82590),s=i(32709),u=i(34765),c=i(16210),d=i(76301),h=i(37053),p=i(82662),g=i(35389),m=i(85657),f=i(3858),b=i(94143),v=i(50738);function _(e){return(0,v.ZP)("MuiButton",e)}let w=(0,b.Z)("MuiButton",["root","text","textInherit","textPrimary","textSecondary","textSuccess","textError","textInfo","textWarning","outlined","outlinedInherit","outlinedPrimary","outlinedSecondary","outlinedSuccess","outlinedError","outlinedInfo","outlinedWarning","contained","containedInherit","containedPrimary","containedSecondary","containedSuccess","containedError","containedInfo","containedWarning","disableElevation","focusVisible","disabled","colorInherit","colorPrimary","colorSecondary","colorSuccess","colorError","colorInfo","colorWarning","textSizeSmall","textSizeMedium","textSizeLarge","outlinedSizeSmall","outlinedSizeMedium","outlinedSizeLarge","containedSizeSmall","containedSizeMedium","containedSizeLarge","sizeMedium","sizeSmall","sizeLarge","fullWidth","startIcon","endIcon","icon","iconSizeSmall","iconSizeMedium","iconSizeLarge","loading","loadingWrapper","loadingIconPlaceholder","loadingIndicator","loadingPositionCenter","loadingPositionStart","loadingPositionEnd"]),y=l.createContext({}),x=l.createContext(void 0);var C=i(57437);let k=e=>{let{color:t,disableElevation:i,fullWidth:l,size:n,variant:o,loading:r,loadingPosition:s,classes:u}=e,c={root:["root",r&&"loading",o,`${o}${(0,m.Z)(t)}`,`size${(0,m.Z)(n)}`,`${o}Size${(0,m.Z)(n)}`,`color${(0,m.Z)(t)}`,i&&"disableElevation",l&&"fullWidth",r&&`loadingPosition${(0,m.Z)(s)}`],startIcon:["icon","startIcon",`iconSize${(0,m.Z)(n)}`],endIcon:["icon","endIcon",`iconSize${(0,m.Z)(n)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]},d=(0,a.Z)(c,_,u);return{...u,...d}},S=[{props:{size:"small"},style:{"& > *:nth-of-type(1)":{fontSize:18}}},{props:{size:"medium"},style:{"& > *:nth-of-type(1)":{fontSize:20}}},{props:{size:"large"},style:{"& > *:nth-of-type(1)":{fontSize:22}}}],z=(0,c.ZP)(p.Z,{shouldForwardProp:e=>(0,u.Z)(e)||"classes"===e,name:"MuiButton",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.root,t[i.variant],t[`${i.variant}${(0,m.Z)(i.color)}`],t[`size${(0,m.Z)(i.size)}`],t[`${i.variant}Size${(0,m.Z)(i.size)}`],"inherit"===i.color&&t.colorInherit,i.disableElevation&&t.disableElevation,i.fullWidth&&t.fullWidth,i.loading&&t.loading]}})((0,d.Z)(e=>{let{theme:t}=e,i="light"===t.palette.mode?t.palette.grey[300]:t.palette.grey[800],l="light"===t.palette.mode?t.palette.grey.A100:t.palette.grey[700];return{...t.typography.button,minWidth:64,padding:"6px 16px",border:0,borderRadius:(t.vars||t).shape.borderRadius,transition:t.transitions.create(["background-color","box-shadow","border-color","color"],{duration:t.transitions.duration.short}),"&:hover":{textDecoration:"none"},[`&.${w.disabled}`]:{color:(t.vars||t).palette.action.disabled},variants:[{props:{variant:"contained"},style:{color:"var(--variant-containedColor)",backgroundColor:"var(--variant-containedBg)",boxShadow:(t.vars||t).shadows[2],"&:hover":{boxShadow:(t.vars||t).shadows[4],"@media (hover: none)":{boxShadow:(t.vars||t).shadows[2]}},"&:active":{boxShadow:(t.vars||t).shadows[8]},[`&.${w.focusVisible}`]:{boxShadow:(t.vars||t).shadows[6]},[`&.${w.disabled}`]:{color:(t.vars||t).palette.action.disabled,boxShadow:(t.vars||t).shadows[0],backgroundColor:(t.vars||t).palette.action.disabledBackground}}},{props:{variant:"outlined"},style:{padding:"5px 15px",border:"1px solid currentColor",borderColor:"var(--variant-outlinedBorder, currentColor)",backgroundColor:"var(--variant-outlinedBg)",color:"var(--variant-outlinedColor)",[`&.${w.disabled}`]:{border:`1px solid ${(t.vars||t).palette.action.disabledBackground}`}}},{props:{variant:"text"},style:{padding:"6px 8px",color:"var(--variant-textColor)",backgroundColor:"var(--variant-textBg)"}},...Object.entries(t.palette).filter((0,f.Z)()).map(e=>{let[i]=e;return{props:{color:i},style:{"--variant-textColor":(t.vars||t).palette[i].main,"--variant-outlinedColor":(t.vars||t).palette[i].main,"--variant-outlinedBorder":t.vars?`rgba(${t.vars.palette[i].mainChannel} / 0.5)`:(0,r.Fq)(t.palette[i].main,.5),"--variant-containedColor":(t.vars||t).palette[i].contrastText,"--variant-containedBg":(t.vars||t).palette[i].main,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":(t.vars||t).palette[i].dark,"--variant-textBg":t.vars?`rgba(${t.vars.palette[i].mainChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,r.Fq)(t.palette[i].main,t.palette.action.hoverOpacity),"--variant-outlinedBorder":(t.vars||t).palette[i].main,"--variant-outlinedBg":t.vars?`rgba(${t.vars.palette[i].mainChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,r.Fq)(t.palette[i].main,t.palette.action.hoverOpacity)}}}}}),{props:{color:"inherit"},style:{color:"inherit",borderColor:"currentColor","--variant-containedBg":t.vars?t.vars.palette.Button.inheritContainedBg:i,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":t.vars?t.vars.palette.Button.inheritContainedHoverBg:l,"--variant-textBg":t.vars?`rgba(${t.vars.palette.text.primaryChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,r.Fq)(t.palette.text.primary,t.palette.action.hoverOpacity),"--variant-outlinedBg":t.vars?`rgba(${t.vars.palette.text.primaryChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,r.Fq)(t.palette.text.primary,t.palette.action.hoverOpacity)}}}},{props:{size:"small",variant:"text"},style:{padding:"4px 5px",fontSize:t.typography.pxToRem(13)}},{props:{size:"large",variant:"text"},style:{padding:"8px 11px",fontSize:t.typography.pxToRem(15)}},{props:{size:"small",variant:"outlined"},style:{padding:"3px 9px",fontSize:t.typography.pxToRem(13)}},{props:{size:"large",variant:"outlined"},style:{padding:"7px 21px",fontSize:t.typography.pxToRem(15)}},{props:{size:"small",variant:"contained"},style:{padding:"4px 10px",fontSize:t.typography.pxToRem(13)}},{props:{size:"large",variant:"contained"},style:{padding:"8px 22px",fontSize:t.typography.pxToRem(15)}},{props:{disableElevation:!0},style:{boxShadow:"none","&:hover":{boxShadow:"none"},[`&.${w.focusVisible}`]:{boxShadow:"none"},"&:active":{boxShadow:"none"},[`&.${w.disabled}`]:{boxShadow:"none"}}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{loadingPosition:"center"},style:{transition:t.transitions.create(["background-color","box-shadow","border-color"],{duration:t.transitions.duration.short}),[`&.${w.loading}`]:{color:"transparent"}}}]}})),L=(0,c.ZP)("span",{name:"MuiButton",slot:"StartIcon",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.startIcon,i.loading&&t.startIconLoadingStart,t[`iconSize${(0,m.Z)(i.size)}`]]}})(e=>{let{theme:t}=e;return{display:"inherit",marginRight:8,marginLeft:-4,variants:[{props:{size:"small"},style:{marginLeft:-2}},{props:{loadingPosition:"start",loading:!0},style:{transition:t.transitions.create(["opacity"],{duration:t.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"start",loading:!0,fullWidth:!0},style:{marginRight:-8}},...S]}}),E=(0,c.ZP)("span",{name:"MuiButton",slot:"EndIcon",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.endIcon,i.loading&&t.endIconLoadingEnd,t[`iconSize${(0,m.Z)(i.size)}`]]}})(e=>{let{theme:t}=e;return{display:"inherit",marginRight:-4,marginLeft:8,variants:[{props:{size:"small"},style:{marginRight:-2}},{props:{loadingPosition:"end",loading:!0},style:{transition:t.transitions.create(["opacity"],{duration:t.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"end",loading:!0,fullWidth:!0},style:{marginLeft:-8}},...S]}}),A=(0,c.ZP)("span",{name:"MuiButton",slot:"LoadingIndicator",overridesResolver:(e,t)=>t.loadingIndicator})(e=>{let{theme:t}=e;return{display:"none",position:"absolute",visibility:"visible",variants:[{props:{loading:!0},style:{display:"flex"}},{props:{loadingPosition:"start"},style:{left:14}},{props:{loadingPosition:"start",size:"small"},style:{left:10}},{props:{variant:"text",loadingPosition:"start"},style:{left:6}},{props:{loadingPosition:"center"},style:{left:"50%",transform:"translate(-50%)",color:(t.vars||t).palette.action.disabled}},{props:{loadingPosition:"end"},style:{right:14}},{props:{loadingPosition:"end",size:"small"},style:{right:10}},{props:{variant:"text",loadingPosition:"end"},style:{right:6}},{props:{loadingPosition:"start",fullWidth:!0},style:{position:"relative",left:-10}},{props:{loadingPosition:"end",fullWidth:!0},style:{position:"relative",right:-10}}]}}),B=(0,c.ZP)("span",{name:"MuiButton",slot:"LoadingIconPlaceholder",overridesResolver:(e,t)=>t.loadingIconPlaceholder})({display:"inline-block",width:"1em",height:"1em"});var N=l.forwardRef(function(e,t){let i=l.useContext(y),a=l.useContext(x),r=(0,o.Z)(i,e),u=(0,h.i)({props:r,name:"MuiButton"}),{children:c,color:d="primary",component:p="button",className:m,disabled:f=!1,disableElevation:b=!1,disableFocusRipple:v=!1,endIcon:_,focusVisibleClassName:w,fullWidth:S=!1,id:N,loading:T=null,loadingIndicator:I,loadingPosition:M="center",size:R="medium",startIcon:H,type:V,variant:D="text",...O}=u,U=(0,s.Z)(N),P=I??(0,C.jsx)(g.default,{"aria-labelledby":U,color:"inherit",size:16}),F={...u,color:d,component:p,disabled:f,disableElevation:b,disableFocusRipple:v,fullWidth:S,loading:T,loadingIndicator:P,loadingPosition:M,size:R,type:V,variant:D},Z=k(F),j=(H||T&&"start"===M)&&(0,C.jsx)(L,{className:Z.startIcon,ownerState:F,children:H||(0,C.jsx)(B,{className:Z.loadingIconPlaceholder,ownerState:F})}),W=(_||T&&"end"===M)&&(0,C.jsx)(E,{className:Z.endIcon,ownerState:F,children:_||(0,C.jsx)(B,{className:Z.loadingIconPlaceholder,ownerState:F})}),q="boolean"==typeof T?(0,C.jsx)("span",{className:Z.loadingWrapper,style:{display:"contents"},children:T&&(0,C.jsx)(A,{className:Z.loadingIndicator,ownerState:F,children:P})}):null;return(0,C.jsxs)(z,{ownerState:F,className:(0,n.Z)(i.className,Z.root,m,a||""),component:p,disabled:f||T,focusRipple:!v,focusVisibleClassName:(0,n.Z)(Z.focusVisible,w),ref:t,type:V,id:T?U:N,...O,classes:Z,children:[j,"end"!==M&&q,c,"end"===M&&q,W]})})},23996:function(e,t,i){"use strict";i.d(t,{Z:function(){return y}});var l,n=i(2265),o=i(61994),a=i(20801),r=i(85657),s=i(46387),u=i(47159),c=i(66515),d=i(16210),h=i(76301),p=i(37053),g=i(94143),m=i(50738);function f(e){return(0,m.ZP)("MuiInputAdornment",e)}let b=(0,g.Z)("MuiInputAdornment",["root","filled","standard","outlined","positionStart","positionEnd","disablePointerEvents","hiddenLabel","sizeSmall"]);var v=i(57437);let _=e=>{let{classes:t,disablePointerEvents:i,hiddenLabel:l,position:n,size:o,variant:s}=e,u={root:["root",i&&"disablePointerEvents",n&&`position${(0,r.Z)(n)}`,s,l&&"hiddenLabel",o&&`size${(0,r.Z)(o)}`]};return(0,a.Z)(u,f,t)},w=(0,d.ZP)("div",{name:"MuiInputAdornment",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.root,t[`position${(0,r.Z)(i.position)}`],!0===i.disablePointerEvents&&t.disablePointerEvents,t[i.variant]]}})((0,h.Z)(e=>{let{theme:t}=e;return{display:"flex",maxHeight:"2em",alignItems:"center",whiteSpace:"nowrap",color:(t.vars||t).palette.action.active,variants:[{props:{variant:"filled"},style:{[`&.${b.positionStart}&:not(.${b.hiddenLabel})`]:{marginTop:16}}},{props:{position:"start"},style:{marginRight:8}},{props:{position:"end"},style:{marginLeft:8}},{props:{disablePointerEvents:!0},style:{pointerEvents:"none"}}]}}));var y=n.forwardRef(function(e,t){let i=(0,p.i)({props:e,name:"MuiInputAdornment"}),{children:a,className:r,component:d="div",disablePointerEvents:h=!1,disableTypography:g=!1,position:m,variant:f,...b}=i,y=(0,c.Z)()||{},x=f;f&&y.variant,y&&!x&&(x=y.variant);let C={...i,hiddenLabel:y.hiddenLabel,size:y.size,disablePointerEvents:h,position:m,variant:x},k=_(C);return(0,v.jsx)(u.Z.Provider,{value:null,children:(0,v.jsx)(w,{as:d,ownerState:C,className:(0,o.Z)(k.root,r),ref:t,...b,children:"string"!=typeof a||g?(0,v.jsxs)(n.Fragment,{children:["start"===m?l||(l=(0,v.jsx)("span",{className:"notranslate","aria-hidden":!0,children:"​"})):null,a]}):(0,v.jsx)(s.default,{color:"textSecondary",children:a})})})})},46387:function(e,t,i){"use strict";var l=i(2265),n=i(61994),o=i(20801),a=i(66659),r=i(16210),s=i(76301),u=i(37053),c=i(85657),d=i(3858),h=i(56200),p=i(57437);let g={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},m=(0,a.u7)(),f=e=>{let{align:t,gutterBottom:i,noWrap:l,paragraph:n,variant:a,classes:r}=e,s={root:["root",a,"inherit"!==e.align&&`align${(0,c.Z)(t)}`,i&&"gutterBottom",l&&"noWrap",n&&"paragraph"]};return(0,o.Z)(s,h.f,r)},b=(0,r.ZP)("span",{name:"MuiTypography",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.root,i.variant&&t[i.variant],"inherit"!==i.align&&t[`align${(0,c.Z)(i.align)}`],i.noWrap&&t.noWrap,i.gutterBottom&&t.gutterBottom,i.paragraph&&t.paragraph]}})((0,s.Z)(e=>{let{theme:t}=e;return{margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(t.typography).filter(e=>{let[t,i]=e;return"inherit"!==t&&i&&"object"==typeof i}).map(e=>{let[t,i]=e;return{props:{variant:t},style:i}}),...Object.entries(t.palette).filter((0,d.Z)()).map(e=>{let[i]=e;return{props:{color:i},style:{color:(t.vars||t).palette[i].main}}}),...Object.entries(t.palette?.text||{}).filter(e=>{let[,t]=e;return"string"==typeof t}).map(e=>{let[i]=e;return{props:{color:`text${(0,c.Z)(i)}`},style:{color:(t.vars||t).palette.text[i]}}}),{props:e=>{let{ownerState:t}=e;return"inherit"!==t.align},style:{textAlign:"var(--Typography-textAlign)"}},{props:e=>{let{ownerState:t}=e;return t.noWrap},style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:e=>{let{ownerState:t}=e;return t.gutterBottom},style:{marginBottom:"0.35em"}},{props:e=>{let{ownerState:t}=e;return t.paragraph},style:{marginBottom:16}}]}})),v={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},_=l.forwardRef(function(e,t){let{color:i,...l}=(0,u.i)({props:e,name:"MuiTypography"}),o=!g[i],a=m({...l,...o&&{color:i}}),{align:r="inherit",className:s,component:c,gutterBottom:d=!1,noWrap:h=!1,paragraph:_=!1,variant:w="body1",variantMapping:y=v,...x}=a,C={...a,align:r,color:i,className:s,component:c,gutterBottom:d,noWrap:h,paragraph:_,variant:w,variantMapping:y},k=c||(_?"p":y[w]||v[w])||"span",S=f(C);return(0,p.jsx)(b,{as:k,ref:t,className:(0,n.Z)(S.root,s),...x,ownerState:C,style:{..."inherit"!==r&&{"--Typography-textAlign":r},...x.style}})});t.default=_},56200:function(e,t,i){"use strict";i.d(t,{f:function(){return o}});var l=i(94143),n=i(50738);function o(e){return(0,n.ZP)("MuiTypography",e)}let a=(0,l.Z)("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);t.Z=a},5100:function(e,t,i){"use strict";i.d(t,{C:function(){return p}});var l=i(1119),n=i(2265),o=i(63582);let a=(0,i(94143).Z)("MuiStack",["root"]);i(46387);var r=i(67753),s=i(36496),u=i(57437);let c=e=>e.match(/^([A-Za-z]+)Range(Calendar|Clock)$/)?"multi-panel-UI-view":e.match(/^([A-Za-z]*)(DigitalClock)$/)?"Tall-UI-view":e.match(/^Static([A-Za-z]+)/)||e.match(/^([A-Za-z]+)(Calendar|Clock)$/)?"UI-view":e.match(/^MultiInput([A-Za-z]+)RangeField$/)||e.match(/^([A-Za-z]+)RangePicker$/)?"multi-input-range-field":e.match(/^SingleInput([A-Za-z]+)RangeField$/)?"single-input-range-field":"single-input-field",d=e=>e.includes("DateTime")?"date-time":e.includes("Date")?"date":"time",h=e=>!!n.isValidElement(e)&&"string"!=typeof e.type&&"DemoItem"===e.type.displayName;function p(e){let t,i;let{children:p,components:g,sx:m}=e,f=new Set,b=new Set;g.forEach(e=>{f.add(c(e)),b.add(d(e))});let v=e=>"row"===e?f.has("UI-view")||f.has("Tall-UI-view")?3:2:f.has("UI-view")?4:3,_={},w={},y=(0,l.Z)({overflow:"auto",pt:1},m);g.length>2||f.has("multi-input-range-field")||f.has("single-input-range-field")||f.has("multi-panel-UI-view")||f.has("UI-view")||b.has("date-time")?(t="column",i=v("column")):(t={xs:"column",lg:"row"},i={xs:v("column"),lg:v("row")}),f.has("UI-view")||(f.has("single-input-range-field")?_=b.has("date-time")?{[`& > .${r.Z.root}, & > .${s.D.root}`]:{minWidth:{xs:300,md:f.has("multi-input-range-field")?460:400}}}:{[`& > .${r.Z.root}, & > .${s.D.root}`]:{minWidth:300}}:b.has("date-time")?(_={[`& > .${r.Z.root}, & > .${s.D.root}`]:{minWidth:270}},f.has("multi-input-range-field")&&(w={[`& > .${a.root} > .${r.Z.root}, & > .${a.root} > .${s.D.root}`]:{minWidth:210}})):_={[`& > .${r.Z.root}, & > .${s.D.root}`]:{minWidth:200}});let x=(0,l.Z)({},y,_);return(0,u.jsx)(o.Z,{direction:t,spacing:i,sx:x,children:n.Children.map(p,e=>n.isValidElement(e)&&h(e)?n.cloneElement(e,{sx:(0,l.Z)({},_,w)}):e)})}},48049:function(e,t,i){"use strict";var l=i(14397);function n(){}function o(){}o.resetWarningCache=n,e.exports=function(){function e(e,t,i,n,o,a){if(a!==l){var r=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw r.name="Invariant Violation",r}}function t(){return e}e.isRequired=e;var i={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:n};return i.PropTypes=i,i}},40718:function(e,t,i){e.exports=i(48049)()},14397:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},25319:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatting=t.complex=t.basic=void 0,t.basic=[["font","fontSize"],["fontColor"],["horizontalRule"],["link","image"]],t.complex=[["undo","redo"],["font","fontSize","formatBlock"],["bold","underline","italic","strike","subscript","superscript"],["removeFormat"],"/",["fontColor","hiliteColor"],["outdent","indent"],["align","horizontalRule","list","table"],["link","image","video"],["fullScreen","showBlocks","codeView"],["preview","print"],["save","template"]],t.formatting=[["undo","redo"],["bold","underline","italic","strike","subscript","superscript"],["removeFormat"],["outdent","indent"],["fullScreen","showBlocks","codeView"],["preview","print"]]},79077:function(e,t,i){"use strict";var l=this&&this.__assign||function(){return(l=Object.assign||function(e){for(var t,i=1,l=arguments.length;i<l;i++)for(var n in t=arguments[i])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)},n=this&&this.__createBinding||(Object.create?function(e,t,i,l){void 0===l&&(l=i);var n=Object.getOwnPropertyDescriptor(t,i);(!n||("get"in n?!t.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(e,l,n)}:function(e,t,i,l){void 0===l&&(l=i),e[l]=t[i]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var i in e)"default"!==i&&Object.prototype.hasOwnProperty.call(e,i)&&n(t,e,i);return o(t,e),t},r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var s=a(i(2265)),u=r(i(61053)),c=r(i(38438)),d=r(i(55789)),h=i(77042);t.default=function(e){var t=e.name,i=e.lang,n=e.setOptions,o=void 0===n?{}:n,a=e.placeholder,r=e.width,p=void 0===r?"100%":r,g=e.height,m=e.defaultValue,f=e.setContents,b=e.setDefaultStyle,v=e.getSunEditorInstance,_=e.appendContents,w=e.setAllPlugins,y=void 0===w||w,x=e.disable,C=void 0!==x&&x,k=e.readOnly,S=void 0!==k&&k,z=e.hide,L=void 0!==z&&z,E=e.hideToolbar,A=void 0!==E&&E,B=e.disableToolbar,N=void 0!==B&&B,T=e.onChange,I=e.autoFocus,M=e.onBlur,R=e.onLoad,H=(0,s.useRef)(null),V=(0,s.useRef)(null),D=(0,s.useRef)(!0);return(0,s.useEffect)(function(){var n,r=l(l({},o),{lang:i?(0,d.default)(i):o.lang,width:null!=p?p:o.width,placeholder:null!=a?a:o.placeholder,plugins:null!==(n=o.plugins)&&void 0!==n?n:y?u.default:void 0,height:null!=g?g:o.height,value:null!=m?m:o.value,defaultStyle:null!=b?b:o.defaultStyle});return t&&r.value&&(H.current.value=r.value),V.current=c.default.create(H.current,r),v&&v(V.current),V.current.onload=function(e,t){return t||(f&&(V.current.setContents(f),V.current.core.focusEdge(null)),_&&V.current.appendContents(_),V.current.util.isIE&&V.current.core._createDefaultRange(),C&&V.current.disable(),S&&V.current.readOnly(!0),L&&V.current.hide(),A&&V.current.toolbar.hide(),N&&V.current.toolbar.disable(),!1===I?V.current.core.context.element.wysiwyg.blur():I&&V.current.core.context.element.wysiwyg.focus()),null==R?void 0:R(t)},V.current.onChange=function(e){t&&H.current&&(H.current.value=e),T&&T(e)},M&&(V.current.onBlur=function(e){return M(e,V.current.getContents(!0))}),h.uploadBeforeEvents.forEach(function(t){var i=e[t];V.current&&i&&(V.current[t]=function(e,t,l,n){return i(e,t,n)})}),h.events.forEach(function(t){var i=e[t];i&&V.current&&(V.current[t]=i)}),function(){V.current&&V.current.destroy(),V.current=null}},[]),(0,s.useEffect)(function(){var e;D.current||null===(e=V.current)||void 0===e||e.setOptions({lang:(0,d.default)(i)})},[i]),(0,s.useEffect)(function(){var e;D.current||null===(e=V.current)||void 0===e||e.setOptions({placeholder:a,height:g,width:p})},[a,g,p]),(0,s.useEffect)(function(){var e;b&&!D.current&&(null===(e=V.current)||void 0===e||e.setDefaultStyle(b))},[b]),(0,s.useEffect)(function(){var e,t;D.current||void 0===f||(null===(e=V.current)||void 0===e?void 0:e.core.hasFocus)||null===(t=V.current)||void 0===t||t.setContents(f)},[f]),(0,s.useEffect)(function(){var e,t,i;D.current||void 0===_||(null===(e=V.current)||void 0===e?void 0:e.core.hasFocus)||(null===(t=V.current)||void 0===t||t.appendContents(_),null===(i=V.current)||void 0===i||i.core.focusEdge(null))},[_]),(0,s.useEffect)(function(){var e,t,i,l,n,o,a,r,s;D.current||(null===(e=V.current)||void 0===e||e.readOnly(S),A?null===(t=V.current)||void 0===t||t.toolbar.hide():null===(i=V.current)||void 0===i||i.toolbar.show(),N?null===(l=V.current)||void 0===l||l.toolbar.disable():null===(n=V.current)||void 0===n||n.toolbar.enable(),C?null===(o=V.current)||void 0===o||o.disable():null===(a=V.current)||void 0===a||a.enable(),L?null===(r=V.current)||void 0===r||r.hide():null===(s=V.current)||void 0===s||s.show())},[C,A,N,L,S]),(0,s.useEffect)(function(){D.current=!1},[]),s.default.createElement("textarea",l({style:{visibility:"hidden"},ref:H},{name:t}))}},77042:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.uploadBeforeEvents=t.events=void 0,t.events=["onMouseDown","onScroll","onInput","onClick","onKeyUp","onKeyDown","onFocus","onImageUpload","onAudioUpload","onVideoUpload","onImageUploadError","onVideoUploadError","onAudioUploadError","onSave","onSetToolbarButtons","imageUploadHandler","toggleCodeView","toggleFullScreen","showInline","showController","onCopy","onCut","onDrop","onPaste"],t.uploadBeforeEvents=["onImageUploadBefore","onVideoUploadBefore","onAudioUploadBefore"]},17382:function(e,t,i){"use strict";var l=this&&this.__createBinding||(Object.create?function(e,t,i,l){void 0===l&&(l=i);var n=Object.getOwnPropertyDescriptor(t,i);(!n||("get"in n?!t.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(e,l,n)}:function(e,t,i,l){void 0===l&&(l=i),e[l]=t[i]}),n=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var i in e)"default"!==i&&Object.prototype.hasOwnProperty.call(e,i)&&l(t,e,i);return n(t,e),t},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.buttonList=void 0;var r=a(i(79077));t.buttonList=o(i(25319)),t.default=r.default},55789:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){switch(typeof e){case"object":return e;case"string":return i(14485)("./".concat(e,".js"));default:return}}},9706:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"ckb",toolbar:{default:"بنه‌ڕه‌ت",save:"پاشه‌كه‌وتكردن",font:"فۆنت",formats:"Formats",fontSize:"قه‌باره‌",bold:"تۆخكردن",underline:"هێڵ به‌ژێردا بێنه‌",italic:"لار",strike:"هێڵ به‌ناودا بێنه‌",subscript:"ژێرسکریپت",superscript:"سەرنووس",removeFormat:"لابردنی فۆرمات",fontColor:"ره‌نگی فۆنت",hiliteColor:"ره‌نگی دیاركراو",indent:"بۆشایی بەجێهێشتن",outdent:"لابردنی بۆشایی",align:"ئاراسته‌",alignLeft:"لای چه‌پ",alignRight:"لای راست",alignCenter:"ناوه‌ند",alignJustify:"به‌رێكی دابه‌ش بكه‌",list:"لیست",orderList:"لیستی ریزكراو",unorderList:"لیستی ریزنه‌كراو",horizontalRule:"هێڵی ئاسۆیی",hr_solid:"پته‌و",hr_dotted:"نوكته‌ نوكته‌",hr_dashed:"داش داش",table:"خشته‌",link:"به‌سته‌ر",math:"بیركاری",image:"وێنه‌",video:"ڤیدیۆ",audio:"ده‌نگ",fullScreen:"پڕ به‌ شاشه‌",showBlocks:"بڵۆك نیشانبده",codeView:"بینینی كۆده‌كان",undo:"وەک خۆی لێ بکەوە",redo:"هەڵگەڕاندنەوە",preview:"پێشبینین",print:"پرینت",tag_p:"په‌ره‌گراف",tag_div:"ی ئاسایی (DIV)",tag_h:"سەرپەڕە",tag_blockquote:"ده‌ق",tag_pre:"كۆد",template:"قاڵب",lineHeight:"بڵندی دێر",paragraphStyle:"ستایلی په‌ره‌گراف",textStyle:"ستایلی نوسین",imageGallery:"گاله‌ری وێنه‌كان",dir_ltr:"من اليسار إلى اليمين",dir_rtl:"من اليمين الى اليسار",mention:"تنويه ب"},dialogBox:{linkBox:{title:"به‌سته‌ر دابنێ",url:"به‌سته‌ر",text:"تێكستی به‌سته‌ر",newWindowCheck:"له‌ په‌نجه‌ره‌یه‌كی نوێ بكه‌ره‌وه‌",downloadLinkCheck:"رابط التحميل",bookmark:"المرجعية"},mathBox:{title:"بیركاری",inputLabel:"نیشانه‌كانی بیركاری",fontSizeLabel:"قه‌باره‌ی فۆنت",previewLabel:"پێشبینین"},imageBox:{title:"وێنه‌یه‌ك دابنێ",file:"فایلێك هه‌ڵبژێره‌",url:"به‌سته‌ری وێنه‌",altText:"نوسینی جێگره‌وه‌"},videoBox:{title:"ڤیدیۆیه‌ك دابنێ",file:"فایلێك هه‌ڵبژێره‌",url:"YouTube/Vimeo به‌سته‌ری له‌ناودانان وه‌ك "},audioBox:{title:"ده‌نگێك دابنێ",file:"فایلێك هه‌ڵبژێره‌",url:"به‌سته‌ری ده‌نگ"},browser:{tags:"تاگه‌كان",search:"گه‌ران"},caption:"پێناسه‌یه‌ك دابنێ",close:"داخستن",submitButton:"ناردن",revertButton:"بیگەڕێنەوە سەر باری سەرەتایی",proportion:"رێژه‌كان وه‌ك خۆی بهێڵه‌وه‌",basic:"سه‌ره‌تایی",left:"چه‌پ",right:"راست",center:"ناوەڕاست",width:"پانی",height:"به‌رزی",size:"قه‌باره‌",ratio:"رێژه‌"},controller:{edit:"دەسکاریکردن",unlink:"سڕینەوەی بەستەر",remove:"سڕینه‌وه‌",insertRowAbove:"ریزك له‌ سه‌ره‌وه‌ زیادبكه‌",insertRowBelow:"ریزێك له‌ خواره‌وه‌ زیادبكه‌",deleteRow:"ریز بسره‌وه‌",insertColumnBefore:"ستونێك له‌ پێشه‌وه‌ زیادبكه‌",insertColumnAfter:"ستونێك له‌ دواوه‌ زیادبكه‌",deleteColumn:"ستونێك بسره‌وه‌",fixedColumnWidth:"پانی ستون نه‌گۆربكه‌",resize100:"قه‌باره‌ بگۆره‌ بۆ ١٠٠%",resize75:"قه‌باره‌ بگۆره‌ بۆ ٧٥%",resize50:"قه‌باره‌ بگۆره‌ بۆ ٥٠%",resize25:"قه‌باره‌ بگۆره‌ بۆ ٢٥%",autoSize:"قه‌باره‌ی خۆكارانه‌",mirrorHorizontal:"هه‌ڵگه‌رێنه‌وه‌ به‌ده‌وری ته‌وه‌ره‌ی ئاسۆیی",mirrorVertical:"هه‌ڵگه‌رێنه‌وه‌ به‌ده‌وری ته‌وه‌ره‌ی ستونی",rotateLeft:"بسوڕێنه‌ به‌لای چه‌پدا",rotateRight:"بسورێنه‌ به‌لای راستدا",maxSize:"گه‌وره‌ترین قه‌باره‌",minSize:"بچوكترین قه‌باره‌",tableHeader:"سه‌ردێری خشته‌ك",mergeCells:"خانه‌كان تێكه‌ڵبكه‌",splitCells:"خانه‌كان لێك جیابكه‌وه‌",HorizontalSplit:"جیاكردنه‌وه‌ی ئاسۆیی",VerticalSplit:"جیاكردنه‌وه‌ی ستونی"},menu:{spaced:"بۆشای هه‌بێت",bordered:"لێواری هه‌بێت",neon:"نیۆن",translucent:"كه‌مێك وه‌ك شووشه‌",shadow:"سێبه‌ر",code:"كۆد"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"ckb",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},79089:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"cs",toolbar:{default:"V\xfdchoz\xed",save:"Uložit",font:"P\xedsmo",formats:"Form\xe1ty",fontSize:"Velikost",bold:"Tučn\xe9",underline:"Podtržen\xed",italic:"Kurz\xedva",strike:"Přeškrtnut\xed",subscript:"Doln\xed index",superscript:"Horn\xed index",removeFormat:"Odebrat form\xe1t",fontColor:"Barva p\xedsma",hiliteColor:"Barva zv\xfdrazněn\xed",indent:"Odsadit",outdent:"Předsadit",align:"Zarovnat",alignLeft:"Zarovnat vlevo",alignRight:"Zarovnat vpravo",alignCenter:"Zarovnat na střed",alignJustify:"Zarovnat do bloku",list:"Seznam",orderList:"Seřazen\xfd seznam",unorderList:"Neřazen\xfd seznam",horizontalRule:"Vodorovn\xe1 č\xe1ra",hr_solid:"Nepřerušovan\xe1",hr_dotted:"Tečkovan\xe1",hr_dashed:"Č\xe1rkovan\xe1",table:"Tabulka",link:"Odkaz",math:"Matematika",image:"Obr\xe1zek",video:"Video",audio:"Zvuk",fullScreen:"Cel\xe1 obrazovka",showBlocks:"Zobrazit bloky",codeView:"Zobrazen\xed k\xf3du",undo:"Zpět",redo:"Opakovat",preview:"N\xe1hled",print:"tisk",tag_p:"Odstavec",tag_div:"Norm\xe1ln\xed (DIV)",tag_h:"Z\xe1hlav\xed",tag_blockquote:"Citovat",tag_pre:"K\xf3d",template:"Šablona",lineHeight:"V\xfdška ř\xe1dku",paragraphStyle:"Styl odstavce",textStyle:"Styl textu",imageGallery:"Obr\xe1zkov\xe1 galerie",dir_ltr:"Zleva doprava",dir_rtl:"Zprava doleva",mention:"Zm\xednka"},dialogBox:{linkBox:{title:"Vložit odkaz",url:"URL pro odkaz",text:"Text k zobrazen\xed",newWindowCheck:"Otevř\xedt v nov\xe9m okně",downloadLinkCheck:"Odkaz ke stažen\xed",bookmark:"Z\xe1ložka"},mathBox:{title:"Matematika",inputLabel:"Matematick\xe1 notace",fontSizeLabel:"Velikost p\xedsma",previewLabel:"N\xe1hled"},imageBox:{title:"Vložit obr\xe1zek",file:"Vybrat ze souborů",url:"URL obr\xe1zku",altText:"Alternativn\xed text"},videoBox:{title:"Vložit video",file:"Vybrat ze souborů",url:"URL pro vložen\xed m\xe9di\xed, YouTube/Vimeo"},audioBox:{title:"Vložit zvuk",file:"Vybrat ze souborů",url:"Adresa URL zvuku"},browser:{tags:"Št\xedtky",search:"Hledat"},caption:"Vložit popis",close:"Zavř\xedt",submitButton:"Odeslat",revertButton:"Vr\xe1tit zpět",proportion:"Omezen\xed proporc\xed",basic:"Z\xe1kladn\xed",left:"Vlevo",right:"Vpravo",center:"Střed",width:"Š\xedřka",height:"V\xfdška",size:"Velikost",ratio:"Poměr"},controller:{edit:"Upravit",unlink:"Odpojit",remove:"Odebrat",insertRowAbove:"Vložit ř\xe1dek v\xfdše",insertRowBelow:"Vložit ř\xe1dek n\xedže",deleteRow:"Smazat ř\xe1dek",insertColumnBefore:"Vložit sloupec před",insertColumnAfter:"Vložit sloupec za",deleteColumn:"Smazat sloupec",fixedColumnWidth:"Pevn\xe1 š\xedřka sloupce",resize100:"Změnit velikost 100%",resize75:"Změnit velikost 75%",resize50:"Změnit velikost 50%",resize25:"Změnit velikost 25%",autoSize:"Automatick\xe1 velikost",mirrorHorizontal:"Zrcadlo, horizont\xe1ln\xed",mirrorVertical:"Zrcadlo, vertik\xe1ln\xed",rotateLeft:"Otočit doleva",rotateRight:"Otočit doprava",maxSize:"Max. velikost",minSize:"Min. velikost",tableHeader:"Z\xe1hlav\xed tabulky",mergeCells:"Spojit buňky",splitCells:"Rozdělit buňky",HorizontalSplit:"Vodorovn\xe9 rozdělen\xed",VerticalSplit:"Svisl\xe9 rozdělen\xed"},menu:{spaced:"Rozložen\xe9",bordered:"Ohraničen\xe9",neon:"Neon",translucent:"Průsvitn\xe9",shadow:"St\xedn",code:"K\xf3d"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"cs",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},83138:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"da",toolbar:{default:"Default",save:"Gem",font:"Skrifttype",formats:"Format",fontSize:"Skriftst\xf8rrelse",bold:"Fed",underline:"Understreget",italic:"Skr\xe5skrift",strike:"Overstreget",subscript:"S\xe6nket skrift",superscript:"H\xe6vet skrift",removeFormat:"Fjern formatering",fontColor:"Skriftfarve",hiliteColor:"Baggrundsfarve",indent:"Ryk ind",outdent:"Ryk ud",align:"Justering",alignLeft:"Venstrejustering",alignRight:"H\xf8jrejustering",alignCenter:"Midterjustering",alignJustify:"Tilpas margin",list:"Lister",orderList:"Nummereret liste",unorderList:"Uordnet liste",horizontalRule:"Horisontal linie",hr_solid:"Almindelig",hr_dotted:"Punkteret",hr_dashed:"Streget",table:"Tabel",link:"Link",math:"Math",image:"Billede",video:"Video",audio:"Audio",fullScreen:"Fuld sk\xe6rm",showBlocks:"Vis blokke",codeView:"Vis koder",undo:"Undo",redo:"Redo",preview:"Preview",print:"Print",tag_p:"Paragraph",tag_div:"Normal (DIV)",tag_h:"Overskrift",tag_blockquote:"Citer",tag_pre:"Code",template:"Schablone",lineHeight:"Linjeh\xf8jde",paragraphStyle:"Afsnitstil",textStyle:"Tekststil",imageGallery:"Billedgalleri",dir_ltr:"Venstre til h\xf8jre",dir_rtl:"H\xf8jre til venstre",mention:"N\xe6vne"},dialogBox:{linkBox:{title:"Inds\xe6t link",url:"URL til link",text:"Tekst for link",newWindowCheck:"\xc5ben i nyt faneblad",downloadLinkCheck:"Download link",bookmark:"Bogm\xe6rke"},mathBox:{title:"Math",inputLabel:"Matematisk notation",fontSizeLabel:"Skriftst\xf8rrelse",previewLabel:"Preview"},imageBox:{title:"Inds\xe6t billede",file:"Inds\xe6t fra fil",url:"Inds\xe6t fra URL",altText:"Alternativ tekst"},videoBox:{title:"Inds\xe6t Video",file:"Inds\xe6t fra fil",url:"Indlejr video / YouTube,Vimeo"},audioBox:{title:"Inds\xe6t Audio",file:"Inds\xe6t fra fil",url:"Inds\xe6t fra URL"},browser:{tags:"Tags",search:"S\xf8g"},caption:"Inds\xe6t beskrivelse",close:"Luk",submitButton:"Gennemf\xf8r",revertButton:"Gendan",proportion:"Bevar proportioner",basic:"Basis",left:"Venstre",right:"H\xf8jre",center:"Center",width:"Bredde",height:"H\xf8jde",size:"St\xf8rrelse",ratio:"Forhold"},controller:{edit:"Rediger",unlink:"Fjern link",remove:"Fjern",insertRowAbove:"Inds\xe6t r\xe6kke foroven",insertRowBelow:"Inds\xe6t r\xe6kke nedenfor",deleteRow:"Slet r\xe6kke",insertColumnBefore:"Inds\xe6t kolonne f\xf8r",insertColumnAfter:"Inds\xe6t kolonne efter",deleteColumn:"Slet kolonne",fixedColumnWidth:"Fast s\xf8jlebredde",resize100:"Forst\xf8r 100%",resize75:"Forst\xf8r 75%",resize50:"Forst\xf8r 50%",resize25:"Forst\xf8r 25%",autoSize:"Auto st\xf8rrelse",mirrorHorizontal:"Spejling, horisontal",mirrorVertical:"Spejling, vertikal",rotateLeft:"Roter til venstre",rotateRight:"Toter til h\xf8jre",maxSize:"Max st\xf8rrelse",minSize:"Min st\xf8rrelse",tableHeader:"Tabel overskrift",mergeCells:"Sammenl\xe6g celler (merge)",splitCells:"Opdel celler",HorizontalSplit:"Opdel horisontalt",VerticalSplit:"Opdel vertikalt"},menu:{spaced:"Brev Afstand",bordered:"Afgr\xe6nsningslinje",neon:"Neon",translucent:"Gennemsigtig",shadow:"Skygge",code:"Code"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"da",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},80043:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"de",toolbar:{default:"Standard",save:"Speichern",font:"Schriftart",formats:"Format",fontSize:"Schriftgr\xf6\xdfe",bold:"Fett",underline:"Unterstrichen",italic:"Kursiv",strike:"Durchgestrichen",subscript:"Tiefgestellt",superscript:"Hochgestellt",removeFormat:"Format entfernen",fontColor:"Schriftfarbe",hiliteColor:"Farbe f\xfcr Hervorhebungen",indent:"Einzug vergr\xf6\xdfern",outdent:"Einzug verkleinern",align:"Ausrichtung",alignLeft:"Links ausrichten",alignRight:"Rechts ausrichten",alignCenter:"Zentriert ausrichten",alignJustify:"Blocksatz",list:"Liste",orderList:"Nummerierte Liste",unorderList:"Aufz\xe4hlung",horizontalRule:"Horizontale Linie",hr_solid:"Strich",hr_dotted:"Gepunktet",hr_dashed:"Gestrichelt",table:"Tabelle",link:"Link",math:"Mathematik",image:"Bild",video:"Video",audio:"Audio",fullScreen:"Vollbild",showBlocks:"Blockformatierungen anzeigen",codeView:"Quelltext anzeigen",undo:"R\xfcckg\xe4ngig",redo:"Wiederholen",preview:"Vorschau",print:"Drucken",tag_p:"Absatz",tag_div:"Normal (DIV)",tag_h:"Header",tag_blockquote:"Zitat",tag_pre:"Quellcode",template:"Vorlage",lineHeight:"Zeilenh\xf6he",paragraphStyle:"Absatzstil",textStyle:"Textstil",imageGallery:"Bildergalerie",dir_ltr:"Links nach rechts",dir_rtl:"Rechts nach links",mention:"Erw\xe4hnen"},dialogBox:{linkBox:{title:"Link einf\xfcgen",url:"Link-URL",text:"Link-Text",newWindowCheck:"In neuem Fenster anzeigen",downloadLinkCheck:"Download-Link",bookmark:"Lesezeichen"},mathBox:{title:"Mathematik",inputLabel:"Mathematische Notation",fontSizeLabel:"Schriftgr\xf6\xdfe",previewLabel:"Vorschau"},imageBox:{title:"Bild einf\xfcgen",file:"Datei ausw\xe4hlen",url:"Bild-URL",altText:"Alternativer Text"},videoBox:{title:"Video einf\xfcgen",file:"Datei ausw\xe4hlen",url:"Video-URL, YouTube/Vimeo"},audioBox:{title:"Audio einf\xfcgen",file:"Datei ausw\xe4hlen",url:"Audio-URL"},browser:{tags:"Stichworte",search:"Suche"},caption:"Beschreibung eingeben",close:"Schlie\xdfen",submitButton:"\xdcbernehmen",revertButton:"R\xfcckg\xe4ngig",proportion:"Seitenverh\xe4ltnis beibehalten",basic:"Standard",left:"Links",right:"Rechts",center:"Zentriert",width:"Breite",height:"H\xf6he",size:"Gr\xf6\xdfe",ratio:"Verh\xe4ltnis"},controller:{edit:"Bearbeiten",unlink:"Link entfernen",remove:"L\xf6schen",insertRowAbove:"Zeile oberhalb einf\xfcgen",insertRowBelow:"Zeile unterhalb einf\xfcgen",deleteRow:"Zeile l\xf6schen",insertColumnBefore:"Spalte links einf\xfcgen",insertColumnAfter:"Spalte rechts einf\xfcgen",deleteColumn:"Spalte l\xf6schen",fixedColumnWidth:"Feste Spaltenbreite",resize100:"Zoom 100%",resize75:"Zoom 75%",resize50:"Zoom 50%",resize25:"Zoom 25%",autoSize:"Automatische Gr\xf6\xdfenanpassung",mirrorHorizontal:"Horizontal spiegeln",mirrorVertical:"Vertikal spiegeln",rotateLeft:"Nach links drehen",rotateRight:"Nach rechts drehen",maxSize:"Maximale Gr\xf6\xdfe",minSize:"Mindestgr\xf6\xdfe",tableHeader:"Tabellen\xfcberschrift",mergeCells:"Zellen verbinden",splitCells:"Zellen teilen",HorizontalSplit:"Horizontal teilen",VerticalSplit:"Vertikal teilen"},menu:{spaced:"Buchstabenabstand",bordered:"Umrandet",neon:"Neon",translucent:"Durchscheinend",shadow:"Schatten",code:"Quellcode"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"de",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},96775:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"en",toolbar:{default:"Default",save:"Save",font:"Font",formats:"Formats",fontSize:"Size",bold:"Bold",underline:"Underline",italic:"Italic",strike:"Strike",subscript:"Subscript",superscript:"Superscript",removeFormat:"Remove Format",fontColor:"Font Color",hiliteColor:"Highlight Color",indent:"Indent",outdent:"Outdent",align:"Align",alignLeft:"Align left",alignRight:"Align right",alignCenter:"Align center",alignJustify:"Align justify",list:"List",orderList:"Ordered list",unorderList:"Unordered list",horizontalRule:"Horizontal line",hr_solid:"Solid",hr_dotted:"Dotted",hr_dashed:"Dashed",table:"Table",link:"Link",math:"Math",image:"Image",video:"Video",audio:"Audio",fullScreen:"Full screen",showBlocks:"Show blocks",codeView:"Code view",undo:"Undo",redo:"Redo",preview:"Preview",print:"print",tag_p:"Paragraph",tag_div:"Normal (DIV)",tag_h:"Header",tag_blockquote:"Quote",tag_pre:"Code",template:"Template",lineHeight:"Line height",paragraphStyle:"Paragraph style",textStyle:"Text style",imageGallery:"Image gallery",dir_ltr:"Left to right",dir_rtl:"Right to left",mention:"Mention"},dialogBox:{linkBox:{title:"Insert Link",url:"URL to link",text:"Text to display",newWindowCheck:"Open in new window",downloadLinkCheck:"Download link",bookmark:"Bookmark"},mathBox:{title:"Math",inputLabel:"Mathematical Notation",fontSizeLabel:"Font Size",previewLabel:"Preview"},imageBox:{title:"Insert image",file:"Select from files",url:"Image URL",altText:"Alternative text"},videoBox:{title:"Insert Video",file:"Select from files",url:"Media embed URL, YouTube/Vimeo"},audioBox:{title:"Insert Audio",file:"Select from files",url:"Audio URL"},browser:{tags:"Tags",search:"Search"},caption:"Insert description",close:"Close",submitButton:"Submit",revertButton:"Revert",proportion:"Constrain proportions",basic:"Basic",left:"Left",right:"Right",center:"Center",width:"Width",height:"Height",size:"Size",ratio:"Ratio"},controller:{edit:"Edit",unlink:"Unlink",remove:"Remove",insertRowAbove:"Insert row above",insertRowBelow:"Insert row below",deleteRow:"Delete row",insertColumnBefore:"Insert column before",insertColumnAfter:"Insert column after",deleteColumn:"Delete column",fixedColumnWidth:"Fixed column width",resize100:"Resize 100%",resize75:"Resize 75%",resize50:"Resize 50%",resize25:"Resize 25%",autoSize:"Auto size",mirrorHorizontal:"Mirror, Horizontal",mirrorVertical:"Mirror, Vertical",rotateLeft:"Rotate left",rotateRight:"Rotate right",maxSize:"Max size",minSize:"Min size",tableHeader:"Table header",mergeCells:"Merge cells",splitCells:"Split Cells",HorizontalSplit:"Horizontal split",VerticalSplit:"Vertical split"},menu:{spaced:"Spaced",bordered:"Bordered",neon:"Neon",translucent:"Translucent",shadow:"Shadow",code:"Code"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"en",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},61288:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"es",toolbar:{default:"Valor por defecto",save:"Guardar",font:"Fuente",formats:"Formato",fontSize:"Tama\xf1o de fuente",bold:"Negrita",underline:"Subrayado",italic:"Cursiva",strike:"Tachado",subscript:"Sub\xedndice",superscript:"Super\xedndice",removeFormat:"Eliminar formato",fontColor:"Color de fuente",hiliteColor:"Color de resaltado",indent:"M\xe1s tabulaci\xf3n",outdent:"Menos tabulaci\xf3n",align:"Alinear",alignLeft:"Alinear a la izquierda",alignRight:"Alinear a la derecha",alignCenter:"Alinear al centro",alignJustify:"Justificar",list:"Lista",orderList:"Lista ordenada",unorderList:"Lista desordenada",horizontalRule:"Horizontal line",hr_solid:"L\xednea horizontal solida",hr_dotted:"L\xednea horizontal punteada",hr_dashed:"L\xednea horizontal discontinua",table:"Tabla",link:"Link",math:"Matem\xe1ticas",image:"Imagen",video:"Video",audio:"Audio",fullScreen:"Pantalla completa",showBlocks:"Ver bloques",codeView:"Ver c\xf3digo fuente",undo:"UndoDeshacer \xfaltima acci\xf3n",redo:"Rehacer \xfaltima acci\xf3n",preview:"Vista previa",print:"Imprimir",tag_p:"P\xe1rrafo",tag_div:"Normal (DIV)",tag_h:"Header",tag_blockquote:"Cita",tag_pre:"C\xf3digo",template:"Plantilla",lineHeight:"Altura de la l\xednea",paragraphStyle:"Estilo del parrafo",textStyle:"Estilo del texto",imageGallery:"Galer\xeda de im\xe1genes",dir_ltr:"De izquierda a derecha",dir_rtl:"De derecha a izquierda",mention:"Mencionar"},dialogBox:{linkBox:{title:"Insertar Link",url:"\xbfHacia que URL lleva el link?",text:"Texto para mostrar",newWindowCheck:"Abrir en una nueva ventana",downloadLinkCheck:"Enlace de descarga",bookmark:"Marcador"},mathBox:{title:"Matem\xe1ticas",inputLabel:"Notaci\xf3n Matem\xe1tica",fontSizeLabel:"Tama\xf1o de fuente",previewLabel:"Vista previa"},imageBox:{title:"Insertar imagen",file:"Seleccionar desde los archivos",url:"URL de la imagen",altText:"Texto alternativo"},videoBox:{title:"Insertar Video",file:"Seleccionar desde los archivos",url:"\xbfURL del v\xeddeo? Youtube/Vimeo"},audioBox:{title:"Insertar Audio",file:"Seleccionar desde los archivos",url:"URL de la audio"},browser:{tags:"Etiquetas",search:"Buscar"},caption:"Insertar descripci\xf3n",close:"Cerrar",submitButton:"Enviar",revertButton:"revertir",proportion:"Restringir las proporciones",basic:"Basico",left:"Izquierda",right:"derecha",center:"Centro",width:"Ancho",height:"Alto",size:"Tama\xf1o",ratio:"Proporci\xf3n"},controller:{edit:"Editar",unlink:"Desvincular",remove:"RemoveQuitar",insertRowAbove:"Insertar fila arriba",insertRowBelow:"Insertar fila debajo",deleteRow:"Eliminar fila",insertColumnBefore:"Insertar columna antes",insertColumnAfter:"Insertar columna despu\xe9s",deleteColumn:"Eliminar columna",fixedColumnWidth:"Ancho de columna fijo",resize100:"Redimensionar 100%",resize75:"Redimensionar 75%",resize50:"Redimensionar 50%",resize25:"Redimensionar 25%",autoSize:"Tama\xf1o autom\xe1tico",mirrorHorizontal:"Espejo, Horizontal",mirrorVertical:"Espejo, Vertical",rotateLeft:"Girar a la izquierda",rotateRight:"Girar a la derecha",maxSize:"Tama\xf1o m\xe1ximo",minSize:"Tama\xf1o min\xedmo",tableHeader:"Encabezado de tabla",mergeCells:"Combinar celdas",splitCells:"Dividir celdas",HorizontalSplit:"Divisi\xf3n horizontal",VerticalSplit:"Divisi\xf3n vertical"},menu:{spaced:"Espaciado",bordered:"Bordeado",neon:"Ne\xf3n",translucent:"Transl\xfacido",shadow:"Sombreado",code:"C\xf3digo"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"es",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},42058:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"fa",toolbar:{default:"پیش فرض",save:"ذخیره",font:"فونت",formats:"قالب‌ها",fontSize:"اندازه‌ی فونت",bold:"پررنگ کردن",underline:"زیرخطدار کردن",italic:"کج کردن",strike:"خط میان‌دار کردن",subscript:"نوشتن به صورت زیر متن",superscript:"نوشتن به صورت بالای متن",removeFormat:"حذف قالب",fontColor:"رنگ پیش زمینه",hiliteColor:"رنگ پس‌زمینه",indent:"جلو بردن",outdent:"عقب بردن",align:"چیدمان",alignLeft:"چپ‌چین",alignRight:"راست‌چین",alignCenter:"وسط‌چین",alignJustify:"همتراز از هر دو سمت",list:"لیست",orderList:"لیست شمارشی",unorderList:"لیست گلوله‌ای",horizontalRule:"درج خط افقی",hr_solid:"تو پر",hr_dotted:"نقطه‌چین",hr_dashed:"خط تیره",table:"درج جدول",link:"درج لینک",math:"درج فرمول ریاضی",image:"درج تصویر",video:"درج ویدئو",audio:"درج صوت",fullScreen:"تمام صفحه",showBlocks:"نمایش بلاک‌بندی",codeView:"مشاهده‌ی کُد HTML",undo:"برگرداندن تغییر",redo:"تکرار تغییر",preview:"پیش نمایش",print:"چاپ",tag_p:"پاراگراف",tag_div:"عادی (DIV)",tag_h:"هدر",tag_blockquote:"نقل قول",tag_pre:"کُد",template:"درج محتوا بر اساس الگو",lineHeight:"ارتفاع خط",paragraphStyle:"استایل پاراگراف",textStyle:"استایل متن",imageGallery:"گالری تصاویر",dir_ltr:"چپ به راست",dir_rtl:"راست به چپ",mention:"ذکر کردن"},dialogBox:{linkBox:{title:"درج  لینک",url:"آدرس لینک",text:"عنوان لینک",newWindowCheck:"در پنجره‌ی جدیدی باز شود",downloadLinkCheck:"لینک دانلود",bookmark:"نشان"},mathBox:{title:"فرمول ریاضی",inputLabel:"تعریف فرمول",fontSizeLabel:"اندازه‌ی فونت",previewLabel:"پیش نمایش"},imageBox:{title:"درج تصویر",file:"انتخاب فایل",url:"آدرس Url",altText:"متن جایگزین"},videoBox:{title:"درج ویدئو",file:"انتخاب فایل",url:"آدرس Url ویدئو, YouTube/Vimeo"},audioBox:{title:"درج صوت",file:"انتخاب فایل",url:"آدرس Url"},browser:{tags:"تگ‌ها",search:"جستجو"},caption:"توضیح",close:"بستن",submitButton:"درج",revertButton:"برگرداندن تغییرات",proportion:"محدودیت اندازه",basic:"چیدمان پیش فرض",left:"چپ",right:"راست",center:"وسط",width:"پهنا",height:"ارتفاع",size:"اندازه",ratio:"نسبت"},controller:{edit:"ویرایش",unlink:"حذف لینک",remove:"حذف",insertRowAbove:"درج سطر در بالا",insertRowBelow:"درج سطر در پایین",deleteRow:"حذف سطر",insertColumnBefore:"درج یک ستون به عقب",insertColumnAfter:"درج یک ستون در جلو",deleteColumn:"حذف ستون",fixedColumnWidth:"اندازه ستون ثابت",resize100:"اندازه‌ی 100%",resize75:"اندازه‌ی 75%",resize50:"اندازه‌ی 50%",resize25:"اندازه‌ی 25%",autoSize:"اندازه‌ی خودکار",mirrorHorizontal:"بر عکس کردن در جهت افقی",mirrorVertical:"بر عکس کردن در جهت عمودی",rotateLeft:"دوران به چپ",rotateRight:"دوران به راست",maxSize:"حداکثر اندازه",minSize:"حداقل اندازه",tableHeader:"هدر جدول",mergeCells:"ادغام خانه‌ها",splitCells:"تقسیم خانه به چند خانه",HorizontalSplit:"تقسیم در جهت افقی",VerticalSplit:"تقسیم در جهت عمودی"},menu:{spaced:"فضادار",bordered:"لبه‌دار",neon:"نئونی",translucent:"نیمه شفاف",shadow:"سایه",code:"کُد"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"fa",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},23147:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"fr",toolbar:{default:"D\xe9faut",save:"Sauvegarder",font:"Police",formats:"Formats",fontSize:"Taille",bold:"Gras",underline:"Soulign\xe9",italic:"Italique",strike:"Barr\xe9",subscript:"Indice",superscript:"Exposant",removeFormat:"Effacer le formatage",fontColor:"Couleur du texte",hiliteColor:"Couleur en arri\xe8re plan",indent:"Indenter",outdent:"D\xe9sindenter",align:"Alignement",alignLeft:"\xc0 gauche",alignRight:"\xc0 droite",alignCenter:"Centr\xe9",alignJustify:"Justifi\xe9",list:"Liste",orderList:"Ordonn\xe9e",unorderList:"Non-ordonn\xe9e",horizontalRule:"Ligne horizontale",hr_solid:"Solide",hr_dotted:"Points",hr_dashed:"Tirets",table:"Table",link:"Lien",math:"Math",image:"Image",video:"Video",audio:"Audio",fullScreen:"Plein \xe9cran",showBlocks:"Voir les blocs",codeView:"Voir le code",undo:"Annuler",redo:"R\xe9tablir",preview:"Pr\xe9visualiser",print:"Imprimer",tag_p:"Paragraphe",tag_div:"Normal (DIV)",tag_h:"Titre",tag_blockquote:"Citation",tag_pre:"Code",template:"Template",lineHeight:"Hauteur de la ligne",paragraphStyle:"Style de paragraphe",textStyle:"Style de texte",imageGallery:"Galerie d'images",dir_ltr:"De gauche \xe0 droite",dir_rtl:"De droite \xe0 gauche",mention:"Mention"},dialogBox:{linkBox:{title:"Ins\xe9rer un lien",url:"Adresse URL du lien",text:"Texte \xe0 afficher",newWindowCheck:"Ouvrir dans une nouvelle fen\xeatre",downloadLinkCheck:"Lien de t\xe9l\xe9chargement",bookmark:"Signet"},mathBox:{title:"Math",inputLabel:"Notation math\xe9matique",fontSizeLabel:"Taille",previewLabel:"Pr\xe9visualiser"},imageBox:{title:"Ins\xe9rer une image",file:"S\xe9lectionner le fichier",url:"Adresse URL du fichier",altText:"Texte Alternatif"},videoBox:{title:"Ins\xe9rer une vid\xe9o",file:"S\xe9lectionner le fichier",url:"URL d’int\xe9gration du m\xe9dia, YouTube/Vimeo"},audioBox:{title:"Ins\xe9rer un fichier audio",file:"S\xe9lectionner le fichier",url:"Adresse URL du fichier"},browser:{tags:"Mots cl\xe9s",search:"Chercher"},caption:"Ins\xe9rer une description",close:"Fermer",submitButton:"Appliquer",revertButton:"Revenir en arri\xe8re",proportion:"Maintenir le rapport hauteur/largeur",basic:"Basique",left:"Gauche",right:"Droite",center:"Centr\xe9",width:"Largeur",height:"Hauteur",size:"Taille",ratio:"Rapport"},controller:{edit:"Modifier",unlink:"Supprimer un lien",remove:"Effacer",insertRowAbove:"Ins\xe9rer une ligne en dessous",insertRowBelow:"Ins\xe9rer une ligne au dessus",deleteRow:"Effacer la ligne",insertColumnBefore:"Ins\xe9rer une colonne avant",insertColumnAfter:"Ins\xe9rer une colonne apr\xe8s",deleteColumn:"Effacer la colonne",fixedColumnWidth:"Largeur de colonne fixe",resize100:"Redimensionner \xe0 100%",resize75:"Redimensionner \xe0 75%",resize50:"Redimensionner \xe0 50%",resize25:"Redimensionner \xe0 25%",autoSize:"Taille automatique",mirrorHorizontal:"Mirroir, Horizontal",mirrorVertical:"Mirroir, Vertical",rotateLeft:"Rotation \xe0 gauche",rotateRight:"Rotation \xe0 droite",maxSize:"Taille max",minSize:"Taille min",tableHeader:"En-t\xeate de table",mergeCells:"Fusionner les cellules",splitCells:"Diviser les Cellules",HorizontalSplit:"Scission horizontale",VerticalSplit:"Scission verticale"},menu:{spaced:"Espacement",bordered:"Ligne de d\xe9marcation",neon:"N\xe9on",translucent:"Translucide",shadow:"Ombre",code:"Code"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"fr",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},38210:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"he",toolbar:{default:"ברירת מחדל",save:"שמור",font:"גופן",formats:"עיצוב",fontSize:"גודל",bold:"מודגש",underline:"קו תחתון",italic:"נטוי",strike:"קו חוצה",subscript:"עילי",superscript:"תחתי",removeFormat:"הסר עיצוב",fontColor:"צבע גופן",hiliteColor:"צבע קו תחתון",indent:"הגדל כניסה",outdent:"הקטן כניסה",align:"יישור",alignLeft:"יישר לשמאל",alignRight:"יישר לימין",alignCenter:"מרכז",alignJustify:"יישר לשני הצדדים",list:"רשימה",orderList:"מספור",unorderList:"תבליטים",horizontalRule:"קו אופקי",hr_solid:"קו",hr_dotted:"נקודות",hr_dashed:"מקפים",table:"טבלה",link:"קישור",math:"מתמטיקה",image:"תמונה",video:"חוזי",audio:"שמע",fullScreen:"מסך מלא",showBlocks:"הצג גושים",codeView:"הצג קוד",undo:"בטל",redo:"חזור",preview:"תצוגה מקדימה",print:"הדפס",tag_p:"פסקה",tag_div:"רגילה (DIV)",tag_h:"כותרת",tag_blockquote:"ציטוט",tag_pre:"קוד",template:"תבנית",lineHeight:"גובה השורה",paragraphStyle:"סגנון פסקה",textStyle:"סגנון גופן",imageGallery:"גלרית תמונות",dir_ltr:"משמאל לימין",dir_rtl:"מימין לשמאל",mention:"הזכר"},dialogBox:{linkBox:{title:"הכנס קשור",url:"כתובת קשור",text:"תיאור",newWindowCheck:"פתח בחלון חדש",downloadLinkCheck:"קישור להורדה",bookmark:"סמניה"},mathBox:{title:"נוסחה",inputLabel:"סימנים מתמטים",fontSizeLabel:"גודל גופן",previewLabel:"תצוגה מקדימה"},imageBox:{title:"הכנס תמונה",file:"בחר מקובץ",url:"כתובת URL תמונה",altText:"תיאור (תגית alt)"},videoBox:{title:"הכנס סרטון",file:"בחר מקובץ",url:"כתובת הטמעה YouTube/Vimeo"},audioBox:{title:"הכנס שמע",file:"בחר מקובץ",url:"כתובת URL שמע"},browser:{tags:"תג",search:"חפש"},caption:"הכנס תיאור",close:"סגור",submitButton:"שלח",revertButton:"בטל",proportion:"שמר יחס",basic:"בסיסי",left:"שמאל",right:"ימין",center:"מרכז",width:"רוחב",height:"גובה",size:"גודל",ratio:"יחס"},controller:{edit:"ערוך",unlink:"הסר קישורים",remove:"הסר",insertRowAbove:"הכנס שורה מעל",insertRowBelow:"הכנס שורה מתחת",deleteRow:"מחק שורה",insertColumnBefore:"הכנס עמודה לפני",insertColumnAfter:"הכנס עמודה אחרי",deleteColumn:"מחק עמודה",fixedColumnWidth:"קבע רוחב עמודות",resize100:"ללא הקטנה",resize75:"הקטן 75%",resize50:"הקטן 50%",resize25:"הקטן 25%",autoSize:"הקטן אוטומטית",mirrorHorizontal:"הפוך לרוחב",mirrorVertical:"הפוך לגובה",rotateLeft:"סובב שמאלה",rotateRight:"סובב ימינה",maxSize:"גודל מרבי",minSize:"גודל מזערי",tableHeader:"כותרת טבלה",mergeCells:"מזג תאים",splitCells:"פצל תא",HorizontalSplit:"פצל לגובה",VerticalSplit:"פצל לרוחב"},menu:{spaced:"מרווח",bordered:"בעל מיתאר",neon:"זוהר",translucent:"שקוף למחצה",shadow:"צל",code:"קוד"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"he",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},10200:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"hu",toolbar:{default:"Alap\xe9rtelmezett",save:"Ment\xe9s",font:"Betűt\xedpus",formats:"Form\xe1z\xe1s",fontSize:"Betűm\xe9ret",bold:"F\xe9lk\xf6v\xe9r",underline:"Al\xe1h\xfazott",italic:"Dőlt",strike:"\xc1th\xfazott",subscript:"Als\xf3 index",superscript:"Felső index",removeFormat:"Form\xe1z\xe1s t\xf6rl\xe9se",fontColor:"Betűsz\xedn",hiliteColor:"H\xe1tt\xe9rsz\xedn",indent:"Beh\xfaz\xe1s n\xf6vel\xe9se",outdent:"Beh\xfaz\xe1s cs\xf6kkent\xe9se",align:"Igaz\xedt\xe1s",alignLeft:"Balra igaz\xedt\xe1s",alignRight:"Jobbra igaz\xedt\xe1s",alignCenter:"K\xf6z\xe9pre igaz\xedt\xe1s",alignJustify:"Sorkiz\xe1rt",list:"Lista",orderList:"Sz\xe1mozott lista",unorderList:"Sz\xe1mozatlan lista",horizontalRule:"Elv\xe1laszt\xf3",hr_solid:"Folytonos",hr_dotted:"Pontozott",hr_dashed:"Szaggatott",table:"T\xe1bl\xe1zat",link:"Hivatkoz\xe1s",math:"Matematika",image:"K\xe9p",video:"Vide\xf3",audio:"Hang",fullScreen:"Teljes k\xe9pernyő",showBlocks:"Blokkok megjelen\xedt\xe9se",codeView:"Forr\xe1sk\xf3d n\xe9zet",undo:"Visszavon\xe1s",redo:"Visszavon\xe1s visszavon\xe1sa",preview:"Előn\xe9zet",print:"Nyomtat\xe1s",tag_p:"Bekezd\xe9s",tag_div:"Norm\xe1l (DIV)",tag_h:"Fejl\xe9c",tag_blockquote:"Id\xe9zet",tag_pre:"K\xf3d",template:"Minta",lineHeight:"Sormagass\xe1g",paragraphStyle:"Bekezd\xe9sst\xedlus",textStyle:"Karakterst\xedlus",imageGallery:"K\xe9pgall\xe9ria",dir_ltr:"Balr\xf3l jobbra",dir_rtl:"Jobbr\xf3l balra",mention:"Eml\xedt\xe9s"},dialogBox:{linkBox:{title:"Link besz\xfar\xe1sa",url:"URL",text:"Megjelen\xedtett sz\xf6veg",newWindowCheck:"Megnyit\xe1s \xfaj ablakban",downloadLinkCheck:"Let\xf6lt\xe9si hivatkoz\xe1s",bookmark:"K\xf6nyvjelző"},mathBox:{title:"Matematika",inputLabel:"Matematikai jel\xf6l\xe9sek",fontSizeLabel:"Betűm\xe9ret",previewLabel:"Előn\xe9zet"},imageBox:{title:"K\xe9p besz\xfar\xe1sa",file:"F\xe1jlfelt\xf6lt\xe9s",url:"K\xe9phivatkoz\xe1s",altText:"Alternat\xedv sz\xf6veg"},videoBox:{title:"Vide\xf3 besz\xfar\xe1sa",file:"F\xe1jlfelt\xf6lt\xe9s",url:"Be\xe1gyazhat\xf3 URL, YouTube/Vimeo"},audioBox:{title:"Hang besz\xfar\xe1sa",file:"F\xe1jlfelt\xf6lt\xe9s",url:"Hang URL"},browser:{tags:"C\xedmk\xe9k",search:"Keres\xe9s"},caption:"K\xe9pal\xe1\xedr\xe1s",close:"Bez\xe1r\xe1s",submitButton:"K\xfcld\xe9s",revertButton:"M\xe9gse",proportion:"M\xe9retkorl\xe1tok",basic:"Alapszintű",left:"Balra",right:"Jobbra",center:"K\xf6z\xe9pre",width:"Sz\xe9less\xe9g",height:"Magass\xe1g",size:"M\xe9ret",ratio:"K\xe9par\xe1ny"},controller:{edit:"Szerkeszt\xe9s",unlink:"Link elt\xe1vol\xedt\xe1sa",remove:"T\xf6rl\xe9s",insertRowAbove:"\xdaj sor f\xf6l\xf6tte",insertRowBelow:"\xdaj sor alatta",deleteRow:"Sor t\xf6rl\xe9se",insertColumnBefore:"\xdaj oszlop balra",insertColumnAfter:"\xdaj oszlop jobbra",deleteColumn:"Oszlop t\xf6rl\xe9se",fixedColumnWidth:"R\xf6gz\xedtett oszlopsz\xe9less\xe9g",resize100:"\xc1tm\xe9retez\xe9s: 100%",resize75:"\xc1tm\xe9retez\xe9s: 75%",resize50:"\xc1tm\xe9retez\xe9s: 50%",resize25:"\xc1tm\xe9retez\xe9s: 25%",autoSize:"Automatikus m\xe9ret",mirrorHorizontal:"V\xedzszintes t\xfckr\xf6z\xe9s",mirrorVertical:"F\xfcggőleges t\xfckr\xf6z\xe9s",rotateLeft:"Forgat\xe1s balra",rotateRight:"Forgat\xe1s jobbra",maxSize:"Maxim\xe1lis m\xe9ret",minSize:"Minim\xe1lis m\xe9ret",tableHeader:"T\xe1bl\xe1zatfejl\xe9c",mergeCells:"Cell\xe1k egyes\xedt\xe9se",splitCells:"Cell\xe1k sz\xe9tv\xe1laszt\xe1sa",HorizontalSplit:"Sz\xe9tv\xe1laszt\xe1s v\xedzszintesen",VerticalSplit:"Sz\xe9tv\xe1laszt\xe1s f\xfcggőlegesen"},menu:{spaced:"Ritk\xedtott",bordered:"Keretezett",neon:"Neon",translucent:"\xc1ttetsző",shadow:"\xc1rny\xe9k",code:"K\xf3d"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"hu",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},47905:function(e,t,i){"use strict";i.r(t),i.d(t,{ckb:function(){return n.a},cs:function(){return a.a},da:function(){return s.a},de:function(){return c.a},en:function(){return h.a},es:function(){return g.a},fr:function(){return f.a},he:function(){return v.a},hu:function(){return w.a},it:function(){return x.a},ja:function(){return k.a},ko:function(){return z.a},lv:function(){return E.a},nl:function(){return B.a},pl:function(){return T.a},pt_br:function(){return M.a},ro:function(){return H.a},ru:function(){return D.a},se:function(){return U.a},ua:function(){return F.a},ur:function(){return j.a},zh_cn:function(){return q.a}});var l=i(9706),n=i.n(l),o=i(79089),a=i.n(o),r=i(83138),s=i.n(r),u=i(80043),c=i.n(u),d=i(96775),h=i.n(d),p=i(61288),g=i.n(p),m=i(23147),f=i.n(m),b=i(38210),v=i.n(b),_=i(10200),w=i.n(_),y=i(47758),x=i.n(y),C=i(36473),k=i.n(C),S=i(7747),z=i.n(S),L=i(17242),E=i.n(L),A=i(16833),B=i.n(A),N=i(37370),T=i.n(N),I=i(86548),M=i.n(I),R=i(20319),H=i.n(R),V=i(659),D=i.n(V),O=i(9296),U=i.n(O),P=i(34699),F=i.n(P),Z=i(9870),j=i.n(Z),W=i(23428),q=i.n(W);t.default={ckb:n(),cs:a(),da:s(),de:c(),en:h(),es:g(),fr:f(),he:v(),hu:w(),it:x(),ja:k(),ko:z(),lv:E(),nl:B(),pl:T(),pt_br:M(),ro:H(),ru:D(),se:U(),ua:F(),ur:j(),zh_cn:q()}},47758:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"it",toolbar:{default:"Predefinita",save:"Salva",font:"Font",formats:"Formato",fontSize:"Grandezza",bold:"Grassetto",underline:"Sottolineato",italic:"Corsivo",strike:"Barrato",subscript:"Apice",superscript:"Pedice",removeFormat:"Rimuovi formattazione",fontColor:"Colore testo",hiliteColor:"Colore sottolineatura",indent:"Aumenta rientro",outdent:"Riduci rientro",align:"Allinea",alignLeft:"Allinea a sinistra",alignRight:"Allinea a destra",alignCenter:"Allinea al centro",alignJustify:"Giustifica testo",list:"Elenco",orderList:"Elenco numerato",unorderList:"Elenco puntato",horizontalRule:"Linea orizzontale",hr_solid:"Linea continua",hr_dotted:"Puntini",hr_dashed:"Trattini",table:"Tabella",link:"Collegamento ipertestuale",math:"Formula matematica",image:"Immagine",video:"Video",audio:"Audio",fullScreen:"A tutto schermo",showBlocks:"Visualizza blocchi",codeView:"Visualizza codice",undo:"Annulla",redo:"Ripristina",preview:"Anteprima",print:"Stampa",tag_p:"Paragrafo",tag_div:"Normale (DIV)",tag_h:"Titolo",tag_blockquote:"Citazione",tag_pre:"Codice",template:"Modello",lineHeight:"Interlinea",paragraphStyle:"Stile paragrafo",textStyle:"Stile testo",imageGallery:"Galleria di immagini",dir_ltr:"Da sinistra a destra",dir_rtl:"Da destra a sinistra",mention:"Menzione"},dialogBox:{linkBox:{title:"Inserisci un link",url:"Indirizzo",text:"Testo da visualizzare",newWindowCheck:"Apri in una nuova finestra",downloadLinkCheck:"Link per scaricare",bookmark:"Segnalibro"},mathBox:{title:"Matematica",inputLabel:"Notazione matematica",fontSizeLabel:"Grandezza testo",previewLabel:"Anteprima"},imageBox:{title:"Inserisci immagine",file:"Seleziona da file",url:"Indirizzo immagine",altText:"Testo alternativo (ALT)"},videoBox:{title:"Inserisci video",file:"Seleziona da file",url:"Indirizzo video di embed, YouTube/Vimeo"},audioBox:{title:"Inserisci audio",file:"Seleziona da file",url:"Indirizzo audio"},browser:{tags:"tag",search:"Ricerca"},caption:"Inserisci didascalia",close:"Chiudi",submitButton:"Invia",revertButton:"Annulla",proportion:"Proporzionale",basic:"Da impostazione",left:"Sinistra",right:"Destra",center:"Centrato",width:"Larghezza",height:"Altezza",size:"Dimensioni",ratio:"Rapporto"},controller:{edit:"Modifica",unlink:"Elimina link",remove:"Rimuovi",insertRowAbove:"Inserisci riga sopra",insertRowBelow:"Inserisci riga sotto",deleteRow:"Cancella riga",insertColumnBefore:"Inserisci colonna prima",insertColumnAfter:"Inserisci colonna dopo",deleteColumn:"Cancella colonna",fixedColumnWidth:"Larghezza delle colonne fissa",resize100:"Ridimensiona 100%",resize75:"Ridimensiona 75%",resize50:"Ridimensiona 50%",resize25:"Ridimensiona 25%",autoSize:"Ridimensione automatica",mirrorHorizontal:"Capovolgi orizzontalmente",mirrorVertical:"Capovolgi verticalmente",rotateLeft:"Ruota a sinistra",rotateRight:"Ruota a destra",maxSize:"Dimensione massima",minSize:"Dimensione minima",tableHeader:"Intestazione tabella",mergeCells:"Unisci celle",splitCells:"Dividi celle",HorizontalSplit:"Separa orizontalmente",VerticalSplit:"Separa verticalmente"},menu:{spaced:"Spaziato",bordered:"Bordato",neon:"Luminoso",translucent:"Traslucido",shadow:"Ombra",code:"Codice"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"it",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG una finestra con un documento");return i(e)}:i(t)},36473:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"ja",toolbar:{default:"デフォルト",save:"保存",font:"フォント",formats:"段落形式",fontSize:"サイズ",bold:"太字",underline:"下線",italic:"イタリック",strike:"取り消し線",subscript:"下付き",superscript:"上付き",removeFormat:"形式を削除",fontColor:"文字色",hiliteColor:"文字の背景色",indent:"インデント",outdent:"インデント",align:"ソート",alignLeft:"左揃え",alignRight:"右揃え",alignCenter:"中央揃え",alignJustify:"両端揃え",list:"リスト",orderList:"数値ブリット",unorderList:"円形ブリット",horizontalRule:"水平線を挿入",hr_solid:"実線",hr_dotted:"点線",hr_dashed:"ダッシュ",table:"テーブル",link:"リンク",math:"数学",image:"画像",video:"動画",audio:"オーディオ",fullScreen:"フルスクリーン",showBlocks:"ブロック表示",codeView:"HTMLの編集",undo:"元に戻す",redo:"再実行",preview:"プレビュー",print:"印刷",tag_p:"本文",tag_div:"基本（DIV）",tag_h:"タイトル",tag_blockquote:"引用",tag_pre:"コード",template:"テンプレート",lineHeight:"行の高さ",paragraphStyle:"段落スタイル",textStyle:"テキストスタイル",imageGallery:"イメージギャラリー",dir_ltr:"左から右へ",dir_rtl:"右から左に",mention:"言及する"},dialogBox:{linkBox:{title:"リンクの挿入",url:"インターネットアドレス",text:"画面のテキスト",newWindowCheck:"別ウィンドウで開く",downloadLinkCheck:"ダウンロードリンク",bookmark:"ブックマーク"},mathBox:{title:"数学",inputLabel:"数学表記",fontSizeLabel:"サイズ",previewLabel:"プレビュー"},imageBox:{title:"画像の挿入",file:"ファイルの選択",url:"イメージアドレス",altText:"置換文字列"},videoBox:{title:"動画を挿入",file:"ファイルの選択",url:"メディア埋め込みアドレス, YouTube/Vimeo"},audioBox:{title:"オーディオを挿入",file:"ファイルの選択",url:"オーディオアドレス"},browser:{tags:"タグ",search:"探す"},caption:"説明付け",close:"閉じる",submitButton:"確認",revertButton:"元に戻す",proportion:"の割合カスタマイズ",basic:"基本",left:"左",right:"右",center:"中央",width:"横",height:"縦",size:"サイズ",ratio:"比率"},controller:{edit:"編集",unlink:"リンク解除",remove:"削除",insertRowAbove:"上に行を挿入",insertRowBelow:"下に行を挿入",deleteRow:"行の削除",insertColumnBefore:"左に列を挿入",insertColumnAfter:"右に列を挿入",deleteColumn:"列を削除する",fixedColumnWidth:"固定列幅",resize100:"100％ サイズ",resize75:"75％ サイズ",resize50:"50％ サイズ",resize25:"25％ サイズ",autoSize:"自動サイズ",mirrorHorizontal:"左右反転",mirrorVertical:"上下反転",rotateLeft:"左に回転",rotateRight:"右に回転",maxSize:"最大サイズ",minSize:"最小サイズ",tableHeader:"表のヘッダー",mergeCells:"セルの結合",splitCells:"セルを分割",HorizontalSplit:"横分割",VerticalSplit:"垂直分割"},menu:{spaced:"文字間隔",bordered:"境界線",neon:"ネオン",translucent:"半透明",shadow:"影",code:"コード"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"ja",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},7747:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"ko",toolbar:{default:"기본값",save:"저장",font:"글꼴",formats:"문단 형식",fontSize:"크기",bold:"굵게",underline:"밑줄",italic:"기울임",strike:"취소선",subscript:"아래 첨자",superscript:"위 첨자",removeFormat:"형식 제거",fontColor:"글자색",hiliteColor:"배경색",indent:"들여쓰기",outdent:"내어쓰기",align:"정렬",alignLeft:"왼쪽 정렬",alignRight:"오른쪽 정렬",alignCenter:"가운데 정렬",alignJustify:"양쪽 정렬",list:"리스트",orderList:"숫자형 리스트",unorderList:"원형 리스트",horizontalRule:"가로 줄 삽입",hr_solid:"실선",hr_dotted:"점선",hr_dashed:"대시",table:"테이블",link:"링크",math:"수식",image:"이미지",video:"동영상",audio:"오디오",fullScreen:"전체 화면",showBlocks:"블록 보기",codeView:"HTML 편집",undo:"실행 취소",redo:"다시 실행",preview:"미리보기",print:"인쇄",tag_p:"본문",tag_div:"기본 (DIV)",tag_h:"제목",tag_blockquote:"인용문",tag_pre:"코드",template:"템플릿",lineHeight:"줄 높이",paragraphStyle:"문단 스타일",textStyle:"글자 스타일",imageGallery:"이미지 갤러리",dir_ltr:"왼쪽에서 오른쪽",dir_rtl:"오른쪽에서 왼쪽",mention:"멘션"},dialogBox:{linkBox:{title:"링크 삽입",url:"인터넷 주소",text:"화면 텍스트",newWindowCheck:"새창으로 열기",downloadLinkCheck:"다운로드 링크",bookmark:"북마크"},mathBox:{title:"수식",inputLabel:"수학적 표기법",fontSizeLabel:"글자 크기",previewLabel:"미리보기"},imageBox:{title:"이미지 삽입",file:"파일 선택",url:"이미지 주소",altText:"대체 문자열"},videoBox:{title:"동영상 삽입",file:"파일 선택",url:"미디어 임베드 주소, 유튜브/비메오"},audioBox:{title:"오디오 삽입",file:"파일 선택",url:"오디오 파일 주소"},browser:{tags:"태그",search:"검색"},caption:"설명 넣기",close:"닫기",submitButton:"확인",revertButton:"되돌리기",proportion:"비율 맞춤",basic:"기본",left:"왼쪽",right:"오른쪽",center:"가운데",width:"가로",height:"세로",size:"크기",ratio:"비율"},controller:{edit:"편집",unlink:"링크 해제",remove:"삭제",insertRowAbove:"위에 행 삽입",insertRowBelow:"아래에 행 삽입",deleteRow:"행 삭제",insertColumnBefore:"왼쪽에 열 삽입",insertColumnAfter:"오른쪽에 열 삽입",deleteColumn:"열 삭제",fixedColumnWidth:"고정 된 열 너비",resize100:"100% 크기",resize75:"75% 크기",resize50:"50% 크기",resize25:"25% 크기",autoSize:"자동 크기",mirrorHorizontal:"좌우 반전",mirrorVertical:"상하 반전",rotateLeft:"왼쪽으로 회전",rotateRight:"오른쪽으로 회전",maxSize:"최대화",minSize:"최소화",tableHeader:"테이블 제목",mergeCells:"셀 병합",splitCells:"셀 분할",HorizontalSplit:"가로 분할",VerticalSplit:"세로 분할"},menu:{spaced:"글자 간격",bordered:"경계선",neon:"네온",translucent:"반투명",shadow:"그림자",code:"코드"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"ko",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},17242:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"lv",toolbar:{default:"Noklusējuma",save:"Saglabāt",font:"Fonts",formats:"Formāti",fontSize:"Fonta lielums",bold:"Treknraksts",underline:"Pasvītrot",italic:"Slīpraksts",strike:"Pārsvītrojums",subscript:"Apakšraksts",superscript:"Augšraksts",removeFormat:"Noņemt formātu",fontColor:"Fonta krāsa",hiliteColor:"Teksta iezīmēšanas krāsa",indent:"Palielināt atkāpi",outdent:"Samazināt atkāpi",align:"Izlīdzināt",alignLeft:"Līdzināt pa kreisi",alignRight:"Līdzināt pa labi",alignCenter:"Centrēt",alignJustify:"Taisnot",list:"Saraksts",orderList:"Numerācija",unorderList:"Aizzimes",horizontalRule:"Horizontāla līnija",hr_solid:"Ciets",hr_dotted:"Punktiņš",hr_dashed:"Braša",table:"Tabula",link:"Saite",math:"Matemātika",image:"Attēls",video:"Video",audio:"Audio",fullScreen:"Pilnekrāna režīms",showBlocks:"Parādit blokus",codeView:"Koda skats",undo:"Atsaukt",redo:"Atkārtot",preview:"Priekšskatījums",print:"Drukāt",tag_p:"Paragrāfs",tag_div:"Normāli (DIV)",tag_h:"Galvene",tag_blockquote:"Citāts",tag_pre:"Kods",template:"Veidne",lineHeight:"Līnijas augstums",paragraphStyle:"Paragrāfa stils",textStyle:"Teksta stils",imageGallery:"Attēlu galerija",dir_ltr:"No kreisās uz labo",dir_rtl:"No labās uz kreiso",mention:"Pieminēt"},dialogBox:{linkBox:{title:"Ievietot saiti",url:"Saites URL",text:"Parādāmais teksts",newWindowCheck:"Atvērt jaunā logā",downloadLinkCheck:"Lejupielādes saite",bookmark:"Grāmatzīme"},mathBox:{title:"Matemātika",inputLabel:"Matemātiskā notācija",fontSizeLabel:"Fonta lielums",previewLabel:"Priekšskatījums"},imageBox:{title:"Ievietot attēlu",file:"Izvēlieties no failiem",url:"Attēla URL",altText:"Alternatīvs teksts"},videoBox:{title:"Ievietot video",file:"Izvēlieties no failiem",url:"Multivides iegulšanas URL, YouTube/Vimeo"},audioBox:{title:"Ievietot audio",file:"Izvēlieties no failiem",url:"Audio URL"},browser:{tags:"Tagi",search:"Meklēt"},caption:"Ievietot aprakstu",close:"Aizvērt",submitButton:"Iesniegt",revertButton:"Atjaunot",proportion:"Ierobežo proporcijas",basic:"Nav iesaiņojuma",left:"Pa kreisi",right:"Labajā pusē",center:"Centrs",width:"Platums",height:"Augstums",size:"Izmērs",ratio:"Attiecība"},controller:{edit:"Rediģēt",unlink:"Atsaistīt",remove:"Noņemt",insertRowAbove:"Ievietot rindu virs",insertRowBelow:"Ievietot rindu zemāk",deleteRow:"Dzēst rindu",insertColumnBefore:"Ievietot kolonnu pirms",insertColumnAfter:"Ievietot kolonnu aiz",deleteColumn:"Dzēst kolonnu",fixColumnWidth:"Fiksēts kolonnas platums",resize100:"Mainīt izmēru 100%",resize75:"Mainīt izmēru 75%",resize50:"Mainīt izmēru 50%",resize25:"Mainīt izmēru 25%",autoSize:"Automātiskais izmērs",mirrorHorizontal:"Spogulis, horizontāls",mirrorVertical:"Spogulis, vertikāls",rotateLeft:"Pagriezt pa kreisi",rotateRight:"Pagriezt pa labi",maxSize:"Maksimālais izmērs",minSize:"Minimālais izmērs",tableHeader:"Tabulas galvene",mergeCells:"Apvienot šūnas",splitCells:"Sadalīt šūnas",HorizontalSplit:"Horizontāls sadalījums",VerticalSplit:"Vertikāls sadalījums"},menu:{spaced:"Ar atstarpi",bordered:"Robežojās",neon:"Neona",translucent:"Caurspīdīgs",shadow:"Ēna",code:"Kods"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"lv",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},16833:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"nl",toolbar:{default:"Standaard",save:"Opslaan",font:"Lettertype",formats:"Formaten",fontSize:"Lettergrootte",bold:"Vetgedrukt",underline:"Onderstrepen",italic:"Cursief",strike:"Doorstrepen",subscript:"Subscript",superscript:"Superscript",removeFormat:"Opmaak verwijderen",fontColor:"Tekstkleur",hiliteColor:"Tekst markeren",indent:"Inspringen",outdent:"Inspringen ongedaan maken",align:"Uitlijnen",alignLeft:"Links uitlijnen",alignRight:"Rechts uitlijnen",alignCenter:"In het midden uitlijnen",alignJustify:"Uitvullen",list:"Lijst",orderList:"Geordende lijst",unorderList:"Ongeordende lijst",horizontalRule:"Horizontale regel",hr_solid:"Standaard",hr_dotted:"Gestippeld",hr_dashed:"Gestreept",table:"Tabel",link:"Link",math:"Wiskunde",image:"Afbeelding",video:"Video",audio:"Audio",fullScreen:"Volledig scherm",showBlocks:"Blokken tonen",codeView:"Broncode weergeven",undo:"Ongedaan maken",redo:"Ongedaan maken herstellen",preview:"Voorbeeldweergave",print:"Printen",tag_p:"Alinea",tag_div:"Normaal (div)",tag_h:"Kop",tag_blockquote:"Citaat",tag_pre:"Code",template:"Sjabloon",lineHeight:"Lijnhoogte",paragraphStyle:"Alineastijl",textStyle:"Tekststijl",imageGallery:"Galerij",dir_ltr:"Van links naar rechts",dir_rtl:"Rechts naar links",mention:"Vermelding"},dialogBox:{linkBox:{title:"Link invoegen",url:"URL",text:"Tekst van de link",newWindowCheck:"In een nieuw tabblad openen",downloadLinkCheck:"Downloadlink",bookmark:"Bladwijzer"},mathBox:{title:"Wiskunde",inputLabel:"Wiskundige notatie",fontSizeLabel:"Lettergrootte",previewLabel:"Voorbeeld"},imageBox:{title:"Afbeelding invoegen",file:"Selecteer een bestand van uw apparaat",url:"URL",altText:"Alt-tekst"},videoBox:{title:"Video invoegen",file:"Selecteer een bestand van uw apparaat",url:"Embedded URL (YouTube/Vimeo)"},audioBox:{title:"Audio invoegen",file:"Selecteer een bestand van uw apparaat",url:"URL"},browser:{tags:"Tags",search:"Zoeken"},caption:"Omschrijving toevoegen",close:"Sluiten",submitButton:"Toepassen",revertButton:"Standaardwaarden herstellen",proportion:"Verhouding behouden",basic:"Standaard",left:"Links",right:"Rechts",center:"Midden",width:"Breedte",height:"Hoogte",size:"Grootte",ratio:"Verhouding"},controller:{edit:"Bewerken",unlink:"Ontkoppelen",remove:"Verwijderen",insertRowAbove:"Rij hierboven invoegen",insertRowBelow:"Rij hieronder invoegen",deleteRow:"Rij verwijderen",insertColumnBefore:"Kolom links invoegen",insertColumnAfter:"Kolom rechts invoegen",deleteColumn:"Kolom verwijderen",fixedColumnWidth:"Vaste kolombreedte",resize100:"Formaat wijzigen: 100%",resize75:"Formaat wijzigen: 75%",resize50:"Formaat wijzigen: 50%",resize25:"Formaat wijzigen: 25%",autoSize:"Automatische grootte",mirrorHorizontal:"Horizontaal spiegelen",mirrorVertical:"Verticaal spiegelen",rotateLeft:"Naar links draaien",rotateRight:"Naar rechts draaien",maxSize:"Maximale grootte",minSize:"Minimale grootte",tableHeader:"Tabelkoppen",mergeCells:"Cellen samenvoegen",splitCells:"Cellen splitsen",HorizontalSplit:"Horizontaal splitsen",VerticalSplit:"Verticaal splitsen"},menu:{spaced:"Uit elkaar",bordered:"Omlijnd",neon:"Neon",translucent:"Doorschijnend",shadow:"Schaduw",code:"Code"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"nl",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},37370:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"pl",toolbar:{default:"Domyślne",save:"Zapisz",font:"Czcionka",formats:"Formaty",fontSize:"Rozmiar",bold:"Pogrubienie",underline:"Podkreślenie",italic:"Kursywa",strike:"Przekreślenie",subscript:"Indeks dolny",superscript:"Indeks g\xf3rny",removeFormat:"Wyczyść formatowanie",fontColor:"Kolor tekstu",hiliteColor:"Kolor tła tekstu",indent:"Zwiększ wcięcie",outdent:"Zmniejsz wcięcie",align:"Wyr\xf3wnaj",alignLeft:"Do lewej",alignRight:"Do prawej",alignCenter:"Do środka",alignJustify:"Wyjustuj",list:"Lista",orderList:"Lista numerowana",unorderList:"Lista wypunktowana",horizontalRule:"Pozioma linia",hr_solid:"Ciągła",hr_dotted:"Kropkowana",hr_dashed:"Przerywana",table:"Tabela",link:"Odnośnik",math:"Matematyczne",image:"Obraz",video:"Wideo",audio:"Audio",fullScreen:"Pełny ekran",showBlocks:"Pokaż bloki",codeView:"Widok kodu",undo:"Cofnij",redo:"Pon\xf3w",preview:"Podgląd",print:"Drukuj",tag_p:"Akapit",tag_div:"Blok (DIV)",tag_h:"Nagł\xf3wek H",tag_blockquote:"Cytat",tag_pre:"Kod",template:"Szablon",lineHeight:"Odstęp między wierszami",paragraphStyle:"Styl akapitu",textStyle:"Styl tekstu",imageGallery:"Galeria obraz\xf3w",dir_ltr:"Od lewej do prawej",dir_rtl:"Od prawej do lewej",mention:"Wzmianka"},dialogBox:{linkBox:{title:"Wstaw odnośnik",url:"Adres URL",text:"Tekst do wyświetlenia",newWindowCheck:"Otw\xf3rz w nowym oknie",downloadLinkCheck:"Link do pobrania",bookmark:"Zakładka"},mathBox:{title:"Matematyczne",inputLabel:"Zapis matematyczny",fontSizeLabel:"Rozmiar czcionki",previewLabel:"Podgląd"},imageBox:{title:"Wstaw obraz",file:"Wybierz plik",url:"Adres URL obrazka",altText:"Tekst alternatywny"},videoBox:{title:"Wstaw wideo",file:"Wybierz plik",url:"Adres URL video, np. YouTube/Vimeo"},audioBox:{title:"Wstaw audio",file:"Wybierz plik",url:"Adres URL audio"},browser:{tags:"Tagi",search:"Szukaj"},caption:"Wstaw opis",close:"Zamknij",submitButton:"Zatwierdź",revertButton:"Cofnij zmiany",proportion:"Ogranicz proporcje",basic:"Bez wyr\xf3wnania",left:"Do lewej",right:"Do prawej",center:"Do środka",width:"Szerokość",height:"Wysokość",size:"Rozmiar",ratio:"Proporcje"},controller:{edit:"Edycja",unlink:"Usuń odnośnik",remove:"Usuń",insertRowAbove:"Wstaw wiersz powyżej",insertRowBelow:"Wstaw wiersz poniżej",deleteRow:"Usuń wiersz",insertColumnBefore:"Wstaw kolumnę z lewej",insertColumnAfter:"Wstaw kolumnę z prawej",deleteColumn:"Usuń kolumnę",fixedColumnWidth:"Stała szerokość kolumny",resize100:"Zmień rozmiar - 100%",resize75:"Zmień rozmiar - 75%",resize50:"Zmień rozmiar - 50%",resize25:"Zmień rozmiar - 25%",autoSize:"Rozmiar automatyczny",mirrorHorizontal:"Odbicie lustrzane w poziomie",mirrorVertical:"Odbicie lustrzane w pionie",rotateLeft:"Obr\xf3ć w lewo",rotateRight:"Obr\xf3ć w prawo",maxSize:"Maksymalny rozmiar",minSize:"Minimalny rozmiar",tableHeader:"Nagł\xf3wek tabeli",mergeCells:"Scal kom\xf3rki",splitCells:"Podziel kom\xf3rki",HorizontalSplit:"Podział poziomy",VerticalSplit:"Podział pionowy"},menu:{spaced:"Rozstawiony",bordered:"Z obw\xf3dką",neon:"Neon",translucent:"P\xf3łprzezroczysty",shadow:"Cień",code:"Kod"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"pl",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},86548:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"pt_br",toolbar:{default:"Padr\xe3o",save:"Salvar",font:"Fonte",formats:"Formatos",fontSize:"Tamanho",bold:"Negrito",underline:"Sublinhado",italic:"It\xe1lico",strike:"Riscado",subscript:"Subescrito",superscript:"Sobrescrito",removeFormat:"Remover Formata\xe7\xe3o",fontColor:"Cor da Fonte",hiliteColor:"Cor de destaque",indent:"Recuo",outdent:"Avan\xe7ar",align:"Alinhar",alignLeft:"Alinhar \xe0 esquerda",alignRight:"Alinhar \xe0 direita",alignCenter:"Centralizar",alignJustify:"Justificar",list:"Lista",orderList:"Lista ordenada",unorderList:"Lista desordenada",horizontalRule:"Linha horizontal",hr_solid:"s\xf3lida",hr_dotted:"pontilhada",hr_dashed:"tracejada",table:"Tabela",link:"Link",math:"Matem\xe1tica",image:"Imagem",video:"V\xeddeo",audio:"\xc1udio",fullScreen:"Tela cheia",showBlocks:"Mostrar blocos",codeView:"Mostrar c\xf3digos",undo:"Voltar",redo:"Refazer",preview:"Prever",print:"Imprimir",tag_p:"Paragr\xe1fo",tag_div:"(DIV) Normal",tag_h:"Cabe\xe7alho",tag_blockquote:"Citar",tag_pre:"C\xf3digo",template:"Modelo",lineHeight:"Altura da linha",paragraphStyle:"Estilo do par\xe1grafo",textStyle:"Estilo do texto",imageGallery:"Galeria de imagens",dir_ltr:"Esquerda para direita",dir_rtl:"Direita para esquerda",mention:"Men\xe7\xe3o"},dialogBox:{linkBox:{title:"Inserir link",url:"URL para link",text:"Texto a mostrar",newWindowCheck:"Abrir em nova guia",downloadLinkCheck:"Link para Download",bookmark:"marcar p\xe1ginas"},mathBox:{title:"Matem\xe1tica",inputLabel:"Nota\xe7\xe3o matem\xe1tica",fontSizeLabel:"Tamanho",previewLabel:"Prever"},imageBox:{title:"Inserir imagens",file:"Selecionar arquivos",url:"URL da imagem",altText:"Texto alternativo"},videoBox:{title:"Inserir v\xeddeo",file:"Selecionar arquivos",url:"URL do YouTube/Vimeo"},audioBox:{title:"Inserir \xe1udio",file:"Selecionar arquivos",url:"URL da \xe1udio"},browser:{tags:"Tag",search:"Procurar"},caption:"Inserir descri\xe7\xe3o",close:"Fechar",submitButton:"Enviar",revertButton:"Reverter",proportion:"Restringir propor\xe7\xf5es",basic:"B\xe1sico",left:"Esquerda",right:"Direita",center:"Centro",width:"Largura",height:"Altura",size:"Tamanho",ratio:"Propor\xe7\xf5es"},controller:{edit:"Editar",unlink:"Remover link",remove:"Remover",insertRowAbove:"Inserir linha acima",insertRowBelow:"Inserir linha abaixo",deleteRow:"Deletar linha",insertColumnBefore:"Inserir coluna antes",insertColumnAfter:"Inserir coluna depois",deleteColumn:"Deletar coluna",fixedColumnWidth:"Largura fixa da coluna",resize100:"Redimensionar para 100%",resize75:"Redimensionar para 75%",resize50:"Redimensionar para 50%",resize25:"Redimensionar para 25%",autoSize:"Tamanho autom\xe1tico",mirrorHorizontal:"Espelho, Horizontal",mirrorVertical:"Espelho, Vertical",rotateLeft:"Girar para esquerda",rotateRight:"Girar para direita",maxSize:"Tam m\xe1x",minSize:"Tam m\xedn",tableHeader:"Cabe\xe7alho da tabela",mergeCells:"Mesclar c\xe9lulas",splitCells:"Dividir c\xe9lulas",HorizontalSplit:"Divis\xe3o horizontal",VerticalSplit:"Divis\xe3o vertical"},menu:{spaced:"Espa\xe7ado",bordered:"Com borda",neon:"Neon",translucent:"Transl\xfacido",shadow:"Sombreado",code:"C\xf3digo"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"pt_br",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},20319:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"ro",toolbar:{default:"Default",save:"Salvează",font:"Font",formats:"Format",fontSize:"Dimensiune",bold:"\xcengroșat",underline:"Subliniat",italic:"\xcenclinat",strike:"Tăiat",subscript:"Subscript",superscript:"Superscript",removeFormat:"Șterge formatare",fontColor:"Culoare font",hiliteColor:"Culoare de evidențiere",indent:"Indentează",outdent:"Fără indentare",align:"Aliniere",alignLeft:"Aliniere la st\xe2nga",alignRight:"Aliniere la dreapta",alignCenter:"Aliniere la centru",alignJustify:"Aliniere st\xe2nga - dreapta",list:"Listă",orderList:"Listă ordonată",unorderList:"Listă neordonată",horizontalRule:"Linie orizontală",hr_solid:"Solid",hr_dotted:"Punctat",hr_dashed:"Punctate",table:"Tabel",link:"Link",math:"Matematică",image:"Imagine",video:"Video",audio:"Audio",fullScreen:"Tot ecranul",showBlocks:"Arată blocuri",codeView:"Vizualizare cod",undo:"Anulează",redo:"Refă",preview:"Previzualizare",print:"printează",tag_p:"Paragraf",tag_div:"Normal (DIV)",tag_h:"Antet",tag_blockquote:"Quote",tag_pre:"Citat",template:"Template",lineHeight:"\xcenălțime linie",paragraphStyle:"Stil paragraf",textStyle:"Stil text",imageGallery:"Galerie de imagini",dir_ltr:"De la st\xe2nga la dreapta",dir_rtl:"De la dreapta la stanga",mention:"Mentiune"},dialogBox:{linkBox:{title:"Inserează Link",url:"Adresă link",text:"Text de afișat",newWindowCheck:"Deschide \xeen fereastră nouă",downloadLinkCheck:"Link de descărcare",bookmark:"Marcaj"},mathBox:{title:"Matematică",inputLabel:"Notație matematică",fontSizeLabel:"Dimensiune font",previewLabel:"Previzualizare"},imageBox:{title:"Inserează imagine",file:"Selectează",url:"URL imagine",altText:"text alternativ"},videoBox:{title:"Inserează video",file:"Selectează",url:"Include URL, youtube/vimeo"},audioBox:{title:"Inserează Audio",file:"Selectează",url:"URL Audio"},browser:{tags:"Etichete",search:"Căutareim"},caption:"Inserează descriere",close:"\xcenchide",submitButton:"Salvează",revertButton:"Revenire",proportion:"Constr\xe2nge proporțiile",basic:"De bază",left:"St\xe2nga",right:"Dreapta",center:"Centru",width:"Lățime",height:"\xcenălțime",size:"Dimensiune",ratio:"Ratie"},controller:{edit:"Editează",unlink:"Scoate link",remove:"Elimină",insertRowAbove:"Inserează r\xe2nd deasupra",insertRowBelow:"Inserează r\xe2nd dedesupt",deleteRow:"Șterge linie",insertColumnBefore:"Inserează coloană \xeenainte",insertColumnAfter:"Inserează coloană după",deleteColumn:"Șterge coloană",fixedColumnWidth:"Lățime fixă coloană",resize100:"Redimensionare 100%",resize75:"Redimensionare 75%",resize50:"Redimensionare 50%",resize25:"Redimensionare 25%",autoSize:"Dimensiune automată",mirrorHorizontal:"Oglindă, orizontal",mirrorVertical:"Oglindă, vertical",rotateLeft:"Rotește la st\xe2nga",rotateRight:"Rotește la dreapta",maxSize:"Dimensiune maximă",minSize:"Dimensiune minimă",tableHeader:"Antet tabel",mergeCells:"\xcembină celule",splitCells:"Divizează celule",HorizontalSplit:"Despicare orizontală",VerticalSplit:"Despicare verticală"},menu:{spaced:"Spațiat",bordered:"Mărginit",neon:"Neon",translucent:"Translucent",shadow:"Umbră",code:"Citat"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"ro",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},659:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"ru",toolbar:{default:"По умолчанию",save:"Сохранить",font:"Шрифт",formats:"Стиль абзаца",fontSize:"Размер шрифта",bold:"Полужирный",underline:"Подчёркнутый",italic:"Курсив",strike:"Зачеркнутый",subscript:"Нижний индекс",superscript:"Верхний индекс",removeFormat:"Очистить форматирование",fontColor:"Цвет текста",hiliteColor:"Цвет фона",indent:"Увеличить отступ",outdent:"Уменьшить отступ",align:"Выравнивание",alignLeft:"Слева",alignRight:"Справа",alignCenter:"По центру",alignJustify:"По ширине",list:"Списки",orderList:"Нумерованный",unorderList:"Маркированный",horizontalRule:"Горизонтальная линия",hr_solid:"Сплошная",hr_dotted:"Пунктир",hr_dashed:"Штриховая",table:"Таблица",link:"Ссылка",math:"математический",image:"Изображение",video:"Видео",audio:"Аудио",fullScreen:"Полный экран",showBlocks:"Блочный вид",codeView:"Редактировать HTML",undo:"Отменить",redo:"Вернуть",preview:"Предварительный просмотр",print:"Печать",tag_p:"Текст",tag_div:"Базовый",tag_h:"Заголовок",tag_blockquote:"Цитата",tag_pre:"Код",template:"Шаблон",lineHeight:"Высота линии",paragraphStyle:"Стиль абзаца",textStyle:"Стиль текста",imageGallery:"Галерея",dir_ltr:"Слева направо",dir_rtl:"Справа налево",mention:"Упоминание"},dialogBox:{linkBox:{title:"Вставить ссылку",url:"Ссылка",text:"Текст",newWindowCheck:"Открывать в новом окне",downloadLinkCheck:"Ссылка для скачивания",bookmark:"Закладка"},mathBox:{title:"математический",inputLabel:"Математическая запись",fontSizeLabel:"Кегль",previewLabel:"Предварительный просмотр"},imageBox:{title:"Вставить изображение",file:"Выберите файл",url:"Адрес изображения",altText:"Текстовое описание изображения"},videoBox:{title:"Вставить видео",file:"Выберите файл",url:"Ссылка на видео, Youtube,Vimeo"},audioBox:{title:"Вставить аудио",file:"Выберите файл",url:"Адрес аудио"},browser:{tags:"Теги",search:"Поиск"},caption:"Добавить подпись",close:"Закрыть",submitButton:"Подтвердить",revertButton:"Сбросить",proportion:"Сохранить пропорции",basic:"Без обтекания",left:"Слева",right:"Справа",center:"По центру",width:"Ширина",height:"Высота",size:"Размер",ratio:"Соотношение"},controller:{edit:"Изменить",unlink:"Убрать ссылку",remove:"Удалить",insertRowAbove:"Вставить строку выше",insertRowBelow:"Вставить строку ниже",deleteRow:"Удалить строку",insertColumnBefore:"Вставить столбец слева",insertColumnAfter:"Вставить столбец справа",deleteColumn:"Удалить столбец",fixedColumnWidth:"Фиксированная ширина столбца",resize100:"Размер 100%",resize75:"Размер 75%",resize50:"Размер 50%",resize25:"Размер 25%",autoSize:"Авто размер",mirrorHorizontal:"Отразить по горизонтали",mirrorVertical:"Отразить по вертикали",rotateLeft:"Повернуть против часовой стрелки",rotateRight:"Повернуть по часовой стрелке",maxSize:"Ширина по размеру страницы",minSize:"Ширина по содержимому",tableHeader:"Строка заголовков",mergeCells:"Объединить ячейки",splitCells:"Разделить ячейку",HorizontalSplit:"Разделить горизонтально",VerticalSplit:"Разделить вертикально"},menu:{spaced:"интервал",bordered:"Граничная Линия",neon:"неон",translucent:"полупрозрачный",shadow:"Тень",code:"Код"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"ru",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},9296:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"se",toolbar:{default:"Default",save:"Spara",font:"Typsnitt",formats:"Format",fontSize:"Textstorlek",bold:"Fet",underline:"Understruket",italic:"Kursiv",strike:"\xd6verstruket",subscript:"S\xe4nkt skrift",superscript:"H\xf6jd skrift",removeFormat:"Ta bort formattering",fontColor:"Textf\xe4rg",hiliteColor:"Bakgrundsf\xe4rg",indent:"Minska indrag",outdent:"\xd6ka indrag",align:"Justering",alignLeft:"V\xe4nsterjustering",alignRight:"H\xf6gerjustering",alignCenter:"Mittenjusteirng",alignJustify:"Justera indrag",list:"Listor",orderList:"Numrerad lista",unorderList:"Oordnad lista",horizontalRule:"Horisontell linje",hr_solid:"Solid",hr_dotted:"Punkter",hr_dashed:"Prickad",table:"Tabell",link:"L\xe4nk",math:"Math",image:"Bild",video:"Video",audio:"Ljud",fullScreen:"Helsk\xe4rm",showBlocks:"Visa block",codeView:"Visa koder",undo:"\xc5ngra",redo:"G\xf6r om",preview:"Preview",print:"Print",tag_p:"Paragraf",tag_div:"Normal (DIV)",tag_h:"Rubrik",tag_blockquote:"Citer",tag_pre:"Kod",template:"Mall",lineHeight:"Linjeh\xf6jd",paragraphStyle:"Stil p\xe5 stycke",textStyle:"Textstil",imageGallery:"Bildgalleri",dir_ltr:"V\xe4nster till h\xf6ger",dir_rtl:"H\xf6ger till v\xe4nster",mention:"Namn"},dialogBox:{linkBox:{title:"L\xe4gg till l\xe4nk",url:"URL till l\xe4nk",text:"L\xe4nktext",newWindowCheck:"\xd6ppna i nytt f\xf6nster",downloadLinkCheck:"Nedladdningsl\xe4nk",bookmark:"Bokm\xe4rke"},mathBox:{title:"Math",inputLabel:"Matematisk notation",fontSizeLabel:"Textstorlek",previewLabel:"Preview"},imageBox:{title:"L\xe4gg till bild",file:"L\xe4gg till fr\xe5n fil",url:"L\xe4gg till fr\xe5n URL",altText:"Alternativ text"},videoBox:{title:"L\xe4gg till video",file:"L\xe4gg till fr\xe5n fil",url:"B\xe4dda in video / YouTube,Vimeo"},audioBox:{title:"L\xe4gg till ljud",file:"L\xe4gg till fr\xe5n fil",url:"L\xe4gg till fr\xe5n URL"},browser:{tags:"Tags",search:"S\xf6k"},caption:"L\xe4gg till beskrivning",close:"St\xe4ng",submitButton:"Skicka",revertButton:"\xc5terg\xe5",proportion:"Spara proportioner",basic:"Basic",left:"V\xe4nster",right:"H\xf6ger",center:"Center",width:"Bredd",height:"H\xf6jd",size:"Storlek",ratio:"F\xf6rh\xe5llande"},controller:{edit:"Redigera",unlink:"Ta bort l\xe4nk",remove:"Ta bort",insertRowAbove:"L\xe4gg till rad \xf6ver",insertRowBelow:"L\xe4gg till rad under",deleteRow:"Ta bort rad",insertColumnBefore:"L\xe4gg till kolumn f\xf6re",insertColumnAfter:"L\xe4gg till kolumn efter",deleteColumn:"Ta bort kolumner",fixedColumnWidth:"Fast kolumnbredd",resize100:"F\xf6rstora 100%",resize75:"F\xf6rstora 75%",resize50:"F\xf6rstora 50%",resize25:"F\xf6rstora 25%",autoSize:"Autostorlek",mirrorHorizontal:"Spegling, horisontell",mirrorVertical:"Spegling, vertikal",rotateLeft:"Rotera till v\xe4nster",rotateRight:"Rotera till h\xf6ger",maxSize:"Maxstorlek",minSize:"Minsta storlek",tableHeader:"Rubrik tabell",mergeCells:"Sammanfoga celler (merge)",splitCells:"Separera celler",HorizontalSplit:"Separera horisontalt",VerticalSplit:"Separera vertikalt"},menu:{spaced:"Avst\xe5nd",bordered:"Avgr\xe4nsningslinje",neon:"Neon",translucent:"Genomskinlig",shadow:"Skugga",code:"Kod"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"se",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},44584:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"tr",toolbar:{default:"Varsayılan",save:"Kaydet",font:"Yazı Tipi",formats:"Bi\xe7imlendirmeler",fontSize:"Boyut",bold:"Kalın",underline:"Alt \xc7izili",italic:"İtalik",strike:"\xdcst\xfc \xc7izili",subscript:"Alt Simge",superscript:"\xdcst Simge",removeFormat:"Bi\xe7imi Kaldır",fontColor:"Yazı Tipi Rengi",hiliteColor:"Vurgu Rengi",indent:"Girinti",outdent:"Girintiyi Azalt",align:"Hizala",alignLeft:"Sola Hizala",alignRight:"Sağa Hizala",alignCenter:"Ortaya Hizala",alignJustify:"İki Yana Yasla",list:"Liste",orderList:"Sıralı Liste",unorderList:"Sırasız Liste",horizontalRule:"Yatay \xc7izgi",hr_solid:"D\xfcz",hr_dotted:"Noktalı",hr_dashed:"Kesikli",table:"Tablo",link:"Bağlantı",math:"Matematik",image:"G\xf6rsel",video:"Video",audio:"Ses",fullScreen:"Tam Ekran",showBlocks:"Blokları G\xf6ster",codeView:"Kod G\xf6r\xfcn\xfcm\xfc",undo:"Geri Al",redo:"İleri Al",preview:"\xd6nizleme",print:"Yazdır",tag_p:"Paragraf",tag_div:"Normal (DIV)",tag_h:"Başlık",tag_blockquote:"Alıntı",tag_pre:"Kod",template:"Şablon",lineHeight:"Satır Y\xfcksekliği",paragraphStyle:"Paragraf Stili",textStyle:"Metin Stili",imageGallery:"G\xf6r\xfcnt\xfc Galerisi",dir_ltr:"Soldan Sağa",dir_rtl:"Sağdan Sola",mention:"Belirtmek"},dialogBox:{linkBox:{title:"Bağlantı Ekle",url:"Bağlantı URL'si",text:"G\xf6r\xfcnt\xfclenecek Metin",newWindowCheck:"Yeni Pencerede A\xe7",downloadLinkCheck:"Bağlantıyı İndir",bookmark:"Bağlantıyı Yer İmlerine Ekle"},mathBox:{title:"Matematik",inputLabel:"Matematiksel Simgeler",fontSizeLabel:"Yazı Tipi Boyutu",previewLabel:"\xd6nizleme"},imageBox:{title:"G\xf6r\xfcnt\xfc Ekle",file:"Dosya Se\xe7",url:"G\xf6r\xfcnt\xfc URL'si",altText:"Alternatif Metin"},videoBox:{title:"Video Ekle",file:"Dosya Se\xe7",url:"Medya Ekleme URL'si (YouTube/Vimeo)"},audioBox:{title:"Ses Ekle",file:"Dosya Se\xe7",url:"Ses URL'si"},browser:{tags:"Etiketler",search:"Ara"},caption:"A\xe7ıklama Giriniz",close:"Kapat",submitButton:"G\xf6nder",revertButton:"Geri D\xf6n",proportion:"Orantıları Koru",basic:"Temel",left:"Sola",right:"Sağa",center:"Ortaya",width:"Genişlik",height:"Y\xfckseklik",size:"Boyut",ratio:"Oran"},controller:{edit:"D\xfczenle",unlink:"Bağlantıyı Kaldır",remove:"Kaldır",insertRowAbove:"Satır Yukarı Ekle",insertRowBelow:"Satır Aşağı Ekle",deleteRow:"Satırı Sil",insertColumnBefore:"S\xfctun \xd6nce Ekle",insertColumnAfter:"S\xfctun Sonrası Ekle",deleteColumn:"S\xfctunu Sil",fixedColumnWidth:"Sabit S\xfctun Genişliği",resize100:"%100 \xd6l\xe7eklendir",resize75:"%75 \xd6l\xe7eklendir",resize50:"%50 \xd6l\xe7eklendir",resize25:"%25 \xd6l\xe7eklendir",autoSize:"\xd6l\xe7eğe Otomatik Ayar",mirrorHorizontal:"D\xfczlemsel Aynalama (Yatay)",mirrorVertical:"D\xfczlemsel Aynalama (Dikey)",rotateLeft:"Saat Y\xf6n\xfcnde D\xf6nd\xfcr",rotateRight:"Saat Y\xf6n\xfcn\xfcn Tersine D\xf6nd\xfcr",maxSize:"En B\xfcy\xfck Boyut",minSize:"En K\xfc\xe7\xfck Boyut",tableHeader:"Tablo Başlığı",mergeCells:"H\xfccreleri Birleştir",splitCells:"H\xfccreleri Ayır",HorizontalSplit:"Yatay Ayırma",VerticalSplit:"Dikey Ayırma"},menu:{spaced:"Aralıklı",bordered:"\xc7er\xe7eveli",neon:"Neon",translucent:"Yarı Saydam",shadow:"G\xf6lge",code:"Kod"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"tr",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},34699:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"ua",toolbar:{default:"По замовчуванням",save:"Зберегти",font:"Шрифт",formats:"Стиль абзацу",fontSize:"Розмір шрифту",bold:"Жирний",underline:"Підкреслений",italic:"Курсив",strike:"Перекреслити",subscript:"Нижній індекс",superscript:"Верхній індекс",removeFormat:"Очистити форматування",fontColor:"Колір тексту",hiliteColor:"Колір виділення",indent:"Збільшити відступ",outdent:"Зменшити відступ",align:"Вирівнювання",alignLeft:"За лівим краєм",alignRight:"За правим краєм",alignCenter:"По центру",alignJustify:"За шириною",list:"Список",orderList:"Нумерований",unorderList:"Маркований",horizontalRule:"Горизонтальна лінія",hr_solid:"Суцільна",hr_dotted:"Пунктирна",hr_dashed:"Штрихова",table:"Таблиця",link:"Посилання",math:"Формула",image:"Зображення",video:"Відео",audio:"Аудіо",fullScreen:"Повний екран",showBlocks:"Показати блоки",codeView:"Редагувати як HTML",undo:"Скасувати",redo:"Виконати знову",preview:"Попередній перегляд",print:"Друк",tag_p:"Абзац",tag_div:"Базовий",tag_h:"Заголовок",tag_blockquote:"Цитата",tag_pre:"Код",template:"Шаблон",lineHeight:"Висота лінії",paragraphStyle:"Стиль абзацу",textStyle:"Стиль тексту",imageGallery:"Галерея",dir_ltr:"Зліва направо",dir_rtl:"Справа наліво",mention:"Згадати"},dialogBox:{linkBox:{title:"Вставити посилання",url:"Посилання",text:"Текст",newWindowCheck:"Відкривати в новому вікні",downloadLinkCheck:"Посилання для завантаження",bookmark:"Закладка"},mathBox:{title:"Формула",inputLabel:"Математична запис",fontSizeLabel:"Розмір шрифту",previewLabel:"Попередній перегляд"},imageBox:{title:"Вставити зображення",file:"Виберіть файл",url:"Посилання на зображення",altText:"Текстовий опис зображення"},videoBox:{title:"Вставити відео",file:"Виберіть файл",url:"Посилання на відео, Youtube, Vimeo"},audioBox:{title:"Вставити аудіо",file:"Виберіть файл",url:"Посилання на аудіо"},browser:{tags:"Теги",search:"Пошук"},caption:"Додати підпис",close:"Закрити",submitButton:"Підтвердити",revertButton:"Скинути",proportion:"Зберегти пропорції",basic:"Без обтікання",left:"Зліва",right:"Справа",center:"По центру",width:"Ширина",height:"Висота",size:"Розмір",ratio:"Співвідношення"},controller:{edit:"Змінити",unlink:"Прибрати посилання",remove:"Видалити",insertRowAbove:"Вставити рядок вище",insertRowBelow:"Вставити рядок нижче",deleteRow:"Видалити рядок",insertColumnBefore:"Вставити стовпець зліва",insertColumnAfter:"Вставити стовпець справа",deleteColumn:"Видалити стовпець",fixedColumnWidth:"Фіксована ширина стовпця",resize100:"Розмір 100%",resize75:"Розмір 75%",resize50:"Розмір 50%",resize25:"Розмір 25%",autoSize:"Авто розмір",mirrorHorizontal:"Відобразити по горизонталі",mirrorVertical:"Відобразити по вертикалі",rotateLeft:"Повернути проти годинникової стрілки",rotateRight:"Повернути за годинниковою стрілкою",maxSize:"Ширина за розміром сторінки",minSize:"Ширина за вмістом",tableHeader:"Заголовок таблиці",mergeCells:"Об'єднати клітинки",splitCells:"Розділити клітинку",HorizontalSplit:"Розділити горизонтально",VerticalSplit:"Розділити вертикально"},menu:{spaced:"Інтервал",bordered:"З лініями",neon:"Неон",translucent:"Напівпрозорий",shadow:"Тінь",code:"Код"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"ua",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},9870:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"ur",toolbar:{default:"طے شدہ",save:"محفوظ کریں",font:"فونٹ",formats:"فارمیٹس",fontSize:"سائز",bold:"بولڈ",underline:"انڈر لائن",italic:"ترچھا",strike:"لکیرہ کردہ",subscript:"ذیلی",superscript:"انتہائی",removeFormat:"فارمیٹ کو حذف دیں",fontColor:"لکھائی کا رنگ",hiliteColor:"نمایاں رنگ",indent:"حاشیہ",outdent:"ہاشیہ واپس",align:"رخ",alignLeft:"بائیں طرف",alignRight:"دائیں طرف",alignCenter:"مرکز میں طرف",alignJustify:"ہر طرف برابر",list:"فہرست",orderList:"ترتیب شدہ فہرست",unorderList:"غیر ترتیب شدہ فہرست",horizontalRule:"لکیر",hr_solid:"ٹھوس",hr_dotted:"نقطے دار",hr_dashed:"ڈیشڈ",table:"میز",link:"لنک",math:"ریاضی",image:"تصویر",video:"ویڈیو",audio:"آواز",fullScreen:"پوری اسکرین",showBlocks:"ڈبے دکھائیں",codeView:"کوڈ کا نظارہ",undo:"واپس کریں",redo:"دوبارہ کریں",preview:"پیشنظر",print:"پرنٹ کریں",tag_p:"پیراگراف",tag_div:"عام (div)",tag_h:"ہیڈر",tag_blockquote:"اقتباس",tag_pre:"کوڈ",template:"سانچہ",lineHeight:"لکیر کی اونچائی",paragraphStyle:"عبارت کا انداز",textStyle:"متن کا انداز",imageGallery:"تصویری نگارخانہ",dir_ltr:"بائیں سے دائیں",dir_rtl:"دائیں سے بائیں",mention:"تذکرہ"},dialogBox:{linkBox:{title:"لنک داخل کریں",url:"لنک کرنے کے لیے URL",text:"ظاہر کرنے کے لیے متن",newWindowCheck:"نئی ونڈو میں کھولیں",downloadLinkCheck:"ڈاؤن لوڈ لنک",bookmark:"بک مارک"},mathBox:{title:"ریاضی",inputLabel:"ریاضیاتی اشارے",fontSizeLabel:"حرف کا سائز",previewLabel:"پیش نظارہ"},imageBox:{title:"تصویر داخل کریں",file:"فائلوں سے منتخب کریں",url:"تصویری URL",altText:"متبادل متن"},videoBox:{title:"ویڈیو داخل کریں",file:"فائلوں سے منتخب کریں",url:"ذرائع ابلاغ کا یو آر ایل، یوٹیوب/ویمیو"},audioBox:{title:"آواز داخل کریں",file:"فائلوں سے منتخب کریں",url:"آواز URL"},browser:{tags:"ٹیگز",search:"تلاش کریں"},caption:"عنوان",close:"بند کریں",submitButton:"بھیجیں",revertButton:"واپس",proportion:"تناسب کو محدود کریں",basic:"بنیادی",left:"بائیں",right:"دائیں",center:"مرکز",width:"چوڑائی",height:"اونچائی",size:"حجم",ratio:"تناسب"},controller:{edit:"ترمیم",unlink:"لنک ختم کریں",remove:"حذف",insertRowAbove:"اوپر قطار شامل کریں",insertRowBelow:"نیچے قطار شامل کریں",deleteRow:"قطار کو حذف کریں",insertColumnBefore:"پہلے ستون شامل کریں",insertColumnAfter:"اس کے بعد ستون شامل کریں",deleteColumn:"ستون حذف کریں",fixedColumnWidth:"مقررہ ستون کی چوڑائی",resize100:"100% کا حجم تبدیل کریں",resize75:"75% کا حجم تبدیل کریں",resize50:"50% کا حجم تبدیل کریں",resize25:"25% کا حجم تبدیل کریں",autoSize:"ازخود حجم",mirrorHorizontal:"آئینہ، افقی",mirrorVertical:"آئینہ، عمودی",rotateLeft:"بائیں گھومو",rotateRight:"دائیں گھمائیں",maxSize:"زیادہ سے زیادہ سائز",minSize:"کم از کم سائز",tableHeader:"میز کی سرخی",mergeCells:"حجروں کو ضم کریں",splitCells:"حجروں کو علیدہ کرو",HorizontalSplit:"افقی تقسیم",VerticalSplit:"عمودی تقسیم"},menu:{spaced:"فاصلہ",bordered:"سرحدی",neon:"نیین",translucent:"پارباسی",shadow:"سایہ",code:"کوڈ"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"ur",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},23428:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={code:"zh_cn",toolbar:{default:"默认",save:"保存",font:"字体",formats:"格式",fontSize:"字号",bold:"粗体",underline:"下划线",italic:"斜体",strike:"删除线",subscript:"下标",superscript:"上标",removeFormat:"清除格式",fontColor:"字体颜色",hiliteColor:"背景颜色",indent:"增加缩进",outdent:"减少缩进",align:"对齐方式",alignLeft:"左对齐",alignRight:"右对齐",alignCenter:"居中",alignJustify:"两端对齐",list:"列表",orderList:"有序列表",unorderList:"无序列表",horizontalRule:"水平线",hr_solid:"实线",hr_dotted:"点线",hr_dashed:"虚线",table:"表格",link:"超链接",math:"数学",image:"图片",video:"视频",audio:"音讯",fullScreen:"全屏",showBlocks:"显示块区域",codeView:"代码视图",undo:"撤消",redo:"恢复",preview:"预览",print:"打印",tag_p:"段落",tag_div:"正文 (DIV)",tag_h:"标题",tag_blockquote:"引用",tag_pre:"代码",template:"模板",lineHeight:"行高",paragraphStyle:"段落样式",textStyle:"文字样式",imageGallery:"图片库",dir_ltr:"左到右",dir_rtl:"右到左",mention:"提到"},dialogBox:{linkBox:{title:"插入超链接",url:"网址",text:"要显示的文字",newWindowCheck:"在新标签页中打开",downloadLinkCheck:"下载链接",bookmark:"书签"},mathBox:{title:"数学",inputLabel:"数学符号",fontSizeLabel:"字号",previewLabel:"预览"},imageBox:{title:"插入图片",file:"上传图片",url:"图片网址",altText:"替换文字"},videoBox:{title:"插入视频",file:"上传图片",url:"嵌入网址, Youtube,Vimeo"},audioBox:{title:"插入音频",file:"上传图片",url:"音频网址"},browser:{tags:"标签",search:"搜索"},caption:"标题",close:"取消",submitButton:"确定",revertButton:"恢复",proportion:"比例",basic:"基本",left:"左",right:"右",center:"居中",width:"宽度",height:"高度",size:"尺寸",ratio:"比"},controller:{edit:"编辑",unlink:"去除链接",remove:"删除",insertRowAbove:"在上方插入",insertRowBelow:"在下方插入",deleteRow:"删除行",insertColumnBefore:"在左侧插入",insertColumnAfter:"在右侧插入",deleteColumn:"删除列",fixedColumnWidth:"固定列宽",resize100:"放大 100%",resize75:"放大 75%",resize50:"放大 50%",resize25:"放大 25%",mirrorHorizontal:"翻转左右",mirrorVertical:"翻转上下",rotateLeft:"向左旋转",rotateRight:"向右旋转",maxSize:"最大尺寸",minSize:"最小尺寸",tableHeader:"表格标题",mergeCells:"合并单元格",splitCells:"分割单元格",HorizontalSplit:"水平分割",VerticalSplit:"垂直分割"},menu:{spaced:"间隔开",bordered:"边界线",neon:"霓虹灯",translucent:"半透明",shadow:"阴影",code:"代码"}};return void 0===t&&(e.SUNEDITOR_LANG||Object.defineProperty(e,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_LANG,"zh_cn",{enumerable:!0,writable:!0,configurable:!0,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_LANG a window with a document");return i(e)}:i(t)},22475:function(e,t,i){"use strict";i.d(t,{Z:function(){return r}});var l={rtl:{italic:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10.5 15.8" xml:space="preserve"><g><path d="M0.3,0.1c0.3,0,0.5,0,0.7,0c1,0.1,1.7,0.1,2.2,0.1H4L7.2,0l0.2,1.1H7c-0.5,0-1,0.1-1.5,0.3v0.4l0.3,1.9L6,4.4L6.3,6 l0.1,0.4l0.1,0.5c0.1,0.2,0.1,0.4,0.2,0.7s0.1,0.6,0.2,0.9L7,9.1l0.6,2.8l0.3,1.4c0.1,0.4,0.2,0.7,0.4,1c0.4,0.2,0.8,0.3,1.2,0.4 l0.8,0.2l0.2,0.9l-1.1,0c-0.9-0.1-1.5-0.1-1.8-0.1h-2c-0.9,0.1-1.4,0.2-1.5,0.2c-0.1,0-0.2,0-0.3,0H3.4c-0.1,0-0.2,0-0.2,0 l-0.1-0.4c0-0.2-0.1-0.4-0.1-0.6l0.7-0.1c0.4,0,0.8-0.1,1.2-0.2c0-0.1,0-0.2,0-0.3l-0.1-0.5l-0.4-2.4L4,9.6L3.4,6.4 C3.2,5.7,3,4.7,2.7,3.3c0-0.3-0.1-0.5-0.1-0.8C2.5,2.1,2.4,1.9,2.3,1.6C2,1.4,1.6,1.3,1.3,1.2C0.9,1.2,0.5,1.1,0.2,0.9L0,0.4L0,0 L0.3,0.1L0.3,0.1z"/></g></svg>',indent:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 12.4" xml:space="preserve"><g><g><path d="M15.5,10.1L15.5,10.1c0.1,0,0.3,0.1,0.3,0.3v1.7c0,0.1,0,0.1-0.1,0.2c-0.1,0.1-0.1,0.1-0.2,0.1l-15.2,0 c-0.1,0-0.1,0-0.2-0.1C0,12.2,0,12.2,0,12.1l0-1.7c0-0.1,0-0.1,0.1-0.2c0.1-0.1,0.1-0.1,0.2-0.1C0.3,10.1,15.5,10.1,15.5,10.1z M9.8,6.7c0.1,0,0.1,0,0.2,0.1C10.1,6.9,10.1,7,10.1,7v1.7c0,0.1,0,0.2-0.1,0.2C10,9,9.9,9,9.8,9L0.3,9C0.2,9,0.1,9,0.1,8.9 C0,8.9,0,8.8,0,8.7V7C0,7,0,6.9,0.1,6.8c0.1-0.1,0.1-0.1,0.2-0.1C0.3,6.7,9.8,6.7,9.8,6.7z M0.3,3.4h9.6h0c0.1,0,0.3,0.1,0.3,0.3 v1.7v0c0,0.1-0.1,0.3-0.3,0.3H0.3c-0.1,0-0.1,0-0.2-0.1C0,5.5,0,5.4,0,5.3V3.6c0-0.1,0-0.1,0.1-0.2C0.1,3.4,0.2,3.4,0.3,3.4 L0.3,3.4z M0.3,0l15.2,0c0.1,0,0.1,0,0.2,0.1c0.1,0.1,0.1,0.1,0.1,0.2V2c0,0.1,0,0.2-0.1,0.2c-0.1,0.1-0.1,0.1-0.2,0.1H0.3 c-0.1,0-0.1,0-0.2-0.1C0,2.1,0,2,0,2l0-1.7c0-0.1,0-0.1,0.1-0.2C0.1,0,0.2,0,0.3,0z"/></g><path d="M13.1,3.5L15.7,6c0.1,0.1,0.1,0.3,0,0.4l-2.5,2.5C13.1,9,13,9,12.9,9c-0.1,0-0.1,0-0.2-0.1c-0.1-0.1-0.1-0.1-0.1-0.2V3.7 c0-0.1,0-0.2,0.1-0.2c0.1-0.1,0.1-0.1,0.2-0.1C13,3.4,13.1,3.4,13.1,3.5z"/></g></svg>',outdent:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 12.4" xml:space="preserve"><g><g><path d="M15.5,10.1L15.5,10.1c0.1,0,0.3,0.1,0.3,0.3v1.7c0,0.1,0,0.1-0.1,0.2c-0.1,0.1-0.1,0.1-0.2,0.1l-15.2,0 c-0.1,0-0.1,0-0.2-0.1C0,12.2,0,12.2,0,12.1l0-1.7c0-0.1,0-0.1,0.1-0.2c0.1-0.1,0.1-0.1,0.2-0.1C0.3,10.1,15.5,10.1,15.5,10.1z M9.8,6.7c0.1,0,0.1,0,0.2,0.1C10.1,6.9,10.1,7,10.1,7v1.7c0,0.1,0,0.2-0.1,0.2C10,9,9.9,9,9.8,9L0.3,9C0.2,9,0.1,9,0.1,8.9 C0,8.9,0,8.8,0,8.7V7C0,7,0,6.9,0.1,6.8c0.1-0.1,0.1-0.1,0.2-0.1C0.3,6.7,9.8,6.7,9.8,6.7z M0.3,3.4h9.6h0c0.1,0,0.3,0.1,0.3,0.3 v1.7v0c0,0.1-0.1,0.3-0.3,0.3H0.3c-0.1,0-0.1,0-0.2-0.1C0,5.5,0,5.4,0,5.3V3.6c0-0.1,0-0.1,0.1-0.2C0.1,3.4,0.2,3.4,0.3,3.4 L0.3,3.4z M0.3,0l15.2,0c0.1,0,0.1,0,0.2,0.1c0.1,0.1,0.1,0.1,0.1,0.2V2c0,0.1,0,0.2-0.1,0.2c-0.1,0.1-0.1,0.1-0.2,0.1H0.3 c-0.1,0-0.1,0-0.2-0.1C0,2.1,0,2,0,2l0-1.7c0-0.1,0-0.1,0.1-0.2C0.1,0,0.2,0,0.3,0z"/></g><path d="M15.5,3.4c0.1,0,0.1,0,0.2,0.1c0.1,0.1,0.1,0.1,0.1,0.2v5.1c0,0.1,0,0.1-0.1,0.2C15.6,9,15.5,9,15.5,9 c-0.1,0-0.1,0-0.2-0.1l-2.5-2.5c-0.1-0.1-0.1-0.3,0-0.4l2.5-2.5C15.3,3.4,15.4,3.4,15.5,3.4z"/></g></svg>',list_bullets:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 12.4" xml:space="preserve"><g><path d="M12.4,10.7c0,0.9,0.8,1.7,1.7,1.7c0.9,0,1.7-0.8,1.7-1.7C15.7,9.8,15,9,14.1,9c-0.4,0-0.9,0.2-1.2,0.5 C12.5,9.8,12.4,10.2,12.4,10.7C12.4,10.7,12.4,10.7,12.4,10.7z M12.4,6.2c0,0.9,0.8,1.7,1.7,1.7c0.4,0,0.9-0.2,1.2-0.5 c0.3-0.3,0.4-0.7,0.4-1.1c0-0.9-0.7-1.7-1.6-1.7C13.1,4.6,12.4,5.3,12.4,6.2C12.4,6.2,12.4,6.2,12.4,6.2z M0,9.8v1.7 c0,0.1,0,0.1,0.1,0.2c0.1,0.1,0.1,0.1,0.2,0.1l10.7,0c0,0,0,0,0,0c0.1,0,0.3-0.1,0.3-0.3V9.8c0-0.1,0-0.1-0.1-0.2 C11.1,9.6,11,9.6,11,9.6l-10.7,0c-0.1,0-0.1,0-0.2,0.1C0,9.7,0,9.8,0,9.8L0,9.8z M12.9,2.9c0.3,0.3,0.7,0.5,1.2,0.5 c0.4,0,0.9-0.2,1.2-0.5c0.7-0.7,0.7-1.7,0-2.4C14.9,0.2,14.5,0,14.1,0c-0.4,0-0.9,0.2-1.2,0.5c-0.3,0.3-0.5,0.7-0.5,1.2 C12.4,2.1,12.5,2.6,12.9,2.9z M0,5.3V7c0,0.1,0,0.1,0.1,0.2c0.1,0.1,0.1,0.1,0.2,0.1H11c0.1,0,0.1,0,0.2-0.1 c0.1-0.1,0.1-0.1,0.1-0.2V5.3c0,0,0,0,0,0c0-0.1-0.1-0.3-0.3-0.3H0.3c-0.1,0-0.1,0-0.2,0.1C0,5.2,0,5.3,0,5.3L0,5.3z M0,0.8v1.7 c0,0.1,0,0.1,0.1,0.2c0.1,0.1,0.1,0.1,0.2,0.1h10.7c0.1,0,0.1,0,0.2-0.1c0,0,0.1-0.1,0.1-0.2V0.8c0-0.1,0-0.1-0.1-0.2 c0-0.1-0.1-0.1-0.2-0.1H0.3c-0.1,0-0.1,0-0.2,0.1C0,0.7,0,0.8,0,0.8L0,0.8z"/></g></svg>',list_number:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 15.7" xml:space="preserve"><g><path d="M0,11.5l0,1.7c0,0.1,0,0.1,0.1,0.2c0.1,0.1,0.1,0.1,0.2,0.1H11c0.1,0,0.2,0,0.2-0.1c0.1-0.1,0.1-0.1,0.1-0.2v-1.7 c0-0.1,0-0.1-0.1-0.2c-0.1-0.1-0.1-0.1-0.2-0.1H0.3c-0.1,0-0.2,0-0.2,0.1C0,11.4,0,11.4,0,11.5L0,11.5z M0,8.7c0,0.1,0,0.1,0.1,0.2 C0.1,8.9,0.2,9,0.3,9H11c0.1,0,0.2,0,0.2-0.1c0.1-0.1,0.1-0.1,0.1-0.2V7c0-0.1,0-0.1-0.1-0.2c-0.1-0.1-0.1-0.1-0.2-0.1l-10.7,0 c-0.1,0-0.2,0-0.2,0.1C0,6.8,0,6.9,0,7C0,7,0,8.7,0,8.7z M0,2.5v1.7c0,0.1,0,0.1,0.1,0.2c0,0,0.1,0.1,0.2,0.1l10.7,0 c0.1,0,0.2,0,0.2-0.1c0.1-0.1,0.1-0.1,0.1-0.2V2.4c0-0.1,0-0.1-0.1-0.2c-0.1,0-0.1,0-0.2,0H0.3c-0.1,0-0.1,0-0.2,0 C0,2.3,0,2.4,0,2.5L0,2.5z"/></g><path d="M15.6,14.2c0-0.3-0.1-0.6-0.3-0.8c-0.2-0.2-0.4-0.4-0.7-0.4l0.9-1v-0.8h-2.9v1.3h0.9v-0.5h0.9l0,0c-0.1,0.1-0.2,0.2-0.3,0.3 s-0.2,0.3-0.4,0.5l-0.3,0.3l0.2,0.5c0.6,0,0.9,0.1,0.9,0.5c0,0.1-0.1,0.3-0.2,0.4c-0.1,0.1-0.3,0.1-0.4,0.1c-0.3,0-0.7-0.1-0.9-0.3 l-0.5,0.8c0.4,0.4,0.9,0.6,1.5,0.6c0.4,0,0.9-0.1,1.2-0.4C15.5,15.1,15.6,14.7,15.6,14.2z"/><path d="M15.6,8.7h-0.9v0.5h-1.1c0-0.2,0.2-0.4,0.4-0.5c0.2-0.2,0.4-0.3,0.7-0.4c0.3-0.2,0.5-0.3,0.7-0.6c0.2-0.2,0.3-0.5,0.3-0.8 c0-0.4-0.2-0.8-0.5-1c-0.6-0.4-1.4-0.5-2-0.1c-0.3,0.2-0.5,0.4-0.6,0.7L13.3,7c0.1-0.3,0.4-0.5,0.7-0.5c0.1,0,0.3,0,0.3,0.1 c0.1,0.1,0.1,0.2,0.1,0.3c0,0.2-0.1,0.3-0.2,0.4c-0.2,0.1-0.3,0.3-0.5,0.4c-0.2,0.1-0.4,0.3-0.6,0.4c-0.2,0.2-0.4,0.4-0.5,0.6 c-0.1,0.2-0.2,0.5-0.2,0.8c0,0.2,0,0.3,0,0.5h3.2L15.6,8.7L15.6,8.7z"/><path d="M15.6,3.6h-1V0h-0.9l-1.2,1.1l0.6,0.7c0.2-0.1,0.3-0.3,0.4-0.5l0,0v2.2h-0.9v0.9h3L15.6,3.6L15.6,3.6z"/></svg>',link:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 15.7" xml:space="preserve"><g><path d="M7.4,9.9l3.1,3.1c0.3,0.3,0.8,0.5,1.3,0.5c0.5,0,0.9-0.2,1.3-0.5c0,0,0,0,0,0c0.7-0.7,0.7-1.9,0-2.6L9.9,7.3 c0-0.1,0-0.2,0-0.3C9.9,7,10,7,10.1,7l2.2-0.2c0.1,0,0.1,0,0.2,0.1l2.1,2.1c0.4,0.4,0.7,0.8,0.9,1.3c0.2,0.5,0.3,1,0.3,1.5 c0,0.5-0.1,1-0.3,1.5c-0.8,2-3.2,3-5.2,2.2c-0.5-0.2-0.9-0.5-1.3-0.9l-2.1-2.1c-0.1,0-0.1-0.1-0.1-0.2L7,10.1C7,10,7,9.9,7.1,9.9 C7.2,9.8,7.3,9.9,7.4,9.9z M1.2,1.1C1.6,0.7,2,0.4,2.5,0.3c1-0.4,2.1-0.4,3.1,0C6,0.4,6.5,0.7,6.8,1.1L9,3.2C9,3.3,9.1,3.3,9,3.4 L8.8,5.6c0,0.1-0.1,0.2-0.2,0.2c-0.1,0.1-0.2,0.1-0.3,0L5.3,2.7C5,2.3,4.5,2.1,4,2.1c-0.5,0-0.9,0.2-1.3,0.5c0,0,0,0,0,0 C2,3.4,2,4.5,2.7,5.2l3.1,3.2c0.1,0.1,0.1,0.2,0,0.3c0,0.1-0.1,0.1-0.2,0.1L3.5,9C3.4,9,3.4,9,3.3,8.9L1.2,6.8c0,0,0,0,0,0 C-0.4,5.2-0.4,2.7,1.2,1.1L1.2,1.1z M14.3,6h-2.6c0,0,0,0,0,0c-0.1,0-0.2-0.1-0.2-0.2c0-0.1,0-0.2,0.1-0.3l2.5-0.7 c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0.1,0.1,0.2l0.1,0.8c0,0.1,0,0.1-0.1,0.2C14.5,6,14.4,6,14.3,6L14.3,6z M10.2,4.1 c0,0.1-0.1,0.2-0.2,0.2l0,0c0,0,0,0,0,0C9.8,4.2,9.7,4.1,9.8,4L9.7,1.4c0-0.1,0-0.1,0.1-0.2c0.1,0,0.1,0,0.2,0h0.8 c0.1,0,0.1,0,0.2,0.1c0,0.1,0,0.1,0,0.2L10.2,4.1L10.2,4.1z M1.5,9.7h1.3h1.3c0.1,0,0.2,0.1,0.2,0.2c0,0.1,0,0.2-0.1,0.3l-2.5,0.6 H1.6c0,0-0.1,0-0.1,0c-0.1,0-0.1-0.1-0.1-0.2L1.2,9.9c0-0.1,0-0.1,0.1-0.2c0-0.1,0.1-0.1,0.2-0.1L1.5,9.7z M5.6,11.6 C5.6,11.6,5.6,11.6,5.6,11.6c0-0.1,0.1-0.2,0.3-0.1c0,0,0,0,0,0c0.1,0,0.2,0.1,0.2,0.2v2.6c0,0.1,0,0.1-0.1,0.2 c0,0-0.1,0.1-0.2,0.1L5,14.5c-0.1,0-0.1,0-0.2-0.1c0-0.1,0-0.1,0-0.2L5.6,11.6L5.6,11.6z"/></g></svg>',unlink:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 15.7" xml:space="preserve"><g><path d="M14.6,14.6c1.6-1.6,1.6-4.1,0-5.7l0,0l-3.1-3.1l-1.2,1.6l2.9,2.9c0.4,0.4,0.6,0.9,0.6,1.5c0,1.1-0.9,2.1-2.1,2.1l0,0 c-0.6,0-1.1-0.2-1.5-0.6l-0.4-0.4l-1.7,1l0.8,0.8C10.4,16.2,13,16.2,14.6,14.6L14.6,14.6L14.6,14.6z M3.6,6C3,5.9,2.6,5.5,2.3,5 S1.9,4,2.1,3.4C2.3,2.9,2.6,2.5,3,2.2C3.5,2,4.1,1.9,4.6,2l3.3,1.4l0.5-2L5.1,0.1C4-0.1,2.9,0,2,0.5C1.1,1.1,0.4,1.9,0.2,3 C-0.1,4,0,5.1,0.6,6C1.1,6.9,1.9,7.6,3,7.8l5.4,2l0.5-2L6.2,6.9L3.6,6z"/></g></svg>'},redo:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.59 14.18"><g><path d="M11.58,18.48a6.84,6.84,0,1,1,6.85-6.85s0,.26,0,.67a8,8,0,0,1-.22,1.44l.91-.55a.51.51,0,0,1,.36,0,.45.45,0,0,1,.29.22.47.47,0,0,1,.06.36.45.45,0,0,1-.22.29L17.42,15.3l-.12,0h-.25l-.12-.06-.09-.09-.06-.07,0-.06-.87-2.12a.43.43,0,0,1,0-.37.49.49,0,0,1,.27-.26.41.41,0,0,1,.36,0,.53.53,0,0,1,.27.26l.44,1.09a6.51,6.51,0,0,0,.24-1.36,4.58,4.58,0,0,0,0-.64,5.83,5.83,0,0,0-1.73-4.17,5.88,5.88,0,0,0-8.34,0,5.9,5.9,0,0,0,4.17,10.06.51.51,0,0,1,.33.15.48.48,0,0,1,0,.68.53.53,0,0,1-.33.12Z" transform="translate(-4.48 -4.54)"/></g></svg>',undo:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.59 14.18"><g><path d="M5,14a.43.43,0,0,1-.22-.29.46.46,0,0,1,.06-.36.43.43,0,0,1,.29-.22.56.56,0,0,1,.36,0l.91.55a8.27,8.27,0,0,1-.22-1.45,5.07,5.07,0,0,1,0-.67A6.85,6.85,0,1,1,13,18.47a.44.44,0,0,1-.33-.13.48.48,0,0,1,0-.68.51.51,0,0,1,.33-.15A5.89,5.89,0,0,0,17.15,7.45a5.88,5.88,0,0,0-8.33,0,5.84,5.84,0,0,0-1.73,4.17s0,.25,0,.65a6.49,6.49,0,0,0,.24,1.37l.44-1.09a.57.57,0,0,1,.27-.26.41.41,0,0,1,.36,0,.53.53,0,0,1,.27.26.43.43,0,0,1,0,.37L7.82,15l0,.09-.09.09-.1.07-.06,0H7.28l-.13,0-1.09-.63c-.65-.36-1-.57-1.1-.63Z" transform="translate(-4.49 -4.53)"/></g></svg>',bold:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 11.76 15.75"><g><path d="M6.4,3.76V19.5h6.76a5.55,5.55,0,0,0,2-.32,4.93,4.93,0,0,0,1.52-1,4.27,4.27,0,0,0,1.48-3.34,3.87,3.87,0,0,0-.69-2.37,5.74,5.74,0,0,0-.71-.83,3.44,3.44,0,0,0-1.1-.65,3.6,3.6,0,0,0,1.58-1.36,3.66,3.66,0,0,0,.53-1.93,3.7,3.7,0,0,0-1.21-2.87,4.65,4.65,0,0,0-3.25-1.1H6.4Zm2.46,6.65V5.57h3.52a4.91,4.91,0,0,1,1.36.15,2.3,2.3,0,0,1,.85.45,2.06,2.06,0,0,1,.74,1.71,2.3,2.3,0,0,1-.78,1.92,2.54,2.54,0,0,1-.86.46,4.7,4.7,0,0,1-1.32.15H8.86Zm0,7.27V12.15H12.7a4.56,4.56,0,0,1,1.38.17,3.43,3.43,0,0,1,.95.49,2.29,2.29,0,0,1,.92,2,2.73,2.73,0,0,1-.83,2.1,2.66,2.66,0,0,1-.83.58,3.25,3.25,0,0,1-1.26.2H8.86Z" transform="translate(-6.4 -3.75)"/></g></svg>',underline:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 9.78 15.74"><g><path d="M14.64,3.76h2.52v7.72a4.51,4.51,0,0,1-.59,2.31,3.76,3.76,0,0,1-1.71,1.53,6.12,6.12,0,0,1-2.64.53,5,5,0,0,1-3.57-1.18,4.17,4.17,0,0,1-1.27-3.24V3.76H9.9v7.3a3,3,0,0,0,.55,2,2.3,2.3,0,0,0,1.83.65,2.26,2.26,0,0,0,1.8-.65,3.09,3.09,0,0,0,.55-2V3.76Zm2.52,13.31V19.5H7.39V17.08h9.77Z" transform="translate(-7.38 -3.76)"/></g></svg>',italic:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10.49 15.76"><g><path d="M17.16,3.79l.37,0-.06.38-.14.52A10,10,0,0,1,16.21,5a9.37,9.37,0,0,0-1,.32,6.68,6.68,0,0,0-.25.89c-.06.31-.11.59-.14.85-.3,1.36-.52,2.41-.68,3.14l-.61,3.18L13.1,15l-.43,2.4-.12.46a.62.62,0,0,0,0,.28c.44.1.85.17,1.23.22l.68.11a4.51,4.51,0,0,1-.08.6l-.09.42a.92.92,0,0,0-.23,0l-.43,0a1.37,1.37,0,0,1-.29,0c-.13,0-.63-.08-1.49-.16l-2,0c-.28,0-.87,0-1.78.12L7,19.5l.17-.88.8-.2A6.61,6.61,0,0,0,9.19,18,2.62,2.62,0,0,0,9.61,17l.28-1.41.58-2.75.12-.66c.05-.3.11-.58.17-.86s.12-.51.17-.69l.12-.48.12-.43.31-1.6.15-.65.31-1.91V5.14a3.86,3.86,0,0,0-1.48-.29l-.38,0,.2-1.06,3.24.14.75,0c.45,0,1.18,0,2.18-.09.23,0,.46,0,.71,0Z" transform="translate(-7.04 -3.76)"/></g></svg>',strike:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 14.9"><g><path d="M12.94,13a4.27,4.27,0,0,1,1.32.58,1.46,1.46,0,0,1,.55,1.2,1.87,1.87,0,0,1-.88,1.64,4.17,4.17,0,0,1-2.35.59,4.44,4.44,0,0,1-2.74-.71,2.72,2.72,0,0,1-1-2.17H5.57a4.56,4.56,0,0,0,1.55,3.7,7,7,0,0,0,4.47,1.23,6,6,0,0,0,4.07-1.3,4.24,4.24,0,0,0,1.52-3.37,4,4,0,0,0-.26-1.4h-4ZM6.37,10.24A3.27,3.27,0,0,1,6,8.68a4,4,0,0,1,1.48-3.3,5.92,5.92,0,0,1,3.88-1.21,5.58,5.58,0,0,1,3.91,1.24,4.36,4.36,0,0,1,1.45,3.17H14.44a2.12,2.12,0,0,0-.91-1.81,4.45,4.45,0,0,0-2.44-.55,3.69,3.69,0,0,0-2,.51A1.64,1.64,0,0,0,8.3,8.22a1.3,1.3,0,0,0,.48,1.11,7,7,0,0,0,2.1.78l.28.06.28.08H6.37Zm13.09.68a.73.73,0,0,1,.***********,0,0,1,.**********,0,0,1-.**********,0,0,1-.49.19H5.1a.67.67,0,0,1-.49-.19.66.66,0,0,1-.2-.48.64.64,0,0,1,.2-.48.73.73,0,0,1,.49-.21H19.46Z" transform="translate(-4.41 -4.17)"/></g></svg>',subscript:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 14.61"><g><path d="M15.38,4.33H12.74L11.19,7c-.28.46-.51.87-.69,1.21L10.07,9h0l-.44-.8c-.22-.4-.45-.81-.71-1.23L7.34,4.33H4.68L8.26,10,4.4,16.08H7.1l1.69-2.83c.38-.63.72-1.22,1-1.78l.25-.46h0l.49.92c.***********.74,1.32L13,16.08h2.61L11.84,10l1.77-2.84,1.77-2.85Zm4.77,13.75H17v-.15c0-.4.05-.64.16-.72a4.42,4.42,0,0,1,1.16-.31,3.3,3.3,0,0,0,1.54-.56A1.84,1.84,0,0,0,20.15,15a1.78,1.78,0,0,0-.44-1.41A2.8,2.8,0,0,0,18,13.25a2.71,2.71,0,0,0-1.69.37,1.83,1.83,0,0,0-.44,1.43v.23H17v-.23q0-.63.18-.78a1.62,1.62,0,0,1,.88-.15,1.59,1.59,0,0,1,.88.15q.18.15.18.75t-.18.75a3.58,3.58,0,0,1-1.18.33,3.33,3.33,0,0,0-1.52.51,1.57,1.57,0,0,0-.32,1.18v1.15h4.27v-.86Z" transform="translate(-4.4 -4.33)"/></g></svg>',superscript:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 15.42"><g><path d="M12,13.14l3.61-5.81H12.94L11.33,10c-.28.46-.51.88-.69,1.25l-.45.83h0l-.45-.85c-.22-.41-.45-.82-.71-1.24L7.4,7.33H4.68l3.66,5.81L4.4,19.33H7.14l1.74-2.87q.58-1,1-1.83l.25-.48h0l.51.94.75,1.37,1.72,2.87h2.67l-1.92-3.09c-1.12-1.8-1.76-2.83-1.92-3.1Zm4.84-4.41h0l0,.15h3.27v.86H15.77V8.58a1.66,1.66,0,0,1,.33-1.22,3.51,3.51,0,0,1,1.56-.51,3.68,3.68,0,0,0,1.21-.34c.13-.1.19-.36.19-.77S19,5.07,18.87,5A1.63,1.63,0,0,0,18,4.8a1.58,1.58,0,0,0-.91.17c-.13.11-.19.38-.19.8V6H15.78V5.76a1.87,1.87,0,0,1,.45-1.47A2.84,2.84,0,0,1,18,3.91a2.8,2.8,0,0,1,1.72.38,1.84,1.84,0,0,1,.45,1.44,1.91,1.91,0,0,1-.34,1.35,3.24,3.24,0,0,1-1.58.57A3.69,3.69,0,0,0,17,8c-.12.1-.17.35-.17.76Z" transform="translate(-4.4 -3.91)"/></g></svg>',erase:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 13.76"><g><path d="M13.69,17.2h6.46v1.31H8.56L4.41,14.37,14,4.75l6.06,6.06L16.89,14l-3.2,3.19Zm-4.61,0h2.77L14.09,15,9.88,10.75,6.25,14.38l1.41,1.41c.84.82,1.31,1.29,1.42,1.41Z" transform="translate(-4.41 -4.75)"/></g></svg>',indent:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 12.36"><g><path d="M4.68,14.45a.27.27,0,0,1-.19-.08.3.3,0,0,1-.08-.21V9.1a.27.27,0,0,1,.08-.19.28.28,0,0,1,.2-.08.25.25,0,0,1,.19.07l2.54,2.54a.29.29,0,0,1,0,.4L4.88,14.36a.24.24,0,0,1-.2.09Zm15.19,1.12a.27.27,0,0,1,.19.08.25.25,0,0,1,.08.19v1.69a.27.27,0,0,1-.08.19.25.25,0,0,1-.19.08H4.68a.27.27,0,0,1-.19-.08.25.25,0,0,1-.08-.19V15.84a.27.27,0,0,1,.27-.27H19.87Zm0-3.38a.27.27,0,0,1,.19.08.28.28,0,0,1,.08.21v1.68a.32.32,0,0,1-.08.21.25.25,0,0,1-.19.08H10.31a.27.27,0,0,1-.19-.08.3.3,0,0,1-.08-.21V12.48a.32.32,0,0,1,.08-.21.24.24,0,0,1,.19-.08h9.56Zm0-3.37a.27.27,0,0,1,.19.08.25.25,0,0,1,.08.19v1.69a.27.27,0,0,1-.08.19.25.25,0,0,1-.19.08H10.31a.27.27,0,0,1-.27-.27V9.1a.27.27,0,0,1,.27-.27h9.56Zm.2-3.29a.28.28,0,0,1,.08.2V7.41a.32.32,0,0,1-.08.21.25.25,0,0,1-.19.08H4.68a.27.27,0,0,1-.19-.08.3.3,0,0,1-.08-.21V5.73a.32.32,0,0,1,.08-.21.25.25,0,0,1,.19-.08H19.87a.28.28,0,0,1,.2.09Z" transform="translate(-4.41 -5.44)"/></g></svg>',outdent:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 12.36"><g><path d="M19.87,15.57a.27.27,0,0,1,.19.08.25.25,0,0,1,.08.19v1.69a.27.27,0,0,1-.08.19.25.25,0,0,1-.19.08H4.68a.27.27,0,0,1-.19-.08.25.25,0,0,1-.08-.19V15.84a.27.27,0,0,1,.27-.27H19.87ZM7.5,14.45a.25.25,0,0,1-.2-.09L4.76,11.84a.29.29,0,0,1,0-.4L7.3,8.9a.29.29,0,0,1,.4,0,.31.31,0,0,1,.07.2v5.06a.32.32,0,0,1-.08.21.26.26,0,0,1-.19.08ZM19.87,8.82a.27.27,0,0,1,.19.08.25.25,0,0,1,.08.19v1.69a.27.27,0,0,1-.08.19.25.25,0,0,1-.19.08H10.31a.27.27,0,0,1-.27-.27V9.1a.27.27,0,0,1,.27-.27h9.56Zm0,3.37a.27.27,0,0,1,.19.08.28.28,0,0,1,.08.21v1.68a.32.32,0,0,1-.08.21.25.25,0,0,1-.19.08H10.31a.27.27,0,0,1-.19-.08.3.3,0,0,1-.08-.21V12.48a.32.32,0,0,1,.08-.21.24.24,0,0,1,.19-.08h9.56Zm.2-6.66a.28.28,0,0,1,.08.2V7.41a.32.32,0,0,1-.08.21.25.25,0,0,1-.19.08H4.68a.27.27,0,0,1-.19-.08.3.3,0,0,1-.08-.21V5.73a.32.32,0,0,1,.08-.21.25.25,0,0,1,.19-.08H19.87a.28.28,0,0,1,.2.09Z" transform="translate(-4.41 -5.44)"/></g></svg>',expansion:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.74"><g><path d="M11.8,13.06l-5.1,5.1H9.51V19.5H4.41V14.4H5.75v2.81L8.3,14.66q2.25-2.23,2.55-2.55Zm8.35-9.3v5.1H18.81V6.05l-5.1,5.1-1-1,5.1-5.1H15.05V3.76Z" transform="translate(-4.41 -3.76)"/></g></svg>',reduction:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.74"><g><path d="M14.91,10h2.87v1.38H12.55V6.12h1.38V9l5.24-5.24.48.49.49.48ZM6.77,11.92H12v5.23H10.62V14.26L5.37,19.5l-1-1L9.63,13.3H6.77Z" transform="translate(-4.4 -3.76)"/></g></svg>',code_view:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.73 11.8"><g><path d="M8.09,7.94a.76.76,0,0,1,.53.22.72.72,0,0,1,.21.52.76.76,0,0,1-.22.54L6.18,11.63l2.43,2.44a.69.69,0,0,1,.2.51.66.66,0,0,1-.21.51.75.75,0,0,1-.51.22.63.63,0,0,1-.51-.21h0L4.63,12.15a.7.7,0,0,1-.22-.53.67.67,0,0,1,.25-.55L7.57,8.16a.82.82,0,0,1,.52-.22Zm12.05,3.69a.7.7,0,0,1-.23.52L17,15.1h0a.66.66,0,0,1-.51.21.73.73,0,0,1-.51-.22.75.75,0,0,1-.22-.51.63.63,0,0,1,.21-.51l2.43-2.44L15.92,9.22a.73.73,0,0,1-.22-.53A.74.74,0,0,1,17,8.18h0l2.91,2.91a.67.67,0,0,1,.27.54Zm-5.9-5.9a.73.73,0,0,1,.61.32.71.71,0,0,1,.07.68L11,17a1,1,0,0,1-.22.32.6.6,0,0,1-.35.16.75.75,0,0,1-.69-.26.69.69,0,0,1-.12-.72L13.56,6.23a.75.75,0,0,1,.26-.35.74.74,0,0,1,.42-.15Z" transform="translate(-4.41 -5.73)"/></g></svg>',preview:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.65 15.66"><g><path d="M16.19,14.43l2.49,2.49a.73.73,0,0,1,.21.52.67.67,0,0,1-.22.51.7.7,0,0,1-.52.22.69.69,0,0,1-.51-.21l-2.49-2.48a5.17,5.17,0,0,1-1.34.69,4.64,4.64,0,0,1-1.48.24,4.78,4.78,0,1,1,0-9.56,4.79,4.79,0,0,1,1.84.36,4.9,4.9,0,0,1,1.56,1,4.77,4.77,0,0,1,.46,6.18ZM10,14a3.3,3.3,0,0,0,2.34.93A3.37,3.37,0,0,0,14.7,14a3.3,3.3,0,0,0-1.08-5.41,3.47,3.47,0,0,0-2.56,0A3,3,0,0,0,10,9.28,3.31,3.31,0,0,0,10,14ZM16,4a3.86,3.86,0,0,1,2.77,1.14A3.9,3.9,0,0,1,20,7.85v4a.77.77,0,0,1-.22.53.7.7,0,0,1-.52.21.72.72,0,0,1-.74-.74v-4a2.46,2.46,0,0,0-.72-1.73A2.37,2.37,0,0,0,16,5.45H8.53A2.42,2.42,0,0,0,6.08,7.89v7.52a2.41,2.41,0,0,0,.71,1.73,2.46,2.46,0,0,0,1.74.72h4.08a.73.73,0,0,1,0,1.46H8.53a3.85,3.85,0,0,1-2.78-1.14A3.93,3.93,0,0,1,4.6,15.4V7.87A3.94,3.94,0,0,1,5.76,5.09,3.88,3.88,0,0,1,8.54,4H16Z" transform="translate(-4.45 -3.8)"/></g></svg>',print:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16.05 16.04"><g><path d="M19.76,15.84a1.29,1.29,0,0,0,.39-.92V8.35A2.05,2.05,0,0,0,19.57,7a1.93,1.93,0,0,0-1.38-.57H6.37a1.95,1.95,0,0,0-2,2v6.56a1.23,1.23,0,0,0,.38.92,1.35,1.35,0,0,0,.93.38h2V14.9l-2,0V8.35a.67.67,0,0,1,.18-.47.62.62,0,0,1,.48-.19H18.18a.6.6,0,0,1,.46.19.66.66,0,0,1,.18.47V14.9h-2v1.32h2A1.35,1.35,0,0,0,19.76,15.84ZM17.52,7.69V5.06a1.31,1.31,0,0,0-.38-.92,1.34,1.34,0,0,0-.94-.38H8.34A1.3,1.3,0,0,0,7,5.06V7.69H8.34V5.06h7.87V7.69h1.31ZM8.34,12.93h7.87l0,5.26H8.34V12.93Zm7.87,5.26v0Zm.65,1.31a.6.6,0,0,0,.46-.19.72.72,0,0,0,.2-.47V12.29a.74.74,0,0,0-.2-.47.6.6,0,0,0-.46-.19H7.68a.6.6,0,0,0-.46.19.72.72,0,0,0-.2.47v6.55a.74.74,0,0,0,.2.47.6.6,0,0,0,.46.19h9.18ZM16.67,9.28a.7.7,0,0,0-.94,0,.63.63,0,0,0-.18.46.67.67,0,0,0,.18.47.68.68,0,0,0,.94,0,.66.66,0,0,0,.18-.47A.58.58,0,0,0,16.67,9.28Z" transform="translate(-4.25 -3.61)"/></g></svg>',template:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14.27 15.64"><g><path d="M18.18,19.16a1,1,0,0,0,1-1V5.73a1,1,0,0,0-1-1h-2v1h2V18.19H6.37V5.73h2v-1h-2A.94.94,0,0,0,5.68,5a1,1,0,0,0-.29.7V18.18a.94.94,0,0,0,.29.69,1,1,0,0,0,.69.29H18.18ZM9.82,10.31h4.92a.49.49,0,0,0,.35-.15.47.47,0,0,0,.15-.35.49.49,0,0,0-.15-.35.47.47,0,0,0-.35-.15H9.82a.49.49,0,0,0-.35.15.47.47,0,0,0-.15.35.49.49,0,0,0,.15.35.47.47,0,0,0,.35.15Zm5.9,4.92H8.83a.49.49,0,0,0-.35.15.47.47,0,0,0-.15.35.49.49,0,0,0,.15.35.47.47,0,0,0,.35.15h6.89a.49.49,0,0,0,.35-.15.47.47,0,0,0,.15-.35.51.51,0,0,0-.5-.5ZM7.36,12.77a.49.49,0,0,0,.15.35.47.47,0,0,0,.35.15h8.85a.49.49,0,0,0,.35-.15.47.47,0,0,0,.15-.35.49.49,0,0,0-.15-.35.47.47,0,0,0-.35-.15H7.85a.49.49,0,0,0-.35.15.52.52,0,0,0-.14.35Z" transform="translate(-5.14 -3.77)"/><path d="M14.24,6.71a1,1,0,0,0,1-1,1,1,0,0,0-1-1,1,1,0,0,0-1-1h-2a.94.94,0,0,0-.69.28,1,1,0,0,0-.29.7A.94.94,0,0,0,9.62,5a.91.91,0,0,0-.29.69,1,1,0,0,0,.29.7,1,1,0,0,0,.69.29h3.93Z" transform="translate(-5.14 -3.77)"/></g></svg>',line_height:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.76 13.56"><g><path d="M4.4,4.88V8.26a2,2,0,0,0,.5.39s.1,0,.18-.12a.62.62,0,0,0,.17-.28c.06-.19.13-.44.21-.74s.14-.52.19-.66a.58.58,0,0,1,.21-.3,2.41,2.41,0,0,1,.63-.21,3.83,3.83,0,0,1,.88-.12,9.15,9.15,0,0,1,1.31.06.16.16,0,0,1,.11,0,.26.26,0,0,1,.06.14,4,4,0,0,1,0,.49v2l.05,3.77c0,1.41,0,2.68-.05,3.81a1.79,1.79,0,0,1-.11.49,10.68,10.68,0,0,1-1.4.45,1.12,1.12,0,0,0-.69.43v.31l0,.22.61,0c.85-.08,1.54-.12,2.06-.12a19.76,19.76,0,0,1,2.09.08,15.08,15.08,0,0,0,1.64.08,1.4,1.4,0,0,0,.29,0,1.58,1.58,0,0,0,0-.26l-.05-.43a2.26,2.26,0,0,0-.43-.17l-.77-.22-.15,0a2.55,2.55,0,0,1-.78-.28,2.56,2.56,0,0,1-.11-.75l0-1.29,0-3.15V7.53a10.51,10.51,0,0,1,.06-1.2,3.83,3.83,0,0,1,.6,0l1.88,0a2.18,2.18,0,0,1,.38,0,.45.45,0,0,1,.23.17.9.9,0,0,1,.05.25c0,.16.06.35.1.58a3.33,3.33,0,0,0,.14.55A6.39,6.39,0,0,0,15,9a2.91,2.91,0,0,0,.6-.15,2.77,2.77,0,0,0,0-.46l0-.51,0-2.95-.25,0-.38,0L15,4.94a.71.71,0,0,1-.18.15.45.45,0,0,1-.25.07l-.29,0H8.75l-.15,0H7.45a17,17,0,0,1-1.86,0L5.36,5l-.25-.13ZM19.75,16.14h-.69v-9h.69A.4.4,0,0,0,20.13,7c.06-.11,0-.24-.1-.39L18.92,5.15a.52.52,0,0,0-.86,0L17,6.58c-.12.15-.16.28-.1.39s.18.16.38.16h.69v9h-.69a.4.4,0,0,0-.38.16c-.06.11,0,.24.1.39l1.11,1.43a.52.52,0,0,0,.86,0L20,16.69c.12-.15.16-.28.1-.39a.4.4,0,0,0-.38-.16Z" transform="translate(-4.4 -4.86)"/></g></svg>',paragraph_style:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 11.81 15.74"><g><path d="M18.18,3.76v2h-2V19.5h-2V5.73h-2V19.5h-2V11.63a3.94,3.94,0,0,1,0-7.87h7.87Z" transform="translate(-6.37 -3.76)"/></g></svg>',text_style:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13.76 15.74"><g><path d="M17.68,6.71a2.22,2.22,0,0,0,1.06-.22.74.74,0,0,0,.42-.7.73.73,0,0,0-.08-.33.67.67,0,0,0-.17-.22,1,1,0,0,0-.31-.15L18.26,5l-.45-.09A15.27,15.27,0,0,0,13.26,5V4.74c0-.66-.63-1-1.92-1-.24,0-.43.15-.59.46a4,4,0,0,0-.36,1.14h0v0a26.45,26.45,0,0,1-3.5.35A2,2,0,0,0,5.77,6a.84.84,0,0,0-.37.79,2.14,2.14,0,0,0,.41,1.29,1.23,1.23,0,0,0,1.05.63,16.62,16.62,0,0,0,3.29-.45l-.34,3.35c-.16,1.61-.29,2.9-.37,3.86s-.12,1.66-.12,2.09l0,.65a5.15,5.15,0,0,0,.05.6,1.28,1.28,0,0,0,.16.54.34.34,0,0,0,.28.18,1.16,1.16,0,0,0,.79-.46,3.66,3.66,0,0,0,.68-1,22.08,22.08,0,0,0,1-4.33q.49-3.1.78-6.15a24.69,24.69,0,0,1,4.62-.84Z" transform="translate(-5.4 -3.76)"/></g></svg>',save:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.74"><g><path d="M18.53,19.5l.2-.05A1.78,1.78,0,0,0,20.13,18l0-.09V7.14a2,2,0,0,0-.28-.64A3.18,3.18,0,0,0,19.43,6c-.5-.52-1-1-1.55-1.54A2.59,2.59,0,0,0,17.37,4a1.83,1.83,0,0,0-.61-.25H6l-.21,0a1.78,1.78,0,0,0-1.4,1.49l0,.1V17.87a2.49,2.49,0,0,0,.09.37,1.79,1.79,0,0,0,1.44,1.23l.09,0Zm-6.25-.6H6.92a.61.61,0,0,1-.68-.48.78.78,0,0,1,0-.22V12.3a.62.62,0,0,1,.69-.68H17.64a.62.62,0,0,1,.69.69V18.2a.64.64,0,0,1-.71.69H12.28ZM12,9.81H8.15a.63.63,0,0,1-.72-.71v-4a.64.64,0,0,1,.72-.72h7.66a.64.64,0,0,1,.72.72v4a.65.65,0,0,1-.74.72ZM13.5,5V9.18h1.78V5Z" transform="translate(-4.41 -3.76)"/></g></svg>',blockquote:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 475.082 475.081"><g><path d="M164.45,219.27h-63.954c-7.614,0-14.087-2.664-19.417-7.994c-5.327-5.33-7.994-11.801-7.994-19.417v-9.132c0-20.177,7.139-37.401,21.416-51.678c14.276-14.272,31.503-21.411,51.678-21.411h18.271c4.948,0,9.229-1.809,12.847-5.424c3.616-3.617,5.424-7.898,5.424-12.847V54.819c0-4.948-1.809-9.233-5.424-12.85c-3.617-3.612-7.898-5.424-12.847-5.424h-18.271c-19.797,0-38.684,3.858-56.673,11.563c-17.987,7.71-33.545,18.132-46.68,31.267c-13.134,13.129-23.553,28.688-31.262,46.677C3.855,144.039,0,162.931,0,182.726v200.991c0,15.235,5.327,28.171,15.986,38.834c10.66,10.657,23.606,15.985,38.832,15.985h109.639c15.225,0,28.167-5.328,38.828-15.985c10.657-10.663,15.987-23.599,15.987-38.834V274.088c0-15.232-5.33-28.168-15.994-38.832C192.622,224.6,179.675,219.27,164.45,219.27z"/><path d="M459.103,235.256c-10.656-10.656-23.599-15.986-38.828-15.986h-63.953c-7.61,0-14.089-2.664-19.41-7.994c-5.332-5.33-7.994-11.801-7.994-19.417v-9.132c0-20.177,7.139-37.401,21.409-51.678c14.271-14.272,31.497-21.411,51.682-21.411h18.267c4.949,0,9.233-1.809,12.848-5.424c3.613-3.617,5.428-7.898,5.428-12.847V54.819c0-4.948-1.814-9.233-5.428-12.85c-3.614-3.612-7.898-5.424-12.848-5.424h-18.267c-19.808,0-38.691,3.858-56.685,11.563c-17.984,7.71-33.537,18.132-46.672,31.267c-13.135,13.129-23.559,28.688-31.265,46.677c-7.707,17.987-11.567,36.879-11.567,56.674v200.991c0,15.235,5.332,28.171,15.988,38.834c10.657,10.657,23.6,15.985,38.828,15.985h109.633c15.229,0,28.171-5.328,38.827-15.985c10.664-10.663,15.985-23.599,15.985-38.834V274.088C475.082,258.855,469.76,245.92,459.103,235.256z"/></g></svg>',arrow_down:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.73 8.67"><g><path d="M18.79,7.52a.8.8,0,0,1,.56-.23.82.82,0,0,1,.79.79.8.8,0,0,1-.23.56l-7.07,7.07a.79.79,0,0,1-.57.25.77.77,0,0,1-.57-.25h0L4.64,8.65a.8.8,0,0,1-.23-.57.82.82,0,0,1,.79-.79.8.8,0,0,1,.56.23L12.28,14l3.26-3.26,3.25-3.26Z" transform="translate(-4.41 -7.29)"/></g></svg>',align_justify:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 13.77"><g><path d="M4.41,4.74v2H20.15v-2H4.41Zm0,5.9H20.15v-2H4.41v2Zm0,3.94H20.15v-2H4.41v2Zm0,3.93h7.87v-2H4.41v2Z" transform="translate(-4.41 -4.74)"/></g></svg>',align_left:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 13.77"><g><path d="M4.41,4.74v2H20.15v-2H4.41Zm11.8,3.94H4.41v2H16.22v-2Zm-11.8,5.9H18.18v-2H4.41v2Zm0,3.93h9.84v-2H4.41v2Z" transform="translate(-4.41 -4.74)"/></g></svg>',align_right:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 13.77"><g><path d="M4.41,4.74v2H20.15v-2H4.41Zm3.93,5.9H20.15v-2H8.34v2Zm-2,3.94H20.14v-2H6.37v2Zm3.94,3.93h9.84v-2H10.31v2Z" transform="translate(-4.41 -4.74)"/></g></svg>',align_center:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 13.77"><g><path d="M4.41,4.74v2H20.15v-2H4.41Zm2,3.94v2H18.18v-2H6.37Zm-1,5.9H19.16v-2H5.39v2Zm2,3.93H17.2v-2H7.36v2Z" transform="translate(-4.41 -4.74)"/></g></svg>',font_color:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 14.61"><g><path d="M18.5,15.57,14.28,4.32h-3.4L6.65,15.57h3l.8-2.26h4.23l.8,2.26h3ZM14,11.07H11.14L12.54,7,13.25,9c.41,1.18.64,1.86.7,2ZM4.41,16.69v2.24H20.15V16.69H4.41Z" transform="translate(-4.41 -4.32)"/></g></svg>',highlight_color:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.66 15.74"><g><path d="M12.32,9.31,13.38,13H11.21l.52-1.83q.46-1.61.54-1.83ZM4.44,3.76H20.1V19.5H4.44V3.76ZM14.71,17.32h2.63L13.7,6H10.89L7.26,17.32H9.89l.63-2.24h3.55l.32,1.12c.18.65.29,1,.32,1.12Z" transform="translate(-4.44 -3.76)"/></g></svg>',list_bullets:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 12.37"><g><path d="M7.77,16.12a1.59,1.59,0,0,0-.49-1.18,1.62,1.62,0,0,0-1.19-.49,1.68,1.68,0,1,0,0,3.36,1.67,1.67,0,0,0,1.68-1.69Zm0-4.48A1.67,1.67,0,0,0,6.09,10,1.68,1.68,0,0,0,4.9,12.82a1.62,1.62,0,0,0,1.19.49,1.67,1.67,0,0,0,1.68-1.67Zm12.38,3.64a.27.27,0,0,0-.08-.19.28.28,0,0,0-.2-.09H9.19a.28.28,0,0,0-.2.08.29.29,0,0,0-.08.19V17a.27.27,0,0,0,.28.28H19.87a.27.27,0,0,0,.19-.08.24.24,0,0,0,.08-.2V15.28ZM7.77,7.13a1.63,1.63,0,0,0-.49-1.2,1.61,1.61,0,0,0-1.19-.49,1.61,1.61,0,0,0-1.19.49,1.71,1.71,0,0,0,0,2.4,1.62,1.62,0,0,0,1.19.49,1.61,1.61,0,0,0,1.19-.49,1.63,1.63,0,0,0,.49-1.2Zm12.38,3.66a.28.28,0,0,0-.08-.2.29.29,0,0,0-.19-.08H9.19a.27.27,0,0,0-.28.28v1.69a.27.27,0,0,0,.08.19.24.24,0,0,0,.2.08H19.87a.27.27,0,0,0,.19-.08.25.25,0,0,0,.08-.19V10.79Zm0-4.5a.27.27,0,0,0-.08-.19A.25.25,0,0,0,19.88,6H9.19A.28.28,0,0,0,9,6.1a.26.26,0,0,0-.08.19V8A.27.27,0,0,0,9,8.17a.24.24,0,0,0,.2.08H19.87a.27.27,0,0,0,.19-.08A.25.25,0,0,0,20.14,8V6.29Z" transform="translate(-4.41 -5.44)"/></g></svg>',list_number:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.69 15.74"><g><path d="M7.66,18a1.24,1.24,0,0,0-.26-.78,1.17,1.17,0,0,0-.72-.42l.85-1V15H4.58v1.34h.94v-.46l.85,0h0c-.11.11-.22.23-.32.35s-.23.27-.37.47L5.39,17l.23.51c.61-.05.92.11.92.49a.42.42,0,0,1-.18.37.79.79,0,0,1-.45.12A1.41,1.41,0,0,1,5,18.15l-.51.77A2.06,2.06,0,0,0,6,19.5a1.8,1.8,0,0,0,1.2-.41A1.38,1.38,0,0,0,7.66,18Zm0-5.54H6.75V13H5.63A.72.72,0,0,1,6,12.51a5.45,5.45,0,0,1,.66-.45,2.71,2.71,0,0,0,.67-.57,1.19,1.19,0,0,0,.31-.81,1.29,1.29,0,0,0-.45-1,1.86,1.86,0,0,0-2-.11,1.51,1.51,0,0,0-.62.7l.74.52A.87.87,0,0,1,6,10.28a.51.51,0,0,1,.35.12.42.42,0,0,1,.13.33.55.55,0,0,1-.21.4,3,3,0,0,1-.5.38c-.19.13-.39.27-.58.42a2,2,0,0,0-.5.6,1.63,1.63,0,0,0-.21.81,3.89,3.89,0,0,0,.05.48h3.2V12.44Zm12.45,2.82a.27.27,0,0,0-.08-.19.28.28,0,0,0-.21-.08H9.1a.32.32,0,0,0-.21.08.24.24,0,0,0-.08.2V17a.27.27,0,0,0,.08.19.3.3,0,0,0,.21.08H19.83a.32.32,0,0,0,.21-.08.25.25,0,0,0,.08-.19V15.26ZM7.69,7.32h-1V3.76H5.8L4.6,4.88l.63.68a1.85,1.85,0,0,0,.43-.48h0l0,2.24H4.74V8.2h3V7.32Zm12.43,3.42a.27.27,0,0,0-.08-.19.28.28,0,0,0-.21-.08H9.1a.32.32,0,0,0-.21.08.24.24,0,0,0-.08.2v1.71a.27.27,0,0,0,.08.19.3.3,0,0,0,.21.08H19.83a.32.32,0,0,0,.21-.08.25.25,0,0,0,.08-.19V10.74Zm0-4.52A.27.27,0,0,0,20,6,.28.28,0,0,0,19.83,6H9.1A.32.32,0,0,0,8.89,6a.24.24,0,0,0-.08.19V7.93a.27.27,0,0,0,.08.19.32.32,0,0,0,.21.08H19.83A.32.32,0,0,0,20,8.12a.26.26,0,0,0,.08-.2V6.22Z" transform="translate(-4.43 -3.76)"/></g></svg>',table:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.74"><g><path d="M4.41,8.05V3.76H8.7V8.05H4.41Zm5.71,0V3.76h4.3V8.05h-4.3Zm5.74-4.29h4.29V8.05H15.86V3.76Zm-11.45,10V9.48H8.7v4.3H4.41Zm5.71,0V9.48h4.3v4.3h-4.3Zm5.74,0V9.48h4.29v4.3H15.86ZM4.41,19.5V15.21H8.7V19.5H4.41Zm5.71,0V15.21h4.3V19.5h-4.3Zm5.74,0V15.21h4.29V19.5H15.86Z" transform="translate(-4.41 -3.76)"/></g></svg>',horizontal_rule:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 2.24"><g><path d="M20.15,12.75V10.51H4.41v2.24H20.15Z" transform="translate(-4.41 -10.51)"/></g></svg>',show_blocks:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.66 15.67"><g><path d="M19.72,5.58a1.64,1.64,0,0,0-1.64-1.64H6.23a1.62,1.62,0,0,0-1.16.48,1.63,1.63,0,0,0-.48,1.16V9.63a1.6,1.6,0,0,0,.48,1.16,1.62,1.62,0,0,0,1.16.47H18.09a1.67,1.67,0,0,0,1.16-.47,1.62,1.62,0,0,0,.48-1.16V5.58Zm-.94,4.05a.68.68,0,0,1-.7.7H6.23a.66.66,0,0,1-.48-.2.74.74,0,0,1-.21-.5V5.58a.66.66,0,0,1,.2-.48.71.71,0,0,1,.48-.21H18.08a.74.74,0,0,1,.5.21.66.66,0,0,1,.2.48ZM6.48,7.72a.21.21,0,0,0,.17-.07.22.22,0,0,0,.07-.17V7.06a1.27,1.27,0,0,1,.11-.52.37.37,0,0,1,.36-.23H8.77A.25.25,0,0,0,9,6.17a.19.19,0,0,0,0-.23.27.27,0,0,0-.2-.12H7.19a.88.88,0,0,0-.72.39,1.51,1.51,0,0,0-.23.85v.42a.24.24,0,0,0,.24.24Zm-.19.81a.21.21,0,0,0,.17-.07.26.26,0,0,0,.07-.17.24.24,0,0,0-.24-.24.2.2,0,0,0-.16.09.2.2,0,0,0-.07.16.22.22,0,0,0,.07.17.23.23,0,0,0,.16.06Zm8.46,5.1a1.63,1.63,0,0,0-.47-1.16A1.61,1.61,0,0,0,13.12,12H6.23a1.6,1.6,0,0,0-1.16.46,1.62,1.62,0,0,0-.48,1.16v4.05a1.64,1.64,0,0,0,1.64,1.64h6.89a1.6,1.6,0,0,0,1.16-.48,1.62,1.62,0,0,0,.47-1.16Zm-.94,4a.7.7,0,0,1-.2.49.65.65,0,0,1-.5.2H6.23a.66.66,0,0,1-.48-.2.75.75,0,0,1-.21-.49v-4a.74.74,0,0,1,.21-.5.66.66,0,0,1,.48-.2h6.89a.68.68,0,0,1,.7.7v4Zm6.15,0v-4a1.6,1.6,0,0,0-.48-1.16A1.67,1.67,0,0,0,18.32,12H17.1a1.63,1.63,0,0,0-1.16.47,1.61,1.61,0,0,0-.47,1.16v4a1.67,1.67,0,0,0,.47,1.16,1.62,1.62,0,0,0,1.16.48h1.22A1.64,1.64,0,0,0,20,17.68Zm-.94-4v4a.75.75,0,0,1-.21.49.62.62,0,0,1-.48.2H17.11a.69.69,0,0,1-.5-.2.7.7,0,0,1-.2-.49v-4a.68.68,0,0,1,.7-.7h1.22a.66.66,0,0,1,.48.2.72.72,0,0,1,.21.5Z" transform="translate(-4.44 -3.79)"/></g></svg>',cancel:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.74"><g><path d="M14.15,11.63l5.61,5.61a1.29,1.29,0,0,1,.38.93,1.27,1.27,0,0,1-.4.93,1.25,1.25,0,0,1-.92.4,1.31,1.31,0,0,1-.94-.4l-5.61-5.61L6.67,19.1a1.31,1.31,0,0,1-.94.4,1.24,1.24,0,0,1-.92-.4,1.27,1.27,0,0,1-.4-.93,1.33,1.33,0,0,1,.38-.93l5.61-5.63L4.79,6a1.26,1.26,0,0,1-.38-.93,1.22,1.22,0,0,1,.4-.92,1.28,1.28,0,0,1,.92-.39,1.38,1.38,0,0,1,.94.38l5.61,5.61,5.61-5.61a1.33,1.33,0,0,1,.94-.38,1.26,1.26,0,0,1,.92.39,1.24,1.24,0,0,1,.4.92,1.29,1.29,0,0,1-.39.93L17,8.81l-2.8,2.82Z" transform="translate(-4.41 -3.76)"/></g></svg>',image:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 15.77"><g><path d="M8.77,8.72a.88.88,0,0,1-.61-.27.82.82,0,0,1-.25-.61.89.89,0,0,1,.25-.62A.82.82,0,0,1,8.77,7a.81.81,0,0,1,.61.25.83.83,0,0,1,.27.62.81.81,0,0,1-.25.61.91.91,0,0,1-.63.27Zm9.62-5a1.74,1.74,0,0,1,1.76,1.76V17.76a1.74,1.74,0,0,1-1.76,1.76H6.16A1.74,1.74,0,0,1,4.4,17.76V5.51A1.74,1.74,0,0,1,6.16,3.75H18.39Zm0,1.75H6.16v8L8.53,11.8a.94.94,0,0,1,.54-.17.86.86,0,0,1,.54.2L11.09,13l3.64-4.55a.78.78,0,0,1,.34-.25.85.85,0,0,1,.42-.07.89.89,0,0,1,.39.12.78.78,0,0,1,.28.29l2.24,3.67V5.51Zm0,12.24V15.6L15.3,10.53,11.89,14.8a.89.89,0,0,1-.59.32.82.82,0,0,1-.64-.18L9,13.62,6.16,15.74v2Z" transform="translate(-4.4 -3.75)"/></g></svg>',video:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 14.55"><g><path d="M20.15,10.26V18.9l-3.94-1.57v1.2H4.41V10.66H16.22v1.23l2-.81,2-.82ZM14.64,17h0V12.54h0v-.31H6V17h8.67Zm3.94-.37v-4l-2.37,1v2l1.18.48,1.19.48ZM7.94,9.86A2.77,2.77,0,0,1,5.19,7.11a2.76,2.76,0,0,1,5.51,0A2.78,2.78,0,0,1,7.94,9.86Zm0-3.93a1.21,1.21,0,0,0-.83.35,1.15,1.15,0,0,0-.34.84A1.09,1.09,0,0,0,7.11,8,1.15,1.15,0,0,0,8,8.28,1.13,1.13,0,0,0,9.11,7.12,1.16,1.16,0,0,0,7.94,5.93Zm5.9,3.93a2.34,2.34,0,0,1-1.67-.68,2.3,2.3,0,0,1-.68-1.67,2.35,2.35,0,0,1,4-1.67,2.37,2.37,0,0,1,0,3.34,2.33,2.33,0,0,1-1.68.68Zm0-3.14a.75.75,0,1,0,.55.22.73.73,0,0,0-.55-.22Z" transform="translate(-4.41 -4.35)"/></g></svg>',link:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.72"><g><path d="M13.05,13.63a.24.24,0,0,1,.15.22L13.42,16a.19.19,0,0,1-.08.18l-2.12,2.14a4.08,4.08,0,0,1-1.29.85A4,4,0,0,1,4.71,17a3.92,3.92,0,0,1-.3-1.52A4,4,0,0,1,4.71,14a3.91,3.91,0,0,1,.87-1.3L7.7,10.56a.25.25,0,0,1,.2-.06l2.17.22a.21.21,0,0,1,.19.15.24.24,0,0,1,0,.25L7.12,14.23a1.81,1.81,0,0,0,0,2.58,1.78,1.78,0,0,0,1.29.52,1.74,1.74,0,0,0,1.28-.52L12.8,13.7a.24.24,0,0,1,.25-.07ZM19,4.92a4,4,0,0,1,0,5.66L16.86,12.7a.25.25,0,0,1-.17.08l-2.2-.23a.21.21,0,0,1-.19-.15.22.22,0,0,1,0-.25L17.44,9a1.81,1.81,0,0,0,0-2.58,1.78,1.78,0,0,0-1.29-.52,1.74,1.74,0,0,0-1.28.52L11.76,9.57a.21.21,0,0,1-.25,0,.24.24,0,0,1-.16-.21l-.22-2.17a.19.19,0,0,1,.08-.18l2.12-2.14a4.08,4.08,0,0,1,1.29-.85,4.05,4.05,0,0,1,3.06,0,3.85,3.85,0,0,1,1.3.85ZM5.84,9.82a.25.25,0,0,1-.18-.08.19.19,0,0,1-.07-.19l.11-.77a.2.2,0,0,1,.11-.17.24.24,0,0,1,.2,0l2.5.72a.25.25,0,0,1,.15.27.22.22,0,0,1-.23.21l-2.59,0Zm4.12-2-.73-2.5a.27.27,0,0,1,0-.2A.21.21,0,0,1,9.41,5L10.19,5a.25.25,0,0,1,.19,0,.23.23,0,0,1,.08.18l-.05,2.61a.2.2,0,0,1-.19.23h0A.22.22,0,0,1,10,7.85Zm8.76,5.58a.25.25,0,0,1,.18.08.23.23,0,0,1,.06.2l-.11.77a.25.25,0,0,1-.11.17.21.21,0,0,1-.12,0l-.08,0L16,14a.25.25,0,0,1-.15-.27.22.22,0,0,1,.22-.21l1.29,0,1.33,0Zm-4.12,2,.74,2.51a.28.28,0,0,1,0,.2.23.23,0,0,1-.18.11l-.8.11a.23.23,0,0,1-.17-.07.25.25,0,0,1-.08-.18l0-2.61a.22.22,0,0,1,.22-.22.21.21,0,0,1,.26.15Z" transform="translate(-4.41 -3.77)"/></g></svg>',math:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 11.81 15.73"><g><path d="M17.19,5.73a1,1,0,0,0,.71-.29,1,1,0,0,0,.28-.7,1,1,0,0,0-1-1H7.35a1,1,0,0,0-1,1,.77.77,0,0,0,.13.47h0l4.58,6.43L6.68,17.81a1.25,1.25,0,0,0-.29.71.94.94,0,0,0,.28.7.92.92,0,0,0,.69.28H17.2a1,1,0,0,0,.71-.28,1,1,0,0,0,0-1.39.92.92,0,0,0-.71-.29H9.26l3.87-5.43a.86.86,0,0,0,0-.95L9.26,5.73h7.93Z" transform="translate(-6.38 -3.77)"/></g></svg>',unlink:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.72"><g><path d="M19,18.32a4,4,0,0,0,0-5.68L15.85,9.5l-1.17,1.55L17.57,14a2,2,0,0,1,.61,1.47,2.08,2.08,0,0,1-2.09,2.09,2,2,0,0,1-1.47-.61l-.38-.37-1.74,1,.8.78a4,4,0,0,0,5.68,0ZM8,9.77a2,2,0,0,1-1.27-1,1.89,1.89,0,0,1-.21-1.57A2.1,2.1,0,0,1,7.45,6,2,2,0,0,1,9,5.76L12.27,7.2l.49-2L9.48,3.9a4,4,0,0,0-3.06.41A3.82,3.82,0,0,0,4.56,6.73a3.8,3.8,0,0,0,.4,3A3.78,3.78,0,0,0,7.39,11.6l5.38,2,.49-2-2.64-.94L8,9.77Z" transform="translate(-4.41 -3.76)"/></g></svg>',table_header:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 15.74"><g><path d="M17,19.5v-.78H15.5v.78H17Zm-3,0v-.78H12.5v.78H14Zm-3,0v-.78H9.53v.78H11Zm-3,0v-.78H6.53v.78H8Zm10.55,0a1.73,1.73,0,0,0,.85-.35,1.67,1.67,0,0,0,.56-.76l-.71-.31a1.21,1.21,0,0,1-.35.4,1.34,1.34,0,0,1-.53.23l.08.38c.06.24.09.38.1.41Zm-13.7-.63.55-.55A.77.77,0,0,1,5.25,18a1.31,1.31,0,0,1-.06-.38v-.38H4.41v.38a2,2,0,0,0,.12.68,1.6,1.6,0,0,0,.35.57Zm15.27-2.12V15.26h-.78v1.49h.78Zm-15-1V14.23H4.41v1.49h.78Zm15-2V12.26h-.78v1.49h.78Zm-15-1V11.22H4.41v1.51h.78Zm15-2V9.26h-.78v1.51h.78Zm-15-1V8.17H4.41V9.74h.78Zm15-2V6.28h-.78V7.77h.78Zm-15-1.11V5.33L4.48,5.1a.77.77,0,0,0-.07.27,2.72,2.72,0,0,0,0,.28v1h.79ZM19.21,5l.63-.4A1.62,1.62,0,0,0,19.16,4a1.94,1.94,0,0,0-.91-.22v.78a1.31,1.31,0,0,1,.56.12.88.88,0,0,1,.4.36ZM6,4.54H7.78V3.76H6a.82.82,0,0,0-.28.06l.12.35c.07.21.1.33.11.36Zm10.8,0V3.76H15.28v.78h1.49Zm-3,0V3.76H12.28v.78h1.49Zm-3,0V3.76H9.28v.78h1.51ZM6,10.84h12.6V6.91H6Z" transform="translate(-4.4 -3.76)"/></g></svg>',merge_cell:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.76 15.74"><g><path d="M18.92,13.5h1.23v4.15A1.84,1.84,0,0,1,18.3,19.5H14V18.27H18.3a.6.6,0,0,0,.44-.18.59.59,0,0,0,.18-.44V13.5ZM18.3,3.76a1.84,1.84,0,0,1,1.85,1.85V9.82H18.92V5.6a.6.6,0,0,0-.18-.44A.59.59,0,0,0,18.3,5H14V3.76H18.3Zm1.85,8.51H15.6L17.26,14l-.86.86-3.14-3.17L16.4,8.51l.86.86L15.62,11h4.54v1.24Zm-13.9,6h4.27V19.5H6.25A1.84,1.84,0,0,1,4.4,17.65V13.5H5.63v4.15a.61.61,0,0,0,.62.62Zm0-14.51h4.27V5H6.25a.6.6,0,0,0-.44.18.57.57,0,0,0-.17.43V9.81H4.41V5.6A1.83,1.83,0,0,1,6.25,3.76Zm5,7.9L8.15,14.83,7.3,14,9,12.27H4.41V11H8.94L7.3,9.38,7.73,9l.43-.43Z" transform="translate(-4.4 -3.76)"/></g></svg>',split_cell:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 15.74"><g><path d="M10.37,12.25H6.74L8.4,13.94l-.87.86L4.41,11.63,7.53,8.5l.87.86L6.74,11h3.62v1.23Zm9.78-.61L17,14.81,16.13,14l1.66-1.69H14.16V11h3.63L16.13,9.37l.43-.43A5.24,5.24,0,0,1,17,8.51ZM18.9,8.22V5.61a.57.57,0,0,0-.18-.43A.65.65,0,0,0,18.29,5H12.88V18.28h5.41a.7.7,0,0,0,.44-.18.57.57,0,0,0,.18-.43V15h1.23v2.64a1.84,1.84,0,0,1-1.85,1.83h-12A1.84,1.84,0,0,1,4.94,19a1.81,1.81,0,0,1-.54-1.29V15H5.63v2.64a.57.57,0,0,0,.18.43.67.67,0,0,0,.44.18h5.41V5H6.25a.7.7,0,0,0-.44.18.56.56,0,0,0-.17.43V8.22H4.41V5.61A1.8,1.8,0,0,1,5,4.31a1.91,1.91,0,0,1,1.31-.55h12a1.89,1.89,0,0,1,1.31.55,1.8,1.8,0,0,1,.54,1.3V8.23H18.9Z" transform="translate(-4.4 -3.76)"/></g></svg>',caption:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 13.79"><g><path d="M4.41,18.52H20.15v-2H4.41ZM20,4.73H18.07V6h.65v.65H20V4.73ZM17,6V4.73H14.55V6H17ZM13.49,6V4.73H11V6h2.47ZM10,6V4.73H7.5V6H10ZM5.79,6h.65V4.73H4.5V6.67H5.8V6ZM4.5,11.34H5.79V8.48H4.5ZM6.44,13.8H5.79v-.65H4.5v1.94H6.44ZM17,15.09V13.8H14.55v1.29H17Zm-3.52,0V13.8H11v1.29h2.47Zm-3.53,0V13.8H7.5v1.29H10ZM20,13.16H18.72v.65h-.65V15.1H20Zm-1.29-1.82H20V8.48h-1.3v2.86Z" transform="translate(-4.41 -4.73)"/></g></svg>',edit:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.73"><g><path d="M7.51,5.68h6l1.52-1.57H6.94a2.4,2.4,0,0,0-1.79.82A2.8,2.8,0,0,0,4.41,6.8V17a2.55,2.55,0,0,0,.75,1.8A2.48,2.48,0,0,0,7,19.5H17.22a2.57,2.57,0,0,0,1.83-.74,2.52,2.52,0,0,0,.77-1.8V8.83l-1.58,1.54v6a1.54,1.54,0,0,1-1.53,1.53H7.51A1.54,1.54,0,0,1,6,16.41V7.21A1.52,1.52,0,0,1,7.51,5.68Zm5.63,7.47h0L10.7,10.74l-1,3.38,1.71-.48,1.7-.49Zm.34-.34h0l5.36-5.32L16.4,5.08,11,10.4l1.23,1.21,1.21,1.2ZM19.93,6.4a.82.82,0,0,0,.22-.48A.54.54,0,0,0,20,5.47L18.45,4A.67.67,0,0,0,18,3.77a.7.7,0,0,0-.48.21l-.74.72,2.44,2.43.37-.37.35-.36Z" transform="translate(-4.41 -3.77)"/></g></svg>',delete:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.73 15.74"><g><path d="M19.16,6.71a.94.94,0,0,0,.69-.28.91.91,0,0,0,.29-.68A1,1,0,0,0,19.85,5a.93.93,0,0,0-.69-.3H14.24A.94.94,0,0,0,14,4.06a.92.92,0,0,0-.7-.3h-2a1,1,0,0,0-.7.3.93.93,0,0,0-.28.68H5.39A.92.92,0,0,0,4.7,5a1,1,0,0,0-.29.71.91.91,0,0,0,.29.68,1,1,0,0,0,.69.28H19.16Zm-12.79,1a1,1,0,0,0-.7.3.94.94,0,0,0-.28.69v8.85A1.88,1.88,0,0,0,6,18.93a1.9,1.9,0,0,0,1.39.57H17.2a1.87,1.87,0,0,0,1.39-.58,1.91,1.91,0,0,0,.58-1.39V8.68A1,1,0,0,0,18.88,8a.89.89,0,0,0-.7-.29,1,1,0,0,0-.69.29.92.92,0,0,0-.29.68v7.87a1,1,0,0,1-1,1H8.34a.94.94,0,0,1-.69-.28,1,1,0,0,1-.29-.71V8.68a1,1,0,0,0-1-1Z" transform="translate(-4.41 -3.76)"/></g></svg>',modify:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 15.74"><g><path d="M19.79,15.23a.66.66,0,0,1,.3.38.59.59,0,0,1-.07.48l-.8,1.38a.66.66,0,0,1-.38.3.59.59,0,0,1-.48-.07l-.68-.38a4.55,4.55,0,0,1-1.34.77v.78a.64.64,0,0,1-.18.45.61.61,0,0,1-.45.18h-1.6a.6.6,0,0,1-.44-.18.66.66,0,0,1-.19-.45v-.78a4.36,4.36,0,0,1-1.32-.77l-.69.38a.58.58,0,0,1-.48.07.66.66,0,0,1-.38-.3l-.38-.66h.83a1.77,1.77,0,0,0,1.23-.52,1.72,1.72,0,0,0,.51-1.23v-.18a3,3,0,0,0,.49-.28l.15.09a1.83,1.83,0,0,0,.88.23A1.75,1.75,0,0,0,15.84,14l.88-1.52a1.7,1.7,0,0,0,.17-1.32,1.66,1.66,0,0,0-.3-.61,1.84,1.84,0,0,0-.51-.45l-.15-.09,0-.29,0-.28.15-.09a1,1,0,0,0,.26-.18l0,.06v.78a4.34,4.34,0,0,1,1.34.77l.68-.38a.68.68,0,0,1,.48-.06.64.64,0,0,1,.38.29l.8,1.38a.58.58,0,0,1,.07.48.63.63,0,0,1-.3.38l-.68.4a3.84,3.84,0,0,1,.08.76,4.13,4.13,0,0,1-.08.78l.34.18.32.2ZM10.17,7.86a1.9,1.9,0,0,1,1.35,3.23,1.85,1.85,0,0,1-1.35.55A1.9,1.9,0,0,1,8.83,8.41a1.92,1.92,0,0,1,1.34-.55Zm1.58,7.2a.73.73,0,0,1-.21.49.66.66,0,0,1-.48.2H9.29a.68.68,0,0,1-.69-.69V14.2a4.75,4.75,0,0,1-1.48-.86l-.75.45a.73.73,0,0,1-.7,0,.63.63,0,0,1-.25-.26L4.54,12a.67.67,0,0,1-.08-.53.71.71,0,0,1,.32-.42l.75-.43a4.8,4.8,0,0,1-.08-.85,4.71,4.71,0,0,1,.08-.85l-.74-.44a.71.71,0,0,1-.32-.42.65.65,0,0,1,.07-.54L5.42,6a.66.66,0,0,1,.42-.32l.18,0a.73.73,0,0,1,.35.09l.75.43A4.68,4.68,0,0,1,8.6,5.33V4.45a.68.68,0,0,1,.69-.69h1.77a.64.64,0,0,1,.48.2.73.73,0,0,1,.21.49v.88a4.75,4.75,0,0,1,1.48.85L14,5.75a.67.67,0,0,1,.34-.09l.18,0a.71.71,0,0,1,.42.32l.89,1.54a.67.67,0,0,1,.06.52.73.73,0,0,1-.32.43l-.75.42a4.8,4.8,0,0,1,.08.85,4.71,4.71,0,0,1-.08.85l.75.43a.66.66,0,0,1,.32.42.73.73,0,0,1-.06.54l-.89,1.52a.69.69,0,0,1-.25.26.7.7,0,0,1-.35.09.64.64,0,0,1-.34-.09l-.75-.45a4.87,4.87,0,0,1-1.48.86v.87ZM7.23,9.75a3,3,0,0,0,.86,2.08,2.94,2.94,0,1,0,4.16-4.16,3,3,0,0,0-2.08-.85A2.94,2.94,0,0,0,7.23,9.75Z" transform="translate(-4.44 -3.76)"/></g></svg>',revert:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.76 14.69"><g><path d="M18.26,15V12.3l1.89-2V15a2.58,2.58,0,0,1-.24,1c-.2.58-.75.92-1.65,1H7.56v2L4.41,15.63,7.56,13v2h10.7ZM6.3,8.28V11L4.41,13V8.28a2.58,2.58,0,0,1,.24-1c.2-.58.75-.92,1.65-1H17v-2l3.15,3.34L17,10.3v-2H6.3Z" transform="translate(-4.4 -4.28)"/></g></svg>',auto_size:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.74"><g><path d="M6.71,17.19,6.89,16l1.21-.15A6,6,0,0,1,6.81,13.9a5.78,5.78,0,0,1-.45-2.27A6,6,0,0,1,8.1,7.45a5.83,5.83,0,0,1,4.17-1.73l1-1-1-1A7.89,7.89,0,0,0,5,14.64a7.73,7.73,0,0,0,1.71,2.55Zm5.57,2.31h0A7.86,7.86,0,0,0,17.85,6.07L17.67,7.3l-1.21.15a5.9,5.9,0,0,1,1.29,1.92,5.81,5.81,0,0,1,.45,2.26,5.91,5.91,0,0,1-5.9,5.9l-1,1,.49.49.47.5Z" transform="translate(-4.41 -3.76)"/></g></svg>',insert_row_below:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.8 15.8"><g><path d="M15.7,1.3c-0.1-0.1-0.1-0.2-0.2-0.2L15.3,1H0.4L0.3,1.1c0,0-0.1,0.1-0.1,0.1c0,0-0.1,0.1-0.1,0.1L0,1.4v7.7l0.1,0.1c0,0.1,0.1,0.1,0.2,0.2l0.1,0.1h2.3V9.3l0.1-0.5L3,8.5l0.1-0.2c-0.1,0-0.2,0-0.3,0H1.2v-6h13.3v6h-1.6c-0.1,0-0.2,0-0.3,0l0.1,0.2l0.2,0.4C12.9,9,13,9.2,13,9.3v0.1h2.3l0.2-0.1c0.1,0,0.1-0.1,0.2-0.2l0.1-0.1V1.4L15.7,1.3z"/><path d="M10.5,7.5C9.9,7.1,9.3,6.8,8.6,6.7c-0.2,0-0.5-0.1-0.7,0c-0.2,0-0.5,0-0.7,0C6.6,6.7,6.1,6.9,5.6,7.3C5.2,7.6,4.7,8,4.4,8.4C4.3,8.6,4.2,8.8,4.2,8.9C4.1,9.1,4,9.3,3.9,9.4C3.9,9.6,3.8,9.7,3.8,9.9c0,0.2-0.1,0.3-0.1,0.5v-0.1c-0.1,0.8,0.1,1.6,0.5,2.4c0.4,0.7,1,1.3,1.7,1.7c0.3,0.2,0.6,0.3,0.9,0.3c0.3,0.1,0.7,0.1,1,0.1c0.3,0,0.7,0,1-0.1c0.3-0.1,0.6-0.2,0.9-0.3c0.5-0.3,0.9-0.6,1.3-1c0.3-0.4,0.6-0.8,0.8-1.3c0.1-0.4,0.2-0.9,0.2-1.4c0-0.5-0.1-1-0.3-1.4C11.5,8.6,11.1,8,10.5,7.5z M10.1,11.3H8.5v1.6H8H7.9H7.3v0v-0.1v-1.4H5.7v-0.4v-0.2v-0.6h0h1.5V8.5h1.2v1.6h1.6V11.3z"/></g></svg>',insert_row_above:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.8 15.8"><g><path d="M0.1,14.5c0.1,0.1,0.1,0.2,0.2,0.2l0.1,0.1h14.9l0.1-0.1c0,0,0.1-0.1,0.1-0.1c0,0,0.1-0.1,0.1-0.1l0.1-0.1V6.7l-0.1-0.1c0-0.1-0.1-0.1-0.2-0.2l-0.1-0.1h-2.3v0.1l-0.1,0.5l-0.2,0.4l-0.1,0.2c0.1,0,0.2,0,0.3,0h1.6v6H1.3v-6h1.6c0.1,0,0.2,0,0.3,0L3.1,7.3L2.9,6.9C2.8,6.8,2.8,6.6,2.7,6.5V6.3H0.4L0.3,6.4c-0.1,0-0.1,0.1-0.2,0.2L0,6.7v7.7L0.1,14.5z"/><path d="M5.3,8.3c0.6,0.5,1.2,0.8,1.9,0.9c0.2,0,0.5,0.1,0.7,0c0.2,0,0.5,0,0.7,0c0.6-0.1,1.1-0.3,1.6-0.6c0.5-0.3,0.9-0.7,1.2-1.2c0.1-0.2,0.2-0.3,0.3-0.5c0.1-0.2,0.2-0.4,0.2-0.5c0.1-0.1,0.1-0.3,0.1-0.4C12,5.8,12,5.6,12,5.4v0.1c0.1-0.8-0.1-1.6-0.5-2.4c-0.4-0.7-1-1.3-1.7-1.7C9.5,1.3,9.2,1.2,8.9,1.1C8.5,1,8.2,1,7.9,1c-0.3,0-0.7,0-1,0.1C6.6,1.2,6.3,1.3,6,1.4C5.5,1.7,5.1,2,4.7,2.4C4.4,2.8,4.1,3.3,3.9,3.8C3.8,4.2,3.7,4.7,3.7,5.2c0,0.5,0.1,1,0.3,1.4C4.3,7.2,4.7,7.8,5.3,8.3z M5.7,4.5h1.6V2.9h0.5h0.1h0.6v0v0.1v1.4H10v0.4v0.2v0.6h0H8.5v1.6H7.3V5.7H5.7V4.5z"/></g></svg>',insert_column_left:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.8 15.8"><g><path d="M14.5,15.7c0.1-0.1,0.2-0.1,0.2-0.2l0.1-0.1V0.4l-0.1-0.1c0,0-0.1-0.1-0.1-0.1c0,0-0.1-0.1-0.1-0.1L14.4,0H6.7L6.6,0.1c-0.1,0-0.1,0.1-0.2,0.2L6.3,0.4v2.3h0.1l0.5,0.1L7.3,3l0.2,0.1c0-0.1,0-0.2,0-0.3V1.2h6v13.3h-6v-1.6c0-0.1,0-0.2,0-0.3l-0.2,0.1l-0.4,0.2C6.7,12.9,6.6,13,6.4,13H6.3v2.3l0.1,0.2c0,0.1,0.1,0.1,0.2,0.2l0.1,0.1h7.7L14.5,15.7z"/><path d="M8.3,10.5C8.7,10,9,9.3,9.1,8.6c0-0.2,0.1-0.5,0-0.7c0-0.2,0-0.5,0-0.7C9,6.7,8.8,6.1,8.5,5.7C8.2,5.2,7.8,4.8,7.3,4.5C7.2,4.4,7,4.3,6.9,4.2C6.7,4.1,6.5,4,6.4,4C6.2,3.9,6.1,3.9,5.9,3.8c-0.2,0-0.3-0.1-0.5-0.1h0.1C4.7,3.7,3.8,3.9,3.1,4.3C2.4,4.7,1.8,5.3,1.4,6C1.3,6.3,1.2,6.6,1.1,6.9C1,7.2,1,7.6,1,7.9c0,0.3,0,0.7,0.1,1c0.1,0.3,0.2,0.6,0.3,0.9c0.3,0.5,0.6,0.9,1,1.3c0.4,0.3,0.8,0.6,1.3,0.8C4.2,12,4.7,12.1,5.1,12c0.5,0,1-0.1,1.4-0.3C7.2,11.5,7.8,11.1,8.3,10.5zM4.5,10.1V8.5H2.9V8V7.9V7.3h0H3h1.4V5.7h0.4h0.2h0.6v0v1.5h1.6v1.2H5.7v1.6H4.5z"/></g></svg>',insert_column_right:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.8 15.8"><g><path d="M1.3,0.1C1.2,0.2,1.1,0.2,1.1,0.3L1,0.4v14.9l0.1,0.1c0,0,0.1,0.1,0.1,0.1c0,0,0.1,0.1,0.1,0.1l0.1,0.1h7.7l0.1-0.1c0.1,0,0.1-0.1,0.2-0.2l0.1-0.1v-2.3H9.3l-0.5-0.1l-0.4-0.2l-0.2-0.1c0,0.1,0,0.2,0,0.3v1.6h-6V1.3h6v1.6c0,0.1,0,0.2,0,0.3l0.2-0.1l0.4-0.2C9,2.9,9.2,2.8,9.3,2.8h0.1V0.5L9.4,0.3c0-0.1-0.1-0.1-0.2-0.2L9.1,0H1.4L1.3,0.1z"/><path d="M7.5,5.3C7,5.8,6.7,6.5,6.6,7.2c0,0.2-0.1,0.5,0,0.7c0,0.2,0,0.5,0,0.7c0.1,0.6,0.3,1.1,0.6,1.6c0.3,0.5,0.7,0.9,1.2,1.2c0.2,0.1,0.3,0.2,0.5,0.3c0.2,0.1,0.4,0.2,0.5,0.2c0.1,0.1,0.3,0.1,0.4,0.1c0.2,0,0.3,0.1,0.5,0.1h-0.1c0.8,0.1,1.6-0.1,2.4-0.5c0.7-0.4,1.3-1,1.7-1.7c0.2-0.3,0.3-0.6,0.3-0.9c0.1-0.3,0.1-0.7,0.1-1c0-0.3,0-0.7-0.1-1c-0.1-0.3-0.2-0.6-0.3-0.9c-0.3-0.5-0.6-0.9-1-1.3C13,4.4,12.5,4.2,12,4c-0.4-0.1-0.9-0.2-1.4-0.2c-0.5,0-1,0.1-1.4,0.2C8.5,4.3,7.9,4.7,7.5,5.3z M11.3,5.7v1.6h1.6v0.5v0.1v0.6h0h-0.1h-1.4v1.6h-0.4h-0.2h-0.6v0V8.5H8.5V7.3h1.6V5.7H11.3z"/></g></svg>',delete_row:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 13.83"><g><path d="M4.7,18.46l.12.08H19.73l.12-.08a.58.58,0,0,0,.22-.22l.08-.12,0-7.69-.08-.11a.77.77,0,0,0-.18-.18l-.11-.08-2.31,0-.08.28-.1.29a1.58,1.58,0,0,1-.12.29l-.14.34s0,0,.18,0H18.9v6H5.64v-6H7.35c.14,0,.2,0,.18,0l-.14-.34a2.85,2.85,0,0,1-.12-.29l-.1-.29-.07-.27-2.31,0-.11.08a.77.77,0,0,0-.18.18l-.08.11,0,7.69.08.12a.47.47,0,0,0,.09.12l.13.09ZM12.11,13a4,4,0,0,0,1.46-.21,4.51,4.51,0,0,0,1.31-.71A4,4,0,0,0,16.26,10a4.32,4.32,0,0,0-.08-2.54,4.34,4.34,0,0,0-1-1.52,4.15,4.15,0,0,0-1.54-1,4.34,4.34,0,0,0-1.35-.22A4.07,4.07,0,0,0,11,4.93,3.94,3.94,0,0,0,9.24,6.07,3.92,3.92,0,0,0,8.15,8.88a3.91,3.91,0,0,0,.12.95A4.16,4.16,0,0,0,12.11,13Zm2.35-4.14v.58H10.09V8.27h4.37v.58Z" transform="translate(-4.4 -4.71)"/></g></svg>',delete_column:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13.81 15.74"><g><path d="M5.66,19.42l.12.08,7.69,0,.11-.08a.77.77,0,0,0,.18-.18l.08-.11,0-2.32-.15,0-.45-.15-.42-.18-.17-.07a1,1,0,0,0,0,.27v1.63h-6V5h6V6.62a.9.9,0,0,0,0,.26l.17-.07.42-.17a3.91,3.91,0,0,1,.45-.15l.15,0,0-2.32L13.75,4a.77.77,0,0,0-.18-.18l-.11-.08H5.79l-.13.07a.63.63,0,0,0-.21.22l-.08.12V19.08l.08.12a.47.47,0,0,0,.09.12.35.35,0,0,0,.12.1Zm9-3.67a4.16,4.16,0,0,0,2.36-.51,4.08,4.08,0,0,0,1.67-1.72,4,4,0,0,0,.35-.91,3.79,3.79,0,0,0,.1-1,4.71,4.71,0,0,0-.11-1,5,5,0,0,0-.3-.87,4.25,4.25,0,0,0-1-1.25,4.49,4.49,0,0,0-1.34-.81A4.26,4.26,0,0,0,15,7.48a3.88,3.88,0,0,0-1.41.25A4.32,4.32,0,0,0,11.86,9,4,4,0,0,0,11,10.94a4.4,4.4,0,0,0-.05.68,4.5,4.5,0,0,0,.05.68,3.93,3.93,0,0,0,.61,1.57,4.22,4.22,0,0,0,1.18,1.2,4.59,4.59,0,0,0,.48.27c.2.1.37.17.5.22a2.44,2.44,0,0,0,.45.12,4.61,4.61,0,0,0,.5.07Zm2.54-4.12v.58H12.87V11h4.37v.59Z" transform="translate(-5.37 -3.76)"/></g></svg>',fixed_column_width:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M6,5H18A1,1 0 0,1 19,6A1,1 0 0,1 18,7H6A1,1 0 0,1 5,6A1,1 0 0,1 6,5M21,2V4H3V2H21M15,8H17V22H15V8M7,8H9V22H7V8M11,8H13V22H11V8Z" /></svg>',rotate_left:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.8 15.8"><g><path d="M0.5,10.2c0,0.1,0,0.2,0,0.3v0.2l0,0c0.1,0.3,0.3,0.6,0.4,0.9l0,0C1,11.8,1.3,12,1.5,11.9h0.1h0.2h0.1c0.1-0.1,0.3-0.3,0.4-0.5v-0.2c0-0.1,0-0.2-0.1-0.3l0,0c-0.2-0.2-0.3-0.4-0.3-0.7l0,0C1.8,10,1.7,9.9,1.5,9.8c-0.1,0-0.2,0-0.3,0H0.9C0.7,9.9,0.6,10,0.5,10.2L0.5,10.2z"/><path d="M2.2,11.5L2.2,11.5L2.2,11.5z"/><path d="M5.9,3.6L5.9,3.6L5.9,3.6z"/><path d="M0.1,7.9c0,0.3,0,0.6,0,0.9l0,0l0,0l0,0l0,0c0,0.2,0.1,0.3,0.2,0.4l0,0c0.2,0.1,0.3,0.2,0.5,0.2l0,0l0,0c0.2,0,0.4-0.1,0.5-0.3l0,0c0-0.1,0.1-0.3,0.1-0.4V8.6l0,0c0-0.2,0-0.5,0-0.7l0,0c0-0.2-0.1-0.4-0.2-0.5C1.1,7.3,0.9,7.2,0.7,7.2S0.3,7.3,0.2,7.4C0.1,7.5,0,7.7,0.1,7.9z"/><path d="M1.9,12.7L1.9,12.7c0,0.2,0,0.4,0.2,0.5l0,0l0.2,0.3l0,0c0.2,0.1,0.3,0.2,0.5,0.4l0,0l0,0l0,0l0,0C2.9,14,3,14.1,3.2,14.1s0.4-0.1,0.5-0.2c0.1-0.2,0.2-0.3,0.2-0.5v-0.1c0-0.2-0.1-0.4-0.2-0.5l0,0l-0.4-0.4l-0.2-0.2l0,0C3,12.1,2.8,12,2.6,12l0,0c-0.2,0-0.4,0.1-0.5,0.2l0,0C2,12.3,1.9,12.5,1.9,12.7z"/><path d="M6.6,15c0,0.2,0.1,0.4,0.2,0.5c0.1,0.1,0.2,0.2,0.4,0.3l0,0c0.3,0,0.5,0,0.7,0h0.3l0,0c0.2,0,0.4-0.1,0.5-0.2c0.1-0.2,0.2-0.3,0.2-0.5l0,0l0,0c0-0.2-0.1-0.4-0.2-0.5l0,0c-0.1-0.1-0.3-0.2-0.5-0.2l0,0H7.9c-0.1,0-0.3,0-0.5,0l0,0H7.3c-0.2-0.1-0.3,0-0.5,0.1l0,0C6.7,14.6,6.6,14.8,6.6,15L6.6,15L6.6,15L6.6,15z"/><path d="M4.2,7.4C4,7.5,4,7.7,4,7.9c0,0.2,0,0.4,0.2,0.5l0,0l3.2,3.2l0,0c0.1,0.1,0.3,0.2,0.5,0.2s0.3-0.1,0.5-0.2l0,0l3.2-3.2l0,0c0.1-0.1,0.2-0.3,0.2-0.5c0-0.2-0.1-0.4-0.2-0.5l0,0C11.5,7.3,11,6.7,10,5.8l0,0L8.4,4.2l0,0C8.3,4.1,8.1,4,7.9,4S7.5,4.1,7.4,4.2L4.2,7.4L4.2,7.4z M6.8,9L5.7,7.9l2.2-2.2l2.3,2.2l-2.3,2.2C7.7,9.9,7.3,9.5,6.8,9L6.8,9z"/><path d="M4.1,14.1C4,14.2,4,14.3,4,14.4v0.2l0,0c0.1,0.1,0.2,0.3,0.4,0.4l0,0c0.3,0.1,0.6,0.2,0.9,0.4h0.1h0.1l0,0c0.2,0,0.3-0.1,0.5-0.1l0,0c0.2-0.1,0.3-0.3,0.3-0.4l0,0l0,0l0,0l0,0v-0.2c0-0.1-0.1-0.2-0.1-0.3l0,0C6.1,14.2,6,14.1,5.8,14l0,0c-0.3-0.1-0.5-0.2-0.8-0.2l0,0c-0.1-0.1-0.2-0.1-0.3-0.1H4.5C4.3,13.7,4.2,13.9,4.1,14.1z"/><path d="M9.3,14.4c0,0.1-0.1,0.3,0,0.4V15l0,0c0,0.1,0.1,0.3,0.5,0.4c0.1,0.1,0.3,0.1,0.4,0.1l0,0h0.1l0,0c0.3-0.1,0.6-0.2,0.9-0.3l0,0c0.1-0.1,0.2-0.2,0.3-0.4l0.1-0.3c0-0.1-0.1-0.2-0.1-0.3l0,0c-0.1-0.2-0.2-0.3-0.4-0.4l0,0h-0.3c-0.1,0-0.2,0-0.3,0l0,0c-0.2,0.1-0.5,0.2-0.8,0.3l0,0C9.5,14.1,9.4,14.2,9.3,14.4L9.3,14.4z"/><path d="M11.4,14.7L11.4,14.7L11.4,14.7z"/><path d="M9.5,15.3L9.5,15.3L9.5,15.3z"/><path d="M15.9,7.9c0-1-0.2-2-0.6-3l0,0c-0.4-1-1-1.9-1.7-2.6C12.8,1.6,12,1,11,0.6l0,0C10.1,0.2,9,0,8,0C7.3,0,6.5,0.1,5.8,0.3l0,0C5.2,0.5,4.6,0.8,4,1.1L3.1,0.2l0,0C2.9,0.1,2.8,0,2.6,0H2.4l0,0C2.2,0,2,0.2,1.9,0.4l0,0L0.1,4.9l0,0C0,5,0,5.1,0,5.2c0,0.2,0.1,0.4,0.2,0.5l0,0c0.2,0.1,0.3,0.2,0.5,0.2h0.1H1l0,0l4.7-1.8l0,0C5.9,4,6.1,3.8,6.1,3.6V3.4C6.1,3.2,6,3,5.9,2.9l0,0L5.1,2.1c0.4-0.2,0.8-0.4,1.3-0.5c0.5-0.1,1.1-0.2,1.7-0.2c0.9,0,1.7,0.2,2.5,0.5l0,0c0.8,0.3,1.5,0.8,2.1,1.4c0.6,0.6,1.1,1.3,1.4,2.1l0,0c0.3,0.8,0.5,1.6,0.5,2.5s-0.2,1.7-0.5,2.5l0,0c-0.3,0.8-0.8,1.5-1.4,2.1c-0.2,0.2-0.4,0.3-0.6,0.5l0,0c-0.2,0.1-0.3,0.3-0.3,0.5v0.1c0,0.1,0,0.3,0.1,0.4l0,0c0.1,0.2,0.3,0.3,0.5,0.3l0,0c0.1,0,0.3-0.1,0.4-0.2l0,0l0,0l0,0l0,0c0.2-0.2,0.5-0.4,0.7-0.6l0,0l0,0l0,0l0,0c0.7-0.8,1.3-1.6,1.7-2.6C15.6,10,15.8,9,15.9,7.9z M1.9,4C2,3.8,2.1,3.5,2.3,3.1l0,0L2.7,2l1.2,1.2L1.9,4z"/><path d="M6.8,15.5L6.8,15.5L6.8,15.5z"/></g></svg>',rotate_right:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.8 15.8"><g><path d="M9.9,15.3L9.9,15.3L9.9,15.3z"/><path d="M6.9,15.1L6.9,15.1c0,0.1,0.1,0.3,0.2,0.4l0,0c0.1,0.2,0.3,0.3,0.5,0.3l0,0h0.3c0.2,0,0.4,0,0.7,0l0,0c0.2-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.2-0.2,0.2-0.4V15c0-0.2-0.1-0.4-0.2-0.4c-0.2-0.1-0.3-0.2-0.5-0.2H8.4l0,0c-0.1,0-0.3,0-0.5,0H7.6l0,0c-0.2,0-0.4,0.1-0.5,0.2C7,14.7,6.9,14.9,6.9,15.1z"/><path d="M6.5,14.4L6.5,14.4L6.5,14.4z"/><path d="M5.8,5.8L5.8,5.8c-1,0.9-1.5,1.5-1.7,1.6l0,0C4,7.5,4,7.7,4,7.9c0,0.2,0,0.4,0.2,0.5l0,0l3.2,3.2l0,0c0.2,0.1,0.3,0.2,0.5,0.2s0.4-0.1,0.5-0.2l0,0l3.2-3.2l0,0c0.1-0.1,0.2-0.3,0.2-0.5c0-0.2-0.1-0.4-0.2-0.5l0,0L8.4,4.2C8.3,4.1,8.1,4,7.9,4C7.7,4,7.5,4.1,7.4,4.2l0,0L5.8,5.8z M5.6,7.9l2.3-2.2l2.2,2.2L9,9l0,0l0,0l0,0l0,0c-0.5,0.6-0.9,0.9-1.1,1.1L5.6,7.9z"/><path d="M9,15.5L9,15.5L9,15.5z"/><path d="M9.6,14.7v0.2l0,0l0,0l0,0l0,0c0.1,0.2,0.1,0.3,0.3,0.3c0.1,0.1,0.3,0.1,0.4,0.1l0,0h0.1h0.1c0.3-0.1,0.6-0.3,0.9-0.4l0,0c0.1-0.1,0.2-0.2,0.3-0.4l0,0v-0.2c0-0.1,0-0.2-0.1-0.3c-0.1-0.2-0.2-0.3-0.4-0.4H11c-0.1,0-0.2,0.1-0.3,0.1l0,0c-0.2,0.1-0.4,0.2-0.7,0.3l0,0l0,0c-0.1,0.1-0.3,0.2-0.4,0.4C9.6,14.5,9.6,14.6,9.6,14.7z"/><path d="M9,14.5L9,14.5L9,14.5z"/><path d="M9.6,14.4L9.6,14.4L9.6,14.4z"/><path d="M11.7,14L11.7,14L11.7,14z"/><path d="M15.6,7.4L15.6,7.4L15.6,7.4z"/><path d="M15,9.4c0.2,0,0.4,0,0.6-0.2l0,0c0.1-0.1,0.2-0.2,0.2-0.4l0,0l0,0l0,0l0,0c0-0.3,0-0.6,0-0.9c0-0.2-0.1-0.4-0.2-0.5c-0.1-0.1-0.3-0.2-0.5-0.2s-0.4,0.1-0.5,0.2c-0.1,0.1-0.2,0.3-0.2,0.5l0,0c0,0.2,0,0.4,0,0.7l0,0v0.1c0,0.1,0,0.3,0.1,0.4l0,0C14.6,9.3,14.8,9.4,15,9.4L15,9.4L15,9.4z"/><path d="M14,12h0.1h0.2h0.1c0.2,0,0.5-0.2,0.6-0.4l0,0c0.2-0.3,0.3-0.6,0.4-0.9l0,0v-0.2c0-0.1-0.1-0.2-0.1-0.3c-0.1-0.2-0.2-0.3-0.4-0.4h-0.3c-0.1,0-0.2,0-0.3,0C14.2,9.9,14,10,14,10.3l0,0c-0.1,0.2-0.2,0.5-0.3,0.7l0,0c-0.1,0.1-0.1,0.2-0.1,0.3v0.2l0,0l0,0C13.6,11.6,13.8,11.8,14,12z"/><path d="M14.6,7.4L14.6,7.4L14.6,7.4z"/><path d="M4.4,14.2c-0.1,0.1-0.1,0.2-0.1,0.3l0.1,0.2c0,0.2,0.2,0.3,0.3,0.4l0,0c0.3,0.1,0.6,0.3,1.1,0.4l0,0h0.1l0,0c0.1,0,0.2-0.1,0.4-0.2c0.1,0,0.2-0.2,0.3-0.3l0,0v-0.2c0-0.1-0.1-0.3-0.2-0.4c-0.1-0.1-0.2-0.2-0.4-0.3l0,0c-0.2-0.1-0.5-0.2-0.7-0.3l0,0c-0.1,0-0.2,0-0.3,0H4.7l0,0C4.6,13.9,4.4,14,4.4,14.2L4.4,14.2z"/><path d="M11.9,13.3c0,0.2,0.1,0.4,0.2,0.6c0.1,0.1,0.3,0.2,0.5,0.2s0.4-0.1,0.5-0.2l0,0l0,0l0,0l0,0c0.1-0.1,0.3-0.3,0.4-0.4l0,0l0.2-0.3l0,0c0.1-0.2,0.2-0.3,0.2-0.5l0,0c0-0.2-0.1-0.4-0.2-0.5l0,0c-0.1-0.1-0.3-0.2-0.5-0.2l0,0c-0.2,0-0.4,0.1-0.5,0.2l0,0l-0.2,0.2l-0.4,0.4l0,0C12,13,11.9,13.1,11.9,13.3L11.9,13.3z"/><path d="M12.1,13.8L12.1,13.8L12.1,13.8z"/><path d="M11.9,13.3L11.9,13.3L11.9,13.3z"/><path d="M15.9,5.2c0-0.1-0.1-0.2-0.1-0.3l0,0L14,0.4l0,0C13.9,0.2,13.7,0,13.5,0l0,0l0,0h-0.2c-0.2,0-0.4,0.1-0.5,0.2l0,0l-0.9,0.9c-0.5-0.3-1.1-0.6-1.8-0.8l0,0C9.4,0.1,8.7,0,7.9,0c-1,0-2,0.2-3,0.6S3,1.6,2.3,2.3C1.6,3.1,1,3.9,0.6,4.9l0,0C0.2,5.8,0,6.8,0,7.9c0,1,0.2,2,0.6,3s0.9,1.8,1.7,2.6l0,0l0,0l0,0l0,0c0.2,0.2,0.5,0.4,0.7,0.6l0,0l0,0l0,0l0,0c0.2,0.1,0.3,0.2,0.5,0.2l0,0c0.2,0,0.4-0.1,0.6-0.3l0,0c0.1-0.1,0.1-0.3,0.1-0.4v-0.1l0,0C4.1,13.3,4,13.1,3.9,13l0,0c-0.2-0.1-0.4-0.3-0.6-0.5c-0.6-0.6-1.1-1.3-1.4-2.1l0,0C1.6,9.6,1.4,8.8,1.4,7.9s0.2-1.7,0.5-2.5l0,0c0.3-0.8,0.8-1.5,1.4-2.1c0.6-0.6,1.3-1.1,2.1-1.4l0,0C6.2,1.6,7,1.4,7.9,1.4c0.6,0,1.1,0.1,1.7,0.2c0.5,0.1,0.9,0.3,1.3,0.5l-0.8,0.8l0,0C10,3.1,9.9,3.2,9.9,3.4v0.2l0,0l0,0c0,0.2,0.2,0.4,0.4,0.5l0,0l4.5,1.8l0,0H15h0.1c0.2,0,0.4-0.1,0.5-0.2l0,0C15.7,5.6,15.8,5.4,15.9,5.2z M11.8,3.2L13,2l0.4,1.1l0,0c0.2,0.4,0.3,0.7,0.4,0.9L11.8,3.2z"/></g></svg>',mirror_horizontal:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14.75 15.74"><g><path d="M13.75,3.76l5.9,15.74h-5.9V3.76ZM4.9,19.5,10.8,3.76V19.5H4.9Z" transform="translate(-4.9 -3.76)"/></g></svg>',mirror_vertical:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 14.75"><g><path d="M20.15,13.1,4.41,19V13.1H20.15ZM4.41,4.25l15.74,5.9H4.41V4.25Z" transform="translate(-4.41 -4.25)"/></g></svg>',checked:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 12.1"><g><path d="M4.59,12.23l.12.18L9.43,17.5a.58.58,0,0,0,.84,0L20,7.45h0a.58.58,0,0,0,0-.84l-.85-.85a.58.58,0,0,0-.84,0H18.2l-8.12,8.41a.29.29,0,0,1-.42,0l-3.4-3.63a.58.58,0,0,0-.84,0l-.85.85a.6.6,0,0,0-.14.21.51.51,0,0,0,0,.44c.05.06.1.13.16.19Z" transform="translate(-4.38 -5.58)"/></g></svg>',line_break:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19,6a1,1,0,0,0-1,1v4a1,1,0,0,1-1,1H7.41l1.3-1.29A1,1,0,0,0,7.29,9.29l-3,3a1,1,0,0,0-.21.33,1,1,0,0,0,0,.76,1,1,0,0,0,.21.33l3,3a1,1,0,0,0,1.42,0,1,1,0,0,0,0-1.42L7.41,14H17a3,3,0,0,0,3-3V7A1,1,0,0,0,19,6Z"/></svg>',audio:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.84 14,18.7V20.77C18,19.86 21,16.28 21,12C21,7.72 18,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12M3,9V15H7L12,20V4L7,9H3Z" /></svg>',image_gallery:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="30 30 150 150"><g><path d="M152.775,120.548V51.651c0-12.271-9.984-22.254-22.254-22.254H43.727c-12.271,0-22.254,9.983-22.254,22.254v68.896c0,12.27,9.983,22.254,22.254,22.254h86.795C142.791,142.802,152.775,132.817,152.775,120.548z M36.394,51.651c0-4.042,3.291-7.333,7.333-7.333h86.795c4.042,0,7.332,3.291,7.332,7.333v23.917l-14.938-17.767c-1.41-1.678-3.487-2.649-5.68-2.658h-0.029c-2.184,0-4.255,0.954-5.674,2.613L76.709,98.519l-9.096-9.398c-1.427-1.474-3.392-2.291-5.448-2.273c-2.052,0.025-4.004,0.893-5.396,2.4L36.394,111.32V51.651z M41.684,127.585l20.697-22.416l9.312,9.622c1.461,1.511,3.489,2.334,5.592,2.27c2.101-0.066,4.075-1.013,5.44-2.612l34.436-40.308l20.693,24.613v21.794c0,4.042-3.29,7.332-7.332,7.332H43.727C43.018,127.88,42.334,127.775,41.684,127.585z M182.616,152.5V75.657c0-4.12-3.34-7.46-7.461-7.46c-4.119,0-7.46,3.34-7.46,7.46V152.5c0,4.112-3.347,7.46-7.461,7.46h-94c-4.119,0-7.46,3.339-7.46,7.459c0,4.123,3.341,7.462,7.46,7.462h94C172.576,174.881,182.616,164.841,182.616,152.5z"/></g></svg>',bookmark:'<svg viewBox="0 0 24 24"><path d="M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3Z" /></svg>',download:'<svg viewBox="0 0 24 24"><path d="M2 12H4V17H20V12H22V17C22 18.11 21.11 19 20 19H4C2.9 19 2 18.11 2 17V12M12 15L17.55 9.54L16.13 8.13L13 11.25V2H11V11.25L7.88 8.13L6.46 9.55L12 15Z" /></svg>',dir_ltr:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M9 4v4c-1.1 0-2-.9-2-2s.9-2 2-2m8-2H9C6.79 2 5 3.79 5 6s1.79 4 4 4v5h2V4h2v11h2V4h2V2zm0 12v3H5v2h12v3l4-4-4-4z"/></svg>',dir_rtl:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M10 4v4c-1.1 0-2-.9-2-2s.9-2 2-2m8-2h-8C7.79 2 6 3.79 6 6s1.79 4 4 4v5h2V4h2v11h2V4h2V2zM8 14l-4 4 4 4v-3h12v-2H8v-3z"/></svg>',alert_outline:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M11,15H13V17H11V15M11,7H13V13H11V7M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20Z" /></svg>',more_text:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="10 10 180 180"><g><path d="M49.711,142.188h49.027c2.328,0.002,4.394,1.492,5.129,3.699l9.742,29.252c0.363,1.092,1.385,1.828,2.537,1.83l15.883,0.01c0.859,0,1.667-0.412,2.17-1.109s0.641-1.594,0.37-2.41l-16.625-50.045L86.503,28.953c-0.36-1.097-1.383-1.839-2.537-1.842H64.532c-1.153-0.001-2.178,0.736-2.542,1.831L13.847,173.457c-0.271,0.816-0.135,1.713,0.369,2.412c0.503,0.697,1.311,1.109,2.171,1.109h15.872c1.151,0,2.173-0.736,2.537-1.828l9.793-29.287C45.325,143.66,47.39,142.18,49.711,142.188L49.711,142.188z M53.493,119.098l15.607-46.9c0.744-2.196,2.806-3.674,5.125-3.674s4.381,1.478,5.125,3.674l15.607,46.904c0.537,1.621,0.263,3.402-0.736,4.789c-1.018,1.408-2.649,2.24-4.386,2.24H58.615c-1.736,0-3.368-0.832-4.386-2.24C53.23,122.504,52.956,120.721,53.493,119.098L53.493,119.098z M190.465,63.32c0-2.919-1.015-5.396-3.059-7.428c-2.029-2.031-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.016-7.388,3.047c-2.029,2.032-3.056,4.498-3.056,7.386c0,2.889,1.026,5.354,3.056,7.385c2.032,2.032,4.499,3.059,7.388,3.059c2.887,0,5.354-1.026,7.383-3.059C189.45,68.633,190.465,66.178,190.465,63.32L190.465,63.32z M190.465,101.994c0-2.858-1.015-5.313-3.059-7.333c-2.029-2.042-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.005-7.388,3.047c-2.029,2.021-3.056,4.486-3.056,7.376c0,2.887,1.026,5.352,3.056,7.395c2.032,2.021,4.499,3.047,7.388,3.047c2.887,0,5.354-1.025,7.383-3.047C189.45,107.389,190.465,104.914,190.465,101.994L190.465,101.994z M190.465,140.76c0-2.918-1.015-5.395-3.059-7.438c-2.029-2.041-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.006-7.388,3.047c-2.029,2.043-3.056,4.52-3.056,7.438c0,2.922,1.026,5.398,3.056,7.439c2.032,2.021,4.499,3.047,7.388,3.047c2.887,0,5.354-1.025,7.383-3.047C189.45,146.158,190.465,143.682,190.465,140.76L190.465,140.76z"/></g></svg>',more_paragraph:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="10 10 180 180"><g><path d="M128.39,28.499H63.493c-25.558,0-46.354,20.796-46.354,46.354c0,25.559,20.796,46.353,46.354,46.353h9.271v55.625h18.542V47.04h9.271V176.83h18.543V47.04h9.271V28.499z M72.764,102.664h-9.271c-15.337,0-27.813-12.475-27.813-27.812c0-15.336,12.476-27.813,27.813-27.813h9.271V102.664z M190.465,63.32c0-2.919-1.015-5.396-3.059-7.428c-2.029-2.031-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.016-7.388,3.047c-2.029,2.032-3.056,4.498-3.056,7.386c0,2.889,1.026,5.354,3.056,7.385c2.032,2.032,4.499,3.059,7.388,3.059c2.887,0,5.354-1.026,7.383-3.059C189.45,68.633,190.465,66.178,190.465,63.32L190.465,63.32z M190.465,101.994c0-2.858-1.015-5.313-3.059-7.333c-2.029-2.042-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.005-7.388,3.047c-2.029,2.021-3.056,4.486-3.056,7.376c0,2.887,1.026,5.352,3.056,7.395c2.032,2.021,4.499,3.047,7.388,3.047c2.887,0,5.354-1.025,7.383-3.047C189.45,107.389,190.465,104.914,190.465,101.994L190.465,101.994z M190.465,140.76c0-2.918-1.015-5.395-3.059-7.438c-2.029-2.041-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.006-7.388,3.047c-2.029,2.043-3.056,4.52-3.056,7.438c0,2.922,1.026,5.398,3.056,7.439c2.032,2.021,4.499,3.047,7.388,3.047c2.887,0,5.354-1.025,7.383-3.047C189.45,146.158,190.465,143.682,190.465,140.76L190.465,140.76z"/></g></svg>',more_plus:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="35 30 140 140"><g><path d="M137.215,102.045c0,3.498-2.835,6.332-6.333,6.332H24.549c-3.498,0-6.334-2.834-6.334-6.332l0,0c0-3.498,2.836-6.333,6.334-6.333h106.333C134.38,95.711,137.215,98.547,137.215,102.045L137.215,102.045z M77.715,161.545c-3.498,0-6.333-2.836-6.333-6.334V48.878c0-3.498,2.836-6.333,6.333-6.333l0,0c3.498,0,6.334,2.835,6.334,6.333v106.333C84.049,158.709,81.213,161.545,77.715,161.545L77.715,161.545z M190.465,63.32c0-2.919-1.015-5.396-3.059-7.428c-2.029-2.031-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.016-7.388,3.047c-2.029,2.032-3.056,4.498-3.056,7.386c0,2.889,1.026,5.354,3.056,7.385c2.032,2.032,4.499,3.059,7.388,3.059c2.887,0,5.354-1.026,7.383-3.059C189.45,68.632,190.465,66.177,190.465,63.32L190.465,63.32z M190.465,101.993c0-2.858-1.015-5.313-3.059-7.333c-2.029-2.042-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.005-7.388,3.047c-2.029,2.021-3.056,4.486-3.056,7.376c0,2.888,1.026,5.353,3.056,7.396c2.032,2.021,4.499,3.047,7.388,3.047c2.887,0,5.354-1.025,7.383-3.047C189.45,107.389,190.465,104.914,190.465,101.993L190.465,101.993z M190.465,140.76c0-2.918-1.015-5.395-3.059-7.438c-2.029-2.041-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.006-7.388,3.047c-2.029,2.043-3.056,4.52-3.056,7.438c0,2.922,1.026,5.398,3.056,7.439c2.032,2.021,4.499,3.047,7.388,3.047c2.887,0,5.354-1.025,7.383-3.047C189.45,146.158,190.465,143.682,190.465,140.76L190.465,140.76z"/></g></svg>',more_horizontal:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.76 3.58"><g><path d="M4.64,10.73a1.84,1.84,0,0,1,.65-.65,1.76,1.76,0,0,1,1.79,0A1.79,1.79,0,0,1,8,11.63a1.84,1.84,0,0,1-.25.9,1.69,1.69,0,0,1-.65.65,1.8,1.8,0,0,1-2.69-1.55A2.08,2.08,0,0,1,4.64,10.73Zm6.09,0a1.84,1.84,0,0,1,.65-.65,1.78,1.78,0,0,1,2.67,1.55,1.73,1.73,0,0,1-.24.9,1.84,1.84,0,0,1-.65.65,1.76,1.76,0,0,1-1.79,0,1.79,1.79,0,0,1-.64-2.44Zm6.08,0a1.69,1.69,0,0,1,.65-.65,1.76,1.76,0,0,1,1.79,0,1.79,1.79,0,0,1,.9,1.54,1.73,1.73,0,0,1-.24.9,1.84,1.84,0,0,1-.65.65,1.8,1.8,0,0,1-2.69-1.55A2,2,0,0,1,16.81,10.73Z" transform="translate(-4.39 -9.84)"/></g></svg>',more_vertical:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 3.94 15.75"><g><path d="M12.28,7.69a1.92,1.92,0,0,1-1.39-.58,2,2,0,0,1-.58-1.39,1.92,1.92,0,0,1,.58-1.39,2,2,0,0,1,1.39-.58,1.92,1.92,0,0,1,1.39.58,2,2,0,0,1,.58,1.39,1.92,1.92,0,0,1-.58,1.39,2,2,0,0,1-1.39.58Zm0,2a1.92,1.92,0,0,1,1.39.58,2,2,0,0,1,.58,1.39A1.92,1.92,0,0,1,13.67,13a2,2,0,0,1-1.39.58A1.92,1.92,0,0,1,10.89,13a2,2,0,0,1-.58-1.39,2,2,0,0,1,2-2Zm0,5.9a1.92,1.92,0,0,1,1.39.58,2,2,0,0,1,.58,1.39,1.92,1.92,0,0,1-.58,1.39,2,2,0,0,1-1.39.58,1.92,1.92,0,0,1-1.39-.58,2,2,0,0,1-.58-1.39,1.92,1.92,0,0,1,.58-1.39,1.94,1.94,0,0,1,1.39-.58Z" transform="translate(-10.31 -3.75)"/></g></svg>',attachment:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 8.38 15.68"><g><path d="M15.23,6h1v9.78a3.88,3.88,0,0,1-1.31,2.45,4,4,0,0,1-6.57-2.45V7A3,3,0,0,1,9.2,4.89a3,3,0,0,1,5,2.09v8.31a1.92,1.92,0,0,1-.58,1.39,2,2,0,0,1-1.39.58,1.92,1.92,0,0,1-1.39-.58,2,2,0,0,1-.58-1.39V8h1v7.32a1,1,0,0,0,.29.69,1,1,0,0,0,.69.28A.9.9,0,0,0,13,16a1,1,0,0,0,.29-.69V7a1.92,1.92,0,0,0-.58-1.39A2,2,0,0,0,11.27,5a1.92,1.92,0,0,0-1.39.58A2,2,0,0,0,9.33,7v8.31a3,3,0,1,0,5.9,0V6Z" transform="translate(-8.08 -3.78)"/></g></svg>',map:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 11.7 15.62"><g><path d="M12.05,12.42a2.93,2.93,0,1,1,2.07-5A2.88,2.88,0,0,1,15,9.49a3,3,0,0,1-.86,2.07,2.89,2.89,0,0,1-2.07.86Zm0-5.36a2.43,2.43,0,0,0-1.72,4.16,2.48,2.48,0,0,0,1.72.72,2.44,2.44,0,0,0,0-4.88Zm0-3.3A5.84,5.84,0,0,1,17.9,9.62a9.94,9.94,0,0,1-1.73,5A33.59,33.59,0,0,1,12.84,19a1.52,1.52,0,0,1-.23.2,1,1,0,0,1-.55.2h0a1,1,0,0,1-.55-.2,1.52,1.52,0,0,1-.23-.2,33.59,33.59,0,0,1-3.33-4.32,9.93,9.93,0,0,1-1.72-5,5.84,5.84,0,0,1,5.85-5.86ZM12,18.34l.08.05.06-.06a35.58,35.58,0,0,0,3.06-3.93,9.35,9.35,0,0,0,1.74-4.77,4.88,4.88,0,0,0-4.88-4.88A4.79,4.79,0,0,0,8.6,6.17,4.84,4.84,0,0,0,7.17,9.62,9.29,9.29,0,0,0,8.91,14.4,36,36,0,0,0,12,18.34Z" transform="translate(-6.2 -3.76)"/></g></svg>',magic_stick:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.73 15.75"><g><path d="M19.86,19.21a1,1,0,0,0,.28-.68,1,1,0,0,0-.28-.7L13,10.93a1,1,0,0,0-.7-.28,1,1,0,0,0-.68,1.65l6.9,6.9a1,1,0,0,0,.69.29.93.93,0,0,0,.69-.28ZM9.19,8.55a3,3,0,0,0,1.68,0,14.12,14.12,0,0,0,1.41-.32A11.26,11.26,0,0,0,10.8,7.06c-.56-.36-.86-.56-.91-.58S10,5.91,10,5.11s0-1.26-.15-1.37a4.35,4.35,0,0,0-1.19.71c-.53.4-.81.62-.87.68a9,9,0,0,0-2-.6,6.84,6.84,0,0,0-.76-.09s0,.27.08.77a8.6,8.6,0,0,0,.61,2q-.09.09-.69.87a3.59,3.59,0,0,0-.68,1.17c.12.17.57.23,1.36.15S7,9.26,7.15,9.23s.21.36.57.91a10.49,10.49,0,0,0,1.14,1.48c0-.1.14-.57.31-1.4a3,3,0,0,0,0-1.67Z" transform="translate(-4.41 -3.74)"/></g></svg>',empty_file:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12.78 15.75"><g><path d="M14.73,3.76,18.67,7.7v9.84a2,2,0,0,1-2,2H7.84a1.89,1.89,0,0,1-1.38-.58,2,2,0,0,1-.57-1.39V5.73a1.93,1.93,0,0,1,.57-1.38,2,2,0,0,1,1.38-.58h6.62l.26,0v0Zm2.95,4.92h-2a1.93,1.93,0,0,1-1.38-.57,2,2,0,0,1-.58-1.4V6.17c0-.36,0-.84,0-1.43H7.85a1,1,0,0,0-.7.29,1,1,0,0,0-.29.7V17.54a1,1,0,0,0,.29.69,1,1,0,0,0,.69.29h8.85a1,1,0,0,0,.71-.29.92.92,0,0,0,.28-.69Zm0-1L14.73,4.74v2A1,1,0,0,0,15,7.4a1,1,0,0,0,.69.29Z" transform="translate(-5.89 -3.76)"/></g></svg>'},n=i(96775),o=i.n(n),a=i(37689),r={init:function(e,t){"object"!=typeof t&&(t={});let i=document;this._initOptions(e,t);let l=i.createElement("DIV");l.className="sun-editor"+(t.rtl?" se-rtl":""),e.id&&(l.id="suneditor_"+e.id);let n=i.createElement("DIV");n.className="se-container";let o=this._createToolBar(i,t.buttonList,t.plugins,t),a=o.element.cloneNode(!1);a.className+=" se-toolbar-shadow",o.element.style.visibility="hidden",o.pluginCallButtons.math&&this._checkKatexMath(t.katex);let r=i.createElement("DIV");r.className="se-arrow";let s=i.createElement("DIV");s.className="se-toolbar-sticky-dummy";let u=i.createElement("DIV");u.className="se-wrapper";let c=this._initElements(t,l,o.element,r),d=c.bottomBar,h=c.wysiwygFrame,p=c.placeholder,g=c.codeView,m=d.resizingBar,f=d.navigation,b=d.charWrapper,v=d.charCounter,_=i.createElement("DIV");_.className="se-loading-box sun-editor-common",_.innerHTML='<div class="se-loading-effect"></div>';let w=i.createElement("DIV");w.className="se-line-breaker",w.innerHTML='<button class="se-btn">'+t.icons.line_break+"</button>";let y=i.createElement("DIV");y.className+="se-line-breaker-component";let x=y.cloneNode(!0);y.innerHTML=x.innerHTML=t.icons.line_break;let C=i.createElement("DIV");C.className="se-resizing-back";let k=i.createElement("INPUT");k.tabIndex=-1,k.style.cssText="position: fixed !important; top: -10000px !important; display: block !important; width: 0 !important; height: 0 !important; margin: 0 !important; padding: 0 !important;";let S=t.toolbarContainer;S&&(S.appendChild(o.element),S.appendChild(a));let z=t.resizingBarContainer;return m&&z&&z.appendChild(m),u.appendChild(g),p&&u.appendChild(p),S||(n.appendChild(o.element),n.appendChild(a)),n.appendChild(s),n.appendChild(u),n.appendChild(C),n.appendChild(_),n.appendChild(w),n.appendChild(y),n.appendChild(x),n.appendChild(k),m&&!z&&n.appendChild(m),l.appendChild(n),g=this._checkCodeMirror(t,g),{constructed:{_top:l,_relative:n,_toolBar:o.element,_toolbarShadow:a,_menuTray:o._menuTray,_editorArea:u,_wysiwygArea:h,_codeArea:g,_placeholder:p,_resizingBar:m,_navigation:f,_charWrapper:b,_charCounter:v,_loading:_,_lineBreaker:w,_lineBreaker_t:y,_lineBreaker_b:x,_resizeBack:C,_stickyDummy:s,_arrow:r,_focusTemp:k},options:t,plugins:o.plugins,pluginCallButtons:o.pluginCallButtons,_responsiveButtons:o.responsiveButtons}},_checkCodeMirror:function(e,t){if(e.codeMirror){let i=[{mode:"htmlmixed",htmlMode:!0,lineNumbers:!0,lineWrapping:!0},e.codeMirror.options||{}].reduce(function(e,t){for(let i in t)a.Z.hasOwn(t,i)&&(e[i]=t[i]);return e},{});"auto"===e.height&&(i.viewportMargin=1/0,i.height="auto");let l=e.codeMirror.src.fromTextArea(t,i);l.display.wrapper.style.cssText=t.style.cssText,e.codeMirrorEditor=l,t=l.display.wrapper,t.className+=" se-wrapper-code-mirror"}return t},_checkKatexMath:function(e){if(!e)throw Error('[SUNEDITOR.create.fail] To use the math button you need to add a "katex" object to the options.');let t=[{throwOnError:!1},e.options||{}].reduce(function(e,t){for(let i in t)a.Z.hasOwn(t,i)&&(e[i]=t[i]);return e},{});e.options=t},_setOptions:function(e,t,i){this._initOptions(t.element.originElement,e);let l=t.element,n=l.relative,o=l.editorArea,r=e.toolbarContainer&&e.toolbarContainer!==i.toolbarContainer,s=e.lang!==i.lang||e.buttonList!==i.buttonList||e.mode!==i.mode||r,u=this._createToolBar(document,s?e.buttonList:i.buttonList,e.plugins,e);u.pluginCallButtons.math&&this._checkKatexMath(e.katex);let c=document.createElement("DIV");c.className="se-arrow",s&&(u.element.style.visibility="hidden",r?(e.toolbarContainer.appendChild(u.element),l.toolbar.parentElement.removeChild(l.toolbar)):l.toolbar.parentElement.replaceChild(u.element,l.toolbar),l.toolbar=u.element,l._menuTray=u._menuTray,l._arrow=c);let d=this._initElements(e,l.topArea,s?u.element:l.toolbar,c),h=d.bottomBar,p=d.wysiwygFrame,g=d.placeholder,m=d.codeView;return l.resizingBar&&a.Z.removeItem(l.resizingBar),h.resizingBar&&(e.resizingBarContainer&&e.resizingBarContainer!==i.resizingBarContainer?e.resizingBarContainer.appendChild(h.resizingBar):n.appendChild(h.resizingBar)),o.innerHTML="",o.appendChild(m),g&&o.appendChild(g),m=this._checkCodeMirror(e,m),l.resizingBar=h.resizingBar,l.navigation=h.navigation,l.charWrapper=h.charWrapper,l.charCounter=h.charCounter,l.wysiwygFrame=p,l.code=m,l.placeholder=g,e.rtl?a.Z.addClass(l.topArea,"se-rtl"):a.Z.removeClass(l.topArea,"se-rtl"),{callButtons:u.pluginCallButtons,plugins:u.plugins,toolbar:u}},_initElements:function(e,t,i,l){t.style.cssText=e._editorStyles.top,/inline/i.test(e.mode)?(i.className+=" se-toolbar-inline",i.style.width=e.toolbarWidth):/balloon/i.test(e.mode)&&(i.className+=" se-toolbar-balloon",i.style.width=e.toolbarWidth,i.appendChild(l));let n=document.createElement(e.iframe?"IFRAME":"DIV");if(n.className="se-wrapper-inner se-wrapper-wysiwyg",e.iframe)n.allowFullscreen=!0,n.frameBorder=0,n.style.cssText=e._editorStyles.frame,n.className+=e.className;else{for(let t in n.setAttribute("contenteditable",!0),n.setAttribute("autocorrect","off"),n.setAttribute("scrolling","auto"),e.iframeAttributes)n.setAttribute(t,e.iframeAttributes[t]);n.className+=" "+e._editableClass,n.style.cssText=e._editorStyles.frame+e._editorStyles.editor,n.className+=e.className}let o=document.createElement("TEXTAREA");o.className="se-wrapper-inner se-wrapper-code"+e.className,o.style.cssText=e._editorStyles.frame,o.style.display="none","auto"===e.height&&(o.style.overflow="hidden");let a=null,r=null,s=null,u=null;if(e.resizingBar&&((a=document.createElement("DIV")).className="se-resizing-bar sun-editor-common",(r=document.createElement("DIV")).className="se-navigation sun-editor-common",a.appendChild(r),e.charCounter)){if((s=document.createElement("DIV")).className="se-char-counter-wrapper",e.charCounterLabel){let t=document.createElement("SPAN");t.className="se-char-label",t.textContent=e.charCounterLabel,s.appendChild(t)}if((u=document.createElement("SPAN")).className="se-char-counter",u.textContent="0",s.appendChild(u),e.maxCharCount>0){let t=document.createElement("SPAN");t.textContent=" / "+e.maxCharCount,s.appendChild(t)}a.appendChild(s)}let c=null;return e.placeholder&&((c=document.createElement("SPAN")).className="se-placeholder",c.innerText=e.placeholder),{bottomBar:{resizingBar:a,navigation:r,charWrapper:s,charCounter:u},wysiwygFrame:n,codeView:o,placeholder:c}},_initOptions:function(e,t){let i={};if(t.plugins){let e=t.plugins,l=e.length?e:Object.keys(e).map(function(t){return e[t]});for(let e=0,t=l.length,n;e<t;e++)i[(n=l[e].default||l[e]).name]=n}t.plugins=i,t.strictMode=!1!==t.strictMode,t.strictHTMLValidation=!1!==t.strictHTMLValidation,t.lang=t.lang||o(),t.value="string"==typeof t.value?t.value:null,t.allowedClassNames=new a.Z._w.RegExp((t.allowedClassNames&&"string"==typeof t.allowedClassNames?t.allowedClassNames+"|":"")+"^__se__|se-|katex"),t.historyStackDelayTime="number"==typeof t.historyStackDelayTime?t.historyStackDelayTime:400,t.frameAttrbutes=t.frameAttrbutes||{},t.defaultTag="string"==typeof t.defaultTag&&t.defaultTag.length>0?t.defaultTag:"p";let n=t.textTags=[{bold:"STRONG",underline:"U",italic:"EM",strike:"DEL",sub:"SUB",sup:"SUP"},t.textTags||{}].reduce(function(e,t){for(let i in t)e[i]=t[i];return e},{});t._textTagsMap={strong:n.bold.toLowerCase(),b:n.bold.toLowerCase(),u:n.underline.toLowerCase(),ins:n.underline.toLowerCase(),em:n.italic.toLowerCase(),i:n.italic.toLowerCase(),del:n.strike.toLowerCase(),strike:n.strike.toLowerCase(),s:n.strike.toLowerCase(),sub:n.sub.toLowerCase(),sup:n.sup.toLowerCase()},t._defaultCommand={bold:t.textTags.bold,underline:t.textTags.underline,italic:t.textTags.italic,strike:t.textTags.strike,subscript:t.textTags.sub,superscript:t.textTags.sup},t.__allowedScriptTag=!0===t.__allowedScriptTag,t.tagsBlacklist=t.tagsBlacklist||"",t._defaultTagsWhitelist=("string"==typeof t._defaultTagsWhitelist?t._defaultTagsWhitelist:"br|p|div|pre|blockquote|h1|h2|h3|h4|h5|h6|ol|ul|li|hr|figure|figcaption|img|iframe|audio|video|source|table|thead|tbody|tr|th|td|a|b|strong|var|i|em|u|ins|s|span|strike|del|sub|sup|code|svg|path|details|summary")+(t.__allowedScriptTag?"|script":""),t._editorTagsWhitelist="*"===t.addTagsWhitelist?"*":this._setWhitelist(t._defaultTagsWhitelist+("string"==typeof t.addTagsWhitelist&&t.addTagsWhitelist.length>0?"|"+t.addTagsWhitelist:""),t.tagsBlacklist),t.pasteTagsBlacklist=t.tagsBlacklist+(t.tagsBlacklist&&t.pasteTagsBlacklist?"|"+t.pasteTagsBlacklist:t.pasteTagsBlacklist||""),t.pasteTagsWhitelist="*"===t.pasteTagsWhitelist?"*":this._setWhitelist("string"==typeof t.pasteTagsWhitelist?t.pasteTagsWhitelist:t._editorTagsWhitelist,t.pasteTagsBlacklist),t.attributesWhitelist=t.attributesWhitelist&&"object"==typeof t.attributesWhitelist?t.attributesWhitelist:null,t.attributesBlacklist=t.attributesBlacklist&&"object"==typeof t.attributesBlacklist?t.attributesBlacklist:null,t.mode=t.mode||"classic",t.rtl=!!t.rtl,t.lineAttrReset=["id"].concat(t.lineAttrReset&&"string"==typeof t.lineAttrReset?t.lineAttrReset.toLowerCase().split("|"):[]),t._editableClass="sun-editor-editable"+(t.rtl?" se-rtl":""),t._printClass="string"==typeof t._printClass?t._printClass:null,t.toolbarWidth=t.toolbarWidth?a.Z.isNumber(t.toolbarWidth)?t.toolbarWidth+"px":t.toolbarWidth:"auto",t.toolbarContainer="string"==typeof t.toolbarContainer?document.querySelector(t.toolbarContainer):t.toolbarContainer,t.stickyToolbar=/balloon/i.test(t.mode)||t.toolbarContainer?-1:void 0===t.stickyToolbar?0:/^\d+/.test(t.stickyToolbar)?a.Z.getNumber(t.stickyToolbar,0):-1,t.hideToolbar=!!t.hideToolbar,t.fullScreenOffset=void 0===t.fullScreenOffset?0:/^\d+/.test(t.fullScreenOffset)?a.Z.getNumber(t.fullScreenOffset,0):0,t.fullPage=!!t.fullPage,t.iframe=t.fullPage||!!t.iframe,t.iframeAttributes=t.iframeAttributes||{},t.iframeCSSFileName=t.iframe?"string"==typeof t.iframeCSSFileName?[t.iframeCSSFileName]:t.iframeCSSFileName||["suneditor"]:null,t.previewTemplate="string"==typeof t.previewTemplate?t.previewTemplate:null,t.printTemplate="string"==typeof t.printTemplate?t.printTemplate:null,t.codeMirror=t.codeMirror?t.codeMirror.src?t.codeMirror:{src:t.codeMirror}:null,t.katex=t.katex?t.katex.src?t.katex:{src:t.katex}:null,t.mathFontSize=t.mathFontSize?t.mathFontSize:[{text:"1",value:"1em"},{text:"1.5",value:"1.5em"},{text:"2",value:"2em"},{text:"2.5",value:"2.5em"}],t.position="string"==typeof t.position?t.position:null,t.display=t.display||("none"!==e.style.display&&e.style.display?e.style.display:"block"),t.popupDisplay=t.popupDisplay||"full",t.resizingBar=void 0===t.resizingBar?!/inline|balloon/i.test(t.mode):t.resizingBar,t.showPathLabel=!!t.resizingBar&&("boolean"!=typeof t.showPathLabel||t.showPathLabel),t.resizeEnable=void 0===t.resizeEnable||!!t.resizeEnable,t.resizingBarContainer="string"==typeof t.resizingBarContainer?document.querySelector(t.resizingBarContainer):t.resizingBarContainer,t.charCounter=t.maxCharCount>0||"boolean"==typeof t.charCounter&&t.charCounter,t.charCounterType="string"==typeof t.charCounterType?t.charCounterType:"char",t.charCounterLabel="string"==typeof t.charCounterLabel?t.charCounterLabel.trim():null,t.maxCharCount=a.Z.isNumber(t.maxCharCount)&&t.maxCharCount>-1?1*t.maxCharCount:null,t.width=t.width?a.Z.isNumber(t.width)?t.width+"px":t.width:e.clientWidth?e.clientWidth+"px":"100%",t.minWidth=(a.Z.isNumber(t.minWidth)?t.minWidth+"px":t.minWidth)||"",t.maxWidth=(a.Z.isNumber(t.maxWidth)?t.maxWidth+"px":t.maxWidth)||"",t.height=t.height?a.Z.isNumber(t.height)?t.height+"px":t.height:e.clientHeight?e.clientHeight+"px":"auto",t.minHeight=(a.Z.isNumber(t.minHeight)?t.minHeight+"px":t.minHeight)||"",t.maxHeight=(a.Z.isNumber(t.maxHeight)?t.maxHeight+"px":t.maxHeight)||"",t.className="string"==typeof t.className&&t.className.length>0?" "+t.className:"",t.defaultStyle="string"==typeof t.defaultStyle?t.defaultStyle:"",t.font=t.font?t.font:["Arial","Comic Sans MS","Courier New","Impact","Georgia","tahoma","Trebuchet MS","Verdana"],t.fontSize=t.fontSize?t.fontSize:null,t.formats=t.formats?t.formats:null,t.colorList=t.colorList?t.colorList:null,t.lineHeights=t.lineHeights?t.lineHeights:null,t.paragraphStyles=t.paragraphStyles?t.paragraphStyles:null,t.textStyles=t.textStyles?t.textStyles:null,t.fontSizeUnit="string"==typeof t.fontSizeUnit&&t.fontSizeUnit.trim().toLowerCase()||"px",t.alignItems="object"==typeof t.alignItems?t.alignItems:t.rtl?["right","center","left","justify"]:["left","center","right","justify"],t.imageResizing=void 0===t.imageResizing||t.imageResizing,t.imageHeightShow=void 0===t.imageHeightShow||!!t.imageHeightShow,t.imageAlignShow=void 0===t.imageAlignShow||!!t.imageAlignShow,t.imageWidth=t.imageWidth?a.Z.isNumber(t.imageWidth)?t.imageWidth+"px":t.imageWidth:"auto",t.imageHeight=t.imageHeight?a.Z.isNumber(t.imageHeight)?t.imageHeight+"px":t.imageHeight:"auto",t.imageSizeOnlyPercentage=!!t.imageSizeOnlyPercentage,t._imageSizeUnit=t.imageSizeOnlyPercentage?"%":"px",t.imageRotation=void 0!==t.imageRotation?t.imageRotation:!(t.imageSizeOnlyPercentage||!t.imageHeightShow),t.imageFileInput=void 0===t.imageFileInput||t.imageFileInput,t.imageUrlInput=void 0===t.imageUrlInput||!t.imageFileInput||t.imageUrlInput,t.imageUploadHeader=t.imageUploadHeader||null,t.imageUploadUrl="string"==typeof t.imageUploadUrl?t.imageUploadUrl:null,t.imageUploadSizeLimit=/\d+/.test(t.imageUploadSizeLimit)?a.Z.getNumber(t.imageUploadSizeLimit,0):null,t.imageMultipleFile=!!t.imageMultipleFile,t.imageAccept="string"!=typeof t.imageAccept||"*"===t.imageAccept.trim()?"image/*":t.imageAccept.trim()||"image/*",t.imageGalleryData=t.imageGalleryData||null,t.imageGalleryUrl="string"==typeof t.imageGalleryUrl?t.imageGalleryUrl:null,t.imageGalleryHeader=t.imageGalleryHeader||null,t.videoResizing=void 0===t.videoResizing||t.videoResizing,t.videoHeightShow=void 0===t.videoHeightShow||!!t.videoHeightShow,t.videoAlignShow=void 0===t.videoAlignShow||!!t.videoAlignShow,t.videoRatioShow=void 0===t.videoRatioShow||!!t.videoRatioShow,t.videoWidth=t.videoWidth&&a.Z.getNumber(t.videoWidth,0)?a.Z.isNumber(t.videoWidth)?t.videoWidth+"px":t.videoWidth:"",t.videoHeight=t.videoHeight&&a.Z.getNumber(t.videoHeight,0)?a.Z.isNumber(t.videoHeight)?t.videoHeight+"px":t.videoHeight:"",t.videoSizeOnlyPercentage=!!t.videoSizeOnlyPercentage,t._videoSizeUnit=t.videoSizeOnlyPercentage?"%":"px",t.videoRotation=void 0!==t.videoRotation?t.videoRotation:!(t.videoSizeOnlyPercentage||!t.videoHeightShow),t.videoRatio=a.Z.getNumber(t.videoRatio,4)||.5625,t.videoRatioList=t.videoRatioList?t.videoRatioList:null,t.youtubeQuery=(t.youtubeQuery||"").replace("?",""),t.vimeoQuery=(t.vimeoQuery||"").replace("?",""),t.videoFileInput=!!t.videoFileInput,t.videoUrlInput=void 0===t.videoUrlInput||!t.videoFileInput||t.videoUrlInput,t.videoUploadHeader=t.videoUploadHeader||null,t.videoUploadUrl="string"==typeof t.videoUploadUrl?t.videoUploadUrl:null,t.videoUploadSizeLimit=/\d+/.test(t.videoUploadSizeLimit)?a.Z.getNumber(t.videoUploadSizeLimit,0):null,t.videoMultipleFile=!!t.videoMultipleFile,t.videoTagAttrs=t.videoTagAttrs||null,t.videoIframeAttrs=t.videoIframeAttrs||null,t.videoAccept="string"!=typeof t.videoAccept||"*"===t.videoAccept.trim()?"video/*":t.videoAccept.trim()||"video/*",t.audioWidth=t.audioWidth?a.Z.isNumber(t.audioWidth)?t.audioWidth+"px":t.audioWidth:"",t.audioHeight=t.audioHeight?a.Z.isNumber(t.audioHeight)?t.audioHeight+"px":t.audioHeight:"",t.audioFileInput=!!t.audioFileInput,t.audioUrlInput=void 0===t.audioUrlInput||!t.audioFileInput||t.audioUrlInput,t.audioUploadHeader=t.audioUploadHeader||null,t.audioUploadUrl="string"==typeof t.audioUploadUrl?t.audioUploadUrl:null,t.audioUploadSizeLimit=/\d+/.test(t.audioUploadSizeLimit)?a.Z.getNumber(t.audioUploadSizeLimit,0):null,t.audioMultipleFile=!!t.audioMultipleFile,t.audioTagAttrs=t.audioTagAttrs||null,t.audioAccept="string"!=typeof t.audioAccept||"*"===t.audioAccept.trim()?"audio/*":t.audioAccept.trim()||"audio/*",t.tableCellControllerPosition="string"==typeof t.tableCellControllerPosition?t.tableCellControllerPosition.toLowerCase():"cell",t.linkTargetNewWindow=!!t.linkTargetNewWindow,t.linkProtocol="string"==typeof t.linkProtocol?t.linkProtocol:null,t.linkRel=Array.isArray(t.linkRel)?t.linkRel:[],t.linkRelDefault=t.linkRelDefault||{},t.tabDisable=!!t.tabDisable,t.shortcutsDisable=Array.isArray(t.shortcutsDisable)?t.shortcutsDisable:[],t.shortcutsHint=void 0===t.shortcutsHint||!!t.shortcutsHint,t.callBackSave=t.callBackSave?t.callBackSave:null,t.templates=t.templates?t.templates:null,t.placeholder="string"==typeof t.placeholder?t.placeholder:null,t.mediaAutoSelect=void 0===t.mediaAutoSelect||!!t.mediaAutoSelect,t.buttonList=t.buttonList?t.buttonList:[["undo","redo"],["bold","underline","italic","strike","subscript","superscript"],["removeFormat"],["outdent","indent"],["fullScreen","showBlocks","codeView"],["preview","print"]],t.rtl&&(t.buttonList=t.buttonList.reverse()),t.icons=t.icons&&"object"==typeof t.icons?[l,t.icons].reduce(function(e,t){for(let i in t)a.Z.hasOwn(t,i)&&(e[i]=t[i]);return e},{}):l,t.icons=t.rtl?[t.icons,t.icons.rtl].reduce(function(e,t){for(let i in t)a.Z.hasOwn(t,i)&&(e[i]=t[i]);return e},{}):t.icons,t.__listCommonStyle=t.__listCommonStyle||["fontSize","color","fontFamily","fontWeight","fontStyle"],t._editorStyles=a.Z._setDefaultOptionStyle(t,t.defaultStyle)},_setWhitelist:function(e,t){if("string"!=typeof t)return e;t=t.split("|"),e=e.split("|");for(let i=0,l=t.length,n;i<l;i++)(n=e.indexOf(t[i]))>-1&&e.splice(n,1);return e.join("|")},_defaultButtons:function(e){let t=e.icons,i=e.lang,l=a.Z.isOSX_IOS?"⌘":"CTRL",n=a.Z.isOSX_IOS?"⇧":"+SHIFT",o=e.shortcutsHint?e.shortcutsDisable:["bold","strike","underline","italic","undo","indent","save"],r=e.rtl?["[","]"]:["]","["],s=e.rtl?[t.outdent,t.indent]:[t.indent,t.outdent];return{bold:["",i.toolbar.bold+'<span class="se-shortcut">'+(o.indexOf("bold")>-1?"":l+'+<span class="se-shortcut-key">B</span>')+"</span>","bold","",t.bold],underline:["",i.toolbar.underline+'<span class="se-shortcut">'+(o.indexOf("underline")>-1?"":l+'+<span class="se-shortcut-key">U</span>')+"</span>","underline","",t.underline],italic:["",i.toolbar.italic+'<span class="se-shortcut">'+(o.indexOf("italic")>-1?"":l+'+<span class="se-shortcut-key">I</span>')+"</span>","italic","",t.italic],strike:["",i.toolbar.strike+'<span class="se-shortcut">'+(o.indexOf("strike")>-1?"":l+n+'+<span class="se-shortcut-key">S</span>')+"</span>","strike","",t.strike],subscript:["",i.toolbar.subscript,"SUB","",t.subscript],superscript:["",i.toolbar.superscript,"SUP","",t.superscript],removeFormat:["",i.toolbar.removeFormat,"removeFormat","",t.erase],indent:["",i.toolbar.indent+'<span class="se-shortcut">'+(o.indexOf("indent")>-1?"":l+'+<span class="se-shortcut-key">'+r[0]+"</span>")+"</span>","indent","",s[0]],outdent:["",i.toolbar.outdent+'<span class="se-shortcut">'+(o.indexOf("indent")>-1?"":l+'+<span class="se-shortcut-key">'+r[1]+"</span>")+"</span>","outdent","",s[1]],fullScreen:["se-code-view-enabled se-resizing-enabled",i.toolbar.fullScreen,"fullScreen","",t.expansion],showBlocks:["",i.toolbar.showBlocks,"showBlocks","",t.show_blocks],codeView:["se-code-view-enabled se-resizing-enabled",i.toolbar.codeView,"codeView","",t.code_view],undo:["",i.toolbar.undo+'<span class="se-shortcut">'+(o.indexOf("undo")>-1?"":l+'+<span class="se-shortcut-key">Z</span>')+"</span>","undo","",t.undo],redo:["",i.toolbar.redo+'<span class="se-shortcut">'+(o.indexOf("undo")>-1?"":l+'+<span class="se-shortcut-key">Y</span> / '+l+n+'+<span class="se-shortcut-key">Z</span>')+"</span>","redo","",t.redo],preview:["se-resizing-enabled",i.toolbar.preview,"preview","",t.preview],print:["se-resizing-enabled",i.toolbar.print,"print","",t.print],dir:["",i.toolbar[e.rtl?"dir_ltr":"dir_rtl"],"dir","",t[e.rtl?"dir_ltr":"dir_rtl"]],dir_ltr:["",i.toolbar.dir_ltr,"dir_ltr","",t.dir_ltr],dir_rtl:["",i.toolbar.dir_rtl,"dir_rtl","",t.dir_rtl],save:["se-resizing-enabled",i.toolbar.save+'<span class="se-shortcut">'+(o.indexOf("save")>-1?"":l+'+<span class="se-shortcut-key">S</span>')+"</span>","save","",t.save],blockquote:["",i.toolbar.tag_blockquote,"blockquote","command",t.blockquote],font:["se-btn-select se-btn-tool-font",i.toolbar.font,"font","submenu",'<span class="txt">'+i.toolbar.font+"</span>"+t.arrow_down],formatBlock:["se-btn-select se-btn-tool-format",i.toolbar.formats,"formatBlock","submenu",'<span class="txt">'+i.toolbar.formats+"</span>"+t.arrow_down],fontSize:["se-btn-select se-btn-tool-size",i.toolbar.fontSize,"fontSize","submenu",'<span class="txt">'+i.toolbar.fontSize+"</span>"+t.arrow_down],fontColor:["",i.toolbar.fontColor,"fontColor","submenu",t.font_color],hiliteColor:["",i.toolbar.hiliteColor,"hiliteColor","submenu",t.highlight_color],align:["se-btn-align",i.toolbar.align,"align","submenu",e.rtl?t.align_right:t.align_left],list:["",i.toolbar.list,"list","submenu",t.list_number],horizontalRule:["btn_line",i.toolbar.horizontalRule,"horizontalRule","submenu",t.horizontal_rule],table:["",i.toolbar.table,"table","submenu",t.table],lineHeight:["",i.toolbar.lineHeight,"lineHeight","submenu",t.line_height],template:["",i.toolbar.template,"template","submenu",t.template],paragraphStyle:["",i.toolbar.paragraphStyle,"paragraphStyle","submenu",t.paragraph_style],textStyle:["",i.toolbar.textStyle,"textStyle","submenu",t.text_style],link:["",i.toolbar.link,"link","dialog",t.link],image:["",i.toolbar.image,"image","dialog",t.image],video:["",i.toolbar.video,"video","dialog",t.video],audio:["",i.toolbar.audio,"audio","dialog",t.audio],math:["",i.toolbar.math,"math","dialog",t.math],imageGallery:["",i.toolbar.imageGallery,"imageGallery","fileBrowser",t.image_gallery]}},_createModuleGroup:function(){let e=a.Z.createElement("DIV");e.className="se-btn-module se-btn-module-border";let t=a.Z.createElement("UL");return t.className="se-menu-list",e.appendChild(t),{div:e,ul:t}},_createButton:function(e,t,i,l,n,o,r){let s=a.Z.createElement("LI"),u=a.Z.createElement("BUTTON"),c=t||i;return u.setAttribute("type","button"),u.setAttribute("class","se-btn"+(e?" "+e:"")+" se-tooltip"),u.setAttribute("data-command",i),u.setAttribute("data-display",l),u.setAttribute("aria-label",c.replace(/<span .+<\/span>/,"")),u.setAttribute("tabindex","-1"),n||(n='<span class="se-icon-text">!</span>'),/^default\./i.test(n)&&(n=r[n.replace(/^default\./i,"")]),/^text\./i.test(n)&&(n=n.replace(/^text\./i,""),u.className+=" se-btn-more-text"),n+='<span class="se-tooltip-inner"><span class="se-tooltip-text">'+c+"</span></span>",o&&u.setAttribute("disabled",!0),u.innerHTML=n,s.appendChild(u),{li:s,button:u}},_createToolBar:function(e,t,i,l){let n=e.createElement("DIV");n.className="se-toolbar-separator-vertical";let o=e.createElement("DIV");o.className="se-toolbar sun-editor-common";let r=e.createElement("DIV");r.className="se-btn-tray",o.appendChild(r),t=JSON.parse(JSON.stringify(t));let s=l.icons,u=this._defaultButtons(l),c={},d=[],h=null,p=null,g=null,m=null,f="",b=!1,v=a.Z.createElement("DIV");v.className="se-toolbar-more-layer";e:for(let l=0,o,_,w,y,x;l<t.length;l++)if(o=!1,x="",y=t[l],g=this._createModuleGroup(),"object"==typeof y){for(let e=0,n;e<y.length;e++){if(p=y[e],n=!1,/^\%\d+/.test(p)&&0===e){y[0]=p.replace(/[^\d]/g,""),d.push(y),t.splice(l--,1);continue e}if("object"==typeof p)"function"==typeof p.add?(h=u[f=p.name],i[f]=p):(f=p.name,h=[p.buttonClass,p.title,p.name,p.dataDisplay,p.innerHTML,p._disabled]);else{if(/^\-/.test(p)){x=p.substr(1),g.div.className+=" module-float-"+x;continue}if(/^\#/.test(p)){"fix"===p.substr(1)&&(g.ul.className+=" se-menu-dir-fix");continue}if(/^\:/.test(p)){n=!0;let e=p.match(/^\:([^\-]+)\-([^\-]+)\-([^\-]+)/);w="__se__"+e[1].trim(),h=["se-btn-more",e[2].trim(),w,"MORE",e[3].trim()]}else h=u[p];if(f=p,!h){let e=i[f];if(!e)throw Error("[SUNEDITOR.create.toolbar.fail] The button name of a plugin that does not exist. ["+f+"]");h=[e.buttonClass,e.title,e.name,e.display,e.innerHTML,e._disabled]}}m=this._createButton(h[0],h[1],h[2],h[3],h[4],h[5],s),(o?_:g.ul).appendChild(m.li),i[f]&&(c[f]=m.button),n&&(o=!0,(_=a.Z.createElement("DIV")).className="se-more-layer "+w,_.innerHTML='<div class="se-more-form"><ul class="se-menu-list"'+(x?' style="float: '+x+';"':"")+"></ul></div>",v.appendChild(_),_=_.firstElementChild.firstElementChild)}if(b){let e=n.cloneNode(!1);r.appendChild(e)}r.appendChild(g.div),b=!0}else if(/^\/$/.test(y)){let t=e.createElement("DIV");t.className="se-btn-module-enter",r.appendChild(t),b=!1}switch(r.children.length){case 0:r.style.display="none";break;case 1:a.Z.removeClass(r.firstElementChild,"se-btn-module-border");break;default:if(l.rtl){let e=n.cloneNode(!1);e.style.float=r.lastElementChild.style.float,r.appendChild(e)}}d.length>0&&d.unshift(t),v.children.length>0&&r.appendChild(v);let _=e.createElement("DIV");_.className="se-menu-tray",o.appendChild(_);let w=e.createElement("DIV");return w.className="se-toolbar-cover",o.appendChild(w),l.hideToolbar&&(o.style.display="none"),{element:o,plugins:i,pluginCallButtons:c,responsiveButtons:d,_menuTray:_,_buttonTray:r}}}},78902:function(e,t){"use strict";t.Z=function(e,t,i){return{element:{originElement:e,topArea:t._top,relative:t._relative,toolbar:t._toolBar,_toolbarShadow:t._toolbarShadow,_buttonTray:t._toolBar.querySelector(".se-btn-tray"),_menuTray:t._menuTray,resizingBar:t._resizingBar,navigation:t._navigation,charWrapper:t._charWrapper,charCounter:t._charCounter,editorArea:t._editorArea,wysiwygFrame:t._wysiwygArea,wysiwyg:t._wysiwygArea,code:t._codeArea,placeholder:t._placeholder,loading:t._loading,lineBreaker:t._lineBreaker,lineBreaker_t:t._lineBreaker_t,lineBreaker_b:t._lineBreaker_b,resizeBackground:t._resizeBack,_stickyDummy:t._stickyDummy,_arrow:t._arrow,_focusTemp:t._focusTemp},tool:{cover:t._toolBar.querySelector(".se-toolbar-cover"),bold:t._toolBar.querySelector('[data-command="bold"]'),underline:t._toolBar.querySelector('[data-command="underline"]'),italic:t._toolBar.querySelector('[data-command="italic"]'),strike:t._toolBar.querySelector('[data-command="strike"]'),sub:t._toolBar.querySelector('[data-command="SUB"]'),sup:t._toolBar.querySelector('[data-command="SUP"]'),undo:t._toolBar.querySelector('[data-command="undo"]'),redo:t._toolBar.querySelector('[data-command="redo"]'),save:t._toolBar.querySelector('[data-command="save"]'),outdent:t._toolBar.querySelector('[data-command="outdent"]'),indent:t._toolBar.querySelector('[data-command="indent"]'),fullScreen:t._toolBar.querySelector('[data-command="fullScreen"]'),showBlocks:t._toolBar.querySelector('[data-command="showBlocks"]'),codeView:t._toolBar.querySelector('[data-command="codeView"]'),dir:t._toolBar.querySelector('[data-command="dir"]'),dir_ltr:t._toolBar.querySelector('[data-command="dir_ltr"]'),dir_rtl:t._toolBar.querySelector('[data-command="dir_rtl"]')},options:i,option:i}}},74935:function(e,t,i){"use strict";function l(e,t){let i=e._w,l=e.util,n=e.options.historyStackDelayTime,o=e.context.element,a=e.context.tool.undo,r=e.context.tool.redo,s=null,u=0,c=[];function d(){let i=c[u];o.wysiwyg.innerHTML=i.contents,e.setRange(l.getNodeFromPath(i.s.path,o.wysiwyg),i.s.offset,l.getNodeFromPath(i.e.path,o.wysiwyg),i.e.offset),e.focus(),c.length<=1?(a&&a.setAttribute("disabled",!0),r&&r.setAttribute("disabled",!0)):0===u?(a&&a.setAttribute("disabled",!0),r&&r.removeAttribute("disabled")):u===c.length-1?(a&&a.removeAttribute("disabled"),r&&r.setAttribute("disabled",!0)):(a&&a.removeAttribute("disabled"),r&&r.removeAttribute("disabled")),e.controllersOff(),e._checkComponents(),e._setCharCount(),e._resourcesStateChange(),t()}function h(){e._checkComponents();let i=o.wysiwyg.innerHTML;if(!i||c[u]&&i===c[u].contents)return;u++;let n=e._variable._range;c.length>u&&(c=c.slice(0,u),r&&r.setAttribute("disabled",!0)),n?c[u]={contents:i,s:{path:l.getNodePath(n.startContainer,null,null),offset:n.startOffset},e:{path:l.getNodePath(n.endContainer,null,null),offset:n.endOffset}}:c[u]={contents:i,s:{path:[0,0],offset:[0,0]},e:{path:0,offset:0}},1===u&&a&&a.removeAttribute("disabled"),e._setCharCount(),t()}return{stack:c,push:function(t){i.setTimeout(e._resourcesStateChange.bind(e));let l="number"==typeof t?t>0?t:0:t?n:0;if((!l||s)&&(i.clearTimeout(s),!l)){h();return}s=i.setTimeout(function(){i.clearTimeout(s),s=null,h()},l)},undo:function(){u>0&&(u--,d())},redo:function(){c.length-1>u&&(u++,d())},go:function(e){u=e<0?c.length-1:e,d()},getCurrentIndex:function(){return u},reset:function(i){a&&a.setAttribute("disabled",!0),r&&r.setAttribute("disabled",!0),e._variable.isChanged=!1,e.context.tool.save&&e.context.tool.save.setAttribute("disabled",!0),c.splice(0),c[u=0]={contents:e.getContents(!0),s:{path:[0,0],offset:0},e:{path:[0,0],offset:0}},i||t()},_resetCachingButton:function(){o=e.context.element,a=e.context.tool.undo,r=e.context.tool.redo,0===u?(a&&a.setAttribute("disabled",!0),r&&u===c.length-1&&r.setAttribute("disabled",!0),e._variable.isChanged=!1,e.context.tool.save&&e.context.tool.save.setAttribute("disabled",!0)):u===c.length-1&&r&&r.setAttribute("disabled",!0)},_destroy:function(){s&&i.clearTimeout(s),c=null}}}i.d(t,{Z:function(){return l}})},37689:function(e,t){"use strict";let i={_d:null,_w:null,isIE:null,isIE_Edge:null,isOSX_IOS:null,isChromium:null,isMobile:null,isResizeObserverSupported:null,_propertiesInit:function(){this._d||(this._d=document,this._w=window,this.isIE=navigator.userAgent.indexOf("Trident")>-1,this.isIE_Edge=navigator.userAgent.indexOf("Trident")>-1||navigator.appVersion.indexOf("Edge")>-1,this.isOSX_IOS=/(Mac|iPhone|iPod|iPad)/.test(navigator.platform),this.isChromium=!!window.chrome,this.isResizeObserverSupported="function"==typeof ResizeObserver,this.isMobile=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)||(navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0)&&"ontouchstart"in window)},_allowedEmptyNodeList:".se-component, pre, blockquote, hr, li, table, img, iframe, video, audio, canvas",_HTMLConvertor:function(e){let t={"&":"&amp;","\xa0":"&nbsp;","'":"&apos;",'"':"&quot;","<":"&lt;",">":"&gt;"};return e.replace(/&|\u00A0|'|"|<|>/g,function(e){return"string"==typeof t[e]?t[e]:e})},zeroWidthSpace:String.fromCharCode(8203),zeroWidthRegExp:RegExp(String.fromCharCode(8203),"g"),onlyZeroWidthRegExp:RegExp("^"+String.fromCharCode(8203)+"+$"),fontValueMap:{"xx-small":1,"x-small":2,small:3,medium:4,large:5,"x-large":6,"xx-large":7},onlyZeroWidthSpace:function(e){return null!=e&&("string"!=typeof e&&(e=e.textContent),""===e||this.onlyZeroWidthRegExp.test(e))},getXMLHttpRequest:function(){if(this._w.ActiveXObject)try{return new ActiveXObject("Msxml2.XMLHTTP")}catch(e){try{return new ActiveXObject("Microsoft.XMLHTTP")}catch(e){return null}}else if(this._w.XMLHttpRequest)return new XMLHttpRequest;else return null},getValues:function(e){return e?this._w.Object.keys(e).map(function(t){return e[t]}):[]},camelToKebabCase:function(e){return"string"==typeof e?e.replace(/[A-Z]/g,function(e){return"-"+e.toLowerCase()}):e.map(function(e){return i.camelToKebabCase(e)})},kebabToCamelCase:function(e){return"string"==typeof e?e.replace(/-[a-zA-Z]/g,function(e){return e.replace("-","").toUpperCase()}):e.map(function(e){return i.camelToKebabCase(e)})},createElement:function(e){return this._d.createElement(e)},createTextNode:function(e){return this._d.createTextNode(e||"")},HTMLEncoder:function(e){let t={"<":"$lt;",">":"$gt;"};return e.replace(/<|>/g,function(e){return"string"==typeof t[e]?t[e]:e})},HTMLDecoder:function(e){let t={"$lt;":"<","$gt;":">"};return e.replace(/\$lt;|\$gt;/g,function(e){return"string"==typeof t[e]?t[e]:e})},hasOwn:function(e,t){return this._hasOwn.call(e,t)},_hasOwn:Object.prototype.hasOwnProperty,getIncludePath:function(e,t){let i="",l=[],n="js"===t?"src":"href",o="(?:";for(let t=0,i=e.length;t<i;t++)o+=e[t]+(t<i-1?"|":")");let a=new this._w.RegExp("(^|.*[\\/])"+o+"(\\.[^\\/]+)?."+t+"(?:\\?.*|;.*)?$","i"),r=new this._w.RegExp(".+\\."+t+"(?:\\?.*|;.*)?$","i");for(let e=this._d.getElementsByTagName("js"===t?"script":"link"),i=0;i<e.length;i++)r.test(e[i][n])&&l.push(e[i]);for(let e=0;e<l.length;e++){let t=l[e][n].match(a);if(t){i=t[0];break}}if(""===i&&(i=l.length>0?l[0][n]:""),-1===i.indexOf(":/")&&"//"!==i.slice(0,2)&&(i=0===i.indexOf("/")?location.href.match(/^.*?:\/\/[^\/]*/)[0]+i:location.href.match(/^[^\?]*\/(?:)/)[0]+i),!i)throw"[SUNEDITOR.util.getIncludePath.fail] The SUNEDITOR installation path could not be automatically detected. (name: +"+name+", extension: "+t+")";return i},getPageStyle:function(e){let t="",i=(e||this._d).styleSheets;for(let e=0,l=i.length,n;e<l;e++){try{n=i[e].cssRules}catch(e){continue}if(n)for(let e=0,i=n.length;e<i;e++)t+=n[e].cssText}return t},getIframeDocument:function(e){let t=e.contentWindow||e.contentDocument;return t.document&&(t=t.document),t},getAttributesToString:function(e,t){if(!e.attributes)return"";let i=e.attributes,l="";for(let e=0,n=i.length;e<n;e++)t&&t.indexOf(i[e].name)>-1||(l+=i[e].name+'="'+i[e].value+'" ');return l},getByteLength:function(e){let t,i;if(!e||!e.toString)return 0;e=e.toString();let l=this._w.encodeURIComponent;return i=this.isIE_Edge?this._w.unescape(l(e)).length:new this._w.TextEncoder("utf-8").encode(e).length,t=0,null!==l(e).match(/(%0A|%0D)/gi)&&(t=l(e).match(/(%0A|%0D)/gi).length),i+t},isWysiwygDiv:function(e){return e&&1===e.nodeType&&(this.hasClass(e,"se-wrapper-wysiwyg")||/^BODY$/i.test(e.nodeName))},isNonEditable:function(e){return e&&1===e.nodeType&&"false"===e.getAttribute("contenteditable")},isTextStyleElement:function(e){return e&&3!==e.nodeType&&/^(strong|span|font|b|var|i|em|u|ins|s|strike|del|sub|sup|mark|a|label|code|summary)$/i.test(e.nodeName)},isInputElement:function(e){return e&&1===e.nodeType&&/^(INPUT|TEXTAREA)$/i.test(e.nodeName)},isFormatElement:function(e){return e&&1===e.nodeType&&(/^(P|DIV|H[1-6]|PRE|LI|TH|TD|DETAILS)$/i.test(e.nodeName)||this.hasClass(e,"(\\s|^)__se__format__replace_.+(\\s|$)|(\\s|^)__se__format__free_.+(\\s|$)"))&&!this.isComponent(e)&&!this.isWysiwygDiv(e)},isRangeFormatElement:function(e){return e&&1===e.nodeType&&(/^(BLOCKQUOTE|OL|UL|FIGCAPTION|TABLE|THEAD|TBODY|TR|TH|TD|DETAILS)$/i.test(e.nodeName)||this.hasClass(e,"(\\s|^)__se__format__range_.+(\\s|$)"))},isClosureRangeFormatElement:function(e){return e&&1===e.nodeType&&(/^(TH|TD)$/i.test(e.nodeName)||this.hasClass(e,"(\\s|^)__se__format__range__closure_.+(\\s|$)"))},isFreeFormatElement:function(e){return e&&1===e.nodeType&&(/^PRE$/i.test(e.nodeName)||this.hasClass(e,"(\\s|^)__se__format__free_.+(\\s|$)"))&&!this.isComponent(e)&&!this.isWysiwygDiv(e)},isClosureFreeFormatElement:function(e){return e&&1===e.nodeType&&this.hasClass(e,"(\\s|^)__se__format__free__closure_.+(\\s|$)")},isComponent:function(e){return e&&(/se-component/.test(e.className)||/^(TABLE|HR)$/.test(e.nodeName))},isUneditableComponent:function(e){return e&&this.hasClass(e,"__se__uneditable")},isMediaComponent:function(e){return e&&/se-component/.test(e.className)},isNotCheckingNode:function(e){return e&&/katex|__se__tag/.test(e.className)},getFormatElement:function(e,t){if(!e)return null;for(t||(t=function(){return!0});e&&!this.isWysiwygDiv(e);){if(this.isRangeFormatElement(e)&&e.firstElementChild,this.isFormatElement(e)&&t(e))return e;e=e.parentNode}return null},getRangeFormatElement:function(e,t){if(!e)return null;for(t||(t=function(){return!0});e&&!this.isWysiwygDiv(e);){if(this.isRangeFormatElement(e)&&!/^(THEAD|TBODY|TR)$/i.test(e.nodeName)&&t(e))return e;e=e.parentNode}return null},getFreeFormatElement:function(e,t){if(!e)return null;for(t||(t=function(){return!0});e&&!this.isWysiwygDiv(e);){if(this.isFreeFormatElement(e)&&t(e))return e;e=e.parentNode}return null},getClosureFreeFormatElement:function(e,t){if(!e)return null;for(t||(t=function(){return!0});e&&!this.isWysiwygDiv(e);){if(this.isClosureFreeFormatElement(e)&&t(e))return e;e=e.parentNode}return null},copyTagAttributes:function(e,t,i){if(t.style.cssText){let i=t.style;for(let t=0,l=i.length;t<l;t++)e.style[i[t]]=i[i[t]]}let l=t.attributes;for(let t=0,n=l.length,o;t<n;t++)o=l[t].name.toLowerCase(),i&&i.indexOf(o)>-1||!l[t].value?e.removeAttribute(o):"style"!==o&&e.setAttribute(l[t].name,l[t].value)},copyFormatAttributes:function(e,t){(t=t.cloneNode(!1)).className=t.className.replace(/(\s|^)__se__format__[^\s]+/g,""),this.copyTagAttributes(e,t)},getArrayItem:function(e,t,i){if(!e||0===e.length)return null;t=t||function(){return!0};let l=[];for(let n=0,o=e.length,a;n<o;n++)if(t(a=e[n])){if(!i)return a;l.push(a)}return i?l:null},arrayIncludes:function(e,t){for(let i=0;i<e.length;i++)if(e[i]===t)return!0;return!1},getArrayIndex:function(e,t){let i=-1;for(let l=0,n=e.length;l<n;l++)if(e[l]===t){i=l;break}return i},nextIdx:function(e,t){let i=this.getArrayIndex(e,t);return -1===i?-1:i+1},prevIdx:function(e,t){let i=this.getArrayIndex(e,t);return -1===i?-1:i-1},getPositionIndex:function(e){let t=0;for(;e=e.previousSibling;)t+=1;return t},getNodePath:function(e,t,i){let l=[],n=!0;return this.getParentElement(e,(function(e){if(e===t&&(n=!1),n&&!this.isWysiwygDiv(e)){if(i&&3===e.nodeType){let t=null,l=null;i.s=i.e=0;let n=e.previousSibling;for(;n&&3===n.nodeType;)l=n.textContent.replace(this.zeroWidthRegExp,""),i.s+=l.length,e.textContent=l+e.textContent,t=n,n=n.previousSibling,this.removeItem(t);let o=e.nextSibling;for(;o&&3===o.nodeType;)l=o.textContent.replace(this.zeroWidthRegExp,""),i.e+=l.length,e.textContent+=l,t=o,o=o.nextSibling,this.removeItem(t)}l.push(e)}return!1}).bind(this)),l.map(this.getPositionIndex).reverse()},getNodeFromPath:function(e,t){let i,l=t;for(let t=0,n=e.length;t<n&&0!==(i=l.childNodes).length;t++)l=i.length<=e[t]?i[i.length-1]:i[e[t]];return l},isSameAttributes:function(e,t){if(3===e.nodeType&&3===t.nodeType)return!0;if(3===e.nodeType||3===t.nodeType)return!1;let i=e.style,l=t.style,n=0;for(let e=0,t=i.length;e<t;e++)i[i[e]]===l[i[e]]&&n++;let o=e.classList,a=t.classList,r=this._w.RegExp,s=0;for(let e=0,t=o.length;e<t;e++)r("(s|^)"+o[e]+"(s|$)").test(a.value)&&s++;return n===l.length&&n===i.length&&s===a.length&&s===o.length},isEmptyLine:function(e){return!e||!e.parentNode||!e.querySelector("IMG, IFRAME, AUDIO, VIDEO, CANVAS, TABLE")&&0===e.children.length&&this.onlyZeroWidthSpace(e.textContent)},isSpanWithoutAttr:function(e){return!!e&&1===e.nodeType&&/^SPAN$/i.test(e.nodeName)&&!e.className&&!e.style.cssText},isList:function(e){return e&&/^(OL|UL)$/i.test("string"==typeof e?e:e.nodeName)},isListCell:function(e){return e&&/^LI$/i.test("string"==typeof e?e:e.nodeName)},isTable:function(e){return e&&/^(TABLE|THEAD|TBODY|TR|TH|TD)$/i.test("string"==typeof e?e:e.nodeName)},isCell:function(e){return e&&/^(TD|TH)$/i.test("string"==typeof e?e:e.nodeName)},isBreak:function(e){return e&&/^BR$/i.test("string"==typeof e?e:e.nodeName)},isAnchor:function(e){return e&&/^A$/i.test("string"==typeof e?e:e.nodeName)},isMedia:function(e){return e&&/^(IMG|IFRAME|AUDIO|VIDEO|CANVAS)$/i.test("string"==typeof e?e:e.nodeName)},isFigures:function(e){return e&&(this.isMedia(e)||/^(FIGURE)$/i.test("string"==typeof e?e:e.nodeName))},isNumber:function(e){return!!e&&/^-?\d+(\.\d+)?$/.test(e+"")},getNumber:function(e,t){if(!e)return 0;let i=(e+"").match(/-?\d+(\.\d+)?/);return i&&i[0]?(i=i[0],t<0?1*i:0===t?this._w.Math.round(1*i):1*(1*i).toFixed(t)):0},getListChildren:function(e,t){let i=[];return e&&e.children&&0!==e.children.length&&(t=t||function(){return!0},function l(n){if(e!==n&&t(n)&&i.push(n),n.children)for(let e=0,t=n.children.length;e<t;e++)l(n.children[e])}(e)),i},getListChildNodes:function(e,t){let i=[];return e&&0!==e.childNodes.length&&(t=t||function(){return!0},function l(n){e!==n&&t(n)&&i.push(n);for(let e=0,t=n.childNodes.length;e<t;e++)l(n.childNodes[e])}(e)),i},getElementDepth:function(e){if(!e||this.isWysiwygDiv(e))return -1;let t=0;for(e=e.parentNode;e&&!this.isWysiwygDiv(e);)t+=1,e=e.parentNode;return t},compareElements:function(e,t){let i=e,l=t;for(;i&&l&&i.parentNode!==l.parentNode;)i=i.parentNode,l=l.parentNode;if(!i||!l)return{ancestor:null,a:e,b:t,result:0};let n=i.parentNode.childNodes,o=this.getArrayIndex(n,i),a=this.getArrayIndex(n,l);return{ancestor:i.parentNode,a:i,b:l,result:o>a?1:o<a?-1:0}},getParentElement:function(e,t){let i;if("function"==typeof t)i=t;else{let e;/^\./.test(t)?(e="className",t=t.split(".")[1]):/^#/.test(t)?(e="id",t="^"+t.split("#")[1]+"$"):/^:/.test(t)?(e="name",t="^"+t.split(":")[1]+"$"):(e="nodeName",t="^"+t+"$");let l=new this._w.RegExp(t,"i");i=function(t){return l.test(t[e])}}for(;e&&!i(e);){if(this.isWysiwygDiv(e))return null;e=e.parentNode}return e},getPreviousDeepestNode:function(e,t){let i=e.previousSibling;if(!i){for(let l=e.parentNode;l;l=l.parentNode){if(l===t)return null;if(l.previousSibling){i=l.previousSibling;break}}if(!i)return null}for(;i.lastChild;)i=i.lastChild;return i},getNextDeepestNode:function(e,t){let i=e.nextSibling;if(!i){for(let l=e.parentNode;l;l=l.parentNode){if(l===t)return null;if(l.nextSibling){i=l.nextSibling;break}}if(!i)return null}for(;i.firstChild;)i=i.firstChild;return i},getChildElement:function(e,t,i){let l;if("function"==typeof t)l=t;else{let e;/^\./.test(t)?(e="className",t=t.split(".")[1]):/^#/.test(t)?(e="id",t="^"+t.split("#")[1]+"$"):/^:/.test(t)?(e="name",t="^"+t.split(":")[1]+"$"):(e="nodeName",t="^"+("text"===t?"#"+t:t)+"$");let i=new this._w.RegExp(t,"i");l=function(t){return i.test(t[e])}}let n=this.getListChildNodes(e,function(e){return l(e)});return n[i?n.length-1:0]},getEdgeChildNodes:function(e,t){if(e){for(t||(t=e);e&&1===e.nodeType&&e.childNodes.length>0&&!this.isBreak(e);)e=e.firstChild;for(;t&&1===t.nodeType&&t.childNodes.length>0&&!this.isBreak(t);)t=t.lastChild;return{sc:e,ec:t||e}}},getOffset:function(e,t){let i=0,l=0,n=3===e.nodeType?e.parentElement:e,o=this.getParentElement(e,this.isWysiwygDiv.bind(this));for(;n&&!this.hasClass(n,"se-container")&&n!==o;)i+=n.offsetLeft,l+=n.offsetTop,n=n.offsetParent;let a=t&&/iframe/i.test(t.nodeName);return{left:i+(a?t.parentElement.offsetLeft:0),top:l-(o?o.scrollTop:0)+(a?t.parentElement.offsetTop:0)}},getOverlapRangeAtIndex:function(e,t,i,l){if(e<=l?t<i:t>i)return 0;let n=(e>i?e:i)-(t<l?t:l);return(n<0?-1*n:n)+1},changeTxt:function(e,t){e&&t&&(e.textContent=t)},changeElement:function(e,t){if("string"==typeof t){if(e.outerHTML)e.outerHTML=t;else{let i=this.createElement("DIV");i.innerHTML=t,t=i.firstChild,e.parentNode.replaceChild(t,e)}}else 1===t.nodeType&&e.parentNode.replaceChild(t,e)},setStyle:function(e,t,i){e.style[t]=i,i||e.style.cssText||e.removeAttribute("style")},hasClass:function(e,t){if(e)return new this._w.RegExp(t).test(e.className)},addClass:function(e,t){e&&(new this._w.RegExp("(\\s|^)"+t+"(\\s|$)").test(e.className)||(e.className+=(e.className.length>0?" ":"")+t))},removeClass:function(e,t){if(!e)return;let i=new this._w.RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(i," ").trim(),e.className.trim()||e.removeAttribute("class")},toggleClass:function(e,t){if(!e)return;let i=!1,l=new this._w.RegExp("(\\s|^)"+t+"(\\s|$)");return l.test(e.className)?e.className=e.className.replace(l," ").trim():(e.className+=" "+t,i=!0),e.className.trim()||e.removeAttribute("class"),i},isImportantDisabled:function(e){return e.hasAttribute("data-important-disabled")},setDisabledButtons:function(e,t,i){for(let l=0,n=t.length;l<n;l++){let n=t[l];(i||!this.isImportantDisabled(n))&&(n.disabled=e),i&&(e?n.setAttribute("data-important-disabled",""):n.removeAttribute("data-important-disabled"))}},removeItem:function(e){e&&("function"==typeof e.remove?e.remove():e.parentNode&&e.parentNode.removeChild(e))},removeItemAllParents:function(e,t,l){if(!e)return null;let n=null;return t||(t=(function(e){if(e===l||this.isComponent(e))return!1;let t=e.textContent.trim();return 0===t.length||/^(\n|\u200B)+$/.test(t)}).bind(this)),!function e(l){if(!i.isWysiwygDiv(l)){let o=l.parentNode;o&&t(l)&&(n={sc:l.previousElementSibling,ec:l.nextElementSibling},i.removeItem(l),e(o))}}(e),n},detachNestedList:function(e,t){let i,l,n;let o=this._deleteNestedList(e);if(o){i=o.cloneNode(!1),l=o.childNodes;let t=this.getPositionIndex(e);for(;l[t];)i.appendChild(l[t])}else i=e;if(t)n=this.getListChildren(i,(function(e){return this.isListCell(e)&&!e.previousElementSibling}).bind(this));else{let t=this.getElementDepth(e)+2;n=this.getListChildren(e,(function(e){return this.isListCell(e)&&!e.previousElementSibling&&this.getElementDepth(e)===t}).bind(this))}for(let e=0,t=n.length;e<t;e++)this._deleteNestedList(n[e]);return o&&(o.parentNode.insertBefore(i,o.nextSibling),l&&0===l.length&&this.removeItem(o)),i===e?i.parentNode:i},_deleteNestedList:function(e){let t,i,l,n,o;let a=e.parentNode,r=a,s=r.parentNode;for(;this.isListCell(s);){for(n=this.getPositionIndex(e),t=s.nextElementSibling,i=s.parentNode,l=r;l;){if(r=r.nextSibling,this.isList(l)){for(o=l.childNodes;o[n];)i.insertBefore(o[n],t);0===o.length&&this.removeItem(l)}else i.appendChild(l);l=r}r=i,s=i.parentNode}return 0===a.children.length&&this.removeItem(a),i},splitElement:function(e,t,l){let n,o,a;if(this.isWysiwygDiv(e))return e;if(t&&!this.isNumber(t)){let i=e.childNodes,l=this.getPositionIndex(t),n=e.cloneNode(!1),o=e.cloneNode(!1);for(let e=0,t=i.length;e<t;e++){if(e<l)n.appendChild(i[e]);else{if(!(e>l))continue;o.appendChild(i[e])}e--,t--,l--}return n.childNodes.length>0&&e.parentNode.insertBefore(n,e),o.childNodes.length>0&&e.parentNode.insertBefore(o,e.nextElementSibling),e}let r=e.parentNode,s=0,u=1,c=!0;if((!l||l<0)&&(l=0),3===e.nodeType){if(s=this.getPositionIndex(e),t>=0&&e.length!==t){e.splitText(t);let i=this.getNodeFromPath([s+1],r);this.onlyZeroWidthSpace(i)&&(i.data=this.zeroWidthSpace)}}else if(1===e.nodeType){if(0===t){for(;e.firstChild;)e=e.firstChild;if(3===e.nodeType){let t=this.createTextNode(this.zeroWidthSpace);e.parentNode.insertBefore(t,e),e=t}}e.previousSibling?e=e.previousSibling:this.getElementDepth(e)===l&&(c=!1)}1===e.nodeType&&(u=0);let d=e;for(;this.getElementDepth(d)>l;)for(s=this.getPositionIndex(d)+u,d=d.parentNode,a=n,n=d.cloneNode(!1),o=d.childNodes,a&&(this.isListCell(n)&&this.isList(a)&&a.firstElementChild?(n.innerHTML=a.firstElementChild.innerHTML,i.removeItem(a.firstElementChild),a.children.length>0&&n.appendChild(a)):n.appendChild(a));o[s];)n.appendChild(o[s]);d.childNodes.length<=1&&(!d.firstChild||0===d.firstChild.textContent.length)&&(d.innerHTML="<br>");let h=d.parentNode;return(c&&(d=d.nextSibling),n)?(this.mergeSameTags(n,null,!1),this.mergeNestedTags(n,(function(e){return this.isList(e)}).bind(this)),n.childNodes.length>0?h.insertBefore(n,d):n=d,this.isListCell(n)&&n.children&&this.isList(n.children[0])&&n.insertBefore(this.createElement("BR"),n.children[0]),0===r.childNodes.length&&this.removeItem(r),n):d},mergeSameTags:function(e,t,i){let l=this,n=t?t.length:0,o=null;return n&&(o=this._w.Array.apply(null,new this._w.Array(n)).map(this._w.Number.prototype.valueOf,0)),!function e(a,r,s){let u=a.childNodes;for(let c=0,d=u.length,h,p;c<d&&(h=u[c],p=u[c+1],h);c++)if(!(l.isBreak(h)||l.isMedia(h)||l.isInputElement(h))){if(i&&l._isIgnoreNodeChange(h)||!i&&(l.isTable(h)||l.isListCell(h)||l.isFormatElement(h)&&!l.isFreeFormatElement(h))){(l.isTable(h)||l.isListCell(h))&&e(h,r+1,c);continue}if(1===d&&a.nodeName===h.nodeName&&a.parentNode){if(n){let e,i,o,s,u;for(let d=0;d<n;d++)if((e=t[d])&&e[r]===c){for(i=h,o=a,s=r,u=!0;s>=0;){if(l.getArrayIndex(o.childNodes,i)!==e[s]){u=!1;break}o=(i=h.parentNode).parentNode,s--}u&&(e.splice(r,1),e[r]=c)}}l.copyTagAttributes(h,a),a.parentNode.insertBefore(h,a),l.removeItem(a)}if(!p){1===h.nodeType&&e(h,r+1,c);break}if(h.nodeName===p.nodeName&&l.isSameAttributes(h,p)&&h.href===p.href){let e=h.childNodes,i=0;for(let t=0,l=e.length;t<l;t++)e[t].textContent.length>0&&i++;let a=h.lastChild,u=p.firstChild,d=0;if(a&&u){let e=3===a.nodeType&&3===u.nodeType;d=a.textContent.length;let l=a.previousSibling;for(;l&&3===l.nodeType;)d+=l.textContent.length,l=l.previousSibling;if(i>0&&3===a.nodeType&&3===u.nodeType&&(a.textContent.length>0||u.textContent.length>0)&&i--,n){let l=null;for(let h=0;h<n;h++)if((l=t[h])&&l[r]>c){if(r>0&&l[r-1]!==s)continue;l[r]-=1,l[r+1]>=0&&l[r]===c&&(l[r+1]+=i,e&&a&&3===a.nodeType&&u&&3===u.nodeType&&(o[h]+=d))}}}if(3===h.nodeType){if(d=h.textContent.length,h.textContent+=p.textContent,n){let e=null;for(let l=0;l<n;l++)if((e=t[l])&&e[r]>c){if(r>0&&e[r-1]!==s)continue;e[r]-=1,e[r+1]>=0&&e[r]===c&&(e[r+1]+=i,o[l]+=d)}}}else h.innerHTML+=p.innerHTML;l.removeItem(p),c--}else 1===h.nodeType&&e(h,r+1,c)}}(e,0,0),o},mergeNestedTags:function(e,t){"string"==typeof t?t=(function(e){return this.test(e.tagName)}).bind(new this._w.RegExp("^("+(t||".+")+")$","i")):"function"!=typeof t&&(t=function(){return!0}),function e(i){let l=i.children;if(1===l.length&&l[0].nodeName===i.nodeName&&t(i)){let e=l[0];for(l=e.children;l[0];)i.appendChild(l[0]);i.removeChild(e)}for(let t=0,l=i.children.length;t<l;t++)e(i.children[t])}(e)},removeEmptyNode:function(e,t,i){let l=this;t&&(t=l.getParentElement(t,function(t){return e===t.parentElement})),function i(n){if(l._notTextNode(n)||n===t||l.isNonEditable(n))return 0;if(n!==e&&l.onlyZeroWidthSpace(n.textContent)&&(!n.firstChild||!l.isBreak(n.firstChild))&&!n.querySelector(l._allowedEmptyNodeList)){if(n.parentNode)return n.parentNode.removeChild(n),-1}else{let e=n.children;for(let t=0,n=e.length,o=0;t<n;t++)!e[t+o]||l.isComponent(e[t+o])||(o+=i(e[t+o]))}return 0}(e),0===e.childNodes.length&&(i?this.removeItem(e):e.innerHTML="<br>")},htmlRemoveWhiteSpace:function(e){return e?e.trim().replace(/<\/?(?!strong|span|font|b|var|i|em|u|ins|s|strike|del|sub|sup|mark|a|label|code|summary)[^>^<]+>\s+(?=<)/ig,function(e){return e.replace(/\n/g,"").replace(/\s+/," ")}):""},htmlCompress:function(e){return e.replace(/\n/g,"").replace(/(>)(?:\s+)(<)/g,"$1$2")},sortByDepth:function(e,t){let i=t?1:-1,l=-1*i;e.sort((function(e,t){return this.isListCell(e)&&this.isListCell(t)?(e=this.getElementDepth(e))>(t=this.getElementDepth(t))?i:e<t?l:0:0}).bind(this))},escapeStringRegexp:function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")},_isExcludeSelectionElement:function(e){return!/FIGCAPTION/i.test(e.nodeName)&&(this.isComponent(e)||/FIGURE/i.test(e.nodeName))},_isIgnoreNodeChange:function(e){return e&&3!==e.nodeType&&(this.isNonEditable(e)||!this.isTextStyleElement(e))},_isMaintainedNode:function(e){return e&&3!==e.nodeType&&/^(a|label|code|summary)$/i.test("string"==typeof e?e:e.nodeName)},_isSizeNode:function(e){return e&&3!==e.nodeType&&this.isTextStyleElement(e)&&!!e.style.fontSize},_notTextNode:function(e){return e&&3!==e.nodeType&&(this.isComponent(e)||/^(br|input|select|canvas|img|iframe|audio|video)$/i.test("string"==typeof e?e:e.nodeName))},_disallowedTags:function(e){return/^(meta|script|link|style|[a-z]+\:[a-z]+)$/i.test(e.nodeName)},createTagsWhitelist:function(e){return RegExp("<\\/?\\b(?!\\b"+(e||"").replace(/\|/g,"\\b|\\b")+"\\b)[^>]*>","gi")},createTagsBlacklist:function(e){return RegExp("<\\/?\\b(?:\\b"+(e||"^").replace(/\|/g,"\\b|\\b")+"\\b)[^>]*>","gi")},_consistencyCheckOfHTML:function(e,t,i,l,n){let o=[],a=[],r=[],s=[],u=this.getListChildNodes(e,(function(u){if(1!==u.nodeType)return this.isList(u.parentElement)&&o.push(u),!1;if(i.test(u.nodeName)||!t.test(u.nodeName)&&0===u.childNodes.length&&this.isNotCheckingNode(u))return o.push(u),!1;let c=!this.getParentElement(u,this.isNotCheckingNode);if(!this.isTable(u)&&!this.isListCell(u)&&!this.isAnchor(u)&&(this.isFormatElement(u)||this.isRangeFormatElement(u)||this.isTextStyleElement(u))&&0===u.childNodes.length&&c)return a.push(u),!1;if(this.isList(u.parentNode)&&!this.isList(u)&&!this.isListCell(u))return r.push(u),!1;if(this.isCell(u)){let e=u.firstElementChild;if(!this.isFormatElement(e)&&!this.isRangeFormatElement(e)&&!this.isComponent(e))return s.push(u),!1}if(c&&u.className){let e=new this._w.Array(u.classList).map(l).join(" ").trim();e?u.className=e:u.removeAttribute("class")}return n&&u.parentNode!==e&&c&&(this.isListCell(u)&&!this.isList(u.parentNode)||(this.isFormatElement(u)||this.isComponent(u))&&!this.isRangeFormatElement(u.parentNode)&&!this.getParentElement(u,this.isComponent))}).bind(this));for(let e=0,t=o.length;e<t;e++)this.removeItem(o[e]);let c=[];for(let e=0,t=u.length,i,l;e<t;e++)if((l=(i=u[e]).parentNode)&&l.parentNode){if(this.getParentElement(i,this.isListCell)){let e=i.childNodes;for(let n=e.length-1;t>=0;n--)l.insertBefore(i,e[n]);c.push(i)}else l.parentNode.insertBefore(i,l),c.push(l)}for(let e=0,t=c.length,i;e<t;e++)i=c[e],this.onlyZeroWidthSpace(i.textContent.trim())&&this.removeItem(i);for(let e=0,t=a.length;e<t;e++)this.removeItem(a[e]);for(let e=0,t=r.length,i,l,n,o;e<t;e++)if(o=(i=r[e]).parentNode){if(l=this.createElement("LI"),this.isFormatElement(i)){for(n=i.childNodes;n[0];)l.appendChild(n[0]);o.insertBefore(l,i),this.removeItem(i)}else i=i.nextSibling,l.appendChild(r[e]),o.insertBefore(l,i)}for(let e=0,t=s.length,i,l;e<t;e++)i=s[e],(l=this.createElement("DIV")).innerHTML=0===i.textContent.trim().length&&0===i.children.length?"<br>":i.innerHTML,i.innerHTML=l.outerHTML},_setDefaultOptionStyle:function(e,t){let i="";e.height&&(i+="height:"+e.height+";"),e.minHeight&&(i+="min-height:"+e.minHeight+";"),e.maxHeight&&(i+="max-height:"+e.maxHeight+";"),e.position&&(i+="position:"+e.position+";"),e.width&&(i+="width:"+e.width+";"),e.minWidth&&(i+="min-width:"+e.minWidth+";"),e.maxWidth&&(i+="max-width:"+e.maxWidth+";");let l="",n="",o="",a=(t=i+t).split(";");for(let t=0,i=a.length,r;t<i;t++)if(r=a[t].trim()){if(/^(min-|max-)?width\s*:/.test(r)||/^(z-index|position)\s*:/.test(r)){l+=r+";";continue}if(/^(min-|max-)?height\s*:/.test(r)){/^height/.test(r)&&"auto"===r.split(":")[1].trim()&&(e.height="auto"),n+=r+";";continue}o+=r+";"}return{top:l,frame:n,editor:o}},_setIframeDocument:function(e,t){e.setAttribute("scrolling","auto"),e.contentDocument.head.innerHTML='<meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">'+this._setIframeCssTags(t),e.contentDocument.body.className=t._editableClass,e.contentDocument.body.setAttribute("contenteditable",!0),e.contentDocument.body.setAttribute("autocorrect","off")},_setIframeCssTags:function(e){let t=e.iframeCSSFileName,i=this._w.RegExp,l="";for(let e=0,n=t.length,o;e<n;e++){if(o=[],/(^https?:\/\/)|(^data:text\/css,)/.test(t[e]))o.push(t[e]);else{let l=new i("(^|.*[\\/])"+t[e]+"(\\..+)?\\.css(?:\\?.*|;.*)?$","i");for(let e=document.getElementsByTagName("link"),t=0,i=e.length,n;t<i;t++)(n=e[t].href.match(l))&&o.push(n[0])}if(!o||0===o.length)throw'[SUNEDITOR.constructor.iframe.fail] The suneditor CSS files installation path could not be automatically detected. Please set the option property "iframeCSSFileName" before creating editor instances.';for(let e=0,t=o.length;e<t;e++)l+='<link href="'+o[e]+'" rel="stylesheet">'}return l+("auto"===e.height?"<style>\n/** Iframe height auto */\nbody{height: min-content; overflow: hidden;}\n</style>":"")}};t.Z=i},61053:function(e,t,i){"use strict";i.r(t),i.d(t,{align:function(){return n},audio:function(){return N},blockquote:function(){return l},default:function(){return V},font:function(){return o},fontColor:function(){return s},fontSize:function(){return a},formatBlock:function(){return p},hiliteColor:function(){return u},horizontalRule:function(){return c},image:function(){return A},imageGallery:function(){return H},lineHeight:function(){return g},link:function(){return x},list:function(){return d},math:function(){return I},paragraphStyle:function(){return f},table:function(){return h},template:function(){return m},textStyle:function(){return b},video:function(){return B}});var l={name:"blockquote",display:"command",add:function(e,t){e.context.blockquote={targetButton:t,tag:e.util.createElement("BLOCKQUOTE")}},active:function(e){if(e){if(/blockquote/i.test(e.nodeName))return this.util.addClass(this.context.blockquote.targetButton,"active"),!0}else this.util.removeClass(this.context.blockquote.targetButton,"active");return!1},action:function(){let e=this.util.getParentElement(this.getSelectionNode(),"blockquote");e?this.detachRangeFormatElement(e,null,null,!1,!1):this.applyRangeFormatElement(this.context.blockquote.tag.cloneNode(!1))}},n={name:"align",display:"submenu",add:function(e,t){let i=e.icons,l=e.context;l.align={targetButton:t,_itemMenu:null,_alignList:null,currentAlign:"",defaultDir:e.options.rtl?"right":"left",icons:{justify:i.align_justify,left:i.align_left,right:i.align_right,center:i.align_center}};let n=this.setSubmenu(e),o=l.align._itemMenu=n.querySelector("ul");o.addEventListener("click",this.pickup.bind(e)),l.align._alignList=o.querySelectorAll("li button"),e.initMenuTarget(this.name,t,n),n=null,o=null},setSubmenu:function(e){let t=e.lang,i=e.icons,l=e.util.createElement("DIV"),n=e.options.alignItems,o="";for(let e=0,l,a;e<n.length;e++)l=n[e],a=t.toolbar["align"+l.charAt(0).toUpperCase()+l.slice(1)],o+='<li><button type="button" class="se-btn-list se-btn-align" data-value="'+l+'" title="'+a+'" aria-label="'+a+'"><span class="se-list-icon">'+i["align_"+l]+"</span>"+a+"</button></li>";return l.className="se-submenu se-list-layer se-list-align",l.innerHTML='<div class="se-list-inner"><ul class="se-list-basic">'+o+"</ul></div>",l},active:function(e){let t=this.context.align,i=t.targetButton,l=i.firstElementChild;if(e){if(this.util.isFormatElement(e)){let n=e.style.textAlign;if(n)return this.util.changeElement(l,t.icons[n]||t.icons[t.defaultDir]),i.setAttribute("data-focus",n),!0}}else this.util.changeElement(l,t.icons[t.defaultDir]),i.removeAttribute("data-focus");return!1},on:function(){let e=this.context.align,t=e._alignList,i=e.targetButton.getAttribute("data-focus")||e.defaultDir;if(i!==e.currentAlign){for(let e=0,l=t.length;e<l;e++)i===t[e].getAttribute("data-value")?this.util.addClass(t[e],"active"):this.util.removeClass(t[e],"active");e.currentAlign=i}},exchangeDir:function(){let e=this.options.rtl?"right":"left";if(!this.context.align||this.context.align.defaultDir===e)return;this.context.align.defaultDir=e;let t=this.context.align._itemMenu,i=t.querySelector('[data-value="left"]'),l=t.querySelector('[data-value="right"]');if(i&&l){let e=i.parentElement,t=l.parentElement;e.appendChild(l),t.appendChild(i)}},pickup:function(e){e.preventDefault(),e.stopPropagation();let t=e.target,i=null;for(;!i&&!/UL/i.test(t.tagName);)i=t.getAttribute("data-value"),t=t.parentNode;if(!i)return;let l=this.context.align.defaultDir,n=this.getSelectedElements();for(let e=0,t=n.length;e<t;e++)this.util.setStyle(n[e],"textAlign",i===l?"":i);this.effectNode=null,this.submenuOff(),this.focus(),this.history.push(!1)}},o={name:"font",display:"submenu",add:function(e,t){let i=e.context;i.font={targetText:t.querySelector(".txt"),targetTooltip:t.parentNode.querySelector(".se-tooltip-text"),_fontList:null,currentFont:""};let l=this.setSubmenu(e);l.querySelector(".se-list-inner").addEventListener("click",this.pickup.bind(e)),i.font._fontList=l.querySelectorAll("ul li button"),e.initMenuTarget(this.name,t,l)},setSubmenu:function(e){let t,i,l,n;let o=e.lang,a=e.util.createElement("DIV");a.className="se-submenu se-list-layer se-list-font-family";let r=e.options.font,s='<div class="se-list-inner"><ul class="se-list-basic"><li><button type="button" class="default_value se-btn-list" title="'+o.toolbar.default+'" aria-label="'+o.toolbar.default+'">('+o.toolbar.default+")</button></li>";for(l=0,n=r.length;l<n;l++)i=(t=r[l]).split(",")[0],s+='<li><button type="button" class="se-btn-list" data-value="'+t+'" data-txt="'+i+'" title="'+i+'" aria-label="'+i+'" style="font-family:'+t+';">'+i+"</button></li>";return s+="</ul></div>",a.innerHTML=s,a},active:function(e){let t=this.context.font.targetText,i=this.context.font.targetTooltip;if(e){if(e.style&&e.style.fontFamily.length>0){let l=e.style.fontFamily.replace(/["']/g,"");return this.util.changeTxt(t,l),this.util.changeTxt(i,this.lang.toolbar.font+" ("+l+")"),!0}}else{let e=this.hasFocus?this.wwComputedStyle.fontFamily:this.lang.toolbar.font;this.util.changeTxt(t,e),this.util.changeTxt(i,this.hasFocus?this.lang.toolbar.font+(e?" ("+e+")":""):e)}return!1},on:function(){let e=this.context.font,t=e._fontList,i=e.targetText.textContent;if(i!==e.currentFont){for(let e=0,l=t.length;e<l;e++)i===(t[e].getAttribute("data-value")||"").replace(/'|"/g,"")?this.util.addClass(t[e],"active"):this.util.removeClass(t[e],"active");e.currentFont=i}},pickup:function(e){if(!/^BUTTON$/i.test(e.target.tagName))return!1;e.preventDefault(),e.stopPropagation();let t=e.target.getAttribute("data-value");if(t){let e=this.util.createElement("SPAN");/[\s\d\W]/.test(t)&&!/^['"].*['"]$/.test(t)&&(t='"'+t+'"'),e.style.fontFamily=t,this.nodeChange(e,["font-family"],null,null)}else this.nodeChange(null,["font-family"],["span"],!0);this.submenuOff()}},a={name:"fontSize",display:"submenu",add:function(e,t){let i=e.context;i.fontSize={targetText:t.querySelector(".txt"),_sizeList:null,currentSize:""};let l=this.setSubmenu(e),n=l.querySelector("ul");n.addEventListener("click",this.pickup.bind(e)),i.fontSize._sizeList=n.querySelectorAll("li button"),e.initMenuTarget(this.name,t,l),l=null,n=null},setSubmenu:function(e){let t=e.options,i=e.lang,l=e.util.createElement("DIV");l.className="se-submenu se-list-layer se-list-font-size";let n=t.fontSize?t.fontSize:[8,9,10,11,12,14,16,18,20,22,24,26,28,36,48,72],o='<div class="se-list-inner"><ul class="se-list-basic"><li><button type="button" class="default_value se-btn-list" title="'+i.toolbar.default+'" aria-label="'+i.toolbar.default+'">('+i.toolbar.default+")</button></li>";for(let e=0,i=t.fontSizeUnit,l=n.length,a;e<l;e++)o+='<li><button type="button" class="se-btn-list" data-value="'+(a=n[e])+i+'" title="'+a+i+'" aria-label="'+a+i+'" style="font-size:'+a+i+';">'+a+"</button></li>";return o+="</ul></div>",l.innerHTML=o,l},active:function(e){if(e){if(e.style&&e.style.fontSize.length>0)return this.util.changeTxt(this.context.fontSize.targetText,this._convertFontSize.call(this,this.options.fontSizeUnit,e.style.fontSize)),!0}else this.util.changeTxt(this.context.fontSize.targetText,this.hasFocus?this._convertFontSize.call(this,this.options.fontSizeUnit,this.wwComputedStyle.fontSize):this.lang.toolbar.fontSize);return!1},on:function(){let e=this.context.fontSize,t=e._sizeList,i=e.targetText.textContent;if(i!==e.currentSize){for(let e=0,l=t.length;e<l;e++)i===t[e].getAttribute("data-value")?this.util.addClass(t[e],"active"):this.util.removeClass(t[e],"active");e.currentSize=i}},pickup:function(e){if(!/^BUTTON$/i.test(e.target.tagName))return!1;e.preventDefault(),e.stopPropagation();let t=e.target.getAttribute("data-value");if(t){let e=this.util.createElement("SPAN");e.style.fontSize=t,this.nodeChange(e,["font-size"],null,null)}else this.nodeChange(null,["font-size"],["span"],!0);this.submenuOff()}},r={name:"colorPicker",add:function(e){let t=e.context;t.colorPicker={colorListHTML:"",_colorInput:"",_defaultColor:"#000",_styleProperty:"color",_currentColor:"",_colorList:[]},t.colorPicker.colorListHTML=this.createColorList(e,this._makeColorList)},createColorList:function(e,t){let i=e.options,l=e.lang,n=i.colorList&&0!==i.colorList.length?i.colorList:["#ff0000","#ff5e00","#ffe400","#abf200","#00d8ff","#0055ff","#6600ff","#ff00dd","#000000","#ffd8d8","#fae0d4","#faf4c0","#e4f7ba","#d4f4fa","#d9e5ff","#e8d9ff","#ffd9fa","#f1f1f1","#ffa7a7","#ffc19e","#faed7d","#cef279","#b2ebf4","#b2ccff","#d1b2ff","#ffb2f5","#bdbdbd","#f15f5f","#f29661","#e5d85c","#bce55c","#5cd1e5","#6699ff","#a366ff","#f261df","#8c8c8c","#980000","#993800","#998a00","#6b9900","#008299","#003399","#3d0099","#990085","#353535","#670000","#662500","#665c00","#476600","#005766","#002266","#290066","#660058","#222222"],o=[],a='<div class="se-list-inner">';for(let e=0,i=n.length,l;e<i;e++)if(l=n[e]){if("string"==typeof l&&(o.push(l),e<i-1))continue;o.length>0&&(a+='<div class="se-selector-color">'+t(o)+"</div>",o=[]),"object"==typeof l&&(a+='<div class="se-selector-color">'+t(l)+"</div>")}return a+('<form class="se-form-group"><input type="text" maxlength="9" class="_se_color_picker_input se-color-input"/><button type="submit" class="se-btn-primary _se_color_picker_submit" title="'+l.dialogBox.submitButton+'" aria-label="'+l.dialogBox.submitButton+'">'+e.icons.checked+'</button><button type="button" class="se-btn _se_color_picker_remove" title="'+l.toolbar.removeFormat+'" aria-label="'+l.toolbar.removeFormat+'">'+e.icons.erase)+"</button></form></div>"},_makeColorList:function(e){let t="";t+='<ul class="se-color-pallet">';for(let i=0,l=e.length,n;i<l;i++)"string"==typeof(n=e[i])&&(t+='<li><button type="button" data-value="'+n+'" title="'+n+'" aria-label="'+n+'" style="background-color:'+n+';"></button></li>');return t+"</ul>"},init:function(e,t){let i=this.plugins.colorPicker,l=t||i.getColorInNode.call(this,e)||this.context.colorPicker._defaultColor;l=i.isHexColor(l)?l:i.rgb2hex(l)||l;let n=this.context.colorPicker._colorList;if(n)for(let e=0,t=n.length;e<t;e++)l.toLowerCase()===n[e].getAttribute("data-value").toLowerCase()?this.util.addClass(n[e],"active"):this.util.removeClass(n[e],"active");i.setInputText.call(this,i.colorName2hex.call(this,l))},setCurrentColor:function(e){this.context.colorPicker._currentColor=e,this.context.colorPicker._colorInput.style.borderColor=e},setInputText:function(e){e=/^#/.test(e)?e:"#"+e,this.context.colorPicker._colorInput.value=e,this.plugins.colorPicker.setCurrentColor.call(this,e)},getColorInNode:function(e){let t="",i=this.context.colorPicker._styleProperty;for(;e&&!this.util.isWysiwygDiv(e)&&0===t.length;)1===e.nodeType&&e.style[i]&&(t=e.style[i]),e=e.parentNode;return t},isHexColor:function(e){return/^#[0-9a-f]{3}(?:[0-9a-f]{3})?$/i.test(e)},rgb2hex:function(e){let t=e.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i);return t&&4===t.length?"#"+("0"+parseInt(t[1],10).toString(16)).slice(-2)+("0"+parseInt(t[2],10).toString(16)).slice(-2)+("0"+parseInt(t[3],10).toString(16)).slice(-2):""},colorName2hex:function(e){if(/^#/.test(e))return e;var t=this.util.createElement("div");t.style.display="none",t.style.color=e;var i=this._w.getComputedStyle(this._d.body.appendChild(t)).color.match(/\d+/g).map(function(e){return parseInt(e,10)});return this.util.removeItem(t),i.length>=3&&"#"+(16777216+(i[0]<<16)+(i[1]<<8)+i[2]).toString(16).substr(1)}},s={name:"fontColor",display:"submenu",add:function(e,t){e.addModule([r]);let i=e.context;i.fontColor={previewEl:null,colorInput:null,colorList:null};let l=this.setSubmenu(e);i.fontColor.colorInput=l.querySelector("._se_color_picker_input"),i.fontColor.colorInput.addEventListener("keyup",this.onChangeInput.bind(e)),l.querySelector("._se_color_picker_submit").addEventListener("click",this.submit.bind(e)),l.querySelector("._se_color_picker_remove").addEventListener("click",this.remove.bind(e)),l.addEventListener("click",this.pickup.bind(e)),i.fontColor.colorList=l.querySelectorAll("li button"),e.initMenuTarget(this.name,t,l)},setSubmenu:function(e){let t=e.context.colorPicker.colorListHTML,i=e.util.createElement("DIV");return i.className="se-submenu se-list-layer",i.innerHTML=t,i},on:function(){let e=this.context.colorPicker,t=this.context.fontColor;e._colorInput=t.colorInput;let i=this.wwComputedStyle.color;e._defaultColor=i?this.plugins.colorPicker.isHexColor(i)?i:this.plugins.colorPicker.rgb2hex(i):"#333333",e._styleProperty="color",e._colorList=t.colorList,this.plugins.colorPicker.init.call(this,this.getSelectionNode(),null)},onChangeInput:function(e){this.plugins.colorPicker.setCurrentColor.call(this,e.target.value)},submit:function(){this.plugins.fontColor.applyColor.call(this,this.context.colorPicker._currentColor)},pickup:function(e){e.preventDefault(),e.stopPropagation(),this.plugins.fontColor.applyColor.call(this,e.target.getAttribute("data-value"))},remove:function(){this.nodeChange(null,["color"],["span"],!0),this.submenuOff()},applyColor:function(e){if(!e)return;let t=this.util.createElement("SPAN");t.style.color=e,this.nodeChange(t,["color"],null,null),this.submenuOff()}},u={name:"hiliteColor",display:"submenu",add:function(e,t){e.addModule([r]);let i=e.context;i.hiliteColor={previewEl:null,colorInput:null,colorList:null};let l=this.setSubmenu(e);i.hiliteColor.colorInput=l.querySelector("._se_color_picker_input"),i.hiliteColor.colorInput.addEventListener("keyup",this.onChangeInput.bind(e)),l.querySelector("._se_color_picker_submit").addEventListener("click",this.submit.bind(e)),l.querySelector("._se_color_picker_remove").addEventListener("click",this.remove.bind(e)),l.addEventListener("click",this.pickup.bind(e)),i.hiliteColor.colorList=l.querySelectorAll("li button"),e.initMenuTarget(this.name,t,l)},setSubmenu:function(e){let t=e.context.colorPicker.colorListHTML,i=e.util.createElement("DIV");return i.className="se-submenu se-list-layer",i.innerHTML=t,i},on:function(){let e=this.context.colorPicker,t=this.context.hiliteColor;e._colorInput=t.colorInput;let i=this.wwComputedStyle.backgroundColor;e._defaultColor=i?this.plugins.colorPicker.isHexColor(i)?i:this.plugins.colorPicker.rgb2hex(i):"#ffffff",e._styleProperty="backgroundColor",e._colorList=t.colorList,this.plugins.colorPicker.init.call(this,this.getSelectionNode(),null)},onChangeInput:function(e){this.plugins.colorPicker.setCurrentColor.call(this,e.target.value)},submit:function(){this.plugins.hiliteColor.applyColor.call(this,this.context.colorPicker._currentColor)},pickup:function(e){e.preventDefault(),e.stopPropagation(),this.plugins.hiliteColor.applyColor.call(this,e.target.getAttribute("data-value"))},remove:function(){this.nodeChange(null,["background-color"],["span"],!0),this.submenuOff()},applyColor:function(e){if(!e)return;let t=this.util.createElement("SPAN");t.style.backgroundColor=e,this.nodeChange(t,["background-color"],null,null),this.submenuOff()}},c={name:"horizontalRule",display:"submenu",add:function(e,t){e.context.horizontalRule={currentHR:null};let i=this.setSubmenu(e);i.querySelector("ul").addEventListener("click",this.horizontalRulePick.bind(e)),e.initMenuTarget(this.name,t,i)},setSubmenu:function(e){let t=e.lang,i=e.util.createElement("DIV"),l=e.options.hrItems||[{name:t.toolbar.hr_solid,class:"__se__solid"},{name:t.toolbar.hr_dashed,class:"__se__dashed"},{name:t.toolbar.hr_dotted,class:"__se__dotted"}],n="";for(let e=0,t=l.length;e<t;e++)n+='<li><button type="button" class="se-btn-list btn_line" data-command="horizontalRule" data-value="'+l[e].class+'" title="'+l[e].name+'" aria-label="'+l[e].name+'"><hr'+(l[e].class?' class="'+l[e].class+'"':"")+(l[e].style?' style="'+l[e].style+'"':"")+"/></button></li>";return i.className="se-submenu se-list-layer se-list-line",i.innerHTML='<div class="se-list-inner"><ul class="se-list-basic">'+n+"</ul></div>",i},active:function(e){if(e){if(/HR/i.test(e.nodeName))return this.context.horizontalRule.currentHR=e,this.util.hasClass(e,"on")||(this.util.addClass(e,"on"),this.controllersOn("hr",this.util.removeClass.bind(this.util,e,"on"))),!0}else this.util.hasClass(this.context.horizontalRule.currentHR,"on")&&this.controllersOff();return!1},appendHr:function(e){return this.focus(),this.insertComponent(e.cloneNode(!1),!1,!0,!1)},horizontalRulePick:function(e){e.preventDefault(),e.stopPropagation();let t=e.target,i=t.getAttribute("data-command");for(;!i&&!/UL/i.test(t.tagName);)i=(t=t.parentNode).getAttribute("data-command");if(!i)return;let l=this.plugins.horizontalRule.appendHr.call(this,t.firstElementChild);l&&(this.setRange(l,0,l,0),this.submenuOff())}},d={name:"list",display:"submenu",add:function(e,t){let i=e.context;i.list={targetButton:t,_list:null,currentList:"",icons:{bullets:e.icons.list_bullets,number:e.icons.list_number}};let l=this.setSubmenu(e),n=l.querySelector("ul");n.addEventListener("click",this.pickup.bind(e)),i.list._list=n.querySelectorAll("li button"),e.initMenuTarget(this.name,t,l),l=null,n=null},setSubmenu:function(e){let t=e.lang,i=e.util.createElement("DIV");return i.className="se-submenu se-list-layer",i.innerHTML='<div class="se-list-inner"><ul class="se-list-basic"><li><button type="button" class="se-btn-list se-tooltip" data-command="OL" title="'+t.toolbar.orderList+'" aria-label="'+t.toolbar.orderList+'">'+e.icons.list_number+'</button></li><li><button type="button" class="se-btn-list se-tooltip" data-command="UL" title="'+t.toolbar.unorderList+'" aria-label="'+t.toolbar.unorderList+'">'+e.icons.list_bullets+"</button></li></ul></div>",i},active:function(e){let t=this.context.list.targetButton,i=t.firstElementChild,l=this.util;if(l.isList(e)){let n=e.nodeName;return t.setAttribute("data-focus",n),l.addClass(t,"active"),/UL/i.test(n)?l.changeElement(i,this.context.list.icons.bullets):l.changeElement(i,this.context.list.icons.number),!0}return t.removeAttribute("data-focus"),l.changeElement(i,this.context.list.icons.number),l.removeClass(t,"active"),!1},on:function(){let e=this.context.list,t=e._list,i=e.targetButton.getAttribute("data-focus")||"";if(i!==e.currentList){for(let e=0,l=t.length;e<l;e++)i===t[e].getAttribute("data-command")?this.util.addClass(t[e],"active"):this.util.removeClass(t[e],"active");e.currentList=i}},editList:function(e,t,i){let l=this.getRange(),n=t||this.getSelectedElementsAndComponents(!1);if(0===n.length&&(t||(l=this.getRange_addLine(l,null),0===(n=this.getSelectedElementsAndComponents(!1)).length)))return;let o=this.util;o.sortByDepth(n,!0);let a=n[0],r=n[n.length-1],s=(o.isListCell(a)||o.isComponent(a))&&!a.previousElementSibling?a.parentNode.previousElementSibling:a.previousElementSibling,u=(o.isListCell(r)||o.isComponent(r))&&!r.nextElementSibling?r.parentNode.nextElementSibling:r.nextElementSibling,c=l.collapsed,d={sc:l.startContainer,so:l.startContainer===l.endContainer&&o.onlyZeroWidthSpace(l.startContainer)&&0===l.startOffset&&1===l.endOffset?l.endOffset:l.startOffset,ec:l.endContainer,eo:l.endOffset},h=null,p=!0;for(let e=0,t=n.length;e<t;e++)if(!o.isList(o.getRangeFormatElement(n[e],(function(t){return this.getRangeFormatElement(t)&&t!==n[e]}).bind(o)))){p=!1;break}if(!p||s&&a.tagName===s.tagName&&e===s.tagName.toUpperCase()||u&&r.tagName===u.tagName&&e===u.tagName.toUpperCase()){let t=s?s.parentNode:s,i=u?u.parentNode:u;s=t&&!o.isWysiwygDiv(t)&&t.nodeName===e?t:s,u=i&&!o.isWysiwygDiv(i)&&i.nodeName===e?i:u;let l=s&&s.tagName===e,a=u&&u.tagName===e,r=l?s:o.createElement(e),c=null,h=null,p=null,g=(function(e){return!this.isComponent(e)&&!this.isList(e)}).bind(o);for(let t=0,i=n.length,a,s,u,p,m,f,b,v,_;t<i;t++){if(0===(s=n[t]).childNodes.length&&!o._isIgnoreNodeChange(s)){o.removeItem(s);continue}if(p=n[t+1],m=s.parentNode,f=p?p.parentNode:null,u=o.isListCell(s),_=o.isRangeFormatElement(m)?m:null,b=u&&!o.isWysiwygDiv(m)?m.parentNode:m,v=u&&!o.isWysiwygDiv(m)?!p||o.isListCell(b)?m:m.nextSibling:s.nextSibling,a=o.createElement("LI"),o.copyFormatAttributes(a,s),0===t&&d.sc===s&&(d.sc=a),t===i-1&&d.ec===s&&(d.ec=a),o.isComponent(s)){let e=/^HR$/i.test(s.nodeName);e||(a.innerHTML="<br>"),a.innerHTML+=s.outerHTML,e&&(a.innerHTML+="<br>")}else{let e=s.childNodes;for(;e[0];)a.appendChild(e[0])}r.appendChild(a),!(!p||b!==f||o.isRangeFormatElement(v))||(c||(c=r),l&&p&&b===f||p&&o.isList(f)&&f===m||r.parentNode===b||b.insertBefore(r,v)),o.removeItem(s),l&&null===h&&(h=r.children.length-1),p&&(o.getRangeFormatElement(f,g)!==o.getRangeFormatElement(m,g)||o.isList(f)&&o.isList(m)&&o.getElementDepth(f)!==o.getElementDepth(m))&&(r=o.createElement(e)),_&&0===_.children.length&&o.removeItem(_)}h&&(c=c.children[h]),a&&(p=r.children.length-1,r.innerHTML+=u.innerHTML,r.children[p],o.removeItem(u))}else{let t,l;if(i){for(let e=0,t=n.length;e<t;e++)for(let i=e-1;i>=0;i--)if(n[i].contains(n[e])){n.splice(e,1),e--,t--;break}}let r=o.getRangeFormatElement(a),s=r&&r.tagName===e,u=(function(e){return!this.isComponent(e)}).bind(o);s||(l=o.createElement(e));for(let a=0,r=n.length,c,d;a<r;a++)(d=o.getRangeFormatElement(n[a],u))&&o.isList(d)&&(c?c!==d?(i&&o.isListCell(d.parentNode)?this.plugins.list._detachNested.call(this,t.f):h=this.detachRangeFormatElement(t.f[0].parentNode,t.f,l,!1,!0),d=n[a].parentNode,s||(l=o.createElement(e)),t={r:c=d,f:[o.getParentElement(n[a],"LI")]}):t.f.push(o.getParentElement(n[a],"LI")):t={r:c=d,f:[o.getParentElement(n[a],"LI")]},a===r-1&&(i&&o.isListCell(d.parentNode)?this.plugins.list._detachNested.call(this,t.f):h=this.detachRangeFormatElement(t.f[0].parentNode,t.f,l,!1,!0)))}return this.effectNode=null,c&&h||d},_detachNested:function(e){let t=e[0],i=e[e.length-1],l=i.nextElementSibling,n=t.parentNode,o=n.parentNode.nextElementSibling,a=n.parentNode.parentNode;for(let t=0,i=e.length;t<i;t++)a.insertBefore(e[t],o);if(l&&n.children.length>0){let e=n.cloneNode(!1),t=n.childNodes,o=this.util.getPositionIndex(l);for(;t[o];)e.appendChild(t[o]);i.appendChild(e)}0===n.children.length&&this.util.removeItem(n),this.util.mergeSameTags(a);let r=this.util.getEdgeChildNodes(t,i);return{cc:t.parentNode,sc:r.sc,ec:r.ec}},editInsideList:function(e,t){let i=(t=t||this.getSelectedElements().filter((function(e){return this.isListCell(e)}).bind(this.util))).length;if(0===i||!e&&!this.util.isListCell(t[0].previousElementSibling)&&!this.util.isListCell(t[i-1].nextElementSibling))return{sc:t[0],so:0,ec:t[i-1],eo:1};let l=t[0].parentNode,n=t[i-1],o=null;if(e){if(l!==n.parentNode&&this.util.isList(n.parentNode.parentNode)&&n.nextElementSibling)for(n=n.nextElementSibling;n;)t.push(n),n=n.nextElementSibling;o=this.plugins.list.editList.call(this,l.nodeName.toUpperCase(),t,!0)}else{let e=this.util.createElement(l.nodeName),a=t[0].previousElementSibling,r=n.nextElementSibling,s={s:null,e:null,sl:l,el:l};for(let n=0,o;n<i;n++)(o=t[n]).parentNode!==l&&(this.plugins.list._insiedList.call(this,l,e,a,r,s),l=o.parentNode,e=this.util.createElement(l.nodeName)),a=o.previousElementSibling,r=o.nextElementSibling,e.appendChild(o);this.plugins.list._insiedList.call(this,l,e,a,r,s);let u=this.util.getNodeFromPath(s.s,s.sl),c=this.util.getNodeFromPath(s.e,s.el);o={sc:u,so:0,ec:c,eo:c.textContent.length}}return o},_insiedList:function(e,t,i,l,n){let o=!1;if(i&&t.tagName===i.tagName){let e=t.children;for(;e[0];)i.appendChild(e[0]);t=i,o=!0}if(l&&t.tagName===l.tagName){let e=l.children;for(;e[0];)t.appendChild(e[0]);let i=l.nextElementSibling;l.parentNode.removeChild(l),l=i}if(!o){this.util.isListCell(i)&&(e=i,l=null),e.insertBefore(t,l),n.s||(n.s=this.util.getNodePath(t.firstElementChild.firstChild,e,null),n.sl=e);let o=e.contains(n.sl)?this.util.getNodePath(n.sl,e):null;n.e=this.util.getNodePath(t.lastElementChild.firstChild,e,null),n.el=e,this.util.mergeSameTags(e,[n.s,n.e,o],!1),this.util.mergeNestedTags(e),o&&(n.sl=this.util.getNodeFromPath(o,e))}return t},pickup:function(e){e.preventDefault(),e.stopPropagation();let t=e.target,i="";for(;!i&&!/^UL$/i.test(t.tagName);)i=t.getAttribute("data-command"),t=t.parentNode;if(!i)return;let l=this.plugins.list.editList.call(this,i,null,!1);l&&this.setRange(l.sc,l.so,l.ec,l.eo),this.submenuOff(),this.history.push(!1)}},h={name:"table",display:"submenu",add:function(e,t){let i=e.context,l=i.table={_element:null,_tdElement:null,_trElement:null,_trElements:null,_tableXY:[],_maxWidth:!0,_fixedColumn:!1,_rtl:e.options.rtl,cellControllerTop:"top"===e.options.tableCellControllerPosition,resizeText:null,headerButton:null,mergeButton:null,splitButton:null,splitMenu:null,maxText:e.lang.controller.maxSize,minText:e.lang.controller.minSize,_physical_cellCnt:0,_logical_cellCnt:0,_rowCnt:0,_rowIndex:0,_physical_cellIndex:0,_logical_cellIndex:0,_current_colSpan:0,_current_rowSpan:0,icons:{expansion:e.icons.expansion,reduction:e.icons.reduction}},n=this.setSubmenu(e),o=n.querySelector(".se-controller-table-picker");l.tableHighlight=n.querySelector(".se-table-size-highlighted"),l.tableUnHighlight=n.querySelector(".se-table-size-unhighlighted"),l.tableDisplay=n.querySelector(".se-table-size-display"),e.options.rtl&&(l.tableHighlight.style.left="167px");let a=this.setController_table(e);l.tableController=a,l.resizeButton=a.querySelector("._se_table_resize"),l.resizeText=a.querySelector("._se_table_resize > span > span"),l.columnFixedButton=a.querySelector("._se_table_fixed_column"),l.headerButton=a.querySelector("._se_table_header");let r=this.setController_tableEditor(e,l.cellControllerTop);l.resizeDiv=r,l.splitMenu=r.querySelector(".se-btn-group-sub"),l.mergeButton=r.querySelector("._se_table_merge_button"),l.splitButton=r.querySelector("._se_table_split_button"),l.insertRowAboveButton=r.querySelector("._se_table_insert_row_a"),l.insertRowBelowButton=r.querySelector("._se_table_insert_row_b"),o.addEventListener("mousemove",this.onMouseMove_tablePicker.bind(e,l)),o.addEventListener("click",this.appendTable.bind(e)),r.addEventListener("click",this.onClick_tableController.bind(e)),a.addEventListener("click",this.onClick_tableController.bind(e)),e.initMenuTarget(this.name,t,n),i.element.relative.appendChild(r),i.element.relative.appendChild(a),n=null,o=null,r=null,a=null,l=null},setSubmenu:function(e){let t=e.util.createElement("DIV");return t.className="se-submenu se-selector-table",t.innerHTML='<div class="se-table-size"><div class="se-table-size-picker se-controller-table-picker"></div><div class="se-table-size-highlighted"></div><div class="se-table-size-unhighlighted"></div></div><div class="se-table-size-display">1 x 1</div>',t},setController_table:function(e){let t=e.lang,i=e.icons,l=e.util.createElement("DIV");return l.className="se-controller se-controller-table",l.innerHTML='<div><div class="se-btn-group"><button type="button" data-command="resize" class="se-btn se-tooltip _se_table_resize">'+i.expansion+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.maxSize+'</span></span></button><button type="button" data-command="layout" class="se-btn se-tooltip _se_table_fixed_column">'+i.fixed_column_width+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.fixedColumnWidth+'</span></span></button><button type="button" data-command="header" class="se-btn se-tooltip _se_table_header">'+i.table_header+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.tableHeader+'</span></span></button><button type="button" data-command="remove" class="se-btn se-tooltip">'+i.delete+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.remove+"</span></span></button></div></div>",l},setController_tableEditor:function(e,t){let i=e.lang,l=e.icons,n=e.util.createElement("DIV");return n.className="se-controller se-controller-table-cell",n.innerHTML=(t?"":'<div class="se-arrow se-arrow-up"></div>')+'<div class="se-btn-group"><button type="button" data-command="insert" data-value="row" data-option="up" class="se-btn se-tooltip _se_table_insert_row_a">'+l.insert_row_above+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.insertRowAbove+'</span></span></button><button type="button" data-command="insert" data-value="row" data-option="down" class="se-btn se-tooltip _se_table_insert_row_b">'+l.insert_row_below+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.insertRowBelow+'</span></span></button><button type="button" data-command="delete" data-value="row" class="se-btn se-tooltip">'+l.delete_row+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.deleteRow+'</span></span></button><button type="button" data-command="merge" class="_se_table_merge_button se-btn se-tooltip" disabled>'+l.merge_cell+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.mergeCells+'</span></span></button></div><div class="se-btn-group" style="padding-top: 0;"><button type="button" data-command="insert" data-value="cell" data-option="left" class="se-btn se-tooltip">'+l.insert_column_left+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.insertColumnBefore+'</span></span></button><button type="button" data-command="insert" data-value="cell" data-option="right" class="se-btn se-tooltip">'+l.insert_column_right+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.insertColumnAfter+'</span></span></button><button type="button" data-command="delete" data-value="cell" class="se-btn se-tooltip">'+l.delete_column+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.deleteColumn+'</span></span></button><button type="button" data-command="onsplit" class="_se_table_split_button se-btn se-tooltip">'+l.split_cell+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.splitCells+'</span></span></button><div class="se-btn-group-sub sun-editor-common se-list-layer se-table-split"><div class="se-list-inner"><ul class="se-list-basic"><li class="se-btn-list" data-command="split" data-value="vertical" style="line-height:32px;" title="'+i.controller.VerticalSplit+'" aria-label="'+i.controller.VerticalSplit+'">'+i.controller.VerticalSplit+'</li><li class="se-btn-list" data-command="split" data-value="horizontal" style="line-height:32px;" title="'+i.controller.HorizontalSplit+'" aria-label="'+i.controller.HorizontalSplit+'">'+i.controller.HorizontalSplit+"</li></ul></div></div></div>",n},appendTable:function(){let e=this.util.createElement("TABLE"),t=this.plugins.table.createCells,i=this.context.table._tableXY[0],l=this.context.table._tableXY[1],n="<tbody>";for(;l>0;)n+="<tr>"+t.call(this,"td",i)+"</tr>",--l;if(n+="</tbody>",e.innerHTML=n,this.insertComponent(e,!1,!0,!1)){let t=e.querySelector("td div");this.setRange(t,0,t,0),this.plugins.table.reset_table_picker.call(this)}},createCells:function(e,t,i){if(e=e.toLowerCase(),i){let t=this.util.createElement(e);return t.innerHTML="<div><br></div>",t}{let i="";for(;t>0;)i+="<"+e+"><div><br></div></"+e+">",t--;return i}},onMouseMove_tablePicker:function(e,t){t.stopPropagation();let i=this._w.Math.ceil(t.offsetX/18),l=this._w.Math.ceil(t.offsetY/18);i=i<1?1:i,l=l<1?1:l,e._rtl&&(e.tableHighlight.style.left=18*i-13+"px",i=11-i),e.tableHighlight.style.width=i+"em",e.tableHighlight.style.height=l+"em",this.util.changeTxt(e.tableDisplay,i+" x "+l),e._tableXY=[i,l]},reset_table_picker:function(){if(!this.context.table.tableHighlight)return;let e=this.context.table.tableHighlight.style,t=this.context.table.tableUnHighlight.style;e.width="1em",e.height="1em",t.width="10em",t.height="10em",this.util.changeTxt(this.context.table.tableDisplay,"1 x 1"),this.submenuOff()},init:function(){let e=this.context.table,t=this.plugins.table;if(t._removeEvents.call(this),t._selectedTable){let e=t._selectedTable.querySelectorAll(".se-table-selected-cell");for(let t=0,i=e.length;t<i;t++)this.util.removeClass(e[t],"se-table-selected-cell")}t._toggleEditor.call(this,!0),e._element=null,e._tdElement=null,e._trElement=null,e._trElements=null,e._tableXY=[],e._maxWidth=!0,e._fixedColumn=!1,e._physical_cellCnt=0,e._logical_cellCnt=0,e._rowCnt=0,e._rowIndex=0,e._physical_cellIndex=0,e._logical_cellIndex=0,e._current_colSpan=0,e._current_rowSpan=0,t._shift=!1,t._selectedCells=null,t._selectedTable=null,t._ref=null,t._fixedCell=null,t._selectedCell=null,t._fixedCellName=null},call_controller_tableEdit:function(e){let t=this.plugins.table,i=this.context.table;if(!this.getSelection().isCollapsed&&!t._selectedCell){this.controllersOff(),this.util.removeClass(e,"se-table-selected-cell");return}let l=i._element||this.plugins.table._selectedTable||this.util.getParentElement(e,"TABLE");i._maxWidth=this.util.hasClass(l,"se-table-size-100")||"100%"===l.style.width||!l.style.width&&!this.util.hasClass(l,"se-table-size-auto"),i._fixedColumn=this.util.hasClass(l,"se-table-layout-fixed")||"fixed"===l.style.tableLayout,t.setTableStyle.call(this,i._maxWidth?"width|column":"width"),t.setPositionControllerTop.call(this,l),t.setPositionControllerDiv.call(this,e,t._shift),t._shift||this.controllersOn(i.resizeDiv,i.tableController,t.init.bind(this),e,"table")},setPositionControllerTop:function(e){this.setControllerPosition(this.context.table.tableController,e,"top",{left:0,top:0})},setPositionControllerDiv:function(e,t){let i=this.context.table,l=i.resizeDiv;this.plugins.table.setCellInfo.call(this,e,t),i.cellControllerTop?this.setControllerPosition(l,i._element,"top",{left:i.tableController.offsetWidth,top:0}):this.setControllerPosition(l,e,"bottom",{left:0,top:0})},setCellInfo:function(e,t){let i=this.context.table,l=i._element=this.plugins.table._selectedTable||this.util.getParentElement(e,"TABLE");if(/THEAD/i.test(l.firstElementChild.nodeName)?this.util.addClass(i.headerButton,"active"):this.util.removeClass(i.headerButton,"active"),t||0===i._physical_cellCnt){i._tdElement!==e&&(i._tdElement=e,i._trElement=e.parentNode);let t=i._trElements=l.rows,n=e.cellIndex,o=0;for(let e=0,i=t[0].cells,l=t[0].cells.length;e<l;e++)o+=i[e].colSpan;let a=i._rowIndex=i._trElement.rowIndex;i._rowCnt=t.length,i._physical_cellCnt=i._trElement.cells.length,i._logical_cellCnt=o,i._physical_cellIndex=n,i._current_colSpan=i._tdElement.colSpan-1,i._current_rowSpan,i._trElement.cells[n].rowSpan;let r=[],s=[];for(let e=0,l,o;e<=a;e++){l=t[e].cells,o=0;for(let t=0,u=l.length,c,d,h,p;t<u;t++){if(d=(c=l[t]).colSpan-1,h=c.rowSpan-1,p=t+o,s.length>0)for(let i=0,l;i<s.length;i++)!((l=s[i]).row>e)&&(p>=l.index?(o+=l.cs,p+=l.cs,l.rs-=1,l.row=e+1,l.rs<1&&(s.splice(i,1),i--)):t===u-1&&(l.rs-=1,l.row=e+1,l.rs<1&&(s.splice(i,1),i--)));if(e===a&&t===n){i._logical_cellIndex=p;break}h>0&&r.push({index:p,cs:d+1,rs:h,row:-1}),o+=d}s=s.concat(r).sort(function(e,t){return e.index-t.index}),r=[]}r=null,s=null}},editTable:function(e,t){let i=this.plugins.table,l=this.context.table,n=l._element,o="row"===e;if(o){let e=l._trElement.parentNode;if(/^THEAD$/i.test(e.nodeName)){if("up"===t)return;if(!e.nextElementSibling||!/^TBODY$/i.test(e.nextElementSibling.nodeName)){n.innerHTML+="<tbody><tr>"+i.createCells.call(this,"td",l._logical_cellCnt,!1)+"</tr></tbody>";return}}}if(i._ref){let e=l._tdElement,n=i._selectedCells;if(o){if(t)i.setCellInfo.call(this,"up"===t?n[0]:n[n.length-1],!0),i.editRow.call(this,t,e);else{let e=n[0].parentNode,l=[n[0]];for(let t=1,i=n.length,o;t<i;t++)e!==(o=n[t]).parentNode&&(l.push(o),e=o.parentNode);for(let e=0,n=l.length;e<n;e++)i.setCellInfo.call(this,l[e],!0),i.editRow.call(this,t)}}else{let l=n[0].parentNode;if(t){let o=null;for(let e=0,t=n.length-1;e<t;e++)if(l!==n[e+1].parentNode){o=n[e];break}i.setCellInfo.call(this,"left"===t?n[0]:o||n[0],!0),i.editCell.call(this,t,e)}else{let e=[n[0]];for(let t=1,i=n.length,o;t<i;t++)if(l===(o=n[t]).parentNode)e.push(o);else break;for(let l=0,n=e.length;l<n;l++)i.setCellInfo.call(this,e[l],!0),i.editCell.call(this,t)}}t||i.init.call(this)}else i[o?"editRow":"editCell"].call(this,t);if(!t){let e=n.children;for(let t=0;t<e.length;t++)0===e[t].children.length&&(this.util.removeItem(e[t]),t--);0===n.children.length&&this.util.removeItem(n)}},editRow:function(e,t){let i=this.context.table,l=!e,n=i._rowIndex,o=l||"up"===e?n:n+i._current_rowSpan+1,a=l?-1:1,r=i._trElements,s=i._logical_cellCnt;for(let e=0,t=n+(l?-1:0),i;e<=t;e++){if(0===(i=r[e].cells).length)return;for(let t=0,l=i.length,n,r;t<l;t++)n=i[t].rowSpan,r=i[t].colSpan,(!(n<2)||!(r<2))&&n+e>o&&o>e&&(i[t].rowSpan=n+a,s-=r)}if(l){let e=r[n+1];if(e){let t=[],i=r[n].cells,l=0;for(let e=0,n=i.length,o,a;e<n;e++)o=i[e],a=e+l,l+=o.colSpan-1,o.rowSpan>1&&(o.rowSpan-=1,t.push({cell:o.cloneNode(!1),index:a}));if(t.length>0){let n=t.shift();i=e.cells,l=0;for(let o=0,a=i.length,r,s;o<a&&(r=i[o],s=o+l,l+=r.colSpan-1,!(s>=n.index)||(o--,l--,l+=n.cell.colSpan-1,e.insertBefore(n.cell,r),n=t.shift()));o++);if(n){e.appendChild(n.cell);for(let i=0,l=t.length;i<l;i++)e.appendChild(t[i].cell)}}}i._element.deleteRow(o)}else i._element.insertRow(o).innerHTML=this.plugins.table.createCells.call(this,"td",s,!1);l?this.controllersOff():this.plugins.table.setPositionControllerDiv.call(this,t||i._tdElement,!0)},editCell:function(e,t){let i=this.context.table,l=this.util,n=!e,o=i._current_colSpan,a=n||"left"===e?i._logical_cellIndex:i._logical_cellIndex+o+1,r=i._trElements,s=[],u=[],c=0,d=[],h=[];for(let e=0,t=i._rowCnt,p,g,m,f,b,v;e<t;e++){p=r[e],g=a,b=!1,m=p.cells,v=0;for(let t=0,i,r=m.length,p,f,_;t<r&&(i=m[t]);t++)if(p=i.rowSpan-1,f=i.colSpan-1,n){if(_=t+v,u.length>0){let i=!m[t+1];for(let l=0,n;l<u.length;l++)!((n=u[l]).row>e)&&(_>=n.index?(v+=n.cs,_=t+v,n.rs-=1,n.row=e+1,n.rs<1&&(u.splice(l,1),l--)):i&&(n.rs-=1,n.row=e+1,n.rs<1&&(u.splice(l,1),l--)))}p>0&&s.push({rs:p,cs:f+1,index:_,row:-1}),_>=g&&_+f<=g+o?d.push(i):_<=g+o&&_+f>=g?i.colSpan-=l.getOverlapRangeAtIndex(a,a+o,_,_+f):p>0&&(_<g||_+f>g+o)&&h.push({cell:i,i:e,rs:e+p}),v+=f}else{if(t>=g)break;if(f>0){if(c<1&&f+t>=g){i.colSpan+=1,g=null,c=p+1;break}g-=f}if(!b){for(let e=0,t;e<u.length;e++)g-=(t=u[e]).cs,t.rs-=1,t.rs<1&&(u.splice(e,1),e--);b=!0}}if(u=u.concat(s).sort(function(e,t){return e.index-t.index}),s=[],!n){if(c>0){c-=1;continue}null!==g&&m.length>0&&(f=this.plugins.table.createCells.call(this,m[0].nodeName,0,!0),f=p.insertBefore(f,m[g]))}}if(n){let e,t;for(let i=0,n=d.length,o;i<n;i++)o=d[i].parentNode,l.removeItem(d[i]),0===o.cells.length&&(e||(e=l.getArrayIndex(r,o)),t=l.getArrayIndex(r,o),l.removeItem(o));for(let i=0,n=h.length,o;i<n;i++)(o=h[i]).cell.rowSpan=l.getOverlapRangeAtIndex(e,t,o.i,o.rs);this.controllersOff()}else this.plugins.table.setPositionControllerDiv.call(this,t||i._tdElement,!0)},_closeSplitMenu:null,openSplitMenu:function(){this.util.addClass(this.context.table.splitButton,"on"),this.context.table.splitMenu.style.display="inline-table",this.plugins.table._closeSplitMenu=(function(){this.util.removeClass(this.context.table.splitButton,"on"),this.context.table.splitMenu.style.display="none",this.removeDocEvent("click",this.plugins.table._closeSplitMenu),this.plugins.table._closeSplitMenu=null}).bind(this),this.addDocEvent("click",this.plugins.table._closeSplitMenu)},splitCells:function(e){let t=this.util,i=this.context.table,l=i._tdElement,n=i._trElements,o=i._trElement,a=i._logical_cellIndex,r=i._rowIndex,s=this.plugins.table.createCells.call(this,l.nodeName,0,!0);if("vertical"===e){let e=l.colSpan;if(s.rowSpan=l.rowSpan,e>1)s.colSpan=this._w.Math.floor(e/2),l.colSpan=e-s.colSpan,o.insertBefore(s,l.nextElementSibling);else{let t=[],r=[];for(let o=0,s=i._rowCnt,u,c;o<s;o++){u=n[o].cells,c=0;for(let i=0,n=u.length,s,d,h,p;i<n;i++){if(d=(s=u[i]).colSpan-1,h=s.rowSpan-1,p=i+c,r.length>0)for(let e=0,t;e<r.length;e++)!((t=r[e]).row>o)&&(p>=t.index?(c+=t.cs,p+=t.cs,t.rs-=1,t.row=o+1,t.rs<1&&(r.splice(e,1),e--)):i===n-1&&(t.rs-=1,t.row=o+1,t.rs<1&&(r.splice(e,1),e--)));if(p<=a&&h>0&&t.push({index:p,cs:d+1,rs:h,row:-1}),s!==l&&p<=a&&p+d>=a+e-1){s.colSpan+=1;break}if(p>a)break;c+=d}r=r.concat(t).sort(function(e,t){return e.index-t.index}),t=[]}o.insertBefore(s,l.nextElementSibling)}}else{let e=l.rowSpan;if(s.colSpan=l.colSpan,e>1){s.rowSpan=this._w.Math.floor(e/2);let i=e-s.rowSpan,r=[],u=t.getArrayIndex(n,o)+i;for(let e=0,t,i;e<u;e++){t=n[e].cells,i=0;for(let l=0,n=t.length,o,s,c;l<n&&!((c=l+i)>=a);l++)(s=(o=t[l]).rowSpan-1)>0&&s+e>=u&&c<a&&r.push({index:c,cs:o.colSpan}),i+=o.colSpan-1}let c=n[u],d=c.cells,h=r.shift();for(let e=0,t=d.length,i=0,l,n,o;e<t;e++){if(o=e+i+(n=(l=d[e]).colSpan-1)+1,h&&o>=h.index&&(i+=h.cs,o+=h.cs,h=r.shift()),o>=a||e===t-1){c.insertBefore(s,l.nextElementSibling);break}i+=n}l.rowSpan=i}else{s.rowSpan=l.rowSpan;let e=t.createElement("TR");e.appendChild(s);for(let e=0,t;e<r;e++){if(0===(t=n[e].cells).length)return;for(let i=0,l=t.length;i<l;i++)e+t[i].rowSpan-1>=r&&(t[i].rowSpan+=1)}let a=i._physical_cellIndex,u=o.cells;for(let e=0,t=u.length;e<t;e++)e!==a&&(u[e].rowSpan+=1);o.parentNode.insertBefore(e,o.nextElementSibling)}}this.focusEdge(l),this.plugins.table.setPositionControllerDiv.call(this,l,!0)},mergeCells:function(){let e=this.plugins.table,t=this.context.table,i=this.util,l=e._ref,n=e._selectedCells,o=n[0],a=null,r=null,s=l.ce-l.cs+1,u=l.re-l.rs+1,c="",d=null;for(let e=1,t=n.length,l,o;e<t;e++){d!==(l=n[e]).parentNode&&(d=l.parentNode),o=l.children;for(let e=0,t=o.length;e<t;e++)i.isFormatElement(o[e])&&i.onlyZeroWidthSpace(o[e].textContent)&&i.removeItem(o[e]);c+=l.innerHTML,i.removeItem(l),0===d.cells.length&&(a?r=d:a=d,u-=1)}if(a){let e=t._trElements,l=i.getArrayIndex(e,a),n=i.getArrayIndex(e,r||a),o=[];for(let t=0,a;t<=n;t++){if(0===(a=e[t].cells).length){o.push(e[t]);continue}for(let e=0,o=a.length,r,s;e<o;e++)(s=(r=a[e]).rowSpan-1)>0&&t+s>=l&&(r.rowSpan-=i.getOverlapRangeAtIndex(l,n,t,t+s))}for(let e=0,t=o.length;e<t;e++)i.removeItem(o[e])}o.innerHTML+=c,o.colSpan=s,o.rowSpan=u,this.controllersOff(),e.setActiveButton.call(this,!0,!1),e.call_controller_tableEdit.call(this,o),i.addClass(o,"se-table-selected-cell"),this.focusEdge(o)},toggleHeader:function(){let e=this.util,t=this.context.table.headerButton,i=e.hasClass(t,"active"),l=this.context.table._element;if(i)e.removeItem(l.querySelector("thead"));else{let t=e.createElement("THEAD");t.innerHTML="<tr>"+this.plugins.table.createCells.call(this,"th",this.context.table._logical_cellCnt,!1)+"</tr>",l.insertBefore(t,l.firstElementChild)}e.toggleClass(t,"active"),/TH/i.test(this.context.table._tdElement.nodeName)?this.controllersOff():this.plugins.table.setPositionControllerDiv.call(this,this.context.table._tdElement,!1)},setTableStyle:function(e){let t,i,l,n;let o=this.context.table,a=o._element;e.indexOf("width")>-1&&(t=o.resizeButton.firstElementChild,i=o.resizeText,o._maxWidth?(l=o.icons.reduction,n=o.minText,o.columnFixedButton.style.display="block",this.util.removeClass(a,"se-table-size-auto"),this.util.addClass(a,"se-table-size-100")):(l=o.icons.expansion,n=o.maxText,o.columnFixedButton.style.display="none",this.util.removeClass(a,"se-table-size-100"),this.util.addClass(a,"se-table-size-auto")),this.util.changeElement(t,l),this.util.changeTxt(i,n)),e.indexOf("column")>-1&&(o._fixedColumn?(this.util.removeClass(a,"se-table-layout-auto"),this.util.addClass(a,"se-table-layout-fixed"),this.util.addClass(o.columnFixedButton,"active")):(this.util.removeClass(a,"se-table-layout-fixed"),this.util.addClass(a,"se-table-layout-auto"),this.util.removeClass(o.columnFixedButton,"active")))},setActiveButton:function(e,t){let i=this.context.table;/^TH$/i.test(e.nodeName)?(i.insertRowAboveButton.setAttribute("disabled",!0),i.insertRowBelowButton.setAttribute("disabled",!0)):(i.insertRowAboveButton.removeAttribute("disabled"),i.insertRowBelowButton.removeAttribute("disabled")),t&&e!==t?(i.splitButton.setAttribute("disabled",!0),i.mergeButton.removeAttribute("disabled")):(i.splitButton.removeAttribute("disabled"),i.mergeButton.setAttribute("disabled",!0))},_bindOnSelect:null,_bindOffSelect:null,_bindOffShift:null,_selectedCells:null,_shift:!1,_fixedCell:null,_fixedCellName:null,_selectedCell:null,_selectedTable:null,_ref:null,_toggleEditor:function(e){this.context.element.wysiwyg.setAttribute("contenteditable",e),e?this.util.removeClass(this.context.element.wysiwyg,"se-disabled"):this.util.addClass(this.context.element.wysiwyg,"se-disabled")},_offCellMultiSelect:function(e){e.stopPropagation();let t=this.plugins.table;t._shift?t._initBind&&(this._wd.removeEventListener("touchmove",t._initBind),t._initBind=null):(t._removeEvents.call(this),t._toggleEditor.call(this,!0)),t._fixedCell&&t._selectedTable&&(t.setActiveButton.call(this,t._fixedCell,t._selectedCell),t.call_controller_tableEdit.call(this,t._selectedCell||t._fixedCell),t._selectedCells=t._selectedTable.querySelectorAll(".se-table-selected-cell"),t._selectedCell&&t._fixedCell&&this.focusEdge(t._selectedCell),t._shift||(t._fixedCell=null,t._selectedCell=null,t._fixedCellName=null))},_onCellMultiSelect:function(e){this._antiBlur=!0;let t=this.plugins.table,i=this.util.getParentElement(e.target,this.util.isCell);if(t._shift)i===t._fixedCell?t._toggleEditor.call(this,!0):t._toggleEditor.call(this,!1);else if(!t._ref){if(i===t._fixedCell)return;t._toggleEditor.call(this,!1)}i&&i!==t._selectedCell&&t._fixedCellName===i.nodeName&&t._selectedTable===this.util.getParentElement(i,"TABLE")&&(t._selectedCell=i,t._setMultiCells.call(this,t._fixedCell,i))},_setMultiCells:function(e,t){let i=this.plugins.table,l=i._selectedTable.rows,n=this.util,o=i._selectedTable.querySelectorAll(".se-table-selected-cell");for(let e=0,t=o.length;e<t;e++)n.removeClass(o[e],"se-table-selected-cell");if(e===t&&(n.addClass(e,"se-table-selected-cell"),!i._shift))return;let a=!0,r=[],s=[],u=i._ref={_i:0,cs:null,ce:null,rs:null,re:null};for(let i=0,o=l.length,c,d;i<o;i++){c=l[i].cells,d=0;for(let l=0,o=c.length,h,p,g,m;l<o;l++){if(g=(h=c[l]).colSpan-1,m=h.rowSpan-1,p=l+d,r.length>0)for(let e=0,t;e<r.length;e++)!((t=r[e]).row>i)&&(p>=t.index?(d+=t.cs,p+=t.cs,t.rs-=1,t.row=i+1,t.rs<1&&(r.splice(e,1),e--)):l===o-1&&(t.rs-=1,t.row=i+1,t.rs<1&&(r.splice(e,1),e--)));if(a){if((h===e||h===t)&&(u.cs=null!==u.cs&&u.cs<p?u.cs:p,u.ce=null!==u.ce&&u.ce>p+g?u.ce:p+g,u.rs=null!==u.rs&&u.rs<i?u.rs:i,u.re=null!==u.re&&u.re>i+m?u.re:i+m,u._i+=1),2===u._i){a=!1,r=[],s=[],i=-1;break}}else if(n.getOverlapRangeAtIndex(u.cs,u.ce,p,p+g)&&n.getOverlapRangeAtIndex(u.rs,u.re,i,i+m)){let e=u.cs<p?u.cs:p,t=u.ce>p+g?u.ce:p+g,l=u.rs<i?u.rs:i,o=u.re>i+m?u.re:i+m;if(u.cs!==e||u.ce!==t||u.rs!==l||u.re!==o){u.cs=e,u.ce=t,u.rs=l,u.re=o,i=-1,r=[],s=[];break}n.addClass(h,"se-table-selected-cell")}m>0&&s.push({index:p,cs:g+1,rs:m,row:-1}),d+=h.colSpan-1}r=r.concat(s).sort(function(e,t){return e.index-t.index}),s=[]}},_removeEvents:function(){let e=this.plugins.table;e._initBind&&(this._wd.removeEventListener("touchmove",e._initBind),e._initBind=null),e._bindOnSelect&&(this._wd.removeEventListener("mousedown",e._bindOnSelect),this._wd.removeEventListener("mousemove",e._bindOnSelect),e._bindOnSelect=null),e._bindOffSelect&&(this._wd.removeEventListener("mouseup",e._bindOffSelect),e._bindOffSelect=null),e._bindOffShift&&(this._wd.removeEventListener("keyup",e._bindOffShift),e._bindOffShift=null)},_initBind:null,onTableCellMultiSelect:function(e,t){let i=this.plugins.table;i._removeEvents.call(this),this.controllersOff(),i._shift=t,i._fixedCell=e,i._fixedCellName=e.nodeName,i._selectedTable=this.util.getParentElement(e,"TABLE");let l=i._selectedTable.querySelectorAll(".se-table-selected-cell");for(let e=0,t=l.length;e<t;e++)this.util.removeClass(l[e],"se-table-selected-cell");this.util.addClass(e,"se-table-selected-cell"),i._bindOnSelect=i._onCellMultiSelect.bind(this),i._bindOffSelect=i._offCellMultiSelect.bind(this),t?(i._bindOffShift=(function(){this.controllersOn(this.context.table.resizeDiv,this.context.table.tableController,this.plugins.table.init.bind(this),e,"table"),i._ref||this.controllersOff()}).bind(this),this._wd.addEventListener("keyup",i._bindOffShift,!1),this._wd.addEventListener("mousedown",i._bindOnSelect,!1)):this._wd.addEventListener("mousemove",i._bindOnSelect,!1),this._wd.addEventListener("mouseup",i._bindOffSelect,!1),i._initBind=i.init.bind(this),this._wd.addEventListener("touchmove",i._initBind,!1)},onClick_tableController:function(e){e.stopPropagation();let t=e.target.getAttribute("data-command")?e.target:e.target.parentNode;if(t.getAttribute("disabled"))return;let i=t.getAttribute("data-command"),l=t.getAttribute("data-value"),n=t.getAttribute("data-option"),o=this.plugins.table;if("function"==typeof o._closeSplitMenu&&(o._closeSplitMenu(),"onsplit"===i)||!i)return;e.preventDefault();let a=this.context.table;switch(i){case"insert":case"delete":o.editTable.call(this,l,n);break;case"header":o.toggleHeader.call(this);break;case"onsplit":o.openSplitMenu.call(this);break;case"split":o.splitCells.call(this,l);break;case"merge":o.mergeCells.call(this);break;case"resize":a._maxWidth=!a._maxWidth,o.setTableStyle.call(this,"width"),o.setPositionControllerTop.call(this,a._element),o.setPositionControllerDiv.call(this,a._tdElement,o._shift);break;case"layout":a._fixedColumn=!a._fixedColumn,o.setTableStyle.call(this,"column"),o.setPositionControllerTop.call(this,a._element),o.setPositionControllerDiv.call(this,a._tdElement,o._shift);break;case"remove":let r=a._element.parentNode;this.util.removeItem(a._element),this.controllersOff(),r!==this.context.element.wysiwyg&&this.util.removeItemAllParents(r,function(e){return 0===e.childNodes.length},null),this.focus()}this.history.push(!1)}},p={name:"formatBlock",display:"submenu",add:function(e,t){let i=e.context;i.formatBlock={targetText:t.querySelector(".txt"),targetTooltip:t.parentNode.querySelector(".se-tooltip-text"),_formatList:null,currentFormat:""};let l=this.setSubmenu(e);l.querySelector("ul").addEventListener("click",this.pickUp.bind(e)),i.formatBlock._formatList=l.querySelectorAll("li button"),e.initMenuTarget(this.name,t,l)},setSubmenu:function(e){let t=e.options,i=e.lang.toolbar,l=e.util.createElement("DIV");l.className="se-submenu se-list-layer se-list-format";let n=["p","div","blockquote","pre","h1","h2","h3","h4","h5","h6"],o=t.formats&&0!==t.formats.length?t.formats:n,a='<div class="se-list-inner"><ul class="se-list-basic">';for(let e=0,t=o.length,l,r,s,u,c,d,h;e<t;e++)"string"==typeof(l=o[e])&&n.indexOf(l)>-1?(s="blockquote"===(r=l.toLowerCase())?"range":"pre"===r?"free":"replace",u=i["tag_"+((c=/^h/.test(r)?r.match(/\d+/)[0]:"")?"h":r)]+c,h="",d=""):(r=l.tag.toLowerCase(),s=l.command,u=l.name||r,d=(h=l.class)?' class="'+h+'"':""),a+='<li><button type="button" class="se-btn-list" data-command="'+s+'" data-value="'+r+'" data-class="'+h+'" title="'+u+'" aria-label="'+u+'"><'+r+d+">"+u+"</"+r+"></button></li>";return a+="</ul></div>",l.innerHTML=a,l},active:function(e){let t=this.lang.toolbar.formats,i=this.context.formatBlock.targetText;if(e){if(this.util.isFormatElement(e)){let l=this.context.formatBlock._formatList,n=e.nodeName.toLowerCase(),o=(e.className.match(/(\s|^)__se__format__[^\s]+/)||[""])[0].trim();for(let e=0,i=l.length,a;e<i;e++)if(n===(a=l[e]).getAttribute("data-value")&&o===a.getAttribute("data-class")){t=a.title;break}return this.util.changeTxt(i,t),i.setAttribute("data-value",n),i.setAttribute("data-class",o),!0}}else this.util.changeTxt(i,t);return!1},on:function(){let e=this.context.formatBlock,t=e._formatList,i=e.targetText,l=(i.getAttribute("data-value")||"")+(i.getAttribute("data-class")||"");if(l!==e.currentFormat){for(let e=0,i=t.length,n;e<i;e++)l===(n=t[e]).getAttribute("data-value")+n.getAttribute("data-class")?this.util.addClass(n,"active"):this.util.removeClass(n,"active");e.currentFormat=l}},pickUp:function(e){e.preventDefault(),e.stopPropagation();let t=e.target,i=null,l=null,n=null,o="";for(;!i&&!/UL/i.test(t.tagName);){if(i=t.getAttribute("data-command"),l=t.getAttribute("data-value"),o=t.getAttribute("data-class"),i){n=t.firstChild;break}t=t.parentNode}if(i){if("range"===i){let e=n.cloneNode(!1);this.applyRangeFormatElement(e)}else{let e=this.getRange(),t=this.getSelectedElementsAndComponents(!1);if(0===t.length&&(e=this.getRange_addLine(e,null),0===(t=this.getSelectedElementsAndComponents(!1)).length))return;let a=e.startOffset,r=e.endOffset,s=this.util,u=t[0],c=t[t.length-1],d=s.getNodePath(e.startContainer,u,null,null),h=s.getNodePath(e.endContainer,c,null,null),p=this.detachList(t,!1);p.sc&&(u=p.sc),p.ec&&(c=p.ec),this.setRange(s.getNodeFromPath(d,u),a,s.getNodeFromPath(h,c),r);let g=this.getSelectedElementsAndComponents(!1);if("free"===i){let e=g.length-1,t=g[e].parentNode,i=n.cloneNode(!1),l=i;for(let l=e,o,a,r,u,c,d,h=!0;l>=0;l--)if((o=g[l])!==(g[l+1]?g[l+1].parentNode:null)){if(a=(d=s.isComponent(o))?"":o.innerHTML.replace(/(?!>)\s+(?=<)|\n/g," "),r=s.getParentElement(o,function(e){return e.parentNode===t}),(t!==o.parentNode||d)&&(s.isFormatElement(t)?(t.parentNode.insertBefore(i,t.nextSibling),t=t.parentNode):(t.insertBefore(i,r?r.nextSibling:null),t=o.parentNode),(u=i.nextSibling)&&i.nodeName===u.nodeName&&s.isSameAttributes(i,u)&&(i.innerHTML+="<BR>"+u.innerHTML,s.removeItem(u)),i=n.cloneNode(!1),h=!0),c=i.innerHTML,i.innerHTML=(h||!a||!c||/<br>$/i.test(a)?a:a+"<BR>")+c,0===l){t.insertBefore(i,o),(u=o.nextSibling)&&i.nodeName===u.nodeName&&s.isSameAttributes(i,u)&&(i.innerHTML+="<BR>"+u.innerHTML,s.removeItem(u));let e=i.previousSibling;e&&i.nodeName===e.nodeName&&s.isSameAttributes(i,e)&&(e.innerHTML+="<BR>"+i.innerHTML,s.removeItem(i))}d||s.removeItem(o),a&&(h=!1)}this.setRange(l,0,l,0)}else{for(let e=0,t=g.length,i,a;e<t;e++)(i=g[e]).nodeName.toLowerCase()===l.toLowerCase()&&(i.className.match(/(\s|^)__se__format__[^\s]+/)||[""])[0].trim()===o||s.isComponent(i)||(a=n.cloneNode(!1),s.copyFormatAttributes(a,i),a.innerHTML=i.innerHTML,i.parentNode.replaceChild(a,i)),0===e&&(u=a||i),e===t-1&&(c=a||i),a=null;this.setRange(s.getNodeFromPath(d,u),a,s.getNodeFromPath(h,c),r)}this.history.push(!1)}this.submenuOff()}}},g={name:"lineHeight",display:"submenu",add:function(e,t){let i=e.context;i.lineHeight={_sizeList:null,currentSize:-1};let l=this.setSubmenu(e),n=l.querySelector("ul");n.addEventListener("click",this.pickup.bind(e)),i.lineHeight._sizeList=n.querySelectorAll("li button"),e.initMenuTarget(this.name,t,l),l=null,n=null},setSubmenu:function(e){let t=e.options,i=e.lang,l=e.util.createElement("DIV");l.className="se-submenu se-list-layer";let n=t.lineHeights?t.lineHeights:[{text:"1",value:1},{text:"1.15",value:1.15},{text:"1.5",value:1.5},{text:"2",value:2}],o='<div class="se-list-inner"><ul class="se-list-basic"><li><button type="button" class="default_value se-btn-list" title="'+i.toolbar.default+'" aria-label="'+i.toolbar.default+'">('+i.toolbar.default+")</button></li>";for(let e=0,t=n.length,i;e<t;e++)o+='<li><button type="button" class="se-btn-list" data-value="'+(i=n[e]).value+'" title="'+i.text+'" aria-label="'+i.text+'">'+i.text+"</button></li>";return o+="</ul></div>",l.innerHTML=o,l},on:function(){let e=this.context.lineHeight,t=e._sizeList,i=this.util.getFormatElement(this.getSelectionNode()),l=i?i.style.lineHeight+"":"";if(l!==e.currentSize){for(let e=0,i=t.length;e<i;e++)l===t[e].getAttribute("data-value")?this.util.addClass(t[e],"active"):this.util.removeClass(t[e],"active");e.currentSize=l}},pickup:function(e){if(!/^BUTTON$/i.test(e.target.tagName))return!1;e.preventDefault(),e.stopPropagation();let t=e.target.getAttribute("data-value")||"",i=this.getSelectedElements();for(let e=0,l=i.length;e<l;e++)i[e].style.lineHeight=t;this.submenuOff(),this.history.push(!1)}},m={name:"template",display:"submenu",add:function(e,t){e.context.template={selectedIndex:-1};let i=this.setSubmenu(e);i.querySelector("ul").addEventListener("click",this.pickup.bind(e)),e.initMenuTarget(this.name,t,i)},setSubmenu:function(e){let t=e.options.templates;if(!t||0===t.length)throw Error('[SUNEDITOR.plugins.template.fail] To use the "template" plugin, please define the "templates" option.');let i=e.util.createElement("DIV");i.className="se-list-layer";let l='<div class="se-submenu se-list-inner"><ul class="se-list-basic">';for(let e=0,i=t.length,n;e<i;e++)n=t[e],l+='<li><button type="button" class="se-btn-list" data-value="'+e+'" title="'+n.name+'" aria-label="'+n.name+'">'+n.name+"</button></li>";return l+="</ul></div>",i.innerHTML=l,i},pickup:function(e){if(!/^BUTTON$/i.test(e.target.tagName))return!1;e.preventDefault(),e.stopPropagation(),this.context.template.selectedIndex=1*e.target.getAttribute("data-value");let t=this.options.templates[this.context.template.selectedIndex];if(t.html)this.setContents(t.html);else throw this.submenuOff(),Error('[SUNEDITOR.template.fail] cause : "templates[i].html not found"');this.submenuOff()}},f={name:"paragraphStyle",display:"submenu",add:function(e,t){let i=e.context;i.paragraphStyle={_classList:null};let l=this.setSubmenu(e);l.querySelector("ul").addEventListener("click",this.pickUp.bind(e)),i.paragraphStyle._classList=l.querySelectorAll("li button"),e.initMenuTarget(this.name,t,l)},setSubmenu:function(e){let t=e.options,i=e.util.createElement("DIV");i.className="se-submenu se-list-layer se-list-format";let l=e.lang.menu,n={spaced:{name:l.spaced,class:"__se__p-spaced",_class:""},bordered:{name:l.bordered,class:"__se__p-bordered",_class:""},neon:{name:l.neon,class:"__se__p-neon",_class:""}},o=t.paragraphStyles&&0!==t.paragraphStyles.length?t.paragraphStyles:["spaced","bordered","neon"],a='<div class="se-list-inner"><ul class="se-list-basic">';for(let e=0,t=o.length,i,l,r,s;e<t;e++){if("string"==typeof(i=o[e])){let e=n[i.toLowerCase()];if(!e)continue;i=e}l=i.name,r=i.class?' class="'+i.class+'"':"",a+='<li><button type="button" class="se-btn-list'+((s=i._class)?" "+s:"")+'" data-value="'+i.class+'" title="'+l+'" aria-label="'+l+'"><div'+r+">"+l+"</div></button></li>"}return a+="</ul></div>",i.innerHTML=a,i},on:function(){let e=this.context.paragraphStyle._classList,t=this.util.getFormatElement(this.getSelectionNode());for(let i=0,l=e.length;i<l;i++)this.util.hasClass(t,e[i].getAttribute("data-value"))?this.util.addClass(e[i],"active"):this.util.removeClass(e[i],"active")},pickUp:function(e){e.preventDefault(),e.stopPropagation();let t=e.target,i=null;for(;!/^UL$/i.test(t.tagName)&&!(i=t.getAttribute("data-value"));)t=t.parentNode;if(!i)return;let l=this.getSelectedElements();if(0===l.length&&(this.getRange_addLine(this.getRange(),null),0===(l=this.getSelectedElements()).length))return;let n=this.util.hasClass(t,"active")?this.util.removeClass.bind(this.util):this.util.addClass.bind(this.util);for(let e=0,t=l.length;e<t;e++)n(l[e],i);this.submenuOff(),this.history.push(!1)}},b={name:"textStyle",display:"submenu",add:function(e,t){let i=e.context;i.textStyle={_styleList:null};let l=this.setSubmenu(e),n=l.querySelector("ul");n.addEventListener("click",this.pickup.bind(e)),i.textStyle._styleList=l.querySelectorAll("li button"),e.initMenuTarget(this.name,t,l),l=null,n=null},setSubmenu:function(e){let t=e.options,i=e.util.createElement("DIV");i.className="se-submenu se-list-layer se-list-format";let l={code:{name:e.lang.menu.code,class:"__se__t-code",tag:"code"},translucent:{name:e.lang.menu.translucent,style:"opacity: 0.5;",tag:"span"},shadow:{name:e.lang.menu.shadow,class:"__se__t-shadow",tag:"span"}},n=t.textStyles?t.textStyles:e._w.Object.keys(l),o='<div class="se-list-inner"><ul class="se-list-basic">';for(let e=0,t=n.length,i,a,r,s,u,c,d;e<t;e++){if(i=n[e],s="",c="",u=[],"string"==typeof i){let e=l[i.toLowerCase()];if(!e)continue;i=e}r=i.name,a=i.tag||"span",d=i._class,i.style&&(s+=' style="'+i.style+'"',c+=i.style.replace(/:[^;]+(;|$)\s*/g,","),u.push("style")),i.class&&(s+=' class="'+i.class+'"',c+="."+i.class.trim().replace(/\s+/g,",."),u.push("class")),o+='<li><button type="button" class="se-btn-list'+(d?" "+d:"")+'" data-command="'+a+'" data-value="'+(c=c.replace(/,$/,""))+'" title="'+r+'" aria-label="'+r+'"><'+a+s+">"+r+"</"+a+"></button></li>"}return o+="</ul></div>",i.innerHTML=o,i},on:function(){let e=this.util,t=this.context.textStyle._styleList,i=this.getSelectionNode();for(let l=0,n=t.length,o,a,r;l<n;l++){a=(o=t[l]).getAttribute("data-value").split(",");for(let t=0,l,n;t<a.length;t++){for(l=i,r=!1;l&&!e.isFormatElement(l)&&!e.isComponent(l);){if(l.nodeName.toLowerCase()===o.getAttribute("data-command").toLowerCase()&&(n=a[t],/^\./.test(n)?e.hasClass(l,n.replace(/^\./,"")):!!l.style[n])){r=!0;break}l=l.parentNode}if(!r)break}r?e.addClass(o,"active"):e.removeClass(o,"active")}},pickup:function(e){e.preventDefault(),e.stopPropagation();let t=e.target,i=null,l=null;for(;!i&&!/UL/i.test(t.tagName);){if(i=t.getAttribute("data-command")){l=t.firstChild;break}t=t.parentNode}if(!i)return;let n=l.style.cssText.replace(/:.+(;|$)/g,",").split(",");n.pop();let o=l.classList;for(let e=0,t=o.length;e<t;e++)n.push("."+o[e]);let a=this.util.hasClass(t,"active")?null:l.cloneNode(!1),r=a?null:[l.nodeName];this.nodeChange(a,n,r,!0),this.submenuOff()}},v=i(10375),_=i.n(v),w={name:"selectMenu",add:function(e){e.context.selectMenu={caller:{},callerContext:null}},setForm:function(){return'<div class="se-select-list"></div>'},createList:function(e,t,i){e.form.innerHTML="<ul>"+i+"</ul>",e.items=t,e.menus=e.form.querySelectorAll("li")},initEvent:function(e,t){let i=t.querySelector(".se-select-list"),l=this.context.selectMenu.caller[e]={form:i,items:[],menus:[],index:-1,item:null,clickMethod:null,callerName:e};i.addEventListener("mousedown",this.plugins.selectMenu.onMousedown_list),i.addEventListener("mousemove",this.plugins.selectMenu.onMouseMove_list.bind(this,l)),i.addEventListener("click",this.plugins.selectMenu.onClick_list.bind(this,l))},onMousedown_list:function(e){e.preventDefault(),e.stopPropagation()},onMouseMove_list:function(e,t){this.util.addClass(e.form,"__se_select-menu-mouse-move");let i=t.target.getAttribute("data-index");i&&(e.index=1*i)},onClick_list:function(e,t){let i=t.target.getAttribute("data-index");i&&e.clickMethod.call(this,e.items[i])},moveItem:function(e,t){this.util.removeClass(e.form,"__se_select-menu-mouse-move"),t=e.index+t;let i=e.menus,l=i.length,n=e.index=t>=l?0:t<0?l-1:t;for(let e=0;e<l;e++)e===n?this.util.addClass(i[e],"active"):this.util.removeClass(i[e],"active");e.item=e.items[n]},getItem:function(e,t){return t=!t||t<0?e.index:t,e.items[t]},on:function(e,t){let i=this.context.selectMenu.caller[e];this.context.selectMenu.callerContext=i,i.clickMethod=t,i.callerName=e},open:function(e,t){let i=e.form;i.style.visibility="hidden",i.style.display="block",t(i),i.style.visibility=""},close:function(e){e.form.style.display="none",e.items=[],e.menus=[],e.index=-1,e.item=null},init:function(e){e&&(e.items=[],e.menus=[],e.index=-1,e.item=null,e.callerName="",this.context.selectMenu.callerContext=null)}},y={name:"anchor",add:function(e){e.addModule([w]),e.context.anchor={caller:{},forms:this.setDialogForm(e),host:(e._w.location.origin+e._w.location.pathname).replace(/\/$/,""),callerContext:null}},setDialogForm:function(e){let t=e.lang,i=e.options.linkRel,l=(e.options.linkRelDefault.default||"").split(" "),n=e.icons,o=e.util.createElement("DIV"),a='<div class="se-dialog-body"><div class="se-dialog-form"><label>'+t.dialogBox.linkBox.url+'</label><div class="se-dialog-form-files"><input class="se-input-form se-input-url" type="text" placeholder="'+(e.options.protocol||"")+'" /><button type="button" class="se-btn se-dialog-files-edge-button _se_bookmark_button" title="'+t.dialogBox.linkBox.bookmark+'" aria-label="'+t.dialogBox.linkBox.bookmark+'">'+n.bookmark+"</button>"+e.plugins.selectMenu.setForm()+'</div><div class="se-anchor-preview-form"><span class="se-svg se-anchor-preview-icon _se_anchor_bookmark_icon">'+n.bookmark+'</span><span class="se-svg se-anchor-preview-icon _se_anchor_download_icon">'+n.download+'</span><pre class="se-link-preview"></pre></div></div><div class="se-dialog-form"><label>'+t.dialogBox.linkBox.text+'</label><input class="se-input-form _se_anchor_text" type="text" /></div><div class="se-dialog-form-footer"><label><input type="checkbox" class="se-dialog-btn-check _se_anchor_check" />&nbsp;'+t.dialogBox.linkBox.newWindowCheck+'</label><label><input type="checkbox" class="se-dialog-btn-check _se_anchor_download" />&nbsp;'+t.dialogBox.linkBox.downloadLinkCheck+"</label>";if(i.length>0){a+='<div class="se-anchor-rel"><button type="button" class="se-btn se-btn-select se-anchor-rel-btn">&lt;rel&gt;</button><div class="se-anchor-rel-wrapper"><pre class="se-link-preview se-anchor-rel-preview"></pre></div><div class="se-list-layer"><div class="se-list-inner"><ul class="se-list-basic se-list-checked">';for(let e=0,t=i.length,o;e<t;e++)o=i[e],a+='<li><button type="button" class="se-btn-list'+(l.indexOf(o)>-1?" se-checked":"")+'" data-command="'+o+'" title="'+o+'" aria-label="'+o+'"><span class="se-svg">'+n.checked+"</span>"+o+"</button></li>";a+="</ul></div></div></div>"}return a+="</div></div>",o.innerHTML=a,o},initEvent:function(e,t){let i=this.plugins.anchor,l=this.context.anchor.caller[e]={modal:t,urlInput:null,linkDefaultRel:this.options.linkRelDefault,defaultRel:this.options.linkRelDefault.default||"",currentRel:[],linkAnchor:null,linkValue:"",_change:!1,callerName:e};"string"==typeof l.linkDefaultRel.default&&(l.linkDefaultRel.default=l.linkDefaultRel.default.trim()),"string"==typeof l.linkDefaultRel.check_new_window&&(l.linkDefaultRel.check_new_window=l.linkDefaultRel.check_new_window.trim()),"string"==typeof l.linkDefaultRel.check_bookmark&&(l.linkDefaultRel.check_bookmark=l.linkDefaultRel.check_bookmark.trim()),l.urlInput=t.querySelector(".se-input-url"),l.anchorText=t.querySelector("._se_anchor_text"),l.newWindowCheck=t.querySelector("._se_anchor_check"),l.downloadCheck=t.querySelector("._se_anchor_download"),l.download=t.querySelector("._se_anchor_download_icon"),l.preview=t.querySelector(".se-link-preview"),l.bookmark=t.querySelector("._se_anchor_bookmark_icon"),l.bookmarkButton=t.querySelector("._se_bookmark_button"),this.plugins.selectMenu.initEvent.call(this,e,t);let n=this.context.selectMenu.caller[e];this.options.linkRel.length>0&&(l.relButton=t.querySelector(".se-anchor-rel-btn"),l.relList=t.querySelector(".se-list-layer"),l.relPreview=t.querySelector(".se-anchor-rel-preview"),l.relButton.addEventListener("click",i.onClick_relButton.bind(this,l)),l.relList.addEventListener("click",i.onClick_relList.bind(this,l))),l.newWindowCheck.addEventListener("change",i.onChange_newWindowCheck.bind(this,l)),l.downloadCheck.addEventListener("change",i.onChange_downloadCheck.bind(this,l)),l.anchorText.addEventListener("input",i.onChangeAnchorText.bind(this,l)),l.urlInput.addEventListener("input",i.onChangeUrlInput.bind(this,l)),l.urlInput.addEventListener("keydown",i.onKeyDownUrlInput.bind(this,n)),l.urlInput.addEventListener("focus",i.onFocusUrlInput.bind(this,l,n)),l.urlInput.addEventListener("blur",i.onBlurUrlInput.bind(this,n)),l.bookmarkButton.addEventListener("click",i.onClick_bookmarkButton.bind(this,l))},on:function(e,t){let i=this.plugins.anchor;if(t){if(e.linkAnchor){this.context.dialog.updateModal=!0;let t=e.linkAnchor.getAttribute("href");e.linkValue=e.preview.textContent=e.urlInput.value=i.selfPathBookmark.call(this,t)?t.substr(t.lastIndexOf("#")):t,e.anchorText.value=e.linkAnchor.textContent,e.newWindowCheck.checked=!!/_blank/i.test(e.linkAnchor.target),e.downloadCheck.checked=e.linkAnchor.download}}else i.init.call(this,e),e.anchorText.value=this.getSelection().toString().trim(),e.newWindowCheck.checked=this.options.linkTargetNewWindow;this.context.anchor.callerContext=e,i.setRel.call(this,e,t&&e.linkAnchor?e.linkAnchor.rel:e.defaultRel),i.setLinkPreview.call(this,e,e.linkValue),this.plugins.selectMenu.on.call(this,e.callerName,this.plugins.anchor.setHeaderBookmark)},selfPathBookmark:function(e){let t=this._w.location.href.replace(/\/$/,"");return 0===e.indexOf("#")||0===e.indexOf(t)&&e.indexOf("#")===(-1===t.indexOf("#")?t.length:t.substr(0,t.indexOf("#")).length)},_closeRelMenu:null,toggleRelList:function(e,t){if(t){let t=e.relButton,i=e.relList;this.util.addClass(t,"active"),i.style.visibility="hidden",i.style.display="block",this.options.rtl?i.style.left=t.offsetLeft-i.offsetWidth-1+"px":i.style.left=t.offsetLeft+t.offsetWidth+1+"px",i.style.top=t.offsetTop+t.offsetHeight/2-i.offsetHeight/2+"px",i.style.visibility="",this.plugins.anchor._closeRelMenu=(function(e,t,i){i&&(e.relButton.contains(i.target)||e.relList.contains(i.target))||(this.util.removeClass(t,"active"),e.relList.style.display="none",this.modalForm.removeEventListener("click",this.plugins.anchor._closeRelMenu),this.plugins.anchor._closeRelMenu=null)}).bind(this,e,t),this.modalForm.addEventListener("click",this.plugins.anchor._closeRelMenu)}else this.plugins.anchor._closeRelMenu&&this.plugins.anchor._closeRelMenu()},onClick_relButton:function(e,t){this.plugins.anchor.toggleRelList.call(this,e,!this.util.hasClass(t.target,"active"))},onClick_relList:function(e,t){let i=t.target,l=i.getAttribute("data-command");if(!l)return;let n=e.currentRel,o=this.util.toggleClass(i,"se-checked"),a=n.indexOf(l);o?-1===a&&n.push(l):a>-1&&n.splice(a,1),e.relPreview.title=e.relPreview.textContent=n.join(" ")},setRel:function(e,t){let i=e.relList,l=e.currentRel=t?t.split(" "):[];if(!i)return;let n=i.querySelectorAll("button");for(let e=0,t=n.length,i;e<t;e++)i=n[e].getAttribute("data-command"),l.indexOf(i)>-1?this.util.addClass(n[e],"se-checked"):this.util.removeClass(n[e],"se-checked");e.relPreview.title=e.relPreview.textContent=l.join(" ")},createHeaderList:function(e,t,i){let l=this.util.getListChildren(this.context.element.wysiwyg,function(e){return/h[1-6]/i.test(e.nodeName)});if(0===l.length)return;let n=new this._w.RegExp("^"+i.replace(/^#/,""),"i"),o=[],a="";for(let e=0,t=l.length,i;e<t;e++)i=l[e],n.test(i.textContent)&&(o.push(i),a+='<li class="se-select-item" data-index="'+e+'">'+i.textContent+"</li>");0===o.length?this.plugins.selectMenu.close.call(this,t):(this.plugins.selectMenu.createList(t,o,a),this.plugins.selectMenu.open.call(this,t,this.plugins.anchor._setMenuListPosition.bind(this,e)))},_setMenuListPosition:function(e,t){t.style.top=e.urlInput.offsetHeight+1+"px"},onKeyDownUrlInput:function(e,t){switch(t.keyCode){case 38:t.preventDefault(),t.stopPropagation(),this.plugins.selectMenu.moveItem.call(this,e,-1);break;case 40:t.preventDefault(),t.stopPropagation(),this.plugins.selectMenu.moveItem.call(this,e,1);break;case 13:e.index>-1&&(t.preventDefault(),t.stopPropagation(),this.plugins.anchor.setHeaderBookmark.call(this,this.plugins.selectMenu.getItem(e,null)))}},setHeaderBookmark:function(e){let t=this.context.anchor.callerContext,i=e.id||"h_"+this._w.Math.random().toString().replace(/.+\./,"");e.id=i,t.urlInput.value="#"+i,t.anchorText.value.trim()&&t._change||(t.anchorText.value=e.textContent),this.plugins.anchor.setLinkPreview.call(this,t,t.urlInput.value),this.plugins.selectMenu.close.call(this,this.context.selectMenu.callerContext),this.context.anchor.callerContext.urlInput.focus()},onChangeAnchorText:function(e,t){e._change=!!t.target.value.trim()},onChangeUrlInput:function(e,t){let i=t.target.value.trim();this.plugins.anchor.setLinkPreview.call(this,e,i),this.plugins.anchor.selfPathBookmark.call(this,i)?this.plugins.anchor.createHeaderList.call(this,e,this.context.selectMenu.callerContext,i):this.plugins.selectMenu.close.call(this,this.context.selectMenu.callerContext)},onFocusUrlInput:function(e,t){let i=e.urlInput.value;this.plugins.anchor.selfPathBookmark.call(this,i)&&this.plugins.anchor.createHeaderList.call(this,e,t,i)},onBlurUrlInput:function(e){this.plugins.selectMenu.close.call(this,e)},setLinkPreview:function(e,t){let i=e.preview,l=this.options.linkProtocol,n=this.options.linkNoPrefix,o=/^(mailto\:|tel\:|sms\:|https*\:\/\/|#)/.test(t)||0===t.indexOf(l),a=!!l&&this._w.RegExp("^"+this.util.escapeStringRegexp(t.substr(0,l.length))).test(l);t=e.linkValue=i.textContent=t?n?t:!l||o||a?o?t:/^www\./.test(t)?"http://"+t:this.context.anchor.host+(/^\//.test(t)?"":"/")+t:l+t:"",this.plugins.anchor.selfPathBookmark.call(this,t)?(e.bookmark.style.display="block",this.util.addClass(e.bookmarkButton,"active")):(e.bookmark.style.display="none",this.util.removeClass(e.bookmarkButton,"active")),!this.plugins.anchor.selfPathBookmark.call(this,t)&&e.downloadCheck.checked?e.download.style.display="block":e.download.style.display="none"},setCtx:function(e,t){e&&(t.linkAnchor=e,t.linkValue=e.href,t.currentRel=e.rel.split(" "))},updateAnchor:function(e,t,i,l,n){!this.plugins.anchor.selfPathBookmark.call(this,t)&&l.downloadCheck.checked?e.setAttribute("download",i||t):e.removeAttribute("download"),l.newWindowCheck.checked?e.target="_blank":e.removeAttribute("target");let o=l.currentRel.join(" ");o?e.rel=o:e.removeAttribute("rel"),e.href=t,n?0===e.children.length&&(e.textContent=""):e.textContent=i},createAnchor:function(e,t){if(0===e.linkValue.length)return null;let i=e.linkValue,l=e.anchorText,n=0===l.value.length?i:l.value,o=e.linkAnchor||this.util.createElement("A");return this.plugins.anchor.updateAnchor.call(this,o,i,n,e,t),e.linkValue=e.preview.textContent=e.urlInput.value=e.anchorText.value="",o},onClick_bookmarkButton:function(e){let t=e.urlInput.value;this.plugins.anchor.selfPathBookmark.call(this,t)?(t=t.substr(1),e.bookmark.style.display="none",this.util.removeClass(e.bookmarkButton,"active"),this.plugins.selectMenu.close.call(this,this.context.selectMenu.callerContext)):(t="#"+t,e.bookmark.style.display="block",this.util.addClass(e.bookmarkButton,"active"),e.downloadCheck.checked=!1,e.download.style.display="none",this.plugins.anchor.createHeaderList.call(this,e,this.context.selectMenu.callerContext,t)),e.urlInput.value=t,this.plugins.anchor.setLinkPreview.call(this,e,t),e.urlInput.focus()},onChange_newWindowCheck:function(e,t){"string"==typeof e.linkDefaultRel.check_new_window&&(t.target.checked?this.plugins.anchor.setRel.call(this,e,this.plugins.anchor._relMerge.call(this,e,e.linkDefaultRel.check_new_window)):this.plugins.anchor.setRel.call(this,e,this.plugins.anchor._relDelete.call(this,e,e.linkDefaultRel.check_new_window)))},onChange_downloadCheck:function(e,t){t.target.checked?(e.download.style.display="block",e.bookmark.style.display="none",this.util.removeClass(e.bookmarkButton,"active"),e.linkValue=e.preview.textContent=e.urlInput.value=e.urlInput.value.replace(/^\#+/,""),"string"==typeof e.linkDefaultRel.check_bookmark&&this.plugins.anchor.setRel.call(this,e,this.plugins.anchor._relMerge.call(this,e,e.linkDefaultRel.check_bookmark))):(e.download.style.display="none","string"==typeof e.linkDefaultRel.check_bookmark&&this.plugins.anchor.setRel.call(this,e,this.plugins.anchor._relDelete.call(this,e,e.linkDefaultRel.check_bookmark)))},_relMerge:function(e,t){let i=e.currentRel;if(!t)return i.join(" ");if(/^only\:/.test(t))return t=t.replace(/^only\:/,"").trim(),e.currentRel=t.split(" "),t;let l=t.split(" ");for(let e=0,t=l.length;e<t;e++)-1===i.indexOf(l[e])&&i.push(l[e]);return i.join(" ")},_relDelete:function(e,t){if(!t)return e.currentRel.join(" ");/^only\:/.test(t)&&(t=t.replace(/^only\:/,"").trim());let i=e.currentRel.join(" ").replace(this._w.RegExp(t+"\\s*"),"");return e.currentRel=i.split(" "),i},init:function(e){e.linkAnchor=null,e.linkValue=e.preview.textContent=e.urlInput.value="",e.anchorText.value="",e.newWindowCheck.checked=!1,e.downloadCheck.checked=!1,e._change=!1,this.plugins.anchor.setRel.call(this,e,e.defaultRel),e.relList&&this.plugins.anchor.toggleRelList.call(this,e,!1),this.context.anchor.callerContext=null,this.plugins.selectMenu.init.call(this,this.context.selectMenu.callerContext)}},x={name:"link",display:"dialog",add:function(e){e.addModule([_(),y]);let t=e.context,i=t.link={focusElement:null,_linkAnchor:null,anchorCtx:null},l=this.setDialog(e);i.modal=l;let n=this.setController_LinkButton(e);i.linkController=n,l.querySelector("form").addEventListener("submit",this.submit.bind(e)),n.addEventListener("click",this.onClick_linkController.bind(e)),t.dialog.modal.appendChild(l),t.element.relative.appendChild(n),e.plugins.anchor.initEvent.call(e,"link",l),i.focusElement=t.anchor.caller.link.urlInput,l=null,n=null},setDialog:function(e){let t=e.lang,i=e.util.createElement("DIV"),l=e.icons;i.className="se-dialog-content",i.style.display="none";let n='<form><div class="se-dialog-header"><button type="button" data-command="close" class="se-btn se-dialog-close" title="'+t.dialogBox.close+'" aria-label="'+t.dialogBox.close+'">'+l.cancel+'</button><span class="se-modal-title">'+t.dialogBox.linkBox.title+"</span></div>"+e.context.anchor.forms.innerHTML+'<div class="se-dialog-footer"><button type="submit" class="se-btn-primary" title="'+t.dialogBox.submitButton+'" aria-label="'+t.dialogBox.submitButton+'"><span>'+t.dialogBox.submitButton+"</span></button></div></form>";return i.innerHTML=n,i},setController_LinkButton:function(e){let t=e.lang,i=e.icons,l=e.util.createElement("DIV");return l.className="se-controller se-controller-link",l.innerHTML='<div class="se-arrow se-arrow-up"></div><div class="link-content"><span><a target="_blank" href=""></a>&nbsp;</span><div class="se-btn-group"><button type="button" data-command="update" tabindex="-1" class="se-btn se-tooltip">'+i.edit+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.edit+'</span></span></button><button type="button" data-command="unlink" tabindex="-1" class="se-btn se-tooltip">'+i.unlink+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.unlink+'</span></span></button><button type="button" data-command="delete" tabindex="-1" class="se-btn se-tooltip">'+i.delete+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.remove+"</span></span></button></div></div>",l},open:function(){this.plugins.dialog.open.call(this,"link","link"===this.currentControllerName)},submit:function(e){this.showLoading(),e.preventDefault(),e.stopPropagation();try{let e=this.plugins.anchor.createAnchor.call(this,this.context.anchor.caller.link,!1);if(null===e)return;if(this.context.dialog.updateModal){let e=this.context.link._linkAnchor.childNodes[0];this.setRange(e,0,e,e.textContent.length)}else{let t=this.getSelectedElements();if(t.length>1){let i=this.util.createElement(t[0].nodeName);if(i.appendChild(e),!this.insertNode(i,null,!0))return}else if(!this.insertNode(e,null,!0))return;this.setRange(e.childNodes[0],0,e.childNodes[0],e.textContent.length)}}finally{this.plugins.dialog.close.call(this),this.closeLoading(),this.history.push(!1)}return!1},active:function(e){if(e){if(this.util.isAnchor(e)&&null===e.getAttribute("data-image-link"))return 0>this.controllerArray.indexOf(this.context.link.linkController)&&this.plugins.link.call_controller.call(this,e),!0}else this.controllerArray.indexOf(this.context.link.linkController)>-1&&this.controllersOff();return!1},on:function(e){this.plugins.anchor.on.call(this,this.context.anchor.caller.link,e)},call_controller:function(e){this.editLink=this.context.link._linkAnchor=this.context.anchor.caller.link.linkAnchor=e;let t=this.context.link.linkController,i=t.querySelector("a");i.href=e.href,i.title=e.textContent,i.textContent=e.textContent,this.util.addClass(e,"on"),this.setControllerPosition(t,e,"bottom",{left:0,top:0}),this.controllersOn(t,e,"link",this.util.removeClass.bind(this.util,this.context.link._linkAnchor,"on"))},onClick_linkController:function(e){e.stopPropagation();let t=e.target.getAttribute("data-command")||e.target.parentNode.getAttribute("data-command");if(t){if(e.preventDefault(),/update/.test(t))this.plugins.dialog.open.call(this,"link",!0);else if(/unlink/.test(t)){let e=this.util.getChildElement(this.context.link._linkAnchor,function(e){return 0===e.childNodes.length||3===e.nodeType},!1),t=this.util.getChildElement(this.context.link._linkAnchor,function(e){return 0===e.childNodes.length||3===e.nodeType},!0);this.setRange(e,0,t,t.textContent.length),this.nodeChange(null,null,["A"],!1)}else this.util.removeItem(this.context.link._linkAnchor),this.context.anchor.caller.link.linkAnchor=null,this.focus(),this.history.push(!1);this.controllersOff()}},init:function(){this.context.link.linkController.style.display="none",this.plugins.anchor.init.call(this,this.context.anchor.caller.link)}},C=i(91497),k=i.n(C),S=i(10877),z=i.n(S),L=i(56350),E=i.n(L),A={name:"image",display:"dialog",add:function(e){e.addModule([_(),y,k(),z(),E()]);let t=e.options,i=e.context,l=i.image={_infoList:[],_infoIndex:0,_uploadFileLength:0,focusElement:null,sizeUnit:t._imageSizeUnit,_linkElement:"",_altText:"",_align:"none",_floatClassRegExp:"__se__float\\-[a-z]+",_v_src:{_linkValue:""},svgDefaultSize:"30%",base64RenderIndex:0,_element:null,_cover:null,_container:null,inputX:null,inputY:null,_element_w:1,_element_h:1,_element_l:0,_element_t:0,_defaultSizeX:"auto",_defaultSizeY:"auto",_origin_w:"auto"===t.imageWidth?"":t.imageWidth,_origin_h:"auto"===t.imageHeight?"":t.imageHeight,_proportionChecked:!0,_resizing:t.imageResizing,_resizeDotHide:!t.imageHeightShow,_rotation:t.imageRotation,_alignHide:!t.imageAlignShow,_onlyPercentage:t.imageSizeOnlyPercentage,_ratio:!1,_ratioX:1,_ratioY:1,_captionShow:!0,_captionChecked:!1,_caption:null,captionCheckEl:null},n=this.setDialog(e);l.modal=n,l.imgInputFile=n.querySelector("._se_image_file"),l.imgUrlFile=n.querySelector("._se_image_url"),l.focusElement=l.imgInputFile||l.imgUrlFile,l.altText=n.querySelector("._se_image_alt"),l.captionCheckEl=n.querySelector("._se_image_check_caption"),l.previewSrc=n.querySelector("._se_tab_content_image .se-link-preview"),n.querySelector(".se-dialog-tabs").addEventListener("click",this.openTab.bind(e)),n.querySelector("form").addEventListener("submit",this.submit.bind(e)),l.imgInputFile&&n.querySelector(".se-file-remove").addEventListener("click",this._removeSelectedFiles.bind(l.imgInputFile,l.imgUrlFile,l.previewSrc)),l.imgUrlFile&&l.imgUrlFile.addEventListener("input",this._onLinkPreview.bind(l.previewSrc,l._v_src,t.linkProtocol)),l.imgInputFile&&l.imgUrlFile&&l.imgInputFile.addEventListener("change",this._fileInputChange.bind(l));let o=n.querySelector(".__se__gallery");o&&o.addEventListener("click",this._openGallery.bind(e)),l.proportion={},l.inputX={},l.inputY={},t.imageResizing&&(l.proportion=n.querySelector("._se_image_check_proportion"),l.inputX=n.querySelector("._se_image_size_x"),l.inputY=n.querySelector("._se_image_size_y"),l.inputX.value=t.imageWidth,l.inputY.value=t.imageHeight,l.inputX.addEventListener("keyup",this.setInputSize.bind(e,"x")),l.inputY.addEventListener("keyup",this.setInputSize.bind(e,"y")),l.inputX.addEventListener("change",this.setRatio.bind(e)),l.inputY.addEventListener("change",this.setRatio.bind(e)),l.proportion.addEventListener("change",this.setRatio.bind(e)),n.querySelector(".se-dialog-btn-revert").addEventListener("click",this.sizeRevert.bind(e))),i.dialog.modal.appendChild(n),e.plugins.anchor.initEvent.call(e,"image",n.querySelector("._se_tab_content_url")),l.anchorCtx=e.context.anchor.caller.image},setDialog:function(e){let t=e.options,i=e.lang,l=e.util.createElement("DIV");l.className="se-dialog-content se-dialog-image",l.style.display="none";let n='<div class="se-dialog-header"><button type="button" data-command="close" class="se-btn se-dialog-close" class="close" title="'+i.dialogBox.close+'" aria-label="'+i.dialogBox.close+'">'+e.icons.cancel+'</button><span class="se-modal-title">'+i.dialogBox.imageBox.title+'</span></div><div class="se-dialog-tabs"><button type="button" class="_se_tab_link active" data-tab-link="image">'+i.toolbar.image+'</button><button type="button" class="_se_tab_link" data-tab-link="url">'+i.toolbar.link+'</button></div><form method="post" enctype="multipart/form-data"><div class="_se_tab_content _se_tab_content_image"><div class="se-dialog-body"><div style="border-bottom: 1px dashed #ccc;">';if(t.imageFileInput&&(n+='<div class="se-dialog-form"><label>'+i.dialogBox.imageBox.file+'</label><div class="se-dialog-form-files"><input class="se-input-form _se_image_file" type="file" accept="'+t.imageAccept+'"'+(t.imageMultipleFile?' multiple="multiple"':"")+'/><button type="button" class="se-btn se-dialog-files-edge-button se-file-remove" title="'+i.controller.remove+'" aria-label="'+i.controller.remove+'">'+e.icons.cancel+"</button></div></div>"),t.imageUrlInput&&(n+='<div class="se-dialog-form"><label>'+i.dialogBox.imageBox.url+'</label><div class="se-dialog-form-files"><input class="se-input-form se-input-url _se_image_url" type="text" />'+(t.imageGalleryUrl&&e.plugins.imageGallery?'<button type="button" class="se-btn se-dialog-files-edge-button __se__gallery" title="'+i.toolbar.imageGallery+'" aria-label="'+i.toolbar.imageGallery+'">'+e.icons.image_gallery+"</button>":"")+'</div><pre class="se-link-preview"></pre></div>'),n+='</div><div class="se-dialog-form"><label>'+i.dialogBox.imageBox.altText+'</label><input class="se-input-form _se_image_alt" type="text" /></div>',t.imageResizing){let l=t.imageSizeOnlyPercentage,o=l?' style="display: none !important;"':"",a=t.imageHeightShow?"":' style="display: none !important;"';n+='<div class="se-dialog-form">',l||!t.imageHeightShow?n+='<div class="se-dialog-size-text"><label class="size-w">'+i.dialogBox.size+"</label></div>":n+='<div class="se-dialog-size-text"><label class="size-w">'+i.dialogBox.width+'</label><label class="se-dialog-size-x">&nbsp;</label><label class="size-h">'+i.dialogBox.height+"</label></div>",n+='<input class="se-input-control _se_image_size_x" placeholder="auto"'+(l?' type="number" min="1"':'type="text"')+(l?' max="100"':"")+' /><label class="se-dialog-size-x"'+a+">"+(l?"%":"x")+'</label><input type="text" class="se-input-control _se_image_size_y" placeholder="auto"'+o+(l?' max="100"':"")+a+"/><label"+o+a+'><input type="checkbox" class="se-dialog-btn-check _se_image_check_proportion" checked/>&nbsp;'+i.dialogBox.proportion+'</label><button type="button" title="'+i.dialogBox.revertButton+'" aria-label="'+i.dialogBox.revertButton+'" class="se-btn se-dialog-btn-revert" style="float: right;">'+e.icons.revert+"</button></div>"}return n+='<div class="se-dialog-form se-dialog-form-footer"><label><input type="checkbox" class="se-dialog-btn-check _se_image_check_caption" />&nbsp;'+i.dialogBox.caption+'</label></div></div></div><div class="_se_tab_content _se_tab_content_url" style="display: none">'+e.context.anchor.forms.innerHTML+'</div><div class="se-dialog-footer"><div'+(t.imageAlignShow?"":' style="display: none"')+'><label><input type="radio" name="suneditor_image_radio" class="se-dialog-btn-radio" value="none" checked>'+i.dialogBox.basic+'</label><label><input type="radio" name="suneditor_image_radio" class="se-dialog-btn-radio" value="left">'+i.dialogBox.left+'</label><label><input type="radio" name="suneditor_image_radio" class="se-dialog-btn-radio" value="center">'+i.dialogBox.center+'</label><label><input type="radio" name="suneditor_image_radio" class="se-dialog-btn-radio" value="right">'+i.dialogBox.right+'</label></div><button type="submit" class="se-btn-primary" title="'+i.dialogBox.submitButton+'" aria-label="'+i.dialogBox.submitButton+'"><span>'+i.dialogBox.submitButton+"</span></button></div></form>",l.innerHTML=n,l},_fileInputChange:function(){this.imgInputFile.value?(this.imgUrlFile.setAttribute("disabled",!0),this.previewSrc.style.textDecoration="line-through"):(this.imgUrlFile.removeAttribute("disabled"),this.previewSrc.style.textDecoration="")},_removeSelectedFiles:function(e,t){this.value="",e&&(e.removeAttribute("disabled"),t.style.textDecoration="")},_openGallery:function(){this.callPlugin("imageGallery",this.plugins.imageGallery.open.bind(this,this.plugins.image._setUrlInput.bind(this.context.image)),null)},_setUrlInput:function(e){this.altText.value=e.alt,this._v_src._linkValue=this.previewSrc.textContent=this.imgUrlFile.value=e.getAttribute("data-value")||e.src,this.imgUrlFile.focus()},_onLinkPreview:function(e,t,i){let l=i.target.value.trim();e._linkValue=this.textContent=l?t&&-1===l.indexOf("://")&&0!==l.indexOf("#")?t+l:-1===l.indexOf("://")?"/"+l:l:""},fileTags:["img"],select:function(e){this.plugins.image.onModifyMode.call(this,e,this.plugins.resizing.call_controller_resize.call(this,e,"image"))},destroy:function(e){let t=e||this.context.image._element,i=this.util.getParentElement(t,this.util.isMediaComponent)||t,l=1*t.getAttribute("data-index");if("function"==typeof this.functions.onImageDeleteBefore&&!1===this.functions.onImageDeleteBefore(t,i,l,this))return;let n=i.previousElementSibling||i.nextElementSibling,o=i.parentNode;this.util.removeItem(i),this.plugins.image.init.call(this),this.controllersOff(),o!==this.context.element.wysiwyg&&this.util.removeItemAllParents(o,function(e){return 0===e.childNodes.length},null),this.focusEdge(n),this.plugins.fileManager.deleteInfo.call(this,"image",l,this.functions.onImageUpload),this.history.push(!1)},on:function(e){let t=this.context.image;e?t.imgInputFile&&this.options.imageMultipleFile&&t.imgInputFile.removeAttribute("multiple"):(t.inputX.value=t._origin_w=this.options.imageWidth===t._defaultSizeX?"":this.options.imageWidth,t.inputY.value=t._origin_h=this.options.imageHeight===t._defaultSizeY?"":this.options.imageHeight,t.imgInputFile&&this.options.imageMultipleFile&&t.imgInputFile.setAttribute("multiple","multiple")),this.plugins.anchor.on.call(this,t.anchorCtx,e)},open:function(){this.plugins.dialog.open.call(this,"image","image"===this.currentControllerName)},openTab:function(e){let t,i,l;let n=this.context.image.modal,o="init"===e?n.querySelector("._se_tab_link"):e.target;if(!/^BUTTON$/i.test(o.tagName))return!1;let a=o.getAttribute("data-tab-link"),r="_se_tab_content";for(t=0,i=n.getElementsByClassName(r);t<i.length;t++)i[t].style.display="none";for(t=0,l=n.getElementsByClassName("_se_tab_link");t<l.length;t++)this.util.removeClass(l[t],"active");return n.querySelector("."+r+"_"+a).style.display="block",this.util.addClass(o,"active"),"image"===a&&this.context.image.focusElement?this.context.image.focusElement.focus():"url"===a&&this.context.anchor.caller.image.urlInput.focus(),!1},submit:function(e){let t=this.context.image,i=this.plugins.image;e.preventDefault(),e.stopPropagation(),t._altText=t.altText.value,t._align=t.modal.querySelector('input[name="suneditor_image_radio"]:checked').value,t._captionChecked=t.captionCheckEl.checked,t._resizing&&(t._proportionChecked=t.proportion.checked);try{this.context.dialog.updateModal&&i.update_image.call(this,!1,!0,!1),t.imgInputFile&&t.imgInputFile.files.length>0?(this.showLoading(),i.submitAction.call(this,this.context.image.imgInputFile.files)):t.imgUrlFile&&t._v_src._linkValue.length>0&&(this.showLoading(),i.onRender_imgUrl.call(this,t._v_src._linkValue))}catch(e){throw this.closeLoading(),Error('[SUNEDITOR.image.submit.fail] cause : "'+e.message+'"')}finally{this.plugins.dialog.close.call(this)}return!1},submitAction:function(e){if(0===e.length)return;let t=0,i=[];for(let l=0,n=e.length;l<n;l++)/image/i.test(e[l].type)&&(i.push(e[l]),t+=e[l].size);let l=this.options.imageUploadSizeLimit;if(l>0){let e=0,i=this.context.image._infoList;for(let t=0,l=i.length;t<l;t++)e+=1*i[t].size;if(t+e>l){this.closeLoading();let i="[SUNEDITOR.imageUpload.fail] Size of uploadable total images: "+l/1e3+"KB";("function"!=typeof this.functions.onImageUploadError||this.functions.onImageUploadError(i,{limitSize:l,currentSize:e,uploadSize:t},this))&&this.functions.noticeOpen(i);return}}let n=this.context.image;n._uploadFileLength=i.length;let o={anchor:this.plugins.anchor.createAnchor.call(this,n.anchorCtx,!0),inputWidth:n.inputX.value,inputHeight:n.inputY.value,align:n._align,isUpdate:this.context.dialog.updateModal,alt:n._altText,element:n._element};if("function"==typeof this.functions.onImageUploadBefore){let e=this.functions.onImageUploadBefore(i,o,this,(function(e){e&&this._w.Array.isArray(e.result)?this.plugins.image.register.call(this,o,e):this.plugins.image.upload.call(this,o,e)}).bind(this));if(void 0===e)return;if(!e){this.closeLoading();return}this._w.Array.isArray(e)&&e.length>0&&(i=e)}this.plugins.image.upload.call(this,o,i)},error:function(e,t){if(this.closeLoading(),"function"!=typeof this.functions.onImageUploadError||this.functions.onImageUploadError(e,t,this))throw this.functions.noticeOpen(e),Error("[SUNEDITOR.plugin.image.error] response: "+e)},upload:function(e,t){if(!t){this.closeLoading();return}if("string"==typeof t){this.plugins.image.error.call(this,t,null);return}let i=this.options.imageUploadUrl,l=this.context.dialog.updateModal?1:t.length;if("string"==typeof i&&i.length>0){let n=new FormData;for(let e=0;e<l;e++)n.append("file-"+e,t[e]);this.plugins.fileManager.upload.call(this,i,this.options.imageUploadHeader,n,this.plugins.image.callBack_imgUpload.bind(this,e),this.functions.onImageUploadError)}else this.plugins.image.setup_reader.call(this,t,e.anchor,e.inputWidth,e.inputHeight,e.align,e.alt,l,e.isUpdate)},callBack_imgUpload:function(e,t){if("function"==typeof this.functions.imageUploadHandler)this.functions.imageUploadHandler(t,e,this);else{let i=JSON.parse(t.responseText);i.errorMessage?this.plugins.image.error.call(this,i.errorMessage,i):this.plugins.image.register.call(this,e,i)}},register:function(e,t){let i=t.result;for(let t=0,l=i.length,n;t<l;t++){if(n={name:i[t].name,size:i[t].size},e.isUpdate){this.plugins.image.update_src.call(this,i[t].url,e.element,n);break}this.plugins.image.create_image.call(this,i[t].url,e.anchor,e.inputWidth,e.inputHeight,e.align,n,e.alt)}this.closeLoading()},setup_reader:function(e,t,i,l,n,o,a,r){try{if(0===a){this.closeLoading(),console.warn("[SUNEDITOR.image.base64.fail] cause : No applicable files");return}this.context.image.base64RenderIndex=a;let s=this._w.FileReader,u=[a];this.context.image.inputX.value=i,this.context.image.inputY.value=l;for(let c=0,d,h;c<a;c++)d=new s,h=e[c],d.onload=(function(e,a,r,s,c){u[c]={result:e.result,file:s},0==--this.context.image.base64RenderIndex&&(this.plugins.image.onRender_imgBase64.call(this,a,u,r,t,i,l,n,o),this.closeLoading())}).bind(this,d,r,this.context.image._element,h,c),d.readAsDataURL(h)}catch(e){throw this.closeLoading(),Error('[SUNEDITOR.image.setup_reader.fail] cause : "'+e.message+'"')}},onRender_imgBase64:function(e,t,i,l,n,o,a,r){let s=this.plugins.image.update_src,u=this.plugins.image.create_image;for(let c=0,d=t.length;c<d;c++)e?(this.context.image._element.setAttribute("data-file-name",t[c].file.name),this.context.image._element.setAttribute("data-file-size",t[c].file.size),s.call(this,t[c].result,i,t[c].file)):u.call(this,t[c].result,l,n,o,a,t[c].file,r)},onRender_imgUrl:function(e){if(e||(e=this.context.image._v_src._linkValue),!e)return!1;let t=this.context.image;try{let i={name:e.split("/").pop(),size:0};this.context.dialog.updateModal?this.plugins.image.update_src.call(this,e,t._element,i):this.plugins.image.create_image.call(this,e,this.plugins.anchor.createAnchor.call(this,t.anchorCtx,!0),t.inputX.value,t.inputY.value,t._align,i,t._altText)}catch(e){throw Error('[SUNEDITOR.image.URLRendering.fail] cause : "'+e.message+'"')}finally{this.closeLoading()}},onRender_link:function(e,t){return t?(t.setAttribute("data-image-link","image"),e.setAttribute("data-image-link",t.href),t.appendChild(e),t):e},setInputSize:function(e,t){if(t&&32===t.keyCode){t.preventDefault();return}this.plugins.resizing._module_setInputSize.call(this,this.context.image,e)},setRatio:function(){this.plugins.resizing._module_setRatio.call(this,this.context.image)},checkFileInfo:function(){let e=this.plugins.image,t=this.context.image,i=(function(i){e.onModifyMode.call(this,i,null),e.openModify.call(this,!0),t.inputX.value=t._origin_w,t.inputY.value=t._origin_h;let l=this.util.getFormatElement(i);l&&(t._align=l.style.textAlign||l.style.float),this.util.isAnchor(i.parentNode)&&!t.anchorCtx.linkValue&&(t.anchorCtx.linkValue=" "),e.update_image.call(this,!0,!1,!0),e.init.call(this)}).bind(this);this.plugins.fileManager.checkInfo.call(this,"image",["img"],this.functions.onImageUpload,i,!0)},resetFileInfo:function(){this.plugins.fileManager.resetInfo.call(this,"image",this.functions.onImageUpload)},create_image:function(e,t,i,l,n,o,a){let r=this.plugins.image,s=this.context.image;this.context.resizing._resize_plugin="image";let u=this.util.createElement("IMG");u.src=e,u.alt=a,u.setAttribute("data-rotate","0"),t=r.onRender_link.call(this,u,t?t.cloneNode(!1):null),s._resizing&&u.setAttribute("data-proportion",s._proportionChecked);let c=this.plugins.component.set_cover.call(this,t),d=this.plugins.component.set_container.call(this,c,"se-image-container");s._captionChecked&&(s._caption=this.plugins.component.create_caption.call(this),c.appendChild(s._caption)),s._element=u,s._cover=c,s._container=d,r.applySize.call(this,i,l),r.setAlign.call(this,n,u,c,d),u.onload=r._image_create_onload.bind(this,u,s.svgDefaultSize,d),this.insertComponent(d,!0,!0,!this.options.mediaAutoSelect)&&this.plugins.fileManager.setInfo.call(this,"image",u,this.functions.onImageUpload,o,!0),this.context.resizing._resize_plugin=""},_image_create_onload:function(e,t,i){if(0===e.offsetWidth&&this.plugins.image.applySize.call(this,t,""),this.options.mediaAutoSelect)this.selectComponent(e,"image");else{let e=this.appendFormatTag(i,null);e&&this.setRange(e,0,e,0)}this.history.push(!1)},update_image:function(e,t,i){let l;let n=this.context.image,o=n._element,a=n._cover,r=n._container,s=!1;null===a&&(s=!0,o=n._element.cloneNode(!0),a=this.plugins.component.set_cover.call(this,o)),null===r?(o=(a=a.cloneNode(!0)).querySelector("img"),s=!0,r=this.plugins.component.set_container.call(this,a,"se-image-container")):s&&(r.innerHTML="",r.appendChild(a),n._cover=a,n._element=o,s=!1);let u=this.util.isNumber(n.inputX.value)?n.inputX.value+n.sizeUnit:n.inputX.value,c=this.util.isNumber(n.inputY.value)?n.inputY.value+n.sizeUnit:n.inputY.value;l=/%$/.test(o.style.width)?u!==r.style.width||c!==r.style.height:u!==o.style.width||c!==o.style.height,o.alt=n._altText;let d=!1;n._captionChecked?n._caption||(n._caption=this.plugins.component.create_caption.call(this),a.appendChild(n._caption),d=!0):n._caption&&(this.util.removeItem(n._caption),n._caption=null,d=!0);let h=null,p=this.plugins.anchor.createAnchor.call(this,n.anchorCtx,!0);if(p)n._linkElement!==p||s&&!r.contains(p)?(n._linkElement=p.cloneNode(!1),a.insertBefore(this.plugins.image.onRender_link.call(this,o,n._linkElement),n._caption),h=n._element):n._linkElement.setAttribute("data-image-link","image");else if(null!==n._linkElement){let e=o;if(e.setAttribute("data-image-link",""),a.contains(n._linkElement)){let t=e.cloneNode(!0);a.removeChild(n._linkElement),a.insertBefore(t,n._caption),n._element=o=t}}let g=null;if(s){if(g=this.util.isRangeFormatElement(n._element.parentNode)||this.util.isWysiwygDiv(n._element.parentNode)?n._element:this.util.isAnchor(n._element.parentNode)?n._element.parentNode:this.util.getFormatElement(n._element)||n._element,this.util.getParentElement(n._element,this.util.isNotCheckingNode))(g=h?p:n._element).parentNode.replaceChild(r,g);else if(this.util.isListCell(g)){let e=this.util.getParentElement(n._element,function(e){return e.parentNode===g});g.insertBefore(r,e),this.util.removeItem(n._element),this.util.removeEmptyNode(e,null,!0)}else if(this.util.isFormatElement(g)){let e=this.util.getParentElement(n._element,function(e){return e.parentNode===g});(g=this.util.splitElement(g,e)).parentNode.insertBefore(r,g),this.util.removeItem(n._element),this.util.removeEmptyNode(g,null,!0),0===g.children.length&&(g.innerHTML=this.util.htmlRemoveWhiteSpace(g.innerHTML))}else if(this.util.isFormatElement(g.parentNode)){let e=g.parentNode;e.parentNode.insertBefore(r,g.previousSibling?e.nextElementSibling:e),0===n.__updateTags.map(function(e){return g.contains(e)}).length&&this.util.removeItem(g)}else(g=this.util.isFigures(g.parentNode)?g.parentNode:g).parentNode.replaceChild(r,g);o=r.querySelector("img"),n._element=o,n._cover=a,n._container=r}h&&(s?(this.util.removeItem(h),0===this.util.getListChildren(p,function(e){return/IMG/i.test(e.tagName)}).length&&this.util.removeItem(p)):this.util.removeItem(p)),(d||!n._onlyPercentage&&l)&&!e&&(/\d+/.test(o.style.height)||this.context.resizing._rotateVertical&&n._captionChecked)&&(/%$/.test(n.inputX.value)||/%$/.test(n.inputY.value)?this.plugins.resizing.resetTransform.call(this,o):this.plugins.resizing.setTransformSize.call(this,o,this.util.getNumber(n.inputX.value,0),this.util.getNumber(n.inputY.value,0))),n._resizing&&(o.setAttribute("data-proportion",n._proportionChecked),l&&this.plugins.image.applySize.call(this)),this.plugins.image.setAlign.call(this,null,o,null,null),e&&this.plugins.fileManager.setInfo.call(this,"image",o,this.functions.onImageUpload,null,!0),t&&this.selectComponent(o,"image"),i||this.history.push(!1)},update_src:function(e,t,i){t.src=e,this._w.setTimeout(this.plugins.fileManager.setInfo.bind(this,"image",t,this.functions.onImageUpload,i,!0)),this.selectComponent(t,"image")},onModifyMode:function(e,t){let i,l;if(!e)return;let n=this.context.image;n._linkElement=n.anchorCtx.linkAnchor=this.util.isAnchor(e.parentNode)?e.parentNode:null,n._element=e,n._cover=this.util.getParentElement(e,"FIGURE"),n._container=this.util.getParentElement(e,this.util.isMediaComponent),n._caption=this.util.getChildElement(n._cover,"FIGCAPTION"),n._align=e.getAttribute("data-align")||e.style.float||"none",e.style.float="",this.plugins.anchor.setCtx(n._linkElement,n.anchorCtx),t&&(n._element_w=t.w,n._element_h=t.h,n._element_t=t.t,n._element_l=t.l);let o=n._element.getAttribute("data-size")||n._element.getAttribute("data-origin");o?(i=(o=o.split(","))[0],l=o[1]):t&&(i=t.w,l=t.h),n._origin_w=i||e.style.width||e.width||"",n._origin_h=l||e.style.height||e.height||""},openModify:function(e){let t=this.context.image;t.imgUrlFile&&(t._v_src._linkValue=t.previewSrc.textContent=t.imgUrlFile.value=t._element.src),t._altText=t.altText.value=t._element.alt,(t.modal.querySelector('input[name="suneditor_image_radio"][value="'+t._align+'"]')||t.modal.querySelector('input[name="suneditor_image_radio"][value="none"]')).checked=!0,t._align=t.modal.querySelector('input[name="suneditor_image_radio"]:checked').value,t._captionChecked=t.captionCheckEl.checked=!!t._caption,t._resizing&&this.plugins.resizing._module_setModifyInputSize.call(this,t,this.plugins.image),e||this.plugins.dialog.open.call(this,"image",!0)},applySize:function(e,t){let i=this.context.image;return(e||(e=i.inputX.value||this.options.imageWidth),t||(t=i.inputY.value||this.options.imageHeight),i._onlyPercentage&&e||/%$/.test(e))?(this.plugins.image.setPercentSize.call(this,e,t),!0):(e&&"auto"!==e||t&&"auto"!==t?this.plugins.image.setSize.call(this,e,t,!1):this.plugins.image.setAutoSize.call(this),!1)},sizeRevert:function(){this.plugins.resizing._module_sizeRevert.call(this,this.context.image)},setSize:function(e,t,i,l){let n=this.context.image,o=/^(rw|lw)$/.test(l)&&/\d+/.test(n._element.style.height);/^(th|bh)$/.test(l)&&/\d+/.test(n._element.style.width)||(n._element.style.width=this.util.isNumber(e)?e+n.sizeUnit:e,this.plugins.image.cancelPercentAttr.call(this)),o||(n._element.style.height=this.util.isNumber(t)?t+n.sizeUnit:/%$/.test(t)?"":t),"center"===n._align&&this.plugins.image.setAlign.call(this,null,null,null,null),i||n._element.removeAttribute("data-percentage"),this.plugins.resizing._module_saveCurrentSize.call(this,n)},setAutoSize:function(){let e=this.context.image;e._caption&&(e._caption.style.marginTop=""),this.plugins.resizing.resetTransform.call(this,e._element),this.plugins.image.cancelPercentAttr.call(this),e._element.style.maxWidth="",e._element.style.width="",e._element.style.height="",e._cover.style.width="",e._cover.style.height="",this.plugins.image.setAlign.call(this,null,null,null,null),e._element.setAttribute("data-percentage","auto,auto"),this.plugins.resizing._module_saveCurrentSize.call(this,e)},setOriginSize:function(){let e=this.context.image;e._element.removeAttribute("data-percentage"),this.plugins.resizing.resetTransform.call(this,e._element),this.plugins.image.cancelPercentAttr.call(this);let t=(e._element.getAttribute("data-origin")||"").split(","),i=t[0],l=t[1];t&&(e._onlyPercentage||/%$/.test(i)&&(/%$/.test(l)||!/\d/.test(l))?this.plugins.image.setPercentSize.call(this,i,l):this.plugins.image.setSize.call(this,i,l),this.plugins.resizing._module_saveCurrentSize.call(this,e))},setPercentSize:function(e,t){let i=this.context.image;t=!t||/%$/.test(t)||this.util.getNumber(t,0)?this.util.isNumber(t)?t+i.sizeUnit:t||"":this.util.isNumber(t)?t+"%":t;let l=/%$/.test(t);i._container.style.width=this.util.isNumber(e)?e+"%":e,i._container.style.height="",i._cover.style.width="100%",i._cover.style.height=l?t:"",i._element.style.width="100%",i._element.style.height=l?"":t,i._element.style.maxWidth="","center"===i._align&&this.plugins.image.setAlign.call(this,null,null,null,null),i._element.setAttribute("data-percentage",e+","+t),this.plugins.resizing.setCaptionPosition.call(this,i._element),this.plugins.resizing._module_saveCurrentSize.call(this,i)},cancelPercentAttr:function(){let e=this.context.image;e._cover.style.width="",e._cover.style.height="",e._container.style.width="",e._container.style.height="",this.util.removeClass(e._container,this.context.image._floatClassRegExp),this.util.addClass(e._container,"__se__float-"+e._align),"center"===e._align&&this.plugins.image.setAlign.call(this,null,null,null,null)},setAlign:function(e,t,i,l){let n=this.context.image;e||(e=n._align),t||(t=n._element),i||(i=n._cover),l||(l=n._container),/%$/.test(t.style.width)&&"center"===e?(l.style.minWidth="100%",i.style.width=l.style.width):(l.style.minWidth="",i.style.width=this.context.resizing._rotateVertical?t.style.height||t.offsetHeight:t.style.width&&"auto"!==t.style.width?t.style.width||"100%":""),this.util.hasClass(l,"__se__float-"+e)||(this.util.removeClass(l,n._floatClassRegExp),this.util.addClass(l,"__se__float-"+e)),t.setAttribute("data-align",e)},init:function(){let e=this.context.image;e.imgInputFile&&(e.imgInputFile.value=""),e.imgUrlFile&&(e._v_src._linkValue=e.previewSrc.textContent=e.imgUrlFile.value=""),e.imgInputFile&&e.imgUrlFile&&(e.imgUrlFile.removeAttribute("disabled"),e.previewSrc.style.textDecoration=""),e.altText.value="",e.modal.querySelector('input[name="suneditor_image_radio"][value="none"]').checked=!0,e.captionCheckEl.checked=!1,e._element=null,this.plugins.image.openTab.call(this,"init"),e._resizing&&(e.inputX.value=this.options.imageWidth===e._defaultSizeX?"":this.options.imageWidth,e.inputY.value=this.options.imageHeight===e._defaultSizeY?"":this.options.imageHeight,e.proportion.checked=!0,e._ratio=!1,e._ratioX=1,e._ratioY=1),this.plugins.anchor.init.call(this,e.anchorCtx)}},B={name:"video",display:"dialog",add:function(e){e.addModule([_(),k(),z(),E()]);let t=e.options,i=e.context,l=i.video={_infoList:[],_infoIndex:0,_uploadFileLength:0,focusElement:null,sizeUnit:t._videoSizeUnit,_align:"none",_floatClassRegExp:"__se__float\\-[a-z]+",_youtubeQuery:t.youtubeQuery,_vimeoQuery:t.vimeoQuery,_videoRatio:100*t.videoRatio+"%",_defaultRatio:100*t.videoRatio+"%",_linkValue:"",_element:null,_cover:null,_container:null,inputX:null,inputY:null,_element_w:1,_element_h:1,_element_l:0,_element_t:0,_defaultSizeX:"100%",_defaultSizeY:100*t.videoRatio+"%",_origin_w:"100%"===t.videoWidth?"":t.videoWidth,_origin_h:"56.25%"===t.videoHeight?"":t.videoHeight,_proportionChecked:!0,_resizing:t.videoResizing,_resizeDotHide:!t.videoHeightShow,_rotation:t.videoRotation,_alignHide:!t.videoAlignShow,_onlyPercentage:t.videoSizeOnlyPercentage,_ratio:!1,_ratioX:1,_ratioY:1,_captionShow:!1},n=this.setDialog(e);l.modal=n,l.videoInputFile=n.querySelector("._se_video_file"),l.videoUrlFile=n.querySelector(".se-input-url"),l.focusElement=l.videoUrlFile||l.videoInputFile,l.preview=n.querySelector(".se-link-preview"),n.querySelector("form").addEventListener("submit",this.submit.bind(e)),l.videoInputFile&&n.querySelector(".se-dialog-files-edge-button").addEventListener("click",this._removeSelectedFiles.bind(l.videoInputFile,l.videoUrlFile,l.preview)),l.videoInputFile&&l.videoUrlFile&&l.videoInputFile.addEventListener("change",this._fileInputChange.bind(l)),l.videoUrlFile&&l.videoUrlFile.addEventListener("input",this._onLinkPreview.bind(l.preview,l,t.linkProtocol)),l.proportion={},l.videoRatioOption={},l.inputX={},l.inputY={},t.videoResizing&&(l.proportion=n.querySelector("._se_video_check_proportion"),l.videoRatioOption=n.querySelector(".se-video-ratio"),l.inputX=n.querySelector("._se_video_size_x"),l.inputY=n.querySelector("._se_video_size_y"),l.inputX.value=t.videoWidth,l.inputY.value=t.videoHeight,l.inputX.addEventListener("keyup",this.setInputSize.bind(e,"x")),l.inputY.addEventListener("keyup",this.setInputSize.bind(e,"y")),l.inputX.addEventListener("change",this.setRatio.bind(e)),l.inputY.addEventListener("change",this.setRatio.bind(e)),l.proportion.addEventListener("change",this.setRatio.bind(e)),l.videoRatioOption.addEventListener("change",this.setVideoRatio.bind(e)),n.querySelector(".se-dialog-btn-revert").addEventListener("click",this.sizeRevert.bind(e))),i.dialog.modal.appendChild(n)},setDialog:function(e){let t=e.options,i=e.lang,l=e.util.createElement("DIV");l.className="se-dialog-content",l.style.display="none";let n='<form method="post" enctype="multipart/form-data"><div class="se-dialog-header"><button type="button" data-command="close" class="se-btn se-dialog-close" title="'+i.dialogBox.close+'" aria-label="'+i.dialogBox.close+'">'+e.icons.cancel+'</button><span class="se-modal-title">'+i.dialogBox.videoBox.title+'</span></div><div class="se-dialog-body">';if(t.videoFileInput&&(n+='<div class="se-dialog-form"><label>'+i.dialogBox.videoBox.file+'</label><div class="se-dialog-form-files"><input class="se-input-form _se_video_file" type="file" accept="'+t.videoAccept+'"'+(t.videoMultipleFile?' multiple="multiple"':"")+'/><button type="button" data-command="filesRemove" class="se-btn se-dialog-files-edge-button se-file-remove" title="'+i.controller.remove+'" aria-label="'+i.controller.remove+'">'+e.icons.cancel+"</button></div></div>"),t.videoUrlInput&&(n+='<div class="se-dialog-form"><label>'+i.dialogBox.videoBox.url+'</label><input class="se-input-form se-input-url" type="text" /><pre class="se-link-preview"></pre></div>'),t.videoResizing){let l=t.videoRatioList||[{name:"16:9",value:.5625},{name:"4:3",value:.75},{name:"21:9",value:.4285}],o=t.videoRatio,a=t.videoSizeOnlyPercentage,r=t.videoHeightShow?"":' style="display: none !important;"',s=t.videoRatioShow?"":' style="display: none !important;"',u=a||t.videoHeightShow||t.videoRatioShow?"":' style="display: none !important;"';n+='<div class="se-dialog-form"><div class="se-dialog-size-text"><label class="size-w">'+i.dialogBox.width+'</label><label class="se-dialog-size-x">&nbsp;</label><label class="size-h"'+r+">"+i.dialogBox.height+'</label><label class="size-h"'+s+">("+i.dialogBox.ratio+')</label></div><input class="se-input-control _se_video_size_x" placeholder="100%"'+(a?' type="number" min="1"':'type="text"')+(a?' max="100"':"")+'/><label class="se-dialog-size-x"'+u+">"+(a?"%":"x")+'</label><input class="se-input-control _se_video_size_y" placeholder="'+100*t.videoRatio+'%"'+(a?' type="number" min="1"':'type="text"')+(a?' max="100"':"")+r+'/><select class="se-input-select se-video-ratio" title="'+i.dialogBox.ratio+'" aria-label="'+i.dialogBox.ratio+'"'+s+">",r||(n+='<option value=""> - </option>');for(let e=0,t=l.length;e<t;e++)n+='<option value="'+l[e].value+'"'+(o.toString()===l[e].value.toString()?" selected":"")+">"+l[e].name+"</option>";n+='</select><button type="button" title="'+i.dialogBox.revertButton+'" aria-label="'+i.dialogBox.revertButton+'" class="se-btn se-dialog-btn-revert" style="float: right;">'+e.icons.revert+'</button></div><div class="se-dialog-form se-dialog-form-footer"'+(a?' style="display: none !important;"':"")+u+'><label><input type="checkbox" class="se-dialog-btn-check _se_video_check_proportion" checked/>&nbsp;'+i.dialogBox.proportion+"</label></div>"}return n+='</div><div class="se-dialog-footer"><div'+(t.videoAlignShow?"":' style="display: none"')+'><label><input type="radio" name="suneditor_video_radio" class="se-dialog-btn-radio" value="none" checked>'+i.dialogBox.basic+'</label><label><input type="radio" name="suneditor_video_radio" class="se-dialog-btn-radio" value="left">'+i.dialogBox.left+'</label><label><input type="radio" name="suneditor_video_radio" class="se-dialog-btn-radio" value="center">'+i.dialogBox.center+'</label><label><input type="radio" name="suneditor_video_radio" class="se-dialog-btn-radio" value="right">'+i.dialogBox.right+'</label></div><button type="submit" class="se-btn-primary" title="'+i.dialogBox.submitButton+'" aria-label="'+i.dialogBox.submitButton+'"><span>'+i.dialogBox.submitButton+"</span></button></div></form>",l.innerHTML=n,l},_fileInputChange:function(){this.videoInputFile.value?(this.videoUrlFile.setAttribute("disabled",!0),this.preview.style.textDecoration="line-through"):(this.videoUrlFile.removeAttribute("disabled"),this.preview.style.textDecoration="")},_removeSelectedFiles:function(e,t){this.value="",e&&(e.removeAttribute("disabled"),t.style.textDecoration="")},_onLinkPreview:function(e,t,i){let l=i.target.value.trim();/^<iframe.*\/iframe>$/.test(l)?(e._linkValue=l,this.textContent='<IFrame :src=".."></IFrame>'):e._linkValue=this.textContent=l?t&&-1===l.indexOf("://")&&0!==l.indexOf("#")?t+l:-1===l.indexOf("://")?"/"+l:l:""},_setTagAttrs:function(e){e.setAttribute("controls",!0);let t=this.options.videoTagAttrs;if(t)for(let i in t)this.util.hasOwn(t,i)&&e.setAttribute(i,t[i])},createVideoTag:function(){let e=this.util.createElement("VIDEO");return this.plugins.video._setTagAttrs.call(this,e),e},_setIframeAttrs:function(e){e.frameBorder="0",e.allowFullscreen=!0;let t=this.options.videoIframeAttrs;if(t)for(let i in t)this.util.hasOwn(t,i)&&e.setAttribute(i,t[i])},createIframeTag:function(){let e=this.util.createElement("IFRAME");return this.plugins.video._setIframeAttrs.call(this,e),e},fileTags:["iframe","video"],select:function(e){this.plugins.video.onModifyMode.call(this,e,this.plugins.resizing.call_controller_resize.call(this,e,"video"))},destroy:function(e){let t=e||this.context.video._element,i=this.context.video._container,l=1*t.getAttribute("data-index");if("function"==typeof this.functions.onVideoDeleteBefore&&!1===this.functions.onVideoDeleteBefore(t,i,l,this))return;let n=i.previousElementSibling||i.nextElementSibling,o=i.parentNode;this.util.removeItem(i),this.plugins.video.init.call(this),this.controllersOff(),o!==this.context.element.wysiwyg&&this.util.removeItemAllParents(o,function(e){return 0===e.childNodes.length},null),this.focusEdge(n),this.plugins.fileManager.deleteInfo.call(this,"video",l,this.functions.onVideoUpload),this.history.push(!1)},on:function(e){let t=this.context.video;e?t.videoInputFile&&this.options.videoMultipleFile&&t.videoInputFile.removeAttribute("multiple"):(t.inputX.value=t._origin_w=this.options.videoWidth===t._defaultSizeX?"":this.options.videoWidth,t.inputY.value=t._origin_h=this.options.videoHeight===t._defaultSizeY?"":this.options.videoHeight,t.proportion.disabled=!0,t.videoInputFile&&this.options.videoMultipleFile&&t.videoInputFile.setAttribute("multiple","multiple")),t._resizing&&this.plugins.video.setVideoRatioSelect.call(this,t._origin_h||t._defaultRatio)},open:function(){this.plugins.dialog.open.call(this,"video","video"===this.currentControllerName)},setVideoRatio:function(e){let t=this.context.video,i=e.target.options[e.target.selectedIndex].value;t._defaultSizeY=t._videoRatio=i?100*i+"%":t._defaultSizeY,t.inputY.placeholder=i?100*i+"%":"",t.inputY.value=""},setInputSize:function(e,t){if(t&&32===t.keyCode){t.preventDefault();return}let i=this.context.video;this.plugins.resizing._module_setInputSize.call(this,i,e),"y"===e&&this.plugins.video.setVideoRatioSelect.call(this,t.target.value||i._defaultRatio)},setRatio:function(){this.plugins.resizing._module_setRatio.call(this,this.context.video)},submit:function(e){let t=this.context.video,i=this.plugins.video;e.preventDefault(),e.stopPropagation(),t._align=t.modal.querySelector('input[name="suneditor_video_radio"]:checked').value;try{t.videoInputFile&&t.videoInputFile.files.length>0?(this.showLoading(),i.submitAction.call(this,this.context.video.videoInputFile.files)):t.videoUrlFile&&t._linkValue.length>0&&(this.showLoading(),i.setup_url.call(this,t._linkValue))}catch(e){throw this.closeLoading(),Error('[SUNEDITOR.video.submit.fail] cause : "'+e.message+'"')}finally{this.plugins.dialog.close.call(this)}return!1},submitAction:function(e){if(0===e.length)return;let t=0,i=[];for(let l=0,n=e.length;l<n;l++)/video/i.test(e[l].type)&&(i.push(e[l]),t+=e[l].size);let l=this.options.videoUploadSizeLimit;if(l>0){let e=0,i=this.context.video._infoList;for(let t=0,l=i.length;t<l;t++)e+=1*i[t].size;if(t+e>l){this.closeLoading();let i="[SUNEDITOR.videoUpload.fail] Size of uploadable total videos: "+l/1e3+"KB";("function"!=typeof this.functions.onVideoUploadError||this.functions.onVideoUploadError(i,{limitSize:l,currentSize:e,uploadSize:t},this))&&this.functions.noticeOpen(i);return}}let n=this.context.video;n._uploadFileLength=i.length;let o={inputWidth:n.inputX.value,inputHeight:n.inputY.value,align:n._align,isUpdate:this.context.dialog.updateModal,element:n._element};if("function"==typeof this.functions.onVideoUploadBefore){let e=this.functions.onVideoUploadBefore(i,o,this,(function(e){e&&this._w.Array.isArray(e.result)?this.plugins.video.register.call(this,o,e):this.plugins.video.upload.call(this,o,e)}).bind(this));if(void 0===e)return;if(!e){this.closeLoading();return}"object"==typeof e&&e.length>0&&(i=e)}this.plugins.video.upload.call(this,o,i)},error:function(e,t){if(this.closeLoading(),"function"!=typeof this.functions.onVideoUploadError||this.functions.onVideoUploadError(e,t,this))throw this.functions.noticeOpen(e),Error("[SUNEDITOR.plugin.video.error] response: "+e)},upload:function(e,t){if(!t){this.closeLoading();return}if("string"==typeof t){this.plugins.video.error.call(this,t,null);return}let i=this.options.videoUploadUrl,l=this.context.dialog.updateModal?1:t.length;if("string"==typeof i&&i.length>0){let n=new FormData;for(let e=0;e<l;e++)n.append("file-"+e,t[e]);this.plugins.fileManager.upload.call(this,i,this.options.videoUploadHeader,n,this.plugins.video.callBack_videoUpload.bind(this,e),this.functions.onVideoUploadError)}else throw Error('[SUNEDITOR.videoUpload.fail] cause : There is no "videoUploadUrl" option.')},callBack_videoUpload:function(e,t){if("function"==typeof this.functions.videoUploadHandler)this.functions.videoUploadHandler(t,e,this);else{let i=JSON.parse(t.responseText);i.errorMessage?this.plugins.video.error.call(this,i.errorMessage,i):this.plugins.video.register.call(this,e,i)}},register:function(e,t){let i=t.result,l=this.plugins.video.createVideoTag.call(this);for(let t=0,n=i.length,o;t<n;t++)o={name:i[t].name,size:i[t].size},this.plugins.video.create_video.call(this,e.isUpdate?e.element:l.cloneNode(!1),i[t].url,e.inputWidth,e.inputHeight,e.align,o,e.isUpdate);this.closeLoading()},setup_url:function(e){try{let t=this.context.video;if(e||(e=t._linkValue),!e||/^<iframe.*\/iframe>$/.test(e)&&(e=new this._w.DOMParser().parseFromString(e,"text/html").querySelector("iframe").src,0===e.length))return!1;if(/youtu\.?be/.test(e)){if(/^http/.test(e)||(e="https://"+e),e=e.replace("watch?v=",""),/^\/\/.+\/embed\//.test(e)||(e=e.replace(e.match(/\/\/.+\//)[0],"//www.youtube.com/embed/").replace("&","?&")),t._youtubeQuery.length>0){if(/\?/.test(e)){let i=e.split("?");e=i[0]+"?"+t._youtubeQuery+"&"+i[1]}else e+="?"+t._youtubeQuery}}else if(/vimeo\.com/.test(e)&&(e.endsWith("/")&&(e=e.slice(0,-1)),e="https://player.vimeo.com/video/"+e.slice(e.lastIndexOf("/")+1),t._vimeoQuery.length>0)){if(/\?/.test(e)){let i=e.split("?");e=i[0]+"?"+t._vimeoQuery+"&"+i[1]}else e+="?"+t._vimeoQuery}this.plugins.video.create_video.call(this,this.plugins.video[/embed|iframe|player|\/e\/|\.php|\.html?/.test(e)||/vimeo\.com/.test(e)?"createIframeTag":"createVideoTag"].call(this),e,t.inputX.value,t.inputY.value,t._align,null,this.context.dialog.updateModal)}catch(e){throw Error('[SUNEDITOR.video.upload.fail] cause : "'+e.message+'"')}finally{this.closeLoading()}},create_video:function(e,t,i,l,n,o,a){this.context.resizing._resize_plugin="video";let r=this.context.video,s=null,u=null,c=!1;if(a){if((e=r._element).src!==t){c=!0;let i=/youtu\.?be/.test(t),l=/vimeo\.com/.test(t);if((i||l)&&!/^iframe$/i.test(e.nodeName)){let i=this.plugins.video.createIframeTag.call(this);i.src=t,e.parentNode.replaceChild(i,e),r._element=e=i}else if(i||l||/^video$/i.test(e.nodeName))e.src=t;else{let i=this.plugins.video.createVideoTag.call(this);i.src=t,e.parentNode.replaceChild(i,e),r._element=e=i}}u=r._container,s=this.util.getParentElement(e,"FIGURE")}else c=!0,e.src=t,r._element=e,s=this.plugins.component.set_cover.call(this,e),u=this.plugins.component.set_container.call(this,s,"se-video-container");r._cover=s,r._container=u;let d=this.plugins.resizing._module_getSizeX.call(this,r)!==(i||r._defaultSizeX)||this.plugins.resizing._module_getSizeY.call(this,r)!==(l||r._videoRatio),h=!a||d;r._resizing&&(this.context.video._proportionChecked=r.proportion.checked,e.setAttribute("data-proportion",r._proportionChecked));let p=!1;h&&(p=this.plugins.video.applySize.call(this)),p&&"center"===n||this.plugins.video.setAlign.call(this,null,e,s,u);let g=!0;if(a)r._resizing&&this.context.resizing._rotateVertical&&h&&this.plugins.resizing.setTransformSize.call(this,e,null,null);else if(g=this.insertComponent(u,!1,!0,!this.options.mediaAutoSelect),!this.options.mediaAutoSelect){let e=this.appendFormatTag(u,null);e&&this.setRange(e,0,e,0)}g&&(c&&this.plugins.fileManager.setInfo.call(this,"video",e,this.functions.onVideoUpload,o,!0),a&&(this.selectComponent(e,"video"),this.history.push(!1))),this.context.resizing._resize_plugin=""},_update_videoCover:function(e){if(!e)return;let t=this.context.video;/^video$/i.test(e.nodeName)?this.plugins.video._setTagAttrs.call(this,e):this.plugins.video._setIframeAttrs.call(this,e);let i=this.util.isRangeFormatElement(e.parentNode)||this.util.isWysiwygDiv(e.parentNode)?e:this.util.getFormatElement(e)||e,l=e;t._element=e=e.cloneNode(!0);let n=t._cover=this.plugins.component.set_cover.call(this,e),o=t._container=this.plugins.component.set_container.call(this,n,"se-video-container");try{let a=i.querySelector("figcaption"),r=null;a&&((r=this.util.createElement("DIV")).innerHTML=a.innerHTML,this.util.removeItem(a));let s=(e.getAttribute("data-size")||e.getAttribute("data-origin")||"").split(",");this.plugins.video.applySize.call(this,s[0]||l.style.width||l.width||"",s[1]||l.style.height||l.height||"");let u=this.util.getFormatElement(l);if(u&&(t._align=u.style.textAlign||u.style.float),this.plugins.video.setAlign.call(this,null,e,n,o),this.util.getParentElement(l,this.util.isNotCheckingNode))l.parentNode.replaceChild(o,l);else if(this.util.isListCell(i)){let e=this.util.getParentElement(l,function(e){return e.parentNode===i});i.insertBefore(o,e),this.util.removeItem(l),this.util.removeEmptyNode(e,null,!0)}else if(this.util.isFormatElement(i)){let e=this.util.getParentElement(l,function(e){return e.parentNode===i});(i=this.util.splitElement(i,e)).parentNode.insertBefore(o,i),this.util.removeItem(l),this.util.removeEmptyNode(i,null,!0),0===i.children.length&&(i.innerHTML=this.util.htmlRemoveWhiteSpace(i.innerHTML))}else i.parentNode.replaceChild(o,i);r&&i.parentNode.insertBefore(r,o.nextElementSibling)}catch(e){console.warn("[SUNEDITOR.video.error] Maybe the video tag is nested.",e)}this.plugins.fileManager.setInfo.call(this,"video",e,this.functions.onVideoUpload,null,!0),this.plugins.video.init.call(this)},onModifyMode:function(e,t){let i,l;let n=this.context.video;n._element=e,n._cover=this.util.getParentElement(e,"FIGURE"),n._container=this.util.getParentElement(e,this.util.isMediaComponent),n._align=e.style.float||e.getAttribute("data-align")||"none",e.style.float="",t&&(n._element_w=t.w,n._element_h=t.h,n._element_t=t.t,n._element_l=t.l);let o=n._element.getAttribute("data-size")||n._element.getAttribute("data-origin");o?(i=(o=o.split(","))[0],l=o[1]):t&&(i=t.w,l=t.h),n._origin_w=i||e.style.width||e.width||"",n._origin_h=l||e.style.height||e.height||""},openModify:function(e){let t=this.context.video;if(t.videoUrlFile&&(t._linkValue=t.preview.textContent=t.videoUrlFile.value=t._element.src||(t._element.querySelector("source")||"").src||""),(t.modal.querySelector('input[name="suneditor_video_radio"][value="'+t._align+'"]')||t.modal.querySelector('input[name="suneditor_video_radio"][value="none"]')).checked=!0,t._resizing){this.plugins.resizing._module_setModifyInputSize.call(this,t,this.plugins.video);let e=t._videoRatio=this.plugins.resizing._module_getSizeY.call(this,t);this.plugins.video.setVideoRatioSelect.call(this,e)||(t.inputY.value=t._onlyPercentage?this.util.getNumber(e,2):e)}e||this.plugins.dialog.open.call(this,"video",!0)},setVideoRatioSelect:function(e){let t=!1,i=this.context.video,l=i.videoRatioOption.options;/%$/.test(e)||i._onlyPercentage?e=this.util.getNumber(e,2)/100+"":(!this.util.isNumber(e)||1*e>=1)&&(e=""),i.inputY.placeholder="";for(let n=0,o=l.length;n<o;n++)l[n].value===e?(t=l[n].selected=!0,i.inputY.placeholder=e?100*e+"%":""):l[n].selected=!1;return t},checkFileInfo:function(){this.plugins.fileManager.checkInfo.call(this,"video",["iframe","video"],this.functions.onVideoUpload,this.plugins.video._update_videoCover.bind(this),!0)},resetFileInfo:function(){this.plugins.fileManager.resetInfo.call(this,"video",this.functions.onVideoUpload)},applySize:function(e,t){let i=this.context.video;return(e||(e=i.inputX.value||this.options.videoWidth),t||(t=i.inputY.value||this.options.videoHeight),i._onlyPercentage||/%$/.test(e)||!e)?(this.plugins.video.setPercentSize.call(this,e||"100%",t||(/%$/.test(i._videoRatio)?i._videoRatio:i._defaultRatio)),!0):(e&&"auto"!==e||t&&"auto"!==t?this.plugins.video.setSize.call(this,e,t||i._videoRatio||i._defaultRatio,!1):this.plugins.video.setAutoSize.call(this),!1)},sizeRevert:function(){this.plugins.resizing._module_sizeRevert.call(this,this.context.video)},setSize:function(e,t,i,l){let n=this.context.video,o=/^(rw|lw)$/.test(l),a=/^(th|bh)$/.test(l);a||(e=this.util.getNumber(e,0)),o||(t=this.util.isNumber(t)?t+n.sizeUnit:t||""),e=e?e+n.sizeUnit:"",a||(n._element.style.width=e),o||(n._cover.style.paddingBottom=n._cover.style.height=t),a||/%$/.test(e)||(n._cover.style.width=e,n._container.style.width=""),o||/%$/.test(t)?n._element.style.height="":n._element.style.height=t,i||n._element.removeAttribute("data-percentage"),this.plugins.resizing._module_saveCurrentSize.call(this,n)},setAutoSize:function(){this.plugins.video.setPercentSize.call(this,100,this.context.video._defaultRatio)},setOriginSize:function(e){let t=this.context.video;t._element.removeAttribute("data-percentage"),this.plugins.resizing.resetTransform.call(this,t._element),this.plugins.video.cancelPercentAttr.call(this);let i=((e?t._element.getAttribute("data-size"):"")||t._element.getAttribute("data-origin")||"").split(",");if(i){let e=i[0],l=i[1];t._onlyPercentage||/%$/.test(e)&&(/%$/.test(l)||!/\d/.test(l))?this.plugins.video.setPercentSize.call(this,e,l):this.plugins.video.setSize.call(this,e,l),this.plugins.resizing._module_saveCurrentSize.call(this,t)}},setPercentSize:function(e,t){let i=this.context.video;t=!t||/%$/.test(t)||this.util.getNumber(t,0)?this.util.isNumber(t)?t+i.sizeUnit:t||i._defaultRatio:this.util.isNumber(t)?t+"%":t,i._container.style.width=this.util.isNumber(e)?e+"%":e,i._container.style.height="",i._cover.style.width="100%",i._cover.style.height=t,i._cover.style.paddingBottom=t,i._element.style.width="100%",i._element.style.height="100%",i._element.style.maxWidth="","center"===i._align&&this.plugins.video.setAlign.call(this,null,null,null,null),i._element.setAttribute("data-percentage",e+","+t),this.plugins.resizing._module_saveCurrentSize.call(this,i)},cancelPercentAttr:function(){let e=this.context.video;e._cover.style.width="",e._cover.style.height="",e._cover.style.paddingBottom="",e._container.style.width="",e._container.style.height="",this.util.removeClass(e._container,this.context.video._floatClassRegExp),this.util.addClass(e._container,"__se__float-"+e._align),"center"===e._align&&this.plugins.video.setAlign.call(this,null,null,null,null)},setAlign:function(e,t,i,l){let n=this.context.video;e||(e=n._align),t||(t=n._element),i||(i=n._cover),l||(l=n._container),/%$/.test(t.style.width)&&"center"===e?(l.style.minWidth="100%",i.style.width=l.style.width,i.style.height=i.style.height,i.style.paddingBottom=/%$/.test(i.style.height)?this.util.getNumber(this.util.getNumber(i.style.height,2)/100*this.util.getNumber(i.style.width,2),2)+"%":i.style.height):(l.style.minWidth="",i.style.width=this.context.resizing._rotateVertical?t.style.height||t.offsetHeight:t.style.width||"100%",i.style.paddingBottom=i.style.height),this.util.hasClass(l,"__se__float-"+e)||(this.util.removeClass(l,n._floatClassRegExp),this.util.addClass(l,"__se__float-"+e)),t.setAttribute("data-align",e)},init:function(){let e=this.context.video;e.videoInputFile&&(e.videoInputFile.value=""),e.videoUrlFile&&(e._linkValue=e.preview.textContent=e.videoUrlFile.value=""),e.videoInputFile&&e.videoUrlFile&&(e.videoUrlFile.removeAttribute("disabled"),e.preview.style.textDecoration=""),e._origin_w=this.options.videoWidth,e._origin_h=this.options.videoHeight,e.modal.querySelector('input[name="suneditor_video_radio"][value="none"]').checked=!0,e._resizing&&(e.inputX.value=this.options.videoWidth===e._defaultSizeX?"":this.options.videoWidth,e.inputY.value=this.options.videoHeight===e._defaultSizeY?"":this.options.videoHeight,e.proportion.checked=!0,e.proportion.disabled=!0,this.plugins.video.setVideoRatioSelect.call(this,e._defaultRatio))}},N={name:"audio",display:"dialog",add:function(e){e.addModule([_(),k(),E()]);let t=e.context,i=t.audio={_infoList:[],_infoIndex:0,_uploadFileLength:0,focusElement:null,targetSelect:null,_origin_w:e.options.audioWidth,_origin_h:e.options.audioHeight,_linkValue:"",_element:null,_cover:null,_container:null},l=this.setDialog(e);i.modal=l,i.audioInputFile=l.querySelector("._se_audio_files"),i.audioUrlFile=l.querySelector(".se-input-url"),i.focusElement=i.audioInputFile||i.audioUrlFile,i.preview=l.querySelector(".se-link-preview");let n=this.setController(e);i.controller=n,l.querySelector("form").addEventListener("submit",this.submit.bind(e)),i.audioInputFile&&l.querySelector(".se-dialog-files-edge-button").addEventListener("click",this._removeSelectedFiles.bind(i.audioInputFile,i.audioUrlFile,i.preview)),i.audioInputFile&&i.audioUrlFile&&i.audioInputFile.addEventListener("change",this._fileInputChange.bind(i)),n.addEventListener("click",this.onClick_controller.bind(e)),i.audioUrlFile&&i.audioUrlFile.addEventListener("input",this._onLinkPreview.bind(i.preview,i,e.options.linkProtocol)),t.dialog.modal.appendChild(l),t.element.relative.appendChild(n),l=null,n=null},setDialog:function(e){let t=e.options,i=e.lang,l=e.util.createElement("DIV");l.className="se-dialog-content",l.style.display="none";let n='<form method="post" enctype="multipart/form-data"><div class="se-dialog-header"><button type="button" data-command="close" class="se-btn se-dialog-close" title="'+i.dialogBox.close+'" aria-label="'+i.dialogBox.close+'">'+e.icons.cancel+'</button><span class="se-modal-title">'+i.dialogBox.audioBox.title+'</span></div><div class="se-dialog-body">';return t.audioFileInput&&(n+='<div class="se-dialog-form"><label>'+i.dialogBox.audioBox.file+'</label><div class="se-dialog-form-files"><input class="se-input-form _se_audio_files" type="file" accept="'+t.audioAccept+'"'+(t.audioMultipleFile?' multiple="multiple"':"")+'/><button type="button" data-command="filesRemove" class="se-btn se-dialog-files-edge-button se-file-remove" title="'+i.controller.remove+'" aria-label="'+i.controller.remove+'">'+e.icons.cancel+"</button></div></div>"),t.audioUrlInput&&(n+='<div class="se-dialog-form"><label>'+i.dialogBox.audioBox.url+'</label><input class="se-input-form se-input-url" type="text" /><pre class="se-link-preview"></pre></div>'),n+='</div><div class="se-dialog-footer"><button type="submit" class="se-btn-primary" title="'+i.dialogBox.submitButton+'" aria-label="'+i.dialogBox.submitButton+'"><span>'+i.dialogBox.submitButton+"</span></button></div></form>",l.innerHTML=n,l},setController:function(e){let t=e.lang,i=e.icons,l=e.util.createElement("DIV");return l.className="se-controller se-controller-link",l.innerHTML='<div class="se-arrow se-arrow-up"></div><div class="link-content"><div class="se-btn-group"><button type="button" data-command="update" tabindex="-1" class="se-tooltip">'+i.edit+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.edit+'</span></span></button><button type="button" data-command="delete" tabindex="-1" class="se-tooltip">'+i.delete+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.remove+"</span></span></button></div></div>",l},_fileInputChange:function(){this.audioInputFile.value?(this.audioUrlFile.setAttribute("disabled",!0),this.preview.style.textDecoration="line-through"):(this.audioUrlFile.removeAttribute("disabled"),this.preview.style.textDecoration="")},_removeSelectedFiles:function(e,t){this.value="",e&&(e.removeAttribute("disabled"),t.style.textDecoration="")},_createAudioTag:function(){let e=this.util.createElement("AUDIO");this.plugins.audio._setTagAttrs.call(this,e);let t=this.context.audio._origin_w,i=this.context.audio._origin_h;return e.setAttribute("origin-size",t+","+i),e.style.cssText=(t?"width:"+t+"; ":"")+(i?"height:"+i+";":""),e},_setTagAttrs:function(e){e.setAttribute("controls",!0);let t=this.options.audioTagAttrs;if(t)for(let i in t)this.util.hasOwn(t,i)&&e.setAttribute(i,t[i])},_onLinkPreview:function(e,t,i){let l=i.target.value.trim();e._linkValue=this.textContent=l?t&&-1===l.indexOf("://")&&0!==l.indexOf("#")?t+l:-1===l.indexOf("://")?"/"+l:l:""},fileTags:["audio"],select:function(e){this.plugins.audio.onModifyMode.call(this,e)},destroy:function(e){e=e||this.context.audio._element;let t=this.util.getParentElement(e,this.util.isComponent)||e,i=1*e.getAttribute("data-index");if("function"==typeof this.functions.onAudioDeleteBefore&&!1===this.functions.onAudioDeleteBefore(e,t,i,this))return;let l=t.previousElementSibling||t.nextElementSibling,n=t.parentNode;this.util.removeItem(t),this.plugins.audio.init.call(this),this.controllersOff(),n!==this.context.element.wysiwyg&&this.util.removeItemAllParents(n,function(e){return 0===e.childNodes.length},null),this.focusEdge(l),this.plugins.fileManager.deleteInfo.call(this,"audio",i,this.functions.onAudioUpload),this.history.push(!1)},checkFileInfo:function(){this.plugins.fileManager.checkInfo.call(this,"audio",["audio"],this.functions.onAudioUpload,this.plugins.audio.updateCover.bind(this),!1)},resetFileInfo:function(){this.plugins.fileManager.resetInfo.call(this,"audio",this.functions.onAudioUpload)},on:function(e){let t=this.context.audio;e?(t._element&&(this.context.dialog.updateModal=!0,t._linkValue=t.preview.textContent=t.audioUrlFile.value=t._element.src),t.audioInputFile&&this.options.audioMultipleFile&&t.audioInputFile.removeAttribute("multiple")):(this.plugins.audio.init.call(this),t.audioInputFile&&this.options.audioMultipleFile&&t.audioInputFile.setAttribute("multiple","multiple"))},open:function(){this.plugins.dialog.open.call(this,"audio","audio"===this.currentControllerName)},submit:function(e){let t=this.context.audio;e.preventDefault(),e.stopPropagation();try{t.audioInputFile&&t.audioInputFile.files.length>0?(this.showLoading(),this.plugins.audio.submitAction.call(this,t.audioInputFile.files)):t.audioUrlFile&&t._linkValue.length>0&&(this.showLoading(),this.plugins.audio.setupUrl.call(this,t._linkValue))}catch(e){throw this.closeLoading(),Error('[SUNEDITOR.audio.submit.fail] cause : "'+e.message+'"')}finally{this.plugins.dialog.close.call(this)}return!1},submitAction:function(e){if(0===e.length)return;let t=0,i=[];for(let l=0,n=e.length;l<n;l++)/audio/i.test(e[l].type)&&(i.push(e[l]),t+=e[l].size);let l=this.options.audioUploadSizeLimit;if(l>0){let e=0,i=this.context.audio._infoList;for(let t=0,l=i.length;t<l;t++)e+=1*i[t].size;if(t+e>l){this.closeLoading();let i="[SUNEDITOR.audioUpload.fail] Size of uploadable total audios: "+l/1e3+"KB";("function"!=typeof this.functions.onAudioUploadError||this.functions.onAudioUploadError(i,{limitSize:l,currentSize:e,uploadSize:t},this))&&this.functions.noticeOpen(i);return}}let n=this.context.audio;n._uploadFileLength=i.length;let o={isUpdate:this.context.dialog.updateModal,element:n._element};if("function"==typeof this.functions.onAudioUploadBefore){let e=this.functions.onAudioUploadBefore(i,o,this,(function(e){e&&this._w.Array.isArray(e.result)?this.plugins.audio.register.call(this,o,e):this.plugins.audio.upload.call(this,o,e)}).bind(this));if(void 0===e)return;if(!e){this.closeLoading();return}"object"==typeof e&&e.length>0&&(i=e)}this.plugins.audio.upload.call(this,o,i)},error:function(e,t){if(this.closeLoading(),"function"!=typeof this.functions.onAudioUploadError||this.functions.onAudioUploadError(e,t,this))throw this.functions.noticeOpen(e),Error("[SUNEDITOR.plugin.audio.exception] response: "+e)},upload:function(e,t){if(!t){this.closeLoading();return}if("string"==typeof t){this.plugins.audio.error.call(this,t,null);return}let i=this.options.audioUploadUrl,l=this.context.dialog.updateModal?1:t.length,n=new FormData;for(let e=0;e<l;e++)n.append("file-"+e,t[e]);this.plugins.fileManager.upload.call(this,i,this.options.audioUploadHeader,n,this.plugins.audio.callBack_upload.bind(this,e),this.functions.onAudioUploadError)},callBack_upload:function(e,t){if("function"==typeof this.functions.audioUploadHandler)this.functions.audioUploadHandler(t,e,this);else{let i=JSON.parse(t.responseText);i.errorMessage?this.plugins.audio.error.call(this,i.errorMessage,i):this.plugins.audio.register.call(this,e,i)}},register:function(e,t){let i=t.result;for(let t=0,l=i.length,n,o;t<l;t++)o=e.isUpdate?e.element:this.plugins.audio._createAudioTag.call(this),n={name:i[t].name,size:i[t].size},this.plugins.audio.create_audio.call(this,o,i[t].url,n,e.isUpdate);this.closeLoading()},setupUrl:function(e){try{if(0===e.length)return!1;this.plugins.audio.create_audio.call(this,this.plugins.audio._createAudioTag.call(this),e,null,this.context.dialog.updateModal)}catch(e){throw Error('[SUNEDITOR.audio.audio.fail] cause : "'+e.message+'"')}finally{this.closeLoading()}},create_audio:function(e,t,i,l){let n=this.context.audio;if(l){if(n._element&&(e=n._element),e&&e.src!==t)e.src=t,this.selectComponent(e,"audio");else{this.selectComponent(e,"audio");return}}else{e.src=t;let i=this.plugins.component.set_cover.call(this,e),l=this.plugins.component.set_container.call(this,i,"");if(!this.insertComponent(l,!1,!0,!this.options.mediaAutoSelect)){this.focus();return}if(!this.options.mediaAutoSelect){let e=this.appendFormatTag(l,null);e&&this.setRange(e,0,e,0)}}this.plugins.fileManager.setInfo.call(this,"audio",e,this.functions.onAudioUpload,i,!1),l&&this.history.push(!1)},updateCover:function(e){let t=this.context.audio;this.plugins.audio._setTagAttrs.call(this,e);let i=this.util.isRangeFormatElement(e.parentNode)||this.util.isWysiwygDiv(e.parentNode)?e:this.util.getFormatElement(e)||e,l=e;t._element=e=e.cloneNode(!1);let n=this.plugins.component.set_cover.call(this,e),o=this.plugins.component.set_container.call(this,n,"se-audio-container");try{if(this.util.getParentElement(l,this.util.isNotCheckingNode))l.parentNode.replaceChild(o,l);else if(this.util.isListCell(i)){let e=this.util.getParentElement(l,function(e){return e.parentNode===i});i.insertBefore(o,e),this.util.removeItem(l),this.util.removeEmptyNode(e,null,!0)}else if(this.util.isFormatElement(i)){let e=this.util.getParentElement(l,function(e){return e.parentNode===i});(i=this.util.splitElement(i,e)).parentNode.insertBefore(o,i),this.util.removeItem(l),this.util.removeEmptyNode(i,null,!0),0===i.children.length&&(i.innerHTML=this.util.htmlRemoveWhiteSpace(i.innerHTML))}else i.parentNode.replaceChild(o,i)}catch(e){console.warn("[SUNEDITOR.audio.error] Maybe the audio tag is nested.",e)}this.plugins.fileManager.setInfo.call(this,"audio",e,this.functions.onAudioUpload,null,!1),this.plugins.audio.init.call(this)},onModifyMode:function(e){let t=this.context.audio;this.setControllerPosition(t.controller,e,"bottom",{left:0,top:0}),this.controllersOn(t.controller,e,this.plugins.audio.onControllerOff.bind(this,e),"audio"),this.util.addClass(e,"active"),t._element=e,t._cover=this.util.getParentElement(e,"FIGURE"),t._container=this.util.getParentElement(e,this.util.isComponent)},openModify:function(e){if(this.context.audio.audioUrlFile){let e=this.context.audio;e._linkValue=e.preview.textContent=e.audioUrlFile.value=e._element.src}e||this.plugins.dialog.open.call(this,"audio",!0)},onClick_controller:function(e){e.stopPropagation();let t=e.target.getAttribute("data-command");t&&(e.preventDefault(),/update/.test(t)?this.plugins.audio.openModify.call(this,!1):this.plugins.audio.destroy.call(this,this.context.audio._element),this.controllersOff())},onControllerOff:function(e){this.util.removeClass(e,"active"),this.context.audio.controller.style.display="none"},init:function(){if(this.context.dialog.updateModal)return;let e=this.context.audio;e.audioInputFile&&(e.audioInputFile.value=""),e.audioUrlFile&&(e._linkValue=e.preview.textContent=e.audioUrlFile.value=""),e.audioInputFile&&e.audioUrlFile&&(e.audioUrlFile.removeAttribute("disabled"),e.preview.style.textDecoration=""),e._element=null}};let T="https://katex.org/docs/supported.html";var I={name:"math",display:"dialog",add:function(e){e.addModule([_()]);let t=e.context;t.math={focusElement:null,previewElement:null,fontSizeElement:null,defaultFontSize:"",_mathExp:null};let i=this.setDialog(e);t.math.modal=i,t.math.focusElement=i.querySelector(".se-math-exp"),t.math.previewElement=i.querySelector(".se-math-preview"),t.math.fontSizeElement=i.querySelector(".se-math-size"),t.math.focusElement.addEventListener("paste",function(t){"function"==typeof e.functions.onPasteMath&&e.functions.onPasteMath(t,e)},!1),t.math.focusElement.addEventListener(e.util.isIE?"textinput":"input",this._renderMathExp.bind(e,t.math),!1),t.math.fontSizeElement.addEventListener("change",(function(e){this.fontSize=e.target.value}).bind(t.math.previewElement.style),!1);let l=this.setController_MathButton(e);t.math.mathController=l,t.math._mathExp=null,i.querySelector("form").addEventListener("submit",this.submit.bind(e),!1),l.addEventListener("click",this.onClick_mathController.bind(e)),t.math.previewElement.style.fontSize=t.math.defaultFontSize,t.dialog.modal.appendChild(i),t.element.relative.appendChild(l),i=null,l=null},setDialog:function(e){let t=e.lang,i=e.util.createElement("DIV"),l=e.options.mathFontSize,n=l[0].value;i.className="se-dialog-content",i.style.display="none";let o='<form><div class="se-dialog-header"><button type="button" data-command="close" class="se-btn se-dialog-close" title="'+t.dialogBox.close+'" aria-label="'+t.dialogBox.close+'">'+e.icons.cancel+'</button><span class="se-modal-title">'+t.dialogBox.mathBox.title+'</span></div><div class="se-dialog-body"><div class="se-dialog-form"><label>'+t.dialogBox.mathBox.inputLabel+' (<a href="'+T+'" target="_blank">KaTeX</a>)</label><textarea class="se-input-form se-math-exp" type="text"></textarea></div><div class="se-dialog-form"><label>'+t.dialogBox.mathBox.fontSizeLabel+'</label><select class="se-input-select se-math-size">';for(let e=0,t=l.length,i;e<t;e++)(i=l[e]).default&&(n=i.value),o+='<option value="'+i.value+'"'+(i.default?" selected":"")+">"+i.text+"</option>";return o+='</select></div><div class="se-dialog-form"><label>'+t.dialogBox.mathBox.previewLabel+'</label><p class="se-math-preview"></p></div></div><div class="se-dialog-footer"><button type="submit" class="se-btn-primary" title="'+t.dialogBox.submitButton+'" aria-label="'+t.dialogBox.submitButton+'"><span>'+t.dialogBox.submitButton+"</span></button></div></form>",e.context.math.defaultFontSize=n,i.innerHTML=o,i},setController_MathButton:function(e){let t=e.lang,i=e.util.createElement("DIV");return i.className="se-controller se-controller-link",i.innerHTML='<div class="se-arrow se-arrow-up"></div><div class="link-content"><div class="se-btn-group"><button type="button" data-command="update" tabindex="-1" class="se-btn se-tooltip">'+e.icons.edit+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.edit+'</span></span></button><button type="button" data-command="delete" tabindex="-1" class="se-btn se-tooltip">'+e.icons.delete+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.remove+"</span></span></button></div></div>",i},open:function(){this.plugins.dialog.open.call(this,"math","math"===this.currentControllerName)},managedTags:function(){return{className:"katex",method:function(e){if(!e.getAttribute("data-exp")||!this.options.katex)return;let t=this._d.createRange().createContextualFragment(this.plugins.math._renderer.call(this,this.util.HTMLDecoder(e.getAttribute("data-exp"))));e.innerHTML=t.querySelector(".katex").innerHTML,e.setAttribute("contenteditable",!1)}}},_renderer:function(e){let t="";try{this.util.removeClass(this.context.math.focusElement,"se-error"),t=this.options.katex.src.renderToString(e,{throwOnError:!0,displayMode:!0})}catch(e){this.util.addClass(this.context.math.focusElement,"se-error"),t='<span class="se-math-katex-error">Katex syntax error. (Refer <a href="'+T+'" target="_blank">KaTeX</a>)</span>',console.warn("[SUNEDITOR.math.Katex.error] ",e)}return t},_renderMathExp:function(e,t){e.previewElement.innerHTML=this.plugins.math._renderer.call(this,t.target.value)},submit:function(e){this.showLoading(),e.preventDefault(),e.stopPropagation();let t=(function(){if(0===this.context.math.focusElement.value.trim().length)return!1;let e=this.context.math,t=e.focusElement.value,i=e.previewElement.querySelector(".katex");if(!i)return!1;if(i.className="__se__katex "+i.className,i.setAttribute("contenteditable",!1),i.setAttribute("data-exp",this.util.HTMLEncoder(t)),i.setAttribute("data-font-size",e.fontSizeElement.value),i.style.fontSize=e.fontSizeElement.value,this.context.dialog.updateModal){let t=this.util.getParentElement(e._mathExp,".katex");t.parentNode.replaceChild(i,t),this.setRange(i,0,i,1)}else{let e=this.getSelectedElements();if(e.length>1){let t=this.util.createElement(e[0].nodeName);if(t.appendChild(i),!this.insertNode(t,null,!0))return!1}else if(!this.insertNode(i,null,!0))return!1;let t=this.util.createTextNode(this.util.zeroWidthSpace);i.parentNode.insertBefore(t,i.nextSibling),this.setRange(i,0,i,1)}return e.focusElement.value="",e.fontSizeElement.value="1em",e.previewElement.style.fontSize="1em",e.previewElement.innerHTML="",!0}).bind(this);try{t()&&(this.plugins.dialog.close.call(this),this.history.push(!1))}catch(e){this.plugins.dialog.close.call(this)}finally{this.closeLoading()}return!1},active:function(e){if(e){if(e.getAttribute("data-exp"))return 0>this.controllerArray.indexOf(this.context.math.mathController)&&(this.setRange(e,0,e,1),this.plugins.math.call_controller.call(this,e)),!0}else this.controllerArray.indexOf(this.context.math.mathController)>-1&&this.controllersOff();return!1},on:function(e){if(e){let e=this.context.math;if(e._mathExp){let t=this.util.HTMLDecoder(e._mathExp.getAttribute("data-exp")),i=e._mathExp.getAttribute("data-font-size")||"1em";this.context.dialog.updateModal=!0,e.focusElement.value=t,e.fontSizeElement.value=i,e.previewElement.innerHTML=this.plugins.math._renderer.call(this,t),e.previewElement.style.fontSize=i}}else this.plugins.math.init.call(this)},call_controller:function(e){this.context.math._mathExp=e;let t=this.context.math.mathController;this.setControllerPosition(t,e,"bottom",{left:0,top:0}),this.controllersOn(t,e,"math")},onClick_mathController:function(e){e.stopPropagation();let t=e.target.getAttribute("data-command")||e.target.parentNode.getAttribute("data-command");t&&(e.preventDefault(),/update/.test(t)?(this.context.math.focusElement.value=this.util.HTMLDecoder(this.context.math._mathExp.getAttribute("data-exp")),this.plugins.dialog.open.call(this,"math",!0)):(this.util.removeItem(this.context.math._mathExp),this.context.math._mathExp=null,this.focus(),this.history.push(!1)),this.controllersOff())},init:function(){let e=this.context.math;e.mathController.style.display="none",e._mathExp=null,e.focusElement.value="",e.previewElement.innerHTML=""}},M=i(56704),R=i.n(M),H={name:"imageGallery",add:function(e){e.addModule([R()]),e.context.imageGallery={title:e.lang.toolbar.imageGallery,directData:e.options.imageGalleryData,url:e.options.imageGalleryUrl,header:e.options.imageGalleryHeader,listClass:"se-image-list",itemTemplateHandler:this.drawItems,selectorHandler:this.setImage.bind(e),columnSize:4}},open:function(e){this.plugins.fileBrowser.open.call(this,"imageGallery",e)},drawItems:function(e){let t=e.src.split("/").pop();return'<div class="se-file-item-img"><img src="'+(e.thumbnail||e.src)+'" alt="'+(e.alt||t)+'" data-command="pick" data-value="'+(e.src||e.thumbnail)+'"><div class="se-file-img-name se-file-name-back"></div><div class="se-file-img-name __se__img_name">'+(e.name||t)+"</div></div>"},setImage:function(e,t){this.callPlugin("image",(function(){this.plugins.image.create_image.call(this,e.getAttribute("data-value"),null,this.context.image._origin_w,this.context.image._origin_h,"none",{name:t,size:0},e.alt)}).bind(this),null)}},V={blockquote:l,align:n,font:o,fontSize:a,fontColor:s,hiliteColor:u,horizontalRule:c,list:d,table:h,formatBlock:p,lineHeight:g,template:m,paragraphStyle:f,textStyle:b,link:x,image:A,video:B,audio:N,math:I,imageGallery:H}},91555:function(e,t){"use strict";t.Z={name:"notice",add:function(e){let t=e.context;t.notice={};let i=e.util.createElement("DIV"),l=e.util.createElement("SPAN"),n=e.util.createElement("BUTTON");i.className="se-notice",n.className="close",n.setAttribute("aria-label","Close"),n.setAttribute("title",e.lang.dialogBox.close),n.innerHTML=e.icons.cancel,i.appendChild(l),i.appendChild(n),t.notice.modal=i,t.notice.message=l,n.addEventListener("click",this.onClick_cancel.bind(e)),t.element.editorArea.appendChild(i)},onClick_cancel:function(e){e.preventDefault(),e.stopPropagation(),this.plugins.notice.close.call(this)},open:function(e){this.context.notice.message.textContent=e,this.context.notice.modal.style.display="block"},close:function(){this.context.notice.modal.style.display="none"}}},91497:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={name:"component",set_container:function(e,t){let i=this.util.createElement("DIV");return i.className="se-component "+t,i.appendChild(e),i},set_cover:function(e){let t=this.util.createElement("FIGURE");return t.appendChild(e),t},create_caption:function(){let e=this.util.createElement("FIGCAPTION");return e.innerHTML="<div>"+this.lang.dialogBox.caption+"</div>",e}};return void 0===t&&(e.SUNEDITOR_MODULES||Object.defineProperty(e,"SUNEDITOR_MODULES",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_MODULES,"component",{enumerable:!0,writable:!1,configurable:!1,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_MODULES a window with a document");return i(e)}:i(t)},10375:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={name:"dialog",add:function(e){let t=e.context;t.dialog={kind:"",updateModal:!1,_closeSignal:!1};let i=e.util.createElement("DIV");i.className="se-dialog sun-editor-common";let l=e.util.createElement("DIV");l.className="se-dialog-back",l.style.display="none";let n=e.util.createElement("DIV");n.className="se-dialog-inner",n.style.display="none",i.appendChild(l),i.appendChild(n),t.dialog.modalArea=i,t.dialog.back=l,t.dialog.modal=n,t.dialog.modal.addEventListener("mousedown",this._onMouseDown_dialog.bind(e)),t.dialog.modal.addEventListener("click",this._onClick_dialog.bind(e)),t.element.relative.appendChild(i),i=null,l=null,n=null},_onMouseDown_dialog:function(e){/se-dialog-inner/.test(e.target.className)?this.context.dialog._closeSignal=!0:this.context.dialog._closeSignal=!1},_onClick_dialog:function(e){(/close/.test(e.target.getAttribute("data-command"))||this.context.dialog._closeSignal)&&this.plugins.dialog.close.call(this)},open:function(e,t){if(this.modalForm)return!1;this.plugins.dialog._bindClose&&(this._d.removeEventListener("keydown",this.plugins.dialog._bindClose),this.plugins.dialog._bindClose=null),this.plugins.dialog._bindClose=(function(e){/27/.test(e.keyCode)&&this.plugins.dialog.close.call(this)}).bind(this),this._d.addEventListener("keydown",this.plugins.dialog._bindClose),this.context.dialog.updateModal=t,"full"===this.options.popupDisplay?this.context.dialog.modalArea.style.position="fixed":this.context.dialog.modalArea.style.position="absolute",this.context.dialog.kind=e,this.modalForm=this.context[e].modal;let i=this.context[e].focusElement;"function"==typeof this.plugins[e].on&&this.plugins[e].on.call(this,t),this.context.dialog.modalArea.style.display="block",this.context.dialog.back.style.display="block",this.context.dialog.modal.style.display="block",this.modalForm.style.display="block",i&&i.focus()},_bindClose:null,close:function(){this.plugins.dialog._bindClose&&(this._d.removeEventListener("keydown",this.plugins.dialog._bindClose),this.plugins.dialog._bindClose=null);let e=this.context.dialog.kind;this.modalForm.style.display="none",this.context.dialog.back.style.display="none",this.context.dialog.modalArea.style.display="none",this.context.dialog.updateModal=!1,"function"==typeof this.plugins[e].init&&this.plugins[e].init.call(this),this.context.dialog.kind="",this.modalForm=null,this.focus()}};return void 0===t&&(e.SUNEDITOR_MODULES||Object.defineProperty(e,"SUNEDITOR_MODULES",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_MODULES,"dialog",{enumerable:!0,writable:!1,configurable:!1,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_MODULES a window with a document");return i(e)}:i(t)},56704:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={name:"fileBrowser",_xmlHttp:null,_loading:null,add:function(e){let t=e.context;t.fileBrowser={_closeSignal:!1,area:null,header:null,tagArea:null,body:null,list:null,tagElements:null,items:[],selectedTags:[],selectorHandler:null,contextPlugin:"",columnSize:4};let i=e.util.createElement("DIV");i.className="se-file-browser sun-editor-common";let l=e.util.createElement("DIV");l.className="se-file-browser-back";let n=e.util.createElement("DIV");n.className="se-file-browser-inner",n.innerHTML=this.set_browser(e),i.appendChild(l),i.appendChild(n),this._loading=i.querySelector(".se-loading-box"),t.fileBrowser.area=i,t.fileBrowser.header=n.querySelector(".se-file-browser-header"),t.fileBrowser.titleArea=n.querySelector(".se-file-browser-title"),t.fileBrowser.tagArea=n.querySelector(".se-file-browser-tags"),t.fileBrowser.body=n.querySelector(".se-file-browser-body"),t.fileBrowser.list=n.querySelector(".se-file-browser-list"),t.fileBrowser.tagArea.addEventListener("click",this.onClickTag.bind(e)),t.fileBrowser.list.addEventListener("click",this.onClickFile.bind(e)),n.addEventListener("mousedown",this._onMouseDown_browser.bind(e)),n.addEventListener("click",this._onClick_browser.bind(e)),t.element.relative.appendChild(i),i=null,l=null,n=null},set_browser:function(e){let t=e.lang;return'<div class="se-file-browser-content"><div class="se-file-browser-header"><button type="button" data-command="close" class="se-btn se-file-browser-close" class="close" title="'+t.dialogBox.close+'" aria-label="'+t.dialogBox.close+'">'+e.icons.cancel+'</button><span class="se-file-browser-title"></span><div class="se-file-browser-tags"></div></div><div class="se-file-browser-body"><div class="se-loading-box sun-editor-common"><div class="se-loading-effect"></div></div><div class="se-file-browser-list"></div></div></div>'},_onMouseDown_browser:function(e){/se-file-browser-inner/.test(e.target.className)?this.context.fileBrowser._closeSignal=!0:this.context.fileBrowser._closeSignal=!1},_onClick_browser:function(e){e.stopPropagation(),(/close/.test(e.target.getAttribute("data-command"))||this.context.fileBrowser._closeSignal)&&this.plugins.fileBrowser.close.call(this)},open:function(e,t){this.plugins.fileBrowser._bindClose&&(this._d.removeEventListener("keydown",this.plugins.fileBrowser._bindClose),this.plugins.fileBrowser._bindClose=null),this.plugins.fileBrowser._bindClose=(function(e){/27/.test(e.keyCode)&&this.plugins.fileBrowser.close.call(this)}).bind(this),this._d.addEventListener("keydown",this.plugins.fileBrowser._bindClose);let i=this.context.fileBrowser;i.contextPlugin=e,i.selectorHandler=t;let l=this.context[e],n=l.listClass;this.util.hasClass(i.list,n)||(i.list.className="se-file-browser-list "+n),"full"===this.options.popupDisplay?i.area.style.position="fixed":i.area.style.position="absolute",i.titleArea.textContent=l.title,i.area.style.display="block",this.context[e].directData?this.plugins.fileBrowser._drawListItem.call(this,this.context[e].directData,!0):this.plugins.fileBrowser._drawFileList.call(this,this.context[e].url,this.context[e].header)},_bindClose:null,close:function(){let e=this.plugins.fileBrowser;e._xmlHttp&&e._xmlHttp.abort(),e._bindClose&&(this._d.removeEventListener("keydown",e._bindClose),e._bindClose=null);let t=this.context.fileBrowser;t.area.style.display="none",t.selectorHandler=null,t.selectedTags=[],t.items=[],t.list.innerHTML=t.tagArea.innerHTML=t.titleArea.textContent="","function"==typeof this.plugins[t.contextPlugin].init&&this.plugins[t.contextPlugin].init.call(this),t.contextPlugin=""},showBrowserLoading:function(){this._loading.style.display="block"},closeBrowserLoading:function(){this._loading.style.display="none"},_drawFileList:function(e,t){let i=this.plugins.fileBrowser,l=i._xmlHttp=this.util.getXMLHttpRequest();if(l.onreadystatechange=i._callBackGet.bind(this,l),l.open("get",e,!0),null!==t&&"object"==typeof t&&this._w.Object.keys(t).length>0)for(let e in t)l.setRequestHeader(e,t[e]);l.send(null),this.plugins.fileBrowser.showBrowserLoading()},_callBackGet:function(e){if(4===e.readyState){if(this.plugins.fileBrowser._xmlHttp=null,200===e.status)try{let t=JSON.parse(e.responseText);t.result.length>0?this.plugins.fileBrowser._drawListItem.call(this,t.result,!0):t.nullMessage&&(this.context.fileBrowser.list.innerHTML=t.nullMessage)}catch(e){throw Error('[SUNEDITOR.fileBrowser.drawList.fail] cause : "'+e.message+'"')}finally{this.plugins.fileBrowser.closeBrowserLoading(),this.context.fileBrowser.body.style.maxHeight=this._w.innerHeight-this.context.fileBrowser.header.offsetHeight-50+"px"}else if(this.plugins.fileBrowser.closeBrowserLoading(),0!==e.status){let t=e.responseText?JSON.parse(e.responseText):e;throw Error("[SUNEDITOR.fileBrowser.get.serverException] status: "+e.status+", response: "+(t.errorMessage||e.responseText))}}},_drawListItem:function(e,t){let i=this.context.fileBrowser,l=this.context[i.contextPlugin],n=[],o=e.length,a=l.columnSize||i.columnSize,r=a<=1?1:Math.round(o/a)||1,s=l.itemTemplateHandler,u="",c='<div class="se-file-item-column">',d=1;for(let i=0,l,h;i<o;i++)if(h=(l=e[i]).tag?"string"==typeof l.tag?l.tag.split(","):l.tag:[],h=l.tag=h.map(function(e){return e.trim()}),c+=s(l),(i+1)%r==0&&d<a&&i+1<o&&(d++,c+='</div><div class="se-file-item-column">'),t&&h.length>0)for(let e=0,t=h.length,i;e<t;e++)(i=h[e])&&-1===n.indexOf(i)&&(n.push(i),u+='<a title="'+i+'" aria-label="'+i+'">'+i+"</a>");c+="</div>",i.list.innerHTML=c,t&&(i.items=e,i.tagArea.innerHTML=u,i.tagElements=i.tagArea.querySelectorAll("A"))},onClickTag:function(e){let t=e.target;if(!this.util.isAnchor(t))return;let i=t.textContent,l=this.plugins.fileBrowser,n=this.context.fileBrowser,o=n.tagArea.querySelector('a[title="'+i+'"]'),a=n.selectedTags,r=a.indexOf(i);r>-1?(a.splice(r,1),this.util.removeClass(o,"on")):(a.push(i),this.util.addClass(o,"on")),l._drawListItem.call(this,0===a.length?n.items:n.items.filter(function(e){return e.tag.some(function(e){return a.indexOf(e)>-1})}),!1)},onClickFile:function(e){e.preventDefault(),e.stopPropagation();let t=this.context.fileBrowser,i=t.list,l=e.target,n=null;if(l!==i){for(;i!==l.parentNode&&!(n=l.getAttribute("data-command"));)l=l.parentNode;n&&((t.selectorHandler||this.context[t.contextPlugin].selectorHandler)(l,l.parentNode.querySelector(".__se__img_name").textContent),this.plugins.fileBrowser.close.call(this))}}};return void 0===t&&(e.SUNEDITOR_MODULES||Object.defineProperty(e,"SUNEDITOR_MODULES",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_MODULES,"fileBrowser",{enumerable:!0,writable:!1,configurable:!1,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_MODULES a window with a document");return i(e)}:i(t)},56350:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={name:"fileManager",_xmlHttp:null,_checkMediaComponent:function(e){return!/IMG/i.test(e)||!/FIGURE/i.test(e.parentElement.nodeName)||!/FIGURE/i.test(e.parentElement.parentElement.nodeName)},upload:function(e,t,i,l,n){this.showLoading();let o=this.plugins.fileManager,a=o._xmlHttp=this.util.getXMLHttpRequest();if(a.onreadystatechange=o._callBackUpload.bind(this,a,l,n),a.open("post",e,!0),null!==t&&"object"==typeof t&&this._w.Object.keys(t).length>0)for(let e in t)a.setRequestHeader(e,t[e]);a.send(i)},_callBackUpload:function(e,t,i){if(4===e.readyState){if(200===e.status)try{t(e)}catch(e){throw Error('[SUNEDITOR.fileManager.upload.callBack.fail] cause : "'+e.message+'"')}finally{this.closeLoading()}else{this.closeLoading();let t=e.responseText?JSON.parse(e.responseText):e;if("function"!=typeof i||i("",t,this)){let i="[SUNEDITOR.fileManager.upload.serverException] status: "+e.status+", response: "+(t.errorMessage||e.responseText);throw this.functions.noticeOpen(i),Error(i)}}}},checkInfo:function(e,t,i,l,n){let o=[];for(let e=0,i=t.length;e<i;e++)o=o.concat([].slice.call(this.context.element.wysiwyg.querySelectorAll(t[e]+':not([data-se-embed="true"])')));let a=this.plugins.fileManager,r=this.context[e],s=r._infoList,u=a.setInfo.bind(this);if(o.length===s.length){if(this._componentsInfoReset){for(let t=0,l=o.length;t<l;t++)u(e,o[t],i,null,n);return}{let e=!1;for(let t=0,i=s.length,l;t<i;t++)if(l=s[t],0===o.filter(function(e){return l.src===e.src&&l.index.toString()===e.getAttribute("data-index")}).length){e=!0;break}if(!e)return}}let c=n?this.context.resizing._resize_plugin:"";n&&(this.context.resizing._resize_plugin=e);let d=[],h=[];for(let e=0,t=s.length;e<t;e++)h[e]=s[e].index;for(r.__updateTags=o;o.length>0;){let t=o.shift();this.util.getParentElement(t,this.util.isMediaComponent)&&a._checkMediaComponent(t)?!t.getAttribute("data-index")||0>h.indexOf(1*t.getAttribute("data-index"))?(d.push(r._infoIndex),t.removeAttribute("data-index"),u(e,t,i,null,n)):d.push(1*t.getAttribute("data-index")):(d.push(r._infoIndex),l(t))}for(let e=0,t;e<s.length;e++)t=s[e].index,!(d.indexOf(t)>-1)&&(s.splice(e,1),"function"==typeof i&&i(null,t,"delete",null,0,this),e--);n&&(this.context.resizing._resize_plugin=c)},setInfo:function(e,t,i,l,n){let o=n?this.context.resizing._resize_plugin:"";n&&(this.context.resizing._resize_plugin=e);let a=this.plugins[e],r=this.context[e],s=r._infoList,u=t.getAttribute("data-index"),c=null,d="";if(l||(l={name:t.getAttribute("data-file-name")||("string"==typeof t.src?t.src.split("/").pop():""),size:t.getAttribute("data-file-size")||0}),!u||this._componentsInfoInit)d="create",u=r._infoIndex++,t.setAttribute("data-index",u),t.setAttribute("data-file-name",l.name),t.setAttribute("data-file-size",l.size),c={src:t.src,index:1*u,name:l.name,size:l.size},s.push(c);else{d="update",u*=1;for(let e=0,t=s.length;e<t;e++)if(u===s[e].index){c=s[e];break}c||(c={index:u=r._infoIndex++},s.push(c)),c.src=t.src,c.name=t.getAttribute("data-file-name"),c.size=1*t.getAttribute("data-file-size")}if(c.element=t,c.delete=a.destroy.bind(this,t),c.select=(function(e){e.scrollIntoView(!0),this._w.setTimeout(a.select.bind(this,e))}).bind(this,t),n){if(!t.getAttribute("origin-size")&&t.naturalWidth&&t.setAttribute("origin-size",t.naturalWidth+","+t.naturalHeight),!t.getAttribute("data-origin")){let e=this.util.getParentElement(t,this.util.isMediaComponent),i=this.util.getParentElement(t,"FIGURE"),l=this.plugins.resizing._module_getSizeX.call(this,r,t,i,e),n=this.plugins.resizing._module_getSizeY.call(this,r,t,i,e);t.setAttribute("data-origin",l+","+n),t.setAttribute("data-size",l+","+n)}if(!t.style.width){let e=(t.getAttribute("data-size")||t.getAttribute("data-origin")||"").split(",");a.onModifyMode.call(this,t,null),a.applySize.call(this,e[0],e[1])}this.context.resizing._resize_plugin=o}"function"==typeof i&&i(t,u,d,c,--r._uploadFileLength<0?0:r._uploadFileLength,this)},deleteInfo:function(e,t,i){if(t>=0){let l=this.context[e]._infoList;for(let e=0,n=l.length;e<n;e++)if(t===l[e].index){l.splice(e,1),"function"==typeof i&&i(null,t,"delete",null,0,this);return}}},resetInfo:function(e,t){let i=this.context[e];if("function"==typeof t){let e=i._infoList;for(let i=0,l=e.length;i<l;i++)t(null,e[i].index,"delete",null,0,this)}i._infoList=[],i._infoIndex=0}};return void 0===t&&(e.SUNEDITOR_MODULES||Object.defineProperty(e,"SUNEDITOR_MODULES",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_MODULES,"fileManager",{enumerable:!0,writable:!1,configurable:!1,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_MODULES a window with a document");return i(e)}:i(t)},10877:function(e){"use strict";var t,i;t="undefined"!=typeof window?window:this,i=function(e,t){let i={name:"resizing",add:function(e){let t=e.icons,i=e.context;i.resizing={_resizeClientX:0,_resizeClientY:0,_resize_plugin:"",_resize_w:0,_resize_h:0,_origin_w:0,_origin_h:0,_rotateVertical:!1,_resize_direction:"",_move_path:null,_isChange:!1,alignIcons:{basic:t.align_justify,left:t.align_left,right:t.align_right,center:t.align_center}};let l=this.setController_resize(e);i.resizing.resizeContainer=l,i.resizing.resizeDiv=l.querySelector(".se-modal-resize"),i.resizing.resizeDot=l.querySelector(".se-resize-dot"),i.resizing.resizeDisplay=l.querySelector(".se-resize-display");let n=this.setController_button(e);i.resizing.resizeButton=n;let o=i.resizing.resizeHandles=i.resizing.resizeDot.querySelectorAll("span");i.resizing.resizeButtonGroup=n.querySelector("._se_resizing_btn_group"),i.resizing.rotationButtons=n.querySelectorAll("._se_resizing_btn_group ._se_rotation"),i.resizing.percentageButtons=n.querySelectorAll("._se_resizing_btn_group ._se_percentage"),i.resizing.alignMenu=n.querySelector(".se-resizing-align-list"),i.resizing.alignMenuList=i.resizing.alignMenu.querySelectorAll("button"),i.resizing.alignButton=n.querySelector("._se_resizing_align_button"),i.resizing.autoSizeButton=n.querySelector("._se_resizing_btn_group ._se_auto_size"),i.resizing.captionButton=n.querySelector("._se_resizing_caption_button"),l.addEventListener("mousedown",function(e){e.preventDefault()}),o[0].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(e)),o[1].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(e)),o[2].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(e)),o[3].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(e)),o[4].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(e)),o[5].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(e)),o[6].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(e)),o[7].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(e)),n.addEventListener("click",this.onClick_resizeButton.bind(e)),i.element.relative.appendChild(l),i.element.relative.appendChild(n),l=null,n=null,o=null},setController_resize:function(e){let t=e.util.createElement("DIV");return t.className="se-controller se-resizing-container",t.style.display="none",t.innerHTML='<div class="se-modal-resize"></div><div class="se-resize-dot"><span class="tl"></span><span class="tr"></span><span class="bl"></span><span class="br"></span><span class="lw"></span><span class="th"></span><span class="rw"></span><span class="bh"></span><div class="se-resize-display"></div></div>',t},setController_button:function(e){let t=e.lang,i=e.icons,l=e.util.createElement("DIV");return l.className="se-controller se-controller-resizing",l.innerHTML='<div class="se-arrow se-arrow-up"></div><div class="se-btn-group _se_resizing_btn_group"><button type="button" data-command="percent" data-value="1" class="se-tooltip _se_percentage"><span>100%</span><span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.resize100+'</span></span></button><button type="button" data-command="percent" data-value="0.75" class="se-tooltip _se_percentage"><span>75%</span><span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.resize75+'</span></span></button><button type="button" data-command="percent" data-value="0.5" class="se-tooltip _se_percentage"><span>50%</span><span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.resize50+'</span></span></button><button type="button" data-command="auto" class="se-btn se-tooltip _se_auto_size">'+i.auto_size+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.autoSize+'</span></span></button><button type="button" data-command="rotate" data-value="-90" class="se-btn se-tooltip _se_rotation">'+i.rotate_left+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.rotateLeft+'</span></span></button><button type="button" data-command="rotate" data-value="90" class="se-btn se-tooltip _se_rotation">'+i.rotate_right+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.rotateRight+'</span></span></button></div><div class="se-btn-group" style="padding-top: 0;"><button type="button" data-command="mirror" data-value="h" class="se-btn se-tooltip">'+i.mirror_horizontal+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.mirrorHorizontal+'</span></span></button><button type="button" data-command="mirror" data-value="v" class="se-btn se-tooltip">'+i.mirror_vertical+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.mirrorVertical+'</span></span></button><button type="button" data-command="onalign" class="se-btn se-tooltip _se_resizing_align_button">'+i.align_justify+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.toolbar.align+'</span></span></button><div class="se-btn-group-sub sun-editor-common se-list-layer se-resizing-align-list"><div class="se-list-inner"><ul class="se-list-basic"><li><button type="button" class="se-btn-list se-tooltip" data-command="align" data-value="basic">'+i.align_justify+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.dialogBox.basic+'</span></span></button></li><li><button type="button" class="se-btn-list se-tooltip" data-command="align" data-value="left">'+i.align_left+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.dialogBox.left+'</span></span></button></li><li><button type="button" class="se-btn-list se-tooltip" data-command="align" data-value="center">'+i.align_center+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.dialogBox.center+'</span></span></button></li><li><button type="button" class="se-btn-list se-tooltip" data-command="align" data-value="right">'+i.align_right+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.dialogBox.right+'</span></span></button></li></ul></div></div><button type="button" data-command="caption" class="se-btn se-tooltip _se_resizing_caption_button">'+i.caption+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.dialogBox.caption+'</span></span></button><button type="button" data-command="revert" class="se-btn se-tooltip">'+i.revert+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.dialogBox.revertButton+'</span></span></button><button type="button" data-command="update" class="se-btn se-tooltip">'+i.modify+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.edit+'</span></span></button><button type="button" data-command="delete" class="se-btn se-tooltip">'+i.delete+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+t.controller.remove+"</span></span></button></div>",l},_module_getSizeX:function(e,t,i,l){return(t||(t=e._element),i||(i=e._cover),l||(l=e._container),t)?/%$/.test(t.style.width)?(l&&this.util.getNumber(l.style.width,2)||100)+"%":t.style.width:""},_module_getSizeY:function(e,t,i,l){return(t||(t=e._element),i||(i=e._cover),l||(l=e._container),l&&i)?this.util.getNumber(i.style.paddingBottom,0)>0&&!this.context.resizing._rotateVertical?i.style.height:/%$/.test(t.style.height)&&/%$/.test(t.style.width)?(l&&this.util.getNumber(l.style.height,2)||100)+"%":t.style.height:t&&t.style.height||""},_module_setModifyInputSize:function(e,t){let i=e._onlyPercentage&&this.context.resizing._rotateVertical;e.proportion.checked=e._proportionChecked="false"!==e._element.getAttribute("data-proportion");let l=i?"":this.plugins.resizing._module_getSizeX.call(this,e);if(l===e._defaultSizeX&&(l=""),e._onlyPercentage&&(l=this.util.getNumber(l,2)),e.inputX.value=l,t.setInputSize.call(this,"x"),!e._onlyPercentage){let t=i?"":this.plugins.resizing._module_getSizeY.call(this,e);t===e._defaultSizeY&&(t=""),e._onlyPercentage&&(t=this.util.getNumber(t,2)),e.inputY.value=t}e.inputX.disabled=!!i,e.inputY.disabled=!!i,e.proportion.disabled=!!i,t.setRatio.call(this)},_module_setInputSize:function(e,t){if(e._onlyPercentage){"x"===t&&e.inputX.value>100&&(e.inputX.value=100);return}if(e.proportion.checked&&e._ratio&&/\d/.test(e.inputX.value)&&/\d/.test(e.inputY.value)){let i=e.inputX.value.replace(/\d+|\./g,"")||e.sizeUnit,l=e.inputY.value.replace(/\d+|\./g,"")||e.sizeUnit;if(i!==l)return;let n="%"===i?2:0;"x"===t?e.inputY.value=this.util.getNumber(e._ratioY*this.util.getNumber(e.inputX.value,n),n)+l:e.inputX.value=this.util.getNumber(e._ratioX*this.util.getNumber(e.inputY.value,n),n)+i}},_module_setRatio:function(e){let t=e.inputX.value,i=e.inputY.value;if(e.proportion.checked&&/\d+/.test(t)&&/\d+/.test(i)){if((t.replace(/\d+|\./g,"")||e.sizeUnit)!==(i.replace(/\d+|\./g,"")||e.sizeUnit))e._ratio=!1;else if(!e._ratio){let l=this.util.getNumber(t,0),n=this.util.getNumber(i,0);e._ratio=!0,e._ratioX=l/n,e._ratioY=n/l}}else e._ratio=!1},_module_sizeRevert:function(e){e._onlyPercentage?e.inputX.value=e._origin_w>100?100:e._origin_w:(e.inputX.value=e._origin_w,e.inputY.value=e._origin_h)},_module_saveCurrentSize:function(e){let t=this.plugins.resizing._module_getSizeX.call(this,e),i=this.plugins.resizing._module_getSizeY.call(this,e);e._element.setAttribute("width",t.replace("px","")),e._element.setAttribute("height",i.replace("px","")),e._element.setAttribute("data-size",t+","+i),e._videoRatio&&(e._videoRatio=i)},call_controller_resize:function(e,t){let i=this.context.resizing,l=this.context[t];i._resize_plugin=t;let n=i.resizeContainer,o=i.resizeDiv,a=this.util.getOffset(e,this.context.element.wysiwygFrame),r=i._rotateVertical=/^(90|270)$/.test(Math.abs(e.getAttribute("data-rotate")).toString()),s=r?e.offsetHeight:e.offsetWidth,u=r?e.offsetWidth:e.offsetHeight,c=a.top,d=a.left-this.context.element.wysiwygFrame.scrollLeft;n.style.top=c+"px",n.style.left=d+"px",n.style.width=s+"px",n.style.height=u+"px",o.style.top="0px",o.style.left="0px",o.style.width=s+"px",o.style.height=u+"px";let h=e.getAttribute("data-align")||"basic";h="none"===h?"basic":h;let p=this.util.getParentElement(e,this.util.isComponent),g=this.util.getParentElement(e,"FIGURE"),m=this.plugins.resizing._module_getSizeX.call(this,l,e,g,p)||"auto",f=l._onlyPercentage&&"image"===t?"":", "+(this.plugins.resizing._module_getSizeY.call(this,l,e,g,p)||"auto");this.util.changeTxt(i.resizeDisplay,this.lang.dialogBox[h]+" ("+m+f+")"),i.resizeButtonGroup.style.display=l._resizing?"":"none";let b=!l._resizing||l._resizeDotHide||l._onlyPercentage?"none":"flex",v=i.resizeHandles;for(let e=0,t=v.length;e<t;e++)v[e].style.display=b;if(l._resizing){let e=i.rotationButtons;e[0].style.display=e[1].style.display=l._rotation?"":"none"}if(l._alignHide)i.alignButton.style.display="none";else{i.alignButton.style.display="";let e=i.alignMenuList;this.util.changeElement(i.alignButton.firstElementChild,i.alignIcons[h]);for(let t=0,i=e.length;t<i;t++)e[t].getAttribute("data-value")===h?this.util.addClass(e[t],"on"):this.util.removeClass(e[t],"on")}let _=i.percentageButtons,w=/%$/.test(e.style.width)&&/%$/.test(p.style.width)?this.util.getNumber(p.style.width,0)/100+"":"";for(let e=0,t=_.length;e<t;e++)_[e].getAttribute("data-value")===w?this.util.addClass(_[e],"active"):this.util.removeClass(_[e],"active");l._captionShow?(i.captionButton.style.display="",this.util.getChildElement(e.parentNode,"figcaption")?(this.util.addClass(i.captionButton,"active"),l._captionChecked=!0):(this.util.removeClass(i.captionButton,"active"),l._captionChecked=!1)):i.captionButton.style.display="none",n.style.display="block";let y={left:0,top:50};this.options.iframe&&(y.left-=this.context.element.wysiwygFrame.parentElement.offsetLeft,y.top-=this.context.element.wysiwygFrame.parentElement.offsetTop),this.setControllerPosition(i.resizeButton,n,"bottom",y),this.controllersOn(n,i.resizeButton,(function(){this.util.setDisabledButtons.call(this.util,!1,this.resizingDisabledButtons),this.history._resetCachingButton()}).bind(this),e,t),this.util.setDisabledButtons(!0,this.resizingDisabledButtons),i._resize_w=s,i._resize_h=u;let x=(e.getAttribute("origin-size")||"").split(",");return i._origin_w=x[0]||e.naturalWidth,i._origin_h=x[1]||e.naturalHeight,{w:s,h:u,t:c,l:d}},_closeAlignMenu:null,openAlignMenu:function(){let e=this.context.resizing.alignButton;this.util.addClass(e,"on"),this.context.resizing.alignMenu.style.top=e.offsetTop+e.offsetHeight+"px",this.context.resizing.alignMenu.style.left=e.offsetLeft-e.offsetWidth/2+"px",this.context.resizing.alignMenu.style.display="block",this.plugins.resizing._closeAlignMenu=(function(){this.util.removeClass(this.context.resizing.alignButton,"on"),this.context.resizing.alignMenu.style.display="none",this.removeDocEvent("click",this.plugins.resizing._closeAlignMenu),this.plugins.resizing._closeAlignMenu=null}).bind(this),this.addDocEvent("click",this.plugins.resizing._closeAlignMenu)},onClick_resizeButton:function(e){e.stopPropagation();let t=e.target,i=t.getAttribute("data-command")||t.parentNode.getAttribute("data-command");if(!i)return;let l=t.getAttribute("data-value")||t.parentNode.getAttribute("data-value"),n=this.context.resizing._resize_plugin,o=this.context[n],a=o._element,r=this.plugins[n];if(e.preventDefault(),"function"!=typeof this.plugins.resizing._closeAlignMenu||(this.plugins.resizing._closeAlignMenu(),"onalign"!==i)){switch(i){case"auto":this.plugins.resizing.resetTransform.call(this,a),r.setAutoSize.call(this),this.selectComponent(a,n);break;case"percent":let e=this.plugins.resizing._module_getSizeY.call(this,o);if(this.context.resizing._rotateVertical){let t=a.getAttribute("data-percentage");t&&(e=t.split(",")[1])}this.plugins.resizing.resetTransform.call(this,a),r.setPercentSize.call(this,100*l,null!==this.util.getNumber(e,0)&&/%$/.test(e)?e:""),this.selectComponent(a,n);break;case"mirror":let t=a.getAttribute("data-rotate")||"0",s=a.getAttribute("data-rotateX")||"",u=a.getAttribute("data-rotateY")||"";"h"===l&&!this.context.resizing._rotateVertical||"v"===l&&this.context.resizing._rotateVertical?u=u?"":"180":s=s?"":"180",a.setAttribute("data-rotateX",s),a.setAttribute("data-rotateY",u),this.plugins.resizing._setTransForm(a,t,s,u);break;case"rotate":let c=this.context.resizing,d=1*a.getAttribute("data-rotate")+1*l,h=this._w.Math.abs(d)>=360?0:d;a.setAttribute("data-rotate",h),c._rotateVertical=/^(90|270)$/.test(this._w.Math.abs(h).toString()),this.plugins.resizing.setTransformSize.call(this,a,null,null),this.selectComponent(a,n);break;case"onalign":this.plugins.resizing.openAlignMenu.call(this);return;case"align":r.setAlign.call(this,"basic"===l?"none":l,null,null,null),this.selectComponent(a,n);break;case"caption":let p=!o._captionChecked;if(r.openModify.call(this,!0),o._captionChecked=o.captionCheckEl.checked=p,r.update_image.call(this,!1,!1,!1),p){let e=this.util.getChildElement(o._caption,function(e){return 3===e.nodeType});e?this.setRange(e,0,e,e.textContent.length):o._caption.focus(),this.controllersOff()}else this.selectComponent(a,n),r.openModify.call(this,!0);break;case"revert":r.setOriginSize.call(this),this.selectComponent(a,n);break;case"update":r.openModify.call(this),this.controllersOff();break;case"delete":r.destroy.call(this)}this.history.push(!1)}},resetTransform:function(e){let t=(e.getAttribute("data-size")||e.getAttribute("data-origin")||"").split(",");this.context.resizing._rotateVertical=!1,e.style.maxWidth="",e.style.transform="",e.style.transformOrigin="",e.setAttribute("data-rotate",""),e.setAttribute("data-rotateX",""),e.setAttribute("data-rotateY",""),this.plugins[this.context.resizing._resize_plugin].setSize.call(this,t[0]?t[0]:"auto",t[1]?t[1]:"",!0)},setTransformSize:function(e,t,i){let l=e.getAttribute("data-percentage"),n=this.context.resizing._rotateVertical,o=1*e.getAttribute("data-rotate"),a="";if(l&&!n)"auto"===(l=l.split(","))[0]&&"auto"===l[1]?this.plugins[this.context.resizing._resize_plugin].setAutoSize.call(this):this.plugins[this.context.resizing._resize_plugin].setPercentSize.call(this,l[0],l[1]);else{let l=this.util.getParentElement(e,"FIGURE"),r=t||e.offsetWidth,s=i||e.offsetHeight;this.plugins[this.context.resizing._resize_plugin].cancelPercentAttr.call(this),this.plugins[this.context.resizing._resize_plugin].setSize.call(this,r+"px",s+"px",!0),l.style.width=(n?s:r)+"px",l.style.height=this.context[this.context.resizing._resize_plugin]._caption?"":(n?r:s)+"px",n&&(a=90===o||-270===o?s/2+"px "+s/2+"px 0":r/2+"px "+r/2+"px 0")}e.style.transformOrigin=a,this.plugins.resizing._setTransForm(e,o.toString(),e.getAttribute("data-rotateX")||"",e.getAttribute("data-rotateY")||""),n?e.style.maxWidth="none":e.style.maxWidth="",this.plugins.resizing.setCaptionPosition.call(this,e)},_setTransForm:function(e,t,i,l){let n=(e.offsetWidth-e.offsetHeight)*(/-/.test(t)?1:-1),o="";if(/[1-9]/.test(t)&&(i||l))switch(o=i?"Y":"X",t){case"90":o=i&&l?"X":l?o:"";break;case"270":n*=-1,o=i&&l?"Y":i?o:"";break;case"-90":o=i&&l?"Y":i?o:"";break;case"-270":n*=-1,o=i&&l?"X":l?o:"";break;default:o=""}t%180==0&&(e.style.maxWidth=""),e.style.transform="rotate("+t+"deg)"+(i?" rotateX("+i+"deg)":"")+(l?" rotateY("+l+"deg)":"")+(o?" translate"+o+"("+n+"px)":"")},setCaptionPosition:function(e){let t=this.util.getChildElement(this.util.getParentElement(e,"FIGURE"),"FIGCAPTION");t&&(t.style.marginTop=(this.context.resizing._rotateVertical?e.offsetWidth-e.offsetHeight:0)+"px")},onMouseDown_resize_handle:function(e){e.stopPropagation(),e.preventDefault();let t=this.context.resizing,i=t._resize_direction=e.target.classList[0];t._resizeClientX=e.clientX,t._resizeClientY=e.clientY,this.context.element.resizeBackground.style.display="block",t.resizeButton.style.display="none",t.resizeDiv.style.float=/l/.test(i)?"right":/r/.test(i)?"left":"none";let l=(function(e){if("keydown"===e.type&&27!==e.keyCode)return;let o=t._isChange;t._isChange=!1,this.removeDocEvent("mousemove",n),this.removeDocEvent("mouseup",l),this.removeDocEvent("keydown",l),"keydown"===e.type?(this.controllersOff(),this.context.element.resizeBackground.style.display="none",this.plugins[this.context.resizing._resize_plugin].init.call(this)):(this.plugins.resizing.cancel_controller_resize.call(this,i),o&&this.history.push(!1))}).bind(this),n=this.plugins.resizing.resizing_element.bind(this,t,i,this.context[t._resize_plugin]);this.addDocEvent("mousemove",n),this.addDocEvent("mouseup",l),this.addDocEvent("keydown",l)},resizing_element:function(e,t,i,l){let n=l.clientX,o=l.clientY,a=i._element_w,r=i._element_h,s=i._element_w+(/r/.test(t)?n-e._resizeClientX:e._resizeClientX-n),u=i._element_h+(/b/.test(t)?o-e._resizeClientY:e._resizeClientY-o),c=i._element_h/i._element_w*s;/t/.test(t)&&(e.resizeDiv.style.top=i._element_h-(/h/.test(t)?u:c)+"px"),/l/.test(t)&&(e.resizeDiv.style.left=i._element_w-s+"px"),/r|l/.test(t)&&(e.resizeDiv.style.width=s+"px",a=s),/^(t|b)[^h]$/.test(t)?(e.resizeDiv.style.height=c+"px",r=c):/^(t|b)h$/.test(t)&&(e.resizeDiv.style.height=u+"px",r=u),e._resize_w=a,e._resize_h=r,this.util.changeTxt(e.resizeDisplay,this._w.Math.round(a)+" x "+this._w.Math.round(r)),e._isChange=!0},cancel_controller_resize:function(e){let t=this.context.resizing._rotateVertical;this.controllersOff(),this.context.element.resizeBackground.style.display="none";let i=this._w.Math.round(t?this.context.resizing._resize_h:this.context.resizing._resize_w),l=this._w.Math.round(t?this.context.resizing._resize_w:this.context.resizing._resize_h);if(!t&&!/%$/.test(i)){let e=this.context.element.wysiwygFrame.clientWidth-32-2;this.util.getNumber(i,0)>e&&(l=this._w.Math.round(l/i*e),i=e)}let n=this.context.resizing._resize_plugin;this.plugins[n].setSize.call(this,i,l,!1,e),t&&this.plugins.resizing.setTransformSize.call(this,this.context[this.context.resizing._resize_plugin]._element,i,l),this.selectComponent(this.context[n]._element,n)}};return void 0===t&&(e.SUNEDITOR_MODULES||Object.defineProperty(e,"SUNEDITOR_MODULES",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_MODULES,"resizing",{enumerable:!0,writable:!1,configurable:!1,value:i})),i},"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw Error("SUNEDITOR_MODULES a window with a document");return i(e)}:i(t)},38438:function(e,t,i){"use strict";i.r(t);var l=i(22094),n=i(37689),o=i(22475),a=i(78902);t.default={init:function(e){return{create:(function(t,i){return this.create(t,i,e)}).bind(this)}},create:function(e,t,i){n.Z._propertiesInit(),"object"!=typeof t&&(t={}),i&&(t=[i,t].reduce(function(e,t){for(let i in t)if(n.Z.hasOwn(t,i)){if("plugins"===i&&t[i]&&e[i]){let l=e[i],n=t[i];l=l.length?l:Object.keys(l).map(function(e){return l[e]}),n=n.length?n:Object.keys(n).map(function(e){return n[e]}),e[i]=n.filter(function(e){return -1===l.indexOf(e)}).concat(l)}else e[i]=t[i]}return e},{}));let r="string"==typeof e?document.getElementById(e):e;if(!r){if("string"==typeof e)throw Error('[SUNEDITOR.create.fail] The element for that id was not found (ID:"'+e+'")');throw Error("[SUNEDITOR.create.fail] suneditor requires textarea's element or id value")}let s=o.Z.init(r,t);if(s.constructed._top.id&&document.getElementById(s.constructed._top.id))throw Error('[SUNEDITOR.create.fail] The ID of the suneditor you are trying to create already exists (ID:"'+s.constructed._top.id+'")');return(0,l.Z)((0,a.Z)(r,s.constructed,s.options),s.pluginCallButtons,s.plugins,s.options.lang,t,s._responsiveButtons)}}},42623:function(){}}]);