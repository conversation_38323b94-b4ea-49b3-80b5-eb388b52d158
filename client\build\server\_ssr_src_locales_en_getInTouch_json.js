"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_en_getInTouch_json";
exports.ids = ["_ssr_src_locales_en_getInTouch_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/en/getInTouch.json":
/*!****************************************!*\
  !*** ./src/locales/en/getInTouch.json ***!
  \****************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"getInTouch":"Get in Touch","altImg":"Pentabell contact","description":"Our team is always here to chat","firstName":"First Name","lastName":"Last Name","fullName":"Full Name","email":"Email address","phone":"Phone Number","message":"Message","typeMessage":"Type your message","youAre":"You Are","subject":"Subject","countryName":"Country","submit":"Submit"}');

/***/ })

};
;