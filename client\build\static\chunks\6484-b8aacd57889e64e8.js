(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6484],{71230:function(t,e,n){"use strict";n.d(e,{j:function(){return s}});var r=n(88671),i=n(1063),u=n(68200),s=new(function(t){function e(){var e;return(e=t.call(this)||this).setup=function(t){var e;if(!u.sk&&(null==(e=window)?void 0:e.addEventListener)){var n=function(){return t()};return window.addEventListener("visibilitychange",n,!1),window.addEventListener("focus",n,!1),function(){window.removeEventListener("visibilitychange",n),window.removeEventListener("focus",n)}}},e}(0,r.Z)(e,t);var n=e.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){if(!this.hasListeners()){var t;null==(t=this.cleanup)||t.call(this),this.cleanup=void 0}},n.setEventListener=function(t){var e,n=this;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t(function(t){"boolean"==typeof t?n.setFocused(t):n.onFocus()})},n.setFocused=function(t){this.focused=t,t&&this.onFocus()},n.onFocus=function(){this.listeners.forEach(function(t){t()})},n.isFocused=function(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)},e}(i.l))},82358:function(t,e,n){"use strict";n.d(e,{QueryClient:function(){return r.S}});var r=n(76809),i=n(96604);n.o(i,"QueryClientProvider")&&n.d(e,{QueryClientProvider:function(){return i.QueryClientProvider}}),n.o(i,"useMutation")&&n.d(e,{useMutation:function(){return i.useMutation}}),n.o(i,"useQuery")&&n.d(e,{useQuery:function(){return i.useQuery}}),n.o(i,"useQueryClient")&&n.d(e,{useQueryClient:function(){return i.useQueryClient}})},3765:function(t,e,n){"use strict";n.d(e,{E:function(){return u},j:function(){return i}});var r=console;function i(){return r}function u(t){r=t}},41362:function(t,e,n){"use strict";n.d(e,{R:function(){return c},m:function(){return a}});var r=n(1119),i=n(3765),u=n(53312),s=n(86348),o=n(68200),a=function(){function t(t){this.options=(0,r.Z)({},t.defaultOptions,t.options),this.mutationId=t.mutationId,this.mutationCache=t.mutationCache,this.observers=[],this.state=t.state||c(),this.meta=t.meta}var e=t.prototype;return e.setState=function(t){this.dispatch({type:"setState",state:t})},e.addObserver=function(t){-1===this.observers.indexOf(t)&&this.observers.push(t)},e.removeObserver=function(t){this.observers=this.observers.filter(function(e){return e!==t})},e.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(o.ZT).catch(o.ZT)):Promise.resolve()},e.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},e.execute=function(){var t,e=this,n="loading"===this.state.status,r=Promise.resolve();return n||(this.dispatch({type:"loading",variables:this.options.variables}),r=r.then(function(){null==e.mutationCache.config.onMutate||e.mutationCache.config.onMutate(e.state.variables,e)}).then(function(){return null==e.options.onMutate?void 0:e.options.onMutate(e.state.variables)}).then(function(t){t!==e.state.context&&e.dispatch({type:"loading",context:t,variables:e.state.variables})})),r.then(function(){return e.executeMutation()}).then(function(n){t=n,null==e.mutationCache.config.onSuccess||e.mutationCache.config.onSuccess(t,e.state.variables,e.state.context,e)}).then(function(){return null==e.options.onSuccess?void 0:e.options.onSuccess(t,e.state.variables,e.state.context)}).then(function(){return null==e.options.onSettled?void 0:e.options.onSettled(t,null,e.state.variables,e.state.context)}).then(function(){return e.dispatch({type:"success",data:t}),t}).catch(function(t){return null==e.mutationCache.config.onError||e.mutationCache.config.onError(t,e.state.variables,e.state.context,e),(0,i.j)().error(t),Promise.resolve().then(function(){return null==e.options.onError?void 0:e.options.onError(t,e.state.variables,e.state.context)}).then(function(){return null==e.options.onSettled?void 0:e.options.onSettled(void 0,t,e.state.variables,e.state.context)}).then(function(){throw e.dispatch({type:"error",error:t}),t})})},e.executeMutation=function(){var t,e=this;return this.retryer=new s.m4({fn:function(){return e.options.mutationFn?e.options.mutationFn(e.state.variables):Promise.reject("No mutationFn found")},onFail:function(){e.dispatch({type:"failed"})},onPause:function(){e.dispatch({type:"pause"})},onContinue:function(){e.dispatch({type:"continue"})},retry:null!=(t=this.options.retry)?t:0,retryDelay:this.options.retryDelay}),this.retryer.promise},e.dispatch=function(t){var e=this;this.state=function(t,e){switch(e.type){case"failed":return(0,r.Z)({},t,{failureCount:t.failureCount+1});case"pause":return(0,r.Z)({},t,{isPaused:!0});case"continue":return(0,r.Z)({},t,{isPaused:!1});case"loading":return(0,r.Z)({},t,{context:e.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:e.variables});case"success":return(0,r.Z)({},t,{data:e.data,error:null,status:"success",isPaused:!1});case"error":return(0,r.Z)({},t,{data:void 0,error:e.error,failureCount:t.failureCount+1,isPaused:!1,status:"error"});case"setState":return(0,r.Z)({},t,e.state);default:return t}}(this.state,t),u.V.batch(function(){e.observers.forEach(function(e){e.onMutationUpdate(t)}),e.mutationCache.notify(e)})},t}();function c(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}},53312:function(t,e,n){"use strict";n.d(e,{V:function(){return i}});var r=n(68200),i=new(function(){function t(){this.queue=[],this.transactions=0,this.notifyFn=function(t){t()},this.batchNotifyFn=function(t){t()}}var e=t.prototype;return e.batch=function(t){var e;this.transactions++;try{e=t()}finally{this.transactions--,this.transactions||this.flush()}return e},e.schedule=function(t){var e=this;this.transactions?this.queue.push(t):(0,r.A4)(function(){e.notifyFn(t)})},e.batchCalls=function(t){var e=this;return function(){for(var n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];e.schedule(function(){t.apply(void 0,r)})}},e.flush=function(){var t=this,e=this.queue;this.queue=[],e.length&&(0,r.A4)(function(){t.batchNotifyFn(function(){e.forEach(function(e){t.notifyFn(e)})})})},e.setNotifyFunction=function(t){this.notifyFn=t},e.setBatchNotifyFunction=function(t){this.batchNotifyFn=t},t}())},47460:function(t,e,n){"use strict";n.d(e,{N:function(){return s}});var r=n(88671),i=n(1063),u=n(68200),s=new(function(t){function e(){var e;return(e=t.call(this)||this).setup=function(t){var e;if(!u.sk&&(null==(e=window)?void 0:e.addEventListener)){var n=function(){return t()};return window.addEventListener("online",n,!1),window.addEventListener("offline",n,!1),function(){window.removeEventListener("online",n),window.removeEventListener("offline",n)}}},e}(0,r.Z)(e,t);var n=e.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){if(!this.hasListeners()){var t;null==(t=this.cleanup)||t.call(this),this.cleanup=void 0}},n.setEventListener=function(t){var e,n=this;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t(function(t){"boolean"==typeof t?n.setOnline(t):n.onOnline()})},n.setOnline=function(t){this.online=t,t&&this.onOnline()},n.onOnline=function(){this.listeners.forEach(function(t){t()})},n.isOnline=function(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine},e}(i.l))},76809:function(t,e,n){"use strict";n.d(e,{S:function(){return m}});var r=n(1119),i=n(68200),u=n(88671),s=n(53312),o=n(3765),a=n(86348),c=function(){function t(t){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.cache=t.cache,this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.initialState=t.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=t.meta,this.scheduleGc()}var e=t.prototype;return e.setOptions=function(t){var e;this.options=(0,r.Z)({},this.defaultOptions,t),this.meta=null==t?void 0:t.meta,this.cacheTime=Math.max(this.cacheTime||0,null!=(e=this.options.cacheTime)?e:3e5)},e.setDefaultOptions=function(t){this.defaultOptions=t},e.scheduleGc=function(){var t=this;this.clearGcTimeout(),(0,i.PN)(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){t.optionalRemove()},this.cacheTime))},e.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},e.optionalRemove=function(){!this.observers.length&&(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},e.setData=function(t,e){var n,r,u=this.state.data,s=(0,i.SE)(t,u);return(null==(n=(r=this.options).isDataEqual)?void 0:n.call(r,u,s))?s=u:!1!==this.options.structuralSharing&&(s=(0,i.Q$)(u,s)),this.dispatch({data:s,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt}),s},e.setState=function(t,e){this.dispatch({type:"setState",state:t,setStateOptions:e})},e.cancel=function(t){var e,n=this.promise;return null==(e=this.retryer)||e.cancel(t),n?n.then(i.ZT).catch(i.ZT):Promise.resolve()},e.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},e.reset=function(){this.destroy(),this.setState(this.initialState)},e.isActive=function(){return this.observers.some(function(t){return!1!==t.options.enabled})},e.isFetching=function(){return this.state.isFetching},e.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(t){return t.getCurrentResult().isStale})},e.isStaleByTime=function(t){return void 0===t&&(t=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,i.Kp)(this.state.dataUpdatedAt,t)},e.onFocus=function(){var t,e=this.observers.find(function(t){return t.shouldFetchOnWindowFocus()});e&&e.refetch(),null==(t=this.retryer)||t.continue()},e.onOnline=function(){var t,e=this.observers.find(function(t){return t.shouldFetchOnReconnect()});e&&e.refetch(),null==(t=this.retryer)||t.continue()},e.addObserver=function(t){-1===this.observers.indexOf(t)&&(this.observers.push(t),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:t}))},e.removeObserver=function(t){-1!==this.observers.indexOf(t)&&(this.observers=this.observers.filter(function(e){return e!==t}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:t}))},e.getObserversCount=function(){return this.observers.length},e.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},e.fetch=function(t,e){var n,r,u,s,c,l,h=this;if(this.state.isFetching){if(this.state.dataUpdatedAt&&(null==e?void 0:e.cancelRefetch))this.cancel({silent:!0});else if(this.promise)return null==(n=this.retryer)||n.continueRetry(),this.promise}if(t&&this.setOptions(t),!this.options.queryFn){var f=this.observers.find(function(t){return t.options.queryFn});f&&this.setOptions(f.options)}var d=(0,i.mc)(this.queryKey),v=(0,i.G9)(),p={queryKey:d,pageParam:void 0,meta:this.meta};Object.defineProperty(p,"signal",{enumerable:!0,get:function(){if(v)return h.abortSignalConsumed=!0,v.signal}});var y={fetchOptions:e,options:this.options,queryKey:d,state:this.state,fetchFn:function(){return h.options.queryFn?(h.abortSignalConsumed=!1,h.options.queryFn(p)):Promise.reject("Missing queryFn")},meta:this.meta};return(null==(s=this.options.behavior)?void 0:s.onFetch)&&(null==(r=this.options.behavior)||r.onFetch(y)),this.revertState=this.state,this.state.isFetching&&this.state.fetchMeta===(null==(c=y.fetchOptions)?void 0:c.meta)||this.dispatch({type:"fetch",meta:null==(u=y.fetchOptions)?void 0:u.meta}),this.retryer=new a.m4({fn:y.fetchFn,abort:null==v?void 0:null==(l=v.abort)?void 0:l.bind(v),onSuccess:function(t){h.setData(t),null==h.cache.config.onSuccess||h.cache.config.onSuccess(t,h),0===h.cacheTime&&h.optionalRemove()},onError:function(t){(0,a.DV)(t)&&t.silent||h.dispatch({type:"error",error:t}),(0,a.DV)(t)||(null==h.cache.config.onError||h.cache.config.onError(t,h),(0,o.j)().error(t)),0===h.cacheTime&&h.optionalRemove()},onFail:function(){h.dispatch({type:"failed"})},onPause:function(){h.dispatch({type:"pause"})},onContinue:function(){h.dispatch({type:"continue"})},retry:y.options.retry,retryDelay:y.options.retryDelay}),this.promise=this.retryer.promise,this.promise},e.dispatch=function(t){var e=this;this.state=this.reducer(this.state,t),s.V.batch(function(){e.observers.forEach(function(e){e.onQueryUpdate(t)}),e.cache.notify({query:e,type:"queryUpdated",action:t})})},e.getDefaultState=function(t){var e="function"==typeof t.initialData?t.initialData():t.initialData,n=void 0!==t.initialData?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0,r=void 0!==e;return{data:e,dataUpdateCount:0,dataUpdatedAt:r?null!=n?n:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:r?"success":"idle"}},e.reducer=function(t,e){var n,i;switch(e.type){case"failed":return(0,r.Z)({},t,{fetchFailureCount:t.fetchFailureCount+1});case"pause":return(0,r.Z)({},t,{isPaused:!0});case"continue":return(0,r.Z)({},t,{isPaused:!1});case"fetch":return(0,r.Z)({},t,{fetchFailureCount:0,fetchMeta:null!=(n=e.meta)?n:null,isFetching:!0,isPaused:!1},!t.dataUpdatedAt&&{error:null,status:"loading"});case"success":return(0,r.Z)({},t,{data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:null!=(i=e.dataUpdatedAt)?i:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var u=e.error;if((0,a.DV)(u)&&u.revert&&this.revertState)return(0,r.Z)({},this.revertState);return(0,r.Z)({},t,{error:u,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return(0,r.Z)({},t,{isInvalidated:!0});case"setState":return(0,r.Z)({},t,e.state);default:return t}},t}(),l=n(1063),h=function(t){function e(e){var n;return(n=t.call(this)||this).config=e||{},n.queries=[],n.queriesMap={},n}(0,u.Z)(e,t);var n=e.prototype;return n.build=function(t,e,n){var r,u=e.queryKey,s=null!=(r=e.queryHash)?r:(0,i.Rm)(u,e),o=this.get(s);return o||(o=new c({cache:this,queryKey:u,queryHash:s,options:t.defaultQueryOptions(e),state:n,defaultOptions:t.getQueryDefaults(u),meta:e.meta}),this.add(o)),o},n.add=function(t){this.queriesMap[t.queryHash]||(this.queriesMap[t.queryHash]=t,this.queries.push(t),this.notify({type:"queryAdded",query:t}))},n.remove=function(t){var e=this.queriesMap[t.queryHash];e&&(t.destroy(),this.queries=this.queries.filter(function(e){return e!==t}),e===t&&delete this.queriesMap[t.queryHash],this.notify({type:"queryRemoved",query:t}))},n.clear=function(){var t=this;s.V.batch(function(){t.queries.forEach(function(e){t.remove(e)})})},n.get=function(t){return this.queriesMap[t]},n.getAll=function(){return this.queries},n.find=function(t,e){var n=(0,i.I6)(t,e)[0];return void 0===n.exact&&(n.exact=!0),this.queries.find(function(t){return(0,i._x)(n,t)})},n.findAll=function(t,e){var n=(0,i.I6)(t,e)[0];return Object.keys(n).length>0?this.queries.filter(function(t){return(0,i._x)(n,t)}):this.queries},n.notify=function(t){var e=this;s.V.batch(function(){e.listeners.forEach(function(e){e(t)})})},n.onFocus=function(){var t=this;s.V.batch(function(){t.queries.forEach(function(t){t.onFocus()})})},n.onOnline=function(){var t=this;s.V.batch(function(){t.queries.forEach(function(t){t.onOnline()})})},e}(l.l),f=n(41362),d=function(t){function e(e){var n;return(n=t.call(this)||this).config=e||{},n.mutations=[],n.mutationId=0,n}(0,u.Z)(e,t);var n=e.prototype;return n.build=function(t,e,n){var r=new f.m({mutationCache:this,mutationId:++this.mutationId,options:t.defaultMutationOptions(e),state:n,defaultOptions:e.mutationKey?t.getMutationDefaults(e.mutationKey):void 0,meta:e.meta});return this.add(r),r},n.add=function(t){this.mutations.push(t),this.notify(t)},n.remove=function(t){this.mutations=this.mutations.filter(function(e){return e!==t}),t.cancel(),this.notify(t)},n.clear=function(){var t=this;s.V.batch(function(){t.mutations.forEach(function(e){t.remove(e)})})},n.getAll=function(){return this.mutations},n.find=function(t){return void 0===t.exact&&(t.exact=!0),this.mutations.find(function(e){return(0,i.X7)(t,e)})},n.findAll=function(t){return this.mutations.filter(function(e){return(0,i.X7)(t,e)})},n.notify=function(t){var e=this;s.V.batch(function(){e.listeners.forEach(function(e){e(t)})})},n.onFocus=function(){this.resumePausedMutations()},n.onOnline=function(){this.resumePausedMutations()},n.resumePausedMutations=function(){var t=this.mutations.filter(function(t){return t.state.isPaused});return s.V.batch(function(){return t.reduce(function(t,e){return t.then(function(){return e.continue().catch(i.ZT)})},Promise.resolve())})},e}(l.l),v=n(71230),p=n(47460);function y(t,e){return null==t.getNextPageParam?void 0:t.getNextPageParam(e[e.length-1],e)}var m=function(){function t(t){void 0===t&&(t={}),this.queryCache=t.queryCache||new h,this.mutationCache=t.mutationCache||new d,this.defaultOptions=t.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var e=t.prototype;return e.mount=function(){var t=this;this.unsubscribeFocus=v.j.subscribe(function(){v.j.isFocused()&&p.N.isOnline()&&(t.mutationCache.onFocus(),t.queryCache.onFocus())}),this.unsubscribeOnline=p.N.subscribe(function(){v.j.isFocused()&&p.N.isOnline()&&(t.mutationCache.onOnline(),t.queryCache.onOnline())})},e.unmount=function(){var t,e;null==(t=this.unsubscribeFocus)||t.call(this),null==(e=this.unsubscribeOnline)||e.call(this)},e.isFetching=function(t,e){var n=(0,i.I6)(t,e)[0];return n.fetching=!0,this.queryCache.findAll(n).length},e.isMutating=function(t){return this.mutationCache.findAll((0,r.Z)({},t,{fetching:!0})).length},e.getQueryData=function(t,e){var n;return null==(n=this.queryCache.find(t,e))?void 0:n.state.data},e.getQueriesData=function(t){return this.getQueryCache().findAll(t).map(function(t){return[t.queryKey,t.state.data]})},e.setQueryData=function(t,e,n){var r=(0,i._v)(t),u=this.defaultQueryOptions(r);return this.queryCache.build(this,u).setData(e,n)},e.setQueriesData=function(t,e,n){var r=this;return s.V.batch(function(){return r.getQueryCache().findAll(t).map(function(t){var i=t.queryKey;return[i,r.setQueryData(i,e,n)]})})},e.getQueryState=function(t,e){var n;return null==(n=this.queryCache.find(t,e))?void 0:n.state},e.removeQueries=function(t,e){var n=(0,i.I6)(t,e)[0],r=this.queryCache;s.V.batch(function(){r.findAll(n).forEach(function(t){r.remove(t)})})},e.resetQueries=function(t,e,n){var u=this,o=(0,i.I6)(t,e,n),a=o[0],c=o[1],l=this.queryCache,h=(0,r.Z)({},a,{active:!0});return s.V.batch(function(){return l.findAll(a).forEach(function(t){t.reset()}),u.refetchQueries(h,c)})},e.cancelQueries=function(t,e,n){var r=this,u=(0,i.I6)(t,e,n),o=u[0],a=u[1],c=void 0===a?{}:a;return void 0===c.revert&&(c.revert=!0),Promise.all(s.V.batch(function(){return r.queryCache.findAll(o).map(function(t){return t.cancel(c)})})).then(i.ZT).catch(i.ZT)},e.invalidateQueries=function(t,e,n){var u,o,a,c=this,l=(0,i.I6)(t,e,n),h=l[0],f=l[1],d=(0,r.Z)({},h,{active:null==(u=null!=(o=h.refetchActive)?o:h.active)||u,inactive:null!=(a=h.refetchInactive)&&a});return s.V.batch(function(){return c.queryCache.findAll(h).forEach(function(t){t.invalidate()}),c.refetchQueries(d,f)})},e.refetchQueries=function(t,e,n){var u=this,o=(0,i.I6)(t,e,n),a=o[0],c=o[1],l=Promise.all(s.V.batch(function(){return u.queryCache.findAll(a).map(function(t){return t.fetch(void 0,(0,r.Z)({},c,{meta:{refetchPage:null==a?void 0:a.refetchPage}}))})})).then(i.ZT);return(null==c?void 0:c.throwOnError)||(l=l.catch(i.ZT)),l},e.fetchQuery=function(t,e,n){var r=(0,i._v)(t,e,n),u=this.defaultQueryOptions(r);void 0===u.retry&&(u.retry=!1);var s=this.queryCache.build(this,u);return s.isStaleByTime(u.staleTime)?s.fetch(u):Promise.resolve(s.state.data)},e.prefetchQuery=function(t,e,n){return this.fetchQuery(t,e,n).then(i.ZT).catch(i.ZT)},e.fetchInfiniteQuery=function(t,e,n){var r=(0,i._v)(t,e,n);return r.behavior={onFetch:function(t){t.fetchFn=function(){var e,n,r,u,s,o,c,l=null==(e=t.fetchOptions)?void 0:null==(n=e.meta)?void 0:n.refetchPage,h=null==(r=t.fetchOptions)?void 0:null==(u=r.meta)?void 0:u.fetchMore,f=null==h?void 0:h.pageParam,d=(null==h?void 0:h.direction)==="forward",v=(null==h?void 0:h.direction)==="backward",p=(null==(s=t.state.data)?void 0:s.pages)||[],m=(null==(o=t.state.data)?void 0:o.pageParams)||[],b=(0,i.G9)(),g=null==b?void 0:b.signal,C=m,O=!1,R=t.options.queryFn||function(){return Promise.reject("Missing queryFn")},Q=function(t,e,n,r){return C=r?[e].concat(C):[].concat(C,[e]),r?[n].concat(t):[].concat(t,[n])},S=function(e,n,r,i){if(O)return Promise.reject("Cancelled");if(void 0===r&&!n&&e.length)return Promise.resolve(e);var u=R({queryKey:t.queryKey,signal:g,pageParam:r,meta:t.meta}),s=Promise.resolve(u).then(function(t){return Q(e,r,t,i)});return(0,a.LE)(u)&&(s.cancel=u.cancel),s};if(p.length){if(d){var P=void 0!==f,q=P?f:y(t.options,p);c=S(p,P,q)}else if(v){var F,E=void 0!==f,w=E?f:null==(F=t.options).getPreviousPageParam?void 0:F.getPreviousPageParam(p[0],p);c=S(p,E,w,!0)}else!function(){C=[];var e=void 0===t.options.getNextPageParam;c=!l||!p[0]||l(p[0],0,p)?S([],e,m[0]):Promise.resolve(Q([],m[0],p[0]));for(var n=function(n){c=c.then(function(r){if(!l||!p[n]||l(p[n],n,p)){var i=e?m[n]:y(t.options,r);return S(r,e,i)}return Promise.resolve(Q(r,m[n],p[n]))})},r=1;r<p.length;r++)n(r)}()}else c=S([]);var T=c.then(function(t){return{pages:t,pageParams:C}});return T.cancel=function(){O=!0,null==b||b.abort(),(0,a.LE)(c)&&c.cancel()},T}}},this.fetchQuery(r)},e.prefetchInfiniteQuery=function(t,e,n){return this.fetchInfiniteQuery(t,e,n).then(i.ZT).catch(i.ZT)},e.cancelMutations=function(){var t=this;return Promise.all(s.V.batch(function(){return t.mutationCache.getAll().map(function(t){return t.cancel()})})).then(i.ZT).catch(i.ZT)},e.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},e.executeMutation=function(t){return this.mutationCache.build(this,t).execute()},e.getQueryCache=function(){return this.queryCache},e.getMutationCache=function(){return this.mutationCache},e.getDefaultOptions=function(){return this.defaultOptions},e.setDefaultOptions=function(t){this.defaultOptions=t},e.setQueryDefaults=function(t,e){var n=this.queryDefaults.find(function(e){return(0,i.yF)(t)===(0,i.yF)(e.queryKey)});n?n.defaultOptions=e:this.queryDefaults.push({queryKey:t,defaultOptions:e})},e.getQueryDefaults=function(t){var e;return t?null==(e=this.queryDefaults.find(function(e){return(0,i.to)(t,e.queryKey)}))?void 0:e.defaultOptions:void 0},e.setMutationDefaults=function(t,e){var n=this.mutationDefaults.find(function(e){return(0,i.yF)(t)===(0,i.yF)(e.mutationKey)});n?n.defaultOptions=e:this.mutationDefaults.push({mutationKey:t,defaultOptions:e})},e.getMutationDefaults=function(t){var e;return t?null==(e=this.mutationDefaults.find(function(e){return(0,i.to)(t,e.mutationKey)}))?void 0:e.defaultOptions:void 0},e.defaultQueryOptions=function(t){if(null==t?void 0:t._defaulted)return t;var e=(0,r.Z)({},this.defaultOptions.queries,this.getQueryDefaults(null==t?void 0:t.queryKey),t,{_defaulted:!0});return!e.queryHash&&e.queryKey&&(e.queryHash=(0,i.Rm)(e.queryKey,e)),e},e.defaultQueryObserverOptions=function(t){return this.defaultQueryOptions(t)},e.defaultMutationOptions=function(t){return(null==t?void 0:t._defaulted)?t:(0,r.Z)({},this.defaultOptions.mutations,this.getMutationDefaults(null==t?void 0:t.mutationKey),t,{_defaulted:!0})},e.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},t}()},86348:function(t,e,n){"use strict";n.d(e,{DV:function(){return c},LE:function(){return o},m4:function(){return l}});var r=n(71230),i=n(47460),u=n(68200);function s(t){return Math.min(1e3*Math.pow(2,t),3e4)}function o(t){return"function"==typeof(null==t?void 0:t.cancel)}var a=function(t){this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent};function c(t){return t instanceof a}var l=function(t){var e,n,c,l,h=this,f=!1;this.abort=t.abort,this.cancel=function(t){return null==e?void 0:e(t)},this.cancelRetry=function(){f=!0},this.continueRetry=function(){f=!1},this.continue=function(){return null==n?void 0:n()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(t,e){c=t,l=e});var d=function(e){h.isResolved||(h.isResolved=!0,null==t.onSuccess||t.onSuccess(e),null==n||n(),c(e))},v=function(e){h.isResolved||(h.isResolved=!0,null==t.onError||t.onError(e),null==n||n(),l(e))};!function c(){var l;if(!h.isResolved){try{l=t.fn()}catch(t){l=Promise.reject(t)}e=function(t){if(!h.isResolved&&(v(new a(t)),null==h.abort||h.abort(),o(l)))try{l.cancel()}catch(t){}},h.isTransportCancelable=o(l),Promise.resolve(l).then(d).catch(function(e){if(!h.isResolved){var o,a,l=null!=(o=t.retry)?o:3,d=null!=(a=t.retryDelay)?a:s,p="function"==typeof d?d(h.failureCount,e):d,y=!0===l||"number"==typeof l&&h.failureCount<l||"function"==typeof l&&l(h.failureCount,e);if(f||!y){v(e);return}h.failureCount++,null==t.onFail||t.onFail(h.failureCount,e),(0,u.Gh)(p).then(function(){if(!r.j.isFocused()||!i.N.isOnline())return new Promise(function(e){n=e,h.isPaused=!0,null==t.onPause||t.onPause()}).then(function(){n=void 0,h.isPaused=!1,null==t.onContinue||t.onContinue()})}).then(function(){f?v(e):c()})}})}}()}},1063:function(t,e,n){"use strict";n.d(e,{l:function(){return r}});var r=function(){function t(){this.listeners=[]}var e=t.prototype;return e.subscribe=function(t){var e=this,n=t||function(){};return this.listeners.push(n),this.onSubscribe(),function(){e.listeners=e.listeners.filter(function(t){return t!==n}),e.onUnsubscribe()}},e.hasListeners=function(){return this.listeners.length>0},e.onSubscribe=function(){},e.onUnsubscribe=function(){},t}()},96604:function(){},68200:function(t,e,n){"use strict";n.d(e,{A4:function(){return Q},G9:function(){return S},Gh:function(){return R},I6:function(){return f},Kp:function(){return c},PN:function(){return o},Q$:function(){return function t(e,n){if(e===n)return e;var r=Array.isArray(e)&&Array.isArray(n);if(r||g(e)&&g(n)){for(var i=r?e.length:Object.keys(e).length,u=r?n:Object.keys(n),s=u.length,o=r?[]:{},a=0,c=0;c<s;c++){var l=r?c:u[c];o[l]=t(e[l],n[l]),o[l]===e[l]&&a++}return i===s&&a===i?e:o}return n}},Rm:function(){return p},SE:function(){return s},VS:function(){return b},X7:function(){return v},ZT:function(){return u},_v:function(){return l},_x:function(){return d},lV:function(){return h},mc:function(){return a},sk:function(){return i},to:function(){return m},yF:function(){return y}});var r=n(1119),i="undefined"==typeof window;function u(){}function s(t,e){return"function"==typeof t?t(e):t}function o(t){return"number"==typeof t&&t>=0&&t!==1/0}function a(t){return Array.isArray(t)?t:[t]}function c(t,e){return Math.max(t+(e||0)-Date.now(),0)}function l(t,e,n){return O(t)?"function"==typeof e?(0,r.Z)({},n,{queryKey:t,queryFn:e}):(0,r.Z)({},e,{queryKey:t}):t}function h(t,e,n){return O(t)?"function"==typeof e?(0,r.Z)({},n,{mutationKey:t,mutationFn:e}):(0,r.Z)({},e,{mutationKey:t}):"function"==typeof t?(0,r.Z)({},e,{mutationFn:t}):(0,r.Z)({},t)}function f(t,e,n){return O(t)?[(0,r.Z)({},e,{queryKey:t}),n]:[t||{},e]}function d(t,e){var n=t.active,r=t.exact,i=t.fetching,u=t.inactive,s=t.predicate,o=t.queryKey,a=t.stale;if(O(o)){if(r){if(e.queryHash!==p(o,e.options))return!1}else if(!m(e.queryKey,o))return!1}var c=!0===n&&!0===u||null==n&&null==u?"all":!1===n&&!1===u?"none":(null!=n?n:!u)?"active":"inactive";if("none"===c)return!1;if("all"!==c){var l=e.isActive();if("active"===c&&!l||"inactive"===c&&l)return!1}return("boolean"!=typeof a||e.isStale()===a)&&("boolean"!=typeof i||e.isFetching()===i)&&(!s||!!s(e))}function v(t,e){var n=t.exact,r=t.fetching,i=t.predicate,u=t.mutationKey;if(O(u)){if(!e.options.mutationKey)return!1;if(n){if(y(e.options.mutationKey)!==y(u))return!1}else if(!m(e.options.mutationKey,u))return!1}return("boolean"!=typeof r||"loading"===e.state.status===r)&&(!i||!!i(e))}function p(t,e){return((null==e?void 0:e.queryKeyHashFn)||y)(t)}function y(t){return JSON.stringify(a(t),function(t,e){return g(e)?Object.keys(e).sort().reduce(function(t,n){return t[n]=e[n],t},{}):e})}function m(t,e){return function t(e,n){return e===n||typeof e==typeof n&&!!e&&!!n&&"object"==typeof e&&"object"==typeof n&&!Object.keys(n).some(function(r){return!t(e[r],n[r])})}(a(t),a(e))}function b(t,e){if(t&&!e||e&&!t)return!1;for(var n in t)if(t[n]!==e[n])return!1;return!0}function g(t){if(!C(t))return!1;var e=t.constructor;if(void 0===e)return!0;var n=e.prototype;return!!(C(n)&&n.hasOwnProperty("isPrototypeOf"))}function C(t){return"[object Object]"===Object.prototype.toString.call(t)}function O(t){return"string"==typeof t||Array.isArray(t)}function R(t){return new Promise(function(e){setTimeout(e,t)})}function Q(t){Promise.resolve().then(t).catch(function(t){return setTimeout(function(){throw t})})}function S(){if("function"==typeof AbortController)return new AbortController}},86484:function(t,e,n){"use strict";n.d(e,{QueryClient:function(){return r.QueryClient},QueryClientProvider:function(){return i.QueryClientProvider},useMutation:function(){return i.useMutation},useQuery:function(){return i.useQuery},useQueryClient:function(){return i.useQueryClient}});var r=n(82358);n.o(r,"QueryClientProvider")&&n.d(e,{QueryClientProvider:function(){return r.QueryClientProvider}}),n.o(r,"useMutation")&&n.d(e,{useMutation:function(){return r.useMutation}}),n.o(r,"useQuery")&&n.d(e,{useQuery:function(){return r.useQuery}}),n.o(r,"useQueryClient")&&n.d(e,{useQueryClient:function(){return r.useQueryClient}});var i=n(70906)},70906:function(t,e,n){"use strict";n.d(e,{QueryClientProvider:function(){return d},useMutation:function(){return O},useQuery:function(){return T},useQueryClient:function(){return f}});var r,i=n(53312),u=n(54887).unstable_batchedUpdates;i.V.setBatchNotifyFunction(u);var s=n(3765),o=console;(0,s.E)(o);var a=n(2265),c=a.createContext(void 0),l=a.createContext(!1);function h(t){return t&&"undefined"!=typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=c),window.ReactQueryClientContext):c}var f=function(){var t=a.useContext(h(a.useContext(l)));if(!t)throw Error("No QueryClient set, use QueryClientProvider to set one");return t},d=function(t){var e=t.client,n=t.contextSharing,r=void 0!==n&&n,i=t.children;a.useEffect(function(){return e.mount(),function(){e.unmount()}},[e]);var u=h(r);return a.createElement(l.Provider,{value:r},a.createElement(u.Provider,{value:e},i))},v=n(1119),p=n(68200),y=n(88671),m=n(41362),b=n(1063),g=function(t){function e(e,n){var r;return(r=t.call(this)||this).client=e,r.setOptions(n),r.bindMethods(),r.updateResult(),r}(0,y.Z)(e,t);var n=e.prototype;return n.bindMethods=function(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},n.setOptions=function(t){this.options=this.client.defaultMutationOptions(t)},n.onUnsubscribe=function(){if(!this.listeners.length){var t;null==(t=this.currentMutation)||t.removeObserver(this)}},n.onMutationUpdate=function(t){this.updateResult();var e={listeners:!0};"success"===t.type?e.onSuccess=!0:"error"===t.type&&(e.onError=!0),this.notify(e)},n.getCurrentResult=function(){return this.currentResult},n.reset=function(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},n.mutate=function(t,e){return this.mutateOptions=e,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,(0,v.Z)({},this.options,{variables:void 0!==t?t:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},n.updateResult=function(){var t=this.currentMutation?this.currentMutation.state:(0,m.R)(),e=(0,v.Z)({},t,{isLoading:"loading"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset});this.currentResult=e},n.notify=function(t){var e=this;i.V.batch(function(){e.mutateOptions&&(t.onSuccess?(null==e.mutateOptions.onSuccess||e.mutateOptions.onSuccess(e.currentResult.data,e.currentResult.variables,e.currentResult.context),null==e.mutateOptions.onSettled||e.mutateOptions.onSettled(e.currentResult.data,null,e.currentResult.variables,e.currentResult.context)):t.onError&&(null==e.mutateOptions.onError||e.mutateOptions.onError(e.currentResult.error,e.currentResult.variables,e.currentResult.context),null==e.mutateOptions.onSettled||e.mutateOptions.onSettled(void 0,e.currentResult.error,e.currentResult.variables,e.currentResult.context))),t.listeners&&e.listeners.forEach(function(t){t(e.currentResult)})})},e}(b.l);function C(t,e,n){return"function"==typeof e?e.apply(void 0,n):"boolean"==typeof e?e:!!t}function O(t,e,n){var r=a.useRef(!1),u=a.useState(0)[1],s=(0,p.lV)(t,e,n),o=f(),c=a.useRef();c.current?c.current.setOptions(s):c.current=new g(o,s);var l=c.current.getCurrentResult();a.useEffect(function(){r.current=!0;var t=c.current.subscribe(i.V.batchCalls(function(){r.current&&u(function(t){return t+1})}));return function(){r.current=!1,t()}},[]);var h=a.useCallback(function(t,e){c.current.mutate(t,e).catch(p.ZT)},[]);if(l.error&&C(void 0,c.current.options.useErrorBoundary,[l.error]))throw l.error;return(0,v.Z)({},l,{mutate:h,mutateAsync:l.mutate})}var R=n(71230),Q=n(86348),S=function(t){function e(e,n){var r;return(r=t.call(this)||this).client=e,r.options=n,r.trackedProps=[],r.selectError=null,r.bindMethods(),r.setOptions(n),r}(0,y.Z)(e,t);var n=e.prototype;return n.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},n.onSubscribe=function(){1===this.listeners.length&&(this.currentQuery.addObserver(this),P(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},n.onUnsubscribe=function(){this.listeners.length||this.destroy()},n.shouldFetchOnReconnect=function(){return q(this.currentQuery,this.options,this.options.refetchOnReconnect)},n.shouldFetchOnWindowFocus=function(){return q(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},n.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},n.setOptions=function(t,e){var n=this.options,r=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=n.queryKey),this.updateQuery();var i=this.hasListeners();i&&F(this.currentQuery,r,this.options,n)&&this.executeFetch(),this.updateResult(e),i&&(this.currentQuery!==r||this.options.enabled!==n.enabled||this.options.staleTime!==n.staleTime)&&this.updateStaleTimeout();var u=this.computeRefetchInterval();i&&(this.currentQuery!==r||this.options.enabled!==n.enabled||u!==this.currentRefetchInterval)&&this.updateRefetchInterval(u)},n.getOptimisticResult=function(t){var e=this.client.defaultQueryObserverOptions(t),n=this.client.getQueryCache().build(this.client,e);return this.createResult(n,e)},n.getCurrentResult=function(){return this.currentResult},n.trackResult=function(t,e){var n=this,r={},i=function(t){n.trackedProps.includes(t)||n.trackedProps.push(t)};return Object.keys(t).forEach(function(e){Object.defineProperty(r,e,{configurable:!1,enumerable:!0,get:function(){return i(e),t[e]}})}),(e.useErrorBoundary||e.suspense)&&i("error"),r},n.getNextResult=function(t){var e=this;return new Promise(function(n,r){var i=e.subscribe(function(e){e.isFetching||(i(),e.isError&&(null==t?void 0:t.throwOnError)?r(e.error):n(e))})})},n.getCurrentQuery=function(){return this.currentQuery},n.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},n.refetch=function(t){return this.fetch((0,v.Z)({},t,{meta:{refetchPage:null==t?void 0:t.refetchPage}}))},n.fetchOptimistic=function(t){var e=this,n=this.client.defaultQueryObserverOptions(t),r=this.client.getQueryCache().build(this.client,n);return r.fetch().then(function(){return e.createResult(r,n)})},n.fetch=function(t){var e=this;return this.executeFetch(t).then(function(){return e.updateResult(),e.currentResult})},n.executeFetch=function(t){this.updateQuery();var e=this.currentQuery.fetch(this.options,t);return(null==t?void 0:t.throwOnError)||(e=e.catch(p.ZT)),e},n.updateStaleTimeout=function(){var t=this;if(this.clearStaleTimeout(),!p.sk&&!this.currentResult.isStale&&(0,p.PN)(this.options.staleTime)){var e=(0,p.Kp)(this.currentResult.dataUpdatedAt,this.options.staleTime);this.staleTimeoutId=setTimeout(function(){t.currentResult.isStale||t.updateResult()},e+1)}},n.computeRefetchInterval=function(){var t;return"function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(t=this.options.refetchInterval)&&t},n.updateRefetchInterval=function(t){var e=this;this.clearRefetchInterval(),this.currentRefetchInterval=t,!p.sk&&!1!==this.options.enabled&&(0,p.PN)(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval(function(){(e.options.refetchIntervalInBackground||R.j.isFocused())&&e.executeFetch()},this.currentRefetchInterval))},n.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},n.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},n.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},n.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},n.createResult=function(t,e){var n,r,i=this.currentQuery,u=this.options,o=this.currentResult,a=this.currentResultState,c=this.currentResultOptions,l=t!==i,h=l?t.state:this.currentQueryInitialState,f=l?this.currentResult:this.previousQueryResult,d=t.state,v=d.dataUpdatedAt,y=d.error,m=d.errorUpdatedAt,b=d.isFetching,g=d.status,C=!1,O=!1;if(e.optimisticResults){var R=this.hasListeners(),Q=!R&&P(t,e),S=R&&F(t,i,e,u);(Q||S)&&(b=!0,v||(g="loading"))}if(e.keepPreviousData&&!d.dataUpdateCount&&(null==f?void 0:f.isSuccess)&&"error"!==g)n=f.data,v=f.dataUpdatedAt,g=f.status,C=!0;else if(e.select&&void 0!==d.data){if(o&&d.data===(null==a?void 0:a.data)&&e.select===this.selectFn)n=this.selectResult;else try{this.selectFn=e.select,n=e.select(d.data),!1!==e.structuralSharing&&(n=(0,p.Q$)(null==o?void 0:o.data,n)),this.selectResult=n,this.selectError=null}catch(t){(0,s.j)().error(t),this.selectError=t}}else n=d.data;if(void 0!==e.placeholderData&&void 0===n&&("loading"===g||"idle"===g)){if((null==o?void 0:o.isPlaceholderData)&&e.placeholderData===(null==c?void 0:c.placeholderData))r=o.data;else if(r="function"==typeof e.placeholderData?e.placeholderData():e.placeholderData,e.select&&void 0!==r)try{r=e.select(r),!1!==e.structuralSharing&&(r=(0,p.Q$)(null==o?void 0:o.data,r)),this.selectError=null}catch(t){(0,s.j)().error(t),this.selectError=t}void 0!==r&&(g="success",n=r,O=!0)}return this.selectError&&(y=this.selectError,n=this.selectResult,m=Date.now(),g="error"),{status:g,isLoading:"loading"===g,isSuccess:"success"===g,isError:"error"===g,isIdle:"idle"===g,data:n,dataUpdatedAt:v,error:y,errorUpdatedAt:m,failureCount:d.fetchFailureCount,errorUpdateCount:d.errorUpdateCount,isFetched:d.dataUpdateCount>0||d.errorUpdateCount>0,isFetchedAfterMount:d.dataUpdateCount>h.dataUpdateCount||d.errorUpdateCount>h.errorUpdateCount,isFetching:b,isRefetching:b&&"loading"!==g,isLoadingError:"error"===g&&0===d.dataUpdatedAt,isPlaceholderData:O,isPreviousData:C,isRefetchError:"error"===g&&0!==d.dataUpdatedAt,isStale:E(t,e),refetch:this.refetch,remove:this.remove}},n.shouldNotifyListeners=function(t,e){if(!e)return!0;var n=this.options,r=n.notifyOnChangeProps,i=n.notifyOnChangePropsExclusions;if(!r&&!i||"tracked"===r&&!this.trackedProps.length)return!0;var u="tracked"===r?this.trackedProps:r;return Object.keys(t).some(function(n){var r=t[n]!==e[n],s=null==u?void 0:u.some(function(t){return t===n}),o=null==i?void 0:i.some(function(t){return t===n});return r&&!o&&(!u||s)})},n.updateResult=function(t){var e=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!(0,p.VS)(this.currentResult,e)){var n={cache:!0};(null==t?void 0:t.listeners)!==!1&&this.shouldNotifyListeners(this.currentResult,e)&&(n.listeners=!0),this.notify((0,v.Z)({},n,t))}},n.updateQuery=function(){var t=this.client.getQueryCache().build(this.client,this.options);if(t!==this.currentQuery){var e=this.currentQuery;this.currentQuery=t,this.currentQueryInitialState=t.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==e||e.removeObserver(this),t.addObserver(this))}},n.onQueryUpdate=function(t){var e={};"success"===t.type?e.onSuccess=!0:"error"!==t.type||(0,Q.DV)(t.error)||(e.onError=!0),this.updateResult(e),this.hasListeners()&&this.updateTimers()},n.notify=function(t){var e=this;i.V.batch(function(){t.onSuccess?(null==e.options.onSuccess||e.options.onSuccess(e.currentResult.data),null==e.options.onSettled||e.options.onSettled(e.currentResult.data,null)):t.onError&&(null==e.options.onError||e.options.onError(e.currentResult.error),null==e.options.onSettled||e.options.onSettled(void 0,e.currentResult.error)),t.listeners&&e.listeners.forEach(function(t){t(e.currentResult)}),t.cache&&e.client.getQueryCache().notify({query:e.currentQuery,type:"observerResultsUpdated"})})},e}(b.l);function P(t,e){return!1!==e.enabled&&!t.state.dataUpdatedAt&&!("error"===t.state.status&&!1===e.retryOnMount)||t.state.dataUpdatedAt>0&&q(t,e,e.refetchOnMount)}function q(t,e,n){if(!1!==e.enabled){var r="function"==typeof n?n(t):n;return"always"===r||!1!==r&&E(t,e)}return!1}function F(t,e,n,r){return!1!==n.enabled&&(t!==e||!1===r.enabled)&&(!n.suspense||"error"!==t.state.status)&&E(t,n)}function E(t,e){return t.isStaleByTime(e.staleTime)}var w=a.createContext((r=!1,{clearReset:function(){r=!1},reset:function(){r=!0},isReset:function(){return r}}));function T(t,e,n){return function(t,e){var n=a.useRef(!1),r=a.useState(0)[1],u=f(),s=a.useContext(w),o=u.defaultQueryObserverOptions(t);o.optimisticResults=!0,o.onError&&(o.onError=i.V.batchCalls(o.onError)),o.onSuccess&&(o.onSuccess=i.V.batchCalls(o.onSuccess)),o.onSettled&&(o.onSettled=i.V.batchCalls(o.onSettled)),o.suspense&&("number"!=typeof o.staleTime&&(o.staleTime=1e3),0===o.cacheTime&&(o.cacheTime=1)),(o.suspense||o.useErrorBoundary)&&!s.isReset()&&(o.retryOnMount=!1);var c=a.useState(function(){return new e(u,o)})[0],l=c.getOptimisticResult(o);if(a.useEffect(function(){n.current=!0,s.clearReset();var t=c.subscribe(i.V.batchCalls(function(){n.current&&r(function(t){return t+1})}));return c.updateResult(),function(){n.current=!1,t()}},[s,c]),a.useEffect(function(){c.setOptions(o,{listeners:!1})},[o,c]),o.suspense&&l.isLoading)throw c.fetchOptimistic(o).then(function(t){var e=t.data;null==o.onSuccess||o.onSuccess(e),null==o.onSettled||o.onSettled(e,null)}).catch(function(t){s.clearReset(),null==o.onError||o.onError(t),null==o.onSettled||o.onSettled(void 0,t)});if(l.isError&&!s.isReset()&&!l.isFetching&&C(o.suspense,o.useErrorBoundary,[l.error,c.getCurrentQuery()]))throw l.error;return"tracked"===o.notifyOnChangeProps&&(l=c.trackResult(l,o)),l}((0,p._v)(t,e,n),S)}}}]);