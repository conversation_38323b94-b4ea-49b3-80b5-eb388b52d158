"use client";

import { Button, Container, Input } from "@mui/material";
import BookIcon from "@/assets/images/icons/BookIcon.svg";
import SvgSearchIcon from "@/assets/images/icons/searchIcon.svg";

import { useTranslation } from "react-i18next";

function GloassaryBanner({ bannerImg, height, altImg }) {
  const { t } = useTranslation();

  const letters = [
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
  ];
  return (
    <div
      id="glossary-banner"
      className={"center-banner"}
      style={{ backgroundImage: `url(${bannerImg.src})`, height: height }}
    >
      {altImg && (
        <img
          width={0}
          height={0}
          alt={altImg}
          src=""
          style={{ display: "none" }}
          loading="lazy"
        />
      )}
      <Container className="top-section custom-max-width">
        <BookIcon />
        <h1 className="heading-h1 text-white">Global Work Glossary</h1>
        <div className="glossary-search">
          <Input
            className="glossary-search-input"
            type="text"
            placeholder="What are you looking for ?"
          />
          <Button className="btn-search">
            <SvgSearchIcon />
          </Button>
        </div>
        <div className="letters">
          <p className="letter selected">#</p>
          {letters?.map((letter, index) => (
            <p className="letter" key={index}>
              {letter}
            </p>
          ))}
        </div>
      </Container>
    </div>
  );
}

export default GloassaryBanner;
