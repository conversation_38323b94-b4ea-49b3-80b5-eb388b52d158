"use client";

import { useState } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

import { Button, Container, Input } from "@mui/material";
import BookIcon from "@/assets/images/icons/BookIcon.svg";
import SvgSearchIcon from "@/assets/images/icons/searchIcon.svg";

function GlossaryBanner({
  bannerImg,
  height,
  altImg,
  letters = [],
  searchWord = "",
  hasContent = true,
}) {
  const searchQueryParams = new URLSearchParams();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const [keyword, setKeyword] = useState(
    searchWord || searchParams?.get("word") || ""
  );

  const handleSearchChange = (e) => {
    setKeyword(e.target.value);
  };

  const handleSearchClick = () => {
    const params = new URLSearchParams();
    if (keyword.trim()) {
      params.set("word", keyword.trim());
    }
    const queryString = params.toString();
    router.push(`${pathname}${queryString ? `?${queryString}` : ""}`);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSearchClick();
    }
  };

  const handleClearSearch = () => {
    setKeyword("");
    router.push(pathname);
  };

  return (
    <div
      id="glossary-banner"
      className={"center-banner"}
      style={{ backgroundImage: `url(${bannerImg.src})`, height: height }}
    >
      {altImg && (
        <img
          width={0}
          height={0}
          alt={altImg}
          src=""
          style={{ display: "none" }}
          loading="lazy"
        />
      )}
      <Container className="top-section custom-max-width">
        <BookIcon />
        <h1 className="heading-h1 text-white">Global Work Glossary</h1>
        <div className="glossary-search">
          <Input
            className="glossary-search-input"
            type="text"
            placeholder="What are you looking for ?"
            onChange={handleSearchChange}
            onKeyDown={handleKeyDown}
            value={keyword}
          />
          <Button className="btn-search" onClick={handleSearchClick}>
            <SvgSearchIcon />
          </Button>
          {keyword && (
            <Button
              className="btn-clear"
              onClick={handleClearSearch}
              size="small"
              style={{
                marginLeft: "8px",
                color: "white",
                textDecoration: "underline",
              }}
            >
              Clear
            </Button>
          )}
        </div>

        {/* Only show letters navigation if we have content */}
        {hasContent && letters?.length > 0 && (
          <div className="letters">
            <p className="letter selected">#</p>
            {letters.map((letter, index) => (
              <a href={`#${letter}`} className="letter" key={index}>
                {letter}
              </a>
            ))}
          </div>
        )}

        {/* Show message when no letters available */}
        {!hasContent && (
          <div className="letters-empty">
            <p
              style={{
                color: "white",
                opacity: 0.8,
                fontSize: "14px",
                marginTop: "20px",
              }}
            >
              {searchWord
                ? `No results found for "${searchWord}"`
                : "No glossary terms available"}
            </p>
          </div>
        )}
      </Container>
    </div>
  );
}

export default GlossaryBanner;
