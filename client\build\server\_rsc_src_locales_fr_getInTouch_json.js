"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_locales_fr_getInTouch_json";
exports.ids = ["_rsc_src_locales_fr_getInTouch_json"];
exports.modules = {

/***/ "(rsc)/./src/locales/fr/getInTouch.json":
/*!****************************************!*\
  !*** ./src/locales/fr/getInTouch.json ***!
  \****************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"getInTouch":"Contactez-nous ","altImg":"Contact Pentabell","fullName":"Nom Complet","description":"Nous sommes là pour vous aider.","firstName":"Prénom  ","lastName":"Nom","email":"Adresse e-mail","phone":"Numéro de téléphone","message":"Message","typeMessage":"Tapez votre message","youAre":"Vous êtes","subject":"Sujet","countryName":"Pays","submit":"Soumettre"}');

/***/ })

};
;