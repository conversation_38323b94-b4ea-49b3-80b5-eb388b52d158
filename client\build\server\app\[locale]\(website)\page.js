(()=>{var e={};e.id=5544,e.ids=[5544],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},99996:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var a,r=s(95746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let i=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,fill:"none"},e),a||(a=r.createElement("path",{fill:"#D69B19",d:"m10.438 17.334 7.152 7.151-1.885 1.886-10.371-10.37 10.37-10.372 1.886 1.886-7.152 7.152h16.229v2.667z"})))},9023:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var a,r=s(95746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let i=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,fill:"none"},e),a||(a=r.createElement("path",{fill:"#D69B19",d:"M21.562 14.667 14.41 7.514l1.885-1.886 10.371 10.37-10.37 10.372-1.886-1.886 7.152-7.152H5.333v-2.666z"})))},5551:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var a,r=s(95746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let i=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:40,height:41,fill:"none"},e),a||(a=r.createElement("path",{fill:"#fff",d:"m18.047 20.501 8.25 8.25-2.357 2.357L13.333 20.5 23.94 9.895l2.357 2.357z"})))},58996:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var a,r=s(95746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let i=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:40,height:41,fill:"none"},e),a||(a=r.createElement("path",{fill:"#fff",d:"m21.953 20.499-8.25-8.25 2.357-2.357L26.667 20.5 16.06 31.106l-2.357-2.358z"})))},76571:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a,r,n=s(95746);function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let l=e=>n.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:228,height:228,fill:"none"},e),a||(a=n.createElement("path",{fill:"#234567",d:"M19.044 102.08C17.794 69.1 43.16 38.014 77.568 30.436c42.79-9.473 84.404 16.77 92.271 57.96 2.868 14.947.735 29.402-6.176 43.015-1.617 3.157-.808 4.631 1.397 6.736 12.426 11.648 24.704 23.367 36.982 35.085 6.323 6.035 8.602 13.332 5.735 21.542-2.867 8.14-9.043 12.912-18.013 14.034-7.132.912-13.234-1.473-18.307-6.245-12.499-11.859-25.072-23.717-37.423-35.716-2.206-2.106-3.823-2.316-6.544-1.053-44.628 20.49-98.08-5.403-107.049-51.926-.735-3.859-.956-7.859-1.397-11.788m122.489 41.26c15.072-12.701 24.483-39.436 14.19-64.978-10.146-25.12-37.791-41.751-66.024-39.436-29.408 2.386-53.377 23.227-58.744 51.225-3.676 18.946 5.955 47.505 18.087 53.118v-8.139c.22-15.578 13.822-28.84 30.217-29.121 10.514-.14 21.101-.14 31.615 0 13.896.211 26.395 9.824 29.262 22.876 1.029 4.561.956 9.402 1.397 14.455m-64.259-26.735c-9.117.912-16.836 8.841-17.13 17.121-.221 5.754-.148 11.438 0 17.192 0 .912.735 2.175 1.543 2.737 17.793 11.718 48.452 11.788 66.685.14.956-.632 1.912-1.965 1.912-2.947.147-5.263.294-10.526-.073-15.789-.809-11.227-9.191-18.595-20.954-18.875-4.559-.07-9.044 0-13.602 0m46.246 43.084c-.295-.21-.662-.491-.956-.702.294.351.441.772.809 1.123 12.645 12.069 25.218 24.139 38.011 36.068 1.617 1.473 4.191 2.526 6.47 2.877 4.558.701 8.749-1.825 10.734-5.754 2.059-4.14 1.323-8.351-2.353-11.859-7.572-7.298-15.292-14.525-22.865-21.823-5.22-5.052-10.293-10.175-14.484-14.455-5.514 5.193-10.44 9.824-15.366 14.525"})),r||(r=n.createElement("path",{fill:"#234567",d:"M68.084 74.783C68.01 60.68 80.068 49.171 94.92 49.171c15.072 0 27.129 11.228 27.277 25.612.147 14.315-12.058 25.753-27.498 25.823-14.704 0-26.542-11.438-26.615-25.823m10.955 0c0 8.842 7.058 15.648 16.175 15.578 8.896-.07 16.248-7.017 16.248-15.297-.073-8.701-7.279-15.578-16.322-15.508-9.043 0-16.175 6.737-16.101 15.227"})))},3459:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a,r,n=s(95746);function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let l=e=>n.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:37,height:36,fill:"none"},e),a||(a=n.createElement("path",{fill:"#fff",d:"M3.507 16.118C3.31 10.91 7.315 6.002 12.747 4.806c6.757-1.496 13.328 2.648 14.57 9.151.453 2.36.116 4.643-.975 6.792-.256.499-.128.731.22 1.064 1.962 1.839 3.9 3.69 5.84 5.54.998.952 1.358 2.104.905 3.4-.453 1.286-1.428 2.04-2.844 2.217-1.126.144-2.09-.233-2.89-.986-1.974-1.873-3.96-3.745-5.91-5.64-.348-.332-.603-.365-1.033-.166-7.046 3.235-15.486-.853-16.902-8.199-.117-.61-.151-1.24-.221-1.861m19.34 6.515c2.38-2.006 3.866-6.227 2.24-10.26-1.601-3.967-5.966-6.592-10.424-6.227-4.643.377-8.428 3.668-9.275 8.088-.58 2.992.94 7.501 2.855 8.388v-1.286c.035-2.46 2.183-4.553 4.772-4.598a187 187 0 0 1 4.991 0c2.195.034 4.168 1.551 4.62 3.612.163.72.152 1.485.221 2.283M12.701 18.41c-1.44.144-2.658 1.396-2.705 2.704a42 42 0 0 0 0 2.714c0 .144.116.344.244.432 2.81 1.85 7.65 1.862 10.53.022.15-.1.301-.31.301-.465.023-.83.047-1.662-.011-2.493-.128-1.773-1.452-2.936-3.309-2.98-.72-.011-1.428 0-2.148 0m7.302 6.803-.15-.111c.046.055.07.122.127.177 1.997 1.906 3.982 3.811 6.002 5.695.255.233.662.399 1.021.454.72.111 1.382-.288 1.695-.908.325-.654.21-1.319-.371-1.873-1.196-1.152-2.415-2.293-3.61-3.445a122 122 0 0 1-2.287-2.283c-.871.82-1.649 1.551-2.427 2.294"})),r||(r=n.createElement("path",{fill:"#fff",d:"M11.25 11.808c-.011-2.227 1.892-4.044 4.237-4.044 2.38 0 4.284 1.773 4.307 4.044.023 2.26-1.904 4.066-4.341 4.077-2.322 0-4.191-1.806-4.203-4.077m1.73 0c0 1.396 1.114 2.47 2.554 2.46 1.404-.011 2.565-1.108 2.565-2.416-.011-1.374-1.149-2.46-2.577-2.448-1.428 0-2.554 1.063-2.542 2.404"})))},27114:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c}),s(12427),s(30962),s(23658),s(54864);var a=s(23191),r=s(88716),n=s(37922),i=s.n(n),l=s(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["[locale]",{children:["(website)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,12427)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\page.jsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,30962)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\layout.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,23658)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54864)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\not-found.js"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\page.jsx"],u="/[locale]/(website)/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/[locale]/(website)/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53030:(e,t,s)=>{Promise.resolve().then(s.bind(s,98139)),Promise.resolve().then(s.bind(s,933)),Promise.resolve().then(s.bind(s,46618)),Promise.resolve().then(s.bind(s,85903)),Promise.resolve().then(s.bind(s,76571)),Promise.resolve().then(s.bind(s,42250)),Promise.resolve().then(s.bind(s,39046)),Promise.resolve().then(s.bind(s,98377)),Promise.resolve().then(s.bind(s,95180)),Promise.resolve().then(s.bind(s,3459)),Promise.resolve().then(s.bind(s,31856)),Promise.resolve().then(s.bind(s,59545)),Promise.resolve().then(s.bind(s,41861)),Promise.resolve().then(s.bind(s,75113)),Promise.resolve().then(s.bind(s,359)),Promise.resolve().then(s.bind(s,70685)),Promise.resolve().then(s.bind(s,55071)),Promise.resolve().then(s.bind(s,28453)),Promise.resolve().then(s.bind(s,34904)),Promise.resolve().then(s.bind(s,44057)),Promise.resolve().then(s.bind(s,90702)),Promise.resolve().then(s.bind(s,9938)),Promise.resolve().then(s.bind(s,10694)),Promise.resolve().then(s.bind(s,90516)),Promise.resolve().then(s.bind(s,91384)),Promise.resolve().then(s.bind(s,90214)),Promise.resolve().then(s.bind(s,89044)),Promise.resolve().then(s.bind(s,96361)),Promise.resolve().then(s.bind(s,91732)),Promise.resolve().then(s.bind(s,2702)),Promise.resolve().then(s.bind(s,84396))},36690:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var a=s(27522),r=s(10326);let n=(0,a.Z)((0,r.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},10163:(e,t,s)=>{"use strict";s.d(t,{Z:()=>p});var a=s(17577),r=s(41135),n=s(88634),i=s(91703),l=s(2791),o=s(71685),c=s(97898);function d(e){return(0,c.ZP)("MuiDialogActions",e)}(0,o.Z)("MuiDialogActions",["root","spacing"]);var u=s(10326);let m=e=>{let{classes:t,disableSpacing:s}=e;return(0,n.Z)({root:["root",!s&&"spacing"]},d,t)},h=(0,i.ZP)("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:s}=e;return[t.root,!s.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:e})=>!e.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),p=a.forwardRef(function(e,t){let s=(0,l.i)({props:e,name:"MuiDialogActions"}),{className:a,disableSpacing:n=!1,...i}=s,o={...s,disableSpacing:n},c=m(o);return(0,u.jsx)(h,{className:(0,r.Z)(c.root,a),ownerState:o,ref:t,...i})})},28591:(e,t,s)=>{"use strict";s.d(t,{Z:()=>f});var a=s(17577),r=s(41135),n=s(88634),i=s(91703),l=s(30990),o=s(2791),c=s(71685),d=s(97898);function u(e){return(0,d.ZP)("MuiDialogContent",e)}(0,c.Z)("MuiDialogContent",["root","dividers"]);var m=s(64650),h=s(10326);let p=e=>{let{classes:t,dividers:s}=e;return(0,n.Z)({root:["root",s&&"dividers"]},u,t)},g=(0,i.ZP)("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:s}=e;return[t.root,s.dividers&&t.dividers]}})((0,l.Z)(({theme:e})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:e})=>e.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(e.vars||e).palette.divider}`,borderBottom:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:e})=>!e.dividers,style:{[`.${m.Z.root} + &`]:{paddingTop:0}}}]}))),f=a.forwardRef(function(e,t){let s=(0,o.i)({props:e,name:"MuiDialogContent"}),{className:a,dividers:n=!1,...i}=s,l={...s,dividers:n},c=p(l);return(0,h.jsx)(g,{className:(0,r.Z)(c.root,a),ownerState:l,ref:t,...i})})},98117:(e,t,s)=>{"use strict";s.d(t,{Z:()=>p});var a=s(17577),r=s(41135),n=s(88634),i=s(25609),l=s(91703),o=s(2791),c=s(64650),d=s(55733),u=s(10326);let m=e=>{let{classes:t}=e;return(0,n.Z)({root:["root"]},c.a,t)},h=(0,l.ZP)(i.default,{name:"MuiDialogTitle",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:"16px 24px",flex:"0 0 auto"}),p=a.forwardRef(function(e,t){let s=(0,o.i)({props:e,name:"MuiDialogTitle"}),{className:n,id:i,...l}=s,c=m(s),{titleId:p=i}=a.useContext(d.Z);return(0,u.jsx)(h,{component:"h2",className:(0,r.Z)(c.root,n),ownerState:s,ref:t,variant:"h6",id:i??p,...l})})},64650:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i,a:()=>n});var a=s(71685),r=s(97898);function n(e){return(0,r.ZP)("MuiDialogTitle",e)}let i=(0,a.Z)("MuiDialogTitle",["root"])},65368:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n}),s(17577);var a=s(27522),r=s(10326);let n=(0,a.Z)((0,r.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},38932:function(e,t,s){(function(e){"use strict";var t=/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?|janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,s=[/^janv/i,/^févr/i,/^mars/i,/^avr/i,/^mai/i,/^juin/i,/^juil/i,/^août/i,/^sept/i,/^oct/i,/^nov/i,/^déc/i];e.defineLocale("fr",{months:"janvier_f\xe9vrier_mars_avril_mai_juin_juillet_ao\xfbt_septembre_octobre_novembre_d\xe9cembre".split("_"),monthsShort:"janv._f\xe9vr._mars_avr._mai_juin_juil._ao\xfbt_sept._oct._nov._d\xe9c.".split("_"),monthsRegex:t,monthsShortRegex:t,monthsStrictRegex:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,monthsShortStrictRegex:/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?)/i,monthsParse:s,longMonthsParse:s,shortMonthsParse:s,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui \xe0] LT",nextDay:"[Demain \xe0] LT",nextWeek:"dddd [\xe0] LT",lastDay:"[Hier \xe0] LT",lastWeek:"dddd [dernier \xe0] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",w:"une semaine",ww:"%d semaines",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|)/,ordinal:function(e,t){switch(t){case"D":return e+(1===e?"er":"");default:case"M":case"Q":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}},week:{dow:1,doy:4}})})(s(57967))},43353:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let a=s(91174);s(10326),s(17577);let r=a._(s(77028));function n(e,t){var s;let a={loading:e=>{let{error:t,isLoading:s,pastDelay:a}=e;return null}};"function"==typeof e&&(a.loader=e);let n={...a,...t};return(0,r.default)({...n,modules:null==(s=n.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77028:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let a=s(10326),r=s(17577),n=s(933),i=s(46618);function l(e){return{default:e&&"default"in e?e.default:e}}let o={loader:()=>Promise.resolve(l(()=>null)),loading:null,ssr:!0},c=function(e){let t={...o,...e},s=(0,r.lazy)(()=>t.loader().then(l)),c=t.loading;function d(e){let l=c?(0,a.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,o=t.ssr?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.PreloadCss,{moduleIds:t.modules}),(0,a.jsx)(s,{...e})]}):(0,a.jsx)(n.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(s,{...e})});return(0,a.jsx)(r.Suspense,{fallback:l,children:o})}return d.displayName="LoadableComponent",d}},86851:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var a=s(10326),r=s(98139);let n=function(){return a.jsx("div",{className:"spinner",children:a.jsx(r.default,{})})}},10694:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var a=s(10326),r=s(90423),n=s(16027),i=s(87638),l=s(90943),o=s(78077),c=s(5394),d=s(76971),u=s(63568),m=s(17577),h=s(52210),p=s(9252);s(11148);var g=s(10123),f=s(91288),b=s(96672);let x={src:"/_next/static/media/get-in-touch.6f6e5b88.png"};var v=s(15082),j=s(87419),y=s(4563),w=s(55618),N=s(5926),P=s(18970);let A=function(){let[e,t]=(0,m.useState)(""),[s,A]=(0,m.useState)(!1),{t:_}=(0,h.$G)(),S=p.PhoneNumberUtil.getInstance(),C=(0,j.uu)(A,t),[M,Z]=(0,m.useState)(""),k=async(e,{resetForm:t})=>{try{await C.mutateAsync({firstName:e.firstName,lastName:e.lastName,email:e.email,phone:e.phone,message:e.message,to:process.env.NEXT_PUBLIC_EMAIL_FORM_DESTINATION,team:"digital",type:"getInTouch"}),Z(e.firstName),window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"home_page_form",button_id:"my_button"}),t()}catch(e){console.error("Error submitting form:",e)}},$=e=>{try{return S.isValidNumber(S.parseAndKeepRawInput(e))}catch(e){return!1}},E=g.Z_().test("is-valid-phone",_("validations:phoneFormat"),e=>$(e)),I=e=>(0,y.ZI)(e).shape({phone:E});return(0,a.jsxs)(r.default,{id:"get-in-touch",className:"custom-max-width",children:[a.jsx(P.Z,{}),s?a.jsx("div",{className:"section-guide",children:(0,a.jsxs)("div",{className:"form-success",children:[a.jsx(f.Z,{}),(0,a.jsxs)("p",{className:"sub-heading",children:[" ",_("messages:thankyou")," ",M," ",_("messages:messagesuccess")]})]})}):(0,a.jsxs)(n.default,{className:"container",container:!0,spacing:2,children:[(0,a.jsxs)(n.default,{item:!0,xs:12,sm:7,children:[a.jsx(u.J9,{initialValues:{firstName:"",lastName:"",email:"",phone:"",message:"",acceptTerms:!1},validationSchema:()=>I(_),onSubmit:k,children:({values:e,handleChange:s,errors:r,setFieldValue:m,touched:h})=>a.jsx(u.l0,{children:(0,a.jsxs)(n.default,{className:"container",container:!0,rowSpacing:4,columnSpacing:3,children:[(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(i.Z,{children:[a.jsx(l.Z,{className:"label-pentabell",children:_("getInTouch:firstName")}),a.jsx(o.Z,{className:"input-pentabell",placeholder:_("getInTouch:firstName"),variant:"standard",name:"firstName",value:e.firstName,onChange:s,autoComplete:"off",error:!!(r.firstName&&h.firstName)})]}),a.jsx(u.Bc,{name:"firstName",children:e=>a.jsx("span",{className:"error-span",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(i.Z,{children:[a.jsx(l.Z,{className:"label-pentabell",children:_("getInTouch:lastName")}),a.jsx(o.Z,{className:"input-pentabell",placeholder:_("getInTouch:lastName"),variant:"standard",name:"lastName",value:e.lastName,onChange:s,autoComplete:"off",error:!!(r.lastName&&h.lastName)})]}),a.jsx(u.Bc,{name:"lastName",children:e=>a.jsx("span",{className:"error-span",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(i.Z,{children:[a.jsx(l.Z,{className:"label-pentabell",children:_("getInTouch:email")}),a.jsx(o.Z,{className:"input-pentabell",placeholder:"<EMAIL>",variant:"standard",name:"email",value:e.email,onChange:s,autoComplete:"off",error:!!(r.email&&h.email)})]}),a.jsx(u.Bc,{name:"email",children:e=>a.jsx("span",{className:"error-span",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(i.Z,{children:[a.jsx(l.Z,{className:"label-pentabell",children:_("getInTouch:phone")}),a.jsx(b.sb,{defaultCountry:"fr",className:"input-pentabell",value:e.phone,name:"phone",placeholder:"Phone number",onChange:e=>{m("phone",e),t("")},flagComponent:e=>a.jsx(N.Z,{...e})})]}),a.jsx(u.Bc,{name:"phone",children:e=>a.jsx("span",{className:"error-span",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:12,children:[(0,a.jsxs)(i.Z,{children:[a.jsx(l.Z,{className:"label-pentabell",children:_("getInTouch:message")}),a.jsx(o.Z,{className:"input-pentabell",placeholder:_("getInTouch:typeMessage"),variant:"standard",name:"message",value:e.message,onChange:s,autoComplete:"off",error:!!(r.message&&h.message)})]}),a.jsx(u.Bc,{name:"message",children:e=>a.jsx("span",{className:"error-span",children:e})})]}),a.jsx(n.default,{item:!0,xs:12,sm:12,children:(0,a.jsxs)(i.Z,{children:[a.jsx(c.Z,{className:"checkbox-pentabell ",control:a.jsx(d.Z,{name:"acceptTerms",checked:e.acceptTerms,onChange:s,error:!!(r.acceptTerms&&h.acceptTerms)}),label:_("aiSourcingService:servicePageForm:formSubmissionAgreement")}),a.jsx(u.Bc,{name:"acceptTerms",children:e=>a.jsx("span",{className:"error-span",children:e})})]})}),a.jsx(n.default,{item:!0,xs:12,sm:12,children:a.jsx(v.default,{text:_("getInTouch:submit"),className:"btn btn-filled",type:"submit"})})]})})}),a.jsx(w.Z,{errMsg:e,success:s})]}),a.jsx(n.default,{item:!0,xs:12,sm:5,children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"heading-h1",children:_("getInTouch:getInTouch")}),a.jsx("p",{className:"sub-heading",children:_("getInTouch:description")}),a.jsx("img",{src:x.src,alt:_("getInTouch:altImg"),loading:"lazy"})]})})]})]})}},90516:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var a=s(10326),r=s(88441),n=s(90423),i=s(23743),l=s(52210),o=s(43353),c=s.n(o),d=s(26704),u=s(79780),m=s(15082),h=s(66964),p=s(90434),g=s(97980);let f=c()(async()=>{},{loadableGenerated:{modules:["components\\sections\\GlobalMap.jsx -> ./MpSvgComponent"]},ssr:!1}),b=function({locale:e,SERVICES:t,title:s,defaultService:o}){let{t:c}=(0,l.$G)(),b=["oil-gaz","energy","banking-insurance","transport","it-telecom"],x="",v=(e,t)=>{let s=document.querySelectorAll(".box-info");document.querySelectorAll(".pin").forEach(e=>e.classList.remove("selected")),s.forEach(e=>e.classList.remove("selected"));let a=document.getElementById(t);a.querySelectorAll(".pin").forEach(e=>e.classList.remove("selected"));let r=a.querySelector(".pin");if(r){r.classList.add("selected");let t=function(){let e;do e=b[Math.floor(Math.random()*b.length)];while(e===x);return x=e,e}();r.classList.add(t),document.getElementById(e).classList.add("selected")}},j=(0,i.Z)(),y=(0,r.Z)(j.breakpoints.down("sm"));return y?a.jsx(h.default,{title:c("homePage:s2:title"),subtitle:c("homePage:s2:description"),children:a.jsx(u.default,{SERVICES:t,title:s,defaultService:o})}):(0,a.jsxs)("div",{id:"global-map",children:[(0,a.jsxs)(n.default,{className:"custom-max-width",children:[a.jsx("h2",{className:"heading-h1 text-center text-white",children:c("homePage:s2:title")}),a.jsx("p",{className:"sub-heading text-center",children:c("homePage:s2:description")}),(0,a.jsxs)("div",{id:"btns-zone",children:[d.qm.map((e,t)=>a.jsx(m.default,{id:t,text:c(e.label),link:e.link,className:"btn btn-zone"},e.id)),a.jsx(p.default,{href:`/${g.Bi.contact.route}/#contact-page-form`,className:"btn btn-zone",children:c("contactUs:bureux:restOfTheWold")})]}),a.jsx("div",{id:"btns-country",children:d.Fu.map((t,s)=>y?a.jsx(m.default,{text:c(t.label),link:t.link,className:"btn btn-country"}):a.jsx(m.default,{text:c(t.label),onClick:()=>{"fr"===e?v(t.idFr,t.idPin):v(t.id,t.idPin)},className:"btn btn-country"},s))})]}),a.jsx(f,{handleClick:v,locale:e}),a.jsx("img",{width:0,height:0,alt:c("homePage:s2:map"),style:{display:"none"},loading:"lazy"}),a.jsx(u.default,{SERVICES:t,title:s,defaultService:o})]})}},89044:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(10326),r=s(90423),n=s(52210);let i=function(){let{t:e}=(0,n.$G)();return a.jsx("div",{id:"new-year-countdown",children:a.jsx(r.default,{className:"new-year-container",children:(0,a.jsxs)("div",{className:"new-year-content",children:[a.jsx("div",{className:"element"}),(0,a.jsxs)("div",{className:"text-section",children:[a.jsx("p",{className:"sm-title",children:e("global:isoSection:smYellowTitle")}),a.jsx("h2",{className:"bg-title",children:e("global:isoSection:bgWhiteTitle")}),a.jsx("p",{className:"content",children:e("global:isoSection:content")})]})]})})})}},91384:(e,t,s)=>{"use strict";s.d(t,{default:()=>b});var a=s(10326),r=s(90423),n=s(98139),i=s(16027),l=s(34039),o=s(18835),c=s(4766),d=s(15082),u=s(35045),m=s(83969),h=s(64504),p=s(52210),g=s(90434),f=s(97980);let b=function({language:e}){let{t}=(0,p.$G)(),{data:s,isLoading:b,error:x}=(0,h.bh)({language:e,isThreeLastArticles:"true"});return(0,a.jsxs)(r.default,{id:"insights-section",className:"custom-max-width",children:[a.jsx("h2",{className:"heading-h1 text-center",children:t("payrollService:insightSection:title")}),a.jsx("p",{className:"sub-heading text-center",children:t("payrollService:insightSection:description")}),b?a.jsx("div",{className:"loading-spinner",children:a.jsx(n.default,{})}):x?a.jsx("p",{className:"error-message text-center",children:"Error loading articles. Please try again later."}):a.jsx(i.default,{container:!0,spacing:4,children:s?.articles?.map(s=>a.jsx(i.default,{className:"blog-item",item:!0,xs:12,sm:6,md:4,children:a.jsxs(l.Z,{className:"card",children:[s?.category?.name&&a.jsx("p",{className:"label-category",children:s?.category?.name}),a.jsx(o.Z,{className:"card-image",component:"img",image:s.versions[0].image?`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${s.versions[0].image}`:u.Z.src,loading:"lazy",alt:s.versions[0]?.title,onClick:()=>window.location.href=`/${f.Bi.blog.route}/${s.versions[0]?.url}/`}),a.jsxs(c.Z,{className:"card-content",children:[a.jsx(g.default,{className:"blog-title",locale:"en"===e?"en":"fr",href:`${"en"===e?`/${f.Bi.blog.route}/${s.versions[0]?.url}`:`/${e}/${f.Bi.blog.route}/${s.versions[0]?.url}`}/`,children:s.versions[0]?.title}),a.jsx("p",{className:"blog-description",children:s.versions[0]?.metaDescription||"Lorem ipsum dolor sit amet consectetur."})]}),a.jsx(d.default,{text:t("global:readMore"),className:"btn btn-ghost",icon:a.jsx(m.default,{}),link:`/${f.Bi.blog.route}/${s.versions[0]?.url}`,aHref:!0})]})},s._id))}),a.jsx(i.default,{className:"btn-view-more",children:a.jsx(d.default,{text:t("global:viewMore"),link:"/blog",className:"btn btn-filled",aHref:!0})})]})}},90214:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(10326),r=s(52210),n=s(90423),i=s(2931);let l=function({locale:e}){let{t}=(0,r.$G)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(n.default,{className:"responsive-row-title-text custom-max-width",children:[a.jsx("div",{className:"grid-item",children:a.jsx("h1",{className:"section heading-h1",children:t("homePage:introSection:title")})}),(0,a.jsxs)("div",{className:"grid-item",children:[a.jsx("p",{className:"sub-heading bold",children:t("homePage:introSection:description")}),a.jsx("p",{className:"sub-heading",children:t("homePage:introSection:description1")})]})]}),a.jsx(i.default,{locale:e})]})}},96361:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var a=s(10326),r=s(17577),n=s(90423),i=s(52210),l=s(481),o=s(55612),c=s.n(o),d=s(19191),u=s(75632),m=s(15082);s(70580),s(50967);var h=s(97980),p=s(86851);let g=function({language:e}){let[t]=(0,l.Z)({loop:!1,align:"start"}),[s,o]=(0,r.useState)([]),[g,f]=(0,r.useState)(!0),[b,x]=(0,r.useState)(null),{t:v}=(0,i.$G)(),[j,y]=(0,r.useState)({urgent:void 0});c().registerLocale(d);let w=e=>{void 0!==e.urgent?y(t=>({...t,urgent:e.urgent})):y(e=>({...e,urgent:void 0}))};return(0,a.jsxs)(n.default,{id:"latest-offers",className:"custom-max-width",children:[a.jsx("h2",{className:"heading-h1 text-center",children:v("homePage:s3:title")}),(0,a.jsxs)("div",{id:"filter-btns",children:[a.jsx(m.default,{onClick:()=>w({urgent:!j.urgent}),text:v("homePage:s3:btu"),className:`${j.urgent?"btn btn-filter selected":"btn btn-outlined"}`}),a.jsx(m.default,{text:v("homePage:s3:btlast"),onClick:()=>w({urgent:void 0}),className:`${void 0===j.urgent?"btn btn-filter selected":"btn btn-outlined"}`})]}),a.jsx("section",{className:"embla",id:"jobs__slider",children:a.jsx("div",{className:"embla__viewport",ref:t,children:a.jsx("div",{className:"embla__container",children:g?a.jsx(p.Z,{}):s?.length>0?s.map(t=>a.jsx(u.Z,{opportunity:t,language:e,website:!0},t?._id)):a.jsx("p",{className:"no-results-message",children:v("opportunities:noOpportunitiesFound")})})})}),a.jsx("div",{className:"center-div",children:a.jsx(m.default,{text:v("homePage:s3:all"),link:`/${h.Bi.opportunities.route}`,className:"btn btn-filled"})})]})}},2931:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var a=s(10326),r=s(90423),n=s(16027),i=s(52210);let l=function({locale:e}){let{t}=(0,i.$G)();return a.jsx(r.default,{className:"custom-max-width",children:(0,a.jsxs)(n.default,{className:"container",container:!0,spacing:2,children:[a.jsx(n.default,{item:!0,xs:12,sm:12,children:(0,a.jsxs)("h2",{className:"heading-h2",children:[t("homePage:introSection:numbers")," ",a.jsx("br",{}),(0,a.jsxs)("span",{className:"sub-heading update-span",children:[t("homePage:introSection:update")," ",t("homePage:introSection:date")]})]})}),a.jsx(n.default,{item:!0,xs:12,sm:12,className:"numbers",children:(0,a.jsxs)("div",{className:"stats-container-pentabell",children:[(0,a.jsxs)("div",{className:"stat-box purple",children:[a.jsx("span",{className:"corner top-left"}),a.jsx("span",{className:"corner top-right"}),a.jsx("span",{className:"corner bottom-left"}),a.jsx("span",{className:"corner bottom-right"}),a.jsx("p",{className:"numbers-pentabell",children:"15K+"}),a.jsx("p",{children:t("homePage:introSection:hiredExperts")})]}),(0,a.jsxs)("div",{className:"stat-box red",children:[a.jsx("span",{className:"corner top-left"}),a.jsx("span",{className:"corner top-right"}),a.jsx("span",{className:"corner bottom-left"}),a.jsx("span",{className:"corner bottom-right"}),a.jsx("p",{className:"numbers-pentabell",children:"17K+"}),a.jsx("p",{children:t("homePage:introSection:performedMissions")})]}),(0,a.jsxs)("div",{className:"stat-box yellow",children:[a.jsx("span",{className:"corner top-left"}),a.jsx("span",{className:"corner top-right"}),a.jsx("span",{className:"corner bottom-left"}),a.jsx("span",{className:"corner bottom-right"}),a.jsx("p",{className:"numbers-pentabell",children:"61"}),a.jsx("p",{children:t("homePage:introSection:nationalities")})]}),(0,a.jsxs)("div",{className:"stat-box green",children:[a.jsx("span",{className:"corner top-left"}),a.jsx("span",{className:"corner top-right"}),a.jsx("span",{className:"corner bottom-left"}),a.jsx("span",{className:"corner bottom-right"}),a.jsx("p",{className:"numbers-pentabell",children:"187"}),a.jsx("p",{children:t("homePage:introSection:satisfiedClients")})]}),(0,a.jsxs)("div",{className:"stat-box blue",children:[a.jsx("span",{className:"corner top-left"}),a.jsx("span",{className:"corner top-right"}),a.jsx("span",{className:"corner bottom-left"}),a.jsx("span",{className:"corner bottom-right"}),a.jsx("p",{className:"numbers-pentabell",children:"265K+"}),a.jsx("p",{children:t("homePage:introSection:cvs")})]})]})})]})})}},2702:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var a,r,n,i=s(10326),l=s(90423),o=s(16027),c=s(33198),d=s(17577),u=s(99996),m=s(9023),h=s(95746);function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let g=e=>h.createElement("svg",p({xmlns:"http://www.w3.org/2000/svg",width:32,height:24,fill:"none"},e),a||(a=h.createElement("path",{fill:"#D69B19",d:"M.954 24v-6.903q0-2.94 1.15-6.009a26.3 26.3 0 0 1 3.037-5.785Q7.026 2.587 9.23.765l6.01 3.548a31.6 31.6 0 0 0-2.941 5.88q-1.119 3.069-1.119 6.84V24zm16.14 0v-6.903q0-2.94 1.15-6.009a26.3 26.3 0 0 1 3.037-5.785q1.885-2.716 4.09-4.538l6.009 3.548a31.6 31.6 0 0 0-2.94 5.88q-1.119 3.069-1.119 6.84V24z"})));function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let b=e=>h.createElement("svg",f({xmlns:"http://www.w3.org/2000/svg",width:28,height:28,fill:"none"},e),r||(r=h.createElement("path",{stroke:"#D69B19",strokeWidth:2,d:"M14.489 20.43 14 20.157l-.488.273-6.337 3.548 1.415-7.124.11-.549-.412-.38-5.332-4.93 7.212-.855.556-.067.235-.508L14 2.971l3.042 6.594.235.508.555.066 7.212.856-5.332 4.93-.41.38.109.55 1.415 7.123z"})));function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let v=e=>h.createElement("svg",x({xmlns:"http://www.w3.org/2000/svg",width:28,height:26,fill:"none"},e),n||(n=h.createElement("path",{fill:"#D69B19",d:"m14 21.303-8.229 4.606 1.838-9.25-6.923-6.402 9.364-1.11L14 .582l3.95 8.563 9.365 1.11-6.924 6.404 1.838 9.25z"}))),j=({rating:e})=>{let t=Math.min(Math.max(e,0),5);return i.jsx("div",{className:"star-fill",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>s<t?i.jsx(v,{},s):i.jsx(b,{},s))})};var y=s(5248);let w=function(){let e=(0,d.useRef)(null),[t,s]=(0,d.useState)(null),a=t=>{if(!e.current)return;let s=e.current,a=s.firstChild;if(!a)return;let r=a.offsetWidth+40,n=s.scrollWidth-s.clientWidth,i=s.scrollLeft+("left"===t?-r:r);i=Math.max(0,Math.min(i,n)),s.scrollTo({left:i,behavior:"smooth"})};return i.jsx("div",{id:"what-they-say",children:i.jsx(l.default,{className:"custom-max-width",children:(0,i.jsxs)(o.default,{className:"container",container:!0,spacing:2,children:[i.jsx(o.default,{item:!0,xs:12,sm:12,children:i.jsx("h2",{className:"heading-h1 text-white text-center",children:"What they say"})}),i.jsx("div",{className:"say-section",ref:e,children:y.KK.map((e,s)=>(0,i.jsxs)("div",{className:`say-item ${s===t?"scale-up":"scale-down"}`,children:[i.jsx("div",{className:"guillemet",children:i.jsx(g,{})}),i.jsx("p",{className:"say-content",children:e.description}),i.jsx(j,{rating:e.rating}),(0,i.jsxs)("div",{className:"author",children:[i.jsx(c.Z,{sx:{width:70,height:70},src:e.image,children:i.jsx("span",{className:"author-icon",children:e.author.split(" ")[0][0].toUpperCase()})}),(0,i.jsxs)("div",{className:"info-author",children:[i.jsx("p",{className:"name",children:e.author}),i.jsx("p",{className:"quality",children:e.quality})]})]})]},e.id))}),(0,i.jsxs)("div",{className:"buttons",children:[i.jsx("button",{className:"scroll-button left","aria-label":"ArrowLeft",onClick:()=>a("left"),children:i.jsx(u.Z,{})}),i.jsx("button",{className:"scroll-button right","aria-label":"ArrowRight",onClick:()=>a("right"),children:i.jsx(m.Z,{})})]})]})})})}},84396:(e,t,s)=>{"use strict";s.d(t,{default:()=>y});var a,r=s(10326),n=s(481),i=s(52210),l=s(33469),o=s(17577),c=s.n(o),d=s(23743),u=s(88441),m=s(90423),h=s(52188),p=s(14245),g=s(15082),f=s(95746);function b(){return(b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let x=e=>f.createElement("svg",b({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none"},e),a||(a=f.createElement("path",{fill:"#fff",d:"M12.172 7 6.808 1.638 8.222.223 16 8l-7.778 7.778-1.414-1.414L12.172 9H0V7z"})));var v=s(5551),j=s(58996);let y=function({slides:e=[],options:t,slideId:s,isMobileSSR:a}){let{t:o}=(0,i.$G)(),f=(0,d.Z)(),b=(0,u.Z)(f.breakpoints.down("sm"))||a,[y,w]=(0,n.Z)(t,[(0,l.Z)({playOnInit:!b,delay:3500})]),{prevBtnDisabled:N,nextBtnDisabled:P,onPrevButtonClick:A,onNextButtonClick:_}=(0,p.XO)(w);return r.jsx("section",{className:"embla",id:s,children:(0,r.jsxs)("div",{className:"embla__viewport",ref:y,children:[r.jsx("div",{className:"embla__container",children:e.map((e,t)=>(0,r.jsxs)("div",{className:"embla__slide",children:[r.jsx("img",{alt:e.altImg,width:800,height:800,src:b?e.imgMobile:e.img,loading:0===t?"eager":"lazy"}),r.jsx(m.default,{className:"slide__container custom-max-width",children:(0,r.jsxs)("div",{className:"embla__slide__content",children:[r.jsx("p",{className:"embla__slide__title",children:function(e,t=5){if(!e)return"";let s=e.trim().split(/\s+/),a=[];for(let e=0;e<s.length;e+=t)a.push(s.slice(e,e+t).join(" "));return r.jsx(r.Fragment,{children:a.map((e,t)=>(0,r.jsxs)(c().Fragment,{children:[e,t<a.length-1&&r.jsx("br",{})]},t))})}(e.title,b?12:5)}),e.customLink?(0,r.jsxs)(h.default,{href:e.link,className:"btn btn-slider",children:[o("global:discoverBtn"),r.jsx(x,{})]}):e.link?r.jsx(g.default,{text:e.linkBtn,className:"explore-btn",link:e.link,samePage:!0,externalLink:!0,icon:r.jsx(x,{})}):r.jsx("br",{})]})})]},t))}),!!e.length&&r.jsx(m.default,{className:"embla__controls",children:(0,r.jsxs)("div",{className:"embla__buttons",children:[r.jsx(p.Q1,{children:r.jsx(v.Z,{}),onClick:A,disabled:N}),r.jsx(p.aW,{children:r.jsx(j.Z,{}),onClick:_,disabled:P})]})})]})})}},14245:(e,t,s)=>{"use strict";s.d(t,{Q1:()=>i,XO:()=>n,aW:()=>l});var a=s(10326),r=s(17577);let n=e=>{let[t,s]=(0,r.useState)(!0),[a,n]=(0,r.useState)(!0),i=(0,r.useCallback)(()=>{e&&e.scrollPrev()},[e]),l=(0,r.useCallback)(()=>{e&&e.scrollNext()},[e]),o=(0,r.useCallback)(e=>{s(!e.canScrollPrev()),n(!e.canScrollNext())},[]);return(0,r.useEffect)(()=>{e&&(o(e),e.on("reInit",o).on("select",o))},[e,o]),{prevBtnDisabled:t,nextBtnDisabled:a,onPrevButtonClick:i,onNextButtonClick:l}},i=e=>{let{children:t,...s}=e;return a.jsx("button",{className:"embla__button embla__button--prev","aria-label":"Previous button",type:"button",...s,children:t})},l=e=>{let{children:t,...s}=e;return a.jsx("button",{className:"embla__button embla__button--next","aria-label":"Next button",type:"button",...s,children:t})}},64504:(e,t,s)=>{"use strict";s.d(t,{QZ:()=>w,qA:()=>N,$F:()=>O,He:()=>T,wv:()=>I,bh:()=>Z,b$:()=>k,KK:()=>$,Py:()=>C,hb:()=>E,Yg:()=>D,mg:()=>M,P0:()=>A,Cb:()=>S,el:()=>P,IX:()=>_});var a=s(2994),r=s(50967),n=s(70580),i=s(31190);let l=e=>(e.t,new Promise(async(t,s)=>{n.yX.post(r.Y.articles,e.data).then(e=>{i.Am.success("Article added successfully"),e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&s(e)})})),o=e=>(e.t,new Promise(async(t,s)=>{n.yX.post(`${r.Y.articles}/auto`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&s(e)})})),c=({data:e,id:t})=>new Promise(async(s,a)=>{n.yX.put(`${r.Y.articles}/${t}/auto`,e).then(e=>{e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&a(e)})}),d=({data:e,id:t})=>new Promise(async(s,a)=>{n.yX.put(`${r.Y.articles}/${t}`,e).then(e=>{i.Am.success("article Commun fields updated successfully"),e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&a(e)})}),u=e=>new Promise(async(t,s)=>{try{let s={};s=await n.yX.get(`${r.Y.articles}/${e.language}/blog/${e.urlArticle}`),t(s.data)}catch(e){e&&e.response&&e.response.data&&e.response.status,s(e)}}),m=({data:e,language:t,id:s})=>new Promise(async(a,l)=>{n.yX.post(`${r.Y.articles}/${t}/${s}`,e).then(e=>{"en"===t&&i.Am.success("Article english updated successfully"),"fr"===t&&i.Am.success("Article french updated successfully"),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&l(e)})}),h=(e,t,s)=>new Promise(async(a,l)=>{try{let l=await n.xk.put(`${r.Y.articles}/${e}/${t}/desarchiver`,{archive:s});l?.data&&(i.Am.success(`Article ${s?"archived":"desarchived"} successfully`),a(l.data))}catch(e){i.Am.error(`Failed to ${s?"archive":"desarchive"} the article.`),l(e)}}),p=e=>new Promise(async(t,s)=>{try{let s=await n.yX.get(`${r.Y.categories}/${e}/all`);t(s.data)}catch(e){s(e)}}),g=e=>new Promise(async(t,s)=>{try{let s=await n.yX.get(`${r.Y.articles}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,searchQuery:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,publishDate:e.publishDate,isThreeLastArticles:e.isThreeLastArticles,isArchived:e.isArchived,categoryName:e.categoryName}});t(s.data)}catch(e){s(e)}}),f=e=>new Promise(async(t,s)=>{try{let s=await n.yX.get(`${r.Y.articles}/dashboard`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,searchQuery:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,publishDate:e.publishDate,isArchived:e.isArchived,categoryName:e.categoryName}});t(s.data)}catch(e){s(e)}}),b=e=>new Promise(async(t,s)=>{try{let s=await n.yX.get(`${r.Y.articles}/${e.language}/listarticle`);t(s.data)}catch(e){s(e)}}),x=({articleId:e,pageNumber:t,pageSize:s,sortOrder:a,name:i,approved:l,createdAt:o,paginated:c})=>new Promise(async(d,u)=>{try{let u=`${r.v}/comments/${e}?pageSize=${encodeURIComponent(s)}&pageNumber=${encodeURIComponent(t)}&sortOrder=${encodeURIComponent(a)}&paginated=${encodeURIComponent(c)}`;i&&(u+=`&name=${encodeURIComponent(i)}`),l&&(u+=`&approved=${encodeURIComponent(l)}`),o&&(u+=`&createdAt=${encodeURIComponent(new Date(o).toISOString())}`);let m=await n.yX.get(u);d(m.data)}catch(e){u(e)}}),v=(e,t)=>new Promise(async(s,a)=>{try{let a=await n.yX.get(`${r.Y.articles}/${t}/${e}`);s(a.data)}catch(e){a(e)}}),j=e=>new Promise(async(t,s)=>{try{let s=await n.xk.get(`${r.Y.comments}/detail/${e}`);t(s.data)}catch(e){s(e)}}),y=(e,t)=>new Promise(async(t,s)=>{try{let s=await n.yX.get(`${r.Y.articles}/${e}`);t(s.data)}catch(e){s(e)}});s(97980);let w=()=>(0,a.useMutation)({mutationFn:e=>l(e),onError:e=>{e.message=""}}),N=()=>(0,a.useMutation)({mutationFn:e=>o(e),onError:e=>{e.message=""}}),P=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t)=>c(e,t),onError:e=>{e.message=""}})),A=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t,s)=>m(e,t,s),onError:e=>{e.message=""}})),_=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:({language:e,id:t,archive:s})=>h(e,t,s),onError:e=>{console.error("Error during mutation",e),e.message=""}})),S=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t)=>d(e,t),onError:e=>{e.message=""}})),C=e=>(0,a.useQuery)(["category",e],async()=>await p(e)),M=e=>(0,a.useQuery)(["service",e],async()=>await p(e)),Z=e=>(0,a.useQuery)("article",async()=>await g(e)),k=e=>(0,a.useQuery)(`articles${e.language}`,async()=>await f(e)),$=e=>(0,a.useQuery)(`articlestitles${e.language}`,async()=>await b(e)),E=(e,t={})=>(0,a.useQuery)("comment",async()=>await x(e),{...t}),I=(e,t={})=>(0,a.useQuery)(["article",e],async()=>{try{return await u(e)}catch(t){throw t.response&&404===t.response.status&&("en"===e.language?window.location.href="/blog/":window.location.href="/fr/blog/"),t}},{onError:e=>{console.error("Error fetching article:",e.message)},...t}),O=(e,t)=>(0,a.useQuery)(["article",e,t],async()=>await v(e,t)),T=e=>(0,a.useQuery)(["articleall",e],async()=>await y(e)),D=e=>(0,a.useQuery)(["comment",e],async()=>await j(e))},65364:(e,t)=>{"use strict";var s=Symbol.for("react.element"),a=(Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.provider"),Symbol.for("react.context"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy"),Symbol.iterator,{isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}}),r=Object.assign,n={};function i(e,t,s){this.props=e,this.context=t,this.refs=n,this.updater=s||a}function l(){}function o(e,t,s){this.props=e,this.context=t,this.refs=n,this.updater=s||a}i.prototype.isReactComponent={},i.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},i.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},l.prototype=i.prototype;var c=o.prototype=new l;c.constructor=o,r(c,i.prototype),c.isPureReactComponent=!0;var d=Object.prototype.hasOwnProperty,u={current:null},m={key:!0,ref:!0,__self:!0,__source:!0};t.createElement=function(e,t,a){var r,n={},i=null,l=null;if(null!=t)for(r in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(i=""+t.key),t)d.call(t,r)&&!m.hasOwnProperty(r)&&(n[r]=t[r]);var o=arguments.length-2;if(1===o)n.children=a;else if(1<o){for(var c=Array(o),h=0;h<o;h++)c[h]=arguments[h+2];n.children=c}if(e&&e.defaultProps)for(r in o=e.defaultProps)void 0===n[r]&&(n[r]=o[r]);return{$$typeof:s,type:e,key:i,ref:l,props:n,_owner:u.current}}},1788:(e,t,s)=>{"use strict";e.exports=s(65364)},12427:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>H,generateMetadata:()=>z});var a,r,n,i,l=s(19510),o=s(55782),c=s(43207),d=s(71159),u=s(71615),m=s(16402),h=s(79604),p=s(11065),g=s(43738),f=s(68570);let b=(0,f.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\sections\InsightsSection.jsx#default`);var x=s(26105),v=s(62204),j=s(90606);let y=(0,f.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\node_modules\@mui\material\CircularProgress\CircularProgress.js#default`),w=function(){return l.jsx("div",{className:"spinner",children:l.jsx(y,{})})};var N=s(85301),P=s(84486),A=s(1788);function _(){return(_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let S=e=>A.createElement("svg",_({xmlns:"http://www.w3.org/2000/svg",width:37,height:36,fill:"none"},e),a||(a=A.createElement("path",{fill:"#fff",d:"M3.507 16.118C3.31 10.91 7.315 6.002 12.747 4.806c6.757-1.496 13.328 2.648 14.57 9.151.453 2.36.116 4.643-.975 6.792-.256.499-.128.731.22 1.064 1.962 1.839 3.9 3.69 5.84 5.54.998.952 1.358 2.104.905 3.4-.453 1.286-1.428 2.04-2.844 2.217-1.126.144-2.09-.233-2.89-.986-1.974-1.873-3.96-3.745-5.91-5.64-.348-.332-.603-.365-1.033-.166-7.046 3.235-15.486-.853-16.902-8.199-.117-.61-.151-1.24-.221-1.861m19.34 6.515c2.38-2.006 3.866-6.227 2.24-10.26-1.601-3.967-5.966-6.592-10.424-6.227-4.643.377-8.428 3.668-9.275 8.088-.58 2.992.94 7.501 2.855 8.388v-1.286c.035-2.46 2.183-4.553 4.772-4.598a187 187 0 0 1 4.991 0c2.195.034 4.168 1.551 4.62 3.612.163.72.152 1.485.221 2.283M12.701 18.41c-1.44.144-2.658 1.396-2.705 2.704a42 42 0 0 0 0 2.714c0 .144.116.344.244.432 2.81 1.85 7.65 1.862 10.53.022.15-.1.301-.31.301-.465.023-.83.047-1.662-.011-2.493-.128-1.773-1.452-2.936-3.309-2.98-.72-.011-1.428 0-2.148 0m7.302 6.803-.15-.111c.046.055.07.122.127.177 1.997 1.906 3.982 3.811 6.002 5.695.255.233.662.399 1.021.454.72.111 1.382-.288 1.695-.908.325-.654.21-1.319-.371-1.873-1.196-1.152-2.415-2.293-3.61-3.445a122 122 0 0 1-2.287-2.283c-.871.82-1.649 1.551-2.427 2.294"})),r||(r=A.createElement("path",{fill:"#fff",d:"M11.25 11.808c-.011-2.227 1.892-4.044 4.237-4.044 2.38 0 4.284 1.773 4.307 4.044.023 2.26-1.904 4.066-4.341 4.077-2.322 0-4.191-1.806-4.203-4.077m1.73 0c0 1.396 1.114 2.47 2.554 2.46 1.404-.011 2.565-1.108 2.565-2.416-.011-1.374-1.149-2.46-2.577-2.448-1.428 0-2.554 1.063-2.542 2.404"})));var C=s(26821),M=s(43634),Z=s(54607),k=s(95700);function $(){return($=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let E=e=>A.createElement("svg",$({xmlns:"http://www.w3.org/2000/svg",width:228,height:228,fill:"none"},e),n||(n=A.createElement("path",{fill:"#234567",d:"M19.044 102.08C17.794 69.1 43.16 38.014 77.568 30.436c42.79-9.473 84.404 16.77 92.271 57.96 2.868 14.947.735 29.402-6.176 43.015-1.617 3.157-.808 4.631 1.397 6.736 12.426 11.648 24.704 23.367 36.982 35.085 6.323 6.035 8.602 13.332 5.735 21.542-2.867 8.14-9.043 12.912-18.013 14.034-7.132.912-13.234-1.473-18.307-6.245-12.499-11.859-25.072-23.717-37.423-35.716-2.206-2.106-3.823-2.316-6.544-1.053-44.628 20.49-98.08-5.403-107.049-51.926-.735-3.859-.956-7.859-1.397-11.788m122.489 41.26c15.072-12.701 24.483-39.436 14.19-64.978-10.146-25.12-37.791-41.751-66.024-39.436-29.408 2.386-53.377 23.227-58.744 51.225-3.676 18.946 5.955 47.505 18.087 53.118v-8.139c.22-15.578 13.822-28.84 30.217-29.121 10.514-.14 21.101-.14 31.615 0 13.896.211 26.395 9.824 29.262 22.876 1.029 4.561.956 9.402 1.397 14.455m-64.259-26.735c-9.117.912-16.836 8.841-17.13 17.121-.221 5.754-.148 11.438 0 17.192 0 .912.735 2.175 1.543 2.737 17.793 11.718 48.452 11.788 66.685.14.956-.632 1.912-1.965 1.912-2.947.147-5.263.294-10.526-.073-15.789-.809-11.227-9.191-18.595-20.954-18.875-4.559-.07-9.044 0-13.602 0m46.246 43.084c-.295-.21-.662-.491-.956-.702.294.351.441.772.809 1.123 12.645 12.069 25.218 24.139 38.011 36.068 1.617 1.473 4.191 2.526 6.47 2.877 4.558.701 8.749-1.825 10.734-5.754 2.059-4.14 1.323-8.351-2.353-11.859-7.572-7.298-15.292-14.525-22.865-21.823-5.22-5.052-10.293-10.175-14.484-14.455-5.514 5.193-10.44 9.824-15.366 14.525"})),i||(i=A.createElement("path",{fill:"#234567",d:"M68.084 74.783C68.01 60.68 80.068 49.171 94.92 49.171c15.072 0 27.129 11.228 27.277 25.612.147 14.315-12.058 25.753-27.498 25.823-14.704 0-26.542-11.438-26.615-25.823m10.955 0c0 8.842 7.058 15.648 16.175 15.578 8.896-.07 16.248-7.017 16.248-15.297-.073-8.701-7.279-15.578-16.322-15.508-9.043 0-16.175 6.737-16.101 15.227"})));var I=s(45537),O=s(32483);let T={src:"/_next/static/media/HR-Management-Services-Pentabell.006393cc.webp"},D={src:"/_next/static/media/HR-Services-Pentabell.bfb9e87d.webp"},L={src:"/_next/static/media/HR-Services-Pentabell.759173f1.webp"},R={src:"/_next/static/media/HR-Management-Services-Pentabell.e1d74706.webp"},B=(0,f.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\ui\emblaCarousel\CustomEmblaCarousel.jsx#default`);async function q({locale:e,isMobileSSR:t}){let{t:s}=await (0,c.Z)(e,["homePage"]),a=[{title:s("homePage:banner:banner4"),link:`/${j.Bi.services.route}/${j.Bi.directHiring.route}`,img:T.src,altImg:s("homePage:banner:alt4"),imgMobile:R.src},{title:s("homePage:banner:banner3"),link:`/${j.Bi.services.route}/${j.Bi.payrollServices.route}`,img:D.src,altImg:s("homePage:banner:alt3"),imgMobile:L.src}],r=[],n=!1;try{let t=await fetch(`${process.env.NEXT_PUBLIC_BASE_API_URL}/sliders?visibility=Public&language=${e}&isArchived=false`),s=(await t.json()).sliders.filter(t=>t.existingLanguages.includes(e)).map(e=>e.versionslide[0]);0===s.length?(r=a,n=!0):r=s}catch(e){console.error("Error fetching sliders:",e),r=a,n=!0}let i=r.map(({title:e,link:t,linkBtn:s,img:a,altImg:r,imgMobile:i})=>({title:e,link:t,linkBtn:s,img:n?a:`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${a}`,altImg:r,imgMobile:n?i:`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${i}`}));return l.jsx(B,{isMobileSSR:t,slides:i,options:{loop:!0},slideId:"home__slider"})}var U=s(36435);async function z({params:{locale:e}}){let t=`https://www.pentabell.com/${"en"!==e?`${e}/`:""}`,{t:s}=await (0,c.Z)(e,["homePage","global","application","contactUs","opportunities"]);return{title:s("homePage:meta:metaTitle"),description:s("homePage:meta:metaDescription"),alternates:{canonical:t,languages:{fr:"https://www.pentabell.com/fr/",en:"https://www.pentabell.com/","x-default":"https://www.pentabell.com/"}},robots:"follow, index, max-snippet:-1, max-image-preview:large"}}let Q=()=>l.jsx(w,{}),H=async function({params:{locale:e}}){let{t}=await (0,c.Z)(e,["homePage","global","Tunisia","contactUs","opportunities"]),a=(0,u.headers)().get("user-agent")||"",r=/mobile/i.test(a),n=[{id:"s1",title:t("homePage:s4:stitle1"),description:t("homePage:s4:description1"),link:`/${j.Bi.services.route}/${j.Bi.payrollServices.route}`,linkText:t("homePage:s4:learnmore"),img:x.Z,altImg:t("Tunisia:services:dataS1:altImg"),icon:l.jsx(N.Z,{}),bgIcon:l.jsx(Z.Z,{className:"svg-service-icon"})},{id:"s2",title:t("homePage:s4:stitle2"),description:t("homePage:s4:description2"),link:`/${j.Bi.services.route}/${j.Bi.consultingServices.route}`,linkText:t("homePage:s4:see"),img:g.Z,altImg:t("Tunisia:services:dataS2:altImg"),icon:l.jsx(P.Z,{}),bgIcon:l.jsx(k.Z,{className:"svg-service-icon"})},{id:"s3",title:t("homePage:s4:stitle3"),description:t("homePage:s4:description3"),link:`/${j.Bi.services.route}/${j.Bi.technicalAssistance.route}`,linkText:t("homePage:s4:findOutMore"),img:p.Z,altImg:t("Tunisia:services:dataS3:altImg"),icon:l.jsx(M.Z,{}),bgIcon:l.jsx(O.Z,{className:"svg-service-icon"})},{id:"s4",title:t("homePage:s4:stitle5"),description:t("homePage:s4:description6"),link:`/${j.Bi.services.route}/${j.Bi.aiSourcing.route}`,linkText:t("homePage:s4:learnmore"),img:h.Z,altImg:t("Tunisia:services:dataS4:altImg"),icon:l.jsx(C.Z,{}),bgIcon:l.jsx(I.Z,{className:"svg-service-icon"})},{id:"s5",title:t("homePage:s4:stitle4"),description:t("homePage:s4:description4"),link:`/${j.Bi.services.route}/${j.Bi.directHiring.route}`,linkText:t("homePage:s4:startHiring"),img:m.Z,altImg:t("Tunisia:services:dataS5:altImg"),icon:l.jsx(S,{}),bgIcon:l.jsx(E,{className:"svg-service-icon"})}],i=(0,o.default)(()=>s.e(6799).then(s.bind(s,86799)),{loadableGenerated:{modules:["app\\[locale]\\(website)\\page.jsx -> @/components/sections/GlobalMap"]},ssr:!1}),f=(0,o.default)(()=>s.e(2398).then(s.bind(s,85053)),{loadableGenerated:{modules:["app\\[locale]\\(website)\\page.jsx -> @/components/sections/WhatTheySay"]},ssr:!0}),y=(0,o.default)(()=>s.e(8512).then(s.bind(s,88512)),{loadableGenerated:{modules:["app\\[locale]\\(website)\\page.jsx -> @/components/sections/GetInTouchSection"]},ssr:!1}),w=(0,o.default)(()=>s.e(8901).then(s.bind(s,38901)),{loadableGenerated:{modules:["app\\[locale]\\(website)\\page.jsx -> @/components/sections/ISOSection"]},ssr:!0}),A=(0,o.default)(()=>s.e(3664).then(s.bind(s,73664)),{loadableGenerated:{modules:["app\\[locale]\\(website)\\page.jsx -> @/components/sections/LatestJobOffers"]},ssr:!0}),_=(0,o.default)(()=>s.e(497).then(s.bind(s,40497)),{loadableGenerated:{modules:["app\\[locale]\\(website)\\page.jsx -> @/components/sections/IntroSection"]},ssr:!0});return(0,l.jsxs)(l.Fragment,{children:[l.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"http://schema.org","@type":"WebSite",url:"https://www.pentabell.com",potentialAction:{"@type":"SearchAction",target:"https://www.pentabell.com/opportunities/?keyWord={search_term_string}","query-input":"required name=search_term_string"}})}}),v.Yf(t,e)?.map((e,t)=>l.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(e)}},t)),l.jsx("div",{children:(0,l.jsxs)(d.Suspense,{fallback:Q(),children:[l.jsx(q,{locale:e,isMobileSSR:"mobile"==(r?"mobile":"desktop")}),l.jsx(U.Z,{disableTxt:!0}),l.jsx(_,{locale:e}),l.jsx("br",{}),l.jsx("div",{className:"gradient-blue",children:l.jsx(i,{locale:e,title:t("homePage:s4:title"),SERVICES:n,defaultService:n[0]})}),l.jsx(A,{language:e}),l.jsx(f,{title:t("homePage:s4:title"),SERVICES:n,defaultImage:x.Z}),l.jsx(b,{language:e}),l.jsx(w,{}),l.jsx(y,{})]})})]})}},34904:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a={src:"/_next/static/media/HR-Management-Services-Pentabell.006393cc.webp",height:738,width:1440,blurDataURL:"data:image/webp;base64,UklGRkgAAABXRUJQVlA4IDwAAADQAQCdASoIAAQAAkA4JQBOgB6TDk/kAAD+5vIVwF07s1HdKv6fD012rGTWWCDPqv5VG0vLkoRHFCS9gAA=",blurWidth:8,blurHeight:4}},44057:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a={src:"/_next/static/media/HR-Services-Pentabell.bfb9e87d.webp",height:738,width:1440,blurDataURL:"data:image/webp;base64,UklGRj4AAABXRUJQVlA4IDIAAADQAQCdASoIAAQAAkA4JQBOgCPw4pAriAD+9cBcZnzSkCl2DeAnSRFYoL4SZ9m/5NAAAA==",blurWidth:8,blurHeight:4}},90702:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a={src:"/_next/static/media/HR-Management-Services-Pentabell.e1d74706.webp",height:852,width:393,blurDataURL:"data:image/webp;base64,UklGRkYAAABXRUJQVlA4IDoAAADwAQCdASoEAAgAAkA4JQBOjXAAWZ6+KgAA/Zy/hHE+O5kht2nrcQq3CoIgEPr1fOr0E+5DivBpjKYA",blurWidth:4,blurHeight:8}},9938:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a={src:"/_next/static/media/HR-Services-Pentabell.759173f1.webp",height:852,width:393,blurDataURL:"data:image/webp;base64,UklGRkAAAABXRUJQVlA4IDQAAACwAQCdASoEAAgAAkA4JQBOgCHXYLHAAP77FECWlDGRyI+JE0VQ/HSnvdCl6U+CeDqRAAAA",blurWidth:4,blurHeight:8}},35045:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a={src:"/_next/static/media/blog-img.ee6ca320.png",height:252,width:372,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAAA1BMVEXb6Pbk38nGAAAACXBIWXMAAAsTAAALEwEAmpwYAAAADElEQVR4nGNgIAEAAAAtAAHF18CGAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:5}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,1479,1619,1336,4227,8077,6027,3718,9645,4289,1692,9433,481,4515,1812,3969,4903,8031,6345,2683,6858,426],()=>s(27114));module.exports=a})();