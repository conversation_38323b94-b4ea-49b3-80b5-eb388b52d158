"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.articleSchema = void 0;
const joi_1 = __importDefault(require("joi"));
// FAQ item schema
const faqItemSchema = joi_1.default.object({
    question: joi_1.default.string().required(),
    answer: joi_1.default.string().required(),
});
const articleSchema = joi_1.default.object({
    versions: joi_1.default.array()
        .items(joi_1.default.object({
        language: joi_1.default.string().required(),
        title: joi_1.default.string().required(),
        metaTitle: joi_1.default.string().required(),
        metaDescription: joi_1.default.string().required(),
        content: joi_1.default.string().required(),
        // Optional FAQ fields
        faqTitle: joi_1.default.string().allow('').optional(),
        faq: joi_1.default.array().items(faqItemSchema).optional(),
        // Other optional fields that might be sent
        description: joi_1.default.string().allow('').optional(),
        url: joi_1.default.string().optional(),
        alt: joi_1.default.string().allow('').optional(),
        keywords: joi_1.default.array().items(joi_1.default.string()).optional(),
        highlights: joi_1.default.array().items(joi_1.default.string()).optional(),
        category: joi_1.default.array().optional(),
        visibility: joi_1.default.string().optional(),
        publishDate: joi_1.default.date().optional(),
        image: joi_1.default.string().optional(),
        shareOnSocialMedia: joi_1.default.boolean().optional(),
        canonical: joi_1.default.string().allow('').optional(),
        isArchived: joi_1.default.boolean().optional(),
        createdBy: joi_1.default.string().optional(),
    }))
        .required(),
    // Optional root level fields
    robotsMeta: joi_1.default.string().optional(),
    tags: joi_1.default.array().items(joi_1.default.string()).optional(),
    createdBy: joi_1.default.string().optional(),
});
exports.articleSchema = articleSchema;
//# sourceMappingURL=article.validations.js.map