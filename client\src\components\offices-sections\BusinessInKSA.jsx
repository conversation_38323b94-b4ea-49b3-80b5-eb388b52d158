"use client";
import { Container, Grid } from "@mui/material";
import { motion } from "framer-motion";
import SvgbarChartTransport from "@/assets/images/services/icons/barChartTransport.svg";
import SvgbarChartTelecom from "@/assets/images/services/icons/barChartTelecom.svg";
import SvgbarChartEnergy from "@/assets/images/services/icons/barChartEnergy.svg";
import SvgbarChartOilGaz from "@/assets/images/services/icons/barChartOilGaz.svg";
import SvgbarChartBanking from "@/assets/images/services/icons/barChartBanking.svg";
import SvgbarChartOther from "@/assets/images/services/icons/barChartOther.svg";
import business from "@/assets/images/Saudi/ksa-business.png";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
function BusinessInKSA() {
  const { t } = useTranslation();
  const highlights = [
    { text: t("ksa:keywords:sagia"), className: "bg-purple" },
    { text: t("ksa:keywords:strategicReforms"), className: "bg-yellow" },
    { text: t("ksa:keywords:gosi"), className: "bg-green" },
    { text: t("ksa:keywords:upskilling"), className: "bg-teal" },
    { text: t("ksa:keywords:cultural"), className: "bg-blue" },
    { text: t("ksa:keywords:saudization"), className: "bg-red" },
    { text: t("ksa:keywords:localPartnerships"), className: "bg-darkblue" },
  ];
  const [visibleIndex, setVisibleIndex] = useState(-1);
  const [allVisible, setAllVisible] = useState(false);
  useEffect(() => {
    let timeout;

    if (!allVisible) {
      if (visibleIndex < highlights.length - 1) {
        timeout = setTimeout(() => {
          setVisibleIndex((prev) => prev + 1);
        }, 800);
      } else {
        timeout = setTimeout(() => {
          setAllVisible(true);
        }, 2000);
      }
    } else {
      timeout = setTimeout(() => {
        setVisibleIndex(-1);
        setAllVisible(false);
      }, 1000);
    }

    return () => clearTimeout(timeout);
  }, [visibleIndex, allVisible]);

  return (
    <Container id="business-tunisia" className="custom-max-width" sx={{margonTop : "20px"}}>
      <div className="business-ksa">
        {" "}
        <Grid container sx={{ justifyContent: "center", marginBottom: "20px" }}>
          <Grid sm={8} md={8}>
            {" "}
            <h2 className="heading-h1 text-center">
              {t("ksa:business:title")}
            </h2>
          </Grid>
        </Grid>
        <Grid container columnSpacing={4}>
          <Grid
            item
            md={6}
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
            }}
          >
            <h2 className="heading-h2">{t("ksa:business:subtitle")}</h2>
            <p className="sub-heading" style={{fontSize:"20px !important"}}>{t("ksa:business:description")}</p>
          </Grid>
          <Grid
            item
            md={6}
            sx={{
              position: "relative",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <img alt={t("ksa:business:alt")} src={business.src} className="business-image" />
            {highlights.map((highlight, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0 }}
                animate={{ opacity: i <= visibleIndex ? 1 : 0 }}
                transition={{ duration: 0.6 }}
                className={`highlight-text text-center ${highlight.className}`}
                style={{
                  top: `${20 + i * 10}%`,
                  left: "60%",
                }}
              >
                {highlight.text}
              </motion.div>
            ))}
          </Grid>
        </Grid>
      </div>
    </Container>
  );
}

export default BusinessInKSA;
