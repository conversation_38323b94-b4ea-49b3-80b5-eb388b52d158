"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/languageChanger.js":
/*!*******************************************!*\
  !*** ./src/components/languageChanger.js ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LanguageChanger; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../i18nConfig */ \"(app-pages-browser)/./i18nConfig.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_i18nConfig__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _ui_DropdownMenu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/DropdownMenu */ \"(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _ui_DropdownMenuMobile__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/DropdownMenuMobile */ \"(app-pages-browser)/./src/components/ui/DropdownMenuMobile.jsx\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _assets_images_flag_fr_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/assets/images/flag/fr.png */ \"(app-pages-browser)/./src/assets/images/flag/fr.png\");\n/* harmony import */ var _assets_images_flag_en_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/assets/images/flag/en.png */ \"(app-pages-browser)/./src/assets/images/flag/en.png\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction LanguageChanger(param) {\n    let { withFlag, onlyWebVersion } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"])();\n    const isTablet = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(theme.breakpoints.down(\"md\"));\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentLocale = i18n.language;\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const currentPathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const pathSegments = currentPathname.split(\"/\");\n    const handleChange = async (newLocale)=>{\n        const days = 30;\n        document.cookie = `NEXT_LOCALE=${newLocale};expires=${new Date(Date.now() + days * 24 * 60 * 60 * 1000).toUTCString()};path=/`;\n        const translateAndNavigate = async (apiUrl, slug, routeBase)=>{\n            try {\n                const response = await _config_axios__WEBPACK_IMPORTED_MODULE_7__.axiosGetJson.get(`${apiUrl}/opposite/${currentLocale}/${slug}`);\n                const translatedSlug = response.data.slug;\n                const translatedLink = translatedSlug ? `${newLocale === \"en\" ? \"\" : `/${newLocale}`}/${routeBase}/${translatedSlug}` : newLocale === \"en\" ? \"\" : `/events`;\n                router.push(translatedLink);\n            } catch (error) {}\n        };\n        const translateAndNavigateCategory = async (apiUrl, slug, routeBase)=>{\n            try {\n                const response = await _config_axios__WEBPACK_IMPORTED_MODULE_7__.axiosGetJson.get(`${apiUrl}/${currentLocale}/${routeBase}/${slug}`);\n                const translatedSlug = response.data.slug;\n                const translatedLink = translatedSlug ? `${newLocale === \"en\" ? \"\" : `/${newLocale}`}/blog/category/${translatedSlug}` : currentPathname;\n                router.push(translatedLink);\n            } catch (error) {}\n        };\n        const isPathMatch = (path, length)=>pathSegments[1] === path && pathSegments.length === length || pathSegments[2] === path && pathSegments.length === length + 1;\n        const isPathMatchCategory = (path, length)=>pathSegments[2] === path && pathSegments.length === length || pathSegments[3] === path && pathSegments.length === length + 1;\n        if (currentLocale !== newLocale) {\n            if (isPathMatch(\"opportunities\", 4)) {\n                await translateAndNavigate(_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.opportunity, pathSegments[pathSegments.length - 2], \"opportunities\");\n            } else if (isPathMatch(\"apply\", 4)) {\n                await translateAndNavigate(_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.opportunity, pathSegments[pathSegments.length - 2], \"apply\");\n            } else if (isPathMatch(\"blog\", 4)) {\n                await translateAndNavigate(_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.articles, pathSegments[pathSegments.length - 2], \"blog\");\n            } else if (isPathMatch(\"guides\", 4)) {\n                await translateAndNavigate(_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.guides, pathSegments[pathSegments.length - 2], \"guides\");\n            } else if (isPathMatch(\"glossaries\", 4)) {\n                await translateAndNavigate(_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.glossaries, pathSegments[pathSegments.length - 2], \"glossaries\");\n            } else if (isPathMatchCategory(\"category\", 5)) {\n                await translateAndNavigateCategory(_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.category, pathSegments[pathSegments.length - 2], \"blog\");\n            } else if (currentLocale === (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default().defaultLocale) && !(_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default().prefixDefault)) {\n                router.push(`/${newLocale}${currentPathname}`);\n            } else {\n                router.push(currentPathname.replace(`/${currentLocale}`, `/${newLocale}`));\n            }\n        }\n        router.refresh();\n    };\n    const menuItems = [\n        {\n            name: \"En\",\n            onClick: ()=>handleChange(\"en\"),\n            flag: _assets_images_flag_en_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            route: \"\"\n        },\n        {\n            name: \"Fr\",\n            onClick: ()=>handleChange(\"fr\"),\n            flag: _assets_images_flag_fr_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            route: \"\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: onlyWebVersion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_DropdownMenu__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            buttonLabel: currentLocale === \"en\" ? \"En\" : \"Fr\",\n            selectedFlag: currentLocale === \"en\" ? _assets_images_flag_en_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"] : _assets_images_flag_fr_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            buttonHref: \"\",\n            menuItems: menuItems,\n            withFlag: withFlag\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\languageChanger.js\",\n            lineNumber: 145,\n            columnNumber: 9\n        }, this) : isMobile || isTablet ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_DropdownMenuMobile__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            buttonLabel: currentLocale === \"en\" ? \"En\" : \"Fr\",\n            selectedFlag: currentLocale === \"en\" ? _assets_images_flag_en_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"] : _assets_images_flag_fr_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            buttonHref: \"\",\n            locale: currentLocale,\n            menuItems: menuItems,\n            withFlag: withFlag\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\languageChanger.js\",\n            lineNumber: 153,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_DropdownMenu__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            buttonLabel: currentLocale === \"en\" ? \"En\" : \"Fr\",\n            selectedFlag: currentLocale === \"en\" ? _assets_images_flag_fr_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"] : _assets_images_flag_en_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            buttonHref: \"\",\n            menuItems: menuItems,\n            withFlag: withFlag\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\languageChanger.js\",\n            lineNumber: 162,\n            columnNumber: 9\n        }, this)\n    }, void 0, false);\n}\n_s(LanguageChanger, \"5nG/xO9gnyVtyWSsQQEV+MRvoJU=\", false, function() {\n    return [\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname\n    ];\n});\n_c = LanguageChanger;\nvar _c;\n$RefreshReg$(_c, \"LanguageChanger\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/languageChanger.js\n"));

/***/ })

});