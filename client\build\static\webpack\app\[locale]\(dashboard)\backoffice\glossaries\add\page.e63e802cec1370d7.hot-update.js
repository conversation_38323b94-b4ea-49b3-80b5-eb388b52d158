"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/glossaries/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,FormLabel,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,FormLabel,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,FormLabel,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,FormLabel,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,FormLabel,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst dragActiveStyles = `\r\n  .file-labels.drag-active {\r\n    border-color: #1976d2 !important;\r\n    background-color: rgba(25, 118, 210, 0.04) !important;\r\n  }\r\n  .file-labels.disabled {\r\n    cursor: not-allowed !important;\r\n    opacity: 0.6 !important;\r\n  }\r\n`;\nif (typeof document !== \"undefined\" && !document.getElementById(\"document-importer-styles\")) {\n    const styleSheet = document.createElement(\"style\");\n    styleSheet.id = \"document-importer-styles\";\n    styleSheet.textContent = dragActiveStyles;\n    document.head.appendChild(styleSheet);\n}\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted = null, language = \"EN\", disabled = false, removeLabel = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement((image)=>image.read(\"base64\").then((imageBuffer)=>({\n                                src: `data:${image.contentType};base64,${imageBuffer}`\n                            })))\n                }\n            });\n            setProgress(75);\n            const cleanContent = result.value.replace(/<p><\\/p>/g, \"\").replace(/\\s+/g, \" \").trim();\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            onContentExtracted(result.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(result.metadata);\n            }\n            setSuccess(true);\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) processFile(acceptedFiles[0]);\n    }, []);\n    const { getRootProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing,\n        noClick: true,\n        noKeyboard: true\n    });\n    const handleFileChange = (event)=>{\n        const files = event.target.files;\n        if (files && files.length > 0) {\n            processFile(files[0]);\n        }\n        event.target.value = \"\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        sx: {\n            mb: 3,\n            mt: 1\n        },\n        children: [\n            removeLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"label-form\",\n                children: [\n                    t(\"createArticle:content\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 201,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"upload-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    ...getRootProps(),\n                    className: `file-labels ${isDragActive ? \"drag-active\" : \"\"} ${disabled || isProcessing ? \"disabled\" : \"\"}`,\n                    style: {\n                        opacity: disabled || isProcessing ? 0.6 : 1,\n                        cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"file\",\n                            accept: \".docx,.doc,.txt\",\n                            onChange: handleFileChange,\n                            className: \"file-input\",\n                            disabled: disabled || isProcessing\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"upload-area\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"icon-pic\",\n                                    style: {\n                                        backgroundImage: `url(\"${_assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"]}\")`,\n                                        backgroundSize: \"cover\",\n                                        backgroundRepeat: \"no-repeat\",\n                                        backgroundPosition: \"center\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-text\",\n                                            children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:importFromDocument\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-description\",\n                                            children: [\n                                                t(\"createArticle:supportedFormats\"),\n                                                \": .docx, .doc, .txt\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 249,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, undefined),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"RXmj7XEvBZCVJUKRSpxVlTpxjkM=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});