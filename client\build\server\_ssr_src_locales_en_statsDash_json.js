"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_en_statsDash_json";
exports.ids = ["_ssr_src_locales_en_statsDash_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/en/statsDash.json":
/*!***************************************!*\
  !*** ./src/locales/en/statsDash.json ***!
  \***************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"commentsByCategory":"Comments By Category","filters":"Filters","categories":"Categories","approvedComments":"Approved Comments","approved":"Approved","notApproved":"Not Approved","fromDate":"From Date","toDate":"To Date","filter":"Filter","usersActivities":"Users Activities","logins":"Logins","newAccounts":"New Accounts","uploadedResumes":"Uploaded Resumes","applications":"Applications","applicationsByStatus":"Applications By Status","opportunities":"Opportunities","type":"Type","industry":"Industry","platformActivity":"Platform Activities","newOpportunities":"New Opportunities","newArticles":"New Articles","newslettersSubscriptions":"Newsletters Subscriptions","newContacts":"New Contacts","articlesByVisibility":"Articles By Visibility","public":"Public","private":"Private","draft":"Draft","accepted":"Accepted","pending":"Pending","rejected":"Rejected","all":"All","opportunityType":"Opportunity Type"}');

/***/ })

};
;