"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const downloadsreport_model_1 = require("./downloadsreport.model");
const path_1 = __importDefault(require("path"));
const services_1 = require("@/utils/services");
const messages_1 = require("@/utils/helpers/messages");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
class DownloadReportService {
    constructor() {
        this.DownloadReport = downloadsreport_model_1.DownloadReportModel;
    }
    async handleReportDownload(fullName, email) {
        if (!fullName || !email) {
            throw new http_exception_1.default(400, messages_1.MESSAGES.REPORT.FULLNAME_EMAIL_REQUIRED);
        }
        const filePath = path_1.default.join(__dirname, '/../../../src/public/static/Pentabell Corporate Profile.pdf');
        const downloadUrl = 'https://www.pentabell.com/Pentabell Corporate Profile.pdf';
        const now = new Date();
        const existingUser = await this.DownloadReport.findOne({ email: email.toLowerCase() });
        await (0, services_1.sendEmail)({
            to: email,
            subject: 'Your Corporate Profile is Ready to Download',
            template: 'downloadReport',
            context: {
                fullName,
                email,
                downloadUrl,
            },
        });
        if (!existingUser) {
            await this.DownloadReport.create({
                fullName,
                email,
                downloadHistory: [now],
            });
            return messages_1.MESSAGES.REPORT.EMAIL_SENT_NEW_RECORD;
        }
        else {
            existingUser.downloadHistory.push(now);
            await existingUser.save();
            return messages_1.MESSAGES.REPORT.EMAIL_SENT_EXISTING_USER;
        }
    }
    async getAllDownloadReports(query) {
        const { keyWord, paginated } = query;
        const pageNumber = Number(query.pageNumber) || 1;
        const pageSize = Number(query.pageSize) || 5;
        const queryConditions = {};
        if (keyWord) {
            queryConditions['email'] = {
                $regex: new RegExp(`.*${keyWord}.*`, 'i'),
            };
        }
        if (query.fromDate || query.toDate) {
            queryConditions.downloadHistory = {};
            if (query.fromDate) {
                queryConditions.downloadHistory.$elemMatch = {
                    $gte: new Date(query.fromDate),
                };
            }
            if (query.toDate) {
                queryConditions.downloadHistory = {
                    $elemMatch: {
                        ...(queryConditions.downloadHistory?.$elemMatch || {}),
                        $lte: new Date(query.toDate),
                        ...(query.fromDate ? { $gte: new Date(query.fromDate) } : {}),
                    },
                };
            }
        }
        const totalDownloads = await this.DownloadReport.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalDownloads / pageSize);
        const queryBuilder = this.DownloadReport.find(queryConditions).sort({ createdAt: -1 });
        if (paginated !== 'false' && paginated) {
            const skip = (pageNumber - 1) * pageSize;
            queryBuilder.skip(skip).limit(pageSize);
        }
        const rawDownloads = await queryBuilder;
        const downloads = rawDownloads.map((doc) => ({
            _id: doc._id.toString(),
            email: doc.email,
            fullName: doc.fullName,
            createdAt: doc.createdAt,
            downloadHistory: doc.downloadHistory.map((d) => d.toISOString()),
        }));
        const result = {
            totalDownloads,
            downloads,
        };
        if (paginated !== 'false' && paginated) {
            result.pageNumber = pageNumber;
            result.pageSize = pageSize;
            result.totalPages = totalPages;
        }
        return result;
    }
}
exports.default = DownloadReportService;
//# sourceMappingURL=downloadsreport.service.js.map