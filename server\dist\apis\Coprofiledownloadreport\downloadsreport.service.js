"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const downloadsreport_model_1 = require("./downloadsreport.model");
const path_1 = __importDefault(require("path"));
const services_1 = require("@/utils/services");
const messages_1 = require("@/utils/helpers/messages");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
class DownloadReportService {
    constructor() {
        this.DownloadReport = downloadsreport_model_1.DownloadReportModel;
    }
    async handleReportDownload(fullName, email) {
        if (!fullName || !email) {
            throw new http_exception_1.default(400, messages_1.MESSAGES.REPORT.FULLNAME_EMAIL_REQUIRED);
        }
        const filePath = path_1.default.join(__dirname, '/../../../src/public/static/Pentabell Corporate Profile.pdf');
        const downloadUrl = 'https://www.pentabell.com/Pentabell Corporate Profile.pdf';
        const existingUser = await this.DownloadReport.findOne({ email: email.toLowerCase() });
        await (0, services_1.sendEmail)({
            to: email,
            subject: 'Your Corporate Profile is Ready to Download',
            template: 'downloadReport',
            context: {
                fullName,
                email,
                downloadUrl,
            },
            attachments: [
                {
                    filename: 'Pentabell Corporate Profile.pdf',
                    path: filePath,
                    contentType: 'application/pdf',
                },
            ],
        });
        if (!existingUser) {
            await this.DownloadReport.create({ fullName, email });
            return messages_1.MESSAGES.REPORT.EMAIL_SENT_NEW_RECORD;
        }
        return messages_1.MESSAGES.REPORT.EMAIL_SENT_EXISTING_USER;
    }
    async getAllDownloadReports(query) {
        const { keyWord, paginated, pageNumber = '1', pageSize = '3' } = query;
        const page = Number(pageNumber) || 1;
        const size = Number(pageSize) || 3;
        const queryConditions = {};
        if (keyWord) {
            queryConditions['email'] = {
                $regex: new RegExp(`.*${keyWord}.*`, 'i'),
            };
        }
        const totalDownloads = await this.DownloadReport.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalDownloads / size);
        if (paginated === 'false' || !paginated) {
            const downloads = await this.DownloadReport.find(queryConditions).sort({ createdAt: -1 });
            return {
                totalDownloads,
                downloads,
            };
        }
        const downloads = await this.DownloadReport.find(queryConditions)
            .sort({ createdAt: -1 })
            .skip((page - 1) * size)
            .limit(size);
        return {
            pageNumber: page,
            pageSize: size,
            totalPages,
            totalDownloads,
            downloads,
        };
    }
}
exports.default = DownloadReportService;
//# sourceMappingURL=downloadsreport.service.js.map