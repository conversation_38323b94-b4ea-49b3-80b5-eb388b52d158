"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_en_ProfessionalInformations_json";
exports.ids = ["_ssr_src_locales_en_ProfessionalInformations_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/en/ProfessionalInformations.json":
/*!******************************************************!*\
  !*** ./src/locales/en/ProfessionalInformations.json ***!
  \******************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"titre":"Professional informations ","mainindustry":"Main industry*","placeholderindustry":"Select an industry","additionalindustries":"Additional industries","plaaceholderindustries":"Choose your industries...","skills":"skills*","linkedinprofile":"Linkedin profile","Website":"website","Summary":"Summary","newskill":"Add a new skill:","chooseskill":"Choose your skills..."}');

/***/ })

};
;