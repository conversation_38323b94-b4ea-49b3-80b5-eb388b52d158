exports.id=7252,exports.ids=[7252],exports.modules={99063:(u,e,A)=>{"use strict";A.d(e,{default:()=>s});var E=A(17577),t=A(41135),C=A(14988),r=A(63946),F=A(35627),o=A(41659),n=A(10326),i=A(5028),a=A(52385),l=A(14750);let B=(0,A(71685).Z)("MuiBox",["root"]),D=(0,a.Z)(),s=function(u={}){let{themeId:e,defaultTheme:A,defaultClassName:i="MuiBox-root",generateClassName:a}=u,l=(0,C.ZP)("div",{shouldForwardProp:u=>"theme"!==u&&"sx"!==u&&"as"!==u})(r.Z);return E.forwardRef(function(u,E){let C=(0,o.Z)(A),{className:r,component:B="div",...D}=(0,F.Z)(u);return(0,n.jsx)(l,{as:B,ref:E,className:(0,t.Z)(r,a?a(i):i),theme:e&&C[e]||C,...D})})}({themeId:l.Z,defaultTheme:D,defaultClassName:B.root,generateClassName:i.Z.generate})},95894:(u,e,A)=>{"use strict";A.d(e,{Z:()=>v});var E=A(17577),t=A(41135),C=A(88634),r=A(92014),F=A(54641),o=A(40955),n=A(33662),i=A(91703),a=A(30990),l=A(2791),B=A(71685),D=A(97898);function s(u){return(0,D.ZP)("MuiSwitch",u)}let c=(0,B.Z)("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]);var d=A(31121),m=A(10326);let p=u=>{let{classes:e,edge:A,size:E,color:t,checked:r,disabled:o}=u,n={root:["root",A&&`edge${(0,F.Z)(A)}`,`size${(0,F.Z)(E)}`],switchBase:["switchBase",`color${(0,F.Z)(t)}`,r&&"checked",o&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},i=(0,C.Z)(n,s,e);return{...e,...i}},h=(0,i.ZP)("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(u,e)=>{let{ownerState:A}=u;return[e.root,A.edge&&e[`edge${(0,F.Z)(A.edge)}`],e[`size${(0,F.Z)(A.size)}`]]}})({display:"inline-flex",width:58,height:38,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,[`& .${c.thumb}`]:{width:16,height:16},[`& .${c.switchBase}`]:{padding:4,[`&.${c.checked}`]:{transform:"translateX(16px)"}}}}]}),f=(0,i.ZP)(n.Z,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(u,e)=>{let{ownerState:A}=u;return[e.switchBase,{[`& .${c.input}`]:e.input},"default"!==A.color&&e[`color${(0,F.Z)(A.color)}`]]}})((0,a.Z)(({theme:u})=>({position:"absolute",top:0,left:0,zIndex:1,color:u.vars?u.vars.palette.Switch.defaultColor:`${"light"===u.palette.mode?u.palette.common.white:u.palette.grey[300]}`,transition:u.transitions.create(["left","transform"],{duration:u.transitions.duration.shortest}),[`&.${c.checked}`]:{transform:"translateX(20px)"},[`&.${c.disabled}`]:{color:u.vars?u.vars.palette.Switch.defaultDisabledColor:`${"light"===u.palette.mode?u.palette.grey[100]:u.palette.grey[600]}`},[`&.${c.checked} + .${c.track}`]:{opacity:.5},[`&.${c.disabled} + .${c.track}`]:{opacity:u.vars?u.vars.opacity.switchTrackDisabled:`${"light"===u.palette.mode?.12:.2}`},[`& .${c.input}`]:{left:"-100%",width:"300%"}})),(0,a.Z)(({theme:u})=>({"&:hover":{backgroundColor:u.vars?`rgba(${u.vars.palette.action.activeChannel} / ${u.vars.palette.action.hoverOpacity})`:(0,r.Fq)(u.palette.action.active,u.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(u.palette).filter((0,o.Z)(["light"])).map(([e])=>({props:{color:e},style:{[`&.${c.checked}`]:{color:(u.vars||u).palette[e].main,"&:hover":{backgroundColor:u.vars?`rgba(${u.vars.palette[e].mainChannel} / ${u.vars.palette.action.hoverOpacity})`:(0,r.Fq)(u.palette[e].main,u.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${c.disabled}`]:{color:u.vars?u.vars.palette.Switch[`${e}DisabledColor`]:`${"light"===u.palette.mode?(0,r.$n)(u.palette[e].main,.62):(0,r._j)(u.palette[e].main,.55)}`}},[`&.${c.checked} + .${c.track}`]:{backgroundColor:(u.vars||u).palette[e].main}}}))]}))),g=(0,i.ZP)("span",{name:"MuiSwitch",slot:"Track",overridesResolver:(u,e)=>e.track})((0,a.Z)(({theme:u})=>({height:"100%",width:"100%",borderRadius:7,zIndex:-1,transition:u.transitions.create(["opacity","background-color"],{duration:u.transitions.duration.shortest}),backgroundColor:u.vars?u.vars.palette.common.onBackground:`${"light"===u.palette.mode?u.palette.common.black:u.palette.common.white}`,opacity:u.vars?u.vars.opacity.switchTrack:`${"light"===u.palette.mode?.38:.3}`}))),y=(0,i.ZP)("span",{name:"MuiSwitch",slot:"Thumb",overridesResolver:(u,e)=>e.thumb})((0,a.Z)(({theme:u})=>({boxShadow:(u.vars||u).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"}))),v=E.forwardRef(function(u,e){let A=(0,l.i)({props:u,name:"MuiSwitch"}),{className:E,color:C="primary",edge:r=!1,size:F="medium",sx:o,slots:n={},slotProps:i={},...a}=A,B={...A,color:C,edge:r,size:F},D=p(B),s={slots:n,slotProps:i},[c,v]=(0,d.Z)("root",{className:(0,t.Z)(D.root,E),elementType:h,externalForwardedProps:s,ownerState:B,additionalProps:{sx:o}}),[b,x]=(0,d.Z)("thumb",{className:D.thumb,elementType:y,externalForwardedProps:s,ownerState:B}),S=(0,m.jsx)(b,{...x}),[k,w]=(0,d.Z)("track",{className:D.track,elementType:g,externalForwardedProps:s,ownerState:B});return(0,m.jsxs)(c,{...v,children:[(0,m.jsx)(f,{type:"checkbox",icon:S,checkedIcon:S,ref:e,ownerState:B,...a,classes:{...D,root:D.switchBase},slots:{...n.switchBase&&{root:n.switchBase},...n.input&&{input:n.input}},slotProps:{...i.switchBase&&{root:"function"==typeof i.switchBase?i.switchBase(B):i.switchBase},...i.input&&{input:"function"==typeof i.input?i.input(B):i.input}}}),(0,m.jsx)(k,{...w})]})})},85447:(u,e,A)=>{"use strict";A.d(e,{Z:()=>P});var E=A(17577),t=A(9026),C=A(78336),r=A(47690),F=A(10326);let o=function(u){let{children:e,theme:A}=u,o=(0,t.Z)(),n=E.useMemo(()=>{let u=null===o?{...A}:"function"==typeof A?A(o):{...o,...A};return null!=u&&(u[r.Z]=null!==o),u},[A,o]);return(0,F.jsx)(C.Z.Provider,{value:n,children:e})};var n=A(76630),i=A(83342),a=A(15601),l=A(33724);let B={};function D(u,e,A,t=!1){return E.useMemo(()=>{let E=u&&e[u]||e;if("function"==typeof A){let C=A(E),r=u?{...e,[u]:C}:C;return t?()=>r:r}return u?{...e,[u]:A}:{...e,...A}},[u,e,A,t])}let s=function(u){let{children:e,theme:A,themeId:E}=u,C=(0,i.Z)(B),r=(0,t.Z)()||B,s=D(E,C,A),c=D(E,r,A,!0),d="rtl"===(E?s[E]:s).direction;return(0,F.jsx)(o,{theme:c,children:(0,F.jsx)(n.T.Provider,{value:s,children:(0,F.jsx)(a.Z,{value:d,children:(0,F.jsx)(l.Z,{value:E?s[E].components:s.components,children:e})})})})};var c=A(14750);function d({theme:u,...e}){let A=c.Z in u?u[c.Z]:void 0;return(0,F.jsx)(s,{...e,themeId:A?c.Z:void 0,theme:A||u})}var m=A(63946),p=A(92530),h=A(63212);let f="mode",g="color-scheme";function y(){}let v=({key:u,storageWindow:e})=>({get(u){},set:A=>{if(e)try{e.localStorage.setItem(u,A)}catch{}},subscribe:A=>{if(!e)return y;let E=e=>{let E=e.newValue;e.key===u&&A(E)};return e.addEventListener("storage",E),()=>{e.removeEventListener("storage",E)}}});function b(){}function x(u){}function S(u,e){return"light"===u.mode||"system"===u.mode&&"light"===u.systemMode?e("light"):"dark"===u.mode||"system"===u.mode&&"dark"===u.systemMode?e("dark"):void 0}var k=A(52385),w=A(43582);let j={colorSchemeStorageKey:"mui-color-scheme",defaultLightColorScheme:"light",defaultDarkColorScheme:"dark",modeStorageKey:"mui-mode"},{CssVarsProvider:O,useColorScheme:$,getInitColorSchemeScript:Z}=function(u){let{themeId:e,theme:A={},modeStorageKey:C=f,colorSchemeStorageKey:r=g,disableTransitionOnChange:o=!1,defaultColorScheme:n,resolveTheme:i}=u,a={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},l=E.createContext(void 0),B={},D={},c="string"==typeof n?n:n.light,d="string"==typeof n?n:n.dark;return{CssVarsProvider:function(u){let{children:a,theme:c,modeStorageKey:d=C,colorSchemeStorageKey:m=r,disableTransitionOnChange:y=o,storageManager:k,storageWindow:w,documentNode:j="undefined"==typeof document?void 0:document,colorSchemeNode:O="undefined"==typeof document?void 0:document.documentElement,disableNestedContext:$=!1,disableStyleSheetGeneration:Z=!1,defaultMode:P="system",noSsr:M}=u,I=E.useRef(!1),_=(0,t.Z)(),T=E.useContext(l),z=!!T&&!$,R=E.useMemo(()=>c||("function"==typeof A?A():A),[c]),L=R[e],N=L||R,{colorSchemes:K=B,components:V=D,cssVarPrefix:q}=N,W=Object.keys(K).filter(u=>!!K[u]).join(","),H=E.useMemo(()=>W.split(","),[W]),J="string"==typeof n?n:n.light,U="string"==typeof n?n:n.dark,Y=K[J]&&K[U]?P:K[N.defaultColorScheme]?.palette?.mode||N.palette?.mode,{mode:X,setMode:G,systemMode:Q,lightColorScheme:uu,darkColorScheme:ue,colorScheme:uA,setColorScheme:uE}=function(u){let{defaultMode:e="light",defaultLightColorScheme:A,defaultDarkColorScheme:t,supportedColorSchemes:C=[],modeStorageKey:r=f,colorSchemeStorageKey:F=g,storageWindow:o,storageManager:n=v,noSsr:i=!1}=u,a=C.join(","),l=C.length>1,B=E.useMemo(()=>n?.({key:r,storageWindow:o}),[n,r,o]),D=E.useMemo(()=>n?.({key:`${F}-light`,storageWindow:o}),[n,F,o]),s=E.useMemo(()=>n?.({key:`${F}-dark`,storageWindow:o}),[n,F,o]),[c,d]=E.useState(()=>{let u=B?.get(e)||e,E=D?.get(A)||A,C=s?.get(t)||t;return{mode:u,systemMode:x(u),lightColorScheme:E,darkColorScheme:C}}),[m,p]=E.useState(i||!l);E.useEffect(()=>{p(!0)},[]);let h=S(c,u=>"light"===u?c.lightColorScheme:"dark"===u?c.darkColorScheme:void 0),y=E.useCallback(u=>{d(A=>{if(u===A.mode)return A;let E=u??e;return B?.set(E),{...A,mode:E,systemMode:x(E)}})},[B,e]),k=E.useCallback(u=>{u?"string"==typeof u?u&&!a.includes(u)?console.error(`\`${u}\` does not exist in \`theme.colorSchemes\`.`):d(e=>{let A={...e};return S(e,e=>{"light"===e&&(D?.set(u),A.lightColorScheme=u),"dark"===e&&(s?.set(u),A.darkColorScheme=u)}),A}):d(e=>{let E={...e},C=null===u.light?A:u.light,r=null===u.dark?t:u.dark;return C&&(a.includes(C)?(E.lightColorScheme=C,D?.set(C)):console.error(`\`${C}\` does not exist in \`theme.colorSchemes\`.`)),r&&(a.includes(r)?(E.darkColorScheme=r,s?.set(r)):console.error(`\`${r}\` does not exist in \`theme.colorSchemes\`.`)),E}):d(u=>(D?.set(A),s?.set(t),{...u,lightColorScheme:A,darkColorScheme:t}))},[a,D,s,A,t]),w=E.useCallback(u=>{"system"===c.mode&&d(e=>{let A=u?.matches?"dark":"light";return e.systemMode===A?e:{...e,systemMode:A}})},[c.mode]),j=E.useRef(w);return j.current=w,E.useEffect(()=>{if("function"!=typeof window.matchMedia||!l)return;let u=(...u)=>j.current(...u),e=window.matchMedia("(prefers-color-scheme: dark)");return e.addListener(u),u(e),()=>{e.removeListener(u)}},[l]),E.useEffect(()=>{if(l){let u=B?.subscribe(u=>{(!u||["light","dark","system"].includes(u))&&y(u||e)})||b,A=D?.subscribe(u=>{(!u||a.match(u))&&k({light:u})})||b,E=s?.subscribe(u=>{(!u||a.match(u))&&k({dark:u})})||b;return()=>{u(),A(),E()}}},[k,y,a,e,o,l,B,D,s]),{...c,mode:m?c.mode:void 0,systemMode:m?c.systemMode:void 0,colorScheme:m?h:void 0,setMode:y,setColorScheme:k}}({supportedColorSchemes:H,defaultLightColorScheme:J,defaultDarkColorScheme:U,modeStorageKey:d,colorSchemeStorageKey:m,defaultMode:Y,storageManager:k,storageWindow:w,noSsr:M}),ut=X,uC=uA;z&&(ut=T.mode,uC=T.colorScheme);let ur=E.useMemo(()=>{let u=uC||N.defaultColorScheme,e=N.generateThemeVars?.()||N.vars,A={...N,components:V,colorSchemes:K,cssVarPrefix:q,vars:e};if("function"==typeof A.generateSpacing&&(A.spacing=A.generateSpacing()),u){let e=K[u];e&&"object"==typeof e&&Object.keys(e).forEach(u=>{e[u]&&"object"==typeof e[u]?A[u]={...A[u],...e[u]}:A[u]=e[u]})}return i?i(A):A},[N,uC,V,K,q]),uF=N.colorSchemeSelector;(0,h.Z)(()=>{if(uC&&O&&uF&&"media"!==uF){let u=uF;if("class"===uF&&(u=".%s"),"data"===uF&&(u="[data-%s]"),uF?.startsWith("data-")&&!uF.includes("%s")&&(u=`[${uF}="%s"]`),u.startsWith("."))O.classList.remove(...H.map(e=>u.substring(1).replace("%s",e))),O.classList.add(u.substring(1).replace("%s",uC));else{let e=u.replace("%s",uC).match(/\[([^\]]+)\]/);if(e){let[u,A]=e[1].split("=");A||H.forEach(e=>{O.removeAttribute(u.replace(uC,e))}),O.setAttribute(u,A?A.replace(/"|'/g,""):"")}else O.setAttribute(u,uC)}}},[uC,uF,O,H]),E.useEffect(()=>{let u;if(y&&I.current&&j){let e=j.createElement("style");e.appendChild(j.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),j.head.appendChild(e),window.getComputedStyle(j.body),u=setTimeout(()=>{j.head.removeChild(e)},1)}return()=>{clearTimeout(u)}},[uC,y,j]),E.useEffect(()=>(I.current=!0,()=>{I.current=!1}),[]);let uo=E.useMemo(()=>({allColorSchemes:H,colorScheme:uC,darkColorScheme:ue,lightColorScheme:uu,mode:ut,setColorScheme:uE,setMode:G,systemMode:Q}),[H,uC,ue,uu,ut,uE,G,Q,ur.colorSchemeSelector]),un=!0;(Z||!1===N.cssVariables||z&&_?.cssVarPrefix===q)&&(un=!1);let ui=(0,F.jsxs)(E.Fragment,{children:[(0,F.jsx)(s,{themeId:L?e:void 0,theme:ur,children:a}),un&&(0,F.jsx)(p.Z,{styles:ur.generateStyleSheets?.()||[]})]});return z?ui:(0,F.jsx)(l.Provider,{value:uo,children:ui})},useColorScheme:()=>E.useContext(l)||a,getInitColorSchemeScript:u=>(function(u){let{defaultMode:e="system",defaultLightColorScheme:A="light",defaultDarkColorScheme:E="dark",modeStorageKey:t=f,colorSchemeStorageKey:C=g,attribute:r="data-color-scheme",colorSchemeNode:o="document.documentElement",nonce:n}=u||{},i="",a=r;if("class"===r&&(a=".%s"),"data"===r&&(a="[data-%s]"),a.startsWith(".")){let u=a.substring(1);i+=`${o}.classList.remove('${u}'.replace('%s', light), '${u}'.replace('%s', dark));
      ${o}.classList.add('${u}'.replace('%s', colorScheme));`}let l=a.match(/\[([^\]]+)\]/);if(l){let[u,e]=l[1].split("=");e||(i+=`${o}.removeAttribute('${u}'.replace('%s', light));
      ${o}.removeAttribute('${u}'.replace('%s', dark));`),i+=`
      ${o}.setAttribute('${u}'.replace('%s', colorScheme), ${e?`${e}.replace('%s', colorScheme)`:'""'});`}else i+=`${o}.setAttribute('${a}', colorScheme);`;return(0,F.jsx)("script",{suppressHydrationWarning:!0,nonce:n,dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${t}') || '${e}';
  const dark = localStorage.getItem('${C}-dark') || '${E}';
  const light = localStorage.getItem('${C}-light') || '${A}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${i}
  }
} catch(e){}})();`}},"mui-color-scheme-init")})({colorSchemeStorageKey:r,defaultLightColorScheme:c,defaultDarkColorScheme:d,modeStorageKey:C,...u})}}({themeId:c.Z,theme:()=>(0,k.Z)({cssVariables:!0}),colorSchemeStorageKey:j.colorSchemeStorageKey,modeStorageKey:j.modeStorageKey,defaultColorScheme:{light:j.defaultLightColorScheme,dark:j.defaultDarkColorScheme},resolveTheme:u=>{let e={...u,typography:(0,w.Z)(u.palette,u.typography)};return e.unstable_sx=function(u){return(0,m.Z)({sx:u,theme:this})},e}});function P({theme:u,...e}){if("function"==typeof u)return(0,F.jsx)(d,{theme:u,...e});let A=c.Z in u?u[c.Z]:u;return"colorSchemes"in A?(0,F.jsx)(O,{theme:u,...e}):"vars"in A?(0,F.jsx)(d,{theme:u,...e}):(0,F.jsx)(d,{theme:{...u,vars:null},...e})}},47690:(u,e,A)=>{"use strict";A.d(e,{Z:()=>E});let E="function"==typeof Symbol&&Symbol.for?Symbol.for("mui.nested"):"__THEME_NESTED__"},78336:(u,e,A)=>{"use strict";A.d(e,{Z:()=>E});let E=A(17577).createContext(null)},9026:(u,e,A)=>{"use strict";A.d(e,{Z:()=>C});var E=A(17577),t=A(78336);function C(){return E.useContext(t.Z)}},78971:(u,e,A)=>{var E=A(65562),t=A(85476);u.exports=function(u,e,A){var C=t(u,e);return A||(C=C.replace(/ (?=\d)/g,"_")),C.replace(/ (.)/g,function(u,A){return E(A,e)})}},27695:u=>{var e={tr:{regexp:/\u0130|\u0049|\u0049\u0307/g,map:{İ:"i",I:"ı",İ:"i"}},az:{regexp:/[\u0130]/g,map:{İ:"i",I:"ı",İ:"i"}},lt:{regexp:/[\u0049\u004A\u012E\u00CC\u00CD\u0128]/g,map:{I:"i̇",J:"j̇",Į:"į̇",Ì:"i̇̀",Í:"i̇́",Ĩ:"i̇̃"}}};u.exports=function(u,A){var E=e[A];return u=null==u?"":String(u),E&&(u=u.replace(E.regexp,function(u){return E.map[u]})),u.toLowerCase()}},85476:(u,e,A)=>{var E=A(27695),t=A(45202),C=A(72350),r=A(84752);u.exports=function(u,e,A){return null==u?"":(A="string"!=typeof A?" ":A,E(u=String(u).replace(C,"$1 $2").replace(r,"$1 $2").replace(t,function(u,e,E){return 0===e||e===E.length-u.length?"":A}),e))}},72350:u=>{u.exports=/([a-z\xB5\xDF-\xF6\xF8-\xFF\u0101\u0103\u0105\u0107\u0109\u010B\u010D\u010F\u0111\u0113\u0115\u0117\u0119\u011B\u011D\u011F\u0121\u0123\u0125\u0127\u0129\u012B\u012D\u012F\u0131\u0133\u0135\u0137\u0138\u013A\u013C\u013E\u0140\u0142\u0144\u0146\u0148\u0149\u014B\u014D\u014F\u0151\u0153\u0155\u0157\u0159\u015B\u015D\u015F\u0161\u0163\u0165\u0167\u0169\u016B\u016D\u016F\u0171\u0173\u0175\u0177\u017A\u017C\u017E-\u0180\u0183\u0185\u0188\u018C\u018D\u0192\u0195\u0199-\u019B\u019E\u01A1\u01A3\u01A5\u01A8\u01AA\u01AB\u01AD\u01B0\u01B4\u01B6\u01B9\u01BA\u01BD-\u01BF\u01C6\u01C9\u01CC\u01CE\u01D0\u01D2\u01D4\u01D6\u01D8\u01DA\u01DC\u01DD\u01DF\u01E1\u01E3\u01E5\u01E7\u01E9\u01EB\u01ED\u01EF\u01F0\u01F3\u01F5\u01F9\u01FB\u01FD\u01FF\u0201\u0203\u0205\u0207\u0209\u020B\u020D\u020F\u0211\u0213\u0215\u0217\u0219\u021B\u021D\u021F\u0221\u0223\u0225\u0227\u0229\u022B\u022D\u022F\u0231\u0233-\u0239\u023C\u023F\u0240\u0242\u0247\u0249\u024B\u024D\u024F-\u0293\u0295-\u02AF\u0371\u0373\u0377\u037B-\u037D\u0390\u03AC-\u03CE\u03D0\u03D1\u03D5-\u03D7\u03D9\u03DB\u03DD\u03DF\u03E1\u03E3\u03E5\u03E7\u03E9\u03EB\u03ED\u03EF-\u03F3\u03F5\u03F8\u03FB\u03FC\u0430-\u045F\u0461\u0463\u0465\u0467\u0469\u046B\u046D\u046F\u0471\u0473\u0475\u0477\u0479\u047B\u047D\u047F\u0481\u048B\u048D\u048F\u0491\u0493\u0495\u0497\u0499\u049B\u049D\u049F\u04A1\u04A3\u04A5\u04A7\u04A9\u04AB\u04AD\u04AF\u04B1\u04B3\u04B5\u04B7\u04B9\u04BB\u04BD\u04BF\u04C2\u04C4\u04C6\u04C8\u04CA\u04CC\u04CE\u04CF\u04D1\u04D3\u04D5\u04D7\u04D9\u04DB\u04DD\u04DF\u04E1\u04E3\u04E5\u04E7\u04E9\u04EB\u04ED\u04EF\u04F1\u04F3\u04F5\u04F7\u04F9\u04FB\u04FD\u04FF\u0501\u0503\u0505\u0507\u0509\u050B\u050D\u050F\u0511\u0513\u0515\u0517\u0519\u051B\u051D\u051F\u0521\u0523\u0525\u0527\u0529\u052B\u052D\u052F\u0561-\u0587\u13F8-\u13FD\u1D00-\u1D2B\u1D6B-\u1D77\u1D79-\u1D9A\u1E01\u1E03\u1E05\u1E07\u1E09\u1E0B\u1E0D\u1E0F\u1E11\u1E13\u1E15\u1E17\u1E19\u1E1B\u1E1D\u1E1F\u1E21\u1E23\u1E25\u1E27\u1E29\u1E2B\u1E2D\u1E2F\u1E31\u1E33\u1E35\u1E37\u1E39\u1E3B\u1E3D\u1E3F\u1E41\u1E43\u1E45\u1E47\u1E49\u1E4B\u1E4D\u1E4F\u1E51\u1E53\u1E55\u1E57\u1E59\u1E5B\u1E5D\u1E5F\u1E61\u1E63\u1E65\u1E67\u1E69\u1E6B\u1E6D\u1E6F\u1E71\u1E73\u1E75\u1E77\u1E79\u1E7B\u1E7D\u1E7F\u1E81\u1E83\u1E85\u1E87\u1E89\u1E8B\u1E8D\u1E8F\u1E91\u1E93\u1E95-\u1E9D\u1E9F\u1EA1\u1EA3\u1EA5\u1EA7\u1EA9\u1EAB\u1EAD\u1EAF\u1EB1\u1EB3\u1EB5\u1EB7\u1EB9\u1EBB\u1EBD\u1EBF\u1EC1\u1EC3\u1EC5\u1EC7\u1EC9\u1ECB\u1ECD\u1ECF\u1ED1\u1ED3\u1ED5\u1ED7\u1ED9\u1EDB\u1EDD\u1EDF\u1EE1\u1EE3\u1EE5\u1EE7\u1EE9\u1EEB\u1EED\u1EEF\u1EF1\u1EF3\u1EF5\u1EF7\u1EF9\u1EFB\u1EFD\u1EFF-\u1F07\u1F10-\u1F15\u1F20-\u1F27\u1F30-\u1F37\u1F40-\u1F45\u1F50-\u1F57\u1F60-\u1F67\u1F70-\u1F7D\u1F80-\u1F87\u1F90-\u1F97\u1FA0-\u1FA7\u1FB0-\u1FB4\u1FB6\u1FB7\u1FBE\u1FC2-\u1FC4\u1FC6\u1FC7\u1FD0-\u1FD3\u1FD6\u1FD7\u1FE0-\u1FE7\u1FF2-\u1FF4\u1FF6\u1FF7\u210A\u210E\u210F\u2113\u212F\u2134\u2139\u213C\u213D\u2146-\u2149\u214E\u2184\u2C30-\u2C5E\u2C61\u2C65\u2C66\u2C68\u2C6A\u2C6C\u2C71\u2C73\u2C74\u2C76-\u2C7B\u2C81\u2C83\u2C85\u2C87\u2C89\u2C8B\u2C8D\u2C8F\u2C91\u2C93\u2C95\u2C97\u2C99\u2C9B\u2C9D\u2C9F\u2CA1\u2CA3\u2CA5\u2CA7\u2CA9\u2CAB\u2CAD\u2CAF\u2CB1\u2CB3\u2CB5\u2CB7\u2CB9\u2CBB\u2CBD\u2CBF\u2CC1\u2CC3\u2CC5\u2CC7\u2CC9\u2CCB\u2CCD\u2CCF\u2CD1\u2CD3\u2CD5\u2CD7\u2CD9\u2CDB\u2CDD\u2CDF\u2CE1\u2CE3\u2CE4\u2CEC\u2CEE\u2CF3\u2D00-\u2D25\u2D27\u2D2D\uA641\uA643\uA645\uA647\uA649\uA64B\uA64D\uA64F\uA651\uA653\uA655\uA657\uA659\uA65B\uA65D\uA65F\uA661\uA663\uA665\uA667\uA669\uA66B\uA66D\uA681\uA683\uA685\uA687\uA689\uA68B\uA68D\uA68F\uA691\uA693\uA695\uA697\uA699\uA69B\uA723\uA725\uA727\uA729\uA72B\uA72D\uA72F-\uA731\uA733\uA735\uA737\uA739\uA73B\uA73D\uA73F\uA741\uA743\uA745\uA747\uA749\uA74B\uA74D\uA74F\uA751\uA753\uA755\uA757\uA759\uA75B\uA75D\uA75F\uA761\uA763\uA765\uA767\uA769\uA76B\uA76D\uA76F\uA771-\uA778\uA77A\uA77C\uA77F\uA781\uA783\uA785\uA787\uA78C\uA78E\uA791\uA793-\uA795\uA797\uA799\uA79B\uA79D\uA79F\uA7A1\uA7A3\uA7A5\uA7A7\uA7A9\uA7B5\uA7B7\uA7FA\uAB30-\uAB5A\uAB60-\uAB65\uAB70-\uABBF\uFB00-\uFB06\uFB13-\uFB17\uFF41-\uFF5A0-9\xB2\xB3\xB9\xBC-\xBE\u0660-\u0669\u06F0-\u06F9\u07C0-\u07C9\u0966-\u096F\u09E6-\u09EF\u09F4-\u09F9\u0A66-\u0A6F\u0AE6-\u0AEF\u0B66-\u0B6F\u0B72-\u0B77\u0BE6-\u0BF2\u0C66-\u0C6F\u0C78-\u0C7E\u0CE6-\u0CEF\u0D66-\u0D75\u0DE6-\u0DEF\u0E50-\u0E59\u0ED0-\u0ED9\u0F20-\u0F33\u1040-\u1049\u1090-\u1099\u1369-\u137C\u16EE-\u16F0\u17E0-\u17E9\u17F0-\u17F9\u1810-\u1819\u1946-\u194F\u19D0-\u19DA\u1A80-\u1A89\u1A90-\u1A99\u1B50-\u1B59\u1BB0-\u1BB9\u1C40-\u1C49\u1C50-\u1C59\u2070\u2074-\u2079\u2080-\u2089\u2150-\u2182\u2185-\u2189\u2460-\u249B\u24EA-\u24FF\u2776-\u2793\u2CFD\u3007\u3021-\u3029\u3038-\u303A\u3192-\u3195\u3220-\u3229\u3248-\u324F\u3251-\u325F\u3280-\u3289\u32B1-\u32BF\uA620-\uA629\uA6E6-\uA6EF\uA830-\uA835\uA8D0-\uA8D9\uA900-\uA909\uA9D0-\uA9D9\uA9F0-\uA9F9\uAA50-\uAA59\uABF0-\uABF9\uFF10-\uFF19])([A-Z\xC0-\xD6\xD8-\xDE\u0100\u0102\u0104\u0106\u0108\u010A\u010C\u010E\u0110\u0112\u0114\u0116\u0118\u011A\u011C\u011E\u0120\u0122\u0124\u0126\u0128\u012A\u012C\u012E\u0130\u0132\u0134\u0136\u0139\u013B\u013D\u013F\u0141\u0143\u0145\u0147\u014A\u014C\u014E\u0150\u0152\u0154\u0156\u0158\u015A\u015C\u015E\u0160\u0162\u0164\u0166\u0168\u016A\u016C\u016E\u0170\u0172\u0174\u0176\u0178\u0179\u017B\u017D\u0181\u0182\u0184\u0186\u0187\u0189-\u018B\u018E-\u0191\u0193\u0194\u0196-\u0198\u019C\u019D\u019F\u01A0\u01A2\u01A4\u01A6\u01A7\u01A9\u01AC\u01AE\u01AF\u01B1-\u01B3\u01B5\u01B7\u01B8\u01BC\u01C4\u01C7\u01CA\u01CD\u01CF\u01D1\u01D3\u01D5\u01D7\u01D9\u01DB\u01DE\u01E0\u01E2\u01E4\u01E6\u01E8\u01EA\u01EC\u01EE\u01F1\u01F4\u01F6-\u01F8\u01FA\u01FC\u01FE\u0200\u0202\u0204\u0206\u0208\u020A\u020C\u020E\u0210\u0212\u0214\u0216\u0218\u021A\u021C\u021E\u0220\u0222\u0224\u0226\u0228\u022A\u022C\u022E\u0230\u0232\u023A\u023B\u023D\u023E\u0241\u0243-\u0246\u0248\u024A\u024C\u024E\u0370\u0372\u0376\u037F\u0386\u0388-\u038A\u038C\u038E\u038F\u0391-\u03A1\u03A3-\u03AB\u03CF\u03D2-\u03D4\u03D8\u03DA\u03DC\u03DE\u03E0\u03E2\u03E4\u03E6\u03E8\u03EA\u03EC\u03EE\u03F4\u03F7\u03F9\u03FA\u03FD-\u042F\u0460\u0462\u0464\u0466\u0468\u046A\u046C\u046E\u0470\u0472\u0474\u0476\u0478\u047A\u047C\u047E\u0480\u048A\u048C\u048E\u0490\u0492\u0494\u0496\u0498\u049A\u049C\u049E\u04A0\u04A2\u04A4\u04A6\u04A8\u04AA\u04AC\u04AE\u04B0\u04B2\u04B4\u04B6\u04B8\u04BA\u04BC\u04BE\u04C0\u04C1\u04C3\u04C5\u04C7\u04C9\u04CB\u04CD\u04D0\u04D2\u04D4\u04D6\u04D8\u04DA\u04DC\u04DE\u04E0\u04E2\u04E4\u04E6\u04E8\u04EA\u04EC\u04EE\u04F0\u04F2\u04F4\u04F6\u04F8\u04FA\u04FC\u04FE\u0500\u0502\u0504\u0506\u0508\u050A\u050C\u050E\u0510\u0512\u0514\u0516\u0518\u051A\u051C\u051E\u0520\u0522\u0524\u0526\u0528\u052A\u052C\u052E\u0531-\u0556\u10A0-\u10C5\u10C7\u10CD\u13A0-\u13F5\u1E00\u1E02\u1E04\u1E06\u1E08\u1E0A\u1E0C\u1E0E\u1E10\u1E12\u1E14\u1E16\u1E18\u1E1A\u1E1C\u1E1E\u1E20\u1E22\u1E24\u1E26\u1E28\u1E2A\u1E2C\u1E2E\u1E30\u1E32\u1E34\u1E36\u1E38\u1E3A\u1E3C\u1E3E\u1E40\u1E42\u1E44\u1E46\u1E48\u1E4A\u1E4C\u1E4E\u1E50\u1E52\u1E54\u1E56\u1E58\u1E5A\u1E5C\u1E5E\u1E60\u1E62\u1E64\u1E66\u1E68\u1E6A\u1E6C\u1E6E\u1E70\u1E72\u1E74\u1E76\u1E78\u1E7A\u1E7C\u1E7E\u1E80\u1E82\u1E84\u1E86\u1E88\u1E8A\u1E8C\u1E8E\u1E90\u1E92\u1E94\u1E9E\u1EA0\u1EA2\u1EA4\u1EA6\u1EA8\u1EAA\u1EAC\u1EAE\u1EB0\u1EB2\u1EB4\u1EB6\u1EB8\u1EBA\u1EBC\u1EBE\u1EC0\u1EC2\u1EC4\u1EC6\u1EC8\u1ECA\u1ECC\u1ECE\u1ED0\u1ED2\u1ED4\u1ED6\u1ED8\u1EDA\u1EDC\u1EDE\u1EE0\u1EE2\u1EE4\u1EE6\u1EE8\u1EEA\u1EEC\u1EEE\u1EF0\u1EF2\u1EF4\u1EF6\u1EF8\u1EFA\u1EFC\u1EFE\u1F08-\u1F0F\u1F18-\u1F1D\u1F28-\u1F2F\u1F38-\u1F3F\u1F48-\u1F4D\u1F59\u1F5B\u1F5D\u1F5F\u1F68-\u1F6F\u1FB8-\u1FBB\u1FC8-\u1FCB\u1FD8-\u1FDB\u1FE8-\u1FEC\u1FF8-\u1FFB\u2102\u2107\u210B-\u210D\u2110-\u2112\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u2130-\u2133\u213E\u213F\u2145\u2183\u2C00-\u2C2E\u2C60\u2C62-\u2C64\u2C67\u2C69\u2C6B\u2C6D-\u2C70\u2C72\u2C75\u2C7E-\u2C80\u2C82\u2C84\u2C86\u2C88\u2C8A\u2C8C\u2C8E\u2C90\u2C92\u2C94\u2C96\u2C98\u2C9A\u2C9C\u2C9E\u2CA0\u2CA2\u2CA4\u2CA6\u2CA8\u2CAA\u2CAC\u2CAE\u2CB0\u2CB2\u2CB4\u2CB6\u2CB8\u2CBA\u2CBC\u2CBE\u2CC0\u2CC2\u2CC4\u2CC6\u2CC8\u2CCA\u2CCC\u2CCE\u2CD0\u2CD2\u2CD4\u2CD6\u2CD8\u2CDA\u2CDC\u2CDE\u2CE0\u2CE2\u2CEB\u2CED\u2CF2\uA640\uA642\uA644\uA646\uA648\uA64A\uA64C\uA64E\uA650\uA652\uA654\uA656\uA658\uA65A\uA65C\uA65E\uA660\uA662\uA664\uA666\uA668\uA66A\uA66C\uA680\uA682\uA684\uA686\uA688\uA68A\uA68C\uA68E\uA690\uA692\uA694\uA696\uA698\uA69A\uA722\uA724\uA726\uA728\uA72A\uA72C\uA72E\uA732\uA734\uA736\uA738\uA73A\uA73C\uA73E\uA740\uA742\uA744\uA746\uA748\uA74A\uA74C\uA74E\uA750\uA752\uA754\uA756\uA758\uA75A\uA75C\uA75E\uA760\uA762\uA764\uA766\uA768\uA76A\uA76C\uA76E\uA779\uA77B\uA77D\uA77E\uA780\uA782\uA784\uA786\uA78B\uA78D\uA790\uA792\uA796\uA798\uA79A\uA79C\uA79E\uA7A0\uA7A2\uA7A4\uA7A6\uA7A8\uA7AA-\uA7AD\uA7B0-\uA7B4\uA7B6\uFF21-\uFF3A])/g},84752:u=>{u.exports=/([A-Z\xC0-\xD6\xD8-\xDE\u0100\u0102\u0104\u0106\u0108\u010A\u010C\u010E\u0110\u0112\u0114\u0116\u0118\u011A\u011C\u011E\u0120\u0122\u0124\u0126\u0128\u012A\u012C\u012E\u0130\u0132\u0134\u0136\u0139\u013B\u013D\u013F\u0141\u0143\u0145\u0147\u014A\u014C\u014E\u0150\u0152\u0154\u0156\u0158\u015A\u015C\u015E\u0160\u0162\u0164\u0166\u0168\u016A\u016C\u016E\u0170\u0172\u0174\u0176\u0178\u0179\u017B\u017D\u0181\u0182\u0184\u0186\u0187\u0189-\u018B\u018E-\u0191\u0193\u0194\u0196-\u0198\u019C\u019D\u019F\u01A0\u01A2\u01A4\u01A6\u01A7\u01A9\u01AC\u01AE\u01AF\u01B1-\u01B3\u01B5\u01B7\u01B8\u01BC\u01C4\u01C7\u01CA\u01CD\u01CF\u01D1\u01D3\u01D5\u01D7\u01D9\u01DB\u01DE\u01E0\u01E2\u01E4\u01E6\u01E8\u01EA\u01EC\u01EE\u01F1\u01F4\u01F6-\u01F8\u01FA\u01FC\u01FE\u0200\u0202\u0204\u0206\u0208\u020A\u020C\u020E\u0210\u0212\u0214\u0216\u0218\u021A\u021C\u021E\u0220\u0222\u0224\u0226\u0228\u022A\u022C\u022E\u0230\u0232\u023A\u023B\u023D\u023E\u0241\u0243-\u0246\u0248\u024A\u024C\u024E\u0370\u0372\u0376\u037F\u0386\u0388-\u038A\u038C\u038E\u038F\u0391-\u03A1\u03A3-\u03AB\u03CF\u03D2-\u03D4\u03D8\u03DA\u03DC\u03DE\u03E0\u03E2\u03E4\u03E6\u03E8\u03EA\u03EC\u03EE\u03F4\u03F7\u03F9\u03FA\u03FD-\u042F\u0460\u0462\u0464\u0466\u0468\u046A\u046C\u046E\u0470\u0472\u0474\u0476\u0478\u047A\u047C\u047E\u0480\u048A\u048C\u048E\u0490\u0492\u0494\u0496\u0498\u049A\u049C\u049E\u04A0\u04A2\u04A4\u04A6\u04A8\u04AA\u04AC\u04AE\u04B0\u04B2\u04B4\u04B6\u04B8\u04BA\u04BC\u04BE\u04C0\u04C1\u04C3\u04C5\u04C7\u04C9\u04CB\u04CD\u04D0\u04D2\u04D4\u04D6\u04D8\u04DA\u04DC\u04DE\u04E0\u04E2\u04E4\u04E6\u04E8\u04EA\u04EC\u04EE\u04F0\u04F2\u04F4\u04F6\u04F8\u04FA\u04FC\u04FE\u0500\u0502\u0504\u0506\u0508\u050A\u050C\u050E\u0510\u0512\u0514\u0516\u0518\u051A\u051C\u051E\u0520\u0522\u0524\u0526\u0528\u052A\u052C\u052E\u0531-\u0556\u10A0-\u10C5\u10C7\u10CD\u13A0-\u13F5\u1E00\u1E02\u1E04\u1E06\u1E08\u1E0A\u1E0C\u1E0E\u1E10\u1E12\u1E14\u1E16\u1E18\u1E1A\u1E1C\u1E1E\u1E20\u1E22\u1E24\u1E26\u1E28\u1E2A\u1E2C\u1E2E\u1E30\u1E32\u1E34\u1E36\u1E38\u1E3A\u1E3C\u1E3E\u1E40\u1E42\u1E44\u1E46\u1E48\u1E4A\u1E4C\u1E4E\u1E50\u1E52\u1E54\u1E56\u1E58\u1E5A\u1E5C\u1E5E\u1E60\u1E62\u1E64\u1E66\u1E68\u1E6A\u1E6C\u1E6E\u1E70\u1E72\u1E74\u1E76\u1E78\u1E7A\u1E7C\u1E7E\u1E80\u1E82\u1E84\u1E86\u1E88\u1E8A\u1E8C\u1E8E\u1E90\u1E92\u1E94\u1E9E\u1EA0\u1EA2\u1EA4\u1EA6\u1EA8\u1EAA\u1EAC\u1EAE\u1EB0\u1EB2\u1EB4\u1EB6\u1EB8\u1EBA\u1EBC\u1EBE\u1EC0\u1EC2\u1EC4\u1EC6\u1EC8\u1ECA\u1ECC\u1ECE\u1ED0\u1ED2\u1ED4\u1ED6\u1ED8\u1EDA\u1EDC\u1EDE\u1EE0\u1EE2\u1EE4\u1EE6\u1EE8\u1EEA\u1EEC\u1EEE\u1EF0\u1EF2\u1EF4\u1EF6\u1EF8\u1EFA\u1EFC\u1EFE\u1F08-\u1F0F\u1F18-\u1F1D\u1F28-\u1F2F\u1F38-\u1F3F\u1F48-\u1F4D\u1F59\u1F5B\u1F5D\u1F5F\u1F68-\u1F6F\u1FB8-\u1FBB\u1FC8-\u1FCB\u1FD8-\u1FDB\u1FE8-\u1FEC\u1FF8-\u1FFB\u2102\u2107\u210B-\u210D\u2110-\u2112\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u2130-\u2133\u213E\u213F\u2145\u2183\u2C00-\u2C2E\u2C60\u2C62-\u2C64\u2C67\u2C69\u2C6B\u2C6D-\u2C70\u2C72\u2C75\u2C7E-\u2C80\u2C82\u2C84\u2C86\u2C88\u2C8A\u2C8C\u2C8E\u2C90\u2C92\u2C94\u2C96\u2C98\u2C9A\u2C9C\u2C9E\u2CA0\u2CA2\u2CA4\u2CA6\u2CA8\u2CAA\u2CAC\u2CAE\u2CB0\u2CB2\u2CB4\u2CB6\u2CB8\u2CBA\u2CBC\u2CBE\u2CC0\u2CC2\u2CC4\u2CC6\u2CC8\u2CCA\u2CCC\u2CCE\u2CD0\u2CD2\u2CD4\u2CD6\u2CD8\u2CDA\u2CDC\u2CDE\u2CE0\u2CE2\u2CEB\u2CED\u2CF2\uA640\uA642\uA644\uA646\uA648\uA64A\uA64C\uA64E\uA650\uA652\uA654\uA656\uA658\uA65A\uA65C\uA65E\uA660\uA662\uA664\uA666\uA668\uA66A\uA66C\uA680\uA682\uA684\uA686\uA688\uA68A\uA68C\uA68E\uA690\uA692\uA694\uA696\uA698\uA69A\uA722\uA724\uA726\uA728\uA72A\uA72C\uA72E\uA732\uA734\uA736\uA738\uA73A\uA73C\uA73E\uA740\uA742\uA744\uA746\uA748\uA74A\uA74C\uA74E\uA750\uA752\uA754\uA756\uA758\uA75A\uA75C\uA75E\uA760\uA762\uA764\uA766\uA768\uA76A\uA76C\uA76E\uA779\uA77B\uA77D\uA77E\uA780\uA782\uA784\uA786\uA78B\uA78D\uA790\uA792\uA796\uA798\uA79A\uA79C\uA79E\uA7A0\uA7A2\uA7A4\uA7A6\uA7A8\uA7AA-\uA7AD\uA7B0-\uA7B4\uA7B6\uFF21-\uFF3A])([A-Z\xC0-\xD6\xD8-\xDE\u0100\u0102\u0104\u0106\u0108\u010A\u010C\u010E\u0110\u0112\u0114\u0116\u0118\u011A\u011C\u011E\u0120\u0122\u0124\u0126\u0128\u012A\u012C\u012E\u0130\u0132\u0134\u0136\u0139\u013B\u013D\u013F\u0141\u0143\u0145\u0147\u014A\u014C\u014E\u0150\u0152\u0154\u0156\u0158\u015A\u015C\u015E\u0160\u0162\u0164\u0166\u0168\u016A\u016C\u016E\u0170\u0172\u0174\u0176\u0178\u0179\u017B\u017D\u0181\u0182\u0184\u0186\u0187\u0189-\u018B\u018E-\u0191\u0193\u0194\u0196-\u0198\u019C\u019D\u019F\u01A0\u01A2\u01A4\u01A6\u01A7\u01A9\u01AC\u01AE\u01AF\u01B1-\u01B3\u01B5\u01B7\u01B8\u01BC\u01C4\u01C7\u01CA\u01CD\u01CF\u01D1\u01D3\u01D5\u01D7\u01D9\u01DB\u01DE\u01E0\u01E2\u01E4\u01E6\u01E8\u01EA\u01EC\u01EE\u01F1\u01F4\u01F6-\u01F8\u01FA\u01FC\u01FE\u0200\u0202\u0204\u0206\u0208\u020A\u020C\u020E\u0210\u0212\u0214\u0216\u0218\u021A\u021C\u021E\u0220\u0222\u0224\u0226\u0228\u022A\u022C\u022E\u0230\u0232\u023A\u023B\u023D\u023E\u0241\u0243-\u0246\u0248\u024A\u024C\u024E\u0370\u0372\u0376\u037F\u0386\u0388-\u038A\u038C\u038E\u038F\u0391-\u03A1\u03A3-\u03AB\u03CF\u03D2-\u03D4\u03D8\u03DA\u03DC\u03DE\u03E0\u03E2\u03E4\u03E6\u03E8\u03EA\u03EC\u03EE\u03F4\u03F7\u03F9\u03FA\u03FD-\u042F\u0460\u0462\u0464\u0466\u0468\u046A\u046C\u046E\u0470\u0472\u0474\u0476\u0478\u047A\u047C\u047E\u0480\u048A\u048C\u048E\u0490\u0492\u0494\u0496\u0498\u049A\u049C\u049E\u04A0\u04A2\u04A4\u04A6\u04A8\u04AA\u04AC\u04AE\u04B0\u04B2\u04B4\u04B6\u04B8\u04BA\u04BC\u04BE\u04C0\u04C1\u04C3\u04C5\u04C7\u04C9\u04CB\u04CD\u04D0\u04D2\u04D4\u04D6\u04D8\u04DA\u04DC\u04DE\u04E0\u04E2\u04E4\u04E6\u04E8\u04EA\u04EC\u04EE\u04F0\u04F2\u04F4\u04F6\u04F8\u04FA\u04FC\u04FE\u0500\u0502\u0504\u0506\u0508\u050A\u050C\u050E\u0510\u0512\u0514\u0516\u0518\u051A\u051C\u051E\u0520\u0522\u0524\u0526\u0528\u052A\u052C\u052E\u0531-\u0556\u10A0-\u10C5\u10C7\u10CD\u13A0-\u13F5\u1E00\u1E02\u1E04\u1E06\u1E08\u1E0A\u1E0C\u1E0E\u1E10\u1E12\u1E14\u1E16\u1E18\u1E1A\u1E1C\u1E1E\u1E20\u1E22\u1E24\u1E26\u1E28\u1E2A\u1E2C\u1E2E\u1E30\u1E32\u1E34\u1E36\u1E38\u1E3A\u1E3C\u1E3E\u1E40\u1E42\u1E44\u1E46\u1E48\u1E4A\u1E4C\u1E4E\u1E50\u1E52\u1E54\u1E56\u1E58\u1E5A\u1E5C\u1E5E\u1E60\u1E62\u1E64\u1E66\u1E68\u1E6A\u1E6C\u1E6E\u1E70\u1E72\u1E74\u1E76\u1E78\u1E7A\u1E7C\u1E7E\u1E80\u1E82\u1E84\u1E86\u1E88\u1E8A\u1E8C\u1E8E\u1E90\u1E92\u1E94\u1E9E\u1EA0\u1EA2\u1EA4\u1EA6\u1EA8\u1EAA\u1EAC\u1EAE\u1EB0\u1EB2\u1EB4\u1EB6\u1EB8\u1EBA\u1EBC\u1EBE\u1EC0\u1EC2\u1EC4\u1EC6\u1EC8\u1ECA\u1ECC\u1ECE\u1ED0\u1ED2\u1ED4\u1ED6\u1ED8\u1EDA\u1EDC\u1EDE\u1EE0\u1EE2\u1EE4\u1EE6\u1EE8\u1EEA\u1EEC\u1EEE\u1EF0\u1EF2\u1EF4\u1EF6\u1EF8\u1EFA\u1EFC\u1EFE\u1F08-\u1F0F\u1F18-\u1F1D\u1F28-\u1F2F\u1F38-\u1F3F\u1F48-\u1F4D\u1F59\u1F5B\u1F5D\u1F5F\u1F68-\u1F6F\u1FB8-\u1FBB\u1FC8-\u1FCB\u1FD8-\u1FDB\u1FE8-\u1FEC\u1FF8-\u1FFB\u2102\u2107\u210B-\u210D\u2110-\u2112\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u2130-\u2133\u213E\u213F\u2145\u2183\u2C00-\u2C2E\u2C60\u2C62-\u2C64\u2C67\u2C69\u2C6B\u2C6D-\u2C70\u2C72\u2C75\u2C7E-\u2C80\u2C82\u2C84\u2C86\u2C88\u2C8A\u2C8C\u2C8E\u2C90\u2C92\u2C94\u2C96\u2C98\u2C9A\u2C9C\u2C9E\u2CA0\u2CA2\u2CA4\u2CA6\u2CA8\u2CAA\u2CAC\u2CAE\u2CB0\u2CB2\u2CB4\u2CB6\u2CB8\u2CBA\u2CBC\u2CBE\u2CC0\u2CC2\u2CC4\u2CC6\u2CC8\u2CCA\u2CCC\u2CCE\u2CD0\u2CD2\u2CD4\u2CD6\u2CD8\u2CDA\u2CDC\u2CDE\u2CE0\u2CE2\u2CEB\u2CED\u2CF2\uA640\uA642\uA644\uA646\uA648\uA64A\uA64C\uA64E\uA650\uA652\uA654\uA656\uA658\uA65A\uA65C\uA65E\uA660\uA662\uA664\uA666\uA668\uA66A\uA66C\uA680\uA682\uA684\uA686\uA688\uA68A\uA68C\uA68E\uA690\uA692\uA694\uA696\uA698\uA69A\uA722\uA724\uA726\uA728\uA72A\uA72C\uA72E\uA732\uA734\uA736\uA738\uA73A\uA73C\uA73E\uA740\uA742\uA744\uA746\uA748\uA74A\uA74C\uA74E\uA750\uA752\uA754\uA756\uA758\uA75A\uA75C\uA75E\uA760\uA762\uA764\uA766\uA768\uA76A\uA76C\uA76E\uA779\uA77B\uA77D\uA77E\uA780\uA782\uA784\uA786\uA78B\uA78D\uA790\uA792\uA796\uA798\uA79A\uA79C\uA79E\uA7A0\uA7A2\uA7A4\uA7A6\uA7A8\uA7AA-\uA7AD\uA7B0-\uA7B4\uA7B6\uFF21-\uFF3A][a-z\xB5\xDF-\xF6\xF8-\xFF\u0101\u0103\u0105\u0107\u0109\u010B\u010D\u010F\u0111\u0113\u0115\u0117\u0119\u011B\u011D\u011F\u0121\u0123\u0125\u0127\u0129\u012B\u012D\u012F\u0131\u0133\u0135\u0137\u0138\u013A\u013C\u013E\u0140\u0142\u0144\u0146\u0148\u0149\u014B\u014D\u014F\u0151\u0153\u0155\u0157\u0159\u015B\u015D\u015F\u0161\u0163\u0165\u0167\u0169\u016B\u016D\u016F\u0171\u0173\u0175\u0177\u017A\u017C\u017E-\u0180\u0183\u0185\u0188\u018C\u018D\u0192\u0195\u0199-\u019B\u019E\u01A1\u01A3\u01A5\u01A8\u01AA\u01AB\u01AD\u01B0\u01B4\u01B6\u01B9\u01BA\u01BD-\u01BF\u01C6\u01C9\u01CC\u01CE\u01D0\u01D2\u01D4\u01D6\u01D8\u01DA\u01DC\u01DD\u01DF\u01E1\u01E3\u01E5\u01E7\u01E9\u01EB\u01ED\u01EF\u01F0\u01F3\u01F5\u01F9\u01FB\u01FD\u01FF\u0201\u0203\u0205\u0207\u0209\u020B\u020D\u020F\u0211\u0213\u0215\u0217\u0219\u021B\u021D\u021F\u0221\u0223\u0225\u0227\u0229\u022B\u022D\u022F\u0231\u0233-\u0239\u023C\u023F\u0240\u0242\u0247\u0249\u024B\u024D\u024F-\u0293\u0295-\u02AF\u0371\u0373\u0377\u037B-\u037D\u0390\u03AC-\u03CE\u03D0\u03D1\u03D5-\u03D7\u03D9\u03DB\u03DD\u03DF\u03E1\u03E3\u03E5\u03E7\u03E9\u03EB\u03ED\u03EF-\u03F3\u03F5\u03F8\u03FB\u03FC\u0430-\u045F\u0461\u0463\u0465\u0467\u0469\u046B\u046D\u046F\u0471\u0473\u0475\u0477\u0479\u047B\u047D\u047F\u0481\u048B\u048D\u048F\u0491\u0493\u0495\u0497\u0499\u049B\u049D\u049F\u04A1\u04A3\u04A5\u04A7\u04A9\u04AB\u04AD\u04AF\u04B1\u04B3\u04B5\u04B7\u04B9\u04BB\u04BD\u04BF\u04C2\u04C4\u04C6\u04C8\u04CA\u04CC\u04CE\u04CF\u04D1\u04D3\u04D5\u04D7\u04D9\u04DB\u04DD\u04DF\u04E1\u04E3\u04E5\u04E7\u04E9\u04EB\u04ED\u04EF\u04F1\u04F3\u04F5\u04F7\u04F9\u04FB\u04FD\u04FF\u0501\u0503\u0505\u0507\u0509\u050B\u050D\u050F\u0511\u0513\u0515\u0517\u0519\u051B\u051D\u051F\u0521\u0523\u0525\u0527\u0529\u052B\u052D\u052F\u0561-\u0587\u13F8-\u13FD\u1D00-\u1D2B\u1D6B-\u1D77\u1D79-\u1D9A\u1E01\u1E03\u1E05\u1E07\u1E09\u1E0B\u1E0D\u1E0F\u1E11\u1E13\u1E15\u1E17\u1E19\u1E1B\u1E1D\u1E1F\u1E21\u1E23\u1E25\u1E27\u1E29\u1E2B\u1E2D\u1E2F\u1E31\u1E33\u1E35\u1E37\u1E39\u1E3B\u1E3D\u1E3F\u1E41\u1E43\u1E45\u1E47\u1E49\u1E4B\u1E4D\u1E4F\u1E51\u1E53\u1E55\u1E57\u1E59\u1E5B\u1E5D\u1E5F\u1E61\u1E63\u1E65\u1E67\u1E69\u1E6B\u1E6D\u1E6F\u1E71\u1E73\u1E75\u1E77\u1E79\u1E7B\u1E7D\u1E7F\u1E81\u1E83\u1E85\u1E87\u1E89\u1E8B\u1E8D\u1E8F\u1E91\u1E93\u1E95-\u1E9D\u1E9F\u1EA1\u1EA3\u1EA5\u1EA7\u1EA9\u1EAB\u1EAD\u1EAF\u1EB1\u1EB3\u1EB5\u1EB7\u1EB9\u1EBB\u1EBD\u1EBF\u1EC1\u1EC3\u1EC5\u1EC7\u1EC9\u1ECB\u1ECD\u1ECF\u1ED1\u1ED3\u1ED5\u1ED7\u1ED9\u1EDB\u1EDD\u1EDF\u1EE1\u1EE3\u1EE5\u1EE7\u1EE9\u1EEB\u1EED\u1EEF\u1EF1\u1EF3\u1EF5\u1EF7\u1EF9\u1EFB\u1EFD\u1EFF-\u1F07\u1F10-\u1F15\u1F20-\u1F27\u1F30-\u1F37\u1F40-\u1F45\u1F50-\u1F57\u1F60-\u1F67\u1F70-\u1F7D\u1F80-\u1F87\u1F90-\u1F97\u1FA0-\u1FA7\u1FB0-\u1FB4\u1FB6\u1FB7\u1FBE\u1FC2-\u1FC4\u1FC6\u1FC7\u1FD0-\u1FD3\u1FD6\u1FD7\u1FE0-\u1FE7\u1FF2-\u1FF4\u1FF6\u1FF7\u210A\u210E\u210F\u2113\u212F\u2134\u2139\u213C\u213D\u2146-\u2149\u214E\u2184\u2C30-\u2C5E\u2C61\u2C65\u2C66\u2C68\u2C6A\u2C6C\u2C71\u2C73\u2C74\u2C76-\u2C7B\u2C81\u2C83\u2C85\u2C87\u2C89\u2C8B\u2C8D\u2C8F\u2C91\u2C93\u2C95\u2C97\u2C99\u2C9B\u2C9D\u2C9F\u2CA1\u2CA3\u2CA5\u2CA7\u2CA9\u2CAB\u2CAD\u2CAF\u2CB1\u2CB3\u2CB5\u2CB7\u2CB9\u2CBB\u2CBD\u2CBF\u2CC1\u2CC3\u2CC5\u2CC7\u2CC9\u2CCB\u2CCD\u2CCF\u2CD1\u2CD3\u2CD5\u2CD7\u2CD9\u2CDB\u2CDD\u2CDF\u2CE1\u2CE3\u2CE4\u2CEC\u2CEE\u2CF3\u2D00-\u2D25\u2D27\u2D2D\uA641\uA643\uA645\uA647\uA649\uA64B\uA64D\uA64F\uA651\uA653\uA655\uA657\uA659\uA65B\uA65D\uA65F\uA661\uA663\uA665\uA667\uA669\uA66B\uA66D\uA681\uA683\uA685\uA687\uA689\uA68B\uA68D\uA68F\uA691\uA693\uA695\uA697\uA699\uA69B\uA723\uA725\uA727\uA729\uA72B\uA72D\uA72F-\uA731\uA733\uA735\uA737\uA739\uA73B\uA73D\uA73F\uA741\uA743\uA745\uA747\uA749\uA74B\uA74D\uA74F\uA751\uA753\uA755\uA757\uA759\uA75B\uA75D\uA75F\uA761\uA763\uA765\uA767\uA769\uA76B\uA76D\uA76F\uA771-\uA778\uA77A\uA77C\uA77F\uA781\uA783\uA785\uA787\uA78C\uA78E\uA791\uA793-\uA795\uA797\uA799\uA79B\uA79D\uA79F\uA7A1\uA7A3\uA7A5\uA7A7\uA7A9\uA7B5\uA7B7\uA7FA\uAB30-\uAB5A\uAB60-\uAB65\uAB70-\uABBF\uFB00-\uFB06\uFB13-\uFB17\uFF41-\uFF5A])/g},45202:u=>{u.exports=/[^A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC0-9\xB2\xB3\xB9\xBC-\xBE\u0660-\u0669\u06F0-\u06F9\u07C0-\u07C9\u0966-\u096F\u09E6-\u09EF\u09F4-\u09F9\u0A66-\u0A6F\u0AE6-\u0AEF\u0B66-\u0B6F\u0B72-\u0B77\u0BE6-\u0BF2\u0C66-\u0C6F\u0C78-\u0C7E\u0CE6-\u0CEF\u0D66-\u0D75\u0DE6-\u0DEF\u0E50-\u0E59\u0ED0-\u0ED9\u0F20-\u0F33\u1040-\u1049\u1090-\u1099\u1369-\u137C\u16EE-\u16F0\u17E0-\u17E9\u17F0-\u17F9\u1810-\u1819\u1946-\u194F\u19D0-\u19DA\u1A80-\u1A89\u1A90-\u1A99\u1B50-\u1B59\u1BB0-\u1BB9\u1C40-\u1C49\u1C50-\u1C59\u2070\u2074-\u2079\u2080-\u2089\u2150-\u2182\u2185-\u2189\u2460-\u249B\u24EA-\u24FF\u2776-\u2793\u2CFD\u3007\u3021-\u3029\u3038-\u303A\u3192-\u3195\u3220-\u3229\u3248-\u324F\u3251-\u325F\u3280-\u3289\u32B1-\u32BF\uA620-\uA629\uA6E6-\uA6EF\uA830-\uA835\uA8D0-\uA8D9\uA900-\uA909\uA9D0-\uA9D9\uA9F0-\uA9F9\uAA50-\uAA59\uABF0-\uABF9\uFF10-\uFF19]+/g},99899:(u,e,A)=>{"use strict";var E=A(56715);function t(){}function C(){}C.resetWarningCache=t,u.exports=function(){function u(u,e,A,t,C,r){if(r!==E){var F=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw F.name="Invariant Violation",F}}function e(){return u}u.isRequired=u;var A={array:u,bigint:u,bool:u,func:u,number:u,object:u,string:u,symbol:u,any:u,arrayOf:e,element:u,elementType:u,instanceOf:e,node:u,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:C,resetWarningCache:t};return A.PropTypes=A,A}},78439:(u,e,A)=>{u.exports=A(99899)()},56715:u=>{"use strict";u.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},42533:(u,e,A)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.withBaseIcon=e.Icon=void 0;var E=r(A(17577)),t=r(A(78439)),C=r(A(81686));function r(u){return u&&u.__esModule?u:{default:u}}function F(){return(F=Object.assign||function(u){for(var e=1;e<arguments.length;e++){var A=arguments[e];for(var E in A)Object.prototype.hasOwnProperty.call(A,E)&&(u[E]=A[E])}return u}).apply(this,arguments)}function o(u){for(var e=1;e<arguments.length;e++){var A=null!=arguments[e]?arguments[e]:{},E=Object.keys(A);"function"==typeof Object.getOwnPropertySymbols&&(E=E.concat(Object.getOwnPropertySymbols(A).filter(function(u){return Object.getOwnPropertyDescriptor(A,u).enumerable}))),E.forEach(function(e){var E;E=A[e],e in u?Object.defineProperty(u,e,{value:E,enumerable:!0,configurable:!0,writable:!0}):u[e]=E})}return u}var n=function(u){var e=u.style,A=u.className,t=(u.icon,u.size,u.tag),r=function(u,e){if(null==u)return{};var A,E,t=function(u,e){if(null==u)return{};var A,E,t={},C=Object.keys(u);for(E=0;E<C.length;E++)A=C[E],e.indexOf(A)>=0||(t[A]=u[A]);return t}(u,e);if(Object.getOwnPropertySymbols){var C=Object.getOwnPropertySymbols(u);for(E=0;E<C.length;E++)A=C[E],!(e.indexOf(A)>=0)&&Object.prototype.propertyIsEnumerable.call(u,A)&&(t[A]=u[A])}return t}(u,["style","className","icon","size","tag"]);return E.default.createElement(t,F({},r,{style:o({display:"inline-block"},e),className:A}),E.default.createElement(C.default,{size:u.size,icon:u.icon,title:u.title}))};e.Icon=n,e.withBaseIcon=function(u){return function(e){var A=o({},u);return E.default.createElement(n,F({},A,e))}},n.defaultProps={size:16,fill:"currentColor",tag:"i"},n.propTypes={icon:t.default.object.isRequired,size:t.default.oneOfType([t.default.number,t.default.string]),style:t.default.object,tag:t.default.oneOf(["i","span","div"]),className:t.default.string},e.default=n},81686:(u,e,A)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.SvgIcon=void 0;var E=function(u){if(u&&u.__esModule)return u;var e={};if(null!=u){for(var A in u)if(Object.prototype.hasOwnProperty.call(u,A)){var E=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(u,A):{};E.get||E.set?Object.defineProperty(e,A,E):e[A]=u[A]}}return e.default=u,e}(A(17577)),t=r(A(78439)),C=r(A(78971));function r(u){return u&&u.__esModule?u:{default:u}}function F(){return(F=Object.assign||function(u){for(var e=1;e<arguments.length;e++){var A=arguments[e];for(var E in A)Object.prototype.hasOwnProperty.call(A,E)&&(u[E]=A[E])}return u}).apply(this,arguments)}var o=function(u){var e=u.size,A=u.icon,t=A.children,r=A.viewBox,o=A.attribs,n=void 0===o?{}:o,i=Object.keys(n).reduce(function(u,e){return u[(0,C.default)(e)]=n[e],u},{});return E.default.createElement("svg",F({fill:"currentColor",style:{display:"inline-block",verticalAlign:"middle"},height:e,width:e,viewBox:r},i),u.title?E.default.createElement("title",null,u.title):null,function u(e){return e.map(function(e,A){var t=e.name,r=e.attribs,F=e.children,o=void 0===F?null:F,n=Object.keys(r).filter(function(u){return"fill"!==u&&"stroke"!==u&&"none"!==r[u]}).reduce(function(u,e){return"style"===e?u.style=function(){var u=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return u.split(";").reduce(function(u,e){var A,E=function(u){if(Array.isArray(u))return u}(A=e.split(":"))||function(u,e){var A=[],E=!0,t=!1,C=void 0;try{for(var r,F=u[Symbol.iterator]();!(E=(r=F.next()).done)&&(A.push(r.value),2!==A.length);E=!0);}catch(u){t=!0,C=u}finally{try{E||null==F.return||F.return()}finally{if(t)throw C}}return A}(A,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance")}(),t=E[0],r=E[1];return null!=t&&null!=r&&(u[(0,C.default)(t)]=r),u},{})}(r[e]):u[(0,C.default)(e)]=r[e],u},{}),i={};return"none"===r.fill&&r.stroke?i={fill:"none",stroke:"currentColor"}:"none"===r.fill&&(i={fill:"none"}),(0,E.createElement)(t,function(u){for(var e=1;e<arguments.length;e++){var A=null!=arguments[e]?arguments[e]:{},E=Object.keys(A);"function"==typeof Object.getOwnPropertySymbols&&(E=E.concat(Object.getOwnPropertySymbols(A).filter(function(u){return Object.getOwnPropertyDescriptor(A,u).enumerable}))),E.forEach(function(e){var E;E=A[e],e in u?Object.defineProperty(u,e,{value:E,enumerable:!0,configurable:!0,writable:!0}):u[e]=E})}return u}({key:A},n,i),null===o?o:u(o))})}(t))};e.SvgIcon=o,o.defaultProps={size:16},o.propTypes={icon:t.default.object.isRequired,size:t.default.oneOfType([t.default.number,t.default.string]),title:t.default.string},e.default=o},72404:(u,e)=>{"use strict";e.i=void 0,e.i={viewBox:"0 0 24 24",children:[{name:"path",attribs:{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"},children:[]},{name:"circle",attribs:{cx:"12",cy:"12",r:"3"},children:[]}],attribs:{fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}}},5512:(u,e)=>{"use strict";e.Y=void 0,e.Y={viewBox:"0 0 24 24",children:[{name:"path",attribs:{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"},children:[]},{name:"line",attribs:{x1:"1",y1:"1",x2:"23",y2:"23"},children:[]}],attribs:{fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}}},50292:(u,e,A)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.horizontalCenter=void 0;var E=function(u){if(u&&u.__esModule)return u;var e={};if(null!=u){for(var A in u)if(Object.prototype.hasOwnProperty.call(u,A)){var E=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(u,A):{};E.get||E.set?Object.defineProperty(e,A,E):e[A]=u[A]}}return e.default=u,e}(A(17577)),t=function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},A=e.rAlign,t=void 0!==A&&A,C=e.space,r=void 0===C?4:C;return function(e){return E.default.createElement(u,e,E.default.createElement("div",{style:{display:"inline-flex",justifyContent:"center",alignItems:"center"}},E.Children.toArray(e.children).map(function(u,e){var A,C;return E.default.createElement("div",{key:e,style:(A={display:"inline-block"},(C=t?"paddingLeft":"paddingRight")in A?Object.defineProperty(A,C,{value:r,enumerable:!0,configurable:!0,writable:!0}):A[C]=r,A)},u)})))}};e.horizontalCenter=t,e.default=t},93969:(u,e,A)=>{"use strict";Object.defineProperty(e,"JO",{enumerable:!0,get:function(){return E.Icon}});var E=A(42533);A(50292),E.Icon},65562:u=>{var e={tr:{regexp:/[\u0069]/g,map:{i:"İ"}},az:{regexp:/[\u0069]/g,map:{i:"İ"}},lt:{regexp:/[\u0069\u006A\u012F]\u0307|\u0069\u0307[\u0300\u0301\u0303]/g,map:{i̇:"I",j̇:"J",į̇:"Į",i̇̀:"\xcc",i̇́:"\xcd",i̇̃:"Ĩ"}}};u.exports=function(u,A){var E=e[A];return u=null==u?"":String(u),E&&(u=u.replace(E.regexp,function(u){return E.map[u]})),u.toUpperCase()}}};