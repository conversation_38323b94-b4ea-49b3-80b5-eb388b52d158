<html lang='en'>
    <head>
        <meta charset='UTF-8' />
        <meta name='viewport' content='width=device-width, initial-scale=1.0' />
        <title>Email Template</title>
        <style>
            @font-face { font-family: "Proxima-Nova-Black"; src:
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF2/Proxima-Nova-Black.woff2") format("woff2"),
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF/Proxima-Nova-Black.woff") format("woff"); font-weight: 900; font-display:
            swap; } @font-face { font-family: "Proxima-Nova-Extrabold"; src:
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF2/Proxima-Nova-Extrabold.woff2") format("woff2"),
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF/Proxima-Nova-Extrabold.woff") format("woff"); font-weight: 800;
            font-display: swap; } @font-face { font-family: "Proxima-Nova-Bold"; src:
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF2/Proxima-Nova-Bold.woff2") format("woff2"),
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF/Proxima-Nova-Bold.woff") format("woff"); font-weight: 700; font-display:
            swap; } @font-face { font-family: "Proxima-Nova-Semibold"; src:
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF2/Proxima-Nova-Semibold.woff2") format("woff2"),
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF/Proxima-Nova-Semibold.woff") format("woff"); font-weight: 600;
            font-display: swap; } @font-face { font-family: "Proxima-Nova-Medium"; src:
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF2/Proxima-Nova-Medium.woff2") format("woff2"),
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF/Proxima-Nova-Medium.woff") format("woff"); font-weight: 500; font-display:
            swap; } @font-face { font-family: "Proxima-Nova-Regular"; src:
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF2/Proxima-Nova-Regular.woff2") format("woff2"),
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF/Proxima-Nova-Regular.woff") format("woff"); font-weight: 400; //
            font-display: swap; } @font-face { font-family: "Proxima-Nova-Light"; src:
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF2/Proxima-Nova-Light.woff2") format("woff2"),
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF/Proxima-Nova-Light.woff") format("woff"); font-weight: 300; font-display:
            swap; } @font-face { font-family: "Proxima-Nova-Thin"; src:
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF2/Proxima-Nova-Thin.woff2") format("woff2"),
            url("../../../../../../../client/public/fonts/proxima-nova/WOFF/Proxima-Nova-Thin.woff") format("woff"); font-weight: 100; font-display:
            swap; } body, table, td, p { font-family: 'Proxima Nova', Arial, Helvetica, sans-serif; }
        </style>
    </head>
    <body>
        {{{body}}}
    </body>
</html>