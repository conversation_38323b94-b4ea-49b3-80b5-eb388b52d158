import { Language, Visibility, robotsMeta } from '@/utils/helpers/constants';
import { UserI } from '../user/user.interfaces';
import { Types } from 'mongoose';
export interface GlossaryVersionFields {
    word: string;
    letter: string;
    content: string;
    metaTitle: string;
    metaDescription: string;
    language: Language;
    url: string;
    visibility: Visibility;
    isArchived: boolean;
    createdAt: Date;
    updatedAt: Date;
    createdBy: Types.ObjectId | UserI;
    updatedBy: Types.ObjectId | UserI;
}

export interface EnglishVersion extends GlossaryVersionFields {}
export interface FrenchVersion extends GlossaryVersionFields {}
export interface GlossaryI {
    versions: {
        [language: string]: GlossaryVersionFields;
    };
    robotsMeta: robotsMeta;
    createdAt?: Date;
    updatedAt?: Date;
}

export type GlossaryVersion = EnglishVersion | FrenchVersion;

export type GlossaryLanguageUpdate = {
    versions: {
        [language: string]: GlossaryVersionFields;
    };
};

export { Language, Visibility, robotsMeta };
