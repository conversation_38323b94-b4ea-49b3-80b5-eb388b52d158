import { websiteRoutesList } from "@/helpers/routesList";
import { formatDate, processContent } from "@/utils/functions";

export default function ExploreMoreArticles({ exploreArticles, language }) {
  const { getBlogVersion } = require("@/utils/blogHelpers");

  return (
    <div className="articles">
      {exploreArticles &&
        exploreArticles.map((article) => {
          const versionData = getBlogVersion(article, language);

          // Skip if no version data for this language
          if (!versionData) {
            return null;
          }

          return (
            <div className="article" key={article._id}>
              <img
                src={`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${versionData?.image}`}
                alt="Greenwashing"
                width={500}
                height={500}
                loading="lazy"
              />
              <h3>{versionData?.title}</h3>
              <p>{formatDate(versionData?.createdAt)}</p>
              <p>{processContent(versionData?.content)}</p>
              <a
                href={
                  language === "en"
                    ? `/${websiteRoutesList.blog.route}/${versionData.url}`
                    : `/fr/${websiteRoutesList.blog.route}/${versionData.url}`
                }
                className="read-more-link"
              >
                {/* {t("global:readMore")} » */}
                Read more
              </a>
            </div>
          );
        })}
    </div>
  );
}
