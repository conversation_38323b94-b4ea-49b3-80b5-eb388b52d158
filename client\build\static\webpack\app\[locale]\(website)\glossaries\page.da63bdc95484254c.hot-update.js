"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx":
/*!*******************************************************************!*\
  !*** ./src/features/glossary/component/GlossariesListWebsite.jsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GlossaryListWebsite; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _mui_icons_material_Search__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/icons-material/Search */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_Book__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/icons-material/Book */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Book.js\");\n/* harmony import */ var _mui_icons_material_ErrorOutline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/ErrorOutline */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ErrorOutline.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Empty state component for no glossaries\nconst EmptyGlossaryState = (param)=>{\n    let { isEmpty, isEmptySearch, searchWord, locale, translations } = param;\n    if (isEmpty) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"empty-glossary-state\",\n            sx: {\n                textAlign: \"center\",\n                py: 8,\n                px: 4,\n                minHeight: \"400px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                justifyContent: \"center\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Book__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    sx: {\n                        fontSize: 80,\n                        color: \"text.secondary\",\n                        mb: 3,\n                        opacity: 0.5\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    variant: \"h4\",\n                    component: \"h2\",\n                    gutterBottom: true,\n                    sx: {\n                        fontWeight: 600,\n                        color: \"text.primary\",\n                        mb: 2\n                    },\n                    children: translations?.emptyState?.title || \"No Glossary Terms Available\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    variant: \"body1\",\n                    color: \"text.secondary\",\n                    sx: {\n                        maxWidth: 600,\n                        mb: 4,\n                        lineHeight: 1.6\n                    },\n                    children: translations?.emptyState?.description || \"We're currently building our glossary. Check back soon for comprehensive definitions and explanations of industry terms.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    text: translations?.emptyState?.exploreButton || \"Explore Our Services\",\n                    link: locale === \"fr\" ? \"/fr/services\" : \"/services\",\n                    className: \"btn btn-filled\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (isEmptySearch) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"empty-search-state\",\n            sx: {\n                textAlign: \"center\",\n                py: 8,\n                px: 4,\n                minHeight: \"400px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                justifyContent: \"center\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Search__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    sx: {\n                        fontSize: 80,\n                        color: \"text.secondary\",\n                        mb: 3,\n                        opacity: 0.5\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    variant: \"h4\",\n                    component: \"h2\",\n                    gutterBottom: true,\n                    sx: {\n                        fontWeight: 600,\n                        color: \"text.primary\",\n                        mb: 2\n                    },\n                    children: translations?.searchEmpty?.title || \"No Results Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    variant: \"body1\",\n                    color: \"text.secondary\",\n                    sx: {\n                        maxWidth: 600,\n                        mb: 2,\n                        lineHeight: 1.6\n                    },\n                    children: translations?.searchEmpty?.description || `No glossary terms found for \"${searchWord}\". Try searching with different keywords or browse all terms.`\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        mt: 3,\n                        display: \"flex\",\n                        gap: 2,\n                        flexWrap: \"wrap\",\n                        justifyContent: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            text: translations?.searchEmpty?.clearButton || \"Clear Search\",\n                            link: locale === \"fr\" ? \"/fr/glossaries\" : \"/glossaries\",\n                            className: \"btn btn-outline\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            text: t?.(\"glossary:searchEmpty:browseButton\") || \"Browse All Terms\",\n                            link: locale === \"fr\" ? \"/fr/glossaries\" : \"/glossaries\",\n                            className: \"btn btn-filled\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, undefined);\n    }\n    return null;\n};\n_c = EmptyGlossaryState;\n// Error state component\nconst ErrorGlossaryState = (param)=>{\n    let { error, locale, t: t1 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        className: \"error-glossary-state\",\n        sx: {\n            textAlign: \"center\",\n            py: 8,\n            px: 4,\n            minHeight: \"400px\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\",\n            justifyContent: \"center\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ErrorOutline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                sx: {\n                    fontSize: 80,\n                    color: \"error.main\",\n                    mb: 3,\n                    opacity: 0.7\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 168,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                variant: \"h4\",\n                component: \"h2\",\n                gutterBottom: true,\n                sx: {\n                    fontWeight: 600,\n                    color: \"text.primary\",\n                    mb: 2\n                },\n                children: t1?.(\"glossary:error:title\") || \"Unable to Load Glossary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 176,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                variant: \"body1\",\n                color: \"text.secondary\",\n                sx: {\n                    maxWidth: 600,\n                    mb: 4,\n                    lineHeight: 1.6\n                },\n                children: t1?.(\"glossary:error:description\") || \"We're experiencing technical difficulties loading the glossary. Please try again later.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 188,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mb: 3,\n                    maxWidth: 500\n                },\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 200,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                text: t1?.(\"glossary:error:retryButton\") || \"Try Again\",\n                onClick: ()=>window.location.reload(),\n                className: \"btn btn-filled\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 203,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n        lineNumber: 155,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = ErrorGlossaryState;\nfunction GlossaryListWebsite(param) {\n    let { glossaries, locale, error, isEmpty, isEmptySearch, searchWord, t: t1 } = param;\n    _s();\n    const { t: fallbackT } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const translationFunction = t1 || fallbackT;\n    const letters = Object.keys(glossaries || {});\n    const [expandedLetters, setExpandedLetters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleToggle = (letter)=>{\n        setExpandedLetters((prev)=>({\n                ...prev,\n                [letter]: !prev[letter]\n            }));\n    };\n    // Handle error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"custom-max-width\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorGlossaryState, {\n                    error: error,\n                    locale: locale,\n                    t: translationFunction\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this);\n    }\n    // Handle empty states\n    if (isEmpty || isEmptySearch) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"custom-max-width\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmptyGlossaryState, {\n                    isEmpty: isEmpty,\n                    isEmptySearch: isEmptySearch,\n                    searchWord: searchWord,\n                    locale: locale,\n                    t: translationFunction\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 251,\n            columnNumber: 7\n        }, this);\n    }\n    // Render glossary content\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-page\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"custom-max-width\",\n            children: [\n                searchWord && letters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        mb: 4,\n                        textAlign: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            variant: \"h5\",\n                            component: \"h2\",\n                            gutterBottom: true,\n                            children: translationFunction?.(\"glossary:searchResults:title\") || `Search Results for \"${searchWord}\"`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            children: translationFunction?.(\"glossary:searchResults:count\") || `Found ${letters.reduce((total, letter)=>total + (glossaries[letter]?.length || 0), 0)} terms`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 271,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    container: true,\n                    spacing: 3,\n                    children: letters?.length > 0 && letters?.map((letter, index)=>{\n                        const letterGlossaries = glossaries[letter] || [];\n                        const isExpanded = expandedLetters[letter] || false;\n                        const displayedGlossaries = isExpanded ? letterGlossaries : letterGlossaries.slice(0, 5);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            item: true,\n                            lg: 3,\n                            md: 4,\n                            sm: 6,\n                            xs: 12,\n                            className: \"letters\",\n                            id: letter,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"letter-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"length\",\n                                        children: letterGlossaries.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"letter\",\n                                        children: letter\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"words\",\n                                        children: displayedGlossaries.map((glossary, glossaryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                className: \"word\",\n                                                href: `${locale === \"fr\" ? \"/fr\" : \"\"}/glossaries/${glossary.url}`,\n                                                title: glossary.word,\n                                                children: glossary.word\n                                            }, `${glossary.url}-${glossaryIndex}`, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 25\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 21\n                                    }, this),\n                                    letterGlossaries.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"glossary-button\",\n                                        onClick: ()=>handleToggle(letter),\n                                        size: \"small\",\n                                        variant: \"text\",\n                                        children: isExpanded ? translationFunction?.(\"glossary:showLess\") || \"Show less\" : translationFunction?.(\"glossary:showMore\") || \"Show more\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 306,\n                                columnNumber: 19\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 296,\n                            columnNumber: 17\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this),\n                letters.length > 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        textAlign: \"center\",\n                        mt: 6\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        variant: \"outlined\",\n                        onClick: ()=>window.scrollTo({\n                                top: 0,\n                                behavior: \"smooth\"\n                            }),\n                        children: translationFunction?.(\"glossary:backToTop\") || \"Back to Top\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                        lineNumber: 346,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 345,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 268,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n        lineNumber: 267,\n        columnNumber: 5\n    }, this);\n}\n_s(GlossaryListWebsite, \"sH3IPgSWaBC0FNR0INvylrLyQHk=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c2 = GlossaryListWebsite;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"EmptyGlossaryState\");\n$RefreshReg$(_c1, \"ErrorGlossaryState\");\n$RefreshReg$(_c2, \"GlossaryListWebsite\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx\n"));

/***/ })

});