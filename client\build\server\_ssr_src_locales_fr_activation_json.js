"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_fr_activation_json";
exports.ids = ["_ssr_src_locales_fr_activation_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/fr/activation.json":
/*!****************************************!*\
  !*** ./src/locales/fr/activation.json ***!
  \****************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"linkresetexpired":"Le lien d\'activation a expiré.","statussuccess":"Succès","statuterror":"erreur","messagepending":"Compte déjà activé.","messagesuccess":"Activation réussie !","emailsucces":"L\'email d\'activation a été renvoyé avec succès.","emailerror":"Une erreur s\'est produite lors de l\'envoi de l\'email d\'activation.","tittle":"Renvoyer l\'email d\'activation."}');

/***/ })

};
;