"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_locales_fr_HomeDashboard_json";
exports.ids = ["_rsc_src_locales_fr_HomeDashboard_json"];
exports.modules = {

/***/ "(rsc)/./src/locales/fr/HomeDashboard.json":
/*!*******************************************!*\
  !*** ./src/locales/fr/HomeDashboard.json ***!
  \*******************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"titleProgress":" Progression du profil","Applications":"Candidatures","profileviews":"Vues des profils","Upgrade":"Amélioration","SaveOpportunities":"Opportunités sauvegardées","savearticle":"Article sauvegardé","All":"Tous","ReadMore":"En savoir plus","Details":"Détails","hello":"Salut","markasallRead":"Marquer comme tous lus"}');

/***/ })

};
;