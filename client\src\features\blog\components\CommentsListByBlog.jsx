import { <PERSON><PERSON>, Container, TextField } from "@mui/material";
import { useEffect, useState } from "react";
import moment from "moment";
import { toast } from "react-toastify";

import CustomButton from "@/components/ui/CustomButton";
import SvgArrowDown from "../../../assets/images/icons/arrowUp.svg";
import { API_URLS } from "@/utils/urls";
import Loading from "@/components/loading/Loading";
import { axiosGetJsonSSR } from "@/config/axios";
import { stringToColor } from "@/utils/functions";

const Comment = ({ comment, addReply, level = 0, user, refetch, language }) => {
  const [showReplies, setShowReplies] = useState(false);
  const [replyInputVisible, setReplyInputVisible] = useState(false);
  const [newReply, setNewReply] = useState("");
  const [shownReplies, setShownReplies] = useState(1); // Initially show the latest reply

  const handleReplySubmit = async (e) => {
    e.preventDefault();

    try {
      const commentData = {
        comment: newReply,
      };

      if (user?._id) {
        commentData.user = user?._id;
      } else {
        commentData.firstName = formData.firstName;
        commentData.email = formData.email;
      }

      commentData.language = language;

      const response = await axiosGetJsonSSR.post(
        `/comments/${replyInputVisible}/response`,
        commentData
      );

      if (response.status === 200) {
        setNewReply("");
        setReplyInputVisible(false);
        setShowReplies(true);
        toast.success(
          language === "en"
            ? "Your comment has been successfully added and is awaiting administrator approval"
            : "Votre commentaire a été ajouté avec succès et est en attente de l'approbation de l'administrateur"
        );
        refetch();
      } else {
        console.error("Error adding comment:", response.statusText);
      }
    } catch (error) {
      console.error("Error adding comment:", error);
    }
  };

  return (
    <div className="one-comment-item" style={{ marginLeft: `${level * 20}px` }}>
      {/* <Image
        width="200"
        height="200"
        src={
          comment?.user?.profilePicture
            ? `${process.env.NEXT_PUBLIC_BASE_API_URL}${API_URLS.files}/${comment?.user?.profilePicture}`
            : avatar
        }
        className="user-img"
        priority
      /> */}
      <Avatar
        sx={{ bgcolor: stringToColor(comment?.firstName) }}
        alt={
          comment.user
            ? `${comment?.user?.firstName} ${comment?.user?.lastName}`
            : comment?.firstName
        }
        src={`${process.env.NEXT_PUBLIC_BASE_API_URL}${API_URLS.files}/${comment?.user?.profilePicture}`}
        className="menu-icon avatar"
      />
      <div className="comment-content">
        <div className="comment-header">
          <p className="username">
            {comment.user
              ? `${comment?.user?.firstName} ${comment?.user?.lastName}`
              : comment?.firstName}
          </p>
          <p className="date">{moment(comment.createdAt).fromNow()}</p>
        </div>
        <p className="text">{comment.comment}</p>
        <div className="flex">
          {/* Show Reply Button */}
          {user && (
            <CustomButton
              text={replyInputVisible ? "Cancel" : "Reply"}
              // icon={<SvgArrow />}
              className={"btn btn-ghost reply grey"}
              onClick={() =>
                replyInputVisible
                  ? setReplyInputVisible(false)
                  : setReplyInputVisible(comment?._id)
              }
            />
          )}

          {/* Show Replies Button */}
          {comment.responses?.length > 0 && (
            <CustomButton
              text={`Replies (${comment.responses.length})`}
              className={"btn btn-ghost reply"}
              onClick={() => setShowReplies(!showReplies)}
            />
          )}
        </div>

        {/* Show Replies */}
        {showReplies && (
          <div className="replies">
            {/* Show last reply and button to show all previous replies */}
            {comment.responses.length > 1 &&
              shownReplies < comment.responses.length && (
                <CustomButton
                  text={`Show Previous Replies`}
                  className={"btn btn-ghost replies-list"}
                  onClick={() => setShownReplies(shownReplies + 1)}
                />
              )}
            {comment.responses.slice(-shownReplies).map((reply, index) => (
              <Comment
                key={index}
                comment={reply}
                addReply={addReply}
                level={level + 1}
              />
            ))}
          </div>
        )}
        {/* Input to Add Reply */}
        {replyInputVisible && (
          <form onSubmit={handleReplySubmit} className="reply-form">
            <TextField
              value={newReply}
              onChange={(e) => setNewReply(e.target.value)}
              placeholder="Add a reply..."
            />
            <CustomButton
              text={"reply"}
              className={"btn btn-filled blue"}
              type="submit"
            />
          </form>
        )}
      </div>
    </div>
  );
};

// Example CommentsList Component
const CommentsList = ({ comments, addReply, user, refetch, articleId }) => {
  const [shownComments, setShownComments] = useState(4);
  const commentsToDisplay = comments?.slice(0, shownComments);

  return (
    <div>
      {commentsToDisplay?.map((comment, index) => (
        <Comment
          key={index}
          comment={comment}
          addReply={addReply}
          user={user}
          refetch={refetch}
          articleId={articleId}
        />
      ))}
      {/* Show "Show More" button if there are more top-level comments */}
      {comments?.length > shownComments && (
        <CustomButton
          text={"Show More Comments"}
          icon={<SvgArrowDown />}
          className={"btn btn-ghost show-more"}
          onClick={() => setShownComments(shownComments + 4)}
        />
      )}
    </div>
  );
};

function CommentsListByBlog({
  articleId,
  isLoadingArticle,
  user,
  data,
  isLoading,
  refetch,
}) {
  const [comments, setComments] = useState([]);

  useEffect(() => {
    data?.comments && setComments(data.comments);
  }, [data, articleId, isLoadingArticle]);

  const totalComments = data?.totalComments;

  const addReply = (commentId, replyText) => {
    setComments((prevComments) =>
      prevComments.map((comment) =>
        comment.id === commentId
          ? {
              ...comment,
              replies: [
                ...comment.replies,
                {
                  id: Math.random(),
                  avatar: "https://example.com/avatar3.jpg",
                  username: "New User",
                  date: new Date().toISOString().split("T")[0],
                  text: replyText,
                  replies: [],
                },
              ],
            }
          : comment
      )
    );
  };

  return isLoading || isLoadingArticle ? (
    <Loading />
  ) : totalComments > 0 ? (
    <Container id="comment-list-blog">
      <p className="sub-heading text-banking">
        Comments{" "}
        {totalComments > 0 && (
          <span className="comments-nbr">{totalComments}</span>
        )}
      </p>

      <CommentsList
        comments={comments}
        addReply={addReply}
        user={user}
        refetch={refetch}
        articleId={articleId}
      />
    </Container>
  ) : null;
}

export default CommentsListByBlog;
