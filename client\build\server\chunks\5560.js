"use strict";exports.id=5560,exports.ids=[5560],exports.modules={85560:(e,a,t)=>{t.d(a,{Z:()=>I});var o=t(17577),l=t(41135),r=t(88634),i=t(92014),n=t(27522),c=t(10326);let s=(0,n.Z)((0,c.jsx)("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}),"Cancel");var p=t(37382),d=t(54641),v=t(6422),m=t(91703),g=t(30990),u=t(40955),$=t(2791),b=t(71685),y=t(97898);function C(e){return(0,y.ZP)("MuiChip",e)}let h=(0,b.Z)("MuiChip",["root","sizeSmall","sizeMedium","colorDefault","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),f=e=>{let{classes:a,disabled:t,size:o,color:l,iconColor:i,onDelete:n,clickable:c,variant:s}=e,p={root:["root",s,t&&"disabled",`size${(0,d.Z)(o)}`,`color${(0,d.Z)(l)}`,c&&"clickable",c&&`clickableColor${(0,d.Z)(l)}`,n&&"deletable",n&&`deletableColor${(0,d.Z)(l)}`,`${s}${(0,d.Z)(l)}`],label:["label",`label${(0,d.Z)(o)}`],avatar:["avatar",`avatar${(0,d.Z)(o)}`,`avatarColor${(0,d.Z)(l)}`],icon:["icon",`icon${(0,d.Z)(o)}`,`iconColor${(0,d.Z)(i)}`],deleteIcon:["deleteIcon",`deleteIcon${(0,d.Z)(o)}`,`deleteIconColor${(0,d.Z)(l)}`,`deleteIcon${(0,d.Z)(s)}Color${(0,d.Z)(l)}`]};return(0,r.Z)(p,C,a)},Z=(0,m.ZP)("div",{name:"MuiChip",slot:"Root",overridesResolver:(e,a)=>{let{ownerState:t}=e,{color:o,iconColor:l,clickable:r,onDelete:i,size:n,variant:c}=t;return[{[`& .${h.avatar}`]:a.avatar},{[`& .${h.avatar}`]:a[`avatar${(0,d.Z)(n)}`]},{[`& .${h.avatar}`]:a[`avatarColor${(0,d.Z)(o)}`]},{[`& .${h.icon}`]:a.icon},{[`& .${h.icon}`]:a[`icon${(0,d.Z)(n)}`]},{[`& .${h.icon}`]:a[`iconColor${(0,d.Z)(l)}`]},{[`& .${h.deleteIcon}`]:a.deleteIcon},{[`& .${h.deleteIcon}`]:a[`deleteIcon${(0,d.Z)(n)}`]},{[`& .${h.deleteIcon}`]:a[`deleteIconColor${(0,d.Z)(o)}`]},{[`& .${h.deleteIcon}`]:a[`deleteIcon${(0,d.Z)(c)}Color${(0,d.Z)(o)}`]},a.root,a[`size${(0,d.Z)(n)}`],a[`color${(0,d.Z)(o)}`],r&&a.clickable,r&&"default"!==o&&a[`clickableColor${(0,d.Z)(o)})`],i&&a.deletable,i&&"default"!==o&&a[`deletableColor${(0,d.Z)(o)}`],a[c],a[`${c}${(0,d.Z)(o)}`]]}})((0,g.Z)(({theme:e})=>{let a="light"===e.palette.mode?e.palette.grey[700]:e.palette.grey[300];return{maxWidth:"100%",fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,color:(e.vars||e).palette.text.primary,backgroundColor:(e.vars||e).palette.action.selected,borderRadius:16,whiteSpace:"nowrap",transition:e.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",[`&.${h.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity,pointerEvents:"none"},[`& .${h.avatar}`]:{marginLeft:5,marginRight:-6,width:24,height:24,color:e.vars?e.vars.palette.Chip.defaultAvatarColor:a,fontSize:e.typography.pxToRem(12)},[`& .${h.avatarColorPrimary}`]:{color:(e.vars||e).palette.primary.contrastText,backgroundColor:(e.vars||e).palette.primary.dark},[`& .${h.avatarColorSecondary}`]:{color:(e.vars||e).palette.secondary.contrastText,backgroundColor:(e.vars||e).palette.secondary.dark},[`& .${h.avatarSmall}`]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:e.typography.pxToRem(10)},[`& .${h.icon}`]:{marginLeft:5,marginRight:-6},[`& .${h.deleteIcon}`]:{WebkitTapHighlightColor:"transparent",color:e.vars?`rgba(${e.vars.palette.text.primaryChannel} / 0.26)`:(0,i.Fq)(e.palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:e.vars?`rgba(${e.vars.palette.text.primaryChannel} / 0.4)`:(0,i.Fq)(e.palette.text.primary,.4)}},variants:[{props:{size:"small"},style:{height:24,[`& .${h.icon}`]:{fontSize:18,marginLeft:4,marginRight:-4},[`& .${h.deleteIcon}`]:{fontSize:16,marginRight:4,marginLeft:-4}}},...Object.entries(e.palette).filter((0,u.Z)(["contrastText"])).map(([a])=>({props:{color:a},style:{backgroundColor:(e.vars||e).palette[a].main,color:(e.vars||e).palette[a].contrastText,[`& .${h.deleteIcon}`]:{color:e.vars?`rgba(${e.vars.palette[a].contrastTextChannel} / 0.7)`:(0,i.Fq)(e.palette[a].contrastText,.7),"&:hover, &:active":{color:(e.vars||e).palette[a].contrastText}}}})),{props:e=>e.iconColor===e.color,style:{[`& .${h.icon}`]:{color:e.vars?e.vars.palette.Chip.defaultIconColor:a}}},{props:e=>e.iconColor===e.color&&"default"!==e.color,style:{[`& .${h.icon}`]:{color:"inherit"}}},{props:{onDelete:!0},style:{[`&.${h.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.action.selectedChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:(0,i.Fq)(e.palette.action.selected,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}}},...Object.entries(e.palette).filter((0,u.Z)(["dark"])).map(([a])=>({props:{color:a,onDelete:!0},style:{[`&.${h.focusVisible}`]:{background:(e.vars||e).palette[a].dark}}})),{props:{clickable:!0},style:{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.selectedChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:(0,i.Fq)(e.palette.action.selected,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity)},[`&.${h.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.action.selectedChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:(0,i.Fq)(e.palette.action.selected,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)},"&:active":{boxShadow:(e.vars||e).shadows[1]}}},...Object.entries(e.palette).filter((0,u.Z)(["dark"])).map(([a])=>({props:{color:a,clickable:!0},style:{[`&:hover, &.${h.focusVisible}`]:{backgroundColor:(e.vars||e).palette[a].dark}}})),{props:{variant:"outlined"},style:{backgroundColor:"transparent",border:e.vars?`1px solid ${e.vars.palette.Chip.defaultBorder}`:`1px solid ${"light"===e.palette.mode?e.palette.grey[400]:e.palette.grey[700]}`,[`&.${h.clickable}:hover`]:{backgroundColor:(e.vars||e).palette.action.hover},[`&.${h.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`& .${h.avatar}`]:{marginLeft:4},[`& .${h.avatarSmall}`]:{marginLeft:2},[`& .${h.icon}`]:{marginLeft:4},[`& .${h.iconSmall}`]:{marginLeft:2},[`& .${h.deleteIcon}`]:{marginRight:5},[`& .${h.deleteIconSmall}`]:{marginRight:3}}},...Object.entries(e.palette).filter((0,u.Z)()).map(([a])=>({props:{variant:"outlined",color:a},style:{color:(e.vars||e).palette[a].main,border:`1px solid ${e.vars?`rgba(${e.vars.palette[a].mainChannel} / 0.7)`:(0,i.Fq)(e.palette[a].main,.7)}`,[`&.${h.clickable}:hover`]:{backgroundColor:e.vars?`rgba(${e.vars.palette[a].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,i.Fq)(e.palette[a].main,e.palette.action.hoverOpacity)},[`&.${h.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette[a].mainChannel} / ${e.vars.palette.action.focusOpacity})`:(0,i.Fq)(e.palette[a].main,e.palette.action.focusOpacity)},[`& .${h.deleteIcon}`]:{color:e.vars?`rgba(${e.vars.palette[a].mainChannel} / 0.7)`:(0,i.Fq)(e.palette[a].main,.7),"&:hover, &:active":{color:(e.vars||e).palette[a].main}}}}))]}})),k=(0,m.ZP)("span",{name:"MuiChip",slot:"Label",overridesResolver:(e,a)=>{let{ownerState:t}=e,{size:o}=t;return[a.label,a[`label${(0,d.Z)(o)}`]]}})({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap",variants:[{props:{variant:"outlined"},style:{paddingLeft:11,paddingRight:11}},{props:{size:"small"},style:{paddingLeft:8,paddingRight:8}},{props:{size:"small",variant:"outlined"},style:{paddingLeft:7,paddingRight:7}}]});function x(e){return"Backspace"===e.key||"Delete"===e.key}let I=o.forwardRef(function(e,a){let t=(0,$.i)({props:e,name:"MuiChip"}),{avatar:r,className:i,clickable:n,color:d="default",component:m,deleteIcon:g,disabled:u=!1,icon:b,label:y,onClick:C,onDelete:h,onKeyDown:I,onKeyUp:S,size:O="medium",variant:R="filled",tabIndex:z,skipFocusWhenDisabled:w=!1,...L}=t,P=o.useRef(null),F=(0,p.Z)(P,a),T=e=>{e.stopPropagation(),h&&h(e)},V=!1!==n&&!!C||n,M=V||h?v.Z:m||"div",q={...t,component:M,disabled:u,size:O,color:d,iconColor:o.isValidElement(b)&&b.props.color||d,onDelete:!!h,clickable:V,variant:R},N=f(q),j=M===v.Z?{component:m||"div",focusVisibleClassName:N.focusVisible,...h&&{disableRipple:!0}}:{},E=null;h&&(E=g&&o.isValidElement(g)?o.cloneElement(g,{className:(0,l.Z)(g.props.className,N.deleteIcon),onClick:T}):(0,c.jsx)(s,{className:(0,l.Z)(N.deleteIcon),onClick:T}));let D=null;r&&o.isValidElement(r)&&(D=o.cloneElement(r,{className:(0,l.Z)(N.avatar,r.props.className)}));let W=null;return b&&o.isValidElement(b)&&(W=o.cloneElement(b,{className:(0,l.Z)(N.icon,b.props.className)})),(0,c.jsxs)(Z,{as:M,className:(0,l.Z)(N.root,i),disabled:!!V&&!!u||void 0,onClick:C,onKeyDown:e=>{e.currentTarget===e.target&&x(e)&&e.preventDefault(),I&&I(e)},onKeyUp:e=>{e.currentTarget===e.target&&h&&x(e)&&h(e),S&&S(e)},ref:F,tabIndex:w&&u?-1:z,ownerState:q,...j,...L,children:[D||W,(0,c.jsx)(k,{className:(0,l.Z)(N.label),ownerState:q,children:y}),E]})})}};