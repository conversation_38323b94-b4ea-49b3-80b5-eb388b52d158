(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8892],{81873:function(e,t,s){Promise.resolve().then(s.bind(s,50839))},50839:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return x}});var a=s(57437),r=s(77210),i=s(2265),n=s(86484),l=s(93214),o=s(46172),c=s(4174);let d=async(e,t,s,a)=>{let r=e.t;return new Promise(async(i,n)=>{l.yX.post(o.Y.signup,e).then(e=>{t(r("register:checkemail")),s(null),a(!0),e?.data&&i(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(409===e.response.status&&s(e.response.data.message),500===e.response.status&&e.response.data&&s(c.X.InvalidEmail),e.response&&400===e.response.status&&s(c.X.INVALID_SIGNUP_DATA)),e&&n(e),t(null)})})},u=(e,t,s)=>{let a=(0,n.useQueryClient)();return(0,n.useMutation)({mutationFn:a=>d(a,e,t,s),onSuccess:()=>{a.invalidateQueries("user")},onError:e=>{e.message=""}})};var m=s(8552),h=s(93127),p=s(80657),g=e=>{let{t,locale:s}=e,[r,n]=(0,i.useState)(!1),[l,o]=(0,i.useState)(""),[c,d]=(0,i.useState)(!1),g=u(d,o,n),f=async(e,s)=>{let{resetForm:a}=s,{confirmPassword:r,acceptTerms:i,...n}=e;try{await g.mutateAsync({...n,t}),a(),setTimeout(()=>{window.location.href=`/${p.jb.login.route}`},1e4)}catch(e){}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.Z,{initialValues:{firstName:"",lastName:"",email:"",phone:"",country:"",industry:"",password:"",confirmPassword:"",acceptTerms:!1},handleSubmit:f,successMsg:c,errorMsg:l,t:t,loading:r,setErrMsg:o,setRegisterSuccess:d}),(0,a.jsx)(m.Z,{t:t,type:"register",locale:s})]})},f=s(55788),x=e=>{let{params:t}=e,{t:s}=(0,f.$G)();return(0,a.jsx)(r.Z,{id:"auth-layout",title:s("register:register"),subTitle:s("register:registerMessage"),children:(0,a.jsx)(g,{locale:t?.locale,t:s})})}},77210:function(e,t,s){"use strict";var a=s(57437),r=s(98489),i=s(90191);t.Z=function(e){let{children:t,title:s,subTitle:n}=e;return(0,a.jsx)("div",{id:"auth-layout",style:{backgroundImage:`url(${i.default.src})`},children:(0,a.jsxs)(r.default,{className:"container custom-max-width",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"heading-h1 text-white",children:s}),(0,a.jsx)("p",{className:"sub-heading text-white",children:n})]}),(0,a.jsx)("div",{children:t})]})})}},30100:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});var a=s(57437);function r(e){let{errMsg:t,success:s}=e;return(0,a.jsxs)(a.Fragment,{children:[t&&(0,a.jsx)("div",{className:"errorMsgBanner",children:t}),s&&(0,a.jsx)("div",{className:"successMsgBanner",children:s})]})}},88415:function(e,t,s){"use strict";var a=s(57437),r=s(2265);s(25330),t.Z=e=>{let{src:t,alt:s}=e,[i,n]=(0,r.useState)(!1),l=(0,r.useRef)();return(0,r.useEffect)(()=>{let e=new IntersectionObserver(t=>{let[s]=t;s.isIntersecting&&(n(!0),e.unobserve(s.target))},{threshold:.1});return l.current&&e.observe(l.current),()=>e.disconnect()},[]),(0,a.jsx)("img",{ref:l,src:i?t:void 0,"data-src":t,alt:s,loading:"lazy",style:{opacity:i?1:.5,transition:"opacity 0.3s"}})}},93214:function(e,t,s){"use strict";s.d(t,{cU:function(){return l},xk:function(){return n},yX:function(){return i}});var a=s(83464),r=s(40257);let i=a.Z.create({baseURL:r.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),n=a.Z.create({baseURL:r.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),l=a.Z.create({baseURL:r.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"});a.Z.create({baseURL:r.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))},8552:function(e,t,s){"use strict";s.d(t,{Z:function(){return v}});var a,r,i,n,l,o,c=s(57437),d=s(41774),u=s(94746);function m(){return(m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}var h=e=>u.createElement("svg",m({xmlns:"http://www.w3.org/2000/svg",width:25,height:24,fill:"none"},e),a||(a=u.createElement("g",{clipPath:"url(#coloredMicrosoft_svg__a)"},u.createElement("path",{fill:"#fff",d:"M3.5 3h18v18h-18z"}),u.createElement("path",{fill:"#F35325",d:"M4.281 3.782h7.826v7.826H4.281z"}),u.createElement("path",{fill:"#81BC06",d:"M12.89 3.782h7.827v7.826H12.89z"}),u.createElement("path",{fill:"#05A6F0",d:"M4.281 12.392h7.826v7.826H4.281z"}),u.createElement("path",{fill:"#FFBA08",d:"M12.89 12.392h7.827v7.826H12.89z"}))),r||(r=u.createElement("defs",null,u.createElement("clipPath",{id:"coloredMicrosoft_svg__a"},u.createElement("path",{fill:"#fff",d:"M3.5 3h18v18h-18z"})))));function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}var g=e=>u.createElement("svg",p({xmlns:"http://www.w3.org/2000/svg",width:25,height:24,fill:"none"},e),i||(i=u.createElement("path",{fill:"#FFC107",d:"M22.306 10.042H21.5V10h-9v4h5.651A5.998 5.998 0 0 1 6.5 12a6 6 0 0 1 6-6c1.53 0 2.921.577 3.98 1.52L19.31 4.69A9.95 9.95 0 0 0 12.5 2c-5.522 0-10 4.478-10 10s4.478 10 10 10 10-4.477 10-10c0-.67-.069-1.325-.195-1.959"})),n||(n=u.createElement("path",{fill:"#FF3D00",d:"m3.652 7.346 3.286 2.409A6 6 0 0 1 12.499 6c1.53 0 2.921.577 3.98 1.52l2.83-2.829A9.95 9.95 0 0 0 12.498 2a9.99 9.99 0 0 0-8.847 5.346"})),l||(l=u.createElement("path",{fill:"#4CAF50",d:"M12.5 22c2.583 0 4.93-.988 6.705-2.596l-3.095-2.619A5.96 5.96 0 0 1 12.5 18a6 6 0 0 1-5.641-3.973L3.598 16.54C5.253 19.778 8.614 22 12.5 22"})),o||(o=u.createElement("path",{fill:"#1976D2",d:"M22.306 10.042H21.5V10h-9v4h5.651a6 6 0 0 1-2.043 2.785h.002l3.095 2.619C18.985 19.602 22.5 17 22.5 12c0-.67-.069-1.325-.195-1.959"}))),f=s(80657),x=s(40257),v=function(e){let{type:t,t:s,redirection:a,removeBottomSection:r,locale:i}=e,n=async()=>{window.location.href=`${x.env.NEXT_PUBLIC_BASE_API_URL}/auth/google?state=${encodeURIComponent(JSON.stringify({isSignup:"register"===t,redirection:a}))}`},l=async()=>{window.location.href=`${x.env.NEXT_PUBLIC_BASE_API_URL}/auth/microsoft?state=${encodeURIComponent(JSON.stringify({isSignup:"register"===t,redirection:a}))}`};return(0,c.jsxs)("div",{id:"connect-social-media",children:[(0,c.jsxs)("div",{className:"top-section",children:[(0,c.jsx)("span",{className:"line"}),(0,c.jsx)("p",{className:"title",children:s("register"==t?"register:registerWith":"login:orConnect")}),(0,c.jsx)("span",{className:"line"})]}),(0,c.jsxs)("div",{className:"btns-section",children:[(0,c.jsx)(d.default,{leftIcon:!0,icon:(0,c.jsx)(g,{}),text:"Google",onClick:n,className:"btn-social-media"}),(0,c.jsx)(d.default,{leftIcon:!0,icon:(0,c.jsx)(h,{}),text:"Microsoft",onClick:l,className:"btn-social-media"})]}),!r&&(0,c.jsxs)("div",{className:"bottom-section",children:[s("register"==t?"register:haveAnAccount":"login:haveAnAccount"),"register"==t?(0,c.jsx)(d.default,{text:s("login:login"),link:`/${f.jb.login.route}`,locale:i,className:"btn yellow text-left"}):(0,c.jsx)(d.default,{text:s("register:register"),link:`/${f.jb.register.route}`,locale:i,className:"btn yellow text-left"})]})]})}},93127:function(e,t,s){"use strict";s.d(t,{Z:function(){return C}});var a=s(57437),r=s(30100),i=s(41774),n=s(51288),l=s(28397),o=s(49651);s(25330);var c=s(34422),d=s(14759),u=s(94395),m=s(89414),h=s(89126),p=s(64393),g=s(77584),f=s(15735),x=s(81799),v=s(68218),j=s(23996),A=s(59832),b=s(85860),N=s(11953),y=s(35389),w=s(63993),E=s(2265),_=s(24086),Z=s(88415);function C(e){let{initialValues:t,handleSubmit:s,successMsg:C,errorMsg:I,t:P,loading:S,setRegisterSuccess:L,setErrMsg:T}=e,F=o.PhoneNumberUtil.getInstance(),U=e=>{try{return F.isValidNumber(F.parseAndKeepRawInput(e))}catch(e){return!1}};c.Z_().test("is-valid-phone",P("validations:phoneFormat"),e=>U(e));let[M,R]=(0,E.useState)(!1),[B,O]=(0,E.useState)(!1),k=()=>{R(!M)},D=()=>{O(!B)},X=e=>c.Ry().shape({firstName:c.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),lastName:c.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:c.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),country:c.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),industry:c.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),password:c.Z_().matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).+$/,e("validations:invalidPassword")).required(e("validations:emptyField")),confirmPassword:c.Z_().oneOf([c.iH("password"),null],e("validations:passwordMatch")).required(e("validations:emptyField")),acceptTerms:c.O7().oneOf([!0],e("validations:emptyField"))});return(0,a.jsx)(w.J9,{initialValues:t,onSubmit:s,validationSchema:()=>X(P),children:e=>{let{values:t,handleChange:s,errors:o,touched:c,setFieldValue:E}=e;return(0,a.jsxs)(w.l0,{id:"login-form",className:"pentabell-form",children:[(0,a.jsxs)(m.default,{container:!0,rowSpacing:4,columnSpacing:3,className:"form-content",children:[(0,a.jsxs)(m.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(h.Z,{className:"form-group light",children:[(0,a.jsxs)(p.Z,{className:"label-pentabell light",children:[P("register:firstName"),"*"]}),(0,a.jsx)(g.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:P("register:firstName"),variant:"standard",type:"text",name:"firstName",value:t.firstName,onChange:e=>{s(e),T(""),L(!1)},error:!!(o.firstName&&c.firstName),disabled:S})]}),(0,a.jsx)(w.Bc,{name:"firstName",children:e=>(0,a.jsx)(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(m.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(h.Z,{className:"form-group light",children:[(0,a.jsxs)(p.Z,{className:"label-pentabell light",children:[P("register:lastName"),"*"]}),(0,a.jsx)(g.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:P("register:lastName"),variant:"standard",type:"text",name:"lastName",value:t.lastName,onChange:e=>{s(e),T(""),L(!1)},error:!!(o.lastName&&c.lastName),disabled:S})]}),(0,a.jsx)(w.Bc,{name:"lastName",children:e=>(0,a.jsx)(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(m.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(h.Z,{className:"form-group light",children:[(0,a.jsx)(p.Z,{className:"label-pentabell light",children:"Email*"}),(0,a.jsx)(g.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:"Email",variant:"standard",name:"email",type:"email",value:t.email,onChange:e=>{s(e),T(""),L(!1)},error:!!(o.email&&c.email),disabled:S})]}),(0,a.jsx)(w.Bc,{name:"email",children:e=>(0,a.jsx)(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(m.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(h.Z,{className:"form-group light",children:[(0,a.jsx)(p.Z,{className:"label-pentabell light",children:P("register:phoneNumber")}),(0,a.jsx)(_.sb,{defaultCountry:"fr",className:"input-pentabell light",value:t.phone,onChange:e=>{E("phone",e),T(""),L(!1)},flagComponent:e=>(0,a.jsx)(Z.Z,{...e})})]}),(0,a.jsx)(w.Bc,{name:"phone",children:e=>(0,a.jsx)(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(m.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(h.Z,{className:"form-group light",children:[(0,a.jsxs)(p.Z,{className:"label-pentabell light",children:[P("register:country"),"*"]}),(0,a.jsx)(x.Z,{className:"input-pentabell light",id:"tags-standard",options:l.nh,getOptionLabel:e=>e,name:"country",value:t.country,onChange:(e,t)=>{E("country",t),T(""),L(!1)},renderInput:e=>(0,a.jsx)(g.Z,{...e,className:"input-pentabell multiple-select  light",variant:"standard",placeholder:P("aiSourcingService:servicePageForm:chooseOne"),error:!!(o.country&&c.country),disabled:S})})]}),(0,a.jsx)(w.Bc,{name:"country",children:e=>(0,a.jsx)(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(m.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(h.Z,{className:"form-group light",children:[(0,a.jsxs)(p.Z,{className:"label-pentabell light",children:[P("register:industry"),"*"]}),(0,a.jsx)(x.Z,{className:"input-pentabell light",id:"tags-standard",options:n.RI,getOptionLabel:e=>e,name:"industry",value:t.industry,onChange:(e,t)=>{E("industry",t),T(""),L(!1)},renderInput:e=>(0,a.jsx)(g.Z,{...e,className:"input-pentabell multiple-select  light",variant:"standard",placeholder:P("aiSourcingService:servicePageForm:chooseOne"),error:!!(o.industry&&c.industry),disabled:S})})]}),(0,a.jsx)(w.Bc,{name:"industry",children:e=>(0,a.jsx)(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(m.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(h.Z,{className:"form-group light",children:[(0,a.jsxs)(p.Z,{className:"label-pentabell light",children:[P("register:password"),"*"]}),(0,a.jsx)(v.Z,{autoComplete:"new-password",className:"input-pentabell light",placeholder:P("register:password"),variant:"standard",type:M?"text":"password",value:t.password,name:"password",onChange:e=>{s(e),T(""),L(!1)},error:!!(o.password&&c.password),disabled:S,endAdornment:(0,a.jsx)(j.Z,{position:"end",children:(0,a.jsx)(A.Z,{className:`toggle-password fa fa-fw ${M?"fa-eye":"fa-eye-slash"}`,onClick:k,"aria-label":"toggle password visibility",edge:"end",children:M?(0,a.jsx)(d.Z,{}):(0,a.jsx)(u.Z,{})})})})]}),(0,a.jsx)(w.Bc,{name:"password",children:e=>(0,a.jsx)(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(m.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(h.Z,{className:"form-group light",children:[(0,a.jsxs)(p.Z,{className:"label-pentabell light",children:[P("register:confirmPassword"),"*"]}),(0,a.jsx)(v.Z,{autoComplete:"new-password",className:"input-pentabell light",placeholder:P("register:confirmPassword"),value:t.confirmPassword,type:B?"text":"password",onChange:e=>{s(e),T(""),L(!1)},name:"confirmPassword",disabled:S,error:!!(o.confirmPassword&&c.confirmPassword),endAdornment:(0,a.jsx)(j.Z,{position:"end",children:(0,a.jsx)(A.Z,{className:`toggle-password fa fa-fw ${B?"fa-eye":"fa-eye-slash"}`,onClick:D,"aria-label":"toggle password visibility",edge:"end",children:B?(0,a.jsx)(d.Z,{}):(0,a.jsx)(u.Z,{})})})})]}),(0,a.jsx)(w.Bc,{name:"confirmPassword",children:e=>(0,a.jsx)(f.Z,{variant:"filled",severity:"error",children:e})})]})]}),(0,a.jsx)(b.Z,{className:"checkbox-pentabell light",control:(0,a.jsx)(N.Z,{name:"acceptTerms",checked:t.acceptTerms,onChange:e=>{s(e),T(""),L(!1)}}),label:P("register:message")}),(0,a.jsx)(w.Bc,{name:"acceptTerms",children:e=>(0,a.jsx)(f.Z,{variant:"filled",severity:"error",children:e})}),(0,a.jsx)(r.Z,{errMsg:I,success:C}),(0,a.jsx)(i.default,{text:S?(0,a.jsx)(y.default,{}):P("register:register"),className:"btn btn-filled full-width btn-submit",type:"submit",disabled:S})]})}})}},4174:function(e,t,s){"use strict";s.d(t,{X:function(){return a}});let a={INCORRECT_PASSWORD:"Password incorrect",EMAIL_NOT_FOUND:"There's no user with this email",InvalidEmail:"Invalid email address.",FailedUpdateFile:"failed to upload file try again !!",ACCOUNT_NOTACTIVATED:"Your account is not activated yet. Please check your email for the activation link.",EMAIL_EXIST:"Email already exist",FileExist:"File already exists",FileNotFound:"File not found!",error:"'An unknown error occurred'",ERROR:"An error occurred. Please try again later !",INVALID_SIGNUP_DATA:"Invalid signup data. Please check your information and try again.",CandidateNotFound:"Candidate not found",ResetPasswordLink:"Check your email. Link expires in 10 mins!",VALIDATIONS:{INVALID_EMAIL:"Invalid email",EMPTY_FIELD:"Please fill in the required fields!",END_DATE:"End date must be after start date",MIN_DATE:"Date of birth must be after 1950",MAX_DATE:"Date must be before 2005",MIN_LENGTH:"Field must be at least 3 characters",MAX_LENGTH:"Field must be at most 20 characters",REQUIRED:"This field is required!",INVALID_PASSWORD:"Password requires at least one uppercase, one lowercase letter, and one digit",PASSWORDMATCH:"password must match"}}},46172:function(e,t,s){"use strict";s.d(t,{Y:function(){return r},v:function(){return a}});let a=s(40257).env.NEXT_PUBLIC_BASE_API_URL,r={seo:"seoTags",auth:"/auth/signin",logout:"/auth/logout",candidatures:"/applications",signup:"/auth/signup",forgetPassword:"/auth/forgot-password",resetPassword:"/auth/reset-password",guides:"/guides",currentUser:"/users/current",updateUser:"/users",users:"/users",categoryGuides:"guidecategory",candidate:"/candidates",report:"/report",skills:"/skills",files:"/files",applications:"/applications",sliders:"/sliders",favoris:"/candidate/favourite",articles:"/articles",categories:"/categories",blog:"/blog",category:"/categories",opportunity:"/opportunities",seoOpportunity:"/seoOpportunity",newsletter:"/newsletter",contact:"/contact",favourite:"/favourite",contacts:"contacts",comments:"/comments",statistics:"/statistics",events:"/events",baseUrl:`${a}`}},90191:function(e,t,s){"use strict";s.r(t),t.default={src:"/_next/static/media/bg-auth.1842cff2.png",height:738,width:1440,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAElBMVEU7Tl4WKTkxRFQ/UmIlOEhOYXEF1jp9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIUlEQVR4nBXBgQ0AMAzCsEDo/y9Ps1G962gLUJpsIvmQBwVgAD+bqpS2AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:4}}},function(e){e.O(0,[3661,8760,775,948,5788,2996,7648,3464,455,2662,7183,2296,747,3200,7584,6484,9832,9414,8467,7571,1799,5719,3993,2412,4782,4244,1774,2971,2117,1744],function(){return e(e.s=81873)}),_N_E=e.O()}]);