"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_locales_en_personalinformation_json";
exports.ids = ["_rsc_src_locales_en_personalinformation_json"];
exports.modules = {

/***/ "(rsc)/./src/locales/en/personalinformation.json":
/*!*************************************************!*\
  !*** ./src/locales/en/personalinformation.json ***!
  \*************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"ProfilePicture":"Profile picture","title":"Personal informations","upload-text":" Add your profile picture","professionalInformation":"Professional Information","update-text":" Upload Picture","upload-description":" Browse photo or drop here","text-label":"Your CV/Resume*","fileRequirements":"JPG, PNG under 15MB","profilePicture":"Profile Picture","upload-text-cv":"Add CV/Resume","upload-description-cv":"  Browse file or drop here. Pdf only","firstname":"First name*","lastname":"Last name*","jobtitle":"Job title","dateofbirth":"Date of birth","gender":"Gender","nationalities":"Nationalities*","nationaliteplaceholder":"Choose your nationalities...","genderplaceholder":"Select a gender","currentadress":"Current address","emailAdress":"Email adress","phoneNumber":"Phone number*","confirmRemoveProfilePicture":"Do you really want to remove your profile picture?"}');

/***/ })

};
;