import { COUNTRIES_LIST_FLAG } from "@/config/countries";
import { INDUSTRIES_LIST } from "@/config/inustries";

import { htmlToText } from "html-to-text";
import { Role } from "./constants";
import { MenuList } from "@/helpers/MenuList";
import { Notifications_LIST } from "@/config/Constants";
import {
  adminPermissionsRoutes,
  candidatePermissionsRoutes,
  editorPermissionsRoutes,
} from "@/helpers/routesList";

export const getExtension = (fileType) => {
  switch (fileType) {
    case "application/pdf":
      return "pdf";
    case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
      return "docx";
    case "image/png":
      return "png";
    case "image/jpg":
      return "jpg";
    case "image/jpeg":
      return "jpeg";

    default:
      return "unknown";
  }
};

// functions.js
export function formatDateArticle(dateString) {
  if (!dateString) return ""; // Handle empty or undefined dateString

  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    console.error(`Invalid date string: ${dateString}`);
    return "";
  }

  const dateOptions = { year: "numeric", month: "long", day: "numeric" };
  const timeOptions = { hour: "numeric", minute: "2-digit", hour12: true };

  const formattedDate = date.toLocaleDateString("en-US", dateOptions);
  const formattedTime = date.toLocaleTimeString("en-US", timeOptions);

  return `${formattedDate}, ${formattedTime}`;
}

export function formatDate(dateString) {
  if (!dateString) return ""; // Handle empty or undefined dateString

  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    console.error(`Invalid date string: ${dateString}`);
    return "";
  }

  const options = { year: "numeric", month: "2-digit", day: "2-digit" };
  return date.toLocaleDateString("en-US", options);
}
export function formatResumeName(resume, fullName) {
  if (typeof resume !== "string") {
    console.error("Le nom du fichier de CV n'est pas valide.");
    return "CV_Anonyme";
  }

  const extension = resume.split(".").pop();

  if (!extension || extension === resume) {
    console.error("Le fichier n'a pas d'extension valide.");
    return `CV_${fullName}`;
  }

  return `CV_${fullName}.${extension}`;
}

export const processContent = (htmlContent) => {
  const plainTextContent = htmlToText(htmlContent, { wordwrap: false });
  return plainTextContent.length > 150
    ? plainTextContent.substring(0, 150) + "..."
    : plainTextContent;
};

export const industryExists = (text) => {
  const result = INDUSTRIES_LIST.some(
    (item) => item.value === text || item.pentabellValue === text
  );
  // if (result == true) {
  //   if (text == "OTHER") {
  //     return false;
  //   } else {
  //     return true;
  //   }
  // }
  return result;
};

export const findIndustryLogoSvg = (text) => {
  return INDUSTRIES_LIST.find(
    (item) =>
      item.value.toLocaleLowerCase() === text.toLocaleLowerCase() ||
      item.pentabellValue === text
  )?.logoSvg;
};

export const findIndustryLabel = (text) => {
  return INDUSTRIES_LIST.find(
    (item) =>
      item.value.toLocaleLowerCase() === text.toLocaleLowerCase() ||
      item.pentabellValue === text
  )?.label;
};
export const findIndustryIcon = (text) => {
  return INDUSTRIES_LIST.find(
    (item) =>
      item.value.toLocaleLowerCase() === text.toLocaleLowerCase() ||
      item.pentabellValue === text
  )?.icon;
};
export const findIndustryColoredIcon = (text) => {
  return INDUSTRIES_LIST.find(
    (item) =>
      (item.value &&
        text &&
        String(item.value).toLocaleLowerCase() ===
          String(text).toLocaleLowerCase()) ||
      item.pentabellValue === text
  )?.iconColored;
};
export const findnotificationColoredIcon = (text) => {
  return Notifications_LIST.find(
    (item) =>
      (item.value &&
        text &&
        String(item.value).toLocaleLowerCase() ===
          String(text).toLocaleLowerCase()) ||
      item.pentabellValue === text
  )?.iconColored;
};
export const findIndustryClassname = (text) => {
  return INDUSTRIES_LIST.find(
    (item) =>
      item.value.toLocaleLowerCase() === text.toLocaleLowerCase() ||
      item.pentabellValue.toLocaleLowerCase() === text.toLocaleLowerCase()
  )?.classname;
};
export const findIndustryLink = (text) => {
  return INDUSTRIES_LIST.find(
    (item) =>
      item.value.toLocaleLowerCase() === text.toLocaleLowerCase() ||
      item.pentabellValue.toLocaleLowerCase() === text.toLocaleLowerCase()
  )?.link;
};
export const findIndustryByLargeIcon = (text) => {
  return INDUSTRIES_LIST.find(
    (item) =>
      item.value.toLocaleLowerCase() === text.toLocaleLowerCase() ||
      item.pentabellValue.toLocaleLowerCase() === text.toLocaleLowerCase()
  )?.largeIcon;
};

export const findCountryFlag = (text) => {
  return COUNTRIES_LIST_FLAG.find(
    (item) => item.value === text || item.pentabellValue === text
  )?.flag;
};
export const findCountryLabel = (text) => {
  return COUNTRIES_LIST_FLAG.find(
    (item) => item.value === text || item.pentabellValue === text
  )?.label;
};

export const getMenuListByRole = (currentUser) => {
  if (currentUser?.roles?.includes(Role.ADMIN)) {
    return MenuList?.admin;
  }
  if (currentUser?.roles?.includes(Role.CANDIDATE)) {
    return MenuList?.candidate;
  }
  if (currentUser?.roles?.includes(Role.EDITOR)) {
    return MenuList?.editor;
  }
};

export const getRoutesListByRole = (currentUser) => {
  if (currentUser?.roles?.includes(Role.ADMIN)) {
    return adminPermissionsRoutes;
  }
  if (currentUser?.roles?.includes(Role.CANDIDATE)) {
    return candidatePermissionsRoutes;
  }
  if (currentUser?.roles?.includes(Role.EDITOR)) {
    return editorPermissionsRoutes;
  }
};

export const getCountryImage = (country) => {
  const formattedCountry = country.toLowerCase().replace(/ /g, "-");

  return `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/maps/${formattedCountry}.png`;
};

export const getCountryEventImage = (country) => {
  const formattedCountry = country.toLowerCase().replace(/ /g, "-");

  return `https://www.pentabell.com/eventMaps/${formattedCountry}.svg`;
};

export const generateLocalizedSlug = (locale, slug) => {
  return locale === "en" ? `${slug}/` : `/fr${slug}/`;
};

export const capitalizeFirstLetter = (str) => {
  return str
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

export function stringToColor(string) {
  let hash = 0;
  let i;

  /* eslint-disable no-bitwise */
  for (i = 0; i < string?.length; i += 1) {
    hash = string.charCodeAt(i) + ((hash << 5) - hash);
  }

  let color = "#";

  for (i = 0; i < 3; i += 1) {
    const value = (hash >> (i * 8)) & 0xff;
    color += `00${value.toString(16)}`.slice(-2);
  }
  /* eslint-enable no-bitwise */

  return color;
}

export function stringAvatar(name) {
  return {
    sx: {
      bgcolor: stringToColor(name),
    },
    children: `${name?.split(" ")[0][0]}${name?.split(" ")[1][0]}`,
  };
}

export const splitFirstWord = (txt) => {
  const words = txt?.toString().split(" ") || [];
  const firstWord = words[0];
  const restOfText = words.slice(1).join(" ");
  return (
    <>
      <span className="first-word">{firstWord} </span>
      {restOfText}
    </>
  );
};
export const highlightMatchingWords = (txt, wordsToHighlight) => {
  if (!txt) return null;

  const regex = new RegExp(`\\b(${wordsToHighlight.join("|")})\\b`, "gi");

  return (
    <>
      {txt.split(regex).map((segment, index) => {
        const isMatch = wordsToHighlight.includes(segment.trim());
        return (
          <span key={index} className={isMatch ? "last-word" : ""}>
            {segment}
          </span>
        );
      })}
    </>
  );
};
export const splitLastWord = (txt) => {
  const words = txt?.toString().split(" ") || [];
  const lastWord = words[words.length - 1]; // Get the last word
  const restOfText = words.slice(0, -1).join(" "); // Join all except the last word
  return (
    <>
      {restOfText && <span>{restOfText} </span>}{" "}
      {/* Add a space after restOfText if it exists */}
      <span className="last-word">{lastWord}</span>
    </>
  );
};
export const formatDuration = (receivedTime) => {
  const duration = moment.duration(moment().diff(moment(receivedTime)));

  if (duration.asDays() >= 1) {
    return `${Math.floor(duration.asDays())}j`;
  } else if (duration.asHours() >= 1) {
    return `${Math.floor(duration.asHours())}h`;
  } else {
    return `${Math.floor(duration.minutes())}min`;
  }
};

export const isExpired = (dateOfExpiration) => {
  const currentDate = new Date();
  let expirationDate = new Date(currentDate);

  if (dateOfExpiration) expirationDate = new Date(dateOfExpiration);
  else expirationDate.setMonth(expirationDate.getMonth() + 3);

  return expirationDate < currentDate;
};

export function truncateByCharacter(text, maxChars) {
  if (text?.length <= maxChars) return text;
  return text?.slice(0, maxChars).trim() + "…";
}

export const getSlugByIndustry = (industry) => {
  const industryValue = industry || "";
  switch (industryValue) {
    case "Energies":
      return "energies";
    case "It & Telecom":
      return "it-telecom";
    case "Banking":
      return "banking-insurance";
    case "Transport":
      return "transport";
    case "Pharmaceutical":
      return "pharmaceutical";
    default:
      return "other";
  }
};
