"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/features/stats/charts/ApplicationsByStatus.jsx":
/*!************************************************************!*\
  !*** ./src/features/stats/charts/ApplicationsByStatus.jsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ApplicationsByStatus; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon.svg\");\n/* harmony import */ var _components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/charts/CustomPieChart */ \"(app-pages-browser)/./src/components/charts/CustomPieChart.jsx\");\n/* harmony import */ var _stats_hooks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../stats.hooks */ \"(app-pages-browser)/./src/features/stats/stats.hooks.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ApplicationsByStatus() {\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [dateFromApplication, setDateFromApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToApplication, setDateToApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchApplication, setSearchApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getDataPieApplications = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_7__.useGetApplicationsStat)({\n        dateFrom: dateFromApplication,\n        dateTo: dateToApplication,\n        barChart: null\n    });\n    const resetSearchApplication = ()=>{\n        setDateToApplication(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromApplication(\"2024-09-01\");\n        setSearchApplication(!searchApplication);\n    };\n    const pieChart = {\n        title: t(\"statsDash:applicationsByStatus\"),\n        dataset: getDataPieApplications?.data?.map((app)=>({\n                label: app.status,\n                value: app.totalApplications\n            })),\n        colors: [\n            \"#E97611\",\n            \"#018055\",\n            \"#D73232\"\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieApplications.refetch();\n    }, [\n        searchApplication\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        className: \"card\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"heading-h3\",\n                    gutterBottom: true,\n                    children: pieChart.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    elevation: 0,\n                    disableGutters: true,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            \"aria-controls\": \"panel1bh-content\",\n                            id: \"panel1bh-header\",\n                            className: \"svg-accordion\",\n                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                lineNumber: 70,\n                                columnNumber: 25\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"label-pentabell\",\n                                children: t(\"statsDash:filters\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            elevation: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                container: true,\n                                className: \"chart-grid\",\n                                spacing: 1,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            label: t(\"statsDash:fromDate\"),\n                                            type: \"date\",\n                                            value: dateFromApplication,\n                                            onChange: (e)=>setDateFromApplication(e.target.value),\n                                            fullWidth: true,\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            label: t(\"statsDash:toDate\"),\n                                            type: \"date\",\n                                            value: dateToApplication,\n                                            onChange: (e)=>setDateToApplication(e.target.value),\n                                            fullWidth: true,\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 3,\n                                        sm: 1,\n                                        md: 4,\n                                        className: \"btns-filter dashboard\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            className: \"btn btn-outlined btn-refresh full-width\",\n                                            onClick: resetSearchApplication\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 11,\n                                        sm: 11,\n                                        md: 8,\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                text: t(\"statsDash:filter\"),\n                                                onClick: ()=>{\n                                                    setSearchApplication(!searchApplication);\n                                                },\n                                                className: \"btn btn-outlined btn-filter-stat full-width\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"chart-wrapper\",\n                    children: [\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            donuts: true,\n                            chart: pieChart\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        \" \",\n                        pieChart.dataset?.some((item)=>item[\"value\"] > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"labelstats-wrapper\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"accepted-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:accepted\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"rejected-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:rejected\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"pending-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:pending\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ApplicationsByStatus.jsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(ApplicationsByStatus, \"s3Jyr6DPAAN3PZd4h1FVS+uNqgc=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_7__.useGetApplicationsStat\n    ];\n});\n_c = ApplicationsByStatus;\nvar _c;\n$RefreshReg$(_c, \"ApplicationsByStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/stats/charts/ApplicationsByStatus.jsx\n"));

/***/ })

});