import { Schema, model, Types } from 'mongoose';
import { IDownload } from './downloadguide.interface';
const downloadSchema = new Schema<IDownload>(
    {
        guides: [
            {
                type: Types.ObjectId,
                ref: 'Guide',
            },
        ],
        email: { type: String },
        firstName: { type: String },
        lastName: { type: String },
        companysize: { type: String },
        Headquarters: { type: String },
    },
    {
        timestamps: true,
    },
);

export const downloadModel = model<IDownload>('Download', downloadSchema);
