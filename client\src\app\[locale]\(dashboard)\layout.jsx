import { ToastContainer } from "react-toastify";
import { dir } from "i18next";

import TranslationsProvider from "@/components/TranslationProvider";
import { ReactQueryProvider } from "@/lib/react-query-client";
import initTranslations from "@/app/i18n";

import "react-toastify/dist/ReactToastify.css";

import Dashboard from "@/components/layouts/Dashboard";

export const metadata = {
  title: "Dashboard",
  robots: "noindex, nofollow",
};

const DashboardLayout = async ({ children, params: { locale } }) => {
  const { resources } = await initTranslations(locale, [
    "personalinformation",
    "ProfessionalInformations",
    "global",
    "listopportunity",
    "application",
    "statisticsDash",
    "statisticsApp",
    "messages",
    "validations",
    "HomeDashboard",
    "listCategory",
    "listArticle",
    "guides",
    "listCommentaire",
    "createArticle",
    "sliders",
    "createOpportunity",
    "experience",
    "education",
    "certification",
    "listusers",
    "menu",
    "comments",
    "footer",
    "register",
    "settings",
    "validations",
    "sidebar",
    "favourite",
    "resumes",
    "contact",
    "getInTouch",
    "statsTotalNumbers",
    "seoSettings",
    "statsDash",
    "steps",
    "eventForm",
    "event",
    "country"
  ]);

  return (
    <html lang={locale} dir={dir(locale)}>
      <body>
        <ReactQueryProvider>
          <ToastContainer />
          <TranslationsProvider
            namespaces={[
              "personalinformation",
              "ProfessionalInformations",
              "global",
              "listCategory",
              "listArticle",
              "HomeDashboard",
              "listCommentaire",
              "application",
              "createArticle",
              "sidebar",
              "listusers",
              "createOpportunity",
              "statisticsDash",
              "statisticsApp",
              "messages",
              "validations",
              "experience",
              "sliders",
              "guides",
              "comments",
              "education",
              "listopportunity",
              "certification",
              "menu",
              "footer",
              "register",
              "settings",
            ]}
            locale={locale}
            resources={resources}
          >
            <Dashboard locale={locale}>{children}</Dashboard>
          </TranslationsProvider>
        </ReactQueryProvider>
      </body>
    </html>
  );
};

export default DashboardLayout;
