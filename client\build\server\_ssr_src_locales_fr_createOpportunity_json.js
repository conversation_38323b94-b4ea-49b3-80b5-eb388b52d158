"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_fr_createOpportunity_json";
exports.ids = ["_ssr_src_locales_fr_createOpportunity_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/fr/createOpportunity.json":
/*!***********************************************!*\
  !*** ./src/locales/fr/createOpportunity.json ***!
  \***********************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"englishVersion":"Version anglaise","frenchVersion":"Version française","addOpportunity":"AJOUTER UNE OPPORTUNITÉ","industry":"Industrie","country":"Pays","dateOfExpiration":"Date d\'expiration","title":"Titre","jobDescription":"Description du poste","summary":"Description du poste","visibility":"Visibilité","gender":"Genre","publishDate":"Sélectionner la date de publication","placeholderIndustry":"Choisissez une industrie...","placeholderCountry":"Choisissez un pays...","placeholderGender":"Choisissez un genre...","placeholderPublishDate":"Choisissez la date de publication...","placeholderDateOfExpiration":"Choisissez la date d\'expiration...","placeholderVisibility":"Choisissez une visibilité","settings":"Paramètre","job":"Emploi","actions":"Actions","search":"Rechercher par titre d\'emploi...","levelOfExperience":"Niveau d\'expérience","url":"Url","publishNow":"Publier maintenant","editopportunity":"Modifier l\'opportunité ","contratType":"Type de contrat"}');

/***/ })

};
;