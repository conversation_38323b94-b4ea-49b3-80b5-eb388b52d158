"use client";

import { useMediaQuery, useTheme } from "@mui/material";

import OpportunityItemByGrid from "./OpportunityItemByGrid";
import OpportunityItemByList from "./OpportunityItemByList";

export default function OpportunityItem({
  key,
  opportunity,
  language,
  isList,
}) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  if (isList && !isMobile)
    return (
      <OpportunityItemByList
        key={key}
        opportunity={opportunity}
        language={language}
      />
    );
  else
    return (
      <OpportunityItemByGrid
        key={key}
        opportunity={opportunity}
        language={language}
      />
    );
}
