"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_en_resumes_json";
exports.ids = ["_ssr_src_locales_en_resumes_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/en/resumes.json":
/*!*************************************!*\
  !*** ./src/locales/en/resumes.json ***!
  \*************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"lastUpdated":"Last updated","delete":"Do you really want to delete this resume","uploadNewResume":"Upload New Resume","fileRequirements":"Only PDF, DOC, and DOCX files, 500KB max file size.","chooseFile":"Choose file","listOfResumes":"List Of Resumes"}');

/***/ })

};
;