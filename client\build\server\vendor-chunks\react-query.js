/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-query";
exports.ids = ["vendor-chunks/react-query"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-query/es/core/focusManager.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-query/es/core/focusManager.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\n\nvar FocusManager = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(FocusManager, _Subscribable);\n\n  function FocusManager() {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n\n    _this.setup = function (onFocus) {\n      var _window;\n\n      if (!_utils__WEBPACK_IMPORTED_MODULE_1__.isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onFocus();\n        }; // Listen to visibillitychange and focus\n\n\n        window.addEventListener('visibilitychange', listener, false);\n        window.addEventListener('focus', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener);\n          window.removeEventListener('focus', listener);\n        };\n      }\n    };\n\n    return _this;\n  }\n\n  var _proto = FocusManager.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n        _this2 = this;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (focused) {\n      if (typeof focused === 'boolean') {\n        _this2.setFocused(focused);\n      } else {\n        _this2.onFocus();\n      }\n    });\n  };\n\n  _proto.setFocused = function setFocused(focused) {\n    this.focused = focused;\n\n    if (focused) {\n      this.onFocus();\n    }\n  };\n\n  _proto.onFocus = function onFocus() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n\n  _proto.isFocused = function isFocused() {\n    if (typeof this.focused === 'boolean') {\n      return this.focused;\n    } // document global can be unavailable in react native\n\n\n    if (typeof document === 'undefined') {\n      return true;\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(document.visibilityState);\n  };\n\n  return FocusManager;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_2__.Subscribable);\nvar focusManager = new FocusManager();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/focusManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/hydration.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-query/es/core/hydration.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dehydrate: () => (/* binding */ dehydrate),\n/* harmony export */   hydrate: () => (/* binding */ hydrate)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n\n\n// TYPES\n// FUNCTIONS\nfunction dehydrateMutation(mutation) {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state\n  };\n} // Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\n\n\nfunction dehydrateQuery(query) {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash\n  };\n}\n\nfunction defaultShouldDehydrateMutation(mutation) {\n  return mutation.state.isPaused;\n}\n\nfunction defaultShouldDehydrateQuery(query) {\n  return query.state.status === 'success';\n}\n\nfunction dehydrate(client, options) {\n  var _options, _options2;\n\n  options = options || {};\n  var mutations = [];\n  var queries = [];\n\n  if (((_options = options) == null ? void 0 : _options.dehydrateMutations) !== false) {\n    var shouldDehydrateMutation = options.shouldDehydrateMutation || defaultShouldDehydrateMutation;\n    client.getMutationCache().getAll().forEach(function (mutation) {\n      if (shouldDehydrateMutation(mutation)) {\n        mutations.push(dehydrateMutation(mutation));\n      }\n    });\n  }\n\n  if (((_options2 = options) == null ? void 0 : _options2.dehydrateQueries) !== false) {\n    var shouldDehydrateQuery = options.shouldDehydrateQuery || defaultShouldDehydrateQuery;\n    client.getQueryCache().getAll().forEach(function (query) {\n      if (shouldDehydrateQuery(query)) {\n        queries.push(dehydrateQuery(query));\n      }\n    });\n  }\n\n  return {\n    mutations: mutations,\n    queries: queries\n  };\n}\nfunction hydrate(client, dehydratedState, options) {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return;\n  }\n\n  var mutationCache = client.getMutationCache();\n  var queryCache = client.getQueryCache();\n  var mutations = dehydratedState.mutations || [];\n  var queries = dehydratedState.queries || [];\n  mutations.forEach(function (dehydratedMutation) {\n    var _options$defaultOptio;\n\n    mutationCache.build(client, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options == null ? void 0 : (_options$defaultOptio = options.defaultOptions) == null ? void 0 : _options$defaultOptio.mutations, {\n      mutationKey: dehydratedMutation.mutationKey\n    }), dehydratedMutation.state);\n  });\n  queries.forEach(function (dehydratedQuery) {\n    var _options$defaultOptio2;\n\n    var query = queryCache.get(dehydratedQuery.queryHash); // Do not hydrate if an existing query exists with newer data\n\n    if (query) {\n      if (query.state.dataUpdatedAt < dehydratedQuery.state.dataUpdatedAt) {\n        query.setState(dehydratedQuery.state);\n      }\n\n      return;\n    } // Restore query\n\n\n    queryCache.build(client, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options == null ? void 0 : (_options$defaultOptio2 = options.defaultOptions) == null ? void 0 : _options$defaultOptio2.queries, {\n      queryKey: dehydratedQuery.queryKey,\n      queryHash: dehydratedQuery.queryHash\n    }), dehydratedQuery.state);\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/hydration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/index.js":
/*!***************************************************!*\
  !*** ./node_modules/react-query/es/core/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* reexport safe */ _retryer__WEBPACK_IMPORTED_MODULE_0__.CancelledError),\n/* harmony export */   InfiniteQueryObserver: () => (/* reexport safe */ _infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_5__.InfiniteQueryObserver),\n/* harmony export */   MutationCache: () => (/* reexport safe */ _mutationCache__WEBPACK_IMPORTED_MODULE_6__.MutationCache),\n/* harmony export */   MutationObserver: () => (/* reexport safe */ _mutationObserver__WEBPACK_IMPORTED_MODULE_7__.MutationObserver),\n/* harmony export */   QueriesObserver: () => (/* reexport safe */ _queriesObserver__WEBPACK_IMPORTED_MODULE_4__.QueriesObserver),\n/* harmony export */   QueryCache: () => (/* reexport safe */ _queryCache__WEBPACK_IMPORTED_MODULE_1__.QueryCache),\n/* harmony export */   QueryClient: () => (/* reexport safe */ _queryClient__WEBPACK_IMPORTED_MODULE_2__.QueryClient),\n/* harmony export */   QueryObserver: () => (/* reexport safe */ _queryObserver__WEBPACK_IMPORTED_MODULE_3__.QueryObserver),\n/* harmony export */   dehydrate: () => (/* reexport safe */ _hydration__WEBPACK_IMPORTED_MODULE_13__.dehydrate),\n/* harmony export */   focusManager: () => (/* reexport safe */ _focusManager__WEBPACK_IMPORTED_MODULE_10__.focusManager),\n/* harmony export */   hashQueryKey: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_12__.hashQueryKey),\n/* harmony export */   hydrate: () => (/* reexport safe */ _hydration__WEBPACK_IMPORTED_MODULE_13__.hydrate),\n/* harmony export */   isCancelledError: () => (/* reexport safe */ _retryer__WEBPACK_IMPORTED_MODULE_0__.isCancelledError),\n/* harmony export */   isError: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_12__.isError),\n/* harmony export */   notifyManager: () => (/* reexport safe */ _notifyManager__WEBPACK_IMPORTED_MODULE_9__.notifyManager),\n/* harmony export */   onlineManager: () => (/* reexport safe */ _onlineManager__WEBPACK_IMPORTED_MODULE_11__.onlineManager),\n/* harmony export */   setLogger: () => (/* reexport safe */ _logger__WEBPACK_IMPORTED_MODULE_8__.setLogger)\n/* harmony export */ });\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n/* harmony import */ var _queryCache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queryCache */ \"(ssr)/./node_modules/react-query/es/core/queryCache.js\");\n/* harmony import */ var _queryClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./queryClient */ \"(ssr)/./node_modules/react-query/es/core/queryClient.js\");\n/* harmony import */ var _queryObserver__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./queryObserver */ \"(ssr)/./node_modules/react-query/es/core/queryObserver.js\");\n/* harmony import */ var _queriesObserver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./queriesObserver */ \"(ssr)/./node_modules/react-query/es/core/queriesObserver.js\");\n/* harmony import */ var _infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./infiniteQueryObserver */ \"(ssr)/./node_modules/react-query/es/core/infiniteQueryObserver.js\");\n/* harmony import */ var _mutationCache__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./mutationCache */ \"(ssr)/./node_modules/react-query/es/core/mutationCache.js\");\n/* harmony import */ var _mutationObserver__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./mutationObserver */ \"(ssr)/./node_modules/react-query/es/core/mutationObserver.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./focusManager */ \"(ssr)/./node_modules/react-query/es/core/focusManager.js\");\n/* harmony import */ var _onlineManager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./onlineManager */ \"(ssr)/./node_modules/react-query/es/core/onlineManager.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _hydration__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hydration */ \"(ssr)/./node_modules/react-query/es/core/hydration.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/react-query/es/core/types.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_types__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_14__) if([\"default\",\"CancelledError\",\"QueryCache\",\"QueryClient\",\"QueryObserver\",\"QueriesObserver\",\"InfiniteQueryObserver\",\"MutationCache\",\"MutationObserver\",\"setLogger\",\"notifyManager\",\"focusManager\",\"onlineManager\",\"hashQueryKey\",\"isError\",\"isCancelledError\",\"dehydrate\",\"hydrate\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_14__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n // Types\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMkM7QUFDRDtBQUNFO0FBQ0k7QUFDSTtBQUNZO0FBQ2hCO0FBQ007QUFDakI7QUFDVztBQUNGO0FBQ0U7QUFDQTtBQUNIO0FBQ0ksQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9jb3JlL2luZGV4LmpzPzI3ZDUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgQ2FuY2VsbGVkRXJyb3IgfSBmcm9tICcuL3JldHJ5ZXInO1xuZXhwb3J0IHsgUXVlcnlDYWNoZSB9IGZyb20gJy4vcXVlcnlDYWNoZSc7XG5leHBvcnQgeyBRdWVyeUNsaWVudCB9IGZyb20gJy4vcXVlcnlDbGllbnQnO1xuZXhwb3J0IHsgUXVlcnlPYnNlcnZlciB9IGZyb20gJy4vcXVlcnlPYnNlcnZlcic7XG5leHBvcnQgeyBRdWVyaWVzT2JzZXJ2ZXIgfSBmcm9tICcuL3F1ZXJpZXNPYnNlcnZlcic7XG5leHBvcnQgeyBJbmZpbml0ZVF1ZXJ5T2JzZXJ2ZXIgfSBmcm9tICcuL2luZmluaXRlUXVlcnlPYnNlcnZlcic7XG5leHBvcnQgeyBNdXRhdGlvbkNhY2hlIH0gZnJvbSAnLi9tdXRhdGlvbkNhY2hlJztcbmV4cG9ydCB7IE11dGF0aW9uT2JzZXJ2ZXIgfSBmcm9tICcuL211dGF0aW9uT2JzZXJ2ZXInO1xuZXhwb3J0IHsgc2V0TG9nZ2VyIH0gZnJvbSAnLi9sb2dnZXInO1xuZXhwb3J0IHsgbm90aWZ5TWFuYWdlciB9IGZyb20gJy4vbm90aWZ5TWFuYWdlcic7XG5leHBvcnQgeyBmb2N1c01hbmFnZXIgfSBmcm9tICcuL2ZvY3VzTWFuYWdlcic7XG5leHBvcnQgeyBvbmxpbmVNYW5hZ2VyIH0gZnJvbSAnLi9vbmxpbmVNYW5hZ2VyJztcbmV4cG9ydCB7IGhhc2hRdWVyeUtleSwgaXNFcnJvciB9IGZyb20gJy4vdXRpbHMnO1xuZXhwb3J0IHsgaXNDYW5jZWxsZWRFcnJvciB9IGZyb20gJy4vcmV0cnllcic7XG5leHBvcnQgeyBkZWh5ZHJhdGUsIGh5ZHJhdGUgfSBmcm9tICcuL2h5ZHJhdGlvbic7IC8vIFR5cGVzXG5cbmV4cG9ydCAqIGZyb20gJy4vdHlwZXMnOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/infiniteQueryBehavior.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-query/es/core/infiniteQueryBehavior.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNextPageParam: () => (/* binding */ getNextPageParam),\n/* harmony export */   getPreviousPageParam: () => (/* binding */ getPreviousPageParam),\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\nfunction infiniteQueryBehavior() {\n  return {\n    onFetch: function onFetch(context) {\n      context.fetchFn = function () {\n        var _context$fetchOptions, _context$fetchOptions2, _context$fetchOptions3, _context$fetchOptions4, _context$state$data, _context$state$data2;\n\n        var refetchPage = (_context$fetchOptions = context.fetchOptions) == null ? void 0 : (_context$fetchOptions2 = _context$fetchOptions.meta) == null ? void 0 : _context$fetchOptions2.refetchPage;\n        var fetchMore = (_context$fetchOptions3 = context.fetchOptions) == null ? void 0 : (_context$fetchOptions4 = _context$fetchOptions3.meta) == null ? void 0 : _context$fetchOptions4.fetchMore;\n        var pageParam = fetchMore == null ? void 0 : fetchMore.pageParam;\n        var isFetchingNextPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'forward';\n        var isFetchingPreviousPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'backward';\n        var oldPages = ((_context$state$data = context.state.data) == null ? void 0 : _context$state$data.pages) || [];\n        var oldPageParams = ((_context$state$data2 = context.state.data) == null ? void 0 : _context$state$data2.pageParams) || [];\n        var abortController = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getAbortController)();\n        var abortSignal = abortController == null ? void 0 : abortController.signal;\n        var newPageParams = oldPageParams;\n        var cancelled = false; // Get query function\n\n        var queryFn = context.options.queryFn || function () {\n          return Promise.reject('Missing queryFn');\n        };\n\n        var buildNewPages = function buildNewPages(pages, param, page, previous) {\n          newPageParams = previous ? [param].concat(newPageParams) : [].concat(newPageParams, [param]);\n          return previous ? [page].concat(pages) : [].concat(pages, [page]);\n        }; // Create function to fetch a page\n\n\n        var fetchPage = function fetchPage(pages, manual, param, previous) {\n          if (cancelled) {\n            return Promise.reject('Cancelled');\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages);\n          }\n\n          var queryFnContext = {\n            queryKey: context.queryKey,\n            signal: abortSignal,\n            pageParam: param,\n            meta: context.meta\n          };\n          var queryFnResult = queryFn(queryFnContext);\n          var promise = Promise.resolve(queryFnResult).then(function (page) {\n            return buildNewPages(pages, param, page, previous);\n          });\n\n          if ((0,_retryer__WEBPACK_IMPORTED_MODULE_1__.isCancelable)(queryFnResult)) {\n            var promiseAsAny = promise;\n            promiseAsAny.cancel = queryFnResult.cancel;\n          }\n\n          return promise;\n        };\n\n        var promise; // Fetch first page?\n\n        if (!oldPages.length) {\n          promise = fetchPage([]);\n        } // Fetch next page?\n        else if (isFetchingNextPage) {\n            var manual = typeof pageParam !== 'undefined';\n            var param = manual ? pageParam : getNextPageParam(context.options, oldPages);\n            promise = fetchPage(oldPages, manual, param);\n          } // Fetch previous page?\n          else if (isFetchingPreviousPage) {\n              var _manual = typeof pageParam !== 'undefined';\n\n              var _param = _manual ? pageParam : getPreviousPageParam(context.options, oldPages);\n\n              promise = fetchPage(oldPages, _manual, _param, true);\n            } // Refetch pages\n            else {\n                (function () {\n                  newPageParams = [];\n                  var manual = typeof context.options.getNextPageParam === 'undefined';\n                  var shouldFetchFirstPage = refetchPage && oldPages[0] ? refetchPage(oldPages[0], 0, oldPages) : true; // Fetch first page\n\n                  promise = shouldFetchFirstPage ? fetchPage([], manual, oldPageParams[0]) : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0])); // Fetch remaining pages\n\n                  var _loop = function _loop(i) {\n                    promise = promise.then(function (pages) {\n                      var shouldFetchNextPage = refetchPage && oldPages[i] ? refetchPage(oldPages[i], i, oldPages) : true;\n\n                      if (shouldFetchNextPage) {\n                        var _param2 = manual ? oldPageParams[i] : getNextPageParam(context.options, pages);\n\n                        return fetchPage(pages, manual, _param2);\n                      }\n\n                      return Promise.resolve(buildNewPages(pages, oldPageParams[i], oldPages[i]));\n                    });\n                  };\n\n                  for (var i = 1; i < oldPages.length; i++) {\n                    _loop(i);\n                  }\n                })();\n              }\n\n        var finalPromise = promise.then(function (pages) {\n          return {\n            pages: pages,\n            pageParams: newPageParams\n          };\n        });\n        var finalPromiseAsAny = finalPromise;\n\n        finalPromiseAsAny.cancel = function () {\n          cancelled = true;\n          abortController == null ? void 0 : abortController.abort();\n\n          if ((0,_retryer__WEBPACK_IMPORTED_MODULE_1__.isCancelable)(promise)) {\n            promise.cancel();\n          }\n        };\n\n        return finalPromise;\n      };\n    }\n  };\n}\nfunction getNextPageParam(options, pages) {\n  return options.getNextPageParam == null ? void 0 : options.getNextPageParam(pages[pages.length - 1], pages);\n}\nfunction getPreviousPageParam(options, pages) {\n  return options.getPreviousPageParam == null ? void 0 : options.getPreviousPageParam(pages[0], pages);\n}\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasNextPage(options, pages) {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    var nextPageParam = getNextPageParam(options, pages);\n    return typeof nextPageParam !== 'undefined' && nextPageParam !== null && nextPageParam !== false;\n  }\n}\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasPreviousPage(options, pages) {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    var previousPageParam = getPreviousPageParam(options, pages);\n    return typeof previousPageParam !== 'undefined' && previousPageParam !== null && previousPageParam !== false;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/infiniteQueryBehavior.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/infiniteQueryObserver.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-query/es/core/infiniteQueryObserver.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InfiniteQueryObserver: () => (/* binding */ InfiniteQueryObserver)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _queryObserver__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./queryObserver */ \"(ssr)/./node_modules/react-query/es/core/queryObserver.js\");\n/* harmony import */ var _infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./infiniteQueryBehavior */ \"(ssr)/./node_modules/react-query/es/core/infiniteQueryBehavior.js\");\n\n\n\n\nvar InfiniteQueryObserver = /*#__PURE__*/function (_QueryObserver) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(InfiniteQueryObserver, _QueryObserver);\n\n  // Type override\n  // Type override\n  // Type override\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  function InfiniteQueryObserver(client, options) {\n    return _QueryObserver.call(this, client, options) || this;\n  }\n\n  var _proto = InfiniteQueryObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    _QueryObserver.prototype.bindMethods.call(this);\n\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  };\n\n  _proto.setOptions = function setOptions(options, notifyOptions) {\n    _QueryObserver.prototype.setOptions.call(this, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n      behavior: (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.infiniteQueryBehavior)()\n    }), notifyOptions);\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(options) {\n    options.behavior = (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.infiniteQueryBehavior)();\n    return _QueryObserver.prototype.getOptimisticResult.call(this, options);\n  };\n\n  _proto.fetchNextPage = function fetchNextPage(options) {\n    var _options$cancelRefetc;\n\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: (_options$cancelRefetc = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc : true,\n      throwOnError: options == null ? void 0 : options.throwOnError,\n      meta: {\n        fetchMore: {\n          direction: 'forward',\n          pageParam: options == null ? void 0 : options.pageParam\n        }\n      }\n    });\n  };\n\n  _proto.fetchPreviousPage = function fetchPreviousPage(options) {\n    var _options$cancelRefetc2;\n\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: (_options$cancelRefetc2 = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc2 : true,\n      throwOnError: options == null ? void 0 : options.throwOnError,\n      meta: {\n        fetchMore: {\n          direction: 'backward',\n          pageParam: options == null ? void 0 : options.pageParam\n        }\n      }\n    });\n  };\n\n  _proto.createResult = function createResult(query, options) {\n    var _state$data, _state$data2, _state$fetchMeta, _state$fetchMeta$fetc, _state$fetchMeta2, _state$fetchMeta2$fet;\n\n    var state = query.state;\n\n    var result = _QueryObserver.prototype.createResult.call(this, query, options);\n\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, result, {\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.hasNextPage)(options, (_state$data = state.data) == null ? void 0 : _state$data.pages),\n      hasPreviousPage: (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.hasPreviousPage)(options, (_state$data2 = state.data) == null ? void 0 : _state$data2.pages),\n      isFetchingNextPage: state.isFetching && ((_state$fetchMeta = state.fetchMeta) == null ? void 0 : (_state$fetchMeta$fetc = _state$fetchMeta.fetchMore) == null ? void 0 : _state$fetchMeta$fetc.direction) === 'forward',\n      isFetchingPreviousPage: state.isFetching && ((_state$fetchMeta2 = state.fetchMeta) == null ? void 0 : (_state$fetchMeta2$fet = _state$fetchMeta2.fetchMore) == null ? void 0 : _state$fetchMeta2$fet.direction) === 'backward'\n    });\n  };\n\n  return InfiniteQueryObserver;\n}(_queryObserver__WEBPACK_IMPORTED_MODULE_3__.QueryObserver);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/infiniteQueryObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/logger.js":
/*!****************************************************!*\
  !*** ./node_modules/react-query/es/core/logger.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLogger: () => (/* binding */ getLogger),\n/* harmony export */   setLogger: () => (/* binding */ setLogger)\n/* harmony export */ });\n// TYPES\n// FUNCTIONS\nvar logger = console;\nfunction getLogger() {\n  return logger;\n}\nfunction setLogger(newLogger) {\n  logger = newLogger;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9sb2dnZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9sb2dnZXIuanM/YzAzNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUWVBFU1xuLy8gRlVOQ1RJT05TXG52YXIgbG9nZ2VyID0gY29uc29sZTtcbmV4cG9ydCBmdW5jdGlvbiBnZXRMb2dnZXIoKSB7XG4gIHJldHVybiBsb2dnZXI7XG59XG5leHBvcnQgZnVuY3Rpb24gc2V0TG9nZ2VyKG5ld0xvZ2dlcikge1xuICBsb2dnZXIgPSBuZXdMb2dnZXI7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/logger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/mutation.js":
/*!******************************************************!*\
  !*** ./node_modules/react-query/es/core/mutation.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\n\n\n // TYPES\n\n// CLASS\nvar Mutation = /*#__PURE__*/function () {\n  function Mutation(config) {\n    this.options = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, config.defaultOptions, config.options);\n    this.mutationId = config.mutationId;\n    this.mutationCache = config.mutationCache;\n    this.observers = [];\n    this.state = config.state || getDefaultState();\n    this.meta = config.meta;\n  }\n\n  var _proto = Mutation.prototype;\n\n  _proto.setState = function setState(state) {\n    this.dispatch({\n      type: 'setState',\n      state: state\n    });\n  };\n\n  _proto.addObserver = function addObserver(observer) {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer);\n    }\n  };\n\n  _proto.removeObserver = function removeObserver(observer) {\n    this.observers = this.observers.filter(function (x) {\n      return x !== observer;\n    });\n  };\n\n  _proto.cancel = function cancel() {\n    if (this.retryer) {\n      this.retryer.cancel();\n      return this.retryer.promise.then(_utils__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_1__.noop);\n    }\n\n    return Promise.resolve();\n  };\n\n  _proto.continue = function _continue() {\n    if (this.retryer) {\n      this.retryer.continue();\n      return this.retryer.promise;\n    }\n\n    return this.execute();\n  };\n\n  _proto.execute = function execute() {\n    var _this = this;\n\n    var data;\n    var restored = this.state.status === 'loading';\n    var promise = Promise.resolve();\n\n    if (!restored) {\n      this.dispatch({\n        type: 'loading',\n        variables: this.options.variables\n      });\n      promise = promise.then(function () {\n        // Notify cache callback\n        _this.mutationCache.config.onMutate == null ? void 0 : _this.mutationCache.config.onMutate(_this.state.variables, _this);\n      }).then(function () {\n        return _this.options.onMutate == null ? void 0 : _this.options.onMutate(_this.state.variables);\n      }).then(function (context) {\n        if (context !== _this.state.context) {\n          _this.dispatch({\n            type: 'loading',\n            context: context,\n            variables: _this.state.variables\n          });\n        }\n      });\n    }\n\n    return promise.then(function () {\n      return _this.executeMutation();\n    }).then(function (result) {\n      data = result; // Notify cache callback\n\n      _this.mutationCache.config.onSuccess == null ? void 0 : _this.mutationCache.config.onSuccess(data, _this.state.variables, _this.state.context, _this);\n    }).then(function () {\n      return _this.options.onSuccess == null ? void 0 : _this.options.onSuccess(data, _this.state.variables, _this.state.context);\n    }).then(function () {\n      return _this.options.onSettled == null ? void 0 : _this.options.onSettled(data, null, _this.state.variables, _this.state.context);\n    }).then(function () {\n      _this.dispatch({\n        type: 'success',\n        data: data\n      });\n\n      return data;\n    }).catch(function (error) {\n      // Notify cache callback\n      _this.mutationCache.config.onError == null ? void 0 : _this.mutationCache.config.onError(error, _this.state.variables, _this.state.context, _this); // Log error\n\n      (0,_logger__WEBPACK_IMPORTED_MODULE_2__.getLogger)().error(error);\n      return Promise.resolve().then(function () {\n        return _this.options.onError == null ? void 0 : _this.options.onError(error, _this.state.variables, _this.state.context);\n      }).then(function () {\n        return _this.options.onSettled == null ? void 0 : _this.options.onSettled(undefined, error, _this.state.variables, _this.state.context);\n      }).then(function () {\n        _this.dispatch({\n          type: 'error',\n          error: error\n        });\n\n        throw error;\n      });\n    });\n  };\n\n  _proto.executeMutation = function executeMutation() {\n    var _this2 = this,\n        _this$options$retry;\n\n    this.retryer = new _retryer__WEBPACK_IMPORTED_MODULE_3__.Retryer({\n      fn: function fn() {\n        if (!_this2.options.mutationFn) {\n          return Promise.reject('No mutationFn found');\n        }\n\n        return _this2.options.mutationFn(_this2.state.variables);\n      },\n      onFail: function onFail() {\n        _this2.dispatch({\n          type: 'failed'\n        });\n      },\n      onPause: function onPause() {\n        _this2.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: function onContinue() {\n        _this2.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: (_this$options$retry = this.options.retry) != null ? _this$options$retry : 0,\n      retryDelay: this.options.retryDelay\n    });\n    return this.retryer.promise;\n  };\n\n  _proto.dispatch = function dispatch(action) {\n    var _this3 = this;\n\n    this.state = reducer(this.state, action);\n    _notifyManager__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batch(function () {\n      _this3.observers.forEach(function (observer) {\n        observer.onMutationUpdate(action);\n      });\n\n      _this3.mutationCache.notify(_this3);\n    });\n  };\n\n  return Mutation;\n}();\nfunction getDefaultState() {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined\n  };\n}\n\nfunction reducer(state, action) {\n  switch (action.type) {\n    case 'failed':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        failureCount: state.failureCount + 1\n      });\n\n    case 'pause':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        isPaused: true\n      });\n\n    case 'continue':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        isPaused: false\n      });\n\n    case 'loading':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        context: action.context,\n        data: undefined,\n        error: null,\n        isPaused: false,\n        status: 'loading',\n        variables: action.variables\n      });\n\n    case 'success':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        data: action.data,\n        error: null,\n        status: 'success',\n        isPaused: false\n      });\n\n    case 'error':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        data: undefined,\n        error: action.error,\n        failureCount: state.failureCount + 1,\n        isPaused: false,\n        status: 'error'\n      });\n\n    case 'setState':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, action.state);\n\n    default:\n      return state;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/mutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/mutationCache.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/core/mutationCache.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _mutation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation */ \"(ssr)/./node_modules/react-query/es/core/mutation.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n\n\n\n\n // TYPES\n\n// CLASS\nvar MutationCache = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(MutationCache, _Subscribable);\n\n  function MutationCache(config) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.mutations = [];\n    _this.mutationId = 0;\n    return _this;\n  }\n\n  var _proto = MutationCache.prototype;\n\n  _proto.build = function build(client, options, state) {\n    var mutation = new _mutation__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n      mutationCache: this,\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state: state,\n      defaultOptions: options.mutationKey ? client.getMutationDefaults(options.mutationKey) : undefined,\n      meta: options.meta\n    });\n    this.add(mutation);\n    return mutation;\n  };\n\n  _proto.add = function add(mutation) {\n    this.mutations.push(mutation);\n    this.notify(mutation);\n  };\n\n  _proto.remove = function remove(mutation) {\n    this.mutations = this.mutations.filter(function (x) {\n      return x !== mutation;\n    });\n    mutation.cancel();\n    this.notify(mutation);\n  };\n\n  _proto.clear = function clear() {\n    var _this2 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      _this2.mutations.forEach(function (mutation) {\n        _this2.remove(mutation);\n      });\n    });\n  };\n\n  _proto.getAll = function getAll() {\n    return this.mutations;\n  };\n\n  _proto.find = function find(filters) {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.mutations.find(function (mutation) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation);\n    });\n  };\n\n  _proto.findAll = function findAll(filters) {\n    return this.mutations.filter(function (mutation) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation);\n    });\n  };\n\n  _proto.notify = function notify(mutation) {\n    var _this3 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(mutation);\n      });\n    });\n  };\n\n  _proto.onFocus = function onFocus() {\n    this.resumePausedMutations();\n  };\n\n  _proto.onOnline = function onOnline() {\n    this.resumePausedMutations();\n  };\n\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    var pausedMutations = this.mutations.filter(function (x) {\n      return x.state.isPaused;\n    });\n    return _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      return pausedMutations.reduce(function (promise, mutation) {\n        return promise.then(function () {\n          return mutation.continue().catch(_utils__WEBPACK_IMPORTED_MODULE_3__.noop);\n        });\n      }, Promise.resolve());\n    });\n  };\n\n  return MutationCache;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/mutationCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/mutationObserver.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-query/es/core/mutationObserver.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationObserver: () => (/* binding */ MutationObserver)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mutation */ \"(ssr)/./node_modules/react-query/es/core/mutation.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n\n\n\n\n\n// CLASS\nvar MutationObserver = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(MutationObserver, _Subscribable);\n\n  function MutationObserver(client, options) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n\n    _this.setOptions(options);\n\n    _this.bindMethods();\n\n    _this.updateResult();\n\n    return _this;\n  }\n\n  var _proto = MutationObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  };\n\n  _proto.setOptions = function setOptions(options) {\n    this.options = this.client.defaultMutationOptions(options);\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      var _this$currentMutation;\n\n      (_this$currentMutation = this.currentMutation) == null ? void 0 : _this$currentMutation.removeObserver(this);\n    }\n  };\n\n  _proto.onMutationUpdate = function onMutationUpdate(action) {\n    this.updateResult(); // Determine which callbacks to trigger\n\n    var notifyOptions = {\n      listeners: true\n    };\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true;\n    }\n\n    this.notify(notifyOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n\n  _proto.reset = function reset() {\n    this.currentMutation = undefined;\n    this.updateResult();\n    this.notify({\n      listeners: true\n    });\n  };\n\n  _proto.mutate = function mutate(variables, options) {\n    this.mutateOptions = options;\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this);\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.options, {\n      variables: typeof variables !== 'undefined' ? variables : this.options.variables\n    }));\n    this.currentMutation.addObserver(this);\n    return this.currentMutation.execute();\n  };\n\n  _proto.updateResult = function updateResult() {\n    var state = this.currentMutation ? this.currentMutation.state : (0,_mutation__WEBPACK_IMPORTED_MODULE_2__.getDefaultState)();\n\n    var result = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n      isLoading: state.status === 'loading',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset\n    });\n\n    this.currentResult = result;\n  };\n\n  _proto.notify = function notify(options) {\n    var _this2 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      // First trigger the mutate callbacks\n      if (_this2.mutateOptions) {\n        if (options.onSuccess) {\n          _this2.mutateOptions.onSuccess == null ? void 0 : _this2.mutateOptions.onSuccess(_this2.currentResult.data, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(_this2.currentResult.data, null, _this2.currentResult.variables, _this2.currentResult.context);\n        } else if (options.onError) {\n          _this2.mutateOptions.onError == null ? void 0 : _this2.mutateOptions.onError(_this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(undefined, _this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n        }\n      } // Then trigger the listeners\n\n\n      if (options.listeners) {\n        _this2.listeners.forEach(function (listener) {\n          listener(_this2.currentResult);\n        });\n      }\n    });\n  };\n\n  return MutationObserver;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/mutationObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/notifyManager.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/core/notifyManager.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotifyManager: () => (/* binding */ NotifyManager),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n // TYPES\n\n// CLASS\nvar NotifyManager = /*#__PURE__*/function () {\n  function NotifyManager() {\n    this.queue = [];\n    this.transactions = 0;\n\n    this.notifyFn = function (callback) {\n      callback();\n    };\n\n    this.batchNotifyFn = function (callback) {\n      callback();\n    };\n  }\n\n  var _proto = NotifyManager.prototype;\n\n  _proto.batch = function batch(callback) {\n    var result;\n    this.transactions++;\n\n    try {\n      result = callback();\n    } finally {\n      this.transactions--;\n\n      if (!this.transactions) {\n        this.flush();\n      }\n    }\n\n    return result;\n  };\n\n  _proto.schedule = function schedule(callback) {\n    var _this = this;\n\n    if (this.transactions) {\n      this.queue.push(callback);\n    } else {\n      (0,_utils__WEBPACK_IMPORTED_MODULE_0__.scheduleMicrotask)(function () {\n        _this.notifyFn(callback);\n      });\n    }\n  }\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  ;\n\n  _proto.batchCalls = function batchCalls(callback) {\n    var _this2 = this;\n\n    return function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      _this2.schedule(function () {\n        callback.apply(void 0, args);\n      });\n    };\n  };\n\n  _proto.flush = function flush() {\n    var _this3 = this;\n\n    var queue = this.queue;\n    this.queue = [];\n\n    if (queue.length) {\n      (0,_utils__WEBPACK_IMPORTED_MODULE_0__.scheduleMicrotask)(function () {\n        _this3.batchNotifyFn(function () {\n          queue.forEach(function (callback) {\n            _this3.notifyFn(callback);\n          });\n        });\n      });\n    }\n  }\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  ;\n\n  _proto.setNotifyFunction = function setNotifyFunction(fn) {\n    this.notifyFn = fn;\n  }\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  ;\n\n  _proto.setBatchNotifyFunction = function setBatchNotifyFunction(fn) {\n    this.batchNotifyFn = fn;\n  };\n\n  return NotifyManager;\n}(); // SINGLETON\n\nvar notifyManager = new NotifyManager();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/notifyManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/onlineManager.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/core/onlineManager.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\n\nvar OnlineManager = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(OnlineManager, _Subscribable);\n\n  function OnlineManager() {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n\n    _this.setup = function (onOnline) {\n      var _window;\n\n      if (!_utils__WEBPACK_IMPORTED_MODULE_1__.isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onOnline();\n        }; // Listen to online\n\n\n        window.addEventListener('online', listener, false);\n        window.addEventListener('offline', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('online', listener);\n          window.removeEventListener('offline', listener);\n        };\n      }\n    };\n\n    return _this;\n  }\n\n  var _proto = OnlineManager.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n        _this2 = this;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (online) {\n      if (typeof online === 'boolean') {\n        _this2.setOnline(online);\n      } else {\n        _this2.onOnline();\n      }\n    });\n  };\n\n  _proto.setOnline = function setOnline(online) {\n    this.online = online;\n\n    if (online) {\n      this.onOnline();\n    }\n  };\n\n  _proto.onOnline = function onOnline() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n\n  _proto.isOnline = function isOnline() {\n    if (typeof this.online === 'boolean') {\n      return this.online;\n    }\n\n    if (typeof navigator === 'undefined' || typeof navigator.onLine === 'undefined') {\n      return true;\n    }\n\n    return navigator.onLine;\n  };\n\n  return OnlineManager;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_2__.Subscribable);\nvar onlineManager = new OnlineManager();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/onlineManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/queriesObserver.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-query/es/core/queriesObserver.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueriesObserver: () => (/* binding */ QueriesObserver)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _queryObserver__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queryObserver */ \"(ssr)/./node_modules/react-query/es/core/queryObserver.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n\n\n\n\n\nvar QueriesObserver = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(QueriesObserver, _Subscribable);\n\n  function QueriesObserver(client, queries) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.queries = [];\n    _this.result = [];\n    _this.observers = [];\n    _this.observersMap = {};\n\n    if (queries) {\n      _this.setQueries(queries);\n    }\n\n    return _this;\n  }\n\n  var _proto = QueriesObserver.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    var _this2 = this;\n\n    if (this.listeners.length === 1) {\n      this.observers.forEach(function (observer) {\n        observer.subscribe(function (result) {\n          _this2.onUpdate(observer, result);\n        });\n      });\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      this.destroy();\n    }\n  };\n\n  _proto.destroy = function destroy() {\n    this.listeners = [];\n    this.observers.forEach(function (observer) {\n      observer.destroy();\n    });\n  };\n\n  _proto.setQueries = function setQueries(queries, notifyOptions) {\n    this.queries = queries;\n    this.updateObservers(notifyOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.result;\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(queries) {\n    return this.findMatchingObservers(queries).map(function (match) {\n      return match.observer.getOptimisticResult(match.defaultedQueryOptions);\n    });\n  };\n\n  _proto.findMatchingObservers = function findMatchingObservers(queries) {\n    var _this3 = this;\n\n    var prevObservers = this.observers;\n    var defaultedQueryOptions = queries.map(function (options) {\n      return _this3.client.defaultQueryObserverOptions(options);\n    });\n    var matchingObservers = defaultedQueryOptions.flatMap(function (defaultedOptions) {\n      var match = prevObservers.find(function (observer) {\n        return observer.options.queryHash === defaultedOptions.queryHash;\n      });\n\n      if (match != null) {\n        return [{\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        }];\n      }\n\n      return [];\n    });\n    var matchedQueryHashes = matchingObservers.map(function (match) {\n      return match.defaultedQueryOptions.queryHash;\n    });\n    var unmatchedQueries = defaultedQueryOptions.filter(function (defaultedOptions) {\n      return !matchedQueryHashes.includes(defaultedOptions.queryHash);\n    });\n    var unmatchedObservers = prevObservers.filter(function (prevObserver) {\n      return !matchingObservers.some(function (match) {\n        return match.observer === prevObserver;\n      });\n    });\n    var newOrReusedObservers = unmatchedQueries.map(function (options, index) {\n      if (options.keepPreviousData) {\n        // return previous data from one of the observers that no longer match\n        var previouslyUsedObserver = unmatchedObservers[index];\n\n        if (previouslyUsedObserver !== undefined) {\n          return {\n            defaultedQueryOptions: options,\n            observer: previouslyUsedObserver\n          };\n        }\n      }\n\n      return {\n        defaultedQueryOptions: options,\n        observer: _this3.getObserver(options)\n      };\n    });\n\n    var sortMatchesByOrderOfQueries = function sortMatchesByOrderOfQueries(a, b) {\n      return defaultedQueryOptions.indexOf(a.defaultedQueryOptions) - defaultedQueryOptions.indexOf(b.defaultedQueryOptions);\n    };\n\n    return matchingObservers.concat(newOrReusedObservers).sort(sortMatchesByOrderOfQueries);\n  };\n\n  _proto.getObserver = function getObserver(options) {\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var currentObserver = this.observersMap[defaultedOptions.queryHash];\n    return currentObserver != null ? currentObserver : new _queryObserver__WEBPACK_IMPORTED_MODULE_1__.QueryObserver(this.client, defaultedOptions);\n  };\n\n  _proto.updateObservers = function updateObservers(notifyOptions) {\n    var _this4 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      var prevObservers = _this4.observers;\n\n      var newObserverMatches = _this4.findMatchingObservers(_this4.queries); // set options for the new observers to notify of changes\n\n\n      newObserverMatches.forEach(function (match) {\n        return match.observer.setOptions(match.defaultedQueryOptions, notifyOptions);\n      });\n      var newObservers = newObserverMatches.map(function (match) {\n        return match.observer;\n      });\n      var newObserversMap = Object.fromEntries(newObservers.map(function (observer) {\n        return [observer.options.queryHash, observer];\n      }));\n      var newResult = newObservers.map(function (observer) {\n        return observer.getCurrentResult();\n      });\n      var hasIndexChange = newObservers.some(function (observer, index) {\n        return observer !== prevObservers[index];\n      });\n\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return;\n      }\n\n      _this4.observers = newObservers;\n      _this4.observersMap = newObserversMap;\n      _this4.result = newResult;\n\n      if (!_this4.hasListeners()) {\n        return;\n      }\n\n      (0,_utils__WEBPACK_IMPORTED_MODULE_3__.difference)(prevObservers, newObservers).forEach(function (observer) {\n        observer.destroy();\n      });\n      (0,_utils__WEBPACK_IMPORTED_MODULE_3__.difference)(newObservers, prevObservers).forEach(function (observer) {\n        observer.subscribe(function (result) {\n          _this4.onUpdate(observer, result);\n        });\n      });\n\n      _this4.notify();\n    });\n  };\n\n  _proto.onUpdate = function onUpdate(observer, result) {\n    var index = this.observers.indexOf(observer);\n\n    if (index !== -1) {\n      this.result = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.replaceAt)(this.result, index, result);\n      this.notify();\n    }\n  };\n\n  _proto.notify = function notify() {\n    var _this5 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      _this5.listeners.forEach(function (listener) {\n        listener(_this5.result);\n      });\n    });\n  };\n\n  return QueriesObserver;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/queriesObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/query.js":
/*!***************************************************!*\
  !*** ./node_modules/react-query/es/core/query.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n\n\n\n\n // TYPES\n\n// CLASS\nvar Query = /*#__PURE__*/function () {\n  function Query(config) {\n    this.abortSignalConsumed = false;\n    this.hadObservers = false;\n    this.defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.cache = config.cache;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.initialState = config.state || this.getDefaultState(this.options);\n    this.state = this.initialState;\n    this.meta = config.meta;\n    this.scheduleGc();\n  }\n\n  var _proto = Query.prototype;\n\n  _proto.setOptions = function setOptions(options) {\n    var _this$options$cacheTi;\n\n    this.options = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.defaultOptions, options);\n    this.meta = options == null ? void 0 : options.meta; // Default to 5 minutes if not cache time is set\n\n    this.cacheTime = Math.max(this.cacheTime || 0, (_this$options$cacheTi = this.options.cacheTime) != null ? _this$options$cacheTi : 5 * 60 * 1000);\n  };\n\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n\n  _proto.scheduleGc = function scheduleGc() {\n    var _this = this;\n\n    this.clearGcTimeout();\n\n    if ((0,_utils__WEBPACK_IMPORTED_MODULE_1__.isValidTimeout)(this.cacheTime)) {\n      this.gcTimeout = setTimeout(function () {\n        _this.optionalRemove();\n      }, this.cacheTime);\n    }\n  };\n\n  _proto.clearGcTimeout = function clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout);\n      this.gcTimeout = undefined;\n    }\n  };\n\n  _proto.optionalRemove = function optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.isFetching) {\n        if (this.hadObservers) {\n          this.scheduleGc();\n        }\n      } else {\n        this.cache.remove(this);\n      }\n    }\n  };\n\n  _proto.setData = function setData(updater, options) {\n    var _this$options$isDataE, _this$options;\n\n    var prevData = this.state.data; // Get the new data\n\n    var data = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.functionalUpdate)(updater, prevData); // Use prev data if an isDataEqual function is defined and returns `true`\n\n    if ((_this$options$isDataE = (_this$options = this.options).isDataEqual) == null ? void 0 : _this$options$isDataE.call(_this$options, prevData, data)) {\n      data = prevData;\n    } else if (this.options.structuralSharing !== false) {\n      // Structurally share data between prev and new data if needed\n      data = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.replaceEqualDeep)(prevData, data);\n    } // Set data and mark it as cached\n\n\n    this.dispatch({\n      data: data,\n      type: 'success',\n      dataUpdatedAt: options == null ? void 0 : options.updatedAt\n    });\n    return data;\n  };\n\n  _proto.setState = function setState(state, setStateOptions) {\n    this.dispatch({\n      type: 'setState',\n      state: state,\n      setStateOptions: setStateOptions\n    });\n  };\n\n  _proto.cancel = function cancel(options) {\n    var _this$retryer;\n\n    var promise = this.promise;\n    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);\n    return promise ? promise.then(_utils__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();\n  };\n\n  _proto.destroy = function destroy() {\n    this.clearGcTimeout();\n    this.cancel({\n      silent: true\n    });\n  };\n\n  _proto.reset = function reset() {\n    this.destroy();\n    this.setState(this.initialState);\n  };\n\n  _proto.isActive = function isActive() {\n    return this.observers.some(function (observer) {\n      return observer.options.enabled !== false;\n    });\n  };\n\n  _proto.isFetching = function isFetching() {\n    return this.state.isFetching;\n  };\n\n  _proto.isStale = function isStale() {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(function (observer) {\n      return observer.getCurrentResult().isStale;\n    });\n  };\n\n  _proto.isStaleByTime = function isStaleByTime(staleTime) {\n    if (staleTime === void 0) {\n      staleTime = 0;\n    }\n\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || !(0,_utils__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n  };\n\n  _proto.onFocus = function onFocus() {\n    var _this$retryer2;\n\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnWindowFocus();\n    });\n\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();\n  };\n\n  _proto.onOnline = function onOnline() {\n    var _this$retryer3;\n\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnReconnect();\n    });\n\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();\n  };\n\n  _proto.addObserver = function addObserver(observer) {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer);\n      this.hadObservers = true; // Stop the query from being garbage collected\n\n      this.clearGcTimeout();\n      this.cache.notify({\n        type: 'observerAdded',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n\n  _proto.removeObserver = function removeObserver(observer) {\n    if (this.observers.indexOf(observer) !== -1) {\n      this.observers = this.observers.filter(function (x) {\n        return x !== observer;\n      });\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.retryer.isTransportCancelable || this.abortSignalConsumed) {\n            this.retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.retryer.cancelRetry();\n          }\n        }\n\n        if (this.cacheTime) {\n          this.scheduleGc();\n        } else {\n          this.cache.remove(this);\n        }\n      }\n\n      this.cache.notify({\n        type: 'observerRemoved',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n\n  _proto.getObserversCount = function getObserversCount() {\n    return this.observers.length;\n  };\n\n  _proto.invalidate = function invalidate() {\n    if (!this.state.isInvalidated) {\n      this.dispatch({\n        type: 'invalidate'\n      });\n    }\n  };\n\n  _proto.fetch = function fetch(options, fetchOptions) {\n    var _this2 = this,\n        _this$options$behavio,\n        _context$fetchOptions,\n        _abortController$abor;\n\n    if (this.state.isFetching) {\n      if (this.state.dataUpdatedAt && (fetchOptions == null ? void 0 : fetchOptions.cancelRefetch)) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({\n          silent: true\n        });\n      } else if (this.promise) {\n        var _this$retryer4;\n\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching\n\n        return this.promise;\n      }\n    } // Update config if passed, otherwise the config from the last execution is used\n\n\n    if (options) {\n      this.setOptions(options);\n    } // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n\n\n    if (!this.options.queryFn) {\n      var observer = this.observers.find(function (x) {\n        return x.options.queryFn;\n      });\n\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n\n    var queryKey = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.ensureQueryKeyArray)(this.queryKey);\n    var abortController = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAbortController)(); // Create query function context\n\n    var queryFnContext = {\n      queryKey: queryKey,\n      pageParam: undefined,\n      meta: this.meta\n    };\n    Object.defineProperty(queryFnContext, 'signal', {\n      enumerable: true,\n      get: function get() {\n        if (abortController) {\n          _this2.abortSignalConsumed = true;\n          return abortController.signal;\n        }\n\n        return undefined;\n      }\n    }); // Create fetch function\n\n    var fetchFn = function fetchFn() {\n      if (!_this2.options.queryFn) {\n        return Promise.reject('Missing queryFn');\n      }\n\n      _this2.abortSignalConsumed = false;\n      return _this2.options.queryFn(queryFnContext);\n    }; // Trigger behavior hook\n\n\n    var context = {\n      fetchOptions: fetchOptions,\n      options: this.options,\n      queryKey: queryKey,\n      state: this.state,\n      fetchFn: fetchFn,\n      meta: this.meta\n    };\n\n    if ((_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch) {\n      var _this$options$behavio2;\n\n      (_this$options$behavio2 = this.options.behavior) == null ? void 0 : _this$options$behavio2.onFetch(context);\n    } // Store state in case the current fetch needs to be reverted\n\n\n    this.revertState = this.state; // Set to fetching state if not already in it\n\n    if (!this.state.isFetching || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {\n      var _context$fetchOptions2;\n\n      this.dispatch({\n        type: 'fetch',\n        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta\n      });\n    } // Try to fetch the data\n\n\n    this.retryer = new _retryer__WEBPACK_IMPORTED_MODULE_2__.Retryer({\n      fn: context.fetchFn,\n      abort: abortController == null ? void 0 : (_abortController$abor = abortController.abort) == null ? void 0 : _abortController$abor.bind(abortController),\n      onSuccess: function onSuccess(data) {\n        _this2.setData(data); // Notify cache callback\n\n\n        _this2.cache.config.onSuccess == null ? void 0 : _this2.cache.config.onSuccess(data, _this2); // Remove query after fetching if cache time is 0\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onError: function onError(error) {\n        // Optimistically update state if needed\n        if (!((0,_retryer__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.silent)) {\n          _this2.dispatch({\n            type: 'error',\n            error: error\n          });\n        }\n\n        if (!(0,_retryer__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error)) {\n          // Notify cache callback\n          _this2.cache.config.onError == null ? void 0 : _this2.cache.config.onError(error, _this2); // Log error\n\n          (0,_logger__WEBPACK_IMPORTED_MODULE_3__.getLogger)().error(error);\n        } // Remove query after fetching if cache time is 0\n\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onFail: function onFail() {\n        _this2.dispatch({\n          type: 'failed'\n        });\n      },\n      onPause: function onPause() {\n        _this2.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: function onContinue() {\n        _this2.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay\n    });\n    this.promise = this.retryer.promise;\n    return this.promise;\n  };\n\n  _proto.dispatch = function dispatch(action) {\n    var _this3 = this;\n\n    this.state = this.reducer(this.state, action);\n    _notifyManager__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batch(function () {\n      _this3.observers.forEach(function (observer) {\n        observer.onQueryUpdate(action);\n      });\n\n      _this3.cache.notify({\n        query: _this3,\n        type: 'queryUpdated',\n        action: action\n      });\n    });\n  };\n\n  _proto.getDefaultState = function getDefaultState(options) {\n    var data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;\n    var hasInitialData = typeof options.initialData !== 'undefined';\n    var initialDataUpdatedAt = hasInitialData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n    var hasData = typeof data !== 'undefined';\n    return {\n      data: data,\n      dataUpdateCount: 0,\n      dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,\n      error: null,\n      errorUpdateCount: 0,\n      errorUpdatedAt: 0,\n      fetchFailureCount: 0,\n      fetchMeta: null,\n      isFetching: false,\n      isInvalidated: false,\n      isPaused: false,\n      status: hasData ? 'success' : 'idle'\n    };\n  };\n\n  _proto.reducer = function reducer(state, action) {\n    var _action$meta, _action$dataUpdatedAt;\n\n    switch (action.type) {\n      case 'failed':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          fetchFailureCount: state.fetchFailureCount + 1\n        });\n\n      case 'pause':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          isPaused: true\n        });\n\n      case 'continue':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          isPaused: false\n        });\n\n      case 'fetch':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          fetchFailureCount: 0,\n          fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,\n          isFetching: true,\n          isPaused: false\n        }, !state.dataUpdatedAt && {\n          error: null,\n          status: 'loading'\n        });\n\n      case 'success':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          data: action.data,\n          dataUpdateCount: state.dataUpdateCount + 1,\n          dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),\n          error: null,\n          fetchFailureCount: 0,\n          isFetching: false,\n          isInvalidated: false,\n          isPaused: false,\n          status: 'success'\n        });\n\n      case 'error':\n        var error = action.error;\n\n        if ((0,_retryer__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.revert && this.revertState) {\n          return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.revertState);\n        }\n\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          error: error,\n          errorUpdateCount: state.errorUpdateCount + 1,\n          errorUpdatedAt: Date.now(),\n          fetchFailureCount: state.fetchFailureCount + 1,\n          isFetching: false,\n          isPaused: false,\n          status: 'error'\n        });\n\n      case 'invalidate':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          isInvalidated: true\n        });\n\n      case 'setState':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, action.state);\n\n      default:\n        return state;\n    }\n  };\n\n  return Query;\n}();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/queryCache.js":
/*!********************************************************!*\
  !*** ./node_modules/react-query/es/core/queryCache.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query */ \"(ssr)/./node_modules/react-query/es/core/query.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n\n\n\n\n\n// CLASS\nvar QueryCache = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(QueryCache, _Subscribable);\n\n  function QueryCache(config) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.queries = [];\n    _this.queriesMap = {};\n    return _this;\n  }\n\n  var _proto = QueryCache.prototype;\n\n  _proto.build = function build(client, options, state) {\n    var _options$queryHash;\n\n    var queryKey = options.queryKey;\n    var queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : (0,_utils__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n    var query = this.get(queryHash);\n\n    if (!query) {\n      query = new _query__WEBPACK_IMPORTED_MODULE_2__.Query({\n        cache: this,\n        queryKey: queryKey,\n        queryHash: queryHash,\n        options: client.defaultQueryOptions(options),\n        state: state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n        meta: options.meta\n      });\n      this.add(query);\n    }\n\n    return query;\n  };\n\n  _proto.add = function add(query) {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query;\n      this.queries.push(query);\n      this.notify({\n        type: 'queryAdded',\n        query: query\n      });\n    }\n  };\n\n  _proto.remove = function remove(query) {\n    var queryInMap = this.queriesMap[query.queryHash];\n\n    if (queryInMap) {\n      query.destroy();\n      this.queries = this.queries.filter(function (x) {\n        return x !== query;\n      });\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash];\n      }\n\n      this.notify({\n        type: 'queryRemoved',\n        query: query\n      });\n    }\n  };\n\n  _proto.clear = function clear() {\n    var _this2 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      _this2.queries.forEach(function (query) {\n        _this2.remove(query);\n      });\n    });\n  };\n\n  _proto.get = function get(queryHash) {\n    return this.queriesMap[queryHash];\n  };\n\n  _proto.getAll = function getAll() {\n    return this.queries;\n  };\n\n  _proto.find = function find(arg1, arg2) {\n    var _parseFilterArgs = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.parseFilterArgs)(arg1, arg2),\n        filters = _parseFilterArgs[0];\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.queries.find(function (query) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query);\n    });\n  };\n\n  _proto.findAll = function findAll(arg1, arg2) {\n    var _parseFilterArgs2 = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.parseFilterArgs)(arg1, arg2),\n        filters = _parseFilterArgs2[0];\n\n    return Object.keys(filters).length > 0 ? this.queries.filter(function (query) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query);\n    }) : this.queries;\n  };\n\n  _proto.notify = function notify(event) {\n    var _this3 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(event);\n      });\n    });\n  };\n\n  _proto.onFocus = function onFocus() {\n    var _this4 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      _this4.queries.forEach(function (query) {\n        query.onFocus();\n      });\n    });\n  };\n\n  _proto.onOnline = function onOnline() {\n    var _this5 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      _this5.queries.forEach(function (query) {\n        query.onOnline();\n      });\n    });\n  };\n\n  return QueryCache;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/queryCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/queryClient.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-query/es/core/queryClient.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _queryCache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queryCache */ \"(ssr)/./node_modules/react-query/es/core/queryCache.js\");\n/* harmony import */ var _mutationCache__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mutationCache */ \"(ssr)/./node_modules/react-query/es/core/mutationCache.js\");\n/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./focusManager */ \"(ssr)/./node_modules/react-query/es/core/focusManager.js\");\n/* harmony import */ var _onlineManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./onlineManager */ \"(ssr)/./node_modules/react-query/es/core/onlineManager.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infiniteQueryBehavior */ \"(ssr)/./node_modules/react-query/es/core/infiniteQueryBehavior.js\");\n\n\n\n\n\n\n\n\n// CLASS\nvar QueryClient = /*#__PURE__*/function () {\n  function QueryClient(config) {\n    if (config === void 0) {\n      config = {};\n    }\n\n    this.queryCache = config.queryCache || new _queryCache__WEBPACK_IMPORTED_MODULE_1__.QueryCache();\n    this.mutationCache = config.mutationCache || new _mutationCache__WEBPACK_IMPORTED_MODULE_2__.MutationCache();\n    this.defaultOptions = config.defaultOptions || {};\n    this.queryDefaults = [];\n    this.mutationDefaults = [];\n  }\n\n  var _proto = QueryClient.prototype;\n\n  _proto.mount = function mount() {\n    var _this = this;\n\n    this.unsubscribeFocus = _focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.subscribe(function () {\n      if (_focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused() && _onlineManager__WEBPACK_IMPORTED_MODULE_4__.onlineManager.isOnline()) {\n        _this.mutationCache.onFocus();\n\n        _this.queryCache.onFocus();\n      }\n    });\n    this.unsubscribeOnline = _onlineManager__WEBPACK_IMPORTED_MODULE_4__.onlineManager.subscribe(function () {\n      if (_focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused() && _onlineManager__WEBPACK_IMPORTED_MODULE_4__.onlineManager.isOnline()) {\n        _this.mutationCache.onOnline();\n\n        _this.queryCache.onOnline();\n      }\n    });\n  };\n\n  _proto.unmount = function unmount() {\n    var _this$unsubscribeFocu, _this$unsubscribeOnli;\n\n    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);\n    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);\n  };\n\n  _proto.isFetching = function isFetching(arg1, arg2) {\n    var _parseFilterArgs = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2),\n        filters = _parseFilterArgs[0];\n\n    filters.fetching = true;\n    return this.queryCache.findAll(filters).length;\n  };\n\n  _proto.isMutating = function isMutating(filters) {\n    return this.mutationCache.findAll((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, filters, {\n      fetching: true\n    })).length;\n  };\n\n  _proto.getQueryData = function getQueryData(queryKey, filters) {\n    var _this$queryCache$find;\n\n    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;\n  };\n\n  _proto.getQueriesData = function getQueriesData(queryKeyOrFilters) {\n    return this.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref) {\n      var queryKey = _ref.queryKey,\n          state = _ref.state;\n      var data = state.data;\n      return [queryKey, data];\n    });\n  };\n\n  _proto.setQueryData = function setQueryData(queryKey, updater, options) {\n    var parsedOptions = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(queryKey);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions);\n    return this.queryCache.build(this, defaultedOptions).setData(updater, options);\n  };\n\n  _proto.setQueriesData = function setQueriesData(queryKeyOrFilters, updater, options) {\n    var _this2 = this;\n\n    return _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      return _this2.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref2) {\n        var queryKey = _ref2.queryKey;\n        return [queryKey, _this2.setQueryData(queryKey, updater, options)];\n      });\n    });\n  };\n\n  _proto.getQueryState = function getQueryState(queryKey, filters) {\n    var _this$queryCache$find2;\n\n    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;\n  };\n\n  _proto.removeQueries = function removeQueries(arg1, arg2) {\n    var _parseFilterArgs2 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2),\n        filters = _parseFilterArgs2[0];\n\n    var queryCache = this.queryCache;\n    _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        queryCache.remove(query);\n      });\n    });\n  };\n\n  _proto.resetQueries = function resetQueries(arg1, arg2, arg3) {\n    var _this3 = this;\n\n    var _parseFilterArgs3 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),\n        filters = _parseFilterArgs3[0],\n        options = _parseFilterArgs3[1];\n\n    var queryCache = this.queryCache;\n\n    var refetchFilters = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, filters, {\n      active: true\n    });\n\n    return _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        query.reset();\n      });\n      return _this3.refetchQueries(refetchFilters, options);\n    });\n  };\n\n  _proto.cancelQueries = function cancelQueries(arg1, arg2, arg3) {\n    var _this4 = this;\n\n    var _parseFilterArgs4 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),\n        filters = _parseFilterArgs4[0],\n        _parseFilterArgs4$ = _parseFilterArgs4[1],\n        cancelOptions = _parseFilterArgs4$ === void 0 ? {} : _parseFilterArgs4$;\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true;\n    }\n\n    var promises = _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      return _this4.queryCache.findAll(filters).map(function (query) {\n        return query.cancel(cancelOptions);\n      });\n    });\n    return Promise.all(promises).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n  };\n\n  _proto.invalidateQueries = function invalidateQueries(arg1, arg2, arg3) {\n    var _ref3,\n        _filters$refetchActiv,\n        _filters$refetchInact,\n        _this5 = this;\n\n    var _parseFilterArgs5 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),\n        filters = _parseFilterArgs5[0],\n        options = _parseFilterArgs5[1];\n\n    var refetchFilters = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, filters, {\n      // if filters.refetchActive is not provided and filters.active is explicitly false,\n      // e.g. invalidateQueries({ active: false }), we don't want to refetch active queries\n      active: (_ref3 = (_filters$refetchActiv = filters.refetchActive) != null ? _filters$refetchActiv : filters.active) != null ? _ref3 : true,\n      inactive: (_filters$refetchInact = filters.refetchInactive) != null ? _filters$refetchInact : false\n    });\n\n    return _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      _this5.queryCache.findAll(filters).forEach(function (query) {\n        query.invalidate();\n      });\n\n      return _this5.refetchQueries(refetchFilters, options);\n    });\n  };\n\n  _proto.refetchQueries = function refetchQueries(arg1, arg2, arg3) {\n    var _this6 = this;\n\n    var _parseFilterArgs6 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),\n        filters = _parseFilterArgs6[0],\n        options = _parseFilterArgs6[1];\n\n    var promises = _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      return _this6.queryCache.findAll(filters).map(function (query) {\n        return query.fetch(undefined, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n          meta: {\n            refetchPage: filters == null ? void 0 : filters.refetchPage\n          }\n        }));\n      });\n    });\n    var promise = Promise.all(promises).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n\n    if (!(options == null ? void 0 : options.throwOnError)) {\n      promise = promise.catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n    }\n\n    return promise;\n  };\n\n  _proto.fetchQuery = function fetchQuery(arg1, arg2, arg3) {\n    var parsedOptions = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions); // https://github.com/tannerlinsley/react-query/issues/652\n\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false;\n    }\n\n    var query = this.queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  };\n\n  _proto.prefetchQuery = function prefetchQuery(arg1, arg2, arg3) {\n    return this.fetchQuery(arg1, arg2, arg3).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n  };\n\n  _proto.fetchInfiniteQuery = function fetchInfiniteQuery(arg1, arg2, arg3) {\n    var parsedOptions = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);\n    parsedOptions.behavior = (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_7__.infiniteQueryBehavior)();\n    return this.fetchQuery(parsedOptions);\n  };\n\n  _proto.prefetchInfiniteQuery = function prefetchInfiniteQuery(arg1, arg2, arg3) {\n    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n  };\n\n  _proto.cancelMutations = function cancelMutations() {\n    var _this7 = this;\n\n    var promises = _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      return _this7.mutationCache.getAll().map(function (mutation) {\n        return mutation.cancel();\n      });\n    });\n    return Promise.all(promises).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n  };\n\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    return this.getMutationCache().resumePausedMutations();\n  };\n\n  _proto.executeMutation = function executeMutation(options) {\n    return this.mutationCache.build(this, options).execute();\n  };\n\n  _proto.getQueryCache = function getQueryCache() {\n    return this.queryCache;\n  };\n\n  _proto.getMutationCache = function getMutationCache() {\n    return this.mutationCache;\n  };\n\n  _proto.getDefaultOptions = function getDefaultOptions() {\n    return this.defaultOptions;\n  };\n\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n\n  _proto.setQueryDefaults = function setQueryDefaults(queryKey, options) {\n    var result = this.queryDefaults.find(function (x) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(queryKey) === (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(x.queryKey);\n    });\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.queryDefaults.push({\n        queryKey: queryKey,\n        defaultOptions: options\n      });\n    }\n  };\n\n  _proto.getQueryDefaults = function getQueryDefaults(queryKey) {\n    var _this$queryDefaults$f;\n\n    return queryKey ? (_this$queryDefaults$f = this.queryDefaults.find(function (x) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(queryKey, x.queryKey);\n    })) == null ? void 0 : _this$queryDefaults$f.defaultOptions : undefined;\n  };\n\n  _proto.setMutationDefaults = function setMutationDefaults(mutationKey, options) {\n    var result = this.mutationDefaults.find(function (x) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(mutationKey) === (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(x.mutationKey);\n    });\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.mutationDefaults.push({\n        mutationKey: mutationKey,\n        defaultOptions: options\n      });\n    }\n  };\n\n  _proto.getMutationDefaults = function getMutationDefaults(mutationKey) {\n    var _this$mutationDefault;\n\n    return mutationKey ? (_this$mutationDefault = this.mutationDefaults.find(function (x) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(mutationKey, x.mutationKey);\n    })) == null ? void 0 : _this$mutationDefault.defaultOptions : undefined;\n  };\n\n  _proto.defaultQueryOptions = function defaultQueryOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n\n    var defaultedOptions = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.defaultOptions.queries, this.getQueryDefaults(options == null ? void 0 : options.queryKey), options, {\n      _defaulted: true\n    });\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKeyByOptions)(defaultedOptions.queryKey, defaultedOptions);\n    }\n\n    return defaultedOptions;\n  };\n\n  _proto.defaultQueryObserverOptions = function defaultQueryObserverOptions(options) {\n    return this.defaultQueryOptions(options);\n  };\n\n  _proto.defaultMutationOptions = function defaultMutationOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.defaultOptions.mutations, this.getMutationDefaults(options == null ? void 0 : options.mutationKey), options, {\n      _defaulted: true\n    });\n  };\n\n  _proto.clear = function clear() {\n    this.queryCache.clear();\n    this.mutationCache.clear();\n  };\n\n  return QueryClient;\n}();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/queryClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/queryObserver.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/core/queryObserver.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryObserver: () => (/* binding */ QueryObserver)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./focusManager */ \"(ssr)/./node_modules/react-query/es/core/focusManager.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n\n\n\n\n\n\n\n\nvar QueryObserver = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(QueryObserver, _Subscribable);\n\n  function QueryObserver(client, options) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.options = options;\n    _this.trackedProps = [];\n    _this.selectError = null;\n\n    _this.bindMethods();\n\n    _this.setOptions(options);\n\n    return _this;\n  }\n\n  var _proto = QueryObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    this.remove = this.remove.bind(this);\n    this.refetch = this.refetch.bind(this);\n  };\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (this.listeners.length === 1) {\n      this.currentQuery.addObserver(this);\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch();\n      }\n\n      this.updateTimers();\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      this.destroy();\n    }\n  };\n\n  _proto.shouldFetchOnReconnect = function shouldFetchOnReconnect() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);\n  };\n\n  _proto.shouldFetchOnWindowFocus = function shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);\n  };\n\n  _proto.destroy = function destroy() {\n    this.listeners = [];\n    this.clearTimers();\n    this.currentQuery.removeObserver(this);\n  };\n\n  _proto.setOptions = function setOptions(options, notifyOptions) {\n    var prevOptions = this.options;\n    var prevQuery = this.currentQuery;\n    this.options = this.client.defaultQueryObserverOptions(options);\n\n    if (typeof this.options.enabled !== 'undefined' && typeof this.options.enabled !== 'boolean') {\n      throw new Error('Expected enabled to be a boolean');\n    } // Keep previous query key if the user does not supply one\n\n\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey;\n    }\n\n    this.updateQuery();\n    var mounted = this.hasListeners(); // Fetch if there are subscribers\n\n    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {\n      this.executeFetch();\n    } // Update result\n\n\n    this.updateResult(notifyOptions); // Update stale interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {\n      this.updateStaleTimeout();\n    }\n\n    var nextRefetchInterval = this.computeRefetchInterval(); // Update refetch interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {\n      this.updateRefetchInterval(nextRefetchInterval);\n    }\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(options) {\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return this.createResult(query, defaultedOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n\n  _proto.trackResult = function trackResult(result, defaultedOptions) {\n    var _this2 = this;\n\n    var trackedResult = {};\n\n    var trackProp = function trackProp(key) {\n      if (!_this2.trackedProps.includes(key)) {\n        _this2.trackedProps.push(key);\n      }\n    };\n\n    Object.keys(result).forEach(function (key) {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: function get() {\n          trackProp(key);\n          return result[key];\n        }\n      });\n    });\n\n    if (defaultedOptions.useErrorBoundary || defaultedOptions.suspense) {\n      trackProp('error');\n    }\n\n    return trackedResult;\n  };\n\n  _proto.getNextResult = function getNextResult(options) {\n    var _this3 = this;\n\n    return new Promise(function (resolve, reject) {\n      var unsubscribe = _this3.subscribe(function (result) {\n        if (!result.isFetching) {\n          unsubscribe();\n\n          if (result.isError && (options == null ? void 0 : options.throwOnError)) {\n            reject(result.error);\n          } else {\n            resolve(result);\n          }\n        }\n      });\n    });\n  };\n\n  _proto.getCurrentQuery = function getCurrentQuery() {\n    return this.currentQuery;\n  };\n\n  _proto.remove = function remove() {\n    this.client.getQueryCache().remove(this.currentQuery);\n  };\n\n  _proto.refetch = function refetch(options) {\n    return this.fetch((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n      meta: {\n        refetchPage: options == null ? void 0 : options.refetchPage\n      }\n    }));\n  };\n\n  _proto.fetchOptimistic = function fetchOptimistic(options) {\n    var _this4 = this;\n\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return query.fetch().then(function () {\n      return _this4.createResult(query, defaultedOptions);\n    });\n  };\n\n  _proto.fetch = function fetch(fetchOptions) {\n    var _this5 = this;\n\n    return this.executeFetch(fetchOptions).then(function () {\n      _this5.updateResult();\n\n      return _this5.currentResult;\n    });\n  };\n\n  _proto.executeFetch = function executeFetch(fetchOptions) {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery(); // Fetch\n\n    var promise = this.currentQuery.fetch(this.options, fetchOptions);\n\n    if (!(fetchOptions == null ? void 0 : fetchOptions.throwOnError)) {\n      promise = promise.catch(_utils__WEBPACK_IMPORTED_MODULE_2__.noop);\n    }\n\n    return promise;\n  };\n\n  _proto.updateStaleTimeout = function updateStaleTimeout() {\n    var _this6 = this;\n\n    this.clearStaleTimeout();\n\n    if (_utils__WEBPACK_IMPORTED_MODULE_2__.isServer || this.currentResult.isStale || !(0,_utils__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(this.options.staleTime)) {\n      return;\n    }\n\n    var time = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.timeUntilStale)(this.currentResult.dataUpdatedAt, this.options.staleTime); // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n\n    var timeout = time + 1;\n    this.staleTimeoutId = setTimeout(function () {\n      if (!_this6.currentResult.isStale) {\n        _this6.updateResult();\n      }\n    }, timeout);\n  };\n\n  _proto.computeRefetchInterval = function computeRefetchInterval() {\n    var _this$options$refetch;\n\n    return typeof this.options.refetchInterval === 'function' ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;\n  };\n\n  _proto.updateRefetchInterval = function updateRefetchInterval(nextInterval) {\n    var _this7 = this;\n\n    this.clearRefetchInterval();\n    this.currentRefetchInterval = nextInterval;\n\n    if (_utils__WEBPACK_IMPORTED_MODULE_2__.isServer || this.options.enabled === false || !(0,_utils__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {\n      return;\n    }\n\n    this.refetchIntervalId = setInterval(function () {\n      if (_this7.options.refetchIntervalInBackground || _focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused()) {\n        _this7.executeFetch();\n      }\n    }, this.currentRefetchInterval);\n  };\n\n  _proto.updateTimers = function updateTimers() {\n    this.updateStaleTimeout();\n    this.updateRefetchInterval(this.computeRefetchInterval());\n  };\n\n  _proto.clearTimers = function clearTimers() {\n    this.clearStaleTimeout();\n    this.clearRefetchInterval();\n  };\n\n  _proto.clearStaleTimeout = function clearStaleTimeout() {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId);\n      this.staleTimeoutId = undefined;\n    }\n  };\n\n  _proto.clearRefetchInterval = function clearRefetchInterval() {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId);\n      this.refetchIntervalId = undefined;\n    }\n  };\n\n  _proto.createResult = function createResult(query, options) {\n    var prevQuery = this.currentQuery;\n    var prevOptions = this.options;\n    var prevResult = this.currentResult;\n    var prevResultState = this.currentResultState;\n    var prevResultOptions = this.currentResultOptions;\n    var queryChange = query !== prevQuery;\n    var queryInitialState = queryChange ? query.state : this.currentQueryInitialState;\n    var prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;\n    var state = query.state;\n    var dataUpdatedAt = state.dataUpdatedAt,\n        error = state.error,\n        errorUpdatedAt = state.errorUpdatedAt,\n        isFetching = state.isFetching,\n        status = state.status;\n    var isPreviousData = false;\n    var isPlaceholderData = false;\n    var data; // Optimistically set result in fetching state if needed\n\n    if (options.optimisticResults) {\n      var mounted = this.hasListeners();\n      var fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      var fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n\n      if (fetchOnMount || fetchOptionally) {\n        isFetching = true;\n\n        if (!dataUpdatedAt) {\n          status = 'loading';\n        }\n      }\n    } // Keep previous data if needed\n\n\n    if (options.keepPreviousData && !state.dataUpdateCount && (prevQueryResult == null ? void 0 : prevQueryResult.isSuccess) && status !== 'error') {\n      data = prevQueryResult.data;\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt;\n      status = prevQueryResult.status;\n      isPreviousData = true;\n    } // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n        // Memoize select result\n        if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {\n          data = this.selectResult;\n        } else {\n          try {\n            this.selectFn = options.select;\n            data = options.select(state.data);\n\n            if (options.structuralSharing !== false) {\n              data = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.replaceEqualDeep)(prevResult == null ? void 0 : prevResult.data, data);\n            }\n\n            this.selectResult = data;\n            this.selectError = null;\n          } catch (selectError) {\n            (0,_logger__WEBPACK_IMPORTED_MODULE_4__.getLogger)().error(selectError);\n            this.selectError = selectError;\n          }\n        }\n      } // Use query data\n      else {\n          data = state.data;\n        } // Show placeholder data if needed\n\n\n    if (typeof options.placeholderData !== 'undefined' && typeof data === 'undefined' && (status === 'loading' || status === 'idle')) {\n      var placeholderData; // Memoize placeholder data\n\n      if ((prevResult == null ? void 0 : prevResult.isPlaceholderData) && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === 'function' ? options.placeholderData() : options.placeholderData;\n\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData);\n\n            if (options.structuralSharing !== false) {\n              placeholderData = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.replaceEqualDeep)(prevResult == null ? void 0 : prevResult.data, placeholderData);\n            }\n\n            this.selectError = null;\n          } catch (selectError) {\n            (0,_logger__WEBPACK_IMPORTED_MODULE_4__.getLogger)().error(selectError);\n            this.selectError = selectError;\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success';\n        data = placeholderData;\n        isPlaceholderData = true;\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError;\n      data = this.selectResult;\n      errorUpdatedAt = Date.now();\n      status = 'error';\n    }\n\n    var result = {\n      status: status,\n      isLoading: status === 'loading',\n      isSuccess: status === 'success',\n      isError: status === 'error',\n      isIdle: status === 'idle',\n      data: data,\n      dataUpdatedAt: dataUpdatedAt,\n      error: error,\n      errorUpdatedAt: errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching: isFetching,\n      isRefetching: isFetching && status !== 'loading',\n      isLoadingError: status === 'error' && state.dataUpdatedAt === 0,\n      isPlaceholderData: isPlaceholderData,\n      isPreviousData: isPreviousData,\n      isRefetchError: status === 'error' && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove\n    };\n    return result;\n  };\n\n  _proto.shouldNotifyListeners = function shouldNotifyListeners(result, prevResult) {\n    if (!prevResult) {\n      return true;\n    }\n\n    var _this$options = this.options,\n        notifyOnChangeProps = _this$options.notifyOnChangeProps,\n        notifyOnChangePropsExclusions = _this$options.notifyOnChangePropsExclusions;\n\n    if (!notifyOnChangeProps && !notifyOnChangePropsExclusions) {\n      return true;\n    }\n\n    if (notifyOnChangeProps === 'tracked' && !this.trackedProps.length) {\n      return true;\n    }\n\n    var includedProps = notifyOnChangeProps === 'tracked' ? this.trackedProps : notifyOnChangeProps;\n    return Object.keys(result).some(function (key) {\n      var typedKey = key;\n      var changed = result[typedKey] !== prevResult[typedKey];\n      var isIncluded = includedProps == null ? void 0 : includedProps.some(function (x) {\n        return x === key;\n      });\n      var isExcluded = notifyOnChangePropsExclusions == null ? void 0 : notifyOnChangePropsExclusions.some(function (x) {\n        return x === key;\n      });\n      return changed && !isExcluded && (!includedProps || isIncluded);\n    });\n  };\n\n  _proto.updateResult = function updateResult(notifyOptions) {\n    var prevResult = this.currentResult;\n    this.currentResult = this.createResult(this.currentQuery, this.options);\n    this.currentResultState = this.currentQuery.state;\n    this.currentResultOptions = this.options; // Only notify if something has changed\n\n    if ((0,_utils__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(this.currentResult, prevResult)) {\n      return;\n    } // Determine which callbacks to trigger\n\n\n    var defaultNotifyOptions = {\n      cache: true\n    };\n\n    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && this.shouldNotifyListeners(this.currentResult, prevResult)) {\n      defaultNotifyOptions.listeners = true;\n    }\n\n    this.notify((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, defaultNotifyOptions, notifyOptions));\n  };\n\n  _proto.updateQuery = function updateQuery() {\n    var query = this.client.getQueryCache().build(this.client, this.options);\n\n    if (query === this.currentQuery) {\n      return;\n    }\n\n    var prevQuery = this.currentQuery;\n    this.currentQuery = query;\n    this.currentQueryInitialState = query.state;\n    this.previousQueryResult = this.currentResult;\n\n    if (this.hasListeners()) {\n      prevQuery == null ? void 0 : prevQuery.removeObserver(this);\n      query.addObserver(this);\n    }\n  };\n\n  _proto.onQueryUpdate = function onQueryUpdate(action) {\n    var notifyOptions = {};\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error' && !(0,_retryer__WEBPACK_IMPORTED_MODULE_5__.isCancelledError)(action.error)) {\n      notifyOptions.onError = true;\n    }\n\n    this.updateResult(notifyOptions);\n\n    if (this.hasListeners()) {\n      this.updateTimers();\n    }\n  };\n\n  _proto.notify = function notify(notifyOptions) {\n    var _this8 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        _this8.options.onSuccess == null ? void 0 : _this8.options.onSuccess(_this8.currentResult.data);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(_this8.currentResult.data, null);\n      } else if (notifyOptions.onError) {\n        _this8.options.onError == null ? void 0 : _this8.options.onError(_this8.currentResult.error);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(undefined, _this8.currentResult.error);\n      } // Then trigger the listeners\n\n\n      if (notifyOptions.listeners) {\n        _this8.listeners.forEach(function (listener) {\n          listener(_this8.currentResult);\n        });\n      } // Then the cache listeners\n\n\n      if (notifyOptions.cache) {\n        _this8.client.getQueryCache().notify({\n          query: _this8.currentQuery,\n          type: 'observerResultsUpdated'\n        });\n      }\n    });\n  };\n\n  return QueryObserver;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_7__.Subscribable);\n\nfunction shouldLoadOnMount(query, options) {\n  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === 'error' && options.retryOnMount === false);\n}\n\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\n\nfunction shouldFetchOn(query, options, field) {\n  if (options.enabled !== false) {\n    var value = typeof field === 'function' ? field(query) : field;\n    return value === 'always' || value !== false && isStale(query, options);\n  }\n\n  return false;\n}\n\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== 'error') && isStale(query, options);\n}\n\nfunction isStale(query, options) {\n  return query.isStaleByTime(options.staleTime);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/queryObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/retryer.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-query/es/core/retryer.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   Retryer: () => (/* binding */ Retryer),\n/* harmony export */   isCancelable: () => (/* binding */ isCancelable),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./focusManager */ \"(ssr)/./node_modules/react-query/es/core/focusManager.js\");\n/* harmony import */ var _onlineManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./onlineManager */ \"(ssr)/./node_modules/react-query/es/core/onlineManager.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1000 * Math.pow(2, failureCount), 30000);\n}\n\nfunction isCancelable(value) {\n  return typeof (value == null ? void 0 : value.cancel) === 'function';\n}\nvar CancelledError = function CancelledError(options) {\n  this.revert = options == null ? void 0 : options.revert;\n  this.silent = options == null ? void 0 : options.silent;\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n} // CLASS\n\nvar Retryer = function Retryer(config) {\n  var _this = this;\n\n  var cancelRetry = false;\n  var cancelFn;\n  var continueFn;\n  var promiseResolve;\n  var promiseReject;\n  this.abort = config.abort;\n\n  this.cancel = function (cancelOptions) {\n    return cancelFn == null ? void 0 : cancelFn(cancelOptions);\n  };\n\n  this.cancelRetry = function () {\n    cancelRetry = true;\n  };\n\n  this.continueRetry = function () {\n    cancelRetry = false;\n  };\n\n  this.continue = function () {\n    return continueFn == null ? void 0 : continueFn();\n  };\n\n  this.failureCount = 0;\n  this.isPaused = false;\n  this.isResolved = false;\n  this.isTransportCancelable = false;\n  this.promise = new Promise(function (outerResolve, outerReject) {\n    promiseResolve = outerResolve;\n    promiseReject = outerReject;\n  });\n\n  var resolve = function resolve(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onSuccess == null ? void 0 : config.onSuccess(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseResolve(value);\n    }\n  };\n\n  var reject = function reject(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onError == null ? void 0 : config.onError(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseReject(value);\n    }\n  };\n\n  var pause = function pause() {\n    return new Promise(function (continueResolve) {\n      continueFn = continueResolve;\n      _this.isPaused = true;\n      config.onPause == null ? void 0 : config.onPause();\n    }).then(function () {\n      continueFn = undefined;\n      _this.isPaused = false;\n      config.onContinue == null ? void 0 : config.onContinue();\n    });\n  }; // Create loop function\n\n\n  var run = function run() {\n    // Do nothing if already resolved\n    if (_this.isResolved) {\n      return;\n    }\n\n    var promiseOrValue; // Execute query\n\n    try {\n      promiseOrValue = config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    } // Create callback to cancel this fetch\n\n\n    cancelFn = function cancelFn(cancelOptions) {\n      if (!_this.isResolved) {\n        reject(new CancelledError(cancelOptions));\n        _this.abort == null ? void 0 : _this.abort(); // Cancel transport if supported\n\n        if (isCancelable(promiseOrValue)) {\n          try {\n            promiseOrValue.cancel();\n          } catch (_unused) {}\n        }\n      }\n    }; // Check if the transport layer support cancellation\n\n\n    _this.isTransportCancelable = isCancelable(promiseOrValue);\n    Promise.resolve(promiseOrValue).then(resolve).catch(function (error) {\n      var _config$retry, _config$retryDelay;\n\n      // Stop if the fetch is already resolved\n      if (_this.isResolved) {\n        return;\n      } // Do we need to retry the request?\n\n\n      var retry = (_config$retry = config.retry) != null ? _config$retry : 3;\n      var retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;\n      var delay = typeof retryDelay === 'function' ? retryDelay(_this.failureCount, error) : retryDelay;\n      var shouldRetry = retry === true || typeof retry === 'number' && _this.failureCount < retry || typeof retry === 'function' && retry(_this.failureCount, error);\n\n      if (cancelRetry || !shouldRetry) {\n        // We are done if the query does not need to be retried\n        reject(error);\n        return;\n      }\n\n      _this.failureCount++; // Notify on fail\n\n      config.onFail == null ? void 0 : config.onFail(_this.failureCount, error); // Delay\n\n      (0,_utils__WEBPACK_IMPORTED_MODULE_0__.sleep)(delay) // Pause if the document is not visible or when the device is offline\n      .then(function () {\n        if (!_focusManager__WEBPACK_IMPORTED_MODULE_1__.focusManager.isFocused() || !_onlineManager__WEBPACK_IMPORTED_MODULE_2__.onlineManager.isOnline()) {\n          return pause();\n        }\n      }).then(function () {\n        if (cancelRetry) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  }; // Start loop\n\n\n  run();\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/retryer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/subscribable.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-query/es/core/subscribable.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\nvar Subscribable = /*#__PURE__*/function () {\n  function Subscribable() {\n    this.listeners = [];\n  }\n\n  var _proto = Subscribable.prototype;\n\n  _proto.subscribe = function subscribe(listener) {\n    var _this = this;\n\n    var callback = listener || function () {\n      return undefined;\n    };\n\n    this.listeners.push(callback);\n    this.onSubscribe();\n    return function () {\n      _this.listeners = _this.listeners.filter(function (x) {\n        return x !== callback;\n      });\n\n      _this.onUnsubscribe();\n    };\n  };\n\n  _proto.hasListeners = function hasListeners() {\n    return this.listeners.length > 0;\n  };\n\n  _proto.onSubscribe = function onSubscribe() {// Do nothing\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {// Do nothing\n  };\n\n  return Subscribable;\n}();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9zdWJzY3JpYmFibGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPOztBQUVQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsK0NBQStDO0FBQy9DOztBQUVBLG1EQUFtRDtBQUNuRDs7QUFFQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9zdWJzY3JpYmFibGUuanM/NWUxOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIFN1YnNjcmliYWJsZSA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoKSB7XG4gIGZ1bmN0aW9uIFN1YnNjcmliYWJsZSgpIHtcbiAgICB0aGlzLmxpc3RlbmVycyA9IFtdO1xuICB9XG5cbiAgdmFyIF9wcm90byA9IFN1YnNjcmliYWJsZS5wcm90b3R5cGU7XG5cbiAgX3Byb3RvLnN1YnNjcmliZSA9IGZ1bmN0aW9uIHN1YnNjcmliZShsaXN0ZW5lcikge1xuICAgIHZhciBfdGhpcyA9IHRoaXM7XG5cbiAgICB2YXIgY2FsbGJhY2sgPSBsaXN0ZW5lciB8fCBmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIH07XG5cbiAgICB0aGlzLmxpc3RlbmVycy5wdXNoKGNhbGxiYWNrKTtcbiAgICB0aGlzLm9uU3Vic2NyaWJlKCk7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgIF90aGlzLmxpc3RlbmVycyA9IF90aGlzLmxpc3RlbmVycy5maWx0ZXIoZnVuY3Rpb24gKHgpIHtcbiAgICAgICAgcmV0dXJuIHggIT09IGNhbGxiYWNrO1xuICAgICAgfSk7XG5cbiAgICAgIF90aGlzLm9uVW5zdWJzY3JpYmUoKTtcbiAgICB9O1xuICB9O1xuXG4gIF9wcm90by5oYXNMaXN0ZW5lcnMgPSBmdW5jdGlvbiBoYXNMaXN0ZW5lcnMoKSB7XG4gICAgcmV0dXJuIHRoaXMubGlzdGVuZXJzLmxlbmd0aCA+IDA7XG4gIH07XG5cbiAgX3Byb3RvLm9uU3Vic2NyaWJlID0gZnVuY3Rpb24gb25TdWJzY3JpYmUoKSB7Ly8gRG8gbm90aGluZ1xuICB9O1xuXG4gIF9wcm90by5vblVuc3Vic2NyaWJlID0gZnVuY3Rpb24gb25VbnN1YnNjcmliZSgpIHsvLyBEbyBub3RoaW5nXG4gIH07XG5cbiAgcmV0dXJuIFN1YnNjcmliYWJsZTtcbn0oKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/subscribable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/types.js":
/*!***************************************************!*\
  !*** ./node_modules/react-query/es/core/types.js ***!
  \***************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/utils.js":
/*!***************************************************!*\
  !*** ./node_modules/react-query/es/core/utils.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   difference: () => (/* binding */ difference),\n/* harmony export */   ensureQueryKeyArray: () => (/* binding */ ensureQueryKeyArray),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   getAbortController: () => (/* binding */ getAbortController),\n/* harmony export */   hashQueryKey: () => (/* binding */ hashQueryKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isError: () => (/* binding */ isError),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isQueryKey: () => (/* binding */ isQueryKey),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   mapQueryStatusFilter: () => (/* binding */ mapQueryStatusFilter),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   parseFilterArgs: () => (/* binding */ parseFilterArgs),\n/* harmony export */   parseMutationArgs: () => (/* binding */ parseMutationArgs),\n/* harmony export */   parseMutationFilterArgs: () => (/* binding */ parseMutationFilterArgs),\n/* harmony export */   parseQueryArgs: () => (/* binding */ parseQueryArgs),\n/* harmony export */   partialDeepEqual: () => (/* binding */ partialDeepEqual),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceAt: () => (/* binding */ replaceAt),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   scheduleMicrotask: () => (/* binding */ scheduleMicrotask),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   stableValueHash: () => (/* binding */ stableValueHash),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n\n// TYPES\n// UTILS\nvar isServer = typeof window === 'undefined';\nfunction noop() {\n  return undefined;\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === 'number' && value >= 0 && value !== Infinity;\n}\nfunction ensureQueryKeyArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\nfunction difference(array1, array2) {\n  return array1.filter(function (x) {\n    return array2.indexOf(x) === -1;\n  });\n}\nfunction replaceAt(array, index, value) {\n  var copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction parseQueryArgs(arg1, arg2, arg3) {\n  if (!isQueryKey(arg1)) {\n    return arg1;\n  }\n\n  if (typeof arg2 === 'function') {\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg3, {\n      queryKey: arg1,\n      queryFn: arg2\n    });\n  }\n\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n    queryKey: arg1\n  });\n}\nfunction parseMutationArgs(arg1, arg2, arg3) {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg3, {\n        mutationKey: arg1,\n        mutationFn: arg2\n      });\n    }\n\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n      mutationKey: arg1\n    });\n  }\n\n  if (typeof arg1 === 'function') {\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n      mutationFn: arg1\n    });\n  }\n\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg1);\n}\nfunction parseFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [(0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n    queryKey: arg1\n  }), arg3] : [arg1 || {}, arg2];\n}\nfunction parseMutationFilterArgs(arg1, arg2) {\n  return isQueryKey(arg1) ? (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n    mutationKey: arg1\n  }) : arg1;\n}\nfunction mapQueryStatusFilter(active, inactive) {\n  if (active === true && inactive === true || active == null && inactive == null) {\n    return 'all';\n  } else if (active === false && inactive === false) {\n    return 'none';\n  } else {\n    // At this point, active|inactive can only be true|false or false|true\n    // so, when only one value is provided, the missing one has to be the negated value\n    var isActive = active != null ? active : !inactive;\n    return isActive ? 'active' : 'inactive';\n  }\n}\nfunction matchQuery(filters, query) {\n  var active = filters.active,\n      exact = filters.exact,\n      fetching = filters.fetching,\n      inactive = filters.inactive,\n      predicate = filters.predicate,\n      queryKey = filters.queryKey,\n      stale = filters.stale;\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n\n  var queryStatusFilter = mapQueryStatusFilter(active, inactive);\n\n  if (queryStatusFilter === 'none') {\n    return false;\n  } else if (queryStatusFilter !== 'all') {\n    var isActive = query.isActive();\n\n    if (queryStatusFilter === 'active' && !isActive) {\n      return false;\n    }\n\n    if (queryStatusFilter === 'inactive' && isActive) {\n      return false;\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false;\n  }\n\n  if (typeof fetching === 'boolean' && query.isFetching() !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  var exact = filters.exact,\n      fetching = filters.fetching,\n      predicate = filters.predicate,\n      mutationKey = filters.mutationKey;\n\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n\n    if (exact) {\n      if (hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n\n  if (typeof fetching === 'boolean' && mutation.state.status === 'loading' !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  var hashFn = (options == null ? void 0 : options.queryKeyHashFn) || hashQueryKey;\n  return hashFn(queryKey);\n}\n/**\n * Default query keys hash function.\n */\n\nfunction hashQueryKey(queryKey) {\n  var asArray = ensureQueryKeyArray(queryKey);\n  return stableValueHash(asArray);\n}\n/**\n * Hashes the value into a stable hash.\n */\n\nfunction stableValueHash(value) {\n  return JSON.stringify(value, function (_, val) {\n    return isPlainObject(val) ? Object.keys(val).sort().reduce(function (result, key) {\n      result[key] = val[key];\n      return result;\n    }, {}) : val;\n  });\n}\n/**\n * Checks if key `b` partially matches with key `a`.\n */\n\nfunction partialMatchKey(a, b) {\n  return partialDeepEqual(ensureQueryKeyArray(a), ensureQueryKeyArray(b));\n}\n/**\n * Checks if `b` partially matches with `a`.\n */\n\nfunction partialDeepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (typeof a !== typeof b) {\n    return false;\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some(function (key) {\n      return !partialDeepEqual(a[key], b[key]);\n    });\n  }\n\n  return false;\n}\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\n\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n\n  var array = Array.isArray(a) && Array.isArray(b);\n\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    var aSize = array ? a.length : Object.keys(a).length;\n    var bItems = array ? b : Object.keys(b);\n    var bSize = bItems.length;\n    var copy = array ? [] : {};\n    var equalItems = 0;\n\n    for (var i = 0; i < bSize; i++) {\n      var key = array ? i : bItems[i];\n      copy[key] = replaceEqualDeep(a[key], b[key]);\n\n      if (copy[key] === a[key]) {\n        equalItems++;\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n\n  return b;\n}\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\n\nfunction shallowEqualObjects(a, b) {\n  if (a && !b || b && !a) {\n    return false;\n  }\n\n  for (var key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n\n  return true;\n} // Copied from: https://github.com/jonschlinkert/is-plain-object\n\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  } // If has modified constructor\n\n\n  var ctor = o.constructor;\n\n  if (typeof ctor === 'undefined') {\n    return true;\n  } // If has modified prototype\n\n\n  var prot = ctor.prototype;\n\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  } // If constructor does not have an Object-specific method\n\n\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false;\n  } // Most likely a plain Object\n\n\n  return true;\n}\n\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === '[object Object]';\n}\n\nfunction isQueryKey(value) {\n  return typeof value === 'string' || Array.isArray(value);\n}\nfunction isError(value) {\n  return value instanceof Error;\n}\nfunction sleep(timeout) {\n  return new Promise(function (resolve) {\n    setTimeout(resolve, timeout);\n  });\n}\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\n\nfunction scheduleMicrotask(callback) {\n  Promise.resolve().then(callback).catch(function (error) {\n    return setTimeout(function () {\n      throw error;\n    });\n  });\n}\nfunction getAbortController() {\n  if (typeof AbortController === 'function') {\n    return new AbortController();\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/react-query/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core */ \"(ssr)/./node_modules/react-query/es/core/index.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _core__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _core__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./react */ \"(ssr)/./node_modules/react-query/es/react/index.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _react__WEBPACK_IMPORTED_MODULE_1__) if([\"default\",\"CancelledError\",\"QueryCache\",\"QueryClient\",\"QueryObserver\",\"QueriesObserver\",\"InfiniteQueryObserver\",\"MutationCache\",\"MutationObserver\",\"setLogger\",\"notifyManager\",\"focusManager\",\"onlineManager\",\"hashQueryKey\",\"isError\",\"isCancelledError\",\"dehydrate\",\"hydrate\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _react__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGVudGFiZWxsLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL2luZGV4LmpzPzgyNzQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9jb3JlJztcbmV4cG9ydCAqIGZyb20gJy4vcmVhY3QnOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/Hydrate.js":
/*!******************************************************!*\
  !*** ./node_modules/react-query/es/react/Hydrate.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hydrate: () => (/* binding */ Hydrate),\n/* harmony export */   useHydrate: () => (/* binding */ useHydrate)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core */ \"(ssr)/./node_modules/react-query/es/core/hydration.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n\n\n\nfunction useHydrate(state, options) {\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n  var optionsRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(options);\n  optionsRef.current = options; // Running hydrate again with the same queries is safe,\n  // it wont overwrite or initialize existing queries,\n  // relying on useMemo here is only a performance optimization.\n  // hydrate can and should be run *during* render here for SSR to work properly\n\n  react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(function () {\n    if (state) {\n      (0,_core__WEBPACK_IMPORTED_MODULE_2__.hydrate)(queryClient, state, optionsRef.current);\n    }\n  }, [queryClient, state]);\n}\nvar Hydrate = function Hydrate(_ref) {\n  var children = _ref.children,\n      options = _ref.options,\n      state = _ref.state;\n  useHydrate(state, options);\n  return children;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvSHlkcmF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFDUTtBQUNxQjtBQUNoRDtBQUNQLG9CQUFvQixvRUFBYztBQUNsQyxtQkFBbUIsbURBQVk7QUFDL0IsZ0NBQWdDO0FBQ2hDO0FBQ0E7QUFDQTs7QUFFQSxFQUFFLG9EQUFhO0FBQ2Y7QUFDQSxNQUFNLDhDQUFPO0FBQ2I7QUFDQSxHQUFHO0FBQ0g7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9yZWFjdC9IeWRyYXRlLmpzP2Y3YmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGh5ZHJhdGUgfSBmcm9tICcuLi9jb3JlJztcbmltcG9ydCB7IHVzZVF1ZXJ5Q2xpZW50IH0gZnJvbSAnLi9RdWVyeUNsaWVudFByb3ZpZGVyJztcbmV4cG9ydCBmdW5jdGlvbiB1c2VIeWRyYXRlKHN0YXRlLCBvcHRpb25zKSB7XG4gIHZhciBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KCk7XG4gIHZhciBvcHRpb25zUmVmID0gUmVhY3QudXNlUmVmKG9wdGlvbnMpO1xuICBvcHRpb25zUmVmLmN1cnJlbnQgPSBvcHRpb25zOyAvLyBSdW5uaW5nIGh5ZHJhdGUgYWdhaW4gd2l0aCB0aGUgc2FtZSBxdWVyaWVzIGlzIHNhZmUsXG4gIC8vIGl0IHdvbnQgb3ZlcndyaXRlIG9yIGluaXRpYWxpemUgZXhpc3RpbmcgcXVlcmllcyxcbiAgLy8gcmVseWluZyBvbiB1c2VNZW1vIGhlcmUgaXMgb25seSBhIHBlcmZvcm1hbmNlIG9wdGltaXphdGlvbi5cbiAgLy8gaHlkcmF0ZSBjYW4gYW5kIHNob3VsZCBiZSBydW4gKmR1cmluZyogcmVuZGVyIGhlcmUgZm9yIFNTUiB0byB3b3JrIHByb3Blcmx5XG5cbiAgUmVhY3QudXNlTWVtbyhmdW5jdGlvbiAoKSB7XG4gICAgaWYgKHN0YXRlKSB7XG4gICAgICBoeWRyYXRlKHF1ZXJ5Q2xpZW50LCBzdGF0ZSwgb3B0aW9uc1JlZi5jdXJyZW50KTtcbiAgICB9XG4gIH0sIFtxdWVyeUNsaWVudCwgc3RhdGVdKTtcbn1cbmV4cG9ydCB2YXIgSHlkcmF0ZSA9IGZ1bmN0aW9uIEh5ZHJhdGUoX3JlZikge1xuICB2YXIgY2hpbGRyZW4gPSBfcmVmLmNoaWxkcmVuLFxuICAgICAgb3B0aW9ucyA9IF9yZWYub3B0aW9ucyxcbiAgICAgIHN0YXRlID0gX3JlZi5zdGF0ZTtcbiAgdXNlSHlkcmF0ZShzdGF0ZSwgb3B0aW9ucyk7XG4gIHJldHVybiBjaGlsZHJlbjtcbn07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/Hydrate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-query/es/react/QueryClientProvider.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar defaultContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(undefined);\nvar QueryClientSharingContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(false); // if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\n\nfunction getQueryClientContext(contextSharing) {\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext;\n    }\n\n    return window.ReactQueryClientContext;\n  }\n\n  return defaultContext;\n}\n\nvar useQueryClient = function useQueryClient() {\n  var queryClient = react__WEBPACK_IMPORTED_MODULE_0___default().useContext(getQueryClientContext(react__WEBPACK_IMPORTED_MODULE_0___default().useContext(QueryClientSharingContext)));\n\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one');\n  }\n\n  return queryClient;\n};\nvar QueryClientProvider = function QueryClientProvider(_ref) {\n  var client = _ref.client,\n      _ref$contextSharing = _ref.contextSharing,\n      contextSharing = _ref$contextSharing === void 0 ? false : _ref$contextSharing,\n      children = _ref.children;\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    client.mount();\n    return function () {\n      client.unmount();\n    };\n  }, [client]);\n  var Context = getQueryClientContext(contextSharing);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(QueryClientSharingContext.Provider, {\n    value: contextSharing\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Context.Provider, {\n    value: client\n  }, children));\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/QueryErrorResetBoundary.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-query/es/react/QueryErrorResetBoundary.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryErrorResetBoundary: () => (/* binding */ QueryErrorResetBoundary),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* binding */ useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n // CONTEXT\n\nfunction createValue() {\n  var _isReset = false;\n  return {\n    clearReset: function clearReset() {\n      _isReset = false;\n    },\n    reset: function reset() {\n      _isReset = true;\n    },\n    isReset: function isReset() {\n      return _isReset;\n    }\n  };\n}\n\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(createValue()); // HOOK\n\nvar useQueryErrorResetBoundary = function useQueryErrorResetBoundary() {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useContext(QueryErrorResetBoundaryContext);\n}; // COMPONENT\n\nvar QueryErrorResetBoundary = function QueryErrorResetBoundary(_ref) {\n  var children = _ref.children;\n  var value = react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(function () {\n    return createValue();\n  }, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(QueryErrorResetBoundaryContext.Provider, {\n    value: value\n  }, typeof children === 'function' ? children(value) : children);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvUXVlcnlFcnJvclJlc2V0Qm91bmRhcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUEwQixDQUFDOztBQUUzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxrREFBa0QsMERBQW1CLGlCQUFpQjs7QUFFL0U7QUFDUCxTQUFTLHVEQUFnQjtBQUN6QixHQUFHOztBQUVJO0FBQ1A7QUFDQSxjQUFjLG9EQUFhO0FBQzNCO0FBQ0EsR0FBRztBQUNILHNCQUFzQiwwREFBbUI7QUFDekM7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvUXVlcnlFcnJvclJlc2V0Qm91bmRhcnkuanM/NzYyNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnOyAvLyBDT05URVhUXG5cbmZ1bmN0aW9uIGNyZWF0ZVZhbHVlKCkge1xuICB2YXIgX2lzUmVzZXQgPSBmYWxzZTtcbiAgcmV0dXJuIHtcbiAgICBjbGVhclJlc2V0OiBmdW5jdGlvbiBjbGVhclJlc2V0KCkge1xuICAgICAgX2lzUmVzZXQgPSBmYWxzZTtcbiAgICB9LFxuICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHtcbiAgICAgIF9pc1Jlc2V0ID0gdHJ1ZTtcbiAgICB9LFxuICAgIGlzUmVzZXQ6IGZ1bmN0aW9uIGlzUmVzZXQoKSB7XG4gICAgICByZXR1cm4gX2lzUmVzZXQ7XG4gICAgfVxuICB9O1xufVxuXG52YXIgUXVlcnlFcnJvclJlc2V0Qm91bmRhcnlDb250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQoY3JlYXRlVmFsdWUoKSk7IC8vIEhPT0tcblxuZXhwb3J0IHZhciB1c2VRdWVyeUVycm9yUmVzZXRCb3VuZGFyeSA9IGZ1bmN0aW9uIHVzZVF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5KCkge1xuICByZXR1cm4gUmVhY3QudXNlQ29udGV4dChRdWVyeUVycm9yUmVzZXRCb3VuZGFyeUNvbnRleHQpO1xufTsgLy8gQ09NUE9ORU5UXG5cbmV4cG9ydCB2YXIgUXVlcnlFcnJvclJlc2V0Qm91bmRhcnkgPSBmdW5jdGlvbiBRdWVyeUVycm9yUmVzZXRCb3VuZGFyeShfcmVmKSB7XG4gIHZhciBjaGlsZHJlbiA9IF9yZWYuY2hpbGRyZW47XG4gIHZhciB2YWx1ZSA9IFJlYWN0LnVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBjcmVhdGVWYWx1ZSgpO1xuICB9LCBbXSk7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChRdWVyeUVycm9yUmVzZXRCb3VuZGFyeUNvbnRleHQuUHJvdmlkZXIsIHtcbiAgICB2YWx1ZTogdmFsdWVcbiAgfSwgdHlwZW9mIGNoaWxkcmVuID09PSAnZnVuY3Rpb24nID8gY2hpbGRyZW4odmFsdWUpIDogY2hpbGRyZW4pO1xufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/QueryErrorResetBoundary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/index.js":
/*!****************************************************!*\
  !*** ./node_modules/react-query/es/react/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hydrate: () => (/* reexport safe */ _Hydrate__WEBPACK_IMPORTED_MODULE_10__.Hydrate),\n/* harmony export */   QueryClientProvider: () => (/* reexport safe */ _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__.QueryClientProvider),\n/* harmony export */   QueryErrorResetBoundary: () => (/* reexport safe */ _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_3__.QueryErrorResetBoundary),\n/* harmony export */   useHydrate: () => (/* reexport safe */ _Hydrate__WEBPACK_IMPORTED_MODULE_10__.useHydrate),\n/* harmony export */   useInfiniteQuery: () => (/* reexport safe */ _useInfiniteQuery__WEBPACK_IMPORTED_MODULE_9__.useInfiniteQuery),\n/* harmony export */   useIsFetching: () => (/* reexport safe */ _useIsFetching__WEBPACK_IMPORTED_MODULE_4__.useIsFetching),\n/* harmony export */   useIsMutating: () => (/* reexport safe */ _useIsMutating__WEBPACK_IMPORTED_MODULE_5__.useIsMutating),\n/* harmony export */   useMutation: () => (/* reexport safe */ _useMutation__WEBPACK_IMPORTED_MODULE_6__.useMutation),\n/* harmony export */   useQueries: () => (/* reexport safe */ _useQueries__WEBPACK_IMPORTED_MODULE_8__.useQueries),\n/* harmony export */   useQuery: () => (/* reexport safe */ _useQuery__WEBPACK_IMPORTED_MODULE_7__.useQuery),\n/* harmony export */   useQueryClient: () => (/* reexport safe */ _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__.useQueryClient),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* reexport safe */ _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_3__.useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var _setBatchUpdatesFn__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./setBatchUpdatesFn */ \"(ssr)/./node_modules/react-query/es/react/setBatchUpdatesFn.js\");\n/* harmony import */ var _setLogger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./setLogger */ \"(ssr)/./node_modules/react-query/es/react/setLogger.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n/* harmony import */ var _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryErrorResetBoundary */ \"(ssr)/./node_modules/react-query/es/react/QueryErrorResetBoundary.js\");\n/* harmony import */ var _useIsFetching__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useIsFetching */ \"(ssr)/./node_modules/react-query/es/react/useIsFetching.js\");\n/* harmony import */ var _useIsMutating__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useIsMutating */ \"(ssr)/./node_modules/react-query/es/react/useIsMutating.js\");\n/* harmony import */ var _useMutation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./useMutation */ \"(ssr)/./node_modules/react-query/es/react/useMutation.js\");\n/* harmony import */ var _useQuery__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./useQuery */ \"(ssr)/./node_modules/react-query/es/react/useQuery.js\");\n/* harmony import */ var _useQueries__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./useQueries */ \"(ssr)/./node_modules/react-query/es/react/useQueries.js\");\n/* harmony import */ var _useInfiniteQuery__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./useInfiniteQuery */ \"(ssr)/./node_modules/react-query/es/react/useInfiniteQuery.js\");\n/* harmony import */ var _Hydrate__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Hydrate */ \"(ssr)/./node_modules/react-query/es/react/Hydrate.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/react-query/es/react/types.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_types__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_11__) if([\"default\",\"QueryClientProvider\",\"useQueryClient\",\"QueryErrorResetBoundary\",\"useQueryErrorResetBoundary\",\"useIsFetching\",\"useIsMutating\",\"useMutation\",\"useQuery\",\"useQueries\",\"useInfiniteQuery\",\"useHydrate\",\"Hydrate\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_11__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n// Side effects\n\n\n\n\n\n\n\n\n\n\n // Types\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQzZCO0FBQ1I7QUFDdUQ7QUFDb0I7QUFDaEQ7QUFDQTtBQUNKO0FBQ047QUFDSTtBQUNZO0FBQ04sQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9yZWFjdC9pbmRleC5qcz83MmM5Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNpZGUgZWZmZWN0c1xuaW1wb3J0ICcuL3NldEJhdGNoVXBkYXRlc0ZuJztcbmltcG9ydCAnLi9zZXRMb2dnZXInO1xuZXhwb3J0IHsgUXVlcnlDbGllbnRQcm92aWRlciwgdXNlUXVlcnlDbGllbnQgfSBmcm9tICcuL1F1ZXJ5Q2xpZW50UHJvdmlkZXInO1xuZXhwb3J0IHsgUXVlcnlFcnJvclJlc2V0Qm91bmRhcnksIHVzZVF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5IH0gZnJvbSAnLi9RdWVyeUVycm9yUmVzZXRCb3VuZGFyeSc7XG5leHBvcnQgeyB1c2VJc0ZldGNoaW5nIH0gZnJvbSAnLi91c2VJc0ZldGNoaW5nJztcbmV4cG9ydCB7IHVzZUlzTXV0YXRpbmcgfSBmcm9tICcuL3VzZUlzTXV0YXRpbmcnO1xuZXhwb3J0IHsgdXNlTXV0YXRpb24gfSBmcm9tICcuL3VzZU11dGF0aW9uJztcbmV4cG9ydCB7IHVzZVF1ZXJ5IH0gZnJvbSAnLi91c2VRdWVyeSc7XG5leHBvcnQgeyB1c2VRdWVyaWVzIH0gZnJvbSAnLi91c2VRdWVyaWVzJztcbmV4cG9ydCB7IHVzZUluZmluaXRlUXVlcnkgfSBmcm9tICcuL3VzZUluZmluaXRlUXVlcnknO1xuZXhwb3J0IHsgdXNlSHlkcmF0ZSwgSHlkcmF0ZSB9IGZyb20gJy4vSHlkcmF0ZSc7IC8vIFR5cGVzXG5cbmV4cG9ydCAqIGZyb20gJy4vdHlwZXMnOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/logger.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-query/es/react/logger.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\nvar logger = console;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvbG9nZ2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9yZWFjdC9sb2dnZXIuanM/YzNhMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIGxvZ2dlciA9IGNvbnNvbGU7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/logger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/reactBatchedUpdates.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-query/es/react/reactBatchedUpdates.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unstable_batchedUpdates: () => (/* binding */ unstable_batchedUpdates)\n/* harmony export */ });\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_0__);\n\nvar unstable_batchedUpdates = (react_dom__WEBPACK_IMPORTED_MODULE_0___default().unstable_batchedUpdates);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvcmVhY3RCYXRjaGVkVXBkYXRlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUM7QUFDMUIsOEJBQThCLDBFQUFnQyIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9yZWFjdC9yZWFjdEJhdGNoZWRVcGRhdGVzLmpzPzlhMmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0RE9NIGZyb20gJ3JlYWN0LWRvbSc7XG5leHBvcnQgdmFyIHVuc3RhYmxlX2JhdGNoZWRVcGRhdGVzID0gUmVhY3RET00udW5zdGFibGVfYmF0Y2hlZFVwZGF0ZXM7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/reactBatchedUpdates.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/setBatchUpdatesFn.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-query/es/react/setBatchUpdatesFn.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _reactBatchedUpdates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./reactBatchedUpdates */ \"(ssr)/./node_modules/react-query/es/react/reactBatchedUpdates.js\");\n\n\n_core__WEBPACK_IMPORTED_MODULE_0__.notifyManager.setBatchNotifyFunction(_reactBatchedUpdates__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3Qvc2V0QmF0Y2hVcGRhdGVzRm4uanMiLCJtYXBwaW5ncyI6Ijs7O0FBQXdDO0FBQ3dCO0FBQ2hFLGdEQUFhLHdCQUF3Qix5RUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3Qvc2V0QmF0Y2hVcGRhdGVzRm4uanM/MjQ0OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBub3RpZnlNYW5hZ2VyIH0gZnJvbSAnLi4vY29yZSc7XG5pbXBvcnQgeyB1bnN0YWJsZV9iYXRjaGVkVXBkYXRlcyB9IGZyb20gJy4vcmVhY3RCYXRjaGVkVXBkYXRlcyc7XG5ub3RpZnlNYW5hZ2VyLnNldEJhdGNoTm90aWZ5RnVuY3Rpb24odW5zdGFibGVfYmF0Y2hlZFVwZGF0ZXMpOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/setBatchUpdatesFn.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/setLogger.js":
/*!********************************************************!*\
  !*** ./node_modules/react-query/es/react/setLogger.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/react/logger.js\");\n\n\n(0,_core__WEBPACK_IMPORTED_MODULE_0__.setLogger)(_logger__WEBPACK_IMPORTED_MODULE_1__.logger);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3Qvc2V0TG9nZ2VyLmpzIiwibWFwcGluZ3MiOiI7OztBQUFvQztBQUNGO0FBQ2xDLGdEQUFTLENBQUMsMkNBQU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3Qvc2V0TG9nZ2VyLmpzP2FlZGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc2V0TG9nZ2VyIH0gZnJvbSAnLi4vY29yZSc7XG5pbXBvcnQgeyBsb2dnZXIgfSBmcm9tICcuL2xvZ2dlcic7XG5zZXRMb2dnZXIobG9nZ2VyKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/setLogger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/types.js":
/*!****************************************************!*\
  !*** ./node_modules/react-query/es/react/types.js ***!
  \****************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useBaseQuery.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/react/useBaseQuery.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryErrorResetBoundary */ \"(ssr)/./node_modules/react-query/es/react/QueryErrorResetBoundary.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/react/utils.js\");\n\n\n\n\n\nfunction useBaseQuery(options, Observer) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(0),\n      forceUpdate = _React$useState[1];\n\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n  var errorResetBoundary = (0,_QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_2__.useQueryErrorResetBoundary)();\n  var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure results are optimistically set in fetching state before subscribing or updating options\n\n  defaultedOptions.optimisticResults = true; // Include callbacks in batch renders\n\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(defaultedOptions.onError);\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(defaultedOptions.onSuccess);\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(defaultedOptions.onSettled);\n  }\n\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000;\n    } // Set cache time to 1 if the option has been set to 0\n    // when using suspense to prevent infinite loop of fetches\n\n\n    if (defaultedOptions.cacheTime === 0) {\n      defaultedOptions.cacheTime = 1;\n    }\n  }\n\n  if (defaultedOptions.suspense || defaultedOptions.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      defaultedOptions.retryOnMount = false;\n    }\n  }\n\n  var _React$useState2 = react__WEBPACK_IMPORTED_MODULE_0___default().useState(function () {\n    return new Observer(queryClient, defaultedOptions);\n  }),\n      observer = _React$useState2[0];\n\n  var result = observer.getOptimisticResult(defaultedOptions);\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    mountedRef.current = true;\n    errorResetBoundary.clearReset();\n    var unsubscribe = observer.subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    })); // Update result to make sure we did not miss any query updates\n    // between creating the observer and subscribing to it.\n\n    observer.updateResult();\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [errorResetBoundary, observer]);\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, {\n      listeners: false\n    });\n  }, [defaultedOptions, observer]); // Handle suspense\n\n  if (defaultedOptions.suspense && result.isLoading) {\n    throw observer.fetchOptimistic(defaultedOptions).then(function (_ref) {\n      var data = _ref.data;\n      defaultedOptions.onSuccess == null ? void 0 : defaultedOptions.onSuccess(data);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(data, null);\n    }).catch(function (error) {\n      errorResetBoundary.clearReset();\n      defaultedOptions.onError == null ? void 0 : defaultedOptions.onError(error);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(undefined, error);\n    });\n  } // Handle error boundary\n\n\n  if (result.isError && !errorResetBoundary.isReset() && !result.isFetching && (0,_utils__WEBPACK_IMPORTED_MODULE_4__.shouldThrowError)(defaultedOptions.suspense, defaultedOptions.useErrorBoundary, [result.error, observer.getCurrentQuery()])) {\n    throw result.error;\n  } // Handle result property usage tracking\n\n\n  if (defaultedOptions.notifyOnChangeProps === 'tracked') {\n    result = observer.trackResult(result, defaultedOptions);\n  }\n\n  return result;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useBaseQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useInfiniteQuery.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-query/es/react/useInfiniteQuery.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInfiniteQuery: () => (/* binding */ useInfiniteQuery)\n/* harmony export */ });\n/* harmony import */ var _core_infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/infiniteQueryObserver */ \"(ssr)/./node_modules/react-query/es/core/infiniteQueryObserver.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _useBaseQuery__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useBaseQuery */ \"(ssr)/./node_modules/react-query/es/react/useBaseQuery.js\");\n\n\n // HOOK\n\nfunction useInfiniteQuery(arg1, arg2, arg3) {\n  var options = (0,_core_utils__WEBPACK_IMPORTED_MODULE_0__.parseQueryArgs)(arg1, arg2, arg3);\n  return (0,_useBaseQuery__WEBPACK_IMPORTED_MODULE_1__.useBaseQuery)(options, _core_infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_2__.InfiniteQueryObserver);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXNlSW5maW5pdGVRdWVyeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXNFO0FBQ3ZCO0FBQ0QsQ0FBQzs7QUFFeEM7QUFDUCxnQkFBZ0IsMkRBQWM7QUFDOUIsU0FBUywyREFBWSxVQUFVLDhFQUFxQjtBQUNwRCIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9yZWFjdC91c2VJbmZpbml0ZVF1ZXJ5LmpzPzFjNmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSW5maW5pdGVRdWVyeU9ic2VydmVyIH0gZnJvbSAnLi4vY29yZS9pbmZpbml0ZVF1ZXJ5T2JzZXJ2ZXInO1xuaW1wb3J0IHsgcGFyc2VRdWVyeUFyZ3MgfSBmcm9tICcuLi9jb3JlL3V0aWxzJztcbmltcG9ydCB7IHVzZUJhc2VRdWVyeSB9IGZyb20gJy4vdXNlQmFzZVF1ZXJ5JzsgLy8gSE9PS1xuXG5leHBvcnQgZnVuY3Rpb24gdXNlSW5maW5pdGVRdWVyeShhcmcxLCBhcmcyLCBhcmczKSB7XG4gIHZhciBvcHRpb25zID0gcGFyc2VRdWVyeUFyZ3MoYXJnMSwgYXJnMiwgYXJnMyk7XG4gIHJldHVybiB1c2VCYXNlUXVlcnkob3B0aW9ucywgSW5maW5pdGVRdWVyeU9ic2VydmVyKTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useInfiniteQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useIsFetching.js":
/*!************************************************************!*\
  !*** ./node_modules/react-query/es/react/useIsFetching.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsFetching: () => (/* binding */ useIsFetching)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n\n\n\n\n\nvar checkIsFetching = function checkIsFetching(queryClient, filters, isFetching, setIsFetching) {\n  var newIsFetching = queryClient.isFetching(filters);\n\n  if (isFetching !== newIsFetching) {\n    setIsFetching(newIsFetching);\n  }\n};\n\nfunction useIsFetching(arg1, arg2) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n\n  var _parseFilterArgs = (0,_core_utils__WEBPACK_IMPORTED_MODULE_2__.parseFilterArgs)(arg1, arg2),\n      filters = _parseFilterArgs[0];\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(queryClient.isFetching(filters)),\n      isFetching = _React$useState[0],\n      setIsFetching = _React$useState[1];\n\n  var filtersRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(filters);\n  filtersRef.current = filters;\n  var isFetchingRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(isFetching);\n  isFetchingRef.current = isFetching;\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    mountedRef.current = true;\n    checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);\n    var unsubscribe = queryClient.getQueryCache().subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [queryClient]);\n  return isFetching;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useIsFetching.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useIsMutating.js":
/*!************************************************************!*\
  !*** ./node_modules/react-query/es/react/useIsMutating.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsMutating: () => (/* binding */ useIsMutating)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n\n\n\n\nfunction useIsMutating(arg1, arg2) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);\n  var filters = (0,_core_utils__WEBPACK_IMPORTED_MODULE_1__.parseMutationFilterArgs)(arg1, arg2);\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(queryClient.isMutating(filters)),\n      isMutating = _React$useState[0],\n      setIsMutating = _React$useState[1];\n\n  var filtersRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(filters);\n  filtersRef.current = filters;\n  var isMutatingRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(isMutating);\n  isMutatingRef.current = isMutating;\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = queryClient.getMutationCache().subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        var newIsMutating = queryClient.isMutating(filtersRef.current);\n\n        if (isMutatingRef.current !== newIsMutating) {\n          setIsMutating(newIsMutating);\n        }\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [queryClient]);\n  return isMutating;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useIsMutating.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useMutation.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-query/es/react/useMutation.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMutation: () => (/* binding */ useMutation)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _core_mutationObserver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../core/mutationObserver */ \"(ssr)/./node_modules/react-query/es/core/mutationObserver.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/react/utils.js\");\n\n\n\n\n\n\n // HOOK\n\nfunction useMutation(arg1, arg2, arg3) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(false);\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0),\n      forceUpdate = _React$useState[1];\n\n  var options = (0,_core_utils__WEBPACK_IMPORTED_MODULE_2__.parseMutationArgs)(arg1, arg2, arg3);\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n  var obsRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef();\n\n  if (!obsRef.current) {\n    obsRef.current = new _core_mutationObserver__WEBPACK_IMPORTED_MODULE_4__.MutationObserver(queryClient, options);\n  } else {\n    obsRef.current.setOptions(options);\n  }\n\n  var currentResult = obsRef.current.getCurrentResult();\n  react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = obsRef.current.subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, []);\n  var mutate = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback(function (variables, mutateOptions) {\n    obsRef.current.mutate(variables, mutateOptions).catch(_core_utils__WEBPACK_IMPORTED_MODULE_2__.noop);\n  }, []);\n\n  if (currentResult.error && (0,_utils__WEBPACK_IMPORTED_MODULE_6__.shouldThrowError)(undefined, obsRef.current.options.useErrorBoundary, [currentResult.error])) {\n    throw currentResult.error;\n  }\n\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, currentResult, {\n    mutate: mutate,\n    mutateAsync: currentResult.mutate\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useMutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useQueries.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-query/es/react/useQueries.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQueries: () => (/* binding */ useQueries)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _core_queriesObserver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/queriesObserver */ \"(ssr)/./node_modules/react-query/es/core/queriesObserver.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n\n\n\n\nfunction useQueries(queries) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(0),\n      forceUpdate = _React$useState[1];\n\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n  var defaultedQueries = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return queries.map(function (options) {\n      var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure the results are already in fetching state before subscribing or updating options\n\n      defaultedOptions.optimisticResults = true;\n      return defaultedOptions;\n    });\n  }, [queries, queryClient]);\n\n  var _React$useState2 = react__WEBPACK_IMPORTED_MODULE_0___default().useState(function () {\n    return new _core_queriesObserver__WEBPACK_IMPORTED_MODULE_2__.QueriesObserver(queryClient, defaultedQueries);\n  }),\n      observer = _React$useState2[0];\n\n  var result = observer.getOptimisticResult(defaultedQueries);\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = observer.subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [observer]);\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(defaultedQueries, {\n      listeners: false\n    });\n  }, [defaultedQueries, observer]);\n  return result;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useQueries.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useQuery.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-query/es/react/useQuery.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuery: () => (/* binding */ useQuery)\n/* harmony export */ });\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core */ \"(ssr)/./node_modules/react-query/es/core/queryObserver.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _useBaseQuery__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useBaseQuery */ \"(ssr)/./node_modules/react-query/es/react/useBaseQuery.js\");\n\n\n // HOOK\n\nfunction useQuery(arg1, arg2, arg3) {\n  var parsedOptions = (0,_core_utils__WEBPACK_IMPORTED_MODULE_0__.parseQueryArgs)(arg1, arg2, arg3);\n  return (0,_useBaseQuery__WEBPACK_IMPORTED_MODULE_1__.useBaseQuery)(parsedOptions, _core__WEBPACK_IMPORTED_MODULE_2__.QueryObserver);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXNlUXVlcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF3QztBQUNPO0FBQ0QsQ0FBQzs7QUFFeEM7QUFDUCxzQkFBc0IsMkRBQWM7QUFDcEMsU0FBUywyREFBWSxnQkFBZ0IsZ0RBQWE7QUFDbEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXNlUXVlcnkuanM/Y2VmOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBRdWVyeU9ic2VydmVyIH0gZnJvbSAnLi4vY29yZSc7XG5pbXBvcnQgeyBwYXJzZVF1ZXJ5QXJncyB9IGZyb20gJy4uL2NvcmUvdXRpbHMnO1xuaW1wb3J0IHsgdXNlQmFzZVF1ZXJ5IH0gZnJvbSAnLi91c2VCYXNlUXVlcnknOyAvLyBIT09LXG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VRdWVyeShhcmcxLCBhcmcyLCBhcmczKSB7XG4gIHZhciBwYXJzZWRPcHRpb25zID0gcGFyc2VRdWVyeUFyZ3MoYXJnMSwgYXJnMiwgYXJnMyk7XG4gIHJldHVybiB1c2VCYXNlUXVlcnkocGFyc2VkT3B0aW9ucywgUXVlcnlPYnNlcnZlcik7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/react-query/es/react/utils.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError)\n/* harmony export */ });\nfunction shouldThrowError(suspense, _useErrorBoundary, params) {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary.apply(void 0, params);\n  } // Allow useErrorBoundary to override suspense's throwing behavior\n\n\n  if (typeof _useErrorBoundary === 'boolean') return _useErrorBoundary; // If suspense is enabled default to throwing errors\n\n  return !!suspense;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsSUFBSTs7O0FBR0osd0VBQXdFOztBQUV4RTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGVudGFiZWxsLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL3JlYWN0L3V0aWxzLmpzPzZlNTYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHNob3VsZFRocm93RXJyb3Ioc3VzcGVuc2UsIF91c2VFcnJvckJvdW5kYXJ5LCBwYXJhbXMpIHtcbiAgLy8gQWxsb3cgdXNlRXJyb3JCb3VuZGFyeSBmdW5jdGlvbiB0byBvdmVycmlkZSB0aHJvd2luZyBiZWhhdmlvciBvbiBhIHBlci1lcnJvciBiYXNpc1xuICBpZiAodHlwZW9mIF91c2VFcnJvckJvdW5kYXJ5ID09PSAnZnVuY3Rpb24nKSB7XG4gICAgcmV0dXJuIF91c2VFcnJvckJvdW5kYXJ5LmFwcGx5KHZvaWQgMCwgcGFyYW1zKTtcbiAgfSAvLyBBbGxvdyB1c2VFcnJvckJvdW5kYXJ5IHRvIG92ZXJyaWRlIHN1c3BlbnNlJ3MgdGhyb3dpbmcgYmVoYXZpb3JcblxuXG4gIGlmICh0eXBlb2YgX3VzZUVycm9yQm91bmRhcnkgPT09ICdib29sZWFuJykgcmV0dXJuIF91c2VFcnJvckJvdW5kYXJ5OyAvLyBJZiBzdXNwZW5zZSBpcyBlbmFibGVkIGRlZmF1bHQgdG8gdGhyb3dpbmcgZXJyb3JzXG5cbiAgcmV0dXJuICEhc3VzcGVuc2U7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/utils.js\n");

/***/ })

};
;