import HttpException from '@/utils/exceptions/http.exception';
import { Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';

const validateMongoId = async (request: Request, response: Response, next: NextFunction) => {
    const id: any = request.params;
    if (!mongoose.Types.ObjectId.isValid(id)) next(new HttpException(400, `Invalid Mongod ID ${id}`));
    next();
};

export const validateMongoIds = async (request: Request, response: Response, next: NextFunction) => {
    const ids: string[] = Object.values(request.params);

    for (const id of ids) {
        if (!mongoose.Types.ObjectId.isValid(id)) next(new HttpException(400, `Invalid Mongod ID ${id}`));
    }

    next();
};

export default validateMongoId;
