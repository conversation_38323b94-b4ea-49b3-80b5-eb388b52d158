import Joi from 'joi';
import { robotsMeta, Visibility, Language } from './glossaries.interface';

// Schema for version fields (common to both languages)
const versionFieldsSchema = {
    word: Joi.string().trim().min(1).required(),
    letter: Joi.string().required(),
    content: Joi.string().required(),
    metaTitle: Joi.string().required(),
    metaDescription: Joi.string().required(),
    url: Joi.string().required(),
    language: Joi.valid(...Object.values(Language)),
    visibility: Joi.valid(...Object.values(Visibility)),
    isArchived: Joi.boolean(),
    createdAt: Joi.date(),
    updatedAt: Joi.date(),
};

// Schema for creating a new glossary
const glossarySchema = Joi.object({
    versions: Joi.object()
        .pattern(
            Joi.string().valid('en', 'fr'), // Keys must be 'en' or 'fr'
            Joi.object(versionFieldsSchema), // Values must match version fields schema
        )
        .min(1)
        .required(), // At least one language version is required
    robotsMeta: Joi.valid(...Object.values(robotsMeta)),
});

// Schema for updating a glossary
const glossaryUpdateSchema = Joi.object({
    versions: Joi.object().pattern(
        Joi.string().valid('en', 'fr'), // Keys must be 'en' or 'fr'
        Joi.object({
            word: Joi.string().trim().min(1),
            letter: Joi.string(),
            content: Joi.string(),
            metaTitle: Joi.string(),
            metaDescription: Joi.string(),
            url: Joi.string(),
            language: Joi.valid(...Object.values(Language)),
            visibility: Joi.valid(...Object.values(Visibility)),
            isArchived: Joi.boolean(),
        }),
    ),
    robotsMeta: Joi.valid(...Object.values(robotsMeta)),
});

export { glossarySchema, glossaryUpdateSchema };
