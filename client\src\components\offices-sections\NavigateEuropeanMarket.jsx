import { Container, Grid } from "@mui/material";
import navigateAfricanMarket from "@/assets/images/offices/navigateEurope.png";
import Image from "next/image";

function NavigateEuropeanMarket({t}) {
  return (
    <div id="navigate-african-market-section">
      <Container className="custom-max-width">
        <Grid className="container" container columnSpacing={0}>
          <Grid item xs={12} sm={6}>
            <div className="left-section">
              <h2 className="heading-h1">
              {t("europe:intro:title")}
              </h2>
              <p className="paragraph">
              {t("europe:intro:description")}
              </p>
            
            </div>
          </Grid>
          <Grid item xs={12} sm={5}>
            <img
              width={307}
              height={324}
              alt= {t("europe:intro:altImg")}
              src={navigateAfricanMarket.src}
              loading="lazy"
            />
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default NavigateEuropeanMarket;
