import { Container, Grid } from "@mui/material";
import EmblaCarouselThumbsTeamPic from "../ui/emblaCarousel/EmblaCarouselThumbsTeamPic";
import img1 from "../../assets/images/Maroc/M1.jpg";
import img2 from "../../assets/images/Maroc/m3.jpg";
import img3 from "../../assets/images/Maroc/m3.jpg";
import img4 from "../../assets/images/Maroc/m3.jpg";
import img5 from "../../assets/images/Maroc/m4.jpg";

function EORServiceMaroc({ t }) {
  const OPTIONS = {};
  const SLIDE_COUNT = 10;
  const SLIDES = Array.from(Array(SLIDE_COUNT).keys());
  const slideImg = [
    { src: img1 },
    { src: img2 },
    { src: img3 },
    { src: img4 },
    { src: img5 },
  ];
  return (
    <Container id="eor-services-tn"  className="custom-max-width">
      <Grid
        className="container"
        justifyContent="space-between"
        container
        spacing={2}
      >
        <Grid item xs={12} sm={6}>
          <div className="team-thumbs">
            <EmblaCarouselThumbsTeamPic slides={slideImg} options={OPTIONS} />
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <div className="right-section">
            <p className="paragraph text-yellow">
              {t("morocco:EORServicesMA:subTitle")}
            </p>
            <p className="heading-h2 text-white">
              {t("morocco:EORServicesMA:title")}
            </p>
            <p className="paragraph text-white">
              {t("morocco:EORServicesMA:description")}
            </p>
            <p className="paragraph text-white">
              {t("morocco:EORServicesMA:description1")}
            </p>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default EORServiceMaroc;
