"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/ListArticles.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/ListArticles.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/CustomTooltip */ \"(app-pages-browser)/./src/components/ui/CustomTooltip.jsx\");\n/* harmony import */ var _components_loading_Loading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/loading/Loading */ \"(app-pages-browser)/./src/components/loading/Loading.jsx\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _assets_images_icons_preview_icon_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/preview-icon.svg */ \"(app-pages-browser)/./src/assets/images/icons/preview-icon.svg\");\n/* harmony import */ var _assets_images_icons_edit_icon_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/icons/edit-icon.svg */ \"(app-pages-browser)/./src/assets/images/icons/edit-icon.svg\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/assets/images/icons/applicationIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/applicationIcon.svg\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n/* harmony import */ var _components_ui_CustomFilters__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/CustomFilters */ \"(app-pages-browser)/./src/components/ui/CustomFilters.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ListArticles = (param)=>{\n    let { language } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [createdAt, setCreatedAt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const savedVisibility = localStorage.getItem(\"Visibility\");\n    const [visibility, setVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedVisibility || \"\");\n    const [isArchived, setIsArchivedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Default to show non-archived articles\n    const savedPagination = localStorage.getItem(\"PAGINATION_KEY\");\n    const savedSeachValue = localStorage.getItem(\"SearchValue\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedSeachValue || \"\");\n    const [paginationModel, setPaginationModel] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(savedPagination ? JSON.parse(savedPagination) : {\n        page: 0,\n        pageSize: 10\n    });\n    const isOpen = true;\n    const [selectedLanguage, setSelectedLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(language ? language : \"en\");\n    const [openArchiveDialog, setOpenArchiveDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openDeleteDialog, setOpenDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedArticle, setSelectedArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const archiveArticleMutation = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.usearchivedarticle)();\n    const deleteArticleMutation = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useDeleteArticle)();\n    const handleArchiveClick = (article)=>{\n        setSelectedArticle(article);\n        setOpenArchiveDialog(true);\n    };\n    const handleDeleteClick = (article)=>{\n        setSelectedArticle(article);\n        setOpenDeleteDialog(true);\n    };\n    const handleArchiveConfirm = async ()=>{\n        if (selectedArticle) {\n            try {\n                await archiveArticleMutation.mutateAsync({\n                    language: selectedLanguage,\n                    id: selectedArticle.id,\n                    archive: !selectedArticle.isArchived\n                });\n                getArticles.refetch();\n            } catch (error) {\n                console.error(\"Error archiving article:\", error);\n            }\n        }\n        setOpenArchiveDialog(false);\n        setSelectedArticle(null);\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (selectedArticle) {\n            try {\n                await deleteArticleMutation.mutateAsync({\n                    language: selectedLanguage,\n                    id: selectedArticle.id\n                });\n                getArticles.refetch();\n            } catch (error) {\n                console.error(\"Error deleting article:\", error);\n            }\n        }\n        setOpenDeleteDialog(false);\n        setSelectedArticle(null);\n    };\n    const handleDialogClose = ()=>{\n        setOpenArchiveDialog(false);\n        setOpenDeleteDialog(false);\n        setSelectedArticle(null);\n    };\n    const resetSearch = ()=>{\n        setCategory(\"\");\n        setSearchQuery(\"\");\n        setVisibility(\"\");\n        setSortOrder(\"\");\n        setCreatedAt(null);\n        setPublishDate(null);\n        setSelectedLanguage(language ? language : \"en\");\n        setPaginationModel({\n            page: 0,\n            pageSize: 10\n        });\n        setIsArchivedFilter(false); // Reset to show non-archived articles\n        setSearch(!search);\n        localStorage.setItem(\"Visibility\", \"\");\n        localStorage.setItem(\"SearchValue\", \"\");\n        localStorage.setItem(\"PAGINATION_KEY\", JSON.stringify({\n            page: 0,\n            pageSize: 10\n        }));\n    };\n    const truncateTitle = (title)=>{\n        const words = title.split(\" \");\n        if (words?.length > 4) {\n            return words.slice(0, 4).join(\" \") + \"...\";\n        } else {\n            return title;\n        }\n    };\n    const getCategoriesLang = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories)(selectedLanguage || \"en\");\n    const transformedCategoriesLang = getCategoriesLang?.data?.categories?.map((category)=>({\n            name: category?.versionscategory[0]?.name,\n            value: category?.versionscategory[0]?.name,\n            label: category?.versionscategory[0]?.name\n        })) || [];\n    const getArticles = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetArticlesDashboard)({\n        language: selectedLanguage,\n        pageSize: paginationModel.pageSize,\n        pageNumber: paginationModel.page + 1,\n        sortOrder,\n        searchQuery,\n        visibility,\n        createdAt,\n        isArchived,\n        publishDate,\n        categoryName: category\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSelectedLanguage(language);\n        getCategoriesLang.refetch();\n    }, [\n        language\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getArticles.refetch();\n    }, [\n        search,\n        paginationModel\n    ]);\n    const visibilityOption = [\n        {\n            value: \"Public\",\n            label: \"Public\"\n        },\n        {\n            value: \"Private\",\n            label: \"Private\"\n        },\n        {\n            value: \"Draft\",\n            label: \"Draft\"\n        }\n    ];\n    const handlePaginationChange = (newPaginationModel)=>{\n        setPaginationModel(newPaginationModel);\n        localStorage.setItem(\"PAGINATION_KEY\", JSON.stringify(newPaginationModel));\n    };\n    if (getArticles.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loading_Loading__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n            lineNumber: 197,\n            columnNumber: 12\n        }, undefined);\n    }\n    const rows = getArticles?.data?.articles?.map((item)=>({\n            id: item._id,\n            title: item?.versions?.[0]?.title ? truncateTitle(item?.versions?.[0]?.title) : \"No title\",\n            createdBy: item?.versions?.[0]?.createdBy || \"N/A\",\n            createdAt: item?.versions?.[0]?.createdAt,\n            language: item?.existingLanguages?.join(\"/\") || \"N/A\",\n            actions: item._id,\n            visibility: item?.versions?.[0]?.visibility || \"N/A\",\n            url: item?.versions?.[0]?.url || \"N/A\",\n            totalCommentaires: item?.totalCommentaires || \"0\",\n            isArchived: item?.versions?.[0]?.isArchived || false\n        })) || [];\n    const columns = [\n        {\n            field: \"title\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            headerName: t(\"listArticle:title\"),\n            flex: 1,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: `/${selectedLanguage}/blog/${params.row?.url}`,\n                    className: \"link\",\n                    children: params.row.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"createdBy\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            headerName: t(\"listArticle:createdBy\"),\n            flex: 0.4\n        },\n        {\n            field: \"createdAt\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            flex: 0.4,\n            headerName: t(\"listArticle:createdAt\"),\n            valueFormatter: _utils_functions__WEBPACK_IMPORTED_MODULE_11__.formatDate\n        },\n        {\n            field: \"language\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            flex: 0.4,\n            headerName: t(\"listopportunity:availablelanguage\")\n        },\n        {\n            field: \"visibility\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            flex: 0.4,\n            headerName: t(\"listArticle:visibility\")\n        },\n        {\n            field: \"isArchived\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            flex: 0.4,\n            headerName: t(\"listArticle:archived\"),\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: params.row.isArchived ? t(\"global:yes\") : t(\"global:no\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"totalCommentaires\",\n            headerClassName: \"datagrid-header\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            headerName: t(\"listArticle:nbOfComments\"),\n            flex: 0.4\n        },\n        {\n            field: \"actions\",\n            cellClassName: \"datagrid-cell\",\n            headerClassName: \"datagrid-header\",\n            headerName: \"\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"action-buttons\",\n                    style: {\n                        gridColumn: \"span 2\",\n                        width: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_edit_icon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.edit.route}/${params.row.id}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 293,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:edit\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"btn-download\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.comments.route}/${params.row.id}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:comments\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_preview_icon_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.websiteRoutesList.blog.route}/${params.row?.url}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 321,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:preview\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"22\",\n                                        height: \"20\",\n                                        viewBox: \"0 0 22 20\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M21 17C21 17.5304 20.7893 18.0391 20.4142 18.4142C20.0391 18.7893 19.5304 19 19 19H3C2.46957 19 1.96086 18.7893 1.58579 18.4142C1.21071 18.0391 1 17.5304 1 17V3C1 2.46957 1.21071 1.96086 1.58579 1.58579C1.96086 1.21071 2.46957 1 3 1H8L10 4H19C19.5304 4 20.0391 4.21071 20.4142 4.58579C20.7893 4.96086 21 5.46957 21 6V17Z\",\n                                            stroke: \"#1E1E1E\",\n                                            strokeWidth: \"2\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    leftIcon: true,\n                                    onClick: ()=>handleArchiveClick(params.row),\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 335,\n                                columnNumber: 15\n                            }, void 0),\n                            title: params.row.isArchived ? t(\"global:unarchive\") : t(\"global:archive\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"20\",\n                                        height: \"22\",\n                                        viewBox: \"0 0 20 22\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M1 5H3M3 5H19M3 5V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H15C15.5304 21 16.0391 20.7893 16.4142 20.4142C16.7893 20.0391 17 19.5304 17 19V5M6 5V3C6 2.46957 6.21071 1.96086 6.58579 1.58579C6.96086 1.21071 7.46957 1 8 1H12C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V5M8 10V16M12 10V16\",\n                                            stroke: \"#1E1E1E\",\n                                            strokeWidth: \"2\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    leftIcon: true,\n                                    onClick: ()=>handleDeleteClick(params.row),\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 369,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:delete\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, undefined),\n            flex: 1\n        }\n    ];\n    const archivedOptions = [\n        {\n            value: true,\n            label: \"Archived\"\n        },\n        {\n            value: false,\n            label: \"Not Archived\"\n        }\n    ];\n    const filters = [\n        {\n            type: \"text\",\n            label: \"Search By Title\",\n            value: searchQuery,\n            onChange: (e)=>setSearchQuery(e.target.value),\n            placeholder: \"Search\",\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:visibility\"),\n            value: visibility ? {\n                value: visibility,\n                label: visibilityOption.find((opt)=>opt.value === visibility)?.label || visibility\n            } : null,\n            onChange: (_, val)=>setVisibility(val?.value || \"\"),\n            options: visibilityOption,\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:archivage\"),\n            value: isArchived ? {\n                value: isArchived,\n                label: archivedOptions.find((opt)=>opt.value === isArchived)?.label || isArchived\n            } : null,\n            onChange: (_, val)=>setIsArchivedFilter(val?.value || \"\"),\n            options: archivedOptions,\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"global:sort\"),\n            value: sortOrder ? {\n                value: sortOrder,\n                label: t(sortOrder === \"desc\" ? \"global:newest\" : \"global:oldest\")\n            } : null,\n            onChange: (_, val)=>setSortOrder(val?.value || \"\"),\n            options: [\n                {\n                    value: \"desc\",\n                    label: t(\"global:newest\")\n                },\n                {\n                    value: \"asc\",\n                    label: t(\"global:oldest\")\n                }\n            ],\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:category\"),\n            value: category ? {\n                value: category,\n                label: transformedCategoriesLang.find((c)=>c.value === category)?.label || category\n            } : null,\n            onChange: (_, val)=>setCategory(val?.value || \"\"),\n            options: transformedCategoriesLang,\n            condition: true\n        },\n        {\n            type: \"date\",\n            label: t(\"listArticle:createdAt\"),\n            value: createdAt,\n            onChange: (newValue)=>setCreatedAt(newValue),\n            condition: true\n        },\n        {\n            type: \"date\",\n            label: t(\"listArticle:publishDate\"),\n            value: publishDate,\n            onChange: (newValue)=>setPublishDate(newValue),\n            condition: true\n        }\n    ];\n    const handleSearch = ()=>{\n        localStorage.setItem(\"SearchValue\", searchQuery);\n        localStorage.setItem(\"Visibility\", visibility);\n        setPaginationModel({\n            page: 0,\n            pageSize: paginationModel.pageSize\n        });\n        setSearch((prev)=>!prev);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"display-inline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"heading-h2 semi-bold\",\n                        children: [\n                            t(\"listArticle:listOfArticles\"),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"opportunities-nbr\",\n                                children: getArticles?.data?.totalArticles\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 505,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 503,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"btn btn-filled\",\n                        text: t(\"global:addarticle\"),\n                        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.add.route}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 502,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content ${isOpen ? \"open\" : \"closed\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"table-Grid\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomFilters__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    filters: filters,\n                                    onSearch: handleSearch,\n                                    onReset: resetSearch,\n                                    searchLabel: t(\"global:search\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 520,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 519,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    height: \"100%\",\n                                    width: \"100%\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_data_grid__WEBPACK_IMPORTED_MODULE_16__.DataGrid, {\n                                    rows: rows,\n                                    columns: columns,\n                                    pagination: true,\n                                    className: \"pentabell-table\",\n                                    paginationMode: \"server\",\n                                    paginationModel: paginationModel,\n                                    onPaginationModelChange: handlePaginationChange,\n                                    pageSizeOptions: [\n                                        5,\n                                        10,\n                                        25\n                                    ],\n                                    rowCount: getArticles?.data?.totalArticles || 0,\n                                    autoHeight: true,\n                                    disableSelectionOnClick: true,\n                                    columnVisibilityModel: {\n                                        createdBy: !isMobile,\n                                        createdAt: !isMobile,\n                                        totalCommentaires: !isMobile,\n                                        visibility: !isMobile,\n                                        language: !isMobile,\n                                        archived: !isMobile\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 531,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 530,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 518,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 517,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                id: \"toggle\",\n                open: openArchiveDialog,\n                onClose: handleDialogClose,\n                \"aria-labelledby\": \"archive-dialog-title\",\n                className: \"dialog-paper\",\n                sx: {\n                    \"& .MuiPaper-root\": {\n                        background: \"linear-gradient(#0b3051 0%, #234791 100%) !important\",\n                        color: \"#f8f8f8 !important\",\n                        borderBottom: \"transparent !important\",\n                        borderRadius: \"0px !important\",\n                        boxShadow: \"transparent !important\"\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        sx: {\n                            m: 0,\n                            p: 2\n                        },\n                        id: \"archive-dialog-title\",\n                        children: [\n                            selectedArticle?.isArchived ? t(\"global:unarchive\") : t(\"global:archive\"),\n                            \" \",\n                            t(\"global:article\"),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                \"aria-label\": \"close\",\n                                onClick: handleDialogClose,\n                                style: {\n                                    position: \"absolute\",\n                                    right: 8,\n                                    top: 8,\n                                    background: \"transparent\",\n                                    border: \"none\",\n                                    color: \"#f8f8f8\",\n                                    cursor: \"pointer\",\n                                    fontSize: \"20px\"\n                                },\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 580,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 575,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        dividers: true,\n                        className: \"dialog-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: \"center\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"48\",\n                                    height: \"48\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"none\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M21 17C21 17.5304 20.7893 18.0391 20.4142 18.4142C20.0391 18.7893 19.5304 19 19 19H3C2.46957 19 1.96086 18.7893 1.58579 18.4142C1.21071 18.0391 1 17.5304 1 17V3C1 2.46957 1.21071 1.96086 1.58579 1.58579C1.96086 1.21071 2.46957 1 3 1H8L10 4H19C19.5304 4 20.0391 4.21071 20.4142 4.58579C20.7893 4.96086 21 5.46957 21 6V17Z\",\n                                        stroke: \"#ffca00\",\n                                        strokeWidth: \"2\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 598,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    textAlign: \"center\",\n                                    margin: 0,\n                                    fontSize: \"16px\"\n                                },\n                                children: selectedArticle?.isArchived ? t(\"global:confirmUnarchiveArticle\") : t(\"global:confirmArchiveArticle\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 615,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 597,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"dialog-actions\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                text: selectedArticle?.isArchived ? t(\"global:unarchive\") : t(\"global:archive\"),\n                                className: \"btn-popup\",\n                                leftIcon: true,\n                                onClick: handleArchiveConfirm\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 622,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                text: t(\"global:cancel\"),\n                                leftIcon: true,\n                                className: \"btn-outlined-popup\",\n                                onClick: handleDialogClose\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 632,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 621,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 559,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                id: \"toggle\",\n                open: openDeleteDialog,\n                onClose: handleDialogClose,\n                \"aria-labelledby\": \"delete-dialog-title\",\n                className: \"dialog-paper\",\n                sx: {\n                    \"& .MuiPaper-root\": {\n                        background: \"linear-gradient(#0b3051 0%, #234791 100%) !important\",\n                        color: \"#f8f8f8 !important\",\n                        borderBottom: \"transparent !important\",\n                        borderRadius: \"0px !important\",\n                        boxShadow: \"transparent !important\"\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        sx: {\n                            m: 0,\n                            p: 2\n                        },\n                        id: \"delete-dialog-title\",\n                        children: [\n                            t(\"global:delete\"),\n                            \" \",\n                            t(\"global:article\"),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                \"aria-label\": \"close\",\n                                onClick: handleDialogClose,\n                                style: {\n                                    position: \"absolute\",\n                                    right: 8,\n                                    top: 8,\n                                    background: \"transparent\",\n                                    border: \"none\",\n                                    color: \"#f8f8f8\",\n                                    cursor: \"pointer\",\n                                    fontSize: \"20px\"\n                                },\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 660,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 658,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        dividers: true,\n                        className: \"dialog-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: \"center\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"48\",\n                                    height: \"48\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"none\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M3 6H5H21M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19ZM10 11V17M14 11V17\",\n                                        stroke: \"#ff4444\",\n                                        strokeWidth: \"2\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 678,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    textAlign: \"center\",\n                                    margin: 0,\n                                    fontSize: \"16px\"\n                                },\n                                children: t(\"global:confirmDeleteArticle\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 695,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 677,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"dialog-actions\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                text: t(\"global:delete\"),\n                                className: \"btn-popup\",\n                                leftIcon: true,\n                                onClick: handleDeleteConfirm\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 700,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                text: t(\"global:cancel\"),\n                                leftIcon: true,\n                                className: \"btn-outlined-popup\",\n                                onClick: handleDialogClose\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 706,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 699,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 642,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ListArticles, \"5YZ611sJ6OtD1N+zmncpv3mb7mI=\", false, function() {\n    return [\n        _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useDeleteArticle,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetArticlesDashboard\n    ];\n});\n_c = ListArticles;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ListArticles);\nvar _c;\n$RefreshReg$(_c, \"ListArticles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/ListArticles.jsx\n"));

/***/ })

});