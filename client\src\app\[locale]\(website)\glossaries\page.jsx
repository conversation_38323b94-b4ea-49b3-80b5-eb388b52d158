import banner from "@/assets/images/website/banner/Pentabell-joinUs.webp";
import GlossaryBanner from "@/components/sections/GlossaryBanner.jsx";
import initTranslations from "@/app/i18n";
import { axiosGetJsonSSR } from "@/config/axios";
import GlossaryListWebsite from "@/features/glossary/component/GlossariesListWebsite";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }glossaries/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/glossaries/`,
    en: `https://www.pentabell.com/glossaries/`,
    "x-default": `https://www.pentabell.com/glossaries/`,
  };

  const { t } = await initTranslations(locale, ["joinUs"]);

  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/glossaries`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("joinUs:metaTitle"),
    description: t("joinUs:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

async function page({ searchParams, params: { locale } }) {
  const { t } = await initTranslations(locale, ["glossary", "global"]);

  // Validate and sanitize search parameters
  const searchWord = searchParams?.word
    ? String(searchParams.word).trim().slice(0, 100)
    : "";

  let glossaries = {};
  let letters = [];
  let error = null;
  let isSearchResult = false;

  try {
    // Enhanced API call with timeout and error handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const res = await axiosGetJsonSSR.get("/glossaries", {
      params: {
        dashboard: "false",
        language: locale || "en",
        word: searchWord,
      },
      signal: controller.signal,
      timeout: 10000,
    });

    clearTimeout(timeoutId);

    // Validate response structure
    if (res?.data && typeof res.data === "object") {
      glossaries = res.data;
      letters = Object.keys(glossaries).filter(
        (key) => Array.isArray(glossaries[key]) && glossaries[key].length > 0
      );
      isSearchResult = Boolean(searchWord);
    } else {
      throw new Error("Invalid response format");
    }
  } catch (err) {
    console.error("Error fetching glossaries:", err);
    error = err.message || "Failed to load glossaries";

    // Set empty state
    glossaries = {};
    letters = [];
  }

  // Determine if we have content to display
  const hasContent = letters.length > 0;
  const isEmpty = !hasContent && !error;
  const isEmptySearch = !hasContent && isSearchResult;

  return (
    <>
      <GlossaryBanner
        altImg={"Join Us, Find Careers and Jobs at Pentabell"}
        bannerImg={banner}
        height={"100vh"}
        letters={letters}
        searchWord={searchWord}
        hasContent={hasContent}
      />
      <GlossaryListWebsite
        glossaries={glossaries}
        locale={locale}
        error={error}
        isEmpty={isEmpty}
        isEmptySearch={isEmptySearch}
        searchWord={searchWord}
        t={t}
      />
    </>
  );
}

export default page;
