import banner from "@/assets/images/website/banner/Pentabell-joinUs.webp";
import GlossaryBanner from "@/components/sections/GlossaryBanner.jsx";
import initTranslations from "@/app/i18n";
import { axiosGetJsonSSR } from "@/config/axios";
import GlossaryListWebsite from "@/features/glossary/component/GlossariesListWebsite";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }glossaries/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/glossaries/`,
    en: `https://www.pentabell.com/glossaries/`,
    "x-default": `https://www.pentabell.com/glossaries/`,
  };

  const { t } = await initTranslations(locale, ["joinUs"]);

  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/glossaries`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("joinUs:metaTitle"),
    description: t("joinUs:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

async function page({ searchParams, params: { locale } }) {
  const res = await axiosGetJsonSSR.get("/glossaries", {
    params: {
      dashboard: "false",
      language: locale || "en",
      word: searchParams?.word || "",
    },
  });

  const glossaries = res.data;

  const letters = Object.keys(glossaries);

  return (
    <>
      <GlossaryBanner
        altImg={"Join Us, Find Careers and Jobs at Pentabell"}
        bannerImg={banner}
        height={"100vh"}
        letters={letters}
      />
      <GlossaryListWebsite glossaries={glossaries} locale={locale} />
    </>
  );
}

export default page;
