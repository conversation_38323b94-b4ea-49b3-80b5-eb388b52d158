"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,Description,PictureAsPdf!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/PictureAsPdf.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,Description,PictureAsPdf!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,Description,PictureAsPdf!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,Description,PictureAsPdf!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Add custom styles for drag state\nconst dragActiveStyles = `\n  .file-labels.drag-active {\n    border-color: #1976d2 !important;\n    background-color: rgba(25, 118, 210, 0.04) !important;\n  }\n  .file-labels.disabled {\n    cursor: not-allowed !important;\n    opacity: 0.6 !important;\n  }\n`;\n// Inject styles if not already present\nif (typeof document !== \"undefined\" && !document.getElementById(\"document-importer-styles\")) {\n    const styleSheet = document.createElement(\"style\");\n    styleSheet.id = \"document-importer-styles\";\n    styleSheet.textContent = dragActiveStyles;\n    document.head.appendChild(styleSheet);\n}\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewOpen, setPreviewOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [extractedData, setExtractedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Supported file types\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes(\"pdf\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            color: \"error\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 74,\n            columnNumber: 42\n        }, undefined);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            color: \"primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 75,\n            columnNumber: 12\n        }, undefined);\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        // Create a temporary DOM element to parse HTML\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        // Extract potential title (first h1, h2, or strong text)\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        // Extract first paragraph as potential description\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        // Extract keywords from headings and strong text\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords: keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement(function(image) {\n                        return image.read(\"base64\").then(function(imageBuffer) {\n                            return {\n                                src: \"data:\" + image.contentType + \";base64,\" + imageBuffer\n                            };\n                        });\n                    })\n                }\n            });\n            setProgress(75);\n            // Clean up the HTML content\n            let cleanContent = result.value.replace(/<p><\\/p>/g, \"\") // Remove empty paragraphs\n            .replace(/\\s+/g, \" \") // Normalize whitespace\n            .trim();\n            // Extract metadata\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata: metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            // Convert plain text to basic HTML\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata: metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            setExtractedData(result);\n            setSuccess(true);\n            // Show preview dialog\n            setPreviewOpen(true);\n        } catch (err) {\n            console.error(\"File processing error:\", err);\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) {\n            processFile(acceptedFiles[0]);\n        }\n    }, []);\n    const { getRootProps, getInputProps, isDragActive, acceptedFiles } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing\n    });\n    const handleApplyContent = ()=>{\n        if (extractedData) {\n            onContentExtracted(extractedData.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(extractedData.metadata);\n            }\n            setPreviewOpen(false);\n            setExtractedData(null);\n        }\n    };\n    const handleClosePreview = ()=>{\n        setPreviewOpen(false);\n        setExtractedData(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        sx: {\n            mb: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: [\n                    t(\"createArticle:importDocument\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                ...getRootProps(),\n                sx: {\n                    p: 3,\n                    border: \"2px dashed\",\n                    borderColor: isDragActive ? \"primary.main\" : \"grey.300\",\n                    backgroundColor: isDragActive ? \"action.hover\" : \"background.paper\",\n                    cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\",\n                    textAlign: \"center\",\n                    transition: \"all 0.3s ease\",\n                    opacity: disabled || isProcessing ? 0.6 : 1,\n                    \"&:hover\": {\n                        borderColor: \"primary.main\",\n                        backgroundColor: \"action.hover\"\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ...getInputProps()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UploadIcon, {\n                        sx: {\n                            fontSize: 48,\n                            color: \"primary.main\",\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:dragDropOrClick\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        sx: {\n                            mb: 2\n                        },\n                        children: [\n                            t(\"createArticle:supportedFormats\"),\n                            \": .docx, .doc, .txt\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            gap: 1,\n                            flexWrap: \"wrap\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 23\n                                }, void 0),\n                                label: \"Word (.docx)\",\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 23\n                                }, void 0),\n                                label: \"Word (.doc)\",\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 23\n                                }, void 0),\n                                label: \"Text (.txt)\",\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        gutterBottom: true,\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 300,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, undefined),\n            success && !previewOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 315,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                open: previewOpen,\n                onClose: handleClosePreview,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"h6\",\n                                    children: t(\"createArticle:previewExtractedContent\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    onClick: handleClosePreview,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        children: extractedData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: [\n                                extractedData.metadata.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        mb: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedTitle\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: extractedData.metadata.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 17\n                                }, undefined),\n                                extractedData.metadata.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        mb: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedDescription\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body2\",\n                                            children: extractedData.metadata.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"subtitle2\",\n                                    color: \"primary\",\n                                    sx: {\n                                        mb: 1\n                                    },\n                                    children: [\n                                        t(\"createArticle:extractedContent\"),\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        maxHeight: 400,\n                                        overflow: \"auto\",\n                                        border: \"1px solid\",\n                                        borderColor: \"grey.300\",\n                                        p: 2,\n                                        borderRadius: 1,\n                                        backgroundColor: \"grey.50\"\n                                    },\n                                    dangerouslySetInnerHTML: {\n                                        __html: extractedData.content\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, undefined),\n                                extractedData.warnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    severity: \"warning\",\n                                    sx: {\n                                        mt: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            children: [\n                                                t(\"createArticle:conversionWarnings\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: extractedData.warnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: warning.message\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 350,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                onClick: handleClosePreview,\n                                children: t(\"global:cancel\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                onClick: handleApplyContent,\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 24\n                                }, void 0),\n                                children: t(\"createArticle:applyContent\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"e7V5N1Q8+U90zRa31VhbBxq3qFc=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});