import { Schema, model, Types } from 'mongoose';

import { Language, robotsMeta } from '@/utils/helpers/constants';
import { CategoryGuide, CategoryGuideI } from './guide.category.interface';

const categoryGuideSchema = new Schema<CategoryGuide>({
    language: { type: String, enum: Language, required: true },
    name: { type: String },
    url: { type: String, unique: true },
    description: { type: String },
    canonical: { type: String },
    guides: [{ type: Types.ObjectId, ref: 'Guide' }],
    metaTitle: { type: String, default: '' },
    metaDescription: { type: String, default: '' },
    createdAt: { type: Date, required: true, default: Date.now },
    updatedAt: { type: Date, required: true, default: Date.now },
});

const categorySchema = new Schema<CategoryGuideI>(
    {
        categoryguide: { type: [categoryGuideSchema] },
        robotsMeta: {
            type: String,
            enum: robotsMeta,
            default: robotsMeta.index,
        },
    },

    {
        timestamps: true,
        toJSON: {
            transform: function (doc, ret) {
                delete ret.__v;
            },
        },
    },
);

export default model<CategoryGuideI>('CategoryGuide', categorySchema);
