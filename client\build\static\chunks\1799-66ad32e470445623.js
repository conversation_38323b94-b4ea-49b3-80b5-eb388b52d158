"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1799],{81799:function(e,t,r){r.d(t,{Z:function(){return U}});var o,n,a=r(2265),l=r(61994),i=r(20801),p=r(82590),s=r(64910),u=r(48467),c=r(16210),d=r(76301),g=r(37053),f=r(85657),h=r(94143),m=r(50738);function b(e){return(0,m.ZP)("MuiListSubheader",e)}(0,h.Z)("MuiListSubheader",["root","colorPrimary","colorInherit","gutters","inset","sticky"]);var v=r(57437);let x=e=>{let{classes:t,color:r,disableGutters:o,inset:n,disableSticky:a}=e,l={root:["root","default"!==r&&`color${(0,f.Z)(r)}`,!o&&"gutters",n&&"inset",!a&&"sticky"]};return(0,i.Z)(l,b,t)},y=(0,c.ZP)("li",{name:"MuiListSubheader",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,"default"!==r.color&&t[`color${(0,f.Z)(r.color)}`],!r.disableGutters&&t.gutters,r.inset&&t.inset,!r.disableSticky&&t.sticky]}})((0,d.Z)(e=>{let{theme:t}=e;return{boxSizing:"border-box",lineHeight:"48px",listStyle:"none",color:(t.vars||t).palette.text.secondary,fontFamily:t.typography.fontFamily,fontWeight:t.typography.fontWeightMedium,fontSize:t.typography.pxToRem(14),variants:[{props:{color:"primary"},style:{color:(t.vars||t).palette.primary.main}},{props:{color:"inherit"},style:{color:"inherit"}},{props:e=>{let{ownerState:t}=e;return!t.disableGutters},style:{paddingLeft:16,paddingRight:16}},{props:e=>{let{ownerState:t}=e;return t.inset},style:{paddingLeft:72}},{props:e=>{let{ownerState:t}=e;return!t.disableSticky},style:{position:"sticky",top:0,zIndex:1,backgroundColor:(t.vars||t).palette.background.paper}}]}})),$=a.forwardRef(function(e,t){let r=(0,g.i)({props:e,name:"MuiListSubheader"}),{className:o,color:n="default",component:a="li",disableGutters:i=!1,disableSticky:p=!1,inset:s=!1,...u}=r,c={...r,color:n,component:a,disableGutters:i,disableSticky:p,inset:s},d=x(c);return(0,v.jsx)(y,{as:a,className:(0,l.Z)(d.root,o),ref:t,ownerState:c,...u})});$&&($.muiSkipListHighlight=!0);var Z=r(53410),k=r(59832),P=r(67571),I=r(86507),S=r(60971),C=r(58108),R=r(2386),A=r(14625),w=r(36674);function L(e){return(0,m.ZP)("MuiAutocomplete",e)}let O=(0,h.Z)("MuiAutocomplete",["root","expanded","fullWidth","focused","focusVisible","tag","tagSizeSmall","tagSizeMedium","hasPopupIcon","hasClearIcon","inputRoot","input","inputFocused","endAdornment","clearIndicator","popupIndicator","popupIndicatorOpen","popper","popperDisablePortal","paper","listbox","loading","noOptions","option","groupLabel","groupUl"]);var M=r(79114);let D=e=>{let{classes:t,disablePortal:r,expanded:o,focused:n,fullWidth:a,hasClearIcon:l,hasPopupIcon:p,inputFocused:s,popupOpen:u,size:c}=e,d={root:["root",o&&"expanded",n&&"focused",a&&"fullWidth",l&&"hasClearIcon",p&&"hasPopupIcon"],inputRoot:["inputRoot"],input:["input",s&&"inputFocused"],tag:["tag",`tagSize${(0,f.Z)(c)}`],endAdornment:["endAdornment"],clearIndicator:["clearIndicator"],popupIndicator:["popupIndicator",u&&"popupIndicatorOpen"],popper:["popper",r&&"popperDisablePortal"],paper:["paper"],listbox:["listbox"],loading:["loading"],noOptions:["noOptions"],option:["option"],groupLabel:["groupLabel"],groupUl:["groupUl"]};return(0,i.Z)(d,L,t)},N=(0,c.ZP)("div",{name:"MuiAutocomplete",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e,{fullWidth:o,hasClearIcon:n,hasPopupIcon:a,inputFocused:l,size:i}=r;return[{[`& .${O.tag}`]:t.tag},{[`& .${O.tag}`]:t[`tagSize${(0,f.Z)(i)}`]},{[`& .${O.inputRoot}`]:t.inputRoot},{[`& .${O.input}`]:t.input},{[`& .${O.input}`]:l&&t.inputFocused},t.root,o&&t.fullWidth,a&&t.hasPopupIcon,n&&t.hasClearIcon]}})({[`&.${O.focused} .${O.clearIndicator}`]:{visibility:"visible"},"@media (pointer: fine)":{[`&:hover .${O.clearIndicator}`]:{visibility:"visible"}},[`& .${O.tag}`]:{margin:3,maxWidth:"calc(100% - 6px)"},[`& .${O.inputRoot}`]:{[`.${O.hasPopupIcon}&, .${O.hasClearIcon}&`]:{paddingRight:30},[`.${O.hasPopupIcon}.${O.hasClearIcon}&`]:{paddingRight:56},[`& .${O.input}`]:{width:0,minWidth:30}},[`& .${I.Z.root}`]:{paddingBottom:1,"& .MuiInput-input":{padding:"4px 4px 4px 0px"}},[`& .${I.Z.root}.${S.Z.sizeSmall}`]:{[`& .${I.Z.input}`]:{padding:"2px 4px 3px 0"}},[`& .${C.Z.root}`]:{padding:9,[`.${O.hasPopupIcon}&, .${O.hasClearIcon}&`]:{paddingRight:39},[`.${O.hasPopupIcon}.${O.hasClearIcon}&`]:{paddingRight:65},[`& .${O.input}`]:{padding:"7.5px 4px 7.5px 5px"},[`& .${O.endAdornment}`]:{right:9}},[`& .${C.Z.root}.${S.Z.sizeSmall}`]:{paddingTop:6,paddingBottom:6,paddingLeft:6,[`& .${O.input}`]:{padding:"2.5px 4px 2.5px 8px"}},[`& .${R.Z.root}`]:{paddingTop:19,paddingLeft:8,[`.${O.hasPopupIcon}&, .${O.hasClearIcon}&`]:{paddingRight:39},[`.${O.hasPopupIcon}.${O.hasClearIcon}&`]:{paddingRight:65},[`& .${R.Z.input}`]:{padding:"7px 4px"},[`& .${O.endAdornment}`]:{right:9}},[`& .${R.Z.root}.${S.Z.sizeSmall}`]:{paddingBottom:1,[`& .${R.Z.input}`]:{padding:"2.5px 4px"}},[`& .${S.Z.hiddenLabel}`]:{paddingTop:8},[`& .${R.Z.root}.${S.Z.hiddenLabel}`]:{paddingTop:0,paddingBottom:0,[`& .${O.input}`]:{paddingTop:16,paddingBottom:17}},[`& .${R.Z.root}.${S.Z.hiddenLabel}.${S.Z.sizeSmall}`]:{[`& .${O.input}`]:{paddingTop:8,paddingBottom:9}},[`& .${O.input}`]:{flexGrow:1,textOverflow:"ellipsis",opacity:0},variants:[{props:{fullWidth:!0},style:{width:"100%"}},{props:{size:"small"},style:{[`& .${O.tag}`]:{margin:2,maxWidth:"calc(100% - 4px)"}}},{props:{inputFocused:!0},style:{[`& .${O.input}`]:{opacity:1}}},{props:{multiple:!0},style:{[`& .${O.inputRoot}`]:{flexWrap:"wrap"}}}]}),T=(0,c.ZP)("div",{name:"MuiAutocomplete",slot:"EndAdornment",overridesResolver:(e,t)=>t.endAdornment})({position:"absolute",right:0,top:"50%",transform:"translate(0, -50%)"}),j=(0,c.ZP)(k.Z,{name:"MuiAutocomplete",slot:"ClearIndicator",overridesResolver:(e,t)=>t.clearIndicator})({marginRight:-2,padding:4,visibility:"hidden"}),z=(0,c.ZP)(k.Z,{name:"MuiAutocomplete",slot:"PopupIndicator",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.popupIndicator,r.popupOpen&&t.popupIndicatorOpen]}})({padding:2,marginRight:-2,variants:[{props:{popupOpen:!0},style:{transform:"rotate(180deg)"}}]}),E=(0,c.ZP)(u.Z,{name:"MuiAutocomplete",slot:"Popper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{[`& .${O.option}`]:t.option},t.popper,r.disablePortal&&t.popperDisablePortal]}})((0,d.Z)(e=>{let{theme:t}=e;return{zIndex:(t.vars||t).zIndex.modal,variants:[{props:{disablePortal:!0},style:{position:"absolute"}}]}})),F=(0,c.ZP)(Z.Z,{name:"MuiAutocomplete",slot:"Paper",overridesResolver:(e,t)=>t.paper})((0,d.Z)(e=>{let{theme:t}=e;return{...t.typography.body1,overflow:"auto"}})),H=(0,c.ZP)("div",{name:"MuiAutocomplete",slot:"Loading",overridesResolver:(e,t)=>t.loading})((0,d.Z)(e=>{let{theme:t}=e;return{color:(t.vars||t).palette.text.secondary,padding:"14px 16px"}})),W=(0,c.ZP)("div",{name:"MuiAutocomplete",slot:"NoOptions",overridesResolver:(e,t)=>t.noOptions})((0,d.Z)(e=>{let{theme:t}=e;return{color:(t.vars||t).palette.text.secondary,padding:"14px 16px"}})),V=(0,c.ZP)("ul",{name:"MuiAutocomplete",slot:"Listbox",overridesResolver:(e,t)=>t.listbox})((0,d.Z)(e=>{let{theme:t}=e;return{listStyle:"none",margin:0,padding:"8px 0",maxHeight:"40vh",overflow:"auto",position:"relative",[`& .${O.option}`]:{minHeight:48,display:"flex",overflow:"hidden",justifyContent:"flex-start",alignItems:"center",cursor:"pointer",paddingTop:6,boxSizing:"border-box",outline:"0",WebkitTapHighlightColor:"transparent",paddingBottom:6,paddingLeft:16,paddingRight:16,[t.breakpoints.up("sm")]:{minHeight:"auto"},[`&.${O.focused}`]:{backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},'&[aria-disabled="true"]':{opacity:(t.vars||t).palette.action.disabledOpacity,pointerEvents:"none"},[`&.${O.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},'&[aria-selected="true"]':{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,p.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${O.focused}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,p.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:(t.vars||t).palette.action.selected}},[`&.${O.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,p.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}}}}})),q=(0,c.ZP)($,{name:"MuiAutocomplete",slot:"GroupLabel",overridesResolver:(e,t)=>t.groupLabel})((0,d.Z)(e=>{let{theme:t}=e;return{backgroundColor:(t.vars||t).palette.background.paper,top:-8}})),B=(0,c.ZP)("ul",{name:"MuiAutocomplete",slot:"GroupUl",overridesResolver:(e,t)=>t.groupUl})({padding:0,[`& .${O.option}`]:{paddingLeft:24}});var U=a.forwardRef(function(e,t){let r;let i=(0,g.i)({props:e,name:"MuiAutocomplete"}),{autoComplete:p=!1,autoHighlight:c=!1,autoSelect:d=!1,blurOnSelect:f=!1,ChipProps:h,className:m,clearIcon:b=o||(o=(0,v.jsx)(A.Z,{fontSize:"small"})),clearOnBlur:x=!i.freeSolo,clearOnEscape:y=!1,clearText:$="Clear",closeText:k="Close",componentsProps:I,defaultValue:S=i.multiple?[]:null,disableClearable:C=!1,disableCloseOnSelect:R=!1,disabled:L=!1,disabledItemsFocusable:O=!1,disableListWrap:U=!1,disablePortal:G=!1,filterOptions:K,filterSelectedOptions:_=!1,forcePopupIcon:J="auto",freeSolo:Q=!1,fullWidth:X=!1,getLimitTagsText:Y=e=>`+${e}`,getOptionDisabled:ee,getOptionKey:et,getOptionLabel:er,isOptionEqualToValue:eo,groupBy:en,handleHomeEndKeys:ea=!i.freeSolo,id:el,includeInputInList:ei=!1,inputValue:ep,limitTags:es=-1,ListboxComponent:eu,ListboxProps:ec,loading:ed=!1,loadingText:eg="Loading…",multiple:ef=!1,noOptionsText:eh="No options",onChange:em,onClose:eb,onHighlightChange:ev,onInputChange:ex,onOpen:ey,open:e$,openOnFocus:eZ=!1,openText:ek="Open",options:eP,PaperComponent:eI,PopperComponent:eS,popupIcon:eC=n||(n=(0,v.jsx)(w.Z,{})),readOnly:eR=!1,renderGroup:eA,renderInput:ew,renderOption:eL,renderTags:eO,selectOnFocus:eM=!i.freeSolo,size:eD="medium",slots:eN={},slotProps:eT={},value:ej,...ez}=i,{getRootProps:eE,getInputProps:eF,getInputLabelProps:eH,getPopupIndicatorProps:eW,getClearProps:eV,getTagProps:eq,getListboxProps:eB,getOptionProps:eU,value:eG,dirty:eK,expanded:e_,id:eJ,popupOpen:eQ,focused:eX,focusedTag:eY,anchorEl:e1,setAnchorEl:e0,inputValue:e5,groupedOptions:e6}=(0,s.Z)({...i,componentName:"Autocomplete"}),e4=!C&&!L&&eK&&!eR,e2=(!Q||!0===J)&&!1!==J,{onMouseDown:e9}=eF(),{ref:e3,...e7}=eB(),e8=er||(e=>e.label??e),te={...i,disablePortal:G,expanded:e_,focused:eX,fullWidth:X,getOptionLabel:e8,hasClearIcon:e4,hasPopupIcon:e2,inputFocused:-1===eY,popupOpen:eQ,size:eD},tt=D(te),tr={slots:{paper:eI,popper:eS,...eN},slotProps:{chip:h,listbox:ec,...I,...eT}},[to,tn]=(0,M.Z)("listbox",{elementType:V,externalForwardedProps:tr,ownerState:te,className:tt.listbox,additionalProps:e7,ref:e3}),[ta,tl]=(0,M.Z)("paper",{elementType:Z.Z,externalForwardedProps:tr,ownerState:te,className:tt.paper}),[ti,tp]=(0,M.Z)("popper",{elementType:u.Z,externalForwardedProps:tr,ownerState:te,className:tt.popper,additionalProps:{disablePortal:G,style:{width:e1?e1.clientWidth:null},role:"presentation",anchorEl:e1,open:eQ}});if(ef&&eG.length>0){let e=e=>({className:tt.tag,disabled:L,...eq(e)});r=eO?eO(eG,e,te):eG.map((t,r)=>{let{key:o,...n}=e({index:r});return(0,v.jsx)(P.Z,{label:e8(t),size:eD,...n,...tr.slotProps.chip},o)})}if(es>-1&&Array.isArray(r)){let e=r.length-es;!eX&&e>0&&(r=r.splice(0,es)).push((0,v.jsx)("span",{className:tt.tag,children:Y(e)},r.length))}let ts=eA||(e=>(0,v.jsxs)("li",{children:[(0,v.jsx)(q,{className:tt.groupLabel,ownerState:te,component:"div",children:e.group}),(0,v.jsx)(B,{className:tt.groupUl,ownerState:te,children:e.children})]},e.key)),tu=eL||((e,t)=>{let{key:r,...o}=e;return(0,v.jsx)("li",{...o,children:e8(t)},r)}),tc=(e,t)=>{let r=eU({option:e,index:t});return tu({...r,className:tt.option},e,{selected:r["aria-selected"],index:t,inputValue:e5},te)},td=tr.slotProps.clearIndicator,tg=tr.slotProps.popupIndicator;return(0,v.jsxs)(a.Fragment,{children:[(0,v.jsx)(N,{ref:t,className:(0,l.Z)(tt.root,m),ownerState:te,...eE(ez),children:ew({id:eJ,disabled:L,fullWidth:!0,size:"small"===eD?"small":void 0,InputLabelProps:eH(),InputProps:{ref:e0,className:tt.inputRoot,startAdornment:r,onMouseDown:e=>{e.target===e.currentTarget&&e9(e)},...(e4||e2)&&{endAdornment:(0,v.jsxs)(T,{className:tt.endAdornment,ownerState:te,children:[e4?(0,v.jsx)(j,{...eV(),"aria-label":$,title:$,ownerState:te,...td,className:(0,l.Z)(tt.clearIndicator,td?.className),children:b}):null,e2?(0,v.jsx)(z,{...eW(),disabled:L,"aria-label":eQ?k:ek,title:eQ?k:ek,ownerState:te,...tg,className:(0,l.Z)(tt.popupIndicator,tg?.className),children:eC}):null]})}},inputProps:{className:tt.input,disabled:L,readOnly:eR,...eF()}})}),e1?(0,v.jsx)(E,{as:ti,...tp,children:(0,v.jsxs)(F,{as:ta,...tl,children:[ed&&0===e6.length?(0,v.jsx)(H,{className:tt.loading,ownerState:te,children:eg}):null,0!==e6.length||Q||ed?null:(0,v.jsx)(W,{className:tt.noOptions,ownerState:te,role:"presentation",onMouseDown:e=>{e.preventDefault()},children:eh}),e6.length>0?(0,v.jsx)(to,{as:eu,...tn,children:e6.map((e,t)=>en?ts({key:e.key,group:e.group,children:e.options.map((t,r)=>tc(t,e.index+r))}):tc(e,t))}):null]})}):null]})})},14625:function(e,t,r){r(2265);var o=r(32464),n=r(57437);t.Z=(0,o.Z)((0,n.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},64910:function(e,t,r){r.d(t,{D:function(){return u}});var o=r(2265),n=r(53025),a=r(38462),l=r(57893),i=r(8659),p=r(29419);function s(e){return e.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}function u(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{ignoreAccents:t=!0,ignoreCase:r=!0,limit:o,matchFrom:n="any",stringify:a,trim:l=!1}=e;return(e,i)=>{let{inputValue:p,getOptionLabel:u}=i,c=l?p.trim():p;r&&(c=c.toLowerCase()),t&&(c=s(c));let d=c?e.filter(e=>{let o=(a||u)(e);return r&&(o=o.toLowerCase()),t&&(o=s(o)),"start"===n?o.startsWith(c):o.includes(c)}):e;return"number"==typeof o?d.slice(0,o):d}}let c=u(),d=e=>null!==e.current&&e.current.parentElement?.contains(document.activeElement),g=[];function f(e,t,r){if(t||null==e)return"";let o=r(e);return"string"==typeof o?o:""}t.Z=function(e){let{unstable_isActiveElementInListbox:t=d,unstable_classNamePrefix:r="Mui",autoComplete:s=!1,autoHighlight:u=!1,autoSelect:h=!1,blurOnSelect:m=!1,clearOnBlur:b=!e.freeSolo,clearOnEscape:v=!1,componentName:x="useAutocomplete",defaultValue:y=e.multiple?g:null,disableClearable:$=!1,disableCloseOnSelect:Z=!1,disabled:k,disabledItemsFocusable:P=!1,disableListWrap:I=!1,filterOptions:S=c,filterSelectedOptions:C=!1,freeSolo:R=!1,getOptionDisabled:A,getOptionKey:w,getOptionLabel:L=e=>e.label??e,groupBy:O,handleHomeEndKeys:M=!e.freeSolo,id:D,includeInputInList:N=!1,inputValue:T,isOptionEqualToValue:j=(e,t)=>e===t,multiple:z=!1,onChange:E,onClose:F,onHighlightChange:H,onInputChange:W,onOpen:V,open:q,openOnFocus:B=!1,options:U,readOnly:G=!1,selectOnFocus:K=!e.freeSolo,value:_}=e,J=(0,n.Z)(D),Q=L;Q=e=>{let t=L(e);return"string"!=typeof t?String(t):t};let X=o.useRef(!1),Y=o.useRef(!0),ee=o.useRef(null),et=o.useRef(null),[er,eo]=o.useState(null),[en,ea]=o.useState(-1),el=u?0:-1,ei=o.useRef(el),ep=o.useRef(f(y??_,z,Q)).current,[es,eu]=(0,a.Z)({controlled:_,default:y,name:x}),[ec,ed]=(0,a.Z)({controlled:T,default:ep,name:x,state:"inputValue"}),[eg,ef]=o.useState(!1),eh=o.useCallback((e,t,r)=>{if(!(z?es.length<t.length:null!==t)&&!b)return;let o=f(t,z,Q);ec!==o&&(ed(o),W&&W(e,o,r))},[Q,ec,z,W,ed,b,es]),[em,eb]=(0,a.Z)({controlled:q,default:!1,name:x,state:"open"}),[ev,ex]=o.useState(!0),ey=!z&&null!=es&&ec===Q(es),e$=em&&!G,eZ=e$?S(U.filter(e=>!(C&&(z?es:[es]).some(t=>null!==t&&j(e,t)))),{inputValue:ey&&ev?"":ec,getOptionLabel:Q}):[],ek=(0,l.Z)({filteredOptions:eZ,value:es,inputValue:ec});o.useEffect(()=>{let e=es!==ek.value;(!eg||e)&&(!R||e)&&eh(null,es,"reset")},[es,eh,eg,ek.value,R]);let eP=em&&eZ.length>0&&!G,eI=(0,i.Z)(e=>{-1===e?ee.current.focus():er.querySelector(`[data-tag-index="${e}"]`).focus()});o.useEffect(()=>{z&&en>es.length-1&&(ea(-1),eI(-1))},[es,z,en,eI]);let eS=(0,i.Z)(e=>{let{event:t,index:o,reason:n}=e;if(ei.current=o,-1===o?ee.current.removeAttribute("aria-activedescendant"):ee.current.setAttribute("aria-activedescendant",`${J}-option-${o}`),H&&["mouse","keyboard","touch"].includes(n)&&H(t,-1===o?null:eZ[o],n),!et.current)return;let a=et.current.querySelector(`[role="option"].${r}-focused`);a&&(a.classList.remove(`${r}-focused`),a.classList.remove(`${r}-focusVisible`));let l=et.current;if("listbox"!==et.current.getAttribute("role")&&(l=et.current.parentElement.querySelector('[role="listbox"]')),!l)return;if(-1===o){l.scrollTop=0;return}let i=et.current.querySelector(`[data-option-index="${o}"]`);if(i&&(i.classList.add(`${r}-focused`),"keyboard"===n&&i.classList.add(`${r}-focusVisible`),l.scrollHeight>l.clientHeight&&"mouse"!==n&&"touch"!==n)){let e=l.clientHeight+l.scrollTop,t=i.offsetTop+i.offsetHeight;t>e?l.scrollTop=t-l.clientHeight:i.offsetTop-i.offsetHeight*(O?1.3:0)<l.scrollTop&&(l.scrollTop=i.offsetTop-i.offsetHeight*(O?1.3:0))}}),eC=(0,i.Z)(e=>{let{event:t,diff:r,direction:o="next",reason:n}=e;if(!e$)return;let a=function(e,t){if(!et.current||e<0||e>=eZ.length)return -1;let r=e;for(;;){let o=et.current.querySelector(`[data-option-index="${r}"]`),n=!P&&(!o||o.disabled||"true"===o.getAttribute("aria-disabled"));if(o&&o.hasAttribute("tabindex")&&!n)return r;if((r="next"===t?(r+1)%eZ.length:(r-1+eZ.length)%eZ.length)===e)return -1}}((()=>{let e=eZ.length-1;if("reset"===r)return el;if("start"===r)return 0;if("end"===r)return e;let t=ei.current+r;return t<0?-1===t&&N?-1:I&&-1!==ei.current||Math.abs(r)>1?0:e:t>e?t===e+1&&N?-1:I||Math.abs(r)>1?e:0:t})(),o);if(eS({index:a,reason:n,event:t}),s&&"reset"!==r){if(-1===a)ee.current.value=ec;else{let e=Q(eZ[a]);ee.current.value=e,0===e.toLowerCase().indexOf(ec.toLowerCase())&&ec.length>0&&ee.current.setSelectionRange(ec.length,e.length)}}}),eR=()=>{var e;if(-1!==ei.current&&ek.filteredOptions&&ek.filteredOptions.length!==eZ.length&&ek.inputValue===ec&&(z?es.length===ek.value.length&&ek.value.every((e,t)=>Q(es[t])===Q(e)):((e=ek.value)?Q(e):"")===(es?Q(es):""))){let e=ek.filteredOptions[ei.current];if(e)return eZ.findIndex(t=>Q(t)===Q(e))}return -1},eA=o.useCallback(()=>{if(!e$)return;let e=eR();if(-1!==e){ei.current=e;return}let t=z?es[0]:es;if(0===eZ.length||null==t){eC({diff:"reset"});return}if(et.current){if(null!=t){let e=eZ[ei.current];if(z&&e&&-1!==es.findIndex(t=>j(e,t)))return;let r=eZ.findIndex(e=>j(e,t));-1===r?eC({diff:"reset"}):eS({index:r});return}if(ei.current>=eZ.length-1){eS({index:eZ.length-1});return}eS({index:ei.current})}},[eZ.length,!z&&es,C,eC,eS,e$,ec,z]),ew=(0,i.Z)(e=>{(0,p.Z)(et,e),e&&eA()});o.useEffect(()=>{eA()},[eA]);let eL=e=>{!em&&(eb(!0),ex(!0),V&&V(e))},eO=(e,t)=>{em&&(eb(!1),F&&F(e,t))},eM=(e,t,r,o)=>{if(z){if(es.length===t.length&&es.every((e,r)=>e===t[r]))return}else if(es===t)return;E&&E(e,t,r,o),eu(t)},eD=o.useRef(!1),eN=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"selectOption",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"options",n=r,a=t;if(z){let e=(a=Array.isArray(es)?es.slice():[]).findIndex(e=>j(t,e));-1===e?a.push(t):"freeSolo"!==o&&(a.splice(e,1),n="removeOption")}eh(e,a,n),eM(e,a,n,{option:t}),Z||e&&(e.ctrlKey||e.metaKey)||eO(e,n),(!0===m||"touch"===m&&eD.current||"mouse"===m&&!eD.current)&&ee.current.blur()},eT=(e,t)=>{if(!z)return;""===ec&&eO(e,"toggleInput");let r=en;-1===en?""===ec&&"previous"===t&&(r=es.length-1):((r+="next"===t?1:-1)<0&&(r=0),r===es.length&&(r=-1)),ea(r=function(e,t){if(-1===e)return -1;let r=e;for(;;){if("next"===t&&r===es.length||"previous"===t&&-1===r)return -1;let e=er.querySelector(`[data-tag-index="${r}"]`);if(e&&e.hasAttribute("tabindex")&&!e.disabled&&"true"!==e.getAttribute("aria-disabled"))return r;r+="next"===t?1:-1}}(r,t)),eI(r)},ej=e=>{X.current=!0,ed(""),W&&W(e,"","clear"),eM(e,z?[]:null,"clear")},ez=e=>t=>{if(e.onKeyDown&&e.onKeyDown(t),!t.defaultMuiPrevented&&(-1===en||["ArrowLeft","ArrowRight"].includes(t.key)||(ea(-1),eI(-1)),229!==t.which))switch(t.key){case"Home":e$&&M&&(t.preventDefault(),eC({diff:"start",direction:"next",reason:"keyboard",event:t}));break;case"End":e$&&M&&(t.preventDefault(),eC({diff:"end",direction:"previous",reason:"keyboard",event:t}));break;case"PageUp":t.preventDefault(),eC({diff:-5,direction:"previous",reason:"keyboard",event:t}),eL(t);break;case"PageDown":t.preventDefault(),eC({diff:5,direction:"next",reason:"keyboard",event:t}),eL(t);break;case"ArrowDown":t.preventDefault(),eC({diff:1,direction:"next",reason:"keyboard",event:t}),eL(t);break;case"ArrowUp":t.preventDefault(),eC({diff:-1,direction:"previous",reason:"keyboard",event:t}),eL(t);break;case"ArrowLeft":eT(t,"previous");break;case"ArrowRight":eT(t,"next");break;case"Enter":if(-1!==ei.current&&e$){let e=eZ[ei.current],r=!!A&&A(e);if(t.preventDefault(),r)return;eN(t,e,"selectOption"),s&&ee.current.setSelectionRange(ee.current.value.length,ee.current.value.length)}else R&&""!==ec&&!1===ey&&(z&&t.preventDefault(),eN(t,ec,"createOption","freeSolo"));break;case"Escape":e$?(t.preventDefault(),t.stopPropagation(),eO(t,"escape")):v&&(""!==ec||z&&es.length>0)&&(t.preventDefault(),t.stopPropagation(),ej(t));break;case"Backspace":if(z&&!G&&""===ec&&es.length>0){let e=-1===en?es.length-1:en,r=es.slice();r.splice(e,1),eM(t,r,"removeOption",{option:es[e]})}break;case"Delete":if(z&&!G&&""===ec&&es.length>0&&-1!==en){let e=es.slice();e.splice(en,1),eM(t,e,"removeOption",{option:es[en]})}}},eE=e=>{ef(!0),B&&!X.current&&eL(e)},eF=e=>{if(t(et)){ee.current.focus();return}ef(!1),Y.current=!0,X.current=!1,h&&-1!==ei.current&&e$?eN(e,eZ[ei.current],"blur"):h&&R&&""!==ec?eN(e,ec,"blur","freeSolo"):b&&eh(e,es,"blur"),eO(e,"blur")},eH=e=>{let t=e.target.value;ec!==t&&(ed(t),ex(!1),W&&W(e,t,"input")),""===t?$||z||eM(e,null,"clear"):eL(e)},eW=e=>{let t=Number(e.currentTarget.getAttribute("data-option-index"));ei.current!==t&&eS({event:e,index:t,reason:"mouse"})},eV=e=>{eS({event:e,index:Number(e.currentTarget.getAttribute("data-option-index")),reason:"touch"}),eD.current=!0},eq=e=>{let t=Number(e.currentTarget.getAttribute("data-option-index"));eN(e,eZ[t],"selectOption"),eD.current=!1},eB=e=>t=>{let r=es.slice();r.splice(e,1),eM(t,r,"removeOption",{option:es[e]})},eU=e=>{em?eO(e,"toggleInput"):eL(e)},eG=e=>{e.currentTarget.contains(e.target)&&e.target.getAttribute("id")!==J&&e.preventDefault()},eK=e=>{e.currentTarget.contains(e.target)&&(ee.current.focus(),K&&Y.current&&ee.current.selectionEnd-ee.current.selectionStart==0&&ee.current.select(),Y.current=!1)},e_=e=>{k||""!==ec&&em||eU(e)},eJ=R&&ec.length>0;eJ=eJ||(z?es.length>0:null!==es);let eQ=eZ;return O&&(eQ=eZ.reduce((e,t,r)=>{let o=O(t);return e.length>0&&e[e.length-1].group===o?e[e.length-1].options.push(t):e.push({key:r,index:r,group:o,options:[t]}),e},[])),k&&eg&&eF(),{getRootProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{...e,onKeyDown:ez(e),onMouseDown:eG,onClick:eK}},getInputLabelProps:()=>({id:`${J}-label`,htmlFor:J}),getInputProps:()=>({id:J,value:ec,onBlur:eF,onFocus:eE,onChange:eH,onMouseDown:e_,"aria-activedescendant":e$?"":null,"aria-autocomplete":s?"both":"list","aria-controls":eP?`${J}-listbox`:void 0,"aria-expanded":eP,autoComplete:"off",ref:ee,autoCapitalize:"none",spellCheck:"false",role:"combobox",disabled:k}),getClearProps:()=>({tabIndex:-1,type:"button",onClick:ej}),getPopupIndicatorProps:()=>({tabIndex:-1,type:"button",onClick:eU}),getTagProps:e=>{let{index:t}=e;return{key:t,"data-tag-index":t,tabIndex:-1,...!G&&{onDelete:eB(t)}}},getListboxProps:()=>({role:"listbox",id:`${J}-listbox`,"aria-labelledby":`${J}-label`,ref:ew,onMouseDown:e=>{e.preventDefault()}}),getOptionProps:e=>{let{index:t,option:r}=e,o=(z?es:[es]).some(e=>null!=e&&j(r,e)),n=!!A&&A(r);return{key:w?.(r)??Q(r),tabIndex:-1,role:"option",id:`${J}-option-${t}`,onMouseMove:eW,onClick:eq,onTouchStart:eV,"data-option-index":t,"aria-disabled":n,"aria-selected":o}},id:J,inputValue:ec,value:es,dirty:eJ,expanded:e$&&er,popupOpen:e$,focused:eg||-1!==en,anchorEl:er,setAnchorEl:eo,focusedTag:en,groupedOptions:eQ}}},57893:function(e,t,r){var o=r(2265);t.Z=e=>{let t=o.useRef({});return o.useEffect(()=>{t.current=e}),t.current}}}]);