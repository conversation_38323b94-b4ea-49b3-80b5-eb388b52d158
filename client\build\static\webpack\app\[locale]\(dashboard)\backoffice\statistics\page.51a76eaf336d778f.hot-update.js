"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/features/stats/ResumesChart.jsx":
/*!*********************************************!*\
  !*** ./src/features/stats/ResumesChart.jsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ResumesChart; },\n/* harmony export */   valueFormatter: function() { return /* binding */ valueFormatter; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _stats_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stats.hooks */ \"(app-pages-browser)/./src/features/stats/stats.hooks.jsx\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon.svg\");\n/* harmony import */ var _components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../components/charts/CustomMultiBarchart */ \"(app-pages-browser)/./src/components/charts/CustomMultiBarchart.jsx\");\n/* harmony import */ var _components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../components/charts/CustomPieChart */ \"(app-pages-browser)/./src/components/charts/CustomPieChart.jsx\");\n/* harmony import */ var _blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../blog/hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _charts_CommentByCategory__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./charts/CommentByCategory */ \"(app-pages-browser)/./src/features/stats/charts/CommentByCategory.jsx\");\n/* harmony import */ var _charts_UsersActivities__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./charts/UsersActivities */ \"(app-pages-browser)/./src/features/stats/charts/UsersActivities.jsx\");\n/* harmony import */ var _charts_ApplicationsByStatus__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./charts/ApplicationsByStatus */ \"(app-pages-browser)/./src/features/stats/charts/ApplicationsByStatus.jsx\");\n/* __next_internal_client_entry_do_not_use__ valueFormatter,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction valueFormatter(value) {\n    return `${value}`;\n}\nfunction ResumesChart() {\n    _s();\n    const getCategoriesLangEN = (0,_blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"en\");\n    const transformedCategories = getCategoriesLangEN?.data?.categories?.map((category)=>({\n            id: category.versionscategory[0]?.id,\n            name: category.versionscategory[0]?.name\n        })) || [];\n    /// filter user Activity ///\n    const [dateFromUser, setDateFromUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToUser, setDateToUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchUser, setSearchUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchActivity = ()=>{\n        setDateToActivity(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromActivity(\"2024-09-01\");\n        setSearchActivity(!searchActivity);\n    };\n    /// filter platform activity ///\n    const [dateFromPlatform, setDateFromPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToPlatform, setDateToPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchPlatform, setSearchPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchPlatform = ()=>{\n        setDateToPlatform(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromPlatform(\"2024-09-01\");\n        setSearchPlatform(!searchPlatform);\n    };\n    /// application filter pie chart ///\n    const [dateFromApplication, setDateFromApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToApplication, setDateToApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchApplication, setSearchApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchApplication = ()=>{\n        setDateToApplication(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromApplication(\"2024-09-01\");\n        setSearchApplication(!searchApplication);\n    };\n    /// article filter pie chart ///\n    const [dateFromArticle, setDateFromArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToArticle, setDateToArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchArticle, setSearchArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchArticles = ()=>{\n        setDateToArticle(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromArticle(\"2024-09-01\");\n        setFilteredCategories([]);\n        setApprove(true);\n        setCategories([]);\n        setSearchArticle(!searchArticle);\n    };\n    /// comment filter pie chart ///\n    const [filteredCategories, setFilteredCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [approve, setApprove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dateFromComment, setDateFromComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToComment, setDateToComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchComment, setSearchComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchComments = ()=>{\n        setDateToComment(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromComment(\"2024-09-01\");\n        setFilteredCategories([]);\n        setApprove(true);\n        setCategories([]);\n        setSearchComment(!searchComment);\n    };\n    /// opportunity filter pie chart ////\n    const [dateFromOpportunity, setDateFromOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToOpportunity, setDateToOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [opportunityType, setOpportunityType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [industry, setIndustry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchOpportunity, setSearchOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchOpportunity = ()=>{\n        setDateToOpportunity(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromOpportunity(\"2024-09-01\");\n        setOpportunityType(\"\");\n        setIndustry(\"\");\n        setSearchOpportunity(!searchOpportunity);\n    };\n    const getDataPieComments = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetCommentsStat)({\n        dateFrom: dateFromComment,\n        dateTo: dateToComment,\n        approve: approve,\n        categories: filteredCategories\n    });\n    const getDataPieArticles = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetArticlesStat)({\n        dateFrom: dateFromArticle,\n        dateTo: dateToArticle,\n        barChart: null\n    });\n    const getDataPieOpportunities = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetOpportunitiesStat)({\n        dateFrom: dateFromOpportunity,\n        dateTo: dateToOpportunity,\n        opportunityType,\n        industry,\n        barChart: null\n    });\n    const getDAtaPieApplications = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetApplicationsStat)({\n        dateFrom: dateFromApplication,\n        dateTo: dateToApplication,\n        barChart: null\n    });\n    const getDataUserActivity = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetUserStat)({\n        dateFrom: dateFromUser,\n        dateTo: dateToUser\n    });\n    const getDataPlatforActivity = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetPlatformStat)({\n        dateFrom: dateFromPlatform,\n        dateTo: dateToPlatform\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataUserActivity.refetch();\n    }, [\n        searchUser\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPlatforActivity.refetch();\n    }, [\n        searchPlatform\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieOpportunities.refetch();\n    }, [\n        searchOpportunity\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDAtaPieApplications.refetch();\n    }, [\n        searchApplication\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieArticles.refetch();\n    }, [\n        searchArticle\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieComments.refetch();\n    }, [\n        searchComment\n    ]);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const isTablet = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(theme.breakpoints.down(\"md\"));\n    const chartSettings1 = {\n        width: isMobile ? 290 : isTablet ? 500 : 580,\n        height: 250,\n        layout: \"vertical\"\n    };\n    if (getDataUserActivity.isLoading || getDataPlatforActivity.isLoading || getDataPieArticles.isLoading || getDataPieOpportunities.isLoading || getDAtaPieApplications.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n            lineNumber: 244,\n            columnNumber: 12\n        }, this);\n    }\n    const pieCharts = [\n        {\n            title: t(\"statsDash:applicationsByStatus\"),\n            dataset: getDAtaPieApplications?.data?.map((app)=>({\n                    label: app.status,\n                    value: app.totalApplications\n                })),\n            colors: [\n                \"#E97611\",\n                \"#018055\",\n                \"#D73232\"\n            ]\n        },\n        {\n            title: t(\"statsDash:articlesByVisibility\"),\n            dataset: getDataPieArticles?.data?.map((article)=>({\n                    label: article.visibility,\n                    value: article.totalArticles\n                })),\n            colors: [\n                \"#234791\",\n                \"#FFCA00\",\n                \"#006A67\"\n            ]\n        },\n        {\n            title: t(\"statsDash:opportunities\"),\n            dataset: getDataPieOpportunities?.data?.map((opp)=>({\n                    label: opp.visibility,\n                    value: opp.totalOpportunities\n                })),\n            colors: [\n                \"#234791\",\n                \"#D5E5FF\",\n                \"#227B94\"\n            ]\n        },\n        {\n            title: t(\"statsDash:commentsByCategory\"),\n            dataset: getDataPieComments?.data?.map((comment)=>({\n                    label: comment.category,\n                    value: comment.total\n                })) || [],\n            colors: [\n                \"#673ab7\",\n                \"#009688\",\n                \"#8bc34a\",\n                \"#ffc107\",\n                \"#ff9800\",\n                \"#ffc107\",\n                \"#3f51b5\",\n                \"#009688\",\n                \"#4caf50\",\n                \"#03a9f4\",\n                \"#ff9800\",\n                \"#8bc34a\",\n                \"#673ab7\"\n            ]\n        }\n    ];\n    const userAactivity = {\n        title: t(\"statsDash:usersActivities\"),\n        dataKey: [\n            \"login\",\n            \"register\",\n            \"resumes\",\n            \"applications\"\n        ],\n        dataset: getDataUserActivity?.data,\n        color: [\n            \"#30B0C7\",\n            \"#234791\",\n            \"#007AFF\",\n            \"#32ADE6\"\n        ]\n    };\n    const platformAactivity = {\n        title: t(\"statsDash:platformActivity\"),\n        dataKey: [\n            \"opportunities\",\n            \"articles\",\n            \"newsletters\",\n            \"contacts\"\n        ],\n        dataset: getDataPlatforActivity?.data,\n        color: [\n            \"#FFCC00\",\n            \"#FFA135\",\n            \"#FFD985\",\n            \"#FF7700\"\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"menu:statistics\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"stats\",\n                className: \"div-wrapper\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    container: true,\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_CommentByCategory__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                transformedCategories: transformedCategories,\n                                dateFromComment: dateFromComment,\n                                dateToComment: dateToComment,\n                                approve: approve,\n                                categories: categories,\n                                setCategories: setCategories,\n                                setFilteredCategories: setFilteredCategories,\n                                setSearchComment: setSearchComment,\n                                searchComment: searchComment,\n                                resetSearchComments: resetSearchComments,\n                                pieCharts: pieCharts,\n                                setApprove: setApprove,\n                                setDateFromComment: setDateFromComment,\n                                setDateToComment: setDateToComment\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_UsersActivities__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                dateFromUser: dateFromUser,\n                                dateToUser: dateToUser,\n                                searchUser: searchUser,\n                                setSearchUser: setSearchUser,\n                                resetSearchActivity: resetSearchActivity,\n                                userAactivity: userAactivity,\n                                chartSettings1: chartSettings1,\n                                setDateFromUser: setDateFromUser,\n                                setDateToUser: setDateToUser\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_ApplicationsByStatus__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                dateFromApplication: dateFromApplication,\n                                dateToApplication: dateToApplication,\n                                searchApplication: searchApplication,\n                                setSearchApplication: setSearchApplication,\n                                resetSearchApplication: resetSearchApplication,\n                                pieCharts: pieCharts,\n                                setDateFromApplication: setDateFromApplication,\n                                setDateToApplication: setDateToApplication\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"heading-h3\",\n                                            gutterBottom: true,\n                                            children: pieCharts[2].title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            elevation: 0,\n                                            disableGutters: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    \"aria-controls\": \"panel1bh-content\",\n                                                    id: \"panel1bh-header\",\n                                                    className: \"svg-accordion\",\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 33\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"label-pentabell\",\n                                                        children: t(\"statsDash:filters\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    elevation: 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        container: true,\n                                                        className: \"chart-grid\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"blue-text\",\n                                                                        children: [\n                                                                            t(\"statsDash:type\"),\n                                                                            \" :\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"select-pentabell blue-text\",\n                                                                        value: opportunityType || \"\",\n                                                                        defaultValue: \"\",\n                                                                        onChange: (event)=>setOpportunityType(event.target.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                selected: true,\n                                                                                disabled: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                                                    children: t(\"statsDash:opportunityType\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 398,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 392,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                children: t(\"statsDash:all\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 400,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            _utils_constants__WEBPACK_IMPORTED_MODULE_4__.OpportunityType.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"blue-text\",\n                                                                                    value: item,\n                                                                                    children: item\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 404,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                md: 6,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"blue-text\",\n                                                                        children: [\n                                                                            t(\"statsDash:industry\"),\n                                                                            \" :\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"select-pentabell blue-text\",\n                                                                        value: industry || \"\",\n                                                                        onChange: (event)=>setIndustry(event.target.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                selected: true,\n                                                                                disabled: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                                                    children: t(\"statsDash:industry\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 429,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 423,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                children: t(\"statsDash:all\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 431,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            _utils_constants__WEBPACK_IMPORTED_MODULE_4__.Industry.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"blue-text\",\n                                                                                    value: item,\n                                                                                    children: item\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 435,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    label: t(\"statsDash:fromDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateFromOpportunity,\n                                                                    onChange: (e)=>setDateFromOpportunity(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    label: t(\"statsDash:toDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateToOpportunity,\n                                                                    onChange: (e)=>setDateToOpportunity(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 3,\n                                                                sm: 1,\n                                                                md: 4,\n                                                                className: \"btns-filter dashboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                                                    onClick: resetSearchOpportunity\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 11,\n                                                                sm: 11,\n                                                                md: 8,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    text: t(\"statsDash:filter\"),\n                                                                    onClick: ()=>{\n                                                                        setSearchOpportunity(!searchOpportunity);\n                                                                    },\n                                                                    className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    donuts: false,\n                                                    chart: pieCharts[2]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                pieCharts[2].dataset?.some((item)=>item[\"value\"] > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"public-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:public\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"privateopportunity-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:private\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"draft-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:draft\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 360,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"barchartfilter-wrapper\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                container: true,\n                                                className: \"chart-grid\",\n                                                spacing: 1,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 12,\n                                                        md: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"heading-h3\",\n                                                            gutterBottom: true,\n                                                            children: platformAactivity.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 6,\n                                                        md: 2.5,\n                                                        xl: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            label: t(\"statsDash:fromDate\"),\n                                                            type: \"date\",\n                                                            value: dateFromPlatform,\n                                                            onChange: (e)=>setDateFromPlatform(e.target.value),\n                                                            fullWidth: true,\n                                                            InputLabelProps: {\n                                                                shrink: true\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 6,\n                                                        md: 2.5,\n                                                        xl: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            label: t(\"statsDash:toDate\"),\n                                                            type: \"date\",\n                                                            value: dateToPlatform,\n                                                            onChange: (e)=>setDateToPlatform(e.target.value),\n                                                            fullWidth: true,\n                                                            InputLabelProps: {\n                                                                shrink: true\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        item: true,\n                                                        xs: 2,\n                                                        sm: 1,\n                                                        md: 1.5,\n                                                        xl: 1,\n                                                        className: \"btns-filter dashboard\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            className: \"btn btn-outlined btn-refresh full-width\",\n                                                            onClick: resetSearchPlatform\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        item: true,\n                                                        xs: 10,\n                                                        sm: 11,\n                                                        md: 2.5,\n                                                        xl: 2,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            text: t(\"statsDash:filter\"),\n                                                            onClick: ()=>{\n                                                                setSearchPlatform(!searchPlatform);\n                                                            },\n                                                            className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                platformAactivity.dataset?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newopportunities-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newOpportunities\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 584,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"neswarticles-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newArticles\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newsletters-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newslettersSubscriptions\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newcontact-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 603,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newContacts\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 604,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    chart: platformAactivity,\n                                                    chartSettings: chartSettings1\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 524,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 523,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"heading-h3\",\n                                            gutterBottom: true,\n                                            children: pieCharts[1].title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            elevation: 0,\n                                            disableGutters: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    \"aria-controls\": \"panel1bh-content\",\n                                                    id: \"panel1bh-header\",\n                                                    className: \"svg-accordion\",\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 33\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"label-pentabell\",\n                                                        children: t(\"statsDash:filters\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    elevation: 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        container: true,\n                                                        className: \"chart-grid\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    label: t(\"statsDash:fromDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateFromArticle,\n                                                                    onChange: (e)=>setDateFromArticle(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    label: t(\"statsDash:toDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateToArticle,\n                                                                    onChange: (e)=>setDateToArticle(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 3,\n                                                                sm: 1,\n                                                                md: 4,\n                                                                className: \"btns-filter dashboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 666,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                                                    onClick: resetSearchArticles\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 11,\n                                                                sm: 11,\n                                                                md: 8,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    text: t(\"statsDash:filter\"),\n                                                                    onClick: ()=>{\n                                                                        setSearchArticle(!searchArticle);\n                                                                    },\n                                                                    className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 672,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    donuts: false,\n                                                    chart: pieCharts[1]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                pieCharts[1].dataset?.some((item)=>item[\"value\"] > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"public-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:public\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"privatearticles-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:private\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"draft-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:draft\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 704,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 620,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 619,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ResumesChart, \"5avvH6cEosRudI6naxYJ3BUU5WU=\", false, function() {\n    return [\n        _blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetCommentsStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetArticlesStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetOpportunitiesStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetApplicationsStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetUserStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetPlatformStat,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    ];\n});\n_c = ResumesChart;\nvar _c;\n$RefreshReg$(_c, \"ResumesChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/stats/ResumesChart.jsx\n"));

/***/ })

});