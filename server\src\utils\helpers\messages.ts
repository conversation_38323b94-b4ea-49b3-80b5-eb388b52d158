
export const MESSAGES = {
    GENERAL: {
        SERVER_ERROR: 'Erreur interne du serveur',
        INVALID_ID: 'Invalid MongoDB ID provided.',
    },
    SEOTAGS: {
        SEO_NOT_FOUND: 'This slug does not exist: ',
        SEO_ID_NOT_FOUND: 'This Seo tags does not exist: ',
        ALREADY_EXISTS: 'Seo Tags with this slug already exists',
        META_TITLE_EXISTS: "SeO mettaTitle already exists"
    },
    EVENT: {
        NOT_FOUND: 'This Event does not exist: ',
        EVENT_VERSION_NOT_FOUND: 'No event version found',
        DELETED: 'Event deleted successfully'
    },
    CANDIDATE_SHORTLIST: {
        OPPORTUNITY_DELETED: 'Opportunity deleted from shortList successfully',
        DELETED: 'Deleted from Favourite list successfully',
        DELETE_FAILED: 'Failed to delete favourite item.',
        OPPORTUNITY_NOT_FOUND: 'This opportunity does not exist',
        ALREADY_APPLIED: 'You have already added this opportunity to your shortList.',
        ID_REQUIRED: 'User ID is required.',
        FAVORITE_ID_REQUIRED: 'User ID is required.',
        TYPE_REQUIRED: 'User ID is required.',               
        NOT_FOUND: 'Favourite item not found',
        ALREADY_EXISTS: 'This item is already in your favorites.',
    },
    RECRUITER_SHORTLIST: {
        CANDIDATE_DELETED: 'Candidate deleted from shortList successfully',
        CANDIDATE_NOT_FOUND: 'Candidate not found',
        ALREADY_APPLIED: 'You have already added this candidate to your shortList.',
    },
    CLIENTS: {
        CLIENTS_IMPORTED: 'Clients have been imported successfully',
        CLIENT_DELETED: 'Client deleted successfully',
        CLIENT_NOT_FOUND: 'Client not found',
        ALREADY_EXIST: 'Client already exists !',
    },
    MANAGERS: {
        MANAGERS_IMPORTED: 'Managers have been imported successfully',
        MANAGER_DELETED: 'Manager deleted successfully',
        MANAGER_NOT_FOUND: 'Manager not found',
        ALREADY_EXIST: 'Manager already exists !',
    },
    OPPORTUNITY: {
        OPPORTUNITIES_IMPORTED: 'Opportunities have been imported successfully',
        DELETED: 'Opportunity deleted successfully',
        NOT_FOUND: 'Opportunity not found',
        NO_RECRUITER: "There's no recruiter with this id",
        MISSING_LANGUAGE_OR_ID: 'Missing language or opportunityId parameter.',
        MISSING_ARCHIVE_FLAG: 'Missing archive flag (true or false) in the request body.'
    },

    COMMENT: {
        DELETED: 'Comment deleted successfully',
        CREATED: 'Comment added to article successfully.',
        NOT_FOUND: 'Comment not found',
        MISSING_EMAIL_OR_FIRSTNAME: 'Email & First Name must be provided!',
        UNAUTHORIZED_DELETE: 'Unauthorized to delete this comment'
    },
    AUTH: {
        LOGOUT: 'User logged out successfully',
        RESET: 'Password reset link sent to ',
        UNAUTHORIZED: 'Unauthorized. Please Login',
        RESET_SUCCESSFUL: 'Password reset was successful',
        FORBIDDEN: 'Access Forbidden.',
        ACCOUNT_ACTIVATED: 'Account activated successfully.',
        ACCOUNT_ALREADY_ACTIVATED: 'Account already activated.',
        EMAIL_ALREADY_EXIST: 'Email already exist',
        USER_NOT_FOUND: 'User not found.',
        TOKEN_REQUIRED: 'Token est requis',
        PASSWORD_INCORRECT: 'Incorrect password',
        ARCHIVED: "You can't sign in because your account is archived",
        INVALID_TOKEN: 'Invalid token',
        INVALID_EMAIL: 'Invalid email',
        ACCOUNT_NOT_ACTIVATED: 'Your account is not activated yet. Please activate your account before signing in.',
        LOGIN_WITH_PROVIDER: 'Please login with your Google/Microsoft account!'
    },
    FILE: {
        UPLOADED: 'File uploaded successfully',
        ERROR_UPLOAD: 'Error uploading file',
        PROVIDE_FILE: 'Please provide a file!',
        UUID_EXIST:'uuid exist',
        INVALID_UUID_FORMAT:'Invalid UUID format',
        FILE_TYPE_NOT_ALLOWED: 'File type not allowed',
        NO_FILE: 'No file uploaded',
        DELETE_FAILED: 'Failed to delete file',
        INVALID_TYPE: 'Invalid file type. Please upload a JSON file.',
        NOT_FOUND: 'File not found',
        INVALID_RESSOURCE :'Invalid Ressource',
        INVALID_FORMAT :'Invalid file format',
        READ_ERROR: 'Error reading CV',
        READ_ERROR_FILE: 'Error reading File',
        CREATED_SUCCESS: 'File created successfully',
        ALREADY_EXISTS:'File already exists',
        DELETED: 'File deleted successfully',
        RESUME_MISSING_INFO: 'The resume lacks essential information (education, experience, or contact details)',
        DUPLICATE_CHECKSUM: 'A file with the same checksum already exists.',
    },
    USER: {
        EMAIL_UNIQUE: 'Email must be unique',
        EMAIL_INVALID:'Invalid email format',
        USER_NOT_FOUND: 'User not found',
        USER_CREATED: 'User created successfully',
        USER_UPDATED: 'User updated successfully',
        USER_DELETED: 'User deleted successfully',
        DEACTIVATE_SUCCESS: 'Deactivated successfully',
        DISARCHIVE_SUCCESS: 'Disarchive successfully',
        ID_EMAIL_REQUIRED:'User ID and email are required',
        EMAIL_ADDED:'Email added successfully',
        EMAIL_UPDATED:'Email updated successfully',
        EMAIL_DELETED:'',
        PHONE_ADDED:'Phone added successfully',
        PHONE_UPDATED:'Phone updated successfully',
        PHONE_DELETED:'Phone deleted successfully',
        PHONE_ALREADY_EXISTS:'Phone already exists',
        EMAIL_ALREADY_EXISTS:'  Email already exists',
        EMAIL_NOT_FOUND:'Email not found in the candidate'
   
    },
    CANDIDATE: {
        NOT_FOUND: 'Candidate not found',
        EMAILS: 'Emails must be as an array.',
        SKILLS: 'Skills must be as an array.',
        PHONES: 'Phones must be as an array.',
        ARCHIVED: 'Candidate successfully archived',
    },
    ALERT: {
        ADDED: 'Alert added successfully',
        UNAUTHORIZED_UPDATE: 'You are not authorized to update this alert',
        DISABLED: 'Alert disabled successfully',
        ENABLED: 'Alert enabled successfully',
        NOT_FOUND: 'Alert not found',
        ALREADY_EXIST: 'Alert already exist !',
        ACTIVE_ALERT: 'You can only have one active alert.',
        NO_USERS_FOR_CREATION: 'Aucun utilisateur trouvé pour la création d’alerte.'
    },
    CATEGORY: {
        NOT_FOUND: 'Category not found',
        ALREADY_EXIST: 'Category name already exist',
        URL_ALREADY_EXIST: "Url category already exists",
        TITLE_ALREADY_EXIST: "Title already exists",
        VERSION_NOT_EXIST: "No version category found.",
        MISSING_ID_OR_LANGUAGE: 'Missing category ID or language.',
    },
    ARTICLE: {
        NOT_FOUND: 'Article not found'
    },
    CANDIDATE_CERTIFICATION: {
        UPDATED: 'Certification updated successfully',
        DELETED: 'Certification deleted successfully',
        NOT_FOUND: 'Certification not found'
    },
    CANDIDATE_EDUCATION: {
        UPDATED: 'Education updated successfully',
        DELETED: 'Education deleted successfully',
        NOT_FOUND: 'Education not found'
    },
    CANDIDATE_EXPERIENCE: {
        UPDATED: 'Experience updated successfully',
        DELETED: 'Experience deleted successfully',
        NOT_FOUND: 'Experience not found'
    },
    CANDIDATE_INFORMATION: {
        INFORMATION_UPDATED: 'Information Updated',
        RESUME_DELETED: 'Resume deleted successfully',
        RESUME_NOTFOUND: 'Resume not found in candidate CV.'
    },
    CONTACT: {
        SENT_SUCCESS: 'Contact form sent successfully',
        NOT_FOUND: 'Contact not found'
    },
    REPORT: {
        EMAIL_SENT_NEW_RECORD: 'Email sent with report (new record)',
        EMAIL_SENT_EXISTING_USER: 'Email sent with report (existing user)',
        FULLNAME_EMAIL_REQUIRED: 'FullName and Email are required',
    },
    GUIDE: {
        MISSING_LANGUAGE_OR_VERSION_IDS: 'Missing language or versionIds parameter.',
        NOT_FOUND: 'No guides found',
        DOWNLOADED_GUIDE: 'Guide download succesfully .',
        MISSING_LANGUAGE_OR_ID: 'Missing guide ID or language.',
        URL_ALREADY_EXIST: "Url guide already exists",
        VERSION_NOT_EXIST: "No guide version found",
        MISSING_EMAIL_OR_FIRSTNAME: 'Email & First Name must be provided!',


    },
    NEWSLETTER: {
        ALREADY_SUBSCRIBED: 'You are already subscribed to our newsletter.',
        SUBSCRIBE: 'Email has successfully subscribed to the newsletter',
        NOT_FOUND: 'You are not subscribed to our newsletter.',
        UNSUBSCRIBE: 'Email has successfully unsubscribed to the newsletter',

    },
    APPLICATION: {
        ID_CANDIDATE_REQUIRED: 'Candidate ID is required',
        RESUME_REQUIRED: 'Resume is required!',
        DELETED: 'Application deleted successfully',
        NOT_FOUND: 'Application not found',
        USER_ALREADY_EXISTS_LOGIN: 'User already exists, to apply please login!',
        ALREADY_APPLIED: 'You have already applied for this opportunity.',
        SUBMITTED: 'Application submitted successfully.',
        PERMISSION_DENIED: 'Permission denied! Only the applicant can cancel their application',

    },
    SITEMAP: {
        CREATED: 'Opening sitemap created',
    },
    NOTIFICATION: {
        NOT_FOUND: 'Notification not found',
    },
    SETTINGS: {
        NOT_FOUND: 'Setting not found',
        USER_NOT_FOUND: 'User settings not found'
    },
    SKILLS: {
        IMPORTED: 'Skills have been imported successfully',
        DELETED: 'Skills deleted successfully',
        NOT_FOUND: 'Skills not found',

    },

    SLIDER: {
        NOT_FOUND: 'Slider not found',
        MISSING_LANGUAGE_OR_ID: 'Missing slider ID or language.',
        ARCHIVED:'Slide archived successfully.',
        DISARCHIVED:'Slide disarchived successfully.'
    },
    STATISTICS:{
        MISSING_RESSOURCE:'Missing resource',
        INVALID_STATUS:'Invalid staus',
        INVALID_TYPE:'Invalid type',
        INVALID_INDUSTRY:'Invalid industry',
        INVALID_VISIBILITY:'Invalid visibility',
        INVALID_OPPORTUNITY_TYPE:'Invalid opportunity type',

    }
};
