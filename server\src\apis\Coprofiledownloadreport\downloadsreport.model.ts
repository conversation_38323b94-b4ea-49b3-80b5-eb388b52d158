import mongoose, { model } from 'mongoose';
import { IDownloadReport } from './downloadsreport.interface';

const downloadReportSchema = new mongoose.Schema(
  {
    email: { type: String, required: true },
    fullName: String,
    createdAt: { type: Date, required: true, default: Date.now },
    
  },
  { timestamps: true }
);


export const DownloadReportModel = model<IDownloadReport>('DownloadReport', downloadReportSchema);