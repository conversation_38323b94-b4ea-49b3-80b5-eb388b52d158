import { Container } from "@mui/material";
import SvgArrowRight from "@/assets/images/icons/arrowRight.svg";

export default function GlossaryHeader({ language, glossaryPath, glossary }) {
  return (
    <div id="glossary-header">
      <Container className="custom-max-width">
        <div className="glossary-path">
          <a
            locale={language === "en" ? "en" : "fr"}
            href={`${glossaryPath}/`}
            className="link"
          >
            Glossary
          </a>
          {glossary?.word && (
            <>
              <SvgArrowRight />
              <p className="word">{glossary.word}</p>
            </>
          )}
        </div>
        <p className="letter">{glossary?.letter}</p>
      </Container>
    </div>
  );
}
