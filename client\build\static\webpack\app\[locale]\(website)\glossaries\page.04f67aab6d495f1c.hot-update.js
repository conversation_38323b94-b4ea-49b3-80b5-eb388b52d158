"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx":
/*!*******************************************************************!*\
  !*** ./src/features/glossary/component/GlossariesListWebsite.jsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GlossaryListWebsite; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _mui_icons_material_Search__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/icons-material/Search */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_Book__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/icons-material/Book */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Book.js\");\n/* harmony import */ var _mui_icons_material_ErrorOutline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/ErrorOutline */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ErrorOutline.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Empty state component for no glossaries\nconst EmptyGlossaryState = (param)=>{\n    let { isEmpty, isEmptySearch, searchWord, locale, translations } = param;\n    if (isEmpty) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"empty-glossary-state\",\n            sx: {\n                textAlign: \"center\",\n                py: 8,\n                px: 4,\n                minHeight: \"400px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                justifyContent: \"center\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Book__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    sx: {\n                        fontSize: 80,\n                        color: \"text.secondary\",\n                        mb: 3,\n                        opacity: 0.5\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    variant: \"h4\",\n                    component: \"h2\",\n                    gutterBottom: true,\n                    sx: {\n                        fontWeight: 600,\n                        color: \"text.primary\",\n                        mb: 2\n                    },\n                    children: translations?.emptyState?.title || \"No Glossary Terms Available\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    variant: \"body1\",\n                    color: \"text.secondary\",\n                    sx: {\n                        maxWidth: 600,\n                        mb: 4,\n                        lineHeight: 1.6\n                    },\n                    children: translations?.emptyState?.description || \"We're currently building our glossary. Check back soon for comprehensive definitions and explanations of industry terms.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    text: translations?.emptyState?.exploreButton || \"Explore Our Services\",\n                    link: locale === \"fr\" ? \"/fr/services\" : \"/services\",\n                    className: \"btn btn-filled\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (isEmptySearch) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"empty-search-state\",\n            sx: {\n                textAlign: \"center\",\n                py: 8,\n                px: 4,\n                minHeight: \"400px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                justifyContent: \"center\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Search__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    sx: {\n                        fontSize: 80,\n                        color: \"text.secondary\",\n                        mb: 3,\n                        opacity: 0.5\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    variant: \"h4\",\n                    component: \"h2\",\n                    gutterBottom: true,\n                    sx: {\n                        fontWeight: 600,\n                        color: \"text.primary\",\n                        mb: 2\n                    },\n                    children: t?.(\"glossary:searchEmpty:title\") || \"No Results Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    variant: \"body1\",\n                    color: \"text.secondary\",\n                    sx: {\n                        maxWidth: 600,\n                        mb: 2,\n                        lineHeight: 1.6\n                    },\n                    children: t?.(\"glossary:searchEmpty:description\") || `No glossary terms found for \"${searchWord}\". Try searching with different keywords or browse all terms.`\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        mt: 3,\n                        display: \"flex\",\n                        gap: 2,\n                        flexWrap: \"wrap\",\n                        justifyContent: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            text: t?.(\"glossary:searchEmpty:clearButton\") || \"Clear Search\",\n                            link: locale === \"fr\" ? \"/fr/glossaries\" : \"/glossaries\",\n                            className: \"btn btn-outline\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            text: t?.(\"glossary:searchEmpty:browseButton\") || \"Browse All Terms\",\n                            link: locale === \"fr\" ? \"/fr/glossaries\" : \"/glossaries\",\n                            className: \"btn btn-filled\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, undefined);\n    }\n    return null;\n};\n_c = EmptyGlossaryState;\n// Error state component\nconst ErrorGlossaryState = (param)=>{\n    let { error, locale, t: t1 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        className: \"error-glossary-state\",\n        sx: {\n            textAlign: \"center\",\n            py: 8,\n            px: 4,\n            minHeight: \"400px\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\",\n            justifyContent: \"center\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ErrorOutline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                sx: {\n                    fontSize: 80,\n                    color: \"error.main\",\n                    mb: 3,\n                    opacity: 0.7\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 168,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                variant: \"h4\",\n                component: \"h2\",\n                gutterBottom: true,\n                sx: {\n                    fontWeight: 600,\n                    color: \"text.primary\",\n                    mb: 2\n                },\n                children: t1?.(\"glossary:error:title\") || \"Unable to Load Glossary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 176,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                variant: \"body1\",\n                color: \"text.secondary\",\n                sx: {\n                    maxWidth: 600,\n                    mb: 4,\n                    lineHeight: 1.6\n                },\n                children: t1?.(\"glossary:error:description\") || \"We're experiencing technical difficulties loading the glossary. Please try again later.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 188,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mb: 3,\n                    maxWidth: 500\n                },\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 200,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                text: t1?.(\"glossary:error:retryButton\") || \"Try Again\",\n                onClick: ()=>window.location.reload(),\n                className: \"btn btn-filled\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 203,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n        lineNumber: 155,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = ErrorGlossaryState;\nfunction GlossaryListWebsite(param) {\n    let { glossaries, locale, error, isEmpty, isEmptySearch, searchWord, t: t1 } = param;\n    _s();\n    const { t: fallbackT } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const translationFunction = t1 || fallbackT;\n    const letters = Object.keys(glossaries || {});\n    const [expandedLetters, setExpandedLetters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleToggle = (letter)=>{\n        setExpandedLetters((prev)=>({\n                ...prev,\n                [letter]: !prev[letter]\n            }));\n    };\n    // Handle error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"custom-max-width\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorGlossaryState, {\n                    error: error,\n                    locale: locale,\n                    t: translationFunction\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this);\n    }\n    // Handle empty states\n    if (isEmpty || isEmptySearch) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"custom-max-width\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmptyGlossaryState, {\n                    isEmpty: isEmpty,\n                    isEmptySearch: isEmptySearch,\n                    searchWord: searchWord,\n                    locale: locale,\n                    t: translationFunction\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 251,\n            columnNumber: 7\n        }, this);\n    }\n    // Render glossary content\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-page\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"custom-max-width\",\n            children: [\n                searchWord && letters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        mb: 4,\n                        textAlign: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            variant: \"h5\",\n                            component: \"h2\",\n                            gutterBottom: true,\n                            children: translationFunction?.(\"glossary:searchResults:title\") || `Search Results for \"${searchWord}\"`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            children: translationFunction?.(\"glossary:searchResults:count\") || `Found ${letters.reduce((total, letter)=>total + (glossaries[letter]?.length || 0), 0)} terms`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 271,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    container: true,\n                    spacing: 3,\n                    children: letters?.length > 0 && letters?.map((letter, index)=>{\n                        const letterGlossaries = glossaries[letter] || [];\n                        const isExpanded = expandedLetters[letter] || false;\n                        const displayedGlossaries = isExpanded ? letterGlossaries : letterGlossaries.slice(0, 5);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            item: true,\n                            lg: 3,\n                            md: 4,\n                            sm: 6,\n                            xs: 12,\n                            className: \"letters\",\n                            id: letter,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"letter-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"length\",\n                                        children: letterGlossaries.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"letter\",\n                                        children: letter\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"words\",\n                                        children: displayedGlossaries.map((glossary, glossaryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                className: \"word\",\n                                                href: `${locale === \"fr\" ? \"/fr\" : \"\"}/glossaries/${glossary.url}`,\n                                                title: glossary.word,\n                                                children: glossary.word\n                                            }, `${glossary.url}-${glossaryIndex}`, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 25\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 21\n                                    }, this),\n                                    letterGlossaries.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"glossary-button\",\n                                        onClick: ()=>handleToggle(letter),\n                                        size: \"small\",\n                                        variant: \"text\",\n                                        children: isExpanded ? translationFunction?.(\"glossary:showLess\") || \"Show less\" : translationFunction?.(\"glossary:showMore\") || \"Show more\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 306,\n                                columnNumber: 19\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 296,\n                            columnNumber: 17\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this),\n                letters.length > 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        textAlign: \"center\",\n                        mt: 6\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        variant: \"outlined\",\n                        onClick: ()=>window.scrollTo({\n                                top: 0,\n                                behavior: \"smooth\"\n                            }),\n                        children: translationFunction?.(\"glossary:backToTop\") || \"Back to Top\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                        lineNumber: 346,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 345,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 268,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n        lineNumber: 267,\n        columnNumber: 5\n    }, this);\n}\n_s(GlossaryListWebsite, \"sH3IPgSWaBC0FNR0INvylrLyQHk=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c2 = GlossaryListWebsite;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"EmptyGlossaryState\");\n$RefreshReg$(_c1, \"ErrorGlossaryState\");\n$RefreshReg$(_c2, \"GlossaryListWebsite\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx\n"));

/***/ })

});