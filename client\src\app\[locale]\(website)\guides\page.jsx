import { redirect } from "next/navigation";

import { axiosGetJsonSSR } from "@/config/axios";
import ArticlesList from "@/features/blog/components/ArticlesList";
import BannerComponents from "@/components/sections/BannerComponents";
import blogBanner from "../../../../assets/images/guides/Banner/GuideBanner.webp";
import initTranslations from "@/app/i18n";
import { websiteRoutesList } from "@/helpers/routesList";
import GuideList from "../../../../features/guides/components/GuideList";
import { API_URLS } from "@/utils/urls";

export async function generateMetadata({ params: { locale }, searchParams }) {
  const canonicalUrl = `https://www.pentabell.com/${locale !== "en" ? `${locale}/` : ""
    }guide/`;
  const languages = {
    fr: `https://www.pentabell.com/fr/guides/`,
    en: `https://www.pentabell.com/guides/`,
    "x-default": `https://www.pentabell.com/guides/`,
  };
  const { t } = await initTranslations(locale, ["aboutUs", "global"]);


  return {
    title: t("HR guides for human resources leaders | Pentabell"),
    description: t("Take a deep dive into HR, Payroll Outsourcing and Consulting topics. Providing insights, best practices, ressources and checklists to help you to make a difference in your organization."),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots:
      Object.keys(searchParams).length > 0
        ? "follow, noindex"
        : "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

const page = async ({ params, searchParams }) => {
  const { t } = await initTranslations(params.locale, ["aboutUs", "global","guides"]);

  try {

    const res = await axiosGetJsonSSR.get(`${API_URLS.guides}`, {
      params: {
        language: params.locale,
        visibility: "Public",
        pageSize: 9,
        pageNumber: searchParams?.pageNumber || 1,
        searchQuery: searchParams?.keyword || "",
      },
    });
    if (!res?.data?.guide?.guides || res.data.guide?.guides?.length === 0) {
      throw new Error("No Content");
    }

    const guides = res?.data.guide;

    return (
      <>
        <BannerComponents
          bannerImg={blogBanner}
          isEvent={true}
          height={"70vh"}
          subtitle={t("guides:bannerDescription")}
          title={t("guides:bannerTitle")}
          altImg="HR & Payroll guides"
        />
        <GuideList
          data={guides}
          language={params.locale}
          searchParams={searchParams}
        />
      </>
    );
  } catch (error) {
    redirect(
      params.locale === "en"
        ? `/${websiteRoutesList.guide.route}/`
        : `/${websiteRoutesList.guide.route}/`
    );
  }
};

export default page;
