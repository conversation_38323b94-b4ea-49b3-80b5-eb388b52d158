"use client";
import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { DataGrid } from "@mui/x-data-grid";
import {
  Grid,
  TextField,
  FormGroup,
  FormLabel,
  InputAdornment,
  Autocomplete,
} from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import CustomTooltip from "@/components/ui/CustomTooltip";
import SvgRefreshIcon from "@/assets/images/icons/refreshIcon.svg";
import "react-datepicker/dist/react-datepicker.css";
import Loading from "../../../components/loading/Loading";
import {
  useArchiveGlossary,
  useDeleteGlossary,
  useGetGlossariesDashboard,
} from "../hooks/glossaries.hook";
import PreviewIcon from "@/assets/images/icons/preview-icon.svg";
import ArchiveIcon from "@/assets/images/icons/archive-icon.svg";
import DeleteIcon from "@/assets/images/delete.svg";
import Svgedit from "@/assets/images/icons/edit-icon.svg";
import CustomButton from "@/components/ui/CustomButton";
import { useTheme, useMediaQuery } from "@mui/material";
import SvgSearchIcon from "@/assets/images/icons/searchIcon.svg";
import {
  adminRoutes,
  baseUrlBackoffice,
  websiteRoutesList,
} from "@/helpers/routesList";
import { formatDate } from "@/utils/functions";
import DialogModal from "@/features/user/component/updateProfile/experience/DialogModal";

const GlossariesListDashboard = ({ language }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { t } = useTranslation();

  const [sortOrder, setSortOrder] = useState("");
  const [sortBy, setSortBy] = useState("createdAt");
  const [letter, setLetter] = useState("");
  const [createdAt, setCreatedAt] = useState(null);
  const [search, setSearch] = useState(false);
  const [isArchived, setIsArchivedFilter] = useState();

  const savedPagination = localStorage.getItem("PAGINATION_KEY");
  const savedVisibility = localStorage.getItem("Visibility");
  const savedSeachValue = localStorage.getItem("SearchValue");
  const savedSortBy = localStorage.getItem("SortBy");
  const savedLetter = localStorage.getItem("Letter");

  const [archiveModalOpen, setArchiveModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedGlossary, setSelectedGlossary] = useState(null);

  const archiveGlossaryMutation = useArchiveGlossary();
  const deleteGlossaryMutation = useDeleteGlossary();

  const [searchQuery, setSearchQuery] = useState(
    savedSeachValue ? JSON.parse(savedSeachValue) : ""
  );
  const [visibility, setVisibility] = useState(
    savedVisibility ? JSON.parse(savedVisibility) : ""
  );

  useEffect(() => {
    if (savedSortBy) {
      setSortBy(JSON.parse(savedSortBy));
    }
  }, [savedSortBy]);

  useEffect(() => {
    if (savedLetter) {
      setLetter(JSON.parse(savedLetter));
    }
  }, [savedLetter]);

  const [paginationModel, setPaginationModel] = React.useState(
    savedPagination
      ? JSON.parse(savedPagination)
      : {
          page: 0,
          pageSize: 10,
        }
  );

  const isOpen = true;
  const [selectedLanguage, setSelectedLanguage] = useState(
    language ? language : "en"
  );

  const resetSearch = () => {
    setSearchQuery("");
    setVisibility("");
    setSortOrder("");
    setSortBy("createdAt");
    setLetter("");
    setCreatedAt(null);
    setSelectedLanguage(language ? language : "en");
    setPaginationModel({ page: 0, pageSize: 10 });
    setIsArchivedFilter("");
    setSearch(!search);
    localStorage.setItem("Visibility", "");
    localStorage.setItem("SearchValue", "");
    localStorage.setItem("SortBy", JSON.stringify("createdAt"));
    localStorage.setItem("Letter", "");
    localStorage.setItem(
      "PAGINATION_KEY",
      JSON.stringify({
        page: 0,
        pageSize: 10,
      })
    );
  };

  const truncateTitle = (title) => {
    const words = title.split(" ");
    if (words?.length > 4) {
      return words.slice(0, 4).join(" ") + "...";
    } else {
      return title;
    }
  };

  const getGlossaries = useGetGlossariesDashboard({
    language: selectedLanguage,
    pageSize: paginationModel.pageSize,
    pageNumber: paginationModel.page + 1,
    sortOrder,
    sortBy,
    letter,
    searchQuery,
    visibility,
    createdAt,
    isArchived,
  });

  useEffect(() => {
    setSelectedLanguage(language);
  }, [language]);

  useEffect(() => {
    getGlossaries.refetch();
  }, [search, paginationModel]);

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleVisibilityChange = (e) => {
    setVisibility(e?.value || "");
    localStorage.setItem("Visibility", JSON.stringify(e?.value || ""));
  };

  const handleCreatedAtChange = (date) => {
    setCreatedAt(date);
  };

  const handleArchiveClick = (glossary) => {
    setSelectedGlossary(glossary);
    setArchiveModalOpen(true);
  };

  const handleDeleteClick = (glossary) => {
    setSelectedGlossary(glossary);
    setDeleteModalOpen(true);
  };

  const handleArchiveConfirm = () => {
    if (selectedGlossary) {
      archiveGlossaryMutation.mutate(
        {
          language: selectedLanguage,
          id: selectedGlossary.id,
        },
        {
          onSuccess: () => {
            setArchiveModalOpen(false);
            setSelectedGlossary(null);
            setSearch(!search);
          },
        }
      );
    }
  };

  const handleDeleteConfirm = () => {
    if (selectedGlossary) {
      deleteGlossaryMutation.mutate(
        {
          language: selectedLanguage,
          id: selectedGlossary.id,
        },
        {
          onSuccess: () => {
            setDeleteModalOpen(false);
            setSelectedGlossary(null);
            setSearch(!search);
          },
        }
      );
    }
  };

  const visibilityOption = [
    { value: "Public", label: "Public" },
    { value: "Private", label: "Private" },
    { value: "Draft", label: "Draft" },
  ];

  const handleIsArchivedFilterChange = (e) => {
    setIsArchivedFilter(e?.value);
    if (e?.value === "undefined") {
      setIsArchivedFilter(undefined);
    }
  };

  const handlePaginationChange = (newPaginationModel) => {
    setPaginationModel(newPaginationModel);
    localStorage.setItem("PAGINATION_KEY", JSON.stringify(newPaginationModel));
  };

  if (getGlossaries.isLoading) {
    return <Loading />;
  }

  const rows =
    getGlossaries?.data?.glossaries?.map((item, index) => {
      const versions = item.versions;
      return {
        id: item._id,
        word: versions?.word ? truncateTitle(versions.word) : "No title",
        letter: versions?.letter ? truncateTitle(versions.letter) : "No title",
        createdBy: versions?.createdBy
          ? `${versions.createdBy?.firstName} ${versions.createdBy?.lastName}`
          : "N/A",
        createdAt: versions?.createdAt,
        language: item?.existingLanguages?.join("/") || "N/A",
        actions: item._id,
        visibility: versions?.visibility || "N/A",
        url: versions?.url || "N/A",
        isArchived: versions?.isArchived,
      };
    }) || [];

  const columns = [
    {
      field: "letter",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell",
      headerName: t("listGlossary:letter"),
      flex: 1,
      renderCell: (params) => (
        <a
          href={`/${selectedLanguage}/${websiteRoutesList.glossaries.route}/${params.row?.letter}`}
          className="link"
        >
          {params.row.letter}
        </a>
      ),
    },
    {
      field: "word",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell",
      headerName: t("listGlossary:word"),
      flex: 0.4,
    },
    {
      field: "createdBy",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell ",
      headerName: t("listGlossary:createdBy"),
      flex: 0.8,
    },
    {
      field: "createdAt",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell ",
      flex: 0.4,
      headerName: t("listGlossary:createdAt"),
      valueFormatter: formatDate,
    },
    {
      field: "language",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell ",
      flex: 0.6,
      headerName: t("listopportunity:availablelanguage"),
    },
    {
      field: "visibility",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell",
      flex: 0.4,
      headerName: t("listGlossary:visibility"),
    },
    {
      field: "isArchived",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell",
      flex: 0.4,
      headerName: t("listGlossary:archived"),
      renderCell: (params) => (
        <p>{params.row.isArchived ? t("global:yes") : t("global:no")}</p>
      ),
    },
    {
      field: "actions",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: "",
      renderCell: (params) => (
        <div
          className="action-buttons"
          style={{ gridColumn: "span 2", width: "100%" }}
        >
          <CustomTooltip
            child={
              <div>
                <CustomButton
                  icon={<Svgedit />}
                  leftIcon={true}
                  link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.glossaries.route}/${adminRoutes.edit.route}/${params.row.id}`}
                  className="btn btn-ghost edit-blog"
                />
              </div>
            }
            title={t("global:edit")}
          />
          <CustomTooltip
            child={
              <div>
                <CustomButton
                  icon={<PreviewIcon />}
                  leftIcon={true}
                  link={`/${selectedLanguage}/${websiteRoutesList.glossaries.route}/${params.row?.url}`}
                  className="btn btn-ghost edit-blog"
                />
              </div>
            }
            title={t("global:preview")}
          />
          <CustomTooltip
            child={
              <div>
                <CustomButton
                  icon={<ArchiveIcon />}
                  leftIcon={true}
                  onClick={() => handleArchiveClick(params.row)}
                  className="btn btn-ghost edit-blog"
                />
              </div>
            }
            title={
              params.row.isArchived
                ? t("global:unarchive")
                : t("global:archive")
            }
          />
          <CustomTooltip
            child={
              <div>
                <CustomButton
                  icon={<DeleteIcon />}
                  leftIcon={true}
                  onClick={() => handleDeleteClick(params.row)}
                  className="btn btn-ghost edit-blog"
                />
              </div>
            }
            title={t("global:delete")}
          />
        </div>
      ),
      flex: 1,
    },
  ];

  const archivedOptions = [
    { value: true, label: "Archived" },
    { value: false, label: "Not Archived" },
  ];

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      setSearch(!search);
    }
  };
  return (
    <>
      <DialogModal
        open={archiveModalOpen}
        onClose={() => setArchiveModalOpen(false)}
        onConfirm={handleArchiveConfirm}
        message={
          selectedGlossary?.isArchived
            ? t("listGlossary:unarchiveConfirmation", {
                word: selectedGlossary?.word,
              })
            : t("listGlossary:archiveConfirmation", {
                word: selectedGlossary?.word,
              })
        }
      />
      <DialogModal
        open={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        message={t("listGlossary:deleteConfirmation", {
          word: selectedGlossary?.word,
        })}
      />
      <div className="display-inline">
        <p className="heading-h2 semi-bold">
          {t("listGlossary:listOfGlossaries")}
          <span className="opportunities-nbr">
            {getGlossaries?.data?.totalGlossaries}
          </span>
        </p>

        <CustomButton
          className="btn btn-filled"
          text={t("listGlossary:addGlossary")}
          link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.glossaries.route}/${adminRoutes.add.route}`}
        />
      </div>

      <div id="container" className="recent-application-pentabell">
        <div className={`main-content ${isOpen ? "open" : "closed"}`}>
          <div className="table-Grid">
            <Grid item xs={12}>
              <Grid
                id="filter"
                container
                spacing={2}
                justifyContent="flex-start"
                alignItems="center"
              >
                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("listGlossary:filterByWord")}
                    </FormLabel>
                    <TextField
                      className="input-pentabell"
                      autoComplete="off"
                      slotProps={{
                        input: {
                          startAdornment: (
                            <InputAdornment position="start">
                              <SvgSearchIcon />
                            </InputAdornment>
                          ),
                        },
                      }}
                      variant="standard"
                      type="text"
                      onChange={handleSearchChange}
                      onKeyDown={handleKeyDown}
                      value={searchQuery}
                    />
                  </FormGroup>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("listGlossary:filterByLetter")}
                    </FormLabel>
                    <Autocomplete
                      className="input-pentabell"
                      id="letter-autocomplete"
                      options={[
                        { value: "A", label: "A" },
                        { value: "B", label: "B" },
                        { value: "C", label: "C" },
                        { value: "D", label: "D" },
                        { value: "E", label: "E" },
                        { value: "F", label: "F" },
                        { value: "G", label: "G" },
                        { value: "H", label: "H" },
                        { value: "I", label: "I" },
                        { value: "J", label: "J" },
                        { value: "K", label: "K" },
                        { value: "L", label: "L" },
                        { value: "M", label: "M" },
                        { value: "N", label: "N" },
                        { value: "O", label: "O" },
                        { value: "P", label: "P" },
                        { value: "Q", label: "Q" },
                        { value: "R", label: "R" },
                        { value: "S", label: "S" },
                        { value: "T", label: "T" },
                        { value: "U", label: "U" },
                        { value: "V", label: "V" },
                        { value: "W", label: "W" },
                        { value: "X", label: "X" },
                        { value: "Y", label: "Y" },
                        { value: "Z", label: "Z" },
                      ]}
                      getOptionLabel={(option) => option.label}
                      value={
                        letter
                          ? {
                              value: letter,
                              label: letter,
                            }
                          : null
                      }
                      onChange={(event, newValue) => {
                        if (newValue) {
                          setLetter(newValue.value);
                          localStorage.setItem(
                            "Letter",
                            JSON.stringify(newValue.value)
                          );
                        } else {
                          setLetter("");
                          localStorage.setItem("Letter", "");
                        }
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          className="input-pentabell multiple-select"
                          variant="standard"
                        />
                      )}
                    />
                  </FormGroup>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("global:sort")}
                    </FormLabel>
                    <Autocomplete
                      className="input-pentabell"
                      id="sort-by-autocomplete"
                      options={[
                        { value: "createdAt", label: "Date" },
                        { value: "alphabetical", label: "Alphabetical" },
                      ]}
                      getOptionLabel={(option) => option.label}
                      value={
                        sortBy
                          ? {
                              value: sortBy,
                              label:
                                sortBy === "createdAt"
                                  ? "Date"
                                  : "Alphabetical",
                            }
                          : null
                      }
                      onChange={(event, newValue) => {
                        if (newValue) {
                          setSortBy(newValue.value);
                          localStorage.setItem(
                            "SortBy",
                            JSON.stringify(newValue.value)
                          );
                        }
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          className="input-pentabell multiple-select"
                          variant="standard"
                        />
                      )}
                    />
                  </FormGroup>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("global:sortOrder")}
                    </FormLabel>
                    <Autocomplete
                      className="input-pentabell"
                      id="sort-order-autocomplete"
                      options={[
                        { value: "desc", label: "DESC" },
                        { value: "asc", label: "ASC" },
                      ]}
                      getOptionLabel={(option) => option.label}
                      value={
                        sortOrder
                          ? {
                              value: sortOrder,
                              label: sortOrder === "desc" ? "DESC" : "ASC",
                            }
                          : null
                      }
                      onChange={(event, newValue) => {
                        if (newValue) {
                          setSortOrder(newValue.value);
                        }
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          className="input-pentabell multiple-select"
                          variant="standard"
                        />
                      )}
                    />
                  </FormGroup>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("listGlossary:visibility")}
                    </FormLabel>
                    <Autocomplete
                      className="input-pentabell"
                      id="visibility-autocomplete"
                      options={visibilityOption}
                      getOptionLabel={(option) => option.label}
                      value={
                        visibility
                          ? {
                              value: visibility,
                              label: visibilityOption.find(
                                (option) => option.value === visibility
                              )?.label,
                            }
                          : null
                      }
                      onChange={(e, value) => handleVisibilityChange(value)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          className="input-pentabell multiple-select"
                          variant="standard"
                        />
                      )}
                    />
                  </FormGroup>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("listGlossary:archivage")}
                    </FormLabel>
                    <Autocomplete
                      className="input-pentabell"
                      id="sort-order-autocomplete"
                      options={archivedOptions}
                      getOptionLabel={(option) => option.label}
                      value={
                        isArchived
                          ? {
                              value: isArchived,
                              label: archivedOptions.find(
                                (option) => option.value === isArchived
                              )?.label,
                            }
                          : null
                      }
                      onChange={(e, value) =>
                        handleIsArchivedFilterChange(value)
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          className="input-pentabell multiple-select"
                          variant="standard"
                        />
                      )}
                    />
                  </FormGroup>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <FormGroup className="form-group data-picker-width">
                    <FormLabel className="label-pentabell">
                      {t("listGlossary:createdAt")}
                    </FormLabel>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DemoContainer
                        components={["DatePicker"]}
                        sx={{ minWidth: "10px" }}
                      >
                        <DatePicker
                          selected={createdAt}
                          onChange={handleCreatedAtChange}
                          placeholderText="Filter by creation date"
                          className="input-date"
                        />
                      </DemoContainer>
                    </LocalizationProvider>
                  </FormGroup>
                </Grid>

                <Grid item xs={12} sm={3} className="btns-filter dashboard">
                  <CustomButton
                    icon={<SvgRefreshIcon />}
                    className={"btn btn-outlined btn-refresh"}
                    onClick={resetSearch}
                  />
                  <CustomButton
                    text={t("global:filter")}
                    onClick={() => {
                      setSearch(!search);
                    }}
                    className={"btn btn-filled"}
                  />
                </Grid>
              </Grid>
            </Grid>
          </div>

          <Grid item xs={12}>
            <div style={{ height: "100%", width: "100%" }}>
              <DataGrid
                rows={rows}
                columns={columns}
                pagination
                className="pentabell-table"
                paginationMode="server"
                paginationModel={paginationModel}
                onPaginationModelChange={handlePaginationChange}
                pageSizeOptions={[5, 10, 25]}
                rowCount={getGlossaries?.data?.totalGlossaries || 0}
                autoHeight
                disableSelectionOnClick
                columnVisibilityModel={{
                  createdBy: !isMobile,
                  createdAt: !isMobile,
                  totalCommentaires: !isMobile,
                  visibility: !isMobile,
                  language: !isMobile,
                  archived: !isMobile,
                }}
              />
            </div>
          </Grid>
        </div>
      </div>
    </>
  );
};

export default GlossariesListDashboard;
