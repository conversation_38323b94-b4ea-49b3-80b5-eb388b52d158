"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/features/stats/charts/CommentByCategory.jsx":
/*!*********************************************************!*\
  !*** ./src/features/stats/charts/CommentByCategory.jsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CommentByCategory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon2_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon2.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon2.svg\");\n/* harmony import */ var _components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/charts/CustomPieChart */ \"(app-pages-browser)/./src/components/charts/CustomPieChart.jsx\");\n/* harmony import */ var _barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction CommentByCategory(param) {\n    let { transformedCategories, dateFromComment, dateToComment, approve, categories, setCategories, setFilteredCategories, setSearchComment, searchComment, resetSearchComments, pieCharts, setApprove } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        className: \"card\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"heading-h3\",\n                    gutterBottom: true,\n                    children: pieCharts[3].title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    elevation: 0,\n                    disableGutters: true,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            \"aria-controls\": \"panel1bh-content\",\n                            id: \"panel1bh-header\",\n                            className: \"svg-accordion\",\n                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                lineNumber: 48,\n                                columnNumber: 25\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"label-pentabell\",\n                                children: t(\"statsDash:filters\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            elevation: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                container: true,\n                                className: \"chart-grid\",\n                                spacing: 1,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"blue-text\",\n                                                    children: [\n                                                        \" \",\n                                                        t(\"statsDash:categories\"),\n                                                        \" :\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    multiple: true,\n                                                    id: `category`,\n                                                    options: transformedCategories ? transformedCategories : [],\n                                                    getOptionLabel: (option)=>option.name,\n                                                    value: categories.length > 0 ? transformedCategories.filter((category)=>categories.includes(category.name)) : [],\n                                                    onChange: (event, selectedOptions)=>{\n                                                        const categoryNames = selectedOptions.map((category)=>category.name);\n                                                        setCategories(categoryNames);\n                                                        setFilteredCategories(categoryNames.join(\",\"));\n                                                    },\n                                                    renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            ...params,\n                                                            className: \"\",\n                                                            variant: \"standard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        md: 12,\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"blue-text\",\n                                                children: [\n                                                    t(\"statsDash:approvedComments\"),\n                                                    \" :\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"select-pentabell blue-text\",\n                                                value: approve,\n                                                defaultValue: \"\",\n                                                onChange: (event)=>setApprove(event.target.value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        value: \"\",\n                                                        selected: true,\n                                                        disabled: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                            children: t(\"statsDash:approvedComments\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                            lineNumber: 98,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"blue-text\",\n                                                        value: \"\",\n                                                        children: t(\"statsDash:all\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"blue-text\",\n                                                        value: true,\n                                                        children: t(\"statsDash:approved\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"blue-text\",\n                                                        value: false,\n                                                        children: t(\"statsDash:notApproved\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            label: t(\"statsDash:fromDate\"),\n                                            type: \"date\",\n                                            value: dateFromComment,\n                                            onChange: (e)=>setDateFromComment(e.target.value),\n                                            fullWidth: true,\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            label: t(\"statsDash:toDate\"),\n                                            type: \"date\",\n                                            value: dateToComment,\n                                            onChange: (e)=>setDateToComment(e.target.value),\n                                            fullWidth: true,\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 3,\n                                        sm: 1,\n                                        md: 4,\n                                        className: \"btns-filter dashboard\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon2_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            className: \"btn btn-outlined btn-refresh full-width\",\n                                            onClick: resetSearchComments\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 11,\n                                        sm: 11,\n                                        md: 8,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            text: t(\"statsDash:filter\"),\n                                            onClick: ()=>{\n                                                setSearchComment(!searchComment);\n                                            },\n                                            className: \"btn btn-outlined btn-filter-stat full-width\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"chart-wrapper\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            donuts: true,\n                            chart: pieCharts[3]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_s(CommentByCategory, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation\n    ];\n});\n_c = CommentByCategory;\nvar _c;\n$RefreshReg$(_c, \"CommentByCategory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/stats/charts/CommentByCategory.jsx\n"));

/***/ })

});