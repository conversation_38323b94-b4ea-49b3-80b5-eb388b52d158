(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5788],{69917:function(e){e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},10241:function(e,n,t){"use strict";t.d(n,{J:function(){return u},j:function(){return o}});let r=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,a={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},s=e=>a[e],i={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(r,s)},o=(e={})=>{i={...i,...e}},u=()=>i},89561:function(e,n,t){"use strict";let r;t.d(n,{I:function(){return a},n:function(){return s}});let a=e=>{r=e},s=()=>r},55788:function(e,n,t){"use strict";t.d(n,{a3:function(){return E},$G:function(){return y}});var r=t(2265);t(69917),Object.create(null);let a=(e,n,t,r)=>{let a=[t,{code:n,...r||{}}];if(e?.services?.logger?.forward)return e.services.logger.forward(a,"warn","react-i18next::",!0);p(a[0])&&(a[0]=`react-i18next:: ${a[0]}`),e?.services?.logger?.warn?e.services.logger.warn(...a):console?.warn&&console.warn(...a)},s={},i=(e,n,t,r)=>{p(t)&&s[t]||(p(t)&&(s[t]=new Date),a(e,n,t,r))},o=(e,n)=>()=>{if(e.isInitialized)n();else{let t=()=>{setTimeout(()=>{e.off("initialized",t)},0),n()};e.on("initialized",t)}},u=(e,n,t)=>{e.loadNamespaces(n,o(e,t))},l=(e,n,t,r)=>{if(p(t)&&(t=[t]),e.options.preload&&e.options.preload.indexOf(n)>-1)return u(e,t,r);t.forEach(n=>{0>e.options.ns.indexOf(n)&&e.options.ns.push(n)}),e.loadLanguages(n,o(e,r))},c=(e,n,t={})=>n.languages&&n.languages.length?n.hasLoadedNamespace(e,{lng:t.lng,precheck:(n,r)=>{if(t.bindI18n?.indexOf("languageChanging")>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!r(n.isLanguageChangingTo,e))return!1}}):(i(n,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:n.languages}),!0),p=e=>"string"==typeof e,g=e=>"object"==typeof e&&null!==e;var d=t(10241),f=t(89561);t(28230);let m=(0,r.createContext)();class h{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}let N=(e,n)=>{let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=n?t.current:e},[e,n]),t.current},b=(e,n,t,r)=>e.getFixedT(n,t,r),w=(e,n,t,a)=>(0,r.useCallback)(b(e,n,t,a),[e,n,t,a]),y=(e,n={})=>{let{i18n:t}=n,{i18n:a,defaultNS:s}=(0,r.useContext)(m)||{},o=t||a||(0,f.n)();if(o&&!o.reportNamespaces&&(o.reportNamespaces=new h),!o){i(o,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");let e=(e,n)=>p(n)?n:g(n)&&p(n.defaultValue)?n.defaultValue:Array.isArray(e)?e[e.length-1]:e,n=[e,{},!1];return n.t=e,n.i18n={},n.ready=!1,n}o.options.react?.wait&&i(o,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");let y={...(0,d.J)(),...o.options.react,...n},{useSuspense:E,keyPrefix:x}=y,k=e||s||o.options?.defaultNS;k=p(k)?[k]:k||["translation"],o.reportNamespaces.addUsedNamespaces?.(k);let C=(o.isInitialized||o.initializedStoreOnce)&&k.every(e=>c(e,o,y)),I=w(o,n.lng||null,"fallback"===y.nsMode?k:k[0],x),v=()=>I,T=()=>b(o,n.lng||null,"fallback"===y.nsMode?k:k[0],x),[O,S]=(0,r.useState)(v),_=k.join();n.lng&&(_=`${n.lng}${_}`);let j=N(_),A=(0,r.useRef)(!0);(0,r.useEffect)(()=>{let{bindI18n:e,bindI18nStore:t}=y;A.current=!0,C||E||(n.lng?l(o,n.lng,k,()=>{A.current&&S(T)}):u(o,k,()=>{A.current&&S(T)})),C&&j&&j!==_&&A.current&&S(T);let r=()=>{A.current&&S(T)};return e&&o?.on(e,r),t&&o?.store.on(t,r),()=>{A.current=!1,o&&e?.split(" ").forEach(e=>o.off(e,r)),t&&o&&t.split(" ").forEach(e=>o.store.off(e,r))}},[o,_]),(0,r.useEffect)(()=>{A.current&&C&&S(v)},[o,x,C]);let z=[O,o,C];if(z.t=O,z.i18n=o,z.ready=C,C||!C&&!E)return z;throw new Promise(e=>{n.lng?l(o,n.lng,k,()=>e()):u(o,k,()=>e())})};function E({i18n:e,defaultNS:n,children:t}){let a=(0,r.useMemo)(()=>({i18n:e,defaultNS:n}),[e,n]);return(0,r.createElement)(m.Provider,{value:a},t)}},28230:function(e,n,t){"use strict";t.d(n,{D:function(){return s}});var r=t(10241),a=t(89561);let s={type:"3rdParty",init(e){(0,r.j)(e.options.react),(0,a.I)(e)}}}}]);