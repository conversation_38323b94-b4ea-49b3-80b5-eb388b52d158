"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/features/stats/charts/UsersActivities.jsx":
/*!*******************************************************!*\
  !*** ./src/features/stats/charts/UsersActivities.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UsersActivities; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon.svg\");\n/* harmony import */ var _components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/charts/CustomMultiBarchart */ \"(app-pages-browser)/./src/components/charts/CustomMultiBarchart.jsx\");\n/* harmony import */ var _stats_hooks__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../stats.hooks */ \"(app-pages-browser)/./src/features/stats/stats.hooks.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction UsersActivities(param) {\n    let { chartSettings, t } = param;\n    _s();\n    const [dateFromUser, setDateFromUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToUser, setDateToUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchUser, setSearchUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchActivity = ()=>{\n        setDateToUser(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromUser(\"2024-09-01\");\n        setSearchUser(!searchUser);\n    };\n    const getDataUserActivity = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_5__.useGetUserStat)({\n        dateFrom: dateFromUser,\n        dateTo: dateToUser\n    });\n    const userAactivity = {\n        title: t(\"statsDash:usersActivities\"),\n        dataKey: [\n            \"login\",\n            \"register\",\n            \"resumes\",\n            \"applications\"\n        ],\n        dataset: getDataUserActivity?.data,\n        color: [\n            \"#30B0C7\",\n            \"#234791\",\n            \"#007AFF\",\n            \"#32ADE6\"\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataUserActivity.refetch();\n    }, [\n        searchUser\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        className: \"card\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"barchartfilter-wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        container: true,\n                        className: \"chart-grid\",\n                        spacing: 1,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 12,\n                                md: 3,\n                                alignContent: \"left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"heading-h3\",\n                                    gutterBottom: true,\n                                    children: userAactivity.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2.5,\n                                xl: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    label: t(\"statsDash:fromDate\"),\n                                    type: \"date\",\n                                    value: dateFromUser,\n                                    onChange: (e)=>setDateFromUser(e.target.value),\n                                    fullWidth: true,\n                                    InputLabelProps: {\n                                        shrink: true\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2.5,\n                                xl: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    label: t(\"statsDash:toDate\"),\n                                    type: \"date\",\n                                    value: dateToUser,\n                                    onChange: (e)=>setDateToUser(e.target.value),\n                                    fullWidth: true,\n                                    InputLabelProps: {\n                                        shrink: true\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                item: true,\n                                xs: 2,\n                                sm: 1,\n                                md: 1.5,\n                                xl: 1,\n                                className: \"btns-filter dashboard\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 23\n                                    }, void 0),\n                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                    onClick: resetSearchActivity\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                item: true,\n                                xs: 10,\n                                sm: 11,\n                                md: 2.5,\n                                xl: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    text: t(\"statsDash:filter\"),\n                                    onClick: ()=>{\n                                        setSearchUser(!searchUser);\n                                    },\n                                    className: \"btn btn-outlined btn-filter-stat full-width\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"chart-wrapper\",\n                    children: [\n                        userAactivity.dataset?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"labelstats-wrapper\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"logins-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:logins\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"newacccounts-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:newAccounts\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"uploadedresumes-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:uploadedResumes\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"applications-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:applications\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            chart: userAactivity,\n                            chartSettings: chartSettings\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\UsersActivities.jsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(UsersActivities, \"HE/7d3HxjxenqdeJPpfMPdUk4sA=\", false, function() {\n    return [\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_5__.useGetUserStat\n    ];\n});\n_c = UsersActivities;\nvar _c;\n$RefreshReg$(_c, \"UsersActivities\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/stats/charts/UsersActivities.jsx\n"));

/***/ })

});