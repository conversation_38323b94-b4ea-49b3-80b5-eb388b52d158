"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/features/stats/charts/CommentByCategory.jsx":
/*!*********************************************************!*\
  !*** ./src/features/stats/charts/CommentByCategory.jsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CommentByCategory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon2_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon2.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon2.svg\");\n/* harmony import */ var _components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/charts/CustomPieChart */ \"(app-pages-browser)/./src/components/charts/CustomPieChart.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CommentByCategory(param) {\n    let { transformedCategories, pieCharts } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    const [filteredCategories, setFilteredCategories] = useState([]);\n    const [approve, setApprove] = useState(true);\n    const [categories, setCategories] = useState([]);\n    const [dateFromComment, setDateFromComment] = useState(\"2024-09-01\");\n    const [dateToComment, setDateToComment] = useState(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchComment, setSearchComment] = useState(false);\n    const resetSearchComments = ()=>{\n        setDateToComment(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromComment(\"2024-09-01\");\n        setFilteredCategories([]);\n        setApprove(true);\n        setCategories([]);\n        setSearchComment(!searchComment);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        className: \"card\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"heading-h3\",\n                    gutterBottom: true,\n                    children: pieCharts[3].title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    elevation: 0,\n                    disableGutters: true,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            \"aria-controls\": \"panel1bh-content\",\n                            id: \"panel1bh-header\",\n                            className: \"svg-accordion\",\n                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                lineNumber: 60,\n                                columnNumber: 25\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"label-pentabell\",\n                                children: t(\"statsDash:filters\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            elevation: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                container: true,\n                                className: \"chart-grid\",\n                                spacing: 1,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"blue-text\",\n                                                    children: [\n                                                        \" \",\n                                                        t(\"statsDash:categories\"),\n                                                        \" :\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    multiple: true,\n                                                    id: `category`,\n                                                    options: transformedCategories ? transformedCategories : [],\n                                                    getOptionLabel: (option)=>option.name,\n                                                    value: categories.length > 0 ? transformedCategories.filter((category)=>categories.includes(category.name)) : [],\n                                                    onChange: (event, selectedOptions)=>{\n                                                        const categoryNames = selectedOptions.map((category)=>category.name);\n                                                        setCategories(categoryNames);\n                                                        setFilteredCategories(categoryNames.join(\",\"));\n                                                    },\n                                                    renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            ...params,\n                                                            className: \"\",\n                                                            variant: \"standard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                            lineNumber: 93,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        md: 12,\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"blue-text\",\n                                                children: [\n                                                    t(\"statsDash:approvedComments\"),\n                                                    \" :\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"select-pentabell blue-text\",\n                                                value: approve,\n                                                defaultValue: \"\",\n                                                onChange: (event)=>setApprove(event.target.value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        value: \"\",\n                                                        selected: true,\n                                                        disabled: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                            children: t(\"statsDash:approvedComments\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"blue-text\",\n                                                        value: \"\",\n                                                        children: t(\"statsDash:all\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"blue-text\",\n                                                        value: true,\n                                                        children: t(\"statsDash:approved\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"blue-text\",\n                                                        value: false,\n                                                        children: t(\"statsDash:notApproved\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            label: t(\"statsDash:fromDate\"),\n                                            type: \"date\",\n                                            value: dateFromComment,\n                                            onChange: (e)=>setDateFromComment(e.target.value),\n                                            fullWidth: true,\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            label: t(\"statsDash:toDate\"),\n                                            type: \"date\",\n                                            value: dateToComment,\n                                            onChange: (e)=>setDateToComment(e.target.value),\n                                            fullWidth: true,\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 3,\n                                        sm: 1,\n                                        md: 4,\n                                        className: \"btns-filter dashboard\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon2_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            className: \"btn btn-outlined btn-refresh full-width\",\n                                            onClick: resetSearchComments\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 11,\n                                        sm: 11,\n                                        md: 8,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            text: t(\"statsDash:filter\"),\n                                            onClick: ()=>{\n                                                setSearchComment(!searchComment);\n                                            },\n                                            className: \"btn btn-outlined btn-filter-stat full-width\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"chart-wrapper\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            donuts: true,\n                            chart: pieCharts[3]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(CommentByCategory, \"3QHr2AwFI/VbFL4RSkKF2GYrAco=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation\n    ];\n});\n_c = CommentByCategory;\nvar _c;\n$RefreshReg$(_c, \"CommentByCategory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/stats/charts/CommentByCategory.jsx\n"));

/***/ })

});