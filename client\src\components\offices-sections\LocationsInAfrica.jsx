import { Container } from "@mui/material";

import CustomButton from "../ui/CustomButton";

import SvgArrow from "@/assets/images/icons/arrow.svg";
import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";
import morroccoImg from "@/assets/images/offices/morroccoImg.png";
import HydraImg from "@/assets/images/offices/HydraImg.png";
import tunisiaImg from "@/assets/images/offices/tunisiaImg.png";
import libyaImg from "@/assets/images/offices/libyaImg.png";
import egypteImg from "@/assets/images/offices/egypteImg.png";
import hassiImg from "@/assets/images/offices/hassiImg.png";
import Image from "next/image";
import { findCountryFlag, findCountryLabel } from "@/utils/functions";
import { OfficesCountries } from "@/config/countries";
import React from "react";
import { websiteRoutesList } from "@/helpers/routesList";

function LocationsInAfrica({ t }) {
  const contacts = [
    {
      title: t("africa:locations:morocco:title"),
      locations: [t("africa:locations:morocco:address")],
      phones: ["+212 5 22 78 63 66"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.moroccoPage.route}`,
      img: morroccoImg,
      country: OfficesCountries.MOROCCO,
      alt: t("africa:locations:morocco:alt")  
    },
    {
      title: t("africa:locations:algeria1:title"),
      locations: [t("africa:locations:algeria1:address")],
      phones: ["+213 982 30 13 29"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.algeriaPage.route}`,
      img: HydraImg,
      country: OfficesCountries.ALGERIAHYDRA,
      alt: t("africa:locations:algeria1:alt")
    },
    {
      title: t("africa:locations:algeria2:title"),
      locations: [t("africa:locations:algeria2:address")],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.algeriaPage.route}`,
      img: hassiImg,
      country: OfficesCountries.ALGERIAHASSI,
      alt: t("africa:locations:algeria2:alt") 
    },
    {
      title: t("africa:locations:tunisia:title"),
      locations: [t("africa:locations:tunisia:address")],
      phones: ["+216 31 385 510"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.tunisiaPage.route}`,
      img: tunisiaImg,
      country: OfficesCountries.TUNISIA,
      alt: t("africa:locations:tunisia:alt") 
    },
    {
      title: t("africa:locations:libya:title"),
      locations: [t("africa:locations:libya:address")],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.libyaPage.route}`,
      img: libyaImg,
      country: OfficesCountries.LIBYA,
      alt: t("africa:locations:libya:alt")  
    },
    {
      title: t("africa:locations:egypt:title"),
      locations: [t("africa:locations:egypt:address")],
      phones: ["+213(0)23485910", "+213(0)23485144"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.egyptePage.route}`,
      img: egypteImg,
      country: OfficesCountries.EGYPT,
      alt: t("africa:locations:egypt:alt")  
    },
  ];

  return (
    <div id="africa-locations">
      <Container className="custom-max-width">
        <p className="sub-heading text-center text-blue">{t("africa:locations:subTitle")}</p>
        <p className="heading-h1 text-center">{t("africa:locations:title")}</p>
      </Container>
      <Container className="contact-items-section custom-max-width">
        {contacts.map((contact, index) => (
          <div className="contact-item" key={index}>
            <div>
              <div className="country-img">
                <p className="country-label">
                  <img
                    width={22}
                    height={14}
                    src={findCountryFlag(contact.country).src}
                    alt={findCountryLabel(contact.country)}
                    loading="lazy"
                  />
                  {findCountryLabel(contact.country)}
                </p>
                <img
                  width={388}
                  height={253}
                  src={contact.img.src}
                  alt={contact.alt}
                  loading="lazy"
                />
              </div>
              <p className="sub-heading text-blue">{contact.title}</p>
              <div>
                {contact.locations.map((location, locIndex) => (
                  <p className="row-item" key={locIndex}>
                    <span>
                      <SvglocationPin />
                    </span>
                    {location}
                  </p>
                ))}
                <p className="row-item">
                  <span>
                    <SvgcallUs />
                  </span>
                  {contact.phones.map((phone, phoneIndex) => (
                    <React.Fragment key={phoneIndex}>
                      {contact.phones.length > 1 ? (
                        <>
                          {phone} <br />
                        </>
                      ) : (
                        phone
                      )}
                    </React.Fragment>
                  ))}
                </p>
                <p className="row-item">
                  <span>
                    <Svgemail />
                  </span>
                  {contact.email}
                </p>
              </div>
            </div>
            <div className="btns">
              <CustomButton
                text={t("africa:locations:exploreMore")}
                className={"btn btn-outlined "}
                link={contact.link}
              />
              <CustomButton
                text={t("africa:locations:viewOnMap")}
                className={"btn btn-ghost"}
                link={contact.link}
                icon={<SvgArrow />}
              />
            </div>
          </div>
        ))}
      </Container>
    </div>
  );
}

export default LocationsInAfrica;
