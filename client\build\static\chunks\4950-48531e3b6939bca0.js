"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4950],{10795:function(e,t,n){let r;n.d(t,{LB:function(){return ez},g4:function(){return v},VK:function(){return X},Cj:function(){return eU},O1:function(){return eJ},Zj:function(){return eY}});var l,i,a,o,u,s,c,d,f,h,g,p,v,b,m,y,w,x,D,E=n(2265),C=n(54887),S=n(59892);let R={display:"none"};function M(e){let{id:t,value:n}=e;return E.createElement("div",{id:t,style:R},n)}function L(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return E.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let k=(0,E.createContext)(null),O={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},T={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function I(e){let{announcements:t=T,container:n,hiddenTextDescribedById:r,screenReaderInstructions:l=O}=e,{announce:i,announcement:a}=function(){let[e,t]=(0,E.useState)("");return{announce:(0,E.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),o=(0,S.Ld)("DndLiveRegion"),[u,s]=(0,E.useState)(!1);if((0,E.useEffect)(()=>{s(!0)},[]),!function(e){let t=(0,E.useContext)(k);(0,E.useEffect)(()=>{if(!t)throw Error("useDndMonitor must be used within a children of <DndContext>");return t(e)},[e,t])}((0,E.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t])),!u)return null;let c=E.createElement(E.Fragment,null,E.createElement(M,{id:r,value:l.draggable}),E.createElement(L,{id:o,announcement:a}));return n?(0,C.createPortal)(c,n):c}function N(){}(l=h||(h={})).DragStart="dragStart",l.DragMove="dragMove",l.DragEnd="dragEnd",l.DragCancel="dragCancel",l.DragOver="dragOver",l.RegisterDroppable="registerDroppable",l.SetDroppableDisabled="setDroppableDisabled",l.UnregisterDroppable="unregisterDroppable";let j=Object.freeze({x:0,y:0});function A(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}let P=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=[];for(let e of r){let{id:r}=e,i=n.get(r);if(i){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),l=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(r<l&&n<i){let a=t.width*t.height,o=e.width*e.height,u=(l-r)*(i-n);return Number((u/(a+o-u)).toFixed(4))}return 0}(i,t);n>0&&l.push({id:r,data:{droppableContainer:e,value:n}})}}return l.sort(A)};function B(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:j}let z=function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.reduce((e,t)=>({...e,top:e.top+1*t.y,bottom:e.bottom+1*t.y,left:e.left+1*t.x,right:e.right+1*t.x}),{...e})},F={ignoreTransform:!1};function X(e,t){void 0===t&&(t=F);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=(0,S.Jj)(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=function(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;let{scaleX:l,scaleY:i,x:a,y:o}=r,u=e.left-a-(1-l)*parseFloat(n),s=e.top-o-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),c=l?e.width/l:e.width,d=i?e.height/i:e.height;return{width:c,height:d,top:s,right:u+c,bottom:s+d,left:u}}(n,t,r))}let{top:r,left:l,width:i,height:a,bottom:o,right:u}=n;return{top:r,left:l,width:i,height:a,bottom:o,right:u}}function J(e){return X(e,{ignoreTransform:!0})}function U(e,t){let n=[];return e?function r(l){var i;if(null!=t&&n.length>=t||!l)return n;if((0,S.qk)(l)&&null!=l.scrollingElement&&!n.includes(l.scrollingElement))return n.push(l.scrollingElement),n;if(!(0,S.Re)(l)||(0,S.vZ)(l)||n.includes(l))return n;let a=(0,S.Jj)(e).getComputedStyle(l);return(l!==e&&function(e,t){void 0===t&&(t=(0,S.Jj)(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(l,a)&&n.push(l),void 0===(i=a)&&(i=(0,S.Jj)(l).getComputedStyle(l)),"fixed"===i.position)?n:r(l.parentNode)}(e):n}function H(e){let[t]=U(e,1);return null!=t?t:null}function Y(e){return S.Nq&&e?(0,S.FJ)(e)?e:(0,S.UG)(e)?(0,S.qk)(e)||e===(0,S.r3)(e).scrollingElement?window:(0,S.Re)(e)?e:null:null:null}function K(e){return(0,S.FJ)(e)?e.scrollX:e.scrollLeft}function W(e){return(0,S.FJ)(e)?e.scrollY:e.scrollTop}function q(e){return{x:K(e),y:W(e)}}function G(e){return!!S.Nq&&!!e&&e===document.scrollingElement}function V(e){let t={x:0,y:0},n=G(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},l=e.scrollTop<=t.y;return{isTop:l,isLeft:e.scrollLeft<=t.x,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}(i=g||(g={}))[i.Forward=1]="Forward",i[i.Backward=-1]="Backward";let _={x:.2,y:.2};function Z(e){return e.reduce((e,t)=>(0,S.IH)(e,q(t)),j)}let $=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+K(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+W(t),0)}]];class Q{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=U(t),r=Z(n);for(let[t,l,i]of(this.rect={...e},this.width=e.width,this.height=e.height,$))for(let e of l)Object.defineProperty(this,e,{get:()=>{let l=i(n),a=r[t]-l;return this.rect[e]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class ee{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function et(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function en(e){e.preventDefault()}function er(e){e.stopPropagation()}(a=p||(p={})).Click="click",a.DragStart="dragstart",a.Keydown="keydown",a.ContextMenu="contextmenu",a.Resize="resize",a.SelectionChange="selectionchange",a.VisibilityChange="visibilitychange",(o=v||(v={})).Space="Space",o.Down="ArrowDown",o.Right="ArrowRight",o.Left="ArrowLeft",o.Up="ArrowUp",o.Esc="Escape",o.Enter="Enter",o.Tab="Tab";let el={start:[v.Space,v.Enter],cancel:[v.Esc],end:[v.Space,v.Enter,v.Tab]},ei=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case v.Right:return{...n,x:n.x+25};case v.Left:return{...n,x:n.x-25};case v.Down:return{...n,y:n.y+25};case v.Up:return{...n,y:n.y-25}}};class ea{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new ee((0,S.r3)(t)),this.windowListeners=new ee((0,S.Jj)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(p.Resize,this.handleCancel),this.windowListeners.add(p.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(p.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=X),!e)return;let{top:n,left:r,bottom:l,right:i}=t(e);H(e)&&(l<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(j)}handleKeyDown(e){if((0,S.vd)(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:l=el,coordinateGetter:i=ei,scrollBehavior:a="smooth"}=r,{code:o}=e;if(l.end.includes(o)){this.handleEnd(e);return}if(l.cancel.includes(o)){this.handleCancel(e);return}let{collisionRect:u}=n.current,s=u?{x:u.left,y:u.top}:j;this.referenceCoordinates||(this.referenceCoordinates=s);let c=i(e,{active:t,context:n.current,currentCoordinates:s});if(c){let t=(0,S.$X)(c,s),r={x:0,y:0},{scrollableAncestors:l}=n.current;for(let n of l){let l=e.code,{isTop:i,isRight:o,isLeft:u,isBottom:s,maxScroll:d,minScroll:f}=V(n),h=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:l}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:l,width:e.clientWidth,height:e.clientHeight}}(n),g={x:Math.min(l===v.Right?h.right-h.width/2:h.right,Math.max(l===v.Right?h.left:h.left+h.width/2,c.x)),y:Math.min(l===v.Down?h.bottom-h.height/2:h.bottom,Math.max(l===v.Down?h.top:h.top+h.height/2,c.y))},p=l===v.Right&&!o||l===v.Left&&!u,b=l===v.Down&&!s||l===v.Up&&!i;if(p&&g.x!==c.x){let e=n.scrollLeft+t.x,i=l===v.Right&&e<=d.x||l===v.Left&&e>=f.x;if(i&&!t.y){n.scrollTo({left:e,behavior:a});return}i?r.x=n.scrollLeft-e:r.x=l===v.Right?n.scrollLeft-d.x:n.scrollLeft-f.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(b&&g.y!==c.y){let e=n.scrollTop+t.y,i=l===v.Down&&e<=d.y||l===v.Up&&e>=f.y;if(i&&!t.x){n.scrollTo({top:e,behavior:a});return}i?r.y=n.scrollTop-e:r.y=l===v.Down?n.scrollTop-d.y:n.scrollTop-f.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,(0,S.IH)((0,S.$X)(c,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function eo(e){return!!(e&&"distance"in e)}function eu(e){return!!(e&&"delay"in e)}ea.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=el,onActivation:l}=t,{active:i}=n,{code:a}=e.nativeEvent;if(r.start.includes(a)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==l||l({event:e.nativeEvent}),!0)}return!1}}];class es{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=(0,S.Jj)(e);return e instanceof t?e:(0,S.r3)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=(0,S.r3)(i),this.documentListeners=new ee(this.document),this.listeners=new ee(n),this.windowListeners=new ee((0,S.Jj)(i)),this.initialCoordinates=null!=(r=(0,S.DC)(l))?r:j,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(p.Resize,this.handleCancel),this.windowListeners.add(p.DragStart,en),this.windowListeners.add(p.VisibilityChange,this.handleCancel),this.windowListeners.add(p.ContextMenu,en),this.documentListeners.add(p.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(eu(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(eo(t)){this.handlePending(t);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(p.Click,er,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(p.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:l}=this,{onMove:i,options:{activationConstraint:a}}=l;if(!r)return;let o=null!=(t=(0,S.DC)(e))?t:j,u=(0,S.$X)(r,o);if(!n&&a){if(eo(a)){if(null!=a.tolerance&&et(u,a.tolerance))return this.handleCancel();if(et(u,a.distance))return this.handleStart()}return eu(a)&&et(u,a.tolerance)?this.handleCancel():void this.handlePending(a,u)}e.cancelable&&e.preventDefault(),i(o)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===v.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let ec={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class ed extends es{constructor(e){let{event:t}=e;super(e,ec,(0,S.r3)(t.target))}}ed.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let ef={move:{name:"mousemove"},end:{name:"mouseup"}};(u=b||(b={}))[u.RightClick=2]="RightClick";class eh extends es{constructor(e){super(e,ef,(0,S.r3)(e.event.target))}}eh.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==b.RightClick&&(null==r||r({event:n}),!0)}}];let eg={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class ep extends es{constructor(e){super(e,eg)}static setup(){return window.addEventListener(eg.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(eg.move.name,e)};function e(){}}}ep.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:l}=n;return!(l.length>1)&&(null==r||r({event:n}),!0)}}],(s=m||(m={}))[s.Pointer=0]="Pointer",s[s.DraggableRect=1]="DraggableRect",(c=y||(y={}))[c.TreeOrder=0]="TreeOrder",c[c.ReversedTreeOrder=1]="ReversedTreeOrder";let ev={x:{[g.Backward]:!1,[g.Forward]:!1},y:{[g.Backward]:!1,[g.Forward]:!1}};(d=w||(w={}))[d.Always=0]="Always",d[d.BeforeDragging=1]="BeforeDragging",d[d.WhileDragging=2]="WhileDragging",(x||(x={})).Optimized="optimized";let eb=new Map;function em(e,t){return(0,S.Gj)(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function ey(e){let{callback:t,disabled:n}=e,r=(0,S.zX)(t),l=(0,E.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,E.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}function ew(e){return new Q(X(e),e)}function ex(e,t,n){void 0===t&&(t=ew);let[r,l]=(0,E.useState)(null);function i(){l(r=>{if(!e)return null;if(!1===e.isConnected){var l;return null!=(l=null!=r?r:n)?l:null}let i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i})}let a=function(e){let{callback:t,disabled:n}=e,r=(0,S.zX)(t),l=(0,E.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,E.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),o=ey({callback:i});return(0,S.LI)(()=>{i(),e?(null==o||o.observe(e),null==a||a.observe(document.body,{childList:!0,subtree:!0})):(null==o||o.disconnect(),null==a||a.disconnect())},[e]),r}let eD=[];function eE(e,t){void 0===t&&(t=[]);let n=(0,E.useRef)(null);return(0,E.useEffect)(()=>{n.current=null},t),(0,E.useEffect)(()=>{let t=e!==j;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?(0,S.$X)(e,n.current):j}function eC(e){return(0,E.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let eS=[],eR=[{sensor:ed,options:{}},{sensor:ea,options:{}}],eM={current:{}},eL={draggable:{measure:J},droppable:{measure:J,strategy:w.WhileDragging,frequency:x.Optimized},dragOverlay:{measure:X}};class ek extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let eO={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new ek,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:N},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eL,measureDroppableContainers:N,windowRect:null,measuringScheduled:!1},eT={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:N,draggableNodes:new Map,over:null,measureDroppableContainers:N},eI=(0,E.createContext)(eT),eN=(0,E.createContext)(eO);function ej(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new ek}}}function eA(e,t){switch(t.type){case h.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case h.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case h.DragEnd:case h.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case h.RegisterDroppable:{let{element:n}=t,{id:r}=n,l=new ek(e.droppable.containers);return l.set(r,n),{...e,droppable:{...e.droppable,containers:l}}}case h.SetDroppableDisabled:{let{id:n,key:r,disabled:l}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;let a=new ek(e.droppable.containers);return a.set(n,{...i,disabled:l}),{...e,droppable:{...e.droppable,containers:a}}}case h.UnregisterDroppable:{let{id:n,key:r}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;let i=new ek(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function eP(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:l}=(0,E.useContext)(eI),i=(0,S.D9)(r),a=(0,S.D9)(null==n?void 0:n.id);return(0,E.useEffect)(()=>{if(!t&&!r&&i&&null!=a){if(!(0,S.vd)(i)||document.activeElement===i.target)return;let e=l.get(a);if(!e)return;let{activatorNode:t,node:n}=e;(t.current||n.current)&&requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=(0,S.so)(e);if(t){t.focus();break}}})}},[r,t,l,a,i]),null}let eB=(0,E.createContext)({...j,scaleX:1,scaleY:1});(f=D||(D={}))[f.Uninitialized=0]="Uninitialized",f[f.Initializing=1]="Initializing",f[f.Initialized=2]="Initialized";let ez=(0,E.memo)(function(e){var t,n,r,l,i,a;let{id:o,accessibility:u,autoScroll:s=!0,children:c,sensors:d=eR,collisionDetection:f=P,measuring:p,modifiers:v,...b}=e,[x,R]=(0,E.useReducer)(eA,void 0,ej),[M,L]=function(){let[e]=(0,E.useState)(()=>new Set),t=(0,E.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,E.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[O,T]=(0,E.useState)(D.Uninitialized),N=O===D.Initialized,{draggable:{active:A,nodes:F,translate:J},droppable:{containers:K}}=x,W=null!=A?F.get(A):null,$=(0,E.useRef)({initial:null,translated:null}),ee=(0,E.useMemo)(()=>{var e;return null!=A?{id:A,data:null!=(e=null==W?void 0:W.data)?e:eM,rect:$}:null},[A,W]),et=(0,E.useRef)(null),[en,er]=(0,E.useState)(null),[el,ei]=(0,E.useState)(null),ea=(0,S.Ey)(b,Object.values(b)),eo=(0,S.Ld)("DndDescribedBy",o),eu=(0,E.useMemo)(()=>K.getEnabled(),[K]),es=(0,E.useMemo)(()=>({draggable:{...eL.draggable,...null==p?void 0:p.draggable},droppable:{...eL.droppable,...null==p?void 0:p.droppable},dragOverlay:{...eL.dragOverlay,...null==p?void 0:p.dragOverlay}}),[null==p?void 0:p.draggable,null==p?void 0:p.droppable,null==p?void 0:p.dragOverlay]),{droppableRects:ec,measureDroppableContainers:ed,measuringScheduled:ef}=function(e,t){let{dragging:n,dependencies:r,config:l}=t,[i,a]=(0,E.useState)(null),{frequency:o,measure:u,strategy:s}=l,c=(0,E.useRef)(e),d=function(){switch(s){case w.Always:return!1;case w.BeforeDragging:return n;default:return!n}}(),f=(0,S.Ey)(d),h=(0,E.useCallback)(function(e){void 0===e&&(e=[]),f.current||a(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[f]),g=(0,E.useRef)(null),p=(0,S.Gj)(t=>{if(d&&!n)return eb;if(!t||t===eb||c.current!==e||null!=i){let t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new Q(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,d,u]);return(0,E.useEffect)(()=>{c.current=e},[e]),(0,E.useEffect)(()=>{d||h()},[n,d]),(0,E.useEffect)(()=>{i&&i.length>0&&a(null)},[JSON.stringify(i)]),(0,E.useEffect)(()=>{d||"number"!=typeof o||null!==g.current||(g.current=setTimeout(()=>{h(),g.current=null},o))},[o,d,h,...r]),{droppableRects:p,measureDroppableContainers:h,measuringScheduled:null!=i}}(eu,{dragging:N,dependencies:[J.x,J.y],config:es.droppable}),eh=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return(0,S.Gj)(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(F,A),eg=(0,E.useMemo)(()=>el?(0,S.DC)(el):null,[el]),ep=function(){let e=(null==en?void 0:en.autoScrollEnabled)===!1,t="object"==typeof s?!1===s.enabled:!1===s,n=N&&!e&&!t;return"object"==typeof s?{...s,enabled:n}:{enabled:n}}(),ew=em(eh,es.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:l=!0}=e,i=(0,E.useRef)(!1),{x:a,y:o}="boolean"==typeof l?{x:l,y:l}:l;(0,S.LI)(()=>{if(!a&&!o||!t){i.current=!1;return}if(i.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let l=B(n(e),r);if(a||(l.x=0),o||(l.y=0),i.current=!0,Math.abs(l.x)>0||Math.abs(l.y)>0){let t=H(e);t&&t.scrollBy({top:l.y,left:l.x})}},[t,a,o,r,n])}({activeNode:null!=A?F.get(A):null,config:ep.layoutShiftCompensation,initialRect:ew,measure:es.draggable.measure});let ek=ex(eh,es.draggable.measure,ew),eO=ex(eh?eh.parentElement:null),eT=(0,E.useRef)({activatorEvent:null,active:null,activeNode:eh,collisionRect:null,collisions:null,droppableRects:ec,draggableNodes:F,draggingNode:null,draggingNodeRect:null,droppableContainers:K,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),ez=K.getNodeFor(null==(t=eT.current.over)?void 0:t.id),eF=function(e){let{measure:t}=e,[n,r]=(0,E.useState)(null),l=ey({callback:(0,E.useCallback)(e=>{for(let{target:n}of e)if((0,S.Re)(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),i=(0,E.useCallback)(e=>{let n=function(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return(0,S.Re)(t)?t:e}(e);null==l||l.disconnect(),n&&(null==l||l.observe(n)),r(n?t(n):null)},[t,l]),[a,o]=(0,S.wm)(i);return(0,E.useMemo)(()=>({nodeRef:a,rect:n,setRef:o}),[n,a,o])}({measure:es.dragOverlay.measure}),eX=null!=(n=eF.nodeRef.current)?n:eh,eJ=N?null!=(r=eF.rect)?r:ek:null,eU=!!(eF.nodeRef.current&&eF.rect),eH=function(e){let t=em(e);return B(e,t)}(eU?null:ek),eY=eC(eX?(0,S.Jj)(eX):null),eK=function(e){let t=(0,E.useRef)(e),n=(0,S.Gj)(n=>e?n&&n!==eD&&e&&t.current&&e.parentNode===t.current.parentNode?n:U(e):eD,[e]);return(0,E.useEffect)(()=>{t.current=e},[e]),n}(N?null!=ez?ez:eh:null),eW=function(e,t){void 0===t&&(t=X);let[n]=e,r=eC(n?(0,S.Jj)(n):null),[l,i]=(0,E.useState)(eS);function a(){i(()=>e.length?e.map(e=>G(e)?r:new Q(t(e),e)):eS)}let o=ey({callback:a});return(0,S.LI)(()=>{null==o||o.disconnect(),a(),e.forEach(e=>null==o?void 0:o.observe(e))},[e]),l}(eK),eq=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}(v,{transform:{x:J.x-eH.x,y:J.y-eH.y,scaleX:1,scaleY:1},activatorEvent:el,active:ee,activeNodeRect:ek,containerNodeRect:eO,draggingNodeRect:eJ,over:eT.current.over,overlayNodeRect:eF.rect,scrollableAncestors:eK,scrollableAncestorRects:eW,windowRect:eY}),eG=eg?(0,S.IH)(eg,J):null,eV=function(e){let[t,n]=(0,E.useState)(null),r=(0,E.useRef)(e),l=(0,E.useCallback)(e=>{let t=Y(e.target);t&&n(e=>e?(e.set(t,q(t)),new Map(e)):null)},[]);return(0,E.useEffect)(()=>{let t=r.current;if(e!==t){i(t);let a=e.map(e=>{let t=Y(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,q(t)]):null}).filter(e=>null!=e);n(a.length?new Map(a):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=Y(e);null==t||t.removeEventListener("scroll",l)})}},[l,e]),(0,E.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>(0,S.IH)(e,t),j):Z(e):j,[e,t])}(eK),e_=eE(eV),eZ=eE(eV,[ek]),e$=(0,S.IH)(eq,e_),eQ=eJ?z(eJ,eq):null,e0=ee&&eQ?f({active:ee,collisionRect:eQ,droppableRects:ec,droppableContainers:eu,pointerCoordinates:eG}):null,e1=function(e,t){if(!e||0===e.length)return null;let[n]=e;return n.id}(e0,0),[e2,e5]=(0,E.useState)(null),e9=(i=eU?eq:(0,S.IH)(eq,eZ),a=null!=(l=null==e2?void 0:e2.rect)?l:null,{...i,scaleX:a&&ek?a.width/ek.width:1,scaleY:a&&ek?a.height/ek.height:1}),e3=(0,E.useRef)(null),e4=(0,E.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==et.current)return;let l=F.get(et.current);if(!l)return;let i=e.nativeEvent,a=new n({active:et.current,activeNode:l,event:i,options:r,context:eT,onAbort(e){if(!F.get(e))return;let{onDragAbort:t}=ea.current,n={id:e};null==t||t(n),M({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!F.get(e))return;let{onDragPending:l}=ea.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==l||l(i),M({type:"onDragPending",event:i})},onStart(e){let t=et.current;if(null==t)return;let n=F.get(t);if(!n)return;let{onDragStart:r}=ea.current,l={activatorEvent:i,active:{id:t,data:n.data,rect:$}};(0,C.unstable_batchedUpdates)(()=>{null==r||r(l),T(D.Initializing),R({type:h.DragStart,initialCoordinates:e,active:t}),M({type:"onDragStart",event:l}),er(e3.current),ei(i)})},onMove(e){R({type:h.DragMove,coordinates:e})},onEnd:o(h.DragEnd),onCancel:o(h.DragCancel)});function o(e){return async function(){let{active:t,collisions:n,over:r,scrollAdjustedTranslate:l}=eT.current,a=null;if(t&&l){let{cancelDrop:o}=ea.current;a={activatorEvent:i,active:t,collisions:n,delta:l,over:r},e===h.DragEnd&&"function"==typeof o&&await Promise.resolve(o(a))&&(e=h.DragCancel)}et.current=null,(0,C.unstable_batchedUpdates)(()=>{R({type:e}),T(D.Uninitialized),e5(null),er(null),ei(null),e3.current=null;let t=e===h.DragEnd?"onDragEnd":"onDragCancel";if(a){let e=ea.current[t];null==e||e(a),M({type:t,event:a})}})}}e3.current=a},[F]),e6=(0,E.useCallback)((e,t)=>(n,r)=>{let l=n.nativeEvent,i=F.get(r);null!==et.current||!i||l.dndKit||l.defaultPrevented||!0!==e(n,t.options,{active:i})||(l.dndKit={capturedBy:t.sensor},et.current=r,e4(n,t))},[F,e4]),e8=(0,E.useMemo)(()=>d.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:e6(e.handler,t)}))]},[]),[d,e6]);(0,E.useEffect)(()=>{if(!S.Nq)return;let e=d.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},d.map(e=>{let{sensor:t}=e;return t})),(0,S.LI)(()=>{ek&&O===D.Initializing&&T(D.Initialized)},[ek,O]),(0,E.useEffect)(()=>{let{onDragMove:e}=ea.current,{active:t,activatorEvent:n,collisions:r,over:l}=eT.current;if(!t||!n)return;let i={active:t,activatorEvent:n,collisions:r,delta:{x:e$.x,y:e$.y},over:l};(0,C.unstable_batchedUpdates)(()=>{null==e||e(i),M({type:"onDragMove",event:i})})},[e$.x,e$.y]),(0,E.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:l}=eT.current;if(!e||null==et.current||!t||!l)return;let{onDragOver:i}=ea.current,a=r.get(e1),o=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null,u={active:e,activatorEvent:t,collisions:n,delta:{x:l.x,y:l.y},over:o};(0,C.unstable_batchedUpdates)(()=>{e5(o),null==i||i(u),M({type:"onDragOver",event:u})})},[e1]),(0,S.LI)(()=>{eT.current={activatorEvent:el,active:ee,activeNode:eh,collisionRect:eQ,collisions:e0,droppableRects:ec,draggableNodes:F,draggingNode:eX,draggingNodeRect:eJ,droppableContainers:K,over:e2,scrollableAncestors:eK,scrollAdjustedTranslate:e$},$.current={initial:eJ,translated:eQ}},[ee,eh,e0,eQ,F,eX,eJ,ec,K,e2,eK,e$]),function(e){let{acceleration:t,activator:n=m.Pointer,canScroll:r,draggingRect:l,enabled:i,interval:a=5,order:o=y.TreeOrder,pointerCoordinates:u,scrollableAncestors:s,scrollableAncestorRects:c,delta:d,threshold:f}=e,h=function(e){let{delta:t,disabled:n}=e,r=(0,S.D9)(t);return(0,S.Gj)(e=>{if(n||!r||!e)return ev;let l={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[g.Backward]:e.x[g.Backward]||-1===l.x,[g.Forward]:e.x[g.Forward]||1===l.x},y:{[g.Backward]:e.y[g.Backward]||-1===l.y,[g.Forward]:e.y[g.Forward]||1===l.y}}},[n,t,r])}({delta:d,disabled:!i}),[p,v]=(0,S.Yz)(),b=(0,E.useRef)({x:0,y:0}),w=(0,E.useRef)({x:0,y:0}),x=(0,E.useMemo)(()=>{switch(n){case m.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case m.DraggableRect:return l}},[n,l,u]),D=(0,E.useRef)(null),C=(0,E.useCallback)(()=>{let e=D.current;if(!e)return;let t=b.current.x*w.current.x,n=b.current.y*w.current.y;e.scrollBy(t,n)},[]),R=(0,E.useMemo)(()=>o===y.TreeOrder?[...s].reverse():s,[o,s]);(0,E.useEffect)(()=>{if(!i||!s.length||!x){v();return}for(let e of R){if((null==r?void 0:r(e))===!1)continue;let n=c[s.indexOf(e)];if(!n)continue;let{direction:l,speed:i}=function(e,t,n,r,l){let{top:i,left:a,right:o,bottom:u}=n;void 0===r&&(r=10),void 0===l&&(l=_);let{isTop:s,isBottom:c,isLeft:d,isRight:f}=V(e),h={x:0,y:0},p={x:0,y:0},v={height:t.height*l.y,width:t.width*l.x};return!s&&i<=t.top+v.height?(h.y=g.Backward,p.y=r*Math.abs((t.top+v.height-i)/v.height)):!c&&u>=t.bottom-v.height&&(h.y=g.Forward,p.y=r*Math.abs((t.bottom-v.height-u)/v.height)),!f&&o>=t.right-v.width?(h.x=g.Forward,p.x=r*Math.abs((t.right-v.width-o)/v.width)):!d&&a<=t.left+v.width&&(h.x=g.Backward,p.x=r*Math.abs((t.left+v.width-a)/v.width)),{direction:h,speed:p}}(e,n,x,t,f);for(let e of["x","y"])h[e][l[e]]||(i[e]=0,l[e]=0);if(i.x>0||i.y>0){v(),D.current=e,p(C,a),b.current=i,w.current=l;return}}b.current={x:0,y:0},w.current={x:0,y:0},v()},[t,C,r,v,i,a,JSON.stringify(x),JSON.stringify(h),p,s,R,c,JSON.stringify(f)])}({...ep,delta:J,draggingRect:eQ,pointerCoordinates:eG,scrollableAncestors:eK,scrollableAncestorRects:eW});let e7=(0,E.useMemo)(()=>({active:ee,activeNode:eh,activeNodeRect:ek,activatorEvent:el,collisions:e0,containerNodeRect:eO,dragOverlay:eF,draggableNodes:F,droppableContainers:K,droppableRects:ec,over:e2,measureDroppableContainers:ed,scrollableAncestors:eK,scrollableAncestorRects:eW,measuringConfiguration:es,measuringScheduled:ef,windowRect:eY}),[ee,eh,ek,el,e0,eO,eF,F,K,ec,e2,ed,eK,eW,es,ef,eY]),te=(0,E.useMemo)(()=>({activatorEvent:el,activators:e8,active:ee,activeNodeRect:ek,ariaDescribedById:{draggable:eo},dispatch:R,draggableNodes:F,over:e2,measureDroppableContainers:ed}),[el,e8,ee,ek,R,eo,F,e2,ed]);return E.createElement(k.Provider,{value:L},E.createElement(eI.Provider,{value:te},E.createElement(eN.Provider,{value:e7},E.createElement(eB.Provider,{value:e9},c)),E.createElement(eP,{disabled:(null==u?void 0:u.restoreFocus)===!1})),E.createElement(I,{...u,hiddenTextDescribedById:eo}))}),eF=(0,E.createContext)(null),eX="button";function eJ(e){let{id:t,data:n,disabled:r=!1,attributes:l}=e,i=(0,S.Ld)("Draggable"),{activators:a,activatorEvent:o,active:u,activeNodeRect:s,ariaDescribedById:c,draggableNodes:d,over:f}=(0,E.useContext)(eI),{role:h=eX,roleDescription:g="draggable",tabIndex:p=0}=null!=l?l:{},v=(null==u?void 0:u.id)===t,b=(0,E.useContext)(v?eB:eF),[m,y]=(0,S.wm)(),[w,x]=(0,S.wm)(),D=(0,E.useMemo)(()=>a.reduce((e,n)=>{let{eventName:r,handler:l}=n;return e[r]=e=>{l(e,t)},e},{}),[a,t]),C=(0,S.Ey)(n);return(0,S.LI)(()=>(d.set(t,{id:t,key:i,node:m,activatorNode:w,data:C}),()=>{let e=d.get(t);e&&e.key===i&&d.delete(t)}),[d,t]),{active:u,activatorEvent:o,activeNodeRect:s,attributes:(0,E.useMemo)(()=>({role:h,tabIndex:p,"aria-disabled":r,"aria-pressed":!!v&&h===eX||void 0,"aria-roledescription":g,"aria-describedby":c.draggable}),[r,h,p,v,g,c.draggable]),isDragging:v,listeners:r?void 0:D,node:m,over:f,setNodeRef:y,setActivatorNodeRef:x,transform:b}}function eU(){return(0,E.useContext)(eN)}let eH={timeout:25};function eY(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:l}=e,i=(0,S.Ld)("Droppable"),{active:a,dispatch:o,over:u,measureDroppableContainers:s}=(0,E.useContext)(eI),c=(0,E.useRef)({disabled:n}),d=(0,E.useRef)(!1),f=(0,E.useRef)(null),g=(0,E.useRef)(null),{disabled:p,updateMeasurementsFor:v,timeout:b}={...eH,...l},m=(0,S.Ey)(null!=v?v:r),y=ey({callback:(0,E.useCallback)(()=>{if(!d.current){d.current=!0;return}null!=g.current&&clearTimeout(g.current),g.current=setTimeout(()=>{s(Array.isArray(m.current)?m.current:[m.current]),g.current=null},b)},[b]),disabled:p||!a}),w=(0,E.useCallback)((e,t)=>{y&&(t&&(y.unobserve(t),d.current=!1),e&&y.observe(e))},[y]),[x,D]=(0,S.wm)(w),C=(0,S.Ey)(t);return(0,E.useEffect)(()=>{y&&x.current&&(y.disconnect(),d.current=!1,y.observe(x.current))},[x,y]),(0,E.useEffect)(()=>(o({type:h.RegisterDroppable,element:{id:r,key:i,disabled:n,node:x,rect:f,data:C}}),()=>o({type:h.UnregisterDroppable,key:i,id:r})),[r]),(0,E.useEffect)(()=>{n!==c.current.disabled&&(o({type:h.SetDroppableDisabled,id:r,key:i,disabled:n}),c.current.disabled=n)},[r,i,n,o]),{active:a,rect:f,isOver:(null==u?void 0:u.id)===r,node:x,over:u,setNodeRef:D}}r={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:n}=e,l={},{styles:i,className:a}=r;if(null!=i&&i.active)for(let[e,n]of Object.entries(i.active))void 0!==n&&(l[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,n));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=a&&a.active&&t.node.classList.add(a.active),null!=a&&a.dragOverlay&&n.node.classList.add(a.dragOverlay),function(){for(let[e,n]of Object.entries(l))t.node.style.setProperty(e,n);null!=a&&a.active&&t.node.classList.remove(a.active)}}},93662:function(e,t,n){n.d(t,{Fo:function(){return h},nB:function(){return w},qw:function(){return c}});var r=n(2265),l=n(10795),i=n(59892);function a(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function o(e){return null!==e&&e>=0}let u=e=>{let{rects:t,activeIndex:n,overIndex:r,index:l}=e,i=a(t,r,n),o=t[l],u=i[l];return u&&o?{x:u.left-o.left,y:u.top-o.top,scaleX:u.width/o.width,scaleY:u.height/o.height}:null},s={scaleX:1,scaleY:1},c=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:l,rects:i,overIndex:a}=e,o=null!=(t=i[n])?t:r;if(!o)return null;if(l===n){let e=i[a];return e?{x:0,y:n<a?e.top+e.height-(o.top+o.height):e.top-o.top,...s}:null}let u=function(e,t,n){let r=e[t],l=e[t-1],i=e[t+1];return r?n<t?l?r.top-(l.top+l.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):l?r.top-(l.top+l.height):0:0}(i,l,n);return l>n&&l<=a?{x:0,y:-o.height-u,...s}:l<n&&l>=a?{x:0,y:o.height+u,...s}:{x:0,y:0,...s}},d="Sortable",f=r.createContext({activeIndex:-1,containerId:d,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:u,disabled:{draggable:!1,droppable:!1}});function h(e){let{children:t,id:n,items:a,strategy:o=u,disabled:s=!1}=e,{active:c,dragOverlay:h,droppableRects:g,over:p,measureDroppableContainers:v}=(0,l.Cj)(),b=(0,i.Ld)(d,n),m=null!==h.rect,y=(0,r.useMemo)(()=>a.map(e=>"object"==typeof e&&"id"in e?e.id:e),[a]),w=null!=c,x=c?y.indexOf(c.id):-1,D=p?y.indexOf(p.id):-1,E=(0,r.useRef)(y),C=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(y,E.current),S=-1!==D&&-1===x||C,R="boolean"==typeof s?{draggable:s,droppable:s}:s;(0,i.LI)(()=>{C&&w&&v(y)},[C,y,w,v]),(0,r.useEffect)(()=>{E.current=y},[y]);let M=(0,r.useMemo)(()=>({activeIndex:x,containerId:b,disabled:R,disableTransforms:S,items:y,overIndex:D,useDragOverlay:m,sortedRects:y.reduce((e,t,n)=>{let r=g.get(t);return r&&(e[n]=r),e},Array(y.length)),strategy:o}),[x,b,R.draggable,R.droppable,S,y,D,g,m,o]);return r.createElement(f.Provider,{value:M},t)}let g=e=>{let{id:t,items:n,activeIndex:r,overIndex:l}=e;return a(n,r,l).indexOf(t)},p=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:l,items:i,newIndex:a,previousItems:o,previousContainerId:u,transition:s}=e;return!!s&&!!r&&(o===i||l!==a)&&(!!n||a!==l&&t===u)},v={duration:200,easing:"ease"},b="transform",m=i.ux.Transition.toString({property:b,duration:0,easing:"linear"}),y={roleDescription:"sortable"};function w(e){var t,n;let{animateLayoutChanges:a=p,attributes:u,disabled:s,data:c,getNewIndex:d=g,id:h,strategy:w,resizeObserverConfig:x,transition:D=v}=e,{items:E,containerId:C,activeIndex:S,disabled:R,disableTransforms:M,sortedRects:L,overIndex:k,useDragOverlay:O,strategy:T}=(0,r.useContext)(f),I="boolean"==typeof s?{draggable:s,droppable:!1}:{draggable:null!=(t=null==s?void 0:s.draggable)?t:R.draggable,droppable:null!=(n=null==s?void 0:s.droppable)?n:R.droppable},N=E.indexOf(h),j=(0,r.useMemo)(()=>({sortable:{containerId:C,index:N,items:E},...c}),[C,c,N,E]),A=(0,r.useMemo)(()=>E.slice(E.indexOf(h)),[E,h]),{rect:P,node:B,isOver:z,setNodeRef:F}=(0,l.Zj)({id:h,data:j,disabled:I.droppable,resizeObserverConfig:{updateMeasurementsFor:A,...x}}),{active:X,activatorEvent:J,activeNodeRect:U,attributes:H,setNodeRef:Y,listeners:K,isDragging:W,over:q,setActivatorNodeRef:G,transform:V}=(0,l.O1)({id:h,data:j,attributes:{...y,...u},disabled:I.draggable}),_=(0,i.HB)(F,Y),Z=!!X,$=Z&&!M&&o(S)&&o(k),Q=!O&&W,ee=Q&&$?V:null,et=$?null!=ee?ee:(null!=w?w:T)({rects:L,activeNodeRect:U,activeIndex:S,overIndex:k,index:N}):null,en=o(S)&&o(k)?d({id:h,items:E,activeIndex:S,overIndex:k}):N,er=null==X?void 0:X.id,el=(0,r.useRef)({activeId:er,items:E,newIndex:en,containerId:C}),ei=E!==el.current.items,ea=a({active:X,containerId:C,isDragging:W,isSorting:Z,id:h,index:N,items:E,newIndex:el.current.newIndex,previousItems:el.current.items,previousContainerId:el.current.containerId,transition:D,wasDragging:null!=el.current.activeId}),eo=function(e){let{disabled:t,index:n,node:a,rect:o}=e,[u,s]=(0,r.useState)(null),c=(0,r.useRef)(n);return(0,i.LI)(()=>{if(!t&&n!==c.current&&a.current){let e=o.current;if(e){let t=(0,l.VK)(a.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&s(n)}}n!==c.current&&(c.current=n)},[t,n,a,o]),(0,r.useEffect)(()=>{u&&s(null)},[u]),u}({disabled:!ea,index:N,node:B,rect:P});return(0,r.useEffect)(()=>{Z&&el.current.newIndex!==en&&(el.current.newIndex=en),C!==el.current.containerId&&(el.current.containerId=C),E!==el.current.items&&(el.current.items=E)},[Z,en,C,E]),(0,r.useEffect)(()=>{if(er===el.current.activeId)return;if(null!=er&&null==el.current.activeId){el.current.activeId=er;return}let e=setTimeout(()=>{el.current.activeId=er},50);return()=>clearTimeout(e)},[er]),{active:X,activeIndex:S,attributes:H,data:j,rect:P,index:N,newIndex:en,items:E,isOver:z,isSorting:Z,isDragging:W,listeners:K,node:B,overIndex:k,over:q,setNodeRef:_,setActivatorNodeRef:G,setDroppableNodeRef:F,setDraggableNodeRef:Y,transform:null!=eo?eo:et,transition:eo||ei&&el.current.newIndex===N?m:(!Q||(0,i.vd)(J))&&D&&(Z||ea)?i.ux.Transition.toString({...D,property:b}):void 0}}l.g4.Down,l.g4.Right,l.g4.Up,l.g4.Left},59892:function(e,t,n){n.d(t,{$X:function(){return C},D9:function(){return y},DC:function(){return R},Ey:function(){return v},FJ:function(){return a},Gj:function(){return b},HB:function(){return l},IH:function(){return E},Jj:function(){return u},LI:function(){return h},Ld:function(){return x},Nq:function(){return i},Re:function(){return c},UG:function(){return o},Yz:function(){return p},qk:function(){return s},r3:function(){return f},so:function(){return k},ux:function(){return M},vZ:function(){return d},vd:function(){return S},wm:function(){return m},zX:function(){return g}});var r=n(2265);function l(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}let i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function a(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function o(e){return"nodeType"in e}function u(e){var t,n;return e?a(e)?e:o(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function s(e){let{Document:t}=u(e);return e instanceof t}function c(e){return!a(e)&&e instanceof u(e).HTMLElement}function d(e){return e instanceof u(e).SVGElement}function f(e){return e?a(e)?e.document:o(e)?s(e)?e:c(e)||d(e)?e.ownerDocument:document:document:document}let h=i?r.useLayoutEffect:r.useEffect;function g(e){let t=(0,r.useRef)(e);return h(()=>{t.current=e}),(0,r.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function p(){let e=(0,r.useRef)(null);return[(0,r.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,r.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}function v(e,t){void 0===t&&(t=[e]);let n=(0,r.useRef)(e);return h(()=>{n.current!==e&&(n.current=e)},t),n}function b(e,t){let n=(0,r.useRef)();return(0,r.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function m(e){let t=g(e),n=(0,r.useRef)(null),l=(0,r.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,l]}function y(e){let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)(()=>{if(t)return t;let n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n},[e,t])}function D(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>{for(let[r,l]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*l)}return t},{...t})}}let E=D(1),C=D(-1);function S(e){if(!e)return!1;let{KeyboardEvent:t}=u(e.target);return t&&e instanceof t}function R(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=u(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let M=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[M.Translate.toString(e),M.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),L="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function k(e){return e.matches(L)?e:e.querySelector(L)}},61994:function(e,t,n){t.Z=function(){for(var e,t,n=0,r="",l=arguments.length;n<l;n++)(e=arguments[n])&&(t=function e(t){var n,r,l="";if("string"==typeof t||"number"==typeof t)l+=t;else if("object"==typeof t){if(Array.isArray(t)){var i=t.length;for(n=0;n<i;n++)t[n]&&(r=e(t[n]))&&(l&&(l+=" "),l+=r)}else for(r in t)t[r]&&(l&&(l+=" "),l+=r)}return l}(e))&&(r&&(r+=" "),r+=t);return r}}}]);