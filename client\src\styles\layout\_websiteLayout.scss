#pentabell-header {
  // background-color: transparent;
  background: #23479126;
  backdrop-filter: blur(10px);

  transition: "background 0.3s ease";
  .icon-open-menu {
    svg {
      path {
        stroke: $white;
      }
    }
  }
  .pentabell-logo {
    display: flex;
    margin: 5px 0;

    img {
      // width: 200px;
      height: auto;
      @include media-query(mobile, tablet) {
        // width: 46px;
        margin-right: 15px;
      }
    }
  }
  .css-1oom1vg-MuiToolbar-root {
    padding: 8px 0;
  }
  &.scroll {
    background: #23479126;
    backdrop-filter: blur(10px);
  }

  .navbar-link,
  .dropdown-toggle-link {
    text-transform: capitalize;
    font-size: 16px;
    // padding: 6px 16px;
    height: auto;
    color: $white;
    text-decoration: none;

    @include media-query(laptops) {
      font-size: 14px;
    }

    svg {
      path {
        fill: $white;
      }
    }
    &:hover {
      background-color: transparent;
      font-family: "Proxima-Nova-Semibold" !important;
    }
    &.whiteBg {
      color: $blue;
      svg {
        path {
          fill: $blue !important;
        }
      }
    }
    &.active {
      color: $yellow;
      font-family: "Proxima-Nova-Semibold" !important;
      svg {
        path {
          fill: $yellow !important;
        }
      }
    }
  }
  .dropdown-toggle-link {
    display: contents;
    // display: flex;
    margin: auto;
  }

  .navbar-link:nth-last-child(2) {
    border: 1px solid $white !important;
    border-radius: 1px;
    &.whiteBg {
      border: 1px solid $blue !important;
    }
    &.active {
      border-color: $yellow !important;
    }
  }

  .menu {
    display: flex;
    align-items: center;
  }

  .dropdown-menu {
    width: auto;
  }
}

.flag-lang {
  display: flex;
  margin: auto;
}

// dropdonw menun style start
.my-profile-img {
  width: 48px;
  height: 48px;
  // background-color: $lightBlue;
  border-radius: 100%;
  // width: auto;
  img {
    width: 48px;
    height: 48px;
    border-radius: 100%;
  }
}
.sub-dropdown-menu {
  width: 100%;
  button {
    svg {
      path {
        fill: $blue !important;
      }
    }
  }
  button {
    width: 100%;
    justify-content: space-between;
  }
}

.dropdown-item {
  padding: 0 !important;

  button.dropdown-toggle {
    padding: 0 !important;
  }
  svg {
    margin-right: 5px;
  }
  button,
  a {
    padding: 6px 16px;

    &:hover {
      background: transparent;
    }
  }
}

#fade-menu {
  ul {
    background-color: #e4effc;
    padding: 10px 0;
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }

  .dropdown-item-link,
  .dropdown-toggle-link {
    color: $blue !important;
    text-transform: capitalize;
    text-decoration: none;
    font-size: 16px;
    width: -webkit-fill-available;
    display: flex;
    align-items: center;
    justify-content: left;
    .color-industry {
      width: 14px;
      height: 14px;
      margin-right: 4px;
      @include industriesBgColors();
    }

    &.active {
      // text-decoration: underline;
      color: $yellow !important;

      svg {
        path {
          fill: $yellow !important;
        }
      }
      font-family: "Proxima-Nova-Medium" !important;
      font-weight: 500;
    }
  }

  .dropdown-toggle-link {
    width: auto;
  }
}
// dropdonw menun style end

#pentabell-footer {
  background-color: $blue;
  color: $white;
  padding-bottom: 34px;
  padding-top: 50px;
  .pentabell-logo {
    // width: 200px;
    height: auto;
  }
  .margin-footer {
    margin: 20px 0;
  }
  .certificate-icons {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    img {
      background-color: $lightBlue2;

      border-radius: 100%;
    }
  }
  form {
    width: 100%;
  }
  .location-footer {
    @include media-query(mobile) {
      padding-top: 0 !important;
    }
  }
  // .MuiGrid-root.MuiGrid-item.MuiGrid-grid-xs-8.MuiGrid-grid-sm-8.css-vok43g-MuiGrid-root{
  //   border: 1px solid $lightBlue;
  // }
  .btn-filled {
    border: 0 !important;
    &:hover {
      background-color: $bankingColor;
    }
  }
  .container {
    justify-content: space-between;
  }

  .title {
    font-family: "Proxima-Nova-Medium" !important;
    font-size: 20px;
    margin: 20px 0;
  }

  .paragraph {
    margin: 10px 0;
    display: flex;
    flex-wrap: wrap;

    &.link {
      font-family: "Proxima-Nova-Semibold" !important;
      font-size: 18px;
      color: $white;
    }
  }

  .link {
    text-decoration: none;
    color: $white;
    margin-right: 5px;

    &:hover {
      text-decoration: underline;
    }
  }

  .bottom-links {
    padding-bottom: 10px;
    margin-top: 15px;

    .website-links {
      padding-top: 10px;
      border-top: 1px solid $white;
      display: flex;
      justify-content: space-between;
    }

    .link {
      font-size: 20px;
      font-family: "Proxima-Nova-Semibold" !important;
    }
  }
  .white-point {
    width: 5px;
    height: 5px;
    content: "";
    border-radius: 100%;
    background-color: $white;
    margin: auto 10px auto 5px;
  }
  .input-pentabell {
    .MuiInputBase-root {
      background: #e5f0fc !important;
    }
  }
}

#social-media-links {
  padding-top: 10px;
  display: flex;

  .link {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;

    svg {
      path {
        fill: $white;
      }

      &:hover {
        path {
          fill: $yellow;
        }
      }
    }
  }

  @include media-query(mobile) {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
  }
}

#newsletter-form {
  @include media-query(mobile) {
    margin: 16px 0 0 16px;
  }

  form {
    display: flex;
  }

  .form-group {
    height: 51px;
  }

  .css-fzx5yo-MuiPaper-root-MuiAlert-root {
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    border-radius: 4px;
    box-shadow: var(--Paper-shadow);
    background-image: var(--Paper-overlay);
    font-family: "Roboto", "Helvetica", "Arial", sans-serif;
    font-weight: 500 !important;
    font-size: 0.875rem;
    line-height: 1.43;
    letter-spacing: 0.01071em;
    display: flex;
    padding: 0px 10px;
    background-color: #e5f0fc;
    color: #cc3233;
    width: 100%;
    margin: 12px 0;
  }
}
.MuiPaper-root.MuiPaper-elevation.MuiPaper-elevation16.MuiDrawer-paper.MuiDrawer-paperAnchorLeft.css-nsg84x {
  background: $blue !important;
}
#mobile-menu {
  .btn-ghost.stroke-svg {
    svg {
      path {
        stroke: $white !important;
      }
    }
  }
  .MuiPaper-root.MuiPaper-elevation {
    background: $blue !important;
    width: 65% !important;
  }
  .menu-item {
    color: $white;
    font-size: 16px;
    font-family: "Proxima-Nova-Medium" !important;
    font-weight: 500;
    text-transform: capitalize;
    text-decoration: none;
    svg {
      margin-right: 5px;
    }
    .color-industry {
      width: 14px;
      height: 14px;
      margin-right: 5px;
      border: 1px solid $white;
      @include industriesBgColors();
    }
    &.sub-menu {
      display: flex;
      flex-direction: column;
      align-items: stretch;
      #collapse-section {
        .menu-item {
          padding: 6px 16px;
          font-size: 14px;
        }
      }
      .dropdown-menu {
        padding: 0;
      }
    }
  }
  nav > a:last-child {
    text-align: center;
    border: 1px solid $white;
    margin: auto 16px;
  }
  .dropdown-menu {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
  }
  .top-section {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .items-list {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 75%;
  }
}
#copyrights {
  padding: 10px;
  background-color: $lightBlue;
  text-align: center;
  font-size: 14px;
  color: $blue;
  font-family: "Proxima-Nova-Medium" !important;

  .link {
    color: $blue;

    text-decoration: underline;
  }
}
.pagination {
  padding: 10px;
  margin: auto;
  text-align: center;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

#page-not-found {
  height: 100vh;
  width: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-image: url("../../assets/images/charte/bg404.png");
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  @include media-query(mobile) {
    background-image: url("../../assets/images/charte/bg404-mobile.png");
  }
  .btn {
    width: fit-content;
    text-align: center;
    margin: 0 auto;
  }
}

.industries-filter.color-industry {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  @include industriesBgColors();
}
