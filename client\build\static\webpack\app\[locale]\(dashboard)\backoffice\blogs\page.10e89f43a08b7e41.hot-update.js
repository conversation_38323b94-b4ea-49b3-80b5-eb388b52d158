"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/ListArticles.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/ListArticles.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/CustomTooltip */ \"(app-pages-browser)/./src/components/ui/CustomTooltip.jsx\");\n/* harmony import */ var _components_loading_Loading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/loading/Loading */ \"(app-pages-browser)/./src/components/loading/Loading.jsx\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _assets_images_icons_preview_icon_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/preview-icon.svg */ \"(app-pages-browser)/./src/assets/images/icons/preview-icon.svg\");\n/* harmony import */ var _assets_images_icons_edit_icon_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/icons/edit-icon.svg */ \"(app-pages-browser)/./src/assets/images/icons/edit-icon.svg\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/assets/images/icons/applicationIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/applicationIcon.svg\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContentText/DialogContentText.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n/* harmony import */ var _components_ui_CustomFilters__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/CustomFilters */ \"(app-pages-browser)/./src/components/ui/CustomFilters.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ListArticles = (param)=>{\n    let { language } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [createdAt, setCreatedAt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const savedVisibility = localStorage.getItem(\"Visibility\");\n    const [visibility, setVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedVisibility || \"\");\n    const [isArchived, setIsArchivedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Default to show non-archived articles\n    const savedPagination = localStorage.getItem(\"PAGINATION_KEY\");\n    const savedSeachValue = localStorage.getItem(\"SearchValue\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedSeachValue || \"\");\n    const [paginationModel, setPaginationModel] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(savedPagination ? JSON.parse(savedPagination) : {\n        page: 0,\n        pageSize: 10\n    });\n    const isOpen = true;\n    const [selectedLanguage, setSelectedLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(language ? language : \"en\");\n    // State for confirmation dialogs\n    const [openArchiveDialog, setOpenArchiveDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openDeleteDialog, setOpenDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedArticle, setSelectedArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Hooks for archive and delete operations\n    const archiveArticleMutation = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.usearchivedarticle)();\n    const deleteArticleMutation = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useDeleteArticle)();\n    // Handler functions for archive and delete\n    const handleArchiveClick = (article)=>{\n        setSelectedArticle(article);\n        setOpenArchiveDialog(true);\n    };\n    const handleDeleteClick = (article)=>{\n        setSelectedArticle(article);\n        setOpenDeleteDialog(true);\n    };\n    const handleArchiveConfirm = async ()=>{\n        if (selectedArticle) {\n            try {\n                await archiveArticleMutation.mutateAsync({\n                    language: selectedLanguage,\n                    id: selectedArticle.id,\n                    archive: !selectedArticle.isArchived\n                });\n                getArticles.refetch();\n            } catch (error) {\n                console.error(\"Error archiving article:\", error);\n            }\n        }\n        setOpenArchiveDialog(false);\n        setSelectedArticle(null);\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (selectedArticle) {\n            try {\n                await deleteArticleMutation.mutateAsync({\n                    language: selectedLanguage,\n                    id: selectedArticle.id\n                });\n                getArticles.refetch();\n            } catch (error) {\n                console.error(\"Error deleting article:\", error);\n            }\n        }\n        setOpenDeleteDialog(false);\n        setSelectedArticle(null);\n    };\n    const handleDialogClose = ()=>{\n        setOpenArchiveDialog(false);\n        setOpenDeleteDialog(false);\n        setSelectedArticle(null);\n    };\n    const resetSearch = ()=>{\n        setCategory(\"\");\n        setSearchQuery(\"\");\n        setVisibility(\"\");\n        setSortOrder(\"\");\n        setCreatedAt(null);\n        setPublishDate(null);\n        setSelectedLanguage(language ? language : \"en\");\n        setPaginationModel({\n            page: 0,\n            pageSize: 10\n        });\n        setIsArchivedFilter(false); // Reset to show non-archived articles\n        setSearch(!search);\n        localStorage.setItem(\"Visibility\", \"\");\n        localStorage.setItem(\"SearchValue\", \"\");\n        localStorage.setItem(\"PAGINATION_KEY\", JSON.stringify({\n            page: 0,\n            pageSize: 10\n        }));\n    };\n    const truncateTitle = (title)=>{\n        const words = title.split(\" \");\n        if (words?.length > 4) {\n            return words.slice(0, 4).join(\" \") + \"...\";\n        } else {\n            return title;\n        }\n    };\n    const getCategoriesLang = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories)(selectedLanguage || \"en\");\n    const transformedCategoriesLang = getCategoriesLang?.data?.categories?.map((category)=>({\n            name: category?.versionscategory[0]?.name,\n            value: category?.versionscategory[0]?.name,\n            label: category?.versionscategory[0]?.name\n        })) || [];\n    const getArticles = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetArticlesDashboard)({\n        language: selectedLanguage,\n        pageSize: paginationModel.pageSize,\n        pageNumber: paginationModel.page + 1,\n        sortOrder,\n        searchQuery,\n        visibility,\n        createdAt,\n        isArchived,\n        publishDate,\n        categoryName: category\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSelectedLanguage(language);\n        getCategoriesLang.refetch();\n    }, [\n        language\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getArticles.refetch();\n    }, [\n        search,\n        paginationModel\n    ]);\n    const visibilityOption = [\n        {\n            value: \"Public\",\n            label: \"Public\"\n        },\n        {\n            value: \"Private\",\n            label: \"Private\"\n        },\n        {\n            value: \"Draft\",\n            label: \"Draft\"\n        }\n    ];\n    const handlePaginationChange = (newPaginationModel)=>{\n        setPaginationModel(newPaginationModel);\n        localStorage.setItem(\"PAGINATION_KEY\", JSON.stringify(newPaginationModel));\n    };\n    if (getArticles.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loading_Loading__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n            lineNumber: 195,\n            columnNumber: 12\n        }, undefined);\n    }\n    const rows = getArticles?.data?.articles?.map((item, index)=>({\n            id: item._id,\n            title: item?.versions?.[0]?.title ? truncateTitle(item?.versions?.[0]?.title) : \"No title\",\n            createdBy: item?.versions?.[0]?.createdBy || \"N/A\",\n            createdAt: item?.versions?.[0]?.createdAt,\n            language: item?.existingLanguages?.join(\"/\") || \"N/A\",\n            actions: item._id,\n            visibility: item?.versions?.[0]?.visibility || \"N/A\",\n            url: item?.versions?.[0]?.url || \"N/A\",\n            totalCommentaires: item?.totalCommentaires || \"0\",\n            isArchived: item?.versions?.[0]?.isArchived || false\n        })) || [];\n    const columns = [\n        {\n            field: \"title\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            headerName: t(\"listArticle:title\"),\n            flex: 1,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: `/${selectedLanguage}/blog/${params.row?.url}`,\n                    className: \"link\",\n                    children: params.row.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"createdBy\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            headerName: t(\"listArticle:createdBy\"),\n            flex: 0.4\n        },\n        {\n            field: \"createdAt\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            flex: 0.4,\n            headerName: t(\"listArticle:createdAt\"),\n            valueFormatter: _utils_functions__WEBPACK_IMPORTED_MODULE_11__.formatDate\n        },\n        {\n            field: \"language\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            flex: 0.4,\n            headerName: t(\"listopportunity:availablelanguage\")\n        },\n        {\n            field: \"visibility\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            flex: 0.4,\n            headerName: t(\"listArticle:visibility\")\n        },\n        {\n            field: \"isArchived\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            flex: 0.4,\n            headerName: t(\"listArticle:archived\"),\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: params.row.isArchived ? t(\"global:yes\") : t(\"global:no\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"totalCommentaires\",\n            headerClassName: \"datagrid-header\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            headerName: t(\"listArticle:nbOfComments\"),\n            flex: 0.4\n        },\n        {\n            field: \"actions\",\n            cellClassName: \"datagrid-cell\",\n            headerClassName: \"datagrid-header\",\n            headerName: \"\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"action-buttons\",\n                    style: {\n                        gridColumn: \"span 2\",\n                        width: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_edit_icon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.edit.route}/${params.row.id}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:edit\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"btn-download\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.comments.route}/${params.row.id}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 305,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:comments\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_preview_icon_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.websiteRoutesList.blog.route}/${params.row?.url}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:preview\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"22\",\n                                        height: \"20\",\n                                        viewBox: \"0 0 22 20\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M21 17C21 17.5304 20.7893 18.0391 20.4142 18.4142C20.0391 18.7893 19.5304 19 19 19H3C2.46957 19 1.96086 18.7893 1.58579 18.4142C1.21071 18.0391 1 17.5304 1 17V3C1 2.46957 1.21071 1.96086 1.58579 1.58579C1.96086 1.21071 2.46957 1 3 1H8L10 4H19C19.5304 4 20.0391 4.21071 20.4142 4.58579C20.7893 4.96086 21 5.46957 21 6V17Z\",\n                                            stroke: \"#1E1E1E\",\n                                            strokeWidth: \"2\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    leftIcon: true,\n                                    onClick: ()=>handleArchiveClick(params.row),\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 333,\n                                columnNumber: 15\n                            }, void 0),\n                            title: params.row.isArchived ? t(\"global:unarchive\") : t(\"global:archive\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"20\",\n                                        height: \"22\",\n                                        viewBox: \"0 0 20 22\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M1 5H3M3 5H19M3 5V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H15C15.5304 21 16.0391 20.7893 16.4142 20.4142C16.7893 20.0391 17 19.5304 17 19V5M6 5V3C6 2.46957 6.21071 1.96086 6.58579 1.58579C6.96086 1.21071 7.46957 1 8 1H12C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V5M8 10V16M12 10V16\",\n                                            stroke: \"#1E1E1E\",\n                                            strokeWidth: \"2\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    leftIcon: true,\n                                    onClick: ()=>handleDeleteClick(params.row),\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 367,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:delete\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, undefined),\n            flex: 1\n        }\n    ];\n    const archivedOptions = [\n        {\n            value: true,\n            label: \"Archived\"\n        },\n        {\n            value: false,\n            label: \"Not Archived\"\n        }\n    ];\n    const filters = [\n        {\n            type: \"text\",\n            label: \"Search By Title\",\n            value: searchQuery,\n            onChange: (e)=>setSearchQuery(e.target.value),\n            placeholder: \"Search\",\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:visibility\"),\n            value: visibility ? {\n                value: visibility,\n                label: visibilityOption.find((opt)=>opt.value === visibility)?.label || visibility\n            } : null,\n            onChange: (_, val)=>setVisibility(val?.value || \"\"),\n            options: visibilityOption,\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:archivage\"),\n            value: isArchived ? {\n                value: isArchived,\n                label: archivedOptions.find((opt)=>opt.value === isArchived)?.label || isArchived\n            } : null,\n            onChange: (_, val)=>setIsArchivedFilter(val?.value || \"\"),\n            options: archivedOptions,\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"global:sort\"),\n            value: sortOrder ? {\n                value: sortOrder,\n                label: t(sortOrder === \"desc\" ? \"global:newest\" : \"global:oldest\")\n            } : null,\n            onChange: (e, val)=>setSortOrder(val?.value || \"\"),\n            options: [\n                {\n                    value: \"desc\",\n                    label: t(\"global:newest\")\n                },\n                {\n                    value: \"asc\",\n                    label: t(\"global:oldest\")\n                }\n            ],\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:category\"),\n            value: category ? {\n                value: category,\n                label: transformedCategoriesLang.find((c)=>c.value === category)?.label || category\n            } : null,\n            onChange: (e, val)=>setCategory(val?.value || \"\"),\n            options: transformedCategoriesLang,\n            condition: true\n        },\n        {\n            type: \"date\",\n            label: t(\"listArticle:createdAt\"),\n            value: createdAt,\n            onChange: (newValue)=>setCreatedAt(newValue),\n            condition: true\n        },\n        {\n            type: \"date\",\n            label: t(\"listArticle:publishDate\"),\n            value: publishDate,\n            onChange: (newValue)=>setPublishDate(newValue),\n            condition: true\n        }\n    ];\n    const handleSearch = ()=>{\n        localStorage.setItem(\"SearchValue\", searchQuery);\n        localStorage.setItem(\"Visibility\", visibility);\n        setPaginationModel({\n            page: 0,\n            pageSize: paginationModel.pageSize\n        });\n        setSearch((prev)=>!prev);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"display-inline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"heading-h2 semi-bold\",\n                        children: [\n                            t(\"listArticle:listOfArticles\"),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"opportunities-nbr\",\n                                children: getArticles?.data?.totalArticles\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 501,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"btn btn-filled\",\n                        text: t(\"global:addarticle\"),\n                        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.add.route}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 508,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 500,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content ${isOpen ? \"open\" : \"closed\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"table-Grid\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomFilters__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    filters: filters,\n                                    onSearch: handleSearch,\n                                    onReset: resetSearch,\n                                    searchLabel: t(\"global:search\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 517,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    height: \"100%\",\n                                    width: \"100%\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_data_grid__WEBPACK_IMPORTED_MODULE_16__.DataGrid, {\n                                    rows: rows,\n                                    columns: columns,\n                                    pagination: true,\n                                    className: \"pentabell-table\",\n                                    paginationMode: \"server\",\n                                    paginationModel: paginationModel,\n                                    onPaginationModelChange: handlePaginationChange,\n                                    pageSizeOptions: [\n                                        5,\n                                        10,\n                                        25\n                                    ],\n                                    rowCount: getArticles?.data?.totalArticles || 0,\n                                    autoHeight: true,\n                                    disableSelectionOnClick: true,\n                                    columnVisibilityModel: {\n                                        createdBy: !isMobile,\n                                        createdAt: !isMobile,\n                                        totalCommentaires: !isMobile,\n                                        visibility: !isMobile,\n                                        language: !isMobile,\n                                        archived: !isMobile\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 529,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 516,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 515,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                open: openArchiveDialog,\n                onClose: handleDialogClose,\n                \"aria-labelledby\": \"archive-dialog-title\",\n                \"aria-describedby\": \"archive-dialog-description\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        id: \"archive-dialog-title\",\n                        children: [\n                            selectedArticle?.isArchived ? t(\"global:unarchive\") : t(\"global:archive\"),\n                            \" \",\n                            t(\"global:article\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 563,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            id: \"archive-dialog-description\",\n                            children: selectedArticle?.isArchived ? t(\"global:confirmUnarchiveArticle\") : t(\"global:confirmArchiveArticle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 570,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 569,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                text: t(\"global:cancel\"),\n                                onClick: handleDialogClose,\n                                className: \"btn btn-secondary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 577,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                text: selectedArticle?.isArchived ? t(\"global:unarchive\") : t(\"global:archive\"),\n                                onClick: handleArchiveConfirm,\n                                className: \"btn btn-primary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 582,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 576,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 557,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                open: openDeleteDialog,\n                onClose: handleDialogClose,\n                \"aria-labelledby\": \"delete-dialog-title\",\n                \"aria-describedby\": \"delete-dialog-description\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        id: \"delete-dialog-title\",\n                        children: [\n                            t(\"global:delete\"),\n                            \" \",\n                            t(\"global:article\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 601,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            id: \"delete-dialog-description\",\n                            children: t(\"global:confirmDeleteArticle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 605,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 604,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                text: t(\"global:cancel\"),\n                                onClick: handleDialogClose,\n                                className: \"btn btn-secondary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 610,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                text: t(\"global:delete\"),\n                                onClick: handleDeleteConfirm,\n                                className: \"btn btn-danger\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 615,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 609,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 595,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ListArticles, \"5YZ611sJ6OtD1N+zmncpv3mb7mI=\", false, function() {\n    return [\n        _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useDeleteArticle,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetArticlesDashboard\n    ];\n});\n_c = ListArticles;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ListArticles);\nvar _c;\n$RefreshReg$(_c, \"ListArticles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/ListArticles.jsx\n"));

/***/ })

});