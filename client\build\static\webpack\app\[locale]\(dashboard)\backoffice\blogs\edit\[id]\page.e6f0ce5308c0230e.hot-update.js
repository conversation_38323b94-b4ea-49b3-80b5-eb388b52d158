"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/edit/[id]/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticle.jsx":
/*!*****************************************************!*\
  !*** ./src/features/blog/components/AddArticle.jsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @feelinglovelynow/slug */ \"(app-pages-browser)/./node_modules/@feelinglovelynow/slug/dist/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! suneditor-react */ \"(app-pages-browser)/./node_modules/suneditor-react/dist/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(suneditor_react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var react_tag_input__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! react-tag-input */ \"(app-pages-browser)/./node_modules/react-tag-input/dist/index.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! suneditor/src/plugins */ \"(app-pages-browser)/./node_modules/suneditor/src/plugins/index.js\");\n/* harmony import */ var _FaqSection__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./FaqSection */ \"(app-pages-browser)/./src/features/blog/components/FaqSection.jsx\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var suneditor_dist_css_suneditor_min_css__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! suneditor/dist/css/suneditor.min.css */ \"(app-pages-browser)/./node_modules/suneditor/dist/css/suneditor.min.css\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/DatePicker/DatePicker.js\");\n/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/x-date-pickers/AdapterDayjs */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/AdapterDayjs/AdapterDayjs.js\");\n/* harmony import */ var _mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/x-date-pickers/internals/demo */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/internals/demo/DemoContainer.js\");\n/* harmony import */ var _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _DocumentImporter__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./DocumentImporter */ \"(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AddArticle = (param)=>{\n    let { language, initialValues, formRef, onImageSelect, validationSchema, image, isEdit, onCategoriesSelect, filteredCategories } = param;\n    _s();\n    const handlePaste = (event, cleanData, maxCharCount)=>{\n        let html = cleanData;\n        html = html.replace(/<strong>(.*?)$/g, \"<strong>$1</strong>\");\n        return html;\n    };\n    const KeyCodes = {\n        comma: 188,\n        enter: 13\n    };\n    const delimiters = [\n        KeyCodes.comma,\n        KeyCodes.enter\n    ];\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [highlights, setHighlights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTags(initialValues?.keywords?.length > 0 ? initialValues?.keywords : []);\n        setHighlights(initialValues?.highlights?.length > 0 ? initialValues?.highlights : []);\n    }, [\n        initialValues\n    ]);\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [publishNow, setPublishNow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handlePhotoChange = async ()=>{\n        const selectedFile = imageInputRef.current.files[0];\n        setSelectedImage(imageInputRef.current.files[0]);\n        if (selectedFile) {\n            onImageSelect(selectedFile, language);\n        }\n    };\n    const getCategories = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories)(language);\n    const transformedCategories = getCategories?.data?.categories?.map((category)=>({\n            id: category.versionscategory[0]?.id,\n            name: category.versionscategory[0]?.name\n        })) || [];\n    const useSaveFileHook = (0,_features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__.useSaveFile)();\n    const handlePhotoBlogChange = async (file, info, core, uploadHandler)=>{\n        if (file instanceof HTMLImageElement) {\n            const src = file.src;\n            if (src.startsWith(\"data:image\")) {\n                const base64Data = src.split(\",\")[1];\n                const contentType = src.match(/data:(.*?);base64/)[1];\n                const byteCharacters = atob(base64Data);\n                const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i)=>byteCharacters.charCodeAt(i));\n                const byteArray = new Uint8Array(byteNumbers);\n                const blob = new Blob([\n                    byteArray\n                ], {\n                    type: contentType\n                });\n                const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                const selectedFile = new File([\n                    blob\n                ], fileName, {\n                    type: contentType\n                });\n                await uploadFile(selectedFile, uploadHandler, core, file);\n            } else {\n                fetch(src).then((response)=>response.blob()).then((blob)=>{\n                    const contentType = blob.type;\n                    const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                    const selectedFile = new File([\n                        blob\n                    ], fileName, {\n                        type: contentType\n                    });\n                    uploadFile(selectedFile, uploadHandler, core, file);\n                }).catch((error)=>console.error(\"Error converting image URL to Blob:\", error));\n            }\n        } else {\n            console.error(\"File is not an HTMLImageElement.\");\n        }\n    };\n    const uploadFile = (selectedFile, uploadHandler, core, originalImage)=>{\n        let uuidPhoto;\n        uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n        const formData = new FormData();\n        formData.append(\"file\", selectedFile);\n        const extension = selectedFile.name.split(\".\").pop();\n        const currentYear = new Date().getFullYear();\n        useSaveFileHook.mutate({\n            resource: \"blogs\",\n            folder: currentYear.toString(),\n            filename: uuidPhoto,\n            body: {\n                formData,\n                t\n            }\n        }, {\n            onSuccess: (dataUUID)=>{\n                const uuidPhotoFileName = dataUUID.message === \"uuid exist\" ? dataUUID.uuid : `${uuidPhoto}.${extension}`;\n                const imageUrl = `${\"http://localhost:4000/api/v1\"}/files/${uuidPhotoFileName}`;\n                originalImage.src = imageUrl;\n                uploadHandler({\n                    result: [\n                        {\n                            id: uuidPhotoFileName,\n                            url: imageUrl\n                        }\n                    ]\n                });\n            },\n            onError: (error)=>{\n                console.error(\"Error uploading file:\", error);\n            }\n        });\n    };\n    // Document import handlers\n    const handleContentExtracted = (extractedContent)=>{\n        // Update the form field based on language\n        const contentField = language === \"en\" ? \"content\" : \"content\";\n        formRef.current?.setFieldValue(contentField, extractedContent);\n    };\n    const handleMetadataExtracted = (metadata)=>{\n        if (metadata.title && formRef.current) {\n            const titleField = language === \"en\" ? \"title\" : \"title\";\n            const urlField = language === \"en\" ? \"url\" : \"url\";\n            const descriptionField = language === \"en\" ? \"description\" : \"description\";\n            // Auto-fill form fields with extracted metadata\n            if (!formRef.current.values[titleField]) {\n                formRef.current.setFieldValue(titleField, metadata.title);\n                // Auto-generate URL from title\n                const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_7__.slug)(metadata.title);\n                formRef.current.setFieldValue(urlField, url);\n            }\n            if (metadata.description && !formRef.current.values[descriptionField]) {\n                formRef.current.setFieldValue(descriptionField, metadata.description);\n            }\n            if (metadata.keywords && metadata.keywords.length > 0) {\n                const keywordField = language === \"en\" ? \"keywords\" : \"keywords\";\n                const existingKeywords = formRef.current.values[keywordField] || [];\n                const mergedKeywords = [\n                    ...existingKeywords,\n                    ...metadata.keywords\n                ];\n                formRef.current.setFieldValue(keywordField, mergedKeywords);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"commun\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"experiences\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"form\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.Formik, {\n                    initialValues: initialValues,\n                    validationSchema: validationSchema,\n                    onSubmit: ()=>{},\n                    innerRef: formRef,\n                    enableReinitialize: \"true\",\n                    children: (param)=>{\n                        let { errors, touched, setFieldValue, values, validateForm } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.Form, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"label-form\",\n                                                        children: [\n                                                            t(\"createArticle:title\"),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                variant: \"standard\",\n                                                                name: \"title\",\n                                                                type: \"text\",\n                                                                value: values.title,\n                                                                onChange: (e)=>{\n                                                                    const title = e.target.value;\n                                                                    setFieldValue(\"title\", title);\n                                                                    const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_7__.slug)(title);\n                                                                    setFieldValue(\"urlEN\", url);\n                                                                },\n                                                                className: \"input-pentabell\" + (errors.title && touched.title ? \" is-invalid\" : \"\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                                className: \"label-error\",\n                                                                name: \"title\",\n                                                                component: \"div\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"label-form\",\n                                                        children: [\n                                                            t(\"createArticle:categories\"),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    multiple: true,\n                                                                    className: \"input-pentabell\",\n                                                                    id: \"tags-standard\",\n                                                                    options: language === \"en\" ? transformedCategories : language === \"fr\" && filteredCategories ? filteredCategories : [],\n                                                                    // defaultValue={values?.category || []}\n                                                                    getOptionLabel: (option)=>option.name,\n                                                                    value: values.category.length > 0 ? transformedCategories.filter((category)=>values.category.some((selectedCategory)=>selectedCategory === category.id)) : [],\n                                                                    onChange: (event, selectedOptions)=>{\n                                                                        const categoryIds = selectedOptions.map((category)=>category.id);\n                                                                        setFieldValue(\"category\", categoryIds);\n                                                                        if (language === \"en\" && onCategoriesSelect) {\n                                                                            onCategoriesSelect(categoryIds);\n                                                                        }\n                                                                    },\n                                                                    renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            ...params,\n                                                                            className: \"input-pentabell  multiple-select\",\n                                                                            variant: \"standard\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 31\n                                                                        }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    touched.category && errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"label-error\",\n                                                        children: errors.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"label-form\",\n                                                children: [\n                                                    \"Description\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        variant: \"standard\",\n                                                        name: \"description\",\n                                                        type: \"text\",\n                                                        multiline: true,\n                                                        rows: 3,\n                                                        value: values.description,\n                                                        onChange: (e)=>{\n                                                            const description = e.target.value;\n                                                            setFieldValue(\"description\", description);\n                                                        },\n                                                        className: \"textArea-pentabell\" + (errors.description && touched.description ? \" is-invalid\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                        className: \"label-error\",\n                                                        name: \"description\",\n                                                        component: \"div\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"label-form\",\n                                                    children: [\n                                                        \"Highlights\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            id: \"tags\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                                                tags: highlights,\n                                                                className: \"input-pentabell\" + (errors.highlights && touched.highlights ? \" is-invalid\" : \"\") + (highlights.length === 0 ? \" no-tags\" : \"\"),\n                                                                delimiters: delimiters,\n                                                                handleDelete: (i)=>{\n                                                                    const updatedTags = highlights.filter((tag, index)=>index !== i);\n                                                                    setHighlights(updatedTags);\n                                                                    setFieldValue(\"highlights\", updatedTags.map((tag)=>tag.text));\n                                                                },\n                                                                handleAddition: (tag)=>{\n                                                                    setHighlights([\n                                                                        ...highlights,\n                                                                        tag\n                                                                    ]);\n                                                                    setFieldValue(\"highlights\", [\n                                                                        ...highlights,\n                                                                        tag\n                                                                    ].map((item)=>item.text));\n                                                                    const updatedTAgs = [\n                                                                        ...highlights,\n                                                                        tag\n                                                                    ].map((item)=>item.text);\n                                                                },\n                                                                inputFieldPosition: \"bottom\",\n                                                                autocomplete: true,\n                                                                allowDragDrop: false\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                            className: \"label-error\",\n                                                            name: \"highlights\",\n                                                            component: \"div\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentImporter__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    onContentExtracted: handleContentExtracted,\n                                    onMetadataExtracted: handleMetadataExtracted,\n                                    language: language.toUpperCase(),\n                                    removeLabel: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((suneditor_react__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                    setContents: values?.content?.length > 0 ? values.content : \"\",\n                                    onChange: (e)=>{\n                                        setFieldValue(\"content\", e);\n                                    },\n                                    onPaste: handlePaste,\n                                    setOptions: {\n                                        cleanHTML: false,\n                                        disableHtmlSanitizer: true,\n                                        addTagsWhitelist: \"h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button\",\n                                        plugins: suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                                        buttonList: [\n                                            [\n                                                \"undo\",\n                                                \"redo\"\n                                            ],\n                                            [\n                                                \"font\",\n                                                \"fontSize\",\n                                                \"formatBlock\"\n                                            ],\n                                            [\n                                                \"bold\",\n                                                \"underline\",\n                                                \"italic\",\n                                                \"strike\",\n                                                \"subscript\",\n                                                \"superscript\"\n                                            ],\n                                            [\n                                                \"fontColor\",\n                                                \"hiliteColor\"\n                                            ],\n                                            [\n                                                \"align\",\n                                                \"list\",\n                                                \"lineHeight\"\n                                            ],\n                                            [\n                                                \"outdent\",\n                                                \"indent\"\n                                            ],\n                                            [\n                                                \"table\",\n                                                \"horizontalRule\",\n                                                \"link\",\n                                                \"image\",\n                                                \"video\"\n                                            ],\n                                            [\n                                                \"fullScreen\",\n                                                \"showBlocks\",\n                                                \"codeView\"\n                                            ],\n                                            [\n                                                \"preview\",\n                                                \"print\"\n                                            ],\n                                            [\n                                                \"removeFormat\"\n                                            ]\n                                        ],\n                                        imageUploadHandler: handlePhotoBlogChange,\n                                        defaultTag: \"div\",\n                                        minHeight: \"300px\",\n                                        maxHeight: \"400px\",\n                                        showPathLabel: false,\n                                        font: [\n                                            \"Proxima-Nova-Regular\",\n                                            \"Proxima-Nova-Medium\",\n                                            \"Proxima-Nova-Semibold\",\n                                            \"Proxima-Nova-Bold\",\n                                            \"Proxima-Nova-Extrabold\",\n                                            \"Proxima-Nova-Black\",\n                                            \"Proxima-Nova-Light\",\n                                            \"Proxima-Nova-Thin\",\n                                            \"Arial\",\n                                            \"Times New Roman\",\n                                            \"Sans-Serif\"\n                                        ],\n                                        charCounter: true,\n                                        charCounterType: \"byte\",\n                                        resizingBar: false,\n                                        colorList: [\n                                            // Standard Colors\n                                            [\n                                                \"#234791\",\n                                                \"#d69b19\",\n                                                \"#cc3233\",\n                                                \"#009966\",\n                                                \"#0b3051\",\n                                                \"#2BBFAD\",\n                                                \"#0b305100\",\n                                                \"#0a305214\",\n                                                \"#743794\",\n                                                \"#ff0000\",\n                                                \"#ff5e00\",\n                                                \"#ffe400\",\n                                                \"#abf200\",\n                                                \"#00d8ff\",\n                                                \"#0055ff\",\n                                                \"#6600ff\",\n                                                \"#ff00dd\",\n                                                \"#000000\",\n                                                \"#ffd8d8\",\n                                                \"#fae0d4\",\n                                                \"#faf4c0\",\n                                                \"#e4f7ba\",\n                                                \"#d4f4fa\",\n                                                \"#d9e5ff\",\n                                                \"#e8d9ff\",\n                                                \"#ffd9fa\",\n                                                \"#f1f1f1\",\n                                                \"#ffa7a7\",\n                                                \"#ffc19e\",\n                                                \"#faed7d\",\n                                                \"#cef279\",\n                                                \"#b2ebf4\",\n                                                \"#b2ccff\",\n                                                \"#d1b2ff\",\n                                                \"#ffb2f5\",\n                                                \"#bdbdbd\",\n                                                \"#f15f5f\",\n                                                \"#f29661\",\n                                                \"#e5d85c\",\n                                                \"#bce55c\",\n                                                \"#5cd1e5\",\n                                                \"#6699ff\",\n                                                \"#a366ff\",\n                                                \"#f261df\",\n                                                \"#8c8c8c\",\n                                                \"#980000\",\n                                                \"#993800\",\n                                                \"#998a00\",\n                                                \"#6b9900\",\n                                                \"#008299\",\n                                                \"#003399\",\n                                                \"#3d0099\",\n                                                \"#990085\",\n                                                \"#353535\",\n                                                \"#670000\",\n                                                \"#662500\",\n                                                \"#665c00\",\n                                                \"#476600\",\n                                                \"#005766\",\n                                                \"#002266\",\n                                                \"#290066\",\n                                                \"#660058\",\n                                                \"#222222\"\n                                            ]\n                                        ]\n                                    },\n                                    onImageUpload: handlePhotoBlogChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaqSection__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    values: values,\n                                    setFieldValue: setFieldValue,\n                                    errors: errors,\n                                    touched: touched,\n                                    language: language === \"en\" ? \"EN\" : \"FR\",\n                                    debounce: ()=>{},\n                                    isEdit: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"label-form\",\n                                                    children: [\n                                                        t(\"createArticle:metaTitle\"),\n                                                        \" (\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: values.metaTitle?.length > 65 ? \" text-danger\" : \"\",\n                                                            children: [\n                                                                \" \",\n                                                                values.metaTitle?.length,\n                                                                \" / 65\",\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        \" \",\n                                                        \")\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            variant: \"standard\",\n                                                            name: \"metaTitle\",\n                                                            type: \"text\",\n                                                            value: values.metaTitle,\n                                                            onChange: (e)=>{\n                                                                setFieldValue(\"metaTitle\", e.target.value);\n                                                            },\n                                                            className: \"input-pentabell\" + (errors.metaTitle && touched.metaTitle ? \" is-invalid\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                            className: \"label-error\",\n                                                            name: \"metaTitle\",\n                                                            component: \"div\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"label-form\",\n                                                    children: [\n                                                        t(\"createArticle:url\"),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            variant: \"standard\",\n                                                            name: \"url\",\n                                                            type: \"text\",\n                                                            value: values.url,\n                                                            onChange: (e)=>{\n                                                                setFieldValue(\"url\", e.target.value);\n                                                            },\n                                                            className: \"input-pentabell\" + (errors.url && touched.url ? \" is-invalid\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                            className: \"label-error\",\n                                                            name: \"url\",\n                                                            component: \"div\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"label-form\",\n                                                children: [\n                                                    t(\"createArticle:metaDescription\"),\n                                                    \" (\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: values.metaDescription?.length > 160 ? \" text-danger\" : \"\",\n                                                        children: [\n                                                            values.metaDescription?.length,\n                                                            \" / 160\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    \" \",\n                                                    \")\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        variant: \"standard\",\n                                                        name: \"metaDescription\",\n                                                        type: \"text\",\n                                                        multiline: true,\n                                                        rows: 3,\n                                                        value: values.metaDescription,\n                                                        onChange: (e)=>{\n                                                            setFieldValue(\"metaDescription\", e.target.value);\n                                                        },\n                                                        className: \"textArea-pentabell\" + (errors.metaDescription && touched.metaDescription ? \" is-invalid\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                        className: \"label-error\",\n                                                        name: \"metaDescription\",\n                                                        component: \"div\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"label-form\",\n                                                children: [\n                                                    t(\"createArticle:featuredImage\"),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"upload-container\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: `image-upload-${language}`,\n                                                            className: \"file-labels\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"file\",\n                                                                    id: `image-upload-${language}`,\n                                                                    name: \"image\",\n                                                                    accept: \".png, .jpg, .jpeg, .webp\",\n                                                                    ref: imageInputRef,\n                                                                    onChange: (e)=>{\n                                                                        setFieldValue(\"image\", e.target.files[0]);\n                                                                        handlePhotoChange();\n                                                                    },\n                                                                    className: \"file-input\" + (errors.image && touched.image ? \" is-invalid\" : \"\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"upload-area\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"icon-pic\",\n                                                                                style: {\n                                                                                    backgroundImage: `url(\"${selectedImage ? URL.createObjectURL(selectedImage) : image ? `${\"http://localhost:4000/api/v1\"}${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.files}/${image}` : _assets_images_add_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src}\")`,\n                                                                                    backgroundSize: \"cover\",\n                                                                                    backgroundRepeat: \"no-repeat\",\n                                                                                    backgroundPosition: \"center\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                                lineNumber: 673,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                            lineNumber: 672,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"upload-text\",\n                                                                                    children: t(\"createArticle:addFeatImg\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                                    lineNumber: 691,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"upload-description\",\n                                                                                    children: t(\"createArticle:clickBox\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                                    lineNumber: 694,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                    lineNumber: 671,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                                    name: \"image\",\n                                                                    component: \"div\",\n                                                                    className: \"invalid-feedback error\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 646,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"label-form\",\n                                                        children: [\n                                                            t(\"createArticle:alt\"),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                variant: \"standard\",\n                                                                name: \"alt\",\n                                                                type: \"text\",\n                                                                value: values.alt,\n                                                                onChange: (e)=>{\n                                                                    setFieldValue(\"alt\", e.target.value);\n                                                                },\n                                                                className: \"input-pentabell\" + (errors.alt && touched.alt ? \" is-invalid\" : \"\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                                className: \"label-error\",\n                                                                name: \"alt\",\n                                                                component: \"div\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 714,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"label-form\",\n                                                        children: [\n                                                            t(\"createArticle:visibility\"),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"select-pentabell\",\n                                                                variant: \"standard\",\n                                                                value: _utils_constants__WEBPACK_IMPORTED_MODULE_4__.Visibility.filter((option)=>values.visibility === option),\n                                                                selected: values?.visibility,\n                                                                onChange: (event)=>{\n                                                                    setFieldValue(\"visibility\", event.target.value);\n                                                                },\n                                                                children: _utils_constants__WEBPACK_IMPORTED_MODULE_4__.Visibility.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        value: item,\n                                                                        children: item\n                                                                    }, index, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 29\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 744,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                                className: \"label-error\",\n                                                                name: \"visibilityEN\",\n                                                                component: \"div\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 740,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 738,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"label-form\",\n                                                        children: [\n                                                            t(\"createArticle:keyword\"),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                id: \"tags\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                                                    tags: tags,\n                                                                    className: \"input-pentabell\" + (errors.keywords && touched.keywords ? \" is-invalid\" : \"\") + (tags.length === 0 ? \" no-tags\" : \"\"),\n                                                                    delimiters: delimiters,\n                                                                    handleDelete: (i)=>{\n                                                                        const updatedTags = tags.filter((tag, index)=>index !== i);\n                                                                        setTags(updatedTags);\n                                                                        setFieldValue(\"keywords\", updatedTags.map((tag)=>tag.text));\n                                                                    },\n                                                                    handleAddition: (tag)=>{\n                                                                        setTags([\n                                                                            ...tags,\n                                                                            tag\n                                                                        ]);\n                                                                        setFieldValue(\"keywords\", [\n                                                                            ...tags,\n                                                                            tag\n                                                                        ].map((item)=>item.text));\n                                                                        const updatedTAgs = [\n                                                                            ...tags,\n                                                                            tag\n                                                                        ].map((item)=>item.text);\n                                                                    },\n                                                                    inputFieldPosition: \"bottom\",\n                                                                    autocomplete: true,\n                                                                    allowDragDrop: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                    lineNumber: 777,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                                className: \"label-error\",\n                                                                name: \"keywords\",\n                                                                component: \"div\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 812,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 771,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"label-form\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.Field, {\n                                            type: \"checkbox\",\n                                            name: \"publishNow\",\n                                            checked: publishNow,\n                                            onChange: (e)=>{\n                                                setPublishNow(e.target.checked);\n                                                if (e.target.checked) {\n                                                    setFieldValue(\"publishDate\", new Date().toISOString());\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        t(\"createArticle:publishNow\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 17\n                                }, undefined),\n                                !publishNow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"label-form\",\n                                                children: [\n                                                    t(\"createArticle:publishDate\"),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_26__.LocalizationProvider, {\n                                                        dateAdapter: _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_27__.AdapterDayjs,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_28__.DemoContainer, {\n                                                            components: [\n                                                                \"DatePicker\"\n                                                            ],\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_29__.DatePicker, {\n                                                                    variant: \"standard\",\n                                                                    className: \"input-date\",\n                                                                    format: \"DD/MM/YYYY\",\n                                                                    value: dayjs__WEBPACK_IMPORTED_MODULE_9___default()(values.publishDateEN),\n                                                                    onChange: (date)=>{\n                                                                        setFieldValue(\"publishDateEN\", dayjs__WEBPACK_IMPORTED_MODULE_9___default()(date).format(\"YYYY-MM-DD\"));\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                    lineNumber: 844,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 843,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 842,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                className: \"label-error\",\n                                                name: \"publishDateEN\",\n                                                component: \"div\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 859,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 838,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.Field, {\n                                    type: \"hidden\",\n                                    name: \"publishDate\",\n                                    value: publishNow ? new Date().toISOString() : publishDate.toISOString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 868,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                            lineNumber: 223,\n                            columnNumber: 15\n                        }, undefined);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                    lineNumber: 215,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                lineNumber: 214,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n            lineNumber: 213,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AddArticle, \"E9i6H46KXeqeynJxyI/25VRxGp4=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories,\n        _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__.useSaveFile\n    ];\n});\n_c = AddArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticle);\nvar _c;\n$RefreshReg$(_c, \"AddArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticle.jsx\n"));

/***/ })

});