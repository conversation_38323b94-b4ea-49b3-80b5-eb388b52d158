"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.js":
/*!***************************!*\
  !*** ./src/middleware.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-i18n-router */ \"(middleware)/./node_modules/next-i18n-router/dist/index.js\");\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_i18n_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! jose */ \"(middleware)/./node_modules/jose/dist/browser/jwt/verify.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cookie */ \"(middleware)/./node_modules/cookie/index.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../i18nConfig */ \"(middleware)/./i18nConfig.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_i18nConfig__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/constants */ \"(middleware)/./src/utils/constants.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/functions */ \"(middleware)/./src/utils/functions.js\");\n/* harmony import */ var _config_translations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./config/translations */ \"(middleware)/./src/config/translations.js\");\n/* harmony import */ var _config_allowedParams__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./config/allowedParams */ \"(middleware)/./src/config/allowedParams.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./helpers/routesList */ \"(middleware)/./src/helpers/routesList.js\");\n\n\n\n\n\n\n\n\n\n\nconst SECURITY_CONFIG = {\n    MAX_REQUESTS_PER_MINUTE: 60,\n    JWT_ALGORITHM: \"HS256\",\n    SUSPICIOUS_PATTERNS: [\n        /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n        /javascript:/gi,\n        /on\\w+\\s*=/gi,\n        /eval\\s*\\(/gi,\n        /expression\\s*\\(/gi,\n        /%3Cscript/gi,\n        /%3C%2Fscript%3E/gi\n    ]\n};\nconst rateLimitStore = new Map();\nconst logSecurityEvent = (event, details = {})=>{\n    if (true) {\n        console.warn(`[SECURITY] ${event}:`, {\n            timestamp: new Date().toISOString(),\n            ...details\n        });\n    }\n};\nconst verifyToken = async (token, clientIP = \"unknown\")=>{\n    try {\n        if (!token || typeof token !== \"string\") {\n            logSecurityEvent(\"INVALID_TOKEN_FORMAT\", {\n                clientIP,\n                reason: \"Missing or invalid token\"\n            });\n            return null;\n        }\n        const jwtRegex = /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/;\n        if (!jwtRegex.test(token)) {\n            logSecurityEvent(\"INVALID_JWT_FORMAT\", {\n                clientIP,\n                tokenPrefix: token.substring(0, 10)\n            });\n            return null;\n        }\n        // Check environment configuration\n        const jwtSecret = process.env.NEXT_JWT_SECRET;\n        if (!jwtSecret || jwtSecret.length < 32) {\n            logSecurityEvent(\"WEAK_JWT_SECRET\", {\n                clientIP\n            });\n            throw new Error(\"JWT secret configuration error\");\n        }\n        const secret = new TextEncoder().encode(jwtSecret);\n        // Verify token with additional security options\n        const { payload } = await (0,jose__WEBPACK_IMPORTED_MODULE_9__.jwtVerify)(token, secret, {\n            algorithms: [\n                SECURITY_CONFIG.JWT_ALGORITHM\n            ],\n            issuer: process.env.JWT_ISSUER || \"pentabell-api\",\n            audience: process.env.JWT_AUDIENCE || \"pentabell-client\"\n        });\n        // Validate payload structure\n        if (!payload || !payload._id || !payload.roles || !Array.isArray(payload.roles)) {\n            logSecurityEvent(\"INVALID_TOKEN_PAYLOAD\", {\n                clientIP,\n                hasId: !!payload?._id,\n                hasRoles: !!payload?.roles\n            });\n            return null;\n        }\n        // Check token age (optional additional security)\n        const tokenAge = Date.now() / 1000 - (payload.iat || 0);\n        if (tokenAge > 86400) {\n            // 24 hours\n            logSecurityEvent(\"OLD_TOKEN_USAGE\", {\n                clientIP,\n                tokenAge,\n                userId: payload._id\n            });\n        }\n        return payload;\n    } catch (error) {\n        if (error.name === \"JWTExpired\") {\n            logSecurityEvent(\"TOKEN_EXPIRED\", {\n                clientIP\n            });\n        } else if (error.name === \"JWTInvalid\") {\n            logSecurityEvent(\"INVALID_TOKEN\", {\n                clientIP,\n                error: error.message\n            });\n        } else {\n            logSecurityEvent(\"TOKEN_VERIFICATION_ERROR\", {\n                clientIP,\n                error: error.message\n            });\n        }\n        return null;\n    }\n};\n// Rate limiting function\nconst checkRateLimit = (clientIP)=>{\n    const now = Date.now();\n    const windowStart = now - 60000; // 1 minute window\n    if (!rateLimitStore.has(clientIP)) {\n        rateLimitStore.set(clientIP, []);\n    }\n    const requests = rateLimitStore.get(clientIP);\n    // Remove old requests\n    const validRequests = requests.filter((timestamp)=>timestamp > windowStart);\n    rateLimitStore.set(clientIP, validRequests);\n    // Check if limit exceeded\n    if (validRequests.length >= SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE) {\n        return false;\n    }\n    // Add current request\n    validRequests.push(now);\n    return true;\n};\n// Input sanitization function\nconst sanitizeInput = (value)=>{\n    if (typeof value !== \"string\") return value;\n    let sanitized = value;\n    SECURITY_CONFIG.SUSPICIOUS_PATTERNS.forEach((pattern)=>{\n        sanitized = sanitized.replace(pattern, \"\");\n    });\n    return sanitized.trim();\n};\n// Security headers function\nconst setSecurityHeaders = (response)=>{\n    response.headers.set(\"X-Content-Type-Options\", \"nosniff\");\n    response.headers.set(\"X-Frame-Options\", \"DENY\");\n    response.headers.set(\"X-XSS-Protection\", \"1; mode=block\");\n    response.headers.set(\"Referrer-Policy\", \"strict-origin-when-cross-origin\");\n    response.headers.set(\"Permissions-Policy\", \"geolocation=(), microphone=(), camera=()\");\n    // Remove server information\n    response.headers.delete(\"Server\");\n    response.headers.delete(\"X-Powered-By\");\n    return response;\n};\nasync function middleware(req) {\n    const url = req.nextUrl.clone();\n    const { defaultLocale } = (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default());\n    const { pathname } = req.nextUrl;\n    const clientIP = req.ip || req.headers.get(\"x-forwarded-for\") || req.headers.get(\"x-real-ip\") || \"unknown\";\n    // Initialize response with security headers\n    let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    response = setSecurityHeaders(response);\n    // Rate limiting check\n    if (!checkRateLimit(clientIP)) {\n        logSecurityEvent(\"RATE_LIMIT_EXCEEDED\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Too Many Requests\", {\n            status: 429,\n            headers: {\n                \"Retry-After\": \"60\",\n                \"X-RateLimit-Limit\": SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE.toString(),\n                \"X-RateLimit-Remaining\": \"0\"\n            }\n        });\n    }\n    // Sanitize query parameters\n    let hasModifiedParams = false;\n    for (const [key, value] of url.searchParams.entries()){\n        const sanitizedValue = sanitizeInput(value);\n        if (sanitizedValue !== value) {\n            url.searchParams.set(key, sanitizedValue);\n            hasModifiedParams = true;\n            logSecurityEvent(\"SUSPICIOUS_QUERY_PARAM\", {\n                clientIP,\n                key,\n                originalValue: value.substring(0, 50)\n            });\n        }\n    }\n    // Check for suspicious patterns in pathname\n    const pathString = pathname.toLowerCase();\n    const hasSuspiciousPath = SECURITY_CONFIG.SUSPICIOUS_PATTERNS.some((pattern)=>pattern.test(pathString));\n    if (hasSuspiciousPath) {\n        logSecurityEvent(\"SUSPICIOUS_PATH_ACCESS\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Forbidden\", {\n            status: 403\n        });\n    }\n    // Enhanced authentication and authorization\n    const cookies = cookie__WEBPACK_IMPORTED_MODULE_2__.parse(req.headers.get(\"cookie\") || \"\");\n    const { accessToken, refreshToken } = cookies;\n    // Check for protected routes\n    const isProtectedRoute = pathname.includes(\"dashboard\") || pathname.includes(\"backoffice\");\n    if (isProtectedRoute && !(accessToken && refreshToken)) {\n        logSecurityEvent(\"UNAUTHORIZED_ACCESS_ATTEMPT\", {\n            clientIP,\n            pathname\n        });\n        url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // Enhanced token verification with proper token selection\n    let user = null;\n    if (refreshToken) {\n        user = await verifyToken(refreshToken, clientIP);\n        // Additional security check for protected routes\n        if (isProtectedRoute && !user) {\n            logSecurityEvent(\"INVALID_TOKEN_PROTECTED_ROUTE\", {\n                clientIP,\n                pathname\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n    }\n    // Handle logout route\n    if (pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/`) {\n        logSecurityEvent(\"USER_LOGOUT\", {\n            clientIP,\n            userId: user?._id\n        });\n        return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n    }\n    // Enhanced role-based access control\n    if (user) {\n        const menuList = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.getRoutesListByRole)(user);\n        // Validate user roles\n        if (!user.roles || !Array.isArray(user.roles) || user.roles.length === 0) {\n            logSecurityEvent(\"INVALID_USER_ROLES\", {\n                clientIP,\n                userId: user._id\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n        const checkRole = !menuList?.some((item)=>pathname.includes(item)) && (pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}`) || pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}`));\n        // Enhanced role checking with security logging\n        if (checkRole || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`) {\n            let redirectPath = null;\n            if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.home.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.EDITOR)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.blogs.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.CANDIDATE)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.candidateRoutes.myApplications.route}`;\n            }\n            if (redirectPath) {\n                logSecurityEvent(\"ROLE_BASED_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles,\n                    fromPath: pathname,\n                    toPath: redirectPath\n                });\n                url.pathname = redirectPath;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            } else {\n                logSecurityEvent(\"NO_VALID_ROLE_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles\n                });\n                url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            }\n        }\n    }\n    // Enhanced parameter filtering with security logging\n    let removedParams = [];\n    for (const param of url.searchParams.keys()){\n        if (!_config_allowedParams__WEBPACK_IMPORTED_MODULE_7__.allowedParams.has(param)) {\n            url.searchParams.delete(param);\n            removedParams.push(param);\n        }\n    }\n    if (removedParams.length > 0) {\n        logSecurityEvent(\"REMOVED_DISALLOWED_PARAMS\", {\n            clientIP,\n            pathname,\n            removedParams,\n            userId: user?._id\n        });\n    }\n    // Check if parameters were modified (either sanitized or removed)\n    if (hasModifiedParams || url.searchParams.toString() !== req.nextUrl.searchParams.toString()) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // Enhanced redirection paths with security checks\n    const frPath = _config_translations__WEBPACK_IMPORTED_MODULE_6__[\"default\"][req.nextUrl.pathname];\n    if (frPath) {\n        logSecurityEvent(\"FRENCH_PATH_REDIRECT\", {\n            clientIP,\n            fromPath: req.nextUrl.pathname,\n            toPath: frPath\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(frPath, req.url));\n    }\n    // Enhanced language handling with security validation\n    if (!pathname.startsWith(\"/fr\") && !pathname.startsWith(`/${defaultLocale}`) && !pathname.startsWith(\"/_next\") && !pathname.startsWith(\"/api\") && !pathname.startsWith(\"/static\") && !pathname.includes(\".\")) {\n        // Additional security check for suspicious paths\n        if (pathname.length > 200) {\n            logSecurityEvent(\"SUSPICIOUS_LONG_PATH\", {\n                clientIP,\n                pathLength: pathname.length\n            });\n            return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Bad Request\", {\n                status: 400\n            });\n        }\n        url.pathname = `/en${pathname}`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.rewrite(url);\n    }\n    // Log successful requests for monitoring (in development only)\n    if ( true && user) {\n        logSecurityEvent(\"SUCCESSFUL_REQUEST\", {\n            clientIP,\n            pathname,\n            userId: user._id,\n            roles: user.roles\n        });\n    }\n    return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     * - files with extensions (images, fonts, etc.)\r\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|public|.*\\\\..*).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.js\n");

/***/ })

});