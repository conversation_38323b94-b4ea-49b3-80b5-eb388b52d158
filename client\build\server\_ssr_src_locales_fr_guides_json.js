"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_fr_guides_json";
exports.ids = ["_ssr_src_locales_fr_guides_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/fr/guides.json":
/*!************************************!*\
  !*** ./src/locales/fr/guides.json ***!
  \************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"listGuide":"listGuide","bannerTitle":"Pentabell Industry Insights","bannerDescription":"Guides pratiques, checklists et modèles pour le portage salarial, le conseil RH et la gestion des talents.","subtitle":"Sous-titre","title":"Titre","subtitleDescription":"Description du sous-titre","addSubtitle":"Ajouter un sous-titre","addItemList":"Ajouter une liste d\'éléments","remove":"Retirer","categoriesGuide":"Categories du guide"}');

/***/ })

};
;