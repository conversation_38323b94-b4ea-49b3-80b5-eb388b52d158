"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.js":
/*!***************************!*\
  !*** ./src/middleware.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-i18n-router */ \"(middleware)/./node_modules/next-i18n-router/dist/index.js\");\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_i18n_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! jose */ \"(middleware)/./node_modules/jose/dist/browser/jwt/verify.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cookie */ \"(middleware)/./node_modules/cookie/index.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../i18nConfig */ \"(middleware)/./i18nConfig.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_i18nConfig__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/constants */ \"(middleware)/./src/utils/constants.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/functions */ \"(middleware)/./src/utils/functions.js\");\n/* harmony import */ var _config_translations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./config/translations */ \"(middleware)/./src/config/translations.js\");\n/* harmony import */ var _config_allowedParams__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./config/allowedParams */ \"(middleware)/./src/config/allowedParams.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./helpers/routesList */ \"(middleware)/./src/helpers/routesList.js\");\n\n\n\n\n\n\n\n\n\n\nconst SECURITY_CONFIG = {\n    MAX_REQUESTS_PER_MINUTE: 60,\n    JWT_ALGORITHM: \"HS256\",\n    SUSPICIOUS_PATTERNS: [\n        /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n        /javascript:/gi,\n        /on\\w+\\s*=/gi,\n        /eval\\s*\\(/gi,\n        /expression\\s*\\(/gi,\n        /%3Cscript/gi,\n        /%3C%2Fscript%3E/gi\n    ]\n};\nconst rateLimitStore = new Map();\nconst logSecurityEvent = (event, details = {})=>{\n    if (true) {\n        console.warn(`[SECURITY] ${event}:`, {\n            timestamp: new Date().toISOString(),\n            ...details\n        });\n    }\n};\nconst verifyToken = async (token, clientIP = \"unknown\")=>{\n    try {\n        if (!token || typeof token !== \"string\") {\n            logSecurityEvent(\"INVALID_TOKEN_FORMAT\", {\n                clientIP,\n                reason: \"Missing or invalid token\"\n            });\n            return null;\n        }\n        const jwtRegex = /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/;\n        if (!jwtRegex.test(token)) {\n            logSecurityEvent(\"INVALID_JWT_FORMAT\", {\n                clientIP,\n                tokenPrefix: token.substring(0, 10)\n            });\n            return null;\n        }\n        const jwtSecret = process.env.NEXT_JWT_SECRET;\n        if (!jwtSecret || jwtSecret.length < 32) {\n            logSecurityEvent(\"WEAK_JWT_SECRET\", {\n                clientIP\n            });\n            throw new Error(\"JWT secret configuration error\");\n        }\n        const secret = new TextEncoder().encode(jwtSecret);\n        const { payload } = await (0,jose__WEBPACK_IMPORTED_MODULE_9__.jwtVerify)(token, secret, {\n            algorithms: [\n                SECURITY_CONFIG.JWT_ALGORITHM\n            ],\n            issuer: process.env.JWT_ISSUER || \"pentabell-api\",\n            audience: process.env.JWT_AUDIENCE || \"pentabell-client\"\n        });\n        if (!payload || !payload._id || !payload.roles || !Array.isArray(payload.roles)) {\n            logSecurityEvent(\"INVALID_TOKEN_PAYLOAD\", {\n                clientIP,\n                hasId: !!payload?._id,\n                hasRoles: !!payload?.roles\n            });\n            return null;\n        }\n        const tokenAge = Date.now() / 1000 - (payload.iat || 0);\n        if (tokenAge > 86400) {\n            logSecurityEvent(\"OLD_TOKEN_USAGE\", {\n                clientIP,\n                tokenAge,\n                userId: payload._id\n            });\n        }\n        return payload;\n    } catch (error) {\n        if (error.name === \"JWTExpired\") {\n            logSecurityEvent(\"TOKEN_EXPIRED\", {\n                clientIP\n            });\n        } else if (error.name === \"JWTInvalid\") {\n            logSecurityEvent(\"INVALID_TOKEN\", {\n                clientIP,\n                error: error.message\n            });\n        } else {\n            logSecurityEvent(\"TOKEN_VERIFICATION_ERROR\", {\n                clientIP,\n                error: error.message\n            });\n        }\n        return null;\n    }\n};\nconst checkRateLimit = (clientIP)=>{\n    const now = Date.now();\n    const windowStart = now - 60000;\n    if (!rateLimitStore.has(clientIP)) {\n        rateLimitStore.set(clientIP, []);\n    }\n    const requests = rateLimitStore.get(clientIP);\n    const validRequests = requests.filter((timestamp)=>timestamp > windowStart);\n    rateLimitStore.set(clientIP, validRequests);\n    if (validRequests.length >= SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE) {\n        return false;\n    }\n    // Add current request\n    validRequests.push(now);\n    return true;\n};\n// Input sanitization function\nconst sanitizeInput = (value)=>{\n    if (typeof value !== \"string\") return value;\n    let sanitized = value;\n    SECURITY_CONFIG.SUSPICIOUS_PATTERNS.forEach((pattern)=>{\n        sanitized = sanitized.replace(pattern, \"\");\n    });\n    return sanitized.trim();\n};\n// Security headers function\nconst setSecurityHeaders = (response)=>{\n    response.headers.set(\"X-Content-Type-Options\", \"nosniff\");\n    response.headers.set(\"X-Frame-Options\", \"DENY\");\n    response.headers.set(\"X-XSS-Protection\", \"1; mode=block\");\n    response.headers.set(\"Referrer-Policy\", \"strict-origin-when-cross-origin\");\n    response.headers.set(\"Permissions-Policy\", \"geolocation=(), microphone=(), camera=()\");\n    // Remove server information\n    response.headers.delete(\"Server\");\n    response.headers.delete(\"X-Powered-By\");\n    return response;\n};\nasync function middleware(req) {\n    const url = req.nextUrl.clone();\n    const { defaultLocale } = (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default());\n    const { pathname } = req.nextUrl;\n    const clientIP = req.ip || req.headers.get(\"x-forwarded-for\") || req.headers.get(\"x-real-ip\") || \"unknown\";\n    // Initialize response with security headers\n    let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    response = setSecurityHeaders(response);\n    // Rate limiting check\n    if (!checkRateLimit(clientIP)) {\n        logSecurityEvent(\"RATE_LIMIT_EXCEEDED\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Too Many Requests\", {\n            status: 429,\n            headers: {\n                \"Retry-After\": \"60\",\n                \"X-RateLimit-Limit\": SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE.toString(),\n                \"X-RateLimit-Remaining\": \"0\"\n            }\n        });\n    }\n    // Sanitize query parameters\n    let hasModifiedParams = false;\n    for (const [key, value] of url.searchParams.entries()){\n        const sanitizedValue = sanitizeInput(value);\n        if (sanitizedValue !== value) {\n            url.searchParams.set(key, sanitizedValue);\n            hasModifiedParams = true;\n            logSecurityEvent(\"SUSPICIOUS_QUERY_PARAM\", {\n                clientIP,\n                key,\n                originalValue: value.substring(0, 50)\n            });\n        }\n    }\n    // Check for suspicious patterns in pathname\n    const pathString = pathname.toLowerCase();\n    const hasSuspiciousPath = SECURITY_CONFIG.SUSPICIOUS_PATTERNS.some((pattern)=>pattern.test(pathString));\n    if (hasSuspiciousPath) {\n        logSecurityEvent(\"SUSPICIOUS_PATH_ACCESS\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Forbidden\", {\n            status: 403\n        });\n    }\n    // Enhanced authentication and authorization\n    const cookies = cookie__WEBPACK_IMPORTED_MODULE_2__.parse(req.headers.get(\"cookie\") || \"\");\n    const { accessToken, refreshToken } = cookies;\n    // Check for protected routes\n    const isProtectedRoute = pathname.includes(\"dashboard\") || pathname.includes(\"backoffice\");\n    if (isProtectedRoute && !(accessToken && refreshToken)) {\n        logSecurityEvent(\"UNAUTHORIZED_ACCESS_ATTEMPT\", {\n            clientIP,\n            pathname\n        });\n        url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // Enhanced token verification with proper token selection\n    let user = null;\n    if (refreshToken) {\n        user = await verifyToken(refreshToken, clientIP);\n        // Additional security check for protected routes\n        if (isProtectedRoute && !user) {\n            logSecurityEvent(\"INVALID_TOKEN_PROTECTED_ROUTE\", {\n                clientIP,\n                pathname\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n    }\n    // Handle logout route\n    if (pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/`) {\n        logSecurityEvent(\"USER_LOGOUT\", {\n            clientIP,\n            userId: user?._id\n        });\n        return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n    }\n    // Enhanced role-based access control\n    if (user) {\n        const menuList = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.getRoutesListByRole)(user);\n        // Validate user roles\n        if (!user.roles || !Array.isArray(user.roles) || user.roles.length === 0) {\n            logSecurityEvent(\"INVALID_USER_ROLES\", {\n                clientIP,\n                userId: user._id\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n        const checkRole = !menuList?.some((item)=>pathname.includes(item)) && (pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}`) || pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}`));\n        // Enhanced role checking with security logging\n        if (checkRole || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`) {\n            let redirectPath = null;\n            if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.home.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.EDITOR)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.blogs.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.CANDIDATE)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.candidateRoutes.myApplications.route}`;\n            }\n            if (redirectPath) {\n                logSecurityEvent(\"ROLE_BASED_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles,\n                    fromPath: pathname,\n                    toPath: redirectPath\n                });\n                url.pathname = redirectPath;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            } else {\n                logSecurityEvent(\"NO_VALID_ROLE_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles\n                });\n                url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            }\n        }\n    }\n    // Enhanced parameter filtering with security logging\n    let removedParams = [];\n    for (const param of url.searchParams.keys()){\n        if (!_config_allowedParams__WEBPACK_IMPORTED_MODULE_7__.allowedParams.has(param)) {\n            url.searchParams.delete(param);\n            removedParams.push(param);\n        }\n    }\n    if (removedParams.length > 0) {\n        logSecurityEvent(\"REMOVED_DISALLOWED_PARAMS\", {\n            clientIP,\n            pathname,\n            removedParams,\n            userId: user?._id\n        });\n    }\n    // Check if parameters were modified (either sanitized or removed)\n    if (hasModifiedParams || url.searchParams.toString() !== req.nextUrl.searchParams.toString()) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // Enhanced redirection paths with security checks\n    const frPath = _config_translations__WEBPACK_IMPORTED_MODULE_6__[\"default\"][req.nextUrl.pathname];\n    if (frPath) {\n        logSecurityEvent(\"FRENCH_PATH_REDIRECT\", {\n            clientIP,\n            fromPath: req.nextUrl.pathname,\n            toPath: frPath\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(frPath, req.url));\n    }\n    // Enhanced language handling with security validation\n    if (!pathname.startsWith(\"/fr\") && !pathname.startsWith(`/${defaultLocale}`) && !pathname.startsWith(\"/_next\") && !pathname.startsWith(\"/api\") && !pathname.startsWith(\"/static\") && !pathname.includes(\".\")) {\n        // Additional security check for suspicious paths\n        if (pathname.length > 200) {\n            logSecurityEvent(\"SUSPICIOUS_LONG_PATH\", {\n                clientIP,\n                pathLength: pathname.length\n            });\n            return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Bad Request\", {\n                status: 400\n            });\n        }\n        url.pathname = `/en${pathname}`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.rewrite(url);\n    }\n    // Log successful requests for monitoring (in development only)\n    if ( true && user) {\n        logSecurityEvent(\"SUCCESSFUL_REQUEST\", {\n            clientIP,\n            pathname,\n            userId: user._id,\n            roles: user.roles\n        });\n    }\n    return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     * - files with extensions (images, fonts, etc.)\r\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|public|.*\\\\..*).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.js\n");

/***/ })

});