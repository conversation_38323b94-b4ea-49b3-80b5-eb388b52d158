(()=>{var t={};t.id=7846,t.ids=[7846],t.modules={72934:t=>{"use strict";t.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:t=>{"use strict";t.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:t=>{"use strict";t.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:t=>{"use strict";t.exports=require("assert")},78893:t=>{"use strict";t.exports=require("buffer")},61282:t=>{"use strict";t.exports=require("child_process")},84770:t=>{"use strict";t.exports=require("crypto")},17702:t=>{"use strict";t.exports=require("events")},92048:t=>{"use strict";t.exports=require("fs")},32615:t=>{"use strict";t.exports=require("http")},35240:t=>{"use strict";t.exports=require("https")},98216:t=>{"use strict";t.exports=require("net")},19801:t=>{"use strict";t.exports=require("os")},55315:t=>{"use strict";t.exports=require("path")},76162:t=>{"use strict";t.exports=require("stream")},82452:t=>{"use strict";t.exports=require("tls")},74175:t=>{"use strict";t.exports=require("tty")},17360:t=>{"use strict";t.exports=require("url")},21764:t=>{"use strict";t.exports=require("util")},71568:t=>{"use strict";t.exports=require("zlib")},83969:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>a});var n,o=r(95746);function i(){return(i=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}let a=t=>o.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},t),n||(n=o.createElement("path",{fill:"#234791",d:"m16.172 11-5.364-5.364 1.414-1.414L20 12l-7.778 7.778-1.414-1.414L16.172 13H4v-2z"})))},57201:(t,e,r)=>{"use strict";r.d(e,{Z:()=>a});var n,o=r(95746);function i(){return(i=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}let a=t=>o.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},t),n||(n=o.createElement("path",{stroke:"#1D5A9F",strokeWidth:1.5,d:"m12.398 17.396-.398-.25-.398.25-6.852 4.296V3A.25.25 0 0 1 5 2.75h14a.25.25 0 0 1 .25.25v18.692zm-8.03 4.535Z"})))},79336:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>f,pages:()=>l,routeModule:()=>d,tree:()=>s}),r(53361),r(75545),r(23658),r(54864);var n=r(23191),o=r(88716),i=r(37922),a=r.n(i),c=r(95231),u={};for(let t in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(t)&&(u[t]=()=>c[t]);r.d(e,u);let s=["",{children:["[locale]",{children:["(dashboard)",{children:["dashboard",{children:["home",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,53361)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(dashboard)\\dashboard\\home\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,75545)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(dashboard)\\layout.jsx"]}]},{metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,73881))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,23658)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54864)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\not-found.js"],metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,73881))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(dashboard)\\dashboard\\home\\page.jsx"],f="/[locale]/(dashboard)/dashboard/home/<USER>",p={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/[locale]/(dashboard)/dashboard/home/<USER>",pathname:"/[locale]/dashboard/home",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:s}})},64262:(t,e,r)=>{Promise.resolve().then(r.bind(r,71103))},36690:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});var n=r(27522),o=r(10326);let i=(0,n.Z)((0,o.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},99063:(t,e,r)=>{"use strict";r.d(e,{default:()=>y});var n=r(17577),o=r(41135),i=r(14988),a=r(63946),c=r(35627),u=r(41659),s=r(10326),l=r(5028),f=r(52385),p=r(14750);let d=(0,r(71685).Z)("MuiBox",["root"]),h=(0,f.Z)(),y=function(t={}){let{themeId:e,defaultTheme:r,defaultClassName:l="MuiBox-root",generateClassName:f}=t,p=(0,i.ZP)("div",{shouldForwardProp:t=>"theme"!==t&&"sx"!==t&&"as"!==t})(a.Z);return n.forwardRef(function(t,n){let i=(0,u.Z)(r),{className:a,component:d="div",...h}=(0,c.Z)(t);return(0,s.jsx)(p,{as:d,ref:n,className:(0,o.Z)(a,f?f(l):l),theme:e&&i[e]||i,...h})})}({themeId:p.Z,defaultTheme:h,defaultClassName:d.root,generateClassName:l.Z.generate})},10163:(t,e,r)=>{"use strict";r.d(e,{Z:()=>h});var n=r(17577),o=r(41135),i=r(88634),a=r(91703),c=r(2791),u=r(71685),s=r(97898);function l(t){return(0,s.ZP)("MuiDialogActions",t)}(0,u.Z)("MuiDialogActions",["root","spacing"]);var f=r(10326);let p=t=>{let{classes:e,disableSpacing:r}=t;return(0,i.Z)({root:["root",!r&&"spacing"]},l,e)},d=(0,a.ZP)("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(t,e)=>{let{ownerState:r}=t;return[e.root,!r.disableSpacing&&e.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:t})=>!t.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),h=n.forwardRef(function(t,e){let r=(0,c.i)({props:t,name:"MuiDialogActions"}),{className:n,disableSpacing:i=!1,...a}=r,u={...r,disableSpacing:i},s=p(u);return(0,f.jsx)(d,{className:(0,o.Z)(s.root,n),ownerState:u,ref:e,...a})})},28591:(t,e,r)=>{"use strict";r.d(e,{Z:()=>v});var n=r(17577),o=r(41135),i=r(88634),a=r(91703),c=r(30990),u=r(2791),s=r(71685),l=r(97898);function f(t){return(0,l.ZP)("MuiDialogContent",t)}(0,s.Z)("MuiDialogContent",["root","dividers"]);var p=r(64650),d=r(10326);let h=t=>{let{classes:e,dividers:r}=t;return(0,i.Z)({root:["root",r&&"dividers"]},f,e)},y=(0,a.ZP)("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(t,e)=>{let{ownerState:r}=t;return[e.root,r.dividers&&e.dividers]}})((0,c.Z)(({theme:t})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:t})=>t.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(t.vars||t).palette.divider}`,borderBottom:`1px solid ${(t.vars||t).palette.divider}`}},{props:({ownerState:t})=>!t.dividers,style:{[`.${p.Z.root} + &`]:{paddingTop:0}}}]}))),v=n.forwardRef(function(t,e){let r=(0,u.i)({props:t,name:"MuiDialogContent"}),{className:n,dividers:i=!1,...a}=r,c={...r,dividers:i},s=h(c);return(0,d.jsx)(y,{className:(0,o.Z)(s.root,n),ownerState:c,ref:e,...a})})},98117:(t,e,r)=>{"use strict";r.d(e,{Z:()=>h});var n=r(17577),o=r(41135),i=r(88634),a=r(25609),c=r(91703),u=r(2791),s=r(64650),l=r(55733),f=r(10326);let p=t=>{let{classes:e}=t;return(0,i.Z)({root:["root"]},s.a,e)},d=(0,c.ZP)(a.default,{name:"MuiDialogTitle",slot:"Root",overridesResolver:(t,e)=>e.root})({padding:"16px 24px",flex:"0 0 auto"}),h=n.forwardRef(function(t,e){let r=(0,u.i)({props:t,name:"MuiDialogTitle"}),{className:i,id:a,...c}=r,s=p(r),{titleId:h=a}=n.useContext(l.Z);return(0,f.jsx)(d,{component:"h2",className:(0,o.Z)(s.root,i),ownerState:r,ref:e,variant:"h6",id:a??h,...c})})},64650:(t,e,r)=>{"use strict";r.d(e,{Z:()=>a,a:()=>i});var n=r(71685),o=r(97898);function i(t){return(0,o.ZP)("MuiDialogTitle",t)}let a=(0,n.Z)("MuiDialogTitle",["root"])},918:(t,e,r)=>{"use strict";r.d(e,{Z:()=>O});var n=r(17577),o=r(88634),i=r(41135),a=r(39914),c=r(65656),u=r(90943),s=r(6379),l=r(54641),f=r(27080),p=r(91703),d=r(30990),h=r(2791),y=r(71685),v=r(97898);function m(t){return(0,v.ZP)("MuiInputLabel",t)}(0,y.Z)("MuiInputLabel",["root","focused","disabled","error","required","asterisk","formControl","sizeSmall","shrink","animated","standard","filled","outlined"]);var b=r(10326);let g=t=>{let{classes:e,formControl:r,size:n,shrink:i,disableAnimation:a,variant:c,required:u}=t,s={root:["root",r&&"formControl",!a&&"animated",i&&"shrink",n&&"normal"!==n&&`size${(0,l.Z)(n)}`,c],asterisk:[u&&"asterisk"]},f=(0,o.Z)(s,m,e);return{...e,...f}},x=(0,p.ZP)(u.Z,{shouldForwardProp:t=>(0,f.Z)(t)||"classes"===t,name:"MuiInputLabel",slot:"Root",overridesResolver:(t,e)=>{let{ownerState:r}=t;return[{[`& .${s.Z.asterisk}`]:e.asterisk},e.root,r.formControl&&e.formControl,"small"===r.size&&e.sizeSmall,r.shrink&&e.shrink,!r.disableAnimation&&e.animated,r.focused&&e.focused,e[r.variant]]}})((0,d.Z)(({theme:t})=>({display:"block",transformOrigin:"top left",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",variants:[{props:({ownerState:t})=>t.formControl,style:{position:"absolute",left:0,top:0,transform:"translate(0, 20px) scale(1)"}},{props:{size:"small"},style:{transform:"translate(0, 17px) scale(1)"}},{props:({ownerState:t})=>t.shrink,style:{transform:"translate(0, -1.5px) scale(0.75)",transformOrigin:"top left",maxWidth:"133%"}},{props:({ownerState:t})=>!t.disableAnimation,style:{transition:t.transitions.create(["color","transform","max-width"],{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut})}},{props:{variant:"filled"},style:{zIndex:1,pointerEvents:"none",transform:"translate(12px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"filled",size:"small"},style:{transform:"translate(12px, 13px) scale(1)"}},{props:({variant:t,ownerState:e})=>"filled"===t&&e.shrink,style:{userSelect:"none",pointerEvents:"auto",transform:"translate(12px, 7px) scale(0.75)",maxWidth:"calc(133% - 24px)"}},{props:({variant:t,ownerState:e,size:r})=>"filled"===t&&e.shrink&&"small"===r,style:{transform:"translate(12px, 4px) scale(0.75)"}},{props:{variant:"outlined"},style:{zIndex:1,pointerEvents:"none",transform:"translate(14px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"outlined",size:"small"},style:{transform:"translate(14px, 9px) scale(1)"}},{props:({variant:t,ownerState:e})=>"outlined"===t&&e.shrink,style:{userSelect:"none",pointerEvents:"auto",maxWidth:"calc(133% - 32px)",transform:"translate(14px, -9px) scale(0.75)"}}]}))),O=n.forwardRef(function(t,e){let r=(0,h.i)({name:"MuiInputLabel",props:t}),{disableAnimation:n=!1,margin:o,shrink:u,variant:s,className:l,...f}=r,p=(0,c.Z)(),d=u;void 0===d&&p&&(d=p.filled||p.focused||p.adornedStart);let y=(0,a.Z)({props:r,muiFormControl:p,states:["size","variant","required","focused"]}),v={...r,disableAnimation:n,formControl:p,shrink:d,size:y.size,variant:y.variant,required:y.required,focused:y.focused},m=g(v);return(0,b.jsx)(x,{"data-shrink":d,ref:e,className:(0,i.Z)(m.root,l),...f,ownerState:v,classes:m})})},51028:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,s,l=this._events[c],f=arguments.length;if(l.fn){switch(l.once&&this.removeListener(t,l.fn,void 0,!0),f){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,e),!0;case 3:return l.fn.call(l.context,e,n),!0;case 4:return l.fn.call(l.context,e,n,o),!0;case 5:return l.fn.call(l.context,e,n,o,i),!0;case 6:return l.fn.call(l.context,e,n,o,i,a),!0}for(s=1,u=Array(f-1);s<f;s++)u[s-1]=arguments[s];l.fn.apply(l.context,u)}else{var p,d=l.length;for(s=0;s<d;s++)switch(l[s].once&&this.removeListener(t,l[s].fn,void 0,!0),f){case 1:l[s].fn.call(l[s].context);break;case 2:l[s].fn.call(l[s].context,e);break;case 3:l[s].fn.call(l[s].context,e,n);break;case 4:l[s].fn.call(l[s].context,e,n,o);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];l[s].fn.apply(l[s].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,s=[],l=c.length;u<l;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&s.push(c[u]);s.length?this._events[i]=1===s.length?s[0]:s:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},44654:(t,e,r)=>{var n=r(7017)(r(39288),"DataView");t.exports=n},27513:(t,e,r)=>{var n=r(7392),o=r(29247),i=r(84190),a=r(66193),c=r(66681);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},58148:(t,e,r)=>{var n=r(38048),o=r(82142),i=r(83226),a=r(84001),c=r(31127);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},67926:(t,e,r)=>{var n=r(7017)(r(39288),"Map");t.exports=n},30095:(t,e,r)=>{var n=r(86487),o=r(93976),i=r(91053),a=r(29941),c=r(70144);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},9186:(t,e,r)=>{var n=r(7017)(r(39288),"Promise");t.exports=n},39746:(t,e,r)=>{var n=r(7017)(r(39288),"Set");t.exports=n},43484:(t,e,r)=>{var n=r(30095),o=r(51793),i=r(59191);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},82006:(t,e,r)=>{var n=r(58148),o=r(82795),i=r(9113),a=r(80934),c=r(68732),u=r(5525);function s(t){var e=this.__data__=new n(t);this.size=e.size}s.prototype.clear=o,s.prototype.delete=i,s.prototype.get=a,s.prototype.has=c,s.prototype.set=u,t.exports=s},76245:(t,e,r)=>{var n=r(39288).Symbol;t.exports=n},89377:(t,e,r)=>{var n=r(39288).Uint8Array;t.exports=n},35803:(t,e,r)=>{var n=r(7017)(r(39288),"WeakMap");t.exports=n},39137:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},12977:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},17536:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},34776:(t,e,r)=>{var n=r(44658);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},19544:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},77133:(t,e,r)=>{var n=r(84643),o=r(46148),i=r(32966),a=r(10750),c=r(19699),u=r(42191),s=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),l=!r&&o(t),f=!r&&!l&&a(t),p=!r&&!l&&!f&&u(t),d=r||l||f||p,h=d?n(t.length,String):[],y=h.length;for(var v in t)(e||s.call(t,v))&&!(d&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&h.push(v);return h}},29738:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},18939:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},83057:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},72375:t=>{t.exports=function(t){return t.split("")}},33646:(t,e,r)=>{var n=r(64111);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},15216:(t,e,r)=>{var n=r(99e3);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},57706:(t,e,r)=>{var n=r(59796),o=r(15271)(n);t.exports=o},78545:(t,e,r)=>{var n=r(57706);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},18401:(t,e,r)=>{var n=r(76871);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,s=a}return s}},47941:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},24354:(t,e,r)=>{var n=r(18939),o=r(62565);t.exports=function t(e,r,i,a,c){var u=-1,s=e.length;for(i||(i=o),c||(c=[]);++u<s;){var l=e[u];r>0&&i(l)?r>1?t(l,r-1,i,a,c):n(c,l):a||(c[c.length]=l)}return c}},67917:(t,e,r)=>{var n=r(13012)();t.exports=n},59796:(t,e,r)=>{var n=r(67917),o=r(85865);t.exports=function(t,e){return t&&n(t,e,o)}},57305:(t,e,r)=>{var n=r(80204),o=r(1094);t.exports=function(t,e){e=n(e,t);for(var r=0,i=e.length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},46840:(t,e,r)=>{var n=r(18939),o=r(32966);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},1534:(t,e,r)=>{var n=r(76245),o=r(34244),i=r(13390),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},20913:t=>{t.exports=function(t,e){return t>e}},50045:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},44658:(t,e,r)=>{var n=r(47941),o=r(22570),i=r(936);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},79574:(t,e,r)=>{var n=r(1534),o=r(91380);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},88132:(t,e,r)=>{var n=r(80588),o=r(91380);t.exports=function t(e,r,i,a,c){return e===r||(null!=e&&null!=r&&(o(e)||o(r))?n(e,r,i,a,t,c):e!=e&&r!=r)}},80588:(t,e,r)=>{var n=r(82006),o=r(12317),i=r(99487),a=r(1958),c=r(74963),u=r(32966),s=r(10750),l=r(42191),f="[object Arguments]",p="[object Array]",d="[object Object]",h=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),O=g?p:c(e);x=x==f?d:x,O=O==f?d:O;var w=x==d,j=O==d,S=x==O;if(S&&s(t)){if(!s(e))return!1;b=!0,w=!1}if(S&&!w)return m||(m=new n),b||l(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var P=w&&h.call(t,"__wrapped__"),A=j&&h.call(e,"__wrapped__");if(P||A){var E=P?t.value():t,k=A?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},7240:(t,e,r)=>{var n=r(82006),o=r(88132);t.exports=function(t,e,r,i){var a=r.length,c=a,u=!i;if(null==t)return!c;for(t=Object(t);a--;){var s=r[a];if(u&&s[2]?s[1]!==t[s[0]]:!(s[0]in t))return!1}for(;++a<c;){var l=(s=r[a])[0],f=t[l],p=s[1];if(u&&s[2]){if(void 0===f&&!(l in t))return!1}else{var d=new n;if(i)var h=i(f,p,l,t,e,d);if(!(void 0===h?o(p,f,3,i,d):h))return!1}}return!0}},22570:t=>{t.exports=function(t){return t!=t}},74104:(t,e,r)=>{var n=r(85586),o=r(15621),i=r(62880),a=r(81708),c=/^\[object .+?Constructor\]$/,u=Object.prototype,s=Function.prototype.toString,l=u.hasOwnProperty,f=RegExp("^"+s.call(l).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?f:c).test(a(t))}},17633:(t,e,r)=>{var n=r(1534),o=r(99002),i=r(91380),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},44729:(t,e,r)=>{var n=r(71069),o=r(44723),i=r(24576),a=r(32966),c=r(94416);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):c(t)}},54190:(t,e,r)=>{var n=r(83314),o=r(12658),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},16409:t=>{t.exports=function(t,e){return t<e}},64635:(t,e,r)=>{var n=r(57706),o=r(17632);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},71069:(t,e,r)=>{var n=r(7240),o=r(35906),i=r(17106);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},44723:(t,e,r)=>{var n=r(88132),o=r(9459),i=r(1433),a=r(76958),c=r(61623),u=r(17106),s=r(1094);t.exports=function(t,e){return a(t)&&c(e)?u(s(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},40620:(t,e,r)=>{var n=r(29738),o=r(57305),i=r(44729),a=r(64635),c=r(12704),u=r(96291),s=r(52463),l=r(24576),f=r(32966);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[l];var p=-1;return e=n(e,u(i)),c(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return s(t,e,r)})}},43927:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},28446:(t,e,r)=>{var n=r(57305);t.exports=function(t){return function(e){return n(e,t)}}},97502:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,c=r(e((n-t)/(o||1)),0),u=Array(c);c--;)u[i?c:++a]=t,t+=o;return u}},44563:(t,e,r)=>{var n=r(24576),o=r(21112),i=r(234);t.exports=function(t,e){return i(o(t,e,n),t+"")}},44578:(t,e,r)=>{var n=r(44347),o=r(99e3),i=r(24576),a=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i;t.exports=a},55804:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},64993:(t,e,r)=>{var n=r(57706);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},12704:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},84643:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},96115:(t,e,r)=>{var n=r(76245),o=r(29738),i=r(32966),a=r(76871),c=1/0,u=n?n.prototype:void 0,s=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return s?s.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},37192:(t,e,r)=>{var n=r(5587),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},96291:t=>{t.exports=function(t){return function(e){return t(e)}}},21213:(t,e,r)=>{var n=r(43484),o=r(34776),i=r(19544),a=r(5354),c=r(51512),u=r(78874);t.exports=function(t,e,r){var s=-1,l=o,f=t.length,p=!0,d=[],h=d;if(r)p=!1,l=i;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,l=a,h=new n}else h=e?[]:d;t:for(;++s<f;){var v=t[s],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=h.length;b--;)if(h[b]===m)continue t;e&&h.push(m),d.push(v)}else l(h,m,r)||(h!==d&&h.push(m),d.push(v))}return d}},5354:t=>{t.exports=function(t,e){return t.has(e)}},80204:(t,e,r)=>{var n=r(32966),o=r(76958),i=r(31364),a=r(41029);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},23136:(t,e,r)=>{var n=r(55804);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},17523:(t,e,r)=>{var n=r(76871);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),c=void 0!==e,u=null===e,s=e==e,l=n(e);if(!u&&!l&&!a&&t>e||a&&c&&s&&!u&&!l||o&&c&&s||!r&&s||!i)return 1;if(!o&&!a&&!l&&t<e||l&&r&&i&&!o&&!a||u&&r&&i||!c&&i||!s)return -1}return 0}},52463:(t,e,r)=>{var n=r(17523);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,c=i.length,u=r.length;++o<c;){var s=n(i[o],a[o]);if(s){if(o>=u)return s;return s*("desc"==r[o]?-1:1)}}return t.index-e.index}},35987:(t,e,r)=>{var n=r(39288)["__core-js_shared__"];t.exports=n},15271:(t,e,r)=>{var n=r(17632);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,c=Object(r);(e?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},13012:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}},64362:(t,e,r)=>{var n=r(23136),o=r(16888),i=r(78041),a=r(41029);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},59698:(t,e,r)=>{var n=r(44729),o=r(17632),i=r(85865);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!o(e)){var u=n(r,3);e=i(e),r=function(t){return u(c[t],t,c)}}var s=t(e,r,a);return s>-1?c[u?e[s]:s]:void 0}}},81592:(t,e,r)=>{var n=r(97502),o=r(47760),i=r(72616);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},51512:(t,e,r)=>{var n=r(39746),o=r(73525),i=r(78874),a=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o;t.exports=a},99e3:(t,e,r)=>{var n=r(7017),o=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},12317:(t,e,r)=>{var n=r(43484),o=r(83057),i=r(5354);t.exports=function(t,e,r,a,c,u){var s=1&r,l=t.length,f=e.length;if(l!=f&&!(s&&f>l))return!1;var p=u.get(t),d=u.get(e);if(p&&d)return p==e&&d==t;var h=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++h<l;){var m=t[h],b=e[h];if(a)var g=s?a(b,m,h,e,t,u):a(m,b,h,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},99487:(t,e,r)=>{var n=r(76245),o=r(89377),i=r(64111),a=r(12317),c=r(59616),u=r(78874),s=n?n.prototype:void 0,l=s?s.valueOf:void 0;t.exports=function(t,e,r,n,s,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new o(t),new o(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var d=c;case"[object Set]":var h=1&n;if(d||(d=u),t.size!=e.size&&!h)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(d(t),d(e),n,s,f,p);return p.delete(t),v;case"[object Symbol]":if(l)return l.call(t)==l.call(e)}return!1}},1958:(t,e,r)=>{var n=r(67),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,c){var u=1&r,s=n(t),l=s.length;if(l!=n(e).length&&!u)return!1;for(var f=l;f--;){var p=s[f];if(!(u?p in e:o.call(e,p)))return!1}var d=c.get(t),h=c.get(e);if(d&&h)return d==e&&h==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<l;){var m=t[p=s[f]],b=e[p];if(i)var g=u?i(b,m,p,e,t,c):i(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,i,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,O=e.constructor;x!=O&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof O&&O instanceof O)&&(y=!1)}return c.delete(t),c.delete(e),y}},37611:t=>{var e="object"==typeof global&&global&&global.Object===Object&&global;t.exports=e},67:(t,e,r)=>{var n=r(46840),o=r(26102),i=r(85865);t.exports=function(t){return n(t,i,o)}},4326:(t,e,r)=>{var n=r(24587);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},35906:(t,e,r)=>{var n=r(61623),o=r(85865);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},7017:(t,e,r)=>{var n=r(74104),o=r(12751);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},89083:(t,e,r)=>{var n=r(87181)(Object.getPrototypeOf,Object);t.exports=n},34244:(t,e,r)=>{var n=r(76245),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[c]=r:delete t[c]),o}},26102:(t,e,r)=>{var n=r(17536),o=r(95252),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,c=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return i.call(t,e)})}:o;t.exports=c},74963:(t,e,r)=>{var n=r(44654),o=r(67926),i=r(9186),a=r(39746),c=r(35803),u=r(1534),s=r(81708),l="[object Map]",f="[object Promise]",p="[object Set]",d="[object WeakMap]",h="[object DataView]",y=s(n),v=s(o),m=s(i),b=s(a),g=s(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=h||o&&x(new o)!=l||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=d)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?s(r):"";if(n)switch(n){case y:return h;case v:return l;case m:return f;case b:return p;case g:return d}return e}),t.exports=x},12751:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},23932:(t,e,r)=>{var n=r(80204),o=r(46148),i=r(32966),a=r(19699),c=r(99002),u=r(1094);t.exports=function(t,e,r){e=n(e,t);for(var s=-1,l=e.length,f=!1;++s<l;){var p=u(e[s]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++s!=l?f:!!(l=null==t?0:t.length)&&c(l)&&a(p,l)&&(i(t)||o(t))}},16888:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},7392:(t,e,r)=>{var n=r(67193);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},29247:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},84190:(t,e,r)=>{var n=r(67193),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},66193:(t,e,r)=>{var n=r(67193),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},66681:(t,e,r)=>{var n=r(67193);t.exports=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},62565:(t,e,r)=>{var n=r(76245),o=r(46148),i=r(32966),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},19699:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},47760:(t,e,r)=>{var n=r(64111),o=r(17632),i=r(19699),a=r(62880);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(o(r)&&i(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},76958:(t,e,r)=>{var n=r(32966),o=r(76871),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},24587:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},15621:(t,e,r)=>{var n=r(35987),o=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!o&&o in t}},83314:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},61623:(t,e,r)=>{var n=r(62880);t.exports=function(t){return t==t&&!n(t)}},38048:t=>{t.exports=function(){this.__data__=[],this.size=0}},82142:(t,e,r)=>{var n=r(33646),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},83226:(t,e,r)=>{var n=r(33646);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},84001:(t,e,r)=>{var n=r(33646);t.exports=function(t){return n(this.__data__,t)>-1}},31127:(t,e,r)=>{var n=r(33646);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},86487:(t,e,r)=>{var n=r(27513),o=r(58148),i=r(67926);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},93976:(t,e,r)=>{var n=r(4326);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=e?1:0,e}},91053:(t,e,r)=>{var n=r(4326);t.exports=function(t){return n(this,t).get(t)}},29941:(t,e,r)=>{var n=r(4326);t.exports=function(t){return n(this,t).has(t)}},70144:(t,e,r)=>{var n=r(4326);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=r.size==o?0:1,this}},59616:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},17106:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},85244:(t,e,r)=>{var n=r(97300);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},67193:(t,e,r)=>{var n=r(7017)(Object,"create");t.exports=n},12658:(t,e,r)=>{var n=r(87181)(Object.keys,Object);t.exports=n},7553:(t,e,r)=>{t=r.nmd(t);var n=r(37611),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,c=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},13390:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},87181:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},21112:(t,e,r)=>{var n=r(39137),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,c=o(i.length-e,0),u=Array(c);++a<c;)u[a]=i[e+a];a=-1;for(var s=Array(e+1);++a<e;)s[a]=i[a];return s[e]=r(u),n(t,this,s)}}},39288:(t,e,r)=>{var n=r(37611),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();t.exports=i},51793:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},59191:t=>{t.exports=function(t){return this.__data__.has(t)}},78874:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},234:(t,e,r)=>{var n=r(44578),o=r(72347)(n);t.exports=o},72347:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},82795:(t,e,r)=>{var n=r(58148);t.exports=function(){this.__data__=new n,this.size=0}},9113:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},80934:t=>{t.exports=function(t){return this.__data__.get(t)}},68732:t=>{t.exports=function(t){return this.__data__.has(t)}},5525:(t,e,r)=>{var n=r(58148),o=r(67926),i=r(30095);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},936:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return -1}},78041:(t,e,r)=>{var n=r(72375),o=r(16888),i=r(38582);t.exports=function(t){return o(t)?i(t):n(t)}},31364:(t,e,r)=>{var n=r(85244),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e});t.exports=a},1094:(t,e,r)=>{var n=r(76871),o=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},81708:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},5587:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},38582:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",s="(?:\\u200d(?:"+[o,i,a].join("|")+")"+u+c+")*",l=RegExp(n+"(?="+n+")|(?:"+[o+r+"?",r,i,a,"["+e+"]"].join("|")+")"+(u+c+s),"g");t.exports=function(t){return t.match(l)||[]}},44347:t=>{t.exports=function(t){return function(){return t}}},19788:(t,e,r)=>{var n=r(62880),o=r(22695),i=r(41309),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,s,l,f,p,d,h=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=s;return u=s=void 0,h=e,f=t.apply(n,r)}function g(t){var r=t-d,n=t-h;return void 0===d||r>=e||r<0||v&&n>=l}function x(){var t,r,n,i=o();if(g(i))return O(i);p=setTimeout(x,(t=i-d,r=i-h,n=e-t,v?c(n,l-r):n))}function O(t){return(p=void 0,m&&u)?b(t):(u=s=void 0,f)}function w(){var t,r=o(),n=g(r);if(u=arguments,s=this,d=r,n){if(void 0===p)return h=t=d,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(d)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,l=(v="maxWait"in r)?a(i(r.maxWait)||0,e):l,m="trailing"in r?!!r.trailing:m),w.cancel=function(){void 0!==p&&clearTimeout(p),h=0,u=d=s=p=void 0},w.flush=function(){return void 0===p?f:O(o())},w}},64111:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},10853:(t,e,r)=>{var n=r(12977),o=r(78545),i=r(44729),a=r(32966),c=r(47760);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},9660:(t,e,r)=>{var n=r(59698)(r(38988));t.exports=n},38988:(t,e,r)=>{var n=r(47941),o=r(44729),i=r(57576),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:i(r);return u<0&&(u=a(c+u,0)),n(t,o(e,3),u)}},78352:(t,e,r)=>{var n=r(24354),o=r(59866);t.exports=function(t,e){return n(o(t,e),1)}},9459:(t,e,r)=>{var n=r(57305);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},1433:(t,e,r)=>{var n=r(50045),o=r(23932);t.exports=function(t,e){return null!=t&&o(t,e,n)}},24576:t=>{t.exports=function(t){return t}},46148:(t,e,r)=>{var n=r(79574),o=r(91380),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable,u=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!c.call(t,"callee")};t.exports=u},32966:t=>{var e=Array.isArray;t.exports=e},17632:(t,e,r)=>{var n=r(85586),o=r(99002);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},99388:(t,e,r)=>{var n=r(1534),o=r(91380);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},10750:(t,e,r)=>{t=r.nmd(t);var n=r(39288),o=r(89531),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,c=a&&a.exports===i?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||o},81711:(t,e,r)=>{var n=r(88132);t.exports=function(t,e){return n(t,e)}},85586:(t,e,r)=>{var n=r(1534),o=r(62880);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},99002:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},81719:(t,e,r)=>{var n=r(77717);t.exports=function(t){return n(t)&&t!=+t}},20119:t=>{t.exports=function(t){return null==t}},77717:(t,e,r)=>{var n=r(1534),o=r(91380);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},62880:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},91380:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},36153:(t,e,r)=>{var n=r(1534),o=r(89083),i=r(91380),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,s=c.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==s}},4891:(t,e,r)=>{var n=r(1534),o=r(32966),i=r(91380);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},76871:(t,e,r)=>{var n=r(1534),o=r(91380);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},42191:(t,e,r)=>{var n=r(17633),o=r(96291),i=r(7553),a=i&&i.isTypedArray,c=a?o(a):n;t.exports=c},85865:(t,e,r)=>{var n=r(77133),o=r(54190),i=r(17632);t.exports=function(t){return i(t)?n(t):o(t)}},90601:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},59866:(t,e,r)=>{var n=r(29738),o=r(44729),i=r(64635),a=r(32966);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},13880:(t,e,r)=>{var n=r(15216),o=r(59796),i=r(44729);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},31955:(t,e,r)=>{var n=r(18401),o=r(20913),i=r(24576);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},97300:(t,e,r)=>{var n=r(30095);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},52692:(t,e,r)=>{var n=r(18401),o=r(16409),i=r(24576);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},73525:t=>{t.exports=function(){}},22695:(t,e,r)=>{var n=r(39288);t.exports=function(){return n.Date.now()}},94416:(t,e,r)=>{var n=r(43927),o=r(28446),i=r(76958),a=r(1094);t.exports=function(t){return i(t)?n(a(t)):o(t)}},65680:(t,e,r)=>{var n=r(81592)();t.exports=n},34009:(t,e,r)=>{var n=r(83057),o=r(44729),i=r(64993),a=r(32966),c=r(47760);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},77529:(t,e,r)=>{var n=r(24354),o=r(40620),i=r(44563),a=r(47760),c=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])});t.exports=c},95252:t=>{t.exports=function(){return[]}},89531:t=>{t.exports=function(){return!1}},69450:(t,e,r)=>{var n=r(19788),o=r(62880);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},72616:(t,e,r)=>{var n=r(41309),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},57576:(t,e,r)=>{var n=r(72616);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},41309:(t,e,r)=>{var n=r(37192),o=r(62880),i=r(76871),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,s=/^0o[0-7]+$/i,l=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||s.test(t)?l(t.slice(2),r?2:8):c.test(t)?a:+t}},41029:(t,e,r)=>{var n=r(96115);t.exports=function(t){return null==t?"":n(t)}},82511:(t,e,r)=>{var n=r(44729),o=r(21213);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},32009:(t,e,r)=>{var n=r(64362)("toUpperCase");t.exports=n},99899:(t,e,r)=>{"use strict";var n=r(56715);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},78439:(t,e,r)=>{t.exports=r(99899)()},56715:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},98255:(t,e)=>{"use strict";var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),u=Symbol.for("react.context"),s=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),h=Symbol.for("react.lazy");Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case r:switch(t=t.type){case o:case a:case i:case f:case p:return t;default:switch(t=t&&t.$$typeof){case s:case u:case l:case h:case d:case c:return t;default:return e}}case n:return e}}}(t)===o}},29507:(t,e,r)=>{"use strict";t.exports=r(98255)},71103:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>l5});var n={};r.r(n),r.d(n,{scaleBand:()=>nc.Z,scaleDiverging:()=>function t(){var e=(0,nW.Q)(ot()(nV.yR));return e.copy=function(){return(0,n7.JG)(e,t())},nH.O.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=(0,nF.Q)(ot()).domain([.1,1,10]);return e.copy=function(){return(0,n7.JG)(e,t()).base(e.base())},nH.O.apply(e,arguments)},scaleDivergingPow:()=>oe,scaleDivergingSqrt:()=>or,scaleDivergingSymlog:()=>function t(){var e=(0,nX.P)(ot());return e.copy=function(){return(0,n7.JG)(e,t()).constant(e.constant())},nH.O.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t=+t)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,nU.Z),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,nU.Z):[0,1],(0,nW.Q)(n)},scaleImplicit:()=>nG.O,scaleLinear:()=>nW.Z,scaleLog:()=>nF.Z,scaleOrdinal:()=>nG.Z,scalePoint:()=>nc.x,scalePow:()=>n$.ZP,scaleQuantile:()=>function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=n2.Z){if(!(!(n=t.length)||isNaN(e=+e))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t=+t)?e:n[(0,n3.ZP)(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e=+e)||r.push(e);return r.sort(nQ.Z),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},nH.o.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[(0,n3.ZP)(i,t,0,o)]:e}function u(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r=+r,n=+n,u()):[r,n]},c.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return i.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},nH.o.apply((0,nW.Q)(c),arguments)},scaleRadial:()=>function t(){var e,r=(0,nV.ZP)(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(nK(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,nU.Z)).map(nK)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},nH.o.apply(i,arguments),(0,nW.Q)(i)},scaleSequential:()=>n7.ZP,scaleSequentialLog:()=>n7.S5,scaleSequentialPow:()=>n7.UN,scaleSequentialQuantile:()=>function t(){var e=[],r=nV.yR;function n(t){if(null!=t&&!isNaN(t=+t))return r(((0,n3.ZP)(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r=+r)||e.push(r);return e.sort(nQ.Z),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from((0,n2.K)(t,void 0))).length)||isNaN(e=+e))){if(e<=0||n<2)return nJ(t);if(e>=1)return nY(t);var n,o=(n-1)*e,i=Math.floor(o),a=nY((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?n0:function(t=nQ.Z){if(t===nQ.Z)return n0;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,c=r-n+1,u=Math.log(a),s=.5*Math.exp(2*u/3),l=.5*Math.sqrt(u*s*(a-s)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*s/a+l)),p=Math.min(o,Math.floor(r+(a-c)*s/a+l));t(e,r,f,p,i)}let a=e[r],c=n,u=o;for(n1(e,n,r),i(e[o],a)>0&&n1(e,n,o);c<u;){for(n1(e,c,u),++c,--u;0>i(e[c],a);)++c;for(;i(e[u],a)>0;)--u}0===i(e[n],a)?n1(e,n,u):n1(e,++u,o),u<=r&&(n=u+1),r<=u&&(o=u-1)}return e})(t,i).subarray(0,i+1));return a+(nJ(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},nH.O.apply(n,arguments)},scaleSequentialSqrt:()=>n7.L2,scaleSequentialSymlog:()=>n7.cV,scaleSqrt:()=>n$._b,scaleSymlog:()=>nX.Z,scaleThreshold:()=>n6.Z,scaleTime:()=>n5.Z,scaleUtc:()=>n4.Z,tickFormat:()=>oi.Z});var o=r(10326),i=r(52210),a=r(17577),c=r.n(a),u=r(91703),s=r(89178),l=r(16027),f=r(34039),p=r(78439),d=r.n(p),h=function(t,e){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)};function y(t){var e,r,n,o,i,c,u,s=t.className,l=t.counterClockwise,f=t.dashRatio,p=t.pathRadius,d=t.strokeWidth,h=t.style;return(0,a.createElement)("path",{className:s,style:Object.assign({},h,(r=(e={pathRadius:p,dashRatio:f,counterClockwise:l}).counterClockwise,o=(1-e.dashRatio)*(n=2*Math.PI*e.pathRadius),{strokeDasharray:n+"px "+n+"px",strokeDashoffset:(r?-o:o)+"px"})),d:"\n      M 50,50\n      m 0,-"+(c=(i={pathRadius:p,counterClockwise:l}).pathRadius)+"\n      a "+c+","+c+" "+(u=i.counterClockwise?1:0)+" 1 1 0,"+2*c+"\n      a "+c+","+c+" "+u+" 1 1 0,-"+2*c+"\n    ",strokeWidth:d,fillOpacity:0})}var v=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return function(t,e){function r(){this.constructor=t}h(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}(e,t),e.prototype.getBackgroundPadding=function(){return this.props.background?this.props.backgroundPadding:0},e.prototype.getPathRadius=function(){return 50-this.props.strokeWidth/2-this.getBackgroundPadding()},e.prototype.getPathRatio=function(){var t=this.props,e=t.value,r=t.minValue,n=t.maxValue;return(Math.min(Math.max(e,r),n)-r)/(n-r)},e.prototype.render=function(){var t=this.props,e=t.circleRatio,r=t.className,n=t.classes,o=t.counterClockwise,i=t.styles,c=t.strokeWidth,u=t.text,s=this.getPathRadius(),l=this.getPathRatio();return(0,a.createElement)("svg",{className:n.root+" "+r,style:i.root,viewBox:"0 0 100 100","data-test-id":"CircularProgressbar"},this.props.background?(0,a.createElement)("circle",{className:n.background,style:i.background,cx:50,cy:50,r:50}):null,(0,a.createElement)(y,{className:n.trail,counterClockwise:o,dashRatio:e,pathRadius:s,strokeWidth:c,style:i.trail}),(0,a.createElement)(y,{className:n.path,counterClockwise:o,dashRatio:l*e,pathRadius:s,strokeWidth:c,style:i.path}),u?(0,a.createElement)("text",{className:n.text,style:i.text,x:50,y:50},u):null)},e.defaultProps={background:!1,backgroundPadding:0,circleRatio:1,classes:{root:"CircularProgressbar",trail:"CircularProgressbar-trail",path:"CircularProgressbar-path",text:"CircularProgressbar-text",background:"CircularProgressbar-background"},counterClockwise:!1,className:"",maxValue:100,minValue:0,strokeWidth:8,styles:{root:{},trail:{},path:{},text:{},background:{}},text:""},e}(a.Component);function m(t){return Object.keys(t).forEach(function(e){null==t[e]&&delete t[e]}),t}r(94230);let b={src:"/_next/static/media/userdashicon.7ade31d7.png"};var g=r(15082),x=r(97980);function O({percentage:t}){var e,r,n,c,u,s,l,f,p,d,h,y;let[O,w]=(0,a.useState)(0),{t:j}=(0,i.$G)();return(0,a.useRef)(O),(0,a.useRef)(null),(0,o.jsxs)("div",{className:"progress-container",children:[o.jsx("p",{className:"profile-title",children:j("HomeDashboard:titleProgress")}),(0,o.jsxs)("div",{className:"gauge-container",style:{position:"relative",width:"150px",height:"150px"},children:[o.jsx(v,{value:t,maxValue:100,strokeWidth:6,styles:(r=(e={pathColor:"#234791",trailColor:"#f8fafd",strokeLinecap:"round",strokeMiterlimit:"2",textSize:"24px",pathTransitionDuration:2}).rotation,n=e.strokeLinecap,c=e.textColor,u=e.textSize,s=e.pathColor,l=e.pathTransition,f=e.pathTransitionDuration,p=e.trailColor,d=e.backgroundColor,{root:{},path:m({stroke:s,strokeLinecap:n,transform:h=null==r?void 0:"rotate("+r+"turn)",transformOrigin:y=null==r?void 0:"center center",transition:l,transitionDuration:null==f?void 0:f+"s"}),trail:m({stroke:p,strokeLinecap:n,transform:h,transformOrigin:y}),text:m({fill:c,fontSize:u}),background:m({fill:d})})}),o.jsx("div",{className:"view-info",style:{position:"absolute",top:"35%",left:"50%",transform:"translate(-50%, -50%)",textAlign:"center"},children:o.jsx("img",{src:b.src,className:"user-icon-progress",alt:"User Icon",loading:"lazy",style:{width:"32px",height:"32px",borderRadius:"50%"}})}),"  ",(0,o.jsxs)("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translateX(-50%)",color:"#234791",fontSize:"30px",fontWeight:"600",textAlign:"center"},children:[Math.round(t),"%"]})]}),o.jsx("div",{className:"upgrade-button",children:o.jsx(g.default,{className:"btn btn-homepage btn-filled",text:j("HomeDashboard:Upgrade"),link:`/${x.pf.baseURL.route}/my-profile`})})]})}O.propTypes={percentage:d().number.isRequired};var w=r(20119),j=r.n(w),S=r(85586),P=r.n(S),A=r(65680),E=r.n(A),k=r(9459),_=r.n(k),T=r(77529),M=r.n(T),C=r(69450),I=r.n(C),N=r(41135);function D(t,e){if(!t)throw Error("Invariant failed")}var B=r(4891),R=r.n(B),L=r(62880),z=r.n(L),Z=r(29507),q=r(81719),W=r.n(q),U=r(77717),F=r.n(U),X=function(t){return 0===t?0:t>0?1:-1},G=function(t){return R()(t)&&t.indexOf("%")===t.length-1},$=function(t){return F()(t)&&!W()(t)},V=function(t){return $(t)||R()(t)},H=0,K=function(t){var e=++H;return"".concat(t||"").concat(e)},Y=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!$(t)&&!R()(t))return n;if(G(t)){var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return W()(r)&&(r=n),o&&r>e&&(r=e),r},J=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},Q=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},tt=function(t,e){return $(t)&&$(e)?function(r){return t+r*(e-t)}:function(){return e}};function te(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):_()(t,e))===r}):null}var tr=function(t,e){return $(t)&&$(e)?t-e:R()(t)&&R()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))};function tn(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function to(t){return(to="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var ti=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],ta=["points","pathLength"],tc={svg:["viewBox","children"],polygon:ta,polyline:ta},tu=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],ts=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,a.isValidElement)(t)&&(r=t.props),!z()(r))return null;var n={};return Object.keys(r).forEach(function(t){tu.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},tl=function(t,e,r){if(!z()(t)||"object"!==to(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];tu.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n},tf=["children"],tp=["children"];function td(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var th={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},ty=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},tv=null,tm=null,tb=function t(e){if(e===tv&&Array.isArray(tm))return tm;var r=[];return a.Children.forEach(e,function(e){j()(e)||((0,Z.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),tm=r,tv=e,r};function tg(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return ty(t)}):[ty(e)],tb(t).forEach(function(t){var e=_()(t,"type.displayName")||_()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function tx(t,e){var r=tg(t,e);return r&&r[0]}var tO=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!$(r)&&!(r<=0)&&!!$(n)&&!(n<=0)},tw=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tj=function(t,e,r,n){var o,i=null!==(o=null==tc?void 0:tc[n])&&void 0!==o?o:[];return e.startsWith("data-")||!P()(t)&&(n&&i.includes(e)||ti.includes(e))||r&&tu.includes(e)},tS=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,a.isValidElement)(t)&&(n=t.props),!z()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;tj(null===(i=n)||void 0===i?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},tP=function t(e,r){if(e===r)return!0;var n=a.Children.count(e);if(n!==a.Children.count(r))return!1;if(0===n)return!0;if(1===n)return tA(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=e[o],c=r[o];if(Array.isArray(i)||Array.isArray(c)){if(!t(i,c))return!1}else if(!tA(i,c))return!1}return!0},tA=function(t,e){if(j()(t)&&j()(e))return!0;if(!j()(t)&&!j()(e)){var r=t.props||{},n=r.children,o=td(r,tf),i=e.props||{},a=i.children,c=td(i,tp);if(n&&a)return tn(o,c)&&tP(n,a);if(!n&&!a)return tn(o,c)}return!1},tE=function(t,e){var r=[],n={};return tb(t).forEach(function(t,o){if(t&&t.type&&R()(t.type)&&tw.indexOf(t.type)>=0)r.push(t);else if(t){var i=ty(t.type),a=e[i]||{},c=a.handler,u=a.once;if(c&&(!u||!n[i])){var s=c(t,i,o);r.push(s),n[i]=!0}}}),r},tk=function(t){var e=t&&t.type;return e&&th[e]?th[e]:null},t_=["children","width","height","viewBox","className","style","title","desc"];function tT(){return(tT=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tM(t){var e=t.children,r=t.width,n=t.height,o=t.viewBox,i=t.className,a=t.style,u=t.title,s=t.desc,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,t_),f=o||{width:r,height:n,x:0,y:0},p=(0,N.Z)("recharts-surface",i);return c().createElement("svg",tT({},tS(l,!0,"svg"),{className:p,width:r,height:n,style:a,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),c().createElement("title",null,u),c().createElement("desc",null,s),e)}var tC=["children","className"];function tI(){return(tI=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tN=c().forwardRef(function(t,e){var r=t.children,n=t.className,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tC),i=(0,N.Z)("recharts-layer",n);return c().createElement("g",tI({className:i},tS(o,!0),{ref:e}),r)});function tD(t){return(tD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tB(){return(tB=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tR(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tL(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tz(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tL(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=tD(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tD(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tL(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tZ(t){return Array.isArray(t)&&V(t[0])&&V(t[1])?t.join(" ~ "):t}var tq=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,o=t.itemStyle,i=void 0===o?{}:o,a=t.labelStyle,u=t.payload,s=t.formatter,l=t.itemSorter,f=t.wrapperClassName,p=t.labelClassName,d=t.label,h=t.labelFormatter,y=t.accessibilityLayer,v=tz({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),m=tz({margin:0},void 0===a?{}:a),b=!j()(d),g=b?d:"",x=(0,N.Z)("recharts-default-tooltip",f),O=(0,N.Z)("recharts-tooltip-label",p);return b&&h&&null!=u&&(g=h(d,u)),c().createElement("div",tB({className:x,style:v},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),c().createElement("p",{className:O,style:m},c().isValidElement(g)?g:"".concat(g)),function(){if(u&&u.length){var t=(l?M()(u,l):u).map(function(t,e){if("none"===t.type)return null;var n=tz({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},i),o=t.formatter||s||tZ,a=t.value,l=t.name,f=a,p=l;if(o&&null!=f&&null!=p){var d=o(a,l,t,e,u);if(Array.isArray(d)){var h=function(t){if(Array.isArray(t))return t}(d)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(d,2)||function(t,e){if(t){if("string"==typeof t)return tR(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tR(t,2)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=h[0],p=h[1]}else f=d}return c().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},V(p)?c().createElement("span",{className:"recharts-tooltip-item-name"},p):null,V(p)?c().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,c().createElement("span",{className:"recharts-tooltip-item-value"},f),c().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return c().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function tW(t){return(tW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tU(t,e,r){var n;return(n=function(t,e){if("object"!=tW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==tW(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var tF="recharts-tooltip-wrapper",tX={visibility:"hidden"};function tG(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,s=t.viewBoxDimension;if(i&&$(i[n]))return i[n];var l=r[n]-c-o,f=r[n]+o;return e[n]?a[n]?l:f:a[n]?l<u[n]?Math.max(f,u[n]):Math.max(l,u[n]):f+c>u[n]+s?Math.max(l,u[n]):Math.max(f,u[n])}function t$(t){return(t$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tH(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tV(Object(r),!0).forEach(function(e){tQ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tK(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tK=function(){return!!t})()}function tY(t){return(tY=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tJ(t,e){return(tJ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tQ(t,e,r){return(e=t0(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function t0(t){var e=function(t,e){if("object"!=t$(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t$(e)?e:e+""}var t1=function(t){var e;function r(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r);for(var t,e,n,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r,n=[].concat(i),e=tY(e),tQ(t=function(t,e){if(e&&("object"===t$(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,tK()?Reflect.construct(e,n||[],tY(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),tQ(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tJ(t,e)}(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,o,i,a,u,s,l,f,p,d,h,y,v,m,b,g,x=this,O=this.props,w=O.active,j=O.allowEscapeViewBox,S=O.animationDuration,P=O.animationEasing,A=O.children,E=O.coordinate,k=O.hasPayload,_=O.isAnimationActive,T=O.offset,M=O.position,C=O.reverseDirection,I=O.useTranslate3d,D=O.viewBox,B=O.wrapperStyle,R=(p=(t={allowEscapeViewBox:j,coordinate:E,offsetTopLeft:T,position:M,reverseDirection:C,tooltipBox:this.state.lastBoundingBox,useTranslate3d:I,viewBox:D}).allowEscapeViewBox,d=t.coordinate,h=t.offsetTopLeft,y=t.position,v=t.reverseDirection,m=t.tooltipBox,b=t.useTranslate3d,g=t.viewBox,m.height>0&&m.width>0&&d?(r=(e={translateX:l=tG({allowEscapeViewBox:p,coordinate:d,key:"x",offsetTopLeft:h,position:y,reverseDirection:v,tooltipDimension:m.width,viewBox:g,viewBoxDimension:g.width}),translateY:f=tG({allowEscapeViewBox:p,coordinate:d,key:"y",offsetTopLeft:h,position:y,reverseDirection:v,tooltipDimension:m.height,viewBox:g,viewBoxDimension:g.height}),useTranslate3d:b}).translateX,n=e.translateY,s={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):s=tX,{cssProperties:s,cssClasses:(i=(o={translateX:l,translateY:f,coordinate:d}).coordinate,a=o.translateX,u=o.translateY,(0,N.Z)(tF,tU(tU(tU(tU({},"".concat(tF,"-right"),$(a)&&i&&$(i.x)&&a>=i.x),"".concat(tF,"-left"),$(a)&&i&&$(i.x)&&a<i.x),"".concat(tF,"-bottom"),$(u)&&i&&$(i.y)&&u>=i.y),"".concat(tF,"-top"),$(u)&&i&&$(i.y)&&u<i.y)))}),L=R.cssClasses,z=R.cssProperties,Z=tH(tH({transition:_&&w?"transform ".concat(S,"ms ").concat(P):void 0},z),{},{pointerEvents:"none",visibility:!this.state.dismissed&&w&&k?"visible":"hidden",position:"absolute",top:0,left:0},B);return c().createElement("div",{tabIndex:-1,className:L,style:Z,ref:function(t){x.wrapperNode=t}},A)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,t0(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a.PureComponent),t2={isSsr:!0,get:function(t){return t2[t]},set:function(t,e){if("string"==typeof t)t2[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){t2[e]=t[e]})}}},t3=r(82511),t6=r.n(t3);function t5(t,e,r){return!0===e?t6()(t,r):P()(e)?t6()(t,e):t}function t4(t){return(t4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t7(Object(r),!0).forEach(function(e){er(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t8(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(t8=function(){return!!t})()}function et(t){return(et=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ee(t,e){return(ee=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function er(t,e,r){return(e=en(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function en(t){var e=function(t,e){if("object"!=t4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t4(e)?e:e+""}function eo(t){return t.dataKey}var ei=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=et(t),function(t,e){if(e&&("object"===t4(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,t8()?Reflect.construct(t,e||[],et(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ee(t,e)}(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,a=r.animationEasing,u=r.content,s=r.coordinate,l=r.filterNull,f=r.isAnimationActive,p=r.offset,d=r.payload,h=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=d?d:[];l&&x.length&&(x=t5(d.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),h,eo));var O=x.length>0;return c().createElement(t1,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:f,active:n,coordinate:s,hasPayload:O,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(t=t9(t9({},this.props),{},{payload:x}),c().isValidElement(u)?c().cloneElement(u,t):"function"==typeof u?c().createElement(u,t):c().createElement(tq,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,en(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a.PureComponent);er(ei,"displayName","Tooltip"),er(ei,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!t2.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var ea=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},ec=r(32009),eu=r.n(ec),es=r(60430);let el={draw(t,e){let r=(0,es._b)(e/es.pi);t.moveTo(r,0),t.arc(0,0,r,0,es.BZ)}},ef=(0,es._b)(1/3),ep=2*ef,ed=(0,es.O$)(es.pi/10)/(0,es.O$)(7*es.pi/10),eh=(0,es.O$)(es.BZ/10)*ed,ey=-(0,es.mC)(es.BZ/10)*ed,ev=(0,es._b)(3),em=(0,es._b)(3)/2,eb=1/(0,es._b)(12),eg=(eb/2+1)*3;var ex=r(68223),eO=r(4443);function ew(t){return(ew="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}(0,es._b)(3),(0,es._b)(3);var ej=["type","size","sizeType"];function eS(){return(eS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eP(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=ew(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ew(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ew(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var eE={symbolCircle:el,symbolCross:{draw(t,e){let r=(0,es._b)(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=(0,es._b)(e/ep),n=r*ef;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=(0,es._b)(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=(0,es._b)(.8908130915292852*e),n=eh*r,o=ey*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=es.BZ*e/5,a=(0,es.mC)(i),c=(0,es.O$)(i);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*o,c*n+a*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-(0,es._b)(e/(3*ev));t.moveTo(0,2*r),t.lineTo(-ev*r,-r),t.lineTo(ev*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=(0,es._b)(e/eg),n=r/2,o=r*eb,i=r*eb+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-em*o,em*n+-.5*o),t.lineTo(-.5*n-em*i,em*n+-.5*i),t.lineTo(-.5*a-em*i,em*a+-.5*i),t.lineTo(-.5*n+em*o,-.5*o-em*n),t.lineTo(-.5*n+em*i,-.5*i-em*n),t.lineTo(-.5*a+em*i,-.5*i-em*a),t.closePath()}}},ek=Math.PI/180,e_=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*ek;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},eT=function(t){var e,r=t.type,n=void 0===r?"circle":r,o=t.size,i=void 0===o?64:o,a=t.sizeType,u=void 0===a?"area":a,s=eA(eA({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,ej)),{},{type:n,size:i,sizeType:u}),l=s.className,f=s.cx,p=s.cy,d=tS(s,!0);return f===+f&&p===+p&&i===+i?c().createElement("path",eS({},d,{className:(0,N.Z)("recharts-symbols",l),transform:"translate(".concat(f,", ").concat(p,")"),d:(e=eE["symbol".concat(eu()(n))]||el,(function(t,e){let r=null,n=(0,eO.d)(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:(0,ex.Z)(t||el),e="function"==typeof e?e:(0,ex.Z)(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:(0,ex.Z)(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:(0,ex.Z)(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(e_(i,u,n))())})):null};function eM(t){return(eM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eC(){return(eC=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eI(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eN(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eN=function(){return!!t})()}function eD(t){return(eD=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eB(t,e){return(eB=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eR(t,e,r){return(e=eL(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eL(t){var e=function(t,e){if("object"!=eM(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eM(e)?e:e+""}eT.registerSymbol=function(t,e){eE["symbol".concat(eu()(t))]=e};var ez=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=eD(t),function(t,e){if(e&&("object"===eM(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,eN()?Reflect.construct(t,e||[],eD(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eB(t,e)}(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,o=t.inactive?e:t.color;if("plainline"===t.type)return c().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return c().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return c().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(c().isValidElement(t.legendIcon)){var i=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eI(Object(r),!0).forEach(function(e){eR(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eI(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete i.legendIcon,c().cloneElement(t.legendIcon,i)}return c().createElement(eT,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,o=e.layout,i=e.formatter,a=e.inactiveColor,u={x:0,y:0,width:32,height:32},s={display:"horizontal"===o?"inline-block":"block",marginRight:10},l={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var o=e.formatter||i,f=(0,N.Z)(eR(eR({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var p=P()(e.value)?null:e.value;ea(!P()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var d=e.inactive?a:e.color;return c().createElement("li",eC({className:f,style:s,key:"legend-item-".concat(r)},tl(t.props,e,r)),c().createElement(tM,{width:n,height:n,viewBox:u,style:l},t.renderIcon(e)),c().createElement("span",{className:"recharts-legend-item-text",style:{color:d}},o?o(p,e,r):p))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?c().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eL(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a.PureComponent);function eZ(t){return(eZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eR(ez,"displayName","Legend"),eR(ez,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var eq=["ref"];function eW(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eU(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eW(Object(r),!0).forEach(function(e){eV(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eW(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function eF(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eH(n.key),n)}}function eX(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eX=function(){return!!t})()}function eG(t){return(eG=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function e$(t,e){return(e$=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eV(t,e,r){return(e=eH(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eH(t){var e=function(t,e){if("object"!=eZ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eZ(e)?e:e+""}function eK(t){return t.value}var eY=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=eG(e),eV(t=function(t,e){if(e&&("object"===eZ(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,eX()?Reflect.construct(e,r||[],eG(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&e$(t,e)}(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?eU({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,s=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((s||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),eU(eU({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,o=e.height,i=e.wrapperStyle,a=e.payloadUniqBy,u=e.payload,s=eU(eU({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return c().createElement("div",{className:"recharts-legend-wrapper",style:s,ref:function(e){t.wrapperNode=e}},function(t,e){if(c().isValidElement(t))return c().cloneElement(t,e);if("function"==typeof t)return c().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,eq);return c().createElement(ez,r)}(r,eU(eU({},this.props),{},{payload:t5(u,a,eK)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=eU(eU({},this.defaultProps),t.props).layout;return"vertical"===r&&$(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&eF(n.prototype,e),r&&eF(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a.PureComponent);function eJ(){return(eJ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}eV(eY,"displayName","Legend"),eV(eY,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var eQ=function(t){var e=t.cx,r=t.cy,n=t.r,o=t.className,i=(0,N.Z)("recharts-dot",o);return e===+e&&r===+r&&n===+n?c().createElement("circle",eJ({},tS(t,!1),ts(t),{className:i,cx:e,cy:r,r:n})):null},e0=Object.getOwnPropertyNames,e1=Object.getOwnPropertySymbols,e2=Object.prototype.hasOwnProperty;function e3(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function e6(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function e5(t){return e0(t).concat(e1(t))}var e4=Object.hasOwn||function(t,e){return e2.call(t,e)};function e7(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var e9=Object.getOwnPropertyDescriptor,e8=Object.keys;function rt(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function re(t,e){return e7(t.getTime(),e.getTime())}function rr(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rn(t,e){return t===e}function ro(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var s=e.entries(),l=!1,f=0;(o=s.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,d=o.value;if(r.equals(p[0],d[0],u,f,t,e,r)&&r.equals(p[1],d[1],p[0],d[0],t,e,r)){l=a[f]=!0;break}f++}if(!l)return!1;u++}return!0}function ri(t,e,r){var n=e8(t),o=n.length;if(e8(e).length!==o)return!1;for(;o-- >0;)if(!rp(t,e,r,n[o]))return!1;return!0}function ra(t,e,r){var n,o,i,a=e5(t),c=a.length;if(e5(e).length!==c)return!1;for(;c-- >0;)if(!rp(t,e,r,n=a[c])||(o=e9(t,n),i=e9(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function rc(t,e){return e7(t.valueOf(),e.valueOf())}function ru(t,e){return t.source===e.source&&t.flags===e.flags}function rs(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),s=!1,l=0;(o=u.next())&&!o.done;){if(!a[l]&&r.equals(n.value,o.value,n.value,o.value,t,e,r)){s=a[l]=!0;break}l++}if(!s)return!1}return!0}function rl(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function rf(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rp(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||e4(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rd=Array.isArray,rh="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,ry=Object.assign,rv=Object.prototype.toString.call.bind(Object.prototype.toString),rm=rb();function rb(t){void 0===t&&(t={});var e,r,n,o,i,a,c,u,s,l,f,p,d,h=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?ra:rt,areDatesEqual:re,areErrorsEqual:rr,areFunctionsEqual:rn,areMapsEqual:n?e3(ro,ra):ro,areNumbersEqual:e7,areObjectsEqual:n?ra:ri,arePrimitiveWrappersEqual:rc,areRegExpsEqual:ru,areSetsEqual:n?e3(rs,ra):rs,areTypedArraysEqual:n?ra:rl,areUrlsEqual:rf};if(r&&(o=ry({},o,r(o))),e){var i=e6(o.areArraysEqual),a=e6(o.areMapsEqual),c=e6(o.areObjectsEqual),u=e6(o.areSetsEqual);o=ry({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,s=e.arePrimitiveWrappersEqual,l=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,d=e.areUrlsEqual,function(t,e,h){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,h):"function"===y&&i(t,e,h);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return u(t,e,h);if(rd(t))return r(t,e,h);if(null!=rh&&rh(t))return p(t,e,h);if(v===Date)return n(t,e,h);if(v===RegExp)return l(t,e,h);if(v===Map)return a(t,e,h);if(v===Set)return f(t,e,h);var m=rv(t);return"[object Date]"===m?n(t,e,h):"[object RegExp]"===m?l(t,e,h):"[object Map]"===m?a(t,e,h):"[object Set]"===m?f(t,e,h):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,h):"[object URL]"===m?d(t,e,h):"[object Error]"===m?o(t,e,h):"[object Arguments]"===m?u(t,e,h):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&s(t,e,h)}),g=y?y(b):function(t,e,r,n,o,i,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:o,meta:c.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==h&&h,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rg(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function rx(t){return(rx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function rw(t){return(rw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rj(Object(r),!0).forEach(function(e){rP(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rP(t,e,r){var n;return(n=function(t,e){if("object"!==rw(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rw(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}rb({strict:!0}),rb({circular:!0}),rb({circular:!0,strict:!0}),rb({createInternalComparator:function(){return e7}}),rb({strict:!0,createInternalComparator:function(){return e7}}),rb({circular:!0,createInternalComparator:function(){return e7}}),rb({circular:!0,createInternalComparator:function(){return e7},strict:!0});var rA=function(t){return t},rE=function(t,e){return Object.keys(e).reduce(function(r,n){return rS(rS({},r),{},rP({},n,t(n,e[n])))},{})},rk=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},r_=function(t,e,r,n,o,i,a,c){};function rT(t,e){if(t){if("string"==typeof t)return rM(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rM(t,e)}}function rM(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rC=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},rI=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},rN=function(t,e){return function(r){return rI(rC(t,e),r)}},rD=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,c=1,u=1;break;case"ease":i=.25,a=.1,c=.25,u=1;break;case"ease-in":i=.42,a=0,c=1,u=1;break;case"ease-out":i=.42,a=0,c=.58,u=1;break;case"ease-in-out":i=0,a=0,c=.58,u=1;break;default:var s=n[0].split("(");if("cubic-bezier"===s[0]&&4===s[1].split(")")[0].split(",").length){var l,f=function(t){if(Array.isArray(t))return t}(l=s[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),4!==c.length);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(l,4)||rT(l,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],c=f[2],u=f[3]}else r_(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}r_([i,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=rN(i,c),d=rN(a,u),h=(t=i,e=c,function(r){var n;return rI([].concat(function(t){if(Array.isArray(t))return rM(t)}(n=rC(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||rT(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=h(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return d(r)};return y.isStepper=!1,y},rB=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(i)?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c},rR=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rD(n);case"spring":return rB();default:if("cubic-bezier"===n.split("(")[0])return rD(n);r_(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(r_(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function rL(t){return(rL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rz(t){return function(t){if(Array.isArray(t))return rF(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||rU(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rZ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rq(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rZ(Object(r),!0).forEach(function(e){rW(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rZ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rW(t,e,r){var n;return(n=function(t,e){if("object"!==rL(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rL(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rU(t,e){if(t){if("string"==typeof t)return rF(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rF(t,e)}}function rF(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rX=function(t,e,r){return t+(e-t)*r},rG=function(t){return t.from!==t.to},r$=function t(e,r,n){var o=rE(function(t,r){if(rG(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(n,2)||rU(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return rq(rq({},r),{},{from:i,velocity:a})}return r},r);return n<1?rE(function(t,e){return rG(e)?rq(rq({},e),{},{velocity:rX(e.velocity,o[t].velocity,n),from:rX(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};let rV=function(t,e,r,n,o){var i,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return rq(rq({},r),{},rW({},n,[t[n],e[n]]))},{}),s=c.reduce(function(r,n){return rq(rq({},r),{},rW({},n,{from:t[n],velocity:0,to:e[n]}))},{}),l=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;s=r$(r,s,a),o(rq(rq(rq({},t),e),rE(function(t,e){return e.from},s))),i=n,Object.values(s).filter(rG).length&&(l=requestAnimationFrame(f))}:function(i){a||(a=i);var c=(i-a)/n,s=rE(function(t,e){return rX.apply(void 0,rz(e).concat([r(c)]))},u);if(o(rq(rq(rq({},t),e),s)),c<1)l=requestAnimationFrame(f);else{var p=rE(function(t,e){return rX.apply(void 0,rz(e).concat([r(1)]))},u);o(rq(rq(rq({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(l)}}};function rH(t){return(rH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var rK=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function rY(t){return function(t){if(Array.isArray(t))return rJ(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return rJ(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rJ(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rJ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function rQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r0(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rQ(Object(r),!0).forEach(function(e){r1(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function r1(t,e,r){return(e=r2(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function r2(t){var e=function(t,e){if("object"!==rH(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===rH(e)?e:String(e)}function r3(t,e){return(r3=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function r6(t,e){if(e&&("object"===rH(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return r5(t)}function r5(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function r4(t){return(r4=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var r7=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&r3(t,e)}(o,t);var e,r,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=r4(o);return t=e?Reflect.construct(r,arguments,r4(this).constructor):r.apply(this,arguments),r6(this,t)});function o(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o);var r,i=(r=n.call(this,t,e)).props,a=i.isActive,c=i.attributeName,u=i.from,s=i.to,l=i.steps,f=i.children,p=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(r5(r)),r.changeStyle=r.changeStyle.bind(r5(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:s}),r6(r);if(l&&l.length)r.state={style:l[0].style};else if(u){if("function"==typeof f)return r.state={style:u},r6(r);r.state={style:c?r1({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var s={style:o?r1({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(s);return}if(!rm(t.to,a)||!t.canBegin||!t.isActive){var l=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=l||i?c:t.to;if(this.state&&u){var p={style:o?r1({},o,f):f};(o&&u[o]!==f||!o&&u!==f)&&this.setState(p)}this.runAnimation(r0(r0({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,s=rV(r,n,rR(i),o,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=s()},o,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration;return this.manager.start([o].concat(rY(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,s=n.properties,l=n.onAnimationEnd,f=o>0?r[o-1]:n,p=s||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(rY(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var d=rk(p,i,c),h=r0(r0(r0({},f.style),u),{},{transition:d});return[].concat(rY(t),[h,i,l]).filter(rA)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){if(!this.manager){var e,r,n;this.manager=(e=function(){return null},r=!1,n=function t(n){if(!r){if(Array.isArray(n)){if(!n.length)return;var o=function(t){if(Array.isArray(t))return t}(n)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||function(t,e){if(t){if("string"==typeof t)return rO(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rO(t,void 0)}}(n)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){rg(t.bind(null,a),i);return}t(i),rg(t.bind(null,a));return}"object"===rx(n)&&e(n),"function"==typeof n&&n()}},{stop:function(){r=!0},start:function(t){r=!1,n(t)},subscribe:function(t){return e=t,function(){e=function(){return null}}}})}var o=t.begin,i=t.duration,a=t.attributeName,c=t.to,u=t.easing,s=t.onAnimationStart,l=t.onAnimationEnd,f=t.steps,p=t.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof u||"function"==typeof p||"spring"===u){this.runJSAnimation(t);return}if(f.length>1){this.runStepAnimation(t);return}var h=a?r1({},a,c):c,y=rk(Object.keys(h),i,u);d.start([s,o,r0(r0({},h),{},{transition:y}),i,l])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),o=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,rK)),i=a.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!n||0===i||r<=0)return e;var s=function(t){var e=t.props,r=e.style,n=e.className;return(0,a.cloneElement)(t,r0(r0({},o),{},{style:r0(r0({},void 0===r?{}:r),u),className:n}))};return 1===i?s(a.Children.only(e)):c().createElement("div",null,a.Children.map(e,function(t){return s(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,r2(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(a.PureComponent);function r9(t){return(r9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r8(){return(r8=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ne(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ne(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=r9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=r9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==r9(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ne(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}r7.displayName="Animate",r7.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},r7.propTypes={from:d().oneOfType([d().object,d().string]),to:d().oneOfType([d().object,d().string]),attributeName:d().string,duration:d().number,begin:d().number,easing:d().oneOfType([d().string,d().func]),steps:d().arrayOf(d().shape({duration:d().number.isRequired,style:d().object.isRequired,easing:d().oneOfType([d().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),d().func]),properties:d().arrayOf("string"),onAnimationEnd:d().func})),children:d().oneOfType([d().node,d().func]),isActive:d().bool,canBegin:d().bool,onAnimationEnd:d().func,shouldReAnimate:d().bool,onAnimationStart:d().func,onAnimationReStart:d().func};var nn=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,s=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var l=[0,0,0,0],f=0;f<4;f++)l[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*l[0]),l[0]>0&&(i+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+u*l[0],",").concat(e)),i+="L ".concat(t+r-u*l[1],",").concat(e),l[1]>0&&(i+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,",\n        ").concat(t+r,",").concat(e+c*l[1])),i+="L ".concat(t+r,",").concat(e+n-c*l[2]),l[2]>0&&(i+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,",\n        ").concat(t+r-u*l[2],",").concat(e+n)),i+="L ".concat(t+u*l[3],",").concat(e+n),l[3]>0&&(i+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,",\n        ").concat(t,",").concat(e+n-c*l[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},no=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;return!!(Math.abs(a)>0&&Math.abs(c)>0)&&r>=Math.min(o,o+a)&&r<=Math.max(o,o+a)&&n>=Math.min(i,i+c)&&n<=Math.max(i,i+c)},ni={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},na=function(t){var e,r=nr(nr({},ni),t),n=(0,a.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,a.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return nt(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nt(t,2)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],u=o[1];(0,a.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var s=r.x,l=r.y,f=r.width,p=r.height,d=r.radius,h=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isAnimationActive,g=r.isUpdateAnimationActive;if(s!==+s||l!==+l||f!==+f||p!==+p||0===f||0===p)return null;var x=(0,N.Z)("recharts-rectangle",h);return g?c().createElement(r7,{canBegin:i>0,from:{width:f,height:p,x:s,y:l},to:{width:f,height:p,x:s,y:l},duration:v,animationEasing:y,isActive:g},function(t){var e=t.width,o=t.height,a=t.x,u=t.y;return c().createElement(r7,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:b,easing:y},c().createElement("path",r8({},tS(r,!0),{className:x,d:nn(a,u,e,o,d),ref:n})))}):c().createElement("path",r8({},tS(r,!0),{className:x,d:nn(s,l,f,p,d)}))},nc=r(13512);function nu(t){return(nu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ns(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ns(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=nu(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nu(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nu(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ns(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var nf={widthCache:{},cacheCount:0},np={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nd="recharts_measurement_span",nh=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||t2.isSsr)return{width:0,height:0};var n=(Object.keys(e=nl({},r)).forEach(function(t){e[t]||delete e[t]}),e),o=JSON.stringify({text:t,copyStyle:n});if(nf.widthCache[o])return nf.widthCache[o];try{var i=document.getElementById(nd);i||((i=document.createElement("span")).setAttribute("id",nd),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=nl(nl({},np),n);Object.assign(i.style,a),i.textContent="".concat(t);var c=i.getBoundingClientRect(),u={width:c.width,height:c.height};return nf.widthCache[o]=u,++nf.cacheCount>2e3&&(nf.cacheCount=0,nf.widthCache={}),u}catch(t){return{width:0,height:0}}};function ny(t){return(ny="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nv(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nm(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nm(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nm(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nb(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=ny(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ny(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ny(e)?e:e+""}(n.key),n)}}var ng=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nx=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nO=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nw=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nj={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},nS=Object.keys(nj),nP=function(){var t,e;function r(t,e){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,r),this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||nO.test(e)||(this.num=NaN,this.unit=""),nS.includes(e)&&(this.num=t*nj[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=nv(null!==(e=nw.exec(t))&&void 0!==e?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&nb(r.prototype,t),e&&nb(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function nA(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=nv(null!==(r=ng.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],c=nP.parse(null!=o?o:""),u=nP.parse(null!=a?a:""),s="*"===i?c.multiply(u):c.divide(u);if(s.isNaN())return"NaN";e=e.replace(ng,s.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var l,f=nv(null!==(l=nx.exec(e))&&void 0!==l?l:[],4),p=f[1],d=f[2],h=f[3],y=nP.parse(null!=p?p:""),v=nP.parse(null!=h?h:""),m="+"===d?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(nx,m.toString())}return e}var nE=/\(([^()]*)\)/;function nk(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=nv(nE.exec(e),2)[1];e=e.replace(nE,nA(r))}return e}(e),e=nA(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var n_=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],nT=["dx","dy","angle","className","breakAll"];function nM(){return(nM=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nC(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function nI(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nN(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nN(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nN(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nD=/[ \f\n\r\t\v\u2028\u2029]+/,nB=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];j()(e)||(o=r?e.toString().split(""):e.toString().split(nD));var i=o.map(function(t){return{word:t,width:nh(t,n).width}}),a=r?0:nh("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(t){return null}},nR=function(t,e,r,n,o){var i,a=t.maxLines,c=t.children,u=t.style,s=t.breakAll,l=$(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||o||c.width+a+r<Number(n))?(c.words.push(i),c.width+=a+r):t.push({words:[i],width:a}),t},[])},p=f(e);if(!l)return p;for(var d=function(t){var e=f(nB({breakAll:s,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},h=0,y=c.length-1,v=0;h<=y&&v<=c.length-1;){var m=Math.floor((h+y)/2),b=nI(d(m-1),2),g=b[0],x=b[1],O=nI(d(m),1)[0];if(g||O||(h=m+1),g&&O&&(y=m-1),!g&&O){i=x;break}v++}return i||p},nL=function(t){return[{words:j()(t)?[]:t.toString().split(nD)}]},nz=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!t2.isSsr){var c=nB({breakAll:i,children:n,style:o});return c?nR({breakAll:i,children:n,maxLines:a,style:o},c.wordsWithComputedWidth,c.spaceWidth,e,r):nL(n)}return nL(n)},nZ="#808080",nq=function(t){var e,r=t.x,n=void 0===r?0:r,o=t.y,i=void 0===o?0:o,u=t.lineHeight,s=void 0===u?"1em":u,l=t.capHeight,f=void 0===l?"0.71em":l,p=t.scaleToFit,d=void 0!==p&&p,h=t.textAnchor,y=t.verticalAnchor,v=t.fill,m=void 0===v?nZ:v,b=nC(t,n_),g=(0,a.useMemo)(function(){return nz({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:d,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,d,b.style,b.width]),x=b.dx,O=b.dy,w=b.angle,j=b.className,S=b.breakAll,P=nC(b,nT);if(!V(n)||!V(i))return null;var A=n+($(x)?x:0),E=i+($(O)?O:0);switch(void 0===y?"end":y){case"start":e=nk("calc(".concat(f,")"));break;case"middle":e=nk("calc(".concat((g.length-1)/2," * -").concat(s," + (").concat(f," / 2))"));break;default:e=nk("calc(".concat(g.length-1," * -").concat(s,")"))}var k=[];if(d){var _=g[0].width,T=b.width;k.push("scale(".concat(($(T)?T/_:1)/_,")"))}return w&&k.push("rotate(".concat(w,", ").concat(A,", ").concat(E,")")),k.length&&(P.transform=k.join(" ")),c().createElement("text",nM({},tS(P,!0),{x:A,y:E,className:(0,N.Z)("recharts-text",j),textAnchor:void 0===h?"start":h,fill:m.includes("url")?nZ:m}),g.map(function(t,r){var n=t.words.join(S?"":" ");return c().createElement("tspan",{x:A,dy:0===r?e:s,key:"".concat(n,"-").concat(r)},n)}))},nW=r(15757),nU=r(43143),nF=r(2007),nX=r(29184),nG=r(15528),n$=r(97530),nV=r(97499),nH=r(17693);function nK(t){return Math.sign(t)*t*t}function nY(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function nJ(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}var nQ=r(11081);function n0(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}function n1(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}var n2=r(88192),n3=r(25734),n6=r(27817),n5=r(90261),n4=r(69428),n7=r(41472),n9=r(59595),n8=r(11552);function ot(){var t,e,r,n,o,i,a,c=0,u=.5,s=1,l=1,f=nV.yR,p=!1;function d(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(l*t<l*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function h(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=n9.Z);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),d):[f(0),f(.5),f(1)]}}return d.domain=function(a){return arguments.length?([c,u,s]=a,t=i(c=+c),e=i(u=+u),r=i(s=+s),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),l=e<t?-1:1,d):[c,u,s]},d.clamp=function(t){return arguments.length?(p=!!t,d):p},d.interpolator=function(t){return arguments.length?(f=t,d):f},d.range=h(n9.Z),d.rangeRound=h(n8.Z),d.unknown=function(t){return arguments.length?(a=t,d):a},function(a){return i=a,t=a(c),e=a(u),r=a(s),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),l=e<t?-1:1,d}}function oe(){var t=(0,n$.Hh)(ot());return t.copy=function(){return(0,n7.JG)(t,oe()).exponent(t.exponent())},nH.O.apply(t,arguments)}function or(){return oe.apply(null,arguments).exponent(.5)}var on,oo,oi=r(97129),oa=r(40435),oc=r(55148),ou=r(42903),os=r(6950),ol=r(11829),of=r(14474),op=r(31955),od=r.n(op),oh=r(52692),oy=r.n(oh),ov=r(78352),om=r.n(ov),ob=r(81711),og=r.n(ob),ox=!0,oO="[DecimalError] ",ow=oO+"Invalid argument: ",oj=oO+"Exponent out of range: ",oS=Math.floor,oP=Math.pow,oA=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,oE=oS(1286742750677284.5),ok={};function o_(t,e){var r,n,o,i,a,c,u,s,l=t.constructor,f=l.precision;if(!t.s||!e.s)return e.s||(e=new l(t)),ox?oz(e,f):e;if(u=t.d,s=e.d,a=t.e,o=e.e,u=u.slice(),i=a-o){for(i<0?(n=u,i=-i,c=s.length):(n=s,o=a,c=u.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=u.length)-(i=s.length)<0&&(i=c,n=s,s=u,u=n),r=0;i;)r=(u[--i]=u[i]+s[i]+r)/1e7|0,u[i]%=1e7;for(r&&(u.unshift(r),++o),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=o,ox?oz(e,f):e}function oT(t,e,r){if(t!==~~t||t<e||t>r)throw Error(ow+t)}function oM(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=oB(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=oB(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}ok.absoluteValue=ok.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},ok.comparedTo=ok.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},ok.decimalPlaces=ok.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},ok.dividedBy=ok.div=function(t){return oC(this,new this.constructor(t))},ok.dividedToIntegerBy=ok.idiv=function(t){var e=this.constructor;return oz(oC(this,new e(t),0,1),e.precision)},ok.equals=ok.eq=function(t){return!this.cmp(t)},ok.exponent=function(){return oN(this)},ok.greaterThan=ok.gt=function(t){return this.cmp(t)>0},ok.greaterThanOrEqualTo=ok.gte=function(t){return this.cmp(t)>=0},ok.isInteger=ok.isint=function(){return this.e>this.d.length-2},ok.isNegative=ok.isneg=function(){return this.s<0},ok.isPositive=ok.ispos=function(){return this.s>0},ok.isZero=function(){return 0===this.s},ok.lessThan=ok.lt=function(t){return 0>this.cmp(t)},ok.lessThanOrEqualTo=ok.lte=function(t){return 1>this.cmp(t)},ok.logarithm=ok.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(oo))throw Error(oO+"NaN");if(this.s<1)throw Error(oO+(this.s?"NaN":"-Infinity"));return this.eq(oo)?new r(0):(ox=!1,e=oC(oR(this,o),oR(t,o),o),ox=!0,oz(e,n))},ok.minus=ok.sub=function(t){return t=new this.constructor(t),this.s==t.s?oZ(this,t):o_(this,(t.s=-t.s,t))},ok.modulo=ok.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(oO+"NaN");return this.s?(ox=!1,e=oC(this,t,0,1).times(t),ox=!0,this.minus(e)):oz(new r(this),n)},ok.naturalExponential=ok.exp=function(){return oI(this)},ok.naturalLogarithm=ok.ln=function(){return oR(this)},ok.negated=ok.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},ok.plus=ok.add=function(t){return t=new this.constructor(t),this.s==t.s?o_(this,t):oZ(this,(t.s=-t.s,t))},ok.precision=ok.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(ow+t);if(e=oN(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},ok.squareRoot=ok.sqrt=function(){var t,e,r,n,o,i,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(oO+"NaN")}for(t=oN(this),ox=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=oM(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=oS((t+1)/2)-(t<0||t%2),n=new c(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(oC(this,i,a+2)).times(.5),oM(i.d).slice(0,a)===(e=oM(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(oz(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return ox=!0,oz(n,r)},ok.times=ok.mul=function(t){var e,r,n,o,i,a,c,u,s,l=this.constructor,f=this.d,p=(t=new l(t)).d;if(!this.s||!t.s)return new l(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(s=p.length)&&(i=f,f=p,p=i,a=u,u=s,s=a),i=[],n=a=u+s;n--;)i.push(0);for(n=s;--n>=0;){for(e=0,o=u+n;o>n;)c=i[o]+p[n]*f[o-n-1]+e,i[o--]=c%1e7|0,e=c/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,ox?oz(t,l.precision):t},ok.toDecimalPlaces=ok.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(oT(t,0,1e9),void 0===e?e=n.rounding:oT(e,0,8),oz(r,t+oN(r)+1,e))},ok.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=oq(n,!0):(oT(t,0,1e9),void 0===e?e=o.rounding:oT(e,0,8),r=oq(n=oz(new o(n),t+1,e),!0,t+1)),r},ok.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?oq(this):(oT(t,0,1e9),void 0===e?e=o.rounding:oT(e,0,8),r=oq((n=oz(new o(this),t+oN(this)+1,e)).abs(),!1,t+oN(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},ok.toInteger=ok.toint=function(){var t=this.constructor;return oz(new t(this),oN(this)+1,t.rounding)},ok.toNumber=function(){return+this},ok.toPower=ok.pow=function(t){var e,r,n,o,i,a,c=this,u=c.constructor,s=+(t=new u(t));if(!t.s)return new u(oo);if(!(c=new u(c)).s){if(t.s<1)throw Error(oO+"Infinity");return c}if(c.eq(oo))return c;if(n=u.precision,t.eq(oo))return oz(c,n);if(a=(e=t.e)>=(r=t.d.length-1),i=c.s,a){if((r=s<0?-s:s)<=9007199254740991){for(o=new u(oo),e=Math.ceil(n/7+4),ox=!1;r%2&&oW((o=o.times(c)).d,e),0!==(r=oS(r/2));)oW((c=c.times(c)).d,e);return ox=!0,t.s<0?new u(oo).div(o):oz(o,n)}}else if(i<0)throw Error(oO+"NaN");return i=i<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,ox=!1,o=t.times(oR(c,n+12)),ox=!0,(o=oI(o)).s=i,o},ok.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=oN(o),n=oq(o,r<=i.toExpNeg||r>=i.toExpPos)):(oT(t,1,1e9),void 0===e?e=i.rounding:oT(e,0,8),r=oN(o=oz(new i(o),t,e)),n=oq(o,t<=r||r<=i.toExpNeg,t)),n},ok.toSignificantDigits=ok.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(oT(t,1,1e9),void 0===e?e=r.rounding:oT(e,0,8)),oz(new r(this),t,e)},ok.toString=ok.valueOf=ok.val=ok.toJSON=ok[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=oN(this),e=this.constructor;return oq(this,t<=e.toExpNeg||t>=e.toExpPos)};var oC=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,u,s,l,f,p,d,h,y,v,m,b,g,x,O,w,j,S,P=n.constructor,A=n.s==o.s?1:-1,E=n.d,k=o.d;if(!n.s)return new P(n);if(!o.s)throw Error(oO+"Division by zero");for(s=0,u=n.e-o.e,j=k.length,O=E.length,h=(d=new P(A)).d=[];k[s]==(E[s]||0);)++s;if(k[s]>(E[s]||0)&&--u,(b=null==i?i=P.precision:a?i+(oN(n)-oN(o))+1:i)<0)return new P(0);if(b=b/7+2|0,s=0,1==j)for(l=0,k=k[0],b++;(s<O||l)&&b--;s++)g=1e7*l+(E[s]||0),h[s]=g/k|0,l=g%k|0;else{for((l=1e7/(k[0]+1)|0)>1&&(k=t(k,l),E=t(E,l),j=k.length,O=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(S=k.slice()).unshift(0),w=k[0],k[1]>=1e7/2&&++w;do l=0,(c=e(k,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(l=m/w|0)>1?(l>=1e7&&(l=1e7-1),p=(f=t(k,l)).length,v=y.length,1==(c=e(f,y,p,v))&&(l--,r(f,j<p?S:k,p))):(0==l&&(c=l=1),f=k.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(k,y,j,v))<1&&(l++,r(y,j<v?S:k,v))),v=y.length):0===c&&(l++,y=[0]),h[s++]=l,c&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1);while((x++<O||void 0!==y[0])&&b--)}return h[0]||h.shift(),d.e=u,oz(d,a?i+oN(d)+1:i)}}();function oI(t,e){var r,n,o,i,a,c=0,u=0,s=t.constructor,l=s.precision;if(oN(t)>16)throw Error(oj+oN(t));if(!t.s)return new s(oo);for(null==e?(ox=!1,a=l):a=e,i=new s(.03125);t.abs().gte(.1);)t=t.times(i),u+=5;for(a+=Math.log(oP(2,u))/Math.LN10*2+5|0,r=n=o=new s(oo),s.precision=a;;){if(n=oz(n.times(t),a),r=r.times(++c),oM((i=o.plus(oC(n,r,a))).d).slice(0,a)===oM(o.d).slice(0,a)){for(;u--;)o=oz(o.times(o),a);return s.precision=l,null==e?(ox=!0,oz(o,l)):o}o=i}}function oN(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function oD(t,e,r){if(e>t.LN10.sd())throw ox=!0,r&&(t.precision=r),Error(oO+"LN10 precision limit exceeded");return oz(new t(t.LN10),e)}function oB(t){for(var e="";t--;)e+="0";return e}function oR(t,e){var r,n,o,i,a,c,u,s,l,f=1,p=t,d=p.d,h=p.constructor,y=h.precision;if(p.s<1)throw Error(oO+(p.s?"NaN":"-Infinity"));if(p.eq(oo))return new h(0);if(null==e?(ox=!1,s=y):s=e,p.eq(10))return null==e&&(ox=!0),oD(h,s);if(s+=10,h.precision=s,n=(r=oM(d)).charAt(0),!(15e14>Math.abs(i=oN(p))))return u=oD(h,s+2,y).times(i+""),p=oR(new h(n+"."+r.slice(1)),s-10).plus(u),h.precision=y,null==e?(ox=!0,oz(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=oM((p=p.times(t)).d)).charAt(0),f++;for(i=oN(p),n>1?(p=new h("0."+r),i++):p=new h(n+"."+r.slice(1)),c=a=p=oC(p.minus(oo),p.plus(oo),s),l=oz(p.times(p),s),o=3;;){if(a=oz(a.times(l),s),oM((u=c.plus(oC(a,new h(o),s))).d).slice(0,s)===oM(c.d).slice(0,s))return c=c.times(2),0!==i&&(c=c.plus(oD(h,s+2,y).times(i+""))),c=oC(c,new h(f),s),h.precision=y,null==e?(ox=!0,oz(c,y)):c;c=u,o+=2}}function oL(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,r=r-n-1,t.e=oS(r/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),ox&&(t.e>oE||t.e<-oE))throw Error(oj+r)}else t.s=0,t.e=0,t.d=[0];return t}function oz(t,e,r){var n,o,i,a,c,u,s,l,f=t.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,s=f[l=0];else{if((l=Math.ceil((n+1)/7))>=(i=f.length))return t;for(a=1,s=i=f[l];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=s/(i=oP(10,a-o-1))%10|0,u=e<0||void 0!==f[l+1]||s%i,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?o>0?s/oP(10,a-o):0:f[l-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(i=oN(t),f.length=1,e=e-i-1,f[0]=oP(10,(7-e%7)%7),t.e=oS(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=l,i=1,l--):(f.length=l+1,i=oP(10,7-n),f[l]=o>0?(s/oP(10,a-o)%oP(10,o)|0)*i:0),u)for(;;){if(0==l){1e7==(f[0]+=i)&&(f[0]=1,++t.e);break}if(f[l]+=i,1e7!=f[l])break;f[l--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(ox&&(t.e>oE||t.e<-oE))throw Error(oj+oN(t));return t}function oZ(t,e){var r,n,o,i,a,c,u,s,l,f,p=t.constructor,d=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),ox?oz(e,d):e;if(u=t.d,f=e.d,n=e.e,s=t.e,u=u.slice(),a=s-n){for((l=a<0)?(r=u,a=-a,c=f.length):(r=f,n=s,c=u.length),a>(o=Math.max(Math.ceil(d/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((l=(o=u.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(u[o]!=f[o]){l=u[o]<f[o];break}a=0}for(l&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,o=f.length-c;o>0;--o)u[c++]=0;for(o=f.length;o>a;){if(u[--o]<f[o]){for(i=o;i&&0===u[--i];)u[i]=1e7-1;--u[i],u[o]+=1e7}u[o]-=f[o]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,ox?oz(e,d):e):new p(0)}function oq(t,e,r){var n,o=oN(t),i=oM(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+oB(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+oB(-o-1)+i,r&&(n=r-a)>0&&(i+=oB(n))):o>=a?(i+=oB(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+oB(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=oB(n))),t.s<0?"-"+i:i}function oW(t,e){if(t.length>e)return t.length=e,!0}function oU(t){if(!t||"object"!=typeof t)throw Error(oO+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(oS(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(ow+r+": "+n)}if(void 0!==(n=t[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(ow+r+": "+n)}return this}var on=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(ow+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return oL(this,t.toString())}if("string"!=typeof t)throw Error(ow+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,oA.test(t))oL(this,t);else throw Error(ow+t)}if(i.prototype=ok,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=oU,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});oo=new on(1);let oF=on;function oX(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var oG=function(t){return t},o$={},oV=function(t){return t===o$},oH=function(t){return function e(){return 0==arguments.length||1==arguments.length&&oV(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},oK=function(t){return function t(e,r){return 1===e?r:oH(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==o$}).length;return a>=e?r.apply(void 0,o):t(e-a,oH(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return oV(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return oX(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return oX(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return oX(t,void 0)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},oY=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},oJ=oK(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),oQ=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return oG;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},o0=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},o1=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};oK(function(t,e,r){var n=+t;return n+r*(+e-n)}),oK(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),oK(function(t,e,r){var n=e-+t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let o2={rangeStep:function(t,e,r){for(var n=new oF(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new oF(t).abs().log(10).toNumber())+1}};function o3(t){return function(t){if(Array.isArray(t))return o4(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||o5(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o6(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}}(t,e)||o5(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o5(t,e){if(t){if("string"==typeof t)return o4(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return o4(t,e)}}function o4(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function o7(t){var e=o6(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function o9(t,e,r){if(t.lte(0))return new oF(0);var n=o2.getDigitCount(t.toNumber()),o=new oF(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new oF(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new oF(Math.ceil(c))}function o8(t,e,r){var n=1,o=new oF(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new oF(10).pow(o2.getDigitCount(t)-1),o=new oF(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new oF(Math.floor(t)))}else 0===t?o=new oF(Math.floor((e-1)/2)):r||(o=new oF(Math.floor(t)));var a=Math.floor((e-1)/2);return oQ(oJ(function(t){return o.add(new oF(t-a).mul(n)).toNumber()}),oY)(0,e)}var it=o1(function(t){var e=o6(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=o6(o7([r,n]),2),u=c[0],s=c[1];if(u===-1/0||s===1/0){var l=s===1/0?[u].concat(o3(oY(0,o-1).map(function(){return 1/0}))):[].concat(o3(oY(0,o-1).map(function(){return-1/0})),[s]);return r>n?o0(l):l}if(u===s)return o8(u,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new oF(0),tickMin:new oF(0),tickMax:new oF(0)};var c=o9(new oF(r).sub(e).div(n-1),o,a),u=Math.ceil((i=e<=0&&r>=0?new oF(0):(i=new oF(e).add(r).div(2)).sub(new oF(i).mod(c))).sub(e).div(c).toNumber()),s=Math.ceil(new oF(r).sub(i).div(c).toNumber()),l=u+s+1;return l>n?t(e,r,n,o,a+1):(l<n&&(s=r>0?s+(n-l):s,u=r>0?u:u+(n-l)),{step:c,tickMin:i.sub(new oF(u).mul(c)),tickMax:i.add(new oF(s).mul(c))})}(u,s,a,i),p=f.step,d=f.tickMin,h=f.tickMax,y=o2.rangeStep(d,h.add(new oF(.1).mul(p)),p);return r>n?o0(y):y});o1(function(t){var e=o6(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=o6(o7([r,n]),2),u=c[0],s=c[1];if(u===-1/0||s===1/0)return[r,n];if(u===s)return o8(u,o,i);var l=o9(new oF(s).sub(u).div(a-1),i,0),f=oQ(oJ(function(t){return new oF(u).add(new oF(t).mul(l)).toNumber()}),oY)(0,a).filter(function(t){return t>=u&&t<=s});return r>n?o0(f):f});var ie=o1(function(t,e){var r=o6(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=o6(o7([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var s=o9(new oF(u).sub(c).div(Math.max(e,2)-1),i,0),l=[].concat(o3(o2.rangeStep(new oF(c),new oF(u).sub(new oF(.99).mul(s)),s)),[u]);return n>o?o0(l):l}),ir=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function io(t){return(io="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ii(){return(ii=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ia(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ic(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ic=function(){return!!t})()}function iu(t){return(iu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function is(t,e){return(is=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function il(t,e,r){return(e=ip(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ip(t){var e=function(t,e){if("object"!=io(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=io(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==io(e)?e:e+""}var id=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=iu(t),function(t,e){if(e&&("object"===io(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ic()?Reflect.construct(t,e||[],iu(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&is(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,o=t.dataKey,i=t.data,a=t.dataPointFormatter,u=t.xAxis,s=t.yAxis,l=tS(function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,ir),!1);"x"===this.props.direction&&"number"!==u.type&&D(!1);var f=i.map(function(t){var i,f,p=a(t,o),d=p.x,h=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return ia(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ia(t,2)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=b[0],f=b[1]}else i=f=v;if("vertical"===r){var g=u.scale,x=h+e,O=x+n,w=x-n,j=g(y-i),S=g(y+f);m.push({x1:S,y1:O,x2:S,y2:w}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:O,x2:j,y2:w})}else if("horizontal"===r){var P=s.scale,A=d+e,E=A-n,k=A+n,_=P(y-i),T=P(y+f);m.push({x1:E,y1:T,x2:k,y2:T}),m.push({x1:A,y1:_,x2:A,y2:T}),m.push({x1:E,y1:_,x2:k,y2:_})}return c().createElement(tN,ii({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},l),m.map(function(t){return c().createElement("line",ii({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return c().createElement(tN,{className:"recharts-errorBars"},f)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ip(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(c().Component);function ih(t){return(ih="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function iy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function iv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?iy(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=ih(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ih(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ih(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):iy(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}il(id,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),il(id,"displayName","ErrorBar");var im=function(t){var e,r=t.children,n=t.formattedGraphicalItems,o=t.legendWidth,i=t.legendContent,a=tx(r,eY);if(!a)return null;var c=eY.defaultProps,u=void 0!==c?iv(iv({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?iv(iv({},r),e.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:u.iconType||a||"square",color:iE(e),value:i||o,payload:n}}),iv(iv(iv({},u),eY.getWithHeight(a,o)),{},{payload:e,item:a})};function ib(t){return(ib="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ig(t){return function(t){if(Array.isArray(t))return ix(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return ix(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ix(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ix(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function iO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function iw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?iO(Object(r),!0).forEach(function(e){ij(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):iO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ij(t,e,r){var n;return(n=function(t,e){if("object"!=ib(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ib(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==ib(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function iS(t,e,r){return j()(t)||j()(e)?r:V(e)?_()(t,e,r):P()(e)?e(t):r}function iP(t,e,r,n){var o=om()(t,function(t){return iS(t,e)});if("number"===r){var i=o.filter(function(t){return $(t)||parseFloat(t)});return i.length?[oy()(i),od()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!j()(t)}):o).map(function(t){return V(t)||t instanceof Date?t:""})}var iA=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var c=o.range,u=0;u<a;u++){var s=u>0?n[u-1].coordinate:n[a-1].coordinate,l=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(X(l-s)!==X(f-l)){var d=[];if(X(f-l)===X(c[1]-c[0])){p=f;var h=l+c[1]-c[0];d[0]=Math.min(h,(h+s)/2),d[1]=Math.max(h,(h+s)/2)}else{p=s;var y=f+c[1]-c[0];d[0]=Math.min(l,(y+l)/2),d[1]=Math.max(l,(y+l)/2)}var v=[Math.min(l,(p+l)/2),Math.max(l,(p+l)/2)];if(t>v[0]&&t<=v[1]||t>=d[0]&&t<=d[1]){i=n[u].index;break}}else{var m=Math.min(s,f),b=Math.max(s,f);if(t>(m+l)/2&&t<=(b+l)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},iE=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?iw(iw({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},ik=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var s=o[a[c]].stackGroups,l=Object.keys(s),f=0,p=l.length;f<p;f++){var d=s[l[f]],h=d.items,y=d.cateAxisId,v=h.filter(function(t){return ty(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?iw(iw({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var O=j()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:j()(O)?void 0:Y(O,r,0)})}}return i},i_=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,c=t.maxBarSize,u=a.length;if(u<1)return null;var s=Y(r,o,0,!0),l=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/u,d=a.reduce(function(t,e){return t+e.barSize||0},0);(d+=(u-1)*s)>=o&&(d-=(u-1)*s,s=0),d>=o&&p>0&&(f=!0,p*=.9,d=u*p);var h={offset:((o-d)/2>>0)-s,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:h.offset+h.size+s,size:f?p:e.barSize}},n=[].concat(ig(t),[r]);return h=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:h})}),n},l)}else{var y=Y(n,o,0,!0);o-2*y-(u-1)*s<=0&&(s=0);var v=(o-2*y-(u-1)*s)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(ig(t),[{item:e.item,position:{offset:y+(v+s)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},l)}return e},iT=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=im({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(c){var u=n||{},s=u.width,l=u.height,f=c.align,p=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===p)&&"center"!==f&&$(t[f]))return iw(iw({},t),{},ij({},f,t[f]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===f)&&"middle"!==p&&$(t[p]))return iw(iw({},t),{},ij({},p,t[p]+(l||0)))}return t},iM=function(t,e,r,n,o){var i=tg(e.props.children,id).filter(function(t){var e;return e=t.props.direction,!!j()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(i&&i.length){var a=i.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=iS(e,r);if(j()(n))return t;var o=Array.isArray(n)?[oy()(n),od()(n)]:[n,n],i=a.reduce(function(t,r){var n=iS(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},iC=function(t,e,r,n,o){var i=e.map(function(e){return iM(t,e,r,o,n)}).filter(function(t){return!j()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},iI=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&iM(t,e,i,n)||iP(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},iN=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},iD=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*X(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!W()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}})},iB=new WeakMap,iR=function(t,e){if("function"!=typeof e)return t;iB.has(t)||iB.set(t,new WeakMap);var r=iB.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},iL=function(t,e,r){var o=t.scale,i=t.type,a=t.layout,c=t.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:nc.Z(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:nW.Z(),realScaleType:"linear"}:"category"===i&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:nc.x(),realScaleType:"point"}:"category"===i?{scale:nc.Z(),realScaleType:"band"}:{scale:nW.Z(),realScaleType:"linear"};if(R()(o)){var u="scale".concat(eu()(o));return{scale:(n[u]||nc.x)(),realScaleType:n[u]?u:"point"}}return P()(o)?{scale:o}:{scale:nc.x(),realScaleType:"point"}},iz=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}},iZ=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},iq=function(t,e){if(!e||2!==e.length||!$(e[0])||!$(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!$(t[0])||t[0]<r)&&(o[0]=r),(!$(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},iW={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=W()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:oa.Z,none:oc.Z,silhouette:ou.Z,wiggle:os.Z,positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=W()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},iU=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=iW[r];return(0,ol.Z)().keys(n).value(function(t,e){return+iS(t,e,0)}).order(of.Z).offset(o)(t)},iF=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?iw(iw({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(V(a)){var s=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};s.items.push(e),u.hasStack=!0,u.stackGroups[a]=s}else u.stackGroups[K("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return iw(iw({},t),{},ij({},c,u))},{});return Object.keys(a).reduce(function(e,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,i){var a=c.stackGroups[i];return iw(iw({},e),{},ij({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:iU(t,a.items,o)}))},{})),iw(iw({},e),{},ij({},i,c))},{})},iX=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var s=it(u,o,a);return t.domain([oy()(s),od()(s)]),{niceTicks:s}}return o&&"number"===n?{niceTicks:ie(t.domain(),o,a)}:null},iG=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=iS(i,e.dataKey,e.domain[a]);return j()(c)?null:e.scale(c)-o/2+n},i$=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},iV=function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?iw(iw({},t.type.defaultProps),t.props):t.props).stackId;if(V(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},iH=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[oy()(e.concat([t[0]]).filter($)),od()(e.concat([t[1]]).filter($))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},iK=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,iY=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,iJ=function(t,e,r){if(P()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if($(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(iK.test(t[0])){var o=+iK.exec(t[0])[1];n[0]=e[0]-o}else P()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if($(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(iY.test(t[1])){var i=+iY.exec(t[1])[1];n[1]=e[1]+i}else P()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},iQ=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=M()(e,function(t){return t.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],s=o[a-1];i=Math.min((u.coordinate||0)-(s.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},i0=function(t,e,r){return!t||!t.length||og()(t,_()(r,"type.defaultProps.domain"))?e:t},i1=function(t,e){var r=t.type.defaultProps?iw(iw({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,s=r.hide;return iw(iw({},tS(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:iE(t),value:iS(e,n),type:c,payload:e,chartType:u,hide:s})};function i2(t){return(i2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i3(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function i6(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i3(Object(r),!0).forEach(function(e){i5(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i3(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function i5(t,e,r){var n;return(n=function(t,e){if("object"!=i2(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==i2(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var i4=["Webkit","Moz","O","ms"],i7=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=i4.reduce(function(t,n){return i6(i6({},t),{},i5({},n+r,e))},{});return n[t]=e,n};function i9(t){return(i9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i8(){return(i8=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function at(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ae(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?at(Object(r),!0).forEach(function(e){aa(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):at(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ar(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ac(n.key),n)}}function an(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(an=function(){return!!t})()}function ao(t){return(ao=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ai(t,e){return(ai=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function aa(t,e,r){return(e=ac(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ac(t){var e=function(t,e){if("object"!=i9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==i9(e)?e:e+""}var au=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=(0,nc.x)().domain(E()(0,c)).range([o,o+i-a]),s=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:s}},as=function(t){return t.changedTouches&&!!t.changedTouches.length},al=function(t){var e,r;function n(t){var e,r,o;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=ao(r),aa(e=function(t,e){if(e&&("object"===i9(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,an()?Reflect.construct(r,o||[],ao(this).constructor):r.apply(this,o)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),aa(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),aa(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),aa(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),aa(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),aa(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),aa(e,"handleSlideDragStart",function(t){var r=as(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ai(t,e)}(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=n.getIndexInRange(o,Math.min(e,r)),s=n.getIndexInRange(o,Math.max(e,r));return{startIndex:u-u%a,endIndex:s===c?c:s-s%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=iS(r[t],o,t);return P()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,s=i.startIndex,l=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var d=this.getIndex({startX:n+p,endX:o+p});(d.startIndex!==s||d.endIndex!==l)&&f&&f(d),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=as(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,s=c.width,l=c.travellerWidth,f=c.onChange,p=c.gap,d=c.data,h={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+s-l-a):y<0&&(y=Math.max(y,u-a)),h[n]=a+y;var v=this.getIndex(h),m=v.startIndex,b=v.endIndex,g=function(){var t=d.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||o<i&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||o>i&&b===t};this.setState(aa(aa({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var s=u+t;if(-1!==s&&!(s>=o.length)){var l=o[s];"startX"===e&&l>=a||"endX"===e&&l<=i||this.setState(aa({},e,l),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.fill,a=t.stroke;return c().createElement("rect",{stroke:a,fill:i,x:e,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.data,u=t.children,s=t.padding,l=a.Children.only(u);return l?c().cloneElement(l,{x:e,y:r,width:n,height:o,margin:s,compact:!0,data:i}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,o,i=this,a=this.props,u=a.y,s=a.travellerWidth,l=a.height,f=a.traveller,p=a.ariaLabel,d=a.data,h=a.startIndex,y=a.endIndex,v=Math.max(t,this.props.x),m=ae(ae({},tS(this.props,!1)),{},{x:v,y:u,width:s,height:l}),b=p||"Min value: ".concat(null===(r=d[h])||void 0===r?void 0:r.name,", Max value: ").concat(null===(o=d[y])||void 0===o?void 0:o.name);return c().createElement(tN,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,o=r.height,i=r.stroke,a=r.travellerWidth;return c().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:Math.min(t,e)+a,y:n,width:Math.max(Math.abs(e-t)-a,0),height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,o=t.height,i=t.travellerWidth,a=t.stroke,u=this.state,s=u.startX,l=u.endX,f={pointerEvents:"none",fill:a};return c().createElement(tN,{className:"recharts-brush-texts"},c().createElement(nq,i8({textAnchor:"end",verticalAnchor:"middle",x:Math.min(s,l)-5,y:n+o/2},f),this.getTextOfTick(e)),c().createElement(nq,i8({textAnchor:"start",verticalAnchor:"middle",x:Math.max(s,l)+i+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,o=t.x,i=t.y,a=t.width,u=t.height,s=t.alwaysShowText,l=this.state,f=l.startX,p=l.endX,d=l.isTextActive,h=l.isSlideMoving,y=l.isTravellerMoving,v=l.isTravellerFocused;if(!e||!e.length||!$(o)||!$(i)||!$(a)||!$(u)||a<=0||u<=0)return null;var m=(0,N.Z)("recharts-brush",r),b=1===c().Children.count(n),g=i7("userSelect","none");return c().createElement(tN,{className:m,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),b&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(d||h||y||v||s)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,o=t.height,i=t.stroke,a=Math.floor(r+o/2)-1;return c().createElement(c().Fragment,null,c().createElement("rect",{x:e,y:r,width:n,height:o,fill:i,stroke:"none"}),c().createElement("line",{x1:e+1,y1:a,x2:e+n-1,y2:a,fill:"none",stroke:"#fff"}),c().createElement("line",{x1:e+1,y1:a+2,x2:e+n-1,y2:a+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){return c().isValidElement(t)?c().cloneElement(t,e):P()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return ae({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?au({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var s=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:s}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&ar(n.prototype,e),r&&ar(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a.PureComponent);function af(t){return(af="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ap(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ad(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ap(Object(r),!0).forEach(function(e){(function(t,e,r){var n;(n=function(t,e){if("object"!=af(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=af(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==af(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r})(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ap(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}aa(al,"displayName","Brush"),aa(al,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var ah=Math.PI/180,ay=function(t,e,r,n){return{x:t+Math.cos(-ah*n)*r,y:e+Math.sin(-ah*n)*r}},av=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},am=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=av({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=Math.acos((r-o)/a);return n>i&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},ab=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},ag=function(t,e){var r,n=am({x:t.x,y:t.y},e),o=n.radius,i=n.angle,a=e.innerRadius,c=e.outerRadius;if(o<a||o>c)return!1;if(0===o)return!0;var u=ab(e),s=u.startAngle,l=u.endAngle,f=i;if(s<=l){for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}else{for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}return r?ad(ad({},e),{},{radius:o,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null};function ax(t){return(ax="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var aO=["offset"];function aw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function aj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function aS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?aj(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=ax(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ax(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ax(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):aj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function aP(){return(aP=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var aA=function(t){var e=t.value,r=t.formatter,n=j()(t.children)?e:t.children;return P()(r)?r(n):n},aE=function(t,e,r){var n,o,i=t.position,a=t.viewBox,u=t.offset,s=t.className,l=a.cx,f=a.cy,p=a.innerRadius,d=a.outerRadius,h=a.startAngle,y=a.endAngle,v=a.clockWise,m=(p+d)/2,b=X(y-h)*Math.min(Math.abs(y-h),360),g=b>=0?1:-1;"insideStart"===i?(n=h+g*u,o=v):"insideEnd"===i?(n=y-g*u,o=!v):"end"===i&&(n=y+g*u,o=v),o=b<=0?o:!o;var x=ay(l,f,m,n),O=ay(l,f,m,n+(o?1:-1)*359),w="M".concat(x.x,",").concat(x.y,"\n    A").concat(m,",").concat(m,",0,1,").concat(o?0:1,",\n    ").concat(O.x,",").concat(O.y),S=j()(t.id)?K("recharts-radial-line-"):t.id;return c().createElement("text",aP({},r,{dominantBaseline:"central",className:(0,N.Z)("recharts-radial-bar-label",s)}),c().createElement("defs",null,c().createElement("path",{id:S,d:w})),c().createElement("textPath",{xlinkHref:"#".concat(S)},e))},ak=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var s=ay(o,i,c+r,u),l=s.x;return{x:l,y:s.y,textAnchor:l>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=ay(o,i,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},a_=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,c=e.width,u=e.height,s=u>=0?1:-1,l=s*n,f=s>0?"end":"start",p=s>0?"start":"end",d=c>=0?1:-1,h=d*n,y=d>0?"end":"start",v=d>0?"start":"end";if("top"===o)return aS(aS({},{x:i+c/2,y:a-s*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===o)return aS(aS({},{x:i+c/2,y:a+u+l,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===o){var m={x:i-h,y:a+u/2,textAnchor:y,verticalAnchor:"middle"};return aS(aS({},m),r?{width:Math.max(m.x-r.x,0),height:u}:{})}if("right"===o){var b={x:i+c+h,y:a+u/2,textAnchor:v,verticalAnchor:"middle"};return aS(aS({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:u}:{})}var g=r?{width:c,height:u}:{};return"insideLeft"===o?aS({x:i+h,y:a+u/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===o?aS({x:i+c-h,y:a+u/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===o?aS({x:i+c/2,y:a+l,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===o?aS({x:i+c/2,y:a+u-l,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===o?aS({x:i+h,y:a+l,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===o?aS({x:i+c-h,y:a+l,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===o?aS({x:i+h,y:a+u-l,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===o?aS({x:i+c-h,y:a+u-l,textAnchor:y,verticalAnchor:f},g):z()(o)&&($(o.x)||G(o.x))&&($(o.y)||G(o.y))?aS({x:i+Y(o.x,c),y:a+Y(o.y,u),textAnchor:"end",verticalAnchor:"end"},g):aS({x:i+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function aT(t){var e,r=t.offset,n=aS({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,aO)),o=n.viewBox,i=n.position,u=n.value,s=n.children,l=n.content,f=n.className,p=n.textBreakAll;if(!o||j()(u)&&j()(s)&&!(0,a.isValidElement)(l)&&!P()(l))return null;if((0,a.isValidElement)(l))return(0,a.cloneElement)(l,n);if(P()(l)){if(e=(0,a.createElement)(l,n),(0,a.isValidElement)(e))return e}else e=aA(n);var d="cx"in o&&$(o.cx),h=tS(n,!0);if(d&&("insideStart"===i||"insideEnd"===i||"end"===i))return aE(n,e,h);var y=d?ak(n):a_(n);return c().createElement(nq,aP({className:(0,N.Z)("recharts-label",void 0===f?"":f)},h,y,{breakAll:p}),e)}aT.displayName="Label";var aM=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,s=t.outerRadius,l=t.x,f=t.y,p=t.top,d=t.left,h=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if($(h)&&$(y)){if($(l)&&$(f))return{x:l,y:f,width:h,height:y};if($(p)&&$(d))return{x:p,y:d,width:h,height:y}}return $(l)&&$(f)?{x:l,y:f,width:0,height:0}:$(e)&&$(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:s||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};aT.parseViewBox=aM,aT.renderCallByParent=function(t,e){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var i=t.children,u=aM(t),s=tg(i,aT).map(function(t,r){return(0,a.cloneElement)(t,{viewBox:e||u,key:"label-".concat(r)})});return o?[(r=t.label,n=e||u,r?!0===r?c().createElement(aT,{key:"label-implicit",viewBox:n}):V(r)?c().createElement(aT,{key:"label-implicit",viewBox:n,value:r}):(0,a.isValidElement)(r)?r.type===aT?(0,a.cloneElement)(r,{key:"label-implicit",viewBox:n}):c().createElement(aT,{key:"label-implicit",content:r,viewBox:n}):P()(r)?c().createElement(aT,{key:"label-implicit",content:r,viewBox:n}):z()(r)?c().createElement(aT,aP({viewBox:n},r,{key:"label-implicit"})):null:null)].concat(function(t){if(Array.isArray(t))return aw(t)}(s)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(s)||function(t,e){if(t){if("string"==typeof t)return aw(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return aw(t,void 0)}}(s)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):s};var aC=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},aI=r(13880),aN=r.n(aI),aD=r(10853),aB=r.n(aD),aR=function(t){return null};aR.displayName="Cell";var aL=r(90601),az=r.n(aL);function aZ(t){return(aZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var aq=["valueAccessor"],aW=["data","dataKey","clockWise","id","textBreakAll"];function aU(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function aF(){return(aF=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function aX(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function aG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?aX(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=aZ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=aZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==aZ(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):aX(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function a$(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var aV=function(t){return Array.isArray(t.value)?az()(t.value):t.value};function aH(t){var e=t.valueAccessor,r=void 0===e?aV:e,n=a$(t,aq),o=n.data,i=n.dataKey,a=n.clockWise,u=n.id,s=n.textBreakAll,l=a$(n,aW);return o&&o.length?c().createElement(tN,{className:"recharts-label-list"},o.map(function(t,e){var n=j()(i)?r(t,e):iS(t&&t.payload,i),o=j()(u)?{}:{id:"".concat(u,"-").concat(e)};return c().createElement(aT,aF({},tS(t,!0),l,o,{parentViewBox:t.parentViewBox,value:n,textBreakAll:s,viewBox:aT.parseViewBox(j()(a)?t:aG(aG({},t),{},{clockWise:a})),key:"label-".concat(e),index:e}))})):null}aH.displayName="LabelList",aH.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var o=tg(t.children,aH).map(function(t,r){return(0,a.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label)?!0===r?c().createElement(aH,{key:"labelList-implicit",data:e}):c().isValidElement(r)||P()(r)?c().createElement(aH,{key:"labelList-implicit",data:e,content:r}):z()(r)?c().createElement(aH,aF({data:e},r,{key:"labelList-implicit"})):null:null].concat(function(t){if(Array.isArray(t))return aU(t)}(o)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return aU(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return aU(t,void 0)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var aK=r(36153),aY=r.n(aK),aJ=r(99388),aQ=r.n(aJ);function a0(t){return(a0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function a1(){return(a1=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function a2(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function a3(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a6(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?a3(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=a0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a0(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):a3(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var a5=function(t,e,r,n,o){var i=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-i/2,",").concat(e+o)+"L ".concat(t+r-i/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},a4={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},a7=function(t){var e,r=a6(a6({},a4),t),n=(0,a.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,a.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return a2(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return a2(t,2)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],u=o[1];(0,a.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var s=r.x,l=r.y,f=r.upperWidth,p=r.lowerWidth,d=r.height,h=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isUpdateAnimationActive;if(s!==+s||l!==+l||f!==+f||p!==+p||d!==+d||0===f&&0===p||0===d)return null;var g=(0,N.Z)("recharts-trapezoid",h);return b?c().createElement(r7,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:d,x:s,y:l},to:{upperWidth:f,lowerWidth:p,height:d,x:s,y:l},duration:v,animationEasing:y,isActive:b},function(t){var e=t.upperWidth,o=t.lowerWidth,a=t.height,u=t.x,s=t.y;return c().createElement(r7,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:y},c().createElement("path",a1({},tS(r,!0),{className:g,d:a5(u,s,e,o,a),ref:n})))}):c().createElement("g",null,c().createElement("path",a1({},tS(r,!0),{className:g,d:a5(s,l,f,p,d)})))};function a9(t){return(a9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function a8(){return(a8=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ct(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ce(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ct(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=a9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a9(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ct(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var cr=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,s=c*(a?1:-1)+n,l=Math.asin(c/s)/ah,f=u?o:o+i*l;return{center:ay(e,r,s,f),circleTangency:ay(e,r,n,f),lineTangency:ay(e,r,s*Math.cos(l*ah),u?o-i*l:o),theta:l}},cn=function(t){var e,r=t.cx,n=t.cy,o=t.innerRadius,i=t.outerRadius,a=t.startAngle,c=X((e=t.endAngle)-a)*Math.min(Math.abs(e-a),359.999),u=a+c,s=ay(r,n,i,a),l=ay(r,n,i,u),f="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(a>u),",\n    ").concat(l.x,",").concat(l.y,"\n  ");if(o>0){var p=ay(r,n,o,a),d=ay(r,n,o,u);f+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(o,",").concat(o,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(a<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(r,",").concat(n," Z");return f},co=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,s=t.endAngle,l=X(s-u),f=cr({cx:e,cy:r,radius:o,angle:u,sign:l,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,d=f.lineTangency,h=f.theta,y=cr({cx:e,cy:r,radius:o,angle:s,sign:-l,cornerRadius:i,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(u-s):Math.abs(u-s)-h-b;if(g<0)return a?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):cn({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:u,endAngle:s});var x="M ".concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(l<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(l<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(l<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var O=cr({cx:e,cy:r,radius:n,angle:u,sign:l,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),w=O.circleTangency,j=O.lineTangency,S=O.theta,P=cr({cx:e,cy:r,radius:n,angle:s,sign:-l,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),A=P.circleTangency,E=P.lineTangency,k=P.theta,_=c?Math.abs(u-s):Math.abs(u-s)-S-k;if(_<0&&0===i)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(l<0),",").concat(A.x,",").concat(A.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(_>180),",").concat(+(l>0),",").concat(w.x,",").concat(w.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(l<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},ci={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},ca=function(t){var e,r=ce(ce({},ci),t),n=r.cx,o=r.cy,i=r.innerRadius,a=r.outerRadius,u=r.cornerRadius,s=r.forceCornerRadius,l=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,d=r.className;if(a<i||f===p)return null;var h=(0,N.Z)("recharts-sector",d),y=a-i,v=Y(u,y,0,!0);return e=v>0&&360>Math.abs(f-p)?co({cx:n,cy:o,innerRadius:i,outerRadius:a,cornerRadius:Math.min(v,y/2),forceCornerRadius:s,cornerIsExternal:l,startAngle:f,endAngle:p}):cn({cx:n,cy:o,innerRadius:i,outerRadius:a,startAngle:f,endAngle:p}),c().createElement("path",a8({},tS(r,!0),{className:h,d:e,role:"img"}))},cc=["option","shapeType","propTransformer","activeClassName","isActive"];function cu(t){return(cu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function cs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function cl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?cs(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=cu(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=cu(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==cu(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):cs(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function cf(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return c().createElement(na,r);case"trapezoid":return c().createElement(a7,r);case"sector":return c().createElement(ca,r);case"symbols":if("symbols"===e)return c().createElement(eT,r);break;default:return null}}function cp(t){var e,r=t.option,n=t.shapeType,o=t.propTransformer,i=t.activeClassName,u=t.isActive,s=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,cc);if((0,a.isValidElement)(r))e=(0,a.cloneElement)(r,cl(cl({},s),(0,a.isValidElement)(r)?r.props:r));else if(P()(r))e=r(s);else if(aY()(r)&&!aQ()(r)){var l=(void 0===o?function(t,e){return cl(cl({},e),t)}:o)(r,s);e=c().createElement(cf,{shapeType:n,elementProps:l})}else e=c().createElement(cf,{shapeType:n,elementProps:s});return u?c().createElement(tN,{className:void 0===i?"recharts-active-shape":i},e):e}function cd(t,e){return null!=e&&"trapezoids"in t.props}function ch(t,e){return null!=e&&"sectors"in t.props}function cy(t,e){return null!=e&&"points"in t.props}function cv(t,e){var r,n,o=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function cm(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function cb(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}var cg=["x","y"];function cx(t){return(cx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function cO(){return(cO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function cw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function cj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?cw(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=cx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=cx(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==cx(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):cw(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function cS(t,e){var r=t.x,n=t.y,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,cg),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||o.height),10),u=parseInt("".concat(e.width||o.width),10);return cj(cj(cj(cj(cj({},e),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function cP(t){return c().createElement(cp,cO({shapeType:"rectangle",propTransformer:cS,activeClassName:"recharts-active-bar"},t))}var cA=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o="number"==typeof r;return o?t(r,n):(o||D(!1),e)}},cE=["value","background"];function ck(t){return(ck="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c_(){return(c_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function cT(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function cM(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?cT(Object(r),!0).forEach(function(e){cB(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):cT(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function cC(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,cR(n.key),n)}}function cI(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(cI=function(){return!!t})()}function cN(t){return(cN=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function cD(t,e){return(cD=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function cB(t,e,r){return(e=cR(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function cR(t){var e=function(t,e){if("object"!=ck(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ck(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ck(e)?e:e+""}var cL=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=cN(e),cB(t=function(t,e){if(e&&("object"===ck(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,cI()?Reflect.construct(e,r||[],cN(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),cB(t,"id",K("recharts-bar-")),cB(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),cB(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&cD(t,e)}(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,a=r.activeBar,u=tS(this.props,!1);return t&&t.map(function(t,r){var s=r===i,l=cM(cM(cM({},u),t),{},{isActive:s,option:s?a:n,index:r,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return c().createElement(tN,c_({className:"recharts-bar-rectangle"},tl(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),c().createElement(cP,l))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,o=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,u=e.animationEasing,s=e.animationId,l=this.state.prevData;return c().createElement(r7,{begin:i,duration:a,isActive:o,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var o=e.t,i=r.map(function(t,e){var r=l&&l[e];if(r){var i=tt(r.x,t.x),a=tt(r.y,t.y),c=tt(r.width,t.width),u=tt(r.height,t.height);return cM(cM({},t),{},{x:i(o),y:a(o),width:c(o),height:u(o)})}if("horizontal"===n){var s=tt(0,t.height)(o);return cM(cM({},t),{},{y:t.y+t.height-s,height:s})}var f=tt(0,t.width)(o);return cM(cM({},t),{},{width:f})});return c().createElement(tN,null,t.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!og()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,o=e.activeIndex,i=tS(this.props.background,!1);return r.map(function(e,r){e.value;var a=e.background,u=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,cE);if(!a)return null;var s=cM(cM(cM(cM(cM({},u),{},{fill:"#eee"},a),i),tl(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return c().createElement(cP,c_({key:"background-bar-".concat(r),option:t.props.background,isActive:r===o},s))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,a=r.layout,u=tg(r.children,id);if(!u)return null;var s="vertical"===a?n[0].height/2:n[0].width/2,l=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:iS(t,e)}};return c().createElement(tN,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return c().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:a,offset:s,dataPointFormatter:l})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,o=t.xAxis,i=t.yAxis,a=t.left,u=t.top,s=t.width,l=t.height,f=t.isAnimationActive,p=t.background,d=t.id;if(e||!r||!r.length)return null;var h=this.state.isAnimationFinished,y=(0,N.Z)("recharts-bar",n),v=o&&o.allowDataOverflow,m=i&&i.allowDataOverflow,b=v||m,g=j()(d)?this.id:d;return c().createElement(tN,{className:y},v||m?c().createElement("defs",null,c().createElement("clipPath",{id:"clipPath-".concat(g)},c().createElement("rect",{x:v?a:a-s/2,y:m?u:u-l/2,width:v?s:2*s,height:m?l:2*l}))):null,c().createElement(tN,{className:"recharts-bar-rectangles",clipPath:b?"url(#clipPath-".concat(g,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(b,g),(!f||h)&&aH.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&cC(n.prototype,e),r&&cC(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a.PureComponent);function cz(t){return(cz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function cZ(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,cF(n.key),n)}}function cq(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function cW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?cq(Object(r),!0).forEach(function(e){cU(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):cq(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function cU(t,e,r){return(e=cF(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function cF(t){var e=function(t,e){if("object"!=cz(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=cz(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==cz(e)?e:e+""}cB(cL,"displayName","Bar"),cB(cL,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!t2.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),cB(cL,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,s=t.stackedData,l=t.dataStartIndex,f=t.displayedData,p=t.offset,d=iZ(n,r);if(!d)return null;var h=e.layout,y=r.type.defaultProps,v=void 0!==y?cM(cM({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===h?a:i,O=s?x.scale.domain():null,w=i$({numericAxis:x}),j=tg(b,aR),S=f.map(function(t,e){s?f=iq(s[l+e],O):Array.isArray(f=iS(t,m))||(f=[w,f]);var n=cA(g,cL.defaultProps.minPointSize)(f[1],e);if("horizontal"===h){var f,p,y,v,b,x,S,P=[a.scale(f[0]),a.scale(f[1])],A=P[0],E=P[1];p=iG({axis:i,ticks:c,bandSize:o,offset:d.offset,entry:t,index:e}),y=null!==(S=null!=E?E:A)&&void 0!==S?S:void 0,v=d.size;var k=A-E;if(b=Number.isNaN(k)?0:k,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var _=X(b||n)*(Math.abs(n)-Math.abs(b));y-=_,b+=_}}else{var T=[i.scale(f[0]),i.scale(f[1])],M=T[0],C=T[1];if(p=M,y=iG({axis:a,ticks:u,bandSize:o,offset:d.offset,entry:t,index:e}),v=C-M,b=d.size,x={x:i.x,y:y,width:i.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var I=X(v||n)*(Math.abs(n)-Math.abs(v));v+=I}}return cM(cM(cM({},t),{},{x:p,y:y,width:v,height:b,value:s?f:f[1],payload:t,background:x},j&&j[e]&&j[e].props),{},{tooltipPayload:[i1(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return cM({data:S,layout:h},p)});var cX=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},cG=function(){var t,e;function r(t){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,r),this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&cZ(r.prototype,t),e&&cZ(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();cU(cG,"EPS",1e-4);var c$=function(t){var e=Object.keys(t).reduce(function(e,r){return cW(cW({},e),{},cU({},r,cG.create(t[r])))},{});return cW(cW({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return aN()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return aB()(t,function(t,r){return e[r].isInRange(t)})}})},cV=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o))};function cH(){return(cH=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function cK(t){return(cK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function cY(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function cJ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?cY(Object(r),!0).forEach(function(e){c2(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):cY(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function cQ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(cQ=function(){return!!t})()}function c0(t){return(c0=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function c1(t,e){return(c1=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function c2(t,e,r){return(e=c3(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function c3(t){var e=function(t,e){if("object"!=cK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=cK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==cK(e)?e:e+""}var c6=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=c$({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return aC(t,"discard")&&!i.isInRange(a)?null:a},c5=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=c0(t),function(t,e){if(e&&("object"===cK(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,cQ()?Reflect.construct(t,e||[],c0(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&c1(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,o=t.r,i=t.alwaysShow,a=t.clipPathId,u=V(e),s=V(n);if(ea(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!s)return null;var l=c6(this.props);if(!l)return null;var f=l.x,p=l.y,d=this.props,h=d.shape,y=d.className,v=cJ(cJ({clipPath:aC(this.props,"hidden")?"url(#".concat(a,")"):void 0},tS(this.props,!0)),{},{cx:f,cy:p});return c().createElement(tN,{className:(0,N.Z)("recharts-reference-dot",y)},r.renderDot(h,v),aT.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,c3(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(c().Component);c2(c5,"displayName","ReferenceDot"),c2(c5,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),c2(c5,"renderDot",function(t,e){return c().isValidElement(t)?c().cloneElement(t,e):P()(t)?t(e):c().createElement(eQ,cH({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var c4=r(34009),c7=r.n(c4);r(9660);var c9=r(97300),c8=r.n(c9)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),ut=(0,a.createContext)(void 0),ue=(0,a.createContext)(void 0),ur=(0,a.createContext)(void 0),un=(0,a.createContext)({}),uo=(0,a.createContext)(void 0),ui=(0,a.createContext)(0),ua=(0,a.createContext)(0),uc=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,o=e.offset,i=t.clipPathId,a=t.children,u=t.width,s=t.height,l=c8(o);return c().createElement(ut.Provider,{value:r},c().createElement(ue.Provider,{value:n},c().createElement(un.Provider,{value:o},c().createElement(ur.Provider,{value:l},c().createElement(uo.Provider,{value:i},c().createElement(ui.Provider,{value:s},c().createElement(ua.Provider,{value:u},a)))))))},uu=function(t){var e=(0,a.useContext)(ut);null!=e||D(!1);var r=e[t];return null!=r||D(!1),r},us=function(t){var e=(0,a.useContext)(ue);null!=e||D(!1);var r=e[t];return null!=r||D(!1),r},ul=function(){return(0,a.useContext)(ua)},uf=function(){return(0,a.useContext)(ui)};function up(t){return(up="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ud(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ud=function(){return!!t})()}function uh(t){return(uh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function uy(t,e){return(uy=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function um(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uv(Object(r),!0).forEach(function(e){ub(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uv(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ub(t,e,r){return(e=ug(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ug(t){var e=function(t,e){if("object"!=up(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=up(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==up(e)?e:e+""}function ux(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uO(){return(uO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var uw=function(t,e,r,n,o,i,a,c,u){var s=o.x,l=o.y,f=o.width,p=o.height;if(r){var d=u.y,h=t.y.apply(d,{position:i});if(aC(u,"discard")&&!t.y.isInRange(h))return null;var y=[{x:s+f,y:h},{x:s,y:h}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(aC(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:l+p},{x:m,y:l}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:i})});return aC(u,"discard")&&c7()(g,function(e){return!t.isInRange(e)})?null:g}return null};function uj(t){var e,r,n,o=t.x,i=t.y,u=t.segment,s=t.xAxisId,l=t.yAxisId,f=t.shape,p=t.className,d=t.alwaysShow,h=(0,a.useContext)(uo),y=uu(s),v=us(l),m=(0,a.useContext)(ur);if(!h||!m)return null;ea(void 0===d,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var b=uw(c$({x:y.scale,y:v.scale}),V(o),V(i),u&&2===u.length,m,t.position,y.orientation,v.orientation,t);if(!b)return null;var g=function(t){if(Array.isArray(t))return t}(b)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(b,2)||function(t,e){if(t){if("string"==typeof t)return ux(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ux(t,2)}}(b,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),x=g[0],O=x.x,w=x.y,j=g[1],S=j.x,A=j.y,E=um(um({clipPath:aC(t,"hidden")?"url(#".concat(h,")"):void 0},tS(t,!0)),{},{x1:O,y1:w,x2:S,y2:A});return c().createElement(tN,{className:(0,N.Z)("recharts-reference-line",p)},(e=f,r=E,c().isValidElement(e)?c().cloneElement(e,r):P()(e)?e(r):c().createElement("line",uO({},r,{className:"recharts-reference-line-line"}))),aT.renderCallByParent(t,cX({x:(n={x1:O,y1:w,x2:S,y2:A}).x1,y:n.y1},{x:n.x2,y:n.y2})))}var uS=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=uh(t),function(t,e){if(e&&("object"===up(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ud()?Reflect.construct(t,e||[],uh(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&uy(t,e)}(r,t),e=[{key:"render",value:function(){return c().createElement(uj,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ug(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(c().Component);function uP(){return(uP=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function uA(t){return(uA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uE(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uk(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uE(Object(r),!0).forEach(function(e){uC(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uE(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function u_(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(u_=function(){return!!t})()}function uT(t){return(uT=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function uM(t,e){return(uM=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uC(t,e,r){return(e=uI(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uI(t){var e=function(t,e){if("object"!=uA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uA(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uA(e)?e:e+""}ub(uS,"displayName","ReferenceLine"),ub(uS,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var uN=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,s=o.xAxis,l=o.yAxis;if(!s||!l)return null;var f=c$({x:s.scale,y:l.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},d={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!aC(o,"discard")||f.isInRange(p)&&f.isInRange(d)?cX(p,d):null},uD=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=uT(t),function(t,e){if(e&&("object"===uA(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,u_()?Reflect.construct(t,e||[],uT(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&uM(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,o=t.y1,i=t.y2,a=t.className,u=t.alwaysShow,s=t.clipPathId;ea(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=V(e),f=V(n),p=V(o),d=V(i),h=this.props.shape;if(!l&&!f&&!p&&!d&&!h)return null;var y=uN(l,f,p,d,this.props);if(!y&&!h)return null;var v=aC(this.props,"hidden")?"url(#".concat(s,")"):void 0;return c().createElement(tN,{className:(0,N.Z)("recharts-reference-area",a)},r.renderRect(h,uk(uk({clipPath:v},tS(this.props,!0)),y)),aT.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uI(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(c().Component);function uB(t){return function(t){if(Array.isArray(t))return uR(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return uR(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uR(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uR(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}uC(uD,"displayName","ReferenceArea"),uC(uD,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),uC(uD,"renderRect",function(t,e){return c().isValidElement(t)?c().cloneElement(t,e):P()(t)?t(e):c().createElement(na,uP({},e,{className:"recharts-reference-area-rect"}))});var uL=function(t,e,r,n,o){var i=tg(t,uS),a=tg(t,c5),c=[].concat(uB(i),uB(a)),u=tg(t,uD),s="".concat(n,"Id"),l=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[s]===r&&aC(e.props,"extendDomain")&&$(e.props[l])){var n=e.props[l];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(l,"1"),d="".concat(l,"2");f=u.reduce(function(t,e){if(e.props[s]===r&&aC(e.props,"extendDomain")&&$(e.props[p])&&$(e.props[d])){var n=e.props[p],o=e.props[d];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return $(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},uz=r(51028),uZ=new(r.n(uz)()),uq="recharts.syncMouseEvents";function uW(t){return(uW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uU(t,e,r){return(e=uF(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uF(t){var e=function(t,e){if("object"!=uW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uW(e)?e:e+""}var uX=function(){var t,e;return t=function t(){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,t),uU(this,"activeIndex",0),uU(this,"coordinateList",[]),uU(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,s=void 0===u?null:u,l=t.mouseHandlerCallback,f=void 0===l?null:l;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=s?s:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,u=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,s=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:s})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uF(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function uG(){}function u$(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function uV(t){this._context=t}function uH(t){this._context=t}function uK(t){this._context=t}uV.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:u$(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:u$(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},uH.prototype={areaStart:uG,areaEnd:uG,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:u$(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},uK.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:u$(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class uY{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function uJ(t){this._context=t}function uQ(t){this._context=t}function u0(t){return new uQ(t)}function u1(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function u2(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function u3(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function u6(t){this._context=t}function u5(t){this._context=new u4(t)}function u4(t){this._context=t}function u7(t){this._context=t}function u9(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function u8(t,e){this._context=t,this._t=e}uJ.prototype={areaStart:uG,areaEnd:uG,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},uQ.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},u6.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:u3(this,this._t0,u2(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,u3(this,u2(this,r=u1(this,t,e)),r);break;default:u3(this,this._t0,r=u1(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(u5.prototype=Object.create(u6.prototype)).point=function(t,e){u6.prototype.point.call(this,e,t)},u4.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},u7.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r){if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=u9(t),o=u9(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},u8.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var st=r(10557);function se(t){return t[0]}function sr(t){return t[1]}function sn(t,e){var r=(0,ex.Z)(!0),n=null,o=u0,i=null,a=(0,eO.d)(c);function c(c){var u,s,l,f=(c=(0,st.Z)(c)).length,p=!1;for(null==n&&(i=o(l=a())),u=0;u<=f;++u)!(u<f&&r(s=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(s,u,c),+e(s,u,c));if(l)return i=null,l+""||null}return t="function"==typeof t?t:void 0===t?se:(0,ex.Z)(t),e="function"==typeof e?e:void 0===e?sr:(0,ex.Z)(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,ex.Z)(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,ex.Z)(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:(0,ex.Z)(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function so(t,e,r){var n=null,o=(0,ex.Z)(!0),i=null,a=u0,c=null,u=(0,eO.d)(s);function s(s){var l,f,p,d,h,y=(s=(0,st.Z)(s)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(c=a(h=u())),l=0;l<=y;++l){if(!(l<y&&o(d=s[l],l,s))===v){if(v=!v)f=l,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=l-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}}v&&(m[l]=+t(d,l,s),b[l]=+e(d,l,s),c.point(n?+n(d,l,s):m[l],r?+r(d,l,s):b[l]))}if(h)return c=null,h+""||null}function l(){return sn().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?se:(0,ex.Z)(+t),e="function"==typeof e?e:void 0===e?(0,ex.Z)(0):(0,ex.Z)(+e),r="function"==typeof r?r:void 0===r?sr:(0,ex.Z)(+r),s.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,ex.Z)(+e),n=null,s):t},s.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,ex.Z)(+e),s):t},s.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,ex.Z)(+t),s):n},s.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,ex.Z)(+t),r=null,s):e},s.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,ex.Z)(+t),s):e},s.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,ex.Z)(+t),s):r},s.lineX0=s.lineY0=function(){return l().x(t).y(e)},s.lineY1=function(){return l().x(t).y(r)},s.lineX1=function(){return l().x(n).y(e)},s.defined=function(t){return arguments.length?(o="function"==typeof t?t:(0,ex.Z)(!!t),s):o},s.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),s):a},s.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),s):i},s}function si(t){return(si="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sa(){return(sa=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function su(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sc(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=si(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=si(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==si(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sc(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var ss={curveBasisClosed:function(t){return new uH(t)},curveBasisOpen:function(t){return new uK(t)},curveBasis:function(t){return new uV(t)},curveBumpX:function(t){return new uY(t,!0)},curveBumpY:function(t){return new uY(t,!1)},curveLinearClosed:function(t){return new uJ(t)},curveLinear:u0,curveMonotoneX:function(t){return new u6(t)},curveMonotoneY:function(t){return new u5(t)},curveNatural:function(t){return new u7(t)},curveStep:function(t){return new u8(t,.5)},curveStepAfter:function(t){return new u8(t,1)},curveStepBefore:function(t){return new u8(t,0)}},sl=function(t){return t.x===+t.x&&t.y===+t.y},sf=function(t){return t.x},sp=function(t){return t.y},sd=function(t,e){if(P()(t))return t;var r="curve".concat(eu()(t));return("curveMonotone"===r||"curveBump"===r)&&e?ss["".concat(r).concat("vertical"===e?"Y":"X")]:ss[r]||u0},sh=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,s=sd(void 0===r?"linear":r,a),l=u?o.filter(function(t){return sl(t)}):o;if(Array.isArray(i)){var f=u?i.filter(function(t){return sl(t)}):i,p=l.map(function(t,e){return su(su({},t),{},{base:f[e]})});return(e="vertical"===a?so().y(sp).x1(sf).x0(function(t){return t.base.x}):so().x(sf).y1(sp).y0(function(t){return t.base.y})).defined(sl).curve(s),e(p)}return(e="vertical"===a&&$(i)?so().y(sp).x1(sf).x0(i):$(i)?so().x(sf).y1(sp).y0(i):sn().x(sf).y(sp)).defined(sl).curve(s),e(l)},sy=function(t){var e=t.className,r=t.points,n=t.path,o=t.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?sh(t):n;return c().createElement("path",sa({},tS(t,!1),ts(t),{className:(0,N.Z)("recharts-curve",e),d:i,ref:o}))};function sv(t){return(sv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var sm=["x","y","top","left","width","height","className"];function sb(){return(sb=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var sx=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,o=void 0===n?0:n,i=t.top,a=void 0===i?0:i,u=t.left,s=void 0===u?0:u,l=t.width,f=void 0===l?0:l,p=t.height,d=void 0===p?0:p,h=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sg(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=sv(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sv(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sv(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sg(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:o,top:a,left:s,width:f,height:d},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,sm));return $(r)&&$(o)&&$(f)&&$(d)&&$(a)&&$(s)?c().createElement("path",sb({},tS(y,!0),{className:(0,N.Z)("recharts-cross",h),d:"M".concat(r,",").concat(a,"v").concat(d,"M").concat(s,",").concat(o,"h").concat(f)})):null};function sO(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[ay(e,r,n,o),ay(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}function sw(t){return(sw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sj(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=sw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sw(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sP(t){var e,r,n,o,i=t.element,c=t.tooltipEventType,u=t.isActive,s=t.activeCoordinate,l=t.activePayload,f=t.offset,p=t.activeTooltipIndex,d=t.tooltipAxisBandSize,h=t.layout,y=t.chartName,v=null!==(r=i.props.cursor)&&void 0!==r?r:null===(n=i.type.defaultProps)||void 0===n?void 0:n.cursor;if(!i||!v||!u||!s||"ScatterChart"!==y&&"axis"!==c)return null;var m=sy;if("ScatterChart"===y)o=s,m=sx;else if("BarChart"===y)e=d/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===h?s.x-e:f.left+.5,y:"horizontal"===h?f.top+.5:s.y-e,width:"horizontal"===h?d:f.width-1,height:"horizontal"===h?f.height-1:d},m=na;else if("radial"===h){var b=sO(s),g=b.cx,x=b.cy,O=b.radius;o={cx:g,cy:x,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:O,outerRadius:O},m=ca}else o={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return sO(e);var c=e.cx,u=e.cy,s=e.innerRadius,l=e.outerRadius,f=e.angle,p=ay(c,u,s,f),d=ay(c,u,l,f);n=p.x,o=p.y,i=d.x,a=d.y}return[{x:n,y:o},{x:i,y:a}]}(h,s,f)},m=sy;var w=sS(sS(sS(sS({stroke:"#ccc",pointerEvents:"none"},f),o),tS(v,!1)),{},{payload:l,payloadIndex:p,className:(0,N.Z)("recharts-tooltip-cursor",v.className)});return(0,a.isValidElement)(v)?(0,a.cloneElement)(v,w):(0,a.createElement)(m,w)}var sA=["item"],sE=["children","className","width","height","style","compact","title","desc"];function sk(t){return(sk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s_(){return(s_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sT(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(t,e)||sB(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function sM(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function sC(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(sC=function(){return!!t})()}function sI(t){return(sI=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function sN(t,e){return(sN=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function sD(t){return function(t){if(Array.isArray(t))return sR(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||sB(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function sB(t,e){if(t){if("string"==typeof t)return sR(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sR(t,e)}}function sR(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sL(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sz(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sL(Object(r),!0).forEach(function(e){sZ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sL(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sZ(t,e,r){return(e=sq(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function sq(t){var e=function(t,e){if("object"!=sk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sk(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sk(e)?e:e+""}var sW={xAxis:["bottom","top"],yAxis:["left","right"]},sU={width:"100%",height:"100%"},sF={x:0,y:0};function sX(t){return t}var sG=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return sz(sz(sz({},n),ay(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return sz(sz(sz({},n),ay(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return sF},s$=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(sD(t),sD(r)):t},[]);return i.length>0?i:t&&t.length&&$(n)&&$(o)?t.slice(n,o+1):[]};function sV(t){return"number"===t?[0,"auto"]:void 0}var sH=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=s$(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var u,s,l=null!==(u=c.props.data)&&void 0!==u?u:e;return(l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1)),s=i.dataKey&&!i.allowDuplicatedCategory?te(void 0===l?a:l,i.dataKey,n):l&&l[r]||a[r])?[].concat(sD(o),[i1(c,s)]):o},[])},sK=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,s=iA(i,a,u,c);if(s>=0&&u){var l=u[s]&&u[s].value,f=sH(t,e,s,l),p=sG(r,a,s,o);return{activeTooltipIndex:s,activeLabel:l,activePayload:f,activeCoordinate:p}}return null},sY=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.layout,l=t.children,f=t.stackOffset,p=iN(s,o);return r.reduce(function(e,r){var d=void 0!==r.type.defaultProps?sz(sz({},r.type.defaultProps),r.props):r.props,h=d.type,y=d.dataKey,v=d.allowDataOverflow,m=d.allowDuplicatedCategory,b=d.scale,g=d.ticks,x=d.includeHidden,O=d[i];if(e[O])return e;var w=s$(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i])===O}),dataStartIndex:c,dataEndIndex:u}),S=w.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&$(n)&&$(o))return!0}return!1})(d.domain,v,h)&&(k=iJ(d.domain,null,v),p&&("number"===h||"auto"!==b)&&(T=iP(w,y,"category")));var P=sV(h);if(!k||0===k.length){var A,k,_,T,M,C=null!==(M=d.domain)&&void 0!==M?M:P;if(y){if(k=iP(w,y,h),"category"===h&&p){var I=Q(k);m&&I?(_=k,k=E()(0,S)):m||(k=i0(C,k,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(sD(t),[e])},[]))}else if("category"===h)k=m?k.filter(function(t){return""!==t&&!j()(t)}):i0(C,k,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||j()(e)?t:[].concat(sD(t),[e])},[]);else if("number"===h){var N=iC(w,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===O&&(x||!o)}),y,o,s);N&&(k=N)}p&&("number"===h||"auto"!==b)&&(T=iP(w,y,"category"))}else k=p?E()(0,S):a&&a[O]&&a[O].hasStack&&"number"===h?"expand"===f?[0,1]:iH(a[O].stackGroups,c,u):iI(w,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===O&&(x||!r)}),h,s,!0);"number"===h?(k=uL(l,k,O,o,g),C&&(k=iJ(C,k,v))):"category"===h&&C&&k.every(function(t){return C.indexOf(t)>=0})&&(k=C)}return sz(sz({},e),{},sZ({},O,sz(sz({},d),{},{axisType:o,domain:k,categoricalDomain:T,duplicateDomain:_,originalDomain:null!==(A=d.domain)&&void 0!==A?A:P,isCategorical:p,layout:s})))},{})},sJ=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.layout,l=t.children,f=s$(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,d=iN(s,o),h=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?sz(sz({},e.type.defaultProps),e.props):e.props)[i],m=sV("number");return t[v]?t:(h++,y=d?E()(0,p):a&&a[v]&&a[v].hasStack?uL(l,y=iH(a[v].stackGroups,c,u),v,o):uL(l,y=iJ(m,iI(f,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===v&&!o}),"number",s),n.defaultProps.allowDataOverflow),v,o),sz(sz({},t),{},sZ({},v,sz(sz({axisType:o},n.defaultProps),{},{hide:!0,orientation:_()(sW,"".concat(o,".").concat(h%2),null),domain:y,originalDomain:m,isCategorical:d,layout:s}))))},{})},sQ=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.children,l="".concat(n,"Id"),f=tg(s,o),p={};return f&&f.length?p=sY(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:l,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(p=sJ(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:l,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},s0=function(t){var e=J(t),r=iD(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:M()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:iQ(e,r)}},s1=function(t){var e=t.children,r=t.defaultShowTooltip,n=tx(e,al),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},s2=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},s3=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,s=r.height,l=r.children,f=r.margin||{},p=tx(l,al),d=tx(l,eY),h=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:sz(sz({},t),{},sZ({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:sz(sz({},t),{},sZ({},n,_()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=sz(sz({},y),h),m=v.bottom;p&&(v.bottom+=p.props.height||al.defaultProps.height),d&&e&&(v=iT(v,n,r,e));var b=u-v.left-v.right,g=s-v.top-v.bottom;return sz(sz({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})};function s6(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e){if(void 0!==r&&!0!==r(t[o]))return;n.push(t[o])}return n}function s5(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function s4(t){return(s4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s7(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=s4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s4(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var s8=["viewBox"],lt=["viewBox"],le=["ticks"];function lr(t){return(lr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ln(){return(ln=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function li(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lo(Object(r),!0).forEach(function(e){lf(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lo(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function la(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function lc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lp(n.key),n)}}function lu(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lu=function(){return!!t})()}function ls(t){return(ls=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ll(t,e){return(ll=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function lf(t,e,r){return(e=lp(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lp(t){var e=function(t,e){if("object"!=lr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lr(e)?e:e+""}var ld=function(t){var e,r;function n(t){var e,r,o;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=ls(r),(e=function(t,e){if(e&&("object"===lr(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,lu()?Reflect.construct(r,o||[],ls(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},e}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ll(t,e)}(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=la(t,s8),o=this.props,i=o.viewBox,a=la(o,lt);return!tn(r,i)||!tn(n,a)||!tn(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,s=c.y,l=c.width,f=c.height,p=c.orientation,d=c.tickSize,h=c.mirror,y=c.tickMargin,v=h?-1:1,m=t.tickSize||d,b=$(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=s+ +!h*f)-v*m)-v*y,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=u+ +!h*l)-v*m)-v*y,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=u+ +h*l)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(o=s+ +h*f)+v*m)+v*y,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.orientation,a=t.mirror,u=t.axisLine,s=li(li(li({},tS(this.props,!1)),tS(u,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var l=+("top"===i&&!a||"bottom"===i&&a);s=li(li({},s),{},{x1:e,y1:r+l*o,x2:e+n,y2:r+l*o})}else{var f=+("left"===i&&!a||"right"===i&&a);s=li(li({},s),{},{x1:e+f*n,y1:r,x2:e+f*n,y2:r+o})}return c().createElement("line",ln({},s,{className:(0,N.Z)("recharts-cartesian-axis-line",_()(u,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var o=this,i=this.props,a=i.tickLine,u=i.stroke,s=i.tick,l=i.tickFormatter,f=i.unit,p=function(t,e,r){var n,o,i,a,c,u=t.tick,s=t.ticks,l=t.viewBox,f=t.minTickGap,p=t.orientation,d=t.interval,h=t.tickFormatter,y=t.unit,v=t.angle;if(!s||!s.length||!u)return[];if($(d)||t2.isSsr)return s6(s,("number"==typeof d&&$(d)?d:0)+1);var m="top"===p||"bottom"===p?"width":"height",b=y&&"width"===m?nh(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},g=function(t,n){var o,i=P()(h)?h(t.value,n):t.value;return"width"===m?cV({width:(o=nh(i,{fontSize:e,letterSpacing:r})).width+b.width,height:o.height+b.height},v):nh(i,{fontSize:e,letterSpacing:r})[m]},x=s.length>=2?X(s[1].coordinate-s[0].coordinate):1,O=(n="width"===m,o=l.x,i=l.y,a=l.width,c=l.height,1===x?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i});return"equidistantPreserveStart"===d?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,s=0,l=1,f=c;l<=a.length;)if(i=function(){var e,i=null==n?void 0:n[s];if(void 0===i)return{v:s6(n,l)};var a=s,p=function(){return void 0===e&&(e=r(i,a)),e},d=i.coordinate,h=0===s||s5(t,d,p,f,u);h||(s=0,f=c,l+=1),h&&(f=d+t*(p()/2+o),s+=l)}())return i.v;return[]}(x,O,g,s,f):("preserveStart"===d||"preserveStartEnd"===d?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,s=e.end;if(i){var l=n[c-1],f=r(l,c-1),p=t*(l.coordinate+t*f/2-s);a[c-1]=l=s9(s9({},l),{},{tickCoord:p>0?l.coordinate-p*t:l.coordinate}),s5(t,l.tickCoord,function(){return f},u,s)&&(s=l.tickCoord-t*(f/2+o),a[c-1]=s9(s9({},l),{},{isShow:!0}))}for(var d=i?c-1:c,h=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var l=t*(i.coordinate-t*c()/2-u);a[e]=i=s9(s9({},i),{},{tickCoord:l<0?i.coordinate-l*t:i.coordinate})}else a[e]=i=s9(s9({},i),{},{tickCoord:i.coordinate});s5(t,i.tickCoord,c,u,s)&&(u=i.tickCoord+t*(c()/2+o),a[e]=s9(s9({},i),{},{isShow:!0}))},y=0;y<d;y++)h(y);return a}(x,O,g,s,f,"preserveStartEnd"===d):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,s=function(e){var n,s=i[e],l=function(){return void 0===n&&(n=r(s,e)),n};if(e===a-1){var f=t*(s.coordinate+t*l()/2-u);i[e]=s=s9(s9({},s),{},{tickCoord:f>0?s.coordinate-f*t:s.coordinate})}else i[e]=s=s9(s9({},s),{},{tickCoord:s.coordinate});s5(t,s.tickCoord,l,c,u)&&(u=s.tickCoord-t*(l()/2+o),i[e]=s9(s9({},s),{},{isShow:!0}))},l=a-1;l>=0;l--)s(l);return i}(x,O,g,s,f)).filter(function(t){return t.isShow})}(li(li({},this.props),{},{ticks:t}),e,r),d=this.getTickTextAnchor(),h=this.getTickVerticalAnchor(),y=tS(this.props,!1),v=tS(s,!1),m=li(li({},y),{},{fill:"none"},tS(a,!1)),b=p.map(function(t,e){var r=o.getTickLineCoord(t),i=r.line,b=r.tick,g=li(li(li(li({textAnchor:d,verticalAnchor:h},y),{},{stroke:"none",fill:u},v),b),{},{index:e,payload:t,visibleTicksCount:p.length,tickFormatter:l});return c().createElement(tN,ln({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},tl(o.props,t,e)),a&&c().createElement("line",ln({},m,i,{className:(0,N.Z)("recharts-cartesian-axis-tick-line",_()(a,"className"))})),s&&n.renderTickItem(s,g,"".concat(P()(l)?l(t.value,e):t.value).concat(f||"")))});return c().createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,o=e.height,i=e.ticksGenerator,a=e.className;if(e.hide)return null;var u=this.props,s=u.ticks,l=la(u,le),f=s;return(P()(i)&&(f=i(s&&s.length>0?this.props:l)),n<=0||o<=0||!f||!f.length)?null:c().createElement(tN,{className:(0,N.Z)("recharts-cartesian-axis",a),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),aT.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){return c().isValidElement(t)?c().cloneElement(t,e):P()(t)?t(e):c().createElement(nq,ln({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&lc(n.prototype,e),r&&lc(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a.Component);function lh(t){return(lh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ly(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ly=function(){return!!t})()}function lv(t){return(lv=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lm(t,e){return(lm=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function lb(t,e,r){return(e=lg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lg(t){var e=function(t,e){if("object"!=lh(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lh(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lh(e)?e:e+""}function lx(){return(lx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lO(t){var e=t.xAxisId,r=ul(),n=uf(),o=uu(e);return null==o?null:c().createElement(ld,lx({},o,{className:(0,N.Z)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return iD(t,!0)}}))}lf(ld,"displayName","CartesianAxis"),lf(ld,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var lw=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=lv(t),function(t,e){if(e&&("object"===lh(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ly()?Reflect.construct(t,e||[],lv(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&lm(t,e)}(r,t),e=[{key:"render",value:function(){return c().createElement(lO,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lg(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(c().Component);function lj(t){return(lj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lS(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lS=function(){return!!t})()}function lP(t){return(lP=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lA(t,e){return(lA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function lE(t,e,r){return(e=lk(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lk(t){var e=function(t,e){if("object"!=lj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lj(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lj(e)?e:e+""}function l_(){return(l_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}lb(lw,"displayName","XAxis"),lb(lw,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});var lT=function(t){var e=t.yAxisId,r=ul(),n=uf(),o=us(e);return null==o?null:c().createElement(ld,l_({},o,{className:(0,N.Z)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return iD(t,!0)}}))},lM=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=lP(t),function(t,e){if(e&&("object"===lj(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,lS()?Reflect.construct(t,e||[],lP(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&lA(t,e)}(r,t),e=[{key:"render",value:function(){return c().createElement(lT,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lk(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(c().Component);lE(lM,"displayName","YAxis"),lE(lM,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var lC=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,o=void 0===n?"axis":n,i=t.validateTooltipEventTypes,u=void 0===i?["axis"]:i,s=t.axisComponents,l=t.legendContent,f=t.formatAxisMap,p=t.defaultProps,d=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,l=t.layout,f=t.barGap,p=t.barCategoryGap,d=t.maxBarSize,h=s2(l),y=h.numericAxisName,v=h.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=ty(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,h){var g=s$(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),x=void 0!==r.type.defaultProps?sz(sz({},r.type.defaultProps),r.props):r.props,O=x.dataKey,w=x.maxBarSize,S=x["".concat(y,"Id")],P=x["".concat(v,"Id")],A=s.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||D(!1);var i=n[o];return sz(sz({},t),{},sZ(sZ({},r.axisType,i),"".concat(r.axisType,"Ticks"),iD(i)))},{}),E=A[v],k=A["".concat(v,"Ticks")],_=n&&n[S]&&n[S].hasStack&&iV(r,n[S].stackGroups),T=ty(r.type).indexOf("Bar")>=0,M=iQ(E,k),C=[],I=m&&ik({barSize:u,stackGroups:n,totalSize:"xAxis"===v?A[v].width:"yAxis"===v?A[v].height:void 0});if(T){var N,B,R=j()(w)?d:w,L=null!==(N=null!==(B=iQ(E,k,!0))&&void 0!==B?B:R)&&void 0!==N?N:0;C=i_({barGap:f,barCategoryGap:p,bandSize:L!==M?L:M,sizeList:I[P],maxBarSize:R}),L!==M&&(C=C.map(function(t){return sz(sz({},t),{},{position:sz(sz({},t.position),{},{offset:t.position.offset-L/2})})}))}var z=r&&r.type&&r.type.getComposedData;z&&b.push({props:sz(sz({},z(sz(sz({},A),{},{displayedData:g,props:t,dataKey:O,item:r,bandSize:M,barPosition:C,offset:o,stackedData:_,layout:l,dataStartIndex:a,dataEndIndex:c}))),{},sZ(sZ(sZ({key:r.key||"item-".concat(h)},y,A[y]),v,A[v]),"animationId",i)),childIndex:tb(t.children).indexOf(r),item:r})}),b},h=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!tO({props:o}))return null;var u=o.children,l=o.layout,p=o.stackOffset,h=o.data,y=o.reverseStackOrder,v=s2(l),m=v.numericAxisName,b=v.cateAxisName,g=tg(u,r),x=iF(h,g,"".concat(m,"Id"),"".concat(b,"Id"),p,y),O=s.reduce(function(t,e){var r="".concat(e.axisType,"Map");return sz(sz({},t),{},sZ({},r,sQ(o,sz(sz({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),w=s3(sz(sz({},O),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(O).forEach(function(t){O[t]=f(o,O[t],w,t.replace("Map",""),e)});var j=s0(O["".concat(b,"Map")]),S=d(o,sz(sz({},O),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:w}));return sz(sz({formattedGraphicalItems:S,graphicalItems:g,offset:w,stackGroups:x},j),O)},y=function(t){var r;function n(t){var r,o,i,u,s;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),u=n,s=[t],u=sI(u),sZ(i=function(t,e){if(e&&("object"===sk(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,sC()?Reflect.construct(u,s||[],sI(this).constructor):u.apply(this,s)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),sZ(i,"accessibilityManager",new uX),sZ(i,"handleLegendBBoxUpdate",function(t){if(t){var e=i.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;i.setState(sz({legendBBox:t},h({props:i.props,dataStartIndex:r,dataEndIndex:n,updateId:o},sz(sz({},i.state),{},{legendBBox:t}))))}}),sZ(i,"handleReceiveSyncEvent",function(t,e,r){i.props.syncId===t&&(r!==i.eventEmitterSymbol||"function"==typeof i.props.syncMethod)&&i.applySyncEvent(e)}),sZ(i,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==i.state.dataStartIndex||r!==i.state.dataEndIndex){var n=i.state.updateId;i.setState(function(){return sz({dataStartIndex:e,dataEndIndex:r},h({props:i.props,dataStartIndex:e,dataEndIndex:r,updateId:n},i.state))}),i.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),sZ(i,"handleMouseEnter",function(t){var e=i.getMouseInfo(t);if(e){var r=sz(sz({},e),{},{isTooltipActive:!0});i.setState(r),i.triggerSyncEvent(r);var n=i.props.onMouseEnter;P()(n)&&n(r,t)}}),sZ(i,"triggeredAfterMouseMove",function(t){var e=i.getMouseInfo(t),r=e?sz(sz({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};i.setState(r),i.triggerSyncEvent(r);var n=i.props.onMouseMove;P()(n)&&n(r,t)}),sZ(i,"handleItemMouseEnter",function(t){i.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),sZ(i,"handleItemMouseLeave",function(){i.setState(function(){return{isTooltipActive:!1}})}),sZ(i,"handleMouseMove",function(t){t.persist(),i.throttleTriggeredAfterMouseMove(t)}),sZ(i,"handleMouseLeave",function(t){i.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};i.setState(e),i.triggerSyncEvent(e);var r=i.props.onMouseLeave;P()(r)&&r(e,t)}),sZ(i,"handleOuterEvent",function(t){var e,r=tk(t),n=_()(i.props,"".concat(r));r&&P()(n)&&n(null!==(e=/.*touch.*/i.test(r)?i.getMouseInfo(t.changedTouches[0]):i.getMouseInfo(t))&&void 0!==e?e:{},t)}),sZ(i,"handleClick",function(t){var e=i.getMouseInfo(t);if(e){var r=sz(sz({},e),{},{isTooltipActive:!0});i.setState(r),i.triggerSyncEvent(r);var n=i.props.onClick;P()(n)&&n(r,t)}}),sZ(i,"handleMouseDown",function(t){var e=i.props.onMouseDown;P()(e)&&e(i.getMouseInfo(t),t)}),sZ(i,"handleMouseUp",function(t){var e=i.props.onMouseUp;P()(e)&&e(i.getMouseInfo(t),t)}),sZ(i,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&i.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),sZ(i,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&i.handleMouseDown(t.changedTouches[0])}),sZ(i,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&i.handleMouseUp(t.changedTouches[0])}),sZ(i,"handleDoubleClick",function(t){var e=i.props.onDoubleClick;P()(e)&&e(i.getMouseInfo(t),t)}),sZ(i,"handleContextMenu",function(t){var e=i.props.onContextMenu;P()(e)&&e(i.getMouseInfo(t),t)}),sZ(i,"triggerSyncEvent",function(t){void 0!==i.props.syncId&&uZ.emit(uq,i.props.syncId,t,i.eventEmitterSymbol)}),sZ(i,"applySyncEvent",function(t){var e=i.props,r=e.layout,n=e.syncMethod,o=i.state.updateId,a=t.dataStartIndex,c=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)i.setState(sz({dataStartIndex:a,dataEndIndex:c},h({props:i.props,dataStartIndex:a,dataEndIndex:c,updateId:o},i.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,s=t.chartY,l=t.activeTooltipIndex,f=i.state,p=f.offset,d=f.tooltipTicks;if(!p)return;if("function"==typeof n)l=n(d,t);else if("value"===n){l=-1;for(var y=0;y<d.length;y++)if(d[y].value===t.activeLabel){l=y;break}}var v=sz(sz({},p),{},{x:p.left,y:p.top}),m=Math.min(u,v.x+v.width),b=Math.min(s,v.y+v.height),g=d[l]&&d[l].value,x=sH(i.state,i.props.data,l),O=d[l]?{x:"horizontal"===r?d[l].coordinate:m,y:"horizontal"===r?b:d[l].coordinate}:sF;i.setState(sz(sz({},t),{},{activeLabel:g,activeCoordinate:O,activePayload:x,activeTooltipIndex:l}))}else i.setState(t)}),sZ(i,"renderCursor",function(t){var r,n=i.state,o=n.isTooltipActive,a=n.activeCoordinate,u=n.activePayload,s=n.offset,l=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=i.getTooltipEventType(),d=null!==(r=t.props.active)&&void 0!==r?r:o,h=i.props.layout,y=t.key||"_recharts-cursor";return c().createElement(sP,{key:y,activeCoordinate:a,activePayload:u,activeTooltipIndex:l,chartName:e,element:t,isActive:d,layout:h,offset:s,tooltipAxisBandSize:f,tooltipEventType:p})}),sZ(i,"renderPolarAxis",function(t,e,r){var n=_()(t,"type.axisType"),o=_()(i.state,"".concat(n,"Map")),c=t.type.defaultProps,u=void 0!==c?sz(sz({},c),t.props):t.props,s=o&&o[u["".concat(n,"Id")]];return(0,a.cloneElement)(t,sz(sz({},s),{},{className:(0,N.Z)(n,s.className),key:t.key||"".concat(e,"-").concat(r),ticks:iD(s,!0)}))}),sZ(i,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,o=e.polarRadius,c=i.state,u=c.radiusAxisMap,s=c.angleAxisMap,l=J(u),f=J(s),p=f.cx,d=f.cy,h=f.innerRadius,y=f.outerRadius;return(0,a.cloneElement)(t,{polarAngles:Array.isArray(n)?n:iD(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(o)?o:iD(l,!0).map(function(t){return t.coordinate}),cx:p,cy:d,innerRadius:h,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),sZ(i,"renderLegend",function(){var t=i.state.formattedGraphicalItems,e=i.props,r=e.children,n=e.width,o=e.height,c=i.props.margin||{},u=im({children:r,formattedGraphicalItems:t,legendWidth:n-(c.left||0)-(c.right||0),legendContent:l});if(!u)return null;var s=u.item,f=sM(u,sA);return(0,a.cloneElement)(s,sz(sz({},f),{},{chartWidth:n,chartHeight:o,margin:c,onBBoxUpdate:i.handleLegendBBoxUpdate}))}),sZ(i,"renderTooltip",function(){var t,e=i.props,r=e.children,n=e.accessibilityLayer,o=tx(r,ei);if(!o)return null;var c=i.state,u=c.isTooltipActive,s=c.activeCoordinate,l=c.activePayload,f=c.activeLabel,p=c.offset,d=null!==(t=o.props.active)&&void 0!==t?t:u;return(0,a.cloneElement)(o,{viewBox:sz(sz({},p),{},{x:p.left,y:p.top}),active:d,label:f,payload:d?l:[],coordinate:s,accessibilityLayer:n})}),sZ(i,"renderBrush",function(t){var e=i.props,r=e.margin,n=e.data,o=i.state,c=o.offset,u=o.dataStartIndex,s=o.dataEndIndex,l=o.updateId;return(0,a.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:iR(i.handleBrushChange,t.props.onChange),data:n,x:$(t.props.x)?t.props.x:c.left,y:$(t.props.y)?t.props.y:c.top+c.height+c.brushBottom-(r.bottom||0),width:$(t.props.width)?t.props.width:c.width,startIndex:u,endIndex:s,updateId:"brush-".concat(l)})}),sZ(i,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=i.clipPathId,o=i.state,c=o.xAxisMap,u=o.yAxisMap,s=o.offset,l=t.type.defaultProps||{},f=t.props,p=f.xAxisId,d=void 0===p?l.xAxisId:p,h=f.yAxisId,y=void 0===h?l.yAxisId:h;return(0,a.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:c[d],yAxis:u[y],viewBox:{x:s.left,y:s.top,width:s.width,height:s.height},clipPathId:n})}),sZ(i,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=e.props.key,s=void 0!==e.item.type.defaultProps?sz(sz({},e.item.type.defaultProps),e.item.props):e.item.props,l=s.activeDot,f=sz(sz({index:i,dataKey:s.dataKey,cx:r.x,cy:r.y,r:4,fill:iE(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},tS(l,!1)),ts(l));return c.push(n.renderActiveDot(l,f,"".concat(u,"-activePoint-").concat(i))),o?c.push(n.renderActiveDot(l,sz(sz({},f),{},{cx:o.x,cy:o.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),sZ(i,"renderGraphicChild",function(t,e,r){var n=i.filterFormatItem(t,e,r);if(!n)return null;var o=i.getTooltipEventType(),c=i.state,u=c.isTooltipActive,s=c.tooltipAxis,l=c.activeTooltipIndex,f=c.activeLabel,p=tx(i.props.children,ei),d=n.props,h=d.points,y=d.isRange,v=d.baseLine,m=void 0!==n.item.type.defaultProps?sz(sz({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,O=m.activeShape,w={};"axis"!==o&&p&&"click"===p.props.trigger?w={onClick:iR(i.handleItemMouseEnter,t.props.onClick)}:"axis"!==o&&(w={onMouseLeave:iR(i.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:iR(i.handleItemMouseEnter,t.props.onMouseEnter)});var S=(0,a.cloneElement)(t,sz(sz({},n.props),w));if(!g&&u&&p&&(b||x||O)){if(l>=0){if(s.dataKey&&!s.allowDuplicatedCategory){var P="function"==typeof s.dataKey?function(t){return"function"==typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());E=te(h,P,f),k=y&&v&&te(v,P,f)}else E=null==h?void 0:h[l],k=y&&v&&v[l];if(O||x){var A=void 0!==t.props.activeIndex?t.props.activeIndex:l;return[(0,a.cloneElement)(t,sz(sz(sz({},n.props),w),{},{activeIndex:A})),null,null]}if(!j()(E))return[S].concat(sD(i.renderActivePoints({item:n,activePoint:E,basePoint:k,childIndex:l,isRange:y})))}else{var E,k,_,T=(null!==(_=i.getItemByXY(i.state.activeCoordinate))&&void 0!==_?_:{graphicalItem:S}).graphicalItem,M=T.item,C=void 0===M?t:M,I=T.childIndex,N=sz(sz(sz({},n.props),w),{},{activeIndex:I});return[(0,a.cloneElement)(C,N),null,null]}}return y?[S,null,null]:[S,null]}),sZ(i,"renderCustomized",function(t,e,r){return(0,a.cloneElement)(t,sz(sz({key:"recharts-customized-".concat(r)},i.props),i.state))}),sZ(i,"renderMap",{CartesianGrid:{handler:sX,once:!0},ReferenceArea:{handler:i.renderReferenceElement},ReferenceLine:{handler:sX},ReferenceDot:{handler:i.renderReferenceElement},XAxis:{handler:sX},YAxis:{handler:sX},Brush:{handler:i.renderBrush,once:!0},Bar:{handler:i.renderGraphicChild},Line:{handler:i.renderGraphicChild},Area:{handler:i.renderGraphicChild},Radar:{handler:i.renderGraphicChild},RadialBar:{handler:i.renderGraphicChild},Scatter:{handler:i.renderGraphicChild},Pie:{handler:i.renderGraphicChild},Funnel:{handler:i.renderGraphicChild},Tooltip:{handler:i.renderCursor,once:!0},PolarGrid:{handler:i.renderPolarGrid,once:!0},PolarAngleAxis:{handler:i.renderPolarAxis},PolarRadiusAxis:{handler:i.renderPolarAxis},Customized:{handler:i.renderCustomized}}),i.clipPathId="".concat(null!==(r=t.id)&&void 0!==r?r:K("recharts"),"-clip"),i.throttleTriggeredAfterMouseMove=I()(i.triggeredAfterMouseMove,null!==(o=t.throttleDelay)&&void 0!==o?o:1e3/60),i.state={},i}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&sN(t,e)}(n,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=tx(e,ei);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=sH(this.state,r,a,c),s=this.state.tooltipTicks[a].coordinate,l=(this.state.offset.top+n)/2,f="horizontal"===o?{x:s,y:l}:{y:s,x:l},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=sz(sz({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var d={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(d),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){tP([tx(t.children,ei)],[tx(this.props.children,ei)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=tx(this.props.children,ei);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return u.indexOf(e)>=0?e:o}return o}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,u=c.xAxisMap,s=c.yAxisMap,l=this.getTooltipEventType(),f=sK(this.state,this.props.data,this.props.layout,a);if("axis"!==l&&u&&s){var p=J(u).scale,d=J(s).scale,h=p&&p.invert?p.invert(o.chartX):null,y=d&&d.invert?d.invert(o.chartY):null;return sz(sz({},o),{},{xValue:h,yValue:y},f)}return f?sz(sz({},o),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,s=c.radiusAxisMap;return u&&s?ag({x:o,y:i},J(u)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=tx(t,ei),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),sz(sz({},ts(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){uZ.on(uq,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){uZ.removeListener(uq,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===ty(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,o=e.height,i=e.width;return c().createElement("defs",null,c().createElement("clipPath",{id:t},c().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=sT(e,2),n=r[0],o=r[1];return sz(sz({},t),{},sZ({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=sT(e,2),n=r[0],o=r[1];return sz(sz({},t),{},sZ({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,s=void 0!==u.type.defaultProps?sz(sz({},u.type.defaultProps),u.props):u.props,l=ty(u.type);if("Bar"===l){var f=(c.data||[]).find(function(e){return no(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===l){var p=(c.data||[]).find(function(e){return ag(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(cd(a,n)||ch(a,n)||cy(a,n)){var d=function(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,c=(cd(i,o)?e="trapezoids":ch(i,o)?e="sectors":cy(i,o)&&(e="points"),e),u=cd(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:ch(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:cy(i,o)?o.payload:{},s=a.filter(function(t,e){var r=og()(u,t),n=i.props[c].filter(function(t){var e;return(cd(i,o)?e=cv:ch(i,o)?e=cm:cy(i,o)&&(e=cb),e)(t,o)}),a=i.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(s[s.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:s.data}),h=void 0===s.activeIndex?d:s.activeIndex;return{graphicalItem:sz(sz({},a),{},{childIndex:h}),payload:cy(a,n)?s.data[d]:a.props.data[d]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!tO(this))return null;var n=this.props,o=n.children,i=n.className,a=n.width,u=n.height,s=n.style,l=n.compact,f=n.title,p=n.desc,d=tS(sM(n,sE),!1);if(l)return c().createElement(uc,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},c().createElement(tM,s_({},d,{width:a,height:u,title:f,desc:p}),this.renderClipPath(),tE(o,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(t=this.props.tabIndex)&&void 0!==t?t:0,d.role=null!==(e=this.props.role)&&void 0!==e?e:"application",d.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},d.onFocus=function(){r.accessibilityManager.focus()});var h=this.parseEventsOfWrapper();return c().createElement(uc,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},c().createElement("div",s_({className:(0,N.Z)("recharts-wrapper",i),style:sz({position:"relative",cursor:"default",width:a,height:u},s)},h,{ref:function(t){r.container=t}}),c().createElement(tM,s_({},d,{width:a,height:u,title:f,desc:p,style:sU}),this.renderClipPath(),tE(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,sq(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a.Component);sZ(y,"displayName",e),sZ(y,"defaultProps",sz({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),sZ(y,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,c=t.layout,u=t.stackOffset,s=t.margin,l=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=s1(t);return sz(sz(sz({},p),{},{updateId:0},h(sz(sz({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:s,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!tn(s,e.prevMargin)){var d=s1(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=sz(sz({},sK(e,n,c)),{},{updateId:e.updateId+1}),m=sz(sz(sz({},d),y),v);return sz(sz(sz({},m),h(sz({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:s,prevChildren:o})}if(!tP(o,e.prevChildren)){var b,g,x,O,w=tx(o,al),S=w&&null!==(b=null===(g=w.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:l,P=w&&null!==(x=null===(O=w.props)||void 0===O?void 0:O.endIndex)&&void 0!==x?x:f,A=j()(n)||S!==l||P!==f?e.updateId+1:e.updateId;return sz(sz({updateId:A},h(sz(sz({props:t},e),{},{updateId:A,dataStartIndex:S,dataEndIndex:P}),e)),{},{prevChildren:o,dataStartIndex:S,dataEndIndex:P})}return null}),sZ(y,"renderActiveDot",function(t,e,r){var n;return n=(0,a.isValidElement)(t)?(0,a.cloneElement)(t,e):P()(t)?t(e):c().createElement(eQ,e),c().createElement(tN,{className:"recharts-active-dot",key:r},n)});var v=(0,a.forwardRef)(function(t,e){return c().createElement(y,s_({},t,{ref:e}))});return v.displayName=y.displayName,v}({chartName:"BarChart",GraphicalChild:cL,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:lw},{axisType:"yAxis",AxisComp:lM}],formatAxisMap:function(t,e,r,n,o){var i=t.width,a=t.height,c=t.layout,u=t.children,s=Object.keys(e),l={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!tx(u,cL);return s.reduce(function(i,a){var u,s,p,d,h,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,O=y.reversed,w="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,P=y.categoricalDomain.sort(tr);if(P.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(P[e-1]||0),S))}),Number.isFinite(S)){var A=S/j,E="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=A*E/2),"no-gap"===y.padding){var k=Y(t.barCategoryGap,A*E),_=A*E/2;u=_-k-(_-k)/E*k}}}s="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:y.range,O&&(s=[s[1],s[0]]);var T=iL(y,o,f),M=T.scale,C=T.realScaleType;M.domain(m).range(s),iz(M);var I=iX(M,cW(cW({},y),{},{realScaleType:C}));"xAxis"===n?(h="top"===v&&!x||"bottom"===v&&x,p=r.left,d=l[w]-h*y.height):"yAxis"===n&&(h="left"===v&&!x||"right"===v&&x,p=l[w]-h*y.width,d=r.top);var N=cW(cW(cW({},y),I),{},{realScaleType:C,x:p,y:d,scale:M,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return N.bandSize=iQ(N,I),y.hide||"xAxis"!==n?y.hide||(l[w]+=(h?-1:1)*N.width):l[w]+=(h?-1:1)*N.height,cW(cW({},i),{},cU({},a,N))},{})}}),lI=r(99063),lN=r(53913),lD=r(918),lB=r(56390),lR=r(37841),lL=r(70580),lz=r(23743),lZ=r(88441);let lq=()=>{let[t,e]=(0,a.useState)([]),[r,n]=(0,a.useState)("all"),[c,u]=(0,a.useState)("lastMonth"),s=(0,lz.Z)(),{t:l}=(0,i.$G)(),f=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],p=(0,lZ.Z)(s.breakpoints.down("sm")),d=(0,lZ.Z)(s.breakpoints.between("sm","md"));return(0,a.useEffect)(()=>{(async()=>{try{let t=(await lL.xk.get("/opportunities/stats",{params:{status:"all"!==r?r:void 0,timeFrame:c,barChart:!0}})).data.map(t=>({date:`${f[t.month-1]} ${t.year}`,applications:t.applications}));e(t)}catch(t){console.error("Error fetching application stats:",t)}})()},[r,c]),(0,o.jsxs)(lI.default,{sx:{width:"100%",maxWidth:450,padding:1.8,borderRadius:2},children:[(0,o.jsxs)(lI.default,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[o.jsx("p",{className:"title-Applications-stat",children:l("HomeDashboard:Applications")}),(0,o.jsxs)(lN.Z,{size:"small",sx:{minWidth:40},children:[o.jsx(lD.Z,{children:"Status"}),(0,o.jsxs)(lB.Z,{value:r,onChange:t=>n(t.target.value),label:"Status",children:[o.jsx(lR.Z,{value:"all",children:"Submitted"}),o.jsx(lR.Z,{value:"Accepted",children:"Accepted"}),o.jsx(lR.Z,{value:"Rejected",children:"Rejected"})]})]}),(0,o.jsxs)(lN.Z,{size:"small",sx:{minWidth:40},children:[o.jsx(lD.Z,{children:"Time "}),(0,o.jsxs)(lB.Z,{value:c,onChange:t=>u(t.target.value),label:"Time Frame",children:[o.jsx(lR.Z,{value:"lastMonth",children:"Last Month"}),o.jsx(lR.Z,{value:"last3Months",children:"Last 3 Months"}),o.jsx(lR.Z,{value:"last6Months",children:"Last 6 Months"})]})]})]}),(0,o.jsxs)(lC,{width:p?200:d?150:450,height:200,data:t,barSize:13,margin:{top:40,bottom:20},children:[o.jsx(lw,{dataKey:"date",tickLine:!1,axisLine:!1}),o.jsx(lM,{tickCount:t?.sort((t,e)=>e.applications-t.applications)[0]?.applications+1||5,tickLine:!1,axisLine:!1}),o.jsx(ei,{}),o.jsx(cL,{dataKey:"applications",fill:(t=>{switch(t){case"Accepted":return"#009966";case"Rejected":return"#cc3233";default:return"#234791"}})(r),radius:[10,10,10,10]})]})]})};var lW=r(25609),lU=r(52188),lF=r(22304),lX=r(83969),lG=r(57201),l$=r(28236),lV=r(90397),lH=r(31190);let lK=()=>{let{t,i18n:e}=(0,i.$G)(),{user:r}=(0,lF.Z)(),[n,c]=(0,a.useState)([]),[u,s]=(0,a.useState)(!1),[f,p]=(0,a.useState)(!1),[d,h]=(0,a.useState)(2),[y,v]=(0,a.useState)(!1),m=async()=>{s(!0);try{let t=await lL.xk.get("/favourite",{params:{pageSize:d}});c(t.data.favourites)}catch(t){}finally{s(!1)}};(0,a.useEffect)(()=>{m()},[d,r]);let b=t=>{p(t),v(!0)},O=async t=>{if(!t){console.error("Invalid opportunity ID");return}try{await lL.xk.delete(`/favourite/${t}`,{data:{type:"opportunity"}})}catch(t){t.response&&404===t.response.status?console.error("Article already deleted or not found"):console.error("Error while deleting article from favourites",t)}},w=async()=>{try{await O(f),lH.Am.success("Opportunity deleted successfully"),m()}catch(t){}v(!1)};return(0,o.jsxs)("div",{className:"favorite-opportunities",children:[(0,o.jsxs)("div",{className:"header",children:[o.jsx("p",{variant:"h4",className:"heading-h3 semi-bold",children:t("HomeDashboard:SaveOpportunities")}),o.jsx(g.default,{link:`/${x.pf.baseURL.route}/${x.D1.favoris.route}`,className:"btn btn-ghost",text:t("HomeDashboard:All"),icon:o.jsx(lX.default,{})})]}),u?o.jsx(lW.default,{children:"Loading..."}):n?.length>0?o.jsx(l.default,{container:!0,spacing:2,className:"opportunity-list",children:n.map(r=>(0,o.jsxs)(l.default,{item:!0,container:!0,alignItems:"center",className:"opportunity-item opportunity-item-homePage",children:[o.jsx("div",{className:"opportunity-left",children:o.jsx(lU.default,{href:`/${x.Bi.jobLocation.route}/${r?.country.toLowerCase()}`,children:o.jsx("img",{width:180,height:140,src:(0,l$.xd)(r.country),className:"map-home-page  ",alt:"Live from space album cover",loading:"lazy"})})}),(0,o.jsxs)("div",{className:"opportunity-center",children:[o.jsx(g.default,{text:r.versions?.[e.language]?.title,className:"btn p-0 job-title article-title",link:`/${x.Bi.opportunities.route}/${r.versions?.[e.language]?.url}`}),(0,o.jsxs)(lW.default,{className:"opportunity-reference",children:["Ref: ",r.reference]})]}),(0,o.jsxs)("div",{className:"opportunity-right",children:[o.jsx(g.default,{icon:o.jsx(lG.Z,{}),onClick:()=>b(r._id),className:"btn btn-ghost bookmark"}),o.jsx(g.default,{className:"btn  btn-homepage btn-filled",text:t("HomeDashboard:ReadMore"),link:`/${x.Bi.opportunities.route}/${r.versions[e.language]?.url}`})]})]},r._id))}):(0,o.jsxs)("div",{className:"  opportunity-item  opportunity-item-homepage empty-state",children:[o.jsx("p",{className:"empty-title-1",children:"You haven't saved any job opportunities yet."}),o.jsx("span",{className:"empty-title-2",children:"Browse available jobs to find and save the ones that match your interests!"}),o.jsx("div",{className:"empty-button",children:o.jsx(g.default,{className:"btn  btn-homepage btn-filled",text:t("Explore"),link:`/${x.Bi.opportunities.route}`})})]}),o.jsx(lV.Z,{open:y,message:t("messages:supprimerapplicationfavoris"),onClose:()=>{v(!1)},onConfirm:w})]})},lY=()=>{let{t}=(0,i.$G)(),{user:e}=(0,lF.Z)(),[r,n]=(0,a.useState)([]),[c,u]=(0,a.useState)(2),[s,f]=(0,a.useState)(!1),[p,d]=(0,a.useState)(!1),[h,y]=(0,a.useState)(!1),v=async()=>{f(!0);try{let t=await lL.xk.get("/favourite",{params:{pageSize:c,typeOfFavourite:"article"}});n(t.data.favourites)}catch(t){}finally{f(!1)}};(0,a.useEffect)(()=>{v()},[c,e]);let m=t=>{y(t),d(!0)},b=async t=>{if(!t){console.error("Invalid opportunity ID");return}try{await lL.xk.delete(`/favourite/${t}`,{data:{type:"article"}}),n(e=>e.filter(e=>e._id!==t))}catch(t){t.response&&404===t.response.status?console.error("Article already deleted or not found"):console.error("Error while deleting article from favourites",t)}},O=async()=>{try{await b(h),lH.Am.success("Article deleted successfully"),v()}catch(t){console.error("Error during article deletion",t)}finally{d(!1)}};return o.jsx(o.Fragment,{children:(0,o.jsxs)("div",{className:"favorite-opportunities",children:[(0,o.jsxs)("div",{className:"header",children:[o.jsx("p",{variant:"h4",className:"heading-h3 semi-bold",children:t("HomeDashboard:savearticle")}),o.jsx(g.default,{link:`/${x.pf.baseURL.route}/${x.D1.favoris.route}`,className:"btn btn-ghost",text:t("HomeDashboard:All"),icon:o.jsx(lX.default,{})})]}),s?o.jsx(lW.default,{children:"Loading..."}):r?.length>0?o.jsx(l.default,{container:!0,spacing:2,className:"opportunity-list",children:r?.map(e=>o.jsxs(l.default,{item:!0,container:!0,alignItems:"center",className:" opportunity-item opportunity-item-homePage",children:[o.jsx("div",{className:"opportunity-center",children:o.jsx(g.default,{text:e.versions[0].title,className:"btn p-0 job-title article-title",link:`/${x.Bi.blog.route}/${e.versions[0].url}`})}),o.jsxs("div",{className:"opportunity-right",children:[o.jsx(g.default,{icon:o.jsx(lG.Z,{}),onClick:()=>m(e._id),className:"btn btn-ghost bookmark"}),o.jsx(g.default,{className:"btn btn-homepage btn-filled",text:t("HomeDashboard:ReadMore"),link:`/${x.Bi.blog.route}/${e.versions[0].url}`})]})]},e._id))}):(0,o.jsxs)("div",{className:" opportunity-item empty-state",children:[o.jsx("p",{className:"empty-title-1",children:"Your list of favorite articles is empty."}),o.jsx("span",{className:"empty-title-2",children:"Explore our blog for insights and resources that can help you stay informed and inspired!"}),o.jsx("div",{className:"empty-button",children:o.jsx(g.default,{className:"btn btn-homepage btn-filled",text:t("Explore"),link:`/${x.Bi.blog.route}`})})]}),o.jsx(lV.Z,{open:p,message:t("messages:supprimerarticlefavoris"),onClose:()=>{d(!1)},onConfirm:O})]})})};var lJ=r(20026),lQ=r(72909),l0=r(71542);let l1={src:"/_next/static/media/icon-eye.f76f08bd.png"},l2={src:"/_next/static/media/profileviews.8e5863fd.png"},l3=function({applicationData:t}){let{t:e}=(0,i.$G)();return(0,o.jsxs)("div",{className:"progress-container",children:[(0,o.jsxs)("p",{className:"profile-title",children:[" ",e("HomeDashboard:profileviews")]}),(0,o.jsxs)("div",{className:"gauge-container",children:[o.jsx("img",{src:l2.src,alt:"Profile View Icon",className:"profile-background",loading:"lazy"}),(0,o.jsxs)("div",{className:"view-info",children:[o.jsx("div",{className:"view-icon",children:o.jsx("img",{src:l1.src,className:"user-icon-progress",alt:"User Icon",loading:"lazy"})}),o.jsx("span",{className:"view-percentage",children:t})]})]}),o.jsx("div",{className:"upgrade-button",children:o.jsx(g.default,{className:"btn btn-homepage btn-filled",text:e("HomeDashboard:Details"),link:`/${x.pf.baseURL.route}/my-applications`})})]})},l6=()=>{let{t}=(0,i.$G)(),{data:e}=(0,lJ.iQ)(t),[r,n]=(0,a.useState)(""),[c,p]=(0,a.useState)(null),[d,h]=(0,a.useState)({pageSize:5,page:0}),y=(0,lQ.t)({pageSize:d.pageSize,pageNumber:d.page+1,searchQuery:r});(0,a.useEffect)(()=>{y.refetch()&&p(y.data)},[y.data,d,r]);let v=(0,u.ZP)(s.Z)(({theme:t})=>({backgroundColor:"#fff",...t.typography.body2,padding:t.spacing(1),textAlign:"center",color:t.palette.text.secondary}));return(0,o.jsxs)(o.Fragment,{children:[o.jsx(l0.Z,{accepted:c?.totalAccepted,rejected:c?.totalRejected,pending:c?.totalPending,totalApplications:c?.candidateNumberOfApp}),(0,o.jsxs)(l.default,{container:!0,spacing:2,children:[o.jsx(l.default,{item:!0,xs:12,sm:4,md:3,children:o.jsx(v,{className:"list-Home-page",sx:{height:"100% ",padding:"1px"},children:o.jsx(O,{percentage:Math.round(e?.candidate.profilePercentage||0),maxPercentage:91})})}),o.jsx(l.default,{item:!0,xs:12,sm:4,md:6,children:o.jsx(v,{className:"list-Home-page stat",sx:{height:"100% ",padding:"1px"},children:o.jsx(lq,{})})}),o.jsx(l.default,{item:!0,xs:12,sm:4,md:3,children:o.jsx(v,{className:"list-Home-page",sx:{height:"100%",padding:"1px"},children:o.jsx(l3,{applicationData:c?.candidateNumberOfApp+1})})}),o.jsx(l.default,{item:!0,xs:12,sm:6,md:6,children:o.jsx(f.Z,{className:"list-Home-page list-saved-article",sx:{height:"100%",padding:"16px"},children:o.jsx(lK,{})})}),o.jsx(l.default,{item:!0,xs:12,sm:6,md:6,children:o.jsx(f.Z,{className:"list-Home-page",sx:{height:"100%",padding:"16px"},children:o.jsx(lY,{})})})]})]})},l5=({locale:t})=>{let{t:e}=(0,i.$G)(),{data:r}=(0,lJ.iQ)(e);return(0,o.jsxs)(o.Fragment,{children:[o.jsx("div",{children:(0,o.jsxs)("p",{className:"heading-h2 semi-bold",children:["Hello ",r?.firstName," \uD83D\uDC4B\uD83C\uDFFC, "]})}),o.jsx(l6,{})]})}},71542:(t,e,r)=>{"use strict";r.d(e,{Z:()=>s});var n=r(10326),o=r(52210),i=r(63416);let a={src:"/_next/static/media/rejectedicon.48eae2f6.png"},c={src:"/_next/static/media/pendingIcon.7e7dfdfb.png"};var u=r(59245);r(29565);let s=({accepted:t,pending:e,rejected:r,totalApplications:s,profileViews:l})=>{let{t:f}=(0,o.$G)();return(0,n.jsxs)("div",{className:"stats-container",children:[n.jsx("div",{className:"stats-card stats-job",children:(0,n.jsxs)("div",{className:"stats-content",children:[n.jsx("div",{className:"stats-icon-wrapper",children:n.jsx("img",{src:i.Z.src,alt:"job",className:"stats-icon"})}),(0,n.jsxs)("div",{className:"stats-info",children:[n.jsx("span",{className:"stats-title",children:f("statisticsApp:myApplications")}),n.jsx("span",{className:"stats-value",children:s})]})]})}),n.jsx("div",{className:"stats-card stats-active",children:(0,n.jsxs)("div",{className:"stats-content",children:[n.jsx("div",{className:"stats-icon-wrapper",children:n.jsx("img",{src:u.Z.src,alt:"expired",className:"stats-icon2"})}),(0,n.jsxs)("div",{className:"stats-info",children:[n.jsx("span",{className:"stats-title",children:f("statisticsApp:accepted")}),n.jsx("span",{className:"stats-value",children:t})]})]})}),n.jsx("div",{className:"stats-card stats-expired",children:(0,n.jsxs)("div",{className:"stats-content",children:[n.jsx("div",{className:"stats-icon-wrapper",children:n.jsx("img",{src:a.src,alt:"expired",className:"stats-icon-rejected"})}),(0,n.jsxs)("div",{className:"stats-info",children:[n.jsx("span",{className:"stats-title",children:f("statisticsApp:rejected")}),n.jsx("span",{className:"stats-value",children:r})]})]})}),n.jsx("div",{className:"stats-card stats-views",children:(0,n.jsxs)("div",{className:"stats-content",children:[n.jsx("div",{className:"stats-icon-wrapper",children:n.jsx("img",{src:c.src,alt:"profile views",className:"stats-icon1"})}),(0,n.jsxs)("div",{className:"stats-info",children:[n.jsx("span",{className:"stats-title",children:f("statisticsApp:pending")}),n.jsx("span",{className:"stats-value",children:e})]})]})})]})}},53361:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});let n=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\app\[locale]\(dashboard)\dashboard\home\page.jsx#default`)},59245:(t,e,r)=>{"use strict";r.d(e,{Z:()=>n});let n={src:"/_next/static/media/activedashboard.f50c7817.png",height:15,width:22,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAAG1BMVEX///9MaXH////////////////////////////wbDDDAAAACXRSTlMCAISOci/EGz0Zyr2+AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAJklEQVR4nB3FwREAIAzDMDu0hf0n5kAfgYopFMySav+c2W+xJ6IXBmIAUr577aAAAAAASUVORK5CYII=",blurWidth:8,blurHeight:5}},29565:()=>{},94230:()=>{}};var e=require("../../../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[8948,1479,1619,1336,4227,6027,4289,5321,1812,3969,8463],()=>r(79336));module.exports=n})();