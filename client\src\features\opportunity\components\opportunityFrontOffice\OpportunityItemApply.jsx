"use client";
import { Grid } from "@mui/material";
import { useTheme, useMediaQuery } from "@mui/material";
import { useTranslation } from "react-i18next";

import SvgTime from "@/assets/images/icons/time.svg";
import SvgFileText from "@/assets/images/icons/FileText.svg";
import {
  findIndustryClassname,
  findIndustryColoredIcon,
  findIndustryLabel,
  formatDate,
  industryExists,
  truncateByCharacter,
} from "@/utils/functions";
import countries from "i18n-iso-countries";
import enLocale from "i18n-iso-countries/langs/en.json";

function OpportunityItemApply({ opportunity, language }) {
  const theme = useTheme();
  countries.registerLocale(enLocale);

  const { t } = useTranslation();

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Grid
      key={opportunity?._id}
      className="container-opportunity-item opportunity-item"
      container
      spacing={0}
    >
      <Grid item lg={3} md={6} sm={12}>
        <p className="heading-h1 text-blue">{t("global:applyForThisJob")}</p>
      </Grid>
      <Grid container item lg={9} md={6} sm={12}>
        <Grid
          item
          lg={4}
          md={4}
          sm={3}
          className="item-image opportunity-apply"
        >
          <img
            width={100}
            height={70}
            src={`https://flagcdn.com/w320/${countries
              .getAlpha2Code(opportunity?.country.toLowerCase(), "en")
              .toLowerCase()}.png`}
            className="map-img"
            alt={`${
              opportunity?.country
                ? `Map of ${opportunity?.country}`
                : `Country map`
            }`}
            loading="lazy"
          />
        </Grid>
        <Grid
          item
          lg={8}
          md={8}
          sm={6}
          className="item-content opportunity-apply"
        >
          <div className="flex row">
            <div className="flex mobile-col">
              <p className="job-title">
                {truncateByCharacter(
                  opportunity?.versions[language]?.title,
                  isMobile ? 30 : 50
                )}
              </p>
              {industryExists(opportunity?.industry) ? (
                <p
                  className={`job-industry border ${findIndustryClassname(
                    opportunity?.industry
                  )}`}
                >
                  {findIndustryColoredIcon(opportunity?.industry)}{" "}
                  {findIndustryLabel(opportunity?.industry)}
                </p>
              ) : null}
            </div>
          </div>
          <div className="flex">
            <p className="job-ref">Ref: {opportunity?.reference}</p>
          </div>
          <div className="flex">
            <p className="job-contract-apply">
              <SvgFileText />
              {opportunity?.contractType || "Agreement"}
            </p>
            <p className="job-time-apply">
              <SvgTime />
              {formatDate(opportunity?.versions[language]?.createdAt)}
            </p>
          </div>
        </Grid>
      </Grid>
    </Grid>
  );
}

export default OpportunityItemApply;
