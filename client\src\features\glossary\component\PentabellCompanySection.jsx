import PentabellOfficesIcon from "@/assets/images/website/PentabellOfficesIcon.svg";
import { Button } from "@mui/material";

export default function PentabellCompanySection({ language }) {
  const handleContactUsClick = () => {
    window.open(
      language === "en"
        ? "https://www.pentabell.com/contact/"
        : `https://www.pentabell.com/${language}/contact/`,
      "_blank"
    );
  };

  return (
    <div className="pentabell-company">
      <div className="content">
        <p className="title">Pentabell Company</p>
        <p className="description">
          Absenteeism is the persistent absence of individuals from work,
          usually without a valid or authorized reason. <br></br>In most
          instances, employee absenteeism is characterized by repeated and
          intentional failure to fulfill obligations which can lead to
          decreased.
        </p>
        <Button
          className="btn btn-filled-yellow"
          onClick={handleContactUsClick}
        >
          Contact Us
        </Button>
      </div>
      <div className="pentabell-offices-icon">
        <PentabellOfficesIcon />
      </div>
    </div>
  );
}
