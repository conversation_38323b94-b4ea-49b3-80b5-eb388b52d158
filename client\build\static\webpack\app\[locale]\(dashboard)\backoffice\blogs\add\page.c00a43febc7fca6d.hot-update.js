"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./node_modules/@mui/icons-material/esm/CloudUpload.js":
/*!*************************************************************!*\
  !*** ./node_modules/@mui/icons-material/esm/CloudUpload.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _utils_createSvgIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/createSvgIcon.js */ \"(app-pages-browser)/./node_modules/@mui/material/utils/createSvgIcon.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/* harmony default export */ __webpack_exports__[\"default\"] = ((0,_utils_createSvgIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n    d: \"M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96M14 13v4h-4v-4H7l5-5 5 5z\"\n}), \"CloudUpload\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AbXVpL2ljb25zLW1hdGVyaWFsL2VzbS9DbG91ZFVwbG9hZC5qcyIsIm1hcHBpbmdzIjoiOzs7NkRBRXFEO0FBQ0w7QUFDaEQsK0RBQWVBLG1FQUFhQSxDQUFDLFdBQVcsR0FBRUUsc0RBQUlBLENBQUMsUUFBUTtJQUNyREMsR0FBRztBQUNMLElBQUksZ0JBQWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BtdWkvaWNvbnMtbWF0ZXJpYWwvZXNtL0Nsb3VkVXBsb2FkLmpzP2IzMGQiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBjcmVhdGVTdmdJY29uIGZyb20gXCIuL3V0aWxzL2NyZWF0ZVN2Z0ljb24uanNcIjtcbmltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5leHBvcnQgZGVmYXVsdCBjcmVhdGVTdmdJY29uKC8qI19fUFVSRV9fKi9fanN4KFwicGF0aFwiLCB7XG4gIGQ6IFwiTTE5LjM1IDEwLjA0QzE4LjY3IDYuNTkgMTUuNjQgNCAxMiA0IDkuMTEgNCA2LjYgNS42NCA1LjM1IDguMDQgMi4zNCA4LjM2IDAgMTAuOTEgMCAxNGMwIDMuMzEgMi42OSA2IDYgNmgxM2MyLjc2IDAgNS0yLjI0IDUtNSAwLTIuNjQtMi4wNS00Ljc4LTQuNjUtNC45Nk0xNCAxM3Y0aC00di00SDdsNS01IDUgNXpcIlxufSksICdDbG91ZFVwbG9hZCcpOyJdLCJuYW1lcyI6WyJjcmVhdGVTdmdJY29uIiwianN4IiwiX2pzeCIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@mui/icons-material/esm/CloudUpload.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,CloudUpload,Description,PictureAsPdf,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/PictureAsPdf.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,CloudUpload,Description,PictureAsPdf,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,CloudUpload,Description,PictureAsPdf,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CloudUpload.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,CloudUpload,Description,PictureAsPdf,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,CloudUpload,Description,PictureAsPdf,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewOpen, setPreviewOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [extractedData, setExtractedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Supported file types\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes(\"pdf\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            color: \"error\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 52,\n            columnNumber: 42\n        }, undefined);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            color: \"primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 53,\n            columnNumber: 12\n        }, undefined);\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        // Create a temporary DOM element to parse HTML\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        // Extract potential title (first h1, h2, or strong text)\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        // Extract first paragraph as potential description\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        // Extract keywords from headings and strong text\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords: keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement(function(image) {\n                        return image.read(\"base64\").then(function(imageBuffer) {\n                            return {\n                                src: \"data:\" + image.contentType + \";base64,\" + imageBuffer\n                            };\n                        });\n                    })\n                }\n            });\n            setProgress(75);\n            // Clean up the HTML content\n            let cleanContent = result.value.replace(/<p><\\/p>/g, \"\") // Remove empty paragraphs\n            .replace(/\\s+/g, \" \") // Normalize whitespace\n            .trim();\n            // Extract metadata\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata: metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            // Convert plain text to basic HTML\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata: metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            setExtractedData(result);\n            setSuccess(true);\n            // Show preview dialog\n            setPreviewOpen(true);\n        } catch (err) {\n            console.error(\"File processing error:\", err);\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) {\n            processFile(acceptedFiles[0]);\n        }\n    }, []);\n    const { getRootProps, getInputProps, isDragActive, acceptedFiles } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing\n    });\n    const handleApplyContent = ()=>{\n        if (extractedData) {\n            onContentExtracted(extractedData.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(extractedData.metadata);\n            }\n            setPreviewOpen(false);\n            setExtractedData(null);\n        }\n    };\n    const handleClosePreview = ()=>{\n        setPreviewOpen(false);\n        setExtractedData(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        sx: {\n            mb: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: [\n                    t(\"createArticle:importDocument\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                ...getRootProps(),\n                sx: {\n                    p: 3,\n                    border: \"2px dashed\",\n                    borderColor: isDragActive ? \"primary.main\" : \"grey.300\",\n                    backgroundColor: isDragActive ? \"action.hover\" : \"background.paper\",\n                    cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\",\n                    textAlign: \"center\",\n                    transition: \"all 0.3s ease\",\n                    opacity: disabled || isProcessing ? 0.6 : 1,\n                    \"&:hover\": {\n                        borderColor: \"primary.main\",\n                        backgroundColor: \"action.hover\"\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ...getInputProps()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        sx: {\n                            fontSize: 48,\n                            color: \"primary.main\",\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:dragDropOrClick\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        sx: {\n                            mb: 2\n                        },\n                        children: [\n                            t(\"createArticle:supportedFormats\"),\n                            \": .docx, .doc, .txt\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            gap: 1,\n                            flexWrap: \"wrap\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 23\n                                }, void 0),\n                                label: \"Word (.docx)\",\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 23\n                                }, void 0),\n                                label: \"Word (.doc)\",\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 23\n                                }, void 0),\n                                label: \"Text (.txt)\",\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        gutterBottom: true,\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, undefined),\n            success && !previewOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                open: previewOpen,\n                onClose: handleClosePreview,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"h6\",\n                                    children: t(\"createArticle:previewExtractedContent\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    onClick: handleClosePreview,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        children: extractedData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: [\n                                extractedData.metadata.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        mb: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedTitle\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: extractedData.metadata.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 17\n                                }, undefined),\n                                extractedData.metadata.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        mb: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedDescription\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body2\",\n                                            children: extractedData.metadata.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"subtitle2\",\n                                    color: \"primary\",\n                                    sx: {\n                                        mb: 1\n                                    },\n                                    children: [\n                                        t(\"createArticle:extractedContent\"),\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        maxHeight: 400,\n                                        overflow: \"auto\",\n                                        border: \"1px solid\",\n                                        borderColor: \"grey.300\",\n                                        p: 2,\n                                        borderRadius: 1,\n                                        backgroundColor: \"grey.50\"\n                                    },\n                                    dangerouslySetInnerHTML: {\n                                        __html: extractedData.content\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, undefined),\n                                extractedData.warnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    severity: \"warning\",\n                                    sx: {\n                                        mt: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            children: [\n                                                t(\"createArticle:conversionWarnings\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: extractedData.warnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: warning.message\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                onClick: handleClosePreview,\n                                children: t(\"global:cancel\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                onClick: handleApplyContent,\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 24\n                                }, void 0),\n                                children: t(\"createArticle:applyContent\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"e7V5N1Q8+U90zRa31VhbBxq3qFc=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});