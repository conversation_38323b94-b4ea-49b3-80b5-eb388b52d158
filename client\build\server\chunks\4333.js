"use strict";exports.id=4333,exports.ids=[4333],exports.modules={76971:(e,a,l)=>{l.d(a,{Z:()=>z});var t=l(17577),s=l(41135),r=l(88634),n=l(92014),i=l(33662),c=l(27522),o=l(10326);let d=(0,c.Z)((0,o.jsx)("path",{d:"M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"}),"CheckBoxOutlineBlank"),m=(0,c.Z)((0,o.jsx)("path",{d:"M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"}),"CheckBox"),h=(0,c.Z)((0,o.jsx)("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10H7v-2h10v2z"}),"IndeterminateCheckBox");var p=l(54641),u=l(27080),x=l(71685),g=l(97898);function j(e){return(0,g.ZP)("MuiCheckbox",e)}let v=(0,x.Z)("MuiCheckbox",["root","checked","disabled","indeterminate","colorPrimary","colorSecondary","sizeSmall","sizeMedium"]);var A=l(91703),f=l(30990),b=l(40955),y=l(2791),N=l(7467),Z=l(31121);let C=e=>{let{classes:a,indeterminate:l,color:t,size:s}=e,n={root:["root",l&&"indeterminate",`color${(0,p.Z)(t)}`,`size${(0,p.Z)(s)}`]},i=(0,r.Z)(n,j,a);return{...a,...i}},w=(0,A.ZP)(i.Z,{shouldForwardProp:e=>(0,u.Z)(e)||"classes"===e,name:"MuiCheckbox",slot:"Root",overridesResolver:(e,a)=>{let{ownerState:l}=e;return[a.root,l.indeterminate&&a.indeterminate,a[`size${(0,p.Z)(l.size)}`],"default"!==l.color&&a[`color${(0,p.Z)(l.color)}`]]}})((0,f.Z)(({theme:e})=>({color:(e.vars||e).palette.text.secondary,variants:[{props:{color:"default",disableRipple:!1},style:{"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,n.Fq)(e.palette.action.active,e.palette.action.hoverOpacity)}}},...Object.entries(e.palette).filter((0,b.Z)()).map(([a])=>({props:{color:a,disableRipple:!1},style:{"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette[a].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,n.Fq)(e.palette[a].main,e.palette.action.hoverOpacity)}}})),...Object.entries(e.palette).filter((0,b.Z)()).map(([a])=>({props:{color:a},style:{[`&.${v.checked}, &.${v.indeterminate}`]:{color:(e.vars||e).palette[a].main},[`&.${v.disabled}`]:{color:(e.vars||e).palette.action.disabled}}})),{props:{disableRipple:!1},style:{"&:hover":{"@media (hover: none)":{backgroundColor:"transparent"}}}}]}))),S=(0,o.jsx)(m,{}),k=(0,o.jsx)(d,{}),T=(0,o.jsx)(h,{}),z=t.forwardRef(function(e,a){let l=(0,y.i)({props:e,name:"MuiCheckbox"}),{checkedIcon:r=S,color:n="primary",icon:i=k,indeterminate:c=!1,indeterminateIcon:d=T,inputProps:m,size:h="medium",disableRipple:p=!1,className:u,slots:x={},slotProps:g={},...j}=l,v=c?d:i,A=c?d:r,f={...l,disableRipple:p,color:n,indeterminate:c,size:h},b=C(f),z=g.input??m,[B,P]=(0,Z.Z)("root",{ref:a,elementType:w,className:(0,s.Z)(b.root,u),shouldForwardComponentProp:!0,externalForwardedProps:{slots:x,slotProps:g,...j},ownerState:f,additionalProps:{type:"checkbox",icon:t.cloneElement(v,{fontSize:v.props.fontSize??h}),checkedIcon:t.cloneElement(A,{fontSize:A.props.fontSize??h}),disableRipple:p,slots:x,slotProps:{input:(0,N.Z)("function"==typeof z?z(f):z,{"data-indeterminate":c})}}});return(0,o.jsx)(B,{...P,classes:b})})},11932:(e,a,l)=>{l.d(a,{default:()=>n});var t=l(10326);l(17577);var s=l(15082),r=l(90423);l(46226);let n=function({title:e,description:a,btnLink:l,btnText:n,img:i,imgAlt:c,light:o}){return t.jsx("div",{id:"cta-payroll",className:!0==o?"light":null,children:t.jsx(r.default,{className:"custom-max-width",children:t.jsx("div",{className:"div",children:(0,t.jsxs)("div",{id:"last-blog",style:{backgroundImage:`linear-gradient(to left, rgba(35, 71, 145, 0), rgba(35, 71, 145, 0.6)), url(${i})`,backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat",padding:"20px"},children:[c&&t.jsx("img",{width:0,height:0,alt:c,src:"",style:{display:"none"},loading:"lazy"}),t.jsx("p",{className:"title",children:e}),t.jsx("p",{className:"description",children:a&&a}),t.jsx(s.default,{text:n,className:"btn btn-outlined white",link:l})]})})})})}},70534:(e,a,l)=>{l.d(a,{default:()=>C});var t=l(10326),s=l(63568),r=l(90423),n=l(16027),i=l(87638),c=l(90943),o=l(78077),d=l(9861),m=l(84648),h=l(5394),p=l(76971),u=l(9252);l(11148);var x=l(10123),g=l(96672),j=l(15082),v=l(17577),A=l(52210),f=l(87419),b=l(4563),y=l(55618),N=l(5248),Z=l(5926);let C=function({country:e}){let[a,l]=(0,v.useState)(""),[C,w]=(0,v.useState)(!1),{t:S}=(0,A.$G)(),k=u.PhoneNumberUtil.getInstance(),T=(0,f.uu)(w,l),z=async(a,{resetForm:l})=>{let t={...Object.fromEntries(Object.entries(a).filter(([e,a])=>"acceptTerms"!==e&&""!==a&&null!=a))};await T.mutateAsync({countryName:e,...t,to:`${process.env.NEXT_PUBLIC_EMAIL_FORM_DESTINATION}`,team:"Digital",type:"countryContact"}),l(),setTimeout(()=>{w(!1)},3e3)},B=e=>{try{return k.isValidNumber(k.parseAndKeepRawInput(e))}catch(e){return!1}},P=x.Z_().test("is-valid-phone",S("validations:phoneFormat"),e=>B(e)),F=e=>(0,b.eo)(e).shape({phone:P});return t.jsx("div",{id:"service-page-form",children:(0,t.jsxs)(r.default,{className:"custom-max-width",children:[(0,t.jsxs)("h2",{className:"heading-h1 text-white text-center",children:[S("Tunisia:form:title1")," ",t.jsx("span",{className:"text-yellow",children:S("Tunisia:form:title2")})]}),t.jsx(s.J9,{initialValues:{fullName:"",email:"",phone:"",youAre:"",howToHelp:"",message:"",country:"",acceptTerms:!1},validationSchema:()=>F(S),onSubmit:z,children:({values:e,handleChange:a,setFieldValue:r,errors:u,touched:x})=>t.jsx(s.l0,{className:"pentabell-form",children:(0,t.jsxs)(n.default,{container:!0,rowSpacing:4,columnSpacing:3,children:[(0,t.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,t.jsxs)(i.Z,{className:"form-group light",children:[(0,t.jsxs)(c.Z,{className:"label-pentabell light",children:[S("aiSourcingService:servicePageForm:fullName"),"*"]}),t.jsx(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:S("aiSourcingService:servicePageForm:fullName"),variant:"standard",type:"text",name:"fullName",value:e.fullName,onChange:a,error:!!(u.fullName&&x.fullName)})]}),t.jsx(s.Bc,{name:"fullName",children:e=>t.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,t.jsxs)(i.Z,{className:"form-group light",children:[t.jsx(c.Z,{className:"label-pentabell light",children:"Email*"}),t.jsx(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:"Email",variant:"standard",type:"email",name:"email",value:e.email,onChange:a,error:!!(u.email&&x.email)})]}),t.jsx(s.Bc,{name:"email",children:e=>t.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsxs)(n.default,{item:!0,xs:12,sm:"Company"===e.youAre?6:12,children:[(0,t.jsxs)(i.Z,{className:"form-group light",children:[(0,t.jsxs)(c.Z,{className:"label-pentabell light",children:[S("aiSourcingService:servicePageForm:youAre"),"*"]}),t.jsx(m.Z,{className:"input-pentabell light",id:"tags-standard",options:["Consultant","Company"],getOptionLabel:e=>e,name:"youAre",value:e.youAre,onChange:(e,a)=>r("youAre",a),renderInput:e=>t.jsx(o.Z,{...e,className:"input-pentabell multiple-select  light",variant:"standard",placeholder:S("aiSourcingService:servicePageForm:chooseOne"),error:!!(u.youAre&&x.youAre)})})]}),t.jsx(s.Bc,{name:"youAre",children:e=>t.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),"Company"===e.youAre&&(0,t.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,t.jsxs)(i.Z,{className:"form-group light",children:[t.jsx(c.Z,{className:"label-pentabell light",children:S("payrollService:servicePageForm:companyName")}),t.jsx(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:S("payrollService:servicePageForm:companyName"),variant:"standard",type:"text",name:"companyName",value:e.companyName,onChange:a,error:!!(u.companyName&&x.companyName)})]}),t.jsx(s.Bc,{name:"companyName",children:e=>t.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,t.jsxs)(i.Z,{className:"form-group light",children:[t.jsx(c.Z,{className:"label-pentabell light",children:S("register:phoneNumber")}),t.jsx(g.sb,{defaultCountry:"fr",className:"input-pentabell light",value:e.phone,onChange:e=>{r("phone",e),l("")},flagComponent:e=>t.jsx(Z.Z,{...e})})]}),t.jsx(s.Bc,{name:"phone",children:e=>t.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,t.jsxs)(i.Z,{className:"form-group light",children:[(0,t.jsxs)(c.Z,{className:"label-pentabell light",children:[S("Tunisia:form:howWeCanHelp"),"*"]}),t.jsx(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:"How can we help you?",variant:"standard",type:"text",name:"howToHelp",value:e.howToHelp,onChange:a,error:!!(u.howToHelp&&x.howToHelp)})]}),t.jsx(s.Bc,{name:"howToHelp",children:e=>t.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsxs)(n.default,{item:!0,xs:12,sm:12,children:[(0,t.jsxs)(i.Z,{className:"form-group light",children:[t.jsx(c.Z,{className:"label-pentabell light",children:S("getInTouch:countryName")}),t.jsx(m.Z,{className:"input-pentabell light",id:"tags-standard",options:N.nh,getOptionLabel:e=>e,name:"country",value:e.country,onChange:(e,a)=>r("country",a),renderInput:e=>t.jsx(o.Z,{...e,className:"input-pentabell multiple-select  light",variant:"standard",placeholder:"Choose country",error:!!(u.country&&x.country)})})]}),t.jsx(s.Bc,{name:"country",children:e=>t.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsxs)(n.default,{item:!0,xs:12,sm:12,children:[(0,t.jsxs)(i.Z,{className:"form-group light",children:[t.jsx(c.Z,{className:"label-pentabell light",children:"Message"}),t.jsx(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:"Message",variant:"standard",type:"text",name:"message",value:e.message,onChange:a,error:!!(u.message&&x.message)})]}),t.jsx(s.Bc,{name:"message",children:e=>t.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),t.jsx(n.default,{item:!0,xs:12,sm:8,children:(0,t.jsxs)(i.Z,{children:[t.jsx(h.Z,{className:"checkbox-pentabell light",control:t.jsx(p.Z,{name:"acceptTerms",checked:e.acceptTerms,onChange:a,error:!!(u.acceptTerms&&x.acceptTerms)}),label:"By submitting this form, you agree to our terms and conditions and privacy policy and that Pentabell may use your data to send you emails, including newsletters, promotional offers and information about our services."}),t.jsx(s.Bc,{name:"acceptTerms",children:e=>t.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]})}),t.jsx(n.default,{item:!0,xs:12,sm:4,className:"flex-end",children:t.jsx(j.default,{text:"Send",className:"btn btn-filled btn-submit",type:"submit"})})]})})}),t.jsx(y.Z,{errMsg:a,success:C})]})})}},35869:(e,a,l)=>{l.d(a,{Z:()=>t});let t=(0,l(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\services\CTAPayroll.jsx#default`)},64789:(e,a,l)=>{l.d(a,{Z:()=>r});var t=l(19510),s=l(55920);let r=function({title:e,items:a}){return a&&0!==a.length?(0,t.jsxs)(s.Z,{id:"hr-solution-africa-section",className:"custom-max-width",children:[t.jsx("h2",{className:"heading-h1 text-center",children:e}),t.jsx("div",{className:"locations",children:a.map((e,a)=>(0,t.jsxs)("div",{className:"location-item four-items",children:[t.jsx("h3",{className:"label",children:e.title}),t.jsx("p",{className:"value paragraph",children:e.description})]},a))})]}):null}},48570:(e,a,l)=>{l.d(a,{Z:()=>u});var t,s=l(19510),r=l(55920),n=l(86483),i=l(1788);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var l=arguments[a];for(var t in l)({}).hasOwnProperty.call(l,t)&&(e[t]=l[t])}return e}).apply(null,arguments)}let o=e=>i.createElement("svg",c({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),t||(t=i.createElement("path",{fill:"#234791",d:"m16.172 11-5.364-5.364 1.414-1.414L20 12l-7.778 7.778-1.414-1.414L16.172 13H4v-2z"})));var d=l(61780),m=l(28853),h=l(70064),p=l(86715);let u=function({t:e,contacts:a,sectionSubtitleKey:l,sectionTitleKey:t,exploreMoreTextKey:i="exploreMore",viewOnMapTextKey:c="viewOnMap"}){return(0,s.jsxs)("div",{id:"africa-locations",children:[(0,s.jsxs)(r.Z,{className:"custom-max-width",children:[l&&s.jsx("p",{className:"sub-heading text-center text-blue",children:e(l)}),t&&s.jsx("h2",{className:"heading-h1 text-center",children:e(t)})]}),s.jsx(r.Z,{className:"contact-items-section custom-max-width",children:a.map((a,t)=>(0,s.jsxs)("div",{className:"contact-item",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"country-img",children:[(0,s.jsxs)("p",{className:"country-label",children:[s.jsx("img",{width:22,height:14,src:(0,h.Qu)(a.country),alt:(0,h.SY)(a.country),loading:"lazy"}),(0,h.SY)(a.country)]}),s.jsx("img",{width:388,height:253,src:a.img.src,alt:a.alt,loading:"lazy"})]}),(0,s.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-end"},children:[s.jsx("h3",{className:"sub-heading text-blue",children:a.title}),a.iconNew&&s.jsx("span",{children:a.iconNew})]}),(0,s.jsxs)("div",{children:[a.locations.map((e,a)=>(0,s.jsxs)("p",{className:"row-item",children:[s.jsx("span",{children:s.jsx(n.Z,{})}),e]},a)),(0,s.jsxs)("p",{className:"row-item",children:[s.jsx("span",{children:s.jsx(d.Z,{})}),a.phones.map((e,l)=>(0,s.jsxs)("span",{children:[e,a.phones.length>1&&l!==a.phones.length-1?s.jsx(s.Fragment,{children:s.jsx("br",{})}):null]},l))]}),(0,s.jsxs)("p",{className:"row-item",children:[s.jsx("span",{children:s.jsx(m.Z,{})}),a.email]})]})]}),(0,s.jsxs)("div",{className:"btns",children:[s.jsx(p.Z,{text:e(l?`${l.split(":")[0]}:locations:${i}`:i),className:"btn btn-outlined ",link:a.link}),s.jsx(p.Z,{text:e(l?`${l.split(":")[0]}:locations:${c}`:c),className:"btn btn-ghost",link:a.link,icon:s.jsx(o,{})})]})]},t))})]})}},46163:(e,a,l)=>{l.d(a,{Z:()=>t});let t=(0,l(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\features\forms\components\AfricaForm.jsx#default`)},34732:(e,a,l)=>{l.d(a,{Z:()=>t});let t={src:"/_next/static/media/ctaAfricaPage.3f6b6c6d.png",height:305,width:1160,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAMAAABSSm3fAAAAHlBMVEUeJztrc5g4UYVcdKNmdaFLZ51CS2Boan5VapdWYICUEIFKAAAAAXRSTlP+GuMHfQAAAAlwSFlzAAALEwAACxMBAJqcGAAAABpJREFUeJxjYGVlZmZhZGdjYGLiYGHkZGMAAAJkAENTNHiqAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:2}},20187:(e,a,l)=>{l.r(a),l.d(a,{default:()=>t});let t={src:"/_next/static/media/ctaAfricaPage.3f6b6c6d.png",height:305,width:1160,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAMAAABSSm3fAAAAHlBMVEUeJztrc5g4UYVcdKNmdaFLZ51CS2Boan5VapdWYICUEIFKAAAAAXRSTlP+GuMHfQAAAAlwSFlzAAALEwAACxMBAJqcGAAAABpJREFUeJxjYGVlZmZhZGdjYGLiYGHkZGMAAAJkAENTNHiqAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:2}}};