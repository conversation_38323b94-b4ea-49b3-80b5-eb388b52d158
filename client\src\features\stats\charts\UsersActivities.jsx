import { useEffect, useState } from "react";
import { <PERSON>, CardContent, TextField, Grid } from "@mui/material";

import CustomButton from "@/components/ui/CustomButton";
import SvgRefreshIcon from "@/assets/images/icons/refreshIcon.svg";
import CustomMultiBarChart from "@/components/charts/CustomMultiBarchart";
import { useGetUserStat } from "../stats.hooks";

export default function UsersActivities({ chartSettings, t }) {
  const [dateFromUser, setDateFromUser] = useState("2024-09-01");
  const [dateToUser, setDateToUser] = useState(() => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  });
  const [searchUser, setSearchUser] = useState(false);

  const resetSearchActivity = () => {
    setDateToUser(() => {
      const today = new Date();
      return today.toISOString().split("T")[0];
    });
    setDateFromUser("2024-09-01");
    setSearchUser(!searchUser);
  };

  const getDataUserActivity = useGetUserStat({
    dateFrom: dateFromUser,
    dateTo: dateToUser,
  });

  const userAactivity = {
    title: t("statsDash:usersActivities"),
    dataKey: ["login", "register", "resumes", "applications"],
    dataset: getDataUserActivity?.data,
    color: ["#30B0C7", "#234791", "#007AFF", "#32ADE6"],
  };

  useEffect(() => {
    getDataUserActivity.refetch();
  }, [searchUser]);

  return (
    <Card className="card">
      <CardContent>
        <div className="barchartfilter-wrapper">
          <Grid container className="chart-grid" spacing={1}>
            <Grid item xs={12} sm={12} md={3} alignContent="left">
              <p className="heading-h3" gutterBottom>
                {userAactivity.title}
              </p>
            </Grid>

            <Grid item xs={12} sm={6} md={2.5} xl={3}>
              <TextField
                label={t("statsDash:fromDate")}
                type="date"
                value={dateFromUser}
                onChange={(e) => setDateFromUser(e.target.value)}
                fullWidth
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2.5} xl={3}>
              <TextField
                label={t("statsDash:toDate")}
                type="date"
                value={dateToUser}
                onChange={(e) => setDateToUser(e.target.value)}
                fullWidth
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid
              item
              xs={2}
              sm={1}
              md={1.5}
              xl={1}
              className="btns-filter dashboard"
            >
              <CustomButton
                icon={<SvgRefreshIcon />}
                className={"btn btn-outlined btn-refresh full-width"}
                onClick={resetSearchActivity}
              />
            </Grid>
            <Grid item xs={10} sm={11} md={2.5} xl={2}>
              <CustomButton
                text={t("statsDash:filter")}
                onClick={() => {
                  setSearchUser(!searchUser);
                }}
                className={"btn btn-outlined btn-filter-stat full-width"}
              />
            </Grid>
          </Grid>
        </div>

        <div className="chart-wrapper">
          {userAactivity.dataset?.length > 0 && (
            <div className="labelstats-wrapper">
              <div className="label-wrapper">
                <span className="logins-dot" />
                <span className="label-chart">{t("statsDash:logins")}</span>
              </div>
              <div className="label-wrapper">
                <span className="newacccounts-dot" />
                <span className="label-chart">
                  {t("statsDash:newAccounts")}
                </span>
              </div>
              <div className="label-wrapper">
                <span className="uploadedresumes-dot" />
                <span className="label-chart">
                  {t("statsDash:uploadedResumes")}
                </span>
              </div>
              <div className="label-wrapper">
                <span className="applications-dot" />
                <span className="label-chart">
                  {t("statsDash:applications")}
                </span>
              </div>
            </div>
          )}
          <CustomMultiBarChart
            chart={userAactivity}
            chartSettings={chartSettings}
          />
        </div>
      </CardContent>
    </Card>
  );
}
