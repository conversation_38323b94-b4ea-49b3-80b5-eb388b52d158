@import url('https://fonts.googleapis.com/css?family=Comfortaa');

* {
  box-sizing: border-box;
}
body,
html {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: hidden;
}
body {
  background-color: #114a8b; /* Manually darkened color */
  font-family: sans-serif;
}
.container {
  z-index: 1;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  text-align: center;
  padding: 10px;
  min-width: 300px;
}
.container div {
  display: inline-block;
}
.container .lock {
  opacity: 1;
}
.container h1 {
  font-family: 'Comfortaa', cursive;
  font-size: 100px;
  text-align: center;
  color: #eee;
  font-weight: 100;
  margin: 0;
}
.container p {
  color: #fff;
}
.lock {
  transition: 0.5s ease;
  position: relative;
  overflow: hidden;
  opacity: 0;
}
.lock.generated {
  transform: scale(0.5);
  position: absolute;
  animation: 2s move linear;
  animation-fill-mode: forwards;
}
.lock .bottom {
  background: #D68910;
  height: 40px;
  width: 60px;
  display: block;
  position: relative;
  margin: 0 auto;
}
.lock .top {
  height: 60px;
  width: 50px;
  border-radius: 50%;
  border: 10px solid #fff;
  display: block;
  position: relative;
  top: 30px;
  margin: 0 auto;
}
.lock .top::after {
  padding: 10px;
  border-radius: 50%;
}
.logo-container {
  position: absolute;
  z-index: 1000;
}

.logo-container img {
  height: 80px; /* Adjust the height as needed */
  width: auto;
}
@keyframes move {
  to {
    top: 100%;
  }
}
@media (max-width: 420px) {
  .container {
    transform: translate(-50%,-50%) scale(0.8);
  }
  .lock.generated {
    transform: scale(0.3);
  }
}
