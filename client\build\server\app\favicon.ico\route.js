(()=>{var e={};e.id=7155,e.ids=[7155],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},44082:(e,t,r)=>{"use strict";r.r(t),r.d(t,{originalPathname:()=>f,patchFetch:()=>b,requestAsyncStorage:()=>d,routeModule:()=>c,serverHooks:()=>h,staticGenerationAsyncStorage:()=>p});var i={};r.r(i),r.d(i,{GET:()=>u,dynamic:()=>l});var n=r(49303),o=r(88716),a=r(60670),s=r(73896);let A=Buffer.from("AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAABMLAAATCwAAAAAAAAAAAAAAAAAAAAAAAAAAAACPRCBAkUcj/49IICAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAj0QgQJFHI/+PSCAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACRRyP/kUcj/5FHI/+RRyP/kUcj/5FHI/+RRyPgkkckcI9IKCCRRyPfkUcj/5FHI/+RRyP/kUcj/wAAAAAAAAAAkUcj/49FIpCRRiJ/kUcj/49HInCPRSNgj0UikJFHI/+RSCSAj0goII9FI2CPRSNgkUYif5FHI/8AAAAAAAAAAJFHI/+PSCRAj0QgQJFHI/+PSCAgAAAAAAAAAACQRyKPkUci3wAAAAAAAAAAAAAAAI9EIECRRyP/AAAAAAAAAACRRyP/j0gkQI9IKCCRRyP/j0gjYAAAAAAAAAAAj0UjYJFHI/8AAAAAAAAAAAAAAACPRCBAkUcj/wAAAAAAAAAAkUcj/49IJEAAAAAAkEcij5FHI++RSCSAj0olMJJHI6+RRiPvAAAAAAAAAAAAAAAAj0QgQJFHI/8AAAAAAAAAAJFHI/+PSCRAAAAAAAAAAACQRyKPkUcj/5FHI/+RRiPvkUgjXwAAAAAAAAAAAAAAAI9EIECRRyP/AAAAAAAAAACRRyP/j0gkQAAAAAAAAAAAAAAAAI9AIBCPRCBAj0AgEAAAAAAAAAAAAAAAAAAAAACPRCBAkUcj/wAAAAAAAAAAkUcj/49IJEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAj0QgQJFHI/8AAAAAAAAAAJFHI/+PSCRAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAI9EIECRRyP/AAAAAAAAAACRRyP/j0gkQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACPRCBAkUcj/wAAAAAAAAAAkUcj/49IJEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAj0QgQJFHI/8AAAAAAAAAAJFHI/+PSCRAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAI9EIECRRyP/AAAAAAAAAACRRyP/kkcicI9IJECPSCRAj0gkQI9IJECPSCRAj0gkQI9IJECPSCRAj0gkQI9IJECPRyJwkUcj/wAAAAAAAAAAkUcj/5FHI/+RRyP/kUcj/5FHI/+RRyP/kUcj/5FHI/+RRyP/kUcj/5FHI/+RRyP/kUcj/5FHI/8AAAAA4/8AAOP/AACAAQAAgAEAAIM5AACDOQAAkDkAAJg5AACceQAAn/kAAJ/5AACf+QAAn/kAAJ/5AACAAQAAgAEAAA==","base64");function u(){return new s.NextResponse(A,{headers:{"Content-Type":"image/x-icon","Cache-Control":"public, max-age=0, must-revalidate"}})}let l="force-static",c=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/favicon.ico/route",pathname:"/favicon.ico",filename:"favicon",bundlePath:"app/favicon.ico/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Ffavicon.ico%2Froute&filePath=C%3A%5CUsers%5Caziz%5CDesktop%5Cpentabellversion2.0%5Cclient%5Csrc%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__",nextConfigOutput:"",userland:i}),{requestAsyncStorage:d,staticGenerationAsyncStorage:p,serverHooks:h}=c,f="/favicon.ico/route";function b(){return(0,a.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:p})}},36637:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,o={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),i=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?i:`${i}; ${r.join("; ")}`}function s(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[i,n]=[r.slice(0,e),r.slice(e+1)];try{t.set(i,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function A(e){var t,r;if(!e)return;let[[i,n],...o]=s(e),{domain:a,expires:A,httponly:c,maxage:d,path:p,samesite:h,secure:f,partitioned:b,priority:m}=Object.fromEntries(o.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:i,value:decodeURIComponent(n),domain:a,...A&&{expires:new Date(A)},...c&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:p,...h&&{sameSite:u.includes(t=(t=h).toLowerCase())?t:void 0},...f&&{secure:!0},...m&&{priority:l.includes(r=(r=m).toLowerCase())?r:void 0},...b&&{partitioned:!0}})}((e,r)=>{for(var i in r)t(e,i,{get:r[i],enumerable:!0})})(o,{RequestCookies:()=>c,ResponseCookies:()=>d,parseCookie:()=>s,parseSetCookie:()=>A,stringifyCookie:()=>a}),e.exports=((e,o,a,s)=>{if(o&&"object"==typeof o||"function"==typeof o)for(let a of i(o))n.call(e,a)||void 0===a||t(e,a,{get:()=>o[a],enumerable:!(s=r(o,a))||s.enumerable});return e})(t({},"__esModule",{value:!0}),o);var u=["strict","lax","none"],l=["low","medium","high"],c=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of s(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===i).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,i=this._parsed;return i.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(i).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,i;this._parsed=new Map,this._headers=e;let n=null!=(i=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?i:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,r,i,n,o,a=[],s=0;function A(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,o=!1;A();)if(","===(r=e.charAt(s))){for(i=s,s+=1,A(),n=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(o=!0,s=n,a.push(e.substring(t,i)),t=s):s=i+1}else s+=1;(!o||s>=e.length)&&a.push(e.substring(t,e.length))}return a}(n)){let t=A(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===i)}has(e){return this._parsed.has(e)}set(...e){let[t,r,i]=1===e.length?[e[0].name,e[0].value,e[0]]:e,n=this._parsed;return n.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...i})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(n,this._headers),this}delete(...e){let[t,r,i]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:i,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},42565:(e,t,r)=>{var i;(()=>{var n={226:function(n,o){!function(a,s){"use strict";var A="function",u="undefined",l="object",c="string",d="major",p="model",h="name",f="type",b="vendor",m="version",g="architecture",w="console",v="mobile",_="tablet",P="smarttv",R="wearable",x="embedded",y="Amazon",E="Apple",S="ASUS",C="BlackBerry",k="Browser",I="Chrome",j="Firefox",O="Google",T="Huawei",N="Microsoft",L="Motorola",U="Opera",H="Samsung",M="Sharp",D="Sony",F="Xiaomi",q="Zebra",B="Facebook",G="Chromium OS",X="Mac OS",z=function(e,t){var r={};for(var i in e)t[i]&&t[i].length%2==0?r[i]=t[i].concat(e[i]):r[i]=e[i];return r},Q=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},$=function(e,t){return typeof e===c&&-1!==J(t).indexOf(J(e))},J=function(e){return e.toLowerCase()},V=function(e,t){if(typeof e===c)return e=e.replace(/^\s\s*/,""),typeof t===u?e:e.substring(0,350)},W=function(e,t){for(var r,i,n,o,a,u,c=0;c<t.length&&!a;){var d=t[c],p=t[c+1];for(r=i=0;r<d.length&&!a&&d[r];)if(a=d[r++].exec(e))for(n=0;n<p.length;n++)u=a[++i],typeof(o=p[n])===l&&o.length>0?2===o.length?typeof o[1]==A?this[o[0]]=o[1].call(this,u):this[o[0]]=o[1]:3===o.length?typeof o[1]!==A||o[1].exec&&o[1].test?this[o[0]]=u?u.replace(o[1],o[2]):void 0:this[o[0]]=u?o[1].call(this,u,o[2]):void 0:4===o.length&&(this[o[0]]=u?o[3].call(this,u.replace(o[1],o[2])):void 0):this[o]=u||s;c+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===l&&t[r].length>0){for(var i=0;i<t[r].length;i++)if($(t[r][i],e))return"?"===r?s:r}else if($(t[r],e))return"?"===r?s:r;return e},K={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[m,[h,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[m,[h,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[h,m],[/opios[\/ ]+([\w\.]+)/i],[m,[h,U+" Mini"]],[/\bopr\/([\w\.]+)/i],[m,[h,U]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[h,m],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[m,[h,"UC"+k]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[m,[h,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[m,[h,"WeChat"]],[/konqueror\/([\w\.]+)/i],[m,[h,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[m,[h,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[m,[h,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[h,/(.+)/,"$1 Secure "+k],m],[/\bfocus\/([\w\.]+)/i],[m,[h,j+" Focus"]],[/\bopt\/([\w\.]+)/i],[m,[h,U+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[m,[h,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[m,[h,"Dolphin"]],[/coast\/([\w\.]+)/i],[m,[h,U+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[m,[h,"MIUI "+k]],[/fxios\/([-\w\.]+)/i],[m,[h,j]],[/\bqihu|(qi?ho?o?|360)browser/i],[[h,"360 "+k]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[h,/(.+)/,"$1 "+k],m],[/(comodo_dragon)\/([\w\.]+)/i],[[h,/_/g," "],m],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[h,m],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[h],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[h,B],m],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[h,m],[/\bgsa\/([\w\.]+) .*safari\//i],[m,[h,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[m,[h,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[m,[h,I+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[h,I+" WebView"],m],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[m,[h,"Android "+k]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[h,m],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[m,[h,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[m,h],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[h,[m,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[h,m],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[h,"Netscape"],m],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[m,[h,j+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[h,m],[/(cobalt)\/([\w\.]+)/i],[h,[m,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,J]],[/((?:i[346]|x)86)[;\)]/i],[[g,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[g,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[g,/ower/,"",J]],[/(sun4\w)[;\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[g,J]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[p,[b,H],[f,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[p,[b,H],[f,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[p,[b,E],[f,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[p,[b,E],[f,_]],[/(macintosh);/i],[p,[b,E]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[p,[b,M],[f,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[p,[b,T],[f,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[p,[b,T],[f,v]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[p,/_/g," "],[b,F],[f,v]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[p,/_/g," "],[b,F],[f,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[p,[b,"OPPO"],[f,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[p,[b,"Vivo"],[f,v]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[p,[b,"Realme"],[f,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[p,[b,L],[f,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[p,[b,L],[f,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[p,[b,"LG"],[f,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[p,[b,"LG"],[f,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[p,[b,"Lenovo"],[f,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[p,/_/g," "],[b,"Nokia"],[f,v]],[/(pixel c)\b/i],[p,[b,O],[f,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[p,[b,O],[f,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[p,[b,D],[f,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[p,"Xperia Tablet"],[b,D],[f,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[p,[b,"OnePlus"],[f,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[p,[b,y],[f,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[p,/(.+)/g,"Fire Phone $1"],[b,y],[f,v]],[/(playbook);[-\w\),; ]+(rim)/i],[p,b,[f,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[p,[b,C],[f,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[p,[b,S],[f,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[p,[b,S],[f,v]],[/(nexus 9)/i],[p,[b,"HTC"],[f,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[b,[p,/_/g," "],[f,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[p,[b,"Acer"],[f,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[p,[b,"Meizu"],[f,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[b,p,[f,v]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[b,p,[f,_]],[/(surface duo)/i],[p,[b,N],[f,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[p,[b,"Fairphone"],[f,v]],[/(u304aa)/i],[p,[b,"AT&T"],[f,v]],[/\bsie-(\w*)/i],[p,[b,"Siemens"],[f,v]],[/\b(rct\w+) b/i],[p,[b,"RCA"],[f,_]],[/\b(venue[\d ]{2,7}) b/i],[p,[b,"Dell"],[f,_]],[/\b(q(?:mv|ta)\w+) b/i],[p,[b,"Verizon"],[f,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[p,[b,"Barnes & Noble"],[f,_]],[/\b(tm\d{3}\w+) b/i],[p,[b,"NuVision"],[f,_]],[/\b(k88) b/i],[p,[b,"ZTE"],[f,_]],[/\b(nx\d{3}j) b/i],[p,[b,"ZTE"],[f,v]],[/\b(gen\d{3}) b.+49h/i],[p,[b,"Swiss"],[f,v]],[/\b(zur\d{3}) b/i],[p,[b,"Swiss"],[f,_]],[/\b((zeki)?tb.*\b) b/i],[p,[b,"Zeki"],[f,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[b,"Dragon Touch"],p,[f,_]],[/\b(ns-?\w{0,9}) b/i],[p,[b,"Insignia"],[f,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[p,[b,"NextBook"],[f,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[b,"Voice"],p,[f,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[b,"LvTel"],p,[f,v]],[/\b(ph-1) /i],[p,[b,"Essential"],[f,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[p,[b,"Envizen"],[f,_]],[/\b(trio[-\w\. ]+) b/i],[p,[b,"MachSpeed"],[f,_]],[/\btu_(1491) b/i],[p,[b,"Rotor"],[f,_]],[/(shield[\w ]+) b/i],[p,[b,"Nvidia"],[f,_]],[/(sprint) (\w+)/i],[b,p,[f,v]],[/(kin\.[onetw]{3})/i],[[p,/\./g," "],[b,N],[f,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[p,[b,q],[f,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[p,[b,q],[f,v]],[/smart-tv.+(samsung)/i],[b,[f,P]],[/hbbtv.+maple;(\d+)/i],[[p,/^/,"SmartTV"],[b,H],[f,P]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[b,"LG"],[f,P]],[/(apple) ?tv/i],[b,[p,E+" TV"],[f,P]],[/crkey/i],[[p,I+"cast"],[b,O],[f,P]],[/droid.+aft(\w)( bui|\))/i],[p,[b,y],[f,P]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[p,[b,M],[f,P]],[/(bravia[\w ]+)( bui|\))/i],[p,[b,D],[f,P]],[/(mitv-\w{5}) bui/i],[p,[b,F],[f,P]],[/Hbbtv.*(technisat) (.*);/i],[b,p,[f,P]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[b,V],[p,V],[f,P]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[f,P]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[b,p,[f,w]],[/droid.+; (shield) bui/i],[p,[b,"Nvidia"],[f,w]],[/(playstation [345portablevi]+)/i],[p,[b,D],[f,w]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[p,[b,N],[f,w]],[/((pebble))app/i],[b,p,[f,R]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[p,[b,E],[f,R]],[/droid.+; (glass) \d/i],[p,[b,O],[f,R]],[/droid.+; (wt63?0{2,3})\)/i],[p,[b,q],[f,R]],[/(quest( 2| pro)?)/i],[p,[b,B],[f,R]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[b,[f,x]],[/(aeobc)\b/i],[p,[b,y],[f,x]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[p,[f,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[p,[f,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[f,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[f,v]],[/(android[-\w\. ]{0,9});.+buil/i],[p,[b,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[m,[h,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[m,[h,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[h,m],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[m,h]],os:[[/microsoft (windows) (vista|xp)/i],[h,m],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[h,[m,Y,K]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[h,"Windows"],[m,Y,K]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[m,/_/g,"."],[h,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[h,X],[m,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[m,h],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[h,m],[/\(bb(10);/i],[m,[h,C]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[m,[h,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[m,[h,j+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[m,[h,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[m,[h,"watchOS"]],[/crkey\/([\d\.]+)/i],[m,[h,I+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[h,G],m],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[h,m],[/(sunos) ?([\w\.\d]*)/i],[[h,"Solaris"],m],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[h,m]]},ee=function(e,t){if(typeof e===l&&(t=e,e=s),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==u&&a.navigator?a.navigator:s,i=e||(r&&r.userAgent?r.userAgent:""),n=r&&r.userAgentData?r.userAgentData:s,o=t?z(Z,t):Z,w=r&&r.userAgent==i;return this.getBrowser=function(){var e,t={};return t[h]=s,t[m]=s,W.call(t,i,o.browser),t[d]=typeof(e=t[m])===c?e.replace(/[^\d\.]/g,"").split(".")[0]:s,w&&r&&r.brave&&typeof r.brave.isBrave==A&&(t[h]="Brave"),t},this.getCPU=function(){var e={};return e[g]=s,W.call(e,i,o.cpu),e},this.getDevice=function(){var e={};return e[b]=s,e[p]=s,e[f]=s,W.call(e,i,o.device),w&&!e[f]&&n&&n.mobile&&(e[f]=v),w&&"Macintosh"==e[p]&&r&&typeof r.standalone!==u&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[p]="iPad",e[f]=_),e},this.getEngine=function(){var e={};return e[h]=s,e[m]=s,W.call(e,i,o.engine),e},this.getOS=function(){var e={};return e[h]=s,e[m]=s,W.call(e,i,o.os),w&&!e[h]&&n&&"Unknown"!=n.platform&&(e[h]=n.platform.replace(/chrome os/i,G).replace(/macos/i,X)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(e){return i=typeof e===c&&e.length>350?V(e,350):e,this},this.setUA(i),this};ee.VERSION="1.0.35",ee.BROWSER=Q([h,m,d]),ee.CPU=Q([g]),ee.DEVICE=Q([p,b,f,w,v,P,_,R,x]),ee.ENGINE=ee.OS=Q([h,m]),typeof o!==u?(n.exports&&(o=n.exports=ee),o.UAParser=ee):r.amdO?void 0!==(i=(function(){return ee}).call(t,r,t,e))&&(e.exports=i):typeof a!==u&&(a.UAParser=ee);var et=typeof a!==u&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},o={};function a(e){var t=o[e];if(void 0!==t)return t.exports;var r=o[e]={exports:{}},i=!0;try{n[e].call(r.exports,r,r.exports,a),i=!1}finally{i&&delete o[e]}return r.exports}a.ab=__dirname+"/";var s=a(226);e.exports=s})()},90319:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return A},APP_DIR_ALIAS:function(){return S},CACHE_ONE_YEAR:function(){return v},DOT_NEXT_ALIAS:function(){return y},ESLINT_DEFAULT_DIRS:function(){return z},GSP_NO_RETURNED_VALUE:function(){return D},GSSP_COMPONENT_MEMBER_ERROR:function(){return B},GSSP_NO_RETURNED_VALUE:function(){return F},INSTRUMENTATION_HOOK_FILENAME:function(){return R},MIDDLEWARE_FILENAME:function(){return _},MIDDLEWARE_LOCATION_REGEXP:function(){return P},NEXT_BODY_SUFFIX:function(){return c},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return w},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return h},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return f},NEXT_CACHE_SOFT_TAGS_HEADER:function(){return p},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return g},NEXT_CACHE_TAGS_HEADER:function(){return d},NEXT_CACHE_TAG_MAX_ITEMS:function(){return b},NEXT_CACHE_TAG_MAX_LENGTH:function(){return m},NEXT_DATA_SUFFIX:function(){return u},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return i},NEXT_META_SUFFIX:function(){return l},NEXT_QUERY_PARAM_PREFIX:function(){return r},NON_STANDARD_NODE_ENV:function(){return G},PAGES_DIR_ALIAS:function(){return x},PRERENDER_REVALIDATE_HEADER:function(){return n},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return o},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return T},ROOT_DIR_ALIAS:function(){return E},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return O},RSC_ACTION_ENCRYPTION_ALIAS:function(){return j},RSC_ACTION_PROXY_ALIAS:function(){return I},RSC_ACTION_VALIDATE_ALIAS:function(){return k},RSC_MOD_REF_PROXY_ALIAS:function(){return C},RSC_PREFETCH_SUFFIX:function(){return a},RSC_SUFFIX:function(){return s},SERVER_PROPS_EXPORT_ERROR:function(){return M},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return L},SERVER_PROPS_SSG_CONFLICT:function(){return U},SERVER_RUNTIME:function(){return Q},SSG_FALLBACK_EXPORT_ERROR:function(){return X},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return N},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return H},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return q},WEBPACK_LAYERS:function(){return J},WEBPACK_RESOURCE_QUERIES:function(){return V}});let r="nxtP",i="nxtI",n="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",a=".prefetch.rsc",s=".rsc",A=".action",u=".json",l=".meta",c=".body",d="x-next-cache-tags",p="x-next-cache-soft-tags",h="x-next-revalidated-tags",f="x-next-revalidate-tag-token",b=128,m=256,g=1024,w="_N_T_",v=31536e3,_="middleware",P=`(?:src/)?${_}`,R="instrumentation",x="private-next-pages",y="private-dot-next",E="private-next-root-dir",S="private-next-app-dir",C="next/dist/build/webpack/loaders/next-flight-loader/module-proxy",k="private-next-rsc-action-validate",I="private-next-rsc-server-reference",j="private-next-rsc-action-encryption",O="private-next-rsc-action-client-wrapper",T="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",N="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",L="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",U="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",H="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",M="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",D="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",F="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",q="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",B="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",G='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',X="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",z=["app","pages","components","lib","src"],Q={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},$={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"},J={...$,GROUP:{serverOnly:[$.reactServerComponents,$.actionBrowser,$.appMetadataRoute,$.appRouteHandler,$.instrument],clientOnly:[$.serverSideRendering,$.appPagesBrowser],nonClientServerTarget:[$.middleware,$.api],app:[$.reactServerComponents,$.actionBrowser,$.appMetadataRoute,$.appRouteHandler,$.serverSideRendering,$.appPagesBrowser,$.shared,$.instrument]}},V={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},49303:(e,t,r)=>{"use strict";e.exports=r(30517)},46294:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PageSignatureError:function(){return r},RemovedPageError:function(){return i},RemovedUAError:function(){return n}});class r extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class i extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class n extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},73896:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ImageResponse:function(){return i.ImageResponse},NextRequest:function(){return n.NextRequest},NextResponse:function(){return o.NextResponse},URLPattern:function(){return s.URLPattern},userAgent:function(){return a.userAgent},userAgentFromString:function(){return a.userAgentFromString}});let i=r(76274),n=r(49253),o=r(86716),a=r(27),s=r(27718)},62420:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextURL",{enumerable:!0,get:function(){return l}});let i=r(19976),n=r(61704),o=r(48614),a=r(95393),s=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function A(e,t){return new URL(String(e).replace(s,"localhost"),t&&String(t).replace(s,"localhost"))}let u=Symbol("NextURLInternal");class l{constructor(e,t,r){let i,n;"object"==typeof t&&"pathname"in t||"string"==typeof t?(i=t,n=r||{}):n=r||t||{},this[u]={url:A(e,i??n.base),options:n,basePath:""},this.analyze()}analyze(){var e,t,r,n,s;let A=(0,a.getNextPathnameInfo)(this[u].url.pathname,{nextConfig:this[u].options.nextConfig,parseData:!0,i18nProvider:this[u].options.i18nProvider}),l=(0,o.getHostname)(this[u].url,this[u].options.headers);this[u].domainLocale=this[u].options.i18nProvider?this[u].options.i18nProvider.detectDomainLocale(l):(0,i.detectDomainLocale)(null==(t=this[u].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,l);let c=(null==(r=this[u].domainLocale)?void 0:r.defaultLocale)||(null==(s=this[u].options.nextConfig)?void 0:null==(n=s.i18n)?void 0:n.defaultLocale);this[u].url.pathname=A.pathname,this[u].defaultLocale=c,this[u].basePath=A.basePath??"",this[u].buildId=A.buildId,this[u].locale=A.locale??c,this[u].trailingSlash=A.trailingSlash}formatPathname(){return(0,n.formatNextPathnameInfo)({basePath:this[u].basePath,buildId:this[u].buildId,defaultLocale:this[u].options.forceLocale?void 0:this[u].defaultLocale,locale:this[u].locale,pathname:this[u].url.pathname,trailingSlash:this[u].trailingSlash})}formatSearch(){return this[u].url.search}get buildId(){return this[u].buildId}set buildId(e){this[u].buildId=e}get locale(){return this[u].locale??""}set locale(e){var t,r;if(!this[u].locale||!(null==(r=this[u].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[u].locale=e}get defaultLocale(){return this[u].defaultLocale}get domainLocale(){return this[u].domainLocale}get searchParams(){return this[u].url.searchParams}get host(){return this[u].url.host}set host(e){this[u].url.host=e}get hostname(){return this[u].url.hostname}set hostname(e){this[u].url.hostname=e}get port(){return this[u].url.port}set port(e){this[u].url.port=e}get protocol(){return this[u].url.protocol}set protocol(e){this[u].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[u].url=A(e),this.analyze()}get origin(){return this[u].url.origin}get pathname(){return this[u].url.pathname}set pathname(e){this[u].url.pathname=e}get hash(){return this[u].url.hash}set hash(e){this[u].url.hash=e}get search(){return this[u].url.search}set search(e){this[u].url.search=e}get password(){return this[u].url.password}set password(e){this[u].url.password=e}get username(){return this[u].url.username}set username(e){this[u].url.username=e}get basePath(){return this[u].basePath}set basePath(e){this[u].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new l(String(this),this[u].options)}}},90127:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let i=Reflect.get(e,t,r);return"function"==typeof i?i.bind(e):i}static set(e,t,r,i){return Reflect.set(e,t,r,i)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},32205:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return i.RequestCookies},ResponseCookies:function(){return i.ResponseCookies},stringifyCookie:function(){return i.stringifyCookie}});let i=r(36637)},76274:(e,t)=>{"use strict";function r(){throw Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead')}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageResponse",{enumerable:!0,get:function(){return r}})},49253:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERNALS:function(){return s},NextRequest:function(){return A}});let i=r(62420),n=r(45724),o=r(46294),a=r(32205),s=Symbol("internal request");class A extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);(0,n.validateURL)(r),e instanceof Request?super(e,t):super(r,t);let o=new i.NextURL(r,{headers:(0,n.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:t.nextConfig});this[s]={cookies:new a.RequestCookies(this.headers),geo:t.geo||{},ip:t.ip,nextUrl:o,url:o.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,geo:this.geo,ip:this.ip,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[s].cookies}get geo(){return this[s].geo}get ip(){return this[s].ip}get nextUrl(){return this[s].nextUrl}get page(){throw new o.RemovedPageError}get ua(){throw new o.RemovedUAError}get url(){return this[s].url}}},86716:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextResponse",{enumerable:!0,get:function(){return c}});let i=r(32205),n=r(62420),o=r(45724),a=r(90127),s=r(32205),A=Symbol("internal response"),u=new Set([301,302,303,307,308]);function l(e,t){var r;if(null==e?void 0:null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let r=[];for(let[i,n]of e.request.headers)t.set("x-middleware-request-"+i,n),r.push(i);t.set("x-middleware-override-headers",r.join(","))}}class c extends Response{constructor(e,t={}){super(e,t);let r=this.headers,u=new Proxy(new s.ResponseCookies(r),{get(e,n,o){switch(n){case"delete":case"set":return(...o)=>{let a=Reflect.apply(e[n],e,o),A=new Headers(r);return a instanceof s.ResponseCookies&&r.set("x-middleware-set-cookie",a.getAll().map(e=>(0,i.stringifyCookie)(e)).join(",")),l(t,A),a};default:return a.ReflectAdapter.get(e,n,o)}}});this[A]={cookies:u,url:t.url?new n.NextURL(t.url,{headers:(0,o.toNodeOutgoingHttpHeaders)(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[A].cookies}static json(e,t){let r=Response.json(e,t);return new c(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!u.has(r))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let i="object"==typeof t?t:{},n=new Headers(null==i?void 0:i.headers);return n.set("Location",(0,o.validateURL)(e)),new c(null,{...i,headers:n,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,o.validateURL)(e)),l(t,r),new c(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),l(e,t),new c(null,{...e,headers:t})}}},27718:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"URLPattern",{enumerable:!0,get:function(){return r}});let r="undefined"==typeof URLPattern?void 0:URLPattern},27:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isBot:function(){return n},userAgent:function(){return a},userAgentFromString:function(){return o}});let i=function(e){return e&&e.__esModule?e:{default:e}}(r(42565));function n(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}function o(e){return{...(0,i.default)(e),isBot:void 0!==e&&n(e)}}function a({headers:e}){return o(e.get("user-agent")||void 0)}},45724:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fromNodeOutgoingHttpHeaders:function(){return n},normalizeNextQueryParam:function(){return A},splitCookiesString:function(){return o},toNodeOutgoingHttpHeaders:function(){return a},validateURL:function(){return s}});let i=r(90319);function n(e){let t=new Headers;for(let[r,i]of Object.entries(e))for(let e of Array.isArray(i)?i:[i])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function o(e){var t,r,i,n,o,a=[],s=0;function A(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,o=!1;A();)if(","===(r=e.charAt(s))){for(i=s,s+=1,A(),n=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(o=!0,s=n,a.push(e.substring(t,i)),t=s):s=i+1}else s+=1;(!o||s>=e.length)&&a.push(e.substring(t,e.length))}return a}function a(e){let t={},r=[];if(e)for(let[i,n]of e.entries())"set-cookie"===i.toLowerCase()?(r.push(...o(n)),t[i]=1===r.length?r[0]:r):t[i]=n;return t}function s(e){try{return String(new URL(String(e)))}catch(t){throw Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t})}}function A(e,t){for(let r of[i.NEXT_QUERY_PARAM_PREFIX,i.NEXT_INTERCEPTION_MARKER_PREFIX])e!==r&&e.startsWith(r)&&t(e.substring(r.length))}},48614:(e,t)=>{"use strict";function r(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getHostname",{enumerable:!0,get:function(){return r}})},19976:(e,t)=>{"use strict";function r(e,t,r){if(e)for(let o of(r&&(r=r.toLowerCase()),e)){var i,n;if(t===(null==(i=o.domain)?void 0:i.split(":",1)[0].toLowerCase())||r===o.defaultLocale.toLowerCase()||(null==(n=o.locales)?void 0:n.some(e=>e.toLowerCase()===r)))return o}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return r}})},82823:(e,t)=>{"use strict";function r(e,t){let r;let i=e.split("/");return(t||[]).some(t=>!!i[1]&&i[1].toLowerCase()===t.toLowerCase()&&(r=t,i.splice(1,1),e=i.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return r}})},68277:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return o}});let i=r(49337),n=r(20234);function o(e,t,r,o){if(!t||t===r)return e;let a=e.toLowerCase();return!o&&((0,n.pathHasPrefix)(a,"/api")||(0,n.pathHasPrefix)(a,"/"+t.toLowerCase()))?e:(0,i.addPathPrefix)(e,"/"+t)}},49337:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return n}});let i=r(93415);function n(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:o}=(0,i.parsePath)(e);return""+t+r+n+o}},15366:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return n}});let i=r(93415);function n(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:o}=(0,i.parsePath)(e);return""+r+t+n+o}},61704:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return s}});let i=r(4864),n=r(49337),o=r(15366),a=r(68277);function s(e){let t=(0,a.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,i.removeTrailingSlash)(t)),e.buildId&&(t=(0,o.addPathSuffix)((0,n.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,n.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,o.addPathSuffix)(t,"/"):(0,i.removeTrailingSlash)(t)}},95393:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return a}});let i=r(82823),n=r(85793),o=r(20234);function a(e,t){var r,a;let{basePath:s,i18n:A,trailingSlash:u}=null!=(r=t.nextConfig)?r:{},l={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):u};s&&(0,o.pathHasPrefix)(l.pathname,s)&&(l.pathname=(0,n.removePathPrefix)(l.pathname,s),l.basePath=s);let c=l.pathname;if(l.pathname.startsWith("/_next/data/")&&l.pathname.endsWith(".json")){let e=l.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];l.buildId=r,c="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(l.pathname=c)}if(A){let e=t.i18nProvider?t.i18nProvider.analyze(l.pathname):(0,i.normalizeLocalePath)(l.pathname,A.locales);l.locale=e.detectedLocale,l.pathname=null!=(a=e.pathname)?a:l.pathname,!e.detectedLocale&&l.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(c):(0,i.normalizeLocalePath)(c,A.locales)).detectedLocale&&(l.locale=e.detectedLocale)}return l}},93415:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),i=r>-1&&(t<0||r<t);return i||t>-1?{pathname:e.substring(0,i?r:t),query:i?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},20234:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return n}});let i=r(93415);function n(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,i.parsePath)(e);return r===t||r.startsWith(t+"/")}},85793:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return n}});let i=r(20234);function n(e,t){if(!(0,i.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},4864:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948],()=>r(44082));module.exports=i})();