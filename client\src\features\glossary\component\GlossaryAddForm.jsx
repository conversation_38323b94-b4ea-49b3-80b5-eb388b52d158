"use client";

import { useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormGroup,
  FormLabel,
  Select,
  FormControl,
  MenuItem,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import { Formik, Form, ErrorMessage } from "formik";

import { RobotsMeta, VisibilityEnum } from "../../../utils/constants";
import {
  useCreateGlossary,
  useUpdateGlossaryByLanguageAndId,
} from "../hooks/glossaries.hook";
import "react-datepicker/dist/react-datepicker.css";
import CustomButton from "@/components/ui/CustomButton";
import GlossaryAddFormByLang from "./GlossaryFormByLang";
import { validationGlossarySchema } from "../validations/glossaries.validations";

const GlossaryAddForm = ({
  updateData,
  glossaryId,
  glossaryEng,
  glossaryFr,
  robotsMeta,
}) => {
  const useCreateGlossaryCreate = useCreateGlossary();
  const useCreateGlossaryUpdate = useUpdateGlossaryByLanguageAndId();

  const { t } = useTranslation();

  const formikRefAll = useRef(null);
  const [expanded, setExpanded] = useState(`panel`);

  const formdata = new FormData();
  const formdatafr = new FormData();

  const [selectedLanguages, setSelectedLanguages] = useState({
    en: updateData ? !!glossaryEng : true,
    fr: updateData ? !!glossaryFr : false,
  });

  const handleImageSelect = async (selectedFile, language) => {
    if (language === "en") {
      let uuidPhotos = uuidv4().replace(/-/g, "");
      setUuidPhotoEN(uuidPhotos);
      formdata.append("file", selectedFile);
      const extension = selectedFile.name.split(".").pop();
      setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);
    } else if (language === "fr") {
      let uuidPhotos = uuidv4().replace(/-/g, "");
      setUuidPhotoFR(uuidPhotos);
      formdatafr.append("file", selectedFile);
      const extension = selectedFile.name.split(".").pop();
      setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);
    }
  };

  const initialGlossary = {
    word: "",
    letter: "",
    url: "",
    visibility: VisibilityEnum.Public,
    content: "",
    metaTitle: "",
    metaDescription: "",
    createdAt: new Date().toISOString(),
  };

  const initialValues = {
    robotsMeta: robotsMeta || "index",
    en: glossaryEng || initialGlossary,
    fr: glossaryFr || initialGlossary,
  };

  const handleSubmit = async (values, { resetForm }) => {
    if (!selectedLanguages.en && !selectedLanguages.fr) {
      toast.error("Please select at least one version!");
      return;
    }

    try {
      const payload = {
        robotsMeta: values.robotsMeta,
        versions: {},
      };

      if (selectedLanguages.en) {
        const enData = { ...values.en };
        payload.versions.en = enData;
      }

      if (selectedLanguages.fr) {
        const frData = { ...values.fr };
        payload.versions.fr = frData;
      }

      if (updateData) {
        const updatePayload = {
          id: glossaryId,
          ...payload,
        };

        useCreateGlossaryUpdate.mutate(updatePayload);
      } else {
        useCreateGlossaryCreate.mutate(
          {
            data: payload,
          },
          {
            onSuccess: () => {
              resetForm();
              window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.glossaries.route}/`;
            },
          }
        );
      }
    } catch (error) {
      console.log(error);
      toast.error("An error occurred while processing the request.");
    }
  };

  const handleChangeAccordion = (panel) => (_, newExpanded) => {
    setExpanded(newExpanded ? panel : false);
  };

  const handleClear = () => {
    formikRefAll.current?.resetForm();
  };

  return (
    <>
      <p className="heading-h2 semi-bold">
        {updateData
          ? t("createGlossary:editGlossary")
          : t("createGlossary:addGlossary")}
      </p>
      <div id="container" className="recent-application-pentabell">
        <div className={`main-content`}>
          <div className="commun">
            <div className="inline-group">
              <label className="label-form">
                <input
                  type="checkbox"
                  checked={selectedLanguages.en}
                  onChange={() => {
                    setSelectedLanguages((prev) => ({
                      ...prev,
                      en: !prev.en,
                    }));
                    if (selectedLanguages.en && !selectedLanguages.fr) {
                      setSelectedLanguages(() => ({
                        en: false,
                        fr: true,
                      }));
                    }
                  }}
                />
                English
              </label>
              <label className="label-form">
                <input
                  type="checkbox"
                  checked={selectedLanguages.fr}
                  onChange={() => {
                    setSelectedLanguages((prev) => ({
                      ...prev,
                      fr: !prev.fr,
                    }));
                    if (!selectedLanguages.en && selectedLanguages.fr) {
                      setSelectedLanguages(() => ({
                        en: true,
                        fr: false,
                      }));
                    }
                  }}
                />
                French
              </label>
            </div>
            <div id="experiences">
              <div id="form">
                <Formik
                  initialValues={initialValues}
                  validationSchema={() =>
                    validationGlossarySchema(t, selectedLanguages)
                  }
                  innerRef={formikRefAll}
                  onSubmit={handleSubmit}
                  className="formik-form"
                  validateOnChange={true}
                  validateOnBlur={true}
                >
                  {({ errors, touched, setFieldValue, values }) => (
                    <Form>
                      <Accordion
                        key={`panel`}
                        id="accordion"
                        disableGutters
                        expanded={expanded === `panel`}
                        onChange={handleChangeAccordion(`panel`)}
                      >
                        <AccordionSummary
                          expandIcon={<ExpandMoreIcon />}
                          aria-controls={`panel-content`}
                          id={`panel-header`}
                        >
                          {t("createGlossary:settings")}
                        </AccordionSummary>
                        <AccordionDetails
                          className="accordion-detail"
                          elevation={0}
                        >
                          <div className="inline-group">
                            <div>
                              <FormGroup>
                                <FormLabel className="label-form">
                                  {"Robots meta"}
                                  <FormControl
                                    className="select-pentabell"
                                    variant="standard"
                                    sx={{ m: 1, minWidth: 120 }}
                                  >
                                    <Select
                                      value={RobotsMeta.filter(
                                        (option) => values.robotsMeta === option
                                      )}
                                      selected={values.robotsMeta}
                                      onChange={(event) => {
                                        setFieldValue(
                                          "robotsMeta",
                                          event.target.value
                                        );
                                      }}
                                    >
                                      {RobotsMeta.map((item, index) => (
                                        <MenuItem key={index} value={item}>
                                          {item}
                                        </MenuItem>
                                      ))}
                                    </Select>
                                  </FormControl>
                                  <ErrorMessage
                                    className="label-error"
                                    name="robotsMeta"
                                    component="div"
                                  />
                                </FormLabel>
                              </FormGroup>
                              <ErrorMessage
                                name="robotsMeta"
                                component="div"
                                className="label-error"
                              />
                            </div>
                          </div>
                        </AccordionDetails>
                      </Accordion>
                      <br></br>
                      {selectedLanguages.en && (
                        <GlossaryAddFormByLang
                          errors={errors}
                          touched={touched}
                          setFieldValue={setFieldValue}
                          values={values.en}
                          onImageSelect={handleImageSelect}
                          language="en"
                          update
                        />
                      )}
                      <br></br>
                      {selectedLanguages.fr && (
                        <GlossaryAddFormByLang
                          errors={errors}
                          touched={touched}
                          setFieldValue={setFieldValue}
                          values={values.fr}
                          onImageSelect={handleImageSelect}
                          language="fr"
                          update
                        />
                      )}
                      <div className="btn-container">
                        <CustomButton
                          type="button"
                          text={"Clear"}
                          className={"btn btn-filled"}
                          onClick={handleClear}
                        />
                        <CustomButton
                          type="submit"
                          text={"Save"}
                          className={"btn btn-filled"}
                        />
                      </div>
                    </Form>
                  )}
                </Formik>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default GlossaryAddForm;
