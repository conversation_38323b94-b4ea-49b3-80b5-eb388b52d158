{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|static|.*\\..*|_next).*))(.json)?[\\/#\\?]?$", "originalSource": "/((?!api|static|.*\\..*|_next).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "ILXqZLmHsp6wEy7zvo2yJ", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RactxPHZ7IyYPayevRCUeun1omSchwonxhwLgi2KJdw=", "__NEXT_PREVIEW_MODE_ID": "9188e2d378a9067ec60201781ec6fe3e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2dcc3147c802b83cec65c409b11fa162b659e039b35ba8ebb71aa72e3b14110e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "62a26a136ed69c63aa9b1d870f7193647d4bb1f062e2d38a9acf535a40c7ce96"}}}, "functions": {}, "sortedMiddleware": ["/"]}