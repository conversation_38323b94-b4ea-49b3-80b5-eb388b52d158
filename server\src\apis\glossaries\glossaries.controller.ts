import { Router, Request, Response, NextFunction } from 'express';
import multer from 'multer';

import isAuthenticated from '@/middlewares/authentication.middleware';
import { hasRoles } from '@/middlewares/authorization.middleware';
import { Language, Role } from '@/utils/helpers/constants';
import { invalidateCache, validateCache } from '@/middlewares/cache.middleware';
import { validationMiddleware } from '@/middlewares/validation.middleware';
import { glossarySchema } from './glossaries.validations';
import GlossaryService from './glossaries.service';
import { UserI } from '../user/user.interfaces';
import { GlossaryQuery } from 'types/request/GlossaryQuery';
import apiKeyMiddleware from '@/middlewares/validateApiKey.middleware';
import HttpException from '@/utils/exceptions/http.exception';

const upload = multer();
class GlossaryController {
    public readonly path = '/glossaries';
    private readonly glossaryService = new GlossaryService();
    public readonly router = Router();

    constructor() {
        this.initialiseRoutes();
    }

    private initialiseRoutes(): void {
        this.router.get(`${this.path}`, apiKeyMiddleware, validateCache, this.getAllGlossaries);
        this.router.get(`${this.path}/dashboard/:id`, apiKeyMiddleware, isAuthenticated, hasRoles([Role.ADMIN, Role.EDITEUR]), this.getGlossaryById);
        this.router.get(`${this.path}/opposite/:language/:url`, apiKeyMiddleware, this.getSlugBySlug);
        this.router.get(
            `${this.path}/dashboard/:language/:id`,
            apiKeyMiddleware,
            isAuthenticated,
            hasRoles([Role.ADMIN, Role.EDITEUR]),
            this.getGlossaryByLanguageAndId,
        );
        this.router.get(`${this.path}/:language/:url`, apiKeyMiddleware, this.getGlossaryByUrl);
        this.router.post(
            `${this.path}`,
            apiKeyMiddleware,
            isAuthenticated,
            hasRoles([Role.ADMIN, Role.EDITEUR]),
            invalidateCache,
            validationMiddleware(glossarySchema),
            this.createGlossary,
        );
        this.router.post(
            `${this.path}/import-json`,
            apiKeyMiddleware,
            isAuthenticated,
            upload.single('file'),
            hasRoles([Role.ADMIN, Role.EDITEUR]),
            invalidateCache,
            this.importJsonGlossaries,
        );
        this.router.put(
            `${this.path}/:glossaryId`,
            apiKeyMiddleware,
            isAuthenticated,
            hasRoles([Role.ADMIN, Role.EDITEUR]),
            invalidateCache,
            this.updateGlossaryVersionByLanguageAndId,
        );
        this.router.delete(
            `${this.path}/:language/:glossaryId`,
            apiKeyMiddleware,
            isAuthenticated,
            hasRoles([Role.ADMIN, Role.EDITEUR]),
            invalidateCache,
            this.deleteByLanguageAndId,
        );
    }

    private readonly importJsonGlossaries = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const currentUser = request.user as UserI;
            const file = request.file;

            const createdEntries = await this.glossaryService.importJsonGlossaries(file, currentUser);
            response.status(201).send(createdEntries);
        } catch (error) {
            next(error);
        }
    };

    private readonly createGlossary = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const glossaryData = request.body;
            const currentUser = request.user as UserI;

            if (glossaryData.en || glossaryData.fr) {
                const versions: Record<string, any> = {};

                if (glossaryData.en) {
                    versions['en'] = {
                        ...glossaryData.en,
                        language: Language.ENGLISH,
                    };
                    delete glossaryData.en;
                }

                if (glossaryData.fr) {
                    versions['fr'] = {
                        ...glossaryData.fr,
                        language: Language.FRENCH,
                    };
                    delete glossaryData.fr;
                }

                glossaryData.versions = versions;
            }

            const newGlossary = await this.glossaryService.createGlossary(glossaryData, currentUser);
            response.status(201).send(newGlossary);
        } catch (error) {
            next(error);
        }
    };

    private readonly getAllGlossaries = async (request: Request, response: Response, next: NextFunction) => {
        const queries: GlossaryQuery = request.query as unknown as GlossaryQuery;
        const language: Language = (request.query.language as Language) || Language.ENGLISH;
        const currentUser = request.user as UserI;

        try {
            const glossary = await this.glossaryService.getGlossaries(queries, language, currentUser);
            response.status(200).send(glossary);
        } catch (error) {
            next(error);
        }
    };

    private readonly getSlugBySlug = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const { url, language } = request.params;
            const glossaries = await this.glossaryService.getSlugBySlug(language, url);
            response.status(200).send(glossaries);
        } catch (error) {
            next(error);
        }
    };

    private readonly getGlossaryByLanguageAndId = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const { id, language } = request.params;
            const glossary = await this.glossaryService.getGlossaryByLanguageAndId(language, id);

            if (!glossary) {
                if (language === 'fr') {
                    response.status(204).send();
                    return;
                } else {
                    response.status(404).json({ message: 'Glossary not found' });
                    return;
                }
            }

            response.status(200).json(glossary);
        } catch (error) {
            next(error);
        }
    };

    private readonly getGlossaryByUrl = async (request: any, response: Response, next: NextFunction) => {
        try {
            const { url, language } = request.params;
            const currentUser = request.user as UserI;
            const glossaries = await this.glossaryService.getGlossaryByUrl(language, url, currentUser);
            response.send(glossaries);
        } catch (error) {
            next(error);
        }
    };

    private readonly getGlossaryById = async (request: any, response: Response, next: NextFunction) => {
        try {
            const { id } = request.params;
            const glossaries = await this.glossaryService.get(id);
            response.send(glossaries);
        } catch (error) {
            next(error);
        }
    };

    private readonly updateGlossaryVersionByLanguageAndId = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const { glossaryId } = request.params;
            const currentUser = request.user as UserI;
            const updateData = request.body;

            if (!glossaryId) {
                response.status(400).send('Missing glossary ID.');
                return;
            }

            let updatedGlossary = await this.glossaryService.updateGlossaryByLanguageAndId(glossaryId, updateData, currentUser);

            response.send(updatedGlossary);
        } catch (error) {
            next(error);
        }
    };

    private readonly deleteByLanguageAndId = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const { language, glossaryId } = request.params;
            const hardDelete = request.query.hardDelete === 'true';

            if (!language || !glossaryId) {
                throw new HttpException(400, 'Missing language or glossaryId parameter.');
            }

            let result;
            let actionMessage;

            if (hardDelete) {
                result = await this.glossaryService.deleteGlossary(language as Language, glossaryId);
                actionMessage = `deleted`;
            } else {
                result = await this.glossaryService.archiveOrRestoreGlossaryByLanguageAndId(language as Language, glossaryId);
                if (result) {
                    actionMessage = result.isArchived ? 'archived' : 'restored';
                }
            }

            if (!result) {
                throw new HttpException(404, `No glossary or version found for language ${language} with glossaryId ${glossaryId}.`);
            }

            response.send({
                message: `Version with language ${language} ${actionMessage} successfully from glossary with ID ${glossaryId}.`,
            });
        } catch (error) {
            next(error);
        }
    };
}

export default GlossaryController;
