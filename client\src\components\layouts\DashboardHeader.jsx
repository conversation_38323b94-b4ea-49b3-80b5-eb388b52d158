import NotificationComponent from "../ui/NotificationComponent";
import MyAccountDropdown from "../ui/MyAccountDropdown";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import LanguageChanger from "../languageChanger";
function DashboardHeader({ resources, locale }) {
  const { user } = useCurrentUser(false);

  return (
    <div id="dashboard-header">
      {user ? (
        <>
          <NotificationComponent locale={locale} />
       
          <MyAccountDropdown locale={locale} disableIconArrow={true} />
          <LanguageChanger withFlag={true} onlyWebVersion={true} />{" "}
        </>
      ) : null}

    
    </div>
  );
}

export default DashboardHeader;
