(()=>{var e={};e.id=58,e.ids=[58],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},85776:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,originalPathname:()=>p,pages:()=>u,routeModule:()=>x,tree:()=>c}),s(66164),s(30962),s(23658),s(54864);var r=s(23191),i=s(88716),a=s(37922),n=s.n(a),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["[locale]",{children:["(website)",{children:["activation-account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,66164)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\activation-account\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,30962)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\layout.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,23658)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54864)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\not-found.js"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\activation-account\\page.jsx"],p="/[locale]/(website)/activation-account/page",d={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/[locale]/(website)/activation-account/page",pathname:"/[locale]/activation-account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},60759:(e,t,s)=>{Promise.resolve().then(s.bind(s,91999))},65368:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a}),s(17577);var r=s(27522),i=s(10326);let a=(0,r.Z)((0,i.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},91999:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r,i=s(10326),a=s(52210),n=s(90052),o=s(95746);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)({}).hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e}).apply(null,arguments)}let c=e=>o.createElement("svg",l({xmlns:"http://www.w3.org/2000/svg",width:62,height:62,fill:"none"},e),r||(r=o.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:4,d:"M59.5 28.122v2.645A28.75 28.75 0 1 1 42.451 4.489M59.5 7.766l-28.75 28.78-8.625-8.626"})));var u=s(14822);let p=function({t:e}){return i.jsx(u.Z,{icon:c,message:"Thank You for Confirming Your Application",text:"Your application has been successfully submitted and your account is now active.",buttons:[{text:"Discover Opportunities",onClick:()=>window.location.href="/opportunities"},{text:"See Insights",onClick:()=>window.location.href="/blog"}]})},d=()=>{let{t:e}=(0,a.$G)();return i.jsx(n.Z,{id:"auth-layout",children:i.jsx(p,{t:e})})}},90052:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(10326),i=s(90423),a=s(6362);let n=function({children:e,title:t,subTitle:s}){return r.jsx("div",{id:"auth-layout",style:{backgroundImage:`url(${a.default.src})`},children:(0,r.jsxs)(i.default,{className:"container custom-max-width",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"heading-h1 text-white",children:t}),r.jsx("p",{className:"sub-heading text-white",children:s})]}),r.jsx("div",{children:e})]})})}},14822:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(10326),i=s(15082),a=s(16027);let n=function({icon:e,message:t,text:s,buttons:n=[]}){return(0,r.jsxs)("div",{children:[r.jsx("div",{className:"confirmation-dialog-icon",children:e&&r.jsx(e,{})}),r.jsx("p",{className:"text-confirmation-h1",children:t}),r.jsx("span",{className:"text-confirmation-second",children:s}),r.jsx(a.default,{container:!0,spacing:2,justifyContent:"center",children:n.map((e,t)=>r.jsx(a.default,{item:!0,xs:12,sm:6,md:6,children:r.jsx(i.default,{text:e.text,className:"btn btn-filled btn-submit full-width",type:e.type||"button",onClick:e.onClick})},t))})]})}},66164:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\app\[locale]\(website)\activation-account\page.jsx#default`)},6362:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r={src:"/_next/static/media/bg-auth.1842cff2.png",height:738,width:1440,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAElBMVEU7Tl4WKTkxRFQ/UmIlOEhOYXEF1jp9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIUlEQVR4nBXBgQ0AMAzCsEDo/y9Ps1G962gLUJpsIvmQBwVgAD+bqpS2AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:4}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,1479,1619,1336,4227,8077,6027,3718,4289,1692,1812,3969,4903],()=>s(85776));module.exports=r})();