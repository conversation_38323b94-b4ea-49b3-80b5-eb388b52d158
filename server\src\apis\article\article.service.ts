import * as fs from 'fs';
import mime from 'mime-types';
import { v4 as uuidv4 } from 'uuid';

import articleModel from './article.model';
import { ArticleI, ArticleVersion } from './article.interface';
import mongoose, { Types } from 'mongoose';
import { Language, Role, Visibility } from '@/utils/helpers/constants';
import HttpException from '@/utils/exceptions/http.exception';
import articleCategoryModel from './category/article.category.model';
import { ArticleQuery } from 'types/request/ArticleQuery';
import axios from 'axios';
import { delay, parseFrenchDate } from '@/utils/helpers/functions';
import FilesService from '../storage/files.service';
import { getCategoryUrlByIdVersion } from '@/utils/helpers/functions';
import path from 'path';
import { UserI } from '../user/user.interfaces';

class ArticleService {
    private readonly fileService = new FilesService();
    private Category = articleCategoryModel;

    public async createArticle(articleData: any): Promise<any> {
        const existingUrls: Set<string> = new Set();
        const existingArticles = await articleModel.find({}, 'versions.url');
        existingArticles.forEach(existingArticle => {
            existingArticle.versions.forEach(version => {
                existingUrls.add(version.url);
            });
        });

        const defaultLanguage: Language = Language.ENGLISH;

        for (const version of articleData.versions) {
            if (!version.language) {
                version.language = defaultLanguage;
            }
            let url = version.url || version.title.toLowerCase().replace(/\s+/g, '-');
            let count = 0;
            if (existingUrls.has(url)) {
                count = 1;
                while (existingUrls.has(`${url}-${count}`)) {
                    count++;
                }
                url = `${url}-${count}`;
            }
            existingUrls.add(url);
            version.url = url.toLowerCase();

            version.alt = version.alt || version.title;
        }

        const articleVersions: ArticleVersion[] = articleData.versions.map((versionData: any) => {
            versionData.category = versionData.category || [];
            return {
                ...versionData,
                category: versionData.category.map((catId: any) => new mongoose.Types.ObjectId(catId)),
            };
        });

        const newArticle = new articleModel({ ...articleData, versions: articleVersions });
        const savedArticle = await newArticle.save();

        for (const version of savedArticle.versions) {
            for (const categoryId of version.category) {
                const categoryVersion = await articleCategoryModel.findOne({
                    'versionscategory._id': categoryId,
                });

                if (categoryVersion) {
                    const targetCategoryVersion = categoryVersion.versionscategory.find((ver: any) => ver._id.equals(categoryId));

                    if (targetCategoryVersion && !targetCategoryVersion.articles.includes(version._id)) {
                        targetCategoryVersion.articles.push(version._id);
                        await categoryVersion.save();
                    }
                }
            }
        }

        return savedArticle;
    }

    public async createArticleautoSave(articleData: any): Promise<any> {
        const existingUrls: Set<string> = new Set();
        const existingArticles = await articleModel.find({}, 'versions.url');
        existingArticles.forEach(existingArticle => {
            existingArticle.versions.forEach(version => {
                existingUrls.add(version.url);
            });
        });
        const defaultLanguage: Language = Language.ENGLISH;

        for (const version of articleData.versions) {
            if (!version.language) {
                version.language = defaultLanguage;
            }

            let url = version.url || version.title.toLowerCase().replace(/\s+/g, '-');
            let count = 0;
            if (existingUrls.has(url)) {
                count = 1;
                while (existingUrls.has(`${url}-${count}`)) {
                    count++;
                }
                url = `${url}-${count}`;
            }
            existingUrls.add(url);
            version.url = url.toLowerCase();

            version.alt = version.alt || version.title;
        }

        const articleVersions: ArticleVersion[] = articleData.versions.map((versionData: any) => {
            versionData.category = versionData.category || [];
            return {
                ...versionData,
                category: versionData.category.map((catId: any) => new mongoose.Types.ObjectId(catId)),
            };
        });

        const newArticle = new articleModel({ ...articleData, versions: articleVersions });
        const savedArticle = await newArticle.save();

        for (const version of savedArticle.versions) {
            for (const categoryId of version.category) {
                const categoryVersion = await articleCategoryModel.findOne({
                    'versionscategory._id': categoryId,
                });

                if (categoryVersion) {
                    const targetCategoryVersion = categoryVersion.versionscategory.find((ver: any) => ver._id.equals(categoryId));

                    if (targetCategoryVersion && !targetCategoryVersion.articles.includes(version._id)) {
                        targetCategoryVersion.articles.push(version._id);
                        await categoryVersion.save();
                    }
                }
            }
        }

        return { articleId: savedArticle._id };
    }

    public async getArticlesDashboard(queries: any, language: Language): Promise<any> {
        const { paginated = 'true', searchQuery, sortOrder = 'desc', isArchived, visibility, publishDate, createdAt, categoryName } = queries;

        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 5;

        const queryConditions: any = {};

        if (categoryName) {
            const category = await articleCategoryModel
                .findOne({
                    'versionscategory.language': language,
                    'versionscategory.name': categoryName,
                })
                .exec();

            if (!category) throw new HttpException(404, `Category not found for language ${language} and name ${categoryName}`);

            const categoryVersion = category.versionscategory.find(version => version.language === language && version.name === categoryName);

            if (!categoryVersion) {
                throw new HttpException(404, `Category version not found for language ${language} and name ${categoryName}`);
            }

            queryConditions['versions._id'] = { $in: categoryVersion.articles };
        }

        if (createdAt) {
            const date = new Date(createdAt);
            queryConditions['createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }

        if (searchQuery) {
            const queryCondition = [{ 'versions.title': new RegExp(`.*${searchQuery}.*`, 'i') }, { 'versions.keywords': { $in: [searchQuery] } }];
            const linkCondition = searchQuery.includes('https://') ? [{ 'versions.content': RegExp(searchQuery) }] : queryCondition;
            queryConditions['$or'] = linkCondition;
        }

        if (language) queryConditions['versions.language'] = language;
        if (visibility) queryConditions['versions.visibility'] = visibility;
        if (isArchived) queryConditions['versions.isArchived'] = isArchived;

        if (publishDate) {
            const date = new Date(publishDate);
            queryConditions['versions.createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }

        const sortCriteria: any = { 'versions.createdAt': sortOrder === 'asc' ? 1 : -1 };

        const skip = (pageNumber - 1) * pageSize;

        const articlesQuery = articleModel.find(queryConditions).sort(sortCriteria).skip(skip).limit(pageSize);
        const articles = await articlesQuery.lean();

        const filteredArticles: any = await Promise.all(
            articles.map(async article => {
                const filteredVersions: any = article.versions.filter(
                    (version: ArticleVersion) => version.language === language && (!visibility || version.visibility === visibility),
                );

                const existingLanguages = article.versions.map((version: ArticleVersion) => version.language);

                const category = await getCategoryUrlByIdVersion(filteredVersions[0]?.category[0]);

                return {
                    ...article,
                    versions: filteredVersions,
                    existingLanguages: Array.from(new Set(existingLanguages)),
                    category,
                };
            }),
        );

        const finalArticles = filteredArticles.filter((article: any) => article.versions.length > 0);

        if (paginated === 'false') {
            return finalArticles;
        }

        const totalArticles = await articleModel.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalArticles / pageSize);

        return {
            pageNumber,
            pageSize,
            totalPages,
            totalArticles,
            articles: finalArticles,
        };
    }

    public async updateArticleAuto(articleId: string, articleData: any): Promise<any> {
        const existingArticle = await articleModel.findById(articleId);

        if (!existingArticle) {
            throw new HttpException(404, 'Article not found');
        }

        const existingUrls: Set<string> = new Set();
        const existingArticles = await articleModel.find({ _id: { $ne: articleId } }, 'versions.url');
        existingArticles.forEach(article => {
            article.versions.forEach(version => {
                existingUrls.add(version.url);
            });
        });

        for (const newVersion of articleData.versions) {
            const index = existingArticle.versions.findIndex(v => v.language === newVersion.language);

            let url = newVersion.url || newVersion.title?.toLowerCase().replace(/\s+/g, '-');
            let count = 0;
            if (existingUrls.has(url)) {
                count = 1;
                while (existingUrls.has(`${url}-${count}`)) {
                    count++;
                }
                url = `${url}-${count}`;
            }
            existingUrls.add(url);

            if (index !== -1) {
                existingArticle.versions[index] = {
                    ...existingArticle.versions[index],
                    ...newVersion,
                    url: url,
                    updatedAt: new Date(),
                };
            } else {
                existingArticle.versions.push({
                    language: newVersion.language!,
                    title: newVersion.title!,
                    url: url,
                    alt: newVersion.alt || newVersion.title,
                    content: newVersion.content || '',
                    category: newVersion.category || [],
                    metaTitle: newVersion.metaTitle || '',
                    metaDescription: newVersion.metaDescription || '',
                    image: newVersion.image || '',
                    keywords: newVersion.keywords || [],
                    shareOnSocialMedia: newVersion.shareOnSocialMedia || false,
                    _id: new Types.ObjectId(),
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    visibility: newVersion.visibility || true,
                    canonical: newVersion.canonical || '',
                    publishDate: newVersion.publishDate || new Date(),
                    isArchived: newVersion.isArchived || false,
                    highlights: newVersion.highlights || [],
                    description: newVersion.description || '',
                    faqTitle: newVersion.faqTitle || '',
                    faq: newVersion.faq || [],
                });
            }
        }

        return await existingArticle.save();
    }

    public async updateMainfieldsArticle(articleId: string, updateData: any): Promise<any> {
        const articleToUpdate = await articleModel.findById(articleId).lean();

        if (!articleToUpdate) throw new HttpException(404, 'Article not found.');

        updateData.versions = undefined;

        const updatedArticle = await articleModel.findByIdAndUpdate(articleId, { $set: updateData }, { new: true });

        return updatedArticle?.toObject();
    }

    public async getArticlesByCategory(language: string, urlCategory: string, queries: ArticleQuery): Promise<any> {
        const { title, visibility = 'Public', publishDate, sortOrder = 'desc', pageNumber = 1, pageSize = 10, createdAt } = queries;

        const category = await articleCategoryModel
            .findOne({
                'versionscategory.language': language,
                'versionscategory.url': urlCategory,
            })
            .lean();
        if (!category) throw new HttpException(404, `Category not found for language ${language} and url ${urlCategory}`);

        const categoryVersion = category.versionscategory.find(version => version.language === language && version.url === urlCategory);
        if (!categoryVersion) throw new HttpException(404, `Category version not found for language ${language} and url ${urlCategory}`);

        const queryConditions: any = {
            'versions.category': categoryVersion._id,
            'versions.visibility': visibility,
        };

        if (createdAt) {
            const date = new Date(createdAt);
            queryConditions['createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }

        if (title) {
            queryConditions['versions.title'] = RegExp(`.*${title}.*`, 'i');
        }

        if (publishDate) {
            const date = new Date(publishDate);
            queryConditions['publishDate'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }

        const sortCriteria: any = {};
        if (sortOrder) {
            sortCriteria['createdAt'] = sortOrder === 'asc' ? 1 : -1;
        }

        const skip = (pageNumber - 1) * pageSize;

        const articlesQuery = articleModel.find(queryConditions).sort(sortCriteria).skip(skip).limit(pageSize);

        const articles = await articlesQuery.lean();

        const filteredArticles: any = await Promise.all(
            articles.map(async article => {
                const versions = article.versions.filter(
                    (version: ArticleVersion) =>
                        version.language === language && version.visibility === 'Public' && (!title || RegExp(`.*${version.title}.*`, 'i')),
                );

                const category = await getCategoryUrlByIdVersion(versions[0]?.category[0]);

                return {
                    ...article,
                    versions,
                    category,
                };
            }),
        );

        filteredArticles.filter((article: any) => article.versions.length > 0);

        const totalArticles = await articleModel.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalArticles / pageSize);

        return {
            pageNumber,
            pageSize,
            totalPages,
            totalArticles,
            articles: filteredArticles,
        };
    }

    public async deleteArticle(language: Language, articleId: string): Promise<void> {
        const categoryToDeleteFrom = await articleModel.findById(articleId);

        if (!categoryToDeleteFrom) throw new HttpException(404, `No category found with ID ${articleId}.`);

        const versionIndex = categoryToDeleteFrom.versions.findIndex(version => version.language === language);

        if (versionIndex === -1) {
            throw new HttpException(404, `No version found with language ${language} for category ID ${articleId}.`);
        }

        categoryToDeleteFrom.versions.splice(versionIndex, 1);

        await categoryToDeleteFrom.save();

        if (categoryToDeleteFrom.versions.length === 0) {
            await articleModel.deleteOne({ _id: articleId });
        }
    }

    public async getArchivedArticles(queries: ArticleQuery, language: Language): Promise<any> {
        const {
            paginated = 'true',
            searchQuery,
            sortOrder = 'desc',
            isArchived = true,
            visibility,
            publishDate,
            pageNumber = 1,
            pageSize = 10,
            createdAt,
            categoryName,
        } = queries;

        const queryConditions: any = {};

        if (categoryName) {
            const category = await articleCategoryModel
                .findOne({
                    'versionscategory.language': language,
                    'versionscategory.name': categoryName,
                })
                .exec();

            if (!category) throw new HttpException(404, `Category not found for language ${language} and name ${categoryName}`);

            const categoryVersion = category.versionscategory.find(version => version.language === language && version.name === categoryName);
            if (!categoryVersion) throw new HttpException(404, `Category version not found for language ${language} and name ${categoryName}`);

            queryConditions['versions._id'] = { $in: categoryVersion.articles };
        }

        if (createdAt) {
            const date = new Date(createdAt);
            queryConditions['createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }

        if (searchQuery) {
            queryConditions['$or'] = [{ 'versions.title': new RegExp(`.*${searchQuery}.*`, 'i') }, { 'versions.keywords': { $in: [searchQuery] } }];
        }

        if (language) {
            queryConditions['versions.language'] = language;
        }

        if (visibility) {
            queryConditions['versions.visibility'] = visibility;
        }

        if (publishDate) {
            const date = new Date(publishDate);
            queryConditions['publishDate'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }

        if (isArchived) {
            queryConditions['versions.isArchived'] = isArchived;
        }

        const sortCriteria: any = {};
        if (sortOrder) {
            sortCriteria['createdAt'] = sortOrder === 'asc' ? 1 : -1;
        }

        const skip = (pageNumber - 1) * pageSize;

        const articlesQuery = articleModel.find(queryConditions).sort(sortCriteria).skip(skip).limit(pageSize);

        const articles = await articlesQuery.lean();

        const filteredArticles = articles
            .map(article => {
                const filteredVersions = article.versions.filter(
                    (version: ArticleVersion) =>
                        version.language === language &&
                        (!visibility || version.visibility === visibility) &&
                        (!searchQuery || RegExp(`.*${searchQuery}.*`, 'i').test(version.title) || version.keywords.includes(searchQuery)) &&
                        version.isArchived,
                );

                const existingLanguages = article.versions.map((version: ArticleVersion) => version.language);

                return {
                    ...article,
                    versions: filteredVersions,
                    existingLanguages: Array.from(new Set(existingLanguages)),
                };
            })
            .filter(article => !article.isArchived && article.versions.length > 0);

        if (paginated === 'false') {
            return filteredArticles;
        }

        const totalArticles = await articleModel.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalArticles / pageSize);

        return {
            pageNumber: pageNumber as number,
            pageSize: pageSize as number,
            totalPages: totalPages as number,
            totalArticles: totalArticles as number,
            articles: filteredArticles,
        };
    }

    public async archivedallArticle(articleId: string): Promise<ArticleI | null> {
        try {
            const article = await articleModel.findById(articleId).exec();

            if (!article) {
                return null;
            }

            article.versions.forEach(version => {
                version.isArchived = true;
                version.visibility = Visibility.Private;
            });

            await article.save();

            return article;
        } catch (error) {
            console.error(`Failed to archive article`, error);
            throw new Error('Failed to archive article');
        }
    }

    public async getArticleByUrl(language: string, url: string, currentUser: UserI): Promise<any> {
        const articles = await articleModel
            .aggregate([
                { $match: { 'versions.language': language, 'versions.url': url.toLocaleLowerCase() } },
                { $unwind: '$versions' },
                { $match: { 'versions.language': language, 'versions.url': url.toLocaleLowerCase() } },
                { $unwind: { path: '$versions.category', preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: 'categories',
                        let: { categoryId: '$versions.category' },
                        pipeline: [
                            { $unwind: '$versionscategory' },
                            { $match: { $expr: { $eq: ['$versionscategory._id', '$$categoryId'] } } },
                            {
                                $project: {
                                    id: '$versionscategory._id',
                                    name: '$versionscategory.name',
                                    url: '$versionscategory.url',
                                },
                            },
                        ],
                        as: 'categoryDetails',
                    },
                },
                { $unwind: { path: '$categoryDetails', preserveNullAndEmptyArrays: true } },
                {
                    $group: {
                        _id: {
                            id: '$_id',
                            idArticle: '$versions._id',
                            language: '$versions.language',
                            url: '$versions.url',
                            title: '$versions.title',
                            description: '$versions.description',
                            keywords: '$versions.keywords',
                            metaTitle: '$versions.metaTitle',
                            metaDescription: '$versions.metaDescription',
                            alt: '$versions.alt',
                            content: '$versions.content',
                            image: '$versions.image',
                            tags: '$tags',
                            isArchived: `$versions.isArchived`,
                            visibility: `$versions.visibility`,
                            publishDate: '$versions.publishDate',
                            highlights: '$versions.highlights',
                            robotsMeta: '$robotsMeta',
                            createdAt: '$createdAt',
                            updatedAt: '$updatedAt',
                        },
                        categories: {
                            $push: {
                                id: '$categoryDetails.id',
                                name: '$categoryDetails.name',
                                url: '$categoryDetails.url',
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: '$_id.id',
                        versions: {
                            language: '$_id.language',
                            idArticle: '$_id.idArticle',
                            title: '$_id.title',
                            description: '$_id.description',
                            keywords: '$_id.keywords',
                            highlights: '$_id.highlights',
                            metaTitle: '$_id.metaTitle',
                            metaDescription: '$_id.metaDescription',
                            isArchived: `$_id.isArchived`,
                            visibility: `$_id.visibility`,
                            publishDate: '$_id.publishDate',
                            updatedAt: '$_id.updatedAt',
                            content: '$_id.content',
                            url: '$_id.url',
                            alt: '$_id.alt',
                            image: '$_id.image',
                            categories: {
                                $cond: {
                                    if: { $eq: ['$categories', [null]] },
                                    then: [],
                                    else: '$categories',
                                },
                            },
                        },
                        tags: '$_id.tags',
                        robotsMeta: '$_id.robotsMeta',
                        createdAt: '$_id.createdAt',
                        updatedAt: '$_id.updatedAt',
                    },
                },
            ])
            .exec();

        if (articles.length === 0) throw new HttpException(404, 'No articles found');

        const article = articles[0];

        if (
            (article.versions.visibility !== 'Public' && (!currentUser || currentUser?.roles?.includes(Role.CANDIDATE))) ||
            article.versions.isArchived === true
        )
            throw new HttpException(404, 'No articles found');

        return {
            _id: article._id,
            versions: [article.versions],
            tags: article.tags || [],
            robotsMeta: article.robotsMeta || '',
            createdAt: article.createdAt,
            updatedAt: article.updatedAt,
        };
    }

    public async getSlugBySlug(language: string, url: string): Promise<{ slug: string }> {
        const article = await articleModel.findOne({ 'versions.language': language, 'versions.url': url.toLocaleLowerCase() });
        if (!article) throw new HttpException(404, 'No article found');

        const targetLanguage = language === 'en' ? 'fr' : 'en';
        const selectedVersions = article.versions.filter(version => version.language === targetLanguage);

        if (selectedVersions.length === 0) throw new HttpException(404, 'No article version found');

        return {
            slug: selectedVersions[0].url,
        };
    }

    public async updateArticleByLanguageAndId(language: Language, articleId: string, updateData: Partial<any>): Promise<any> {
        const articleToUpdate = await articleModel.findById(articleId);

        if (!articleToUpdate) throw new HttpException(404, `No article found with ID ${articleId}.`);

        const existingUrls: Set<string> = new Set();
        const existingArticles = await articleModel.find({}, 'versions.url');
        existingArticles.forEach(existingArticle => {
            existingArticle.versions.forEach(version => {
                existingUrls.add(version.url);
            });
        });

        let updatedVersion: ArticleVersion | null = null;

        for (const version of articleToUpdate.versions) {
            if (version.language === language) {
                const titleChanged = updateData.title && updateData.title !== version.title;

                if (titleChanged) {
                    const titleExists = await articleModel.exists({ 'versions.title': updateData.title });
                    if (titleExists) {
                        let newUrl = version.url || updateData.title.toLowerCase().replace(/\s+/g, '-');
                        let count = 0;
                        while (existingUrls.has(newUrl)) {
                            count++;
                            newUrl = `${updateData.title.toLowerCase().replace(/\s+/g, '-')}-${count}`;
                        }
                        version.url = newUrl;
                        existingUrls.add(newUrl);
                    } else {
                        version.url = updateData.url || updateData.title.toLowerCase().replace(/\s+/g, '-');
                    }
                }

                Object.assign(version, updateData);

                if (titleChanged || updateData.alt) {
                    version.alt = updateData.alt || updateData.title;
                }

                version.updatedAt = new Date();
                updatedVersion = version;
                break;
            }
        }

        if (!updatedVersion) throw new HttpException(404, `No version found for language ${language} in article with ID ${articleId}.`);

        await articleToUpdate.save();

        if (updatedVersion.category.length > 0) {
            for (const categoryId of updatedVersion.category) {
                const categoryVersion = await articleCategoryModel.findOne({ 'versionscategory._id': categoryId });

                if (categoryVersion) {
                    const targetCategoryVersionIndex = categoryVersion.versionscategory.findIndex((ver: any) => ver._id.equals(categoryId));

                    if (targetCategoryVersionIndex !== -1) {
                        const targetCategoryVersion = categoryVersion.versionscategory[targetCategoryVersionIndex];

                        if (!targetCategoryVersion.articles.includes(updatedVersion._id)) {
                            targetCategoryVersion.articles.push(updatedVersion._id);
                            await categoryVersion.save();
                        }
                    }
                }
            }
        }

        return articleToUpdate;
    }

    public async updateArticleVersion(articleId: string, language: Language, versionData: Partial<any>): Promise<any> {
        const existingArticle = await articleModel.findById(articleId);

        if (!existingArticle) throw new HttpException(404, 'Article not found');

        const existingUrls: Set<string> = new Set();
        const existingArticles = await articleModel.find({ _id: { $ne: articleId } }, 'versions.url');
        existingArticles.forEach(article => {
            article.versions.forEach(version => {
                existingUrls.add(version.url);
            });
        });

        let isVersionExisting = false;

        for (const version of existingArticle.versions) {
            if (version.language === language) {
                isVersionExisting = true;
                break;
            }
        }

        if (isVersionExisting) {
            return await this.updateArticleByLanguageAndId(language, articleId, versionData);
        } else {
            const newVersion: ArticleVersion = {
                language: language,
                title: versionData.title,
                url: versionData.url || versionData.title.toLowerCase().replace(/\s+/g, '-'),
                alt: versionData.alt || versionData.title,
                content: versionData.content,
                category: versionData.category || [],
                metaTitle: versionData.metaTitle,
                metaDescription: versionData.metaDescription,
                image: versionData.image,
                keywords: versionData.keywords || '',
                shareOnSocialMedia: versionData.shareOnSocialMedia,
                _id: new Types.ObjectId(),
                createdAt: versionData.publishDate,
                updatedAt: new Date(),
                visibility: versionData.visibility,
                canonical: versionData.canonical,
                publishDate: versionData.publishDate,
                // createdBy: versionData.createdBy,
                isArchived: versionData.isArchived,
                highlights: versionData.highlights || [],
                description: versionData.description || '',
                faqTitle: versionData.faqTitle || '',
                faq: versionData.faq || [],
            };

            return await this.addVersionToArticle(articleId, newVersion);
        }
    }

    public async archiverArticleByLanguageAndId(language: Language, articleId: string): Promise<any> {
        const articleToDeleteFrom = await articleModel.findById(articleId);

        if (!articleToDeleteFrom) throw new HttpException(404, `No article found with ID ${articleId}.`);

        const versionIndex = articleToDeleteFrom.versions.findIndex(version => version.language === language);

        if (versionIndex === -1) throw new HttpException(404, `No version found with language ${language} for article ID ${articleId}.`);

        const versionToDelete = articleToDeleteFrom.versions[versionIndex];

        versionToDelete.isArchived = true;
        await articleToDeleteFrom.save();

        const nonArchivedVersions = articleToDeleteFrom.versions.filter(version => !version.isArchived);
        if (nonArchivedVersions.length === 0) {
            articleToDeleteFrom.isArchived = true;
            await articleToDeleteFrom.save();
        }

        return articleToDeleteFrom;
    }

    public async toggleArchiveStatusByLanguageAndId(language: Language, articleId: string, archive: boolean): Promise<any> {
        const articleToModify = await articleModel.findById(articleId);

        if (!articleToModify) throw new HttpException(404, `No article found with ID ${articleId}.`);

        const versionIndex = articleToModify.versions.findIndex(version => version.language === language);

        if (versionIndex === -1) throw new HttpException(404, `No version found with language ${language} for article ID ${articleId}.`);

        const versionToModify = articleToModify.versions[versionIndex];

        versionToModify.isArchived = archive;
        await articleToModify.save();

        const nonArchivedVersions = articleToModify.versions.filter(version => !version.isArchived);

        if (nonArchivedVersions.length === 0) {
            articleToModify.isArchived = archive;
            await articleToModify.save();
        }

        return articleToModify;
    }

    public async addVersionToArticle(articleId: string, newVersion: any): Promise<any> {
        const existingArticle = await articleModel.findById(articleId);
        if (!existingArticle) throw new HttpException(404, 'Article not found');

        const existingUrls: Set<string> = new Set();
        const existingArticles = await articleModel.find({}, 'versions.url');
        existingArticles.forEach(existingArticle => {
            existingArticle.versions.forEach(version => {
                existingUrls.add(version.url);
            });
        });

        if (!newVersion.url) {
            newVersion.url = `${newVersion.title.toLowerCase().replace(/\s+/g, '-')}`;
        }
        let tempUrl = newVersion.url;
        let count = 1;

        while (existingUrls.has(tempUrl)) {
            tempUrl = `${newVersion.url}-${count}`;
            count++;
        }
        newVersion.url = tempUrl;

        newVersion.alt = newVersion.alt || newVersion.title;

        existingArticle.versions.push(newVersion);

        const updatedArticle = await existingArticle.save();

        for (const version of updatedArticle.versions) {
            for (const categoryId of version.category) {
                const categoryVersion = await articleCategoryModel.findOne({ 'versionscategory._id': categoryId });
                if (categoryVersion) {
                    const targetCategoryVersion = categoryVersion.versionscategory.find(ver => ver._id.equals(categoryId));
                    if (targetCategoryVersion && !targetCategoryVersion.articles.includes(version._id)) {
                        targetCategoryVersion.articles.push(version._id);
                        await categoryVersion.save();
                    }
                }
            }
        }

        return updatedArticle;
    }

    public async getArticles(queries: any, language: Language): Promise<any> {
        const {
            paginated = 'true',
            searchQuery,
            sortOrder = 'desc',
            isArchived = 'false',
            visibility = Visibility.Public,
            publishDate,
            createdAt,
            categoryName,
            isThreeLastArticles,
        } = queries;

        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 5;

        const queryConditions: any = {};

        if (categoryName) {
            const category = await articleCategoryModel
                .findOne({
                    'versionscategory.language': language,
                    'versionscategory.name': categoryName,
                })
                .exec();

            if (!category) throw new HttpException(404, `Category not found for language ${language} and name ${categoryName}`);

            const categoryVersion = category.versionscategory.find(version => version.language === language && version.name === categoryName);

            if (!categoryVersion) {
                throw new HttpException(404, `Category version not found for language ${language} and name ${categoryName}`);
            }

            queryConditions['versions._id'] = { $in: categoryVersion.articles };
        }
        if (createdAt) {
            const date = new Date(createdAt);
            queryConditions['createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }

        if (searchQuery) {
            queryConditions['$or'] = [{ 'versions.title': new RegExp(`.*${searchQuery}.*`, 'i') }, { 'versions.keywords': { $in: [searchQuery] } }];
        }

        if (language) {
            queryConditions['versions.language'] = language;
        }

        if (visibility) {
            queryConditions['versions.visibility'] = visibility;
        }

        if (isArchived) {
            queryConditions['versions.isArchived'] = isArchived;
        }

        if (publishDate) {
            const date = new Date(publishDate);
            queryConditions['publishDate'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }
        const sortCriteria: any = {};
        if (sortOrder) {
            sortCriteria['createdAt'] = sortOrder === 'asc' ? 1 : -1;
        }

        const skip = (pageNumber - 1) * pageSize;

        const articlesQuery = articleModel.find(queryConditions);

        const articles = await articlesQuery.lean();

        const filteredArticles: any = await Promise.all(
            articles.map(async article => {
                const filteredVersions: any = article.versions.filter(
                    (version: ArticleVersion) =>
                        version.language === language &&
                        (!visibility || version.visibility === visibility) &&
                        (!searchQuery || new RegExp(`.*${searchQuery}.*`, 'i').test(version.title) || version.keywords.includes(searchQuery)),
                );

                const existingLanguages = article.versions.map((version: ArticleVersion) => version.language);

                const category = await getCategoryUrlByIdVersion(filteredVersions[0]?.category[0]);

                return {
                    ...article,
                    versions: filteredVersions,
                    existingLanguages: Array.from(new Set(existingLanguages)),
                    category,
                };
            }),
        );

        if (paginated === 'false') {
            return filteredArticles.sort((a: any, b: any) => {
                return b.versions[0].publishDate - a.versions[0].publishDate;
            }) as ArticleI[];
        }

        const sortedArticleList = filteredArticles
            .filter((article: any) => article.versions.length > 0)
            .sort((a: any, b: any) => {
                return (new Date(b.versions[0].publishDate) as any) - (new Date(a.versions[0].publishDate) as any);
            });

        if (isThreeLastArticles) {
            const threeLastArticles = sortedArticleList.slice(0, 3) as ArticleI[];
            return {
                articles: threeLastArticles,
            };
        }

        let firstArticle;

        if (!searchQuery && pageNumber === 1) firstArticle = sortedArticleList.shift();

        const paginatedArticle = sortedArticleList.slice(skip, skip + Number(pageSize)) as ArticleI[];

        const totalArticles = await articleModel.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalArticles / pageSize);

        return {
            pageNumber: Number(pageNumber),
            pageSize: Number(pageSize),
            totalPages,
            totalArticles,
            firstArticle: !searchQuery && pageNumber === 1 ? firstArticle : null,
            articles: paginatedArticle,
        };
    }

    public async getArticleWithPrevAndNext(language: string, url: string): Promise<any> {
        const queryConditions: any = { 'versions.url': url, 'versions.language': language };

        const currentArticle = await articleModel.findOne(queryConditions).exec();

        if (!currentArticle) throw new HttpException(404, 'Article not found');

        const currentArticleDate = currentArticle.createdAt;

        const prevQueryConditions: any = {
            createdAt: { $lt: currentArticleDate },
            'versions.language': language,
        };

        const nextQueryConditions: any = {
            createdAt: { $gt: currentArticleDate },
            'versions.language': language,
        };

        const [previousArticle, nextArticle] = await Promise.all([
            articleModel.findOne(prevQueryConditions).sort({ createdAt: -1 }).limit(1).exec(),
            articleModel.findOne(nextQueryConditions).sort({ createdAt: 1 }).limit(1).exec(),
        ]);

        const extractFields = (article: any) => {
            if (!article) return null;
            const version = article.versions.find((version: any) => version.language === language);
            return version ? { title: version.title, url: version.url } : null;
        };

        const previousArticleData = extractFields(previousArticle);
        const nextArticleData = extractFields(nextArticle);

        return {
            previousArticle: previousArticleData,
            nextArticle: nextArticleData,
        };
    }

    public checkAndUpdateVisibility = async () => {
        const articlesToUpdate = await articleModel.find({
            'versions.publishDate': { $lte: new Date() },
            'versions.status': Visibility.Draft,
        });

        for (const article of articlesToUpdate) {
            for (const version of article.versions) {
                if (version.publishDate <= new Date() && version.visibility === Visibility.Draft) {
                    version.visibility = Visibility.Public;
                }
            }

            await article.save();
        }
    };

    // public async deleteCategoryVersionAndHandleArticles(categoryVersionId: string, newCategoryVersionId: string | null = null): Promise<void> {
    //     try {
    //         const categoryVersionObjectId = new Types.ObjectId(categoryVersionId);
    //         const category = await articleCategoryModel.findOne({ 'versionscategory._id': categoryVersionObjectId });
    //         if (!category) {
    //             throw new HttpException(404, 'Category version not found');
    //         }

    //         const versionToDelete = category.versionscategory.find((v: any) => v._id.equals(categoryVersionObjectId));
    //         if (!versionToDelete) {
    //             throw new HttpException(404, 'Category version not found in versionscategory');
    //         }

    //         if (newCategoryVersionId) {
    //             const newCategoryVersionObjectId = new Types.ObjectId(newCategoryVersionId);

    //             const articleUpdateResultAdd = await articleModel.updateMany(
    //                 { 'versions.category': categoryVersionObjectId },
    //                 { $set: { 'versions.$[elem].category': newCategoryVersionObjectId } },
    //                 { arrayFilters: [{ 'elem.category': categoryVersionObjectId }] },
    //             );
    //             console.log('Articles update result (add new version):', articleUpdateResultAdd);

    //             const updateNewCategoryVersion = await articleCategoryModel.updateOne(
    //                 { 'versionscategory._id': newCategoryVersionObjectId },
    //                 { $addToSet: { 'versionscategory.$.articles': { $each: versionToDelete.articles } } },
    //             );
    //             console.log('Update new category version with articles:', updateNewCategoryVersion);
    //         }

    //         const categoryUpdateResult = await articleCategoryModel.updateOne(
    //             { _id: category._id },
    //             { $pull: { versionscategory: { _id: categoryVersionObjectId } } },
    //         );
    //         console.log('Category update result:', categoryUpdateResult);

    //         const articleUpdateResultRemove = await articleModel.updateMany(
    //             { 'versions.category': categoryVersionObjectId },
    //             { $pull: { versions: { category: categoryVersionObjectId } } },
    //         );
    //         console.log('Articles update result (remove version):', articleUpdateResultRemove);
    //     } catch (error) {
    //         console.error('Error deleting category version and handling articles:', error);
    //         throw new HttpException(500, 'Failed to delete category version and handle associated articles');
    //     }
    // }
    public async deleteCategoryVersionAndHandleArticles(categoryVersionId: string, newCategoryVersionId: string | null = null): Promise<void> {
        try {
            const categoryVersionObjectId = new Types.ObjectId(categoryVersionId);

            const category = await articleCategoryModel.findOne({ 'versionscategory._id': categoryVersionObjectId });
            if (!category) {
                throw new HttpException(404, 'Category version not found');
            }

            const versionToDelete = category.versionscategory.find((v: any) => v._id.equals(categoryVersionObjectId));
            if (!versionToDelete) {
                throw new HttpException(404, 'Category version not found in versionscategory');
            }

            // If a new category version ID is provided, transfer articles
            if (newCategoryVersionId) {
                const newCategoryVersionObjectId = new Types.ObjectId(newCategoryVersionId);

                // Update articles to replace the old category version with the new one, avoiding duplication
                const articleUpdateResultAdd = await articleModel.updateMany(
                    {
                        $and: [
                            { 'versions.category': categoryVersionObjectId }, // Check for the old category version
                            { 'versions.category': { $ne: newCategoryVersionObjectId } }, // Ensure the new category version is not already present
                        ],
                    },
                    {
                        $set: {
                            'versions.$[elem].category.$[cat]': newCategoryVersionObjectId,
                        },
                    },
                    {
                        arrayFilters: [{ 'elem.category': categoryVersionObjectId }, { cat: categoryVersionObjectId }],
                    },
                );

                console.log('Articles update result (add new version):', articleUpdateResultAdd);

                // Add the articles to the new category version
                const updateNewCategoryVersion = await articleCategoryModel.updateOne(
                    { 'versionscategory._id': newCategoryVersionObjectId },
                    { $addToSet: { 'versionscategory.$.articles': { $each: versionToDelete.articles } } },
                );
                console.log('Update new category version with articles:', updateNewCategoryVersion);
            }

            // Check if this is the last version in the category
            if (category.versionscategory.length === 1) {
                // Delete the entire category
                const categoryDeleteResult = await articleCategoryModel.deleteOne({ _id: category._id });
                console.log('Category delete result:', categoryDeleteResult);
            } else {
                // Remove the specific version from the category
                const categoryUpdateResult = await articleCategoryModel.updateOne(
                    { _id: category._id },
                    { $pull: { versionscategory: { _id: categoryVersionObjectId } } },
                );
                console.log('Category update result:', categoryUpdateResult);
            }

            // Remove the category version reference from articles
            const articleUpdateResultRemove = await articleModel.updateMany(
                { 'versions.category': categoryVersionObjectId },
                { $pull: { 'versions.$.category': categoryVersionObjectId } },
            );
            console.log('Articles update result (remove version):', articleUpdateResultRemove);
        } catch (error) {
            console.error('Error deleting category version and handling articles:', error);
            throw new HttpException(500, 'Failed to delete category version and handle associated articles');
        }
    }

    /*     public async deleteCategoryVersionAndHandleArticles(categoryVersionId: string, newCategoryVersionId: string | null = null): Promise<void> {
        const categoryVersionObjectId = new Types.ObjectId(categoryVersionId);

        const category = await articleCategoryModel.findOne({ 'versionscategory._id': categoryVersionObjectId });
        if (!category) throw new HttpException(404, 'Category version not found');

        const versionToDelete = category.versionscategory.find((v: any) => v._id.equals(categoryVersionObjectId));
        if (!versionToDelete) {
            throw new HttpException(404, 'Category version not found in versionscategory');
        }

        if (newCategoryVersionId) {
            const newCategoryVersionObjectId = new Types.ObjectId(newCategoryVersionId);

            await articleModel.updateMany(
                { 'versions.category': categoryVersionObjectId },
                { $set: { 'versions.$[elem].category': newCategoryVersionObjectId } },
                { arrayFilters: [{ 'elem.category': categoryVersionObjectId }] },
            );

            await articleCategoryModel.updateOne(
                { 'versionscategory._id': newCategoryVersionObjectId },
                { $addToSet: { 'versionscategory.$.articles': { $each: versionToDelete.articles } } },
            );
        }

        await articleCategoryModel.updateOne({ _id: category._id }, { $pull: { versionscategory: { _id: categoryVersionObjectId } } });

        await articleModel.updateMany(
            { 'versions.category': categoryVersionObjectId },
            { $pull: { versions: { category: categoryVersionObjectId } } },
        );
    } */

    public async getArticleByIdAndLang(language: string, id: string): Promise<any> {
        const articles = await articleModel
            .aggregate([
                { $match: { _id: mongoose.Types.ObjectId.createFromHexString(id) } },
                { $unwind: '$versions' },
                { $match: { 'versions.language': language } },
                { $unwind: { path: '$versions.category', preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: 'categories',
                        let: { categoryId: '$versions.category' },
                        pipeline: [
                            { $unwind: '$versionscategory' },
                            { $match: { $expr: { $eq: ['$versionscategory._id', '$$categoryId'] } } },
                            {
                                $project: {
                                    id: '$versionscategory._id',
                                    name: '$versionscategory.name',
                                    url: '$versionscategory.url',
                                },
                            },
                        ],
                        as: 'categoryDetails',
                    },
                },
                { $unwind: { path: '$categoryDetails', preserveNullAndEmptyArrays: true } },
                {
                    $group: {
                        _id: {
                            id: '$_id',
                            language: '$versions.language',
                            url: '$versions.url',
                            title: '$versions.title',
                            description: '$versions.description',
                            keywords: '$versions.keywords',
                            highlights: '$versions.highlights',
                            metaTitle: '$versions.metaTitle',
                            metaDescription: '$versions.metaDescription',
                            alt: '$versions.alt',
                            content: '$versions.content',
                            image: '$versions.image',
                            tags: '$tags',
                            publishDate: '$versions.publishDate',
                            isArchived: '$versions.isArchived',
                            visibility: '$versions.visibility',
                            robotsMeta: '$robotsMeta',
                            createdAt: '$createdAt',
                            updatedAt: '$updatedAt',
                        },
                        categories: {
                            $push: {
                                id: '$categoryDetails.id',
                                name: '$categoryDetails.name',
                                url: '$categoryDetails.url',
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: '$_id.id',
                        versions: {
                            language: '$_id.language',
                            title: '$_id.title',
                            description: '$_id.description',
                            keywords: '$_id.keywords',
                            highlights: '$_id.highlights',
                            isArchived: '$_id.isArchived',
                            metaTitle: '$_id.metaTitle',
                            metaDescription: '$_id.metaDescription',
                            publishDate: '$_id.publishDate',

                            content: '$_id.content',
                            url: '$_id.url',
                            alt: '$_id.alt',
                            image: '$_id.image',
                            visibility: '$_id.visibility',
                            categories: {
                                $cond: {
                                    if: { $eq: ['$categories', [null]] },
                                    then: [],
                                    else: '$categories',
                                },
                            },
                        },
                        tags: '$_id.tags',
                        robotsMeta: '$_id.robotsMeta',
                        createdAt: '$_id.createdAt',
                        updatedAt: '$_id.updatedAt',
                    },
                },
            ])
            .exec();

        if (articles.length === 0) throw new HttpException(404, 'Article not found');

        return articles[0];
    }

    public async getArticleById(id: string): Promise<ArticleI> {
        const article = await articleModel.findById(id);
        if (!article) throw new HttpException(404, 'Article not found');

        return article;
    }

    //-------All functions above are made to transform SQL data into NoSQL-------
    public async importCategories(categories: any) {
        categories = JSON.parse(categories.buffer.toString('utf-8')).categories;

        for (const category of categories) {
            const versionscategory = [
                {
                    language: Language.ENGLISH,
                    name: category.english_category,
                    url: await this.createUniqueSlug(category.english_category),
                    metaTitle: `Explore the Latest Insights in ${category.english_category} | Pentabell Blog`,
                    metaDescription: `Stay updated with the latest trends and expert insights in ${category.english_category}. Discover valuable tips, industry news, and in-depth articles on Pentabell's blog for professionals in ${category.english_category}.`,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
                {
                    language: Language.FRENCH,
                    name: category.french_category,
                    url: await this.createUniqueSlug(category.french_category),
                    metaTitle: `Soyez toujours informé des dernières tendances en ${category.french_category} grâce à notre guide carrière régulièrement mis à jour.`,
                    metaDescription: `Tenez-vous au courant des dernières actualités et tendances en ${category.french_category} et suivez nos conseils dans notre guide des carrières.`,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
            ];
            await this.Category.create({
                versionscategory,
            });
        }
    }

    public async importTranslatedArticlesFromPentabell(articles: any) {
        articles = JSON.parse(articles.buffer.toString('utf-8'));
        for (const article of articles) {
            const categoriesAng = article.BlogCategoryAng.split(', ');
            const categoriesAngIds: any = [];
            const categoriesFrIds: any = [];

            const foundArticle = await articleModel.findOne({ 'versions.createdAt': parseFrenchDate(article.blogDate, article.blogTime) });
            if (!foundArticle) {
                for (let i = 0; i < categoriesAng.length; i++) {
                    categoriesAng[i] = categoriesAng[i].replace('**', '');
                    const foundCategory = await this.Category.findOne({ 'versionscategory.url': await this.createUniqueSlug(categoriesAng[i]) });
                    if (foundCategory) {
                        const enIndex = foundCategory.versionscategory.findIndex((version: any) => version.language === 'en');
                        const frIndex = foundCategory.versionscategory.findIndex((version: any) => version.language === 'fr');

                        if (enIndex !== -1) categoriesAngIds.push(foundCategory.versionscategory[enIndex]._id);
                        if (frIndex !== -1) categoriesFrIds.push(foundCategory.versionscategory[frIndex]._id);
                    }
                }
                await articleModel.create({
                    versions: [
                        {
                            language: Language.FRENCH,
                            title: article.blogTitleFr,
                            metaTitle: article.meta_title,
                            metaDescription: article.meta_description,
                            url: await this.titleToSlug(article.blogTitleFr),
                            category: categoriesFrIds,
                            shareOnSocialMedia: true,
                            content: article.blogContentFr,
                            visibility: Visibility.Public,
                            publishDate: parseFrenchDate(article.blogDate, article.blogTime),
                            createdAt: parseFrenchDate(article.blogDate, article.blogTime),
                            updatedAt: parseFrenchDate(article.blogDate, article.blogTime),
                        },
                        {
                            language: Language.ENGLISH,
                            title: article.blogTitleAng,
                            metaTitle: article.meta_title,
                            metaDescription: article.meta_description,
                            url: await this.titleToSlug(article.blogTitleAng),
                            category: categoriesAngIds,
                            shareOnSocialMedia: true,
                            content: article.blogContentAng,
                            visibility: Visibility.Public,
                            publishDate: parseFrenchDate(article.blogDate, article.blogTime),
                            createdAt: parseFrenchDate(article.blogDate, article.blogTime),
                            updatedAt: parseFrenchDate(article.blogDate, article.blogTime),
                        },
                    ],
                    tags: undefined,
                    createdAt: parseFrenchDate(article.blogDate, article.blogTime),
                    updatedAt: parseFrenchDate(article.blogDate, article.blogTime),
                });
            }

            await delay(500);
        }
    }

    private async createUniqueSlug(name: string) {
        const numberOfCategories = await this.Category.countDocuments({ 'versionscategory.name': RegExp(`${name}`, 'i') });
        if (numberOfCategories > 1)
            return `${name
                .normalize('NFD')
                .replace(/[\u0300-\u036f]/g, '')
                .toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .trim()
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')}-${numberOfCategories}`;
        return name
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .trim()
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-');
    }

    private async titleToSlug(title: string) {
        const numberOfArticles = await articleModel.countDocuments({ 'versions.title': RegExp(title, 'i') });
        if (numberOfArticles >= 1)
            return `${title
                .normalize('NFD')
                .replace(/[\u0300-\u036f]/g, '')
                .toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .trim()
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')}-${numberOfArticles}`;
        return title
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .trim()
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-');
    }

    public async fixSlug(data: any) {
        const blogs = JSON.parse(data.buffer.toString());
        for (const blog of blogs) {
            const foundBlog = await articleModel.findOne({ 'versions.createdAt': parseFrenchDate(blog.blogDate, blog.blogTime) });
            if (!foundBlog) continue;

            for (const version of foundBlog.versions) {
                if (version.language === Language.ENGLISH) version.url = blog.blogUrlEN.replace('http://v5.pentabell.com/blog/', '').replace('/', '');
                else version.url = blog.blogUrl.replace('https://www.pentabell.com/fr/blog/', '').replace('/', '');
            }

            await articleModel.findByIdAndUpdate(foundBlog._id, foundBlog);
        }
    }

    public async importArticlesContentImages() {
        const articles = await articleModel.find();

        const extractLinks = (text: string) => {
            const regex = /https?:\/\/(?:www\.|v5\.)?pentabell\.com\/wp-content\/uploads\/[\w\/.-]+/gi;
            return text.match(regex) || [];
        };

        for (const article of articles) {
            for (const version of article.versions) {
                const extractedLinks = extractLinks(version.content);
                if (extractLinks.length === 0) continue;

                for (let link of extractedLinks) {
                    const originalLink = link;
                    link = link.replace('https://www.pentabell.com', 'http://v5.pentabell.com');

                    try {
                        const response = await axios({
                            url: link,
                            method: 'GET',
                            responseType: 'stream',
                        });

                        if (response.status !== 200) continue;
                        if (!response.data) continue;

                        const contentType = response.headers['content-type'];
                        const extension = mime.extension(contentType);

                        if (!extension || !['png', 'jpeg', 'jpg', 'webp'].includes(extension)) {
                            continue;
                        }

                        const resource = 'blogs';
                        const folder = Number(version.createdAt.toISOString().substring(0, 4));
                        const uuid = uuidv4().replace(/-/g, '');
                        const fileName = `${uuid}.${extension}`;
                        const originalName = path.basename(originalLink);
                        const fileData = {
                            resource: resource,
                            folder: String(folder),
                            uuid: uuid,
                            originalName: originalName,
                            fileName: fileName,
                            fileType: contentType,
                            fileSize: response.headers['content-length'],
                        };
                        const folderPath = `uploads/${resource}/${folder}`;
                        const uploadPath = `uploads/${resource}/${folder}/${fileName}`;

                        if (!fs.existsSync(folderPath)) {
                            fs.mkdirSync(folderPath, { recursive: true });
                        }

                        const writer = fs.createWriteStream(uploadPath);

                        await new Promise((resolve, reject) => {
                            response.data.pipe(writer);
                            writer.on('finish', () => {
                                resolve(fileName);
                            });
                            writer.on('error', reject);
                        });

                        await this.fileService.createFile(fileData);

                        version.content = version.content.replace(originalLink, `https://www.pentabell.com/api/v1/files/${fileName}`);
                        await articleModel.findByIdAndUpdate(article._id, article);
                    } catch (error) {
                        console.error(`Error downloading the image: ${error}`);
                    }
                }
            }
        }
    }

    public async editArticleLinks(oldLink: string, newLink: string): Promise<any> {
        const articlesToUpdate = await articleModel.find({ 'versions.content': RegExp(oldLink) });

        if (!articlesToUpdate) throw new HttpException(404, `No article found with the link ${oldLink}.`);

        for (const article of articlesToUpdate) {
            for (const version of article.versions) {
                version.content = version.content.replace(new RegExp(oldLink, 'g'), newLink);
            }
            await article.save();
        }

        return articlesToUpdate;
    }

    public async getarticlesTitles(language: Language) {
        return articleModel.aggregate([
            { $unwind: '$versions' },
            { $match: { 'versions.language': language } },
            {
                $project: {
                    _id: 0,
                    title: '$versions.title',
                    articleId: '$versions._id',
                },
            },
        ]);
    }

    public async getOppositeLanguageVersionsArticle(language: string, versionIds: string[]): Promise<any[]> {
        const targetLanguage = language === 'en' ? 'fr' : 'en';

        const articles = await articleModel.find({ 'versions._id': { $in: versionIds } }).exec();

        if (!articles || articles.length === 0) {
            return [];
        }

        const filteredArticles = articles.map(article => {
            const targetVersion = article.versions.find(version => version.language === targetLanguage);

            if (targetVersion) {
                return {
                    articleId: targetVersion._id,
                    title: targetVersion.title,
                };
            } else {
                return {
                    articleId: null,
                    title: 'N/A',
                };
            }
        });

        return filteredArticles;
    }

    public async updateBlogSitemap() {
        try {
            const staticUrls = [
                {
                    loc: 'https://www.pentabell.com/blog/',
                    lastmod: '2024-10-07T10:04:37+00:00',
                    alternates: [
                        { hreflang: 'en-US', href: 'https://www.pentabell.com/blog/' },
                        { hreflang: 'fr-FR', href: 'https://www.pentabell.com/fr/blog/' },
                    ],
                },
                {
                    loc: 'https://www.pentabell.com/fr/blog/',
                    lastmod: '2024-10-07T10:04:37+00:00',
                    alternates: [
                        { hreflang: 'en-US', href: 'https://www.pentabell.com/blog/' },
                        { hreflang: 'fr-FR', href: 'https://www.pentabell.com/fr/blog/' },
                    ],
                },
            ];

            const articles: any = await articleModel.find();

            let sitemapContent = `<?xml version="1.0" encoding="UTF-8"?>\n`;
            sitemapContent += `<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"\n`;
            sitemapContent += `        xmlns:xhtml="http://www.w3.org/1999/xhtml">\n`;

            staticUrls.forEach(entry => {
                sitemapContent += `  <url>\n`;
                sitemapContent += `    <loc>${entry.loc}</loc>\n`;
                sitemapContent += `    <lastmod>${entry.lastmod}</lastmod>\n`;
                sitemapContent += `    <xhtml:link rel='canonical' href='${entry.loc}' />\n`;
                entry.alternates.forEach(alt => {
                    sitemapContent += `    <xhtml:link rel='alternate' hreflang='${alt.hreflang}' href='${alt.href}' />\n`;
                });
                sitemapContent += `  </url>\n`;
            });

            articles.forEach((article: any) => {
                if (
                    article.versions.filter((version: any) => version.language === 'en' && !version?.isArchived && version.visibility === 'Public')[0]
                        ?.url
                ) {
                    sitemapContent += `  <url>\n`;
                    sitemapContent += `    <loc>https://www.pentabell.com/blog/${
                        article.versions.filter(
                            (version: any) => version.language === 'en' && !version?.isArchived && version.visibility === 'Public',
                        )[0].url
                    }/</loc>\n`;
                    sitemapContent += `    <lastmod>${article.versions
                        .filter((version: any) => version.language === 'en' && !version?.isArchived && version.visibility === 'Public')[0]
                        .updatedAt.toISOString()}</lastmod>\n`;
                    sitemapContent += article.versions.filter(
                        (version: any) => version.language === 'en' && !version?.isArchived && version.visibility === 'Public',
                    )[0]?.url
                        ? `    <xhtml:link rel='canonical' href='https://www.pentabell.com/blog/${
                              article.versions.filter(
                                  (version: any) => version.language === 'en' && !version?.isArchived && version.visibility === 'Public',
                              )[0].url
                          }/' />\n`
                        : ``;
                    sitemapContent += article.versions.filter(
                        (version: any) => version.language === 'en' && !version?.isArchived && version.visibility === 'Public',
                    )[0]?.url
                        ? `    <xhtml:link rel='alternate' hreflang='en-US' href='https://www.pentabell.com/blog/${
                              article.versions.filter(
                                  (version: any) => version.language === 'en' && !version?.isArchived && version.visibility === 'Public',
                              )[0].url
                          }/' />\n`
                        : ``;
                    sitemapContent += article.versions.filter(
                        (version: any) => version.language === 'fr' && !version?.isArchived && version.visibility === 'Public',
                    )[0]?.url
                        ? `    <xhtml:link rel='alternate' hreflang='fr-FR' href='https://www.pentabell.com/fr/blog/${
                              article.versions.filter(
                                  (version: any) => version.language === 'fr' && !version?.isArchived && version.visibility === 'Public',
                              )[0].url
                          }/' />\n`
                        : ``;
                    sitemapContent += `  </url>\n`;
                }

                if (
                    article.versions.filter((version: any) => version.language === 'fr' && !version?.isArchived && version.visibility === 'Public')[0]
                        ?.url
                ) {
                    sitemapContent += `  <url>\n`;
                    sitemapContent += `    <loc>https://www.pentabell.com/fr/blog/${
                        article.versions.filter(
                            (version: any) => version.language === 'fr' && !version?.isArchived && version.visibility === 'Public',
                        )[0].url
                    }/</loc>\n`;
                    sitemapContent += `    <lastmod>${article.versions
                        .filter((version: any) => version.language === 'fr' && !version?.isArchived && version.visibility === 'Public')[0]
                        .updatedAt.toISOString()}</lastmod>\n`;
                    sitemapContent += article.versions.filter(
                        (version: any) => version.language === 'fr' && !version?.isArchived && version.visibility === 'Public',
                    )[0]?.url
                        ? `    <xhtml:link rel='canonical' href='https://www.pentabell.com/fr/blog/${
                              article.versions.filter(
                                  (version: any) => version.language === 'fr' && !version?.isArchived && version.visibility === 'Public',
                              )[0].url
                          }/' />\n`
                        : ``;
                    sitemapContent += article.versions.filter(
                        (version: any) => version.language === 'en' && !version?.isArchived && version.visibility === 'Public',
                    )[0]?.url
                        ? `    <xhtml:link rel='alternate' hreflang='en-US' href='https://www.pentabell.com/blog/${
                              article.versions.filter(
                                  (version: any) => version.language === 'en' && !version?.isArchived && version.visibility === 'Public',
                              )[0].url
                          }/' />\n`
                        : ``;
                    sitemapContent += article.versions.filter(
                        (version: any) => version.language === 'fr' && !version?.isArchived && version.visibility === 'Public',
                    )[0]?.url
                        ? `    <xhtml:link rel='alternate' hreflang='fr-FR' href='https://www.pentabell.com/fr/blog/${
                              article.versions.filter(
                                  (version: any) => version.language === 'fr' && !version?.isArchived && version.visibility === 'Public',
                              )[0].url
                          }/' />\n`
                        : ``;
                    sitemapContent += `  </url>\n`;
                }
            });

            sitemapContent += `</urlset>`;

            const filePath = path.join(__dirname, '../../../../client/public/sitemap_blog.xml');
            fs.writeFileSync(filePath, sitemapContent, 'utf-8');
            console.log(`Sitemap has been generated at ${filePath}`);
            console.log(articles.length > 1 ? `With ${articles.length} blog` : `With ${articles.length} blogs`);
        } catch (error: any) {
            console.error('Error creating opportunity sitemap: ', error.message);
        }
    }
}

export default ArticleService;
