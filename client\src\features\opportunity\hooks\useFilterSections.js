import { useState, useEffect, useCallback } from 'react';

const useFilterSections = (isMobile, isTablet) => {
  const [expandedSections, setExpandedSections] = useState({
    industry: !isMobile && !isTablet,
    contract: !isMobile && !isTablet,
    search: isMobile || isTablet,
    country: false,
    language: false,
    experience: false,
  });

  const toggleSection = useCallback((section) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  }, []);

  useEffect(() => {
    setExpandedSections((prev) => ({
      ...prev,
      industry: !isMobile && !isTablet,
      contract: !isMobile && !isTablet,
      search: isMobile || isTablet,
    }));
  }, [isMobile, isTablet]);

  return { expandedSections, toggleSection };
};

export default useFilterSections;
