(()=>{var e={};e.id=3689,e.ids=[3689],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},51156:(e,t,a)=>{"use strict";a.d(t,{Z:()=>o});var s,r=a(95746);function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let o=e=>r.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:22,height:22,fill:"none"},e),s||(s=r.createElement("path",{stroke:"#1E1E1E",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 2a2.827 2.827 0 1 1 4 4L6.5 19.5 1 21l1.5-5.5z"})))},71227:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l});var s,r,i=a(95746);function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let l=e=>i.createElement("svg",o({xmlns:"http://www.w3.org/2000/svg",width:24,height:18,fill:"none"},e),s||(s=i.createElement("path",{stroke:"#1E1E1E",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M1 9s4-8 11-8 11 8 11 8-4 8-11 8S1 9 1 9"})),r||(r=i.createElement("path",{stroke:"#1E1E1E",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 12a3 3 0 1 0 0-6 3 3 0 0 0 0 6"})))},80870:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c}),a(99959),a(75545),a(23658),a(54864);var s=a(23191),r=a(88716),i=a(37922),o=a.n(i),l=a(95231),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);a.d(t,n);let c=["",{children:["[locale]",{children:["(dashboard)",{children:["backoffice",{children:["home",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,99959)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(dashboard)\\backoffice\\home\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,75545)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(dashboard)\\layout.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,23658)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54864)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\not-found.js"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(dashboard)\\backoffice\\home\\page.jsx"],p="/[locale]/(dashboard)/backoffice/home/<USER>",u={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/[locale]/(dashboard)/backoffice/home/<USER>",pathname:"/[locale]/backoffice/home",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},86395:(e,t,a)=>{Promise.resolve().then(a.bind(a,32372))},10163:(e,t,a)=>{"use strict";a.d(t,{Z:()=>h});var s=a(17577),r=a(41135),i=a(88634),o=a(91703),l=a(2791),n=a(71685),c=a(97898);function d(e){return(0,c.ZP)("MuiDialogActions",e)}(0,n.Z)("MuiDialogActions",["root","spacing"]);var p=a(10326);let u=e=>{let{classes:t,disableSpacing:a}=e;return(0,i.Z)({root:["root",!a&&"spacing"]},d,t)},m=(0,o.ZP)("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:a}=e;return[t.root,!a.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:e})=>!e.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),h=s.forwardRef(function(e,t){let a=(0,l.i)({props:e,name:"MuiDialogActions"}),{className:s,disableSpacing:i=!1,...o}=a,n={...a,disableSpacing:i},c=u(n);return(0,p.jsx)(m,{className:(0,r.Z)(c.root,s),ownerState:n,ref:t,...o})})},28591:(e,t,a)=>{"use strict";a.d(t,{Z:()=>x});var s=a(17577),r=a(41135),i=a(88634),o=a(91703),l=a(30990),n=a(2791),c=a(71685),d=a(97898);function p(e){return(0,d.ZP)("MuiDialogContent",e)}(0,c.Z)("MuiDialogContent",["root","dividers"]);var u=a(64650),m=a(10326);let h=e=>{let{classes:t,dividers:a}=e;return(0,i.Z)({root:["root",a&&"dividers"]},p,t)},g=(0,o.ZP)("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:a}=e;return[t.root,a.dividers&&t.dividers]}})((0,l.Z)(({theme:e})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:e})=>e.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(e.vars||e).palette.divider}`,borderBottom:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:e})=>!e.dividers,style:{[`.${u.Z.root} + &`]:{paddingTop:0}}}]}))),x=s.forwardRef(function(e,t){let a=(0,n.i)({props:e,name:"MuiDialogContent"}),{className:s,dividers:i=!1,...o}=a,l={...a,dividers:i},c=h(l);return(0,m.jsx)(g,{className:(0,r.Z)(c.root,s),ownerState:l,ref:t,...o})})},64650:(e,t,a)=>{"use strict";a.d(t,{Z:()=>o,a:()=>i});var s=a(71685),r=a(97898);function i(e){return(0,r.ZP)("MuiDialogTitle",e)}let o=(0,s.Z)("MuiDialogTitle",["root"])},43659:(e,t,a)=>{"use strict";a.d(t,{Z:()=>C});var s=a(17577),r=a(41135),i=a(88634),o=a(34018),l=a(54641),n=a(24810),c=a(48467),d=a(89178),p=a(17251),u=a(55733),m=a(7783),h=a(91703),g=a(23743),x=a(30990),b=a(2791),f=a(31121),v=a(10326);let y=(0,h.ZP)(m.Z,{name:"MuiDialog",slot:"Backdrop",overrides:(e,t)=>t.backdrop})({zIndex:-1}),N=e=>{let{classes:t,scroll:a,maxWidth:s,fullWidth:r,fullScreen:o}=e,n={root:["root"],container:["container",`scroll${(0,l.Z)(a)}`],paper:["paper",`paperScroll${(0,l.Z)(a)}`,`paperWidth${(0,l.Z)(String(s))}`,r&&"paperFullWidth",o&&"paperFullScreen"]};return(0,i.Z)(n,p.D,t)},j=(0,h.ZP)(n.Z,{name:"MuiDialog",slot:"Root",overridesResolver:(e,t)=>t.root})({"@media print":{position:"absolute !important"}}),A=(0,h.ZP)("div",{name:"MuiDialog",slot:"Container",overridesResolver:(e,t)=>{let{ownerState:a}=e;return[t.container,t[`scroll${(0,l.Z)(a.scroll)}`]]}})({height:"100%","@media print":{height:"auto"},outline:0,variants:[{props:{scroll:"paper"},style:{display:"flex",justifyContent:"center",alignItems:"center"}},{props:{scroll:"body"},style:{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}}}]}),w=(0,h.ZP)(d.Z,{name:"MuiDialog",slot:"Paper",overridesResolver:(e,t)=>{let{ownerState:a}=e;return[t.paper,t[`scrollPaper${(0,l.Z)(a.scroll)}`],t[`paperWidth${(0,l.Z)(String(a.maxWidth))}`],a.fullWidth&&t.paperFullWidth,a.fullScreen&&t.paperFullScreen]}})((0,x.Z)(({theme:e})=>({margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"},variants:[{props:{scroll:"paper"},style:{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"}},{props:{scroll:"body"},style:{display:"inline-block",verticalAlign:"middle",textAlign:"initial"}},{props:({ownerState:e})=>!e.maxWidth,style:{maxWidth:"calc(100% - 64px)"}},{props:{maxWidth:"xs"},style:{maxWidth:"px"===e.breakpoints.unit?Math.max(e.breakpoints.values.xs,444):`max(${e.breakpoints.values.xs}${e.breakpoints.unit}, 444px)`,[`&.${p.Z.paperScrollBody}`]:{[e.breakpoints.down(Math.max(e.breakpoints.values.xs,444)+64)]:{maxWidth:"calc(100% - 64px)"}}}},...Object.keys(e.breakpoints.values).filter(e=>"xs"!==e).map(t=>({props:{maxWidth:t},style:{maxWidth:`${e.breakpoints.values[t]}${e.breakpoints.unit}`,[`&.${p.Z.paperScrollBody}`]:{[e.breakpoints.down(e.breakpoints.values[t]+64)]:{maxWidth:"calc(100% - 64px)"}}}})),{props:({ownerState:e})=>e.fullWidth,style:{width:"calc(100% - 64px)"}},{props:({ownerState:e})=>e.fullScreen,style:{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,[`&.${p.Z.paperScrollBody}`]:{margin:0,maxWidth:"100%"}}}]}))),C=s.forwardRef(function(e,t){let a=(0,b.i)({props:e,name:"MuiDialog"}),i=(0,g.Z)(),l={enter:i.transitions.duration.enteringScreen,exit:i.transitions.duration.leavingScreen},{"aria-describedby":n,"aria-labelledby":p,"aria-modal":m=!0,BackdropComponent:h,BackdropProps:x,children:C,className:k,disableEscapeKeyDown:Z=!1,fullScreen:E=!1,fullWidth:$=!1,maxWidth:P="sm",onBackdropClick:S,onClick:M,onClose:O,open:D,PaperComponent:T=d.Z,PaperProps:R={},scroll:F="paper",slots:I={},slotProps:W={},TransitionComponent:U=c.Z,transitionDuration:_=l,TransitionProps:L,...H}=a,q={...a,disableEscapeKeyDown:Z,fullScreen:E,fullWidth:$,maxWidth:P,scroll:F},Y=N(q),z=s.useRef(),X=(0,o.Z)(p),B=s.useMemo(()=>({titleId:X}),[X]),G={slots:{transition:U,...I},slotProps:{transition:L,paper:R,backdrop:x,...W}},[Q,V]=(0,f.Z)("root",{elementType:j,shouldForwardComponentProp:!0,externalForwardedProps:G,ownerState:q,className:(0,r.Z)(Y.root,k),ref:t}),[J,K]=(0,f.Z)("backdrop",{elementType:y,shouldForwardComponentProp:!0,externalForwardedProps:G,ownerState:q}),[ee,et]=(0,f.Z)("paper",{elementType:w,shouldForwardComponentProp:!0,externalForwardedProps:G,ownerState:q,className:(0,r.Z)(Y.paper,R.className)}),[ea,es]=(0,f.Z)("container",{elementType:A,externalForwardedProps:G,ownerState:q,className:(0,r.Z)(Y.container)}),[er,ei]=(0,f.Z)("transition",{elementType:c.Z,externalForwardedProps:G,ownerState:q,additionalProps:{appear:!0,in:D,timeout:_,role:"presentation"}});return(0,v.jsx)(Q,{closeAfterTransition:!0,slots:{backdrop:J},slotProps:{backdrop:{transitionDuration:_,as:h,...K}},disableEscapeKeyDown:Z,onClose:O,open:D,onClick:e=>{M&&M(e),z.current&&(z.current=null,S&&S(e),O&&O(e,"backdropClick"))},...V,...H,children:(0,v.jsx)(er,{...ei,children:(0,v.jsx)(ea,{onMouseDown:e=>{z.current=e.target===e.currentTarget},...es,children:(0,v.jsx)(ee,{as:T,elevation:24,role:"dialog","aria-describedby":n,"aria-labelledby":X,"aria-modal":m,...et,children:(0,v.jsx)(u.Z.Provider,{value:B,children:C})})})})})})},55733:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=a(17577).createContext({})},17251:(e,t,a)=>{"use strict";a.d(t,{D:()=>i,Z:()=>o});var s=a(71685),r=a(97898);function i(e){return(0,r.ZP)("MuiDialog",e)}let o=(0,s.Z)("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"])},32372:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>k});var s=a(10326),r=a(90308),i=a(52210),o=a(56390),l=a(15897),n=a(17577),c=a(15082),d=a(87419),p=a(90397),u=a(3416),m=a(63416);a(59245),a(29565);let h=({applications:e,articles:t,comments:a,contacts:r,opportunities:o,candidates:l,cvUploaded:n,logins:c})=>{let{t:d}=(0,i.$G)();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"stats-container",children:[s.jsx("div",{className:"stats-card stats-job",children:(0,s.jsxs)("div",{className:"stats-content",children:[s.jsx("div",{className:"stats-icon-wrapper",children:s.jsx("img",{src:m.Z.src,alt:"job",className:"stats-icon"})}),(0,s.jsxs)("div",{className:"stats-info",children:[s.jsx("span",{className:"stats-title",children:d("statsTotalNumbers:totalCandidates")}),s.jsx("span",{className:"stats-value",children:l})]})]})}),s.jsx("div",{className:"stats-card stats-application",children:(0,s.jsxs)("div",{className:"stats-content",children:[s.jsx("div",{className:"stats-icon-wrapper",children:s.jsx("img",{src:m.Z.src,alt:"job",className:"stats-icon"})}),(0,s.jsxs)("div",{className:"stats-info",children:[s.jsx("span",{className:"stats-title",children:d("statsTotalNumbers:totalCvUploaded")}),s.jsx("span",{className:"stats-value",children:n})]})]})}),s.jsx("div",{className:"stats-card stats-job",children:(0,s.jsxs)("div",{className:"stats-content",children:[s.jsx("div",{className:"stats-icon-wrapper",children:s.jsx("img",{src:m.Z.src,alt:"job",className:"stats-icon"})}),(0,s.jsxs)("div",{className:"stats-info",children:[s.jsx("span",{className:"stats-title",children:d("statsTotalNumbers:logins")}),s.jsx("span",{className:"stats-value",children:c})]})]})}),s.jsx("div",{className:"stats-card stats-contact",children:(0,s.jsxs)("div",{className:"stats-content",children:[s.jsx("div",{className:"stats-icon-wrapper",children:s.jsx("img",{src:m.Z.src,alt:"expired",className:"stats-icon"})}),(0,s.jsxs)("div",{className:"stats-info",children:[s.jsx("span",{className:"stats-title",children:d("statsTotalNumbers:totalApplications")}),s.jsx("span",{className:"stats-value",children:e})]})]})})]}),(0,s.jsxs)("div",{className:"stats-container",children:[s.jsx("div",{className:"stats-card stats-contact",children:(0,s.jsxs)("div",{className:"stats-content",children:[s.jsx("div",{className:"stats-icon-wrapper",children:s.jsx("img",{src:m.Z.src,alt:"job",className:"stats-icon"})}),(0,s.jsxs)("div",{className:"stats-info",children:[s.jsx("span",{className:"stats-title",children:d("statsTotalNumbers:totalArticles")}),s.jsx("span",{className:"stats-value",children:t})]})]})}),s.jsx("div",{className:"stats-card stats-job",children:(0,s.jsxs)("div",{className:"stats-content",children:[s.jsx("div",{className:"stats-icon-wrapper",children:s.jsx("img",{src:m.Z.src,alt:"active",className:"stats-icon"})}),(0,s.jsxs)("div",{className:"stats-info",children:[s.jsx("span",{className:"stats-title",children:d("statsTotalNumbers:totalOpportunities")}),s.jsx("span",{className:"stats-value",children:o})]})]})}),s.jsx("div",{className:"stats-card stats-application",children:(0,s.jsxs)("div",{className:"stats-content",children:[s.jsx("div",{className:"stats-icon-wrapper",children:s.jsx("img",{src:m.Z.src,alt:"expired",className:"stats-icon"})}),(0,s.jsxs)("div",{className:"stats-info",children:[s.jsx("span",{className:"stats-title",children:d("statsTotalNumbers:totalComments")}),s.jsx("span",{className:"stats-value",children:a})]})]})}),s.jsx("div",{className:"stats-card stats-job",children:(0,s.jsxs)("div",{className:"stats-content",children:[s.jsx("div",{className:"stats-icon-wrapper",children:s.jsx("img",{src:m.Z.src,alt:"profile views",className:"stats-icon"})}),(0,s.jsxs)("div",{className:"stats-info",children:[s.jsx("span",{className:"stats-title",children:d("statsTotalNumbers:totalContacts")}),s.jsx("span",{className:"stats-value",children:r||0})]})]})})]})]})};var g=a(51156),x=a(5248),b=a(26949),f=a(71227),v=a(64504),y=a(86184),N=a(54528),j=a(41117),A=a(70580),w=a(31190);let C=function({selectedLanguage:e}){let{t,i18n:a}=(0,i.$G)(),[m,C]=(0,n.useState)(""),[k,Z]=(0,n.useState)(""),[E,$]=(0,n.useState)(!1),[P,S]=(0,n.useState)(!1),[M,O]=(0,n.useState)(!1),[D,T]=(0,n.useState)(!1),[R,F]=(0,n.useState)(""),[I,W]=(0,n.useState)([]),[U,_]=(0,n.useState)(null),[L,H]=(0,n.useState)(null),q=(0,j.sE)(),Y=(e,t)=>{H(e),_(t),T(!0)},z=async e=>{try{await A.yX.delete(`articles/archiver/${e}`),V.refetch(),w.Am.success("article archived successfully")}catch{}},X=e=>{Z(e),S(!0)},B=async()=>{await z(k),S(!1)},G=(0,N.gu)(),Q=(0,d.cl)({pageSize:5,pageNumber:1,paginated:!0,sortOrder:"desc"}),V=(0,v.b$)({language:e,pageSize:5,pageNumber:1,sortOrder:"desc"}),J=(0,y.Fe)({language:e,pageSize:5,pageNumber:1,sortOrder:"desc"}),K=(0,j.hb)({pageSize:5,pageNumber:1,sortOrder:"desc",approved:!1}),ee=async()=>{K?.data&&W(K.data.comments||[])},et=(e,t)=>{q.mutate({id:e,approve:t},{onSuccess:()=>{K?.refetch()&&ee()}})};if(V.isLoading||J.isLoading||K.isLoading||Q.isLoading)return s.jsx("p",{children:"Loading..."});let ea=e=>{let t=e.split(" ");return t?.length>3?t.slice(0,2).join(" ")+"...":e},es=(e,t)=>{switch(C(t.target.value),t.target.value){case"edit":handleEdit(e.id);break;case"preview":router.push(`/blog/${e?.url}`);break;case"delete":X(e.id);break;case"comment":router.push(`/comments/${e.id}`)}},er=(e,t)=>{"edit"===e.target.value&&handleEdit(t)},ei=[{field:"user",headerName:"User",headerClassName:"datagrid-header",cellClassName:"datagrid-cell  hide-on-mobile",flex:1,renderCell:t=>s.jsx("a",{href:t.row.user?.firstName?`/backoffice/users/detail/${t.row.user?._id}`:"en"===e?"/backoffice/home":`/${e}/backoffice/home`,children:t.row.user?.firstName?`${t.row.user?.firstName} ${t.row.user?.lastName}`:t.row?.email})},{field:"article",headerClassName:"datagrid-header",cellClassName:"datagrid-cell  hide-on-mobile",headerName:"article",flex:1,renderCell:e=>s.jsx("a",{href:`/${e.row.article?.versions[0]?.language}/blog/${e.row.article?.versions[0]?.url}`,children:e.row?.article?.versions[0]?.title})},{field:"comment",headerClassName:"datagrid-header",cellClassName:"datagrid-cell  hide-on-mobile",headerName:"comment",flex:1},{field:"actions",headerName:"Actions",flex:1,renderCell:e=>s.jsx(s.Fragment,{children:s.jsx(c.default,{text:!0===e.row.approved?"Disapprove":"Approve",className:"btn btn-link",onClick:()=>Y(e.row.approved?"disapprove":"approve",e.row._id)})})}],eo=V?.data?.articles?.map((e,t)=>({id:e._id,title:e?.versions[0]?.title?ea(e?.versions[0]?.title):"No title",actions:e._id,visibility:e?.versions?.[0]?.visibility||"N/A",url:e?.versions?.[0]?.url||"N/A",totalCommentaires:e?.totalCommentaires||"0"}))||[],el=[{field:"title",headerClassName:"datagrid-header",cellClassName:"datagrid-cell  hide-on-mobile",headerName:t("listArticle:title"),flex:1,renderCell:e=>s.jsx("a",{href:`/en/blog/${e.row?.url}`,className:"link",children:e.row.title})},{field:"totalCommentaires",headerClassName:"datagrid-header",cellClassName:"datagrid-cell  hide-on-mobile",headerName:t("listArticle:nbOfComments"),flex:1},{field:"actions",headerName:"",renderCell:e=>(0,s.jsxs)(o.Z,{onChange:t=>es(e.row,t),displayEmpty:!0,input:s.jsx(l.ZP,{}),style:{width:"100%"},renderValue:()=>t("listArticle:Actions"),children:[s.jsx(c.default,{text:t("global:edit"),icon:s.jsx(g.Z,{}),leftIcon:!0,link:`/backoffice/blogs${b.Z.editArticle}/${e.row.id}`,className:"btn btn-ghost edit-blog"}),s.jsx(c.default,{text:t("global:comments"),icon:s.jsx(g.Z,{}),leftIcon:!0,link:`/backoffice/blogs/comments/${e.row.id}`,className:"btn btn-ghost edit-blog"}),s.jsx(c.default,{text:t("global:preview"),icon:s.jsx(f.Z,{}),leftIcon:!0,link:`/blog/${e.row?.url}`,className:"btn btn-ghost edit-blog"})]}),flex:1}],en=J?.data?.opportunities?.map((e,t)=>({id:t,opportunityId:e?._id,title:e?.versions[a.language]?.title,industry:e?.industry,actions:e?._id,url:e?.versions[a.language].url,currentLaguage:e.versions[a.language]?.language}))||[],ec=[{field:"title",headerClassName:"datagrid-header",cellClassName:"datagrid-cell  hide-on-mobile",headerName:t("listopportunity:job"),flex:1,renderCell:e=>s.jsx("a",{href:`/en/opportunities/${e.row?.url}`,className:"link",children:e.row.title})},{field:"industry",headerClassName:"datagrid-header",cellClassName:"datagrid-cell  hide-on-mobile",headerName:t("listopportunity:industry"),flex:1},{field:"actions",headerName:"",flex:1,renderCell:e=>{let a=e.row.actions;return(0,s.jsxs)(o.Z,{value:"",onChange:e=>er(e,a),displayEmpty:!0,input:s.jsx(l.ZP,{}),style:{width:"100%"},renderValue:()=>t("listArticle:Actions"),children:[s.jsx(c.default,{text:t("global:preview"),icon:s.jsx(f.Z,{}),leftIcon:!0,link:`/opportunities/${e.row.url}/`,className:"btn btn-ghost edit-blog"}),s.jsx(c.default,{text:t("global:edit"),icon:s.jsx(g.Z,{}),leftIcon:!0,link:`/backoffice/opportunities/edit/${a}/`,className:"btn btn-ghost edit-blog"}),s.jsx(c.default,{text:t("global:applications"),icon:s.jsx(f.Z,{}),leftIcon:!0,link:`/backoffice/applications/opportunity/${a}`,className:"btn btn-ghost edit-blog"})]})}}],ed=Q?.data?.contacts?.map((e,t)=>({id:e._id,fullName:e.fullName?e.fullName:e.firstName+" "+e.lastName||"N/A",email:e?.email||"N/A",type:x.rO[e.type]||"N/A",actions:e?._id,userId:e?.userId}))||[],ep=[{field:"fullName",cellClassName:"datagrid-cell",headerClassName:"datagrid-header",headerName:t("contact:fullName"),renderCell:e=>null===e.row.userId?s.jsx("span",{children:e.value}):s.jsx(c.default,{text:e.value,link:`/backoffice/users/detail/${e.row.userId}`,className:"btn btn-ghost edit-blog"}),flex:1},{field:"type",cellClassName:"datagrid-cell",headerClassName:"datagrid-header",headerName:t("contact:type"),flex:1},{field:"actions",cellClassName:"datagrid-cell",headerClassName:"datagrid-header",headerName:t("contact:actions"),renderCell:e=>s.jsx("div",{id:"datagrid",children:s.jsx("div",{className:"action-buttons",children:s.jsx(c.default,{text:t("contact:detail"),link:`/backoffice/contacts${b.Z.detail}/${e.row.id}`,className:"btn btn-ghost edit-blog"})})}),flex:1}];return(0,s.jsxs)(s.Fragment,{children:[s.jsx("p",{className:"heading-h2 semi-bold",children:" Home Page "}),(0,s.jsxs)("div",{id:"container",children:[s.jsx(h,{applications:G?.data?.totalApplications,opportunities:G?.data?.totalOpportunity,comments:G?.data?.totalComments,contacts:G?.data?.totalContacts,candidates:G?.data?.totalCandidates,articles:G?.data?.totalArticles,logins:G?.data?.totalLogins,cvUploaded:G?.data?.totalFilesUploaded}),(0,s.jsxs)("div",{id:"dashboard-grid",children:[(0,s.jsxs)("div",{className:"dashboard-grid",children:[(0,s.jsxs)("div",{className:"table-container disable-pagination",children:[(0,s.jsxs)("div",{className:"flex",children:[s.jsx("p",{className:"heading-h2 semi-bold",children:t("contact:listOfArticles")}),s.jsx(c.default,{text:t("global:viewMore"),className:"btn btn-ghost",link:"/backoffice/blogs"})]}),s.jsx("div",{style:{height:"100%",width:"100%"},children:s.jsx(r._,{localeText:{noRowsLabel:"No result found."},rows:eo,columns:el,rowHeight:40,autoHeight:!0})})]})," ",(0,s.jsxs)("div",{className:"table-container disable-pagination",children:[(0,s.jsxs)("div",{className:"flex",children:[s.jsx("p",{className:"heading-h2 semi-bold",children:t("contact:listOfOpportunities")}),s.jsx(c.default,{text:t("global:viewMore"),className:"btn btn-ghost",link:"/backoffice/opportunities"})]}),s.jsx("div",{style:{height:"100%",width:"100%"},children:s.jsx(r._,{localeText:{noRowsLabel:"No result found."},pagination:!1,rows:en,columns:ec,rowHeight:40,autoHeight:!0})})]})," ",(0,s.jsxs)("div",{className:"table-container disable-pagination",children:[(0,s.jsxs)("div",{className:"flex",children:[" ",s.jsx("p",{className:"heading-h2 semi-bold",style:{marginTop:"5px !important"},children:t("contact:listOfContact")}),s.jsx(c.default,{text:t("global:viewMore"),className:"btn btn-ghost",link:"/backoffice/contacts"})]}),s.jsx("div",{style:{height:"100%",width:"100%"},children:s.jsx(r._,{localeText:{noRowsLabel:"No result found."},autoHeight:!0,rows:ed,columns:ep,rowHeight:40,disableSelectionOnClick:!0})})]})," ",(0,s.jsxs)("div",{className:"table-container disable-pagination",children:[(0,s.jsxs)("div",{className:"flex",children:[" ",s.jsx("p",{className:"heading-h2 semi-bold",children:t("contact:listOfComments")}),s.jsx(c.default,{text:t("global:viewMore"),className:"btn btn-ghost",link:"/backoffice/comments"})]}),s.jsx("div",{style:{height:"100%",width:"100%"},children:s.jsx(r._,{localeText:{noRowsLabel:"No result found."},autoHeight:!0,getRowId:e=>e._id,rows:I,columns:ei,rowHeight:40})})]})," "]})," ",P&&s.jsx(p.Z,{icon:s.jsx(u.Z,{}),message:t("messages:deleteArticle"),onClose:()=>{S(!1)},onConfirm:B}),E&&s.jsx(p.Z,{message:t("messages:deleteOpportunity"),icon:s.jsx(u.Z,{}),onClose:handleDeleteOpportunite,onConfirm:handleToggleCancelOpportunity}),s.jsx(p.Z,{open:D,message:"delete"===L?t("messages:deleteComment"):"approve"===L?t("messages:approveComment"):t("messages:disapproveComment"),icon:s.jsx(u.Z,{}),onClose:()=>{T(!1)},onConfirm:()=>{"approve"===L?(et(U,!0),T(!1)):"disapprove"===L&&(et(U,!1),T(!1)),T(!1)}})]})]})]})},k=({params:e})=>s.jsx(C,{selectedLanguage:e?.locale})},87419:(e,t,a)=>{"use strict";a.d(t,{uu:()=>c,eo:()=>p,cl:()=>d});var s=a(2994),r=a(70580),i=a(50967);let o=async(e,t,a)=>new Promise(async(s,o)=>{r.xk.post(`${i.Y.contact}`,e).then(e=>{t("Submitted."),a(""),e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(500===e.response.status?(t(!1),a("Internal error server")):(t(!1),a(e.response.data.message))),e&&o(e)})}),l=e=>new Promise(async(t,a)=>{try{let a=await r.yX.get(`${i.Y.contact}`,{params:{paginated:e.paginated,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyword:e.keyword,createdAt:e.createdAt,type:e.type,email:e.email}});t(a.data)}catch(e){a(e)}}),n=e=>new Promise(async(t,a)=>{try{let a=await r.yX.get(`${i.Y.contact}/${e}`);t(a.data)}catch(e){a(e)}}),c=(e,t)=>{let a=(0,s.useQueryClient)();return(0,s.useMutation)({mutationFn:a=>o(a,e,t),onSuccess:e=>{a.invalidateQueries("user")},onError:e=>{e.message=""}})},d=e=>(0,s.useQuery)("contact",async()=>await l(e)),p=e=>(0,s.useQuery)(["contact",e],async()=>await n(e))},86184:(e,t,a)=>{"use strict";a.d(t,{$i:()=>g,BF:()=>h,Fe:()=>o,Gc:()=>d,HF:()=>i,Hr:()=>n,IZ:()=>m,NF:()=>c,PM:()=>l,UJ:()=>p,jd:()=>u});var s=a(2994),r=a(21464);a(35047),a(97980);let i=()=>(0,s.useMutation)({mutationFn:e=>(0,r.W3)(e),onError:e=>{e.message=""}}),o=e=>(0,s.useQuery)("opportunities",async()=>await (0,r.fH)(e)),l=()=>(0,s.useMutation)(()=>(0,r.AE)()),n=e=>(0,s.useQuery)(["opportunities",e],async()=>await (0,r.Mq)(e)),c=()=>(0,s.useMutation)({mutationFn:(e,t,a)=>(0,r.rE)(e,t,a),onError:e=>{e.message=""}}),d=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t)=>(0,r.S1)(e,t),onError:e=>{e.message=""}})),p=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t,a)=>(0,r.lU)(e,t,a),onError:e=>{e.message=""}})),u=()=>{let e=(0,s.useQueryClient)();return(0,s.useMutation)({mutationFn:(e,t,a,s)=>(0,r.yH)(e,t,a,s),onSuccess:t=>{e.invalidateQueries("files")}})},m=()=>(0,s.useQuery)("SeoOpportunities",async()=>await (0,r.yJ)()),h=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t)=>(0,r.mt)(e,t),onError:e=>{e.message=""}})),g=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:({language:e,id:t,archive:a})=>(0,r.TK)(e,t,a),onError:e=>{console.error("Error during mutation",e),e.message=""}}))},21464:(e,t,a)=>{"use strict";a.d(t,{AE:()=>c,Mq:()=>n,S1:()=>p,TK:()=>g,W3:()=>o,fH:()=>l,lU:()=>u,mt:()=>x,rE:()=>d,yH:()=>m,yJ:()=>h});var s=a(50967),r=a(70580),i=a(31190);let o=e=>(e.t,new Promise(async(t,a)=>{r.yX.post(`/opportunities${s.Y.applications}/${e.opportunityId}`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&a(e)})})),l=e=>new Promise(async(t,a)=>{try{let a=await r.yX.get(`${s.Y.opportunity}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyWord:e.keyWord,visibility:e.visibility,title:e.title,createdAt:e.createdAt,publishDate:e.publishDate,country:e.country,industry:e.industry,contractType:e.contractType,minExperience:e.minExperience,maxExperience:e.maxExperience,jobDescriptionLanguages:e.jobDescriptionLanguages,reference:e.reference,opportunityType:e.opportunityType,exclude:"In House"!==e.opportunityType&&"true"}});t(a.data)}catch(e){a(e)}}),n=e=>new Promise(async(t,a)=>{try{let a=await r.yX.get(`${s.Y.opportunity}/${e}`);t(a.data)}catch(e){a(e)}}),c=async()=>(await r.xk.put("/UpdateJobdescription")).data,d=({data:e,language:t,id:a})=>new Promise(async(o,l)=>{r.yX.post(`${s.Y.opportunity}/${t}/${a}`,e).then(e=>{"en"===t&&i.Am.success("Opportunity english updated successfully"),"fr"===t&&i.Am.success("Opportunity french updated successfully"),e?.data&&o(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&l(e)})}),p=({data:e,id:t})=>new Promise(async(a,o)=>{r.yX.put(`${s.Y.opportunity}/${t}`,e).then(e=>{i.Am.success("Opportunity Commun fields updated successfully"),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&o(e)})}),u=({id:e,title:t,typeOfFavourite:a})=>new Promise(async(o,l)=>{r.yX.put(`${s.Y.baseUrl}/favourite/${e}`,{type:a}).then(e=>{i.Am.success(`${a} : ${t} saved to your favorites.`),e?.data&&o(e.data)}).catch(e=>{e&&e.response&&e.response.data&&409===e.response.status&&i.Am.warning(` ${t} already in shortlist`),e&&l(e)})}),m=({resource:e,folder:t,filename:a,body:o})=>new Promise(async(l,n)=>{r.cU.post(`${s.Y.files}/uploadResume/${e}/${t}/${a}`,o.formData).then(e=>{e.data&&l(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(400===e.response.status?e.response.data.message.includes("The resume lacks essential information")?i.Am.warn(o.t("messages:requireResume")):i.Am.warn(e.response.data.message):500===e.response.status&&i.Am.error("Internal Server Error")),e&&n(e)})}),h=()=>new Promise(async(e,t)=>{try{let t=await r.yX.get(`${s.Y.seoOpportunity}`);e(t.data)}catch(e){t(e)}}),g=(e,t,a)=>new Promise(async(o,l)=>{try{let l=await r.yX.put(`${s.Y.opportunity}/${e}/${t}/desarchiver`,{archive:a});l?.data&&(i.Am.success(`opportunity ${a?"archived":"desarchived"} successfully`),o(l.data))}catch(e){i.Am.error(`Failed to ${a?"archive":"desarchive"} the opportunity.`),l(e)}}),x=({data:e,id:t})=>new Promise(async(a,o)=>{r.yX.put(`${s.Y.seoOpportunity}/${t}`,e).then(e=>{i.Am.success("Opportunity seo updated successfully"),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&o(e)})})},26949:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s={home:"/",REGISTER:"/register",DASHBOARD:"/dashboard",UPDATE_PROFILE:"update-profile-informations",COMPLETE_PROFILE:"complete-profile-informations",SETTINGS:"settings",MY_APPLICATION:"myapplications",FORGETPASSWORD:"/forgot-password",RESETPASSWORD:"/reset-password/:token",RECENTAPPLICATIONS:"recent-applications",favoris:"/favoris",resend:"/resend-activation",confirmApplicationNotConnected:"/confirm-application",activation:"/activation/:token",blog:"/blog",blogFR:"/:language/blog",previewArticleFr:"/:language/blog/:urlArticle",previewArticleEn:"/blog/:urlArticle",previewFrO:"/:language/opportunities/:urlOpportunity",previewEnO:"/opportunities/:urlOpportunity",similar:"/RecommandedJobs",opportunities:"/opportunities",opportunitiesFR:"/:language/opportunities",opportunitiesPage:"/:language/opportunities?industry",applicationreceived:"/application-received",addArticle:"/add",editArticle:"/edit",listArticlesEN:"/articles",listArticleFr:"/:language/articles",activation:"/activation-account",addCategory:"/add",editCategory:"/edit",listCategoryEN:"/list-category",listcategoryFR:"/:language/list-category",categoryEN:"/blog/category/:urlCategory",categoryFR:"/:language/blog/category/:urlCategory",addOpportunity:"/add-opportunity",listOpportunities:"/list-opportunities",editOpportunity:"/edit-opportunity",updateprofile:"/update-profile",comments:"/comments",archived:"/archived",addUser:"/addUser",editUser:"/editUser",listUsers:"/listrtUsers",updateProfileCommun:"/update-profile",detail:"/detail"}},99959:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\app\[locale]\(dashboard)\backoffice\home\page.jsx#default`)},59245:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s={src:"/_next/static/media/activedashboard.f50c7817.png",height:15,width:22,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAAG1BMVEX///9MaXH////////////////////////////wbDDDAAAACXRSTlMCAISOci/EGz0Zyr2+AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAJklEQVR4nB3FwREAIAzDMDu0hf0n5kAfgYopFMySav+c2W+xJ6IXBmIAUr577aAAAAAASUVORK5CYII=",blurWidth:8,blurHeight:5}},63416:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s={src:"/_next/static/media/applicationsdashboard.9f162efa.png",height:26,width:21,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAIVBMVEX///////////////////////9MaXH///////////////+rUk3jAAAAC3RSTlNKJW9lVjQAe6kGCY2lCMwAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAtSURBVHicHcjJDQAgDMRAb27ov2BE/LE0dNtEEVU+QIoeUOoEyF0fd3H10bYHFHYApBwVcWMAAAAASUVORK5CYII=",blurWidth:6,blurHeight:8}},29565:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[8948,1479,1619,1336,4227,8077,5560,6636,9645,5476,8841,308,1812,3969,5048,2399],()=>a(80870));module.exports=s})();