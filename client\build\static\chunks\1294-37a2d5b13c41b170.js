"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1294],{98489:function(t,e,n){n.d(e,{default:function(){return v}});var r=n(2265),i=n(61994),o=n(50738),u=n(20801),c=n(4647),a=n(20956),s=n(95045),l=n(58698),f=n(57437);let d=(0,l.Z)(),p=(0,s.Z)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(t,e)=>{let{ownerState:n}=t;return[e.root,e[`maxWidth${(0,c.Z)(String(n.maxWidth))}`],n.fixed&&e.fixed,n.disableGutters&&e.disableGutters]}}),m=t=>(0,a.Z)({props:t,name:"<PERSON><PERSON><PERSON><PERSON><PERSON>",defaultTheme:d}),g=(t,e)=>{let{classes:n,fixed:r,disableGutters:i,maxWidth:a}=t,s={root:["root",a&&`maxWidth${(0,c.Z)(String(a))}`,r&&"fixed",i&&"disableGutters"]};return(0,u.Z)(s,t=>(0,o.ZP)(e,t),n)};var h=n(85657),b=n(16210),x=n(37053),v=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{createStyledComponent:e=p,useThemeProps:n=m,componentName:o="MuiContainer"}=t,u=e(t=>{let{theme:e,ownerState:n}=t;return{width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",...!n.disableGutters&&{paddingLeft:e.spacing(2),paddingRight:e.spacing(2),[e.breakpoints.up("sm")]:{paddingLeft:e.spacing(3),paddingRight:e.spacing(3)}}}},t=>{let{theme:e,ownerState:n}=t;return n.fixed&&Object.keys(e.breakpoints.values).reduce((t,n)=>{let r=e.breakpoints.values[n];return 0!==r&&(t[e.breakpoints.up(n)]={maxWidth:`${r}${e.breakpoints.unit}`}),t},{})},t=>{let{theme:e,ownerState:n}=t;return{..."xs"===n.maxWidth&&{[e.breakpoints.up("xs")]:{maxWidth:Math.max(e.breakpoints.values.xs,444)}},...n.maxWidth&&"xs"!==n.maxWidth&&{[e.breakpoints.up(n.maxWidth)]:{maxWidth:`${e.breakpoints.values[n.maxWidth]}${e.breakpoints.unit}`}}}});return r.forwardRef(function(t,e){let r=n(t),{className:c,component:a="div",disableGutters:s=!1,fixed:l=!1,maxWidth:d="lg",classes:p,...m}=r,h={...r,component:a,disableGutters:s,fixed:l,maxWidth:d},b=g(h,o);return(0,f.jsx)(u,{as:a,ownerState:h,className:(0,i.Z)(b.root,c),ref:e,...m})})}({createStyledComponent:(0,b.ZP)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(t,e)=>{let{ownerState:n}=t;return[e.root,e[`maxWidth${(0,h.Z)(String(n.maxWidth))}`],n.fixed&&e.fixed,n.disableGutters&&e.disableGutters]}}),useThemeProps:t=>(0,x.i)({props:t,name:"MuiContainer"})})},95045:function(t,e,n){let r=(0,n(29418).ZP)();e.Z=r},93826:function(t,e,n){n.d(e,{Z:function(){return i}});var r=n(53232);function i(t){let{theme:e,name:n,props:i}=t;return e&&e.components&&e.components[n]&&e.components[n].defaultProps?(0,r.Z)(e.components[n].defaultProps,i):i}},20956:function(t,e,n){n.d(e,{Z:function(){return o}});var r=n(93826),i=n(49695);function o(t){let{props:e,name:n,defaultTheme:o,themeId:u}=t,c=(0,i.Z)(o);return u&&(c=c[u]||c),(0,r.Z)({theme:c,name:n,props:e})}},9467:function(t,e,n){n.d(e,{Z:function(){return O}});var r=n(2265);function i(t){return"[object Object]"===Object.prototype.toString.call(t)||Array.isArray(t)}function o(t,e){let n=Object.keys(t),r=Object.keys(e);return n.length===r.length&&JSON.stringify(Object.keys(t.breakpoints||{}))===JSON.stringify(Object.keys(e.breakpoints||{}))&&n.every(n=>{let r=t[n],u=e[n];return"function"==typeof r?`${r}`==`${u}`:i(r)&&i(u)?o(r,u):r===u})}function u(t){return t.concat().sort((t,e)=>t.name>e.name?1:-1).map(t=>t.options)}function c(t){return"number"==typeof t}function a(t){return"string"==typeof t}function s(t){return"boolean"==typeof t}function l(t){return"[object Object]"===Object.prototype.toString.call(t)}function f(t){return Math.abs(t)}function d(t){return Math.sign(t)}function p(t){return b(t).map(Number)}function m(t){return t[g(t)]}function g(t){return Math.max(0,t.length-1)}function h(t,e=0){return Array.from(Array(t),(t,n)=>e+n)}function b(t){return Object.keys(t)}function x(t,e){return void 0!==e.MouseEvent&&t instanceof e.MouseEvent}function v(){let t=[],e={add:function(n,r,i,o={passive:!0}){let u;return"addEventListener"in n?(n.addEventListener(r,i,o),u=()=>n.removeEventListener(r,i,o)):(n.addListener(i),u=()=>n.removeListener(i)),t.push(u),e},clear:function(){t=t.filter(t=>t())}};return e}function y(t=0,e=0){let n=f(t-e);function r(n){return n<t||n>e}return{length:n,max:e,min:t,constrain:function(n){return r(n)?n<t?t:e:n},reachedAny:r,reachedMax:function(t){return t>e},reachedMin:function(e){return e<t},removeOffset:function(t){return n?t-n*Math.ceil((t-e)/n):t}}}function w(t){let e=t;function n(t){return c(t)?t:t.get()}return{get:function(){return e},set:function(t){e=n(t)},add:function(t){e+=n(t)},subtract:function(t){e-=n(t)}}}function S(t,e){let n="x"===t.scroll?function(t){return`translate3d(${t}px,0px,0px)`}:function(t){return`translate3d(0px,${t}px,0px)`},r=e.style,i=null,o=!1;return{clear:function(){o||(r.transform="",e.getAttribute("style")||e.removeAttribute("style"))},to:function(e){if(o)return;let u=Math.round(100*t.direction(e))/100;u!==i&&(r.transform=n(u),i=u)},toggleActive:function(t){o=!t}}}let k={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function E(t,e,n){let r,i,o,u,O;let M=t.ownerDocument,D=M.defaultView,L=function(t){function e(t,e){return function t(e,n){return[e,n].reduce((e,n)=>(b(n).forEach(r=>{let i=e[r],o=n[r],u=l(i)&&l(o);e[r]=u?t(i,o):o}),e),{})}(t,e||{})}return{mergeOptions:e,optionsAtMedia:function(n){let r=n.breakpoints||{},i=b(r).filter(e=>t.matchMedia(e).matches).map(t=>r[t]).reduce((t,n)=>e(t,n),{});return e(n,i)},optionsMediaQueries:function(e){return e.map(t=>b(t.breakpoints||{})).reduce((t,e)=>t.concat(e),[]).map(t.matchMedia)}}}(D),I=(O=[],{init:function(t,e){return(O=e.filter(({options:t})=>!1!==L.optionsAtMedia(t).active)).forEach(e=>e.init(t,L)),e.reduce((t,e)=>Object.assign(t,{[e.name]:e}),{})},destroy:function(){O=O.filter(t=>t.destroy())}}),A=v(),P=function(){let t,e={},n={init:function(e){t=e},emit:function(r){return(e[r]||[]).forEach(e=>e(t,r)),n},off:function(t,r){return e[t]=(e[t]||[]).filter(t=>t!==r),n},on:function(t,r){return e[t]=(e[t]||[]).concat([r]),n},clear:function(){e={}}};return n}(),{mergeOptions:F,optionsAtMedia:Z,optionsMediaQueries:j}=L,{on:C,off:N,emit:R}=P,T=!1,W=F(k,E.globalOptions),$=F(W),z=[];function H(e,n){!T&&($=Z(W=F(W,e)),z=n||z,function(){let{container:e,slides:n}=$;o=(a(e)?t.querySelector(e):e)||t.children[0];let r=a(n)?o.querySelectorAll(n):n;u=[].slice.call(r||o.children)}(),r=function e(n){let r=function(t,e,n,r,i,o,u){let l,k;let{align:E,axis:O,direction:M,startIndex:D,loop:L,duration:I,dragFree:A,dragThreshold:P,inViewThreshold:F,slidesToScroll:Z,skipSnaps:j,containScroll:C,watchResize:N,watchSlides:R,watchDrag:T,watchFocus:W}=o,$={measure:function(t){let{offsetTop:e,offsetLeft:n,offsetWidth:r,offsetHeight:i}=t;return{top:e,right:n+r,bottom:e+i,left:n,width:r,height:i}}},z=$.measure(e),H=n.map($.measure),V=function(t,e){let n="rtl"===e,r="y"===t,i=!r&&n?-1:1;return{scroll:r?"y":"x",cross:r?"x":"y",startEdge:r?"top":n?"right":"left",endEdge:r?"bottom":n?"left":"right",measureSize:function(t){let{height:e,width:n}=t;return r?e:n},direction:function(t){return t*i}}}(O,M),B=V.measureSize(z),G={measure:function(t){return t/100*B}},q=function(t,e){let n={start:function(){return 0},center:function(t){return(e-t)/2},end:function(t){return e-t}};return{measure:function(r,i){return a(t)?n[t](r):t(e,r,i)}}}(E,B),U=!L&&!!C,{slideSizes:_,slideSizesWithGaps:J,startGap:X,endGap:Q}=function(t,e,n,r,i,o){let{measureSize:u,startEdge:c,endEdge:a}=t,s=n[0]&&i,l=function(){if(!s)return 0;let t=n[0];return f(e[c]-t[c])}(),d=s?parseFloat(o.getComputedStyle(m(r)).getPropertyValue(`margin-${a}`)):0,p=n.map(u),h=n.map((t,e,n)=>{let r=e===g(n);return e?r?p[e]+d:n[e+1][c]-t[c]:p[e]+l}).map(f);return{slideSizes:p,slideSizesWithGaps:h,startGap:l,endGap:d}}(V,z,H,n,L||!!C,i),Y=function(t,e,n,r,i,o,u,a,s){let{startEdge:l,endEdge:d,direction:h}=t,b=c(n);return{groupSlides:function(t){return b?p(t).filter(t=>t%n==0).map(e=>t.slice(e,e+n)):t.length?p(t).reduce((n,c,s)=>{let p=m(n)||0,b=c===g(t),x=i[l]-o[p][l],v=i[l]-o[c][d],y=r||0!==p?0:h(u),w=f(v-(!r&&b?h(a):0)-(x+y));return s&&w>e+2&&n.push(c),b&&n.push(t.length),n},[]).map((e,n,r)=>{let i=Math.max(r[n-1]||0);return t.slice(i,e)}):[]}}}(V,B,Z,L,z,H,X,Q,0),{snaps:K,snapsAligned:tt}=function(t,e,n,r,i){let{startEdge:o,endEdge:u}=t,{groupSlides:c}=i,a=c(r).map(t=>m(t)[u]-t[0][o]).map(f).map(e.measure),s=r.map(t=>n[o]-t[o]).map(t=>-f(t)),l=c(s).map(t=>t[0]).map((t,e)=>t+a[e]);return{snaps:s,snapsAligned:l}}(V,q,z,H,Y),te=-m(K)+m(J),{snapsContained:tn,scrollContainLimit:tr}=function(t,e,n,r,i){let o=y(-e+t,0),u=n.map((t,e)=>{let{min:r,max:i}=o,u=o.constrain(t),c=e===g(n);return e?c||1>=f(r-u)?r:1>=f(i-u)?i:u:i}).map(t=>parseFloat(t.toFixed(3))),c=function(){let t=u[0],e=m(u);return y(u.lastIndexOf(t),u.indexOf(e)+1)}();return{snapsContained:function(){if(e<=t+2)return[o.max];if("keepSnaps"===r)return u;let{min:n,max:i}=c;return u.slice(n,i)}(),scrollContainLimit:c}}(B,te,tt,C,0),ti=U?tn:tt,{limit:to}=function(t,e,n){let r=e[0];return{limit:y(n?r-t:m(e),r)}}(te,ti,L),tu=function t(e,n,r){let{constrain:i}=y(0,e),o=e+1,u=c(n);function c(t){return r?f((o+t)%o):i(t)}function a(){return t(e,u,r)}let s={get:function(){return u},set:function(t){return u=c(t),s},add:function(t){return a().set(u+t)},clone:a};return s}(g(ti),D,L),tc=tu.clone(),ta=p(n),ts=({dragHandler:t,scrollBody:e,scrollBounds:n,options:{loop:r}})=>{r||n.constrain(t.pointerDown()),e.seek()},tl=({scrollBody:t,translate:e,location:n,offsetLocation:r,previousLocation:i,scrollLooper:o,slideLooper:u,dragHandler:c,animation:a,eventHandler:s,scrollBounds:l,options:{loop:f}},d)=>{let p=t.settled(),m=!l.shouldConstrain(),g=f?p:p&&m,h=g&&!c.pointerDown();h&&a.stop();let b=n.get()*d+i.get()*(1-d);r.set(b),f&&(o.loop(t.direction()),u.loop()),e.to(r.get()),h&&s.emit("settle"),g||s.emit("scroll")},tf=function(t,e,n,r){let i=v(),o=1e3/60,u=null,c=0,a=0;function s(t){if(!a)return;u||(u=t,n(),n());let i=t-u;for(u=t,c+=i;c>=o;)n(),c-=o;r(c/o),a&&(a=e.requestAnimationFrame(s))}function l(){e.cancelAnimationFrame(a),u=null,c=0,a=0}return{init:function(){i.add(t,"visibilitychange",()=>{t.hidden&&(u=null,c=0)})},destroy:function(){l(),i.clear()},start:function(){a||(a=e.requestAnimationFrame(s))},stop:l,update:n,render:r}}(r,i,()=>ts(tO),t=>tl(tO,t)),td=ti[tu.get()],tp=w(td),tm=w(td),tg=w(td),th=w(td),tb=function(t,e,n,r,i,o){let u=0,c=0,a=i,s=.68,l=t.get(),p=0;function m(t){return a=t,h}function g(t){return s=t,h}let h={direction:function(){return c},duration:function(){return a},velocity:function(){return u},seek:function(){let e=r.get()-t.get(),i=0;return a?(n.set(t),u+=e/a,u*=s,l+=u,t.add(u),i=l-p):(u=0,n.set(r),t.set(r),i=e),c=d(i),p=l,h},settled:function(){return .001>f(r.get()-e.get())},useBaseFriction:function(){return g(.68)},useBaseDuration:function(){return m(i)},useFriction:g,useDuration:m};return h}(tp,tg,tm,th,I,0),tx=function(t,e,n,r,i){let{reachedAny:o,removeOffset:u,constrain:c}=r;function a(t){return t.concat().sort((t,e)=>f(t)-f(e))[0]}function s(e,r){let i=[e,e+n,e-n];if(!t)return e;if(!r)return a(i);let o=i.filter(t=>d(t)===r);return o.length?a(o):m(i)-n}return{byDistance:function(n,r){let a=i.get()+n,{index:l,distance:d}=function(n){let r=t?u(n):c(n),{index:i}=e.map((t,e)=>({diff:s(t-r,0),index:e})).sort((t,e)=>f(t.diff)-f(e.diff))[0];return{index:i,distance:r}}(a),p=!t&&o(a);if(!r||p)return{index:l,distance:n};let m=n+s(e[l]-d,0);return{index:l,distance:m}},byIndex:function(t,n){let r=s(e[t]-i.get(),n);return{index:t,distance:r}},shortcut:s}}(L,ti,te,to,th),tv=function(t,e,n,r,i,o,u){function c(i){let c=i.distance,a=i.index!==e.get();o.add(c),c&&(r.duration()?t.start():(t.update(),t.render(1),t.update())),a&&(n.set(e.get()),e.set(i.index),u.emit("select"))}return{distance:function(t,e){c(i.byDistance(t,e))},index:function(t,n){let r=e.clone().set(t);c(i.byIndex(r.get(),n))}}}(tf,tu,tc,tb,tx,th,u),ty=function(t){let{max:e,length:n}=t;return{get:function(t){return n?-((t-e)/n):0}}}(to),tw=v(),tS=function(t,e,n,r){let i;let o={},u=null,c=null,a=!1;return{init:function(){i=new IntersectionObserver(t=>{a||(t.forEach(t=>{o[e.indexOf(t.target)]=t}),u=null,c=null,n.emit("slidesInView"))},{root:t.parentElement,threshold:r}),e.forEach(t=>i.observe(t))},destroy:function(){i&&i.disconnect(),a=!0},get:function(t=!0){if(t&&u)return u;if(!t&&c)return c;let e=b(o).reduce((e,n)=>{let r=parseInt(n),{isIntersecting:i}=o[r];return(t&&i||!t&&!i)&&e.push(r),e},[]);return t&&(u=e),t||(c=e),e}}}(e,n,u,F),{slideRegistry:tk}=function(t,e,n,r,i,o){let{groupSlides:u}=i,{min:c,max:a}=r;return{slideRegistry:function(){let r=u(o);return 1===n.length?[o]:t&&"keepSnaps"!==e?r.slice(c,a).map((t,e,n)=>{let r=e===g(n);return e?r?h(g(o)-m(n)[0]+1,m(n)[0]):t:h(m(n[0])+1)}):r}()}}(U,C,ti,tr,Y,ta),tE=function(t,e,n,r,i,o,u,a){let l={passive:!0,capture:!0},f=0;function d(t){"Tab"===t.code&&(f=new Date().getTime())}return{init:function(p){a&&(o.add(document,"keydown",d,!1),e.forEach((e,d)=>{o.add(e,"focus",e=>{(s(a)||a(p,e))&&function(e){if(new Date().getTime()-f>10)return;u.emit("slideFocusStart"),t.scrollLeft=0;let o=n.findIndex(t=>t.includes(e));c(o)&&(i.useDuration(0),r.index(o,0),u.emit("slideFocus"))}(d)},l)}))}}}(t,n,tk,tv,tb,tw,u,W),tO={ownerDocument:r,ownerWindow:i,eventHandler:u,containerRect:z,slideRects:H,animation:tf,axis:V,dragHandler:function(t,e,n,r,i,o,u,c,a,l,p,m,g,h,b,w,S,k,E){let{cross:O,direction:M}=t,D=["INPUT","SELECT","TEXTAREA"],L={passive:!1},I=v(),A=v(),P=y(50,225).constrain(h.measure(20)),F={mouse:300,touch:400},Z={mouse:500,touch:600},j=b?43:25,C=!1,N=0,R=0,T=!1,W=!1,$=!1,z=!1;function H(t){if(!x(t,r)&&t.touches.length>=2)return V(t);let e=o.readPoint(t),n=o.readPoint(t,O),u=f(e-N),a=f(n-R);if(!W&&!z&&(!t.cancelable||!(W=u>a)))return V(t);let s=o.pointerMove(t);u>w&&($=!0),l.useFriction(.3).useDuration(.75),c.start(),i.add(M(s)),t.preventDefault()}function V(t){let e=p.byDistance(0,!1).index!==m.get(),n=o.pointerUp(t)*(b?Z:F)[z?"mouse":"touch"],r=function(t,e){let n=m.add(-1*d(t)),r=p.byDistance(t,!b).distance;return b||f(t)<P?r:S&&e?.5*r:p.byIndex(n.get(),0).distance}(M(n),e),i=function(t,e){var n,r;if(0===t||0===e||f(t)<=f(e))return 0;let i=(n=f(t),r=f(e),f(n-r));return f(i/t)}(n,r);W=!1,T=!1,A.clear(),l.useDuration(j-10*i).useFriction(.68+i/50),a.distance(r,!b),z=!1,g.emit("pointerUp")}function B(t){$&&(t.stopPropagation(),t.preventDefault(),$=!1)}return{init:function(t){E&&I.add(e,"dragstart",t=>t.preventDefault(),L).add(e,"touchmove",()=>void 0,L).add(e,"touchend",()=>void 0).add(e,"touchstart",c).add(e,"mousedown",c).add(e,"touchcancel",V).add(e,"contextmenu",V).add(e,"click",B,!0);function c(c){(s(E)||E(t,c))&&function(t){let c=x(t,r);z=c,$=b&&c&&!t.buttons&&C,C=f(i.get()-u.get())>=2,c&&0!==t.button||function(t){let e=t.nodeName||"";return D.includes(e)}(t.target)||(T=!0,o.pointerDown(t),l.useFriction(0).useDuration(0),i.set(u),function(){let t=z?n:e;A.add(t,"touchmove",H,L).add(t,"touchend",V).add(t,"mousemove",H,L).add(t,"mouseup",V)}(),N=o.readPoint(t),R=o.readPoint(t,O),g.emit("pointerDown"))}(c)}},destroy:function(){I.clear(),A.clear()},pointerDown:function(){return T}}}(V,t,r,i,th,function(t,e){let n,r;function i(t){return t.timeStamp}function o(n,r){let i=r||t.scroll,o=`client${"x"===i?"X":"Y"}`;return(x(n,e)?n:n.touches[0])[o]}return{pointerDown:function(t){return n=t,r=t,o(t)},pointerMove:function(t){let e=o(t)-o(r),u=i(t)-i(n)>170;return r=t,u&&(n=t),e},pointerUp:function(t){if(!n||!r)return 0;let e=o(r)-o(n),u=i(t)-i(n),c=i(t)-i(r)>170,a=e/u;return u&&!c&&f(a)>.1?a:0},readPoint:o}}(V,i),tp,tf,tv,tb,tx,tu,u,G,A,P,j,0,T),eventStore:tw,percentOfView:G,index:tu,indexPrevious:tc,limit:to,location:tp,offsetLocation:tg,previousLocation:tm,options:o,resizeHandler:function(t,e,n,r,i,o,u){let c,a;let l=[t].concat(r),d=[],p=!1;function m(t){return i.measureSize(u.measure(t))}return{init:function(i){o&&(a=m(t),d=r.map(m),c=new ResizeObserver(n=>{(s(o)||o(i,n))&&function(n){for(let o of n){if(p)return;let n=o.target===t,u=r.indexOf(o.target),c=n?a:d[u];if(f(m(n?t:r[u])-c)>=.5){i.reInit(),e.emit("resize");break}}}(n)}),n.requestAnimationFrame(()=>{l.forEach(t=>c.observe(t))}))},destroy:function(){p=!0,c&&c.disconnect()}}}(e,u,i,n,V,N,$),scrollBody:tb,scrollBounds:function(t,e,n,r,i){let o=i.measure(10),u=i.measure(50),c=y(.1,.99),a=!1;function s(){return!!(!a&&t.reachedAny(n.get())&&t.reachedAny(e.get()))}return{shouldConstrain:s,constrain:function(i){if(!s())return;let a=t.reachedMin(e.get())?"min":"max",l=f(t[a]-e.get()),d=n.get()-e.get(),p=c.constrain(l/u);n.subtract(d*p),!i&&f(d)<o&&(n.set(t.constrain(n.get())),r.useDuration(25).useBaseFriction())},toggleActive:function(t){a=!t}}}(to,tg,th,tb,G),scrollLooper:function(t,e,n,r){let{reachedMin:i,reachedMax:o}=y(e.min+.1,e.max+.1);return{loop:function(e){if(!(1===e?o(n.get()):-1===e&&i(n.get())))return;let u=-1*e*t;r.forEach(t=>t.add(u))}}}(te,to,tg,[tp,tg,tm,th]),scrollProgress:ty,scrollSnapList:ti.map(ty.get),scrollSnaps:ti,scrollTarget:tx,scrollTo:tv,slideLooper:function(t,e,n,r,i,o,u,c,a){let s=p(i),l=m(d(p(i).reverse(),u[0]),n,!1).concat(m(d(s,e-u[0]-1),-n,!0));function f(t,e){return t.reduce((t,e)=>t-i[e],e)}function d(t,e){return t.reduce((t,n)=>f(t,e)>0?t.concat([n]):t,[])}function m(i,u,s){let l=o.map((t,n)=>({start:t-r[n]+.5+u,end:t+e-.5+u}));return i.map(e=>{let r=s?0:-n,i=s?n:0,o=l[e][s?"end":"start"];return{index:e,loopPoint:o,slideLocation:w(-1),translate:S(t,a[e]),target:()=>c.get()>o?r:i}})}return{canLoop:function(){return l.every(({index:t})=>.1>=f(s.filter(e=>e!==t),e))},clear:function(){l.forEach(t=>t.translate.clear())},loop:function(){l.forEach(t=>{let{target:e,translate:n,slideLocation:r}=t,i=e();i!==r.get()&&(n.to(i),r.set(i))})},loopPoints:l}}(V,B,te,_,J,K,ti,tg,n),slideFocus:tE,slidesHandler:(k=!1,{init:function(t){R&&(l=new MutationObserver(e=>{!k&&(s(R)||R(t,e))&&function(e){for(let n of e)if("childList"===n.type){t.reInit(),u.emit("slidesChanged");break}}(e)})).observe(e,{childList:!0})},destroy:function(){l&&l.disconnect(),k=!0}}),slidesInView:tS,slideIndexes:ta,slideRegistry:tk,slidesToScroll:Y,target:th,translate:S(V,e)};return tO}(t,o,u,M,D,n,P);return n.loop&&!r.slideLooper.canLoop()?e(Object.assign({},n,{loop:!1})):r}($),j([W,...z.map(({options:t})=>t)]).forEach(t=>A.add(t,"change",V)),$.active&&(r.translate.to(r.location.get()),r.animation.init(),r.slidesInView.init(),r.slideFocus.init(U),r.eventHandler.init(U),r.resizeHandler.init(U),r.slidesHandler.init(U),r.options.loop&&r.slideLooper.loop(),o.offsetParent&&u.length&&r.dragHandler.init(U),i=I.init(U,z)))}function V(t,e){let n=q();B(),H(F({startIndex:n},t),e),P.emit("reInit")}function B(){r.dragHandler.destroy(),r.eventStore.clear(),r.translate.clear(),r.slideLooper.clear(),r.resizeHandler.destroy(),r.slidesHandler.destroy(),r.slidesInView.destroy(),r.animation.destroy(),I.destroy(),A.clear()}function G(t,e,n){$.active&&!T&&(r.scrollBody.useBaseFriction().useDuration(!0===e?0:$.duration),r.scrollTo.index(t,n||0))}function q(){return r.index.get()}let U={canScrollNext:function(){return r.index.add(1).get()!==q()},canScrollPrev:function(){return r.index.add(-1).get()!==q()},containerNode:function(){return o},internalEngine:function(){return r},destroy:function(){T||(T=!0,A.clear(),B(),P.emit("destroy"),P.clear())},off:N,on:C,emit:R,plugins:function(){return i},previousScrollSnap:function(){return r.indexPrevious.get()},reInit:V,rootNode:function(){return t},scrollNext:function(t){G(r.index.add(1).get(),t,-1)},scrollPrev:function(t){G(r.index.add(-1).get(),t,1)},scrollProgress:function(){return r.scrollProgress.get(r.offsetLocation.get())},scrollSnapList:function(){return r.scrollSnapList},scrollTo:G,selectedScrollSnap:q,slideNodes:function(){return u},slidesInView:function(){return r.slidesInView.get()},slidesNotInView:function(){return r.slidesInView.get(!1)}};return H(e,n),setTimeout(()=>P.emit("init"),0),U}function O(t={},e=[]){let n=(0,r.useRef)(t),i=(0,r.useRef)(e),[c,a]=(0,r.useState)(),[s,l]=(0,r.useState)(),f=(0,r.useCallback)(()=>{c&&c.reInit(n.current,i.current)},[c]);return(0,r.useEffect)(()=>{o(n.current,t)||(n.current=t,f())},[t,f]),(0,r.useEffect)(()=>{!function(t,e){if(t.length!==e.length)return!1;let n=u(t),r=u(e);return n.every((t,e)=>o(t,r[e]))}(i.current,e)&&(i.current=e,f())},[e,f]),(0,r.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&s){E.globalOptions=O.globalOptions;let t=E(s,n.current,i.current);return a(t),()=>t.destroy()}a(void 0)},[s,a]),[l,c]}E.globalOptions=void 0,O.globalOptions=void 0}}]);