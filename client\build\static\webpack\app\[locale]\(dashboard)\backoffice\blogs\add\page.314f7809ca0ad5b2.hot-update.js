"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/AddArticleEN.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! suneditor-react */ \"(app-pages-browser)/./node_modules/suneditor-react/dist/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(suneditor_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n/* harmony import */ var suneditor_dist_css_suneditor_min_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! suneditor/dist/css/suneditor.min.css */ \"(app-pages-browser)/./node_modules/suneditor/dist/css/suneditor.min.css\");\n/* harmony import */ var _feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @feelinglovelynow/slug */ \"(app-pages-browser)/./node_modules/@feelinglovelynow/slug/dist/index.js\");\n/* harmony import */ var suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! suneditor/src/plugins */ \"(app-pages-browser)/./node_modules/suneditor/src/plugins/index.js\");\n/* harmony import */ var _FaqSection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./FaqSection */ \"(app-pages-browser)/./src/features/blog/components/FaqSection.jsx\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/DatePicker/DatePicker.js\");\n/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/x-date-pickers/AdapterDayjs */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/AdapterDayjs/AdapterDayjs.js\");\n/* harmony import */ var _mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/x-date-pickers/internals/demo */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/internals/demo/DemoContainer.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var react_tag_input__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! react-tag-input */ \"(app-pages-browser)/./node_modules/react-tag-input/dist/index.js\");\n/* harmony import */ var _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomTextInput */ \"(app-pages-browser)/./src/components/ui/CustomTextInput.jsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AddArticleEN(param) {\n    let { errors, touched, setFieldValue, values, onImageSelect, filteredCategories, categories, onCategoriesSelect, debounce } = param;\n    _s();\n    const KeyCodes = {\n        comma: 188,\n        enter: 13\n    };\n    const delimiters = [\n        KeyCodes.comma,\n        KeyCodes.enter\n    ];\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [highlights, setHighlights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(values.descriptionEN || \"\");\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(values.urlEN || \"\");\n    const [publishNow, setPublishNow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const language = \"en\";\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedTitle = localStorage.getItem(\"title\");\n        return savedTitle ? JSON.parse(savedTitle) : \"\";\n    });\n    const [metatitle, setMetatitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetatitle = localStorage.getItem(\"metatitle\");\n        return savedMetatitle ? JSON.parse(savedMetatitle) : \"\";\n    });\n    const [metaDescription, setMetaDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetadescription = localStorage.getItem(\"metaDescription\");\n        return savedMetadescription ? JSON.parse(savedMetadescription) : \"\";\n    });\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedContent = localStorage.getItem(\"content\");\n        return savedContent ? JSON.parse(savedContent) : \"\";\n    });\n    const handlePaste = (event, cleanData, maxCharCount)=>{\n        let html = cleanData;\n        // Correction des balises non fermées\n        html = html.replace(/<strong>(.*?)$/g, \"<strong>$1</strong>\");\n        return html;\n    };\n    const handleEditorChange = (newContent)=>{\n        debounce();\n        setContent(newContent);\n        setFieldValue(\"contentEN\", newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (title) {\n            localStorage.setItem(\"title\", JSON.stringify(title));\n        }\n        if (content) {\n            localStorage.setItem(\"content\", JSON.stringify(content));\n        }\n        if (metatitle) {\n            localStorage.setItem(\"metatitle\", JSON.stringify(metatitle));\n        }\n        if (metaDescription) {\n            localStorage.setItem(\"metaDescription\", JSON.stringify(metaDescription));\n        }\n    }, [\n        title,\n        content,\n        metatitle,\n        metaDescription\n    ]);\n    const handlePhotoChange = async ()=>{\n        const selectedFile = imageInputRef.current.files[0];\n        setSelectedImage(imageInputRef.current.files[0]);\n        if (selectedFile) {\n            onImageSelect(selectedFile, language);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setFieldValue(\"titleEN\", title);\n        setFieldValue(\"descriptionEN\", description);\n        setFieldValue(\"urlEN\", url);\n        setFieldValue(\"keywordsEN\", tags.map((t)=>t.text));\n        setFieldValue(\"highlightsEN\", highlights.map((h)=>h.text));\n        setFieldValue(\"contentEN\", content);\n        setFieldValue(\"metaTitleEN\", metatitle);\n        setFieldValue(\"metaDescriptionEN\", metaDescription);\n    }, [\n        title,\n        description,\n        url,\n        tags,\n        highlights,\n        content,\n        metatitle,\n        metaDescription\n    ]);\n    const useSaveFileHook = (0,_features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__.useSaveFile)();\n    let uuidPhoto;\n    uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n    const handlePhotoBlogChange = async (file, info, core, uploadHandler)=>{\n        if (file instanceof HTMLImageElement) {\n            const src = file.src;\n            if (src.startsWith(\"data:image\")) {\n                const base64Data = src.split(\",\")[1];\n                const contentType = src.match(/data:(.*?);base64/)[1];\n                const byteCharacters = atob(base64Data);\n                const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i)=>byteCharacters.charCodeAt(i));\n                const byteArray = new Uint8Array(byteNumbers);\n                const blob = new Blob([\n                    byteArray\n                ], {\n                    type: contentType\n                });\n                const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                const selectedFile = new File([\n                    blob\n                ], fileName, {\n                    type: contentType\n                });\n                await uploadFile(selectedFile, uploadHandler, core, file);\n            } else {\n                fetch(src).then((response)=>response.blob()).then((blob)=>{\n                    const contentType = blob.type;\n                    const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                    const selectedFile = new File([\n                        blob\n                    ], fileName, {\n                        type: contentType\n                    });\n                    uploadFile(selectedFile, uploadHandler, core, file);\n                }).catch((error)=>console.error(\"Error converting image URL to Blob:\", error));\n            }\n        } else {\n            console.error(\"File is not an HTMLImageElement.\");\n        }\n    };\n    const uploadFile = (selectedFile, uploadHandler, core, originalImage)=>{\n        let uuidPhoto;\n        uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n        const formData = new FormData();\n        formData.append(\"file\", selectedFile);\n        const extension = selectedFile.name.split(\".\").pop();\n        const currentYear = new Date().getFullYear();\n        useSaveFileHook.mutate({\n            resource: \"blogs\",\n            folder: currentYear.toString(),\n            filename: uuidPhoto,\n            body: {\n                formData,\n                t\n            }\n        }, {\n            onSuccess: (dataUUID)=>{\n                const uuidPhotoFileName = dataUUID.message === \"uuid exist\" ? dataUUID.uuid : `${uuidPhoto}.${extension}`;\n                const imageUrl = `${\"http://localhost:4000/api/v1\"}/files/${uuidPhotoFileName}`;\n                originalImage.src = imageUrl;\n                uploadHandler({\n                    result: [\n                        {\n                            id: uuidPhotoFileName,\n                            url: imageUrl\n                        }\n                    ]\n                });\n            },\n            onError: (error)=>{\n                console.error(\"Error uploading file:\", error);\n            }\n        });\n    };\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Enhanced document import handlers\n    const handleContentExtracted = (extractedContent)=>{\n        setFieldValue(\"contentEN\", extractedContent);\n        setContent(extractedContent);\n        debounce(); // Trigger autosave\n    };\n    const handleMetadataExtracted = (metadata)=>{\n        // Auto-fill form fields with extracted metadata\n        if (metadata.title && !values.titleEN) {\n            setFieldValue(\"titleEN\", metadata.title);\n            // Auto-generate URL from title\n            const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_10__.slug)(metadata.title);\n            setFieldValue(\"urlEN\", url);\n        }\n        if (metadata.description && !values.descriptionEN) {\n            setFieldValue(\"descriptionEN\", metadata.description);\n        }\n        if (metadata.keywords && metadata.keywords.length > 0) {\n            // Convert keywords to the expected format for ReactTags\n            const keywordTags = metadata.keywords.map((keyword, index)=>({\n                    id: `extracted-${index}`,\n                    text: keyword\n                }));\n            // Merge with existing keywords\n            const existingKeywords = values.keywordsEN || [];\n            const mergedKeywords = [\n                ...existingKeywords,\n                ...keywordTags\n            ];\n            setFieldValue(\"keywordsEN\", mergedKeywords.map((tag)=>tag.text));\n        }\n        debounce(); // Trigger autosave\n    };\n    // Legacy file change handler (kept for backward compatibility)\n    const handleFileChange = async (event)=>{\n        const file = event.target.files[0];\n        if (!file || file.type !== \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\") {\n            setError(\"Please upload a valid .docx file.\");\n            return;\n        }\n        const reader = new FileReader();\n        reader.onload = async (event)=>{\n            const arrayBuffer = event.target.result;\n            try {\n                const result = await mammoth__WEBPACK_IMPORTED_MODULE_4__.convertToHtml({\n                    arrayBuffer\n                });\n                setFieldValue(\"contentEN\", result.value);\n                setContent(result.value);\n                setError(null);\n            } catch (err) {\n                console.error(\"Mammoth conversion error:\", err);\n                setError(\"Failed to convert the DOCX file.\");\n            }\n        };\n        reader.readAsArrayBuffer(file);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"label-pentabell\",\n                children: \"Add article English : \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    label: t(\"createArticle:title\"),\n                                    name: \"titleEN\",\n                                    value: title,\n                                    onChange: (e)=>{\n                                        const v = e.target.value;\n                                        setTitle(v);\n                                        setUrl((0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_10__.slug)(v));\n                                        debounce();\n                                    },\n                                    error: touched.titleEN && errors.titleEN\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:categories\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                multiple: true,\n                                                className: \"input-pentabell\",\n                                                id: \"tags-standard\",\n                                                options: filteredCategories.length > 0 ? filteredCategories : categories,\n                                                getOptionLabel: (option)=>option.name,\n                                                selected: values.categoryEN.length > 0 ? (filteredCategories.length > 0 ? filteredCategories : categories).filter((category)=>values.categoryEN.includes(category.id)) : [],\n                                                onChange: (event, selectedOptions)=>{\n                                                    const categoryIds = selectedOptions.map((category)=>category.id);\n                                                    setFieldValue(\"categoryEN\", categoryIds);\n                                                    onCategoriesSelect(categoryIds);\n                                                },\n                                                renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        ...params,\n                                                        className: \"input-pentabell  multiple-select\",\n                                                        variant: \"standard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this),\n                            touched.category && errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"label-error\",\n                                children: errors.category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Description\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"descriptionEN\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: values.descriptionEN,\n                                    onChange: (e)=>{\n                                        const descriptionEN = e.target.value;\n                                        setFieldValue(\"descriptionEN\", descriptionEN);\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.descriptionEN && touched.descriptionEN ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"descriptionEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Highlights\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"tags\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                        tags: highlights,\n                                        className: \"input-pentabell\" + (errors.highlightsEN && touched.highlightsEN ? \" is-invalid\" : \"\"),\n                                        delimiters: delimiters,\n                                        handleDelete: (i)=>{\n                                            const updatedTags = highlights.filter((tag, index)=>index !== i);\n                                            setHighlights(updatedTags);\n                                            setFieldValue(\"highlightsEN\", updatedTags.map((tag)=>tag.text));\n                                        },\n                                        handleAddition: (tag)=>{\n                                            setHighlights([\n                                                ...highlights,\n                                                tag\n                                            ]);\n                                            setFieldValue(\"highlightsEN\", [\n                                                ...highlights,\n                                                tag\n                                            ].map((item)=>item.text));\n                                        },\n                                        inputFieldPosition: \"bottom\",\n                                        autocomplete: true,\n                                        allowDragDrop: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"keywordsEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 407,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 406,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DocumentImporter, {\n                onContentExtracted: handleContentExtracted,\n                onMetadataExtracted: handleMetadataExtracted,\n                language: \"EN\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"none\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"file\",\n                        accept: \".docx\",\n                        onChange: handleFileChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"red\"\n                        },\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 460,\n                        columnNumber: 19\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 458,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((suneditor_react__WEBPACK_IMPORTED_MODULE_3___default()), {\n                setContents: content || values?.contentEN || \"\",\n                onChange: (newContent)=>{\n                    setContent(newContent);\n                    setFieldValue(\"contentEN\", newContent);\n                    debounce();\n                },\n                onPaste: handlePaste,\n                setOptions: {\n                    cleanHTML: false,\n                    disableHtmlSanitizer: true,\n                    addTagsWhitelist: \"h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button\",\n                    plugins: suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                    buttonList: [\n                        [\n                            \"undo\",\n                            \"redo\"\n                        ],\n                        [\n                            \"font\",\n                            \"fontSize\",\n                            \"formatBlock\"\n                        ],\n                        [\n                            \"bold\",\n                            \"underline\",\n                            \"italic\",\n                            \"strike\",\n                            \"subscript\",\n                            \"superscript\"\n                        ],\n                        [\n                            \"fontColor\",\n                            \"hiliteColor\"\n                        ],\n                        [\n                            \"align\",\n                            \"list\",\n                            \"lineHeight\"\n                        ],\n                        [\n                            \"outdent\",\n                            \"indent\"\n                        ],\n                        [\n                            \"table\",\n                            \"horizontalRule\",\n                            \"link\",\n                            \"image\",\n                            \"video\"\n                        ],\n                        [\n                            \"fullScreen\",\n                            \"showBlocks\",\n                            \"codeView\"\n                        ],\n                        [\n                            \"preview\",\n                            \"print\"\n                        ],\n                        [\n                            \"removeFormat\"\n                        ]\n                    ],\n                    imageUploadHandler: handlePhotoBlogChange,\n                    defaultTag: \"div\",\n                    minHeight: \"300px\",\n                    maxHeight: \"400px\",\n                    showPathLabel: false,\n                    font: [\n                        \"Proxima-Nova-Regular\",\n                        \"Proxima-Nova-Medium\",\n                        \"Proxima-Nova-Semibold\",\n                        \"Proxima-Nova-Bold\",\n                        \"Proxima-Nova-Extrabold\",\n                        \"Proxima-Nova-Black\",\n                        \"Proxima-Nova-Light\",\n                        \"Proxima-Nova-Thin\",\n                        \"Arial\",\n                        \"Times New Roman\",\n                        \"Sans-Serif\"\n                    ],\n                    charCounter: true,\n                    charCounterType: \"byte\",\n                    resizingBar: false,\n                    colorList: [\n                        // Standard Colors\n                        [\n                            \"#234791\",\n                            \"#d69b19\",\n                            \"#cc3233\",\n                            \"#009966\",\n                            \"#0b3051\",\n                            \"#2BBFAD\",\n                            \"#0b305100\",\n                            \"#0a305214\",\n                            \"#743794\",\n                            \"#ff0000\",\n                            \"#ff5e00\",\n                            \"#ffe400\",\n                            \"#abf200\",\n                            \"#00d8ff\",\n                            \"#0055ff\",\n                            \"#6600ff\",\n                            \"#ff00dd\",\n                            \"#000000\",\n                            \"#ffd8d8\",\n                            \"#fae0d4\",\n                            \"#faf4c0\",\n                            \"#e4f7ba\",\n                            \"#d4f4fa\",\n                            \"#d9e5ff\",\n                            \"#e8d9ff\",\n                            \"#ffd9fa\",\n                            \"#f1f1f1\",\n                            \"#ffa7a7\",\n                            \"#ffc19e\",\n                            \"#faed7d\",\n                            \"#cef279\",\n                            \"#b2ebf4\",\n                            \"#b2ccff\",\n                            \"#d1b2ff\",\n                            \"#ffb2f5\",\n                            \"#bdbdbd\",\n                            \"#f15f5f\",\n                            \"#f29661\",\n                            \"#e5d85c\",\n                            \"#bce55c\",\n                            \"#5cd1e5\",\n                            \"#6699ff\",\n                            \"#a366ff\",\n                            \"#f261df\",\n                            \"#8c8c8c\",\n                            \"#980000\",\n                            \"#993800\",\n                            \"#998a00\",\n                            \"#6b9900\",\n                            \"#008299\",\n                            \"#003399\",\n                            \"#3d0099\",\n                            \"#990085\",\n                            \"#353535\",\n                            \"#670000\",\n                            \"#662500\",\n                            \"#665c00\",\n                            \"#476600\",\n                            \"#005766\",\n                            \"#002266\",\n                            \"#290066\",\n                            \"#660058\",\n                            \"#222222\"\n                        ]\n                    ]\n                },\n                onImageUpload: handlePhotoBlogChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 462,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 587,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaqSection__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                values: values,\n                setFieldValue: setFieldValue,\n                errors: errors,\n                touched: touched,\n                language: \"EN\",\n                debounce: debounce\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 588,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 596,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:metaTitle\"),\n                                        \" (\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: values.metaTitleEN?.length > 65 ? \" text-danger\" : \"\",\n                                            children: [\n                                                \" \",\n                                                values.metaTitleEN?.length,\n                                                \" / 65\",\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        \")\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            variant: \"standard\",\n                                            name: \"metaTitleEN\",\n                                            type: \"text\",\n                                            value: metatitle || values.metaTitleEN,\n                                            onChange: (e)=>{\n                                                const metaTitleEN = e.target.value;\n                                                setFieldValue(\"metaTitleEN\", metaTitleEN);\n                                                setMetatitle(metaTitleEN);\n                                                debounce();\n                                            },\n                                            className: \"input-pentabell\" + (errors.metaTitleEN && touched.metaTitleEN ? \" is-invalid\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"metaTitleEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 600,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 598,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                label: t(\"createArticle:url\"),\n                                name: \"urlEN\",\n                                value: url,\n                                onChange: (e)=>setUrl(e.target.value),\n                                error: touched.urlEN && errors.urlEN\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 640,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 639,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 638,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 597,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:metaDescription\"),\n                                \" (\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: values.metaDescriptionEN?.length > 160 ? \" text-danger\" : \"\",\n                                    children: [\n                                        values.metaDescriptionEN?.length,\n                                        \" / 160\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                \")\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"metaDescriptionEN\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: metaDescription || values.metaDescriptionEN,\n                                    onChange: (e)=>{\n                                        const metaDescriptionEN = e.target.value;\n                                        setFieldValue(\"metaDescriptionEN\", metaDescriptionEN);\n                                        setMetaDescription(metaDescriptionEN);\n                                        debounce();\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.metaDescriptionEN && touched.metaDescriptionEN ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"metaDescriptionEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 683,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 653,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 652,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 651,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 650,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:featuredImage\"),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"upload-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: `image-upload-en`,\n                                        className: \"file-labels\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                id: `image-upload-en`,\n                                                name: \"imageEN\",\n                                                accept: \".png, .jpg, .jpeg, .webp\",\n                                                ref: imageInputRef,\n                                                onChange: (e)=>{\n                                                    setFieldValue(\"imageEN\", e.target.files[0]);\n                                                    handlePhotoChange();\n                                                },\n                                                className: \"file-input\" + (errors.imageEN && touched.imageEN ? \" is-invalid\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"upload-area\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"icon-pic\",\n                                                            style: {\n                                                                backgroundImage: `url(\"${selectedImage ? URL.createObjectURL(selectedImage) : values.imageEN ? `${process.env.REACT_APP_API_URL}${_utils_urls__WEBPACK_IMPORTED_MODULE_7__.API_URLS.files}/${values.imageEN}` : _assets_images_add_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"].src}\")`,\n                                                                backgroundSize: \"cover\",\n                                                                backgroundRepeat: \"no-repeat\",\n                                                                backgroundPosition: \"center\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                            lineNumber: 717,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-text\",\n                                                                children: t(\"createArticle:addFeatImg\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-description\",\n                                                                children: t(\"createArticle:clickBox\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                                name: \"image\",\n                                                component: \"div\",\n                                                className: \"invalid-feedback error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 743,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 695,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 694,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 693,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 692,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:alt\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"altEN\",\n                                        type: \"text\",\n                                        value: values.altEN,\n                                        onChange: (e)=>{\n                                            setFieldValue(\"altEN\", e.target.value);\n                                            debounce();\n                                        },\n                                        className: \"input-pentabell\" + (errors.altEN && touched.altEN ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"altEN\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 758,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 757,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 756,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:visibility\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"select-pentabell\",\n                                            variant: \"standard\",\n                                            value: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.Visibility.filter((option)=>values.visibilityEN === option),\n                                            selected: values.visibilityEN,\n                                            onChange: (event)=>{\n                                                setFieldValue(\"visibilityEN\", event.target.value);\n                                            },\n                                            children: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.Visibility.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    value: item,\n                                                    children: item\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"visibilityEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 785,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 784,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 782,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:keyword\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            id: \"tags\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                                tags: tags,\n                                                className: \"input-pentabell\" + (errors.keywordsEN && touched.keywordsEN ? \" is-invalid\" : \"\"),\n                                                delimiters: delimiters,\n                                                handleDelete: (i)=>{\n                                                    const updatedTags = tags.filter((tag, index)=>index !== i);\n                                                    setTags(updatedTags);\n                                                    setFieldValue(\"keywordsEN\", updatedTags.map((tag)=>tag.text));\n                                                },\n                                                handleAddition: (tag)=>{\n                                                    setTags([\n                                                        ...tags,\n                                                        tag\n                                                    ]);\n                                                    setFieldValue(\"keywordsEN\", [\n                                                        ...tags,\n                                                        tag\n                                                    ].map((item)=>item.text));\n                                                    debounce();\n                                                },\n                                                inputFieldPosition: \"bottom\",\n                                                autocomplete: true,\n                                                allowDragDrop: false\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 821,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"keywordsEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 854,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 818,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 817,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 815,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 755,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"label-form\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.Field, {\n                                    type: \"checkbox\",\n                                    name: \"publishNow\",\n                                    checked: publishNow,\n                                    onChange: (e)=>{\n                                        setPublishNow(e.target.checked);\n                                        if (e.target.checked) {\n                                            setFieldValue(\"publishDateEN\", new Date().toISOString());\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 866,\n                                    columnNumber: 13\n                                }, this),\n                                t(\"createArticle:publishNow\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 865,\n                            columnNumber: 11\n                        }, this),\n                        !publishNow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"label-form\",\n                                        children: [\n                                            t(\"createArticle:publishDate\"),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_26__.LocalizationProvider, {\n                                                dateAdapter: _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_27__.AdapterDayjs,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_28__.DemoContainer, {\n                                                    components: [\n                                                        \"DatePicker\"\n                                                    ],\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_29__.DatePicker, {\n                                                            variant: \"standard\",\n                                                            className: \"input-date\",\n                                                            format: \"DD/MM/YYYY\",\n                                                            value: dayjs__WEBPACK_IMPORTED_MODULE_12___default()(values.publishDateEN || new Date()),\n                                                            onChange: (date)=>{\n                                                                setFieldValue(\"publishDateEN\", dayjs__WEBPACK_IMPORTED_MODULE_12___default()(date).format(\"YYYY-MM-DD\"));\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                            lineNumber: 887,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 885,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 883,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"publishDateEN\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 902,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 882,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 881,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 864,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 863,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.Field, {\n                type: \"hidden\",\n                name: \"publishDateEN\",\n                value: publishNow ? new Date().toISOString() : publishDate.toISOString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 913,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AddArticleEN, \"nDc9FkDcP0selbZlc9Kghllvj14=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__.useSaveFile\n    ];\n});\n_c = AddArticleEN;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticleEN);\nvar _c;\n$RefreshReg$(_c, \"AddArticleEN\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\n"));

/***/ })

});