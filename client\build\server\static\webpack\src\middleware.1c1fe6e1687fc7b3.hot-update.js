"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.js":
/*!***************************!*\
  !*** ./src/middleware.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-i18n-router */ \"(middleware)/./node_modules/next-i18n-router/dist/index.js\");\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_i18n_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! jose */ \"(middleware)/./node_modules/jose/dist/browser/jwt/verify.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cookie */ \"(middleware)/./node_modules/cookie/index.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../i18nConfig */ \"(middleware)/./i18nConfig.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_i18nConfig__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/constants */ \"(middleware)/./src/utils/constants.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/functions */ \"(middleware)/./src/utils/functions.js\");\n/* harmony import */ var _config_translations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./config/translations */ \"(middleware)/./src/config/translations.js\");\n/* harmony import */ var _config_allowedParams__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./config/allowedParams */ \"(middleware)/./src/config/allowedParams.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./helpers/routesList */ \"(middleware)/./src/helpers/routesList.js\");\n\n\n\n\n\n\n\n\n\n\nconst SECURITY_CONFIG = {\n    MAX_REQUESTS_PER_MINUTE: 60,\n    JWT_ALGORITHM: \"HS256\",\n    SUSPICIOUS_PATTERNS: [\n        /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n        /javascript:/gi,\n        /on\\w+\\s*=/gi,\n        /eval\\s*\\(/gi,\n        /expression\\s*\\(/gi,\n        /%3Cscript/gi,\n        /%3C%2Fscript%3E/gi\n    ]\n};\nconst rateLimitStore = new Map();\nconst logSecurityEvent = (event, details = {})=>{\n    if (true) {\n        console.warn(`[SECURITY] ${event}:`, {\n            timestamp: new Date().toISOString(),\n            ...details\n        });\n    }\n};\nconst verifyToken = async (token, clientIP = \"unknown\")=>{\n    try {\n        if (!token || typeof token !== \"string\") {\n            logSecurityEvent(\"INVALID_TOKEN_FORMAT\", {\n                clientIP,\n                reason: \"Missing or invalid token\"\n            });\n            return null;\n        }\n        const jwtRegex = /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/;\n        if (!jwtRegex.test(token)) {\n            logSecurityEvent(\"INVALID_JWT_FORMAT\", {\n                clientIP,\n                tokenPrefix: token.substring(0, 10)\n            });\n            return null;\n        }\n        const jwtSecret = process.env.NEXT_JWT_SECRET;\n        if (!jwtSecret || jwtSecret.length < 32) {\n            logSecurityEvent(\"WEAK_JWT_SECRET\", {\n                clientIP\n            });\n            throw new Error(\"JWT secret configuration error\");\n        }\n        const secret = new TextEncoder().encode(jwtSecret);\n        const { payload } = await (0,jose__WEBPACK_IMPORTED_MODULE_9__.jwtVerify)(token, secret, {\n            algorithms: [\n                SECURITY_CONFIG.JWT_ALGORITHM\n            ],\n            issuer: process.env.JWT_ISSUER || \"pentabell-api\",\n            audience: process.env.JWT_AUDIENCE || \"pentabell-client\"\n        });\n        if (!payload || !payload._id || !payload.roles || !Array.isArray(payload.roles)) {\n            logSecurityEvent(\"INVALID_TOKEN_PAYLOAD\", {\n                clientIP,\n                hasId: !!payload?._id,\n                hasRoles: !!payload?.roles\n            });\n            return null;\n        }\n        const tokenAge = Date.now() / 1000 - (payload.iat || 0);\n        if (tokenAge > 86400) {\n            logSecurityEvent(\"OLD_TOKEN_USAGE\", {\n                clientIP,\n                tokenAge,\n                userId: payload._id\n            });\n        }\n        return payload;\n    } catch (error) {\n        if (error.name === \"JWTExpired\") {\n            logSecurityEvent(\"TOKEN_EXPIRED\", {\n                clientIP\n            });\n        } else if (error.name === \"JWTInvalid\") {\n            logSecurityEvent(\"INVALID_TOKEN\", {\n                clientIP,\n                error: error.message\n            });\n        } else {\n            logSecurityEvent(\"TOKEN_VERIFICATION_ERROR\", {\n                clientIP,\n                error: error.message\n            });\n        }\n        return null;\n    }\n};\nconst checkRateLimit = (clientIP)=>{\n    const now = Date.now();\n    const windowStart = now - 60000;\n    if (!rateLimitStore.has(clientIP)) {\n        rateLimitStore.set(clientIP, []);\n    }\n    const requests = rateLimitStore.get(clientIP);\n    const validRequests = requests.filter((timestamp)=>timestamp > windowStart);\n    rateLimitStore.set(clientIP, validRequests);\n    if (validRequests.length >= SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE) {\n        return false;\n    }\n    validRequests.push(now);\n    return true;\n};\nconst sanitizeInput = (value)=>{\n    if (typeof value !== \"string\") return value;\n    let sanitized = value;\n    SECURITY_CONFIG.SUSPICIOUS_PATTERNS.forEach((pattern)=>{\n        sanitized = sanitized.replace(pattern, \"\");\n    });\n    return sanitized.trim();\n};\nconst setSecurityHeaders = (response)=>{\n    response.headers.set(\"X-Content-Type-Options\", \"nosniff\");\n    response.headers.set(\"X-Frame-Options\", \"DENY\");\n    response.headers.set(\"X-XSS-Protection\", \"1; mode=block\");\n    response.headers.set(\"Referrer-Policy\", \"strict-origin-when-cross-origin\");\n    response.headers.set(\"Permissions-Policy\", \"geolocation=(), microphone=(), camera=()\");\n    response.headers.delete(\"Server\");\n    response.headers.delete(\"X-Powered-By\");\n    return response;\n};\nasync function middleware(req) {\n    const url = req.nextUrl.clone();\n    const { defaultLocale } = (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default());\n    const { pathname } = req.nextUrl;\n    const clientIP = req.ip || req.headers.get(\"x-forwarded-for\") || req.headers.get(\"x-real-ip\") || \"unknown\";\n    let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    response = setSecurityHeaders(response);\n    if (!checkRateLimit(clientIP)) {\n        logSecurityEvent(\"RATE_LIMIT_EXCEEDED\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Too Many Requests\", {\n            status: 429,\n            headers: {\n                \"Retry-After\": \"60\",\n                \"X-RateLimit-Limit\": SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE.toString(),\n                \"X-RateLimit-Remaining\": \"0\"\n            }\n        });\n    }\n    // Sanitize query parameters\n    let hasModifiedParams = false;\n    for (const [key, value] of url.searchParams.entries()){\n        const sanitizedValue = sanitizeInput(value);\n        if (sanitizedValue !== value) {\n            url.searchParams.set(key, sanitizedValue);\n            hasModifiedParams = true;\n            logSecurityEvent(\"SUSPICIOUS_QUERY_PARAM\", {\n                clientIP,\n                key,\n                originalValue: value.substring(0, 50)\n            });\n        }\n    }\n    // Check for suspicious patterns in pathname\n    const pathString = pathname.toLowerCase();\n    const hasSuspiciousPath = SECURITY_CONFIG.SUSPICIOUS_PATTERNS.some((pattern)=>pattern.test(pathString));\n    if (hasSuspiciousPath) {\n        logSecurityEvent(\"SUSPICIOUS_PATH_ACCESS\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Forbidden\", {\n            status: 403\n        });\n    }\n    // Enhanced authentication and authorization\n    const cookies = cookie__WEBPACK_IMPORTED_MODULE_2__.parse(req.headers.get(\"cookie\") || \"\");\n    const { accessToken, refreshToken } = cookies;\n    // Check for protected routes\n    const isProtectedRoute = pathname.includes(\"dashboard\") || pathname.includes(\"backoffice\");\n    if (isProtectedRoute && !(accessToken && refreshToken)) {\n        logSecurityEvent(\"UNAUTHORIZED_ACCESS_ATTEMPT\", {\n            clientIP,\n            pathname\n        });\n        url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // Enhanced token verification with proper token selection\n    let user = null;\n    if (refreshToken) {\n        user = await verifyToken(refreshToken, clientIP);\n        // Additional security check for protected routes\n        if (isProtectedRoute && !user) {\n            logSecurityEvent(\"INVALID_TOKEN_PROTECTED_ROUTE\", {\n                clientIP,\n                pathname\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n    }\n    // Handle logout route\n    if (pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/`) {\n        logSecurityEvent(\"USER_LOGOUT\", {\n            clientIP,\n            userId: user?._id\n        });\n        return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n    }\n    // Enhanced role-based access control\n    if (user) {\n        const menuList = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.getRoutesListByRole)(user);\n        // Validate user roles\n        if (!user.roles || !Array.isArray(user.roles) || user.roles.length === 0) {\n            logSecurityEvent(\"INVALID_USER_ROLES\", {\n                clientIP,\n                userId: user._id\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n        const checkRole = !menuList?.some((item)=>pathname.includes(item)) && (pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}`) || pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}`));\n        // Enhanced role checking with security logging\n        if (checkRole || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`) {\n            let redirectPath = null;\n            if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.home.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.EDITOR)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.blogs.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.CANDIDATE)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.candidateRoutes.myApplications.route}`;\n            }\n            if (redirectPath) {\n                logSecurityEvent(\"ROLE_BASED_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles,\n                    fromPath: pathname,\n                    toPath: redirectPath\n                });\n                url.pathname = redirectPath;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            } else {\n                logSecurityEvent(\"NO_VALID_ROLE_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles\n                });\n                url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            }\n        }\n    }\n    // Enhanced parameter filtering with security logging\n    let removedParams = [];\n    for (const param of url.searchParams.keys()){\n        if (!_config_allowedParams__WEBPACK_IMPORTED_MODULE_7__.allowedParams.has(param)) {\n            url.searchParams.delete(param);\n            removedParams.push(param);\n        }\n    }\n    if (removedParams.length > 0) {\n        logSecurityEvent(\"REMOVED_DISALLOWED_PARAMS\", {\n            clientIP,\n            pathname,\n            removedParams,\n            userId: user?._id\n        });\n    }\n    // Check if parameters were modified (either sanitized or removed)\n    if (hasModifiedParams || url.searchParams.toString() !== req.nextUrl.searchParams.toString()) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // Enhanced redirection paths with security checks\n    const frPath = _config_translations__WEBPACK_IMPORTED_MODULE_6__[\"default\"][req.nextUrl.pathname];\n    if (frPath) {\n        logSecurityEvent(\"FRENCH_PATH_REDIRECT\", {\n            clientIP,\n            fromPath: req.nextUrl.pathname,\n            toPath: frPath\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(frPath, req.url));\n    }\n    // Enhanced language handling with security validation\n    if (!pathname.startsWith(\"/fr\") && !pathname.startsWith(`/${defaultLocale}`) && !pathname.startsWith(\"/_next\") && !pathname.startsWith(\"/api\") && !pathname.startsWith(\"/static\") && !pathname.includes(\".\")) {\n        // Additional security check for suspicious paths\n        if (pathname.length > 200) {\n            logSecurityEvent(\"SUSPICIOUS_LONG_PATH\", {\n                clientIP,\n                pathLength: pathname.length\n            });\n            return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Bad Request\", {\n                status: 400\n            });\n        }\n        url.pathname = `/en${pathname}`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.rewrite(url);\n    }\n    // Log successful requests for monitoring (in development only)\n    if ( true && user) {\n        logSecurityEvent(\"SUCCESSFUL_REQUEST\", {\n            clientIP,\n            pathname,\n            userId: user._id,\n            roles: user.roles\n        });\n    }\n    return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     * - files with extensions (images, fonts, etc.)\r\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|public|.*\\\\..*).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.js\n");

/***/ })

});