import { Container } from "@mui/material";
import SvgUsers from "@/assets/images/icons/yellow/Users.svg";
import Svglanguage from "@/assets/images/icons/yellow/language.svg";
import Svggdp from "@/assets/images/icons/yellow/gdp.svg";
import Svgcurrency from "@/assets/images/icons/yellow/currency.svg";
import SvgcapitalCity from "@/assets/images/icons/yellow/capitalCity.svg";
import Svggross from "@/assets/images/icons/yellow/gross.svg";

function OfficeInfoAlgeria({t}) {
  return (
    <Container  className="custom-max-width">
      <div id="office-info-tn">
        <div className="info">
          <div className="icon">
            <SvgUsers />
          </div>
          <div>
            <p className="label sub-heading text-white">{t("Algeria:officeInfoTN:title1")}</p>
            <p className="value paragraph text-white">{t("Algeria:officeInfoTN:description1")}</p>
          </div>
        </div>
        <div className="info">
          <div className="icon">
            <Svglanguage />
          </div>
          <div>
            <p className="label sub-heading text-white">{t("Algeria:officeInfoTN:title2")}</p>
            <p className="value paragraph text-white">
            {t("Algeria:officeInfoTN:description2")}
            </p>
          </div>
        </div>
        <div className="info">
          <div className="icon">
            <Svggdp />
          </div>
          <div>
            <p className="label sub-heading text-white">{t("Algeria:officeInfoTN:title3")}</p>
            <p className="value paragraph text-white">{t("Algeria:officeInfoTN:description3")}

</p>
          </div>
        </div>
        <div className="info">
          <div className="icon">
            <Svgcurrency />
          </div>
          <div>
            <p className="label sub-heading text-white">{t("Algeria:officeInfoTN:title4")}</p>
            <p className="value paragraph text-white">{t("Algeria:officeInfoTN:description4")}</p>
          </div>
        </div>
        <div className="info">
          <div className="icon">
            <SvgcapitalCity />
          </div>
          <div>
            <p className="label sub-heading text-white">{t("Algeria:officeInfoTN:title5")}</p>
            <p className="value paragraph text-white">{t("Algeria:officeInfoTN:description5")}</p>
          </div>
        </div>
        <div className="info">
          <div className="icon">
            <Svggross />
          </div>
          <div>
            <p className="label sub-heading text-white">
            {t("Algeria:officeInfoTN:title6")}
            </p>
            <p className="value paragraph text-white">{t("Algeria:officeInfoTN:description6")}</p>
          </div>
        </div>
      </div>
    </Container>
  );
}

export default OfficeInfoAlgeria;
