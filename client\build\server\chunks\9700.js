"use strict";exports.id=9700,exports.ids=[9700],exports.modules={98813:e=>{e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(i=n;0!=i--;)if(!e(t[i],r[i]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(o=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(i=n;0!=i--;)if(!Object.prototype.hasOwnProperty.call(r,o[i]))return!1;for(i=n;0!=i--;){var n,i,o,s=o[i];if(!e(t[s],r[s]))return!1}return!0}return t!=t&&r!=r}},47463:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(6005),i=r.n(n);let o={randomUUID:i().randomUUID},s=new Uint8Array(256),a=s.length,u=[];for(let e=0;e<256;++e)u.push((e+256).toString(16).slice(1));let l=function(e,t,r){if(o.randomUUID&&!t&&!e)return o.randomUUID();let n=(e=e||{}).random||(e.rng||function(){return a>s.length-16&&(i().randomFillSync(s),a=0),s.slice(a,a+=16)})();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=n[e];return t}return function(e,t=0){return(u[e[t+0]]+u[e[t+1]]+u[e[t+2]]+u[e[t+3]]+"-"+u[e[t+4]]+u[e[t+5]]+"-"+u[e[t+6]]+u[e[t+7]]+"-"+u[e[t+8]]+u[e[t+9]]+"-"+u[e[t+10]]+u[e[t+11]]+u[e[t+12]]+u[e[t+13]]+u[e[t+14]]+u[e[t+15]]).toLowerCase()}(n)}},92086:(e,t,r)=>{let n,i;r.d(t,{V:()=>nt});var o,s,a,u={};r.r(u),r.d(u,{FILE:()=>eu,HTML:()=>ed,TEXT:()=>ec,URL:()=>el});var l=r(10326);function c(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var d="function"==typeof Symbol&&Symbol.observable||"@@observable",g=function(){return Math.random().toString(36).substring(7).split("").join(".")},h={INIT:"@@redux/INIT"+g(),REPLACE:"@@redux/REPLACE"+g(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+g()}};function f(e,t,...r){if("undefined"!=typeof process&&void 0===t)throw Error("invariant requires an error message argument");if(!e){let e;if(void 0===t)e=Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let n=0;(e=Error(t.replace(/%s/g,function(){return r[n++]}))).name="Invariant Violation"}throw e.framesToPop=1,e}}function p(e){return"object"==typeof e}let v="dnd-core/INIT_COORDS",y="dnd-core/BEGIN_DRAG",m="dnd-core/PUBLISH_DRAG_SOURCE",b="dnd-core/HOVER",O="dnd-core/DROP",D="dnd-core/END_DRAG";function T(e,t){return{type:v,payload:{sourceClientOffset:t||null,clientOffset:e||null}}}let S={type:v,payload:{clientOffset:null,sourceClientOffset:null}};function I(e,t){return null===t?null===e:Array.isArray(e)?e.some(e=>e===t):e===t}class w{receiveBackend(e){this.backend=e}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){var e,t,r,n,i;let o=this,{dispatch:s}=this.store,a={beginDrag:(e=this,function(t=[],r={publishSource:!0}){let{publishSource:n=!0,clientOffset:i,getSourceClientOffset:o}=r,s=e.getMonitor(),a=e.getRegistry();e.dispatch(T(i)),f(!s.isDragging(),"Cannot call beginDrag while dragging."),t.forEach(function(e){f(a.getSource(e),"Expected sourceIds to be registered.")});let u=function(e,t){let r=null;for(let n=e.length-1;n>=0;n--)if(t.canDragSource(e[n])){r=e[n];break}return r}(t,s);if(null==u){e.dispatch(S);return}let l=null;if(i){if(!o)throw Error("getSourceClientOffset must be defined");f("function"==typeof o,"When clientOffset is provided, getSourceClientOffset must be a function."),l=o(u)}e.dispatch(T(i,l));let c=a.getSource(u).beginDrag(s,u);if(null!=c)return f(p(c),"Item must be an object."),a.pinSource(u),{type:y,payload:{itemType:a.getSourceType(u),item:c,sourceId:u,clientOffset:i||null,sourceClientOffset:l||null,isSourcePublic:!!n}}}),publishDragSource:(t=this,function(){if(t.getMonitor().isDragging())return{type:m}}),hover:(r=this,function(e,{clientOffset:t}={}){f(Array.isArray(e),"Expected targetIds to be an array.");let n=e.slice(0),i=r.getMonitor(),o=r.getRegistry();return function(e,t,r){for(let n=e.length-1;n>=0;n--){let i=e[n];I(t.getTargetType(i),r)||e.splice(n,1)}}(n,o,i.getItemType()),function(e,t,r){f(t.isDragging(),"Cannot call hover while not dragging."),f(!t.didDrop(),"Cannot call hover after drop.");for(let t=0;t<e.length;t++){let n=e[t];f(e.lastIndexOf(n)===t,"Expected targetIds to be unique in the passed array."),f(r.getTarget(n),"Expected targetIds to be registered.")}}(n,i,o),function(e,t,r){e.forEach(function(e){r.getTarget(e).hover(t,e)})}(n,i,o),{type:b,payload:{targetIds:n,clientOffset:t||null}}}),drop:(n=this,function(e={}){let t=n.getMonitor(),r=n.getRegistry();f(t.isDragging(),"Cannot call drop while not dragging."),f(!t.didDrop(),"Cannot call drop twice during one drag operation."),(function(e){let t=e.getTargetIds().filter(e.canDropOnTarget,e);return t.reverse(),t})(t).forEach((i,o)=>{let s={type:O,payload:{dropResult:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n})}return e}({},e,function(e,t,r,n){var i;let o=r.getTarget(e),s=o?o.drop(n,e):void 0;return f(void 0===(i=s)||p(i),"Drop result must either be an object or undefined."),void 0===s&&(s=0===t?{}:n.getDropResult()),s}(i,o,r,t))}};n.dispatch(s)})}),endDrag:(i=this,function(){let e=i.getMonitor(),t=i.getRegistry();f(e.isDragging(),"Cannot call endDrag while not dragging.");let r=e.getSourceId();return null!=r&&(t.getSource(r,!0).endDrag(e,r),t.unpinSource()),{type:D}})};return Object.keys(a).reduce((e,t)=>{let r=a[t];return e[t]=(...e)=>{let t=r.apply(o,e);void 0!==t&&s(t)},e},{})}dispatch(e){this.store.dispatch(e)}constructor(e,t){this.isSetUp=!1,this.handleRefCountChange=()=>{let e=this.store.getState().refCount>0;this.backend&&(e&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!e&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=e,this.monitor=t,e.subscribe(this.handleRefCountChange)}}function E(e,t){return{x:e.x-t.x,y:e.y-t.y}}let C=[],_=[];C.__IS_NONE__=!0,_.__IS_ALL__=!0;class j{subscribeToStateChange(e,t={}){let{handlerIds:r}=t;f("function"==typeof e,"listener must be a function."),f(void 0===r||Array.isArray(r),"handlerIds, when specified, must be an array of strings.");let n=this.store.getState().stateId;return this.store.subscribe(()=>{let t=this.store.getState(),i=t.stateId;try{var o,s;i!==n&&(i!==n+1||(o=t.dirtyHandlerIds,s=r,o!==C&&(o===_||void 0===s||s.filter(e=>o.indexOf(e)>-1).length>0)))&&e()}finally{n=i}})}subscribeToOffsetChange(e){f("function"==typeof e,"listener must be a function.");let t=this.store.getState().dragOffset;return this.store.subscribe(()=>{let r=this.store.getState().dragOffset;r!==t&&(t=r,e())})}canDragSource(e){if(!e)return!1;let t=this.registry.getSource(e);return f(t,`Expected to find a valid source. sourceId=${e}`),!this.isDragging()&&t.canDrag(this,e)}canDropOnTarget(e){if(!e)return!1;let t=this.registry.getTarget(e);return f(t,`Expected to find a valid target. targetId=${e}`),!(!this.isDragging()||this.didDrop())&&I(this.registry.getTargetType(e),this.getItemType())&&t.canDrop(this,e)}isDragging(){return!!this.getItemType()}isDraggingSource(e){if(!e)return!1;let t=this.registry.getSource(e,!0);return f(t,`Expected to find a valid source. sourceId=${e}`),!!(this.isDragging()&&this.isSourcePublic())&&this.registry.getSourceType(e)===this.getItemType()&&t.isDragging(this,e)}isOverTarget(e,t={shallow:!1}){if(!e)return!1;let{shallow:r}=t;if(!this.isDragging())return!1;let n=this.registry.getTargetType(e),i=this.getItemType();if(i&&!I(n,i))return!1;let o=this.getTargetIds();if(!o.length)return!1;let s=o.indexOf(e);return r?s===o.length-1:s>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return!!this.store.getState().dragOperation.isSourcePublic}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return function(e){let{clientOffset:t,initialClientOffset:r,initialSourceClientOffset:n}=e;return t&&r&&n?E({x:t.x+n.x,y:t.y+n.y},r):null}(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return function(e){let{clientOffset:t,initialClientOffset:r}=e;return t&&r?E(t,r):null}(this.store.getState().dragOffset)}constructor(e,t){this.store=e,this.registry=t}}let x="undefined"!=typeof global?global:self,P=x.MutationObserver||x.WebKitMutationObserver;function N(e){return function(){let t=setTimeout(n,0),r=setInterval(n,50);function n(){clearTimeout(t),clearInterval(r),e()}}}let R="function"==typeof P?function(e){let t=1,r=new P(e),n=document.createTextNode("");return r.observe(n,{characterData:!0}),function(){t=-t,n.data=t}}:N;class A{enqueueTask(e){let{queue:t,requestFlush:r}=this;t.length||(r(),this.flushing=!0),t[t.length]=e}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{let{queue:e}=this;for(;this.index<e.length;){let t=this.index;if(this.index++,e[t].call(),this.index>this.capacity){for(let t=0,r=e.length-this.index;t<r;t++)e[t]=e[t+this.index];e.length-=this.index,this.index=0}}e.length=0,this.index=0,this.flushing=!1},this.registerPendingError=e=>{this.pendingErrors.push(e),this.requestErrorThrow()},this.requestFlush=R(this.flush),this.requestErrorThrow=N(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class M{call(){try{this.task&&this.task()}catch(e){this.onError(e)}finally{this.task=null,this.release(this)}}constructor(e,t){this.onError=e,this.release=t,this.task=null}}class k{create(e){let t=this.freeTasks,r=t.length?t.pop():new M(this.onError,e=>t[t.length]=e);return r.task=e,r}constructor(e){this.onError=e,this.freeTasks=[]}}let L=new A,H=new k(L.registerPendingError),U="dnd-core/ADD_SOURCE",F="dnd-core/ADD_TARGET",B="dnd-core/REMOVE_SOURCE",$="dnd-core/REMOVE_TARGET";function z(e,t){if(t&&Array.isArray(e)){e.forEach(e=>z(e,!1));return}f("string"==typeof e||"symbol"==typeof e,t?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}!function(e){e.SOURCE="SOURCE",e.TARGET="TARGET"}(a||(a={}));let G=0;function q(e){switch(e[0]){case"S":return a.SOURCE;case"T":return a.TARGET;default:throw Error(`Cannot parse handler ID: ${e}`)}}function W(e,t){let r=e.entries(),n=!1;do{let{done:e,value:[,i]}=r.next();if(i===t)return!0;n=!!e}while(!n);return!1}class V{addSource(e,t){z(e),f("function"==typeof t.canDrag,"Expected canDrag to be a function."),f("function"==typeof t.beginDrag,"Expected beginDrag to be a function."),f("function"==typeof t.endDrag,"Expected endDrag to be a function.");let r=this.addHandler(a.SOURCE,e,t);return this.store.dispatch({type:U,payload:{sourceId:r}}),r}addTarget(e,t){z(e,!0),f("function"==typeof t.canDrop,"Expected canDrop to be a function."),f("function"==typeof t.hover,"Expected hover to be a function."),f("function"==typeof t.drop,"Expected beginDrag to be a function.");let r=this.addHandler(a.TARGET,e,t);return this.store.dispatch({type:F,payload:{targetId:r}}),r}containsHandler(e){return W(this.dragSources,e)||W(this.dropTargets,e)}getSource(e,t=!1){return f(this.isSourceId(e),"Expected a valid source ID."),t&&e===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(e)}getTarget(e){return f(this.isTargetId(e),"Expected a valid target ID."),this.dropTargets.get(e)}getSourceType(e){return f(this.isSourceId(e),"Expected a valid source ID."),this.types.get(e)}getTargetType(e){return f(this.isTargetId(e),"Expected a valid target ID."),this.types.get(e)}isSourceId(e){return q(e)===a.SOURCE}isTargetId(e){return q(e)===a.TARGET}removeSource(e){var t;f(this.getSource(e),"Expected an existing source."),this.store.dispatch({type:B,payload:{sourceId:e}}),t=()=>{this.dragSources.delete(e),this.types.delete(e)},L.enqueueTask(H.create(t))}removeTarget(e){f(this.getTarget(e),"Expected an existing target."),this.store.dispatch({type:$,payload:{targetId:e}}),this.dropTargets.delete(e),this.types.delete(e)}pinSource(e){let t=this.getSource(e);f(t,"Expected an existing source."),this.pinnedSourceId=e,this.pinnedSource=t}unpinSource(){f(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(e,t,r){let n=function(e){let t=(G++).toString();switch(e){case a.SOURCE:return`S${t}`;case a.TARGET:return`T${t}`;default:throw Error(`Unknown Handler Role: ${e}`)}}(e);return this.types.set(n,t),e===a.SOURCE?this.dragSources.set(n,r):e===a.TARGET&&this.dropTargets.set(n,r),n}constructor(e){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=e}}let K=(e,t)=>e===t,X={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function Y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n})}return e}let Q={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function Z(e={},t){var r;return{dirtyHandlerIds:function(e=C,t){switch(t.type){case b:break;case U:case F:case $:case B:return C;default:return _}let{targetIds:r=[],prevTargetIds:n=[]}=t.payload,i=function(e,t){let r=new Map,n=e=>{r.set(e,r.has(e)?r.get(e)+1:1)};e.forEach(n),t.forEach(n);let i=[];return r.forEach((e,t)=>{1===e&&i.push(t)}),i}(r,n);if(!(i.length>0||!function(e,t,r=K){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!r(e[n],t[n]))return!1;return!0}(r,n)))return C;let o=n[n.length-1],s=r[r.length-1];return o!==s&&(o&&i.push(o),s&&i.push(s)),i}(e.dirtyHandlerIds,{type:t.type,payload:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n})}return e}({},t.payload,{prevTargetIds:(r=[],"dragOperation.targetIds".split(".").reduce((e,t)=>e&&e[t]?e[t]:r||null,e))})}),dragOffset:function(e=X,t){let{payload:r}=t;switch(t.type){case v:case y:return{initialSourceClientOffset:r.sourceClientOffset,initialClientOffset:r.clientOffset,clientOffset:r.clientOffset};case b:var n,i;if(n=e.clientOffset,i=r.clientOffset,!n&&!i||n&&i&&n.x===i.x&&n.y===i.y)return e;return function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n})}return e}({},e,{clientOffset:r.clientOffset});case D:case O:return X;default:return e}}(e.dragOffset,t),refCount:function(e=0,t){switch(t.type){case U:case F:return e+1;case B:case $:return e-1;default:return e}}(e.refCount,t),dragOperation:function(e=Q,t){let{payload:r}=t;switch(t.type){case y:return Y({},e,{itemType:r.itemType,item:r.item,sourceId:r.sourceId,isSourcePublic:r.isSourcePublic,dropResult:null,didDrop:!1});case m:return Y({},e,{isSourcePublic:!0});case b:return Y({},e,{targetIds:r.targetIds});case $:var n,i;if(-1===e.targetIds.indexOf(r.targetId))return e;return Y({},e,{targetIds:(n=e.targetIds,i=r.targetId,n.filter(e=>e!==i))});case O:return Y({},e,{dropResult:r.dropResult,didDrop:!0,targetIds:[]});case D:return Y({},e,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return e}}(e.dragOperation,t),stateId:function(e=0){return e+1}(e.stateId)}}var J=r(17577);let ee=(0,J.createContext)({dragDropManager:void 0}),et=0,er=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var en=(0,J.memo)(function(e){var t,{children:r}=e;let[n,i]="manager"in(t=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,["children"]))?[{dragDropManager:t.manager},!1]:[function(e,t=ei(),r,n){return t[er]||(t[er]={dragDropManager:function(e,t,r={},n=!1){let i=function(e){let t="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__;return function e(t,r,n){if("function"==typeof r&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw Error(c(0));if("function"==typeof r&&void 0===n&&(n=r,r=void 0),void 0!==n){if("function"!=typeof n)throw Error(c(1));return n(e)(t,r)}if("function"!=typeof t)throw Error(c(2));var i,o=t,s=r,a=[],u=a,l=!1;function g(){u===a&&(u=a.slice())}function f(){if(l)throw Error(c(3));return s}function p(e){if("function"!=typeof e)throw Error(c(4));if(l)throw Error(c(5));var t=!0;return g(),u.push(e),function(){if(t){if(l)throw Error(c(6));t=!1,g();var r=u.indexOf(e);u.splice(r,1),a=null}}}function v(e){if(!function(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(e))throw Error(c(7));if(void 0===e.type)throw Error(c(8));if(l)throw Error(c(9));try{l=!0,s=o(s,e)}finally{l=!1}for(var t=a=u,r=0;r<t.length;r++)(0,t[r])();return e}return v({type:h.INIT}),(i={dispatch:v,subscribe:p,getState:f,replaceReducer:function(e){if("function"!=typeof e)throw Error(c(10));o=e,v({type:h.REPLACE})}})[d]=function(){var e;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw Error(c(11));function t(){e.next&&e.next(f())}return t(),{unsubscribe:p(t)}}})[d]=function(){return this},e},i}(Z,e&&t&&t({name:"dnd-core",instanceId:"dnd-core"}))}(n),o=new j(i,new V(i)),s=new w(i,o),a=e(s,t,r);return s.receiveBackend(a),s}(e,t,r,n)}),t[er]}(t.backend,t.context,t.options,t.debugMode),!t.context];return(0,J.useEffect)(()=>{if(i){let e=ei();return++et,()=>{0==--et&&(e[er]=null)}}},[]),(0,l.jsx)(ee.Provider,{value:n,children:r})});function ei(){return"undefined"!=typeof global?global:window}function eo(e){let t=null;return()=>(null==t&&(t=e()),t)}class es{enter(e){let t=this.entered.length;return this.entered=function(e,t){let r=new Set,n=e=>r.add(e);e.forEach(n),t.forEach(n);let i=[];return r.forEach(e=>i.push(e)),i}(this.entered.filter(t=>this.isNodeInDocument(t)&&(!t.contains||t.contains(e))),[e]),0===t&&this.entered.length>0}leave(e){let t=this.entered.length;return this.entered=this.entered.filter(this.isNodeInDocument).filter(t=>t!==e),t>0&&0===this.entered.length}reset(){this.entered=[]}constructor(e){this.entered=[],this.isNodeInDocument=e}}class ea{initializeExposedProperties(){Object.keys(this.config.exposeProperties).forEach(e=>{Object.defineProperty(this.item,e,{configurable:!0,enumerable:!0,get:()=>(console.warn(`Browser doesn't allow reading "${e}" until the drop event.`),null)})})}loadDataTransfer(e){if(e){let t={};Object.keys(this.config.exposeProperties).forEach(r=>{let n=this.config.exposeProperties[r];null!=n&&(t[r]={value:n(e,this.config.matchesTypes),configurable:!0,enumerable:!0})}),Object.defineProperties(this.item,t)}}canDrag(){return!0}beginDrag(){return this.item}isDragging(e,t){return t===e.getSourceId()}endDrag(){}constructor(e){this.config=e,this.item={},this.initializeExposedProperties()}}let eu="__NATIVE_FILE__",el="__NATIVE_URL__",ec="__NATIVE_TEXT__",ed="__NATIVE_HTML__";function eg(e,t,r){let n=t.reduce((t,r)=>t||e.getData(r),"");return null!=n?n:r}let eh={[eu]:{exposeProperties:{files:e=>Array.prototype.slice.call(e.files),items:e=>e.items,dataTransfer:e=>e},matchesTypes:["Files"]},[ed]:{exposeProperties:{html:(e,t)=>eg(e,t,""),dataTransfer:e=>e},matchesTypes:["Html","text/html"]},[el]:{exposeProperties:{urls:(e,t)=>eg(e,t,"").split("\n"),dataTransfer:e=>e},matchesTypes:["Url","text/uri-list"]},[ec]:{exposeProperties:{text:(e,t)=>eg(e,t,""),dataTransfer:e=>e},matchesTypes:["Text","text/plain"]}};function ef(e){if(!e)return null;let t=Array.prototype.slice.call(e.types||[]);return Object.keys(eh).filter(e=>{let r=eh[e];return null!=r&&!!r.matchesTypes&&r.matchesTypes.some(e=>t.indexOf(e)>-1)})[0]||null}let ep=eo(()=>/firefox/i.test(navigator.userAgent)),ev=eo(()=>!!window.safari);class ey{interpolate(e){let t;let{xs:r,ys:n,c1s:i,c2s:o,c3s:s}=this,a=r.length-1;if(e===r[a])return n[a];let u=0,l=s.length-1;for(;u<=l;){let i=r[t=Math.floor(.5*(u+l))];if(i<e)u=t+1;else{if(!(i>e))return n[t];l=t-1}}let c=e-r[a=Math.max(0,l)],d=c*c;return n[a]+i[a]*c+o[a]*d+s[a]*c*d}constructor(e,t){let r,n,i;let{length:o}=e,s=[];for(let e=0;e<o;e++)s.push(e);s.sort((t,r)=>e[t]<e[r]?-1:1);let a=[],u=[],l=[];for(let i=0;i<o-1;i++)r=e[i+1]-e[i],n=t[i+1]-t[i],u.push(r),a.push(n),l.push(n/r);let c=[l[0]];for(let e=0;e<u.length-1;e++){let t=l[e],n=l[e+1];if(t*n<=0)c.push(0);else{r=u[e];let i=u[e+1],o=r+i;c.push(3*o/((o+i)/t+(o+r)/n))}}c.push(l[l.length-1]);let d=[],g=[];for(let e=0;e<c.length-1;e++){i=l[e];let t=c[e],r=1/u[e],n=t+c[e+1]-i-i;d.push((i-t-n)*r),g.push(n*r*r)}this.xs=e,this.ys=t,this.c1s=c,this.c2s=d,this.c3s=g}}function em(e){let t=1===e.nodeType?e:e.parentElement;if(!t)return null;let{top:r,left:n}=t.getBoundingClientRect();return{x:n,y:r}}function eb(e){return{x:e.clientX,y:e.clientY}}class eO{get window(){return this.globalContext?this.globalContext:"undefined"!=typeof window?window:void 0}get document(){var e;return(null===(e=this.globalContext)||void 0===e?void 0:e.document)?this.globalContext.document:this.window?this.window.document:void 0}get rootElement(){var e;return(null===(e=this.optionsArgs)||void 0===e?void 0:e.rootElement)||this.window}constructor(e,t){this.ownerDocument=null,this.globalContext=e,this.optionsArgs=t}}function eD(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n})}return e}class eT{profile(){var e,t;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:(null===(e=this.dragStartSourceIds)||void 0===e?void 0:e.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:(null===(t=this.dragOverTargetIds)||void 0===t?void 0:t.length)||0}}get window(){return this.options.window}get document(){return this.options.document}get rootElement(){return this.options.rootElement}setup(){let e=this.rootElement;if(void 0!==e){if(e.__isReactDndBackendSetUp)throw Error("Cannot have two HTML5 backends at the same time.");e.__isReactDndBackendSetUp=!0,this.addEventListeners(e)}}teardown(){let e=this.rootElement;if(void 0!==e&&(e.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId)){var t;null===(t=this.window)||void 0===t||t.cancelAnimationFrame(this.asyncEndDragFrameId)}}connectDragPreview(e,t,r){return this.sourcePreviewNodeOptions.set(e,r),this.sourcePreviewNodes.set(e,t),()=>{this.sourcePreviewNodes.delete(e),this.sourcePreviewNodeOptions.delete(e)}}connectDragSource(e,t,r){this.sourceNodes.set(e,t),this.sourceNodeOptions.set(e,r);let n=t=>this.handleDragStart(t,e),i=e=>this.handleSelectStart(e);return t.setAttribute("draggable","true"),t.addEventListener("dragstart",n),t.addEventListener("selectstart",i),()=>{this.sourceNodes.delete(e),this.sourceNodeOptions.delete(e),t.removeEventListener("dragstart",n),t.removeEventListener("selectstart",i),t.setAttribute("draggable","false")}}connectDropTarget(e,t){let r=t=>this.handleDragEnter(t,e),n=t=>this.handleDragOver(t,e),i=t=>this.handleDrop(t,e);return t.addEventListener("dragenter",r),t.addEventListener("dragover",n),t.addEventListener("drop",i),()=>{t.removeEventListener("dragenter",r),t.removeEventListener("dragover",n),t.removeEventListener("drop",i)}}addEventListeners(e){e.addEventListener&&(e.addEventListener("dragstart",this.handleTopDragStart),e.addEventListener("dragstart",this.handleTopDragStartCapture,!0),e.addEventListener("dragend",this.handleTopDragEndCapture,!0),e.addEventListener("dragenter",this.handleTopDragEnter),e.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.addEventListener("dragover",this.handleTopDragOver),e.addEventListener("dragover",this.handleTopDragOverCapture,!0),e.addEventListener("drop",this.handleTopDrop),e.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(e){e.removeEventListener&&(e.removeEventListener("dragstart",this.handleTopDragStart),e.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),e.removeEventListener("dragend",this.handleTopDragEndCapture,!0),e.removeEventListener("dragenter",this.handleTopDragEnter),e.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.removeEventListener("dragover",this.handleTopDragOver),e.removeEventListener("dragover",this.handleTopDragOverCapture,!0),e.removeEventListener("drop",this.handleTopDrop),e.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){let e=this.monitor.getSourceId(),t=this.sourceNodeOptions.get(e);return eD({dropEffect:this.altKeyPressed?"copy":"move"},t||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){let e=this.monitor.getSourceId();return eD({anchorX:.5,anchorY:.5,captureDraggingState:!1},this.sourcePreviewNodeOptions.get(e)||{})}isDraggingNativeItem(){let e=this.monitor.getItemType();return Object.keys(u).some(t=>u[t]===e)}beginDragNativeItem(e,t){this.clearCurrentDragSourceNode(),this.currentNativeSource=function(e,t){let r=eh[e];if(!r)throw Error(`native type ${e} has no configuration`);let n=new ea(r);return n.loadDataTransfer(t),n}(e,t),this.currentNativeHandle=this.registry.addSource(e,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(e){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=e,this.mouseMoveTimeoutTimer=setTimeout(()=>{var e;return null===(e=this.rootElement)||void 0===e?void 0:e.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)},1e3)}clearCurrentDragSourceNode(){if(this.currentDragSourceNode){if(this.currentDragSourceNode=null,this.rootElement){var e;null===(e=this.window)||void 0===e||e.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}return this.mouseMoveTimeoutTimer=null,!0}return!1}handleDragStart(e,t){e.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(t))}handleDragEnter(e,t){this.dragEnterTargetIds.unshift(t)}handleDragOver(e,t){null===this.dragOverTargetIds&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(t)}handleDrop(e,t){this.dropTargetIds.unshift(t)}constructor(e,t,r){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.lastClientOffset=null,this.hoverRafId=null,this.getSourceClientOffset=e=>{let t=this.sourceNodes.get(e);return t&&em(t)||null},this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.currentNativeHandle&&this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=e=>!!(e&&this.document&&this.document.body&&this.document.body.contains(e)),this.endDragIfSourceWasRemovedFromDOM=()=>{let e=this.currentDragSourceNode;null==e||this.isNodeInDocument(e)||(this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover())},this.scheduleHover=e=>{null===this.hoverRafId&&"undefined"!=typeof requestAnimationFrame&&(this.hoverRafId=requestAnimationFrame(()=>{this.monitor.isDragging()&&this.actions.hover(e||[],{clientOffset:this.lastClientOffset}),this.hoverRafId=null}))},this.cancelHover=()=>{null!==this.hoverRafId&&"undefined"!=typeof cancelAnimationFrame&&(cancelAnimationFrame(this.hoverRafId),this.hoverRafId=null)},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=e=>{if(e.defaultPrevented)return;let{dragStartSourceIds:t}=this;this.dragStartSourceIds=null;let r=eb(e);this.monitor.isDragging()&&(this.actions.endDrag(),this.cancelHover()),this.actions.beginDrag(t||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:r});let{dataTransfer:n}=e,i=ef(n);if(this.monitor.isDragging()){if(n&&"function"==typeof n.setDragImage){let e=this.monitor.getSourceId(),t=this.sourceNodes.get(e),i=this.sourcePreviewNodes.get(e)||t;if(i){let{anchorX:e,anchorY:o,offsetX:s,offsetY:a}=this.getCurrentSourcePreviewNodeOptions(),u=function(e,t,r,n,i){var o;let s,a,u;let l="IMG"===t.nodeName&&(ep()||!(null===(o=document.documentElement)||void 0===o?void 0:o.contains(t))),c=em(l?e:t),d={x:r.x-c.x,y:r.y-c.y},{offsetWidth:g,offsetHeight:h}=e,{anchorX:f,anchorY:p}=n,{dragPreviewWidth:v,dragPreviewHeight:y}=(s=l?t.width:g,a=l?t.height:h,ev()&&l&&(a/=window.devicePixelRatio,s/=window.devicePixelRatio),{dragPreviewWidth:s,dragPreviewHeight:a}),{offsetX:m,offsetY:b}=i;return{x:0===m||m?m:new ey([0,.5,1],[d.x,d.x/g*v,d.x+v-g]).interpolate(f),y:0===b||b?b:(u=new ey([0,.5,1],[d.y,d.y/h*y,d.y+y-h]).interpolate(p),ev()&&l&&(u+=(window.devicePixelRatio-1)*y),u)}}(t,i,r,{anchorX:e,anchorY:o},{offsetX:s,offsetY:a});n.setDragImage(i,u.x,u.y)}}try{null==n||n.setData("application/json",{})}catch(e){}this.setCurrentDragSourceNode(e.target);let{captureDraggingState:t}=this.getCurrentSourcePreviewNodeOptions();t?this.actions.publishDragSource():setTimeout(()=>this.actions.publishDragSource(),0)}else if(i)this.beginDragNativeItem(i);else{if(n&&!n.types&&(e.target&&!e.target.hasAttribute||!e.target.hasAttribute("draggable")))return;e.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleTopDragEnterCapture=e=>{if(this.dragEnterTargetIds=[],this.isDraggingNativeItem()){var t;null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer)}if(!this.enterLeaveCounter.enter(e.target)||this.monitor.isDragging())return;let{dataTransfer:r}=e,n=ef(r);n&&this.beginDragNativeItem(n,r)},this.handleTopDragEnter=e=>{let{dragEnterTargetIds:t}=this;this.dragEnterTargetIds=[],this.monitor.isDragging()&&(this.altKeyPressed=e.altKey,t.length>0&&this.actions.hover(t,{clientOffset:eb(e)}),t.some(e=>this.monitor.canDropOnTarget(e))&&(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect=this.getCurrentDropEffect())))},this.handleTopDragOverCapture=e=>{if(this.dragOverTargetIds=[],this.isDraggingNativeItem()){var t;null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer)}},this.handleTopDragOver=e=>{let{dragOverTargetIds:t}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging()){e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="none");return}this.altKeyPressed=e.altKey,this.lastClientOffset=eb(e),this.scheduleHover(t),(t||[]).some(e=>this.monitor.canDropOnTarget(e))?(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?e.preventDefault():(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=e=>{this.isDraggingNativeItem()&&e.preventDefault(),this.enterLeaveCounter.leave(e.target)&&(this.isDraggingNativeItem()&&setTimeout(()=>this.endDragNativeItem(),0),this.cancelHover())},this.handleTopDropCapture=e=>{if(this.dropTargetIds=[],this.isDraggingNativeItem()){var t;e.preventDefault(),null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer)}else ef(e.dataTransfer)&&e.preventDefault();this.enterLeaveCounter.reset()},this.handleTopDrop=e=>{let{dropTargetIds:t}=this;this.dropTargetIds=[],this.actions.hover(t,{clientOffset:eb(e)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleSelectStart=e=>{let t=e.target;"function"!=typeof t.dragDrop||"INPUT"===t.tagName||"SELECT"===t.tagName||"TEXTAREA"===t.tagName||t.isContentEditable||(e.preventDefault(),t.dragDrop())},this.options=new eO(t,r),this.actions=e.getActions(),this.monitor=e.getMonitor(),this.registry=e.getRegistry(),this.enterLeaveCounter=new es(this.isNodeInDocument)}}let eS=function(e,t,r){return new eT(e,t,r)};var eI=r(98813);let ew="undefined"!=typeof window?J.useLayoutEffect:J.useEffect;function eE(e,t,r){return function(e,t,r){let[n,i]=function(e,t,r){let[n,i]=(0,J.useState)(()=>t(e)),o=(0,J.useCallback)(()=>{let o=t(e);!eI(n,o)&&(i(o),r&&r())},[n,e,r]);return ew(o),[n,o]}(e,t,r);return ew(function(){let t=e.getHandlerId();if(null!=t)return e.subscribeToStateChange(i,{handlerIds:[t]})},[e,i]),n}(t,e||(()=>({})),()=>r.reconnect())}function eC(e,t){let r=[...t||[]];return null==t&&"function"!=typeof e&&r.push(e),(0,J.useMemo)(()=>"function"==typeof e?e():e,r)}function e_(e,t,r,n){let i=r?r.call(n,e,t):void 0;if(void 0!==i)return!!i;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;let o=Object.keys(e),s=Object.keys(t);if(o.length!==s.length)return!1;let a=Object.prototype.hasOwnProperty.bind(t);for(let s=0;s<o.length;s++){let u=o[s];if(!a(u))return!1;let l=e[u],c=t[u];if(!1===(i=r?r.call(n,l,c,u):void 0)||void 0===i&&l!==c)return!1}return!0}function ej(e){return null!==e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function ex(e){let t={};return Object.keys(e).forEach(r=>{let n=e[r];if(r.endsWith("Ref"))t[r]=e[r];else{let e=(e=null,t=null)=>(0,J.isValidElement)(e)?(function(e){if("string"==typeof e.type)return;let t=e.type.displayName||e.type.name||"the component";throw Error(`Only native element nodes can now be passed to React DnD connectors.You can either wrap ${t} into a <div>, or turn it into a drag source or a drop target itself.`)}(e),function(e,t){let r=e.ref;return(f("string"!=typeof r,"Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),r)?(0,J.cloneElement)(e,{ref:e=>{eP(r,e),eP(t,e)}}):(0,J.cloneElement)(e,{ref:t})}(e,t?e=>n(e,t):n)):(n(e,t),e);t[r]=()=>e}}),t}function eP(e,t){"function"==typeof e?e(t):e.current=t}class eN{receiveHandlerId(e){this.handlerId!==e&&(this.handlerId=e,this.reconnect())}get connectTarget(){return this.dragSource}get dragSourceOptions(){return this.dragSourceOptionsInternal}set dragSourceOptions(e){this.dragSourceOptionsInternal=e}get dragPreviewOptions(){return this.dragPreviewOptionsInternal}set dragPreviewOptions(e){this.dragPreviewOptionsInternal=e}reconnect(){let e=this.reconnectDragSource();this.reconnectDragPreview(e)}reconnectDragSource(){let e=this.dragSource,t=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();return t&&this.disconnectDragSource(),this.handlerId&&(e?t&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=e,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,e,this.dragSourceOptions)):this.lastConnectedDragSource=e),t}reconnectDragPreview(e=!1){let t=this.dragPreview,r=e||this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();if(r&&this.disconnectDragPreview(),this.handlerId){if(!t){this.lastConnectedDragPreview=t;return}r&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=t,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,t,this.dragPreviewOptions))}}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didConnectedDragSourceChange(){return this.lastConnectedDragSource!==this.dragSource}didConnectedDragPreviewChange(){return this.lastConnectedDragPreview!==this.dragPreview}didDragSourceOptionsChange(){return!e_(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}didDragPreviewOptionsChange(){return!e_(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}disconnectDragSource(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}disconnectDragPreview(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}get dragSource(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}get dragPreview(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}clearDragSource(){this.dragSourceNode=null,this.dragSourceRef=null}clearDragPreview(){this.dragPreviewNode=null,this.dragPreviewRef=null}constructor(e){this.hooks=ex({dragSource:(e,t)=>{this.clearDragSource(),this.dragSourceOptions=t||null,ej(e)?this.dragSourceRef=e:this.dragSourceNode=e,this.reconnectDragSource()},dragPreview:(e,t)=>{this.clearDragPreview(),this.dragPreviewOptions=t||null,ej(e)?this.dragPreviewRef=e:this.dragPreviewNode=e,this.reconnectDragPreview()}}),this.handlerId=null,this.dragSourceRef=null,this.dragSourceOptionsInternal=null,this.dragPreviewRef=null,this.dragPreviewOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDragSource=null,this.lastConnectedDragSourceOptions=null,this.lastConnectedDragPreview=null,this.lastConnectedDragPreviewOptions=null,this.backend=e}}function eR(){let{dragDropManager:e}=(0,J.useContext)(ee);return f(null!=e,"Expected drag drop context"),e}let eA=!1,eM=!1;class ek{receiveHandlerId(e){this.sourceId=e}getHandlerId(){return this.sourceId}canDrag(){f(!eA,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return eA=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{eA=!1}}isDragging(){if(!this.sourceId)return!1;f(!eM,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return eM=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{eM=!1}}subscribeToStateChange(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}isDraggingSource(e){return this.internalMonitor.isDraggingSource(e)}isOverTarget(e,t){return this.internalMonitor.isOverTarget(e,t)}getTargetIds(){return this.internalMonitor.getTargetIds()}isSourcePublic(){return this.internalMonitor.isSourcePublic()}getSourceId(){return this.internalMonitor.getSourceId()}subscribeToOffsetChange(e){return this.internalMonitor.subscribeToOffsetChange(e)}canDragSource(e){return this.internalMonitor.canDragSource(e)}canDropOnTarget(e){return this.internalMonitor.canDropOnTarget(e)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.sourceId=null,this.internalMonitor=e.getMonitor()}}class eL{beginDrag(){let e=this.spec,t=this.monitor,r=null;return null!=(r="object"==typeof e.item?e.item:"function"==typeof e.item?e.item(t):{})?r:null}canDrag(){let e=this.spec,t=this.monitor;return"boolean"==typeof e.canDrag?e.canDrag:"function"!=typeof e.canDrag||e.canDrag(t)}isDragging(e,t){let r=this.spec,n=this.monitor,{isDragging:i}=r;return i?i(n):t===e.getSourceId()}endDrag(){let e=this.spec,t=this.monitor,r=this.connector,{end:n}=e;n&&n(t.getItem(),t),r.reconnect()}constructor(e,t,r){this.spec=e,this.monitor=t,this.connector=r}}class eH{get connectTarget(){return this.dropTarget}reconnect(){let e=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();e&&this.disconnectDropTarget();let t=this.dropTarget;if(this.handlerId){if(!t){this.lastConnectedDropTarget=t;return}e&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=t,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,t,this.dropTargetOptions))}}receiveHandlerId(e){e!==this.handlerId&&(this.handlerId=e,this.reconnect())}get dropTargetOptions(){return this.dropTargetOptionsInternal}set dropTargetOptions(e){this.dropTargetOptionsInternal=e}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didDropTargetChange(){return this.lastConnectedDropTarget!==this.dropTarget}didOptionsChange(){return!e_(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}disconnectDropTarget(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}get dropTarget(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}clearDropTarget(){this.dropTargetRef=null,this.dropTargetNode=null}constructor(e){this.hooks=ex({dropTarget:(e,t)=>{this.clearDropTarget(),this.dropTargetOptions=t,ej(e)?this.dropTargetRef=e:this.dropTargetNode=e,this.reconnect()}}),this.handlerId=null,this.dropTargetRef=null,this.dropTargetOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDropTarget=null,this.lastConnectedDropTargetOptions=null,this.backend=e}}let eU=!1;class eF{receiveHandlerId(e){this.targetId=e}getHandlerId(){return this.targetId}subscribeToStateChange(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}canDrop(){if(!this.targetId)return!1;f(!eU,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return eU=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{eU=!1}}isOver(e){return!!this.targetId&&this.internalMonitor.isOverTarget(this.targetId,e)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.targetId=null,this.internalMonitor=e.getMonitor()}}class eB{canDrop(){let e=this.spec,t=this.monitor;return!e.canDrop||e.canDrop(t.getItem(),t)}hover(){let e=this.spec,t=this.monitor;e.hover&&e.hover(t.getItem(),t)}drop(){let e=this.spec,t=this.monitor;if(e.drop)return e.drop(t.getItem(),t)}constructor(e,t){this.spec=e,this.monitor=t}}var e$=Object.create,ez=Object.defineProperty,eG=Object.getOwnPropertyDescriptor,eq=Object.getOwnPropertyNames,eW=Object.getPrototypeOf,eV=Object.prototype.hasOwnProperty,eK=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of eq(t))eV.call(e,i)||i===r||ez(e,i,{get:()=>t[i],enumerable:!(n=eG(t,i))||n.enumerable});return e},eX=(e,t,r)=>(r=null!=e?e$(eW(e)):{},eK(!t&&e&&e.__esModule?r:ez(r,"default",{value:e,enumerable:!0}),e)),eY=(n={"node_modules/classnames/index.js"(e,t){!function(){var e={}.hasOwnProperty;function r(){for(var t=[],n=0;n<arguments.length;n++){var i=arguments[n];if(i){var o=typeof i;if("string"===o||"number"===o)t.push(i);else if(Array.isArray(i)){if(i.length){var s=r.apply(null,i);s&&t.push(s)}}else if("object"===o){if(i.toString!==Object.prototype.toString&&!i.toString.toString().includes("[native code]")){t.push(i.toString());continue}for(var a in i)e.call(i,a)&&i[a]&&t.push(a)}}}return t.join(" ")}void 0!==t&&t.exports?(r.default=r,t.exports=r):"function"==typeof define&&"object"==typeof define.amd&&define.amd?define("classnames",[],function(){return r}):window.classNames=r}()}},function(){return i||(0,n[eq(n)[0]])((i={exports:{}}).exports,i),i.exports}),eQ=eX(eY(),1),eZ="object"==typeof global&&global&&global.Object===Object&&global,eJ="object"==typeof self&&self&&self.Object===Object&&self,e0=eZ||eJ||Function("return this")(),e1=e0.Symbol,e2=Object.prototype,e3=e2.hasOwnProperty,e4=e2.toString,e5=e1?e1.toStringTag:void 0,e6=function(e){var t=e3.call(e,e5),r=e[e5];try{e[e5]=void 0;var n=!0}catch(e){}var i=e4.call(e);return n&&(t?e[e5]=r:delete e[e5]),i},e7=Object.prototype.toString,e8=e1?e1.toStringTag:void 0,e9=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":e8&&e8 in Object(e)?e6(e):e7.call(e)},te=function(e){return null!=e&&"object"==typeof e},tt=function(e,t){for(var r=-1,n=null==e?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i},tr=Array.isArray,tn=1/0,ti=e1?e1.prototype:void 0,to=ti?ti.toString:void 0,ts=function e(t){if("string"==typeof t)return t;if(tr(t))return tt(t,e)+"";if("symbol"==typeof t||te(t)&&"[object Symbol]"==e9(t))return to?to.call(t):"";var r=t+"";return"0"==r&&1/t==-tn?"-0":r},ta=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},tu=function(e){if(!ta(e))return!1;var t=e9(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},tl=e0["__core-js_shared__"],tc=function(){var e=/[^.]+$/.exec(tl&&tl.keys&&tl.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),td=Function.prototype.toString,tg=function(e){if(null!=e){try{return td.call(e)}catch(e){}try{return e+""}catch(e){}}return""},th=/^\[object .+?Constructor\]$/,tf=Object.prototype,tp=Function.prototype.toString,tv=tf.hasOwnProperty,ty=RegExp("^"+tp.call(tv).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),tm=function(e,t){var r,n=null==e?void 0:e[t];return ta(r=n)&&(!tc||!(tc in r))&&(tu(r)?ty:th).test(tg(r))?n:void 0},tb=tm(e0,"WeakMap"),tO=function(e,t,r,n){for(var i=e.length,o=r+(n?1:-1);n?o--:++o<i;)if(t(e[o],o,e))return o;return -1},tD=function(e){return e!=e},tT=function(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return -1},tS=function(e,t){return!!(null==e?0:e.length)&&(t==t?tT(e,t,0):tO(e,tD,0))>-1},tI=/^(?:0|[1-9]\d*)$/,tw=function(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&tI.test(e))&&e>-1&&e%1==0&&e<t},tE=function(e,t){return e===t||e!=e&&t!=t},tC=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991},t_=Object.prototype,tj=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||t_)},tx=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n},tP=function(e){return te(e)&&"[object Arguments]"==e9(e)},tN=Object.prototype,tR=tN.hasOwnProperty,tA=tN.propertyIsEnumerable,tM=tP(function(){return arguments}())?tP:function(e){return te(e)&&tR.call(e,"callee")&&!tA.call(e,"callee")},tk="object"==typeof exports&&exports&&!exports.nodeType&&exports,tL=tk&&"object"==typeof module&&module&&!module.nodeType&&module,tH=tL&&tL.exports===tk?e0.Buffer:void 0,tU=(tH?tH.isBuffer:void 0)||function(){return!1},tF={};tF["[object Float32Array]"]=tF["[object Float64Array]"]=tF["[object Int8Array]"]=tF["[object Int16Array]"]=tF["[object Int32Array]"]=tF["[object Uint8Array]"]=tF["[object Uint8ClampedArray]"]=tF["[object Uint16Array]"]=tF["[object Uint32Array]"]=!0,tF["[object Arguments]"]=tF["[object Array]"]=tF["[object ArrayBuffer]"]=tF["[object Boolean]"]=tF["[object DataView]"]=tF["[object Date]"]=tF["[object Error]"]=tF["[object Function]"]=tF["[object Map]"]=tF["[object Number]"]=tF["[object Object]"]=tF["[object RegExp]"]=tF["[object Set]"]=tF["[object String]"]=tF["[object WeakMap]"]=!1;var tB="object"==typeof exports&&exports&&!exports.nodeType&&exports,t$=tB&&"object"==typeof module&&module&&!module.nodeType&&module,tz=t$&&t$.exports===tB&&eZ.process,tG=function(){try{var e=t$&&t$.require&&t$.require("util").types;if(e)return e;return tz&&tz.binding&&tz.binding("util")}catch(e){}}(),tq=tG&&tG.isTypedArray,tW=tq?function(e){return tq(e)}:function(e){return te(e)&&tC(e.length)&&!!tF[e9(e)]},tV=Object.prototype.hasOwnProperty,tK=function(e,t){var r=tr(e),n=!r&&tM(e),i=!r&&!n&&tU(e),o=!r&&!n&&!i&&tW(e),s=r||n||i||o,a=s?tx(e.length,String):[],u=a.length;for(var l in e)(t||tV.call(e,l))&&!(s&&("length"==l||i&&("offset"==l||"parent"==l)||o&&("buffer"==l||"byteLength"==l||"byteOffset"==l)||tw(l,u)))&&a.push(l);return a},tX=(o=Object.keys,s=Object,function(e){return o(s(e))}),tY=Object.prototype.hasOwnProperty,tQ=function(e){if(!tj(e))return tX(e);var t=[];for(var r in Object(e))tY.call(e,r)&&"constructor"!=r&&t.push(r);return t},tZ=function(e){return null!=e&&tC(e.length)&&!tu(e)?tK(e):tQ(e)},tJ=tm(Object,"create"),t0=Object.prototype.hasOwnProperty,t1=Object.prototype.hasOwnProperty;function t2(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}t2.prototype.clear=function(){this.__data__=tJ?tJ(null):{},this.size=0},t2.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},t2.prototype.get=function(e){var t=this.__data__;if(tJ){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return t0.call(t,e)?t[e]:void 0},t2.prototype.has=function(e){var t=this.__data__;return tJ?void 0!==t[e]:t1.call(t,e)},t2.prototype.set=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=tJ&&void 0===t?"__lodash_hash_undefined__":t,this};var t3=function(e,t){for(var r=e.length;r--;)if(tE(e[r][0],t))return r;return -1},t4=Array.prototype.splice;function t5(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}t5.prototype.clear=function(){this.__data__=[],this.size=0},t5.prototype.delete=function(e){var t=this.__data__,r=t3(t,e);return!(r<0)&&(r==t.length-1?t.pop():t4.call(t,r,1),--this.size,!0)},t5.prototype.get=function(e){var t=this.__data__,r=t3(t,e);return r<0?void 0:t[r][1]},t5.prototype.has=function(e){return t3(this.__data__,e)>-1},t5.prototype.set=function(e,t){var r=this.__data__,n=t3(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this};var t6=tm(e0,"Map"),t7=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e},t8=function(e,t){var r=e.__data__;return t7(t)?r["string"==typeof t?"string":"hash"]:r.map};function t9(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}t9.prototype.clear=function(){this.size=0,this.__data__={hash:new t2,map:new(t6||t5),string:new t2}},t9.prototype.delete=function(e){var t=t8(this,e).delete(e);return this.size-=t?1:0,t},t9.prototype.get=function(e){return t8(this,e).get(e)},t9.prototype.has=function(e){return t8(this,e).has(e)},t9.prototype.set=function(e,t){var r=t8(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this};var re=function(e){return null==e?"":ts(e)},rt=function(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e};function rr(e){var t=this.__data__=new t5(e);this.size=t.size}rr.prototype.clear=function(){this.__data__=new t5,this.size=0},rr.prototype.delete=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r},rr.prototype.get=function(e){return this.__data__.get(e)},rr.prototype.has=function(e){return this.__data__.has(e)},rr.prototype.set=function(e,t){var r=this.__data__;if(r instanceof t5){var n=r.__data__;if(!t6||n.length<199)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new t9(n)}return r.set(e,t),this.size=r.size,this};var rn=function(e,t){for(var r=-1,n=null==e?0:e.length,i=0,o=[];++r<n;){var s=e[r];t(s,r,e)&&(o[i++]=s)}return o},ri=Object.prototype.propertyIsEnumerable,ro=Object.getOwnPropertySymbols,rs=ro?function(e){return null==e?[]:rn(ro(e=Object(e)),function(t){return ri.call(e,t)})}:function(){return[]},ra=function(e,t,r){var n=t(e);return tr(e)?n:rt(n,r(e))},ru=function(e){return ra(e,tZ,rs)},rl=tm(e0,"DataView"),rc=tm(e0,"Promise"),rd=tm(e0,"Set"),rg="[object Map]",rh="[object Promise]",rf="[object Set]",rp="[object WeakMap]",rv="[object DataView]",ry=tg(rl),rm=tg(t6),rb=tg(rc),rO=tg(rd),rD=tg(tb),rT=e9;(rl&&rT(new rl(new ArrayBuffer(1)))!=rv||t6&&rT(new t6)!=rg||rc&&rT(rc.resolve())!=rh||rd&&rT(new rd)!=rf||tb&&rT(new tb)!=rp)&&(rT=function(e){var t=e9(e),r="[object Object]"==t?e.constructor:void 0,n=r?tg(r):"";if(n)switch(n){case ry:return rv;case rm:return rg;case rb:return rh;case rO:return rf;case rD:return rp}return t});var rS=rT,rI=e0.Uint8Array;function rw(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new t9;++t<r;)this.add(e[t])}rw.prototype.add=rw.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},rw.prototype.has=function(e){return this.__data__.has(e)};var rE=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1},rC=function(e,t){return e.has(t)},r_=function(e,t,r,n,i,o){var s=1&r,a=e.length,u=t.length;if(a!=u&&!(s&&u>a))return!1;var l=o.get(e),c=o.get(t);if(l&&c)return l==t&&c==e;var d=-1,g=!0,h=2&r?new rw:void 0;for(o.set(e,t),o.set(t,e);++d<a;){var f=e[d],p=t[d];if(n)var v=s?n(p,f,d,t,e,o):n(f,p,d,e,t,o);if(void 0!==v){if(v)continue;g=!1;break}if(h){if(!rE(t,function(e,t){if(!rC(h,t)&&(f===e||i(f,e,r,n,o)))return h.push(t)})){g=!1;break}}else if(!(f===p||i(f,p,r,n,o))){g=!1;break}}return o.delete(e),o.delete(t),g},rj=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e,n){r[++t]=[n,e]}),r},rx=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e){r[++t]=e}),r},rP=e1?e1.prototype:void 0,rN=rP?rP.valueOf:void 0,rR=function(e,t,r,n,i,o,s){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":if(e.byteLength!=t.byteLength||!o(new rI(e),new rI(t)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return tE(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var a=rj;case"[object Set]":var u=1&n;if(a||(a=rx),e.size!=t.size&&!u)break;var l=s.get(e);if(l)return l==t;n|=2,s.set(e,t);var c=r_(a(e),a(t),n,i,o,s);return s.delete(e),c;case"[object Symbol]":if(rN)return rN.call(e)==rN.call(t)}return!1},rA=Object.prototype.hasOwnProperty,rM=function(e,t,r,n,i,o){var s=1&r,a=ru(e),u=a.length;if(u!=ru(t).length&&!s)return!1;for(var l=u;l--;){var c=a[l];if(!(s?c in t:rA.call(t,c)))return!1}var d=o.get(e),g=o.get(t);if(d&&g)return d==t&&g==e;var h=!0;o.set(e,t),o.set(t,e);for(var f=s;++l<u;){var p=e[c=a[l]],v=t[c];if(n)var y=s?n(v,p,c,t,e,o):n(p,v,c,e,t,o);if(!(void 0===y?p===v||i(p,v,r,n,o):y)){h=!1;break}f||(f="constructor"==c)}if(h&&!f){var m=e.constructor,b=t.constructor;m!=b&&"constructor"in e&&"constructor"in t&&!("function"==typeof m&&m instanceof m&&"function"==typeof b&&b instanceof b)&&(h=!1)}return o.delete(e),o.delete(t),h},rk="[object Arguments]",rL="[object Array]",rH="[object Object]",rU=Object.prototype.hasOwnProperty,rF=function(e,t,r,n,i,o){var s=tr(e),a=tr(t),u=s?rL:rS(e),l=a?rL:rS(t);u=u==rk?rH:u,l=l==rk?rH:l;var c=u==rH,d=l==rH,g=u==l;if(g&&tU(e)){if(!tU(t))return!1;s=!0,c=!1}if(g&&!c)return o||(o=new rr),s||tW(e)?r_(e,t,r,n,i,o):rR(e,t,u,r,n,i,o);if(!(1&r)){var h=c&&rU.call(e,"__wrapped__"),f=d&&rU.call(t,"__wrapped__");if(h||f){var p=h?e.value():e,v=f?t.value():t;return o||(o=new rr),i(p,v,r,n,o)}}return!!g&&(o||(o=new rr),rM(e,t,r,n,i,o))},rB=function(e,t,r){for(var n=-1,i=null==e?0:e.length;++n<i;)if(r(t,e[n]))return!0;return!1},r$=function(e){return function(t){return null==e?void 0:e[t]}}({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),rz=/[&<>"']/g,rG=RegExp(rz.source),rq=/[\\^$.*+?()[\]{}|]/g,rW=RegExp(rq.source),rV=rd&&1/rx(new rd([,-0]))[1]==1/0?function(e){return new rd(e)}:function(){},rK=function(e,t,r){var n=-1,i=tS,o=e.length,s=!0,a=[],u=a;if(r)s=!1,i=rB;else if(o>=200){var l=t?null:rV(e);if(l)return rx(l);s=!1,i=rC,u=new rw}else u=t?[]:a;e:for(;++n<o;){var c=e[n],d=t?t(c):c;if(c=r||0!==c?c:0,s&&d==d){for(var g=u.length;g--;)if(u[g]===d)continue e;t&&u.push(d),a.push(c)}else i(u,d,r)||(u!==a&&u.push(d),a.push(c))}return a},rX={ENTER:[10,13],TAB:9,BACKSPACE:8,UP_ARROW:38,DOWN_ARROW:40,SPACE:32},rY={ENTER:"Enter",TAB:"Tab",COMMA:",",SPACE:" ",SEMICOLON:";"},rQ={tags:"ReactTags__tags",tagInput:"ReactTags__tagInput",tagInputField:"ReactTags__tagInputField",selected:"ReactTags__selected",tag:"ReactTags__tag",remove:"ReactTags__remove",suggestions:"ReactTags__suggestions",activeSuggestion:"ReactTags__activeSuggestion",editTagInput:"ReactTags__editTagInput",editTagInputField:"ReactTags__editTagInputField",clearAll:"ReactTags__clearAll"},rZ={INLINE:"inline",TOP:"top",BOTTOM:"bottom"},rJ={TAG_LIMIT:"Tag limit reached!"};function r0(e){let{moveTag:t,readOnly:r,allowDragDrop:n}=e;return void 0!==t&&!r&&n}var r1=e=>{let{readOnly:t,removeComponent:r,onRemove:n,className:i,tag:o,index:s}=e,a=e=>{if(rX.ENTER.includes(e.keyCode)||e.keyCode===rX.SPACE){e.preventDefault(),e.stopPropagation();return}e.keyCode===rX.BACKSPACE&&n(e)};if(t)return(0,l.jsx)("span",{});let u=`Tag at index ${s} with value ${o.id} focussed. Press backspace to remove`;return r?(0,l.jsx)(r,{"data-testid":"remove",onRemove:n,onKeyDown:a,className:i,"aria-label":u,tag:o,index:s}):(0,l.jsx)("button",{"data-testid":"remove",onClick:n,onKeyDown:a,className:i,type:"button","aria-label":u,children:(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",height:"12",width:"12",fill:"#fff",children:(0,l.jsx)("path",{d:"M376.6 84.5c11.3-13.6 9.5-33.8-4.1-45.1s-33.8-9.5-45.1 4.1L192 206 56.6 43.5C45.3 29.9 25.1 28.1 11.5 39.4S-3.9 70.9 7.4 84.5L150.3 256 7.4 427.5c-11.3 13.6-9.5 33.8 4.1 45.1s33.8 9.5 45.1-4.1L192 306 327.4 468.5c11.3 13.6 31.5 15.4 45.1 4.1s15.4-31.5 4.1-45.1L233.7 256 376.6 84.5z"})})})},r2={TAG:"tag"},r3=e=>{let t=(0,J.useRef)(null),{readOnly:r=!1,tag:n,classNames:i,index:o,moveTag:s,allowDragDrop:a=!0,labelField:u="text",tags:c}=e,[{isDragging:d},g]=function(e,t){let r=eC(e,t);f(!r.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");let n=function(){let e=eR();return(0,J.useMemo)(()=>new ek(e),[e])}(),i=function(e,t){let r=eR(),n=(0,J.useMemo)(()=>new eN(r.getBackend()),[r]);return ew(()=>(n.dragSourceOptions=e||null,n.reconnect(),()=>n.disconnectDragSource()),[n,e]),ew(()=>(n.dragPreviewOptions=t||null,n.reconnect(),()=>n.disconnectDragPreview()),[n,t]),n}(r.options,r.previewOptions);return function(e,t,r){let n=eR(),i=function(e,t,r){let n=(0,J.useMemo)(()=>new eL(e,t,r),[t,r]);return(0,J.useEffect)(()=>{n.spec=e},[e]),n}(e,t,r),o=(0,J.useMemo)(()=>{let t=e.type;return f(null!=t,"spec.type must be defined"),t},[e]);ew(function(){if(null!=o){let[e,s]=function(e,t,r){let n=r.getRegistry(),i=n.addSource(e,t);return[i,()=>n.removeSource(i)]}(o,i,n);return t.receiveHandlerId(e),r.receiveHandlerId(e),s}},[n,t,r,i,o])}(r,n,i),[eE(r.collect,n,i),(0,J.useMemo)(()=>i.hooks.dragSource(),[i]),(0,J.useMemo)(()=>i.hooks.dragPreview(),[i])]}(()=>({type:r2.TAG,collect:e=>({isDragging:!!e.isDragging()}),item:e,canDrag:()=>r0({moveTag:s,readOnly:r,allowDragDrop:a})}),[c]),[,h]=function(e,t){let r=eC(e,t),n=function(){let e=eR();return(0,J.useMemo)(()=>new eF(e),[e])}(),i=function(e){let t=eR(),r=(0,J.useMemo)(()=>new eH(t.getBackend()),[t]);return ew(()=>(r.dropTargetOptions=e||null,r.reconnect(),()=>r.disconnectDropTarget()),[e]),r}(r.options);return function(e,t,r){let n=eR(),i=function(e,t){let r=(0,J.useMemo)(()=>new eB(e,t),[t]);return(0,J.useEffect)(()=>{r.spec=e},[e]),r}(e,t),o=function(e){let{accept:t}=e;return(0,J.useMemo)(()=>(f(null!=e.accept,"accept must be defined"),Array.isArray(t)?t:[t]),[t])}(e);ew(function(){let[e,s]=function(e,t,r){let n=r.getRegistry(),i=n.addTarget(e,t);return[i,()=>n.removeTarget(i)]}(o,i,n);return t.receiveHandlerId(e),r.receiveHandlerId(e),s},[n,t,i,r,o.map(e=>e.toString()).join("|")])}(r,n,i),[eE(r.collect,n,i),(0,J.useMemo)(()=>i.hooks.dropTarget(),[i])]}(()=>({accept:r2.TAG,drop:t=>{let r=t.index;r!==o&&e?.moveTag?.(r,o)},canDrop:e=>(function(e){let{readOnly:t,allowDragDrop:r}=e;return!t&&r})(e)}),[c]);g(h(t));let p=e.tag[u],{className:v=""}=n;return(0,l.jsxs)("span",{ref:t,className:(0,eQ.default)("tag-wrapper",i.tag,v),style:{opacity:d?0:1,cursor:r0({moveTag:s,readOnly:r,allowDragDrop:a})?"move":"auto"},"data-testid":"tag",onClick:e.onTagClicked,onTouchStart:e.onTagClicked,children:[p,(0,l.jsx)(r1,{tag:e.tag,className:i.remove,removeComponent:e.removeComponent,onRemove:e.onDelete,readOnly:r,index:o})]})},r4=e=>(0,l.jsx)("button",{"aria-label":e["aria-label"],className:e.classNames.clearAll,onClick:e.onClick,children:"Clear all"}),r5=(e,t)=>{let r=t.offsetHeight,n=e.offsetHeight,i=e.offsetTop-t.scrollTop;i+n>=r?t.scrollTop+=i-r+n:i<0&&(t.scrollTop+=i)},r6=(e,t,r,n)=>"function"==typeof n?n(e):e.length>=t&&r,r7=(0,J.memo)(e=>{let t=(0,J.createRef)(),{labelField:r,minQueryLength:n,isFocused:i,classNames:o,selectedIndex:s,query:a}=e;(0,J.useEffect)(()=>{if(!t.current)return;let e=t.current.querySelector(`.${o.activeSuggestion}`);e&&r5(e,t.current)},[s]);let u=(e,t)=>{let n=t.trim().replace(/[-\\^$*+?.()|[\]{}]/g,"\\$&"),{[r]:i}=e;return{__html:i.replace(RegExp(n,"gi"),e=>`<mark>${function(e){return(e=re(e))&&rG.test(e)?e.replace(rz,r$):e}(e)}</mark>`)}},c=(t,r)=>"function"==typeof e.renderSuggestion?e.renderSuggestion(t,r):(0,l.jsx)("span",{dangerouslySetInnerHTML:u(t,r)}),d=e.suggestions.map((t,r)=>(0,l.jsx)("li",{onMouseDown:e.handleClick.bind(null,r),onTouchStart:e.handleClick.bind(null,r),onMouseOver:e.handleHover.bind(null,r),className:r===e.selectedIndex?e.classNames.activeSuggestion:"",children:c(t,e.query)},r));return 0!==d.length&&r6(a,n||2,i,e.shouldRenderSuggestions)?(0,l.jsx)("div",{ref:t,className:o.suggestions,"data-testid":"suggestions",children:(0,l.jsxs)("ul",{children:[" ",d," "]})}):null},(e,t)=>{let{query:r,minQueryLength:n=2,isFocused:i,suggestions:o}=t;return!!(e.isFocused===i&&function e(t,r,n,i,o){return t===r||(null!=t&&null!=r&&(te(t)||te(r))?rF(t,r,n,i,e,o):t!=t&&r!=r)}(e.suggestions,o))&&r6(r,n,i,t.shouldRenderSuggestions)===r6(e.query,e.minQueryLength??2,e.isFocused,e.shouldRenderSuggestions)&&e.selectedIndex===t.selectedIndex}),r8=eX(eY(),1),r9=e=>{let{autofocus:t,autoFocus:r,readOnly:n,labelField:i,allowDeleteFromEmptyInput:o,allowAdditionFromPaste:s,allowDragDrop:a,minQueryLength:u,shouldRenderSuggestions:c,removeComponent:d,autocomplete:g,inline:h,maxTags:f,allowUnique:p,editable:v,placeholder:y,delimiters:m,separators:b,tags:O,inputFieldPosition:D,inputProps:T,classNames:S,maxLength:I,inputValue:w,clearAll:E,ariaAttrs:C}=e,[_,j]=(0,J.useState)(e.suggestions),[x,P]=(0,J.useState)(""),[N,R]=(0,J.useState)(!1),[A,M]=(0,J.useState)(-1),[k,L]=(0,J.useState)(!1),[H,U]=(0,J.useState)(""),[F,B]=(0,J.useState)(-1),[$,z]=(0,J.useState)(""),G=(0,J.createRef)(),q=(0,J.useRef)(null),W=(0,J.useRef)(null);(0,J.useEffect)(()=>{m.length&&console.warn("[Deprecation] The delimiters prop is deprecated and will be removed in v7.x.x, please use separators instead. If you have any concerns regarding this, please share your thoughts in https://github.com/react-tags/react-tags/issues/960")},[]),(0,J.useEffect)(()=>{void 0!==h&&console.warn("[Deprecation] The inline attribute is deprecated and will be removed in v7.x.x, please use inputFieldPosition instead.")},[h]),(0,J.useEffect)(()=>{void 0!==t&&console.warn("[Deprecated] autofocus prop will be removed in 7.x so please migrate to autoFocus prop."),(t||r&&!1!==t)&&!n&&X()},[r,r,n]),(0,J.useEffect)(()=>{et()},[x,e.suggestions]);let V=t=>{let r=e.suggestions.slice();if(p){let e=O.map(e=>e.id.trim().toLowerCase());r=r.filter(t=>!e.includes(t.id.toLowerCase()))}if(e.handleFilterSuggestions)return e.handleFilterSuggestions(t,r);let n=r.filter(e=>0===K(t,e)),i=r.filter(e=>K(t,e)>0);return n.concat(i)},K=(e,t)=>t[i].toLowerCase().indexOf(e.toLowerCase()),X=()=>{P(""),q.current&&(q.current.value="",q.current.focus())},Y=(t,r)=>{r.preventDefault(),r.stopPropagation();let n=O.slice();0!==n.length&&(z(""),e?.handleDelete?.(t,r),Q(t,n))},Q=(e,t)=>{if(!G?.current)return;let r=G.current.querySelectorAll(".ReactTags__remove"),n="";0===e&&t.length>1?(n=`Tag at index ${e} with value ${t[e].id} deleted. Tag at index 0 with value ${t[1].id} focussed. Press backspace to remove`,r[0].focus()):e>0?(n=`Tag at index ${e} with value ${t[e].id} deleted. Tag at index ${e-1} with value ${t[e-1].id} focussed. Press backspace to remove`,r[e-1].focus()):(n=`Tag at index ${e} with value ${t[e].id} deleted. Input focussed. Press enter to add a new tag`,q.current?.focus()),U(n)},Z=(t,r,o)=>{n||(v&&(B(t),P(r[i]),W.current?.focus()),e.handleTagClick?.(t,o))},ee=t=>{e.handleInputChange&&e.handleInputChange(t.target.value,t),P(t.target.value.trim())},et=()=>{let e=V(x);j(e),M(A>=e.length?e.length-1:A)},er=t=>{let r=t.target.value;e.handleInputFocus&&e.handleInputFocus(r,t),R(!0)},en=t=>{let r=t.target.value;e.handleInputBlur&&(e.handleInputBlur(r,t),q.current&&(q.current.value="")),R(!1),B(-1)},ei=e=>{if(!e.nativeEvent.isComposing){if("Escape"===e.key&&(e.preventDefault(),e.stopPropagation(),M(-1),L(!1),j([]),B(-1)),(-1!==b.indexOf(e.key)||-1!==m.indexOf(e.keyCode))&&!e.shiftKey){(e.keyCode!==rX.TAB||""!==x)&&e.preventDefault();let t=k&&-1!==A?_[A]:{id:x.trim(),[i]:x.trim(),className:""};Object.keys(t)&&ea(t)}"Backspace"===e.key&&""===x&&(o||D===rZ.INLINE)&&Y(O.length-1,e),e.keyCode===rX.UP_ARROW&&(e.preventDefault(),M(A<=0?_.length-1:A-1),L(!0)),e.keyCode===rX.DOWN_ARROW&&(e.preventDefault(),L(!0),0===_.length?M(-1):M((A+1)%_.length))}},eo=()=>f&&O.length>=f,es=t=>{if(!s)return;if(eo()){z(rJ.TAG_LIMIT),X();return}z(""),t.preventDefault();let r=t.clipboardData||window.clipboardData,n=r.getData("text"),{maxLength:o=n.length}=e,a=Math.min(o,n.length),u=r.getData("text").substr(0,a),l=m;b.length&&(l=[],b.forEach(e=>{let t=function(e){switch(e){case rY.ENTER:return[10,13];case rY.TAB:return 9;case rY.COMMA:return 188;case rY.SPACE:return 32;case rY.SEMICOLON:return 186;default:return 0}}(e);Array.isArray(t)?l=[...l,...t]:l.push(t)}));let c=function(e){var t;let r=(t=re(t=e.map(e=>String.fromCharCode(96<=e?e-48*Math.floor(e/48):e)).join("")))&&rW.test(t)?t.replace(rq,"\\$&"):t;return RegExp(`[${r}]+`)}(l);(function(e){return e&&e.length?rK(e):[]})(u.split(c).map(e=>e.trim())).forEach(e=>ea({id:e.trim(),[i]:e.trim(),className:""}))},ea=t=>{if(!t.id||!t[i])return;if(-1===F){if(eo()){z(rJ.TAG_LIMIT),X();return}z("")}let r=O.map(e=>e.id.toLowerCase());if(!(p&&r.indexOf(t.id.trim().toLowerCase())>=0)){if(g){let e=V(t[i]);console.warn("[Deprecation] The autocomplete prop will be removed in 7.x to simplify the integration and make it more intutive. If you have any concerns regarding this, please share your thoughts in https://github.com/react-tags/react-tags/issues/949"),(1===g&&1===e.length||!0===g&&e.length)&&(t=e[0])}-1!==F&&e.onTagUpdate?e.onTagUpdate(F,t):e?.handleAddition?.(t),P(""),L(!1),M(-1),B(-1),X()}},eu=(t,r)=>{let n=O[t];e?.handleDrag?.(n,t,r)},el=(()=>{let t={...rQ,...e.classNames};return O.map((e,r)=>(0,l.jsx)(J.Fragment,{children:F===r?(0,l.jsx)("div",{className:t.editTagInput,children:(0,l.jsx)("input",{ref:e=>{W.current=e},onFocus:er,value:x,onChange:ee,onKeyDown:ei,onBlur:en,className:t.editTagInputField,onPaste:es,"data-testid":"tag-edit"})}):(0,l.jsx)(r3,{index:r,tag:e,tags:O,labelField:i,onDelete:e=>Y(r,e),moveTag:a?eu:void 0,removeComponent:d,onTagClicked:t=>Z(r,e,t),readOnly:n,classNames:t,allowDragDrop:a})},r))})(),ec={...rQ,...S},{name:ed,id:eg}=e,eh=!1===h?rZ.BOTTOM:D,ef=n?null:(0,l.jsxs)("div",{className:ec.tagInput,children:[(0,l.jsx)("input",{...T,ref:e=>{q.current=e},className:ec.tagInputField,type:"text",placeholder:y,"aria-label":y,onFocus:er,onBlur:en,onChange:ee,onKeyDown:ei,onPaste:es,name:ed,id:eg,maxLength:I,value:w,"data-automation":"input","data-testid":"input"}),(0,l.jsx)(r7,{query:x.trim(),suggestions:_,labelField:i,selectedIndex:A,handleClick:e=>{ea(_[e])},handleHover:e=>{M(e),L(!0)},minQueryLength:u,shouldRenderSuggestions:c,isFocused:N,classNames:ec,renderSuggestion:e.renderSuggestion}),E&&O.length>0&&(0,l.jsx)(r4,{"aria-label":C?.clearAllLabel,classNames:ec,onClick:()=>{e.onClearAll&&e.onClearAll(),z(""),X()}}),$&&(0,l.jsxs)("div",{"data-testid":"error",className:"ReactTags__error",children:[(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",height:"24",width:"24",fill:"#e03131",children:(0,l.jsx)("path",{d:"M256 32c14.2 0 27.3 7.5 34.5 19.8l216 368c7.3 12.4 7.3 27.7 .2 40.1S486.3 480 472 480H40c-14.3 0-27.6-7.7-34.7-20.1s-7-27.8 .2-40.1l216-368C228.7 39.5 241.8 32 256 32zm0 128c-13.3 0-24 10.7-24 24V296c0 13.3 10.7 24 24 24s24-10.7 24-24V184c0-13.3-10.7-24-24-24zm32 224a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"})}),$]})]});return(0,l.jsxs)("div",{className:(0,r8.default)(ec.tags,"react-tags-wrapper"),ref:G,children:[(0,l.jsx)("p",{role:"alert",className:"sr-only",style:{position:"absolute",overflow:"hidden",clip:"rect(0 0 0 0)",margin:"-1px",padding:0,width:"1px",height:"1px",border:0},children:H}),eh===rZ.TOP&&ef,(0,l.jsxs)("div",{className:ec.selected,children:[el,eh===rZ.INLINE&&ef]}),eh===rZ.BOTTOM&&ef]})},ne=e=>{let{placeholder:t="Press enter to add new tag",labelField:r="text",suggestions:n=[],delimiters:i=[],separators:o=e.delimiters?.length?[]:[rY.ENTER,rY.TAB],autofocus:s,autoFocus:a=!0,inline:u,inputFieldPosition:c="inline",allowDeleteFromEmptyInput:d=!1,allowAdditionFromPaste:g=!0,autocomplete:h=!1,readOnly:f=!1,allowUnique:p=!0,allowDragDrop:v=!0,tags:y=[],inputProps:m={},editable:b=!1,clearAll:O=!1,ariaAttrs:D={clearAllLabel:"clear all tags"},handleDelete:T,handleAddition:S,onTagUpdate:I,handleDrag:w,handleFilterSuggestions:E,handleTagClick:C,handleInputChange:_,handleInputFocus:j,handleInputBlur:x,minQueryLength:P,shouldRenderSuggestions:N,removeComponent:R,onClearAll:A,classNames:M,name:k,id:L,maxLength:H,inputValue:U,maxTags:F,renderSuggestion:B}=e;return(0,l.jsx)(r9,{placeholder:t,labelField:r,suggestions:n,delimiters:i,separators:o,autofocus:s,autoFocus:a,inline:u,inputFieldPosition:c,allowDeleteFromEmptyInput:d,allowAdditionFromPaste:g,autocomplete:h,readOnly:f,allowUnique:p,allowDragDrop:v,tags:y,inputProps:m,editable:b,clearAll:O,ariaAttrs:D,handleDelete:T,handleAddition:S,onTagUpdate:I,handleDrag:w,handleFilterSuggestions:E,handleTagClick:C,handleInputChange:_,handleInputFocus:j,handleInputBlur:x,minQueryLength:P,shouldRenderSuggestions:N,removeComponent:R,onClearAll:A,classNames:M,name:k,id:L,maxLength:H,inputValue:U,maxTags:F,renderSuggestion:B})},nt=({...e})=>(0,l.jsx)(en,{backend:eS,children:(0,l.jsx)(ne,{...e})})}};