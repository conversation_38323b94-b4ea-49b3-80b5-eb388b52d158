"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/AddArticleFroala.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _AddArticleEN__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AddArticleEN */ \"(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\");\n/* harmony import */ var _AddArticleFR__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AddArticleFR */ \"(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx\");\n/* harmony import */ var _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_15__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AddArticle = ()=>{\n    _s();\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const savedArticle = localStorage.getItem(\"savedArticle\");\n    const useUpdateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave)();\n    const useCreateArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle)();\n    const useCreateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave)();\n    const useSaveFileHook = (0,_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentYear = new Date().getFullYear();\n    const formikRefAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(`panel`);\n    const [formdataEN, setFormDataEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [formdataFR, setFormDataFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const formdata = new FormData();\n    const formdatafr = new FormData();\n    const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedEnCategories, setSelectedEnCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredFrCategories, setFilteredFrCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidPhotoEN, setUuidPhotoEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uuidPhotoFR, setUuidPhotoFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedLanguages, setSelectedLanguages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        en: true,\n        fr: false\n    });\n    const [categoriesEN, setCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesFR, setCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesEN, setFilteredCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesFR, setFilteredCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesEN, setSelectedCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesFR, setSelectedCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const getCategoriesEN = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"en\");\n    const getCategoriesFR = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"fr\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesEN.data?.categories) {\n            const transformedCategories = getCategoriesEN.data.categories.map((category)=>({\n                    id: category.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesEN(transformedCategories);\n        }\n    }, [\n        getCategoriesEN.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesFR.data?.categories) {\n            const transformedCategories = getCategoriesFR.data.categories.map((category)=>({\n                    id: category?.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesFR(transformedCategories);\n        }\n    }, [\n        getCategoriesFR.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedCategoriesEN.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesEN, \"en\");\n        } else if (selectedCategoriesFR.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesFR, \"fr\");\n        }\n    }, [\n        selectedCategoriesEN,\n        selectedCategoriesFR\n    ]);\n    const fetchTranslatedCategories = async (selectedCategories, language)=>{\n        try {\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_12__.axiosGetJson.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.categories}/${language}/${selectedCategories}`);\n            if (language === \"en\") {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesFR(transformedCategories);\n            } else {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesEN(transformedCategories);\n            }\n        } catch (error) {\n            console.error(\"Error fetching translated categories:\", error);\n        }\n    };\n    const handleImageSelect = async (selectedFile, language)=>{\n        if (language === \"en\") {\n            let uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            setUuidPhotoEN(uuidPhotos);\n            formdata.append(\"file\", selectedFile);\n            setFormDataEN(formdata);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n        } else if (language === \"fr\") {\n            let uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            setUuidPhotoFR(uuidPhotos);\n            formdatafr.append(\"file\", selectedFile);\n            setFormDataFR(formdatafr);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n        }\n    };\n    const titleEN = localStorage.getItem(\"title\");\n    const metaTitleEN = localStorage.getItem(\"metatitle\");\n    const metaDescriptionEN = localStorage.getItem(\"metaDescription\");\n    const contentEN = localStorage.getItem(\"content\");\n    const contentFR = localStorage.getItem(\"contentfr\");\n    const titleFR = localStorage.getItem(\"titlefr\");\n    const metaDescriptionFR = localStorage.getItem(\"metaDescriptionfr\");\n    const metaTitleFR = localStorage.getItem(\"metatitlefr\");\n    const initialValues = {\n        robotsMeta: \"index\",\n        metaTitleEN: metaTitleEN ? JSON.parse(metaTitleEN) : \"\",\n        metaDescriptionEN: metaDescriptionEN ? JSON.parse(metaDescriptionEN) : \"\",\n        descriptionEN: \"\",\n        visibilityEN: \"\",\n        categoryEN: [],\n        imageEN: null,\n        keywordsEN: \"\",\n        titleEN: titleEN ? JSON.parse(titleEN) : \"\",\n        urlEN: \"\",\n        altEN: \"\",\n        contentEN: contentEN ? JSON.parse(contentEN) : \"\",\n        highlightsEN: [],\n        publishDateEN: \"\",\n        faqTitleEN: \"\",\n        faqEN: [],\n        titleFR: titleFR ? JSON.parse(titleFR) : \"\",\n        metaTitleFR: metaTitleFR ? JSON.parse(metaTitleFR) : \"\",\n        metaDescriptionFR: metaDescriptionFR ? JSON.parse(metaDescriptionFR) : \"\",\n        descriptionFR: \"\",\n        visibilityFR: \"\",\n        categoryFR: [],\n        imageFR: null,\n        keywordsFR: \"\",\n        urlFR: \"\",\n        altFR: \"\",\n        contentFR: contentFR ? JSON.parse(contentFR) : \"\",\n        publishDateFR: \"\",\n        highlightsFR: [],\n        faqTitleFR: \"\",\n        faqFR: []\n    };\n    const validationSchema = yup__WEBPACK_IMPORTED_MODULE_3__.object().shape({\n        ...selectedLanguages.en && {\n            metaTitleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            category: yup__WEBPACK_IMPORTED_MODULE_3__.array().min(1, t(\"validations:minCategory\")),\n            visibilityEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        },\n        ...selectedLanguages.fr && {\n            metaTitleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            visibilityFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        }\n    });\n    const uploadFile = (filename, file, formData, lang)=>{\n        return new Promise((resolve, reject)=>{\n            useSaveFileHook.mutate({\n                resource: \"blogs\",\n                folder: currentYear,\n                filename,\n                body: {\n                    formData,\n                    t\n                }\n            }, {\n                onSuccess: (data)=>{\n                    if (data.message === \"uuid exist\") {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    } else {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    }\n                },\n                onError: (error)=>{\n                    console.error(`Error uploading ${lang} image:`, error);\n                    resolve({\n                        lang,\n                        uuid: null\n                    });\n                }\n            });\n        });\n    };\n    const handleSubmit = async (values)=>{\n        const data = {\n            robotsMeta: values.robotsMeta,\n            versions: []\n        };\n        if (!selectedLanguages.en && !selectedLanguages.fr) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select at least one version!\");\n            return;\n        }\n        try {\n            let resultEN, resultFR;\n            if (selectedLanguages.en) {\n                resultEN = await uploadFile(uuidPhotoEN, uuidPhotoFileNameEN, formdataEN, \"English\");\n            }\n            if (selectedLanguages.fr) {\n                resultFR = await uploadFile(uuidPhotoFR, uuidPhotoFileNameFR, formdataFR, \"French\");\n            }\n            if (selectedLanguages.en && resultEN.uuid) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    description: values.descriptionEN,\n                    url: values.urlEN,\n                    visibility: values.visibilityEN,\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    highlights: values.highlightsEN,\n                    image: resultEN.uuid,\n                    category: values.categoryEN === \"\" ? [] : values.categoryEN,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr && resultFR.uuid) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: values.visibilityFR,\n                    description: values.descriptionFR,\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    highlights: values.highlightsFR,\n                    image: resultFR.uuid,\n                    category: values.categoryFR === \"\" ? [] : values.categoryFR,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (data.versions.length > 0) {\n                if (isSavedArticle !== \"\") {\n                    useUpdateAutoSaveHook.mutate({\n                        data: data,\n                        id: isSavedArticle\n                    }, {\n                        onSuccess: ()=>{\n                            localStorage.removeItem(\"title\");\n                            localStorage.removeItem(\"content\");\n                            localStorage.removeItem(\"titlefr\");\n                            localStorage.removeItem(\"contentfr\");\n                            localStorage.removeItem(\"metaDescription\");\n                            localStorage.removeItem(\"metaDescriptionfr\");\n                            localStorage.removeItem(\"metatitle\");\n                            localStorage.removeItem(\"metatitlefr\");\n                            localStorage.removeItem(\"savedArticle\");\n                        //window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        }\n                    });\n                } else {\n                    useCreateArticleHook.mutate({\n                        data\n                    }, {\n                        onSuccess: ()=>{\n                            localStorage.removeItem(\"title\");\n                            localStorage.removeItem(\"content\");\n                            localStorage.removeItem(\"titlefr\");\n                            localStorage.removeItem(\"contentfr\");\n                            localStorage.removeItem(\"metaDescription\");\n                            localStorage.removeItem(\"metaDescriptionfr\");\n                            localStorage.removeItem(\"metatitle\");\n                            localStorage.removeItem(\"metatitlefr\");\n                            localStorage.removeItem(\"savedArticle\");\n                        // window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        }\n                    });\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No valid image uploads found. Article not created.\");\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"An error occurred while processing uploads.\");\n        }\n    };\n    const handleChangeAccordion = (panel)=>(event, newExpanded)=>{\n            setExpanded(newExpanded ? panel : false);\n        };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchFrenchCategories = async ()=>{\n            const response = await axios.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.categories}/en/${selectedEnCategories}`);\n            setFilteredFrCategories(response.data);\n        };\n        if (selectedEnCategories.length > 0) {\n            fetchFrenchCategories();\n        } else {\n            setFilteredFrCategories([]);\n        }\n    }, [\n        selectedEnCategories\n    ]);\n    const handleCategoriesENSelect = (selectedCategories)=>{\n        setSelectedCategoriesEN(selectedCategories);\n    };\n    const handleCategoriesFRSelect = (selectedCategories)=>{\n        setSelectedCategoriesFR(selectedCategories);\n    };\n    const [isSavedArticle, setIsSavedArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedArticle ? savedArticle : \"\");\n    const isSavedArticleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isSavedArticle);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        isSavedArticleRef.current = isSavedArticle;\n    }, [\n        isSavedArticle\n    ]);\n    const autosave = async ()=>{\n        const values = formikRefAll.current?.values;\n        const data = {\n            robotsMeta: values?.robotsMeta,\n            versions: []\n        };\n        if (selectedLanguages.en) {\n            data.versions.push({\n                language: \"en\",\n                metaTitle: values.metaTitleEN,\n                title: values.titleEN,\n                metaDescription: values.metaDescriptionEN,\n                url: values.urlEN,\n                visibility: \"Draft\",\n                publishDate: values.publishDateEN,\n                content: values.contentEN,\n                alt: values.altEN,\n                keywords: values.keywordsEN,\n                category: values.categoryEN,\n                highlights: values.highlightsEN,\n                faqTitle: values.faqTitleEN || \"\",\n                faq: values.faqEN || []\n            });\n        }\n        if (selectedLanguages.fr) {\n            data.versions.push({\n                language: \"fr\",\n                metaTitle: values.metaTitleFR,\n                title: values.titleFR,\n                metaDescription: values.metaDescriptionFR,\n                url: values.urlFR,\n                visibility: \"Draft\",\n                publishDate: values.publishDateFR,\n                content: values.contentFR,\n                alt: values.altFR,\n                keywords: values.keywordsFR,\n                category: values.categoryFR,\n                highlights: values.highlightsFR,\n                faqTitle: values.faqTitleFR || \"\",\n                faq: values.faqFR || []\n            });\n        }\n        if (isSavedArticleRef.current != \"\") {\n            useUpdateAutoSaveHook.mutate({\n                data: data,\n                id: isSavedArticleRef.current\n            });\n        } else {\n            if (data.versions.length > 0) {\n                useCreateAutoSaveHook.mutate({\n                    data\n                }, {\n                    onSuccess: (data)=>{\n                        setIsSavedArticle(data.articleId);\n                        localStorage.setItem(\"savedArticle\", data.articleId);\n                    }\n                });\n            }\n        }\n    };\n    const handleChange = lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default()(autosave, 120000);\n    const handleClear = ()=>{\n        formikRefAll.current?.resetForm();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"createArticle:addArticle\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 463,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"commun\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.en,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            en: !prev.en\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"English\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.fr,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            fr: !prev.fr\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"French\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"experiences\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"form\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Formik, {\n                                        initialValues: initialValues,\n                                        validationSchema: validationSchema,\n                                        innerRef: formikRefAll,\n                                        onSubmit: handleSubmit,\n                                        className: \"formik-form\",\n                                        children: (param)=>{\n                                            let { errors, touched, setFieldValue, values } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Form, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        id: \"accordion\",\n                                                        disableGutters: true,\n                                                        expanded: expanded === `panel`,\n                                                        onChange: handleChangeAccordion(`panel`),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 39\n                                                                }, void 0),\n                                                                \"aria-controls\": `panel-content`,\n                                                                id: `panel-header`,\n                                                                children: t(\"createArticle:settings\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"accordion-detail\",\n                                                                elevation: 0,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"label-form\",\n                                                                                    children: [\n                                                                                        \"Robots meta\",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"select-pentabell\",\n                                                                                            variant: \"standard\",\n                                                                                            sx: {\n                                                                                                m: 1,\n                                                                                                minWidth: 120\n                                                                                            },\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                                value: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.filter((option)=>values.robotsMeta === option),\n                                                                                                selected: values.robotsMeta,\n                                                                                                onChange: (event)=>{\n                                                                                                    setFieldValue(\"robotsMeta\", event.target.value);\n                                                                                                },\n                                                                                                children: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                                        value: item,\n                                                                                                        children: item\n                                                                                                    }, index, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                        lineNumber: 548,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                lineNumber: 535,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 530,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                            className: \"label-error\",\n                                                                                            name: \"robotsMeta\",\n                                                                                            component: \"div\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 554,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                    lineNumber: 528,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 527,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                name: \"robotsMeta\",\n                                                                                component: \"div\",\n                                                                                className: \"label-error\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 562,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                        lineNumber: 526,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, `panel`, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    selectedLanguages.en && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleEN__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        debounce: handleChange,\n                                                        categories: categoriesEN,\n                                                        filteredCategories: filteredCategoriesEN,\n                                                        onCategoriesSelect: handleCategoriesENSelect\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    selectedLanguages.fr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleFR__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        categories: categoriesFR,\n                                                        filteredCategories: filteredCategoriesFR,\n                                                        onCategoriesSelect: handleCategoriesFRSelect,\n                                                        debounce: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"btn-container\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"button\",\n                                                                text: \"Clear\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: handleClear\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"submit\",\n                                                                text: \"Save\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: ()=>{}\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                    lineNumber: 466,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 465,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AddArticle, \"57/0ap54x2D1ClkLvO5TpuJwM1g=\", false, function() {\n    return [\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave,\n        _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories\n    ];\n});\n_c = AddArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticle);\nvar _c;\n$RefreshReg$(_c, \"AddArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx\n"));

/***/ })

});