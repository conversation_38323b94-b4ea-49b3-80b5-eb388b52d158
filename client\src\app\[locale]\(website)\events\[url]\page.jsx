"use server";
import initTranslations from "@/app/i18n";
import { axiosGetJsonSSR } from "@/config/axios";
import { redirect } from "next/navigation";
import { headers } from "next/headers";
import EventsDetail from "../../../../../components/events/EventDetail";
import EventsDetailLeap from "../../../../../components/events/EventDetailLeap";
import EventDetailGitex from "../../../../../components/events/EventDetailGitex";
import EventQHSEEXPO from "../../../../../components/events/EventQHSEEXPO";
import EventDetailLibya from "../../../../../components/events/EventDetailLibya";
import { lazy, Suspense } from "react";
import EventForumAfricaFrance from "@/components/events/EventForumAfricaFrance";
import PentabellSalesTraining from "../../../../../components/events/PentabellSalesTraining";
export async function generateMetadata({ params: { locale, url } }) {

  const canonicalUrl = `https://www.pentabell.com/${locale !== "en" ? `${locale}/` : ""
    }events/${url}`;

  const languages = {
    fr: `https://www.pentabell.com/fr/events/${url}`,
    en: `https://www.pentabell.com/events/${url}`,
    "x-default": `https://www.pentabell.com/events/${url}`,
  };

  const { t } = await initTranslations(locale, [
    "eventDetails",
    "eventDetailsLeap",
    "global",
    "eventDetailsLibya",
    "eventDetailsGitex",
    "ForumAfricaFrance",
    "PentabellSalestraining",
    "QHSEEXPO"
  ]);
  const metaTitle =
    url === "franco-saudi-decarbonization-days"
      ? t("eventDetails:metaTitle")
      : url === "leap-tech-conference-2025-riyadh"
        ? t("eventDetailsLeap:metaTitle")
        : url === "Libyan-French-economic-forum-2025"
          ? t("eventDetailsLibya:metaTitle")
          : url === "Gitex-africa-morocco-2025"
            ? t("eventDetailsGitex:metaTitle")
            : url === "Africa-France-forum-on-ecological-and-energy-transition-2025"
              ? t("ForumAfricaFrance:metaTitle")
              : url === "Pentabell-sales-training-and-workshop"
                ? t("PentabellSalestraining:metaTitle")
                : url === "QHSE-EXPO-2025"
                  ? t("QHSEEXPO:metaTitle")
                  : "";

  const metaDescription =
    url === "franco-saudi-decarbonization-days"
      ? t("eventDetails:metaDescription")
      : url === "leap-tech-conference-2025-riyadh"
        ? t("eventDetailsLeap:metaDescription")
        : url === "Libyan-French-economic-forum-2025"
          ? t("eventDetailsLibya:metaDescription")
          : url === "Gitex-africa-morocco-2025"
            ? t("eventDetailsGitex:metaDescription")
            : url === "Africa-France-forum-on-ecological-and-energy-transition-2025"
              ? t("eventDetailsGitex:metaDescription")
              : url === "Pentabell-sales-training-and-workshop"
                ? t("PentabellSalestraining:metaDescription")
                : url === "QHSE-EXPO-2025"
                  ? t("QHSEEXPO:metaDescription")
                  : "";

  return {
    title: metaTitle,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
  };
}



const page = async ({ searchParams, params }) => {
  const BannerWrapper = lazy(() => import("@/components/events/BannerWrapper"));
  const requestHeaders = headers();
  const userAgent = headers().get("user-agent") || "";
  const isMobile = /mobile/i.test(userAgent);
  const deviceType = isMobile ? "mobile" : "desktop";
  const isMobileSSR = deviceType === "mobile";
  const cookie = requestHeaders.get("cookie");

  try {
    const res = await axiosGetJsonSSR.get(
      `/events/${params.locale}/${params.url}`,
      {
        headers: {
          Cookie: cookie,
        },
      }
    );

    const eventData = res?.data?.data;

    const event =
      params.url === "franco-saudi-decarbonization-days"
        ? "decarbonization"
        : params.url === "leap-tech-conference-2025-riyadh"
          ? "leap"
          : params.url === "Libyan-French-economic-forum-2025"
            ? "libya"
            : params.url === "Gitex-africa-morocco-2025"
              ? "Gitex"
              : params.url === "Africa-France-forum-on-ecological-and-energy-transition-2025"
                ? "AfricaFranceForum"
                : params.url === "Pentabell-sales-training-and-workshop"
                  ? "PentabellSalestraining"
                  : params.url === "QHSE-EXPO-2025"
                    ? "QHSEEXPO"
                    : "";

    const { t } = await initTranslations(params.locale, [
      "eventDetails",
      "eventDetailsLeap",
      "eventDetailsLibya",
      "eventDetailsGitex",
      "ForumAfricaFrance",
      "event",
      "country",
      "PentabellSalestraining",
      "QHSEEXPO"
    ]);

    if (
      !["franco-saudi-decarbonization-days", "leap-tech-conference-2025-riyadh", "Libyan-French-economic-forum-2025", "Gitex-africa-morocco-2025", "Africa-France-forum-on-ecological-and-energy-transition-2025", "Pentabell-sales-training-and-workshop", "QHSE-EXPO-2025"].includes(params.url) &&
      !eventData
    ) {
      redirect(
        params.locale === "en" ? `/events/` : `/${params.locale}/events/`
      );
    }

    return (
      <div id="events-page">
        {eventData ? (
          <>
            <BannerWrapper
              eventData={eventData}
              language={params.locale}
              event={event}
            />
            {event === "libya" ? (
              <EventDetailLibya event={eventData} language={params.locale} />
            ) : event === "leap" ? (
              <EventsDetailLeap event={eventData} language={params.locale} />
            ) : event === "Gitex" ? (
              <EventDetailGitex event={eventData} language={params.locale} />
            ) : event === "AfricaFranceForum" ? (
              <EventForumAfricaFrance event={eventData} language={params.locale} />

            ) :
              event === "PentabellSalestraining" ? (
                <PentabellSalesTraining event={eventData} language={params.locale} />

              ) :
                event === "QHSEEXPO" ? (
                  <EventQHSEEXPO event={eventData} language={params.locale} />

                ) :

                  (
                    <EventsDetail event={eventData} language={params.locale} />
                  )}
          </>
        ) : params.url === "franco-saudi-decarbonization-days" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <EventsDetail language={params.locale} />
          </>
        ) : params.url === "leap-tech-conference-2025-riyadh" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <EventsDetailLeap language={params.locale} />
          </>
        ) : params.url === "Libyan-French-economic-forum-2025" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <EventDetailLibya language={params.locale} />
          </>
        ) : params.url === "Africa-France-forum-on-ecological-and-energy-transition-2025" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <EventForumAfricaFrance language={params.locale} />
          </>
        ) : params.url === "QHSE-EXPO-2025" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <EventQHSEEXPO language={params.locale} />
          </>
        ) : params.url === "Gitex-africa-morocco-2025" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <EventDetailGitex language={params.locale} />
          </>
        ) :
          params.url === "Pentabell-sales-training-and-workshop" ? (
            <>
              <BannerWrapper language={params.locale} event={event} />
              <PentabellSalesTraining language={params.locale} isMobileSSR={isMobileSSR} />
            </>
          ) :


            null}

      </div>
    );

  } catch (error) {
    if (
      !["franco-saudi-decarbonization-days", "leap-tech-conference-2025-riyadh", "Libyan-French-economic-forum-2025", "Gitex-africa-morocco-2025", "Africa-France-forum-on-ecological-and-energy-transition-2025", "Pentabell-sales-training-and-workshop", "QHSE-EXPO-2025"].includes(params.url)
    )
      redirect(
        params.locale === "en" ? `/events/` : `/${params.locale}/events/`
      );
  }
};

export default page;


