"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/languageChanger.js":
/*!*******************************************!*\
  !*** ./src/components/languageChanger.js ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LanguageChanger; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../i18nConfig */ \"(app-pages-browser)/./i18nConfig.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_i18nConfig__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _ui_DropdownMenu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/DropdownMenu */ \"(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _ui_DropdownMenuMobile__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/DropdownMenuMobile */ \"(app-pages-browser)/./src/components/ui/DropdownMenuMobile.jsx\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _assets_images_flag_fr_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/assets/images/flag/fr.png */ \"(app-pages-browser)/./src/assets/images/flag/fr.png\");\n/* harmony import */ var _assets_images_flag_en_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/assets/images/flag/en.png */ \"(app-pages-browser)/./src/assets/images/flag/en.png\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction LanguageChanger(param) {\n    let { withFlag, onlyWebVersion } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"])();\n    const isTablet = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(theme.breakpoints.down(\"md\"));\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentLocale = i18n.language;\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const currentPathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const pathSegments = currentPathname.split(\"/\");\n    const handleChange = async (newLocale)=>{\n        const days = 30;\n        document.cookie = `NEXT_LOCALE=${newLocale};expires=${new Date(Date.now() + days * 24 * 60 * 60 * 1000).toUTCString()};path=/`;\n        const translateAndNavigate = async (apiUrl, slug, routeBase)=>{\n            try {\n                const response = await _config_axios__WEBPACK_IMPORTED_MODULE_7__.axiosGetJson.get(`${apiUrl}/opposite/${currentLocale}/${slug}`);\n                const translatedSlug = response.data.slug;\n                const translatedLink = translatedSlug ? `${newLocale === \"en\" ? \"\" : `/${newLocale}`}/${routeBase}/${translatedSlug}` : newLocale === \"en\" ? \"\" : `/events`;\n                router.push(translatedLink);\n            } catch (error) {}\n        };\n        const translateAndNavigateCategory = async (apiUrl, slug, routeBase)=>{\n            try {\n                const response = await _config_axios__WEBPACK_IMPORTED_MODULE_7__.axiosGetJson.get(`${apiUrl}/${currentLocale}/${routeBase}/${slug}`);\n                const translatedSlug = response.data.slug;\n                const translatedLink = translatedSlug ? `${newLocale === \"en\" ? \"\" : `/${newLocale}`}/blog/category/${translatedSlug}` : currentPathname;\n                router.push(translatedLink);\n            } catch (error) {}\n        };\n        const isPathMatch = (path, length)=>pathSegments[1] === path && pathSegments.length === length || pathSegments[2] === path && pathSegments.length === length + 1;\n        const isPathMatchCategory = (path, length)=>pathSegments[2] === path && pathSegments.length === length || pathSegments[3] === path && pathSegments.length === length + 1;\n        if (currentLocale !== newLocale) {\n            if (isPathMatch(\"opportunities\", 4)) {\n                await translateAndNavigate(_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.opportunity, pathSegments[pathSegments.length - 2], \"opportunities\");\n            } else if (isPathMatch(\"apply\", 4)) {\n                await translateAndNavigate(_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.opportunity, pathSegments[pathSegments.length - 2], \"apply\");\n            } else if (isPathMatch(\"blog\", 4)) {\n                await translateAndNavigate(_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.articles, pathSegments[pathSegments.length - 2], \"blog\");\n            } else if (isPathMatch(\"guides\", 4)) {\n                await translateAndNavigate(_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.guides, pathSegments[pathSegments.length - 2], \"guides\");\n            } else if (isPathMatchCategory(\"category\", 5)) {\n                await translateAndNavigateCategory(_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.category, pathSegments[pathSegments.length - 2], \"blog\");\n            } else if (currentLocale === (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default().defaultLocale) && !(_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default().prefixDefault)) {\n                router.push(`/${newLocale}${currentPathname}`);\n            } else {\n                router.push(currentPathname.replace(`/${currentLocale}`, `/${newLocale}`));\n            }\n        }\n        router.refresh();\n    };\n    const menuItems = [\n        {\n            name: \"En\",\n            onClick: ()=>handleChange(\"en\"),\n            flag: _assets_images_flag_en_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            route: \"\"\n        },\n        {\n            name: \"Fr\",\n            onClick: ()=>handleChange(\"fr\"),\n            flag: _assets_images_flag_fr_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            route: \"\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: onlyWebVersion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_DropdownMenu__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            buttonLabel: currentLocale === \"en\" ? \"En\" : \"Fr\",\n            selectedFlag: currentLocale === \"en\" ? _assets_images_flag_en_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"] : _assets_images_flag_fr_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            buttonHref: \"\",\n            menuItems: menuItems,\n            withFlag: withFlag\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\languageChanger.js\",\n            lineNumber: 139,\n            columnNumber: 9\n        }, this) : isMobile || isTablet ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_DropdownMenuMobile__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            buttonLabel: currentLocale === \"en\" ? \"En\" : \"Fr\",\n            selectedFlag: currentLocale === \"en\" ? _assets_images_flag_en_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"] : _assets_images_flag_fr_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            buttonHref: \"\",\n            locale: currentLocale,\n            menuItems: menuItems,\n            withFlag: withFlag\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\languageChanger.js\",\n            lineNumber: 147,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_DropdownMenu__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            buttonLabel: currentLocale === \"en\" ? \"En\" : \"Fr\",\n            selectedFlag: currentLocale === \"en\" ? _assets_images_flag_fr_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"] : _assets_images_flag_en_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            buttonHref: \"\",\n            menuItems: menuItems,\n            withFlag: withFlag\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\languageChanger.js\",\n            lineNumber: 156,\n            columnNumber: 9\n        }, this)\n    }, void 0, false);\n}\n_s(LanguageChanger, \"5nG/xO9gnyVtyWSsQQEV+MRvoJU=\", false, function() {\n    return [\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname\n    ];\n});\n_c = LanguageChanger;\nvar _c;\n$RefreshReg$(_c, \"LanguageChanger\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/languageChanger.js\n"));

/***/ })

});