"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_en_eventForm_json";
exports.ids = ["_ssr_src_locales_en_eventForm_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/en/eventForm.json":
/*!***************************************!*\
  !*** ./src/locales/en/eventForm.json ***!
  \***************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"createEvent":"Create new Event","addEventEnglish":"Add Event English","addEventFrench":"Add Event French","addWebsiteImage":"Add Website Image","addMobileImage":"Add Mobile Image","subTitle":"Sub Title","eventList":"Events List","addEvent":"Add Event","editEvent":"Edit Event"}');

/***/ })

};
;