#search-bar-glossary {
  @extend #search-bar-opportunities;

  .btn {
    margin: 0 5px;

    svg {
      margin: 0 !important;
    }
  }
}

#glossary-page {
  display: flex;

  .custom-max-width {
    padding-left: 28px;
    padding-right: 28px;
  }

  .letters {
    margin: 35px 0px;

    .length {
      margin-left: 40px;
      font-weight: 600;
      background: $yellow;
      width: 20px;
      padding: 5px;
      border-radius: 20px;
      text-align: center;
    }

    .letter {
      margin-bottom: 20px;
      background-color: $blue;
      border-radius: 5px;
      text-align: center;
      width: 40px;
      color: white;
      font-size: 30px;
    }

    .words {
      display: flex;
      flex-direction: column;
    }

    .word {
      margin: 5px 0;
      text-decoration: none;
      color: black;
    }

    .word:hover {
      color: $blue;
    }

    .glossary-button {
      text-transform: none;
      border: solid 1px;
      border-radius: 20px;
      padding: 5px 10px;
      margin-top: 5px;
      color: $blue;
    }
  }

  .pagination {
    margin-top: 20px;
  }

  .featured-title {
    font-size: 2.5rem;
    font-family: "Proxima-Nova-SemiBold" !important;
    @include media-query(mobile, tablet) {
      font-size: 1.75rem !important;
    }
  }

  padding-bottom: 50px;

  @include media-query(mobile) {
    padding-bottom: 20px;
  }

  .btn.white {
    color: $white;
    border-color: $white;

    @include media-query(mobile) {
      width: auto;
      display: flex;
      justify-content: center;
    }
  }

  .first-blog {
    display: flex;
    justify-content: space-between;

    @include media-query(mobile, tablet) {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      flex-direction: column-reverse;
      align-content: flex-start;
    }
  }

  .blog-img-section {
    border: 1px solid #699bd4;
    background-color: transparent;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: auto;
    min-width: 675px;

    box-shadow: none;
    position: relative;
    margin: 10px 0px 20px 30px;

    @include media-query(mobile, tablet) {
      min-width: 200px;
      margin: 50px 0px 20px 0px;
    }

    .img-section {
      background-size: cover;
      background-position: center;
      padding: 100px;
      margin: 15px;
      background-color: #dbe8f6;
      height: 100%;
      min-height: 150px;
      @include media-query(mobile, tablet) {
        min-height: 100px;
      }
    }
  }

  .last-blog {
    min-height: 220px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    margin-bottom: 20px;
    .description {
      font-size: 18px;
      @include media-query(mobile) {
        font-size: 16px;
      }
    }
    .title,
    .description {
      color: $blue;
      margin-bottom: 20px;

      @include media-query(mobile, tablet) {
        margin-bottom: 20px;
      }
    }
  }
}

#social-media-share {
  @include media-query(mobile) {
    margin-top: 20px;
  }

  background-color: $lightBlue2;
  padding: 15px;
  margin-bottom: 20px;

  .title {
    text-align: center;
    font-size: 20px;
    font-family: "Proxima-Nova-Semibold" !important;
    color: $bankingColor;
    margin: 10px 0;
  }

  div {
    display: flex;

    .btn {
      margin: auto;
      padding: 10px;
      svg {
        margin: 0 0 0 0 !important;
      }
    }
  }
}
#blog__categories__slider {
  margin-top: 20px;
}
.categories-list {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  .category-2 {
    flex: 0 0 13%;
    min-width: max-content;
  }
  .category-2 a {
    text-align: center;
    display: block;
    text-decoration: none;
    font-size: 20px;
    margin-right: 10px;
    color: $grey;
    border: 1px solid $grey;
    border-radius: 18px;
    padding: 8px 20px;
    margin-bottom: 5px;
  }
  .category a {
    display: block;
    text-decoration: none;
    font-size: 14px;
    margin-right: 10px;
    color: $grey;
    border: 1px solid $grey;
    border-radius: 18px;
    padding: 4px 10px;
    margin-bottom: 5px;
    &.light {
      border: 1px solid $white;
      color: $white;
    }
  }
}
#one-blog-details {
  .categories-path {
    padding: 15px 0;
    display: flex;
    align-items: center;

    svg {
      // margin-right: 6px;
      transform: scale(0.5);

      path {
        fill: $grey;
      }
    }

    .link {
      font-size: 18px;
      color: $grey;

      // margin-right: 6px;
      &:last-child {
        color: $bankingColor;
      }
    }
  }

  .blog-content {
    padding: 15px;
    @include dropShadow();
    p {
      font-size: 18px;
    }
    p,
    ul,
    div,
    li {
      background: transparent !important;
      font-size: 18px !important;
    }
    figure {
      width: 100% !important;
      margin: 10px 0;
      height: auto;
      min-width: 300px;

      img {
        width: -webkit-fill-available !important;
        height: auto;
      }
    }
    img {
      min-width: 300px;
      width: -webkit-fill-available !important;
      height: auto;
    }
    table {
      width: -webkit-fill-available !important;
    }
    .summary {
      margin: 15px 0;

      .dropdown-header {
        display: flex;
        align-items: center;
      }

      svg {
        path {
          fill: $blue;
        }
      }
    }
  }

  .date {
    font-size: 16px;
    display: flex;
    align-items: center;

    svg {
      margin-right: 10px;

      path {
        fill: $white;
      }
    }
  }

  .sidebar {
    .last-blog {
      margin-bottom: 10px;

      .btn-ghost.white {
        margin-top: 10px;
        padding: 4px 0;
        width: 100%;
      }
    }
  }
}
.btn-filled-yellow {
  fill: $yellow !important;
}

#glossary-page-details {
  background-color: $lightBlue2;
  #glossary-header {
    padding-top: 10%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    padding-bottom: 15px;
    background-color: $lightBlue2;

    .letter {
      color: $blue;
      font-size: 100px;
      font-weight: 800;
    }

    .heading-h1 {
      font-size: 2.75rem !important;
      margin-top: 5px !important;
    }

    .css-o1xwpy-MuiGrid-root > .MuiGrid-item {
      padding-left: 7% !important;
    }
    @include media-query(mobile) {
      .css-zow5z4-MuiGrid-root > .MuiGrid-item {
        padding-top: 0 !important;
      }
      align-items: center;
      padding-top: 20%;
      .heading-h1 {
        font-size: 1.75rem !important;
      }
    }

    .MuiGrid-container {
      align-items: center;
    }
  }

  .pentabell-company {
    background: linear-gradient(180deg, #234791 0%, #20284b 100%);
    border-radius: 24px;
    padding: 40px;
    margin: 40px 0px;
    display: flex;
    align-items: center;

    @include media-query(mobile, tablet) {
      flex-direction: column;
      margin: 10px 0px 40px;
      padding: 40px;
    }

    .content {
      color: white;
      padding-right: 20px;

      .title {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 30px;
      }

      .description {
        font-size: 18px;
        font-weight: 300;
        margin-bottom: 30px;
      }

      .btn-filled-yellow {
        background: $yellow;
        color: $blue;
        padding: 10px 30px;
        text-transform: none;
        border-radius: 0;
        font-size: 16px;
      }
    }

    .pentabell-offices-icon {
      padding-left: 20px;

      @include media-query(mobile, tablet) {
        padding-left: 0;
        margin-top: 40px;
      }
    }
  }

  .glossary-social-media-icons {
    padding: 120px 40px;
    display: flex;
    flex-direction: column;
    align-items: end;
    justify-content: left;

    @include media-query(mobile, tablet) {
      padding: 10px 0;
      align-items: center;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: center;

      svg {
        margin: 0px 20px;
      }
    }

    svg {
      margin-bottom: 10px;
      border-radius: 50%;
      border: 1px solid;
      cursor: pointer;
      padding: 15px;
    }

    svg:hover {
      border: 1px solid $blue;
      path {
        fill: $blue;
      }
    }
  }

  .date {
    font-size: 16px;
    display: flex;
    align-items: center;
    color: $blue;
    font-weight: 500;

    svg {
      margin-right: 10px;

      path {
        fill: $blue;
      }

      &:last-child {
        margin-left: 20px;
      }
    }
  }

  .blog-img {
    background-color: $blue;
    max-width: 90%;
    height: auto;
    max-height: 80%;
    margin-bottom: 20px;

    // margin-right: 16px;
    img {
      height: auto;
      width: 100%;
      margin: auto;
      margin-left: 20px;
      margin-bottom: -20px;
      margin-top: 20px;
    }
  }

  .last-word {
    background-color: $yellow;
  }

  .glossary-path {
    padding: 15px 0;
    display: flex;
    align-items: center;

    svg {
      transform: scale(0.5);

      path {
        fill: $grey;
      }
    }

    .link {
      font-size: 18px;
      color: $grey;
      text-decoration: none;

      &:last-child {
        color: $bankingColor;
      }
    }

    .word {
      font-size: 18px;
      color: "black";
      text-decoration: none;
    }
  }

  .section-related-blog {
    margin-bottom: 20px;

    .last-blog {
      .card {
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            to top,
            $bankingColor,
            $bankingColorOpacity0
          );
          opacity: 0.6;
          pointer-events: none;
        }

        .card-media {
          height: 185px;

          @include media-query(tablet) {
            height: 136px;
          }
        }

        .card-text {
          color: $white;
          position: absolute;
          bottom: 10px;
          left: 10px;
          font-size: 18px;
          padding: 0px 2px;
          font-family: "Proxima-Nova-Semibold" !important;

          button {
            margin: 0;
            padding: 5px 0px;
            justify-content: left;
          }

          a {
            margin: 0;
            padding: 5px 0px;
            justify-content: left;
          }
        }
      }
    }

    .btn-filled.blue {
      width: max-content;
      margin: 15px auto;
    }
  }

  .section {
    background-color: $lightBlue2;
    padding: 15px;
    margin-bottom: 20px;

    .title {
      font-size: 20px;
      font-family: "Proxima-Nova-Semibold" !important;
      color: $bankingColor;
      margin: 10px 0;
    }

    .description {
      color: $bankingColor;
      margin-bottom: 10px;
      font-size: 16px;
    }

    .btn {
      margin-top: 20px;
      width: -webkit-fill-available;
      text-align: center;
      display: flex;
      justify-content: center;
    }
  }

  #sticky-sidebar {
    padding-left: 7%;
    position: sticky;
    top: 110px;
    left: 0;
    width: 100%;
    z-index: 1000;

    max-height: 80vh;
  }

  #content-table {
    &::-webkit-scrollbar {
      border-radius: 8px;

      background: $white;
      width: 12px;
      // height: 50%;
      //   margin-top: 20px;
      // margin-bottom: 20px;
    }

    &::-webkit-scrollbar-thumb {
      background: $yellow;
      border-radius: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      // margin-bottom: 50px;
      // margin-top: 10px;
    }

    padding: 10px;
    max-height: 50vh;
    margin-bottom: 20px;

    @include media-query(mobile) {
      max-height: 65vh;
    }

    // background-color: $blue;
    @extend .gradient-blue;
    overflow: auto;

    ul {
      list-style: none;
      padding-left: 1em;

      li {
        margin-bottom: 10px;

        a {
          color: $white;
          text-decoration: none;
          font-family: "Proxima-Nova-Medium";
          font-size: 16px;
        }
      }

      & > li::before {
        content: "";
        display: inline-block;
        width: 12px;
        height: 12px;
        background-color: $yellow;
        margin-right: 0.5em;
      }
    }
  }
}
