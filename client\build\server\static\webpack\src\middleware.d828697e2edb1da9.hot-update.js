"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.js":
/*!***************************!*\
  !*** ./src/middleware.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-i18n-router */ \"(middleware)/./node_modules/next-i18n-router/dist/index.js\");\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_i18n_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! jose */ \"(middleware)/./node_modules/jose/dist/browser/jwt/verify.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cookie */ \"(middleware)/./node_modules/cookie/index.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../i18nConfig */ \"(middleware)/./i18nConfig.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_i18nConfig__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/constants */ \"(middleware)/./src/utils/constants.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/functions */ \"(middleware)/./src/utils/functions.js\");\n/* harmony import */ var _config_translations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./config/translations */ \"(middleware)/./src/config/translations.js\");\n/* harmony import */ var _config_allowedParams__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./config/allowedParams */ \"(middleware)/./src/config/allowedParams.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./helpers/routesList */ \"(middleware)/./src/helpers/routesList.js\");\n\n\n\n\n\n\n\n\n\n\nconst SECURITY_CONFIG = {\n    MAX_REQUESTS_PER_MINUTE: 60,\n    JWT_ALGORITHM: \"HS256\",\n    SUSPICIOUS_PATTERNS: [\n        /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n        /javascript:/gi,\n        /on\\w+\\s*=/gi,\n        /eval\\s*\\(/gi,\n        /expression\\s*\\(/gi,\n        /%3Cscript/gi,\n        /%3C%2Fscript%3E/gi\n    ]\n};\nconst rateLimitStore = new Map();\nconst logSecurityEvent = (event, details = {})=>{\n    if (true) {\n        console.warn(`[SECURITY] ${event}:`, {\n            timestamp: new Date().toISOString(),\n            ...details\n        });\n    }\n};\nconst verifyToken = async (token, clientIP = \"unknown\")=>{\n    try {\n        if (!token || typeof token !== \"string\") {\n            logSecurityEvent(\"INVALID_TOKEN_FORMAT\", {\n                clientIP,\n                reason: \"Missing or invalid token\"\n            });\n            return null;\n        }\n        // Validate JWT format\n        const jwtRegex = /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/;\n        if (!jwtRegex.test(token)) {\n            logSecurityEvent(\"INVALID_JWT_FORMAT\", {\n                clientIP,\n                tokenPrefix: token.substring(0, 10)\n            });\n            return null;\n        }\n        // Check environment configuration\n        const jwtSecret = process.env.NEXT_JWT_SECRET;\n        if (!jwtSecret || jwtSecret.length < 32) {\n            logSecurityEvent(\"WEAK_JWT_SECRET\", {\n                clientIP\n            });\n            throw new Error(\"JWT secret configuration error\");\n        }\n        const secret = new TextEncoder().encode(jwtSecret);\n        // Verify token with additional security options\n        const { payload } = await (0,jose__WEBPACK_IMPORTED_MODULE_9__.jwtVerify)(token, secret, {\n            algorithms: [\n                SECURITY_CONFIG.JWT_ALGORITHM\n            ],\n            issuer: process.env.JWT_ISSUER || \"pentabell-api\",\n            audience: process.env.JWT_AUDIENCE || \"pentabell-client\"\n        });\n        // Validate payload structure\n        if (!payload || !payload._id || !payload.roles || !Array.isArray(payload.roles)) {\n            logSecurityEvent(\"INVALID_TOKEN_PAYLOAD\", {\n                clientIP,\n                hasId: !!payload?._id,\n                hasRoles: !!payload?.roles\n            });\n            return null;\n        }\n        // Check token age (optional additional security)\n        const tokenAge = Date.now() / 1000 - (payload.iat || 0);\n        if (tokenAge > 86400) {\n            // 24 hours\n            logSecurityEvent(\"OLD_TOKEN_USAGE\", {\n                clientIP,\n                tokenAge,\n                userId: payload._id\n            });\n        }\n        return payload;\n    } catch (error) {\n        if (error.name === \"JWTExpired\") {\n            logSecurityEvent(\"TOKEN_EXPIRED\", {\n                clientIP\n            });\n        } else if (error.name === \"JWTInvalid\") {\n            logSecurityEvent(\"INVALID_TOKEN\", {\n                clientIP,\n                error: error.message\n            });\n        } else {\n            logSecurityEvent(\"TOKEN_VERIFICATION_ERROR\", {\n                clientIP,\n                error: error.message\n            });\n        }\n        return null;\n    }\n};\n// Rate limiting function\nconst checkRateLimit = (clientIP)=>{\n    const now = Date.now();\n    const windowStart = now - 60000; // 1 minute window\n    if (!rateLimitStore.has(clientIP)) {\n        rateLimitStore.set(clientIP, []);\n    }\n    const requests = rateLimitStore.get(clientIP);\n    // Remove old requests\n    const validRequests = requests.filter((timestamp)=>timestamp > windowStart);\n    rateLimitStore.set(clientIP, validRequests);\n    // Check if limit exceeded\n    if (validRequests.length >= SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE) {\n        return false;\n    }\n    // Add current request\n    validRequests.push(now);\n    return true;\n};\n// Input sanitization function\nconst sanitizeInput = (value)=>{\n    if (typeof value !== \"string\") return value;\n    let sanitized = value;\n    SECURITY_CONFIG.SUSPICIOUS_PATTERNS.forEach((pattern)=>{\n        sanitized = sanitized.replace(pattern, \"\");\n    });\n    return sanitized.trim();\n};\n// Security headers function\nconst setSecurityHeaders = (response)=>{\n    response.headers.set(\"X-Content-Type-Options\", \"nosniff\");\n    response.headers.set(\"X-Frame-Options\", \"DENY\");\n    response.headers.set(\"X-XSS-Protection\", \"1; mode=block\");\n    response.headers.set(\"Referrer-Policy\", \"strict-origin-when-cross-origin\");\n    response.headers.set(\"Permissions-Policy\", \"geolocation=(), microphone=(), camera=()\");\n    // Remove server information\n    response.headers.delete(\"Server\");\n    response.headers.delete(\"X-Powered-By\");\n    return response;\n};\nasync function middleware(req) {\n    const url = req.nextUrl.clone();\n    const { defaultLocale } = (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default());\n    const { pathname } = req.nextUrl;\n    const clientIP = req.ip || req.headers.get(\"x-forwarded-for\") || req.headers.get(\"x-real-ip\") || \"unknown\";\n    // Initialize response with security headers\n    let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    response = setSecurityHeaders(response);\n    // Rate limiting check\n    if (!checkRateLimit(clientIP)) {\n        logSecurityEvent(\"RATE_LIMIT_EXCEEDED\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Too Many Requests\", {\n            status: 429,\n            headers: {\n                \"Retry-After\": \"60\",\n                \"X-RateLimit-Limit\": SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE.toString(),\n                \"X-RateLimit-Remaining\": \"0\"\n            }\n        });\n    }\n    // Sanitize query parameters\n    let hasModifiedParams = false;\n    for (const [key, value] of url.searchParams.entries()){\n        const sanitizedValue = sanitizeInput(value);\n        if (sanitizedValue !== value) {\n            url.searchParams.set(key, sanitizedValue);\n            hasModifiedParams = true;\n            logSecurityEvent(\"SUSPICIOUS_QUERY_PARAM\", {\n                clientIP,\n                key,\n                originalValue: value.substring(0, 50)\n            });\n        }\n    }\n    // Check for suspicious patterns in pathname\n    const pathString = pathname.toLowerCase();\n    const hasSuspiciousPath = SECURITY_CONFIG.SUSPICIOUS_PATTERNS.some((pattern)=>pattern.test(pathString));\n    if (hasSuspiciousPath) {\n        logSecurityEvent(\"SUSPICIOUS_PATH_ACCESS\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Forbidden\", {\n            status: 403\n        });\n    }\n    // Enhanced authentication and authorization\n    const cookies = cookie__WEBPACK_IMPORTED_MODULE_2__.parse(req.headers.get(\"cookie\") || \"\");\n    const { accessToken, refreshToken } = cookies;\n    // Check for protected routes\n    const isProtectedRoute = pathname.includes(\"dashboard\") || pathname.includes(\"backoffice\");\n    if (isProtectedRoute && !(accessToken && refreshToken)) {\n        logSecurityEvent(\"UNAUTHORIZED_ACCESS_ATTEMPT\", {\n            clientIP,\n            pathname\n        });\n        url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // Enhanced token verification with proper token selection\n    let user = null;\n    if (refreshToken) {\n        user = await verifyToken(refreshToken, clientIP);\n        // Additional security check for protected routes\n        if (isProtectedRoute && !user) {\n            logSecurityEvent(\"INVALID_TOKEN_PROTECTED_ROUTE\", {\n                clientIP,\n                pathname\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n    }\n    // Handle logout route\n    if (pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/`) {\n        logSecurityEvent(\"USER_LOGOUT\", {\n            clientIP,\n            userId: user?._id\n        });\n        return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n    }\n    // Enhanced role-based access control\n    if (user) {\n        const menuList = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.getRoutesListByRole)(user);\n        // Validate user roles\n        if (!user.roles || !Array.isArray(user.roles) || user.roles.length === 0) {\n            logSecurityEvent(\"INVALID_USER_ROLES\", {\n                clientIP,\n                userId: user._id\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n        const checkRole = !menuList?.some((item)=>pathname.includes(item)) && (pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}`) || pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}`));\n        // Enhanced role checking with security logging\n        if (checkRole || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`) {\n            let redirectPath = null;\n            if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.home.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.EDITOR)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.blogs.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.CANDIDATE)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.candidateRoutes.myApplications.route}`;\n            }\n            if (redirectPath) {\n                logSecurityEvent(\"ROLE_BASED_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles,\n                    fromPath: pathname,\n                    toPath: redirectPath\n                });\n                url.pathname = redirectPath;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            } else {\n                logSecurityEvent(\"NO_VALID_ROLE_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles\n                });\n                url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            }\n        }\n    }\n    // Enhanced parameter filtering with security logging\n    let removedParams = [];\n    for (const param of url.searchParams.keys()){\n        if (!_config_allowedParams__WEBPACK_IMPORTED_MODULE_7__.allowedParams.has(param)) {\n            url.searchParams.delete(param);\n            removedParams.push(param);\n        }\n    }\n    if (removedParams.length > 0) {\n        logSecurityEvent(\"REMOVED_DISALLOWED_PARAMS\", {\n            clientIP,\n            pathname,\n            removedParams,\n            userId: user?._id\n        });\n    }\n    // Check if parameters were modified (either sanitized or removed)\n    if (hasModifiedParams || url.searchParams.toString() !== req.nextUrl.searchParams.toString()) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // Enhanced redirection paths with security checks\n    const frPath = _config_translations__WEBPACK_IMPORTED_MODULE_6__[\"default\"][req.nextUrl.pathname];\n    if (frPath) {\n        logSecurityEvent(\"FRENCH_PATH_REDIRECT\", {\n            clientIP,\n            fromPath: req.nextUrl.pathname,\n            toPath: frPath\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(frPath, req.url));\n    }\n    // Enhanced language handling with security validation\n    if (!pathname.startsWith(\"/fr\") && !pathname.startsWith(`/${defaultLocale}`) && !pathname.startsWith(\"/_next\") && !pathname.startsWith(\"/api\") && !pathname.startsWith(\"/static\") && !pathname.includes(\".\")) {\n        // Additional security check for suspicious paths\n        if (pathname.length > 200) {\n            logSecurityEvent(\"SUSPICIOUS_LONG_PATH\", {\n                clientIP,\n                pathLength: pathname.length\n            });\n            return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Bad Request\", {\n                status: 400\n            });\n        }\n        url.pathname = `/en${pathname}`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.rewrite(url);\n    }\n    // Log successful requests for monitoring (in development only)\n    if ( true && user) {\n        logSecurityEvent(\"SUCCESSFUL_REQUEST\", {\n            clientIP,\n            pathname,\n            userId: user._id,\n            roles: user.roles\n        });\n    }\n    return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     * - files with extensions (images, fonts, etc.)\r\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|public|.*\\\\..*).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.js\n");

/***/ })

});