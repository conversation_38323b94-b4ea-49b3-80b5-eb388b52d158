"use strict";exports.id=2365,exports.ids=[2365],exports.modules={9130:(e,t,n)=>{n.d(t,{Z:()=>i});var r,a,s=n(95746);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}let i=e=>s.createElement("svg",l({xmlns:"http://www.w3.org/2000/svg",width:29,height:28,fill:"none"},e),r||(r=s.createElement("rect",{width:27,height:27,x:28,y:27.5,stroke:"#798BA3",rx:13.5,transform:"rotate(-180 28 27.5)"})),a||(a=s.createElement("path",{stroke:"#798BA3",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"m16.75 9.5-4.5 4.5 4.5 4.5"})))},23218:(e,t,n)=>{n.r(t),n.d(t,{default:()=>l});var r,a=n(95746);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}let l=e=>a.createElement("svg",s({xmlns:"http://www.w3.org/2000/svg",width:12,height:14,fill:"none"},e),r||(r=a.createElement("path",{fill:"#fff",fillRule:"evenodd",d:"M6 1.465a4.56 4.56 0 0 0-3.182 1.287A4.34 4.34 0 0 0 1.5 5.86c0 1.7 1.137 3.373 2.42 4.696A17 17 0 0 0 6 12.365q.155-.114.353-.267a17 17 0 0 0 1.728-1.542C9.363 9.233 10.5 7.56 10.5 5.86a4.34 4.34 0 0 0-1.318-3.108A4.56 4.56 0 0 0 6 1.465m0 11.802-.416.61-.002-.002-.004-.002-.014-.01a7 7 0 0 1-.229-.156 18.345 18.345 0 0 1-2.505-2.143C1.489 10.178 0 8.148 0 5.86c0-1.554.632-3.044 1.757-4.144A6.07 6.07 0 0 1 6 0c1.591 0 3.117.617 4.243 1.716A5.8 5.8 0 0 1 12 5.86c0 2.29-1.488 4.32-2.83 5.703a18.4 18.4 0 0 1-2.734 2.3l-.014.01-.004.003h-.001s-.001.001-.417-.609m0 0 .416.61a.77.77 0 0 1-.832 0zm0-8.383c-.552 0-1 .437-1 .976 0 .54.448.977 1 .977S7 6.4 7 5.86a.99.99 0 0 0-1-.976m-2.5.976C3.5 4.512 4.62 3.42 6 3.42S8.5 4.512 8.5 5.86c0 1.35-1.12 2.442-2.5 2.442S3.5 7.21 3.5 5.86",clipRule:"evenodd"})))},68719:(e,t,n)=>{n.d(t,{Z:()=>i});var r,a,s=n(95746);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}let i=e=>s.createElement("svg",l({xmlns:"http://www.w3.org/2000/svg",width:18,height:18,fill:"none"},e),r||(r=s.createElement("g",{clipPath:"url(#refreshIcon_svg__a)"},s.createElement("path",{fill:"#234791",fillRule:"evenodd",d:"M6.85 1.557a7.75 7.75 0 0 1 9.46 4.86 1 1 0 1 1-1.885.667 5.75 5.75 0 0 0-9.51-2.125L3.275 6.5H5.25a1 1 0 1 1 0 2H.75a1 1 0 0 1-1-1V3a1 1 0 1 1 2 0v2.188l1.784-1.676a7.75 7.75 0 0 1 3.317-1.955m4.9 8.943a1 1 0 0 1 1-1h4.5a1 1 0 0 1 1 1V15a1 1 0 1 1-2 0v-2.188l-1.784 1.677A7.75 7.75 0 0 1 1.69 11.584a1 1 0 1 1 1.885-.667 5.75 5.75 0 0 0 9.51 2.124l1.64-1.54H12.75a1 1 0 0 1-1-1",clipRule:"evenodd"}))),a||(a=s.createElement("defs",null,s.createElement("clipPath",{id:"refreshIcon_svg__a"},s.createElement("path",{fill:"#fff",d:"M0 0h18v18H0z"})))))},36990:(e,t,n)=>{n.d(t,{Z:()=>l});var r,a=n(95746);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}let l=e=>a.createElement("svg",s({xmlns:"http://www.w3.org/2000/svg",width:18,height:18,fill:"none"},e),r||(r=a.createElement("path",{stroke:"#798BA3",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"m15.75 15.75-3.262-3.262M14.25 8.25a6 6 0 1 1-12 0 6 6 0 0 1 12 0"})))},94474:(e,t,n)=>{n.r(t),n.d(t,{default:()=>l});var r,a=n(95746);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}let l=e=>a.createElement("svg",s({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none"},e),r||(r=a.createElement("path",{fill:"#798BA3",d:"M7.999 14.666a6.667 6.667 0 1 1 0-13.333 6.667 6.667 0 0 1 0 13.333m0-1.333a5.333 5.333 0 1 0 0-10.667 5.333 5.333 0 0 0 0 10.667M8.665 8h2.667v1.333h-4V4.666h1.333z"})))},49369:(e,t,n)=>{n.d(t,{Z:()=>x});var r,a,s=n(10326),l=n(17577),i=n(11383),o=n(78439),c=n.n(o),d=n(56390),u=n(50201),p=n(95746);function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}let g=e=>p.createElement("svg",h({xmlns:"http://www.w3.org/2000/svg",width:29,height:28,fill:"none"},e),r||(r=p.createElement("rect",{width:27,height:27,x:1,y:.5,stroke:"#798BA3",rx:13.5})),a||(a=p.createElement("path",{stroke:"#798BA3",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"m12.25 18.5 4.5-4.5-4.5-4.5"})));var m=n(9130);let y=({totalPages:e,currentPage:t,onPageChange:n,onPageSizeChange:r,showSelectPageSize:a,type:o,searchQueryParams:c})=>{let[p,h]=(0,l.useState)(5);return(0,l.useEffect)(()=>{h(p)},[p]),(0,s.jsxs)("center",{className:"pagination",children:[a&&s.jsx("div",{className:"page-size-container",children:(0,s.jsxs)(d.Z,{value:p,onChange:e=>{let t=Number(e.target.value);h(t),r(t)},className:"page-size-select",children:[s.jsx("option",{value:"3",children:"3"}),s.jsx("option",{value:"5",children:"5"}),s.jsx("option",{value:"8",children:"8"})]})}),s.jsx("div",{className:"pagination-wrapper",children:s.jsx(i.Z,{count:e,page:t,onChange:(e,t)=>{if("ssr"===o){let e=new URLSearchParams(c);e.set("pageNumber",t),window.location.href=`?${e.toString()}`}else n(parseInt(t))},siblingCount:0,boundaryCount:1,renderItem:e=>s.jsx(u.Z,{slots:{previous:m.Z,next:g},...e}),sx:{"& .MuiPaginationItem-root":{fontWeight:"400",color:"#798BA3"},"& .Mui-selected":{backgroundColor:"transparent !important",color:"#0B3051",fontWeight:"700"}}})})]})};y.propTypes={totalPages:c().number.isRequired,currentPage:c().number.isRequired,onPageChange:c().func.isRequired,onPageSizeChange:c().func.isRequired};let x=y},56849:(e,t,n)=>{n.d(t,{default:()=>er});var r,a,s,l=n(10326),i=n(17577),o=n(52210),c=n(90423),d=n(16027),u=n(23743),p=n(88441),h=n(21464),g=n(35047);let m=(e,t,n)=>{let r=(0,g.usePathname)(),a=(0,g.useSearchParams)(),s=a?.toString()||"",l=new URLSearchParams(s),[o,c]=(0,i.useState)(a?.get("keyWord")||""),[d,u]=(0,i.useState)(a?.get("country")||""),[p,h]=(0,i.useState)(parseInt(a?.get("pageNumber")||"1",10)),[m,y]=(0,i.useState)(a?.get("industry")?.split(",")||[]),[x,f]=(0,i.useState)(a?.get("contractType")?.split(",")||[]),[v,j]=(0,i.useState)(a?.get("levelOfExperience")?.split(",")||[]),[w,b]=(0,i.useState)(a?.get("jobDescriptionLanguages")?.split(",")||[]),[C,N]=(0,i.useState)([]),[E,k]=(0,i.useState)(!0),[S,A]=(0,i.useState)(void 0!==n?n:a?.get("list")==="Yes");(0,i.useEffect)(()=>{let e=[];if(a?.get("industry")){let t=a.get("industry").split(",");e.push(...t.map(e=>({category:"industry",label:e.trim()})))}if(a?.get("contractType")){let t=a.get("contractType").split(",");e.push(...t.map(e=>({category:"contractType",label:e.trim()})))}if(a?.get("levelOfExperience")){let t=a.get("levelOfExperience").split(",");e.push(...t.map(e=>({category:"levelOfExperience",label:e.trim()})))}if(a?.get("jobDescriptionLanguages")){let t=a.get("jobDescriptionLanguages").split(",");e.push(...t.map(e=>({category:"jobDescriptionLanguages",label:e.trim()})))}if(a?.get("country")){let t=a.get("country").split(",");e.push(...t.map(e=>({category:"country",label:e.trim()})))}if(a?.get("keyWord")){let t=a.get("keyWord").split(",");e.push(...t.map(e=>({category:"keyWord",label:e.trim()})))}N(e)},[a]);let L=(0,i.useCallback)(n=>{let a=n||window.scrollY||document.documentElement.scrollTop;window._isSearching=!0,m&&m.length>0&&l.set("industry",Array.isArray(m)?m.join(","):m),x&&x.length>0&&l.set("contractType",Array.isArray(x)?x.join(","):x),o&&l.set("keyWord",o),d&&l.set("country",Array.isArray(d)?d.join(","):d),l.set("pageNumber",p||1),S?l.set("list","Yes"):l.delete("list"),w&&w.length>0&&l.set("jobDescriptionLanguages",Array.isArray(w)?w.join(","):w),v&&v.length>0&&l.set("levelOfExperience",Array.isArray(v)?v.join(","):v);let s=`${r}?${l.toString()}`;window.history.replaceState({path:s},"",s);let i={};for(let[e,t]of l.entries())i[e]=t;i.language=t,e(i),window.scrollTo({top:a,behavior:"instant"}),window._isSearching=!1},[m,x,o,d,p,w,v,l,r,e,t]),P=(0,i.useCallback)(e=>{c(e.target.value)},[]),O=(0,i.useCallback)(e=>{e&&e.preventDefault&&e.preventDefault();let t=window.scrollY||document.documentElement.scrollTop;return""===o?l.delete("keyWord"):o&&l.set("keyWord",o),d&&0!==d.length?d&&l.set("country",Array.isArray(d)?d.join(","):d):l.delete("country"),L(t),window.dispatchEvent(new CustomEvent("searchPerformed",{detail:{keyWord:o,country:d,industry:m,contractType:x,levelOfExperience:v,jobDescriptionLanguages:w,maintainScroll:!0,scrollPosition:t}})),!1},[L,o,d,m,x,v,w,l]),T=(0,i.useCallback)(e=>{if("Enter"===e.key)return e.preventDefault(),!1},[]),Z=(0,i.useCallback)((n,a={})=>{n&&n.preventDefault&&n.preventDefault();let s=window.scrollY||document.documentElement.scrollTop;window._isResetting=!0,c(""),h(1),a.preserveCountry||u(""),a.preserveIndustry||y([]),f([]),j([]),b([]);let l=new URLSearchParams;l.set("pageNumber",1),S&&l.set("list","Yes"),a.preserveIndustry&&m&&m.length>0&&l.set("industry",Array.isArray(m)?m.join(","):m),a.preserveCountry&&a.countryName&&l.set("country",a.countryName);let i=`${r}?${l.toString()}`;window.history.replaceState({path:i},"",i);let o={pageNumber:1,language:t};a.preserveIndustry&&(a.industryName?o.industry=a.industryName:m&&m.length>0&&(o.industry=Array.isArray(m)?m.join(","):m)),a.preserveCountry&&a.countryName&&(o.country=a.countryName),e(o);let d=[];return a.preserveIndustry&&C.length>0&&(d=C.filter(e=>"industry"===e.category)),N(d),window.dispatchEvent(new CustomEvent("filtersReset",{detail:{pageNumber:1,maintainScroll:!0,scrollPosition:s,preserveIndustry:a.preserveIndustry,preserveCountry:a.preserveCountry}})),window.scrollTo({top:s,behavior:"instant"}),window._isResetting=!1,!1},[c,h,u,y,f,j,b,N,r,e,t,m,C]),D=(0,i.useCallback)(n=>{h(n),l.set("pageNumber",n),S?l.set("list","Yes"):l.delete("list");let a=`${r}?${l.toString()}`;window.history.replaceState({path:a},"",a);let s={};for(let[e,t]of l.entries())s[e]=t;s.language=t,e(s)},[l,r,e,t]),I=(0,i.useCallback)(t=>{let n=window.scrollY||document.documentElement.scrollTop;A(t);let a=new URLSearchParams(window.location.search),s={};for(let[e,t]of a.entries())"list"!==e&&(s[e]=t);t?(a.set("list","Yes"),s.list="Yes"):a.delete("list"),s.pageSize=10;let l=`${r}?${a.toString()}`;window.history.replaceState({path:l},"",l),window.scrollTo({top:n,behavior:"instant"}),e(s),window.dispatchEvent(new CustomEvent("viewModeChanged",{detail:{isList:t,params:s,maintainScroll:!0,scrollPosition:n}}))},[e,r]);return(0,i.useEffect)(()=>{let e=a?.get("list")==="Yes";e!==S&&A(e)},[a,S]),{keyWord:o,setKeyWord:c,country:d,setCountry:u,pageNumber:p,setPageNumber:h,industry:m,setIndustry:y,contractType:x,setContractType:f,levelOfExperience:v,setLevelOfExperience:j,jobDescriptionLanguages:w,setJobDescriptionLanguages:b,selectedFilters:C,setSelectedFilters:N,isFilterOpen:E,setIsFilterOpen:k,handleSearchChange:P,handleSearchClick:O,handleKeyDown:T,resetSearch:Z,handlePageChange:D,updateUrlWithParams:L,searchQueryParams:l,searchParamsContent:s,isList:S,setIsList:A,handleViewModeChange:I}};var y=n(78077),x=n(57329),f=n(84648),v=n(36990),j=n(23218),w=n(68719),b=n(15082);let C=({keyWord:e,country:t,countries:n,handleSearchChange:r,setCountry:a,resetSearch:s,handleSearchClick:i,setPageNumber:o,jobLocation:c,t:u})=>(0,l.jsxs)(d.default,{className:"container",container:!0,spacing:0,children:[(0,l.jsxs)(d.default,{item:!0,xs:12,sm:9,container:!0,spacing:0,className:"filter-inputs",children:[l.jsx(d.default,{item:!0,xs:12,sm:!0===c?8:6,children:l.jsx(y.Z,{className:"input-pentabell",autoComplete:"off",slotProps:{input:{startAdornment:l.jsx(x.Z,{position:"start",children:l.jsx(v.Z,{})})}},variant:"standard",type:"text",onKeyDown:e=>{if("Enter"===e.key)return e.preventDefault(),!1},onChange:e=>{r(e)},value:e,placeholder:u("Search")})}),!0===c?l.jsx(l.Fragment,{}):l.jsx(d.default,{item:!0,xs:12,sm:6,children:l.jsx(f.Z,{className:"input-pentabell maps",id:"tags-standard",options:n,getOptionLabel:e=>e,value:t,onChange:(e,t)=>{a(t)},renderInput:e=>l.jsx(y.Z,{...e,className:"input-pentabell multiple-select",variant:"standard",placeholder:"Country",InputProps:{...e.InputProps,startAdornment:(0,l.jsxs)(l.Fragment,{children:[l.jsx(x.Z,{position:"start",children:l.jsx(j.default,{})}),e.InputProps.startAdornment]})}})})})]}),(0,l.jsxs)(d.default,{item:!0,xs:12,sm:3,className:"btns-filter search-bar-opportunities filter-inputs",children:[l.jsx(b.default,{icon:l.jsx(w.Z,{}),className:"btn btn-outlined btn-refresh",onClick:e=>s(e)}),l.jsx(b.default,{text:"Search",onClick:e=>{i(e),o(1)},className:" btn btn-search btn-filled"})]})]});var N=n(21656),E=n(85560);let k=({selectedFilters:e})=>l.jsx(N.Z,{className:"checkbox-pentabell-delete check",direction:"row",spacing:1,sx:{marginTop:"20px",flexWrap:"wrap",mb:2},children:Array.isArray(e)&&e.map((e,t)=>l.jsx(E.Z,{label:`${e.label}`,sx:{backgroundColor:"transparent",borderColor:"#1D5A9F !important",marginTop:"10px !important",border:"1px solid",padding:"8px 20px","&:hover":{backgroundColor:"transparent"},".css-1dybbl5-MuiChip-label":{fontSize:"14px",fontFamily:"Proxima-Nova-Medium",color:"#1D5A9F","!important":!0}}},t))});var S=n(98139),A=n(91345),L=n(49369);let P=({opportunitiesData:e,language:t,pageNumber:n,handlePageChange:r,searchParamsContent:a,t:s,isList:i,isLoading:o})=>{let c=e?.totalOpportunities||0;return(0,l.jsxs)(l.Fragment,{children:[l.jsx(d.default,{item:!0,lg:9,md:9,sm:12,xs:12,container:!0,className:"grid",children:o?(0,l.jsxs)("div",{className:"loading-container",style:{textAlign:"center",padding:"40px 0",width:"100%"},children:[l.jsx(S.default,{size:40}),l.jsx("p",{style:{marginTop:"16px"},children:s("global:loading")})]}):e?.opportunities?.length>0?e.opportunities.map(e=>l.jsx(A.default,{opportunity:e,language:t,isList:i},e?._id)):(0,l.jsxs)("div",{className:"no-results-container",style:{textAlign:"center",padding:"40px 0",width:"100%"},children:[l.jsx("p",{className:"no-results-message",children:s("opportunities:noOpportunitiesFound")}),l.jsx("p",{children:s("opportunities:tryDifferentFilters")})]})}),l.jsx(d.default,{item:!0,xs:12,lg:12,md:12,sm:12,container:!0,spacing:0,children:c>0&&l.jsx(L.Z,{type:"ssr",totalPages:Math.ceil(c/10),currentPage:n,onPageChange:r,searchQueryParams:a})})]})};var O=n(63568),T=n(88948),Z=n(21418),D=n(25609),I=n(50295),W=n(39404);let R=({title:e,expanded:t,onChange:n,children:r})=>(0,l.jsxs)(T.Z,{expanded:t,onChange:n,sx:{backgroundColor:"transparent",boxShadow:"none",margin:0,padding:0},children:[l.jsx(Z.Z,{expandIcon:l.jsx(W.Z,{}),children:l.jsx(D.default,{component:"span",sx:{width:"auto",flexShrink:0},className:"title-filter",children:e})}),l.jsx(I.Z,{children:r})]});var $=n(5394),M=n(76971);let F=({options:e,values:t,category:n,onChange:r})=>l.jsx("div",{className:"filter-options",children:e.map(e=>l.jsx($.Z,{control:l.jsx(M.Z,{checked:t?.[n]?.includes(e),onChange:()=>r(n,e),className:"checkbox-custom-color"}),label:e,className:"checkbox-pentabell-filter blue"},e))}),_=({value:e,onChange:t,placeholder:n})=>l.jsx(y.Z,{className:"input-pentabell",autoComplete:"off",slotProps:{input:{startAdornment:l.jsx(x.Z,{position:"start",children:l.jsx(v.Z,{})})}},variant:"standard",type:"text",value:Array.isArray(e)?e.length>0?e[0]:"":e||"",onChange:t,placeholder:n}),z=({value:e,options:t,onChange:n})=>l.jsx("div",{className:"filter-options",children:l.jsx(f.Z,{className:"input-pentabell maps",id:"tags-standard",options:t,getOptionLabel:e=>e,value:Array.isArray(e)?e.length>0?e[0]:"":e||"",onChange:n,renderInput:e=>l.jsx(y.Z,{...e,className:"input-pentabell multiple-select",variant:"standard",placeholder:"Country",InputProps:{...e.InputProps,startAdornment:(0,l.jsxs)(l.Fragment,{children:[l.jsx(x.Z,{position:"start",children:l.jsx(j.default,{})}),e.InputProps.startAdornment]})}})})}),Y=({onClear:e})=>l.jsx("div",{className:"filter-actions",children:l.jsx(b.default,{text:"Clear",onClick:e,className:"btn btn-search btn-filled apply"})}),U=["Banking","Energies","IT & Telecom","Transport","Pharmaceutical","Other"],B=["CDD","CDIC","FREELANCE"],V=["French","English","Spanish","Arabic","German"],H=["Entry level","Intermediate","Expert"],q=(e,t)=>{let[n,r]=(0,i.useState)({industry:!e&&!t,contract:!e&&!t,search:e||t,country:!1,language:!1,experience:!1}),a=(0,i.useCallback)(e=>{r(t=>({...t,[e]:!t[e]}))},[]);return(0,i.useEffect)(()=>{r(n=>({...n,industry:!e&&!t,contract:!e&&!t,search:e||t}))},[e,t]),{expandedSections:n,toggleSection:a}},G=({setFieldValue:e,values:t,pathname:n,setPageNumber:r,setSelectedFilters:a})=>{let s=(0,u.Z)(),l=(0,p.Z)(s.breakpoints.down("sm")),o=(0,p.Z)(s.breakpoints.down("md"));(0,i.useEffect)(()=>{let t=t=>{let n=t.detail?.preserveIndustry,r=t.detail?.preserveCountry;e("jobDescriptionLanguages",[]),e("levelOfExperience",[]),e("contractType",[]),e("keyWord",[]),n||e("industry",[]),r||e("country",[])};return window.addEventListener("filtersReset",t),()=>{window.removeEventListener("filtersReset",t)}},[e]);let c=(0,i.useCallback)(e=>{let t=window.scrollY||document.documentElement.scrollTop,r=new URLSearchParams(window.location.search);r.has("list")&&!e.has("list")&&e.set("list",r.get("list"));let a=`${n}?${e.toString()}`;window.history.replaceState({path:a},"",a);let s={};for(let[t,n]of e.entries())s[t]=n;window.dispatchEvent(new CustomEvent("filterChanged",{detail:{params:s,maintainScroll:!0,scrollPosition:t}})),window.scrollTo({top:t,behavior:"instant"})},[n]),d=(0,i.useCallback)((n,s)=>{let l=t[n]?.includes(s)?t[n]?.filter(e=>e!==s):[...t[n]||[],s];e(n,l);let i=new URLSearchParams(window.location.search);l.length>0?i.set(n,l.join(",")):i.delete(n);let o=new URLSearchParams(window.location.search);o.has("list")&&!i.has("list")&&i.set("list",o.get("list")),i.set("pageNumber",1),r(1),c(i),a(e=>{let t=e.filter(e=>e.category!==n);return l.length>0?[...t,...l.map(e=>({category:n,label:e}))]:t}),window.dispatchEvent(new CustomEvent("checkboxFilterChanged",{detail:{category:n,value:s,newValues:l,allValues:t,maintainScroll:!0,scrollPosition:window.scrollY||document.documentElement.scrollTop}}))},[t,e,r,a,c]);return{handleCheckboxChange:d,handleSearchChange:(0,i.useCallback)(n=>{let s=n.target.value,i=s?[s]:[];if(e("keyWord",i),l||o){let e=new URLSearchParams(window.location.search);i.length>0&&i[0].trim()?e.set("keyWord",i[0]):e.delete("keyWord");let n=new URLSearchParams(window.location.search);n.has("list")&&!e.has("list")&&e.set("list",n.get("list")),e.set("pageNumber",1),r(1),c(e),a(e=>{let t=e.filter(e=>"keyWord"!==e.category);return i.length>0&&i[0].trim()?[...t,{category:"keyWord",label:i[0]}]:t}),window.dispatchEvent(new CustomEvent("checkboxFilterChanged",{detail:{category:"keyWord",value:i[0]||"",newValues:i,allValues:t,maintainScroll:!0,scrollPosition:window.scrollY||document.documentElement.scrollTop}}))}},[e,l,o,r,a,c,t]),handleCountryChange:(0,i.useCallback)((n,s)=>{if(e("country",s),l||o){let e=new URLSearchParams(window.location.search);s&&s.trim()?e.set("country",s):e.delete("country");let n=new URLSearchParams(window.location.search);n.has("list")&&!e.has("list")&&e.set("list",n.get("list")),e.set("pageNumber",1),r(1),c(e),a(e=>{let t=e.filter(e=>"country"!==e.category);return s&&s.trim()?[...t,{category:"country",label:s}]:t}),window.dispatchEvent(new CustomEvent("checkboxFilterChanged",{detail:{category:"country",value:s||"",newValues:s?[s]:[],allValues:t,maintainScroll:!0,scrollPosition:window.scrollY||document.documentElement.scrollTop}}))}},[e,l,o,r,a,c,t]),handleClearFilters:(0,i.useCallback)(t=>{t&&t.preventDefault&&t.preventDefault();let r=window.scrollY||document.documentElement.scrollTop;window._isClearing=!0,e("jobDescriptionLanguages",[]),e("levelOfExperience",[]),e("industry",[]),e("contractType",[]),(l||o)&&(e("keyWord",[]),e("country",[]));let s=new URLSearchParams(window.location.search);s.delete("jobDescriptionLanguages"),s.delete("levelOfExperience"),s.delete("industry"),s.delete("contractType"),(l||o)&&(s.delete("keyWord"),s.delete("country"));let i=`${n}?${s.toString()}`;window.history.replaceState({path:i},"",i);let c={};for(let[e,t]of s.entries())c[e]=t;return window.dispatchEvent(new CustomEvent("filterChanged",{detail:{params:c,maintainScroll:!0,scrollPosition:r}})),a(e=>l||o?[]:e.filter(e=>"keyWord"===e.category||"country"===e.category)),window.scrollTo({top:r,behavior:"instant"}),window._isClearing=!1,!1},[e,a,n,l,o])}},J=({isOpen:e,onClose:t,setFieldValue:n,values:r,t:a,countries:s,setPageNumber:i,jobIndustry:o,setSelectedFilters:c})=>{let d=(0,u.Z)(),h=(0,p.Z)(d.breakpoints.down("sm")),m=(0,p.Z)(d.breakpoints.down("md")),y=(0,g.usePathname)(),{expandedSections:x,toggleSection:f}=q(h,m),{handleCheckboxChange:v,handleSearchChange:j,handleCountryChange:w,handleClearFilters:b}=G({setFieldValue:n,values:r,pathname:y,setPageNumber:i,setSelectedFilters:c});return l.jsx("div",{id:"filter-actions",className:`filter-popup ${e?"open":""}`,children:(0,l.jsxs)("div",{className:"filter-popup-content",children:[h&&l.jsx(R,{title:"Search",expanded:x.search,onChange:()=>f("search"),children:l.jsx(_,{value:r.keyWord,onChange:j,placeholder:a("Search")})}),h&&l.jsx(R,{title:"Country",expanded:x.country,onChange:()=>f("country"),children:l.jsx(z,{value:r.country,options:s,onChange:w})}),!o&&l.jsx(R,{title:"Industry",expanded:x.industry,onChange:()=>f("industry"),children:l.jsx(F,{options:U,values:r,category:"industry",onChange:v})}),l.jsx(R,{title:"Contract Type",expanded:x.contract,onChange:()=>f("contract"),children:l.jsx(F,{options:B,values:r,category:"contractType",onChange:v})}),l.jsx(R,{title:"Language",expanded:x.language,onChange:()=>f("language"),children:l.jsx(F,{options:V,values:r,category:"jobDescriptionLanguages",onChange:v})}),l.jsx(R,{title:"Level of Experience",expanded:x.experience,onChange:()=>f("experience"),children:l.jsx(F,{options:H,values:r,category:"levelOfExperience",onChange:v})}),l.jsx(Y,{onClear:b})]})})},X=({initialValues:e,isFilterOpen:t,setIsFilterOpen:n,t:r,jobLocation:a,jobIndustry:s,countries:o,setSelectedFilters:c,setPageNumber:u,handleSubmitFilter:p})=>{let h=(0,i.useRef)(null);return(0,i.useEffect)(()=>{let e=e=>{if(h.current){let{setFieldValue:t}=h.current,{keyWord:n,country:r,industry:a,contractType:s,levelOfExperience:l,jobDescriptionLanguages:i}=e.detail;n&&t("keyWord",Array.isArray(n)?n:[n]),r&&t("country",Array.isArray(r)?r:[r]),a&&t("industry",Array.isArray(a)?a:[a]),s&&t("contractType",Array.isArray(s)?s:[s]),l&&t("levelOfExperience",Array.isArray(l)?l:[l]),i&&t("jobDescriptionLanguages",Array.isArray(i)?i:[i])}};return window.addEventListener("searchPerformed",e),()=>{window.removeEventListener("searchPerformed",e)}},[]),l.jsx(d.default,{item:!0,lg:3,md:3,sm:12,xs:12,children:l.jsx(O.J9,{initialValues:e,enableReinitialize:"true",onSubmit:(e,t)=>p(e,t),innerRef:h,children:({setFieldValue:e,values:i})=>l.jsx(O.l0,{children:l.jsx(J,{isOpen:t,onClose:()=>n(!1),t:r,jobLocation:a,setFieldValue:e,jobIndustry:s,values:i,countries:o,setSelectedFilters:c,setPageNumber:u})})})})};var K=n(95746);function Q(){return(Q=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}let ee=e=>K.createElement("svg",Q({xmlns:"http://www.w3.org/2000/svg",width:21,height:21,fill:"none"},e),r||(r=K.createElement("g",{fill:"#0B3051",clipPath:"url(#GridIcon_svg__a)"},K.createElement("path",{d:"M1.5 1.5v6h6v-6zm7.5 6A1.5 1.5 0 0 1 7.5 9h-6A1.5 1.5 0 0 1 0 7.5v-6A1.5 1.5 0 0 1 1.5 0h6A1.5 1.5 0 0 1 9 1.5zM13.5 1.5v6h6v-6zm7.5 6A1.5 1.5 0 0 1 19.5 9h-6A1.5 1.5 0 0 1 12 7.5v-6A1.5 1.5 0 0 1 13.5 0h6A1.5 1.5 0 0 1 21 1.5zM1.5 13.5v6h6v-6zm7.5 6A1.5 1.5 0 0 1 7.5 21h-6A1.5 1.5 0 0 1 0 19.5v-6A1.5 1.5 0 0 1 1.5 12h6A1.5 1.5 0 0 1 9 13.5zM13.5 13.5v6h6v-6zm7.5 6a1.5 1.5 0 0 1-1.5 1.5h-6a1.5 1.5 0 0 1-1.5-1.5v-6a1.5 1.5 0 0 1 1.5-1.5h6a1.5 1.5 0 0 1 1.5 1.5z"}))),a||(a=K.createElement("defs",null,K.createElement("clipPath",{id:"GridIcon_svg__a"},K.createElement("path",{fill:"#fff",d:"M0 0h21v21H0z"})))));function et(){return(et=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}let en=e=>K.createElement("svg",et({xmlns:"http://www.w3.org/2000/svg",width:27,height:24,fill:"none"},e),s||(s=K.createElement("path",{fill:"#0B3051",d:"M5.633 4.365a2.17 2.17 0 0 1-2.16 2.18 2.17 2.17 0 0 1-2.148-1.959l-.012-.221.012-.224c.11-1.1 1.03-1.96 2.149-1.96l.219.013a2.173 2.173 0 0 1 1.94 2.17M23.75 3.273l.22.022a1.092 1.092 0 0 1 0 2.139l-.22.021H9.731a1.09 1.09 0 0 1 0-2.182zM5.633 12.001a2.17 2.17 0 0 1-2.16 2.181 2.17 2.17 0 0 1-2.148-1.96l-.012-.22.012-.224c.11-1.1 1.03-1.96 2.149-1.96l.219.013A2.173 2.173 0 0 1 5.633 12M23.75 10.908l.22.022a1.092 1.092 0 0 1 0 2.139l-.22.021H9.731a1.09 1.09 0 0 1 0-2.182zM5.633 19.638a2.17 2.17 0 0 1-2.16 2.18 2.17 2.17 0 0 1-2.148-1.959l-.012-.22.012-.225c.11-1.1 1.03-1.959 2.149-1.959l.219.013a2.173 2.173 0 0 1 1.94 2.17M23.75 18.545l.22.021a1.092 1.092 0 0 1 0 2.14l-.22.02H9.731a1.09 1.09 0 0 1 0-2.181z"})));function er({language:e,initialOpportunities:t,searchParams:n,countries:r,typeCategory:a,jobIndustry:s,countryName:g,industryName:y,jobLocation:x,initialListView:f}){let{t:v}=(0,o.$G)(),j=(0,u.Z)(),w=(0,p.Z)(j.breakpoints.down("sm")),[N,E]=(0,i.useState)(t||null),[S,A]=(0,i.useState)(!1),L=N?.totalOpportunities,O=(0,i.useCallback)(async t=>{try{A(!0);let n={language:e,pageSize:10,pageNumber:t.pageNumber||1,visibility:"Public",keyWord:t.keyWord||"",levelOfExperience:t.levelOfExperience||"",contractType:t.contractType||"",jobDescriptionLanguages:t.jobDescriptionLanguages||"",opportunityType:t.opportunityType||""};s&&y?n.industry=y:t.industry?n.industry=t.industry.replace(/\bIT\b/g,"It").replace(/\bOther\b/g,"Others"):n.industry="",x&&g?n.country=g:t.country?n.country=t.country:n.country="";let r=new Promise((e,t)=>setTimeout(()=>t(Error("Request timeout")),1e4)),a=await Promise.race([(0,h.fH)(n),r]);E(a),A(!1)}catch(e){console.error("Error fetching opportunities:",e),A(!1)}},[e,s,y,x,g]),{keyWord:T,setKeyWord:Z,country:D,setCountry:I,pageNumber:W,setPageNumber:R,setIndustry:$,setContractType:M,setLevelOfExperience:F,setJobDescriptionLanguages:_,selectedFilters:z,setSelectedFilters:Y,isFilterOpen:U,setIsFilterOpen:B,handleSearchChange:V,handleSearchClick:H,resetSearch:q,handlePageChange:G,searchParamsContent:J,isList:K,setIsList:Q,handleViewModeChange:et}=m(O,e,f),er={industry:n?.industry&&n.industry.split(",").map(e=>e.trim()).length>0?decodeURIComponent(n?.industry).split(","):"",contractType:n?.contractType&&n.contractType.split(",").map(e=>e.trim()).length>0?decodeURIComponent(n?.contractType).split(","):"",jobDescriptionLanguages:n?.jobDescriptionLanguages&&n.jobDescriptionLanguages.split(",").map(e=>e.trim()).length>0?decodeURIComponent(n?.jobDescriptionLanguages).split(","):"",levelOfExperience:n?.levelOfExperience&&n.levelOfExperience.split(",").map(e=>e.trim()).length>0?decodeURIComponent(n?.levelOfExperience).split(","):"",keyWord:n?.keyWord&&n.keyWord.split(",").map(e=>e.trim()).length>0?decodeURIComponent(n?.keyWord).split(","):"",country:n?.country&&n.country.split(",").map(e=>e.trim()).length>0?decodeURIComponent(n?.country).split(","):""};return(0,l.jsxs)(l.Fragment,{children:[a&&l.jsx("div",{id:"search-bar-opportunities",children:(0,l.jsxs)(c.default,{className:"custom-max-width",children:[l.jsx("p",{className:"sub-heading text-banking",children:v("global:findCareer")}),!w&&l.jsx(C,{keyWord:T,country:D,countries:r,handleSearchChange:V,setCountry:I,resetSearch:e=>q(e,{preserveIndustry:s,industryName:y,preserveCountry:x,countryName:g}),handleSearchClick:H,setPageNumber:R,jobLocation:x,jobIndustry:s,industryName:y,countryName:g,t:v}),l.jsx(k,{selectedFilters:z})]})}),l.jsx("div",{id:"opportunities",children:(0,l.jsxs)(c.default,{className:"custom-max-width",children:[(0,l.jsxs)("div",{className:"display",children:[(0,l.jsxs)("div",{className:"opportunity-chip",children:[(0,l.jsxs)("p",{className:"sub-heading text-banking",children:["Opportunities"," ",l.jsx("span",{className:"opportunities-nbr",children:L})]})," "]}),!w&&(0,l.jsxs)("div",{className:"grid-list-buttons",children:[l.jsx(b.default,{icon:l.jsx(ee,{}),className:`btn btn-ghost ${K?"":"active"}`,onClick:()=>et(!1)}),l.jsx(b.default,{icon:l.jsx(en,{}),className:`btn btn-ghost ${K?"active":""}`,onClick:()=>et(!0)})]})]}),(0,l.jsxs)(d.default,{className:"container opportunity-card",container:!0,columnSpacing:2,children:[l.jsx(X,{initialValues:er,isFilterOpen:U,setIsFilterOpen:B,t:v,jobLocation:x,jobIndustry:s,countries:r,setSelectedFilters:Y,setPageNumber:R,handleSubmitFilter:(e,{setFieldValue:t})=>{let n=new URLSearchParams(J);e.industry?.length&&(n.set("industry",e.industry.join(",")),$(e.industry.join(","))),e.contractType?.length&&(n.set("contractType",e.contractType.join(",")),M(e.contractType.join(","))),e.levelOfExperience?.length&&(n.set("levelOfExperience",e.levelOfExperience.join(",")),F(e.levelOfExperience.join(","))),e.jobDescriptionLanguages?.length&&(n.set("jobDescriptionLanguages",e.jobDescriptionLanguages.join(",")),_(e.jobDescriptionLanguages.join(","))),e.keyWord?.length&&(n.set("keyWord",e.keyWord.join(",")),Z(e.keyWord.join(","))),e.country&&(n.set("country",e.country),I(e.country));let r=window.scrollY||document.documentElement.scrollTop,a=`${window.location.pathname}?${n.toString()}`;window.history.replaceState({path:a},"",a);let s={};for(let[e,t]of n.entries())s[e]=t;O(s);let l=[];e.industry?.length&&l.push(...e.industry.map(e=>({category:"industry",label:e}))),e.contractType?.length&&l.push(...e.contractType.map(e=>({category:"contractType",label:e}))),e.levelOfExperience?.length&&l.push(...e.levelOfExperience.map(e=>({category:"levelOfExperience",label:e}))),e.jobDescriptionLanguages?.length&&l.push(...e.jobDescriptionLanguages.map(e=>({category:"jobDescriptionLanguages",label:e}))),e.country&&l.push(...e.country.map(e=>({category:"country",label:e}))),e.keyWord&&l.push(...e.keyWord.map(e=>({category:"keyWord",label:e}))),Y(l),t("filters",l.map(e=>e.label)),window.scrollTo({top:r,behavior:"instant"})}}),l.jsx(P,{opportunitiesData:N,language:e,pageNumber:W,handlePageChange:G,searchParamsContent:J,t:v,isList:K,isLoading:S})]})]})})]})}},91345:(e,t,n)=>{n.d(t,{default:()=>Z});var r=n(10326),a=n(23743),s=n(88441),l=n(75632),i=n(90434),o=n(17577),c=n(31190),d=n(16027),u=n(57967),p=n.n(u);n(38932);var h=n(55612),g=n.n(h),m=n(19191),y=n(52210),x=n(15082),f=n(28236),v=n(18970),j=n(90397),w=n(94474),b=n(75742),C=n(30088),N=n(53930),E=n(57201),k=n(22304),S=n(5248),A=n(86184),L=n(97980),P=n(70580),O=n(88065);let T=function({opportunity:e,language:t}){let{t:n,i18n:l}=(0,y.$G)();g().registerLocale(m);let u=(0,a.Z)(),{user:h}=(0,k.Z)(),[T,Z]=(0,o.useState)(!1),[D,I]=(0,o.useState)(!1),W=async e=>{try{await P.yX.delete(`/favourite/${e}`,{data:{type:"opportunity"}}),_(!1)}catch(e){}},R=async()=>{try{await W(D)}catch(e){}Z(!1)};p().locale(l.language||"en");let $=(0,s.Z)(u.breakpoints.down("sm")),M=(0,A.UJ)(),[F,_]=(0,o.useState)(!1),z=e=>{window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"opportunity_view",button_id:"my_button"}),setTimeout(()=>{window.location.href=e},300)},Y=n("createOpportunity:summary"),U=RegExp(`<strong>${Y}:</strong><br>([\\s\\S]*?)(?=<br>)`,"i"),B=e?.versions[l.language]?.jobDescription?.match(U)?.[1]?.trim()??"";return(0,r.jsxs)("div",{className:"button-pointer",onClick:()=>{$&&z(`/${L.Bi.opportunities.route}/${e?.versions[t]?.url}`)},children:[r.jsx(v.Z,{}),(0,r.jsxs)(d.default,{className:"container opportunity-item",container:!0,spacing:0,children:[(0,r.jsxs)(d.default,{container:!0,spacing:0,className:"flex-item row",children:[r.jsx(O.Z,{title:e?.versions?.[t]?.title||e?.title,child:r.jsx("a",{href:`/${L.Bi.opportunities.route}/${e?.versions?.[t]?.url||e?.url}`,className:"btn p-0 job-title",children:(0,f.rZ)(e?.versions?.[t]?.title||e?.title,80)})}),(0,r.jsxs)("div",{className:"flex-item",children:[(0,f.f8)(e?.industry)?r.jsx(i.default,{style:{textDecoration:"none"},href:`/${L.Bi.jobCategory.route}/${(0,f.Gc)(e?.industry)}`,children:(0,r.jsxs)("p",{className:`job-industry border ${(0,f.jX)(e?.industry)}`,children:[(0,f.y9)(e?.industry)," ",(0,f.sC)(e?.industry)]})}):null,!$&&(!h||h?.roles?.includes(S.uU.CANDIDATE))?r.jsx(x.default,{icon:r.jsx(E.Z,{className:`${F?"btn-filled-yellow":""}`}),onClick:F?()=>{I(e?._id),Z(!0)}:()=>{h?M.mutate({id:e?._id,title:e?.versions[t]?.title,typeOfFavourite:"opportunity"},{onSuccess:()=>{_(!0)}}):c.Am.warning("Login or create account to save opportunity.")},className:"btn btn-ghost bookmark"}):r.jsx("div",{}),$&&(!h||h?.roles?.includes(S.uU.CANDIDATE))&&r.jsx(x.default,{icon:r.jsx(E.Z,{className:`${F?"btn-filled-yellow ":""}`}),className:"btn btn-ghost bookmark"})]})]}),(0,r.jsxs)(d.default,{container:!0,spacing:0,className:"flex-item margin-section-item",children:[(0,r.jsxs)("p",{className:"job-ref",children:["Ref: ",e?.reference]}),(0,r.jsxs)("a",{className:"location",href:`/${L.Bi.jobLocation.route}/${e?.country.toLowerCase()}`,children:[r.jsx(N.Z,{}),r.jsx("p",{className:"location-text",children:e?.country})]})]}),r.jsx(d.default,{container:!0,spacing:0,className:"flex-item margin-section-item",children:r.jsx("div",{className:"job-description",dangerouslySetInnerHTML:{__html:B}})}),(0,r.jsxs)(d.default,{container:!0,spacing:0,className:"flex-item row",children:[r.jsx("div",{className:"flex-apply",children:(0,r.jsxs)("div",{className:"job-contrat-time",children:[(0,r.jsxs)("p",{className:"job-contract",children:[r.jsx(C.default,{}),e?.contractType||"Agreement"]}),(0,r.jsxs)("p",{className:"job-deadline",children:[r.jsx(b.default,{}),e?.dateOfExpiration?(0,f.fm)(p()(e?.dateOfExpiration).format("DD MMMM YYYY")):"N/A"]}),(0,r.jsxs)("p",{className:"job-time",children:[r.jsx(w.default,{}),e?.versions[t]?.createdAt?(0,f.fm)(p()(e?.versions[t]?.createdAt).format("DD MMMM YYYY")):"N/A"]})]})}),r.jsx("div",{className:"item-btns",children:r.jsx(x.default,{text:n("global:applyNow"),className:"btn btn-search btn-filled apply",onClick:()=>z(`/${L.Bi.opportunities.route}/${e?.versions[t]?.url}`)})})]})]},e?._id),r.jsx(j.Z,{open:T,message:n("messages:supprimeropportunityfavoris"),onClose:()=>{Z(!1)},onConfirm:R})]})};function Z({key:e,opportunity:t,language:n,isList:i}){let o=(0,a.Z)(),c=(0,s.Z)(o.breakpoints.down("sm"));return i&&!c?r.jsx(T,{opportunity:t,language:n},e):r.jsx(l.Z,{opportunity:t,language:n},e)}},94034:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(19510),a=n(55920),s=n(70064);let l=function({bannerImg:e,height:t,title:n,IconImg:l,description:i,subtitle:o,bottomChildren:c,opportunitysubTitle:d,centerValue:u,altImg:p,topChildren:h,isEvent:g,title2:m,titleHighlight:y,bannerImgDynamic:x,link:f,linkTitle:v,highlights:j}){return(0,r.jsxs)("div",{id:"banner-component",className:!0===u?"center-banner":"",style:{backgroundImage:e?.src?`url(${e.src})`:x?`url(${x})`:"none",height:t||"auto"},children:[p&&r.jsx("img",{width:0,height:0,alt:p,src:"",style:{display:"none"},loading:"lazy"}),(0,r.jsxs)(a.Z,{className:"custom-max-width",children:[h&&h,l&&r.jsx("img",{src:l.src}),g?r.jsx(r.Fragment,{children:j&&j.length>0?(0,r.jsxs)("h1",{className:"heading-h1 text-white",children:[" ",(0,s.q1)(y,j)," "]}):(0,r.jsxs)(r.Fragment,{children:[" ",(0,r.jsxs)("h1",{className:"heading-h1 text-white",children:[(0,r.jsxs)("span",{className:"text-yellow",children:[n," "]}),y,m]}),r.jsx("p",{className:"sub-heading text-slide text-white  ",children:o})]})}):(0,r.jsxs)(r.Fragment,{children:[" ",r.jsx("h1",{className:"heading-h1 text-white",children:n||"Services We Offer"}),r.jsx("p",{className:"sub-heading text-slide text-white  ",children:o}),r.jsx("p",{className:"sub-heading text-slide text-white  ",children:d}),r.jsx("p",{className:"sub-heading text-slide text-white  ",children:i||null}),f&&r.jsx("a",{href:f,style:{textDecoration:"none",width:"fit-content"},className:"btn btn-filled ",children:v})]}),c&&c]})]})}},36522:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\features\opportunity\components\opportunityFrontOffice\OpportunityCard.jsx#default`)}};