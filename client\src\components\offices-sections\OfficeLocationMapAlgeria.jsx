import { Contain<PERSON>, <PERSON><PERSON>, <PERSON> } from "@mui/material";
import CustomButton from "../ui/CustomButton";
import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";
import { websiteRoutesList } from "@/helpers/routesList";

function OfficeLocationMapAlgeria({ t }) {
  return (
    <Container id="office-location-map" className="custom-max-width">
      <Grid
        className="container"
        justifyContent="space-between"
        container
        spacing={0}
      >
        <Grid item xs={12} sm={6}>
          <div className="content">
            <p className="heading-h2 text-white">
              {t("Algeria:officeLocation1:label")}
            </p>
            <p className="sub-heading text-white">
              {t("Algeria:officeLocation1:title")}
            </p>
            <div>
              <p className="paragraph text-white" key={"tn"}>
                <span>
                  <SvglocationPin />
                </span>
                {t("Algeria:officeLocation1:address")}
              </p>
              <p className="paragraph text-white">
                <span>
                  <SvgcallUs />
                </span>
                {t("Algeria:officeLocation1:tel1")}
                <br /> {t("Algeria:officeLocation1:tel2")}
              </p>

              <p className="paragraph text-white">
                <span>
                  <Svgemail />
                </span>
                {t("Algeria:officeLocation1:mail")}
              </p>
            </div>
            <Link
              href={`#service-page-form`}
              className={"btn btn-outlined white"}
            >
              {t("Tunisia:officeLocation:talk")}
            </Link>
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <div className="map-frame">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d25577.502753053846!2d3.038734!3d36.742062!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x128fad8ab92e9313%3A0x7228405fce638a07!2sPentabell%20Alg%C3%A9rie-%20Cabinet%20de%20Recrutement%2C%20Portage%20Salarial%20et%20Assistance%20Technique!5e0!3m2!1sfr!2sus!4v1728577693998!5m2!1sfr!2sus"
              allowfullscreen=""
              loading="lazy"
              referrerpolicy="no-referrer-when-downgrade"
            ></iframe>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default OfficeLocationMapAlgeria;
