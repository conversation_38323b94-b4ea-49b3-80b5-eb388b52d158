import { Request, Response, NextFunction } from 'express';

import { FileI } from '@/apis/storage/files.interface';
import FilesModel from '../apis/storage/files.model';
import { MESSAGES } from '@/utils/helpers/messages';
export async function fileExistMiddleware(req: Request, res: Response, next: NextFunction) {
    const uuid = req.params.filename;
    const findFile: FileI | null = await FilesModel.findOne({ uuid: uuid });
    if (findFile) return res.status(409).json({ message: MESSAGES.FILE.ALREADY_EXISTS });
    next();
}
