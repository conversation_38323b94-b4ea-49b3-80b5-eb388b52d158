"use client";
import { useEffect, useState } from "react";
import moment from "moment";
import { CardMedia, Container, Grid } from "@mui/material";
import { useTheme, useMediaQuery } from "@mui/material";
import { toast } from "react-toastify";
import "moment/locale/fr";
import { useTranslation } from "react-i18next";
import Link from "next/link";

import ArticleHeadingContent from "./ArticleHeadingContent";
import {
  useGetArticleByUrlANDlanguage,
  useGetComments,
} from "../hooks/blog.hook";
import ArticleContent from "./ArticleContent";
import { capitalizeFirstLetter } from "../../../utils/functions";
import SvgTime from "../../../assets/images/icons/time.svg";
import CustomButton from "@/components/ui/CustomButton";
import CommentsListByBlog from "./CommentsListByBlog";
import CreateBlogComment from "./CreateBlogComment";
import SvgFacebook from "../../../assets/images/icons/facebook.svg";
import SvgLinkedin from "../../../assets/images/icons/linkedin.svg";
import SvgX from "../../../assets/images/icons/x.svg";
import SvgArrowRight from "../../../assets/images/icons/arrowRight.svg";
import SvgArrow from "@/assets/images/icons/arrow.svg";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import { API_URLS } from "@/utils/urls";
import { axiosGetJsonSSR } from "@/config/axios";
import NewsletterSubscription from "@/components/sections/NewsletterSubscription";
import { useAddToFavourite } from "../../opportunity/hooks/opportunity.hooks";
import { Role } from "@/utils/constants";
import { websiteRoutesList } from "@/helpers/routesList";

function BlogPage({ article, language, url }) {
  const theme = useTheme();
  const { user } = useCurrentUser();
  const { i18n } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const useAddTofavouriteHook = useAddToFavourite();

  moment.locale(i18n.language || "en");

  const [headings, setHeadings] = useState([]);
  const [modifiedHtmlContent, setModifiedHtmlContent] = useState(
    article?.content
  );
  const [relatedArticles, setRelatedArticles] = useState([]);

  const getArticle = useGetArticleByUrlANDlanguage(
    { language, urlArticle: url },
    { enabled: !!url }
  );

  useEffect(() => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(article?.content, "text/html");
    const extractedHeadings = [];

    Array.from(doc.querySelectorAll("h2, h3, h4, h5, h6")).forEach(
      (heading) => {
        const id = heading.innerText
          .toLowerCase()
          .replace(/\s+/g, "-")
          .replace(/[^a-z0-9\-]/g, "");
        heading.id = id;
        extractedHeadings.push({
          tagName: heading.tagName.toLowerCase(),
          content: heading.innerText,
          id,
        });
      }
    );

    setHeadings(extractedHeadings);
    setModifiedHtmlContent(doc.body.innerHTML);
  }, [article?.content]);

  const articleId = getArticle?.data?._id;

  const handleSaveClick = () => {
    if (!user) {
      toast.warning("Login or create account to save article.");
    } else {
      useAddTofavouriteHook.mutate(
        {
          id: idArticle,
          title: article?.title,
          typeOfFavourite: "article",
        },
        {
          onSuccess: () => {
            // setIsSaved(true);
          },
        }
      );
    }
  };
  const {
    data: commentsData,
    isLoading: commentsLoading,
    refetch: commentsRefetch,
  } = useGetComments(
    {
      articleId,
      pageNumber: 1,
      pageSize: 6,
      paginated: true,
    },
    { enabled: !!articleId }
  );

  // const categorySlugs = article.categories.map((cat) => cat.url).join("/");
  const sahredUrl = `${process.env.NEXT_PUBLIC_FRONTEND_URL}/blog/${url}`;
  // const shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;

  useEffect(() => {
    const fetchRelatedArticles = async () => {
      if (article?.categories?.length > 0) {
        const categorySlugs = article?.categories
          .map((cat) => cat.url)
          .join("/");

        // const response = await fetch(
        //   `${baseURL}/${API_URLS.articles}/${language}/blog/category/${categorySlugs}`
        // );

        if (!categorySlugs) return;

        try {
          const response = await axiosGetJsonSSR.get(
            `${API_URLS.articles}/${language}/blog/category/${categorySlugs}`
          );

          if (response.status === 200) {
            const data = await response.data;
            const fetchedArticles = data?.articles?.map((item) => ({
              id: item._id,
              title: item.versions[0]?.title || "Titre non disponible",
              picture: item.versions[0]?.image || "",
              description:
                item.versions[0]?.metaDescription ||
                "Description non disponible",
              slug: item.versions[0]?.url || "",
            }));

            setRelatedArticles(fetchedArticles);
          } else {
            if (process.env.NODE_ENV === "dev")
              console.error("Failed to fetch related articles");
          }
        } catch (error) {
          if (error.status !== 500 && process.env.NODE_ENV === "dev")
            console.error("Failed to fetch related articles", error);
        }
      }
    };

    fetchRelatedArticles();
  }, [article?.categories, language]);

  return (
    <div id="one-blog-details">
      <div
        id="banner-component"
        style={{
          backgroundImage: `url(${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${
            article?.picture || article?.image
          })`,
          backgroundImage: `linear-gradient(to left, rgba(11, 48, 81, 0.6), rgba(11, 48, 81, 0.6)), url(${
            process.env.NEXT_PUBLIC_BASE_API_URL
          }/files/${article?.picture || article?.image})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          height: "70vh",
        }}
      >
        <Container>
          {article?.categories?.length > 0 && (
            <div className="categories-list">
              {article.categories.map(
                (category) =>
                  category?.url && (
                    <span className="category light" key={category.url}>
                      <a
                        href={`${
                          language === "en"
                            ? `/blog/category/${category.url}`
                            : `/${language}/blog/category/${category.url}`
                        }/`}
                      >
                        {category.name}
                      </a>
                    </span>
                  )
              )}
            </div>
          )}
          <p className="heading-h1 text-white">{article?.title}</p>
          <p className="sub-heading text-white date ">
            <SvgTime />{" "}
            {article?.publishDate
              ? capitalizeFirstLetter(
                  moment(article?.publishDate).format("LLL")
                )
              : ""}
          </p>{" "}
          {(!user || user?.roles?.includes(Role.CANDIDATE)) && (
            <CustomButton
              text={"Save"}
              onClick={handleSaveClick}
              className={"btn btn-outlined white"}
            />
          )}
        </Container>
      </div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            itemListElement: [
              {
                "@type": "ListItem",
                position: 1,
                item: {
                  "@id":
                    language === "en"
                      ? `https://www.pentabell.com/blog/`
                      : `https://www.pentabell.com/${language}/blog/`,
                  name: "Blog",
                },
              },
              article?.categories[0].url && {
                "@type": "ListItem",
                position: 2,
                item: {
                  "@id":
                    language === "en"
                      ? `https://www.pentabell.com/blog/category/${article?.categories[0].url}/`
                      : `https://www.pentabell.com/${language}/blog/category/${article?.categories[0].url}/`,
                  name: article?.categories[0].name,
                },
              },
            ],
          }),
        }}
      />
      <Container className="custom-max-width">
        <div className="categories-path">
          <a
            locale={language === "en" ? "en" : "fr"}
            href={`${language === "en" ? `/blog` : `/${language}/blog`}/`}
            className="link"
          >
            Blog
          </a>

          {article?.categories?.length > 0 && (
            <>
              <SvgArrowRight />
              {article?.categories[0].url && (
                <a
                  className="link"
                  href={`${
                    language === "en"
                      ? `/blog/category/${article?.categories[0].url}`
                      : `/${language}/blog/category/${article?.categories[0].url}`
                  }/`}
                >
                  {article?.categories[0].name}
                </a>
              )}
            </>
          )}
        </div>

        <Grid className="container" container columnSpacing={2}>
          <Grid item xs={12} sm={8}>
            <ArticleHeadingContent headings={headings} />
            <ArticleContent htmlContent={modifiedHtmlContent} />
            {isMobile && (
              <div id="social-media-share">
                <p className="title">Share article on </p>
                <div>
                  <CustomButton
                    icon={<SvgFacebook />}
                    ariaLabel="Share on Facebook"
                    className={"btn btn-ghost"}
                    onClick={() =>
                      window.open(
                        `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                          sahredUrl
                        )}`,
                        "_blank"
                      )
                    }
                  />
                  <CustomButton
                    ariaLabel="Share on Twitter"
                    onClick={() =>
                      window.open(
                        `https://twitter.com/intent/tweet?url=${encodeURIComponent(
                          sahredUrl
                        )}`,
                        "_blank"
                      )
                    }
                    icon={<SvgX />}
                    className={"btn btn-ghost"}
                  />
                  <CustomButton
                  ariaLabel="Share on LinkedIn"
                    onClick={() =>
                      window.open(
                        `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
                          sahredUrl
                        )}`,
                        "_blank"
                      )
                    }
                    icon={<SvgLinkedin />}
                    className={"btn btn-ghost"}
                  />
                </div>
              </div>
            )}
            <CreateBlogComment
              refetch={commentsRefetch}
              articleId={articleId}
              user={user}
              language={language}
            />
            <CommentsListByBlog
              articleId={articleId}
              data={commentsData}
              isLoading={commentsLoading}
              refetch={commentsRefetch}
              user={user}
              language={language}
            />
          </Grid>
          <Grid item xs={12} sm={4} className="sidebar">
            {!isMobile && (
              <div id="social-media-share">
                <p className="title">Share article on</p>
                <div>
                  <CustomButton
                    icon={<SvgFacebook />}
                    className={"btn btn-ghost"}
                    onClick={() =>
                      window.open(
                        `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                          sahredUrl
                        )}`,
                        "_blank"
                      )
                    }
                    ariaLabel="Share on Facebook"
                  />
                  <CustomButton
                    onClick={() =>
                      window.open(
                        `https://twitter.com/intent/tweet?url=${encodeURIComponent(
                          sahredUrl
                        )}`,
                        "_blank"
                      )
                    }
                    icon={<SvgX />}
                    className={"btn btn-ghost"}
                    ariaLabel="Share on Twitter"
                  />
                  <CustomButton
                    onClick={() =>
                      window.open(
                        `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
                          sahredUrl
                        )}`,
                        "_blank"
                      )
                    }
                    icon={<SvgLinkedin />}
                    className={"btn btn-ghost"}
                    ariaLabel="Share on LinkedIn"
                  />
                </div>
              </div>
            )}
            <div className="section">
              <NewsletterSubscription />
            </div>
            <div className="section">
              <p className="title">Related Articles</p>
              <p className="description">Explore Our Blog Content</p>
              {relatedArticles?.length > 0 ? (
                relatedArticles
                  ?.filter((relatedArticle) => relatedArticle?.id !== articleId)
                  .slice(0, 3)
                  .map((relatedArticle) => (
                    <div className="last-blog" key={relatedArticle?.id}>
                      <Link
                        locale={language === "en" ? "en" : "fr"}
                        href={`${
                          language === "en"
                            ? `/blog/${relatedArticle?.slug}`
                            : `/${language}/blog/${relatedArticle?.slug}`
                        }/`}
                      >
                        <CardMedia
                          component="img"
                          image={`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${relatedArticle?.picture}`}
                          title={relatedArticle.title}
                          alt={relatedArticle.title}
                          className="card-media"
                        />
                      </Link>
                      <div className="card-text">
                        <p>{relatedArticle.title}</p>
                        <CustomButton
                          text={"Explore"}
                          icon={<SvgArrow />}
                          className={"btn btn-ghost white"}
                          link={`/${websiteRoutesList.blog.route}/${relatedArticle.slug}`}
                        />
                      </div>
                    </div>
                  ))
              ) : (
                <p>No related articles found</p>
              )}
            </div>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default BlogPage;
