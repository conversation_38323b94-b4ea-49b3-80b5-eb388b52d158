exports.id=2683,exports.ids=[2683],exports.modules={5784:(e,t,s)=>{var n={"./af.json":7571,"./am.json":25031,"./ar.json":98133,"./az.json":33255,"./be.json":598,"./bg.json":81858,"./bn.json":51394,"./br.json":16570,"./bs.json":60169,"./ca.json":95684,"./cs.json":29123,"./cy.json":79579,"./da.json":32091,"./de.json":37257,"./dv.json":84403,"./el.json":20699,"./en.json":19191,"./es.json":77831,"./et.json":59952,"./eu.json":3622,"./fa.json":30912,"./fi.json":23972,"./fr.json":93474,"./ga.json":93327,"./gl.json":66623,"./ha.json":43008,"./he.json":1119,"./hi.json":43182,"./hr.json":17635,"./hu.json":2009,"./hy.json":78635,"./id.json":8374,"./is.json":30858,"./it.json":54200,"./ja.json":3140,"./ka.json":40091,"./kk.json":30459,"./km.json":44902,"./ko.json":52706,"./ku.json":21489,"./ky.json":59764,"./lt.json":17079,"./lv.json":25865,"./mk.json":93520,"./ml.json":16564,"./mn.json":97211,"./mr.json":48785,"./ms.json":77396,"./mt.json":38636,"./nb.json":39263,"./nl.json":83533,"./nn.json":8103,"./no.json":7156,"./pl.json":25401,"./ps.json":94556,"./pt.json":50828,"./ro.json":17573,"./ru.json":67296,"./sd.json":58613,"./sk.json":64757,"./sl.json":21687,"./so.json":40132,"./sq.json":70592,"./sr.json":98636,"./sv.json":11447,"./sw.json":98602,"./ta.json":22589,"./tg.json":75019,"./th.json":75775,"./tk.json":66202,"./tr.json":42236,"./tt.json":73666,"./ug.json":53010,"./uk.json":86537,"./ur.json":88800,"./uz.json":78968,"./vi.json":23132,"./zh.json":26118};function a(e){return s(o(e))}function o(e){if(!s.o(n,e)){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}a.keys=function(){return Object.keys(n)},a.resolve=o,e.exports=a,a.id=5784},30088:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var n,a=s(95746);function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)({}).hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e}).apply(null,arguments)}let r=e=>a.createElement("svg",o({xmlns:"http://www.w3.org/2000/svg",width:18,height:18,fill:"none",viewBox:"0 0 14 14"},e),n||(n=a.createElement("path",{fill:"#798BA3",fillRule:"evenodd",d:"M2.143.978C2.503.618 2.99.417 3.5.417h4.666a.75.75 0 0 1 .53.22l3.5 3.5c.141.14.22.331.22.53v7a1.917 1.917 0 0 1-1.916 1.917h-7a1.917 1.917 0 0 1-1.917-1.917V2.334c0-.509.202-.996.561-1.356m1.356.939a.417.417 0 0 0-.417.417v9.333a.417.417 0 0 0 .417.417h7a.416.416 0 0 0 .416-.417v-6.25h-2.75a.75.75 0 0 1-.75-.75v-2.75zm5.416 1.06.94.94h-.94zm-5 2.273a.75.75 0 0 1 .75-.75h1.167a.75.75 0 1 1 0 1.5H4.665a.75.75 0 0 1-.75-.75m0 2.334a.75.75 0 0 1 .75-.75h4.667a.75.75 0 0 1 0 1.5H4.665a.75.75 0 0 1-.75-.75m0 2.333a.75.75 0 0 1 .75-.75h4.667a.75.75 0 0 1 0 1.5H4.665a.75.75 0 0 1-.75-.75",clipRule:"evenodd"})))},57201:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});var n,a=s(95746);function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)({}).hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e}).apply(null,arguments)}let r=e=>a.createElement("svg",o({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),n||(n=a.createElement("path",{stroke:"#1D5A9F",strokeWidth:1.5,d:"m12.398 17.396-.398-.25-.398.25-6.852 4.296V3A.25.25 0 0 1 5 2.75h14a.25.25 0 0 1 .25.25v18.692zm-8.03 4.535Z"})))},75742:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var n,a=s(95746);function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)({}).hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e}).apply(null,arguments)}let r=e=>a.createElement("svg",o({xmlns:"http://www.w3.org/2000/svg",width:10,height:12,fill:"none"},e),n||(n=a.createElement("path",{fill:"#fff",fillRule:"evenodd",d:"M3.165 5.364a8 8 0 0 1-.59-.544c-.6-.62-1.076-1.378-1.076-2.32V1.333H.332V.167h9.333v1.166H8.5V2.5c0 .942-.476 1.7-1.076 2.32q-.275.281-.59.544c-.264.22-.543.432-.825.636.282.204.561.415.824.635.21.176.41.357.591.545.6.62 1.076 1.378 1.076 2.32v1.166h1.166v1.167H.332v-1.167h1.167V9.5c0-.942.476-1.7 1.075-2.32q.275-.281.59-.545c.264-.22.543-.431.825-.635-.282-.204-.56-.415-.824-.636m.128-1.24-.599.58M5 5.287c-.61-.43-1.162-.84-1.586-1.279-.495-.51-.748-.992-.748-1.508V1.333h4.667V2.5c0 .516-.253.998-.747 1.508-.424.438-.977.848-1.586 1.28m2.333 5.38H2.665V9.5c0-.516.253-.998.748-1.509.424-.438.976-.848 1.586-1.278.61.43 1.162.84 1.586 1.278.494.511.747.993.747 1.509z",clipRule:"evenodd"})))},53930:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});var n,a=s(95746);function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)({}).hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e}).apply(null,arguments)}let r=e=>a.createElement("svg",o({xmlns:"http://www.w3.org/2000/svg",width:10,height:12,fill:"none"},e),n||(n=a.createElement("path",{fill:"#1D5A9F",fillRule:"evenodd",d:"M5 1.256c-.995 0-1.948.397-2.652 1.103A3.78 3.78 0 0 0 1.25 5.023c0 1.457.947 2.891 2.016 4.025A14 14 0 0 0 5 10.598q.13-.096.294-.228c.394-.316.918-.769 1.44-1.322C7.803 7.914 8.75 6.48 8.75 5.023c0-.999-.395-1.957-1.098-2.664A3.74 3.74 0 0 0 5 1.256m0 10.116-.347.522h-.002l-.003-.003-.012-.008a5 5 0 0 1-.19-.134A15.437 15.437 0 0 1 2.36 9.912C1.24 8.724 0 6.984 0 5.022c0-1.332.527-2.61 1.464-3.552A5 5 0 0 1 5 0c1.326 0 2.598.53 3.536 1.471A5.04 5.04 0 0 1 10 5.023c0 1.962-1.24 3.702-2.359 4.888a15.4 15.4 0 0 1-2.277 1.972l-.012.008-.003.002-.002.001zm0 0 .347.523a.62.62 0 0 1-.694 0zm0-7.186a.835.835 0 0 0-.833.837c0 .463.373.837.833.837s.833-.374.833-.837A.835.835 0 0 0 5 4.186m-2.083.837c0-1.156.932-2.093 2.083-2.093 1.15 0 2.083.937 2.083 2.093A2.09 2.09 0 0 1 5 7.116a2.09 2.09 0 0 1-2.083-2.093",clipRule:"evenodd"})))},88065:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var n=s(10326),a=s(83708);let o=function({child:e,title:t,placement:s="bottom"}){return n.jsx(a.Z,{title:t,placement:s,componentsProps:{tooltip:{sx:{color:"#798BA3",backgroundColor:"white",fontWeight:"bold",fontSize:"16px"}}},children:e})}},75632:(e,t,s)=>{"use strict";s.d(t,{Z:()=>C});var n=s(10326),a=s(90434),o=s(17577),r=s(31190),i=s(23743),l=s(88441),c=s(16027),u=s(57967),d=s.n(u);s(38932);var p=s(55612),m=s.n(p),j=s(19191),y=s(52210),h=s(15082),f=s(28236),g=s(18970),v=s(90397),x=s(75742),b=s(30088),w=s(57201),$=s(53930),N=s(22304),A=s(5248),k=s(86184),E=s(97980),O=s(70580),M=s(88065);let C=function({opportunity:e,language:t,website:s}){let{t:u,i18n:p}=(0,y.$G)();m().registerLocale(j);let C=(0,i.Z)(),{user:Z}=(0,N.Z)(),[Y,F]=(0,o.useState)(!1),[P,D]=(0,o.useState)(!1),H=async e=>{try{await O.yX.delete(`/favourite/${e}`,{data:{type:"opportunity"}}),L(!1)}catch(e){}},B=async()=>{try{await H(P)}catch(e){}F(!1)};d().locale(p.language||"en");let S=(0,l.Z)(C.breakpoints.down("sm")),T=(0,k.UJ)(),[z,L]=(0,o.useState)(!1),R=e=>{window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"opportunity_view",button_id:"my_button"}),setTimeout(()=>{window.location.href=e},300)};return(0,n.jsxs)("div",{className:"button-pointer",onClick:()=>{S&&R(`/${E.Bi.opportunities.route}/${e?.versions[t]?.url}`)},children:[n.jsx(g.Z,{}),(0,n.jsxs)(c.default,{className:`container opportunity-grid-item ${s?"website":""}`,container:!0,children:[n.jsx(c.default,{item:!0,xs:3,sm:3,className:"item-image",children:n.jsx(a.default,{href:`/${E.Bi.jobCategory.route}/${(0,f.Gc)(e?.industry)}`,children:(0,f.f8)(e?.industry)?(0,f.bO)(e?.industry):null})}),(0,n.jsxs)(c.default,{item:!0,xs:9,sm:9,className:"item-content",children:[(0,n.jsxs)("div",{className:"flex row",children:[n.jsx("div",{className:"flex-item mobile-col",children:n.jsx(M.Z,{title:e?.versions?.[t]?.title||e?.title,child:n.jsx("a",{href:`/${E.Bi.opportunities.route}/${e?.versions?.[t]?.url||e?.url}`,className:"btn p-0 job-title",children:(0,f.rZ)(e?.versions?.[t]?.title||e?.title,30)})})}),(!Z||Z?.roles?.includes(A.uU.CANDIDATE))&&n.jsx(h.default,{icon:n.jsx(w.Z,{className:`${z?"btn-filled-yellow":""}`}),onClick:z?()=>{D(e?._id),F(!0)}:()=>{Z?T.mutate({id:e?._id,title:e?.versions[t]?.title||e?.title,typeOfFavourite:"opportunity"},{onSuccess:()=>{L(!0)}}):r.Am.warning("Login or create account to save opportunity.")},className:"btn btn-ghost bookmark"})]}),(0,n.jsxs)("div",{className:"flex",children:[(0,n.jsxs)("p",{className:"job-ref",children:["Ref: ",e?.reference]}),!S&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("p",{className:"job-contract",children:[n.jsx(b.default,{}),e?.contractType||"Agreement"]}),(0,n.jsxs)("p",{className:"job-deadline",children:[n.jsx(x.default,{}),e?.dateOfExpiration?(0,f.fm)(d()(e?.dateOfExpiration).format("DD MMMM YYYY")):"N/A"]})]})]}),(0,n.jsxs)("div",{className:"flex row",children:[(0,n.jsxs)("a",{className:"location",href:`/${E.Bi.jobLocation.route}/${e?.country.toLowerCase()}`,children:[n.jsx($.Z,{}),n.jsx("p",{className:"location-text",children:e?.country})]}),!S&&n.jsx(h.default,{text:u("global:applyNow"),className:"btn btn-search btn-filled apply",onClick:()=>R(`/${E.Bi.opportunities.route}/${e?.versions?.[t]?.url||e?.url}`)})]})]}),S&&(0,n.jsxs)(c.default,{item:!0,xs:12,sm:12,className:"item-apply",children:[(0,n.jsxs)("div",{className:"flex contract",children:[(0,n.jsxs)("p",{className:"job-contract",children:[n.jsx(b.default,{}),e?.contractType||"Agreement"]}),(0,n.jsxs)("p",{className:"job-deadline",children:[n.jsx(x.default,{}),e?.dateOfExpiration?(0,f.fm)(d()(e?.dateOfExpiration).format("DD MMMM YYYY")):"N/A"]})]}),n.jsx(h.default,{text:u("global:applyNow"),className:"btn btn-outlined apply",onClick:()=>R(`/${E.Bi.opportunities.route}/${e?.versions[t]?.url}`)})]})]},e?._id),n.jsx(v.Z,{open:Y,message:u("messages:supprimeropportunityfavoris"),onClose:()=>{F(!1)},onConfirm:B})]})}},86184:(e,t,s)=>{"use strict";s.d(t,{$i:()=>y,BF:()=>j,Fe:()=>r,Gc:()=>u,HF:()=>o,Hr:()=>l,IZ:()=>m,NF:()=>c,PM:()=>i,UJ:()=>d,jd:()=>p});var n=s(2994),a=s(21464);s(35047),s(97980);let o=()=>(0,n.useMutation)({mutationFn:e=>(0,a.W3)(e),onError:e=>{e.message=""}}),r=e=>(0,n.useQuery)("opportunities",async()=>await (0,a.fH)(e)),i=()=>(0,n.useMutation)(()=>(0,a.AE)()),l=e=>(0,n.useQuery)(["opportunities",e],async()=>await (0,a.Mq)(e)),c=()=>(0,n.useMutation)({mutationFn:(e,t,s)=>(0,a.rE)(e,t,s),onError:e=>{e.message=""}}),u=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t)=>(0,a.S1)(e,t),onError:e=>{e.message=""}})),d=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t,s)=>(0,a.lU)(e,t,s),onError:e=>{e.message=""}})),p=()=>{let e=(0,n.useQueryClient)();return(0,n.useMutation)({mutationFn:(e,t,s,n)=>(0,a.yH)(e,t,s,n),onSuccess:t=>{e.invalidateQueries("files")}})},m=()=>(0,n.useQuery)("SeoOpportunities",async()=>await (0,a.yJ)()),j=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t)=>(0,a.mt)(e,t),onError:e=>{e.message=""}})),y=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:({language:e,id:t,archive:s})=>(0,a.TK)(e,t,s),onError:e=>{console.error("Error during mutation",e),e.message=""}}))},21464:(e,t,s)=>{"use strict";s.d(t,{AE:()=>c,Mq:()=>l,S1:()=>d,TK:()=>y,W3:()=>r,fH:()=>i,lU:()=>p,mt:()=>h,rE:()=>u,yH:()=>m,yJ:()=>j});var n=s(50967),a=s(70580),o=s(31190);let r=e=>(e.t,new Promise(async(t,s)=>{a.yX.post(`/opportunities${n.Y.applications}/${e.opportunityId}`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&s(e)})})),i=e=>new Promise(async(t,s)=>{try{let s=await a.yX.get(`${n.Y.opportunity}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyWord:e.keyWord,visibility:e.visibility,title:e.title,createdAt:e.createdAt,publishDate:e.publishDate,country:e.country,industry:e.industry,contractType:e.contractType,minExperience:e.minExperience,maxExperience:e.maxExperience,jobDescriptionLanguages:e.jobDescriptionLanguages,reference:e.reference,opportunityType:e.opportunityType,exclude:"In House"!==e.opportunityType&&"true"}});t(s.data)}catch(e){s(e)}}),l=e=>new Promise(async(t,s)=>{try{let s=await a.yX.get(`${n.Y.opportunity}/${e}`);t(s.data)}catch(e){s(e)}}),c=async()=>(await a.xk.put("/UpdateJobdescription")).data,u=({data:e,language:t,id:s})=>new Promise(async(r,i)=>{a.yX.post(`${n.Y.opportunity}/${t}/${s}`,e).then(e=>{"en"===t&&o.Am.success("Opportunity english updated successfully"),"fr"===t&&o.Am.success("Opportunity french updated successfully"),e?.data&&r(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})}),d=({data:e,id:t})=>new Promise(async(s,r)=>{a.yX.put(`${n.Y.opportunity}/${t}`,e).then(e=>{o.Am.success("Opportunity Commun fields updated successfully"),e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&r(e)})}),p=({id:e,title:t,typeOfFavourite:s})=>new Promise(async(r,i)=>{a.yX.put(`${n.Y.baseUrl}/favourite/${e}`,{type:s}).then(e=>{o.Am.success(`${s} : ${t} saved to your favorites.`),e?.data&&r(e.data)}).catch(e=>{e&&e.response&&e.response.data&&409===e.response.status&&o.Am.warning(` ${t} already in shortlist`),e&&i(e)})}),m=({resource:e,folder:t,filename:s,body:r})=>new Promise(async(i,l)=>{a.cU.post(`${n.Y.files}/uploadResume/${e}/${t}/${s}`,r.formData).then(e=>{e.data&&i(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(400===e.response.status?e.response.data.message.includes("The resume lacks essential information")?o.Am.warn(r.t("messages:requireResume")):o.Am.warn(e.response.data.message):500===e.response.status&&o.Am.error("Internal Server Error")),e&&l(e)})}),j=()=>new Promise(async(e,t)=>{try{let t=await a.yX.get(`${n.Y.seoOpportunity}`);e(t.data)}catch(e){t(e)}}),y=(e,t,s)=>new Promise(async(r,i)=>{try{let i=await a.yX.put(`${n.Y.opportunity}/${e}/${t}/desarchiver`,{archive:s});i?.data&&(o.Am.success(`opportunity ${s?"archived":"desarchived"} successfully`),r(i.data))}catch(e){o.Am.error(`Failed to ${s?"archive":"desarchive"} the opportunity.`),i(e)}}),h=({data:e,id:t})=>new Promise(async(s,r)=>{a.yX.put(`${n.Y.seoOpportunity}/${t}`,e).then(e=>{o.Am.success("Opportunity seo updated successfully"),e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&r(e)})})},90397:(e,t,s)=>{"use strict";s.d(t,{Z:()=>m});var n=s(10326);s(17577);var a=s(43659),o=s(98117),r=s(48260),i=s(28591),l=s(25609),c=s(10163),u=s(36690),d=s(52210),p=s(15082);let m=function({open:e,onClose:t,onConfirm:s,message:m,icon:j}){let{t:y}=(0,d.$G)();return(0,n.jsxs)(a.Z,{id:"toggle",open:e,onClose:t,"aria-labelledby":"delete-dialog-title",className:"dialog-paper",sx:{"& .MuiPaper-root":{background:"linear-gradient(#0b3051 0%, #234791 100%) !important",color:"#f8f8f8 !important",borderBottom:"transparent !important",borderRadius:"0px !important",boxShadow:"transparent !important"}},children:[n.jsx(o.Z,{sx:{m:0,p:2},id:"delete-dialog-title",children:n.jsx(r.Z,{"aria-label":"close",onClick:t,sx:{position:"absolute",right:8,top:8,color:e=>e.palette.grey[500]},children:n.jsx(u.Z,{})})}),(0,n.jsxs)(i.Z,{dividers:!0,className:"dialog-content",children:[n.jsx("div",{style:{textAlign:"center",marginBottom:"16px"},children:j}),n.jsx(l.default,{gutterBottom:!0,children:m})]}),(0,n.jsxs)(c.Z,{className:"dialog-actions",children:[n.jsx(p.default,{text:y("global:yes"),className:"btn-popup",leftIcon:!0,onClick:s}),n.jsx(p.default,{text:y("global:no"),leftIcon:!0,className:"btn-outlined-popup",onClick:t})]})]})}}};