"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7571],{67571:function(e,a,t){t.d(a,{Z:function(){return S}});var o=t(2265),l=t(61994),r=t(20801),i=t(82590),n=t(32464),c=t(57437),s=(0,n.Z)((0,c.jsx)("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}),"Cancel"),p=t(60118),d=t(85657),v=t(82662),m=t(16210),u=t(76301),g=t(3858),$=t(37053),b=t(94143),y=t(50738);function h(e){return(0,y.ZP)("MuiChip",e)}let C=(0,b.Z)("MuiChip",["root","sizeSmall","sizeMedium","colorDefault","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),f=e=>{let{classes:a,disabled:t,size:o,color:l,iconColor:i,onDelete:n,clickable:c,variant:s}=e,p={root:["root",s,t&&"disabled",`size${(0,d.Z)(o)}`,`color${(0,d.Z)(l)}`,c&&"clickable",c&&`clickableColor${(0,d.Z)(l)}`,n&&"deletable",n&&`deletableColor${(0,d.Z)(l)}`,`${s}${(0,d.Z)(l)}`],label:["label",`label${(0,d.Z)(o)}`],avatar:["avatar",`avatar${(0,d.Z)(o)}`,`avatarColor${(0,d.Z)(l)}`],icon:["icon",`icon${(0,d.Z)(o)}`,`iconColor${(0,d.Z)(i)}`],deleteIcon:["deleteIcon",`deleteIcon${(0,d.Z)(o)}`,`deleteIconColor${(0,d.Z)(l)}`,`deleteIcon${(0,d.Z)(s)}Color${(0,d.Z)(l)}`]};return(0,r.Z)(p,h,a)},Z=(0,m.ZP)("div",{name:"MuiChip",slot:"Root",overridesResolver:(e,a)=>{let{ownerState:t}=e,{color:o,iconColor:l,clickable:r,onDelete:i,size:n,variant:c}=t;return[{[`& .${C.avatar}`]:a.avatar},{[`& .${C.avatar}`]:a[`avatar${(0,d.Z)(n)}`]},{[`& .${C.avatar}`]:a[`avatarColor${(0,d.Z)(o)}`]},{[`& .${C.icon}`]:a.icon},{[`& .${C.icon}`]:a[`icon${(0,d.Z)(n)}`]},{[`& .${C.icon}`]:a[`iconColor${(0,d.Z)(l)}`]},{[`& .${C.deleteIcon}`]:a.deleteIcon},{[`& .${C.deleteIcon}`]:a[`deleteIcon${(0,d.Z)(n)}`]},{[`& .${C.deleteIcon}`]:a[`deleteIconColor${(0,d.Z)(o)}`]},{[`& .${C.deleteIcon}`]:a[`deleteIcon${(0,d.Z)(c)}Color${(0,d.Z)(o)}`]},a.root,a[`size${(0,d.Z)(n)}`],a[`color${(0,d.Z)(o)}`],r&&a.clickable,r&&"default"!==o&&a[`clickableColor${(0,d.Z)(o)})`],i&&a.deletable,i&&"default"!==o&&a[`deletableColor${(0,d.Z)(o)}`],a[c],a[`${c}${(0,d.Z)(o)}`]]}})((0,u.Z)(e=>{let{theme:a}=e,t="light"===a.palette.mode?a.palette.grey[700]:a.palette.grey[300];return{maxWidth:"100%",fontFamily:a.typography.fontFamily,fontSize:a.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,color:(a.vars||a).palette.text.primary,backgroundColor:(a.vars||a).palette.action.selected,borderRadius:16,whiteSpace:"nowrap",transition:a.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",[`&.${C.disabled}`]:{opacity:(a.vars||a).palette.action.disabledOpacity,pointerEvents:"none"},[`& .${C.avatar}`]:{marginLeft:5,marginRight:-6,width:24,height:24,color:a.vars?a.vars.palette.Chip.defaultAvatarColor:t,fontSize:a.typography.pxToRem(12)},[`& .${C.avatarColorPrimary}`]:{color:(a.vars||a).palette.primary.contrastText,backgroundColor:(a.vars||a).palette.primary.dark},[`& .${C.avatarColorSecondary}`]:{color:(a.vars||a).palette.secondary.contrastText,backgroundColor:(a.vars||a).palette.secondary.dark},[`& .${C.avatarSmall}`]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:a.typography.pxToRem(10)},[`& .${C.icon}`]:{marginLeft:5,marginRight:-6},[`& .${C.deleteIcon}`]:{WebkitTapHighlightColor:"transparent",color:a.vars?`rgba(${a.vars.palette.text.primaryChannel} / 0.26)`:(0,i.Fq)(a.palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:a.vars?`rgba(${a.vars.palette.text.primaryChannel} / 0.4)`:(0,i.Fq)(a.palette.text.primary,.4)}},variants:[{props:{size:"small"},style:{height:24,[`& .${C.icon}`]:{fontSize:18,marginLeft:4,marginRight:-4},[`& .${C.deleteIcon}`]:{fontSize:16,marginRight:4,marginLeft:-4}}},...Object.entries(a.palette).filter((0,g.Z)(["contrastText"])).map(e=>{let[t]=e;return{props:{color:t},style:{backgroundColor:(a.vars||a).palette[t].main,color:(a.vars||a).palette[t].contrastText,[`& .${C.deleteIcon}`]:{color:a.vars?`rgba(${a.vars.palette[t].contrastTextChannel} / 0.7)`:(0,i.Fq)(a.palette[t].contrastText,.7),"&:hover, &:active":{color:(a.vars||a).palette[t].contrastText}}}}}),{props:e=>e.iconColor===e.color,style:{[`& .${C.icon}`]:{color:a.vars?a.vars.palette.Chip.defaultIconColor:t}}},{props:e=>e.iconColor===e.color&&"default"!==e.color,style:{[`& .${C.icon}`]:{color:"inherit"}}},{props:{onDelete:!0},style:{[`&.${C.focusVisible}`]:{backgroundColor:a.vars?`rgba(${a.vars.palette.action.selectedChannel} / calc(${a.vars.palette.action.selectedOpacity} + ${a.vars.palette.action.focusOpacity}))`:(0,i.Fq)(a.palette.action.selected,a.palette.action.selectedOpacity+a.palette.action.focusOpacity)}}},...Object.entries(a.palette).filter((0,g.Z)(["dark"])).map(e=>{let[t]=e;return{props:{color:t,onDelete:!0},style:{[`&.${C.focusVisible}`]:{background:(a.vars||a).palette[t].dark}}}}),{props:{clickable:!0},style:{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:a.vars?`rgba(${a.vars.palette.action.selectedChannel} / calc(${a.vars.palette.action.selectedOpacity} + ${a.vars.palette.action.hoverOpacity}))`:(0,i.Fq)(a.palette.action.selected,a.palette.action.selectedOpacity+a.palette.action.hoverOpacity)},[`&.${C.focusVisible}`]:{backgroundColor:a.vars?`rgba(${a.vars.palette.action.selectedChannel} / calc(${a.vars.palette.action.selectedOpacity} + ${a.vars.palette.action.focusOpacity}))`:(0,i.Fq)(a.palette.action.selected,a.palette.action.selectedOpacity+a.palette.action.focusOpacity)},"&:active":{boxShadow:(a.vars||a).shadows[1]}}},...Object.entries(a.palette).filter((0,g.Z)(["dark"])).map(e=>{let[t]=e;return{props:{color:t,clickable:!0},style:{[`&:hover, &.${C.focusVisible}`]:{backgroundColor:(a.vars||a).palette[t].dark}}}}),{props:{variant:"outlined"},style:{backgroundColor:"transparent",border:a.vars?`1px solid ${a.vars.palette.Chip.defaultBorder}`:`1px solid ${"light"===a.palette.mode?a.palette.grey[400]:a.palette.grey[700]}`,[`&.${C.clickable}:hover`]:{backgroundColor:(a.vars||a).palette.action.hover},[`&.${C.focusVisible}`]:{backgroundColor:(a.vars||a).palette.action.focus},[`& .${C.avatar}`]:{marginLeft:4},[`& .${C.avatarSmall}`]:{marginLeft:2},[`& .${C.icon}`]:{marginLeft:4},[`& .${C.iconSmall}`]:{marginLeft:2},[`& .${C.deleteIcon}`]:{marginRight:5},[`& .${C.deleteIconSmall}`]:{marginRight:3}}},...Object.entries(a.palette).filter((0,g.Z)()).map(e=>{let[t]=e;return{props:{variant:"outlined",color:t},style:{color:(a.vars||a).palette[t].main,border:`1px solid ${a.vars?`rgba(${a.vars.palette[t].mainChannel} / 0.7)`:(0,i.Fq)(a.palette[t].main,.7)}`,[`&.${C.clickable}:hover`]:{backgroundColor:a.vars?`rgba(${a.vars.palette[t].mainChannel} / ${a.vars.palette.action.hoverOpacity})`:(0,i.Fq)(a.palette[t].main,a.palette.action.hoverOpacity)},[`&.${C.focusVisible}`]:{backgroundColor:a.vars?`rgba(${a.vars.palette[t].mainChannel} / ${a.vars.palette.action.focusOpacity})`:(0,i.Fq)(a.palette[t].main,a.palette.action.focusOpacity)},[`& .${C.deleteIcon}`]:{color:a.vars?`rgba(${a.vars.palette[t].mainChannel} / 0.7)`:(0,i.Fq)(a.palette[t].main,.7),"&:hover, &:active":{color:(a.vars||a).palette[t].main}}}}})]}})),k=(0,m.ZP)("span",{name:"MuiChip",slot:"Label",overridesResolver:(e,a)=>{let{ownerState:t}=e,{size:o}=t;return[a.label,a[`label${(0,d.Z)(o)}`]]}})({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap",variants:[{props:{variant:"outlined"},style:{paddingLeft:11,paddingRight:11}},{props:{size:"small"},style:{paddingLeft:8,paddingRight:8}},{props:{size:"small",variant:"outlined"},style:{paddingLeft:7,paddingRight:7}}]});function I(e){return"Backspace"===e.key||"Delete"===e.key}var S=o.forwardRef(function(e,a){let t=(0,$.i)({props:e,name:"MuiChip"}),{avatar:r,className:i,clickable:n,color:d="default",component:m,deleteIcon:u,disabled:g=!1,icon:b,label:y,onClick:h,onDelete:C,onKeyDown:S,onKeyUp:x,size:O="medium",variant:R="filled",tabIndex:w,skipFocusWhenDisabled:z=!1,...L}=t,P=o.useRef(null),F=(0,p.Z)(P,a),T=e=>{e.stopPropagation(),C&&C(e)},V=!1!==n&&!!h||n,N=V||C?v.Z:m||"div",E={...t,component:N,disabled:g,size:O,color:d,iconColor:o.isValidElement(b)&&b.props.color||d,onDelete:!!C,clickable:V,variant:R},M=f(E),q=N===v.Z?{component:m||"div",focusVisibleClassName:M.focusVisible,...C&&{disableRipple:!0}}:{},j=null;C&&(j=u&&o.isValidElement(u)?o.cloneElement(u,{className:(0,l.Z)(u.props.className,M.deleteIcon),onClick:T}):(0,c.jsx)(s,{className:(0,l.Z)(M.deleteIcon),onClick:T}));let D=null;r&&o.isValidElement(r)&&(D=o.cloneElement(r,{className:(0,l.Z)(M.avatar,r.props.className)}));let W=null;return b&&o.isValidElement(b)&&(W=o.cloneElement(b,{className:(0,l.Z)(M.icon,b.props.className)})),(0,c.jsxs)(Z,{as:N,className:(0,l.Z)(M.root,i),disabled:!!V&&!!g||void 0,onClick:h,onKeyDown:e=>{e.currentTarget===e.target&&I(e)&&e.preventDefault(),S&&S(e)},onKeyUp:e=>{e.currentTarget===e.target&&C&&I(e)&&C(e),x&&x(e)},ref:F,tabIndex:z&&g?-1:w,ownerState:E,...q,...L,children:[D||W,(0,c.jsx)(k,{className:(0,l.Z)(M.label),ownerState:E,children:y}),j]})})}}]);