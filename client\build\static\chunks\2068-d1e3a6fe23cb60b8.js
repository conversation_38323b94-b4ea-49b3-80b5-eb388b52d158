"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2068,1177],{93214:function(e,t,s){s.d(t,{cU:function(){return l},xk:function(){return i},yX:function(){return r}});var a=s(83464),n=s(40257);let r=a.Z.create({baseURL:n.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),i=a.Z.create({baseURL:n.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),l=a.Z.create({baseURL:n.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"});a.<PERSON><PERSON>create({baseURL:n.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))},17828:function(e,t,s){s.d(t,{Z:function(){return c}});var a=s(83464),n=s(40257),r=a.Z.create({baseURL:n.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0});a.Z.create({baseURL:n.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},withCredentials:!0}),a.Z.create({baseURL:n.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e));var i=s(40257);let l=()=>new Promise(async(e,t)=>{try{let t=await r.get(`${i.env.NEXT_PUBLIC_BASE_API_URL}/account`);e(t.data)}catch(e){t(e)}});var o=s(86484);function c(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],{data:t,error:s,isLoading:a,refetch:n}=(0,o.useQuery)({queryKey:["currentUser"],queryFn:l,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1,retry:e});return{user:t,error:s,isLoading:a,refetch:n}}},2068:function(e,t,s){var a=s(57437),n=s(55788),r=s(78772),i=s(36202),l=s(89138);t.Z=function(){let{t:e}=(0,n.$G)();return(0,a.jsx)("div",{id:"container",children:(0,a.jsxs)("div",{className:"main-content",children:[(0,a.jsx)(r.Z,{t:e}),(0,a.jsx)(i.Z,{t:e}),(0,a.jsx)("hr",{className:"line-update-profile"}),(0,a.jsx)(l.Z,{})]})})}},78772:function(e,t,s){var a=s(57437),n=s(2265),r=s(63993),i=s(35389),l=s(89414),o=s(89126),c=s(64393),d=s(77584),u=s(41774),p=s(28397),m=s(17828),h=s(93214),w=s(81799),x=s(7261);t.Z=function(e){let{t}=e,{user:s,error:f,isLoading:g}=(0,m.Z)(),[y,b]=(0,n.useState)([]),[j,P]=(0,n.useState)(!0),[N,v]=(0,n.useState)(null);(0,n.useEffect)(()=>{let e=async()=>{if(s)try{let e=await h.xk.get("/alerts/user");b(e.data)}catch(e){console.error("Error fetching alerts:",e),v("Unable to fetch alerts. Please try again later.")}finally{P(!1)}};g||f||e()},[s,g,f]);let A=async(e,s)=>{try{await h.xk.put(`/alerts/${e}`,s),x.Am.success(t("settings:updateSuccessalerts"))}catch(e){console.error("Error updating alert:",e),x.Am.warning(t("settings:updateError"))}};if(g||j)return(0,a.jsx)(i.default,{});if(f||N)return(0,a.jsx)("div",{children:f?.message||N});let _={alerts:y.map(e=>({_id:e._id,frequence:e.frequence,title:e.title||"",industry:e.industry||[],country:e.country||[]}))};return(0,a.jsxs)("section",{id:"settings",className:"container-settings",children:[(0,a.jsx)("h3",{className:"heading-h3 bold",children:t("settings:jobAlerts")}),(0,a.jsx)(r.J9,{id:"settings",initialValues:_,enableReinitialize:!0,onSubmit:e=>{e.alerts.forEach(e=>{A(e._id,e)})},children:e=>{let{values:s,setFieldValue:n,initialValues:i}=e;return(0,a.jsx)(r.l0,{children:(0,a.jsx)(r.F2,{name:"alerts",children:e=>{let{remove:m}=e;return(0,a.jsx)(l.default,{container:!0,spacing:2,children:s.alerts.map((e,s)=>(0,a.jsx)(l.default,{item:!0,xs:12,children:(0,a.jsxs)(o.Z,{className:"container-update",children:[(0,a.jsxs)(l.default,{container:!0,spacing:2,children:[(0,a.jsx)(l.default,{item:!0,xs:12,sm:6,children:(0,a.jsxs)(o.Z,{children:[(0,a.jsx)(c.Z,{className:"input-text-settings",children:t("settings:JobTitle")}),(0,a.jsx)(r.gN,{as:d.Z,variant:"standard",name:`alerts.${s}.title`,type:"text",value:e.title})]})}),(0,a.jsx)(l.default,{item:!0,xs:12,sm:6,children:(0,a.jsxs)(o.Z,{children:[(0,a.jsx)(c.Z,{className:"input-text-settings",children:t("settings:industry")}),(0,a.jsx)(w.Z,{multiple:!0,id:`industry-${s}`,options:p.dh,getOptionLabel:e=>e,value:e.industry,onChange:(e,t)=>{n(`alerts.${s}.industry`,t)},renderInput:e=>(0,a.jsx)(d.Z,{...e,className:"input-pentabell multiple-select settings-input",variant:"standard"})})]})})]}),(0,a.jsxs)(l.default,{container:!0,spacing:2,style:{marginTop:"16px"},children:[(0,a.jsx)(l.default,{item:!0,xs:12,sm:6,children:(0,a.jsxs)(o.Z,{children:[(0,a.jsx)(c.Z,{className:"input-text-settings",children:t("settings:country")}),(0,a.jsx)(w.Z,{multiple:!0,id:`country-${s}`,options:p.nh,getOptionLabel:e=>e,value:e.country,onChange:(e,t)=>{n(`alerts.${s}.country`,t)},renderInput:e=>(0,a.jsx)(d.Z,{...e,className:"input-pentabell multiple-select",variant:"standard"})})]})}),(0,a.jsx)(l.default,{item:!0,xs:12,sm:6,children:(0,a.jsxs)(o.Z,{children:[(0,a.jsx)(c.Z,{className:"input-text-settings",children:t("settings:typeofalerts")}),(0,a.jsx)(w.Z,{id:`frequence-${s}`,options:p.Vz,getOptionLabel:e=>e,value:e.frequence,onChange:(e,t)=>{n(`alerts.${s}.frequence`,t)},renderInput:e=>(0,a.jsx)(d.Z,{...e,className:"input-pentabell multiple-select",variant:"standard"})})]})})]}),(0,a.jsxs)("div",{className:"button-group14",style:{marginTop:"16px"},children:[(0,a.jsx)(u.default,{text:t("global:cancel"),className:"btn btn-outlined ",onClick:()=>{n(`alerts.${s}`,i.alerts[s])}}),(0,a.jsx)(u.default,{type:"submit",text:t("global:edit"),className:"btn btn-filled"})]})]})},e._id))})}})})}})]})}},36202:function(e,t,s){var a=s(57437),n=s(89126),r=s(89414),i=s(46387),l=s(85860),o=s(14865),c=s(41774),d=s(17828),u=s(2265),p=s(93214),m=s(7261),h=s(26792),w=s(99075);let x=(0,h.Z)({components:{MuiSwitch:{styleOverrides:{switchBase:{color:"#fffff"},colorPrimary:{"&.Mui-checked":{color:"#234791"}},track:{backgroundColor:"#bbb",opacity:.5,"&.Mui-checked":{backgroundColor:"#234791",opacity:1}}}}}});t.Z=function(e){let{t}=e,{user:s,error:h,isLoading:f}=(0,d.Z)(),[g,y]=(0,u.useState)(!0),[b,j]=(0,u.useState)({newJobAlerts:{email:!1,website:!1},appliedJobStatusUpdates:{email:!1,website:!1},newsLetter:{email:!1,website:!1}}),[P,N]=(0,u.useState)(null),[v,A]=(0,u.useState)(!0),[_,Z]=(0,u.useState)(null),[E,C]=(0,u.useState)(null);(0,u.useEffect)(()=>{let e=async()=>{if(s)try{let e=(await p.xk.get("/settings")).data[0];if(e){let t=e.notifications||{};j({newJobAlerts:t.newJobAlerts||{email:!1,website:!1},appliedJobStatusUpdates:t.appliedJobStatusUpdates||{email:!1,website:!1},newsLetter:t.newsLetter||{email:!1,website:!1}}),N({newJobAlerts:t.newJobAlerts||{email:!1,website:!1},appliedJobStatusUpdates:t.appliedJobStatusUpdates||{email:!1,website:!1},newsLetter:t.newsLetter||{email:!1,website:!1}}),C(e._id)}}catch(e){console.error("Error fetching settings:",e),Z("Unable to fetch settings. Please try again later.")}finally{A(!1)}};f||h||e()},[s,f,h]);let S=e=>{let{name:t,checked:s}=e.target;j(e=>({...e,[t]:{...e[t],email:s}}))},U=e=>{let{name:t,checked:s}=e.target;j(e=>({...e,[t]:{...e[t],website:s}}))},L=async()=>{if(E)try{await p.xk.put(`/settings/${E}`,{notifications:b}),m.Am.success(t("settings:updateSuccessnotifications"))}catch(e){console.error("Error updating settings:",e),m.Am.warning(t("settings:updateError"))}};return(0,a.jsxs)("section",{id:"Notifications-dashboard",className:"section",children:[(0,a.jsx)("p",{className:"heading-h3 bold",children:"Notifications"}),(0,a.jsxs)(n.Z,{children:[(0,a.jsxs)(r.default,{container:!0,spacing:3,alignItems:"center",children:[(0,a.jsx)(r.default,{item:!0,xs:4}),(0,a.jsx)(r.default,{item:!0,xs:2,children:(0,a.jsx)(i.default,{className:"input-text-settings",children:"Email"})}),(0,a.jsx)(r.default,{item:!0,xs:4,children:(0,a.jsx)(i.default,{className:"input-text-settings",children:t("settings:website")})}),(0,a.jsx)(r.default,{item:!0,xs:4,children:(0,a.jsx)(i.default,{className:"input-text-settings-section",children:t("settings:newjobalerts")})}),(0,a.jsx)(r.default,{item:!0,xs:2,children:(0,a.jsx)(w.Z,{theme:x,children:(0,a.jsx)(l.Z,{control:(0,a.jsx)(o.Z,{checked:b.newJobAlerts?.email||!1,onChange:S,name:"newJobAlerts","data-type":"email"}),label:""})})}),(0,a.jsx)(r.default,{item:!0,xs:4,children:(0,a.jsx)(w.Z,{theme:x,children:(0,a.jsx)(l.Z,{control:(0,a.jsx)(o.Z,{checked:b.newJobAlerts?.website||!1,onChange:U,name:"newJobAlerts","data-type":"website"}),label:""})})}),(0,a.jsx)(r.default,{item:!0,xs:4,children:(0,a.jsx)(i.default,{className:"input-text-settings-section",children:t("settings:appliedjobs")})}),(0,a.jsxs)(r.default,{item:!0,xs:2,children:[" ",(0,a.jsx)(w.Z,{theme:x,children:(0,a.jsx)(l.Z,{control:(0,a.jsx)(o.Z,{checked:b.appliedJobStatusUpdates?.email||!1,onChange:S,name:"appliedJobStatusUpdates","data-type":"email"}),label:""})})]}),(0,a.jsx)(r.default,{item:!0,xs:4,children:(0,a.jsx)(w.Z,{theme:x,children:(0,a.jsx)(l.Z,{control:(0,a.jsx)(o.Z,{checked:b.appliedJobStatusUpdates?.website||!1,onChange:U,name:"appliedJobStatusUpdates","data-type":"website"}),label:""})})}),(0,a.jsx)(r.default,{item:!0,xs:4,children:(0,a.jsx)(i.default,{className:"input-text-settings-section",children:t("settings:newsletter")})}),(0,a.jsx)(r.default,{item:!0,xs:2,children:(0,a.jsx)(w.Z,{theme:x,children:(0,a.jsx)(l.Z,{control:(0,a.jsx)(o.Z,{checked:b.newsLetter?.email||!1,onChange:S,name:"newsLetter","data-type":"email"}),label:""})})}),(0,a.jsx)(r.default,{item:!0,xs:4,children:(0,a.jsx)(w.Z,{theme:x,children:(0,a.jsx)(l.Z,{control:(0,a.jsx)(o.Z,{checked:b.newsLetter?.website||!1,onChange:U,name:"newsLetter","data-type":"website"}),label:""})})})]}),(0,a.jsxs)("div",{className:"button-group14",children:[(0,a.jsx)(c.default,{text:t("global:cancel"),className:"btn btn-outlined ",onClick:()=>{j(P)}}),(0,a.jsx)(c.default,{text:t("global:edit"),className:"btn btn-filled ",onClick:L})]})]})]})}},89138:function(e,t,s){var a=s(57437),n=s(2265),r=s(63993),i=s(34422),l=s(85554),o=s(23710),c=s(66422),d=s(55788),u=s(86484),p=s(51177),m=s(10926),h=s(89414),w=s(89126),x=s(64393),f=s(77584),g=s(41774),y=s(17828);t.Z=function(){let e=(0,u.useQueryClient)(),t=(0,p.fV)(),[s,b]=(0,n.useState)("password"),[j,P]=(0,n.useState)(o.Y),[N,v]=(0,n.useState)("password"),[A,_]=(0,n.useState)(o.Y),[Z,E]=(0,n.useState)("password"),[C,S]=(0,n.useState)(o.Y),{t:U,i18n:L}=(0,d.$G)(),{user:I,isLoading:R}=(0,y.Z)(),T=""===I?.password,[k,O]=(0,n.useState)(!1),$=i.Ry().shape({currentPassword:I?.passwordExist&&i.Z_().required(U("validations:emptyField")),newPassword:i.Z_().required(U("validations:emptyField")).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/,U("validations:invalidPassword")),confirmnewPassword:i.Z_().required(U("validations:emptyField")).oneOf([i.iH("newPassword"),null],U("validations:passwordMatch"))}),D=async(s,a)=>{let{setSubmitting:n}=a;try{await t.mutateAsync({passwordData:s,t:U}),e.invalidateQueries("password")}catch(e){console.error("Error:",e)}finally{n(!1)}},F=()=>{b("password"===s?"text":"password"),P("password"===s?c.i:o.Y)},X=()=>{v("password"===N?"text":"password"),_("password"===N?c.i:o.Y)},J=()=>{E("password"===Z?"text":"password"),S("password"===Z?c.i:o.Y)},B=e=>{O(!0),e({values:{currentPassword:null,newPassword:null,confirmnewPassword:null}})};return(0,a.jsx)("div",{id:"settings",children:!T&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{className:"heading-h3 bold",children:U("settings:changePassword")}),!R&&(0,a.jsx)(r.J9,{initialValues:k?{currentPassword:null,newPassword:null,confirmnewPassword:null}:{currentPassword:"",newPassword:"",confirmnewPassword:""},validationSchema:$,onSubmit:D,children:e=>{let{errors:t,touched:n,setFieldValue:i,resetForm:o}=e;return(0,a.jsx)(r.l0,{className:"password-section",children:(0,a.jsx)(m.default,{sx:{display:"flex",gap:2,mb:2},children:(0,a.jsxs)(h.default,{container:!0,rowSpacing:4,columnSpacing:3,sx:{display:"flex",justifyContent:"flex-end"},children:[I?.passwordExist&&(0,a.jsx)(h.default,{item:!0,xs:12,sm:4,children:(0,a.jsx)(w.Z,{children:(0,a.jsxs)(x.Z,{className:"label-form",children:[U("settings:currentPwd"),(0,a.jsx)(f.Z,{name:"currentPassword",variant:"standard",type:s,onChange:e=>{i("currentPassword",e.target.value)},className:`input-pentabell ${t.currentPassword&&n.currentPassword?"is-invalid":""}`,disabled:T}),(0,a.jsx)("span",{className:"eye-icon",onClick:F,children:(0,a.jsx)(l.JO,{icon:j,size:23})}),n.currentPassword&&t.currentPassword&&(0,a.jsx)("div",{className:"label-error",children:t.currentPassword})]})})}),(0,a.jsx)(h.default,{item:!0,xs:12,sm:4,children:(0,a.jsx)(w.Z,{children:(0,a.jsxs)(x.Z,{className:"label-form",children:[U("settings:newPwd"),(0,a.jsx)(f.Z,{name:"newPassword",variant:"standard",type:N,onChange:e=>{i("newPassword",e.target.value)},className:`input-pentabell ${t.newPassword&&n.newPassword?"is-invalid":""}`,disabled:T}),(0,a.jsx)("span",{className:"eye-icon",onClick:X,children:(0,a.jsx)(l.JO,{icon:A,size:23})}),(0,a.jsx)(r.Bc,{name:"newPassword",component:"div",className:"label-error"})]})})}),(0,a.jsx)(h.default,{item:!0,xs:12,sm:4,children:(0,a.jsx)(w.Z,{children:(0,a.jsxs)(x.Z,{className:"label-form",children:[U("settings:confirmNewPwd"),(0,a.jsx)(f.Z,{name:"confirmnewPassword",variant:"standard",type:Z,onChange:e=>{i("confirmnewPassword",e.target.value)},className:`input-pentabell ${t.confirmnewPassword&&n.confirmnewPassword?"is-invalid":""}`,disabled:T}),(0,a.jsx)("span",{className:"eye-icon",onClick:J,children:(0,a.jsx)(l.JO,{icon:C,size:23})}),n.confirmnewPassword&&t.confirmnewPassword&&(0,a.jsx)("div",{className:"label-error",children:t.confirmnewPassword})]})})}),(0,a.jsxs)(m.default,{sx:{display:"flex",justifyContent:"flex-end",gap:2,marginTop:"20px"},children:[(0,a.jsx)(g.default,{type:"button",text:U("global:cancel"),className:"btn btn-outlined",onClick:()=>B(o)}),(0,a.jsx)(g.default,{type:"submit",text:U("global:edit"),className:"btn btn-filled",disabled:T})]})]})})})}})]})})}},51177:function(e,t,s){s.d(t,{px:function(){return y},V3:function(){return v},kw:function(){return A},PU:function(){return j},ZC:function(){return N},iQ:function(){return P},vD:function(){return g},NB:function(){return b},fV:function(){return _}});var a=s(86484),n=s(7261),r=s(93214),i=s(4174),l=s(46172);let o=e=>{let t=e.t;return new Promise(async(s,a)=>{r.yX.put(`${l.Y.updateUser}/${e.user}`,e.nonEmptyValues).then(t=>{e.nonEmptyValues.profilePicture||n.Am.success("Personal information updated successfully."),t.data&&s(t.data)}).catch(e=>{e&&e.response&&e.response.data&&404===e.response.status&&n.Am.error(t("messages:userNotFound")),e&&a(e)})})},c=e=>new Promise(async(t,s)=>{r.yX.put(`${l.Y.users}/archive/${e.id}`,{archive:e.archive}).then(e=>{n.Am.success("user updated successfully"),e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&s(e)})}),d=e=>new Promise(async(t,s)=>{try{let s=await r.xk.get(`/users/${e}`);t(s.data)}catch(e){s(e)}}),u=e=>{let t=e.body.t;return new Promise(async(s,a)=>{r.yX.put(`${l.Y.candidate}`,e.body).then(e=>{e.data&&(s(e.data),n.Am.success(t("messages:professionalInfoUpdated")))}).catch(e=>{e&&e.response&&e.response.data&&404===e.response.status&&n.Am.error(t("messages:candidateNotFound")),e&&a(e)})})},p=e=>new Promise(async(e,t)=>{try{let t=await r.yX.get(l.Y.currentUser);e(t.data)}catch(e){e.response&&404===e.response.status&&n.Am.error(i.X.USER_NOT_FOUND),e.response&&401===e.response.status||n.Am.error(i.X.SERVER_ERROR),t(e)}}),m=e=>new Promise(async(t,s)=>{try{let s=await r.yX.get(`${l.Y.users}/${e}`);t(s.data)}catch(e){e.response&&404===e.response.status&&n.Am.error(i.X.USER_NOT_FOUND),e.response&&401===e.response.status||n.Am.error(i.X.SERVER_ERROR),s(e)}}),h=()=>new Promise(async(e,t)=>{try{let t=await r.yX.get(`${l.Y.candidate}/currentCandidate`);e(t.data)}catch(e){e.response&&404===e.response.status&&n.Am.error(i.X.CandidateNotFound),t(e)}}),w=(e,t)=>new Promise(async(s,a)=>{try{let a=e.includes(t)?e:[...e,t],n=await r.yX.get(`${l.Y.skills}?industries=${encodeURIComponent(a.length>0?a.join(","):[])}`);s(n.data)}catch(e){a(e)}});async function x(e){let t=e.t;try{let s=await r.yX.put(`${l.v}/account/password`,e.passwordData);return n.Am.success(t("settings:supdatepassword")),s.data}catch(e){throw e.response&&406===e.response.status?n.Am.error(t("messages:currentpassword")):n.Am.error(t("messages:errorupdatepassword")),e.response?e.response.data:e}}var f=s(17828);let g=()=>{let e=(0,a.useQueryClient)(),{refetch:t}=P();return(0,a.useMutation)({mutationFn:e=>o(e),onSuccess:s=>{e.invalidateQueries("userData"),t(),localStorage.setItem("user",JSON.stringify(s))},onError:e=>{e.message=""}})},y=()=>(0,a.useMutation)({mutationFn:e=>{let{id:t,archive:s}=e;return c({id:t,archive:s})},onError:e=>{e.message="error on useArchiveduser"}}),b=()=>{let e=(0,a.useQueryClient)(),{user:t}=(0,f.Z)();return(0,a.useMutation)({mutationFn:e=>u({body:e}),onSuccess:t=>{e.invalidateQueries("candidateData")},onError:e=>{e.message=""}})},j=e=>(0,a.useQuery)(["user",e],async()=>await d(e)),P=()=>(0,a.useQuery)("userData",async()=>{try{return await p()}catch(e){}}),N=e=>(0,a.useQuery)("userData",async()=>{try{return await m(e)}catch(e){}}),v=()=>{let{user:e}=(0,f.Z)();return(0,a.useQuery)(["getCandidateData"],async()=>{try{return await h()}catch(e){}})},A=(e,t)=>(0,a.useQuery)("skills",async()=>{try{return await w(e,t)}catch(e){}}),_=()=>{let e=(0,a.useQueryClient)();return(0,a.useMutation)(x,{onSuccess:()=>{e.invalidateQueries("password")}})}},4174:function(e,t,s){s.d(t,{X:function(){return a}});let a={INCORRECT_PASSWORD:"Password incorrect",EMAIL_NOT_FOUND:"There's no user with this email",InvalidEmail:"Invalid email address.",FailedUpdateFile:"failed to upload file try again !!",ACCOUNT_NOTACTIVATED:"Your account is not activated yet. Please check your email for the activation link.",EMAIL_EXIST:"Email already exist",FileExist:"File already exists",FileNotFound:"File not found!",error:"'An unknown error occurred'",ERROR:"An error occurred. Please try again later !",INVALID_SIGNUP_DATA:"Invalid signup data. Please check your information and try again.",CandidateNotFound:"Candidate not found",ResetPasswordLink:"Check your email. Link expires in 10 mins!",VALIDATIONS:{INVALID_EMAIL:"Invalid email",EMPTY_FIELD:"Please fill in the required fields!",END_DATE:"End date must be after start date",MIN_DATE:"Date of birth must be after 1950",MAX_DATE:"Date must be before 2005",MIN_LENGTH:"Field must be at least 3 characters",MAX_LENGTH:"Field must be at most 20 characters",REQUIRED:"This field is required!",INVALID_PASSWORD:"Password requires at least one uppercase, one lowercase letter, and one digit",PASSWORDMATCH:"password must match"}}},46172:function(e,t,s){s.d(t,{Y:function(){return n},v:function(){return a}});let a=s(40257).env.NEXT_PUBLIC_BASE_API_URL,n={seo:"seoTags",auth:"/auth/signin",logout:"/auth/logout",candidatures:"/applications",signup:"/auth/signup",forgetPassword:"/auth/forgot-password",resetPassword:"/auth/reset-password",guides:"/guides",currentUser:"/users/current",updateUser:"/users",users:"/users",categoryGuides:"guidecategory",candidate:"/candidates",report:"/report",skills:"/skills",files:"/files",applications:"/applications",sliders:"/sliders",favoris:"/candidate/favourite",articles:"/articles",categories:"/categories",blog:"/blog",category:"/categories",opportunity:"/opportunities",seoOpportunity:"/seoOpportunity",newsletter:"/newsletter",contact:"/contact",favourite:"/favourite",contacts:"contacts",comments:"/comments",statistics:"/statistics",events:"/events",baseUrl:`${a}`}}}]);