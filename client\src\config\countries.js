import tunisiaFlag from "@/assets/images/countries/tunisia.png";
import algeriaFlag from "@/assets/images/countries/algeria.png";
import moroccoFlag from "@/assets/images/countries/morocco.png";
import libyaFlag from "@/assets/images/countries/libya.png";
import egyptFlag from "@/assets/images/countries/egypt.png";
import { websiteRoutesList } from "../helpers/routesList";

export const TeamCountries = {
  TUNISIA: "TUNISIA",
  ALGERIA: "ALGERIA",
  MOROCCO: "MOROCCO",
};

export const OfficesCountries = {
  TUNISIA: "TUNISIA",
  ALGERIA: "ALGERIA",
  Qatar: "Qatar",
  UAE: "UAE",
  IRAQ: "IRAQ",
  SaudiArabia: "Saudi Arabia",
  ALGERIAHASSI: "ALGERIAHASSI",
  ALGERIAHYDRA: "ALGERIAHYDRA",
  MOROCCO: "MOROCCO",
  EGYPT: "EGYPT",
  LIBYA: "LIBYA",
  FRANCE: "FRANCE",
  SWITZERLAND: "SWITZERLAND",
};


export const COUNTRIES_LIST_FLAG = [
  { value: "TUNISIA", label: "Tunisia", flag: tunisiaFlag.src },
  {
    value: "ALGERIAHASSI",
    label: "Hassi Messaoud, Algeria",
    flag: algeriaFlag.src,
  },
  { value: "ALGERIAHYDRA", label: "Hydra, Algeria", flag: algeriaFlag.src },
  { value: "ALGERIA", label: "Algeria", flag: algeriaFlag.src },

  { value: "MOROCCO", label: "morocco", flag: moroccoFlag.src },
  { value: "EGYPT", label: "Egypt", flag: egyptFlag.src },
  { value: "LIBYA", label: "Libya", flag: libyaFlag.src },
];

export const OFFICES_COUNTRIES_LIST = [
  {
    value: "FRNCE",
    label: "global:countryFrance",
    id: "franceInfo",
    idFr: "franceInfofr",

    idPin: "france",
    link: `/${websiteRoutesList.francePage.route}`,
    city: "global:cityParis",
  },
  {
    value: "SWITZERLAND",
    label: "global:countrySwitzerland",
    id: "switzerlandInfo",
    idFr: "switzerlandInfofr",

    idPin: "switzerland",
    link: `/${websiteRoutesList.contact.route}`,
    city: "global:cityMontreux",
  },
  {
    value: "SAUDIARABIA",
    label: "global:countrySaudiArabia",
    id: "saudiarabiaInfo",
    idFr: "saudiarabiaInfofr",

    idPin: "saudiarabia",
    link: `/${websiteRoutesList.ksaPage.route}`,
    city: "global:cityRiyadh",
  },
  {
    value: "UAE",
    label: "global:countryUAE",
    id: "uaeInfo",
    idFr: "uaeInfofr",

    idPin: "uae",
    link: `/${websiteRoutesList.dubaiPage.route}`,
    city: "global:cityDubai",
  },
  {
    value: "QATAR",
    label: "global:countryQatar",
    id: "qatarInfo",
    idFr: "qatarInfofr",

    idPin: "qatar",
    link: `/${websiteRoutesList.qatarPage.route}`,
    city: "global:cityDoha",
  },
  {
    value: "TUNISIA",
    label: "global:countryTunisia",
    id: "tunisInfo",
    idFr: "tunisInfofr",

    idPin: "tunisia",
    link: `/${websiteRoutesList.tunisiaPage.route}`,
    city: "global:cityTunis",
  },
  {
    value: "ALGERIA",
    label: "global:countryAlgeria",
    id: "algeriaInfo",
    idFr: "algeriaInfofr",

    idPin: "algeria",
    link: `/${websiteRoutesList.algeriaPage.route}`,
    city: "global:cityAlger",
  },
  {
    value: "MOROCCO",
    label: "global:countryMorocco",
    id: "moroccoInfo",
    idFr: "moroccoInfofr",

    idPin: "morocco",
    link: `/${websiteRoutesList.moroccoPage.route}`,
    city: "global:cityCasablanca",
  },
  {
    value: "EGYPTE",
    label: "global:countryEgypt",
    id: "egypteInfo",
    idFr: "egypteInfofr",

    idPin: "egypte",
    link: `/${websiteRoutesList.egyptePage.route}`,
    city: "global:cityCairo",
  },
  {
    value: "LIBYA",
    label: "global:countryLibya",
    id: "libyaInfo",
    idFr: "libyaInfofr",

    idPin: "libya",
    link: `/${websiteRoutesList.libyaPage.route}`,
    city: "global:cityTripoli",
  },
  {
    value: "IRAQ",
    label: "global:countryIraq",
    id: "iraqInfo",
    idFr: "iraqInfofr",

    idPin: "iraq",
    link: `/${websiteRoutesList.contact.route}`,
    city: "global:cityBagdad",
  },
];

export const OFFICES_ZONE_LIST = [
  {
    value: "EUROPEAN",
    label: "global:officeZoneEuropean",
    id: "europeanInfo",
    link: `/${websiteRoutesList.europePage.route}`,
  },
  {
    value: "MIDDLEEAST",
    label: "global:officeZoneMiddleEast",
    id: "middleeastInfo",
    link: `/${websiteRoutesList.middleEastPage.route}`,
  },
  {
    value: "AFRICA",
    label: "global:officeZoneAfrica",
    id: "africaInfo",
    link: `/${websiteRoutesList.africaPage.route}`,
  },
];
