"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/blog/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/ArticlesList.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/ArticlesList.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ArticlesList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,InputAdornment,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,InputAdornment,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,InputAdornment,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,InputAdornment,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/InputAdornment/InputAdornment.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! embla-carousel-react */ \"(app-pages-browser)/./node_modules/embla-carousel-react/esm/embla-carousel-react.esm.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _components_CustomPagination__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/CustomPagination */ \"(app-pages-browser)/./src/components/CustomPagination.jsx\");\n/* harmony import */ var _assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../assets/images/icons/searchIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/searchIcon.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../assets/images/icons/refreshIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon.svg\");\n/* harmony import */ var _BlogItem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./BlogItem */ \"(app-pages-browser)/./src/features/blog/components/BlogItem.jsx\");\n/* harmony import */ var _guides_components_GuideItem__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../guides/components/GuideItem */ \"(app-pages-browser)/./src/features/guides/components/GuideItem.jsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var embla_carousel_autoplay__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! embla-carousel-autoplay */ \"(app-pages-browser)/./node_modules/embla-carousel-autoplay/esm/embla-carousel-autoplay.esm.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* harmony import */ var html_to_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! html-to-text */ \"(app-pages-browser)/./node_modules/html-to-text/lib/html-to-text.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import SvgCategoriesIcon from \"../../../assets/images/icons/categoriesIcon.svg\";\n\n\n\n\nfunction ArticlesList(param) {\n    let { data, language, searchParams, isCategory } = param;\n    _s();\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(parseInt(searchParams?.pageNumber || 1));\n    const [keyword, setkeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(searchParams?.keyword);\n    const searchQueryParams = new URLSearchParams();\n    const searchParamsVar = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.useSearchParams)();\n    const searchParamsContent = searchParamsVar.toString();\n    const [slides, setSlides] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const gradients = [\n        \"linear-gradient(to bottom, rgba(214, 155, 25, 0), rgba(214, 155, 25, 0.6))\",\n        \"linear-gradient(to bottom, rgba(116, 55, 148, 0), rgba(116, 55, 148, 0.6))\",\n        \"linear-gradient(to bottom, rgba(0, 153, 102, 0), rgba(0, 153, 102, 0.6))\",\n        \"linear-gradient(to bottom, rgba(204, 50, 51, 0), rgba(204, 50, 51, 0.6))\"\n    ];\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.usePathname)();\n    const truncateDescription = (title)=>{\n        title = (0,html_to_text__WEBPACK_IMPORTED_MODULE_13__.htmlToText)(title, {\n            wordwrap: false\n        });\n        const words = title.split(\" \");\n        if (words?.length >= 20) {\n            return words.slice(0, 20).join(\" \");\n        } else {\n            return title;\n        }\n    };\n    const updateUrlWithParams = ()=>{\n        if (keyword) searchQueryParams.set(\"keyword\", keyword);\n        if (pageNumber) searchQueryParams.set(\"pageNumber\", 1);\n        router.push(`${pathname}?${searchQueryParams.toString()}`);\n    };\n    const resetSearch = ()=>{\n        setkeyword(\"\");\n        setPageNumber(1);\n        searchQueryParams.set(\"pageNumber\", 1);\n        router.push(`${pathname}?${searchQueryParams.toString()}`);\n    };\n    const handlePageChange = (page)=>{\n        setPageNumber(page);\n    };\n    let lastColor = \"\";\n    const getRandomGradient = ()=>{\n        // return gradients[Math.floor(Math.random() * gradients.length)];\n        let randomColor;\n        do {\n            randomColor = gradients[Math.floor(Math.random() * gradients.length)];\n        }while (randomColor === lastColor);\n        lastColor = randomColor;\n        return randomColor;\n    };\n    const fetchCategories = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_5__.axiosGetJsonSSR.get(`${language}${_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.category}`);\n            const categoriesData = response.data.categoriesData.map((category)=>{\n                const firstVersion = category?.versionscategory[0];\n                return {\n                    img: firstVersion?.image ? `${\"http://localhost:4000/api/v1\"}/files/${firstVersion?.image}` : null,\n                    link: `${firstVersion?.url}`,\n                    name: firstVersion?.name || \"N/A\"\n                };\n            });\n            setSlides(categoriesData);\n        } catch (error) {\n            setError(t(\"messages:fetchCategoriesFailed\"));\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCategories();\n    }, []);\n    const OPTIONS = {\n        loop: true,\n        align: \"start\"\n    };\n    const [emblaRef] = (0,embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(OPTIONS, [\n        (0,embla_carousel_autoplay__WEBPACK_IMPORTED_MODULE_16__[\"default\"])({\n            playOnInit: true,\n            delay: 1000,\n            stopOnMouseEnter: true\n        })\n    ]);\n    const OPTIONS_Right = {\n        loop: true,\n        align: \"start\",\n        direction: \"rtl\"\n    };\n    const [emblaRefRight] = (0,embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(OPTIONS_Right, [\n        (0,embla_carousel_autoplay__WEBPACK_IMPORTED_MODULE_16__[\"default\"])({\n            playOnInit: true,\n            delay: 1000,\n            stopOnMouseEnter: true\n        })\n    ]);\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\") {\n            updateUrlWithParams();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"blogs-page\",\n        children: [\n            !isCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"search-bar-blogs\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"custom-max-width\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"container\",\n                        container: true,\n                        spacing: 0,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 8,\n                                container: true,\n                                spacing: 0,\n                                className: \"filter-inputs\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"input-pentabell\",\n                                        autoComplete: \"off\",\n                                        slotProps: {\n                                            input: {\n                                                startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    position: \"start\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            }\n                                        },\n                                        variant: \"standard\",\n                                        type: \"text\",\n                                        value: keyword,\n                                        onChange: (e)=>setkeyword(e.target.value),\n                                        onKeyDown: handleKeyDown,\n                                        placeholder: \"Keywords\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 4,\n                                className: \"btns-filter\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 25\n                                        }, void 0),\n                                        className: \"btn btn-outlined\",\n                                        onClick: resetSearch\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        text: \"Search\",\n                                        onClick: updateUrlWithParams,\n                                        className: \"btn btn-filled full-width\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 144,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                className: \"custom-max-width\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"heading-h1\",\n                        children: \"Browse by topic\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"embla\",\n                        id: \"blog__categories__slider\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"embla__viewport\",\n                            ref: emblaRef,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"embla__container categories-list\",\n                                children: slides.map((category, index)=>category?.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"category-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.category.route}/${category.link}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.category.route}/${category.link}`}/`,\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, category.link, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"embla\",\n                        style: {\n                            marginBottom: \"20px\"\n                        },\n                        id: \"blog__categories__slider\",\n                        dir: \"rtl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"embla__viewport\",\n                            ref: emblaRefRight,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"embla__container categories-list\",\n                                children: slides.map((category, index)=>category?.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"category-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.category.route}/${category.link}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.category.route}/${category.link}`}/`,\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, category.link, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                className: \"custom-max-width\",\n                children: [\n                    data?.firstArticle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"first-blog\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"last-blog\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            locale: language === \"en\" ? \"en\" : \"fr\",\n                                            href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${data?.firstArticle?.versions[0]?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${data?.firstArticle?.versions[0]?.url}`}/`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"title featured-title\",\n                                                children: data?.firstArticle?.versions[0]?.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"description\",\n                                            children: data?.firstArticle?.versions[0]?.description ? data?.firstArticle?.versions[0]?.description : truncateDescription(data?.firstArticle?.versions[0]?.content)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            text: t(\"global:readMore\"),\n                                            className: \"btn btn-outlined\",\n                                            link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${data?.firstArticle?.versions[0]?.url}`,\n                                            aHref: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    style: {\n                                        textDecoration: \"none\"\n                                    },\n                                    locale: language === \"en\" ? \"en\" : \"fr\",\n                                    href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${data?.firstArticle?.versions[0]?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${data?.firstArticle?.versions[0]?.url}`}/`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"blog-img-section\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"img-section\",\n                                            style: {\n                                                backgroundImage: `url(${\"http://localhost:4000/api/v1\"}/files/${data?.firstArticle?.versions[0]?.image})`\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                            lineNumber: 247,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        container: true,\n                        rowSpacing: 0,\n                        columnSpacing: 2,\n                        children: data?.items?.map((item, index)=>{\n                            if (item.type === \"guide\") {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_guides_components_GuideItem__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    guideData: item,\n                                    language: language\n                                }, `guide-${index}`, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 17\n                                }, this);\n                            }\n                            if (item.type === \"article\") {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BlogItem__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    blogData: item,\n                                    language: language\n                                }, `article-${index}`, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 17\n                                }, this);\n                            }\n                            return null;\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    data?.totalItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomPagination__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        type: \"ssr\",\n                        totalPages: Math.ceil(data?.totalItems / data?.pageSize),\n                        currentPage: pageNumber,\n                        onPageChange: handlePageChange,\n                        searchQueryParams: searchParamsContent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n_s(ArticlesList, \"kCCrGwijBur6MosTsACv7Pj+5/c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.useSearchParams,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.usePathname,\n        embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    ];\n});\n_c = ArticlesList;\nvar _c;\n$RefreshReg$(_c, \"ArticlesList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/ArticlesList.jsx\n"));

/***/ })

});