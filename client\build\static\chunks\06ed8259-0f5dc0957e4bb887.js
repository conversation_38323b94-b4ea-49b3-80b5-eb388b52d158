"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[738],{22094:function(e,t,n){n.d(t,{Z:function(){return a}});var i=n(22475),l=n(78902),o=n(74935),s=n(37689),r=n(91555);function a(e,t,n,a,d,c){let f=e.element.originElement.ownerDocument||document,h=f.defaultView||window,u=s.Z,g=d.icons,m={_d:f,_w:h,_parser:new h.DOMParser,_prevRtl:d.rtl,_editorHeight:0,_editorHeightPadding:0,_listCamel:d.__listCommonStyle,_listKebab:u.camelToKebabCase(d.__listCommonStyle),__focusTemp:e.element._focusTemp,_wd:null,_ww:null,_shadowRoot:null,_shadowRootControllerEventTarget:null,util:u,functions:null,options:null,wwComputedStyle:null,notice:r.Z,icons:g,history:null,context:e,pluginCallButtons:t,plugins:n||{},initPlugins:{},_targetPlugins:{},_menuTray:{},lang:a,effectNode:null,submenu:null,container:null,_submenuName:"",_bindedSubmenuOff:null,_bindedContainerOff:null,submenuActiveButton:null,containerActiveButton:null,controllerArray:[],currentControllerName:"",currentControllerTarget:null,currentFileComponentInfo:null,codeViewDisabledButtons:[],resizingDisabledButtons:[],_moreLayerActiveButton:null,_htmlCheckWhitelistRegExp:null,_htmlCheckBlacklistRegExp:null,_disallowedTextTagsRegExp:null,editorTagsWhitelistRegExp:null,editorTagsBlacklistRegExp:null,pasteTagsWhitelistRegExp:null,pasteTagsBlacklistRegExp:null,hasFocus:!1,isDisabled:!1,isReadOnly:!1,_attributesWhitelistRegExp:null,_attributesWhitelistRegExp_all_data:null,_attributesBlacklistRegExp:null,_attributesTagsWhitelist:null,_attributesTagsBlacklist:null,_bindControllersOff:null,_isInline:null,_isBalloon:null,_isBalloonAlways:null,_inlineToolbarAttr:{top:"",width:"",isShow:!1},_notHideToolbar:!1,_sticky:!1,_antiBlur:!1,_lineBreaker:null,_lineBreakerButton:null,_componentsInfoInit:!0,_componentsInfoReset:!1,activePlugins:null,managedTagsInfo:null,_charTypeHTML:!1,_fileInfoPluginsCheck:null,_fileInfoPluginsReset:null,_fileManager:{tags:null,regExp:null,queryString:null,pluginRegExp:null,pluginMap:null},commandMap:{},_commandMapStyles:{STRONG:["font-weight"],U:["text-decoration"],EM:["font-style"],DEL:["text-decoration"]},_styleCommandMap:null,_cleanStyleRegExp:{div:new h.RegExp("\\s*[^-a-zA-Z](.+)\\s*:[^;]+(?!;)*","ig"),span:new h.RegExp("\\s*[^-a-zA-Z](font-family|font-size|color|background-color)\\s*:[^;]+(?!;)*","ig"),format:new h.RegExp("\\s*[^-a-zA-Z](text-align|margin-left|margin-right|width|height|line-height)\\s*:[^;]+(?!;)*","ig"),fontSizeUnit:new h.RegExp("\\d+"+d.fontSizeUnit+"$","i")},_variable:{isChanged:!1,isCodeView:!1,isFullScreen:!1,innerHeight_fullScreen:0,resizeClientY:0,tabSize:4,codeIndent:2,minResizingSize:u.getNumber(e.element.wysiwygFrame.style.minHeight||"65",0),currentNodes:[],currentNodesMap:[],_range:null,_selectionNode:null,_originCssText:e.element.topArea.style.cssText,_bodyOverflow:"",_editorAreaOriginCssText:"",_wysiwygOriginCssText:"",_codeOriginCssText:"",_fullScreenAttrs:{sticky:!1,balloon:!1,inline:!1},_lineBreakComp:null,_lineBreakDir:""},_formatAttrsTemp:null,_saveButtonStates:function(){this.allCommandButtons||(this.allCommandButtons={});let e=this.context.element._buttonTray.querySelectorAll(".se-menu-list button[data-display]");for(let t=0,n,i;t<e.length;t++)i=(n=e[t]).getAttribute("data-command"),this.allCommandButtons[i]=n},_recoverButtonStates:function(){if(this.allCommandButtons){let e=this.context.element._buttonTray.querySelectorAll(".se-menu-list button[data-display]");for(let t=0,n,i,l;t<e.length;t++)i=(n=e[t]).getAttribute("data-command"),(l=this.allCommandButtons[i])&&(n.parentElement.replaceChild(l,n),this.context.tool[i]&&(this.context.tool[i]=l))}},callPlugin:function(e,n,i){if(i=i||t[e],this.plugins[e])this.initPlugins[e]?"object"==typeof this._targetPlugins[e]&&i&&this.initMenuTarget(e,i,this._targetPlugins[e]):(this.plugins[e].add(this,i),this.initPlugins[e]=!0);else throw Error('[SUNEDITOR.core.callPlugin.fail] The called plugin does not exist or is in an invalid format. (pluginName:"'+e+'")');this.plugins[e].active&&!this.commandMap[e]&&i&&(this.commandMap[e]=i,this.activePlugins.push(e)),"function"==typeof n&&n()},addModule:function(e){for(let t=0,n=e.length,i;t<n;t++)i=e[t].name,this.plugins[i]||(this.plugins[i]=e[t]),this.initPlugins[i]||(this.initPlugins[i]=!0,"function"==typeof this.plugins[i].add&&this.plugins[i].add(this))},getGlobalScrollOffset:function(){let t=0,n=0,i=e.element.topArea;for(;i;)t+=i.scrollTop,n+=i.scrollLeft,i=i.parentElement;for(i=this._shadowRoot?this._shadowRoot.host:null;i;)t+=i.scrollTop,n+=i.scrollLeft,i=i.parentElement;return{top:t,left:n}},initMenuTarget:function(t,n,i){n?(e.element._menuTray.appendChild(i),this._targetPlugins[t]=!0,this._menuTray[n.getAttribute("data-command")]=i):this._targetPlugins[t]=i},submenuOn:function(e){this._bindedSubmenuOff&&this._bindedSubmenuOff(),this._bindControllersOff&&this.controllersOff();let t=this._submenuName=e.getAttribute("data-command"),n=this.submenu=this._menuTray[t];this.submenuActiveButton=e,this._setMenuPosition(e,n),this._bindedSubmenuOff=this.submenuOff.bind(this),this.addDocEvent("mousedown",this._bindedSubmenuOff,!1),this.plugins[t].on&&this.plugins[t].on.call(this),this._antiBlur=!0},submenuOff:function(){this.removeDocEvent("mousedown",this._bindedSubmenuOff),this._bindedSubmenuOff=null,this.submenu&&(this._submenuName="",this.submenu.style.display="none",this.submenu=null,u.removeClass(this.submenuActiveButton,"on"),this.submenuActiveButton=null,this._notHideToolbar=!1),this._antiBlur=!1},moreLayerOff:function(){this._moreLayerActiveButton&&(e.element.toolbar.querySelector("."+this._moreLayerActiveButton.getAttribute("data-command")).style.display="none",u.removeClass(this._moreLayerActiveButton,"on"),this._moreLayerActiveButton=null)},containerOn:function(e){this._bindedContainerOff&&this._bindedContainerOff();let t=this._containerName=e.getAttribute("data-command"),n=this.container=this._menuTray[t];this.containerActiveButton=e,this._setMenuPosition(e,n),this._bindedContainerOff=this.containerOff.bind(this),this.addDocEvent("mousedown",this._bindedContainerOff,!1),this.plugins[t].on&&this.plugins[t].on.call(this),this._antiBlur=!0},containerOff:function(){this.removeDocEvent("mousedown",this._bindedContainerOff),this._bindedContainerOff=null,this.container&&(this._containerName="",this.container.style.display="none",this.container=null,u.removeClass(this.containerActiveButton,"on"),this.containerActiveButton=null,this._notHideToolbar=!1),this._antiBlur=!1},_setMenuPosition:function(t,n){n.style.visibility="hidden",n.style.display="block",n.style.height="",u.addClass(t,"on");let i=this.context.element.toolbar,l=i.offsetWidth,o=p._getEditorOffsets(e.element.toolbar),s=n.offsetWidth,r=t.parentElement.offsetLeft+3;if(d.rtl){let e=t.offsetWidth,i=s>e?s-e:0;n.style.left=r-i+(i>0?0:e-s)+"px",o.left>p._getEditorOffsets(n).left&&(n.style.left="0px")}else{let e=l<=s?0:l-(r+s);e<0?n.style.left=r+e+"px":n.style.left=r+"px"}let a=0,c=t;for(;c&&c!==i;)a+=c.offsetTop,c=c.offsetParent;let f=a;this._isBalloon?a+=i.offsetTop+t.offsetHeight:a-=t.offsetHeight;let g=o.top,m=n.offsetHeight,y=this.getGlobalScrollOffset().top,C=h.innerHeight-(g-y+f+t.parentElement.offsetHeight);if(C<m){let e=-1*(m-f+3),i=g-y+e,l=m+(i<0?i:0);l>C?(n.style.height=l+"px",e=-1*(l-f+3)):(n.style.height=C+"px",e=f+t.parentElement.offsetHeight),n.style.top=e+"px"}else n.style.top=f+t.parentElement.offsetHeight+"px";n.style.visibility=""},controllersOn:function(){this._bindControllersOff&&this._bindControllersOff(),this.controllerArray=[];for(let e=0,t;e<arguments.length;e++)if(t=arguments[e]){if("string"==typeof t){this.currentControllerName=t;continue}if("function"==typeof t){this.controllerArray.push(t);continue}if(!u.hasClass(t,"se-controller")){this.currentControllerTarget=t,this.currentFileComponentInfo=this.getFileComponent(t);continue}t.style&&(t.style.display="block",this._shadowRoot&&-1===this._shadowRootControllerEventTarget.indexOf(t)&&(t.addEventListener("mousedown",function(e){e.preventDefault(),e.stopPropagation()}),this._shadowRootControllerEventTarget.push(t))),this.controllerArray.push(t)}this._bindControllersOff=this.controllersOff.bind(this),this.addDocEvent("mousedown",this._bindControllersOff,!1),this.addDocEvent("keydown",this._bindControllersOff,!1),this._antiBlur=!0,"function"==typeof y.showController&&y.showController(this.currentControllerName,this.controllerArray,this)},controllersOff:function(t){this._lineBreaker.style.display="none";let n=this.controllerArray.length;if(t&&t.target&&n>0){for(let e=0;e<n;e++)if("function"==typeof this.controllerArray[e].contains&&this.controllerArray[e].contains(t.target))return}if((!this._fileManager.pluginRegExp.test(this.currentControllerName)||!t||"keydown"!==t.type||27===t.keyCode)&&(e.element.lineBreaker_t.style.display=e.element.lineBreaker_b.style.display="none",this._variable._lineBreakComp=null,this.currentControllerName="",this.currentControllerTarget=null,this.currentFileComponentInfo=null,this.effectNode=null,this._bindControllersOff)){if(this.removeDocEvent("mousedown",this._bindControllersOff),this.removeDocEvent("keydown",this._bindControllersOff),this._bindControllersOff=null,n>0){for(let e=0;e<n;e++)"function"==typeof this.controllerArray[e]?this.controllerArray[e]():this.controllerArray[e].style.display="none";this.controllerArray=[]}this._antiBlur=!1}},setControllerPosition:function(t,n,i,l){d.rtl&&(l.left*=-1);let o=u.getOffset(n,e.element.wysiwygFrame);t.style.visibility="hidden",t.style.display="block";let s="top"===i?-(t.offsetHeight+2):n.offsetHeight+12;t.style.top=o.top+s+l.top+"px";let r=o.left-e.element.wysiwygFrame.scrollLeft+l.left,a=t.offsetWidth,c=n.offsetWidth,f=u.hasClass(t.firstElementChild,"se-arrow")?t.firstElementChild:null;if(d.rtl){let n=a>c?a-c:0;t.style.left=r-n+(n>0?0:c-a)+"px",n>0&&f&&(f.style.left=(a-14<10+n?a-14:10+n)+"px");let i=e.element.wysiwygFrame.offsetLeft-t.offsetLeft;i>0&&(t.style.left="0px",f&&(f.style.left=i+"px"))}else{t.style.left=r+"px";let n=e.element.wysiwygFrame.offsetWidth-(t.offsetLeft+a);n<0?(t.style.left=t.offsetLeft+n+"px",f&&(f.style.left=20-n+"px")):f&&(f.style.left="20px")}t.style.visibility=""},execCommand:function(e,t,n){this._wd.execCommand(e,t,"formatBlock"===e?"<"+n+">":n),this.history.push(!0)},nativeFocus:function(){this.__focus(),this._editorRange()},__focus:function(){let t=u.getParentElement(this.getSelectionNode(),"figcaption");t?t.focus():e.element.wysiwyg.focus()},focus:function(){if("none"!==e.element.wysiwygFrame.style.display){if(d.iframe)this.nativeFocus();else try{let t=this.getRange();if(t.startContainer===t.endContainer&&u.isWysiwygDiv(t.startContainer)){let n=t.commonAncestorContainer.children[t.startOffset];if(!u.isFormatElement(n)&&!u.isComponent(n)){let t=u.createElement(d.defaultTag),i=u.createElement("BR");t.appendChild(i),e.element.wysiwyg.insertBefore(t,n),this.setRange(i,0,i,0);return}}this.setRange(t.startContainer,t.startOffset,t.endContainer,t.endOffset)}catch(e){this.nativeFocus()}p._applyTagEffects(),this._isBalloon&&p._toggleToolbarBalloon()}},focusEdge:function(t){t||(t=e.element.wysiwyg.lastElementChild);let n=this.getFileComponent(t);n?this.selectComponent(n.target,n.pluginName):t?(t=u.getChildElement(t,function(e){return 0===e.childNodes.length||3===e.nodeType},!0))?this.setRange(t,t.textContent.length,t,t.textContent.length):this.nativeFocus():this.focus()},blur:function(){d.iframe?e.element.wysiwygFrame.blur():e.element.wysiwyg.blur()},setRange:function(e,t,n,i){if(!e||!n)return;t>e.textContent.length&&(t=e.textContent.length),i>n.textContent.length&&(i=n.textContent.length),u.isFormatElement(e)&&(e=e.childNodes[t]||e.childNodes[t-1]||e,t=t>0?1===e.nodeType?1:e.textContent?e.textContent.length:0:0),u.isFormatElement(n)&&(n=n.childNodes[i]||n.childNodes[i-1]||n,i=i>0?1===n.nodeType?1:n.textContent?n.textContent.length:0:0);let l=this._wd.createRange();try{l.setStart(e,t),l.setEnd(n,i)}catch(e){console.warn("[SUNEDITOR.core.focus.error] "+e),this.nativeFocus();return}let o=this.getSelection();return o.removeAllRanges&&o.removeAllRanges(),o.addRange(l),this._rangeInfo(l,this.getSelection()),d.iframe&&this.__focus(),l},removeRange:function(){this._variable._range=null,this._variable._selectionNode=null,this.hasFocus&&this.getSelection().removeAllRanges(),this._setKeyEffect([])},getRange:function(){let t=this._variable._range||this._createDefaultRange(),n=this.getSelection();if(t.collapsed===n.isCollapsed||!e.element.wysiwyg.contains(n.focusNode))return t;if(n.rangeCount>0)return this._variable._range=n.getRangeAt(0),this._variable._range;{let e=n.anchorNode,t=n.focusNode,i=n.anchorOffset,l=n.focusOffset,o=u.compareElements(e,t),s=o.ancestor&&(0===o.result?i<=l:o.result>1);return this.setRange(s?e:t,s?i:l,s?t:e,s?l:i)}},getRange_addLine:function(t,n){if(this._selectionVoid(t)){let i=e.element.wysiwyg,l=u.createElement(d.defaultTag);l.innerHTML="<br>",i.insertBefore(l,n&&n!==i?n.nextElementSibling:i.firstElementChild),this.setRange(l.firstElementChild,0,l.firstElementChild,1),t=this._variable._range}return t},getSelection:function(){let t=this._shadowRoot&&this._shadowRoot.getSelection?this._shadowRoot.getSelection():this._ww.getSelection();return this._variable._range||e.element.wysiwyg.contains(t.focusNode)||(t.removeAllRanges(),t.addRange(this._createDefaultRange())),t},getSelectionNode:function(){if(e.element.wysiwyg.contains(this._variable._selectionNode)||this._editorRange(),!this._variable._selectionNode){let t=u.getChildElement(e.element.wysiwyg.firstChild,function(e){return 0===e.childNodes.length||3===e.nodeType},!1);if(t)return this._variable._selectionNode=t,t;this._editorRange()}return this._variable._selectionNode},_editorRange:function(){let e=this._wd.activeElement;if(u.isInputElement(e))return this._variable._selectionNode=e,e;let t=this.getSelection();if(!t)return null;let n=null;n=t.rangeCount>0?t.getRangeAt(0):this._createDefaultRange(),this._rangeInfo(n,t)},_rangeInfo:function(e,t){let n=null;this._variable._range=e,n=e.collapsed?u.isWysiwygDiv(e.commonAncestorContainer)&&e.commonAncestorContainer.children[e.startOffset]||e.commonAncestorContainer:t.extentNode||t.anchorNode,this._variable._selectionNode=n},_createDefaultRange:function(){let t=e.element.wysiwyg,n=this._wd.createRange(),i=t.firstElementChild,l=null;return i?(l=i.firstChild)||(l=u.createElement("BR"),i.appendChild(l)):(i=u.createElement(d.defaultTag),l=u.createElement("BR"),i.appendChild(l),t.appendChild(i)),n.setStart(l,0),n.setEnd(l,0),n},_selectionVoid:function(e){let t=e.commonAncestorContainer;return u.isWysiwygDiv(e.startContainer)&&u.isWysiwygDiv(e.endContainer)||/FIGURE/i.test(t.nodeName)||this._fileManager.regExp.test(t.nodeName)||u.isMediaComponent(t)},_resetRangeToTextNode:function(){let t,n,i;let l=this.getRange();if(this._selectionVoid(l))return!1;let o=l.collapsed,s=l.startContainer,r=l.startOffset,a=l.endContainer,c=l.endOffset;if(u.isFormatElement(s))for(s.childNodes[r]?(s=s.childNodes[r]||s,r=0):r=(s=s.lastChild||s).textContent.length;s&&1===s.nodeType&&s.firstChild;)s=s.firstChild||s,r=0;if(u.isFormatElement(a)){for(a=a.childNodes[c]||a.lastChild||a;a&&1===a.nodeType&&a.lastChild;)a=a.lastChild;c=o?0:a.textContent.length}if(t=u.isWysiwygDiv(s)?e.element.wysiwyg.firstChild:s,n=r,u.isBreak(t)||1===t.nodeType&&t.childNodes.length>0){let e=u.isBreak(t);if(!e){for(;t&&!u.isBreak(t)&&1===t.nodeType;)t=t.childNodes[n]||t.nextElementSibling||t.nextSibling,n=0;let e=u.getFormatElement(t,null);e===u.getRangeFormatElement(e,null)&&(e=u.createElement(u.getParentElement(t,u.isCell)?"DIV":d.defaultTag),t.parentNode.insertBefore(e,t),e.appendChild(t))}if(u.isBreak(t)){let n=u.createTextNode(u.zeroWidthSpace);t.parentNode.insertBefore(n,t),t=n,e&&s===a&&(a=t,c=1)}}if(s=t,r=n,t=u.isWysiwygDiv(a)?e.element.wysiwyg.lastChild:a,n=c,u.isBreak(t)||1===t.nodeType&&t.childNodes.length>0){let e=u.isBreak(t);if(!e){for(;t&&!u.isBreak(t)&&1===t.nodeType&&0!==(i=t.childNodes).length;)t=i[n>0?n-1:n]||!/FIGURE/i.test(i[0].nodeName)?i[0]:t.previousElementSibling||t.previousSibling||s,n=n>0?t.textContent.length:n;let e=u.getFormatElement(t,null);e===u.getRangeFormatElement(e,null)&&(e=u.createElement(u.isCell(e)?"DIV":d.defaultTag),t.parentNode.insertBefore(e,t),e.appendChild(t))}if(u.isBreak(t)){let i=u.createTextNode(u.zeroWidthSpace);t.parentNode.insertBefore(i,t),t=i,n=1,e&&!t.previousSibling&&u.removeItem(a)}}return a=t,c=n,this.setRange(s,r,a,c),!0},getSelectedElements:function(t){if(!this._resetRangeToTextNode())return[];let n=this.getRange();if(u.isWysiwygDiv(n.startContainer)){let t=e.element.wysiwyg.children;if(0===t.length)return[];this.setRange(t[0],0,t[t.length-1],t[t.length-1].textContent.trim().length),n=this.getRange()}let i=n.startContainer,l=n.endContainer,o=n.commonAncestorContainer,s=u.getListChildren(o,function(e){return t?t(e):u.isFormatElement(e)});if(u.isWysiwygDiv(o)||u.isRangeFormatElement(o)||s.unshift(u.getFormatElement(o,null)),i===l||1===s.length)return s;let r=u.getFormatElement(i,null),a=u.getFormatElement(l,null),d=null,c=null,f=function(e){return!u.isTable(e)||/^TABLE$/i.test(e.nodeName)},h=u.getRangeFormatElement(r,f),g=u.getRangeFormatElement(a,f);u.isTable(h)&&u.isListCell(h.parentNode)&&(h=h.parentNode),u.isTable(g)&&u.isListCell(g.parentNode)&&(g=g.parentNode);let m=h===g;for(let e=0,t=s.length,n;e<t;e++){if(r===(n=s[e])||!m&&n===h){d=e;continue}if(a===n||!m&&n===g){c=e;break}}return null===d&&(d=0),null===c&&(c=s.length-1),s.slice(d,c+1)},getSelectedElementsAndComponents:function(e){let t=this.getRange().commonAncestorContainer,n=u.getParentElement(t,u.isComponent),i=u.isTable(t)?this.getSelectedElements(null):this.getSelectedElements((function(e){let t=this.getParentElement(e,this.isComponent);return this.isFormatElement(e)&&(!t||t===n)||this.isComponent(e)&&!this.getFormatElement(e)}).bind(u));if(e){for(let e=0,t=i.length;e<t;e++)for(let n=e-1;n>=0;n--)if(i[n].contains(i[e])){i.splice(e,1),e--,t--;break}}return i},isEdgePoint:function(e,t,n){return 1===e.nodeType&&!e.textContent.length||"end"!==n&&0===t||(!n||"start"!==n)&&!e.nodeValue&&1===t||(!n||"end"===n)&&!!e.nodeValue&&t===e.nodeValue.length},_isEdgeFormat:function(e,t,n){if(!this.isEdgePoint(e,t,n))return!1;let i=[];for(n="start"===n?"previousSibling":"nextSibling";e&&!u.isFormatElement(e)&&!u.isWysiwygDiv(e);){if(!(!e[n]||u.isBreak(e[n])&&!e[n][n]))return null;1===e.nodeType&&i.push(e.cloneNode(!1)),e=e.parentNode}return i},showLoading:function(){e.element.loading.style.display="block"},closeLoading:function(){e.element.loading.style.display="none"},appendFormatTag:function(e,t){if(!e||!e.parentNode)return null;let n=u.getFormatElement(this.getSelectionNode(),null),i=null;if(!u.isFormatElement(e)&&u.isFreeFormatElement(n||e.parentNode))i=u.createElement("BR");else{let e=t?"string"==typeof t?t:t.nodeName:!u.isFormatElement(n)||u.isRangeFormatElement(n)||u.isFreeFormatElement(n)?d.defaultTag:n.nodeName;(i=u.createElement(e)).innerHTML="<br>",(t&&"string"!=typeof t||!t&&u.isFormatElement(n))&&u.copyTagAttributes(i,t||n,["id"])}return u.isCell(e)?e.insertBefore(i,e.nextElementSibling):e.parentNode.insertBefore(i,e.nextElementSibling),i},insertComponent:function(e,t,n,i){if(this.isReadOnly||n&&!this.checkCharCount(e,null))return null;let l=this.removeNode();this.getRange_addLine(this.getRange(),l.container);let o=null,s=this.getSelectionNode(),r=u.getFormatElement(s,null);if(u.isListCell(r))this.insertNode(e,s===r?null:l.container.nextSibling,!1),e.nextSibling||e.parentNode.appendChild(u.createElement("BR"));else{if(this.getRange().collapsed&&(3===l.container.nodeType||u.isBreak(l.container))){let e=u.getParentElement(l.container,(function(e){return this.isRangeFormatElement(e)}).bind(u));(o=u.splitElement(l.container,l.offset,e?u.getElementDepth(e)+1:0))&&(r=o.previousSibling)}this.insertNode(e,u.isRangeFormatElement(r)?null:r,!1),r&&u.onlyZeroWidthSpace(r)&&u.removeItem(r)}if(!i){this.setRange(e,0,e,0);let t=this.getFileComponent(e);t?this.selectComponent(t.target,t.pluginName):o&&(o=u.getEdgeChildNodes(o,null).sc||o,this.setRange(o,0,o,0))}return t||this.history.push(1),o||e},getFileComponent:function(e){let t,n;return this._fileManager.queryString&&e&&((/^FIGURE$/i.test(e.nodeName)||/se-component/.test(e.className))&&(t=e.querySelector(this._fileManager.queryString)),!t&&e.nodeName&&this._fileManager.regExp.test(e.nodeName)&&(t=e),t&&(n=this._fileManager.pluginMap[t.nodeName.toLowerCase()]))?{target:t,component:u.getParentElement(t,u.isComponent),pluginName:n}:null},selectComponent:function(e,t){if(u.isUneditableComponent(u.getParentElement(e,u.isComponent))||u.isUneditableComponent(e))return!1;this.hasFocus||this.focus();let n=this.plugins[t];n&&h.setTimeout((function(){"function"==typeof n.select&&this.callPlugin(t,n.select.bind(this,e),null),this._setComponentLineBreaker(e)}).bind(this))},_setComponentLineBreaker:function(t){let n,i,l;this._lineBreaker.style.display="none";let o=u.getParentElement(t,u.isComponent),s=e.element.lineBreaker_t.style,r=e.element.lineBreaker_b.style,a="block"===this.context.resizing.resizeContainer.style.display?this.context.resizing.resizeContainer:t,d=u.isListCell(o.parentNode);(d?o.previousSibling:u.isFormatElement(o.previousElementSibling))?s.display="none":(this._variable._lineBreakComp=o,i=e.element.wysiwyg.scrollTop,n=u.getOffset(t,e.element.wysiwygFrame).top+i,l=a.offsetWidth/2/2,s.top=n-i-12+"px",s.left=u.getOffset(a).left+l+"px",s.display="block"),(d?o.nextSibling:u.isFormatElement(o.nextElementSibling))?r.display="none":(n||(this._variable._lineBreakComp=o,i=e.element.wysiwyg.scrollTop,n=u.getOffset(t,e.element.wysiwygFrame).top+i,l=a.offsetWidth/2/2),r.top=n+a.offsetHeight-i-12+"px",r.left=u.getOffset(a).left+a.offsetWidth-l-24+"px",r.display="block")},_checkDuplicateNode:function(e,t){!function e(n){m._dupleCheck(n,t);let i=n.childNodes;for(let t=0,n=i.length;t<n;t++)e(i[t])}(e)},_dupleCheck:function(e,t){if(!u.isTextStyleElement(e))return;let n=(e.style.cssText.match(/[^;]+;/g)||[]).map(function(e){return e.trim()}),i=e.nodeName;if(/^span$/i.test(i)&&0===n.length)return e;let l=!1;return!function t(o){if(!u.isWysiwygDiv(o)&&u.isTextStyleElement(o)){if(o.nodeName===i){l=!0;let t=o.style.cssText.match(/[^;]+;/g)||[];for(let e=0,i=t.length,l;e<i;e++)(l=n.indexOf(t[e].trim()))>-1&&n.splice(l,1);for(let t=0,n=o.classList.length;t<n;t++)e.classList.remove(o.classList[t])}t(o.parentElement)}}(t),l&&((e.style.cssText=n.join(" "))||(e.setAttribute("style",""),e.removeAttribute("style")),e.attributes.length||e.setAttribute("data-se-duple","true")),e},insertNode:function(t,n,i){if(this.isReadOnly||i&&!this.checkCharCount(t,null))return null;let l=null,o=this.getRange(),s=u.isListCell(o.commonAncestorContainer)?o.commonAncestorContainer:u.getFormatElement(this.getSelectionNode(),null),r=u.isListCell(s)&&(u.isListCell(t)||u.isList(t)),a,c,f,h=null,g=u.isFreeFormatElement(s),m=!g&&(u.isFormatElement(t)||u.isRangeFormatElement(t))||u.isComponent(t);if(r&&(f=n||u.isList(t)?s.lastChild:s.nextElementSibling,h=u.isList(t)?s:(f||s).parentNode),!n&&(m||u.isComponent(t)||u.isMedia(t))){let e=this.isEdgePoint(o.endContainer,o.endOffset,"end"),t=this.removeNode(),i=t.container,l=i===t.prevContainer&&o.collapsed?null:t.prevContainer;if(r&&l){if((h=3===l.nodeType?l.parentNode:l).contains(i)){let e=!0;for(f=i;f.parentNode&&f.parentNode!==h;)f=f.parentNode,e=!1;e&&i===l&&(f=f.nextSibling)}else f=null}else if(r&&u.isListCell(i)&&!s.parentElement)s=u.createElement("LI"),h.appendChild(s),i.appendChild(h),f=null;else if(3===i.nodeType||u.isBreak(i)||r){let l=u.getParentElement(i,(function(e){return this.isRangeFormatElement(e)||this.isListCell(e)}).bind(u));if(n=u.splitElement(i,t.offset,l?u.getElementDepth(l)+1:0)){if(r){if(s.contains(i)){let t=u.isList(s.lastElementChild),i=null;e||(i=s.cloneNode(!1)).appendChild(n.textContent.trim()?n:u.createTextNode(u.zeroWidthSpace)),t&&(i||(i=s.cloneNode(!1)).appendChild(u.createTextNode(u.zeroWidthSpace)),i.appendChild(s.lastElementChild)),i&&(s.parentNode.insertBefore(i,s.nextElementSibling),f=n=i)}}else n=n.previousSibling}else f=n=s}}let p=(o=n||m?this.getRange():this.getRange_addLine(this.getRange(),null)).commonAncestorContainer,y=o.startOffset,C=o.endOffset,_=o.startContainer===p&&u.isFormatElement(p),b=_&&(p.childNodes[y]||p.childNodes[0])||o.startContainer,E=_&&(p.childNodes[C]||p.childNodes[p.childNodes.length-1])||o.endContainer;if(!r){if(n)a=n.parentNode,n=n.nextSibling,c=!0;else if(a=b,3===b.nodeType&&(a=b.parentNode),o.collapsed){if(3===p.nodeType)n=p.textContent.length>C?p.splitText(C):p.nextSibling;else if(u.isBreak(a))n=a,a=a.parentNode;else{let e=a.childNodes[y],i=e&&3===e.nodeType&&u.onlyZeroWidthSpace(e)&&u.isBreak(e.nextSibling)?e.nextSibling:e;i?!i.nextSibling&&u.isBreak(i)?(a.removeChild(i),n=null):n=u.isBreak(i)&&!u.isBreak(t)?i:i.nextSibling:n=null}}else if(b===E){n=this.isEdgePoint(E,C)?E.nextSibling:E.splitText(C);let e=b;this.isEdgePoint(b,y)||(e=b.splitText(y)),a.removeChild(e),0===a.childNodes.length&&m&&(a.innerHTML="<br>")}else{let e=this.removeNode(),i=e.container,l=e.prevContainer;if(i&&0===i.childNodes.length&&m&&(u.isFormatElement(i)?i.innerHTML="<br>":u.isRangeFormatElement(i)&&(i.innerHTML="<"+d.defaultTag+"><br></"+d.defaultTag+">")),u.isListCell(i)&&3===t.nodeType)a=i,n=null;else if(!m&&l){if((a=3===l.nodeType?l.parentNode:l).contains(i)){let e=!0;for(n=i;n.parentNode&&n.parentNode!==a;)n=n.parentNode,e=!1;e&&i===l&&(n=n.nextSibling)}else n=null}else u.isWysiwygDiv(i)&&!u.isFormatElement(t)?(a=i.appendChild(u.createElement(d.defaultTag)),n=null):a=(n=m?E:i===l?i.nextSibling:i)&&n.parentNode?n.parentNode:p;for(;n&&!u.isFormatElement(n)&&n.parentNode!==p;)n=n.parentNode}}try{if(!r){if((u.isWysiwygDiv(n)||a===e.element.wysiwyg.parentNode)&&(a=e.element.wysiwyg,n=null),u.isFormatElement(t)||u.isRangeFormatElement(t)||!u.isListCell(a)&&u.isComponent(t)){let e=a;if(u.isList(n))a=n,n=null;else if(u.isListCell(n))a=n.previousElementSibling||n;else if(!c&&!n){let e=this.removeNode(),t=3===e.container.nodeType?u.isListCell(u.getFormatElement(e.container,null))?e.container:u.getFormatElement(e.container,null)||e.container.parentNode:e.container,i=u.isWysiwygDiv(t)||u.isRangeFormatElement(t);a=i?t:t.parentNode,n=i?null:t.nextSibling}0===e.childNodes.length&&a!==e&&u.removeItem(e)}if(!m||g||u.isRangeFormatElement(a)||u.isListCell(a)||u.isWysiwygDiv(a)||(n=a.nextElementSibling,a=a.parentNode),u.isWysiwygDiv(a)&&(3===t.nodeType||u.isBreak(t))){let e=u.createElement(d.defaultTag);e.appendChild(t),l=t,t=e}}if(r?h.parentNode?(a=h,n=f):(a=e.element.wysiwyg,n=null):n=a===n?a.lastChild:n,u.isListCell(t)&&!u.isList(a)){if(u.isListCell(a))n=a.nextElementSibling,a=a.parentNode;else{let e=u.createElement("ol");a.insertBefore(e,n),a=e,n=null}r=!0}if(this._checkDuplicateNode(t,a),a.insertBefore(t,n),r){if(u.onlyZeroWidthSpace(s.textContent.trim()))u.removeItem(s),t=t.lastChild;else{let e=u.getArrayItem(s.children,u.isList);e&&(t!==e?(t.appendChild(e),t=e.previousSibling):(a.appendChild(t),t=a),u.onlyZeroWidthSpace(s.textContent.trim())&&u.removeItem(s))}}}catch(e){a.appendChild(t),console.warn("[SUNEDITOR.insertNode.warn] "+e)}finally{l&&(t=l);let e=a.querySelectorAll("[data-se-duple]");if(e.length>0)for(let n=0,i=e.length,l,o,s,r;n<i;n++){for(s=(l=e[n]).childNodes,r=l.parentNode;s[0];)o=s[0],r.insertBefore(o,l);l===t&&(t=o),u.removeItem(l)}if((u.isFormatElement(t)||u.isComponent(t))&&b===E){let e=u.getFormatElement(p,null);e&&1===e.nodeType&&u.isEmptyLine(e)&&u.removeItem(e)}if(g&&(u.isFormatElement(t)||u.isRangeFormatElement(t))&&(t=this._setIntoFreeFormat(t)),!u.isComponent(t)){let e=1;if(3===t.nodeType)e=t.textContent.length,this.setRange(t,e,t,e);else if(!u.isBreak(t)&&!u.isListCell(t)&&u.isFormatElement(a)){let n=null;(!t.previousSibling||u.isBreak(t.previousSibling))&&(n=u.createTextNode(u.zeroWidthSpace),t.parentNode.insertBefore(n,t)),(!t.nextSibling||u.isBreak(t.nextSibling))&&(n=u.createTextNode(u.zeroWidthSpace),t.parentNode.insertBefore(n,t.nextSibling)),u._isIgnoreNodeChange(t)&&(t=t.nextSibling,e=0)}this.setRange(t,e,t,e)}return t}},_setIntoFreeFormat:function(e){let t,n;let i=e.parentNode;for(;u.isFormatElement(e)||u.isRangeFormatElement(e);){for(t=e.childNodes,n=null;t[0];){if(n=t[0],u.isFormatElement(n)||u.isRangeFormatElement(n)){if(this._setIntoFreeFormat(n),!e.parentNode)break;t=e.childNodes;continue}i.insertBefore(n,e)}0===e.childNodes.length&&u.removeItem(e),e=u.createElement("BR"),i.insertBefore(e,n.nextSibling)}return e},removeNode:function(){this._resetRangeToTextNode();let t=this.getRange();if(t.startContainer===t.endContainer){let e=u.getParentElement(t.startContainer,u.isMediaComponent);if(e){let t=u.createElement("BR"),n=u.createElement(d.defaultTag);return n.appendChild(t),u.changeElement(e,n),m.setRange(n,0,n,0),this.history.push(!0),{container:n,offset:0,prevContainer:null}}}let n=0===t.startOffset,i=m.isEdgePoint(t.endContainer,t.endOffset,"end"),l=null,o=null,s=null;n&&(o=u.getFormatElement(t.startContainer))&&(o=l=o.previousElementSibling),i&&(s=(s=u.getFormatElement(t.endContainer))?s.nextElementSibling:s);let r,a=0,c=t.startContainer,f=t.endContainer,h=t.startOffset,g=t.endOffset,p=3===t.commonAncestorContainer.nodeType&&t.commonAncestorContainer.parentNode===c.parentNode?c.parentNode:t.commonAncestorContainer;if(p===c&&p===f&&(c=p.children[h],f=p.children[g],h=g=0),!c||!f)return{container:p,offset:0};if(c===f&&t.collapsed&&c.textContent&&u.onlyZeroWidthSpace(c.textContent.substr(h)))return{container:c,offset:h,prevContainer:c&&c.parentNode?c:null};let y=null,C=null,_=u.getListChildNodes(p,null),b=u.getArrayIndex(_,c),E=u.getArrayIndex(_,f);if(_.length>0&&b>-1&&E>-1){for(let e=b+1,t=c;e>=0;e--)_[e]===t.parentNode&&_[e].firstChild===t&&0===h&&(b=e,t=t.parentNode);for(let e=E-1,t=f;e>b;e--)_[e]===t.parentNode&&1===_[e].nodeType&&(_.splice(e,1),t=t.parentNode,--E)}else{if(0===_.length){if(u.isFormatElement(p)||u.isRangeFormatElement(p)||u.isWysiwygDiv(p)||u.isBreak(p)||u.isMedia(p))return{container:p,offset:0};if(3===p.nodeType)return{container:p,offset:g};_.push(p),c=f=p}else if(c=f=_[0],u.isBreak(c)||u.onlyZeroWidthSpace(c))return{container:u.isMedia(p)?p:c,offset:0};b=E=0}for(let e=b;e<=E;e++){let t=_[e];if(0===t.length||3===t.nodeType&&void 0===t.data){this._nodeRemoveListItem(t);continue}if(t===c){if(1===c.nodeType){if(u.isComponent(c))continue;y=u.createTextNode(c.textContent)}else t===f?(y=u.createTextNode(c.substringData(0,h)+f.substringData(g,f.length-g)),a=h):y=u.createTextNode(c.substringData(0,h));if(y.length>0?c.data=y.data:this._nodeRemoveListItem(c),t===f)break;continue}if(t===f){if(1===f.nodeType){if(u.isComponent(f))continue;C=u.createTextNode(f.textContent)}else C=u.createTextNode(f.substringData(g,f.length-g));C.length>0?f.data=C.data:this._nodeRemoveListItem(f);continue}this._nodeRemoveListItem(t)}let v=u.getParentElement(f,"ul"),w=u.getParentElement(c,"li");if(v&&w&&w.contains(v)?a=(r=v.previousSibling).textContent.length:(r=f&&f.parentNode?f:c&&c.parentNode?c:t.endContainer||t.startContainer,a=n||i?i?r.textContent.length:0:a),!u.isWysiwygDiv(r)&&0===r.childNodes.length){let t=u.removeItemAllParents(r,null,null);t&&(r=t.sc||t.ec||e.element.wysiwyg)}return u.getFormatElement(r)||c&&c.parentNode||(s?(r=s,a=0):o&&(r=o,a=1)),this.setRange(r,a,r,a),this.history.push(!0),{container:r,offset:a,prevContainer:l}},_nodeRemoveListItem:function(e){let t=u.getFormatElement(e,null);u.removeItem(e),u.isListCell(t)&&(u.removeItemAllParents(t,null,null),t&&u.isList(t.firstChild)&&t.insertBefore(u.createTextNode(u.zeroWidthSpace),t.firstChild))},applyRangeFormatElement:function(e){let t,n,i;this.getRange_addLine(this.getRange(),null);let l=this.getSelectedElementsAndComponents(!1);if(!l||0===l.length)return;e:for(let e=0,t=l.length,n,i,o,s,r,a;e<t;e++)if(n=l[e],u.isListCell(n)&&(i=n.lastElementChild)&&u.isListCell(n.nextElementSibling)&&l.indexOf(n.nextElementSibling)>-1&&(s=i.lastElementChild,l.indexOf(s)>-1)){let e=null;for(;e=s.lastElementChild;)if(u.isList(e)){if(l.indexOf(e.lastElementChild)>-1)s=e.lastElementChild;else continue e}o=i.firstElementChild,r=l.indexOf(o),a=l.indexOf(s),l.splice(r,a-r+1),t=l.length;continue}let o=l[l.length-1];t=u.isRangeFormatElement(o)||u.isFormatElement(o)?o:u.getRangeFormatElement(o,null)||u.getFormatElement(o,null),u.isCell(t)?(n=null,i=t):(n=t.nextSibling,i=t.parentNode);let s=u.getElementDepth(t),r=null,a=[],d=function(e,t,n){let i=null;if(e!==t&&!u.isTable(t)){if(t&&u.getElementDepth(e)===u.getElementDepth(t))return n;i=u.removeItemAllParents(t,null,e)}return i?i.ec:n};for(let t=0,o=l.length,c,f,h,g,m,p,y;t<o;t++)if(!(!(f=(c=l[t]).parentNode)||e.contains(f))){if(h=u.getElementDepth(c),u.isList(f)){if(null===r&&(p?(r=p,y=!0,p=null):r=f.cloneNode(!1)),a.push(c),m=l[t+1],t===o-1||m&&m.parentNode!==f){m&&c.contains(m.parentNode)&&(p=m.parentNode.cloneNode(!1));let t=f.parentNode,l;for(;u.isList(t);)(l=u.createElement(t.nodeName)).appendChild(r),r=l,t=t.parentNode;let o=this.detachRangeFormatElement(f,a,null,!0,!0);s>=h?(s=h,(n=d(i=o.cc,f,o.ec))&&(i=n.parentNode)):i===o.cc&&(n=o.ec),i!==o.cc&&(n=void 0!==(g=d(i,o.cc,g))?g:o.cc);for(let e=0,t=o.removeArray.length;e<t;e++)r.appendChild(o.removeArray[e]);y||e.appendChild(r),p&&o.removeArray[o.removeArray.length-1].appendChild(p),r=null,y=!1}}else s>=h&&(s=h,i=f,n=c.nextSibling),e.appendChild(c),i!==f&&void 0!==(g=d(i,f))&&(n=g)}if(this.effectNode=null,u.mergeSameTags(e,null,!1),u.mergeNestedTags(e,(function(e){return this.isList(e)}).bind(u)),n&&u.getElementDepth(n)>0&&(u.isList(n.parentNode)||u.isList(n.parentNode.parentNode))){let t=u.getParentElement(n,(function(e){return this.isRangeFormatElement(e)&&!this.isList(e)}).bind(u)),i=u.splitElement(n,null,t?u.getElementDepth(t)+1:0);i.parentNode.insertBefore(e,i)}else i.insertBefore(e,n),d(e,n);let c=u.getEdgeChildNodes(e.firstElementChild,e.lastElementChild);l.length>1?this.setRange(c.sc,0,c.ec,c.ec.textContent.length):this.setRange(c.ec,c.ec.textContent.length,c.ec,c.ec.textContent.length),this.history.push(!1)},detachRangeFormatElement:function(e,t,n,i,l){let o=this.getRange(),s=o.startOffset,r=o.endOffset,a=u.getListChildNodes(e,function(t){return t.parentNode===e}),c=e.parentNode,f=null,h=null,g=e.cloneNode(!1),m=[],p=u.isList(n),y=!1,C=!1,_=!1;function b(t,n,i,l){if(u.onlyZeroWidthSpace(n)&&(n.innerHTML=u.zeroWidthSpace,s=r=1),3===n.nodeType)return t.insertBefore(n,i),n;let o=(_?n:l).childNodes,a=n.cloneNode(!1),d=null,c=null;for(;o[0];)c=o[0],!u._notTextNode(c)||u.isBreak(c)||u.isListCell(a)?a.appendChild(c):(a.childNodes.length>0&&(d||(d=a),t.insertBefore(a,i),a=n.cloneNode(!1)),t.insertBefore(c,i),d||(d=c));if(a.childNodes.length>0){if(u.isListCell(t)&&u.isListCell(a)&&u.isList(i)){if(p){for(d=i;i;)a.appendChild(i),i=i.nextSibling;t.parentNode.insertBefore(a,t.nextElementSibling)}else{let t=l.nextElementSibling,n=u.detachNestedList(l,!1);if(e!==n||t!==l.nextElementSibling){let t=a.childNodes;for(;t[0];)l.appendChild(t[0]);e=n,C=!0}}}else t.insertBefore(a,i);d||(d=a)}return d}for(let l=0,o=a.length,s,r,E;l<o;l++)if(!(3===(s=a[l]).nodeType&&u.isList(g))){if(_=!1,i&&0===l&&(f=t&&t.length!==o&&t[0]!==s?g:e.previousSibling),t&&(r=t.indexOf(s)),t&&-1===r)g||(g=e.cloneNode(!1)),g.appendChild(s);else{if(t&&(E=t[r+1]),g&&g.children.length>0&&(c.insertBefore(g,e),g=null),!p&&u.isListCell(s)){if(E&&u.getElementDepth(s)!==u.getElementDepth(E)&&(u.isListCell(c)||u.getArrayItem(s.children,u.isList,!1))){let t=s.nextElementSibling,n=u.detachNestedList(s,!1);(e!==n||t!==s.nextElementSibling)&&(e=n,C=!0)}else{let t=s;s=u.createElement(i?t.nodeName:u.isList(e.parentNode)||u.isListCell(e.parentNode)?"LI":u.isCell(e.parentNode)?"DIV":d.defaultTag);let n=u.isListCell(s),l=t.childNodes;for(;l[0]&&(!u.isList(l[0])||n);)s.appendChild(l[0]);u.copyFormatAttributes(s,t),_=!0}}else s=s.cloneNode(!1);if(!C&&(i?(m.push(s),u.removeItem(a[l])):(n?(y||(c.insertBefore(n,e),y=!0),s=b(n,s,null,a[l])):s=b(c,s,e,a[l]),C||(t?(h=s,f||(f=s)):f||(f=h=s))),C)){C=_=!1,a=u.getListChildNodes(e,function(t){return t.parentNode===e}),g=e.cloneNode(!1),c=e.parentNode,l=-1,o=a.length;continue}}}let E=e.parentNode,v=e.nextSibling;g&&g.children.length>0&&E.insertBefore(g,v),n?f=n.previousSibling:f||(f=e.previousSibling),v=e.nextSibling!==g?e.nextSibling:g?g.nextSibling:null,0===e.children.length||0===e.textContent.length?u.removeItem(e):u.removeEmptyNode(e,null,!1);let w=null;if(i)w={cc:E,sc:f,so:s,ec:v,eo:r,removeArray:m};else{f||(f=h),h||(h=f);let e=u.getEdgeChildNodes(f,h.parentNode?f:h);w={cc:(e.sc||e.ec).parentNode,sc:e.sc,so:s,ec:e.ec,eo:r,removeArray:null}}if(this.effectNode=null,l)return w;!i&&w&&(t?this.setRange(w.sc,s,w.ec,r):this.setRange(w.sc,0,w.sc,0)),this.history.push(!1)},detachList:function(e,t){let n={},i=!1,l=!1,o=null,s=null,r=(function(e){return!this.isComponent(e)}).bind(u);for(let a=0,d=e.length,c,f,h,g;a<d;a++){if(h=a===d-1,f=u.getRangeFormatElement(e[a],r),g=u.isList(f),!c&&g)n={r:c=f,f:[u.getParentElement(e[a],"LI")]},0===a&&(i=!0);else if(c&&g){if(c!==f){let r=this.detachRangeFormatElement(n.f[0].parentNode,n.f,null,t,!0);f=e[a].parentNode,i&&(o=r.sc,i=!1),h&&(s=r.ec),g?(n={r:c=f,f:[u.getParentElement(e[a],"LI")]},h&&(l=!0)):c=null}else n.f.push(u.getParentElement(e[a],"LI")),h&&(l=!0)}if(h&&u.isList(c)){let e=this.detachRangeFormatElement(n.f[0].parentNode,n.f,null,t,!0);(l||1===d)&&(s=e.ec),i&&(o=e.sc||s)}}return{sc:o,ec:s}},nodeChange:function(e,t,n,i){this._resetRangeToTextNode();let l=this.getRange_addLine(this.getRange(),null);t=!!t&&t.length>0&&t,n=!!n&&n.length>0&&n;let o=!e,s=o&&!n&&!t,r=l.startContainer,a=l.startOffset,d=l.endContainer,c=l.endOffset;if(s&&l.collapsed&&u.isFormatElement(r.parentNode)||r===d&&1===r.nodeType&&u.isNonEditable(r)){let e=r.parentNode;if(!u.isListCell(e)||!u.getValues(e.style).some((function(e){return this._listKebab.indexOf(e)>-1}).bind(this)))return}if(l.collapsed&&!s&&1===r.nodeType&&!u.isBreak(r)&&!u.isComponent(r)){let e=null,t=r.childNodes[a];t&&(e=t.nextSibling?u.isBreak(t)?t:t.nextSibling:null);let n=u.createTextNode(u.zeroWidthSpace);r.insertBefore(n,e),this.setRange(n,1,n,1),r=(l=this.getRange()).startContainer,a=l.startOffset,d=l.endContainer,c=l.endOffset}u.isFormatElement(r)&&(r=r.childNodes[a]||r.firstChild,a=0),u.isFormatElement(d)&&(c=(d=d.childNodes[c]||d.lastChild).textContent.length),o&&(e=u.createElement("DIV"));let f=h.RegExp,g=e.nodeName;if(!s&&r===d&&!n&&e){let t=r,n=0,i=[],l=e.style;for(let e=0,t=l.length;e<t;e++)i.push(l[e]);let s=e.classList;for(let e=0,t=s.length;e<t;e++)i.push("."+s[e]);if(i.length>0){for(;!u.isFormatElement(t)&&!u.isWysiwygDiv(t);){for(let l=0;l<i.length;l++)if(1===t.nodeType){let s=i[l],r=!!/^\./.test(s)&&new f("\\s*"+s.replace(/^\./,"")+"(\\s+|$)","ig"),a=o?!!t.style[s]:!!t.style[s]&&!!e.style[s]&&t.style[s]===e.style[s],d=!1!==r&&(o?!!t.className.match(r):!!t.className.match(r)&&!!e.className.match(r));(a||d)&&n++}t=t.parentNode}if(n>=i.length)return}}let m={},p={},y,C="",_="",b="";if(t){for(let e=0,n=t.length,i;e<n;e++)i=t[e],/^\./.test(i)?_+=(_?"|":"\\s*(?:")+i.replace(/^\./,""):C+=(C?"|":"(?:;|^|\\s)(?:")+i;C&&(C+=")\\s*:[^;]*\\s*(?:;|$)",C=new f(C,"ig")),_&&(_+=")(?=\\s+|$)",_=new f(_,"ig"))}if(n){b="^(?:"+n[0];for(let e=1;e<n.length;e++)b+="|"+n[e];b+=")$",b=new f(b,"i")}let E=h.Boolean,v={v:!1},w=function(e){let t=e.cloneNode(!1);if(3===t.nodeType||u.isBreak(t))return t;if(s)return null;let n=!b&&o||b&&b.test(t.nodeName);if(n&&!i)return v.v=!0,null;let l=t.style.cssText,r="";C&&l.length>0&&(r=l.replace(C,"").trim())!==l&&(v.v=!0);let a=t.className,d="";return(_&&a.length>0&&(d=a.replace(_,"").trim())!==a&&(v.v=!0),o&&(_||!a)&&(C||!l)&&!r&&!d&&n)?(v.v=!0,null):(r||d||t.nodeName!==g||E(C)!==E(l)||E(_)!==E(a))&&(C&&l.length>0&&(t.style.cssText=r),t.style.cssText||t.removeAttribute("style"),_&&a.length>0&&(t.className=d.trim()),t.className.trim()||t.removeAttribute("class"),t.style.cssText||t.className||t.nodeName!==g&&!n)?t:(v.v=!0,null)},T=this.getSelectedElements(null);r=(l=this.getRange()).startContainer,a=l.startOffset,d=l.endContainer,c=l.endOffset,u.getFormatElement(r,null)||(r=u.getChildElement(T[0],function(e){return 3===e.nodeType},!1),a=0),u.getFormatElement(d,null)||(c=(d=u.getChildElement(T[T.length-1],function(e){return 3===e.nodeType},!1)).textContent.length);let N=u.getFormatElement(r,null)===u.getFormatElement(d,null),x=T.length-(N?0:1);y=e.cloneNode(!1);let S=s||o&&function(e){for(let t=0,n=e.length;t<n;t++)if(u._isMaintainedNode(e[t])||u._isSizeNode(e[t]))return!0;return!1}(n),B=o||u._isSizeNode(y),L=this._util_getMaintainedNode.bind(u,S,B),R=this._util_isMaintainedNode.bind(u,S,B);if(N){this._resetCommonListCell(T[0],t)&&(l=this.setRange(r,a,d,c));let e=this._nodeChange_oneLine(T[0],y,w,r,a,d,c,s,o,l.collapsed,v,L,R);m.container=e.startContainer,m.offset=e.startOffset,p.container=e.endContainer,p.offset=e.endOffset,m.container===p.container&&u.onlyZeroWidthSpace(m.container)&&(m.offset=p.offset=1),this._setCommonListStyle(e.ancestor,null)}else{let n=!1;x>0&&this._resetCommonListCell(T[x],t)&&(n=!0),this._resetCommonListCell(T[0],t)&&(n=!0),n&&this.setRange(r,a,d,c),x>0&&(y=e.cloneNode(!1),p=this._nodeChange_endLine(T[x],y,w,d,c,s,o,v,L,R));for(let n=x-1,i;n>0;n--)this._resetCommonListCell(T[n],t),y=e.cloneNode(!1),(i=this._nodeChange_middleLine(T[n],y,w,s,o,v,p.container)).endContainer&&i.ancestor.contains(i.endContainer)&&(p.ancestor=null,p.container=i.endContainer),this._setCommonListStyle(i.ancestor,null);y=e.cloneNode(!1),(m=this._nodeChange_startLine(T[0],y,w,r,a,s,o,v,L,R,p.container)).endContainer&&(p.ancestor=null,p.container=m.endContainer),x<=0?p=m:p.container||(p.ancestor=null,p.container=m.container,p.offset=m.container.textContent.length),this._setCommonListStyle(m.ancestor,null),this._setCommonListStyle(p.ancestor||u.getFormatElement(p.container),null)}this.controllersOff(),this.setRange(m.container,m.offset,p.container,p.offset),this.history.push(!1)},_resetCommonListCell:function(e,t){if(!u.isListCell(e))return;t||(t=this._listKebab);let n=u.getArrayItem(e.childNodes,function(e){return!u.isBreak(e)},!0),i=e.style,l=[],o=[],s=u.getValues(i);for(let e=0,n=this._listKebab.length;e<n;e++)s.indexOf(this._listKebab[e])>-1&&t.indexOf(this._listKebab[e])>-1&&(l.push(this._listCamel[e]),o.push(this._listKebab[e]));if(!l.length)return;let r=u.createElement("SPAN");for(let e=0,t=l.length;e<t;e++)r.style[l[e]]=i[o[e]],i.removeProperty(o[e]);let a=r.cloneNode(!1),c=null,f=!1;for(let t=0,i=n.length,o,s;t<i;t++)o=n[t],!d._textTagsMap[o.nodeName.toLowerCase()]&&(0===(s=u.getValues(o.style)).length||l.some(function(e){return -1===s.indexOf(e)})&&s.some(function(e){l.indexOf(e)})?(c=o.nextSibling,a.appendChild(o)):a.childNodes.length>0&&(e.insertBefore(a,c),a=r.cloneNode(!1),c=null,f=!0));return a.childNodes.length>0&&(e.insertBefore(a,c),f=!0),i.length||e.removeAttribute("style"),f},_setCommonListStyle:function(e,t){if(!u.isListCell(e))return;let n=u.getArrayItem((t||e).childNodes,function(e){return!u.isBreak(e)},!0);if(!(t=n[0])||n.length>1||1!==t.nodeType)return;let i=t.style,l=e.style,o=t.nodeName.toLowerCase(),s=!1;d._textTagsMap[o]===d._defaultCommand.bold.toLowerCase()&&(l.fontWeight="bold"),d._textTagsMap[o]===d._defaultCommand.italic.toLowerCase()&&(l.fontStyle="italic");let r=u.getValues(i);if(r.length>0)for(let e=0,t=this._listCamel.length;e<t;e++)r.indexOf(this._listKebab[e])>-1&&(l[this._listCamel[e]]=i[this._listCamel[e]],i.removeProperty(this._listKebab[e]),s=!0);if(this._setCommonListStyle(e,t),s&&!i.length){let e=t.childNodes,n=t.parentNode,i=t.nextSibling;for(;e.length>0;)n.insertBefore(e[0],i);u.removeItem(t)}},_stripRemoveNode:function(e){let t=e.parentNode;if(!e||3===e.nodeType||!t)return;let n=e.childNodes;for(;n[0];)t.insertBefore(n[0],e);t.removeChild(e)},_util_getMaintainedNode:function(e,t,n){return!n||e?null:this.getParentElement(n,this._isMaintainedNode.bind(this))||(t?null:this.getParentElement(n,this._isSizeNode.bind(this)))},_util_isMaintainedNode:function(e,t,n){if(!n||e||1!==n.nodeType)return!1;let i=this._isMaintainedNode(n);return this.getParentElement(n,this._isMaintainedNode.bind(this))?i:i||!t&&this._isSizeNode(n)},_nodeChange_oneLine:function(e,t,n,i,l,o,s,r,a,d,c,f,g){let m,p,y,C,_,b=i.parentNode;for(;!b.nextSibling&&!b.previousSibling&&!u.isFormatElement(b.parentNode)&&!u.isWysiwygDiv(b.parentNode)&&b.nodeName!==t.nodeName;)b=b.parentNode;if(!a&&b===o.parentNode&&b.nodeName===t.nodeName&&u.onlyZeroWidthSpace(i.textContent.slice(0,l))&&u.onlyZeroWidthSpace(o.textContent.slice(s))){let n=b.childNodes,r=!0;for(let e=0,t=n.length,l,s,a,d;e<t;e++){if(l=n[e],d=!u.onlyZeroWidthSpace(l),l===i){s=!0;continue}if(l===o){a=!0;continue}if(!s&&d||s&&a&&d){r=!1;break}}if(r)return u.copyTagAttributes(b,t),{ancestor:e,startContainer:i,startOffset:l,endContainer:o,endOffset:s}}c.v=!1;let E=[t],v=e.cloneNode(!1),w=i===o,T=i,N=l,x=o,S=s,B=!1,L=!1,R=h.RegExp;function k(e){let t=new R("(?:;|^|\\s)(?:"+C+"null)\\s*:[^;]*\\s*(?:;|$)","ig"),n="";return t&&e.style.cssText.length>0&&(n=t.test(e.style.cssText)),!n}if(!function i(l,o){let s=l.childNodes;for(let l=0,a=s.length,c;l<a;l++){let a,h=s[l];if(!h)continue;let b=o;if(!B&&h===T){let i=v;_=f(h);let l=u.createTextNode(1===T.nodeType?"":T.substringData(0,N)),s=u.createTextNode(1===T.nodeType?"":T.substringData(N,w&&S>=N?S-N:T.data.length-N));if(_){let e=f(o);if(e&&e.parentNode!==i){let t=e,n=null;for(;t.parentNode!==i;){for(o=n=t.parentNode.cloneNode(!1);t.childNodes[0];)n.appendChild(t.childNodes[0]);t.appendChild(n),t=t.parentNode}t.parentNode.appendChild(e)}_=_.cloneNode(!1)}u.onlyZeroWidthSpace(l)||o.appendChild(l);let r=f(o);for(r&&(_=r),_&&(i=_),p=h,m=[],C="";p!==i&&p!==e&&null!==p;)(c=g(p)?null:n(p))&&1===p.nodeType&&k(p)&&(m.push(c),C+=p.style.cssText.substr(0,p.style.cssText.indexOf(":"))+"|"),p=p.parentNode;let a=m.pop()||s;for(y=p=a;m.length>0;)p=m.pop(),y.appendChild(p),y=p;if(t.appendChild(a),i.appendChild(t),_&&!f(x)&&(t=t.cloneNode(!1),v.appendChild(t),E.push(t)),T=s,N=0,B=!0,p!==s&&p.appendChild(T),!w)continue}if(!L&&h===x){_=f(h);let i=u.createTextNode(1===x.nodeType?"":x.substringData(S,x.length-S)),l=u.createTextNode(w||1===x.nodeType?"":x.substringData(0,S));if(_?_=_.cloneNode(!1):g(t.parentNode)&&!_&&(t=t.cloneNode(!1),v.appendChild(t),E.push(t)),!u.onlyZeroWidthSpace(i)){p=h,C="",m=[];let t=[];for(;p!==v&&p!==e&&null!==p;)1===p.nodeType&&k(p)&&(g(p)?t.push(p.cloneNode(!1)):m.push(p.cloneNode(!1)),C+=p.style.cssText.substr(0,p.style.cssText.indexOf(":"))+"|"),p=p.parentNode;for(a=y=p=(m=m.concat(t)).pop()||i;m.length>0;)p=m.pop(),y.appendChild(p),y=p;v.appendChild(a),p.textContent=i.data}if(_&&a){let e=f(a);e&&(_=e)}for(p=h,m=[],C="";p!==v&&p!==e&&null!==p;)(c=g(p)?null:n(p))&&1===p.nodeType&&k(p)&&(m.push(c),C+=p.style.cssText.substr(0,p.style.cssText.indexOf(":"))+"|"),p=p.parentNode;let o=m.pop()||l;for(y=p=o;m.length>0;)p=m.pop(),y.appendChild(p),y=p;_?((t=t.cloneNode(!1)).appendChild(o),_.insertBefore(t,_.firstChild),v.appendChild(_),E.push(t),_=null):t.appendChild(o),x=l,S=l.data.length,L=!0,!r&&d&&(t=l,l.textContent=u.zeroWidthSpace),p!==l&&p.appendChild(x);continue}if(B){if(1===h.nodeType&&!u.isBreak(h)){u._isIgnoreNodeChange(h)?(v.appendChild(h.cloneNode(!0)),d||(t=t.cloneNode(!1),v.appendChild(t),E.push(t))):i(h,h);continue}p=h,m=[],C="";let l=[];for(;null!==p.parentNode&&p!==e&&p!==t;)c=L?p.cloneNode(!1):n(p),1===p.nodeType&&!u.isBreak(h)&&c&&k(p)&&(g(p)?_||l.push(c):m.push(c),C+=p.style.cssText.substr(0,p.style.cssText.indexOf(":"))+"|"),p=p.parentNode;let s=(m=m.concat(l)).pop()||h;for(y=p=s;m.length>0;)p=m.pop(),y.appendChild(p),y=p;if(!g(t.parentNode)||g(s)||u.onlyZeroWidthSpace(t)||(t=t.cloneNode(!1),v.appendChild(t),E.push(t)),!L&&!_&&g(s)){t=t.cloneNode(!1);let e=s.childNodes;for(let n=0,i=e.length;n<i;n++)t.appendChild(e[n]);s.appendChild(t),v.appendChild(s),E.push(t),o=t.children.length>0?p:t}else s===h?o=L?v:t:(L?v.appendChild(s):t.appendChild(s),o=p);if(_&&3===h.nodeType){if(f(h)){let e=u.getParentElement(o,(function(e){return this._isMaintainedNode(e.parentNode)||e.parentNode===v}).bind(u));_.appendChild(e),t=e.cloneNode(!1),E.push(t),v.appendChild(t)}else _=null}}a=h.cloneNode(!1),o.appendChild(a),1!==h.nodeType||u.isBreak(h)||(b=a),i(h,b)}}(e,v),a&&!r&&!c.v)return{ancestor:e,startContainer:i,startOffset:l,endContainer:o,endOffset:s};if(r=r&&a)for(let e=0;e<E.length;e++){let t,n,i,l=E[e];if(d)t=u.createTextNode(u.zeroWidthSpace),v.replaceChild(t,l);else{let e=l.childNodes;for(n=e[0];e[0];)i=e[0],v.insertBefore(i,l);u.removeItem(l)}0===e&&(d?T=x=t:(T=n,x=i))}else{if(a)for(let e=0;e<E.length;e++)this._stripRemoveNode(E[e]);d&&(T=x=t)}u.removeEmptyNode(v,t,!1),d&&(N=T.textContent.length,S=x.textContent.length);let O=r||0===x.textContent.length;u.isBreak(x)||0!==x.textContent.length||(u.removeItem(x),x=T),S=O?x.textContent.length:S;let F={s:0,e:0},D=u.getNodePath(T,v,F),A=!x.parentNode;A&&(x=T);let I={s:0,e:0},M=u.getNodePath(x,v,A||O?null:I);N+=F.s,S=d?N:A?T.textContent.length:O?S+F.s:S+I.s;let P=u.mergeSameTags(v,[D,M],!0);return e.parentNode.replaceChild(v,e),T=u.getNodeFromPath(D,v),x=u.getNodeFromPath(M,v),{ancestor:v,startContainer:T,startOffset:N+P[0],endContainer:x,endOffset:S+P[1]}},_nodeChange_startLine:function(e,t,n,i,l,o,s,r,a,d,c){let f,h,g,m,p=i.parentNode;for(;!p.nextSibling&&!p.previousSibling&&!u.isFormatElement(p.parentNode)&&!u.isWysiwygDiv(p.parentNode)&&p.nodeName!==t.nodeName;)p=p.parentNode;if(!s&&p.nodeName===t.nodeName&&!u.isFormatElement(p)&&!p.nextSibling&&u.onlyZeroWidthSpace(i.textContent.slice(0,l))){let n=!0,o=i.previousSibling;for(;o;){if(!u.onlyZeroWidthSpace(o)){n=!1;break}o=o.previousSibling}if(n)return u.copyTagAttributes(p,t),{ancestor:e,container:i,offset:l}}r.v=!1;let y=[t],C=e.cloneNode(!1),_=i,b=l,E=!1;if(!function i(l,o){let s=l.childNodes;for(let l=0,r=s.length,p,v;l<r;l++){let r=s[l];if(!r)continue;let w=o;if(E&&!u.isBreak(r)){if(1===r.nodeType){if(u._isIgnoreNodeChange(r)){if(t=t.cloneNode(!1),v=r.cloneNode(!0),C.appendChild(v),C.appendChild(t),y.push(t),c&&r.contains(c)){let e=u.getNodePath(c,r);c=u.getNodeFromPath(e,v)}}else i(r,r);continue}h=r,f=[];let l=[];for(;null!==h.parentNode&&h!==e&&h!==t;)p=n(h),1===h.nodeType&&p&&(d(h)?m||l.push(p):f.push(p)),h=h.parentNode;let s=(f=f.concat(l)).length>0,_=f.pop()||r;for(g=h=_;f.length>0;)h=f.pop(),g.appendChild(h),g=h;if(d(t.parentNode)&&!d(_)&&(t=t.cloneNode(!1),C.appendChild(t),y.push(t)),!m&&d(_)){t=t.cloneNode(!1);let e=_.childNodes;for(let n=0,i=e.length;n<i;n++)t.appendChild(e[n]);_.appendChild(t),C.appendChild(_),o=d(h)?t:h,y.push(t)}else s?(t.appendChild(_),o=h):o=t;if(m&&3===r.nodeType){if(a(r)){let e=u.getParentElement(o,(function(e){return this._isMaintainedNode(e.parentNode)||e.parentNode===C}).bind(u));m.appendChild(e),t=e.cloneNode(!1),y.push(t),C.appendChild(t)}else m=null}}if(!E&&r===_){let e=C;m=a(r);let i=u.createTextNode(1===_.nodeType?"":_.substringData(0,b)),l=u.createTextNode(1===_.nodeType?"":_.substringData(b,_.length-b));if(m){let t=a(o);if(t&&t.parentNode!==e){let n=t,i=null;for(;n.parentNode!==e;){for(o=i=n.parentNode.cloneNode(!1);n.childNodes[0];)i.appendChild(n.childNodes[0]);n.appendChild(i),n=n.parentNode}n.parentNode.appendChild(t)}m=m.cloneNode(!1)}u.onlyZeroWidthSpace(i)||o.appendChild(i);let s=a(o);for(s&&(m=s),m&&(e=m),h=o,f=[];h!==e&&null!==h;)p=n(h),1===h.nodeType&&p&&f.push(p),h=h.parentNode;let d=f.pop()||o;for(g=h=d;f.length>0;)h=f.pop(),g.appendChild(h),g=h;d!==o?(t.appendChild(d),o=h):o=t,u.isBreak(r)&&t.appendChild(r.cloneNode(!1)),e.appendChild(t),_=l,b=0,E=!0,o.appendChild(_);continue}(p=E?n(r):r.cloneNode(!1))&&(o.appendChild(p),1!==r.nodeType||u.isBreak(r)||(w=p)),i(r,w)}}(e,C),s&&!o&&!r.v)return{ancestor:e,container:i,offset:l,endContainer:c};if(o=o&&s)for(let e=0;e<y.length;e++){let t=y[e],n=t.childNodes,i=n[0];for(;n[0];)C.insertBefore(n[0],t);u.removeItem(t),0===e&&(_=i)}else if(s){t=t.firstChild;for(let e=0;e<y.length;e++)this._stripRemoveNode(y[e])}if(o||0!==C.childNodes.length){u.removeEmptyNode(C,t,!1),u.onlyZeroWidthSpace(C.textContent)&&(_=C.firstChild,b=0);let n={s:0,e:0},i=u.getNodePath(_,C,n);b+=n.s;let l=u.mergeSameTags(C,[i],!0);e.parentNode.replaceChild(C,e),_=u.getNodeFromPath(i,C),b+=l[0]}else e.childNodes?_=e.childNodes[0]:(_=u.createTextNode(u.zeroWidthSpace),e.appendChild(_));return{ancestor:C,container:_,offset:b,endContainer:c}},_nodeChange_middleLine:function(e,t,n,i,l,o,s){if(!l){let n=null;s&&e.contains(s)&&(n=u.getNodePath(s,e));let i=e.cloneNode(!0),l=t.nodeName,o=t.style.cssText,r=t.className,a=i.childNodes,d=0,c=a.length;for(let e;d<c&&3!==(e=a[d]).nodeType;d++)if(e.nodeName===l)e.style.cssText+=o,u.addClass(e,r);else if(!u.isBreak(e)&&u._isIgnoreNodeChange(e))continue;else if(1===c){c=(a=e.childNodes).length,d=-1;continue}else break;if(c>0&&d===c)return e.innerHTML=i.innerHTML,{ancestor:e,endContainer:n?u.getNodeFromPath(n,e):null}}o.v=!1;let r=e.cloneNode(!1),a=[t],d=!0;if(!function e(i,l){let o=i.childNodes;for(let i=0,c=o.length,f,h;i<c;i++){let c=o[i];if(!c)continue;let g=l;if(!u.isBreak(c)&&u._isIgnoreNodeChange(c)){if(t.childNodes.length>0&&(r.appendChild(t),t=t.cloneNode(!1)),h=c.cloneNode(!0),r.appendChild(h),r.appendChild(t),a.push(t),l=t,s&&c.contains(s)){let e=u.getNodePath(s,c);s=u.getNodeFromPath(e,h)}continue}(f=n(c))&&(d=!1,l.appendChild(f),1===c.nodeType&&(g=f)),u.isBreak(c)||e(c,g)}}(e,t),d||l&&!i&&!o.v)return{ancestor:e,endContainer:s};if(r.appendChild(t),i&&l)for(let e=0;e<a.length;e++){let t=a[e],n=t.childNodes;for(;n[0];)r.insertBefore(n[0],t);u.removeItem(t)}else if(l){t=t.firstChild;for(let e=0;e<a.length;e++)this._stripRemoveNode(a[e])}return u.removeEmptyNode(r,t,!1),u.mergeSameTags(r,null,!0),e.parentNode.replaceChild(r,e),{ancestor:r,endContainer:s}},_nodeChange_endLine:function(e,t,n,i,l,o,s,r,a,d){let c,f,h,g,m=i.parentNode;for(;!m.nextSibling&&!m.previousSibling&&!u.isFormatElement(m.parentNode)&&!u.isWysiwygDiv(m.parentNode)&&m.nodeName!==t.nodeName;)m=m.parentNode;if(!s&&m.nodeName===t.nodeName&&!u.isFormatElement(m)&&!m.previousSibling&&u.onlyZeroWidthSpace(i.textContent.slice(l))){let n=!0,o=i.nextSibling;for(;o;){if(!u.onlyZeroWidthSpace(o)){n=!1;break}o=o.nextSibling}if(n)return u.copyTagAttributes(m,t),{ancestor:e,container:i,offset:l}}r.v=!1;let p=[t],y=e.cloneNode(!1),C=i,_=l,b=!1;if(!function i(l,o){let s=l.childNodes;for(let l=s.length-1,r;0<=l;l--){let m=s[l];if(!m)continue;let E=o;if(b&&!u.isBreak(m)){if(1===m.nodeType){if(u._isIgnoreNodeChange(m)){t=t.cloneNode(!1);let e=m.cloneNode(!0);y.insertBefore(e,o),y.insertBefore(t,e),p.push(t)}else i(m,m);continue}f=m,c=[];let l=[];for(;null!==f.parentNode&&f!==e&&f!==t;)(r=n(f))&&1===f.nodeType&&(d(f)?g||l.push(r):c.push(r)),f=f.parentNode;let s=(c=c.concat(l)).length>0,C=c.pop()||m;for(h=f=C;c.length>0;)f=c.pop(),h.appendChild(f),h=f;if(d(t.parentNode)&&!d(C)&&(t=t.cloneNode(!1),y.insertBefore(t,y.firstChild),p.push(t)),!g&&d(C)){t=t.cloneNode(!1);let e=C.childNodes;for(let n=0,i=e.length;n<i;n++)t.appendChild(e[n]);C.appendChild(t),y.insertBefore(C,y.firstChild),p.push(t),o=t.children.length>0?f:t}else s?(t.insertBefore(C,t.firstChild),o=f):o=t;if(g&&3===m.nodeType){if(a(m)){let e=u.getParentElement(o,(function(e){return this._isMaintainedNode(e.parentNode)||e.parentNode===y}).bind(u));g.appendChild(e),t=e.cloneNode(!1),p.push(t),y.insertBefore(t,y.firstChild)}else g=null}}if(!b&&m===C){g=a(m);let e=u.createTextNode(1===C.nodeType?"":C.substringData(_,C.length-_)),i=u.createTextNode(1===C.nodeType?"":C.substringData(0,_));if(g){g=g.cloneNode(!1);let e=a(o);if(e&&e.parentNode!==y){let t=e,n=null;for(;t.parentNode!==y;){for(o=n=t.parentNode.cloneNode(!1);t.childNodes[0];)n.appendChild(t.childNodes[0]);t.appendChild(n),t=t.parentNode}t.parentNode.insertBefore(e,t.parentNode.firstChild)}g=g.cloneNode(!1)}else d(t.parentNode)&&!g&&(t=t.cloneNode(!1),y.appendChild(t),p.push(t));for(u.onlyZeroWidthSpace(e)||o.insertBefore(e,o.firstChild),f=o,c=[];f!==y&&null!==f;)(r=d(f)?null:n(f))&&1===f.nodeType&&c.push(r),f=f.parentNode;let l=c.pop()||o;for(h=f=l;c.length>0;)f=c.pop(),h.appendChild(f),h=f;l!==o?(t.insertBefore(l,t.firstChild),o=f):o=t,u.isBreak(m)&&t.appendChild(m.cloneNode(!1)),g?(g.insertBefore(t,g.firstChild),y.insertBefore(g,y.firstChild),g=null):y.insertBefore(t,y.firstChild),C=i,_=i.data.length,b=!0,o.insertBefore(C,o.firstChild);continue}(r=b?n(m):m.cloneNode(!1))&&(o.insertBefore(r,o.firstChild),1!==m.nodeType||u.isBreak(m)||(E=r)),i(m,E)}}(e,y),s&&!o&&!r.v)return{ancestor:e,container:i,offset:l};if(o=o&&s)for(let e=0;e<p.length;e++){let t=p[e],n=t.childNodes,i=null;for(;n[0];)i=n[0],y.insertBefore(i,t);u.removeItem(t),e===p.length-1&&(C=i,_=i.textContent.length)}else if(s){t=t.firstChild;for(let e=0;e<p.length;e++)this._stripRemoveNode(p[e])}if(o||0!==y.childNodes.length){if(!s&&0===t.textContent.length)return u.removeEmptyNode(y,null,!1),{ancestor:null,container:null,offset:0};u.removeEmptyNode(y,t,!1),u.onlyZeroWidthSpace(y.textContent)?_=(C=y.firstChild).textContent.length:u.onlyZeroWidthSpace(C)&&(C=t,_=1);let n={s:0,e:0},i=u.getNodePath(C,y,n);_+=n.s;let l=u.mergeSameTags(y,[i],!0);e.parentNode.replaceChild(y,e),C=u.getNodeFromPath(i,y),_+=l[0]}else e.childNodes?C=e.childNodes[0]:(C=u.createTextNode(u.zeroWidthSpace),e.appendChild(C));return{ancestor:y,container:C,offset:1===C.nodeType&&1===_?C.childNodes.length:_}},actionCall:function(t,n,i){if(n){if(/more/i.test(n)){if(i!==this._moreLayerActiveButton){let n=e.element.toolbar.querySelector("."+t);n&&(this._moreLayerActiveButton&&this.moreLayerOff(),this._moreLayerActiveButton=i,n.style.display="block",p._showToolbarBalloon(),p._showToolbarInline()),u.addClass(i,"on")}else e.element.toolbar.querySelector("."+this._moreLayerActiveButton.getAttribute("data-command"))&&(this.moreLayerOff(),p._showToolbarBalloon(),p._showToolbarInline());return}if(/container/.test(n)&&(null===this._menuTray[t]||i!==this.containerActiveButton)){this.callPlugin(t,this.containerOn.bind(this,i),i);return}if(this.isReadOnly&&u.arrayIncludes(this.resizingDisabledButtons,i))return;if(/submenu/.test(n)&&(null===this._menuTray[t]||i!==this.submenuActiveButton)){this.callPlugin(t,this.submenuOn.bind(this,i),i);return}if(/dialog/.test(n)){this.callPlugin(t,this.plugins[t].open.bind(this),i);return}/command/.test(n)?this.callPlugin(t,this.plugins[t].action.bind(this),i):/fileBrowser/.test(n)&&this.callPlugin(t,this.plugins[t].open.bind(this,null),i)}else t&&this.commandHandler(i,t);/submenu/.test(n)?this.submenuOff():/command/.test(n)||(this.submenuOff(),this.containerOff())},commandHandler:function(t,n){if(!m.isReadOnly||/copy|cut|selectAll|codeView|fullScreen|print|preview|showBlocks/.test(n))switch(n){case"copy":case"cut":this.execCommand(n);break;case"paste":break;case"selectAll":this.containerOff();let i=e.element.wysiwyg,l=u.getChildElement(i.firstChild,function(e){return 0===e.childNodes.length||3===e.nodeType},!1)||i.firstChild,o=u.getChildElement(i.lastChild,function(e){return 0===e.childNodes.length||3===e.nodeType},!0)||i.lastChild;if(!l||!o)return;if(u.isMedia(l)){let e=this.getFileComponent(l),t=u.createElement("BR"),n=u.createElement(d.defaultTag);n.appendChild(t),(l=e?e.component:l).parentNode.insertBefore(n,l),l=t}if(u.isMedia(o)){let e=u.createElement("BR"),t=u.createElement(d.defaultTag);t.appendChild(e),i.appendChild(t),o=e}p._showToolbarBalloon(this.setRange(l,0,o,o.textContent.length));break;case"codeView":this.toggleCodeView();break;case"fullScreen":this.toggleFullScreen(t);break;case"indent":case"outdent":this.indent(n);break;case"undo":this.history.undo();break;case"redo":this.history.redo();break;case"removeFormat":this.removeFormat(),this.focus();break;case"print":this.print();break;case"preview":this.preview();break;case"showBlocks":this.toggleDisplayBlocks();break;case"dir":this.setDir(d.rtl?"ltr":"rtl");break;case"dir_ltr":this.setDir("ltr");break;case"dir_rtl":this.setDir("rtl");break;case"save":if("function"==typeof d.callBackSave)d.callBackSave(this.getContents(!1),this._variable.isChanged);else if(this._variable.isChanged&&"function"==typeof y.save)y.save();else throw Error("[SUNEDITOR.core.commandHandler.fail] Please register call back function in creation option. (callBackSave : Function)");this._variable.isChanged=!1,e.tool.save&&e.tool.save.setAttribute("disabled",!0);break;default:n=d._defaultCommand[n.toLowerCase()]||n,this.commandMap[n]||(this.commandMap[n]=t);let s=this._variable.currentNodesMap,r=s.indexOf(n)>-1?null:u.createElement(n),a=n;/^SUB$/i.test(n)&&s.indexOf("SUP")>-1?a="SUP":/^SUP$/i.test(n)&&s.indexOf("SUB")>-1&&(a="SUB"),this.nodeChange(r,this._commandMapStyles[n]||null,[a],!1),this.focus()}},removeFormat:function(){this.nodeChange(null,null,null,null)},indent:function(e){let t=this.getRange(),n=this.getSelectedElements(null),i=[],l="indent"!==e,o=d.rtl?"marginRight":"marginLeft",s=t.startContainer,r=t.endContainer,a=t.startOffset,c=t.endOffset;for(let e=0,t=n.length,s,r;e<t;e++)s=n[e],u.isListCell(s)&&this.plugins.list?(l||s.previousElementSibling)&&i.push(s):(r=/\d+/.test(s.style[o])?u.getNumber(s.style[o],0):0,l?r-=25:r+=25,u.setStyle(s,o,r<=0?"":r+"px"));i.length>0&&this.plugins.list.editInsideList.call(this,l,i),this.effectNode=null,this.setRange(s,a,r,c),this.history.push(!1)},toggleDisplayBlocks:function(){let t=e.element.wysiwyg;u.toggleClass(t,"se-show-block"),u.hasClass(t,"se-show-block")?u.addClass(this._styleCommandMap.showBlocks,"active"):u.removeClass(this._styleCommandMap.showBlocks,"active"),this._resourcesStateChange()},toggleCodeView:function(){let t=this._variable.isCodeView;this.controllersOff(),u.setDisabledButtons(!t,this.codeViewDisabledButtons),t?(u.isNonEditable(e.element.wysiwygFrame)||this._setCodeDataToEditor(),e.element.wysiwygFrame.scrollTop=0,e.element.code.style.display="none",e.element.wysiwygFrame.style.display="block",this._variable._codeOriginCssText=this._variable._codeOriginCssText.replace(/(\s?display(\s+)?:(\s+)?)[a-zA-Z]+(?=;)/,"display: none"),this._variable._wysiwygOriginCssText=this._variable._wysiwygOriginCssText.replace(/(\s?display(\s+)?:(\s+)?)[a-zA-Z]+(?=;)/,"display: block"),"auto"!==d.height||d.codeMirrorEditor||(e.element.code.style.height="0px"),this._variable.isCodeView=!1,!this._variable.isFullScreen&&(this._notHideToolbar=!1,/balloon|balloon-always/i.test(d.mode)&&(e.element._arrow.style.display="",this._isInline=!1,this._isBalloon=!0,p._hideToolbar())),this.nativeFocus(),u.removeClass(this._styleCommandMap.codeView,"active"),u.isNonEditable(e.element.wysiwygFrame)||(this.history.push(!1),this.history._resetCachingButton())):(this._setEditorDataToCodeView(),this._variable._codeOriginCssText=this._variable._codeOriginCssText.replace(/(\s?display(\s+)?:(\s+)?)[a-zA-Z]+(?=;)/,"display: block"),this._variable._wysiwygOriginCssText=this._variable._wysiwygOriginCssText.replace(/(\s?display(\s+)?:(\s+)?)[a-zA-Z]+(?=;)/,"display: none"),this._variable.isFullScreen?e.element.code.style.height="100%":"auto"!==d.height||d.codeMirrorEditor||(e.element.code.style.height=e.element.code.scrollHeight>0?e.element.code.scrollHeight+"px":"auto"),d.codeMirrorEditor&&d.codeMirrorEditor.refresh(),this._variable.isCodeView=!0,!this._variable.isFullScreen&&(this._notHideToolbar=!0,this._isBalloon&&(e.element._arrow.style.display="none",e.element.toolbar.style.left="",this._isInline=!0,this._isBalloon=!1,p._showToolbarInline())),this._variable._range=null,e.element.code.focus(),u.addClass(this._styleCommandMap.codeView,"active")),this._checkPlaceholder(),this.isReadOnly&&u.setDisabledButtons(!0,this.resizingDisabledButtons),"function"==typeof y.toggleCodeView&&y.toggleCodeView(this._variable.isCodeView,this)},_setCodeDataToEditor:function(){let t=this._getCodeView();if(d.fullPage){let e=this._parser.parseFromString(t,"text/html");if(!this.options.__allowedScriptTag){let t=e.head.children;for(let n=0,i=t.length;n<i;n++)/^script$/i.test(t[n].tagName)&&(e.head.removeChild(t[n]),n--,i--)}let n=e.head.innerHTML;e.head.querySelector('link[rel="stylesheet"]')&&("auto"!==this.options.height||e.head.querySelector("style"))||(n+=u._setIframeCssTags(this.options)),this._wd.head.innerHTML=n,this._wd.body.innerHTML=this.convertContentsForEditor(e.body.innerHTML);let i=e.body.attributes;for(let e=0,t=i.length;e<t;e++)"contenteditable"!==i[e].name&&this._wd.body.setAttribute(i[e].name,i[e].value);if(!u.hasClass(this._wd.body,"sun-editor-editable")){let e=d._editableClass.split(" ");for(let t=0;t<e.length;t++)u.addClass(this._wd.body,d._editableClass[t])}}else e.element.wysiwyg.innerHTML=t.length>0?this.convertContentsForEditor(t):"<"+d.defaultTag+"><br></"+d.defaultTag+">"},_setEditorDataToCodeView:function(){let t=this.convertHTMLForCodeView(e.element.wysiwyg,!1),n="";if(d.fullPage){let e=u.getAttributesToString(this._wd.body,null);n="<!DOCTYPE html>\n<html>\n"+this._wd.head.outerHTML.replace(/>(?!\n)/g,">\n")+"<body "+e+">\n"+t+"</body>\n</html>"}else n=t;e.element.code.style.display="block",e.element.wysiwygFrame.style.display="none",this._setCodeView(n)},toggleFullScreen:function(t){let n=e.element.topArea,i=e.element.toolbar,l=e.element.editorArea,o=e.element.wysiwygFrame,s=e.element.code,r=this._variable;this.controllersOff();let a="none"===i.style.display||this._isInline&&!this._inlineToolbarAttr.isShow;r.isFullScreen?(r.isFullScreen=!1,o.style.cssText=r._wysiwygOriginCssText,s.style.cssText=r._codeOriginCssText,i.style.cssText="",l.style.cssText=r._editorAreaOriginCssText,n.style.cssText=r._originCssText,f.body.style.overflow=r._bodyOverflow,"auto"!==d.height||d.codeMirrorEditor||p._codeViewAutoHeight(),d.toolbarContainer&&d.toolbarContainer.appendChild(i),d.stickyToolbar>-1&&u.removeClass(i,"se-toolbar-sticky"),r._fullScreenAttrs.sticky&&!d.toolbarContainer&&(r._fullScreenAttrs.sticky=!1,e.element._stickyDummy.style.display="block",u.addClass(i,"se-toolbar-sticky")),this._isInline=r._fullScreenAttrs.inline,this._isBalloon=r._fullScreenAttrs.balloon,this._isInline&&p._showToolbarInline(),d.toolbarContainer&&u.removeClass(i,"se-toolbar-balloon"),p.onScroll_window(),t&&u.changeElement(t.firstElementChild,g.expansion),e.element.topArea.style.marginTop="",u.removeClass(this._styleCommandMap.fullScreen,"active")):(r.isFullScreen=!0,r._fullScreenAttrs.inline=this._isInline,r._fullScreenAttrs.balloon=this._isBalloon,(this._isInline||this._isBalloon)&&(this._isInline=!1,this._isBalloon=!1),d.toolbarContainer&&e.element.relative.insertBefore(i,l),n.style.position="fixed",n.style.top="0",n.style.left="0",n.style.width="100%",n.style.maxWidth="100%",n.style.height="100%",n.style.zIndex="2147483647",""!==e.element._stickyDummy.style.display&&(r._fullScreenAttrs.sticky=!0,e.element._stickyDummy.style.display="none",u.removeClass(i,"se-toolbar-sticky")),r._bodyOverflow=f.body.style.overflow,f.body.style.overflow="hidden",r._editorAreaOriginCssText=l.style.cssText,r._wysiwygOriginCssText=o.style.cssText,r._codeOriginCssText=s.style.cssText,l.style.cssText=i.style.cssText="",o.style.cssText=(o.style.cssText.match(/\s?display(\s+)?:(\s+)?[a-zA-Z]+;/)||[""])[0]+d._editorStyles.editor,s.style.cssText=(s.style.cssText.match(/\s?display(\s+)?:(\s+)?[a-zA-Z]+;/)||[""])[0],i.style.width=o.style.height=s.style.height="100%",i.style.position="relative",i.style.display="block",r.innerHeight_fullScreen=h.innerHeight-i.offsetHeight,l.style.height=r.innerHeight_fullScreen-d.fullScreenOffset+"px",t&&u.changeElement(t.firstElementChild,g.reduction),d.iframe&&"auto"===d.height&&(l.style.overflow="auto",this._iframeAutoHeight()),e.element.topArea.style.marginTop=d.fullScreenOffset+"px",u.addClass(this._styleCommandMap.fullScreen,"active")),a&&y.toolbar.hide(),"function"==typeof y.toggleFullScreen&&y.toggleFullScreen(this._variable.isFullScreen,this)},print:function(){let e=u.createElement("IFRAME");e.style.display="none",f.body.appendChild(e);let t=d.printTemplate?d.printTemplate.replace(/\{\{\s*contents\s*\}\}/i,this.getContents(!0)):this.getContents(!0),n=u.getIframeDocument(e),i=this._wd;if(d.iframe){let e=null!==d._printClass?'class="'+d._printClass+'"':d.fullPage?u.getAttributesToString(i.body,["contenteditable"]):'class="'+d._editableClass+'"';n.write("<!DOCTYPE html><html><head>"+i.head.innerHTML+"</head><body "+e+">"+t+"</body></html>")}else{let e=f.head.getElementsByTagName("link"),i=f.head.getElementsByTagName("style"),l="";for(let t=0,n=e.length;t<n;t++)l+=e[t].outerHTML;for(let e=0,t=i.length;e<t;e++)l+=i[e].outerHTML;n.write("<!DOCTYPE html><html><head>"+l+'</head><body class="'+(null!==d._printClass?d._printClass:d._editableClass)+'">'+t+"</body></html>")}this.showLoading(),h.setTimeout(function(){try{if(e.focus(),u.isIE_Edge||u.isChromium||f.documentMode||h.StyleMedia)try{e.contentWindow.document.execCommand("print",!1,null)}catch(t){e.contentWindow.print()}else e.contentWindow.print()}catch(e){throw Error("[SUNEDITOR.core.print.fail] error: "+e)}finally{m.closeLoading(),u.removeItem(e)}},1e3)},preview:function(){m.submenuOff(),m.containerOff(),m.controllersOff();let e=d.previewTemplate?d.previewTemplate.replace(/\{\{\s*contents\s*\}\}/i,this.getContents(!0)):this.getContents(!0),t=h.open("","_blank");t.mimeType="text/html";let n=this._wd;if(d.iframe){let i=null!==d._printClass?'class="'+d._printClass+'"':d.fullPage?u.getAttributesToString(n.body,["contenteditable"]):'class="'+d._editableClass+'"';t.document.write("<!DOCTYPE html><html><head>"+n.head.innerHTML+"<style>body {overflow:auto !important; margin: 10px auto !important; height:auto !important; outline:1px dashed #ccc;}</style></head><body "+i+">"+e+"</body></html>")}else{let n=f.head.getElementsByTagName("link"),i=f.head.getElementsByTagName("style"),l="";for(let e=0,t=n.length;e<t;e++)l+=n[e].outerHTML;for(let e=0,t=i.length;e<t;e++)l+=i[e].outerHTML;t.document.write('<!DOCTYPE html><html><head><meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"><title>'+a.toolbar.preview+"</title>"+l+'</head><body class="'+(null!==d._printClass?d._printClass:d._editableClass)+'" style="margin:10px auto !important; height:auto !important; outline:1px dashed #ccc;">'+e+"</body></html>")}},setDir:function(t){let n="rtl"===t,i=this._prevRtl!==n;this._prevRtl=d.rtl=n,i&&(this.plugins.align&&this.plugins.align.exchangeDir.call(this),e.tool.indent&&u.changeElement(e.tool.indent.firstElementChild,g.indent),e.tool.outdent&&u.changeElement(e.tool.outdent.firstElementChild,g.outdent));let l=e.element;n?(u.addClass(l.topArea,"se-rtl"),u.addClass(l.wysiwygFrame,"se-rtl")):(u.removeClass(l.topArea,"se-rtl"),u.removeClass(l.wysiwygFrame,"se-rtl"));let o=u.getListChildren(l.wysiwyg,function(e){return u.isFormatElement(e)&&(e.style.marginRight||e.style.marginLeft||e.style.textAlign)});for(let e=0,t=o.length,n,i,l;e<t;e++)l=(n=o[e]).style.marginRight,i=n.style.marginLeft,(l||i)&&(n.style.marginRight=i,n.style.marginLeft=l),"left"===(l=n.style.textAlign)?n.style.textAlign="right":"right"===l&&(n.style.textAlign="left");let s=e.tool;s.dir&&(u.changeTxt(s.dir.querySelector(".se-tooltip-text"),a.toolbar[d.rtl?"dir_ltr":"dir_rtl"]),u.changeElement(s.dir.firstElementChild,g[d.rtl?"dir_ltr":"dir_rtl"])),s.dir_ltr&&(n?u.removeClass(s.dir_ltr,"active"):u.addClass(s.dir_ltr,"active")),s.dir_rtl&&(n?u.addClass(s.dir_rtl,"active"):u.removeClass(s.dir_rtl,"active"))},setContents:function(t){this.removeRange();let n=null==t?"":this.convertContentsForEditor(t,null,null);if(this._variable.isCodeView){let e=this.convertHTMLForCodeView(n,!1);this._setCodeView(e)}else e.element.wysiwyg.innerHTML=n,this._resetComponents(),this.history.push(!1)},setIframeContents:function(e){if(!d.iframe)return!1;e.head&&(this._wd.head.innerHTML=this.options.__allowedScriptTag?e.head:e.head.replace(this.__scriptTagRegExp,"")),e.body&&(this._wd.body.innerHTML=this.convertContentsForEditor(e.body)),this._resetComponents()},getContents:function(t){let n=this.cleanHTML(e.element.wysiwyg.innerHTML,null,null),i=u.createElement("DIV");i.innerHTML=n;let l=u.getListChildren(i,function(e){return e.hasAttribute("contenteditable")});for(let e=0,t=l.length;e<t;e++)l[e].removeAttribute("contenteditable");if(!d.fullPage||t)return i.innerHTML;{let e=u.getAttributesToString(this._wd.body,["contenteditable"]);return"<!DOCTYPE html><html>"+this._wd.head.outerHTML+"<body "+e+">"+i.innerHTML+"</body></html>"}},getFullContents:function(e){return'<div class="sun-editor-editable'+(d.rtl?" se-rtl":"")+'">'+this.getContents(e)+"</div>"},_makeLine:function(e,t){let n=d.defaultTag;if(1===e.nodeType){if(this.__disallowedTagNameRegExp.test(e.nodeName))return"";if(/__se__tag/.test(e.className))return e.outerHTML;let i=u.getListChildNodes(e,function(e){return u.isSpanWithoutAttr(e)&&!u.getParentElement(e,u.isNotCheckingNode)})||[];for(let e=i.length-1;e>=0;e--)i[e].outerHTML=i[e].innerHTML;return!t||u.isFormatElement(e)||u.isRangeFormatElement(e)||u.isComponent(e)||u.isFigures(e)||u.isAnchor(e)&&u.isMedia(e.firstElementChild)?u.isSpanWithoutAttr(e)?e.innerHTML:e.outerHTML:"<"+n+">"+(u.isSpanWithoutAttr(e)?e.innerHTML:e.outerHTML)+"</"+n+">"}if(3===e.nodeType){if(!t)return u._HTMLConvertor(e.textContent);let i=e.textContent.split(/\n/g),l="";for(let e=0,t=i.length,o;e<t;e++)(o=i[e].trim()).length>0&&(l+="<"+n+">"+u._HTMLConvertor(o)+"</"+n+">");return l}return 8===e.nodeType&&this._allowHTMLComments?"<!--"+e.textContent.trim()+"-->":""},_tagConvertor:function(e){if(!this._disallowedTextTagsRegExp)return e;let t=d._textTagsMap;return e.replace(this._disallowedTextTagsRegExp,function(e,n,i,l){return n+("string"==typeof t[i]?t[i]:i)+(l?" "+l:"")})},_deleteDisallowedTags:function(e){return e=e.replace(this.__disallowedTagsRegExp,"").replace(/<[a-z0-9]+\:[a-z0-9]+[^>^\/]*>[^>]*<\/[a-z0-9]+\:[a-z0-9]+>/gi,""),/\bfont\b/i.test(this.options._editorTagsWhitelist)||(e=e.replace(/(<\/?)font(\s?)/gi,"$1span$2")),e.replace(this.editorTagsWhitelistRegExp,"").replace(this.editorTagsBlacklistRegExp,"")},_convertFontSize:function(e,t){let n=this._w.Math,i=t.match(/(\d+(?:\.\d+)?)(.+)/),l=i?1*i[1]:u.fontValueMap[t],o=i?i[2]:"rem",s=l;switch(/em/.test(o)?s=n.round(l/.0625):"pt"===o?s=n.round(1.333*l):"%"===o&&(s=l/100),e){case"em":case"rem":case"%":return(.0625*s).toFixed(2)+e;case"pt":return n.floor(s/1.333)+e;default:return s+e}},_cleanStyle:function(e,t,n){let i=(e.match(/style\s*=\s*(?:"|')[^"']*(?:"|')/)||[])[0];if(/span/i.test(n)&&!i&&(e.match(/<[^\s]+\s(.+)/)||[])[1]){let t=(e.match(/\ssize="([^"]+)"/i)||[])[1],n=(e.match(/\sface="([^"]+)"/i)||[])[1],l=(e.match(/\scolor="([^"]+)"/i)||[])[1];(t||n||l)&&(i='style="'+(t?"font-size:"+this.util.getNumber(t/3.333,1)+"rem;":"")+(n?"font-family:"+n+";":"")+(l?"color:"+l+";":"")+'"')}if(i){t||(t=[]);let e=i.replace(/&quot;/g,"").match(this._cleanStyleRegExp[n]);if(e){let n=[];for(let t=0,i=e.length,l;t<i;t++)if((l=e[t].match(/([a-zA-Z0-9-]+)(:)([^"']+)/))&&!/inherit|initial|revert|unset/i.test(l[3])){let e=u.kebabToCamelCase(l[1].trim()),t=this.wwComputedStyle[e]?this.wwComputedStyle[e].replace(/"/g,""):"",i=l[3].trim();switch(e){case"fontFamily":if(!d.plugins.font||-1===d.font.indexOf(i))continue;break;case"fontSize":if(!d.plugins.fontSize)continue;this._cleanStyleRegExp.fontSizeUnit.test(l[0])||(l[0]=l[0].replace((l[0].match(/:\s*([^;]+)/)||[])[1],this._convertFontSize.bind(this,d.fontSizeUnit)));break;case"color":if(!d.plugins.fontColor||/rgba\(([0-9]+\s*,\s*){3}0\)|windowtext/i.test(i))continue;break;case"backgroundColor":if(!d.plugins.hiliteColor||/rgba\(([0-9]+\s*,\s*){3}0\)|windowtext/i.test(i))continue}t!==i&&n.push(l[0])}n.length>0&&t.push('style="'+n.join(";")+'"')}}return t},_cleanTags:function(e,t,n){if(/^<[a-z0-9]+\:[a-z0-9]+/i.test(t))return t;let i=null,l=n.match(/(?!<)[a-zA-Z0-9\-]+/)[0].toLowerCase(),o=this._attributesTagsBlacklist[l];t=t.replace(/\s(?:on[a-z]+)\s*=\s*(")[^"]*\1/ig,""),t=o?t.replace(o,""):t.replace(this._attributesBlacklistRegExp,"");let s=this._attributesTagsWhitelist[l];if(i=s?t.match(s):t.match(e?this._attributesWhitelistRegExp:this._attributesWhitelistRegExp_all_data),e||"span"===l||"li"===l||this._cleanStyleRegExp[l]){if("a"===l){let e=t.match(/(?:(?:id|name)\s*=\s*(?:"|')[^"']*(?:"|'))/g);e&&(i||(i=[]),i.push(e[0]))}else i&&/style=/i.test(i.toString())||(("span"===l||"li"===l)&&(i=this._cleanStyle(t,i,"span")),this._cleanStyleRegExp[l]?i=this._cleanStyle(t,i,l):/^(P|DIV|H[1-6]|PRE)$/i.test(l)&&(i=this._cleanStyle(t,i,"format")))}else{let e=t.match(/style\s*=\s*(?:"|')[^"']*(?:"|')/);e&&!i?i=[e[0]]:e&&!i.some(function(e){return/^style/.test(e.trim())})&&i.push(e[0])}if(u.isFigures(l)){let e=t.match(/style\s*=\s*(?:"|')[^"']*(?:"|')/);i||(i=[]),e&&i.push(e[0])}if(i)for(let e=0,t=i.length,l;e<t;e++)l=/^(?:href|src)\s*=\s*('|"|\s)*javascript\s*\:/i.test(i[e].trim())?"":i[e],n+=(/^\s/.test(l)?"":" ")+l;return n},_editFormat:function(e){let t="",n,i=e.childNodes;for(let e=0,l=i.length,o;e<l;e++)8===(o=i[e]).nodeType?t+="<!-- "+o.textContent+" -->":u.isFormatElement(o)||u.isRangeFormatElement(o)||u.isComponent(o)||/meta/i.test(o.nodeName)?(n&&(t+=n.outerHTML,n=null),t+=o.outerHTML):(n||(n=u.createElement(d.defaultTag)),n.appendChild(o),e--,l--);return n&&(t+=n.outerHTML),f.createRange().createContextualFragment(t)},_convertListCell:function(e){let t="";for(let n=0,i=e.length,l;n<i;n++)1===(l=e[n]).nodeType?u.isList(l)?t+=l.innerHTML:u.isListCell(l)?t+=l.outerHTML:u.isFormatElement(l)?t+="<li>"+(l.innerHTML.trim()||"<br>")+"</li>":u.isRangeFormatElement(l)&&!u.isTable(l)?t+=this._convertListCell(l):t+="<li>"+l.outerHTML+"</li>":t+="<li>"+(l.textContent||"<br>")+"</li>";return t},_isFormatData:function(e){let t=!1;for(let n=0,i=e.length,l;n<i;n++)if(1===(l=e[n]).nodeType&&!u.isTextStyleElement(l)&&!u.isBreak(l)&&!this.__disallowedTagNameRegExp.test(l.nodeName)){t=!0;break}return t},cleanHTML:function(e,t,n){if(!d.strictMode)return u.htmlCompress(e);e=this._deleteDisallowedTags(this._parser.parseFromString(u.htmlCompress(e),"text/html").body.innerHTML).replace(/(<[a-zA-Z0-9\-]+)[^>]*(?=>)/g,this._cleanTags.bind(this,!0)).replace(/<br\/?>$/i,"");let i=f.createRange().createContextualFragment(e);try{u._consistencyCheckOfHTML(i,this._htmlCheckWhitelistRegExp,this._htmlCheckBlacklistRegExp,this._classNameFilter,d.strictHTMLValidation)}catch(e){console.warn("[SUNEDITOR.cleanHTML.consistencyCheck.fail] "+e)}if(this.managedTagsInfo&&this.managedTagsInfo.query){let e=i.querySelectorAll(this.managedTagsInfo.query);for(let t=0,n=e.length,i,l;t<n;t++){l=[].slice.call(e[t].classList);for(let n=0,o=l.length;n<o;n++)if(i=this.managedTagsInfo.map[l[n]]){i(e[t]);break}}}let l=i.childNodes,o="",s=this._isFormatData(l);s&&(l=this._editFormat(i).childNodes);for(let e=0,t=l.length,n;e<t;e++){if(n=l[e],this.__allowedScriptRegExp.test(n.nodeName)){o+=n.outerHTML;continue}o+=this._makeLine(n,s)}return(o=u.htmlRemoveWhiteSpace(o))?(t&&(o=o.replace("string"==typeof t?u.createTagsWhitelist(t):t,"")),n&&(o=o.replace("string"==typeof n?u.createTagsBlacklist(n):n,""))):o=e,this._tagConvertor(o)},convertContentsForEditor:function(e){if(!d.strictMode)return u.htmlCompress(e);e=this._deleteDisallowedTags(this._parser.parseFromString(u.htmlCompress(e),"text/html").body.innerHTML).replace(/(<[a-zA-Z0-9\-]+)[^>]*(?=>)/g,this._cleanTags.bind(this,!0));let t=f.createRange().createContextualFragment(e);try{u._consistencyCheckOfHTML(t,this._htmlCheckWhitelistRegExp,this._htmlCheckBlacklistRegExp,this._classNameFilter,d.strictHTMLValidation)}catch(e){console.warn("[SUNEDITOR.convertContentsForEditor.consistencyCheck.fail] "+e)}if(this.managedTagsInfo&&this.managedTagsInfo.query){let e=t.querySelectorAll(this.managedTagsInfo.query);for(let t=0,n=e.length,i,l;t<n;t++){l=[].slice.call(e[t].classList);for(let n=0,o=l.length;n<o;n++)if(i=this.managedTagsInfo.map[l[n]]){i(e[t]);break}}}let n=t.childNodes,i="",l=null;for(let e=0,t;e<n.length;e++){if(t=n[e],this.__allowedScriptRegExp.test(t.nodeName)){i+=t.outerHTML;continue}if(!u.isFormatElement(t)&&!u.isRangeFormatElement(t)&&!u.isComponent(t)&&!u.isFigures(t)&&8!==t.nodeType&&!/__se__tag/.test(t.className)){if(l||(l=u.createElement(d.defaultTag)),l.appendChild(t),n[--e+1]&&!u.isFormatElement(n[e+1]))continue;t=l,l=null}l&&(i+=this._makeLine(l,!0),l=null),i+=this._makeLine(t,!0)}return(l&&(i+=this._makeLine(l,!0)),0===i.length)?"<"+d.defaultTag+"><br></"+d.defaultTag+">":(i=u.htmlRemoveWhiteSpace(i),this._tagConvertor(i))},convertHTMLForCodeView:function(e,t){let n="",i=h.RegExp,l=new i("^(BLOCKQUOTE|PRE|TABLE|THEAD|TBODY|TR|TH|TD|OL|UL|IMG|IFRAME|VIDEO|AUDIO|FIGURE|FIGCAPTION|HR|BR|CANVAS|SELECT)$","i"),o="string"==typeof e?f.createRange().createContextualFragment(e):e,s=(function(e){return this.isFormatElement(e)||this.isComponent(e)}).bind(u),r=t?"":"\n",a=t?0:1*this._variable.codeIndent;return a=a>0?new h.Array(a+1).join(" "):"",!function e(t,o){let d=t.childNodes,c=l.test(t.nodeName),f=c?o:"";for(let g=0,m=d.length,p,y,C,_,b,E;g<m;g++){if(p=d[g],y=(_=l.test(p.nodeName))?r:"",C=!s(p)||c||/^(TH|TD)$/i.test(t.nodeName)?"":r,8===p.nodeType){n+="\n<!-- "+p.textContent.trim()+" -->"+y;continue}if(3===p.nodeType){u.isList(p.parentElement)||(n+=u._HTMLConvertor(/^\n+$/.test(p.data)?"":p.data));continue}if(0===p.childNodes.length){n+=(/^HR$/i.test(p.nodeName)?r:"")+(/^PRE$/i.test(p.parentElement.nodeName)&&/^BR$/i.test(p.nodeName)?"":f)+p.outerHTML+y;continue}p.outerHTML?(b=p.nodeName.toLowerCase(),n+=(C||(c?"":y))+(E=f||_?o:"")+p.outerHTML.match(i("<"+b+"[^>]*>","i"))[0]+y,e(p,o+a,""),n+=(/\n$/.test(n)?E:"")+"</"+b+">"+(C||y||c?r:/^(TH|TD)$/i.test(p.nodeName)?r:"")):n+=new h.XMLSerializer().serializeToString(p)}}(o,""),n.trim()+r},addDocEvent:function(e,t,n){f.addEventListener(e,t,n),d.iframe&&this._wd.addEventListener(e,t)},removeDocEvent:function(e,t){f.removeEventListener(e,t),d.iframe&&this._wd.removeEventListener(e,t)},_charCount:function(e){let t=d.maxCharCount,n=d.charCounterType,i=0;if(e&&(i=this.getCharLength(e,n)),this._setCharCount(),t>0){let e=!1,l=y.getCharCount(n);if(l>t){if(e=!0,i>0){this._editorRange();let e=this.getRange(),n=e.endOffset-1,i=this.getSelectionNode().textContent,o=e.endOffset-(l-t);this.getSelectionNode().textContent=i.slice(0,o<0?0:o)+i.slice(e.endOffset,i.length),this.setRange(e.endContainer,n,e.endContainer,n)}}else l+i>t&&(e=!0);if(e&&(this._callCounterBlink(),i>0))return!1}return!0},checkCharCount:function(e,t){if(d.maxCharCount){let n=t||d.charCounterType,i=this.getCharLength("string"==typeof e?e:this._charTypeHTML&&1===e.nodeType?e.outerHTML:e.textContent,n);if(i>0&&i+y.getCharCount(n)>d.maxCharCount)return this._callCounterBlink(),!1}return!0},getCharLength:function(e,t){return/byte/.test(t)?u.getByteLength(e):e.length},resetResponsiveToolbar:function(){m.controllersOff();let t=p._responsiveButtonSize;if(t){let n=0;n=(m._isBalloon||m._isInline)&&"auto"===d.toolbarWidth?e.element.topArea.offsetWidth:e.element.toolbar.offsetWidth;let i="default";for(let e=1,l=t.length;e<l;e++)if(n<t[e]){i=t[e]+"";break}p._responsiveCurrentSize!==i&&(p._responsiveCurrentSize=i,y.setToolbarButtons(p._responsiveButtons[i]))}},_setCharCount:function(){e.element.charCounter&&h.setTimeout((function(e,t){this.textContent&&e&&(this.textContent=e.getCharCount(t.charCounterType))}).bind(e.element.charCounter,y,d),0)},_callCounterBlink:function(){let t=e.element.charWrapper;t&&!u.hasClass(t,"se-blink")&&(u.addClass(t,"se-blink"),h.setTimeout(function(){u.removeClass(t,"se-blink")},600))},_checkComponents:function(){for(let e=0,t=this._fileInfoPluginsCheck.length;e<t;e++)this._fileInfoPluginsCheck[e]()},_resetComponents:function(){for(let e=0,t=this._fileInfoPluginsReset.length;e<t;e++)this._fileInfoPluginsReset[e]()},_setCodeView:function(t){d.codeMirrorEditor?d.codeMirrorEditor.getDoc().setValue(t):e.element.code.value=t},_getCodeView:function(){return d.codeMirrorEditor?d.codeMirrorEditor.getDoc().getValue():e.element.code.value},_setKeyEffect:function(e){let t=this.commandMap,i=this.activePlugins;for(let l in t)!(e.indexOf(l)>-1)&&u.hasOwn(t,l)&&(i.indexOf(l)>-1?n[l].active.call(this,null):t.OUTDENT&&/^OUTDENT$/i.test(l)?u.isImportantDisabled(t.OUTDENT)||t.OUTDENT.setAttribute("disabled",!0):t.INDENT&&/^INDENT$/i.test(l)?u.isImportantDisabled(t.INDENT)||t.INDENT.removeAttribute("disabled"):u.removeClass(t[l],"active"))},_init:function(i,l){let s,a;let c=h.RegExp;this._ww=d.iframe?e.element.wysiwygFrame.contentWindow:h,this._wd=f,this._charTypeHTML="byte-html"===d.charCounterType,this.wwComputedStyle=h.getComputedStyle(e.element.wysiwyg),this._editorHeight=e.element.wysiwygFrame.offsetHeight,this._editorHeightPadding=u.getNumber(this.wwComputedStyle.getPropertyValue("padding-top"))+u.getNumber(this.wwComputedStyle.getPropertyValue("padding-bottom")),this._classNameFilter=(function(e){return this.test(e)?e:""}).bind(d.allowedClassNames);let g=d.__allowedScriptTag?"":"script|";if(this.__scriptTagRegExp=new c("<(script)[^>]*>([\\s\\S]*?)<\\/\\1>|<script[^>]*\\/?>","gi"),this.__disallowedTagsRegExp=new c("<("+g+"style)[^>]*>([\\s\\S]*?)<\\/\\1>|<("+g+"style)[^>]*\\/?>","gi"),this.__disallowedTagNameRegExp=new c("^("+g+"meta|link|style|[a-z]+:[a-z]+)$","i"),this.__allowedScriptRegExp=new c("^"+(d.__allowedScriptTag?"script":"")+"$","i"),!d.iframe&&"function"==typeof h.ShadowRoot){let t=e.element.wysiwygFrame;for(;t;){if(t.shadowRoot){this._shadowRoot=t.shadowRoot;break}if(t instanceof h.ShadowRoot){this._shadowRoot=t;break}t=t.parentNode}this._shadowRoot&&(this._shadowRootControllerEventTarget=[])}let m=h.Object.keys(d._textTagsMap),p=d.addTagsWhitelist?d.addTagsWhitelist.split("|").filter(function(e){return/b|i|ins|s|strike/i.test(e)}):[];for(let e=0;e<p.length;e++)m.splice(m.indexOf(p[e].toLowerCase()),1);this._disallowedTextTagsRegExp=0===m.length?null:new c("(<\\/?)("+m.join("|")+")\\b\\s*([^>^<]+)?\\s*(?=>)","gi");let y=function(e,t){return e?"*"===e?"[a-z-]+":t?e+"|"+t:e:"^"},C="contenteditable|colspan|rowspan|target|href|download|rel|src|alt|class|type|origin-size|controls|autoplay|loop|muted|poster|preload|playsinline|allowfullscreen|sandbox|loading|allow|referrerpolicy|frameborder|scrolling";this._allowHTMLComments=d._editorTagsWhitelist.indexOf("//")>-1||"*"===d._editorTagsWhitelist,this._htmlCheckWhitelistRegExp=new c("^("+y(d._editorTagsWhitelist.replace("|//",""),"")+")$","i"),this._htmlCheckBlacklistRegExp=new c("^("+(d.tagsBlacklist||"^")+")$","i"),this.editorTagsWhitelistRegExp=u.createTagsWhitelist(y(d._editorTagsWhitelist.replace("|//","|<!--|-->"),"")),this.editorTagsBlacklistRegExp=u.createTagsBlacklist(d.tagsBlacklist.replace("|//","|<!--|-->")),this.pasteTagsWhitelistRegExp=u.createTagsWhitelist(y(d.pasteTagsWhitelist,"")),this.pasteTagsBlacklistRegExp=u.createTagsBlacklist(d.pasteTagsBlacklist);let _='\\s*=\\s*(")[^"]*\\1',b=d.attributesWhitelist,E={},v="";if(b)for(let e in b)!u.hasOwn(b,e)||/^on[a-z]+$/i.test(b[e])||("all"===e?v=y(b[e],C):E[e]=new c("\\s(?:"+y(b[e],"")+")"+_,"ig"));this._attributesWhitelistRegExp=new c("\\s(?:"+(v||C+"|data-format|data-size|data-file-size|data-file-name|data-origin|data-align|data-image-link|data-rotate|data-proportion|data-percentage|data-exp|data-font-size")+")"+_,"ig"),this._attributesWhitelistRegExp_all_data=new c("\\s(?:"+(v||C)+"|data-[a-z0-9\\-]+)"+_,"ig"),this._attributesTagsWhitelist=E;let w=d.attributesBlacklist;if(E={},v="",w)for(let e in w)u.hasOwn(w,e)&&("all"===e?v=y(w[e],""):E[e]=new c("\\s(?:"+y(w[e],"")+")"+_,"ig"));this._attributesBlacklistRegExp=new c("\\s(?:"+(v||"^")+")"+_,"ig"),this._attributesTagsBlacklist=E,this._isInline=/inline/i.test(d.mode),this._isBalloon=/balloon|balloon-always/i.test(d.mode),this._isBalloonAlways=/balloon-always/i.test(d.mode),this._cachingButtons(),this._fileInfoPluginsCheck=[],this._fileInfoPluginsReset=[],this.managedTagsInfo={query:"",map:{}};let T=[];this.activePlugins=[],this._fileManager.tags=[],this._fileManager.pluginMap={};let N=[];for(let e in n)if(u.hasOwn(n,e)){if(s=n[e],a=t[e],(s.active||s.action)&&a&&this.callPlugin(e,null,a),"function"==typeof s.checkFileInfo&&"function"==typeof s.resetFileInfo&&(this.callPlugin(e,null,a),this._fileInfoPluginsCheck.push(s.checkFileInfo.bind(this)),this._fileInfoPluginsReset.push(s.resetFileInfo.bind(this))),h.Array.isArray(s.fileTags)){let t=s.fileTags;this.callPlugin(e,null,a),this._fileManager.tags=this._fileManager.tags.concat(t),N.push(e);for(let n=0,i=t.length;n<i;n++)this._fileManager.pluginMap[t[n].toLowerCase()]=e}if(s.managedTags){let e=s.managedTags();T.push("."+e.className),this.managedTagsInfo.map[e.className]=e.method.bind(this)}}this.managedTagsInfo.query=T.toString(),this._fileManager.queryString=this._fileManager.tags.join(","),this._fileManager.regExp=new c("^("+(this._fileManager.tags.join("|")||"^")+")$","i"),this._fileManager.pluginRegExp=new c("^("+(0===N.length?"^":N.join("|"))+")$","i"),this._variable._originCssText=e.element.topArea.style.cssText,this._placeholder=e.element.placeholder,this._lineBreaker=e.element.lineBreaker,this._lineBreakerButton=this._lineBreaker.querySelector("button"),this.history=(0,o.Z)(this,this._onChange_historyStack.bind(this)),this.addModule([r.Z]),d.iframe&&(this._wd=e.element.wysiwygFrame.contentDocument,e.element.wysiwyg=this._wd.body,d._editorStyles.editor&&(e.element.wysiwyg.style.cssText=d._editorStyles.editor),"auto"===d.height&&(this._iframeAuto=this._wd.body)),this._initWysiwygArea(i,l)},_cachingButtons:function(){this.codeViewDisabledButtons=e.element._buttonTray.querySelectorAll('.se-menu-list button[data-display]:not([class~="se-code-view-enabled"]):not([data-display="MORE"])'),this.resizingDisabledButtons=e.element._buttonTray.querySelectorAll('.se-menu-list button[data-display]:not([class~="se-resizing-enabled"]):not([data-display="MORE"])');let t=e.tool,n=this.commandMap;n.INDENT=t.indent,n.OUTDENT=t.outdent,n[d.textTags.bold.toUpperCase()]=t.bold,n[d.textTags.underline.toUpperCase()]=t.underline,n[d.textTags.italic.toUpperCase()]=t.italic,n[d.textTags.strike.toUpperCase()]=t.strike,n[d.textTags.sub.toUpperCase()]=t.subscript,n[d.textTags.sup.toUpperCase()]=t.superscript,this._styleCommandMap={fullScreen:t.fullScreen,showBlocks:t.showBlocks,codeView:t.codeView},this._saveButtonStates()},_initWysiwygArea:function(t,n){e.element.wysiwyg.innerHTML=t?n:this.convertContentsForEditor(("string"==typeof n?n:/^TEXTAREA$/i.test(e.element.originElement.nodeName)?e.element.originElement.value:e.element.originElement.innerHTML)||"")},_resourcesStateChange:function(){this._iframeAutoHeight(),this._checkPlaceholder()},_onChange_historyStack:function(){this.hasFocus&&p._applyTagEffects(),this._variable.isChanged=!0,e.tool.save&&e.tool.save.removeAttribute("disabled"),y.onChange&&y.onChange(this.getContents(!0),this),"block"===e.element.toolbar.style.display&&p._showToolbarBalloon()},_iframeAutoHeight:function(){this._iframeAuto?h.setTimeout(function(){let t=m._iframeAuto.offsetHeight;e.element.wysiwygFrame.style.height=t+"px",u.isResizeObserverSupported||m.__callResizeFunction(t,null)}):u.isResizeObserverSupported||m.__callResizeFunction(e.element.wysiwygFrame.offsetHeight,null)},__callResizeFunction:function(e,t){e=-1===e?t.borderBoxSize&&t.borderBoxSize[0]?t.borderBoxSize[0].blockSize:t.contentRect.height+this._editorHeightPadding:e,this._editorHeight!==e&&("function"==typeof y.onResizeEditor&&y.onResizeEditor(e,this._editorHeight,m,t),this._editorHeight=e)},_checkPlaceholder:function(){if(this._placeholder){if(this._variable.isCodeView){this._placeholder.style.display="none";return}let t=e.element.wysiwyg;!u.onlyZeroWidthSpace(t.textContent)||t.querySelector(u._allowedEmptyNodeList)||(t.innerText.match(/\n/g)||"").length>1?this._placeholder.style.display="none":this._placeholder.style.display="block"}},_setDefaultFormat:function(e){let t,n,i;if(this._fileManager.pluginRegExp.test(this.currentControllerName))return;let l=this.getRange(),o=l.commonAncestorContainer,s=l.startContainer,r=u.getRangeFormatElement(o,null),a=u.getParentElement(o,u.isComponent);if(!a||u.isTable(a)){if(1===o.nodeType&&"true"===o.getAttribute("data-se-embed")){let e=o.nextElementSibling;u.isFormatElement(e)||(e=this.appendFormatTag(o,d.defaultTag)),this.setRange(e.firstChild,0,e.firstChild,0);return}if(!((u.isRangeFormatElement(s)||u.isWysiwygDiv(s))&&(u.isComponent(s.children[l.startOffset])||u.isComponent(s.children[l.startOffset-1])))){if(u.getParentElement(o,u.isNotCheckingNode))return null;if(r){(i=u.createElement(e||d.defaultTag)).innerHTML=r.innerHTML,0===i.childNodes.length&&(i.innerHTML=u.zeroWidthSpace),r.innerHTML=i.outerHTML,i=r.firstChild,(t=u.getEdgeChildNodes(i,null).sc)||(t=u.createTextNode(u.zeroWidthSpace),i.insertBefore(t,i.firstChild)),n=t.textContent.length,this.setRange(t,n,t,n);return}if(u.isRangeFormatElement(o)&&o.childNodes.length<=1){let e=null;1===o.childNodes.length&&u.isBreak(o.firstChild)?e=o.firstChild:(e=u.createTextNode(u.zeroWidthSpace),o.appendChild(e)),this.setRange(e,1,e,1);return}try{if(3===o.nodeType&&(i=u.createElement(e||d.defaultTag),o.parentNode.insertBefore(i,o),i.appendChild(o)),u.isBreak(i.nextSibling)&&u.removeItem(i.nextSibling),u.isBreak(i.previousSibling)&&u.removeItem(i.previousSibling),u.isBreak(t)){let e=u.createTextNode(u.zeroWidthSpace);t.parentNode.insertBefore(e,t),t=e}}catch(t){this.execCommand("formatBlock",!1,e||d.defaultTag),this.removeRange(),this._editorRange(),this.effectNode=null;return}if(i&&(u.isBreak(i.nextSibling)&&u.removeItem(i.nextSibling),u.isBreak(i.previousSibling)&&u.removeItem(i.previousSibling),u.isBreak(t))){let e=u.createTextNode(u.zeroWidthSpace);t.parentNode.insertBefore(e,t),t=e}this.effectNode=null,s?this.setRange(s,1,s,1):this.nativeFocus()}}},_setOptionsInit:function(t,n){this.context=e=(0,l.Z)(t.originElement,this._getConstructed(t),d),this._componentsInfoReset=!0,this._editorInit(!0,n)},_editorInit:function(t,n){this._init(t,n),p._addEvent(),this._setCharCount(),p._offStickyToolbar(),p.onResize_window(),e.element.toolbar.style.visibility="";let i=d.frameAttrbutes;for(let t in i)e.element.wysiwyg.setAttribute(t,i[t]);this._checkComponents(),this._componentsInfoInit=!1,this._componentsInfoReset=!1,this.history.reset(!0),h.setTimeout(function(){"function"==typeof m._resourcesStateChange&&(p._resizeObserver&&p._resizeObserver.observe(e.element.wysiwygFrame),p._toolbarObserver&&p._toolbarObserver.observe(e.element._toolbarShadow),m._resourcesStateChange(),"function"==typeof y.onload&&y.onload(m,t))})},_getConstructed:function(e){return{_top:e.topArea,_relative:e.relative,_toolBar:e.toolbar,_toolbarShadow:e._toolbarShadow,_menuTray:e._menuTray,_editorArea:e.editorArea,_wysiwygArea:e.wysiwygFrame,_codeArea:e.code,_placeholder:e.placeholder,_resizingBar:e.resizingBar,_navigation:e.navigation,_charCounter:e.charCounter,_charWrapper:e.charWrapper,_loading:e.loading,_lineBreaker:e.lineBreaker,_lineBreaker_t:e.lineBreaker_t,_lineBreaker_b:e.lineBreaker_b,_resizeBack:e.resizeBackground,_stickyDummy:e._stickyDummy,_arrow:e._arrow}}},p={_IEisComposing:!1,_lineBreakerBind:null,_responsiveCurrentSize:"default",_responsiveButtonSize:null,_responsiveButtons:null,_deleteKeyCode:new h.RegExp("^(8|46)$"),_cursorMoveKeyCode:new h.RegExp("^(8|3[2-9]|40|46)$"),_directionKeyCode:new h.RegExp("^(8|13|3[2-9]|40|46)$"),_nonTextKeyCode:new h.RegExp("^(8|13|1[6-9]|20|27|3[3-9]|40|45|46|11[2-9]|12[0-3]|144|145)$"),_historyIgnoreKeyCode:new h.RegExp("^(1[6-9]|20|27|3[3-9]|40|45|11[2-9]|12[0-3]|144|145)$"),_onButtonsCheck:new h.RegExp("^("+h.Object.keys(d._textTagsMap).join("|")+")$","i"),_frontZeroWidthReg:new h.RegExp(u.zeroWidthSpace+"+",""),_keyCodeShortcut:{65:"A",66:"B",83:"S",85:"U",73:"I",89:"Y",90:"Z",219:"[",221:"]"},_shortcutCommand:function(e,t){let n=null,i=p._keyCodeShortcut[e];switch(i){case"A":n="selectAll";break;case"B":-1===d.shortcutsDisable.indexOf("bold")&&(n="bold");break;case"S":t&&-1===d.shortcutsDisable.indexOf("strike")?n="strike":t||-1!==d.shortcutsDisable.indexOf("save")||(n="save");break;case"U":-1===d.shortcutsDisable.indexOf("underline")&&(n="underline");break;case"I":-1===d.shortcutsDisable.indexOf("italic")&&(n="italic");break;case"Z":-1===d.shortcutsDisable.indexOf("undo")&&(n=t?"redo":"undo");break;case"Y":-1===d.shortcutsDisable.indexOf("undo")&&(n="redo");break;case"[":-1===d.shortcutsDisable.indexOf("indent")&&(n=d.rtl?"indent":"outdent");break;case"]":-1===d.shortcutsDisable.indexOf("indent")&&(n=d.rtl?"outdent":"indent")}return n?(m.commandHandler(m.commandMap[n],n),!0):!!i},_applyTagEffects:function(){if(u.hasClass(e.element.wysiwyg,"se-read-only"))return!1;let t=m.getSelectionNode();if(t===m.effectNode)return;m.effectNode=t;let i=d.rtl?"marginRight":"marginLeft",l=m.commandMap,o=p._onButtonsCheck,s=[],r=[],a=m.activePlugins,c=a.length,f="";for(;t.firstChild;)t=t.firstChild;for(let e=t;!u.isWysiwygDiv(e)&&e;e=e.parentNode)if(!(1!==e.nodeType||u.isBreak(e))){if(f=e.nodeName.toUpperCase(),r.push(f),!m.isReadOnly)for(let t=0,i;t<c;t++)i=a[t],-1===s.indexOf(i)&&n[i].active.call(m,e)&&s.push(i);if(u.isFormatElement(e)){-1===s.indexOf("OUTDENT")&&l.OUTDENT&&!u.isImportantDisabled(l.OUTDENT)&&(u.isListCell(e)||e.style[i]&&u.getNumber(e.style[i],0)>0)&&(s.push("OUTDENT"),l.OUTDENT.removeAttribute("disabled")),-1===s.indexOf("INDENT")&&l.INDENT&&!u.isImportantDisabled(l.INDENT)&&(s.push("INDENT"),u.isListCell(e)&&!e.previousElementSibling?l.INDENT.setAttribute("disabled",!0):l.INDENT.removeAttribute("disabled"));continue}o&&o.test(f)&&(s.push(f),u.addClass(l[f],"active"))}m._setKeyEffect(s),m._variable.currentNodes=r.reverse(),m._variable.currentNodesMap=s,d.showPathLabel&&(e.element.navigation.textContent=m._variable.currentNodes.join(" > "))},_buttonsEventHandler:function(e){let t=e.target;if(m._bindControllersOff&&e.stopPropagation(),/^(input|textarea|select|option)$/i.test(t.nodeName)?m._antiBlur=!1:e.preventDefault(),u.getParentElement(t,".se-submenu"))e.stopPropagation(),m._notHideToolbar=!0;else{let n=t.getAttribute("data-command"),i=t.className;for(;!n&&!/se-menu-list/.test(i)&&!/sun-editor-common/.test(i);)n=(t=t.parentNode).getAttribute("data-command"),i=t.className;(n===m._submenuName||n===m._containerName)&&e.stopPropagation()}},addGlobalEvent:function(e,t,n){return d.iframe&&m._ww.addEventListener(e,t,n),m._w.addEventListener(e,t,n),{type:e,listener:t,useCapture:n}},removeGlobalEvent:function(e,t,n){e&&("object"==typeof e&&(t=e.listener,n=e.useCapture,e=e.type),d.iframe&&m._ww.removeEventListener(e,t,n),m._w.removeEventListener(e,t,n))},onClick_toolbar:function(e){let t=e.target,n=t.getAttribute("data-display"),i=t.getAttribute("data-command"),l=t.className;for(m.controllersOff();t.parentNode&&!i&&!/se-menu-list/.test(l)&&!/se-toolbar/.test(l);)i=(t=t.parentNode).getAttribute("data-command"),n=t.getAttribute("data-display"),l=t.className;(i||n)&&(t.disabled||m.actionCall(i,n,t))},__selectionSyncEvent:null,onMouseDown_wysiwyg:function(t){if(m.isReadOnly||u.isNonEditable(e.element.wysiwyg))return;if(u._isExcludeSelectionElement(t.target)){t.preventDefault();return}if(p.removeGlobalEvent(p.__selectionSyncEvent),p.__selectionSyncEvent=p.addGlobalEvent("mouseup",function(){m&&m._editorRange(),p.removeGlobalEvent(p.__selectionSyncEvent)}),"function"==typeof y.onMouseDown&&!1===y.onMouseDown(t,m))return;let n=u.getParentElement(t.target,u.isCell);if(n){let e=m.plugins.table;e&&n!==e._fixedCell&&!e._shift&&m.callPlugin("table",function(){e.onTableCellMultiSelect.call(m,n,!1)},null)}m._isBalloon&&p._hideToolbar()},onClick_wysiwyg:function(t){let n=t.target;if(m.isReadOnly)return t.preventDefault(),u.isAnchor(n)&&h.open(n.href,n.target),!1;if(u.isNonEditable(e.element.wysiwyg)||"function"==typeof y.onClick&&!1===y.onClick(t,m))return;let i=m.getFileComponent(n);if(i){t.preventDefault(),m.selectComponent(i.target,i.pluginName);return}let l=u.getParentElement(n,"FIGCAPTION");if(l&&u.isNonEditable(l)&&(t.preventDefault(),l.focus(),m._isInline&&!m._inlineToolbarAttr.isShow)){p._showToolbarInline();let e=function(){p._hideToolbar(),l.removeEventListener("blur",e)};l.addEventListener("blur",e)}if(m._editorRange(),3===t.detail){let e=m.getRange();u.isFormatElement(e.endContainer)&&0===e.endOffset&&(e=m.setRange(e.startContainer,e.startOffset,e.startContainer,e.startContainer.length),m._rangeInfo(e,m.getSelection()))}let o=m.getSelectionNode(),s=u.getFormatElement(o,null),r=u.getRangeFormatElement(o,null),a=o;for(;a&&a.firstChild;)a=a.firstChild;let c=m.getFileComponent(a);if(c){let e=m.getRange();r||e.startContainer!==e.endContainer||m.selectComponent(c.target,c.pluginName)}else m.currentFileComponentInfo&&m.controllersOff();if(s||u.isNonEditable(n)||u.isList(r))p._applyTagEffects();else{let e=m.getRange();if(u.getFormatElement(e.startContainer)===u.getFormatElement(e.endContainer)){if(u.isList(r)){t.preventDefault();let e=u.createElement("LI"),n=o.nextElementSibling;e.appendChild(o),r.insertBefore(e,n),m.focus()}else!u.isWysiwygDiv(o)&&!u.isComponent(o)&&(!u.isTable(o)||u.isCell(o))&&null!==m._setDefaultFormat(u.isRangeFormatElement(r)?"DIV":d.defaultTag)?(t.preventDefault(),m.focus()):p._applyTagEffects()}}m._isBalloon&&h.setTimeout(p._toggleToolbarBalloon)},_balloonDelay:null,_showToolbarBalloonDelay:function(){p._balloonDelay&&h.clearTimeout(p._balloonDelay),p._balloonDelay=h.setTimeout((function(){h.clearTimeout(this._balloonDelay),this._balloonDelay=null,this._showToolbarBalloon()}).bind(p),350)},_toggleToolbarBalloon:function(){if(m){m._editorRange();let e=m.getRange();m._bindControllersOff||!m._isBalloonAlways&&e.collapsed?p._hideToolbar():p._showToolbarBalloon(e)}},_showToolbarBalloon:function(t){let n;if(!m._isBalloon)return;let i=t||m.getRange(),l=e.element.toolbar,o=e.element.topArea,s=m.getSelection();if(m._isBalloonAlways&&i.collapsed)n=!0;else if(s.focusNode===s.anchorNode)n=s.focusOffset<s.anchorOffset;else{let e=u.getListChildNodes(i.commonAncestorContainer,null);n=u.getArrayIndex(e,s.focusNode)<u.getArrayIndex(e,s.anchorNode)}let r=i.getClientRects();r=r[n?0:r.length-1];let a=m.getGlobalScrollOffset(),c=a.left,f=a.top,g=o.offsetWidth,y=p._getEditorOffsets(null),C=y.top,_=y.left;if(l.style.top="-10000px",l.style.visibility="hidden",l.style.display="block",!r){let t=m.getSelectionNode();if(u.isFormatElement(t)){let e=u.createTextNode(u.zeroWidthSpace);m.insertNode(e,null,!1),m.setRange(e,1,e,1),m._editorRange(),r=(r=m.getRange().getClientRects())[n?0:r.length-1]}if(!r){let n=u.getOffset(t,e.element.wysiwygFrame);r={left:n.left,top:n.top,right:n.left,bottom:n.top+t.offsetHeight,noText:!0},c=0,f=0}n=!0}let b=h.Math.round(e.element._arrow.offsetWidth/2),E=l.offsetWidth,v=l.offsetHeight,w=/iframe/i.test(e.element.wysiwygFrame.nodeName)?e.element.wysiwygFrame.getClientRects()[0]:null;if(w&&(r={left:r.left+w.left,top:r.top+w.top,right:r.right+w.right-w.width,bottom:r.bottom+w.bottom-w.height}),p._setToolbarOffset(n,r,l,_,g,c,f,C,b),(E!==l.offsetWidth||v!==l.offsetHeight)&&p._setToolbarOffset(n,r,l,_,g,c,f,C,b),d.toolbarContainer){let e=o.parentElement,t=d.toolbarContainer,n=t.offsetLeft,i=t.offsetTop;for(;!t.parentElement.contains(e)||!/^(BODY|HTML)$/i.test(t.parentElement.nodeName);)n+=(t=t.offsetParent).offsetLeft,i+=t.offsetTop;l.style.left=l.offsetLeft-n+o.offsetLeft+"px",l.style.top=l.offsetTop-i+o.offsetTop+"px"}l.style.visibility=""},_setToolbarOffset:function(t,n,i,l,o,s,r,a,d){let c=i.offsetWidth,g=n.noText&&!t?0:i.offsetHeight,m=(t?n.left:n.right)-l-c/2+s,y=m+c-o,C=(t?n.top-g-d:n.bottom+d)-(n.noText?0:a)+r,_=m<0?1:y<0?m:m-y-1-1,b=!1,E=C+(t?p._getEditorOffsets(null).top:i.offsetHeight-e.element.wysiwyg.offsetHeight);!t&&E>0&&p._getPageBottomSpace()<E?(t=!0,b=!0):t&&f.documentElement.offsetTop>E&&(t=!1,b=!0),b&&(C=(t?n.top-g-d:n.bottom+d)-(n.noText?0:a)+r),i.style.left=h.Math.floor(_)+"px",i.style.top=h.Math.floor(C)+"px",t?(u.removeClass(e.element._arrow,"se-arrow-up"),u.addClass(e.element._arrow,"se-arrow-down"),e.element._arrow.style.top=g+"px"):(u.removeClass(e.element._arrow,"se-arrow-down"),u.addClass(e.element._arrow,"se-arrow-up"),e.element._arrow.style.top=-d+"px");let v=h.Math.floor(c/2+(m-_));e.element._arrow.style.left=(v+d>i.offsetWidth?i.offsetWidth-d:v<d?d:v)+"px"},_showToolbarInline:function(){if(!m._isInline)return;let t=e.element.toolbar;d.toolbarContainer?t.style.position="relative":t.style.position="absolute",t.style.visibility="hidden",t.style.display="block",m._inlineToolbarAttr.width=t.style.width=d.toolbarWidth,m._inlineToolbarAttr.top=t.style.top=(d.toolbarContainer?0:-1-t.offsetHeight)+"px","function"==typeof y.showInline&&y.showInline(t,e,m),p.onScroll_window(),m._inlineToolbarAttr.isShow=!0,t.style.visibility=""},_hideToolbar:function(){m._notHideToolbar||m._variable.isFullScreen||(e.element.toolbar.style.display="none",m._inlineToolbarAttr.isShow=!1)},onInput_wysiwyg:function(e){if(/AUDIO/.test(e.target.nodeName))return!1;if(m.isReadOnly||m.isDisabled)return e.preventDefault(),e.stopPropagation(),m.history.go(m.history.getCurrentIndex()),!1;let t=m.getRange(),n=m.getSelectionNode(),i=u.getFormatElement(n,null);if(!i&&t.collapsed&&!u.isComponent(n)&&!u.isList(n)){let e=u.getRangeFormatElement(i,null);m._setDefaultFormat(u.isRangeFormatElement(e)?"DIV":d.defaultTag)}m._editorRange();let l=(null===e.data?"":void 0===e.data?" ":e.data)||"";if(!m._charCount(l))return e.preventDefault(),e.stopPropagation(),!1;("function"!=typeof y.onInput||!1!==y.onInput(e,m))&&m.history.push(!0)},_isUneditableNode:function(e,t){let n;let i=t?e.startContainer:e.endContainer,l=t?e.startOffset:e.endOffset,o=t?"previousSibling":"nextSibling";return 1===i.nodeType?(n=p._isUneditableNode_getSibling(i.childNodes[l],o,i))&&1===n.nodeType&&"false"===n.getAttribute("contenteditable"):(n=p._isUneditableNode_getSibling(i,o,i),m.isEdgePoint(i,l,t?"start":"end")&&n&&1===n.nodeType&&"false"===n.getAttribute("contenteditable"))},_isUneditableNode_getSibling:function(e,t,n){if(!e)return null;let i=e[t];if(!i){if(!(i=(i=u.getFormatElement(n))?i[t]:null)||u.isComponent(i))return null;i="previousSibling"===t?i.firstChild:i.lastChild}return i},_onShortcutKey:!1,onKeyDown_wysiwyg:function(t){let n=m.getSelectionNode();if(u.isInputElement(n))return;let i=t.keyCode,l=t.shiftKey,o=t.ctrlKey||t.metaKey||91===i||92===i||224===i,s=t.altKey;if(p._IEisComposing=229===i,!o&&m.isReadOnly&&!p._cursorMoveKeyCode.test(i))return t.preventDefault(),!1;if(m.submenuOff(),m._isBalloon&&p._hideToolbar(),"function"==typeof y.onKeyDown&&!1===y.onKeyDown(t,m))return;if(o&&p._shortcutCommand(i,l))return p._onShortcutKey=!0,t.preventDefault(),t.stopPropagation(),!1;p._onShortcutKey&&(p._onShortcutKey=!1),13===i&&u.isFormatElement(m.getRange().startContainer)&&(m._resetRangeToTextNode(),n=m.getSelectionNode());let r=m.getRange(),a=!r.collapsed||r.startContainer!==r.endContainer,c=m._fileManager.pluginRegExp.test(m.currentControllerName)?m.currentControllerName:"",f=u.getFormatElement(n,null)||n,g=u.getRangeFormatElement(f,null);if(!/37|38|39|40/.test(t.keyCode)||!1!==p._onKeyDown_wysiwyg_arrowKey(t)){switch(i){case 8:if(!a&&c){t.preventDefault(),t.stopPropagation(),m.plugins[c].destroy.call(m);break}if(a&&p._hardDelete()){t.preventDefault(),t.stopPropagation();break}if(!u.isFormatElement(f)&&!e.element.wysiwyg.firstElementChild&&!u.isComponent(n)&&null!==m._setDefaultFormat(d.defaultTag))return t.preventDefault(),t.stopPropagation(),!1;if(!a&&!f.previousElementSibling&&0===r.startOffset&&!n.previousSibling&&!u.isListCell(f)&&u.isFormatElement(f)&&(!u.isFreeFormatElement(f)||u.isClosureFreeFormatElement(f))){if(u.isClosureRangeFormatElement(f.parentNode))return t.preventDefault(),t.stopPropagation(),!1;if(u.isWysiwygDiv(f.parentNode)&&f.childNodes.length<=1&&(!f.firstChild||u.onlyZeroWidthSpace(f.textContent))){if(t.preventDefault(),t.stopPropagation(),f.nodeName.toUpperCase()===d.defaultTag.toUpperCase()){f.innerHTML="<br>";let e=f.attributes;for(;e[0];)f.removeAttribute(e[0].name)}else{let e=u.createElement(d.defaultTag);e.innerHTML="<br>",f.parentElement.replaceChild(e,f)}return m.nativeFocus(),!1}}let C=r.startContainer;if(f&&!f.previousElementSibling&&0===r.startOffset&&3===C.nodeType&&!u.isFormatElement(C.parentNode)){let e=C.parentNode.previousSibling,t=C.parentNode.nextSibling;e||(t?e=t:(e=u.createElement("BR"),f.appendChild(e)));let n=C;for(;f.contains(n)&&!n.previousSibling;)n=n.parentNode;if(!f.contains(n)){C.textContent="",u.removeItemAllParents(C,null,f);break}}if(p._isUneditableNode(r,!0)){t.preventDefault(),t.stopPropagation();break}!a&&m._isEdgeFormat(r.startContainer,r.startOffset,"start")&&u.isFormatElement(f.previousElementSibling)&&(m._formatAttrsTemp=f.previousElementSibling.attributes);let _=r.commonAncestorContainer;if(f=u.getFormatElement(r.startContainer,null),(g=u.getRangeFormatElement(f,null))&&f&&!u.isCell(g)&&!/^FIGCAPTION$/i.test(g.nodeName)){if(u.isListCell(f)&&u.isList(g)&&(u.isListCell(g.parentNode)||f.previousElementSibling)&&(n===f||3===n.nodeType&&(!n.previousSibling||u.isList(n.previousSibling)))&&(u.getFormatElement(r.startContainer,null)!==u.getFormatElement(r.endContainer,null)?g.contains(r.startContainer):0===r.startOffset&&r.collapsed)){if(r.startContainer!==r.endContainer)t.preventDefault(),m.removeNode(),3===r.startContainer.nodeType&&m.setRange(r.startContainer,r.startContainer.textContent.length,r.startContainer,r.startContainer.textContent.length),m.history.push(!0);else{let e=f.previousElementSibling||g.parentNode;if(u.isListCell(e)){t.preventDefault();let n=e;if(!e.contains(f)&&u.isListCell(n)&&u.isList(n.lastElementChild)){for(n=n.lastElementChild.lastElementChild;u.isListCell(n)&&u.isList(n.lastElementChild);)n=n.lastElementChild&&n.lastElementChild.lastElementChild;e=n}let i=e===g.parentNode?g.previousSibling:e.lastChild;i||(i=u.createTextNode(u.zeroWidthSpace),g.parentNode.insertBefore(i,g.parentNode.firstChild));let l=3===i.nodeType?i.textContent.length:1,o=f.childNodes,s=i,r=o[0];for(;r=o[0];)e.insertBefore(r,s.nextSibling),s=r;u.removeItem(f),0===g.children.length&&u.removeItem(g),m.setRange(i,l,i,l),m.history.push(!0)}}break}if(!a&&0===r.startOffset){let e=!0,n=_;for(;n&&n!==g&&!u.isWysiwygDiv(n);){if(n.previousSibling&&(1===n.previousSibling.nodeType||!u.onlyZeroWidthSpace(n.previousSibling.textContent.trim()))){e=!1;break}n=n.parentNode}if(e&&g.parentNode){t.preventDefault(),m.detachRangeFormatElement(g,u.isListCell(f)?[f]:null,null,!1,!1),m.history.push(!0);break}}}if(!a&&f&&(0===r.startOffset||n===f&&f.childNodes[r.startOffset])){let e=n===f?f.childNodes[r.startOffset]:n,i=f.previousSibling,l=(3===_.nodeType||u.isBreak(_))&&!_.previousSibling&&0===r.startOffset;if(e&&!e.previousSibling&&(_&&u.isComponent(_.previousSibling)||l&&u.isComponent(i))){let e=m.getFileComponent(i);e?(t.preventDefault(),t.stopPropagation(),0===f.textContent.length&&u.removeItem(f),!1===m.selectComponent(e.target,e.pluginName)&&m.blur()):u.isComponent(i)&&(t.preventDefault(),t.stopPropagation(),u.removeItem(i));break}e&&u.isNonEditable(e.previousSibling)&&(t.preventDefault(),t.stopPropagation(),u.removeItem(e.previousSibling))}break;case 46:if(c){t.preventDefault(),t.stopPropagation(),m.plugins[c].destroy.call(m);break}if(a&&p._hardDelete()){t.preventDefault(),t.stopPropagation();break}if(!a&&m._isEdgeFormat(r.endContainer,r.endOffset,"end")&&!f.nextSibling){t.preventDefault(),t.stopPropagation();return}if(p._isUneditableNode(r,!1)){t.preventDefault(),t.stopPropagation();break}if((u.isFormatElement(n)||null===n.nextSibling||u.onlyZeroWidthSpace(n.nextSibling)&&null===n.nextSibling.nextSibling)&&r.startOffset===n.textContent.length){let e=f.nextElementSibling;if(!e)break;if(u.isComponent(e)){if(t.preventDefault(),u.onlyZeroWidthSpace(f)&&(u.removeItem(f),u.isTable(e))){let t=u.getChildElement(e,u.isCell,!1);t=t.firstElementChild||t,m.setRange(t,0,t,0);break}let n=m.getFileComponent(e);n?(t.stopPropagation(),!1===m.selectComponent(n.target,n.pluginName)&&m.blur()):u.isComponent(e)&&(t.stopPropagation(),u.removeItem(e));break}}if(!a&&(m.isEdgePoint(r.endContainer,r.endOffset)||n===f&&f.childNodes[r.startOffset])){let e=n===f&&f.childNodes[r.startOffset]||n;if(e&&u.isNonEditable(e.nextSibling)){t.preventDefault(),t.stopPropagation(),u.removeItem(e.nextSibling);break}if(u.isComponent(e)){t.preventDefault(),t.stopPropagation(),u.removeItem(e);break}}if(!a&&m._isEdgeFormat(r.endContainer,r.endOffset,"end")&&u.isFormatElement(f.nextElementSibling)&&(m._formatAttrsTemp=f.attributes),f=u.getFormatElement(r.startContainer,null),g=u.getRangeFormatElement(f,null),u.isListCell(f)&&u.isList(g)&&(n===f||3===n.nodeType&&(!n.nextSibling||u.isList(n.nextSibling))&&(u.getFormatElement(r.startContainer,null)!==u.getFormatElement(r.endContainer,null)?g.contains(r.endContainer):r.endOffset===n.textContent.length&&r.collapsed))){r.startContainer!==r.endContainer&&m.removeNode();let e=u.getArrayItem(f.children,u.isList,!1);if((e=e||f.nextElementSibling||g.parentNode.nextElementSibling)&&(u.isList(e)||u.getArrayItem(e.children,u.isList,!1))){let n,i;if(t.preventDefault(),u.isList(e)){let t=e.firstElementChild;for(n=(i=t.childNodes)[0];i[0];)f.insertBefore(i[0],e);u.removeItem(t)}else{for(n=e.firstChild,i=e.childNodes;i[0];)f.appendChild(i[0]);u.removeItem(e)}m.setRange(n,0,n,0),m.history.push(!0)}}break;case 9:if(c||d.tabDisable||(t.preventDefault(),o||s||u.isWysiwygDiv(n)))break;let b=!r.collapsed||m.isEdgePoint(r.startContainer,r.startOffset),E=m.getSelectedElements(null);n=m.getSelectionNode();let v=[],w=[],T=u.isListCell(E[0]),N=u.isListCell(E[E.length-1]),x={sc:r.startContainer,so:r.startOffset,ec:r.endContainer,eo:r.endOffset};for(let e=0,t=E.length,n;e<t;e++)if(n=E[e],u.isListCell(n)){if(!n.previousElementSibling&&!l)continue;v.push(n)}else w.push(n);if(v.length>0&&b&&m.plugins.list)x=m.plugins.list.editInsideList.call(m,l,v);else{let e=u.getParentElement(n,u.isCell);if(e&&b){let t=u.getParentElement(e,"table"),n=u.getListChildren(t,u.isCell),i=l?u.prevIdx(n,e):u.nextIdx(n,e);i!==n.length||l||(i=0),-1===i&&l&&(i=n.length-1);let o=n[i];if(!o)break;o=o.firstElementChild||o,m.setRange(o,0,o,0);break}w=w.concat(v),T=N=null}if(w.length>0){if(l){let e=w.length-1;for(let t=0,n;t<=e;t++){n=w[t].childNodes;for(let e=0,t=n.length,i;e<t&&(i=n[e]);e++)if(!u.onlyZeroWidthSpace(i)){/^\s{1,4}$/.test(i.textContent)?u.removeItem(i):/^\s{1,4}/.test(i.textContent)&&(i.textContent=i.textContent.replace(/^\s{1,4}/,""));break}}let t=u.getChildElement(w[0],"text",!1),n=u.getChildElement(w[e],"text",!0);!T&&t&&(x.sc=t,x.so=0),!N&&n&&(x.ec=n,x.eo=n.textContent.length)}else{let e=u.createTextNode(new h.Array(m._variable.tabSize+1).join("\xa0"));if(1===w.length){if(!m.insertNode(e,null,!0))return!1;T||(x.sc=e,x.so=e.length),N||(x.ec=e,x.eo=e.length)}else{let t=w.length-1;for(let n=0,i;n<=t;n++)(i=w[n].firstChild)&&(u.isBreak(i)?w[n].insertBefore(e.cloneNode(!1),i):i.textContent=e.textContent+i.textContent);let n=u.getChildElement(w[0],"text",!1),i=u.getChildElement(w[t],"text",!0);!T&&n&&(x.sc=n,x.so=0),!N&&i&&(x.ec=i,x.eo=i.textContent.length)}}}m.setRange(x.sc,x.so,x.ec,x.eo),m.history.push(!1);break;case 13:let S=u.getFreeFormatElement(n,null);if(m._charTypeHTML){let e="";if(e=!l&&S||l?"<br>":"<"+f.nodeName+"><br></"+f.nodeName+">",!m.checkCharCount(e,"byte-html"))return t.preventDefault(),!1}if(!l&&!c){let i=m._isEdgeFormat(r.endContainer,r.endOffset,"end"),l=m._isEdgeFormat(r.startContainer,r.startOffset,"start");if(i&&(/^H[1-6]$/i.test(f.nodeName)||/^HR$/i.test(f.nodeName))){p._enterPrevent(t);let e=null,n=m.appendFormatTag(f,d.defaultTag);if(i&&i.length>0){let t=e=i.pop();for(;i.length>0;)e=e.appendChild(i.pop());n.appendChild(t)}if(e=e?e.appendChild(n.firstChild):n.firstChild,u.isBreak(e)){let t=u.createTextNode(u.zeroWidthSpace);e.parentNode.insertBefore(t,e),m.setRange(t,1,t,1)}else m.setRange(e,0,e,0);break}if(g&&f&&!u.isCell(g)&&!/^FIGCAPTION$/i.test(g.nodeName)){let e=m.getRange();if(m.isEdgePoint(e.endContainer,e.endOffset)&&u.isList(n.nextSibling)){p._enterPrevent(t);let e=u.createElement("LI"),i=u.createElement("BR");e.appendChild(i),f.parentNode.insertBefore(e,f.nextElementSibling),e.appendChild(n.nextSibling),m.setRange(i,1,i,1);break}if((3!==e.commonAncestorContainer.nodeType||!e.commonAncestorContainer.nextElementSibling)&&u.onlyZeroWidthSpace(f.innerText.trim())&&!u.isListCell(f.nextElementSibling)){p._enterPrevent(t);let e=null;if(u.isListCell(g.parentNode)){let t=f.parentNode.parentNode;g=t.parentNode;let n=u.createElement("LI");n.innerHTML="<br>",u.copyTagAttributes(n,f,d.lineAttrReset),e=n,g.insertBefore(e,t.nextElementSibling)}else{let t=u.isCell(g.parentNode)?"DIV":u.isList(g.parentNode)?"LI":u.isFormatElement(g.nextElementSibling)&&!u.isRangeFormatElement(g.nextElementSibling)?g.nextElementSibling.nodeName:u.isFormatElement(g.previousElementSibling)&&!u.isRangeFormatElement(g.previousElementSibling)?g.previousElementSibling.nodeName:d.defaultTag;e=u.createElement(t),u.copyTagAttributes(e,f,d.lineAttrReset);let n=m.detachRangeFormatElement(g,[f],null,!0,!0);n.cc.insertBefore(e,n.ec)}e.innerHTML="<br>",u.removeItemAllParents(f,null,null),m.setRange(e,1,e,1);break}}if(S){p._enterPrevent(t);let e=n===S,i=m.getSelection(),l=n.childNodes,o=i.focusOffset,s=n.previousElementSibling,a=n.nextSibling;if(!u.isClosureFreeFormatElement(S)&&l&&(e&&r.collapsed&&l.length-1<=o+1&&u.isBreak(l[o])&&(!l[o+1]||(!l[o+2]||u.onlyZeroWidthSpace(l[o+2].textContent))&&3===l[o+1].nodeType&&u.onlyZeroWidthSpace(l[o+1].textContent))&&o>0&&u.isBreak(l[o-1])||!e&&u.onlyZeroWidthSpace(n.textContent)&&u.isBreak(s)&&(u.isBreak(s.previousSibling)||!u.onlyZeroWidthSpace(s.previousSibling.textContent))&&(!a||!u.isBreak(a)&&u.onlyZeroWidthSpace(a.textContent)))){e?u.removeItem(l[o-1]):u.removeItem(n);let t=m.appendFormatTag(S,u.isFormatElement(S.nextElementSibling)&&!u.isRangeFormatElement(S.nextElementSibling)?S.nextElementSibling:null);u.copyFormatAttributes(t,S),m.setRange(t,1,t,1);break}if(e){y.insertHTML(r.collapsed&&u.isBreak(r.startContainer.childNodes[r.startOffset-1])?"<br>":"<br><br>",!0,!1);let e=i.focusNode,t=i.focusOffset;S===e&&(e=e.childNodes[t-o>1?t-1:t]),m.setRange(e,1,e,1)}else{let e=i.focusNode.nextSibling,t=u.createElement("BR");m.insertNode(t,null,!1);let n=t.previousSibling,l=t.nextSibling;!u.isBreak(e)&&!u.isBreak(n)&&(!l||u.onlyZeroWidthSpace(l))?(t.parentNode.insertBefore(t.cloneNode(!1),t),m.setRange(t,1,t,1)):m.setRange(l,0,l,0)}p._onShortcutKey=!0;break}if(r.collapsed&&(l||i)){p._enterPrevent(t);let e=u.createElement("BR"),o=u.createElement(f.nodeName);u.copyTagAttributes(o,f,d.lineAttrReset);let s=e;do{if(!u.isBreak(n)&&1===n.nodeType){let e=n.cloneNode(!1);e.appendChild(s),s=e}n=n.parentNode}while(f!==n&&f.contains(n));o.appendChild(s),f.parentNode.insertBefore(o,l&&!i?f:f.nextElementSibling),i&&m.setRange(e,1,e,1);break}if(f){let n;t.stopPropagation();let o=0;if(r.collapsed)n=u.onlyZeroWidthSpace(f)?m.appendFormatTag(f,f.cloneNode(!1)):u.splitElement(r.endContainer,r.endOffset,u.getElementDepth(f));else{let s=u.getFormatElement(r.startContainer,null)!==u.getFormatElement(r.endContainer,null),a=f.cloneNode(!1);a.innerHTML="<br>";let c=r.commonAncestorContainer,h=c===r.startContainer&&c===r.endContainer&&u.onlyZeroWidthSpace(c)?r:m.removeNode();if(!(n=u.getFormatElement(h.container,null))){u.isWysiwygDiv(h.container)&&(p._enterPrevent(t),e.element.wysiwyg.appendChild(a),n=a,u.copyTagAttributes(n,f,d.lineAttrReset),m.setRange(n,o,n,o));break}let g=u.getRangeFormatElement(h.container);if(n=n.contains(g)?u.getChildElement(g,u.getFormatElement.bind(u)):n,s){if(i&&!l)n.parentNode.insertBefore(a,h.prevContainer&&h.container!==h.prevContainer?n:n.nextElementSibling),n=a,o=0;else if(o=h.offset,l){let e=n.parentNode.insertBefore(a,n);i&&(n=e,o=0)}}else i&&l?(n.parentNode.insertBefore(a,h.prevContainer&&h.container===h.prevContainer?n.nextElementSibling:n),n=a,o=0):n=u.splitElement(h.container,h.offset,u.getElementDepth(f))}p._enterPrevent(t),u.copyTagAttributes(n,f,d.lineAttrReset),m.setRange(n,o,n,o);break}}if(a)break;if(g&&u.getParentElement(g,"FIGCAPTION")&&u.getParentElement(g,u.isList)&&(p._enterPrevent(t),f=m.appendFormatTag(f,null),m.setRange(f,0,f,0)),c){t.preventDefault(),t.stopPropagation(),m.containerOff(),m.controllersOff();let n=e[c],i=n._container,o=i.previousElementSibling||i.nextElementSibling,s=null;u.isListCell(i.parentNode)?s=u.createElement("BR"):(s=u.createElement(u.isFormatElement(o)&&!u.isRangeFormatElement(o)?o.nodeName:d.defaultTag)).innerHTML="<br>",l?i.parentNode.insertBefore(s,i):i.parentNode.insertBefore(s,i.nextElementSibling),m.callPlugin(c,function(){!1===m.selectComponent(n._element,c)&&m.blur()},null)}break;case 27:if(c)return t.preventDefault(),t.stopPropagation(),m.controllersOff(),!1}if(l&&16===i){t.preventDefault(),t.stopPropagation();let e=m.plugins.table;if(e&&!e._shift&&!e._ref){let t=u.getParentElement(f,u.isCell);if(t){e.onTableCellMultiSelect.call(m,t,!0);return}}}else if(l&&(u.isOSX_IOS?s:o)&&32===i){t.preventDefault(),t.stopPropagation();let e=m.insertNode(u.createTextNode("\xa0"));if(e){m.setRange(e,e.length,e,e.length);return}}if(u.isIE&&!o&&!s&&!a&&!p._nonTextKeyCode.test(i)&&u.isBreak(r.commonAncestorContainer)){let e=u.createTextNode(u.zeroWidthSpace);m.insertNode(e,null,!1),m.setRange(e,1,e,1)}p._directionKeyCode.test(i)&&(h.setTimeout(m._editorRange.bind(m),0),p._applyTagEffects())}},_onKeyDown_wysiwyg_arrowKey:function(e){if(e.shiftKey)return;let t=m.getSelectionNode(),n=function(t,n){if(n||(n=0),e.preventDefault(),e.stopPropagation(),!t)return;let i=m.getFileComponent(t);i?m.selectComponent(i.target,i.pluginName):(m.setRange(t,n,t,n),m.controllersOff())},i=u.getParentElement(t,"table");if(i){let l=u.getParentElement(t,"tr"),o=u.getParentElement(t,"td"),s=o,r=o;if(o){for(;s&&s.firstChild;)s=s.firstChild;for(;r&&r.lastChild;)r=r.lastChild}let a=t;for(;a&&a.firstChild;)a=a.firstChild;let d=a===s,c=a===r,f=null,h=0;if(38===e.keyCode&&d){let e=l&&l.previousElementSibling;for(f=e?e.children[o.cellIndex]:u.getPreviousDeepestNode(i,m.context.element.wysiwyg);f&&f.lastChild;)f=f.lastChild;f&&(h=f.textContent.length)}else if(40===e.keyCode&&c){let e=l&&l.nextElementSibling;for(f=e?e.children[o.cellIndex]:u.getNextDeepestNode(i,m.context.element.wysiwyg);f&&f.firstChild;)f=f.firstChild}if(f)return n(f,h),!1}let l=m.getFileComponent(t);if(l){let t=/37|38/.test(e.keyCode),i=/39|40/.test(e.keyCode);if(t){let e=u.getPreviousDeepestNode(l.target,m.context.element.wysiwyg);n(e,e&&e.textContent.length)}else i&&n(u.getNextDeepestNode(l.target,m.context.element.wysiwyg))}},onKeyUp_wysiwyg:function(e){if(p._onShortcutKey)return;m._editorRange();let t=e.keyCode,n=e.ctrlKey||e.metaKey||91===t||92===t||224===t,i=e.altKey;if(m.isReadOnly){!n&&p._cursorMoveKeyCode.test(t)&&p._applyTagEffects();return}let l=m.getRange(),o=m.getSelectionNode();if(m._isBalloon&&(m._isBalloonAlways&&27!==t||!l.collapsed)){if(m._isBalloonAlways)27!==t&&p._showToolbarBalloonDelay();else{p._showToolbarBalloon();return}}let s=o;for(;s&&s.firstChild;)s=s.firstChild;let r=m.getFileComponent(s);if(!(16===e.keyCode||e.shiftKey)&&r?m.selectComponent(r.target,r.pluginName):m.currentFileComponentInfo&&m.controllersOff(),8===t&&u.isWysiwygDiv(o)&&""===o.textContent&&0===o.children.length){e.preventDefault(),e.stopPropagation(),o.innerHTML="";let t=u.createElement(u.isFormatElement(m._variable.currentNodes[0])?m._variable.currentNodes[0]:d.defaultTag);t.innerHTML="<br>",o.appendChild(t),m.setRange(t,0,t,0),p._applyTagEffects(),m.history.push(!1);return}let a=u.getFormatElement(o,null),c=u.getRangeFormatElement(o,null),f=m._formatAttrsTemp;if(f){for(let e=0,n=f.length;e<n;e++){if(13===t&&/^id$/i.test(f[e].name)){a.removeAttribute("id");continue}a.setAttribute(f[e].name,f[e].value)}m._formatAttrsTemp=null}if(a||!l.collapsed||u.isComponent(o)||u.isList(o)||null===m._setDefaultFormat(u.isRangeFormatElement(c)?"DIV":d.defaultTag)||(o=m.getSelectionNode()),!n&&!i&&!p._nonTextKeyCode.test(t)&&3===o.nodeType&&u.zeroWidthRegExp.test(o.textContent)&&!(void 0!==e.isComposing?e.isComposing:p._IEisComposing)){let e=l.startOffset,t=l.endOffset,n=(o.textContent.substring(0,t).match(p._frontZeroWidthReg)||"").length;e=l.startOffset-n,t=l.endOffset-n,o.textContent=o.textContent.replace(u.zeroWidthRegExp,""),m.setRange(o,e<0?0:e,o,t<0?0:t)}p._deleteKeyCode.test(t)&&a&&u.onlyZeroWidthSpace(a.textContent)&&!a.previousElementSibling&&(a.innerHTML="<br>",m.setRange(a,0,a,0)),m._charCount(""),"function"==typeof y.onKeyUp&&!1===y.onKeyUp(e,m)||n||i||p._historyIgnoreKeyCode.test(t)||m.history.push(!0)},onScroll_wysiwyg:function(e){m.controllersOff(),m._isBalloon&&p._hideToolbar(),"function"==typeof y.onScroll&&y.onScroll(e,m)},onFocus_wysiwyg:function(e){m._antiBlur||(m.hasFocus=!0,h.setTimeout(p._applyTagEffects),m._isInline&&p._showToolbarInline(),"function"==typeof y.onFocus&&y.onFocus(e,m))},onBlur_wysiwyg:function(t){m._antiBlur||m._variable.isCodeView||(m.hasFocus=!1,m.effectNode=null,m.controllersOff(),(m._isInline||m._isBalloon)&&p._hideToolbar(),m._setKeyEffect([]),m._variable.currentNodes=[],m._variable.currentNodesMap=[],d.showPathLabel&&(e.element.navigation.textContent=""),"function"==typeof y.onBlur&&y.onBlur(t,m,this))},onMouseDown_resizingBar:function(t){t.stopPropagation(),m.submenuOff(),m.controllersOff(),m._variable.resizeClientY=t.clientY,e.element.resizeBackground.style.display="block",f.addEventListener("mousemove",p._resize_editor),f.addEventListener("mouseup",function t(){e.element.resizeBackground.style.display="none",f.removeEventListener("mousemove",p._resize_editor),f.removeEventListener("mouseup",t)})},_resize_editor:function(t){let n=e.element.editorArea.offsetHeight+(t.clientY-m._variable.resizeClientY),i=n<m._variable.minResizingSize?m._variable.minResizingSize:n;e.element.wysiwygFrame.style.height=e.element.code.style.height=i+"px",m._variable.resizeClientY=t.clientY,u.isResizeObserverSupported||m.__callResizeFunction(i,null)},onResize_window:function(){u.isResizeObserverSupported||m.resetResponsiveToolbar();let t=e.element.toolbar,n="none"===t.style.display||m._isInline&&!m._inlineToolbarAttr.isShow;if(0!==t.offsetWidth||n){if(e.fileBrowser&&"block"===e.fileBrowser.area.style.display&&(e.fileBrowser.body.style.maxHeight=h.innerHeight-e.fileBrowser.header.offsetHeight-50+"px"),m.submenuActiveButton&&m.submenu&&m._setMenuPosition(m.submenuActiveButton,m.submenu),m._variable.isFullScreen){m._variable.innerHeight_fullScreen+=h.innerHeight-t.offsetHeight-m._variable.innerHeight_fullScreen,e.element.editorArea.style.height=m._variable.innerHeight_fullScreen+"px";return}if(m._variable.isCodeView&&m._isInline){p._showToolbarInline();return}m._iframeAutoHeight(),m._sticky&&(t.style.width=e.element.topArea.offsetWidth-2+"px",p.onScroll_window())}},onScroll_window:function(){if(m._variable.isFullScreen||0===e.element.toolbar.offsetWidth||d.stickyToolbar<0)return;let t=e.element,n=t.editorArea.offsetHeight,i=(this.scrollY||f.documentElement.scrollTop)+d.stickyToolbar,l=p._getEditorOffsets(d.toolbarContainer).top-(m._isInline?t.toolbar.offsetHeight:0),o=m._isInline&&i-l>0?i-l-e.element.toolbar.offsetHeight:0;i<l?p._offStickyToolbar():i+m._variable.minResizingSize>=n+l?(m._sticky||p._onStickyToolbar(o),t.toolbar.style.top=o+n+l+d.stickyToolbar-i-m._variable.minResizingSize+"px"):i>=l&&p._onStickyToolbar(o)},_getEditorOffsets:function(t){let n=t||e.element.topArea,i=0,l=0,o=0;for(;n;)i+=n.offsetTop,l+=n.offsetLeft,o+=n.scrollTop,n=n.offsetParent;return{top:i,left:l,scroll:o}},_getPageBottomSpace:function(){return f.documentElement.scrollHeight-(p._getEditorOffsets(null).top+e.element.topArea.offsetHeight)},_onStickyToolbar:function(t){let n=e.element;m._isInline||d.toolbarContainer||(n._stickyDummy.style.height=n.toolbar.offsetHeight+"px",n._stickyDummy.style.display="block"),n.toolbar.style.top=d.stickyToolbar+t+"px",n.toolbar.style.width=m._isInline?m._inlineToolbarAttr.width:n.toolbar.offsetWidth+"px",u.addClass(n.toolbar,"se-toolbar-sticky"),m._sticky=!0},_offStickyToolbar:function(){let t=e.element;t._stickyDummy.style.display="none",t.toolbar.style.top=m._isInline?m._inlineToolbarAttr.top:"",t.toolbar.style.width=m._isInline?m._inlineToolbarAttr.width:"",t.editorArea.style.marginTop="",u.removeClass(t.toolbar,"se-toolbar-sticky"),m._sticky=!1},_codeViewAutoHeight:function(){m._variable.isFullScreen||(e.element.code.style.height=e.element.code.scrollHeight+"px")},_hardDelete:function(){let e=m.getRange(),t=e.startContainer,n=e.endContainer,i=u.getRangeFormatElement(t),l=u.getRangeFormatElement(n),o=u.isCell(i),s=u.isCell(l),r=e.commonAncestorContainer;if((o&&!i.previousElementSibling&&!i.parentElement.previousElementSibling||s&&!l.nextElementSibling&&!l.parentElement.nextElementSibling)&&i!==l){if(o){if(s)return u.removeItem(u.getParentElement(i,function(e){return r===e.parentNode})),m.nativeFocus(),!0;u.removeItem(u.getParentElement(i,function(e){return r===e.parentNode}))}else u.removeItem(u.getParentElement(l,function(e){return r===e.parentNode}))}let a=1===t.nodeType?u.getParentElement(t,".se-component"):null,d=1===n.nodeType?u.getParentElement(n,".se-component"):null;return a&&u.removeItem(a),d&&u.removeItem(d),!1},onPaste_wysiwyg:function(e){let t=u.isIE?h.clipboardData:e.clipboardData;return!t||p._dataTransferAction("paste",e,t)},_setClipboardComponent:function(e,t,n){e.preventDefault(),e.stopPropagation(),n.setData("text/html",t.component.outerHTML)},onCopy_wysiwyg:function(e){let t=u.isIE?h.clipboardData:e.clipboardData;if("function"==typeof y.onCopy&&!1===y.onCopy(e,t,m))return e.preventDefault(),e.stopPropagation(),!1;let n=m.currentFileComponentInfo;n&&!u.isIE&&(p._setClipboardComponent(e,n,t),u.addClass(n.component,"se-component-copy"),h.setTimeout(function(){u.removeClass(n.component,"se-component-copy")},150))},onSave_wysiwyg:function(e){if("function"==typeof y.onSave){y.onSave(e,m);return}},onCut_wysiwyg:function(e){let t=u.isIE?h.clipboardData:e.clipboardData;if("function"==typeof y.onCut&&!1===y.onCut(e,t,m))return e.preventDefault(),e.stopPropagation(),!1;let n=m.currentFileComponentInfo;n&&!u.isIE&&(p._setClipboardComponent(e,n,t),u.removeItem(n.component),m.controllersOff()),h.setTimeout(function(){m.history.push(!1)})},onDrop_wysiwyg:function(e){if(m.isReadOnly||u.isIE)return e.preventDefault(),e.stopPropagation(),!1;let t=e.dataTransfer;return!t||(p._setDropLocationSelection(e),m.removeNode(),document.body.contains(m.currentControllerTarget)||m.controllersOff(),p._dataTransferAction("drop",e,t))},_setDropLocationSelection:function(e){let t={startContainer:null,startOffset:null,endContainer:null,endOffset:null},n=null;if(e.rangeParent?(t.startContainer=e.rangeParent,t.startOffset=e.rangeOffset,t.endContainer=e.rangeParent,t.endOffset=e.rangeOffset):n=m._wd.caretRangeFromPoint?m._wd.caretRangeFromPoint(e.clientX,e.clientY):m.getRange(),n&&(t.startContainer=n.startContainer,t.startOffset=n.startOffset,t.endContainer=n.endContainer,t.endOffset=n.endOffset),t.startContainer===t.endContainer){let e=u.getParentElement(t.startContainer,u.isComponent);e&&(t.startContainer=e,t.startOffset=0,t.endContainer=e,t.endOffset=0)}m.setRange(t.startContainer,t.startOffset,t.endContainer,t.endOffset)},_dataTransferAction:function(t,n,i){let l,o;if(u.isIE){l=i.getData("Text");let s=m.getRange(),r=u.createElement("DIV"),a={sc:s.startContainer,so:s.startOffset,ec:s.endContainer,eo:s.endOffset};return r.setAttribute("contenteditable",!0),r.style.cssText="position:absolute; top:0; left:0; width:1px; height:1px; overflow:hidden;",e.element.relative.appendChild(r),r.focus(),h.setTimeout(function(){o=r.innerHTML,u.removeItem(r),m.setRange(a.sc,a.so,a.ec,a.eo),p._setClipboardData(t,n,l,o,i)}),!0}if(l=i.getData("text/plain"),o=i.getData("text/html"),!1===p._setClipboardData(t,n,l,o,i))return n.preventDefault(),n.stopPropagation(),!1},_setClipboardData:function(e,t,n,i,l){let o=/class=["']*Mso(Normal|List)/i.test(i)||/content=["']*Word.Document/i.test(i)||/content=["']*OneNote.File/i.test(i)||/content=["']*Excel.Sheet/i.test(i);i?(i=i.replace(/^<html>\r?\n?<body>\r?\n?\x3C!--StartFragment--\>|\x3C!--EndFragment-->\r?\n?<\/body\>\r?\n?<\/html>$/g,""),o&&(i=i.replace(/\n/g," "),n=n.replace(/\n/g," ")),i=m.cleanHTML(i,m.pasteTagsWhitelistRegExp,m.pasteTagsBlacklistRegExp)):i=u._HTMLConvertor(n).replace(/\n/g,"<br>");let s=m._charCount(m._charTypeHTML?i:n);if("paste"===e&&"function"==typeof y.onPaste){let e=y.onPaste(t,i,s,m);if(!1===e)return!1;if("string"==typeof e){if(!e)return!1;i=e}}if("drop"===e&&"function"==typeof y.onDrop){let e=y.onDrop(t,i,s,m);if(!1===e)return!1;if("string"==typeof e){if(!e)return!1;i=e}}let r=l.files;return r.length>0&&!o?(/^image/.test(r[0].type)&&m.plugins.image&&y.insertImage(r),!1):!!s&&(i?(y.insertHTML(i,!0,!1),!1):void 0)},onMouseMove_wysiwyg:function(t){if(m.isDisabled||m.isReadOnly)return!1;let n=u.getParentElement(t.target,u.isComponent),i=m._lineBreaker.style;if(n&&!m.currentControllerName){let l=e.element,o=0,s=l.wysiwyg;do o+=s.scrollTop,s=s.parentElement;while(s&&!/^(BODY|HTML)$/i.test(s.nodeName));let r=l.wysiwyg.scrollTop,a=p._getEditorOffsets(null),c=u.getOffset(n,l.wysiwygFrame).top+r,f=t.pageY+o+(d.iframe&&!d.toolbarContainer?l.toolbar.offsetHeight:0),h=c+(d.iframe?o:a.top),g=u.isListCell(n.parentNode),y="",C="";if((g?n.previousSibling:u.isFormatElement(n.previousElementSibling))||!(f<h+20)){if((g?n.nextSibling:u.isFormatElement(n.nextElementSibling))||!(f>h+n.offsetHeight-20)){i.display="none";return}C=c+n.offsetHeight,y="b"}else C=c,y="t";m._variable._lineBreakComp=n,m._variable._lineBreakDir=y,i.top=C-r+"px",m._lineBreakerButton.style.left=u.getOffset(n).left+n.offsetWidth/2-15+"px",i.display="block"}else"none"!==i.display&&(i.display="none")},_enterPrevent:function(e){e.preventDefault(),u.isMobile&&m.__focusTemp.focus()},_onMouseDown_lineBreak:function(e){e.preventDefault()},_onLineBreak:function(e){e.preventDefault();let t=m._variable._lineBreakComp,n=this?this:m._variable._lineBreakDir,i=u.isListCell(t.parentNode),l=u.createElement(i?"BR":u.isCell(t.parentNode)?"DIV":d.defaultTag);if(i||(l.innerHTML="<br>"),m._charTypeHTML&&!m.checkCharCount(l.outerHTML,"byte-html"))return;t.parentNode.insertBefore(l,"t"===n?t:t.nextSibling),m._lineBreaker.style.display="none",m._variable._lineBreakComp=null;let o=i?l:l.firstChild;m.setRange(o,1,o,1),m.history.push(!1)},_resizeObserver:null,_toolbarObserver:null,_addEvent:function(){let t=d.iframe?m._ww:e.element.wysiwyg;u.isResizeObserverSupported&&(this._resizeObserver=new h.ResizeObserver(function(e){m.__callResizeFunction(-1,e[0])})),e.element.toolbar.addEventListener("mousedown",p._buttonsEventHandler,!1),e.element._menuTray.addEventListener("mousedown",p._buttonsEventHandler,!1),e.element.toolbar.addEventListener("click",p.onClick_toolbar,!1),t.addEventListener("mousedown",p.onMouseDown_wysiwyg,!1),t.addEventListener("click",p.onClick_wysiwyg,!1),t.addEventListener(u.isIE?"textinput":"input",p.onInput_wysiwyg,!1),t.addEventListener("keydown",p.onKeyDown_wysiwyg,!1),t.addEventListener("keyup",p.onKeyUp_wysiwyg,!1),t.addEventListener("paste",p.onPaste_wysiwyg,!1),t.addEventListener("copy",p.onCopy_wysiwyg,!1),t.addEventListener("cut",p.onCut_wysiwyg,!1),t.addEventListener("drop",p.onDrop_wysiwyg,!1),t.addEventListener("scroll",p.onScroll_wysiwyg,!1),t.addEventListener("focus",p.onFocus_wysiwyg,!1),t.addEventListener("blur",p.onBlur_wysiwyg,!1),p._lineBreakerBind={a:p._onLineBreak.bind(""),t:p._onLineBreak.bind("t"),b:p._onLineBreak.bind("b")},t.addEventListener("mousemove",p.onMouseMove_wysiwyg,!1),m._lineBreakerButton.addEventListener("mousedown",p._onMouseDown_lineBreak,!1),m._lineBreakerButton.addEventListener("click",p._lineBreakerBind.a,!1),e.element.lineBreaker_t.addEventListener("mousedown",p._lineBreakerBind.t,!1),e.element.lineBreaker_b.addEventListener("mousedown",p._lineBreakerBind.b,!1),t.addEventListener("touchstart",p.onMouseDown_wysiwyg,{passive:!0,useCapture:!1}),t.addEventListener("touchend",p.onClick_wysiwyg,{passive:!0,useCapture:!1}),"auto"!==d.height||d.codeMirrorEditor||(e.element.code.addEventListener("keydown",p._codeViewAutoHeight,!1),e.element.code.addEventListener("keyup",p._codeViewAutoHeight,!1),e.element.code.addEventListener("paste",p._codeViewAutoHeight,!1)),e.element.resizingBar&&(/\d+/.test(d.height)&&d.resizeEnable?e.element.resizingBar.addEventListener("mousedown",p.onMouseDown_resizingBar,!1):u.addClass(e.element.resizingBar,"se-resizing-none")),p._setResponsiveToolbar(),u.isResizeObserverSupported&&(this._toolbarObserver=new h.ResizeObserver(m.resetResponsiveToolbar)),h.addEventListener("resize",p.onResize_window,!1),d.stickyToolbar>-1&&h.addEventListener("scroll",p.onScroll_window,!1)},_removeEvent:function(){let t=d.iframe?m._ww:e.element.wysiwyg;e.element.toolbar.removeEventListener("mousedown",p._buttonsEventHandler),e.element._menuTray.removeEventListener("mousedown",p._buttonsEventHandler),e.element.toolbar.removeEventListener("click",p.onClick_toolbar),t.removeEventListener("mousedown",p.onMouseDown_wysiwyg),t.removeEventListener("click",p.onClick_wysiwyg),t.removeEventListener(u.isIE?"textinput":"input",p.onInput_wysiwyg),t.removeEventListener("keydown",p.onKeyDown_wysiwyg),t.removeEventListener("keyup",p.onKeyUp_wysiwyg),t.removeEventListener("paste",p.onPaste_wysiwyg),t.removeEventListener("copy",p.onCopy_wysiwyg),t.removeEventListener("cut",p.onCut_wysiwyg),t.removeEventListener("drop",p.onDrop_wysiwyg),t.removeEventListener("scroll",p.onScroll_wysiwyg),t.removeEventListener("mousemove",p.onMouseMove_wysiwyg),m._lineBreakerButton.removeEventListener("mousedown",p._onMouseDown_lineBreak),m._lineBreakerButton.removeEventListener("click",p._lineBreakerBind.a),e.element.lineBreaker_t.removeEventListener("mousedown",p._lineBreakerBind.t),e.element.lineBreaker_b.removeEventListener("mousedown",p._lineBreakerBind.b),p._lineBreakerBind=null,t.removeEventListener("touchstart",p.onMouseDown_wysiwyg,{passive:!0,useCapture:!1}),t.removeEventListener("touchend",p.onClick_wysiwyg,{passive:!0,useCapture:!1}),t.removeEventListener("focus",p.onFocus_wysiwyg),t.removeEventListener("blur",p.onBlur_wysiwyg),e.element.code.removeEventListener("keydown",p._codeViewAutoHeight),e.element.code.removeEventListener("keyup",p._codeViewAutoHeight),e.element.code.removeEventListener("paste",p._codeViewAutoHeight),e.element.resizingBar&&e.element.resizingBar.removeEventListener("mousedown",p.onMouseDown_resizingBar),p._resizeObserver&&(p._resizeObserver.unobserve(e.element.wysiwygFrame),p._resizeObserver=null),p._toolbarObserver&&(p._toolbarObserver.unobserve(e.element._toolbarShadow),p._toolbarObserver=null),h.removeEventListener("resize",p.onResize_window),h.removeEventListener("scroll",p.onScroll_window)},_setResponsiveToolbar:function(){if(0===c.length){c=null;return}p._responsiveCurrentSize="default";let e=p._responsiveButtonSize=[],t=p._responsiveButtons={default:c[0]};for(let n=1,i=c.length,l,o;n<i;n++)l=1*(o=c[n])[0],e.push(l),t[l]=o[1];e.sort(function(e,t){return e-t}).unshift("default")}},y={core:m,util:u,onload:null,onScroll:null,onMouseDown:null,onClick:null,onInput:null,onKeyDown:null,onKeyUp:null,onCopy:null,onCut:null,onFocus:null,onBlur:null,onChange:null,onSave:null,onDrop:null,onPaste:null,showInline:null,showController:null,toggleCodeView:null,toggleFullScreen:null,imageUploadHandler:null,videoUploadHandler:null,audioUploadHandler:null,onImageUploadBefore:null,onVideoUploadBefore:null,onAudioUploadBefore:null,onImageUpload:null,onVideoUpload:null,onAudioUpload:null,onImageUploadError:null,onVideoUploadError:null,onAudioUploadError:null,onResizeEditor:null,onSetToolbarButtons:null,setToolbarButtons:function(t){m.submenuOff(),m.containerOff(),m.moreLayerOff();let n=i.Z._createToolBar(f,t,m.plugins,d);c=n.responsiveButtons,p._setResponsiveToolbar(),e.element.toolbar.replaceChild(n._buttonTray,e.element._buttonTray);let o=(0,l.Z)(e.element.originElement,m._getConstructed(e.element),d);e.element=o.element,e.tool=o.tool,d.iframe&&(e.element.wysiwyg=m._wd.body),m._recoverButtonStates(),m._cachingButtons(),m.history._resetCachingButton(),m.effectNode=null,m.hasFocus&&p._applyTagEffects(),m.isReadOnly&&u.setDisabledButtons(!0,m.resizingDisabledButtons),"function"==typeof y.onSetToolbarButtons&&y.onSetToolbarButtons(n._buttonTray.querySelectorAll("button"),m)},setOptions:function(l){p._removeEvent(),m._resetComponents(),u.removeClass(m._styleCommandMap.showBlocks,"active"),u.removeClass(m._styleCommandMap.codeView,"active"),m._variable.isCodeView=!1,m._iframeAuto=null,m.plugins=l.plugins||m.plugins;let o=[d,l].reduce(function(e,t){for(let n in t)if(u.hasOwn(t,n)){if("plugins"===n&&t[n]&&e[n]){let i=e[n],l=t[n];i=i.length?i:h.Object.keys(i).map(function(e){return i[e]}),l=l.length?l:h.Object.keys(l).map(function(e){return l[e]}),e[n]=l.filter(function(e){return -1===i.indexOf(e)}).concat(i)}else e[n]=t[n]}return e},{}),s=e.element,r=s.wysiwyg.innerHTML,f=i.Z._setOptions(o,e,d);f.callButtons&&(t=f.callButtons,m.initPlugins={}),f.plugins&&(m.plugins=n=f.plugins),0===s._menuTray.children.length&&(this._menuTray={}),c=f.toolbar.responsiveButtons,m.options=d=o,m.lang=a=d.lang,d.iframe&&s.wysiwygFrame.addEventListener("load",function(){u._setIframeDocument(this,d),m._setOptionsInit(s,r)}),s.editorArea.appendChild(s.wysiwygFrame),d.iframe||m._setOptionsInit(s,r)},setDefaultStyle:function(t){let n=d._editorStyles=u._setDefaultOptionStyle(d,t),i=e.element;i.topArea.style.cssText=n.top,i.code.style.cssText=d._editorStyles.frame,i.code.style.display="none","auto"===d.height?i.code.style.overflow="hidden":i.code.style.overflow="",d.iframe?(i.wysiwygFrame.style.cssText=n.frame,i.wysiwyg.style.cssText=n.editor):i.wysiwygFrame.style.cssText=n.frame+n.editor},noticeOpen:function(e){m.notice.open.call(m,e)},noticeClose:function(){m.notice.close.call(m)},save:function(){let t=m.getContents(!1);e.element.originElement.value=t,p.onSave_wysiwyg(t,m)},getContext:function(){return e},getContents:function(e){return m.getContents(e)},getText:function(){return e.element.wysiwyg.textContent},getCharCount:function(t){return t="string"==typeof t?t:d.charCounterType,m.getCharLength(m._charTypeHTML?e.element.wysiwyg.innerHTML:e.element.wysiwyg.textContent,t)},getImagesInfo:function(){return e.image?e.image._infoList:[]},getFilesInfo:function(t){return e[t]?e[t]._infoList:[]},insertImage:function(e){m.plugins.image&&e&&(m.initPlugins.image?m.plugins.image.submitAction.call(m,e):m.callPlugin("image",m.plugins.image.submitAction.bind(m,e),null),m.focus())},insertHTML:function(t,n,i,l){if(e.element.wysiwygFrame.contains(m.getSelection().focusNode)||m.focus(),"string"==typeof t){n||(t=m.cleanHTML(t,null,null));try{let e,n,o,s,r;if(u.isListCell(u.getFormatElement(m.getSelectionNode(),null))){let e=f.createRange().createContextualFragment(t).childNodes;m._isFormatData(e)&&(t=m._convertListCell(e))}let a=f.createRange().createContextualFragment(t).childNodes;if(i){let e=m._charTypeHTML?"outerHTML":"textContent",t="";for(let n=0,i=a.length;n<i;n++)t+=a[n][e];if(!m.checkCharCount(t,null))return}for(;e=a[0];){if(s&&3===s.nodeType&&n&&1===n.nodeType&&u.isBreak(e)){s=e,u.removeItem(e);continue}n=(o=m.insertNode(e,n,!1)).container||o,r||(r=o),s=e}3===s.nodeType&&1===n.nodeType&&(n=s);let d=3===n.nodeType?o.endOffset||n.textContent.length:n.childNodes.length;l?m.setRange(r.container||r,r.startOffset||0,n,d):m.setRange(n,d,n,d)}catch(e){if(m.isDisabled||m.isReadOnly)return;console.warn("[SUNEDITOR.insertHTML.fail] "+e),m.execCommand("insertHTML",!1,t)}}else if(u.isComponent(t))m.insertComponent(t,!1,i,!1);else{let e=null;(u.isFormatElement(t)||u.isMedia(t))&&(e=u.getFormatElement(m.getSelectionNode(),null)),m.insertNode(t,e,i)}m.effectNode=null,m.focus(),m.history.push(!1)},setContents:function(e){m.setContents(e)},appendContents:function(t){let n=m.convertContentsForEditor(t);if(m._variable.isCodeView)m._setCodeView(m._getCodeView()+"\n"+m.convertHTMLForCodeView(n,!1));else{let t=u.createElement("DIV");t.innerHTML=n;let i=e.element.wysiwyg,l=t.children;for(let e=0,t=l.length;e<t;e++)l[e]&&i.appendChild(l[e])}m.history.push(!1)},readOnly:function(t){m.isReadOnly=t,u.setDisabledButtons(!!t,m.resizingDisabledButtons),t?(m.controllersOff(),m.submenuActiveButton&&m.submenuActiveButton.disabled&&m.submenuOff(),m._moreLayerActiveButton&&m._moreLayerActiveButton.disabled&&m.moreLayerOff(),m.containerActiveButton&&m.containerActiveButton.disabled&&m.containerOff(),m.modalForm&&m.plugins.dialog.close.call(m),e.element.code.setAttribute("readOnly","true"),u.addClass(e.element.wysiwygFrame,"se-read-only")):(e.element.code.removeAttribute("readOnly"),u.removeClass(e.element.wysiwygFrame,"se-read-only")),d.codeMirrorEditor&&d.codeMirrorEditor.setOption("readOnly",!!t)},disable:function(){this.toolbar.disable(),this.wysiwyg.disable()},disabled:function(){this.disable()},enable:function(){this.toolbar.enable(),this.wysiwyg.enable()},enabled:function(){this.enable()},show:function(){let t=e.element.topArea.style;"none"===t.display&&(t.display=d.display)},hide:function(){e.element.topArea.style.display="none"},destroy:function(){for(let t in m.submenuOff(),m.containerOff(),m.controllersOff(),m.notice&&m.notice.close.call(m),m.modalForm&&m.plugins.dialog.close.call(m),m.history._destroy(),p._removeEvent(),u.removeItem(e.element.toolbar),u.removeItem(e.element.topArea),m.functions)u.hasOwn(m,t)&&delete m.functions[t];for(let e in m)u.hasOwn(m,e)&&delete m[e];for(let e in p)u.hasOwn(p,e)&&delete p[e];for(let t in e)u.hasOwn(e,t)&&delete e[t];for(let e in t)u.hasOwn(t,e)&&delete t[e];for(let e in this)u.hasOwn(this,e)&&delete this[e]},toolbar:{disable:function(){m.submenuOff(),m.moreLayerOff(),m.containerOff(),e.tool.cover.style.display="block"},disabled:function(){this.disable()},enable:function(){e.tool.cover.style.display="none"},enabled:function(){this.enable()},show:function(){m._isInline?p._showToolbarInline():(e.element.toolbar.style.display="",e.element._stickyDummy.style.display=""),p.onResize_window()},hide:function(){m._isInline?p._hideToolbar():(e.element.toolbar.style.display="none",e.element._stickyDummy.style.display="none"),p.onResize_window()}},wysiwyg:{disable:function(){m.controllersOff(),m.modalForm&&m.plugins.dialog.close.call(m),e.element.wysiwyg.setAttribute("contenteditable",!1),m.isDisabled=!0,d.codeMirrorEditor?d.codeMirrorEditor.setOption("readOnly",!0):e.element.code.setAttribute("disabled","disabled")},enable:function(){e.element.wysiwyg.setAttribute("contenteditable",!0),m.isDisabled=!1,d.codeMirrorEditor?d.codeMirrorEditor.setOption("readOnly",!1):e.element.code.removeAttribute("disabled")}}};m.functions=y,m.options=d;let C=e.element,_=C.originElement,b=C.topArea;return _.style.display="none",b.style.display="block",d.iframe&&C.wysiwygFrame.addEventListener("load",function(){u._setIframeDocument(this,d),m._editorInit(!1,d.value),d.value=null}),"object"==typeof _.nextElementSibling?_.parentNode.insertBefore(b,_.nextElementSibling):_.parentNode.appendChild(b),C.editorArea.appendChild(C.wysiwygFrame),C=_=b=null,d.iframe||(m._editorInit(!1,d.value),d.value=null),y}}}]);