"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/components/charts/CustomPieChart.jsx":
/*!**************************************************!*\
  !*** ./src/components/charts/CustomPieChart.jsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_x_charts_PieChart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/x-charts/PieChart */ \"(app-pages-browser)/./node_modules/@mui/x-charts/PieChart/PieChart.js\");\n/* harmony import */ var _assets_images_icons_noDataPie_svg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/assets/images/icons/noDataPie.svg */ \"(app-pages-browser)/./src/assets/images/icons/noDataPie.svg\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst CustomPieChart = (param)=>{\n    let { chart, donuts } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const hasData = chart.dataset?.some((item)=>item[\"value\"] > 0);\n    const hasntData = chart.dataset?.length === 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\"\n        },\n        children: hasData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_charts_PieChart__WEBPACK_IMPORTED_MODULE_4__.PieChart, {\n            series: [\n                {\n                    data: chart.dataset,\n                    arcLabel: (item)=>item.value > 0 ? `${t(`statsDash:${item.value}`)}` : \"\",\n                    innerRadius: donuts ? 50 : 90,\n                    outerRadius: donuts ? 90 : 0,\n                    paddingAngle: 1,\n                    cornerRadius: 4,\n                    highlightScope: {\n                        fade: \"global\",\n                        highlight: \"item\"\n                    },\n                    faded: {\n                        innerRadius: donuts ? 30 : 70,\n                        additionalRadius: donuts ? -30 : -20,\n                        color: \"gray\"\n                    }\n                }\n            ],\n            slotProps: {\n                legend: {\n                    hidden: true\n                }\n            },\n            width: 300,\n            height: 200,\n            colors: chart.colors,\n            sx: {\n                marginLeft: \"30%\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\charts\\\\CustomPieChart.jsx\",\n            lineNumber: 13,\n            columnNumber: 9\n        }, undefined) : (!hasData || hasntData) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_noDataPie_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\charts\\\\CustomPieChart.jsx\",\n            lineNumber: 48,\n            columnNumber: 36\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\charts\\\\CustomPieChart.jsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomPieChart, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = CustomPieChart;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CustomPieChart);\nvar _c;\n$RefreshReg$(_c, \"CustomPieChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/charts/CustomPieChart.jsx\n"));

/***/ })

});