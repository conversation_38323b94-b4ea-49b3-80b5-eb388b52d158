import { i18nRouter } from "next-i18n-router";
import { jwtVerify } from "jose";
import { NextResponse } from "next/server";
import cookie from "cookie";

import i18nConfig from "../i18nConfig";
import { Role } from "./utils/constants";
import { getRoutesListByRole } from "./utils/functions";
import translations from "./config/translations";
import { allowedParams } from "./config/allowedParams";
import {
  adminRoutes,
  authRoutes,
  baseUrlBackoffice,
  baseUrlFrontoffice,
  candidateRoutes,
} from "./helpers/routesList";

const SECURITY_CONFIG = {
  MAX_REQUESTS_PER_MINUTE: 60,
  JWT_ALGORITHM: "HS256",
  SUSPICIOUS_PATTERNS: [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /eval\s*\(/gi,
    /expression\s*\(/gi,
    /%3Cscript/gi,
    /%3C%2Fscript%3E/gi,
  ],
};

const rateLimitStore = new Map();

const logSecurityEvent = (event, details = {}) => {
  if (typeof window === "undefined") {
    console.warn(`[SECURITY] ${event}:`, {
      timestamp: new Date().toISOString(),
      ...details,
    });
  }
};

export const verifyToken = async (token, clientIP = "unknown") => {
  try {
    if (!token || typeof token !== "string") {
      logSecurityEvent("INVALID_TOKEN_FORMAT", {
        clientIP,
        reason: "Missing or invalid token",
      });
      return null;
    }

    const jwtRegex = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
    if (!jwtRegex.test(token)) {
      logSecurityEvent("INVALID_JWT_FORMAT", {
        clientIP,
        tokenPrefix: token.substring(0, 10),
      });
      return null;
    }

    const jwtSecret = process.env.NEXT_JWT_SECRET;
    if (!jwtSecret || jwtSecret.length < 32) {
      logSecurityEvent("WEAK_JWT_SECRET", { clientIP });
      throw new Error("JWT secret configuration error");
    }

    const secret = new TextEncoder().encode(jwtSecret);

    const { payload } = await jwtVerify(token, secret, {
      algorithms: [SECURITY_CONFIG.JWT_ALGORITHM],
      // issuer: process.env.JWT_ISSUER,
      // audience: process.env.JWT_AUDIENCE
    });

    if (
      !payload ||
      !payload._id ||
      !payload.roles ||
      !Array.isArray(payload.roles)
    ) {
      logSecurityEvent("INVALID_TOKEN_PAYLOAD", {
        clientIP,
        hasId: !!payload?._id,
        hasRoles: !!payload?.roles,
      });
      return null;
    }

    const tokenAge = Date.now() / 1000 - (payload.iat || 0);
    if (tokenAge > 86400) {
      logSecurityEvent("OLD_TOKEN_USAGE", {
        clientIP,
        tokenAge,
        userId: payload._id,
      });
    }

    return payload;
  } catch (error) {
    if (error.name === "JWTExpired") {
      logSecurityEvent("TOKEN_EXPIRED", { clientIP });
    } else if (error.name === "JWTInvalid") {
      logSecurityEvent("INVALID_TOKEN", { clientIP, error: error.message });
    } else {
      logSecurityEvent("TOKEN_VERIFICATION_ERROR", {
        clientIP,
        error: error.message,
      });
    }
    return null;
  }
};

const checkRateLimit = (clientIP) => {
  const now = Date.now();
  const windowStart = now - 60000; 

  if (!rateLimitStore.has(clientIP)) {
    rateLimitStore.set(clientIP, []);
  }

  const requests = rateLimitStore.get(clientIP);

  const validRequests = requests.filter((timestamp) => timestamp > windowStart);
  rateLimitStore.set(clientIP, validRequests);

  if (validRequests.length >= SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE) {
    return false;
  }

  validRequests.push(now);
  return true;
};

const sanitizeInput = (value) => {
  if (typeof value !== "string") return value;

  let sanitized = value;
  SECURITY_CONFIG.SUSPICIOUS_PATTERNS.forEach((pattern) => {
    sanitized = sanitized.replace(pattern, "");
  });

  return sanitized.trim();
};

const setSecurityHeaders = (response) => {
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("X-Frame-Options", "DENY");
  response.headers.set("X-XSS-Protection", "1; mode=block");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  response.headers.set(
    "Permissions-Policy",
    "geolocation=(), microphone=(), camera=()"
  );

  response.headers.delete("Server");
  response.headers.delete("X-Powered-By");

  return response;
};

export async function middleware(req) {
  const url = req.nextUrl.clone();
  const { defaultLocale } = i18nConfig;
  const { pathname } = req.nextUrl;
  const clientIP =
    req.ip ||
    req.headers.get("x-forwarded-for") ||
    req.headers.get("x-real-ip") ||
    "unknown";

  let response = NextResponse.next();
  response = setSecurityHeaders(response);

  if (!checkRateLimit(clientIP)) {
    logSecurityEvent("RATE_LIMIT_EXCEEDED", { clientIP, pathname });
    return new NextResponse("Too Many Requests", {
      status: 429,
      headers: {
        "Retry-After": "60",
        "X-RateLimit-Limit": SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE.toString(),
        "X-RateLimit-Remaining": "0",
      },
    });
  }

  let hasModifiedParams = false;
  for (const [key, value] of url.searchParams.entries()) {
    const sanitizedValue = sanitizeInput(value);
    if (sanitizedValue !== value) {
      url.searchParams.set(key, sanitizedValue);
      hasModifiedParams = true;
      logSecurityEvent("SUSPICIOUS_QUERY_PARAM", {
        clientIP,
        key,
        originalValue: value.substring(0, 50),
      });
    }
  }

  const pathString = pathname.toLowerCase();
  const hasSuspiciousPath = SECURITY_CONFIG.SUSPICIOUS_PATTERNS.some(
    (pattern) => pattern.test(pathString)
  );

  if (hasSuspiciousPath) {
    logSecurityEvent("SUSPICIOUS_PATH_ACCESS", { clientIP, pathname });
    return new NextResponse("Forbidden", { status: 403 });
  }

  const cookies = cookie.parse(req.headers.get("cookie") || "");
  const { accessToken, refreshToken } = cookies;

  const isProtectedRoute =
    pathname.includes("dashboard") || pathname.includes("backoffice");

  if (isProtectedRoute && !(accessToken && refreshToken)) {
    logSecurityEvent("UNAUTHORIZED_ACCESS_ATTEMPT", { clientIP, pathname });
    url.pathname = `/${authRoutes.login.route}/`;
    return NextResponse.redirect(url);
  }

  let user = null;
  if (refreshToken) {
    user = await verifyToken(refreshToken, clientIP);

    if (isProtectedRoute && !user) {
      logSecurityEvent("INVALID_TOKEN_PROTECTED_ROUTE", { clientIP, pathname });
      url.pathname = `/${authRoutes.login.route}/`;
      return NextResponse.redirect(url);
    }
  }

  // Handle logout route
  if (
    pathname === `/${authRoutes.logout.route}/` ||
    pathname === `/fr/${authRoutes.logout.route}/`
  ) {
    logSecurityEvent("USER_LOGOUT", { clientIP, userId: user?._id });
    return i18nRouter(req, i18nConfig);
  }

  // Enhanced role-based access control
  if (user) {
    const menuList = getRoutesListByRole(user);

    // Validate user roles
    if (!user.roles || !Array.isArray(user.roles) || user.roles.length === 0) {
      logSecurityEvent("INVALID_USER_ROLES", { clientIP, userId: user._id });
      url.pathname = `/${authRoutes.login.route}/`;
      return NextResponse.redirect(url);
    }

    const checkRole =
      !menuList?.some((item) => pathname.includes(item)) &&
      (pathname?.includes(`/${baseUrlFrontoffice.baseURL.route}`) ||
        pathname?.includes(`/${baseUrlBackoffice.baseURL.route}`));

    // Enhanced role checking with security logging
    if (
      checkRole ||
      pathname === `/fr/${authRoutes.register.route}/` ||
      pathname === `/fr/${authRoutes.login.route}/` ||
      pathname === `/${authRoutes.register.route}/` ||
      pathname === `/${authRoutes.login.route}/`
    ) {
      let redirectPath = null;

      if (user.roles.includes(Role.ADMIN)) {
        redirectPath = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.home.route}/`;
      } else if (user.roles.includes(Role.EDITOR)) {
        redirectPath = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;
      } else if (user.roles.includes(Role.CANDIDATE)) {
        redirectPath = `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.myApplications.route}`;
      }

      if (redirectPath) {
        logSecurityEvent("ROLE_BASED_REDIRECT", {
          clientIP,
          userId: user._id,
          roles: user.roles,
          fromPath: pathname,
          toPath: redirectPath,
        });
        url.pathname = redirectPath;
        return NextResponse.redirect(url);
      } else {
        logSecurityEvent("NO_VALID_ROLE_REDIRECT", {
          clientIP,
          userId: user._id,
          roles: user.roles,
        });
        url.pathname = `/${authRoutes.login.route}/`;
        return NextResponse.redirect(url);
      }
    }
  }

  // Enhanced parameter filtering with security logging
  let removedParams = [];
  for (const param of url.searchParams.keys()) {
    if (!allowedParams.has(param)) {
      url.searchParams.delete(param);
      removedParams.push(param);
    }
  }

  if (removedParams.length > 0) {
    logSecurityEvent("REMOVED_DISALLOWED_PARAMS", {
      clientIP,
      pathname,
      removedParams,
      userId: user?._id,
    });
  }

  // Check if parameters were modified (either sanitized or removed)
  if (
    hasModifiedParams ||
    url.searchParams.toString() !== req.nextUrl.searchParams.toString()
  ) {
    return NextResponse.redirect(url);
  }

  // Enhanced redirection paths with security checks
  const frPath = translations[req.nextUrl.pathname];
  if (frPath) {
    logSecurityEvent("FRENCH_PATH_REDIRECT", {
      clientIP,
      fromPath: req.nextUrl.pathname,
      toPath: frPath,
    });
    return NextResponse.redirect(new URL(frPath, req.url));
  }

  // Enhanced language handling with security validation
  if (
    !pathname.startsWith("/fr") &&
    !pathname.startsWith(`/${defaultLocale}`) &&
    !pathname.startsWith("/_next") &&
    !pathname.startsWith("/api") &&
    !pathname.startsWith("/static") &&
    !pathname.includes(".")
  ) {
    // Additional security check for suspicious paths
    if (pathname.length > 200) {
      logSecurityEvent("SUSPICIOUS_LONG_PATH", {
        clientIP,
        pathLength: pathname.length,
      });
      return new NextResponse("Bad Request", { status: 400 });
    }

    url.pathname = `/en${pathname}`;
    return NextResponse.rewrite(url);
  }

  // Log successful requests for monitoring (in development only)
  if (process.env.NODE_ENV === "development" && user) {
    logSecurityEvent("SUCCESSFUL_REQUEST", {
      clientIP,
      pathname,
      userId: user._id,
      roles: user.roles,
    });
  }

  return i18nRouter(req, i18nConfig);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - files with extensions (images, fonts, etc.)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|public|.*\\..*).*)",
  ],
};
