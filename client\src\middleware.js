import { i18nRouter } from "next-i18n-router";
import { jwtVerify } from "jose";
import { NextResponse } from "next/server";
import cookie from "cookie";

import i18nConfig from "../i18nConfig";
import { Role } from "./utils/constants";
import { getRoutesListByRole } from "./utils/functions";
import translations from "./config/translations";
import { allowedParams } from "./config/allowedParams";
import {
  adminRoutes,
  authRoutes,
  baseUrlBackoffice,
  baseUrlFrontoffice,
  candidateRoutes,
} from "./helpers/routesList";

export const verifyToken = async (token) => {
  try {
    const secret = new TextEncoder().encode(process.env.NEXT_JWT_SECRET);
    const { payload } = await jwtVerify(token, secret);
    return payload;
  } catch (error) {
    return null;
  }
};

export async function middleware(req) {
  const url = req.nextUrl.clone();
  const { defaultLocale } = i18nConfig;
  const { pathname } = req.nextUrl;

  // authentication and authorisation
  const cookies = cookie.parse(req.headers.get("cookie") || "");
  const { accessToken, refreshToken } = cookies;

  if (
    !(accessToken && refreshToken) &&
    (pathname.includes(`/dashboard`) || pathname.includes(`/backoffice`))
  ) {
    url.pathname = `/${authRoutes.login.route}/`;
    return NextResponse.redirect(url);
  }

  const user = await verifyToken(refreshToken);

  if (
    pathname === `/${authRoutes.logout.route}/` ||
    pathname === `/fr/${authRoutes.logout.route}/`
  ) {
    return i18nRouter(req, i18nConfig);
  }

  if (user) {
    const menuList = getRoutesListByRole(user);

    const checkRole =
      !menuList?.some((item) => pathname.includes(item)) &&
      (pathname?.includes(`/${baseUrlFrontoffice.baseURL.route}`) ||
        pathname?.includes(`/${baseUrlBackoffice.baseURL.route}`));

    if (
      checkRole ||
      pathname === `/fr/${authRoutes.register.route}/` ||
      pathname === `/fr/${authRoutes.login.route}/` ||
      pathname === `/${authRoutes.register.route}/` ||
      pathname === `/${authRoutes.login.route}/`
    ) {
      if (user.roles?.includes(Role.ADMIN))
        url.pathname = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.home.route}/`;
      if (user.roles?.includes(Role.EDITOR))
        url.pathname = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;
      if (user?.roles?.includes(Role.CANDIDATE))
        url.pathname = `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.myApplications.route}`;

      return NextResponse.redirect(url);
    }
  }

  // remove any unallowed params
  for (const param of url.searchParams.keys()) {
    if (!allowedParams.has(param)) {
      url.searchParams.delete(param);
    }
  }

  if (url.searchParams.toString() !== req.nextUrl.searchParams.toString()) {
    return NextResponse.redirect(url);
  }

  // redirection paths
  const frPath = translations[req.nextUrl.pathname];

  if (frPath) return NextResponse.redirect(new URL(frPath, req.url));

  // translate links
  if (
    !pathname.startsWith("/fr") &&
    !pathname.startsWith(`/${defaultLocale}`)
  ) {
    url.pathname = `/en${pathname}`;
    return NextResponse.rewrite(url);
  }

  return i18nRouter(req, i18nConfig);
}

export const config = {
  matcher: "/((?!api|static|.*\\..*|_next).*)",
};
