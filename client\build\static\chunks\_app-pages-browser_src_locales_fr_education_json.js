"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_locales_fr_education_json"],{

/***/ "(app-pages-browser)/./src/locales/fr/education.json":
/*!***************************************!*\
  !*** ./src/locales/fr/education.json ***!
  \***************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"titeleeditedu":"Modifier Education","titleajouteredu":"Ajouter Education","degree":"diplome*","startdate":"date debut*","enddate":"date fin*","university":"Université*","fieldofstudy":"Spécialité","message":"Voulez-vous vraiment supprimer l\'éducation","addsuccess":"Education ajouter avec succée","noEducations":"Sans education"}');

/***/ })

}]);