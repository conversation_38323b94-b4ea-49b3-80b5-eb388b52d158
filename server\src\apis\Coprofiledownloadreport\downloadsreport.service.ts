
import { DownloadReportModel } from "./downloadsreport.model";
import path from "path";
import { sendEmail } from "@/utils/services";
import { DownloadReportQuery } from "types/request/DownloadReportQuery";
import { MESSAGES } from "@/utils/helpers/messages";
import HttpException from "@/utils/exceptions/http.exception";

class DownloadReportService {

    private readonly DownloadReport = DownloadReportModel;

    public async handleReportDownload(fullName: string, email: string): Promise<string> {
        if (!fullName || !email) {
            throw new HttpException(400, MESSAGES.REPORT.FULLNAME_EMAIL_REQUIRED);
        }
        const filePath = path.join(__dirname, '/../../../src/public/static/Pentabell Corporate Profile.pdf');
        const downloadUrl = 'https://www.pentabell.com/Pentabell Corporate Profile.pdf';
        const existingUser = await this.DownloadReport.findOne({ email: email.toLowerCase() });
        await sendEmail({
            to: email,
            subject: 'Your Corporate Profile is Ready to Download',
            template: 'downloadReport',
            context: {
                fullName,
                email,
                downloadUrl,
            },
            attachments: [
                {
                    filename: 'Pentabell Corporate Profile.pdf',
                    path: filePath,
                    contentType: 'application/pdf',
                },
            ],
        });
        if (!existingUser) {
            await this.DownloadReport.create({ fullName, email });
            return MESSAGES.REPORT.EMAIL_SENT_NEW_RECORD;
        }
        return MESSAGES.REPORT.EMAIL_SENT_EXISTING_USER;
    }


    public async getAllDownloadReports(query: DownloadReportQuery): Promise<any> {
        const { keyWord, paginated, pageNumber = '1', pageSize = '3' } = query;

        const page = Number(pageNumber) || 1;
        const size = Number(pageSize) || 3;

        const queryConditions: any = {};

        if (keyWord) {
            queryConditions['email'] = {
                $regex: new RegExp(`.*${keyWord}.*`, 'i'),
            };
        }
        const totalDownloads = await this.DownloadReport.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalDownloads / size);

        if (paginated === 'false' || !paginated) {
            const downloads = await this.DownloadReport.find(queryConditions).sort({ createdAt: -1 });

            return {
                totalDownloads,
                downloads,
            };
        }
        const downloads = await this.DownloadReport.find(queryConditions)
            .sort({ createdAt: -1 })
            .skip((page - 1) * size)
            .limit(size);

        return {
            pageNumber: page,
            pageSize: size,
            totalPages,
            totalDownloads,
            downloads,
        };
    }



}




export default DownloadReportService;