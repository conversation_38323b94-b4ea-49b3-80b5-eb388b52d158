
import { DownloadReportModel } from "./downloadsreport.model";
import path from "path";
import { sendEmail } from "@/utils/services";
import { DownloadReportQuery } from "types/request/DownloadReportQuery";
import { MESSAGES } from "@/utils/helpers/messages";
import HttpException from "@/utils/exceptions/http.exception";

class DownloadReportService {

  private readonly DownloadReport = DownloadReportModel;

  public async handleReportDownload(fullName: string, email: string): Promise<string> {
    if (!fullName || !email) {
      throw new HttpException(400, MESSAGES.REPORT.FULLNAME_EMAIL_REQUIRED);
    }

    const filePath = path.join(__dirname, '/../../../src/public/static/Pentabell Corporate Profile.pdf');
    const downloadUrl = 'https://www.pentabell.com/Pentabell Corporate Profile.pdf';
    const now = new Date();

    const existingUser = await this.DownloadReport.findOne({ email: email.toLowerCase() });

    await sendEmail({
      to: email,
      subject: 'Your Corporate Profile is Ready to Download',
      template: 'downloadReport',
      context: {
        fullName,
        email,
        downloadUrl,
      },
    });

    if (!existingUser) {
      await this.DownloadReport.create({
        fullName,
        email,
        downloadHistory: [now],
      });
      return MESSAGES.REPORT.EMAIL_SENT_NEW_RECORD;
    } else {
      existingUser.downloadHistory.push(now);
      await existingUser.save();
      return MESSAGES.REPORT.EMAIL_SENT_EXISTING_USER;
    }
  }

  public async getAllDownloadReports(query: DownloadReportQuery): Promise<{
    pageNumber?: number;
    pageSize?: number;
    totalPages?: number;
    totalDownloads: number;
    downloads: {
      _id: string;
      email: string;
      fullName: string;
      createdAt: Date;
      downloadHistory: string[];
    }[];
  }> {
    const { keyWord, paginated } = query;
 
    const pageNumber = Number(query.pageNumber) || 1;
    const pageSize = Number(query.pageSize) || 5;

  const queryConditions: any = {};

if (keyWord) {
  queryConditions['email'] = {
    $regex: new RegExp(`.*${keyWord}.*`, 'i'),
  };
}

if (query.fromDate || query.toDate) {
  queryConditions.downloadHistory = {};

  if (query.fromDate) {
    queryConditions.downloadHistory.$elemMatch = {
      $gte: new Date(query.fromDate),
    };
  }

  if (query.toDate) {
    queryConditions.downloadHistory = {
      $elemMatch: {
        ...(queryConditions.downloadHistory?.$elemMatch || {}),
        $lte: new Date(query.toDate),
        ...(query.fromDate ? { $gte: new Date(query.fromDate) } : {}),
      },
    };
  }
}

    const totalDownloads = await this.DownloadReport.countDocuments(queryConditions);
    const totalPages = Math.ceil(totalDownloads / pageSize);

    const queryBuilder = this.DownloadReport.find(queryConditions).sort({ createdAt: -1 });


    if (paginated !== 'false' && paginated) {
      const skip = (pageNumber - 1) * pageSize;
      queryBuilder.skip(skip).limit(pageSize);
    }

    const rawDownloads = await queryBuilder;

    const downloads = rawDownloads.map((doc) => ({
      _id: doc._id.toString(),
      email: doc.email,
      fullName: doc.fullName,
      createdAt: doc.createdAt,
      downloadHistory: doc.downloadHistory.map((d: Date) => d.toISOString()),
    }));

    const result: any = {
      totalDownloads,
      downloads,
    };

    if (paginated !== 'false' && paginated) {
      result.pageNumber = pageNumber;
      result.pageSize = pageSize;
      result.totalPages = totalPages;
    }

    return result;
  }



}




export default DownloadReportService;