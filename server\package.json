{"name": "pentabell-backend", "version": "2.0.0", "description": "pentabell-backend", "main": "./dist/server.js", "scripts": {"start": "tsc && cross-env NODE_ENV=dev node dist/server.js", "start:test": "tsc && cross-env NODE_ENV=test node dist/server.js", "start:prod": "tsc && cross-env NODE_ENV=prod node dist/server.js", "dev": "tsc-watch --onSuccess \"cross-env NODE_ENV=dev nodemon ./dist/server.js\"", "build": "tsc && cpx \"./src/utils/services/email-service/**/*\" ./dist/utils/services/email-service/ --clean && cpx \"./src/public/**/*\" ./dist/public/ --clean", "lint": "eslint . --ext .ts --fix", "pm2": "tsc && cross-env NODE_ENV=prod pm2 start pm2.config.js"}, "keywords": ["pentabell", "cv", "jobs"], "author": "", "license": "ISC", "devDependencies": {"@types/cookie-parser": "^1.4.3", "@types/cors": "^2.8.13", "@types/dotenv": "^8.2.0", "@types/express": "^4.17.17", "@types/express-session": "^1.18.0", "@types/franc": "^5.0.3", "@types/mime-types": "^2.1.4", "@types/node": "^18.19.17", "@types/node-cron": "^3.0.11", "@types/pako": "^2.0.3", "@types/passport": "^1.0.16", "@types/passport-google-oauth2": "^0.1.8", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-linkedin-oauth2": "^1.5.6", "@types/passport-microsoft": "^1.0.3", "@types/supertest": "^2.0.16", "@types/swagger-ui-express": "^4.1.3", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.13", "@types/yamljs": "^0.2.34", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "cpx": "^1.5.0", "cross-env": "^7.0.3", "eslint": "^8.38.0", "ts-node": "^10.9.1", "tsc-watch": "^6.0.0", "typescript": "^5.0.4"}, "dependencies": {"@handlebars/allow-prototype-access": "^1.0.5", "@jest/types": "^29.6.3", "@nestjs/common": "^10.4.1", "@pm2/io": "^6.0.1", "@types/bcrypt": "^5.0.0", "@types/bcryptjs": "^2.4.6", "@types/body-parser": "^1.19.2", "@types/jsonwebtoken": "^9.0.1", "@types/multer": "^1.4.7", "@types/nodemailer": "^6.4.7", "@types/nodemailer-express-handlebars": "^4.0.2", "@types/pdf-parse": "^1.1.4", "@types/request-ip": "^0.0.38", "@types/socket.io": "^3.0.2", "@types/word-extractor": "^1.0.5", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "bson": "^6.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csv-writer": "^1.6.0", "date-fns": "^3.3.1", "dotenv": "^16.0.3", "envalid": "^7.3.1", "express": "^4.18.2", "express-handlebars": "^7.1.2", "express-session": "^1.18.0", "form-data": "^4.0.1", "franc": "^5.0.0", "fs": "^0.0.1-security", "handlebars": "^4.7.8", "is-disposable-email": "^0.0.1", "joi": "^17.12.2", "jsonwebtoken": "^9.0.0", "mailchecker": "^6.0.4", "mammoth": "^1.7.1", "module-alias": "^2.2.2", "mongodb": "^6.3.0", "mongoose": "^8.2.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "node-temp-mail": "^2.0.2", "nodemailer": "^6.10.1", "nodemailer-express-handlebars": "^6.1.0", "nodemon": "^3.1.0", "pako": "^2.1.0", "passport": "^0.7.0", "passport-google-oauth2": "^0.2.0", "passport-google-oauth20": "^2.0.0", "passport-linkedin": "^1.0.0", "passport-linkedin-oauth2": "^2.0.0", "passport-microsoft": "^2.1.0", "pdf-parse": "^1.1.1", "pmx": "^1.6.7", "reflect-metadata": "^0.2.2", "request-ip": "^3.3.0", "sharp": "^0.33.5", "sib-api-v3-typescript": "^2.2.2", "socket.io": "^4.7.5", "swagger-ui-express": "^4.6.3", "temp-mail-detector": "^0.1.3", "transporter": "^0.0.1", "types": "^0.1.1", "validator": "^13.12.0", "winston": "^3.8.2", "word-extractor": "^1.0.4", "ws": "^8.18.0", "yamljs": "^0.3.0"}, "_moduleAliases": {"@/apis": "dist/apis", "@/utils": "dist/utils", "@/middlewares": "dist/middlewares"}}