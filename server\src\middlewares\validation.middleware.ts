import {
    authenticatedCommentSchema,
    authenticatedResponseSchema,
    unauthenticatedCommentSchema,
    unauthenticatedResponseSchema,
} from '@/apis/article/commentaire/commentaire.validations';
import HttpException from '@/utils/exceptions/http.exception';
import { Request, Response, NextFunction, RequestHandler } from 'express';
import Jo<PERSON> from 'joi';

export function validationMiddleware(schema: Joi.Schema): RequestHandler {
    return async (request: any, response: Response, next: NextFunction): Promise<void> => {
        const validationOptions = {
            abortEarly: false,
            allowUnknown: true,
            stripUnknown: true,
        };

        try {
            const value = await schema.validateAsync(request.body, validationOptions);
            request.body = value;
            next();
        } catch (error: any) {
            const errors: string[] = [];
            error.details.forEach((error: Joi.ValidationErrorItem) => {
                errors.push(error.message);
            });
            response.status(500).send(errors);
        }
    };
}

export function validationWithFileMiddleware(schema: Joi.Schema): RequestHandler {
    return async (request: Request, response: Response, next: NextFunction): Promise<void> => {
        const validationOptions = {
            abortEarly: false,
            allowUnknown: true,
            stripUnknown: true,
        };
        const candidatObjectFromUpload = request.file?.buffer.toString();

        if (candidatObjectFromUpload === undefined) {
            response.status(400).send({ error: 'Upload file field is empty' });
            return;
        }

        try {
            const value = await schema.validateAsync(JSON.parse(candidatObjectFromUpload), validationOptions);
            request.body = value;
            next();
        } catch (e: any) {
            const errors: string[] = [];
            e.details.forEach((error: Joi.ValidationErrorItem) => {
                errors.push(error.message);
            });
            response.status(500).send(errors);
        }
    };
}

export const validateUUID = (request: Request, response: Response, next: NextFunction) => {
    const regexExp = /^[0-9a-f]{32}$/gi;

    const { filename } = request.params;

    const uuid = filename.split('.')[0];

    if (!regexExp.test(uuid)) {
        return next(new HttpException(400, 'Invalid UUID format'));
    }

    next();
};

export const validateParams = (request: Request, response: Response, next: NextFunction) => {
    const { resource, folder } = request.params;

    const year = parseInt(folder, 10);

    if (isNaN(year) || year.toString() !== folder) {
        return next(new HttpException(404, 'Invalid folder format'));
    }

    const currentYear = new Date().getFullYear();
    if (year <= 2005 || year > currentYear) {
        return next(new HttpException(404, 'Invalid folder format'));
    }

    if (
        !resource ||
        (resource !== 'users' && resource !== 'candidates' && resource !== 'clients' && resource !== 'blogs' && resource !== 'categories' && resource !== 'sliders' && resource !== 'events')
    ) {
        return next(new HttpException(404, 'Invalid resource'));
    }

    next();
};

export const checkCurrentUserForComment = async (request: any, response: Response, next: NextFunction) => {
    request.user ? validationMiddleware(authenticatedCommentSchema) : validationMiddleware(unauthenticatedCommentSchema);
    next();
};

export const checkCurrentUserForResponse = async (request: any, response: Response, next: NextFunction) => {
    request.user ? validationMiddleware(authenticatedResponseSchema) : validationMiddleware(unauthenticatedResponseSchema);
    next();
};
