"use strict";exports.id=1619,exports.ids=[1619],exports.modules={23743:(e,t,s)=>{s.d(t,{Z:()=>o}),s(17577);var i=s(41659),r=s(41222),n=s(14750);function o(){let e=(0,i.Z)(r.Z);return e[n.Z]||e}},88441:(e,t,s)=>{s.d(t,{Z:()=>h});var i=s(17577),r=s(63212),n=s(38489),o=s(83342);let a={...i}.useSyncExternalStore;function l(e={}){let{themeId:t}=e;return function(e,s={}){let l=(0,o.Z)();l&&t&&(l=l[t]||l);let{defaultMatches:h=!1,matchMedia:u=null,ssrMatchMedia:p=null,noSsr:g=!1}=(0,n.Z)({name:"MuiUseMediaQuery",props:s,theme:l}),d="function"==typeof e?e(l):e;return(void 0!==a?function(e,t,s,r,n){let o=i.useCallback(()=>t,[t]),l=i.useMemo(()=>{if(n&&s)return()=>s(e).matches;if(null!==r){let{matches:t}=r(e);return()=>t}return o},[o,e,r,n,s]),[h,u]=i.useMemo(()=>{if(null===s)return[o,()=>()=>{}];let t=s(e);return[()=>t.matches,e=>(t.addEventListener("change",e),()=>{t.removeEventListener("change",e)})]},[o,s,e]);return a(u,h,l)}:function(e,t,s,n,o){let[a,l]=i.useState(()=>o&&s?s(e).matches:n?n(e).matches:t);return(0,r.Z)(()=>{if(!s)return;let t=s(e),i=()=>{l(t.matches)};return i(),t.addEventListener("change",i),()=>{t.removeEventListener("change",i)}},[e,s]),a})(d=d.replace(/^@media( ?)/m,""),h,u,p,g)}}l();let h=l({themeId:s(14750).Z})},63212:(e,t,s)=>{s.d(t,{Z:()=>i});let i=s(17577).useEffect},83803:(e,t,s)=>{s.d(t,{Z:()=>i});var i=function(e){return{type:"backend",init:function(e,t,s){},read:function(t,s,i){if("function"==typeof e){if(e.length<3){try{var r=e(t,s);r&&"function"==typeof r.then?r.then(function(e){return i(null,e&&e.default||e)}).catch(i):i(null,r)}catch(e){i(e)}return}e(t,s,i);return}i(null,e&&e[t]&&e[t][s])}}}},18177:(e,t,s)=>{s.d(t,{Fs:()=>es,cp:()=>ei});let i=e=>"string"==typeof e,r=()=>{let e,t;let s=new Promise((s,i)=>{e=s,t=i});return s.resolve=e,s.reject=t,s},n=e=>null==e?"":""+e,o=(e,t,s)=>{e.forEach(e=>{t[e]&&(s[e]=t[e])})},a=/###/g,l=e=>e&&e.indexOf("###")>-1?e.replace(a,"."):e,h=e=>!e||i(e),u=(e,t,s)=>{let r=i(t)?t.split("."):t,n=0;for(;n<r.length-1;){if(h(e))return{};let t=l(r[n]);!e[t]&&s&&(e[t]=new s),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++n}return h(e)?{}:{obj:e,k:l(r[n])}},p=(e,t,s)=>{let{obj:i,k:r}=u(e,t,Object);if(void 0!==i||1===t.length){i[r]=s;return}let n=t[t.length-1],o=t.slice(0,t.length-1),a=u(e,o,Object);for(;void 0===a.obj&&o.length;)n=`${o[o.length-1]}.${n}`,(a=u(e,o=o.slice(0,o.length-1),Object))&&a.obj&&void 0!==a.obj[`${a.k}.${n}`]&&(a.obj=void 0);a.obj[`${a.k}.${n}`]=s},g=(e,t,s,i)=>{let{obj:r,k:n}=u(e,t,Object);r[n]=r[n]||[],r[n].push(s)},d=(e,t)=>{let{obj:s,k:i}=u(e,t);if(s)return s[i]},c=(e,t,s)=>{let i=d(e,s);return void 0!==i?i:d(t,s)},f=(e,t,s)=>{for(let r in t)"__proto__"!==r&&"constructor"!==r&&(r in e?i(e[r])||e[r]instanceof String||i(t[r])||t[r]instanceof String?s&&(e[r]=t[r]):f(e[r],t[r],s):e[r]=t[r]);return e},m=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var y={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};let v=e=>i(e)?e.replace(/[&<>"'\/]/g,e=>y[e]):e;class b{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){let t=this.regExpMap.get(e);if(void 0!==t)return t;let s=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,s),this.regExpQueue.push(e),s}}let x=[" ",",","?","!",";"],S=new b(20),k=(e,t,s)=>{t=t||"",s=s||"";let i=x.filter(e=>0>t.indexOf(e)&&0>s.indexOf(e));if(0===i.length)return!0;let r=S.getRegExp(`(${i.map(e=>"?"===e?"\\?":e).join("|")})`),n=!r.test(e);if(!n){let t=e.indexOf(s);t>0&&!r.test(e.substring(0,t))&&(n=!0)}return n},L=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t])return e[t];let i=t.split(s),r=e;for(let e=0;e<i.length;){let t;if(!r||"object"!=typeof r)return;let n="";for(let o=e;o<i.length;++o)if(o!==e&&(n+=s),n+=i[o],void 0!==(t=r[n])){if(["string","number","boolean"].indexOf(typeof t)>-1&&o<i.length-1)continue;e+=o-e+1;break}r=t}return r},O=e=>e&&e.replace("_","-"),N={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console&&console[e]&&console[e].apply(console,t)}};class w{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||N,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,s,r){return r&&!this.debug?null:(i(e[0])&&(e[0]=`${s}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new w(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new w(this.logger,e)}}var C=new w;class R{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);let s=this.observers[e].get(t)||0;this.observers[e].set(t,s+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,s=Array(t>1?t-1:0),i=1;i<t;i++)s[i-1]=arguments[i];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(e=>{let[t,i]=e;for(let e=0;e<i;e++)t(...s)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(t=>{let[i,r]=t;for(let t=0;t<r;t++)i.apply(i,[e,...s])})}}class $ extends R{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}removeNamespaces(e){let t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,s){let r,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=void 0!==n.keySeparator?n.keySeparator:this.options.keySeparator,a=void 0!==n.ignoreJSONStructure?n.ignoreJSONStructure:this.options.ignoreJSONStructure;e.indexOf(".")>-1?r=e.split("."):(r=[e,t],s&&(Array.isArray(s)?r.push(...s):i(s)&&o?r.push(...s.split(o)):r.push(s)));let l=d(this.data,r);return(!l&&!t&&!s&&e.indexOf(".")>-1&&(e=r[0],t=r[1],s=r.slice(2).join(".")),!l&&a&&i(s))?L(this.data&&this.data[e]&&this.data[e][t],s,o):l}addResource(e,t,s,i){let r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1},n=void 0!==r.keySeparator?r.keySeparator:this.options.keySeparator,o=[e,t];s&&(o=o.concat(n?s.split(n):s)),e.indexOf(".")>-1&&(o=e.split("."),i=t,t=o[1]),this.addNamespaces(t),p(this.data,o,i),r.silent||this.emit("added",e,t,s,i)}addResources(e,t,s){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(let r in s)(i(s[r])||Array.isArray(s[r]))&&this.addResource(e,t,r,s[r],{silent:!0});r.silent||this.emit("added",e,t,s)}addResourceBundle(e,t,s,i,r){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},o=[e,t];e.indexOf(".")>-1&&(o=e.split("."),i=s,s=t,t=o[1]),this.addNamespaces(t);let a=d(this.data,o)||{};n.skipCopy||(s=JSON.parse(JSON.stringify(s))),i?f(a,s,r):a={...a,...s},p(this.data,o,a),n.silent||this.emit("added",e,t,s)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return(t||(t=this.options.defaultNS),"v1"===this.options.compatibilityAPI)?{...this.getResource(e,t)}:this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){let t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(e=>t[e]&&Object.keys(t[e]).length>0)}toJSON(){return this.data}}var P={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,s,i,r){return e.forEach(e=>{this.processors[e]&&(t=this.processors[e].process(t,s,i,r))}),t}};let j={};class E extends R{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),o(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=C.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(null==e)return!1;let s=this.resolve(e,t);return s&&void 0!==s.res}extractFromKey(e,t){let s=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===s&&(s=":");let r=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,n=t.ns||this.options.defaultNS||[],o=s&&e.indexOf(s)>-1,a=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!k(e,s,r);if(o&&!a){let t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:i(n)?[n]:n};let o=e.split(s);(s!==r||s===r&&this.options.ns.indexOf(o[0])>-1)&&(n=o.shift()),e=o.join(r)}return{key:e,namespaces:i(n)?[n]:n}}translate(e,t,s){if("object"!=typeof t&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof t&&(t={...t}),t||(t={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);let r=void 0!==t.returnDetails?t.returnDetails:this.options.returnDetails,n=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,{key:o,namespaces:a}=this.extractFromKey(e[e.length-1],t),l=a[a.length-1],h=t.lng||this.language,u=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(h&&"cimode"===h.toLowerCase()){if(u){let e=t.nsSeparator||this.options.nsSeparator;return r?{res:`${l}${e}${o}`,usedKey:o,exactUsedKey:o,usedLng:h,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:`${l}${e}${o}`}return r?{res:o,usedKey:o,exactUsedKey:o,usedLng:h,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:o}let p=this.resolve(e,t),g=p&&p.res,d=p&&p.usedKey||o,c=p&&p.exactUsedKey||o,f=Object.prototype.toString.apply(g),m=void 0!==t.joinArrays?t.joinArrays:this.options.joinArrays,y=!this.i18nFormat||this.i18nFormat.handleAsObject,v=!i(g)&&"boolean"!=typeof g&&"number"!=typeof g;if(y&&g&&v&&0>["[object Number]","[object Function]","[object RegExp]"].indexOf(f)&&!(i(m)&&Array.isArray(g))){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(d,g,{...t,ns:a}):`key '${o} (${this.language})' returned an object instead of string.`;return r?(p.res=e,p.usedParams=this.getUsedParamsDetails(t),p):e}if(n){let e=Array.isArray(g),s=e?[]:{},i=e?c:d;for(let e in g)if(Object.prototype.hasOwnProperty.call(g,e)){let r=`${i}${n}${e}`;s[e]=this.translate(r,{...t,joinArrays:!1,ns:a}),s[e]===r&&(s[e]=g[e])}g=s}}else if(y&&i(m)&&Array.isArray(g))(g=g.join(m))&&(g=this.extendTranslation(g,e,t,s));else{let r=!1,a=!1,u=void 0!==t.count&&!i(t.count),d=E.hasDefaultValue(t),c=u?this.pluralResolver.getSuffix(h,t.count,t):"",f=t.ordinal&&u?this.pluralResolver.getSuffix(h,t.count,{ordinal:!1}):"",m=u&&!t.ordinal&&0===t.count&&this.pluralResolver.shouldUseIntlApi(),y=m&&t[`defaultValue${this.options.pluralSeparator}zero`]||t[`defaultValue${c}`]||t[`defaultValue${f}`]||t.defaultValue;!this.isValidLookup(g)&&d&&(r=!0,g=y),this.isValidLookup(g)||(a=!0,g=o);let v=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&a?void 0:g,b=d&&y!==g&&this.options.updateMissing;if(a||r||b){if(this.logger.log(b?"updateKey":"missingKey",h,l,o,b?y:g),n){let e=this.resolve(o,{...t,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[],s=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if("fallback"===this.options.saveMissingTo&&s&&s[0])for(let t=0;t<s.length;t++)e.push(s[t]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(t.lng||this.language):e.push(t.lng||this.language);let i=(e,s,i)=>{let r=d&&i!==g?i:v;this.options.missingKeyHandler?this.options.missingKeyHandler(e,l,s,r,b,t):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(e,l,s,r,b,t),this.emit("missingKey",e,l,s,g)};this.options.saveMissing&&(this.options.saveMissingPlurals&&u?e.forEach(e=>{let s=this.pluralResolver.getSuffixes(e,t);m&&t[`defaultValue${this.options.pluralSeparator}zero`]&&0>s.indexOf(`${this.options.pluralSeparator}zero`)&&s.push(`${this.options.pluralSeparator}zero`),s.forEach(s=>{i([e],o+s,t[`defaultValue${s}`]||y)})}):i(e,o,y))}g=this.extendTranslation(g,e,t,p,s),a&&g===o&&this.options.appendNamespaceToMissingKey&&(g=`${l}:${o}`),(a||r)&&this.options.parseMissingKeyHandler&&(g="v1"!==this.options.compatibilityAPI?this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}:${o}`:o,r?g:void 0):this.options.parseMissingKeyHandler(g))}return r?(p.res=g,p.usedParams=this.getUsedParamsDetails(t),p):g}extendTranslation(e,t,s,r,n){var o=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...s},s.lng||this.language||r.usedLng,r.usedNS,r.usedKey,{resolved:r});else if(!s.skipInterpolation){let a;s.interpolation&&this.interpolator.init({...s,interpolation:{...this.options.interpolation,...s.interpolation}});let l=i(e)&&(s&&s.interpolation&&void 0!==s.interpolation.skipOnVariables?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);if(l){let t=e.match(this.interpolator.nestingRegexp);a=t&&t.length}let h=s.replace&&!i(s.replace)?s.replace:s;if(this.options.interpolation.defaultVariables&&(h={...this.options.interpolation.defaultVariables,...h}),e=this.interpolator.interpolate(e,h,s.lng||this.language||r.usedLng,s),l){let t=e.match(this.interpolator.nestingRegexp);a<(t&&t.length)&&(s.nest=!1)}!s.lng&&"v1"!==this.options.compatibilityAPI&&r&&r.res&&(s.lng=this.language||r.usedLng),!1!==s.nest&&(e=this.interpolator.nest(e,function(){for(var e=arguments.length,i=Array(e),r=0;r<e;r++)i[r]=arguments[r];return n&&n[0]===i[0]&&!s.context?(o.logger.warn(`It seems you are nesting recursively key: ${i[0]} in key: ${t[0]}`),null):o.translate(...i,t)},s)),s.interpolation&&this.interpolator.reset()}let a=s.postProcess||this.options.postProcess,l=i(a)?[a]:a;return null!=e&&l&&l.length&&!1!==s.applyPostProcessor&&(e=P.handle(l,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...r,usedParams:this.getUsedParamsDetails(s)},...s}:s,this)),e}resolve(e){let t,s,r,n,o,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i(e)&&(e=[e]),e.forEach(e=>{if(this.isValidLookup(t))return;let l=this.extractFromKey(e,a),h=l.key;s=h;let u=l.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));let p=void 0!==a.count&&!i(a.count),g=p&&!a.ordinal&&0===a.count&&this.pluralResolver.shouldUseIntlApi(),d=void 0!==a.context&&(i(a.context)||"number"==typeof a.context)&&""!==a.context,c=a.lngs?a.lngs:this.languageUtils.toResolveHierarchy(a.lng||this.language,a.fallbackLng);u.forEach(e=>{this.isValidLookup(t)||(o=e,!j[`${c[0]}-${e}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(o)&&(j[`${c[0]}-${e}`]=!0,this.logger.warn(`key "${s}" for languages "${c.join(", ")}" won't get resolved as namespace "${o}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),c.forEach(s=>{let i;if(this.isValidLookup(t))return;n=s;let o=[h];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(o,h,s,e,a);else{let e;p&&(e=this.pluralResolver.getSuffix(s,a.count,a));let t=`${this.options.pluralSeparator}zero`,i=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(p&&(o.push(h+e),a.ordinal&&0===e.indexOf(i)&&o.push(h+e.replace(i,this.options.pluralSeparator)),g&&o.push(h+t)),d){let s=`${h}${this.options.contextSeparator}${a.context}`;o.push(s),p&&(o.push(s+e),a.ordinal&&0===e.indexOf(i)&&o.push(s+e.replace(i,this.options.pluralSeparator)),g&&o.push(s+t))}}for(;i=o.pop();)this.isValidLookup(t)||(r=i,t=this.getResource(s,e,i,a))}))})}),{res:t,usedKey:s,exactUsedKey:r,usedLng:n,usedNS:o}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,s,i):this.resourceStore.getResource(e,t,s,i)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.replace&&!i(e.replace),s=t?e.replace:e;if(t&&void 0!==e.count&&(s.count=e.count),this.options.interpolation.defaultVariables&&(s={...this.options.interpolation.defaultVariables,...s}),!t)for(let e of(s={...s},["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"]))delete s[e];return s}static hasDefaultValue(e){let t="defaultValue";for(let s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&t===s.substring(0,t.length)&&void 0!==e[s])return!0;return!1}}let I=e=>e.charAt(0).toUpperCase()+e.slice(1);class F{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=C.create("languageUtils")}getScriptPartFromCode(e){if(!(e=O(e))||0>e.indexOf("-"))return null;let t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase())?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(!(e=O(e))||0>e.indexOf("-"))return e;let t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(i(e)&&e.indexOf("-")>-1){if("undefined"!=typeof Intl&&void 0!==Intl.getCanonicalLocales)try{let t=Intl.getCanonicalLocales(e)[0];if(t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t)return t}catch(e){}let t=["hans","hant","latn","cyrl","cans","mong","arab"],s=e.split("-");return this.options.lowerCaseLng?s=s.map(e=>e.toLowerCase()):2===s.length?(s[0]=s[0].toLowerCase(),s[1]=s[1].toUpperCase(),t.indexOf(s[1].toLowerCase())>-1&&(s[1]=I(s[1].toLowerCase()))):3===s.length&&(s[0]=s[0].toLowerCase(),2===s[1].length&&(s[1]=s[1].toUpperCase()),"sgn"!==s[0]&&2===s[2].length&&(s[2]=s[2].toUpperCase()),t.indexOf(s[1].toLowerCase())>-1&&(s[1]=I(s[1].toLowerCase())),t.indexOf(s[2].toLowerCase())>-1&&(s[2]=I(s[2].toLowerCase()))),s.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){let t;return e?(e.forEach(e=>{if(t)return;let s=this.formatLanguageCode(e);(!this.options.supportedLngs||this.isSupportedCode(s))&&(t=s)}),!t&&this.options.supportedLngs&&e.forEach(e=>{if(t)return;let s=this.getLanguagePartFromCode(e);if(this.isSupportedCode(s))return t=s;t=this.options.supportedLngs.find(e=>{if(e===s||!(0>e.indexOf("-")&&0>s.indexOf("-"))&&(e.indexOf("-")>0&&0>s.indexOf("-")&&e.substring(0,e.indexOf("-"))===s||0===e.indexOf(s)&&s.length>1))return e})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t):null}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),i(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let s=e[t];return s||(s=e[this.getScriptPartFromCode(t)]),s||(s=e[this.formatLanguageCode(t)]),s||(s=e[this.getLanguagePartFromCode(t)]),s||(s=e.default),s||[]}toResolveHierarchy(e,t){let s=this.getFallbackCodes(t||this.options.fallbackLng||[],e),r=[],n=e=>{e&&(this.isSupportedCode(e)?r.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return i(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&n(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&n(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&n(this.getLanguagePartFromCode(e))):i(e)&&n(this.formatLanguageCode(e)),s.forEach(e=>{0>r.indexOf(e)&&n(this.formatLanguageCode(e))}),r}}let A=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],D={1:e=>Number(e>1),2:e=>Number(1!=e),3:e=>0,4:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),5:e=>Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5),6:e=>Number(1==e?0:e>=2&&e<=4?1:2),7:e=>Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),8:e=>Number(1==e?0:2==e?1:8!=e&&11!=e?2:3),9:e=>Number(e>=2),10:e=>Number(1==e?0:2==e?1:e<7?2:e<11?3:4),11:e=>Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3),12:e=>Number(e%10!=1||e%100==11),13:e=>Number(0!==e),14:e=>Number(1==e?0:2==e?1:3==e?2:3),15:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2),16:e=>Number(e%10==1&&e%100!=11?0:0!==e?1:2),17:e=>Number(1==e||e%10==1&&e%100!=11?0:1),18:e=>Number(0==e?0:1==e?1:2),19:e=>Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3),20:e=>Number(1==e?0:0==e||e%100>0&&e%100<20?1:2),21:e=>Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0),22:e=>Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)},V=["v1","v2","v3"],U=["v4"],T={zero:0,one:1,two:2,few:3,many:4,other:5},M=()=>{let e={};return A.forEach(t=>{t.lngs.forEach(s=>{e[s]={numbers:t.nr,plurals:D[t.fc]}})}),e};class K{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=C.create("pluralResolver"),(!this.options.compatibilityJSON||U.includes(this.options.compatibilityJSON))&&("undefined"==typeof Intl||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=M(),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.shouldUseIntlApi()){let s;let i=O("dev"===e?"en":e),r=t.ordinal?"ordinal":"cardinal",n=JSON.stringify({cleanedCode:i,type:r});if(n in this.pluralRulesCache)return this.pluralRulesCache[n];try{s=new Intl.PluralRules(i,{type:r})}catch(r){if(!e.match(/-|_/))return;let i=this.languageUtils.getLanguagePartFromCode(e);s=this.getRule(i,t)}return this.pluralRulesCache[n]=s,s}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=this.getRule(e,t);return this.shouldUseIntlApi()?s&&s.resolvedOptions().pluralCategories.length>1:s&&s.numbers.length>1}getPluralFormsOfKey(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,s).map(e=>`${t}${e}`)}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=this.getRule(e,t);return s?this.shouldUseIntlApi()?s.resolvedOptions().pluralCategories.sort((e,t)=>T[e]-T[t]).map(e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`):s.numbers.map(s=>this.getSuffix(e,s,t)):[]}getSuffix(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=this.getRule(e,s);return i?this.shouldUseIntlApi()?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${i.select(t)}`:this.getSuffixRetroCompatible(i,t):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,t){let s=e.noAbs?e.plurals(t):e.plurals(Math.abs(t)),i=e.numbers[s];this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]&&(2===i?i="plural":1===i&&(i=""));let r=()=>this.options.prepend&&i.toString()?this.options.prepend+i.toString():i.toString();return"v1"===this.options.compatibilityJSON?1===i?"":"number"==typeof i?`_plural_${i.toString()}`:r():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]?r():this.options.prepend&&s.toString()?this.options.prepend+s.toString():s.toString()}shouldUseIntlApi(){return!V.includes(this.options.compatibilityJSON)}}let H=function(e,t,s){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",n=!(arguments.length>4)||void 0===arguments[4]||arguments[4],o=c(e,t,s);return!o&&n&&i(s)&&void 0===(o=L(e,s,r))&&(o=L(t,s,r)),o},z=e=>e.replace(/\$/g,"$$$$");class J{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=C.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});let{escape:t,escapeValue:s,useRawValueToEscape:i,prefix:r,prefixEscaped:n,suffix:o,suffixEscaped:a,formatSeparator:l,unescapeSuffix:h,unescapePrefix:u,nestingPrefix:p,nestingPrefixEscaped:g,nestingSuffix:d,nestingSuffixEscaped:c,nestingOptionsSeparator:f,maxReplaces:y,alwaysFormat:b}=e.interpolation;this.escape=void 0!==t?t:v,this.escapeValue=void 0===s||s,this.useRawValueToEscape=void 0!==i&&i,this.prefix=r?m(r):n||"{{",this.suffix=o?m(o):a||"}}",this.formatSeparator=l||",",this.unescapePrefix=h?"":u||"-",this.unescapeSuffix=this.unescapePrefix?"":h||"",this.nestingPrefix=p?m(p):g||m("$t("),this.nestingSuffix=d?m(d):c||m(")"),this.nestingOptionsSeparator=f||",",this.maxReplaces=y||1e3,this.alwaysFormat=void 0!==b&&b,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let e=(e,t)=>e&&e.source===t?(e.lastIndex=0,e):RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,s,r){let o,a,l;let h=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=e=>{if(0>e.indexOf(this.formatSeparator)){let i=H(t,h,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(i,void 0,s,{...r,...t,interpolationkey:e}):i}let i=e.split(this.formatSeparator),n=i.shift().trim(),o=i.join(this.formatSeparator).trim();return this.format(H(t,h,n,this.options.keySeparator,this.options.ignoreJSONStructure),o,s,{...r,...t,interpolationkey:n})};this.resetRegExp();let p=r&&r.missingInterpolationHandler||this.options.missingInterpolationHandler,g=r&&r.interpolation&&void 0!==r.interpolation.skipOnVariables?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>z(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?z(this.escape(e)):z(e)}].forEach(t=>{for(l=0;o=t.regex.exec(e);){let s=o[1].trim();if(void 0===(a=u(s))){if("function"==typeof p){let t=p(e,o,r);a=i(t)?t:""}else if(r&&Object.prototype.hasOwnProperty.call(r,s))a="";else if(g){a=o[0];continue}else this.logger.warn(`missed to pass in variable ${s} for interpolating ${e}`),a=""}else i(a)||this.useRawValueToEscape||(a=n(a));let h=t.safeValue(a);if(e=e.replace(o[0],h),g?(t.regex.lastIndex+=a.length,t.regex.lastIndex-=o[0].length):t.regex.lastIndex=0,++l>=this.maxReplaces)break}}),e}nest(e,t){let s,r,o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=(e,t)=>{let s=this.nestingOptionsSeparator;if(0>e.indexOf(s))return e;let i=e.split(RegExp(`${s}[ ]*{`)),r=`{${i[1]}`;e=i[0];let n=(r=this.interpolate(r,o)).match(/'/g),a=r.match(/"/g);(n&&n.length%2==0&&!a||a.length%2!=0)&&(r=r.replace(/'/g,'"'));try{o=JSON.parse(r),t&&(o={...t,...o})}catch(t){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,t),`${e}${s}${r}`}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,e};for(;s=this.nestingRegexp.exec(e);){let h=[];(o=(o={...a}).replace&&!i(o.replace)?o.replace:o).applyPostProcessor=!1,delete o.defaultValue;let u=!1;if(-1!==s[0].indexOf(this.formatSeparator)&&!/{.*}/.test(s[1])){let e=s[1].split(this.formatSeparator).map(e=>e.trim());s[1]=e.shift(),h=e,u=!0}if((r=t(l.call(this,s[1].trim(),o),o))&&s[0]===e&&!i(r))return r;i(r)||(r=n(r)),r||(this.logger.warn(`missed to resolve ${s[1]} for nesting ${e}`),r=""),u&&(r=h.reduce((e,t)=>this.format(e,t,a.lng,{...a,interpolationkey:s[1].trim()}),r.trim())),e=e.replace(s[0],r),this.regexp.lastIndex=0}return e}}let B=e=>{let t=e.toLowerCase().trim(),s={};if(e.indexOf("(")>-1){let i=e.split("(");t=i[0].toLowerCase().trim();let r=i[1].substring(0,i[1].length-1);"currency"===t&&0>r.indexOf(":")?s.currency||(s.currency=r.trim()):"relativetime"===t&&0>r.indexOf(":")?s.range||(s.range=r.trim()):r.split(";").forEach(e=>{if(e){let[t,...i]=e.split(":"),r=i.join(":").trim().replace(/^'+|'+$/g,""),n=t.trim();s[n]||(s[n]=r),"false"===r&&(s[n]=!1),"true"===r&&(s[n]=!0),isNaN(r)||(s[n]=parseInt(r,10))}})}return{formatName:t,formatOptions:s}},q=e=>{let t={};return(s,i,r)=>{let n=r;r&&r.interpolationkey&&r.formatParams&&r.formatParams[r.interpolationkey]&&r[r.interpolationkey]&&(n={...n,[r.interpolationkey]:void 0});let o=i+JSON.stringify(n),a=t[o];return a||(a=e(O(i),r),t[o]=a),a(s)}};class Z{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=C.create("formatter"),this.options=e,this.formats={number:q((e,t)=>{let s=new Intl.NumberFormat(e,{...t});return e=>s.format(e)}),currency:q((e,t)=>{let s=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>s.format(e)}),datetime:q((e,t)=>{let s=new Intl.DateTimeFormat(e,{...t});return e=>s.format(e)}),relativetime:q((e,t)=>{let s=new Intl.RelativeTimeFormat(e,{...t});return e=>s.format(e,t.range||"day")}),list:q((e,t)=>{let s=new Intl.ListFormat(e,{...t});return e=>s.format(e)})},this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=q(t)}format(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=t.split(this.formatSeparator);if(r.length>1&&r[0].indexOf("(")>1&&0>r[0].indexOf(")")&&r.find(e=>e.indexOf(")")>-1)){let e=r.findIndex(e=>e.indexOf(")")>-1);r[0]=[r[0],...r.splice(1,e)].join(this.formatSeparator)}return r.reduce((e,t)=>{let{formatName:r,formatOptions:n}=B(t);if(this.formats[r]){let t=e;try{let o=i&&i.formatParams&&i.formatParams[i.interpolationkey]||{},a=o.locale||o.lng||i.locale||i.lng||s;t=this.formats[r](e,a,{...n,...i,...o})}catch(e){this.logger.warn(e)}return t}return this.logger.warn(`there was no format function for ${r}`),e},e)}}let _=(e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)};class W extends R{constructor(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=s,this.languageUtils=s.languageUtils,this.options=i,this.logger=C.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=i.maxParallelReads||10,this.readingCalls=0,this.maxRetries=i.maxRetries>=0?i.maxRetries:5,this.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(s,i.backend,i)}queueLoad(e,t,s,i){let r={},n={},o={},a={};return e.forEach(e=>{let i=!0;t.forEach(t=>{let o=`${e}|${t}`;!s.reload&&this.store.hasResourceBundle(e,t)?this.state[o]=2:this.state[o]<0||(1===this.state[o]?void 0===n[o]&&(n[o]=!0):(this.state[o]=1,i=!1,void 0===n[o]&&(n[o]=!0),void 0===r[o]&&(r[o]=!0),void 0===a[t]&&(a[t]=!0)))}),i||(o[e]=!0)}),(Object.keys(r).length||Object.keys(n).length)&&this.queue.push({pending:n,pendingCount:Object.keys(n).length,loaded:{},errors:[],callback:i}),{toLoad:Object.keys(r),pending:Object.keys(n),toLoadLanguages:Object.keys(o),toLoadNamespaces:Object.keys(a)}}loaded(e,t,s){let i=e.split("|"),r=i[0],n=i[1];t&&this.emit("failedLoading",r,n,t),!t&&s&&this.store.addResourceBundle(r,n,s,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&s&&(this.state[e]=0);let o={};this.queue.forEach(s=>{g(s.loaded,[r],n),_(s,e),t&&s.errors.push(t),0!==s.pendingCount||s.done||(Object.keys(s.loaded).forEach(e=>{o[e]||(o[e]={});let t=s.loaded[e];t.length&&t.forEach(t=>{void 0===o[e][t]&&(o[e][t]=!0)})}),s.done=!0,s.errors.length?s.callback(s.errors):s.callback())}),this.emit("loaded",o),this.queue=this.queue.filter(e=>!e.done)}read(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,n=arguments.length>5?arguments[5]:void 0;if(!e.length)return n(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:s,tried:i,wait:r,callback:n});return}this.readingCalls++;let o=(o,a)=>{if(this.readingCalls--,this.waitingReads.length>0){let e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}if(o&&a&&i<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,s,i+1,2*r,n)},r);return}n(o,a)},a=this.backend[s].bind(this.backend);if(2===a.length){try{let s=a(e,t);s&&"function"==typeof s.then?s.then(e=>o(null,e)).catch(o):o(null,s)}catch(e){o(e)}return}return a(e,t,o)}prepareLoading(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),r&&r();i(e)&&(e=this.languageUtils.toResolveHierarchy(e)),i(t)&&(t=[t]);let n=this.queueLoad(e,t,s,r);if(!n.toLoad.length)return n.pending.length||r(),null;n.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,s){this.prepareLoading(e,t,{},s)}reload(e,t,s){this.prepareLoading(e,t,{reload:!0},s)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=e.split("|"),i=s[0],r=s[1];this.read(i,r,"read",void 0,void 0,(s,n)=>{s&&this.logger.warn(`${t}loading namespace ${r} for language ${i} failed`,s),!s&&n&&this.logger.log(`${t}loaded namespace ${r} for language ${i}`,n),this.loaded(e,s,n)})}saveMissing(e,t,s,i,r){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t)){this.logger.warn(`did not save key "${s}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(null!=s&&""!==s){if(this.backend&&this.backend.create){let a={...n,isUpdate:r},l=this.backend.create.bind(this.backend);if(l.length<6)try{let r;(r=5===l.length?l(e,t,s,i,a):l(e,t,s,i))&&"function"==typeof r.then?r.then(e=>o(null,e)).catch(o):o(null,r)}catch(e){o(e)}else l(e,t,s,i,o,a)}e&&e[0]&&this.store.addResource(e[0],t,s,i)}}}let Y=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),i(e[1])&&(t.defaultValue=e[1]),i(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){let s=e[3]||e[2];Object.keys(s).forEach(e=>{t[e]=s[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),Q=e=>(i(e.ns)&&(e.ns=[e.ns]),i(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),i(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&0>e.supportedLngs.indexOf("cimode")&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e),G=()=>{},X=e=>{Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach(t=>{"function"==typeof e[t]&&(e[t]=e[t].bind(e))})};class ee extends R{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=Q(e),this.services={},this.logger=C,this.modules={external:[]},X(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"==typeof t&&(s=t,t={}),!t.defaultNS&&!1!==t.defaultNS&&t.ns&&(i(t.ns)?t.defaultNS=t.ns:0>t.ns.indexOf("translation")&&(t.defaultNS=t.ns[0]));let n=Y();this.options={...n,...this.options,...Q(t)},"v1"!==this.options.compatibilityAPI&&(this.options.interpolation={...n.interpolation,...this.options.interpolation}),void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);let o=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?C.init(o(this.modules.logger),this.options):C.init(null,this.options),this.modules.formatter?t=this.modules.formatter:"undefined"!=typeof Intl&&(t=Z);let s=new F(this.options);this.store=new $(this.options.resources,this.options);let i=this.services;i.logger=C,i.resourceStore=this.store,i.languageUtils=s,i.pluralResolver=new K(s,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),t&&(!this.options.interpolation.format||this.options.interpolation.format===n.interpolation.format)&&(i.formatter=o(t),i.formatter.init(i,this.options),this.options.interpolation.format=i.formatter.format.bind(i.formatter)),i.interpolator=new J(this.options),i.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},i.backendConnector=new W(o(this.modules.backend),i.resourceStore,i,this.options),i.backendConnector.on("*",function(t){for(var s=arguments.length,i=Array(s>1?s-1:0),r=1;r<s;r++)i[r-1]=arguments[r];e.emit(t,...i)}),this.modules.languageDetector&&(i.languageDetector=o(this.modules.languageDetector),i.languageDetector.init&&i.languageDetector.init(i,this.options.detection,this.options)),this.modules.i18nFormat&&(i.i18nFormat=o(this.modules.i18nFormat),i.i18nFormat.init&&i.i18nFormat.init(this)),this.translator=new E(this.services,this.options),this.translator.on("*",function(t){for(var s=arguments.length,i=Array(s>1?s-1:0),r=1;r<s;r++)i[r-1]=arguments[r];e.emit(t,...i)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}if(this.format=this.options.interpolation.format,s||(s=G),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(t=>{this[t]=function(){return e.store[t](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(t=>{this[t]=function(){return e.store[t](...arguments),e}});let a=r(),l=()=>{let e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(t),s(e,t)};if(this.languages&&"v1"!==this.options.compatibilityAPI&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initImmediate?l():setTimeout(l,0),a}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:G,s=t,r=i(e)?e:this.language;if("function"==typeof e&&(s=e),!this.options.resources||this.options.partialBundledLanguages){if(r&&"cimode"===r.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return s();let e=[],t=t=>{t&&"cimode"!==t&&this.services.languageUtils.toResolveHierarchy(t).forEach(t=>{"cimode"!==t&&0>e.indexOf(t)&&e.push(t)})};r?t(r):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(e=>t(e)),this.options.preload&&this.options.preload.forEach(e=>t(e)),this.services.backendConnector.load(e,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),s(e)})}else s(null)}reloadResources(e,t,s){let i=r();return"function"==typeof e&&(s=e,e=void 0),"function"==typeof t&&(s=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),s||(s=G),this.services.backendConnector.reload(e,t,e=>{i.resolve(),s(e)}),i}use(e){if(!e)throw Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&P.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1))for(let e=0;e<this.languages.length;e++){let t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}}changeLanguage(e,t){var s=this;this.isLanguageChangingTo=e;let n=r();this.emit("languageChanging",e);let o=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},a=(e,i)=>{i?(o(i),this.translator.changeLanguage(i),this.isLanguageChangingTo=void 0,this.emit("languageChanged",i),this.logger.log("languageChanged",i)):this.isLanguageChangingTo=void 0,n.resolve(function(){return s.t(...arguments)}),t&&t(e,function(){return s.t(...arguments)})},l=t=>{e||t||!this.services.languageDetector||(t=[]);let s=i(t)?t:this.services.languageUtils.getBestMatchFromCodes(t);s&&(this.language||o(s),this.translator.language||this.translator.changeLanguage(s),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(s)),this.loadResources(s,e=>{a(e,s)})};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(l):this.services.languageDetector.detect(l):l(e):l(this.services.languageDetector.detect()),n}getFixedT(e,t,s){var r=this;let n=function(e,t){let i,o;if("object"!=typeof t){for(var a=arguments.length,l=Array(a>2?a-2:0),h=2;h<a;h++)l[h-2]=arguments[h];i=r.options.overloadTranslationOptionHandler([e,t].concat(l))}else i={...t};i.lng=i.lng||n.lng,i.lngs=i.lngs||n.lngs,i.ns=i.ns||n.ns,""!==i.keyPrefix&&(i.keyPrefix=i.keyPrefix||s||n.keyPrefix);let u=r.options.keySeparator||".";return o=i.keyPrefix&&Array.isArray(e)?e.map(e=>`${i.keyPrefix}${u}${e}`):i.keyPrefix?`${i.keyPrefix}${u}${e}`:e,r.t(o,i)};return i(e)?n.lng=e:n.lngs=e,n.ns=t,n.keyPrefix=s,n}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let s=t.lng||this.resolvedLanguage||this.languages[0],i=!!this.options&&this.options.fallbackLng,r=this.languages[this.languages.length-1];if("cimode"===s.toLowerCase())return!0;let n=(e,t)=>{let s=this.services.backendConnector.state[`${e}|${t}`];return -1===s||0===s||2===s};if(t.precheck){let e=t.precheck(this,n);if(void 0!==e)return e}return!!(this.hasResourceBundle(s,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||n(s,e)&&(!i||n(r,e)))}loadNamespaces(e,t){let s=r();return this.options.ns?(i(e)&&(e=[e]),e.forEach(e=>{0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}),this.loadResources(e=>{s.resolve(),t&&t(e)}),s):(t&&t(),Promise.resolve())}loadLanguages(e,t){let s=r();i(e)&&(e=[e]);let n=this.options.preload||[],o=e.filter(e=>0>n.indexOf(e)&&this.services.languageUtils.isSupportedCode(e));return o.length?(this.options.preload=n.concat(o),this.loadResources(e=>{s.resolve(),t&&t(e)}),s):(t&&t(),Promise.resolve())}dir(e){return(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),e)?["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf((this.services&&this.services.languageUtils||new F(Y())).getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr":"rtl"}static createInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new ee(e,t)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:G,s=e.forkResourceStore;s&&delete e.forkResourceStore;let i={...this.options,...e,isClone:!0},r=new ee(i);return(void 0!==e.debug||void 0!==e.prefix)&&(r.logger=r.logger.clone(e)),["store","services","language"].forEach(e=>{r[e]=this[e]}),r.services={...this.services},r.services.utils={hasLoadedNamespace:r.hasLoadedNamespace.bind(r)},s&&(r.store=new $(this.store.data,i),r.services.resourceStore=r.store),r.translator=new E(r.services,i),r.translator.on("*",function(e){for(var t=arguments.length,s=Array(t>1?t-1:0),i=1;i<t;i++)s[i-1]=arguments[i];r.emit(e,...s)}),r.init(i,t),r.translator.options=i,r.translator.backendConnector.services.utils={hasLoadedNamespace:r.hasLoadedNamespace.bind(r)},r}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}let et=ee.createInstance();et.createInstance=ee.createInstance;let es=et.createInstance,ei=et.dir;et.init,et.loadResources,et.reloadResources,et.use,et.changeLanguage,et.getFixedT,et.t,et.exists,et.setDefaultNamespace,et.hasLoadedNamespace,et.loadNamespaces,et.loadLanguages},66067:(e,t,s)=>{s.d(t,{D:()=>h});let i=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,r={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},n=e=>r[e],o={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(i,n)},a=(e={})=>{o={...o,...e}},l=e=>{},h={type:"3rdParty",init(e){a(e.options.react),l(e)}}}};