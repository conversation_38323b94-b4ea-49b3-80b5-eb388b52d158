"use client";

import { useTranslation } from "react-i18next";
import BannerComponentsEvent from "@/components/sections/BannerComponentsEvent";
import bannerEvent from "@/assets/images/events/EventDetail.webp";
import bannerLeap from "@/assets/images/events/leapEvent.webp";
import bannerLibya from "@/assets/images/events/Libyaevent.webp";
import bannerGitex from "@/assets/images/events/gitexbanner.png";
import bannerAfricaForumFrance from "@/assets/images/events/AfricaForumFrance.webp";
import bannerLeapMobile from "@/assets/images/events/leapMobile.webp";
import bannerQHSEEXPO from "@/assets/images/events/bannerQHSEEXPO.webp";
import bannerAfricafrancemobile from "@/assets/images/events/AfricaForumFrancemobile.webp";
import bannerPentabellSalesTraining from "@/assets/images/events/BannerPentabellSalesTraining.webp"
import BannerQHSEmobile from "@/assets/images/events/BannerQHSEmobile.webp"
import bannerPentabellSalesTrainingmobile from "@/assets/images/events/BannerPentabellSalesTrainingmobile.webp"
import bannerLibyaMobile from "@/assets/images/events/eventlibyamobile.webp";
import eventmorocomobile from "@/assets/images/events/eventmorocomobile.webp";
import bannerMobileEvent from "@/assets/images/events/EventsDetailMobile.webp";
import iconSaudi from "@/assets/images/icons/mapSaudi.svg";
import iconTunisia from "@/assets/images/icons/mapiconTunisia.svg";
import iconTunisiamobile from "@/assets/images/icons/mapiconTunisiamobile.svg";
import iconmapAlgeria from "@/assets/images/icons/mapalgeriaicon.svg";
import iconLibya from "@/assets/images/icons/maplibya.svg";
import iconMaroc from "@/assets/images/icons/morocomap.svg";
import iconMarocMobile from "@/assets/images/icons/moroccomapmobile.svg";
import mapiconalgeriamobile from "@/assets/images/icons/mapiconalgeriamobile.svg";
import LeapIcon from "@/assets/images/icons/Leap.svg";
import iconSaudiMobile from "@/assets/images/icons/mapSaudiMobile.svg";

export default function BannerWrapper({ language, event, eventData }) {
  const { t } = useTranslation();

  const isDecarbonization = event === "decarbonization";
  const isLeap = event === "leap";
  const isQHSE = event === "QHSEEXPO"
  const isLibya = event === "libya";
  const isFranceAfricaForm = event === "AfricaFranceForum";
  const isGitex = event === "Gitex";
  const isPentabellSalesTraining = event === "PentabellSalestraining"
  const Icon = isDecarbonization
    ? iconSaudi
    : isLibya
      ? iconLibya
      : isGitex
        ? iconMaroc
        : isFranceAfricaForm
          ? iconTunisia
          : isPentabellSalesTraining
            ? iconTunisia
            : isQHSE
              ? iconmapAlgeria
              : iconSaudi;

  const MobileIcon = isDecarbonization
    ? iconSaudiMobile
    : isLibya
      ? iconLibya
      : isGitex
        ? iconMarocMobile
        : isFranceAfricaForm
          ? iconTunisiamobile
          : isPentabellSalesTraining
            ? iconTunisiamobile
            : isQHSE
              ? mapiconalgeriamobile
              : iconSaudiMobile;

  return (
    <BannerComponentsEvent
      bannerImg={
        isDecarbonization
          ? bannerEvent
          : isLibya
            ? bannerLibya
            : isGitex

              ? bannerGitex
              : isPentabellSalesTraining

                ? bannerPentabellSalesTraining
                : isFranceAfricaForm
                  ? bannerAfricaForumFrance
                  : isQHSE
                    ? bannerQHSEEXPO
                    : bannerLeap
      }
      bannerMobileImg={
        isDecarbonization
          ? bannerMobileEvent
          : isLibya
            ? bannerLibyaMobile
            : isGitex
              ? eventmorocomobile
              : isFranceAfricaForm
                ? bannerAfricafrancemobile
                : isPentabellSalesTraining
                  ? bannerPentabellSalesTrainingmobile
                  : isQHSE
                    ? BannerQHSEmobile
                    : bannerLeapMobile
      }
      Icon={Icon}
      MobileIcon={MobileIcon}
      {...(isLeap && { LeapIcon })}
      event={event}
      eventData={eventData}
      title={
        isDecarbonization
          ? t("eventDetails:bannerTitle")
          : isLibya
            ? t("eventDetailsLibya:bannerTitle")
            : isGitex
              ? t("eventDetailsGitex:bannerTitle")
              : isFranceAfricaForm
                ? t("ForumAfricaFrance:bannerTitle")
                : isPentabellSalesTraining
                  ? t("PentabellSalestraining:bannerTitle")
                  : isQHSE
                    ? t("QHSEEXPO:bannerTitle")
                    : t("eventDetailsLeap:bannerTitle")
      }
      subtitle={
        isDecarbonization
          ? t("eventDetails:bannerSubTitle")
          : isLibya
            ? t("eventDetailsLibya:bannerSubTitle")
            : isGitex
              ? t("eventDetailsGitex:bannerSubTitle")
              : isFranceAfricaForm
                ? t("ForumAfricaFrance:bannerSubTitle")
                : isPentabellSalesTraining
                  ? t("PentabellSalestraining:bannerSubTitle")
                  : isQHSE
                    ? t("QHSEEXPO:bannerSubTitle")
                    : t("eventDetailsLeap:bannerSubTitle")

      }
      language={language}
      url={
        isDecarbonization
          ? "franco-saudi-decarbonization-days"
          : isLibya
            ? "Libyan-French-economic-forum-2025"
            : isGitex
              ? "Gitex-africa-morocco-2025"
              : isFranceAfricaForm
                ? "Africa-France-forum-on-ecological-and-energy-transition-2025"
                : isPentabellSalesTraining
                  ? "Pentabell-sales-training-and-workshop"
                  : isQHSE
                    ? "QHSE-EXPO-2025"
                    : "leap-tech-conference-2025-riyadh"
      }
      name={
        isDecarbonization
          ? t("eventDetails:titleBreadCrumbs")
          : isLibya
            ? t("eventDetailsLibya:titleBreadCrumbs")
            : isGitex
              ? t("eventDetailsGitex:titleBreadCrumbs")
              : isFranceAfricaForm
                ? t("ForumAfricaFrance:titleBreadCrumbs")
                : isPentabellSalesTraining
                  ? t("PentabellSalestraining:titleBreadCrumbs")
                  : isQHSE
                    ? t("QHSEEXPO:titleBreadCrumbs")
                    : t("eventDetailsLeap:titleBreadCrumbs")
      }
      height="70vh"
      t={t}
    />
  );
}
