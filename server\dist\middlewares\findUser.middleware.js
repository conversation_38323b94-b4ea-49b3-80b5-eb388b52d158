"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const user_model_1 = __importDefault(require("../apis/user/user.model"));
const messages_1 = require("@/utils/helpers/messages");
const isUserExist = async (request, response, next) => {
    const id = request.user._id;
    const user = await user_model_1.default.findById(id);
    request.user = user;
    if (!user)
        throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
    next();
};
exports.default = isUserExist;
//# sourceMappingURL=findUser.middleware.js.map