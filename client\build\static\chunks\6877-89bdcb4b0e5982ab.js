"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6877],{64832:function(e,t,A){A.r(t);var a,i=A(94746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var a in A)({}).hasOwnProperty.call(A,a)&&(e[a]=A[a])}return e}).apply(null,arguments)}t.default=e=>i.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),a||(a=i.createElement("path",{fill:"#234791",d:"m16.172 11-5.364-5.364 1.414-1.414L20 12l-7.778 7.778-1.414-1.414L16.172 13H4v-2z"})))},37693:function(e,t,A){var a,i=A(94746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var a in A)({}).hasOwnProperty.call(A,a)&&(e[a]=A[a])}return e}).apply(null,arguments)}t.Z=e=>i.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),a||(a=i.createElement("path",{fill:"#E5F0FC",d:"m12 15-4.242-4.242 1.414-1.414 2.829 2.828 2.828-2.828 1.414 1.414z"})))},97160:function(e,t,A){A.r(t);var a,i,r=A(94746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var a in A)({}).hasOwnProperty.call(A,a)&&(e[a]=A[a])}return e}).apply(null,arguments)}t.default=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,fill:"none"},e),a||(a=r.createElement("rect",{width:32,height:32,fill:"#699BD4",rx:2})),i||(i=r.createElement("path",{stroke:"#fff",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M26.5 21.42v3a2 2 0 0 1-2.18 2 19.8 19.8 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.8 19.8 0 0 1-3.07-8.67A2 2 0 0 1 8.61 6.5h3a2 2 0 0 1 2 1.72c.127.96.362 1.903.7 2.81a2 2 0 0 1-.45 2.11l-1.27 1.27a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45c.908.339 1.85.573 2.81.7a2 2 0 0 1 1.72 2.03"})))},87311:function(e,t,A){A.r(t);var a,i,r=A(94746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var a in A)({}).hasOwnProperty.call(A,a)&&(e[a]=A[a])}return e}).apply(null,arguments)}t.default=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,fill:"none"},e),a||(a=r.createElement("rect",{width:32,height:32,fill:"#699BD4",rx:2})),i||(i=r.createElement("path",{stroke:"#fff",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M26.5 10.5c0-1.1-.9-2-2-2h-16c-1.1 0-2 .9-2 2m20 0v12c0 1.1-.9 2-2 2h-16c-1.1 0-2-.9-2-2v-12m20 0-10 7-10-7"})))},88833:function(e,t,A){A.r(t);var a,i,r,n=A(94746);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var a in A)({}).hasOwnProperty.call(A,a)&&(e[a]=A[a])}return e}).apply(null,arguments)}t.default=e=>n.createElement("svg",l({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,fill:"none"},e),a||(a=n.createElement("rect",{width:32,height:32,fill:"#699BD4",rx:2})),i||(i=n.createElement("g",{stroke:"#fff",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,clipPath:"url(#locationPin_svg__a)"},n.createElement("path",{d:"M25.5 14.5c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0"}),n.createElement("path",{d:"M16.5 17.5a3 3 0 1 0 0-6 3 3 0 0 0 0 6"}))),r||(r=n.createElement("defs",null,n.createElement("clipPath",{id:"locationPin_svg__a"},n.createElement("path",{fill:"#fff",d:"M4.5 4.5h24v24h-24z"})))))},23913:function(e,t,A){var a,i,r,n=A(94746);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var a in A)({}).hasOwnProperty.call(A,a)&&(e[a]=A[a])}return e}).apply(null,arguments)}t.Z=e=>n.createElement("svg",l({xmlns:"http://www.w3.org/2000/svg",width:48,height:49,fill:"none"},e),a||(a=n.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M24 25a1.5 1.5 0 0 1 1.5 1.5v16a1.5 1.5 0 0 1-3 0v-16A1.5 1.5 0 0 1 24 25",clipRule:"evenodd"})),i||(i=n.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M22.573 8.869A12.5 12.5 0 0 0 9.072 29.247a1.5 1.5 0 1 1-2.144 2.099 15.5 15.5 0 1 1 25.566-16.347H35a10.501 10.501 0 0 1 5.834 19.23 1.5 1.5 0 1 1-1.666-2.494A7.5 7.5 0 0 0 35 18h-3.58a1.5 1.5 0 0 1-1.438-1.071 12.5 12.5 0 0 0-7.409-8.06",clipRule:"evenodd"})),r||(r=n.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M22.94 25.44a1.5 1.5 0 0 1 2.12 0l8 8a1.5 1.5 0 0 1-2.12 2.12L24 28.622l-6.94 6.94a1.5 1.5 0 0 1-2.12-2.122z",clipRule:"evenodd"})))},45870:function(e,t,A){A.r(t);var a,i,r=A(94746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var a in A)({}).hasOwnProperty.call(A,a)&&(e[a]=A[a])}return e}).apply(null,arguments)}t.default=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:42,height:42,fill:"none"},e),a||(a=r.createElement("g",{clipPath:"url(#Users_svg__a)"},r.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:4,d:"M29.75 36.75v-3.5a7 7 0 0 0-7-7h-14a7 7 0 0 0-7 7v3.5m38.5 0v-3.5A7 7 0 0 0 35 26.477m-7-21a7 7 0 0 1 0 13.563m-5.25-6.79a7 7 0 1 1-14 0 7 7 0 0 1 14 0"}))),i||(i=r.createElement("defs",null,r.createElement("clipPath",{id:"Users_svg__a"},r.createElement("path",{fill:"#fff",d:"M0 0h42v42H0z"})))))},69025:function(e,t,A){A.r(t);var a,i,r=A(94746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var a in A)({}).hasOwnProperty.call(A,a)&&(e[a]=A[a])}return e}).apply(null,arguments)}t.default=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:42,height:42,fill:"none"},e),a||(a=r.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M10.5 38.5V7A3.5 3.5 0 0 1 14 3.5h14A3.5 3.5 0 0 1 31.5 7v31.5zM10.5 21H7a3.5 3.5 0 0 0-3.5 3.5V35A3.5 3.5 0 0 0 7 38.5h3.5M31.5 15.75H35a3.5 3.5 0 0 1 3.5 3.5V35a3.5 3.5 0 0 1-3.5 3.5h-3.5"})),i||(i=r.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.5 10.5h7M17.5 17.5h7M17.5 24.5h7M17.5 31.5h7"})))},8429:function(e,t,A){A.r(t);var a,i,r,n=A(94746);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var a in A)({}).hasOwnProperty.call(A,a)&&(e[a]=A[a])}return e}).apply(null,arguments)}t.default=e=>n.createElement("svg",l({xmlns:"http://www.w3.org/2000/svg",width:42,height:42,fill:"none"},e),a||(a=n.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M35 10.5H7A3.5 3.5 0 0 0 3.5 14v14A3.5 3.5 0 0 0 7 31.5h28a3.5 3.5 0 0 0 3.5-3.5V14a3.5 3.5 0 0 0-3.5-3.5"})),i||(i=n.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M21 24.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7"})),r||(r=n.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.5 21h.018M31.5 21h.017"})))},22849:function(e,t,A){A.r(t);var a,i,r=A(94746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var a in A)({}).hasOwnProperty.call(A,a)&&(e[a]=A[a])}return e}).apply(null,arguments)}t.default=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:42,height:42,fill:"none"},e),a||(a=r.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M6.737 15.085a7 7 0 0 1 8.365-8.347 7 7 0 0 1 11.795 0 7 7 0 0 1 8.365 8.365 7 7 0 0 1 0 11.795 7 7 0 0 1-8.347 8.365 7 7 0 0 1-11.813 0 7 7 0 0 1-8.365-8.348 7 7 0 0 1 0-11.83"})),i||(i=r.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M28 14H17.5a3.5 3.5 0 1 0 0 7h7a3.5 3.5 0 1 1 0 7H14M21 31.5v-21"})))},36637:function(e,t,A){A.r(t);var a,i=A(94746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var a in A)({}).hasOwnProperty.call(A,a)&&(e[a]=A[a])}return e}).apply(null,arguments)}t.default=e=>i.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:42,height:42,fill:"none"},e),a||(a=i.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M21 12.25v24.5M5.25 31.5a1.75 1.75 0 0 1-1.75-1.75V7a1.75 1.75 0 0 1 1.75-1.75H14a7 7 0 0 1 7 7 7 7 0 0 1 7-7h8.75A1.75 1.75 0 0 1 38.5 7v22.75a1.75 1.75 0 0 1-1.75 1.75h-10.5A5.25 5.25 0 0 0 21 36.75a5.25 5.25 0 0 0-5.25-5.25z"})))},75638:function(e,t,A){var a=A(2265),i=A(4828),r=A.n(i);t.Z=()=>((0,a.useEffect)(()=>{r().initialize({gtmId:"GTM-NXLL5DG"})},[]),null)},85079:function(e,t,A){A.d(t,{Z:function(){return f}});var a,i=A(57437),r=A(2265),n=A(98489),l=A(89414),s=A(30731),o=A(96369),c=A(44164),d=A(55788),u=A(37693),m=A(94746);function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var a in A)({}).hasOwnProperty.call(A,a)&&(e[a]=A[a])}return e}).apply(null,arguments)}var p=e=>m.createElement("svg",h({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),a||(a=m.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16 2v4M8 2v4m-5 4h18M5 4h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2"}))),g=e=>{let{t,section:A}=e;if(!A||!A.data)return null;let{description:a,subTitle:r,subDescription:n,leaves:l=[],annualLeave:s,maternityLeave:o,paternityLeave:c,sickLeave:d,paidTimeOff:u,parentalLeave:m,bereavementLeave:h,pilgrimageleave:g,marriageLeave:b,casualLeave:f}=A.data,v=(e,A)=>(0,i.jsxs)("div",{className:"item",children:[(0,i.jsx)("p",{className:"service-sub-title",children:t(e)}),(0,i.jsx)("p",{className:"service-description paragraph",children:Array.isArray(A)?A.map((e,A)=>(0,i.jsx)("p",{children:t(e)},A)):(0,i.jsx)("p",{children:t(A)})})]}),w=(e,A)=>e?Array.isArray(e)?e.map((e,a)=>(0,i.jsx)("p",{className:A,children:t(e)},a)):(0,i.jsx)("p",{className:A,children:t(e)}):null;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{id:"labor-tn-laws",className:"item",children:[w(a,"service-description paragraph"),w(r,"service-sub-title"),w(n,"service-description paragraph"),(0,i.jsx)("div",{className:"holidays-dates",children:l.map((e,A)=>(0,i.jsxs)("div",{className:"item",children:[(0,i.jsxs)("p",{className:"title",children:[(0,i.jsx)(p,{}),t(e.date)]}),(0,i.jsx)("p",{className:"paragraph",children:t(e.title)})]},A))})]}),s&&v(s.title,s.description),u&&v(u.title,u.description),o&&v(o.title,o.description),c&&v(c.title,c.description),d&&v(d.title,d.description),f&&v(f.title,f.description),b&&v(b.title,b.description),h&&v(h.title,h.description),m&&v(m.title,m.description),g&&v(g.title,g.description)]})},b=e=>{let{t,data:A}=e;return A&&A.items?(0,i.jsxs)(i.Fragment,{children:[A.titleKey&&(0,i.jsxs)("div",{className:"item",children:[(0,i.jsx)("p",{className:"service-sub-title",children:t(A.titleKey)}),A.descriptionKey&&(0,i.jsx)("p",{className:"service-description paragraph",children:t(A.descriptionKey)})]}),(0,i.jsx)("div",{className:"payroll-tn",children:A.items.map((e,A)=>(0,i.jsxs)("div",{className:"payroll-tn-item",children:[e.titleKey&&(0,i.jsx)("p",{className:"title",children:t(e.titleKey)}),e.dateKeys&&(0,i.jsx)("p",{className:"date",children:e.dateKeys.map((A,a)=>(0,i.jsxs)("span",{children:[t(A),a<e.dateKeys.length-1&&(0,i.jsx)("br",{})]},a))}),e.descriptionKey&&(0,i.jsx)("p",{className:"paragraph",children:t(e.descriptionKey)})]},A))})]}):null},f=e=>{let{data:t}=e,{t:A}=(0,d.$G)(),[a,m]=(0,r.useState)(t.sections[0]?.id||!1),h=e=>(t,A)=>{m(!!A&&e)};return(0,i.jsx)("div",{id:"labor-tn-laws",className:"custom-max-width",children:(0,i.jsxs)(n.default,{children:[(0,i.jsx)("h2",{className:"heading-h1",children:A(t.title)}),(0,i.jsx)(l.default,{container:!0,columnSpacing:3,rowSpacing:2,className:"container",children:t.sections.map(e=>{let{id:t,title:r,type:n,subsections:d,data:m}=e;return(0,i.jsx)(l.default,{item:!0,xs:12,sm:12,children:(0,i.jsxs)(s.Z,{elevation:0,expanded:a===t,className:"services-accordion",disableGutters:!0,onChange:h(t),children:[(0,i.jsx)(o.Z,{"aria-controls":`panel-content-${t}`,id:`panel-header-${t}`,className:"services-accordion-header",expandIcon:(0,i.jsx)(u.Z,{}),children:(0,i.jsx)("h3",{className:"service-title",children:A(r)})}),(0,i.jsxs)(c.Z,{children:["leaveEntitlements"===n&&(0,i.jsx)(g,{t:A,section:e}),"payroll"===n&&(0,i.jsx)(b,{t:A,data:e}),!n&&d?.map((e,t)=>i.jsxs("div",{children:[e.title&&i.jsx("p",{className:"service-sub-title",children:A(e.title)}),e.description&&(Array.isArray(e.description)?e.description.map((e,t)=>i.jsx("p",{className:"service-description paragraph",children:A(e)},t)):i.jsx("p",{className:"service-description paragraph",children:A(e.description)})),e.list&&i.jsx("div",{className:"item",children:i.jsx("ul",{className:"service-description paragraph",children:e.list.map((e,t)=>i.jsxs("li",{children:[A(e.text),e.sublist&&i.jsx("ul",{children:e.sublist.map((e,t)=>i.jsx("li",{children:A(e)},t))})]},t))})})]},t))]})]})},t)})})]})})}},32091:function(e,t,A){A.d(t,{default:function(){return _}});var a=A(57437),i=A(98489),r=A(55788),n=A(9467),l=A(61984),s={src:"/_next/static/media/logo-abb.0b5fb7ca.webp",height:66,width:75,blurDataURL:"data:image/webp;base64,UklGRqYAAABXRUJQVlA4WAoAAAAQAAAABwAABgAAQUxQSDkAAAAAAAAAAAAAAAAAMwcqNxA9FxjjVaHnV+iHZ9OiktKL07Z1D3luk0aZYQAAAAAAAAAAAAAAAAAAAAAAVlA4IEYAAACwAQCdASoIAAcAAkA4JbACdAEOumzkAP75L565+ULVGH/CQTlCT4VNvFBKSr7YW5n6HfKU/8zm2j/TizX/f8p3+nFRwAAA",blurWidth:8,blurHeight:7},o={src:"/_next/static/media/logo-nokia.e19b4454.webp",height:66,width:130,blurDataURL:"data:image/webp;base64,UklGRoAAAABXRUJQVlA4WAoAAAAQAAAABwAAAwAAQUxQSCEAAAAABgAcBQoNAwOGMjhSXEMORik5SSw3LEJDAAAAAAAAAAAAVlA4IDgAAADwAQCdASoIAAQAAkA4JbACdLoAArrgZqAA/vktvFhrqwWOjM6Y6A7Dj/4IRMaYcz54fr5lhTSAAA==",blurWidth:8,blurHeight:4},c=A(46923),d=A(57644),u={src:"/_next/static/media/logo-Alcatel.d0a74567.webp",height:66,width:194,blurDataURL:"data:image/webp;base64,UklGRnAAAABXRUJQVlA4WAoAAAAQAAAABwAAAgAAQUxQSBkAAAAAAAAAAAAAPkU7Ri45Oy3c7AAAAAAAADA2AFZQOCAwAAAA0AEAnQEqCAADAAJAOCUAXYAj7cIf1wAA/vr+Tv2ucxk/RLycjQ5wQ81HAtLwsAAA",blurWidth:8,blurHeight:3},m={src:"/_next/static/media/logo-ZTE.1150a51f.webp",height:66,width:52,blurDataURL:"data:image/webp;base64,UklGRpIAAABXRUJQVlA4WAoAAAAQAAAABQAABwAAQUxQSC4AAAABYBvbViKsASKGBogtpA5Ct1JWwo19m6WGiEgw9W89liIGK6f7LS+7UYZQIcEUVlA4ID4AAAAQAgCdASoGAAgAAkA4JaACdFkAAYhpEgAAAP7aJ9pZ9CrjnvVnmOKckZ5OSTxrfsZ/xanwRQf5Wj4ImAAAAA==",blurWidth:6,blurHeight:8},h={src:"/_next/static/media/logo-johncockerill.5d50a59f.webp",height:66,width:105,blurDataURL:"data:image/webp;base64,UklGRmgAAABXRUJQVlA4WAoAAAAQAAAABwAABAAAQUxQSCkAAAAAABECAAAAAAA1gy8WCgAAAJeIGF1oTQADch0dYV1lZ3sAAAAAAAAAAABWUDggGAAAADABAJ0BKggABQACQDglpAADcAD++5zAAA==",blurWidth:8,blurHeight:5},p={src:"/_next/static/media/logo-proximus.b6613119.webp",height:65,width:139,blurDataURL:"data:image/webp;base64,UklGRn4AAABXRUJQVlA4WAoAAAAQAAAABwAAAwAAQUxQSCEAAAAAAAAAAAAAAABQW314WVpUWCRCX2IrLUQ6AAAAAAAAAAAAVlA4IDYAAACwAQCdASoIAAQAAkA4JYgCdAD0qHfMAP7270XtkW3d6fJzQQwuTfxzZ+5ICOOghmaNBZfgAAA=",blurWidth:8,blurHeight:4},g=A(16757),b={src:"/_next/static/media/logo-arcelor.c505bea8.webp",height:65,width:121,blurDataURL:"data:image/webp;base64,UklGRoAAAABXRUJQVlA4WAoAAAAQAAAABwAAAwAAQUxQSCEAAAAAAAAAACOZfUkAAAAYLBwUG0E9SzlUT00AAAAAAAAAAAAAVlA4IDgAAABQAQCdASoIAAQAAkA4JQBOgCgAAP7wORAl6pUufsrFXcAz7ZTfr48dH08B2Q26uyuXuV9UuIAAAA==",blurWidth:8,blurHeight:4},f=A(87448),v={src:"/_next/static/media/logo-samsung.5dd9d4fc.webp",height:65,width:164,blurDataURL:"data:image/webp;base64,UklGRnQAAABXRUJQVlA4WAoAAAAQAAAABwAAAgAAQUxQSBkAAAAAEg0NDxIMERGLkJGFh3yokQIAAAABAQAAAFZQOCA0AAAA0AEAnQEqCAADAAJAOCUAToAh4CVYSAAA/v0dymISI4gomHFG5X1TNbLa//BxeJOaJogAAA==",blurWidth:8,blurHeight:3},w={src:"/_next/static/media/logo-weir.a9a7b255.webp",height:28,width:85,blurDataURL:"data:image/webp;base64,UklGRmwAAABXRUJQVlA4WAoAAAAQAAAABwAAAgAAQUxQSBkAAAAAs6ir06Oo06/Lv/Lg3/LyxubmttiotbTCAFZQOCAsAAAA0AEAnQEqCAADAAJAOCWoAnS6AfgAA7AA/vCwN/84lRBJu/H/5gkbxG5hAAA=",blurWidth:8,blurHeight:3},y={src:"/_next/static/media/logo-butachimie.59c76f66.webp",height:65,width:216,blurDataURL:"data:image/webp;base64,UklGRmoAAABXRUJQVlA4WAoAAAAQAAAABwAAAQAAQUxQSBEAAAAAey0gIzAlHyRgNkE/NUIyQwBWUDggMgAAALABAJ0BKggAAgACQDglAE6AId/AXYAA/vwPXH1exr4DY8oT661l7fbv6cRsTa28AAAA",blurWidth:8,blurHeight:2},x={src:"/_next/static/media/logo-egis.887410c8.webp",height:65,width:95,blurDataURL:"data:image/webp;base64,UklGRoIAAABXRUJQVlA4WAoAAAAQAAAABwAABAAAQUxQSCkAAAAAAAAAAAAAAAA2bU4KFhoXGlGLNlRKPmhWMlJFAAMnEAIAAAAAAAAAAABWUDggMgAAALABAJ0BKggABQACQDgljAAC5xN5JQAA/k3f6/4zq3VYyB1zo7yeZ+hcpPx+aciEQAAA",blurWidth:8,blurHeight:5},Q={src:"/_next/static/media/logo-systra.64d60e6a.webp",height:66,width:132,blurDataURL:"data:image/webp;base64,UklGRnQAAABXRUJQVlA4WAoAAAAQAAAABwAAAwAAQUxQSCEAAAAAAAAAAAAAAACBU1Zqe1thYjs2MV5HSkFXAAAAAAAAAAAAVlA4ICwAAABwAQCdASoIAAQAAkA4JagCdAFAAAD+71JoztOfJ7lopFf3VL37WY/V1/gAAA==",blurWidth:8,blurHeight:4},U={src:"/_next/static/media/logo-equinix.7982b8bd.webp",height:66,width:248,blurDataURL:"data:image/webp;base64,UklGRmQAAABXRUJQVlA4WAoAAAAQAAAABwAAAQAAQUxQSBEAAAAAPzAPEgsODw4+MxEVDwsRDgBWUDggLAAAALABAJ0BKggAAgACQDglAE6AId/AXYAA/vzTBk9hpTeoEZl/e00k3CuAAAAA",blurWidth:8,blurHeight:2},j={src:"/_next/static/media/logo-lilly.e041692b.webp",height:51,width:88,blurDataURL:"data:image/webp;base64,UklGRpIAAABXRUJQVlA4WAoAAAAQAAAABwAABAAAQUxQSCkAAAAAMCcvgQAAAABnH5FDa0YpAFqORndxlDYcclNnZlJimzYAAAAAAAhKAABWUDggQgAAANABAJ0BKggABQACQDglsAJ0APRtJ0YAAP7o8dCABirqxCO4ZJBnMYEGzKfTMcOPtWff2iJymmOaX7ZmEXxisccgAA==",blurWidth:8,blurHeight:5},B={src:"/_next/static/media/logo-saipem.7f02e25b.webp",height:150,width:117,blurDataURL:"data:image/webp;base64,UklGRqoAAABXRUJQVlA4WAoAAAAQAAAABQAABwAAQUxQSDEAAAAA/ffX1ff//QYBAQR++4ORXSIDEjR4q5bxZwIAAAb+/eHDweL/ODQxMjo1oqOYjZvAAFZQOCBSAAAAEAIAnQEqBgAIAAJAOCWIAnQwR0GnygTjQAD+9uOk+UU1Q94WJWFizIBLVHb/lux4ffyhg8xaFl07/irVu1u0XZionrfGwx5vUD8sRtx4/TkgAA==",blurWidth:6,blurHeight:8},E={src:"/_next/static/media/logo-nec.cc83c162.webp",height:66,width:81,blurDataURL:"data:image/webp;base64,UklGRpYAAABXRUJQVlA4WAoAAAAQAAAABwAABgAAQUxQSDkAAAAAAAAAAAAAAAACEx8rJgk5NIFuuGw+xFQg06e4ekDoEwAxmWOFZluNZAAAAAAAAAAAAAAAAAAAAAAAVlA4IDYAAADQAQCdASoIAAcAAkA4JZgCdAEO+yDQAAD+/Laosm+iyfwQnfbLP+99uSjUvOodhmJHkW4AAAA=",blurWidth:8,blurHeight:7},C={src:"/_next/static/media/logo-colas.78353cd6.webp",height:66,width:140,blurDataURL:"data:image/webp;base64,UklGRpAAAABXRUJQVlA4WAoAAAAQAAAABwAAAwAAQUxQSCEAAAAAAAAZUjYVAAA/qfn/771rLyeP7v/lr1whAAAKOScNAAAAVlA4IEgAAAAQAgCdASoIAAQAAkA4JagCdLoAAxoZ++cAAPx5P2Gfvr6xDBvm9SPX8YBoAojrCs/9jOrfHf/ppH/RnVvrv8z31L6lxu3cAAA=",blurWidth:8,blurHeight:4},R={src:"/_next/static/media/logo-omv.5f5920c4.webp",height:66,width:126,blurDataURL:"data:image/webp;base64,UklGRngAAABXRUJQVlA4WAoAAAAQAAAABwAAAwAAQUxQSCEAAAAAAwkAAAAAAABmaiRValVxPW90IlJhVklKAgMAAAAAAAAAVlA4IDAAAACwAQCdASoIAAQAAkA4JQBOgCHfTAwAAP79HbRRRncO7UdyPEMErVtDt37GE0kdgAA=",blurWidth:8,blurHeight:4},k=A(39907),Z=A(64485),N=A(44511),F={src:"/_next/static/media/logo-siemens.ff304601.webp",height:66,width:141,blurDataURL:"data:image/webp;base64,UklGRnoAAABXRUJQVlA4WAoAAAAQAAAABwAAAwAAQUxQSCEAAAAAAAAAAAAAAABlmY+VioJwjzJQMkFELT9GAAAAAAAAAAAAVlA4IDIAAACwAQCdASoIAAQAAkA4JagCdAEO9qOAAP7vWfXCinAELali1BRJ/kVySSftxJ/fT9wAAA==",blurWidth:8,blurHeight:4},L={src:"/_next/static/media/logo-geocean.a252c33b.webp",height:66,width:174,blurDataURL:"data:image/webp;base64,UklGRnIAAABXRUJQVlA4WAoAAAAQAAAABwAAAgAAQUxQSBkAAAAAOSsAAAAAAABcgUpfX15bViQkAAAAAAAAAFZQOCAyAAAA8AEAnQEqCAADAAJAOCWUAnR/ABgaLbMAAP728Mpqdiwpr29zArZRfvHDFMsCbfXQoAA=",blurWidth:8,blurHeight:3},O={src:"/_next/static/media/logo-LFB.f9e8ae71.webp",height:66,width:108,blurDataURL:"data:image/webp;base64,UklGRoYAAABXRUJQVlA4WAoAAAAQAAAABwAABAAAQUxQSCkAAAAAAAAAAAAAAADGv7/TWUxog/Dn5/+BdXeSHBkXGyEaHAwAAAAAAAAAAABWUDggNgAAALABAJ0BKggABQACQDgllALsAPN3hQAA/t2S/Z0JQE+vRymUNH8DmlQAd0kP3t/PQpPT+vwAAA==",blurWidth:8,blurHeight:5},S={src:"/_next/static/media/logo-Ericsson.a4fa3379.webp",height:66,width:132,blurDataURL:"data:image/webp;base64,UklGRmAAAABXRUJQVlA4WAoAAAAQAAAABwAAAwAAQUxQSCEAAAAACzsAAAAAAACwgjtRSVJUXKFfGRkiIyMaAAAAAAAAAAAAVlA4IBgAAAAwAQCdASoIAAQAAkA4JaQAA3AA/vucwAA=",blurWidth:8,blurHeight:4},D={src:"/_next/static/media/logo-motorola.ddbd251a.webp",height:66,width:198,blurDataURL:"data:image/webp;base64,UklGRlwAAABXRUJQVlA4WAoAAAAQAAAABwAAAgAAQUxQSBkAAAAAQAoLGQ4HFwuXO1paVlNbYRYAAAAAAAAAAFZQOCAcAAAAUAEAnQEqCAADAAJAOCWcAAQAAAD++u/pvYzwAA==",blurWidth:8,blurHeight:3},W=A(41351),_=function(e){let{disableTxt:t,ksa:A}=e,{t:_}=(0,r.$G)(),J=[{label:"ABB",logo:s},{label:"Nokia",logo:o},{label:"Total",logo:c.Z},{label:"Naval",logo:d.Z},{label:"AlcatelLucent",logo:u},{label:"ZTE",logo:m},{label:"JohnCockerill",logo:h},{label:"Proximus",logo:p},{label:"ARCELOR",logo:b},{label:"TRACTEBEL",logo:f.Z},{label:"Samsung",logo:v},{label:"Butachimie",logo:y},{label:"EGIS",logo:x},{label:"Systra",logo:Q},{label:"Equinix",logo:U},{label:"ColasRail",logo:C},{label:"Omv",logo:R},{label:"Alstom",logo:k.Z},{label:"LFB",logo:O},{label:"Tecnimont",logo:N.Z},{label:"Motorola",logo:D},{label:"Ericsson",logo:S},{label:"Siemens",logo:F},{label:"Geocean",logo:L},{label:"Visa",logo:g.Z},{label:"NEC",logo:E},{label:"Weir",logo:w},{label:"GE",logo:Z.Z},{label:"Lilly",logo:j},{label:"Saipem",logo:B}],[I]=(0,n.Z)({loop:!0,align:"start"},[(0,l.Z)({playOnInit:!0,delay:1e3})]);return(0,a.jsx)(i.default,{id:"our-partners",className:A?"custom-max-width ksa":"custom-max-width",children:(0,a.jsxs)("section",{className:"embla",id:"partners__slider",children:[!t&&(0,a.jsxs)("div",{className:"heading",children:[(0,a.jsx)("h2",{className:"heading-h1 text-center",children:_("aboutUs:partners:title")}),(0,a.jsx)("p",{className:"sub-heading text-center",children:_("aboutUs:partners:description")})]}),(0,a.jsx)("div",{className:"embla__viewport",ref:I,children:(0,a.jsx)("div",{className:"embla__container",children:J.map((e,t)=>(0,a.jsx)(W.Z,{item:e,t:_},t))})})]})})}},41351:function(e,t,A){A.d(t,{Z:function(){return l}});var a=A(57437),i=A(98489),r=A(2265),n=()=>{let[e,t]=(0,r.useState)(!1),A=(0,r.useRef)(null),a=(0,r.useRef)(!1);return(0,r.useEffect)(()=>{let e=new IntersectionObserver(A=>{let[i]=A;i.isIntersecting&&!a.current&&(t(!0),a.current=!0,e.disconnect())},{threshold:.1});return A.current&&e.observe(A.current),()=>{A.current&&e.unobserve(A.current)}},[]),{isInView:e,elementRef:A}},l=e=>{let{item:t,t:A}=e,{isInView:r,elementRef:l}=n();return(0,a.jsx)("div",{className:"embla__slide",ref:l,children:(0,a.jsx)(i.default,{className:"slide__container custom-max-width",sx:{backgroundImage:r?`url(${t.logo.src})`:"linear-gradient(to bottom, rgba(35, 71, 145, 0), rgba(35, 71, 145, 0.6))",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat",transition:"background-image 0.5s ease-in-out"},children:t.label&&(0,a.jsx)("img",{width:0,height:0,alt:`${A("aboutUs:partners:strategicPartner")}${t.label}`,title:`${A("aboutUs:partners:collaborationAvec")}${t.label}`,src:t.logo.src,style:{display:"none"},loading:"lazy"})})})}},30100:function(e,t,A){A.d(t,{Z:function(){return i}});var a=A(57437);function i(e){let{errMsg:t,success:A}=e;return(0,a.jsxs)(a.Fragment,{children:[t&&(0,a.jsx)("div",{className:"errorMsgBanner",children:t}),A&&(0,a.jsx)("div",{className:"successMsgBanner",children:A})]})}},88415:function(e,t,A){var a=A(57437),i=A(2265);A(25330),t.Z=e=>{let{src:t,alt:A}=e,[r,n]=(0,i.useState)(!1),l=(0,i.useRef)();return(0,i.useEffect)(()=>{let e=new IntersectionObserver(t=>{let[A]=t;A.isIntersecting&&(n(!0),e.unobserve(A.target))},{threshold:.1});return l.current&&e.observe(l.current),()=>e.disconnect()},[]),(0,a.jsx)("img",{ref:l,src:r?t:void 0,"data-src":t,alt:A,loading:"lazy",style:{opacity:r?1:.5,transition:"opacity 0.3s"}})}},93214:function(e,t,A){A.d(t,{cU:function(){return l},xk:function(){return n},yX:function(){return r}});var a=A(83464),i=A(40257);let r=a.Z.create({baseURL:i.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),n=a.Z.create({baseURL:i.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),l=a.Z.create({baseURL:i.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"});a.Z.create({baseURL:i.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))},50933:function(e,t,A){A.d(t,{uu:function(){return o},eo:function(){return d},cl:function(){return c}});var a=A(86484),i=A(93214),r=A(46172);let n=async(e,t,A)=>new Promise(async(a,n)=>{i.xk.post(`${r.Y.contact}`,e).then(e=>{t("Submitted."),A(""),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(500===e.response.status?(t(!1),A("Internal error server")):(t(!1),A(e.response.data.message))),e&&n(e)})}),l=e=>new Promise(async(t,A)=>{try{let A=await i.yX.get(`${r.Y.contact}`,{params:{paginated:e.paginated,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyword:e.keyword,createdAt:e.createdAt,type:e.type,email:e.email}});t(A.data)}catch(e){A(e)}}),s=e=>new Promise(async(t,A)=>{try{let A=await i.yX.get(`${r.Y.contact}/${e}`);t(A.data)}catch(e){A(e)}}),o=(e,t)=>{let A=(0,a.useQueryClient)();return(0,a.useMutation)({mutationFn:A=>n(A,e,t),onSuccess:e=>{A.invalidateQueries("user")},onError:e=>{e.message=""}})},c=e=>(0,a.useQuery)("contact",async()=>await l(e)),d=e=>(0,a.useQuery)(["contact",e],async()=>await s(e))},77184:function(e,t,A){var a=A(57437),i=A(63993),r=A(98489),n=A(89414),l=A(89126),s=A(64393),o=A(77584),c=A(15735),d=A(81799),u=A(85860),m=A(11953),h=A(49651);A(25330);var p=A(34422),g=A(24086),b=A(49360),f=A(41774),v=A(2265),w=A(55788),y=A(50933),x=A(93770),Q=A(30100),U=A(75638),j=A(64821),B=A(23913),E=A(88415),C=A(62953),R=A(28397),k=A(40257);t.default=function(e){let t,A,{title:Z,country:N,defaultCountryPhone:F}=e,[L,O]=(0,v.useState)(""),[S,D]=(0,v.useState)(!1),[W,_]=(0,v.useState)(null),[J,I]=(0,v.useState)(""),{t:M}=(0,w.$G)(),H=h.PhoneNumberUtil.getInstance(),V=(0,y.uu)(D,O),T=(0,C.jd)(),P=new FormData,X=async(e,t)=>{let{resetForm:A}=t,a={...Object.fromEntries(Object.entries(e).filter(e=>{let[t,A]=e;return"acceptTerms"!==t&&""!==A&&null!=A}))};window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:`${N.toLowerCase()}_office_form`,button_id:"my_button"}),await V.mutateAsync({countryName:N,...a,to:`${k.env.NEXT_PUBLIC_EMAIL_FORM_DESTINATION}`,team:"Digital",type:"countryContact"}),A(),setTimeout(()=>{D(!1)},3e3)},q=(e,a)=>{e.preventDefault(),t=(0,b.Z)().replace(/-/g,""),I(""),_(null);let i=e.target.files[0];if(i){P.append("file",i);let e=i.name.split(".").pop();A=`${t}.${e}`;let r=new Date().getFullYear();T.mutate({resource:"candidates",folder:r,filename:t,body:{formData:P,t:M}},{onSuccess:e=>{"uuid exist"===e.message?(_(e.uuid),a("resume",e.uuid)):(_(A),a("resume",A))},onError:e=>{400===e.response.data.status?I(M("messages:requireResume")):500===e.response.data.status?I("Internal Server Error"):I(e.response.data.message)}})}},G=e=>{try{return H.isValidNumber(H.parseAndKeepRawInput(e))}catch(e){return!1}},Y=p.Z_().test("is-valid-phone",M("validations:phoneFormat"),e=>G(e)),K=e=>(0,x.eo)(e).shape({phone:Y,email:p.Z_().email(e("validations:invalidEmail")).required(e("validations:required")).test("is-company-email",e("validations:companyEmailRequired"),function(e){let{youAre:t}=this.parent;return!!e&&("Company"!==t||j.isCompanyEmail(e))})});return(0,a.jsxs)("div",{id:"service-page-form",children:[(0,a.jsx)(U.Z,{}),(0,a.jsxs)(r.default,{className:"custom-max-width",children:[(0,a.jsxs)("h2",{className:"heading-h1 text-white text-center",children:[Z||(0,a.jsxs)(a.Fragment,{children:[M("Tunisia:form:title1")," ",(0,a.jsx)("span",{className:"text-yellow",children:M("Tunisia:form:title2")})]})," "]}),(0,a.jsx)(i.J9,{initialValues:{fullName:"",email:"",phone:"",youAre:"",howToHelp:"",message:"",resume:"",field:"",acceptTerms:!1},validationSchema:()=>K(M),onSubmit:X,children:e=>{let{values:t,handleChange:A,setFieldValue:r,errors:h,touched:p}=e;return(0,a.jsx)(i.l0,{className:"pentabell-form",children:(0,a.jsxs)(n.default,{container:!0,rowSpacing:4,columnSpacing:3,children:[(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(l.Z,{className:"form-group light",children:[(0,a.jsxs)(s.Z,{className:"label-pentabell light",children:[M("aiSourcingService:servicePageForm:fullName"),"*"]}),(0,a.jsx)(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:M("aiSourcingService:servicePageForm:fullName"),variant:"standard",type:"text",name:"fullName",value:t.fullName,onChange:A,error:!!(h.fullName&&p.fullName)})]}),(0,a.jsx)(i.Bc,{name:"fullName",children:e=>(0,a.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(l.Z,{className:"form-group light",children:[(0,a.jsx)(s.Z,{className:"label-pentabell light",children:"Email*"}),(0,a.jsx)(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:"Email",variant:"standard",type:"email",name:"email",value:t.email,onChange:A,error:!!(h.email&&p.email)})]}),(0,a.jsx)(i.Bc,{name:"email",children:e=>(0,a.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:"Company"===t.youAre||"Consultant"===t.youAre?6:12,children:[(0,a.jsxs)(l.Z,{className:"form-group light",children:[(0,a.jsxs)(s.Z,{className:"label-pentabell light",children:[M("aiSourcingService:servicePageForm:youAre"),"*"]}),(0,a.jsx)(d.Z,{className:"input-pentabell light",id:"tags-standard",options:["Consultant","Company"],getOptionLabel:e=>e,name:"youAre",value:t.youAre,onChange:(e,t)=>r("youAre",t),renderInput:e=>(0,a.jsx)(o.Z,{...e,className:"input-pentabell multiple-select  light",variant:"standard",placeholder:M("aiSourcingService:servicePageForm:chooseOne"),error:!!(h.youAre&&p.youAre)})})]}),(0,a.jsx)(i.Bc,{name:"youAre",children:e=>(0,a.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),"Consultant"===t.youAre&&(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(l.Z,{className:"form-group light",children:[(0,a.jsxs)(s.Z,{className:"label-pentabell light",children:[M("consultingServices:servicePageForm:industry"),"*"]}),(0,a.jsx)(d.Z,{className:"input-pentabell light",id:"tags-standard",options:Object.values(R.b5),getOptionLabel:e=>e,name:"field",value:t.field,onChange:(e,t)=>{r("field",t)},renderInput:e=>(0,a.jsx)(o.Z,{...e,className:"input-pentabell multiple-select  light",variant:"standard",placeholder:M("consultingServices:servicePageForm:chooseOne"),error:!!(h.field&&p.field)})})]}),(0,a.jsx)(i.Bc,{name:"field",children:e=>(0,a.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),"Company"===t.youAre&&(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(l.Z,{className:"form-group light",children:[(0,a.jsx)(s.Z,{className:"label-pentabell light",children:M("payrollService:servicePageForm:companyName")}),(0,a.jsx)(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:M("payrollService:servicePageForm:companyName"),variant:"standard",type:"text",name:"companyName",value:t.companyName,onChange:A,error:!!(h.companyName&&p.companyName)})]}),(0,a.jsx)(i.Bc,{name:"companyName",children:e=>(0,a.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(l.Z,{className:"form-group light",children:[(0,a.jsx)(s.Z,{className:"label-pentabell light",children:M("register:phoneNumber")}),(0,a.jsx)(g.sb,{defaultCountry:F||"fr",className:"input-pentabell light",value:t.phone,onChange:e=>{r("phone",e),O("")},flagComponent:e=>(0,a.jsx)(E.Z,{...e})})]}),(0,a.jsx)(i.Bc,{name:"phone",children:e=>(0,a.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(l.Z,{className:"form-group light",children:[(0,a.jsxs)(s.Z,{className:"label-pentabell light",children:[M("Tunisia:form:howWeCanHelp"),"*"]}),(0,a.jsx)(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:M("Tunisia:form:howWeCanHelp"),variant:"standard",type:"text",name:"howToHelp",value:t.howToHelp,onChange:A,error:!!(h.howToHelp&&p.howToHelp)})]}),(0,a.jsx)(i.Bc,{name:"howToHelp",children:e=>(0,a.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:12,children:[(0,a.jsxs)(l.Z,{className:"form-group light",children:[(0,a.jsx)(s.Z,{className:"label-pentabell light",children:"Message"}),(0,a.jsx)(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:"Message",variant:"standard",type:"text",name:"message",value:t.message,onChange:A,error:!!(h.message&&p.message)})]}),(0,a.jsx)(i.Bc,{name:"message",children:e=>(0,a.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),"Consultant"===t.youAre&&(0,a.jsxs)(n.default,{item:!0,xs:12,sm:12,children:[(0,a.jsx)(l.Z,{className:"form-group light form-section",children:(0,a.jsxs)("div",{className:"custom-file-upload",onClick:()=>document.getElementById("file-upload").click(),children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(B.Z,{}),W?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(s.Z,{className:"label-pentabell light",children:[M("joinUs:form:uploadCv"),"*"]}),(0,a.jsx)("p",{className:"sub-label",children:W})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.Z,{className:"label-pentabell light",children:M("joinUs:form:uploadCv")}),(0,a.jsx)("p",{className:"sub-label",children:M("joinUs:form:control")})]}),(0,a.jsx)(f.default,{text:"Choose a file",className:"btn btn-outlined white"}),J&&(0,a.jsx)(c.Z,{variant:"filled",severity:"error",children:J})]}),(0,a.jsx)("input",{id:"file-upload",type:"file",name:"resume",accept:"application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword",style:{display:"none"},onChange:e=>{q(e,r)},error:!!(h.resume&&p.resume)})]})}),(0,a.jsx)(i.Bc,{name:"resume",children:e=>(0,a.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsx)(n.default,{item:!0,xs:12,sm:8,children:(0,a.jsxs)(l.Z,{children:[(0,a.jsx)(u.Z,{className:"checkbox-pentabell light",control:(0,a.jsx)(m.Z,{name:"acceptTerms",checked:t.acceptTerms,onChange:A,error:!!(h.acceptTerms&&p.acceptTerms)}),label:M("aiSourcingService:servicePageForm:formSubmissionAgreement")}),(0,a.jsx)(i.Bc,{name:"acceptTerms",children:e=>(0,a.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]})}),(0,a.jsx)(n.default,{item:!0,xs:12,sm:4,className:"flex-end",children:(0,a.jsx)(f.default,{text:M("aiSourcingService:servicePageForm:send"),className:"btn btn-filled btn-submit",type:"submit"})})]})})}}),(0,a.jsx)(Q.Z,{errMsg:L,success:S})]})]})}},62953:function(e,t,A){A.d(t,{$i:function(){return p},BF:function(){return h},Fe:function(){return n},Gc:function(){return c},HF:function(){return r},Hr:function(){return s},IZ:function(){return m},NF:function(){return o},PM:function(){return l},UJ:function(){return d},jd:function(){return u}});var a=A(86484),i=A(49443);A(99376),A(80657);let r=()=>(0,a.useMutation)({mutationFn:e=>(0,i.W3)(e),onError:e=>{e.message=""}}),n=e=>(0,a.useQuery)("opportunities",async()=>await (0,i.fH)(e)),l=()=>(0,a.useMutation)(()=>(0,i.AE)()),s=e=>(0,a.useQuery)(["opportunities",e],async()=>await (0,i.Mq)(e)),o=()=>(0,a.useMutation)({mutationFn:(e,t,A)=>(0,i.rE)(e,t,A),onError:e=>{e.message=""}}),c=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t)=>(0,i.S1)(e,t),onError:e=>{e.message=""}})),d=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t,A)=>(0,i.lU)(e,t,A),onError:e=>{e.message=""}})),u=()=>{let e=(0,a.useQueryClient)();return(0,a.useMutation)({mutationFn:(e,t,A,a)=>(0,i.yH)(e,t,A,a),onSuccess:t=>{e.invalidateQueries("files")}})},m=()=>(0,a.useQuery)("SeoOpportunities",async()=>await (0,i.yJ)()),h=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t)=>(0,i.mt)(e,t),onError:e=>{e.message=""}})),p=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:e=>{let{language:t,id:A,archive:a}=e;return(0,i.TK)(t,A,a)},onError:e=>{console.error("Error during mutation",e),e.message=""}}))},49443:function(e,t,A){A.d(t,{AE:function(){return o},Mq:function(){return s},S1:function(){return d},TK:function(){return p},W3:function(){return n},fH:function(){return l},lU:function(){return u},mt:function(){return g},rE:function(){return c},yH:function(){return m},yJ:function(){return h}});var a=A(46172),i=A(93214),r=A(7261);let n=e=>(e.t,new Promise(async(t,A)=>{i.yX.post(`/opportunities${a.Y.applications}/${e.opportunityId}`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&A(e)})})),l=e=>new Promise(async(t,A)=>{try{let A=await i.yX.get(`${a.Y.opportunity}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyWord:e.keyWord,visibility:e.visibility,title:e.title,createdAt:e.createdAt,publishDate:e.publishDate,country:e.country,industry:e.industry,contractType:e.contractType,minExperience:e.minExperience,maxExperience:e.maxExperience,jobDescriptionLanguages:e.jobDescriptionLanguages,reference:e.reference,opportunityType:e.opportunityType,exclude:"In House"!==e.opportunityType&&"true"}});t(A.data)}catch(e){A(e)}}),s=e=>new Promise(async(t,A)=>{try{let A=await i.yX.get(`${a.Y.opportunity}/${e}`);t(A.data)}catch(e){A(e)}}),o=async()=>(await i.xk.put("/UpdateJobdescription")).data,c=e=>{let{data:t,language:A,id:n}=e;return new Promise(async(e,l)=>{i.yX.post(`${a.Y.opportunity}/${A}/${n}`,t).then(t=>{"en"===A&&r.Am.success("Opportunity english updated successfully"),"fr"===A&&r.Am.success("Opportunity french updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&l(e)})})},d=e=>{let{data:t,id:A}=e;return new Promise(async(e,n)=>{i.yX.put(`${a.Y.opportunity}/${A}`,t).then(t=>{r.Am.success("Opportunity Commun fields updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&n(e)})})},u=e=>{let{id:t,title:A,typeOfFavourite:n}=e;return new Promise(async(e,l)=>{i.yX.put(`${a.Y.baseUrl}/favourite/${t}`,{type:n}).then(t=>{r.Am.success(`${n} : ${A} saved to your favorites.`),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data&&409===e.response.status&&r.Am.warning(` ${A} already in shortlist`),e&&l(e)})})},m=e=>{let{resource:t,folder:A,filename:n,body:l}=e;return new Promise(async(e,s)=>{i.cU.post(`${a.Y.files}/uploadResume/${t}/${A}/${n}`,l.formData).then(t=>{t.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data&&(400===e.response.status?e.response.data.message.includes("The resume lacks essential information")?r.Am.warn(l.t("messages:requireResume")):r.Am.warn(e.response.data.message):500===e.response.status&&r.Am.error("Internal Server Error")),e&&s(e)})})},h=()=>new Promise(async(e,t)=>{try{let t=await i.yX.get(`${a.Y.seoOpportunity}`);e(t.data)}catch(e){t(e)}}),p=(e,t,A)=>new Promise(async(n,l)=>{try{let l=await i.yX.put(`${a.Y.opportunity}/${e}/${t}/desarchiver`,{archive:A});l?.data&&(r.Am.success(`opportunity ${A?"archived":"desarchived"} successfully`),n(l.data))}catch(e){r.Am.error(`Failed to ${A?"archive":"desarchive"} the opportunity.`),l(e)}}),g=e=>{let{data:t,id:A}=e;return new Promise(async(e,n)=>{i.yX.put(`${a.Y.seoOpportunity}/${A}`,t).then(t=>{r.Am.success("Opportunity seo updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&n(e)})})}},46172:function(e,t,A){A.d(t,{Y:function(){return i},v:function(){return a}});let a=A(40257).env.NEXT_PUBLIC_BASE_API_URL,i={seo:"seoTags",auth:"/auth/signin",logout:"/auth/logout",candidatures:"/applications",signup:"/auth/signup",forgetPassword:"/auth/forgot-password",resetPassword:"/auth/reset-password",guides:"/guides",currentUser:"/users/current",updateUser:"/users",users:"/users",categoryGuides:"guidecategory",candidate:"/candidates",report:"/report",skills:"/skills",files:"/files",applications:"/applications",sliders:"/sliders",favoris:"/candidate/favourite",articles:"/articles",categories:"/categories",blog:"/blog",category:"/categories",opportunity:"/opportunities",seoOpportunity:"/seoOpportunity",newsletter:"/newsletter",contact:"/contact",favourite:"/favourite",contacts:"contacts",comments:"/comments",statistics:"/statistics",events:"/events",baseUrl:`${a}`}},93770:function(e,t,A){A.d(t,{BH:function(){return u},Bj:function(){return o},Ju:function(){return g},Ld:function(){return d},ZI:function(){return n},_Q:function(){return m},eo:function(){return h},ft:function(){return l},iP:function(){return s},ie:function(){return c},it:function(){return p},lg:function(){return r},oc:function(){return i}});var a=A(34422);let i=e=>a.Ry().shape({email:a.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")).matches(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,e("validations:invalidEmail"))}),r=e=>a.Ry().shape({password:a.Z_().matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).+$/,e("validations:invalidPassword")).required(e("validations:emptyField")),confirmPassword:a.Z_().oneOf([a.iH("password"),null],e("validations:passwordMatch")).required(e("validations:emptyField"))}),n=e=>a.Ry().shape({firstName:a.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),lastName:a.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:a.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),message:a.Z_().required(e("validations:emptyField")),acceptTerms:a.O7().oneOf([!0],e("validations:emptyField"))}),l=e=>a.Ry().shape({email:a.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),firstName:a.Z_().required(e("validations:emptyField")),lastName:a.Z_().required(e("validations:emptyField"))}),s=e=>a.Ry().shape({firstName:a.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),lastName:a.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:a.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),message:a.Z_().required(e("validations:emptyField")),acceptTerms:a.O7().oneOf([!0],e("validations:emptyField")),field:a.Z_().when("youAre",{is:e=>"Consultant"===e,then:t=>t.required(e("validations:emptyField")),otherwise:e=>e})}),o=e=>a.Ry().shape({firstName:a.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),lastName:a.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:a.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),enquirySelect:a.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),message:a.Z_().required(e("validations:emptyField")),acceptTerms:a.O7().oneOf([!0],e("validations:emptyField"))}),c=e=>a.Ry().shape({fullName:a.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),youAre:a.Z_().required(e("validations:emptyField")),message:a.Z_().required(e("validations:emptyField")),acceptTerms:a.O7().oneOf([!0],e("validations:emptyField")),field:a.Z_().when("youAre",{is:e=>"Consultant"===e,then:t=>t.required(e("validations:emptyField")),otherwise:e=>e})}),d=e=>a.Ry().shape({fullName:a.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:a.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),message:a.Z_().required(e("validations:emptyField")),acceptTerms:a.O7().oneOf([!0],e("validations:emptyField"))}),u=e=>a.Ry().shape({fullName:a.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:a.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),phone:a.Z_().min(8,e("validations:minLength")).max(13,e("validations:maxLength")).required(e("validations:emptyField")),field:a.Z_().required(e("validations:emptyField")),subject:a.Z_().required(e("validations:emptyField")),message:a.Z_().required(e("validations:emptyField")),resume:a.Z_().required(e("validations:emptyField")),mission:a.nK().oneOf([!0,!1],e("validations:emptyField")).required(e("validations:emptyField")),acceptTerms:a.O7().oneOf([!0],e("validations:emptyField"))}),m=e=>a.Ry().shape({fullName:a.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:a.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),message:a.Z_().required(e("validations:emptyField")),acceptTerms:a.O7().oneOf([!0],e("validations:emptyField"))}),h=e=>a.Ry().shape({fullName:a.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:a.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),message:a.Z_().required(e("validations:emptyField")),howToHelp:a.Z_().required(e("validations:emptyField")),youAre:a.Z_().required(e("validations:emptyField")),acceptTerms:a.O7().oneOf([!0],e("validations:emptyField")),field:a.Z_().when("youAre",{is:e=>"Consultant"===e,then:t=>t.required(e("validations:emptyField")),otherwise:e=>e})}),p=e=>a.Ry().shape({resume:a.Z_().required(e("validations:emptyField")),acceptTerms:a.O7().oneOf([!0],e("validations:emptyField"))}),g=(e,t)=>a.Ry().shape({resume:t?a.Z_():a.Z_().required(e("validations:emptyField")),acceptTerms:a.O7().oneOf([!0],e("validations:emptyField"))})},39907:function(e,t){t.Z={src:"/_next/static/media/logo-Alstom.96e3a690.webp",height:500,width:1080,blurDataURL:"data:image/webp;base64,UklGRooAAABXRUJQVlA4WAoAAAAQAAAABwAAAwAAQUxQSB8AAAABUBMAaEIVasDN/HX/1z8CHSIiQRG+m/zu1XyWbAMoAFZQOCBEAAAAcAIAnQEqCAAEAAJAOCWoAnRHAGSAfoAClPtaAAD+9YJy15CaQOGj9/KHYF1j/oh+cefWN3ySPlxSPv/DZ/qUj786gAA=",blurWidth:8,blurHeight:4}},46923:function(e,t){t.Z={src:"/_next/static/media/logo-Total.1cb006be.webp",height:66,width:266,blurDataURL:"data:image/webp;base64,UklGRmYAAABXRUJQVlA4WAoAAAAQAAAABwAAAQAAQUxQSBEAAAAAS3snIy4eJRwUOBEkJyMyKABWUDggLgAAALABAJ0BKggAAgACQDgloAJ0AQ7+AuwA/vsHm5fDaEAbIdTU1+RM+dvoGWwAAAA=",blurWidth:8,blurHeight:2}},64485:function(e,t){t.Z={src:"/_next/static/media/logo-ge.d2a815e8.webp",height:1080,width:1080,blurDataURL:"data:image/webp;base64,UklGRqwAAABXRUJQVlA4WAoAAAAQAAAABwAABwAAQUxQSEEAAAAAAAAAAAAAAAAACFl3Yy4AAABY7cLL0DkAEI+acmzxfQAWn8aJhsVVAACKuNerwzUAABJmhLNfBAAAAAANBwAAAABWUDggRAAAAFACAJ0BKggACAACQDgliAJ0bX8PFbF/+hocAAD++4+mkjXmujhKO1bzhEjqhn49ykeoRbXl0Dey/aAbwdtIbbteAAAA",blurWidth:8,blurHeight:8}},57644:function(e,t){t.Z={src:"/_next/static/media/logo-navalgrp.976309f5.webp",height:66,width:151,blurDataURL:"data:image/webp;base64,UklGRnIAAABXRUJQVlA4WAoAAAAQAAAABwAAAgAAQUxQSBkAAAAARCwxMDMzGhN9bHtTRYNiUwAALERBJgAAAFZQOCAyAAAAsAEAnQEqCAADAAJAOCUAToAh38BxwAD+/E3wnaXlLFM4P4a9ZC/xB59D7esUYTgHAAA=",blurWidth:8,blurHeight:3}},44511:function(e,t){t.Z={src:"/_next/static/media/logo-tecnimont.5b4a1d59.webp",height:66,width:220,blurDataURL:"data:image/webp;base64,UklGRloAAABXRUJQVlA4WAoAAAAQAAAABwAAAQAAQUxQSBEAAAAALDEtKTc2My4SDQwUEQ8TDABWUDggIgAAAJABAJ0BKggAAgACQDglpAAC50WsAAD++NI6t04jswc4AAA=",blurWidth:8,blurHeight:2}},87448:function(e,t){t.Z={src:"/_next/static/media/logo-tractebel.702539df.webp",height:500,width:1080,blurDataURL:"data:image/webp;base64,UklGRqgAAABXRUJQVlA4WAoAAAAQAAAABwAAAwAAQUxQSCEAAAAAAAAAAAAAAABXYkNwbiwAAAAAAAAANU4bAAAAAAAAAAAAVlA4IGAAAACQAgCdASoIAAQAAkA4JbACdLoAyv/cAN5VBXH2KAD+9q9w43z88H5cV19Cpb/Hlcp/v4/0xFb0Oev/yjo99fCzG/Nu/9/XiP2so57QdKz/6NJ8O/pEh+2f/B1yj/KyAAA=",blurWidth:8,blurHeight:4}},16757:function(e,t){t.Z={src:"/_next/static/media/logo-visa.a8cc47be.webp",height:65,width:81,blurDataURL:"data:image/webp;base64,UklGRo4AAABXRUJQVlA4WAoAAAAQAAAABwAABQAAQUxQSC0AAAABYBvZtpKX/1q8AHciarKQ+EPm0ihDCxGR8BHEedlijNa6valOcDfduKwz/AIAVlA4IDoAAADwAQCdASoIAAYAAkA4JYgCdEf/gedjrAAA/vqSgfts7/g9tTpn4zehQF/U38+M2ro/UZt0hPfj8QAA",blurWidth:8,blurHeight:6}},41182:function(e,t,A){A.r(t),t.default={src:"/_next/static/media/recrutement1.f40c9bbb.png",height:487,width:578,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAUVBMVEVDMywqKyh0YlFEQz8iIyI9PTmBeW83My9nWkwtMjJtYldbS0CWh3hFPzdZVU6nm49eYF+/qJGujnBLTk1ENSpTRDUUFRMFCg0MERQ6LSN2bWNyzueEAAAAAXRSTlP+GuMHfQAAAAlwSFlzAAALEwAACxMBAJqcGAAAADtJREFUeJwFwYcBgCAQBLADvlPtuv+iJhAxS4kZsu1ZtRREy1W1MiLORHQ4zOh51R39KmsN6sB3+5iz/TkPAiZNSXWMAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:7}},24385:function(e,t,A){A.r(t),t.default={src:"/_next/static/media/recrutement2.ea7e7e66.png",height:487,width:578,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAIVBMVEUACx0EHzgABhYCGCsEK0cDM04wb54OPl0KVHMaXIMlhq1qUshrAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAMklEQVR4nBXKQRLAMAgDsbUNgeT/D+5UZ4EAJKo024HAvlHoVb/bRWKdc/+I55SxIcIfFbAAsD97IbgAAAAASUVORK5CYII=",blurWidth:8,blurHeight:7}},96609:function(e,t,A){A.r(t),t.default={src:"/_next/static/media/recrutement3.6ad4b886.png",height:487,width:578,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAS1BMVEWopqglJCKDd27MzMvZ2d7EwcMXGBa7ubyKfnZmYl2TjoiTkY9sWUu2saq2tLZ4cG1GREJXVlXPzNhRPSmmoJ8mHxafmJaLfGzJyMYfaOyaAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPUlEQVR4nB3G2xKAIAgFwCOCgHdL6///tJn2acGCrJEZ10bRRhF6W+laBTh1OOzBSmG5DcJMwbPR+6ehywc65QHwEQieoQAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:7}},47185:function(e,t,A){A.r(t),t.default={src:"/_next/static/media/recrutement4.a8254f52.png",height:487,width:578,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAATlBMVEXZ29rQ0M+dk5HDvrgoJi7h5uqkoJ+6tbHKxLtaVFfo6eNkXVpIRk+snZBzb205OT56eXqsq6u/xM/Aydu/rJqoqqGijXw2NTl/g4JPTlRv9lEiAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAO0lEQVR4nAXBBwKAMAgEsGMV6HTr/19qAoogAxVEG9UqCmRcAgTD+fHDOOFr4ty7Qtb0ktrh73dvqe0HMgoB6Bd+yCcAAAAASUVORK5CYII=",blurWidth:8,blurHeight:7}},34069:function(e,t,A){A.r(t),t.default={src:"/_next/static/media/service1.93923315.png",height:488,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAeFBMVEVdPSB5hYBpc3I3ODehfloNDg8ICAoiJSe3k2zw4MxVWVlxUTyMmJ43MSn9+e8UCwU7QUdqeINza1iBiIVYY2qTiXqrtrV8iZJkUj59a1V6ShsBAwhaTETL294dGA9gbnhPJgF4XUilbTLStJHk1LphTz1KPjJMVFp50xqjAAAAD3RSTlP+8fHx/vDx8f798f7+/f1EbXElAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAR0lEQVR4nAXBBQKAIAAEsFNRwKLT7v//0A0gfOQMAx5yX2dOGm9ieY9O4wtHNGYVUJvxi9czpLKhdx0wSWWlEAK0asuCNvUPsB0EgzCapeEAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8}}}]);