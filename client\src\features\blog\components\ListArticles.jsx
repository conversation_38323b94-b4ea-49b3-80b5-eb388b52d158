import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { DataGrid } from "@mui/x-data-grid";
import { Grid } from "@mui/material";
import CustomTooltip from "@/components/ui/CustomTooltip";
import Loading from "../../../components/loading/Loading";
import {
  useGetArticlesDashboard,
  useGetCategories,
  usearchivedarticle,
  useDeleteArticle,
} from "../hooks/blog.hook";
import Svgpreview from "@/assets/images/icons/preview-icon.svg";
import Svgedit from "@/assets/images/icons/edit-icon.svg";
import CustomButton from "@/components/ui/CustomButton";
import SvgApplicationIcon from "@/assets/images/icons/applicationIcon.svg";
import {
  useTheme,
  useMediaQuery,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@mui/material";
import {
  adminRoutes,
  baseUrlBackoffice,
  websiteRoutesList,
} from "@/helpers/routesList";
import { formatDate } from "@/utils/functions";
import CustomFilters from "@/components/ui/CustomFilters";

const ListArticles = ({ language }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { t } = useTranslation();
  
  const [category, setCategory] = useState("");
  const [sortOrder, setSortOrder] = useState("");
  const [createdAt, setCreatedAt] = useState(null);
  const [publishDate, setPublishDate] = useState(null);
  const [search, setSearch] = useState(false);
  
  const savedVisibility = localStorage.getItem("Visibility");
  
  const [visibility, setVisibility] = useState(savedVisibility || "");
  const [isArchived, setIsArchivedFilter] = useState(false); // Default to show non-archived articles
  
  const savedPagination = localStorage.getItem("PAGINATION_KEY");
  const savedSeachValue = localStorage.getItem("SearchValue");
  
  const [searchQuery, setSearchQuery] = useState(savedSeachValue || "");
  const [paginationModel, setPaginationModel] = React.useState(
    savedPagination
      ? JSON.parse(savedPagination)
      : {
          page: 0,
          pageSize: 10,
        }
  );

  const isOpen = true;
  const [selectedLanguage, setSelectedLanguage] = useState(
    language ? language : "en"
  );

  const [openArchiveDialog, setOpenArchiveDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedArticle, setSelectedArticle] = useState(null);

  const archiveArticleMutation = usearchivedarticle();
  const deleteArticleMutation = useDeleteArticle();
  
  const handleArchiveClick = (article) => {
    setSelectedArticle(article);
    setOpenArchiveDialog(true);
  };

  const handleDeleteClick = (article) => {
    setSelectedArticle(article);
    setOpenDeleteDialog(true);
  };

  const handleArchiveConfirm = async () => {
    if (selectedArticle) {
      try {
        await archiveArticleMutation.mutateAsync({
          language: selectedLanguage,
          id: selectedArticle.id,
          archive: !selectedArticle.isArchived,
        });
        getArticles.refetch();
      } catch (error) {
        console.error("Error archiving article:", error);
      }
    }
    setOpenArchiveDialog(false);
    setSelectedArticle(null);
  };

  const handleDeleteConfirm = async () => {
    if (selectedArticle) {
      try {
        await deleteArticleMutation.mutateAsync({
          language: selectedLanguage,
          id: selectedArticle.id,
        });
        getArticles.refetch();
      } catch (error) {
        console.error("Error deleting article:", error);
      }
    }
    setOpenDeleteDialog(false);
    setSelectedArticle(null);
  };

  const handleDialogClose = () => {
    setOpenArchiveDialog(false);
    setOpenDeleteDialog(false);
    setSelectedArticle(null);
  };

  const resetSearch = () => {
    setCategory("");
    setSearchQuery("");
    setVisibility("");
    setSortOrder("");
    setCreatedAt(null);
    setPublishDate(null);
    setSelectedLanguage(language ? language : "en");
    setPaginationModel({ page: 0, pageSize: 10 });
    setIsArchivedFilter(false); // Reset to show non-archived articles
    setSearch(!search);
    localStorage.setItem("Visibility", "");
    localStorage.setItem("SearchValue", "");
    localStorage.setItem(
      "PAGINATION_KEY",
      JSON.stringify({
        page: 0,
        pageSize: 10,
      })
    );
  };

  const truncateTitle = (title) => {
    const words = title.split(" ");
    if (words?.length > 4) {
      return words.slice(0, 4).join(" ") + "...";
    } else {
      return title;
    }
  };

  const getCategoriesLang = useGetCategories(selectedLanguage || "en");

  const transformedCategoriesLang =
    getCategoriesLang?.data?.categories?.map((category) => ({
      name: category?.versionscategory[0]?.name,
      value: category?.versionscategory[0]?.name,
      label: category?.versionscategory[0]?.name,
    })) || [];

  const getArticles = useGetArticlesDashboard({
    language: selectedLanguage,
    pageSize: paginationModel.pageSize,
    pageNumber: paginationModel.page + 1,
    sortOrder,
    searchQuery,
    visibility,
    createdAt,
    isArchived,
    publishDate,
    categoryName: category,
  });

  useEffect(() => {
    setSelectedLanguage(language);
    getCategoriesLang.refetch();
  }, [language]);

  useEffect(() => {
    getArticles.refetch();
  }, [search, paginationModel]);

  const visibilityOption = [
    { value: "Public", label: "Public" },
    { value: "Private", label: "Private" },
    { value: "Draft", label: "Draft" },
  ];

  const handlePaginationChange = (newPaginationModel) => {
    setPaginationModel(newPaginationModel);
    localStorage.setItem("PAGINATION_KEY", JSON.stringify(newPaginationModel));
  };

  if (getArticles.isLoading) {
    return <Loading />;
  }

  const rows =
    getArticles?.data?.articles?.map((item) => ({
      id: item._id,
      title: item?.versions?.[0]?.title
        ? truncateTitle(item?.versions?.[0]?.title)
        : "No title",
      createdBy: item?.versions?.[0]?.createdBy || "N/A",
      createdAt: item?.versions?.[0]?.createdAt,
      language: item?.existingLanguages?.join("/") || "N/A",
      actions: item._id,
      visibility: item?.versions?.[0]?.visibility || "N/A",
      url: item?.versions?.[0]?.url || "N/A",
      totalCommentaires: item?.totalCommentaires || "0",
      isArchived: item?.versions?.[0]?.isArchived || false,
    })) || [];

  const columns = [
    {
      field: "title",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell",
      headerName: t("listArticle:title"),

      flex: 1,
      renderCell: (params) => (
        <a
          href={`/${selectedLanguage}/blog/${params.row?.url}`}
          className="link"
        >
          {params.row.title}
        </a>
      ),
    },
    {
      field: "createdBy",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell ",
      headerName: t("listArticle:createdBy"),
      flex: 0.4,
    },
    {
      field: "createdAt",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell ",
      flex: 0.4,
      headerName: t("listArticle:createdAt"),
      valueFormatter: formatDate,
    },
    {
      field: "language",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell ",
      flex: 0.4,
      headerName: t("listopportunity:availablelanguage"),
    },
    {
      field: "visibility",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell",
      flex: 0.4,
      headerName: t("listArticle:visibility"),
    },
    {
      field: "isArchived",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell",
      flex: 0.4,
      headerName: t("listArticle:archived"),
      renderCell: (params) => (
        <p>{params.row.isArchived ? t("global:yes") : t("global:no")}</p>
      ),
    },
    {
      field: "totalCommentaires",
      headerClassName: "datagrid-header",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell ",
      headerName: t("listArticle:nbOfComments"),
      flex: 0.4,
    },

    {
      field: "actions",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: "",
      renderCell: (params) => (
        <div
          className="action-buttons"
          style={{ gridColumn: "span 2", width: "100%" }}
        >
          <CustomTooltip
            child={
              <div>
                <CustomButton
                  icon={<Svgedit />}
                  leftIcon={true}
                  link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/${adminRoutes.edit.route}/${params.row.id}`}
                  className="btn btn-ghost edit-blog"
                />
              </div>
            }
            title={t("global:edit")}
          />

          <CustomTooltip
            child={
              <div className="btn-download">
                <CustomButton
                  icon={<SvgApplicationIcon />}
                  leftIcon={true}
                  link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/${adminRoutes.comments.route}/${params.row.id}`}
                  className="btn btn-ghost edit-blog"
                />
              </div>
            }
            title={t("global:comments")}
          />

          <CustomTooltip
            child={
              <div>
                <CustomButton
                  icon={<Svgpreview />}
                  leftIcon={true}
                  link={`/${websiteRoutesList.blog.route}/${params.row?.url}`}
                  className="btn btn-ghost edit-blog"
                />
              </div>
            }
            title={t("global:preview")}
          />

          <CustomTooltip
            child={
              <div>
                <CustomButton
                  icon={
                    <svg
                      width="22"
                      height="20"
                      viewBox="0 0 22 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M21 17C21 17.5304 20.7893 18.0391 20.4142 18.4142C20.0391 18.7893 19.5304 19 19 19H3C2.46957 19 1.96086 18.7893 1.58579 18.4142C1.21071 18.0391 1 17.5304 1 17V3C1 2.46957 1.21071 1.96086 1.58579 1.58579C1.96086 1.21071 2.46957 1 3 1H8L10 4H19C19.5304 4 20.0391 4.21071 20.4142 4.58579C20.7893 4.96086 21 5.46957 21 6V17Z"
                        stroke="#1E1E1E"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  }
                  leftIcon={true}
                  onClick={() => handleArchiveClick(params.row)}
                  className="btn btn-ghost edit-blog"
                />
              </div>
            }
            title={
              params.row.isArchived
                ? t("global:unarchive")
                : t("global:archive")
            }
          />

          <CustomTooltip
            child={
              <div>
                <CustomButton
                  icon={
                    <svg
                      width="20"
                      height="22"
                      viewBox="0 0 20 22"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M1 5H3M3 5H19M3 5V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H15C15.5304 21 16.0391 20.7893 16.4142 20.4142C16.7893 20.0391 17 19.5304 17 19V5M6 5V3C6 2.46957 6.21071 1.96086 6.58579 1.58579C6.96086 1.21071 7.46957 1 8 1H12C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V5M8 10V16M12 10V16"
                        stroke="#1E1E1E"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  }
                  leftIcon={true}
                  onClick={() => handleDeleteClick(params.row)}
                  className="btn btn-ghost edit-blog"
                />
              </div>
            }
            title={t("global:delete")}
          />
        </div>
      ),
      flex: 1,
    },
  ];

  const archivedOptions = [
    { value: true, label: "Archived" },
    { value: false, label: "Not Archived" },
  ];

  const filters = [
    {
      type: "text",
      label: "Search By Title",
      value: searchQuery,
      onChange: (e) => setSearchQuery(e.target.value),
      placeholder: "Search",
      condition: true,
    },
    {
      type: "select",
      label: t("listArticle:visibility"),
      value: visibility
        ? {
            value: visibility,
            label:
              visibilityOption.find((opt) => opt.value === visibility)?.label ||
              visibility,
          }
        : null,
      onChange: (_, val) => setVisibility(val?.value || ""),
      options: visibilityOption,
      condition: true,
    },
    {
      type: "select",
      label: t("listArticle:archivage"),
      value: isArchived
        ? {
            value: isArchived,
            label:
              archivedOptions.find((opt) => opt.value === isArchived)?.label ||
              isArchived,
          }
        : null,
      onChange: (_, val) => setIsArchivedFilter(val?.value || ""),
      options: archivedOptions,
      condition: true,
    },
    {
      type: "select",
      label: t("global:sort"),
      value: sortOrder
        ? {
            value: sortOrder,
            label: t(sortOrder === "desc" ? "global:newest" : "global:oldest"),
          }
        : null,
      onChange: (_, val) => setSortOrder(val?.value || ""),
      options: [
        { value: "desc", label: t("global:newest") },
        { value: "asc", label: t("global:oldest") },
      ],
      condition: true,
    },
    {
      type: "select",
      label: t("listArticle:category"),
      value: category
        ? {
            value: category,
            label:
              transformedCategoriesLang.find((c) => c.value === category)
                ?.label || category,
          }
        : null,
      onChange: (_, val) => setCategory(val?.value || ""),
      options: transformedCategoriesLang,
      condition: true,
    },
    {
      type: "date",
      label: t("listArticle:createdAt"),
      value: createdAt,
      onChange: (newValue) => setCreatedAt(newValue),
      condition: true,
    },
    {
      type: "date",
      label: t("listArticle:publishDate"),
      value: publishDate,
      onChange: (newValue) => setPublishDate(newValue),
      condition: true,
    },
  ];

  const handleSearch = () => {
    localStorage.setItem("SearchValue", searchQuery);
    localStorage.setItem("Visibility", visibility);
    setPaginationModel({ page: 0, pageSize: paginationModel.pageSize });
    setSearch((prev) => !prev);
  };

  return (
    <>
      <div className="display-inline">
        <p className="heading-h2 semi-bold">
          {t("listArticle:listOfArticles")}
          <span className="opportunities-nbr">
            {getArticles?.data?.totalArticles}
          </span>
        </p>

        <CustomButton
          className="btn btn-filled"
          text={t("global:addarticle")}
          link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/${adminRoutes.add.route}`}
        />
      </div>

      <div id="container" className="recent-application-pentabell">
        <div className={`main-content ${isOpen ? "open" : "closed"}`}>
          <div className="table-Grid">
            <Grid item xs={12}>
              <CustomFilters
                filters={filters}
                onSearch={handleSearch}
                onReset={resetSearch}
                searchLabel={t("global:search")}
              />
            </Grid>
          </div>

          <Grid item xs={12}>
            <div style={{ height: "100%", width: "100%" }}>
              <DataGrid
                rows={rows}
                columns={columns}
                pagination
                className="pentabell-table"
                paginationMode="server"
                paginationModel={paginationModel}
                onPaginationModelChange={handlePaginationChange}
                pageSizeOptions={[5, 10, 25]}
                rowCount={getArticles?.data?.totalArticles || 0}
                autoHeight
                disableSelectionOnClick
                columnVisibilityModel={{
                  createdBy: !isMobile,
                  createdAt: !isMobile,
                  totalCommentaires: !isMobile,
                  visibility: !isMobile,
                  language: !isMobile,
                  archived: !isMobile,
                }}
              />
            </div>
          </Grid>
        </div>
      </div>

      {/* Archive Confirmation Dialog */}
      <Dialog
        id="toggle"
        open={openArchiveDialog}
        onClose={handleDialogClose}
        aria-labelledby="archive-dialog-title"
        className="dialog-paper"
        sx={{
          "& .MuiPaper-root": {
            background: "linear-gradient(#0b3051 0%, #234791 100%) !important",
            color: "#f8f8f8 !important",
            borderBottom: "transparent !important",
            borderRadius: "0px !important",
            boxShadow: "transparent !important",
          },
        }}
      >
        <DialogTitle sx={{ m: 0, p: 2 }} id="archive-dialog-title">
          {selectedArticle?.isArchived
            ? t("global:unarchive")
            : t("global:archive")}{" "}
          {t("global:article")}
          <button
            aria-label="close"
            onClick={handleDialogClose}
            style={{
              position: "absolute",
              right: 8,
              top: 8,
              background: "transparent",
              border: "none",
              color: "#f8f8f8",
              cursor: "pointer",
              fontSize: "20px",
            }}
          >
            ×
          </button>
        </DialogTitle>
        <DialogContent dividers className="dialog-content">
          <div style={{ textAlign: "center", marginBottom: "16px" }}>
            <svg
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M21 17C21 17.5304 20.7893 18.0391 20.4142 18.4142C20.0391 18.7893 19.5304 19 19 19H3C2.46957 19 1.96086 18.7893 1.58579 18.4142C1.21071 18.0391 1 17.5304 1 17V3C1 2.46957 1.21071 1.96086 1.58579 1.58579C1.96086 1.21071 2.46957 1 3 1H8L10 4H19C19.5304 4 20.0391 4.21071 20.4142 4.58579C20.7893 4.96086 21 5.46957 21 6V17Z"
                stroke="#ffca00"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <p style={{ textAlign: "center", margin: 0, fontSize: "16px" }}>
            {selectedArticle?.isArchived
              ? t("global:confirmUnarchiveArticle")
              : t("global:confirmArchiveArticle")}
          </p>
        </DialogContent>
        <DialogActions className="dialog-actions">
          <CustomButton
            text={
              selectedArticle?.isArchived
                ? t("global:unarchive")
                : t("global:archive")
            }
            className="btn-popup"
            leftIcon={true}
            onClick={handleArchiveConfirm}
          />
          <CustomButton
            text={t("global:cancel")}
            leftIcon={true}
            className="btn-outlined-popup"
            onClick={handleDialogClose}
          />
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        id="toggle"
        open={openDeleteDialog}
        onClose={handleDialogClose}
        aria-labelledby="delete-dialog-title"
        className="dialog-paper"
        sx={{
          "& .MuiPaper-root": {
            background: "linear-gradient(#0b3051 0%, #234791 100%) !important",
            color: "#f8f8f8 !important",
            borderBottom: "transparent !important",
            borderRadius: "0px !important",
            boxShadow: "transparent !important",
          },
        }}
      >
        <DialogTitle sx={{ m: 0, p: 2 }} id="delete-dialog-title">
          {t("global:delete")} {t("global:article")}
          <button
            aria-label="close"
            onClick={handleDialogClose}
            style={{
              position: "absolute",
              right: 8,
              top: 8,
              background: "transparent",
              border: "none",
              color: "#f8f8f8",
              cursor: "pointer",
              fontSize: "20px",
            }}
          >
            ×
          </button>
        </DialogTitle>
        <DialogContent dividers className="dialog-content">
          <div style={{ textAlign: "center", marginBottom: "16px" }}>
            <svg
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M3 6H5H21M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19ZM10 11V17M14 11V17"
                stroke="#ff4444"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <p style={{ textAlign: "center", margin: 0, fontSize: "16px" }}>
            {t("global:confirmDeleteArticle")}
          </p>
        </DialogContent>
        <DialogActions className="dialog-actions">
          <CustomButton
            text={t("global:delete")}
            className="btn-popup"
            leftIcon={true}
            onClick={handleDeleteConfirm}
          />
          <CustomButton
            text={t("global:cancel")}
            leftIcon={true}
            className="btn-outlined-popup"
            onClick={handleDialogClose}
          />
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ListArticles;
