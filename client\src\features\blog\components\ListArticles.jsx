import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { DataGrid } from "@mui/x-data-grid";
import { Grid } from "@mui/material";
import CustomTooltip from "@/components/ui/CustomTooltip";
import Loading from "../../../components/loading/Loading";
import { useGetArticlesDashboard, useGetCategories } from "../hooks/blog.hook";
import Svgpreview from "@/assets/images/icons/preview-icon.svg";
import Svgedit from "@/assets/images/icons/edit-icon.svg";
import CustomButton from "@/components/ui/CustomButton";
import SvgApplicationIcon from "@/assets/images/icons/applicationIcon.svg";
import { useTheme, useMediaQuery } from "@mui/material";
import {
  adminRoutes,
  baseUrlBackoffice,
  websiteRoutesList,
} from "@/helpers/routesList";
import { formatDate } from "@/utils/functions";
import CustomFilters from "@/components/ui/CustomFilters";

const ListArticles = ({ language }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { t } = useTranslation();
  const [category, setCategory] = useState("");
  const [sortOrder, setSortOrder] = useState("");
  const [createdAt, setCreatedAt] = useState(null);
  const [publishDate, setPublishDate] = useState(null);
  const [search, setSearch] = useState(false);
  const savedVisibility = localStorage.getItem("Visibility");
  const [visibility, setVisibility] = useState(savedVisibility || "");
  const [isArchived, setIsArchivedFilter] = useState();
  const savedPagination = localStorage.getItem("PAGINATION_KEY");
  const savedSeachValue = localStorage.getItem("SearchValue");
  const [searchQuery, setSearchQuery] = useState(savedSeachValue || "");
  const [paginationModel, setPaginationModel] = React.useState(
    savedPagination
      ? JSON.parse(savedPagination)
      : {
          page: 0,
          pageSize: 10,
        }
  );

  const isOpen = true;
  const [selectedLanguage, setSelectedLanguage] = useState(
    language ? language : "en"
  );
  const resetSearch = () => {
    setCategory("");
    setSearchQuery("");
    setVisibility("");
    setSortOrder("");
    setCreatedAt(null);
    setPublishDate(null);
    setSelectedLanguage(language ? language : "en");
    setPaginationModel({ page: 0, pageSize: 10 });
    setIsArchivedFilter("");
    setSearch(!search);
    localStorage.setItem("Visibility", "");
    localStorage.setItem("SearchValue", "");
    localStorage.setItem(
      "PAGINATION_KEY",
      JSON.stringify({
        page: 0,
        pageSize: 10,
      })
    );
  };

  const truncateTitle = (title) => {
    const words = title.split(" ");
    if (words?.length > 4) {
      return words.slice(0, 4).join(" ") + "...";
    } else {
      return title;
    }
  };

  const getCategoriesLang = useGetCategories(selectedLanguage || "en");

  const transformedCategoriesLang =
    getCategoriesLang?.data?.categories?.map((category) => ({
      name: category?.versionscategory[0]?.name,
      value: category?.versionscategory[0]?.name,
      label: category?.versionscategory[0]?.name,
    })) || [];

  const getArticles = useGetArticlesDashboard({
    language: selectedLanguage,
    pageSize: paginationModel.pageSize,
    pageNumber: paginationModel.page + 1,
    sortOrder,
    searchQuery,
    visibility,
    createdAt,
    isArchived,
    publishDate,
    categoryName: category,
  });

  useEffect(() => {
    setSelectedLanguage(language);
    getCategoriesLang.refetch();
  }, [language]);

  useEffect(() => {
    getArticles.refetch();
  }, [search, paginationModel]);

  const visibilityOption = [
    { value: "Public", label: "Public" },
    { value: "Private", label: "Private" },
    { value: "Draft", label: "Draft" },
  ];

  const handlePaginationChange = (newPaginationModel) => {
    setPaginationModel(newPaginationModel);
    localStorage.setItem("PAGINATION_KEY", JSON.stringify(newPaginationModel));
  };

  if (getArticles.isLoading) {
    return <Loading />;
  }

  const rows =
    getArticles?.data?.articles?.map((item, index) => ({
      id: item._id,
      title: item?.versions?.[0]?.title
        ? truncateTitle(item?.versions?.[0]?.title)
        : "No title",
      createdBy: item?.versions[0].createdBy || "N/A",
      createdAt: item?.versions[0].createdAt,
      language: item?.existingLanguages?.join("/") || "N/A",
      actions: item._id,
      visibility: item?.versions?.[0]?.visibility || "N/A",
      url: item?.versions?.[0]?.url || "N/A",
      totalCommentaires: item?.totalCommentaires || "0",
      isArchived: item?.versions[0].isArchived,
    })) || [];

  const columns = [
    {
      field: "title",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell",
      headerName: t("listArticle:title"),

      flex: 1,
      renderCell: (params) => (
        <a
          href={`/${selectedLanguage}/blog/${params.row?.url}`}
          className="link"
        >
          {params.row.title}
        </a>
      ),
    },
    {
      field: "createdBy",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell ",
      headerName: t("listArticle:createdBy"),
      flex: 0.4,
    },
    {
      field: "createdAt",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell ",
      flex: 0.4,
      headerName: t("listArticle:createdAt"),
      valueFormatter: formatDate,
    },
    {
      field: "language",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell ",
      flex: 0.4,
      headerName: t("listopportunity:availablelanguage"),
    },
    {
      field: "visibility",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell",
      flex: 0.4,
      headerName: t("listArticle:visibility"),
    },
    {
      field: "isArchived",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell",
      flex: 0.4,
      headerName: t("listArticle:archived"),
      renderCell: (params) => (
        <p>{params.row.isArchived ? t("global:yes") : t("global:no")}</p>
      ),
    },
    {
      field: "totalCommentaires",
      headerClassName: "datagrid-header",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell ",
      headerName: t("listArticle:nbOfComments"),
      flex: 0.4,
    },

    {
      field: "actions",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: "",
      renderCell: (params) => (
        <div
          className="action-buttons"
          style={{ gridColumn: "span 2", width: "100%" }}
        >
          <CustomTooltip
            child={
              <div>
                <CustomButton
                  icon={<Svgedit />}
                  leftIcon={true}
                  link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/${adminRoutes.edit.route}/${params.row.id}`}
                  className="btn btn-ghost edit-blog"
                />
              </div>
            }
            title={t("global:edit")}
          />

          <CustomTooltip
            child={
              <div className="btn-download">
                <CustomButton
                  icon={<SvgApplicationIcon />}
                  leftIcon={true}
                  link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/${adminRoutes.comments.route}/${params.row.id}`}
                  className="btn btn-ghost edit-blog"
                />
              </div>
            }
            title={t("global:comments")}
          />

          <CustomTooltip
            child={
              <div>
                <CustomButton
                  icon={<Svgpreview />}
                  leftIcon={true}
                  link={`/${websiteRoutesList.blog.route}/${params.row?.url}`}
                  className="btn btn-ghost edit-blog"
                />
              </div>
            }
            title={t("global:preview")}
          />
        </div>
      ),
      flex: 1,
    },
  ];
  const archivedOptions = [
    { value: true, label: "Archived" },
    { value: false, label: "Not Archived" },
  ];

  const filters = [
    {
      type: "text",
      label: "Search By Title",
      value: searchQuery,
      onChange: (e) => setSearchQuery(e.target.value),
      placeholder: "Search",
      condition: true,
    },

    {
      type: "select",
      label: t("listArticle:visibility"),
      value: visibility
        ? {
            value: visibility,
            label:
              visibilityOption.find((opt) => opt.value === visibility)?.label ||
              visibility,
          }
        : null,
      onChange: (e, val) => setVisibility(val?.value || ""),
      options: visibilityOption,
      condition: true,
    },
    {
      type: "select",
      label: t("listArticle:archivage"),
      value: isArchived
        ? {
            value: isArchived,
            label:
              archivedOptions.find((opt) => opt.value === isArchived)?.label ||
              isArchived,
          }
        : null,
      onChange: (e, val) => setIsArchivedFilter(val?.value || ""),
      options: archivedOptions,
      condition: true,
    },
    {
      type: "select",
      label: t("global:sort"),
      value: sortOrder
        ? {
            value: sortOrder,
            label: t(sortOrder === "desc" ? "global:newest" : "global:oldest"),
          }
        : null,
      onChange: (e, val) => setSortOrder(val?.value || ""),
      options: [
        { value: "desc", label: t("global:newest") },
        { value: "asc", label: t("global:oldest") },
      ],
      condition: true,
    },
    {
      type: "select",
      label: t("listArticle:category"),
      value: category
        ? {
            value: category,
            label:
              transformedCategoriesLang.find((c) => c.value === category)
                ?.label || category,
          }
        : null,
      onChange: (e, val) => setCategory(val?.value || ""),
      options: transformedCategoriesLang,
      condition: true,
    },
    {
      type: "date",
      label: t("listArticle:createdAt"),
      value: createdAt,
      onChange: (newValue) => setCreatedAt(newValue),
      condition: true,
    },
    {
      type: "date",
      label: t("listArticle:publishDate"),
      value: publishDate,
      onChange: (newValue) => setPublishDate(newValue),
      condition: true,
    },
  ];

  const handleSearch = () => {
    localStorage.setItem("SearchValue", searchQuery);
    localStorage.setItem("Visibility", visibility);
    setPaginationModel({ page: 0, pageSize: paginationModel.pageSize });
    setSearch((prev) => !prev);
  };

  return (
    <>
      <div className="display-inline">
        <p className="heading-h2 semi-bold">
          {t("listArticle:listOfArticles")}
          <span className="opportunities-nbr">
            {getArticles?.data?.totalArticles}
          </span>
        </p>

        <CustomButton
          className="btn btn-filled"
          text={t("global:addarticle")}
          link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/${adminRoutes.add.route}`}
        />
      </div>

      <div id="container" className="recent-application-pentabell">
        <div className={`main-content ${isOpen ? "open" : "closed"}`}>
          <div className="table-Grid">
            <Grid item xs={12}>
              <CustomFilters
                filters={filters}
                onSearch={handleSearch}
                onReset={resetSearch}
                searchLabel={t("global:search")}
              />
            </Grid>
          </div>

          <Grid item xs={12}>
            <div style={{ height: "100%", width: "100%" }}>
              <DataGrid
                rows={rows}
                columns={columns}
                pagination
                className="pentabell-table"
                paginationMode="server"
                paginationModel={paginationModel}
                onPaginationModelChange={handlePaginationChange}
                pageSizeOptions={[5, 10, 25]}
                rowCount={getArticles?.data?.totalArticles || 0}
                autoHeight
                disableSelectionOnClick
                columnVisibilityModel={{
                  createdBy: !isMobile,
                  createdAt: !isMobile,
                  totalCommentaires: !isMobile,
                  visibility: !isMobile,
                  language: !isMobile,
                  archived: !isMobile,
                }}
              />
            </div>
          </Grid>
        </div>
      </div>
    </>
  );
};

export default ListArticles;
