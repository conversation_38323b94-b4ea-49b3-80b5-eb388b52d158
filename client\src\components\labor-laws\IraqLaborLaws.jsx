"use client";
import {
  Container,
  Grid,
  AccordionSummary,
  AccordionDetails,
  Accordion,
} from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import SvgexpandIcon from "../../assets/images/icons/arrowUp.svg";

import SvgCallendar from "@/assets/images/icons/yellow/yellowCalendar.svg";

export default function IraqLaborLaws() {
  const [expanded, setExpanded] = useState(1);
  const { t } = useTranslation();
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  return (
    <div id="labor-tn-laws" className="custom-max-width">
      <Container>
        <h2 className="heading-h1">{t("iraq:IraqLabor:title")}</h2>

        <Grid className="container" container columnSpacing={3} rowSpacing={2}>
          <Grid item xs={12} sm={12} key={1}>
            <Accordion
              elevation={0}
              expanded={expanded === 1}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(1)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("iraq:IraqLabor:workingHours:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("iraq:IraqLabor:workingHours:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:workingHours:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("iraq:IraqLabor:workingHours:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:workingHours:description2")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={2}>
            <Accordion
              elevation={0}
              expanded={expanded === 2}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(2)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("iraq:IraqLabor:employmentContracts:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:employmentContracts:description")}
                  </p>
                  <br />
                  <p className="service-sub-title">
                    {t("iraq:IraqLabor:employmentContracts:title1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("iraq:IraqLabor:employmentContracts:data1")}</li>
                    <li>{t("iraq:IraqLabor:employmentContracts:data2")}</li>
                  </ul>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:employmentContracts:ps")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={3}>
            <Accordion
              elevation={0}
              expanded={expanded === 3}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(3)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("iraq:IraqLabor:termination:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:termination:description")}
                  </p>{" "}
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("iraq:IraqLabor:termination:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:termination:description1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("iraq:IraqLabor:termination:data1")}</li>
                    <li>{t("iraq:IraqLabor:termination:data2")}</li>
                    <li>{t("iraq:IraqLabor:termination:data3")}</li>
                    <li>{t("iraq:IraqLabor:termination:data4")}</li>
                    <li>{t("iraq:IraqLabor:termination:data5")}</li>
                  </ul>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t("iraq:IraqLabor:termination:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:termination:description2")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("iraq:IraqLabor:termination:title3")}
                  </p>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:termination:description3")}
                  </p>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:termination:description4")}
                  </p>
                  <ul className="service-description paragraph list-arrow">
                    <li className="item-arrow">
                      {t("iraq:IraqLabor:termination:item1")}
                    </li>
                    <li className="item-arrow">
                      {t("iraq:IraqLabor:termination:item2")}
                    </li>
                  </ul>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:termination:description5")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={4}>
            <Accordion
              elevation={0}
              expanded={expanded === 4}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(4)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("iraq:IraqLabor:payroll:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:payroll:description")}
                  </p>
                </div>
                <div className="payroll-tn">
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("iraq:IraqLabor:payroll:fiscalYear:title")}
                    </p>
                    <p className="date">
                      {t("iraq:IraqLabor:payroll:fiscalYear:date1")}
                      <br />
                      {t("iraq:IraqLabor:payroll:fiscalYear:date2")}
                    </p>
                    <p className="paragraph">
                      {t("iraq:IraqLabor:payroll:fiscalYear:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("iraq:IraqLabor:payroll:payrollCycle:title")}
                    </p>
                    <p className="date">
                      {t("iraq:IraqLabor:payroll:payrollCycle:date")}
                    </p>
                    <p className="paragraph">
                      {t("iraq:IraqLabor:payroll:payrollCycle:description")}
                    </p>
                  </div>

                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("iraq:IraqLabor:payroll:minimumWage:title")}
                    </p>
                    <p className="date">
                      {t("iraq:IraqLabor:payroll:minimumWage:wage")}
                      <br />
                      {t("iraq:IraqLabor:payroll:minimumWage:date")}​
                    </p>
                    <p className="paragraph">
                      {t("iraq:IraqLabor:payroll:minimumWage:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("iraq:IraqLabor:payroll:payrollManagement:title")}
                    </p>
                    <p className="date">
                      {t("iraq:IraqLabor:payroll:payrollManagement:date1")}
                      <br />
                      {t("iraq:IraqLabor:payroll:payrollManagement:date2")}
                    </p>
                    <p className="paragraph">
                      {t(
                        "iraq:IraqLabor:payroll:payrollManagement:description"
                      )}
                    </p>
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={5}>
            <Accordion
              elevation={0}
              expanded={expanded === 5}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(5)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("iraq:IraqLabor:leaveEntitlements:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:leaveEntitlements:description1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:leaveEntitlements:description2")}
                  </p>
                  <br />
                  <p className="service-sub-title">
                    {t("iraq:IraqLabor:leaveEntitlements:subTitle")}
                  </p>
                  <div className="holidays-dates">
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS1:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS1:title"
                        )}
                      </p>
                    </div>
                    {/* <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS2:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS2:title"
                        )}
                      </p>
                    </div> */}
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS3:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS3:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS4:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS4:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS5:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS5:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS6:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS6:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS7:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS7:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS8:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS8:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS9:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS9:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS10:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS10:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS11:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS11:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS12:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS12:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS13:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "iraq:IraqLabor:leaveEntitlements:leaves:dataS13:title"
                        )}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "iraq:IraqLabor:leaveEntitlements:leaves:maternityLeave:title"
                    )}
                  </p>
                  <p className="service-description paragraph">
                    {t(
                      "iraq:IraqLabor:leaveEntitlements:leaves:maternityLeave:description1"
                    )}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "iraq:IraqLabor:leaveEntitlements:leaves:paternityLeave:title"
                    )}
                  </p>
                  <p className="service-description paragraph">
                    {t(
                      "iraq:IraqLabor:leaveEntitlements:leaves:paternityLeave:description1"
                    )}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "iraq:IraqLabor:leaveEntitlements:leaves:marriageLeave:title"
                    )}
                  </p>
                  <p className="service-description paragraph">
                    {t(
                      "iraq:IraqLabor:leaveEntitlements:leaves:marriageLeave:description1"
                    )}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "iraq:IraqLabor:leaveEntitlements:leaves:bereavementLeave:title"
                    )}
                  </p>
                  <p className="service-description paragraph">
                    {t(
                      "iraq:IraqLabor:leaveEntitlements:leaves:bereavementLeave:description1"
                    )}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "iraq:IraqLabor:leaveEntitlements:leaves:sickLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "iraq:IraqLabor:leaveEntitlements:leaves:sickLeave:description1"
                    )}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={6}>
            <Accordion
              elevation={0}
              expanded={expanded === 6}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(6)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("iraq:IraqLabor:tax:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:tax:description")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("iraq:IraqLabor:tax:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:tax:description1")}
                  </p>
                  <div className="item">
                    <ul className="service-description paragraph">
                      <li>{t("iraq:IraqLabor:tax:data01")}</li>
                      <li>{t("iraq:IraqLabor:tax:data03")}</li>
                      <li>{t("iraq:IraqLabor:tax:data04")}</li>
                    </ul>
                  </div>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("iraq:IraqLabor:tax:title2")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("iraq:IraqLabor:tax:data11")}</li>
                    <li>{t("iraq:IraqLabor:tax:data12")}</li>
                    <li>{t("iraq:IraqLabor:tax:data13")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>

          <Grid item xs={12} sm={12} key={7}>
            <Accordion
              elevation={0}
              expanded={expanded === 7}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(7)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("iraq:IraqLabor:visa:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:visa:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("iraq:IraqLabor:visa:title1")}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t("iraq:IraqLabor:visa:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:visa:description2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:visa:description21")}
                  </p>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:visa:description22")}
                  </p>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:visa:description23")}
                  </p>
                  <p className="service-description paragraph">
                    {t("iraq:IraqLabor:visa:description24")}
                  </p>
                  <br />
                  <p className="service-sub-title">
                    {t("iraq:IraqLabor:visa:title3")}
                  </p>
                  {/* <p className="service-description paragraph">
                    {t("iraq:IraqLabor:visa:description3")}
                  </p> */}
                </div>
                <div className="item">
                  <ul className="service-description paragraph">
                    <li>{t("iraq:IraqLabor:visa:data1")}</li>
                    <li>{t("iraq:IraqLabor:visa:data2")}</li>
                    <li>{t("iraq:IraqLabor:visa:data3")}</li>
                  </ul>
                </div>
                <p className="service-sub-title">
                  {t("iraq:IraqLabor:visa:title4")}
                </p>
                <div className="item">
                  <ol className="service-description paragraph">
                    <li>{t("iraq:IraqLabor:visa:data01")}</li>
                    <li>{t("iraq:IraqLabor:visa:data02")}</li>
                    <li>{t("iraq:IraqLabor:visa:data03")}</li>
                    <li>{t("iraq:IraqLabor:visa:data04")}</li>
                  </ol>
                </div>
                <p className="service-sub-title">
                  {t("iraq:IraqLabor:visa:title5")}
                </p>
                <p className="service-description paragraph">
                  {t("iraq:IraqLabor:visa:data11")}
                </p>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}
