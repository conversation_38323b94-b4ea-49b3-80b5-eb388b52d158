(()=>{var e={};e.id=206,e.ids=[206],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},83969:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s,r=a(95746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let i=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),s||(s=r.createElement("path",{fill:"#234791",d:"m16.172 11-5.364-5.364 1.414-1.414L20 12l-7.778 7.778-1.414-1.414L16.172 13H4v-2z"})))},58996:(e,t,a)=>{"use strict";a.d(t,{Z:()=>i});var s,r=a(95746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let i=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:40,height:41,fill:"none"},e),s||(s=r.createElement("path",{fill:"#fff",d:"m21.953 20.499-8.25-8.25 2.357-2.357L26.667 20.5 16.06 31.106l-2.357-2.358z"})))},57201:(e,t,a)=>{"use strict";a.d(t,{Z:()=>i});var s,r=a(95746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let i=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),s||(s=r.createElement("path",{stroke:"#1D5A9F",strokeWidth:1.5,d:"m12.398 17.396-.398-.25-.398.25-6.852 4.296V3A.25.25 0 0 1 5 2.75h14a.25.25 0 0 1 .25.25v18.692zm-8.03 4.535Z"})))},94474:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s,r=a(95746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let i=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none"},e),s||(s=r.createElement("path",{fill:"#798BA3",d:"M7.999 14.666a6.667 6.667 0 1 1 0-13.333 6.667 6.667 0 0 1 0 13.333m0-1.333a5.333 5.333 0 1 0 0-10.667 5.333 5.333 0 0 0 0 10.667M8.665 8h2.667v1.333h-4V4.666h1.333z"})))},66818:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),a(55568),a(30962),a(23658),a(54864);var s=a(23191),r=a(88716),n=a(37922),i=a.n(n),o=a(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let c=["",{children:["[locale]",{children:["(website)",{children:["blog",{children:["[url]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,55568)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\blog\\[url]\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,30962)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\layout.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,23658)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54864)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\not-found.js"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\blog\\[url]\\page.jsx"],u="/[locale]/(website)/blog/[url]/page",m={require:a,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/[locale]/(website)/blog/[url]/page",pathname:"/[locale]/blog/[url]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},92757:(e,t,a)=>{Promise.resolve().then(a.bind(a,39154))},36690:(e,t,a)=>{"use strict";a.d(t,{Z:()=>n});var s=a(27522),r=a(10326);let n=(0,s.Z)((0,r.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},18835:(e,t,a)=>{"use strict";a.d(t,{Z:()=>x});var s=a(17577),r=a(41135),n=a(88634),i=a(91703),o=a(2791),l=a(71685),c=a(97898);function d(e){return(0,c.ZP)("MuiCardMedia",e)}(0,l.Z)("MuiCardMedia",["root","media","img"]);var u=a(10326);let m=e=>{let{classes:t,isMediaComponent:a,isImageComponent:s}=e;return(0,n.Z)({root:["root",a&&"media",s&&"img"]},d,t)},p=(0,i.ZP)("div",{name:"MuiCardMedia",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:a}=e,{isMediaComponent:s,isImageComponent:r}=a;return[t.root,s&&t.media,r&&t.img]}})({display:"block",backgroundSize:"cover",backgroundRepeat:"no-repeat",backgroundPosition:"center",variants:[{props:{isMediaComponent:!0},style:{width:"100%"}},{props:{isImageComponent:!0},style:{objectFit:"cover"}}]}),h=["video","audio","picture","iframe","img"],g=["picture","img"],x=s.forwardRef(function(e,t){let a=(0,o.i)({props:e,name:"MuiCardMedia"}),{children:s,className:n,component:i="div",image:l,src:c,style:d,...x}=a,f=h.includes(i),v=!f&&l?{backgroundImage:`url("${l}")`,...d}:d,b={...a,component:i,isMediaComponent:f,isImageComponent:g.includes(i)},y=m(b);return(0,u.jsx)(p,{className:(0,r.Z)(y.root,n),as:i,role:!f&&l?"img":void 0,ref:t,style:v,ownerState:b,src:f?l||c:void 0,...x,children:s})})},34039:(e,t,a)=>{"use strict";a.d(t,{Z:()=>g});var s=a(17577),r=a(41135),n=a(88634),i=a(91703),o=a(2791),l=a(89178),c=a(71685),d=a(97898);function u(e){return(0,d.ZP)("MuiCard",e)}(0,c.Z)("MuiCard",["root"]);var m=a(10326);let p=e=>{let{classes:t}=e;return(0,n.Z)({root:["root"]},u,t)},h=(0,i.ZP)(l.Z,{name:"MuiCard",slot:"Root",overridesResolver:(e,t)=>t.root})({overflow:"hidden"}),g=s.forwardRef(function(e,t){let a=(0,o.i)({props:e,name:"MuiCard"}),{className:s,raised:n=!1,...i}=a,l={...a,raised:n},c=p(l);return(0,m.jsx)(h,{className:(0,r.Z)(c.root,s),elevation:n?8:void 0,ref:t,ownerState:l,...i})})},76971:(e,t,a)=>{"use strict";a.d(t,{Z:()=>M});var s=a(17577),r=a(41135),n=a(88634),i=a(92014),o=a(33662),l=a(27522),c=a(10326);let d=(0,l.Z)((0,c.jsx)("path",{d:"M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"}),"CheckBoxOutlineBlank"),u=(0,l.Z)((0,c.jsx)("path",{d:"M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"}),"CheckBox"),m=(0,l.Z)((0,c.jsx)("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10H7v-2h10v2z"}),"IndeterminateCheckBox");var p=a(54641),h=a(27080),g=a(71685),x=a(97898);function f(e){return(0,x.ZP)("MuiCheckbox",e)}let v=(0,g.Z)("MuiCheckbox",["root","checked","disabled","indeterminate","colorPrimary","colorSecondary","sizeSmall","sizeMedium"]);var b=a(91703),y=a(30990),w=a(40955),j=a(2791),$=a(7467),N=a(31121);let _=e=>{let{classes:t,indeterminate:a,color:s,size:r}=e,i={root:["root",a&&"indeterminate",`color${(0,p.Z)(s)}`,`size${(0,p.Z)(r)}`]},o=(0,n.Z)(i,f,t);return{...t,...o}},C=(0,b.ZP)(o.Z,{shouldForwardProp:e=>(0,h.Z)(e)||"classes"===e,name:"MuiCheckbox",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:a}=e;return[t.root,a.indeterminate&&t.indeterminate,t[`size${(0,p.Z)(a.size)}`],"default"!==a.color&&t[`color${(0,p.Z)(a.color)}`]]}})((0,y.Z)(({theme:e})=>({color:(e.vars||e).palette.text.secondary,variants:[{props:{color:"default",disableRipple:!1},style:{"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,i.Fq)(e.palette.action.active,e.palette.action.hoverOpacity)}}},...Object.entries(e.palette).filter((0,w.Z)()).map(([t])=>({props:{color:t,disableRipple:!1},style:{"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,i.Fq)(e.palette[t].main,e.palette.action.hoverOpacity)}}})),...Object.entries(e.palette).filter((0,w.Z)()).map(([t])=>({props:{color:t},style:{[`&.${v.checked}, &.${v.indeterminate}`]:{color:(e.vars||e).palette[t].main},[`&.${v.disabled}`]:{color:(e.vars||e).palette.action.disabled}}})),{props:{disableRipple:!1},style:{"&:hover":{"@media (hover: none)":{backgroundColor:"transparent"}}}}]}))),Z=(0,c.jsx)(u,{}),P=(0,c.jsx)(d,{}),S=(0,c.jsx)(m,{}),M=s.forwardRef(function(e,t){let a=(0,j.i)({props:e,name:"MuiCheckbox"}),{checkedIcon:n=Z,color:i="primary",icon:o=P,indeterminate:l=!1,indeterminateIcon:d=S,inputProps:u,size:m="medium",disableRipple:p=!1,className:h,slots:g={},slotProps:x={},...f}=a,v=l?d:o,b=l?d:n,y={...a,disableRipple:p,color:i,indeterminate:l,size:m},w=_(y),M=x.input??u,[k,A]=(0,N.Z)("root",{ref:t,elementType:C,className:(0,r.Z)(w.root,h),shouldForwardComponentProp:!0,externalForwardedProps:{slots:g,slotProps:x,...f},ownerState:y,additionalProps:{type:"checkbox",icon:s.cloneElement(v,{fontSize:v.props.fontSize??m}),checkedIcon:s.cloneElement(b,{fontSize:b.props.fontSize??m}),disableRipple:p,slots:g,slotProps:{input:(0,$.Z)("function"==typeof M?M(y):M,{"data-indeterminate":l})}}});return(0,c.jsx)(k,{...A,classes:w})})},10163:(e,t,a)=>{"use strict";a.d(t,{Z:()=>h});var s=a(17577),r=a(41135),n=a(88634),i=a(91703),o=a(2791),l=a(71685),c=a(97898);function d(e){return(0,c.ZP)("MuiDialogActions",e)}(0,l.Z)("MuiDialogActions",["root","spacing"]);var u=a(10326);let m=e=>{let{classes:t,disableSpacing:a}=e;return(0,n.Z)({root:["root",!a&&"spacing"]},d,t)},p=(0,i.ZP)("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:a}=e;return[t.root,!a.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:e})=>!e.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),h=s.forwardRef(function(e,t){let a=(0,o.i)({props:e,name:"MuiDialogActions"}),{className:s,disableSpacing:n=!1,...i}=a,l={...a,disableSpacing:n},c=m(l);return(0,u.jsx)(p,{className:(0,r.Z)(c.root,s),ownerState:l,ref:t,...i})})},28591:(e,t,a)=>{"use strict";a.d(t,{Z:()=>x});var s=a(17577),r=a(41135),n=a(88634),i=a(91703),o=a(30990),l=a(2791),c=a(71685),d=a(97898);function u(e){return(0,d.ZP)("MuiDialogContent",e)}(0,c.Z)("MuiDialogContent",["root","dividers"]);var m=a(64650),p=a(10326);let h=e=>{let{classes:t,dividers:a}=e;return(0,n.Z)({root:["root",a&&"dividers"]},u,t)},g=(0,i.ZP)("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:a}=e;return[t.root,a.dividers&&t.dividers]}})((0,o.Z)(({theme:e})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:e})=>e.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(e.vars||e).palette.divider}`,borderBottom:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:e})=>!e.dividers,style:{[`.${m.Z.root} + &`]:{paddingTop:0}}}]}))),x=s.forwardRef(function(e,t){let a=(0,l.i)({props:e,name:"MuiDialogContent"}),{className:s,dividers:n=!1,...i}=a,o={...a,dividers:n},c=h(o);return(0,p.jsx)(g,{className:(0,r.Z)(c.root,s),ownerState:o,ref:t,...i})})},98117:(e,t,a)=>{"use strict";a.d(t,{Z:()=>h});var s=a(17577),r=a(41135),n=a(88634),i=a(25609),o=a(91703),l=a(2791),c=a(64650),d=a(55733),u=a(10326);let m=e=>{let{classes:t}=e;return(0,n.Z)({root:["root"]},c.a,t)},p=(0,o.ZP)(i.default,{name:"MuiDialogTitle",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:"16px 24px",flex:"0 0 auto"}),h=s.forwardRef(function(e,t){let a=(0,l.i)({props:e,name:"MuiDialogTitle"}),{className:n,id:i,...o}=a,c=m(a),{titleId:h=i}=s.useContext(d.Z);return(0,u.jsx)(p,{component:"h2",className:(0,r.Z)(c.root,n),ownerState:a,ref:t,variant:"h6",id:i??h,...o})})},64650:(e,t,a)=>{"use strict";a.d(t,{Z:()=>i,a:()=>n});var s=a(71685),r=a(97898);function n(e){return(0,r.ZP)("MuiDialogTitle",e)}let i=(0,s.Z)("MuiDialogTitle",["root"])},65368:(e,t,a)=>{"use strict";a.d(t,{Z:()=>n}),a(17577);var s=a(27522),r=a(10326);let n=(0,s.Z)((0,r.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},38932:function(e,t,a){(function(e){"use strict";var t=/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?|janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,a=[/^janv/i,/^févr/i,/^mars/i,/^avr/i,/^mai/i,/^juin/i,/^juil/i,/^août/i,/^sept/i,/^oct/i,/^nov/i,/^déc/i];e.defineLocale("fr",{months:"janvier_f\xe9vrier_mars_avril_mai_juin_juillet_ao\xfbt_septembre_octobre_novembre_d\xe9cembre".split("_"),monthsShort:"janv._f\xe9vr._mars_avr._mai_juin_juil._ao\xfbt_sept._oct._nov._d\xe9c.".split("_"),monthsRegex:t,monthsShortRegex:t,monthsStrictRegex:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,monthsShortStrictRegex:/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui \xe0] LT",nextDay:"[Demain \xe0] LT",nextWeek:"dddd [\xe0] LT",lastDay:"[Hier \xe0] LT",lastWeek:"dddd [dernier \xe0] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",w:"une semaine",ww:"%d semaines",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|)/,ordinal:function(e,t){switch(t){case"D":return e+(1===e?"er":"");default:case"M":case"Q":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}},week:{dow:1,doy:4}})})(a(57967))},86851:(e,t,a)=>{"use strict";a.d(t,{Z:()=>n});var s=a(10326),r=a(98139);let n=function(){return s.jsx("div",{className:"spinner",children:s.jsx(r.default,{})})}},82045:(e,t,a)=>{"use strict";a.d(t,{Z:()=>v});var s,r,n=a(10326),i=a(17577),o=a(87638),l=a(78077),c=a(63568),d=a(52210),u=a(15082),m=a(4658),p=a(55618),h=a(95746);function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let x=e=>h.createElement("svg",g({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,fill:"none"},e),s||(s=h.createElement("rect",{width:32,height:32,fill:"#0B3051",fillOpacity:.12,rx:3})),r||(r=h.createElement("path",{fill:"#0B3051",d:"M8 24q-.824 0-1.412-.587A1.93 1.93 0 0 1 6 22V10q0-.825.588-1.412A1.93 1.93 0 0 1 8 8h16q.824 0 1.413.588Q26 9.175 26 10v12q0 .824-.587 1.413A1.93 1.93 0 0 1 24 24zm8-7-8-5v10h16V12zm0-2 8-5H8zm-8-3v-2 12z"})));var f=a(4563);function v(){let[e,t]=(0,i.useState)(""),[a,s]=(0,i.useState)(!1),{t:r}=(0,d.$G)(),h=(0,m.W)(s,t),g=async(e,{resetForm:t})=>{let{email:a}=e;await h.mutateAsync({email:a}),t(),setTimeout(()=>{s(!1)},3e3)};return(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"icon",children:n.jsx(x,{})}),n.jsx("p",{className:"title",children:"Subscribe for updates"}),n.jsx("p",{className:"description",children:"Turn on this job alert so you don t miss out on openings that could fit your needs"}),n.jsx(c.J9,{initialValues:{email:""},validationSchema:()=>(0,f.ft)(r),onSubmit:g,children:({values:e,handleChange:a,errors:s,touched:r})=>(0,n.jsxs)(c.l0,{children:[n.jsx(o.Z,{className:"form-group",children:n.jsx(l.Z,{className:"input-pentabell",placeholder:"<EMAIL>",variant:"standard",name:"email",value:e.email,onChange:e=>{a(e),t("")},error:!!(s.email&&r.email)})}),n.jsx(c.Bc,{name:"email",children:e=>n.jsx("span",{className:"error-span",children:e})}),n.jsx(u.default,{text:"Submit",className:"btn btn-filled blue",type:"submit"})]})}),n.jsx(p.Z,{errMsg:e,success:a})]})}},57993:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});var s=a(10326);function r({htmlContent:e}){return s.jsx("div",{dangerouslySetInnerHTML:{__html:e}})}},39154:(e,t,a)=>{"use strict";a.d(t,{default:()=>V});var s=a(10326),r=a(17577),n=a(57967),i=a.n(n),o=a(90423),l=a(16027),c=a(23743),d=a(88441),u=a(31190);a(38932);var m=a(52210),p=a(90397),h=a(64504),g=a(57993),x=a(28236),f=a(94474),v=a(15082),b=a(33198),y=a(78077),w=a(16376),j=a(50967),$=a(86851),N=a(70580);let _=({comment:e,addReply:t,level:a=0,user:n,refetch:o,language:l})=>{let[c,d]=(0,r.useState)(!1),[m,p]=(0,r.useState)(!1),[h,g]=(0,r.useState)(""),[f,w]=(0,r.useState)(1),$=async e=>{e.preventDefault();try{let e={comment:h};n?._id?e.user=n?._id:(e.firstName=formData.firstName,e.email=formData.email),e.language=l;let t=await N.xk.post(`/comments/${m}/response`,e);200===t.status?(g(""),p(!1),d(!0),u.Am.success("en"===l?"Your comment has been successfully added and is awaiting administrator approval":"Votre commentaire a \xe9t\xe9 ajout\xe9 avec succ\xe8s et est en attente de l'approbation de l'administrateur"),o()):console.error("Error adding comment:",t.statusText)}catch(e){console.error("Error adding comment:",e)}};return(0,s.jsxs)("div",{className:"one-comment-item",style:{marginLeft:`${20*a}px`},children:[s.jsx(b.Z,{sx:{bgcolor:(0,x.u_)(e?.firstName)},alt:e.user?`${e?.user?.firstName} ${e?.user?.lastName}`:e?.firstName,src:`${process.env.NEXT_PUBLIC_BASE_API_URL}${j.Y.files}/${e?.user?.profilePicture}`,className:"menu-icon avatar"}),(0,s.jsxs)("div",{className:"comment-content",children:[(0,s.jsxs)("div",{className:"comment-header",children:[s.jsx("p",{className:"username",children:e.user?`${e?.user?.firstName} ${e?.user?.lastName}`:e?.firstName}),s.jsx("p",{className:"date",children:i()(e.createdAt).fromNow()})]}),s.jsx("p",{className:"text",children:e.comment}),(0,s.jsxs)("div",{className:"flex",children:[n&&s.jsx(v.default,{text:m?"Cancel":"Reply",className:"btn btn-ghost reply grey",onClick:()=>m?p(!1):p(e?._id)}),e.responses?.length>0&&s.jsx(v.default,{text:`Replies (${e.responses.length})`,className:"btn btn-ghost reply",onClick:()=>d(!c)})]}),c&&(0,s.jsxs)("div",{className:"replies",children:[e.responses.length>1&&f<e.responses.length&&s.jsx(v.default,{text:"Show Previous Replies",className:"btn btn-ghost replies-list",onClick:()=>w(f+1)}),e.responses.slice(-f).map((e,r)=>s.jsx(_,{comment:e,addReply:t,level:a+1},r))]}),m&&(0,s.jsxs)("form",{onSubmit:$,className:"reply-form",children:[s.jsx(y.Z,{value:h,onChange:e=>g(e.target.value),placeholder:"Add a reply..."}),s.jsx(v.default,{text:"reply",className:"btn btn-filled blue",type:"submit"})]})]})]})},C=({comments:e,addReply:t,user:a,refetch:n,articleId:i})=>{let[o,l]=(0,r.useState)(4),c=e?.slice(0,o);return(0,s.jsxs)("div",{children:[c?.map((e,r)=>s.jsx(_,{comment:e,addReply:t,user:a,refetch:n,articleId:i},r)),e?.length>o&&s.jsx(v.default,{text:"Show More Comments",icon:s.jsx(w.Z,{}),className:"btn btn-ghost show-more",onClick:()=>l(o+4)})]})},Z=function({articleId:e,isLoadingArticle:t,user:a,data:n,isLoading:i,refetch:l}){let[c,d]=(0,r.useState)([]),u=n?.totalComments;return i||t?s.jsx($.Z,{}):u>0?(0,s.jsxs)(o.default,{id:"comment-list-blog",children:[(0,s.jsxs)("p",{className:"sub-heading text-banking",children:["Comments"," ",u>0&&s.jsx("span",{className:"comments-nbr",children:u})]}),s.jsx(C,{comments:c,addReply:(e,t)=>{d(a=>a.map(a=>a.id===e?{...a,replies:[...a.replies,{id:Math.random(),avatar:"https://example.com/avatar3.jpg",username:"New User",date:new Date().toISOString().split("T")[0],text:t,replies:[]}]}:a))},user:a,refetch:l,articleId:e})]}):null};var P=a(87638),S=a(90943),M=a(76971),k=a(5394);function A({articleId:e,user:t,refetch:a,language:n}){let[i,c]=(0,r.useState)({firstName:"",email:"",comment:"",saveInfo:!1}),d=e=>{let{name:t,value:a,type:s,checked:r}=e.target;c({...i,[t]:"checkbox"===s?r:a})},m=async s=>{if(s.preventDefault(),!e){console.error("Article ID is not defined");return}try{let s={comment:i.comment};if(t?._id?s.user=t?._id:(s.firstName=i.firstName,s.email=i.email),s?.comment?.trim()!==""){s.language=n;let t=await N.yX.post(`/comments/${e}`,s);200===t.status?(c({firstName:"",email:"",comment:"",saveInfo:!1}),u.Am.success("en"===n?"Your comment has been successfully added and is awaiting administrator approval":"Votre commentaire a \xe9t\xe9 ajout\xe9 avec succ\xe8s et est en attente de l'approbation de l'administrateur"),a()):console.error("Error adding comment:",t.statusText)}}catch(e){console.error("Error adding comment:",e)}};return s.jsx(o.default,{id:"create-comment-blog",children:(0,s.jsxs)("form",{onSubmit:m,children:[s.jsx("p",{className:"heading-h2",children:"Leave a Comment"}),(0,s.jsxs)(l.default,{className:"container",container:!0,rowSpacing:2,columnSpacing:3,children:[t?._id?null:(0,s.jsxs)(s.Fragment,{children:[s.jsx(l.default,{item:!0,xs:12,sm:6,children:(0,s.jsxs)(P.Z,{children:[s.jsx(S.Z,{className:"label-pentabell",children:"Name"}),s.jsx(y.Z,{value:i.firstName,name:"firstName",onChange:d,required:!0,className:"input-pentabell",placeholder:"Name",variant:"standard"})]})}),s.jsx(l.default,{item:!0,xs:12,sm:6,children:(0,s.jsxs)(P.Z,{children:[s.jsx(S.Z,{className:"label-pentabell",children:"Email Address"}),s.jsx(y.Z,{value:i.email,onChange:d,name:"email",required:!0,className:"input-pentabell",placeholder:"<EMAIL>",variant:"standard"})]})})]}),s.jsx(l.default,{item:!0,xs:12,sm:12,children:(0,s.jsxs)(P.Z,{children:[s.jsx(S.Z,{className:"label-pentabell",children:"Your Comment"}),s.jsx(y.Z,{className:"input-pentabell",type:"text",name:"comment",variant:"standard",placeholder:"Your Comment",value:i.comment,autoComplete:"off",onChange:d,required:!0})]})}),!("loading"===t&&t?._id)&&s.jsx(l.default,{item:!0,xs:12,sm:12,children:s.jsx(k.Z,{className:"checkbox-pentabell",control:s.jsx(M.Z,{checked:i.saveInfo,onChange:d,name:"saveInfo"}),label:"Save Info"})}),s.jsx(l.default,{item:!0,xs:12,sm:12,children:s.jsx(v.default,{text:"Submit",className:"btn btn-filled",onClick:m})})]})]})})}var E=a(58996),L=a(22304),I=a(82045),R=a(86184),D=a(5248),T=a(57201);a(38106);let O=function({headings:e}){let[t,a]=(0,r.useState)(!1),n=e=>{let t=document.getElementById(e);t&&t.scrollIntoView({behavior:"smooth"})};return(0,s.jsxs)("div",{id:"content-table",children:[s.jsx("p",{className:"heading-h3 text-center text-white semi-bold",children:"Table of content"}),s.jsx("ul",{children:e.map((e,t)=>s.jsx("li",{children:s.jsx("a",{href:`#${e.id}`,onClick:()=>n(e.id),children:e.content})},t))})]})};var z=a(3094),U=a(3361),Y=a(78882);let q=function({sahredUrl:e}){return(0,s.jsxs)("div",{id:"social-media-share",children:[s.jsx("p",{className:"title",children:"Share article on"}),(0,s.jsxs)("div",{children:[s.jsx(v.default,{icon:s.jsx(z.Z,{}),className:"btn btn-ghost",onClick:()=>window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(e)}`,"_blank"),"aria-label":"Share on Facebook"}),s.jsx(v.default,{onClick:()=>window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(e)}`,"_blank"),icon:s.jsx(Y.Z,{}),className:"btn btn-ghost","aria-label":"Share on Twitter"}),s.jsx(v.default,{onClick:()=>window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(e)}`,"_blank"),icon:s.jsx(U.Z,{}),className:"btn btn-ghost","aria-label":"Share on LinkedIn"})]})]})};var B=a(34039),F=a(18835),X=a(90434),Q=a(83969),H=a(97980);let G=function({relatedArticles:e,articleId:t,language:a}){let{t:r}=(0,m.$G)();return e?.length>0?(0,s.jsxs)("div",{className:"section-related-blog",children:[s.jsx("p",{className:"heading-h2 semi-bold text-center",children:"Related Articles"}),s.jsx(l.default,{container:!0,rowSpacing:0,columnSpacing:2,children:e?.length>0?e?.filter(e=>e?.id!==t).slice(0,3).map(e=>s.jsx(l.default,{item:!0,xs:12,sm:6,md:4,className:"last-blog",children:s.jsxs(B.Z,{className:"card",children:[s.jsx(X.default,{locale:"en"===a?"en":"fr",href:`${"en"===a?`/blog/${e?.slug}`:`/${a}/blog/${e?.slug}`}/`,children:s.jsx(F.Z,{component:"img",image:`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${e?.picture}`,title:e.title,alt:e.title,className:"card-media"})}),s.jsxs("div",{className:"card-text",children:[s.jsx("p",{children:e.title}),s.jsx(v.default,{text:"Explore",icon:s.jsx(Q.default,{}),className:"btn btn-ghost white",link:`/${H.Bi.blog.route}/${e.slug}`})]})]})},e?.id)):s.jsx("p",{children:"No related articles found"})}),s.jsx(l.default,{className:"btn-view-more",children:s.jsx(v.default,{link:`/${H.Bi.blog.route}`,text:r("global:viewMore"),className:"btn btn-filled blue",aHref:!0})})]}):null},V=function({id:e,article:t,language:a,url:n}){let[b,y]=(0,r.useState)(!1),[w,j]=(0,r.useState)(!1),{t:$}=(0,m.$G)(),_=async e=>{try{await N.yX.delete(`/favourite/${e}`,{data:{type:"article"}}),V(!1)}catch(e){}},C=async()=>{try{await _(w)}catch(e){}y(!1)},P=(0,c.Z)(),{user:S}=(0,L.Z)(),{i18n:M}=(0,m.$G)(),k=(0,d.Z)(P.breakpoints.down("sm")),z=(0,R.UJ)();i().locale(M.language||"en");let[U,Y]=(0,r.useState)([]),[B,F]=(0,r.useState)(t?.content),[X,Q]=(0,r.useState)([]),[H,V]=(0,r.useState)(!1),W=(0,h.wv)({language:a,urlArticle:n},{enabled:!!n}),J=W?.data?._id,{data:K,isLoading:ee,refetch:et}=(0,h.hb)({articleId:J,pageNumber:1,pageSize:6,paginated:!0},{enabled:!!J}),ea=`${process.env.NEXT_PUBLIC_FRONTEND_URL}/blog/${n}`;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{id:"blog-page-details",children:[s.jsx("div",{id:"blog-header",children:s.jsx(o.default,{className:"custom-max-width",children:(0,s.jsxs)(l.default,{container:!0,spacing:3,children:[(0,s.jsxs)(l.default,{item:!0,xs:12,sm:6,children:[t?.categories?.length>0&&s.jsx("div",{className:"categories-list",children:t.categories.map(e=>e?.url&&s.jsx("span",{className:"category light",children:s.jsx("a",{href:`${"en"===a?`/blog/category/${e.url}`:`/${a}/blog/category/${e.url}`}/`,children:e.name})},e.url))}),s.jsx("h1",{className:"heading-h1",children:t?.highlights&&t?.highlights.length>0?(0,x.q1)(t?.title,t?.highlights):(0,x.RQ)(t?.title)}),(0,s.jsxs)("p",{className:"sub-heading date ",children:[s.jsx(f.default,{})," ",t?.publishDate?(0,x.fm)(i()(t?.publishDate).format("LL")):"",s.jsx(f.default,{})," ",t?.publishDate?(0,x.fm)(i()(t?.publishDate).format("LT")):""]}),(!S||S?.roles?.includes(D.uU.CANDIDATE))&&s.jsx(v.default,{leftIcon:!0,text:H?"Saved":"Save",onClick:H?()=>{j(e),y(!0)}:()=>{S?z.mutate({id:e,title:t?.title,typeOfFavourite:"article"},{onSuccess:()=>{V(!0)}}):u.Am.warning("Login or create account to save article.")},icon:s.jsx(T.Z,{className:`${H?"btn-filled-yellow":""}`}),className:`btn btn-ghost p-save ${H?"btn-filled-yellow ":""}`})]}),s.jsx(l.default,{item:!0,xs:12,sm:6,children:s.jsx("div",{className:"blog-img",children:s.jsx("img",{src:`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${t?.image}`,width:700,height:700,alt:t?.alt,loading:"lazy"})})})]})})}),s.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:[{"@type":"ListItem",position:1,item:{"@id":"en"===a?"https://www.pentabell.com/blog/":`https://www.pentabell.com/${a}/blog/`,name:"Blog"}},t?.categories[0].url&&{"@type":"ListItem",position:2,item:{"@id":"en"===a?`https://www.pentabell.com/blog/category/${t?.categories[0].url}/`:`https://www.pentabell.com/${a}/blog/category/${t?.categories[0].url}/`,name:t?.categories[0].name}}]})}}),(0,s.jsxs)(o.default,{className:"custom-max-width",children:[(0,s.jsxs)("div",{className:"categories-path",children:[s.jsx("a",{locale:"en"===a?"en":"fr",href:`${"en"===a?"/blog":`/${a}/blog`}/`,className:"link",children:"Blog"}),t?.categories?.length>0&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(E.Z,{}),t?.categories[0].url&&s.jsx("a",{className:"link",href:`${"en"===a?`/blog/category/${t?.categories[0].url}`:`/${a}/blog/category/${t?.categories[0].url}`}/`,children:t?.categories[0].name})]})]}),(0,s.jsxs)(l.default,{className:"container",container:!0,columnSpacing:2,children:[(0,s.jsxs)(l.default,{item:!0,xs:12,sm:8,children:[k&&U?.length>0?s.jsx(O,{headings:U}):null,s.jsx("div",{className:"blog-content",children:s.jsx(g.Z,{htmlContent:B})})]}),s.jsx(l.default,{item:!0,xs:12,sm:4,id:"sticky-sidebar",children:k?null:(0,s.jsxs)(s.Fragment,{children:[U?.length>0?s.jsx(O,{headings:U}):null,s.jsx(q,{sahredUrl:ea}),s.jsx("div",{className:"section",children:s.jsx(I.Z,{})})]})})]}),s.jsx(l.default,{className:"container",container:!0,columnSpacing:2,children:(0,s.jsxs)(l.default,{item:!0,xs:12,sm:8,children:[k?(0,s.jsxs)(s.Fragment,{children:[s.jsx(q,{sahredUrl:ea}),s.jsx("div",{className:"section",children:s.jsx(I.Z,{})})]}):null,s.jsx(G,{relatedArticles:X,language:a,articleId:J}),s.jsx(A,{refetch:et,articleId:J,user:S}),s.jsx(Z,{articleId:J,data:K,isLoading:ee,refetch:et,user:S})]})})]})]}),s.jsx(p.Z,{open:b,message:$("messages:supprimerarticlefavoris"),onClose:()=>{y(!1)},onConfirm:C})]})}},64504:(e,t,a)=>{"use strict";a.d(t,{QZ:()=>j,qA:()=>$,$F:()=>I,He:()=>R,wv:()=>L,bh:()=>M,b$:()=>k,KK:()=>A,Py:()=>P,hb:()=>E,Yg:()=>D,mg:()=>S,P0:()=>_,Cb:()=>Z,el:()=>N,IX:()=>C});var s=a(2994),r=a(50967),n=a(70580),i=a(31190);let o=e=>(e.t,new Promise(async(t,a)=>{n.yX.post(r.Y.articles,e.data).then(e=>{i.Am.success("Article added successfully"),e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&a(e)})})),l=e=>(e.t,new Promise(async(t,a)=>{n.yX.post(`${r.Y.articles}/auto`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&a(e)})})),c=({data:e,id:t})=>new Promise(async(a,s)=>{n.yX.put(`${r.Y.articles}/${t}/auto`,e).then(e=>{e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&s(e)})}),d=({data:e,id:t})=>new Promise(async(a,s)=>{n.yX.put(`${r.Y.articles}/${t}`,e).then(e=>{i.Am.success("article Commun fields updated successfully"),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&s(e)})}),u=e=>new Promise(async(t,a)=>{try{let a={};a=await n.yX.get(`${r.Y.articles}/${e.language}/blog/${e.urlArticle}`),t(a.data)}catch(e){e&&e.response&&e.response.data&&e.response.status,a(e)}}),m=({data:e,language:t,id:a})=>new Promise(async(s,o)=>{n.yX.post(`${r.Y.articles}/${t}/${a}`,e).then(e=>{"en"===t&&i.Am.success("Article english updated successfully"),"fr"===t&&i.Am.success("Article french updated successfully"),e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&o(e)})}),p=(e,t,a)=>new Promise(async(s,o)=>{try{let o=await n.xk.put(`${r.Y.articles}/${e}/${t}/desarchiver`,{archive:a});o?.data&&(i.Am.success(`Article ${a?"archived":"desarchived"} successfully`),s(o.data))}catch(e){i.Am.error(`Failed to ${a?"archive":"desarchive"} the article.`),o(e)}}),h=e=>new Promise(async(t,a)=>{try{let a=await n.yX.get(`${r.Y.categories}/${e}/all`);t(a.data)}catch(e){a(e)}}),g=e=>new Promise(async(t,a)=>{try{let a=await n.yX.get(`${r.Y.articles}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,searchQuery:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,publishDate:e.publishDate,isThreeLastArticles:e.isThreeLastArticles,isArchived:e.isArchived,categoryName:e.categoryName}});t(a.data)}catch(e){a(e)}}),x=e=>new Promise(async(t,a)=>{try{let a=await n.yX.get(`${r.Y.articles}/dashboard`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,searchQuery:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,publishDate:e.publishDate,isArchived:e.isArchived,categoryName:e.categoryName}});t(a.data)}catch(e){a(e)}}),f=e=>new Promise(async(t,a)=>{try{let a=await n.yX.get(`${r.Y.articles}/${e.language}/listarticle`);t(a.data)}catch(e){a(e)}}),v=({articleId:e,pageNumber:t,pageSize:a,sortOrder:s,name:i,approved:o,createdAt:l,paginated:c})=>new Promise(async(d,u)=>{try{let u=`${r.v}/comments/${e}?pageSize=${encodeURIComponent(a)}&pageNumber=${encodeURIComponent(t)}&sortOrder=${encodeURIComponent(s)}&paginated=${encodeURIComponent(c)}`;i&&(u+=`&name=${encodeURIComponent(i)}`),o&&(u+=`&approved=${encodeURIComponent(o)}`),l&&(u+=`&createdAt=${encodeURIComponent(new Date(l).toISOString())}`);let m=await n.yX.get(u);d(m.data)}catch(e){u(e)}}),b=(e,t)=>new Promise(async(a,s)=>{try{let s=await n.yX.get(`${r.Y.articles}/${t}/${e}`);a(s.data)}catch(e){s(e)}}),y=e=>new Promise(async(t,a)=>{try{let a=await n.xk.get(`${r.Y.comments}/detail/${e}`);t(a.data)}catch(e){a(e)}}),w=(e,t)=>new Promise(async(t,a)=>{try{let a=await n.yX.get(`${r.Y.articles}/${e}`);t(a.data)}catch(e){a(e)}});a(97980);let j=()=>(0,s.useMutation)({mutationFn:e=>o(e),onError:e=>{e.message=""}}),$=()=>(0,s.useMutation)({mutationFn:e=>l(e),onError:e=>{e.message=""}}),N=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t)=>c(e,t),onError:e=>{e.message=""}})),_=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t,a)=>m(e,t,a),onError:e=>{e.message=""}})),C=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:({language:e,id:t,archive:a})=>p(e,t,a),onError:e=>{console.error("Error during mutation",e),e.message=""}})),Z=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t)=>d(e,t),onError:e=>{e.message=""}})),P=e=>(0,s.useQuery)(["category",e],async()=>await h(e)),S=e=>(0,s.useQuery)(["service",e],async()=>await h(e)),M=e=>(0,s.useQuery)("article",async()=>await g(e)),k=e=>(0,s.useQuery)(`articles${e.language}`,async()=>await x(e)),A=e=>(0,s.useQuery)(`articlestitles${e.language}`,async()=>await f(e)),E=(e,t={})=>(0,s.useQuery)("comment",async()=>await v(e),{...t}),L=(e,t={})=>(0,s.useQuery)(["article",e],async()=>{try{return await u(e)}catch(t){throw t.response&&404===t.response.status&&("en"===e.language?window.location.href="/blog/":window.location.href="/fr/blog/"),t}},{onError:e=>{console.error("Error fetching article:",e.message)},...t}),I=(e,t)=>(0,s.useQuery)(["article",e,t],async()=>await b(e,t)),R=e=>(0,s.useQuery)(["articleall",e],async()=>await w(e)),D=e=>(0,s.useQuery)(["comment",e],async()=>await y(e))},86184:(e,t,a)=>{"use strict";a.d(t,{$i:()=>g,BF:()=>h,Fe:()=>i,Gc:()=>d,HF:()=>n,Hr:()=>l,IZ:()=>p,NF:()=>c,PM:()=>o,UJ:()=>u,jd:()=>m});var s=a(2994),r=a(21464);a(35047),a(97980);let n=()=>(0,s.useMutation)({mutationFn:e=>(0,r.W3)(e),onError:e=>{e.message=""}}),i=e=>(0,s.useQuery)("opportunities",async()=>await (0,r.fH)(e)),o=()=>(0,s.useMutation)(()=>(0,r.AE)()),l=e=>(0,s.useQuery)(["opportunities",e],async()=>await (0,r.Mq)(e)),c=()=>(0,s.useMutation)({mutationFn:(e,t,a)=>(0,r.rE)(e,t,a),onError:e=>{e.message=""}}),d=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t)=>(0,r.S1)(e,t),onError:e=>{e.message=""}})),u=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t,a)=>(0,r.lU)(e,t,a),onError:e=>{e.message=""}})),m=()=>{let e=(0,s.useQueryClient)();return(0,s.useMutation)({mutationFn:(e,t,a,s)=>(0,r.yH)(e,t,a,s),onSuccess:t=>{e.invalidateQueries("files")}})},p=()=>(0,s.useQuery)("SeoOpportunities",async()=>await (0,r.yJ)()),h=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t)=>(0,r.mt)(e,t),onError:e=>{e.message=""}})),g=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:({language:e,id:t,archive:a})=>(0,r.TK)(e,t,a),onError:e=>{console.error("Error during mutation",e),e.message=""}}))},21464:(e,t,a)=>{"use strict";a.d(t,{AE:()=>c,Mq:()=>l,S1:()=>u,TK:()=>g,W3:()=>i,fH:()=>o,lU:()=>m,mt:()=>x,rE:()=>d,yH:()=>p,yJ:()=>h});var s=a(50967),r=a(70580),n=a(31190);let i=e=>(e.t,new Promise(async(t,a)=>{r.yX.post(`/opportunities${s.Y.applications}/${e.opportunityId}`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&a(e)})})),o=e=>new Promise(async(t,a)=>{try{let a=await r.yX.get(`${s.Y.opportunity}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyWord:e.keyWord,visibility:e.visibility,title:e.title,createdAt:e.createdAt,publishDate:e.publishDate,country:e.country,industry:e.industry,contractType:e.contractType,minExperience:e.minExperience,maxExperience:e.maxExperience,jobDescriptionLanguages:e.jobDescriptionLanguages,reference:e.reference,opportunityType:e.opportunityType,exclude:"In House"!==e.opportunityType&&"true"}});t(a.data)}catch(e){a(e)}}),l=e=>new Promise(async(t,a)=>{try{let a=await r.yX.get(`${s.Y.opportunity}/${e}`);t(a.data)}catch(e){a(e)}}),c=async()=>(await r.xk.put("/UpdateJobdescription")).data,d=({data:e,language:t,id:a})=>new Promise(async(i,o)=>{r.yX.post(`${s.Y.opportunity}/${t}/${a}`,e).then(e=>{"en"===t&&n.Am.success("Opportunity english updated successfully"),"fr"===t&&n.Am.success("Opportunity french updated successfully"),e?.data&&i(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&o(e)})}),u=({data:e,id:t})=>new Promise(async(a,i)=>{r.yX.put(`${s.Y.opportunity}/${t}`,e).then(e=>{n.Am.success("Opportunity Commun fields updated successfully"),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})}),m=({id:e,title:t,typeOfFavourite:a})=>new Promise(async(i,o)=>{r.yX.put(`${s.Y.baseUrl}/favourite/${e}`,{type:a}).then(e=>{n.Am.success(`${a} : ${t} saved to your favorites.`),e?.data&&i(e.data)}).catch(e=>{e&&e.response&&e.response.data&&409===e.response.status&&n.Am.warning(` ${t} already in shortlist`),e&&o(e)})}),p=({resource:e,folder:t,filename:a,body:i})=>new Promise(async(o,l)=>{r.cU.post(`${s.Y.files}/uploadResume/${e}/${t}/${a}`,i.formData).then(e=>{e.data&&o(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(400===e.response.status?e.response.data.message.includes("The resume lacks essential information")?n.Am.warn(i.t("messages:requireResume")):n.Am.warn(e.response.data.message):500===e.response.status&&n.Am.error("Internal Server Error")),e&&l(e)})}),h=()=>new Promise(async(e,t)=>{try{let t=await r.yX.get(`${s.Y.seoOpportunity}`);e(t.data)}catch(e){t(e)}}),g=(e,t,a)=>new Promise(async(i,o)=>{try{let o=await r.yX.put(`${s.Y.opportunity}/${e}/${t}/desarchiver`,{archive:a});o?.data&&(n.Am.success(`opportunity ${a?"archived":"desarchived"} successfully`),i(o.data))}catch(e){n.Am.error(`Failed to ${a?"archive":"desarchive"} the opportunity.`),o(e)}}),x=({data:e,id:t})=>new Promise(async(a,i)=>{r.yX.put(`${s.Y.seoOpportunity}/${t}`,e).then(e=>{n.Am.success("Opportunity seo updated successfully"),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})})},90397:(e,t,a)=>{"use strict";a.d(t,{Z:()=>p});var s=a(10326);a(17577);var r=a(43659),n=a(98117),i=a(48260),o=a(28591),l=a(25609),c=a(10163),d=a(36690),u=a(52210),m=a(15082);let p=function({open:e,onClose:t,onConfirm:a,message:p,icon:h}){let{t:g}=(0,u.$G)();return(0,s.jsxs)(r.Z,{id:"toggle",open:e,onClose:t,"aria-labelledby":"delete-dialog-title",className:"dialog-paper",sx:{"& .MuiPaper-root":{background:"linear-gradient(#0b3051 0%, #234791 100%) !important",color:"#f8f8f8 !important",borderBottom:"transparent !important",borderRadius:"0px !important",boxShadow:"transparent !important"}},children:[s.jsx(n.Z,{sx:{m:0,p:2},id:"delete-dialog-title",children:s.jsx(i.Z,{"aria-label":"close",onClick:t,sx:{position:"absolute",right:8,top:8,color:e=>e.palette.grey[500]},children:s.jsx(d.Z,{})})}),(0,s.jsxs)(o.Z,{dividers:!0,className:"dialog-content",children:[s.jsx("div",{style:{textAlign:"center",marginBottom:"16px"},children:h}),s.jsx(l.default,{gutterBottom:!0,children:p})]}),(0,s.jsxs)(c.Z,{className:"dialog-actions",children:[s.jsx(m.default,{text:g("global:yes"),className:"btn-popup",leftIcon:!0,onClick:a}),s.jsx(m.default,{text:g("global:no"),leftIcon:!0,className:"btn-outlined-popup",onClick:t})]})]})}},55568:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c,generateMetadata:()=>l});var s=a(19510),r=a(58585),n=a(44957);let i=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\features\blog\components\new-blog\BlogPageDetails.jsx#default`);var o=a(71615);async function l({params:e}){let t=(0,o.headers)().get("cookie"),a=e.locale,s=e?.url,r=`https://www.pentabell.com/${"en"!==a?`${a}/`:""}blog/${s}/`;try{let i=await n.xk.get(`/articles/${a}/blog/${s}`,{headers:{Cookie:t}}),o=i?.data?.versions[0],l={};try{let e=(await n.xk.get(`/articles/opposite/${a}/${s}`)).data.slug;"fr"===a&&(l.fr=`https://www.pentabell.com/fr/blog/${s}/`,l.en=`https://www.pentabell.com/blog/${e}/`,l["x-default"]=`https://www.pentabell.com/blog/${e}/`,r=`https://www.pentabell.com/fr/blog/${s}/`),"en"===a&&(l.fr=`https://www.pentabell.com/fr/blog/${e}/`,l.en=`https://www.pentabell.com/blog/${s}/`,l["x-default"]=`https://www.pentabell.com/blog/${s}/`,r=`https://www.pentabell.com/blog/${s}/`)}catch(e){console.log("error",e.response.data)}if(o)return{title:o.metaTitle,description:o.metaDescription,robots:i?.data?.robotsMeta==="index"?"follow, index, max-snippet:-1, max-image-preview:large":"noindex",alternates:{canonical:r,languages:l},openGraph:{title:o?.title,description:o?.content?.replace(/<\/?[^>]+(>|$)/g," ")?.replace(/&[a-z]+;/gi,"")?.replace(/\s\s+/g," ")?.slice(0,500),url:"en"===e.locale?`https://www.pentabell.com/opportunities/${o?.url}/`:`https://www.pentabell.com/opportunities/${e.locale}/${o?.url}/`,images:[{url:o?.image?`https://www.pentabell.com/api/v1/files/${o?.image}`:null,alt:o?.alt}]}}}catch(e){return{title:"Error",description:"An error occurred while fetching the article"}}return{title:"Blog",description:"Blog page with articles and categories"}}async function c({params:e}){let t=e.locale,a=e?.url,l=(0,o.headers)().get("cookie");try{let e=await n.xk.get(`/articles/${t}/blog/${a}`,{headers:{Cookie:l}}),r=e?.data,o=r?.versions[0],c=r?._id;return(0,s.jsxs)(s.Fragment,{children:[s.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"BlogPosting",mainEntityOfPage:{"@type":"WebPage","@id":"en"===t?`https://www.pentabell.com/blog/${a}`:`https://www.pentabell.com/${t}/blog/${a}`},headline:r?.versions[0]?.title,description:r?.versions[0]?.metaDescription,image:`https://www.pentabell.com/api/v1/files/${r?.versions[0]?.image}`,publisher:{"@type":"Organization",name:"Pentabell",logo:{"@type":"ImageObject",url:"https://www.pentabell.com/logos/pentabell-logo.png"}},datePublished:r?.versions[0]?.publishDate,dateModified:r?.versions[0]?.updatedAt,keywords:r?.versions[0]?.keywords,articleSection:r?.versions[0]?.categories?.map(e=>e?.name)})}}),s.jsx(i,{id:c,article:o,language:t,url:a})]})}catch(t){(0,r.redirect)("en"===e.locale?"/blog/":`/${e.locale}/blog/`)}}},44957:(e,t,a)=>{"use strict";a.d(t,{xk:()=>n,yX:()=>r});var s=a(29712);let r=s.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),n=s.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"});s.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"}),s.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[8948,1479,1619,1336,4227,8077,6027,3718,9645,4289,1692,9712,1812,3969,4903],()=>a(66818));module.exports=s})();