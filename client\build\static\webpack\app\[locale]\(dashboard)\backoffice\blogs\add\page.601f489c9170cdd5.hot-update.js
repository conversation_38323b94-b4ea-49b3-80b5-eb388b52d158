"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/AddArticleFR.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! suneditor-react */ \"(app-pages-browser)/./node_modules/suneditor-react/dist/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(suneditor_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @feelinglovelynow/slug */ \"(app-pages-browser)/./node_modules/@feelinglovelynow/slug/dist/index.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n/* harmony import */ var suneditor_dist_css_suneditor_min_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! suneditor/dist/css/suneditor.min.css */ \"(app-pages-browser)/./node_modules/suneditor/dist/css/suneditor.min.css\");\n/* harmony import */ var react_tag_input__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-tag-input */ \"(app-pages-browser)/./node_modules/react-tag-input/dist/index.js\");\n/* harmony import */ var suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! suneditor/src/plugins */ \"(app-pages-browser)/./node_modules/suneditor/src/plugins/index.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/DatePicker/DatePicker.js\");\n/* harmony import */ var _mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/x-date-pickers/internals/demo */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/internals/demo/DemoContainer.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/x-date-pickers/AdapterDayjs */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/AdapterDayjs/AdapterDayjs.js\");\n/* harmony import */ var _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AddArticleFR(param) {\n    let { errors, touched, setFieldValue, values, onImageSelect, categories, filteredCategories, onCategoriesSelect, debounce } = param;\n    _s();\n    const handlePaste = (event, cleanData, maxCharCount)=>{\n        let html = cleanData;\n        // Correction des balises non fermées\n        html = html.replace(/<strong>(.*?)$/g, \"<strong>$1</strong>\");\n        return html;\n    };\n    const KeyCodes = {\n        comma: 188,\n        enter: 13\n    };\n    const delimiters = [\n        KeyCodes.comma,\n        KeyCodes.enter\n    ];\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [highlights, setHighlights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [publishNow, setPublishNow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const language = \"fr\";\n    const handlePhotoChange = async ()=>{\n        const selectedFile = imageInputRef.current.files[0];\n        setSelectedImage(imageInputRef.current.files[0]);\n        if (selectedFile) {\n            onImageSelect(selectedFile, language);\n        }\n    };\n    const useSaveFileHook = (0,_features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_11__.useSaveFile)();\n    const handlePhotoBlogChange = async (file, info, core, uploadHandler)=>{\n        if (file instanceof HTMLImageElement) {\n            const src = file.src;\n            if (src.startsWith(\"data:image\")) {\n                const base64Data = src.split(\",\")[1];\n                const contentType = src.match(/data:(.*?);base64/)[1];\n                const byteCharacters = atob(base64Data);\n                const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i)=>byteCharacters.charCodeAt(i));\n                const byteArray = new Uint8Array(byteNumbers);\n                const blob = new Blob([\n                    byteArray\n                ], {\n                    type: contentType\n                });\n                const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                const selectedFile = new File([\n                    blob\n                ], fileName, {\n                    type: contentType\n                });\n                await uploadFile(selectedFile, uploadHandler, core, file);\n            } else {\n                fetch(src).then((response)=>response.blob()).then((blob)=>{\n                    const contentType = blob.type;\n                    const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                    const selectedFile = new File([\n                        blob\n                    ], fileName, {\n                        type: contentType\n                    });\n                    uploadFile(selectedFile, uploadHandler, core, file);\n                }).catch((error)=>console.error(\"Error converting image URL to Blob:\", error));\n            }\n        } else {\n            console.error(\"File is not an HTMLImageElement.\");\n        }\n    };\n    const uploadFile = (selectedFile, uploadHandler, core, originalImage)=>{\n        let uuidPhoto;\n        uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_13__[\"default\"])().replace(/-/g, \"\");\n        const formData = new FormData();\n        formData.append(\"file\", selectedFile);\n        const extension = selectedFile.name.split(\".\").pop();\n        const currentYear = new Date().getFullYear();\n        useSaveFileHook.mutate({\n            resource: \"blogs\",\n            folder: currentYear.toString(),\n            filename: uuidPhoto,\n            body: {\n                formData,\n                t\n            }\n        }, {\n            onSuccess: (dataUUID)=>{\n                const uuidPhotoFileName = dataUUID.message === \"uuid exist\" ? dataUUID.uuid : `${uuidPhoto}.${extension}`;\n                const imageUrl = `${\"http://localhost:4000/api/v1\"}/files/${uuidPhotoFileName}`;\n                originalImage.src = imageUrl;\n                uploadHandler({\n                    result: [\n                        {\n                            id: uuidPhotoFileName,\n                            url: imageUrl\n                        }\n                    ]\n                });\n            },\n            onError: (error)=>{\n                console.error(\"Error uploading file:\", error);\n            }\n        });\n    };\n    const [titlefr, setTitlefr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedTitlefr = localStorage.getItem(\"titlefr\");\n        return savedTitlefr ? JSON.parse(savedTitlefr) : \"\";\n    });\n    const [metatitlefr, setMetatitlefr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetatitlefr = localStorage.getItem(\"metatitlefr\");\n        return savedMetatitlefr ? JSON.parse(savedMetatitlefr) : \"\";\n    });\n    const [metaDescriptionfr, setMetaDescriptionfr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetadescriptionfr = localStorage.getItem(\"metaDescriptionfr\");\n        return savedMetadescriptionfr ? JSON.parse(savedMetadescriptionfr) : \"\";\n    });\n    const [contentfr, setContentfr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedContentfr = localStorage.getItem(\"contentfr\");\n        return savedContentfr ? JSON.parse(savedContentfr) : \"\";\n    });\n    const handleEditorChange = (newContentfr)=>{\n        setContentfr(newContentfr);\n        setFieldValue(\"contentFR\", newContentfr);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (titlefr) {\n            localStorage.setItem(\"titlefr\", JSON.stringify(titlefr));\n        }\n        if (contentfr) {\n            localStorage.setItem(\"contentfr\", JSON.stringify(contentfr));\n        }\n        if (metatitlefr) {\n            localStorage.setItem(\"metatitlefr\", JSON.stringify(metatitlefr));\n        }\n        if (metaDescriptionfr) {\n            localStorage.setItem(\"metaDescriptionfr\", JSON.stringify(metaDescriptionfr));\n        }\n    }, [\n        titlefr,\n        contentfr,\n        metatitlefr,\n        metaDescriptionfr\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"label-pentabell\",\n                children: \"Add article French : \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:title\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"titleFR\",\n                                        type: \"text\",\n                                        value: titlefr || values.titleFR,\n                                        onChange: (e)=>{\n                                            const titleFR = e.target.value;\n                                            setFieldValue(\"titleFR\", titleFR);\n                                            setTitlefr(titleFR);\n                                            const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_6__.slug)(titleFR);\n                                            setFieldValue(\"urlFR\", url);\n                                        },\n                                        className: \"input-pentabell\" + (errors.titleFR && touched.titleFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"titleFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:categories\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                multiple: true,\n                                                className: \"input-pentabell\",\n                                                id: \"tags-standard\",\n                                                options: filteredCategories.length > 0 ? filteredCategories : categories,\n                                                getOptionLabel: (option)=>option.name,\n                                                selected: values.categoryFR.length > 0 ? (filteredCategories.length > 0 ? filteredCategories : categories).filter((category)=>values.categoryFR.includes(category.id)) : [],\n                                                onChange: (event, selectedOptions)=>{\n                                                    const categoryIds = selectedOptions.map((category)=>category.id);\n                                                    setFieldValue(\"categoryFR\", categoryIds);\n                                                    onCategoriesSelect(categoryIds);\n                                                },\n                                                renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        ...params,\n                                                        className: \"input-pentabell  multiple-select\",\n                                                        variant: \"standard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            touched.categoryFR && errors.categoryFR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"error\",\n                                children: errors.categoryFR\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Description\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"descriptionFR\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: values.descriptionFR,\n                                    onChange: (e)=>{\n                                        const descriptionFR = e.target.value;\n                                        setFieldValue(\"descriptionFR\", descriptionFR);\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.descriptionFR && touched.descriptionFR ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"descriptionFR\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Highlights\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"tags\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_20__.WithContext, {\n                                        tags: highlights,\n                                        className: \"input-pentabell\" + (errors.highlightsFR && touched.highlightsFR ? \" is-invalid\" : \"\"),\n                                        delimiters: delimiters,\n                                        handleDelete: (i)=>{\n                                            const updatedTags = highlights.filter((tag, index)=>index !== i);\n                                            setHighlights(updatedTags);\n                                            setFieldValue(\"highlightsFR\", updatedTags.map((tag)=>tag.text));\n                                        },\n                                        handleAddition: (tag)=>{\n                                            setHighlights([\n                                                ...highlights,\n                                                tag\n                                            ]);\n                                            setFieldValue(\"highlightsFR\", [\n                                                ...highlights,\n                                                tag\n                                            ].map((item)=>item.text));\n                                        },\n                                        inputFieldPosition: \"bottom\",\n                                        autocomplete: true,\n                                        allowDragDrop: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"keywordsEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((suneditor_react__WEBPACK_IMPORTED_MODULE_3___default()), {\n                setContents: contentfr || values?.contentFR || \"\",\n                onChange: handleEditorChange,\n                onPaste: handlePaste,\n                setOptions: {\n                    cleanHTML: false,\n                    disableHtmlSanitizer: true,\n                    addTagsWhitelist: \"h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button\",\n                    plugins: suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                    buttonList: [\n                        [\n                            \"undo\",\n                            \"redo\"\n                        ],\n                        [\n                            \"font\",\n                            \"fontSize\",\n                            \"formatBlock\"\n                        ],\n                        [\n                            \"bold\",\n                            \"underline\",\n                            \"italic\",\n                            \"strike\",\n                            \"subscript\",\n                            \"superscript\"\n                        ],\n                        [\n                            \"fontColor\",\n                            \"hiliteColor\"\n                        ],\n                        [\n                            \"align\",\n                            \"list\",\n                            \"lineHeight\"\n                        ],\n                        [\n                            \"outdent\",\n                            \"indent\"\n                        ],\n                        [\n                            \"table\",\n                            \"horizontalRule\",\n                            \"link\",\n                            \"image\",\n                            \"video\"\n                        ],\n                        [\n                            \"fullScreen\",\n                            \"showBlocks\",\n                            \"codeView\"\n                        ],\n                        [\n                            \"preview\",\n                            \"print\"\n                        ],\n                        [\n                            \"removeFormat\"\n                        ]\n                    ],\n                    imageUploadHandler: handlePhotoBlogChange,\n                    defaultTag: \"div\",\n                    minHeight: \"300px\",\n                    maxHeight: \"400px\",\n                    showPathLabel: false,\n                    font: [\n                        \"Proxima-Nova-Regular\",\n                        \"Proxima-Nova-Medium\",\n                        \"Proxima-Nova-Semibold\",\n                        \"Proxima-Nova-Bold\",\n                        \"Proxima-Nova-Extrabold\",\n                        \"Proxima-Nova-Black\",\n                        \"Proxima-Nova-Light\",\n                        \"Proxima-Nova-Thin\",\n                        \"Arial\",\n                        \"Times New Roman\",\n                        \"Sans-Serif\"\n                    ],\n                    charCounter: true,\n                    charCounterType: \"byte\",\n                    resizingBar: false,\n                    colorList: [\n                        // Standard Colors\n                        [\n                            \"#234791\",\n                            \"#d69b19\",\n                            \"#cc3233\",\n                            \"#009966\",\n                            \"#0b3051\",\n                            \"#2BBFAD\",\n                            \"#0b305100\",\n                            \"#0a305214\",\n                            \"#743794\",\n                            \"#ff0000\",\n                            \"#ff5e00\",\n                            \"#ffe400\",\n                            \"#abf200\",\n                            \"#00d8ff\",\n                            \"#0055ff\",\n                            \"#6600ff\",\n                            \"#ff00dd\",\n                            \"#000000\",\n                            \"#ffd8d8\",\n                            \"#fae0d4\",\n                            \"#faf4c0\",\n                            \"#e4f7ba\",\n                            \"#d4f4fa\",\n                            \"#d9e5ff\",\n                            \"#e8d9ff\",\n                            \"#ffd9fa\",\n                            \"#f1f1f1\",\n                            \"#ffa7a7\",\n                            \"#ffc19e\",\n                            \"#faed7d\",\n                            \"#cef279\",\n                            \"#b2ebf4\",\n                            \"#b2ccff\",\n                            \"#d1b2ff\",\n                            \"#ffb2f5\",\n                            \"#bdbdbd\",\n                            \"#f15f5f\",\n                            \"#f29661\",\n                            \"#e5d85c\",\n                            \"#bce55c\",\n                            \"#5cd1e5\",\n                            \"#6699ff\",\n                            \"#a366ff\",\n                            \"#f261df\",\n                            \"#8c8c8c\",\n                            \"#980000\",\n                            \"#993800\",\n                            \"#998a00\",\n                            \"#6b9900\",\n                            \"#008299\",\n                            \"#003399\",\n                            \"#3d0099\",\n                            \"#990085\",\n                            \"#353535\",\n                            \"#670000\",\n                            \"#662500\",\n                            \"#665c00\",\n                            \"#476600\",\n                            \"#005766\",\n                            \"#002266\",\n                            \"#290066\",\n                            \"#660058\",\n                            \"#222222\"\n                        ]\n                    ]\n                },\n                onImageUpload: handlePhotoBlogChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 360,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 481,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FaqSection, {\n                values: values,\n                setFieldValue: setFieldValue,\n                errors: errors,\n                touched: touched,\n                language: \"FR\",\n                debounce: debounce\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 483,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 491,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:metaTitle\"),\n                                    \" (\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: values.metaTitleFR?.length > 65 ? \" text-danger\" : \"\",\n                                        children: [\n                                            \" \",\n                                            values.metaTitleFR?.length,\n                                            \" / 65\",\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    \")\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"metaTitleFR\",\n                                        type: \"text\",\n                                        value: metatitlefr || values.metaTitleFR,\n                                        onChange: (e)=>{\n                                            const metaTitleFR = e.target.value;\n                                            setFieldValue(\"metaTitleFR\", metaTitleFR);\n                                            setMetatitlefr(metaTitleFR);\n                                        },\n                                        className: \"input-pentabell\" + (errors.metaTitleFR && touched.metaTitleFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"metaTitleFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 495,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 494,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 493,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:url\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"urlFR\",\n                                        type: \"text\",\n                                        value: values.urlFR,\n                                        onChange: (e)=>{\n                                            setFieldValue(\"urlFR\", e.target.value);\n                                        },\n                                        className: \"input-pentabell\" + (errors.urlFR && touched.urlFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"urlFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 533,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 532,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 531,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 492,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:metaDescription\"),\n                                \" (\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: values.metaDescriptionFR?.length > 160 ? \" text-danger\" : \"\",\n                                    children: [\n                                        values.metaDescriptionFR?.length,\n                                        \" / 160\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                \")\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"metaDescriptionFR\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 2,\n                                    value: metaDescriptionfr || values.metaDescriptionFR,\n                                    onChange: (e)=>{\n                                        const metaDescriptionFR = e.target.value;\n                                        setFieldValue(\"metaDescriptionFR\", metaDescriptionFR);\n                                        setMetaDescriptionfr(metaDescriptionFR);\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.metaDescriptionFR && touched.metaDescriptionFR ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"metaDescriptionFR\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 560,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 559,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 558,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 557,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:featuredImage\"),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"upload-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: `image-upload-fr`,\n                                        className: \"file-labels\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                id: `image-upload-fr`,\n                                                name: \"imageEN\",\n                                                accept: \".png, .jpg, .jpeg, .webp\",\n                                                ref: imageInputRef,\n                                                onChange: (e)=>{\n                                                    setFieldValue(\"imageFR\", e.target.files[0]);\n                                                    handlePhotoChange();\n                                                },\n                                                className: \"file-input\" + (errors.imageFR && touched.imageFR ? \" is-invalid\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"upload-area\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"icon-pic\",\n                                                            style: {\n                                                                backgroundImage: `url(\"${selectedImage ? URL.createObjectURL(selectedImage) : values.imageFR ? `${process.env.REACT_APP_API_URL}${_utils_urls__WEBPACK_IMPORTED_MODULE_7__.API_URLS.files}/${values.imageFR}` : _assets_images_add_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"].src}\")`,\n                                                                backgroundSize: \"cover\",\n                                                                backgroundRepeat: \"no-repeat\",\n                                                                backgroundPosition: \"center\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-text\",\n                                                                children: t(\"createArticle:addFeatImg\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-description\",\n                                                                children: t(\"createArticle:clickBox\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                name: \"imageFR\",\n                                                component: \"div\",\n                                                className: \"invalid-feedback error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 601,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 600,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 599,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 598,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:alt\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"altFR\",\n                                        type: \"text\",\n                                        value: values.altFR,\n                                        onChange: (e)=>{\n                                            setFieldValue(\"altFR\", e.target.value);\n                                        },\n                                        className: \"input-pentabell\" + (errors.altFR && touched.altFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"altFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 664,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 663,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 662,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:visibility\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"select-pentabell\",\n                                        variant: \"standard\",\n                                        value: _utils_constants__WEBPACK_IMPORTED_MODULE_4__.Visibility.filter((option)=>values.visibilityFR === option),\n                                        selected: values.visibilityFR,\n                                        onChange: (event)=>{\n                                            setFieldValue(\"visibilityFR\", event.target.value);\n                                        },\n                                        children: _utils_constants__WEBPACK_IMPORTED_MODULE_4__.Visibility.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                value: item,\n                                                children: item\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"visibilityEN\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 689,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 688,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 687,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:keyword\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        id: \"tags\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_20__.WithContext, {\n                                            tags: tags,\n                                            className: \"input-pentabell\" + (errors.keywordsFR && touched.keywordsFR ? \" is-invalid\" : \"\"),\n                                            delimiters: delimiters,\n                                            handleDelete: (i)=>{\n                                                const updatedTags = tags.filter((tag, index)=>index !== i);\n                                                setTags(updatedTags);\n                                                setFieldValue(\"keywordsFR\", updatedTags.map((tag)=>tag.text));\n                                            },\n                                            handleAddition: (tag)=>{\n                                                setTags([\n                                                    ...tags,\n                                                    tag\n                                                ]);\n                                                setFieldValue(\"keywordsFR\", [\n                                                    ...tags,\n                                                    tag\n                                                ].map((item)=>item.text));\n                                            },\n                                            inputFieldPosition: \"bottom\",\n                                            autocomplete: true,\n                                            allowDragDrop: false\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"keywordsFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 719,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 718,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 717,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 661,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"label-form\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Field, {\n                        type: \"checkbox\",\n                        name: \"publishNow\",\n                        checked: publishNow,\n                        onChange: (e)=>{\n                            setPublishNow(e.target.checked);\n                            if (e.target.checked) {\n                                setFieldValue(\"publishDateFR\", new Date().toISOString());\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 763,\n                        columnNumber: 9\n                    }, this),\n                    t(\"createArticle:publishNow\")\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 762,\n                columnNumber: 7\n            }, this),\n            !publishNow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"label-form\",\n                        children: [\n                            t(\"createArticle:publishDate\"),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_24__.LocalizationProvider, {\n                                dateAdapter: _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_25__.AdapterDayjs,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_26__.DemoContainer, {\n                                    components: [\n                                        \"DatePicker\"\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_27__.DatePicker, {\n                                            variant: \"standard\",\n                                            className: \"input-date\",\n                                            format: \"DD/MM/YYYY\",\n                                            value: dayjs__WEBPACK_IMPORTED_MODULE_10___default()(values.publishDateEN),\n                                            onChange: (date)=>{\n                                                setFieldValue(\"publishDateEN\", dayjs__WEBPACK_IMPORTED_MODULE_10___default()(date).format(\"YYYY-MM-DD\"));\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"publishDateEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 782,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 780,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 779,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 778,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Field, {\n                type: \"hidden\",\n                name: \"publishDateFR\",\n                value: publishNow ? new Date().toISOString() : publishDate.toISOString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 808,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AddArticleFR, \"+ONfVgda+KvaQf3X16NXIW62f9w=\", false, function() {\n    return [\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_11__.useSaveFile\n    ];\n});\n_c = AddArticleFR;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticleFR);\nvar _c;\n$RefreshReg$(_c, \"AddArticleFR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx\n"));

/***/ })

});