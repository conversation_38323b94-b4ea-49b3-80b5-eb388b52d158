"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3390],{71076:function(e,t,n){n.r(t);var a,s=n(94746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(null,arguments)}t.default=e=>s.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:18,height:18,fill:"none",viewBox:"0 0 14 14"},e),a||(a=s.createElement("path",{fill:"#798BA3",fillRule:"evenodd",d:"M2.143.978C2.503.618 2.99.417 3.5.417h4.666a.75.75 0 0 1 .53.22l3.5 3.5c.141.14.22.331.22.53v7a1.917 1.917 0 0 1-1.916 1.917h-7a1.917 1.917 0 0 1-1.917-1.917V2.334c0-.509.202-.996.561-1.356m1.356.939a.417.417 0 0 0-.417.417v9.333a.417.417 0 0 0 .417.417h7a.416.416 0 0 0 .416-.417v-6.25h-2.75a.75.75 0 0 1-.75-.75v-2.75zm5.416 1.06.94.94h-.94zm-5 2.273a.75.75 0 0 1 .75-.75h1.167a.75.75 0 1 1 0 1.5H4.665a.75.75 0 0 1-.75-.75m0 2.334a.75.75 0 0 1 .75-.75h4.667a.75.75 0 0 1 0 1.5H4.665a.75.75 0 0 1-.75-.75m0 2.333a.75.75 0 0 1 .75-.75h4.667a.75.75 0 0 1 0 1.5H4.665a.75.75 0 0 1-.75-.75",clipRule:"evenodd"})))},46359:function(e,t,n){var a,s=n(94746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(null,arguments)}t.Z=e=>s.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),a||(a=s.createElement("path",{stroke:"#1D5A9F",strokeWidth:1.5,d:"m12.398 17.396-.398-.25-.398.25-6.852 4.296V3A.25.25 0 0 1 5 2.75h14a.25.25 0 0 1 .25.25v18.692zm-8.03 4.535Z"})))},29308:function(e,t,n){n.r(t);var a,s=n(94746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(null,arguments)}t.default=e=>s.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:10,height:12,fill:"none"},e),a||(a=s.createElement("path",{fill:"#fff",fillRule:"evenodd",d:"M3.165 5.364a8 8 0 0 1-.59-.544c-.6-.62-1.076-1.378-1.076-2.32V1.333H.332V.167h9.333v1.166H8.5V2.5c0 .942-.476 1.7-1.076 2.32q-.275.281-.59.544c-.264.22-.543.432-.825.636.282.204.561.415.824.635.21.176.41.357.591.545.6.62 1.076 1.378 1.076 2.32v1.166h1.166v1.167H.332v-1.167h1.167V9.5c0-.942.476-1.7 1.075-2.32q.275-.281.59-.545c.264-.22.543-.431.825-.635-.282-.204-.56-.415-.824-.636m.128-1.24-.599.58M5 5.287c-.61-.43-1.162-.84-1.586-1.279-.495-.51-.748-.992-.748-1.508V1.333h4.667V2.5c0 .516-.253.998-.747 1.508-.424.438-.977.848-1.586 1.28m2.333 5.38H2.665V9.5c0-.516.253-.998.748-1.509.424-.438.976-.848 1.586-1.278.61.43 1.162.84 1.586 1.278.494.511.747.993.747 1.509z",clipRule:"evenodd"})))},76119:function(e,t,n){var a,s=n(94746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(null,arguments)}t.Z=e=>s.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:10,height:12,fill:"none"},e),a||(a=s.createElement("path",{fill:"#1D5A9F",fillRule:"evenodd",d:"M5 1.256c-.995 0-1.948.397-2.652 1.103A3.78 3.78 0 0 0 1.25 5.023c0 1.457.947 2.891 2.016 4.025A14 14 0 0 0 5 10.598q.13-.096.294-.228c.394-.316.918-.769 1.44-1.322C7.803 7.914 8.75 6.48 8.75 5.023c0-.999-.395-1.957-1.098-2.664A3.74 3.74 0 0 0 5 1.256m0 10.116-.347.522h-.002l-.003-.003-.012-.008a5 5 0 0 1-.19-.134A15.437 15.437 0 0 1 2.36 9.912C1.24 8.724 0 6.984 0 5.022c0-1.332.527-2.61 1.464-3.552A5 5 0 0 1 5 0c1.326 0 2.598.53 3.536 1.471A5.04 5.04 0 0 1 10 5.023c0 1.962-1.24 3.702-2.359 4.888a15.4 15.4 0 0 1-2.277 1.972l-.012.008-.003.002-.002.001zm0 0 .347.523a.62.62 0 0 1-.694 0zm0-7.186a.835.835 0 0 0-.833.837c0 .463.373.837.833.837s.833-.374.833-.837A.835.835 0 0 0 5 4.186m-2.083.837c0-1.156.932-2.093 2.083-2.093 1.15 0 2.083.937 2.083 2.093A2.09 2.09 0 0 1 5 7.116a2.09 2.09 0 0 1-2.083-2.093",clipRule:"evenodd"})))},2818:function(e,t,n){n.r(t);var a,s=n(94746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(null,arguments)}t.default=e=>s.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none"},e),a||(a=s.createElement("path",{fill:"#798BA3",d:"M7.999 14.666a6.667 6.667 0 1 1 0-13.333 6.667 6.667 0 0 1 0 13.333m0-1.333a5.333 5.333 0 1 0 0-10.667 5.333 5.333 0 0 0 0 10.667M8.665 8h2.667v1.333h-4V4.666h1.333z"})))},75638:function(e,t,n){var a=n(2265),s=n(4828),r=n.n(s);t.Z=()=>((0,a.useEffect)(()=>{r().initialize({gtmId:"GTM-NXLL5DG"})},[]),null)},78153:function(e,t,n){var a=n(57437),s=n(89051);t.Z=function(e){let{child:t,title:n,placement:r="bottom"}=e;return(0,a.jsx)(s.Z,{title:n,placement:r,componentsProps:{tooltip:{sx:{color:"#798BA3",backgroundColor:"white",fontWeight:"bold",fontSize:"16px"}}},children:t})}},93214:function(e,t,n){n.d(t,{cU:function(){return o},xk:function(){return i},yX:function(){return r}});var a=n(83464),s=n(40257);let r=a.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),i=a.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),o=a.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"});a.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))},17828:function(e,t,n){n.d(t,{Z:function(){return l}});var a=n(83464),s=n(40257),r=a.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0});a.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},withCredentials:!0}),a.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e));var i=n(40257);let o=()=>new Promise(async(e,t)=>{try{let t=await r.get(`${i.env.NEXT_PUBLIC_BASE_API_URL}/account`);e(t.data)}catch(e){t(e)}});var c=n(86484);function l(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],{data:t,error:n,isLoading:a,refetch:s}=(0,c.useQuery)({queryKey:["currentUser"],queryFn:o,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1,retry:e});return{user:t,error:n,isLoading:a,refetch:s}}},23390:function(e,t,n){n.d(t,{default:function(){return U}});var a=n(57437),s=n(31691),r=n(59873),i=n(9504),o=n(27648),c=n(2265),l=n(7261),u=n(89414),d=n(77398),p=n.n(d);n(51865);var m=n(61791),f=n(48962),y=n(55788),h=n(41774),g=n(44179),v=n(75638),x=n(39825),j=n(2818),b=n(29308),w=n(71076),N=n(76119),A=n(46359),$=n(17828),_=n(28397),E=n(62953),C=n(80657),L=n(93214),Z=n(78153),P=function(e){let{opportunity:t,language:n}=e,{t:i,i18n:d}=(0,y.$G)();m.registerLocale(f);let P=(0,s.Z)(),{user:U}=(0,$.Z)(),[O,B]=(0,c.useState)(!1),[M,T]=(0,c.useState)(!1),k=async e=>{try{await L.yX.delete(`/favourite/${e}`,{data:{type:"opportunity"}}),X(!1)}catch(e){}},R=async()=>{try{await k(M)}catch(e){}B(!1)};p().locale(d.language||"en");let S=(0,r.Z)(P.breakpoints.down("sm")),I=(0,E.UJ)(),[Y,X]=(0,c.useState)(!1);(0,c.useEffect)(()=>{(async()=>{if(t?._id)try{let e=await L.yX.get(`/favourite/is-saved/${t?._id}`);X(e.data)}catch(e){}})()},[t?._id]);let D=e=>{window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"opportunity_view",button_id:"my_button"}),setTimeout(()=>{window.location.href=e},300)},F=i("createOpportunity:summary"),H=RegExp(`<strong>${F}:</strong><br>([\\s\\S]*?)(?=<br>)`,"i"),z=t?.versions[d.language]?.jobDescription?.match(H)?.[1]?.trim()??"";return(0,a.jsxs)("div",{className:"button-pointer",onClick:()=>{S&&D(`/${C.Bi.opportunities.route}/${t?.versions[n]?.url}`)},children:[(0,a.jsx)(v.Z,{}),(0,a.jsxs)(u.default,{className:"container opportunity-item",container:!0,spacing:0,children:[(0,a.jsxs)(u.default,{container:!0,spacing:0,className:"flex-item row",children:[(0,a.jsx)(Z.Z,{title:t?.versions?.[n]?.title||t?.title,child:(0,a.jsx)("a",{href:`/${C.Bi.opportunities.route}/${t?.versions?.[n]?.url||t?.url}`,className:"btn p-0 job-title",children:(0,g.rZ)(t?.versions?.[n]?.title||t?.title,80)})}),(0,a.jsxs)("div",{className:"flex-item",children:[(0,g.f8)(t?.industry)?(0,a.jsx)(o.default,{style:{textDecoration:"none"},href:`/${C.Bi.jobCategory.route}/${(0,g.Gc)(t?.industry)}`,children:(0,a.jsxs)("p",{className:`job-industry border ${(0,g.jX)(t?.industry)}`,children:[(0,g.y9)(t?.industry)," ",(0,g.sC)(t?.industry)]})}):null,!S&&(!U||U?.roles?.includes(_.uU.CANDIDATE))?(0,a.jsx)(h.default,{icon:(0,a.jsx)(A.Z,{className:`${Y?"btn-filled-yellow":""}`}),onClick:Y?()=>{T(t?._id),B(!0)}:()=>{U?I.mutate({id:t?._id,title:t?.versions[n]?.title,typeOfFavourite:"opportunity"},{onSuccess:()=>{X(!0)}}):l.Am.warning("Login or create account to save opportunity.")},className:"btn btn-ghost bookmark"}):(0,a.jsx)("div",{}),S&&(!U||U?.roles?.includes(_.uU.CANDIDATE))&&(0,a.jsx)(h.default,{icon:(0,a.jsx)(A.Z,{className:`${Y?"btn-filled-yellow ":""}`}),className:"btn btn-ghost bookmark"})]})]}),(0,a.jsxs)(u.default,{container:!0,spacing:0,className:"flex-item margin-section-item",children:[(0,a.jsxs)("p",{className:"job-ref",children:["Ref: ",t?.reference]}),(0,a.jsxs)("a",{className:"location",href:`/${C.Bi.jobLocation.route}/${t?.country.toLowerCase()}`,children:[(0,a.jsx)(N.Z,{}),(0,a.jsx)("p",{className:"location-text",children:t?.country})]})]}),(0,a.jsx)(u.default,{container:!0,spacing:0,className:"flex-item margin-section-item",children:(0,a.jsx)("div",{className:"job-description",dangerouslySetInnerHTML:{__html:z}})}),(0,a.jsxs)(u.default,{container:!0,spacing:0,className:"flex-item row",children:[(0,a.jsx)("div",{className:"flex-apply",children:(0,a.jsxs)("div",{className:"job-contrat-time",children:[(0,a.jsxs)("p",{className:"job-contract",children:[(0,a.jsx)(w.default,{}),t?.contractType||"Agreement"]}),(0,a.jsxs)("p",{className:"job-deadline",children:[(0,a.jsx)(b.default,{}),t?.dateOfExpiration?(0,g.fm)(p()(t?.dateOfExpiration).format("DD MMMM YYYY")):"N/A"]}),(0,a.jsxs)("p",{className:"job-time",children:[(0,a.jsx)(j.default,{}),t?.versions[n]?.createdAt?(0,g.fm)(p()(t?.versions[n]?.createdAt).format("DD MMMM YYYY")):"N/A"]})]})}),(0,a.jsx)("div",{className:"item-btns",children:(0,a.jsx)(h.default,{text:i("global:applyNow"),className:"btn btn-search btn-filled apply",onClick:()=>D(`/${C.Bi.opportunities.route}/${t?.versions[n]?.url}`)})})]})]},t?._id),(0,a.jsx)(x.Z,{open:O,message:i("messages:supprimeropportunityfavoris"),onClose:()=>{B(!1)},onConfirm:R})]})};function U(e){let{key:t,opportunity:n,language:o,isList:c}=e,l=(0,s.Z)(),u=(0,r.Z)(l.breakpoints.down("sm"));return c&&!u?(0,a.jsx)(P,{opportunity:n,language:o},t):(0,a.jsx)(i.Z,{opportunity:n,language:o},t)}},9504:function(e,t,n){var a=n(57437),s=n(27648),r=n(2265),i=n(7261),o=n(31691),c=n(59873),l=n(89414),u=n(77398),d=n.n(u);n(51865);var p=n(61791),m=n(48962),f=n(55788),y=n(41774),h=n(44179),g=n(75638),v=n(39825),x=n(29308),j=n(71076),b=n(46359),w=n(76119),N=n(17828),A=n(28397),$=n(62953),_=n(80657),E=n(93214),C=n(78153);t.Z=function(e){let{opportunity:t,language:n,website:u}=e,{t:L,i18n:Z}=(0,f.$G)();p.registerLocale(m);let P=(0,o.Z)(),{user:U}=(0,N.Z)(),[O,B]=(0,r.useState)(!1),[M,T]=(0,r.useState)(!1),k=async e=>{try{await E.yX.delete(`/favourite/${e}`,{data:{type:"opportunity"}}),X(!1)}catch(e){}},R=async()=>{try{await k(M)}catch(e){}B(!1)};d().locale(Z.language||"en");let S=(0,c.Z)(P.breakpoints.down("sm")),I=(0,$.UJ)(),[Y,X]=(0,r.useState)(!1);(0,r.useEffect)(()=>{(async()=>{if(t?._id)try{let e=await E.yX.get(`/favourite/is-saved/${t?._id}`);X(e.data)}catch(e){}})()},[t?._id]);let D=e=>{window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"opportunity_view",button_id:"my_button"}),setTimeout(()=>{window.location.href=e},300)};return(0,a.jsxs)("div",{className:"button-pointer",onClick:()=>{S&&D(`/${_.Bi.opportunities.route}/${t?.versions[n]?.url}`)},children:[(0,a.jsx)(g.Z,{}),(0,a.jsxs)(l.default,{className:`container opportunity-grid-item ${u?"website":""}`,container:!0,children:[(0,a.jsx)(l.default,{item:!0,xs:3,sm:3,className:"item-image",children:(0,a.jsx)(s.default,{href:`/${_.Bi.jobCategory.route}/${(0,h.Gc)(t?.industry)}`,children:(0,h.f8)(t?.industry)?(0,h.bO)(t?.industry):null})}),(0,a.jsxs)(l.default,{item:!0,xs:9,sm:9,className:"item-content",children:[(0,a.jsxs)("div",{className:"flex row",children:[(0,a.jsx)("div",{className:"flex-item mobile-col",children:(0,a.jsx)(C.Z,{title:t?.versions?.[n]?.title||t?.title,child:(0,a.jsx)("a",{href:`/${_.Bi.opportunities.route}/${t?.versions?.[n]?.url||t?.url}`,className:"btn p-0 job-title",children:(0,h.rZ)(t?.versions?.[n]?.title||t?.title,30)})})}),(!U||U?.roles?.includes(A.uU.CANDIDATE))&&(0,a.jsx)(y.default,{icon:(0,a.jsx)(b.Z,{className:`${Y?"btn-filled-yellow":""}`}),onClick:Y?()=>{T(t?._id),B(!0)}:()=>{U?I.mutate({id:t?._id,title:t?.versions[n]?.title||t?.title,typeOfFavourite:"opportunity"},{onSuccess:()=>{X(!0)}}):i.Am.warning("Login or create account to save opportunity.")},className:"btn btn-ghost bookmark"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsxs)("p",{className:"job-ref",children:["Ref: ",t?.reference]}),!S&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("p",{className:"job-contract",children:[(0,a.jsx)(j.default,{}),t?.contractType||"Agreement"]}),(0,a.jsxs)("p",{className:"job-deadline",children:[(0,a.jsx)(x.default,{}),t?.dateOfExpiration?(0,h.fm)(d()(t?.dateOfExpiration).format("DD MMMM YYYY")):"N/A"]})]})]}),(0,a.jsxs)("div",{className:"flex row",children:[(0,a.jsxs)("a",{className:"location",href:`/${_.Bi.jobLocation.route}/${t?.country.toLowerCase()}`,children:[(0,a.jsx)(w.Z,{}),(0,a.jsx)("p",{className:"location-text",children:t?.country})]}),!S&&(0,a.jsx)(y.default,{text:L("global:applyNow"),className:"btn btn-search btn-filled apply",onClick:()=>D(`/${_.Bi.opportunities.route}/${t?.versions?.[n]?.url||t?.url}`)})]})]}),S&&(0,a.jsxs)(l.default,{item:!0,xs:12,sm:12,className:"item-apply",children:[(0,a.jsxs)("div",{className:"flex contract",children:[(0,a.jsxs)("p",{className:"job-contract",children:[(0,a.jsx)(j.default,{}),t?.contractType||"Agreement"]}),(0,a.jsxs)("p",{className:"job-deadline",children:[(0,a.jsx)(x.default,{}),t?.dateOfExpiration?(0,h.fm)(d()(t?.dateOfExpiration).format("DD MMMM YYYY")):"N/A"]})]}),(0,a.jsx)(y.default,{text:L("global:applyNow"),className:"btn btn-outlined apply",onClick:()=>D(`/${_.Bi.opportunities.route}/${t?.versions[n]?.url}`)})]})]},t?._id),(0,a.jsx)(v.Z,{open:O,message:L("messages:supprimeropportunityfavoris"),onClose:()=>{B(!1)},onConfirm:R})]})}},62953:function(e,t,n){n.d(t,{$i:function(){return y},BF:function(){return f},Fe:function(){return i},Gc:function(){return u},HF:function(){return r},Hr:function(){return c},IZ:function(){return m},NF:function(){return l},PM:function(){return o},UJ:function(){return d},jd:function(){return p}});var a=n(86484),s=n(49443);n(99376),n(80657);let r=()=>(0,a.useMutation)({mutationFn:e=>(0,s.W3)(e),onError:e=>{e.message=""}}),i=e=>(0,a.useQuery)("opportunities",async()=>await (0,s.fH)(e)),o=()=>(0,a.useMutation)(()=>(0,s.AE)()),c=e=>(0,a.useQuery)(["opportunities",e],async()=>await (0,s.Mq)(e)),l=()=>(0,a.useMutation)({mutationFn:(e,t,n)=>(0,s.rE)(e,t,n),onError:e=>{e.message=""}}),u=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t)=>(0,s.S1)(e,t),onError:e=>{e.message=""}})),d=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t,n)=>(0,s.lU)(e,t,n),onError:e=>{e.message=""}})),p=()=>{let e=(0,a.useQueryClient)();return(0,a.useMutation)({mutationFn:(e,t,n,a)=>(0,s.yH)(e,t,n,a),onSuccess:t=>{e.invalidateQueries("files")}})},m=()=>(0,a.useQuery)("SeoOpportunities",async()=>await (0,s.yJ)()),f=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t)=>(0,s.mt)(e,t),onError:e=>{e.message=""}})),y=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:e=>{let{language:t,id:n,archive:a}=e;return(0,s.TK)(t,n,a)},onError:e=>{console.error("Error during mutation",e),e.message=""}}))},49443:function(e,t,n){n.d(t,{AE:function(){return l},Mq:function(){return c},S1:function(){return d},TK:function(){return y},W3:function(){return i},fH:function(){return o},lU:function(){return p},mt:function(){return h},rE:function(){return u},yH:function(){return m},yJ:function(){return f}});var a=n(46172),s=n(93214),r=n(7261);let i=e=>(e.t,new Promise(async(t,n)=>{s.yX.post(`/opportunities${a.Y.applications}/${e.opportunityId}`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&n(e)})})),o=e=>new Promise(async(t,n)=>{try{let n=await s.yX.get(`${a.Y.opportunity}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyWord:e.keyWord,visibility:e.visibility,title:e.title,createdAt:e.createdAt,publishDate:e.publishDate,country:e.country,industry:e.industry,contractType:e.contractType,minExperience:e.minExperience,maxExperience:e.maxExperience,jobDescriptionLanguages:e.jobDescriptionLanguages,reference:e.reference,opportunityType:e.opportunityType,exclude:"In House"!==e.opportunityType&&"true"}});t(n.data)}catch(e){n(e)}}),c=e=>new Promise(async(t,n)=>{try{let n=await s.yX.get(`${a.Y.opportunity}/${e}`);t(n.data)}catch(e){n(e)}}),l=async()=>(await s.xk.put("/UpdateJobdescription")).data,u=e=>{let{data:t,language:n,id:i}=e;return new Promise(async(e,o)=>{s.yX.post(`${a.Y.opportunity}/${n}/${i}`,t).then(t=>{"en"===n&&r.Am.success("Opportunity english updated successfully"),"fr"===n&&r.Am.success("Opportunity french updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&o(e)})})},d=e=>{let{data:t,id:n}=e;return new Promise(async(e,i)=>{s.yX.put(`${a.Y.opportunity}/${n}`,t).then(t=>{r.Am.success("Opportunity Commun fields updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})})},p=e=>{let{id:t,title:n,typeOfFavourite:i}=e;return new Promise(async(e,o)=>{s.yX.put(`${a.Y.baseUrl}/favourite/${t}`,{type:i}).then(t=>{r.Am.success(`${i} : ${n} saved to your favorites.`),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data&&409===e.response.status&&r.Am.warning(` ${n} already in shortlist`),e&&o(e)})})},m=e=>{let{resource:t,folder:n,filename:i,body:o}=e;return new Promise(async(e,c)=>{s.cU.post(`${a.Y.files}/uploadResume/${t}/${n}/${i}`,o.formData).then(t=>{t.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data&&(400===e.response.status?e.response.data.message.includes("The resume lacks essential information")?r.Am.warn(o.t("messages:requireResume")):r.Am.warn(e.response.data.message):500===e.response.status&&r.Am.error("Internal Server Error")),e&&c(e)})})},f=()=>new Promise(async(e,t)=>{try{let t=await s.yX.get(`${a.Y.seoOpportunity}`);e(t.data)}catch(e){t(e)}}),y=(e,t,n)=>new Promise(async(i,o)=>{try{let o=await s.yX.put(`${a.Y.opportunity}/${e}/${t}/desarchiver`,{archive:n});o?.data&&(r.Am.success(`opportunity ${n?"archived":"desarchived"} successfully`),i(o.data))}catch(e){r.Am.error(`Failed to ${n?"archive":"desarchive"} the opportunity.`),o(e)}}),h=e=>{let{data:t,id:n}=e;return new Promise(async(e,i)=>{s.yX.put(`${a.Y.seoOpportunity}/${n}`,t).then(t=>{r.Am.success("Opportunity seo updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})})}},39825:function(e,t,n){var a=n(57437);n(2265);var s=n(35791),r=n(79507),i=n(59832),o=n(77468),c=n(46387),l=n(9026),u=n(8430),d=n(55788),p=n(41774);t.Z=function(e){let{open:t,onClose:n,onConfirm:m,message:f,icon:y}=e,{t:h}=(0,d.$G)();return(0,a.jsxs)(s.Z,{id:"toggle",open:t,onClose:n,"aria-labelledby":"delete-dialog-title",className:"dialog-paper",sx:{"& .MuiPaper-root":{background:"linear-gradient(#0b3051 0%, #234791 100%) !important",color:"#f8f8f8 !important",borderBottom:"transparent !important",borderRadius:"0px !important",boxShadow:"transparent !important"}},children:[(0,a.jsx)(r.Z,{sx:{m:0,p:2},id:"delete-dialog-title",children:(0,a.jsx)(i.Z,{"aria-label":"close",onClick:n,sx:{position:"absolute",right:8,top:8,color:e=>e.palette.grey[500]},children:(0,a.jsx)(u.Z,{})})}),(0,a.jsxs)(o.Z,{dividers:!0,className:"dialog-content",children:[(0,a.jsx)("div",{style:{textAlign:"center",marginBottom:"16px"},children:y}),(0,a.jsx)(c.default,{gutterBottom:!0,children:f})]}),(0,a.jsxs)(l.Z,{className:"dialog-actions",children:[(0,a.jsx)(p.default,{text:h("global:yes"),className:"btn-popup",leftIcon:!0,onClick:m}),(0,a.jsx)(p.default,{text:h("global:no"),leftIcon:!0,className:"btn-outlined-popup",onClick:n})]})]})}},46172:function(e,t,n){n.d(t,{Y:function(){return s},v:function(){return a}});let a=n(40257).env.NEXT_PUBLIC_BASE_API_URL,s={seo:"seoTags",auth:"/auth/signin",logout:"/auth/logout",candidatures:"/applications",signup:"/auth/signup",forgetPassword:"/auth/forgot-password",resetPassword:"/auth/reset-password",guides:"/guides",currentUser:"/users/current",updateUser:"/users",users:"/users",categoryGuides:"guidecategory",candidate:"/candidates",report:"/report",skills:"/skills",files:"/files",applications:"/applications",sliders:"/sliders",favoris:"/candidate/favourite",articles:"/articles",categories:"/categories",blog:"/blog",category:"/categories",opportunity:"/opportunities",seoOpportunity:"/seoOpportunity",newsletter:"/newsletter",contact:"/contact",favourite:"/favourite",contacts:"contacts",comments:"/comments",statistics:"/statistics",events:"/events",baseUrl:`${a}`}}}]);