import { axiosGetJsonSSR } from "@/config/axios";
import ApplyPage from "@/features/application/component/ApplyPage";
import { websiteRoutesList } from "@/helpers/routesList";
import { redirect } from "next/navigation";

import { isExpired } from "../../../../../utils/functions";

export async function generateMetadata({ params }) {
  try {
    const opportunityData = await fetchData(params);
    const data = await axiosGetJsonSSR.get(`/seoOpportunity`);
    return {
      title:
        data?.data?.metaTitle ||
        "" + opportunityData?.versions[params.locale]?.title,
      description: data?.data?.metaDescription || "",
      robots: "nofollow, noindex",
    };
  } catch (error) {
    console.error("Error fetching opportunity metadata:", error);
    redirect(`/${params.locale}/${websiteRoutesList.opportunities.route}`);
  }
}

async function fetchData(params) {
  try {
    const res = await axiosGetJsonSSR.get(
      `/${websiteRoutesList.opportunities.route}/${params.locale}/${params.opportunity}`
    );
    return res.data;
  } catch (error) {
    console.error("Error fetching opportunity data:", error);
    redirect(`/${params.locale}/${websiteRoutesList.opportunities.route}`);
  }
}

export default async function page({ params, searchParams }) {
  const { error, success } = searchParams;

  const data = await fetchData(params);

  if (isExpired(data?.dateOfExpiration))
    redirect(
      `/${params.locale === "en" ? "" : params.locale + "/"}${
      websiteRoutesList.opportunities.route
    }/`
    );

  const industry = data?.industry;

  if (!industry) {
    console.error(
      "Industry not found for the opportunity. Related jobs cannot be fetched."
    );
    return null;
  }

  return (
    <ApplyPage data={data} params={params} success={success} error={error} />
  );
}
