"use client";
import { Stack, Chip } from "@mui/material";

const FilterChips = ({ selectedFilters }) => {
  return (
    <Stack
      className="checkbox-pentabell-delete check"
      direction="row"
      spacing={1}
      sx={{ marginTop: "20px", flexWrap: "wrap", mb: 2 }}
    >
      {Array.isArray(selectedFilters) &&
        selectedFilters.map((filter, index) => (
          <Chip
            key={index}
            label={`${filter.label}`}
            sx={{
              backgroundColor: "transparent",
              borderColor: "#1D5A9F !important",
              marginTop: "10px !important",
              border: "1px solid",
              padding: "8px 20px",
              "&:hover": {
                backgroundColor: "transparent",
              },
              ".css-1dybbl5-MuiChip-label": {
                fontSize: "14px",
                fontFamily: "Proxima-Nova-Medium",
                color: "#1D5A9F",
                "!important": true,
              },
            }}
          />
        ))}
    </Stack>
  );
};

export default FilterChips;
