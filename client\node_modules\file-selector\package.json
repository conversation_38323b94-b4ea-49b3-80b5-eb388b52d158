{"name": "file-selector", "version": "2.1.2", "description": "Convert DataTransfer object to a list of File objects", "main": "./dist/index.js", "module": "./dist/es2015/index.js", "types": "./dist/index.d.ts", "files": ["dist/**/*", "src/*", "!*.spec.*"], "keywords": ["drag-and-drop", "html5", "file-api", "DataTransfer", "File"], "homepage": "https://github.com/react-dropzone/file-selector", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/react-dropzone/file-selector.git"}, "publishConfig": {"access": "public"}, "scripts": {"prebuild": "npm run clean", "build": "npm-run-all -s compile build:umd", "build:umd": "rollup -c ./rollup.config.mjs", "compile": "npm-run-all -p compile:es2015 compile:cjs compile:types", "compile:es2015": "tsc -p ./tsconfig.es2015.json", "compile:cjs": "tsc -p ./tsconfig.cjs.json", "compile:types": "tsc -p ./tsconfig.types.json", "clean": "rm -rf dist/*", "lint": "tslint -c tslint.json -p ./tsconfig.spec.json -t stylish", "lint:fix": "npm run lint -- --fix", "pretest:cov": "npm run lint", "test:cov": "jest --coverage", "test": "jest --watch"}, "dependencies": {"tslib": "^2.7.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-terser": "^0.4.4", "@types/jest": "^29.5.13", "@types/node": "^22.7.5", "camelcase": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "npm-run-all": "^4.1.5", "rollup": "^4.24.0", "ts-jest": "^29.2.5", "tslint": "^6.1.3", "typescript": "^5.6.2"}, "engines": {"node": ">= 12"}}