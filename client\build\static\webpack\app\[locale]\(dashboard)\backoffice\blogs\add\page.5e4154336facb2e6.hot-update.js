"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/AddArticleEN.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! suneditor-react */ \"(app-pages-browser)/./node_modules/suneditor-react/dist/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(suneditor_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n/* harmony import */ var suneditor_dist_css_suneditor_min_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! suneditor/dist/css/suneditor.min.css */ \"(app-pages-browser)/./node_modules/suneditor/dist/css/suneditor.min.css\");\n/* harmony import */ var _feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @feelinglovelynow/slug */ \"(app-pages-browser)/./node_modules/@feelinglovelynow/slug/dist/index.js\");\n/* harmony import */ var suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! suneditor/src/plugins */ \"(app-pages-browser)/./node_modules/suneditor/src/plugins/index.js\");\n/* harmony import */ var _FaqSection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./FaqSection */ \"(app-pages-browser)/./src/features/blog/components/FaqSection.jsx\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/DatePicker/DatePicker.js\");\n/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/x-date-pickers/AdapterDayjs */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/AdapterDayjs/AdapterDayjs.js\");\n/* harmony import */ var _mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/x-date-pickers/internals/demo */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/internals/demo/DemoContainer.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var react_tag_input__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! react-tag-input */ \"(app-pages-browser)/./node_modules/react-tag-input/dist/index.js\");\n/* harmony import */ var _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomTextInput */ \"(app-pages-browser)/./src/components/ui/CustomTextInput.jsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AddArticleEN(param) {\n    let { errors, touched, setFieldValue, values, onImageSelect, filteredCategories, categories, onCategoriesSelect, debounce } = param;\n    _s();\n    const KeyCodes = {\n        comma: 188,\n        enter: 13\n    };\n    const delimiters = [\n        KeyCodes.comma,\n        KeyCodes.enter\n    ];\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [highlights, setHighlights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(values.descriptionEN || \"\");\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(values.urlEN || \"\");\n    const [publishNow, setPublishNow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const language = \"en\";\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedTitle = localStorage.getItem(\"title\");\n        return savedTitle ? JSON.parse(savedTitle) : \"\";\n    });\n    const [metatitle, setMetatitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetatitle = localStorage.getItem(\"metatitle\");\n        return savedMetatitle ? JSON.parse(savedMetatitle) : \"\";\n    });\n    const [metaDescription, setMetaDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetadescription = localStorage.getItem(\"metaDescription\");\n        return savedMetadescription ? JSON.parse(savedMetadescription) : \"\";\n    });\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedContent = localStorage.getItem(\"content\");\n        return savedContent ? JSON.parse(savedContent) : \"\";\n    });\n    const handlePaste = (event, cleanData, maxCharCount)=>{\n        let html = cleanData;\n        // Correction des balises non fermées\n        html = html.replace(/<strong>(.*?)$/g, \"<strong>$1</strong>\");\n        return html;\n    };\n    const handleEditorChange = (newContent)=>{\n        debounce();\n        setContent(newContent);\n        setFieldValue(\"contentEN\", newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (title) {\n            localStorage.setItem(\"title\", JSON.stringify(title));\n        }\n        if (content) {\n            localStorage.setItem(\"content\", JSON.stringify(content));\n        }\n        if (metatitle) {\n            localStorage.setItem(\"metatitle\", JSON.stringify(metatitle));\n        }\n        if (metaDescription) {\n            localStorage.setItem(\"metaDescription\", JSON.stringify(metaDescription));\n        }\n    }, [\n        title,\n        content,\n        metatitle,\n        metaDescription\n    ]);\n    const handlePhotoChange = async ()=>{\n        const selectedFile = imageInputRef.current.files[0];\n        setSelectedImage(imageInputRef.current.files[0]);\n        if (selectedFile) {\n            onImageSelect(selectedFile, language);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setFieldValue(\"titleEN\", title);\n        setFieldValue(\"descriptionEN\", description);\n        setFieldValue(\"urlEN\", url);\n        setFieldValue(\"keywordsEN\", tags.map((t)=>t.text));\n        setFieldValue(\"highlightsEN\", highlights.map((h)=>h.text));\n        setFieldValue(\"contentEN\", content);\n        setFieldValue(\"metaTitleEN\", metatitle);\n        setFieldValue(\"metaDescriptionEN\", metaDescription);\n    }, [\n        title,\n        description,\n        url,\n        tags,\n        highlights,\n        content,\n        metatitle,\n        metaDescription\n    ]);\n    const useSaveFileHook = (0,_features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__.useSaveFile)();\n    let uuidPhoto;\n    uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n    const handlePhotoBlogChange = async (file, info, core, uploadHandler)=>{\n        if (file instanceof HTMLImageElement) {\n            const src = file.src;\n            if (src.startsWith(\"data:image\")) {\n                const base64Data = src.split(\",\")[1];\n                const contentType = src.match(/data:(.*?);base64/)[1];\n                const byteCharacters = atob(base64Data);\n                const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i)=>byteCharacters.charCodeAt(i));\n                const byteArray = new Uint8Array(byteNumbers);\n                const blob = new Blob([\n                    byteArray\n                ], {\n                    type: contentType\n                });\n                const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                const selectedFile = new File([\n                    blob\n                ], fileName, {\n                    type: contentType\n                });\n                await uploadFile(selectedFile, uploadHandler, core, file);\n            } else {\n                fetch(src).then((response)=>response.blob()).then((blob)=>{\n                    const contentType = blob.type;\n                    const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                    const selectedFile = new File([\n                        blob\n                    ], fileName, {\n                        type: contentType\n                    });\n                    uploadFile(selectedFile, uploadHandler, core, file);\n                }).catch((error)=>console.error(\"Error converting image URL to Blob:\", error));\n            }\n        } else {\n            console.error(\"File is not an HTMLImageElement.\");\n        }\n    };\n    const uploadFile = (selectedFile, uploadHandler, core, originalImage)=>{\n        let uuidPhoto;\n        uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n        const formData = new FormData();\n        formData.append(\"file\", selectedFile);\n        const extension = selectedFile.name.split(\".\").pop();\n        const currentYear = new Date().getFullYear();\n        useSaveFileHook.mutate({\n            resource: \"blogs\",\n            folder: currentYear.toString(),\n            filename: uuidPhoto,\n            body: {\n                formData,\n                t\n            }\n        }, {\n            onSuccess: (dataUUID)=>{\n                const uuidPhotoFileName = dataUUID.message === \"uuid exist\" ? dataUUID.uuid : `${uuidPhoto}.${extension}`;\n                const imageUrl = `${\"http://localhost:4000/api/v1\"}/files/${uuidPhotoFileName}`;\n                originalImage.src = imageUrl;\n                uploadHandler({\n                    result: [\n                        {\n                            id: uuidPhotoFileName,\n                            url: imageUrl\n                        }\n                    ]\n                });\n            },\n            onError: (error)=>{\n                console.error(\"Error uploading file:\", error);\n            }\n        });\n    };\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleFileChange = async (event)=>{\n        const file = event.target.files[0];\n        if (!file || file.type !== \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\") {\n            setError(\"Please upload a valid .docx file.\");\n            return;\n        }\n        const reader = new FileReader();\n        reader.onload = async (event)=>{\n            const arrayBuffer = event.target.result;\n            try {\n                const result = await mammoth__WEBPACK_IMPORTED_MODULE_4__.convertToHtml({\n                    arrayBuffer\n                });\n                setFieldValue(\"contentEN\", result.value);\n                set;\n                setError(null);\n            } catch (err) {\n                console.error(\"Mammoth conversion error:\", err);\n                setError(\"Failed to convert the DOCX file.\");\n            }\n        };\n        reader.readAsArrayBuffer(file);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"label-pentabell\",\n                children: \"Add article English : \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"file\",\n                accept: \".docx\",\n                onChange: handleFileChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: \"red\"\n                },\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 261,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    label: t(\"createArticle:title\"),\n                                    name: \"titleEN\",\n                                    value: title,\n                                    onChange: (e)=>{\n                                        const v = e.target.value;\n                                        setTitle(v);\n                                        setUrl((0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_10__.slug)(v));\n                                        debounce();\n                                    },\n                                    error: touched.titleEN && errors.titleEN\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:categories\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                multiple: true,\n                                                className: \"input-pentabell\",\n                                                id: \"tags-standard\",\n                                                options: filteredCategories.length > 0 ? filteredCategories : categories,\n                                                getOptionLabel: (option)=>option.name,\n                                                selected: values.categoryEN.length > 0 ? (filteredCategories.length > 0 ? filteredCategories : categories).filter((category)=>values.categoryEN.includes(category.id)) : [],\n                                                onChange: (event, selectedOptions)=>{\n                                                    const categoryIds = selectedOptions.map((category)=>category.id);\n                                                    setFieldValue(\"categoryEN\", categoryIds);\n                                                    onCategoriesSelect(categoryIds);\n                                                },\n                                                renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        ...params,\n                                                        className: \"input-pentabell  multiple-select\",\n                                                        variant: \"standard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            touched.category && errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"label-error\",\n                                children: errors.category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Description\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"descriptionEN\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: values.descriptionEN,\n                                    onChange: (e)=>{\n                                        const descriptionEN = e.target.value;\n                                        setFieldValue(\"descriptionEN\", descriptionEN);\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.descriptionEN && touched.descriptionEN ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"descriptionEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 332,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Highlights\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"tags\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                        tags: highlights,\n                                        className: \"input-pentabell\" + (errors.highlightsEN && touched.highlightsEN ? \" is-invalid\" : \"\"),\n                                        delimiters: delimiters,\n                                        handleDelete: (i)=>{\n                                            const updatedTags = highlights.filter((tag, index)=>index !== i);\n                                            setHighlights(updatedTags);\n                                            setFieldValue(\"highlightsEN\", updatedTags.map((tag)=>tag.text));\n                                        },\n                                        handleAddition: (tag)=>{\n                                            setHighlights([\n                                                ...highlights,\n                                                tag\n                                            ]);\n                                            setFieldValue(\"highlightsEN\", [\n                                                ...highlights,\n                                                tag\n                                            ].map((item)=>item.text));\n                                        },\n                                        inputFieldPosition: \"bottom\",\n                                        autocomplete: true,\n                                        allowDragDrop: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"keywordsEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 367,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((suneditor_react__WEBPACK_IMPORTED_MODULE_3___default()), {\n                setContents: content || values?.contentEN || \"\",\n                onChange: (newContent)=>{\n                    setContent(newContent);\n                    setFieldValue(\"contentEN\", newContent);\n                    debounce();\n                },\n                onPaste: handlePaste,\n                setOptions: {\n                    cleanHTML: false,\n                    disableHtmlSanitizer: true,\n                    addTagsWhitelist: \"h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button\",\n                    plugins: suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                    buttonList: [\n                        [\n                            \"undo\",\n                            \"redo\"\n                        ],\n                        [\n                            \"font\",\n                            \"fontSize\",\n                            \"formatBlock\"\n                        ],\n                        [\n                            \"bold\",\n                            \"underline\",\n                            \"italic\",\n                            \"strike\",\n                            \"subscript\",\n                            \"superscript\"\n                        ],\n                        [\n                            \"fontColor\",\n                            \"hiliteColor\"\n                        ],\n                        [\n                            \"align\",\n                            \"list\",\n                            \"lineHeight\"\n                        ],\n                        [\n                            \"outdent\",\n                            \"indent\"\n                        ],\n                        [\n                            \"table\",\n                            \"horizontalRule\",\n                            \"link\",\n                            \"image\",\n                            \"video\"\n                        ],\n                        [\n                            \"fullScreen\",\n                            \"showBlocks\",\n                            \"codeView\"\n                        ],\n                        [\n                            \"preview\",\n                            \"print\"\n                        ],\n                        [\n                            \"removeFormat\"\n                        ]\n                    ],\n                    imageUploadHandler: handlePhotoBlogChange,\n                    defaultTag: \"div\",\n                    minHeight: \"300px\",\n                    maxHeight: \"400px\",\n                    showPathLabel: false,\n                    font: [\n                        \"Proxima-Nova-Regular\",\n                        \"Proxima-Nova-Medium\",\n                        \"Proxima-Nova-Semibold\",\n                        \"Proxima-Nova-Bold\",\n                        \"Proxima-Nova-Extrabold\",\n                        \"Proxima-Nova-Black\",\n                        \"Proxima-Nova-Light\",\n                        \"Proxima-Nova-Thin\",\n                        \"Arial\",\n                        \"Times New Roman\",\n                        \"Sans-Serif\"\n                    ],\n                    charCounter: true,\n                    charCounterType: \"byte\",\n                    resizingBar: false,\n                    colorList: [\n                        // Standard Colors\n                        [\n                            \"#234791\",\n                            \"#d69b19\",\n                            \"#cc3233\",\n                            \"#009966\",\n                            \"#0b3051\",\n                            \"#2BBFAD\",\n                            \"#0b305100\",\n                            \"#0a305214\",\n                            \"#743794\",\n                            \"#ff0000\",\n                            \"#ff5e00\",\n                            \"#ffe400\",\n                            \"#abf200\",\n                            \"#00d8ff\",\n                            \"#0055ff\",\n                            \"#6600ff\",\n                            \"#ff00dd\",\n                            \"#000000\",\n                            \"#ffd8d8\",\n                            \"#fae0d4\",\n                            \"#faf4c0\",\n                            \"#e4f7ba\",\n                            \"#d4f4fa\",\n                            \"#d9e5ff\",\n                            \"#e8d9ff\",\n                            \"#ffd9fa\",\n                            \"#f1f1f1\",\n                            \"#ffa7a7\",\n                            \"#ffc19e\",\n                            \"#faed7d\",\n                            \"#cef279\",\n                            \"#b2ebf4\",\n                            \"#b2ccff\",\n                            \"#d1b2ff\",\n                            \"#ffb2f5\",\n                            \"#bdbdbd\",\n                            \"#f15f5f\",\n                            \"#f29661\",\n                            \"#e5d85c\",\n                            \"#bce55c\",\n                            \"#5cd1e5\",\n                            \"#6699ff\",\n                            \"#a366ff\",\n                            \"#f261df\",\n                            \"#8c8c8c\",\n                            \"#980000\",\n                            \"#993800\",\n                            \"#998a00\",\n                            \"#6b9900\",\n                            \"#008299\",\n                            \"#003399\",\n                            \"#3d0099\",\n                            \"#990085\",\n                            \"#353535\",\n                            \"#670000\",\n                            \"#662500\",\n                            \"#665c00\",\n                            \"#476600\",\n                            \"#005766\",\n                            \"#002266\",\n                            \"#290066\",\n                            \"#660058\",\n                            \"#222222\"\n                        ]\n                    ]\n                },\n                onImageUpload: handlePhotoBlogChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 410,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 535,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaqSection__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                values: values,\n                setFieldValue: setFieldValue,\n                errors: errors,\n                touched: touched,\n                language: \"EN\",\n                debounce: debounce\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 537,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 545,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:metaTitle\"),\n                                        \" (\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: values.metaTitleEN?.length > 65 ? \" text-danger\" : \"\",\n                                            children: [\n                                                \" \",\n                                                values.metaTitleEN?.length,\n                                                \" / 65\",\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        \")\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            variant: \"standard\",\n                                            name: \"metaTitleEN\",\n                                            type: \"text\",\n                                            value: metatitle || values.metaTitleEN,\n                                            onChange: (e)=>{\n                                                const metaTitleEN = e.target.value;\n                                                setFieldValue(\"metaTitleEN\", metaTitleEN);\n                                                setMetatitle(metaTitleEN);\n                                                debounce();\n                                            },\n                                            className: \"input-pentabell\" + (errors.metaTitleEN && touched.metaTitleEN ? \" is-invalid\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"metaTitleEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 549,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 547,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    label: t(\"createArticle:url\"),\n                                    name: \"urlEN\",\n                                    value: url,\n                                    onChange: (e)=>setUrl(e.target.value),\n                                    error: touched.urlEN && errors.urlEN\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 587,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 546,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:metaDescription\"),\n                                \" (\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: values.metaDescriptionEN?.length > 160 ? \" text-danger\" : \"\",\n                                    children: [\n                                        values.metaDescriptionEN?.length,\n                                        \" / 160\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                \")\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"metaDescriptionEN\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: metaDescription || values.metaDescriptionEN,\n                                    onChange: (e)=>{\n                                        const metaDescriptionEN = e.target.value;\n                                        setFieldValue(\"metaDescriptionEN\", metaDescriptionEN);\n                                        setMetaDescription(metaDescriptionEN);\n                                        debounce();\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.metaDescriptionEN && touched.metaDescriptionEN ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"metaDescriptionEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 603,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 602,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 601,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 600,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:featuredImage\"),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"upload-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: `image-upload-en`,\n                                        className: \"file-labels\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                id: `image-upload-en`,\n                                                name: \"imageEN\",\n                                                accept: \".png, .jpg, .jpeg, .webp\",\n                                                ref: imageInputRef,\n                                                onChange: (e)=>{\n                                                    setFieldValue(\"imageEN\", e.target.files[0]);\n                                                    handlePhotoChange();\n                                                },\n                                                className: \"file-input\" + (errors.imageEN && touched.imageEN ? \" is-invalid\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"upload-area\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"icon-pic\",\n                                                            style: {\n                                                                backgroundImage: `url(\"${selectedImage ? URL.createObjectURL(selectedImage) : values.imageEN ? `${process.env.REACT_APP_API_URL}${_utils_urls__WEBPACK_IMPORTED_MODULE_7__.API_URLS.files}/${values.imageEN}` : _assets_images_add_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"].src}\")`,\n                                                                backgroundSize: \"cover\",\n                                                                backgroundRepeat: \"no-repeat\",\n                                                                backgroundPosition: \"center\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-text\",\n                                                                children: t(\"createArticle:addFeatImg\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                                lineNumber: 686,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-description\",\n                                                                children: t(\"createArticle:clickBox\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                                name: \"image\",\n                                                component: \"div\",\n                                                className: \"invalid-feedback error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 646,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 645,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 644,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 643,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:alt\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"altEN\",\n                                        type: \"text\",\n                                        value: values.altEN,\n                                        onChange: (e)=>{\n                                            setFieldValue(\"altEN\", e.target.value);\n                                            debounce();\n                                        },\n                                        className: \"input-pentabell\" + (errors.altEN && touched.altEN ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"altEN\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 709,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 708,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 707,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:visibility\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"select-pentabell\",\n                                            variant: \"standard\",\n                                            value: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.Visibility.filter((option)=>values.visibilityEN === option),\n                                            selected: values.visibilityEN,\n                                            onChange: (event)=>{\n                                                setFieldValue(\"visibilityEN\", event.target.value);\n                                            },\n                                            children: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.Visibility.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    value: item,\n                                                    children: item\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"visibilityEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 757,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 735,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 733,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:keyword\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            id: \"tags\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                                tags: tags,\n                                                className: \"input-pentabell\" + (errors.keywordsEN && touched.keywordsEN ? \" is-invalid\" : \"\"),\n                                                delimiters: delimiters,\n                                                handleDelete: (i)=>{\n                                                    const updatedTags = tags.filter((tag, index)=>index !== i);\n                                                    setTags(updatedTags);\n                                                    setFieldValue(\"keywordsEN\", updatedTags.map((tag)=>tag.text));\n                                                },\n                                                handleAddition: (tag)=>{\n                                                    setTags([\n                                                        ...tags,\n                                                        tag\n                                                    ]);\n                                                    setFieldValue(\"keywordsEN\", [\n                                                        ...tags,\n                                                        tag\n                                                    ].map((item)=>item.text));\n                                                    debounce();\n                                                },\n                                                inputFieldPosition: \"bottom\",\n                                                autocomplete: true,\n                                                allowDragDrop: false\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"keywordsEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 768,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 766,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 706,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"label-form\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.Field, {\n                                    type: \"checkbox\",\n                                    name: \"publishNow\",\n                                    checked: publishNow,\n                                    onChange: (e)=>{\n                                        setPublishNow(e.target.checked);\n                                        if (e.target.checked) {\n                                            setFieldValue(\"publishDateEN\", new Date().toISOString());\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 13\n                                }, this),\n                                t(\"createArticle:publishNow\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 816,\n                            columnNumber: 11\n                        }, this),\n                        !publishNow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"label-form\",\n                                        children: [\n                                            t(\"createArticle:publishDate\"),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_26__.LocalizationProvider, {\n                                                dateAdapter: _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_27__.AdapterDayjs,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_28__.DemoContainer, {\n                                                    components: [\n                                                        \"DatePicker\"\n                                                    ],\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_29__.DatePicker, {\n                                                            variant: \"standard\",\n                                                            className: \"input-date\",\n                                                            format: \"DD/MM/YYYY\",\n                                                            value: dayjs__WEBPACK_IMPORTED_MODULE_12___default()(values.publishDateEN || new Date()),\n                                                            onChange: (date)=>{\n                                                                setFieldValue(\"publishDateEN\", dayjs__WEBPACK_IMPORTED_MODULE_12___default()(date).format(\"YYYY-MM-DD\"));\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                            lineNumber: 838,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 834,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"publishDateEN\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 853,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 833,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 832,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 815,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 814,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_21__.Field, {\n                type: \"hidden\",\n                name: \"publishDateEN\",\n                value: publishNow ? new Date().toISOString() : publishDate.toISOString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 864,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AddArticleEN, \"nDc9FkDcP0selbZlc9Kghllvj14=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__.useSaveFile\n    ];\n});\n_c = AddArticleEN;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticleEN);\nvar _c;\n$RefreshReg$(_c, \"AddArticleEN\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\n"));

/***/ })

});