"use client";
import {
  Container,
  Grid,
  AccordionSummary,
  AccordionDetails,
  Accordion,
} from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import SvgexpandIcon from "../../assets/images/icons/arrowUp.svg";

import SvgCallendar from "@/assets/images/icons/yellow/yellowCalendar.svg";

export default function KSALaborLaws() {
  const [expanded, setExpanded] = useState(1);
  const { t } = useTranslation();
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  return (
    <div id="labor-tn-laws">
      <Container className="custom-max-width">
        <h2 className="heading-h1">{t("ksa:KSALabor:title")}</h2>

        <Grid className="container" container columnSpacing={3} rowSpacing={2}>
          <Grid item xs={12} sm={12} key={1}>
            <Accordion
              elevation={0}
              expanded={expanded === 1}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(1)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("ksa:KSALabor:workingHours:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:workingHours:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("ksa:KSALabor:workingHours:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:workingHours:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("ksa:KSALabor:workingHours:description2")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={2}>
            <Accordion
              elevation={0}
              expanded={expanded === 2}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(2)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("ksa:KSALabor:employmentContracts:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:employmentContracts:title1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("ksa:KSALabor:employmentContracts:data1")}</li>
                    <li>{t("ksa:KSALabor:employmentContracts:data2")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={3}>
            <Accordion
              elevation={0}
              expanded={expanded === 3}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(3)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("ksa:KSALabor:termination:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:termination:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("ksa:KSALabor:termination:description1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("ksa:KSALabor:termination:data1")}</li>
                    <li>{t("ksa:KSALabor:termination:data2")}</li>
                    <li>{t("ksa:KSALabor:termination:data3")}</li>
                    <li>{t("ksa:KSALabor:termination:data4")}</li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:termination:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("ksa:KSALabor:termination:description2")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:termination:title3")}
                  </p>
                  <p className="service-description paragraph">
                    {t("ksa:KSALabor:termination:description3")}
                  </p>
                  <p className="service-description paragraph">
                    {t("ksa:KSALabor:termination:description4")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("ksa:KSALabor:termination:data5")}</li>
                    <li>{t("ksa:KSALabor:termination:data6")}</li>
                  </ul>
                  <p className="service-description paragraph">
                    {t("ksa:KSALabor:termination:description5")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("ksa:KSALabor:termination:data7")}</li>
                    <li>{t("ksa:KSALabor:termination:data8")}</li>
                    <li>{t("ksa:KSALabor:termination:data9")}</li>
                  </ul>
                  <p className="service-description paragraph">
                    {t("ksa:KSALabor:termination:description6")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={4}>
            <Accordion
              elevation={0}
              expanded={expanded === 4}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(4)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("ksa:KSALabor:payroll:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:payroll:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("ksa:KSALabor:payroll:description")}
                  </p>
                </div>
                <div className="payroll-tn">
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("ksa:KSALabor:payroll:fiscalYear:title")}
                    </p>
                    <p className="date">
                      {t("ksa:KSALabor:payroll:fiscalYear:date1")}
                      <br />
                      {t("ksa:KSALabor:payroll:fiscalYear:date2")}
                    </p>
                    <p className="paragraph">
                      {t("ksa:KSALabor:payroll:fiscalYear:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("ksa:KSALabor:payroll:payrollCycle:title")}
                    </p>
                    <p className="date">
                      {t("ksa:KSALabor:payroll:payrollCycle:date")}
                    </p>
                    <p className="paragraph">
                      {t("ksa:KSALabor:payroll:payrollCycle:description")}
                    </p>
                  </div>

                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("ksa:KSALabor:payroll:minimumWage:title")}
                    </p>
                    <p className="date">
                      {t("ksa:KSALabor:payroll:minimumWage:wage")}
                      <br />
                      {t("ksa:KSALabor:payroll:minimumWage:date")}​
                    </p>
                    <p className="paragraph">
                      {t("ksa:KSALabor:payroll:minimumWage:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("ksa:KSALabor:payroll:payrollManagement:title")}
                    </p>
                    <p className="date">
                      {t("ksa:KSALabor:payroll:payrollManagement:date1")}
                      <br />
                      {t("ksa:KSALabor:payroll:payrollManagement:date2")}
                    </p>
                    <p className="paragraph">
                      {t("ksa:KSALabor:payroll:payrollManagement:description")}
                    </p>
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={5}>
            <Accordion
              elevation={0}
              expanded={expanded === 5}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(5)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("ksa:KSALabor:leaveEntitlements:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:leaveEntitlements:description")}
                  </p>
                  <div className="holidays-dates">
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t("ksa:KSALabor:leaveEntitlements:leaves:dataS1:date")}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "ksa:KSALabor:leaveEntitlements:leaves:dataS1:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t("ksa:KSALabor:leaveEntitlements:leaves:dataS2:date")}
                      </p>
                      <p className="paragraph">
                        {t(
                          "ksa:KSALabor:leaveEntitlements:leaves:dataS2:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t("ksa:KSALabor:leaveEntitlements:leaves:dataS3:date")}
                      </p>
                      <p className="paragraph">
                        {t(
                          "ksa:KSALabor:leaveEntitlements:leaves:dataS3:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t("ksa:KSALabor:leaveEntitlements:leaves:dataS4:date")}
                      </p>
                      <p className="paragraph">
                        {t(
                          "ksa:KSALabor:leaveEntitlements:leaves:dataS4:title"
                        )}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "ksa:KSALabor:leaveEntitlements:leaves:maternityLeave:title"
                    )}
                  </p>
                  <p className="service-description paragraph">
                    {t(
                      "ksa:KSALabor:leaveEntitlements:leaves:maternityLeave:description1"
                    )}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:leaveEntitlements:leaves:sickLeave:title")}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "ksa:KSALabor:leaveEntitlements:leaves:sickLeave:description"
                    )}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={6}>
            <Accordion
              elevation={0}
              expanded={expanded === 6}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(6)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">{t("ksa:KSALabor:tax:title")}</h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("ksa:KSALabor:tax:description")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:tax:title1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li> {t("ksa:KSALabor:tax:data1")}</li>
                    <li> {t("ksa:KSALabor:tax:data2")}</li>
                    <li> {t("ksa:KSALabor:tax:data3")}</li>
                    <li> {t("ksa:KSALabor:tax:data4")}</li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:tax:title2")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("ksa:KSALabor:tax:dataS1")}</li>
                    <li>{t("ksa:KSALabor:tax:dataS2")}</li>
                    <li>{t("ksa:KSALabor:tax:dataS3")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>

          <Grid item xs={12} sm={12} key={7}>
            <Accordion
              elevation={0}
              expanded={expanded === 7}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(7)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("ksa:KSALabor:visa:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("ksa:KSALabor:visa:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:visa:title1")}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:visa:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("ksa:KSALabor:visa:description3")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("ksa:KSALabor:visa:title3")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("ksa:KSALabor:visa:data1")}</li>
                    <li>{t("ksa:KSALabor:visa:data2")}</li>
                    <li>{t("ksa:KSALabor:visa:data3")}</li>
                    <li>{t("ksa:KSALabor:visa:data4")}</li>
                    <li>{t("ksa:KSALabor:visa:data5")}</li>
                    <li>{t("ksa:KSALabor:visa:data6")}</li>
                    <li>{t("ksa:KSALabor:visa:data7")}</li>
                    <li>{t("ksa:KSALabor:visa:data8")}</li>
                    <li>{t("ksa:KSALabor:visa:data9")}</li>
                    <li>{t("ksa:KSALabor:visa:data10")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}
