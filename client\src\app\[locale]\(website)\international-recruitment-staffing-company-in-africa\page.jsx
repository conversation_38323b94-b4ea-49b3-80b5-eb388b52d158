import OurPartners from "@/components/sections/OurPartners";
import banner from "../../../../assets/images/website/banner/international-recruitment-staffing-company-in-africa.webp";
import AfricaBanner from "@/components/offices-sections/AfricaBanner";
import GlobalHRServicesSection from "@/components/sections/GlobalHRServicesSection";
import r1 from "@/assets/images/services/recrutement1.png";
import r2 from "@/assets/images/services/recrutement2.png";
import r3 from "@/assets/images/services/recrutement3.png";
import r4 from "@/assets/images/services/recrutement4.png";
import serviceImg from "@/assets/images/services/service1.png";
import CTAPayroll from "@/components/services/CTAPayroll";
import AfricaForm from "@/features/forms/components/AfricaForm";

import ctaAfricaBg from "@/assets/images/services/ctaAfricaPage.png";
import HrSolutionsInAfrica from "@/components/offices-sections/HrSolutionsInAfrica";
import LocationsInAfrica from "@/components/offices-sections/LocationsInAfrica";
import CTAContact from "@/components/offices-sections/CTAContact";
import NavigateAfricanMarket from "@/components/offices-sections/NavigateAfricanMarket";
import initTranslations from "@/app/i18n";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJsonSSR } from "@/config/axios";
import serviceimgS1 from "@/assets/images/services/service1.png";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }international-recruitment-staffing-company-in-africa/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/international-recruitment-staffing-company-in-africa/`,
    en: `https://www.pentabell.com/international-recruitment-staffing-company-in-africa/`,
    "x-default": `https://www.pentabell.com/international-recruitment-staffing-company-in-africa/`,
  };
  const { t } = await initTranslations(locale, [
    "servicesByCountry",
    "europe",
    "Tunisia",
  ]);
  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/international-recruitment-staffing-company-in-africa`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("servicesByCountry:africa:metaTitle"),
    description: t("servicesByCountry:africa:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

async function companyInAfrica({ params: { locale } }) {
  const { t } = await initTranslations(locale, ["Tunisia", "africa", "europe"]);

  const SERVICES = [
    {
      id: "s1",
      title: t("Tunisia:services:dataS1:title"),
      description: t("Tunisia:services:dataS1:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: serviceImg,
      altImg: t("Tunisia:services:dataS1:altImg"),
    },
    {
      id: "s2",
      title: t("Tunisia:services:dataS2:title"),
      description: t("Tunisia:services:dataS2:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r4,
      altImg: t("Tunisia:services:dataS2:altImg"),
    },
    {
      id: "s3",
      title: t("Tunisia:services:dataS3:title"),
      description: t("Tunisia:services:dataS3:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r3,
      altImg: t("Tunisia:services:dataS3:altImg"),
    },
    {
      id: "s4",
      title: t("Tunisia:services:dataS4:title"),
      description: t("Tunisia:services:dataS4:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r2,
      altImg: t("Tunisia:services:dataS4:altImg"),
    },
    {
      id: "s5",
      title: t("Tunisia:services:dataS5:title"),
      description: t("Tunisia:services:dataS5:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r1,
      altImg: t("Tunisia:services:dataS5:altImg"),
    },
  ];
  return (
    <div>
      <AfricaBanner
        altImg={t("africa:banner:altImg")}
        bannerImg={banner}
        height={"100vh"}
        t={t}
      />
      <OurPartners disableTxt={true} />
      <NavigateAfricanMarket t={t} />
      <GlobalHRServicesSection
        title={t("africa:services:title")}
        subTitle={t("africa:services:subTitle")}
        SERVICES={SERVICES}
        defaultImage={serviceimgS1}
      />

      <CTAPayroll
        light={true}
        title={t("africa:hire:title")}
        description={t("africa:hire:description")}
        btnLink={"#africa-form"}
        btnText={t("africa:hire:getStarted")}
        img={ctaAfricaBg.src}
        imgAlt={t("europe:contact:altImage")}
      />
      <HrSolutionsInAfrica t={t} />
      <LocationsInAfrica t={t} />
      <CTAContact country={"africa"} t={t} />
      <AfricaForm country={"Africa"} />
    </div>
  );
}

export default companyInAfrica;
