import { Container } from "@mui/material";

import CustomButton from "../ui/CustomButton";
import SvglocationPin from "@/assets/images/icons/locationPin.svg";

import SvgArrow from "@/assets/images/icons/arrow.svg";

import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";
import Svgnew from "../../assets/images/icons/new.svg";

import Qatar from "@/assets/images/offices/Qatar.webp";
import UAE from "@/assets/images/offices/UAE.webp";
import KSA from "@/assets/images/offices/KSA.webp";
import Iraq from "@/assets/images/offices/Iraq.webp";

import Image from "next/image";
import { findCountryFlag, findCountryLabel } from "@/utils/functions";
import { OfficesCountries } from "@/config/countries";
import { websiteRoutesList } from "@/helpers/routesList";

function LocationsInMiddleEast({ t }) {
  const contacts = [
    {
      title: t("middleeast:locations:Qatar:title"),
      locations: [
        "Address : Level 14, Commercial Bank Plaza, West Bay . Doha , Qatar, PO BOX : 27111",
      ],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.qatarPage.route}`,
      img: Qatar,
      country: OfficesCountries.Qatar,
      alt: t("middleeast:locations:Qatar:alt"),
    },
    {
      title: t("middleeast:locations:UAE:title"),
      locations: [
        "Adress : HDS Business Center Office 306 JLT, Dubai, United Arab Emirates",
      ],
      phones: ["+216 31 385 510"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.dubaiPage.route}`,
      img: UAE,
      country: OfficesCountries.UAE,
      alt: t("middleeast:locations:UAE:alt"),
    },
    {
      title: t("middleeast:locations:KSA:title"),
      locations: [
        t("ksa:officeLocation:address"),
      ],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.ksaPage.route}`,
      img: KSA,
      country: OfficesCountries.SaudiArabia,
      alt: t("middleeast:locations:KSA:alt"),
    },
    {
      title: t("middleeast:locations:iraq:title"),
      locations: [
        "Address : Business Avenu 3 (First floor), Abu Jaafar Mansour street 10013, baghdad, Iraq",
      ],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.iraqPage.route}`,
      img: Iraq,
      country: OfficesCountries.IRAQ,
      alt: t("middleeast:locations:iraq:alt"),
      iconNew: <Svgnew />,
    },
  ];
  return (
    <div id="africa-locations">
      <Container className="custom-max-width">
        <p className="sub-heading text-center text-blue">
          {t("middleeast:locations:subTitle")}
        </p>
        <h2 className="heading-h1 text-center">
          {t("middleeast:locations:title")}
        </h2>
      </Container>
      <Container className="contact-items-section custom-max-width">
        {contacts.map((contact, index) => (
          <div className="contact-item" key={index}>
            <div>
              <div className="country-img">
                {" "}
                <p className="country-label">
                  <img
                    width={22}
                    height={14}
                    src={findCountryFlag(contact.country)}
                    alt={findCountryLabel(contact.country)}
                    loading="lazy"
                  />
                  {findCountryLabel(contact.country)}
                </p>
                <img
                  width={388}
                  height={253}
                  src={contact.img.src}
                  alt={contact.alt}
                  loading="lazy"
                />
              </div>
              <div style={{ display: "flex", justifyContent: "space-between",alignItems:"flex-end" }}>
                {" "}
                <h3 className="sub-heading text-blue">{contact.title}</h3>
                {contact.iconNew && <span>{contact.iconNew}</span>}
              </div>{" "}
              <div>
                {contact.locations.map((location, locIndex) => (
                  <p className="row-item" key={locIndex}>
                    <span>
                      <SvglocationPin />
                    </span>
                    {location}
                  </p>
                ))}

                <p className="row-item">
                  <span>
                    <SvgcallUs />
                  </span>
                  {contact.phones.map((phone, phoneIndex) => (
                    <>
                      {" "}
                      {contact.phones.length > 1 ? (
                        <>
                          {phone} <br />
                        </>
                      ) : (
                        phone
                      )}
                    </>
                  ))}
                </p>

                <p className="row-item">
                  <span>
                    <Svgemail />
                  </span>
                  {contact.email}
                </p>
              </div>
            </div>
            <div className="btns">
              <CustomButton
                text={t("middleeast:locations:exploreMore")}
                className={"btn btn-outlined "}
                link={contact.link}
              />
              <CustomButton
                text={t("middleeast:locations:viewOnMap")}
                className={"btn btn-ghost"}
                link={contact.link}
                icon={<SvgArrow />}
              />
            </div>
          </div>
        ))}
      </Container>
    </div>
  );
}

export default LocationsInMiddleEast;
