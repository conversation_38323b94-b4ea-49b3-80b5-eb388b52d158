import { axiosGetJsonSSR } from "@/config/axios";
import { headers } from "next/headers";

import GuidePageDetails from "../../../../../features/guides/components/GuidePageDetails";
import BannerComponents from "@/components/sections/BannerComponents";
import initTranslations from "@/app/i18n";
import { redirect } from "next/navigation";

export async function generateMetadata({ params }) {
  const requestHeaders = headers();
  const cookie = requestHeaders.get("cookie");
  const language = params.locale;
  const url = params?.url;
  let canonicalUrl = `https://www.pentabell.com/${
    language !== "en" ? `${language}/` : ""
  }guides/${url}/`;
  try {
    const res = await axiosGetJsonSSR.get(`/guides/${language}/${url}`, {
      headers: {
        Cookie: cookie,
      },
    });
    const guide = res?.data?.versionsguide[0];
    let languages = {};
    try {
      const res = await axiosGetJsonSSR.get(
        `/guides/opposite/${language}/${url}`
      );
      const oppositeLanguageUrl = res.data.slug;
      if (language === "fr") {
        languages.fr = `https://www.pentabell.com/fr/guides/${url}/`;
        if (oppositeLanguageUrl) {
          languages.en = `https://www.pentabell.com/guides/${oppositeLanguageUrl}/`;
          languages[
            "x-default"
          ] = `https://www.pentabell.com/guides/${oppositeLanguageUrl}/`;
        }
        canonicalUrl = `https://www.pentabell.com/fr/guides/${url}/`;
      }
      if (language === "en") {
        if (oppositeLanguageUrl)
          languages.fr = `https://www.pentabell.com/fr/guides/${oppositeLanguageUrl}/`;
        languages.en = `https://www.pentabell.com/guides/${url}/`;
        languages["x-default"] = `https://www.pentabell.com/guides/${url}/`;
        canonicalUrl = `https://www.pentabell.com/guides/${url}/`;
      }
    } catch (error) {
      console.log("error", error.response.data);
    }
    if (guide) {
      return {
        title: guide.metaTitle,
        description: guide.metaDescription,
        robots:
          res?.data?.robotsMeta === "index"
            ? "follow, index, max-snippet:-1, max-image-preview:large"
            : "noindex",
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
        openGraph: {
          title: guide?.title,
          description: guide?.content
            ?.replace(/<\/?[^>]+(>|$)/g, " ")
            ?.replace(/&[a-z]+;/gi, "")
            ?.replace(/\s\s+/g, " ")
            ?.slice(0, 500),
          url:
            params.locale === "en"
              ? `https://www.pentabell.com/guides/${guide?.url}/`
              : `https://www.pentabell.com/guides/${params.locale}/${guide?.url}/`,
          images: [
            {
              url: guide?.image
                ? `https://www.pentabell.com/api/v1/files/${guide?.image}`
                : null,
              alt: guide?.alt,
            },
          ],
        },
      };
    }
  } catch (error) {
    return {
      title: "Error",
      description: "An error occurred while fetching the guide",
    };
  }

  return {
    title: "Guide",
    description: "Guide page with guides and categories",
  };
}

export default async function Page({ params }) {
  const language = params.locale;
  const url = params?.url;

  const { t } = await initTranslations(params.locale, [
    "eventDetails",
    "global",
    "guides",
  ]);
  const requestHeaders = headers();
  const cookie = requestHeaders.get("cookie");

  try {
    const res = await axiosGetJsonSSR.get(`/guides/${language}/${url}`, {
      headers: {
        Cookie: cookie,
      },
    });

    const oneGuide = res?.data;
    const guide = oneGuide?.versionsguide[0];
    const guideId = oneGuide?._id;
    const cible = oneGuide?.cible;
    const fileName = oneGuide?.versionsguide[0]?.file;
    const image = oneGuide?.versionsguide[0]?.image;
    const Name = oneGuide?.versionsguide[0]?.title;
    const itemList = oneGuide?.versionsguide[0]?.guideList;
    return (
      <>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "ItemList",
              name: itemList?.itemTitle,
              description: itemList?.itemDescription,
              url: url,
              itemListOrder: "Ascending",
              numberOfItems: itemList?.subtitles?.length || 0,
              itemListElement: itemList?.subtitles?.map((subtitle, index) => ({
                "@type": "ListItem",
                position: index + 1,
                name: subtitle.title,
                description: subtitle.description,
              })),
            }),
          }}
        />
        <BannerComponents
          bannerImgDynamic={`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${image}`}
          isEvent={true}
          titleHighlight={guide.title}
          highlights={guide.highlights ? guide.highlights : null}
          category={guide.categories.name}
          height={"70vh"}
          altImg={guide.alt}
        />
        <GuidePageDetails
          id={guideId}
          guide={guide}
          cible={cible}
          fileName={fileName}
          Name={Name}
          language={language}
          url={url}
        />
      </>
    );
  } catch (error) {
    redirect(params.locale === "en" ? `/guides/` : `/${params.locale}/guides/`);
  }
}
