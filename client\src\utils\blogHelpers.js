/**
 * Blog utility functions to handle the new Map-based version structure
 */

/**
 * Get the version data for a specific language from blog data
 * Handles both old array format and new Map format for backward compatibility
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code ('en' or 'fr')
 * @returns {Object|null} The version data for the specified language
 */
export const getBlogVersion = (blogData, language = 'en') => {
  if (!blogData || !blogData.versions) {
    return null;
  }

  // Handle new Map structure (versions is an object with language keys)
  if (typeof blogData.versions === 'object' && !Array.isArray(blogData.versions)) {
    return blogData.versions[language] || blogData.versions[Object.keys(blogData.versions)[0]];
  }

  // Handle old array structure for backward compatibility
  if (Array.isArray(blogData.versions)) {
    const version = blogData.versions.find(v => v.language === language);
    return version || blogData.versions[0];
  }

  return null;
};

/**
 * Get all available languages for a blog post
 * @param {Object} blogData - The blog data object
 * @returns {Array} Array of language codes
 */
export const getBlogLanguages = (blogData) => {
  if (!blogData || !blogData.versions) {
    return [];
  }

  // Handle new Map structure
  if (typeof blogData.versions === 'object' && !Array.isArray(blogData.versions)) {
    return Object.keys(blogData.versions);
  }

  // Handle old array structure
  if (Array.isArray(blogData.versions)) {
    return blogData.versions.map(v => v.language);
  }

  return [];
};

/**
 * Check if a blog has a version in a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code to check
 * @returns {boolean} True if the language version exists
 */
export const hasBlogLanguage = (blogData, language) => {
  const languages = getBlogLanguages(blogData);
  return languages.includes(language);
};

/**
 * Get the primary language for a blog post (first available language)
 * @param {Object} blogData - The blog data object
 * @returns {string} The primary language code
 */
export const getPrimaryBlogLanguage = (blogData) => {
  const languages = getBlogLanguages(blogData);
  return languages[0] || 'en';
};

/**
 * Transform blog data from old array format to new Map format
 * @param {Object} blogData - The blog data object with array versions
 * @returns {Object} The blog data with Map-based versions
 */
export const transformBlogToMapFormat = (blogData) => {
  if (!blogData || !blogData.versions || !Array.isArray(blogData.versions)) {
    return blogData;
  }

  const versionsMap = {};
  blogData.versions.forEach(version => {
    if (version.language) {
      versionsMap[version.language] = version;
    }
  });

  return {
    ...blogData,
    versions: versionsMap
  };
};

/**
 * Transform blog data from new Map format to old array format (for backward compatibility)
 * @param {Object} blogData - The blog data object with Map versions
 * @returns {Object} The blog data with array-based versions
 */
export const transformBlogToArrayFormat = (blogData) => {
  if (!blogData || !blogData.versions || Array.isArray(blogData.versions)) {
    return blogData;
  }

  const versionsArray = Object.values(blogData.versions);

  return {
    ...blogData,
    versions: versionsArray
  };
};

/**
 * Get blog URL for a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code
 * @returns {string|null} The URL for the blog in the specified language
 */
export const getBlogUrl = (blogData, language = 'en') => {
  const version = getBlogVersion(blogData, language);
  return version?.url || null;
};

/**
 * Get blog title for a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code
 * @returns {string|null} The title for the blog in the specified language
 */
export const getBlogTitle = (blogData, language = 'en') => {
  const version = getBlogVersion(blogData, language);
  return version?.title || null;
};

/**
 * Get blog image for a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code
 * @returns {string|null} The image URL for the blog in the specified language
 */
export const getBlogImage = (blogData, language = 'en') => {
  const version = getBlogVersion(blogData, language);
  return version?.image || null;
};

/**
 * Get blog content for a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code
 * @returns {string|null} The content for the blog in the specified language
 */
export const getBlogContent = (blogData, language = 'en') => {
  const version = getBlogVersion(blogData, language);
  return version?.content || null;
};

/**
 * Get blog description for a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code
 * @returns {string|null} The description for the blog in the specified language
 */
export const getBlogDescription = (blogData, language = 'en') => {
  const version = getBlogVersion(blogData, language);
  return version?.description || null;
};

/**
 * Get blog meta title for a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code
 * @returns {string|null} The meta title for the blog in the specified language
 */
export const getBlogMetaTitle = (blogData, language = 'en') => {
  const version = getBlogVersion(blogData, language);
  return version?.metaTitle || null;
};

/**
 * Get blog meta description for a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code
 * @returns {string|null} The meta description for the blog in the specified language
 */
export const getBlogMetaDescription = (blogData, language = 'en') => {
  const version = getBlogVersion(blogData, language);
  return version?.metaDescription || null;
};

/**
 * Get blog creation date for a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code
 * @returns {Date|null} The creation date for the blog in the specified language
 */
export const getBlogCreatedAt = (blogData, language = 'en') => {
  const version = getBlogVersion(blogData, language);
  return version?.createdAt || null;
};

/**
 * Get blog publish date for a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code
 * @returns {Date|null} The publish date for the blog in the specified language
 */
export const getBlogPublishDate = (blogData, language = 'en') => {
  const version = getBlogVersion(blogData, language);
  return version?.publishDate || null;
};

/**
 * Check if blog is archived for a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code
 * @returns {boolean} True if the blog is archived in the specified language
 */
export const isBlogArchived = (blogData, language = 'en') => {
  const version = getBlogVersion(blogData, language);
  return version?.isArchived || false;
};

/**
 * Get blog visibility for a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code
 * @returns {string|null} The visibility status for the blog in the specified language
 */
export const getBlogVisibility = (blogData, language = 'en') => {
  const version = getBlogVersion(blogData, language);
  return version?.visibility || null;
};

/**
 * Get blog keywords for a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code
 * @returns {Array} The keywords array for the blog in the specified language
 */
export const getBlogKeywords = (blogData, language = 'en') => {
  const version = getBlogVersion(blogData, language);
  return version?.keywords || [];
};

/**
 * Get blog highlights for a specific language
 * @param {Object} blogData - The blog data object
 * @param {string} language - The language code
 * @returns {Array} The highlights array for the blog in the specified language
 */
export const getBlogHighlights = (blogData, language = 'en') => {
  const version = getBlogVersion(blogData, language);
  return version?.highlights || [];
};
