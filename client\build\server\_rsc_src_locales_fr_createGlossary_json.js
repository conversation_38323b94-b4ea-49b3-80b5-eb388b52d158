"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_locales_fr_createGlossary_json";
exports.ids = ["_rsc_src_locales_fr_createGlossary_json"];
exports.modules = {

/***/ "(rsc)/./src/locales/fr/createGlossary.json":
/*!********************************************!*\
  !*** ./src/locales/fr/createGlossary.json ***!
  \********************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"addCategory":"Ajouter une catégorie","editCategory":"Modifier une catégorie","nameCat":"Nom catégorie","descriptionCat":"Description","fr":"Français","en":"Anglais","image":"Image","addGlossary":"Ajouter un glossary","addGlossaryEng":"Ajouter un glossary (Anglais)","addGlossaryFr":"Ajouter un glossary (Français)","editGlossary":"Modifier glossary","editGlossaryEng":"Modifier glossary (Anglais)","editGlossaryFr":"Modifier glossary (Français)","title":"Titre","category":"Categorie","Robotsmeta":"Méta robots","content":"Contenu","metaTitle":"Meta titre","categories":"Catégories","metaDescription":"Meta description","url":"Url","featuredImage":"Featured image","alt":"Alt ( Featured image )","visibility":"Visibilité","keyword":"Keyword","publishNow":"Publier maintenant","publishDate":"Date de publication","addFeatImg":"Ajouter featured image","clickBox":"Cliquer sur le box pour importer image","settings":"Paramètre"}');

/***/ })

};
;