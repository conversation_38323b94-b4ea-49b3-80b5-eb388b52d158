import { redirect } from "next/navigation";
import initTranslations from "@/app/i18n";

import { axiosGetJsonSSR } from "@/config/axios";
import BannerComponents from "@/components/sections/BannerComponents";

import OpportunityPage from "@/features/opportunity/components/opportunityFrontOffice/OpportunityPage";
import { getBannerImgByIndustryBySlug } from "../../job-category/[industry]/page";
import SvgFileText from "@/assets/images/icons/FileText.svg";
import SvgMapPin from "@/assets/images/icons/MapPin.svg";
import SvgTime from "@/assets/images/icons/time.svg";
import SvgDeadline from "@/assets/images/icons/deadline.svg";
import Link from "next/link";

import moment from "moment";
import {
  findIndustryClassname,
  findIndustryIcon,
  findIndustryLabel,
  findIndustryLink,
  industryExists,
} from "@/utils/functions";
import { websiteRoutesList } from "@/helpers/routesList";
import { getSlugByIndustry } from "../../../../../utils/functions";

export async function generateMetadata({ params }) {
  const { t } = await initTranslations(params.locale, ["opportunities"]);

  try {
    const opportunityData = await fetchData(params);

    const canonicalUrl = `https://www.pentabell.com/${
      params.locale !== "en" ? `${params.locale}/` : ""
    }opportunities/${params.slug}/`;

    const languages = {
      fr: `https://www.pentabell.com/fr/opportunities/${params.slug}/`,
      en: `https://www.pentabell.com/opportunities/${params.slug}/`,
      "x-default": `https://www.pentabell.com/opportunities/${params.slug}/`,
    };

    return {
      title: `${opportunityData?.versions[params.locale]?.title} ${t(
        "opportunities:metaTitleOneOpportunity2"
      )} ${opportunityData?.country} (${opportunityData?.reference})`,
      description: `${t("opportunities:metaDescriptionOneOpportunity1")} ${
        opportunityData?.versions[params.locale]?.title
      } ${t("opportunities:metaDescriptionOneOpportunity3")} ${
        opportunityData?.country
      }${t("opportunities:metaDescriptionOneOpportunity2")} (${
        opportunityData?.reference
      })`,
      robots: "follow, index, max-snippet:-1, max-image-preview:large",
      alternates: {
        canonical: canonicalUrl,
        languages,
      },
      openGraph: {
        title: opportunityData?.versions[params.locale]?.title,
        description: opportunityData?.versions[params.locale]?.jobDescription
          ?.replace(/<\/?[^>]+(>|$)/g, "")
          ?.replace(/&[a-z]+;/gi, ""),
        url:
          params.locale === "en"
            ? `https://www.pentabell.com/opportunities/${
                opportunityData?.versions[params.locale]?.url
              }/`
            : `https://www.pentabell.com/opportunities/${params.locale}/${
                opportunityData?.versions[params.locale]?.url
              }/`,
        images: [
          {
            url: `https://www.pentabell.com/industries/${getSlugByIndustry(
              opportunityData?.industry
            )?.toLowerCase()}.png`,
            alt: opportunityData?.versions[params.locale]?.alt,
          },
        ],
      },
    };
  } catch (error) {
    console.log("Error fetch opportunities metadata", error);
    redirect(
      params.locale === "en"
        ? `/${websiteRoutesList.opportunities.route}/`
        : `/${params.locale}/${websiteRoutesList.opportunities.route}/`
    );
  }
}

async function fetchData(params) {
  try {
    const res = await axiosGetJsonSSR.get(
      `/opportunities/${params.locale}/${params.slug}`
    );
    return res.data;
  } catch (error) {
    console.log("Error fetch opportunities data", error);
    redirect(
      params.locale === "en"
        ? `/${websiteRoutesList.opportunities.route}/`
        : `/${params.locale}/${websiteRoutesList.opportunities.route}/`
    );
  }
}

async function fetchRelatedOpportunities(keyWord) {
  if (!keyWord) {
    console.error("keyWord is undefined. Cannot fetch related opportunities.");
    return [];
  }
  try {
    const res = await axiosGetJsonSSR.get(
      `/${websiteRoutesList.opportunities.route}?keyWord=${keyWord}`
    );

    return res.data;
  } catch (error) {
    console.error(
      "Erreur lors de la récupération des opportunités connexes:",
      error
    );
    return [];
  }
}

export default async function Opportunities({ params }) {
  try {
    const data = await fetchData(params);

    const industry = data?.industry;

    if (!industry) {
      console.error(
        "Industry not found for the opportunity. Related jobs cannot be fetched."
      );
      return null;
    }

    const relatedOpportunities = await fetchRelatedOpportunities(
      data?.versions[params.locale]?.title
    );

    return (
      <div id="one-opportunity-page">
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "JobPosting",
              title: data?.versions[params.locale]?.title,
              url:
                params.locale === "en"
                  ? `https://www.pentabell.com/opportunities/${data?.versions["en"]?.url}/`
                  : `https://www.pentabell.com/opportunities/${params.locale}/${
                      data?.versions[params.locale]?.url
                    }/`,
              datePosted:
                data?.versions[params.locale]?.publishDate ??
                data?.versions[params.locale]?.createdAt,
              validThrough: data?.dateOfExpiration,
              description: data?.versions[params.locale]?.jobDescription,
              industry: data?.industry || "other",
              employmentType: data?.contractType || "Agreement",
              identifier: {
                "@type": "PropertyValue",
                name: "Job ID",
                value: data?.versions[params.locale]?.url,
              },
              jobLocation: {
                "@type": "Place",
                address: {
                  "@type": "PostalAddress",
                  addressCountry: data?.country,
                },
              },
              hiringOrganization: {
                "@type": "Organization",
                name: "Pentabell",
                sameAs:
                  params.locale === "en"
                    ? "https://www.pentabell.com/"
                    : `https://www.pentabell.com/${params.locale}/`,
                logo: "https://www.pentabell.com/api/v1/images/pentabell.png",
              },
            }),
          }}
        />
        <BannerComponents
          title={data?.versions[params.locale]?.title}
          bannerImg={getBannerImgByIndustryBySlug(industry)}
          topChildren={
            industryExists(industry) ? (
              <Link
                style={{ textDecoration: "none" }}
                href={`/${
                  websiteRoutesList.jobCategory.route
                }/${findIndustryLink(data?.industry)}`}
              >
                <p
                  className={`job-industry banner border ${findIndustryClassname(
                    industry
                  )}`}
                >
                  {findIndustryIcon(industry)} {findIndustryLabel(industry)}
                </p>
              </Link>
            ) : null
          }
          height={"70vh"}
          bottomChildren={
            <div id="banner-job-info">
              <p className="job-info">Ref: {data?.reference}</p>
              {data.contractType && (
                <p className="job-info">
                  <SvgFileText />
                  <span>{data.contractType}</span>
                </p>
              )}
              {data.country && (
                <p className="job-info">
                  <SvgMapPin />
                  <Link
                    href={`/${
                      websiteRoutesList.jobLocation.route
                    }/${data?.country.toLowerCase()}`}
                  >
                    {" "}
                    <span style={{ color: "white" }}>{data?.country}</span>
                  </Link>
                </p>
              )}

              {data?.createdAt && (
                <p className="job-info">
                  <SvgTime />
                  <span>
                    {" "}
                    {moment(data?.createdAt).format("MMMM DD, YYYY")}
                  </span>
                </p>
              )}
              {data?.dateOfExpiration && (
                <p className="job-info">
                  <SvgDeadline />
                  <span>
                    {" "}
                    {moment(data?.dateOfExpiration).format("MMMM DD, YYYY")}
                  </span>
                </p>
              )}
            </div>
          }
        />
        <OpportunityPage
          data={data}
          language={params.locale}
          relatedOpportunities={relatedOpportunities}
          industry={industry}
        />
      </div>
    );
  } catch (error) {}
}
