import { Contain<PERSON>, G<PERSON>, <PERSON> } from "@mui/material";
import CustomButton from "../ui/CustomButton";
import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";
import { websiteRoutesList } from "@/helpers/routesList";

function OfficeLocationMapLibya({ t }) {
  return (
    <Container id="office-location-map" className="custom-max-width">
      <Grid
        className="container"
        justifyContent="space-between"
        container
        spacing={0}
      >
        <Grid item xs={12} sm={6}>
          <div className="content">
            <p className="heading-h2 text-white">
              {t("libya:officeLocation:label")}
            </p>
            <p className="sub-heading text-white">
              {t("libya:officeLocation:title")}
            </p>
            <div>
              <p className="paragraph text-white" key={"tn"}>
                <span>
                  <SvglocationPin />
                </span>
                {t("libya:officeLocation:address")}
              </p>
              <p className="paragraph text-white">
                <span>
                  <SvgcallUs />
                </span>
                {t("libya:officeLocation:tel1")}
              </p>

              <p className="paragraph text-white">
                <span>
                  <Svgemail />
                </span>
                {t("libya:officeLocation:mail")}
              </p>
            </div>
            <Link
              href={`#service-page-form`}
              className={"btn btn-outlined white"}
            >
              {" "}
              {t("libya:officeLocation:talk")}
            </Link>
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <div className="map-frame">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d107301.37762314167!2d13.060563!3d32.814312!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x13a8e94d4b4a4cc9%3A0x3e417e0f847f6762!2sInternational%20Recruitment%20%26%20Staffing%20Agency%20Libya%20%7C%20Pentabell%20Libya!5e0!3m2!1sfr!2sus!4v1728632814048!5m2!1sfr!2sus"
              allowfullscreen=""
              priority
              referrerpolicy="no-referrer-when-downgrade"
            ></iframe>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default OfficeLocationMapLibya;
