"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2767],{89051:function(e,t,r){r.d(t,{Z:function(){return N}});var o=r(2265),p=r(61994),n=r(73207),a=r(20801),i=r(82590),l=r(39963),s=r(62919),c=r(30628),u=r(16210),m=r(31691),d=r(76301),g=r(37053),h=r(85657),f=r(78826),y=r(48467),w=r(9665),v=r(60118),b=r(32709),Z=r(67184),x=r(79114),T=r(94143),R=r(50738);function $(e){return(0,R.ZP)("MuiTooltip",e)}let M=(0,T.Z)("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]);var O=r(57437);let E=e=>{let{classes:t,disableInteractive:r,arrow:o,touch:p,placement:n}=e,i={popper:["popper",!r&&"popperInteractive",o&&"popperArrow"],tooltip:["tooltip",o&&"tooltipArrow",p&&"touch",`tooltipPlacement${(0,h.Z)(n.split("-")[0])}`],arrow:["arrow"]};return(0,a.Z)(i,$,t)},k=(0,u.ZP)(y.Z,{name:"MuiTooltip",slot:"Popper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.popper,!r.disableInteractive&&t.popperInteractive,r.arrow&&t.popperArrow,!r.open&&t.popperClose]}})((0,d.Z)(e=>{let{theme:t}=e;return{zIndex:(t.vars||t).zIndex.tooltip,pointerEvents:"none",variants:[{props:e=>{let{ownerState:t}=e;return!t.disableInteractive},style:{pointerEvents:"auto"}},{props:e=>{let{open:t}=e;return!t},style:{pointerEvents:"none"}},{props:e=>{let{ownerState:t}=e;return t.arrow},style:{[`&[data-popper-placement*="bottom"] .${M.arrow}`]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},[`&[data-popper-placement*="top"] .${M.arrow}`]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},[`&[data-popper-placement*="right"] .${M.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}},[`&[data-popper-placement*="left"] .${M.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!t.isRtl},style:{[`&[data-popper-placement*="right"] .${M.arrow}`]:{left:0,marginLeft:"-0.71em"}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!!t.isRtl},style:{[`&[data-popper-placement*="right"] .${M.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!t.isRtl},style:{[`&[data-popper-placement*="left"] .${M.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!!t.isRtl},style:{[`&[data-popper-placement*="left"] .${M.arrow}`]:{left:0,marginLeft:"-0.71em"}}}]}})),P=(0,u.ZP)("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.tooltip,r.touch&&t.touch,r.arrow&&t.tooltipArrow,t[`tooltipPlacement${(0,h.Z)(r.placement.split("-")[0])}`]]}})((0,d.Z)(e=>{let{theme:t}=e;return{backgroundColor:t.vars?t.vars.palette.Tooltip.bg:(0,i.Fq)(t.palette.grey[700],.92),borderRadius:(t.vars||t).shape.borderRadius,color:(t.vars||t).palette.common.white,fontFamily:t.typography.fontFamily,padding:"4px 8px",fontSize:t.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:t.typography.fontWeightMedium,[`.${M.popper}[data-popper-placement*="left"] &`]:{transformOrigin:"right center"},[`.${M.popper}[data-popper-placement*="right"] &`]:{transformOrigin:"left center"},[`.${M.popper}[data-popper-placement*="top"] &`]:{transformOrigin:"center bottom",marginBottom:"14px"},[`.${M.popper}[data-popper-placement*="bottom"] &`]:{transformOrigin:"center top",marginTop:"14px"},variants:[{props:e=>{let{ownerState:t}=e;return t.arrow},style:{position:"relative",margin:0}},{props:e=>{let{ownerState:t}=e;return t.touch},style:{padding:"8px 16px",fontSize:t.typography.pxToRem(14),lineHeight:`${Math.round(16/14*1e5)/1e5}em`,fontWeight:t.typography.fontWeightRegular}},{props:e=>{let{ownerState:t}=e;return!t.isRtl},style:{[`.${M.popper}[data-popper-placement*="left"] &`]:{marginRight:"14px"},[`.${M.popper}[data-popper-placement*="right"] &`]:{marginLeft:"14px"}}},{props:e=>{let{ownerState:t}=e;return!t.isRtl&&t.touch},style:{[`.${M.popper}[data-popper-placement*="left"] &`]:{marginRight:"24px"},[`.${M.popper}[data-popper-placement*="right"] &`]:{marginLeft:"24px"}}},{props:e=>{let{ownerState:t}=e;return!!t.isRtl},style:{[`.${M.popper}[data-popper-placement*="left"] &`]:{marginLeft:"14px"},[`.${M.popper}[data-popper-placement*="right"] &`]:{marginRight:"14px"}}},{props:e=>{let{ownerState:t}=e;return!!t.isRtl&&t.touch},style:{[`.${M.popper}[data-popper-placement*="left"] &`]:{marginLeft:"24px"},[`.${M.popper}[data-popper-placement*="right"] &`]:{marginRight:"24px"}}},{props:e=>{let{ownerState:t}=e;return t.touch},style:{[`.${M.popper}[data-popper-placement*="top"] &`]:{marginBottom:"24px"}}},{props:e=>{let{ownerState:t}=e;return t.touch},style:{[`.${M.popper}[data-popper-placement*="bottom"] &`]:{marginTop:"24px"}}}]}})),S=(0,u.ZP)("span",{name:"MuiTooltip",slot:"Arrow",overridesResolver:(e,t)=>t.arrow})((0,d.Z)(e=>{let{theme:t}=e;return{overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:t.vars?t.vars.palette.Tooltip.bg:(0,i.Fq)(t.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}}})),L=!1,A=new n.V,C={x:0,y:0};function W(e,t){return function(r){for(var o=arguments.length,p=Array(o>1?o-1:0),n=1;n<o;n++)p[n-1]=arguments[n];t&&t(r,...p),e(r,...p)}}var N=o.forwardRef(function(e,t){let r=(0,g.i)({props:e,name:"MuiTooltip"}),{arrow:a=!1,children:i,classes:u,components:d={},componentsProps:h={},describeChild:T=!1,disableFocusListener:R=!1,disableHoverListener:$=!1,disableInteractive:M=!1,disableTouchListener:N=!1,enterDelay:B=100,enterNextDelay:F=0,enterTouchDelay:I=700,followCursor:j=!1,id:z,leaveDelay:U=0,leaveTouchDelay:_=1500,onClose:V,onOpen:q,open:H,placement:X="bottom",PopperComponent:Y,PopperProps:D={},slotProps:G={},slots:J={},title:K,TransitionComponent:Q,TransitionProps:ee,...et}=r,er=o.isValidElement(i)?i:(0,O.jsx)("span",{children:i}),eo=(0,m.Z)(),ep=(0,l.V)(),[en,ea]=o.useState(),[ei,el]=o.useState(null),es=o.useRef(!1),ec=M||j,eu=(0,n.Z)(),em=(0,n.Z)(),ed=(0,n.Z)(),eg=(0,n.Z)(),[eh,ef]=(0,Z.Z)({controlled:H,default:!1,name:"Tooltip",state:"open"}),ey=eh,ew=(0,b.Z)(z),ev=o.useRef(),eb=(0,w.Z)(()=>{void 0!==ev.current&&(document.body.style.WebkitUserSelect=ev.current,ev.current=void 0),eg.clear()});o.useEffect(()=>eb,[eb]);let eZ=e=>{A.clear(),L=!0,ef(!0),q&&!ey&&q(e)},ex=(0,w.Z)(e=>{A.start(800+U,()=>{L=!1}),ef(!1),V&&ey&&V(e),eu.start(eo.transitions.duration.shortest,()=>{es.current=!1})}),eT=e=>{es.current&&"touchstart"!==e.type||(en&&en.removeAttribute("title"),em.clear(),ed.clear(),B||L&&F?em.start(L?F:B,()=>{eZ(e)}):eZ(e))},eR=e=>{em.clear(),ed.start(U,()=>{ex(e)})},[,e$]=o.useState(!1),eM=e=>{(0,s.Z)(e.target)||(e$(!1),eR(e))},eO=e=>{en||ea(e.currentTarget),(0,s.Z)(e.target)&&(e$(!0),eT(e))},eE=e=>{es.current=!0;let t=er.props;t.onTouchStart&&t.onTouchStart(e)};o.useEffect(()=>{if(ey)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){"Escape"===e.key&&ex(e)}},[ex,ey]);let ek=(0,v.Z)((0,c.Z)(er),ea,t);K||0===K||(ey=!1);let eP=o.useRef(),eS={},eL="string"==typeof K;T?(eS.title=ey||!eL||$?null:K,eS["aria-describedby"]=ey?ew:null):(eS["aria-label"]=eL?K:null,eS["aria-labelledby"]=ey&&!eL?ew:null);let eA={...eS,...et,...er.props,className:(0,p.Z)(et.className,er.props.className),onTouchStart:eE,ref:ek,...j?{onMouseMove:e=>{let t=er.props;t.onMouseMove&&t.onMouseMove(e),C={x:e.clientX,y:e.clientY},eP.current&&eP.current.update()}}:{}},eC={};N||(eA.onTouchStart=e=>{eE(e),ed.clear(),eu.clear(),eb(),ev.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",eg.start(I,()=>{document.body.style.WebkitUserSelect=ev.current,eT(e)})},eA.onTouchEnd=e=>{er.props.onTouchEnd&&er.props.onTouchEnd(e),eb(),ed.start(_,()=>{ex(e)})}),$||(eA.onMouseOver=W(eT,eA.onMouseOver),eA.onMouseLeave=W(eR,eA.onMouseLeave),ec||(eC.onMouseOver=eT,eC.onMouseLeave=eR)),R||(eA.onFocus=W(eO,eA.onFocus),eA.onBlur=W(eM,eA.onBlur),ec||(eC.onFocus=eO,eC.onBlur=eM));let eW={...r,isRtl:ep,arrow:a,disableInteractive:ec,placement:X,PopperComponentProp:Y,touch:es.current},eN="function"==typeof G.popper?G.popper(eW):G.popper,eB=o.useMemo(()=>{let e=[{name:"arrow",enabled:!!ei,options:{element:ei,padding:4}}];return D.popperOptions?.modifiers&&(e=e.concat(D.popperOptions.modifiers)),eN?.popperOptions?.modifiers&&(e=e.concat(eN.popperOptions.modifiers)),{...D.popperOptions,...eN?.popperOptions,modifiers:e}},[ei,D.popperOptions,eN?.popperOptions]),eF=E(eW),eI="function"==typeof G.transition?G.transition(eW):G.transition,ej={slots:{popper:d.Popper,transition:d.Transition??Q,tooltip:d.Tooltip,arrow:d.Arrow,...J},slotProps:{arrow:G.arrow??h.arrow,popper:{...D,...eN??h.popper},tooltip:G.tooltip??h.tooltip,transition:{...ee,...eI??h.transition}}},[ez,eU]=(0,x.Z)("popper",{elementType:k,externalForwardedProps:ej,ownerState:eW,className:(0,p.Z)(eF.popper,D?.className)}),[e_,eV]=(0,x.Z)("transition",{elementType:f.Z,externalForwardedProps:ej,ownerState:eW}),[eq,eH]=(0,x.Z)("tooltip",{elementType:P,className:eF.tooltip,externalForwardedProps:ej,ownerState:eW}),[eX,eY]=(0,x.Z)("arrow",{elementType:S,className:eF.arrow,externalForwardedProps:ej,ownerState:eW,ref:el});return(0,O.jsxs)(o.Fragment,{children:[o.cloneElement(er,eA),(0,O.jsx)(ez,{as:Y??y.Z,placement:X,anchorEl:j?{getBoundingClientRect:()=>({top:C.y,left:C.x,right:C.x,bottom:C.y,width:0,height:0})}:en,popperRef:eP,open:!!en&&ey,id:ew,transition:!0,...eC,...eU,popperOptions:eB,children:e=>{let{TransitionProps:t}=e;return(0,O.jsx)(e_,{timeout:eo.transitions.duration.shorter,...t,...eV,children:(0,O.jsxs)(eq,{...eH,children:[K,a?(0,O.jsx)(eX,{...eY}):null]})})}})]})})}}]);