import { Contain<PERSON>, <PERSON><PERSON>, <PERSON> } from "@mui/material";
import CustomButton from "../ui/CustomButton";
import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";
import { websiteRoutesList } from "@/helpers/routesList";

function OfficeLocationMapEgypt({ t }) {
  return (
    <Container id="office-location-map" className="custom-max-width">
      <Grid
        className="container"
        justifyContent="space-between"
        container
        spacing={0}
      >
        <Grid item xs={12} sm={6}>
          <div className="content">
            <p className="heading-h2 text-white">
              {t("Egypte:officeLocation:label")}
            </p>
            <p className="sub-heading text-white">
              {t("Egypte:officeLocation:title")}
            </p>
            <div>
              <p className="paragraph text-white" key={"tn"}>
                <span>
                  <SvglocationPin />
                </span>
                {t("Egypte:officeLocation:address")}
              </p>
              <p className="paragraph text-white">
                <span>
                  <SvgcallUs />
                </span>
                +33 1 73 07 42 54
              </p>

              <p className="paragraph text-white">
                <span>
                  <Svgemail />
                </span>
                <EMAIL>
              </p>
            </div>
            <Link
              href={`#service-page-form`}
              className={"btn btn-outlined white"}
            >
              {t("Tunisia:officeLocation:talk")}
            </Link>
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <div className="map-frame">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d6908.10051409472!2d31.232283!3d30.035415999999998!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x145840ccc19a9c45%3A0x9eaeba178c16fd08!2s8%20Al%20Bergas%2C%20Qasr%20El%20Nil%2C%20Cairo%20Governorate%204272015%2C%20%C3%89gypte!5e0!3m2!1sfr!2sus!4v1728580479664!5m2!1sfr!2sus"
              allowfullscreen=""
              loading="lazy"
              referrerpolicy="no-referrer-when-downgrade"
            ></iframe>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default OfficeLocationMapEgypt;
