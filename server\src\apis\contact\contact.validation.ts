import Joi from 'joi';

const getInTouchFormSchema = Joi.object({
    to: Joi.string().email().required(),
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    email: Joi.string().email().required(),
    phone: Joi.string().allow(''),
    message: Joi.string().required(),
    type: Joi.string().required(),
    team: Joi.string().required(),
    resume: Joi.string().optional(),
    field: Joi.string().optional(),
});
const getInTouchFormContactSchema = Joi.object({
    to: Joi.string().email().required(),
    fullName: Joi.string().required(),
    email: Joi.string().email().required(),
    phone: Joi.string().allow(''),
    youAre: Joi.string().required(),
    subject: Joi.string().allow(''),
    country: Joi.string().allow(''),
    message: Joi.string().required(),
    type: Joi.string().required(),
    team: Joi.string().required(),
    resume: Joi.string().optional(),
    field: Joi.string().when('youAre', {
        is: 'Consultant',
        then: Joi.required(),
        otherwise: Joi.optional(),
    }),
});

const payrollServiceFormSchema = Joi.object({
    to: Joi.string().email().required(),
    type: Joi.string().required(),
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    email: Joi.string().email().required(),
    phone: Joi.string().allow(''),
    message: Joi.string().required(),
    youAre: Joi.string().required(),
    companyName: Joi.string().allow(''),
    resume: Joi.string().optional(),
    field: Joi.string().when('youAre', {
        is: 'Consultant',
        then: Joi.required(),
        otherwise: Joi.optional(),
    }),
});
const mainServiceFormSchema = Joi.object({
    to: Joi.string().email().required(),
    type: Joi.string().required(),
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    email: Joi.string().email().required(),
    phone: Joi.string().allow(''),
    message: Joi.string().required(),
    companyName: Joi.string().allow(''),
    enquirySelect: Joi.string().required(),
    resume: Joi.string().optional(),
    field: Joi.string().optional(),
});

const technicalAssistanceContactSchema = Joi.object({
    to: Joi.string().email().required(),
    fullName: Joi.string().required(),
    jobTitle: Joi.string().allow(''),
    phone: Joi.string().allow(''),
    companyName: Joi.string().allow(''),
    email: Joi.string().email().required(),
    message: Joi.string().required(),
    type: Joi.string().required(),
    team: Joi.string().required(),
    resume: Joi.string().optional(),
    field: Joi.string().optional(),
});

const GetInTouchFormConsultingService = Joi.object({
    to: Joi.string().email().required(),
    fullName: Joi.string().required(),
    jobTitle: Joi.string().allow(''),
    email: Joi.string().email().required(),
    phone: Joi.string().allow(''),
    companyName: Joi.string().allow(''),
    youAre: Joi.string().required(),
    message: Joi.string().required(),
    type: Joi.string().required(),
    team: Joi.string().required(),
    resume: Joi.string().optional(),
    field: Joi.string().when('youAre', {
        is: 'Consultant',
        then: Joi.required(),
        otherwise: Joi.optional(),
    }),
});

const DirectHiringSchema = Joi.object({
    to: Joi.string().email().required(),
    fullName: Joi.string().required(),
    jobTitle: Joi.string().allow(''),
    email: Joi.string().email().required(),
    phone: Joi.string().allow(''),
    youAre: Joi.string().required(),
    message: Joi.string().required(),
    type: Joi.string().required(),
    resume: Joi.string().optional(),
    field: Joi.string().when('youAre', {
        is: 'Consultant',
        then: Joi.required(),
        otherwise: Joi.optional(),
    }),
});

const JoinUsFormContactSchema = Joi.object({
    to: Joi.alternatives().try(Joi.string().email().required(), Joi.array().items(Joi.string().email()).min(1).required()),
    fullName: Joi.string().required(),
    email: Joi.string().email().required(),
    phone: Joi.string().allow(''),
    field: Joi.string().required(),
    subject: Joi.string().required(),
    message: Joi.string().required(),
    type: Joi.string().required(),
    team: Joi.string().required(),
    mission: Joi.boolean().required(),
    resume: Joi.string().required(),
});
const CountryContactSchema = Joi.object({
    to: Joi.string().email().required(),
    type: Joi.string().required(),
    fullName: Joi.string().required(),
    email: Joi.string().email().required(),
    phone: Joi.string().allow(''),
    message: Joi.string().required(),
    companyName: Joi.string().allow(''),
    howToHelp: Joi.string().required(),
    youAre: Joi.string().required(),
    countryName: Joi.string().required(),
    country: Joi.string().allow(''),
    resume: Joi.string().optional(),
    field: Joi.string().when('youAre', {
        is: 'Consultant',
        then: Joi.required(),
        otherwise: Joi.optional(),
    }),
});
export {
    getInTouchFormSchema,
    technicalAssistanceContactSchema,
    GetInTouchFormConsultingService,
    payrollServiceFormSchema,
    JoinUsFormContactSchema,
    mainServiceFormSchema,
    getInTouchFormContactSchema,
    CountryContactSchema,
    DirectHiringSchema,
};
