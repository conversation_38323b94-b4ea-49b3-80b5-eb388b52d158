"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/features/stats/ResumesChart.jsx":
/*!*********************************************!*\
  !*** ./src/features/stats/ResumesChart.jsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ResumesChart; },\n/* harmony export */   valueFormatter: function() { return /* binding */ valueFormatter; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _stats_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stats.hooks */ \"(app-pages-browser)/./src/features/stats/stats.hooks.jsx\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon.svg\");\n/* harmony import */ var _components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../components/charts/CustomMultiBarchart */ \"(app-pages-browser)/./src/components/charts/CustomMultiBarchart.jsx\");\n/* harmony import */ var _components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../components/charts/CustomPieChart */ \"(app-pages-browser)/./src/components/charts/CustomPieChart.jsx\");\n/* harmony import */ var _blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../blog/hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _charts_CommentByCategory__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./charts/CommentByCategory */ \"(app-pages-browser)/./src/features/stats/charts/CommentByCategory.jsx\");\n/* harmony import */ var _charts_UsersActivities__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./charts/UsersActivities */ \"(app-pages-browser)/./src/features/stats/charts/UsersActivities.jsx\");\n/* harmony import */ var _charts_ApplicationsByStatus__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./charts/ApplicationsByStatus */ \"(app-pages-browser)/./src/features/stats/charts/ApplicationsByStatus.jsx\");\n/* __next_internal_client_entry_do_not_use__ valueFormatter,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction valueFormatter(value) {\n    return `${value}`;\n}\nfunction ResumesChart() {\n    _s();\n    const getCategoriesLangEN = (0,_blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"en\");\n    const transformedCategories = getCategoriesLangEN?.data?.categories?.map((category)=>({\n            id: category.versionscategory[0]?.id,\n            name: category.versionscategory[0]?.name\n        })) || [];\n    /// filter user Activity ///\n    const [dateFromUser, setDateFromUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToUser, setDateToUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchUser, setSearchUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchActivity = ()=>{\n        setDateToActivity(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromActivity(\"2024-09-01\");\n        setSearchActivity(!searchActivity);\n    };\n    /// filter platform activity ///\n    const [dateFromPlatform, setDateFromPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToPlatform, setDateToPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchPlatform, setSearchPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchPlatform = ()=>{\n        setDateToPlatform(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromPlatform(\"2024-09-01\");\n        setSearchPlatform(!searchPlatform);\n    };\n    /// application filter pie chart ///\n    const [dateFromApplication, setDateFromApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToApplication, setDateToApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchApplication, setSearchApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchApplication = ()=>{\n        setDateToApplication(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromApplication(\"2024-09-01\");\n        setSearchApplication(!searchApplication);\n    };\n    /// article filter pie chart ///\n    const [dateFromArticle, setDateFromArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToArticle, setDateToArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchArticle, setSearchArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchArticles = ()=>{\n        setDateToArticle(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromArticle(\"2024-09-01\");\n        setFilteredCategories([]);\n        setApprove(true);\n        setCategories([]);\n        setSearchArticle(!searchArticle);\n    };\n    /// comment filter pie chart ///\n    const [filteredCategories, setFilteredCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [approve, setApprove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dateFromComment, setDateFromComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToComment, setDateToComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchComment, setSearchComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchComments = ()=>{\n        setDateToComment(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromComment(\"2024-09-01\");\n        setFilteredCategories([]);\n        setApprove(true);\n        setCategories([]);\n        setSearchComment(!searchComment);\n    };\n    /// opportunity filter pie chart ////\n    const [dateFromOpportunity, setDateFromOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToOpportunity, setDateToOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [opportunityType, setOpportunityType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [industry, setIndustry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchOpportunity, setSearchOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchOpportunity = ()=>{\n        setDateToOpportunity(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromOpportunity(\"2024-09-01\");\n        setOpportunityType(\"\");\n        setIndustry(\"\");\n        setSearchOpportunity(!searchOpportunity);\n    };\n    const getDataPieComments = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetCommentsStat)({\n        dateFrom: dateFromComment,\n        dateTo: dateToComment,\n        approve: approve,\n        categories: filteredCategories\n    });\n    const getDataPieArticles = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetArticlesStat)({\n        dateFrom: dateFromArticle,\n        dateTo: dateToArticle,\n        barChart: null\n    });\n    const getDataPieOpportunities = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetOpportunitiesStat)({\n        dateFrom: dateFromOpportunity,\n        dateTo: dateToOpportunity,\n        opportunityType,\n        industry,\n        barChart: null\n    });\n    const getDAtaPieApplications = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetApplicationsStat)({\n        dateFrom: dateFromApplication,\n        dateTo: dateToApplication,\n        barChart: null\n    });\n    const getDataUserActivity = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetUserStat)({\n        dateFrom: dateFromUser,\n        dateTo: dateToUser\n    });\n    const getDataPlatforActivity = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetPlatformStat)({\n        dateFrom: dateFromPlatform,\n        dateTo: dateToPlatform\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataUserActivity.refetch();\n    }, [\n        searchUser\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPlatforActivity.refetch();\n    }, [\n        searchPlatform\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieOpportunities.refetch();\n    }, [\n        searchOpportunity\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDAtaPieApplications.refetch();\n    }, [\n        searchApplication\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieArticles.refetch();\n    }, [\n        searchArticle\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieComments.refetch();\n    }, [\n        searchComment\n    ]);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const isTablet = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(theme.breakpoints.down(\"md\"));\n    const chartSettings1 = {\n        width: isMobile ? 290 : isTablet ? 500 : 580,\n        height: 250,\n        layout: \"vertical\"\n    };\n    if (getDataUserActivity.isLoading || getDataPlatforActivity.isLoading || getDataPieArticles.isLoading || getDataPieOpportunities.isLoading || getDAtaPieApplications.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n            lineNumber: 244,\n            columnNumber: 12\n        }, this);\n    }\n    const pieCharts = [\n        {\n            title: t(\"statsDash:applicationsByStatus\"),\n            dataset: getDAtaPieApplications?.data?.map((app)=>({\n                    label: app.status,\n                    value: app.totalApplications\n                })),\n            colors: [\n                \"#E97611\",\n                \"#018055\",\n                \"#D73232\"\n            ]\n        },\n        {\n            title: t(\"statsDash:articlesByVisibility\"),\n            dataset: getDataPieArticles?.data?.map((article)=>({\n                    label: article.visibility,\n                    value: article.totalArticles\n                })),\n            colors: [\n                \"#234791\",\n                \"#FFCA00\",\n                \"#006A67\"\n            ]\n        },\n        {\n            title: t(\"statsDash:opportunities\"),\n            dataset: getDataPieOpportunities?.data?.map((opp)=>({\n                    label: opp.visibility,\n                    value: opp.totalOpportunities\n                })),\n            colors: [\n                \"#234791\",\n                \"#D5E5FF\",\n                \"#227B94\"\n            ]\n        },\n        {\n            title: t(\"statsDash:commentsByCategory\"),\n            dataset: getDataPieComments?.data?.map((comment)=>({\n                    label: comment.category,\n                    value: comment.total\n                })) || [],\n            colors: [\n                \"#673ab7\",\n                \"#009688\",\n                \"#8bc34a\",\n                \"#ffc107\",\n                \"#ff9800\",\n                \"#ffc107\",\n                \"#3f51b5\",\n                \"#009688\",\n                \"#4caf50\",\n                \"#03a9f4\",\n                \"#ff9800\",\n                \"#8bc34a\",\n                \"#673ab7\"\n            ]\n        }\n    ];\n    const userAactivity = {\n        title: t(\"statsDash:usersActivities\"),\n        dataKey: [\n            \"login\",\n            \"register\",\n            \"resumes\",\n            \"applications\"\n        ],\n        dataset: getDataUserActivity?.data,\n        color: [\n            \"#30B0C7\",\n            \"#234791\",\n            \"#007AFF\",\n            \"#32ADE6\"\n        ]\n    };\n    const platformAactivity = {\n        title: t(\"statsDash:platformActivity\"),\n        dataKey: [\n            \"opportunities\",\n            \"articles\",\n            \"newsletters\",\n            \"contacts\"\n        ],\n        dataset: getDataPlatforActivity?.data,\n        color: [\n            \"#FFCC00\",\n            \"#FFA135\",\n            \"#FFD985\",\n            \"#FF7700\"\n        ]\n    };\n    console.log();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"menu:statistics\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"stats\",\n                className: \"div-wrapper\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    container: true,\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_CommentByCategory__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                transformedCategories: transformedCategories,\n                                dateFromComment: dateFromComment,\n                                dateToComment: dateToComment,\n                                approve: approve,\n                                categories: categories,\n                                setCategories: setCategories,\n                                setFilteredCategories: setFilteredCategories,\n                                setSearchComment: setSearchComment,\n                                searchComment: searchComment,\n                                resetSearchComments: resetSearchComments,\n                                pieCharts: pieCharts,\n                                setApprove: setApprove,\n                                setDateFromComment: setDateFromComment,\n                                setDateToComment: setDateToComment\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_UsersActivities__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                dateFromUser: dateFromUser,\n                                dateToUser: dateToUser,\n                                searchUser: searchUser,\n                                setSearchUser: setSearchUser,\n                                resetSearchActivity: resetSearchActivity,\n                                userAactivity: userAactivity,\n                                chartSettings1: chartSettings1,\n                                setDateFromUser: setDateFromUser,\n                                setDateToUser: setDateToUser\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_ApplicationsByStatus__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                dateFromApplication: dateFromApplication,\n                                dateToApplication: dateToApplication,\n                                searchApplication: searchApplication,\n                                setSearchApplication: setSearchApplication,\n                                resetSearchApplication: resetSearchApplication,\n                                pieCharts: pieCharts,\n                                setDateFromApplication: setDateFromApplication,\n                                setDateToApplication: setDateToApplication\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"heading-h3\",\n                                            gutterBottom: true,\n                                            children: pieCharts[2].title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            elevation: 0,\n                                            disableGutters: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    \"aria-controls\": \"panel1bh-content\",\n                                                    id: \"panel1bh-header\",\n                                                    className: \"svg-accordion\",\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 33\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"label-pentabell\",\n                                                        children: t(\"statsDash:filters\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    elevation: 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        container: true,\n                                                        className: \"chart-grid\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"blue-text\",\n                                                                        children: [\n                                                                            t(\"statsDash:type\"),\n                                                                            \" :\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"select-pentabell blue-text\",\n                                                                        value: opportunityType || \"\",\n                                                                        defaultValue: \"\",\n                                                                        onChange: (event)=>setOpportunityType(event.target.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                selected: true,\n                                                                                disabled: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                                                    children: t(\"statsDash:opportunityType\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 399,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 393,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                children: t(\"statsDash:all\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            _utils_constants__WEBPACK_IMPORTED_MODULE_4__.OpportunityType.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"blue-text\",\n                                                                                    value: item,\n                                                                                    children: item\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 405,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                md: 6,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"blue-text\",\n                                                                        children: [\n                                                                            t(\"statsDash:industry\"),\n                                                                            \" :\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"select-pentabell blue-text\",\n                                                                        value: industry || \"\",\n                                                                        onChange: (event)=>setIndustry(event.target.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                selected: true,\n                                                                                disabled: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                                                    children: t(\"statsDash:industry\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 430,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 424,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                children: t(\"statsDash:all\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            _utils_constants__WEBPACK_IMPORTED_MODULE_4__.Industry.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"blue-text\",\n                                                                                    value: item,\n                                                                                    children: item\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 436,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    label: t(\"statsDash:fromDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateFromOpportunity,\n                                                                    onChange: (e)=>setDateFromOpportunity(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    label: t(\"statsDash:toDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateToOpportunity,\n                                                                    onChange: (e)=>setDateToOpportunity(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 3,\n                                                                sm: 1,\n                                                                md: 4,\n                                                                className: \"btns-filter dashboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                                                    onClick: resetSearchOpportunity\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 11,\n                                                                sm: 11,\n                                                                md: 8,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    text: t(\"statsDash:filter\"),\n                                                                    onClick: ()=>{\n                                                                        setSearchOpportunity(!searchOpportunity);\n                                                                    },\n                                                                    className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    donuts: false,\n                                                    chart: pieCharts[2]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                pieCharts[2].dataset?.some((item)=>item[\"value\"] > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"public-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:public\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"privateopportunity-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:private\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"draft-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:draft\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"barchartfilter-wrapper\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                container: true,\n                                                className: \"chart-grid\",\n                                                spacing: 1,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 12,\n                                                        md: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"heading-h3\",\n                                                            gutterBottom: true,\n                                                            children: platformAactivity.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 6,\n                                                        md: 2.5,\n                                                        xl: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            label: t(\"statsDash:fromDate\"),\n                                                            type: \"date\",\n                                                            value: dateFromPlatform,\n                                                            onChange: (e)=>setDateFromPlatform(e.target.value),\n                                                            fullWidth: true,\n                                                            InputLabelProps: {\n                                                                shrink: true\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 6,\n                                                        md: 2.5,\n                                                        xl: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            label: t(\"statsDash:toDate\"),\n                                                            type: \"date\",\n                                                            value: dateToPlatform,\n                                                            onChange: (e)=>setDateToPlatform(e.target.value),\n                                                            fullWidth: true,\n                                                            InputLabelProps: {\n                                                                shrink: true\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        item: true,\n                                                        xs: 2,\n                                                        sm: 1,\n                                                        md: 1.5,\n                                                        xl: 1,\n                                                        className: \"btns-filter dashboard\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            className: \"btn btn-outlined btn-refresh full-width\",\n                                                            onClick: resetSearchPlatform\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        item: true,\n                                                        xs: 10,\n                                                        sm: 11,\n                                                        md: 2.5,\n                                                        xl: 2,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            text: t(\"statsDash:filter\"),\n                                                            onClick: ()=>{\n                                                                setSearchPlatform(!searchPlatform);\n                                                            },\n                                                            className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                platformAactivity.dataset?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newopportunities-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newOpportunities\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 587,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"neswarticles-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newArticles\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 593,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newsletters-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newslettersSubscriptions\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newcontact-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 604,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newContacts\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    chart: platformAactivity,\n                                                    chartSettings: chartSettings1\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 525,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 524,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"heading-h3\",\n                                            gutterBottom: true,\n                                            children: pieCharts[1].title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            elevation: 0,\n                                            disableGutters: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    \"aria-controls\": \"panel1bh-content\",\n                                                    id: \"panel1bh-header\",\n                                                    className: \"svg-accordion\",\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 33\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"label-pentabell\",\n                                                        children: t(\"statsDash:filters\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    elevation: 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        container: true,\n                                                        className: \"chart-grid\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    label: t(\"statsDash:fromDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateFromArticle,\n                                                                    onChange: (e)=>setDateFromArticle(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 640,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    label: t(\"statsDash:toDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateToArticle,\n                                                                    onChange: (e)=>setDateToArticle(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 650,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 3,\n                                                                sm: 1,\n                                                                md: 4,\n                                                                className: \"btns-filter dashboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 667,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                                                    onClick: resetSearchArticles\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 659,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                item: true,\n                                                                xs: 11,\n                                                                sm: 11,\n                                                                md: 8,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    text: t(\"statsDash:filter\"),\n                                                                    onClick: ()=>{\n                                                                        setSearchArticle(!searchArticle);\n                                                                    },\n                                                                    className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    donuts: false,\n                                                    chart: pieCharts[1]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 688,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                pieCharts[1].dataset?.some((item)=>item[\"value\"] > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"public-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:public\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"privatearticles-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:private\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"draft-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 704,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:draft\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 705,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 620,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ResumesChart, \"5avvH6cEosRudI6naxYJ3BUU5WU=\", false, function() {\n    return [\n        _blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetCommentsStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetArticlesStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetOpportunitiesStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetApplicationsStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetUserStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetPlatformStat,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    ];\n});\n_c = ResumesChart;\nvar _c;\n$RefreshReg$(_c, \"ResumesChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/stats/ResumesChart.jsx\n"));

/***/ })

});