"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Description!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Add custom styles for drag state\nconst dragActiveStyles = `\n  .file-labels.drag-active {\n    border-color: #1976d2 !important;\n    background-color: rgba(25, 118, 210, 0.04) !important;\n  }\n  .file-labels.disabled {\n    cursor: not-allowed !important;\n    opacity: 0.6 !important;\n  }\n`;\n// Inject styles if not already present\nif (typeof document !== \"undefined\" && !document.getElementById(\"document-importer-styles\")) {\n    const styleSheet = document.createElement(\"style\");\n    styleSheet.id = \"document-importer-styles\";\n    styleSheet.textContent = dragActiveStyles;\n    document.head.appendChild(styleSheet);\n}\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewOpen, setPreviewOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [extractedData, setExtractedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Supported file types\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes(\"pdf\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PdfIcon, {\n            color: \"error\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 55,\n            columnNumber: 42\n        }, undefined);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            color: \"primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 56,\n            columnNumber: 12\n        }, undefined);\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        // Create a temporary DOM element to parse HTML\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        // Extract potential title (first h1, h2, or strong text)\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        // Extract first paragraph as potential description\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        // Extract keywords from headings and strong text\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords: keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement(function(image) {\n                        return image.read(\"base64\").then(function(imageBuffer) {\n                            return {\n                                src: \"data:\" + image.contentType + \";base64,\" + imageBuffer\n                            };\n                        });\n                    })\n                }\n            });\n            setProgress(75);\n            // Clean up the HTML content\n            let cleanContent = result.value.replace(/<p><\\/p>/g, \"\") // Remove empty paragraphs\n            .replace(/\\s+/g, \" \") // Normalize whitespace\n            .trim();\n            // Extract metadata\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata: metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            // Convert plain text to basic HTML\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata: metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            setExtractedData(result);\n            setSuccess(true);\n            // Show preview dialog\n            setPreviewOpen(true);\n        } catch (err) {\n            console.error(\"File processing error:\", err);\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) {\n            processFile(acceptedFiles[0]);\n        }\n    }, []);\n    const { getRootProps, getInputProps, isDragActive, acceptedFiles } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing\n    });\n    const handleApplyContent = ()=>{\n        if (extractedData) {\n            onContentExtracted(extractedData.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(extractedData.metadata);\n            }\n            setPreviewOpen(false);\n            setExtractedData(null);\n        }\n    };\n    const handleClosePreview = ()=>{\n        setPreviewOpen(false);\n        setExtractedData(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        sx: {\n            mb: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: [\n                    t(\"createArticle:importDocument\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paper, {\n                ...getRootProps(),\n                sx: {\n                    p: 3,\n                    border: \"2px dashed\",\n                    borderColor: isDragActive ? \"primary.main\" : \"grey.300\",\n                    backgroundColor: isDragActive ? \"action.hover\" : \"background.paper\",\n                    cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\",\n                    textAlign: \"center\",\n                    transition: \"all 0.3s ease\",\n                    opacity: disabled || isProcessing ? 0.6 : 1,\n                    \"&:hover\": {\n                        borderColor: \"primary.main\",\n                        backgroundColor: \"action.hover\"\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ...getInputProps()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UploadIcon, {\n                        sx: {\n                            fontSize: 48,\n                            color: \"primary.main\",\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:dragDropOrClick\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        sx: {\n                            mb: 2\n                        },\n                        children: [\n                            t(\"createArticle:supportedFormats\"),\n                            \": .docx, .doc, .txt\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            gap: 1,\n                            flexWrap: \"wrap\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 23\n                                }, void 0),\n                                label: \"Word (.docx)\",\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 23\n                                }, void 0),\n                                label: \"Word (.doc)\",\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 23\n                                }, void 0),\n                                label: \"Text (.txt)\",\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        gutterBottom: true,\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, undefined),\n            success && !previewOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dialog, {\n                open: previewOpen,\n                onClose: handleClosePreview,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogTitle, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"h6\",\n                                    children: t(\"createArticle:previewExtractedContent\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconButton, {\n                                    onClick: handleClosePreview,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CloseIcon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogContent, {\n                        children: extractedData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: [\n                                extractedData.metadata.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        mb: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedTitle\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: extractedData.metadata.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 17\n                                }, undefined),\n                                extractedData.metadata.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        mb: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedDescription\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body2\",\n                                            children: extractedData.metadata.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"subtitle2\",\n                                    color: \"primary\",\n                                    sx: {\n                                        mb: 1\n                                    },\n                                    children: [\n                                        t(\"createArticle:extractedContent\"),\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        maxHeight: 400,\n                                        overflow: \"auto\",\n                                        border: \"1px solid\",\n                                        borderColor: \"grey.300\",\n                                        p: 2,\n                                        borderRadius: 1,\n                                        backgroundColor: \"grey.50\"\n                                    },\n                                    dangerouslySetInnerHTML: {\n                                        __html: extractedData.content\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, undefined),\n                                extractedData.warnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    severity: \"warning\",\n                                    sx: {\n                                        mt: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            children: [\n                                                t(\"createArticle:conversionWarnings\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: extractedData.warnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: warning.message\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogActions, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                onClick: handleClosePreview,\n                                children: t(\"global:cancel\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                onClick: handleApplyContent,\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SuccessIcon, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 24\n                                }, void 0),\n                                children: t(\"createArticle:applyContent\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"e7V5N1Q8+U90zRa31VhbBxq3qFc=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});