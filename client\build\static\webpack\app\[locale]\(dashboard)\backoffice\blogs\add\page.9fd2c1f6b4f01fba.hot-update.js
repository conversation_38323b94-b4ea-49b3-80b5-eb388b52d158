"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Description!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_icons_uploadIcon_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/icons/uploadIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/uploadIcon.svg\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _mui_icons_material_Check__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/Check */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Check.js\");\n/* harmony import */ var _barrel_optimize_names_Button_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_DialogActions_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DialogActions,DialogContent!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_DialogActions_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DialogActions,DialogContent!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Inject drag state styles\nconst dragActiveStyles = `\n  .file-labels.drag-active {\n    border-color: #1976d2 !important;\n    background-color: rgba(25, 118, 210, 0.04) !important;\n  }\n  .file-labels.disabled {\n    cursor: not-allowed !important;\n    opacity: 0.6 !important;\n  }\n`;\nif (typeof document !== \"undefined\" && !document.getElementById(\"document-importer-styles\")) {\n    const styleSheet = document.createElement(\"style\");\n    styleSheet.id = \"document-importer-styles\";\n    styleSheet.textContent = dragActiveStyles;\n    document.head.appendChild(styleSheet);\n}\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewOpen, setPreviewOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [extractedData, setExtractedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement((image)=>image.read(\"base64\").then((imageBuffer)=>({\n                                src: `data:${image.contentType};base64,${imageBuffer}`\n                            })))\n                }\n            });\n            setProgress(75);\n            const cleanContent = result.value.replace(/<p><\\/p>/g, \"\").replace(/\\s+/g, \" \").trim();\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            setExtractedData(result);\n            setSuccess(true);\n            setPreviewOpen(true);\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) processFile(acceptedFiles[0]);\n    }, []);\n    const { getRootProps, getInputProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing\n    });\n    const handleApplyContent = ()=>{\n        if (extractedData) {\n            onContentExtracted(extractedData.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(extractedData.metadata);\n            }\n            setPreviewOpen(false);\n            setExtractedData(null);\n        }\n    };\n    const handleClosePreview = ()=>{\n        setPreviewOpen(false);\n        setExtractedData(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        sx: {\n            mb: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: [\n                    t(\"createArticle:importDocument\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                sx: {\n                    p: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    ...getRootProps(),\n                    className: `file-labels ${isDragActive ? \"drag-active\" : \"\"} ${disabled || isProcessing ? \"disabled\" : \"\"}`,\n                    style: {\n                        display: \"block\",\n                        border: \"2px dashed #ccc\",\n                        padding: \"20px\",\n                        textAlign: \"center\",\n                        cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\",\n                        opacity: disabled || isProcessing ? 0.6 : 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ...getInputProps()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundImage: `url(\"${_assets_images_icons_uploadIcon_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"]}\")`,\n                                        backgroundSize: \"contain\",\n                                        backgroundRepeat: \"no-repeat\",\n                                        backgroundPosition: \"center\",\n                                        width: 60,\n                                        height: 60,\n                                        margin: \"auto\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"body1\",\n                                    mt: 2,\n                                    children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:importFromDocument\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"caption\",\n                                    children: [\n                                        t(\"createArticle:supportedFormats\"),\n                                        \": .docx, .doc, .txt\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        mt: 1,\n                                        display: \"flex\",\n                                        justifyContent: \"center\",\n                                        gap: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            label: \".docx\",\n                                            size: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            label: \".doc\",\n                                            size: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            label: \".txt\",\n                                            size: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"body2\",\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 257,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, undefined),\n            success && !previewOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                open: previewOpen,\n                onClose: handleClosePreview,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"h6\",\n                                    children: t(\"createArticle:previewExtractedContent\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    onClick: handleClosePreview,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogActions_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        children: extractedData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            children: [\n                                extractedData.metadata.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    mb: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedTitle\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            children: extractedData.metadata.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 17\n                                }, undefined),\n                                extractedData.metadata.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    mb: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedDescription\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"body2\",\n                                            children: extractedData.metadata.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"subtitle2\",\n                                    color: \"primary\",\n                                    sx: {\n                                        mb: 1\n                                    },\n                                    children: [\n                                        t(\"createArticle:extractedContent\"),\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        maxHeight: 400,\n                                        overflow: \"auto\",\n                                        border: \"1px solid\",\n                                        borderColor: \"grey.300\",\n                                        p: 2,\n                                        borderRadius: 1,\n                                        backgroundColor: \"grey.50\"\n                                    },\n                                    dangerouslySetInnerHTML: {\n                                        __html: extractedData.content\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, undefined),\n                                extractedData.warnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    severity: \"warning\",\n                                    sx: {\n                                        mt: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            children: [\n                                                t(\"createArticle:conversionWarnings\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: extractedData.warnings.map((w, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: w.message\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogActions_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                onClick: handleClosePreview,\n                                children: t(\"global:cancel\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                onClick: handleApplyContent,\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Check__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 24\n                                }, void 0),\n                                children: t(\"createArticle:applyContent\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 204,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"7cFeiPgqFhGSzHeGTiXZN3jgdSs=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});