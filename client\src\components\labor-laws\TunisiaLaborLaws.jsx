"use client";
import {
  Container,
  Grid,
  AccordionSummary,
  AccordionDetails,
  Accordion,
} from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import SvgexpandIcon from "../../assets/images/icons/arrowUp.svg";

import SvgCallendar from "@/assets/images/icons/yellow/yellowCalendar.svg";

export default function TunisiaLaborLaws() {
  const [expanded, setExpanded] = useState(1);
  const { t } = useTranslation();
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  return (
    <div id="labor-tn-laws" className="custom-max-width">
      <Container>
        <h2 className="heading-h1">{t("Tunisia:tunisiaLabor:title")}</h2>

        <Grid className="container" container columnSpacing={3} rowSpacing={2}>
          <Grid item xs={12} sm={12} key={1}>
            <Accordion
              elevation={0}
              expanded={expanded === 1}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(1)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Tunisia:tunisiaLabor:workingHours:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Tunisia:tunisiaLabor:workingHours:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Tunisia:tunisiaLabor:workingHours:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Tunisia:tunisiaLabor:workingHours:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Tunisia:tunisiaLabor:workingHours:description2")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={2}>
            <Accordion
              elevation={0}
              expanded={expanded === 2}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(2)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Tunisia:tunisiaLabor:employmentContracts:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Tunisia:tunisiaLabor:employmentContracts:title1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>
                      {t("Tunisia:tunisiaLabor:employmentContracts:data1")}
                    </li>
                    <li>
                      {t("Tunisia:tunisiaLabor:employmentContracts:data2")}
                    </li>
                    <li>
                      {t("Tunisia:tunisiaLabor:employmentContracts:data3")}
                    </li>
                    <li>
                      {t("Tunisia:tunisiaLabor:employmentContracts:data4")}
                    </li>
                    <li>
                      {t("Tunisia:tunisiaLabor:employmentContracts:data5")}
                    </li>
                    <li>
                      {t("Tunisia:tunisiaLabor:employmentContracts:data6")}
                    </li>
                    <li>
                      {t("Tunisia:tunisiaLabor:employmentContracts:data7")}
                    </li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Tunisia:tunisiaLabor:employmentContracts:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Tunisia:tunisiaLabor:employmentContracts:description")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>
                      {t("Tunisia:tunisiaLabor:employmentContracts:dataS1")}
                    </li>
                    <li>
                      {t("Tunisia:tunisiaLabor:employmentContracts:dataS2")}
                    </li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={3}>
            <Accordion
              elevation={0}
              expanded={expanded === 3}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(3)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Tunisia:tunisiaLabor:termination:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Tunisia:tunisiaLabor:termination:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Tunisia:tunisiaLabor:termination:description1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("Tunisia:tunisiaLabor:termination:data1")}</li>
                    <li>{t("Tunisia:tunisiaLabor:termination:data2")}</li>
                    <li>{t("Tunisia:tunisiaLabor:termination:data3")}</li>
                    <li>{t("Tunisia:tunisiaLabor:termination:data4")}</li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Tunisia:tunisiaLabor:termination:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Tunisia:tunisiaLabor:termination:description2")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Tunisia:tunisiaLabor:termination:title3")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Tunisia:tunisiaLabor:termination:description3")}{" "}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={4}>
            <Accordion
              elevation={0}
              expanded={expanded === 4}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(4)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Tunisia:tunisiaLabor:payroll:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Tunisia:tunisiaLabor:payroll:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Tunisia:tunisiaLabor:payroll:description")}
                  </p>
                </div>
                <div className="payroll-tn">
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("Tunisia:tunisiaLabor:payroll:fiscalYear:title")}
                    </p>
                    <p className="date">
                      {t("Tunisia:tunisiaLabor:payroll:fiscalYear:date1")}
                      <br />
                      {t("Tunisia:tunisiaLabor:payroll:fiscalYear:date2")}
                    </p>
                    <p className="paragraph">
                      {t("Tunisia:tunisiaLabor:payroll:fiscalYear:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("Tunisia:tunisiaLabor:payroll:payrollCycle:title")}
                    </p>
                    <p className="date">
                      {t("Tunisia:tunisiaLabor:payroll:payrollCycle:date")}
                    </p>
                    <p className="paragraph">
                      {t(
                        "Tunisia:tunisiaLabor:payroll:payrollCycle:description"
                      )}
                    </p>
                  </div>

                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("Tunisia:tunisiaLabor:payroll:minimumWage:title")}
                    </p>
                    <p className="date">
                      {t("Tunisia:tunisiaLabor:payroll:minimumWage:wage")}
                      <br />
                      {t("Tunisia:tunisiaLabor:payroll:minimumWage:date")}​
                    </p>
                    <p className="paragraph">
                      {t(
                        "Tunisia:tunisiaLabor:payroll:minimumWage:description"
                      )}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t(
                        "Tunisia:tunisiaLabor:payroll:payrollManagement:title"
                      )}
                    </p>
                    <p className="date">
                      {t(
                        "Tunisia:tunisiaLabor:payroll:payrollManagement:date1"
                      )}
                      <br />
                      {t(
                        "Tunisia:tunisiaLabor:payroll:payrollManagement:date2"
                      )}
                    </p>
                    <p className="paragraph">
                      {t(
                        "Tunisia:tunisiaLabor:payroll:payrollManagement:description"
                      )}
                    </p>
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={5}>
            <Accordion
              elevation={0}
              expanded={expanded === 5}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(5)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Tunisia:tunisiaLabor:leaveEntitlements:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Tunisia:tunisiaLabor:leaveEntitlements:description")}
                  </p>
                  <div className="holidays-dates">
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS1:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS1:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS2:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS2:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS3:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS3:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS4:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS4:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS5:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS5:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS6:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS6:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS7:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS7:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS8:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS8:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS9:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS9:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS10:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS10:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS11:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS11:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS12:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS12:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS13:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS13:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS14:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS14:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS15:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS15:title"
                        )}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Tunisia:tunisiaLabor:leaveEntitlements:leaves:annualLeave:title"
                    )}
                  </p>
                  <ul className="service-description paragraph">
                    <li>
                      {t(
                        "Tunisia:tunisiaLabor:leaveEntitlements:leaves:annualLeave:description1"
                      )}
                    </li>
                    <li>
                      {t(
                        "Tunisia:tunisiaLabor:leaveEntitlements:leaves:annualLeave:description2"
                      )}
                    </li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Tunisia:tunisiaLabor:leaveEntitlements:leaves:maternityLeave:title"
                    )}
                  </p>
                  <ul className="service-description paragraph">
                    <li>
                      {t(
                        "Tunisia:tunisiaLabor:leaveEntitlements:leaves:maternityLeave:description1"
                      )}
                    </li>
                    <li>
                      {t(
                        "Tunisia:tunisiaLabor:leaveEntitlements:leaves:maternityLeave:description2"
                      )}
                    </li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Tunisia:tunisiaLabor:leaveEntitlements:leaves:paternityLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "Tunisia:tunisiaLabor:leaveEntitlements:leaves:paternityLeave:description"
                    )}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Tunisia:tunisiaLabor:leaveEntitlements:leaves:sickLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "Tunisia:tunisiaLabor:leaveEntitlements:leaves:sickLeave:description"
                    )}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={6}>
            <Accordion
              elevation={0}
              expanded={expanded === 6}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(6)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Tunisia:tunisiaLabor:tax:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("Tunisia:tunisiaLabor:tax:description")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Tunisia:tunisiaLabor:tax:title1")}
                  </p>

                  <p className="service-description paragraph">
                    {t("Tunisia:tunisiaLabor:tax:description1")}
                  </p>

                  <ul className="service-description paragraph">
                    <li> {t("Tunisia:tunisiaLabor:tax:data1")}</li>
                    <li> {t("Tunisia:tunisiaLabor:tax:data2")}</li>
                    <li> {t("Tunisia:tunisiaLabor:tax:data3")}</li>
                    <li> {t("Tunisia:tunisiaLabor:tax:data4")}</li>
                  </ul>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {" "}
                    {t("Tunisia:tunisiaLabor:tax:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("Tunisia:tunisiaLabor:tax:description2")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("Tunisia:tunisiaLabor:tax:dataS1")}</li>
                    <li>{t("Tunisia:tunisiaLabor:tax:dataS2")}</li>
                    <li>{t("Tunisia:tunisiaLabor:tax:dataS3")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>

          <Grid item xs={12} sm={12} key={7}>
            <Accordion
              elevation={0}
              expanded={expanded === 7}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(7)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Tunisia:tunisiaLabor:visa:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("Tunisia:tunisiaLabor:visa:description1")}
                    <br />
                    {t("Tunisia:tunisiaLabor:visa:description2")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Tunisia:tunisiaLabor:visa:title1")}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t("Tunisia:tunisiaLabor:visa:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("Tunisia:tunisiaLabor:visa:description3")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Tunisia:tunisiaLabor:visa:title3")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("Tunisia:tunisiaLabor:visa:data1")}</li>
                    <li>{t("Tunisia:tunisiaLabor:visa:data2")}</li>
                    <li>{t("Tunisia:tunisiaLabor:visa:data3")}</li>
                    <li>{t("Tunisia:tunisiaLabor:visa:data4")}</li>
                    <li>{t("Tunisia:tunisiaLabor:visa:data5")}</li>
                    <li>{t("Tunisia:tunisiaLabor:visa:data6")}</li>
                    <li>{t("Tunisia:tunisiaLabor:visa:data7")}</li>
                    <li>{t("Tunisia:tunisiaLabor:visa:data8")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}
