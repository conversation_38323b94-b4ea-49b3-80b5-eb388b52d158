import dynamic from "next/dynamic";
import initTranslations from "@/app/i18n";
import { Suspense } from "react";
import { headers } from "next/headers";

import r1 from "@/assets/images/services/recrutement1.png";
import r2 from "@/assets/images/services/recrutement2.png";
import r3 from "@/assets/images/services/recrutement3.png";
import r4 from "@/assets/images/services/recrutement4.png";
import InsightsSection from "@/components/sections/InsightsSection";
import serviceimgS1 from "@/assets/images/services/service1.png";
import { contactData } from "@/utils/constants";
import { websiteRoutesList } from "../../../helpers/routesList";
import Loading from "@/components/loading/Loading";
import PayrollIcon from "@/assets/images/services/icons/Payroll.svg";
import HRIcon from "@/assets/images/services/icons/HR.svg";
import DirectHireIcon from "@/assets/images/services/icons/directhire.svg";
import AiIcon from "@/assets/images/services/icons/Ai.svg";
import TechassistanceIcon from "@/assets/images/services/icons/techassistance.svg";
import PayrollBGIcon from "@/assets/images/icons/moneyIcon.svg";
import HRBGIcon from "@/assets/images/icons/HrIcon.svg";
import DirectHireBGIcon from "@/assets/images/icons/directHireIcon.svg";
import AiBGIcon from "@/assets/images/icons/AiIcon.svg";
import TechassistanceBGIcon from "@/assets/images/icons/techAssistanceIcon.svg";
import HomeSlider from "@/components/sections/HomeSlider";
import OurPartners from "@/components/sections/OurPartners";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }`;
  const languages = {
    fr: `https://www.pentabell.com/fr/`,
    en: `https://www.pentabell.com/`,
    "x-default": `https://www.pentabell.com/`,
  };

  const { t } = await initTranslations(locale, [
    "homePage",
    "global",
    "application",
    "contactUs",
    "opportunities",
  ]);

  return {
    title: t("homePage:meta:metaTitle"),
    description: t("homePage:meta:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

const renderLoader = () => <Loading />;

async function homePage({ params: { locale } }) {
  const { t } = await initTranslations(locale, [
    "homePage",
    "global",
    "Tunisia",
    "contactUs",
    "opportunities",
  ]);

  const userAgent = headers().get("user-agent") || "";
  const isMobile = /mobile/i.test(userAgent);
  const deviceType = isMobile ? "mobile" : "desktop";
  const isMobileSSR = deviceType === "mobile";

  const SERVICES = [
    {
      id: "s1",
      title: t("homePage:s4:stitle1"),
      description: t("homePage:s4:description1"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      linkText: t("homePage:s4:learnmore"),
      img: serviceimgS1,
      altImg: t("Tunisia:services:dataS1:altImg"),
      icon: <PayrollIcon />,
      bgIcon: <PayrollBGIcon className="svg-service-icon" />,
    },
    {
      id: "s2",
      title: t("homePage:s4:stitle2"),
      description: t("homePage:s4:description2"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
      linkText: t("homePage:s4:see"),
      img: r4,
      altImg: t("Tunisia:services:dataS2:altImg"),
      icon: <HRIcon />,
      bgIcon: <HRBGIcon className="svg-service-icon" />,
    },
    {
      id: "s3",
      title: t("homePage:s4:stitle3"),
      description: t("homePage:s4:description3"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
      linkText: t("homePage:s4:findOutMore"),
      img: r3,
      altImg: t("Tunisia:services:dataS3:altImg"),
      icon: <TechassistanceIcon />,
      bgIcon: <TechassistanceBGIcon className="svg-service-icon" />,
    },
    {
      id: "s4",
      title: t("homePage:s4:stitle5"),
      description: t("homePage:s4:description6"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
      linkText: t("homePage:s4:learnmore"),
      img: r2,
      altImg: t("Tunisia:services:dataS4:altImg"),
      icon: <AiIcon />,
      bgIcon: <AiBGIcon className="svg-service-icon" />,
    },
    {
      id: "s5",
      title: t("homePage:s4:stitle4"),
      description: t("homePage:s4:description4"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      linkText: t("homePage:s4:startHiring"),
      img: r1,
      altImg: t("Tunisia:services:dataS5:altImg"),
      icon: <DirectHireIcon />,
      bgIcon: <DirectHireBGIcon className="svg-service-icon" />,
    },
  ];

  const GlobalMap = dynamic(() => import("@/components/sections/GlobalMap"), {
    ssr: false,
  });

  const WhatTheySay = dynamic(
    () => import("@/components/sections/WhatTheySay"),
    {
      ssr: true,
    }
  );

  const GetInTouchSection = dynamic(
    () => import("@/components/sections/GetInTouchSection"),
    {
      ssr: false,
    }
  );

  const ISOSection = dynamic(() => import("@/components/sections/ISOSection"), {
    ssr: true,
  });

  const LatestJobOffers = dynamic(
    () => import("@/components/sections/LatestJobOffers"),
    {
      ssr: true,
    }
  );

  const IntroSection = dynamic(
    () => import("@/components/sections/IntroSection"),
    {
      ssr: true,
    }
  );

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "http://schema.org",
            "@type": "WebSite",
            url: `https://www.pentabell.com`,
            potentialAction: {
              "@type": "SearchAction",
              target:
                "https://www.pentabell.com/opportunities/?keyWord={search_term_string}",
              "query-input": "required name=search_term_string",
            },
          }),
        }}
      />
      {contactData(t, locale)?.map((item, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(item),
          }}
        />
      ))}
      <div>
        <Suspense fallback={renderLoader()}>
          <HomeSlider locale={locale} isMobileSSR={isMobileSSR} />
          <OurPartners disableTxt />
          <IntroSection locale={locale} />
          <br></br>
          <div className="gradient-blue">
            <GlobalMap
              locale={locale}
              title={t("homePage:s4:title")}
              SERVICES={SERVICES}
              defaultService={SERVICES[0]}
            />
          </div>
          <LatestJobOffers language={locale} />
          <WhatTheySay
            title={t("homePage:s4:title")}
            SERVICES={SERVICES}
            defaultImage={serviceimgS1}
          />
          <InsightsSection language={locale} />
          <ISOSection />
          <GetInTouchSection />
        </Suspense>
      </div>
    </>
  );
}

export default homePage;
