import { Container } from "@mui/material";

import CustomButton from "../ui/CustomButton";

import SvgArrow from "@/assets/images/icons/arrow.svg";
import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";
import franceImg from "@/assets/images/offices/france.png";
import switzerlandImg from "@/assets/images/offices/switzerland.png";

import Image from "next/image";
import { findCountryFlag, findCountryLabel } from "@/utils/functions";
import { OfficesCountries } from "@/config/countries";

function LocationsInEurope({t}) {
  const contacts = [
    {
      title: t("europe:locations:france:title"),
      locations: [
        t("europe:locations:france:address"),
      ],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      // link: `https://www.google.com/maps?q=${encodeURIComponent(t("europe:locations:france:address"))}`,
      link:"#",
      img: franceImg,
      country: OfficesCountries.FRANCE,
      alt: t("europe:locations:france:alt")  
    },  
    {
      title: t("europe:locations:switzerland:title"),
      locations: [
        t("europe:locations:switzerland:address"),
      ],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: "#",
      img: switzerlandImg,
      country: OfficesCountries.SWITZERLAND,
      alt: t("europe:locations:switzerland:alt")  
    },
   
  ];
  return (
    <div id="africa-locations">
      <Container className="custom-max-width">
        <p className="sub-heading text-center text-blue">{t("europe:locations:subTitle")}</p>
        <h2 className="heading-h1 text-center">
        {t("europe:locations:title")}
        </h2>
      </Container>
      <Container className="contact-items-section custom-max-width">
        {contacts.map((contact, index) => (
          <div className="contact-item" key={index}>
            <div>
              <div className="country-img">
                {" "}
                <p className="country-label">
                  <img
                    width={22}
                    height={14}
                    src={findCountryFlag(contact.country)}
                    alt={findCountryLabel(contact.country)}
                    loading="lazy"
                  />
                  {findCountryLabel(contact.country)}
                </p>
                <img
                  width={388}
                  height={253}
                  src={contact.img.src}
                  alt={contact.alt}  
                  loading="lazy"
                />
              </div>
              <h3 className="sub-heading text-blue">{contact.title}</h3>
              <div>
                {contact.locations.map((location, locIndex) => (
                  <p className="row-item" key={locIndex}>
                    <span>
                      <SvglocationPin />
                    </span>
                    {location}
                  </p>
                ))}

                <p className="row-item">
                  <span>
                    <SvgcallUs />
                  </span>
                  {contact.phones.map((phone, phoneIndex) => (
                    <>
                      {" "}
                      {contact.phones.length > 1 ? (
                        <>
                          {phone} <br />
                        </>
                      ) : (
                        phone
                      )}
                    </>
                  ))}
                </p>

                <p className="row-item">
                  <span>
                    <Svgemail />
                  </span>
                  {contact.email}
                </p>
              </div>
            </div>
            <div className="btns">
              <CustomButton
                text={t("europe:locations:exploreMore")}
                className={"btn btn-outlined "}
                link={contact.link}
              />
              <CustomButton
                text={t("europe:locations:viewOnMap")}
                className={"btn btn-ghost"}
                link={contact.link}
                icon={<SvgArrow />}
              />
            </div>
          </div>
        ))}
      </Container>
    </div>
  );
}

export default LocationsInEurope;
