"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dnd-core";
exports.ids = ["vendor-chunks/dnd-core"];
exports.modules = {

/***/ "(ssr)/./node_modules/dnd-core/node_modules/redux/es/redux.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/node_modules/redux/es/redux.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __DO_NOT_USE__ActionTypes: () => (/* binding */ ActionTypes),\n/* harmony export */   applyMiddleware: () => (/* binding */ applyMiddleware),\n/* harmony export */   bindActionCreators: () => (/* binding */ bindActionCreators),\n/* harmony export */   combineReducers: () => (/* binding */ combineReducers),\n/* harmony export */   compose: () => (/* binding */ compose),\n/* harmony export */   createStore: () => (/* binding */ createStore),\n/* harmony export */   legacy_createStore: () => (/* binding */ legacy_createStore)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n\n\n/**\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\n *\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\n * during build.\n * @param {number} code\n */\nfunction formatProdErrorMessage(code) {\n  return \"Minified Redux error #\" + code + \"; visit https://redux.js.org/Errors?code=\" + code + \" for the full message or \" + 'use the non-minified dev environment for full errors. ';\n}\n\n// Inlined version of the `symbol-observable` polyfill\nvar $$observable = (function () {\n  return typeof Symbol === 'function' && Symbol.observable || '@@observable';\n})();\n\n/**\n * These are private action types reserved by Redux.\n * For any unknown actions, you must return the current state.\n * If the current state is undefined, you must return the initial state.\n * Do not reference these action types directly in your code.\n */\nvar randomString = function randomString() {\n  return Math.random().toString(36).substring(7).split('').join('.');\n};\n\nvar ActionTypes = {\n  INIT: \"@@redux/INIT\" + randomString(),\n  REPLACE: \"@@redux/REPLACE\" + randomString(),\n  PROBE_UNKNOWN_ACTION: function PROBE_UNKNOWN_ACTION() {\n    return \"@@redux/PROBE_UNKNOWN_ACTION\" + randomString();\n  }\n};\n\n/**\n * @param {any} obj The object to inspect.\n * @returns {boolean} True if the argument appears to be a plain object.\n */\nfunction isPlainObject(obj) {\n  if (typeof obj !== 'object' || obj === null) return false;\n  var proto = obj;\n\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n\n  return Object.getPrototypeOf(obj) === proto;\n}\n\n// Inlined / shortened version of `kindOf` from https://github.com/jonschlinkert/kind-of\nfunction miniKindOf(val) {\n  if (val === void 0) return 'undefined';\n  if (val === null) return 'null';\n  var type = typeof val;\n\n  switch (type) {\n    case 'boolean':\n    case 'string':\n    case 'number':\n    case 'symbol':\n    case 'function':\n      {\n        return type;\n      }\n  }\n\n  if (Array.isArray(val)) return 'array';\n  if (isDate(val)) return 'date';\n  if (isError(val)) return 'error';\n  var constructorName = ctorName(val);\n\n  switch (constructorName) {\n    case 'Symbol':\n    case 'Promise':\n    case 'WeakMap':\n    case 'WeakSet':\n    case 'Map':\n    case 'Set':\n      return constructorName;\n  } // other\n\n\n  return type.slice(8, -1).toLowerCase().replace(/\\s/g, '');\n}\n\nfunction ctorName(val) {\n  return typeof val.constructor === 'function' ? val.constructor.name : null;\n}\n\nfunction isError(val) {\n  return val instanceof Error || typeof val.message === 'string' && val.constructor && typeof val.constructor.stackTraceLimit === 'number';\n}\n\nfunction isDate(val) {\n  if (val instanceof Date) return true;\n  return typeof val.toDateString === 'function' && typeof val.getDate === 'function' && typeof val.setDate === 'function';\n}\n\nfunction kindOf(val) {\n  var typeOfVal = typeof val;\n\n  if (true) {\n    typeOfVal = miniKindOf(val);\n  }\n\n  return typeOfVal;\n}\n\n/**\n * @deprecated\n *\n * **We recommend using the `configureStore` method\n * of the `@reduxjs/toolkit` package**, which replaces `createStore`.\n *\n * Redux Toolkit is our recommended approach for writing Redux logic today,\n * including store setup, reducers, data fetching, and more.\n *\n * **For more details, please read this Redux docs page:**\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * `configureStore` from Redux Toolkit is an improved version of `createStore` that\n * simplifies setup and helps avoid common bugs.\n *\n * You should not be using the `redux` core package by itself today, except for learning purposes.\n * The `createStore` method from the core `redux` package will not be removed, but we encourage\n * all users to migrate to using Redux Toolkit for all Redux code.\n *\n * If you want to use `createStore` without this visual deprecation warning, use\n * the `legacy_createStore` import instead:\n *\n * `import { legacy_createStore as createStore} from 'redux'`\n *\n */\n\nfunction createStore(reducer, preloadedState, enhancer) {\n  var _ref2;\n\n  if (typeof preloadedState === 'function' && typeof enhancer === 'function' || typeof enhancer === 'function' && typeof arguments[3] === 'function') {\n    throw new Error( false ? 0 : 'It looks like you are passing several store enhancers to ' + 'createStore(). This is not supported. Instead, compose them ' + 'together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.');\n  }\n\n  if (typeof preloadedState === 'function' && typeof enhancer === 'undefined') {\n    enhancer = preloadedState;\n    preloadedState = undefined;\n  }\n\n  if (typeof enhancer !== 'undefined') {\n    if (typeof enhancer !== 'function') {\n      throw new Error( false ? 0 : \"Expected the enhancer to be a function. Instead, received: '\" + kindOf(enhancer) + \"'\");\n    }\n\n    return enhancer(createStore)(reducer, preloadedState);\n  }\n\n  if (typeof reducer !== 'function') {\n    throw new Error( false ? 0 : \"Expected the root reducer to be a function. Instead, received: '\" + kindOf(reducer) + \"'\");\n  }\n\n  var currentReducer = reducer;\n  var currentState = preloadedState;\n  var currentListeners = [];\n  var nextListeners = currentListeners;\n  var isDispatching = false;\n  /**\n   * This makes a shallow copy of currentListeners so we can use\n   * nextListeners as a temporary list while dispatching.\n   *\n   * This prevents any bugs around consumers calling\n   * subscribe/unsubscribe in the middle of a dispatch.\n   */\n\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = currentListeners.slice();\n    }\n  }\n  /**\n   * Reads the state tree managed by the store.\n   *\n   * @returns {any} The current state tree of your application.\n   */\n\n\n  function getState() {\n    if (isDispatching) {\n      throw new Error( false ? 0 : 'You may not call store.getState() while the reducer is executing. ' + 'The reducer has already received the state as an argument. ' + 'Pass it down from the top reducer instead of reading it from the store.');\n    }\n\n    return currentState;\n  }\n  /**\n   * Adds a change listener. It will be called any time an action is dispatched,\n   * and some part of the state tree may potentially have changed. You may then\n   * call `getState()` to read the current state tree inside the callback.\n   *\n   * You may call `dispatch()` from a change listener, with the following\n   * caveats:\n   *\n   * 1. The subscriptions are snapshotted just before every `dispatch()` call.\n   * If you subscribe or unsubscribe while the listeners are being invoked, this\n   * will not have any effect on the `dispatch()` that is currently in progress.\n   * However, the next `dispatch()` call, whether nested or not, will use a more\n   * recent snapshot of the subscription list.\n   *\n   * 2. The listener should not expect to see all state changes, as the state\n   * might have been updated multiple times during a nested `dispatch()` before\n   * the listener is called. It is, however, guaranteed that all subscribers\n   * registered before the `dispatch()` started will be called with the latest\n   * state by the time it exits.\n   *\n   * @param {Function} listener A callback to be invoked on every dispatch.\n   * @returns {Function} A function to remove this change listener.\n   */\n\n\n  function subscribe(listener) {\n    if (typeof listener !== 'function') {\n      throw new Error( false ? 0 : \"Expected the listener to be a function. Instead, received: '\" + kindOf(listener) + \"'\");\n    }\n\n    if (isDispatching) {\n      throw new Error( false ? 0 : 'You may not call store.subscribe() while the reducer is executing. ' + 'If you would like to be notified after the store has been updated, subscribe from a ' + 'component and invoke store.getState() in the callback to access the latest state. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n    }\n\n    var isSubscribed = true;\n    ensureCanMutateNextListeners();\n    nextListeners.push(listener);\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return;\n      }\n\n      if (isDispatching) {\n        throw new Error( false ? 0 : 'You may not unsubscribe from a store listener while the reducer is executing. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n      }\n\n      isSubscribed = false;\n      ensureCanMutateNextListeners();\n      var index = nextListeners.indexOf(listener);\n      nextListeners.splice(index, 1);\n      currentListeners = null;\n    };\n  }\n  /**\n   * Dispatches an action. It is the only way to trigger a state change.\n   *\n   * The `reducer` function, used to create the store, will be called with the\n   * current state tree and the given `action`. Its return value will\n   * be considered the **next** state of the tree, and the change listeners\n   * will be notified.\n   *\n   * The base implementation only supports plain object actions. If you want to\n   * dispatch a Promise, an Observable, a thunk, or something else, you need to\n   * wrap your store creating function into the corresponding middleware. For\n   * example, see the documentation for the `redux-thunk` package. Even the\n   * middleware will eventually dispatch plain object actions using this method.\n   *\n   * @param {Object} action A plain object representing “what changed”. It is\n   * a good idea to keep actions serializable so you can record and replay user\n   * sessions, or use the time travelling `redux-devtools`. An action must have\n   * a `type` property which may not be `undefined`. It is a good idea to use\n   * string constants for action types.\n   *\n   * @returns {Object} For convenience, the same action object you dispatched.\n   *\n   * Note that, if you use a custom middleware, it may wrap `dispatch()` to\n   * return something else (for example, a Promise you can await).\n   */\n\n\n  function dispatch(action) {\n    if (!isPlainObject(action)) {\n      throw new Error( false ? 0 : \"Actions must be plain objects. Instead, the actual type was: '\" + kindOf(action) + \"'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.\");\n    }\n\n    if (typeof action.type === 'undefined') {\n      throw new Error( false ? 0 : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n    }\n\n    if (isDispatching) {\n      throw new Error( false ? 0 : 'Reducers may not dispatch actions.');\n    }\n\n    try {\n      isDispatching = true;\n      currentState = currentReducer(currentState, action);\n    } finally {\n      isDispatching = false;\n    }\n\n    var listeners = currentListeners = nextListeners;\n\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n\n    return action;\n  }\n  /**\n   * Replaces the reducer currently used by the store to calculate the state.\n   *\n   * You might need this if your app implements code splitting and you want to\n   * load some of the reducers dynamically. You might also need this if you\n   * implement a hot reloading mechanism for Redux.\n   *\n   * @param {Function} nextReducer The reducer for the store to use instead.\n   * @returns {void}\n   */\n\n\n  function replaceReducer(nextReducer) {\n    if (typeof nextReducer !== 'function') {\n      throw new Error( false ? 0 : \"Expected the nextReducer to be a function. Instead, received: '\" + kindOf(nextReducer));\n    }\n\n    currentReducer = nextReducer; // This action has a similiar effect to ActionTypes.INIT.\n    // Any reducers that existed in both the new and old rootReducer\n    // will receive the previous state. This effectively populates\n    // the new state tree with any relevant data from the old one.\n\n    dispatch({\n      type: ActionTypes.REPLACE\n    });\n  }\n  /**\n   * Interoperability point for observable/reactive libraries.\n   * @returns {observable} A minimal observable of state changes.\n   * For more information, see the observable proposal:\n   * https://github.com/tc39/proposal-observable\n   */\n\n\n  function observable() {\n    var _ref;\n\n    var outerSubscribe = subscribe;\n    return _ref = {\n      /**\n       * The minimal observable subscription method.\n       * @param {Object} observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns {subscription} An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe: function subscribe(observer) {\n        if (typeof observer !== 'object' || observer === null) {\n          throw new Error( false ? 0 : \"Expected the observer to be an object. Instead, received: '\" + kindOf(observer) + \"'\");\n        }\n\n        function observeState() {\n          if (observer.next) {\n            observer.next(getState());\n          }\n        }\n\n        observeState();\n        var unsubscribe = outerSubscribe(observeState);\n        return {\n          unsubscribe: unsubscribe\n        };\n      }\n    }, _ref[$$observable] = function () {\n      return this;\n    }, _ref;\n  } // When a store is created, an \"INIT\" action is dispatched so that every\n  // reducer returns their initial state. This effectively populates\n  // the initial state tree.\n\n\n  dispatch({\n    type: ActionTypes.INIT\n  });\n  return _ref2 = {\n    dispatch: dispatch,\n    subscribe: subscribe,\n    getState: getState,\n    replaceReducer: replaceReducer\n  }, _ref2[$$observable] = observable, _ref2;\n}\n/**\n * Creates a Redux store that holds the state tree.\n *\n * **We recommend using `configureStore` from the\n * `@reduxjs/toolkit` package**, which replaces `createStore`:\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\n\nvar legacy_createStore = createStore;\n\n/**\n * Prints a warning in the console if it exists.\n *\n * @param {String} message The warning message.\n * @returns {void}\n */\nfunction warning(message) {\n  /* eslint-disable no-console */\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    console.error(message);\n  }\n  /* eslint-enable no-console */\n\n\n  try {\n    // This error was thrown as a convenience so that if you enable\n    // \"break on all exceptions\" in your console,\n    // it would pause the execution at this line.\n    throw new Error(message);\n  } catch (e) {} // eslint-disable-line no-empty\n\n}\n\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n  var reducerKeys = Object.keys(reducers);\n  var argumentName = action && action.type === ActionTypes.INIT ? 'preloadedState argument passed to createStore' : 'previous state received by the reducer';\n\n  if (reducerKeys.length === 0) {\n    return 'Store does not have a valid reducer. Make sure the argument passed ' + 'to combineReducers is an object whose values are reducers.';\n  }\n\n  if (!isPlainObject(inputState)) {\n    return \"The \" + argumentName + \" has unexpected type of \\\"\" + kindOf(inputState) + \"\\\". Expected argument to be an object with the following \" + (\"keys: \\\"\" + reducerKeys.join('\", \"') + \"\\\"\");\n  }\n\n  var unexpectedKeys = Object.keys(inputState).filter(function (key) {\n    return !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key];\n  });\n  unexpectedKeys.forEach(function (key) {\n    unexpectedKeyCache[key] = true;\n  });\n  if (action && action.type === ActionTypes.REPLACE) return;\n\n  if (unexpectedKeys.length > 0) {\n    return \"Unexpected \" + (unexpectedKeys.length > 1 ? 'keys' : 'key') + \" \" + (\"\\\"\" + unexpectedKeys.join('\", \"') + \"\\\" found in \" + argumentName + \". \") + \"Expected to find one of the known reducer keys instead: \" + (\"\\\"\" + reducerKeys.join('\", \"') + \"\\\". Unexpected keys will be ignored.\");\n  }\n}\n\nfunction assertReducerShape(reducers) {\n  Object.keys(reducers).forEach(function (key) {\n    var reducer = reducers[key];\n    var initialState = reducer(undefined, {\n      type: ActionTypes.INIT\n    });\n\n    if (typeof initialState === 'undefined') {\n      throw new Error( false ? 0 : \"The slice reducer for key \\\"\" + key + \"\\\" returned undefined during initialization. \" + \"If the state passed to the reducer is undefined, you must \" + \"explicitly return the initial state. The initial state may \" + \"not be undefined. If you don't want to set a value for this reducer, \" + \"you can use null instead of undefined.\");\n    }\n\n    if (typeof reducer(undefined, {\n      type: ActionTypes.PROBE_UNKNOWN_ACTION()\n    }) === 'undefined') {\n      throw new Error( false ? 0 : \"The slice reducer for key \\\"\" + key + \"\\\" returned undefined when probed with a random type. \" + (\"Don't try to handle '\" + ActionTypes.INIT + \"' or other actions in \\\"redux/*\\\" \") + \"namespace. They are considered private. Instead, you must return the \" + \"current state for any unknown actions, unless it is undefined, \" + \"in which case you must return the initial state, regardless of the \" + \"action type. The initial state may not be undefined, but can be null.\");\n    }\n  });\n}\n/**\n * Turns an object whose values are different reducer functions, into a single\n * reducer function. It will call every child reducer, and gather their results\n * into a single state object, whose keys correspond to the keys of the passed\n * reducer functions.\n *\n * @param {Object} reducers An object whose values correspond to different\n * reducer functions that need to be combined into one. One handy way to obtain\n * it is to use ES6 `import * as reducers` syntax. The reducers may never return\n * undefined for any action. Instead, they should return their initial state\n * if the state passed to them was undefined, and the current state for any\n * unrecognized action.\n *\n * @returns {Function} A reducer function that invokes every reducer inside the\n * passed object, and builds a state object with the same shape.\n */\n\n\nfunction combineReducers(reducers) {\n  var reducerKeys = Object.keys(reducers);\n  var finalReducers = {};\n\n  for (var i = 0; i < reducerKeys.length; i++) {\n    var key = reducerKeys[i];\n\n    if (true) {\n      if (typeof reducers[key] === 'undefined') {\n        warning(\"No reducer provided for key \\\"\" + key + \"\\\"\");\n      }\n    }\n\n    if (typeof reducers[key] === 'function') {\n      finalReducers[key] = reducers[key];\n    }\n  }\n\n  var finalReducerKeys = Object.keys(finalReducers); // This is used to make sure we don't warn about the same\n  // keys multiple times.\n\n  var unexpectedKeyCache;\n\n  if (true) {\n    unexpectedKeyCache = {};\n  }\n\n  var shapeAssertionError;\n\n  try {\n    assertReducerShape(finalReducers);\n  } catch (e) {\n    shapeAssertionError = e;\n  }\n\n  return function combination(state, action) {\n    if (state === void 0) {\n      state = {};\n    }\n\n    if (shapeAssertionError) {\n      throw shapeAssertionError;\n    }\n\n    if (true) {\n      var warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n\n      if (warningMessage) {\n        warning(warningMessage);\n      }\n    }\n\n    var hasChanged = false;\n    var nextState = {};\n\n    for (var _i = 0; _i < finalReducerKeys.length; _i++) {\n      var _key = finalReducerKeys[_i];\n      var reducer = finalReducers[_key];\n      var previousStateForKey = state[_key];\n      var nextStateForKey = reducer(previousStateForKey, action);\n\n      if (typeof nextStateForKey === 'undefined') {\n        var actionType = action && action.type;\n        throw new Error( false ? 0 : \"When called with an action of type \" + (actionType ? \"\\\"\" + String(actionType) + \"\\\"\" : '(unknown type)') + \", the slice reducer for key \\\"\" + _key + \"\\\" returned undefined. \" + \"To ignore an action, you must explicitly return the previous state. \" + \"If you want this reducer to hold no value, you can return null instead of undefined.\");\n      }\n\n      nextState[_key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n\n    hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n    return hasChanged ? nextState : state;\n  };\n}\n\nfunction bindActionCreator(actionCreator, dispatch) {\n  return function () {\n    return dispatch(actionCreator.apply(this, arguments));\n  };\n}\n/**\n * Turns an object whose values are action creators, into an object with the\n * same keys, but with every function wrapped into a `dispatch` call so they\n * may be invoked directly. This is just a convenience method, as you can call\n * `store.dispatch(MyActionCreators.doSomething())` yourself just fine.\n *\n * For convenience, you can also pass an action creator as the first argument,\n * and get a dispatch wrapped function in return.\n *\n * @param {Function|Object} actionCreators An object whose values are action\n * creator functions. One handy way to obtain it is to use ES6 `import * as`\n * syntax. You may also pass a single function.\n *\n * @param {Function} dispatch The `dispatch` function available on your Redux\n * store.\n *\n * @returns {Function|Object} The object mimicking the original object, but with\n * every action creator wrapped into the `dispatch` call. If you passed a\n * function as `actionCreators`, the return value will also be a single\n * function.\n */\n\n\nfunction bindActionCreators(actionCreators, dispatch) {\n  if (typeof actionCreators === 'function') {\n    return bindActionCreator(actionCreators, dispatch);\n  }\n\n  if (typeof actionCreators !== 'object' || actionCreators === null) {\n    throw new Error( false ? 0 : \"bindActionCreators expected an object or a function, but instead received: '\" + kindOf(actionCreators) + \"'. \" + \"Did you write \\\"import ActionCreators from\\\" instead of \\\"import * as ActionCreators from\\\"?\");\n  }\n\n  var boundActionCreators = {};\n\n  for (var key in actionCreators) {\n    var actionCreator = actionCreators[key];\n\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n    }\n  }\n\n  return boundActionCreators;\n}\n\n/**\n * Composes single-argument functions from right to left. The rightmost\n * function can take multiple arguments as it provides the signature for\n * the resulting composite function.\n *\n * @param {...Function} funcs The functions to compose.\n * @returns {Function} A function obtained by composing the argument functions\n * from right to left. For example, compose(f, g, h) is identical to doing\n * (...args) => f(g(h(...args))).\n */\nfunction compose() {\n  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) {\n    funcs[_key] = arguments[_key];\n  }\n\n  if (funcs.length === 0) {\n    return function (arg) {\n      return arg;\n    };\n  }\n\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n\n  return funcs.reduce(function (a, b) {\n    return function () {\n      return a(b.apply(void 0, arguments));\n    };\n  });\n}\n\n/**\n * Creates a store enhancer that applies middleware to the dispatch method\n * of the Redux store. This is handy for a variety of tasks, such as expressing\n * asynchronous actions in a concise manner, or logging every action payload.\n *\n * See `redux-thunk` package as an example of the Redux middleware.\n *\n * Because middleware is potentially asynchronous, this should be the first\n * store enhancer in the composition chain.\n *\n * Note that each middleware will be given the `dispatch` and `getState` functions\n * as named arguments.\n *\n * @param {...Function} middlewares The middleware chain to be applied.\n * @returns {Function} A store enhancer applying the middleware.\n */\n\nfunction applyMiddleware() {\n  for (var _len = arguments.length, middlewares = new Array(_len), _key = 0; _key < _len; _key++) {\n    middlewares[_key] = arguments[_key];\n  }\n\n  return function (createStore) {\n    return function () {\n      var store = createStore.apply(void 0, arguments);\n\n      var _dispatch = function dispatch() {\n        throw new Error( false ? 0 : 'Dispatching while constructing your middleware is not allowed. ' + 'Other middleware would not be applied to this dispatch.');\n      };\n\n      var middlewareAPI = {\n        getState: store.getState,\n        dispatch: function dispatch() {\n          return _dispatch.apply(void 0, arguments);\n        }\n      };\n      var chain = middlewares.map(function (middleware) {\n        return middleware(middlewareAPI);\n      });\n      _dispatch = compose.apply(void 0, chain)(store.dispatch);\n      return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, store), {}, {\n        dispatch: _dispatch\n      });\n    };\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/node_modules/redux/es/redux.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js":
/*!******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBeginDrag: () => (/* binding */ createBeginDrag)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n/* harmony import */ var _local_setClientOffset_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./local/setClientOffset.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\n\n\nconst ResetCoordinatesAction = {\n    type: _types_js__WEBPACK_IMPORTED_MODULE_1__.INIT_COORDS,\n    payload: {\n        clientOffset: null,\n        sourceClientOffset: null\n    }\n};\nfunction createBeginDrag(manager) {\n    return function beginDrag(sourceIds = [], options = {\n        publishSource: true\n    }) {\n        const { publishSource =true , clientOffset , getSourceClientOffset ,  } = options;\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        // Initialize the coordinates using the client offset\n        manager.dispatch((0,_local_setClientOffset_js__WEBPACK_IMPORTED_MODULE_2__.setClientOffset)(clientOffset));\n        verifyInvariants(sourceIds, monitor, registry);\n        // Get the draggable source\n        const sourceId = getDraggableSource(sourceIds, monitor);\n        if (sourceId == null) {\n            manager.dispatch(ResetCoordinatesAction);\n            return;\n        }\n        // Get the source client offset\n        let sourceClientOffset = null;\n        if (clientOffset) {\n            if (!getSourceClientOffset) {\n                throw new Error('getSourceClientOffset must be defined');\n            }\n            verifyGetSourceClientOffsetIsFunction(getSourceClientOffset);\n            sourceClientOffset = getSourceClientOffset(sourceId);\n        }\n        // Initialize the full coordinates\n        manager.dispatch((0,_local_setClientOffset_js__WEBPACK_IMPORTED_MODULE_2__.setClientOffset)(clientOffset, sourceClientOffset));\n        const source = registry.getSource(sourceId);\n        const item = source.beginDrag(monitor, sourceId);\n        // If source.beginDrag returns null, this is an indicator to cancel the drag\n        if (item == null) {\n            return undefined;\n        }\n        verifyItemIsObject(item);\n        registry.pinSource(sourceId);\n        const itemType = registry.getSourceType(sourceId);\n        return {\n            type: _types_js__WEBPACK_IMPORTED_MODULE_1__.BEGIN_DRAG,\n            payload: {\n                itemType,\n                item,\n                sourceId,\n                clientOffset: clientOffset || null,\n                sourceClientOffset: sourceClientOffset || null,\n                isSourcePublic: !!publishSource\n            }\n        };\n    };\n}\nfunction verifyInvariants(sourceIds, monitor, registry) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.isDragging(), 'Cannot call beginDrag while dragging.');\n    sourceIds.forEach(function(sourceId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(registry.getSource(sourceId), 'Expected sourceIds to be registered.');\n    });\n}\nfunction verifyGetSourceClientOffsetIsFunction(getSourceClientOffset) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof getSourceClientOffset === 'function', 'When clientOffset is provided, getSourceClientOffset must be a function.');\n}\nfunction verifyItemIsObject(item) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)((0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__.isObject)(item), 'Item must be an object.');\n}\nfunction getDraggableSource(sourceIds, monitor) {\n    let sourceId = null;\n    for(let i = sourceIds.length - 1; i >= 0; i--){\n        if (monitor.canDragSource(sourceIds[i])) {\n            sourceId = sourceIds[i];\n            break;\n        }\n    }\n    return sourceId;\n}\n\n//# sourceMappingURL=beginDrag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/drop.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/drop.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDrop: () => (/* binding */ createDrop)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\nfunction createDrop(manager) {\n    return function drop(options = {}) {\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        verifyInvariants(monitor);\n        const targetIds = getDroppableTargets(monitor);\n        // Multiple actions are dispatched here, which is why this doesn't return an action\n        targetIds.forEach((targetId, index)=>{\n            const dropResult = determineDropResult(targetId, index, registry, monitor);\n            const action = {\n                type: _types_js__WEBPACK_IMPORTED_MODULE_1__.DROP,\n                payload: {\n                    dropResult: _objectSpread({}, options, dropResult)\n                }\n            };\n            manager.dispatch(action);\n        });\n    };\n}\nfunction verifyInvariants(monitor) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), 'Cannot call drop while not dragging.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.didDrop(), 'Cannot call drop twice during one drag operation.');\n}\nfunction determineDropResult(targetId, index, registry, monitor) {\n    const target = registry.getTarget(targetId);\n    let dropResult = target ? target.drop(monitor, targetId) : undefined;\n    verifyDropResultType(dropResult);\n    if (typeof dropResult === 'undefined') {\n        dropResult = index === 0 ? {} : monitor.getDropResult();\n    }\n    return dropResult;\n}\nfunction verifyDropResultType(dropResult) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof dropResult === 'undefined' || (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(dropResult), 'Drop result must either be an object or undefined.');\n}\nfunction getDroppableTargets(monitor) {\n    const targetIds = monitor.getTargetIds().filter(monitor.canDropOnTarget, monitor);\n    targetIds.reverse();\n    return targetIds;\n}\n\n//# sourceMappingURL=drop.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/drop.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js":
/*!****************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEndDrag: () => (/* binding */ createEndDrag)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\nfunction createEndDrag(manager) {\n    return function endDrag() {\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        verifyIsDragging(monitor);\n        const sourceId = monitor.getSourceId();\n        if (sourceId != null) {\n            const source = registry.getSource(sourceId, true);\n            source.endDrag(monitor, sourceId);\n            registry.unpinSource();\n        }\n        return {\n            type: _types_js__WEBPACK_IMPORTED_MODULE_1__.END_DRAG\n        };\n    };\n}\nfunction verifyIsDragging(monitor) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), 'Cannot call endDrag while not dragging.');\n}\n\n//# sourceMappingURL=endDrag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2VuZERyYWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlEO0FBQ1g7QUFDL0I7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLCtDQUFRO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSwrREFBUztBQUNiOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGVudGFiZWxsLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvYWN0aW9ucy9kcmFnRHJvcC9lbmREcmFnLmpzPzM1MmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW52YXJpYW50IH0gZnJvbSAnQHJlYWN0LWRuZC9pbnZhcmlhbnQnO1xuaW1wb3J0IHsgRU5EX0RSQUcgfSBmcm9tICcuL3R5cGVzLmpzJztcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVFbmREcmFnKG1hbmFnZXIpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gZW5kRHJhZygpIHtcbiAgICAgICAgY29uc3QgbW9uaXRvciA9IG1hbmFnZXIuZ2V0TW9uaXRvcigpO1xuICAgICAgICBjb25zdCByZWdpc3RyeSA9IG1hbmFnZXIuZ2V0UmVnaXN0cnkoKTtcbiAgICAgICAgdmVyaWZ5SXNEcmFnZ2luZyhtb25pdG9yKTtcbiAgICAgICAgY29uc3Qgc291cmNlSWQgPSBtb25pdG9yLmdldFNvdXJjZUlkKCk7XG4gICAgICAgIGlmIChzb3VyY2VJZCAhPSBudWxsKSB7XG4gICAgICAgICAgICBjb25zdCBzb3VyY2UgPSByZWdpc3RyeS5nZXRTb3VyY2Uoc291cmNlSWQsIHRydWUpO1xuICAgICAgICAgICAgc291cmNlLmVuZERyYWcobW9uaXRvciwgc291cmNlSWQpO1xuICAgICAgICAgICAgcmVnaXN0cnkudW5waW5Tb3VyY2UoKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdHlwZTogRU5EX0RSQUdcbiAgICAgICAgfTtcbiAgICB9O1xufVxuZnVuY3Rpb24gdmVyaWZ5SXNEcmFnZ2luZyhtb25pdG9yKSB7XG4gICAgaW52YXJpYW50KG1vbml0b3IuaXNEcmFnZ2luZygpLCAnQ2Fubm90IGNhbGwgZW5kRHJhZyB3aGlsZSBub3QgZHJhZ2dpbmcuJyk7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWVuZERyYWcuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/hover.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/hover.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHover: () => (/* binding */ createHover)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/matchesType.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\n\nfunction createHover(manager) {\n    return function hover(targetIdsArg, { clientOffset  } = {}) {\n        verifyTargetIdsIsArray(targetIdsArg);\n        const targetIds = targetIdsArg.slice(0);\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        const draggedItemType = monitor.getItemType();\n        removeNonMatchingTargetIds(targetIds, registry, draggedItemType);\n        checkInvariants(targetIds, monitor, registry);\n        hoverAllTargets(targetIds, monitor, registry);\n        return {\n            type: _types_js__WEBPACK_IMPORTED_MODULE_1__.HOVER,\n            payload: {\n                targetIds,\n                clientOffset: clientOffset || null\n            }\n        };\n    };\n}\nfunction verifyTargetIdsIsArray(targetIdsArg) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(Array.isArray(targetIdsArg), 'Expected targetIds to be an array.');\n}\nfunction checkInvariants(targetIds, monitor, registry) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), 'Cannot call hover while not dragging.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.didDrop(), 'Cannot call hover after drop.');\n    for(let i = 0; i < targetIds.length; i++){\n        const targetId = targetIds[i];\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(targetIds.lastIndexOf(targetId) === i, 'Expected targetIds to be unique in the passed array.');\n        const target = registry.getTarget(targetId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(target, 'Expected targetIds to be registered.');\n    }\n}\nfunction removeNonMatchingTargetIds(targetIds, registry, draggedItemType) {\n    // Remove those targetIds that don't match the targetType.  This\n    // fixes shallow isOver which would only be non-shallow because of\n    // non-matching targets.\n    for(let i = targetIds.length - 1; i >= 0; i--){\n        const targetId = targetIds[i];\n        const targetType = registry.getTargetType(targetId);\n        if (!(0,_utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType)) {\n            targetIds.splice(i, 1);\n        }\n    }\n}\nfunction hoverAllTargets(targetIds, monitor, registry) {\n    // Finally call hover on all matching targets.\n    targetIds.forEach(function(targetId) {\n        const target = registry.getTarget(targetId);\n        target.hover(monitor, targetId);\n    });\n}\n\n//# sourceMappingURL=hover.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/hover.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BEGIN_DRAG: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG),\n/* harmony export */   DROP: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.DROP),\n/* harmony export */   END_DRAG: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.END_DRAG),\n/* harmony export */   HOVER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.HOVER),\n/* harmony export */   INIT_COORDS: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS),\n/* harmony export */   PUBLISH_DRAG_SOURCE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE),\n/* harmony export */   createDragDropActions: () => (/* binding */ createDragDropActions)\n/* harmony export */ });\n/* harmony import */ var _beginDrag_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./beginDrag.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js\");\n/* harmony import */ var _drop_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./drop.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/drop.js\");\n/* harmony import */ var _endDrag_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./endDrag.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js\");\n/* harmony import */ var _hover_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hover.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/hover.js\");\n/* harmony import */ var _publishDragSource_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./publishDragSource.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\n\n\n\n\nfunction createDragDropActions(manager) {\n    return {\n        beginDrag: (0,_beginDrag_js__WEBPACK_IMPORTED_MODULE_1__.createBeginDrag)(manager),\n        publishDragSource: (0,_publishDragSource_js__WEBPACK_IMPORTED_MODULE_2__.createPublishDragSource)(manager),\n        hover: (0,_hover_js__WEBPACK_IMPORTED_MODULE_3__.createHover)(manager),\n        drop: (0,_drop_js__WEBPACK_IMPORTED_MODULE_4__.createDrop)(manager),\n        endDrag: (0,_endDrag_js__WEBPACK_IMPORTED_MODULE_5__.createEndDrag)(manager)\n    };\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBaUQ7QUFDVjtBQUNNO0FBQ0o7QUFDd0I7QUFDdEM7QUFDcEI7QUFDUDtBQUNBLG1CQUFtQiw4REFBZTtBQUNsQywyQkFBMkIsOEVBQXVCO0FBQ2xELGVBQWUsc0RBQVc7QUFDMUIsY0FBYyxvREFBVTtBQUN4QixpQkFBaUIsMERBQWE7QUFDOUI7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L2FjdGlvbnMvZHJhZ0Ryb3AvaW5kZXguanM/MDg5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVCZWdpbkRyYWcgfSBmcm9tICcuL2JlZ2luRHJhZy5qcyc7XG5pbXBvcnQgeyBjcmVhdGVEcm9wIH0gZnJvbSAnLi9kcm9wLmpzJztcbmltcG9ydCB7IGNyZWF0ZUVuZERyYWcgfSBmcm9tICcuL2VuZERyYWcuanMnO1xuaW1wb3J0IHsgY3JlYXRlSG92ZXIgfSBmcm9tICcuL2hvdmVyLmpzJztcbmltcG9ydCB7IGNyZWF0ZVB1Ymxpc2hEcmFnU291cmNlIH0gZnJvbSAnLi9wdWJsaXNoRHJhZ1NvdXJjZS5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3R5cGVzLmpzJztcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVEcmFnRHJvcEFjdGlvbnMobWFuYWdlcikge1xuICAgIHJldHVybiB7XG4gICAgICAgIGJlZ2luRHJhZzogY3JlYXRlQmVnaW5EcmFnKG1hbmFnZXIpLFxuICAgICAgICBwdWJsaXNoRHJhZ1NvdXJjZTogY3JlYXRlUHVibGlzaERyYWdTb3VyY2UobWFuYWdlciksXG4gICAgICAgIGhvdmVyOiBjcmVhdGVIb3ZlcihtYW5hZ2VyKSxcbiAgICAgICAgZHJvcDogY3JlYXRlRHJvcChtYW5hZ2VyKSxcbiAgICAgICAgZW5kRHJhZzogY3JlYXRlRW5kRHJhZyhtYW5hZ2VyKVxuICAgIH07XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js":
/*!******************************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setClientOffset: () => (/* binding */ setClientOffset)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\nfunction setClientOffset(clientOffset, sourceClientOffset) {\n    return {\n        type: _types_js__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS,\n        payload: {\n            sourceClientOffset: sourceClientOffset || null,\n            clientOffset: clientOffset || null\n        }\n    };\n}\n\n//# sourceMappingURL=setClientOffset.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2xvY2FsL3NldENsaWVudE9mZnNldC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQztBQUNuQztBQUNQO0FBQ0EsY0FBYyxrREFBVztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2xvY2FsL3NldENsaWVudE9mZnNldC5qcz84ZGUwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IElOSVRfQ09PUkRTIH0gZnJvbSAnLi4vdHlwZXMuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIHNldENsaWVudE9mZnNldChjbGllbnRPZmZzZXQsIHNvdXJjZUNsaWVudE9mZnNldCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IElOSVRfQ09PUkRTLFxuICAgICAgICBwYXlsb2FkOiB7XG4gICAgICAgICAgICBzb3VyY2VDbGllbnRPZmZzZXQ6IHNvdXJjZUNsaWVudE9mZnNldCB8fCBudWxsLFxuICAgICAgICAgICAgY2xpZW50T2Zmc2V0OiBjbGllbnRPZmZzZXQgfHwgbnVsbFxuICAgICAgICB9XG4gICAgfTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2V0Q2xpZW50T2Zmc2V0LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js":
/*!**************************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPublishDragSource: () => (/* binding */ createPublishDragSource)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\nfunction createPublishDragSource(manager) {\n    return function publishDragSource() {\n        const monitor = manager.getMonitor();\n        if (monitor.isDragging()) {\n            return {\n                type: _types_js__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE\n            };\n        }\n        return;\n    };\n}\n\n//# sourceMappingURL=publishDragSource.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL3B1Ymxpc2hEcmFnU291cmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlEO0FBQzFDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsMERBQW1CO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL3B1Ymxpc2hEcmFnU291cmNlLmpzP2RjNDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUFVCTElTSF9EUkFHX1NPVVJDRSB9IGZyb20gJy4vdHlwZXMuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZVB1Ymxpc2hEcmFnU291cmNlKG1hbmFnZXIpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gcHVibGlzaERyYWdTb3VyY2UoKSB7XG4gICAgICAgIGNvbnN0IG1vbml0b3IgPSBtYW5hZ2VyLmdldE1vbml0b3IoKTtcbiAgICAgICAgaWYgKG1vbml0b3IuaXNEcmFnZ2luZygpKSB7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHR5cGU6IFBVQkxJU0hfRFJBR19TT1VSQ0VcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuO1xuICAgIH07XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXB1Ymxpc2hEcmFnU291cmNlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/types.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BEGIN_DRAG: () => (/* binding */ BEGIN_DRAG),\n/* harmony export */   DROP: () => (/* binding */ DROP),\n/* harmony export */   END_DRAG: () => (/* binding */ END_DRAG),\n/* harmony export */   HOVER: () => (/* binding */ HOVER),\n/* harmony export */   INIT_COORDS: () => (/* binding */ INIT_COORDS),\n/* harmony export */   PUBLISH_DRAG_SOURCE: () => (/* binding */ PUBLISH_DRAG_SOURCE)\n/* harmony export */ });\nconst INIT_COORDS = 'dnd-core/INIT_COORDS';\nconst BEGIN_DRAG = 'dnd-core/BEGIN_DRAG';\nconst PUBLISH_DRAG_SOURCE = 'dnd-core/PUBLISH_DRAG_SOURCE';\nconst HOVER = 'dnd-core/HOVER';\nconst DROP = 'dnd-core/DROP';\nconst END_DRAG = 'dnd-core/END_DRAG';\n\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFPO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFUCIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L2FjdGlvbnMvZHJhZ0Ryb3AvdHlwZXMuanM/OTgzOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgSU5JVF9DT09SRFMgPSAnZG5kLWNvcmUvSU5JVF9DT09SRFMnO1xuZXhwb3J0IGNvbnN0IEJFR0lOX0RSQUcgPSAnZG5kLWNvcmUvQkVHSU5fRFJBRyc7XG5leHBvcnQgY29uc3QgUFVCTElTSF9EUkFHX1NPVVJDRSA9ICdkbmQtY29yZS9QVUJMSVNIX0RSQUdfU09VUkNFJztcbmV4cG9ydCBjb25zdCBIT1ZFUiA9ICdkbmQtY29yZS9IT1ZFUic7XG5leHBvcnQgY29uc3QgRFJPUCA9ICdkbmQtY29yZS9EUk9QJztcbmV4cG9ydCBjb25zdCBFTkRfRFJBRyA9ICdkbmQtY29yZS9FTkRfRFJBRyc7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/registry.js":
/*!********************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/registry.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADD_SOURCE: () => (/* binding */ ADD_SOURCE),\n/* harmony export */   ADD_TARGET: () => (/* binding */ ADD_TARGET),\n/* harmony export */   REMOVE_SOURCE: () => (/* binding */ REMOVE_SOURCE),\n/* harmony export */   REMOVE_TARGET: () => (/* binding */ REMOVE_TARGET),\n/* harmony export */   addSource: () => (/* binding */ addSource),\n/* harmony export */   addTarget: () => (/* binding */ addTarget),\n/* harmony export */   removeSource: () => (/* binding */ removeSource),\n/* harmony export */   removeTarget: () => (/* binding */ removeTarget)\n/* harmony export */ });\nconst ADD_SOURCE = 'dnd-core/ADD_SOURCE';\nconst ADD_TARGET = 'dnd-core/ADD_TARGET';\nconst REMOVE_SOURCE = 'dnd-core/REMOVE_SOURCE';\nconst REMOVE_TARGET = 'dnd-core/REMOVE_TARGET';\nfunction addSource(sourceId) {\n    return {\n        type: ADD_SOURCE,\n        payload: {\n            sourceId\n        }\n    };\n}\nfunction addTarget(targetId) {\n    return {\n        type: ADD_TARGET,\n        payload: {\n            targetId\n        }\n    };\n}\nfunction removeSource(sourceId) {\n    return {\n        type: REMOVE_SOURCE,\n        payload: {\n            sourceId\n        }\n    };\n}\nfunction removeTarget(targetId) {\n    return {\n        type: REMOVE_TARGET,\n        payload: {\n            targetId\n        }\n    };\n}\n\n//# sourceMappingURL=registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL3JlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGVudGFiZWxsLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvYWN0aW9ucy9yZWdpc3RyeS5qcz8yMzM1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBBRERfU09VUkNFID0gJ2RuZC1jb3JlL0FERF9TT1VSQ0UnO1xuZXhwb3J0IGNvbnN0IEFERF9UQVJHRVQgPSAnZG5kLWNvcmUvQUREX1RBUkdFVCc7XG5leHBvcnQgY29uc3QgUkVNT1ZFX1NPVVJDRSA9ICdkbmQtY29yZS9SRU1PVkVfU09VUkNFJztcbmV4cG9ydCBjb25zdCBSRU1PVkVfVEFSR0VUID0gJ2RuZC1jb3JlL1JFTU9WRV9UQVJHRVQnO1xuZXhwb3J0IGZ1bmN0aW9uIGFkZFNvdXJjZShzb3VyY2VJZCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IEFERF9TT1VSQ0UsXG4gICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgIHNvdXJjZUlkXG4gICAgICAgIH1cbiAgICB9O1xufVxuZXhwb3J0IGZ1bmN0aW9uIGFkZFRhcmdldCh0YXJnZXRJZCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IEFERF9UQVJHRVQsXG4gICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgIHRhcmdldElkXG4gICAgICAgIH1cbiAgICB9O1xufVxuZXhwb3J0IGZ1bmN0aW9uIHJlbW92ZVNvdXJjZShzb3VyY2VJZCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IFJFTU9WRV9TT1VSQ0UsXG4gICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgIHNvdXJjZUlkXG4gICAgICAgIH1cbiAgICB9O1xufVxuZXhwb3J0IGZ1bmN0aW9uIHJlbW92ZVRhcmdldCh0YXJnZXRJZCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IFJFTU9WRV9UQVJHRVQsXG4gICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgIHRhcmdldElkXG4gICAgICAgIH1cbiAgICB9O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWdpc3RyeS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js":
/*!*******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragDropManagerImpl: () => (/* binding */ DragDropManagerImpl)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/index.js\");\n\nclass DragDropManagerImpl {\n    receiveBackend(backend) {\n        this.backend = backend;\n    }\n    getMonitor() {\n        return this.monitor;\n    }\n    getBackend() {\n        return this.backend;\n    }\n    getRegistry() {\n        return this.monitor.registry;\n    }\n    getActions() {\n        /* eslint-disable-next-line @typescript-eslint/no-this-alias */ const manager = this;\n        const { dispatch  } = this.store;\n        function bindActionCreator(actionCreator) {\n            return (...args)=>{\n                const action = actionCreator.apply(manager, args);\n                if (typeof action !== 'undefined') {\n                    dispatch(action);\n                }\n            };\n        }\n        const actions = (0,_actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.createDragDropActions)(this);\n        return Object.keys(actions).reduce((boundActions, key)=>{\n            const action = actions[key];\n            boundActions[key] = bindActionCreator(action);\n            return boundActions;\n        }, {});\n    }\n    dispatch(action) {\n        this.store.dispatch(action);\n    }\n    constructor(store, monitor){\n        this.isSetUp = false;\n        this.handleRefCountChange = ()=>{\n            const shouldSetUp = this.store.getState().refCount > 0;\n            if (this.backend) {\n                if (shouldSetUp && !this.isSetUp) {\n                    this.backend.setup();\n                    this.isSetUp = true;\n                } else if (!shouldSetUp && this.isSetUp) {\n                    this.backend.teardown();\n                    this.isSetUp = false;\n                }\n            }\n        };\n        this.store = store;\n        this.monitor = monitor;\n        store.subscribe(this.handleRefCountChange);\n    }\n}\n\n//# sourceMappingURL=DragDropManagerImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js":
/*!*******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragDropMonitorImpl: () => (/* binding */ DragDropMonitorImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_coords_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/coords.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/coords.js\");\n/* harmony import */ var _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/dirtiness.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js\");\n/* harmony import */ var _utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/matchesType.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js\");\n\n\n\n\nclass DragDropMonitorImpl {\n    subscribeToStateChange(listener, options = {}) {\n        const { handlerIds  } = options;\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof listener === 'function', 'listener must be a function.');\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof handlerIds === 'undefined' || Array.isArray(handlerIds), 'handlerIds, when specified, must be an array of strings.');\n        let prevStateId = this.store.getState().stateId;\n        const handleChange = ()=>{\n            const state = this.store.getState();\n            const currentStateId = state.stateId;\n            try {\n                const canSkipListener = currentStateId === prevStateId || currentStateId === prevStateId + 1 && !(0,_utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_1__.areDirty)(state.dirtyHandlerIds, handlerIds);\n                if (!canSkipListener) {\n                    listener();\n                }\n            } finally{\n                prevStateId = currentStateId;\n            }\n        };\n        return this.store.subscribe(handleChange);\n    }\n    subscribeToOffsetChange(listener) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof listener === 'function', 'listener must be a function.');\n        let previousState = this.store.getState().dragOffset;\n        const handleChange = ()=>{\n            const nextState = this.store.getState().dragOffset;\n            if (nextState === previousState) {\n                return;\n            }\n            previousState = nextState;\n            listener();\n        };\n        return this.store.subscribe(handleChange);\n    }\n    canDragSource(sourceId) {\n        if (!sourceId) {\n            return false;\n        }\n        const source = this.registry.getSource(sourceId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(source, `Expected to find a valid source. sourceId=${sourceId}`);\n        if (this.isDragging()) {\n            return false;\n        }\n        return source.canDrag(this, sourceId);\n    }\n    canDropOnTarget(targetId) {\n        // undefined on initial render\n        if (!targetId) {\n            return false;\n        }\n        const target = this.registry.getTarget(targetId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(target, `Expected to find a valid target. targetId=${targetId}`);\n        if (!this.isDragging() || this.didDrop()) {\n            return false;\n        }\n        const targetType = this.registry.getTargetType(targetId);\n        const draggedItemType = this.getItemType();\n        return (0,_utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType) && target.canDrop(this, targetId);\n    }\n    isDragging() {\n        return Boolean(this.getItemType());\n    }\n    isDraggingSource(sourceId) {\n        // undefined on initial render\n        if (!sourceId) {\n            return false;\n        }\n        const source = this.registry.getSource(sourceId, true);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(source, `Expected to find a valid source. sourceId=${sourceId}`);\n        if (!this.isDragging() || !this.isSourcePublic()) {\n            return false;\n        }\n        const sourceType = this.registry.getSourceType(sourceId);\n        const draggedItemType = this.getItemType();\n        if (sourceType !== draggedItemType) {\n            return false;\n        }\n        return source.isDragging(this, sourceId);\n    }\n    isOverTarget(targetId, options = {\n        shallow: false\n    }) {\n        // undefined on initial render\n        if (!targetId) {\n            return false;\n        }\n        const { shallow  } = options;\n        if (!this.isDragging()) {\n            return false;\n        }\n        const targetType = this.registry.getTargetType(targetId);\n        const draggedItemType = this.getItemType();\n        if (draggedItemType && !(0,_utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType)) {\n            return false;\n        }\n        const targetIds = this.getTargetIds();\n        if (!targetIds.length) {\n            return false;\n        }\n        const index = targetIds.indexOf(targetId);\n        if (shallow) {\n            return index === targetIds.length - 1;\n        } else {\n            return index > -1;\n        }\n    }\n    getItemType() {\n        return this.store.getState().dragOperation.itemType;\n    }\n    getItem() {\n        return this.store.getState().dragOperation.item;\n    }\n    getSourceId() {\n        return this.store.getState().dragOperation.sourceId;\n    }\n    getTargetIds() {\n        return this.store.getState().dragOperation.targetIds;\n    }\n    getDropResult() {\n        return this.store.getState().dragOperation.dropResult;\n    }\n    didDrop() {\n        return this.store.getState().dragOperation.didDrop;\n    }\n    isSourcePublic() {\n        return Boolean(this.store.getState().dragOperation.isSourcePublic);\n    }\n    getInitialClientOffset() {\n        return this.store.getState().dragOffset.initialClientOffset;\n    }\n    getInitialSourceClientOffset() {\n        return this.store.getState().dragOffset.initialSourceClientOffset;\n    }\n    getClientOffset() {\n        return this.store.getState().dragOffset.clientOffset;\n    }\n    getSourceClientOffset() {\n        return (0,_utils_coords_js__WEBPACK_IMPORTED_MODULE_3__.getSourceClientOffset)(this.store.getState().dragOffset);\n    }\n    getDifferenceFromInitialOffset() {\n        return (0,_utils_coords_js__WEBPACK_IMPORTED_MODULE_3__.getDifferenceFromInitialOffset)(this.store.getState().dragOffset);\n    }\n    constructor(store, registry){\n        this.store = store;\n        this.registry = registry;\n    }\n}\n\n//# sourceMappingURL=DragDropMonitorImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9jbGFzc2VzL0RyYWdEcm9wTW9uaXRvckltcGwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBaUQ7QUFDMEM7QUFDMUM7QUFDSztBQUMvQztBQUNQLGlEQUFpRDtBQUNqRCxnQkFBZ0IsY0FBYztBQUM5QixRQUFRLCtEQUFTO0FBQ2pCLFFBQVEsK0RBQVM7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlIQUFpSCw2REFBUTtBQUN6SDtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSwrREFBUztBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsK0RBQVMsc0RBQXNELFNBQVM7QUFDaEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsK0RBQVMsc0RBQXNELFNBQVM7QUFDaEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsa0VBQVc7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLCtEQUFTLHNEQUFzRCxTQUFTO0FBQ2hGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixXQUFXO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0Msa0VBQVc7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSx1RUFBcUI7QUFDcEM7QUFDQTtBQUNBLGVBQWUsZ0ZBQThCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L2NsYXNzZXMvRHJhZ0Ryb3BNb25pdG9ySW1wbC5qcz8wY2QwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGludmFyaWFudCB9IGZyb20gJ0ByZWFjdC1kbmQvaW52YXJpYW50JztcbmltcG9ydCB7IGdldERpZmZlcmVuY2VGcm9tSW5pdGlhbE9mZnNldCwgZ2V0U291cmNlQ2xpZW50T2Zmc2V0IH0gZnJvbSAnLi4vdXRpbHMvY29vcmRzLmpzJztcbmltcG9ydCB7IGFyZURpcnR5IH0gZnJvbSAnLi4vdXRpbHMvZGlydGluZXNzLmpzJztcbmltcG9ydCB7IG1hdGNoZXNUeXBlIH0gZnJvbSAnLi4vdXRpbHMvbWF0Y2hlc1R5cGUuanMnO1xuZXhwb3J0IGNsYXNzIERyYWdEcm9wTW9uaXRvckltcGwge1xuICAgIHN1YnNjcmliZVRvU3RhdGVDaGFuZ2UobGlzdGVuZXIsIG9wdGlvbnMgPSB7fSkge1xuICAgICAgICBjb25zdCB7IGhhbmRsZXJJZHMgIH0gPSBvcHRpb25zO1xuICAgICAgICBpbnZhcmlhbnQodHlwZW9mIGxpc3RlbmVyID09PSAnZnVuY3Rpb24nLCAnbGlzdGVuZXIgbXVzdCBiZSBhIGZ1bmN0aW9uLicpO1xuICAgICAgICBpbnZhcmlhbnQodHlwZW9mIGhhbmRsZXJJZHMgPT09ICd1bmRlZmluZWQnIHx8IEFycmF5LmlzQXJyYXkoaGFuZGxlcklkcyksICdoYW5kbGVySWRzLCB3aGVuIHNwZWNpZmllZCwgbXVzdCBiZSBhbiBhcnJheSBvZiBzdHJpbmdzLicpO1xuICAgICAgICBsZXQgcHJldlN0YXRlSWQgPSB0aGlzLnN0b3JlLmdldFN0YXRlKCkuc3RhdGVJZDtcbiAgICAgICAgY29uc3QgaGFuZGxlQ2hhbmdlID0gKCk9PntcbiAgICAgICAgICAgIGNvbnN0IHN0YXRlID0gdGhpcy5zdG9yZS5nZXRTdGF0ZSgpO1xuICAgICAgICAgICAgY29uc3QgY3VycmVudFN0YXRlSWQgPSBzdGF0ZS5zdGF0ZUlkO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBjYW5Ta2lwTGlzdGVuZXIgPSBjdXJyZW50U3RhdGVJZCA9PT0gcHJldlN0YXRlSWQgfHwgY3VycmVudFN0YXRlSWQgPT09IHByZXZTdGF0ZUlkICsgMSAmJiAhYXJlRGlydHkoc3RhdGUuZGlydHlIYW5kbGVySWRzLCBoYW5kbGVySWRzKTtcbiAgICAgICAgICAgICAgICBpZiAoIWNhblNraXBMaXN0ZW5lcikge1xuICAgICAgICAgICAgICAgICAgICBsaXN0ZW5lcigpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZmluYWxseXtcbiAgICAgICAgICAgICAgICBwcmV2U3RhdGVJZCA9IGN1cnJlbnRTdGF0ZUlkO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5zdG9yZS5zdWJzY3JpYmUoaGFuZGxlQ2hhbmdlKTtcbiAgICB9XG4gICAgc3Vic2NyaWJlVG9PZmZzZXRDaGFuZ2UobGlzdGVuZXIpIHtcbiAgICAgICAgaW52YXJpYW50KHR5cGVvZiBsaXN0ZW5lciA9PT0gJ2Z1bmN0aW9uJywgJ2xpc3RlbmVyIG11c3QgYmUgYSBmdW5jdGlvbi4nKTtcbiAgICAgICAgbGV0IHByZXZpb3VzU3RhdGUgPSB0aGlzLnN0b3JlLmdldFN0YXRlKCkuZHJhZ09mZnNldDtcbiAgICAgICAgY29uc3QgaGFuZGxlQ2hhbmdlID0gKCk9PntcbiAgICAgICAgICAgIGNvbnN0IG5leHRTdGF0ZSA9IHRoaXMuc3RvcmUuZ2V0U3RhdGUoKS5kcmFnT2Zmc2V0O1xuICAgICAgICAgICAgaWYgKG5leHRTdGF0ZSA9PT0gcHJldmlvdXNTdGF0ZSkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHByZXZpb3VzU3RhdGUgPSBuZXh0U3RhdGU7XG4gICAgICAgICAgICBsaXN0ZW5lcigpO1xuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5zdG9yZS5zdWJzY3JpYmUoaGFuZGxlQ2hhbmdlKTtcbiAgICB9XG4gICAgY2FuRHJhZ1NvdXJjZShzb3VyY2VJZCkge1xuICAgICAgICBpZiAoIXNvdXJjZUlkKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3Qgc291cmNlID0gdGhpcy5yZWdpc3RyeS5nZXRTb3VyY2Uoc291cmNlSWQpO1xuICAgICAgICBpbnZhcmlhbnQoc291cmNlLCBgRXhwZWN0ZWQgdG8gZmluZCBhIHZhbGlkIHNvdXJjZS4gc291cmNlSWQ9JHtzb3VyY2VJZH1gKTtcbiAgICAgICAgaWYgKHRoaXMuaXNEcmFnZ2luZygpKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHNvdXJjZS5jYW5EcmFnKHRoaXMsIHNvdXJjZUlkKTtcbiAgICB9XG4gICAgY2FuRHJvcE9uVGFyZ2V0KHRhcmdldElkKSB7XG4gICAgICAgIC8vIHVuZGVmaW5lZCBvbiBpbml0aWFsIHJlbmRlclxuICAgICAgICBpZiAoIXRhcmdldElkKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdGFyZ2V0ID0gdGhpcy5yZWdpc3RyeS5nZXRUYXJnZXQodGFyZ2V0SWQpO1xuICAgICAgICBpbnZhcmlhbnQodGFyZ2V0LCBgRXhwZWN0ZWQgdG8gZmluZCBhIHZhbGlkIHRhcmdldC4gdGFyZ2V0SWQ9JHt0YXJnZXRJZH1gKTtcbiAgICAgICAgaWYgKCF0aGlzLmlzRHJhZ2dpbmcoKSB8fCB0aGlzLmRpZERyb3AoKSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHRhcmdldFR5cGUgPSB0aGlzLnJlZ2lzdHJ5LmdldFRhcmdldFR5cGUodGFyZ2V0SWQpO1xuICAgICAgICBjb25zdCBkcmFnZ2VkSXRlbVR5cGUgPSB0aGlzLmdldEl0ZW1UeXBlKCk7XG4gICAgICAgIHJldHVybiBtYXRjaGVzVHlwZSh0YXJnZXRUeXBlLCBkcmFnZ2VkSXRlbVR5cGUpICYmIHRhcmdldC5jYW5Ecm9wKHRoaXMsIHRhcmdldElkKTtcbiAgICB9XG4gICAgaXNEcmFnZ2luZygpIHtcbiAgICAgICAgcmV0dXJuIEJvb2xlYW4odGhpcy5nZXRJdGVtVHlwZSgpKTtcbiAgICB9XG4gICAgaXNEcmFnZ2luZ1NvdXJjZShzb3VyY2VJZCkge1xuICAgICAgICAvLyB1bmRlZmluZWQgb24gaW5pdGlhbCByZW5kZXJcbiAgICAgICAgaWYgKCFzb3VyY2VJZCkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHNvdXJjZSA9IHRoaXMucmVnaXN0cnkuZ2V0U291cmNlKHNvdXJjZUlkLCB0cnVlKTtcbiAgICAgICAgaW52YXJpYW50KHNvdXJjZSwgYEV4cGVjdGVkIHRvIGZpbmQgYSB2YWxpZCBzb3VyY2UuIHNvdXJjZUlkPSR7c291cmNlSWR9YCk7XG4gICAgICAgIGlmICghdGhpcy5pc0RyYWdnaW5nKCkgfHwgIXRoaXMuaXNTb3VyY2VQdWJsaWMoKSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHNvdXJjZVR5cGUgPSB0aGlzLnJlZ2lzdHJ5LmdldFNvdXJjZVR5cGUoc291cmNlSWQpO1xuICAgICAgICBjb25zdCBkcmFnZ2VkSXRlbVR5cGUgPSB0aGlzLmdldEl0ZW1UeXBlKCk7XG4gICAgICAgIGlmIChzb3VyY2VUeXBlICE9PSBkcmFnZ2VkSXRlbVR5cGUpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gc291cmNlLmlzRHJhZ2dpbmcodGhpcywgc291cmNlSWQpO1xuICAgIH1cbiAgICBpc092ZXJUYXJnZXQodGFyZ2V0SWQsIG9wdGlvbnMgPSB7XG4gICAgICAgIHNoYWxsb3c6IGZhbHNlXG4gICAgfSkge1xuICAgICAgICAvLyB1bmRlZmluZWQgb24gaW5pdGlhbCByZW5kZXJcbiAgICAgICAgaWYgKCF0YXJnZXRJZCkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHsgc2hhbGxvdyAgfSA9IG9wdGlvbnM7XG4gICAgICAgIGlmICghdGhpcy5pc0RyYWdnaW5nKCkpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB0YXJnZXRUeXBlID0gdGhpcy5yZWdpc3RyeS5nZXRUYXJnZXRUeXBlKHRhcmdldElkKTtcbiAgICAgICAgY29uc3QgZHJhZ2dlZEl0ZW1UeXBlID0gdGhpcy5nZXRJdGVtVHlwZSgpO1xuICAgICAgICBpZiAoZHJhZ2dlZEl0ZW1UeXBlICYmICFtYXRjaGVzVHlwZSh0YXJnZXRUeXBlLCBkcmFnZ2VkSXRlbVR5cGUpKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdGFyZ2V0SWRzID0gdGhpcy5nZXRUYXJnZXRJZHMoKTtcbiAgICAgICAgaWYgKCF0YXJnZXRJZHMubGVuZ3RoKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgaW5kZXggPSB0YXJnZXRJZHMuaW5kZXhPZih0YXJnZXRJZCk7XG4gICAgICAgIGlmIChzaGFsbG93KSB7XG4gICAgICAgICAgICByZXR1cm4gaW5kZXggPT09IHRhcmdldElkcy5sZW5ndGggLSAxO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIGluZGV4ID4gLTE7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZ2V0SXRlbVR5cGUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0b3JlLmdldFN0YXRlKCkuZHJhZ09wZXJhdGlvbi5pdGVtVHlwZTtcbiAgICB9XG4gICAgZ2V0SXRlbSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RvcmUuZ2V0U3RhdGUoKS5kcmFnT3BlcmF0aW9uLml0ZW07XG4gICAgfVxuICAgIGdldFNvdXJjZUlkKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5zdG9yZS5nZXRTdGF0ZSgpLmRyYWdPcGVyYXRpb24uc291cmNlSWQ7XG4gICAgfVxuICAgIGdldFRhcmdldElkcygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RvcmUuZ2V0U3RhdGUoKS5kcmFnT3BlcmF0aW9uLnRhcmdldElkcztcbiAgICB9XG4gICAgZ2V0RHJvcFJlc3VsdCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RvcmUuZ2V0U3RhdGUoKS5kcmFnT3BlcmF0aW9uLmRyb3BSZXN1bHQ7XG4gICAgfVxuICAgIGRpZERyb3AoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0b3JlLmdldFN0YXRlKCkuZHJhZ09wZXJhdGlvbi5kaWREcm9wO1xuICAgIH1cbiAgICBpc1NvdXJjZVB1YmxpYygpIHtcbiAgICAgICAgcmV0dXJuIEJvb2xlYW4odGhpcy5zdG9yZS5nZXRTdGF0ZSgpLmRyYWdPcGVyYXRpb24uaXNTb3VyY2VQdWJsaWMpO1xuICAgIH1cbiAgICBnZXRJbml0aWFsQ2xpZW50T2Zmc2V0KCkge1xuICAgICAgICByZXR1cm4gdGhpcy5zdG9yZS5nZXRTdGF0ZSgpLmRyYWdPZmZzZXQuaW5pdGlhbENsaWVudE9mZnNldDtcbiAgICB9XG4gICAgZ2V0SW5pdGlhbFNvdXJjZUNsaWVudE9mZnNldCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RvcmUuZ2V0U3RhdGUoKS5kcmFnT2Zmc2V0LmluaXRpYWxTb3VyY2VDbGllbnRPZmZzZXQ7XG4gICAgfVxuICAgIGdldENsaWVudE9mZnNldCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RvcmUuZ2V0U3RhdGUoKS5kcmFnT2Zmc2V0LmNsaWVudE9mZnNldDtcbiAgICB9XG4gICAgZ2V0U291cmNlQ2xpZW50T2Zmc2V0KCkge1xuICAgICAgICByZXR1cm4gZ2V0U291cmNlQ2xpZW50T2Zmc2V0KHRoaXMuc3RvcmUuZ2V0U3RhdGUoKS5kcmFnT2Zmc2V0KTtcbiAgICB9XG4gICAgZ2V0RGlmZmVyZW5jZUZyb21Jbml0aWFsT2Zmc2V0KCkge1xuICAgICAgICByZXR1cm4gZ2V0RGlmZmVyZW5jZUZyb21Jbml0aWFsT2Zmc2V0KHRoaXMuc3RvcmUuZ2V0U3RhdGUoKS5kcmFnT2Zmc2V0KTtcbiAgICB9XG4gICAgY29uc3RydWN0b3Ioc3RvcmUsIHJlZ2lzdHJ5KXtcbiAgICAgICAgdGhpcy5zdG9yZSA9IHN0b3JlO1xuICAgICAgICB0aGlzLnJlZ2lzdHJ5ID0gcmVnaXN0cnk7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1EcmFnRHJvcE1vbml0b3JJbXBsLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js":
/*!*******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandlerRegistryImpl: () => (/* binding */ HandlerRegistryImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_asap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/asap */ \"(ssr)/./node_modules/@react-dnd/asap/dist/index.js\");\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n/* harmony import */ var _contracts_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contracts.js */ \"(ssr)/./node_modules/dnd-core/dist/contracts.js\");\n/* harmony import */ var _interfaces_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../interfaces.js */ \"(ssr)/./node_modules/dnd-core/dist/interfaces.js\");\n/* harmony import */ var _utils_getNextUniqueId_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/getNextUniqueId.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/getNextUniqueId.js\");\n\n\n\n\n\n\nfunction getNextHandlerId(role) {\n    const id = (0,_utils_getNextUniqueId_js__WEBPACK_IMPORTED_MODULE_2__.getNextUniqueId)().toString();\n    switch(role){\n        case _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE:\n            return `S${id}`;\n        case _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET:\n            return `T${id}`;\n        default:\n            throw new Error(`Unknown Handler Role: ${role}`);\n    }\n}\nfunction parseRoleFromHandlerId(handlerId) {\n    switch(handlerId[0]){\n        case 'S':\n            return _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE;\n        case 'T':\n            return _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET;\n        default:\n            throw new Error(`Cannot parse handler ID: ${handlerId}`);\n    }\n}\nfunction mapContainsValue(map, searchValue) {\n    const entries = map.entries();\n    let isDone = false;\n    do {\n        const { done , value: [, value] ,  } = entries.next();\n        if (value === searchValue) {\n            return true;\n        }\n        isDone = !!done;\n    }while (!isDone)\n    return false;\n}\nclass HandlerRegistryImpl {\n    addSource(type, source) {\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateType)(type);\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateSourceContract)(source);\n        const sourceId = this.addHandler(_interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE, type, source);\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.addSource)(sourceId));\n        return sourceId;\n    }\n    addTarget(type, target) {\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateType)(type, true);\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateTargetContract)(target);\n        const targetId = this.addHandler(_interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET, type, target);\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.addTarget)(targetId));\n        return targetId;\n    }\n    containsHandler(handler) {\n        return mapContainsValue(this.dragSources, handler) || mapContainsValue(this.dropTargets, handler);\n    }\n    getSource(sourceId, includePinned = false) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isSourceId(sourceId), 'Expected a valid source ID.');\n        const isPinned = includePinned && sourceId === this.pinnedSourceId;\n        const source = isPinned ? this.pinnedSource : this.dragSources.get(sourceId);\n        return source;\n    }\n    getTarget(targetId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isTargetId(targetId), 'Expected a valid target ID.');\n        return this.dropTargets.get(targetId);\n    }\n    getSourceType(sourceId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isSourceId(sourceId), 'Expected a valid source ID.');\n        return this.types.get(sourceId);\n    }\n    getTargetType(targetId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isTargetId(targetId), 'Expected a valid target ID.');\n        return this.types.get(targetId);\n    }\n    isSourceId(handlerId) {\n        const role = parseRoleFromHandlerId(handlerId);\n        return role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE;\n    }\n    isTargetId(handlerId) {\n        const role = parseRoleFromHandlerId(handlerId);\n        return role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET;\n    }\n    removeSource(sourceId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.getSource(sourceId), 'Expected an existing source.');\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.removeSource)(sourceId));\n        (0,_react_dnd_asap__WEBPACK_IMPORTED_MODULE_0__.asap)(()=>{\n            this.dragSources.delete(sourceId);\n            this.types.delete(sourceId);\n        });\n    }\n    removeTarget(targetId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.getTarget(targetId), 'Expected an existing target.');\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.removeTarget)(targetId));\n        this.dropTargets.delete(targetId);\n        this.types.delete(targetId);\n    }\n    pinSource(sourceId) {\n        const source = this.getSource(sourceId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(source, 'Expected an existing source.');\n        this.pinnedSourceId = sourceId;\n        this.pinnedSource = source;\n    }\n    unpinSource() {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.pinnedSource, 'No source is pinned at the time.');\n        this.pinnedSourceId = null;\n        this.pinnedSource = null;\n    }\n    addHandler(role, type, handler) {\n        const id = getNextHandlerId(role);\n        this.types.set(id, type);\n        if (role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE) {\n            this.dragSources.set(id, handler);\n        } else if (role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET) {\n            this.dropTargets.set(id, handler);\n        }\n        return id;\n    }\n    constructor(store){\n        this.types = new Map();\n        this.dragSources = new Map();\n        this.dropTargets = new Map();\n        this.pinnedSourceId = null;\n        this.pinnedSource = null;\n        this.store = store;\n    }\n}\n\n//# sourceMappingURL=HandlerRegistryImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/contracts.js":
/*!*************************************************!*\
  !*** ./node_modules/dnd-core/dist/contracts.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateSourceContract: () => (/* binding */ validateSourceContract),\n/* harmony export */   validateTargetContract: () => (/* binding */ validateTargetContract),\n/* harmony export */   validateType: () => (/* binding */ validateType)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n\nfunction validateSourceContract(source) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.canDrag === 'function', 'Expected canDrag to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.beginDrag === 'function', 'Expected beginDrag to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.endDrag === 'function', 'Expected endDrag to be a function.');\n}\nfunction validateTargetContract(target) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.canDrop === 'function', 'Expected canDrop to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.hover === 'function', 'Expected hover to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.drop === 'function', 'Expected beginDrag to be a function.');\n}\nfunction validateType(type, allowArray) {\n    if (allowArray && Array.isArray(type)) {\n        type.forEach((t)=>validateType(t, false)\n        );\n        return;\n    }\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof type === 'string' || typeof type === 'symbol', allowArray ? 'Type can only be a string, a symbol, or an array of either.' : 'Type can only be a string or a symbol.');\n}\n\n//# sourceMappingURL=contracts.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/contracts.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/createDragDropManager.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/createDragDropManager.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDragDropManager: () => (/* binding */ createDragDropManager)\n/* harmony export */ });\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/dnd-core/node_modules/redux/es/redux.js\");\n/* harmony import */ var _classes_DragDropManagerImpl_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./classes/DragDropManagerImpl.js */ \"(ssr)/./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js\");\n/* harmony import */ var _classes_DragDropMonitorImpl_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./classes/DragDropMonitorImpl.js */ \"(ssr)/./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js\");\n/* harmony import */ var _classes_HandlerRegistryImpl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./classes/HandlerRegistryImpl.js */ \"(ssr)/./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js\");\n/* harmony import */ var _reducers_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./reducers/index.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/index.js\");\n\n\n\n\n\nfunction createDragDropManager(backendFactory, globalContext = undefined, backendOptions = {}, debugMode = false) {\n    const store = makeStoreInstance(debugMode);\n    const monitor = new _classes_DragDropMonitorImpl_js__WEBPACK_IMPORTED_MODULE_0__.DragDropMonitorImpl(store, new _classes_HandlerRegistryImpl_js__WEBPACK_IMPORTED_MODULE_1__.HandlerRegistryImpl(store));\n    const manager = new _classes_DragDropManagerImpl_js__WEBPACK_IMPORTED_MODULE_2__.DragDropManagerImpl(store, monitor);\n    const backend = backendFactory(manager, globalContext, backendOptions);\n    manager.receiveBackend(backend);\n    return manager;\n}\nfunction makeStoreInstance(debugMode) {\n    // TODO: if we ever make a react-native version of this,\n    // we'll need to consider how to pull off dev-tooling\n    const reduxDevTools = typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION__;\n    return (0,redux__WEBPACK_IMPORTED_MODULE_3__.createStore)(_reducers_index_js__WEBPACK_IMPORTED_MODULE_4__.reduce, debugMode && reduxDevTools && reduxDevTools({\n        name: 'dnd-core',\n        instanceId: 'dnd-core'\n    }));\n}\n\n//# sourceMappingURL=createDragDropManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/createDragDropManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/interfaces.js":
/*!**************************************************!*\
  !*** ./node_modules/dnd-core/dist/interfaces.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandlerRole: () => (/* binding */ HandlerRole)\n/* harmony export */ });\nvar HandlerRole;\n(function(HandlerRole) {\n    HandlerRole[\"SOURCE\"] = \"SOURCE\";\n    HandlerRole[\"TARGET\"] = \"TARGET\";\n})(HandlerRole || (HandlerRole = {}));\n\n//# sourceMappingURL=interfaces.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9pbnRlcmZhY2VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLENBQUMsa0NBQWtDOztBQUVuQyIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L2ludGVyZmFjZXMuanM/NTI4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIEhhbmRsZXJSb2xlO1xuKGZ1bmN0aW9uKEhhbmRsZXJSb2xlKSB7XG4gICAgSGFuZGxlclJvbGVbXCJTT1VSQ0VcIl0gPSBcIlNPVVJDRVwiO1xuICAgIEhhbmRsZXJSb2xlW1wiVEFSR0VUXCJdID0gXCJUQVJHRVRcIjtcbn0pKEhhbmRsZXJSb2xlIHx8IChIYW5kbGVyUm9sZSA9IHt9KSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWludGVyZmFjZXMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/interfaces.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js":
/*!****************************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n/* harmony import */ var _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/dirtiness.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js\");\n/* harmony import */ var _utils_equality_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/equality.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/equality.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n\n\n\n\n\nfunction reduce(// eslint-disable-next-line @typescript-eslint/no-unused-vars\n_state = _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.NONE, action) {\n    switch(action.type){\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.HOVER:\n            break;\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.ADD_SOURCE:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.ADD_TARGET:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.REMOVE_TARGET:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.REMOVE_SOURCE:\n            return _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.NONE;\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.BEGIN_DRAG:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.PUBLISH_DRAG_SOURCE:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.END_DRAG:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.DROP:\n        default:\n            return _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.ALL;\n    }\n    const { targetIds =[] , prevTargetIds =[]  } = action.payload;\n    const result = (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__.xor)(targetIds, prevTargetIds);\n    const didChange = result.length > 0 || !(0,_utils_equality_js__WEBPACK_IMPORTED_MODULE_4__.areArraysEqual)(targetIds, prevTargetIds);\n    if (!didChange) {\n        return _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.NONE;\n    }\n    // Check the target ids at the innermost position. If they are valid, add them\n    // to the result\n    const prevInnermostTargetId = prevTargetIds[prevTargetIds.length - 1];\n    const innermostTargetId = targetIds[targetIds.length - 1];\n    if (prevInnermostTargetId !== innermostTargetId) {\n        if (prevInnermostTargetId) {\n            result.push(prevInnermostTargetId);\n        }\n        if (innermostTargetId) {\n            result.push(innermostTargetId);\n        }\n    }\n    return result;\n}\n\n//# sourceMappingURL=dirtyHandlerIds.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/dragOffset.js":
/*!***********************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/dragOffset.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n/* harmony import */ var _utils_equality_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/equality.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/equality.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\nconst initialState = {\n    initialSourceClientOffset: null,\n    initialClientOffset: null,\n    clientOffset: null\n};\nfunction reduce(state = initialState, action) {\n    const { payload  } = action;\n    switch(action.type){\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG:\n            return {\n                initialSourceClientOffset: payload.sourceClientOffset,\n                initialClientOffset: payload.clientOffset,\n                clientOffset: payload.clientOffset\n            };\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.HOVER:\n            if ((0,_utils_equality_js__WEBPACK_IMPORTED_MODULE_1__.areCoordsEqual)(state.clientOffset, payload.clientOffset)) {\n                return state;\n            }\n            return _objectSpread({}, state, {\n                clientOffset: payload.clientOffset\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.END_DRAG:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.DROP:\n            return initialState;\n        default:\n            return state;\n    }\n}\n\n//# sourceMappingURL=dragOffset.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/dragOffset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/dragOperation.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/dragOperation.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\nconst initialState = {\n    itemType: null,\n    item: null,\n    sourceId: null,\n    targetIds: [],\n    dropResult: null,\n    didDrop: false,\n    isSourcePublic: null\n};\nfunction reduce(state = initialState, action) {\n    const { payload  } = action;\n    switch(action.type){\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG:\n            return _objectSpread({}, state, {\n                itemType: payload.itemType,\n                item: payload.item,\n                sourceId: payload.sourceId,\n                isSourcePublic: payload.isSourcePublic,\n                dropResult: null,\n                didDrop: false\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE:\n            return _objectSpread({}, state, {\n                isSourcePublic: true\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.HOVER:\n            return _objectSpread({}, state, {\n                targetIds: payload.targetIds\n            });\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_1__.REMOVE_TARGET:\n            if (state.targetIds.indexOf(payload.targetId) === -1) {\n                return state;\n            }\n            return _objectSpread({}, state, {\n                targetIds: (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__.without)(state.targetIds, payload.targetId)\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.DROP:\n            return _objectSpread({}, state, {\n                dropResult: payload.dropResult,\n                didDrop: true,\n                targetIds: []\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.END_DRAG:\n            return _objectSpread({}, state, {\n                itemType: null,\n                item: null,\n                sourceId: null,\n                dropResult: null,\n                didDrop: false,\n                isSourcePublic: null,\n                targetIds: []\n            });\n        default:\n            return state;\n    }\n}\n\n//# sourceMappingURL=dragOperation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/dragOperation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/index.js":
/*!******************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n/* harmony import */ var _dirtyHandlerIds_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dirtyHandlerIds.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js\");\n/* harmony import */ var _dragOffset_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dragOffset.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/dragOffset.js\");\n/* harmony import */ var _dragOperation_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dragOperation.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/dragOperation.js\");\n/* harmony import */ var _refCount_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./refCount.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/refCount.js\");\n/* harmony import */ var _stateId_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./stateId.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/stateId.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\n\n\n\nfunction reduce(state = {}, action) {\n    return {\n        dirtyHandlerIds: (0,_dirtyHandlerIds_js__WEBPACK_IMPORTED_MODULE_0__.reduce)(state.dirtyHandlerIds, {\n            type: action.type,\n            payload: _objectSpread({}, action.payload, {\n                prevTargetIds: (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_1__.get)(state, 'dragOperation.targetIds', [])\n            })\n        }),\n        dragOffset: (0,_dragOffset_js__WEBPACK_IMPORTED_MODULE_2__.reduce)(state.dragOffset, action),\n        refCount: (0,_refCount_js__WEBPACK_IMPORTED_MODULE_3__.reduce)(state.refCount, action),\n        dragOperation: (0,_dragOperation_js__WEBPACK_IMPORTED_MODULE_4__.reduce)(state.dragOperation, action),\n        stateId: (0,_stateId_js__WEBPACK_IMPORTED_MODULE_5__.reduce)(state.stateId)\n    };\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/refCount.js":
/*!*********************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/refCount.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n\nfunction reduce(state = 0, action) {\n    switch(action.type){\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.ADD_SOURCE:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.ADD_TARGET:\n            return state + 1;\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.REMOVE_SOURCE:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.REMOVE_TARGET:\n            return state - 1;\n        default:\n            return state;\n    }\n}\n\n//# sourceMappingURL=refCount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9yZWR1Y2Vycy9yZWZDb3VudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4RjtBQUN2RjtBQUNQO0FBQ0EsYUFBYSw0REFBVTtBQUN2QixhQUFhLDREQUFVO0FBQ3ZCO0FBQ0EsYUFBYSwrREFBYTtBQUMxQixhQUFhLCtEQUFhO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9yZWR1Y2Vycy9yZWZDb3VudC5qcz85ZjJhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFERF9TT1VSQ0UsIEFERF9UQVJHRVQsIFJFTU9WRV9TT1VSQ0UsIFJFTU9WRV9UQVJHRVQgfSBmcm9tICcuLi9hY3Rpb25zL3JlZ2lzdHJ5LmpzJztcbmV4cG9ydCBmdW5jdGlvbiByZWR1Y2Uoc3RhdGUgPSAwLCBhY3Rpb24pIHtcbiAgICBzd2l0Y2goYWN0aW9uLnR5cGUpe1xuICAgICAgICBjYXNlIEFERF9TT1VSQ0U6XG4gICAgICAgIGNhc2UgQUREX1RBUkdFVDpcbiAgICAgICAgICAgIHJldHVybiBzdGF0ZSArIDE7XG4gICAgICAgIGNhc2UgUkVNT1ZFX1NPVVJDRTpcbiAgICAgICAgY2FzZSBSRU1PVkVfVEFSR0VUOlxuICAgICAgICAgICAgcmV0dXJuIHN0YXRlIC0gMTtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHJldHVybiBzdGF0ZTtcbiAgICB9XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlZkNvdW50LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/refCount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/stateId.js":
/*!********************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/stateId.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\nfunction reduce(state = 0) {\n    return state + 1;\n}\n\n//# sourceMappingURL=stateId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9yZWR1Y2Vycy9zdGF0ZUlkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9yZWR1Y2Vycy9zdGF0ZUlkLmpzP2ZjMGIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHJlZHVjZShzdGF0ZSA9IDApIHtcbiAgICByZXR1cm4gc3RhdGUgKyAxO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdGF0ZUlkLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/stateId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/coords.js":
/*!****************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/coords.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   getDifferenceFromInitialOffset: () => (/* binding */ getDifferenceFromInitialOffset),\n/* harmony export */   getSourceClientOffset: () => (/* binding */ getSourceClientOffset),\n/* harmony export */   subtract: () => (/* binding */ subtract)\n/* harmony export */ });\n/**\n * Coordinate addition\n * @param a The first coordinate\n * @param b The second coordinate\n */ function add(a, b) {\n    return {\n        x: a.x + b.x,\n        y: a.y + b.y\n    };\n}\n/**\n * Coordinate subtraction\n * @param a The first coordinate\n * @param b The second coordinate\n */ function subtract(a, b) {\n    return {\n        x: a.x - b.x,\n        y: a.y - b.y\n    };\n}\n/**\n * Returns the cartesian distance of the drag source component's position, based on its position\n * at the time when the current drag operation has started, and the movement difference.\n *\n * Returns null if no item is being dragged.\n *\n * @param state The offset state to compute from\n */ function getSourceClientOffset(state) {\n    const { clientOffset , initialClientOffset , initialSourceClientOffset  } = state;\n    if (!clientOffset || !initialClientOffset || !initialSourceClientOffset) {\n        return null;\n    }\n    return subtract(add(clientOffset, initialSourceClientOffset), initialClientOffset);\n}\n/**\n * Determines the x,y offset between the client offset and the initial client offset\n *\n * @param state The offset state to compute from\n */ function getDifferenceFromInitialOffset(state) {\n    const { clientOffset , initialClientOffset  } = state;\n    if (!clientOffset || !initialClientOffset) {\n        return null;\n    }\n    return subtract(clientOffset, initialClientOffset);\n}\n\n//# sourceMappingURL=coords.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/coords.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js":
/*!*******************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/dirtiness.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL: () => (/* binding */ ALL),\n/* harmony export */   NONE: () => (/* binding */ NONE),\n/* harmony export */   areDirty: () => (/* binding */ areDirty)\n/* harmony export */ });\n/* harmony import */ var _js_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n\nconst NONE = [];\nconst ALL = [];\nNONE.__IS_NONE__ = true;\nALL.__IS_ALL__ = true;\n/**\n * Determines if the given handler IDs are dirty or not.\n *\n * @param dirtyIds The set of dirty handler ids\n * @param handlerIds The set of handler ids to check\n */ function areDirty(dirtyIds, handlerIds) {\n    if (dirtyIds === NONE) {\n        return false;\n    }\n    if (dirtyIds === ALL || typeof handlerIds === 'undefined') {\n        return true;\n    }\n    const commonIds = (0,_js_utils_js__WEBPACK_IMPORTED_MODULE_0__.intersection)(handlerIds, dirtyIds);\n    return commonIds.length > 0;\n}\n\n//# sourceMappingURL=dirtiness.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9kaXJ0aW5lc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE2QztBQUN0QztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDBEQUFZO0FBQ2xDO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9kaXJ0aW5lc3MuanM/NTg3MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpbnRlcnNlY3Rpb24gfSBmcm9tICcuL2pzX3V0aWxzLmpzJztcbmV4cG9ydCBjb25zdCBOT05FID0gW107XG5leHBvcnQgY29uc3QgQUxMID0gW107XG5OT05FLl9fSVNfTk9ORV9fID0gdHJ1ZTtcbkFMTC5fX0lTX0FMTF9fID0gdHJ1ZTtcbi8qKlxuICogRGV0ZXJtaW5lcyBpZiB0aGUgZ2l2ZW4gaGFuZGxlciBJRHMgYXJlIGRpcnR5IG9yIG5vdC5cbiAqXG4gKiBAcGFyYW0gZGlydHlJZHMgVGhlIHNldCBvZiBkaXJ0eSBoYW5kbGVyIGlkc1xuICogQHBhcmFtIGhhbmRsZXJJZHMgVGhlIHNldCBvZiBoYW5kbGVyIGlkcyB0byBjaGVja1xuICovIGV4cG9ydCBmdW5jdGlvbiBhcmVEaXJ0eShkaXJ0eUlkcywgaGFuZGxlcklkcykge1xuICAgIGlmIChkaXJ0eUlkcyA9PT0gTk9ORSkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGlmIChkaXJ0eUlkcyA9PT0gQUxMIHx8IHR5cGVvZiBoYW5kbGVySWRzID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgY29uc3QgY29tbW9uSWRzID0gaW50ZXJzZWN0aW9uKGhhbmRsZXJJZHMsIGRpcnR5SWRzKTtcbiAgICByZXR1cm4gY29tbW9uSWRzLmxlbmd0aCA+IDA7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRpcnRpbmVzcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/equality.js":
/*!******************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/equality.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areArraysEqual: () => (/* binding */ areArraysEqual),\n/* harmony export */   areCoordsEqual: () => (/* binding */ areCoordsEqual),\n/* harmony export */   strictEquality: () => (/* binding */ strictEquality)\n/* harmony export */ });\nconst strictEquality = (a, b)=>a === b\n;\n/**\n * Determine if two cartesian coordinate offsets are equal\n * @param offsetA\n * @param offsetB\n */ function areCoordsEqual(offsetA, offsetB) {\n    if (!offsetA && !offsetB) {\n        return true;\n    } else if (!offsetA || !offsetB) {\n        return false;\n    } else {\n        return offsetA.x === offsetB.x && offsetA.y === offsetB.y;\n    }\n}\n/**\n * Determines if two arrays of items are equal\n * @param a The first array of items\n * @param b The second array of items\n */ function areArraysEqual(a, b, isEqual = strictEquality) {\n    if (a.length !== b.length) {\n        return false;\n    }\n    for(let i = 0; i < a.length; ++i){\n        if (!isEqual(a[i], b[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\n//# sourceMappingURL=equality.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9lcXVhbGl0eS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFXO0FBQ1g7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsY0FBYztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9lcXVhbGl0eS5qcz83MWIyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBzdHJpY3RFcXVhbGl0eSA9IChhLCBiKT0+YSA9PT0gYlxuO1xuLyoqXG4gKiBEZXRlcm1pbmUgaWYgdHdvIGNhcnRlc2lhbiBjb29yZGluYXRlIG9mZnNldHMgYXJlIGVxdWFsXG4gKiBAcGFyYW0gb2Zmc2V0QVxuICogQHBhcmFtIG9mZnNldEJcbiAqLyBleHBvcnQgZnVuY3Rpb24gYXJlQ29vcmRzRXF1YWwob2Zmc2V0QSwgb2Zmc2V0Qikge1xuICAgIGlmICghb2Zmc2V0QSAmJiAhb2Zmc2V0Qikge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGVsc2UgaWYgKCFvZmZzZXRBIHx8ICFvZmZzZXRCKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gb2Zmc2V0QS54ID09PSBvZmZzZXRCLnggJiYgb2Zmc2V0QS55ID09PSBvZmZzZXRCLnk7XG4gICAgfVxufVxuLyoqXG4gKiBEZXRlcm1pbmVzIGlmIHR3byBhcnJheXMgb2YgaXRlbXMgYXJlIGVxdWFsXG4gKiBAcGFyYW0gYSBUaGUgZmlyc3QgYXJyYXkgb2YgaXRlbXNcbiAqIEBwYXJhbSBiIFRoZSBzZWNvbmQgYXJyYXkgb2YgaXRlbXNcbiAqLyBleHBvcnQgZnVuY3Rpb24gYXJlQXJyYXlzRXF1YWwoYSwgYiwgaXNFcXVhbCA9IHN0cmljdEVxdWFsaXR5KSB7XG4gICAgaWYgKGEubGVuZ3RoICE9PSBiLmxlbmd0aCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGZvcihsZXQgaSA9IDA7IGkgPCBhLmxlbmd0aDsgKytpKXtcbiAgICAgICAgaWYgKCFpc0VxdWFsKGFbaV0sIGJbaV0pKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWVxdWFsaXR5LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/equality.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/getNextUniqueId.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/getNextUniqueId.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNextUniqueId: () => (/* binding */ getNextUniqueId)\n/* harmony export */ });\nlet nextUniqueId = 0;\nfunction getNextUniqueId() {\n    return nextUniqueId++;\n}\n\n//# sourceMappingURL=getNextUniqueId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9nZXROZXh0VW5pcXVlSWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUDtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGVudGFiZWxsLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvdXRpbHMvZ2V0TmV4dFVuaXF1ZUlkLmpzPzNhNmQiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IG5leHRVbmlxdWVJZCA9IDA7XG5leHBvcnQgZnVuY3Rpb24gZ2V0TmV4dFVuaXF1ZUlkKCkge1xuICAgIHJldHVybiBuZXh0VW5pcXVlSWQrKztcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0TmV4dFVuaXF1ZUlkLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/getNextUniqueId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js":
/*!******************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/js_utils.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   intersection: () => (/* binding */ intersection),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   without: () => (/* binding */ without),\n/* harmony export */   xor: () => (/* binding */ xor)\n/* harmony export */ });\n// cheap lodash replacements\n/**\n * drop-in replacement for _.get\n * @param obj\n * @param path\n * @param defaultValue\n */ function get(obj, path, defaultValue) {\n    return path.split('.').reduce((a, c)=>a && a[c] ? a[c] : defaultValue || null\n    , obj);\n}\n/**\n * drop-in replacement for _.without\n */ function without(items, item) {\n    return items.filter((i)=>i !== item\n    );\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */ function isString(input) {\n    return typeof input === 'string';\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */ function isObject(input) {\n    return typeof input === 'object';\n}\n/**\n * replacement for _.xor\n * @param itemsA\n * @param itemsB\n */ function xor(itemsA, itemsB) {\n    const map = new Map();\n    const insertItem = (item)=>{\n        map.set(item, map.has(item) ? map.get(item) + 1 : 1);\n    };\n    itemsA.forEach(insertItem);\n    itemsB.forEach(insertItem);\n    const result = [];\n    map.forEach((count, key)=>{\n        if (count === 1) {\n            result.push(key);\n        }\n    });\n    return result;\n}\n/**\n * replacement for _.intersection\n * @param itemsA\n * @param itemsB\n */ function intersection(itemsA, itemsB) {\n    return itemsA.filter((t)=>itemsB.indexOf(t) > -1\n    );\n}\n\n//# sourceMappingURL=js_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9qc191dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFXO0FBQ1g7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGVudGFiZWxsLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvdXRpbHMvanNfdXRpbHMuanM/MzRmNSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBjaGVhcCBsb2Rhc2ggcmVwbGFjZW1lbnRzXG4vKipcbiAqIGRyb3AtaW4gcmVwbGFjZW1lbnQgZm9yIF8uZ2V0XG4gKiBAcGFyYW0gb2JqXG4gKiBAcGFyYW0gcGF0aFxuICogQHBhcmFtIGRlZmF1bHRWYWx1ZVxuICovIGV4cG9ydCBmdW5jdGlvbiBnZXQob2JqLCBwYXRoLCBkZWZhdWx0VmFsdWUpIHtcbiAgICByZXR1cm4gcGF0aC5zcGxpdCgnLicpLnJlZHVjZSgoYSwgYyk9PmEgJiYgYVtjXSA/IGFbY10gOiBkZWZhdWx0VmFsdWUgfHwgbnVsbFxuICAgICwgb2JqKTtcbn1cbi8qKlxuICogZHJvcC1pbiByZXBsYWNlbWVudCBmb3IgXy53aXRob3V0XG4gKi8gZXhwb3J0IGZ1bmN0aW9uIHdpdGhvdXQoaXRlbXMsIGl0ZW0pIHtcbiAgICByZXR1cm4gaXRlbXMuZmlsdGVyKChpKT0+aSAhPT0gaXRlbVxuICAgICk7XG59XG4vKipcbiAqIGRyb3AtaW4gcmVwbGFjZW1lbnQgZm9yIF8uaXNTdHJpbmdcbiAqIEBwYXJhbSBpbnB1dFxuICovIGV4cG9ydCBmdW5jdGlvbiBpc1N0cmluZyhpbnB1dCkge1xuICAgIHJldHVybiB0eXBlb2YgaW5wdXQgPT09ICdzdHJpbmcnO1xufVxuLyoqXG4gKiBkcm9wLWluIHJlcGxhY2VtZW50IGZvciBfLmlzU3RyaW5nXG4gKiBAcGFyYW0gaW5wdXRcbiAqLyBleHBvcnQgZnVuY3Rpb24gaXNPYmplY3QoaW5wdXQpIHtcbiAgICByZXR1cm4gdHlwZW9mIGlucHV0ID09PSAnb2JqZWN0Jztcbn1cbi8qKlxuICogcmVwbGFjZW1lbnQgZm9yIF8ueG9yXG4gKiBAcGFyYW0gaXRlbXNBXG4gKiBAcGFyYW0gaXRlbXNCXG4gKi8gZXhwb3J0IGZ1bmN0aW9uIHhvcihpdGVtc0EsIGl0ZW1zQikge1xuICAgIGNvbnN0IG1hcCA9IG5ldyBNYXAoKTtcbiAgICBjb25zdCBpbnNlcnRJdGVtID0gKGl0ZW0pPT57XG4gICAgICAgIG1hcC5zZXQoaXRlbSwgbWFwLmhhcyhpdGVtKSA/IG1hcC5nZXQoaXRlbSkgKyAxIDogMSk7XG4gICAgfTtcbiAgICBpdGVtc0EuZm9yRWFjaChpbnNlcnRJdGVtKTtcbiAgICBpdGVtc0IuZm9yRWFjaChpbnNlcnRJdGVtKTtcbiAgICBjb25zdCByZXN1bHQgPSBbXTtcbiAgICBtYXAuZm9yRWFjaCgoY291bnQsIGtleSk9PntcbiAgICAgICAgaWYgKGNvdW50ID09PSAxKSB7XG4gICAgICAgICAgICByZXN1bHQucHVzaChrZXkpO1xuICAgICAgICB9XG4gICAgfSk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbn1cbi8qKlxuICogcmVwbGFjZW1lbnQgZm9yIF8uaW50ZXJzZWN0aW9uXG4gKiBAcGFyYW0gaXRlbXNBXG4gKiBAcGFyYW0gaXRlbXNCXG4gKi8gZXhwb3J0IGZ1bmN0aW9uIGludGVyc2VjdGlvbihpdGVtc0EsIGl0ZW1zQikge1xuICAgIHJldHVybiBpdGVtc0EuZmlsdGVyKCh0KT0+aXRlbXNCLmluZGV4T2YodCkgPiAtMVxuICAgICk7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWpzX3V0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js":
/*!*********************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/matchesType.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   matchesType: () => (/* binding */ matchesType)\n/* harmony export */ });\nfunction matchesType(targetType, draggedItemType) {\n    if (draggedItemType === null) {\n        return targetType === null;\n    }\n    return Array.isArray(targetType) ? targetType.some((t)=>t === draggedItemType\n    ) : targetType === draggedItemType;\n}\n\n//# sourceMappingURL=matchesType.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9tYXRjaGVzVHlwZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9tYXRjaGVzVHlwZS5qcz9mMGZjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBtYXRjaGVzVHlwZSh0YXJnZXRUeXBlLCBkcmFnZ2VkSXRlbVR5cGUpIHtcbiAgICBpZiAoZHJhZ2dlZEl0ZW1UeXBlID09PSBudWxsKSB7XG4gICAgICAgIHJldHVybiB0YXJnZXRUeXBlID09PSBudWxsO1xuICAgIH1cbiAgICByZXR1cm4gQXJyYXkuaXNBcnJheSh0YXJnZXRUeXBlKSA/IHRhcmdldFR5cGUuc29tZSgodCk9PnQgPT09IGRyYWdnZWRJdGVtVHlwZVxuICAgICkgOiB0YXJnZXRUeXBlID09PSBkcmFnZ2VkSXRlbVR5cGU7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1hdGNoZXNUeXBlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js\n");

/***/ })

};
;