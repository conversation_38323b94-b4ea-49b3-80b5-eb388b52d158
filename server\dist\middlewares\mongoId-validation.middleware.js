"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateMongoIds = void 0;
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const messages_1 = require("@/utils/helpers/messages");
const mongoose_1 = __importDefault(require("mongoose"));
const validateMongoId = async (request, response, next) => {
    const id = request.params;
    if (!mongoose_1.default.Types.ObjectId.isValid(id))
        next(new http_exception_1.default(400, messages_1.MESSAGES.GENERAL.INVALID_ID));
    next();
};
const validateMongoIds = async (request, response, next) => {
    const ids = Object.values(request.params);
    for (const id of ids) {
        if (!mongoose_1.default.Types.ObjectId.isValid(id))
            next(new http_exception_1.default(400, messages_1.MESSAGES.GENERAL.INVALID_ID));
    }
    next();
};
exports.validateMongoIds = validateMongoIds;
exports.default = validateMongoId;
//# sourceMappingURL=mongoId-validation.middleware.js.map