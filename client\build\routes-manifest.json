{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [{"page": "/[locale]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)(?:/)?$"}, {"page": "/[locale]/about-us", "regex": "^/([^/]+?)/about\\-us(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/about\\-us(?:/)?$"}, {"page": "/[locale]/activation", "regex": "^/([^/]+?)/activation(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/activation(?:/)?$"}, {"page": "/[locale]/activation-account", "regex": "^/([^/]+?)/activation\\-account(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/activation\\-account(?:/)?$"}, {"page": "/[locale]/application-received", "regex": "^/([^/]+?)/application\\-received(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/application\\-received(?:/)?$"}, {"page": "/[locale]/apply/[opportunity]", "regex": "^/([^/]+?)/apply/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPopportunity": "nxtPopportunity"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/apply/(?<nxtPopportunity>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/applications", "regex": "^/([^/]+?)/backoffice/applications(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/applications(?:/)?$"}, {"page": "/[locale]/backoffice/applications/detail/[id]", "regex": "^/([^/]+?)/backoffice/applications/detail/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/applications/detail/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/applications/edit/[id]", "regex": "^/([^/]+?)/backoffice/applications/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/applications/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/applications/opportunity/[id]", "regex": "^/([^/]+?)/backoffice/applications/opportunity/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/applications/opportunity/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/applications/[id]", "regex": "^/([^/]+?)/backoffice/applications/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/applications/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/blogs", "regex": "^/([^/]+?)/backoffice/blogs(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/blogs(?:/)?$"}, {"page": "/[locale]/backoffice/blogs/add", "regex": "^/([^/]+?)/backoffice/blogs/add(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/blogs/add(?:/)?$"}, {"page": "/[locale]/backoffice/blogs/comments/[id]", "regex": "^/([^/]+?)/backoffice/blogs/comments/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/blogs/comments/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/blogs/edit/[id]", "regex": "^/([^/]+?)/backoffice/blogs/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/blogs/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/categories", "regex": "^/([^/]+?)/backoffice/categories(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/categories(?:/)?$"}, {"page": "/[locale]/backoffice/categories/add", "regex": "^/([^/]+?)/backoffice/categories/add(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/categories/add(?:/)?$"}, {"page": "/[locale]/backoffice/categories/edit/[id]", "regex": "^/([^/]+?)/backoffice/categories/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/categories/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/comments", "regex": "^/([^/]+?)/backoffice/comments(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/comments(?:/)?$"}, {"page": "/[locale]/backoffice/comments/detail/[id]", "regex": "^/([^/]+?)/backoffice/comments/detail/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/comments/detail/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/contacts", "regex": "^/([^/]+?)/backoffice/contacts(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/contacts(?:/)?$"}, {"page": "/[locale]/backoffice/contacts/detail/[id]", "regex": "^/([^/]+?)/backoffice/contacts/detail/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/contacts/detail/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/downloadReport", "regex": "^/([^/]+?)/backoffice/downloadReport(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/downloadReport(?:/)?$"}, {"page": "/[locale]/backoffice/events", "regex": "^/([^/]+?)/backoffice/events(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/events(?:/)?$"}, {"page": "/[locale]/backoffice/events/add", "regex": "^/([^/]+?)/backoffice/events/add(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/events/add(?:/)?$"}, {"page": "/[locale]/backoffice/events/edit/[id]", "regex": "^/([^/]+?)/backoffice/events/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/events/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/guides", "regex": "^/([^/]+?)/backoffice/guides(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/guides(?:/)?$"}, {"page": "/[locale]/backoffice/guides/add", "regex": "^/([^/]+?)/backoffice/guides/add(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/guides/add(?:/)?$"}, {"page": "/[locale]/backoffice/guides/categories", "regex": "^/([^/]+?)/backoffice/guides/categories(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/guides/categories(?:/)?$"}, {"page": "/[locale]/backoffice/guides/categories/add", "regex": "^/([^/]+?)/backoffice/guides/categories/add(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/guides/categories/add(?:/)?$"}, {"page": "/[locale]/backoffice/guides/categories/edit", "regex": "^/([^/]+?)/backoffice/guides/categories/edit(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/guides/categories/edit(?:/)?$"}, {"page": "/[locale]/backoffice/guides/categories/edit/[id]", "regex": "^/([^/]+?)/backoffice/guides/categories/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/guides/categories/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/guides/downloads/[id]", "regex": "^/([^/]+?)/backoffice/guides/downloads/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/guides/downloads/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/guides/edit/[id]", "regex": "^/([^/]+?)/backoffice/guides/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/guides/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/home", "regex": "^/([^/]+?)/backoffice/home(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/home(?:/)?$"}, {"page": "/[locale]/backoffice/my-profile", "regex": "^/([^/]+?)/backoffice/my\\-profile(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/my\\-profile(?:/)?$"}, {"page": "/[locale]/backoffice/newsletters", "regex": "^/([^/]+?)/backoffice/newsletters(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/newsletters(?:/)?$"}, {"page": "/[locale]/backoffice/notifications", "regex": "^/([^/]+?)/backoffice/notifications(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/notifications(?:/)?$"}, {"page": "/[locale]/backoffice/opportunities", "regex": "^/([^/]+?)/backoffice/opportunities(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/opportunities(?:/)?$"}, {"page": "/[locale]/backoffice/opportunities/edit/[id]", "regex": "^/([^/]+?)/backoffice/opportunities/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/opportunities/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/opportunities/edit-seo-tags", "regex": "^/([^/]+?)/backoffice/opportunities/edit\\-seo\\-tags(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/opportunities/edit\\-seo\\-tags(?:/)?$"}, {"page": "/[locale]/backoffice/seo-settings", "regex": "^/([^/]+?)/backoffice/seo\\-settings(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/seo\\-settings(?:/)?$"}, {"page": "/[locale]/backoffice/seo-settings/add", "regex": "^/([^/]+?)/backoffice/seo\\-settings/add(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/seo\\-settings/add(?:/)?$"}, {"page": "/[locale]/backoffice/seo-settings/detail/[slug]", "regex": "^/([^/]+?)/backoffice/seo\\-settings/detail/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPslug": "nxtPslug"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/seo\\-settings/detail/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/seo-settings/edit/[id]", "regex": "^/([^/]+?)/backoffice/seo\\-settings/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/seo\\-settings/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/settings", "regex": "^/([^/]+?)/backoffice/settings(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/settings(?:/)?$"}, {"page": "/[locale]/backoffice/sliders", "regex": "^/([^/]+?)/backoffice/sliders(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/sliders(?:/)?$"}, {"page": "/[locale]/backoffice/sliders/add", "regex": "^/([^/]+?)/backoffice/sliders/add(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/sliders/add(?:/)?$"}, {"page": "/[locale]/backoffice/sliders/edit/[id]", "regex": "^/([^/]+?)/backoffice/sliders/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/sliders/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/sliders/updateslider", "regex": "^/([^/]+?)/backoffice/sliders/updateslider(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/sliders/updateslider(?:/)?$"}, {"page": "/[locale]/backoffice/statistics", "regex": "^/([^/]+?)/backoffice/statistics(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/statistics(?:/)?$"}, {"page": "/[locale]/backoffice/users", "regex": "^/([^/]+?)/backoffice/users(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/users(?:/)?$"}, {"page": "/[locale]/backoffice/users/add", "regex": "^/([^/]+?)/backoffice/users/add(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/users/add(?:/)?$"}, {"page": "/[locale]/backoffice/users/detail/[id]", "regex": "^/([^/]+?)/backoffice/users/detail/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/users/detail/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/backoffice/users/edit/[id]", "regex": "^/([^/]+?)/backoffice/users/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/backoffice/users/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/blog", "regex": "^/([^/]+?)/blog(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/blog(?:/)?$"}, {"page": "/[locale]/blog/category/[category]", "regex": "^/([^/]+?)/blog/category/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPcategory": "nxtPcategory"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/blog/category/(?<nxtPcategory>[^/]+?)(?:/)?$"}, {"page": "/[locale]/blog/[url]", "regex": "^/([^/]+?)/blog/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPurl": "nxtPurl"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/blog/(?<nxtPurl>[^/]+?)(?:/)?$"}, {"page": "/[locale]/confirm-application", "regex": "^/([^/]+?)/confirm\\-application(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/confirm\\-application(?:/)?$"}, {"page": "/[locale]/contact", "regex": "^/([^/]+?)/contact(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/contact(?:/)?$"}, {"page": "/[locale]/corporate-profile", "regex": "^/([^/]+?)/corporate\\-profile(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/corporate\\-profile(?:/)?$"}, {"page": "/[locale]/dashboard", "regex": "^/([^/]+?)/dashboard(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/dashboard(?:/)?$"}, {"page": "/[locale]/dashboard/favoris", "regex": "^/([^/]+?)/dashboard/favoris(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/dashboard/favoris(?:/)?$"}, {"page": "/[locale]/dashboard/home", "regex": "^/([^/]+?)/dashboard/home(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/dashboard/home(?:/)?$"}, {"page": "/[locale]/dashboard/my-applications", "regex": "^/([^/]+?)/dashboard/my\\-applications(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/dashboard/my\\-applications(?:/)?$"}, {"page": "/[locale]/dashboard/my-profile", "regex": "^/([^/]+?)/dashboard/my\\-profile(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/dashboard/my\\-profile(?:/)?$"}, {"page": "/[locale]/dashboard/my-resumes", "regex": "^/([^/]+?)/dashboard/my\\-resumes(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/dashboard/my\\-resumes(?:/)?$"}, {"page": "/[locale]/dashboard/notifications", "regex": "^/([^/]+?)/dashboard/notifications(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/dashboard/notifications(?:/)?$"}, {"page": "/[locale]/dashboard/settings", "regex": "^/([^/]+?)/dashboard/settings(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/dashboard/settings(?:/)?$"}, {"page": "/[locale]/document/pfe-book-2024-2025", "regex": "^/([^/]+?)/document/pfe\\-book\\-2024\\-2025(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/document/pfe\\-book\\-2024\\-2025(?:/)?$"}, {"page": "/[locale]/events", "regex": "^/([^/]+?)/events(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/events(?:/)?$"}, {"page": "/[locale]/events/[url]", "regex": "^/([^/]+?)/events/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPurl": "nxtPurl"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/events/(?<nxtPurl>[^/]+?)(?:/)?$"}, {"page": "/[locale]/expert-care-demo", "regex": "^/([^/]+?)/expert\\-care\\-demo(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/expert\\-care\\-demo(?:/)?$"}, {"page": "/[locale]/forgot-password", "regex": "^/([^/]+?)/forgot\\-password(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/forgot\\-password(?:/)?$"}, {"page": "/[locale]/guide-to-hiring-employees-in-egypt", "regex": "^/([^/]+?)/guide\\-to\\-hiring\\-employees\\-in\\-egypt(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/guide\\-to\\-hiring\\-employees\\-in\\-egypt(?:/)?$"}, {"page": "/[locale]/guide-to-hiring-employees-in-libya", "regex": "^/([^/]+?)/guide\\-to\\-hiring\\-employees\\-in\\-libya(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/guide\\-to\\-hiring\\-employees\\-in\\-libya(?:/)?$"}, {"page": "/[locale]/guides", "regex": "^/([^/]+?)/guides(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/guides(?:/)?$"}, {"page": "/[locale]/guides/category/[category]", "regex": "^/([^/]+?)/guides/category/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPcategory": "nxtPcategory"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/guides/category/(?<nxtPcategory>[^/]+?)(?:/)?$"}, {"page": "/[locale]/guides/[url]", "regex": "^/([^/]+?)/guides/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPurl": "nxtPurl"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/guides/(?<nxtPurl>[^/]+?)(?:/)?$"}, {"page": "/[locale]/hiring-employees-tunisia-guide", "regex": "^/([^/]+?)/hiring\\-employees\\-tunisia\\-guide(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/hiring\\-employees\\-tunisia\\-guide(?:/)?$"}, {"page": "/[locale]/hr-services", "regex": "^/([^/]+?)/hr\\-services(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/hr\\-services(?:/)?$"}, {"page": "/[locale]/hr-services/consulting-services", "regex": "^/([^/]+?)/hr\\-services/consulting\\-services(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/hr\\-services/consulting\\-services(?:/)?$"}, {"page": "/[locale]/hr-services/direct-hiring-solutions", "regex": "^/([^/]+?)/hr\\-services/direct\\-hiring\\-solutions(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/hr\\-services/direct\\-hiring\\-solutions(?:/)?$"}, {"page": "/[locale]/hr-services/payroll-service", "regex": "^/([^/]+?)/hr\\-services/payroll\\-service(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/hr\\-services/payroll\\-service(?:/)?$"}, {"page": "/[locale]/hr-services/pentabell-ai-sourcing-coordinators", "regex": "^/([^/]+?)/hr\\-services/pentabell\\-ai\\-sourcing\\-coordinators(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/hr\\-services/pentabell\\-ai\\-sourcing\\-coordinators(?:/)?$"}, {"page": "/[locale]/hr-services/technical-assistance", "regex": "^/([^/]+?)/hr\\-services/technical\\-assistance(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/hr\\-services/technical\\-assistance(?:/)?$"}, {"page": "/[locale]/international-hr-services-recruitment-agency-iraq", "regex": "^/([^/]+?)/international\\-hr\\-services\\-recruitment\\-agency\\-iraq(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/international\\-hr\\-services\\-recruitment\\-agency\\-iraq(?:/)?$"}, {"page": "/[locale]/international-hr-services-recruitment-agency-ksa", "regex": "^/([^/]+?)/international\\-hr\\-services\\-recruitment\\-agency\\-ksa(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/international\\-hr\\-services\\-recruitment\\-agency\\-ksa(?:/)?$"}, {"page": "/[locale]/international-hr-services-recruitment-agency-qatar", "regex": "^/([^/]+?)/international\\-hr\\-services\\-recruitment\\-agency\\-qatar(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/international\\-hr\\-services\\-recruitment\\-agency\\-qatar(?:/)?$"}, {"page": "/[locale]/international-recruitment-staffing-company-in-africa", "regex": "^/([^/]+?)/international\\-recruitment\\-staffing\\-company\\-in\\-africa(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/international\\-recruitment\\-staffing\\-company\\-in\\-africa(?:/)?$"}, {"page": "/[locale]/international-recruitment-staffing-company-in-europe", "regex": "^/([^/]+?)/international\\-recruitment\\-staffing\\-company\\-in\\-europe(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/international\\-recruitment\\-staffing\\-company\\-in\\-europe(?:/)?$"}, {"page": "/[locale]/international-recruitment-staffing-company-in-middle-east", "regex": "^/([^/]+?)/international\\-recruitment\\-staffing\\-company\\-in\\-middle\\-east(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/international\\-recruitment\\-staffing\\-company\\-in\\-middle\\-east(?:/)?$"}, {"page": "/[locale]/job-category/[industry]", "regex": "^/([^/]+?)/job\\-category/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPindustry": "nxtPindustry"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/job\\-category/(?<nxtPindustry>[^/]+?)(?:/)?$"}, {"page": "/[locale]/job-location/[country]", "regex": "^/([^/]+?)/job\\-location/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPcountry": "nxtPcountry"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/job\\-location/(?<nxtPcountry>[^/]+?)(?:/)?$"}, {"page": "/[locale]/join-us", "regex": "^/([^/]+?)/join\\-us(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/join\\-us(?:/)?$"}, {"page": "/[locale]/login", "regex": "^/([^/]+?)/login(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/login(?:/)?$"}, {"page": "/[locale]/logout", "regex": "^/([^/]+?)/logout(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/logout(?:/)?$"}, {"page": "/[locale]/opportunities", "regex": "^/([^/]+?)/opportunities(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/opportunities(?:/)?$"}, {"page": "/[locale]/opportunities/[slug]", "regex": "^/([^/]+?)/opportunities/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPslug": "nxtPslug"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/opportunities/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/[locale]/privacy-policy", "regex": "^/([^/]+?)/privacy\\-policy(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/privacy\\-policy(?:/)?$"}, {"page": "/[locale]/recruitment-agency-france", "regex": "^/([^/]+?)/recruitment\\-agency\\-france(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/recruitment\\-agency\\-france(?:/)?$"}, {"page": "/[locale]/recruitment-staffing-agency-dubai", "regex": "^/([^/]+?)/recruitment\\-staffing\\-agency\\-dubai(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/recruitment\\-staffing\\-agency\\-dubai(?:/)?$"}, {"page": "/[locale]/register", "regex": "^/([^/]+?)/register(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/register(?:/)?$"}, {"page": "/[locale]/reset-password", "regex": "^/([^/]+?)/reset\\-password(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/reset\\-password(?:/)?$"}, {"page": "/[locale]/terms-and-conditions", "regex": "^/([^/]+?)/terms\\-and\\-conditions(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/terms\\-and\\-conditions(?:/)?$"}, {"page": "/[locale]/ultimate-guide-to-hiring-employees-in-algeria", "regex": "^/([^/]+?)/ultimate\\-guide\\-to\\-hiring\\-employees\\-in\\-algeria(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/ultimate\\-guide\\-to\\-hiring\\-employees\\-in\\-algeria(?:/)?$"}, {"page": "/[locale]/ultimate-guide-to-hiring-employees-in-morocco", "regex": "^/([^/]+?)/ultimate\\-guide\\-to\\-hiring\\-employees\\-in\\-morocco(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/ultimate\\-guide\\-to\\-hiring\\-employees\\-in\\-morocco(?:/)?$"}], "staticRoutes": [{"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}