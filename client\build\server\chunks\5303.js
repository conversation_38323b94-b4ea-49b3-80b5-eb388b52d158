exports.id=5303,exports.ids=[5303],exports.modules={35766:(e,a,t)=>{var l={"./ckb.js":5228,"./cs.js":88020,"./da.js":38280,"./de.js":16272,"./en.js":79175,"./es.js":35537,"./fa.js":73767,"./fr.js":47259,"./he.js":94438,"./hu.js":37503,"./index.js":22962,"./it.js":92603,"./ja.js":73302,"./ko.js":66045,"./lv.js":12972,"./nl.js":63969,"./pl.js":44751,"./pt_br.js":69738,"./ro.js":37499,"./ru.js":47429,"./se.js":31622,"./tr.js":38612,"./ua.js":59668,"./ur.js":96635,"./zh_cn.js":50869};function s(e){return t(n(e))}function n(e){if(!t.o(l,e)){var a=Error("Cannot find module '"+e+"'");throw a.code="MODULE_NOT_FOUND",a}return l[e]}s.keys=function(){return Object.keys(l)},s.resolve=n,e.exports=s,s.id=35766},61083:(e,a,t)=>{"use strict";t.d(a,{Z:()=>D});var l=t(10326),s=t(63568),n=t(17577),i=t(52210),r=t(41434),c=t.n(r),o=t(59968),d=t(5248);t(82094),t(50967),t(96940),t(4849);var m=t(20843),u=t(87638),h=t(90943),p=t(78077),x=t(56390),v=t(37841),N=t(54472),b=t(91898),f=t(42341),j=t(77196),g=t(88295),E=t.n(g),y=t(92086),F=t(86184),R=t(47463),Z=t(22304);let D=function({errors:e,touched:a,setFieldValue:t,values:r}){let[g,D]=(0,n.useState)([]),[C,w]=(0,n.useState)([]),{t:A}=(0,i.$G)(),[B,T]=(0,n.useState)(!1),[S,k]=(0,n.useState)(new Date);(0,n.useRef)(null);let[P,$]=(0,n.useState)(null),{user:Y}=(0,Z.Z)(),L=(0,F.jd)();(0,R.Z)().replace(/-/g,"");let M=async(e,a,t,l)=>{if(e instanceof HTMLImageElement){let a=e.src;if(a.startsWith("data:image")){let s=a.split(",")[1],n=a.match(/data:(.*?);base64/)[1],i=atob(s),r=new File([new Blob([new Uint8Array(Array(i.length).fill(0).map((e,a)=>i.charCodeAt(a)))],{type:n})],`image_${Date.now()}.${n.split("/")[1]}`,{type:n});await _(r,l,t,e)}else fetch(a).then(e=>e.blob()).then(a=>{let s=a.type;_(new File([a],`image_${Date.now()}.${s.split("/")[1]}`,{type:s}),l,t,e)}).catch(e=>console.error("Error converting image URL to Blob:",e))}else console.error("File is not an HTMLImageElement.")},_=(e,a,t,l)=>{let s;s=(0,R.Z)().replace(/-/g,"");let n=new FormData;n.append("file",e);let i=e.name.split(".").pop(),r=new Date().getFullYear();L.mutate({resource:"blogs",folder:r.toString(),filename:s,body:{formData:n,t:A}},{onSuccess:e=>{let t="uuid exist"===e.message?e.uuid:`${s}.${i}`,n=`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${t}`;l.src=n,a({result:[{id:t,url:n}]})},onError:e=>{console.error("Error uploading file:",e)}})};return(0,l.jsxs)(l.Fragment,{children:[l.jsx("p",{className:"label-pentabell",children:"Add Event English : "}),(0,l.jsxs)("div",{className:"inline-group",children:[(0,l.jsxs)("div",{children:[" ",l.jsx(u.Z,{children:(0,l.jsxs)(h.Z,{className:"label-form",children:[A("createArticle:title"),l.jsx(p.Z,{variant:"standard",name:"titleEN",type:"text",value:r.titleEN,onChange:e=>{let a=e.target.value;t("titleEN",a),t("urlEN",(0,m.o)(a))},className:"input-pentabell"+(e.titleEN&&a.titleEN?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"titleEN",component:"div"})]})})]}),(0,l.jsxs)("div",{children:[" ",l.jsx(u.Z,{children:(0,l.jsxs)(h.Z,{className:"label-form",children:[A("eventForm:subTitle"),l.jsx(p.Z,{variant:"standard",name:"subTitleEN",type:"text",value:r.subTitleEN,onChange:e=>{let a=e.target.value;t("subTitleEN",a),(0,m.o)(a)},className:"input-pentabell"+(e.subTitleEN&&a.subTitleEN?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"subTitleEN",component:"div"})]})})]})]}),l.jsx("div",{className:"inline-group",children:l.jsx("div",{children:l.jsx(u.Z,{children:(0,l.jsxs)(h.Z,{className:"label-form",children:[A("BreadCrumbs"),l.jsx(p.Z,{variant:"standard",name:"nameEN",type:"text",value:r.nameEN,onChange:e=>{e.target.value,t("nameEN",nameEN),(0,m.o)(nameEN)},className:"input-pentabell"+(e.nameEN&&a.nameEN?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"nameEN",component:"div"})]})})})}),l.jsx("div",{className:"inline-group",children:l.jsx("div",{children:l.jsx(u.Z,{children:(0,l.jsxs)(h.Z,{className:"label-form",children:["Country Concerned",l.jsx(p.Z,{variant:"standard",name:"countryConcernedEN",type:"text",value:r.countryConcernedEN,onChange:e=>{t("countryConcernedEN",e.target.value)},className:"textArea-pentabell"+(e.countryConcernedEN&&a.countryConcernedEN?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"countryConcernedEN",component:"div"})]})})})}),l.jsx("div",{className:"inline-group",children:l.jsx("div",{children:l.jsx(u.Z,{children:(0,l.jsxs)(h.Z,{className:"label-form",children:["Sector",l.jsx(p.Z,{variant:"standard",name:"sectorEN",type:"text",value:r.sectorEN,onChange:e=>{t("sectorEN",e.target.value)},className:"textArea-pentabell"+(e.sectorEN&&a.sectorEN?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"sectorEN",component:"div"})]})})})}),(0,l.jsxs)("div",{children:[" ",l.jsx(u.Z,{children:(0,l.jsxs)(h.Z,{className:"label-form",children:[A("createArticle:Organiser"),l.jsx(p.Z,{variant:"standard",name:"organiserEN",type:"text",value:r.organiserEN,onChange:e=>{t("organiserEN",e.target.value)},className:"input-pentabell"+(e.organiserEN&&a.organiserEN?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"organiserEN",component:"div"})]})})]}),l.jsx(c(),{setContents:r?.contentEN||"",onChange:e=>{t("contentEN",e)},onPaste:(e,a,t)=>a.replace(/<strong>(.*?)$/g,"<strong>$1</strong>"),setOptions:{cleanHTML:!1,disableHtmlSanitizer:!0,addTagsWhitelist:"h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button",plugins:o.default,buttonList:[["undo","redo"],["font","fontSize","formatBlock"],["bold","underline","italic","strike","subscript","superscript"],["fontColor","hiliteColor"],["align","list","lineHeight"],["outdent","indent"],["table","horizontalRule","link","image","video"],["fullScreen","showBlocks","codeView"],["preview","print"],["removeFormat"]],imageUploadHandler:M,defaultTag:"div",minHeight:"300px",maxHeight:"400px",showPathLabel:!1,font:["Proxima-Nova-Regular","Proxima-Nova-Medium","Proxima-Nova-Semibold","Proxima-Nova-Bold","Proxima-Nova-Extrabold","Proxima-Nova-Black","Proxima-Nova-Light","Proxima-Nova-Thin","Arial","Times New Roman","Sans-Serif"],charCounter:!0,charCounterType:"byte",resizingBar:!1,colorList:[["#234791","#d69b19","#cc3233","#009966","#0b3051","#2BBFAD","#0b305100","#0a305214","#743794","#ff0000","#ff5e00","#ffe400","#abf200","#00d8ff","#0055ff","#6600ff","#ff00dd","#000000","#ffd8d8","#fae0d4","#faf4c0","#e4f7ba","#d4f4fa","#d9e5ff","#e8d9ff","#ffd9fa","#f1f1f1","#ffa7a7","#ffc19e","#faed7d","#cef279","#b2ebf4","#b2ccff","#d1b2ff","#ffb2f5","#bdbdbd","#f15f5f","#f29661","#e5d85c","#bce55c","#5cd1e5","#6699ff","#a366ff","#f261df","#8c8c8c","#980000","#993800","#998a00","#6b9900","#008299","#003399","#3d0099","#990085","#353535","#670000","#662500","#665c00","#476600","#005766","#002266","#290066","#660058","#222222"]]},onImageUpload:M}),l.jsx("br",{}),(0,l.jsxs)("div",{className:"inline-group",children:[(0,l.jsxs)("div",{children:[" ",l.jsx(u.Z,{children:(0,l.jsxs)(h.Z,{className:"label-form",children:[A("createArticle:metaTitle")," ("," ",(0,l.jsxs)("span",{className:r.metaTitleEN?.length>65?" text-danger":"",children:[" ",r.metaTitleEN?.length," / 65"," "]})," ",")",l.jsx(p.Z,{variant:"standard",name:"metaTitleEN",type:"text",value:r.metaTitleEN,onChange:e=>{t("metaTitleEN",e.target.value)},className:"input-pentabell"+(e.metaTitleEN&&a.metaTitleEN?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"metaTitleEN",component:"div"})]})})]}),(0,l.jsxs)("div",{children:[" ",l.jsx(u.Z,{children:(0,l.jsxs)(h.Z,{className:"label-form",children:[A("createArticle:url"),l.jsx(p.Z,{variant:"standard",name:"urlEN",type:"text",value:r.urlEN,onChange:e=>{t("urlEN",e.target.value)},className:"input-pentabell"+(e.urlEN&&a.urlEN?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"urlEN",component:"div"})]})})]})]}),l.jsx("div",{className:"inline-group",children:l.jsx("div",{children:l.jsx(u.Z,{children:(0,l.jsxs)(h.Z,{className:"label-form",children:[A("createArticle:metaDescription")," ("," ",(0,l.jsxs)("span",{className:r.metaDescriptionEN?.length>160?" text-danger":"",children:[r.metaDescriptionEN?.length," / 160"]})," ",")",l.jsx(p.Z,{variant:"standard",name:"metaDescriptionEN",type:"text",multiline:!0,rows:3,value:r.metaDescriptionEN,onChange:e=>{t("metaDescriptionEN",e.target.value)},className:"textArea-pentabell"+(e.metaDescriptionEN&&a.metaDescriptionEN?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"metaDescriptionEN",component:"div"})]})})})}),(0,l.jsxs)("div",{className:"inline-group",children:[l.jsx("div",{children:l.jsx(u.Z,{children:(0,l.jsxs)(h.Z,{className:"label-form",children:[A("createArticle:alt"),l.jsx(p.Z,{variant:"standard",name:"altEN",type:"text",value:r.altEN,onChange:e=>{t("altEN",e.target.value)},className:"input-pentabell"+(e.altEN&&a.altEN?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"altEN",component:"div"})]})})}),(0,l.jsxs)("div",{children:[" ",l.jsx(u.Z,{children:(0,l.jsxs)(h.Z,{className:"label-form",children:[A("createArticle:visibility"),l.jsx(x.Z,{className:"select-pentabell",variant:"standard",value:d.EE.filter(e=>r.visibilityEN===e),selected:r.visibilityEN,onChange:e=>{t("visibilityEN",e.target.value)},children:d.EE.map((e,a)=>l.jsx(v.Z,{value:e,children:e},a))}),l.jsx(s.Bc,{className:"label-error",name:"visibilityEN",component:"div"})]})})]}),(0,l.jsxs)("div",{children:[" ",l.jsx(u.Z,{children:(0,l.jsxs)(h.Z,{className:"label-form",children:[A("createArticle:keyword"),l.jsx("div",{id:"tags",children:l.jsx(y.V,{tags:g,className:"input-pentabell"+(e.keywordsEN&&a.keywordsEN?" is-invalid":""),delimiters:[188,13],handleDelete:e=>{let a=g.filter((a,t)=>t!==e);D(a),t("keywordsEN",a.map(e=>e.text))},handleAddition:e=>{D([...g,e]),t("keywordsEN",[...g,e].map(e=>e.text))},inputFieldPosition:"bottom",autocomplete:!0,allowDragDrop:!1})}),l.jsx(s.Bc,{className:"label-error",name:"keywordsEN",component:"div"})]})})]})]}),l.jsx("div",{className:"inline-group",children:(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"label-form",children:[l.jsx(s.gN,{type:"checkbox",name:"publishNow",checked:B,onChange:e=>{T(e.target.checked),e.target.checked&&t("publishDateEN",new Date().toISOString())}}),A("createArticle:publishNow")]}),!B&&l.jsx("div",{children:(0,l.jsxs)(u.Z,{children:[(0,l.jsxs)(h.Z,{className:"label-form",children:[A("createArticle:publishDate"),l.jsx(N._,{dateAdapter:f.y,children:(0,l.jsxs)(j.C,{components:["DatePicker"],children:[l.jsx(b.M,{variant:"standard",className:"input-date",format:"DD/MM/YYYY",value:E()(r.publishDateEN||new Date),onChange:e=>{t("publishDateEN",E()(e).format("YYYY-MM-DD"))}})," "]})})]})," ",l.jsx(s.Bc,{className:"label-error",name:"publishDateEN",component:"div"})]})})]})}),l.jsx(s.gN,{type:"hidden",name:"publishDateEN",value:B?new Date().toISOString():S.toISOString()})]})}},55898:(e,a,t)=>{"use strict";t.d(a,{Z:()=>D});var l=t(10326),s=t(63568),n=t(17577),i=t(52210),r=t(41434),c=t.n(r),o=t(47463),d=t(5248);t(82094);var m=t(20843);t(50967),t(96940),t(4849);var u=t(92086),h=t(59968),p=t(87638),x=t(90943),v=t(78077),N=t(56390),b=t(37841),f=t(54472),j=t(91898),g=t(77196),E=t(88295),y=t.n(E),F=t(42341),R=t(86184),Z=t(22304);let D=function({errors:e,touched:a,setFieldValue:t,values:r,onImageSelect:E,categories:D,filteredCategories:C,onCategoriesSelect:w}){let[A,B]=(0,n.useState)([]),{user:T}=(0,Z.Z)(),{t:S}=(0,i.$G)(),[k,P]=(0,n.useState)(!1),[$,Y]=(0,n.useState)(new Date);(0,n.useRef)(null);let[L,M]=(0,n.useState)(null),_=(0,R.jd)(),H=async(e,a,t,l)=>{if(e instanceof HTMLImageElement){let a=e.src;if(a.startsWith("data:image")){let s=a.split(",")[1],n=a.match(/data:(.*?);base64/)[1],i=atob(s),r=new File([new Blob([new Uint8Array(Array(i.length).fill(0).map((e,a)=>i.charCodeAt(a)))],{type:n})],`image_${Date.now()}.${n.split("/")[1]}`,{type:n});await I(r,l,t,e)}else fetch(a).then(e=>e.blob()).then(a=>{let s=a.type;I(new File([a],`image_${Date.now()}.${s.split("/")[1]}`,{type:s}),l,t,e)}).catch(e=>console.error("Error converting image URL to Blob:",e))}else console.error("File is not an HTMLImageElement.")},I=(e,a,t,l)=>{let s;s=(0,o.Z)().replace(/-/g,"");let n=new FormData;n.append("file",e);let i=e.name.split(".").pop(),r=new Date().getFullYear();_.mutate({resource:"blogs",folder:r.toString(),filename:s,body:{formData:n,t:S}},{onSuccess:e=>{let t="uuid exist"===e.message?e.uuid:`${s}.${i}`,n=`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${t}`;l.src=n,a({result:[{id:t,url:n}]})},onError:e=>{console.error("Error uploading file:",e)}})};return(0,l.jsxs)(l.Fragment,{children:[l.jsx("p",{className:"label-pentabell",children:"Add Event French : "}),(0,l.jsxs)("div",{className:"inline-group",children:[(0,l.jsxs)("div",{children:[" ",l.jsx(p.Z,{children:(0,l.jsxs)(x.Z,{className:"label-form",children:[S("createArticle:title"),l.jsx(v.Z,{variant:"standard",name:"titleFR",type:"text",value:r.titleFR,onChange:e=>{let a=e.target.value;t("titleFR",a),t("urlEN",(0,m.o)(a))},className:"input-pentabell"+(e.titleFR&&a.titleFR?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"titleFR",component:"div"})]})})]}),(0,l.jsxs)("div",{children:[" ",l.jsx(p.Z,{children:(0,l.jsxs)(x.Z,{className:"label-form",children:[S("eventForm:subTitle"),l.jsx(v.Z,{variant:"standard",name:"subTitleFR",type:"text",value:r.subTitleFR,onChange:e=>{let a=e.target.value;t("subTitleFR",a),(0,m.o)(a)},className:"input-pentabell"+(e.subTitleFR&&a.subTitleFR?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"subTitleFR",component:"div"})]})})]})]}),l.jsx("div",{className:"inline-group",children:l.jsx("div",{children:l.jsx(p.Z,{children:(0,l.jsxs)(x.Z,{className:"label-form",children:[S("BreadCrumbs"),l.jsx(v.Z,{variant:"standard",name:"nameFR",type:"text",value:r.nameFR,onChange:e=>{let a=e.target.value;t("nameFR",a),(0,m.o)(a)},className:"input-pentabell"+(e.nameFR&&a.nameFR?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"nameFR",component:"div"})]})})})}),l.jsx("div",{className:"inline-group",children:l.jsx("div",{children:l.jsx(p.Z,{children:(0,l.jsxs)(x.Z,{className:"label-form",children:["Country Concerned",l.jsx(v.Z,{variant:"standard",name:"countryConcernedFR",type:"text",value:r.countryConcernedFR,onChange:e=>{t("countryConcernedFR",e.target.value)},className:"textArea-pentabell"+(e.countryConcernedFR&&a.countryConcernedFR?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"countryConcernedFR",component:"div"})]})})})}),l.jsx("div",{className:"inline-group",children:l.jsx("div",{children:l.jsx(p.Z,{children:(0,l.jsxs)(x.Z,{className:"label-form",children:["Sector",l.jsx(v.Z,{variant:"standard",name:"sectorFR",type:"text",value:r.sectorFR,onChange:e=>{t("sectorFR",e.target.value)},className:"textArea-pentabell"+(e.sectorFR&&a.sectorFR?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"sectorFR",component:"div"})]})})})}),(0,l.jsxs)("div",{children:[" ",l.jsx(p.Z,{children:(0,l.jsxs)(x.Z,{className:"label-form",children:[S("createArticle:Organiser"),l.jsx(v.Z,{variant:"standard",name:"organiserFR",type:"text",value:r.organiserFR,onChange:e=>{t("organiserFR",e.target.value)},className:"input-pentabell"+(e.organiserFR&&a.organiserFR?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"organiserFR",component:"div"})]})})]}),l.jsx(c(),{setContents:r?.contentFR||"",onChange:e=>{t("contentFR",e)},onPaste:(e,a,t)=>a.replace(/<strong>(.*?)$/g,"<strong>$1</strong>"),setOptions:{cleanHTML:!1,disableHtmlSanitizer:!0,addTagsWhitelist:"h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button",plugins:h.default,buttonList:[["undo","redo"],["font","fontSize","formatBlock"],["bold","underline","italic","strike","subscript","superscript"],["fontColor","hiliteColor"],["align","list","lineHeight"],["outdent","indent"],["table","horizontalRule","link","image","video"],["fullScreen","showBlocks","codeView"],["preview","print"],["removeFormat"]],imageUploadHandler:H,defaultTag:"div",minHeight:"300px",maxHeight:"400px",showPathLabel:!1,font:["Proxima-Nova-Regular","Proxima-Nova-Medium","Proxima-Nova-Semibold","Proxima-Nova-Bold","Proxima-Nova-Extrabold","Proxima-Nova-Black","Proxima-Nova-Light","Proxima-Nova-Thin","Arial","Times New Roman","Sans-Serif"],charCounter:!0,charCounterType:"byte",resizingBar:!1,colorList:[["#234791","#d69b19","#cc3233","#009966","#0b3051","#2BBFAD","#0b305100","#0a305214","#743794","#ff0000","#ff5e00","#ffe400","#abf200","#00d8ff","#0055ff","#6600ff","#ff00dd","#000000","#ffd8d8","#fae0d4","#faf4c0","#e4f7ba","#d4f4fa","#d9e5ff","#e8d9ff","#ffd9fa","#f1f1f1","#ffa7a7","#ffc19e","#faed7d","#cef279","#b2ebf4","#b2ccff","#d1b2ff","#ffb2f5","#bdbdbd","#f15f5f","#f29661","#e5d85c","#bce55c","#5cd1e5","#6699ff","#a366ff","#f261df","#8c8c8c","#980000","#993800","#998a00","#6b9900","#008299","#003399","#3d0099","#990085","#353535","#670000","#662500","#665c00","#476600","#005766","#002266","#290066","#660058","#222222"]]},onImageUpload:H}),l.jsx("br",{}),(0,l.jsxs)("div",{className:"inline-group",children:[l.jsx("div",{children:l.jsx(p.Z,{children:(0,l.jsxs)(x.Z,{className:"label-form",children:[S("createArticle:metaTitle")," ("," ",(0,l.jsxs)("span",{className:r.metaTitleFR?.length>65?" text-danger":"",children:[" ",r.metaTitleFR?.length," / 65"," "]})," ",")",l.jsx(v.Z,{variant:"standard",name:"metaTitleFR",type:"text",value:r.metaTitleFR,onChange:e=>{t("metaTitleFR",e.target.value)},className:"input-pentabell"+(e.metaTitleFR&&a.metaTitleFR?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"metaTitleFR",component:"div"})]})})}),l.jsx("div",{children:l.jsx(p.Z,{children:(0,l.jsxs)(x.Z,{className:"label-form",children:[S("createArticle:url"),l.jsx(v.Z,{variant:"standard",name:"urlFR",type:"text",value:r.urlFR,onChange:e=>{t("urlFR",e.target.value)},className:"input-pentabell"+(e.urlFR&&a.urlFR?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"urlFR",component:"div"})]})})})]}),l.jsx("div",{className:"inline-group",children:l.jsx("div",{children:l.jsx(p.Z,{children:(0,l.jsxs)(x.Z,{className:"label-form",children:[S("createArticle:metaDescription")," ("," ",(0,l.jsxs)("span",{className:r.metaDescriptionFR?.length>160?" text-danger":"",children:[r.metaDescriptionFR?.length," / 160"]})," ",")",l.jsx(v.Z,{variant:"standard",name:"metaDescriptionFR",type:"text",multiline:!0,rows:2,value:r.metaDescriptionFR,onChange:e=>{t("metaDescriptionFR",e.target.value)},className:"textArea-pentabell"+(e.metaDescriptionFR&&a.metaDescriptionFR?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"metaDescriptionFR",component:"div"})]})})})}),(0,l.jsxs)("div",{className:"inline-group",children:[l.jsx("div",{children:l.jsx(p.Z,{children:(0,l.jsxs)(x.Z,{className:"label-form",children:[S("createArticle:alt"),l.jsx(v.Z,{variant:"standard",name:"altFR",type:"text",value:r.altFR,onChange:e=>{t("altFR",e.target.value)},className:"input-pentabell"+(e.altFR&&a.altFR?" is-invalid":"")})," ",l.jsx(s.Bc,{className:"label-error",name:"altFR",component:"div"})]})})}),l.jsx("div",{children:l.jsx(p.Z,{children:(0,l.jsxs)(x.Z,{className:"label-form",children:[S("createArticle:visibility"),l.jsx(N.Z,{className:"select-pentabell",variant:"standard",value:d.EE.filter(e=>r.visibilityFR===e),selected:r.visibilityFR,onChange:e=>{t("visibilityFR",e.target.value)},children:d.EE.map((e,a)=>l.jsx(b.Z,{value:e,children:e},a))}),l.jsx(s.Bc,{className:"label-error",name:"visibilityEN",component:"div"})]})})}),l.jsx("div",{children:l.jsx(p.Z,{children:(0,l.jsxs)(x.Z,{className:"label-form",children:[S("createArticle:keyword"),l.jsx("div",{id:"tags",children:l.jsx(u.V,{tags:A,className:"input-pentabell"+(e.keywordsFR&&a.keywordsFR?" is-invalid":""),delimiters:[188,13],handleDelete:e=>{let a=A.filter((a,t)=>t!==e);B(a),t("keywordsFR",a.map(e=>e.text))},handleAddition:e=>{B([...A,e]),t("keywordsFR",[...A,e].map(e=>e.text))},inputFieldPosition:"bottom",autocomplete:!0,allowDragDrop:!1})}),l.jsx(s.Bc,{className:"label-error",name:"keywordsFR",component:"div"})]})})})]}),(0,l.jsxs)("label",{className:"label-form",children:[l.jsx(s.gN,{type:"checkbox",name:"publishNow",checked:k,onChange:e=>{P(e.target.checked),e.target.checked&&t("publishDateFR",new Date().toISOString())}}),S("createArticle:publishNow")]}),!k&&l.jsx("div",{children:l.jsx(p.Z,{children:(0,l.jsxs)(x.Z,{className:"label-form",children:[S("createArticle:publishDate"),l.jsx(f._,{dateAdapter:F.y,children:(0,l.jsxs)(g.C,{components:["DatePicker"],children:[l.jsx(j.M,{variant:"standard",className:"input-date",format:"DD/MM/YYYY",value:y()(r.publishDateEN),onChange:e=>{t("publishDateEN",y()(e).format("YYYY-MM-DD"))}})," ",l.jsx(s.Bc,{className:"label-error",name:"publishDateEN",component:"div"})]})})]})})}),l.jsx(s.gN,{type:"hidden",name:"publishDateFR",value:k?new Date().toISOString():$.toISOString()})]})}},64430:(e,a,t)=>{"use strict";t.d(a,{_x:()=>m,Cr:()=>p,dB:()=>h,ff:()=>u});var l=t(2994);t(97980);var s=t(50967),n=t(70580),i=t(31190);let r=e=>(e.t,new Promise(async(a,t)=>{n.yX.post(s.Y.events,e.data).then(e=>{i.Am.success("Event added successfully"),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&t(e)})})),c=({data:e,id:a})=>new Promise(async(t,l)=>{n.yX.put(`${s.Y.events}/${a}`,e).then(e=>{i.Am.success("Event updated successfully"),e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&l(e)})}),o=e=>new Promise(async(a,t)=>{try{let t=await n.yX.get(`${s.Y.events}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,slug:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,publishDate:e.publishDate,isArchived:e.isArchived}});a(t.data)}catch(e){t(e)}}),d=e=>new Promise(async(a,t)=>{try{let t=await n.yX.get(`${s.Y.events}/${e}`);a(t.data)}catch(e){t(e)}}),m=()=>(0,l.useMutation)({mutationFn:e=>r(e),onError:e=>{e.message=""}}),u=()=>((0,l.useQueryClient)(),(0,l.useMutation)({mutationFn:(e,a)=>c(e,a),onError:e=>{e.message=""}})),h=e=>(0,l.useQuery)(`Events${e.language}`,async()=>await o(e)),p=e=>(0,l.useQuery)(["event",e],async()=>await d(e))}};