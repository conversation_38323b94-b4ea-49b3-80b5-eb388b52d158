"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-tag-input";
exports.ids = ["vendor-chunks/react-tag-input"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-tag-input/dist/index.js":
/*!****************************************************!*\
  !*** ./node_modules/react-tag-input/dist/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KEYS: () => (/* binding */ KEYS),\n/* harmony export */   SEPARATORS: () => (/* binding */ SEPARATORS),\n/* harmony export */   WithContext: () => (/* binding */ WithContext),\n/* harmony export */   WithOutContext: () => (/* binding */ ReactTagsWrapper)\n/* harmony export */ });\n/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-dnd */ \"(ssr)/./node_modules/react-dnd/dist/core/DndProvider.js\");\n/* harmony import */ var react_dnd_html5_backend__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-dnd-html5-backend */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dnd */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDrag.js\");\n/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dnd */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useDrop.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\n\n// node_modules/classnames/index.js\nvar require_classnames = __commonJS({\n  \"node_modules/classnames/index.js\"(exports2, module2) {\n    (function() {\n      \"use strict\";\n      var hasOwn = {}.hasOwnProperty;\n      var nativeCodeString = \"[native code]\";\n      function classNames() {\n        var classes = [];\n        for (var i = 0; i < arguments.length; i++) {\n          var arg = arguments[i];\n          if (!arg) continue;\n          var argType = typeof arg;\n          if (argType === \"string\" || argType === \"number\") {\n            classes.push(arg);\n          } else if (Array.isArray(arg)) {\n            if (arg.length) {\n              var inner = classNames.apply(null, arg);\n              if (inner) {\n                classes.push(inner);\n              }\n            }\n          } else if (argType === \"object\") {\n            if (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes(\"[native code]\")) {\n              classes.push(arg.toString());\n              continue;\n            }\n            for (var key in arg) {\n              if (hasOwn.call(arg, key) && arg[key]) {\n                classes.push(key);\n              }\n            }\n          }\n        }\n        return classes.join(\" \");\n      }\n      if (typeof module2 !== \"undefined\" && module2.exports) {\n        classNames.default = classNames;\n        module2.exports = classNames;\n      } else if (typeof define === \"function\" && typeof define.amd === \"object\" && define.amd) {\n        define(\"classnames\", [], function() {\n          return classNames;\n        });\n      } else {\n        window.classNames = classNames;\n      }\n    })();\n  }\n});\n\n// src/index.tsx\n\n\n\n// src/components/SingleTag.tsx\nvar import_classnames = __toESM(require_classnames(), 1);\n\n\n\n// node_modules/lodash-es/_freeGlobal.js\nvar freeGlobal = typeof global == \"object\" && global && global.Object === Object && global;\nvar freeGlobal_default = freeGlobal;\n\n// node_modules/lodash-es/_root.js\nvar freeSelf = typeof self == \"object\" && self && self.Object === Object && self;\nvar root = freeGlobal_default || freeSelf || Function(\"return this\")();\nvar root_default = root;\n\n// node_modules/lodash-es/_Symbol.js\nvar Symbol2 = root_default.Symbol;\nvar Symbol_default = Symbol2;\n\n// node_modules/lodash-es/_getRawTag.js\nvar objectProto = Object.prototype;\nvar hasOwnProperty = objectProto.hasOwnProperty;\nvar nativeObjectToString = objectProto.toString;\nvar symToStringTag = Symbol_default ? Symbol_default.toStringTag : void 0;\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag), tag = value[symToStringTag];\n  try {\n    value[symToStringTag] = void 0;\n    var unmasked = true;\n  } catch (e) {\n  }\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\nvar getRawTag_default = getRawTag;\n\n// node_modules/lodash-es/_objectToString.js\nvar objectProto2 = Object.prototype;\nvar nativeObjectToString2 = objectProto2.toString;\nfunction objectToString(value) {\n  return nativeObjectToString2.call(value);\n}\nvar objectToString_default = objectToString;\n\n// node_modules/lodash-es/_baseGetTag.js\nvar nullTag = \"[object Null]\";\nvar undefinedTag = \"[object Undefined]\";\nvar symToStringTag2 = Symbol_default ? Symbol_default.toStringTag : void 0;\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === void 0 ? undefinedTag : nullTag;\n  }\n  return symToStringTag2 && symToStringTag2 in Object(value) ? getRawTag_default(value) : objectToString_default(value);\n}\nvar baseGetTag_default = baseGetTag;\n\n// node_modules/lodash-es/isObjectLike.js\nfunction isObjectLike(value) {\n  return value != null && typeof value == \"object\";\n}\nvar isObjectLike_default = isObjectLike;\n\n// node_modules/lodash-es/isSymbol.js\nvar symbolTag = \"[object Symbol]\";\nfunction isSymbol(value) {\n  return typeof value == \"symbol\" || isObjectLike_default(value) && baseGetTag_default(value) == symbolTag;\n}\nvar isSymbol_default = isSymbol;\n\n// node_modules/lodash-es/_arrayMap.js\nfunction arrayMap(array, iteratee) {\n  var index = -1, length = array == null ? 0 : array.length, result = Array(length);\n  while (++index < length) {\n    result[index] = iteratee(array[index], index, array);\n  }\n  return result;\n}\nvar arrayMap_default = arrayMap;\n\n// node_modules/lodash-es/isArray.js\nvar isArray = Array.isArray;\nvar isArray_default = isArray;\n\n// node_modules/lodash-es/_baseToString.js\nvar INFINITY = 1 / 0;\nvar symbolProto = Symbol_default ? Symbol_default.prototype : void 0;\nvar symbolToString = symbolProto ? symbolProto.toString : void 0;\nfunction baseToString(value) {\n  if (typeof value == \"string\") {\n    return value;\n  }\n  if (isArray_default(value)) {\n    return arrayMap_default(value, baseToString) + \"\";\n  }\n  if (isSymbol_default(value)) {\n    return symbolToString ? symbolToString.call(value) : \"\";\n  }\n  var result = value + \"\";\n  return result == \"0\" && 1 / value == -INFINITY ? \"-0\" : result;\n}\nvar baseToString_default = baseToString;\n\n// node_modules/lodash-es/isObject.js\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == \"object\" || type == \"function\");\n}\nvar isObject_default = isObject;\n\n// node_modules/lodash-es/isFunction.js\nvar asyncTag = \"[object AsyncFunction]\";\nvar funcTag = \"[object Function]\";\nvar genTag = \"[object GeneratorFunction]\";\nvar proxyTag = \"[object Proxy]\";\nfunction isFunction(value) {\n  if (!isObject_default(value)) {\n    return false;\n  }\n  var tag = baseGetTag_default(value);\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\nvar isFunction_default = isFunction;\n\n// node_modules/lodash-es/_coreJsData.js\nvar coreJsData = root_default[\"__core-js_shared__\"];\nvar coreJsData_default = coreJsData;\n\n// node_modules/lodash-es/_isMasked.js\nvar maskSrcKey = function() {\n  var uid = /[^.]+$/.exec(coreJsData_default && coreJsData_default.keys && coreJsData_default.keys.IE_PROTO || \"\");\n  return uid ? \"Symbol(src)_1.\" + uid : \"\";\n}();\nfunction isMasked(func) {\n  return !!maskSrcKey && maskSrcKey in func;\n}\nvar isMasked_default = isMasked;\n\n// node_modules/lodash-es/_toSource.js\nvar funcProto = Function.prototype;\nvar funcToString = funcProto.toString;\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {\n    }\n    try {\n      return func + \"\";\n    } catch (e) {\n    }\n  }\n  return \"\";\n}\nvar toSource_default = toSource;\n\n// node_modules/lodash-es/_baseIsNative.js\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\nvar funcProto2 = Function.prototype;\nvar objectProto3 = Object.prototype;\nvar funcToString2 = funcProto2.toString;\nvar hasOwnProperty2 = objectProto3.hasOwnProperty;\nvar reIsNative = RegExp(\n  \"^\" + funcToString2.call(hasOwnProperty2).replace(reRegExpChar, \"\\\\$&\").replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, \"$1.*?\") + \"$\"\n);\nfunction baseIsNative(value) {\n  if (!isObject_default(value) || isMasked_default(value)) {\n    return false;\n  }\n  var pattern = isFunction_default(value) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource_default(value));\n}\nvar baseIsNative_default = baseIsNative;\n\n// node_modules/lodash-es/_getValue.js\nfunction getValue(object, key) {\n  return object == null ? void 0 : object[key];\n}\nvar getValue_default = getValue;\n\n// node_modules/lodash-es/_getNative.js\nfunction getNative(object, key) {\n  var value = getValue_default(object, key);\n  return baseIsNative_default(value) ? value : void 0;\n}\nvar getNative_default = getNative;\n\n// node_modules/lodash-es/_WeakMap.js\nvar WeakMap = getNative_default(root_default, \"WeakMap\");\nvar WeakMap_default = WeakMap;\n\n// node_modules/lodash-es/noop.js\nfunction noop() {\n}\nvar noop_default = noop;\n\n// node_modules/lodash-es/_baseFindIndex.js\nfunction baseFindIndex(array, predicate, fromIndex, fromRight) {\n  var length = array.length, index = fromIndex + (fromRight ? 1 : -1);\n  while (fromRight ? index-- : ++index < length) {\n    if (predicate(array[index], index, array)) {\n      return index;\n    }\n  }\n  return -1;\n}\nvar baseFindIndex_default = baseFindIndex;\n\n// node_modules/lodash-es/_baseIsNaN.js\nfunction baseIsNaN(value) {\n  return value !== value;\n}\nvar baseIsNaN_default = baseIsNaN;\n\n// node_modules/lodash-es/_strictIndexOf.js\nfunction strictIndexOf(array, value, fromIndex) {\n  var index = fromIndex - 1, length = array.length;\n  while (++index < length) {\n    if (array[index] === value) {\n      return index;\n    }\n  }\n  return -1;\n}\nvar strictIndexOf_default = strictIndexOf;\n\n// node_modules/lodash-es/_baseIndexOf.js\nfunction baseIndexOf(array, value, fromIndex) {\n  return value === value ? strictIndexOf_default(array, value, fromIndex) : baseFindIndex_default(array, baseIsNaN_default, fromIndex);\n}\nvar baseIndexOf_default = baseIndexOf;\n\n// node_modules/lodash-es/_arrayIncludes.js\nfunction arrayIncludes(array, value) {\n  var length = array == null ? 0 : array.length;\n  return !!length && baseIndexOf_default(array, value, 0) > -1;\n}\nvar arrayIncludes_default = arrayIncludes;\n\n// node_modules/lodash-es/_isIndex.js\nvar MAX_SAFE_INTEGER = 9007199254740991;\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\nfunction isIndex(value, length) {\n  var type = typeof value;\n  length = length == null ? MAX_SAFE_INTEGER : length;\n  return !!length && (type == \"number\" || type != \"symbol\" && reIsUint.test(value)) && (value > -1 && value % 1 == 0 && value < length);\n}\nvar isIndex_default = isIndex;\n\n// node_modules/lodash-es/eq.js\nfunction eq(value, other) {\n  return value === other || value !== value && other !== other;\n}\nvar eq_default = eq;\n\n// node_modules/lodash-es/isLength.js\nvar MAX_SAFE_INTEGER2 = 9007199254740991;\nfunction isLength(value) {\n  return typeof value == \"number\" && value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER2;\n}\nvar isLength_default = isLength;\n\n// node_modules/lodash-es/isArrayLike.js\nfunction isArrayLike(value) {\n  return value != null && isLength_default(value.length) && !isFunction_default(value);\n}\nvar isArrayLike_default = isArrayLike;\n\n// node_modules/lodash-es/_isPrototype.js\nvar objectProto4 = Object.prototype;\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor, proto = typeof Ctor == \"function\" && Ctor.prototype || objectProto4;\n  return value === proto;\n}\nvar isPrototype_default = isPrototype;\n\n// node_modules/lodash-es/_baseTimes.js\nfunction baseTimes(n, iteratee) {\n  var index = -1, result = Array(n);\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n  return result;\n}\nvar baseTimes_default = baseTimes;\n\n// node_modules/lodash-es/_baseIsArguments.js\nvar argsTag = \"[object Arguments]\";\nfunction baseIsArguments(value) {\n  return isObjectLike_default(value) && baseGetTag_default(value) == argsTag;\n}\nvar baseIsArguments_default = baseIsArguments;\n\n// node_modules/lodash-es/isArguments.js\nvar objectProto5 = Object.prototype;\nvar hasOwnProperty3 = objectProto5.hasOwnProperty;\nvar propertyIsEnumerable = objectProto5.propertyIsEnumerable;\nvar isArguments = baseIsArguments_default(/* @__PURE__ */ function() {\n  return arguments;\n}()) ? baseIsArguments_default : function(value) {\n  return isObjectLike_default(value) && hasOwnProperty3.call(value, \"callee\") && !propertyIsEnumerable.call(value, \"callee\");\n};\nvar isArguments_default = isArguments;\n\n// node_modules/lodash-es/stubFalse.js\nfunction stubFalse() {\n  return false;\n}\nvar stubFalse_default = stubFalse;\n\n// node_modules/lodash-es/isBuffer.js\nvar freeExports = typeof exports == \"object\" && exports && !exports.nodeType && exports;\nvar freeModule = freeExports && typeof module == \"object\" && module && !module.nodeType && module;\nvar moduleExports = freeModule && freeModule.exports === freeExports;\nvar Buffer2 = moduleExports ? root_default.Buffer : void 0;\nvar nativeIsBuffer = Buffer2 ? Buffer2.isBuffer : void 0;\nvar isBuffer = nativeIsBuffer || stubFalse_default;\nvar isBuffer_default = isBuffer;\n\n// node_modules/lodash-es/_baseIsTypedArray.js\nvar argsTag2 = \"[object Arguments]\";\nvar arrayTag = \"[object Array]\";\nvar boolTag = \"[object Boolean]\";\nvar dateTag = \"[object Date]\";\nvar errorTag = \"[object Error]\";\nvar funcTag2 = \"[object Function]\";\nvar mapTag = \"[object Map]\";\nvar numberTag = \"[object Number]\";\nvar objectTag = \"[object Object]\";\nvar regexpTag = \"[object RegExp]\";\nvar setTag = \"[object Set]\";\nvar stringTag = \"[object String]\";\nvar weakMapTag = \"[object WeakMap]\";\nvar arrayBufferTag = \"[object ArrayBuffer]\";\nvar dataViewTag = \"[object DataView]\";\nvar float32Tag = \"[object Float32Array]\";\nvar float64Tag = \"[object Float64Array]\";\nvar int8Tag = \"[object Int8Array]\";\nvar int16Tag = \"[object Int16Array]\";\nvar int32Tag = \"[object Int32Array]\";\nvar uint8Tag = \"[object Uint8Array]\";\nvar uint8ClampedTag = \"[object Uint8ClampedArray]\";\nvar uint16Tag = \"[object Uint16Array]\";\nvar uint32Tag = \"[object Uint32Array]\";\nvar typedArrayTags = {};\ntypedArrayTags[float32Tag] = typedArrayTags[float64Tag] = typedArrayTags[int8Tag] = typedArrayTags[int16Tag] = typedArrayTags[int32Tag] = typedArrayTags[uint8Tag] = typedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] = typedArrayTags[uint32Tag] = true;\ntypedArrayTags[argsTag2] = typedArrayTags[arrayTag] = typedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] = typedArrayTags[dataViewTag] = typedArrayTags[dateTag] = typedArrayTags[errorTag] = typedArrayTags[funcTag2] = typedArrayTags[mapTag] = typedArrayTags[numberTag] = typedArrayTags[objectTag] = typedArrayTags[regexpTag] = typedArrayTags[setTag] = typedArrayTags[stringTag] = typedArrayTags[weakMapTag] = false;\nfunction baseIsTypedArray(value) {\n  return isObjectLike_default(value) && isLength_default(value.length) && !!typedArrayTags[baseGetTag_default(value)];\n}\nvar baseIsTypedArray_default = baseIsTypedArray;\n\n// node_modules/lodash-es/_baseUnary.js\nfunction baseUnary(func) {\n  return function(value) {\n    return func(value);\n  };\n}\nvar baseUnary_default = baseUnary;\n\n// node_modules/lodash-es/_nodeUtil.js\nvar freeExports2 = typeof exports == \"object\" && exports && !exports.nodeType && exports;\nvar freeModule2 = freeExports2 && typeof module == \"object\" && module && !module.nodeType && module;\nvar moduleExports2 = freeModule2 && freeModule2.exports === freeExports2;\nvar freeProcess = moduleExports2 && freeGlobal_default.process;\nvar nodeUtil = function() {\n  try {\n    var types = freeModule2 && freeModule2.require && freeModule2.require(\"util\").types;\n    if (types) {\n      return types;\n    }\n    return freeProcess && freeProcess.binding && freeProcess.binding(\"util\");\n  } catch (e) {\n  }\n}();\nvar nodeUtil_default = nodeUtil;\n\n// node_modules/lodash-es/isTypedArray.js\nvar nodeIsTypedArray = nodeUtil_default && nodeUtil_default.isTypedArray;\nvar isTypedArray = nodeIsTypedArray ? baseUnary_default(nodeIsTypedArray) : baseIsTypedArray_default;\nvar isTypedArray_default = isTypedArray;\n\n// node_modules/lodash-es/_arrayLikeKeys.js\nvar objectProto6 = Object.prototype;\nvar hasOwnProperty4 = objectProto6.hasOwnProperty;\nfunction arrayLikeKeys(value, inherited) {\n  var isArr = isArray_default(value), isArg = !isArr && isArguments_default(value), isBuff = !isArr && !isArg && isBuffer_default(value), isType = !isArr && !isArg && !isBuff && isTypedArray_default(value), skipIndexes = isArr || isArg || isBuff || isType, result = skipIndexes ? baseTimes_default(value.length, String) : [], length = result.length;\n  for (var key in value) {\n    if ((inherited || hasOwnProperty4.call(value, key)) && !(skipIndexes && // Safari 9 has enumerable `arguments.length` in strict mode.\n    (key == \"length\" || // Node.js 0.10 has enumerable non-index properties on buffers.\n    isBuff && (key == \"offset\" || key == \"parent\") || // PhantomJS 2 has enumerable non-index properties on typed arrays.\n    isType && (key == \"buffer\" || key == \"byteLength\" || key == \"byteOffset\") || // Skip index properties.\n    isIndex_default(key, length)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\nvar arrayLikeKeys_default = arrayLikeKeys;\n\n// node_modules/lodash-es/_overArg.js\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\nvar overArg_default = overArg;\n\n// node_modules/lodash-es/_nativeKeys.js\nvar nativeKeys = overArg_default(Object.keys, Object);\nvar nativeKeys_default = nativeKeys;\n\n// node_modules/lodash-es/_baseKeys.js\nvar objectProto7 = Object.prototype;\nvar hasOwnProperty5 = objectProto7.hasOwnProperty;\nfunction baseKeys(object) {\n  if (!isPrototype_default(object)) {\n    return nativeKeys_default(object);\n  }\n  var result = [];\n  for (var key in Object(object)) {\n    if (hasOwnProperty5.call(object, key) && key != \"constructor\") {\n      result.push(key);\n    }\n  }\n  return result;\n}\nvar baseKeys_default = baseKeys;\n\n// node_modules/lodash-es/keys.js\nfunction keys(object) {\n  return isArrayLike_default(object) ? arrayLikeKeys_default(object) : baseKeys_default(object);\n}\nvar keys_default = keys;\n\n// node_modules/lodash-es/_nativeCreate.js\nvar nativeCreate = getNative_default(Object, \"create\");\nvar nativeCreate_default = nativeCreate;\n\n// node_modules/lodash-es/_hashClear.js\nfunction hashClear() {\n  this.__data__ = nativeCreate_default ? nativeCreate_default(null) : {};\n  this.size = 0;\n}\nvar hashClear_default = hashClear;\n\n// node_modules/lodash-es/_hashDelete.js\nfunction hashDelete(key) {\n  var result = this.has(key) && delete this.__data__[key];\n  this.size -= result ? 1 : 0;\n  return result;\n}\nvar hashDelete_default = hashDelete;\n\n// node_modules/lodash-es/_hashGet.js\nvar HASH_UNDEFINED = \"__lodash_hash_undefined__\";\nvar objectProto8 = Object.prototype;\nvar hasOwnProperty6 = objectProto8.hasOwnProperty;\nfunction hashGet(key) {\n  var data = this.__data__;\n  if (nativeCreate_default) {\n    var result = data[key];\n    return result === HASH_UNDEFINED ? void 0 : result;\n  }\n  return hasOwnProperty6.call(data, key) ? data[key] : void 0;\n}\nvar hashGet_default = hashGet;\n\n// node_modules/lodash-es/_hashHas.js\nvar objectProto9 = Object.prototype;\nvar hasOwnProperty7 = objectProto9.hasOwnProperty;\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate_default ? data[key] !== void 0 : hasOwnProperty7.call(data, key);\n}\nvar hashHas_default = hashHas;\n\n// node_modules/lodash-es/_hashSet.js\nvar HASH_UNDEFINED2 = \"__lodash_hash_undefined__\";\nfunction hashSet(key, value) {\n  var data = this.__data__;\n  this.size += this.has(key) ? 0 : 1;\n  data[key] = nativeCreate_default && value === void 0 ? HASH_UNDEFINED2 : value;\n  return this;\n}\nvar hashSet_default = hashSet;\n\n// node_modules/lodash-es/_Hash.js\nfunction Hash(entries) {\n  var index = -1, length = entries == null ? 0 : entries.length;\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\nHash.prototype.clear = hashClear_default;\nHash.prototype[\"delete\"] = hashDelete_default;\nHash.prototype.get = hashGet_default;\nHash.prototype.has = hashHas_default;\nHash.prototype.set = hashSet_default;\nvar Hash_default = Hash;\n\n// node_modules/lodash-es/_listCacheClear.js\nfunction listCacheClear() {\n  this.__data__ = [];\n  this.size = 0;\n}\nvar listCacheClear_default = listCacheClear;\n\n// node_modules/lodash-es/_assocIndexOf.js\nfunction assocIndexOf(array, key) {\n  var length = array.length;\n  while (length--) {\n    if (eq_default(array[length][0], key)) {\n      return length;\n    }\n  }\n  return -1;\n}\nvar assocIndexOf_default = assocIndexOf;\n\n// node_modules/lodash-es/_listCacheDelete.js\nvar arrayProto = Array.prototype;\nvar splice = arrayProto.splice;\nfunction listCacheDelete(key) {\n  var data = this.__data__, index = assocIndexOf_default(data, key);\n  if (index < 0) {\n    return false;\n  }\n  var lastIndex = data.length - 1;\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n  --this.size;\n  return true;\n}\nvar listCacheDelete_default = listCacheDelete;\n\n// node_modules/lodash-es/_listCacheGet.js\nfunction listCacheGet(key) {\n  var data = this.__data__, index = assocIndexOf_default(data, key);\n  return index < 0 ? void 0 : data[index][1];\n}\nvar listCacheGet_default = listCacheGet;\n\n// node_modules/lodash-es/_listCacheHas.js\nfunction listCacheHas(key) {\n  return assocIndexOf_default(this.__data__, key) > -1;\n}\nvar listCacheHas_default = listCacheHas;\n\n// node_modules/lodash-es/_listCacheSet.js\nfunction listCacheSet(key, value) {\n  var data = this.__data__, index = assocIndexOf_default(data, key);\n  if (index < 0) {\n    ++this.size;\n    data.push([key, value]);\n  } else {\n    data[index][1] = value;\n  }\n  return this;\n}\nvar listCacheSet_default = listCacheSet;\n\n// node_modules/lodash-es/_ListCache.js\nfunction ListCache(entries) {\n  var index = -1, length = entries == null ? 0 : entries.length;\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\nListCache.prototype.clear = listCacheClear_default;\nListCache.prototype[\"delete\"] = listCacheDelete_default;\nListCache.prototype.get = listCacheGet_default;\nListCache.prototype.has = listCacheHas_default;\nListCache.prototype.set = listCacheSet_default;\nvar ListCache_default = ListCache;\n\n// node_modules/lodash-es/_Map.js\nvar Map = getNative_default(root_default, \"Map\");\nvar Map_default = Map;\n\n// node_modules/lodash-es/_mapCacheClear.js\nfunction mapCacheClear() {\n  this.size = 0;\n  this.__data__ = {\n    \"hash\": new Hash_default(),\n    \"map\": new (Map_default || ListCache_default)(),\n    \"string\": new Hash_default()\n  };\n}\nvar mapCacheClear_default = mapCacheClear;\n\n// node_modules/lodash-es/_isKeyable.js\nfunction isKeyable(value) {\n  var type = typeof value;\n  return type == \"string\" || type == \"number\" || type == \"symbol\" || type == \"boolean\" ? value !== \"__proto__\" : value === null;\n}\nvar isKeyable_default = isKeyable;\n\n// node_modules/lodash-es/_getMapData.js\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable_default(key) ? data[typeof key == \"string\" ? \"string\" : \"hash\"] : data.map;\n}\nvar getMapData_default = getMapData;\n\n// node_modules/lodash-es/_mapCacheDelete.js\nfunction mapCacheDelete(key) {\n  var result = getMapData_default(this, key)[\"delete\"](key);\n  this.size -= result ? 1 : 0;\n  return result;\n}\nvar mapCacheDelete_default = mapCacheDelete;\n\n// node_modules/lodash-es/_mapCacheGet.js\nfunction mapCacheGet(key) {\n  return getMapData_default(this, key).get(key);\n}\nvar mapCacheGet_default = mapCacheGet;\n\n// node_modules/lodash-es/_mapCacheHas.js\nfunction mapCacheHas(key) {\n  return getMapData_default(this, key).has(key);\n}\nvar mapCacheHas_default = mapCacheHas;\n\n// node_modules/lodash-es/_mapCacheSet.js\nfunction mapCacheSet(key, value) {\n  var data = getMapData_default(this, key), size = data.size;\n  data.set(key, value);\n  this.size += data.size == size ? 0 : 1;\n  return this;\n}\nvar mapCacheSet_default = mapCacheSet;\n\n// node_modules/lodash-es/_MapCache.js\nfunction MapCache(entries) {\n  var index = -1, length = entries == null ? 0 : entries.length;\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\nMapCache.prototype.clear = mapCacheClear_default;\nMapCache.prototype[\"delete\"] = mapCacheDelete_default;\nMapCache.prototype.get = mapCacheGet_default;\nMapCache.prototype.has = mapCacheHas_default;\nMapCache.prototype.set = mapCacheSet_default;\nvar MapCache_default = MapCache;\n\n// node_modules/lodash-es/toString.js\nfunction toString(value) {\n  return value == null ? \"\" : baseToString_default(value);\n}\nvar toString_default = toString;\n\n// node_modules/lodash-es/_arrayPush.js\nfunction arrayPush(array, values) {\n  var index = -1, length = values.length, offset = array.length;\n  while (++index < length) {\n    array[offset + index] = values[index];\n  }\n  return array;\n}\nvar arrayPush_default = arrayPush;\n\n// node_modules/lodash-es/_basePropertyOf.js\nfunction basePropertyOf(object) {\n  return function(key) {\n    return object == null ? void 0 : object[key];\n  };\n}\nvar basePropertyOf_default = basePropertyOf;\n\n// node_modules/lodash-es/_stackClear.js\nfunction stackClear() {\n  this.__data__ = new ListCache_default();\n  this.size = 0;\n}\nvar stackClear_default = stackClear;\n\n// node_modules/lodash-es/_stackDelete.js\nfunction stackDelete(key) {\n  var data = this.__data__, result = data[\"delete\"](key);\n  this.size = data.size;\n  return result;\n}\nvar stackDelete_default = stackDelete;\n\n// node_modules/lodash-es/_stackGet.js\nfunction stackGet(key) {\n  return this.__data__.get(key);\n}\nvar stackGet_default = stackGet;\n\n// node_modules/lodash-es/_stackHas.js\nfunction stackHas(key) {\n  return this.__data__.has(key);\n}\nvar stackHas_default = stackHas;\n\n// node_modules/lodash-es/_stackSet.js\nvar LARGE_ARRAY_SIZE = 200;\nfunction stackSet(key, value) {\n  var data = this.__data__;\n  if (data instanceof ListCache_default) {\n    var pairs = data.__data__;\n    if (!Map_default || pairs.length < LARGE_ARRAY_SIZE - 1) {\n      pairs.push([key, value]);\n      this.size = ++data.size;\n      return this;\n    }\n    data = this.__data__ = new MapCache_default(pairs);\n  }\n  data.set(key, value);\n  this.size = data.size;\n  return this;\n}\nvar stackSet_default = stackSet;\n\n// node_modules/lodash-es/_Stack.js\nfunction Stack(entries) {\n  var data = this.__data__ = new ListCache_default(entries);\n  this.size = data.size;\n}\nStack.prototype.clear = stackClear_default;\nStack.prototype[\"delete\"] = stackDelete_default;\nStack.prototype.get = stackGet_default;\nStack.prototype.has = stackHas_default;\nStack.prototype.set = stackSet_default;\nvar Stack_default = Stack;\n\n// node_modules/lodash-es/_arrayFilter.js\nfunction arrayFilter(array, predicate) {\n  var index = -1, length = array == null ? 0 : array.length, resIndex = 0, result = [];\n  while (++index < length) {\n    var value = array[index];\n    if (predicate(value, index, array)) {\n      result[resIndex++] = value;\n    }\n  }\n  return result;\n}\nvar arrayFilter_default = arrayFilter;\n\n// node_modules/lodash-es/stubArray.js\nfunction stubArray() {\n  return [];\n}\nvar stubArray_default = stubArray;\n\n// node_modules/lodash-es/_getSymbols.js\nvar objectProto10 = Object.prototype;\nvar propertyIsEnumerable2 = objectProto10.propertyIsEnumerable;\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\nvar getSymbols = !nativeGetSymbols ? stubArray_default : function(object) {\n  if (object == null) {\n    return [];\n  }\n  object = Object(object);\n  return arrayFilter_default(nativeGetSymbols(object), function(symbol) {\n    return propertyIsEnumerable2.call(object, symbol);\n  });\n};\nvar getSymbols_default = getSymbols;\n\n// node_modules/lodash-es/_baseGetAllKeys.js\nfunction baseGetAllKeys(object, keysFunc, symbolsFunc) {\n  var result = keysFunc(object);\n  return isArray_default(object) ? result : arrayPush_default(result, symbolsFunc(object));\n}\nvar baseGetAllKeys_default = baseGetAllKeys;\n\n// node_modules/lodash-es/_getAllKeys.js\nfunction getAllKeys(object) {\n  return baseGetAllKeys_default(object, keys_default, getSymbols_default);\n}\nvar getAllKeys_default = getAllKeys;\n\n// node_modules/lodash-es/_DataView.js\nvar DataView = getNative_default(root_default, \"DataView\");\nvar DataView_default = DataView;\n\n// node_modules/lodash-es/_Promise.js\nvar Promise2 = getNative_default(root_default, \"Promise\");\nvar Promise_default = Promise2;\n\n// node_modules/lodash-es/_Set.js\nvar Set = getNative_default(root_default, \"Set\");\nvar Set_default = Set;\n\n// node_modules/lodash-es/_getTag.js\nvar mapTag2 = \"[object Map]\";\nvar objectTag2 = \"[object Object]\";\nvar promiseTag = \"[object Promise]\";\nvar setTag2 = \"[object Set]\";\nvar weakMapTag2 = \"[object WeakMap]\";\nvar dataViewTag2 = \"[object DataView]\";\nvar dataViewCtorString = toSource_default(DataView_default);\nvar mapCtorString = toSource_default(Map_default);\nvar promiseCtorString = toSource_default(Promise_default);\nvar setCtorString = toSource_default(Set_default);\nvar weakMapCtorString = toSource_default(WeakMap_default);\nvar getTag = baseGetTag_default;\nif (DataView_default && getTag(new DataView_default(new ArrayBuffer(1))) != dataViewTag2 || Map_default && getTag(new Map_default()) != mapTag2 || Promise_default && getTag(Promise_default.resolve()) != promiseTag || Set_default && getTag(new Set_default()) != setTag2 || WeakMap_default && getTag(new WeakMap_default()) != weakMapTag2) {\n  getTag = function(value) {\n    var result = baseGetTag_default(value), Ctor = result == objectTag2 ? value.constructor : void 0, ctorString = Ctor ? toSource_default(Ctor) : \"\";\n    if (ctorString) {\n      switch (ctorString) {\n        case dataViewCtorString:\n          return dataViewTag2;\n        case mapCtorString:\n          return mapTag2;\n        case promiseCtorString:\n          return promiseTag;\n        case setCtorString:\n          return setTag2;\n        case weakMapCtorString:\n          return weakMapTag2;\n      }\n    }\n    return result;\n  };\n}\nvar getTag_default = getTag;\n\n// node_modules/lodash-es/_Uint8Array.js\nvar Uint8Array2 = root_default.Uint8Array;\nvar Uint8Array_default = Uint8Array2;\n\n// node_modules/lodash-es/_setCacheAdd.js\nvar HASH_UNDEFINED3 = \"__lodash_hash_undefined__\";\nfunction setCacheAdd(value) {\n  this.__data__.set(value, HASH_UNDEFINED3);\n  return this;\n}\nvar setCacheAdd_default = setCacheAdd;\n\n// node_modules/lodash-es/_setCacheHas.js\nfunction setCacheHas(value) {\n  return this.__data__.has(value);\n}\nvar setCacheHas_default = setCacheHas;\n\n// node_modules/lodash-es/_SetCache.js\nfunction SetCache(values) {\n  var index = -1, length = values == null ? 0 : values.length;\n  this.__data__ = new MapCache_default();\n  while (++index < length) {\n    this.add(values[index]);\n  }\n}\nSetCache.prototype.add = SetCache.prototype.push = setCacheAdd_default;\nSetCache.prototype.has = setCacheHas_default;\nvar SetCache_default = SetCache;\n\n// node_modules/lodash-es/_arraySome.js\nfunction arraySome(array, predicate) {\n  var index = -1, length = array == null ? 0 : array.length;\n  while (++index < length) {\n    if (predicate(array[index], index, array)) {\n      return true;\n    }\n  }\n  return false;\n}\nvar arraySome_default = arraySome;\n\n// node_modules/lodash-es/_cacheHas.js\nfunction cacheHas(cache, key) {\n  return cache.has(key);\n}\nvar cacheHas_default = cacheHas;\n\n// node_modules/lodash-es/_equalArrays.js\nvar COMPARE_PARTIAL_FLAG = 1;\nvar COMPARE_UNORDERED_FLAG = 2;\nfunction equalArrays(array, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG, arrLength = array.length, othLength = other.length;\n  if (arrLength != othLength && !(isPartial && othLength > arrLength)) {\n    return false;\n  }\n  var arrStacked = stack.get(array);\n  var othStacked = stack.get(other);\n  if (arrStacked && othStacked) {\n    return arrStacked == other && othStacked == array;\n  }\n  var index = -1, result = true, seen = bitmask & COMPARE_UNORDERED_FLAG ? new SetCache_default() : void 0;\n  stack.set(array, other);\n  stack.set(other, array);\n  while (++index < arrLength) {\n    var arrValue = array[index], othValue = other[index];\n    if (customizer) {\n      var compared = isPartial ? customizer(othValue, arrValue, index, other, array, stack) : customizer(arrValue, othValue, index, array, other, stack);\n    }\n    if (compared !== void 0) {\n      if (compared) {\n        continue;\n      }\n      result = false;\n      break;\n    }\n    if (seen) {\n      if (!arraySome_default(other, function(othValue2, othIndex) {\n        if (!cacheHas_default(seen, othIndex) && (arrValue === othValue2 || equalFunc(arrValue, othValue2, bitmask, customizer, stack))) {\n          return seen.push(othIndex);\n        }\n      })) {\n        result = false;\n        break;\n      }\n    } else if (!(arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n      result = false;\n      break;\n    }\n  }\n  stack[\"delete\"](array);\n  stack[\"delete\"](other);\n  return result;\n}\nvar equalArrays_default = equalArrays;\n\n// node_modules/lodash-es/_mapToArray.js\nfunction mapToArray(map) {\n  var index = -1, result = Array(map.size);\n  map.forEach(function(value, key) {\n    result[++index] = [key, value];\n  });\n  return result;\n}\nvar mapToArray_default = mapToArray;\n\n// node_modules/lodash-es/_setToArray.js\nfunction setToArray(set) {\n  var index = -1, result = Array(set.size);\n  set.forEach(function(value) {\n    result[++index] = value;\n  });\n  return result;\n}\nvar setToArray_default = setToArray;\n\n// node_modules/lodash-es/_equalByTag.js\nvar COMPARE_PARTIAL_FLAG2 = 1;\nvar COMPARE_UNORDERED_FLAG2 = 2;\nvar boolTag2 = \"[object Boolean]\";\nvar dateTag2 = \"[object Date]\";\nvar errorTag2 = \"[object Error]\";\nvar mapTag3 = \"[object Map]\";\nvar numberTag2 = \"[object Number]\";\nvar regexpTag2 = \"[object RegExp]\";\nvar setTag3 = \"[object Set]\";\nvar stringTag2 = \"[object String]\";\nvar symbolTag2 = \"[object Symbol]\";\nvar arrayBufferTag2 = \"[object ArrayBuffer]\";\nvar dataViewTag3 = \"[object DataView]\";\nvar symbolProto2 = Symbol_default ? Symbol_default.prototype : void 0;\nvar symbolValueOf = symbolProto2 ? symbolProto2.valueOf : void 0;\nfunction equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {\n  switch (tag) {\n    case dataViewTag3:\n      if (object.byteLength != other.byteLength || object.byteOffset != other.byteOffset) {\n        return false;\n      }\n      object = object.buffer;\n      other = other.buffer;\n    case arrayBufferTag2:\n      if (object.byteLength != other.byteLength || !equalFunc(new Uint8Array_default(object), new Uint8Array_default(other))) {\n        return false;\n      }\n      return true;\n    case boolTag2:\n    case dateTag2:\n    case numberTag2:\n      return eq_default(+object, +other);\n    case errorTag2:\n      return object.name == other.name && object.message == other.message;\n    case regexpTag2:\n    case stringTag2:\n      return object == other + \"\";\n    case mapTag3:\n      var convert = mapToArray_default;\n    case setTag3:\n      var isPartial = bitmask & COMPARE_PARTIAL_FLAG2;\n      convert || (convert = setToArray_default);\n      if (object.size != other.size && !isPartial) {\n        return false;\n      }\n      var stacked = stack.get(object);\n      if (stacked) {\n        return stacked == other;\n      }\n      bitmask |= COMPARE_UNORDERED_FLAG2;\n      stack.set(object, other);\n      var result = equalArrays_default(convert(object), convert(other), bitmask, customizer, equalFunc, stack);\n      stack[\"delete\"](object);\n      return result;\n    case symbolTag2:\n      if (symbolValueOf) {\n        return symbolValueOf.call(object) == symbolValueOf.call(other);\n      }\n  }\n  return false;\n}\nvar equalByTag_default = equalByTag;\n\n// node_modules/lodash-es/_equalObjects.js\nvar COMPARE_PARTIAL_FLAG3 = 1;\nvar objectProto11 = Object.prototype;\nvar hasOwnProperty8 = objectProto11.hasOwnProperty;\nfunction equalObjects(object, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG3, objProps = getAllKeys_default(object), objLength = objProps.length, othProps = getAllKeys_default(other), othLength = othProps.length;\n  if (objLength != othLength && !isPartial) {\n    return false;\n  }\n  var index = objLength;\n  while (index--) {\n    var key = objProps[index];\n    if (!(isPartial ? key in other : hasOwnProperty8.call(other, key))) {\n      return false;\n    }\n  }\n  var objStacked = stack.get(object);\n  var othStacked = stack.get(other);\n  if (objStacked && othStacked) {\n    return objStacked == other && othStacked == object;\n  }\n  var result = true;\n  stack.set(object, other);\n  stack.set(other, object);\n  var skipCtor = isPartial;\n  while (++index < objLength) {\n    key = objProps[index];\n    var objValue = object[key], othValue = other[key];\n    if (customizer) {\n      var compared = isPartial ? customizer(othValue, objValue, key, other, object, stack) : customizer(objValue, othValue, key, object, other, stack);\n    }\n    if (!(compared === void 0 ? objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack) : compared)) {\n      result = false;\n      break;\n    }\n    skipCtor || (skipCtor = key == \"constructor\");\n  }\n  if (result && !skipCtor) {\n    var objCtor = object.constructor, othCtor = other.constructor;\n    if (objCtor != othCtor && (\"constructor\" in object && \"constructor\" in other) && !(typeof objCtor == \"function\" && objCtor instanceof objCtor && typeof othCtor == \"function\" && othCtor instanceof othCtor)) {\n      result = false;\n    }\n  }\n  stack[\"delete\"](object);\n  stack[\"delete\"](other);\n  return result;\n}\nvar equalObjects_default = equalObjects;\n\n// node_modules/lodash-es/_baseIsEqualDeep.js\nvar COMPARE_PARTIAL_FLAG4 = 1;\nvar argsTag3 = \"[object Arguments]\";\nvar arrayTag2 = \"[object Array]\";\nvar objectTag3 = \"[object Object]\";\nvar objectProto12 = Object.prototype;\nvar hasOwnProperty9 = objectProto12.hasOwnProperty;\nfunction baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {\n  var objIsArr = isArray_default(object), othIsArr = isArray_default(other), objTag = objIsArr ? arrayTag2 : getTag_default(object), othTag = othIsArr ? arrayTag2 : getTag_default(other);\n  objTag = objTag == argsTag3 ? objectTag3 : objTag;\n  othTag = othTag == argsTag3 ? objectTag3 : othTag;\n  var objIsObj = objTag == objectTag3, othIsObj = othTag == objectTag3, isSameTag = objTag == othTag;\n  if (isSameTag && isBuffer_default(object)) {\n    if (!isBuffer_default(other)) {\n      return false;\n    }\n    objIsArr = true;\n    objIsObj = false;\n  }\n  if (isSameTag && !objIsObj) {\n    stack || (stack = new Stack_default());\n    return objIsArr || isTypedArray_default(object) ? equalArrays_default(object, other, bitmask, customizer, equalFunc, stack) : equalByTag_default(object, other, objTag, bitmask, customizer, equalFunc, stack);\n  }\n  if (!(bitmask & COMPARE_PARTIAL_FLAG4)) {\n    var objIsWrapped = objIsObj && hasOwnProperty9.call(object, \"__wrapped__\"), othIsWrapped = othIsObj && hasOwnProperty9.call(other, \"__wrapped__\");\n    if (objIsWrapped || othIsWrapped) {\n      var objUnwrapped = objIsWrapped ? object.value() : object, othUnwrapped = othIsWrapped ? other.value() : other;\n      stack || (stack = new Stack_default());\n      return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);\n    }\n  }\n  if (!isSameTag) {\n    return false;\n  }\n  stack || (stack = new Stack_default());\n  return equalObjects_default(object, other, bitmask, customizer, equalFunc, stack);\n}\nvar baseIsEqualDeep_default = baseIsEqualDeep;\n\n// node_modules/lodash-es/_baseIsEqual.js\nfunction baseIsEqual(value, other, bitmask, customizer, stack) {\n  if (value === other) {\n    return true;\n  }\n  if (value == null || other == null || !isObjectLike_default(value) && !isObjectLike_default(other)) {\n    return value !== value && other !== other;\n  }\n  return baseIsEqualDeep_default(value, other, bitmask, customizer, baseIsEqual, stack);\n}\nvar baseIsEqual_default = baseIsEqual;\n\n// node_modules/lodash-es/_arrayIncludesWith.js\nfunction arrayIncludesWith(array, value, comparator) {\n  var index = -1, length = array == null ? 0 : array.length;\n  while (++index < length) {\n    if (comparator(value, array[index])) {\n      return true;\n    }\n  }\n  return false;\n}\nvar arrayIncludesWith_default = arrayIncludesWith;\n\n// node_modules/lodash-es/_escapeHtmlChar.js\nvar htmlEscapes = {\n  \"&\": \"&amp;\",\n  \"<\": \"&lt;\",\n  \">\": \"&gt;\",\n  '\"': \"&quot;\",\n  \"'\": \"&#39;\"\n};\nvar escapeHtmlChar = basePropertyOf_default(htmlEscapes);\nvar escapeHtmlChar_default = escapeHtmlChar;\n\n// node_modules/lodash-es/escape.js\nvar reUnescapedHtml = /[&<>\"']/g;\nvar reHasUnescapedHtml = RegExp(reUnescapedHtml.source);\nfunction escape(string) {\n  string = toString_default(string);\n  return string && reHasUnescapedHtml.test(string) ? string.replace(reUnescapedHtml, escapeHtmlChar_default) : string;\n}\nvar escape_default = escape;\n\n// node_modules/lodash-es/escapeRegExp.js\nvar reRegExpChar2 = /[\\\\^$.*+?()[\\]{}|]/g;\nvar reHasRegExpChar = RegExp(reRegExpChar2.source);\nfunction escapeRegExp(string) {\n  string = toString_default(string);\n  return string && reHasRegExpChar.test(string) ? string.replace(reRegExpChar2, \"\\\\$&\") : string;\n}\nvar escapeRegExp_default = escapeRegExp;\n\n// node_modules/lodash-es/isEqual.js\nfunction isEqual(value, other) {\n  return baseIsEqual_default(value, other);\n}\nvar isEqual_default = isEqual;\n\n// node_modules/lodash-es/_createSet.js\nvar INFINITY2 = 1 / 0;\nvar createSet = !(Set_default && 1 / setToArray_default(new Set_default([, -0]))[1] == INFINITY2) ? noop_default : function(values) {\n  return new Set_default(values);\n};\nvar createSet_default = createSet;\n\n// node_modules/lodash-es/_baseUniq.js\nvar LARGE_ARRAY_SIZE2 = 200;\nfunction baseUniq(array, iteratee, comparator) {\n  var index = -1, includes = arrayIncludes_default, length = array.length, isCommon = true, result = [], seen = result;\n  if (comparator) {\n    isCommon = false;\n    includes = arrayIncludesWith_default;\n  } else if (length >= LARGE_ARRAY_SIZE2) {\n    var set = iteratee ? null : createSet_default(array);\n    if (set) {\n      return setToArray_default(set);\n    }\n    isCommon = false;\n    includes = cacheHas_default;\n    seen = new SetCache_default();\n  } else {\n    seen = iteratee ? [] : result;\n  }\n  outer:\n    while (++index < length) {\n      var value = array[index], computed = iteratee ? iteratee(value) : value;\n      value = comparator || value !== 0 ? value : 0;\n      if (isCommon && computed === computed) {\n        var seenIndex = seen.length;\n        while (seenIndex--) {\n          if (seen[seenIndex] === computed) {\n            continue outer;\n          }\n        }\n        if (iteratee) {\n          seen.push(computed);\n        }\n        result.push(value);\n      } else if (!includes(seen, computed, comparator)) {\n        if (seen !== result) {\n          seen.push(computed);\n        }\n        result.push(value);\n      }\n    }\n  return result;\n}\nvar baseUniq_default = baseUniq;\n\n// node_modules/lodash-es/uniq.js\nfunction uniq(array) {\n  return array && array.length ? baseUniq_default(array) : [];\n}\nvar uniq_default = uniq;\n\n// src/components/constants.ts\nvar KEYS = {\n  ENTER: [10, 13],\n  TAB: 9,\n  BACKSPACE: 8,\n  UP_ARROW: 38,\n  DOWN_ARROW: 40,\n  ESCAPE: 27,\n  SPACE: 32,\n  COMMA: 188\n};\nvar SEPARATORS = {\n  ENTER: \"Enter\",\n  TAB: \"Tab\",\n  COMMA: \",\",\n  SPACE: \" \",\n  SEMICOLON: \";\"\n};\nvar DEFAULT_PLACEHOLDER = \"Press enter to add new tag\";\nvar DEFAULT_LABEL_FIELD = \"text\";\nvar DEFAULT_CLASSNAMES = {\n  tags: \"ReactTags__tags\",\n  tagInput: \"ReactTags__tagInput\",\n  tagInputField: \"ReactTags__tagInputField\",\n  selected: \"ReactTags__selected\",\n  tag: \"ReactTags__tag\",\n  remove: \"ReactTags__remove\",\n  suggestions: \"ReactTags__suggestions\",\n  activeSuggestion: \"ReactTags__activeSuggestion\",\n  editTagInput: \"ReactTags__editTagInput\",\n  editTagInputField: \"ReactTags__editTagInputField\",\n  clearAll: \"ReactTags__clearAll\"\n};\nvar INPUT_FIELD_POSITIONS = {\n  INLINE: \"inline\",\n  TOP: \"top\",\n  BOTTOM: \"bottom\"\n};\nvar ERRORS = {\n  TAG_LIMIT: \"Tag limit reached!\"\n};\n\n// src/components/utils.ts\nfunction buildRegExpFromDelimiters(delimiters) {\n  const delimiterChars = delimiters.map((delimiter) => {\n    const chrCode = delimiter - 48 * Math.floor(delimiter / 48);\n    return String.fromCharCode(96 <= delimiter ? chrCode : delimiter);\n  }).join(\"\");\n  const escapedDelimiterChars = escapeRegExp_default(delimiterChars);\n  return new RegExp(`[${escapedDelimiterChars}]+`);\n}\nfunction getKeyCodeFromSeparator(separator) {\n  switch (separator) {\n    case SEPARATORS.ENTER:\n      return [10, 13];\n    case SEPARATORS.TAB:\n      return 9;\n    case SEPARATORS.COMMA:\n      return 188;\n    case SEPARATORS.SPACE:\n      return 32;\n    case SEPARATORS.SEMICOLON:\n      return 186;\n    default:\n      return 0;\n  }\n}\nfunction canDrag(params) {\n  const { moveTag, readOnly, allowDragDrop } = params;\n  return moveTag !== void 0 && !readOnly && allowDragDrop;\n}\nfunction canDrop(params) {\n  const { readOnly, allowDragDrop } = params;\n  return !readOnly && allowDragDrop;\n}\n\n// src/components/RemoveComponent.tsx\n\nvar RemoveComponent = (props) => {\n  const { readOnly, removeComponent, onRemove, className, tag, index } = props;\n  const onKeydown = (event) => {\n    if (KEYS.ENTER.includes(event.keyCode) || event.keyCode === KEYS.SPACE) {\n      event.preventDefault();\n      event.stopPropagation();\n      return;\n    }\n    if (event.keyCode === KEYS.BACKSPACE) {\n      onRemove(event);\n    }\n  };\n  if (readOnly) {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {});\n  }\n  const ariaLabel = `Tag at index ${index} with value ${tag.id} focussed. Press backspace to remove`;\n  if (removeComponent) {\n    const Component = removeComponent;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      Component,\n      {\n        \"data-testid\": \"remove\",\n        onRemove,\n        onKeyDown: onKeydown,\n        className,\n        \"aria-label\": ariaLabel,\n        tag,\n        index\n      }\n    );\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    \"button\",\n    {\n      \"data-testid\": \"remove\",\n      onClick: onRemove,\n      onKeyDown: onKeydown,\n      className,\n      type: \"button\",\n      \"aria-label\": ariaLabel,\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n        \"svg\",\n        {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          viewBox: \"0 0 512 512\",\n          height: \"12\",\n          width: \"12\",\n          fill: \"#fff\",\n          children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"path\", { d: \"M376.6 84.5c11.3-13.6 9.5-33.8-4.1-45.1s-33.8-9.5-45.1 4.1L192 206 56.6 43.5C45.3 29.9 25.1 28.1 11.5 39.4S-3.9 70.9 7.4 84.5L150.3 256 7.4 427.5c-11.3 13.6-9.5 33.8 4.1 45.1s33.8 9.5 45.1-4.1L192 306 327.4 468.5c11.3 13.6 31.5 15.4 45.1 4.1s15.4-31.5 4.1-45.1L233.7 256 376.6 84.5z\" })\n        }\n      )\n    }\n  );\n};\nvar RemoveComponent_default = RemoveComponent;\n\n// src/components/SingleTag.tsx\n\nvar ItemTypes = { TAG: \"tag\" };\nvar SingleTag = (props) => {\n  const tagRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const {\n    readOnly = false,\n    tag,\n    classNames,\n    index,\n    moveTag,\n    allowDragDrop = true,\n    labelField = \"text\",\n    tags\n  } = props;\n  const [{ isDragging }, drag] = (0,react_dnd__WEBPACK_IMPORTED_MODULE_2__.useDrag)(() => ({\n    type: ItemTypes.TAG,\n    collect: (monitor) => ({\n      isDragging: !!monitor.isDragging()\n    }),\n    item: props,\n    canDrag: () => canDrag({ moveTag, readOnly, allowDragDrop })\n  }), [tags]);\n  const [, drop] = (0,react_dnd__WEBPACK_IMPORTED_MODULE_3__.useDrop)(() => ({\n    accept: ItemTypes.TAG,\n    drop: (item) => {\n      const dragIndex = item.index;\n      const hoverIndex = index;\n      if (dragIndex === hoverIndex) {\n        return;\n      }\n      props?.moveTag?.(dragIndex, hoverIndex);\n    },\n    canDrop: (item) => canDrop(item)\n  }), [tags]);\n  drag(drop(tagRef));\n  const label = props.tag[labelField];\n  const { className = \"\" } = tag;\n  const opacity = isDragging ? 0 : 1;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\n    \"span\",\n    {\n      ref: tagRef,\n      className: (0, import_classnames.default)(\"tag-wrapper\", classNames.tag, className),\n      style: {\n        opacity,\n        cursor: canDrag({ moveTag, readOnly, allowDragDrop }) ? \"move\" : \"auto\"\n      },\n      \"data-testid\": \"tag\",\n      onClick: props.onTagClicked,\n      onTouchStart: props.onTagClicked,\n      children: [\n        label,\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          RemoveComponent_default,\n          {\n            tag: props.tag,\n            className: classNames.remove,\n            removeComponent: props.removeComponent,\n            onRemove: props.onDelete,\n            readOnly,\n            index\n          }\n        )\n      ]\n    }\n  );\n};\n\n// src/components/ReactTags.tsx\n\n\n// src/components/ClearAllTags.tsx\n\nvar ClearAllTags = (props) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"button\", { \"aria-label\": props[\"aria-label\"], className: props.classNames.clearAll, onClick: props.onClick, children: \"Clear all\" });\n};\nvar ClearAllTags_default = ClearAllTags;\n\n// src/components/Suggestions.tsx\n\n\nvar maybeScrollSuggestionIntoView = (suggestionEl, suggestionsContainer) => {\n  const containerHeight = suggestionsContainer.offsetHeight;\n  const suggestionHeight = suggestionEl.offsetHeight;\n  const relativeSuggestionTop = suggestionEl.offsetTop - suggestionsContainer.scrollTop;\n  if (relativeSuggestionTop + suggestionHeight >= containerHeight) {\n    suggestionsContainer.scrollTop += relativeSuggestionTop - containerHeight + suggestionHeight;\n  } else if (relativeSuggestionTop < 0) {\n    suggestionsContainer.scrollTop += relativeSuggestionTop;\n  }\n};\nvar shouldRenderSuggestions = (query, minQueryLength, isFocused, shouldRenderSuggestionsCb) => {\n  if (typeof shouldRenderSuggestionsCb === \"function\") {\n    return shouldRenderSuggestionsCb(query);\n  }\n  return query.length >= minQueryLength && isFocused;\n};\nvar SuggestionsComp = (props) => {\n  const suggestionsContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.createRef)();\n  const {\n    labelField,\n    minQueryLength,\n    isFocused,\n    classNames,\n    selectedIndex,\n    query\n  } = props;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!suggestionsContainerRef.current) {\n      return;\n    }\n    const activeSuggestion = suggestionsContainerRef.current.querySelector(\n      `.${classNames.activeSuggestion}`\n    );\n    if (activeSuggestion) {\n      maybeScrollSuggestionIntoView(\n        activeSuggestion,\n        suggestionsContainerRef.current\n      );\n    }\n  }, [selectedIndex]);\n  const markIt = (tag, query2) => {\n    const escapedRegex = query2.trim().replace(/[-\\\\^$*+?.()|[\\]{}]/g, \"\\\\$&\");\n    const { [labelField]: labelValue } = tag;\n    return {\n      __html: labelValue.replace(RegExp(escapedRegex, \"gi\"), (x) => {\n        return `<mark>${escape_default(x)}</mark>`;\n      })\n    };\n  };\n  const renderSuggestion = (tag, query2) => {\n    if (typeof props.renderSuggestion === \"function\") {\n      return props.renderSuggestion(tag, query2);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", { dangerouslySetInnerHTML: markIt(tag, query2) });\n  };\n  const suggestions = props.suggestions.map((tag, index) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      \"li\",\n      {\n        onMouseDown: props.handleClick.bind(null, index),\n        onTouchStart: props.handleClick.bind(null, index),\n        onMouseOver: props.handleHover.bind(null, index),\n        className: index === props.selectedIndex ? props.classNames.activeSuggestion : \"\",\n        children: renderSuggestion(tag, props.query)\n      },\n      index\n    );\n  });\n  if (suggestions.length === 0 || !shouldRenderSuggestions(\n    query,\n    minQueryLength || 2,\n    isFocused,\n    props.shouldRenderSuggestions\n  )) {\n    return null;\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    \"div\",\n    {\n      ref: suggestionsContainerRef,\n      className: classNames.suggestions,\n      \"data-testid\": \"suggestions\",\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"ul\", { children: [\n        \" \",\n        suggestions,\n        \" \"\n      ] })\n    }\n  );\n};\nvar arePropsEqual = (prevProps, nextProps) => {\n  const { query, minQueryLength = 2, isFocused, suggestions } = nextProps;\n  if (prevProps.isFocused === isFocused && isEqual_default(prevProps.suggestions, suggestions) && shouldRenderSuggestions(\n    query,\n    minQueryLength,\n    isFocused,\n    nextProps.shouldRenderSuggestions\n  ) === shouldRenderSuggestions(\n    prevProps.query,\n    prevProps.minQueryLength ?? 2,\n    prevProps.isFocused,\n    prevProps.shouldRenderSuggestions\n  ) && prevProps.selectedIndex === nextProps.selectedIndex) {\n    return true;\n  }\n  return false;\n};\nvar Suggestions = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(SuggestionsComp, arePropsEqual);\nvar Suggestions_default = Suggestions;\n\n// src/components/ReactTags.tsx\nvar import_classnames2 = __toESM(require_classnames(), 1);\n\nvar ReactTags = (props) => {\n  const {\n    autofocus,\n    autoFocus,\n    readOnly,\n    labelField,\n    allowDeleteFromEmptyInput,\n    allowAdditionFromPaste,\n    allowDragDrop,\n    minQueryLength,\n    shouldRenderSuggestions: shouldRenderSuggestions2,\n    removeComponent,\n    autocomplete,\n    inline,\n    maxTags,\n    allowUnique,\n    editable,\n    placeholder,\n    delimiters,\n    separators,\n    tags,\n    inputFieldPosition,\n    inputProps,\n    classNames,\n    maxLength,\n    inputValue,\n    clearAll,\n    ariaAttrs\n  } = props;\n  const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(props.suggestions);\n  const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(-1);\n  const [selectionMode, setSelectionMode] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [ariaLiveStatus, setAriaLiveStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [currentEditIndex, setCurrentEditIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(-1);\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const reactTagsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.createRef)();\n  const textInput = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const tagInput = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (delimiters.length) {\n      console.warn(\n        \"[Deprecation] The delimiters prop is deprecated and will be removed in v7.x.x, please use separators instead. If you have any concerns regarding this, please share your thoughts in https://github.com/react-tags/react-tags/issues/960\"\n      );\n    }\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (typeof inline !== \"undefined\") {\n      console.warn(\n        \"[Deprecation] The inline attribute is deprecated and will be removed in v7.x.x, please use inputFieldPosition instead.\"\n      );\n    }\n  }, [inline]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (typeof autofocus !== \"undefined\") {\n      console.warn(\n        \"[Deprecated] autofocus prop will be removed in 7.x so please migrate to autoFocus prop.\"\n      );\n    }\n    if ((autofocus || autoFocus && autofocus !== false) && !readOnly) {\n      resetAndFocusInput();\n    }\n  }, [autoFocus, autoFocus, readOnly]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    updateSuggestions();\n  }, [query, props.suggestions]);\n  const filteredSuggestions = (query2) => {\n    let updatedSuggestions = props.suggestions.slice();\n    if (allowUnique) {\n      const existingTags = tags.map((tag) => tag.id.trim().toLowerCase());\n      updatedSuggestions = updatedSuggestions.filter(\n        (suggestion) => !existingTags.includes(suggestion.id.toLowerCase())\n      );\n    }\n    if (props.handleFilterSuggestions) {\n      return props.handleFilterSuggestions(query2, updatedSuggestions);\n    }\n    const exactSuggestions = updatedSuggestions.filter(\n      (item) => getQueryIndex(query2, item) === 0\n    );\n    const partialSuggestions = updatedSuggestions.filter(\n      (item) => getQueryIndex(query2, item) > 0\n    );\n    return exactSuggestions.concat(partialSuggestions);\n  };\n  const getQueryIndex = (query2, item) => {\n    return item[labelField].toLowerCase().indexOf(query2.toLowerCase());\n  };\n  const resetAndFocusInput = () => {\n    setQuery(\"\");\n    if (!textInput.current) {\n      return;\n    }\n    textInput.current.value = \"\";\n    textInput.current.focus();\n  };\n  const handleDelete = (index, event) => {\n    event.preventDefault();\n    event.stopPropagation();\n    const currentTags = tags.slice();\n    if (currentTags.length === 0) {\n      return;\n    }\n    setError(\"\");\n    props?.handleDelete?.(index, event);\n    updateAriaLiveStatus(index, currentTags);\n  };\n  const updateAriaLiveStatus = (index, tags2) => {\n    if (!reactTagsRef?.current) {\n      return;\n    }\n    const tagRemoveButtons = reactTagsRef.current.querySelectorAll(\".ReactTags__remove\");\n    let ariaLiveStatus2 = \"\";\n    if (index === 0 && tags2.length > 1) {\n      ariaLiveStatus2 = `Tag at index ${index} with value ${tags2[index].id} deleted. Tag at index 0 with value ${tags2[1].id} focussed. Press backspace to remove`;\n      tagRemoveButtons[0].focus();\n    } else if (index > 0) {\n      ariaLiveStatus2 = `Tag at index ${index} with value ${tags2[index].id} deleted. Tag at index ${index - 1} with value ${tags2[index - 1].id} focussed. Press backspace to remove`;\n      tagRemoveButtons[index - 1].focus();\n    } else {\n      ariaLiveStatus2 = `Tag at index ${index} with value ${tags2[index].id} deleted. Input focussed. Press enter to add a new tag`;\n      textInput.current?.focus();\n    }\n    setAriaLiveStatus(ariaLiveStatus2);\n  };\n  const handleTagClick = (index, tag, event) => {\n    if (readOnly) {\n      return;\n    }\n    if (editable) {\n      setCurrentEditIndex(index);\n      setQuery(tag[labelField]);\n      tagInput.current?.focus();\n    }\n    props.handleTagClick?.(index, event);\n  };\n  const handleChange = (event) => {\n    if (props.handleInputChange) {\n      props.handleInputChange(event.target.value, event);\n    }\n    const query2 = event.target.value.trim();\n    setQuery(query2);\n  };\n  const updateSuggestions = () => {\n    const newSuggestions = filteredSuggestions(query);\n    setSuggestions(newSuggestions);\n    setSelectedIndex(\n      selectedIndex >= newSuggestions.length ? newSuggestions.length - 1 : selectedIndex\n    );\n  };\n  const handleFocus = (event) => {\n    const value = event.target.value;\n    if (props.handleInputFocus) {\n      props.handleInputFocus(value, event);\n    }\n    setIsFocused(true);\n  };\n  const handleBlur = (event) => {\n    const value = event.target.value;\n    if (props.handleInputBlur) {\n      props.handleInputBlur(value, event);\n      if (textInput.current) {\n        textInput.current.value = \"\";\n      }\n    }\n    setIsFocused(false);\n    setCurrentEditIndex(-1);\n  };\n  const handleKeyDown = (event) => {\n    const nativeEvent = event.nativeEvent;\n    if (nativeEvent.isComposing) {\n      return;\n    }\n    if (event.key === \"Escape\") {\n      event.preventDefault();\n      event.stopPropagation();\n      setSelectedIndex(-1);\n      setSelectionMode(false);\n      setSuggestions([]);\n      setCurrentEditIndex(-1);\n    }\n    if ((separators.indexOf(event.key) !== -1 || delimiters.indexOf(event.keyCode) !== -1) && !event.shiftKey) {\n      if (event.keyCode !== KEYS.TAB || query !== \"\") {\n        event.preventDefault();\n      }\n      const selectedQuery = selectionMode && selectedIndex !== -1 ? suggestions[selectedIndex] : {\n        id: query.trim(),\n        [labelField]: query.trim(),\n        className: \"\"\n      };\n      if (Object.keys(selectedQuery)) {\n        addTag(selectedQuery);\n      }\n    }\n    if (event.key === \"Backspace\" && query === \"\" && (allowDeleteFromEmptyInput || inputFieldPosition === INPUT_FIELD_POSITIONS.INLINE)) {\n      handleDelete(tags.length - 1, event);\n    }\n    if (event.keyCode === KEYS.UP_ARROW) {\n      event.preventDefault();\n      setSelectedIndex(\n        selectedIndex <= 0 ? suggestions.length - 1 : selectedIndex - 1\n      );\n      setSelectionMode(true);\n    }\n    if (event.keyCode === KEYS.DOWN_ARROW) {\n      event.preventDefault();\n      setSelectionMode(true);\n      suggestions.length === 0 ? setSelectedIndex(-1) : setSelectedIndex((selectedIndex + 1) % suggestions.length);\n    }\n  };\n  const tagLimitReached = () => {\n    return maxTags && tags.length >= maxTags;\n  };\n  const handlePaste = (event) => {\n    if (!allowAdditionFromPaste) {\n      return;\n    }\n    if (tagLimitReached()) {\n      setError(ERRORS.TAG_LIMIT);\n      resetAndFocusInput();\n      return;\n    }\n    setError(\"\");\n    event.preventDefault();\n    const clipboardData = event.clipboardData || window.clipboardData;\n    const clipboardText = clipboardData.getData(\"text\");\n    const { maxLength: maxLength2 = clipboardText.length } = props;\n    const maxTextLength = Math.min(maxLength2, clipboardText.length);\n    const pastedText = clipboardData.getData(\"text\").substr(0, maxTextLength);\n    let keycodes = delimiters;\n    if (separators.length) {\n      keycodes = [];\n      separators.forEach((separator) => {\n        const keycode = getKeyCodeFromSeparator(separator);\n        if (Array.isArray(keycode)) {\n          keycodes = [...keycodes, ...keycode];\n        } else {\n          keycodes.push(keycode);\n        }\n      });\n    }\n    const delimiterRegExp = buildRegExpFromDelimiters(keycodes);\n    const tags2 = pastedText.split(delimiterRegExp).map((tag) => tag.trim());\n    uniq_default(tags2).forEach(\n      (tag) => addTag({\n        id: tag.trim(),\n        [labelField]: tag.trim(),\n        className: \"\"\n      })\n    );\n  };\n  const addTag = (tag) => {\n    if (!tag.id || !tag[labelField]) {\n      return;\n    }\n    if (currentEditIndex === -1) {\n      if (tagLimitReached()) {\n        setError(ERRORS.TAG_LIMIT);\n        resetAndFocusInput();\n        return;\n      }\n      setError(\"\");\n    }\n    const existingKeys = tags.map((tag2) => tag2.id.toLowerCase());\n    if (allowUnique && existingKeys.indexOf(tag.id.trim().toLowerCase()) >= 0) {\n      return;\n    }\n    if (autocomplete) {\n      const possibleMatches = filteredSuggestions(tag[labelField]);\n      console.warn(\n        \"[Deprecation] The autocomplete prop will be removed in 7.x to simplify the integration and make it more intutive. If you have any concerns regarding this, please share your thoughts in https://github.com/react-tags/react-tags/issues/949\"\n      );\n      if (autocomplete === 1 && possibleMatches.length === 1 || autocomplete === true && possibleMatches.length) {\n        tag = possibleMatches[0];\n      }\n    }\n    if (currentEditIndex !== -1 && props.onTagUpdate)\n      props.onTagUpdate(currentEditIndex, tag);\n    else props?.handleAddition?.(tag);\n    setQuery(\"\");\n    setSelectionMode(false);\n    setSelectedIndex(-1);\n    setCurrentEditIndex(-1);\n    resetAndFocusInput();\n  };\n  const handleSuggestionClick = (index) => {\n    addTag(suggestions[index]);\n  };\n  const handleClearAll = () => {\n    if (props.onClearAll) {\n      props.onClearAll();\n    }\n    setError(\"\");\n    resetAndFocusInput();\n  };\n  const handleSuggestionHover = (index) => {\n    setSelectedIndex(index);\n    setSelectionMode(true);\n  };\n  const moveTag = (dragIndex, hoverIndex) => {\n    const dragTag = tags[dragIndex];\n    props?.handleDrag?.(dragTag, dragIndex, hoverIndex);\n  };\n  const getTagItems = () => {\n    const allClassNames2 = { ...DEFAULT_CLASSNAMES, ...props.classNames };\n    return tags.map((tag, index) => {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: currentEditIndex === index ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", { className: allClassNames2.editTagInput, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n        \"input\",\n        {\n          ref: (input) => {\n            tagInput.current = input;\n          },\n          onFocus: handleFocus,\n          value: query,\n          onChange: handleChange,\n          onKeyDown: handleKeyDown,\n          onBlur: handleBlur,\n          className: allClassNames2.editTagInputField,\n          onPaste: handlePaste,\n          \"data-testid\": \"tag-edit\"\n        }\n      ) }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n        SingleTag,\n        {\n          index,\n          tag,\n          tags,\n          labelField,\n          onDelete: (event) => handleDelete(index, event),\n          moveTag: allowDragDrop ? moveTag : void 0,\n          removeComponent,\n          onTagClicked: (event) => handleTagClick(index, tag, event),\n          readOnly,\n          classNames: allClassNames2,\n          allowDragDrop\n        }\n      ) }, index);\n    });\n  };\n  const tagItems = getTagItems();\n  const allClassNames = { ...DEFAULT_CLASSNAMES, ...classNames };\n  const { name: inputName, id: inputId } = props;\n  const position = inline === false ? INPUT_FIELD_POSITIONS.BOTTOM : inputFieldPosition;\n  const tagsComponent = !readOnly ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { className: allClassNames.tagInput, children: [\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      \"input\",\n      {\n        ...inputProps,\n        ref: (input) => {\n          textInput.current = input;\n        },\n        className: allClassNames.tagInputField,\n        type: \"text\",\n        placeholder,\n        \"aria-label\": placeholder,\n        onFocus: handleFocus,\n        onBlur: handleBlur,\n        onChange: handleChange,\n        onKeyDown: handleKeyDown,\n        onPaste: handlePaste,\n        name: inputName,\n        id: inputId,\n        maxLength,\n        value: inputValue,\n        \"data-automation\": \"input\",\n        \"data-testid\": \"input\"\n      }\n    ),\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      Suggestions_default,\n      {\n        query: query.trim(),\n        suggestions,\n        labelField,\n        selectedIndex,\n        handleClick: handleSuggestionClick,\n        handleHover: handleSuggestionHover,\n        minQueryLength,\n        shouldRenderSuggestions: shouldRenderSuggestions2,\n        isFocused,\n        classNames: allClassNames,\n        renderSuggestion: props.renderSuggestion\n      }\n    ),\n    clearAll && tags.length > 0 && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ClearAllTags_default, { \"aria-label\": ariaAttrs?.clearAllLabel, classNames: allClassNames, onClick: handleClearAll }),\n    error && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { \"data-testid\": \"error\", className: \"ReactTags__error\", children: [\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n        \"svg\",\n        {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          viewBox: \"0 0 512 512\",\n          height: \"24\",\n          width: \"24\",\n          fill: \"#e03131\",\n          children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"path\", { d: \"M256 32c14.2 0 27.3 7.5 34.5 19.8l216 368c7.3 12.4 7.3 27.7 .2 40.1S486.3 480 472 480H40c-14.3 0-27.6-7.7-34.7-20.1s-7-27.8 .2-40.1l216-368C228.7 39.5 241.8 32 256 32zm0 128c-13.3 0-24 10.7-24 24V296c0 13.3 10.7 24 24 24s24-10.7 24-24V184c0-13.3-10.7-24-24-24zm32 224a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z\" })\n        }\n      ),\n      error\n    ] })\n  ] }) : null;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\n    \"div\",\n    {\n      className: (0, import_classnames2.default)(allClassNames.tags, \"react-tags-wrapper\"),\n      ref: reactTagsRef,\n      children: [\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          \"p\",\n          {\n            role: \"alert\",\n            className: \"sr-only\",\n            style: {\n              position: \"absolute\",\n              overflow: \"hidden\",\n              clip: \"rect(0 0 0 0)\",\n              margin: \"-1px\",\n              padding: 0,\n              width: \"1px\",\n              height: \"1px\",\n              border: 0\n            },\n            children: ariaLiveStatus\n          }\n        ),\n        position === INPUT_FIELD_POSITIONS.TOP && tagsComponent,\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { className: allClassNames.selected, children: [\n          tagItems,\n          position === INPUT_FIELD_POSITIONS.INLINE && tagsComponent\n        ] }),\n        position === INPUT_FIELD_POSITIONS.BOTTOM && tagsComponent\n      ]\n    }\n  );\n};\nvar ReactTags_default = ReactTags;\n\n// src/index.tsx\n\nvar ReactTagsWrapper = (props) => {\n  const {\n    placeholder = DEFAULT_PLACEHOLDER,\n    labelField = DEFAULT_LABEL_FIELD,\n    suggestions = [],\n    // Set delimeters to empty array if not provided\n    delimiters = [],\n    // Set separators to empty array if delimiters is provided\n    separators = props.delimiters?.length ? [] : [SEPARATORS.ENTER, SEPARATORS.TAB],\n    autofocus,\n    autoFocus = true,\n    inline,\n    // TODO= Remove in v7.x.x\n    inputFieldPosition = \"inline\",\n    allowDeleteFromEmptyInput = false,\n    allowAdditionFromPaste = true,\n    autocomplete = false,\n    readOnly = false,\n    allowUnique = true,\n    allowDragDrop = true,\n    tags = [],\n    inputProps = {},\n    editable = false,\n    clearAll = false,\n    ariaAttrs = { clearAllLabel: \"clear all tags\" },\n    handleDelete,\n    handleAddition,\n    onTagUpdate,\n    handleDrag,\n    handleFilterSuggestions,\n    handleTagClick,\n    handleInputChange,\n    handleInputFocus,\n    handleInputBlur,\n    minQueryLength,\n    shouldRenderSuggestions: shouldRenderSuggestions2,\n    removeComponent,\n    onClearAll,\n    classNames,\n    name,\n    id,\n    maxLength,\n    inputValue,\n    maxTags,\n    renderSuggestion\n  } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    ReactTags_default,\n    {\n      placeholder,\n      labelField,\n      suggestions,\n      delimiters,\n      separators,\n      autofocus,\n      autoFocus,\n      inline,\n      inputFieldPosition,\n      allowDeleteFromEmptyInput,\n      allowAdditionFromPaste,\n      autocomplete,\n      readOnly,\n      allowUnique,\n      allowDragDrop,\n      tags,\n      inputProps,\n      editable,\n      clearAll,\n      ariaAttrs,\n      handleDelete,\n      handleAddition,\n      onTagUpdate,\n      handleDrag,\n      handleFilterSuggestions,\n      handleTagClick,\n      handleInputChange,\n      handleInputFocus,\n      handleInputBlur,\n      minQueryLength,\n      shouldRenderSuggestions: shouldRenderSuggestions2,\n      removeComponent,\n      onClearAll,\n      classNames,\n      name,\n      id,\n      maxLength,\n      inputValue,\n      maxTags,\n      renderSuggestion\n    }\n  );\n};\nvar WithContext = ({ ...props }) => (\n  // @ts-ignore\n  /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_dnd__WEBPACK_IMPORTED_MODULE_4__.DndProvider, { backend: react_dnd_html5_backend__WEBPACK_IMPORTED_MODULE_5__.HTML5Backend, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ReactTagsWrapper, { ...props }) })\n);\n\n/*! Bundled license information:\n\nclassnames/index.js:\n  (*!\n  \tCopyright (c) 2018 Jed Watson.\n  \tLicensed under the MIT License (MIT), see\n  \thttp://jedwatson.github.io/classnames\n  *)\n\nlodash-es/lodash.js:\n  (**\n   * @license\n   * Lodash (Custom Build) <https://lodash.com/>\n   * Build: `lodash modularize exports=\"es\" -o ./`\n   * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>\n   * Released under MIT license <https://lodash.com/license>\n   * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n   * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors\n   *)\n*/\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tag-input/dist/index.js\n");

/***/ })

};
;