import { axiosGetJsonSSR } from "../../../config/axios";

export const getDownloadReport =(body) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await axiosGetJsonSSR.get(`${"/report"}`, {
          params: {
            paginated: body.paginated,
            pageSize: body.pageSize,
            pageNumber: body.pageNumber,
            keyWord: body.keyWord,
          },
        });
        resolve(response.data);
      } catch (err) {
        reject(err);
      }
    });
  };