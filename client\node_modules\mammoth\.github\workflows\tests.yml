name: Tests

on: [push, pull_request]

jobs:
    build:
        runs-on: ubuntu-latest

        strategy:
            matrix:
                node-version: ["12", "14", "16"]

        steps:

        - uses: actions/checkout@v4

        - name: Use Node.js ${{ matrix.node-version }}
          uses: actions/setup-node@v4
          with:
              node-version: ${{ matrix.node-version }}

        - run: npm install

        - run: npm test

        - run: npm run check-typescript
