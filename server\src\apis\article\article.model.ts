import { Schema, model, Types } from 'mongoose';

import { ArticleI, ArticleVersion } from './article.interface';
import { Language, Visibility, robotsMeta } from '@/utils/helpers/constants';

// FAQ item schema for the FAQ array
const faqItemSchema = new Schema(
    {
        question: { type: String, required: true },
        answer: { type: String, required: true },
    },
    { _id: false },
); // _id: false to prevent automatic _id generation for subdocuments

const articleVersionSchema = new Schema<ArticleVersion>({
    language: { type: String, enum: Language },
    title: { type: String },
    keywords: { type: [String] },
    metaTitle: { type: String, default: '' },
    metaDescription: { type: String, default: '' },
    url: { type: String },
    alt: { type: String, default: '' },
    image: { type: String },
    category: [{ type: Types.ObjectId, ref: 'Category' }],
    shareOnSocialMedia: { type: Boolean, default: false },
    content: { type: String },
    canonical: { type: String },
    visibility: {
        type: String,
        enum: Visibility,
        default: Visibility.Draft,
    },
    publishDate: { type: Date },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
    isArchived: { type: Boolean, default: false },
    highlights: { type: [String] },
    description: { type: String },
    faqTitle: { type: String, default: '' },
    faq: { type: [faqItemSchema], default: [] },
});

const articleSchema = new Schema<ArticleI>(
    {
        versions: { type: [articleVersionSchema] },
        tags: { type: [String] },
        totalCommentaires: { type: Number, default: 0 },
        robotsMeta: {
            type: String,
            enum: robotsMeta,
            default: robotsMeta.index,
        },
        createdBy: { type: Types.ObjectId, ref: 'User' },
    },
    {
        timestamps: true,
        toJSON: {
            transform: function (doc, ret) {
                delete ret.__v;
            },
        },
    },
);

articleSchema.index(
    {
        'versions.title': 'text',
        url: 'text',
    },
    {
        weights: {
            'versions.title': 50,
            url: 30,
        },
        name: 'ArticleTextIndex',
    },
);

export default model<ArticleI>('Article', articleSchema);
