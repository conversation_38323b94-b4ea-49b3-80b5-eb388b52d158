import { Schema, model, Types } from 'mongoose';

import { ArticleI } from './article.interface';
import { Language, Visibility, robotsMeta } from '@/utils/helpers/constants';

const articleVersionFieldsSchema = {
    language: { type: String, enum: Language },
    title: { type: String },
    keywords: { type: [String] },
    metaTitle: { type: String, default: '' },
    metaDescription: { type: String, default: '' },
    url: { type: String },
    alt: { type: String, default: '' },
    image: { type: String },
    category: [{ type: Types.ObjectId, ref: 'Category' }],
    shareOnSocialMedia: { type: Boolean, default: false },
    content: { type: String },
    canonical: { type: String },
    visibility: {
        type: String,
        enum: Object.values(Visibility),
        default: Visibility.Draft,
    },
    publishDate: { type: Date },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
    isArchived: { type: Boolean, default: false },
    highlights: { type: [String] },
    description: { type: String },
    createdBy: { type: Types.ObjectId, ref: 'User' },
    updatedBy: { type: Types.ObjectId, ref: 'User' },
};

const articleSchema = new Schema<ArticleI>(
    {
        versions: { type: Map, of: articleVersionFieldsSchema, required: true },
        tags: { type: [String] },
        totalCommentaires: { type: Number, default: 0 },
        robotsMeta: {
            type: String,
            enum: Object.values(robotsMeta),
            default: robotsMeta.index,
        },
        createdBy: { type: Types.ObjectId, ref: 'User' },
    },
    {
        timestamps: true,
        toJSON: {
            transform: function (doc, ret) {
                delete ret.__v;
            },
        },
    },
);

articleSchema.index(
    {
        'versions.title': 'text',
        url: 'text',
    },
    {
        weights: {
            'versions.title': 50,
            url: 30,
        },
        name: 'ArticleTextIndex',
    },
);

export default model<ArticleI>('Article', articleSchema);
