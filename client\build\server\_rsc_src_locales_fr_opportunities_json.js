"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_locales_fr_opportunities_json";
exports.ids = ["_rsc_src_locales_fr_opportunities_json"];
exports.modules = {

/***/ "(rsc)/./src/locales/fr/opportunities.json":
/*!*******************************************!*\
  !*** ./src/locales/fr/opportunities.json ***!
  \*******************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"findMatch":"Trouvez votre partenaire idéal","country":"Pays","contractType":"Type de contrat","otherFilters":"Autres filtres","readMore":"En savoir plus","search":"Recherche","searchBy":"Rechercher par poste, mot-clé ou entreprise","noOpportunitiesFound":"Aucune opportunité trouvée","tryDifferentFilters":"Essayez d\'ajuster vos filtres ou critères de recherche","metaTitleOneOpportunity1":"Postulez maintenant pour","metaTitleOneOpportunity2":"en","metaDescriptionOneOpportunity1":"Candidatez pour le poste de","metaDescriptionOneOpportunity2":". Saisissez de nouvelles opportunités et développez vos compétences","metaDescriptionOneOpportunity3":"en"}');

/***/ })

};
;