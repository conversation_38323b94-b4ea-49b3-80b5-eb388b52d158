self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"5d4e1745b359194dee0f653f2deecc494f54833a\": {\n      \"workers\": {\n        \"app/[locale]/(website)/events/[url]/page\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(website)%5C%5Cevents%5C%5C%5Burl%5D%5C%5Cpage.jsx%22%2C%5B%22generateMetadata%22%2C%22default%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/[locale]/(website)/events/[url]/page\": \"rsc\"\n      }\n    },\n    \"bccf3e1378c81b1c82b03b218e3d6aedbbdabae2\": {\n      \"workers\": {\n        \"app/[locale]/(website)/events/[url]/page\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(website)%5C%5Cevents%5C%5C%5Burl%5D%5C%5Cpage.jsx%22%2C%5B%22generateMetadata%22%2C%22default%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/[locale]/(website)/events/[url]/page\": \"rsc\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"