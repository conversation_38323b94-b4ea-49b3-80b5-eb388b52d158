(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4128],{13941:function(e,t,a){Promise.resolve().then(a.bind(a,9474))},9474:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return w}});var n=a(57437),s=a(2265);a(49360),a(6179);var r=a(99376),i=a(63993),l=a(34422),c=a(55788),u=a(89126),o=a(64393),d=a(77584),m=a(63582),p=a(81799),g=a(33833),h=a(42187),y=a(28397),x=a(46172),f=a(74269),v=a(41774),j=a(62953);a(80657);var b=a(93214),N=a(1255),A=e=>{let{categoryId:t}=e;(0,N.xv)("en");let a=(0,N.xv)("fr");(0,r.useSearchParams)();let{data:A}=(0,N.xY)(t);a?.data?.categories?.map(e=>({id:e.categoryguide[0]?.id,name:e.categoryguide[0]?.name}));let[w,Z]=(0,s.useState)({}),[C,$]=(0,s.useState)({}),[T,E]=(0,s.useState)([]),[S,P]=(0,s.useState)([]),[D,_]=(0,s.useState)([]),[U,B]=(0,s.useState)([]),[I,k]=(0,s.useState)([]),[M,Y]=(0,s.useState)([]),O=(0,N.VO)({language:"en",paginated:!1},{enabled:!1}),R=(0,N.VO)({language:"fr"},{enabled:!1});(0,s.useEffect)(()=>{Z(A?.categoryguide?.find(e=>"en"===e.language)),$(A?.categoryguide?.find(e=>"fr"===e.language)),A?.categoryguide?.find(e=>"en"===e.language)?.guides&&k(A?.categoryguide?.find(e=>"en"===e.language)?.guides.map(e=>e)),A?.categoryguide?.find(e=>"fr"===e.language)?.guides&&Y(A?.categoryguide?.find(e=>"fr"===e.language)?.guides.map(e=>e))},[A]),(0,s.useEffect)(()=>{O.data&&E(O.data.map(e=>({id:e.guideId,name:e.title})))},[O.data]),(0,s.useEffect)(()=>{R.data&&P(R.data.map(e=>({id:e?.guideId,name:e?.title})))},[R.data]),(0,s.useEffect)(()=>{I.length>0?F(I,"en"):M.length>0&&F(M,"fr")},[I,M]);let F=async(e,t)=>{try{let a=await b.yX.get(`${x.Y.guides}/${t}/${e}/translation`);if("en"===t){let e=a.data.map(e=>({id:e.guideId,name:e.title}));B(e)}else{let e=a.data.map(e=>({id:e.guideId,name:e.title}));_(e)}}catch(e){console.error("Error fetching translated guides:",e)}};(0,j.jd)(),new Date().getFullYear();let{t:L}=(0,c.$G)();(0,s.useRef)(null),(0,s.useRef)(null);let[Q,X]=(0,s.useState)(null),[G,H]=(0,s.useState)(null),[q,z]=(0,s.useState)(!1),[J,V]=(0,s.useState)(""),[W,K]=(0,s.useState)(!1),[ee,et]=(0,s.useState)(!1),[ea,en]=(0,s.useState)(""),es=(0,N.Ny)(),er={language:"en",robotsMeta:A?.robotsMeta,name:w?.name,metaTitle:w?.metaTitle,metaDescription:w?.metaDescription,guides:w?.guides?.length>0?w.guides?.map(e=>e):[{name:""}],url:w?.url,description:w?.description},ei={robotsMeta:A?.robotsMeta,language:"fr",name:C?.name,metaTitle:C?.metaTitle,metaDescription:C?.metaDescription,guides:C?.guides?.length>0?C.guides?.map(e=>e):[{name:""}],url:C?.url,description:C?.description},el=l.Ry().shape({name:l.Z_().required(L("validations.emptyField"))}),ec=l.Ry().shape({name:l.Z_().required(L("validations.emptyField"))}),eu=async e=>{Array.isArray(e.guides)&&1===e.guides.length&&""===e.guides[0].name&&(e.guides=[]),es.mutate({data:e,language:"fr",id:t},{onSuccess:()=>{}})},eo=async e=>{Array.isArray(e.guides)&&1===e.guides.length&&""===e.guides[0].name&&(e.guides=[]),es.mutate({data:e,language:"en",id:t})},ed=e=>{V(e),K(!0)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{className:"heading-h2 semi-bold",children:L("createArticle:editCategory")}),(0,n.jsx)("div",{className:"main-content",children:(0,n.jsx)("div",{id:"container",className:"container",children:(0,n.jsxs)("div",{className:"commun",children:[(0,n.jsx)(i.J9,{initialValues:er,validationSchema:el,onSubmit:eo,enableReinitialize:"true",children:e=>{let{errors:t,touched:a,setFieldValue:s,values:r}=e;return(0,n.jsxs)(i.l0,{children:[(0,n.jsxs)("div",{className:"inline-group",children:[(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(u.Z,{children:(0,n.jsxs)(o.Z,{className:"label-form",children:[L("createArticle:nameCat"),"* (",L("createArticle:en"),") ",(0,n.jsx)(d.Z,{variant:"standard",name:"name",type:"text",value:r.name,onChange:e=>{let t=e.target.value;s("name",t),s("url",(0,f.o)(t))},className:"input-pentabell"+(t.name&&a.name?" is-invalid":"")})," ",(0,n.jsx)(i.Bc,{className:"label-error",name:"name",component:"div"})]})})]}),(0,n.jsx)("div",{children:(0,n.jsx)(u.Z,{children:(0,n.jsxs)(o.Z,{className:"label-form",children:[L("createArticle:descriptionCat")," (",L("createArticle:en"),") ",(0,n.jsx)(d.Z,{variant:"standard",name:"description",type:"text",value:r.description,onChange:e=>{s("description",e.target.value)},className:"input-pentabell"+(t.description&&a.description?" is-invalid":"")})," ",(0,n.jsx)(i.Bc,{className:"label-error",name:"description",component:"div"})]})})})]}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsx)("div",{children:(0,n.jsx)(u.Z,{children:(0,n.jsxs)(o.Z,{className:"label-form",children:["guides",(0,n.jsx)(m.Z,{children:(0,n.jsx)(p.Z,{multiple:!0,className:"input-pentabell",id:"tags-standard",options:D.length>0?D:T,getOptionLabel:e=>e.name,value:r.guides.length>0?(D.length>0?D:T).filter(e=>r.guides?.includes(e.id)):[],onChange:(e,t)=>{let a=t.map(e=>e.id);s("guides",a),k(a)},renderInput:e=>(0,n.jsx)(d.Z,{...e,className:"input-pentabell  multiple-select",variant:"standard"})})})]})})})}),(0,n.jsxs)("div",{className:"inline-group",children:[(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(u.Z,{children:(0,n.jsxs)(o.Z,{className:"label-form",children:[L("createArticle:metaTitle"),"* (",L("createArticle:en"),") ","("," ",(0,n.jsxs)("span",{className:"char-count"+(r.metaTitle?.length>65?" text-danger":""),children:[r.metaTitle?.length," / 65"]}),")",(0,n.jsx)(d.Z,{variant:"standard",name:"metaTitle",type:"text",value:r.metaTitle,onChange:e=>{s("metaTitle",e.target.value)},className:"input-pentabell"+(t.metaTitle&&a.metaTitle?" is-invalid":"")})," ",(0,n.jsx)(i.Bc,{className:"label-error",name:"metaTitle",component:"div"})]})})]}),(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(u.Z,{children:(0,n.jsxs)(o.Z,{className:"label-form",children:[L("createArticle:url")," (",L("createArticle:en"),") ",(0,n.jsx)(d.Z,{variant:"standard",name:"url",type:"text",value:r.url,onChange:e=>{s("url",e.target.value)},className:"input-pentabell"+(t.url&&a.url?" is-invalid":"")})," ",(0,n.jsx)(i.Bc,{className:"label-error",name:"url",component:"div"})]})})]})]}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(u.Z,{children:(0,n.jsxs)(o.Z,{className:"label-form",children:[L("createArticle:metaDescription"),"* (",L("createArticle:en"),") "," ("," ",(0,n.jsxs)("span",{className:"char-count"+(r.metaDescription?.length>160?" text-danger":""),children:[r.metaDescription?.length," / 160"]})," ",")",(0,n.jsx)(d.Z,{variant:"standard",name:"metaDescription",type:"text",multiline:!0,rows:3,value:r.metaDescription,onChange:e=>{s("metaDescription",e.target.value)},className:"input-pentabell"+(t.metaDescription&&a.metaDescription?" is-invalid":"")})," ",(0,n.jsx)(i.Bc,{className:"label-error",name:"metaDescription",component:"div"})]})})]})}),(0,n.jsxs)("div",{className:"btn-container",children:[(0,n.jsx)(v.default,{text:L("global:delete"),className:"btn btn-outlined",onClick:()=>{ed("en")}}),(0,n.jsx)(v.default,{text:L("createArticle:editCategory")+" ("+L("createArticle:en")+") ",className:"btn btn-filled",type:"submit",onClick:()=>{}}),"\xa0"]})]})}}),(0,n.jsx)(i.J9,{initialValues:ei,validationSchema:ec,onSubmit:eu,enableReinitialize:"true",children:e=>{let{errors:t,touched:a,setFieldValue:s,values:r}=e;return(0,n.jsxs)(i.l0,{children:[(0,n.jsxs)("div",{className:"inline-group",children:[(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(u.Z,{children:(0,n.jsxs)(o.Z,{className:"label-form",children:[L("createArticle:nameCat"),"* (",L("createArticle:fr"),") ",(0,n.jsx)(d.Z,{variant:"standard",name:"name",type:"text",value:r.name,onChange:e=>{s("name",e.target.value),s("url",(0,f.o)(e.target.value))},className:"input-pentabell"+(t.name&&a.name?" is-invalid":"")})," ",(0,n.jsx)(i.Bc,{className:"label-error",name:"name",component:"div"})]})})]}),(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(u.Z,{children:(0,n.jsxs)(o.Z,{className:"label-form",children:[L("createArticle:descriptionCat")," (",L("createArticle:fr"),") ",(0,n.jsx)(d.Z,{variant:"standard",name:"description",type:"text",value:r.description,onChange:e=>{s("description",e.target.value)},className:"input-pentabell"+(t.description&&a.description?" is-invalid":"")})," ",(0,n.jsx)(i.Bc,{className:"label-error",name:"description",component:"div"})]})})]})]}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsx)("div",{children:(0,n.jsx)(u.Z,{children:(0,n.jsxs)(o.Z,{className:"label-form",children:["guides",(0,n.jsx)(m.Z,{children:(0,n.jsx)(p.Z,{multiple:!0,className:"input-pentabell",id:"tags-standard",options:U.length>0?U:S,getOptionLabel:e=>e.name,value:r.guides?.length>0?(U.length>0?U:S).filter(e=>r.guides?.includes(e.id)):[],onChange:(e,t)=>{let a=t.map(e=>e.id);s("guides",a),Y(a)},renderInput:e=>(0,n.jsx)(d.Z,{...e,className:"input-pentabell  multiple-select",variant:"standard"})})})]})})})}),(0,n.jsxs)("div",{className:"inline-group",children:[(0,n.jsx)("div",{children:(0,n.jsx)(u.Z,{children:(0,n.jsxs)(o.Z,{className:"label-form",children:[L("createArticle:metaTitle"),"* (",L("createArticle:fr"),") ","("," ",(0,n.jsxs)("span",{className:"char-count"+(r.metaTitle?.length>65?" text-danger":""),children:[r.metaTitle?.length," / 65"]}),")",(0,n.jsx)(d.Z,{variant:"standard",name:"metaTitle",type:"text",value:r.metaTitle,onChange:e=>{s("metaTitle",e.target.value)},className:"input-pentabell"+(t.metaTitle&&a.metaTitle?" is-invalid":"")})," ",(0,n.jsx)(i.Bc,{className:"label-error",name:"metaTitle",component:"div"})]})})}),(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(u.Z,{children:(0,n.jsxs)(o.Z,{className:"label-form",children:[L("createArticle:url")," (",L("createArticle:fr"),") ",(0,n.jsx)(d.Z,{variant:"standard",name:"url",type:"text",value:r.url,onChange:e=>{s("url",e.target.value)},className:"input-pentabell"+(t.url&&a.url?" is-invalid":"")})," ",(0,n.jsx)(i.Bc,{className:"label-error",name:"url",component:"div"})]})})]})]}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(u.Z,{children:(0,n.jsxs)(o.Z,{className:"label-form",children:[L("createArticle:metaDescription"),"* (",L("createArticle:fr"),") "," ("," ",(0,n.jsxs)("span",{className:"char-count"+(r.metaDescription?.length>160?" text-danger":""),children:[r.metaDescription?.length," / 160"]})," ",")",(0,n.jsx)(d.Z,{variant:"standard",name:"metaDescription",type:"text",multiline:!0,rows:3,value:r.metaDescription,onChange:e=>{s("metaDescription",e.target.value)},className:"input-pentabell"+(t.metaDescription&&a.metaDescription?" is-invalid":"")})," ",(0,n.jsx)(i.Bc,{className:"label-error",name:"metaDescription",component:"div"})]})})]})}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsx)("div",{children:(0,n.jsx)(u.Z,{children:(0,n.jsxs)(o.Z,{className:"label-form",children:[L("createArticle:Robotsmeta"),(0,n.jsx)(g.Z,{className:"select-pentabell",variant:"standard",value:y.Qd.filter(e=>r.robotsMeta===e),selected:r?.robotsMeta,onChange:e=>{s("robotsMeta",e.target.value)},children:y.Qd.map((e,t)=>(0,n.jsx)(h.Z,{value:e,children:e},t))}),(0,n.jsx)(i.Bc,{className:"label-error",name:"robotsMeta",component:"div"})]})})})}),(0,n.jsxs)("div",{className:"btn-container",children:[(0,n.jsx)(v.default,{text:L("global:delete"),className:"btn btn-outlined",onClick:()=>{ed("fr")}}),(0,n.jsx)(v.default,{text:L("createArticle:editCategory")+" ("+L("createArticle:fr")+") ",className:"btn btn-filled",type:"submit",onClick:()=>{}})]})]})}})]})})})]})},w=e=>{let{params:t}=e;return(0,n.jsx)(A,{categoryId:t?.id})}},93214:function(e,t,a){"use strict";a.d(t,{cU:function(){return l},xk:function(){return i},yX:function(){return r}});var n=a(83464),s=a(40257);let r=n.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),i=n.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),l=n.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"});n.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))},1255:function(e,t,a){"use strict";a.d(t,{yR:function(){return p},Py:function(){return g},xv:function(){return y},xY:function(){return x},VO:function(){return h},Ny:function(){return f}});var n=a(86484),s=a(7261),r=a(46172),i=a(93214);let l=e=>{let t=e.t;return new Promise(async(a,n)=>{i.xk.post(r.Y.categoryGuides,e.data).then(e=>{s.Am.success(t("messages:categoryAdded")),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(e?.response?.data?.status===409||e?.status===409)&&s.Am.warning(t("messages:categoryNameExists")),e&&n(e)})})},c=e=>new Promise(async(t,a)=>{try{let a=await (0,i.xk)(`${r.Y.categoryGuides}/catgory/${e}/all`);t(a.data)}catch(e){a(e)}}),u=e=>new Promise(async(t,a)=>{try{let a=await i.xk.get(`${r.Y.guides}/${e.language}/listguide`);t(a.data)}catch(e){a(e)}}),o=e=>new Promise(async(t,a)=>{try{let a=await i.xk.get(`${r.Y.categoryGuides}`,{params:{language:e.language,pageSize:e.pageSize,name:e.name,pageNumber:e.pageNumber,sortOrder:e.sortOrder}});t(a.data)}catch(e){a(e)}}),d=e=>new Promise(async(t,a)=>{try{let a=await i.xk.get(`${r.Y.categoryGuides}/${e}`);t(a.data)}catch(e){a(e)}}),m=e=>{let{data:t,language:a,id:n}=e;return new Promise(async(e,l)=>{i.xk.post(`${r.Y.categoryGuides}/${a}/${n}`,t).then(t=>{"en"===a&&s.Am.success("Category english updated successfully"),"fr"===a&&s.Am.success("Category french updated successfully"),t?.data&&e(t.data)}).catch(e=>{e?.response?.data?.status===500||e?.status===500?s.Am.error("Internal Server Error"):s.Am.error(e.response.data.message)})})},p=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:e=>l(e),onError:e=>{e.message=""}})),g=e=>(0,n.useQuery)(["categoryguides",e],async()=>await c(e)),h=e=>(0,n.useQuery)(`guidestitles${e.language}`,async()=>await u(e)),y=e=>(0,n.useQuery)(["categoryguide",e],async()=>await o(e)),x=e=>(0,n.useQuery)(["categoriesGuideData",e],async()=>await d(e)),f=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t,a)=>m(e,t,a),onError:e=>{e.message=""}}))},62953:function(e,t,a){"use strict";a.d(t,{$i:function(){return h},BF:function(){return g},Fe:function(){return i},Gc:function(){return o},HF:function(){return r},Hr:function(){return c},IZ:function(){return p},NF:function(){return u},PM:function(){return l},UJ:function(){return d},jd:function(){return m}});var n=a(86484),s=a(49443);a(99376),a(80657);let r=()=>(0,n.useMutation)({mutationFn:e=>(0,s.W3)(e),onError:e=>{e.message=""}}),i=e=>(0,n.useQuery)("opportunities",async()=>await (0,s.fH)(e)),l=()=>(0,n.useMutation)(()=>(0,s.AE)()),c=e=>(0,n.useQuery)(["opportunities",e],async()=>await (0,s.Mq)(e)),u=()=>(0,n.useMutation)({mutationFn:(e,t,a)=>(0,s.rE)(e,t,a),onError:e=>{e.message=""}}),o=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t)=>(0,s.S1)(e,t),onError:e=>{e.message=""}})),d=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t,a)=>(0,s.lU)(e,t,a),onError:e=>{e.message=""}})),m=()=>{let e=(0,n.useQueryClient)();return(0,n.useMutation)({mutationFn:(e,t,a,n)=>(0,s.yH)(e,t,a,n),onSuccess:t=>{e.invalidateQueries("files")}})},p=()=>(0,n.useQuery)("SeoOpportunities",async()=>await (0,s.yJ)()),g=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t)=>(0,s.mt)(e,t),onError:e=>{e.message=""}})),h=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:e=>{let{language:t,id:a,archive:n}=e;return(0,s.TK)(t,a,n)},onError:e=>{console.error("Error during mutation",e),e.message=""}}))},49443:function(e,t,a){"use strict";a.d(t,{AE:function(){return u},Mq:function(){return c},S1:function(){return d},TK:function(){return h},W3:function(){return i},fH:function(){return l},lU:function(){return m},mt:function(){return y},rE:function(){return o},yH:function(){return p},yJ:function(){return g}});var n=a(46172),s=a(93214),r=a(7261);let i=e=>(e.t,new Promise(async(t,a)=>{s.yX.post(`/opportunities${n.Y.applications}/${e.opportunityId}`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&a(e)})})),l=e=>new Promise(async(t,a)=>{try{let a=await s.yX.get(`${n.Y.opportunity}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyWord:e.keyWord,visibility:e.visibility,title:e.title,createdAt:e.createdAt,publishDate:e.publishDate,country:e.country,industry:e.industry,contractType:e.contractType,minExperience:e.minExperience,maxExperience:e.maxExperience,jobDescriptionLanguages:e.jobDescriptionLanguages,reference:e.reference,opportunityType:e.opportunityType,exclude:"In House"!==e.opportunityType&&"true"}});t(a.data)}catch(e){a(e)}}),c=e=>new Promise(async(t,a)=>{try{let a=await s.yX.get(`${n.Y.opportunity}/${e}`);t(a.data)}catch(e){a(e)}}),u=async()=>(await s.xk.put("/UpdateJobdescription")).data,o=e=>{let{data:t,language:a,id:i}=e;return new Promise(async(e,l)=>{s.yX.post(`${n.Y.opportunity}/${a}/${i}`,t).then(t=>{"en"===a&&r.Am.success("Opportunity english updated successfully"),"fr"===a&&r.Am.success("Opportunity french updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&l(e)})})},d=e=>{let{data:t,id:a}=e;return new Promise(async(e,i)=>{s.yX.put(`${n.Y.opportunity}/${a}`,t).then(t=>{r.Am.success("Opportunity Commun fields updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})})},m=e=>{let{id:t,title:a,typeOfFavourite:i}=e;return new Promise(async(e,l)=>{s.yX.put(`${n.Y.baseUrl}/favourite/${t}`,{type:i}).then(t=>{r.Am.success(`${i} : ${a} saved to your favorites.`),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data&&409===e.response.status&&r.Am.warning(` ${a} already in shortlist`),e&&l(e)})})},p=e=>{let{resource:t,folder:a,filename:i,body:l}=e;return new Promise(async(e,c)=>{s.cU.post(`${n.Y.files}/uploadResume/${t}/${a}/${i}`,l.formData).then(t=>{t.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data&&(400===e.response.status?e.response.data.message.includes("The resume lacks essential information")?r.Am.warn(l.t("messages:requireResume")):r.Am.warn(e.response.data.message):500===e.response.status&&r.Am.error("Internal Server Error")),e&&c(e)})})},g=()=>new Promise(async(e,t)=>{try{let t=await s.yX.get(`${n.Y.seoOpportunity}`);e(t.data)}catch(e){t(e)}}),h=(e,t,a)=>new Promise(async(i,l)=>{try{let l=await s.yX.put(`${n.Y.opportunity}/${e}/${t}/desarchiver`,{archive:a});l?.data&&(r.Am.success(`opportunity ${a?"archived":"desarchived"} successfully`),i(l.data))}catch(e){r.Am.error(`Failed to ${a?"archive":"desarchive"} the opportunity.`),l(e)}}),y=e=>{let{data:t,id:a}=e;return new Promise(async(e,i)=>{s.yX.put(`${n.Y.seoOpportunity}/${a}`,t).then(t=>{r.Am.success("Opportunity seo updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})})}},46172:function(e,t,a){"use strict";a.d(t,{Y:function(){return s},v:function(){return n}});let n=a(40257).env.NEXT_PUBLIC_BASE_API_URL,s={seo:"seoTags",auth:"/auth/signin",logout:"/auth/logout",candidatures:"/applications",signup:"/auth/signup",forgetPassword:"/auth/forgot-password",resetPassword:"/auth/reset-password",guides:"/guides",currentUser:"/users/current",updateUser:"/users",users:"/users",categoryGuides:"guidecategory",candidate:"/candidates",report:"/report",skills:"/skills",files:"/files",applications:"/applications",sliders:"/sliders",favoris:"/candidate/favourite",articles:"/articles",categories:"/categories",blog:"/blog",category:"/categories",opportunity:"/opportunities",seoOpportunity:"/seoOpportunity",newsletter:"/newsletter",contact:"/contact",favourite:"/favourite",contacts:"contacts",comments:"/comments",statistics:"/statistics",events:"/events",baseUrl:`${n}`}}},function(e){e.O(0,[6443,775,948,5788,2996,7648,3464,455,2662,7183,2296,747,3200,7584,6484,9832,7261,8467,7571,1799,3993,2412,5478,9750,4244,1774,2971,2117,1744],function(){return e(e.s=13941)}),_N_E=e.O()}]);