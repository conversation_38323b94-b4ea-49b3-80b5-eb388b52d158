"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Paper,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,CloudUpload,Description,PictureAsPdf,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/PictureAsPdf.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,CloudUpload,Description,PictureAsPdf,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,CloudUpload,Description,PictureAsPdf,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CloudUpload.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,CloudUpload,Description,PictureAsPdf,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,CloudUpload,Description,PictureAsPdf,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewOpen, setPreviewOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [extractedData, setExtractedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Supported file types\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes(\"pdf\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            color: \"error\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 52,\n            columnNumber: 42\n        }, undefined);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            color: \"primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 53,\n            columnNumber: 12\n        }, undefined);\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        // Create a temporary DOM element to parse HTML\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        // Extract potential title (first h1, h2, or strong text)\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        // Extract first paragraph as potential description\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        // Extract keywords from headings and strong text\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords: keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement(function(image) {\n                        return image.read(\"base64\").then(function(imageBuffer) {\n                            return {\n                                src: \"data:\" + image.contentType + \";base64,\" + imageBuffer\n                            };\n                        });\n                    })\n                }\n            });\n            setProgress(75);\n            // Clean up the HTML content\n            let cleanContent = result.value.replace(/<p><\\/p>/g, \"\") // Remove empty paragraphs\n            .replace(/\\s+/g, \" \") // Normalize whitespace\n            .trim();\n            // Extract metadata\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata: metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            // Convert plain text to basic HTML\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata: metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            setExtractedData(result);\n            setSuccess(true);\n            // Show preview dialog\n            setPreviewOpen(true);\n        } catch (err) {\n            console.error(\"File processing error:\", err);\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) {\n            processFile(acceptedFiles[0]);\n        }\n    }, []);\n    const { getRootProps, getInputProps, isDragActive, acceptedFiles } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing\n    });\n    const handleApplyContent = ()=>{\n        if (extractedData) {\n            onContentExtracted(extractedData.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(extractedData.metadata);\n            }\n            setPreviewOpen(false);\n            setExtractedData(null);\n        }\n    };\n    const handleClosePreview = ()=>{\n        setPreviewOpen(false);\n        setExtractedData(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        sx: {\n            mb: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: [\n                    t(\"createArticle:importDocument\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                ...getRootProps(),\n                sx: {\n                    p: 3,\n                    border: \"2px dashed\",\n                    borderColor: isDragActive ? \"primary.main\" : \"grey.300\",\n                    backgroundColor: isDragActive ? \"action.hover\" : \"background.paper\",\n                    cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\",\n                    textAlign: \"center\",\n                    transition: \"all 0.3s ease\",\n                    opacity: disabled || isProcessing ? 0.6 : 1,\n                    \"&:hover\": {\n                        borderColor: \"primary.main\",\n                        backgroundColor: \"action.hover\"\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ...getInputProps()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        sx: {\n                            fontSize: 48,\n                            color: \"primary.main\",\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:dragDropOrClick\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        sx: {\n                            mb: 2\n                        },\n                        children: [\n                            t(\"createArticle:supportedFormats\"),\n                            \": .docx, .doc, .txt\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            gap: 1,\n                            flexWrap: \"wrap\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 23\n                                }, void 0),\n                                label: \"Word (.docx)\",\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 23\n                                }, void 0),\n                                label: \"Word (.doc)\",\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 23\n                                }, void 0),\n                                label: \"Text (.txt)\",\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        gutterBottom: true,\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, undefined),\n            success && !previewOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                open: previewOpen,\n                onClose: handleClosePreview,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"h6\",\n                                    children: t(\"createArticle:previewExtractedContent\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    onClick: handleClosePreview,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        children: extractedData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: [\n                                extractedData.metadata.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        mb: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedTitle\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: extractedData.metadata.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 17\n                                }, undefined),\n                                extractedData.metadata.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        mb: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedDescription\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body2\",\n                                            children: extractedData.metadata.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"subtitle2\",\n                                    color: \"primary\",\n                                    sx: {\n                                        mb: 1\n                                    },\n                                    children: [\n                                        t(\"createArticle:extractedContent\"),\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        maxHeight: 400,\n                                        overflow: \"auto\",\n                                        border: \"1px solid\",\n                                        borderColor: \"grey.300\",\n                                        p: 2,\n                                        borderRadius: 1,\n                                        backgroundColor: \"grey.50\"\n                                    },\n                                    dangerouslySetInnerHTML: {\n                                        __html: extractedData.content\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, undefined),\n                                extractedData.warnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    severity: \"warning\",\n                                    sx: {\n                                        mt: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            children: [\n                                                t(\"createArticle:conversionWarnings\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: extractedData.warnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: warning.message\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                onClick: handleClosePreview,\n                                children: t(\"global:cancel\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                onClick: handleApplyContent,\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_CloudUpload_Description_PictureAsPdf_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 24\n                                }, void 0),\n                                children: t(\"createArticle:applyContent\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"e7V5N1Q8+U90zRa31VhbBxq3qFc=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});