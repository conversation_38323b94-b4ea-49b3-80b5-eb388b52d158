"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-gtm-module";
exports.ids = ["vendor-chunks/react-gtm-module"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-gtm-module/dist/Snippets.js":
/*!********************************************************!*\
  !*** ./node_modules/react-gtm-module/dist/Snippets.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar _warn = __webpack_require__(/*! ./utils/warn */ \"(ssr)/./node_modules/react-gtm-module/dist/utils/warn.js\");\n\nvar _warn2 = _interopRequireDefault(_warn);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// https://developers.google.com/tag-manager/quickstart\n\nvar Snippets = {\n  tags: function tags(_ref) {\n    var id = _ref.id,\n        events = _ref.events,\n        dataLayer = _ref.dataLayer,\n        dataLayerName = _ref.dataLayerName,\n        preview = _ref.preview,\n        auth = _ref.auth;\n\n    var gtm_auth = '&gtm_auth=' + auth;\n    var gtm_preview = '&gtm_preview=' + preview;\n\n    if (!id) (0, _warn2.default)('GTM Id is required');\n\n    var iframe = '\\n      <iframe src=\"https://www.googletagmanager.com/ns.html?id=' + id + gtm_auth + gtm_preview + '&gtm_cookies_win=x\"\\n        height=\"0\" width=\"0\" style=\"display:none;visibility:hidden\" id=\"tag-manager\"></iframe>';\n\n    var script = '\\n      (function(w,d,s,l,i){w[l]=w[l]||[];\\n        w[l].push({\\'gtm.start\\': new Date().getTime(),event:\\'gtm.js\\', ' + JSON.stringify(events).slice(1, -1) + '});\\n        var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!=\\'dataLayer\\'?\\'&l=\\'+l:\\'\\';\\n        j.async=true;j.src=\\'https://www.googletagmanager.com/gtm.js?id=\\'+i+dl+\\'' + gtm_auth + gtm_preview + '&gtm_cookies_win=x\\';\\n        f.parentNode.insertBefore(j,f);\\n      })(window,document,\\'script\\',\\'' + dataLayerName + '\\',\\'' + id + '\\');';\n\n    var dataLayerVar = this.dataLayer(dataLayer, dataLayerName);\n\n    return {\n      iframe: iframe,\n      script: script,\n      dataLayerVar: dataLayerVar\n    };\n  },\n  dataLayer: function dataLayer(_dataLayer, dataLayerName) {\n    return '\\n      window.' + dataLayerName + ' = window.' + dataLayerName + ' || [];\\n      window.' + dataLayerName + '.push(' + JSON.stringify(_dataLayer) + ')';\n  }\n};\n\nmodule.exports = Snippets;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gtm-module/dist/Snippets.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gtm-module/dist/TagManager.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-gtm-module/dist/TagManager.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar _Snippets = __webpack_require__(/*! ./Snippets */ \"(ssr)/./node_modules/react-gtm-module/dist/Snippets.js\");\n\nvar _Snippets2 = _interopRequireDefault(_Snippets);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar TagManager = {\n  dataScript: function dataScript(dataLayer) {\n    var script = document.createElement('script');\n    script.innerHTML = dataLayer;\n    return script;\n  },\n  gtm: function gtm(args) {\n    var snippets = _Snippets2.default.tags(args);\n\n    var noScript = function noScript() {\n      var noscript = document.createElement('noscript');\n      noscript.innerHTML = snippets.iframe;\n      return noscript;\n    };\n\n    var script = function script() {\n      var script = document.createElement('script');\n      script.innerHTML = snippets.script;\n      return script;\n    };\n\n    var dataScript = this.dataScript(snippets.dataLayerVar);\n\n    return {\n      noScript: noScript,\n      script: script,\n      dataScript: dataScript\n    };\n  },\n  initialize: function initialize(_ref) {\n    var gtmId = _ref.gtmId,\n        _ref$events = _ref.events,\n        events = _ref$events === undefined ? {} : _ref$events,\n        dataLayer = _ref.dataLayer,\n        _ref$dataLayerName = _ref.dataLayerName,\n        dataLayerName = _ref$dataLayerName === undefined ? 'dataLayer' : _ref$dataLayerName,\n        _ref$auth = _ref.auth,\n        auth = _ref$auth === undefined ? '' : _ref$auth,\n        _ref$preview = _ref.preview,\n        preview = _ref$preview === undefined ? '' : _ref$preview;\n\n    var gtm = this.gtm({\n      id: gtmId,\n      events: events,\n      dataLayer: dataLayer || undefined,\n      dataLayerName: dataLayerName,\n      auth: auth,\n      preview: preview\n    });\n    if (dataLayer) document.head.appendChild(gtm.dataScript);\n    document.head.insertBefore(gtm.script(), document.head.childNodes[0]);\n    document.body.insertBefore(gtm.noScript(), document.body.childNodes[0]);\n  },\n  dataLayer: function dataLayer(_ref2) {\n    var _dataLayer = _ref2.dataLayer,\n        _ref2$dataLayerName = _ref2.dataLayerName,\n        dataLayerName = _ref2$dataLayerName === undefined ? 'dataLayer' : _ref2$dataLayerName;\n\n    if (window[dataLayerName]) return window[dataLayerName].push(_dataLayer);\n    var snippets = _Snippets2.default.dataLayer(_dataLayer, dataLayerName);\n    var dataScript = this.dataScript(snippets);\n    document.head.insertBefore(dataScript, document.head.childNodes[0]);\n  }\n};\n\nmodule.exports = TagManager;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gtm-module/dist/TagManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gtm-module/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-gtm-module/dist/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar _TagManager = __webpack_require__(/*! ./TagManager */ \"(ssr)/./node_modules/react-gtm-module/dist/TagManager.js\");\n\nvar _TagManager2 = _interopRequireDefault(_TagManager);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nmodule.exports = _TagManager2.default;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ3RtLW1vZHVsZS9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGtCQUFrQixtQkFBTyxDQUFDLDhFQUFjOztBQUV4Qzs7QUFFQSx1Q0FBdUMsdUNBQXVDOztBQUU5RSIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9yZWFjdC1ndG0tbW9kdWxlL2Rpc3QvaW5kZXguanM/NmNhMyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBfVGFnTWFuYWdlciA9IHJlcXVpcmUoJy4vVGFnTWFuYWdlcicpO1xuXG52YXIgX1RhZ01hbmFnZXIyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChfVGFnTWFuYWdlcik7XG5cbmZ1bmN0aW9uIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQob2JqKSB7IHJldHVybiBvYmogJiYgb2JqLl9fZXNNb2R1bGUgPyBvYmogOiB7IGRlZmF1bHQ6IG9iaiB9OyB9XG5cbm1vZHVsZS5leHBvcnRzID0gX1RhZ01hbmFnZXIyLmRlZmF1bHQ7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gtm-module/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gtm-module/dist/utils/warn.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-gtm-module/dist/utils/warn.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nvar warn = function warn(s) {\n  console.warn('[react-gtm]', s);\n};\n\nexports[\"default\"] = warn;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ3RtLW1vZHVsZS9kaXN0L3V0aWxzL3dhcm4uanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Y7QUFDQTtBQUNBOztBQUVBLGtCQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGVudGFiZWxsLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWd0bS1tb2R1bGUvZGlzdC91dGlscy93YXJuLmpzPzQxZGYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xudmFyIHdhcm4gPSBmdW5jdGlvbiB3YXJuKHMpIHtcbiAgY29uc29sZS53YXJuKCdbcmVhY3QtZ3RtXScsIHMpO1xufTtcblxuZXhwb3J0cy5kZWZhdWx0ID0gd2FybjsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gtm-module/dist/utils/warn.js\n");

/***/ })

};
;