"use client";
import {
  Container,
  Grid,
  AccordionSummary,
  AccordionDetails,
  Accordion,
} from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import SvgexpandIcon from "../../assets/images/icons/arrowUp.svg";

import SvgCallendar from "@/assets/images/icons/yellow/yellowCalendar.svg";

export default function UAELaborLaws() {
  const [expanded, setExpanded] = useState(1);
  const { t } = useTranslation();
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  return (
    <div id="labor-tn-laws" className="custom-max-width">
      <Container>
        <h2 className="heading-h1">{t("dubai:dubaiLabor:title")}</h2>

        <Grid className="container" container columnSpacing={3} rowSpacing={2}>
          <Grid item xs={12} sm={12} key={1}>
            <Accordion
              elevation={0}
              expanded={expanded === 1}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(1)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("dubai:dubaiLabor:workingHours:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:workingHours:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("dubai:dubaiLabor:workingHours:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:workingHours:title2")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("dubai:dubaiLabor:workingHours:description2")}</li>
                    <li>{t("dubai:dubaiLabor:workingHours:description3")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={2}>
            <Accordion
              elevation={0}
              expanded={expanded === 2}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(2)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("dubai:dubaiLabor:employmentContracts:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:employmentContracts:title1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("dubai:dubaiLabor:employmentContracts:data1")}</li>
                    <li>{t("dubai:dubaiLabor:employmentContracts:data2")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={3}>
            <Accordion
              elevation={0}
              expanded={expanded === 3}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(3)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("dubai:dubaiLabor:termination:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:termination:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("dubai:dubaiLabor:termination:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:termination:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("dubai:dubaiLabor:termination:description2")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("dubai:dubaiLabor:termination:data1")}</li>
                    <li>{t("dubai:dubaiLabor:termination:data2")}</li>
                    <li>{t("dubai:dubaiLabor:termination:data3")}</li>
                    <li>{t("dubai:dubaiLabor:termination:data4")}</li>
                    <li>{t("dubai:dubaiLabor:termination:data5")}</li>
                    <li>{t("dubai:dubaiLabor:termination:data6")}</li>
                    <li>{t("dubai:dubaiLabor:termination:data7")}</li>
                    <li>{t("dubai:dubaiLabor:termination:data8")}</li>
                    <li>{t("dubai:dubaiLabor:termination:data9")}</li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:termination:title3")}
                  </p>
                  <p className="service-description paragraph">
                    {t("dubai:dubaiLabor:termination:description3")}{" "}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:termination:title4")}
                  </p>
                  <p className="service-description paragraph">
                    {t("dubai:dubaiLabor:termination:description4")}{" "}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={4}>
            <Accordion
              elevation={0}
              expanded={expanded === 4}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(4)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("dubai:dubaiLabor:payroll:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:payroll:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("dubai:dubaiLabor:payroll:description")}
                  </p>
                </div>
                <div className="payroll-tn">
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("dubai:dubaiLabor:payroll:fiscalYear:title")}
                    </p>
                    <p className="date">
                      {t("dubai:dubaiLabor:payroll:fiscalYear:date1")}
                      <br />
                      {t("dubai:dubaiLabor:payroll:fiscalYear:date2")}
                    </p>
                    <p className="paragraph">
                      {t("dubai:dubaiLabor:payroll:fiscalYear:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("dubai:dubaiLabor:payroll:payrollCycle:title")}
                    </p>
                    <p className="date">
                      {t("dubai:dubaiLabor:payroll:payrollCycle:date")}
                    </p>
                    <p className="paragraph">
                      {t("dubai:dubaiLabor:payroll:payrollCycle:description")}
                    </p>
                  </div>

                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("dubai:dubaiLabor:payroll:minimumWage:title")}
                    </p>
                    <p className="date">
                      {t("dubai:dubaiLabor:payroll:minimumWage:wage")}
                      <br />
                      {t("dubai:dubaiLabor:payroll:minimumWage:date")}
                    </p>
                    <p className="paragraph">
                      {t("dubai:dubaiLabor:payroll:minimumWage:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("dubai:dubaiLabor:payroll:payrollManagement:title")}
                    </p>
                    <p className="date">
                      {t("dubai:dubaiLabor:payroll:payrollManagement:date1")}
                      <br />
                      {t("dubai:dubaiLabor:payroll:payrollManagement:date2")}
                    </p>
                    <p className="paragraph">
                      {t(
                        "dubai:dubaiLabor:payroll:payrollManagement:description"
                      )}
                    </p>
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={5}>
            <Accordion
              elevation={0}
              expanded={expanded === 5}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(5)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("dubai:dubaiLabor:leaveEntitlements:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:leaveEntitlements:description")}
                  </p>
                  <div className="holidays-dates">
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS1:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS1:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS2:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS2:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS3:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS3:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS4:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS4:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS5:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS5:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS6:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS6:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS7:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS7:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS8:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "dubai:dubaiLabor:leaveEntitlements:leaves:dataS8:title"
                        )}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "dubai:dubaiLabor:leaveEntitlements:leaves:maternityLeave:title"
                    )}
                  </p>
                  <p className="service-description paragraph">
                    {t(
                      "dubai:dubaiLabor:leaveEntitlements:leaves:maternityLeave:description1"
                    )}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "dubai:dubaiLabor:leaveEntitlements:leaves:sickLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "dubai:dubaiLabor:leaveEntitlements:leaves:sickLeave:description"
                    )}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={6}>
            <Accordion
              elevation={0}
              expanded={expanded === 6}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(6)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("dubai:dubaiLabor:tax:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("dubai:dubaiLabor:tax:description")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:tax:title1")}
                  </p>

                  <p className="service-description paragraph">
                    {t("dubai:dubaiLabor:tax:description1")}
                  </p>

                  <ul className="service-description paragraph">
                    <li> {t("dubai:dubaiLabor:tax:data1")}</li>
                    <li> {t("dubai:dubaiLabor:tax:data2")}</li>
                    <li> {t("dubai:dubaiLabor:tax:data3")}</li>
                  </ul>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {" "}
                    {t("dubai:dubaiLabor:tax:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("dubai:dubaiLabor:tax:description2")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("dubai:dubaiLabor:tax:dataS1")}</li>
                    <li>{t("dubai:dubaiLabor:tax:dataS2")}</li>
                    <li>{t("dubai:dubaiLabor:tax:dataS3")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>

          <Grid item xs={12} sm={12} key={7}>
            <Accordion
              elevation={0}
              expanded={expanded === 7}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(7)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("dubai:dubaiLabor:visa:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("dubai:dubaiLabor:visa:description1")}
                    <br />
                    {t("dubai:dubaiLabor:visa:description2")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:visa:titleResidence")}
                  </p>

                  <p className="service-description paragraph">
                    {t("dubai:dubaiLabor:visa:descriptionResidence")}
                  </p>
                </div>{" "}
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:visa:title1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:visa:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("dubai:dubaiLabor:visa:description3")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("dubai:dubaiLabor:visa:title3")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("dubai:dubaiLabor:visa:data1")}</li>
                    <li>{t("dubai:dubaiLabor:visa:data2")}</li>
                    <li>{t("dubai:dubaiLabor:visa:data3")}</li>
                    <li>{t("dubai:dubaiLabor:visa:data4")}</li>
                    <li>{t("dubai:dubaiLabor:visa:data5")}</li>
                    <li>{t("dubai:dubaiLabor:visa:data6")}</li>
                    <li>{t("dubai:dubaiLabor:visa:data7")}</li>
                    <li>{t("dubai:dubaiLabor:visa:data8")}</li>
                  </ul>
                  <p className="service-description paragraph">
                    {t("dubai:dubaiLabor:visa:note")}

                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}
