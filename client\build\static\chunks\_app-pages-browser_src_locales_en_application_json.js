"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_locales_en_application_json"],{

/***/ "(app-pages-browser)/./src/locales/en/application.json":
/*!*****************************************!*\
  !*** ./src/locales/en/application.json ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"job":"  Job","date":"Application date","status":"Status","action":"action","details":"Job Details","candidaturelist":"List of applications","applicationByOpportunity":"List Of Applications By Opportunity","active":"Active","expired":"Expired","rejected":"Rejected","industrie":"Industry","accepted":"Accepted","pending":"Pending","messagesuccessupload":"Resume uploaded successfully","viewDetailsCandidate":"view Details Candidate","candidatures":"Applications","applicationDate":"Application date","emailcandidat":"Candidate email","searchByNote":"Search By Note","searchbyjobTitle":"Search By job Title","fullName":"Full Name","dateOfBirth":"Date of birth","phone":"Phone","Genre":"Gender","Country":"Country","Resume":"Resume","Note":"Note","ApplicationDate":"Application date","from":"From","jobTitle":"Job title","to":"To","Resumes":"Resumes","Status":"Status","namecandidat":"Candidate name","search":"Search with Job title...","recent":"Recent Applications","deleteApplication":"Do you really want to cancel your application ?","recentApplications":"Recent applications"}');

/***/ })

}]);