"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_features_blog_components_OptimizedArticleContent_jsx"],{

/***/ "(app-pages-browser)/./src/features/blog/components/OptimizedArticleContent.jsx":
/*!******************************************************************!*\
  !*** ./src/features/blog/components/OptimizedArticleContent.jsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst OptimizedArticleContent = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function OptimizedArticleContent(param) {\n    let { htmlContent } = param;\n    _s();\n    // Sanitize and optimize HTML content\n    const optimizedContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!htmlContent) return \"\";\n        // Basic HTML sanitization and optimization\n        return htmlContent.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\") // Remove scripts\n        .replace(/<iframe\\b[^>]*>/gi, (match)=>{\n            // Lazy load iframes\n            return match.replace(/src=/gi, \"data-src=\").replace(/<iframe/, '<iframe loading=\"lazy\"');\n        }).replace(/<img\\b[^>]*>/gi, (match)=>{\n            // Optimize images\n            if (!match.includes(\"loading=\")) {\n                return match.replace(/<img/, '<img loading=\"lazy\"');\n            }\n            return match;\n        });\n    }, [\n        htmlContent\n    ]);\n    if (!optimizedContent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"blog-content-placeholder\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-full mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\OptimizedArticleContent.jsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-5/6 mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\OptimizedArticleContent.jsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-4/6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\OptimizedArticleContent.jsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\OptimizedArticleContent.jsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"blog-content optimized\",\n        dangerouslySetInnerHTML: {\n            __html: optimizedContent\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\OptimizedArticleContent.jsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}, \"a2pCLaCCi4894QpiHe1hLjNbD/w=\")), \"a2pCLaCCi4894QpiHe1hLjNbD/w=\");\n_c1 = OptimizedArticleContent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (OptimizedArticleContent);\nvar _c, _c1;\n$RefreshReg$(_c, \"OptimizedArticleContent$memo\");\n$RefreshReg$(_c1, \"OptimizedArticleContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/OptimizedArticleContent.jsx\n"));

/***/ })

}]);