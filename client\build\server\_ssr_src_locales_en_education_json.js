"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_en_education_json";
exports.ids = ["_ssr_src_locales_en_education_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/en/education.json":
/*!***************************************!*\
  !*** ./src/locales/en/education.json ***!
  \***************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"titeleeditedu":"Edit Education","titleajouteredu":"Add Education","degree":"diploma*","startdate":"Start date*","enddate":"End date*","university":"University*","fieldofstudy":"fieldOfStudy","message":" Do you really want to delete education ","addsuccess":"Education added success","noEducations":"No education"}');

/***/ })

};
;