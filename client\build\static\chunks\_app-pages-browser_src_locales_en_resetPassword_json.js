"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_locales_en_resetPassword_json"],{

/***/ "(app-pages-browser)/./src/locales/en/resetPassword.json":
/*!*******************************************!*\
  !*** ./src/locales/en/resetPassword.json ***!
  \*******************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"title":"Reset Password","newPassword":"New password","confirmNewPassword":"Confirm new password","resetPassword":"Reset Password","failed":"Request Expired","description":"The link has expired. You may have already reset your password or the time limit has expired. Please try resetting your password again","tryAgain":"Try Again"}');

/***/ })

}]);