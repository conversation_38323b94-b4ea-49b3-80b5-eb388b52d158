import HttpException from '@/utils/exceptions/http.exception';
import { auth, sendEmail } from '@/utils/services';
import UserModel from '../user.model';
import crypto from 'crypto';
import { UserI } from '../user.interfaces';
import bcrypt from 'bcrypt';
import <PERSON><PERSON><PERSON><PERSON> from 'mailchecker';
import * as fs from 'fs';
import axios from 'axios';
import mammoth from 'mammoth';
import WordExtractor from 'word-extractor';
import pdf from 'pdf-parse';
import { v4 as uuidv4 } from 'uuid';
import jwt from 'jsonwebtoken';
import validator from 'validator';
import https from 'https';

import { computeChecksumFromBuffer, validateEmailFormat, verifyRole, verifyRoles } from '@/utils/helpers/functions';
import { Role } from '@/utils/helpers/constants';
import CandidatModel from '../../candidat/candidat.model';
import { MESSAGES } from '@/utils/helpers/messages';
import FilesService from '@/apis/storage/files.service';
import candidatModel from '../../candidat/candidat.model';
import alertModel from '@/apis/alert/alert.model';
import userModel from '../user.model';
import userSettings from '../../settings/settings.model';
import { sendNotification } from '@/utils/config/socket';
import { CommentModel } from '@/apis/article/commentaire/commentaire.model';
import { IsPhoneNumber } from 'class-validator';
import notificationModel from '@/apis/notifications/notification.model';
import loginRecordsModel from '@/apis/auth/login-records/login-records.model';
import favouriteModel from '@/apis/Favourite/favourite.model';
import opportunityApplicationModel from '@/apis/opportunity/model/opportunity.application.model';
import candidatExperienceModel from '@/apis/candidat/experience/candidat.experience.model';
import candidatEducationModel from '@/apis/candidat/education/candidat.education.model';
import candidatCertificationModel from '@/apis/candidat/certification/candidat.certification.model';
class UserService {
    private User = UserModel;
    private Comment = CommentModel;
    private Alert = alertModel;
    private userSettings = userSettings;
    private notifications = notificationModel;
    private loginRecord = loginRecordsModel;
    private favorite = favouriteModel;
    private Applications = opportunityApplicationModel;
    private readonly Candidat = CandidatModel;
    private readonly fileService = new FilesService();

    public async getAll(queries: any): Promise<any> {
        const { roles, status, country, isArchived, paginated, searchQuery } = queries;

        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 5;

        const queryConditions: any = {};

        if (roles) queryConditions['roles'] = { $in: roles };
        if (status) queryConditions['status'] = status;
        if (country) queryConditions['country'] = country;
        if (isArchived) queryConditions['isArchived'] = isArchived;

        if (searchQuery) {
            queryConditions['$or'] = [
                { firstName: new RegExp(`.*${searchQuery}.*`, 'i') },
                { lastName: new RegExp(`.*${searchQuery}.*`, 'i') },
                { jobTitle: new RegExp(`.*${searchQuery}.*`, 'i') },
            ];
        }

        const totalUsers = await this.User.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalUsers / pageSize);

        let users;
        if (paginated) {
            users = await this.User.find(queryConditions)
                .sort({ createdAt: 'desc' })
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize);
        } else {
            users = await this.User.find(queryConditions).sort({ createdAt: 'desc' });
        }

        return {
            pageNumber,
            pageSize,
            totalPages,
            totalUsers,
            users,
        };
    }

    public async createUsers(user: any): Promise<void> {
        if (!MailChecker.isValid(user.email)) {
            throw new HttpException(400, MESSAGES.AUTH.INVALID_EMAIL);
        }

        const oldUser = await this.User.findOne({ email: user.email });
        if (oldUser) throw new HttpException(409, MESSAGES.USER.EMAIL_UNIQUE);

        const password = await auth.randomPassword();

        const hashedPassword = await auth.hashPassword(password);
        const token = crypto.randomBytes(20).toString('hex');

        const formatfirstName = (firstName: string): string =>
            firstName
                .split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ');

        user.firstName = formatfirstName(user.firstName);

        const newUser = await this.User.create({
            ...user,
            password: hashedPassword,
            confirmAccountToken: token,
            isActive: true,
        });

        const defaultUserSettings = {
            user: newUser._id,
            notifications: {
                newJobAlerts: {
                    email: true,
                    website: true,
                },
                appliedJobStatusUpdates: {
                    email: true,
                    website: true,
                },
                newsLetter: {
                    email: true,
                    website: true,
                },
            },
        };

        await userSettings.create(defaultUserSettings);

        const alertData = {
            isActive: true,
        };

        const createdAlert = await alertModel.create({
            ...alertData,
            createdBy: newUser._id,
        });

        let updatedUser: any = { alerts: createdAlert._id };

        if (user.roles.includes(Role.CANDIDATE)) {
            const candidate = await new candidatModel({
                user: newUser._id,
                emails: [newUser.email],
            }).save();

            updatedUser = { ...updatedUser, candidate: candidate._id };
        }

        await userModel.findByIdAndUpdate(newUser._id, updatedUser);

        const notificationMessage = 'Welcome to Pentabell, we are happy to see you here!';

        await sendNotification({
            receiver: newUser._id,
            sender: null,
            message: notificationMessage,
            link: `${process.env.LOGIN_LINK}`,
        });

        sendEmail({
            to: newUser.email,
            subject: 'Welcome to Pentabell website',
            template: 'newUser',
            context: {
                firstName: newUser.firstName,
                email: newUser.email,
                password: password,
                link: process.env.LOGIN_LINK,
            },
        });
    }

    public async deleteUser(id: string): Promise<void> {
        try {
            const oldUser = await this.User.findById(id);
            if (!oldUser) throw new HttpException(404, MESSAGES.USER.USER_NOT_FOUND);
            await this.User.findByIdAndUpdate(id, { isArchived: !oldUser.isArchived }, { new: true });
        } catch (error) {
            console.error(error);
        }
    }


    public async desactiverAccount(id: string): Promise<void> {
        try {
            const oldUser = await this.User.findById(id);
            if (!oldUser) {
                throw new HttpException(404, MESSAGES.USER.USER_NOT_FOUND);
            }
            const candidat = await this.Candidat.findOne({ user: id });

            if (candidat) {
                const experienceIds = candidat.experiences;
                const educationIds = candidat.educations;
                const certificationIds = candidat.certifications;
                await this.Applications.deleteMany({ candidat: candidat._id })

                await Promise.all([
                    candidatExperienceModel.deleteMany({ _id: { $in: experienceIds } }),
                    candidatEducationModel.deleteMany({ _id: { $in: educationIds } }),
                    candidatCertificationModel.deleteMany({ _id: { $in: certificationIds } }),
                    this.Candidat.deleteOne({ user: id })
                ]);
            }
            await this.Alert.deleteMany({ createdBy: id });
            await this.userSettings.deleteMany({ user: id });
            await this.loginRecord.deleteMany({ user: id });
            await this.favorite.deleteMany({ user: id });
            await this.notifications.deleteMany({ receiver: id });
            await this.Comment.deleteMany({ user: id });
            await this.User.findByIdAndDelete(id);
        } catch (error) {
            throw new HttpException(500, MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    public async updateUserDetails(userId: string, updates: Partial<UserI>, adminId: string): Promise<UserI | null> {
        const admin = await this.User.findById(adminId);
        if (!admin || !verifyRoles(admin.roles, [Role.ADMIN])) {
            throw new HttpException(403, MESSAGES.AUTH.UNAUTHORIZED);
        }
        const user = await this.User.findById(userId);
        if (!user) {
            throw new HttpException(404, MESSAGES.USER.USER_NOT_FOUND);
        }
        if (updates.password) {
            updates.password = await bcrypt.hash(updates.password, 10);
        }
        const updatedUser = await this.User.findByIdAndUpdate(userId, updates, { new: true });
        return updatedUser;
    }

    public async updateUser(id: string, user: any, currentUser: UserI): Promise<any> {
        const existingUser = await this.get(id);
        if (verifyRole(Role.ADMIN, currentUser) === true) {
            if (user.email && user.email !== existingUser.email) {
                const isEmailUnique = await this.User.exists({ email: user.email });

                if (isEmailUnique) {
                    throw new HttpException(409, MESSAGES.USER.EMAIL_UNIQUE);
                }
            }
            return await this.User.findOneAndUpdate({ email: existingUser.email }, { $set: { ...user } }, { new: true });
        }
        if (currentUser._id.toString() === id) {
            if (user.nationalities) {
                await this.Candidat.findOneAndUpdate({ user: id }, { nationalities: user.nationalities });
            }
            return await this.User.findByIdAndUpdate(
                id,
                { ...user },
                { new: true },
            );
        }
    }
    public async get(id: string): Promise<any> {
        const user = await this.User.findById(id).populate({
            path: 'candidate',
            populate: [
                { path: 'certifications', model: 'Certification' },
                { path: 'educations', model: 'Education' },
                { path: 'experiences', model: 'Experience' },
            ],
        });

        if (!user) {
            throw new HttpException(404, MESSAGES.USER.USER_NOT_FOUND);
        }
        return user;
    }

    public async addPhonetouser(id: string, phone: string): Promise<void> {
        try {
            const candidat = await this.Candidat.findOne({ user: id });
            if (!candidat) {
                throw new HttpException(404, MESSAGES.USER.USER_NOT_FOUND);
            }
            if (candidat.phones.includes(phone)) {
                throw new HttpException(400, MESSAGES.USER.PHONE_ALREADY_EXISTS);
            }
            candidat.phones.push(phone);
            await candidat.save();
        } catch (error) {
            throw new HttpException(500, MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    public async addEmailToUser(id: string, email: string): Promise<void> {
        try {
            validateEmailFormat(email);

            const candidat = await this.Candidat.findOne({ user: id });
            if (!candidat) {
                throw new HttpException(404, MESSAGES.USER.USER_NOT_FOUND);
            }
            if (candidat.emails.includes(email)) {
                throw new HttpException(400, MESSAGES.USER.EMAIL_ALREADY_EXISTS);
            }
            candidat.emails.push(email);
            await candidat.save();

        } catch (error) {
            throw new HttpException(500, MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    public async getByEmail(email: string): Promise<UserI> {
        const user = await this.User.findOne({ email });
        if (!user) throw new HttpException(404, MESSAGES.USER.USER_NOT_FOUND);
        return user;
    }

    public async EditEmail(id: string, oldEmail: string, newEmail: string): Promise<void> {
        try {
            const candidat = await this.Candidat.findOne({ user: id });
            if (!candidat) {
                throw new HttpException(404, MESSAGES.USER.USER_NOT_FOUND);
            }
            const emailIndex = candidat.emails.indexOf(oldEmail);
            if (emailIndex === -1) {
                throw new HttpException(400, MESSAGES.USER.EMAIL_NOT_FOUND);
            }

            if (candidat.emails.includes(newEmail)) {
                throw new HttpException(400, MESSAGES.USER.EMAIL_ALREADY_EXISTS);
            }

            candidat.emails[emailIndex] = newEmail;
            await candidat.save();
        } catch (error) {
            throw new HttpException(500, MESSAGES.GENERAL.SERVER_ERROR);
        }
    }

    public async deleteEmail(id: string, email: string): Promise<void> {
        const candidat = await this.Candidat.findOne({ user: id });
        if (!candidat) {
            throw new HttpException(404, MESSAGES.USER.USER_NOT_FOUND);
        }
        candidat.emails = candidat.emails.filter((p) => p !== email);
        await candidat.save()

    }
    public async deletePhone(id: string, phone: string): Promise<void> {
        const candidat = await this.Candidat.findOne({ user: id });
        if (!candidat) {
            throw new HttpException(404, MESSAGES.USER.USER_NOT_FOUND);
        }
        candidat.phones = candidat.phones.filter((p) => p !== phone);

        await candidat.save()

    }
    public async EditPhone(id: string, oldPhone: string, newPhone: string): Promise<void> {
        try {
            const candidat = await this.Candidat.findOne({ user: id });
            if (!candidat) {
                throw new HttpException(404, MESSAGES.USER.USER_NOT_FOUND);
            }
            const phoneIndex = candidat.phones.indexOf(oldPhone)
            if (phoneIndex === -1) {
                throw new HttpException(400, MESSAGES.CANDIDATE.NOT_FOUND)
            }
            candidat.phones[phoneIndex] = newPhone;
            await candidat.save();
        } catch (error) {
            throw new HttpException(500, MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    public async importUsers(users: any): Promise<void> {
        users = JSON.parse(users.buffer.toString('utf-8'));
        const createdUsers: any = [];
        for (const user of users) {
            const foundUser = await this.User.findOne({ email: user.user_email });
            if (!foundUser && MailChecker.isValid(user.user_email)) {
                if (!user.display_name || user.display_name === '') continue;

                const createdUser = await this.User.create({
                    firstName: this.extractFirstAndLastName(user.display_name).firstName,
                    lastName: this.extractFirstAndLastName(user.display_name).lastName,
                    email: user.user_email.toLowerCase().trim(),
                    roles: Role.CANDIDATE,
                    isActive: true,
                    createdAt: new Date(user.user_registered),
                });

                const createdCandidate = await this.Candidat.create({
                    user: createdUser._id,
                    createdAt: new Date(user.user_registered),
                    updatedAt: new Date(),
                });

                const resetPasswordToken = jwt.sign({ userId: createdUser._id }, process.env.RESET_PASSWORD_TOKEN_PRIVATE_KEY as string, {
                    expiresIn: '336h',
                });

                await this.User.findByIdAndUpdate(createdUser._id, { $set: { candidate: createdCandidate._id, resetPasswordToken } });

                createdUsers.push({
                    fullName: `${createdUser.firstName} ${createdUser.lastName}`,
                    email: createdUser.email,
                    link: `https://www.pentabell.com/reset-password?token=${resetPasswordToken}`,
                });
            }
        }

        fs.writeFileSync('new_users.json', JSON.stringify(createdUsers), 'utf-8');
    }

    public async updateUsers() {
        const users = await this.User.find();
        const updatedUsers: any = [];

        for (const user of users) {
            const resetPasswordToken = jwt.sign({ userId: user._id }, process.env.RESET_PASSWORD_TOKEN_PRIVATE_KEY as string, {
                expiresIn: '336h',
            });

            user.resetPasswordToken = resetPasswordToken;

            const updatedUser = await this.User.findByIdAndUpdate(user._id, user, { new: true });

            updatedUsers.push({
                fullName: `${updatedUser?.firstName} ${updatedUser?.lastName}`,
                email: updatedUser?.email,
                link: `https://www.pentabell.com/reset-password?token=${resetPasswordToken}`,
            });
        }
        fs.writeFileSync('updated_users.json', JSON.stringify(updatedUsers), 'utf-8');
    }

    public async attachResumeToCandidate(postmeta: any) {
        postmeta = JSON.parse(postmeta.buffer.toString('utf-8'));

        const agent = new https.Agent({
            rejectUnauthorized: false,
        });

        const extractUrl = (string: string) => {
            const regex = /(https?:\/\/[^\s"]+)/;
            const match = string.match(regex);
            return match ? match[0] : '';
        };

        const extractEmails = (text: string) => {
            const words = text.split(/\s+/);

            return words.filter(word => validator.isEmail(word));
        };

        for (const post of postmeta) {
            let extractedUrl = '';

            if (post.meta_key === '_candidate_cv_attachment')
                extractedUrl = extractUrl(post.meta_value.replace(/\/wp-job-board-pro-uploads\/_candidate_cv_attachment/g, ''));
            else
                extractedUrl = `https://www.pentabell.com/wp-content/uploads/${post.meta_value.replace(
                    /\/wp-job-board-pro-uploads\/_candidate_cv_attachment/g,
                    '',
                )}`;

            try {
                const response = await axios.get(extractedUrl, { responseType: 'arraybuffer', httpsAgent: agent });
                if (response.status !== 200) {
            
                    continue;
                }

                let extractedText;
                let extension;
                let mimeType;

                if (extractedUrl.endsWith('.docx')) {
                    const textResult = await mammoth.extractRawText({ buffer: response.data });
                    extractedText = textResult.value;
                    extension = 'docx';
                    mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                } else if (extractedUrl.endsWith('.doc')) {
                    const extractor = new WordExtractor();
                    const doc = await extractor.extract(response.data);
                    extractedText = doc.getBody();
                    extension = 'doc';
                    mimeType = 'application/msword';
                } else if (extractedUrl.endsWith('.pdf')) {
                    const data = await pdf(response.data);
                    extractedText = data.text;
                    extension = 'pdf';
                    mimeType = 'application/pdf';
                } else {
                    continue;
                }

                const emails = extractEmails(extractedText);

                const foundUsers = await this.User.find({ email: { $in: emails } });
                if (foundUsers.length === 0) {
                    continue;
                }

                const resource = 'candidates';

                const extractYear = (string: string) => {
                    const regex = /\b(20\d{2})\b/;
                    const match = string.match(regex);
                    return match ? match[0] : null;
                };
                const folder = Number(extractYear(post.meta_value));

                const uuid = uuidv4().replace(/-/g, '');
                const fileName = uuid + '.' + extension;
                const urlParts = extractedUrl.split('/');
                const originalName = decodeURIComponent(urlParts[urlParts.length - 1]);
                const fileData = {
                    resource: resource,
                    folder: String(folder),
                    ipSender: undefined,
                    uuid: uuid,
                    originalName: originalName,
                    fileName: fileName,
                    fileType: mimeType as string,
                    fileSize: response.data.length,
                    checksum: computeChecksumFromBuffer(response.data),
                };
                const folderPath = `uploads/${resource}/${folder}`;
                const uploadPath = `uploads/${resource}/${folder}/${fileName}`;

                if (!fs.existsSync(folderPath)) {
                    fs.mkdirSync(folderPath, { recursive: true });
                }

                await this.fileService.createFile(fileData);
                await fs.writeFileSync(uploadPath, response.data);

                for (const user of foundUsers) {
                    const foundCandidate = await this.Candidat.findById(user.candidate);
                    if (!foundCandidate) continue;
                    await this.Candidat.findByIdAndUpdate(foundCandidate?._id, {
                        $set: { cv: Array.from(new Set([...foundCandidate.cv, fileName])), flatText: extractedText },
                    });
                }
            } catch (error) {
                throw new HttpException(500,MESSAGES.GENERAL.SERVER_ERROR)
            }
        }
    }

    public async archivedAndDisarchiveUsers(userId: string, isArchived: boolean): Promise<string> {
        try {
            const updatedUser = await this.User.findByIdAndUpdate(userId, { $set: { isArchived } },
                { new: true }
            );

            if (!updatedUser) {
             throw new HttpException(404,MESSAGES.USER.USER_NOT_FOUND)
            }
            return isArchived
                ? `User ${userId} archived successfully`
                : `User ${userId} unarchived successfully`;
        } catch (error) {

            console.error(error);
            throw new Error(`Error archiving/unarchiving user: `);
        }
    }


    private extractFirstAndLastName(fullName: string): { firstName: string; lastName: string } {
        const cleanedName = fullName?.replace(/[^a-zA-Z\s]/g, '').trim();
        const nameParts = cleanedName?.split(/\s+/);

        const length = nameParts.length;
        let firstName, lastName;

        if (length === 0) return { firstName: '', lastName: '' };

        if (length % 2 === 0 && length > 2) {
            firstName = nameParts.slice(0, 2).join(' ');
            lastName = nameParts.slice(2).join(' ');
        } else {
            firstName = nameParts[0];
            lastName = nameParts.slice(1).join(' ');
        }

        return { firstName, lastName: lastName.toUpperCase() };
    }
}

export default UserService;
