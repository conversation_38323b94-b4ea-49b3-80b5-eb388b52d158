"use client";
import { useEffect, useRef, useState } from 'react';

// Performance monitoring hook for carousel
export const useCarouselPerformance = (carouselId) => {
  const [metrics, setMetrics] = useState({
    loadTime: 0,
    firstImageLoad: 0,
    layoutShifts: 0,
    renderTime: 0,
    interactionDelay: 0
  });
  
  const startTime = useRef(Date.now());
  const observer = useRef(null);
  const layoutShiftScore = useRef(0);

  useEffect(() => {
    // Measure initial render time
    const renderStartTime = performance.now();
    
    // Layout Shift Observer
    if ('LayoutShift' in window && PerformanceObserver.supportedEntryTypes.includes('layout-shift')) {
      observer.current = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            layoutShiftScore.current += entry.value;
            setMetrics(prev => ({
              ...prev,
              layoutShifts: layoutShiftScore.current
            }));
          }
        }
      });
      
      observer.current.observe({ entryTypes: ['layout-shift'] });
    }

    // Measure carousel load completion
    const checkCarouselReady = () => {
      const carousel = document.getElementById(carouselId);
      if (carousel) {
        const images = carousel.querySelectorAll('img');
        const firstImage = images[0];
        
        if (firstImage && firstImage.complete) {
          const firstImageLoadTime = Date.now() - startTime.current;
          setMetrics(prev => ({
            ...prev,
            firstImageLoad: firstImageLoadTime,
            renderTime: performance.now() - renderStartTime
          }));
        }
        
        // Check if all images are loaded
        const allImagesLoaded = Array.from(images).every(img => img.complete);
        if (allImagesLoaded) {
          const totalLoadTime = Date.now() - startTime.current;
          setMetrics(prev => ({
            ...prev,
            loadTime: totalLoadTime
          }));
        }
      }
    };

    // Check immediately and then periodically
    checkCarouselReady();
    const interval = setInterval(checkCarouselReady, 100);

    // Cleanup
    return () => {
      clearInterval(interval);
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [carouselId]);

  // Measure interaction delay
  const measureInteractionDelay = (callback) => {
    return (...args) => {
      const start = performance.now();
      const result = callback(...args);
      const end = performance.now();
      
      setMetrics(prev => ({
        ...prev,
        interactionDelay: end - start
      }));
      
      return result;
    };
  };

  return { metrics, measureInteractionDelay };
};

// Performance monitoring component
const CarouselPerformanceMonitor = ({ carouselId, onMetricsUpdate, showDebug = false }) => {
  const { metrics } = useCarouselPerformance(carouselId);
  const [performanceGrade, setPerformanceGrade] = useState('A');

  useEffect(() => {
    // Calculate performance grade
    let grade = 'A';
    let score = 100;

    // Deduct points for poor metrics
    if (metrics.loadTime > 3000) score -= 20; // > 3s load time
    if (metrics.firstImageLoad > 1000) score -= 15; // > 1s first image
    if (metrics.layoutShifts > 0.1) score -= 25; // > 0.1 CLS
    if (metrics.renderTime > 100) score -= 10; // > 100ms render
    if (metrics.interactionDelay > 50) score -= 10; // > 50ms interaction delay

    if (score >= 90) grade = 'A';
    else if (score >= 80) grade = 'B';
    else if (score >= 70) grade = 'C';
    else if (score >= 60) grade = 'D';
    else grade = 'F';

    setPerformanceGrade(grade);

    // Report metrics to parent component
    if (onMetricsUpdate) {
      onMetricsUpdate({ ...metrics, score, grade });
    }
  }, [metrics, onMetricsUpdate]);

  // Log performance metrics in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && metrics.loadTime > 0) {
      console.group(`🎠 Carousel Performance (${carouselId})`);
      console.log(`📊 Overall Grade: ${performanceGrade}`);
      console.log(`⏱️ Total Load Time: ${metrics.loadTime}ms`);
      console.log(`🖼️ First Image Load: ${metrics.firstImageLoad}ms`);
      console.log(`📐 Layout Shifts (CLS): ${metrics.layoutShifts.toFixed(4)}`);
      console.log(`🎨 Render Time: ${metrics.renderTime.toFixed(2)}ms`);
      console.log(`🖱️ Interaction Delay: ${metrics.interactionDelay.toFixed(2)}ms`);
      console.groupEnd();
    }
  }, [metrics, performanceGrade, carouselId]);

  // Debug overlay (only in development)
  if (showDebug && process.env.NODE_ENV === 'development') {
    return (
      <div
        style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          background: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '10px',
          borderRadius: '5px',
          fontSize: '12px',
          fontFamily: 'monospace',
          zIndex: 9999,
          minWidth: '200px'
        }}
      >
        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
          🎠 Carousel Performance
        </div>
        <div>Grade: <span style={{ color: getGradeColor(performanceGrade) }}>{performanceGrade}</span></div>
        <div>Load: {metrics.loadTime}ms</div>
        <div>First Image: {metrics.firstImageLoad}ms</div>
        <div>CLS: {metrics.layoutShifts.toFixed(4)}</div>
        <div>Render: {metrics.renderTime.toFixed(1)}ms</div>
        <div>Interaction: {metrics.interactionDelay.toFixed(1)}ms</div>
      </div>
    );
  }

  return null;
};

// Helper function for grade colors
const getGradeColor = (grade) => {
  switch (grade) {
    case 'A': return '#4CAF50';
    case 'B': return '#8BC34A';
    case 'C': return '#FFC107';
    case 'D': return '#FF9800';
    case 'F': return '#F44336';
    default: return '#9E9E9E';
  }
};

// Performance optimization utilities
export const CarouselPerformanceUtils = {
  // Preload critical images
  preloadImages: (imageUrls) => {
    imageUrls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = url;
      document.head.appendChild(link);
    });
  },

  // Lazy load non-critical images
  lazyLoadImages: (selector = 'img[data-src]') => {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            observer.unobserve(img);
          }
        });
      });

      document.querySelectorAll(selector).forEach(img => {
        imageObserver.observe(img);
      });
    }
  },

  // Optimize carousel for performance
  optimizeCarousel: (carouselElement) => {
    if (!carouselElement) return;

    // Add performance hints
    carouselElement.style.willChange = 'transform';
    carouselElement.style.transform = 'translateZ(0)';

    // Optimize images
    const images = carouselElement.querySelectorAll('img');
    images.forEach((img, index) => {
      // Add loading attribute
      img.loading = index === 0 ? 'eager' : 'lazy';
      
      // Add decode hint
      img.decoding = 'async';
      
      // Optimize for performance
      img.style.willChange = 'opacity';
    });
  },

  // Monitor Web Vitals
  measureWebVitals: (callback) => {
    if (typeof window !== 'undefined') {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(callback);
        getFID(callback);
        getFCP(callback);
        getLCP(callback);
        getTTFB(callback);
      });
    }
  }
};

export default CarouselPerformanceMonitor;
