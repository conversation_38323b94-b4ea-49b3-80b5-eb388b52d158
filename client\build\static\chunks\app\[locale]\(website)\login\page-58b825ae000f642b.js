(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8701],{52162:function(e,t,s){Promise.resolve().then(s.bind(s,35633))},77210:function(e,t,s){"use strict";var n=s(57437),a=s(98489),r=s(90191);t.Z=function(e){let{children:t,title:s,subTitle:l}=e;return(0,n.jsx)("div",{id:"auth-layout",style:{backgroundImage:`url(${r.default.src})`},children:(0,n.jsxs)(a.default,{className:"container custom-max-width",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"heading-h1 text-white",children:s}),(0,n.jsx)("p",{className:"sub-heading text-white",children:l})]}),(0,n.jsx)("div",{children:t})]})})}},30100:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var n=s(57437);function a(e){let{errMsg:t,success:s}=e;return(0,n.jsxs)(n.Fragment,{children:[t&&(0,n.jsx)("div",{className:"errorMsgBanner",children:t}),s&&(0,n.jsx)("div",{className:"successMsgBanner",children:s})]})}},93214:function(e,t,s){"use strict";s.d(t,{cU:function(){return i},xk:function(){return l},yX:function(){return r}});var n=s(83464),a=s(40257);let r=n.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),l=n.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),i=n.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"});n.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))},8552:function(e,t,s){"use strict";s.d(t,{Z:function(){return v}});var n,a,r,l,i,o,c=s(57437),u=s(41774),d=s(94746);function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)({}).hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e}).apply(null,arguments)}var h=e=>d.createElement("svg",g({xmlns:"http://www.w3.org/2000/svg",width:25,height:24,fill:"none"},e),n||(n=d.createElement("g",{clipPath:"url(#coloredMicrosoft_svg__a)"},d.createElement("path",{fill:"#fff",d:"M3.5 3h18v18h-18z"}),d.createElement("path",{fill:"#F35325",d:"M4.281 3.782h7.826v7.826H4.281z"}),d.createElement("path",{fill:"#81BC06",d:"M12.89 3.782h7.827v7.826H12.89z"}),d.createElement("path",{fill:"#05A6F0",d:"M4.281 12.392h7.826v7.826H4.281z"}),d.createElement("path",{fill:"#FFBA08",d:"M12.89 12.392h7.827v7.826H12.89z"}))),a||(a=d.createElement("defs",null,d.createElement("clipPath",{id:"coloredMicrosoft_svg__a"},d.createElement("path",{fill:"#fff",d:"M3.5 3h18v18h-18z"})))));function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)({}).hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e}).apply(null,arguments)}var f=e=>d.createElement("svg",p({xmlns:"http://www.w3.org/2000/svg",width:25,height:24,fill:"none"},e),r||(r=d.createElement("path",{fill:"#FFC107",d:"M22.306 10.042H21.5V10h-9v4h5.651A5.998 5.998 0 0 1 6.5 12a6 6 0 0 1 6-6c1.53 0 2.921.577 3.98 1.52L19.31 4.69A9.95 9.95 0 0 0 12.5 2c-5.522 0-10 4.478-10 10s4.478 10 10 10 10-4.477 10-10c0-.67-.069-1.325-.195-1.959"})),l||(l=d.createElement("path",{fill:"#FF3D00",d:"m3.652 7.346 3.286 2.409A6 6 0 0 1 12.499 6c1.53 0 2.921.577 3.98 1.52l2.83-2.829A9.95 9.95 0 0 0 12.498 2a9.99 9.99 0 0 0-8.847 5.346"})),i||(i=d.createElement("path",{fill:"#4CAF50",d:"M12.5 22c2.583 0 4.93-.988 6.705-2.596l-3.095-2.619A5.96 5.96 0 0 1 12.5 18a6 6 0 0 1-5.641-3.973L3.598 16.54C5.253 19.778 8.614 22 12.5 22"})),o||(o=d.createElement("path",{fill:"#1976D2",d:"M22.306 10.042H21.5V10h-9v4h5.651a6 6 0 0 1-2.043 2.785h.002l3.095 2.619C18.985 19.602 22.5 17 22.5 12c0-.67-.069-1.325-.195-1.959"}))),m=s(80657),A=s(40257),v=function(e){let{type:t,t:s,redirection:n,removeBottomSection:a,locale:r}=e,l=async()=>{window.location.href=`${A.env.NEXT_PUBLIC_BASE_API_URL}/auth/google?state=${encodeURIComponent(JSON.stringify({isSignup:"register"===t,redirection:n}))}`},i=async()=>{window.location.href=`${A.env.NEXT_PUBLIC_BASE_API_URL}/auth/microsoft?state=${encodeURIComponent(JSON.stringify({isSignup:"register"===t,redirection:n}))}`};return(0,c.jsxs)("div",{id:"connect-social-media",children:[(0,c.jsxs)("div",{className:"top-section",children:[(0,c.jsx)("span",{className:"line"}),(0,c.jsx)("p",{className:"title",children:s("register"==t?"register:registerWith":"login:orConnect")}),(0,c.jsx)("span",{className:"line"})]}),(0,c.jsxs)("div",{className:"btns-section",children:[(0,c.jsx)(u.default,{leftIcon:!0,icon:(0,c.jsx)(f,{}),text:"Google",onClick:l,className:"btn-social-media"}),(0,c.jsx)(u.default,{leftIcon:!0,icon:(0,c.jsx)(h,{}),text:"Microsoft",onClick:i,className:"btn-social-media"})]}),!a&&(0,c.jsxs)("div",{className:"bottom-section",children:[s("register"==t?"register:haveAnAccount":"login:haveAnAccount"),"register"==t?(0,c.jsx)(u.default,{text:s("login:login"),link:`/${m.jb.login.route}`,locale:r,className:"btn yellow text-left"}):(0,c.jsx)(u.default,{text:s("register:register"),link:`/${m.jb.register.route}`,locale:r,className:"btn yellow text-left"})]})]})}},35633:function(e,t,s){"use strict";var n=s(57437),a=s(2265),r=s(89126),l=s(64393),i=s(77584),o=s(15735),c=s(68218),u=s(23996),d=s(59832),g=s(85860),h=s(11953),p=s(41774),f=s(86100),m=s(28397),A=s(8552),v=s(30100),x=s(14759),b=s(94395),j=s(77210),w=s(55788),E=s(80657);t.default=function(e){let{successMsg:t,errorMsg:s,locale:N}=e,{t:_}=(0,w.$G)(),[y,C]=(0,a.useState)(""),[U,I]=(0,a.useState)(""),[L,P]=(0,a.useState)(""),[S,B]=(0,a.useState)(!1),[Z,M]=(0,a.useState)(!1),[R,k]=(0,a.useState)(null),[T,F]=(0,a.useState)(null);(0,a.useEffect)(()=>{t&&B(t),s&&P(s)},[s,t]);let O=(0,f.f)(_,P,B),$=async e=>{e.preventDefault(),""===y.trim()&&k(_("validations:emptyField")),""===U.trim()&&F(_("validations:emptyField")),await O.mutateAsync({email:y,password:U},{onSuccess:e=>{C(""),I(""),P(""),e?.user?.roles?.includes(m.uU.CANDIDATE)?window.location.href=`/${E.pf.baseURL.route}/`:(e?.user?.roles?.includes(m.uU.EDITOR)||e?.user?.roles?.includes(m.uU.ADMIN))&&(window.location.href=`/${E.GW.baseURL.route}/${E.MW.home.route}/`)}})};return(0,n.jsx)(j.Z,{id:"auth-layout",title:_("login:login"),subTitle:_("login:connectWithUs"),children:(0,n.jsxs)("form",{id:"login-form",className:"pentabell-form",children:[(0,n.jsxs)(r.Z,{className:"form-group",children:[(0,n.jsx)(l.Z,{className:"label-pentabell light",children:_("register:email")}),(0,n.jsx)(i.Z,{className:"input-pentabell light",placeholder:_("register:email"),variant:"standard",type:"email",value:y,onChange:e=>{C(e.target.value),P(""),F(null),k(null),B(!1)}})]}),R&&(0,n.jsx)(o.Z,{variant:"filled",severity:"error",children:R}),(0,n.jsxs)(r.Z,{className:"form-group",children:[(0,n.jsx)(l.Z,{className:"label-pentabell light",children:_("register:password")}),(0,n.jsx)(c.Z,{className:"input-pentabell light",placeholder:_("register:password"),variant:"standard",type:Z?"text":"password",value:U,onChange:e=>{I(e.target.value),P(""),F(null),k(null),B(!1)},endAdornment:(0,n.jsx)(u.Z,{position:"end",children:(0,n.jsx)(d.Z,{className:`toggle-password fa fa-fw ${Z?"fa-eye":"fa-eye-slash"}`,onClick:()=>{M(!Z)},"aria-label":"toggle password visibility",edge:"end",children:Z?(0,n.jsx)(x.Z,{}):(0,n.jsx)(b.Z,{})})})})]}),T&&(0,n.jsx)(o.Z,{variant:"filled",severity:"error",children:T}),(0,n.jsx)(p.default,{text:_("login:forgotPassword"),className:"btn btn-reset-pwd",link:`/${E.jb.forgetPassword.route}`,locale:N}),(0,n.jsx)(g.Z,{className:"checkbox-pentabell light",control:(0,n.jsx)(h.Z,{name:"keep-loggin"}),label:_("login:keepMeSignedIn")}),(0,n.jsx)(v.Z,{errMsg:L,success:S}),(0,n.jsx)(p.default,{text:"Login",className:"btn btn-filled full-width btn-submit",onClick:$}),(0,n.jsx)(A.Z,{t:_,locale:N})]})})}},86100:function(e,t,s){"use strict";s.d(t,{f:function(){return o}});var n=s(86484),a=s(93214),r=s(46172);let l=(e,t,s,n)=>new Promise(async(l,i)=>{a.yX.post(r.Y.auth,e).then(e=>{n(t("messages:successfulLogin")),e?.data&&l(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(n(!1),401===e.response.status&&s(t("messages:incorrectPassword")),404===e.response.status&&s(t("messages:emailNotFound")),403===e.response.status&&(e.response.data.message.includes("activate your account")?s(t("messages:accountNotActivated")):s(e.response.data.message)),500===e.response.status&&s("Internal Server Error"))})});var i=s(99376);let o=(e,t,s)=>{(0,i.useRouter)();let a=(0,n.useQueryClient)();return(0,n.useMutation)({mutationFn:n=>l(n,e,t,s),onSuccess:e=>{a.invalidateQueries("user")},onError:e=>{console.log(e)}})}},46172:function(e,t,s){"use strict";s.d(t,{Y:function(){return a},v:function(){return n}});let n=s(40257).env.NEXT_PUBLIC_BASE_API_URL,a={seo:"seoTags",auth:"/auth/signin",logout:"/auth/logout",candidatures:"/applications",signup:"/auth/signup",forgetPassword:"/auth/forgot-password",resetPassword:"/auth/reset-password",guides:"/guides",currentUser:"/users/current",updateUser:"/users",users:"/users",categoryGuides:"guidecategory",candidate:"/candidates",report:"/report",skills:"/skills",files:"/files",applications:"/applications",sliders:"/sliders",favoris:"/candidate/favourite",articles:"/articles",categories:"/categories",blog:"/blog",category:"/categories",opportunity:"/opportunities",seoOpportunity:"/seoOpportunity",newsletter:"/newsletter",contact:"/contact",favourite:"/favourite",contacts:"contacts",comments:"/comments",statistics:"/statistics",events:"/events",baseUrl:`${n}`}},90191:function(e,t,s){"use strict";s.r(t),t.default={src:"/_next/static/media/bg-auth.1842cff2.png",height:738,width:1440,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAElBMVEU7Tl4WKTkxRFQ/UmIlOEhOYXEF1jp9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIUlEQVR4nBXBgQ0AMAzCsEDo/y9Ps1G962gLUJpsIvmQBwVgAD+bqpS2AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:4}}},function(e){e.O(0,[775,948,5788,2996,7648,3464,455,2662,7183,2296,747,3200,7584,6484,9832,5719,5554,4244,1774,2971,2117,1744],function(){return e(e.s=52162)}),_N_E=e.O()}]);