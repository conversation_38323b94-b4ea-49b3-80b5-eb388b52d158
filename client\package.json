{"name": "pentabell", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env dotenv -e .env.dev next dev", "prod": "cross-env dotenv -e .env next dev", "build": "npm run partytown && next build", "build:dev": "npm run partytown &&  cross-env dotenv -e .env.dev next build", "start": "next start", "lint": "next lint", "analyze": "cross-env ANALYZE=true next build", "preprod": "npm run partytown && cross-env dotenv -e .env.preprod next build", "partytown": "partytown copylib public/~partytown"}, "browserslist": ["defaults and supports es6-module"], "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@feelinglovelynow/slug": "^1.1.1", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^6.1.0", "@mui/material": "^6.1.10", "@mui/styles": "^6.1.0", "@mui/x-charts": "^7.23.2", "@mui/x-data-grid": "^7.19.0", "@mui/x-data-grid-pro": "^7.24.0", "@mui/x-date-pickers": "^7.18.0", "@next/bundle-analyzer": "^15.0.2", "@qwik.dev/partytown": "^0.11.0", "@reduxjs/toolkit": "^2.2.7", "@svgr/webpack": "^8.1.0", "axios": "^1.7.7", "bootstrap": "^5.3.3", "circles.js": "^0.0.6", "company-email-validator": "^1.1.0", "cookie": "^0.6.0", "dayjs": "^1.11.13", "dotenv-cli": "^7.3.0", "dotenv-webpack": "^8.0.1", "embla-carousel": "^8.3.0", "embla-carousel-autoplay": "^8.3.0", "embla-carousel-react": "^8.3.0", "formik": "^2.4.6", "framer-motion": "^12.4.7", "google-libphonenumber": "^3.2.38", "html-to-text": "^9.0.5", "i18n-iso-countries": "^7.14.0", "i18next": "^23.15.1", "i18next-resources-to-backend": "^1.2.1", "jose": "^5.9.3", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "mammoth": "^1.9.1", "moment": "^2.30.1", "next": "^14.2.25", "next-auth": "^4.24.7", "next-i18n-router": "^5.5.1", "next-i18next": "^15.3.1", "next-images": "^1.8.5", "next-svgr": "^0.0.2", "npm": "^10.8.3", "pako": "^2.1.0", "rc-slider": "^11.1.6", "react": "^18", "react-bootstrap": "^2.10.4", "react-calendar": "^5.0.0", "react-circular-progressbar": "^2.2.0", "react-datepicker": "^7.3.0", "react-dom": "^18", "react-gtm-module": "^2.0.11", "react-i18next": "^15.0.1", "react-icons": "^5.3.0", "react-icons-kit": "^2.0.0", "react-international-phone": "^4.3.0", "react-pdf": "^9.1.0", "react-query": "^3.39.3", "react-redux": "^9.1.2", "react-router-dom": "^6.26.2", "react-tag-input": "^6.10.3", "react-toastify": "^10.0.5", "recharts": "^2.14.1", "redux-persist": "^6.0.0", "sass": "^1.78.0", "sharp": "^0.34.1", "socket.io-client": "^4.8.1", "suneditor": "^2.47.5", "suneditor-react": "^3.6.1", "svelte": "^4.2.19", "svelte-loader": "^3.2.3", "uuid": "^10.0.0", "yup": "^1.4.0"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "svelte": "^4.2.19", "svelte-loader": "^3.2.3", "tailwindcss": "^3.4.1"}}