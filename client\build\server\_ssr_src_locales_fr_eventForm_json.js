"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_fr_eventForm_json";
exports.ids = ["_ssr_src_locales_fr_eventForm_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/fr/eventForm.json":
/*!***************************************!*\
  !*** ./src/locales/fr/eventForm.json ***!
  \***************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"createEvent":"Ajouter un nouveau événement","addEventEnglish":"Ajouter événement Anglais","addEventFrench":"Ajouter événement Français","addWebsiteImage":"Ajouter Image","addMobileImage":"Ajouter Image Mobile","subTitle":"Sub Title","eventList":"Liste des événements","addEvent":"Ajouter événement","editEvent":"Modifier événement"}');

/***/ })

};
;