"use client";

import { Container, Grid, Skeleton, Box } from "@mui/material";
import { memo } from "react";

const GlossaryPageLoading = memo(function GlossaryPageLoading() {
  return (
    <>
      {/* Banner Loading */}
      <Box
        sx={{
          height: "100vh",
          background: "linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          position: "relative",
          overflow: "hidden"
        }}
      >
        <Container className="custom-max-width">
          <Box sx={{ textAlign: "center", color: "white" }}>
            {/* Icon skeleton */}
            <Skeleton
              variant="circular"
              width={80}
              height={80}
              sx={{
                bgcolor: "rgba(255, 255, 255, 0.1)",
                mx: "auto",
                mb: 3
              }}
            />
            
            {/* Title skeleton */}
            <Skeleton
              variant="text"
              width="60%"
              height={60}
              sx={{
                bgcolor: "rgba(255, 255, 255, 0.1)",
                mx: "auto",
                mb: 4
              }}
            />
            
            {/* Search bar skeleton */}
            <Box sx={{ maxWidth: 600, mx: "auto", mb: 4 }}>
              <Skeleton
                variant="rectangular"
                height={56}
                sx={{
                  bgcolor: "rgba(255, 255, 255, 0.1)",
                  borderRadius: 2
                }}
              />
            </Box>
            
            {/* Letters navigation skeleton */}
            <Box sx={{ display: "flex", justifyContent: "center", gap: 1, flexWrap: "wrap" }}>
              {Array.from({ length: 12 }).map((_, index) => (
                <Skeleton
                  key={index}
                  variant="circular"
                  width={40}
                  height={40}
                  sx={{
                    bgcolor: "rgba(255, 255, 255, 0.1)"
                  }}
                />
              ))}
            </Box>
          </Box>
        </Container>
      </Box>

      {/* Content Loading */}
      <Box sx={{ py: 6 }}>
        <Container className="custom-max-width">
          <Grid container spacing={3}>
            {Array.from({ length: 8 }).map((_, index) => (
              <Grid item lg={3} md={4} sm={6} xs={12} key={index}>
                <Box
                  sx={{
                    p: 3,
                    border: "1px solid",
                    borderColor: "divider",
                    borderRadius: 2,
                    height: "100%"
                  }}
                >
                  {/* Letter count skeleton */}
                  <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                    <Skeleton
                      variant="circular"
                      width={32}
                      height={32}
                      sx={{ mr: 2 }}
                    />
                    <Skeleton variant="text" width={40} height={24} />
                  </Box>
                  
                  {/* Letter skeleton */}
                  <Skeleton
                    variant="text"
                    width={60}
                    height={48}
                    sx={{ mb: 3, fontWeight: "bold" }}
                  />
                  
                  {/* Words skeleton */}
                  <Box sx={{ mb: 3 }}>
                    {Array.from({ length: 5 }).map((_, wordIndex) => (
                      <Skeleton
                        key={wordIndex}
                        variant="text"
                        width={`${Math.random() * 40 + 60}%`}
                        height={20}
                        sx={{ mb: 1 }}
                      />
                    ))}
                  </Box>
                  
                  {/* Show more button skeleton */}
                  <Skeleton
                    variant="rectangular"
                    width={100}
                    height={32}
                    sx={{ borderRadius: 1 }}
                  />
                </Box>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>
    </>
  );
});

// Compact loading component for search results
export const GlossarySearchLoading = memo(function GlossarySearchLoading() {
  return (
    <Container className="custom-max-width">
      <Box sx={{ py: 4 }}>
        {/* Search results header skeleton */}
        <Box sx={{ textAlign: "center", mb: 4 }}>
          <Skeleton variant="text" width="40%" height={32} sx={{ mx: "auto", mb: 1 }} />
          <Skeleton variant="text" width="20%" height={20} sx={{ mx: "auto" }} />
        </Box>

        {/* Results grid skeleton */}
        <Grid container spacing={3}>
          {Array.from({ length: 4 }).map((_, index) => (
            <Grid item lg={3} md={4} sm={6} xs={12} key={index}>
              <Box
                sx={{
                  p: 2,
                  border: "1px solid",
                  borderColor: "divider",
                  borderRadius: 1,
                  height: 200
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  <Skeleton variant="circular" width={24} height={24} sx={{ mr: 1 }} />
                  <Skeleton variant="text" width={30} height={20} />
                </Box>
                <Skeleton variant="text" width={40} height={36} sx={{ mb: 2 }} />
                <Box>
                  {Array.from({ length: 3 }).map((_, wordIndex) => (
                    <Skeleton
                      key={wordIndex}
                      variant="text"
                      width={`${Math.random() * 30 + 70}%`}
                      height={16}
                      sx={{ mb: 0.5 }}
                    />
                  ))}
                </Box>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Container>
  );
});

// Letter section loading component
export const GlossaryLetterLoading = memo(function GlossaryLetterLoading({ count = 1 }) {
  return (
    <Grid container spacing={3}>
      {Array.from({ length: count }).map((_, index) => (
        <Grid item lg={3} md={4} sm={6} xs={12} key={index}>
          <Box
            sx={{
              p: 3,
              border: "1px solid",
              borderColor: "divider",
              borderRadius: 2,
              height: "100%",
              minHeight: 250
            }}
          >
            {/* Header */}
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
              <Skeleton
                variant="circular"
                width={28}
                height={28}
                sx={{ mr: 2 }}
              />
              <Skeleton variant="text" width={35} height={20} />
            </Box>
            
            {/* Letter */}
            <Skeleton
              variant="text"
              width={50}
              height={40}
              sx={{ mb: 3 }}
            />
            
            {/* Terms */}
            <Box sx={{ mb: 3 }}>
              {Array.from({ length: 5 }).map((_, termIndex) => (
                <Skeleton
                  key={termIndex}
                  variant="text"
                  width={`${Math.random() * 50 + 50}%`}
                  height={18}
                  sx={{ mb: 1 }}
                />
              ))}
            </Box>
            
            {/* Button */}
            <Skeleton
              variant="rectangular"
              width={90}
              height={28}
              sx={{ borderRadius: 1 }}
            />
          </Box>
        </Grid>
      ))}
    </Grid>
  );
});

// Shimmer animation styles
const shimmerStyles = `
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
  
  .glossary-shimmer {
    background: linear-gradient(
      90deg,
      #f0f0f0 25%,
      #e0e0e0 50%,
      #f0f0f0 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
`;

// Inject shimmer styles
if (typeof document !== "undefined") {
  const styleSheet = document.createElement("style");
  styleSheet.textContent = shimmerStyles;
  document.head.appendChild(styleSheet);
}

export default GlossaryPageLoading;
