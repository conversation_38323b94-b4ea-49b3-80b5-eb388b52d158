import { API_URLS, baseURL } from "../../../utils/urls";
import { axiosGetJson, axiosGetJsonSSR } from "../../../config/axios";
import { toast } from "react-toastify";

export const createGlossary = (body) => {
  return new Promise(async (resolve, reject) => {
    axiosGetJson
      .post(API_URLS.glossaries, body.data)
      .then((valid) => {
        toast.success("Glossary added successfully");
        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (
          err &&
          err.response &&
          err.response.data &&
          err?.response?.data?.status !== 500
        ) {
          toast.error(err.response.data.message);
        } else {
          toast.error("Internal Server Error");
        }
        reject(err);
      });
  });
};

export const getGlossaryByUrlAndlanguage = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      let response = {};
      response = await axiosGetJson.get(
        `${API_URLS.glossaries}/dashboard/${body.id}`
      );
      resolve(response.data);
    } catch (err) {
      if (
        err &&
        err.response &&
        err.response.data &&
        err.response.status !== 500
      ) {
        toast.error(err.response.data.message);
      }
      reject(err);
    }
  });
};

export const updateGlossaryByLanguageAndId = (updateData) => {
  const { id, versions, robotsMeta } = updateData;

  const payload = { robotsMeta, versions };

  return new Promise(async (resolve, reject) => {
    axiosGetJson
      .put(`${API_URLS.glossaries}/${id}`, payload)
      .then((response) => {
        toast.success("Glossary updated successfully");
        resolve(response.data);
      })
      .catch((err) => {
        if (
          err &&
          err.response &&
          err.response.data &&
          err?.response?.data?.status !== 500
        ) {
          toast.error(err.response.data.message);
        } else {
          toast.error("Internal Server Error");
        }
        reject(err);
      });
  });
};

export const getArticleWithPrevAndNext = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      let response = {};
      response = await axiosGetJson.get(
        `${API_URLS.articles}/${body.language}/article/${body.urlArticle}`
      );
      resolve(response.data);
    } catch (err) {
      if (err && err.response && err.response.data) {
        if (err.response.status === 404) {
        }
      }
      reject(err);
    }
  });
};

export const getSlugBySlug = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      let response = {};
      response = await axiosGetJson.get(
        `${API_URLS.articles}/${body.language}/${body.urlArticle}`
      );
      resolve(response.data);
    } catch (err) {
      if (err && err.response && err.response.data) {
        if (err.response.status === 404) {
        }
      }
      reject(err);
    }
  });
};

export const desarchiveversions = (language, id, archive) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJsonSSR.put(
        `${API_URLS.articles}/${language}/${id}/desarchiver`,
        { archive }
      );
      if (response?.data) {
        toast.success(
          `Article ${archive ? "archived" : "desarchived"} successfully`
        );
        resolve(response.data);
      }
    } catch (err) {
      toast.error(
        `Failed to ${archive ? "archive" : "desarchive"} the article.`
      );
      reject(err);
    }
  });
};

export const getarchivedArticles = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(`${baseURL}/articles/archived`, {
        params: {
          language: body.language,
          pageSize: body.pageSize,
          pageNumber: body.pageNumber,
          sortOrder: body.sortOrder,
          searchQuery: body.searchQuery,
          visibility: body.visibility,
          createdAt: body.createdAt,
          isArchived: body.isArchived,
          publishDate: body.publishDate,
          paginated: body.paginated,
          categoryName: body.categoryName,
        },
      });
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getGlossaryDashboard = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(`${API_URLS.glossaries}/`, {
        params: {
          paginated: body.paginated,
          language: body.language,
          pageSize: body.pageSize,
          pageNumber: body.pageNumber,
          sortOrder: body.sortOrder,
          sortBy: body.sortBy,
          letter: body.letter,
          searchQuery: body.searchQuery,
          visibility: body.visibility,
          createdAt: body.createdAt,
          isArchived: body.isArchived,
        },
      });
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getArticleById = (id, language) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(
        `${API_URLS.articles}/${language}/${id}`
      );
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getArticleByIdAll = (id, language) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(`${API_URLS.articles}/${id}`);
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const archiveGlossary = (language, id) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.delete(
        `${API_URLS.glossaries}/${language}/${id}`
      );
      toast.success("Glossary status updated successfully");
      resolve(response.data);
    } catch (err) {
      if (
        err &&
        err.response &&
        err.response.data &&
        err.response.status !== 500
      ) {
        toast.error(err.response.data.message);
      } else {
        toast.error("Internal Server Error");
      }
      reject(err);
    }
  });
};

export const deleteGlossary = (language, id) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.delete(
        `${API_URLS.glossaries}/${language}/${id}`,
        { params: { hardDelete: "true" } }
      );
      toast.success("Glossary deleted successfully");
      resolve(response.data);
    } catch (err) {
      if (
        err &&
        err.response &&
        err.response.data &&
        err.response.status !== 500
      ) {
        toast.error(err.response.data.message);
      } else {
        toast.error("Internal Server Error");
      }
      reject(err);
    }
  });
};
