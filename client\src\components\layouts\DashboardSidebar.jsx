"use client";
import Link from "next/link";

import SvgArrowLeft from "../../assets/images/icons/arrowLeft.svg";
import SvgArrowRight from "../../assets/images/icons/arrowRight.svg";
import CustomButton from "../ui/CustomButton";
import { usePathname } from "next/navigation";
import Tooltip from "@mui/material/Tooltip";
import { useTranslation } from "react-i18next";
import { generateLocalizedSlug, stringAvatar } from "@/utils/functions";
import { Avatar } from "@mui/material";
import { useGetUserData } from "@/features/user/hooks/updateProfile.hooks";
import logoPentabell from "@/assets/images/charte/new-logo-dark.png";

function DashboardSidebar({ menuList, isOpen, toggleSidebar, locale }) {
  const pathname = usePathname();
  const { t } = useTranslation();
  const { data } = useGetUserData(t);

  const truncateTitle = (title) => {
    const words = title.split(" ");
    if (words?.length > 2) {
      return words.slice(0, 2).join(" ") + "...";
    } else {
      return title;
    }
  };

  return (
    <div id="sidebar-component" className={`${isOpen ? "toggled" : ""}`}>
      {isOpen ? (
        <CustomButton
          icon={
            <span className="menu-icon">
              <SvgArrowRight />
            </span>
          }
          onClick={toggleSidebar}
          className={"btn btn-ghost toggle-menu"}
        />
      ) : (
        <div className="pentabell-logo">
          <img
            src={logoPentabell.src}
            alt="Pentabell logo"
            loading="lazy"
            onClick={() =>
              (window.location.href = generateLocalizedSlug(locale, ""))
            }
          />
          <CustomButton
            icon={
              <span className="menu-icon">
                <SvgArrowLeft />
              </span>
            }
            onClick={toggleSidebar}
            className={"btn btn-ghost toggle-menu"}
          />
        </div>
      )}

      <div id="sidebar-menu">
        <div className="menu-main">
          {menuList?.map((item) => {
            const localizedRoute = generateLocalizedSlug(locale, item.route);
            return (
              <Link
                key={item.key}
                className={`nav-link ${
                  pathname === localizedRoute ? "active" : ""
                  // pathname.includes(localizedRoute)  ? "active" : ""
                }`}
                locale={locale === "en" ? "en" : "fr"}
                href={localizedRoute}
              >
                <Tooltip
                  title={item.i18nName ? t(item.i18nName) : item.name}
                  placement="right-start"
                  componentsProps={{
                    tooltip: {
                      sx: {
                        color: "#798BA3",
                        backgroundColor: "white",
                        fontWeight: "bold",
                        fontSize: "0.8em",
                      },
                    },
                  }}
                >
                  <span className="menu-icon">{item.svgIcon}</span>
                </Tooltip>
                <span className="menu-title">
                  {item.i18nName ? t(item.i18nName) : item.name}
                </span>
              </Link>
            );
          })}
        </div>
        {/*  {data?.roles.includes('Candidate') && !isOpen && (
         <div id="profile-percentage">
      
        <ProfilePercentage 
          percentage={Math.round(data?.candidate.profilePercentage || 0)}
          maxPercentage={91} 
        />
     </div> )} */}
        <div className="nav-link">
          <Avatar
            {...stringAvatar(data?.firstName + " " + data?.lastName)}
            alt={data?.firstName}
            src={`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${data?.profilePicture}`}
            className="menu-icon avatar"
          />
          {!isOpen && (
            <span className="menu-title">
              {truncateTitle(data?.firstName + " " + data?.lastName)}
            </span>
          )}
        </div>
      </div>
    </div>
  );
}

export default DashboardSidebar;
