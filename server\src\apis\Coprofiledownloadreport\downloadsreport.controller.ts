
import { Request, Response, NextFunction, Router } from 'express';
import Controller from '@/utils/interfaces/controller.interface';
import DownloadReportService from './downloadsreport.service';

class DownloadReport implements Controller {
  public readonly path = '/report';
  public readonly router = Router();
  private readonly downloadreportService = new DownloadReportService();
  constructor() {
    this.initialiseRoutes();
  }
  private initialiseRoutes(): void {
    this.router.post(`${this.path}/download`, this.handleReportDownload)
    this.router.get(`${this.path}`, this.getAllDownloadReport)
  }
  private handleReportDownload = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { fullName, email } = req.body;
      const message = await this.downloadreportService.handleReportDownload(fullName, email);
      return res.status(200).json({ message });
    } catch (error) {
      next(error);
    }
  }


  private getAllDownloadReport = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const queries: any = req.query;
      const result = await this.downloadreportService.getAllDownloadReports(queries);
      return res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };


}
export default DownloadReport;