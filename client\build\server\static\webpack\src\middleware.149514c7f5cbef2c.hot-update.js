"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.js":
/*!***************************!*\
  !*** ./src/middleware.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-i18n-router */ \"(middleware)/./node_modules/next-i18n-router/dist/index.js\");\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_i18n_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! jose */ \"(middleware)/./node_modules/jose/dist/browser/jwt/verify.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cookie */ \"(middleware)/./node_modules/cookie/index.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../i18nConfig */ \"(middleware)/./i18nConfig.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_i18nConfig__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/constants */ \"(middleware)/./src/utils/constants.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/functions */ \"(middleware)/./src/utils/functions.js\");\n/* harmony import */ var _config_translations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./config/translations */ \"(middleware)/./src/config/translations.js\");\n/* harmony import */ var _config_allowedParams__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./config/allowedParams */ \"(middleware)/./src/config/allowedParams.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./helpers/routesList */ \"(middleware)/./src/helpers/routesList.js\");\n\n\n\n\n\n\n\n\n\n\nconst SECURITY_CONFIG = {\n    MAX_REQUESTS_PER_MINUTE: 60,\n    JWT_ALGORITHM: \"HS256\",\n    SUSPICIOUS_PATTERNS: [\n        /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n        /javascript:/gi,\n        /on\\w+\\s*=/gi,\n        /eval\\s*\\(/gi,\n        /expression\\s*\\(/gi,\n        /%3Cscript/gi,\n        /%3C%2Fscript%3E/gi\n    ]\n};\nconst rateLimitStore = new Map();\nconst logSecurityEvent = (event, details = {})=>{\n    if (true) {\n        console.warn(`[SECURITY] ${event}:`, {\n            timestamp: new Date().toISOString(),\n            ...details\n        });\n    }\n};\nconst verifyToken = async (token, clientIP = \"unknown\")=>{\n    try {\n        if (!token || typeof token !== \"string\") {\n            logSecurityEvent(\"INVALID_TOKEN_FORMAT\", {\n                clientIP,\n                reason: \"Missing or invalid token\"\n            });\n            return null;\n        }\n        const jwtRegex = /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/;\n        if (!jwtRegex.test(token)) {\n            logSecurityEvent(\"INVALID_JWT_FORMAT\", {\n                clientIP,\n                tokenPrefix: token.substring(0, 10)\n            });\n            return null;\n        }\n        const jwtSecret = process.env.NEXT_JWT_SECRET;\n        if (!jwtSecret || jwtSecret.length < 32) {\n            logSecurityEvent(\"WEAK_JWT_SECRET\", {\n                clientIP\n            });\n            throw new Error(\"JWT secret configuration error\");\n        }\n        const secret = new TextEncoder().encode(jwtSecret);\n        const { payload } = await (0,jose__WEBPACK_IMPORTED_MODULE_9__.jwtVerify)(token, secret, {\n            algorithms: [\n                SECURITY_CONFIG.JWT_ALGORITHM\n            ],\n            issuer: process.env.JWT_ISSUER,\n            audience: process.env.JWT_AUDIENCE\n        });\n        if (!payload || !payload._id || !payload.roles || !Array.isArray(payload.roles)) {\n            logSecurityEvent(\"INVALID_TOKEN_PAYLOAD\", {\n                clientIP,\n                hasId: !!payload?._id,\n                hasRoles: !!payload?.roles\n            });\n            return null;\n        }\n        const tokenAge = Date.now() / 1000 - (payload.iat || 0);\n        if (tokenAge > 86400) {\n            logSecurityEvent(\"OLD_TOKEN_USAGE\", {\n                clientIP,\n                tokenAge,\n                userId: payload._id\n            });\n        }\n        return payload;\n    } catch (error) {\n        if (error.name === \"JWTExpired\") {\n            logSecurityEvent(\"TOKEN_EXPIRED\", {\n                clientIP\n            });\n        } else if (error.name === \"JWTInvalid\") {\n            logSecurityEvent(\"INVALID_TOKEN\", {\n                clientIP,\n                error: error.message\n            });\n        } else {\n            logSecurityEvent(\"TOKEN_VERIFICATION_ERROR\", {\n                clientIP,\n                error: error.message\n            });\n        }\n        return null;\n    }\n};\nconst checkRateLimit = (clientIP)=>{\n    const now = Date.now();\n    const windowStart = now - 60000;\n    if (!rateLimitStore.has(clientIP)) {\n        rateLimitStore.set(clientIP, []);\n    }\n    const requests = rateLimitStore.get(clientIP);\n    const validRequests = requests.filter((timestamp)=>timestamp > windowStart);\n    rateLimitStore.set(clientIP, validRequests);\n    if (validRequests.length >= SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE) {\n        return false;\n    }\n    validRequests.push(now);\n    return true;\n};\nconst sanitizeInput = (value)=>{\n    if (typeof value !== \"string\") return value;\n    let sanitized = value;\n    SECURITY_CONFIG.SUSPICIOUS_PATTERNS.forEach((pattern)=>{\n        sanitized = sanitized.replace(pattern, \"\");\n    });\n    return sanitized.trim();\n};\nconst setSecurityHeaders = (response)=>{\n    response.headers.set(\"X-Content-Type-Options\", \"nosniff\");\n    response.headers.set(\"X-Frame-Options\", \"DENY\");\n    response.headers.set(\"X-XSS-Protection\", \"1; mode=block\");\n    response.headers.set(\"Referrer-Policy\", \"strict-origin-when-cross-origin\");\n    response.headers.set(\"Permissions-Policy\", \"geolocation=(), microphone=(), camera=()\");\n    response.headers.delete(\"Server\");\n    response.headers.delete(\"X-Powered-By\");\n    return response;\n};\nasync function middleware(req) {\n    const url = req.nextUrl.clone();\n    const { defaultLocale } = (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default());\n    const { pathname } = req.nextUrl;\n    const clientIP = req.ip || req.headers.get(\"x-forwarded-for\") || req.headers.get(\"x-real-ip\") || \"unknown\";\n    let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    response = setSecurityHeaders(response);\n    if (!checkRateLimit(clientIP)) {\n        logSecurityEvent(\"RATE_LIMIT_EXCEEDED\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Too Many Requests\", {\n            status: 429,\n            headers: {\n                \"Retry-After\": \"60\",\n                \"X-RateLimit-Limit\": SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE.toString(),\n                \"X-RateLimit-Remaining\": \"0\"\n            }\n        });\n    }\n    let hasModifiedParams = false;\n    for (const [key, value] of url.searchParams.entries()){\n        const sanitizedValue = sanitizeInput(value);\n        if (sanitizedValue !== value) {\n            url.searchParams.set(key, sanitizedValue);\n            hasModifiedParams = true;\n            logSecurityEvent(\"SUSPICIOUS_QUERY_PARAM\", {\n                clientIP,\n                key,\n                originalValue: value.substring(0, 50)\n            });\n        }\n    }\n    const pathString = pathname.toLowerCase();\n    const hasSuspiciousPath = SECURITY_CONFIG.SUSPICIOUS_PATTERNS.some((pattern)=>pattern.test(pathString));\n    if (hasSuspiciousPath) {\n        logSecurityEvent(\"SUSPICIOUS_PATH_ACCESS\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Forbidden\", {\n            status: 403\n        });\n    }\n    const cookies = cookie__WEBPACK_IMPORTED_MODULE_2__.parse(req.headers.get(\"cookie\") || \"\");\n    const { accessToken, refreshToken } = cookies;\n    const isProtectedRoute = pathname.includes(\"dashboard\") || pathname.includes(\"backoffice\");\n    if (isProtectedRoute && !(accessToken && refreshToken)) {\n        logSecurityEvent(\"UNAUTHORIZED_ACCESS_ATTEMPT\", {\n            clientIP,\n            pathname\n        });\n        url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    let user = null;\n    if (refreshToken) {\n        user = await verifyToken(refreshToken, clientIP);\n        if (isProtectedRoute && !user) {\n            logSecurityEvent(\"INVALID_TOKEN_PROTECTED_ROUTE\", {\n                clientIP,\n                pathname\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n    }\n    // Handle logout route\n    if (pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/`) {\n        logSecurityEvent(\"USER_LOGOUT\", {\n            clientIP,\n            userId: user?._id\n        });\n        return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n    }\n    // Enhanced role-based access control\n    if (user) {\n        const menuList = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.getRoutesListByRole)(user);\n        // Validate user roles\n        if (!user.roles || !Array.isArray(user.roles) || user.roles.length === 0) {\n            logSecurityEvent(\"INVALID_USER_ROLES\", {\n                clientIP,\n                userId: user._id\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n        const checkRole = !menuList?.some((item)=>pathname.includes(item)) && (pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}`) || pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}`));\n        // Enhanced role checking with security logging\n        if (checkRole || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`) {\n            let redirectPath = null;\n            if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.home.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.EDITOR)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.blogs.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.CANDIDATE)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.candidateRoutes.myApplications.route}`;\n            }\n            if (redirectPath) {\n                logSecurityEvent(\"ROLE_BASED_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles,\n                    fromPath: pathname,\n                    toPath: redirectPath\n                });\n                url.pathname = redirectPath;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            } else {\n                logSecurityEvent(\"NO_VALID_ROLE_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles\n                });\n                url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            }\n        }\n    }\n    // Enhanced parameter filtering with security logging\n    let removedParams = [];\n    for (const param of url.searchParams.keys()){\n        if (!_config_allowedParams__WEBPACK_IMPORTED_MODULE_7__.allowedParams.has(param)) {\n            url.searchParams.delete(param);\n            removedParams.push(param);\n        }\n    }\n    if (removedParams.length > 0) {\n        logSecurityEvent(\"REMOVED_DISALLOWED_PARAMS\", {\n            clientIP,\n            pathname,\n            removedParams,\n            userId: user?._id\n        });\n    }\n    // Check if parameters were modified (either sanitized or removed)\n    if (hasModifiedParams || url.searchParams.toString() !== req.nextUrl.searchParams.toString()) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // Enhanced redirection paths with security checks\n    const frPath = _config_translations__WEBPACK_IMPORTED_MODULE_6__[\"default\"][req.nextUrl.pathname];\n    if (frPath) {\n        logSecurityEvent(\"FRENCH_PATH_REDIRECT\", {\n            clientIP,\n            fromPath: req.nextUrl.pathname,\n            toPath: frPath\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(frPath, req.url));\n    }\n    // Enhanced language handling with security validation\n    if (!pathname.startsWith(\"/fr\") && !pathname.startsWith(`/${defaultLocale}`) && !pathname.startsWith(\"/_next\") && !pathname.startsWith(\"/api\") && !pathname.startsWith(\"/static\") && !pathname.includes(\".\")) {\n        // Additional security check for suspicious paths\n        if (pathname.length > 200) {\n            logSecurityEvent(\"SUSPICIOUS_LONG_PATH\", {\n                clientIP,\n                pathLength: pathname.length\n            });\n            return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Bad Request\", {\n                status: 400\n            });\n        }\n        url.pathname = `/en${pathname}`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.rewrite(url);\n    }\n    // Log successful requests for monitoring (in development only)\n    if ( true && user) {\n        logSecurityEvent(\"SUCCESSFUL_REQUEST\", {\n            clientIP,\n            pathname,\n            userId: user._id,\n            roles: user.roles\n        });\n    }\n    return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     * - files with extensions (images, fonts, etc.)\r\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|public|.*\\\\..*).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.js\n");

/***/ })

});