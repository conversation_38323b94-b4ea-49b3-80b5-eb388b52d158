(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6577,254],{14485:function(e,t,r){var n={"./ckb.js":9706,"./cs.js":79089,"./da.js":83138,"./de.js":80043,"./en.js":96775,"./es.js":61288,"./fa.js":42058,"./fr.js":23147,"./he.js":38210,"./hu.js":10200,"./index.js":47905,"./it.js":47758,"./ja.js":36473,"./ko.js":7747,"./lv.js":17242,"./nl.js":16833,"./pl.js":37370,"./pt_br.js":86548,"./ro.js":20319,"./ru.js":659,"./se.js":9296,"./tr.js":44584,"./ua.js":34699,"./ur.js":9870,"./zh_cn.js":23428};function a(e){return r(s(e))}function s(e){if(!r.o(n,e)){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}a.keys=function(){return Object.keys(n)},a.resolve=s,e.exports=a,a.id=14485},52700:function(e,t,r){"use strict";var n=r(32464),a=r(57437);t.Z=(0,n.Z)((0,a.jsx)("path",{d:"M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z"}),"ExpandMore")},44164:function(e,t,r){"use strict";r.d(t,{Z:function(){return g}});var n=r(2265),a=r(61994),s=r(20801),i=r(16210),o=r(76301),u=r(37053),c=r(94143),d=r(50738);function l(e){return(0,d.ZP)("MuiAccordionDetails",e)}(0,c.Z)("MuiAccordionDetails",["root"]);var p=r(57437);let m=e=>{let{classes:t}=e;return(0,s.Z)({root:["root"]},l,t)},y=(0,i.ZP)("div",{name:"MuiAccordionDetails",slot:"Root",overridesResolver:(e,t)=>t.root})((0,o.Z)(e=>{let{theme:t}=e;return{padding:t.spacing(1,2,2)}}));var g=n.forwardRef(function(e,t){let r=(0,u.i)({props:e,name:"MuiAccordionDetails"}),{className:n,...s}=r,i=m(r);return(0,p.jsx)(y,{className:(0,a.Z)(i.root,n),ref:t,ownerState:r,...s})})},96369:function(e,t,r){"use strict";r.d(t,{Z:function(){return b}});var n=r(2265),a=r(61994),s=r(20801),i=r(16210),o=r(76301),u=r(37053),c=r(82662),d=r(31288),l=r(94143),p=r(50738);function m(e){return(0,p.ZP)("MuiAccordionSummary",e)}let y=(0,l.Z)("MuiAccordionSummary",["root","expanded","focusVisible","disabled","gutters","contentGutters","content","expandIconWrapper"]);var g=r(79114),h=r(57437);let f=e=>{let{classes:t,expanded:r,disabled:n,disableGutters:a}=e;return(0,s.Z)({root:["root",r&&"expanded",n&&"disabled",!a&&"gutters"],focusVisible:["focusVisible"],content:["content",r&&"expanded",!a&&"contentGutters"],expandIconWrapper:["expandIconWrapper",r&&"expanded"]},m,t)},w=(0,i.ZP)(c.Z,{name:"MuiAccordionSummary",slot:"Root",overridesResolver:(e,t)=>t.root})((0,o.Z)(e=>{let{theme:t}=e,r={duration:t.transitions.duration.shortest};return{display:"flex",width:"100%",minHeight:48,padding:t.spacing(0,2),transition:t.transitions.create(["min-height","background-color"],r),[`&.${y.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${y.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity},[`&:hover:not(.${y.disabled})`]:{cursor:"pointer"},variants:[{props:e=>!e.disableGutters,style:{[`&.${y.expanded}`]:{minHeight:64}}}]}})),v=(0,i.ZP)("span",{name:"MuiAccordionSummary",slot:"Content",overridesResolver:(e,t)=>t.content})((0,o.Z)(e=>{let{theme:t}=e;return{display:"flex",textAlign:"start",flexGrow:1,margin:"12px 0",variants:[{props:e=>!e.disableGutters,style:{transition:t.transitions.create(["margin"],{duration:t.transitions.duration.shortest}),[`&.${y.expanded}`]:{margin:"20px 0"}}}]}})),x=(0,i.ZP)("span",{name:"MuiAccordionSummary",slot:"ExpandIconWrapper",overridesResolver:(e,t)=>t.expandIconWrapper})((0,o.Z)(e=>{let{theme:t}=e;return{display:"flex",color:(t.vars||t).palette.action.active,transform:"rotate(0deg)",transition:t.transitions.create("transform",{duration:t.transitions.duration.shortest}),[`&.${y.expanded}`]:{transform:"rotate(180deg)"}}}));var b=n.forwardRef(function(e,t){let r=(0,u.i)({props:e,name:"MuiAccordionSummary"}),{children:s,className:i,expandIcon:o,focusVisibleClassName:c,onClick:l,slots:p,slotProps:m,...y}=r,{disabled:b=!1,disableGutters:A,expanded:$,toggle:Z}=n.useContext(d.Z),P=e=>{Z&&Z(e),l&&l(e)},j={...r,expanded:$,disabled:b,disableGutters:A},C=f(j),R={slots:p,slotProps:m},[E,N]=(0,g.Z)("root",{ref:t,shouldForwardComponentProp:!0,className:(0,a.Z)(C.root,i),elementType:w,externalForwardedProps:{...R,...y},ownerState:j,additionalProps:{focusRipple:!1,disableRipple:!0,disabled:b,"aria-expanded":$,focusVisibleClassName:(0,a.Z)(C.focusVisible,c)},getSlotProps:e=>({...e,onClick:t=>{e.onClick?.(t),P(t)}})}),[S,M]=(0,g.Z)("content",{className:C.content,elementType:v,externalForwardedProps:R,ownerState:j}),[I,U]=(0,g.Z)("expandIconWrapper",{className:C.expandIconWrapper,elementType:x,externalForwardedProps:R,ownerState:j});return(0,h.jsxs)(E,{...N,children:[(0,h.jsx)(S,{...M,children:s}),o&&(0,h.jsx)(I,{...U,children:o})]})})},30731:function(e,t,r){"use strict";r.d(t,{Z:function(){return A}});var n=r(2265),a=r(61994),s=r(20801),i=r(16210),o=r(76301),u=r(37053),c=r(17162),d=r(53410),l=r(31288),p=r(67184),m=r(79114),y=r(94143),g=r(50738);function h(e){return(0,g.ZP)("MuiAccordion",e)}let f=(0,y.Z)("MuiAccordion",["root","heading","rounded","expanded","disabled","gutters","region"]);var w=r(57437);let v=e=>{let{classes:t,square:r,expanded:n,disabled:a,disableGutters:i}=e;return(0,s.Z)({root:["root",!r&&"rounded",n&&"expanded",a&&"disabled",!i&&"gutters"],heading:["heading"],region:["region"]},h,t)},x=(0,i.ZP)(d.Z,{name:"MuiAccordion",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{[`& .${f.region}`]:t.region},t.root,!r.square&&t.rounded,!r.disableGutters&&t.gutters]}})((0,o.Z)(e=>{let{theme:t}=e,r={duration:t.transitions.duration.shortest};return{position:"relative",transition:t.transitions.create(["margin"],r),overflowAnchor:"none","&::before":{position:"absolute",left:0,top:-1,right:0,height:1,content:'""',opacity:1,backgroundColor:(t.vars||t).palette.divider,transition:t.transitions.create(["opacity","background-color"],r)},"&:first-of-type":{"&::before":{display:"none"}},[`&.${f.expanded}`]:{"&::before":{opacity:0},"&:first-of-type":{marginTop:0},"&:last-of-type":{marginBottom:0},"& + &":{"&::before":{display:"none"}}},[`&.${f.disabled}`]:{backgroundColor:(t.vars||t).palette.action.disabledBackground}}}),(0,o.Z)(e=>{let{theme:t}=e;return{variants:[{props:e=>!e.square,style:{borderRadius:0,"&:first-of-type":{borderTopLeftRadius:(t.vars||t).shape.borderRadius,borderTopRightRadius:(t.vars||t).shape.borderRadius},"&:last-of-type":{borderBottomLeftRadius:(t.vars||t).shape.borderRadius,borderBottomRightRadius:(t.vars||t).shape.borderRadius,"@supports (-ms-ime-align: auto)":{borderBottomLeftRadius:0,borderBottomRightRadius:0}}}},{props:e=>!e.disableGutters,style:{[`&.${f.expanded}`]:{margin:"16px 0"}}}]}})),b=(0,i.ZP)("h3",{name:"MuiAccordion",slot:"Heading",overridesResolver:(e,t)=>t.heading})({all:"unset"});var A=n.forwardRef(function(e,t){let r=(0,u.i)({props:e,name:"MuiAccordion"}),{children:s,className:i,defaultExpanded:o=!1,disabled:d=!1,disableGutters:y=!1,expanded:g,onChange:h,square:f=!1,slots:A={},slotProps:$={},TransitionComponent:Z,TransitionProps:P,...j}=r,[C,R]=(0,p.Z)({controlled:g,default:o,name:"Accordion",state:"expanded"}),E=n.useCallback(e=>{R(!C),h&&h(e,!C)},[C,h,R]),[N,...S]=n.Children.toArray(s),M=n.useMemo(()=>({expanded:C,disabled:d,disableGutters:y,toggle:E}),[C,d,y,E]),I={...r,square:f,disabled:d,disableGutters:y,expanded:C},U=v(I),Q={slots:{transition:Z,...A},slotProps:{transition:P,...$}},[T,k]=(0,m.Z)("root",{elementType:x,externalForwardedProps:{...Q,...j},className:(0,a.Z)(U.root,i),shouldForwardComponentProp:!0,ownerState:I,ref:t,additionalProps:{square:f}}),[Y,F]=(0,m.Z)("heading",{elementType:b,externalForwardedProps:Q,className:U.heading,ownerState:I}),[O,X]=(0,m.Z)("transition",{elementType:c.Z,externalForwardedProps:Q,ownerState:I});return(0,w.jsxs)(T,{...k,children:[(0,w.jsx)(Y,{...F,children:(0,w.jsx)(l.Z.Provider,{value:M,children:N})}),(0,w.jsx)(O,{in:C,timeout:"auto",...X,children:(0,w.jsx)("div",{"aria-labelledby":N.props.id,id:N.props["aria-controls"],role:"region",className:U.region,children:S})})]})})},31288:function(e,t,r){"use strict";let n=r(2265).createContext({});t.Z=n},17162:function(e,t,r){"use strict";r.d(t,{Z:function(){return Z}});var n=r(2265),a=r(61994),s=r(52836),i=r(73207),o=r(20801),u=r(16210),c=r(31691),d=r(76301),l=r(37053),p=r(73220),m=r(31090),y=r(60118),g=r(94143),h=r(50738);function f(e){return(0,h.ZP)("MuiCollapse",e)}(0,g.Z)("MuiCollapse",["root","horizontal","vertical","entered","hidden","wrapper","wrapperInner"]);var w=r(57437);let v=e=>{let{orientation:t,classes:r}=e,n={root:["root",`${t}`],entered:["entered"],hidden:["hidden"],wrapper:["wrapper",`${t}`],wrapperInner:["wrapperInner",`${t}`]};return(0,o.Z)(n,f,r)},x=(0,u.ZP)("div",{name:"MuiCollapse",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.orientation],"entered"===r.state&&t.entered,"exited"===r.state&&!r.in&&"0px"===r.collapsedSize&&t.hidden]}})((0,d.Z)(e=>{let{theme:t}=e;return{height:0,overflow:"hidden",transition:t.transitions.create("height"),variants:[{props:{orientation:"horizontal"},style:{height:"auto",width:0,transition:t.transitions.create("width")}},{props:{state:"entered"},style:{height:"auto",overflow:"visible"}},{props:{state:"entered",orientation:"horizontal"},style:{width:"auto"}},{props:e=>{let{ownerState:t}=e;return"exited"===t.state&&!t.in&&"0px"===t.collapsedSize},style:{visibility:"hidden"}}]}})),b=(0,u.ZP)("div",{name:"MuiCollapse",slot:"Wrapper",overridesResolver:(e,t)=>t.wrapper})({display:"flex",width:"100%",variants:[{props:{orientation:"horizontal"},style:{width:"auto",height:"100%"}}]}),A=(0,u.ZP)("div",{name:"MuiCollapse",slot:"WrapperInner",overridesResolver:(e,t)=>t.wrapperInner})({width:"100%",variants:[{props:{orientation:"horizontal"},style:{width:"auto",height:"100%"}}]}),$=n.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiCollapse"}),{addEndListener:o,children:u,className:d,collapsedSize:g="0px",component:h,easing:f,in:$,onEnter:Z,onEntered:P,onEntering:j,onExit:C,onExited:R,onExiting:E,orientation:N="vertical",style:S,timeout:M=p.x9.standard,TransitionComponent:I=s.ZP,...U}=r,Q={...r,orientation:N,collapsedSize:g},T=v(Q),k=(0,c.Z)(),Y=(0,i.Z)(),F=n.useRef(null),O=n.useRef(),X="number"==typeof g?`${g}px`:g,D="horizontal"===N,_=D?"width":"height",L=n.useRef(null),z=(0,y.Z)(t,L),B=e=>t=>{if(e){let r=L.current;void 0===t?e(r):e(r,t)}},G=()=>F.current?F.current[D?"clientWidth":"clientHeight"]:0,W=B((e,t)=>{F.current&&D&&(F.current.style.position="absolute"),e.style[_]=X,Z&&Z(e,t)}),H=B((e,t)=>{let r=G();F.current&&D&&(F.current.style.position="");let{duration:n,easing:a}=(0,m.C)({style:S,timeout:M,easing:f},{mode:"enter"});if("auto"===M){let t=k.transitions.getAutoHeightDuration(r);e.style.transitionDuration=`${t}ms`,O.current=t}else e.style.transitionDuration="string"==typeof n?n:`${n}ms`;e.style[_]=`${r}px`,e.style.transitionTimingFunction=a,j&&j(e,t)}),J=B((e,t)=>{e.style[_]="auto",P&&P(e,t)}),V=B(e=>{e.style[_]=`${G()}px`,C&&C(e)}),q=B(R),K=B(e=>{let t=G(),{duration:r,easing:n}=(0,m.C)({style:S,timeout:M,easing:f},{mode:"exit"});if("auto"===M){let r=k.transitions.getAutoHeightDuration(t);e.style.transitionDuration=`${r}ms`,O.current=r}else e.style.transitionDuration="string"==typeof r?r:`${r}ms`;e.style[_]=X,e.style.transitionTimingFunction=n,E&&E(e)});return(0,w.jsx)(I,{in:$,onEnter:W,onEntered:J,onEntering:H,onExit:V,onExited:q,onExiting:K,addEndListener:e=>{"auto"===M&&Y.start(O.current||0,e),o&&o(L.current,e)},nodeRef:L,timeout:"auto"===M?null:M,...U,children:(e,t)=>{let{ownerState:r,...n}=t;return(0,w.jsx)(x,{as:h,className:(0,a.Z)(T.root,d,{entered:T.entered,exited:!$&&"0px"===X&&T.hidden}[e]),style:{[D?"minWidth":"minHeight"]:X,...S},ref:z,ownerState:{...Q,state:e},...n,children:(0,w.jsx)(b,{ownerState:{...Q,state:e},className:T.wrapper,ref:F,children:(0,w.jsx)(A,{ownerState:{...Q,state:e},className:T.wrapperInner,children:u})})})}})});$&&($.muiSupportAuto=!0);var Z=$},93214:function(e,t,r){"use strict";r.d(t,{cU:function(){return o},xk:function(){return i},yX:function(){return s}});var n=r(83464),a=r(40257);let s=n.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),i=n.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),o=n.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"});n.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))},59459:function(e,t,r){"use strict";r.d(t,{QZ:function(){return A},qA:function(){return $},$F:function(){return Q},He:function(){return T},wv:function(){return U},bh:function(){return N},b$:function(){return S},KK:function(){return M},Py:function(){return R},hb:function(){return I},Yg:function(){return k},mg:function(){return E},P0:function(){return P},Cb:function(){return C},el:function(){return Z},IX:function(){return j}});var n=r(86484),a=r(46172),s=r(93214),i=r(7261);let o=e=>(e.t,new Promise(async(t,r)=>{s.yX.post(a.Y.articles,e.data).then(e=>{i.Am.success("Article added successfully"),e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&r(e)})})),u=e=>(e.t,new Promise(async(t,r)=>{s.yX.post(`${a.Y.articles}/auto`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&r(e)})})),c=e=>{let{data:t,id:r}=e;return new Promise(async(e,n)=>{s.yX.put(`${a.Y.articles}/${r}/auto`,t).then(t=>{t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&n(e)})})},d=e=>{let{data:t,id:r}=e;return new Promise(async(e,n)=>{s.yX.put(`${a.Y.articles}/${r}`,t).then(t=>{i.Am.success("article Commun fields updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&n(e)})})},l=e=>new Promise(async(t,r)=>{try{let r={};r=await s.yX.get(`${a.Y.articles}/${e.language}/blog/${e.urlArticle}`),t(r.data)}catch(e){e&&e.response&&e.response.data&&e.response.status,r(e)}}),p=e=>{let{data:t,language:r,id:n}=e;return new Promise(async(e,o)=>{s.yX.post(`${a.Y.articles}/${r}/${n}`,t).then(t=>{"en"===r&&i.Am.success("Article english updated successfully"),"fr"===r&&i.Am.success("Article french updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&o(e)})})},m=(e,t,r)=>new Promise(async(n,o)=>{try{let o=await s.xk.put(`${a.Y.articles}/${e}/${t}/desarchiver`,{archive:r});o?.data&&(i.Am.success(`Article ${r?"archived":"desarchived"} successfully`),n(o.data))}catch(e){i.Am.error(`Failed to ${r?"archive":"desarchive"} the article.`),o(e)}}),y=e=>new Promise(async(t,r)=>{try{let r=await s.yX.get(`${a.Y.categories}/${e}/all`);t(r.data)}catch(e){r(e)}}),g=e=>new Promise(async(t,r)=>{try{let r=await s.yX.get(`${a.Y.articles}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,searchQuery:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,publishDate:e.publishDate,isThreeLastArticles:e.isThreeLastArticles,isArchived:e.isArchived,categoryName:e.categoryName}});t(r.data)}catch(e){r(e)}}),h=e=>new Promise(async(t,r)=>{try{let r=await s.yX.get(`${a.Y.articles}/dashboard`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,searchQuery:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,publishDate:e.publishDate,isArchived:e.isArchived,categoryName:e.categoryName}});t(r.data)}catch(e){r(e)}}),f=e=>new Promise(async(t,r)=>{try{let r=await s.yX.get(`${a.Y.articles}/${e.language}/listarticle`);t(r.data)}catch(e){r(e)}}),w=e=>{let{articleId:t,pageNumber:r,pageSize:n,sortOrder:i,name:o,approved:u,createdAt:c,paginated:d}=e;return new Promise(async(e,l)=>{try{let l=`${a.v}/comments/${t}?pageSize=${encodeURIComponent(n)}&pageNumber=${encodeURIComponent(r)}&sortOrder=${encodeURIComponent(i)}&paginated=${encodeURIComponent(d)}`;o&&(l+=`&name=${encodeURIComponent(o)}`),u&&(l+=`&approved=${encodeURIComponent(u)}`),c&&(l+=`&createdAt=${encodeURIComponent(new Date(c).toISOString())}`);let p=await s.yX.get(l);e(p.data)}catch(e){l(e)}})},v=(e,t)=>new Promise(async(r,n)=>{try{let n=await s.yX.get(`${a.Y.articles}/${t}/${e}`);r(n.data)}catch(e){n(e)}}),x=e=>new Promise(async(t,r)=>{try{let r=await s.xk.get(`${a.Y.comments}/detail/${e}`);t(r.data)}catch(e){r(e)}}),b=(e,t)=>new Promise(async(t,r)=>{try{let r=await s.yX.get(`${a.Y.articles}/${e}`);t(r.data)}catch(e){r(e)}});r(80657);let A=()=>(0,n.useMutation)({mutationFn:e=>o(e),onError:e=>{e.message=""}}),$=()=>(0,n.useMutation)({mutationFn:e=>u(e),onError:e=>{e.message=""}}),Z=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t)=>c(e,t),onError:e=>{e.message=""}})),P=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t,r)=>p(e,t,r),onError:e=>{e.message=""}})),j=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:e=>{let{language:t,id:r,archive:n}=e;return m(t,r,n)},onError:e=>{console.error("Error during mutation",e),e.message=""}})),C=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t)=>d(e,t),onError:e=>{e.message=""}})),R=e=>(0,n.useQuery)(["category",e],async()=>await y(e)),E=e=>(0,n.useQuery)(["service",e],async()=>await y(e)),N=e=>(0,n.useQuery)("article",async()=>await g(e)),S=e=>(0,n.useQuery)(`articles${e.language}`,async()=>await h(e)),M=e=>(0,n.useQuery)(`articlestitles${e.language}`,async()=>await f(e)),I=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,n.useQuery)("comment",async()=>await w(e),{...t})},U=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,n.useQuery)(["article",e],async()=>{try{return await l(e)}catch(t){throw t.response&&404===t.response.status&&("en"===e.language?window.location.href="/blog/":window.location.href="/fr/blog/"),t}},{onError:e=>{console.error("Error fetching article:",e.message)},...t})},Q=(e,t)=>(0,n.useQuery)(["article",e,t],async()=>await v(e,t)),T=e=>(0,n.useQuery)(["articleall",e],async()=>await b(e)),k=e=>(0,n.useQuery)(["comment",e],async()=>await x(e))},1255:function(e,t,r){"use strict";r.d(t,{yR:function(){return m},Py:function(){return y},xv:function(){return h},xY:function(){return f},VO:function(){return g},Ny:function(){return w}});var n=r(86484),a=r(7261),s=r(46172),i=r(93214);let o=e=>{let t=e.t;return new Promise(async(r,n)=>{i.xk.post(s.Y.categoryGuides,e.data).then(e=>{a.Am.success(t("messages:categoryAdded")),e?.data&&r(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(e?.response?.data?.status===409||e?.status===409)&&a.Am.warning(t("messages:categoryNameExists")),e&&n(e)})})},u=e=>new Promise(async(t,r)=>{try{let r=await (0,i.xk)(`${s.Y.categoryGuides}/catgory/${e}/all`);t(r.data)}catch(e){r(e)}}),c=e=>new Promise(async(t,r)=>{try{let r=await i.xk.get(`${s.Y.guides}/${e.language}/listguide`);t(r.data)}catch(e){r(e)}}),d=e=>new Promise(async(t,r)=>{try{let r=await i.xk.get(`${s.Y.categoryGuides}`,{params:{language:e.language,pageSize:e.pageSize,name:e.name,pageNumber:e.pageNumber,sortOrder:e.sortOrder}});t(r.data)}catch(e){r(e)}}),l=e=>new Promise(async(t,r)=>{try{let r=await i.xk.get(`${s.Y.categoryGuides}/${e}`);t(r.data)}catch(e){r(e)}}),p=e=>{let{data:t,language:r,id:n}=e;return new Promise(async(e,o)=>{i.xk.post(`${s.Y.categoryGuides}/${r}/${n}`,t).then(t=>{"en"===r&&a.Am.success("Category english updated successfully"),"fr"===r&&a.Am.success("Category french updated successfully"),t?.data&&e(t.data)}).catch(e=>{e?.response?.data?.status===500||e?.status===500?a.Am.error("Internal Server Error"):a.Am.error(e.response.data.message)})})},m=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:e=>o(e),onError:e=>{e.message=""}})),y=e=>(0,n.useQuery)(["categoryguides",e],async()=>await u(e)),g=e=>(0,n.useQuery)(`guidestitles${e.language}`,async()=>await c(e)),h=e=>(0,n.useQuery)(["categoryguide",e],async()=>await d(e)),f=e=>(0,n.useQuery)(["categoriesGuideData",e],async()=>await l(e)),w=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t,r)=>p(e,t,r),onError:e=>{e.message=""}}))},41538:function(e,t,r){"use strict";r.d(t,{Z:function(){return h}});var n=r(57437),a=r(30731),s=r(96369),i=r(44164),o=r(89126),u=r(64393),c=r(41327),d=r(68218),l=r(77584),p=r(94013),m=r(52700),y=r(41774),g=r(2265);function h(e){let t,{t:r,itemGuideList:h,setItemGuideList:f}=e,[w,v]=(0,g.useState)("panel"),[x,b]=(0,g.useState)({title:"",description:""});return(0,n.jsxs)(a.Z,{id:"accordion",disableGutters:!0,expanded:"panel"===w,onChange:(t="panel",(e,r)=>{v(!!r&&t)}),children:[(0,n.jsx)(s.Z,{expandIcon:(0,n.jsx)(m.Z,{}),"aria-controls":"panel-content",id:"panel-header",children:r("guides:addItemList")}),(0,n.jsxs)(i.Z,{className:"accordion-detail",elevation:0,children:[(0,n.jsx)(o.Z,{children:(0,n.jsxs)(u.Z,{className:"label-form",children:[r("guides:title"),(0,n.jsx)(c.Z,{className:"select-pentabell",variant:"standard",sx:{m:1,minWidth:120},children:(0,n.jsx)(d.Z,{type:"text",name:"itemTitle",value:h?.itemTitle,onChange:e=>{f({...h,itemTitle:e.target.value})}})})]})}),(0,n.jsx)(o.Z,{children:(0,n.jsxs)(u.Z,{className:"label-form",children:["Description",(0,n.jsx)(l.Z,{variant:"standard",type:"text",rows:4,multiline:!0,name:"itemDescription",value:h?.itemDescription,onChange:e=>{f({...h,itemDescription:e.target.value})},className:"textArea-pentabell"})]})}),(0,n.jsxs)("div",{className:"inline-group flex-end",children:[(0,n.jsx)(o.Z,{children:(0,n.jsxs)(u.Z,{className:"label-form",children:[r("guides:subtitle"),(0,n.jsx)(c.Z,{className:"select-pentabell",variant:"standard",sx:{m:1,minWidth:120},children:(0,n.jsx)(d.Z,{type:"text",name:"title",value:x.title,onChange:e=>{b({...x,title:e.target.value})}})})]})}),(0,n.jsx)(o.Z,{children:(0,n.jsxs)(u.Z,{className:"label-form",children:[r("guides:subtitleDescription"),(0,n.jsx)(c.Z,{className:"select-pentabell",variant:"standard",sx:{m:1,minWidth:120},children:(0,n.jsx)(l.Z,{variant:"standard",type:"text",rows:6,name:"description",value:x.description,onChange:e=>{b({...x,description:e.target.value})},className:"textArea-pentabell"})})]})}),(0,n.jsx)(y.default,{text:r("guides:addSubtitle"),className:"btn btn-filled blue",onClick:()=>{""!==x.title.trim()&&""!==x.description.trim()&&(f(e=>{let t=Array.isArray(e?.subtitles)?[...e.subtitles,x]:[x];return{...e,subtitles:t}}),b({title:"",description:""}))}})]}),(0,n.jsx)("div",{children:h?.subtitles?.map((e,t)=>n.jsxs("div",{className:"item-list-guide",children:[n.jsxs("div",{className:"subtitle-item",children:[n.jsxs("p",{children:[r("guides:subtitle")," ",t+1]}),n.jsx(p.Z,{className:"remove-button",onClick:()=>f(e=>({...e,subtitles:e?.subtitles?.filter((e,r)=>r!==t)||[]})),children:r("guides:remove")})]}),n.jsxs("p",{className:"subtitle-title",children:[n.jsxs("strong",{children:[r("guides:title"),":"]})," ",e.title]}),n.jsxs("p",{className:"subtitle-description",children:[n.jsx("strong",{children:"Description:"})," ",n.jsx("br",{}),e.description]})]},t))})]})]},"panel")}},48839:function(e,t,r){"use strict";r.d(t,{_r:function(){return A},Zm:function(){return g},Fx:function(){return f},Px:function(){return w},cp:function(){return v},XJ:function(){return h},sv:function(){return x},Qv:function(){return b}});var n=r(86484),a=r(46172),s=r(93214),i=r(7261);let o=e=>(e.t,new Promise(async(t,r)=>{s.xk.post("/guides",e.data).then(e=>{i.Am.success("guide added successfully"),e?.data&&t(e.data)}).catch(e=>{e?.response?.data?.status!==500&&i.Am.error(e.response.data.message),e&&r(e)})})),u=e=>{let{guideId:t,pageNumber:r,pageSize:n,sortOrder:a,firstName:i,createdAt:o,paginated:u}=e;return new Promise(async(e,c)=>{try{let c=`/downloads/${t}?pageSize=${encodeURIComponent(n)}&pageNumber=${encodeURIComponent(r)}&sortOrder=${encodeURIComponent(a)}&paginated=${encodeURIComponent(u)}`;i&&(c+=`&firstName=${encodeURIComponent(i)}`),o&&(c+=`&createdAt=${encodeURIComponent(new Date(o).toISOString())}`);let d=await s.xk.get(c);e(d.data)}catch(e){c(e)}})},c=e=>new Promise(async(t,r)=>{try{let r=await s.yX.get(`${a.Y.guides}/${e}`);t(r.data)}catch(e){r(e)}}),d=(e,t)=>new Promise(async(r,n)=>{try{let n=await s.yX.get(`${a.Y.guides}/guide/${t}/${e}`);r(n.data)}catch(e){n(e)}}),l=e=>{let{data:t,id:r}=e;return new Promise(async(e,n)=>{s.yX.put(`${a.Y.guides}/${r}`,t).then(t=>{i.Am.success("guide Commun fields updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&n(e)})})},p=e=>{let{data:t,language:r,id:n}=e;return new Promise(async(e,o)=>{s.xk.post(`${a.Y.guides}/update/${r}/${n}`,t).then(t=>{"en"===r&&i.Am.success("guide english updated successfully"),"fr"===r&&i.Am.success("guide french updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&o(e)})})},m=e=>new Promise(async(t,r)=>{try{let r=await s.xk.get(`${a.Y.guides}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,cible:e.cible,pageNumber:e.pageNumber,sortOrder:e.sortOrder,searchQuery:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,dashboard:!0}});t(r.data)}catch(e){r(e)}}),y=(e,t)=>new Promise(async(r,n)=>{try{let n=await s.xk.delete(`${a.Y.guides}/${e}/${t}`);n?.data&&(i.Am.success(`${"en"===e?"English":"fr"===e?"French":""} article version archived successfully`),r(n.data))}catch(e){i.Am.error("Failed to archive the article"),n(e)}}),g=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:e=>o(e),onError:e=>{e.message=""}})),h=e=>(0,n.useQuery)("guides",async()=>await m(e)),f=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,n.useQuery)("downloadsGuides",async()=>await u(e),{...t})},w=e=>(0,n.useQuery)(["guide",e],async()=>await c(e)),v=(e,t)=>(0,n.useQuery)(["guide",e,t],async()=>await d(e,t)),x=()=>(0,n.useMutation)({mutationFn:(e,t,r)=>p(e,t,r),onError:e=>{e.message=""}}),b=()=>(0,n.useMutation)({mutationFn:(e,t)=>l(e,t),onError:e=>{e.message=""}}),A=()=>(0,n.useMutation)({mutationFn:e=>{let{language:t,guideId:r}=e;return y(t,r)},onError:e=>{console.error("Error during mutation",e),e.message=""}})},62953:function(e,t,r){"use strict";r.d(t,{$i:function(){return g},BF:function(){return y},Fe:function(){return i},Gc:function(){return d},HF:function(){return s},Hr:function(){return u},IZ:function(){return m},NF:function(){return c},PM:function(){return o},UJ:function(){return l},jd:function(){return p}});var n=r(86484),a=r(49443);r(99376),r(80657);let s=()=>(0,n.useMutation)({mutationFn:e=>(0,a.W3)(e),onError:e=>{e.message=""}}),i=e=>(0,n.useQuery)("opportunities",async()=>await (0,a.fH)(e)),o=()=>(0,n.useMutation)(()=>(0,a.AE)()),u=e=>(0,n.useQuery)(["opportunities",e],async()=>await (0,a.Mq)(e)),c=()=>(0,n.useMutation)({mutationFn:(e,t,r)=>(0,a.rE)(e,t,r),onError:e=>{e.message=""}}),d=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t)=>(0,a.S1)(e,t),onError:e=>{e.message=""}})),l=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t,r)=>(0,a.lU)(e,t,r),onError:e=>{e.message=""}})),p=()=>{let e=(0,n.useQueryClient)();return(0,n.useMutation)({mutationFn:(e,t,r,n)=>(0,a.yH)(e,t,r,n),onSuccess:t=>{e.invalidateQueries("files")}})},m=()=>(0,n.useQuery)("SeoOpportunities",async()=>await (0,a.yJ)()),y=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t)=>(0,a.mt)(e,t),onError:e=>{e.message=""}})),g=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:e=>{let{language:t,id:r,archive:n}=e;return(0,a.TK)(t,r,n)},onError:e=>{console.error("Error during mutation",e),e.message=""}}))},49443:function(e,t,r){"use strict";r.d(t,{AE:function(){return c},Mq:function(){return u},S1:function(){return l},TK:function(){return g},W3:function(){return i},fH:function(){return o},lU:function(){return p},mt:function(){return h},rE:function(){return d},yH:function(){return m},yJ:function(){return y}});var n=r(46172),a=r(93214),s=r(7261);let i=e=>(e.t,new Promise(async(t,r)=>{a.yX.post(`/opportunities${n.Y.applications}/${e.opportunityId}`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&r(e)})})),o=e=>new Promise(async(t,r)=>{try{let r=await a.yX.get(`${n.Y.opportunity}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyWord:e.keyWord,visibility:e.visibility,title:e.title,createdAt:e.createdAt,publishDate:e.publishDate,country:e.country,industry:e.industry,contractType:e.contractType,minExperience:e.minExperience,maxExperience:e.maxExperience,jobDescriptionLanguages:e.jobDescriptionLanguages,reference:e.reference,opportunityType:e.opportunityType,exclude:"In House"!==e.opportunityType&&"true"}});t(r.data)}catch(e){r(e)}}),u=e=>new Promise(async(t,r)=>{try{let r=await a.yX.get(`${n.Y.opportunity}/${e}`);t(r.data)}catch(e){r(e)}}),c=async()=>(await a.xk.put("/UpdateJobdescription")).data,d=e=>{let{data:t,language:r,id:i}=e;return new Promise(async(e,o)=>{a.yX.post(`${n.Y.opportunity}/${r}/${i}`,t).then(t=>{"en"===r&&s.Am.success("Opportunity english updated successfully"),"fr"===r&&s.Am.success("Opportunity french updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&o(e)})})},l=e=>{let{data:t,id:r}=e;return new Promise(async(e,i)=>{a.yX.put(`${n.Y.opportunity}/${r}`,t).then(t=>{s.Am.success("Opportunity Commun fields updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})})},p=e=>{let{id:t,title:r,typeOfFavourite:i}=e;return new Promise(async(e,o)=>{a.yX.put(`${n.Y.baseUrl}/favourite/${t}`,{type:i}).then(t=>{s.Am.success(`${i} : ${r} saved to your favorites.`),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data&&409===e.response.status&&s.Am.warning(` ${r} already in shortlist`),e&&o(e)})})},m=e=>{let{resource:t,folder:r,filename:i,body:o}=e;return new Promise(async(e,u)=>{a.cU.post(`${n.Y.files}/uploadResume/${t}/${r}/${i}`,o.formData).then(t=>{t.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data&&(400===e.response.status?e.response.data.message.includes("The resume lacks essential information")?s.Am.warn(o.t("messages:requireResume")):s.Am.warn(e.response.data.message):500===e.response.status&&s.Am.error("Internal Server Error")),e&&u(e)})})},y=()=>new Promise(async(e,t)=>{try{let t=await a.yX.get(`${n.Y.seoOpportunity}`);e(t.data)}catch(e){t(e)}}),g=(e,t,r)=>new Promise(async(i,o)=>{try{let o=await a.yX.put(`${n.Y.opportunity}/${e}/${t}/desarchiver`,{archive:r});o?.data&&(s.Am.success(`opportunity ${r?"archived":"desarchived"} successfully`),i(o.data))}catch(e){s.Am.error(`Failed to ${r?"archive":"desarchive"} the opportunity.`),o(e)}}),h=e=>{let{data:t,id:r}=e;return new Promise(async(e,i)=>{a.yX.put(`${n.Y.seoOpportunity}/${r}`,t).then(t=>{s.Am.success("Opportunity seo updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})})}},46172:function(e,t,r){"use strict";r.d(t,{Y:function(){return a},v:function(){return n}});let n=r(40257).env.NEXT_PUBLIC_BASE_API_URL,a={seo:"seoTags",auth:"/auth/signin",logout:"/auth/logout",candidatures:"/applications",signup:"/auth/signup",forgetPassword:"/auth/forgot-password",resetPassword:"/auth/reset-password",guides:"/guides",currentUser:"/users/current",updateUser:"/users",users:"/users",categoryGuides:"guidecategory",candidate:"/candidates",report:"/report",skills:"/skills",files:"/files",applications:"/applications",sliders:"/sliders",favoris:"/candidate/favourite",articles:"/articles",categories:"/categories",blog:"/blog",category:"/categories",opportunity:"/opportunities",seoOpportunity:"/seoOpportunity",newsletter:"/newsletter",contact:"/contact",favourite:"/favourite",contacts:"contacts",comments:"/comments",statistics:"/statistics",events:"/events",baseUrl:`${n}`}},19841:function(e,t){"use strict";t.Z={src:"/_next/static/media/add.7d9a0730.png",height:29,width:29,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEUjT5YfPJMjTpUjT5chT5UjTpckT5YkT5SyXEFwAAAACHRSTlOKAQ+aI3pIMEwTVZQAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAvSURBVHicJYrJEQAgEIPIofbfsbOaFxPAi1lhS9IJSLbEwMIPnPQ/7VOd2GF6yL4OogBsGLyWkgAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8}}}]);