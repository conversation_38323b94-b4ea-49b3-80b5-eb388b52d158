"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.hasRoles = void 0;
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const messages_1 = require("@/utils/helpers/messages");
const hasRoles = (roles) => {
    return async (request, response, next) => {
        const userRoles = request.user.roles;
        const hasRole = roles.some(role => userRoles.includes(role));
        if (!hasRole) {
            return next(new http_exception_1.default(403, messages_1.MESSAGES.AUTH.FORBIDDEN));
        }
        return next();
    };
};
exports.hasRoles = hasRoles;
//# sourceMappingURL=authorization.middleware.js.map