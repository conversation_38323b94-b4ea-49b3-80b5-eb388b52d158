import banner from "@/assets/images/website/banner/Pentabell-Tunisia.webp";
import BannerComponents from "@/components/sections/BannerComponents";
import OurPartners from "@/components/sections/OurPartners";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import GlobalHRServicesSection from "@/components/sections/GlobalHRServicesSection";
import r1 from "@/assets/images/services/recrutement1.png";
import r2 from "@/assets/images/services/recrutement2.png";
import r3 from "@/assets/images/services/recrutement3.png";
import r4 from "@/assets/images/services/recrutement4.png";
import serviceimgS1 from "@/assets/images/services/service1.png";

import BusinessInTunisia from "@/components/offices-sections/BusinessInTunisia";
import TunisiaOfficePageForm from "@/features/forms/components/TunisiaOfficePageForm";
import ComplexityControlSection from "@/components/offices-sections/ComplexityControlSection";
import EORServicesTN from "@/components/offices-sections/EORServicesTN";
import OfficeInfoTN from "@/components/offices-sections/OfficeInfoTN";
import OfficeLocationMap from "@/components/offices-sections/OfficeLocationMap";
import initTranslations from "@/app/i18n";
import TunisiaLaborLaws from "../../../../components/labor-laws/TunisiaLaborLaws";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJsonSSR } from "@/config/axios";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }hiring-employees-tunisia-guide/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/hiring-employees-tunisia-guide/`,
    en: `https://www.pentabell.com/hiring-employees-tunisia-guide/`,
    "x-default": `https://www.pentabell.com/hiring-employees-tunisia-guide/`,
  };
  const { t } = await initTranslations(locale, ["servicesByCountry"]);
  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/hiring-employees-tunisia-guide`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("servicesByCountry:tunisia:metaTitle"),
    description: t("servicesByCountry:tunisia:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}
async function hiringEmployeesTunisiaGuide({ params: { locale } }) {
  const { t } = await initTranslations(locale, ["Tunisia"]);

  const SERVICES = [
    {
      id: "s1",
      title: t("Tunisia:services:dataS1:title"),
      description: t("Tunisia:services:dataS1:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: serviceimgS1,
      altImg: t("Tunisia:services:dataS1:altImg"),
    },
    {
      id: "s2",
      title: t("Tunisia:services:dataS2:title"),
      description: t("Tunisia:services:dataS2:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r4,
      altImg: t("Tunisia:services:dataS2:altImg"),
    },
    {
      id: "s3",
      title: t("Tunisia:services:dataS3:title"),
      description: t("Tunisia:services:dataS3:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r3,
      altImg: t("Tunisia:services:dataS3:altImg"),
    },
    {
      id: "s4",
      title: t("Tunisia:services:dataS4:title"),
      description: t("Tunisia:services:dataS4:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r2,
      altImg: t("Tunisia:services:dataS4:altImg"),
    },
    {
      id: "s5",
      title: t("Tunisia:services:dataS5:title"),
      description: t("Tunisia:services:dataS5:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r1,
      altImg: t("Tunisia:services:dataS5:altImg"),
    },
  ];
  return (
    <div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            name: t("contactUs:bureux:contacts:tunisia"),
            address: {
              "@type": "PostalAddress",
              streetAddress: "Imm. MADIBA, Rue Khawarizmi",
              addressLocality: "La Goulette",
              postalCode: "2015",
              addressCountry: "TN",
            },
            telephone: ["+216 31 385 510"],
            email: "<EMAIL>",
            url:
              locale === "en"
                ? "https://www.pentabell.com/hiring-employees-tunisia-guide/"
                : `https://www.pentabell.com/${locale}/hiring-employees-tunisia-guide/`,
          }),
        }}
      />
      <BannerComponents
        title={t("Tunisia:title")}
        description={t("Tunisia:description")}
        bannerImg={banner}
        height={"100vh"}
        altImg={t("Tunisia:altImg")}
      />
      <OurPartners disableTxt={true} />
      <ResponsiveRowTitleText
        title={t("Tunisia:intro:title")}
        paragraph={t("Tunisia:intro:description")}
      />
      <OfficeInfoTN t={t} />
      <BusinessInTunisia t={t} />
      <EORServicesTN t={t} />
      <OfficeLocationMap t={t} />
      <ComplexityControlSection t={t} />
      <GlobalHRServicesSection
        title={t("Tunisia:services:title")}
        SERVICES={SERVICES}
        defaultImage={serviceimgS1}
      />
      <TunisiaLaborLaws />
      <TunisiaOfficePageForm country={"Tunisia"} />
    </div>
  );
}

export default hiringEmployeesTunisiaGuide;
