#tags .ReactTags__tags {
  position: relative;
  display: flex;
  align-items: flex-start;
  border-bottom: 1px solid #798ba3;
  max-height: 120px;
  overflow-y: auto;
  flex-wrap: wrap;
  width: 100%;

  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }

  /* Firefox scrollbar */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;

  /* Smooth scrolling */
  scroll-behavior: smooth;

  /* Better spacing when scrolling */
  &:not(:empty) {
    padding-bottom: 6px;
  }

  /* Empty state - reset to normal layout */
  &:empty,
  &:has(.ReactTags__selected:empty) {
    display: block;
    align-items: normal;
    flex-wrap: nowrap;
    max-height: none;
    overflow: visible;
    // padding: 4px 10px;
  }
}

#tags .ReactTags__selected {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  max-width: 100%;
  margin-right: 8px;

  /* When empty, don't take up space */
  &:empty {
    display: none;
    margin-right: 0;
  }
}

#tags .ReactTags__clearAll {
  cursor: pointer;
  padding: 10px;
  margin: 10px;
  background: #f88d8d;
  color: #fff;
  border: none;
}

#tags .ReactTags__tagInput {
  border-radius: 2px;
  display: inline-block;
  flex: 1;
  min-width: 120px;
  margin-top: 2px;
}

/* When no tags are present, input takes full width */
#tags .ReactTags__tags:has(.ReactTags__selected:empty) .ReactTags__tagInput,
#tags .ReactTags__tags .ReactTags__selected:empty + .ReactTags__tagInput {
  flex: none;
  width: 100%;
  margin-top: 0;
  min-width: 100%;
}

/* Fallback for browsers that don't support :has() */
#tags .ReactTags__tags.no-tags .ReactTags__tagInput {
  flex: none;
  width: 100%;
  margin-top: 0;
  min-width: 100%;
}

#tags .ReactTags__tags.no-tags {
  display: block;
  align-items: normal;
  flex-wrap: nowrap;
  max-height: none;
  overflow: visible;
  padding: 4px 10px;
}

#tags .ReactTags__tagInput input.ReactTags__tagInputField,
#tags .ReactTags__tagInput input.ReactTags__tagInputField:focus {
  height: 31px;
  margin: 0;
  font-size: 12px;
  min-width: 120px;
  width: 100%;
  padding: 0px 10px;
  border: none;
  outline: none;
}

#tags .ReactTags__editInput {
  border-radius: 1px;
}

#tags .ReactTags__editTagInput {
  display: inline-flex;
}

#tags .ReactTags__selected span.ReactTags__tag {
  background: $lightBlue;
  border-radius: 15px;
  color: #000000;
  font-size: 12px;
  display: flex;
  padding: 5px;
  margin: 2px 0;
  white-space: nowrap;
  align-items: center;
}

#tags button.ReactTags__remove {
  color: #000000;
  margin-left: 5px;
  cursor: pointer;
}

#tags .ReactTags__suggestions {
  position: absolute;
}

#tags .ReactTags__suggestions li {
  border-bottom: 1px solid #ddd;
  padding: 5px 10px;
  margin: 0;
}

#tags .ReactTags__suggestions li mark {
  text-decoration: underline;
  background: none;
  font-weight: 600;
}

#tags .ReactTags__suggestions ul li.ReactTags__activeSuggestion {
  background: #b7cfe0;
  cursor: pointer;
}

#tags .ReactTags__remove {
  border: none;
  cursor: pointer;
  background: none;
  color: #798ba3;
  margin-left: 5px;
}

#tags input {
  border: none;
  outline: none;
}

#tags input:before {
  border: none;
}

// Responsive adjustments for scroll behavior
@media (max-width: 768px) {
  #tags .ReactTags__tags {
    max-height: 100px; // Smaller height on mobile
    padding: 6px 8px;
  }

  #tags .ReactTags__tagInput {
    min-width: 100px;
  }

  #tags .ReactTags__tagInput input.ReactTags__tagInputField {
    font-size: 14px; // Larger font on mobile
    min-width: 100px;
  }

  #tags .ReactTags__selected span.ReactTags__tag {
    font-size: 11px;
    padding: 4px 6px;
  }
}
