(()=>{var e={};e.id=2360,e.ids=[2360],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},43667:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>f,originalPathname:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>u}),t(52669),t(30962),t(23658),t(54864);var s=t(23191),i=t(88716),n=t(37922),o=t.n(n),a=t(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let u=["",{children:["[locale]",{children:["(website)",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,52669)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\reset-password\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30962)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\layout.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,23658)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54864)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\not-found.js"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\reset-password\\page.jsx"],p="/[locale]/(website)/reset-password/page",f={require:t,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/[locale]/(website)/reset-password/page",pathname:"/[locale]/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},75826:(e,r,t)=>{Promise.resolve().then(t.bind(t,30677))},66042:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var s=t(27522),i=t(10326);let n=(0,s.Z)((0,i.jsx)("path",{d:"M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5M12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5m0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3"}),"Visibility")},96741:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var s=t(27522),i=t(10326);let n=(0,s.Z)((0,i.jsx)("path",{d:"M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7M2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2m4.31-.78 3.15 3.15.02-.16c0-1.66-1.34-3-3-3z"}),"VisibilityOff")},57329:(e,r,t)=>{"use strict";t.d(r,{Z:()=>w});var s,i=t(17577),n=t(41135),o=t(88634),a=t(54641),l=t(25609),u=t(45011),c=t(65656),p=t(91703),f=t(30990),h=t(2791),d=t(71685),m=t(97898);function E(e){return(0,m.ZP)("MuiInputAdornment",e)}let g=(0,d.Z)("MuiInputAdornment",["root","filled","standard","outlined","positionStart","positionEnd","disablePointerEvents","hiddenLabel","sizeSmall"]);var y=t(10326);let v=e=>{let{classes:r,disablePointerEvents:t,hiddenLabel:s,position:i,size:n,variant:l}=e,u={root:["root",t&&"disablePointerEvents",i&&`position${(0,a.Z)(i)}`,l,s&&"hiddenLabel",n&&`size${(0,a.Z)(n)}`]};return(0,o.Z)(u,E,r)},b=(0,p.ZP)("div",{name:"MuiInputAdornment",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.root,r[`position${(0,a.Z)(t.position)}`],!0===t.disablePointerEvents&&r.disablePointerEvents,r[t.variant]]}})((0,f.Z)(({theme:e})=>({display:"flex",maxHeight:"2em",alignItems:"center",whiteSpace:"nowrap",color:(e.vars||e).palette.action.active,variants:[{props:{variant:"filled"},style:{[`&.${g.positionStart}&:not(.${g.hiddenLabel})`]:{marginTop:16}}},{props:{position:"start"},style:{marginRight:8}},{props:{position:"end"},style:{marginLeft:8}},{props:{disablePointerEvents:!0},style:{pointerEvents:"none"}}]}))),w=i.forwardRef(function(e,r){let t=(0,h.i)({props:e,name:"MuiInputAdornment"}),{children:o,className:a,component:p="div",disablePointerEvents:f=!1,disableTypography:d=!1,position:m,variant:E,...g}=t,w=(0,c.Z)()||{},S=E;E&&w.variant,w&&!S&&(S=w.variant);let $={...t,hiddenLabel:w.hiddenLabel,size:w.size,disablePointerEvents:f,position:m,variant:S},x=v($);return(0,y.jsx)(u.Z.Provider,{value:null,children:(0,y.jsx)(b,{as:p,ownerState:$,className:(0,n.Z)(x.root,a),ref:r,...g,children:"string"!=typeof o||d?(0,y.jsxs)(i.Fragment,{children:["start"===m?s||(s=(0,y.jsx)("span",{className:"notranslate","aria-hidden":!0,children:"​"})):null,o]}):(0,y.jsx)(l.default,{color:"textSecondary",children:o})})})})},29916:(e,r,t)=>{"use strict";var s=t(78893).Buffer,i=t(78893).SlowBuffer;function n(e,r){if(!s.isBuffer(e)||!s.isBuffer(r)||e.length!==r.length)return!1;for(var t=0,i=0;i<e.length;i++)t|=e[i]^r[i];return 0===t}e.exports=n,n.install=function(){s.prototype.equal=i.prototype.equal=function(e){return n(this,e)}};var o=s.prototype.equal,a=i.prototype.equal;n.restore=function(){s.prototype.equal=o,i.prototype.equal=a}},3164:(e,r,t)=>{"use strict";var s=t(90892).Buffer,i=t(21887);function n(e){if(s.isBuffer(e))return e;if("string"==typeof e)return s.from(e,"base64");throw TypeError("ECDSA signature must be a Base64 string or a Buffer")}function o(e,r,t){for(var s=0;r+s<t&&0===e[r+s];)++s;return e[r+s]>=128&&--s,s}e.exports={derToJose:function(e,r){e=n(e);var t=i(r),o=t+1,a=e.length,l=0;if(48!==e[l++])throw Error('Could not find expected "seq"');var u=e[l++];if(129===u&&(u=e[l++]),a-l<u)throw Error('"seq" specified length of "'+u+'", only "'+(a-l)+'" remaining');if(2!==e[l++])throw Error('Could not find expected "int" for "r"');var c=e[l++];if(a-l-2<c)throw Error('"r" specified length of "'+c+'", only "'+(a-l-2)+'" available');if(o<c)throw Error('"r" specified length of "'+c+'", max of "'+o+'" is acceptable');var p=l;if(l+=c,2!==e[l++])throw Error('Could not find expected "int" for "s"');var f=e[l++];if(a-l!==f)throw Error('"s" specified length of "'+f+'", expected "'+(a-l)+'"');if(o<f)throw Error('"s" specified length of "'+f+'", max of "'+o+'" is acceptable');var h=l;if((l+=f)!==a)throw Error('Expected to consume entire buffer, but "'+(a-l)+'" bytes remain');var d=t-c,m=t-f,E=s.allocUnsafe(d+c+m+f);for(l=0;l<d;++l)E[l]=0;e.copy(E,l,p+Math.max(-d,0),p+c),l=t;for(var g=l;l<g+m;++l)E[l]=0;return e.copy(E,l,h+Math.max(-m,0),h+f),E=(E=E.toString("base64")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},joseToDer:function(e,r){e=n(e);var t=i(r),a=e.length;if(a!==2*t)throw TypeError('"'+r+'" signatures must be "'+2*t+'" bytes, saw "'+a+'"');var l=o(e,0,t),u=o(e,t,e.length),c=t-l,p=t-u,f=2+c+1+1+p,h=f<128,d=s.allocUnsafe((h?2:3)+f),m=0;return d[m++]=48,h?d[m++]=f:(d[m++]=129,d[m++]=255&f),d[m++]=2,d[m++]=c,l<0?(d[m++]=0,m+=e.copy(d,m,0,t)):m+=e.copy(d,m,l,t),d[m++]=2,d[m++]=p,u<0?(d[m++]=0,e.copy(d,m,t)):e.copy(d,m,t+u),d}}},21887:e=>{"use strict";function r(e){return(e/8|0)+(e%8==0?0:1)}var t={ES256:r(256),ES384:r(384),ES512:r(521)};e.exports=function(e){var r=t[e];if(r)return r;throw Error('Unknown algorithm "'+e+'"')}},85911:(e,r,t)=>{var s=t(8287);e.exports=function(e,r){r=r||{};var t=s.decode(e,r);if(!t)return null;var i=t.payload;if("string"==typeof i)try{var n=JSON.parse(i);null!==n&&"object"==typeof n&&(i=n)}catch(e){}return!0===r.complete?{header:t.header,payload:i,signature:t.signature}:i}},98327:(e,r,t)=>{e.exports={decode:t(85911),verify:t(17910),sign:t(85917),JsonWebTokenError:t(69062),NotBeforeError:t(13020),TokenExpiredError:t(82504)}},69062:e=>{var r=function(e,r){Error.call(this,e),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.name="JsonWebTokenError",this.message=e,r&&(this.inner=r)};r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,e.exports=r},13020:(e,r,t)=>{var s=t(69062),i=function(e,r){s.call(this,e),this.name="NotBeforeError",this.date=r};i.prototype=Object.create(s.prototype),i.prototype.constructor=i,e.exports=i},82504:(e,r,t)=>{var s=t(69062),i=function(e,r){s.call(this,e),this.name="TokenExpiredError",this.expiredAt=r};i.prototype=Object.create(s.prototype),i.prototype.constructor=i,e.exports=i},2511:(e,r,t)=>{let s=t(77801);e.exports=s.satisfies(process.version,">=15.7.0")},20499:(e,r,t)=>{var s=t(77801);e.exports=s.satisfies(process.version,"^6.12.0 || >=8.0.0")},80980:(e,r,t)=>{let s=t(77801);e.exports=s.satisfies(process.version,">=16.9.0")},59477:(e,r,t)=>{var s=t(87914);e.exports=function(e,r){var t=r||Math.floor(Date.now()/1e3);if("string"==typeof e){var i=s(e);if(void 0===i)return;return Math.floor(t+i/1e3)}if("number"==typeof e)return t+e}},40451:(e,r,t)=>{let s=t(2511),i=t(80980),n={ec:["ES256","ES384","ES512"],rsa:["RS256","PS256","RS384","PS384","RS512","PS512"],"rsa-pss":["PS256","PS384","PS512"]},o={ES256:"prime256v1",ES384:"secp384r1",ES512:"secp521r1"};e.exports=function(e,r){if(!e||!r)return;let t=r.asymmetricKeyType;if(!t)return;let a=n[t];if(!a)throw Error(`Unknown key type "${t}".`);if(!a.includes(e))throw Error(`"alg" parameter for "${t}" key type must be one of: ${a.join(", ")}.`);if(s)switch(t){case"ec":let l=r.asymmetricKeyDetails.namedCurve,u=o[e];if(l!==u)throw Error(`"alg" parameter "${e}" requires curve "${u}".`);break;case"rsa-pss":if(i){let t=parseInt(e.slice(-3),10),{hashAlgorithm:s,mgf1HashAlgorithm:i,saltLength:n}=r.asymmetricKeyDetails;if(s!==`sha${t}`||i!==s)throw Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${e}.`);if(void 0!==n&&n>t>>3)throw Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${e}.`)}}}},81272:(e,r,t)=>{let s=Symbol("SemVer ANY");class i{static get ANY(){return s}constructor(e,r){if(r=n(r),e instanceof i){if(!!r.loose===e.loose)return e;e=e.value}u("comparator",e=e.trim().split(/\s+/).join(" "),r),this.options=r,this.loose=!!r.loose,this.parse(e),this.semver===s?this.value="":this.value=this.operator+this.semver.version,u("comp",this)}parse(e){let r=this.options.loose?o[a.COMPARATORLOOSE]:o[a.COMPARATOR],t=e.match(r);if(!t)throw TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==t[1]?t[1]:"","="===this.operator&&(this.operator=""),t[2]?this.semver=new c(t[2],this.options.loose):this.semver=s}toString(){return this.value}test(e){if(u("Comparator.test",e,this.options.loose),this.semver===s||e===s)return!0;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,r){if(!(e instanceof i))throw TypeError("a Comparator is required");return""===this.operator?""===this.value||new p(e.value,r).test(this.value):""===e.operator?""===e.value||new p(this.value,r).test(e.semver):!((r=n(r)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!r.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||l(this.semver,"<",e.semver,r)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||l(this.semver,">",e.semver,r)&&this.operator.startsWith("<")&&e.operator.startsWith(">"))}}e.exports=i;let n=t(49587),{safeRe:o,t:a}=t(5316),l=t(8741),u=t(75868),c=t(57023),p=t(15609)},15609:(e,r,t)=>{let s=/\s+/g;class i{constructor(e,r){if(r=o(r),e instanceof i){if(!!r.loose===e.loose&&!!r.includePrerelease===e.includePrerelease)return e;return new i(e.raw,r)}if(e instanceof a)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease,this.raw=e.trim().replace(s," "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let e=this.set[0];if(this.set=this.set.filter(e=>!g(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1){for(let e of this.set)if(1===e.length&&y(e[0])){this.set=[e];break}}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let r=this.set[e];for(let e=0;e<r.length;e++)e>0&&(this.formatted+=" "),this.formatted+=r[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let r=((this.options.includePrerelease&&m)|(this.options.loose&&E))+":"+e,t=n.get(r);if(t)return t;let s=this.options.loose,i=s?c[p.HYPHENRANGELOOSE]:c[p.HYPHENRANGE];l("hyphen replace",e=e.replace(i,P(this.options.includePrerelease))),l("comparator trim",e=e.replace(c[p.COMPARATORTRIM],f)),l("tilde trim",e=e.replace(c[p.TILDETRIM],h)),l("caret trim",e=e.replace(c[p.CARETTRIM],d));let o=e.split(" ").map(e=>b(e,this.options)).join(" ").split(/\s+/).map(e=>O(e,this.options));s&&(o=o.filter(e=>(l("loose invalid filter",e,this.options),!!e.match(c[p.COMPARATORLOOSE])))),l("range list",o);let u=new Map;for(let e of o.map(e=>new a(e,this.options))){if(g(e))return[e];u.set(e.value,e)}u.size>1&&u.has("")&&u.delete("");let y=[...u.values()];return n.set(r,y),y}intersects(e,r){if(!(e instanceof i))throw TypeError("a Range is required");return this.set.some(t=>v(t,r)&&e.set.some(e=>v(e,r)&&t.every(t=>e.every(e=>t.intersects(e,r)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new u(e,this.options)}catch(e){return!1}for(let r=0;r<this.set.length;r++)if(N(this.set[r],e,this.options))return!0;return!1}}e.exports=i;let n=new(t(32170)),o=t(49587),a=t(81272),l=t(75868),u=t(57023),{safeRe:c,t:p,comparatorTrimReplace:f,tildeTrimReplace:h,caretTrimReplace:d}=t(5316),{FLAG_INCLUDE_PRERELEASE:m,FLAG_LOOSE:E}=t(74323),g=e=>"<0.0.0-0"===e.value,y=e=>""===e.value,v=(e,r)=>{let t=!0,s=e.slice(),i=s.pop();for(;t&&s.length;)t=s.every(e=>i.intersects(e,r)),i=s.pop();return t},b=(e,r)=>(l("comp",e,r),l("caret",e=x(e,r)),l("tildes",e=S(e,r)),l("xrange",e=I(e,r)),l("stars",e=j(e,r)),e),w=e=>!e||"x"===e.toLowerCase()||"*"===e,S=(e,r)=>e.trim().split(/\s+/).map(e=>$(e,r)).join(" "),$=(e,r)=>{let t=r.loose?c[p.TILDELOOSE]:c[p.TILDE];return e.replace(t,(r,t,s,i,n)=>{let o;return l("tilde",e,r,t,s,i,n),w(t)?o="":w(s)?o=`>=${t}.0.0 <${+t+1}.0.0-0`:w(i)?o=`>=${t}.${s}.0 <${t}.${+s+1}.0-0`:n?(l("replaceTilde pr",n),o=`>=${t}.${s}.${i}-${n} <${t}.${+s+1}.0-0`):o=`>=${t}.${s}.${i} <${t}.${+s+1}.0-0`,l("tilde return",o),o})},x=(e,r)=>e.trim().split(/\s+/).map(e=>R(e,r)).join(" "),R=(e,r)=>{l("caret",e,r);let t=r.loose?c[p.CARETLOOSE]:c[p.CARET],s=r.includePrerelease?"-0":"";return e.replace(t,(r,t,i,n,o)=>{let a;return l("caret",e,r,t,i,n,o),w(t)?a="":w(i)?a=`>=${t}.0.0${s} <${+t+1}.0.0-0`:w(n)?a="0"===t?`>=${t}.${i}.0${s} <${t}.${+i+1}.0-0`:`>=${t}.${i}.0${s} <${+t+1}.0.0-0`:o?(l("replaceCaret pr",o),a="0"===t?"0"===i?`>=${t}.${i}.${n}-${o} <${t}.${i}.${+n+1}-0`:`>=${t}.${i}.${n}-${o} <${t}.${+i+1}.0-0`:`>=${t}.${i}.${n}-${o} <${+t+1}.0.0-0`):(l("no pr"),a="0"===t?"0"===i?`>=${t}.${i}.${n}${s} <${t}.${i}.${+n+1}-0`:`>=${t}.${i}.${n}${s} <${t}.${+i+1}.0-0`:`>=${t}.${i}.${n} <${+t+1}.0.0-0`),l("caret return",a),a})},I=(e,r)=>(l("replaceXRanges",e,r),e.split(/\s+/).map(e=>A(e,r)).join(" ")),A=(e,r)=>{e=e.trim();let t=r.loose?c[p.XRANGELOOSE]:c[p.XRANGE];return e.replace(t,(t,s,i,n,o,a)=>{l("xRange",e,t,s,i,n,o,a);let u=w(i),c=u||w(n),p=c||w(o);return"="===s&&p&&(s=""),a=r.includePrerelease?"-0":"",u?t=">"===s||"<"===s?"<0.0.0-0":"*":s&&p?(c&&(n=0),o=0,">"===s?(s=">=",c?(i=+i+1,n=0):n=+n+1,o=0):"<="===s&&(s="<",c?i=+i+1:n=+n+1),"<"===s&&(a="-0"),t=`${s+i}.${n}.${o}${a}`):c?t=`>=${i}.0.0${a} <${+i+1}.0.0-0`:p&&(t=`>=${i}.${n}.0${a} <${i}.${+n+1}.0-0`),l("xRange return",t),t})},j=(e,r)=>(l("replaceStars",e,r),e.trim().replace(c[p.STAR],"")),O=(e,r)=>(l("replaceGTE0",e,r),e.trim().replace(c[r.includePrerelease?p.GTE0PRE:p.GTE0],"")),P=e=>(r,t,s,i,n,o,a,l,u,c,p,f)=>(t=w(s)?"":w(i)?`>=${s}.0.0${e?"-0":""}`:w(n)?`>=${s}.${i}.0${e?"-0":""}`:o?`>=${t}`:`>=${t}${e?"-0":""}`,l=w(u)?"":w(c)?`<${+u+1}.0.0-0`:w(p)?`<${u}.${+c+1}.0-0`:f?`<=${u}.${c}.${p}-${f}`:e?`<${u}.${c}.${+p+1}-0`:`<=${l}`,`${t} ${l}`.trim()),N=(e,r,t)=>{for(let t=0;t<e.length;t++)if(!e[t].test(r))return!1;if(r.prerelease.length&&!t.includePrerelease){for(let t=0;t<e.length;t++)if(l(e[t].semver),e[t].semver!==a.ANY&&e[t].semver.prerelease.length>0){let s=e[t].semver;if(s.major===r.major&&s.minor===r.minor&&s.patch===r.patch)return!0}return!1}return!0}},57023:(e,r,t)=>{let s=t(75868),{MAX_LENGTH:i,MAX_SAFE_INTEGER:n}=t(74323),{safeRe:o,safeSrc:a,t:l}=t(5316),u=t(49587),{compareIdentifiers:c}=t(89653);class p{constructor(e,r){if(r=u(r),e instanceof p){if(!!r.loose===e.loose&&!!r.includePrerelease===e.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>i)throw TypeError(`version is longer than ${i} characters`);s("SemVer",e,r),this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease;let t=e.trim().match(r.loose?o[l.LOOSE]:o[l.FULL]);if(!t)throw TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+t[1],this.minor=+t[2],this.patch=+t[3],this.major>n||this.major<0)throw TypeError("Invalid major version");if(this.minor>n||this.minor<0)throw TypeError("Invalid minor version");if(this.patch>n||this.patch<0)throw TypeError("Invalid patch version");t[4]?this.prerelease=t[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){let r=+e;if(r>=0&&r<n)return r}return e}):this.prerelease=[],this.build=t[5]?t[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(s("SemVer.compare",this.version,this.options,e),!(e instanceof p)){if("string"==typeof e&&e===this.version)return 0;e=new p(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof p||(e=new p(e,this.options)),c(this.major,e.major)||c(this.minor,e.minor)||c(this.patch,e.patch)}comparePre(e){if(e instanceof p||(e=new p(e,this.options)),this.prerelease.length&&!e.prerelease.length)return -1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let r=0;do{let t=this.prerelease[r],i=e.prerelease[r];if(s("prerelease compare",r,t,i),void 0===t&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===t)return -1;if(t===i)continue;else return c(t,i)}while(++r)}compareBuild(e){e instanceof p||(e=new p(e,this.options));let r=0;do{let t=this.build[r],i=e.build[r];if(s("build compare",r,t,i),void 0===t&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===t)return -1;if(t===i)continue;else return c(t,i)}while(++r)}inc(e,r,t){if(e.startsWith("pre")){if(!r&&!1===t)throw Error("invalid increment argument: identifier is empty");if(r){let e=RegExp(`^${this.options.loose?a[l.PRERELEASELOOSE]:a[l.PRERELEASE]}$`),t=`-${r}`.match(e);if(!t||t[1]!==r)throw Error(`invalid identifier: ${r}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",r,t);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",r,t);break;case"prepatch":this.prerelease.length=0,this.inc("patch",r,t),this.inc("pre",r,t);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",r,t),this.inc("pre",r,t);break;case"release":if(0===this.prerelease.length)throw Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(0!==this.minor||0!==this.patch||0===this.prerelease.length)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(0!==this.patch||0===this.prerelease.length)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{let e=Number(t)?1:0;if(0===this.prerelease.length)this.prerelease=[e];else{let s=this.prerelease.length;for(;--s>=0;)"number"==typeof this.prerelease[s]&&(this.prerelease[s]++,s=-2);if(-1===s){if(r===this.prerelease.join(".")&&!1===t)throw Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(r){let s=[r,e];!1===t&&(s=[r]),0===c(this.prerelease[0],r)?isNaN(this.prerelease[1])&&(this.prerelease=s):this.prerelease=s}break}default:throw Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=p},78515:(e,r,t)=>{let s=t(80216);e.exports=(e,r)=>{let t=s(e.trim().replace(/^[=v]+/,""),r);return t?t.version:null}},8741:(e,r,t)=>{let s=t(33048),i=t(74695),n=t(4824),o=t(65230),a=t(86113),l=t(35884);e.exports=(e,r,t,u)=>{switch(r){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof t&&(t=t.version),e===t;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof t&&(t=t.version),e!==t;case"":case"=":case"==":return s(e,t,u);case"!=":return i(e,t,u);case">":return n(e,t,u);case">=":return o(e,t,u);case"<":return a(e,t,u);case"<=":return l(e,t,u);default:throw TypeError(`Invalid operator: ${r}`)}}},66032:(e,r,t)=>{let s=t(57023),i=t(80216),{safeRe:n,t:o}=t(5316);e.exports=(e,r)=>{if(e instanceof s)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let t=null;if((r=r||{}).rtl){let s;let i=r.includePrerelease?n[o.COERCERTLFULL]:n[o.COERCERTL];for(;(s=i.exec(e))&&(!t||t.index+t[0].length!==e.length);)t&&s.index+s[0].length===t.index+t[0].length||(t=s),i.lastIndex=s.index+s[1].length+s[2].length;i.lastIndex=-1}else t=e.match(r.includePrerelease?n[o.COERCEFULL]:n[o.COERCE]);if(null===t)return null;let a=t[2],l=t[3]||"0",u=t[4]||"0",c=r.includePrerelease&&t[5]?`-${t[5]}`:"",p=r.includePrerelease&&t[6]?`+${t[6]}`:"";return i(`${a}.${l}.${u}${c}${p}`,r)}},69678:(e,r,t)=>{let s=t(57023);e.exports=(e,r,t)=>{let i=new s(e,t),n=new s(r,t);return i.compare(n)||i.compareBuild(n)}},42820:(e,r,t)=>{let s=t(763);e.exports=(e,r)=>s(e,r,!0)},763:(e,r,t)=>{let s=t(57023);e.exports=(e,r,t)=>new s(e,t).compare(new s(r,t))},63278:(e,r,t)=>{let s=t(80216);e.exports=(e,r)=>{let t=s(e,null,!0),i=s(r,null,!0),n=t.compare(i);if(0===n)return null;let o=n>0,a=o?t:i,l=o?i:t,u=!!a.prerelease.length;if(l.prerelease.length&&!u){if(!l.patch&&!l.minor)return"major";if(0===l.compareMain(a))return l.minor&&!l.patch?"minor":"patch"}let c=u?"pre":"";return t.major!==i.major?c+"major":t.minor!==i.minor?c+"minor":t.patch!==i.patch?c+"patch":"prerelease"}},33048:(e,r,t)=>{let s=t(763);e.exports=(e,r,t)=>0===s(e,r,t)},4824:(e,r,t)=>{let s=t(763);e.exports=(e,r,t)=>s(e,r,t)>0},65230:(e,r,t)=>{let s=t(763);e.exports=(e,r,t)=>s(e,r,t)>=0},70278:(e,r,t)=>{let s=t(57023);e.exports=(e,r,t,i,n)=>{"string"==typeof t&&(n=i,i=t,t=void 0);try{return new s(e instanceof s?e.version:e,t).inc(r,i,n).version}catch(e){return null}}},86113:(e,r,t)=>{let s=t(763);e.exports=(e,r,t)=>0>s(e,r,t)},35884:(e,r,t)=>{let s=t(763);e.exports=(e,r,t)=>0>=s(e,r,t)},8809:(e,r,t)=>{let s=t(57023);e.exports=(e,r)=>new s(e,r).major},83165:(e,r,t)=>{let s=t(57023);e.exports=(e,r)=>new s(e,r).minor},74695:(e,r,t)=>{let s=t(763);e.exports=(e,r,t)=>0!==s(e,r,t)},80216:(e,r,t)=>{let s=t(57023);e.exports=(e,r,t=!1)=>{if(e instanceof s)return e;try{return new s(e,r)}catch(e){if(!t)return null;throw e}}},44968:(e,r,t)=>{let s=t(57023);e.exports=(e,r)=>new s(e,r).patch},87783:(e,r,t)=>{let s=t(80216);e.exports=(e,r)=>{let t=s(e,r);return t&&t.prerelease.length?t.prerelease:null}},58945:(e,r,t)=>{let s=t(763);e.exports=(e,r,t)=>s(r,e,t)},67107:(e,r,t)=>{let s=t(69678);e.exports=(e,r)=>e.sort((e,t)=>s(t,e,r))},62161:(e,r,t)=>{let s=t(15609);e.exports=(e,r,t)=>{try{r=new s(r,t)}catch(e){return!1}return r.test(e)}},85202:(e,r,t)=>{let s=t(69678);e.exports=(e,r)=>e.sort((e,t)=>s(e,t,r))},89128:(e,r,t)=>{let s=t(80216);e.exports=(e,r)=>{let t=s(e,r);return t?t.version:null}},77801:(e,r,t)=>{let s=t(5316),i=t(74323),n=t(57023),o=t(89653),a=t(80216),l=t(89128),u=t(78515),c=t(70278),p=t(63278),f=t(8809),h=t(83165),d=t(44968),m=t(87783),E=t(763),g=t(58945),y=t(42820),v=t(69678),b=t(85202),w=t(67107),S=t(4824),$=t(86113),x=t(33048),R=t(74695),I=t(65230),A=t(35884),j=t(8741),O=t(66032),P=t(81272),N=t(15609),L=t(62161),T=t(99023),C=t(45448),k=t(59830),D=t(23685),M=t(69094),G=t(29470),B=t(63383),U=t(300),F=t(84208),_=t(80062),Z=t(26698);e.exports={parse:a,valid:l,clean:u,inc:c,diff:p,major:f,minor:h,patch:d,prerelease:m,compare:E,rcompare:g,compareLoose:y,compareBuild:v,sort:b,rsort:w,gt:S,lt:$,eq:x,neq:R,gte:I,lte:A,cmp:j,coerce:O,Comparator:P,Range:N,satisfies:L,toComparators:T,maxSatisfying:C,minSatisfying:k,minVersion:D,validRange:M,outside:G,gtr:B,ltr:U,intersects:F,simplifyRange:_,subset:Z,SemVer:n,re:s.re,src:s.src,tokens:s.t,SEMVER_SPEC_VERSION:i.SEMVER_SPEC_VERSION,RELEASE_TYPES:i.RELEASE_TYPES,compareIdentifiers:o.compareIdentifiers,rcompareIdentifiers:o.rcompareIdentifiers}},74323:e=>{let r=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:r,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},75868:e=>{let r="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=r},89653:e=>{let r=/^[0-9]+$/,t=(e,t)=>{let s=r.test(e),i=r.test(t);return s&&i&&(e=+e,t=+t),e===t?0:s&&!i?-1:i&&!s?1:e<t?-1:1};e.exports={compareIdentifiers:t,rcompareIdentifiers:(e,r)=>t(r,e)}},32170:e=>{class r{constructor(){this.max=1e3,this.map=new Map}get(e){let r=this.map.get(e);if(void 0!==r)return this.map.delete(e),this.map.set(e,r),r}delete(e){return this.map.delete(e)}set(e,r){if(!this.delete(e)&&void 0!==r){if(this.map.size>=this.max){let e=this.map.keys().next().value;this.delete(e)}this.map.set(e,r)}return this}}e.exports=r},49587:e=>{let r=Object.freeze({loose:!0}),t=Object.freeze({});e.exports=e=>e?"object"!=typeof e?r:e:t},5316:(e,r,t)=>{let{MAX_SAFE_COMPONENT_LENGTH:s,MAX_SAFE_BUILD_LENGTH:i,MAX_LENGTH:n}=t(74323),o=t(75868),a=(r=e.exports={}).re=[],l=r.safeRe=[],u=r.src=[],c=r.safeSrc=[],p=r.t={},f=0,h="[a-zA-Z0-9-]",d=[["\\s",1],["\\d",n],[h,i]],m=e=>{for(let[r,t]of d)e=e.split(`${r}*`).join(`${r}{0,${t}}`).split(`${r}+`).join(`${r}{1,${t}}`);return e},E=(e,r,t)=>{let s=m(r),i=f++;o(e,i,r),p[e]=i,u[i]=r,c[i]=s,a[i]=new RegExp(r,t?"g":void 0),l[i]=new RegExp(s,t?"g":void 0)};E("NUMERICIDENTIFIER","0|[1-9]\\d*"),E("NUMERICIDENTIFIERLOOSE","\\d+"),E("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${h}*`),E("MAINVERSION",`(${u[p.NUMERICIDENTIFIER]})\\.(${u[p.NUMERICIDENTIFIER]})\\.(${u[p.NUMERICIDENTIFIER]})`),E("MAINVERSIONLOOSE",`(${u[p.NUMERICIDENTIFIERLOOSE]})\\.(${u[p.NUMERICIDENTIFIERLOOSE]})\\.(${u[p.NUMERICIDENTIFIERLOOSE]})`),E("PRERELEASEIDENTIFIER",`(?:${u[p.NUMERICIDENTIFIER]}|${u[p.NONNUMERICIDENTIFIER]})`),E("PRERELEASEIDENTIFIERLOOSE",`(?:${u[p.NUMERICIDENTIFIERLOOSE]}|${u[p.NONNUMERICIDENTIFIER]})`),E("PRERELEASE",`(?:-(${u[p.PRERELEASEIDENTIFIER]}(?:\\.${u[p.PRERELEASEIDENTIFIER]})*))`),E("PRERELEASELOOSE",`(?:-?(${u[p.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${u[p.PRERELEASEIDENTIFIERLOOSE]})*))`),E("BUILDIDENTIFIER",`${h}+`),E("BUILD",`(?:\\+(${u[p.BUILDIDENTIFIER]}(?:\\.${u[p.BUILDIDENTIFIER]})*))`),E("FULLPLAIN",`v?${u[p.MAINVERSION]}${u[p.PRERELEASE]}?${u[p.BUILD]}?`),E("FULL",`^${u[p.FULLPLAIN]}$`),E("LOOSEPLAIN",`[v=\\s]*${u[p.MAINVERSIONLOOSE]}${u[p.PRERELEASELOOSE]}?${u[p.BUILD]}?`),E("LOOSE",`^${u[p.LOOSEPLAIN]}$`),E("GTLT","((?:<|>)?=?)"),E("XRANGEIDENTIFIERLOOSE",`${u[p.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),E("XRANGEIDENTIFIER",`${u[p.NUMERICIDENTIFIER]}|x|X|\\*`),E("XRANGEPLAIN",`[v=\\s]*(${u[p.XRANGEIDENTIFIER]})(?:\\.(${u[p.XRANGEIDENTIFIER]})(?:\\.(${u[p.XRANGEIDENTIFIER]})(?:${u[p.PRERELEASE]})?${u[p.BUILD]}?)?)?`),E("XRANGEPLAINLOOSE",`[v=\\s]*(${u[p.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[p.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[p.XRANGEIDENTIFIERLOOSE]})(?:${u[p.PRERELEASELOOSE]})?${u[p.BUILD]}?)?)?`),E("XRANGE",`^${u[p.GTLT]}\\s*${u[p.XRANGEPLAIN]}$`),E("XRANGELOOSE",`^${u[p.GTLT]}\\s*${u[p.XRANGEPLAINLOOSE]}$`),E("COERCEPLAIN",`(^|[^\\d])(\\d{1,${s}})(?:\\.(\\d{1,${s}}))?(?:\\.(\\d{1,${s}}))?`),E("COERCE",`${u[p.COERCEPLAIN]}(?:$|[^\\d])`),E("COERCEFULL",u[p.COERCEPLAIN]+`(?:${u[p.PRERELEASE]})?`+`(?:${u[p.BUILD]})?`+"(?:$|[^\\d])"),E("COERCERTL",u[p.COERCE],!0),E("COERCERTLFULL",u[p.COERCEFULL],!0),E("LONETILDE","(?:~>?)"),E("TILDETRIM",`(\\s*)${u[p.LONETILDE]}\\s+`,!0),r.tildeTrimReplace="$1~",E("TILDE",`^${u[p.LONETILDE]}${u[p.XRANGEPLAIN]}$`),E("TILDELOOSE",`^${u[p.LONETILDE]}${u[p.XRANGEPLAINLOOSE]}$`),E("LONECARET","(?:\\^)"),E("CARETTRIM",`(\\s*)${u[p.LONECARET]}\\s+`,!0),r.caretTrimReplace="$1^",E("CARET",`^${u[p.LONECARET]}${u[p.XRANGEPLAIN]}$`),E("CARETLOOSE",`^${u[p.LONECARET]}${u[p.XRANGEPLAINLOOSE]}$`),E("COMPARATORLOOSE",`^${u[p.GTLT]}\\s*(${u[p.LOOSEPLAIN]})$|^$`),E("COMPARATOR",`^${u[p.GTLT]}\\s*(${u[p.FULLPLAIN]})$|^$`),E("COMPARATORTRIM",`(\\s*)${u[p.GTLT]}\\s*(${u[p.LOOSEPLAIN]}|${u[p.XRANGEPLAIN]})`,!0),r.comparatorTrimReplace="$1$2$3",E("HYPHENRANGE",`^\\s*(${u[p.XRANGEPLAIN]})\\s+-\\s+(${u[p.XRANGEPLAIN]})\\s*$`),E("HYPHENRANGELOOSE",`^\\s*(${u[p.XRANGEPLAINLOOSE]})\\s+-\\s+(${u[p.XRANGEPLAINLOOSE]})\\s*$`),E("STAR","(<|>)?=?\\s*\\*"),E("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),E("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},63383:(e,r,t)=>{let s=t(29470);e.exports=(e,r,t)=>s(e,r,">",t)},84208:(e,r,t)=>{let s=t(15609);e.exports=(e,r,t)=>(e=new s(e,t),r=new s(r,t),e.intersects(r,t))},300:(e,r,t)=>{let s=t(29470);e.exports=(e,r,t)=>s(e,r,"<",t)},45448:(e,r,t)=>{let s=t(57023),i=t(15609);e.exports=(e,r,t)=>{let n=null,o=null,a=null;try{a=new i(r,t)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!n||-1===o.compare(e))&&(o=new s(n=e,t))}),n}},59830:(e,r,t)=>{let s=t(57023),i=t(15609);e.exports=(e,r,t)=>{let n=null,o=null,a=null;try{a=new i(r,t)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!n||1===o.compare(e))&&(o=new s(n=e,t))}),n}},23685:(e,r,t)=>{let s=t(57023),i=t(15609),n=t(4824);e.exports=(e,r)=>{e=new i(e,r);let t=new s("0.0.0");if(e.test(t)||(t=new s("0.0.0-0"),e.test(t)))return t;t=null;for(let r=0;r<e.set.length;++r){let i=e.set[r],o=null;i.forEach(e=>{let r=new s(e.semver.version);switch(e.operator){case">":0===r.prerelease.length?r.patch++:r.prerelease.push(0),r.raw=r.format();case"":case">=":(!o||n(r,o))&&(o=r);break;case"<":case"<=":break;default:throw Error(`Unexpected operation: ${e.operator}`)}}),o&&(!t||n(t,o))&&(t=o)}return t&&e.test(t)?t:null}},29470:(e,r,t)=>{let s=t(57023),i=t(81272),{ANY:n}=i,o=t(15609),a=t(62161),l=t(4824),u=t(86113),c=t(35884),p=t(65230);e.exports=(e,r,t,f)=>{let h,d,m,E,g;switch(e=new s(e,f),r=new o(r,f),t){case">":h=l,d=c,m=u,E=">",g=">=";break;case"<":h=u,d=p,m=l,E="<",g="<=";break;default:throw TypeError('Must provide a hilo val of "<" or ">"')}if(a(e,r,f))return!1;for(let t=0;t<r.set.length;++t){let s=r.set[t],o=null,a=null;if(s.forEach(e=>{e.semver===n&&(e=new i(">=0.0.0")),o=o||e,a=a||e,h(e.semver,o.semver,f)?o=e:m(e.semver,a.semver,f)&&(a=e)}),o.operator===E||o.operator===g||(!a.operator||a.operator===E)&&d(e,a.semver)||a.operator===g&&m(e,a.semver))return!1}return!0}},80062:(e,r,t)=>{let s=t(62161),i=t(763);e.exports=(e,r,t)=>{let n=[],o=null,a=null,l=e.sort((e,r)=>i(e,r,t));for(let e of l)s(e,r,t)?(a=e,o||(o=e)):(a&&n.push([o,a]),a=null,o=null);o&&n.push([o,null]);let u=[];for(let[e,r]of n)e===r?u.push(e):r||e!==l[0]?r?e===l[0]?u.push(`<=${r}`):u.push(`${e} - ${r}`):u.push(`>=${e}`):u.push("*");let c=u.join(" || "),p="string"==typeof r.raw?r.raw:String(r);return c.length<p.length?c:r}},26698:(e,r,t)=>{let s=t(15609),i=t(81272),{ANY:n}=i,o=t(62161),a=t(763),l=[new i(">=0.0.0-0")],u=[new i(">=0.0.0")],c=(e,r,t)=>{let s,i,c,h,d,m,E;if(e===r)return!0;if(1===e.length&&e[0].semver===n){if(1===r.length&&r[0].semver===n)return!0;e=t.includePrerelease?l:u}if(1===r.length&&r[0].semver===n){if(t.includePrerelease)return!0;r=u}let g=new Set;for(let r of e)">"===r.operator||">="===r.operator?s=p(s,r,t):"<"===r.operator||"<="===r.operator?i=f(i,r,t):g.add(r.semver);if(g.size>1||s&&i&&((c=a(s.semver,i.semver,t))>0||0===c&&(">="!==s.operator||"<="!==i.operator)))return null;for(let e of g){if(s&&!o(e,String(s),t)||i&&!o(e,String(i),t))return null;for(let s of r)if(!o(e,String(s),t))return!1;return!0}let y=!!i&&!t.includePrerelease&&!!i.semver.prerelease.length&&i.semver,v=!!s&&!t.includePrerelease&&!!s.semver.prerelease.length&&s.semver;for(let e of(y&&1===y.prerelease.length&&"<"===i.operator&&0===y.prerelease[0]&&(y=!1),r)){if(E=E||">"===e.operator||">="===e.operator,m=m||"<"===e.operator||"<="===e.operator,s){if(v&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===v.major&&e.semver.minor===v.minor&&e.semver.patch===v.patch&&(v=!1),">"===e.operator||">="===e.operator){if((h=p(s,e,t))===e&&h!==s)return!1}else if(">="===s.operator&&!o(s.semver,String(e),t))return!1}if(i){if(y&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===y.major&&e.semver.minor===y.minor&&e.semver.patch===y.patch&&(y=!1),"<"===e.operator||"<="===e.operator){if((d=f(i,e,t))===e&&d!==i)return!1}else if("<="===i.operator&&!o(i.semver,String(e),t))return!1}if(!e.operator&&(i||s)&&0!==c)return!1}return(!s||!m||!!i||0===c)&&(!i||!E||!!s||0===c)&&!v&&!y},p=(e,r,t)=>{if(!e)return r;let s=a(e.semver,r.semver,t);return s>0?e:s<0?r:">"===r.operator&&">="===e.operator?r:e},f=(e,r,t)=>{if(!e)return r;let s=a(e.semver,r.semver,t);return s<0?e:s>0?r:"<"===r.operator&&"<="===e.operator?r:e};e.exports=(e,r,t={})=>{if(e===r)return!0;e=new s(e,t),r=new s(r,t);let i=!1;e:for(let s of e.set){for(let e of r.set){let r=c(s,e,t);if(i=i||null!==r,r)continue e}if(i)return!1}return!0}},99023:(e,r,t)=>{let s=t(15609);e.exports=(e,r)=>new s(e,r).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))},69094:(e,r,t)=>{let s=t(15609);e.exports=(e,r)=>{try{return new s(e,r).range||"*"}catch(e){return null}}},85917:(e,r,t)=>{let s=t(59477),i=t(20499),n=t(40451),o=t(8287),a=t(48905),l=t(69006),u=t(47139),c=t(32904),p=t(8247),f=t(82743),h=t(27599),{KeyObject:d,createSecretKey:m,createPrivateKey:E}=t(84770),g=["RS256","RS384","RS512","ES256","ES384","ES512","HS256","HS384","HS512","none"];i&&g.splice(3,0,"PS256","PS384","PS512");let y={expiresIn:{isValid:function(e){return u(e)||f(e)&&e},message:'"expiresIn" should be a number of seconds or string representing a timespan'},notBefore:{isValid:function(e){return u(e)||f(e)&&e},message:'"notBefore" should be a number of seconds or string representing a timespan'},audience:{isValid:function(e){return f(e)||Array.isArray(e)},message:'"audience" must be a string or array'},algorithm:{isValid:a.bind(null,g),message:'"algorithm" must be a valid string enum value'},header:{isValid:p,message:'"header" must be an object'},encoding:{isValid:f,message:'"encoding" must be a string'},issuer:{isValid:f,message:'"issuer" must be a string'},subject:{isValid:f,message:'"subject" must be a string'},jwtid:{isValid:f,message:'"jwtid" must be a string'},noTimestamp:{isValid:l,message:'"noTimestamp" must be a boolean'},keyid:{isValid:f,message:'"keyid" must be a string'},mutatePayload:{isValid:l,message:'"mutatePayload" must be a boolean'},allowInsecureKeySizes:{isValid:l,message:'"allowInsecureKeySizes" must be a boolean'},allowInvalidAsymmetricKeyTypes:{isValid:l,message:'"allowInvalidAsymmetricKeyTypes" must be a boolean'}},v={iat:{isValid:c,message:'"iat" should be a number of seconds'},exp:{isValid:c,message:'"exp" should be a number of seconds'},nbf:{isValid:c,message:'"nbf" should be a number of seconds'}};function b(e,r,t,s){if(!p(t))throw Error('Expected "'+s+'" to be a plain object.');Object.keys(t).forEach(function(i){let n=e[i];if(!n){if(!r)throw Error('"'+i+'" is not allowed in "'+s+'"');return}if(!n.isValid(t[i]))throw Error(n.message)})}let w={audience:"aud",issuer:"iss",subject:"sub",jwtid:"jti"},S=["expiresIn","notBefore","noTimestamp","audience","issuer","subject","jwtid"];e.exports=function(e,r,t,i){var a,l;"function"==typeof t?(i=t,t={}):t=t||{};let u="object"==typeof e&&!Buffer.isBuffer(e),c=Object.assign({alg:t.algorithm||"HS256",typ:u?"JWT":void 0,kid:t.keyid},t.header);function p(e){if(i)return i(e);throw e}if(!r&&"none"!==t.algorithm)return p(Error("secretOrPrivateKey must have a value"));if(null!=r&&!(r instanceof d))try{r=E(r)}catch(e){try{r=m("string"==typeof r?Buffer.from(r):r)}catch(e){return p(Error("secretOrPrivateKey is not valid key material"))}}if(c.alg.startsWith("HS")&&"secret"!==r.type)return p(Error(`secretOrPrivateKey must be a symmetric key when using ${c.alg}`));if(/^(?:RS|PS|ES)/.test(c.alg)){if("private"!==r.type)return p(Error(`secretOrPrivateKey must be an asymmetric key when using ${c.alg}`));if(!t.allowInsecureKeySizes&&!c.alg.startsWith("ES")&&void 0!==r.asymmetricKeyDetails&&r.asymmetricKeyDetails.modulusLength<2048)return p(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${c.alg}`))}if(void 0===e)return p(Error("payload is required"));if(u){try{a=e,b(v,!0,a,"payload")}catch(e){return p(e)}t.mutatePayload||(e=Object.assign({},e))}else{let r=S.filter(function(e){return void 0!==t[e]});if(r.length>0)return p(Error("invalid "+r.join(",")+" option for "+typeof e+" payload"))}if(void 0!==e.exp&&void 0!==t.expiresIn)return p(Error('Bad "options.expiresIn" option the payload already has an "exp" property.'));if(void 0!==e.nbf&&void 0!==t.notBefore)return p(Error('Bad "options.notBefore" option the payload already has an "nbf" property.'));try{l=t,b(y,!1,l,"options")}catch(e){return p(e)}if(!t.allowInvalidAsymmetricKeyTypes)try{n(c.alg,r)}catch(e){return p(e)}let f=e.iat||Math.floor(Date.now()/1e3);if(t.noTimestamp?delete e.iat:u&&(e.iat=f),void 0!==t.notBefore){try{e.nbf=s(t.notBefore,f)}catch(e){return p(e)}if(void 0===e.nbf)return p(Error('"notBefore" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}if(void 0!==t.expiresIn&&"object"==typeof e){try{e.exp=s(t.expiresIn,f)}catch(e){return p(e)}if(void 0===e.exp)return p(Error('"expiresIn" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}Object.keys(w).forEach(function(r){let s=w[r];if(void 0!==t[r]){if(void 0!==e[s])return p(Error('Bad "options.'+r+'" option. The payload already has an "'+s+'" property.'));e[s]=t[r]}});let g=t.encoding||"utf8";if("function"==typeof i)i=i&&h(i),o.createSign({header:c,privateKey:r,payload:e,encoding:g}).once("error",i).once("done",function(e){if(!t.allowInsecureKeySizes&&/^(?:RS|PS)/.test(c.alg)&&e.length<256)return i(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${c.alg}`));i(null,e)});else{let s=o.sign({header:c,payload:e,secret:r,encoding:g});if(!t.allowInsecureKeySizes&&/^(?:RS|PS)/.test(c.alg)&&s.length<256)throw Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${c.alg}`);return s}}},17910:(e,r,t)=>{let s=t(69062),i=t(13020),n=t(82504),o=t(85911),a=t(59477),l=t(40451),u=t(20499),c=t(8287),{KeyObject:p,createSecretKey:f,createPublicKey:h}=t(84770),d=["RS256","RS384","RS512"],m=["ES256","ES384","ES512"],E=["RS256","RS384","RS512"],g=["HS256","HS384","HS512"];u&&(d.splice(d.length,0,"PS256","PS384","PS512"),E.splice(E.length,0,"PS256","PS384","PS512")),e.exports=function(e,r,t,u){let y,v,b;if("function"!=typeof t||u||(u=t,t={}),t||(t={}),t=Object.assign({},t),y=u||function(e,r){if(e)throw e;return r},t.clockTimestamp&&"number"!=typeof t.clockTimestamp)return y(new s("clockTimestamp must be a number"));if(void 0!==t.nonce&&("string"!=typeof t.nonce||""===t.nonce.trim()))return y(new s("nonce must be a non-empty string"));if(void 0!==t.allowInvalidAsymmetricKeyTypes&&"boolean"!=typeof t.allowInvalidAsymmetricKeyTypes)return y(new s("allowInvalidAsymmetricKeyTypes must be a boolean"));let w=t.clockTimestamp||Math.floor(Date.now()/1e3);if(!e)return y(new s("jwt must be provided"));if("string"!=typeof e)return y(new s("jwt must be a string"));let S=e.split(".");if(3!==S.length)return y(new s("jwt malformed"));try{v=o(e,{complete:!0})}catch(e){return y(e)}if(!v)return y(new s("invalid token"));let $=v.header;if("function"==typeof r){if(!u)return y(new s("verify must be called asynchronous if secret or public key is provided as a callback"));b=r}else b=function(e,t){return t(null,r)};return b($,function(r,o){let u;if(r)return y(new s("error in secret or public key callback: "+r.message));let b=""!==S[2].trim();if(!b&&o)return y(new s("jwt signature is required"));if(b&&!o)return y(new s("secret or public key must be provided"));if(!b&&!t.algorithms)return y(new s('please specify "none" in "algorithms" to verify unsigned tokens'));if(null!=o&&!(o instanceof p))try{o=h(o)}catch(e){try{o=f("string"==typeof o?Buffer.from(o):o)}catch(e){return y(new s("secretOrPublicKey is not valid key material"))}}if(t.algorithms||("secret"===o.type?t.algorithms=g:["rsa","rsa-pss"].includes(o.asymmetricKeyType)?t.algorithms=E:"ec"===o.asymmetricKeyType?t.algorithms=m:t.algorithms=d),-1===t.algorithms.indexOf(v.header.alg))return y(new s("invalid algorithm"));if($.alg.startsWith("HS")&&"secret"!==o.type)return y(new s(`secretOrPublicKey must be a symmetric key when using ${$.alg}`));if(/^(?:RS|PS|ES)/.test($.alg)&&"public"!==o.type)return y(new s(`secretOrPublicKey must be an asymmetric key when using ${$.alg}`));if(!t.allowInvalidAsymmetricKeyTypes)try{l($.alg,o)}catch(e){return y(e)}try{u=c.verify(e,v.header.alg,o)}catch(e){return y(e)}if(!u)return y(new s("invalid signature"));let x=v.payload;if(void 0!==x.nbf&&!t.ignoreNotBefore){if("number"!=typeof x.nbf)return y(new s("invalid nbf value"));if(x.nbf>w+(t.clockTolerance||0))return y(new i("jwt not active",new Date(1e3*x.nbf)))}if(void 0!==x.exp&&!t.ignoreExpiration){if("number"!=typeof x.exp)return y(new s("invalid exp value"));if(w>=x.exp+(t.clockTolerance||0))return y(new n("jwt expired",new Date(1e3*x.exp)))}if(t.audience){let e=Array.isArray(t.audience)?t.audience:[t.audience];if(!(Array.isArray(x.aud)?x.aud:[x.aud]).some(function(r){return e.some(function(e){return e instanceof RegExp?e.test(r):e===r})}))return y(new s("jwt audience invalid. expected: "+e.join(" or ")))}if(t.issuer&&("string"==typeof t.issuer&&x.iss!==t.issuer||Array.isArray(t.issuer)&&-1===t.issuer.indexOf(x.iss)))return y(new s("jwt issuer invalid. expected: "+t.issuer));if(t.subject&&x.sub!==t.subject)return y(new s("jwt subject invalid. expected: "+t.subject));if(t.jwtid&&x.jti!==t.jwtid)return y(new s("jwt jwtid invalid. expected: "+t.jwtid));if(t.nonce&&x.nonce!==t.nonce)return y(new s("jwt nonce invalid. expected: "+t.nonce));if(t.maxAge){if("number"!=typeof x.iat)return y(new s("iat required when maxAge is specified"));let e=a(t.maxAge,x.iat);if(void 0===e)return y(new s('"maxAge" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'));if(w>=e+(t.clockTolerance||0))return y(new n("maxAge exceeded",new Date(1e3*e)))}return!0===t.complete?y(null,{header:$,payload:x,signature:v.signature}):y(null,x)})}},95350:(e,r,t)=>{var s=t(29916),i=t(90892).Buffer,n=t(84770),o=t(3164),a=t(21764),l="secret must be a string or buffer",u="key must be a string or a buffer",c="function"==typeof n.createPublicKey;function p(e){if(!i.isBuffer(e)&&"string"!=typeof e&&(!c||"object"!=typeof e||"string"!=typeof e.type||"string"!=typeof e.asymmetricKeyType||"function"!=typeof e.export))throw m(u)}function f(e){if(!i.isBuffer(e)&&"string"!=typeof e&&"object"!=typeof e)throw m("key must be a string, a buffer or an object")}function h(e){return e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function d(e){var r=4-(e=e.toString()).length%4;if(4!==r)for(var t=0;t<r;++t)e+="=";return e.replace(/\-/g,"+").replace(/_/g,"/")}function m(e){var r=[].slice.call(arguments,1);return TypeError(a.format.bind(a,e).apply(null,r))}function E(e){var r;return r=e,i.isBuffer(r)||"string"==typeof r||(e=JSON.stringify(e)),e}function g(e){return function(r,t){(function(e){if(!i.isBuffer(e)&&"string"!=typeof e&&(!c||"object"!=typeof e||"secret"!==e.type||"function"!=typeof e.export))throw m(l)})(t),r=E(r);var s=n.createHmac("sha"+e,t);return h((s.update(r),s.digest("base64")))}}function y(e){return function(r,t,n){var o=g(e)(r,n);return s(i.from(t),i.from(o))}}function v(e){return function(r,t){f(t),r=E(r);var s=n.createSign("RSA-SHA"+e);return h((s.update(r),s.sign(t,"base64")))}}function b(e){return function(r,t,s){p(s),r=E(r),t=d(t);var i=n.createVerify("RSA-SHA"+e);return i.update(r),i.verify(s,t,"base64")}}function w(e){return function(r,t){f(t),r=E(r);var s=n.createSign("RSA-SHA"+e);return h((s.update(r),s.sign({key:t,padding:n.constants.RSA_PKCS1_PSS_PADDING,saltLength:n.constants.RSA_PSS_SALTLEN_DIGEST},"base64")))}}function S(e){return function(r,t,s){p(s),r=E(r),t=d(t);var i=n.createVerify("RSA-SHA"+e);return i.update(r),i.verify({key:s,padding:n.constants.RSA_PKCS1_PSS_PADDING,saltLength:n.constants.RSA_PSS_SALTLEN_DIGEST},t,"base64")}}function $(e){var r=v(e);return function(){var t=r.apply(null,arguments);return o.derToJose(t,"ES"+e)}}function x(e){var r=b(e);return function(t,s,i){return r(t,s=o.joseToDer(s,"ES"+e).toString("base64"),i)}}function R(){return function(){return""}}function I(){return function(e,r){return""===r}}c&&(u+=" or a KeyObject",l+="or a KeyObject"),e.exports=function(e){var r=e.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/i);if(!r)throw m('"%s" is not a valid algorithm.\n  Supported algorithms are:\n  "HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "PS256", "PS384", "PS512", "ES256", "ES384", "ES512" and "none".',e);var t=(r[1]||r[3]).toLowerCase(),s=r[2];return{sign:({hs:g,rs:v,ps:w,es:$,none:R})[t](s),verify:({hs:y,rs:b,ps:S,es:x,none:I})[t](s)}}},8287:(e,r,t)=>{var s=t(99505),i=t(55850);r.ALGORITHMS=["HS256","HS384","HS512","RS256","RS384","RS512","PS256","PS384","PS512","ES256","ES384","ES512"],r.sign=s.sign,r.verify=i.verify,r.decode=i.decode,r.isValid=i.isValid,r.createSign=function(e){return new s(e)},r.createVerify=function(e){return new i(e)}},34630:(e,r,t)=>{var s=t(90892).Buffer,i=t(76162);function n(e){if(this.buffer=null,this.writable=!0,this.readable=!0,!e)return this.buffer=s.alloc(0),this;if("function"==typeof e.pipe)return this.buffer=s.alloc(0),e.pipe(this),this;if(e.length||"object"==typeof e)return this.buffer=e,this.writable=!1,process.nextTick((function(){this.emit("end",e),this.readable=!1,this.emit("close")}).bind(this)),this;throw TypeError("Unexpected data type ("+typeof e+")")}t(21764).inherits(n,i),n.prototype.write=function(e){this.buffer=s.concat([this.buffer,s.from(e)]),this.emit("data",e)},n.prototype.end=function(e){e&&this.write(e),this.emit("end",e),this.emit("close"),this.writable=!1,this.readable=!1},e.exports=n},99505:(e,r,t)=>{var s=t(90892).Buffer,i=t(34630),n=t(95350),o=t(76162),a=t(60904),l=t(21764);function u(e,r){return s.from(e,r).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function c(e){var r,t,s,i=e.header,o=e.payload,c=e.secret||e.privateKey,p=e.encoding,f=n(i.alg),h=(r=(r=p)||"utf8",t=u(a(i),"binary"),s=u(a(o),r),l.format("%s.%s",t,s)),d=f.sign(h,c);return l.format("%s.%s",h,d)}function p(e){var r=new i(e.secret||e.privateKey||e.key);this.readable=!0,this.header=e.header,this.encoding=e.encoding,this.secret=this.privateKey=this.key=r,this.payload=new i(e.payload),this.secret.once("close",(function(){!this.payload.writable&&this.readable&&this.sign()}).bind(this)),this.payload.once("close",(function(){!this.secret.writable&&this.readable&&this.sign()}).bind(this))}l.inherits(p,o),p.prototype.sign=function(){try{var e=c({header:this.header,payload:this.payload.buffer,secret:this.secret.buffer,encoding:this.encoding});return this.emit("done",e),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},p.sign=c,e.exports=p},60904:(e,r,t)=>{var s=t(78893).Buffer;e.exports=function(e){return"string"==typeof e?e:"number"==typeof e||s.isBuffer(e)?e.toString():JSON.stringify(e)}},55850:(e,r,t)=>{var s=t(90892).Buffer,i=t(34630),n=t(95350),o=t(76162),a=t(60904),l=t(21764),u=/^[a-zA-Z0-9\-_]+?\.[a-zA-Z0-9\-_]+?\.([a-zA-Z0-9\-_]+)?$/;function c(e){var r=e.split(".",1)[0];return function(e){if("[object Object]"===Object.prototype.toString.call(e))return e;try{return JSON.parse(e)}catch(e){return}}(s.from(r,"base64").toString("binary"))}function p(e){return e.split(".")[2]}function f(e){return u.test(e)&&!!c(e)}function h(e,r,t){if(!r){var s=Error("Missing algorithm parameter for jws.verify");throw s.code="MISSING_ALGORITHM",s}var i=p(e=a(e)),o=e.split(".",2).join(".");return n(r).verify(o,i,t)}function d(e,r){if(r=r||{},!f(e=a(e)))return null;var t,i,n=c(e);if(!n)return null;var o=(t=t||"utf8",i=e.split(".")[1],s.from(i,"base64").toString(t));return("JWT"===n.typ||r.json)&&(o=JSON.parse(o,r.encoding)),{header:n,payload:o,signature:p(e)}}function m(e){var r=new i((e=e||{}).secret||e.publicKey||e.key);this.readable=!0,this.algorithm=e.algorithm,this.encoding=e.encoding,this.secret=this.publicKey=this.key=r,this.signature=new i(e.signature),this.secret.once("close",(function(){!this.signature.writable&&this.readable&&this.verify()}).bind(this)),this.signature.once("close",(function(){!this.secret.writable&&this.readable&&this.verify()}).bind(this))}l.inherits(m,o),m.prototype.verify=function(){try{var e=h(this.signature.buffer,this.algorithm,this.key.buffer),r=d(this.signature.buffer,this.encoding);return this.emit("done",e,r),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},m.decode=d,m.isValid=f,m.verify=h,e.exports=m},48905:e=>{var r,t,s=1/0,i=0/0,n=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,l=/^0o[0-7]+$/i,u=/^(?:0|[1-9]\d*)$/,c=parseInt;function p(e){return e!=e}var f=Object.prototype,h=f.hasOwnProperty,d=f.toString,m=f.propertyIsEnumerable,E=(r=Object.keys,t=Object,function(e){return r(t(e))}),g=Math.max,y=Array.isArray;function v(e){var r,t;return null!=e&&"number"==typeof(r=e.length)&&r>-1&&r%1==0&&r<=9007199254740991&&!("[object Function]"==(t=b(e)?d.call(e):"")||"[object GeneratorFunction]"==t)}function b(e){var r=typeof e;return!!e&&("object"==r||"function"==r)}function w(e){return!!e&&"object"==typeof e}e.exports=function(e,r,t,S){e=v(e)?e:($=e)?function(e,r){for(var t=-1,s=e?e.length:0,i=Array(s);++t<s;)i[t]=r(e[t],t,e);return i}(v($)?function(e,r){var t,s=y(e)||w(e)&&v(e)&&h.call(e,"callee")&&(!m.call(e,"callee")||"[object Arguments]"==d.call(e))?function(e,r){for(var t=-1,s=Array(e);++t<e;)s[t]=r(t);return s}(e.length,String):[],i=s.length,n=!!i;for(var o in e)h.call(e,o)&&!(n&&("length"==o||(t=null==(t=i)?9007199254740991:t)&&("number"==typeof o||u.test(o))&&o>-1&&o%1==0&&o<t))&&s.push(o);return s}($):function(e){if(r=e&&e.constructor,e!==("function"==typeof r&&r.prototype||f))return E(e);var r,t=[];for(var s in Object(e))h.call(e,s)&&"constructor"!=s&&t.push(s);return t}($),function(e){return $[e]}):[],t=t&&!S?(I=(R=(x=t)?(x=function(e){if("number"==typeof e)return e;if("symbol"==typeof(r=e)||w(r)&&"[object Symbol]"==d.call(r))return i;if(b(e)){var r,t="function"==typeof e.valueOf?e.valueOf():e;e=b(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(n,"");var s=a.test(e);return s||l.test(e)?c(e.slice(2),s?2:8):o.test(e)?i:+e}(x))===s||x===-s?(x<0?-1:1)*17976931348623157e292:x==x?x:0:0===x?x:0)%1,R==R?I?R-I:R:0):0;var $,x,R,I,A,j=e.length;return t<0&&(t=g(j+t,0)),"string"==typeof(A=e)||!y(A)&&w(A)&&"[object String]"==d.call(A)?t<=j&&e.indexOf(r,t)>-1:!!j&&function(e,r,t){if(r!=r)return function(e,r,t,s){for(var i=e.length,n=t+-1;++n<i;)if(r(e[n],n,e))return n;return -1}(e,p,t);for(var s=t-1,i=e.length;++s<i;)if(e[s]===r)return s;return -1}(e,r,t)>-1}},69006:e=>{var r=Object.prototype.toString;e.exports=function(e){return!0===e||!1===e||!!e&&"object"==typeof e&&"[object Boolean]"==r.call(e)}},47139:e=>{var r=1/0,t=0/0,s=/^\s+|\s+$/g,i=/^[-+]0x[0-9a-f]+$/i,n=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function u(e){var r=typeof e;return!!e&&("object"==r||"function"==r)}e.exports=function(e){var c,p,f;return"number"==typeof e&&e==(f=(p=(c=e)?(c=function(e){if("number"==typeof e)return e;if("symbol"==typeof(r=e)||r&&"object"==typeof r&&"[object Symbol]"==l.call(r))return t;if(u(e)){var r,c="function"==typeof e.valueOf?e.valueOf():e;e=u(c)?c+"":c}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(s,"");var p=n.test(e);return p||o.test(e)?a(e.slice(2),p?2:8):i.test(e)?t:+e}(c))===r||c===-r?(c<0?-1:1)*17976931348623157e292:c==c?c:0:0===c?c:0)%1,p==p?f?p-f:p:0)}},32904:e=>{var r=Object.prototype.toString;e.exports=function(e){return"number"==typeof e||!!e&&"object"==typeof e&&"[object Number]"==r.call(e)}},8247:e=>{var r,t,s=Object.prototype,i=Function.prototype.toString,n=s.hasOwnProperty,o=i.call(Object),a=s.toString,l=(r=Object.getPrototypeOf,t=Object,function(e){return r(t(e))});e.exports=function(e){if(!(e&&"object"==typeof e)||"[object Object]"!=a.call(e)||function(e){var r=!1;if(null!=e&&"function"!=typeof e.toString)try{r=!!(e+"")}catch(e){}return r}(e))return!1;var r=l(e);if(null===r)return!0;var t=n.call(r,"constructor")&&r.constructor;return"function"==typeof t&&t instanceof t&&i.call(t)==o}},82743:e=>{var r=Object.prototype.toString,t=Array.isArray;e.exports=function(e){var s;return"string"==typeof e||!t(e)&&!!(s=e)&&"object"==typeof s&&"[object String]"==r.call(e)}},27599:e=>{var r=1/0,t=0/0,s=/^\s+|\s+$/g,i=/^[-+]0x[0-9a-f]+$/i,n=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function u(e){var r=typeof e;return!!e&&("object"==r||"function"==r)}e.exports=function(e){return function(e,c){var p,f,h,d;if("function"!=typeof c)throw TypeError("Expected a function");return d=(h=(f=e)?(f=function(e){if("number"==typeof e)return e;if("symbol"==typeof(r=e)||r&&"object"==typeof r&&"[object Symbol]"==l.call(r))return t;if(u(e)){var r,c="function"==typeof e.valueOf?e.valueOf():e;e=u(c)?c+"":c}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(s,"");var p=n.test(e);return p||o.test(e)?a(e.slice(2),p?2:8):i.test(e)?t:+e}(f))===r||f===-r?(f<0?-1:1)*17976931348623157e292:f==f?f:0:0===f?f:0)%1,e=h==h?d?h-d:h:0,function(){return--e>0&&(p=c.apply(this,arguments)),e<=1&&(c=void 0),p}}(2,e)}},90892:(e,r,t)=>{var s=t(78893),i=s.Buffer;function n(e,r){for(var t in e)r[t]=e[t]}function o(e,r,t){return i(e,r,t)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=s:(n(s,r),r.Buffer=o),o.prototype=Object.create(i.prototype),n(i,o),o.from=function(e,r,t){if("number"==typeof e)throw TypeError("Argument must not be a number");return i(e,r,t)},o.alloc=function(e,r,t){if("number"!=typeof e)throw TypeError("Argument must be a number");var s=i(e);return void 0!==r?"string"==typeof t?s.fill(r,t):s.fill(r):s.fill(0),s},o.allocUnsafe=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return i(e)},o.allocUnsafeSlow=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return s.SlowBuffer(e)}},30677:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>D});var s,i=t(10326),n=t(17577),o=t(98327),a=t.n(o),l=t(52210),u=t(95746);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var s in t)({}).hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(null,arguments)}let p=e=>u.createElement("svg",c({xmlns:"http://www.w3.org/2000/svg",width:67,height:68,fill:"none"},e),s||(s=u.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:4,d:"m42.875 24.5-18.75 19m0-19 18.75 19M64.75 34c0 17.49-13.991 31.667-31.25 31.667S2.25 51.49 2.25 34.001 16.241 2.334 33.5 2.334s31.25 14.178 31.25 31.667"})));var f=t(90052),h=t(87638),d=t(90943),m=t(84648),E=t(78077),g=t(9861),y=t(52321),v=t(57329),b=t(48260),w=t(66042),S=t(96741),$=t(15082),x=t(55618),R=t(63568),I=t(2994),A=t(70580),j=t(50967);let O=(e,r,t,s,i)=>new Promise(async(n,o)=>{A.yX.post(`${j.Y.resetPassword}/${r}`,e).then(e=>{s(""),i(t("messages:resetpassworsuccess")),e.data&&n(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(400===e.response.status?(s(t("messages:invalidtoken")),i(null)):404===e.response.status&&(s(t("messages:resetpassword")),i(null))),e&&o(e)})}),P=()=>{let e=(0,I.useQueryClient)();return(0,I.useMutation)({mutationFn:({formValues:e,token:r,t,setErrMsg:s,setResetSuccess:i})=>O(e,r,t,s,i),onSuccess:r=>{e.invalidateQueries("user")},onError:e=>{}})};var N=t(4563),L=t(41107),T=t(5248),C=t(97980);let k=function({token:e,t:r,locale:t}){let[s,o]=(0,n.useState)(""),[a,l]=(0,n.useState)(!1),[u,c]=(0,n.useState)(!1),p=P(),[f,I]=(0,n.useState)(!1),[A,j]=(0,n.useState)(!1),[O,k]=(0,n.useState)(!1),D=()=>{I(!f)},M=()=>{j(!A)};return i.jsx(R.J9,{initialValues:{industry:"",country:"",password:"",confirmPassword:""},validationSchema:()=>(0,N.lg)(r),onSubmit:(t,{resetForm:s})=>{try{let{country:i,industry:n,password:a}=t,u={industry:n?[n]:void 0,country:i?[i]:void 0,password:a||void 0};Object.keys(u).forEach(e=>void 0===u[e]&&delete u[e]),Object.keys(u).length>0?p.mutate({formValues:u,token:e,t:r,setErrMsg:o,setResetSuccess:l}):console.warn("Aucune mise \xe0 jour effectu\xe9e, aucun champ n'est modifi\xe9."),s(),k(!0),setTimeout(()=>{window.location.href=`/${C.jb.login.route}`},3e3)}catch(e){console.error("Erreur lors de la r\xe9initialisation du mot de passe :",e)}},className:"formik-form",children:({values:e,errors:t,touched:n,setFieldValue:c,handleChange:p})=>(0,i.jsxs)(R.l0,{id:"login-form",className:"pentabell-form",children:[!u&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(h.Z,{className:"form-group light",children:[i.jsx(d.Z,{className:"label-pentabell light",children:r("register:country")}),i.jsx(m.Z,{className:"input-pentabell light",id:"tags-standard",options:T.nh,getOptionLabel:e=>e,name:"country",autoComplete:"off",value:e.country,onChange:(e,r)=>{c("country",r),o(""),l(!1)},renderInput:e=>i.jsx(E.Z,{...e,className:"input-pentabell multiple-select light",variant:"standard",placeholder:r("aiSourcingService:servicePageForm:chooseOne"),disabled:O,error:!!(t.country&&n.country)})})]}),i.jsx(R.Bc,{name:"country",children:e=>i.jsx(g.Z,{variant:"filled",severity:"error",children:e})}),(0,i.jsxs)(h.Z,{className:"form-group light",children:[i.jsx(d.Z,{className:"label-pentabell light",children:r("register:industry")}),i.jsx(m.Z,{className:"input-pentabell light",id:"tags-standard",options:L.J3,getOptionLabel:e=>e,name:"industry",value:e.industry,autoComplete:"off",onChange:(e,r)=>{c("industry",r),o(""),l(!1)},renderInput:e=>i.jsx(E.Z,{...e,className:"input-pentabell multiple-select light",variant:"standard",placeholder:r("aiSourcingService:servicePageForm:chooseOne"),disabled:O,error:!!(t.industry&&n.industry)})})]}),i.jsx(R.Bc,{name:"industry",children:e=>i.jsx(g.Z,{variant:"filled",severity:"error",children:e})})]}),(0,i.jsxs)(h.Z,{className:"form-group light",children:[i.jsx(d.Z,{className:"label-pentabell light",children:r("resetPassword:newPassword")}),i.jsx(y.Z,{className:"input-pentabell light",autoComplete:"new-password",placeholder:r("resetPassword:newPassword"),variant:"standard",type:f?"text":"password",value:e.password,name:"password",disabled:O,onChange:e=>{p(e),o(""),l(!1)},error:!!(t.password&&n.password),endAdornment:i.jsx(v.Z,{position:"end",children:i.jsx(b.Z,{className:`toggle-password fa fa-fw ${f?"fa-eye":"fa-eye-slash"}`,onClick:D,"aria-label":"toggle password visibility",edge:"end",children:f?i.jsx(S.Z,{}):i.jsx(w.Z,{})})})})]}),i.jsx(R.Bc,{name:"password",children:e=>i.jsx(g.Z,{variant:"filled",severity:"error",children:e})}),(0,i.jsxs)(h.Z,{className:"form-group light",children:[i.jsx(d.Z,{className:"label-pentabell light",children:r("resetPassword:confirmNewPassword")}),i.jsx(y.Z,{className:"input-pentabell light",placeholder:r("resetPassword:confirmNewPassword"),value:e.confirmPassword,type:A?"text":"password",disabled:O,onChange:e=>{p(e),o(""),l(!1)},name:"confirmPassword",autoComplete:"new-password",error:!!(t.confirmPassword&&n.confirmPassword),endAdornment:i.jsx(v.Z,{position:"end",children:i.jsx(b.Z,{className:`toggle-password fa fa-fw ${A?"fa-eye":"fa-eye-slash"}`,onClick:M,"aria-label":"toggle password visibility",edge:"end",children:A?i.jsx(S.Z,{}):i.jsx(w.Z,{})})})})]}),i.jsx(R.Bc,{name:"confirmPassword",children:e=>i.jsx(g.Z,{variant:"filled",severity:"error",children:e})}),i.jsx($.default,{text:r("resetPassword:resetPassword"),className:"btn btn-filled full-width btn-submit",type:"submit"}),i.jsx(x.Z,{errMsg:s,success:a})]})})},D=({searchParams:e,params:{locale:r}})=>{let{t}=(0,l.$G)(),s=e.token,[o,u]=(0,n.useState)(null),[c,h]=(0,n.useState)("");return((0,n.useEffect)(()=>{(()=>{try{if(!s)throw Error("Missing Token");let e=a().decode(s);if(!e)throw Error("Invalid token");let r=Math.floor(Date.now()/1e3);if(e.exp&&e.exp<r)throw Error("Token expired");u(!0)}catch(e){u(!1),h(e.message)}})()},[s,t]),null===o)?i.jsx("div",{children:"Loading..."}):o?i.jsx(f.Z,{id:"auth-layout",title:t("resetPassword:title"),children:i.jsx(k,{token:s,t:t,locale:r})}):i.jsx(f.Z,{id:"auth-layout",children:(0,i.jsxs)("div",{class:"text-center",children:[i.jsx(p,{}),i.jsx("p",{className:"heading-h1 text-white",children:t("resetPassword:title")}),i.jsx("p",{className:"sub-heading text-white",children:t("resetPassword:description")}),i.jsx($.default,{text:t("resetPassword:tryAgain"),className:"btn btn-filled full-width btn-submit",link:`/${C.jb.forgetPassword.route}`,locale:r})]})})}},90052:(e,r,t)=>{"use strict";t.d(r,{Z:()=>o});var s=t(10326),i=t(90423),n=t(6362);let o=function({children:e,title:r,subTitle:t}){return s.jsx("div",{id:"auth-layout",style:{backgroundImage:`url(${n.default.src})`},children:(0,s.jsxs)(i.default,{className:"container custom-max-width",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"heading-h1 text-white",children:r}),s.jsx("p",{className:"sub-heading text-white",children:t})]}),s.jsx("div",{children:e})]})})}},52669:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\app\[locale]\(website)\reset-password\page.jsx#default`)},6362:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s={src:"/_next/static/media/bg-auth.1842cff2.png",height:738,width:1440,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAElBMVEU7Tl4WKTkxRFQ/UmIlOEhOYXEF1jp9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIUlEQVR4nBXBgQ0AMAzCsEDo/y9Ps1G962gLUJpsIvmQBwVgAD+bqpS2AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:4}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,1479,1619,1336,4227,8077,6027,3718,5560,6636,4289,1692,1812,3969,4903],()=>t(43667));module.exports=s})();