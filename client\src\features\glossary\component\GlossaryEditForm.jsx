"use client";

import { CircularProgress } from "@mui/material";
import { useGetGlossaryByUrlAndLanguage } from "../hooks/glossaries.hook";
import GlossaryAddForm from "./GlossaryAddForm";

export default function GlossaryEditForm({ params }) {
  const glossaryId = params?.id;

  const glossaryGetHook = useGetGlossaryByUrlAndLanguage({
    id: glossaryId,
  });

  if (glossaryGetHook.isLoading) {
    return <CircularProgress />;
  }

  if (glossaryGetHook.isError) {
    return <div>Error: {glossaryGetHook.error.message}</div>;
  }

  if (glossaryGetHook.isSuccess) {
    const glossaryData = glossaryGetHook.data;

    const glossaryEng = glossaryData?.versions.en;
    const glossaryFr = glossaryData?.versions.fr;

    return (
      <GlossaryAddForm
        updateData
        glossaryId={glossaryId}
        glossaryEng={glossaryEng}
        glossaryFr={glossaryFr}
        robotsMeta={glossaryData?.robotsMeta}
      />
    );
  }
}
