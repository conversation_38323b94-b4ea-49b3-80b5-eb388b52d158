"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const apiKeyMiddleware = async (req, res, next) => {
    if (req.headers?.accept !== 'application/json' && req.method == 'GET') {
        try {
            const unauthorizedHtmlPath = path_1.default.join(__dirname, '../public/unauthorized.html');
            const unauthorizedHtml = fs_1.default.readFileSync(unauthorizedHtmlPath, 'utf-8');
            res.status(403).send(unauthorizedHtml);
        }
        catch (error) {
            console.error('Error reading unauthorized HTML file:', error);
            res.status(500).send('Internal Server Error');
        }
    }
    else {
        next();
    }
};
exports.default = apiKeyMiddleware;
//# sourceMappingURL=validateApiKey.middleware.js.map