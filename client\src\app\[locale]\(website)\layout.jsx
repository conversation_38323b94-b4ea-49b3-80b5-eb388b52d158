import { dir } from "i18next";
import initTranslations from "@/app/i18n";
import dynamic from "next/dynamic";
import { notFound } from "next/navigation";
import { ToastContainer } from "react-toastify";
import Script from "next/script";

import i18nConfig from "../../../../i18nConfig";
import TranslationsProvider from "@/components/TranslationProvider";
import { ReactQueryProvider } from "../../../lib/react-query-client";
import "react-toastify/dist/ReactToastify.css";
import { headers } from "next/headers";

export const metadata = {
  title: "International Recruitment, Staffing & Payroll Agency | Pentabell",
  description:
    "Seeking the best recruitment agency ? Pentabell is an award-winning provider of international recruitment, staffing, consulting and HR services.",
};

export function generateStaticParams() {
  return i18nConfig.locales.map((locale) => ({ locale }));
}

const WebsiteLayout = async ({ params: { locale }, children }) => {
  const validLocales = ["en", "fr"];
  if (!validLocales.includes(locale)) {
    notFound();
  }

  const userAgent = headers().get("user-agent") || "";
  const isMobile = /mobile/i.test(userAgent);
  const deviceType = isMobile ? "mobile" : "desktop";
  const isMobileSSR = deviceType === "mobile";

  const { resources } = await initTranslations(locale, [
    "menu",
    "footer",
    "register",
    "validations",
    "login",
    "messages",
    "activation",
    "forgotPassword",
    "resetPassword",
    "homePage",
    "middleeast",
    "guides",
    "eventDetailsLibya",
    "payrollService",
    "application",
    "global",
    "comments",
    "consultingServices",
    "technicalAssistanceService",
    "aiSourcingService",
    "directHiringService",
    "mainService",
    "aboutUs",
    "joinUs",
    "getInTouch",
    "contactUs",
    "listopportunity",
    "Tunisia",
    "Algeria",
    "libya",
    "france",
    "Egypte",
    "ksa",
    "qatar",
    "morocco",
    "dubai",
    "africa",
    "europe",
    "seoSettings",
    "opportunities",
    "iraq",
    "event",
    "eventDetails",
    "eventDetailsLeap",
    "country",
    "eventDetailsGitex",
    "ForumAfricaFrance",
    "expertCare",
    "createOpportunity",
    "PentabellSalestraining",
    "QHSEEXPO"
  ]);

  const Footer = dynamic(() => import("@/components/layouts/Footer"), {
    ssr: true,
  });

  const Header = dynamic(() => import("@/components/layouts/Header"), {
    ssr: true,
  });

  return (
    <html lang={locale} dir={dir(locale)}>
      <head>
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Black.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Bold.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Extrabold.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Light.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Medium.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Regular.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Semibold.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Thin.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <Script
          id="cookieyes-script"
          strategy="afterInteractive"
          type="text/partytown"
          dangerouslySetInnerHTML={{
            __html: `
          window.addEventListener('load', function() {
            // Initialize CookieYes
            window.cookieyes || (window.cookieyes = {});

            // Load CookieYes Script
            var script = document.createElement('script');
            script.src = 'https://cdn.cookieyes.com/client_data.js'; 
            script.type = 'text/javascript';
            script.async = true;
            script.onload = function () {
              // CookieYes initialization with your domain settings
              cookieyes.init({
                consent_url: '/cookie-policy',
                onConsentGiven: function () {
                  window.dataLayer = window.dataLayer || [];
                  window.dataLayer.push({'event': 'cookie_consent_given'});
                },
              });
            };
            document.body.appendChild(script);
          });
        `,
          }}
        />
        <Script
          id="gtm-script"
          strategy="afterInteractive"
          type="text/partytown"
          dangerouslySetInnerHTML={{
            __html: `
      (function(w,d,s,l,i){
        w[l]=w[l]||[];
        w[l].push({'gtm.start': new Date().getTime(), event:'gtm.js'});
        var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s), dl=l!='dataLayer'?'&l='+l:'';
        j.async=true; j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;
        f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-NXLL5DG');
    `,
          }}
        />
      </head>
      <body>
        <noscript
          type="text/partytown"
          dangerouslySetInnerHTML={{
            __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NXLL5DG"
        height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
          }}
        ></noscript>
        <ReactQueryProvider>
          <ToastContainer />
          <TranslationsProvider
            namespaces={[
              "menu",
              "footer",
              "register",
              "login",
              "validations",
              "messages",
              "opportunities",
              "activation",
            ]}
            locale={locale}
            resources={resources}
          >
            <Header
              resources={[
                "menu",
                "footer",
                "register",
                "login",
                "validations",
                "messages",
                "opportunities",
                "activation",
              ]}
              locale={locale}
              isMobileSSR={isMobileSSR}
            />
            <main>{children}</main>
            <Footer locale={locale} />
          </TranslationsProvider>
        </ReactQueryProvider>
      </body>
    </html>
  );
};

export default WebsiteLayout;
