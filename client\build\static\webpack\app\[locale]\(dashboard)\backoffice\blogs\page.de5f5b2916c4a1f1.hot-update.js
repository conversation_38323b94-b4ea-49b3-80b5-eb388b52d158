"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/ListArticles.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/ListArticles.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/CustomTooltip */ \"(app-pages-browser)/./src/components/ui/CustomTooltip.jsx\");\n/* harmony import */ var _components_loading_Loading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/loading/Loading */ \"(app-pages-browser)/./src/components/loading/Loading.jsx\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _assets_images_icons_preview_icon_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/preview-icon.svg */ \"(app-pages-browser)/./src/assets/images/icons/preview-icon.svg\");\n/* harmony import */ var _assets_images_icons_edit_icon_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/icons/edit-icon.svg */ \"(app-pages-browser)/./src/assets/images/icons/edit-icon.svg\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/assets/images/icons/applicationIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/applicationIcon.svg\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContentText/DialogContentText.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n/* harmony import */ var _components_ui_CustomFilters__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/CustomFilters */ \"(app-pages-browser)/./src/components/ui/CustomFilters.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ListArticles = (param)=>{\n    let { language } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [createdAt, setCreatedAt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const savedVisibility = localStorage.getItem(\"Visibility\");\n    const [visibility, setVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedVisibility || \"\");\n    const [isArchived, setIsArchivedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Default to show non-archived articles\n    const savedPagination = localStorage.getItem(\"PAGINATION_KEY\");\n    const savedSeachValue = localStorage.getItem(\"SearchValue\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedSeachValue || \"\");\n    const [paginationModel, setPaginationModel] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(savedPagination ? JSON.parse(savedPagination) : {\n        page: 0,\n        pageSize: 10\n    });\n    const isOpen = true;\n    const [selectedLanguage, setSelectedLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(language ? language : \"en\");\n    // State for confirmation dialogs\n    const [openArchiveDialog, setOpenArchiveDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openDeleteDialog, setOpenDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedArticle, setSelectedArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Hooks for archive and delete operations\n    const archiveArticleMutation = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.usearchivedarticle)();\n    const deleteArticleMutation = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useDeleteArticle)();\n    // Handler functions for archive and delete\n    const handleArchiveClick = (article)=>{\n        setSelectedArticle(article);\n        setOpenArchiveDialog(true);\n    };\n    const handleDeleteClick = (article)=>{\n        setSelectedArticle(article);\n        setOpenDeleteDialog(true);\n    };\n    const handleArchiveConfirm = async ()=>{\n        if (selectedArticle) {\n            try {\n                await archiveArticleMutation.mutateAsync({\n                    language: selectedLanguage,\n                    id: selectedArticle.id,\n                    archive: !selectedArticle.isArchived\n                });\n                getArticles.refetch();\n            } catch (error) {\n                console.error(\"Error archiving article:\", error);\n            }\n        }\n        setOpenArchiveDialog(false);\n        setSelectedArticle(null);\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (selectedArticle) {\n            try {\n                await deleteArticleMutation.mutateAsync({\n                    language: selectedLanguage,\n                    id: selectedArticle.id\n                });\n                getArticles.refetch();\n            } catch (error) {\n                console.error(\"Error deleting article:\", error);\n            }\n        }\n        setOpenDeleteDialog(false);\n        setSelectedArticle(null);\n    };\n    const handleDialogClose = ()=>{\n        setOpenArchiveDialog(false);\n        setOpenDeleteDialog(false);\n        setSelectedArticle(null);\n    };\n    const resetSearch = ()=>{\n        setCategory(\"\");\n        setSearchQuery(\"\");\n        setVisibility(\"\");\n        setSortOrder(\"\");\n        setCreatedAt(null);\n        setPublishDate(null);\n        setSelectedLanguage(language ? language : \"en\");\n        setPaginationModel({\n            page: 0,\n            pageSize: 10\n        });\n        setIsArchivedFilter(false); // Reset to show non-archived articles\n        setSearch(!search);\n        localStorage.setItem(\"Visibility\", \"\");\n        localStorage.setItem(\"SearchValue\", \"\");\n        localStorage.setItem(\"PAGINATION_KEY\", JSON.stringify({\n            page: 0,\n            pageSize: 10\n        }));\n    };\n    const truncateTitle = (title)=>{\n        const words = title.split(\" \");\n        if (words?.length > 4) {\n            return words.slice(0, 4).join(\" \") + \"...\";\n        } else {\n            return title;\n        }\n    };\n    const getCategoriesLang = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories)(selectedLanguage || \"en\");\n    const transformedCategoriesLang = getCategoriesLang?.data?.categories?.map((category)=>({\n            name: category?.versionscategory[0]?.name,\n            value: category?.versionscategory[0]?.name,\n            label: category?.versionscategory[0]?.name\n        })) || [];\n    const getArticles = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetArticlesDashboard)({\n        language: selectedLanguage,\n        pageSize: paginationModel.pageSize,\n        pageNumber: paginationModel.page + 1,\n        sortOrder,\n        searchQuery,\n        visibility,\n        createdAt,\n        isArchived,\n        publishDate,\n        categoryName: category\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSelectedLanguage(language);\n        getCategoriesLang.refetch();\n    }, [\n        language\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getArticles.refetch();\n    }, [\n        search,\n        paginationModel\n    ]);\n    const visibilityOption = [\n        {\n            value: \"Public\",\n            label: \"Public\"\n        },\n        {\n            value: \"Private\",\n            label: \"Private\"\n        },\n        {\n            value: \"Draft\",\n            label: \"Draft\"\n        }\n    ];\n    const handlePaginationChange = (newPaginationModel)=>{\n        setPaginationModel(newPaginationModel);\n        localStorage.setItem(\"PAGINATION_KEY\", JSON.stringify(newPaginationModel));\n    };\n    if (getArticles.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loading_Loading__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n            lineNumber: 195,\n            columnNumber: 12\n        }, undefined);\n    }\n    const rows = getArticles?.data?.articles?.map((item)=>({\n            id: item._id,\n            title: item?.versions?.[0]?.title ? truncateTitle(item?.versions?.[0]?.title) : \"No title\",\n            createdBy: item?.versions?.[0]?.createdBy || \"N/A\",\n            createdAt: item?.versions?.[0]?.createdAt,\n            language: item?.existingLanguages?.join(\"/\") || \"N/A\",\n            actions: item._id,\n            visibility: item?.versions?.[0]?.visibility || \"N/A\",\n            url: item?.versions?.[0]?.url || \"N/A\",\n            totalCommentaires: item?.totalCommentaires || \"0\",\n            isArchived: item?.versions?.[0]?.isArchived || false\n        })) || [];\n    const columns = [\n        {\n            field: \"title\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            headerName: t(\"listArticle:title\"),\n            flex: 1,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: `/${selectedLanguage}/blog/${params.row?.url}`,\n                    className: \"link\",\n                    children: params.row.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"createdBy\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            headerName: t(\"listArticle:createdBy\"),\n            flex: 0.4\n        },\n        {\n            field: \"createdAt\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            flex: 0.4,\n            headerName: t(\"listArticle:createdAt\"),\n            valueFormatter: _utils_functions__WEBPACK_IMPORTED_MODULE_11__.formatDate\n        },\n        {\n            field: \"language\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            flex: 0.4,\n            headerName: t(\"listopportunity:availablelanguage\")\n        },\n        {\n            field: \"visibility\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            flex: 0.4,\n            headerName: t(\"listArticle:visibility\")\n        },\n        {\n            field: \"isArchived\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            flex: 0.4,\n            headerName: t(\"listArticle:archived\"),\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: params.row.isArchived ? t(\"global:yes\") : t(\"global:no\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"totalCommentaires\",\n            headerClassName: \"datagrid-header\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            headerName: t(\"listArticle:nbOfComments\"),\n            flex: 0.4\n        },\n        {\n            field: \"actions\",\n            cellClassName: \"datagrid-cell\",\n            headerClassName: \"datagrid-header\",\n            headerName: \"\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"action-buttons\",\n                    style: {\n                        gridColumn: \"span 2\",\n                        width: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_edit_icon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.edit.route}/${params.row.id}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:edit\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"btn-download\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.comments.route}/${params.row.id}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 305,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:comments\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_preview_icon_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.websiteRoutesList.blog.route}/${params.row?.url}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:preview\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"22\",\n                                        height: \"20\",\n                                        viewBox: \"0 0 22 20\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M21 17C21 17.5304 20.7893 18.0391 20.4142 18.4142C20.0391 18.7893 19.5304 19 19 19H3C2.46957 19 1.96086 18.7893 1.58579 18.4142C1.21071 18.0391 1 17.5304 1 17V3C1 2.46957 1.21071 1.96086 1.58579 1.58579C1.96086 1.21071 2.46957 1 3 1H8L10 4H19C19.5304 4 20.0391 4.21071 20.4142 4.58579C20.7893 4.96086 21 5.46957 21 6V17Z\",\n                                            stroke: \"#1E1E1E\",\n                                            strokeWidth: \"2\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    leftIcon: true,\n                                    onClick: ()=>handleArchiveClick(params.row),\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 333,\n                                columnNumber: 15\n                            }, void 0),\n                            title: params.row.isArchived ? t(\"global:unarchive\") : t(\"global:archive\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"20\",\n                                        height: \"22\",\n                                        viewBox: \"0 0 20 22\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M1 5H3M3 5H19M3 5V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H15C15.5304 21 16.0391 20.7893 16.4142 20.4142C16.7893 20.0391 17 19.5304 17 19V5M6 5V3C6 2.46957 6.21071 1.96086 6.58579 1.58579C6.96086 1.21071 7.46957 1 8 1H12C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V5M8 10V16M12 10V16\",\n                                            stroke: \"#1E1E1E\",\n                                            strokeWidth: \"2\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    leftIcon: true,\n                                    onClick: ()=>handleDeleteClick(params.row),\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 367,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:delete\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, undefined),\n            flex: 1\n        }\n    ];\n    const archivedOptions = [\n        {\n            value: true,\n            label: \"Archived\"\n        },\n        {\n            value: false,\n            label: \"Not Archived\"\n        }\n    ];\n    const filters = [\n        {\n            type: \"text\",\n            label: \"Search By Title\",\n            value: searchQuery,\n            onChange: (e)=>setSearchQuery(e.target.value),\n            placeholder: \"Search\",\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:visibility\"),\n            value: visibility ? {\n                value: visibility,\n                label: visibilityOption.find((opt)=>opt.value === visibility)?.label || visibility\n            } : null,\n            onChange: (_, val)=>setVisibility(val?.value || \"\"),\n            options: visibilityOption,\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:archivage\"),\n            value: isArchived ? {\n                value: isArchived,\n                label: archivedOptions.find((opt)=>opt.value === isArchived)?.label || isArchived\n            } : null,\n            onChange: (_, val)=>setIsArchivedFilter(val?.value || \"\"),\n            options: archivedOptions,\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"global:sort\"),\n            value: sortOrder ? {\n                value: sortOrder,\n                label: t(sortOrder === \"desc\" ? \"global:newest\" : \"global:oldest\")\n            } : null,\n            onChange: (_, val)=>setSortOrder(val?.value || \"\"),\n            options: [\n                {\n                    value: \"desc\",\n                    label: t(\"global:newest\")\n                },\n                {\n                    value: \"asc\",\n                    label: t(\"global:oldest\")\n                }\n            ],\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:category\"),\n            value: category ? {\n                value: category,\n                label: transformedCategoriesLang.find((c)=>c.value === category)?.label || category\n            } : null,\n            onChange: (_, val)=>setCategory(val?.value || \"\"),\n            options: transformedCategoriesLang,\n            condition: true\n        },\n        {\n            type: \"date\",\n            label: t(\"listArticle:createdAt\"),\n            value: createdAt,\n            onChange: (newValue)=>setCreatedAt(newValue),\n            condition: true\n        },\n        {\n            type: \"date\",\n            label: t(\"listArticle:publishDate\"),\n            value: publishDate,\n            onChange: (newValue)=>setPublishDate(newValue),\n            condition: true\n        }\n    ];\n    const handleSearch = ()=>{\n        localStorage.setItem(\"SearchValue\", searchQuery);\n        localStorage.setItem(\"Visibility\", visibility);\n        setPaginationModel({\n            page: 0,\n            pageSize: paginationModel.pageSize\n        });\n        setSearch((prev)=>!prev);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"display-inline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"heading-h2 semi-bold\",\n                        children: [\n                            t(\"listArticle:listOfArticles\"),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"opportunities-nbr\",\n                                children: getArticles?.data?.totalArticles\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 501,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"btn btn-filled\",\n                        text: t(\"global:addarticle\"),\n                        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.add.route}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 508,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 500,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content ${isOpen ? \"open\" : \"closed\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"table-Grid\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomFilters__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    filters: filters,\n                                    onSearch: handleSearch,\n                                    onReset: resetSearch,\n                                    searchLabel: t(\"global:search\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 517,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    height: \"100%\",\n                                    width: \"100%\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_data_grid__WEBPACK_IMPORTED_MODULE_16__.DataGrid, {\n                                    rows: rows,\n                                    columns: columns,\n                                    pagination: true,\n                                    className: \"pentabell-table\",\n                                    paginationMode: \"server\",\n                                    paginationModel: paginationModel,\n                                    onPaginationModelChange: handlePaginationChange,\n                                    pageSizeOptions: [\n                                        5,\n                                        10,\n                                        25\n                                    ],\n                                    rowCount: getArticles?.data?.totalArticles || 0,\n                                    autoHeight: true,\n                                    disableSelectionOnClick: true,\n                                    columnVisibilityModel: {\n                                        createdBy: !isMobile,\n                                        createdAt: !isMobile,\n                                        totalCommentaires: !isMobile,\n                                        visibility: !isMobile,\n                                        language: !isMobile,\n                                        archived: !isMobile\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 529,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 516,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 515,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                open: openArchiveDialog,\n                onClose: handleDialogClose,\n                \"aria-labelledby\": \"archive-dialog-title\",\n                \"aria-describedby\": \"archive-dialog-description\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        id: \"archive-dialog-title\",\n                        children: [\n                            selectedArticle?.isArchived ? t(\"global:unarchive\") : t(\"global:archive\"),\n                            \" \",\n                            t(\"global:article\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 563,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            id: \"archive-dialog-description\",\n                            children: selectedArticle?.isArchived ? t(\"global:confirmUnarchiveArticle\") : t(\"global:confirmArchiveArticle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 570,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 569,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                text: t(\"global:cancel\"),\n                                onClick: handleDialogClose,\n                                className: \"btn btn-secondary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 577,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                text: selectedArticle?.isArchived ? t(\"global:unarchive\") : t(\"global:archive\"),\n                                onClick: handleArchiveConfirm,\n                                className: \"btn btn-primary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 582,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 576,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 557,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                open: openDeleteDialog,\n                onClose: handleDialogClose,\n                \"aria-labelledby\": \"delete-dialog-title\",\n                \"aria-describedby\": \"delete-dialog-description\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        id: \"delete-dialog-title\",\n                        children: [\n                            t(\"global:delete\"),\n                            \" \",\n                            t(\"global:article\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 601,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            id: \"delete-dialog-description\",\n                            children: t(\"global:confirmDeleteArticle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 605,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 604,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                text: t(\"global:cancel\"),\n                                onClick: handleDialogClose,\n                                className: \"btn btn-secondary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 610,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                text: t(\"global:delete\"),\n                                onClick: handleDeleteConfirm,\n                                className: \"btn btn-danger\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 615,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 609,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 595,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ListArticles, \"5YZ611sJ6OtD1N+zmncpv3mb7mI=\", false, function() {\n    return [\n        _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _barrel_optimize_names_Dialog_DialogActions_DialogContent_DialogContentText_DialogTitle_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useDeleteArticle,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetArticlesDashboard\n    ];\n});\n_c = ListArticles;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ListArticles);\nvar _c;\n$RefreshReg$(_c, \"ListArticles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/ListArticles.jsx\n"));

/***/ })

});