"use client";
import { useState, useRef, useEffect } from "react";
import { Formik, Field, Form, ErrorMessage } from "formik";
import { useTranslation } from "react-i18next";

import upload from "@/assets/images/add.png";
import { Visibility } from "../../../utils/constants";
import { useGetCategories } from "../hooks/blog.hook";
import { API_URLS } from "../../../utils/urls";
import { slug } from "@feelinglovelynow/slug";
import SunEditor from "suneditor-react";
import dayjs from "dayjs";
import { WithContext as ReactTags } from "react-tag-input";
import { v4 as uuidv4 } from "uuid";
import plugins from "suneditor/src/plugins";
import FaqSection from "./FaqSection";

import "react-datepicker/dist/react-datepicker.css";
import "suneditor/dist/css/suneditor.min.css";
import {
  Autocomplete,
  FormGroup,
  FormLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import { useSaveFile } from "@/features/opportunity/hooks/opportunity.hooks";
import DocumentImporter from "./DocumentImporter";

const AddArticle = ({
  language,
  initialValues,
  formRef,
  onImageSelect,
  validationSchema,
  image,
  isEdit,
  onCategoriesSelect,
  filteredCategories,
}) => {
  const handlePaste = (event, cleanData, maxCharCount) => {
    let html = cleanData;
    html = html.replace(/<strong>(.*?)$/g, "<strong>$1</strong>");
    return html;
  };

  const KeyCodes = {
    comma: 188,
    enter: 13,
  };

  const delimiters = [KeyCodes.comma, KeyCodes.enter];
  const [tags, setTags] = useState([]);
  const [highlights, setHighlights] = useState([]);

  useEffect(() => {
    setTags(initialValues?.keywords?.length > 0 ? initialValues?.keywords : []);
    setHighlights(
      initialValues?.highlights?.length > 0 ? initialValues?.highlights : []
    );
  }, [initialValues]);

  const { t, i18n } = useTranslation();

  const [publishNow, setPublishNow] = useState(false);
  const [publishDate, setPublishDate] = useState(new Date());

  const imageInputRef = useRef(null);
  const [selectedImage, setSelectedImage] = useState(null);

  const handlePhotoChange = async () => {
    const selectedFile = imageInputRef.current.files[0];
    setSelectedImage(imageInputRef.current.files[0]);

    if (selectedFile) {
      onImageSelect(selectedFile, language);
    }
  };
  const getCategories = useGetCategories(language);
  const transformedCategories =
    getCategories?.data?.categories?.map((category) => ({
      id: category.versionscategory[0]?.id,
      name: category.versionscategory[0]?.name,
    })) || [];

  const useSaveFileHook = useSaveFile();

  const handlePhotoBlogChange = async (file, info, core, uploadHandler) => {
    if (file instanceof HTMLImageElement) {
      const src = file.src;

      if (src.startsWith("data:image")) {
        const base64Data = src.split(",")[1];
        const contentType = src.match(/data:(.*?);base64/)[1];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length)
          .fill(0)
          .map((_, i) => byteCharacters.charCodeAt(i));
        const byteArray = new Uint8Array(byteNumbers);

        const blob = new Blob([byteArray], { type: contentType });
        const fileName = `image_${Date.now()}.${contentType.split("/")[1]}`;
        const selectedFile = new File([blob], fileName, { type: contentType });

        await uploadFile(selectedFile, uploadHandler, core, file);
      } else {
        fetch(src)
          .then((response) => response.blob())
          .then((blob) => {
            const contentType = blob.type;
            const fileName = `image_${Date.now()}.${contentType.split("/")[1]}`;
            const selectedFile = new File([blob], fileName, {
              type: contentType,
            });

            uploadFile(selectedFile, uploadHandler, core, file);
          })
          .catch((error) =>
            console.error("Error converting image URL to Blob:", error)
          );
      }
    } else {
      console.error("File is not an HTMLImageElement.");
    }
  };

  const uploadFile = (selectedFile, uploadHandler, core, originalImage) => {
    let uuidPhoto;
    uuidPhoto = uuidv4().replace(/-/g, "");

    const formData = new FormData();
    formData.append("file", selectedFile);

    const extension = selectedFile.name.split(".").pop();
    const currentYear = new Date().getFullYear();

    useSaveFileHook.mutate(
      {
        resource: "blogs",
        folder: currentYear.toString(),
        filename: uuidPhoto,
        body: { formData, t },
      },
      {
        onSuccess: (dataUUID) => {
          const uuidPhotoFileName =
            dataUUID.message === "uuid exist"
              ? dataUUID.uuid
              : `${uuidPhoto}.${extension}`;

          const imageUrl = `${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${uuidPhotoFileName}`;

          originalImage.src = imageUrl;

          uploadHandler({
            result: [
              {
                id: uuidPhotoFileName,
                url: imageUrl,
              },
            ],
          });
        },
        onError: (error) => {
          console.error("Error uploading file:", error);
        },
      }
    );
  };

  // Document import handlers
  const handleContentExtracted = (extractedContent) => {
    // Update the form field based on language
    const contentField = language === "en" ? "content" : "content";
    formRef.current?.setFieldValue(contentField, extractedContent);
  };

  const handleMetadataExtracted = (metadata) => {
    if (metadata.title && formRef.current) {
      const titleField = language === "en" ? "title" : "title";
      const urlField = language === "en" ? "url" : "url";
      const descriptionField =
        language === "en" ? "description" : "description";

      if (!formRef.current.values[titleField]) {
        formRef.current.setFieldValue(titleField, metadata.title);
        const url = slug(metadata.title);
        formRef.current.setFieldValue(urlField, url);
      }

      if (metadata.description && !formRef.current.values[descriptionField]) {
        formRef.current.setFieldValue(descriptionField, metadata.description);
      }

      if (metadata.keywords && metadata.keywords.length > 0) {
        const keywordField = language === "en" ? "keywords" : "keywords";
        const existingKeywords = formRef.current.values[keywordField] || [];
        const mergedKeywords = [...existingKeywords, ...metadata.keywords];
        formRef.current.setFieldValue(keywordField, mergedKeywords);
      }
    }
  };

  return (
    <div className="commun">
      <div id="experiences">
        <div id="form">
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={() => {}}
            innerRef={formRef}
            enableReinitialize="true"
          >
            {({ errors, touched, setFieldValue, values, validateForm }) => (
              <Form>
                <div className="inline-group">
                  <div>
                    {" "}
                    <FormGroup>
                      <FormLabel className="label-form">
                        {t("createArticle:title")}
                        <TextField
                          variant="standard"
                          name="title"
                          type="text"
                          value={values.title}
                          onChange={(e) => {
                            const title = e.target.value;
                            setFieldValue("title", title);
                            const url = slug(title);
                            setFieldValue("urlEN", url);
                          }}
                          className={
                            "input-pentabell" +
                            (errors.title && touched.title ? " is-invalid" : "")
                          }
                        />
                        <ErrorMessage
                          className="label-error"
                          name="title"
                          component="div"
                        />
                      </FormLabel>
                    </FormGroup>
                  </div>
                  <div>
                    <FormGroup>
                      <FormLabel className="label-form">
                        {t("createArticle:categories")}
                        <Stack>
                          <Autocomplete
                            multiple
                            className="input-pentabell"
                            id="tags-standard"
                            options={
                              language === "en"
                                ? transformedCategories
                                : language === "fr" && filteredCategories
                                ? filteredCategories
                                : []
                            }
                            // defaultValue={values?.category || []}
                            getOptionLabel={(option) => option.name}
                            value={
                              values.category.length > 0
                                ? transformedCategories.filter((category) =>
                                    values.category.some(
                                      (selectedCategory) =>
                                        selectedCategory === category.id
                                    )
                                  )
                                : []
                            }
                            onChange={(event, selectedOptions) => {
                              const categoryIds = selectedOptions.map(
                                (category) => category.id
                              );
                              setFieldValue("category", categoryIds);
                              if (language === "en" && onCategoriesSelect) {
                                onCategoriesSelect(categoryIds);
                              }
                            }}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                className="input-pentabell  multiple-select"
                                variant="standard"
                                // placeholder="nationalities"
                              />
                            )}
                          />
                        </Stack>
                      </FormLabel>
                      {touched.category && errors.category && (
                        <div className="label-error">{errors.category}</div>
                      )}
                    </FormGroup>
                  </div>
                </div>
                <div className="inline-group">
                  <div>
                    <FormGroup>
                      <FormLabel className="label-form">
                        Description
                        <TextField
                          variant="standard"
                          name="description"
                          type="text"
                          multiline
                          rows={3}
                          value={values.description}
                          onChange={(e) => {
                            const description = e.target.value;
                            setFieldValue("description", description);
                          }}
                          className={
                            "textArea-pentabell" +
                            (errors.description && touched.description
                              ? " is-invalid"
                              : "")
                          }
                        />{" "}
                        <ErrorMessage
                          className="label-error"
                          name="description"
                          component="div"
                        />
                      </FormLabel>
                    </FormGroup>
                  </div>
                </div>
                <div className="inline-group">
                  <div>
                    {" "}
                    <FormGroup>
                      <FormLabel className="label-form">
                        Highlights
                        <div id="tags">
                          <ReactTags
                            tags={highlights}
                            className={
                              "input-pentabell" +
                              (errors.highlights && touched.highlights
                                ? " is-invalid"
                                : "") +
                              (highlights.length === 0 ? " no-tags" : "")
                            }
                            delimiters={delimiters}
                            handleDelete={(i) => {
                              const updatedTags = highlights.filter(
                                (tag, index) => index !== i
                              );
                              setHighlights(updatedTags);
                              setFieldValue(
                                "highlights",
                                updatedTags.map((tag) => tag.text)
                              );
                            }}
                            handleAddition={(tag) => {
                              setHighlights([...highlights, tag]);
                              setFieldValue(
                                "highlights",
                                [...highlights, tag].map((item) => item.text)
                              );
                              const updatedTAgs = [...highlights, tag].map(
                                (item) => item.text
                              );
                            }}
                            inputFieldPosition="bottom"
                            autocomplete
                            allowDragDrop={false}
                          />
                        </div>
                        <ErrorMessage
                          className="label-error"
                          name="highlights"
                          component="div"
                        />
                      </FormLabel>
                    </FormGroup>
                  </div>
                </div>

                {/* Document Importer */}
                <DocumentImporter
                  onContentExtracted={handleContentExtracted}
                  onMetadataExtracted={handleMetadataExtracted}
                  language={language.toUpperCase()}
                  removeLabel={true}
                />

                <SunEditor
                  setContents={
                    values?.content?.length > 0 ? values.content : ""
                  }
                  onChange={(e) => {
                    setFieldValue("content", e);
                  }}
                  onPaste={handlePaste}
                  setOptions={{
                    cleanHTML: false,
                    disableHtmlSanitizer: true,
                    addTagsWhitelist:
                      "h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button",
                    plugins: plugins,
                    buttonList: [
                      ["undo", "redo"],
                      ["font", "fontSize", "formatBlock"],
                      [
                        "bold",
                        "underline",
                        "italic",
                        "strike",
                        "subscript",
                        "superscript",
                      ],
                      ["fontColor", "hiliteColor"],
                      ["align", "list", "lineHeight"],
                      ["outdent", "indent"],
                      ["table", "horizontalRule", "link", "image", "video"],
                      ["fullScreen", "showBlocks", "codeView"],
                      ["preview", "print"],
                      ["removeFormat"],
                    ],
                    imageUploadHandler: handlePhotoBlogChange,
                    defaultTag: "div",
                    minHeight: "300px",
                    maxHeight: "400px",
                    showPathLabel: false,
                    font: [
                      "Proxima-Nova-Regular",
                      "Proxima-Nova-Medium",
                      "Proxima-Nova-Semibold",
                      "Proxima-Nova-Bold",
                      "Proxima-Nova-Extrabold",
                      "Proxima-Nova-Black",
                      "Proxima-Nova-Light",
                      "Proxima-Nova-Thin",
                      "Arial",
                      "Times New Roman",
                      "Sans-Serif",
                    ],
                    charCounter: true, // Show character counter
                    charCounterType: "byte",
                    resizingBar: false, // Hide resizing bar for a cleaner UI
                    colorList: [
                      // Standard Colors
                      [
                        "#234791",
                        "#d69b19",
                        "#cc3233",
                        "#009966",
                        "#0b3051",
                        "#2BBFAD",
                        "#0b305100",
                        "#0a305214",
                        "#743794",
                        "#ff0000",
                        "#ff5e00",
                        "#ffe400",
                        "#abf200",
                        "#00d8ff",
                        "#0055ff",
                        "#6600ff",
                        "#ff00dd",
                        "#000000",
                        "#ffd8d8",
                        "#fae0d4",
                        "#faf4c0",
                        "#e4f7ba",
                        "#d4f4fa",
                        "#d9e5ff",
                        "#e8d9ff",
                        "#ffd9fa",
                        "#f1f1f1",
                        "#ffa7a7",
                        "#ffc19e",
                        "#faed7d",
                        "#cef279",
                        "#b2ebf4",
                        "#b2ccff",
                        "#d1b2ff",
                        "#ffb2f5",
                        "#bdbdbd",
                        "#f15f5f",
                        "#f29661",
                        "#e5d85c",
                        "#bce55c",
                        "#5cd1e5",
                        "#6699ff",
                        "#a366ff",
                        "#f261df",
                        "#8c8c8c",
                        "#980000",
                        "#993800",
                        "#998a00",
                        "#6b9900",
                        "#008299",
                        "#003399",
                        "#3d0099",
                        "#990085",
                        "#353535",
                        "#670000",
                        "#662500",
                        "#665c00",
                        "#476600",
                        "#005766",
                        "#002266",
                        "#290066",
                        "#660058",
                        "#222222",
                      ], // For box shadow with opacity
                    ],
                  }}
                  onImageUpload={handlePhotoBlogChange}
                />

                <br></br>

                <FaqSection
                  values={values}
                  setFieldValue={setFieldValue}
                  errors={errors}
                  touched={touched}
                  language={language === "en" ? "EN" : "FR"}
                  debounce={() => {}}
                  isEdit={true}
                />

                <br></br>
                <div className="inline-group">
                  <div>
                    <FormGroup>
                      <FormLabel className="label-form">
                        {t("createArticle:metaTitle")} ({" "}
                        <span
                          className={
                            values.metaTitle?.length > 65 ? " text-danger" : ""
                          }
                        >
                          {" "}
                          {values.metaTitle?.length} / 65{" "}
                        </span>{" "}
                        )
                        <TextField
                          variant="standard"
                          name="metaTitle"
                          type="text"
                          value={values.metaTitle}
                          onChange={(e) => {
                            setFieldValue("metaTitle", e.target.value);
                          }}
                          className={
                            "input-pentabell" +
                            (errors.metaTitle && touched.metaTitle
                              ? " is-invalid"
                              : "")
                          }
                        />{" "}
                        <ErrorMessage
                          className="label-error"
                          name="metaTitle"
                          component="div"
                        />
                      </FormLabel>
                    </FormGroup>
                  </div>
                  <div>
                    <FormGroup>
                      <FormLabel className="label-form">
                        {t("createArticle:url")}
                        <TextField
                          variant="standard"
                          name="url"
                          type="text"
                          value={values.url}
                          onChange={(e) => {
                            setFieldValue("url", e.target.value);
                          }}
                          className={
                            "input-pentabell" +
                            (errors.url && touched.url ? " is-invalid" : "")
                          }
                        />{" "}
                        <ErrorMessage
                          className="label-error"
                          name="url"
                          component="div"
                        />
                      </FormLabel>
                    </FormGroup>
                  </div>
                </div>
                <div className="inline-group">
                  <div>
                    <FormGroup>
                      <FormLabel className="label-form">
                        {t("createArticle:metaDescription")} ({" "}
                        <span
                          className={
                            values.metaDescription?.length > 160
                              ? " text-danger"
                              : ""
                          }
                        >
                          {values.metaDescription?.length} / 160
                        </span>{" "}
                        )
                        <TextField
                          variant="standard"
                          name="metaDescription"
                          type="text"
                          multiline
                          rows={3}
                          value={values.metaDescription}
                          onChange={(e) => {
                            setFieldValue("metaDescription", e.target.value);
                          }}
                          className={
                            "textArea-pentabell" +
                            (errors.metaDescription && touched.metaDescription
                              ? " is-invalid"
                              : "")
                          }
                        />{" "}
                        <ErrorMessage
                          className="label-error"
                          name="metaDescription"
                          component="div"
                        />
                      </FormLabel>
                    </FormGroup>
                  </div>
                </div>

                <div className="inline-group">
                  <div>
                    <FormGroup>
                      <FormLabel className="label-form">
                        {t("createArticle:featuredImage")}
                        <div className="upload-container">
                          <label
                            htmlFor={`image-upload-${language}`}
                            className="file-labels"
                          >
                            <input
                              type="file"
                              id={`image-upload-${language}`}
                              name="image"
                              accept=".png, .jpg, .jpeg, .webp"
                              ref={imageInputRef}
                              onChange={(e) => {
                                setFieldValue("image", e.target.files[0]);
                                handlePhotoChange();
                              }}
                              className={
                                "file-input" +
                                (errors.image && touched.image
                                  ? " is-invalid"
                                  : "")
                              }
                            />
                            <div className="upload-area">
                              <div>
                                <div
                                  className="icon-pic"
                                  style={{
                                    backgroundImage: `url("${
                                      selectedImage
                                        ? URL.createObjectURL(selectedImage)
                                        : image
                                        ? `${process.env.NEXT_PUBLIC_BASE_API_URL}${API_URLS.files}/${image}`
                                        : upload.src
                                    }")`,
                                    backgroundSize: "cover",
                                    backgroundRepeat: "no-repeat",
                                    backgroundPosition: "center",
                                    //backgroundColor: "#40bd3921",
                                  }}
                                ></div>
                              </div>
                              <div>
                                <p className="upload-text">
                                  {t("createArticle:addFeatImg")}
                                </p>
                                <p className="upload-description">
                                  {t("createArticle:clickBox")}
                                </p>
                              </div>
                            </div>
                            <ErrorMessage
                              name="image"
                              component="div"
                              className="invalid-feedback error"
                            />
                          </label>
                        </div>
                      </FormLabel>
                    </FormGroup>
                  </div>
                </div>

                <div className="inline-group">
                  <div>
                    {" "}
                    <FormGroup>
                      <FormLabel className="label-form">
                        {t("createArticle:alt")}
                        <TextField
                          variant="standard"
                          name="alt"
                          type="text"
                          value={values.alt}
                          onChange={(e) => {
                            setFieldValue("alt", e.target.value);
                          }}
                          className={
                            "input-pentabell" +
                            (errors.alt && touched.alt ? " is-invalid" : "")
                          }
                        />{" "}
                        <ErrorMessage
                          className="label-error"
                          name="alt"
                          component="div"
                        />
                      </FormLabel>
                    </FormGroup>
                  </div>
                  <div>
                    {" "}
                    <FormGroup>
                      <FormLabel className="label-form">
                        {t("createArticle:visibility")}

                        <Select
                          className="select-pentabell"
                          variant="standard"
                          value={Visibility.filter(
                            (option) => values.visibility === option
                          )}
                          selected={values?.visibility}
                          onChange={(event) => {
                            setFieldValue("visibility", event.target.value);
                          }}
                        >
                          {Visibility.map((item, index) => (
                            <MenuItem key={index} value={item}>
                              {item}
                            </MenuItem>
                          ))}
                        </Select>

                        <ErrorMessage
                          className="label-error"
                          name="visibilityEN"
                          component="div"
                        />
                      </FormLabel>
                    </FormGroup>
                  </div>

                  <div>
                    {" "}
                    <FormGroup>
                      <FormLabel className="label-form">
                        {t("createArticle:keyword")}
                        <div id="tags">
                          <ReactTags
                            tags={tags}
                            className={
                              "input-pentabell" +
                              (errors.keywords && touched.keywords
                                ? " is-invalid"
                                : "") +
                              (tags.length === 0 ? " no-tags" : "")
                            }
                            delimiters={delimiters}
                            handleDelete={(i) => {
                              const updatedTags = tags.filter(
                                (tag, index) => index !== i
                              );
                              setTags(updatedTags);
                              setFieldValue(
                                "keywords",
                                updatedTags.map((tag) => tag.text)
                              );
                            }}
                            handleAddition={(tag) => {
                              setTags([...tags, tag]);
                              setFieldValue(
                                "keywords",
                                [...tags, tag].map((item) => item.text)
                              );
                              const updatedTAgs = [...tags, tag].map(
                                (item) => item.text
                              );
                            }}
                            inputFieldPosition="bottom"
                            autocomplete
                            allowDragDrop={false}
                          />
                        </div>
                        <ErrorMessage
                          className="label-error"
                          name="keywords"
                          component="div"
                        />
                      </FormLabel>
                    </FormGroup>
                  </div>
                </div>

                <label className="label-form">
                  <Field
                    type="checkbox"
                    name="publishNow"
                    checked={publishNow}
                    onChange={(e) => {
                      setPublishNow(e.target.checked);
                      if (e.target.checked) {
                        setFieldValue("publishDate", new Date().toISOString());
                      }
                    }}
                  />
                  {t("createArticle:publishNow")}
                </label>

                {!publishNow && (
                  <div>
                    <FormGroup>
                      <FormLabel className="label-form">
                        {t("createArticle:publishDate")}
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                          <DemoContainer components={["DatePicker"]}>
                            <DatePicker
                              variant="standard"
                              className="input-date"
                              format="DD/MM/YYYY"
                              value={dayjs(values.publishDateEN)}
                              onChange={(date) => {
                                setFieldValue(
                                  "publishDateEN",
                                  dayjs(date).format("YYYY-MM-DD")
                                );
                              }}
                            />{" "}
                          </DemoContainer>
                        </LocalizationProvider>
                      </FormLabel>{" "}
                      <ErrorMessage
                        className="label-error"
                        name="publishDateEN"
                        component="div"
                      />
                    </FormGroup>
                  </div>
                )}

                <Field
                  type="hidden"
                  name="publishDate"
                  value={
                    publishNow
                      ? new Date().toISOString()
                      : publishDate.toISOString()
                  }
                />
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  );
};

export default AddArticle;
