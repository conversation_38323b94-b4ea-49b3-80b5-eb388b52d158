"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_en_createArticle_json";
exports.ids = ["_ssr_src_locales_en_createArticle_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/en/createArticle.json":
/*!*******************************************!*\
  !*** ./src/locales/en/createArticle.json ***!
  \*******************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"addCategory":"Add category","editCategory":"Edit category","nameCat":"Name categorie","descriptionCat":"Description","fr":"French","en":"English","image":"Image","addArticle":"Create New Blog","addArticleFr":"Create New Blog (French)","editArticle":"Edit Blog","editArticleFr":"Update Blog (French)","title":"Title","category":"Category","content":"Content","metaTitle":"Meta title","Robotsmeta":"Robots meta","categories":"Categories","url":"Url","metaDescription":"Meta description","featuredImage":"Featured image","alt":"Alt ( Featured image )","visibility":"Visibility","keyword":"Keyword","publishNow":"Publish Now","publishDate":"Select Publish Date","addFeatImg":"Add featured image","clickBox":"Click box to upload image","settings":"Settings","faqSection":"FAQ Section","faqTitle":"FAQ Title","faqTitlePlaceholder":"Enter FAQ section title (e.g., Frequently Asked Questions)","faqItems":"FAQ Items","addFaqItem":"Add FAQ Item","noFaqItems":"No FAQ items added yet. Click \'Add FAQ Item\' to get started.","faqItemNumber":"FAQ Item {{number}}","question":"Question","questionPlaceholder":"Enter the question here...","answer":"Answer","answerPlaceholder":"Enter the answer here..."}');

/***/ })

};
;