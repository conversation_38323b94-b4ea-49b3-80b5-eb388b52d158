"use client";
import { Grid } from "@mui/material";
import { Formik, Form } from "formik";
import FilterPopup from "../FilterPopup";
import { useEffect, useRef } from "react";

const FilterSidebar = ({
  initialValues,
  isFilterOpen,
  setIsFilterOpen,
  t,
  jobLocation,
  jobIndustry,
  countries,
  setSelectedFilters,
  setPageNumber,
  handleSubmitFilter,
}) => {
  const formikRef = useRef(null);

  useEffect(() => {
    const handleSearchPerformed = (event) => {
      if (formikRef.current) {
        const { setFieldValue } = formikRef.current;

        const {
          keyWord,
          country,
          industry,
          contractType,
          levelOfExperience,
          jobDescriptionLanguages,
        } = event.detail;

        if (keyWord)
          setFieldValue(
            "keyWord",
            Array.isArray(keyWord) ? keyWord : [keyWord]
          );
        if (country)
          setFieldValue(
            "country",
            Array.isArray(country) ? country : [country]
          );
        if (industry)
          setFieldValue(
            "industry",
            Array.isArray(industry) ? industry : [industry]
          );
        if (contractType)
          setFieldValue(
            "contractType",
            Array.isArray(contractType) ? contractType : [contractType]
          );
        if (levelOfExperience)
          setFieldValue(
            "levelOfExperience",
            Array.isArray(levelOfExperience)
              ? levelOfExperience
              : [levelOfExperience]
          );
        if (jobDescriptionLanguages)
          setFieldValue(
            "jobDescriptionLanguages",
            Array.isArray(jobDescriptionLanguages)
              ? jobDescriptionLanguages
              : [jobDescriptionLanguages]
          );
      }
    };

    window.addEventListener("searchPerformed", handleSearchPerformed);

    return () => {
      window.removeEventListener("searchPerformed", handleSearchPerformed);
    };
  }, []);

  return (
    <Grid item lg={3} md={3} sm={12} xs={12}>
      <Formik
        initialValues={initialValues}
        enableReinitialize="true"
        onSubmit={(values, actions) => handleSubmitFilter(values, actions)}
        innerRef={formikRef}
      >
        {({ setFieldValue, values }) => (
          <Form>
            <FilterPopup
              isOpen={isFilterOpen}
              onClose={() => setIsFilterOpen(false)}
              t={t}
              jobLocation={jobLocation}
              setFieldValue={setFieldValue}
              jobIndustry={jobIndustry}
              values={values}
              countries={countries}
              setSelectedFilters={setSelectedFilters}
              setPageNumber={setPageNumber}
            />
          </Form>
        )}
      </Formik>
    </Grid>
  );
};

export default FilterSidebar;
