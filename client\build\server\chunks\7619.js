"use strict";exports.id=7619,exports.ids=[7619],exports.modules={77394:(e,t,a)=>{a.d(t,{Z:()=>o});var s,r,n,i=a(95746);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let o=e=>i.createElement("svg",l({xmlns:"http://www.w3.org/2000/svg",width:48,height:49,fill:"none"},e),s||(s=i.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M24 25a1.5 1.5 0 0 1 1.5 1.5v16a1.5 1.5 0 0 1-3 0v-16A1.5 1.5 0 0 1 24 25",clipRule:"evenodd"})),r||(r=i.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M22.573 8.869A12.5 12.5 0 0 0 9.072 29.247a1.5 1.5 0 1 1-2.144 2.099 15.5 15.5 0 1 1 25.566-16.347H35a10.501 10.501 0 0 1 5.834 19.23 1.5 1.5 0 1 1-1.666-2.494A7.5 7.5 0 0 0 35 18h-3.58a1.5 1.5 0 0 1-1.438-1.071 12.5 12.5 0 0 0-7.409-8.06",clipRule:"evenodd"})),n||(n=i.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M22.94 25.44a1.5 1.5 0 0 1 2.12 0l8 8a1.5 1.5 0 0 1-2.12 2.12L24 28.622l-6.94 6.94a1.5 1.5 0 0 1-2.12-2.122z",clipRule:"evenodd"})))},80695:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var s,r,n=a(95746);function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let l=e=>n.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:42,height:42,fill:"none"},e),s||(s=n.createElement("g",{clipPath:"url(#Users_svg__a)"},n.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:4,d:"M29.75 36.75v-3.5a7 7 0 0 0-7-7h-14a7 7 0 0 0-7 7v3.5m38.5 0v-3.5A7 7 0 0 0 35 26.477m-7-21a7 7 0 0 1 0 13.563m-5.25-6.79a7 7 0 1 1-14 0 7 7 0 0 1 14 0"}))),r||(r=n.createElement("defs",null,n.createElement("clipPath",{id:"Users_svg__a"},n.createElement("path",{fill:"#fff",d:"M0 0h42v42H0z"})))))},28578:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var s,r,n=a(95746);function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let l=e=>n.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:42,height:42,fill:"none"},e),s||(s=n.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M10.5 38.5V7A3.5 3.5 0 0 1 14 3.5h14A3.5 3.5 0 0 1 31.5 7v31.5zM10.5 21H7a3.5 3.5 0 0 0-3.5 3.5V35A3.5 3.5 0 0 0 7 38.5h3.5M31.5 15.75H35a3.5 3.5 0 0 1 3.5 3.5V35a3.5 3.5 0 0 1-3.5 3.5h-3.5"})),r||(r=n.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.5 10.5h7M17.5 17.5h7M17.5 24.5h7M17.5 31.5h7"})))},92669:(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});var s,r,n,i=a(95746);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let o=e=>i.createElement("svg",l({xmlns:"http://www.w3.org/2000/svg",width:42,height:42,fill:"none"},e),s||(s=i.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M35 10.5H7A3.5 3.5 0 0 0 3.5 14v14A3.5 3.5 0 0 0 7 31.5h28a3.5 3.5 0 0 0 3.5-3.5V14a3.5 3.5 0 0 0-3.5-3.5"})),r||(r=i.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M21 24.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7"})),n||(n=i.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.5 21h.018M31.5 21h.017"})))},57015:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var s,r,n=a(95746);function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let l=e=>n.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:42,height:42,fill:"none"},e),s||(s=n.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M6.737 15.085a7 7 0 0 1 8.365-8.347 7 7 0 0 1 11.795 0 7 7 0 0 1 8.365 8.365 7 7 0 0 1 0 11.795 7 7 0 0 1-8.347 8.365 7 7 0 0 1-11.813 0 7 7 0 0 1-8.365-8.348 7 7 0 0 1 0-11.83"})),r||(r=n.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M28 14H17.5a3.5 3.5 0 1 0 0 7h7a3.5 3.5 0 1 1 0 7H14M21 31.5v-21"})))},94342:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var s,r=a(95746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let i=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:42,height:42,fill:"none"},e),s||(s=r.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M21 12.25v24.5M5.25 31.5a1.75 1.75 0 0 1-1.75-1.75V7a1.75 1.75 0 0 1 1.75-1.75H14a7 7 0 0 1 7 7 7 7 0 0 1 7-7h8.75A1.75 1.75 0 0 1 38.5 7v22.75a1.75 1.75 0 0 1-1.75 1.75h-10.5A5.25 5.25 0 0 0 21 36.75a5.25 5.25 0 0 0-5.25-5.25z"})))},47463:(e,t,a)=>{a.d(t,{Z:()=>c});var s=a(6005),r=a.n(s);let n={randomUUID:r().randomUUID},i=new Uint8Array(256),l=i.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let c=function(e,t,a){if(n.randomUUID&&!t&&!e)return n.randomUUID();let s=(e=e||{}).random||(e.rng||function(){return l>i.length-16&&(r().randomFillSync(i),l=0),i.slice(l,l+=16)})();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t){a=a||0;for(let e=0;e<16;++e)t[a+e]=s[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(s)}},33814:(e,t,a)=>{a.d(t,{Z:()=>v});var s,r=a(10326),n=a(17577),i=a(90423),l=a(16027),o=a(88948),c=a(21418),d=a(50295),u=a(52210),m=a(16376),p=a(95746);function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let g=e=>p.createElement("svg",h({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),s||(s=p.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16 2v4M8 2v4m-5 4h18M5 4h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2"}))),x=({t:e,section:t})=>{if(!t||!t.data)return null;let{description:a,subTitle:s,subDescription:n,leaves:i=[],annualLeave:l,maternityLeave:o,paternityLeave:c,sickLeave:d,paidTimeOff:u,parentalLeave:m,bereavementLeave:p,pilgrimageleave:h,marriageLeave:x,casualLeave:f}=t.data,v=(t,a)=>(0,r.jsxs)("div",{className:"item",children:[r.jsx("p",{className:"service-sub-title",children:e(t)}),r.jsx("p",{className:"service-description paragraph",children:Array.isArray(a)?a.map((t,a)=>r.jsx("p",{children:e(t)},a)):r.jsx("p",{children:e(a)})})]}),y=(t,a)=>t?Array.isArray(t)?t.map((t,s)=>r.jsx("p",{className:a,children:e(t)},s)):r.jsx("p",{className:a,children:e(t)}):null;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{id:"labor-tn-laws",className:"item",children:[y(a,"service-description paragraph"),y(s,"service-sub-title"),y(n,"service-description paragraph"),r.jsx("div",{className:"holidays-dates",children:i.map((t,a)=>(0,r.jsxs)("div",{className:"item",children:[(0,r.jsxs)("p",{className:"title",children:[r.jsx(g,{}),e(t.date)]}),r.jsx("p",{className:"paragraph",children:e(t.title)})]},a))})]}),l&&v(l.title,l.description),u&&v(u.title,u.description),o&&v(o.title,o.description),c&&v(c.title,c.description),d&&v(d.title,d.description),f&&v(f.title,f.description),x&&v(x.title,x.description),p&&v(p.title,p.description),m&&v(m.title,m.description),h&&v(h.title,h.description)]})},f=({t:e,data:t})=>t&&t.items?(0,r.jsxs)(r.Fragment,{children:[t.titleKey&&(0,r.jsxs)("div",{className:"item",children:[r.jsx("p",{className:"service-sub-title",children:e(t.titleKey)}),t.descriptionKey&&r.jsx("p",{className:"service-description paragraph",children:e(t.descriptionKey)})]}),r.jsx("div",{className:"payroll-tn",children:t.items.map((t,a)=>(0,r.jsxs)("div",{className:"payroll-tn-item",children:[t.titleKey&&r.jsx("p",{className:"title",children:e(t.titleKey)}),t.dateKeys&&r.jsx("p",{className:"date",children:t.dateKeys.map((a,s)=>(0,r.jsxs)("span",{children:[e(a),s<t.dateKeys.length-1&&r.jsx("br",{})]},s))}),t.descriptionKey&&r.jsx("p",{className:"paragraph",children:e(t.descriptionKey)})]},a))})]}):null,v=({data:e})=>{let{t}=(0,u.$G)(),[a,s]=(0,n.useState)(e.sections[0]?.id||!1),p=e=>(t,a)=>{s(!!a&&e)};return r.jsx("div",{id:"labor-tn-laws",className:"custom-max-width",children:(0,r.jsxs)(i.default,{children:[r.jsx("h2",{className:"heading-h1",children:t(e.title)}),r.jsx(l.default,{container:!0,columnSpacing:3,rowSpacing:2,className:"container",children:e.sections.map(e=>{let{id:s,title:n,type:i,subsections:u,data:h}=e;return r.jsx(l.default,{item:!0,xs:12,sm:12,children:(0,r.jsxs)(o.Z,{elevation:0,expanded:a===s,className:"services-accordion",disableGutters:!0,onChange:p(s),children:[r.jsx(c.Z,{"aria-controls":`panel-content-${s}`,id:`panel-header-${s}`,className:"services-accordion-header",expandIcon:r.jsx(m.Z,{}),children:r.jsx("h3",{className:"service-title",children:t(n)})}),(0,r.jsxs)(d.Z,{children:["leaveEntitlements"===i&&r.jsx(x,{t:t,section:e}),"payroll"===i&&r.jsx(f,{t:t,data:e}),!i&&u?.map((e,a)=>r.jsxs("div",{children:[e.title&&r.jsx("p",{className:"service-sub-title",children:t(e.title)}),e.description&&(Array.isArray(e.description)?e.description.map((e,a)=>r.jsx("p",{className:"service-description paragraph",children:t(e)},a)):r.jsx("p",{className:"service-description paragraph",children:t(e.description)})),e.list&&r.jsx("div",{className:"item",children:r.jsx("ul",{className:"service-description paragraph",children:e.list.map((e,a)=>r.jsxs("li",{children:[t(e.text),e.sublist&&r.jsx("ul",{children:e.sublist.map((e,a)=>r.jsx("li",{children:t(e)},a))})]},a))})})]},a))]})]})},s)})})]})})}},9741:(e,t,a)=>{a.d(t,{default:()=>S});var s=a(10326),r=a(63568),n=a(90423),i=a(16027),l=a(87638),o=a(90943),c=a(78077),d=a(9861),u=a(84648),m=a(5394),p=a(76971),h=a(9252);a(11148);var g=a(10123),x=a(96672),f=a(47463),v=a(15082),y=a(17577),j=a(52210),b=a(87419),w=a(4563),N=a(55618),A=a(18970),F=a(26066),Z=a(77394),C=a(5926),E=a(86184),k=a(5248);let S=function({title:e,country:t,defaultCountryPhone:a}){let S,O;let[M,$]=(0,y.useState)(""),[P,L]=(0,y.useState)(!1),[T,H]=(0,y.useState)(null),[U,I]=(0,y.useState)(""),{t:W}=(0,j.$G)(),D=h.PhoneNumberUtil.getInstance(),_=(0,b.uu)(L,$),K=(0,E.jd)(),B=new FormData,R=async(e,{resetForm:a})=>{let s={...Object.fromEntries(Object.entries(e).filter(([e,t])=>"acceptTerms"!==e&&""!==t&&null!=t))};window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:`${t.toLowerCase()}_office_form`,button_id:"my_button"}),await _.mutateAsync({countryName:t,...s,to:`${process.env.NEXT_PUBLIC_EMAIL_FORM_DESTINATION}`,team:"Digital",type:"countryContact"}),a(),setTimeout(()=>{L(!1)},3e3)},Y=(e,t)=>{e.preventDefault(),S=(0,f.Z)().replace(/-/g,""),I(""),H(null);let a=e.target.files[0];if(a){B.append("file",a);let e=a.name.split(".").pop();O=`${S}.${e}`;let s=new Date().getFullYear();K.mutate({resource:"candidates",folder:s,filename:S,body:{formData:B,t:W}},{onSuccess:e=>{"uuid exist"===e.message?(H(e.uuid),t("resume",e.uuid)):(H(O),t("resume",O))},onError:e=>{400===e.response.data.status?I(W("messages:requireResume")):500===e.response.data.status?I("Internal Server Error"):I(e.response.data.message)}})}},X=e=>{try{return D.isValidNumber(D.parseAndKeepRawInput(e))}catch(e){return!1}},z=g.Z_().test("is-valid-phone",W("validations:phoneFormat"),e=>X(e)),Q=e=>(0,w.eo)(e).shape({phone:z,email:g.Z_().email(e("validations:invalidEmail")).required(e("validations:required")).test("is-company-email",e("validations:companyEmailRequired"),function(e){let{youAre:t}=this.parent;return!!e&&("Company"!==t||F.isCompanyEmail(e))})});return(0,s.jsxs)("div",{id:"service-page-form",children:[s.jsx(A.Z,{}),(0,s.jsxs)(n.default,{className:"custom-max-width",children:[(0,s.jsxs)("h2",{className:"heading-h1 text-white text-center",children:[e||(0,s.jsxs)(s.Fragment,{children:[W("Tunisia:form:title1")," ",s.jsx("span",{className:"text-yellow",children:W("Tunisia:form:title2")})]})," "]}),s.jsx(r.J9,{initialValues:{fullName:"",email:"",phone:"",youAre:"",howToHelp:"",message:"",resume:"",field:"",acceptTerms:!1},validationSchema:()=>Q(W),onSubmit:R,children:({values:e,handleChange:t,setFieldValue:n,errors:h,touched:g})=>s.jsx(r.l0,{className:"pentabell-form",children:(0,s.jsxs)(i.default,{container:!0,rowSpacing:4,columnSpacing:3,children:[(0,s.jsxs)(i.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(l.Z,{className:"form-group light",children:[(0,s.jsxs)(o.Z,{className:"label-pentabell light",children:[W("aiSourcingService:servicePageForm:fullName"),"*"]}),s.jsx(c.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:W("aiSourcingService:servicePageForm:fullName"),variant:"standard",type:"text",name:"fullName",value:e.fullName,onChange:t,error:!!(h.fullName&&g.fullName)})]}),s.jsx(r.Bc,{name:"fullName",children:e=>s.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),(0,s.jsxs)(i.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(l.Z,{className:"form-group light",children:[s.jsx(o.Z,{className:"label-pentabell light",children:"Email*"}),s.jsx(c.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:"Email",variant:"standard",type:"email",name:"email",value:e.email,onChange:t,error:!!(h.email&&g.email)})]}),s.jsx(r.Bc,{name:"email",children:e=>s.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),(0,s.jsxs)(i.default,{item:!0,xs:12,sm:"Company"===e.youAre||"Consultant"===e.youAre?6:12,children:[(0,s.jsxs)(l.Z,{className:"form-group light",children:[(0,s.jsxs)(o.Z,{className:"label-pentabell light",children:[W("aiSourcingService:servicePageForm:youAre"),"*"]}),s.jsx(u.Z,{className:"input-pentabell light",id:"tags-standard",options:["Consultant","Company"],getOptionLabel:e=>e,name:"youAre",value:e.youAre,onChange:(e,t)=>n("youAre",t),renderInput:e=>s.jsx(c.Z,{...e,className:"input-pentabell multiple-select  light",variant:"standard",placeholder:W("aiSourcingService:servicePageForm:chooseOne"),error:!!(h.youAre&&g.youAre)})})]}),s.jsx(r.Bc,{name:"youAre",children:e=>s.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),"Consultant"===e.youAre&&(0,s.jsxs)(i.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(l.Z,{className:"form-group light",children:[(0,s.jsxs)(o.Z,{className:"label-pentabell light",children:[W("consultingServices:servicePageForm:industry"),"*"]}),s.jsx(u.Z,{className:"input-pentabell light",id:"tags-standard",options:Object.values(k.b5),getOptionLabel:e=>e,name:"field",value:e.field,onChange:(e,t)=>{n("field",t)},renderInput:e=>s.jsx(c.Z,{...e,className:"input-pentabell multiple-select  light",variant:"standard",placeholder:W("consultingServices:servicePageForm:chooseOne"),error:!!(h.field&&g.field)})})]}),s.jsx(r.Bc,{name:"field",children:e=>s.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),"Company"===e.youAre&&(0,s.jsxs)(i.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(l.Z,{className:"form-group light",children:[s.jsx(o.Z,{className:"label-pentabell light",children:W("payrollService:servicePageForm:companyName")}),s.jsx(c.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:W("payrollService:servicePageForm:companyName"),variant:"standard",type:"text",name:"companyName",value:e.companyName,onChange:t,error:!!(h.companyName&&g.companyName)})]}),s.jsx(r.Bc,{name:"companyName",children:e=>s.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),(0,s.jsxs)(i.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(l.Z,{className:"form-group light",children:[s.jsx(o.Z,{className:"label-pentabell light",children:W("register:phoneNumber")}),s.jsx(x.sb,{defaultCountry:a||"fr",className:"input-pentabell light",value:e.phone,onChange:e=>{n("phone",e),$("")},flagComponent:e=>s.jsx(C.Z,{...e})})]}),s.jsx(r.Bc,{name:"phone",children:e=>s.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),(0,s.jsxs)(i.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(l.Z,{className:"form-group light",children:[(0,s.jsxs)(o.Z,{className:"label-pentabell light",children:[W("Tunisia:form:howWeCanHelp"),"*"]}),s.jsx(c.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:W("Tunisia:form:howWeCanHelp"),variant:"standard",type:"text",name:"howToHelp",value:e.howToHelp,onChange:t,error:!!(h.howToHelp&&g.howToHelp)})]}),s.jsx(r.Bc,{name:"howToHelp",children:e=>s.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),(0,s.jsxs)(i.default,{item:!0,xs:12,sm:12,children:[(0,s.jsxs)(l.Z,{className:"form-group light",children:[s.jsx(o.Z,{className:"label-pentabell light",children:"Message"}),s.jsx(c.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:"Message",variant:"standard",type:"text",name:"message",value:e.message,onChange:t,error:!!(h.message&&g.message)})]}),s.jsx(r.Bc,{name:"message",children:e=>s.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),"Consultant"===e.youAre&&(0,s.jsxs)(i.default,{item:!0,xs:12,sm:12,children:[s.jsx(l.Z,{className:"form-group light form-section",children:(0,s.jsxs)("div",{className:"custom-file-upload",onClick:()=>document.getElementById("file-upload").click(),children:[(0,s.jsxs)("div",{children:[s.jsx(Z.Z,{}),T?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(o.Z,{className:"label-pentabell light",children:[W("joinUs:form:uploadCv"),"*"]}),s.jsx("p",{className:"sub-label",children:T})]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(o.Z,{className:"label-pentabell light",children:W("joinUs:form:uploadCv")}),s.jsx("p",{className:"sub-label",children:W("joinUs:form:control")})]}),s.jsx(v.default,{text:"Choose a file",className:"btn btn-outlined white"}),U&&s.jsx(d.Z,{variant:"filled",severity:"error",children:U})]}),s.jsx("input",{id:"file-upload",type:"file",name:"resume",accept:"application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword",style:{display:"none"},onChange:e=>{Y(e,n)},error:!!(h.resume&&g.resume)})]})}),s.jsx(r.Bc,{name:"resume",children:e=>s.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]}),s.jsx(i.default,{item:!0,xs:12,sm:8,children:(0,s.jsxs)(l.Z,{children:[s.jsx(m.Z,{className:"checkbox-pentabell light",control:s.jsx(p.Z,{name:"acceptTerms",checked:e.acceptTerms,onChange:t,error:!!(h.acceptTerms&&g.acceptTerms)}),label:W("aiSourcingService:servicePageForm:formSubmissionAgreement")}),s.jsx(r.Bc,{name:"acceptTerms",children:e=>s.jsx(d.Z,{variant:"filled",severity:"error",children:e})})]})}),s.jsx(i.default,{item:!0,xs:12,sm:4,className:"flex-end",children:s.jsx(v.default,{text:W("aiSourcingService:servicePageForm:send"),className:"btn btn-filled btn-submit",type:"submit"})})]})})}),s.jsx(N.Z,{errMsg:M,success:P})]})]})}},86184:(e,t,a)=>{a.d(t,{$i:()=>g,BF:()=>h,Fe:()=>i,Gc:()=>d,HF:()=>n,Hr:()=>o,IZ:()=>p,NF:()=>c,PM:()=>l,UJ:()=>u,jd:()=>m});var s=a(2994),r=a(21464);a(35047),a(97980);let n=()=>(0,s.useMutation)({mutationFn:e=>(0,r.W3)(e),onError:e=>{e.message=""}}),i=e=>(0,s.useQuery)("opportunities",async()=>await (0,r.fH)(e)),l=()=>(0,s.useMutation)(()=>(0,r.AE)()),o=e=>(0,s.useQuery)(["opportunities",e],async()=>await (0,r.Mq)(e)),c=()=>(0,s.useMutation)({mutationFn:(e,t,a)=>(0,r.rE)(e,t,a),onError:e=>{e.message=""}}),d=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t)=>(0,r.S1)(e,t),onError:e=>{e.message=""}})),u=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t,a)=>(0,r.lU)(e,t,a),onError:e=>{e.message=""}})),m=()=>{let e=(0,s.useQueryClient)();return(0,s.useMutation)({mutationFn:(e,t,a,s)=>(0,r.yH)(e,t,a,s),onSuccess:t=>{e.invalidateQueries("files")}})},p=()=>(0,s.useQuery)("SeoOpportunities",async()=>await (0,r.yJ)()),h=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t)=>(0,r.mt)(e,t),onError:e=>{e.message=""}})),g=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:({language:e,id:t,archive:a})=>(0,r.TK)(e,t,a),onError:e=>{console.error("Error during mutation",e),e.message=""}}))},21464:(e,t,a)=>{a.d(t,{AE:()=>c,Mq:()=>o,S1:()=>u,TK:()=>g,W3:()=>i,fH:()=>l,lU:()=>m,mt:()=>x,rE:()=>d,yH:()=>p,yJ:()=>h});var s=a(50967),r=a(70580),n=a(31190);let i=e=>(e.t,new Promise(async(t,a)=>{r.yX.post(`/opportunities${s.Y.applications}/${e.opportunityId}`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&a(e)})})),l=e=>new Promise(async(t,a)=>{try{let a=await r.yX.get(`${s.Y.opportunity}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyWord:e.keyWord,visibility:e.visibility,title:e.title,createdAt:e.createdAt,publishDate:e.publishDate,country:e.country,industry:e.industry,contractType:e.contractType,minExperience:e.minExperience,maxExperience:e.maxExperience,jobDescriptionLanguages:e.jobDescriptionLanguages,reference:e.reference,opportunityType:e.opportunityType,exclude:"In House"!==e.opportunityType&&"true"}});t(a.data)}catch(e){a(e)}}),o=e=>new Promise(async(t,a)=>{try{let a=await r.yX.get(`${s.Y.opportunity}/${e}`);t(a.data)}catch(e){a(e)}}),c=async()=>(await r.xk.put("/UpdateJobdescription")).data,d=({data:e,language:t,id:a})=>new Promise(async(i,l)=>{r.yX.post(`${s.Y.opportunity}/${t}/${a}`,e).then(e=>{"en"===t&&n.Am.success("Opportunity english updated successfully"),"fr"===t&&n.Am.success("Opportunity french updated successfully"),e?.data&&i(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&l(e)})}),u=({data:e,id:t})=>new Promise(async(a,i)=>{r.yX.put(`${s.Y.opportunity}/${t}`,e).then(e=>{n.Am.success("Opportunity Commun fields updated successfully"),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})}),m=({id:e,title:t,typeOfFavourite:a})=>new Promise(async(i,l)=>{r.yX.put(`${s.Y.baseUrl}/favourite/${e}`,{type:a}).then(e=>{n.Am.success(`${a} : ${t} saved to your favorites.`),e?.data&&i(e.data)}).catch(e=>{e&&e.response&&e.response.data&&409===e.response.status&&n.Am.warning(` ${t} already in shortlist`),e&&l(e)})}),p=({resource:e,folder:t,filename:a,body:i})=>new Promise(async(l,o)=>{r.cU.post(`${s.Y.files}/uploadResume/${e}/${t}/${a}`,i.formData).then(e=>{e.data&&l(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(400===e.response.status?e.response.data.message.includes("The resume lacks essential information")?n.Am.warn(i.t("messages:requireResume")):n.Am.warn(e.response.data.message):500===e.response.status&&n.Am.error("Internal Server Error")),e&&o(e)})}),h=()=>new Promise(async(e,t)=>{try{let t=await r.yX.get(`${s.Y.seoOpportunity}`);e(t.data)}catch(e){t(e)}}),g=(e,t,a)=>new Promise(async(i,l)=>{try{let l=await r.yX.put(`${s.Y.opportunity}/${e}/${t}/desarchiver`,{archive:a});l?.data&&(n.Am.success(`opportunity ${a?"archived":"desarchived"} successfully`),i(l.data))}catch(e){n.Am.error(`Failed to ${a?"archive":"desarchive"} the opportunity.`),l(e)}}),x=({data:e,id:t})=>new Promise(async(a,i)=>{r.yX.put(`${s.Y.seoOpportunity}/${t}`,e).then(e=>{n.Am.success("Opportunity seo updated successfully"),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})})},94034:(e,t,a)=>{a.d(t,{Z:()=>i});var s=a(19510),r=a(55920),n=a(70064);let i=function({bannerImg:e,height:t,title:a,IconImg:i,description:l,subtitle:o,bottomChildren:c,opportunitysubTitle:d,centerValue:u,altImg:m,topChildren:p,isEvent:h,title2:g,titleHighlight:x,bannerImgDynamic:f,link:v,linkTitle:y,highlights:j}){return(0,s.jsxs)("div",{id:"banner-component",className:!0===u?"center-banner":"",style:{backgroundImage:e?.src?`url(${e.src})`:f?`url(${f})`:"none",height:t||"auto"},children:[m&&s.jsx("img",{width:0,height:0,alt:m,src:"",style:{display:"none"},loading:"lazy"}),(0,s.jsxs)(r.Z,{className:"custom-max-width",children:[p&&p,i&&s.jsx("img",{src:i.src}),h?s.jsx(s.Fragment,{children:j&&j.length>0?(0,s.jsxs)("h1",{className:"heading-h1 text-white",children:[" ",(0,n.q1)(x,j)," "]}):(0,s.jsxs)(s.Fragment,{children:[" ",(0,s.jsxs)("h1",{className:"heading-h1 text-white",children:[(0,s.jsxs)("span",{className:"text-yellow",children:[a," "]}),x,g]}),s.jsx("p",{className:"sub-heading text-slide text-white  ",children:o})]})}):(0,s.jsxs)(s.Fragment,{children:[" ",s.jsx("h1",{className:"heading-h1 text-white",children:a||"Services We Offer"}),s.jsx("p",{className:"sub-heading text-slide text-white  ",children:o}),s.jsx("p",{className:"sub-heading text-slide text-white  ",children:d}),s.jsx("p",{className:"sub-heading text-slide text-white  ",children:l||null}),v&&s.jsx("a",{href:v,style:{textDecoration:"none",width:"fit-content"},className:"btn btn-filled ",children:y})]}),c&&c]})]})}},28409:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\features\forms\components\TunisiaOfficePageForm.jsx#default`)}};