"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useTheme, useMediaQuery, Grid } from "@mui/material";
import moment from "moment";
import "moment/locale/fr";
import countries from "i18n-iso-countries";
import enLocale from "i18n-iso-countries/langs/en.json";
import { useTranslation } from "react-i18next";

import CustomButton from "@/components/ui/CustomButton";
import {
  findIndustryClassname,
  findIndustryColoredIcon,
  findIndustryLabel,
  findIndustryLink,
  // getCountryImage,
  industryExists,
} from "@/utils/functions";
import GTM from "../../../../components/GTM";
import DialogModal from "@/features/user/component/updateProfile/experience/DialogModal";
import SvgTime from "@/assets/images/icons/time.svg";
import SvgFileText from "@/assets/images/icons/FileText.svg";
import SvgBookmark from "@/assets/images/icons/bookmark.svg";
import useCurrentUser from "../../../auth/hooks/currentUser.hooks";
import { Role } from "@/utils/constants";
import { useAddToFavourite } from "../../hooks/opportunity.hooks";
import { capitalizeFirstLetter } from "../../../../utils/functions";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJson } from "@/config/axios";

function OpportunityItem({ opportunity, language }) {
  const { t, i18n } = useTranslation();
  countries.registerLocale(enLocale);
  const theme = useTheme();
  const { user } = useCurrentUser();
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [jobsTodelete, setJobsTodelete] = useState(false);

  const handeleDeleteconfirmation = () => {
    setJobsTodelete(opportunity?._id);
    setShowDeleteConfirmation(true);
  };

  const handlecanceldelete = () => {
    setShowDeleteConfirmation(false);
  };

  const deleteOpportunityFromShortlist = async (opportunityId) => {
    try {
      await axiosGetJson.delete(`/favourite/${opportunityId}`, {
        data: { type: "opportunity" },
      });
      setIsSaved(false);
    } catch (error) {}
  };

  const handleToggleOpportunity = async () => {
    try {
      await deleteOpportunityFromShortlist(jobsTodelete);
    } catch (error) {}
    setShowDeleteConfirmation(false);
  };

  moment.locale(i18n.language || "en");

  // const imgMap = getCountryImage(opportunity?.country);
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const useAddTofavouriteHook = useAddToFavourite();
  const [isSaved, setIsSaved] = useState(false);

  useEffect(() => {
    const checkIfSaved = async () => {
      if (opportunity?._id) {
        try {
          const response = await axiosGetJson.get(
            `/favourite/is-saved/${opportunity?._id}`
          );

          setIsSaved(response.data);
        } catch (error) {
          if (process.env.NODE_ENV === "dev")
            console.error("Failed to check if article is saved:", error);
        }
      }
    };

    checkIfSaved();
  }, [opportunity?._id]);

  const handleSaveClick = () => {
    if (!user) {
      toast.warning("Login or create account to save opportunity.");
    } else {
      useAddTofavouriteHook.mutate(
        {
          id: opportunity?._id,
          title: opportunity?.versions[language]?.title,
          typeOfFavourite: "opportunity",
        },
        {
          onSuccess: () => {
            setIsSaved(true);
          },
        }
      );
    }
  };

  const handleClick = (link) => {
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: `opportunity_view`,
      button_id: "my_button",
    });
    setTimeout(() => {
      window.location.href = link;
    }, 300);
  };

  return (
    <>
      <GTM />
      <Grid
        key={opportunity?._id}
        className="container opportunity-item"
        container
        spacing={0}
      >
        <Grid item xs={5} sm={3} className="item-image">
          <Link
            href={`/${
              websiteRoutesList.jobLocation.route
            }/${opportunity?.country.toLowerCase()}`}
          >
            <img
              width={"100px"}
              height={"70px"}
              src={`https://flagcdn.com/w320/${countries
                .getAlpha2Code(opportunity?.country.toLowerCase(), "en")
                ?.toLowerCase()}.png`}
              className="map-img"
              alt={`${opportunity?.versions[language]?.title} ${t(
                "opportunities:metaTitleOneOpportunity2"
              )} ${opportunity?.country} (${opportunity?.reference})`}
              loading="lazy"
            />
          </Link>
        </Grid>
        <Grid item xs={7} sm={6} className="item-content">
          <div className="flex-item row">
            <div className="flex-item mobile-col">
              <CustomButton
                link={`/${websiteRoutesList.opportunities.route}/${opportunity?.versions[language]?.url}`}
                text={opportunity?.versions[language]?.title}
                className={"btn p-0 job-title"}
                onClick={handleSaveClick}
              />
              {industryExists(opportunity?.industry) ? (
                <Link
                  style={{ textDecoration: "none" }}
                  href={`/${
                    websiteRoutesList.jobCategory.route
                  }/${findIndustryLink(opportunity?.industry)}`}
                >
                  {" "}
                  <p
                    className={`job-industry border ${findIndustryClassname(
                      opportunity?.industry
                    )}`}
                  >
                    {findIndustryColoredIcon(opportunity?.industry)}{" "}
                    {findIndustryLabel(opportunity?.industry)}
                  </p>
                </Link>
              ) : null}
            </div>
            {isMobile && (!user || user?.roles?.includes(Role.CANDIDATE)) && (
              <CustomButton
                icon={
                  <SvgBookmark
                    className={`${isSaved ? "btn-filled-yellow " : ""}`}
                  />
                }
                className={"btn btn-ghost bookmark"}
              />
            )}
          </div>
          <div className="flex-apply">
            <p className="job-ref">Ref: {opportunity?.reference}</p>
          </div>
          <div className="flex-apply">
            <div className="job-contrat-time">
              <p className="job-contract">
                <SvgFileText />
                {opportunity?.contractType || "Agreement"}
              </p>
              <p className="job-time">
                <SvgTime />{" "}
                {opportunity?.versions[language]?.createdAt
                  ? capitalizeFirstLetter(
                      moment(opportunity?.versions[language]?.createdAt).format(
                        "DD MMMM YYYY"
                      )
                    )
                  : "N/A"}
              </p>
            </div>
          </div>
        </Grid>
        <Grid item xs={12} sm={3} className="item-btns">
          {!isMobile && (!user || user?.roles?.includes(Role.CANDIDATE)) ? (
            <CustomButton
              icon={
                <SvgBookmark
                  className={`${isSaved ? "btn-filled-yellow" : ""}`}
                />
              }
              onClick={isSaved ? handeleDeleteconfirmation : handleSaveClick}
              className={"btn btn-ghost bookmark"}
            />
          ) : (
            <div></div>
          )}
          <CustomButton
            text={t("global:applyNow")}
            className={"btn btn-outlined apply"}
            onClick={() =>
              handleClick(
                `/${websiteRoutesList.opportunities.route}/${opportunity?.versions[language]?.url}`
              )
            }
          />
        </Grid>
      </Grid>
      <DialogModal
        open={showDeleteConfirmation}
        message={t("messages:supprimeropportunityfavoris")}
        onClose={handlecanceldelete}
        onConfirm={handleToggleOpportunity}
      />
    </>
  );
}

export default OpportunityItem;
