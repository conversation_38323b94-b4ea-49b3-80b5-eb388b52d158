"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_locales_en_register_json"],{

/***/ "(app-pages-browser)/./src/locales/en/register.json":
/*!**************************************!*\
  !*** ./src/locales/en/register.json ***!
  \**************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"create":"Create Account","firstName":"First Name","lastName":"Last Name","email":"Email","password":"Password","register":"Register","phoneNumber":"Phone number","country":"Country","industry":"Industry","registerMessage":"Join our platform to connect with us and take the next step in your career","registerWith":"Or register with","confirmPassword":"Confirm Password","message":"You accept our Terms and Conditions and Privacy","haveAnAccount":"Already have an account?","checkemail":"Please check your email to verify your account","validemail":"Email must be valid","emailexist":"This email already exists"}');

/***/ })

}]);