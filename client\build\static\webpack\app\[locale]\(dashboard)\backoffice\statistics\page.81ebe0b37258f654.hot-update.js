"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/components/charts/CustomPieChart.jsx":
/*!**************************************************!*\
  !*** ./src/components/charts/CustomPieChart.jsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_x_charts_PieChart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/x-charts/PieChart */ \"(app-pages-browser)/./node_modules/@mui/x-charts/PieChart/PieChart.js\");\n/* harmony import */ var _assets_images_icons_noDataPie_svg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/assets/images/icons/noDataPie.svg */ \"(app-pages-browser)/./src/assets/images/icons/noDataPie.svg\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst CustomPieChart = (param)=>{\n    let { chart, donuts } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const hasData = chart.dataset?.some((item)=>item[\"value\"] > 0);\n    const hasntData = chart.dataset.length === 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\"\n        },\n        children: hasData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_charts_PieChart__WEBPACK_IMPORTED_MODULE_4__.PieChart, {\n            series: [\n                {\n                    data: chart.dataset,\n                    arcLabel: (item)=>item.value > 0 ? `${t(`statsDash:${item.value}`)}` : \"\",\n                    innerRadius: donuts ? 50 : 90,\n                    outerRadius: donuts ? 90 : 0,\n                    paddingAngle: 1,\n                    cornerRadius: 4,\n                    highlightScope: {\n                        fade: \"global\",\n                        highlight: \"item\"\n                    },\n                    faded: {\n                        innerRadius: donuts ? 30 : 70,\n                        additionalRadius: donuts ? -30 : -20,\n                        color: \"gray\"\n                    }\n                }\n            ],\n            slotProps: {\n                legend: {\n                    hidden: true\n                }\n            },\n            width: 300,\n            height: 200,\n            colors: chart.colors,\n            sx: {\n                marginLeft: \"30%\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\charts\\\\CustomPieChart.jsx\",\n            lineNumber: 13,\n            columnNumber: 9\n        }, undefined) : (!hasData || hasntData) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_noDataPie_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\charts\\\\CustomPieChart.jsx\",\n            lineNumber: 48,\n            columnNumber: 36\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\charts\\\\CustomPieChart.jsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomPieChart, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = CustomPieChart;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CustomPieChart);\nvar _c;\n$RefreshReg$(_c, \"CustomPieChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/charts/CustomPieChart.jsx\n"));

/***/ })

});