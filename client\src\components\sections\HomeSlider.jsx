import blogIMG4 from "../../assets/images/website/banner/HR-Management-Services-Pentabell.webp";
import blogIMG3 from "../../assets/images/website/banner/HR-Services-Pentabell.webp";
import blogIMG3Mobile from "../../assets/images/website/banner/mobile-banner/HR-Services-Pentabell.webp";
import blogIMG4Mobile from "../../assets/images/website/banner/mobile-banner/HR-Management-Services-Pentabell.webp";
import { websiteRoutesList } from "@/helpers/routesList";
import initTranslations from "@/app/i18n";
import CustomEmblaCarousel from "../ui/emblaCarousel/CustomEmblaCarousel";

export default async function HomeSlider({ locale, isMobileSSR }) {
  const { t } = await initTranslations(locale, ["homePage"]);

  const staticSliders = [
    {
      title: t("homePage:banner:banner4"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      img: blogIMG4.src,
      altImg: t("homePage:banner:alt4"),
      imgMobile: blogIMG4Mobile.src,
    },
    {
      title: t("homePage:banner:banner3"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      img: blogIMG3.src,
      altImg: t("homePage:banner:alt3"),
      imgMobile: blogIMG3Mobile.src,
    },
  ];

  let sliders = [];
  let isStatic = false;

  try {
    // Enhanced fetch with timeout and error handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_API_URL}/sliders?visibility=Public&language=${locale}&isArchived=false`,
      {
        signal: controller.signal,
        headers: {
          "Cache-Control": "public, max-age=300", // 5 minutes cache
        },
        next: {
          revalidate: 300, // Revalidate every 5 minutes
        },
      }
    );

    clearTimeout(timeoutId);

    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    const data = await res.json();

    if (data?.sliders && Array.isArray(data.sliders)) {
      const filtered = data.sliders
        .filter(
          (slider) =>
            slider?.existingLanguages?.includes(locale) &&
            slider?.versionslide?.[0]
        )
        .map((slider) => slider.versionslide[0]);

      if (filtered.length === 0) {
        sliders = staticSliders;
        isStatic = true;
      } else {
        sliders = filtered;
      }
    } else {
      sliders = staticSliders;
      isStatic = true;
    }
  } catch (error) {
    console.error("Error fetching sliders:", error);
    sliders = staticSliders;
    isStatic = true;
  }

  // Optimize slides data with performance considerations
  const slides = sliders.map(
    ({ title, link, linkBtn, img, altImg, imgMobile }, index) => ({
      title,
      link,
      linkBtn: linkBtn || t("global:discoverBtn"),
      img: isStatic
        ? img
        : `${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${img}`,
      altImg: altImg || `${title} - Slide ${index + 1}`,
      imgMobile: isStatic
        ? imgMobile
        : `${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${imgMobile}`,
      // Add performance hints
      priority: index === 0, // First image should be prioritized
      loading: index === 0 ? "eager" : "lazy",
    })
  );

  // Enhanced carousel options for performance
  const carouselOptions = {
    loop: true,
    align: "start",
    skipSnaps: false,
    dragFree: false,
    containScroll: "trimSnaps",
    // Performance optimizations
    watchDrag: true,
    watchResize: true,
    watchSlides: true,
  };

  return (
    <CustomEmblaCarousel
      isMobileSSR={isMobileSSR}
      slides={slides}
      options={carouselOptions}
      slideId="home__slider"
    />
  );
}
