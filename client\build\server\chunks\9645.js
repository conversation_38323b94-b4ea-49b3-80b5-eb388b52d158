"use strict";exports.id=9645,exports.ids=[9645],exports.modules={5394:(e,r,t)=>{t.d(r,{Z:()=>k});var l=t(17577),o=t(41135),a=t(88634),s=t(65656),n=t(91703),i=t(30990),d=t(2791),p=t(25609),c=t(54641),u=t(71685),m=t(97898);function b(e){return(0,m.ZP)("MuiFormControlLabel",e)}let h=(0,u.Z)("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]);var g=t(39914),f=t(31121),v=t(10326);let Z=e=>{let{classes:r,disabled:t,labelPlacement:l,error:o,required:s}=e,n={root:["root",t&&"disabled",`labelPlacement${(0,c.Z)(l)}`,o&&"error",s&&"required"],label:["label",t&&"disabled"],asterisk:["asterisk",o&&"error"]};return(0,a.Z)(n,b,r)},P=(0,n.ZP)("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[{[`& .${h.label}`]:r.label},r.root,r[`labelPlacement${(0,c.Z)(t.labelPlacement)}`]]}})((0,i.Z)(({theme:e})=>({display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,[`&.${h.disabled}`]:{cursor:"default"},[`& .${h.label}`]:{[`&.${h.disabled}`]:{color:(e.vars||e).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:({labelPlacement:e})=>"start"===e||"top"===e||"bottom"===e,style:{marginLeft:16}}]}))),x=(0,n.ZP)("span",{name:"MuiFormControlLabel",slot:"Asterisk",overridesResolver:(e,r)=>r.asterisk})((0,i.Z)(({theme:e})=>({[`&.${h.error}`]:{color:(e.vars||e).palette.error.main}}))),k=l.forwardRef(function(e,r){let t=(0,d.i)({props:e,name:"MuiFormControlLabel"}),{checked:a,className:n,componentsProps:i={},control:c,disabled:u,disableTypography:m,inputRef:b,label:h,labelPlacement:k="end",name:y,onChange:R,required:w,slots:F={},slotProps:C={},value:L,...S}=t,B=(0,s.Z)(),$=u??c.props.disabled??B?.disabled,j=w??c.props.required,N={disabled:$,required:j};["checked","name","onChange","value","inputRef"].forEach(e=>{void 0===c.props[e]&&void 0!==t[e]&&(N[e]=t[e])});let z=(0,g.Z)({props:t,muiFormControl:B,states:["error"]}),M={...t,disabled:$,labelPlacement:k,required:j,error:z.error},T=Z(M),q={slots:F,slotProps:{...i,...C}},[E,D]=(0,f.Z)("typography",{elementType:p.default,externalForwardedProps:q,ownerState:M}),I=h;return null==I||I.type===p.default||m||(I=(0,v.jsx)(E,{component:"span",...D,className:(0,o.Z)(T.label,D?.className),children:I})),(0,v.jsxs)(P,{className:(0,o.Z)(T.root,n),ownerState:M,ref:r,...S,children:[l.cloneElement(c,N),j?(0,v.jsxs)("div",{children:[I,(0,v.jsxs)(x,{ownerState:M,"aria-hidden":!0,className:T.asterisk,children:[" ","*"]})]}):I]})})},33662:(e,r,t)=>{t.d(r,{Z:()=>Z});var l=t(17577),o=t(88634),a=t(54641),s=t(27080),n=t(91703),i=t(86102),d=t(65656),p=t(6422),c=t(71685),u=t(97898);function m(e){return(0,u.ZP)("PrivateSwitchBase",e)}(0,c.Z)("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);var b=t(31121),h=t(10326);let g=e=>{let{classes:r,checked:t,disabled:l,edge:s}=e,n={root:["root",t&&"checked",l&&"disabled",s&&`edge${(0,a.Z)(s)}`],input:["input"]};return(0,o.Z)(n,m,r)},f=(0,n.ZP)(p.Z)({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:e,ownerState:r})=>"start"===e&&"small"!==r.size,style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:e,ownerState:r})=>"end"===e&&"small"!==r.size,style:{marginRight:-12}}]}),v=(0,n.ZP)("input",{shouldForwardProp:s.Z})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),Z=l.forwardRef(function(e,r){let{autoFocus:t,checked:l,checkedIcon:o,defaultChecked:a,disabled:s,disableFocusRipple:n=!1,edge:p=!1,icon:c,id:u,inputProps:m,inputRef:Z,name:P,onBlur:x,onChange:k,onFocus:y,readOnly:R,required:w=!1,tabIndex:F,type:C,value:L,slots:S={},slotProps:B={},...$}=e,[j,N]=(0,i.Z)({controlled:l,default:!!a,name:"SwitchBase",state:"checked"}),z=(0,d.Z)(),M=e=>{y&&y(e),z&&z.onFocus&&z.onFocus(e)},T=e=>{x&&x(e),z&&z.onBlur&&z.onBlur(e)},q=e=>{if(e.nativeEvent.defaultPrevented)return;let r=e.target.checked;N(r),k&&k(e,r)},E=s;z&&void 0===E&&(E=z.disabled);let D="checkbox"===C||"radio"===C,I={...e,checked:j,disabled:E,disableFocusRipple:n,edge:p},A=g(I),H={slots:S,slotProps:{input:m,...B}},[W,G]=(0,b.Z)("root",{ref:r,elementType:f,className:A.root,shouldForwardComponentProp:!0,externalForwardedProps:{...H,component:"span",...$},getSlotProps:e=>({...e,onFocus:r=>{e.onFocus?.(r),M(r)},onBlur:r=>{e.onBlur?.(r),T(r)}}),ownerState:I,additionalProps:{centerRipple:!0,focusRipple:!n,disabled:E,role:void 0,tabIndex:null}}),[J,K]=(0,b.Z)("input",{ref:Z,elementType:v,className:A.input,externalForwardedProps:H,getSlotProps:e=>({onChange:r=>{e.onChange?.(r),q(r)}}),ownerState:I,additionalProps:{autoFocus:t,checked:l,defaultChecked:a,disabled:E,id:D?u:void 0,name:P,readOnly:R,required:w,tabIndex:F,type:C,..."checkbox"===C&&void 0===L?{}:{value:L}}});return(0,h.jsxs)(W,{...G,children:[(0,h.jsx)(J,{...K}),j?o:c]})})}};