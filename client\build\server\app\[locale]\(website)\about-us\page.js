(()=>{var e={};e.id=5097,e.ids=[5097],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},83969:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var i,a=t(95746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var i in t)({}).hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e}).apply(null,arguments)}let n=e=>a.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),i||(i=a.createElement("path",{fill:"#234791",d:"m16.172 11-5.364-5.364 1.414-1.414L20 12l-7.778 7.778-1.414-1.414L16.172 13H4v-2z"})))},78732:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>A}),t(87389),t(30962),t(23658),t(54864);var i=t(23191),a=t(88716),r=t(37922),n=t.n(r),l=t(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let A=["",{children:["[locale]",{children:["(website)",{children:["about-us",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,87389)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\about-us\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30962)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\layout.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,23658)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54864)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\not-found.js"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\about-us\\page.jsx"],d="/[locale]/(website)/about-us/page",h={require:t,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/(website)/about-us/page",pathname:"/[locale]/about-us",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:A}})},43612:(e,s,t)=>{Promise.resolve().then(t.bind(t,90423)),Promise.resolve().then(t.bind(t,16027)),Promise.resolve().then(t.bind(t,9782)),Promise.resolve().then(t.bind(t,26887)),Promise.resolve().then(t.bind(t,94705)),Promise.resolve().then(t.bind(t,18054)),Promise.resolve().then(t.bind(t,40162)),Promise.resolve().then(t.bind(t,87893)),Promise.resolve().then(t.bind(t,82639)),Promise.resolve().then(t.bind(t,86682)),Promise.resolve().then(t.bind(t,85127)),Promise.resolve().then(t.bind(t,2851)),Promise.resolve().then(t.bind(t,80918)),Promise.resolve().then(t.bind(t,79189)),Promise.resolve().then(t.bind(t,10827)),Promise.resolve().then(t.bind(t,55854)),Promise.resolve().then(t.bind(t,96314)),Promise.resolve().then(t.bind(t,84791)),Promise.resolve().then(t.bind(t,16190)),Promise.resolve().then(t.bind(t,26668)),Promise.resolve().then(t.bind(t,70719)),Promise.resolve().then(t.bind(t,22522)),Promise.resolve().then(t.bind(t,72150)),Promise.resolve().then(t.bind(t,66871)),Promise.resolve().then(t.bind(t,85260)),Promise.resolve().then(t.bind(t,20964)),Promise.resolve().then(t.bind(t,95487)),Promise.resolve().then(t.bind(t,59505)),Promise.resolve().then(t.bind(t,5728)),Promise.resolve().then(t.bind(t,95053)),Promise.resolve().then(t.bind(t,63572)),Promise.resolve().then(t.bind(t,42021)),Promise.resolve().then(t.bind(t,58536)),Promise.resolve().then(t.bind(t,94599)),Promise.resolve().then(t.bind(t,32923)),Promise.resolve().then(t.bind(t,81957)),Promise.resolve().then(t.bind(t,18990)),Promise.resolve().then(t.bind(t,40166)),Promise.resolve().then(t.bind(t,6690)),Promise.resolve().then(t.bind(t,21447)),Promise.resolve().then(t.bind(t,65449)),Promise.resolve().then(t.bind(t,44790)),Promise.resolve().then(t.bind(t,63123)),Promise.resolve().then(t.bind(t,70722)),Promise.resolve().then(t.bind(t,92072)),Promise.resolve().then(t.bind(t,85155)),Promise.resolve().then(t.bind(t,2246)),Promise.resolve().then(t.bind(t,30805)),Promise.resolve().then(t.bind(t,91732)),Promise.resolve().then(t.bind(t,16554)),Promise.resolve().then(t.bind(t,2931)),Promise.resolve().then(t.bind(t,23597)),Promise.resolve().then(t.bind(t,51607))},65368:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r}),t(17577);var i=t(27522),a=t(10326);let r=(0,i.Z)((0,a.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},2246:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var i=t(10326);t(17577);var a=t(90423),r=t(15082);t(73172);var n=t(52210),l=t(97980);let o=function(){let{t:e}=(0,n.$G)();return i.jsx(a.default,{id:"explore-more-section",className:"custom-max-width",children:(0,i.jsxs)("div",{className:"text-section blue-color text-center",children:[i.jsx("h2",{className:"heading-h1 text-white mb-0",children:e("aboutUs:exploreMore:title")}),i.jsx("p",{className:"sub-heading text-white text-center",children:e("aboutUs:exploreMore:description")}),(0,i.jsxs)("div",{className:"btns",children:[i.jsx(r.default,{text:e("aboutUs:exploreMore:item1"),className:"btn btn-outlined white first-child",link:`/${l.Bi.services.route}`,aHref:!0}),i.jsx(r.default,{text:e("aboutUs:exploreMore:item2"),className:"btn btn-outlined white ",link:`/${l.Bi.opportunities.route}`,aHref:!0})]})]})})}},30805:(e,s,t)=>{"use strict";t.d(s,{default:()=>h});var i=t(10326),a=t(90423),r=t(16027),n=t(15082),l=t(83969);let o={src:"/_next/static/media/offices10.58475b37.png"};var A=t(73172);t(46226);var c=t(52210),d=t(97980);let h=function(){let{t:e}=(0,c.$G)();return(0,i.jsxs)("div",{id:"offices-section",children:[i.jsx(a.default,{id:"our-location-section",className:"custom-max-width",children:(0,i.jsxs)(r.default,{className:"container align-items-center",container:!0,spacing:2,children:[(0,i.jsxs)(r.default,{item:!0,xs:12,sm:6,children:[i.jsx("h2",{className:"heading-h1 text-white",children:e("aboutUs:offices:office:title")}),i.jsx("p",{className:"sub-heading",children:e("aboutUs:offices:office:description")}),i.jsx(n.default,{text:e("aboutUs:offices:office:view"),className:"btn btn-ghost white",icon:i.jsx(l.default,{}),link:`/${d.Bi.contact.route}`})]}),i.jsx(r.default,{item:!0,xs:12,sm:6,children:i.jsx("img",{alt:e("aboutUs:offices:office:altImg"),src:o.src,className:"offices-img",loading:"lazy"})})]})}),i.jsx(a.default,{id:"our-culture-section",className:"custom-max-width",children:(0,i.jsxs)(r.default,{className:"container",container:!0,columnSpacing:2,children:[i.jsx(r.default,{item:!0,xs:12,sm:5,children:(0,i.jsxs)("div",{className:"text-section banking-color",children:[i.jsx("h2",{className:"heading-h1 text-white",children:e("aboutUs:offices:people:title")}),(0,i.jsxs)("p",{className:"sub-heading",children:[e("aboutUs:offices:people:description")," "]}),i.jsx(n.default,{text:e("aboutUs:offices:people:joinUs"),className:"btn btn btn-filled full-width",link:`/${d.Bi.joinUs.route}`})]})}),i.jsx(r.default,{item:!0,xs:12,sm:7,className:"text-section-img",children:i.jsx("img",{alt:e("aboutUs:offices:people:altImg"),src:A.Z.src,loading:"lazy"})})]})})]})}},16554:(e,s,t)=>{"use strict";t.d(s,{default:()=>n});var i=t(10326),a=t(90423),r=t(52210);let n=function(){let{t:e}=(0,r.$G)();return(0,i.jsxs)(a.default,{id:"our-values",className:"custom-max-width",children:[i.jsx("h2",{className:"heading-h1",children:e("aboutUs:values:title")}),(0,i.jsxs)("div",{className:"values",children:[(0,i.jsxs)("div",{className:"value-item banking-insurance",children:[i.jsx("h3",{className:"sub-heading text-white",children:e("aboutUs:values:integrity:title")}),i.jsx("p",{className:"paragraph text-white",children:e("aboutUs:values:integrity:description")})]}),(0,i.jsxs)("div",{className:"value-item it-telecom",children:[i.jsx("h3",{className:"sub-heading text-white",children:e("aboutUs:values:collaboration:title")}),i.jsx("p",{className:"paragraph text-white",children:e("aboutUs:values:collaboration:description")})]}),(0,i.jsxs)("div",{className:"value-item oil-gaz",children:[i.jsx("h3",{className:"sub-heading text-white",children:e("aboutUs:values:innovation:title")}),i.jsx("p",{className:"paragraph text-white",children:e("aboutUs:values:innovation:description")})]}),(0,i.jsxs)("div",{className:"value-item energy",children:[i.jsx("h3",{className:"sub-heading text-white",children:e("aboutUs:values:diversity:title")}),i.jsx("p",{className:"paragraph text-white",children:e("aboutUs:values:diversity:description")})]}),(0,i.jsxs)("div",{className:"value-item transport",children:[i.jsx("h3",{className:"sub-heading text-white",children:e("aboutUs:values:valueCreation:title")}),i.jsx("p",{className:"paragraph text-white",children:e("aboutUs:values:valueCreation:description")})]})]})]})}},2931:(e,s,t)=>{"use strict";t.d(s,{default:()=>l});var i=t(10326),a=t(90423),r=t(16027),n=t(52210);let l=function({locale:e}){let{t:s}=(0,n.$G)();return i.jsx(a.default,{className:"custom-max-width",children:(0,i.jsxs)(r.default,{className:"container",container:!0,spacing:2,children:[i.jsx(r.default,{item:!0,xs:12,sm:12,children:(0,i.jsxs)("h2",{className:"heading-h2",children:[s("homePage:introSection:numbers")," ",i.jsx("br",{}),(0,i.jsxs)("span",{className:"sub-heading update-span",children:[s("homePage:introSection:update")," ",s("homePage:introSection:date")]})]})}),i.jsx(r.default,{item:!0,xs:12,sm:12,className:"numbers",children:(0,i.jsxs)("div",{className:"stats-container-pentabell",children:[(0,i.jsxs)("div",{className:"stat-box purple",children:[i.jsx("span",{className:"corner top-left"}),i.jsx("span",{className:"corner top-right"}),i.jsx("span",{className:"corner bottom-left"}),i.jsx("span",{className:"corner bottom-right"}),i.jsx("p",{className:"numbers-pentabell",children:"15K+"}),i.jsx("p",{children:s("homePage:introSection:hiredExperts")})]}),(0,i.jsxs)("div",{className:"stat-box red",children:[i.jsx("span",{className:"corner top-left"}),i.jsx("span",{className:"corner top-right"}),i.jsx("span",{className:"corner bottom-left"}),i.jsx("span",{className:"corner bottom-right"}),i.jsx("p",{className:"numbers-pentabell",children:"17K+"}),i.jsx("p",{children:s("homePage:introSection:performedMissions")})]}),(0,i.jsxs)("div",{className:"stat-box yellow",children:[i.jsx("span",{className:"corner top-left"}),i.jsx("span",{className:"corner top-right"}),i.jsx("span",{className:"corner bottom-left"}),i.jsx("span",{className:"corner bottom-right"}),i.jsx("p",{className:"numbers-pentabell",children:"61"}),i.jsx("p",{children:s("homePage:introSection:nationalities")})]}),(0,i.jsxs)("div",{className:"stat-box green",children:[i.jsx("span",{className:"corner top-left"}),i.jsx("span",{className:"corner top-right"}),i.jsx("span",{className:"corner bottom-left"}),i.jsx("span",{className:"corner bottom-right"}),i.jsx("p",{className:"numbers-pentabell",children:"187"}),i.jsx("p",{children:s("homePage:introSection:satisfiedClients")})]}),(0,i.jsxs)("div",{className:"stat-box blue",children:[i.jsx("span",{className:"corner top-left"}),i.jsx("span",{className:"corner top-right"}),i.jsx("span",{className:"corner bottom-left"}),i.jsx("span",{className:"corner bottom-right"}),i.jsx("p",{className:"numbers-pentabell",children:"265K+"}),i.jsx("p",{children:s("homePage:introSection:cvs")})]})]})})]})})}},23597:(e,s,t)=>{"use strict";t.d(s,{default:()=>g});var i,a,r,n,l,o=t(10326),A=t(90423),c=t(16027);t(46226);let d={src:"/_next/static/media/purposeImg.2d031f8b.jpg"};var h=t(95746);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var i in t)({}).hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e}).apply(null,arguments)}let m=e=>h.createElement("svg",u({xmlns:"http://www.w3.org/2000/svg",width:42,height:42,fill:"none"},e),i||(i=h.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M1.75 21S8.75 7 21 7s19.25 14 19.25 14-7 14-19.25 14S1.75 21 1.75 21"})),a||(a=h.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 26.25a5.25 5.25 0 1 0 0-10.5 5.25 5.25 0 0 0 0 10.5"})));function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var i in t)({}).hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e}).apply(null,arguments)}let x=e=>h.createElement("svg",p({xmlns:"http://www.w3.org/2000/svg",width:42,height:42,fill:"none"},e),r||(r=h.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 38.5c9.665 0 17.5-7.835 17.5-17.5S30.665 3.5 21 3.5 3.5 11.335 3.5 21 11.335 38.5 21 38.5"})),n||(n=h.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 31.5c5.799 0 10.5-4.701 10.5-10.5S26.799 10.5 21 10.5 10.5 15.201 10.5 21 15.201 31.5 21 31.5"})),l||(l=h.createElement("path",{stroke:"#FFCA00",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 24.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7"})));var b=t(52210);let g=function({locale:e}){let{t:s}=(0,b.$G)();return(0,o.jsxs)(A.default,{className:"purpose-section custom-max-width",id:"what-we-beleive-section",children:[o.jsx("h2",{className:"heading-h1 text-center",children:s("aboutUs:whatwebelieve")}),(0,o.jsxs)(c.default,{className:"container",container:!0,columnSpacing:3,rowSpacing:2,children:[(0,o.jsxs)(c.default,{item:!0,xs:12,sm:6,children:["en"===e&&o.jsx("h3",{className:"heading-h2",children:s("aboutUs:purpose:title")}),o.jsx("p",{className:"sub-heading",children:s("aboutUs:purpose:description")})]}),o.jsx(c.default,{item:!0,xs:12,sm:6,children:o.jsx("img",{alt:s("aboutUs:purpose:altImg"),src:d.src,loading:"lazy"})}),o.jsx(c.default,{item:!0,xs:12,sm:6,children:(0,o.jsxs)("div",{className:"vision-mission-section",children:[o.jsx(m,{}),o.jsx("h3",{className:"heading-h2 text-white",children:s("aboutUs:vision:title")}),o.jsx("p",{className:"sub-heading text-white",children:s("aboutUs:vision:description")})]})}),o.jsx(c.default,{item:!0,xs:12,sm:6,children:(0,o.jsxs)("div",{className:"vision-mission-section",children:[o.jsx(x,{}),o.jsx("h3",{className:"heading-h2 text-white",children:s("aboutUs:mission:title")}),o.jsx("p",{className:"sub-heading text-white",children:s("aboutUs:mission:description")})]})})]})]})}},28868:(e,s,t)=>{"use strict";t.d(s,{ZP:()=>a});var i=t(68570);(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\node_modules\@mui\material\Grid\Grid.js#generateGrid`),(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\node_modules\@mui\material\Grid\Grid.js#generateDirection`),(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\node_modules\@mui\material\Grid\Grid.js#generateRowGap`),(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\node_modules\@mui\material\Grid\Grid.js#generateColumnGap`),(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\node_modules\@mui\material\Grid\Grid.js#resolveSpacingStyles`),(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\node_modules\@mui\material\Grid\Grid.js#resolveSpacingClasses`);let a=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\node_modules\@mui\material\Grid\Grid.js#default`)},87389:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w,generateMetadata:()=>j});var i=t(19510),a=t(94034),r=t(19946),n=t(55920),l=t(28868);let o={src:"/_next/static/media/walid.3b6fec8d.jpg"},A={src:"/_next/static/media/signwalid.aee14a5b.png"};var c=t(68570);let d=(0,c.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\sections\PentabellByNumbers.jsx#default`),h=(0,c.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\sections\WhatWeBeleiveSection.jsx#default`),u=(0,c.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\sections\OurValues.jsx#default`),m=(0,c.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\sections\Offices.jsx#default`),p=(0,c.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\sections\ExploreMore.jsx#default`);var x=t(36435),b=t(43207),g=t(90606),v=t(44957);async function j({params:{locale:e}}){let s=`https://www.pentabell.com/${"en"!==e?`${e}/`:""}${g.Bi.aboutUs.route}/`,t={fr:`https://www.pentabell.com/fr/${g.Bi.aboutUs.route}/`,en:`https://www.pentabell.com/${g.Bi.aboutUs.route}/`,"x-default":`https://www.pentabell.com/${g.Bi.aboutUs.route}/`},{t:i}=await (0,b.Z)(e,["aboutUs","global"]);try{let i=encodeURIComponent(g.Bi.aboutUs.route),a=await v.xk.get(`${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${e}/${i}`);if(a?.data?.status===200)return{title:a?.data?.data?.versions[0]?.metaTitle,description:a?.data?.data?.versions[0]?.metaDescription,robots:a?.data?.data?.robotMeta,alternates:{canonical:s,languages:t}}}catch(e){console.error("Error fetching SEO tags:",e)}return{title:i("aboutUs:metaTitle"),description:i("aboutUs:metaDescription"),alternates:{canonical:s,languages:t},robots:"follow, index, max-snippet:-1, max-image-preview:large"}}let w=async function({params:{locale:e}}){let{t:s}=await (0,b.Z)(e,["aboutUs","global"]);return(0,i.jsxs)("div",{id:"about-us-page",children:[i.jsx(a.Z,{bannerImg:r.Z,height:"100vh",title:s("aboutUs:intro:title"),description:s("aboutUs:intro:description"),altImg:s("aboutUs:intro:altImg")}),(0,i.jsxs)("div",{className:"intro-section",children:[i.jsx(n.Z,{children:(0,i.jsxs)(l.ZP,{className:"container",container:!0,spacing:2,children:[i.jsx(l.ZP,{item:!0,xs:12,sm:6,children:i.jsx("h2",{className:"heading-h1 text-white",children:s("aboutUs:overview:title")})}),(0,i.jsxs)(l.ZP,{item:!0,xs:12,sm:6,children:[i.jsx("p",{className:"sub-heading text-white",children:s("aboutUs:overview:paragraph1")}),i.jsx("p",{className:"sub-heading text-white",children:s("aboutUs:overview:paragraph2")})]})]})}),(0,i.jsxs)(n.Z,{className:"quote-section",children:[(0,i.jsxs)("div",{className:"first-row",children:[i.jsx("img",{alt:s("aboutUs:card:altImg"),src:o.src,loading:"lazy",className:"user-img"}),i.jsx("p",{className:"sub-heading text-yellow text-center",children:"Walid BEN JEMAA"}),(0,i.jsxs)("p",{className:"paragraph text-white text-center",children:[" ",s("aboutUs:card:position")]})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"quote",children:["“",s("aboutUs:card:description"),"”"]}),i.jsx("img",{src:A.src,className:"user-img sign",alt:"Sign Walid",loading:"lazy"})]})]})]}),i.jsx(d,{locale:e}),i.jsx(h,{locale:e}),i.jsx(u,{}),i.jsx(m,{}),i.jsx(p,{}),i.jsx(x.Z,{})]})}},94034:(e,s,t)=>{"use strict";t.d(s,{Z:()=>n});var i=t(19510),a=t(55920),r=t(70064);let n=function({bannerImg:e,height:s,title:t,IconImg:n,description:l,subtitle:o,bottomChildren:A,opportunitysubTitle:c,centerValue:d,altImg:h,topChildren:u,isEvent:m,title2:p,titleHighlight:x,bannerImgDynamic:b,link:g,linkTitle:v,highlights:j}){return(0,i.jsxs)("div",{id:"banner-component",className:!0===d?"center-banner":"",style:{backgroundImage:e?.src?`url(${e.src})`:b?`url(${b})`:"none",height:s||"auto"},children:[h&&i.jsx("img",{width:0,height:0,alt:h,src:"",style:{display:"none"},loading:"lazy"}),(0,i.jsxs)(a.Z,{className:"custom-max-width",children:[u&&u,n&&i.jsx("img",{src:n.src}),m?i.jsx(i.Fragment,{children:j&&j.length>0?(0,i.jsxs)("h1",{className:"heading-h1 text-white",children:[" ",(0,r.q1)(x,j)," "]}):(0,i.jsxs)(i.Fragment,{children:[" ",(0,i.jsxs)("h1",{className:"heading-h1 text-white",children:[(0,i.jsxs)("span",{className:"text-yellow",children:[t," "]}),x,p]}),i.jsx("p",{className:"sub-heading text-slide text-white  ",children:o})]})}):(0,i.jsxs)(i.Fragment,{children:[" ",i.jsx("h1",{className:"heading-h1 text-white",children:t||"Services We Offer"}),i.jsx("p",{className:"sub-heading text-slide text-white  ",children:o}),i.jsx("p",{className:"sub-heading text-slide text-white  ",children:c}),i.jsx("p",{className:"sub-heading text-slide text-white  ",children:l||null}),g&&i.jsx("a",{href:g,style:{textDecoration:"none",width:"fit-content"},className:"btn btn-filled ",children:v})]}),A&&A]})]})}},70722:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});let i={src:"/_next/static/media/signwalid.aee14a5b.png",height:82,width:195,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAADCAMAAACZFr56AAAACVBMVEX/ygD/ywD/wwAFBHwSAAAAA3RSTlMEIxEpZjuRAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAG0lEQVR4nB3GsQ0AAAjAIOz/RxtlAiZo3j3FAgD/ABSU5hR1AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:3}},92072:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});let i={src:"/_next/static/media/walid.3b6fec8d.jpg",height:1728,width:1589,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wgARCAAIAAcDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAH/xAAVAQEBAAAAAAAAAAAAAAAAAAABA//aAAwDAQACEAMQAAABgof/xAAWEAEBAQAAAAAAAAAAAAAAAAAEAxP/2gAIAQEAAQUCOrO//8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAgBAwEBPwF//8QAFhEAAwAAAAAAAAAAAAAAAAAAAAER/9oACAECAQE/AYj/xAAYEAEAAwEAAAAAAAAAAAAAAAABAAIDIf/aAAgBAQAGPwJ0sdSf/8QAFxAAAwEAAAAAAAAAAAAAAAAAAAExEf/aAAgBAQABPyHFipKI/9oADAMBAAIAAwAAABAH/8QAFREBAQAAAAAAAAAAAAAAAAAAAQD/2gAIAQMBAT8QVv/EABURAQEAAAAAAAAAAAAAAAAAAAAB/9oACAECAQE/EIv/xAAZEAEAAgMAAAAAAAAAAAAAAAABABEhgbH/2gAIAQEAAT8QZy5Tb1VrfZ//2Q==",blurWidth:7,blurHeight:8}},19946:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});let i={src:"/_next/static/media/aboutUsBanner.ac89e331.webp",height:738,width:1440,blurDataURL:"data:image/webp;base64,UklGRj4AAABXRUJQVlA4IDIAAACwAQCdASoIAAQAAkA4JQBOgCHRwpUAAP7qAU9LOU+bX0OPe4c22N0n6nyPri5shvAAAA==",blurWidth:8,blurHeight:4}},85155:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});let i={src:"/_next/static/media/aboutUsBanner.ac89e331.webp",height:738,width:1440,blurDataURL:"data:image/webp;base64,UklGRj4AAABXRUJQVlA4IDIAAACwAQCdASoIAAQAAkA4JQBOgCHRwpUAAP7qAU9LOU+bX0OPe4c22N0n6nyPri5shvAAAA==",blurWidth:8,blurHeight:4}},73172:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});let i={src:"/_next/static/media/people.5f68cb3e.jpg",height:360,width:780,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wgARCAAEAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAX/xAAVAQEBAAAAAAAAAAAAAAAAAAAAAf/aAAwDAQACEAMQAAABpiP/xAAXEAEAAwAAAAAAAAAAAAAAAAABAgME/9oACAEBAAEFAmBZl//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQMBAT8Bf//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQIBAT8Bf//EABoQAAAHAAAAAAAAAAAAAAAAAAABAgMSMcH/2gAIAQEABj8CN1Vxwf/EABcQAQEBAQAAAAAAAAAAAAAAAAERACH/2gAIAQEAAT8hv4uUvHP/2gAMAwEAAgADAAAAEAv/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oACAEDAQE/EH//xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oACAECAQE/EH//xAAZEAEAAgMAAAAAAAAAAAAAAAABEUEAITH/2gAIAQEAAT8QKOgAwYbQQTfc/9k=",blurWidth:8,blurHeight:4}}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),i=s.X(0,[8948,1479,1619,1336,4227,8077,6027,3718,4289,1692,9712,8582,481,1812,3969,4903,8031,3195,6345],()=>t(78732));module.exports=i})();