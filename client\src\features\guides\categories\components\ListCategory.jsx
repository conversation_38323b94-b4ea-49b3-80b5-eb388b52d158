"use client";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import Svgedit from "@/assets/images/icons/edit-icon.svg";
import CustomFilters from "@/components/ui/CustomFilters";
import { Grid, InputBase, Select, MenuItem } from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { axiosGetJson } from "@/config/axios";
import { API_URLS } from "@/utils/urls";
import Loading from "@/components/loading/Loading";
import { formatDate } from "@/utils/functions";
import CustomButton from "@/components/ui/CustomButton";
import { useTheme, useMediaQuery } from "@mui/material";
import { adminRoutes, baseUrlBackoffice } from "@/helpers/routesList";

const ListCategory = ({ language }) => {
  const theme = useTheme();

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const router = useRouter();
  const { t } = useTranslation();
  const [action, setAction] = useState("");
  const [pageNumber, setPageNumber] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortOrder, setSortOrder] = useState("desc");
  const [categoriesData, setCategoriesData] = useState([]);
  const [search, setSearch] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(
    language ? language : "en"
  );
  const [loading, setLoading] = useState(false);
  const [paginationModel, setPaginationModel] = React.useState({
    page: 0,
    pageSize: 10,
  });

  const resetSearch = () => {
    setSortOrder("desc");
    setSearchQuery("");
    setSelectedLanguage(language ? language : "en");
    setPageNumber(1);
    setSearch(!search);
  };
  const fetchCategories = async () => {
    setLoading(true);
    try {
      const response = await axiosGetJson.get(`${API_URLS.categoryGuides}`, {
        params: {
          language: selectedLanguage,
          pageSize: paginationModel.pageSize,
          pageNumber: paginationModel.page + 1,
          sortOrder,
          name: searchQuery,
        },
      });
      setCategoriesData(response?.data?.categoriesData);
      setTotalPages(response?.data?.totalCategories);
    } catch (error) {
      toast.error(t("messages:fetchCategoriesFailed"));
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchCategories();
  }, [search, paginationModel]);

  const handleEdit = (id) => {
    if (id) {
      router.push(`edit/${id}`);
    } else {
      toast.error(t("messages:idNotFound"));
    }
  };
  const handleSearchClick = () => {
    setSearch(!search);
  };

  const handleChange = (item, event) => {
    setAction(event.target.value);
    switch (event.target.value) {
      case "edit":
        handleEdit(item.id);
        break;
      default:
        break;
    }
  };
  const columns = [
    {
      field: "name",
      headerName: t("listCategory:name"),
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell",
      flex: 1,

      renderCell: (params) => (
        <a
          href={`/${params.row.language}/blog/category/${params.row?.url}`}
          className="link"
        >
          {params.row.name}
        </a>
      ),
    },
    {
      field: "url",
      headerName: t("listCategory:url"),
      headerClassName: "datagrid-header ",
      cellClassName: "datagrid-cell  ",

      flex: 1,
    },
    {
      field: "createdAt",
      headerName: t("listCategory:dateOfCreation"),
      headerClassName: "datagrid-header ",
      cellClassName: "datagrid-cell  ",

      flex: 1,
    },
    {
      field: "actions",
      headerName: "",
      renderCell: (params) => (
        <Select
          value={action}
          onChange={(e) => handleChange(params.row, e)}
          displayEmpty
          input={<InputBase />}
          style={{ width: "100%" }}
          renderValue={() => t("listArticle:Actions")}
        >
          <MenuItem value="edit">
            <Svgedit style={{ marginRight: 8 }} />
            {t("global:edit")}
          </MenuItem>
        </Select>
      ),
      flex: 1,
    },
  ];

  const rows = categoriesData.map((item) => ({
    id: item._id,
    name: item.categoryguide[0]?.name,
    url: item.categoryguide[0]?.url,
    createdAt: formatDate(item.categoryguide[0]?.createdAt),
    language: item.categoryguide[0]?.language,
  }));

  if (loading) {
    return <Loading />;
  }
  const filters = [
    {
      type: "text",
      label: "Search by title ",
      value: searchQuery,
      onChange: (e) => setSearchQuery(e.target.value),
      placeholder: "Search",
    },
    {
      type: "select",
      label: "Language",
      value: selectedLanguage,
      onChange: (e, val) => setSelectedLanguage(val ? val.toLowerCase() : ""),
      options: ["EN", "FR"],
      condition: true,
    },
    {
      type: "select",
      label: t("global:sort"),
      value: sortOrder
        ? {
            value: sortOrder,
            label: t(sortOrder === "desc" ? "global:newest" : "global:oldest"),
          }
        : null,
      onChange: (e, val) => setSortOrder(val?.value || ""),
      options: [
        { value: "desc", label: t("global:newest") },
        { value: "asc", label: t("global:oldest") },
      ],
      condition: true,
    },
  ];

  return (
    <>
      <div className="display-inline">
        <p className="heading-h2 semi-bold">
          {t("List Category Guide")}{" "}
          <span className="opportunities-nbr">{totalPages}</span>
        </p>

        <CustomButton
          className="btn btn-filled"
          text={t("global:addcategorie")}
          link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.guides.route}/${adminRoutes.categories.route}/${adminRoutes.add.route}/`}
        />
      </div>

      <div id="container" className="recent-application-pentabell">
        <div className={`main-content`}>
          <div className="">
            <Grid container className="flex" spacing={3}>
              <Grid item xs={12}>
                <CustomFilters
                  filters={filters}
                  onSearch={handleSearchClick}
                  onReset={resetSearch}
                  searchLabel={t("global:searchDashboard")}
                />
              </Grid>
              <Grid item xs={12}>
                <div style={{ height: "100%", width: "100%" }}>
                  <DataGrid
                    rows={rows}
                    columns={columns}
                    pagination
                    paginationMode="server"
                    paginationModel={paginationModel}
                    onPaginationModelChange={setPaginationModel}
                    pageSizeOptions={[5, 10, 25]}
                    rowCount={totalPages || [0]}
                    autoHeight
                    className="pentabell-table"
                    disableSelectionOnClick
                    columnVisibilityModel={{
                      createdAt: !isMobile,
                      url: !isMobile,
                    }}
                  />
                </div>
              </Grid>
            </Grid>
          </div>
        </div>
      </div>
    </>
  );
};

export default ListCategory;
