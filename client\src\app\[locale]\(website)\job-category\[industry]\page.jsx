import { axiosGetJsonSSR } from "@/config/axios";
import { redirect } from "next/navigation";

import initTranslations from "@/app/i18n";
import OpportunityCard from "@/features/opportunity/components/opportunityFrontOffice/OpportunityCard";
import BannerComponents from "@/components/sections/BannerComponents";
import bannerOthers from "../../../../../assets/images/industries/banner/bannerOthers.png";
import bannerEnergies from "../../../../../assets/images/industries/banner/bannerEnergies.png";
import energies from "../../../../../assets/images/industries/banner/energies.png";
import transport from "../../../../../assets/images/industries/banner/transport.png";
import ItTelecom from "../../../../../assets/images/industries/banner/ItTelecom.png";
import others from "../../../../../assets/images/industries/banner/others.png";
import banking from "../../../../../assets/images/industries/banner/banking.png";
import bannerBanking from "../../../../../assets/images/industries/banner/bannerBanking.png";
import bannerItTelecom from "../../../../../assets/images/industries/banner/bannerItTelecom.png";
import bannerTransport from "../../../../../assets/images/industries/banner/bannerTransport.png";
import BannerPharma from "../../../../../assets/images/industries/banner/BannerPharma.png";
import pharma from "../../../../../assets/images/industries/banner/pharma.png";

export const getBannerImgByIndustry = (industry) => {
  const industryValue = industry || "";
  switch (industryValue) {
    case "energies":
      return bannerEnergies;
    case "it-telecom":
      return bannerItTelecom;
    case "banking-insurance":
      return bannerBanking;
    case "transport":
      return bannerTransport;
    case "pharmaceutical":
      return BannerPharma;
    default:
      return bannerOthers;
  }
};

export const getBannerSubTitle = (t, industry) => {
  const industryValue = industry || "";
  switch (industryValue) {
    case "energies":
      return t("global:descriptionOpportunityEnergies");
    case "it-telecom":
      return t("global:descriptionOpportunityItTelecom");
    case "banking-insurance":
      return t("global:descriptionOpportunityBanking");
    case "transport":
      return t("global:descriptionOpportunityTransport");
    case "pharmaceutical":
      return t("global:descriptionOpportunityPharma");
    case "other":
      return t("global:descriptionOpportunityOther");
  }
};

export const getBannerIconByIndustry = (industry) => {
  const industryValue = industry || "";
  switch (industryValue) {
    case "energies":
      return energies;
    case "it-telecom":
      return ItTelecom;
    case "banking-insurance":
      return banking;
    case "pharmaceutical":
      return pharma;
    case "transport":
      return transport;
    default:
      return others;
  }
};

export const getIndustryBySlug = (industry) => {
  const industryValue = industry || "";
  switch (industryValue) {
    case "energies":
      return "Energies";
    case "it-telecom":
      return "It & Telecom";
    case "banking-insurance":
      return "Banking";
    case "transport":
      return "Transport";
    case "pharmaceutical":
      return "Pharmaceutical";
    default:
      return "Others";
  }
};

export const getBannerImgByIndustryBySlug = (industry) => {
  const industryValue = industry || "";
  switch (industryValue) {
    case "Energies":
      return bannerEnergies;
    case "It & Telecom":
      return bannerItTelecom;
    case "Banking":
      return bannerBanking;
    case "Transport":
      return bannerTransport;
    case "Pharmaceutical":
      return BannerPharma;
    case "Others":
      return bannerOthers;
  }
};

export const getMetaDescriptionJobCategory = (industry) => {
  const industryValue = industry || "";
  switch (industryValue) {
    case "energies":
      return "global:metaDescriptionJobCategory-energy";
    case "it-telecom":
      return "global:metaDescriptionJobCategory-it-telecom";
    case "banking-insurance":
      return "global:metaDescriptionJobCategory-banking-insurance";
    case "transport":
      return "global:metaDescriptionJobCategory-transport";
    case "Pharmaceutical":
      return "global:metaDescriptionJobCategory-banking-insurance";
    default:
      return "global:metaDescriptionJobCategory";
  }
};

export const getMetaTitleJobCategory = (industry) => {
  const industryValue = industry || "";
  switch (industryValue) {
    case "energies":
      return "global:metaTitleJobCategory-energy";
    case "it-telecom":
      return "global:metaTitleJobCategory-it-telecom";
    case "banking-insurance":
      return "global:metaTitleJobCategory-banking-insurance";
    case "transport":
      return "global:metaTitleJobCategory-transport";
    case "pharmaceutical":
      return "global:metaTitleJobCategory-transport";
    default:
      return "global:metaTitleJobCategory";
  }
};

export async function generateMetadata({ params: { locale, industry } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }job-category/${industry}/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/job-category/${industry}/`,
    en: `https://www.pentabell.com/job-category/${industry}/`,
    "x-default": `https://www.pentabell.com/job-category/${industry}/`,
  };

  const { t } = await initTranslations(locale, ["aboutUs", "global"]);

  return {
    title: t(getMetaTitleJobCategory(industry)),
    description: t(getMetaDescriptionJobCategory(industry)),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
  };
}

const Opportunities = async ({ searchParams, params }) => {
  const { t } = await initTranslations(params.locale, [
    "opportunities",
    "global",
  ]);

  const industriesSlug = [
    "energies",
    "it-telecom",
    "banking-insurance",
    "transport",
    "other",
    "pharmaceutical",
  ];

  if (!industriesSlug.includes(params.industry)) {
    redirect(
      params.locale === "en"
        ? `/job-category/other/`
        : `/${params.locale}/job-category/other/`
    );
  }

  const countryResponse = await axiosGetJsonSSR.get("/countries");
  const countries = countryResponse.data || [];
  const res = await axiosGetJsonSSR.get("/opportunities", {
    params: {
      pageSize: 10,
      pageNumber: searchParams.pageNumber,
      industry: getIndustryBySlug(params.industry),
      country: searchParams.country || "",
      keyWord: searchParams.keyWord || "",
      language: params.locale,
      jobDescriptionLanguages: searchParams.jobDescriptionLanguages,
      contractType: searchParams.contractType || "",
      levelOfExperience: searchParams.levelOfExperience || "",
      exclude: "true",
    },
  });

  const initialOpportunities = res.data;

  return (
    <div id="opportunities-page">
      <BannerComponents
        bannerImg={getBannerImgByIndustry(params.industry)}
        subtitle={getBannerSubTitle(t, params.industry)}
        opportunitysubTitle={t("global:opportunitysubTitle")}
        IconImg={getBannerIconByIndustry(params.industry)}
        height={"70vh"}
        title={t("global:titleOpportunity")}
      />
      <OpportunityCard
        jobIndustry={true}
        industrySlug={params.industry}
        industryName={getIndustryBySlug(params.industry)}
        language={params.locale}
        countries={countries}
        initialOpportunities={initialOpportunities}
        searchParams={searchParams}
        typeCategory
      />
    </div>
  );
};
export default Opportunities;
