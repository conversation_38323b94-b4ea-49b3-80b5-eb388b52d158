/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./node_modules/react-international-phone/dist/index.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
.react-international-phone-country-selector{position:relative}.react-international-phone-country-selector-button{display:flex;height:var(--react-international-phone-height, 36px);box-sizing:border-box;align-items:center;justify-content:center;padding:0;border:1px solid var(--react-international-phone-country-selector-border-color, var(--react-international-phone-border-color, gainsboro));margin:0;appearance:button;-webkit-appearance:button;background-color:var(--react-international-phone-country-selector-background-color, var(--react-international-phone-background-color, white));cursor:pointer;text-transform:none;-webkit-user-select:none;user-select:none}.react-international-phone-country-selector-button:hover{background-color:var(--react-international-phone-country-selector-background-color-hover, whitesmoke)}.react-international-phone-country-selector-button--hide-dropdown{cursor:auto}.react-international-phone-country-selector-button--hide-dropdown:hover{background-color:transparent}.react-international-phone-country-selector-button__button-content{display:flex;align-items:center;justify-content:center}.react-international-phone-country-selector-button__flag-emoji{margin:0 4px}.react-international-phone-country-selector-button__flag-emoji--disabled{opacity:.75}.react-international-phone-country-selector-button__dropdown-arrow{border-top:var(--react-international-phone-country-selector-arrow-size, 4px) solid var(--react-international-phone-country-selector-arrow-color, #777);border-right:var(--react-international-phone-country-selector-arrow-size, 4px) solid transparent;border-left:var(--react-international-phone-country-selector-arrow-size, 4px) solid transparent;margin-right:4px;transition:all .1s ease-out}.react-international-phone-country-selector-button__dropdown-arrow--active{transform:rotateX(180deg)}.react-international-phone-country-selector-button__dropdown-arrow--disabled{border-top-color:var(--react-international-phone-disabled-country-selector-arrow-color, #999)}.react-international-phone-country-selector-button--disabled{background-color:var(--react-international-phone-disabled-country-selector-background-color, var(--react-international-phone-disabled-background-color, whitesmoke));cursor:auto}.react-international-phone-country-selector-button--disabled:hover{background-color:var(--react-international-phone-disabled-country-selector-background-color, var(--react-international-phone-disabled-background-color, whitesmoke))}.react-international-phone-flag-emoji{width:var(--react-international-phone-flag-width, 24px);height:var(--react-international-phone-flag-height, 24px);box-sizing:border-box}.react-international-phone-country-selector-dropdown{position:absolute;z-index:1;top:var(--react-international-phone-dropdown-top, 44px);left:var(--react-international-phone-dropdown-left, 0);display:flex;width:300px;max-height:200px;flex-direction:column;padding:4px 0;margin:0;background-color:var(--react-international-phone-dropdown-item-background-color, var(--react-international-phone-background-color, white));box-shadow:var(--react-international-phone-dropdown-shadow, 2px 2px 16px rgba(0, 0, 0, .25));color:var(--react-international-phone-dropdown-item-text-color, var(--react-international-phone-text-color, #222));list-style:none;overflow-y:scroll}.react-international-phone-country-selector-dropdown__preferred-list-divider{height:1px;border:none;margin:var(--react-international-phone-dropdown-preferred-list-divider-margin, 0);background:var(--react-international-phone-dropdown-preferred-list-divider-color, var(--react-international-phone-border-color, gainsboro))}.react-international-phone-country-selector-dropdown__list-item{display:flex;min-height:var(--react-international-phone-dropdown-item-height, 28px);box-sizing:border-box;align-items:center;padding:2px 8px}.react-international-phone-country-selector-dropdown__list-item-flag-emoji{margin-right:8px}.react-international-phone-country-selector-dropdown__list-item-country-name{overflow:hidden;margin-right:8px;font-size:var(--react-international-phone-dropdown-item-font-size, 14px);text-overflow:ellipsis;white-space:nowrap}.react-international-phone-country-selector-dropdown__list-item-dial-code{color:var(--react-international-phone-dropdown-item-dial-code-color, gray);font-size:var(--react-international-phone-dropdown-item-font-size, 14px)}.react-international-phone-country-selector-dropdown__list-item:hover{background-color:var(--react-international-phone-selected-dropdown-item-background-color, var(--react-international-phone-selected-dropdown-item-background-color, whitesmoke));cursor:pointer}.react-international-phone-country-selector-dropdown__list-item--selected,.react-international-phone-country-selector-dropdown__list-item--focused{background-color:var(--react-international-phone-selected-dropdown-item-background-color, whitesmoke);color:var(--react-international-phone-selected-dropdown-item-text-color, var(--react-international-phone-text-color, #222))}.react-international-phone-country-selector-dropdown__list-item--selected .react-international-phone-country-selector-dropdown__list-item-dial-code,.react-international-phone-country-selector-dropdown__list-item--focused .react-international-phone-country-selector-dropdown__list-item-dial-code{color:var(--react-international-phone-selected-dropdown-item-dial-code-color, var(--react-international-phone-dropdown-item-dial-code-color, gray))}.react-international-phone-country-selector-dropdown__list-item--focused{background-color:var(--react-international-phone-selected-dropdown-item-background-color, var(--react-international-phone-selected-dropdown-item-background-color, whitesmoke))}.react-international-phone-dial-code-preview{display:flex;align-items:center;justify-content:center;padding:0 8px;border:1px solid var(--react-international-phone-dial-code-preview-border-color, var(--react-international-phone-border-color, gainsboro));margin-right:-1px;background-color:var(--react-international-phone-dial-code-preview-background-color, var(--react-international-phone-background-color, white));color:var(--react-international-phone-dial-code-preview-text-color, var(--react-international-phone-text-color, #222));font-size:var(--react-international-phone-dial-code-preview-font-size, var(--react-international-phone-font-size, 13px))}.react-international-phone-dial-code-preview--disabled{background-color:var(--react-international-phone-dial-code-preview-disabled-background-color, var(--react-international-phone-disabled-background-color, whitesmoke));color:var(--react-international-phone-dial-code-preview-disabled-text-color, var(--react-international-phone-disabled-text-color, #666))}.react-international-phone-input-container{display:flex}.react-international-phone-input-container .react-international-phone-country-selector-button{border-radius:var(--react-international-phone-border-radius, 4px);margin-right:-1px;border-bottom-right-radius:0;border-top-right-radius:0}.react-international-phone-input-container .react-international-phone-input{overflow:visible;height:var(--react-international-phone-height, 36px);box-sizing:border-box;padding:0 8px;border:1px solid var(--react-international-phone-border-color, gainsboro);border-radius:var(--react-international-phone-border-radius, 4px);margin:0;background-color:var(--react-international-phone-background-color, white);border-bottom-left-radius:0;border-top-left-radius:0;color:var(--react-international-phone-text-color, #222);font-family:inherit;font-size:var(--react-international-phone-font-size, 13px)}.react-international-phone-input-container .react-international-phone-input:focus{outline:none}.react-international-phone-input-container .react-international-phone-input--disabled{background-color:var(--react-international-phone-disabled-background-color, whitesmoke);color:var(--react-international-phone-disabled-text-color, #666)}

/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/styles/optimized-carousel.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/* Optimized Carousel Styles - Performance & Layout Shift Prevention */

/* Container optimizations */
.embla {
  position: relative;
  overflow: hidden;
  /* Hardware acceleration for smooth animations */
  transform: translateZ(0);
  will-change: transform;
}

.embla__viewport {
  overflow: hidden;
  /* Prevent layout shifts during initialization */
  min-height: inherit;
}

.embla__container {
  display: flex;
  /* Smooth transitions */
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Hardware acceleration */
  transform: translateZ(0);
  will-change: transform;
}

.embla__slide {
  position: relative;
  flex: 0 0 100%;
  min-width: 0;
  /* Prevent layout shifts */
  aspect-ratio: 21/9;
}

/* Mobile responsive aspect ratio */
@media (max-width: 600px) {
  .embla__slide {
    aspect-ratio: 16/9;
  }
}

/* Image container optimizations */
.carousel-image-container {
  /* Prevent layout shifts with consistent sizing */
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
  overflow: hidden !important;
  
  /* Smooth loading transitions */
  background-color: #f5f5f5;
  background-image: linear-gradient(
    90deg,
    #f5f5f5 0%,
    #e8e8e8 50%,
    #f5f5f5 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Shimmer loading animation */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Image optimizations */
.carousel-image-container img {
  /* Prevent layout shifts */
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  object-position: center !important;
  
  /* Smooth loading */
  transition: opacity 0.3s ease-in-out !important;
  
  /* Hardware acceleration */
  transform: translateZ(0);
  will-change: opacity;
}

/* Content overlay optimizations */
.slide__container {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 2 !important;
  
  /* Flexbox for content positioning */
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  
  /* Prevent text selection during transitions */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.embla__slide__content {
  /* Smooth content transitions */
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
  transform: translateZ(0);
  will-change: opacity, transform;
  
  /* Content positioning */
  max-width: 600px;
  padding: 2rem;
  
  /* Text styling */
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Title optimizations */
.embla__slide__title {
  /* Prevent layout shifts */
  margin: 0 0 1.5rem 0 !important;
  padding: 0 !important;
  
  /* Typography */
  font-size: clamp(1.5rem, 4vw, 3rem) !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  
  /* Performance */
  contain: layout style;
  will-change: auto;
}

/* Button optimizations */
.btn-slider,
.explore-btn {
  /* Smooth interactions */
  transition: all 0.2s ease-in-out !important;
  transform: translateZ(0);
  will-change: transform, background-color;
  
  /* Prevent layout shifts */
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  
  /* Interactive states */
  cursor: pointer;
}

.btn-slider:hover,
.explore-btn:hover {
  transform: translateY(-2px) translateZ(0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-slider:active,
.explore-btn:active {
  transform: translateY(0) translateZ(0);
}

/* Controls optimizations */
.embla__controls {
  position: absolute !important;
  bottom: 2rem !important;
  right: 2rem !important;
  z-index: 3 !important;
}

.embla__buttons {
  display: flex !important;
  gap: 0.5rem !important;
}

.embla__button {
  /* Button styling */
  width: 48px !important;
  height: 48px !important;
  border-radius: 50% !important;
  border: none !important;
  background: rgba(255, 255, 255, 0.9) !important;
  color: #333 !important;
  
  /* Smooth interactions */
  transition: all 0.2s ease-in-out !important;
  transform: translateZ(0);
  will-change: transform, background-color;
  
  /* Flexbox centering */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  
  /* Interactive */
  cursor: pointer;
}

.embla__button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 1) !important;
  transform: scale(1.05) translateZ(0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.embla__button:active:not(:disabled) {
  transform: scale(0.95) translateZ(0);
}

.embla__button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Skeleton loading optimizations */
.MuiSkeleton-root {
  /* Smooth skeleton animations */
  transform: translateZ(0);
  will-change: transform;
}

/* Mobile optimizations */
@media (max-width: 600px) {
  .embla__slide__content {
    padding: 1rem;
    max-width: 100%;
  }
  
  .embla__slide__title {
    font-size: clamp(1.25rem, 6vw, 2rem) !important;
    margin-bottom: 1rem !important;
  }
  
  .embla__controls {
    bottom: 1rem !important;
    right: 1rem !important;
  }
  
  .embla__button {
    width: 40px !important;
    height: 40px !important;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .embla__container,
  .carousel-image-container img,
  .embla__slide__content,
  .btn-slider,
  .explore-btn,
  .embla__button {
    transition: none !important;
    animation: none !important;
  }
  
  .carousel-image-container {
    animation: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .embla__slide__content {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  }
  
  .embla__button {
    border: 2px solid #333 !important;
  }
}

/* Focus management for accessibility */
.embla__button:focus-visible {
  outline: 2px solid #007bff !important;
  outline-offset: 2px !important;
}

/* Performance optimizations for older browsers */
.embla,
.embla__viewport,
.embla__container,
.embla__slide {
  /* Fallback for browsers without will-change support */
  transform: translateZ(0);
}

/* Print styles */
@media print {
  .embla__controls,
  .embla__button {
    display: none !important;
  }
  
  .embla__slide {
    page-break-inside: avoid;
    -moz-column-break-inside: avoid;
         break-inside: avoid;
  }
}

