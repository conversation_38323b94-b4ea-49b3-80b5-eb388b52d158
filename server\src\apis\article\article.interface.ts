import { Language, Visibility, robotsMeta } from '@/utils/helpers/constants';
import mongoose from 'mongoose';
export interface ArticleVersion {
    _id: mongoose.Types.ObjectId;
    language: Language;
    title: string;
    image: string;
    keywords: string[];
    metaTitle: string;
    metaDescription: string;
    url: string;
    category: mongoose.Types.ObjectId[];
    alt?: string;
    visibility: Visibility;
    publishDate: Date;
    shareOnSocialMedia: boolean;
    isArchived: boolean;
    content: string;
    canonical: string;
    createdAt: Date;
    updatedAt: Date;
    highlights: string[];
    description: string;
    createdBy: mongoose.Types.ObjectId;
    updatedBy: mongoose.Types.ObjectId;
}

export interface ArticleI {
    _id: string;
    versions: {
        [language: string]: ArticleVersion;
    };
    totalCommentaires: number;
    tags: string[];
    robotsMeta: robotsMeta;
    createdAt: Date;
    createdBy: mongoose.Types.ObjectId;
    updatedAt: Date;
}

export { Language, Visibility, robotsMeta };
