(()=>{var A={};A.id=6371,A.ids=[6371],A.modules={72934:A=>{"use strict";A.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:A=>{"use strict";A.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:A=>{"use strict";A.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:A=>{"use strict";A.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:A=>{"use strict";A.exports=require("assert")},78893:A=>{"use strict";A.exports=require("buffer")},61282:A=>{"use strict";A.exports=require("child_process")},84770:A=>{"use strict";A.exports=require("crypto")},17702:A=>{"use strict";A.exports=require("events")},92048:A=>{"use strict";A.exports=require("fs")},32615:A=>{"use strict";A.exports=require("http")},35240:A=>{"use strict";A.exports=require("https")},98216:A=>{"use strict";A.exports=require("net")},19801:A=>{"use strict";A.exports=require("os")},55315:A=>{"use strict";A.exports=require("path")},76162:A=>{"use strict";A.exports=require("stream")},82452:A=>{"use strict";A.exports=require("tls")},74175:A=>{"use strict";A.exports=require("tty")},17360:A=>{"use strict";A.exports=require("url")},21764:A=>{"use strict";A.exports=require("util")},71568:A=>{"use strict";A.exports=require("zlib")},5551:(A,e,t)=>{"use strict";t.d(e,{Z:()=>n});var a,l=t(95746);function i(){return(i=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let n=A=>l.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:40,height:41,fill:"none"},A),a||(a=l.createElement("path",{fill:"#fff",d:"m18.047 20.501 8.25 8.25-2.357 2.357L13.333 20.5 23.94 9.895l2.357 2.357z"})))},58996:(A,e,t)=>{"use strict";t.d(e,{Z:()=>n});var a,l=t(95746);function i(){return(i=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let n=A=>l.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:40,height:41,fill:"none"},A),a||(a=l.createElement("path",{fill:"#fff",d:"m21.953 20.499-8.25-8.25 2.357-2.357L26.667 20.5 16.06 31.106l-2.357-2.358z"})))},31487:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>s});var a,l,i=t(95746);function n(){return(n=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let s=A=>i.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:25,height:30,fill:"none"},A),a||(a=i.createElement("path",{fill:"#E5F0FC",d:"M11.868 4.942c.624.346 2.643.59 3.79.704q.053.005.105.005c.352 0 .686-.173.89-.462a1.06 1.06 0 0 0 .087-1.08c-.461-.95-1.62-2.035-4.538-1.804a.97.97 0 0 0-.862.739c-.22.912-.032 1.587.528 1.898m.252-1.71a.16.16 0 0 1 .143-.126c1.95-.156 3.25.314 3.75 1.352a.27.27 0 0 1-.022.27.28.28 0 0 1-.258.12c-1.765-.176-3.13-.413-3.478-.607-.299-.167-.194-.763-.135-1.01M18.964 16.91a2.327 2.327 0 0 0-4.651 0 2.33 2.33 0 0 0 2.325 2.325 2.33 2.33 0 0 0 2.326-2.326m-3.845 0c0-.84.683-1.523 1.523-1.523.838 0 1.522.683 1.522 1.522s-.683 1.523-1.522 1.523a1.53 1.53 0 0 1-1.523-1.523"})),l||(l=i.createElement("path",{fill:"#E5F0FC",d:"M18.507 26.278c1.122-1.622 2.009-3.1 2.654-4.425l.182-.182A12.42 12.42 0 0 0 25 12.84C25 5.95 19.392.343 12.5.343c-1.484 0-2.942.258-4.335.77a12.5 12.5 0 0 0-5.87 4.509A12.4 12.4 0 0 0 0 12.843c0 1.745.352 3.426 1.041 5.004q.004.007.006.015a12.5 12.5 0 0 0 3.127 4.303 12.48 12.48 0 0 0 9.672 3.106q.325.497.683 1.011c-1.393.15-3.745.578-3.745 1.75 0 1.277 2.974 1.857 5.734 1.857 2.763 0 5.734-.581 5.734-1.856 0-1.177-2.352-1.604-3.745-1.754m-7.116-8.877a5.13 5.13 0 0 1 3.54-4.877 5.1 5.1 0 0 1 2.147-.218 5.135 5.135 0 0 1 4.563 5.095c0 1.006-.358 2.276-1.05 3.771q-.001.003-.003.006 0 .003-.003.005c-.79 1.698-2.016 3.687-3.65 5.91a.524.524 0 0 1-.846 0c-3.07-4.19-4.698-7.54-4.698-9.693m10.702 2.13q.354-1.18.355-2.13a5.936 5.936 0 0 0-4.783-5.815 1.78 1.78 0 0 0-.116-1.649c-.004-.003-.004-.01-.006-.011l-.01-.015a.78.78 0 0 1-.035-.754.8.8 0 0 1 .63-.444c1.35-.152 3.07-.017 3.613 1.497.41 1.146 1.253 1.865 2.44 2.09q.015.27.015.543a11.62 11.62 0 0 1-2.103 6.688M7.63 2.206c-.083.634.1 1.431.88 2.364 1.267 1.517 1.81 2.955 1.53 4.05-.22.865-.95 1.545-2.162 2.023-.038.015-.184.043-.31.07-.822.173-2.534.534-3.176 1.525a1.56 1.56 0 0 0-.214 1.214c.111.517.297 1.822-.417 2.766-.434.575-1.138.917-2.09 1.03a11.6 11.6 0 0 1-.866-4.411c0-4.571 2.71-8.74 6.825-10.63M2.012 18.02c1.056-.17 1.859-.61 2.39-1.314.911-1.211.695-2.796.56-3.42a.75.75 0 0 1 .103-.607c.46-.71 2.006-1.038 2.666-1.177.235-.05.351-.077.436-.109 1.47-.577 2.361-1.443 2.649-2.572.35-1.37-.235-3.017-1.692-4.762-.704-.844-.884-1.578-.546-2.235a11.7 11.7 0 0 1 3.922-.675c5.98 0 10.925 4.514 11.611 10.312-.792-.21-1.323-.71-1.613-1.525-.564-1.566-2.144-2.284-4.461-2.02a1.6 1.6 0 0 0-1.262.885 1.58 1.58 0 0 0 .074 1.53l.011.02q.15.239.15.518a.98.98 0 0 1-.22.609 5.932 5.932 0 0 0-6.204 5.922c0 1.011.303 2.217.898 3.613-.69.126-1.267.124-1.79-.017q-.053-.027-.105-.058c-1.256-.702-2.965-.546-5.077.449a11.9 11.9 0 0 1-2.5-3.367M12.5 24.536c-2.672 0-5.244-.912-7.317-2.575 1.71-.745 3.059-.857 4.015-.326q.087.048.173.094a.4.4 0 0 0 .07.027c.704.199 1.47.199 2.388.005q.611 1.276 1.528 2.743-.427.031-.857.032m7.681 4.139c-.973.264-2.276.41-3.666.41s-2.692-.146-3.666-.41c-1.047-.284-1.267-.592-1.267-.643 0-.158.874-.795 3.476-.994q.19.263.387.534c.252.344.643.543 1.07.543.429 0 .816-.197 1.07-.543q.199-.27.388-.534c2.598.2 3.476.836 3.476.994 0 .047-.22.355-1.268.643"})))},59788:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>n});var a,l=t(95746);function i(){return(i=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let n=A=>l.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:25,height:35,fill:"none"},A),a||(a=l.createElement("path",{fill:"#FAFCFF",d:"M12.503 0C6.405 0 1.448 4.997 1.448 11.145c0 1.629.696 3.565 1.699 5.624.942 1.935 2.17 3.963 3.412 5.846H2.934a.544.544 0 0 0-.528.432L.012 34.338a.55.55 0 0 0 .108.458.54.54 0 0 0 .42.204h23.92a.54.54 0 0 0 .42-.204.55.55 0 0 0 .108-.458l-2.394-11.291a.544.544 0 0 0-.528-.433h-3.624c1.243-1.88 2.47-3.91 3.413-5.846 1.003-2.059 1.698-3.994 1.698-5.623C23.553 4.997 18.601 0 12.503 0m0 1.094c5.513 0 9.968 4.495 9.968 10.051 0 1.298-.619 3.155-1.588 5.144-.968 1.989-2.271 4.122-3.576 6.075-.107.162-.214.317-.32.475-2.26 3.337-4.132 5.62-4.484 6.054-.352-.432-2.228-2.716-4.488-6.054l-.32-.475c-1.305-1.953-2.61-4.086-3.577-6.075-.968-1.99-1.587-3.845-1.587-5.144 0-5.556 4.459-10.051 9.972-10.051m0 4.44c-3.066 0-5.567 2.519-5.567 5.611 0 3.093 2.5 5.608 5.567 5.608 3.066 0 5.563-2.514 5.563-5.608 0-3.092-2.497-5.61-5.563-5.61m0 1.094c2.482 0 4.48 2.017 4.48 4.517s-1.998 4.518-4.48 4.518-4.485-2.017-4.485-4.518 2.002-4.517 4.485-4.517m-9.13 17.08H7.29a103 103 0 0 0 4.792 6.448h-.001c.103.126.257.2.418.2a.54.54 0 0 0 .418-.2s2.322-2.831 4.792-6.447h3.917l2.163 10.198H1.211z"})))},71557:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>n});var a,l=t(95746);function i(){return(i=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let n=A=>l.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:25,height:26,fill:"none"},A),a||(a=l.createElement("path",{fill:"#fff",d:"M1.552 13.441c0-.2.153-.363.341-.363H6.72c.188 0 .341.163.341.363s-.153.362-.341.362H1.893c-.188 0-.341-.162-.341-.362m.341 2.227H6.72c.188 0 .341-.162.341-.362s-.153-.363-.341-.363H1.893c-.188 0-.341.162-.341.363s.153.362.341.362m0 1.865H6.72c.188 0 .341-.163.341-.363s-.153-.362-.341-.362H1.893c-.188 0-.341.162-.341.362s.153.363.341.363m0 1.862H6.72c.188 0 .341-.162.341-.363s-.153-.362-.341-.362H1.893c-.188 0-.341.162-.341.362s.153.363.341.363m0 1.866H6.72c.188 0 .341-.162.341-.363s-.153-.362-.341-.362H1.893c-.188 0-.341.162-.341.362s.153.363.341.363m0 1.863H6.72c.188 0 .341-.162.341-.362s-.153-.363-.341-.363H1.893c-.188 0-.341.162-.341.363s.153.362.341.362M25 21.677v3.261c0 .2-.153.363-.342.363H.342c-.189 0-.342-.162-.342-.363v-14.24c0-.14.077-.27.197-.328l5.631-2.789V5.912c0-.118.055-.23.146-.298L13.098.366a.33.33 0 0 1 .475.09l3.462 5.247c.04.06.061.133.061.208v1.086l1.121-.555a.33.33 0 0 1 .381.067l3.852 3.927a.37.37 0 0 1 .105.261v5.08c.512.443.84 1.116.84 1.87 0 .657-.25 1.254-.652 1.69h.052c1.215.002 2.205 1.05 2.205 2.34m-2.289-4.03c0-.93-.715-1.688-1.592-1.688-.88 0-1.593.758-1.593 1.689s.715 1.69 1.593 1.69 1.592-.758 1.592-1.69M18.703 7.622v11.853a2.1 2.1 0 0 1 .739-.136h.052a2.5 2.5 0 0 1-.652-1.69c0-1.33 1.021-2.414 2.276-2.414.264 0 .518.049.754.136v-4.517zm-1.607.175v12.058c.189.162.353.356.483.574.12-.202.27-.383.44-.537V7.34zm-3.461-5.981v13.458a2 2 0 0 1 .403-.039c1.256 0 2.277 1.083 2.277 2.414 0 .657-.25 1.255-.652 1.69h.053q.366.002.697.12V6.025zm.403 17.524c.878 0 1.593-.759 1.593-1.69s-.715-1.689-1.593-1.689c-.877 0-1.591.758-1.591 1.69 0 .93.715 1.689 1.591 1.689m-6.11-12L.685 10.93.684 24.575h7.243zm.579-.83 3.852 3.927a.37.37 0 0 1 .105.26v5.212c.145-.149.31-.277.487-.38V1.357L6.511 6.1v1.14l1.614-.8a.33.33 0 0 1 .382.067m.105 18.067h1.546v-2.899c0-1.29.99-2.338 2.205-2.338h.052a2.49 2.49 0 0 1-.635-1.985v-6.502l-3.168-3.23zm3.51 0h5.115v-2.899a1.64 1.64 0 0 0-.69-1.35 1.45 1.45 0 0 0-.831-.263h-3.353c-.838 0-1.521.724-1.521 1.613v2.899zm12.194-2.899c0-.89-.682-1.613-1.521-1.613h-3.353c-.327 0-.63.11-.878.297l-.016.012c-.38.294-.627.77-.627 1.304v2.899h6.394z"})))},97397:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>n});var a,l=t(95746);function i(){return(i=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let n=A=>l.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:26,height:30,fill:"none"},A),a||(a=l.createElement("path",{fill:"#E5F0FC",d:"M24.874 28.356h-.1V16.765l-1.992-.394v-1.494a.472.472 0 1 0-.945 0v1.308l-4.099-.81V7.458h-1.345V5.123h-1.27V2.788H12.67V.473a.472.472 0 1 0-.946 0v2.315H9.27v2.335H8v2.335H6.654v2.665H5.19v-.96a.472.472 0 1 0-.945 0v.96h-1.43v-1.68a.472.472 0 1 0-.946 0v1.68H.498v18.233H.473a.472.472 0 1 0 0 .945H24.87a.472.472 0 1 0 0-.945zm-1.046 0h-8.516V15.86l1.48.293.946.186 4.477.882 1.613.319zM10.218 3.734h3.96v1.389h-3.96zm-1.27 2.334h6.5v1.39h-6.5zm.397 22.288H1.446v-2.7h7.899zM1.446 11.068h7.899v2.7H1.446zm0 3.646h7.899v2.7H1.446zm7.587 9.993H1.446v-2.7h7.899v2.7zM1.446 18.36h7.899v2.7H1.446zm8.844 9.994V10.12H7.603V8.4h9.193v6.786l-2.426-.479v13.646h-4.08M21.664 24.6v1.415a.472.472 0 1 1-.945 0V24.6a.472.472 0 1 1 .945 0m-3.24 0v1.415a.47.47 0 0 1-.472.472.44.44 0 0 1-.21-.053.47.47 0 0 1-.262-.42V24.6c0-.186.107-.343.261-.419a.5.5 0 0 1 .211-.053c.262 0 .473.21.473.472m3.24-5.265v1.415a.472.472 0 1 1-.945 0v-1.415a.472.472 0 1 1 .945 0m-3.24 0v1.415a.47.47 0 0 1-.472.473.44.44 0 0 1-.21-.054.47.47 0 0 1-.262-.419v-1.415c0-.185.107-.343.261-.419a.5.5 0 0 1 .211-.053c.262 0 .473.211.473.473"})))},15814:(A,e,t)=>{"use strict";t.r(e),t.d(e,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>h,pages:()=>g,routeModule:()=>d,tree:()=>c}),t(83694),t(30962),t(23658),t(54864);var a=t(23191),l=t(88716),i=t(37922),n=t.n(i),s=t(95231),r={};for(let A in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(A)&&(r[A]=()=>s[A]);t.d(e,r);let c=["",{children:["[locale]",{children:["(website)",{children:["events",{children:["[url]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,83694)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\events\\[url]\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30962)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\layout.jsx"]}]},{metadata:{icon:[async A=>(await Promise.resolve().then(t.bind(t,73881))).default(A)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,23658)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54864)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\not-found.js"],metadata:{icon:[async A=>(await Promise.resolve().then(t.bind(t,73881))).default(A)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],g=["C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\events\\[url]\\page.jsx"],h="/[locale]/(website)/events/[url]/page",m={require:t,loadChunk:()=>Promise.resolve()},d=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/[locale]/(website)/events/[url]/page",pathname:"/[locale]/events/[url]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},37620:(A,e,t)=>{let a={"5d4e1745b359194dee0f653f2deecc494f54833a":()=>Promise.resolve().then(t.bind(t,83694)).then(A=>A.generateMetadata),bccf3e1378c81b1c82b03b218e3d6aedbbdabae2:()=>Promise.resolve().then(t.bind(t,83694)).then(A=>A.default)};async function l(A,...e){return(await a[A]()).apply(null,e)}A.exports={"5d4e1745b359194dee0f653f2deecc494f54833a":l.bind(null,"5d4e1745b359194dee0f653f2deecc494f54833a"),bccf3e1378c81b1c82b03b218e3d6aedbbdabae2:l.bind(null,"bccf3e1378c81b1c82b03b218e3d6aedbbdabae2")}},91017:(A,e,t)=>{Promise.resolve().then(t.bind(t,99063)),Promise.resolve().then(t.bind(t,90423)),Promise.resolve().then(t.bind(t,16027)),Promise.resolve().then(t.bind(t,25609)),Promise.resolve().then(t.bind(t,62445)),Promise.resolve().then(t.bind(t,36056)),Promise.resolve().then(t.bind(t,76297)),Promise.resolve().then(t.bind(t,31487)),Promise.resolve().then(t.bind(t,59788)),Promise.resolve().then(t.bind(t,71557)),Promise.resolve().then(t.bind(t,97397)),Promise.resolve().then(t.bind(t,67142)),Promise.resolve().then(t.bind(t,44181)),Promise.resolve().then(t.bind(t,60419)),Promise.resolve().then(t.bind(t,87881)),Promise.resolve().then(t.bind(t,35349)),Promise.resolve().then(t.bind(t,10331)),Promise.resolve().then(t.bind(t,54600)),Promise.resolve().then(t.bind(t,89279)),Promise.resolve().then(t.bind(t,17601)),Promise.resolve().then(t.bind(t,71155)),Promise.resolve().then(t.bind(t,45978)),Promise.resolve().then(t.bind(t,42579)),Promise.resolve().then(t.bind(t,45173)),Promise.resolve().then(t.bind(t,76012)),Promise.resolve().then(t.bind(t,94932)),Promise.resolve().then(t.bind(t,19727)),Promise.resolve().then(t.bind(t,48967)),Promise.resolve().then(t.bind(t,51335)),Promise.resolve().then(t.bind(t,39428)),Promise.resolve().then(t.bind(t,87166)),Promise.resolve().then(t.bind(t,21961))},99063:(A,e,t)=>{"use strict";t.d(e,{default:()=>b});var a=t(17577),l=t(41135),i=t(14988),n=t(63946),s=t(35627),r=t(41659),c=t(10326),g=t(5028),h=t(52385),m=t(14750);let d=(0,t(71685).Z)("MuiBox",["root"]),o=(0,h.Z)(),b=function(A={}){let{themeId:e,defaultTheme:t,defaultClassName:g="MuiBox-root",generateClassName:h}=A,m=(0,i.ZP)("div",{shouldForwardProp:A=>"theme"!==A&&"sx"!==A&&"as"!==A})(n.Z);return a.forwardRef(function(A,a){let i=(0,r.Z)(t),{className:n,component:d="div",...o}=(0,s.Z)(A);return(0,c.jsx)(m,{as:d,ref:a,className:(0,l.Z)(n,h?h(g):g),theme:e&&i[e]||i,...o})})}({themeId:m.Z,defaultTheme:o,defaultClassName:d.root,generateClassName:g.Z.generate})},45173:(A,e,t)=>{"use strict";t.d(e,{default:()=>h});var a=t(10326),l=t(17577),i=t(481);let n=A=>{let{selected:e,index:t,onClick:l,imageSrc:i}=A;return a.jsx("div",{className:"</div>",children:a.jsx("div",{className:"embla-thumbs__slide".concat(e?" embla-thumbs__slide--selected":""),children:a.jsx("button",{onClick:l,type:"button",className:"embla-thumbs__slide__number",children:a.jsx("img",{src:i,alt:`Thumbnail ${t+1}`,className:"embla-thumbs__image",style:{width:"100%",height:"100%",objectFit:"cover"}})})})})};var s=t(5551),r=t(58996),c=t(23743),g=t(88441);let h=A=>{let{proceccedSlides:e,slides:t,options:h,slidesMobile:m}=A,d=(0,c.Z)(),o=(0,g.Z)(d.breakpoints.down("sm"),{noSsr:!0}),[b,u]=(0,l.useState)(0),[v,p]=(0,i.Z)(h),[E,w]=(0,i.Z)({containScroll:"keepSnaps",dragFree:!0}),q=(0,l.useCallback)(A=>{p&&w&&p.scrollTo(A)},[p,w]),f=(0,l.useCallback)(()=>{if(!p||!w)return;let A=p.selectedScrollSnap();u(A),w.scrollTo(A)},[p,w]);(0,l.useEffect)(()=>{p&&(f(),p.on("select",f).on("reInit",f))},[p,f]);let U=(0,l.useCallback)(()=>{p&&p.scrollPrev()},[p]),V=(0,l.useCallback)(()=>{p&&p.scrollNext()},[p]);return a.jsx("div",{id:"embla",children:(0,a.jsxs)("div",{className:"embla",children:[a.jsx("div",{className:"embla__viewport",ref:v,children:a.jsx("div",{className:"embla__container",children:t?.map((A,e)=>a.jsx("div",{className:"embla__slide",children:a.jsx("img",{src:A.src,alt:`Slide ${e+1}`,className:"embla__image"})},e))})}),a.jsx("div",{className:"embla__controls left",children:a.jsx("button",{className:"left embla__button",onClick:U,children:a.jsx(s.Z,{})})}),a.jsx("div",{className:"embla__controls right",children:a.jsx("button",{className:"right embla__button",onClick:V,children:a.jsx(r.Z,{})})}),a.jsx("div",{className:"embla-thumbs",children:a.jsx("div",{className:"embla-thumbs__viewport",ref:E,children:a.jsx("div",{className:"embla-thumbs__container",children:!o&&t?.map((A,e)=>a.jsx(n,{onClick:()=>q(e),selected:e===b,index:e,imageSrc:A.src},e))})})})]})})}},76012:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>AQ});var a,l,i,n,s,r,c,g,h,m,d,o,b,u,v,p,E,w,q,f,U,V,B,x,R,C,M,X,W,S,D,G,j,I,L,J,H,N,y,Y,F,k,Q,O,P,K,T,Z,z,_,$,AA=t(10326),Ae=t(52210),At=t(23743),Aa=t(88441),Al=t(90423),Ai=t(16027),An=t(58996),As=t(50967),Ar=t(28236);let Ac=function({bannerImg:A,bannerMobileImg:e,height:t,title:a,Icon:l,LeapIcon:i,MobileIcon:n,subtitle:s,bottomChildren:r,event:c,altImg:g,topChildren:h,language:m,url:d,name:o,t:b,eventData:u}){let v=(0,At.Z)(),p=(0,Aa.Z)(v.breakpoints.down("sm"));return(0,AA.jsxs)("div",{id:"banner-component",style:{backgroundImage:`url(${p&&u?.mobileImage?`${process.env.NEXT_PUBLIC_BASE_API_URL}${As.Y.files}/${u?.mobileImage}`:!p&&u?.image?`${process.env.NEXT_PUBLIC_BASE_API_URL}${As.Y.files}/${u?.image}`:p?e.src:A.src})`,height:t||"auto"},children:[g&&AA.jsx("img",{width:0,height:0,alt:g,src:"",style:{display:"none"},loading:"lazy"}),(0,AA.jsxs)(Al.default,{children:[h&&h,(0,AA.jsxs)(AA.Fragment,{children:[AA.jsx(Ai.default,{item:!0,className:"continer_banner_event",sx:{display:"flex",alignItems:"flex-start"},children:(0,AA.jsxs)("div",{className:"event-path",children:[AA.jsx("a",{locale:"en"===m?"en":"fr",href:`${"en"===m?"/events":`/${m}/events`}/`,className:"link",sx:{marginRight:"10px"},children:b("event:eventBreadCrumb")}),AA.jsx(An.Z,{}),AA.jsx("a",{className:"link",href:`${"en"===m?`/events/${d}`:`/${m}/events/${d}`}/`,children:u?.versions[0]?.name?u?.versions[0]?.name:o})]})}),AA.jsx(Ai.default,{sx:{display:"flex",justifyContent:"flex-end"},item:!0,children:" "}),(0,AA.jsxs)(Ai.default,{item:!0,sx:{display:"flex",justifyContent:"center",flexDirection:"column",alignItems:"center",textAlign:"center"},children:[AA.jsx("p",{className:"sub-heading text-slide text-yellow",children:u?.versions[0]?.subTitle?u?.versions[0]?.subTitle:s}),AA.jsx("h1",{className:"heading-h1 text-white",children:u?.versions[0]?.title?u?.versions[0]?.title:a}),i&&!p&&"decarbonization"!==c&&AA.jsx(i,{className:"icon-leap"}),p&&u?.country?AA.jsx("img",{width:150,height:130,src:(0,Ar.I4)(u?.country),className:"icon-map",alt:`${u?.country?`Map of ${u?.country}`:"Country map"}`,loading:"lazy"}):p?AA.jsx(n,{className:"icon-map"}):u?.country?AA.jsx("img",{width:180,height:150,src:(0,Ar.I4)(u?.country),className:"icon-map",alt:`${u?.country?`Map of ${u?.country}`:"Country map"}`,loading:"lazy"}):AA.jsx(l,{className:"icon-map"})]})]}),r&&r]})]})},Ag={src:"/_next/static/media/EventDetail.c90eb7d5.webp",height:1086,width:2880,blurDataURL:"data:image/webp;base64,UklGRj4AAABXRUJQVlA4IDIAAADQAQCdASoIAAMAAkA4JQBOgCHgif4bAAD+6V8R33vCMWzLWYcAeFFOX4Zc3vqQSiAAAA==",blurWidth:8,blurHeight:3},Ah={src:"/_next/static/media/leapEvent.31fa203c.webp",height:1112,width:2880,blurDataURL:"data:image/webp;base64,UklGRjwAAABXRUJQVlA4IDAAAADQAQCdASoIAAMAAkA4JZQCdAD5ik6qAAD++WG9P2IBsM+TyhN9I581337R3zXIAAA=",blurWidth:8,blurHeight:3},Am={src:"/_next/static/media/Libyaevent.ea482813.webp",height:830,width:1900,blurDataURL:"data:image/webp;base64,UklGRkoAAABXRUJQVlA4ID4AAADQAQCdASoIAAMAAkA4JYwCdAD0gbffAAD+80exBaR+8NhVt/yQB8NicHjeZ2srGdjn4rGv+WW7N6wx4AAAAA==",blurWidth:8,blurHeight:3},Ad={src:"/_next/static/media/gitexbanner.d0aa7ef5.png",height:830,width:1900,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAADCAMAAACZFr56AAAANlBMVEVSREFdW1ihcWShR0VlaW2zeXebop5dUE01MzJ3WlWIZ1d+dHRqa3Nsb4lmTT9PcHBfh5JZbYL6d2EnAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAI0lEQVR4nGPg5RTk4WLhF2BgY2TnYGdg4GZgZebkYGDnYwIAC2wAuUd1ZqwAAAAASUVORK5CYII=",blurWidth:8,blurHeight:3},Ao={src:"/_next/static/media/AfricaForumFrance.285f477d.webp",height:827,width:1900,blurDataURL:"data:image/webp;base64,UklGRj4AAABXRUJQVlA4IDIAAADQAQCdASoIAAMAAkA4JZwC7AEPA+odAAD9wXvNiDaXZ1PbmGtDLPlECxvJuAAQQAAAAA==",blurWidth:8,blurHeight:3},Ab={src:"/_next/static/media/leapMobile.c3614757.webp",height:1160,width:786,blurDataURL:"data:image/webp;base64,UklGRjIAAABXRUJQVlA4ICYAAACwAQCdASoFAAgAAkA4JZwCdAD0eqnsAP753LFu7ChiHFysAMAAAA==",blurWidth:5,blurHeight:8},Au={src:"/_next/static/media/bannerQHSEEXPO.21a4497a.webp",height:830,width:1910,blurDataURL:"data:image/webp;base64,UklGRkIAAABXRUJQVlA4IDYAAADQAQCdASoIAAMAAkA4JZwCdAEO+mOFgAD+wjdPn+cK9tyWodEpY10g3gyuS2RZ4NYopg4a6AA=",blurWidth:8,blurHeight:3},Av={src:"/_next/static/media/AfricaForumFrancemobile.d5f2aa13.webp",height:619,width:386,blurDataURL:"data:image/webp;base64,UklGRkQAAABXRUJQVlA4IDgAAADQAQCdASoFAAgAAkA4JZQCdAEO/lfZ4AD+o2czzE+mRDSxhyFlUKwg5IBUZXAI12iaYAFstcAAAA==",blurWidth:5,blurHeight:8},Ap={src:"/_next/static/media/BannerPentabellSalesTraining.70a6262e.webp",height:760,width:1900,blurDataURL:"data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACQAQCdASoIAAMAAkA4JZQAAtz5KQQA/uod5k/wfec5ahJnu+UzV/JOjp5gooAA",blurWidth:8,blurHeight:3},AE={src:"/_next/static/media/BannerQHSEmobile.87adf9d7.webp",height:444,width:392,blurDataURL:"data:image/webp;base64,UklGRkYAAABXRUJQVlA4IDoAAADwAQCdASoHAAgAAkA4JZQCdAEQ/bCwgoAA/u+lcuTRqFa7zWEtU/rRXrHFJaWmZngRZ1/DAl4g9EAA",blurWidth:7,blurHeight:8},Aw={src:"/_next/static/media/BannerPentabellSalesTrainingmobile.99bae85c.webp",height:619,width:392,blurDataURL:"data:image/webp;base64,UklGRkAAAABXRUJQVlA4IDQAAADQAQCdASoFAAgAAkA4JYwCdAD0ob+HAAD+3VQied75HlEEui/OIVEhHcrIs6vFNw016UAA",blurWidth:5,blurHeight:8},Aq={src:"/_next/static/media/eventlibyamobile.82a905ca.webp",height:619,width:392,blurDataURL:"data:image/webp;base64,UklGRkYAAABXRUJQVlA4IDoAAACwAQCdASoFAAgAAkA4JQBOgB5vdqIAAP70oOgubiN9el2OE0sabdEgZYk+zMhH6k4g1+oIMttggAAA",blurWidth:5,blurHeight:8},Af={src:"/_next/static/media/eventmorocomobile.4cde13f4.webp",height:619,width:392,blurDataURL:"data:image/webp;base64,UklGRkAAAABXRUJQVlA4IDQAAAAQAgCdASoFAAgAAkA4JZQCdGuAAs2fOw4AAP74Z0AY33/uG74VHSRYpZb08cUx6bylyAAA",blurWidth:5,blurHeight:8},AU={src:"/_next/static/media/EventsDetailMobile.c3ba71a8.webp",height:1228,width:786,blurDataURL:"data:image/webp;base64,UklGRn4AAABXRUJQVlA4WAoAAAAQAAAABAAABwAAQUxQSCkAAAAA/f39/f37+/v7+/n5+fn59/f39/f19fX19fT09PT08vLy8vLy8vLy8gBWUDggLgAAAFACAJ0BKgUACAACQDgllAJ0fwjAAFmtISacAAD+9aJc/7DP1YDkIDehKUXiAAA=",blurWidth:5,blurHeight:8};var AV=t(95746);function AB(){return(AB=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let Ax=A=>AV.createElement("svg",AB({xmlns:"http://www.w3.org/2000/svg",width:181,height:155,fill:"none"},A),a||(a=AV.createElement("mask",{id:"mapSaudi_svg__a",fill:"#fff"},AV.createElement("path",{d:"m145.708 88.842.532.885.345.393.364.157.436.063.41-.002.378-.13.492-.397.016.066.371-.054.068.044.06.084.063.047.082-.065.007.06-.009.073-.028.059-.047.023-.04.04.014.087.033.077.019.014-.021.274.042.037.128-.045.098-.074.198-.354.103-.103.093-.063.058-.091-.004-.185.107.056.133.012.112.026.049.098.035.105.088.014.112.003.11.07v.058l-.117.056-.149.047-.128.07-.054.13h-.028l-.166.182-.034.065-.038.106-.032.053-.43.321-.067.094-.019.037-.093.131-.035.07-.014.08-.016.178-.019.077-.005.047.007.054-.002.044-.028.019-.077-.005-.035.007-.014.024-.005.173.007.089.026.037.455.042.165-.042.089-.056.061-.054.067-.04.11-.016.48.1.131.101.212.241.312.096.045.307-.003.206-.014.512.04.209.11.187.412.54.378.494.378.494.378.494.378.494.377.493.378.494.378.494.378.494.377.491.378.492.378.491.378.492.378.491.377.492.378.491.378.492.389.503.145.129.18.058 1.166.157 1.009.136 1.01.135 1.01.136 1.009.136 1.01.136 1.01.135 1.01.136 1.009.136 1.01.136 1.01.135 1.01.136 1.009.136 1.008.136 1.01.135 1.009.136 1.01.136.994.133.109-.019.483-.631.462.774.585.985.588.983.585.983.588.983.384.646.075.171.005.18-.271.887-.037.117-.049.155-.135.437-.21.681-.271.883-.322 1.041-.359 1.163-.382 1.241-.094.302-.303.978-.396 1.28-.383 1.238-.356 1.156-.322 1.035-.271.875-.207.674-.135.433-.049.152-.234.749-1.091.388-1.243.443-1.243.442-1.24.442-1.243.443-1.243.442-1.243.442-1.241.443-1.243.44-1.24.44-1.243.44-1.241.44-1.243.44-1.243.44-1.24.44-1.241.44-1.243.44-.807.285-1.145.164-1.164.166-1.163.166-1.164.166-1.164.167-1.161.166-1.161.166-1.164.166-1.161.166-1.164.166-1.163.167-1.164.166-1.164.166-1.161.166-1.164.166-1.163.166-1.164.167-.938.133-.673.248-.325.169-1.042.538-1.245.643-1.39.719-1.248.644-.93.479-.163.115-.562.714-.66.828-1.148 1.449-1.082 1.364-.706.89-.124.236-.343.953-.485 1.348-.212.287-.007.01-.828.487-.891.524-.214.086-.236.033-.956-.042-.102-.005-.091-.058-.614-.859-.536-.754-.469-.657-.268-.157-1.614.225-1.198.166-1.761-.183-1.373-.143-1.803-.187-1.539-.159-.331-.11-1.18-.693-.413-.084-1.22-.009-.179-.003-.896-.007-1.469-.009-.158.035-.455.173-.205.012-.5-.103-.443-.016-.701.075-.558.131-.137.009-.306-.089-.147-.019-.107.078-.107.189-.11.138-.14.066-.189-.028-.277-.145-.124-.014-.156.084-.11.131-.093.15-.114.112-.17.026-.087-.019-.042-.023-.023-.038-.028-.061.019-.152v-.084l-.042-.021-.17.051-.126.007-.287-.025-.27.03-.131-.002-.145-.052-.3-.239-.507-.573-.308-.185-.534-.231-.116-.031-.247-.009-.133-.031-.161.021-.159.061-.639.405-.632.548-.35.433-.02.035.415.138.142.11.074.164-.025.152-.163.082-.166.037-.191.082-.15.122-.044.15.007.175-.042.155-.151.301-.042.157.03.122.056.126.042.169v.166l-.033.145-.116.295-.05.19.029.128.153.298.09.278.083.07.268.075.075.059.049.082.01.098-.031.079-.054.04-.067.031-.066.046-.105.16.017.105.065.117.042.178.002.011-.044.143-.124.024-.291-.089-.182-.035-.093.039-.035.106-.01.161-.03.143-.186.496-.091.131-.157.119-.317.19-.107.038-.282.051-.077.033-.028.098.019.115.009.121-.063.122-.187.131-.254.11-.347.124-.01-.021.003-.274-.052-.079.1-.248-.048-.145-.352-.401-.08-.17-.028-.146.007-.332.084-.325-.002-.138-.13-.033.048-.056-.086-.082-.072-.117-.017-.108.08-.056-.045-.077-.203-.183-.046-.054-.093-.138-.17-.192-.05-.035-.233-.128-.072-.01-.08.042-.247-.346.1-.335-.05-.121v-.375l-.033-.131-.07-.044-.07-.035-.03-.101-.059-.068-.133-.012-.149.01-.103-.01-.088-.079-.072-.122-.091-.213-.033-.112-.037-.262-.035-.106-.066-.058-.326-.145.019.131.053.121.128.216-.147.093.073.499-.08.14-.042-.159-.004-.522-.03-.157-.098-.297-.126-1.034-.047-.183-.033-.135.042-.382-.011-.161-.054-.171-.047-.07-.072-.029-.075-.004-.055-.017-.038-.037-.014-.068-.044-.087-.781-.678-.066-.021-.02.042-.05-.066-.076-.159-.145-.203-.042-.129-.03-.059-.054-.025-.166.035-.074-.019-.033-.096-.037-.066-.315-.33-.014.017-.047.021-.056.016h-.04l-.07-.07-.223-.321-.075-.039-.429-.408-.156.066-.273-.192-.165.026-.068-.201-.128-.129-.157-.108-.144-.143-.306-.393-.046-.128.011-.096.021-.085-.011-.061-.094-.023-.053-.023-.012-.056.005-.068-.012-.059-.305-.276-.112-.159.065-.145-.126-.159-.051-.085-.021-.1-.033-.045-.063-.023-.04-.047.033-.119-.147-.017-.14-.037.021-.033.012-.026.016-.021.04-.023-.044-.091-.066-.045-.081-.002-.1.033.046-.073.035-.075.007-.068-.04-.054.045-.103-.042-.051-.093-.014-.112.012.023-.106-.023-.147-.054-.145-.068-.101-.032-.091-.045-.29-.02-.064-.017-.065-.012-.412-.016-.044-.02-.033-.034-.021-.053-.007-.038-.028-.014-.066-.004-.072-.014-.05-.264-.182-.04-.049-.011-.08-.035-.096-.056-.079-.112-.052-.042-.037-.032-.038-.012-.016-.044.016-.075.08-.028.014-.08-.031-.079-.049-.074-.063-.058-.07-.194-.435-.082-.12-.093-.1-.047-.089-.007-.106.021-.149.173-.506.026-.157.02-.061.038-.028.01-.044-.07-.11-.031-.007-.161-.087-.007-.011-.296-.075-.075-.241-.004-.045-.14-.187-.033-.026-.005-.079.012-.082.044-.152.035-.085.035-.058.026-.061.016-.141.035-.159.093-.187-.062-.115-.133-.088-.145-.061-.312-.092-.129-.091-.074-.213-.103-.164-.028-.072.005-.087.053-.178-.004-.075-.042-.14-.012-.077-.03-.068-.133-.073-.03-.068-.033-.163-.08-.085-.111-.06-.196-.16.014-.023.01-.094-.017-.077-.07-.119-.016-.066-.012-.082-.04-.124.005-.086.049-.077.067-.061.035-.057-.05-.063.018-.082-.016-.082-.04-.065-.016-.091-.62-.223-.173-.093-.369-.384-.175-.096v-.049l.08.012.167.093.045-.316-.019-.388-.051-.169-.334-.487-.142-.096-.373-.044-.145.005-.065.075.084.194-.11-.091-.093-.162-.06-.154-.008-.068-.104-.066-.297-.435-.223-.141-.047-.049.007-.056.09-.203-.043-.139-.087-.081-.221-.096-.273-.169-.065-.023-.068-.106-.063-.023-.065-.037-.18-.258-.058-.03-.18-.045-.063-.007-.133-.035-.147-.087-.33-.269-.26-.297.05-.112-.124-.084-.191-.054-.224-.035-.042-.04-.068-.101-.114-.103-.065-.075.007-.035-.075-.047-.23-.215-.117-.056-.164.002-.18.031-.144.053-.06.066-.173-.066-.31-.189-.161-.068.01.098.041.054.058.038.061.051.035.016.07.05.044.049-.046.023-.126-.035-.71-.438-.102-.089-.095-.046-.145-.042-.133-.064-.112-.236-.126-.122-.39-.306-.195-.197-.431-.704-.143-.122v-.059l.073.005.058-.009.042-.035.026-.066-.112-.021-.094-.054-.168-.164-.046-.086-.042-.227-.033-.087-.242-.276-.021-.129.116-.182-.252-.087-.1-.016.019.061.032.044.045.031.051.023-.142-.047-.159-.091-.095-.117.05-.122-.067-.033-.03-.046.007-.059.042-.068.033.061.055.045.075.03.087.021-.035-.094-.012-.042-.005-.049-.014-.075-.035-.002-.049.014-.051-.021-.098-.141-.04-.002-.011.145-.154-.1-.208-.169-.149-.194.012-.178.03.04.056.049.051.016.017-.054-.026-.091-.04-.035-.053-.019-.059-.039-.07-.035-.074.014-.077.03-.07.016-.002-.03-.206-.316-.046-.112-.021-.131-.005-.188-.023-.07-.1-.068-.022-.072-.009-.094-.028-.033-.047-.016-.305-.218-.08-.091-.046-.215-.068-.152-.016-.092.016-.039.07-.099.014-.049-.051-.112-.273-.426-.1-.061-.226-.365.573-.508.082-.19.01-.25-.057-.227-.11-.119v-.042l.078-.068-.012-.106-.117-.259-.093.166-.075.021-.067-.089-.065-.159.053-.049-.119-.174-.025-.229.011-.243-.014-.216-.077-.213.014-.117.138-.051.04-.045.037-.105.005-.122-.056-.096-.056.164-.038.068-.056.038-.102.004-.024-.037.003-.061-.026-.066-.047-.042-.032-.023-.03-.031-.038-.063-.02-.072-.012-.131-.021-.066-.014-.005-.084-.049-.014-.025-.021-.059-.014-.026-.247-.313-.21-.471-.133-.66.009-.149.191-.073.196.253.142.002.03-.173v-.152l.02-.084.03-.031.098-.07.02-.04.015-.15.067-.311.017-.159h-.045l-.102.269-.05-.115.026-.086.05-.084.025-.115v-.323l.054-.213.237-.279.075-.334.051-.077.066-.066.065-.082.1-.22.124-.129.023-.032v-.099l-.04-.091-.07-.051-.093.021-.049.077-.051.225-.047.081-.177.036-.007-.225.093-.33-.056-.431.054-.44-.054-.215-.056-.105-.2-.279-.189-.442-.142-.237-.054-.124.019-.096.191-.177-.012-.061-.347-.391-.142-.117-.063.037.025.045.129.136.049.07.023.084.007.08.019.079.049.08-.054.054-.096-.223-.203-.316-.198-.222-.375-.683.002-.108.212.117.236.526.128.043.08.049.07-.012.055-.056.042-.089-.074-.091-.124-.239-.075-.049-.053-.024-.059-.058-.044-.08-.019-.086-.062-.127-.145-.06-.166-.04-.12-.068-.148.03-.107-.126-.15-.337.082-.08.06-.105.043-.115.014-.11-.019-.227-.035-.121-.007-.099-.084-.039-.065.121-.09-.173-.127-.173-.133-.265-.128-.154-.054-.176-.088-.121-.021-.108-.063-.136.002-.114-.054-.237-.097-.187-.094-.23-.042-.31-.028-.223-.042-.173-.016-.143-.121-.13-.054-.09-.023-.13-.01-.13-.16.092-.073-.047h-.058l.007.133.068.07.181.068v.054l-.242-.023-.159-.15-.007-.386-.322-.42-.17-.544-.051-.18-.033-.197-.053-.185-.115-.143-.072-.037-.08-.017-.12-.042-.068-.08-.033-.114-.058-.14-.089-.115-.118-.038-.117-.032-.098-.056-.07-.057-.07-.133-.119-.14-.058-.05-.12-.086-.148-.047-.12-.016-.172-.115-.093-.07-.142-.136-.138-.176-.15-.114-.123-.115-.217-.21-.058-.031-.058-.016-.066.03-.06.04-.163-.113-.115-.09-.298-.193-.226-.175.041-.096.127-.09.179-.027-.072-.073-.107.035-.138.07-.054-.102.084-.136.03-.126-.076-.03-.05.058-.088.016-.042.077.042.042.023.073.012.1.005.073-.042.047-.161-.061-.222.058-.33-.255-.101-.09-.282-.225-.066-.162-.095-.066-.124-.06-.13-.073-.108-.103-.142-.096-.173.005-.081-.01-.11.082-.023.106.042.161-.31.012-.077-.12-.215-.103-.168.054-.16-.168-.1-.18-.166-.24-.257-.221-.084-.27.072-.161.054.019.142.096.06-.127-.046-.15-.998-1.465-.448-.624-.212-.284-.011-.222.025-.058.007-.08-.007-.08-.023-.056-.054-.025-.093.093-.077.012-.128-.077-.093-.13-.124-.285.184.005.163.026.164.004.184-.063.09-.135.012-.405.159-.34-.021-.18-.14-.337-.047-.192.047-.473-.014-.182-.037-.171-.063-.16-.084-.147-.135-.166-.084-.08-.077-.055-.068-.064-.067-.15-.061-.063-.145-.112-.144-.16-.126-.182-.087-.18-.016-.08-.016-.18-.019-.07-.049-.075-.11-.121-.034-.08.128-.091-.107-.183-.322-.33-.056-.103-.117-.344-.037-.056-.1-.066-.038-.042-.009-.047.007-.1-.021-.047-.063-.098-.103-.234-.056-.09-.093-.07-.212-.086-.093-.058-.285-.462-.18-.227-.158-.011-.014.091.049.094.026.08-.084.048-.093-.028-.222-.159-.11-.037-.065-.045-.163-.21-.047-.078-.01-.049.01-.173-.016-.047-.087-.02-.044-.04-.088-.204-.01-.19.07-.18.152-.178.095-.166.026-.222-.028-.23-.07-.185-.172-.184-.231-.122-.255-.066-.24-.018-.26-.096-.173-.232-.462-1.102-.166-.284-.315-.428-.536-1.332-.574-1.036-.345-.445-.42-.38-.193-.221-.114-.354-.22-.386-.144-.508-.13.038-.1-.092-.171-.278-.471-.424-.58-.76-.208-.405-.024-.066-.01-.082.017-.067.07-.118.017-.07-.035-.098-.173-.264-.611-.733-.343-.239-.088-.08-.173-.215-.196-.323-.219-.22-.277-.514-.08-.211-.026-.166-.004-.297-.042-.155-.06-.108-.24-.255-.134-.22-.186-.503-.15-.21-.067-.047-.147-.059-.056-.037-.04-.059-.032-.112-.073-.155-.032-.234-.07-.117-.376-.423-.114-.206-.07-.08-.114-.054-.352-.28v.056h-.05l-.079-.11.094-.084.163-.073.126-.077.077.037-.007-.047-.07-.128-.045-.02-.2-.043-.058-.026h-.05v.056h-.044l-.03-.052-.042-.056-.051-.044-.056-.019-.128.005-.066-.007-.084-.09-.338-.245-.072.054-.084-.005h-.077l.02.047.029.04.033.032-.073-.037-.081-.056-.084-.03-.087.04-.074.048-.22.066-.06.033-.042.044-.026.061h-.049v-.056h-.049l-.095.026-.147-.029-.15.003-.1.117-.018-.042h-.007l-.03-.019v-.056h.055v-.061l-.028-.056.038-.047.067-.035.068-.023-.07-.03-.07-.01-.075.011-.081.029v.056h.053v.056l-.081.028-.054.056-.067.148-.024-.035-.023-.012-.026-.005-.025-.012.027-.049.042-.047.056-.042.07-.03-.004-.033-.003-.035.005-.047-.102-.005-.082.02-.054.05-.014.099-.04-.023-.011-.003-.012.01-.037.016-.002-.026.004-.054v-.025h-.049v.056h-.058l-.037-.07-.033-.035-.035.004-.042.047-.067-.051-.017-.024-.044-.065-.026-.038-.044-.103-.021.021-.028.021-.028.028-.026.042-.095-.072-.026.037-.005.082-.023.066-.056.033-.098.042-.046.037v.052l.153.12v.06l-.046.002-.021.014-.012.019-.016.016-.058-.051-.035.138-.008.105-.046.07-.159.026.012-.04.007-.007.014-.007.023-.018-.053-.056-.033-.12-.014-.108-.005-.198-.053-.061-.177.075.032-.099-.044-.053.107-.157.07-.059.063-.03.005-.042.044-.024.06-.058.043-.026-.045-.056.021-.045.028-.032.04-.024.056-.014v.038l-.017.007-.02-.003-.014.007.072.038.06.02.045-.023.023-.086h-.042v-.056l-.051-.052.011-.054.056-.044.08-.021v-.056l-.026-.073.07-.38.028.017.051.021.026.02-.005-.027.005-.086-.042.028-.019.019-.042-.047.147-.143.147-.274.112-.3.077-.388.142-.215.054-.248.051-.14.208-.363-.056-.375-.021-.39-.11-.375.133-.51.094-.267.111-.22v-.052h-.042l.124-.215.019-.1-.042-.092v-.051l.081-.255-.016-.11-.121-.038v-.051l.107-.183.117-.344.053-.353-.081-.209v-.114l.084-.155.07-.18.144-.747.033-.107.081-.187-.011-.052.011-.12.07-.07.168-.095-.049-.14-.018-.183.028-.122.084.047.009-.026v-.024l.01-.011.034.005-.056-.125-.018-.14.03-.124.804.143.863.15 1.129.196 1.01.176 1.077.187.858.15.413.072.837.145.756.131.198-.007.187-.077.788-.653.767-.639.844-.702.56-.465.466-.794.453-.772.333-.569.401-.686.173-.187.2-.1.63-.132.646-.133 1.044-.22 1.036-.218.972-.203.856-.178.156-.14.322-.792.24-.587.262-.639.212-.524.096-.152.167-.14.789-.448.876-.496.532-.3.049-.03.007-.002.002-.002-.002-.005-.36-.424-.794-.94-.798-.941-.797-.944-.796-.945-.023-.028-.023-.028-.023-.028-.024-.026-.944-1.03-.93-1.013-.015-.017-.944-1.032-.945-1.032.278-.084 1.52-.456 1.798-.541 1.798-.543.427-.129 1.371-.414.261-.077 1.372-.398 1.632-.475 1.63-.475 1.632-.475.826-.241v-.005l.252-.108.863-.823.14-.108.074-.023.021-.008.021-.007.021-.007.012-.007 1.436.248 1.434.249 1.435.248 1.434.248.583.1h.004l2.475.48.391.155.404.238.746.45.637.384.636.38.637.385.636.381.637.382.637.381.636.382.637.381.637.382.636.381.637.382.637.381.636.382.637.381.636.382.637.38.648.387.48.421.614.536.613.534.614.534.613.533.688.602.688.601.688.602.142.123.546.478.767.63.639.524.639.524.639.524.639.524.83.681.832.68.833.678.83.679.352.287.48.391.833.677.83.676.83.676.693.562.693.562.692.561.691.562.557.452.047.019.046.018.045.019.046.019.665.06.578.054.578.052.579.054.578.051.744.068.744.068.744.068.744.068.744.067.743.068.744.068.744.068.553.052.553.049.552.051.553.05.765.07.317-.068.189-.04.128-.028.317-.068.317-.068.812.1.809.101.809.101.812.1.723.097.723.096.723.096.723.096.405.053h.005l.089.152.349.602.404 1.11.032.21.028.447.07.253.098.245.11.188.154.142.165.117.136.143.06.22.39-.005 1.289-.014 1.29-.014 1.29-.014 1.289-.014.17.183.091.065.189.087.028.06.065.363.003.277-.063.184-.166-.06-.035.053-.009.024-.009.035.142.01.121-.031.091-.077.049-.129.084.056-.005.075-.079.152-.044.216-.005.098.037.194.094.166.606.674.037.098-.105.16-.053.154-.007.162.039.175.073.131.231.319.048.086-.011.096-.009.03.114.07.247.113.079.01.247-.01.063.019.145.08.086.018.031.021-.005.052-.023.056-.028.037-.047.021-.049-.002-.054-.019-.123.063-.033.054.009.115.07.11.145.15.166.133.135.056.004.038.164.245.291.293.035.084-.065.166-.009.106.007.096.023.042-.016.058-.066.131-.093.136-.089.082.052-.096.011-.07-.016-.201.021-.115.042-.066.023-.067-.035-.12-.107.115-.119.306-.121.085.133-.356.016-.094-.035-.157-.058.012-.061.112-.137.579-.005.133-.016.021-.026.054.009.044.082-.007.04-.04.051-.138.053-.051v.367l.028.087.068-.012.068-.07.037-.087.051.092.01.11-.014.138.091.035.07-.04.053-.08.038-.081.002.142-.016.05-.038.037.035.075-.007.05-.046.027-.08.007.075.18.026.05-.178-.09-.072.003-.051.089.151.065.131-.007.103-.077.065-.147h.049l.049.117-.089.091-.03.161.039.134.129.005.053-.061-.032-.059-.056-.056-.014-.056.056-.075.077-.042.168-.04.142-.014.095.047.259.24.268.139.07.098-.139.1.102.05.093-.012.045-.058-.04-.094.121.056.126.035.275.022.061-.02.128-.076.059-.017.077.01.144.042.079.01.147.043.112.106.166.239.053.046.052.029.049.035.046.063.019.063.014.147.021.066-.091.005-.054-.04-.021-.077.014-.108h-.049l-.151.185-.399-.042-.142.138h-.054l-.049-.14-.126-.003-.275.143.061.035.158.047.082.037.065.05.042.041.052.04.088.038.047.063.037.028.04-.014.123-.124.003-.017.039-.035.017-.047.021-.025.049.028.056.049.067.033-.051.114-.07.057-.072.042-.056.07-.021.091-.005.213-.019.094.077-.021.084-.045.084-.06.073-.068.081-.03.098.027.149.087-.058.159.021.157.054.159.028.166-.014.351.039.148.124-.017-.009.15.049.056.077-.019.079-.075-.038-.121.066-.08.112-.007.105.096.044-.06.037.09-.021.082-.039.087-.024.108-.025.063-.063.026-.082.002-.079-.012.058.094.175.035.058.098.073-.044.046-.068.084-.166.007.037.007.005.012-.002.025.011-.072.145.061.024.184-.059.114.021.196.087.117.01-.196-.216-.061-.129.11-.107.091-.005.07.044.063.054.072.021.056-.025.14-.143-.026.13-.067.2-.007.12.044.186.04.103.039.045.136.063.132.154.42.663.212.267.063.049.206.091.067.054.107.112.075.059.45.208.282.208.161.019.168-.01.182.026.224.131.21.209.364.498.123.223.53.386.118.133.026.208-.329-.294-.175-.113-.396-.049-.103-.035-.07-.091-.056-.3-.063.052-.065.138-.035.133-.007.103.003.098.023.09.051.076.072.078.019.042-.03.498.011.136.196.62.082.131.119.117.133.082.622.218.213.192.128.276.054.37-.005.884-.028.192-.075.162-.142.091-.005-.124-.056-.042-.077.028-.065.089.047.204-.019.313-.058.318-.065.22-.112-.084-.033-.115.002-.128-.011-.12-.052-.098-.065-.065-.068-.052-.058-.063-.04-.03h-.032l-.024-.02-.014-.224-.013-.054-.028-.063-.056-.08-.089-.088-.096-.054-.081.028-.189.37-.058.051-.08.052.038.11.077.105.041.04.017.058.067.117.012.077-.023.022-.052.018-.051.033-.023.06.016.118.028.115.007.117-.051.126.121.042.103-.063.051-.122-.03-.133.07.02.049.043.084.103.039.019.031-.007.021.007.007.063v.049l.009.026.021.01h.044l.184.044.075.11.037.145.08.145v.056l-.145.152.077.38.17.418.145.27.436.528.184.307.075.384h-.047l-.126-.384-.051-.061-.089-.021-.107-.05-.091-.062-.091-.136-.114-.091-.105-.012-.023.157.053.112.187.136.058.082.037-.082.012-.035h.049l.275.582.828 1.075.191.168-.021-.108-.049-.126-.021-.105.063-.045.077-.037.033.019.03.177.037.045.035.023.017.019.058.112.135.063.248.075.062.08.14.363-.053.056.088.124.098.213.082.232.035.177-.009.101-.038.178-.009.105.016.103.033.073.033.054.039.138.105.194.019.124-.045.33.059.328.063.184.077.096.056-.154.189.157.198.25.081.131.122.122.144.531.182.122.145.138.063.307.016.491.03.094.063.14.077.133.077.075.107-.002zm-62.57 62.686v.108l-.02.136-.05.166.02.126.065.225-.072.152.002.012-.166-.012-.203-.246-.165-.061-.156-.011-.077-.071.205-.086.117-.096-.047-.157-.184-.098-.185.028-.214.019-.147.037-.138.077-.116.068-.145-.049-.163-.157-.117-.107-.165-.04-.194-.138-.175-.138-.086-.157-.047-.234-.126-.089h-.125l-.087-.021.019-.052-.01-.164-.107-.138-.067-.147.011-.136.108.031.04.058.086.07.282.129.154.178.077.108.028.184-.005.089.13.148.126.108.05.03.263-.028.086.138.147.012.147-.047.126-.01.086.089v.089h.089l.214-.096-.205-.039-.107-.169.089-.138.053-.218.047-.075.105-.016.046.019.059.023.2.124.01.127.095.156-.014.082.18.028.046.148zm-1.942-.65-.107-.089-.107-.138-.184-.237-.154-.215-.059-.087.082-.072.175-.01.128-.039.093-.08.073-.133.074.007.014.002.04-.152-.056-.183-.098-.044-.098-.045-.093-.007-.163-.035-.089-.007-.019-.033.056-.06.068-.028.019.065.235.023.184.075.124.145.007.031.044.201.003.145-.04.166.028.139.233.06.068.167-.299.004-.086.113.042.128.177.063.1.061.138.061.047.185v.089l-.213-.129-.263-.079zm55.497-83.16.142.061.387.112.366.106-.133.051-.112-.026-.107-.051-.121-.028-.119.016-.112.047-.103.07-.088.087-.059-.063-.007.004-.032.007-.059-.042-.039-.018h-.003v-.052l.098-.042.019-.044-.03-.052-.031-.033-.028-.028-.074-.035-.08.012-.076.03-.068.017-.093.037-.091.096-.08.129-.056.133-.018-.213.135-.21.21-.153.217-.04.081.015.063.03zM45.694 83.43l.077.063.035.035-.016.007-.033-.014-.056-.01-.007.034.03.051-.058-.01-.142-.093-.037-.089.011-.054-.07-.007-.088.026-.049-.068-.035-.11h-.065l-.08.056-.016-.056.054-.152.07-.082h.088l.1.014.087.054.07.157.047-.007.016-.059.03.04zm-.615.01.116-.005.024.037-.133.073-.056.044-.077.087-.072.147-.11.08-.154-.103-.137-.155-.133-.105-.292-.314-.019-.02-.025-.047v-.03l.014.001.014-.016-.014-.047-.07-.08-.084-.119.002-.058.077.019.07.06.133.166.063.05.133.072.254.113.121.032.091.01.068.04.032.058-.151.007-.023.103.058.096.086-.023.089-.099zm-.674-.925-.07-.023-.093-.092.014-.115.088-.07.105.014.08.087-.005.094-.077.075zm-.43.283-.07-.051-.034-.01-.033-.011-.04-.059-.004-.082-.065-.023-.103-.019-.019-.028.03.01-.006-.036-.063-.086-.11-.073-.13.007-.059.035-.032.03-.056.02.002-.076.072-.11.112-.032.135.049.36.24.04.062.036.093.11.068.114.042.164.1.256.216.063.073-.012.028-.028.026.01.035-.04-.021-.112-.09-.098-.06-.074-.019-.03-.051-.038-.09-.068-.032-.046.021-.066-.021zm.803-.414-.01-.23-.009-.051v-.091l.047-.063.07-.028.063.03.035.08.005.074-.02.059-.032.002-.044-.035-.01.014.02.059-.005.028-.017-.01.05.113.05.215-.032.073-.082-.127-.046-.033zm-1.579-.187-.01.049-.041.093-.06.04-.05-.026-.095-.07-.581-.3-.156-.037-.05.113-.072-.038-.137-.133-.093-.11-.047-.171.06-.14.18-.188.08-.1.06-.038.012.052-.042.093-.***************.***************.***************.***************.***************.*************.06-.***************.***************.031.063-.007.011-.007-.023-.01.005-.034.032-.033.06.01zm-1.007-1.285-.012-.066-.019-.17.02-.335.116-.279.184-.173.051-.021-.098.096-.079.126-.06.138-.***************.**************.052.007-.005.054-.063.107-.***************.***************.084-.018.025.014-.04.067-.072.038-.09-.016-.145-.117-.082-.052z"}))),l||(l=AV.createElement("path",{stroke:"#E5F0FC",strokeWidth:2,d:"m145.708 88.842.532.885.345.393.364.157.436.063.41-.002.378-.13.492-.397.016.066.371-.054.068.044.06.084.063.047.082-.065.007.06-.009.073-.028.059-.047.023-.04.04.014.087.033.077.019.014-.021.274.042.037.128-.045.098-.074.198-.354.103-.103.093-.063.058-.091-.004-.185.107.056.133.012.112.026.049.098.035.105.088.014.112.003.11.07v.058l-.117.056-.149.047-.128.07-.054.13h-.028l-.166.182-.034.065-.038.106-.032.053-.43.321-.067.094-.019.037-.093.131-.035.07-.014.08-.016.178-.019.077-.005.047.007.054-.002.044-.028.019-.077-.005-.035.007-.014.024-.005.173.007.089.026.037.455.042.165-.042.089-.056.061-.054.067-.04.11-.016.48.1.131.101.212.241.312.096.045.307-.003.206-.014.512.04.209.11.187.412.54.378.494.378.494.378.494.378.494.377.493.378.494.378.494.378.494.377.491.378.492.378.491.378.492.378.491.377.492.378.491.378.492.389.503.145.129.18.058 1.166.157 1.009.136 1.01.135 1.01.136 1.009.136 1.01.136 1.01.135 1.01.136 1.009.136 1.01.136 1.01.135 1.01.136 1.009.136 1.008.136 1.01.135 1.009.136 1.01.136.994.133.109-.019.483-.631.462.774.585.985.588.983.585.983.588.983.384.646.075.171.005.18-.271.887-.037.117-.049.155-.135.437-.21.681-.271.883-.322 1.041-.359 1.163-.382 1.241-.094.302-.303.978-.396 1.28-.383 1.238-.356 1.156-.322 1.035-.271.875-.207.674-.135.433-.049.152-.234.749-1.091.388-1.243.443-1.243.442-1.24.442-1.243.443-1.243.442-1.243.442-1.241.443-1.243.44-1.24.44-1.243.44-1.241.44-1.243.44-1.243.44-1.24.44-1.241.44-1.243.44-.807.285-1.145.164-1.164.166-1.163.166-1.164.166-1.164.167-1.161.166-1.161.166-1.164.166-1.161.166-1.164.166-1.163.167-1.164.166-1.164.166-1.161.166-1.164.166-1.163.166-1.164.167-.938.133-.673.248-.325.169-1.042.538-1.245.643-1.39.719-1.248.644-.93.479-.163.115-.562.714-.66.828-1.148 1.449-1.082 1.364-.706.89-.124.236-.343.953-.485 1.348-.212.287-.007.01-.828.487-.891.524-.214.086-.236.033-.956-.042-.102-.005-.091-.058-.614-.859-.536-.754-.469-.657-.268-.157-1.614.225-1.198.166-1.761-.183-1.373-.143-1.803-.187-1.539-.159-.331-.11-1.18-.693-.413-.084-1.22-.009-.179-.003-.896-.007-1.469-.009-.158.035-.455.173-.205.012-.5-.103-.443-.016-.701.075-.558.131-.137.009-.306-.089-.147-.019-.107.078-.107.189-.11.138-.14.066-.189-.028-.277-.145-.124-.014-.156.084-.11.131-.093.15-.114.112-.17.026-.087-.019-.042-.023-.023-.038-.028-.061.019-.152v-.084l-.042-.021-.17.051-.126.007-.287-.025-.27.03-.131-.002-.145-.052-.3-.239-.507-.573-.308-.185-.534-.231-.116-.031-.247-.009-.133-.031-.161.021-.159.061-.639.405-.632.548-.35.433-.02.035.415.138.142.11.074.164-.025.152-.163.082-.166.037-.191.082-.15.122-.044.15.007.175-.042.155-.151.301-.042.157.03.122.056.126.042.169v.166l-.033.145-.116.295-.05.19.029.128.153.298.09.278.083.07.268.075.075.059.049.082.01.098-.031.079-.054.04-.067.031-.066.046-.105.16.017.105.065.117.042.178.002.011-.044.143-.124.024-.291-.089-.182-.035-.093.039-.035.106-.01.161-.03.143-.186.496-.091.131-.157.119-.317.19-.107.038-.282.051-.077.033-.028.098.019.115.009.121-.063.122-.187.131-.254.11-.347.124-.01-.021.003-.274-.052-.079.1-.248-.048-.145-.352-.401-.08-.17-.028-.146.007-.332.084-.325-.002-.138-.13-.033.048-.056-.086-.082-.072-.117-.017-.108.08-.056-.045-.077-.203-.183-.046-.054-.093-.138-.17-.192-.05-.035-.233-.128-.072-.01-.08.042-.247-.346.1-.335-.05-.121v-.375l-.033-.131-.07-.044-.07-.035-.03-.101-.059-.068-.133-.012-.149.01-.103-.01-.088-.079-.072-.122-.091-.213-.033-.112-.037-.262-.035-.106-.066-.058-.326-.145.019.131.053.121.128.216-.147.093.073.499-.08.14-.042-.159-.004-.522-.03-.157-.098-.297-.126-1.034-.047-.183-.033-.135.042-.382-.011-.161-.054-.171-.047-.07-.072-.029-.075-.004-.055-.017-.038-.037-.014-.068-.044-.087-.781-.678-.066-.021-.02.042-.05-.066-.076-.159-.145-.203-.042-.129-.03-.059-.054-.025-.166.035-.074-.019-.033-.096-.037-.066-.315-.33-.014.017-.047.021-.056.016h-.04l-.07-.07-.223-.321-.075-.039-.429-.408-.156.066-.273-.192-.165.026-.068-.201-.128-.129-.157-.108-.144-.143-.306-.393-.046-.128.011-.096.021-.085-.011-.061-.094-.023-.053-.023-.012-.056.005-.068-.012-.059-.305-.276-.112-.159.065-.145-.126-.159-.051-.085-.021-.1-.033-.045-.063-.023-.04-.047.033-.119-.147-.017-.14-.037.021-.033.012-.026.016-.021.04-.023-.044-.091-.066-.045-.081-.002-.1.033.046-.073.035-.075.007-.068-.04-.054.045-.103-.042-.051-.093-.014-.112.012.023-.106-.023-.147-.054-.145-.068-.101-.032-.091-.045-.29-.02-.064-.017-.065-.012-.412-.016-.044-.02-.033-.034-.021-.053-.007-.038-.028-.014-.066-.004-.072-.014-.05-.264-.182-.04-.049-.011-.08-.035-.096-.056-.079-.112-.052-.042-.037-.032-.038-.012-.016-.044.016-.075.08-.028.014-.08-.031-.079-.049-.074-.063-.058-.07-.194-.435-.082-.12-.093-.1-.047-.089-.007-.106.021-.149.173-.506.026-.157.02-.061.038-.028.01-.044-.07-.11-.031-.007-.161-.087-.007-.011-.296-.075-.075-.241-.004-.045-.14-.187-.033-.026-.005-.079.012-.082.044-.152.035-.085.035-.058.026-.061.016-.141.035-.159.093-.187-.062-.115-.133-.088-.145-.061-.312-.092-.129-.091-.074-.213-.103-.164-.028-.072.005-.087.053-.178-.004-.075-.042-.14-.012-.077-.03-.068-.133-.073-.03-.068-.033-.163-.08-.085-.111-.06-.196-.16.014-.023.01-.094-.017-.077-.07-.119-.016-.066-.012-.082-.04-.124.005-.086.049-.077.067-.061.035-.057-.05-.063.018-.082-.016-.082-.04-.065-.016-.091-.62-.223-.173-.093-.369-.384-.175-.096v-.049l.08.012.167.093.045-.316-.019-.388-.051-.169-.334-.487-.142-.096-.373-.044-.145.005-.065.075.084.194-.11-.091-.093-.162-.06-.154-.008-.068-.104-.066-.297-.435-.223-.141-.047-.049.007-.056.09-.203-.043-.139-.087-.081-.221-.096-.273-.169-.065-.023-.068-.106-.063-.023-.065-.037-.18-.258-.058-.03-.18-.045-.063-.007-.133-.035-.147-.087-.33-.269-.26-.297.05-.112-.124-.084-.191-.054-.224-.035-.042-.04-.068-.101-.114-.103-.065-.075.007-.035-.075-.047-.23-.215-.117-.056-.164.002-.18.031-.144.053-.06.066-.173-.066-.31-.189-.161-.068.01.098.041.054.058.038.061.051.035.016.07.05.044.049-.046.023-.126-.035-.71-.438-.102-.089-.095-.046-.145-.042-.133-.064-.112-.236-.126-.122-.39-.306-.195-.197-.431-.704-.143-.122v-.059l.073.005.058-.009.042-.035.026-.066-.112-.021-.094-.054-.168-.164-.046-.086-.042-.227-.033-.087-.242-.276-.021-.129.116-.182-.252-.087-.1-.016.019.061.032.044.045.031.051.023-.142-.047-.159-.091-.095-.117.05-.122-.067-.033-.03-.046.007-.059.042-.068.033.061.055.045.075.03.087.021-.035-.094-.012-.042-.005-.049-.014-.075-.035-.002-.049.014-.051-.021-.098-.141-.04-.002-.011.145-.154-.1-.208-.169-.149-.194.012-.178.03.04.056.049.051.016.017-.054-.026-.091-.04-.035-.053-.019-.059-.039-.07-.035-.074.014-.077.03-.07.016-.002-.03-.206-.316-.046-.112-.021-.131-.005-.188-.023-.07-.1-.068-.022-.072-.009-.094-.028-.033-.047-.016-.305-.218-.08-.091-.046-.215-.068-.152-.016-.092.016-.039.07-.099.014-.049-.051-.112-.273-.426-.1-.061-.226-.365.573-.508.082-.19.01-.25-.057-.227-.11-.119v-.042l.078-.068-.012-.106-.117-.259-.093.166-.075.021-.067-.089-.065-.159.053-.049-.119-.174-.025-.229.011-.243-.014-.216-.077-.213.014-.117.138-.051.04-.045.037-.105.005-.122-.056-.096-.056.164-.038.068-.056.038-.102.004-.024-.037.003-.061-.026-.066-.047-.042-.032-.023-.03-.031-.038-.063-.02-.072-.012-.131-.021-.066-.014-.005-.084-.049-.014-.025-.021-.059-.014-.026-.247-.313-.21-.471-.133-.66.009-.149.191-.073.196.253.142.002.03-.173v-.152l.02-.084.03-.031.098-.07.02-.04.015-.15.067-.311.017-.159h-.045l-.102.269-.05-.115.026-.086.05-.084.025-.115v-.323l.054-.213.237-.279.075-.334.051-.077.066-.066.065-.082.1-.22.124-.129.023-.032v-.099l-.04-.091-.07-.051-.093.021-.049.077-.051.225-.047.081-.177.036-.007-.225.093-.33-.056-.431.054-.44-.054-.215-.056-.105-.2-.279-.189-.442-.142-.237-.054-.124.019-.096.191-.177-.012-.061-.347-.391-.142-.117-.063.037.025.045.129.136.049.07.023.084.007.08.019.079.049.08-.054.054-.096-.223-.203-.316-.198-.222-.375-.683.002-.108.212.117.236.526.128.043.08.049.07-.012.055-.056.042-.089-.074-.091-.124-.239-.075-.049-.053-.024-.059-.058-.044-.08-.019-.086-.062-.127-.145-.06-.166-.04-.12-.068-.148.03-.107-.126-.15-.337.082-.08.06-.105.043-.115.014-.11-.019-.227-.035-.121-.007-.099-.084-.039-.065.121-.09-.173-.127-.173-.133-.265-.128-.154-.054-.176-.088-.121-.021-.108-.063-.136.002-.114-.054-.237-.097-.187-.094-.23-.042-.31-.028-.223-.042-.173-.016-.143-.121-.13-.054-.09-.023-.13-.01-.13-.16.092-.073-.047h-.058l.007.133.068.07.181.068v.054l-.242-.023-.159-.15-.007-.386-.322-.42-.17-.544-.051-.18-.033-.197-.053-.185-.115-.143-.072-.037-.08-.017-.12-.042-.068-.08-.033-.114-.058-.14-.089-.115-.118-.038-.117-.032-.098-.056-.07-.057-.07-.133-.119-.14-.058-.05-.12-.086-.148-.047-.12-.016-.172-.115-.093-.07-.142-.136-.138-.176-.15-.114-.123-.115-.217-.21-.058-.031-.058-.016-.066.03-.06.04-.163-.113-.115-.09-.298-.193-.226-.175.041-.096.127-.09.179-.027-.072-.073-.107.035-.138.07-.054-.102.084-.136.03-.126-.076-.03-.05.058-.088.016-.042.077.042.042.023.073.012.1.005.073-.042.047-.161-.061-.222.058-.33-.255-.101-.09-.282-.225-.066-.162-.095-.066-.124-.06-.13-.073-.108-.103-.142-.096-.173.005-.081-.01-.11.082-.023.106.042.161-.31.012-.077-.12-.215-.103-.168.054-.16-.168-.1-.18-.166-.24-.257-.221-.084-.27.072-.161.054.019.142.096.06-.127-.046-.15-.998-1.465-.448-.624-.212-.284-.011-.222.025-.058.007-.08-.007-.08-.023-.056-.054-.025-.093.093-.077.012-.128-.077-.093-.13-.124-.285.184.005.163.026.164.004.184-.063.09-.135.012-.405.159-.34-.021-.18-.14-.337-.047-.192.047-.473-.014-.182-.037-.171-.063-.16-.084-.147-.135-.166-.084-.08-.077-.055-.068-.064-.067-.15-.061-.063-.145-.112-.144-.16-.126-.182-.087-.18-.016-.08-.016-.18-.019-.07-.049-.075-.11-.121-.034-.08.128-.091-.107-.183-.322-.33-.056-.103-.117-.344-.037-.056-.1-.066-.038-.042-.009-.047.007-.1-.021-.047-.063-.098-.103-.234-.056-.09-.093-.07-.212-.086-.093-.058-.285-.462-.18-.227-.158-.011-.014.091.049.094.026.08-.084.048-.093-.028-.222-.159-.11-.037-.065-.045-.163-.21-.047-.078-.01-.049.01-.173-.016-.047-.087-.02-.044-.04-.088-.204-.01-.19.07-.18.152-.178.095-.166.026-.222-.028-.23-.07-.185-.172-.184-.231-.122-.255-.066-.24-.018-.26-.096-.173-.232-.462-1.102-.166-.284-.315-.428-.536-1.332-.574-1.036-.345-.445-.42-.38-.193-.221-.114-.354-.22-.386-.144-.508-.13.038-.1-.092-.171-.278-.471-.424-.58-.76-.208-.405-.024-.066-.01-.082.017-.067.07-.118.017-.07-.035-.098-.173-.264-.611-.733-.343-.239-.088-.08-.173-.215-.196-.323-.219-.22-.277-.514-.08-.211-.026-.166-.004-.297-.042-.155-.06-.108-.24-.255-.134-.22-.186-.503-.15-.21-.067-.047-.147-.059-.056-.037-.04-.059-.032-.112-.073-.155-.032-.234-.07-.117-.376-.423-.114-.206-.07-.08-.114-.054-.352-.28v.056h-.05l-.079-.11.094-.084.163-.073.126-.077.077.037-.007-.047-.07-.128-.045-.02-.2-.043-.058-.026h-.05v.056h-.044l-.03-.052-.042-.056-.051-.044-.056-.019-.128.005-.066-.007-.084-.09-.338-.245-.072.054-.084-.005h-.077l.02.047.029.04.033.032-.073-.037-.081-.056-.084-.03-.087.04-.074.048-.22.066-.06.033-.042.044-.026.061h-.049v-.056h-.049l-.095.026-.147-.029-.15.003-.1.117-.018-.042h-.007l-.03-.019v-.056h.055v-.061l-.028-.056.038-.047.067-.035.068-.023-.07-.03-.07-.01-.075.011-.081.029v.056h.053v.056l-.081.028-.054.056-.067.148-.024-.035-.023-.012-.026-.005-.025-.012.027-.049.042-.047.056-.042.07-.03-.004-.033-.003-.035.005-.047-.102-.005-.082.02-.054.05-.014.099-.04-.023-.011-.003-.012.01-.037.016-.002-.026.004-.054v-.025h-.049v.056h-.058l-.037-.07-.033-.035-.035.004-.042.047-.067-.051-.017-.024-.044-.065-.026-.038-.044-.103-.021.021-.028.021-.028.028-.026.042-.095-.072-.026.037-.005.082-.023.066-.056.033-.098.042-.046.037v.052l.153.12v.06l-.046.002-.021.014-.012.019-.016.016-.058-.051-.035.138-.008.105-.046.07-.159.026.012-.04.007-.007.014-.007.023-.018-.053-.056-.033-.12-.014-.108-.005-.198-.053-.061-.177.075.032-.099-.044-.053.107-.157.07-.059.063-.03.005-.042.044-.024.06-.058.043-.026-.045-.056.021-.045.028-.032.04-.024.056-.014v.038l-.017.007-.02-.003-.014.007.072.038.06.02.045-.023.023-.086h-.042v-.056l-.051-.052.011-.054.056-.044.08-.021v-.056l-.026-.073.07-.38.028.017.051.021.026.02-.005-.027.005-.086-.042.028-.019.019-.042-.047.147-.143.147-.274.112-.3.077-.388.142-.215.054-.248.051-.14.208-.363-.056-.375-.021-.39-.11-.375.133-.51.094-.267.111-.22v-.052h-.042l.124-.215.019-.1-.042-.092v-.051l.081-.255-.016-.11-.121-.038v-.051l.107-.183.117-.344.053-.353-.081-.209v-.114l.084-.155.07-.18.144-.747.033-.107.081-.187-.011-.052.011-.12.07-.07.168-.095-.049-.14-.018-.183.028-.122.084.047.009-.026v-.024l.01-.011.034.005-.056-.125-.018-.14.03-.124.804.143.863.15 1.129.196 1.01.176 1.077.187.858.15.413.072.837.145.756.131.198-.007.187-.077.788-.653.767-.639.844-.702.56-.465.466-.794.453-.772.333-.569.401-.686.173-.187.2-.1.63-.132.646-.133 1.044-.22 1.036-.218.972-.203.856-.178.156-.14.322-.792.24-.587.262-.639.212-.524.096-.152.167-.14.789-.448.876-.496.532-.3.049-.03.007-.002.002-.002-.002-.005-.36-.424-.794-.94-.798-.941-.797-.944-.796-.945-.023-.028-.023-.028-.023-.028-.024-.026-.944-1.03-.93-1.013-.015-.017-.944-1.032-.945-1.032.278-.084 1.52-.456 1.798-.541 1.798-.543.427-.129 1.371-.414.261-.077 1.372-.398 1.632-.475 1.63-.475 1.632-.475.826-.241v-.005l.252-.108.863-.823.14-.108.074-.023.021-.008.021-.007.021-.007.012-.007 1.436.248 1.434.249 1.435.248 1.434.248.583.1h.004l2.475.48.391.155.404.238.746.45.637.384.636.38.637.385.636.381.637.382.637.381.636.382.637.381.637.382.636.381.637.382.637.381.636.382.637.381.636.382.637.38.648.387.48.421.614.536.613.534.614.534.613.533.688.602.688.601.688.602.142.123.546.478.767.63.639.524.639.524.639.524.639.524.83.681.832.68.833.678.83.679.352.287.48.391.833.677.83.676.83.676.693.562.693.562.692.561.691.562.557.452.047.019.046.018.045.019.046.019.665.06.578.054.578.052.579.054.578.051.744.068.744.068.744.068.744.068.744.067.743.068.744.068.744.068.553.052.553.049.552.051.553.05.765.07.317-.068.189-.04.128-.028.317-.068.317-.068.812.1.809.101.809.101.812.1.723.097.723.096.723.096.723.096.405.053h.005l.089.152.349.602.404 1.11.032.21.028.447.07.253.098.245.11.188.154.142.165.117.136.143.06.22.39-.005 1.289-.014 1.29-.014 1.29-.014 1.289-.014.17.183.091.065.189.087.028.06.065.363.003.277-.063.184-.166-.06-.035.053-.009.024-.009.035.142.01.121-.031.091-.077.049-.129.084.056-.005.075-.079.152-.044.216-.005.098.037.194.094.166.606.674.037.098-.105.16-.053.154-.007.162.039.175.073.131.231.319.048.086-.011.096-.009.03.114.07.247.113.079.01.247-.01.063.019.145.08.086.018.031.021-.005.052-.023.056-.028.037-.047.021-.049-.002-.054-.019-.123.063-.033.054.009.115.07.11.145.15.166.133.135.056.004.038.164.245.291.293.035.084-.065.166-.009.106.007.096.023.042-.016.058-.066.131-.093.136-.089.082.052-.096.011-.07-.016-.201.021-.115.042-.066.023-.067-.035-.12-.107.115-.119.306-.121.085.133-.356.016-.094-.035-.157-.058.012-.061.112-.137.579-.005.133-.016.021-.026.054.009.044.082-.007.04-.04.051-.138.053-.051v.367l.028.087.068-.012.068-.07.037-.087.051.092.01.11-.014.138.091.035.07-.04.053-.08.038-.081.002.142-.016.05-.038.037.035.075-.007.05-.046.027-.08.007.075.18.026.05-.178-.09-.072.003-.051.089.151.065.131-.007.103-.077.065-.147h.049l.049.117-.089.091-.03.161.039.134.129.005.053-.061-.032-.059-.056-.056-.014-.056.056-.075.077-.042.168-.04.142-.014.095.047.259.24.268.139.07.098-.139.1.102.05.093-.012.045-.058-.04-.094.121.056.126.035.275.022.061-.02.128-.076.059-.017.077.01.144.042.079.01.147.043.112.106.166.239.053.046.052.029.049.035.046.063.019.063.014.147.021.066-.091.005-.054-.04-.021-.077.014-.108h-.049l-.151.185-.399-.042-.142.138h-.054l-.049-.14-.126-.003-.275.143.061.035.158.047.082.037.065.05.042.041.052.04.088.038.047.063.037.028.04-.014.123-.124.003-.017.039-.035.017-.047.021-.025.049.028.056.049.067.033-.051.114-.07.057-.072.042-.056.07-.021.091-.005.213-.019.094.077-.021.084-.045.084-.06.073-.068.081-.03.098.027.149.087-.058.159.021.157.054.159.028.166-.014.351.039.148.124-.017-.009.15.049.056.077-.019.079-.075-.038-.121.066-.08.112-.007.105.096.044-.06.037.09-.021.082-.039.087-.024.108-.025.063-.063.026-.082.002-.079-.012.058.094.175.035.058.098.073-.044.046-.068.084-.166.007.037.007.005.012-.002.025.011-.072.145.061.024.184-.059.114.021.196.087.117.01-.196-.216-.061-.129.11-.107.091-.005.07.044.063.054.072.021.056-.025.14-.143-.026.13-.067.2-.007.12.044.186.04.103.039.045.136.063.132.154.42.663.212.267.063.049.206.091.067.054.107.112.075.059.45.208.282.208.161.019.168-.01.182.026.224.131.21.209.364.498.123.223.53.386.118.133.026.208-.329-.294-.175-.113-.396-.049-.103-.035-.07-.091-.056-.3-.063.052-.065.138-.035.133-.007.103.003.098.023.09.051.076.072.078.019.042-.03.498.011.136.196.62.082.131.119.117.133.082.622.218.213.192.128.276.054.37-.005.884-.028.192-.075.162-.142.091-.005-.124-.056-.042-.077.028-.065.089.047.204-.019.313-.058.318-.065.22-.112-.084-.033-.115.002-.128-.011-.12-.052-.098-.065-.065-.068-.052-.058-.063-.04-.03h-.032l-.024-.02-.014-.224-.013-.054-.028-.063-.056-.08-.089-.088-.096-.054-.081.028-.189.37-.058.051-.08.052.038.11.077.105.041.04.017.058.067.117.012.077-.023.022-.052.018-.051.033-.023.06.016.118.028.115.007.117-.051.126.121.042.103-.063.051-.122-.03-.133.07.02.049.043.084.103.039.019.031-.007.021.007.007.063v.049l.009.026.021.01h.044l.184.044.075.11.037.145.08.145v.056l-.145.152.077.38.17.418.145.27.436.528.184.307.075.384h-.047l-.126-.384-.051-.061-.089-.021-.107-.05-.091-.062-.091-.136-.114-.091-.105-.012-.023.157.053.112.187.136.058.082.037-.082.012-.035h.049l.275.582.828 1.075.191.168-.021-.108-.049-.126-.021-.105.063-.045.077-.037.033.019.03.177.037.045.035.023.017.019.058.112.135.063.248.075.062.08.14.363-.053.056.088.124.098.213.082.232.035.177-.009.101-.038.178-.009.105.016.103.033.073.033.054.039.138.105.194.019.124-.045.33.059.328.063.184.077.096.056-.154.189.157.198.25.081.131.122.122.144.531.182.122.145.138.063.307.016.491.03.094.063.14.077.133.077.075.107-.002zm-62.57 62.686v.108l-.02.136-.05.166.02.126.065.225-.072.152.002.012-.166-.012-.203-.246-.165-.061-.156-.011-.077-.071.205-.086.117-.096-.047-.157-.184-.098-.185.028-.214.019-.147.037-.138.077-.116.068-.145-.049-.163-.157-.117-.107-.165-.04-.194-.138-.175-.138-.086-.157-.047-.234-.126-.089h-.125l-.087-.021.019-.052-.01-.164-.107-.138-.067-.147.011-.136.108.031.04.058.086.07.282.129.154.178.077.108.028.184-.005.089.13.148.126.108.05.03.263-.028.086.138.147.012.147-.047.126-.01.086.089v.089h.089l.214-.096-.205-.039-.107-.169.089-.138.053-.218.047-.075.105-.016.046.019.059.023.2.124.01.127.095.156-.014.082.18.028.046.148zm-1.942-.65-.107-.089-.107-.138-.184-.237-.154-.215-.059-.087.082-.072.175-.01.128-.039.093-.08.073-.133.074.007.014.002.04-.152-.056-.183-.098-.044-.098-.045-.093-.007-.163-.035-.089-.007-.019-.033.056-.06.068-.028.019.065.235.023.184.075.124.145.007.031.044.201.003.145-.04.166.028.139.233.06.068.167-.299.004-.086.113.042.128.177.063.1.061.138.061.047.185v.089l-.213-.129-.263-.079zm55.497-83.16.142.061.387.112.366.106-.133.051-.112-.026-.107-.051-.121-.028-.119.016-.112.047-.103.07-.088.087-.059-.063-.007.004-.032.007-.059-.042-.039-.018h-.003v-.052l.098-.042.019-.044-.03-.052-.031-.033-.028-.028-.074-.035-.08.012-.076.03-.068.017-.093.037-.091.096-.08.129-.056.133-.018-.213.135-.21.21-.153.217-.04.081.015.063.03zM45.694 83.43l.077.063.035.035-.016.007-.033-.014-.056-.01-.007.034.03.051-.058-.01-.142-.093-.037-.089.011-.054-.07-.007-.088.026-.049-.068-.035-.11h-.065l-.08.056-.016-.056.054-.152.07-.082h.088l.1.014.087.054.07.157.047-.007.016-.059.03.04zm-.615.01.116-.005.024.037-.133.073-.056.044-.077.087-.072.147-.11.08-.154-.103-.137-.155-.133-.105-.292-.314-.019-.02-.025-.047v-.03l.014.001.014-.016-.014-.047-.07-.08-.084-.119.002-.058.077.019.07.06.133.166.063.05.133.072.254.113.121.032.091.01.068.04.032.058-.151.007-.023.103.058.096.086-.023.089-.099zm-.674-.925-.07-.023-.093-.092.014-.115.088-.07.105.014.08.087-.005.094-.077.075zm-.43.283-.07-.051-.034-.01-.033-.011-.04-.059-.004-.082-.065-.023-.103-.019-.019-.028.03.01-.006-.036-.063-.086-.11-.073-.13.007-.059.035-.032.03-.056.02.002-.076.072-.11.112-.032.135.049.36.24.04.062.036.093.11.068.114.042.164.1.256.216.063.073-.012.028-.028.026.01.035-.04-.021-.112-.09-.098-.06-.074-.019-.03-.051-.038-.09-.068-.032-.046.021-.066-.021zm.803-.414-.01-.23-.009-.051v-.091l.047-.063.07-.028.063.03.035.08.005.074-.02.059-.032.002-.044-.035-.01.014.02.059-.005.028-.017-.01.05.113.05.215-.032.073-.082-.127-.046-.033zm-1.579-.187-.01.049-.041.093-.06.04-.05-.026-.095-.07-.581-.3-.156-.037-.05.113-.072-.038-.137-.133-.093-.11-.047-.171.06-.14.18-.188.08-.1.06-.038.012.052-.042.093-.***************.***************.***************.***************.***************.*************.06-.***************.***************.031.063-.007.011-.007-.023-.01.005-.034.032-.033.06.01zm-1.007-1.285-.012-.066-.019-.17.02-.335.116-.279.184-.173.051-.021-.098.096-.079.126-.06.138-.***************.**************.052.007-.005.054-.063.107-.***************.***************.084-.018.025.014-.04.067-.072.038-.09-.016-.145-.117-.082-.052z",mask:"url(#mapSaudi_svg__a)"})),i||(i=AV.createElement("g",{clipPath:"url(#mapSaudi_svg__b)"},AV.createElement("path",{fill:"#FFCA00",d:"m105.823 79.868-4.325 4.324-4.324-4.324a6.116 6.116 0 1 1 8.649 0m-4.325-2.965a1.36 1.36 0 1 0 0-2.72 1.36 1.36 0 0 0 0 2.72"}))),n||(n=AV.createElement("path",{fill:"#E5F0FC",d:"M65.497 98.565c-1.666 0-2.884-.602-3.682-1.484l.798-1.079c.63.7 1.638 1.317 2.94 1.317 1.512 0 2.044-.77 2.044-1.443 0-.98-1.064-1.26-2.254-1.568-1.498-.392-3.234-.826-3.234-2.73 0-1.54 1.358-2.66 3.304-2.66 1.442 0 2.576.462 3.402 1.302l-.812 1.037c-.714-.757-1.694-1.093-2.702-1.093-1.036 0-1.75.532-1.75 1.317 0 .84 1.008 1.106 2.17 1.4 1.526.406 3.304.882 3.304 2.87 0 1.456-1.008 2.813-3.528 2.813m10.634-.168h-1.26v-.743c-.532.589-1.302.91-2.212.91-1.12 0-2.352-.755-2.352-2.24 0-1.54 1.218-2.212 2.352-2.212.91 0 1.694.294 2.212.897v-1.12c0-.84-.686-1.344-1.652-1.344-.77 0-1.428.293-2.03.895l-.546-.867c.756-.742 1.68-1.106 2.772-1.106 1.47 0 2.716.644 2.716 2.365zm-2.996-.715c.7 0 1.358-.266 1.736-.77v-1.148c-.378-.504-1.036-.77-1.736-.77-.91 0-1.554.546-1.554 1.344 0 .799.644 1.345 1.554 1.345m10.919.715h-1.26v-.925c-.518.56-1.372 1.093-2.408 1.093-1.442 0-2.156-.728-2.156-2.156v-4.775h1.26v4.355c0 1.12.56 1.456 1.428 1.456.784 0 1.498-.448 1.876-.952v-4.858h1.26zm8.152 0h-1.26v-.967c-.504.672-1.316 1.134-2.212 1.134-1.764 0-3.024-1.33-3.024-3.541 0-2.17 1.26-3.556 3.024-3.556.868 0 1.666.42 2.212 1.148v-3.556h1.26zm-3.136-.953c.77 0 1.526-.434 1.876-.98v-2.883c-.35-.547-1.106-.995-1.876-.995-1.26 0-2.044 1.023-2.044 2.436 0 1.4.784 2.422 2.044 2.422m5.85-6.706a.82.82 0 0 1-.812-.811.81.81 0 0 1 .812-.799.8.8 0 0 1 .812.799.813.813 0 0 1-.812.811m.644 7.659h-1.26v-6.763h1.26zm4.86-2.843h-3.36v-1.091h3.36zm9.737 2.843h-1.596l-.756-1.933h-4.466l-.756 1.933h-1.596l3.71-9.339h1.75zm-2.758-3.178-1.834-4.732-1.82 4.732zm5.19 3.178h-1.26v-6.763h1.26v1.037c.518-.672 1.316-1.19 2.212-1.19v1.273a2 2 0 0 0-.434-.041c-.63 0-1.484.476-1.778.98zm8.983 0h-1.26v-.743c-.532.589-1.302.91-2.212.91-1.12 0-2.352-.755-2.352-2.24 0-1.54 1.218-2.212 2.352-2.212.91 0 1.694.294 2.212.897v-1.12c0-.84-.686-1.344-1.652-1.344-.77 0-1.428.293-2.03.895l-.546-.867c.756-.742 1.68-1.106 2.772-1.106 1.47 0 2.716.644 2.716 2.365zm-2.996-.715c.7 0 1.358-.266 1.736-.77v-1.148c-.378-.504-1.036-.77-1.736-.77-.91 0-1.554.546-1.554 1.344 0 .799.644 1.345 1.554 1.345m6.355-1.218c.336.546 1.106.98 1.876.98 1.26 0 2.044-1.008 2.044-2.422 0-1.413-.784-2.436-2.044-2.436-.77 0-1.54.463-1.876 1.008zm0 1.933h-1.26v-9.339h1.26v3.557c.532-.728 1.33-1.148 2.212-1.148 1.764 0 3.01 1.386 3.01 3.555 0 2.212-1.26 3.542-3.01 3.542-.91 0-1.708-.461-2.212-1.133zm7.529-7.659a.82.82 0 0 1-.812-.811.81.81 0 0 1 .812-.799.8.8 0 0 1 .812.799.813.813 0 0 1-.812.811m.644 7.659h-1.26v-6.763h1.26zm7.52 0h-1.26v-.743c-.532.589-1.302.91-2.212.91-1.12 0-2.352-.755-2.352-2.24 0-1.54 1.218-2.212 2.352-2.212.91 0 1.694.294 2.212.897v-1.12c0-.84-.686-1.344-1.652-1.344-.77 0-1.428.293-2.03.895l-.546-.867c.756-.742 1.68-1.106 2.772-1.106 1.47 0 2.716.644 2.716 2.365zm-2.996-.715c.7 0 1.358-.266 1.736-.77v-1.148c-.378-.504-1.036-.77-1.736-.77-.91 0-1.554.546-1.554 1.344 0 .799.644 1.345 1.554 1.345"})),s||(s=AV.createElement("path",{fill:"#fff",d:"M12.072 24.488a.9.9 0 0 1-.576-.144.78.78 0 0 1-.336-.384 4 4 0 0 1-.312-.528 2.1 2.1 0 0 1-.264-.672 25 25 0 0 1-.096-1.488q0-.72.072-2.016a10 10 0 0 1 .288-1.872q.216-.84.432-1.416.216-.6.336-.888.12-.312.048-.312-.096 0-.216.192a10 10 0 0 0-.24.456q-.12.288-.288.624-.144.336-.312.648a24 24 0 0 1-.792 1.584 37 37 0 0 1-.888 1.584q-.456.768-.792 1.344-.216.24-.504.696-.288.432-.624.912t-.696.792q-.528.504-1.08.6a1.52 1.52 0 0 1-1.032-.12q-.24-.024-.504-.264a1.7 1.7 0 0 1-.432-.552q-.144-.336-.072-.624a11 11 0 0 1 0-1.032q.048-.72.144-1.56.096-.864.192-1.656.12-.792.216-1.32.216-1.536.576-3.144a73 73 0 0 0 .696-3.24q.192-.48.336-1.08.168-.624.336-1.152.024-.528.384-.648a1.25 1.25 0 0 1 .768-.048q.432.096.6.336.24.24.456.744t-.12 1.272q-.48 1.176-.936 2.88-.432 1.68-.792 4.08l-.216 1.296q-.096.696-.192 1.344l-.12 1.08q-.024.408.024.432.192.048.408-.216.24-.264.504-.84a75 75 0 0 0 1.848-3.24 107 107 0 0 0 1.776-3.36q.408-.696.816-1.512.432-.84.816-1.536l.792-1.392q.384-.672.672-1.128.288-.48.432-.528.648 0 .96.336.336.336.528.816.048.12.048.408.024.288-.072.504-.024.168-.216.696-.168.528-.312 1.176-.24.648-.432 1.296-.168.624-.24 1.032-.192 1.032-.48 2.256t-.504 2.448q-.192 1.2-.216 2.136-.024.672 0 .888.048.192.144.408.048.192.216.096t.336-.336q.336-.504.504-.864.192-.36.384-.768.192-.432.528-1.08.6-1.104 1.224-2.424.648-1.32 1.224-2.616.6-1.296 1.032-2.304.456-1.296.768-2.088.312-.816.576-1.248t.504-.576q.12-.096.504-.096.408 0 .624.192.096.096.336.312t.216.504q-.12.384-.456 1.248a50 50 0 0 1-.744 1.92 85 85 0 0 1-.816 1.992q-.384.936-.648 1.488a10 10 0 0 1-.504.864q-.24.36-.24.552 0 .096-.24.6-.24.48-.624 1.176-.36.696-.792 1.44t-.816 1.368-.648.96q-.6.744-1.296 1.248-.672.528-1.2.792t-.696.264m9.264-2.4q-.168.024-.384-.12a5 5 0 0 0-.36-.288 2.3 2.3 0 0 1-.336-.504 1.2 1.2 0 0 1-.096-.48q0-.264.048-.624.12-.384.288-1.032t.408-1.392q.048-.288.168-.672.144-.408.288-.768l.216-.576q.168-.6.336-1.152.192-.552.456-1.272t.648-1.824l.792-2.16q.264-.72.384-1.032t.168-.504q0-.192.264-.36.288-.192.504-.192.336.024.648.408.312.36.432.888.12.504-.168.984a28 28 0 0 0-.768 1.944l-.72 2.112a37 37 0 0 1-.72 1.944 2 2 0 0 1-.168.408q-.12.288-.216.48a7 7 0 0 1-.192.36l-.072.168a5 5 0 0 1-.096.408 5 5 0 0 1-.144.456q-.024.12-.072.312a3 3 0 0 0-.072.384q.12-.144.336-.336.216-.216.552-.528.384-.336.672-.576t.576-.432q.264-.192.48-.36l.24-.168a2 2 0 0 0 .096-.168.3.3 0 0 1 .144-.12 1 1 0 0 1 .168-.12l.24-.192a2.5 2.5 0 0 1 .96-.384 3 3 0 0 1 .504-.048q.288 0 .6.24.312.216.576.624.24.312.288.744.072.408.024 1.104 0 .192-.12.72l-.144.792-.216.816h.192a.8.8 0 0 1 .504-.024l.264.072q.12.072.216.168.12.096.216.144.144.192-.048.552t-.432.504q-.576.384-1.032.48-.432.096-.912-.168-.456-.312-.624-.744-.144-.432-.12-1.176.096-1.056.12-1.512.024-.48-.24-.48-.288 0-.792.288a8 8 0 0 0-1.08.744q-.6.456-1.2 1.032-.576.552-1.032 1.128-.288.36-.624.72a1.2 1.2 0 0 1-.816.36m15.04.432a3.75 3.75 0 0 1-1.536.48 3.54 3.54 0 0 1-1.488-.216 2.7 2.7 0 0 1-1.128-.84q-.24-.312-.456-.984-.216-.696-.072-1.872a7.6 7.6 0 0 1 .672-2.232 8.4 8.4 0 0 1 1.344-1.992q.792-.864 1.728-1.176.336-.12.672-.168.36-.072.936-.024.552.072.984.288.432.192.624.432.384.384.504.744t.096.696q-.12.768-.696 1.656a3.33 3.33 0 0 1-1.56 1.32 3.3 3.3 0 0 1-.96.216q-.552.048-1.512-.144-.576-.12-.768.12-.168.24-.144.936a2.7 2.7 0 0 0 .192.768q.168.312.6.432.552.096.792.024.24-.096.6-.264.192-.12.36-.264.192-.144.36-.288.192-.144.312-.216a.6.6 0 0 1 .24-.12q.12-.024.24-.024.264.024.504.288t.312.504q0 .192-.24.552a5 5 0 0 1-.648.72q-.384.36-.864.648m-.312-5.352q.168-.072.48-.336a5 5 0 0 0 .6-.552q.264-.312.264-.48 0-.264-.36-.456-.336-.192-.888-.072-.192.048-.456.264a5 5 0 0 0-.528.504 4.5 4.5 0 0 0-.456.528q-.192.24-.264.384.096.12.384.192t.624.072q.36 0 .6-.048m5.615 5.328q-.072.024-.312-.024a7 7 0 0 1-.456-.12q-.216-.072-.264-.12a3 3 0 0 1-.264-.336q-.168-.264-.192-.696 0-.432.408-1.032.12-.24.288-.72t.336-1.008q.192-.552.336-.984.168-.432.24-.6.048-.408-.048-.576-.072-.192-.408-.216a.48.48 0 0 1-.384-.168q-.12-.168-.12-.6a1.9 1.9 0 0 1 .168-.576 1.25 1.25 0 0 1 .384-.432q.24-.12.6-.168a4 4 0 0 1 .744-.024q.36.024.504.168.168.096.264.432.12.312.192.648t.096.504q.24-.192.528-.528.312-.36.624-.672.384-.264.912-.528.528-.288.768-.288.264 0 .648.264.384.24.48.432.24.408.336.864a.82.82 0 0 1-.144.768q-.24.312-.672.624-.432.288-1.08.168-.144-.048-.312-.36a2 2 0 0 1-.168-.456q0-.072-.216.144l-.48.48a17 17 0 0 1-.504.528q-.408.504-.864 1.368t-1.08 2.088q-.288.6-.48 1.152-.192.528-.408.6m9.153.936q-1.32 0-2.04-.768-.696-.768-.696-2.352 0-.864.312-1.896.336-1.032.864-2.016a9.4 9.4 0 0 1 1.2-1.704q.648-.744 1.296-1.032.24-.12.504-.168.288-.072.552-.072.288 0 .624.048.36.024.672.144.552.168.96.648.432.48.432 1.392 0 .24-.048.528a2.3 2.3 0 0 1-.168.528 5 5 0 0 1-.96 1.512q-.624.72-1.32 1.008a2 2 0 0 1-.384.144 3 3 0 0 1-.36.024q-.504 0-.912-.144a3.1 3.1 0 0 1-.72-.384l-.192-.168-.12.24q-.168.456-.24.768-.048.312-.048.456 0 .288.024.6.024.288.144.48.096.12.24.192.168.072.408.072h.264q.144-.024.24-.048.432-.096.696-.312.288-.24.6-.408l.096-.048-.024-.072q.408-.216.552-.264.144-.072.216-.12a.3.3 0 0 1 .216-.096q.336-.048.648.192.336.216.336.552 0 .144-.216.456a3.5 3.5 0 0 1-.504.624 2.6 2.6 0 0 1-.6.456.6.6 0 0 0-.144.072.6.6 0 0 0-.144.072h.024q-.216.288-.432.288h-.192q-.096.168-.624.36a2.7 2.7 0 0 1-1.032.216m1.248-5.784a1 1 0 0 0 .384-.072q.192-.072.36-.168.576-.408.744-1.056t-.12-.984a.23.23 0 0 0-.12-.096.3.3 0 0 0-.168-.048q-.264 0-.6.168a2.7 2.7 0 0 0-.624.48q-.312.312-.576.744l-.312.504a.2.2 0 0 1 .072-.024.2.2 0 0 1 .072-.024q.216 0 .288.144t.168.288q.12.144.432.144"})),r||(r=AV.createElement("path",{fill:"#FFCA00",d:"M103.046 62.394a.5.5 0 0 0 .702.086l3.542-2.775a.5.5 0 1 0-.616-.788l-3.149 2.467-2.467-3.149a.5.5 0 1 0-.787.617zM65.183 23.326c12.373-3.546 22.497.242 29.191 7.828 6.713 7.608 10.003 19.07 8.569 30.872l.993.12c1.463-12.044-1.884-23.802-8.812-31.653-6.947-7.872-17.468-11.781-30.216-8.128z"})),c||(c=AV.createElement("defs",null,AV.createElement("clipPath",{id:"mapSaudi_svg__b"},AV.createElement("path",{fill:"#fff",d:"M93.344 68.068h16.308v16.308H93.344z"})))));function AR(){return(AR=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let AC=A=>AV.createElement("svg",AR({xmlns:"http://www.w3.org/2000/svg",width:177,height:229,fill:"none"},A),g||(g=AV.createElement("mask",{id:"mapiconTunisia_svg__a",fill:"#fff"},AV.createElement("path",{d:"m151.318 149.057.021 1.231-.65 2.611-.058.423-.011 1.534-.408 1.859.166 2.838-.333 2.885.019 1.505.466 1.055 1.525 1.523.527.979.08 1.146-.405.851-.462.446-.279.27-1.59 1.077-5.955 2.893-7.036 3.411-.622.676-.918 2.154-.744.756-.828.519-.762.628-.445.139-.415-.168-.408-.238-.427-.058-.513.555-.192 2.169-.296.899-.961.909-.383.935-.275.405-.332.344-.373.303-1.246.5-2.559-.033-1.171.957-1.535 2.754-1.453 1.647-.358.624-.242 1.761.043.467 1.699 4.605.694 2.834.719 1.538.029.5-.127 1.03.101.953.571 1.881-.011.898-.361 1.081-1.406 2.958-2.074 2.396-2.403 3.933-2.83 3.718-.581.344-1.659.113-.672.179-5.085 2.742-.643-2.932-.643-2.936-.644-2.937-.646-2.94-.647-2.947-.643-2.947-.644-2.951-.643-2.954-.65-2.958-.644-2.962-.647-2.966-.643-2.969-.643-2.973-.647-2.972-.643-2.98-.65-2.984-.062-.285-.416-1.943-.256-.515-.315-.383-3.83-2.809-4.763-3.495-3.635-2.673-2.794-1.804-.621-.69-.033-.037-.267-.898-.22-2.82-.308-3.842-.307-.876-2.31-3.656-2.153-3.414-.51-.314-.477.109-.477-.033-.466-.146-.44-.234-2.523-1.848-.831-.284-1.092-.103-.838-.255-.585-.654-.347-1.285-.033-3.649v-.113l-.343-1.081-1.742-3.086-1.445-2.564-.196-.705-.23-1.439-.655-1.289-.159-.474-.31-1.523-.124-1.096.474-2.783.072-2.045.314-.752.496-.519.917-.964.557-.752.625-.658.885-.449 1.446-.237.43-.234.285-.351.207-.452.856-3.295.448-.978.889-.658 1.973-.88.95-.69 2.162-1.004.191-.176.936-.847.535-.332.18-.187.098-.193.16-.486.144-.226.82-.523.173-.314-.589-.938.065-.435.268-.416.73-.785.032-.044.177-.252.066-.38-.423-2.827-.044-1.044.232-1.085.542-1.497.4-2.067.3-.993 1.078-1.206 1.376-2.143.228-.552-.784-.617-.947-.3-.86-.427-.524-1.004-.144-1.154.046-1.03.246-.986.817-2.001.007-.837-.224-2.359-.083-.873-.387-.927-1.116-1.86-.271-.993-.098-1.205.13-2.26.484-1.779.09-.336.073-1.567.48-2.092.257-.471.206-.54-.058-.516-.17-.53-.108-.565.108-1.176 1.077-3.06.022-.15.062-.395.036-1.18-.044-.547-.191-.526-.35-.216-.43-.12-.442-.23-1.976-.263-.933-.387v-.019l.047-.967.535-.661.658-.424 1.47-.562 2.754-1.699.51-.573.52-1.004.195-1.008-.459-.585-.795-.423.145-.457.705-.354.878-.11.853.059.784-.165 1.579-.642.408-.296.018-.35-.78-1.213-.076-.296.018-.347-.072-.763.422-.18 1.616-.678.556-.106h1.945l.51-.183.939-.81.69-.326.173-.08.503-.497.708-.876.701-1.373.303-.205.225-.113.636-.694.242-.15.686-.285 1.814-.752.279-.172.477-.788.415-.223 3-.044.867-.35.556-.607.318-.212.308.128.235.139.249-.051.242-.12.669-.165.534-.248.485-.329.321-.332.463.266.419-.077.842-.584.473-.252.387-.102 1.001-.022.502-.208.286-.048.238.154.185.182.235.092 1.944.05.003.085.047.274.011 1.307-.704.577-.864.38-.477.712.076.22.354.792.112.175.134.091.141.41.184.09.997.14.459-.088.506-.428.278-.387.25-.533-.008-.47-1.449-.603-.216.029-.47.146-.26.004v-.18l.552-.394.217-.058.138-.136.097-.142.08-.077.343-.054 2.215.507.409-.04 1.217-.551.196.05.364.574 1.601.245.741.686 1.695.618.434.372-.73.194-.336.146-.318.234-.123-.475-.633-.084-.726.193-.408.362.166.47.596.231.69.033.441-.124.159.248.203.325.075.278-.133.35-.156-.413-.983.983.185 1.417.856 1.453 1.045 1.085.849.416.238.172.532.767.231.332-.119.336-.524.526-.221.588-.238.23-.311.051-.296-.262.169-.439-.213-.12-.394.062-.741.245-.224.171-.39.563-.116.423.329.143.712.022.297.16.151-.15.069-.069.343-.639.488 1.103.604.833.802.504 1.084.127.314-.08.459.022 1.157-.599.704-.175.445-.468.177-.8.148-.66.325-1.158.412-.373.481-.102 1.059.08.957-.19.882-.354 1.131-.822.701-.748.206-.102.087-.267.072-.146 1.308-1.088.188-.234.058-.322-.087-.2-.039-.187.209-.292.492-.237 1.149-.12.878-.68.47-.077.401.208.21.515-.033.249-.235.343-.058.303.051.322.224.412.051.26.083.2.394.596.144.288.054.281.04.617.062.282.104.226.156.26.162.211.12.088.133.157.271.434-.13.475-.314.45-.911.38-1.243 1.058-.672 1.271-.969 1.44-1.604 3.02-1.225 3.111-.586.8-1.882.497-2.201 1.267-.795-.223-.788.595-.726 1.136-.325 1.088-.315 1.18-.267 1.172-.054 1.465-.051 1.512.108.942.293.968.17.515.112.178.101.278.13.628.087.26.499.77.361.424.314.182.271.278.6 1.873.307.377 1.576 1.504.054.051.441.241.694.27 1.37-.489.231.055.209.128.416.394-.253.376-.134.563.116.573.264.406.918.445 2.056.818.914-.106.662.57.318.657-.864.344-.116.657.044.457.021.219.206.606.347.43 1.045.333-.622.566-.159.76-.116.708.087 1.227-.278.577.115 1.027.539.88.806 1.026.473.373.571.27.311.164-.036.548-.633.022-.451.347-.438.89-.668.57-.322.632-.314.68-.434.398.148.796-.097.85-.77.976-.878.445-.643.735.166.653-.192.585-.003 1.113-.929.687-.199.219-.144.124-.094.168.253.555.094.384-.831.391-.539.405-.734.822-.354.73-.444.413-.21.5-.397.449-.365.245-1.522.504-.206.131-.086.194-.036.292-.113.344-.173.284-.04.548-.159.314-.039.497-.351.336-.506.256-2.125.321-.777.435-1.21 2.045-1.099.292-.228-.018-.209-.099-.257-.026-.271.103-.137.142-.09.172-.488.657-.214.194-.296.076-1.08.486-.459.365-.911.369-.56.599-.282.866-.813 1.665-.484.792-.318.114-.195.204-.004.004-.112.069.213.479.087.551-.004 1.165.076.508.333 1.077.072.511.217.906.509 1.034 2.089 3.049.217.226.321.176 3.264 3.29.852.519 1.786.792.321.077.662.153 1.572-.237 1.723-.497 1.446-.799.433-.121.532.979-.069 1.899-.669.584-.365.154-.281.372-.112.467.133.435.539.931.307.344.477.153.965-.212 1.724-1.063.82-.248.39-.175.358-.427.173-.523-.148-.474-.39-.461-.062-.237.676-.548.268.19.289.051.245-.117.145-.31h.155l.365.248.962.449.397.263.304.384.788 1.238.079.267.116 1.38-.607.99.473 1.687-.199.796.661-.204.452.485.452.61.528.296.545.128 1.167.606.561.205-.145.131-.065.033-.101.022-2.233-.738-1.099-.128-.267.866.22.328.369.307.354.395.155.595.213.274.499-.004.997-.186.969.215 1.922.724h1.012l.372-.271-.126-.369-.387-.328-.408-.143-.26-.182-.318-.402-.354-.373 2.059 1.491 1.262.602.155.037zm-11.691-18.224.445.281.459.216.397.252.267.387-1.51 1.41-.38.182-.14.124-.105.231-.224.902h-.141l.072-.731.069-.23-.354.117-.286.873-.408.628-.383.431-.134.5-.379-.391-.008-.482.141-.452-.408-.632-.383-.19-.362-.329-.393-.124-.423.435-.192.412-.423.099-.531-.424-.144-.42-.138-.759.011-.731.437-.781-.274-1.136.011-.858.842-.322 1.85.406.99.065.73-.471.679.837zm-.625-22.679.22-.175.304-.245 2.598.022.217.321-.34.351-.914.727-.437-.095-.452-.23-.755-.376zm6.87-3.265.**************.553.577-.177.38-2.216.95-1.149.712-.419.168-.192-.479.315-.376.376-.365.408-.606.058-.387-.018-.179.018-.26.181-.105.05.27.343.23.546-.102.051-.629-.156-.325.177-.379.271-.252.185-.303.206-.304.375.223.655.102.108.249-.593.212-.083.168-.141.179z"}))),h||(h=AV.createElement("path",{stroke:"#fff",strokeWidth:2,d:"m151.318 149.057.021 1.231-.65 2.611-.058.423-.011 1.534-.408 1.859.166 2.838-.333 2.885.019 1.505.466 1.055 1.525 1.523.527.979.08 1.146-.405.851-.462.446-.279.27-1.59 1.077-5.955 2.893-7.036 3.411-.622.676-.918 2.154-.744.756-.828.519-.762.628-.445.139-.415-.168-.408-.238-.427-.058-.513.555-.192 2.169-.296.899-.961.909-.383.935-.275.405-.332.344-.373.303-1.246.5-2.559-.033-1.171.957-1.535 2.754-1.453 1.647-.358.624-.242 1.761.043.467 1.699 4.605.694 2.834.719 1.538.029.5-.127 1.03.101.953.571 1.881-.011.898-.361 1.081-1.406 2.958-2.074 2.396-2.403 3.933-2.83 3.718-.581.344-1.659.113-.672.179-5.085 2.742-.643-2.932-.643-2.936-.644-2.937-.646-2.94-.647-2.947-.643-2.947-.644-2.951-.643-2.954-.65-2.958-.644-2.962-.647-2.966-.643-2.969-.643-2.973-.647-2.972-.643-2.98-.65-2.984-.062-.285-.416-1.943-.256-.515-.315-.383-3.83-2.809-4.763-3.495-3.635-2.673-2.794-1.804-.621-.69-.033-.037-.267-.898-.22-2.82-.308-3.842-.307-.876-2.31-3.656-2.153-3.414-.51-.314-.477.109-.477-.033-.466-.146-.44-.234-2.523-1.848-.831-.284-1.092-.103-.838-.255-.585-.654-.347-1.285-.033-3.649v-.113l-.343-1.081-1.742-3.086-1.445-2.564-.196-.705-.23-1.439-.655-1.289-.159-.474-.31-1.523-.124-1.096.474-2.783.072-2.045.314-.752.496-.519.917-.964.557-.752.625-.658.885-.449 1.446-.237.43-.234.285-.351.207-.452.856-3.295.448-.978.889-.658 1.973-.88.95-.69 2.162-1.004.191-.176.936-.847.535-.332.18-.187.098-.193.16-.486.144-.226.82-.523.173-.314-.589-.938.065-.435.268-.416.73-.785.032-.044.177-.252.066-.38-.423-2.827-.044-1.044.232-1.085.542-1.497.4-2.067.3-.993 1.078-1.206 1.376-2.143.228-.552-.784-.617-.947-.3-.86-.427-.524-1.004-.144-1.154.046-1.03.246-.986.817-2.001.007-.837-.224-2.359-.083-.873-.387-.927-1.116-1.86-.271-.993-.098-1.205.13-2.26.484-1.779.09-.336.073-1.567.48-2.092.257-.471.206-.54-.058-.516-.17-.53-.108-.565.108-1.176 1.077-3.06.022-.15.062-.395.036-1.18-.044-.547-.191-.526-.35-.216-.43-.12-.442-.23-1.976-.263-.933-.387v-.019l.047-.967.535-.661.658-.424 1.47-.562 2.754-1.699.51-.573.52-1.004.195-1.008-.459-.585-.795-.423.145-.457.705-.354.878-.11.853.059.784-.165 1.579-.642.408-.296.018-.35-.78-1.213-.076-.296.018-.347-.072-.763.422-.18 1.616-.678.556-.106h1.945l.51-.183.939-.81.69-.326.173-.08.503-.497.708-.876.701-1.373.303-.205.225-.113.636-.694.242-.15.686-.285 1.814-.752.279-.172.477-.788.415-.223 3-.044.867-.35.556-.607.318-.212.308.128.235.139.249-.051.242-.12.669-.165.534-.248.485-.329.321-.332.463.266.419-.077.842-.584.473-.252.387-.102 1.001-.022.502-.208.286-.048.238.154.185.182.235.092 1.944.05.003.085.047.274.011 1.307-.704.577-.864.38-.477.712.076.22.354.792.112.175.134.091.141.41.184.09.997.14.459-.088.506-.428.278-.387.25-.533-.008-.47-1.449-.603-.216.029-.47.146-.26.004v-.18l.552-.394.217-.058.138-.136.097-.142.08-.077.343-.054 2.215.507.409-.04 1.217-.551.196.05.364.574 1.601.245.741.686 1.695.618.434.372-.73.194-.336.146-.318.234-.123-.475-.633-.084-.726.193-.408.362.166.47.596.231.69.033.441-.124.159.248.203.325.075.278-.133.35-.156-.413-.983.983.185 1.417.856 1.453 1.045 1.085.849.416.238.172.532.767.231.332-.119.336-.524.526-.221.588-.238.23-.311.051-.296-.262.169-.439-.213-.12-.394.062-.741.245-.224.171-.39.563-.116.423.329.143.712.022.297.16.151-.15.069-.069.343-.639.488 1.103.604.833.802.504 1.084.127.314-.08.459.022 1.157-.599.704-.175.445-.468.177-.8.148-.66.325-1.158.412-.373.481-.102 1.059.08.957-.19.882-.354 1.131-.822.701-.748.206-.102.087-.267.072-.146 1.308-1.088.188-.234.058-.322-.087-.2-.039-.187.209-.292.492-.237 1.149-.12.878-.68.47-.077.401.208.21.515-.033.249-.235.343-.058.303.051.322.224.412.051.26.083.2.394.596.144.288.054.281.04.617.062.282.104.226.156.26.162.211.12.088.133.157.271.434-.13.475-.314.45-.911.38-1.243 1.058-.672 1.271-.969 1.44-1.604 3.02-1.225 3.111-.586.8-1.882.497-2.201 1.267-.795-.223-.788.595-.726 1.136-.325 1.088-.315 1.18-.267 1.172-.054 1.465-.051 1.512.108.942.293.968.17.515.112.178.101.278.13.628.087.26.499.77.361.424.314.182.271.278.6 1.873.307.377 1.576 1.504.054.051.441.241.694.27 1.37-.489.231.055.209.128.416.394-.253.376-.134.563.116.573.264.406.918.445 2.056.818.914-.106.662.57.318.657-.864.344-.116.657.044.457.021.219.206.606.347.43 1.045.333-.622.566-.159.76-.116.708.087 1.227-.278.577.115 1.027.539.88.806 1.026.473.373.571.27.311.164-.036.548-.633.022-.451.347-.438.89-.668.57-.322.632-.314.68-.434.398.148.796-.097.85-.77.976-.878.445-.643.735.166.653-.192.585-.003 1.113-.929.687-.199.219-.144.124-.094.168.253.555.094.384-.831.391-.539.405-.734.822-.354.73-.444.413-.21.5-.397.449-.365.245-1.522.504-.206.131-.086.194-.036.292-.113.344-.173.284-.04.548-.159.314-.039.497-.351.336-.506.256-2.125.321-.777.435-1.21 2.045-1.099.292-.228-.018-.209-.099-.257-.026-.271.103-.137.142-.09.172-.488.657-.214.194-.296.076-1.08.486-.459.365-.911.369-.56.599-.282.866-.813 1.665-.484.792-.318.114-.195.204-.004.004-.112.069.213.479.087.551-.004 1.165.076.508.333 1.077.072.511.217.906.509 1.034 2.089 3.049.217.226.321.176 3.264 3.29.852.519 1.786.792.321.077.662.153 1.572-.237 1.723-.497 1.446-.799.433-.121.532.979-.069 1.899-.669.584-.365.154-.281.372-.112.467.133.435.539.931.307.344.477.153.965-.212 1.724-1.063.82-.248.39-.175.358-.427.173-.523-.148-.474-.39-.461-.062-.237.676-.548.268.19.289.051.245-.117.145-.31h.155l.365.248.962.449.397.263.304.384.788 1.238.079.267.116 1.38-.607.99.473 1.687-.199.796.661-.204.452.485.452.61.528.296.545.128 1.167.606.561.205-.145.131-.065.033-.101.022-2.233-.738-1.099-.128-.267.866.22.328.369.307.354.395.155.595.213.274.499-.004.997-.186.969.215 1.922.724h1.012l.372-.271-.126-.369-.387-.328-.408-.143-.26-.182-.318-.402-.354-.373 2.059 1.491 1.262.602.155.037zm-11.691-18.224.445.281.459.216.397.252.267.387-1.51 1.41-.38.182-.14.124-.105.231-.224.902h-.141l.072-.731.069-.23-.354.117-.286.873-.408.628-.383.431-.134.5-.379-.391-.008-.482.141-.452-.408-.632-.383-.19-.362-.329-.393-.124-.423.435-.192.412-.423.099-.531-.424-.144-.42-.138-.759.011-.731.437-.781-.274-1.136.011-.858.842-.322 1.85.406.99.065.73-.471.679.837zm-.625-22.679.22-.175.304-.245 2.598.022.217.321-.34.351-.914.727-.437-.095-.452-.23-.755-.376zm6.87-3.265.**************.553.577-.177.38-2.216.95-1.149.712-.419.168-.192-.479.315-.376.376-.365.408-.606.058-.387-.018-.179.018-.26.181-.105.05.27.343.23.546-.102.051-.629-.156-.325.177-.379.271-.252.185-.303.206-.304.375.223.655.102.108.249-.593.212-.083.168-.141.179z",mask:"url(#mapiconTunisia_svg__a)"})),m||(m=AV.createElement("g",{clipPath:"url(#mapiconTunisia_svg__b)"},AV.createElement("path",{fill:"#FFCA00",stroke:"#FFCA00",d:"M100.19 111.414c2.226-2.094 5.801-2.159 8.111-.196l.22.196c2.208 2.079 2.277 5.397.207 7.553l-.207.205-4.166 3.919-4.165-3.919c-2.208-2.079-2.277-5.397-.207-7.553zm4.165 2.046c-1.029 0-1.917.792-1.917 1.833s.888 1.833 1.917 1.833c1.03 0 1.917-.792 1.917-1.833s-.887-1.833-1.917-1.833Z"}))),d||(d=AV.createElement("path",{fill:"#fff",d:"M85.713 150.961h-1.755v-8.46h-3.03v-1.545h7.815v1.545h-3.03zm9.89 0h-1.576v-.96a3.47 3.47 0 0 1-2.565 1.14c-1.56 0-2.325-.81-2.325-2.295v-5.13h1.575v4.56c0 1.125.57 1.47 1.455 1.47.795 0 1.485-.45 1.86-.945v-5.085h1.575zm8.59 0h-1.575v-4.53c0-1.125-.57-1.5-1.455-1.5-.81 0-1.5.48-1.86.975v5.055h-1.575v-7.245h1.575v.99c.48-.57 1.41-1.17 2.55-1.17 1.56 0 2.34.84 2.34 2.325zm2.906-8.13a.983.983 0 0 1-.975-.975c0-.54.45-.975.975-.975.54 0 .975.435.975.975s-.435.975-.975.975m.795 8.13h-1.575v-7.245h1.575zm4.598.18c-1.23 0-2.355-.39-3.105-1.125l.72-1.14c.51.525 1.515 1.02 2.46 1.02s1.425-.405 1.425-.975c0-1.425-4.38-.405-4.38-3.18 0-1.185 1.02-2.205 2.85-2.205 1.23 0 2.16.435 2.82 1.02l-.66 1.11c-.435-.495-1.245-.87-2.145-.87-.825 0-1.35.375-1.35.885 0 1.29 4.38.33 4.38 3.21 0 1.275-1.065 2.25-3.015 2.25m5.46-8.31a.983.983 0 0 1-.975-.975c0-.54.45-.975.975-.975.54 0 .975.435.975.975s-.435.975-.975.975m.795 8.13h-1.575v-7.245h1.575zm5.468.18c-2.175 0-3.795-1.515-3.795-3.81 0-2.1 1.53-3.795 3.675-3.795 2.175 0 3.57 1.665 3.57 3.945v.375h-5.595c.12 1.095.945 1.995 2.31 1.995.705 0 1.545-.285 2.055-.795l.72 1.035c-.72.69-1.785 1.05-2.94 1.05m1.92-4.395c-.03-.855-.615-1.92-2.04-1.92-1.35 0-1.965 1.035-2.04 1.92zM10.06 22.24a.76.76 0 0 1-.48-.12.65.65 0 0 1-.28-.32 3.4 3.4 0 0 1-.26-.44q-.18-.32-.22-.56-.06-.64-.08-1.24 0-.6.06-1.68a8.4 8.4 0 0 1 .24-1.56q.18-.7.36-1.18.18-.5.28-.74.1-.26.04-.26-.08 0-.18.16-.08.14-.2.38-.1.24-.24.52a7 7 0 0 1-.26.54q-.28.62-.66 1.32-.36.68-.74 1.32t-.66 1.12q-.18.2-.42.58-.24.36-.52.76t-.58.66q-.44.42-.9.5a1.27 1.27 0 0 1-.86-.1q-.2-.02-.42-.22a1.4 1.4 0 0 1-.36-.46.8.8 0 0 1-.06-.52 9 9 0 0 1 0-.86q.04-.6.12-1.3.08-.72.16-1.38.1-.66.18-1.1.18-1.28.48-2.62.32-1.34.58-2.7.16-.4.28-.9.14-.52.28-.96.02-.44.32-.54.3-.12.64-.04.36.08.5.28.2.2.38.62t-.1 1.06a20 20 0 0 0-.78 2.4q-.36 1.4-.66 3.4l-.18 1.08q-.08.58-.16 1.12l-.1.9q-.02.34.02.36.16.04.34-.18.2-.22.42-.7a63 63 0 0 0 1.54-2.7q.74-1.34 1.48-2.8.34-.58.68-1.26.36-.7.68-1.28l.66-1.16q.32-.56.56-.94.24-.4.36-.44.54 0 .8.28.28.28.44.68.04.1.04.34a.85.85 0 0 1-.06.42q-.02.14-.18.58-.14.44-.26.98-.2.54-.36 1.08-.14.52-.2.86-.16.86-.4 1.88t-.42 2.04q-.16 1-.18 1.78-.02.56 0 .74.04.16.12.34.04.16.18.08a.9.9 0 0 0 .28-.28q.28-.42.42-.72.16-.3.32-.64.16-.36.44-.9.5-.92 1.02-2.02.54-1.1 1.02-2.18.5-1.08.86-1.92.38-1.08.64-1.74.26-.68.48-1.04t.42-.48q.1-.08.42-.08.34 0 .52.16.08.08.28.26a.49.49 0 0 1 .18.42q-.1.32-.38 1.04-.26.72-.62 1.6a71 71 0 0 1-.68 1.66q-.32.78-.54 1.24-.22.42-.42.72t-.2.46q0 .08-.2.5-.2.4-.52.98-.3.58-.66 1.2t-.68 1.14a9 9 0 0 1-.54.8q-.5.62-1.08 1.04a6 6 0 0 1-1 .66q-.44.22-.58.22m7.72-2q-.14.02-.32-.1a4 4 0 0 0-.3-.24q-.2-.24-.28-.42a1 1 0 0 1-.08-.4q0-.22.04-.52.1-.32.24-.86t.34-1.16a4 4 0 0 1 .14-.56q.12-.34.24-.64l.18-.48q.14-.5.28-.96.16-.46.38-1.06t.54-1.52l.66-1.8q.22-.6.32-.86t.14-.42q0-.16.22-.3.24-.16.42-.16.28.02.54.34.26.3.36.74.1.42-.14.82-.32.72-.64 1.62l-.6 1.76q-.3.88-.6 1.62a1.6 1.6 0 0 1-.14.34 6.108 6.108 0 0 1-.34.7l-.06.14a4 4 0 0 1-.08.34q-.06.22-.12.38a6 6 0 0 1-.06.26 3 3 0 0 0-.06.32q.1-.12.28-.28.18-.18.46-.44.32-.28.56-.48t.48-.36q.22-.16.4-.3l.2-.14a1 1 0 0 0 .08-.14.27.27 0 0 1 .12-.1.8.8 0 0 1 .14-.1l.2-.16a2.1 2.1 0 0 1 .8-.32q.22-.04.42-.04.24 0 .5.2.26.18.48.52.2.26.24.62.06.34.02.92 0 .16-.1.6l-.12.66-.18.68h.16a.65.65 0 0 1 .42-.02l.22.06a1 1 0 0 1 .18.14q.1.08.18.12.12.16-.04.46t-.36.42q-.48.32-.86.4-.36.08-.76-.14a1.35 1.35 0 0 1-.52-.62q-.12-.36-.1-.98.08-.88.1-1.26.02-.4-.2-.4-.24 0-.66.24a6.5 6.5 0 0 0-.9.62q-.5.38-1 .86-.48.46-.86.94-.24.3-.52.6a1 1 0 0 1-.68.3m12.533.36q-.6.34-1.28.4a2.95 2.95 0 0 1-1.24-.18q-.58-.24-.94-.7-.2-.26-.38-.82-.18-.58-.06-1.56a6.3 6.3 0 0 1 .56-1.86q.46-.94 1.12-1.66t1.44-.98a3 3 0 0 1 .56-.14q.3-.06.78-.02.46.06.82.24.36.16.52.36.32.32.42.62t.08.58q-.1.64-.58 1.38-.46.74-1.3 1.1a2.7 2.7 0 0 1-.8.18q-.46.04-1.26-.12-.48-.1-.64.1-.14.2-.12.78.04.36.16.64.14.26.5.36.46.08.66.02.2-.08.5-.22.16-.1.3-.22.16-.12.3-.24.16-.12.26-.18a.5.5 0 0 1 .2-.1 1 1 0 0 1 .2-.02q.22.02.42.24t.26.42q0 .16-.2.46a4 4 0 0 1-.54.6q-.32.3-.72.54m-.26-4.46q.14-.06.4-.28.28-.22.5-.46.22-.26.22-.4 0-.22-.3-.38-.28-.16-.74-.06-.16.04-.38.22t-.44.42a4 4 0 0 0-.38.44 2 2 0 0 0-.22.32q.08.1.32.16t.52.06q.3 0 .5-.04m4.68 4.44q-.06.02-.26-.02a5 5 0 0 1-.38-.1.7.7 0 0 1-.22-.1 2 2 0 0 1-.22-.28q-.14-.22-.16-.58 0-.36.34-.86.1-.2.24-.6t.28-.84q.16-.46.28-.82.14-.36.2-.5.04-.34-.04-.48-.06-.16-.34-.18a.4.4 0 0 1-.32-.14q-.1-.14-.1-.5.04-.28.14-.48a1.04 1.04 0 0 1 .32-.36q.2-.1.5-.14a3.3 3.3 0 0 1 .62-.02q.3.02.42.14.14.08.22.36a3.6 3.6 0 0 1 .16.54q.06.28.08.42.2-.16.44-.44.26-.3.52-.56.32-.22.76-.44.44-.24.64-.24.219 0 .54.22.32.2.4.36.2.34.28.72a.69.69 0 0 1-.12.64 2.7 2.7 0 0 1-.56.52q-.36.24-.9.14-.12-.04-.26-.3-.12-.26-.14-.38 0-.06-.18.12l-.4.4q-.22.24-.42.44-.34.42-.72 1.14t-.9 1.74a9 9 0 0 0-.4.96q-.16.44-.34.5m7.627.78q-1.1 0-1.7-.64-.58-.64-.58-1.96 0-.72.26-1.58.279-.86.72-1.68.46-.82 1-1.42.54-.62 1.08-.86.2-.1.42-.14.24-.06.46-.06.24 0 .52.04.3.02.56.12.46.14.8.54.36.4.36 1.16 0 .2-.04.44a2 2 0 0 1-.14.44q-.26.66-.8 1.26-.52.6-1.1.84a1.6 1.6 0 0 1-.32.12q-.14.02-.3.02-.42 0-.76-.12a2.6 2.6 0 0 1-.6-.32l-.16-.14-.1.2q-.14.38-.2.64-.04.26-.04.38 0 .24.02.5.02.24.12.4a.6.6 0 0 0 .2.16q.14.06.34.06h.22q.12-.02.2-.04.36-.08.58-.26.24-.2.5-.34l.08-.04-.02-.06q.34-.18.46-.22.12-.06.18-.1a.25.25 0 0 1 .18-.08.7.7 0 0 1 .54.16q.28.18.28.46 0 .12-.18.38a3 3 0 0 1-.42.52q-.24.24-.5.38a.5.5 0 0 0-.12.06.5.5 0 0 0-.12.06h.02q-.18.24-.36.24h-.16q-.08.14-.52.3-.44.18-.86.18m1.04-4.82q.18 0 .32-.06.16-.06.3-.14.48-.34.62-.88t-.1-.82a.2.2 0 0 0-.1-.08.25.25 0 0 0-.14-.04q-.22 0-.5.14a2.3 2.3 0 0 0-.52.4 3.3 3.3 0 0 0-.48.62l-.26.42a.2.2 0 0 1 .06-.02.2.2 0 0 1 .06-.02q.18 0 .24.12t.14.24q.**********"})),o||(o=AV.createElement("path",{fill:"#FFCA00",d:"M98.235 62.012a.5.5 0 0 0 .704.073l3.494-2.835a.5.5 0 0 0-.63-.777l-3.106 2.52-2.52-3.105a.5.5 0 0 0-.777.63zM55.304 14.5l.142.48c13.772-4.08 25.137.548 32.724 9.693 7.605 9.169 11.421 22.898 9.956 36.972l.498.052.497.052c1.488-14.299-2.378-28.308-10.182-37.714-7.822-9.43-19.593-14.215-33.777-10.015z"})),b||(b=AV.createElement("defs",null,AV.createElement("clipPath",{id:"mapiconTunisia_svg__b"},AV.createElement("path",{fill:"#fff",d:"M95.856 107.961h17v16h-17z"})))));function AM(){return(AM=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let AX=A=>AV.createElement("svg",AM({xmlns:"http://www.w3.org/2000/svg",width:180,height:140,fill:"none"},A),u||(u=AV.createElement("g",{clipPath:"url(#mapiconTunisiamobile_svg__a)"},AV.createElement("mask",{id:"mapiconTunisiamobile_svg__b",fill:"#fff"},AV.createElement("path",{d:"m118.046 82.818.015.874-.462 1.854-.041.3-.007 1.09-.29 1.319.118 2.014-.236 2.048.012 1.069.331.749 1.083 1.08.375.696.056.814-.287.604-.329.316-.197.192-1.129.765-4.228 2.053-4.995 2.422-.441.479-.651 1.53-.529.537-.587.368-.542.446-.315.098-.295-.119-.29-.169-.303-.041-.364.394-.136 1.54-.211.638-.682.645-.272.664-.195.288-.236.244-.264.215-.885.355-1.816-.023-.832.679-1.09 1.955-1.031 1.169-.254.443-.172 1.25.03.332 1.207 3.269.492 2.012.51 1.091.021.355-.09.732.072.676.405 1.335-.007.638-.257.768-.998 2.1-1.472 1.7-1.706 2.793-2.009 2.639-.413.244-1.178.08-.477.127-3.61 1.947-.456-2.082-.457-2.084-.456-2.085-.46-2.087-.459-2.092-.456-2.092-.457-2.095-.456-2.097-.462-2.1-.457-2.103-.46-2.105-.456-2.108-.456-2.11-.46-2.111-.456-2.115-.462-2.118-.044-.203-.295-1.379-.182-.365-.223-.273-2.72-1.993-3.38-2.481-2.581-1.898-1.983-1.281-.441-.49-.024-.026-.19-.638-.156-2.001-.218-2.728-.218-.622-1.64-2.595-1.528-2.424-.362-.223-.339.078-.338-.024-.331-.103-.313-.166-1.79-1.312-.59-.202-.776-.073-.595-.181-.415-.465-.247-.912-.023-2.59v-.08l-.244-.768-1.236-2.19-1.026-1.82-.139-.501-.164-1.021-.464-.916-.113-.337-.22-1.08-.088-.779.336-1.975.051-1.452.224-.534.351-.368.652-.685.395-.534.444-.466.628-.32 1.026-.168.306-.166.202-.249.146-.321.608-2.339.319-.694.63-.467 1.401-.625.675-.49 1.534-.713.136-.124.664-.602.38-.236.128-.132.07-.137.113-.345.102-.16.582-.372.124-.223-.419-.666.047-.308.19-.296.518-.557.023-.032.126-.178.046-.27-.3-2.007-.031-.741.164-.77.385-1.063.284-1.468.213-.705.765-.855.977-1.522.162-.392-.557-.438-.672-.212-.61-.304-.372-.713-.103-.819.033-.731.175-.7.58-1.42.005-.594-.16-1.675-.058-.62-.275-.658-.793-1.32-.192-.705-.07-.856.093-1.605.344-1.262.064-.239.051-1.112.341-1.485.183-.335.146-.384-.041-.365-.12-.376-.078-.402.077-.835.765-2.172.015-.107.044-.28.025-.837-.03-.389-.136-.373-.25-.153-.304-.086-.313-.163-1.404-.187-.662-.275v-.013l.034-.687.38-.469.466-.3 1.044-.4 1.955-1.205.362-.407.37-.713.138-.716-.326-.415-.564-.3.102-.325.5-.251.624-.078.605.042.557-.117 1.121-.456.29-.21.013-.25-.554-.86-.054-.21.013-.246-.052-.542.3-.127 1.147-.482.395-.076h1.38l.362-.13.667-.575.49-.23.124-.058.356-.352.503-.622.498-.975.215-.145.16-.08.45-.493.173-.107.487-.202 1.288-.534.197-.122.339-.56.295-.158 2.13-.031.615-.249.395-.43.226-.15.218.09.167.099.177-.037.171-.085.475-.117.38-.176.344-.234.228-.235.328.189.298-.055.597-.414.337-.18.274-.072.71-.016.357-.147.203-.034.17.109.13.13.167.064 1.38.037.003.06.033.194.008.928-.5.41-.614.27-.338.505.053.155.252.563.08.124.094.065.1.29.131.065.708.099.326-.062.36-.304.197-.275.177-.378-.005-.335-1.029-.427-.154.02-.334.104-.184.003v-.127l.392-.28.154-.042.098-.096.069-.1.056-.055.244-.04 1.573.361.29-.028.864-.392.139.036.259.408 1.136.173.526.488 1.203.438.308.264-.518.138-.239.103-.225.166-.088-.337-.448-.06-.516.138-.29.257.118.334.423.163.49.024.313-.088.113.176.144.23.054.198-.095.249-.11-.293-.698.697.13 1.006.609 1.032.74.77.604.295.17.122.376.545.164.236-.084.238-.372.373-.157.418-.17.163-.22.037-.21-.187.12-.311-.15-.086-.28.044-.526.174-.16.122-.276.399-.083.3.234.102.505.015.21.114.108-.106.05-.049.243-.454.346.783.428.591.57.358.77.09.223-.056.326.015.821-.425.5-.124.315-.332.126-.568.105-.47.231-.821.293-.264.341-.073.751.057.68-.135.626-.251.803-.584.498-.531.146-.073.062-.189.051-.104.929-.772.133-.166.041-.228-.061-.143-.029-.132.149-.208.349-.168.816-.086.623-.482.334-.054.284.147.149.366-.023.176-.167.244-.041.215.036.228.159.293.036.184.059.143.28.423.103.204.038.2.028.438.044.2.074.16.111.185.115.15.085.062.095.112.192.308-.092.337-.224.32-.646.269-.883.752-.477.902-.687 1.021-1.139 2.144-.87 2.21-.416.567-1.336.353-1.562.9-.565-.159-.559.423-.516.806-.231.773-.223.837-.19.832-.038 1.04-.036 1.073.077.67.208.686.12.366.08.127.072.197.092.446.062.184.354.547.256.3.223.13.193.197.426 1.33.218.267 1.118 1.068.039.037.313.17.492.193.972-.348.165.04.148.09.295.28-.179.267-.095.4.082.406.187.288.652.316 1.46.581.649-.075.469.404.226.467-.613.244-.082.466.031.324.015.156.146.43.246.306.742.236-.441.402-.113.54-.082.502.061.871-.197.41.082.729.382.624.572.729.336.264.405.192.221.117-.026.389-.449.015-.32.247-.311.632-.474.405-.229.448-.223.482-.308.283.106.565-.07.604-.546.692-.623.317-.457.52.118.465-.136.415-.003.79-.659.488-.141.155-.103.089-.066.119.179.394.067.272-.59.277-.382.288-.521.584-.252.518-.315.293-.149.355-.282.32-.259.173-1.08.358-.146.093-.062.137-.026.208-.079.243-.123.203-.029.389-.112.223-.029.352-.249.239-.359.181-1.508.228-.552.309-.86 1.452-.779.207-.162-.013-.148-.07-.183-.018-.192.073-.097.1-.065.123-.346.466-.151.138-.21.054-.768.345-.326.26-.646.261-.398.425-.2.615-.577 1.182-.344.562-.225.08-.139.146-.002.003-.08.049.151.34.062.391-.003.827.054.36.236.765.052.363.153.643.362.734 1.483 2.165.154.16.228.125 2.317 2.336.605.368 1.268.563.228.054.469.109 1.116-.169 1.224-.352 1.026-.568.308-.086.377.695-.049 1.348-.474.415-.259.109-.2.264-.08.332.095.309.382.661.218.244.339.109.685-.15 1.223-.755.583-.177.277-.124.254-.303.123-.371-.105-.337-.277-.327-.044-.168.48-.39.19.136.205.036.174-.083.103-.22h.11l.259.176.683.319.282.186.216.273.559.879.056.189.082.98-.431.702.336 1.198-.141.565.47-.145.32.345.321.433.375.21.387.09.829.431.397.145-.102.094-.046.023-.072.016-1.586-.524-.78-.091-.189.615.156.233.262.218.251.28.11.422.152.195.354-.003.708-.132.687.153 1.365.513h.718l.265-.192-.09-.262-.275-.233-.289-.101-.185-.13-.226-.285-.251-.264 1.462 1.058.895.427.111.026zm-8.299-12.937.315.2.326.153.282.179.19.274-1.072 1.001-.27.13-.1.088-.074.163-.159.64h-.1l.051-.518.049-.163-.252.083-.202.62-.29.445-.272.306-.095.355-.269-.277-.006-.342.101-.322-.29-.448-.272-.135-.257-.233-.279-.088-.301.308-.136.293-.3.07-.377-.3-.102-.299-.098-.54.008-.518.31-.555-.195-.806.008-.61.598-.227 1.313.288.703.046.518-.334.483.593zm-.444-16.1.156-.124.216-.174 1.844.016.154.228-.241.249-.649.516-.31-.068-.321-.163-.536-.267zm4.877-2.318.146.06.092.148.393.41-.126.269-1.573.674-.815.506-.298.119-.136-.34.223-.267.267-.259.29-.43.041-.275-.013-.127.013-.184.128-.076.036.192.244.164.387-.073.036-.446-.11-.23.126-.27.192-.18.131-.214.146-.215.267.158.464.072.077.177-.421.15-.059.12-.1.126z"})),AV.createElement("path",{stroke:"#fff",strokeWidth:2,d:"m118.046 82.818.015.874-.462 1.854-.041.3-.007 1.09-.29 1.319.118 2.014-.236 2.048.012 1.069.331.749 1.083 1.08.375.696.056.814-.287.604-.329.316-.197.192-1.129.765-4.228 2.053-4.995 2.422-.441.479-.651 1.53-.529.537-.587.368-.542.446-.315.098-.295-.119-.29-.169-.303-.041-.364.394-.136 1.54-.211.638-.682.645-.272.664-.195.288-.236.244-.264.215-.885.355-1.816-.023-.832.679-1.09 1.955-1.031 1.169-.254.443-.172 1.25.03.332 1.207 3.269.492 2.012.51 1.091.021.355-.09.732.072.676.405 1.335-.007.638-.257.768-.998 2.1-1.472 1.7-1.706 2.793-2.009 2.639-.413.244-1.178.08-.477.127-3.61 1.947-.456-2.082-.457-2.084-.456-2.085-.46-2.087-.459-2.092-.456-2.092-.457-2.095-.456-2.097-.462-2.1-.457-2.103-.46-2.105-.456-2.108-.456-2.11-.46-2.111-.456-2.115-.462-2.118-.044-.203-.295-1.379-.182-.365-.223-.273-2.72-1.993-3.38-2.481-2.581-1.898-1.983-1.281-.441-.49-.024-.026-.19-.638-.156-2.001-.218-2.728-.218-.622-1.64-2.595-1.528-2.424-.362-.223-.339.078-.338-.024-.331-.103-.313-.166-1.79-1.312-.59-.202-.776-.073-.595-.181-.415-.465-.247-.912-.023-2.59v-.08l-.244-.768-1.236-2.19-1.026-1.82-.139-.501-.164-1.021-.464-.916-.113-.337-.22-1.08-.088-.779.336-1.975.051-1.452.224-.534.351-.368.652-.685.395-.534.444-.466.628-.32 1.026-.168.306-.166.202-.249.146-.321.608-2.339.319-.694.63-.467 1.401-.625.675-.49 1.534-.713.136-.124.664-.602.38-.236.128-.132.07-.137.113-.345.102-.16.582-.372.124-.223-.419-.666.047-.308.19-.296.518-.557.023-.032.126-.178.046-.27-.3-2.007-.031-.741.164-.77.385-1.063.284-1.468.213-.705.765-.855.977-1.522.162-.392-.557-.438-.672-.212-.61-.304-.372-.713-.103-.819.033-.731.175-.7.58-1.42.005-.594-.16-1.675-.058-.62-.275-.658-.793-1.32-.192-.705-.07-.856.093-1.605.344-1.262.064-.239.051-1.112.341-1.485.183-.335.146-.384-.041-.365-.12-.376-.078-.402.077-.835.765-2.172.015-.107.044-.28.025-.837-.03-.389-.136-.373-.25-.153-.304-.086-.313-.163-1.404-.187-.662-.275v-.013l.034-.687.38-.469.466-.3 1.044-.4 1.955-1.205.362-.407.37-.713.138-.716-.326-.415-.564-.3.102-.325.5-.251.624-.078.605.042.557-.117 1.121-.456.29-.21.013-.25-.554-.86-.054-.21.013-.246-.052-.542.3-.127 1.147-.482.395-.076h1.38l.362-.13.667-.575.49-.23.124-.058.356-.352.503-.622.498-.975.215-.145.16-.08.45-.493.173-.107.487-.202 1.288-.534.197-.122.339-.56.295-.158 2.13-.031.615-.249.395-.43.226-.15.218.09.167.099.177-.037.171-.085.475-.117.38-.176.344-.234.228-.235.328.189.298-.055.597-.414.337-.18.274-.072.71-.016.357-.147.203-.034.17.109.13.13.167.064 1.38.037.003.06.033.194.008.928-.5.41-.614.27-.338.505.053.155.252.563.08.124.094.065.1.29.131.065.708.099.326-.062.36-.304.197-.275.177-.378-.005-.335-1.029-.427-.154.02-.334.104-.184.003v-.127l.392-.28.154-.042.098-.096.069-.1.056-.055.244-.04 1.573.361.29-.028.864-.392.139.036.259.408 1.136.173.526.488 1.203.438.308.264-.518.138-.239.103-.225.166-.088-.337-.448-.06-.516.138-.29.257.118.334.423.163.49.024.313-.088.113.176.144.23.054.198-.095.249-.11-.293-.698.697.13 1.006.609 1.032.74.77.604.295.17.122.376.545.164.236-.084.238-.372.373-.157.418-.17.163-.22.037-.21-.187.12-.311-.15-.086-.28.044-.526.174-.16.122-.276.399-.083.3.234.102.505.015.21.114.108-.106.05-.049.243-.454.346.783.428.591.57.358.77.09.223-.056.326.015.821-.425.5-.124.315-.332.126-.568.105-.47.231-.821.293-.264.341-.073.751.057.68-.135.626-.251.803-.584.498-.531.146-.073.062-.189.051-.104.929-.772.133-.166.041-.228-.061-.143-.029-.132.149-.208.349-.168.816-.086.623-.482.334-.054.284.147.149.366-.023.176-.167.244-.041.215.036.228.159.293.036.184.059.143.28.423.103.204.038.2.028.438.044.2.074.16.111.185.115.15.085.062.095.112.192.308-.092.337-.224.32-.646.269-.883.752-.477.902-.687 1.021-1.139 2.144-.87 2.21-.416.567-1.336.353-1.562.9-.565-.159-.559.423-.516.806-.231.773-.223.837-.19.832-.038 1.04-.036 1.073.077.67.208.686.12.366.08.127.072.197.092.446.062.184.354.547.256.3.223.13.193.197.426 1.33.218.267 1.118 1.068.039.037.313.17.492.193.972-.348.165.04.148.09.295.28-.179.267-.095.4.082.406.187.288.652.316 1.46.581.649-.075.469.404.226.467-.613.244-.082.466.031.324.015.156.146.43.246.306.742.236-.441.402-.113.54-.082.502.061.871-.197.41.082.729.382.624.572.729.336.264.405.192.221.117-.026.389-.449.015-.32.247-.311.632-.474.405-.229.448-.223.482-.308.283.106.565-.07.604-.546.692-.623.317-.457.52.118.465-.136.415-.003.79-.659.488-.141.155-.103.089-.066.119.179.394.067.272-.59.277-.382.288-.521.584-.252.518-.315.293-.149.355-.282.32-.259.173-1.08.358-.146.093-.062.137-.026.208-.079.243-.123.203-.029.389-.112.223-.029.352-.249.239-.359.181-1.508.228-.552.309-.86 1.452-.779.207-.162-.013-.148-.07-.183-.018-.192.073-.097.1-.065.123-.346.466-.151.138-.21.054-.768.345-.326.26-.646.261-.398.425-.2.615-.577 1.182-.344.562-.225.08-.139.146-.002.003-.08.049.151.34.062.391-.003.827.054.36.236.765.052.363.153.643.362.734 1.483 2.165.154.16.228.125 2.317 2.336.605.368 1.268.563.228.054.469.109 1.116-.169 1.224-.352 1.026-.568.308-.086.377.695-.049 1.348-.474.415-.259.109-.2.264-.08.332.095.309.382.661.218.244.339.109.685-.15 1.223-.755.583-.177.277-.124.254-.303.123-.371-.105-.337-.277-.327-.044-.168.48-.39.19.136.205.036.174-.083.103-.22h.11l.259.176.683.319.282.186.216.273.559.879.056.189.082.98-.431.702.336 1.198-.141.565.47-.145.32.345.321.433.375.21.387.09.829.431.397.145-.102.094-.046.023-.072.016-1.586-.524-.78-.091-.189.615.156.233.262.218.251.28.11.422.152.195.354-.003.708-.132.687.153 1.365.513h.718l.265-.192-.09-.262-.275-.233-.289-.101-.185-.13-.226-.285-.251-.264 1.462 1.058.895.427.111.026zm-8.299-12.937.315.2.326.153.282.179.19.274-1.072 1.001-.27.13-.1.088-.074.163-.159.64h-.1l.051-.518.049-.163-.252.083-.202.62-.29.445-.272.306-.095.355-.269-.277-.006-.342.101-.322-.29-.448-.272-.135-.257-.233-.279-.088-.301.308-.136.293-.3.07-.377-.3-.102-.299-.098-.54.008-.518.31-.555-.195-.806.008-.61.598-.227 1.313.288.703.046.518-.334.483.593zm-.444-16.1.156-.124.216-.174 1.844.016.154.228-.241.249-.649.516-.31-.068-.321-.163-.536-.267zm4.877-2.318.146.06.092.148.393.41-.126.269-1.573.674-.815.506-.298.119-.136-.34.223-.267.267-.259.29-.43.041-.275-.013-.127.013-.184.128-.076.036.192.244.164.387-.073.036-.446-.11-.23.126-.27.192-.18.131-.214.146-.215.267.158.464.072.077.177-.421.15-.059.12-.1.126z",mask:"url(#mapiconTunisiamobile_svg__b)"}),AV.createElement("path",{fill:"#FFCA00",stroke:"#FFCA00",d:"M82.111 61.447a5.5 5.5 0 0 1 7.573-.195l.205.195a5.5 5.5 0 0 1 .195 7.572l-.195.206L86 73.113l-3.889-3.888a5.5 5.5 0 0 1-.195-7.573zM86 63.503a1.833 1.833 0 1 0 0 3.665 1.833 1.833 0 0 0 0-3.665Z"}),AV.createElement("path",{fill:"#fff",d:"M68.586 87h-1.638v-7.896H64.12v-1.442h7.294v1.442h-2.828zm9.235 0h-1.47v-.896a3.24 3.24 0 0 1-2.394 1.064c-1.456 0-2.17-.756-2.17-2.142v-4.788h1.47v4.256c0 1.05.532 1.372 1.358 1.372.742 0 1.386-.42 1.736-.882v-4.746h1.47zm8.023 0h-1.47v-4.228c0-1.05-.531-1.4-1.358-1.4-.756 0-1.4.448-1.736.91V87h-1.47v-6.762h1.47v.924a3.2 3.2 0 0 1 2.38-1.092c1.457 0 2.185.784 2.185 2.17zm2.718-7.588a.917.917 0 0 1-.91-.91c0-.504.42-.91.91-.91.504 0 .91.406.91.91s-.406.91-.91.91M89.304 87h-1.47v-6.762h1.47zm4.297.168c-1.148 0-2.198-.364-2.898-1.05l.672-1.064c.476.49 1.414.952 2.296.952s1.33-.378 1.33-.91c0-1.33-4.088-.378-4.088-2.968 0-1.106.952-2.058 2.66-2.058 1.148 0 2.016.406 2.632.952l-.616 1.036c-.406-.462-1.162-.812-2.002-.812-.77 0-1.26.35-1.26.826 0 1.204 4.088.308 4.088 2.996 0 1.19-.994 2.1-2.814 2.1m5.101-7.756a.917.917 0 0 1-.91-.91c0-.504.42-.91.91-.91.504 0 .91.406.91.91s-.406.91-.91.91M99.444 87h-1.47v-6.762h1.47zm5.109.168c-2.03 0-3.542-1.414-3.542-3.556 0-1.96 1.428-3.542 3.43-3.542 2.03 0 3.332 1.554 3.332 3.682v.35h-5.222c.112 1.022.882 1.862 2.156 1.862.658 0 1.442-.266 1.918-.742l.672.966c-.672.644-1.666.98-2.744.98m1.792-4.102c-.028-.798-.574-1.792-1.904-1.792-1.26 0-1.834.966-1.904 1.792zM12.06 14.24a.76.76 0 0 1-.48-.12.65.65 0 0 1-.28-.32 3.4 3.4 0 0 1-.26-.44q-.18-.32-.22-.56-.06-.64-.08-1.24 0-.6.06-1.68a8.4 8.4 0 0 1 .24-1.56q.18-.7.36-1.18.18-.5.28-.74.1-.26.04-.26-.08 0-.18.16-.08.14-.2.38a8 8 0 0 1-.24.52 7 7 0 0 1-.26.54q-.28.62-.66 1.32-.36.68-.74 1.32t-.66 1.12q-.18.2-.42.58-.24.36-.52.76t-.58.66q-.44.42-.9.5a1.27 1.27 0 0 1-.86-.1q-.2-.02-.42-.22a1.4 1.4 0 0 1-.36-.46.8.8 0 0 1-.06-.52 9 9 0 0 1 0-.86q.04-.6.12-1.3.08-.72.16-1.38.1-.66.18-1.1.18-1.28.48-2.62.32-1.34.58-2.7.16-.4.28-.9.14-.52.28-.96.02-.44.32-.54.3-.12.64-.04.36.08.5.28.2.2.38.62t-.1 1.06a20 20 0 0 0-.78 2.4q-.36 1.4-.66 3.4l-.18 1.08q-.08.58-.16 1.12l-.1.9q-.02.34.02.36.16.04.34-.18.2-.22.42-.7a63 63 0 0 0 1.54-2.7q.74-1.34 1.48-2.8.34-.58.68-1.26.36-.7.68-1.28l.66-1.16q.32-.56.56-.94.24-.4.36-.44.54 0 .8.28.28.28.44.68.04.1.04.34a.85.85 0 0 1-.06.42q-.02.14-.18.58-.14.44-.26.98-.2.54-.36 1.08-.14.52-.2.86-.16.86-.4 1.88t-.42 2.04q-.16 1-.18 1.78-.02.56 0 .74.04.16.12.34.04.16.18.08a.9.9 0 0 0 .28-.28q.28-.42.42-.72.16-.3.32-.64.16-.36.44-.9.5-.92 1.02-2.02.54-1.1 1.02-2.18.5-1.08.86-1.92.38-1.08.64-1.74.26-.68.48-1.04t.42-.48q.1-.08.42-.08.34 0 .52.16.08.08.28.26a.49.49 0 0 1 .18.42q-.1.32-.38 1.04a41 41 0 0 1-.62 1.6 71 71 0 0 1-.68 1.66q-.32.78-.54 1.24-.22.42-.42.72t-.2.46q0 .08-.2.5-.2.4-.52.98-.3.58-.66 1.2t-.68 1.14a9 9 0 0 1-.54.8q-.5.62-1.08 1.04a6 6 0 0 1-1 .66q-.44.22-.58.22m7.72-2q-.14.02-.32-.1a4 4 0 0 0-.3-.24q-.2-.24-.28-.42a1 1 0 0 1-.08-.4q0-.22.04-.52.1-.32.24-.86t.34-1.16a4 4 0 0 1 .14-.56q.12-.34.24-.64l.18-.48q.14-.5.28-.96.16-.46.38-1.06t.54-1.52l.66-1.8q.22-.6.32-.86t.14-.42q0-.16.22-.3.24-.16.42-.16.28.02.54.34.26.3.36.74.1.42-.14.82-.32.72-.64 1.62l-.6 1.76q-.3.88-.6 1.62a1.6 1.6 0 0 1-.14.34 6.135 6.135 0 0 1-.34.7l-.06.14q-.02.12-.08.34t-.12.38a6 6 0 0 1-.06.26 3 3 0 0 0-.06.32q.1-.12.28-.28.18-.18.46-.44.32-.28.56-.48t.48-.36q.22-.16.4-.3l.2-.14q.04-.06.08-.14a.27.27 0 0 1 .12-.1.8.8 0 0 1 .14-.1l.2-.16a2.1 2.1 0 0 1 .8-.32 2.4 2.4 0 0 1 .42-.04q.24 0 .5.2.26.18.48.52.2.26.24.62.06.34.02.92 0 .16-.1.6l-.12.66-.18.68h.16a.65.65 0 0 1 .42-.02l.22.06a1 1 0 0 1 .18.14q.1.08.18.12.12.16-.04.46t-.36.42q-.48.32-.86.4-.36.08-.76-.14a1.35 1.35 0 0 1-.52-.62q-.12-.36-.1-.98.08-.88.1-1.26.02-.4-.2-.4-.24 0-.66.24a6.5 6.5 0 0 0-.9.62q-.5.38-1 .86-.48.46-.86.94-.24.3-.52.6a1 1 0 0 1-.68.3m12.533.36q-.6.34-1.28.4a2.95 2.95 0 0 1-1.24-.18q-.58-.24-.94-.7-.2-.26-.38-.82-.18-.58-.06-1.56.12-.92.56-1.86.46-.94 1.12-1.66t1.44-.98a3 3 0 0 1 .56-.14q.3-.06.78-.02.46.06.82.24.36.16.52.36.32.32.42.62t.08.58q-.1.64-.58 1.38-.46.74-1.3 1.1a2.7 2.7 0 0 1-.8.18q-.46.04-1.26-.12-.48-.1-.64.1-.14.2-.12.78.04.36.16.64.14.26.5.36.46.08.66.02.2-.08.5-.22.16-.1.3-.22.16-.12.3-.24.16-.12.26-.18a.5.5 0 0 1 .2-.1 1 1 0 0 1 .2-.02q.22.02.42.24t.26.42q0 .16-.2.46a4 4 0 0 1-.54.6q-.32.3-.72.54m-.26-4.46q.14-.06.4-.28.28-.22.5-.46.22-.26.22-.4 0-.22-.3-.38-.28-.16-.74-.06-.16.04-.38.22t-.44.42a4 4 0 0 0-.38.44q-.16.2-.22.32.08.1.32.16t.52.06q.3 0 .5-.04m4.68 4.44q-.06.02-.26-.02a5 5 0 0 1-.38-.1.7.7 0 0 1-.22-.1 2 2 0 0 1-.22-.28q-.14-.22-.16-.58 0-.36.34-.86.1-.2.24-.6t.28-.84q.16-.46.28-.82.14-.36.2-.5.04-.34-.04-.48-.06-.16-.34-.18a.4.4 0 0 1-.32-.14q-.1-.14-.1-.5.04-.28.14-.48.12-.22.32-.36.2-.1.5-.14a3.3 3.3 0 0 1 .62-.02q.3.02.42.14.14.08.22.36.1.26.16.54t.08.42q.2-.16.44-.44.26-.3.52-.56.32-.22.76-.44.44-.24.64-.24.219 0 .54.22.32.2.4.36.2.34.28.72a.69.69 0 0 1-.12.64 2.7 2.7 0 0 1-.56.52q-.36.24-.9.14-.12-.04-.26-.3-.12-.26-.14-.38 0-.06-.18.12l-.4.4q-.22.24-.42.44-.34.42-.72 1.14t-.9 1.74a9 9 0 0 0-.4.96q-.16.44-.34.5m7.627.78q-1.1 0-1.7-.64-.58-.64-.58-1.96 0-.72.26-1.58.279-.86.72-1.68.46-.82 1-1.42.54-.62 1.08-.86.2-.1.42-.14.24-.06.46-.06.24 0 .52.04.3.02.56.12.46.14.8.54.36.4.36 1.16 0 .2-.04.44a2 2 0 0 1-.14.44q-.26.66-.8 1.26-.52.6-1.1.84a1.6 1.6 0 0 1-.32.12q-.14.02-.3.02-.42 0-.76-.12a2.6 2.6 0 0 1-.6-.32l-.16-.14-.1.2q-.14.38-.2.64-.04.26-.04.38 0 .24.02.5.02.24.12.4a.6.6 0 0 0 .2.16q.14.06.34.06h.22q.12-.02.2-.04.36-.08.58-.26.24-.2.5-.34l.08-.04-.02-.06q.34-.18.46-.22.12-.06.18-.1a.25.25 0 0 1 .18-.08.7.7 0 0 1 .54.16q.28.18.28.46 0 .12-.18.38a3 3 0 0 1-.42.52q-.24.24-.5.38a.5.5 0 0 0-.12.06.5.5 0 0 0-.12.06h.02q-.18.24-.36.24h-.16q-.08.14-.52.3-.44.18-.86.18m1.04-4.82q.18 0 .32-.06.16-.06.3-.14.48-.34.62-.88t-.1-.82a.2.2 0 0 0-.1-.08.25.25 0 0 0-.14-.04q-.22 0-.5.14a2.3 2.3 0 0 0-.52.4q-.261.26-.48.62l-.26.42a.2.2 0 0 1 .06-.02.2.2 0 0 1 .06-.02q.18 0 .24.12t.14.24q.**********"}),AV.createElement("path",{fill:"#FFCA00",d:"M84.403 44.109a.5.5 0 0 0 .703.073l3.494-2.835a.5.5 0 0 0-.63-.777l-3.106 2.52-2.52-3.105a.5.5 0 1 0-.777.63zm-30.365-33.82.142.479c9.717-2.878 17.727.384 23.078 6.835 5.37 6.474 8.072 16.18 7.036 26.14l.497.051.497.052c1.06-10.183-1.692-20.169-7.26-26.881-5.588-6.736-14.003-10.156-24.132-7.156z"}))),v||(v=AV.createElement("defs",null,AV.createElement("clipPath",{id:"mapiconTunisiamobile_svg__a"},AV.createElement("path",{fill:"#fff",d:"M0 0h180v140H0z"})))));function AW(){return(AW=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let AS=A=>AV.createElement("svg",AW({xmlns:"http://www.w3.org/2000/svg",width:175,height:132,fill:"none"},A),p||(p=AV.createElement("path",{fill:"#fff",d:"M12.072 20.488a.9.9 0 0 1-.576-.144.78.78 0 0 1-.336-.384 4 4 0 0 1-.312-.528 2.1 2.1 0 0 1-.264-.672 25 25 0 0 1-.096-1.488q0-.72.072-2.016a10 10 0 0 1 .288-1.872q.216-.84.432-1.416.216-.6.336-.888.12-.312.048-.312-.096 0-.216.192a10 10 0 0 0-.24.456q-.12.288-.288.624-.144.336-.312.648a24 24 0 0 1-.792 1.584 37 37 0 0 1-.888 1.584q-.456.768-.792 1.344-.216.24-.504.696-.288.432-.624.912t-.696.792q-.528.504-1.08.6a1.52 1.52 0 0 1-1.032-.12q-.24-.024-.504-.264a1.7 1.7 0 0 1-.432-.552q-.144-.336-.072-.624a11 11 0 0 1 0-1.032q.048-.72.144-1.56.096-.864.192-1.656.12-.792.216-1.32.216-1.536.576-3.144a73 73 0 0 0 .696-3.24q.192-.48.336-1.08.168-.624.336-1.152.024-.528.384-.648a1.25 1.25 0 0 1 .768-.048q.432.096.6.336.24.24.456.744t-.12 1.272q-.48 1.176-.936 2.88-.432 1.68-.792 4.08l-.216 1.296q-.096.696-.192 1.344l-.12 1.08q-.024.408.024.432.192.048.408-.216.24-.264.504-.84a75 75 0 0 0 1.848-3.24 107 107 0 0 0 1.776-3.36q.408-.696.816-1.512.432-.84.816-1.536l.792-1.392q.384-.672.672-1.128.288-.48.432-.528.648 0 .96.336.336.336.528.816.048.12.048.408.024.288-.072.504-.024.168-.216.696-.168.528-.312 1.176-.24.648-.432 1.296-.168.624-.24 1.032-.192 1.032-.48 2.256t-.504 2.448q-.192 1.2-.216 2.136-.024.672 0 .888.048.192.144.408.048.192.216.096t.336-.336q.336-.504.504-.864.192-.36.384-.768.192-.432.528-1.08.6-1.104 1.224-2.424.648-1.32 1.224-2.616.6-1.296 1.032-2.304.456-1.296.768-2.088.312-.816.576-1.248t.504-.576q.12-.096.504-.096.408 0 .624.192.096.096.336.312t.216.504q-.12.384-.456 1.248a50 50 0 0 1-.744 1.92 85 85 0 0 1-.816 1.992q-.384.936-.648 1.488a10 10 0 0 1-.504.864q-.24.36-.24.552 0 .096-.24.6-.24.48-.624 1.176-.36.696-.792 1.44t-.816 1.368-.648.96q-.6.744-1.296 1.248-.672.528-1.2.792t-.696.264m9.264-2.4q-.168.024-.384-.12a5 5 0 0 0-.36-.288 2.3 2.3 0 0 1-.336-.504 1.2 1.2 0 0 1-.096-.48q0-.264.048-.624.12-.384.288-1.032t.408-1.392q.048-.288.168-.672.144-.408.288-.768l.216-.576q.168-.6.336-1.152.192-.552.456-1.272t.648-1.824l.792-2.16q.264-.72.384-1.032t.168-.504q0-.192.264-.36.288-.192.504-.192.336.024.648.408.312.36.432.888.12.504-.168.984a28 28 0 0 0-.768 1.944l-.72 2.112a37 37 0 0 1-.72 1.944 2 2 0 0 1-.168.408q-.12.288-.216.48a7 7 0 0 1-.192.36l-.072.168a5 5 0 0 1-.096.408 5 5 0 0 1-.144.456q-.024.12-.072.312a3 3 0 0 0-.072.384q.12-.144.336-.336.216-.216.552-.528.384-.336.672-.576t.576-.432q.264-.192.48-.36l.24-.168a2 2 0 0 0 .096-.168.3.3 0 0 1 .144-.12 1 1 0 0 1 .168-.12l.24-.192a2.5 2.5 0 0 1 .96-.384 3 3 0 0 1 .504-.048q.288 0 .6.24.312.216.576.624.24.312.288.744.072.408.024 1.104 0 .192-.12.72l-.144.792-.216.816h.192a.8.8 0 0 1 .504-.024l.264.072q.12.072.216.168.12.096.216.144.144.192-.048.552t-.432.504q-.576.384-1.032.48-.432.096-.912-.168-.456-.312-.624-.744-.144-.432-.12-1.176.096-1.056.12-1.512.024-.48-.24-.48-.288 0-.792.288a8 8 0 0 0-1.08.744q-.6.456-1.2 1.032-.576.552-1.032 1.128-.288.36-.624.72a1.2 1.2 0 0 1-.816.36m15.04.432a3.75 3.75 0 0 1-1.536.48 3.54 3.54 0 0 1-1.488-.216 2.7 2.7 0 0 1-1.128-.84q-.24-.312-.456-.984-.216-.696-.072-1.872a7.6 7.6 0 0 1 .672-2.232 8.4 8.4 0 0 1 1.344-1.992q.792-.864 1.728-1.176.336-.12.672-.168.36-.072.936-.024.552.072.984.288.432.192.624.432.384.384.504.744t.096.696q-.12.768-.696 1.656a3.33 3.33 0 0 1-1.56 1.32 3.3 3.3 0 0 1-.96.216q-.552.048-1.512-.144-.576-.12-.768.12-.168.24-.144.936a2.7 2.7 0 0 0 .192.768q.168.312.6.432.552.096.792.024.24-.096.6-.264.192-.12.36-.264.192-.144.36-.288.192-.144.312-.216a.6.6 0 0 1 .24-.12q.12-.024.24-.024.264.024.504.288t.312.504q0 .192-.24.552a5 5 0 0 1-.648.72q-.384.36-.864.648m-.312-5.352q.168-.072.48-.336a5 5 0 0 0 .6-.552q.264-.312.264-.48 0-.264-.36-.456-.336-.192-.888-.072-.192.048-.456.264a5 5 0 0 0-.528.504 4.5 4.5 0 0 0-.456.528q-.192.24-.264.384.096.12.384.192t.624.072q.36 0 .6-.048m5.615 5.328q-.072.024-.312-.024a7 7 0 0 1-.456-.12q-.216-.072-.264-.12a3 3 0 0 1-.264-.336q-.168-.264-.192-.696 0-.432.408-1.032.12-.24.288-.72t.336-1.008q.192-.552.336-.984.168-.432.24-.6.048-.408-.048-.576-.072-.192-.408-.216a.48.48 0 0 1-.384-.168q-.12-.168-.12-.6a1.9 1.9 0 0 1 .168-.576 1.25 1.25 0 0 1 .384-.432q.24-.12.6-.168a4 4 0 0 1 .744-.024q.36.024.504.168.168.096.264.432.12.312.192.648t.096.504q.24-.192.528-.528.312-.36.624-.672.384-.264.912-.528.528-.288.768-.288.264 0 .648.264.384.24.48.432.24.408.336.864a.82.82 0 0 1-.144.768q-.24.312-.672.624-.432.288-1.08.168-.144-.048-.312-.36a2 2 0 0 1-.168-.456q0-.072-.216.144l-.48.48a17 17 0 0 1-.504.528q-.408.504-.864 1.368t-1.08 2.088q-.288.6-.48 1.152-.192.528-.408.6m9.153.936q-1.32 0-2.04-.768-.696-.768-.696-2.352 0-.864.312-1.896.336-1.032.864-2.016a9.4 9.4 0 0 1 1.2-1.704q.648-.744 1.296-1.032.24-.12.504-.168.288-.072.552-.072.288 0 .624.048.36.024.672.144.552.168.96.648.432.48.432 1.392 0 .24-.048.528a2.3 2.3 0 0 1-.168.528 5 5 0 0 1-.96 1.512q-.624.72-1.32 1.008a2 2 0 0 1-.384.144 3 3 0 0 1-.36.024q-.504 0-.912-.144a3.1 3.1 0 0 1-.72-.384l-.192-.168-.12.24q-.168.456-.24.768-.048.312-.048.456 0 .288.024.6.024.288.144.48.096.12.24.192.168.072.408.072h.264q.144-.024.24-.048.432-.096.696-.312.288-.24.6-.408l.096-.048-.024-.072q.408-.216.552-.264.144-.072.216-.12a.3.3 0 0 1 .216-.096q.336-.048.648.192.336.216.336.552 0 .144-.216.456a3.5 3.5 0 0 1-.504.624 2.6 2.6 0 0 1-.6.456.6.6 0 0 0-.144.072.6.6 0 0 0-.144.072h.024q-.216.288-.432.288h-.192q-.096.168-.624.36a2.7 2.7 0 0 1-1.032.216m1.248-5.784a1 1 0 0 0 .384-.072q.192-.072.36-.168.576-.408.744-1.056t-.12-.984a.23.23 0 0 0-.12-.096.3.3 0 0 0-.168-.048q-.264 0-.6.168a2.7 2.7 0 0 0-.624.48q-.312.312-.576.744l-.312.504a.2.2 0 0 1 .072-.024.2.2 0 0 1 .072-.024q.216 0 .288.144t.168.288q.12.144.432.144"})),E||(E=AV.createElement("path",{fill:"#FFCA00",d:"M110.944 39.179a.5.5 0 0 0 .693-.141l2.483-3.753a.5.5 0 0 0-.834-.552l-2.207 3.336-3.336-2.207a.5.5 0 0 0-.552.834zm-36.89-21.88.28.415c7.923-5.333 16.044-4.663 22.678-.375 6.66 4.304 11.834 12.27 13.718 21.523l.49-.1.49-.1c-1.929-9.473-7.235-17.691-14.155-22.163-6.944-4.489-15.5-5.187-23.78.386z"})),w||(w=AV.createElement("path",{fill:"#fff",d:"M96.35 120.32h-3.192l-1.176-3.072h-7.344l-1.176 3.072H80.27l6.288-16.008h3.504zm-5.16-5.544-2.88-7.632-2.88 7.632zm9.437 5.544h-2.52v-16.008h2.52zm8.053 4.704c-1.968 0-3.432-.432-4.848-1.728l1.176-1.824c.936 1.08 2.136 1.488 3.672 1.488 1.656 0 3.432-.768 3.432-3.144v-1.392c-.888 1.152-2.208 1.92-3.72 1.92-2.952 0-5.136-2.136-5.136-5.952 0-3.744 2.16-5.952 5.136-5.952 1.464 0 2.784.672 3.72 1.896v-1.608h2.52v10.992c0 4.176-3.096 5.304-5.952 5.304m.456-6.912c1.176 0 2.424-.696 2.976-1.56v-4.32c-.552-.864-1.8-1.56-2.976-1.56-1.992 0-3.264 1.488-3.264 3.72s1.272 3.72 3.264 3.72m14.194 2.496c-3.48 0-6.072-2.424-6.072-6.096 0-3.36 2.448-6.072 5.88-6.072 3.48 0 5.712 2.664 5.712 6.312v.6h-8.952c.192 1.752 1.512 3.192 3.696 3.192 1.128 0 2.472-.456 3.288-1.272l1.152 1.656c-1.152 1.104-2.856 1.68-4.704 1.68m3.072-7.032c-.048-1.368-.984-3.072-3.264-3.072-2.16 0-3.144 1.656-3.264 3.072zm7.535 6.744h-2.52v-11.592h2.52v1.704c.864-1.104 2.256-1.968 3.768-1.968v2.496a3.7 3.7 0 0 0-.792-.072c-1.056 0-2.472.72-2.976 1.536zm7.128-13.008c-.84 0-1.56-.696-1.56-1.56s.72-1.56 1.56-1.56c.864 0 1.56.696 1.56 1.56s-.696 1.56-1.56 1.56m1.272 13.008h-2.52v-11.592h2.52zm12.949 0h-2.52v-1.248c-.864.984-2.208 1.536-3.744 1.536-1.896 0-4.032-1.272-4.032-3.84 0-2.664 2.136-3.768 4.032-3.768 1.56 0 2.904.504 3.744 1.488v-1.728c0-1.344-1.104-2.16-2.712-2.16-1.296 0-2.424.48-3.432 1.464l-1.032-1.752c1.344-1.272 3.024-1.872 4.872-1.872 2.568 0 4.824 1.08 4.824 4.2zm-5.28-1.44c1.104 0 2.184-.408 2.76-1.2v-1.752c-.576-.792-1.656-1.2-2.76-1.2-1.416 0-2.472.816-2.472 2.088 0 1.248 1.056 2.064 2.472 2.064"})),q||(q=AV.createElement("path",{fill:"#FFCA00",d:"m137.627 79.479-14.23 14.23-14.229-14.23c-7.859-7.86-7.859-20.6 0-28.46s20.6-7.859 28.459 0c7.859 7.86 7.859 20.6 0 28.46m-14.23-9.758a4.472 4.472 0 1 0 0-8.944 4.472 4.472 0 0 0 0 8.944"})));function AD(){return(AD=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let AG=A=>AV.createElement("svg",AD({xmlns:"http://www.w3.org/2000/svg",width:131,height:136,fill:"none"},A),f||(f=AV.createElement("path",{fill:"#fff",d:"M10.06 16.24a.76.76 0 0 1-.48-.12.65.65 0 0 1-.28-.32 3.4 3.4 0 0 1-.26-.44q-.18-.32-.22-.56-.06-.64-.08-1.24 0-.6.06-1.68a8.4 8.4 0 0 1 .24-1.56q.18-.7.36-1.18.18-.5.28-.74.1-.26.04-.26-.08 0-.18.16-.08.14-.2.38-.1.24-.24.52a7 7 0 0 1-.26.54q-.28.62-.66 1.32-.36.68-.74 1.32t-.66 1.12q-.18.2-.42.58-.24.36-.52.76t-.58.66q-.44.42-.9.5a1.27 1.27 0 0 1-.86-.1q-.2-.02-.42-.22a1.4 1.4 0 0 1-.36-.46.8.8 0 0 1-.06-.52 9 9 0 0 1 0-.86q.04-.6.12-1.3.08-.72.16-1.38.1-.66.18-1.1.18-1.28.48-2.62.32-1.34.58-2.7.16-.4.28-.9.14-.52.28-.96.02-.44.32-.54.3-.12.64-.04.36.08.5.28.2.2.38.62t-.1 1.06a20 20 0 0 0-.78 2.4q-.36 1.4-.66 3.4l-.18 1.08q-.08.58-.16 1.12l-.1.9q-.02.34.02.36.16.04.34-.18.2-.22.42-.7a63 63 0 0 0 1.54-2.7q.74-1.34 1.48-2.8.34-.58.68-1.26.36-.7.68-1.28l.66-1.16q.32-.56.56-.94.24-.4.36-.44.54 0 .8.28.28.28.44.68.04.1.04.34a.85.85 0 0 1-.06.42q-.02.14-.18.58-.14.44-.26.98-.2.54-.36 1.08-.14.52-.2.86-.16.86-.4 1.88t-.42 2.04q-.16 1-.18 1.78-.02.56 0 .74.04.16.12.34.04.16.18.08a.9.9 0 0 0 .28-.28q.28-.42.42-.72.16-.3.32-.64.16-.36.44-.9.5-.92 1.02-2.02.54-1.1 1.02-2.18.5-1.08.86-1.92.38-1.08.64-1.74.26-.68.48-1.04t.42-.48q.1-.08.42-.08.34 0 .52.16.08.08.28.26a.49.49 0 0 1 .18.42q-.1.32-.38 1.04a41 41 0 0 1-.62 1.6 71 71 0 0 1-.68 1.66q-.32.78-.54 1.24-.22.42-.42.72t-.2.46q0 .08-.2.5-.2.4-.52.98-.3.58-.66 1.2t-.68 1.14a9 9 0 0 1-.54.8q-.5.62-1.08 1.04a6 6 0 0 1-1 .66q-.44.22-.58.22m7.72-2q-.14.02-.32-.1a4 4 0 0 0-.3-.24q-.2-.24-.28-.42a1 1 0 0 1-.08-.4q0-.22.04-.52.1-.32.24-.86t.34-1.16a4 4 0 0 1 .14-.56q.12-.34.24-.64l.18-.48q.14-.5.28-.96.16-.46.38-1.06t.54-1.52l.66-1.8q.22-.6.32-.86t.14-.42q0-.16.22-.3.24-.16.42-.16.28.02.54.34.26.3.36.74.1.42-.14.82-.32.72-.64 1.62l-.6 1.76q-.3.88-.6 1.62a1.6 1.6 0 0 1-.14.34 6.135 6.135 0 0 1-.34.7l-.06.14a4 4 0 0 1-.08.34q-.06.22-.12.38a6 6 0 0 1-.06.26 3 3 0 0 0-.06.32q.1-.12.28-.28.18-.18.46-.44.32-.28.56-.48t.48-.36q.22-.16.4-.3l.2-.14q.04-.06.08-.14a.27.27 0 0 1 .12-.1.8.8 0 0 1 .14-.1l.2-.16a2.1 2.1 0 0 1 .8-.32 2.4 2.4 0 0 1 .42-.04q.24 0 .5.2.26.18.48.52.2.26.24.62.06.34.02.92 0 .16-.1.6l-.12.66-.18.68h.16a.65.65 0 0 1 .42-.02l.22.06a1 1 0 0 1 .18.14q.1.08.18.12.12.16-.04.46t-.36.42q-.48.32-.86.4-.36.08-.76-.14a1.35 1.35 0 0 1-.52-.62q-.12-.36-.1-.98.08-.88.1-1.26.02-.4-.2-.4-.24 0-.66.24a6.5 6.5 0 0 0-.9.62q-.5.38-1 .86-.48.46-.86.94-.24.3-.52.6a1 1 0 0 1-.68.3m12.533.36q-.6.34-1.28.4a2.95 2.95 0 0 1-1.24-.18q-.58-.24-.94-.7-.2-.26-.38-.82-.18-.58-.06-1.56a6.3 6.3 0 0 1 .56-1.86q.46-.94 1.12-1.66t1.44-.98a3 3 0 0 1 .56-.14q.3-.06.78-.02.46.06.82.24.36.16.52.36.32.32.42.62t.08.58q-.1.64-.58 1.38-.46.74-1.3 1.1a2.7 2.7 0 0 1-.8.18q-.46.04-1.26-.12-.48-.1-.64.1-.14.2-.12.78.04.36.16.64.14.26.5.36.46.08.66.02.2-.08.5-.22.16-.1.3-.22.16-.12.3-.24.16-.12.26-.18a.5.5 0 0 1 .2-.1 1 1 0 0 1 .2-.02q.22.02.42.24t.26.42q0 .16-.2.46a4 4 0 0 1-.54.6q-.32.3-.72.54m-.26-4.46q.14-.06.4-.28.28-.22.5-.46.22-.26.22-.4 0-.22-.3-.38-.28-.16-.74-.06-.16.04-.38.22t-.44.42a4 4 0 0 0-.38.44q-.16.2-.22.32.08.1.32.16t.52.06q.3 0 .5-.04m4.68 4.44q-.06.02-.26-.02a5 5 0 0 1-.38-.1.7.7 0 0 1-.22-.1 2 2 0 0 1-.22-.28q-.14-.22-.16-.58 0-.36.34-.86.1-.2.24-.6t.28-.84q.16-.46.28-.82.14-.36.2-.5.04-.34-.04-.48-.06-.16-.34-.18a.4.4 0 0 1-.32-.14q-.1-.14-.1-.5.04-.28.14-.48.12-.22.32-.36.2-.1.5-.14a3.3 3.3 0 0 1 .62-.02q.3.02.42.14.14.08.22.36.1.26.16.54t.08.42q.2-.16.44-.44.26-.3.52-.56.32-.22.76-.44.44-.24.64-.24.219 0 .54.22.32.2.4.36.2.34.28.72a.69.69 0 0 1-.12.64 2.7 2.7 0 0 1-.56.52q-.36.24-.9.14-.12-.04-.26-.3-.12-.26-.14-.38 0-.06-.18.12l-.4.4q-.22.24-.42.44-.34.42-.72 1.14t-.9 1.74a9 9 0 0 0-.4.96q-.16.44-.34.5m7.627.78q-1.1 0-1.7-.64-.58-.64-.58-1.96 0-.72.26-1.58.279-.86.72-1.68.46-.82 1-1.42.54-.62 1.08-.86.2-.1.42-.14.24-.06.46-.06.24 0 .52.04.3.02.56.12.46.14.8.54.36.4.36 1.16 0 .2-.04.44a2 2 0 0 1-.14.44q-.26.66-.8 1.26-.52.6-1.1.84a1.6 1.6 0 0 1-.32.12q-.14.02-.3.02-.42 0-.76-.12a2.6 2.6 0 0 1-.6-.32l-.16-.14-.1.2q-.14.38-.2.64-.04.26-.04.38 0 .24.02.5.02.24.12.4a.6.6 0 0 0 .2.16q.14.06.34.06h.22q.12-.02.2-.04.36-.08.58-.26.24-.2.5-.34l.08-.04-.02-.06q.34-.18.46-.22.12-.06.18-.1a.25.25 0 0 1 .18-.08.7.7 0 0 1 .54.16q.28.18.28.46 0 .12-.18.38a3 3 0 0 1-.42.52q-.24.24-.5.38a.5.5 0 0 0-.12.06.5.5 0 0 0-.12.06h.02q-.18.24-.36.24h-.16q-.08.14-.52.3-.44.18-.86.18m1.04-4.82q.18 0 .32-.06.16-.06.3-.14.48-.34.62-.88t-.1-.82a.2.2 0 0 0-.1-.08.25.25 0 0 0-.14-.04q-.22 0-.5.14a2.3 2.3 0 0 0-.52.4q-.261.26-.48.62l-.26.42a.2.2 0 0 1 .06-.02.2.2 0 0 1 .06-.02q.18 0 .24.12t.14.24q.**********"})),U||(U=AV.createElement("path",{fill:"#FFCA00",d:"M80.688 46.615a.5.5 0 0 0 .703.073l3.494-2.836a.5.5 0 1 0-.63-.776l-3.106 2.52-2.52-3.106a.5.5 0 0 0-.777.63zm-32.232-35.53c10.365-3.07 18.911.41 24.62 7.292 5.728 6.905 8.607 17.254 7.502 27.87l.995.104c1.128-10.84-1.802-21.47-7.727-28.612C67.9 10.573 58.95 6.934 48.172 10.126z"})),V||(V=AV.createElement("mask",{id:"maplibya_svg__a",fill:"#fff"},AV.createElement("path",{d:"m40.652 34.793.137.034.227.086.097.064.085.202.1.086.216.102.799.127.243.092-.134-.146-.432-.141-.11-.15.319.182.712.238 1.544.984.276.287.252.074.424.282.521.258.294.083 2.52.268.623-.045 1.651-.487.272-.124.214-.173.112-.069.152-.028.457-.019.626.113.075.037.07.06 1.306.701.3.073 1.124-.049.285.044.56.166.503.221 1.218.212.064.025.12.11.04.024.112.05.238.24.361.233.574.499.3.324.133.05 1.07.184.105.059.101.09.289.159.133.043 1.218.054 1.344.244.298.11.144.025.092.045.245.333.24.143.088.09.06.285.056.157.145.287.277.386.04.122-.012.165-.058.283-.019.315-.01.18.039.57.061.278.793 2.057.16.283.716 1.015.427.461.489.385 1.87.87 2.075.374 2.44.026 1.413.313 1.443.499 1.125.249h.232l.06.028.047.034.242.127.19.18.069.041.06.027.97.288.514.253.557.188.48.088.085.077.182.275.122.11.128.064.594.133 1.046.4.217.129.18.172.585.692 1.611 1.203.364.416.197.18.436.236.276.057.13.063.959.632.277.12.587.088.588.011.619-.071.343-.1.309-.09.53-.264.737-.487.135-.062.298-.054.135-.071.875-.686 1.093-1.32.29-.513.16-.223.241-.193.118-.118.418-.892.31-.851.037-.328-.037-.992-.122-.202-.163-.508-.14-.215-.29-.304-.106-.161-.445-1.26-.039-.322-.005-.341-.025-.128-.114-.292-.041-.144-.023-.323.017-.352.097-.65.09-.293-.015-.078-.026-.085-.011-.084.03-.077.082-.118.6-1.273.156-.236 1.255-1.274.102-.167.318-.382.133-.08.263-.115.118-.1.088-.095.963-.77 1.544-.668.909-.624.772-.39.302-.069 1.256.079.298-.028.547-.148.263-.15 1.017-.93.096-.047.32-.024.295-.083.179.154.289.118.321.073.273.026 1.445-.155.034-.026.069-.115.032-.026.146-.026.072-.004.078.03.053.047.011.041v.053l.026.078.21.222.268.058.609-.01.261.094.648.497.129.077.113.05.122.026.307.023 1.543.472.358.238.118.034.075.013.221.09.088.013.227-.013.15.049.275.14.298.047.129.053.083.082.015.11-.038.081-.114.143-.022.073.061.478.062.121.101.098.133.08-.052.154-.082.493-.132-.054.023-.075-.015-.066-.043-.062-.06-.065-.101.64.009.16.099.082.403.156.148.028.107.066.129.15.107.17.085.235.091.052.105.032.081.047.052.09.062.168.064.062-.011-.13-.075-.35.127.05.289.005.058.024.185.156.042.026.83.073.915-.015 1.126.266 1.046.431-.156.154-.03.06.053.034.172.073.416.326.15.041 1.256-.013 1.75.125.596-.181.148-.107.077-.03.266.052.053-.015.05-.03.049-.015.054.032.027.023.08.048.092.025.131.07.216.037.272.106.768.167.24.146.101.255-.06.31-.015.17.049.139.225.255.35.513.034.133.097.22.13.228-.025.025-.011.026-.412.333-.186.265-.242.657-1.073.883-.081.246-.002.283.066.65.005.634.154.824.045.156.231.478.086.393.288.619.09.309-.041.266-.099.264-.083.308-.069.421-.201.529-.022.152-.062.258-.129.263-.386.588-.201.204-.178.23-.064.106-.11.262-.274.497-.105.268-.002.294.06.165.186.338.568 1.43.03.17-.008.152-.11.397.045.317.339.573.095.313v.186l-.06.551.015.294.066.263.547 1.33.117.407.044.435v66.763l-.005.012-.006.011-.005.011-.006.011-.015.002-.015.002-.015.002-.015.002h-6.515v3.495l-.815-.427-.815-.427-.815-.428-.816-.429-.815-.427-.817-.428-.815-.429-.817-.427-.816-.429-.815-.429-.815-.43-.816-.429-.815-.429-.815-.429-.815-.429-.816-.43-.817-.431-.815-.429-.817-.431-.815-.431-.816-.431-.815-.431-.817-.431-.815-.431-.817-.431-.816-.431-.815-.431-.817-.433-.815-.433-.818-.431-.815-.433-.815-.433-.815-.433-.817-.433-.816-.433-.817-.433-.815-.433-.815-.435-.673-.358-.143-.076-.815-.435-.817-.435-.815-.435-.816-.435-.815-.434-.817-.435-.815-.435-.817-.435-.816-.437-.815-.434-.815-.437-.815-.437-.816-.436-.817-.437-.815-.437-.815-.436-.817-.437-.816-.437-.817-.436-.815-.439-.815-.439-.816-.438-.817-.439-.815-.438-.817-.439-.602-.324-.135.019-.294.146-.38.187-.38.188-.381.187-.38.188-.381.187-.38.187-.383.188-.38.187-.381.188-.38.187-.38.187-.381.188-.383.187-.378.188-.38.187-.383.188-.116.058-.004.002-1.13.62-1.128.618-1.13.619-1.13.618-.419.229-.103.011-.095-.049-.3-.268-.827-.736-.824-.737-.827-.738-.826-.738-.371-.334-.774-.435-.671-.163-.583-.142-.583-.141-.583-.142-.583-.143-.583-.142-.583-.141-.582-.142-.583-.143-.583-.142-.583-.141-.583-.14-.583-.143-.583-.142-.582-.143-.583-.142-.583-.142-.504-1.029-.463-.943-.74-1.513-.01-.02-.476-.975-.457-.935-.17-.223-.216-.118-.274-.09-2.095-.695-1.577-.523-1.259-.42-.142-.028-.143.017-.73.271-.773.295-.262.026-.124-.049-.868-.7-.12-.136-.082-.253-.116-.674-.124-.197-.982-.58-.083-.194-.013-1.01-.03-1.031-.028-.962-.088-.463-.253-.466-1.008-1.413-.812-1.136-1.008-1.415-.926-1.3-.154-.41.163-.346.092-.118.12-.292.225-.214.058-.107.025-.126.007-.146 2.332-1.119.123-.148.276-.948-.015-.157-.075-.207.019-.256.165-.787-.027-.107-.105-.09-.296-.165-.073-.068-.064-.144-.127-.774-.56-1.98.003-.123.03-.12.109-.292.088-.439.094-.157.285-.315.052-.142-.065-.156-.096-.167-.02-.155.162-.12.186-.102.11-.148.449-1.488.02-.065.011-.294-.965-2.564-.086-.437.332-2.637.159-1.26-.02-1.431-.147-1.155-.523-1.816-.523-1.82-.78-1.475-.844-1.267-.345-.412-.394-.47-.148-.253 1.54-.855 1.477-.783.195-.053.482-.032.169-.099.82-1.068.698-1.129.601-.687.407-.85.105-.31.002-.26-.165-.539-.03-.274.037-.296-.009-.144-.208-.44-.2-.814-.493-1.323-.014-.135.072-.504.103-.18.422-.472.445-.791.34-.274.742.01.362-.145.108-.086.098-.1.078-.115.111-.268.28-.263.085-.258.055-.624.148-.**************.**************-.04.221-.18.24-.147.216-.218.266-.618.18-.195 2.04-.98 1.729-.83.46-.31.083-.073.133-.129.118-.244-.022-.33-.154-.28-.442-.437-.135-.304-.006-.431.096-.828-.047-.816.118-.534.004-.44.017-.122.189-.75z"}))),B||(B=AV.createElement("path",{stroke:"#fff",strokeWidth:2,d:"m40.652 34.793.137.034.227.086.097.064.085.202.1.086.216.102.799.127.243.092-.134-.146-.432-.141-.11-.15.319.182.712.238 1.544.984.276.287.252.074.424.282.521.258.294.083 2.52.268.623-.045 1.651-.487.272-.124.214-.173.112-.069.152-.028.457-.019.626.113.075.037.07.06 1.306.701.3.073 1.124-.049.285.044.56.166.503.221 1.218.212.064.025.12.11.04.024.112.05.238.24.361.233.574.499.3.324.133.05 1.07.184.105.059.101.09.289.159.133.043 1.218.054 1.344.244.298.11.144.025.092.045.245.333.24.143.088.09.06.285.056.157.145.287.277.386.04.122-.012.165-.058.283-.019.315-.01.18.039.57.061.278.793 2.057.16.283.716 1.015.427.461.489.385 1.87.87 2.075.374 2.44.026 1.413.313 1.443.499 1.125.249h.232l.06.028.047.034.242.127.19.18.069.041.06.027.97.288.514.253.557.188.48.088.085.077.182.275.122.11.128.064.594.133 1.046.4.217.129.18.172.585.692 1.611 1.203.364.416.197.18.436.236.276.057.13.063.959.632.277.12.587.088.588.011.619-.071.343-.1.309-.09.53-.264.737-.487.135-.062.298-.054.135-.071.875-.686 1.093-1.32.29-.513.16-.223.241-.193.118-.118.418-.892.31-.851.037-.328-.037-.992-.122-.202-.163-.508-.14-.215-.29-.304-.106-.161-.445-1.26-.039-.322-.005-.341-.025-.128-.114-.292-.041-.144-.023-.323.017-.352.097-.65.09-.293-.015-.078-.026-.085-.011-.084.03-.077.082-.118.6-1.273.156-.236 1.255-1.274.102-.167.318-.382.133-.08.263-.115.118-.1.088-.095.963-.77 1.544-.668.909-.624.772-.39.302-.069 1.256.079.298-.028.547-.148.263-.15 1.017-.93.096-.047.32-.024.295-.083.179.154.289.118.321.073.273.026 1.445-.155.034-.026.069-.115.032-.026.146-.026.072-.004.078.03.053.047.011.041v.053l.026.078.21.222.268.058.609-.01.261.094.648.497.129.077.113.05.122.026.307.023 1.543.472.358.238.118.034.075.013.221.09.088.013.227-.013.15.049.275.14.298.047.129.053.083.082.015.11-.038.081-.114.143-.022.073.061.478.062.121.101.098.133.08-.052.154-.082.493-.132-.054.023-.075-.015-.066-.043-.062-.06-.065-.101.64.009.16.099.082.403.156.148.028.107.066.129.15.107.17.085.235.091.052.105.032.081.047.052.09.062.168.064.062-.011-.13-.075-.35.127.05.289.005.058.024.185.156.042.026.83.073.915-.015 1.126.266 1.046.431-.156.154-.03.06.053.034.172.073.416.326.15.041 1.256-.013 1.75.125.596-.181.148-.107.077-.03.266.052.053-.015.05-.03.049-.015.054.032.027.023.08.048.092.025.131.07.216.037.272.106.768.167.24.146.101.255-.06.31-.015.17.049.139.225.255.35.513.034.133.097.22.13.228-.025.025-.011.026-.412.333-.186.265-.242.657-1.073.883-.081.246-.002.283.066.65.005.634.154.824.045.156.231.478.086.393.288.619.09.309-.041.266-.099.264-.083.308-.069.421-.201.529-.022.152-.062.258-.129.263-.386.588-.201.204-.178.23-.064.106-.11.262-.274.497-.105.268-.002.294.06.165.186.338.568 1.43.03.17-.008.152-.11.397.045.317.339.573.095.313v.186l-.06.551.015.294.066.263.547 1.33.117.407.044.435v66.763l-.005.012-.006.011-.005.011-.006.011-.015.002-.015.002-.015.002-.015.002h-6.515v3.495l-.815-.427-.815-.427-.815-.428-.816-.429-.815-.427-.817-.428-.815-.429-.817-.427-.816-.429-.815-.429-.815-.43-.816-.429-.815-.429-.815-.429-.815-.429-.816-.43-.817-.431-.815-.429-.817-.431-.815-.431-.816-.431-.815-.431-.817-.431-.815-.431-.817-.431-.816-.431-.815-.431-.817-.433-.815-.433-.818-.431-.815-.433-.815-.433-.815-.433-.817-.433-.816-.433-.817-.433-.815-.433-.815-.435-.673-.358-.143-.076-.815-.435-.817-.435-.815-.435-.816-.435-.815-.434-.817-.435-.815-.435-.817-.435-.816-.437-.815-.434-.815-.437-.815-.437-.816-.436-.817-.437-.815-.437-.815-.436-.817-.437-.816-.437-.817-.436-.815-.439-.815-.439-.816-.438-.817-.439-.815-.438-.817-.439-.602-.324-.135.019-.294.146-.38.187-.38.188-.381.187-.38.188-.381.187-.38.187-.383.188-.38.187-.381.188-.38.187-.38.187-.381.188-.383.187-.378.188-.38.187-.383.188-.116.058-.004.002-1.13.62-1.128.618-1.13.619-1.13.618-.419.229-.103.011-.095-.049-.3-.268-.827-.736-.824-.737-.827-.738-.826-.738-.371-.334-.774-.435-.671-.163-.583-.142-.583-.141-.583-.142-.583-.143-.583-.142-.583-.141-.582-.142-.583-.143-.583-.142-.583-.141-.583-.14-.583-.143-.583-.142-.582-.143-.583-.142-.583-.142-.504-1.029-.463-.943-.74-1.513-.01-.02-.476-.975-.457-.935-.17-.223-.216-.118-.274-.09-2.095-.695-1.577-.523-1.259-.42-.142-.028-.143.017-.73.271-.773.295-.262.026-.124-.049-.868-.7-.12-.136-.082-.253-.116-.674-.124-.197-.982-.58-.083-.194-.013-1.01-.03-1.031-.028-.962-.088-.463-.253-.466-1.008-1.413-.812-1.136-1.008-1.415-.926-1.3-.154-.41.163-.346.092-.118.12-.292.225-.214.058-.107.025-.126.007-.146 2.332-1.119.123-.148.276-.948-.015-.157-.075-.207.019-.256.165-.787-.027-.107-.105-.09-.296-.165-.073-.068-.064-.144-.127-.774-.56-1.98.003-.123.03-.12.109-.292.088-.439.094-.157.285-.315.052-.142-.065-.156-.096-.167-.02-.155.162-.12.186-.102.11-.148.449-1.488.02-.065.011-.294-.965-2.564-.086-.437.332-2.637.159-1.26-.02-1.431-.147-1.155-.523-1.816-.523-1.82-.78-1.475-.844-1.267-.345-.412-.394-.47-.148-.253 1.54-.855 1.477-.783.195-.053.482-.032.169-.099.82-1.068.698-1.129.601-.687.407-.85.105-.31.002-.26-.165-.539-.03-.274.037-.296-.009-.144-.208-.44-.2-.814-.493-1.323-.014-.135.072-.504.103-.18.422-.472.445-.791.34-.274.742.01.362-.145.108-.086.098-.1.078-.115.111-.268.28-.263.085-.258.055-.624.148-.**************.**************-.04.221-.18.24-.147.216-.218.266-.618.18-.195 2.04-.98 1.729-.83.46-.31.083-.073.133-.129.118-.244-.022-.33-.154-.28-.442-.437-.135-.304-.006-.431.096-.828-.047-.816.118-.534.004-.44.017-.122.189-.75z",mask:"url(#maplibya_svg__a)"})),x||(x=AV.createElement("g",{clipPath:"url(#maplibya_svg__b)"},AV.createElement("path",{fill:"#FFCA00",d:"m81.922 76.938-4.242 4.243-4.243-4.243a6 6 0 1 1 8.485 0m-4.242-2.91a1.333 1.333 0 1 0 0-2.666 1.333 1.333 0 0 0 0 2.667"}))),R||(R=AV.createElement("path",{fill:"#fff",d:"M67.624 92.363H61.87v-9.338h1.638v7.896h4.116zm2.244-7.588a.917.917 0 0 1-.91-.91c0-.504.42-.91.91-.91.504 0 .91.406.91.91s-.406.91-.91.91m.742 7.588h-1.47v-6.762h1.47zm3.457-2.03c.322.504 1.05.896 1.736.896 1.176 0 1.918-.924 1.918-2.24s-.742-2.254-1.918-2.254c-.686 0-1.414.42-1.736.924zm0 2.03h-1.47v-9.338h1.47v3.514a2.67 2.67 0 0 1 2.17-1.106c1.736 0 2.996 1.372 2.996 3.556 0 2.226-1.274 3.542-2.996 3.542-.882 0-1.652-.42-2.17-1.092zm6.45 2.646.225-1.316c.154.07.392.112.56.112.462 0 .77-.14.966-.588l.336-.77-2.772-6.846H81.4l1.974 5.068 1.974-5.068h1.582l-3.262 7.98c-.462 1.148-1.274 1.512-2.324 1.526-.21 0-.616-.042-.826-.098m13.016-2.646h-1.47v-.728c-.504.574-1.288.896-2.184.896-1.106 0-2.352-.742-2.352-2.24 0-1.554 1.246-2.198 2.352-2.198.91 0 1.694.294 2.184.868v-1.008c0-.784-.644-1.26-1.582-1.26-.756 0-1.414.28-2.002.854l-.602-1.022c.784-.742 1.764-1.092 2.842-1.092 1.498 0 2.814.63 2.814 2.45zm-3.08-.84c.644 0 1.274-.238 1.61-.7v-1.022c-.336-.462-.966-.7-1.61-.7-.826 0-1.442.476-1.442 1.218 0 .728.616 1.204 1.442 1.204"})),C||(C=AV.createElement("defs",null,AV.createElement("clipPath",{id:"maplibya_svg__b"},AV.createElement("path",{fill:"#fff",d:"M69.68 65.363h16v16h-16z"})))));function Aj(){return(Aj=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let AI=A=>AV.createElement("svg",Aj({xmlns:"http://www.w3.org/2000/svg",width:175,height:132,fill:"none"},A),M||(M=AV.createElement("path",{fill:"#fff",d:"M12.072 20.488a.9.9 0 0 1-.576-.144.78.78 0 0 1-.336-.384 4 4 0 0 1-.312-.528 2.1 2.1 0 0 1-.264-.672 25 25 0 0 1-.096-1.488q0-.72.072-2.016a10 10 0 0 1 .288-1.872q.216-.84.432-1.416.216-.6.336-.888.12-.312.048-.312-.096 0-.216.192a10 10 0 0 0-.24.456q-.12.288-.288.624-.144.336-.312.648a24 24 0 0 1-.792 1.584 37 37 0 0 1-.888 1.584q-.456.768-.792 1.344-.216.24-.504.696-.288.432-.624.912t-.696.792q-.528.504-1.08.6a1.52 1.52 0 0 1-1.032-.12q-.24-.024-.504-.264a1.7 1.7 0 0 1-.432-.552q-.144-.336-.072-.624a11 11 0 0 1 0-1.032q.048-.72.144-1.56.096-.864.192-1.656.12-.792.216-1.32.216-1.536.576-3.144a73 73 0 0 0 .696-3.24q.192-.48.336-1.08.168-.624.336-1.152.024-.528.384-.648a1.25 1.25 0 0 1 .768-.048q.432.096.6.336.24.24.456.744t-.12 1.272q-.48 1.176-.936 2.88-.432 1.68-.792 4.08l-.216 1.296q-.096.696-.192 1.344l-.12 1.08q-.024.408.024.432.192.048.408-.216.24-.264.504-.84a75 75 0 0 0 1.848-3.24 107 107 0 0 0 1.776-3.36q.408-.696.816-1.512.432-.84.816-1.536l.792-1.392q.384-.672.672-1.128.288-.48.432-.528.648 0 .96.336.336.336.528.816.048.12.048.408.024.288-.072.504-.024.168-.216.696-.168.528-.312 1.176-.24.648-.432 1.296-.168.624-.24 1.032-.192 1.032-.48 2.256t-.504 2.448q-.192 1.2-.216 2.136-.024.672 0 .888.048.192.144.408.048.192.216.096t.336-.336q.336-.504.504-.864.192-.36.384-.768.192-.432.528-1.08.6-1.104 1.224-2.424.648-1.32 1.224-2.616.6-1.296 1.032-2.304.456-1.296.768-2.088.312-.816.576-1.248t.504-.576q.12-.096.504-.096.408 0 .624.192.096.096.336.312t.216.504q-.12.384-.456 1.248a50 50 0 0 1-.744 1.92 85 85 0 0 1-.816 1.992q-.384.936-.648 1.488a10 10 0 0 1-.504.864q-.24.36-.24.552 0 .096-.24.6-.24.48-.624 1.176-.36.696-.792 1.44t-.816 1.368-.648.96q-.6.744-1.296 1.248-.672.528-1.2.792t-.696.264m9.264-2.4q-.168.024-.384-.12a5 5 0 0 0-.36-.288 2.3 2.3 0 0 1-.336-.504 1.2 1.2 0 0 1-.096-.48q0-.264.048-.624.12-.384.288-1.032t.408-1.392q.048-.288.168-.672.144-.408.288-.768l.216-.576q.168-.6.336-1.152.192-.552.456-1.272t.648-1.824l.792-2.16q.264-.72.384-1.032t.168-.504q0-.192.264-.36.288-.192.504-.192.336.024.648.408.312.36.432.888.12.504-.168.984a28 28 0 0 0-.768 1.944l-.72 2.112a37 37 0 0 1-.72 1.944 2 2 0 0 1-.168.408q-.12.288-.216.48a7 7 0 0 1-.192.36l-.072.168a5 5 0 0 1-.096.408 5 5 0 0 1-.144.456q-.024.12-.072.312a3 3 0 0 0-.072.384q.12-.144.336-.336.216-.216.552-.528.384-.336.672-.576t.576-.432q.264-.192.48-.36l.24-.168a2 2 0 0 0 .096-.168.3.3 0 0 1 .144-.12 1 1 0 0 1 .168-.12l.24-.192a2.5 2.5 0 0 1 .96-.384 3 3 0 0 1 .504-.048q.288 0 .6.24.312.216.576.624.24.312.288.744.072.408.024 1.104 0 .192-.12.72l-.144.792-.216.816h.192a.8.8 0 0 1 .504-.024l.264.072q.12.072.216.168.12.096.216.144.144.192-.048.552t-.432.504q-.576.384-1.032.48-.432.096-.912-.168-.456-.312-.624-.744-.144-.432-.12-1.176.096-1.056.12-1.512.024-.48-.24-.48-.288 0-.792.288a8 8 0 0 0-1.08.744q-.6.456-1.2 1.032-.576.552-1.032 1.128-.288.36-.624.72a1.2 1.2 0 0 1-.816.36m15.04.432a3.75 3.75 0 0 1-1.536.48 3.54 3.54 0 0 1-1.488-.216 2.7 2.7 0 0 1-1.128-.84q-.24-.312-.456-.984-.216-.696-.072-1.872a7.6 7.6 0 0 1 .672-2.232 8.4 8.4 0 0 1 1.344-1.992q.792-.864 1.728-1.176.336-.12.672-.168.36-.072.936-.024.552.072.984.288.432.192.624.432.384.384.504.744t.096.696q-.12.768-.696 1.656a3.33 3.33 0 0 1-1.56 1.32 3.3 3.3 0 0 1-.96.216q-.552.048-1.512-.144-.576-.12-.768.12-.168.24-.144.936a2.7 2.7 0 0 0 .192.768q.168.312.6.432.552.096.792.024.24-.096.6-.264.192-.12.36-.264.192-.144.36-.288.192-.144.312-.216a.6.6 0 0 1 .24-.12q.12-.024.24-.024.264.024.504.288t.312.504q0 .192-.24.552a5 5 0 0 1-.648.72q-.384.36-.864.648m-.312-5.352q.168-.072.48-.336a5 5 0 0 0 .6-.552q.264-.312.264-.48 0-.264-.36-.456-.336-.192-.888-.072-.192.048-.456.264a5 5 0 0 0-.528.504 4.5 4.5 0 0 0-.456.528q-.192.24-.264.384.096.12.384.192t.624.072q.36 0 .6-.048m5.615 5.328q-.072.024-.312-.024a7 7 0 0 1-.456-.12q-.216-.072-.264-.12a3 3 0 0 1-.264-.336q-.168-.264-.192-.696 0-.432.408-1.032.12-.24.288-.72t.336-1.008q.192-.552.336-.984.168-.432.24-.6.048-.408-.048-.576-.072-.192-.408-.216a.48.48 0 0 1-.384-.168q-.12-.168-.12-.6a1.9 1.9 0 0 1 .168-.576 1.25 1.25 0 0 1 .384-.432q.24-.12.6-.168a4 4 0 0 1 .744-.024q.36.024.504.168.168.096.264.432.12.312.192.648t.096.504q.24-.192.528-.528.312-.36.624-.672.384-.264.912-.528.528-.288.768-.288.264 0 .648.264.384.24.48.432.24.408.336.864a.82.82 0 0 1-.144.768q-.24.312-.672.624-.432.288-1.08.168-.144-.048-.312-.36a2 2 0 0 1-.168-.456q0-.072-.216.144l-.48.48a17 17 0 0 1-.504.528q-.408.504-.864 1.368t-1.08 2.088q-.288.6-.48 1.152-.192.528-.408.6m9.153.936q-1.32 0-2.04-.768-.696-.768-.696-2.352 0-.864.312-1.896.336-1.032.864-2.016a9.4 9.4 0 0 1 1.2-1.704q.648-.744 1.296-1.032.24-.12.504-.168.288-.072.552-.072.288 0 .624.048.36.024.672.144.552.168.96.648.432.48.432 1.392 0 .24-.048.528a2.3 2.3 0 0 1-.168.528 5 5 0 0 1-.96 1.512q-.624.72-1.32 1.008a2 2 0 0 1-.384.144 3 3 0 0 1-.36.024q-.504 0-.912-.144a3.1 3.1 0 0 1-.72-.384l-.192-.168-.12.24q-.168.456-.24.768-.048.312-.048.456 0 .288.024.6.024.288.144.48.096.12.24.192.168.072.408.072h.264q.144-.024.24-.048.432-.096.696-.312.288-.24.6-.408l.096-.048-.024-.072q.408-.216.552-.264.144-.072.216-.12a.3.3 0 0 1 .216-.096q.336-.048.648.192.336.216.336.552 0 .144-.216.456a3.5 3.5 0 0 1-.504.624 2.6 2.6 0 0 1-.6.456.6.6 0 0 0-.144.072.6.6 0 0 0-.144.072h.024q-.216.288-.432.288h-.192q-.096.168-.624.36a2.7 2.7 0 0 1-1.032.216m1.248-5.784a1 1 0 0 0 .384-.072q.192-.072.36-.168.576-.408.744-1.056t-.12-.984a.23.23 0 0 0-.12-.096.3.3 0 0 0-.168-.048q-.264 0-.6.168a2.7 2.7 0 0 0-.624.48q-.312.312-.576.744l-.312.504a.2.2 0 0 1 .072-.024.2.2 0 0 1 .072-.024q.216 0 .288.144t.168.288q.12.144.432.144"})),X||(X=AV.createElement("path",{fill:"#FFCA00",d:"M110.948 39.175a.5.5 0 0 0 .693-.141l2.483-3.753a.5.5 0 0 0-.834-.552l-2.207 3.336-3.336-2.207a.5.5 0 1 0-.552.834zm-36.89-21.88.28.415c7.923-5.333 16.044-4.663 22.678-.375 6.66 4.304 11.834 12.27 13.718 21.523l.49-.1.49-.1c-1.929-9.473-7.235-17.691-14.155-22.163-6.944-4.489-15.5-5.187-23.78.386z"})),W||(W=AV.createElement("path",{fill:"#fff",d:"M89.938 120.32H87.13v-11.88l-4.92 11.88h-1.2l-4.896-11.88v11.88h-2.808v-16.008h3.96l4.344 10.536 4.368-10.536h3.96zm8.647.288c-3.672 0-5.952-2.76-5.952-6.096 0-3.312 2.28-6.072 5.952-6.072 3.72 0 5.976 2.76 5.976 6.072 0 3.336-2.256 6.096-5.976 6.096m0-2.232c2.16 0 3.36-1.8 3.36-3.864 0-2.04-1.2-3.84-3.36-3.84-2.136 0-3.336 1.8-3.336 3.84 0 2.064 1.2 3.864 3.336 3.864m11.126 1.944h-2.52v-11.592h2.52v1.704c.864-1.104 2.256-1.968 3.768-1.968v2.496a3.7 3.7 0 0 0-.792-.072c-1.056 0-2.472.72-2.976 1.536zm11.112.288c-3.672 0-5.952-2.76-5.952-6.096 0-3.312 2.28-6.072 5.952-6.072 3.72 0 5.976 2.76 5.976 6.072 0 3.336-2.256 6.096-5.976 6.096m0-2.232c2.16 0 3.36-1.8 3.36-3.864 0-2.04-1.2-3.84-3.36-3.84-2.136 0-3.336 1.8-3.336 3.84 0 2.064 1.2 3.864 3.336 3.864m13.886 2.232c-3.528 0-6-2.568-6-6.096 0-3.504 2.472-6.072 6-6.072 2.256 0 3.624.96 4.44 2.064l-1.656 1.536q-.972-1.368-2.664-1.368c-2.112 0-3.504 1.584-3.504 3.84s1.392 3.864 3.504 3.864c1.128 0 2.016-.48 2.664-1.368l1.656 1.536c-.816 1.104-2.184 2.064-4.44 2.064m12.009 0c-3.528 0-6-2.568-6-6.096 0-3.504 2.472-6.072 6-6.072 2.256 0 3.624.96 4.44 2.064l-1.656 1.536q-.972-1.368-2.664-1.368c-2.112 0-3.504 1.584-3.504 3.84s1.392 3.864 3.504 3.864c1.128 0 2.016-.48 2.664-1.368l1.656 1.536c-.816 1.104-2.184 2.064-4.44 2.064m11.962 0c-3.672 0-5.952-2.76-5.952-6.096 0-3.312 2.28-6.072 5.952-6.072 3.72 0 5.976 2.76 5.976 6.072 0 3.336-2.256 6.096-5.976 6.096m0-2.232c2.16 0 3.36-1.8 3.36-3.864 0-2.04-1.2-3.84-3.36-3.84-2.136 0-3.336 1.8-3.336 3.84 0 2.064 1.2 3.864 3.336 3.864"})),S||(S=AV.createElement("path",{fill:"#FFCA00",d:"m137.635 79.483-14.23 14.23-14.23-14.23c-7.858-7.86-7.858-20.601 0-28.46 7.859-7.859 20.601-7.859 28.46 0s7.859 20.6 0 28.46m-14.23-9.758a4.472 4.472 0 1 0 0-8.944 4.472 4.472 0 0 0 0 8.944"})));function AL(){return(AL=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let AJ=A=>AV.createElement("svg",AL({xmlns:"http://www.w3.org/2000/svg",width:125,height:98,fill:"none"},A),D||(D=AV.createElement("path",{fill:"#fff",d:"M10.06 15.24a.76.76 0 0 1-.48-.12.65.65 0 0 1-.28-.32 3.4 3.4 0 0 1-.26-.44q-.18-.32-.22-.56-.06-.64-.08-1.24 0-.6.06-1.68a8.4 8.4 0 0 1 .24-1.56q.18-.7.36-1.18.18-.5.28-.74.1-.26.04-.26-.08 0-.18.16-.08.14-.2.38-.1.24-.24.52a7 7 0 0 1-.26.54q-.28.62-.66 1.32-.36.68-.74 1.32t-.66 1.12q-.18.2-.42.58-.24.36-.52.76t-.58.66q-.44.42-.9.5a1.27 1.27 0 0 1-.86-.1q-.2-.02-.42-.22a1.4 1.4 0 0 1-.36-.46.8.8 0 0 1-.06-.52 9 9 0 0 1 0-.86q.04-.6.12-1.3.08-.72.16-1.38.1-.66.18-1.1.18-1.28.48-2.62.32-1.34.58-2.7.16-.4.28-.9.14-.52.28-.96.02-.44.32-.54.3-.12.64-.04.36.08.5.28.2.2.38.62t-.1 1.06a20 20 0 0 0-.78 2.4q-.36 1.4-.66 3.4l-.18 1.08q-.08.58-.16 1.12l-.1.9q-.02.34.02.36.16.04.34-.18.2-.22.42-.7a63 63 0 0 0 1.54-2.7q.74-1.34 1.48-2.8.34-.58.68-1.26.36-.7.68-1.28l.66-1.16q.32-.56.56-.94.24-.4.36-.44.54 0 .8.28.28.28.44.68.04.1.04.34a.85.85 0 0 1-.06.42q-.02.14-.18.58-.14.44-.26.98-.2.54-.36 1.08-.14.52-.2.86-.16.86-.4 1.88t-.42 2.04q-.16 1-.18 1.78-.02.56 0 .74.04.16.12.34.04.16.18.08a.9.9 0 0 0 .28-.28q.28-.42.42-.72.16-.3.32-.64.16-.36.44-.9.5-.92 1.02-2.02.54-1.1 1.02-2.18.5-1.08.86-1.92.38-1.08.64-1.74.26-.68.48-1.04t.42-.48q.1-.08.42-.08.34 0 .52.16.08.08.28.26a.49.49 0 0 1 .18.42q-.1.32-.38 1.04a41 41 0 0 1-.62 1.6 71 71 0 0 1-.68 1.66q-.32.78-.54 1.24-.22.42-.42.72t-.2.46q0 .08-.2.5-.2.4-.52.98-.3.58-.66 1.2t-.68 1.14a9 9 0 0 1-.54.8q-.5.62-1.08 1.04a6 6 0 0 1-1 .66q-.44.22-.58.22m7.72-2q-.14.02-.32-.1a4 4 0 0 0-.3-.24q-.2-.24-.28-.42a1 1 0 0 1-.08-.4q0-.22.04-.52.1-.32.24-.86t.34-1.16a4 4 0 0 1 .14-.56q.12-.34.24-.64l.18-.48q.14-.5.28-.96.16-.46.38-1.06t.54-1.52l.66-1.8q.22-.6.32-.86t.14-.42q0-.16.22-.3.24-.16.42-.16.28.02.54.34.26.3.36.74.1.42-.14.82-.32.72-.64 1.62l-.6 1.76q-.3.88-.6 1.62a1.6 1.6 0 0 1-.14.34 6.135 6.135 0 0 1-.34.7l-.06.14q-.02.12-.08.34t-.12.38a6 6 0 0 1-.06.26 3 3 0 0 0-.06.32q.1-.12.28-.28.18-.18.46-.44.32-.28.56-.48t.48-.36q.22-.16.4-.3l.2-.14q.04-.06.08-.14a.27.27 0 0 1 .12-.1.8.8 0 0 1 .14-.1l.2-.16a2.1 2.1 0 0 1 .8-.32 2.4 2.4 0 0 1 .42-.04q.24 0 .5.2.26.18.48.52.2.26.24.62.06.34.02.92 0 .16-.1.6l-.12.66-.18.68h.16a.65.65 0 0 1 .42-.02l.22.06a1 1 0 0 1 .18.14q.1.08.18.12.12.16-.04.46t-.36.42q-.48.32-.86.4-.36.08-.76-.14a1.35 1.35 0 0 1-.52-.62q-.12-.36-.1-.98.08-.88.1-1.26.02-.4-.2-.4-.24 0-.66.24a6.5 6.5 0 0 0-.9.62q-.5.38-1 .86-.48.46-.86.94-.24.3-.52.6a1 1 0 0 1-.68.3m12.533.36q-.6.34-1.28.4a2.95 2.95 0 0 1-1.24-.18q-.58-.24-.94-.7-.2-.26-.38-.82-.18-.58-.06-1.56.12-.92.56-1.86.46-.94 1.12-1.66t1.44-.98a3 3 0 0 1 .56-.14q.3-.06.78-.02.46.06.82.24.36.16.52.36.32.32.42.62t.08.58q-.1.64-.58 1.38-.46.74-1.3 1.1a2.7 2.7 0 0 1-.8.18q-.46.04-1.26-.12-.48-.1-.64.1-.14.2-.12.78.04.36.16.64.14.26.5.36.46.08.66.02.2-.08.5-.22.16-.1.3-.22.16-.12.3-.24.16-.12.26-.18a.5.5 0 0 1 .2-.1 1 1 0 0 1 .2-.02q.22.02.42.24t.26.42q0 .16-.2.46a4 4 0 0 1-.54.6q-.32.3-.72.54m-.26-4.46q.14-.06.4-.28.28-.22.5-.46.22-.26.22-.4 0-.22-.3-.38-.28-.16-.74-.06-.16.04-.38.22t-.44.42a4 4 0 0 0-.38.44q-.16.2-.22.32.08.1.32.16t.52.06q.3 0 .5-.04m4.68 4.44q-.06.02-.26-.02a5 5 0 0 1-.38-.1.7.7 0 0 1-.22-.1 2 2 0 0 1-.22-.28q-.14-.22-.16-.58 0-.36.34-.86.1-.2.24-.6t.28-.84q.16-.46.28-.82.14-.36.2-.5.04-.34-.04-.48-.06-.16-.34-.18a.4.4 0 0 1-.32-.14q-.1-.14-.1-.5.04-.28.14-.48.12-.22.32-.36.2-.1.5-.14a3.3 3.3 0 0 1 .62-.02q.3.02.42.14.14.08.22.36.1.26.16.54t.08.42q.2-.16.44-.44.26-.3.52-.56.32-.22.76-.44.44-.24.64-.24.219 0 .54.22.32.2.4.36.2.34.28.72a.69.69 0 0 1-.12.64 2.7 2.7 0 0 1-.56.52q-.36.24-.9.14-.12-.04-.26-.3-.12-.26-.14-.38 0-.06-.18.12l-.4.4q-.22.24-.42.44-.34.42-.72 1.14t-.9 1.74a9 9 0 0 0-.4.96q-.16.44-.34.5m7.627.78q-1.1 0-1.7-.64-.58-.64-.58-1.96 0-.72.26-1.58.279-.86.72-1.68.46-.82 1-1.42.54-.62 1.08-.86.2-.1.42-.14.24-.06.46-.06.24 0 .52.04.3.02.56.12.46.14.8.54.36.4.36 1.16 0 .2-.04.44a2 2 0 0 1-.14.44q-.26.66-.8 1.26-.52.6-1.1.84a1.6 1.6 0 0 1-.32.12q-.14.02-.3.02-.42 0-.76-.12a2.6 2.6 0 0 1-.6-.32l-.16-.14-.1.2q-.14.38-.2.64-.04.26-.04.38 0 .24.02.5.02.24.12.4a.6.6 0 0 0 .2.16q.14.06.34.06h.22q.12-.02.2-.04.36-.08.58-.26.24-.2.5-.34l.08-.04-.02-.06q.34-.18.46-.22.12-.06.18-.1a.25.25 0 0 1 .18-.08.7.7 0 0 1 .54.16q.28.18.28.46 0 .12-.18.38a3 3 0 0 1-.42.52q-.24.24-.5.38a.5.5 0 0 0-.12.06.5.5 0 0 0-.12.06h.02q-.18.24-.36.24h-.16q-.08.14-.52.3-.44.18-.86.18m1.04-4.82q.18 0 .32-.06.16-.06.3-.14.48-.34.62-.88t-.1-.82a.2.2 0 0 0-.1-.08.25.25 0 0 0-.14-.04q-.22 0-.5.14a2.3 2.3 0 0 0-.52.4q-.261.26-.48.62l-.26.42a.2.2 0 0 1 .06-.02.2.2 0 0 1 .06-.02q.18 0 .24.12t.14.24q.**********"})),G||(G=AV.createElement("path",{fill:"#FFCA00",d:"M69.623 32.194a.5.5 0 0 0 .703.073l3.494-2.835a.5.5 0 0 0-.63-.777l-3.106 2.52-2.52-3.105a.5.5 0 0 0-.777.63zM53.02 13.368l.142.48c5.276-1.563 9.61.204 12.514 3.704 2.923 3.524 4.404 8.823 3.837 14.275l.497.052.497.052c.591-5.676-.94-11.255-4.061-15.017-3.14-3.785-7.88-5.71-13.568-4.025z"})),j||(j=AV.createElement("path",{fill:"#fff",d:"M60.085 91h-2.34v-9.9l-4.1 9.9h-1l-4.08-9.9V91h-2.34V77.66h3.3l3.62 8.78 3.64-8.78h3.3zm7.22.24c-3.06 0-4.96-2.3-4.96-5.08 0-2.76 1.9-5.06 4.96-5.06 3.1 0 4.98 2.3 4.98 5.06 0 2.78-1.88 5.08-4.98 5.08m0-1.86c1.8 0 2.8-1.5 2.8-3.22 0-1.7-1-3.2-2.8-3.2-1.78 0-2.78 1.5-2.78 3.2 0 1.72 1 3.22 2.78 3.22M76.588 91h-2.1v-9.66h2.1v1.42c.72-.92 1.88-1.64 3.14-1.64v2.08q-.27-.06-.66-.06c-.88 0-2.06.6-2.48 1.28zm9.274.24c-3.06 0-4.96-2.3-4.96-5.08 0-2.76 1.9-5.06 4.96-5.06 3.1 0 4.98 2.3 4.98 5.06 0 2.78-1.88 5.08-4.98 5.08m0-1.86c1.8 0 2.8-1.5 2.8-3.22 0-1.7-1-3.2-2.8-3.2-1.78 0-2.78 1.5-2.78 3.2 0 1.72 1 3.22 2.78 3.22m11.584 1.86c-2.94 0-5-2.14-5-5.08 0-2.92 2.06-5.06 5-5.06 1.88 0 3.02.8 3.7 1.72l-1.38 1.28q-.81-1.14-2.22-1.14c-1.76 0-2.92 1.32-2.92 3.2s1.16 3.22 2.92 3.22c.94 0 1.68-.4 2.22-1.14l1.38 1.28c-.68.92-1.82 1.72-3.7 1.72m10.022 0c-2.94 0-5-2.14-5-5.08 0-2.92 2.06-5.06 5-5.06 1.88 0 3.02.8 3.7 1.72l-1.38 1.28q-.81-1.14-2.22-1.14c-1.76 0-2.92 1.32-2.92 3.2s1.16 3.22 2.92 3.22c.94 0 1.68-.4 2.22-1.14l1.38 1.28c-.68.92-1.82 1.72-3.7 1.72m9.981 0c-3.06 0-4.96-2.3-4.96-5.08 0-2.76 1.9-5.06 4.96-5.06 3.1 0 4.98 2.3 4.98 5.06 0 2.78-1.88 5.08-4.98 5.08m0-1.86c1.8 0 2.8-1.5 2.8-3.22 0-1.7-1-3.2-2.8-3.2-1.78 0-2.78 1.5-2.78 3.2 0 1.72 1 3.22 2.78 3.22"})),I||(I=AV.createElement("path",{fill:"#FFCA00",d:"M97.842 60.217 87.5 70.557l-10.341-10.34c-5.712-5.712-5.712-14.972 0-20.683s14.971-5.712 20.682 0c5.712 5.711 5.712 14.971 0 20.683M87.5 53.125a3.25 3.25 0 1 0 0-6.5 3.25 3.25 0 0 0 0 6.5"})));function AH(){return(AH=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let AN=A=>AV.createElement("svg",AH({xmlns:"http://www.w3.org/2000/svg",width:175,height:132,fill:"none"},A),L||(L=AV.createElement("path",{fill:"#fff",d:"M12.072 20.488a.9.9 0 0 1-.576-.144.78.78 0 0 1-.336-.384 4 4 0 0 1-.312-.528 2.1 2.1 0 0 1-.264-.672 25 25 0 0 1-.096-1.488q0-.72.072-2.016a10 10 0 0 1 .288-1.872q.216-.84.432-1.416.216-.6.336-.888.12-.312.048-.312-.096 0-.216.192a10 10 0 0 0-.24.456q-.12.288-.288.624-.144.336-.312.648a24 24 0 0 1-.792 1.584 37 37 0 0 1-.888 1.584q-.456.768-.792 1.344-.216.24-.504.696-.288.432-.624.912t-.696.792q-.528.504-1.08.6a1.52 1.52 0 0 1-1.032-.12q-.24-.024-.504-.264a1.7 1.7 0 0 1-.432-.552q-.144-.336-.072-.624a11 11 0 0 1 0-1.032q.048-.72.144-1.56.096-.864.192-1.656.12-.792.216-1.32.216-1.536.576-3.144a73 73 0 0 0 .696-3.24q.192-.48.336-1.08.168-.624.336-1.152.024-.528.384-.648a1.25 1.25 0 0 1 .768-.048q.432.096.6.336.24.24.456.744t-.12 1.272q-.48 1.176-.936 2.88-.432 1.68-.792 4.08l-.216 1.296q-.096.696-.192 1.344l-.12 1.08q-.024.408.024.432.192.048.408-.216.24-.264.504-.84a75 75 0 0 0 1.848-3.24 107 107 0 0 0 1.776-3.36q.408-.696.816-1.512.432-.84.816-1.536l.792-1.392q.384-.672.672-1.128.288-.48.432-.528.648 0 .96.336.336.336.528.816.048.12.048.408.024.288-.072.504-.024.168-.216.696-.168.528-.312 1.176-.24.648-.432 1.296-.168.624-.24 1.032-.192 1.032-.48 2.256t-.504 2.448q-.192 1.2-.216 2.136-.024.672 0 .888.048.192.144.408.048.192.216.096t.336-.336q.336-.504.504-.864.192-.36.384-.768.192-.432.528-1.08.6-1.104 1.224-2.424.648-1.32 1.224-2.616.6-1.296 1.032-2.304.456-1.296.768-2.088.312-.816.576-1.248t.504-.576q.12-.096.504-.096.408 0 .624.192.096.096.336.312t.216.504q-.12.384-.456 1.248a50 50 0 0 1-.744 1.92 85 85 0 0 1-.816 1.992q-.384.936-.648 1.488a10 10 0 0 1-.504.864q-.24.36-.24.552 0 .096-.24.6-.24.48-.624 1.176-.36.696-.792 1.44t-.816 1.368-.648.96q-.6.744-1.296 1.248-.672.528-1.2.792t-.696.264m9.264-2.4q-.168.024-.384-.12a5 5 0 0 0-.36-.288 2.3 2.3 0 0 1-.336-.504 1.2 1.2 0 0 1-.096-.48q0-.264.048-.624.12-.384.288-1.032t.408-1.392q.048-.288.168-.672.144-.408.288-.768l.216-.576q.168-.6.336-1.152.192-.552.456-1.272t.648-1.824l.792-2.16q.264-.72.384-1.032t.168-.504q0-.192.264-.36.288-.192.504-.192.336.024.648.408.312.36.432.888.12.504-.168.984a28 28 0 0 0-.768 1.944l-.72 2.112a37 37 0 0 1-.72 1.944 2 2 0 0 1-.168.408q-.12.288-.216.48a7 7 0 0 1-.192.36l-.072.168a5 5 0 0 1-.096.408 5 5 0 0 1-.144.456q-.024.12-.072.312a3 3 0 0 0-.072.384q.12-.144.336-.336.216-.216.552-.528.384-.336.672-.576t.576-.432q.264-.192.48-.36l.24-.168a2 2 0 0 0 .096-.168.3.3 0 0 1 .144-.12 1 1 0 0 1 .168-.12l.24-.192a2.5 2.5 0 0 1 .96-.384 3 3 0 0 1 .504-.048q.288 0 .6.24.312.216.576.624.24.312.288.744.072.408.024 1.104 0 .192-.12.72l-.144.792-.216.816h.192a.8.8 0 0 1 .504-.024l.264.072q.12.072.216.168.12.096.216.144.144.192-.048.552t-.432.504q-.576.384-1.032.48-.432.096-.912-.168-.456-.312-.624-.744-.144-.432-.12-1.176.096-1.056.12-1.512.024-.48-.24-.48-.288 0-.792.288a8 8 0 0 0-1.08.744q-.6.456-1.2 1.032-.576.552-1.032 1.128-.288.36-.624.72a1.2 1.2 0 0 1-.816.36m15.04.432a3.75 3.75 0 0 1-1.536.48 3.54 3.54 0 0 1-1.488-.216 2.7 2.7 0 0 1-1.128-.84q-.24-.312-.456-.984-.216-.696-.072-1.872a7.6 7.6 0 0 1 .672-2.232 8.4 8.4 0 0 1 1.344-1.992q.792-.864 1.728-1.176.336-.12.672-.168.36-.072.936-.024.552.072.984.288.432.192.624.432.384.384.504.744t.096.696q-.12.768-.696 1.656a3.33 3.33 0 0 1-1.56 1.32 3.3 3.3 0 0 1-.96.216q-.552.048-1.512-.144-.576-.12-.768.12-.168.24-.144.936a2.7 2.7 0 0 0 .192.768q.168.312.6.432.552.096.792.024.24-.096.6-.264.192-.12.36-.264.192-.144.36-.288.192-.144.312-.216a.6.6 0 0 1 .24-.12q.12-.024.24-.024.264.024.504.288t.312.504q0 .192-.24.552a5 5 0 0 1-.648.72q-.384.36-.864.648m-.312-5.352q.168-.072.48-.336a5 5 0 0 0 .6-.552q.264-.312.264-.48 0-.264-.36-.456-.336-.192-.888-.072-.192.048-.456.264a5 5 0 0 0-.528.504 4.5 4.5 0 0 0-.456.528q-.192.24-.264.384.096.12.384.192t.624.072q.36 0 .6-.048m5.615 5.328q-.072.024-.312-.024a7 7 0 0 1-.456-.12q-.216-.072-.264-.12a3 3 0 0 1-.264-.336q-.168-.264-.192-.696 0-.432.408-1.032.12-.24.288-.72t.336-1.008q.192-.552.336-.984.168-.432.24-.6.048-.408-.048-.576-.072-.192-.408-.216a.48.48 0 0 1-.384-.168q-.12-.168-.12-.6a1.9 1.9 0 0 1 .168-.576 1.25 1.25 0 0 1 .384-.432q.24-.12.6-.168a4 4 0 0 1 .744-.024q.36.024.504.168.168.096.264.432.12.312.192.648t.096.504q.24-.192.528-.528.312-.36.624-.672.384-.264.912-.528.528-.288.768-.288.264 0 .648.264.384.24.48.432.24.408.336.864a.82.82 0 0 1-.144.768q-.24.312-.672.624-.432.288-1.08.168-.144-.048-.312-.36a2 2 0 0 1-.168-.456q0-.072-.216.144l-.48.48a17 17 0 0 1-.504.528q-.408.504-.864 1.368t-1.08 2.088q-.288.6-.48 1.152-.192.528-.408.6m9.153.936q-1.32 0-2.04-.768-.696-.768-.696-2.352 0-.864.312-1.896.336-1.032.864-2.016a9.4 9.4 0 0 1 1.2-1.704q.648-.744 1.296-1.032.24-.12.504-.168.288-.072.552-.072.288 0 .624.048.36.024.672.144.552.168.96.648.432.48.432 1.392 0 .24-.048.528a2.3 2.3 0 0 1-.168.528 5 5 0 0 1-.96 1.512q-.624.72-1.32 1.008a2 2 0 0 1-.384.144 3 3 0 0 1-.36.024q-.504 0-.912-.144a3.1 3.1 0 0 1-.72-.384l-.192-.168-.12.24q-.168.456-.24.768-.048.312-.048.456 0 .288.024.6.024.288.144.48.096.12.24.192.168.072.408.072h.264q.144-.024.24-.048.432-.096.696-.312.288-.24.6-.408l.096-.048-.024-.072q.408-.216.552-.264.144-.072.216-.12a.3.3 0 0 1 .216-.096q.336-.048.648.192.336.216.336.552 0 .144-.216.456a3.5 3.5 0 0 1-.504.624 2.6 2.6 0 0 1-.6.456.6.6 0 0 0-.144.072.6.6 0 0 0-.144.072h.024q-.216.288-.432.288h-.192q-.096.168-.624.36a2.7 2.7 0 0 1-1.032.216m1.248-5.784a1 1 0 0 0 .384-.072q.192-.072.36-.168.576-.408.744-1.056t-.12-.984a.23.23 0 0 0-.12-.096.3.3 0 0 0-.168-.048q-.264 0-.6.168a2.7 2.7 0 0 0-.624.48q-.312.312-.576.744l-.312.504a.2.2 0 0 1 .072-.024.2.2 0 0 1 .072-.024q.216 0 .288.144t.168.288q.12.144.432.144"})),J||(J=AV.createElement("path",{fill:"#FFCA00",d:"M110.944 39.179a.5.5 0 0 0 .693-.141l2.483-3.753a.5.5 0 0 0-.834-.552l-2.207 3.336-3.336-2.207a.5.5 0 0 0-.552.834zm-36.89-21.88.28.415c7.923-5.333 16.044-4.663 22.678-.375 6.66 4.304 11.834 12.27 13.718 21.523l.49-.1.49-.1c-1.929-9.473-7.235-17.691-14.155-22.163-6.944-4.489-15.5-5.187-23.78.386z"})),H||(H=AV.createElement("path",{fill:"#fff",d:"M96.35 120.32h-3.192l-1.176-3.072h-7.344l-1.176 3.072H80.27l6.288-16.008h3.504zm-5.16-5.544-2.88-7.632-2.88 7.632zm9.437 5.544h-2.52v-16.008h2.52zm8.053 4.704c-1.968 0-3.432-.432-4.848-1.728l1.176-1.824c.936 1.08 2.136 1.488 3.672 1.488 1.656 0 3.432-.768 3.432-3.144v-1.392c-.888 1.152-2.208 1.92-3.72 1.92-2.952 0-5.136-2.136-5.136-5.952 0-3.744 2.16-5.952 5.136-5.952 1.464 0 2.784.672 3.72 1.896v-1.608h2.52v10.992c0 4.176-3.096 5.304-5.952 5.304m.456-6.912c1.176 0 2.424-.696 2.976-1.56v-4.32c-.552-.864-1.8-1.56-2.976-1.56-1.992 0-3.264 1.488-3.264 3.72s1.272 3.72 3.264 3.72m14.194 2.496c-3.48 0-6.072-2.424-6.072-6.096 0-3.36 2.448-6.072 5.88-6.072 3.48 0 5.712 2.664 5.712 6.312v.6h-8.952c.192 1.752 1.512 3.192 3.696 3.192 1.128 0 2.472-.456 3.288-1.272l1.152 1.656c-1.152 1.104-2.856 1.68-4.704 1.68m3.072-7.032c-.048-1.368-.984-3.072-3.264-3.072-2.16 0-3.144 1.656-3.264 3.072zm7.535 6.744h-2.52v-11.592h2.52v1.704c.864-1.104 2.256-1.968 3.768-1.968v2.496a3.7 3.7 0 0 0-.792-.072c-1.056 0-2.472.72-2.976 1.536zm7.128-13.008c-.84 0-1.56-.696-1.56-1.56s.72-1.56 1.56-1.56c.864 0 1.56.696 1.56 1.56s-.696 1.56-1.56 1.56m1.272 13.008h-2.52v-11.592h2.52zm12.949 0h-2.52v-1.248c-.864.984-2.208 1.536-3.744 1.536-1.896 0-4.032-1.272-4.032-3.84 0-2.664 2.136-3.768 4.032-3.768 1.56 0 2.904.504 3.744 1.488v-1.728c0-1.344-1.104-2.16-2.712-2.16-1.296 0-2.424.48-3.432 1.464l-1.032-1.752c1.344-1.272 3.024-1.872 4.872-1.872 2.568 0 4.824 1.08 4.824 4.2zm-5.28-1.44c1.104 0 2.184-.408 2.76-1.2v-1.752c-.576-.792-1.656-1.2-2.76-1.2-1.416 0-2.472.816-2.472 2.088 0 1.248 1.056 2.064 2.472 2.064"})),N||(N=AV.createElement("path",{fill:"#FFCA00",d:"m137.627 79.479-14.23 14.23-14.229-14.23c-7.859-7.86-7.859-20.6 0-28.46s20.6-7.859 28.459 0c7.859 7.86 7.859 20.6 0 28.46m-14.23-9.758a4.472 4.472 0 1 0 0-8.944 4.472 4.472 0 0 0 0 8.944"})));function Ay(){return(Ay=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let AY=A=>AV.createElement("svg",Ay({xmlns:"http://www.w3.org/2000/svg",width:167,height:44,fill:"none"},A),AV.createElement("g",{clipPath:"url(#Leap_svg__a)"},AV.createElement("mask",{id:"Leap_svg__b",width:167,height:44,x:0,y:0,maskUnits:"userSpaceOnUse",style:{maskType:"luminance"}},y||(y=AV.createElement("path",{fill:"#fff",d:"M167 0H0v44h167z"}))),AV.createElement("g",{mask:"url(#Leap_svg__b)"},Y||(Y=AV.createElement("path",{fill:"#fff",d:"m6.38 8.219-4.764 4.504V42H2.93L.813 44h4.609l8.677-8.204H9.49l2.116-2.002h-.088V8.22z"})),F||(F=AV.createElement("path",{fill:"#fff",d:"m25.397 41.998 3.426-3.236v-4.967H16.216l-8.677 8.203zM0 7.275v4.507l4.764-4.507zM67.715 42.226v-8.06h-19.43v-5.423h17.36v-7.474h-11.94l7.792-7.366h8.272V5.842l-2.315 2.186h-29.04V36.41l6.15 5.814h23.149zM48.285 16.09h10.39l-5.477 5.178h-4.912zM117.419 27.977h-5.545l-8.925-19.961h-9.544l-12.07 26.99v7.03h7.213l5.571-13.27h-.052l4.034-9.603.026.062.027-.062 3.654 8.814h5.545l3.314 7.996h-5.545l2.514 6.063h7.854v-5.97l-.043-.093h5.549z"})),AV.createElement("mask",{id:"Leap_svg__c",width:167,height:44,x:0,y:0,maskUnits:"userSpaceOnUse",style:{maskType:"luminance"}},k||(k=AV.createElement("path",{fill:"#fff",d:"M167 0H0v44h167z"}))),Q||(Q=AV.createElement("g",{mask:"url(#Leap_svg__c)"},AV.createElement("path",{fill:"#fff",d:"M160.705 25.053c.467-.92.772-1.942.956-3.04h3.277c.118-.707.193-1.44.193-2.22v-.096c0-.653-.059-1.274-.144-1.88h-3.278c-.159-1.124-.448-2.177-.902-3.137a10.3 10.3 0 0 0-2.985-3.74q-1.941-1.52-4.696-2.317t-6.125-.797h-9.268l-6.045 5.716v4.274h3.276v4.197h-3.276v19.593h3.777l6.125-5.791v-3.86h4.899q3.317 0 6.15-.772 2.833-.773 4.875-2.316a10.9 10.9 0 0 0 3.191-3.812m-9.124-3.04a3.9 3.9 0 0 1-1.134 1.419q-1.507 1.189-4.161 1.188h-4.696v-2.607h3.277v-4.197h-3.277v-2.171h4.645q2.704-.002 4.212 1.14c.38.289.665.642.902 1.031h3.277c.387.636.604 1.39.604 2.291v.096q0 1.01-.371 1.81z"}))))),O||(O=AV.createElement("defs",null,AV.createElement("clipPath",{id:"Leap_svg__a"},AV.createElement("path",{fill:"#fff",d:"M0 0h167v44H0z"})))));function AF(){return(AF=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let Ak=A=>AV.createElement("svg",AF({xmlns:"http://www.w3.org/2000/svg",width:151,height:137,fill:"none"},A),P||(P=AV.createElement("mask",{id:"mapSaudiMobile_svg__a",fill:"#fff"},AV.createElement("path",{d:"m123.425 86.254.413.688.269.306.283.122.339.05.319-.003.294-.1.383-.31.012.052.289-.***************.***************-.05.006.047-.008.056-.021.046-.037.018-.**************.**************-.**************.099-.035.076-.059.155-.274.079-.08.073-.05.045-.07-.003-.***************.*************.***************.**************.055v.046l-.091.043-.116.037-.099.054-.042.1h-.022l-.129.142-.027.051-.029.082-.025.042-.334.25-.053.072-.014.03-.073.101-.027.055-.011.062-.012.138-.015.06-.004.037.006.041-.002.035-.022.015-.06-.004-.027.005-.011.019-.003.134.005.07.02.029.354.032.129-.032.068-.044.048-.042.052-.03.085-.014.374.079.102.078.165.187.243.075.034.239-.002.16-.01.398.03.162.086.146.321.42.293.385.294.384.294.384.294.384.294.384.294.384.294.384.293.384.294.382.294.383.294.382.294.382.294.382.293.383.294.382.294.382.303.392.112.1.14.045.907.122.785.106.786.105.785.106.785.105.786.106.785.106.786.105.785.106.785.105.786.106.785.106.785.105.784.106.785.105.786.106.785.105.773.104.085-.014.375-.492.36.603.455.766.457.765.455.764.457.765.3.502.058.133.003.14-.21.69-.029.091-.038.12-.106.34-.163.53-.21.686-.25.81-.28.905-.297.965-.073.235-.236.761-.308.995-.297.963-.278.9-.25.804-.211.681-.161.524-.105.337-.038.118-.182.583-.849.302-.966.344-.967.344-.965.344-.967.344-.967.344-.966.344-.965.344-.967.342-.965.343-.967.342-.965.342-.966.342-.967.342-.965.343-.965.342-.967.342-.627.222-.891.128-.905.129-.905.129-.905.129-.905.13-.903.129-.904.129-.905.129-.903.13-.905.129-.905.129-.905.129-.905.129-.904.13-.905.129-.905.129-.905.129-.729.104-.524.193-.252.131-.811.419-.969.5-1.081.559-.97.501-.724.373-.127.089-.437.555-.513.645-.893 1.127-.841 1.061-.55.692-.096.183-.266.741-.378 1.049-.165.224-.005.007-.644.379-.693.407-.167.068-.183.025-.744-.033-.08-.003-.07-.046-.477-.668-.417-.586-.365-.511-.209-.122-1.255.174-.932.13-1.37-.142-1.068-.111-1.402-.146-1.197-.124-.257-.085-.918-.539-.321-.066-.949-.007-.14-.002-.696-.005-1.143-.008-.123.028-.354.134-.16.009-.388-.08-.344-.012-.546.058-.434.102-.107.007-.237-.069-.115-.015-.083.06-.083.148-.086.107-.109.051-.146-.022-.216-.112-.096-.011-.122.065-.085.102-.073.117-.089.087-.132.02-.067-.015-.033-.018-.018-.029-.022-.047.015-.119v-.065l-.033-.016-.132.04-.098.005-.223-.02-.21.024-.102-.002-.113-.04-.234-.186-.393-.446-.24-.144-.415-.18-.09-.023-.193-.008-.103-.023-.126.016-.123.047-.497.315-.491.426-.272.337-.017.027.323.108.11.085.059.128-.02.118-.127.064-.129.029-.149.063-.116.095-.034.117.005.136-.032.12-.118.235-.033.122.024.095.043.098.033.131v.129l-.026.113-.09.23-.038.147.021.1.12.231.069.217.065.054.209.059.058.045.038.064.007.076-.023.062-.042.031-.053.024-.05.036-.082.124.013.082.05.091.033.138.002.009-.035.111-.096.019-.226-.069-.142-.028-.072.031-.028.082-.007.126-.023.111-.145.386-.071.102-.122.092-.247.148-.083.029-.22.04-.06.025-.021.077.014.089.008.095-.05.094-.144.102-.198.086-.27.096-.008-.016.002-.213-.04-.062.078-.193-.038-.113-.274-.311-.061-.133-.022-.113.005-.258.066-.253-.002-.108-.102-.025.038-.044-.067-.063-.056-.091-.013-.084.062-.044-.034-.06-.158-.142-.037-.042-.072-.107-.133-.149-.038-.028-.18-.1-.057-.007-.062.033-.192-.27.078-.26-.04-.095v-.291l-.025-.102-.055-.035-.054-.027-.024-.078-.045-.053-.103-.009-.117.007-.08-.007-.068-.062-.057-.095-.07-.165-.026-.088-.029-.203-.027-.082-.05-.046-.255-.113.015.102.042.095.1.167-.115.073.056.388-.061.109-.033-.124-.004-.406-.023-.122-.076-.231-.098-.804-.037-.142-.025-.106.033-.297-.01-.125-.041-.133-.036-.055-.057-.022-.058-.003-.043-.013-.03-.029-.01-.053-.035-.067-.607-.528-.051-.016-.016.032-.038-.051-.06-.123-.113-.159-.032-.1-.024-.045-.042-.02-.128.027-.059-.015-.025-.074-.029-.051-.245-.257-.01.013-.037.016-.043.013h-.031l-.055-.055-.174-.249-.058-.031-.334-.317-.121.051-.212-.149-.13.02-.052-.157-.1-.1-.121-.083-.112-.111-.238-.306-.036-.1.009-.075.016-.066-.009-.047-.073-.018-.041-.018-.01-.044.004-.053-.009-.045-.237-.215-.088-.124.051-.113-.098-.124-.04-.065-.016-.078-.025-.035-.05-.018-.03-.036.025-.093-.114-.013-.109-.029.017-.026.009-.02.012-.016.031-.018-.034-.071-.051-.035-.064-.002-.078.026.037-.057.027-.058.005-.053-.03-.042.034-.08-.033-.04-.072-.011-.087.009.018-.081-.018-.115-.042-.113-.053-.078-.025-.071-.034-.226-.017-.049-.013-.051-.008-.32-.013-.035-.016-.025-.026-.017-.042-.005-.029-.022-.01-.051-.004-.057-.011-.038-.205-.142-.03-.038-.01-.062-.027-.075-.044-.061-.087-.04-.032-.03-.026-.029-.009-.012-.034.012-.058.062-.022.011-.062-.024-.061-.038-.058-.049-.046-.055-.15-.338-.064-.093-.072-.078-.037-.069-.005-.082.016-.117.134-.393.02-.122.017-.047.029-.022.007-.035-.054-.085-.024-.006-.125-.067-.006-.009-.23-.058-.058-.188-.004-.035-.108-.145-.026-.02-.003-.062.009-.064.034-.118.027-.066.028-.045.02-.047.012-.11.027-.123.073-.146-.05-.089-.103-.069-.112-.048-.243-.071-.1-.071-.058-.165-.08-.128-.021-.056.003-.068.042-.138-.004-.058-.032-.109-.01-.06-.023-.053-.103-.057-.024-.052-.025-.128-.062-.065-.087-.048-.152-.124.01-.018.008-.073-.013-.06-.054-.092-.013-.051-.01-.064-.03-.097.004-.067.038-.06.052-.047.028-.044-.04-.049.014-.064-.013-.064-.03-.051-.013-.071-.483-.173-.134-.072-.286-.299-.136-.074v-.039l.061.009.13.073.035-.246-.014-.302-.04-.131-.26-.378-.11-.075-.29-.035-.113.004-.05.058.065.151-.085-.071-.073-.125-.047-.12-.006-.053-.081-.051-.23-.339-.175-.109-.036-.038.005-.044.071-.158-.034-.108-.067-.063-.173-.075-.212-.131-.05-.018-.053-.082-.05-.018-.05-.029-.14-.201-.045-.023-.14-.035-.049-.005-.103-.028-.114-.067-.258-.209-.201-.232.038-.087-.096-.065-.149-.042-.174-.028-.033-.031-.052-.078-.09-.08-.05-.058.005-.028-.058-.036-.18-.167-.09-.044-.127.002-.14.023-.112.042-.047.051-.134-.051-.242-.147-.125-.053.008.076.032.042.046.029.047.041.027.012.054.039.035.038-.036.018-.099-.027-.55-.341-.08-.069-.075-.036-.113-.033-.103-.049-.087-.184-.098-.095-.303-.238-.152-.153-.336-.548-.11-.095v-.045l.056.003.045-.007.033-.027.02-.051-.087-.016-.073-.042-.13-.128-.037-.067-.032-.177-.026-.067-.188-.215-.017-.1.091-.142-.196-.067-.078-.013.015.047.025.035.034.024.04.018-.11-.037-.124-.071-.074-.091.04-.094-.053-.026-.023-.036.005-.046.033-.053.025.048.044.034.058.024.067.016-.027-.072-.01-.033-.003-.038-.01-.059-.028-.001-.038.01-.04-.016-.076-.109-.031-.002-.01.113-.119-.078-.161-.131-.116-.151.009-.139.023.031.044.038.04.013.012-.042-.02-.071-.03-.027-.042-.015-.045-.031-.055-.027-.058.011-.06.024-.054.012-.002-.023-.16-.246-.036-.087-.016-.102-.004-.146-.018-.055-.078-.052-.016-.057-.008-.073-.021-.025-.036-.013-.238-.169-.062-.071-.036-.168-.053-.118-.012-.071.012-.031.055-.076.01-.038-.04-.088-.212-.331-.078-.047-.175-.284.446-.395.063-.148.008-.195-.044-.176-.085-.093v-.033l.06-.053-.01-.082-.09-.202-.073.13-.058.016-.052-.069-.051-.124.042-.038-.093-.135-.02-.178.01-.19-.012-.167-.06-.166.011-.091.107-.04.031-.034.03-.082.003-.095-.044-.074-.043.127-.03.053-.043.029-.08.003-.018-.029.002-.047-.02-.051-.036-.033-.026-.018-.023-.024-.03-.049-.016-.056-.009-.102-.016-.051-.01-.004-.066-.038-.01-.02-.017-.045-.011-.02-.192-.244-.164-.366-.103-.514.007-.116.149-.057.152.197.111.002.023-.135v-.118l.015-.066.024-.023.076-.055.016-.031.011-.116.053-.242.012-.124h-.034l-.08.209-.038-.089.02-.067.038-.066.02-.089v-.251l.042-.166.185-.217.058-.26.04-.06.05-.051.051-.064.078-.171.096-.1.018-.025v-.077l-.03-.071-.055-.04-.072.016-.038.061-.04.174-.037.064-.138.027-.005-.174.073-.257-.044-.335.042-.342-.042-.168-.043-.082-.156-.216-.147-.344-.111-.184-.042-.097.015-.074.149-.139-.01-.047-.27-.304-.11-.09-.05.028.02.035.1.105.038.055.019.066.005.061.014.062.039.062-.042.042-.075-.173-.157-.246-.154-.173-.293-.531.002-.084.165.091.184.41.1.032.061.039.054-.01.044-.043.033-.07-.058-.07-.097-.186-.058-.038-.041-.018-.046-.046-.034-.062-.015-.067-.049-.098-.112-.048-.129-.03-.094-.053-.114.023-.084-.098-.116-.262.063-.062.048-.082.032-.09.011-.085-.014-.176-.027-.095-.006-.076-.065-.031-.051.094-.07-.134-.099-.135-.103-.206-.1-.12-.042-.136-.069-.095-.016-.084-.049-.105.002-.09-.042-.183-.076-.146-.072-.178-.033-.243-.022-.172-.032-.135-.013-.111-.094-.102-.042-.07-.018-.101-.008-.1-.125.07-.056-.036h-.045l.005.104.053.054.141.053v.042l-.188-.018-.124-.117-.005-.3-.25-.326-.133-.424-.04-.14-.025-.153-.042-.144-.089-.11-.056-.03-.062-.013-.094-.032-.053-.062-.025-.09-.045-.109-.07-.089-.092-.029-.09-.025-.077-.044-.054-.044-.055-.104-.092-.109-.045-.038-.093-.067-.116-.037-.092-.012-.135-.09-.072-.054-.11-.106-.108-.136-.116-.09-.096-.089-.169-.164-.045-.023-.046-.013-.05.024-.047.03-.127-.087-.09-.07-.231-.15-.176-.137.032-.074.098-.07.14-.021-.056-.057-.084.028-.107.054-.041-.08.065-.105.023-.099-.06-.023-.038.045-.069.013-.032.06.032.033.019.056.009.078.003.057-.032.036-.125-.047-.173.045-.257-.198-.078-.071-.22-.175-.05-.125-.075-.051-.096-.048-.102-.056-.083-.08-.11-.075-.135.004-.063-.007-.086.063-.018.082.033.126-.241.009-.06-.093-.167-.08-.13.042-.126-.131-.078-.14-.129-.186-.2-.173-.064-.21.056-.125.041.015.111.074.047-.098-.036-.117-.776-1.14-.349-.485-.165-.22-.009-.173.02-.046.006-.062-.006-.062-.018-.043-.041-.02-.073.073-.06.009-.1-.06-.072-.1-.096-.223.143.004.127.02.127.004.143-.05.07-.105.01-.315.123-.264-.016-.14-.109-.262-.036-.15.036-.367-.01-.142-.03-.133-.049-.124-.065-.115-.105-.129-.066-.062-.06-.043-.052-.05-.053-.116-.047-.05-.112-.087-.113-.123-.098-.142-.067-.14-.012-.062-.013-.14-.015-.055-.038-.058-.085-.095-.027-.062.1-.07-.084-.143-.25-.257-.044-.08-.09-.267-.03-.044-.077-.05-.03-.034-.007-.036.006-.078-.017-.037-.048-.076-.08-.182-.044-.07-.072-.054-.166-.067-.072-.046-.221-.358-.14-.177-.123-.01-.011.072.038.073.02.062-.065.038-.073-.022-.172-.124-.086-.029-.05-.035-.127-.163-.037-.06-.007-.039.007-.134-.012-.037-.067-.016-.035-.031-.069-.158-.007-.148.054-.14.118-.138.075-.13.02-.172-.022-.179-.055-.144-.134-.144-.18-.094-.197-.051-.187-.015-.203-.074-.134-.18-.36-.858-.128-.22-.245-.333-.417-1.036-.446-.806-.269-.346-.326-.295-.15-.173-.09-.275-.17-.3-.113-.395-.101.029-.078-.071-.133-.217-.366-.33-.452-.59-.161-.316-.018-.05-.008-.064.013-.053.055-.091.012-.055-.027-.076-.134-.206-.475-.57-.267-.185-.069-.062-.134-.168-.152-.251-.171-.171-.216-.4-.062-.164-.02-.13-.003-.23-.033-.121-.047-.084-.187-.198-.103-.171-.145-.392-.116-.163-.053-.037-.114-.045-.044-.03-.03-.045-.026-.087-.056-.12-.026-.183-.054-.09-.292-.33-.089-.16-.054-.062-.09-.042-.273-.218v.043h-.038l-.062-.085.073-.066.127-.056.098-.06.06.029-.006-.037-.054-.1-.035-.014-.156-.035-.045-.02h-.038v.044h-.035l-.023-.04-.033-.044-.04-.035-.043-.014-.1.004-.05-.006-.066-.069-.263-.191-.056.042-.066-.004h-.06l.017.037.022.03.025.026-.056-.03-.064-.043-.065-.024-.067.031-.058.039-.17.05-.048.026-.032.035-.02.047h-.038v-.044h-.039l-.074.02-.114-.022-.116.002-.078.091-.015-.032h-.005l-.024-.015v-.044h.044v-.047l-.022-.044.029-.036.052-.027.053-.019-.054-.023-.055-.008-.058.01-.063.021v.044h.041v.044l-.063.022-.042.043-.052.115-.018-.027-.019-.01-.02-.003-.02-.01.022-.037.033-.037.044-.033.054-.023-.004-.026-.002-.027.004-.036-.08-.004-.063.014-.042.04-.01.077-.032-.018-.009-.002-.009.007-.029.013-.002-.02.004-.042v-.02h-.038v.044h-.045l-.03-.055-.025-.027-.027.004-.033.036-.052-.04-.013-.018-.035-.051-.02-.03-.034-.08-.016.017-.022.016-.022.022-.02.033-.074-.057-.02.03-.004.063-.018.051-.043.026-.077.032-.036.03v.04l.12.092v.048l-.036.002-.017.01-.009.015-.012.013-.046-.04-.027.107-.005.082-.037.055-.123.02.009-.031.005-.006.011-.005.018-.015-.041-.043-.026-.093-.01-.084-.004-.155-.042-.047-.138.058.026-.076-.035-.042.084-.122.054-.046.05-.023.003-.033.034-.018.047-.046.033-.02-.035-.043.017-.035.022-.025.03-.019.044-.01v.028l-.013.006-.016-.002-.01.006.055.029.047.016.035-.018.018-.067h-.033v-.044l-.04-.04.01-.042.043-.035.062-.016v-.044l-.02-.056.054-.295.022.013.04.016.02.015-.004-.02.004-.068-.033.022-.014.015-.033-.037.114-.11.115-.214.087-.233.06-.302.11-.167.042-.193.04-.11.161-.282-.043-.29-.017-.305-.085-.291.103-.397.073-.207.087-.172v-.04h-.033l.097-.167.014-.078-.033-.071v-.04l.064-.199-.013-.085-.094-.03v-.04l.083-.142.091-.267.042-.275-.064-.162v-.09l.066-.12.054-.14.112-.58.026-.084.063-.145-.009-.04.01-.093.054-.055.13-.075-.038-.109-.014-.142.021-.095.066.037.007-.02v-.018l.007-.01.027.004-.043-.096-.015-.11.024-.096.626.111.67.117.879.153.785.136.838.146.668.116.32.056.652.113.587.102.155-.005.145-.06.613-.508.596-.497.657-.546.435-.362.363-.617.352-.601.26-.442.311-.534.135-.145.156-.079.49-.102.502-.103.812-.172.806-.169.756-.158.666-.139.121-.109.25-.615.187-.457.203-.497.166-.408.074-.118.13-.11.613-.347.682-.386.414-.233.038-.023.006-.002.001-.002-.001-.004-.28-.33-.618-.73-.62-.733-.62-.733-.62-.736-.017-.021-.019-.022-.018-.022-.018-.02-.734-.801-.724-.788-.011-.013-.735-.803-.734-.803.216-.065 1.182-.355 1.399-.42 1.398-.423.332-.1 1.067-.322.203-.06 1.066-.31 1.27-.37 1.268-.369 1.27-.37.641-.187v-.003l.196-.084.671-.64.11-.085.057-.018.017-.005.016-.006.016-.005.01-.006 1.117.193 1.115.193 1.115.193 1.116.193.453.078h.004l1.924.374.305.12.314.186.58.349.496.298.495.297.495.299.495.296.495.297.495.297.496.297.495.296.495.297.495.297.495.296.495.297.496.297.495.297.495.296.495.297.504.3.374.328.477.417.477.415.477.415.477.415.535.468.535.468.535.467.11.097.425.371.597.49.497.408.497.407.497.408.497.408.646.53.647.527.648.528.645.528.274.224.374.304.648.526.645.526.646.526.539.437.538.437.539.437.537.437.433.351.037.015.036.014.034.015.037.014.517.048.45.041.45.04.45.042.449.04.579.053.578.053.579.053.578.053.579.052.579.053.578.053.579.053.43.04.43.038.43.04.43.038.594.055.247-.053.147-.03.1-.023.246-.052.247-.053.631.078.629.078.63.079.631.078.562.075.562.074.563.075.562.074.316.042h.003l.069.119.272.467.314.863.025.164.022.348.055.196.076.191.085.146.12.111.129.091.105.111.047.171.303-.003 1.003-.011 1.003-.011 1.003-.011 1.003-.011.132.142.071.05.147.068.022.048.051.282.001.215-.049.143-.128-.047-.028.042-.007.018-.007.027.111.008.094-.024.071-.06.038-.1.065.044-.004.058-.061.118-.035.168-.003.076.029.151.072.13.472.524.029.076-.082.124-.042.12-.005.126.031.136.056.102.18.248.038.067-.009.075-.008.023.089.055.192.087.062.008.192-.008.049.015.113.062.067.014.024.017-.004.04-.018.043-.022.03-.036.016-.038-.002-.042-.015-.096.05-.026.041.008.09.054.085.113.117.128.103.106.044.003.03.127.19.227.228.027.066-.051.129-.007.082.005.074.019.033-.013.045-.051.102-.072.106-.069.064.04-.075.009-.054-.013-.157.016-.09.033-.05.018-.053-.027-.093-.084.09-.092.238-.094.065.103-.276.013-.073-.028-.122-.045.009-.047.087-.107.45-.004.104-.012.016-.02.042.007.035.063-.006.031-.03.04-.108.042-.04v.285l.022.068.052-.01.053-.054.029-.067.04.07.007.086-.011.108.071.027.054-.03.042-.063.029-.064.002.112-.013.038-.029.029.027.058-.005.038-.036.022-.062.006.058.14.02.038-.138-.07-.056.003-.04.069.118.05.101-.005.08-.06.051-.114h.038l.038.09-.069.072-.023.125.03.104.1.004.042-.048-.025-.045-.044-.044-.011-.044.044-.058.06-.033.13-.03.111-.011.074.036.201.188.209.107.054.076-.108.079.079.038.073-.01.034-.045-.03-.073.094.044.098.027.214.017.047-.015.1-.06.045-.013.06.008.112.032.062.008.114.034.087.082.129.186.042.036.04.022.038.027.036.05.015.049.011.114.016.051-.071.004-.041-.03-.017-.061.011-.084h-.038l-.118.144-.31-.033-.111.108h-.041l-.039-.11-.097-.001-.215.11.048.028.123.036.063.03.051.038.033.033.04.03.069.03.036.049.029.022.031-.011.096-.097.002-.013.031-.027.012-.036.017-.02.038.022.043.038.053.025-.04.09-.054.043-.057.033-.043.055-.017.07-.003.166-.015.073.06-.016.066-.035.065-.047.056-.053.064-.024.076.022.116.067-.046.124.017.122.041.124.022.13-.011.272.031.115.096-.013-.007.117.038.043.06-.014.062-.058-.029-.095.05-.062.088-.005.081.074.035-.047.029.07-.017.065-.031.067-.018.084-.02.049-.049.02-.063.002-.062-.01.046.073.136.028.045.076.056-.034.036-.053.066-.13.005.03.006.003.009-.002.02.01-.057.112.048.019.143-.046.089.016.152.068.091.007-.153-.167-.047-.1.086-.084.07-.004.055.035.049.042.056.016.043-.02.109-.111-.02.102-.052.155-.006.092.035.146.031.08.03.035.106.049.103.12.326.515.166.208.048.038.16.07.053.043.083.087.058.046.35.162.22.162.125.014.13-.007.142.02.174.102.163.162.283.388.096.173.412.3.093.104.02.162-.256-.23-.136-.087-.309-.038-.079-.028-.055-.07-.043-.234-.049.04-.051.108-.027.104-.006.08.002.076.018.07.04.06.056.06.015.032-.024.388.009.105.153.483.063.102.093.09.103.065.484.169.165.15.1.214.042.287-.004.689-.021.149-.059.126-.11.07-.004-.096-.043-.033-.06.022-.051.07.036.158-.014.243-.046.248-.05.171-.087-.065-.026-.09.002-.1-.009-.093-.04-.076-.051-.05-.052-.04-.046-.05-.031-.024h-.025l-.018-.014-.011-.175-.011-.042-.022-.05-.043-.061-.069-.07-.074-.041-.064.022-.147.287-.045.04-.062.04.029.086.06.082.033.03.012.047.053.09.009.06-.018.017-.04.015-.04.025-.018.047.013.091.021.09.006.09-.04.099.094.033.08-.05.04-.094-.024-.104.055.016.038.033.065.08.031.015.024-.006.016.006.005.049v.038l.008.02.016.007h.034l.144.035.058.086.029.112.061.113v.044l-.112.118.06.295.132.326.113.21.339.41.143.24.058.298h-.036l-.098-.299-.04-.047-.069-.016-.083-.039-.071-.049-.071-.105-.089-.071-.081-.01-.018.123.041.087.145.105.046.064.029-.064.009-.027h.038l.214.453.644.836.149.131-.017-.084-.038-.098-.016-.082.049-.035.06-.029.025.015.024.138.029.035.027.018.013.015.045.087.105.05.192.057.049.062.109.282-.041.044.068.097.077.165.063.18.027.139-.007.078-.029.138-.007.082.012.08.026.057.025.042.031.107.082.151.014.097-.034.256.045.255.049.144.06.075.043-.12.147.121.155.195.063.102.094.095.113.413.141.095.113.107.049.238.012.383.024.073.049.109.06.103.06.059.083-.002zm-48.666 48.757v.084l-.016.105-.038.129.014.099.05.174-.055.119.002.009-.13-.009-.157-.191-.129-.048-.121-.009-.06-.054.16-.068.09-.074-.036-.122-.144-.077-.143.022-.167.015-.114.029-.107.06-.09.053-.113-.039-.127-.122-.09-.083-.13-.031-.15-.108-.136-.107-.067-.122-.036-.182-.098-.069h-.098l-.067-.017.014-.04-.007-.127-.084-.107-.052-.115.009-.106.083.024.031.045.067.055.22.1.12.138.06.084.021.144-.004.069.102.115.098.084.038.023.205-.022.067.108.114.009.115-.036.097-.008.068.069v.07h.069l.166-.075-.16-.031-.083-.131.07-.107.041-.17.036-.058.082-.013.036.015.046.018.155.097.008.098.074.122-.01.063.139.022.036.115zm-1.51-.506-.084-.069-.084-.108-.143-.184-.12-.167-.045-.067.063-.057.136-.007.1-.031.073-.062.056-.104.058.006.01.002.032-.119-.044-.142-.076-.034-.076-.035-.073-.005-.127-.028-.069-.005-.014-.026.043-.047.053-.022.015.051.183.018.143.059.096.112.006.024.034.157.002.113-.03.129.02.107.182.047.053.13-.232.003-.067.088.032.1.138.049.078.047.107.048.036.143v.07l-.165-.1-.205-.062zm43.164-64.681.11.047.302.088.284.082-.103.04-.087-.02-.084-.04-.094-.022-.092.013-.087.036-.08.055-.069.067-.046-.05-.005.004-.025.006-.046-.033-.031-.014h-.001v-.04l.076-.033.014-.035-.023-.04-.024-.025-.022-.022-.058-.028-.061.01-.06.023-.053.013-.072.03-.071.074-.062.1-.043.104-.015-.166.105-.164.164-.118.168-.031.064.01.049.024zm-70.778 12.22.06.049.027.027-.013.006-.025-.011-.044-.008-.005.026.024.04-.046-.007-.11-.073-.03-.07.01-.041-.055-.006-.069.02-.038-.052-.027-.086h-.05l-.062.044-.013-.044.042-.118.054-.064h.069l.078.01.067.043.054.122.037-.006.012-.045.024.03zm-.479.007.09-.004.019.03-.103.056-.044.035-.06.067-.056.115-.085.061-.12-.08-.107-.12-.103-.082-.227-.244-.015-.016-.02-.037v-.023l.011.002.011-.013-.01-.036-.055-.062-.065-.093.001-.046.06.015.055.047.103.13.049.038.103.056.198.088.094.025.071.007.053.031.025.046-.118.005-.018.08.045.075.068-.018.069-.077zm-.524-.719-.054-.018-.073-.071.01-.09.07-.054.081.01.062.068-.004.073-.06.058zm-.334.22-.054-.04-.027-.007-.026-.01-.03-.045-.004-.063-.051-.019-.08-.014-.014-.022.023.007-.005-.027-.05-.067-.084-.057-.102.006-.045.027-.026.024-.043.014.002-.058.056-.086.087-.025.105.038.28.188.03.047.03.073.084.052.09.033.126.079.2.167.049.056-.01.022-.021.02.007.028-.03-.017-.088-.069-.076-.047-.058-.015-.023-.04-.03-.069-.052-.025-.036.016-.051-.016zm.624-.322-.007-.178-.007-.04v-.071l.036-.05.054-.021.05.023.026.062.004.058-.014.046-.026.002-.034-.028-.008.011.015.046-.004.022-.012-.008.038.088.04.167-.026.057-.063-.099-.036-.025zm-1.228-.146-.007.039-.033.072-.047.031-.038-.02-.074-.054-.452-.233-.121-.03-.038.088-.057-.03-.107-.103-.072-.085-.037-.133.048-.11.14-.145.06-.078.048-.03.01.04-.034.073-.***************.************.***************.**************.***************.***************.047-.***************.**************.024.049-.006.009-.005-.018-.008.003-.027.026-.025.047.007zm-.783-.999-.01-.05-.014-.134.015-.26.09-.217.144-.134.04-.017-.077.075-.061.098-.047.108-.**************.***************.04.005-.004.042-.05.084-.***************.***************.065-.014.02.01-.031.053-.056.03-.071-.013-.112-.091-.064-.04z"}))),K||(K=AV.createElement("path",{stroke:"#E5F0FC",strokeWidth:2,d:"m123.425 86.254.413.688.269.306.283.122.339.05.319-.003.294-.1.383-.31.012.052.289-.***************.***************-.05.006.047-.008.056-.021.046-.037.018-.**************.**************-.**************.099-.035.076-.059.155-.274.079-.08.073-.05.045-.07-.003-.***************.*************.***************.**************.055v.046l-.091.043-.116.037-.099.054-.042.1h-.022l-.129.142-.027.051-.029.082-.025.042-.334.25-.053.072-.014.03-.073.101-.027.055-.011.062-.012.138-.015.06-.004.037.006.041-.002.035-.022.015-.06-.004-.027.005-.011.019-.003.134.005.07.02.029.354.032.129-.032.068-.044.048-.042.052-.03.085-.014.374.079.102.078.165.187.243.075.034.239-.002.16-.01.398.03.162.086.146.321.42.293.385.294.384.294.384.294.384.294.384.294.384.294.384.293.384.294.382.294.383.294.382.294.382.294.382.293.383.294.382.294.382.303.392.112.1.14.045.907.122.785.106.786.105.785.106.785.105.786.106.785.106.786.105.785.106.785.105.786.106.785.106.785.105.784.106.785.105.786.106.785.105.773.104.085-.014.375-.492.36.603.455.766.457.765.455.764.457.765.3.502.058.133.003.14-.21.69-.029.091-.038.12-.106.34-.163.53-.21.686-.25.81-.28.905-.297.965-.073.235-.236.761-.308.995-.297.963-.278.9-.25.804-.211.681-.161.524-.105.337-.038.118-.182.583-.849.302-.966.344-.967.344-.965.344-.967.344-.967.344-.966.344-.965.344-.967.342-.965.343-.967.342-.965.342-.966.342-.967.342-.965.343-.965.342-.967.342-.627.222-.891.128-.905.129-.905.129-.905.129-.905.13-.903.129-.904.129-.905.129-.903.13-.905.129-.905.129-.905.129-.905.129-.904.13-.905.129-.905.129-.905.129-.729.104-.524.193-.252.131-.811.419-.969.5-1.081.559-.97.501-.724.373-.127.089-.437.555-.513.645-.893 1.127-.841 1.061-.55.692-.096.183-.266.741-.378 1.049-.165.224-.005.007-.644.379-.693.407-.167.068-.183.025-.744-.033-.08-.003-.07-.046-.477-.668-.417-.586-.365-.511-.209-.122-1.255.174-.932.13-1.37-.142-1.068-.111-1.402-.146-1.197-.124-.257-.085-.918-.539-.321-.066-.949-.007-.14-.002-.696-.005-1.143-.008-.123.028-.354.134-.16.009-.388-.08-.344-.012-.546.058-.434.102-.107.007-.237-.069-.115-.015-.083.06-.083.148-.086.107-.109.051-.146-.022-.216-.112-.096-.011-.122.065-.085.102-.073.117-.089.087-.132.02-.067-.015-.033-.018-.018-.029-.022-.047.015-.119v-.065l-.033-.016-.132.04-.098.005-.223-.02-.21.024-.102-.002-.113-.04-.234-.186-.393-.446-.24-.144-.415-.18-.09-.023-.193-.008-.103-.023-.126.016-.123.047-.497.315-.491.426-.272.337-.017.027.323.108.11.085.059.128-.02.118-.127.064-.129.029-.149.063-.116.095-.034.117.005.136-.032.12-.118.235-.033.122.024.095.043.098.033.131v.129l-.026.113-.09.23-.038.147.021.1.12.231.069.217.065.054.209.059.058.045.038.064.007.076-.023.062-.042.031-.053.024-.05.036-.082.124.013.082.05.091.033.138.002.009-.035.111-.096.019-.226-.069-.142-.028-.072.031-.028.082-.007.126-.023.111-.145.386-.071.102-.122.092-.247.148-.083.029-.22.04-.06.025-.021.077.014.089.008.095-.05.094-.144.102-.198.086-.27.096-.008-.016.002-.213-.04-.062.078-.193-.038-.113-.274-.311-.061-.133-.022-.113.005-.258.066-.253-.002-.108-.102-.025.038-.044-.067-.063-.056-.091-.013-.084.062-.044-.034-.06-.158-.142-.037-.042-.072-.107-.133-.149-.038-.028-.18-.1-.057-.007-.062.033-.192-.27.078-.26-.04-.095v-.291l-.025-.102-.055-.035-.054-.027-.024-.078-.045-.053-.103-.009-.117.007-.08-.007-.068-.062-.057-.095-.07-.165-.026-.088-.029-.203-.027-.082-.05-.046-.255-.113.015.102.042.095.1.167-.115.073.056.388-.061.109-.033-.124-.004-.406-.023-.122-.076-.231-.098-.804-.037-.142-.025-.106.033-.297-.01-.125-.041-.133-.036-.055-.057-.022-.058-.003-.043-.013-.03-.029-.01-.053-.035-.067-.607-.528-.051-.016-.016.032-.038-.051-.06-.123-.113-.159-.032-.1-.024-.045-.042-.02-.128.027-.059-.015-.025-.074-.029-.051-.245-.257-.01.013-.037.016-.043.013h-.031l-.055-.055-.174-.249-.058-.031-.334-.317-.121.051-.212-.149-.13.02-.052-.157-.1-.1-.121-.083-.112-.111-.238-.306-.036-.1.009-.075.016-.066-.009-.047-.073-.018-.041-.018-.01-.044.004-.053-.009-.045-.237-.215-.088-.124.051-.113-.098-.124-.04-.065-.016-.078-.025-.035-.05-.018-.03-.036.025-.093-.114-.013-.109-.029.017-.026.009-.02.012-.016.031-.018-.034-.071-.051-.035-.064-.002-.078.026.037-.057.027-.058.005-.053-.03-.042.034-.08-.033-.04-.072-.011-.087.009.018-.081-.018-.115-.042-.113-.053-.078-.025-.071-.034-.226-.017-.049-.013-.051-.008-.32-.013-.035-.016-.025-.026-.017-.042-.005-.029-.022-.01-.051-.004-.057-.011-.038-.205-.142-.03-.038-.01-.062-.027-.075-.044-.061-.087-.04-.032-.03-.026-.029-.009-.012-.034.012-.058.062-.022.011-.062-.024-.061-.038-.058-.049-.046-.055-.15-.338-.064-.093-.072-.078-.037-.069-.005-.082.016-.117.134-.393.02-.122.017-.047.029-.022.007-.035-.054-.085-.024-.006-.125-.067-.006-.009-.23-.058-.058-.188-.004-.035-.108-.145-.026-.02-.003-.062.009-.064.034-.118.027-.066.028-.045.02-.047.012-.11.027-.123.073-.146-.05-.089-.103-.069-.112-.048-.243-.071-.1-.071-.058-.165-.08-.128-.021-.056.003-.068.042-.138-.004-.058-.032-.109-.01-.06-.023-.053-.103-.057-.024-.052-.025-.128-.062-.065-.087-.048-.152-.124.01-.018.008-.073-.013-.06-.054-.092-.013-.051-.01-.064-.03-.097.004-.067.038-.06.052-.047.028-.044-.04-.049.014-.064-.013-.064-.03-.051-.013-.071-.483-.173-.134-.072-.286-.299-.136-.074v-.039l.061.009.13.073.035-.246-.014-.302-.04-.131-.26-.378-.11-.075-.29-.035-.113.004-.05.058.065.151-.085-.071-.073-.125-.047-.12-.006-.053-.081-.051-.23-.339-.175-.109-.036-.038.005-.044.071-.158-.034-.108-.067-.063-.173-.075-.212-.131-.05-.018-.053-.082-.05-.018-.05-.029-.14-.201-.045-.023-.14-.035-.049-.005-.103-.028-.114-.067-.258-.209-.201-.232.038-.087-.096-.065-.149-.042-.174-.028-.033-.031-.052-.078-.09-.08-.05-.058.005-.028-.058-.036-.18-.167-.09-.044-.127.002-.14.023-.112.042-.047.051-.134-.051-.242-.147-.125-.053.008.076.032.042.046.029.047.041.027.012.054.039.035.038-.036.018-.099-.027-.55-.341-.08-.069-.075-.036-.113-.033-.103-.049-.087-.184-.098-.095-.303-.238-.152-.153-.336-.548-.11-.095v-.045l.056.003.045-.007.033-.027.02-.051-.087-.016-.073-.042-.13-.128-.037-.067-.032-.177-.026-.067-.188-.215-.017-.1.091-.142-.196-.067-.078-.013.015.047.025.035.034.024.04.018-.11-.037-.124-.071-.074-.091.04-.094-.053-.026-.023-.036.005-.046.033-.053.025.048.044.034.058.024.067.016-.027-.072-.01-.033-.003-.038-.01-.059-.028-.001-.038.01-.04-.016-.076-.109-.031-.002-.01.113-.119-.078-.161-.131-.116-.151.009-.139.023.031.044.038.04.013.012-.042-.02-.071-.03-.027-.042-.015-.045-.031-.055-.027-.058.011-.06.024-.054.012-.002-.023-.16-.246-.036-.087-.016-.102-.004-.146-.018-.055-.078-.052-.016-.057-.008-.073-.021-.025-.036-.013-.238-.169-.062-.071-.036-.168-.053-.118-.012-.071.012-.031.055-.076.01-.038-.04-.088-.212-.331-.078-.047-.175-.284.446-.395.063-.148.008-.195-.044-.176-.085-.093v-.033l.06-.053-.01-.082-.09-.202-.073.13-.058.016-.052-.069-.051-.124.042-.038-.093-.135-.02-.178.01-.19-.012-.167-.06-.166.011-.091.107-.04.031-.034.03-.082.003-.095-.044-.074-.043.127-.03.053-.043.029-.08.003-.018-.029.002-.047-.02-.051-.036-.033-.026-.018-.023-.024-.03-.049-.016-.056-.009-.102-.016-.051-.01-.004-.066-.038-.01-.02-.017-.045-.011-.02-.192-.244-.164-.366-.103-.514.007-.116.149-.057.152.197.111.002.023-.135v-.118l.015-.066.024-.023.076-.055.016-.031.011-.116.053-.242.012-.124h-.034l-.08.209-.038-.089.02-.067.038-.066.02-.089v-.251l.042-.166.185-.217.058-.26.04-.06.05-.051.051-.064.078-.171.096-.1.018-.025v-.077l-.03-.071-.055-.04-.072.016-.038.061-.04.174-.037.064-.138.027-.005-.174.073-.257-.044-.335.042-.342-.042-.168-.043-.082-.156-.216-.147-.344-.111-.184-.042-.097.015-.074.149-.139-.01-.047-.27-.304-.11-.09-.05.028.02.035.1.105.038.055.019.066.005.061.014.062.039.062-.042.042-.075-.173-.157-.246-.154-.173-.293-.531.002-.084.165.091.184.41.1.032.061.039.054-.01.044-.043.033-.07-.058-.07-.097-.186-.058-.038-.041-.018-.046-.046-.034-.062-.015-.067-.049-.098-.112-.048-.129-.03-.094-.053-.114.023-.084-.098-.116-.262.063-.062.048-.082.032-.09.011-.085-.014-.176-.027-.095-.006-.076-.065-.031-.051.094-.07-.134-.099-.135-.103-.206-.1-.12-.042-.136-.069-.095-.016-.084-.049-.105.002-.09-.042-.183-.076-.146-.072-.178-.033-.243-.022-.172-.032-.135-.013-.111-.094-.102-.042-.07-.018-.101-.008-.1-.125.07-.056-.036h-.045l.005.104.053.054.141.053v.042l-.188-.018-.124-.117-.005-.3-.25-.326-.133-.424-.04-.14-.025-.153-.042-.144-.089-.11-.056-.03-.062-.013-.094-.032-.053-.062-.025-.09-.045-.109-.07-.089-.092-.029-.09-.025-.077-.044-.054-.044-.055-.104-.092-.109-.045-.038-.093-.067-.116-.037-.092-.012-.135-.09-.072-.054-.11-.106-.108-.136-.116-.09-.096-.089-.169-.164-.045-.023-.046-.013-.05.024-.047.03-.127-.087-.09-.07-.231-.15-.176-.137.032-.074.098-.07.14-.021-.056-.057-.084.028-.107.054-.041-.08.065-.105.023-.099-.06-.023-.038.045-.069.013-.032.06.032.033.019.056.009.078.003.057-.032.036-.125-.047-.173.045-.257-.198-.078-.071-.22-.175-.05-.125-.075-.051-.096-.048-.102-.056-.083-.08-.11-.075-.135.004-.063-.007-.086.063-.018.082.033.126-.241.009-.06-.093-.167-.08-.13.042-.126-.131-.078-.14-.129-.186-.2-.173-.064-.21.056-.125.041.015.111.074.047-.098-.036-.117-.776-1.14-.349-.485-.165-.22-.009-.173.02-.046.006-.062-.006-.062-.018-.043-.041-.02-.073.073-.06.009-.1-.06-.072-.1-.096-.223.143.004.127.02.127.004.143-.05.07-.105.01-.315.123-.264-.016-.14-.109-.262-.036-.15.036-.367-.01-.142-.03-.133-.049-.124-.065-.115-.105-.129-.066-.062-.06-.043-.052-.05-.053-.116-.047-.05-.112-.087-.113-.123-.098-.142-.067-.14-.012-.062-.013-.14-.015-.055-.038-.058-.085-.095-.027-.062.1-.07-.084-.143-.25-.257-.044-.08-.09-.267-.03-.044-.077-.05-.03-.034-.007-.036.006-.078-.017-.037-.048-.076-.08-.182-.044-.07-.072-.054-.166-.067-.072-.046-.221-.358-.14-.177-.123-.01-.011.072.038.073.02.062-.065.038-.073-.022-.172-.124-.086-.029-.05-.035-.127-.163-.037-.06-.007-.039.007-.134-.012-.037-.067-.016-.035-.031-.069-.158-.007-.148.054-.14.118-.138.075-.13.02-.172-.022-.179-.055-.144-.134-.144-.18-.094-.197-.051-.187-.015-.203-.074-.134-.18-.36-.858-.128-.22-.245-.333-.417-1.036-.446-.806-.269-.346-.326-.295-.15-.173-.09-.275-.17-.3-.113-.395-.101.029-.078-.071-.133-.217-.366-.33-.452-.59-.161-.316-.018-.05-.008-.064.013-.053.055-.091.012-.055-.027-.076-.134-.206-.475-.57-.267-.185-.069-.062-.134-.168-.152-.251-.171-.171-.216-.4-.062-.164-.02-.13-.003-.23-.033-.121-.047-.084-.187-.198-.103-.171-.145-.392-.116-.163-.053-.037-.114-.045-.044-.03-.03-.045-.026-.087-.056-.12-.026-.183-.054-.09-.292-.33-.089-.16-.054-.062-.09-.042-.273-.218v.043h-.038l-.062-.085.073-.066.127-.056.098-.06.06.029-.006-.037-.054-.1-.035-.014-.156-.035-.045-.02h-.038v.044h-.035l-.023-.04-.033-.044-.04-.035-.043-.014-.1.004-.05-.006-.066-.069-.263-.191-.056.042-.066-.004h-.06l.017.037.022.03.025.026-.056-.03-.064-.043-.065-.024-.067.031-.058.039-.17.05-.048.026-.032.035-.02.047h-.038v-.044h-.039l-.074.02-.114-.022-.116.002-.078.091-.015-.032h-.005l-.024-.015v-.044h.044v-.047l-.022-.044.029-.036.052-.027.053-.019-.054-.023-.055-.008-.058.01-.063.021v.044h.041v.044l-.063.022-.042.043-.052.115-.018-.027-.019-.01-.02-.003-.02-.01.022-.037.033-.037.044-.033.054-.023-.004-.026-.002-.027.004-.036-.08-.004-.063.014-.042.04-.01.077-.032-.018-.009-.002-.009.007-.029.013-.002-.02.004-.042v-.02h-.038v.044h-.045l-.03-.055-.025-.027-.027.004-.033.036-.052-.04-.013-.018-.035-.051-.02-.03-.034-.08-.016.017-.022.016-.022.022-.02.033-.074-.057-.02.03-.004.063-.018.051-.043.026-.077.032-.036.03v.04l.12.092v.048l-.036.002-.017.01-.009.015-.012.013-.046-.04-.027.107-.005.082-.037.055-.123.02.009-.031.005-.006.011-.005.018-.015-.041-.043-.026-.093-.01-.084-.004-.155-.042-.047-.138.058.026-.076-.035-.042.084-.122.054-.046.05-.023.003-.033.034-.018.047-.046.033-.02-.035-.043.017-.035.022-.025.03-.019.044-.01v.028l-.013.006-.016-.002-.01.006.055.029.047.016.035-.018.018-.067h-.033v-.044l-.04-.04.01-.042.043-.035.062-.016v-.044l-.02-.056.054-.295.022.013.04.016.02.015-.004-.02.004-.068-.033.022-.014.015-.033-.037.114-.11.115-.214.087-.233.06-.302.11-.167.042-.193.04-.11.161-.282-.043-.29-.017-.305-.085-.291.103-.397.073-.207.087-.172v-.04h-.033l.097-.167.014-.078-.033-.071v-.04l.064-.199-.013-.085-.094-.03v-.04l.083-.142.091-.267.042-.275-.064-.162v-.09l.066-.12.054-.14.112-.58.026-.084.063-.145-.009-.04.01-.093.054-.055.13-.075-.038-.109-.014-.142.021-.095.066.037.007-.02v-.018l.007-.01.027.004-.043-.096-.015-.11.024-.096.626.111.67.117.879.153.785.136.838.146.668.116.32.056.652.113.587.102.155-.005.145-.06.613-.508.596-.497.657-.546.435-.362.363-.617.352-.601.26-.442.311-.534.135-.145.156-.079.49-.102.502-.103.812-.172.806-.169.756-.158.666-.139.121-.109.25-.615.187-.457.203-.497.166-.408.074-.118.13-.11.613-.347.682-.386.414-.233.038-.023.006-.002.001-.002-.001-.004-.28-.33-.618-.73-.62-.733-.62-.733-.62-.736-.017-.021-.019-.022-.018-.022-.018-.02-.734-.801-.724-.788-.011-.013-.735-.803-.734-.803.216-.065 1.182-.355 1.399-.42 1.398-.423.332-.1 1.067-.322.203-.06 1.066-.31 1.27-.37 1.268-.369 1.27-.37.641-.187v-.003l.196-.084.671-.64.11-.085.057-.018.017-.005.016-.006.016-.005.01-.006 1.117.193 1.115.193 1.115.193 1.116.193.453.078h.004l1.924.374.305.12.314.186.58.349.496.298.495.297.495.299.495.296.495.297.495.297.496.297.495.296.495.297.495.297.495.296.495.297.496.297.495.297.495.296.495.297.504.3.374.328.477.417.477.415.477.415.477.415.535.468.535.468.535.467.11.097.425.371.597.49.497.408.497.407.497.408.497.408.646.53.647.527.648.528.645.528.274.224.374.304.648.526.645.526.646.526.539.437.538.437.539.437.537.437.433.351.037.015.036.014.034.015.037.014.517.048.45.041.45.04.45.042.449.04.579.053.578.053.579.053.578.053.579.052.579.053.578.053.579.053.43.04.43.038.43.04.43.038.594.055.247-.053.147-.03.1-.023.246-.052.247-.053.631.078.629.078.63.079.631.078.562.075.562.074.563.075.562.074.316.042h.003l.069.119.272.467.314.863.025.164.022.348.055.196.076.191.085.146.12.111.129.091.105.111.047.171.303-.003 1.003-.011 1.003-.011 1.003-.011 1.003-.011.132.142.071.05.147.068.022.048.051.282.001.215-.049.143-.128-.047-.028.042-.007.018-.007.027.111.008.094-.024.071-.06.038-.1.065.044-.004.058-.061.118-.035.168-.003.076.029.151.072.13.472.524.029.076-.082.124-.042.12-.005.126.031.136.056.102.18.248.038.067-.009.075-.008.023.089.055.192.087.062.008.192-.008.049.015.113.062.067.014.024.017-.004.04-.018.043-.022.03-.036.016-.038-.002-.042-.015-.096.05-.026.041.008.09.054.085.113.117.128.103.106.044.003.03.127.19.227.228.027.066-.051.129-.007.082.005.074.019.033-.013.045-.051.102-.072.106-.069.064.04-.075.009-.054-.013-.157.016-.09.033-.05.018-.053-.027-.093-.084.09-.092.238-.094.065.103-.276.013-.073-.028-.122-.045.009-.047.087-.107.45-.004.104-.012.016-.02.042.007.035.063-.006.031-.03.04-.108.042-.04v.285l.022.068.052-.01.053-.054.029-.067.04.07.007.086-.011.108.071.027.054-.03.042-.063.029-.064.002.112-.013.038-.029.029.027.058-.005.038-.036.022-.062.006.058.14.02.038-.138-.07-.056.003-.04.069.118.05.101-.005.08-.06.051-.114h.038l.038.09-.069.072-.023.125.03.104.1.004.042-.048-.025-.045-.044-.044-.011-.044.044-.058.06-.033.13-.03.111-.011.074.036.201.188.209.107.054.076-.108.079.079.038.073-.01.034-.045-.03-.073.094.044.098.027.214.017.047-.015.1-.06.045-.013.06.008.112.032.062.008.114.034.087.082.129.186.042.036.04.022.038.027.036.05.015.049.011.114.016.051-.071.004-.041-.03-.017-.061.011-.084h-.038l-.118.144-.31-.033-.111.108h-.041l-.039-.11-.097-.001-.215.11.048.028.123.036.063.03.051.038.033.033.04.03.069.03.036.049.029.022.031-.011.096-.097.002-.013.031-.027.012-.036.017-.02.038.022.043.038.053.025-.04.09-.054.043-.057.033-.043.055-.017.07-.003.166-.015.073.06-.016.066-.035.065-.047.056-.053.064-.024.076.022.116.067-.046.124.017.122.041.124.022.13-.011.272.031.115.096-.013-.007.117.038.043.06-.014.062-.058-.029-.095.05-.062.088-.005.081.074.035-.047.029.07-.017.065-.031.067-.018.084-.02.049-.049.02-.063.002-.062-.01.046.073.136.028.045.076.056-.034.036-.053.066-.13.005.03.006.003.009-.002.02.01-.057.112.048.019.143-.046.089.016.152.068.091.007-.153-.167-.047-.1.086-.084.07-.004.055.035.049.042.056.016.043-.02.109-.111-.02.102-.052.155-.006.092.035.146.031.08.03.035.106.049.103.12.326.515.166.208.048.038.16.07.053.043.083.087.058.046.35.162.22.162.125.014.13-.007.142.02.174.102.163.162.283.388.096.173.412.3.093.104.02.162-.256-.23-.136-.087-.309-.038-.079-.028-.055-.07-.043-.234-.049.04-.051.108-.027.104-.006.08.002.076.018.07.04.06.056.06.015.032-.024.388.009.105.153.483.063.102.093.09.103.065.484.169.165.15.1.214.042.287-.004.689-.021.149-.059.126-.11.07-.004-.096-.043-.033-.06.022-.051.07.036.158-.014.243-.046.248-.05.171-.087-.065-.026-.09.002-.1-.009-.093-.04-.076-.051-.05-.052-.04-.046-.05-.031-.024h-.025l-.018-.014-.011-.175-.011-.042-.022-.05-.043-.061-.069-.07-.074-.041-.064.022-.147.287-.045.04-.062.04.029.086.06.082.033.03.012.047.053.09.009.06-.018.017-.04.015-.04.025-.018.047.013.091.021.09.006.09-.04.099.094.033.08-.05.04-.094-.024-.104.055.016.038.033.065.08.031.015.024-.006.016.006.005.049v.038l.008.02.016.007h.034l.144.035.058.086.029.112.061.113v.044l-.112.118.06.295.132.326.113.21.339.41.143.24.058.298h-.036l-.098-.299-.04-.047-.069-.016-.083-.039-.071-.049-.071-.105-.089-.071-.081-.01-.018.123.041.087.145.105.046.064.029-.064.009-.027h.038l.214.453.644.836.149.131-.017-.084-.038-.098-.016-.082.049-.035.06-.029.025.015.024.138.029.035.027.018.013.015.045.087.105.05.192.057.049.062.109.282-.041.044.068.097.077.165.063.18.027.139-.007.078-.029.138-.007.082.012.08.026.057.025.042.031.107.082.151.014.097-.034.256.045.255.049.144.06.075.043-.12.147.121.155.195.063.102.094.095.113.413.141.095.113.107.049.238.012.383.024.073.049.109.06.103.06.059.083-.002zm-48.666 48.757v.084l-.016.105-.038.129.014.099.05.174-.055.119.002.009-.13-.009-.157-.191-.129-.048-.121-.009-.06-.054.16-.068.09-.074-.036-.122-.144-.077-.143.022-.167.015-.114.029-.107.06-.09.053-.113-.039-.127-.122-.09-.083-.13-.031-.15-.108-.136-.107-.067-.122-.036-.182-.098-.069h-.098l-.067-.017.014-.04-.007-.127-.084-.107-.052-.115.009-.106.083.024.031.045.067.055.22.1.12.138.06.084.021.144-.004.069.102.115.098.084.038.023.205-.022.067.108.114.009.115-.036.097-.008.068.069v.07h.069l.166-.075-.16-.031-.083-.131.07-.107.041-.17.036-.058.082-.013.036.015.046.018.155.097.008.098.074.122-.01.063.139.022.036.115zm-1.51-.506-.084-.069-.084-.108-.143-.184-.12-.167-.045-.067.063-.057.136-.007.1-.031.073-.062.056-.104.058.006.01.002.032-.119-.044-.142-.076-.034-.076-.035-.073-.005-.127-.028-.069-.005-.014-.026.043-.047.053-.022.015.051.183.018.143.059.096.112.006.024.034.157.002.113-.03.129.02.107.182.047.053.13-.232.003-.067.088.032.1.138.049.078.047.107.048.036.143v.07l-.165-.1-.205-.062zm43.164-64.681.11.047.302.088.284.082-.103.04-.087-.02-.084-.04-.094-.022-.092.013-.087.036-.08.055-.069.067-.046-.05-.005.004-.025.006-.046-.033-.031-.014h-.001v-.04l.076-.033.014-.035-.023-.04-.024-.025-.022-.022-.058-.028-.061.01-.06.023-.053.013-.072.03-.071.074-.062.1-.043.104-.015-.166.105-.164.164-.118.168-.031.064.01.049.024zm-70.778 12.22.06.049.027.027-.013.006-.025-.011-.044-.008-.005.026.024.04-.046-.007-.11-.073-.03-.07.01-.041-.055-.006-.069.02-.038-.052-.027-.086h-.05l-.062.044-.013-.044.042-.118.054-.064h.069l.078.01.067.043.054.122.037-.006.012-.045.024.03zm-.479.007.09-.004.019.03-.103.056-.044.035-.06.067-.056.115-.085.061-.12-.08-.107-.12-.103-.082-.227-.244-.015-.016-.02-.037v-.023l.011.002.011-.013-.01-.036-.055-.062-.065-.093.001-.046.06.015.055.047.103.13.049.038.103.056.198.088.094.025.071.007.053.031.025.046-.118.005-.018.08.045.075.068-.018.069-.077zm-.524-.719-.054-.018-.073-.071.01-.09.07-.054.081.01.062.068-.004.073-.06.058zm-.334.22-.054-.04-.027-.007-.026-.01-.03-.045-.004-.063-.051-.019-.08-.014-.014-.022.023.007-.005-.027-.05-.067-.084-.057-.102.006-.045.027-.026.024-.043.014.002-.058.056-.086.087-.025.105.038.28.188.03.047.03.073.084.052.09.033.126.079.2.167.049.056-.01.022-.021.02.007.028-.03-.017-.088-.069-.076-.047-.058-.015-.023-.04-.03-.069-.052-.025-.036.016-.051-.016zm.624-.322-.007-.178-.007-.04v-.071l.036-.05.054-.021.05.023.026.062.004.058-.014.046-.026.002-.034-.028-.008.011.015.046-.004.022-.012-.008.038.088.04.167-.026.057-.063-.099-.036-.025zm-1.228-.146-.007.039-.033.072-.047.031-.038-.02-.074-.054-.452-.233-.121-.03-.038.088-.057-.03-.107-.103-.072-.085-.037-.133.048-.11.14-.145.06-.078.048-.03.01.04-.034.073-.***************.************.***************.**************.***************.***************.047-.***************.**************.024.049-.006.009-.005-.018-.008.003-.027.026-.025.047.007zm-.783-.999-.01-.05-.014-.134.015-.26.09-.217.144-.134.04-.017-.077.075-.061.098-.047.108-.**************.***************.04.005-.004.042-.05.084-.***************.***************.065-.014.02.01-.031.053-.056.03-.071-.013-.112-.091-.064-.04z",mask:"url(#mapSaudiMobile_svg__a)"})),T||(T=AV.createElement("g",{clipPath:"url(#mapSaudiMobile_svg__b)"},AV.createElement("path",{fill:"#FFCA00",d:"m96.634 76.16-4.757 5.124-4.758-5.124c-2.627-2.83-2.627-7.417 0-10.247 2.628-2.83 6.888-2.83 9.515 0s2.628 7.418 0 10.247m-4.757-3.513c.825 0 1.495-.72 1.495-1.61s-.67-1.61-1.495-1.61-1.495.72-1.495 1.61.669 1.61 1.495 1.61"}))),Z||(Z=AV.createElement("path",{fill:"#E5F0FC",d:"M66.89 107.144c-1.393 0-2.413-.528-3.073-1.272l.588-.756a3.38 3.38 0 0 0 2.52 1.14c1.428 0 1.884-.768 1.884-1.38 0-2.064-4.74-.924-4.74-3.78 0-1.32 1.176-2.22 2.748-2.22 1.224 0 2.172.408 2.844 1.116l-.6.732c-.6-.66-1.44-.96-2.316-.96-.96 0-1.644.516-1.644 1.272 0 1.8 4.74.768 4.74 3.756 0 1.164-.792 2.352-2.952 2.352m8.927-.144h-.9v-.66c-.48.528-1.14.804-1.92.804-.984 0-2.028-.66-2.028-1.92 0-1.296 1.044-1.908 2.028-1.908.792 0 1.452.252 1.92.792v-1.044c0-.78-.624-1.224-1.464-1.224-.696 0-1.26.252-1.776.804l-.42-.624c.624-.648 1.368-.96 2.316-.96 1.224 0 2.244.552 2.244 1.956zm-2.496-.504c.636 0 1.248-.24 1.596-.72v-1.092c-.348-.48-.96-.72-1.596-.72-.84 0-1.428.528-1.428 1.272 0 .732.588 1.26 1.428 1.26m9.112.504h-.9v-.816c-.444.504-1.2.96-2.064.96-1.212 0-1.848-.588-1.848-1.848v-4.092h.9v3.816c0 1.02.516 1.32 1.296 1.32.708 0 1.38-.408 1.716-.876v-4.26h.9zm6.897 0h-.9v-.864a2.44 2.44 0 0 1-1.956 1.008c-1.512 0-2.58-1.152-2.58-3.036 0-1.848 1.056-3.048 2.58-3.048.78 0 1.488.384 1.956 1.02v-3.084h.9zm-2.64-.66c.72 0 1.416-.408 1.74-.912V102.8c-.324-.504-1.02-.936-1.74-.936-1.164 0-1.86.96-1.86 2.244s.696 2.232 1.86 2.232m4.886-5.964a.6.6 0 0 1-.6-.6c0-.336.276-.612.6-.612.336 0 .612.276.612.612s-.276.6-.612.6m.456 6.624h-.9v-5.796h.9zm4.136-2.508h-2.88v-.792h2.88zm8.145 2.508h-1.14l-.708-1.776h-3.984L97.773 107h-1.14l3.216-8.004h1.236zm-2.148-2.664-1.692-4.308-1.704 4.308zm4.059 2.664h-.9v-5.796h.9v.936c.468-.612 1.14-1.056 1.932-1.056v.924a1.6 1.6 0 0 0-.36-.036c-.552 0-1.308.456-1.572.924zm7.585 0h-.9v-.66c-.48.528-1.14.804-1.92.804-.984 0-2.028-.66-2.028-1.92 0-1.296 1.044-1.908 2.028-1.908.792 0 1.452.252 1.92.792v-1.044c0-.78-.624-1.224-1.464-1.224-.696 0-1.26.252-1.776.804l-.42-.624c.624-.648 1.368-.96 2.316-.96 1.224 0 2.244.552 2.244 1.956zm-2.496-.504c.636 0 1.248-.24 1.596-.72v-1.092c-.348-.48-.96-.72-1.596-.72-.84 0-1.428.528-1.428 1.272 0 .732.588 1.26 1.428 1.26m5.2-1.068c.312.504 1.02.912 1.728.912 1.176 0 1.872-.948 1.872-2.232s-.696-2.244-1.872-2.244c-.708 0-1.416.432-1.728.936zm0 1.572h-.9v-8.004h.9v3.084c.468-.636 1.164-1.02 1.956-1.02 1.512 0 2.58 1.2 2.58 3.048 0 1.884-1.068 3.036-2.58 3.036a2.44 2.44 0 0 1-1.956-1.008zm6.446-6.624a.6.6 0 0 1-.6-.6c0-.336.276-.612.6-.612.336 0 .612.276.612.612s-.276.6-.612.6m.456 6.624h-.9v-5.796h.9zm6.32 0h-.9v-.66c-.48.528-1.14.804-1.92.804-.984 0-2.028-.66-2.028-1.92 0-1.296 1.044-1.908 2.028-1.908.792 0 1.452.252 1.92.792v-1.044c0-.78-.624-1.224-1.464-1.224-.696 0-1.26.252-1.776.804l-.42-.624c.624-.648 1.368-.96 2.316-.96 1.224 0 2.244.552 2.244 1.956zm-2.496-.504c.636 0 1.248-.24 1.596-.72v-1.092c-.348-.48-.96-.72-1.596-.72-.84 0-1.428.528-1.428 1.272 0 .732.588 1.26 1.428 1.26"})),z||(z=AV.createElement("path",{fill:"#fff",d:"M10.06 19.24a.76.76 0 0 1-.48-.12.65.65 0 0 1-.28-.32 3.4 3.4 0 0 1-.26-.44q-.18-.32-.22-.56-.06-.64-.08-1.24 0-.6.06-1.68a8.4 8.4 0 0 1 .24-1.56q.18-.7.36-1.18.18-.5.28-.74.1-.26.04-.26-.08 0-.18.16-.08.14-.2.38-.1.24-.24.52a7 7 0 0 1-.26.54q-.28.62-.66 1.32-.36.68-.74 1.32t-.66 1.12q-.18.2-.42.58-.24.36-.52.76t-.58.66q-.44.42-.9.5a1.27 1.27 0 0 1-.86-.1q-.2-.02-.42-.22a1.4 1.4 0 0 1-.36-.46.8.8 0 0 1-.06-.52 9 9 0 0 1 0-.86q.04-.6.12-1.3.08-.72.16-1.38.1-.66.18-1.1.18-1.28.48-2.62.32-1.34.58-2.7.16-.4.28-.9.14-.52.28-.96.02-.44.32-.54.3-.12.64-.04.36.08.5.28.2.2.38.62t-.1 1.06a20 20 0 0 0-.78 2.4q-.36 1.4-.66 3.4l-.18 1.08q-.08.58-.16 1.12l-.1.9q-.02.34.02.36.16.04.34-.18.2-.22.42-.7a63 63 0 0 0 1.54-2.7q.74-1.34 1.48-2.8.34-.58.68-1.26.36-.7.68-1.28l.66-1.16q.32-.56.56-.94.24-.4.36-.44.54 0 .8.28.28.28.44.68.04.1.04.34a.85.85 0 0 1-.06.42q-.02.14-.18.58-.14.44-.26.98-.2.54-.36 1.08-.14.52-.2.86-.16.86-.4 1.88t-.42 2.04q-.16 1-.18 1.78-.02.56 0 .74.04.16.12.34.04.16.18.08a.9.9 0 0 0 .28-.28q.28-.42.42-.72.16-.3.32-.64.16-.36.44-.9.5-.92 1.02-2.02.54-1.1 1.02-2.18.5-1.08.86-1.92.38-1.08.64-1.74.26-.68.48-1.04t.42-.48q.1-.08.42-.08.34 0 .52.16.08.08.28.26a.49.49 0 0 1 .18.42q-.1.32-.38 1.04a41 41 0 0 1-.62 1.6 71 71 0 0 1-.68 1.66q-.32.78-.54 1.24-.22.42-.42.72t-.2.46q0 .08-.2.5-.2.4-.52.98-.3.58-.66 1.2t-.68 1.14a9 9 0 0 1-.54.8q-.5.62-1.08 1.04a6 6 0 0 1-1 .66q-.44.22-.58.22m7.72-2q-.14.02-.32-.1a4 4 0 0 0-.3-.24q-.2-.24-.28-.42a1 1 0 0 1-.08-.4q0-.22.04-.52.1-.32.24-.86t.34-1.16a4 4 0 0 1 .14-.56q.12-.34.24-.64l.18-.48q.14-.5.28-.96.16-.46.38-1.06t.54-1.52l.66-1.8q.22-.6.32-.86t.14-.42q0-.16.22-.3.24-.16.42-.16.28.02.54.34.26.3.36.74.1.42-.14.82-.32.72-.64 1.62l-.6 1.76q-.3.88-.6 1.62a1.6 1.6 0 0 1-.14.34 6.108 6.108 0 0 1-.34.7l-.06.14a4 4 0 0 1-.08.34q-.06.22-.12.38a6 6 0 0 1-.06.26 3 3 0 0 0-.06.32q.1-.12.28-.28.18-.18.46-.44.32-.28.56-.48t.48-.36q.22-.16.4-.3l.2-.14a1 1 0 0 0 .08-.14.27.27 0 0 1 .12-.1.8.8 0 0 1 .14-.1l.2-.16a2.1 2.1 0 0 1 .8-.32q.22-.04.42-.04.24 0 .5.2.26.18.48.52.2.26.24.62.06.34.02.92 0 .16-.1.6l-.12.66-.18.68h.16a.65.65 0 0 1 .42-.02l.22.06a1 1 0 0 1 .18.14q.1.08.18.12.12.16-.04.46t-.36.42q-.48.32-.86.4-.36.08-.76-.14a1.35 1.35 0 0 1-.52-.62q-.12-.36-.1-.98.08-.88.1-1.26.02-.4-.2-.4-.24 0-.66.24a6.5 6.5 0 0 0-.9.62q-.5.38-1 .86-.48.46-.86.94-.24.3-.52.6a1 1 0 0 1-.68.3m12.533.36q-.6.34-1.28.4a2.95 2.95 0 0 1-1.24-.18q-.58-.24-.94-.7-.2-.26-.38-.82-.18-.58-.06-1.56a6.3 6.3 0 0 1 .56-1.86q.46-.94 1.12-1.66t1.44-.98a3 3 0 0 1 .56-.14q.3-.06.78-.02.46.06.82.24.36.16.52.36.32.32.42.62t.08.58q-.1.64-.58 1.38-.46.74-1.3 1.1a2.7 2.7 0 0 1-.8.18q-.46.04-1.26-.12-.48-.1-.64.1-.14.2-.12.78.04.36.16.64.14.26.5.36.46.08.66.02.2-.08.5-.22.16-.1.3-.22.16-.12.3-.24.16-.12.26-.18a.5.5 0 0 1 .2-.1 1 1 0 0 1 .2-.02q.22.02.42.24t.26.42q0 .16-.2.46a4 4 0 0 1-.54.6q-.32.3-.72.54m-.26-4.46q.14-.06.4-.28.28-.22.5-.46.22-.26.22-.4 0-.22-.3-.38-.28-.16-.74-.06-.16.04-.38.22t-.44.42a4 4 0 0 0-.38.44 2 2 0 0 0-.22.32q.08.1.32.16t.52.06q.3 0 .5-.04m4.68 4.44q-.06.02-.26-.02a5 5 0 0 1-.38-.1.7.7 0 0 1-.22-.1 2 2 0 0 1-.22-.28q-.14-.22-.16-.58 0-.36.34-.86.1-.2.24-.6t.28-.84q.16-.46.28-.82.14-.36.2-.5.04-.34-.04-.48-.06-.16-.34-.18a.4.4 0 0 1-.32-.14q-.1-.14-.1-.5.04-.28.14-.48a1.04 1.04 0 0 1 .32-.36q.2-.1.5-.14a3.3 3.3 0 0 1 .62-.02q.3.02.42.14.14.08.22.36a3.6 3.6 0 0 1 .16.54q.06.28.08.42.2-.16.44-.44.26-.3.52-.56.32-.22.76-.44.44-.24.64-.24.219 0 .54.22.32.2.4.36.2.34.28.72a.69.69 0 0 1-.12.64 2.7 2.7 0 0 1-.56.52q-.36.24-.9.14-.12-.04-.26-.3-.12-.26-.14-.38 0-.06-.18.12l-.4.4q-.22.24-.42.44-.34.42-.72 1.14t-.9 1.74a9 9 0 0 0-.4.96q-.16.44-.34.5m7.627.78q-1.1 0-1.7-.64-.58-.64-.58-1.96 0-.72.26-1.58.279-.86.72-1.68.46-.82 1-1.42.54-.62 1.08-.86.2-.1.42-.14.24-.06.46-.06.24 0 .52.04.3.02.56.12.46.14.8.54.36.4.36 1.16 0 .2-.04.44a2 2 0 0 1-.14.44q-.26.66-.8 1.26-.52.6-1.1.84a1.6 1.6 0 0 1-.32.12q-.14.02-.3.02-.42 0-.76-.12a2.6 2.6 0 0 1-.6-.32l-.16-.14-.1.2q-.14.38-.2.64-.04.26-.04.38 0 .24.02.5.02.24.12.4a.6.6 0 0 0 .2.16q.14.06.34.06h.22q.12-.02.2-.04.36-.08.58-.26.24-.2.5-.34l.08-.04-.02-.06q.34-.18.46-.22.12-.06.18-.1a.25.25 0 0 1 .18-.08.7.7 0 0 1 .54.16q.28.18.28.46 0 .12-.18.38a3 3 0 0 1-.42.52q-.24.24-.5.38a.5.5 0 0 0-.12.06.5.5 0 0 0-.12.06h.02q-.18.24-.36.24h-.16q-.08.14-.52.3-.44.18-.86.18m1.04-4.82q.18 0 .32-.06.16-.06.3-.14.48-.34.62-.88t-.1-.82a.2.2 0 0 0-.1-.08.25.25 0 0 0-.14-.04q-.22 0-.5.14a2.3 2.3 0 0 0-.52.4 3.3 3.3 0 0 0-.48.62l-.26.42a.2.2 0 0 1 .06-.02.2.2 0 0 1 .06-.02q.18 0 .24.12t.14.24q.**********"})),_||(_=AV.createElement("path",{fill:"#FFCA00",d:"M92.444 53.329a.5.5 0 0 0 .703.073l3.494-2.836a.5.5 0 1 0-.63-.776l-3.106 2.52-2.52-3.106a.5.5 0 1 0-.777.63zM55.462 12.624c11.898-3.524 21.712.472 28.266 8.372 6.573 7.924 9.873 19.793 8.607 31.966l.994.103c1.29-12.396-2.06-24.545-8.831-32.707-6.79-8.185-17.01-12.339-29.32-8.693z"})),$||($=AV.createElement("defs",null,AV.createElement("clipPath",{id:"mapSaudiMobile_svg__b"},AV.createElement("path",{fill:"#fff",d:"M82.906 62.182h17.942v19.322H82.906z"})))));function AQ({language:A,event:e,eventData:t}){let{t:a}=(0,Ae.$G)(),l="decarbonization"===e,i="QHSEEXPO"===e,n="libya"===e,s="AfricaFranceForum"===e,r="Gitex"===e,c="PentabellSalestraining"===e,g=l?Ax:n?AG:r?AI:s?AC:c?AC:i?AS:Ax,h=l?Ak:n?AG:r?AJ:s?AX:c?AX:i?AN:Ak;return AA.jsx(Ac,{bannerImg:l?Ag:n?Am:r?Ad:c?Ap:s?Ao:i?Au:Ah,bannerMobileImg:l?AU:n?Aq:r?Af:s?Av:c?Aw:i?AE:Ab,Icon:g,MobileIcon:h,..."leap"===e&&{LeapIcon:AY},event:e,eventData:t,title:a(l?"eventDetails:bannerTitle":n?"eventDetailsLibya:bannerTitle":r?"eventDetailsGitex:bannerTitle":s?"ForumAfricaFrance:bannerTitle":c?"PentabellSalestraining:bannerTitle":i?"QHSEEXPO:bannerTitle":"eventDetailsLeap:bannerTitle"),subtitle:a(l?"eventDetails:bannerSubTitle":n?"eventDetailsLibya:bannerSubTitle":r?"eventDetailsGitex:bannerSubTitle":s?"ForumAfricaFrance:bannerSubTitle":c?"PentabellSalestraining:bannerSubTitle":i?"QHSEEXPO:bannerSubTitle":"eventDetailsLeap:bannerSubTitle"),language:A,url:l?"franco-saudi-decarbonization-days":n?"Libyan-French-economic-forum-2025":r?"Gitex-africa-morocco-2025":s?"Africa-France-forum-on-ecological-and-energy-transition-2025":c?"Pentabell-sales-training-and-workshop":i?"QHSE-EXPO-2025":"leap-tech-conference-2025-riyadh",name:a(l?"eventDetails:titleBreadCrumbs":n?"eventDetailsLibya:titleBreadCrumbs":r?"eventDetailsGitex:titleBreadCrumbs":s?"ForumAfricaFrance:titleBreadCrumbs":c?"PentabellSalestraining:titleBreadCrumbs":i?"QHSEEXPO:titleBreadCrumbs":"eventDetailsLeap:titleBreadCrumbs"),height:"70vh",t:a})}},94932:(A,e,t)=>{"use strict";t.d(e,{default:()=>F});var a=t(10326),l=t(16027),i=t(90423),n=t(76297),s=t(62445),r=t(33487),c=t(87166),g=t(36056);let h={src:"/_next/static/media/image1.62171899.png",height:487,width:863,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAAUVBMVEWjoJpsb3aKhYEZGiW1r6etqqhWYm+ekHWvqaB9gYWxoIEoJCu8rZSVjojNw7Foa3ktMkRjWFFuZmiMb4pGP054eHxXYGyIjZg0O1FdYWdYTlPguF7KAAAAD3RSTlPr6+v+6+vr/f3r/f79/f02vo7uAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAM0lEQVR4nAXBBwKAIAwEsGNPbctU/v9QEoQc6hufCOki7dBE68MUJgbx94+1CckrQDt7ATJkAhfySPt2AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:5},m={src:"/_next/static/media/image2.253db932.png",height:486,width:647,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAZlBMVEXKvq2lopehnJaBjp1DVWwoQmFLZXyusqze2cy7rp5eZIhXd5atoZZQSWJZZ3sZMEoqQ3eaxfQnQmV3foiUm6IMKE2Lg4JsbXqrq6r39fPm4+W/vb64tL6ZiIiAk6kqQF5ja4XP0czLplN9AAAAFHRSTlP+/f3s9v71/v7+/uz9/uzs/uzs/o9gVroAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA6SURBVHicBcEFAoAwDACxA6a4tBsu//8kCYUPENsS8R8xbCsi+crnfrA429mxTtxGH9U3MfWDm01T/UcIAtj1k16+AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},d={src:"/_next/static/media/image3.f8fda693.png",height:487,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAbFBMVEWGemytpY2ak4oyNziyp5R/eHqdmIVHO0tcXGFtdYeqoI0KDRQ5NUU3QU68tqvJvamQhnBgY2lpY2VMUVlFS1NtcXgxMDOXiW6BWUZ8cWigl3+Rc260rJp4YV2UlZZ+aHDSysbP0Numpabj3dJkbQ+IAAAAEXRSTlP+/erq/v79/urq6ur+/v39/YJRLToAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA9SURBVHicBcELAkAgFATAjVIhvD5SiXD/O5qBWBe+YWAI39P4mSPoFXct6YALRsrZAv4iZz3t6EetO6WmH1ZJAwgU4xaUAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},o={src:"/_next/static/media/image4.63aa1c29.png",height:487,width:647,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAY1BMVEXAtZtzeYiblonj276cp605R2rWz7bBuqVMYHo/Tl8RFiUAFGsAJpGiq7a0qpeLio2urql0bWfQx6jJw7CoopqZoad8iJZDXIldZYF7eHZ0eI1ERlCRkpAOQn/QwMfj7eqdmJpzX6E+AAAAFHRSTlP+6/396/79/vXr6/7+6/3+/////g5PIDgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA7SURBVHicBcEHAsAQEATAjagpnJ6K/7/SDOSqGEVYBASipDqif0r+RkPy+6GdEHjvq+byV2zLybnRZgJMFQLeYfYM9wAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},b={src:"/_next/static/media/image5.cc516408.png",height:487,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAY1BMVEWSdUJlTU94TUS1oovEsYjXz7a/sY63nU2ShHvc28eAaFrq1adPOzd7fH1maWt7Xk+ahnammouTdnKBc29fRkaFaFvBoX2Xa1Ssmm6OfHmZc2qjh2u7qp1eVla4l4R0cGRLOjR/NVLeAAAAD3RSTlP+6urq/f39/er96v3+6uqOYJ00AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPElEQVR4nAXBBwKAIAwEsENQhqMty638/5Uk8OMEM1uLA4n5pxtxZ6m5ESKJ1LIQznw9X3oLnBq029bQAUwgAvMv45tHAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},u={src:"/_next/static/media/image6.259d7064.png",height:487,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAXVBMVEXnw37Iq3rlzJSbh4pXU1yAenG/q4XGoWNYYW1APkWploC6yd5TQz2Eb12zpo+Se2yYj3yZgHnDpHKiglpcYGR+W0ecfDWulW1lSjrkxo2Jf2XS08xoZWLFta+znGMGW63yAAAADnRSTlP9/f3+9er9/erq/ur+/tfcDqwAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA6SURBVHicBcEHAoAgDACxU0HAUSnL7f+faYJzQDf2tJbOvEvkq3k7LlHSUleN04AUo+/9FKz1wcw+/EWRAqVsXlYZAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},v={src:"/_next/static/media/image7.3273d2d2.png",height:487,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAWlBMVEWbhFKBaD14Zj8eHhzd06izmWVSXFuznmxNTEZzdnBvXkicoZutl253blZPTFKOf2aupbS7pGyolGcuNESafWRlX1KKeValnIyTlJ0dGxedk3eUdkW0jXSsqpVI/xCqAAAAEXRSTlP+/f3q/urq/ur1/v7q6v7+/oHVLNkAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA6SURBVHicBcEHAoAgDACxU1HA3Zbl+v83Teh6h3oBVfcg4ilfTW8qN8di7dznjFSLza7MuIYtTkP4AUbiArtDFOvRAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},p={src:"/_next/static/media/image8.a4d0e929.png",height:487,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAb1BMVEXex56cg4vLu5nRz78bIjAtKC41PU5DPESXkY9lZWZqY17Pt5K6sp/Pv6SDtdCPjISqv9YADCG1pYt8cHaKc2hRTVNjT0eOgnukkYReT0xcW2aYiHV8d3WSkYCspZeii3mKamnT2dXL4emzusC+tq5OpEyZAAAAEnRSTlP9/v399erq/vTq/v39/f3q6v7OH45UAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPUlEQVR4nAXBCQJAIAAEwI1SOYpuRKL/v9EM0PULplkhmduZUl/YM4TrsQ3ckTHyTcDvR47UfxBskJLq9QdXHQMy3ZY70wAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},E={src:"/_next/static/media/image9.1d138066.png",height:487,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAYFBMVEXUwaaYm5x6b3u9m2+QkY1oZFyTh2u8qYk2N0VSVWeJkZ5ZXWA4Oz8tOUTGrobJp3iUfV6Pf3CBgo1dYHI8QUqVjneCdmjFtI+gjX6SjoiwpZDAnW1SUVdsb3NWX2YhKzg3AIE0AAAADnRSTlP96v796ur9/f7+/urq6h8isl0AAAAJcEhZcwAACxMAAAsTAQCanBgAAAA8SURBVHicBcEHAoAgDASwU0GG2JYhCjj+/0sTmIUMICtO4sFMB3q7RGKN8M7m5OyM53tLLneC3pXegpp+SIICth1UpggAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},w={src:"/_next/static/media/image10.bf155ebd.png",height:487,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAYFBMVEWfiF9kZ2gmJybFp36rjFjGpnNSWmBrcnp3cGbn2rt/goA+PkWUjYi7s5hcY2tLOih2dndQV2E4OD5VSkJESE5oX1+ah3DJxLVocXuZe1VtV02xnId7dG67rII+MCuuk2qp40MyAAAAEHRSTlP+6urq/f7q6vX96v7+/f7+6PBMYQAAAAlwSFlzAAALEwAACxMBAJqcGAAAADpJREFUeJwFwYcBgCAQALFTQcD6dLv7b0mC0orVOdD/e5daIX42X08u7FFmv4WFUyT4IyWGyfSj6WwDS2gCvkSkuvYAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},q={src:"/_next/static/media/image11.5e5cb006.png",height:487,width:647,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAYFBMVEWzsKXEpIaOi4uKk611bm3bwZ1kY2DMs5U5QEzIuaKVkIkqLDFXQk4dHzRcanqmrbTKpn5WVmivo5WbmJHArJWRfWmaiXtZUVSMkqWfnZ/a0LGQfVizvdEsMUAfIC5gZG4iEp7oAAAAFXRSTlP+/ev+9f7r/uv9/uv+/uvr//////5ZrMJ0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAO0lEQVR4nAXBiQKAEBAFwFcIqey66fr/vzQDWrRNygJJE5EaQMx3LiVEBMFnPeSO3vit3/9glZtx5vITR50Cztb8fdQAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},f={src:"/_next/static/media/image12.3ab79b7f.png",height:487,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAbFBMVEWIeF+UinIxSVkRLkWzrZx7bFJTXoCs2ORvdoWgrqlrdIdSaHiTVmmEdohlh5qxUnKPm551i5p6WzeZsbp8mrM0VG2WlYqMazaRhHVhYGOfj5udmo8vPk5FVVnG3u1PRkAhGSFabHMhRF6AiI4MzSGUAAAAEHRSTlP9/erq/f3+/v796urq6urqYrxnKQAAAAlwSFlzAAALEwAACxMBAJqcGAAAADtJREFUeJwFwQUCgDAMALFDN7yd4M7//0iCqSChyDHipm12I/rcl6zLh/WitozK7s9wvDGQpU3dDl3/A014AxadpiuTAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},U={src:"/_next/static/media/image13.0b8d8caa.png",height:487,width:647,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAZlBMVEVQUVGKc1HFqYLMu5Wum4eefUl2X1/XxJa+pXe2ol4hNm9oXE49LyR1bWbbsIDGzMjI1NyKeGajiWWCd2lpZ2JnZ3dlZ2aRfWyOblelj3rGtZGZinGckIOkmZacbWDKn3FHSFC1o5dNy7bpAAAAE3RSTlPr/f796/7+/f7+/v7+6/7r6//+R6Xl1gAAAAlwSFlzAAALEwAACxMBAJqcGAAAADpJREFUeJwFwYUBgDAQALEDqlj5Cq77L0lC1VivnY3EbfXa7Qey3I8USdTZjF1retL8fme5MsCgVJh+SaECyYlcI8cAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},V={src:"/_next/static/media/image14.7528cf90.png",height:487,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAV1BMVEXRuYcxQHV3dGyMhWvCqn7Ns4C0pH26uaSYjG2wva8ZM3Tj48mao5nDyrXh8etmXlajo5RwbXOJgYJNVWeQj5J0Y1nBwcWtrJysoIJjaXd2boeUjoJtdZN7gXDmAAAAD3RSTlPq/v796urq/v7+/v39/f0zvDvrAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBBwKAIAwEsENAhmBbpgr/f6cJ0uV0voMH8+QZ3INvtNJlL6jROtV4QIoQ1dfDABbWnD9LjwKTY9YMCwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},B={src:"/_next/static/media/imagemobile1.5bb5285a.png",height:198,width:349,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAAVFBMVEWNb4rOxbK8rZSUjoguMEFpZGx8f4Kdj3VIQU+xqqKxoIFVYm4ZGiSsqaaJhYGlo6AoJCq1sKifm5NqbXRpbHpmWVEyO05bUlNcYmt6eXtyZ2aLj5o4DgVIAAAAFHRSTlP++/v7/f3i+/77++L94uLi/eLi4pakBnsAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA1SURBVHicY2DnYmdi5GTmZBBlEBWXEheRZGBl4OARYBFhYRARE5PgkJAWYWDjFuYX4uMVBAArnQItn8couwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:5},x={src:"/_next/static/media/imagemobile2.e5bb3813.png",height:198,width:262,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAY1BMVEWjnpawsamtoZeHgYNPcI9LXnHIuajLwK7f2cy8rp6AjpwYLklYZngjPmKbxfR2focpQXWUmqIpQmENKU5gZ4dHTWVra3mssrCPiIS1s72srKq/vr/29PHn4+OBlKrO0syXh4i2lmLOAAAAEXRSTlP7+/v+5PH7/f395OTk5OT+/jhq3rYAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA7SURBVHicBcEHAoAgDASwA9nOtoAb/P8rTQAVvIsaoL37GJ4LRPWr53tjtVx4ywNaERbJB5ZpNLM16QdKFALd34+gywAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},R={src:"/_next/static/media/imagemobile3.21ded12f.png",height:198,width:263,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAXVBMVEWUgGFcYXOQkYzaxahybnc1NkJVXGi3p4vKt5fErIiYnJ29m3BUUWU7PkBuamIuOURcYGJgXVV+fInJqHiPgHGGjZg9QkvLsYKUjXaCdmlUU1jAnW2ejH2UkIojLTrzaYiyAAAAFnRSTlP9/uP7/v3+/f394/v94+Pj4+P9///+i9qpnAAAAAlwSFlzAAALEwAACxMBAJqcGAAAADtJREFUeJwFwQsCQCAQBcDXd4uIdosK9z+mGdAaKRgIQuTJfN2g9wGkC6p3qiRnYT/fVBsVe9bnsenlB0J+AnwhdNftAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},C={src:"/_next/static/media/imagemobile4.26a26f57.png",height:198,width:263,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAaVBMVEWel4Cqpp9OVFhiY2pIPEy6tKWBem9eXF6Ndm+vpI03NUUHChGTjIRqdYkyODmpoI1zeoZWVVg3QE3JvKhpcoGQhXBITVdhZGl6bnLRzdGAWUWYiW00MjV7b2ZsaW2TlZZ3X1t/eIHj3dIIrGD+AAAAFnRSTlP9/f3j/f37/v79/ePj4+Pj4+P9+/37Y/Q2ggAAAAlwSFlzAAALEwAACxMBAJqcGAAAAD1JREFUeJwFwYcBgCAQBLBDqWIBy1MEEfcf0gTyEOySMIhfSkbgRmA9vrpmDOfO+aQ0qAV6SBWsm1vm0dsfTQgC8Lb6b64AAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},M={src:"/_next/static/media/imagemobile5.1ca1d011.png",height:198,width:263,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAXVBMVEXAtpw/TV/Xz7WalIjCva6kn5o7SmyvrqmeqbF0bWbj270PFSVxeYkAEGcAI49XZXqzqpdJYH3Px6t+iZiaoqiKi4xFX4unpJ12eo1FR1FeZoR9enrQv8bi7ekNQH2yuk5GAAAAFnRSTlP94/v7/f79/uP+++Pj/f39++P////+3KAPOAAAAAlwSFlzAAALEwAACxMBAJqcGAAAADtJREFUeJwFwQcCgCAMBLBDRosDtCwV5P/PNMGyQpPBCYEQNfNB/J1asQPWHyEW5/D0t+Y0K7admdWlfj33AoLstJ0fAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},X={src:"/_next/static/media/imagemobile6.95399ba1.png",height:198,width:263,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAY1BMVEV5XU/b2sfY0LWWg4FNOjWpnIqPdXKTdULCsoy2nU1mTk9+Z1i0n4l6TUWOhHTGnX7q1aZ3d3lnamyHZld9cmybhnuYdW2znnNgR0afiW2ZbFWPfXqimZCEal+7q59eVla5mIVqEX4+AAAAFHRSTlP++/vj/v39/fv74+Pj4+P9++Pj/TNfY2YAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA7SURBVHicBcGHAYAgEACxU0HAXp4mWPaf0oSmtcZuzhGN+CxkwuVrTAp6tF5PBSWF55P3phvmaTn28QdGGALNDdCuowAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},W={src:"/_next/static/media/imagemobile7.cd6a7343.png",height:198,width:263,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAXVBMVEXnw39BP0Z/XEd/eXCilYKYiICZgHnCq3/ny5LGoGNVYG6HcV27y99URT9ZYWxUU1q0qJCwmGfCpHKhglldYGWTfWGKgGVlSztoZmOMd3XT1MzHtrGcdTucgjBfWGH76afOAAAAE3RSTlP74/7j/f7++/374/3j/ePj///+Fc/VXQAAAAlwSFlzAAALEwAACxMBAJqcGAAAADlJREFUeJwFwYUBwCAQALHDofpIXfYfk4SUgBAi31tizeLIum7HLhdl1qO31iHt9899NoxZ1DoNqgM/rAJpLJdJQwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},S={src:"/_next/static/media/imagemobile8.10a3c96c.png",height:198,width:263,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAWlBMVEW3oWujpZiTk53c0qdqXUt8Zj1NS0WylnGxmGqZhVaokmR3e24cHBpPWlqRgmirpLR1a1VQS1AvNka9pW+QeF6Qe0yolmmnnYweGxdiYlidk3idgU90c3OcfGZaST4FAAAAEnRSTlP+/v79/fvj/eP7++Pj4/394/3ltOpnAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBhwGAIBAAsaODDX2adf81TXDOpCmQ6N20pwBVjS/UdrMp0fmwljLk1XJexOj3eV38Dz4PAm5syfESAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},D={src:"/_next/static/media/imagemobile9.8ac28da8.png",height:198,width:263,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAY1BMVEXex51lZWc1PU7Rz76Yj4kiJjKXi49CPETNuZaLdGjPwKS4sp+DtdAACiCqwthiT0l8c3e0pIpiX2GVhnajkIJWS1CrpJbW3tiPg4Khi3nL4OeMamqRkIDAt65LTFK5v8QcHCliBklNAAAAFHRSTlP74+P74+P9/fv++/v7/eP////+/sCLqUMAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA7SURBVHicBcEHAoAgDASwQzaOlulW/v9KEsDJADcvKLH5eO4XOBM9N7+w3qRsV4NKiY/+/VB6EkKrbQBIggKtGVTX1AAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},G={src:"/_next/static/media/imagemobile10.4e3a1fdb.png",height:198,width:262,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAYFBMVEWfiGAiIyJRWmFtd4GrjFl+gX/Ep35pbG/n2rvGpnJ5dGtbYmmWkIu7s5hMOih3eHhAQEVRWWNNS07KxLVoX195cGk3MzWZe1Vocnubhm+wm4aXhnFtVkxWSjy7rIKskmlcfSQlAAAAD3RSTlP95OTk++Tk5Pv95P7++/4y57KwAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBBwJAMBAAwUVIot2l6vz/l2Yw3rA4B/57zysleGrJd8kHaxQNU53ZRDTsURl627SdHX9NHALAEU5IogAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},j={src:"/_next/static/media/imagemobile11.ea7a9505.png",height:198,width:262,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAY1BMVEXJpoA/RE1saG2mrrbbwpxgYF1cannFtp+zsKTMspQtLjMyO0qMi4t9dGpcUmEbHTPDp41YQk2bmZOVjoOLkqiuopWRfWlaUVS1wNRhZG8wNUMdHy6enJ5NV27c0bKQfVifhns3N9XXAAAAEnRSTlP+5P7k/eTk/f395OTk5P7++/6bs18nAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBhwGAIBAAsUPRR7DS7e4/pQkgsmoxyADox+Dzm8OXPCnOS9mawBXvo5x7ZZw6ZV3f/kCKAq3AxTxyAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},I={src:"/_next/static/media/imagemobile12.8ee70ff1.png",height:198,width:263,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAb1BMVEV0i5qFfGWfragQL0Wbs7yRm6MnSmar2OWIdluUiHRUaXmyrZpTXoBrc4F0eoxud4l6bVMwR1mEeIeOVmlihJmxUnGWnZWFZTp7mbIvPk6gkZxaa3KYl4tyUytgX2LH3+5PRT1FVFghGCGBiI4+W3FsX3gqAAAAFnRSTlP++/vj/v7+/fv94/v9/f3j++Pj4+PjMRq54wAAAAlwSFlzAAALEwAACxMBAJqcGAAAADtJREFUeJwFwQkCQCAAAMFFJ3KrkNv/32gGnRtjs65C+xC83RNszyXX48UJSVMrWMQZb/VFir5sx3mYfkyTAwdqabcpAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},L={src:"/_next/static/media/imagemobile13.4f593d94.png",height:198,width:262,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAaVBMVEVRUlHMupXHzMjds4KjiWaefUq7p13FqX6LdlrWw5Wxn4lybGN8XFxmW02qloM8LiMgNm/J1d6HeGpsZWKUg26hlo6Qb1elj3plZ2fEtI98c2OvnF5mZHVHSFHJnm+cbWC4oHRoanq4pZinH6bBAAAAEnRSTlPk++T+/f37/f375OT+/uT+/uROF7ncAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAO0lEQVR4nAXBBwKAIAwAsVNBwG0L7v3/R5rgs1BaFwxmnnb7rBEf70te2XCqRTs0PbKcXzqSAtRdlY8/TNoC6PcdpisAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},J={src:"/_next/static/media/imagemobile14.cc4c16a1.png",height:198,width:262,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAYFBMVEXMtISNh2zf8OrDybK1o32Wi2nj48q7uqSvvK7BqX00QXTBw8mZopnUuYNHUW8XM3ZtYVd0b3CSiICgo5J0cIGFhoxbWlutoIKurJlrdJOVkY/DwMCppJhjaXiqq5ycj3PcwnW1AAAAEHRSTlPk+/v75Pv7/f3k/v775P7+6Dd6mAAAAAlwSFlzAAALEwAACxMBAJqcGAAAADpJREFUeJwFwQcCQDAQAMFVkxDcpSHa/39pBj+apmvtQIzH/Vqzk64aRM/ANGcp66ZoTiLl+XDAgut/SvMCvxsNoDoAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6};var H=t(45173);t(50967);var N=t(17577),y=t(52210);t(70580);var Y=t(74345);function F({language:A,event:e}){let t;let{t:F}=(0,y.$G)(),[k,Q]=(0,N.useState)(),O={sector:F("ForumAfricaFrance:sectorValue"),country:F("ForumAfricaFrance:locationValue"),countryConcerned:F("ForumAfricaFrance:countryConcernedValue"),organiser:F("ForumAfricaFrance:organiserValue")};e&&(t={sector:e?.versions[0]?.sector,country:F(`country:${e?.country?.replace(/\s+/g,"")}`),countryConcerned:e?.versions[0]?.countryConcerned,organiser:e?.versions[0]?.organiser});let P=[{title:F("event:events.eventLibya.title"),eventDate:F("event:events.eventLibya.eventDate"),postingDate:F("event:events.eventLibya.postingDate"),exactPlace:F("event:events.eventLibya.exactPlace"),link:"Libyan-French-economic-forum-2025",type:"events",image:g.default},{title:F("event:events.event11.title"),eventDate:F("event:events.event11.eventDate"),postingDate:F("event:events.event11.postingDate"),exactPlace:F("event:events.event11.exactPlace"),type:"events",link:"leap-tech-conference-2025-riyadh",image:s.default},{title:F("event:events.event2.title"),eventDate:F("event:events.event2.eventDate"),postingDate:F("event:events.event2.postingDate"),exactPlace:F("event:events.event2.exactPlace"),link:"pentabell-salon-sme-and-european-microwave-week",type:"blog",image:n.default}];return a.jsx("div",{id:"event-page",children:e?(0,a.jsxs)("div",{id:"event-detail",children:[(0,a.jsxs)("div",{className:"custom-max-width",children:[a.jsx(Y.Z,{t:F,infos:t})," ",a.jsx(r.Z,{htmlString:e?.versions[0]?.content}),(0,a.jsxs)(l.default,{className:"more-events-section",container:!0,rowSpacing:0,columnSpacing:3,children:[" ",k?.length>0&&k?.map((e,t)=>a.jsx(c.default,{eventData:e,language:A,isEvent:!0},t))," "]})]})," "]}):a.jsx("div",{id:"event-detail",children:(0,a.jsxs)(i.default,{className:"custom-max-width",children:[a.jsx(Y.Z,{t:F,infos:O}),(0,a.jsxs)("div",{className:"details",children:[a.jsx("p",{className:"heading-h1",children:F("ForumAfricaFrance:eventProgram:eventProgram")}),a.jsx("p",{className:"text",children:F("ForumAfricaFrance:eventProgram:data11")}),a.jsx("p",{className:"text",children:F("ForumAfricaFrance:eventProgram:data12")}),a.jsx("p",{className:"heading-h1",children:F("ForumAfricaFrance:aboutEvent:aboutEvent")}),a.jsx("p",{className:"text",children:F("ForumAfricaFrance:aboutEvent:description")}),a.jsx(H.default,{slides:[h,m,d,o,b,u,v,p,E,w,q,f,U,V],options:{},slidesMobile:[B,x,R,C,M,X,W,S,D,G,j,I,L,J]}),a.jsx("p",{className:"heading-h1",children:F("ForumAfricaFrance:moreEvents")})]}),a.jsx(l.default,{className:"more-events-section",container:!0,rowSpacing:0,columnSpacing:3,children:P?.map((e,t)=>a.jsx(c.default,{eventData:e,language:A,isEvent:!0},t))})]})})})}},19727:(A,e,t)=>{"use strict";t.d(e,{default:()=>G});var a=t(10326),l=t(16027),i=t(90423),n=t(76297),s=t(62445),r=t(33487),c=t(87166),g=t(74345),h=t(90434);let m={src:"/_next/static/media/image1.d77f8e50.png",height:508,width:677,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAXVBMVEVYTkBfbZpESFCRiKNhaY1weJZwiq4yLyprb4kqLzlBOSsmPVRNSUJWVVtxZUA2OEKJVF86RVuAcJx5O099gadhfbtcYIB7dYVyPW8cHimCbmolLz80NEdmf7qShY8/RWAmAAAACXRSTlP+/////////v4HKoILAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAO0lEQVR4nAXBBwKAIAwEsCuUYlkKblD//0wTTI72l5hxHryJuIxlvZq1X4Fqjbk/CTqbEY0GwN8oIfkfPo0CSeSiOywAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},d={src:"/_next/static/media/image2.dedccd5d.png",height:512,width:683,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAWlBMVEVLSWVyYmVnXU9iU0l6bWaWinxaV26Qh5BtWztNOydGQDhXTkdwbHxVRziVgHE7MTCdjYmAeYVxY1JdWV+SkW3QslSEbVJ/f3BubG9/c3hRS0/LrpfRs6QZHS98QPQ4AAAACnRSTlP9/////////vz8KN17MQAAAAlwSFlzAAALEwAACxMBAJqcGAAAADpJREFUeJwFwQcCgCAMBLArtEXZDobr/980gZNhTt8W1Mx+tz2BqAQbDoP83cI0GanUKFfcsD6vQhU/O6sCNE6HSpwAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},o={src:"/_next/static/media/image3.db96c86e.png",height:512,width:683,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAS1BMVEW7n4ZjV1NRQzZya3qNhY5OTGCTfnRnYm6smI2Be6BGSmw5NS5uZFyJfHjdw51dWWNUOkWTiaU7QmNfXXWLdGprU0BFSFlAPj2WgnmZ1Q7dAAAACnRSTlP+/////////v3+NP+QXwAAAAlwSFlzAAALEwAACxMBAJqcGAAAADZJREFUeJwFwYUBwCAQBLB7xakALftPSoL0+PQIQNVFVBvCyLTIBPm9ufA22FULoxOCfX/tLR4pCAGrBquBhwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},b={src:"/_next/static/media/image4.8f6cff4f.png",height:512,width:683,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAATlBMVEUoJh2+v72FXCdYPxnLzs5zcGbFysqFh3/R09A8KhfFztSmcRCFdV2cUiJKOSN2d3C6qZO2trFeYGCNYxpbSzeYaBiBWUF+f3+PfVp0QxuPzoKsAAAACHRSTlP9/////////CitBBMAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA4SURBVHicBcEJAsAQDATARaIbZy9a//+oGYTwqmo/kHNJkSSe8acoFHhfKO4iap3uu+2ELWvNgA0wywHj7K2qrAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},u={src:"/_next/static/media/image5.66b723c5.png",height:512,width:683,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAV1BMVEVISUpbXWFmbY5teqZQZ5NYbZkgIihnd51yeZF9foBLZ6dDW48QGjyNg2dtZFiPkIV2fK9gdrmJd2uajnSOjcFwicSjm6p9eGi5qGteZHwcKEN7eV9edo6KvGTNAAAACXRSTlP9/////////Pzin71gAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBCQKAIAgEwEUBxbvsrv+/sxk4xyknCajml91bx/Ue27BZ0ehjkbUBJz2ZyRBv1VIUPzhTAhP/7iBNAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},v={src:"/_next/static/media/image6.56e69e77.png",height:512,width:683,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAVFBMVEWJfH+KgYyKe3BzamtsZGt0Y1d2coZOQEBoYnx5c284KS9AOj2YiIJYT02yqKKflZtLRlV9dnxZWmhjaluAckO2oWmCc15mXltgWFSPk5R1d3RkWVwuur+gAAAACXRSTlP+/////////vt3QHaEAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBCQLAEAwEwEUS4qYHbf//z87ADHXWAnhePc4rZbTOxHdkSInfXlTQks/qzITvtQpJ+AE4GwITl4Ei3gAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},p={src:"/_next/static/media/image7.ca8230f5.png",height:509,width:678,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAS1BMVEVgXnJRRDeKenS7oIaUf3WPho5iVlOBe6GmmIpMS18+QmdzanQ5NS5vZV3dxJ5TOUZdWmNtZG1KUG2UiaWIfo+xl49rU0A/PDxua4U/xkwKAAAACXRSTlP+/////////vzpJOMnAAAACXBIWXMAAAsTAAALEwEAmpwYAAAANklEQVR4nAXBhQGAQBADsJ5CX3HYf1ISLKer32aI8FqvUOw48kl+KGVKEye49SY2EiDfPnT9AS3EAbuBihn+AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},E={src:"/_next/static/media/image8.19fc6c56.png",height:509,width:679,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAXVBMVEVNT2ZYUFpsbIYYY60JdeVGODpwTi01IRspNUkkIip3irNYb5wVY7UNU6NOQkdgQSZmc5dJW4FdRCZlUj6omJlYYoJmNiZagrDPO1F6IEFsmcY1cbEIbM40hddBZI9c1fHCAAAACXRSTlP+/////////v4HKoILAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAO0lEQVR4nAXBBwLAEBAEwMUpp6eQQvL/Z5qBzgLNV0ALKYMPG/bjvGJUCsbc9L1EKP1J408T1jI7ZrcANz0CQ1UyGR0AAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},w={src:"/_next/static/media/image9.6f789c15.png",height:513,width:684,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAUVBMVEXJpnxBLyqDZ0qNakUsK0SoknOymHbDspSZe1mWf197Z1HXr366poVdT09eKQVpPxcyEQOqi2iUe2FPSlQpIC/An3dtW1FCNzpTGgAyHiRXQzmyh+wmAAAACnRSTlP+/////////vv+YqU32QAAAAlwSFlzAAALEwAACxMBAJqcGAAAADdJREFUeJwFwYcBwDAIAzBDDKRZ3fP/QythsojmLTCkf1dRw/PyuGWZMYTciya4k92gwLmtqDX/NGsB1lzQQv8AAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},q={src:"/_next/static/media/image10.7410d5ba.png",height:512,width:912,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAP1BMVEXb4+Gmr6tdVUplTiifqaidinejlXV2bVuQYyaNmJdXPB/N2d5TSTmToaWgr7OIgnWAfW7Bx8PV29elh1KDdFqVhj9gAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAKklEQVR4nAXBhQEAIAwDsG7McP3/VhIkz7kLTYRVADCEDjqyFaU5r/v4Aw+qAP2ZLNmgAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:4},f={src:"/_next/static/media/imagemobile1.462f9ef3.png",height:195,width:260,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAXVBMVEVSaJMQGjyAf36Ng2dLZ6h6fYGVj30hIihhbJVteqVuY1hZWl1ISUp8eGNnd52PjsJEXImJd2tDW5VfdrlvicR2e69Tb59qbomknKobKENyepG6qWtgYmhdZXxddo6bB1k7AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBhQHAIBAAsUMfrUFd9h+zCXvALjYMGB1bj85z12NctTek/LExJ0p9zonsuF4RpaT8OjQCNGYl1YMAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},U={src:"/_next/static/media/imagemobile2.d9687dfe.png",height:194,width:259,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAXVBMVEUkIioYY614i7NYb5xwTS1YUFtsbIZJPD4KduZNUGcVY7UqN0sNU6NgQSZJW4Fmc5hdRCZmUz6omJpYYoI8IhgvIR9bgq/PO1FnNSZ5IEFtmMY3cbEIa801hddDZY8oO2OuAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBBQKAMAwAsZu2Exju8P9nkiAS0mxbQkKMzrqeYZy2nI3B+13fS5XuOOv91YdlhQLlBzVfAi8KNGgQAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},V={src:"/_next/static/media/imagemobile3.352c2662.png",height:196,width:261,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAUVBMVEUsK0TWr366poU+NDSCZ0p6Z1GnjGzHpXuvmHqNakVVSk+vkW5eKQXAr484IybFtp4xEANpPheZfFlhVFSUemApHy9sW1BWQjiWf19TGgBLMyq3J6tdAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBwKAIAwAsQNKW5HhXv9/qAmPtxY9DupX7iuL8nbOlJaV2qFkmZgddjXB7FALYfsBN4wB1QRaCqkAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},B={src:"/_next/static/media/imagemobile4.9ba42b69.png",height:195,width:261,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAWlBMVEVlWk2Vj3JVTEqYi455cGluXkZcUktTUmxyYmVtanNURjhLPy15cXyai4M7MjB/eYRcWF5/fm9IPj+TiHyDbFLPsFOQfXZ8aWBIRWSZgGnNsJgYHC9jXG/Rs6PF3tv4AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBQKAMAwAsZu2U9zh/98kwS0TpmyBth9ziGtGtUfsCNdjjdfkyd3VVtNAub/3FJEfN2ECLA7GVNQAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},x={src:"/_next/static/media/imagemobile5.9cf16e02.png",height:196,width:348,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAARVBMVEXEz8+SmJSpq6KJdE2dimqHl57L1dR3gH7V395hVkN+eXXl5+JnYFS4wcF8io2Uf2J3YU5ANiY+RUa3uKiTd0WWcVlDOi0ZiJ58AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAMklEQVR4nAXBiQGAIAwAsQMKbRVQHt1/VBKqas85XKQGxRCewm2Y8AaNS+LHcJ9p+38AGF4BR71Wse0AAAAASUVORK5CYII=",blurWidth:8,blurHeight:5},R={src:"/_next/static/media/imagemobile6.11ec4a82.png",height:195,width:261,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAATlBMVEWNhI5gX3M+Q2duZV1iV1OVfnVxa3pOTGGKfnhRQzc5NS69o4fdxJ5UOUVdWmNKUG1tZG2Be6KUiaWKdGprVECxl4+0l4NBRFY+OzummIqajxHyAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBwKAIAwAsQNaWoaCW///URO2XQ+97gb4zODMMOQUy8S4lFrUsDXV0roQ7HlT9+8HMEkBzHVNW98AAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},C={src:"/_next/static/media/imagemobile8.af20a608.png",height:195,width:260,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAATlBMVEV5dHWEdWV1cWxvaGiJe36KgoxqZHd1Y1dkW1l2coaYiII4Ki9SS1KzqKNJPjdMQ0+flZw8OENuYWhbUU1iaFqOf3NZWmi2oWmAcUKPkpRQsyVhAAAACXBIWXMAAAsTAAALEwEAmpwYAAAANklEQVR4nAXBhwGAMAzAMHcm6WbD/48ikcC7K0Y+/Pk+W2FNCeKqYK2q6n6wxiyEdDNa75Yt/zDoAea6pa1FAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},M={src:"/_next/static/media/imagemobile9.9b6d317f.png",height:195,width:261,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAS1BMVEXQ0s/V1tS/wL6mchAnJB2FXCc7Lx7Hy810cmiIeF1YPxnGztWdUiJAJxFKOiOGiICNYxpfYWC5qZS2trCYaBhbTDh9fn+CWkF1QxoVd8v3AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nBXGRxYAERAFwE+jEyYw4f4n9dSqYPaIiBwgUmcAOKc67+WsgeMIqPWL71VutL/1XlJaK8MBvaLUspUAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6};var X=t(45173),W=t(36056);t(50967);var S=t(17577),D=t(52210);function G({language:A,event:e}){let t;let{t:G}=(0,D.$G)(),[j,I]=(0,S.useState)();e&&(t={sector:e?.versions[0]?.sector,country:G(`country:${e?.country?.replace(/\s+/g,"")}`),countryConcerned:e?.versions[0]?.countryConcerned,organiser:e?.versions[0]?.organiser});let L=[{title:G("event:events.eventLibya.title"),eventDate:G("event:events.eventLibya.eventDate"),postingDate:G("event:events.eventLibya.postingDate"),exactPlace:G("event:events.eventLibya.exactPlace"),link:"Libyan-French-economic-forum-2025",type:"events",image:W.default},{title:G("event:events.event11.title"),eventDate:G("event:events.event11.eventDate"),postingDate:G("event:events.event11.postingDate"),exactPlace:G("event:events.event11.exactPlace"),type:"events",link:"leap-tech-conference-2025-riyadh",image:s.default},{title:G("event:events.event2.title"),eventDate:G("event:events.event2.eventDate"),postingDate:G("event:events.event2.postingDate"),exactPlace:G("event:events.event2.exactPlace"),link:"pentabell-salon-sme-and-european-microwave-week",type:"blog",image:n.default}],J={sector:G("eventDetails:sectorValue"),country:G("eventDetails:locationValue"),countryConcerned:G("eventDetails:countryConcernedValue"),organiser:G("eventDetails:organiserValue")};return a.jsx("div",{id:"event-page",children:e?(0,a.jsxs)("div",{id:"event-detail",children:[(0,a.jsxs)("div",{className:"custom-max-width",children:[a.jsx(g.Z,{t:G,infos:t})," ",a.jsx(r.Z,{htmlString:e?.versions[0]?.content}),(0,a.jsxs)(l.default,{className:"more-events-section",container:!0,rowSpacing:0,columnSpacing:3,children:[" ",j?.length>0&&j?.map((e,t)=>a.jsx(c.default,{eventData:e,language:A,isEvent:!0},t))," "]})]})," "]}):a.jsx("div",{id:"event-detail",children:(0,a.jsxs)(i.default,{className:"custom-max-width",children:[a.jsx(g.Z,{t:G,infos:J}),(0,a.jsxs)("div",{className:"details",children:[a.jsx("p",{className:"heading-h1",children:G("eventDetails:aboutEvent:aboutEvent")}),(0,a.jsxs)("p",{className:"text",children:[a.jsx(h.default,{href:"https://www.businessfrance.fr/",children:G("eventDetails:aboutEvent:business")}),G("eventDetails:aboutEvent:description1"),a.jsx(h.default,{href:"https://misa.gov.sa/",children:G("eventDetails:aboutEvent:ministry")}),G("eventDetails:aboutEvent:description11")]}),a.jsx("p",{className:"text",children:G("eventDetails:aboutEvent:description2")}),a.jsx("p",{className:"text",children:G("eventDetails:aboutEvent:description3")}),a.jsx("p",{className:"text",children:G("eventDetails:aboutEvent:description4")}),a.jsx("p",{className:"text",children:G("eventDetails:aboutEvent:description5")}),a.jsx("p",{className:"heading-h1",children:G("eventDetails:eventProgram:eventProgram")}),(0,a.jsxs)("p",{className:"text",children:[G("eventDetails:eventProgram:data11")," ",a.jsx(h.default,{href:"en"===A?"/hr-services/":`/${A}/hr-services/`,children:G("eventDetails:eventProgram:data12")}),G("eventDetails:eventProgram:data13")]}),a.jsx("p",{className:"text",children:G("eventDetails:eventProgram:data2")}),(0,a.jsxs)("ul",{children:[a.jsx("li",{className:"text",children:G("eventDetails:eventProgram:puce1")}),(0,a.jsxs)("li",{className:"text",children:[" ",G("eventDetails:eventProgram:puce2")]}),a.jsx("li",{className:"text",children:G("eventDetails:eventProgram:puce3")})]}),a.jsx("p",{className:"text",children:G("eventDetails:eventProgram:data3")}),a.jsx(X.default,{slides:[m,d,o,b,u,v,p,E,w,q],options:{},slidesMobile:[f,U,V,B,x,R,C,M]}),a.jsx("p",{className:"heading-h1",children:G("eventDetails:moreEvents")})]}),a.jsx(l.default,{className:"more-events-section",container:!0,rowSpacing:0,columnSpacing:3,children:L?.map((e,t)=>a.jsx(c.default,{eventData:e,language:A,isEvent:!0},t))})]})})})}t(70580)},48967:(A,e,t)=>{"use strict";t.d(e,{default:()=>j});var a=t(10326),l=t(16027),i=t(90423),n=t(61421),s=t(62445),r=t(33487),c=t(74345),g=t(87166),h=t(36056);let m={src:"/_next/static/media/1.d4f64b02.png",height:486,width:647,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAV1BMVEVpZmGYm5paVkg3Oz0cISRjX1FSS0QsLzNNRU9DQD5ufo9CRz8KCwpJWWJAN0WNi4N9eXF9m44TOSpKi3AsI0Nqm4xnVlxXlmx4fGcaKTQrk2lsVnpIY28V/tRuAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBQLAIAwAsUNbbAbz/f+dS5hSscEGGPrCMhsOzXtfTUbSdT5bFLyjfSUKUuvd1PkfNSoCCKElNVoAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},d={src:"/_next/static/media/2.b907b0d5.png",height:486,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAV1BMVEWPf3WlprTIiYeWioPHr6WnnZ6wvNO6xNi+vcxzY12Og4makZtuWVM1ND9mVEy5q6afinibn62ys8O8qbWWiIu+d3Z/bWolJzDEx9ZLQx9QR0ZeWGOmlIgHYwZdAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBQKAMAwAsZu2neAO/38nCaLe76socrkaNS/kksqxuRnr9T3DmLh74yFONOMj2PADNe0CCg1off8AAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},o={src:"/_next/static/media/3.27264f47.png",height:486,width:364,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAVFBMVEU3UA97fH4vNDeKjIo/XA0tRBQ0RglCSEq/v6lGYhFYYGiSlatzfnPS0bh/hIVJYhw9VCFPbw6ajGYVIRJRZTY0QTJpb3qeno9AWIk9V4tmamNQVVflGeshAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBQLAIAwAsUNbdALz//9zCZdqa0ruuboPc95Pf3GmFLHsIvWwxBFYJ4GIXwC/pfQDPf4B8zYDoi4AAAAASUVORK5CYII=",blurWidth:6,blurHeight:8},b={src:"/_next/static/media/4.8582d9e5.png",height:486,width:365,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAUVBMVEVWU1eFd20tMDl3V1c+PEFjWlpBQEl8a2FHSk9KQUKUgnQjIyytn5Obl4qPjYbT1skNESFZTUnAt6lwamXm5eCVlpevSzmminB4eX3Yt6NaND7zPpLEAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAN0lEQVR4nAXBhQHAMAzAMBeTFMf0/6GTcLL7tZCS1bahgpUOwcVo5CgaPjyjXzfLc8x3ojWPs/0u2wH0w1LJ+AAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},u={src:"/_next/static/media/5.1c02d7fc.png",height:486,width:488,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAe1BMVEVSTUqhemnU3eA7QEcDKVBbWWF0mr48ODq0nJDY08+gpK99am68qqHKztNxZViHenKknJyTlJBwa3EUMVlUQk6Jn7JSV1dgepgRGzKtusK0qaO+m5Fyh5u8nX5XbYLH09qLhny2nHrBn2Ser7y3trzwyX2sl4BKirdIbYcmOTgNAAAACXBIWXMAAAsTAAALEwEAmpwYAAAARklEQVR4nAXBhQGAMAADsDIfc9zd/r+QBFrHYNKgAHyvO3kFUC/c1FEkpa68b0s8lqw5qRnuPdpRC4bDyywzhcS2hFk2HD+M8QPmiWoLHwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},v={src:"/_next/static/media/6.4485373a.png",height:486,width:365,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAXVBMVEWZkom9j1xsZ2l3cF/Duaell5WBgYhlWlKEfm9CPz5ORkqcgIe6qJu1intUWVBrbGKujoSejHaNf4F9dGN4doVyWVDRsY9HPS/Bq33QpGyPipc/OCspIyWSiXTVwZvUxKrhAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBBQKAMAwAsZu2HTLc4f/PJMH6qkTabogkpibBRV68t8wmVp4DtxbZT0aRcDtU5/B+PzUJAihIETPwAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8},p={src:"/_next/static/media/7.78d75664.png",height:486,width:365,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAWlBMVEVeYGumpcQCG/UHJOVMSFCKipTIyMoPIN/UzLVgZHU7Q3IQIL86Pl0lO+GwqZ/f2shQVGV3cWy5t8NLY6RZapcBDeIWMc1wcMUWIV4+PUlRbbc+UMZKT+Bpa3xyqtOIAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBhwGAIBAAsaM+VRTsZf81TYjce85A1SJ8POY98K4kqcz2KpvGLukUQ5hcXAct+K7UDzxLAh3m+2+OAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8},E={src:"/_next/static/media/8.aec19c18.png",height:486,width:414,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAICAMAAAAC2hU0AAAAbFBMVEVIPDdXQi2Ejo+tcz1iTD+fbDkqJTJ9WTntmC1zVkSVc0dzc18yMDGnkTeqgELHcCXAfToZGCVsY1W4eTRPU0woIR6iub19gH59hYU/REubrLJrhl9siT9mY0g5Kyb/nyrAjSm4bzCIelSuk0d3UZ8/AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAQElEQVR4nAXBBQKAMAwEsJu2c9xlwP//SILz9eopBUeqzHxjDyl/lCG3cMFGzNMSsY4YZAfRAkKTMVaDvOob535xlwL+t2UkPgAAAABJRU5ErkJggg==",blurWidth:7,blurHeight:8},w={src:"/_next/static/media/9.e6bc395e.png",height:486,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAS1BMVEWcmJFoZ2ipoZkyPEt2b3CnqbWin5eRlqSAe3efpa6xqKR7eoiMkphPWXDIvrS0rqVTVFmMbleMlq5tcXitlH1YWV5pXE+ijoRodIZaRDk4AAAACXBIWXMAAAsTAAALEwEAmpwYAAAANklEQVR4nAXBBwLAIAgAsVNBwNG9/v/SJswxP7MwTEO19QN5XXouhee6fU9b4gwZ7mSoVNq6/DJLAbgkkXPwAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},q={src:"/_next/static/media/10.fe220a97.png",height:486,width:537,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAb1BMVEUwd8JghqV1g45bmaeXdHBLcJOTjoosdMFdUU8xesmYgHucUU1rWVGUZ2Q6ZJU2crCCiHxaeI7At7PSXlikm5eHnKvTSUpDbZnlZ1+KgHvqVFKFbnCFS0myeGQtMjWaiYdRQ0dga2qifm24dW+ojoMIWQkQAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAQElEQVR4nAXBBQLAIAwEsENbbMLc9f9vJAEcEQGAq+qmhZlgtJCLUApWD9vhH4ncW778+2Nc9/OOMaCbA6fEXwFK+gMF3zp/HAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:7},f={src:"/_next/static/media/mobile1Gitex.ed6da4c8.png",height:197,width:263,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAV1BMVEU5OT+XmZlSTEVaVkgtMTVFSkdjYFEiKCxDQT1qaGF7emxPj21IXmsLCwuNioJ0gYpPPk9/npAVOyxqY2lTSlVHWV4qI0IlkGZrm4wWHSNrfphnVVxsVXmCi5ZKAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBBQLAIAwEsAMKbZEZMoH/v3MJIOKCC4pxrKpxM5jte9JpdpDEdF+W8EJ79pZBpXD24B86PAIOgzoTdwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},U={src:"/_next/static/media/mobile2Gitex.320566ee.png",height:197,width:262,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAWlBMVEVyYl3Af36LgHuvu9K7wtbFraSejYK+vcyhpLKThoaonp6jlJA2NUC4xtuys8NlU0y8p7S3qqUkJi99a2fNjoqVj55vVEqVgG9KQyCPe3KJgYpTSUddV2LDxtVx8dq/AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBQLAIAwAsUPbAnO3/39zCVFW58ZOiJ/WJKEn3MUWU4W2vXOeCmfjenzK7BzVG8MPNt4CGnJ6j34AAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},V={src:"/_next/static/media/mobile3Gitex.81cf892c.png",height:196,width:147,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAVFBMVEU3UA80RgmLjIl2eXpGYRE/Ww0uRBQxOTV+g4S/v6lAR0WQlKtTWFzS0bk/WIpPbw48UyBKYhybjWYVIBJHS1NTZjdqb3p5hW2fno9maWNbZnBIUEzsRhERAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAN0lEQVR4nAXBhQHAMAzAMBeTFMf0/6GTeMx6N5p+wTf0GkNffKi1CNtRbhHcnllOMo60AmnG+AM8HgHh7eaRagAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},B={src:"/_next/static/media/mobile4Gitex.8dc7363c.png",height:196,width:147,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAVFBMVEVWU1d3V1eUgXRFOz09PUNjWlqFd21HQ0d7amFHSk8iIyytn5KcmIuPjIXT18kvMDoOEiJXTEjAt6lxamUrLzani3Hn5+KUlZZ4eX3Yt6OvTDpcNj/l9UsyAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBhwHAIAzAMAMBElb3/v/PSiTd9sUTYy51xZTgG7gUJJNFzX1M9PYezPc5noEV6Vf9ATDhAgB7fu4JAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8},x={src:"/_next/static/media/mobile5Gitex.3fbee9e0.png",height:197,width:198,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAgVBMVEXW089vaGmfeWigpK9TR0lAPD5RUE4IKlK5nJLP2t97anCyucCUlJGumYc2NTNeYF55mb4zPEyMo7ZRV1difJsUHTNaUmK0qaN0iJ69rKSlmaGKhXtYboN1ZlOEfne7m33CoGW1nHufsL7uyXzDytO7qJ1ynL5IirejoJlIbYeNdmxTGGBDAAAACXBIWXMAAAsTAAALEwEAmpwYAAAARUlEQVR4nAXBgwHAQAAEsHu7tq39B2wC4PmsrzU4f29sFYcU5DqaMoUP2jFXKITTGGYyhX3tWo1EYRkFpZZE6IdpFnksf5MiBASb/4IiAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},R={src:"/_next/static/media/mobile6Gitex.50cdb5fb.png",height:196,width:148,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAV1BMVEWZkohubGWklpS9jlydhn+GgXJ+d2ZlWlFCPz6+r5yBgYd2bl5ORko/NyqvjoW2iXtXW1LGvrBqZGuLfoDRso94doRwWVDWw5yRh3LSqG6QjJktJyi8pnsNV/FMAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBBQLAIAwAsUNbbAbz/f+dS9DFBBJpnlYs3Vt4yPtmNDNE6/vhjirtxouU5ggxluv8ATQ9AgjOmS/qAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8},C={src:"/_next/static/media/mobile7Gitex.81432e89.png",height:197,width:147,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAYFBMVEVeYGsRIL8IJOQBGvbQysGKipSmpcNjZncSIt1KR08GE+M6Pl3f2cevqJ/Vy6xQU2R3cW1JYaZXaJW4tsPFys8/R3A+PUg2P3JRbLc/UMZzcsUhL+AqReNJTeAWMc4WIV+vnRj+AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBBwKAIAwAsWO2gIp7j///0gRhf40BbpcV4bDPyRzKphc1rSU7Ur98aunaIONA4/0U4w8+5QJClJw5sQAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},M={src:"/_next/static/media/mobile8Gitex.e8a8e372.png",height:197,width:167,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAICAMAAAAC2hU0AAAAb1BMVEVEP0DFiDNXRThyc2EnIiieazlPPjUwLDHumC5qT0B/XDuGkJKwcTeOeEyqgkC9cCt6XUwYGCRcRCacjUJoZktoYFRvjD6ze0FQVE45KyR9hYeqkzGiuL1uiF5+gn+SbUeZqa//oSzLcCOqkUt6VDddfToKAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAQElEQVR4nAXBBQKAMAwEsJu2c9wd/v9GEnQpxVIeDK1g5hu7WvP7ZYRLzSDCsS0EaEyht7K2aKQ3Rjv4c4zCVT9uXwMiiinonAAAAABJRU5ErkJggg==",blurWidth:7,blurHeight:8},X={src:"/_next/static/media/mobile9Gitex.4190f636.png",height:197,width:263,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAUVBMVEWfm5JUVVqnoJeDfX2gpq56dHMxPEyRlaSxqaNxbW6Lk6WnqbVrc3/FvLGGg3+loZuXlZSMkZJ6e45pZmWNb1etlX2oqbehjYNNVGxRYHZoXE4ZcsffAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBwKAIAwEsANaOlhOUP//UBM8/lZxEkihWfRw2GJOMWd8d+DrDIZKtqe+DQCIUG0/N5sB2ZewxDsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},W={src:"/_next/static/media/mobile10Gitex.f0775b69.png",height:197,width:218,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAbFBMVEUvdsGZj4uNTUq0dGUweslth5p+hYFQc5KXc3CUf3peVFM4ZZg3crCNiojAubXRXVdYgKrVSUminJiDm6niZ15Ca5mIbG7qUk9WTElWnqIwNDdjlK5cbWxTREeQXltsXlGniYCignJpVFGiTkumuSs1AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAP0lEQVR4nCXBRQLAIAwEwEWTUKHUXf//xx6YASwyW5SeQAOIGydu2VBxNxrZPepWzUqeG2nS6/FeH2KvA5/B/EIqAsbly1fbAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:7};var S=t(45173);t(50967);var D=t(17577),G=t(52210);function j({language:A,event:e}){let t;let{t:j}=(0,G.$G)(),[I,L]=(0,D.useState)(),J={sector:j("eventDetailsGitex:sectorValue"),country:j("eventDetailsGitex:locationValue"),countryConcerned:j("eventDetailsGitex:countryConcernedValue"),organiser:j("eventDetailsGitex:organiserValue")};e&&(t={sector:e?.versions[0]?.sector,country:j(`country:${e?.country?.replace(/\s+/g,"")}`),countryConcerned:e?.versions[0]?.countryConcerned,organiser:e?.versions[0]?.organiser});let H=[{title:j("event:events.eventLibya.title"),eventDate:j("event:events.eventLibya.eventDate"),postingDate:j("event:events.eventLibya.postingDate"),exactPlace:j("event:events.eventLibya.exactPlace"),link:"Libyan-French-economic-forum-2025",type:"events",image:h.default},{title:j("event:events.event11.title"),eventDate:j("event:events.event11.eventDate"),postingDate:j("event:events.event11.postingDate"),exactPlace:j("event:events.event11.exactPlace"),type:"events",link:"leap-tech-conference-2025-riyadh",image:s.default},{title:j("event:events.event1.title"),eventDate:j("event:events.event1.eventDate"),postingDate:j("event:events.event1.postingDate"),exactPlace:j("event:events.event1.exactPlace"),link:"franco-saudi-decarbonization-days",type:"events",image:n.default}];return a.jsx("div",{id:"event-page",children:e?(0,a.jsxs)("div",{id:"event-detail",children:[(0,a.jsxs)("div",{className:"custom-max-width",children:[a.jsx(c.Z,{t:j,infos:t})," ",a.jsx(r.Z,{htmlString:e?.versions[0]?.content}),(0,a.jsxs)(l.default,{className:"more-events-section",container:!0,rowSpacing:0,columnSpacing:3,children:[" ",I?.length>0&&I?.map((e,t)=>a.jsx(g.default,{eventData:e,language:A,isEvent:!0},t))," "]})]})," "]}):a.jsx("div",{id:"event-detail",children:(0,a.jsxs)(i.default,{className:"custom-max-width",children:[a.jsx(c.Z,{t:j,infos:J}),(0,a.jsxs)("div",{className:"details",children:[a.jsx("p",{className:"heading-h1",children:j("eventDetailsGitex:eventProgram:eventProgram")}),a.jsx("p",{className:"text",children:j("eventDetailsGitex:eventProgram:data12")}),a.jsx("p",{className:"heading-h1",children:j("eventDetailsGitex:aboutEvent:aboutEvent")}),a.jsx("p",{className:"text",children:j("eventDetailsGitex:aboutEvent:description")}),a.jsx(S.default,{slides:[m,d,o,b,u,v,p,E,w,q],slidesMobile:[f,U,V,B,x,R,C,M,X,W],options:{}}),a.jsx("p",{className:"heading-h1",children:j("eventDetailsGitex:moreEvents")})]}),a.jsx(l.default,{className:"more-events-section",container:!0,rowSpacing:0,columnSpacing:3,children:H?.map((e,t)=>a.jsx(g.default,{eventData:e,language:A,isEvent:!0},t))})]})})})}t(70580)},51335:(A,e,t)=>{"use strict";t.d(e,{default:()=>M});var a=t(10326),l=t(16027),i=t(90423),n=t(61421),s=t(76297),r=t(62445),c=t(33487),g=t(87166);let h={src:"/_next/static/media/image1.09b64b02.png",height:468,width:882,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAARVBMVEWmteVRgMSZmZyJhXHP1vh1gYmCmNeSla1jh8eAirVLWpV3leRnYW5XWX9WcMehpLpsfLhdd75AeOCjyf67yuuImOfu8/u6oWloAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAK0lEQVR4nAXBhwEAIAgDsKog4N7/n2qCPtw05gA9QDTxcJtejhDoutRSLR8TdQEmsM9YqAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:4},m={src:"/_next/static/media/image2.d3d55633.png",height:468,width:350,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAS1BMVEVLT1tzdXZlZ29WXGRWVlvAs6FwkY6Rkoqs4OGbrKNET1xvbG8tMkJ3mZWh0dE7PkeZs6jXy7dugWC0q5ybxsRzg4SfxbmIrqxtiHx80HnGAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAN0lEQVR4nAXBBwKAMAgAsesASqdb//9SE96hOpQ5r1IKj5v7xpGgZb5Mj5W7k1blzC3EwC4iZj82RwG9KdGcLgAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},d={src:"/_next/static/media/image3.abaaf2fb.png",height:468,width:833,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAQlBMVEXUuq7GrJ0wO1m8fKhwZG9+YHBoVGXAnXGugX+mWWmbbGnLmHx0YoFtYn1NSFLQtJ6jgGK0lWHDiH6ZcGVZVGU1RHA7ALpFAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAK0lEQVR4nAXBhwEAIAgDsIKA4p7/v2oCEOIaKQKH9kxeoCZ21TOk8+MW6gcSRQESlDJySQAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:4},o={src:"/_next/static/media/image4.15046ebf.png",height:468,width:927,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAANlBMVEWBcl2wpo+Mh26PhIGAdnhfWl9RUjpKRj9pV0l4Xkt3b2ltWVNARSJVRECXhWallXaSk4TDtpyTQ57bAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAKUlEQVR4nGNgZGFlZ+dhY2JgZuHm4ODgZGLgYmXnZePkFGAAAT5+RkEADm0A3GvHH/YAAAAASUVORK5CYII=",blurWidth:8,blurHeight:4},b={src:"/_next/static/media/image5.d97280e6.png",height:467,width:378,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAUVBMVEW7rZuqnZGumYWQiYF5c3NxcH/Hp4O6mHjSwqrdzbihmZFnZ3ZjVlWLZEvdq3nQxLbIvbHFvrDFs5/TuZFlZGlEQ02GemlSVmqshGVFKhepgmicMPUpAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAN0lEQVR4nAXBhQHAMAzAMBeTFMf0/6GT6GM8knm7fJJpKaVrh3rX6NHYzqJYCQeKuRBXY8O7Zf47iwHq3hHIggAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},u={src:"/_next/static/media/image6.5ccc1b9f.png",height:468,width:351,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAYFBMVEWlgmF/b1wmIBzhvZBsUjK6jEyHbVTUkDC7nHvFqYLqyKF4X0azvb+6kGrOsZa+pId2WT6wknD9yo9KPzTdq26rl3xCNCeun4nBxcuQkI8EBAh4URhjZ3B4aloXExbPrnAJzcn/AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBhQHAIBAAsUMfq0Ld9t+yCTDSR3zOQ+goJX5TYD82w8L9qlQV+nzSqrGXM97iRKTNPz6xAkbLCinDAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8},v={src:"/_next/static/media/image7.38c63d94.png",height:468,width:623,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAQlBMVEUlJBNtaVMUEggvMRJsZDRLSjxSUSUMDQZdV092bj5gXzFzZkFDPzFfYF6clFxFRheDgFxoV0R+akSQclBna15WTUVRftiNAAAACXBIWXMAAAsTAAALEwEAmpwYAAAANElEQVR4nAXBhQHAIBAAsQNekVLdf9UmuLvXCrQNTDMkxz0e6aTYFV9AzxIzlnKcRZuu9wci6QF564lkpwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},p={src:"/_next/static/media/imagemobilelibya1.c94c5777.png",height:197,width:391,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAQlBMVEVpV0mMh22Cc2KAc3OwppBwWk17X05JRj9eW1+Bc1qOg4B3cGp/eH1UU0R/cF9ARSNUQ0BQUzLDtpuWhGaklHaSkoVfPA7fAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAKklEQVR4nAXBhQEAIAwDsA6Y4fr/qyRI1VTHIuTIAJhQTGcTeQjd/dy0PxESARdb0XaYAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:4},E={src:"/_next/static/media/imagemobilelibya2.e8ec763a.png",height:197,width:148,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAS1BMVEVIT15ydXZXXWRJTVZXV1uq3uCXraS+saFxko5vbG0wNEWPkYmdxr+alY87PUagq6JeY3TWyrZxg4Si0tJtiH2ezs6Irqt6m5htgGBhMI2+AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBCQKAIAgAwVVBQC276/8vbYbndXfnvIeqMmqvs/JlSgiX0DY4WskrmESKxG5mffkBNJQBsWuup4wAAAAASUVORK5CYII=",blurWidth:6,blurHeight:8},w={src:"/_next/static/media/imagemobilelibya3.ae542812.png",height:197,width:148,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAY1BMVEXUkDB2W0K4pIooIh2lgFzfvpeyvb67m3q2kW2lhmXXrW+GbFN7a1hHOi7MqIZxVC6+j03Ospd4UBePjo0YExcEBQliZm/Axsx6YUi9qHyEdmPuyaLlvIuql3y5jE5qUzr9yo/JO2VjAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBBQKAIAAAsZNOOxCs/7/SDaU6P/XEUoR1GHMGZzkWof3NvladE3JLLUuG+bniywiE7wdAsAKDeGWSXwAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},q={src:"/_next/static/media/imagemobilelibya4.d9898efe.png",height:197,width:262,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAQlBMVEVxaT5saVJ9bUJvYzISEQgkIQ9FQTQoKBZdVEoMDAVfXjBbW1lOTj1UUytkZ2CZkl2EgVxDRRY0NxRnV0ZPTxqQck/DcAl/AAAACXBIWXMAAAsTAAALEwEAmpwYAAAANElEQVR4nAXBhQHAIBAAsQNesfr+qzZBMlNElftSd1svDSpf61TWExFOp4SNPTnOMt3G/gEnLQGHFNTQgwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},f={src:"/_next/static/media/imagelibya5.e831da1f.png",height:197,width:351,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAPFBMVEV1ZXAvO1ewg4JuYX5pVWbGrZ2cbmfSvbLAnXDVtaSlWGhMR1F/Xm2fe16zlV82RnLLmHvDiH3AfahZVGXzl8h6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAKUlEQVR4nAXBhQEAIAwDsDIfLv//SgJXj5kUSNMzqG5AIK9XA1pZhS9/Eb4A9kklsgEAAAAASUVORK5CYII=",blurWidth:8,blurHeight:4},U={src:"/_next/static/media/imagemobilelibya6.8e9a0738.png",height:197,width:158,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAUVBMVEXNwbDCsJ1iXmKrhGbezrl7dHTIqIJsa3mjmpC7mHneq3qMZUu7p5SsloSVioGtoJONiYK7s6VEQk2vmoTTuZGHfGpmUkxzdYbWwKdGKhdTV2qPrbYsAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nBXFSRKAIAwAwQESE0BFcff/D7XsS1NLSXngqfnvtMuOnWCv6catsTuIL3EFabMihHFy+AA33wHK2XNLPQAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},V={src:"/_next/static/media/imagemobilelibya7.6cdc440c.png",height:197,width:371,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAASFBMVEWltOSLh3KZmJyDj73Q1/h0gIlRgMSRlK1jh8eCl9dqY3B3luVMW5ZYWoFXcchsfLihpLpddr19hK09duGlyf+6yeyHmejr8vs2T6hmAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAK0lEQVR4nAXBBwIAEAwEsENbam///6kEvcmYzAbhAOq8hWx6SeER1qWaS/wVfwE8oLgJ8gAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:4};var B=t(45173);t(50967);var x=t(17577),R=t(52210);t(70580);var C=t(74345);function M({language:A,event:e}){let t;let{t:M}=(0,R.$G)(),[X,W]=(0,x.useState)(),S={sector:M("eventDetailsLibya:sectorValue"),country:M("eventDetailsLibya:locationValue"),countryConcerned:M("eventDetailsLibya:countryConcernedValue"),organiser:M("eventDetailsLibya:organiserValue")};e&&(t={sector:e?.versions[0]?.sector,country:M(`country:${e?.country?.replace(/\s+/g,"")}`),countryConcerned:e?.versions[0]?.countryConcerned,organiser:e?.versions[0]?.organiser});let D=[{title:M("event:events.event1.title"),eventDate:M("event:events.event1.eventDate"),postingDate:M("event:events.event1.postingDate"),exactPlace:M("event:events.event1.exactPlace"),link:"franco-saudi-decarbonization-days",type:"events",image:n.default},{title:M("event:events.event11.title"),eventDate:M("event:events.event11.eventDate"),postingDate:M("event:events.event11.postingDate"),exactPlace:M("event:events.event11.exactPlace"),type:"events",link:"leap-tech-conference-2025-riyadh",image:r.default},{title:M("event:events.event2.title"),eventDate:M("event:events.event2.eventDate"),postingDate:M("event:events.event2.postingDate"),exactPlace:M("event:events.event2.exactPlace"),link:"pentabell-salon-sme-and-european-microwave-week",type:"blog",image:s.default}];return a.jsx("div",{id:"event-page",children:e?(0,a.jsxs)("div",{id:"event-detail",children:[(0,a.jsxs)("div",{className:"custom-max-width",children:[a.jsx(C.Z,{t:M,infos:t})," ",a.jsx(c.Z,{htmlString:e?.versions[0]?.content}),(0,a.jsxs)(l.default,{className:"more-events-section",container:!0,rowSpacing:0,columnSpacing:3,children:[" ",X?.length>0&&X?.map((e,t)=>a.jsx(g.default,{eventData:e,language:A,isEvent:!0},t))," "]})]})," "]}):a.jsx("div",{id:"event-detail",children:(0,a.jsxs)(i.default,{className:"custom-max-width",children:[a.jsx(C.Z,{t:M,infos:S}),(0,a.jsxs)("div",{className:"details",children:[a.jsx("p",{className:"heading-h1",children:M("eventDetailsLibya:eventProgram:eventProgram")}),a.jsx("p",{className:"text",children:M("eventDetailsLibya:eventProgram:data11")}),a.jsx("p",{className:"text",children:M("eventDetailsLibya:eventProgram:data12")}),a.jsx("p",{className:"heading-h1",children:M("eventDetailsLibya:aboutEvent:aboutEvent")}),a.jsx("p",{className:"text",children:M("eventDetailsLibya:aboutEvent:description")}),a.jsx(B.default,{slides:[h,m,d,o,b,u,v],options:{},slidesMobile:[p,E,w,q,f,U,V]}),a.jsx("p",{className:"heading-h1",children:M("eventDetailsLibya:moreEvents")})]}),a.jsx(l.default,{className:"more-events-section",container:!0,rowSpacing:0,columnSpacing:3,children:D?.map((e,t)=>a.jsx(g.default,{eventData:e,language:A,isEvent:!0},t))})]})})})}},39428:(A,e,t)=>{"use strict";t.d(e,{default:()=>D});var a=t(10326),l=t(16027),i=t(90423),n=t(74345),s=t(61421),r=t(62445),c=t(33487),g=t(87166),h=t(36056);let m={src:"/_next/static/media/image1.330e7574.png",height:486,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAbFBMVEW8qZOaiIS2o6GutsPv5M6Fk6HEgF+cgHDdwprf1sutr5n/9+edk4W9poyTfWG6nHjBuJHYmGaRhpxqXkjQr4Ojcmd+cYA/N0PIxcwRDxZsYmmZfX6ysrKGm7elkJWwoI6VlaVwhKWolq3Yx7r5LSlJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBhQHAIBAAsUMfatTdu/+OTeiDrYqYQRPyhBXB+7rUn6yoobv0uRucm5ifw9COi4rb/f48PQKruOUP2QAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},d={src:"/_next/static/media/image2.94ecd05a.png",height:486,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAb1BMVEVwWEnKwbKdnKhaRDZSQEO0q67FsKbRyL/myKjewaBuT1TKp5PTzc2UcVzGqpV/a3p8f2yPoqitqZ6ngIH5y7o+joHAm7Tj2r9He2loYmegcVnlycfv6uNufXvGQEffsbGtT0zW3dr/PTy3uNPOgn6rBVnZAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPElEQVR4nAXBhwGAIBAAsQOBBxv23nX/GU1ISKHwGcZEkdIHXFMPnco3Rlu1y/zeBG2V2/XH1Mt6XsfzAzlyAsxaM0UjAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},o={src:"/_next/static/media/image3.2ed0ab05.png",height:486,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAYFBMVEVucGmGgndeMEioqKHgors2Jy9HP00/P0PYoLN1cG/Pj6V7em4qHykqOUeTUnFHCBRyFiamVneKGzGESGe4kqKhiYiemZbDm6RMTEnCraxZdpm6ZYS1hp60j5KYhZxiECCkFlV3AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBBwKAIAwAsWO2gIp7j///0gRzOAq7kF59lrVAOO+cN+OIqt7PJOw1dJMNwjf2tWmj/DzXAkJ8LMF9AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},b={src:"/_next/static/media/image4.e977de2d.png",height:486,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAaVBMVEXb2NUYbnx4d3XgMzsVFlAufIURLWYSe42rp6bRzMVGXXtHgIKnppQBDWK5NUVaQ2YHPH2hSlU7ho29r53Iw70wSWU1LVKdqJ2HdHGAcnKHY1pFXl84lZxqMTzGSkzSOUK9urXdQ0ynXlExte1EAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBhQHAMAzAMBeTjpn5/yMnoVYfTcB5fdvSJ7jfdQ95MeDaoxMJM65uMjsaQxVLP/koP0x2ApBQNDH4AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},u={src:"/_next/static/media/image5.4528e0fa.png",height:486,width:486,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAflBMVEXGuKa/r57+++m8qJv05NNvXEl1Zljr49H78t/y3L7o28ni1Mk8hHsPjYFwv7eBnYkbpJ+tnIiYi4Cji32MdWTQw7LHwLtuuJzyy5tcs55Dd02XoL9BalOTkaswSjzHzNNgj6Tm0rEpg4owm4wJADYAaIE/n5hUW15Qy7cNPmDmixQAAAAACXBIWXMAAAsTAAALEwEAmpwYAAAARklEQVR4nAXBhQGAMAADsM59uLvD/w+SQEgpyiLPAEYIKhhoRWtHNYeyali05TCtndhmHJourkkSb/TB72n6PRhnH473On9qugQIu7wNGAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},v={src:"/_next/static/media/image6.4233556b.png",height:486,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAYFBMVEV/hpFeiNsgCQ1yd4pbXF6Bjb9saE95dWp8d3J8jNyHg2lvfLJoanl2cV6fopy4u7Z+hpyFm/xWZZtKUFo8R26Df35CR1ZBKSUreMuKioZVOTNzi+uJgIszGiI8fcVmTDL91hV6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBQKAMAwAsZu2He4O//8lCUkkx7aLVD3LrU3NOJjbLzIT5l5LgbVwFJXArN+5Pd7/OGACQDBISx4AAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},p={src:"/_next/static/media/image7.109eaa54.png",height:486,width:365,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAY1BMVEUcbXVrb3K5UV8jipoqj6GLcnZOhYsrYGc0cXosmKapc3cknLRkb1lJdmM7gopCe3nSTU8Ze4keJykRmbQheYxSSjc1X06KUVxCZXGWS1oFXmZKXWJcYG12YG2EfFGJgYpIkZ+AdyMhAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBhQHAMAzAMJeTDjpm+v/KSXQhvDljTYyfYmWqH2WXkhplXWbGivsYknguzJkcG23v/A9DNgJgWoAyrgAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},E={src:"/_next/static/media/image8.b2596b17.png",height:486,width:709,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAAYFBMVEXiy7AsHhvkson/1ZaYh4mQmqOEdm3t28IQCRHhl3PYwqwjFiqlx79cREatoZuKjaXPp5Lt2bpxcYFHSl1uXHGkbHL747yKbXDkqmWkrK7IxLnr4s91W1Twow3br5PPnFWevmxQAAAACXBIWXMAAAsTAAALEwEAmpwYAAAANUlEQVR4nGOQEGNnEBRgYmaQZ2Dg4ePk5GKQlRFn5WfhFWZgk2RjYRXi5mCQkmaXExXhYAQALT4CGF0wiusAAAAASUVORK5CYII=",blurWidth:8,blurHeight:5},w={src:"/_next/static/media/image9.161dd7c6.png",height:486,width:394,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAbFBMVEXPwqqjqMHunyPc0bSAaVHqyKTrs3+6rp5pZZWfm5evp6rr2be8r5Gwn3xxeaCbn63Tzrp+SXSwsMHRsq74NjPISEnFhoaju2mRfJnMzJvQt5N1fyHcpYpIS4qjkGNaRZaTeD8pHojtpD6Fql92XUR/AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAO0lEQVR4nAXBBQLAIAwAsYMBLXN33///uARX5FInOKoyZgRU4wJe/PSRilm3G9POx7kzdvq8gX5oLmt/PlgCsLxezAkAAAAASUVORK5CYII=",blurWidth:6,blurHeight:8},q={src:"/_next/static/media/imagemobile1.73df5d8c.png",height:196,width:261,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAZlBMVEW7qZCaiYSQlKN3iaLf18vDgF/ItIqcgG/v5M+ik46utcKwoZXfxaCtsJr/9ua6nHiRfF9pXkekcmaRhpzcv5XYmGURDha6o6Z9cX+GnLk9N0LXxbnBppmxsbGplqxsYWiYfX7Jxcz+GseJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBhQGAMBAAsat+BSjuuv+SJPgqtNHWMI1NIYjgnCcf8qD6Li/xSmizn5/ZEsM6K/vq+wc6YgJ7SiIE9AAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},f={src:"/_next/static/media/imagemobile2.d6d70d69.png",height:196,width:261,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAbFBMVEVVQT7U1NN0fXSdnajRy7vLwbHeuKvIqprlxqZzWktrUkZvT1SWdF76zLvnysiicltpYmbAm7WOoajEt6ukfn+Aa3vPwcbawZpJfGqsqZ6xq7Tm3L8+jYG5rajs5d/FQUj/PTqtUlDPg321ttHpFeomAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBhQHAIBAAsUMfatTdu/+OTSDLYyyCAIO1EjyytE3p1Ml26D7Vn2Iyq+u0eZn3sfLPdf85JgKr0TKKgQAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},U={src:"/_next/static/media/imagemobile3.15732b24.png",height:196,width:261,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAY1BMVEU/QUa9lqMsMTzPjqWfkY/gorpqbml+e3HXn7Jvcm91b2hAJSumV3iKGzBJCRZ0FicoHymRUXCBSGaJiXpnLUClpJtLTEm1j5K6ZYVhECBNOEurq6bCrq20hZ1adpmYhZxXM1BcWcriAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBBQLAIAwAsUNbmDB3+/8rlyDXmSUtkF9dvZeIfW7nzDhzqIZgYuLb6n6YKtib0nYWfkH/AlddQPzKAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6},V={src:"/_next/static/media/imagemobile4.687bb69f.png",height:195,width:261,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAaVBMVEXb2NXXNj59dHSsqKihppgtfIUYbnsTe43Ry8QaJGLgPEVHgIJFXXpzd3MBDWGhS1a4Nka9rpw8ho0HPH1XQ2bHwrsvSWU2L1INDksLNGNGXmCmoZ+HdHI4lp2HY1rIS0y9urWnXlFtMj3gUqDIAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBhQHAIBAAsUMfqFB333/IJhhtnj0A3/1eRxtAJb25wo6kfHYizqKGvqwm72liHedllR9F2wKSfgQfvgAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6},B={src:"/_next/static/media/imagemobile5.0ce739da.png",height:196,width:195,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAhFBMVEVvXkzNwrjv38lDclHj1sjJuqjDtae+rJz89eH05NPp383z27s7hHsPjYEjnZGunYmXiX+ji32LdWR6aVzq5dbyzJxvt5v//u/47dtds52Wn78wSzxrwLfGy9KTkqxhj6WBoYgqhIrm0bCFmIp3vrZQyrcMQGA+npcAZoAep6VUXF4JADa5ENm3AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAR0lEQVR4nAXBgwHAQAAEsGvfrG1r//2aAMiQJnEEJrhgAQ8hcxnQqlZQVLeD1Aqm0KNYDUXZ9NO87ReIs4t/zheks84f9/cDc6kEQJeh1gwAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},x={src:"/_next/static/media/imagemobile6.0a84f3a6.png",height:196,width:261,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAYFBMVEV3c2SFiolyd4iIhGp9hZiBjb+Bend7dm+Dg49bXV85IiMhCg54jNlvfLJqa3q4urWfoptqZ1dWZZyFnP08R25LUVqBj+JCSFZua0dhidxnTTIqeMtyi+s7fcZWOjNah9x0HkV4AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBhQHAMAzAMBeTwpj5/y8nEWBIfZdovEYVWsZp/a7b7yxaj6cGy+aMcSKWOZ5vzqX8O+ECToDuv4MAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},R={src:"/_next/static/media/imagemobile7.55c29692.png",height:195,width:147,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAYFBMVEUljZweeotrcHMrYmYZa3ZAa3U/gIOKc3gbnLW4U2Aqb3kvlKWndHkpmKhKjZZUgYpkb1pJd2RUTDlaYG4fJykrXGuJU10GXmZ0YW8weXzSTVCUTVs2X09LXmOIg4yEfFHuOeD4AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBBQKAIAAAsaNbbAXr/790ozv35kyQtQ6FYMT4Ja5m1VTY71l7z3msKiaWKJ8NjLYCfkiNAltug2TZAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8},C={src:"/_next/static/media/imagemobile8.9b159a63.png",height:196,width:286,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAAYFBMVEXv38kvIh/js4n/1ZePkJ+pqaeIcXTYxawdEiHhl3LNpZFXPkCfhYju2rtJTF+gwbVzc4MEAAOSiIl1YHSncXWIm6fhz73lqmXkxqj64rx2XFaBeWHSn1Xuog3asJXIxrsjw5hdAAAACXBIWXMAAAsTAAALEwEAmpwYAAAANUlEQVR4nGMQl2Rg5+ViYmaQkRDjZ+XkZGeQlWITZeHh5mOQZmUTYhHg4GCQZ2CQExEWZAQAL1gCGNfG3CAAAAAASUVORK5CYII=",blurWidth:8,blurHeight:5},M={src:"/_next/static/media/imagemobile9.1dec6bcf.png",height:196,width:159,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAclBMVEVrZ5akqcLtniTu27nMwqu8rpbstYLY0bifnJjfyKexqKqFalGxrqBve6KZn698a1OwoHzJSEqwrsDvxqLEs6PFhod/S3X5NzPQsq+lvW3OzZyRfZnQuJXro0B2gCLep41bR5WTeECkk2QpH4mGq2BHSoxJJqSAAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPElEQVR4nAXBBQKAIAAAsQNJuxMT/f8X3ahqnVtLpsrCJRgPbkV53S0vaS/CcSPa7XtOhpm4G6ZmvKT8AUTDAup4RuvIAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8};var X=t(45173);t(50967);var W=t(17577),S=t(52210);function D({language:A,event:e}){let t;let{t:D}=(0,S.$G)(),[G,j]=(0,W.useState)(),I={sector:D("QHSEEXPO:sectorValue"),country:D("QHSEEXPO:locationValue"),countryConcerned:D("QHSEEXPO:countryConcernedValue"),organiser:D("QHSEEXPO:organiserValue")};e&&(t={sector:e?.versions[0]?.sector,country:D(`country:${e?.country?.replace(/\s+/g,"")}`),countryConcerned:e?.versions[0]?.countryConcerned,organiser:e?.versions[0]?.organiser});let L=[{title:D("event:events.eventLibya.title"),eventDate:D("event:events.eventLibya.eventDate"),postingDate:D("event:events.eventLibya.postingDate"),exactPlace:D("event:events.eventLibya.exactPlace"),link:"Libyan-French-economic-forum-2025",type:"events",image:h.default},{title:D("event:events.event11.title"),eventDate:D("event:events.event11.eventDate"),postingDate:D("event:events.event11.postingDate"),exactPlace:D("event:events.event11.exactPlace"),type:"events",link:"leap-tech-conference-2025-riyadh",image:r.default},{title:D("event:events.event1.title"),eventDate:D("event:events.event1.eventDate"),postingDate:D("event:events.event1.postingDate"),exactPlace:D("event:events.event1.exactPlace"),link:"franco-saudi-decarbonization-days",type:"events",image:s.default}];return a.jsx("div",{id:"event-page",children:e?(0,a.jsxs)("div",{id:"event-detail",children:[(0,a.jsxs)("div",{className:"custom-max-width",children:[a.jsx(n.Z,{t:D,infos:t})," ",a.jsx(c.Z,{htmlString:e?.versions[0]?.content}),(0,a.jsxs)(l.default,{className:"more-events-section",container:!0,rowSpacing:0,columnSpacing:3,children:[" ",G?.length>0&&G?.map((e,t)=>a.jsx(g.default,{eventData:e,language:A,isEvent:!0},t))," "]})]})," "]}):a.jsx("div",{id:"event-detail",children:(0,a.jsxs)(i.default,{className:"custom-max-width",children:[a.jsx(n.Z,{t:D,infos:I}),(0,a.jsxs)("div",{className:"details",children:[a.jsx("p",{className:"heading-h1",children:D("QHSEEXPO:eventProgram:eventProgram")}),a.jsx("p",{className:"text",children:D("QHSEEXPO:eventProgram:data12")}),a.jsx("p",{className:"heading-h1",children:D("QHSEEXPO:aboutEvent:aboutEvent")}),a.jsx("p",{className:"text",children:D("QHSEEXPO:aboutEvent:description")}),a.jsx(X.default,{slides:[m,d,o,b,u,v,p,E,w],slidesMobile:[q,f,U,V,B,x,R,C,M],options:{}}),a.jsx("p",{className:"heading-h1",children:D("QHSEEXPO:moreEvents")})]}),a.jsx(l.default,{className:"more-events-section",container:!0,rowSpacing:0,columnSpacing:3,children:L?.map((e,t)=>a.jsx(g.default,{eventData:e,language:A,isEvent:!0},t))})]})})})}t(70580)},21961:(A,e,t)=>{"use strict";t.d(e,{default:()=>z});var a,l,i,n,s=t(10326),r=t(23743),c=t(16027),g=t(90423),h=t(74345),m=t(33487),d=t(87166);let o={src:"/_next/static/media/image1.96792af3.png",height:486,width:358,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAATlBMVEW6oIGJblbCqYuwl3yxopPBrZOWhG+ljHO/oHvGqX27nHDfyay6po3QwrLbzsDb0MuumYLGuanNrYiXeF+ml4zLuKSUiID95bvn3NX27NO2SJASAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAN0lEQVR4nAXBhwGAMAzAMHcm6WbD/48isWvaAmjRhMeKIQf+Yokj9hokc4/HmqN/rcbMfOeI5w8tNQHaHoPt2QAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},b={src:"/_next/static/media/image2.15d4c56a.png",height:486,width:458,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAbFBMVEUCBATBwrigo5+6sIXK1NHA09uzrJ23spiyrZLP1MnOzrW/vJ+3tKKml3vCxr97hY5ZV0+Ym5UeISGtp4i+tpO+wKqfhmWmdVTZ3tacrbbHu7N6eW9gZWd4aFo1NCyLjYlRUUllVj1tamCem4YV+4pZAAAACXBIWXMAAAsTAAALEwEAmpwYAAAARElEQVR4nAXBhQGAMBAEsKt9XXEv7D8kCax1YtRmgLROeK8NlExznIKGUnKJjAzWLV9fDQTKAPpdsJ+tobwHUuX84Yz9aL4DKcuiPe4AAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},u={src:"/_next/static/media/image3.a1f1c1bd.png",height:486,width:364,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAATlBMVEXUzMtrYV1sZVHAwsFhW2FyaWabjH/QycTd19jb09CanphkWFxhWkuIgn+QgYGNhX+auNGjq7CwyOOuxszAwryys7cqJSzOsZ9JQEZaXncuxmC/AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAN0lEQVR4nAXBhwHAIAzAMAMhA+je/z9aCXD34DKwwrpHmHEeWysNzV8fmVSl34NUJ3kVXWapzw8tvgHCpHZjOQAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},v={src:"/_next/static/media/image4.c4fc1287.png",height:486,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAWlBMVEWXi4CekHyct820qplta2mIiIIxLjJ8bmZ2ZmCvtK+WjoxhYVmplImrqqOUrL51eHOKqcZ8eUeWgXLPvb1VYoiyydZnYHKIjmSRg3dxaXmLpqvAr6aEeHVhU0q6eRDeAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBQLAIAwAsUPbAnO3/39zCakzP/YhMKRit7cNZl4n00FbHs5yVaIq375GRHPN0twPOHACGmXSP24AAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},p={src:"/_next/static/media/image5.c3065d46.png",height:485,width:338,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAP1BMVEXEpYS7mXncwq61knDQt6W7opDIpHrEqI/CpYqti23Rs5XBraKNcF+QdmCZe2eehHF8YlF9WkKojH6ojHhLNCp3TT0tAAAACXBIWXMAAAsTAAALEwEAmpwYAAAANElEQVR4nAXBhwHAIAzAMCdk0kHX/7dWgswsYQhFMyOihKYZJ1+nzYNHVW3h7r4Zd9m7Xz8fYQFVTB8GcQAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},E={src:"/_next/static/media/image6.68a1b1de.png",height:486,width:864,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAARVBMVEUoJia5oX4xKh2rqKSbjnaMcUyjmYp2Z0yUe1V7bFeNpqODdl5NRTKFkpmjopyPhHZeUj/K2u+Qk52qlHvAr483e6xFcpOWZkm5AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAMUlEQVR4nAXBBwLAIAgAsVNRoMPd/v+pJuRSNY0dMLmsTDVyf9sK8uPgt8mHPhHXyAEgMgFTejibSwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:5},w={src:"/_next/static/media/image7.a976d733.png",height:486,width:364,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAY1BMVEXz3r7JtZtqbF1UUUuXuON9t/iVhXu8tJ+YmHySrMeJiHGJteHn3+L86M3SyZidokqCoseVi52KkV+4qZiio5dkqfuIgpnizrKksbbk0ra1pJV8lKNygY2QnKHo1s/f19VqdFVNNBciAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBhQGAMBAAsat+DYq77T8lCZ19SnmJs3P5Zss2nheTLH34kGTMUJGkdWhh9P5oQO2r4gdLdgJmUoIG9QAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},q={src:"/_next/static/media/image8.ead827e9.png",height:486,width:1040,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAARVBMVEVob295eGBZWVuMlZSMkIijo4g4ODZSUDx6em5nZGCPlluBhYV6flW5t6dhYlFVU0eEl5+HobGoxN+nsbacmpWzsJe3z+mOIZs1AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAK0lEQVR4nAXBhwEAIAgDsIIg7q3/n2qC/dYcDgLhE2NAhnru3lLBpUYWqn4W5QEoQ15rbAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:4},f={src:"/_next/static/media/image9.f6f62dd3.png",height:486,width:1041,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAOVBMVEVxYkqHgXhXTjs3MCh4bmBZXGGrqaVYUUx1Z1JKPjBMSUiVlIqXj4tjXFSijWabnpaxmW+3u7TMzsaleWEXAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAKUlEQVR4nAXBhwEAIAgDsLLBrf8fa4LF+11N4IB8TBQg4ZrVwGKdIuwDEeQA4EB+0gsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:4},U={src:"/_next/static/media/image10.d37981b9.png",height:486,width:648,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAV1BMVEWYjoRsZ3GSrMKMh3t+bmFwa2d0ZFuxqp2NfnWfjn6ts62gucxcWE5oXlt1d3OEh4O2trFiXGm2y9aOpafLurmGjGKmm46OiIx5dkgqJyjAsahPXYE1MzXRtwRjAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBQKAMAwAsZu2neAO/38nCUGm6r0Iw0hZa1lAUYvXjm0PerqG6zm0+U3E/PU7HfYDMaUCCsb2q1UAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},V={src:"/_next/static/media/image11.8e41c865.png",height:486,width:1126,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAADCAMAAACZFr56AAAANlBMVEV4bWuBhYYxJSpXXFuCdm6ikIiin6tgV0lMSEaXhn6Bb1qBYVeEdEaAhWaKiJNnZlCbkpd6W1No19tBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAI0lEQVR4nGPg5ediYWBgZGbg4ebkZGVg52BgEBTgY+NgYgcACTQAsnh4YoEAAAAASUVORK5CYII=",blurWidth:8,blurHeight:3};var B=t(95746);function x(){return(x=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let R=A=>B.createElement("svg",x({xmlns:"http://www.w3.org/2000/svg",width:69,height:53,fill:"none"},A),a||(a=B.createElement("path",{fill:"#234791",d:"M52.558 52.402c8.774 0 15.322-7.334 15.887-16.339.577-9.191-6.855-18.729-21.423-14.922 0 0 2.085-8.794 10.596-16.928 1.334-1.619 3.972-3.15 3.972-3.15-4.44.696-8.479 2.334-13.139 6.996-12.536 12.543-12.536 21.394-12.536 28.004s2.724 15.746 16.643 16.34M17.151 52.305c8.775 0 15.323-7.334 15.888-16.34.577-9.19-6.855-18.728-21.423-14.922 0 0 2.085-8.794 10.596-16.928 1.333-1.618 3.972-3.15 3.972-3.15-4.44.697-8.48 2.335-13.139 6.997C.51 20.505.51 29.355.51 35.965s2.723 15.747 16.642 16.34"})));function C(){return(C=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let M=A=>B.createElement("svg",C({xmlns:"http://www.w3.org/2000/svg",width:68,height:53,fill:"none"},A),l||(l=B.createElement("path",{fill:"#234791",d:"M16.013.598C7.333.598.855 7.932.297 16.937c-.571 9.191 6.78 18.729 21.191 14.922 0 0-2.062 8.794-10.481 16.928-1.32 1.619-3.93 3.15-3.93 3.15 4.393-.696 8.388-2.334 12.997-6.996 12.401-12.543 12.401-21.394 12.401-28.004S29.781 1.19 16.012.597M51.036.695c-8.68 0-15.157 7.334-15.716 16.34-.57 9.19 6.781 18.728 21.192 14.922 0 0-2.063 8.794-10.482 16.928-1.319 1.618-3.929 3.15-3.929 3.15 4.392-.697 8.388-2.335 12.997-6.997 12.4-12.543 12.4-21.393 12.4-28.003S64.806 1.288 51.037.695"})));var X=t(36056),W=t(53601),S=t(54485);function D(){return(D=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let G=A=>B.createElement("svg",D({xmlns:"http://www.w3.org/2000/svg",width:40,height:30,fill:"none"},A),i||(i=B.createElement("path",{fill:"#234791",d:"M30.036 29.945c5.014 0 8.756-4.19 9.079-9.336.33-5.253-3.917-10.703-12.242-8.528 0 0 1.191-5.025 6.055-9.672.762-.925 2.27-1.8 2.27-1.8-2.538.397-4.846 1.333-7.508 3.997-7.164 7.168-7.164 12.225-7.164 16.003 0 3.777 1.556 8.997 9.51 9.336M9.802 29.89c5.014 0 8.756-4.19 9.079-9.336.33-5.252-3.918-10.702-12.242-8.527 0 0 1.191-5.025 6.055-9.673.762-.925 2.27-1.8 2.27-1.8-2.538.398-4.846 1.334-7.509 3.998C.292 11.719.292 16.777.292 20.554s1.556 8.998 9.51 9.337"})));function j(){return(j=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let I=A=>B.createElement("svg",j({xmlns:"http://www.w3.org/2000/svg",width:39,height:30,fill:"none"},A),n||(n=B.createElement("path",{fill:"#234791",d:"M9.295.055c-4.96 0-8.66 4.19-8.98 9.336-.327 5.253 3.874 10.703 12.11 8.528 0 0-1.18 5.025-5.99 9.672-.754.925-2.246 1.8-2.246 1.8 2.51-.397 4.793-1.333 7.427-3.997 7.086-7.168 7.086-12.225 7.086-16.003 0-3.777-1.54-8.997-9.407-9.336M29.31.11c-4.96 0-8.66 4.19-8.98 9.336-.326 5.252 3.875 10.702 12.11 8.527 0 0-1.179 5.025-5.99 9.673-.753.925-2.245 1.8-2.245 1.8 2.51-.398 4.793-1.334 7.427-3.998 7.086-7.167 7.086-12.225 7.086-16.002S37.178.448 29.311.11"}))),L={src:"/_next/static/media/imagemobile1.dfbffa2e.png",height:197,width:145,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAATlBMVEW7oYC2mnymi2/Eo3mxo5Tbz8GJblbHqoGxnIS/rJK8nHHDqo3QwbLg1tKpmY6MfnDGuamljnyeh2yWeWHhy63Lt6L647jcyK6XiYL37dWcKWHwAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBwLAIAgAsVNBxFG7x/8/2gTD1xBIFaeRqhEP2kXZMtLPEndeva1k+uePTMZQleUHKpMBwprwb5UAAAAASUVORK5CYII=",blurWidth:6,blurHeight:8},J={src:"/_next/static/media/imagemobile2.380795be.png",height:197,width:186,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAbFBMVEW3spmAh4yeoZ2uqYvA09uyrprAw7oDBQXP1MnJ1NLAuZe4roS+vqjJy7laWVLCvbaYnJW2sJDR0bOkmXufhmXY3daplHmmdVOcrba1p6C0s6RhZWl3Z1k4Ny57em8iJCMYHB5wbGJkUzlKSkEdz4t6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAARUlEQVR4nAXBBQLAIAwEsMNaHObu///jEljbBg+l4GwKPqKBdqnvIgCt3TSSMRhmsT91NVgEM39HRtnui/N7gqqUQlL5AWa6AzBzOHF6AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},H={src:"/_next/static/media/imagemobile3.cf9a8a31.png",height:197,width:137,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAP1BMVEXEpoqkiHO+oILHsaPHq5Kui2q6l3fHpH7Yvai7opC2kWzVt5WNc16Lb1yYemZ5X07Kt6xIMimskYB9WkLkyrge61rqAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAM0lEQVR4nAXBhwHAMAgEscMGHtLb/rNGAnAGS8IIVkkkoVAe3ObTN56vqozu7nnxUrafPxwuAUfZDxcLAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8},N={src:"/_next/static/media/imagemobile4.a950bd13.png",height:197,width:148,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAARVBMVEVxaGPMxsaYh3xnXlbW0dGNgn/Awr/f19ZhWFzTy8mjp6leXW+amYvUzMOwyOKaudGwyc6zs7dqYkpYVUHRtaMsJyxOREkp9PrpAAAACXBIWXMAAAsTAAALEwEAmpwYAAAANUlEQVR4nAXBhwGAIBAAsQO+Uuy6/6gmZLq7cBbJYuyby1rY0ad16jWiAYx4bxoaX2WqBs8PK1ABj0cTClgAAAAASUVORK5CYII=",blurWidth:6,blurHeight:8},y={src:"/_next/static/media/imagemobile5.f1379f98.png",height:197,width:262,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAWlBMVEWYjoQvLC5yb3WGhoBqaGd1ZFtoX1uhj36RhHZ8bWe1q5u6sqyguc2tsq1RX4Kkmo3LublhWmepqKFeUUa2zNaFenaTrcB6dkqOq8OFi1+OpKR6bmGNhotbX1czSSBcAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBQKAMAwAsZu2M4Y7//8mCW4u9dxzxjraXdsKyibPcSFjQV/74WPQZRo8KZhoUpcfPV4CHnEGQOMAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6},Y={src:"/_next/static/media/imagemobile6.ae69bd79.png",height:197,width:351,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAASFBMVEWSinVyb2w0NTRdVUSlnZOji25rZVWNe2YpJR55Z0yUnpWXekxoW0SDh32umX2PcUpWSTGspZVGib27o4R5b18uPUmxmGq5xtfIY8mQAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAK0lEQVR4nAXBBwIAEAwEsENbam///6kESAS8ceFbiUxbUNUaz2siuxBE+/kVLwE8uwcEOwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:4},F={src:"/_next/static/media/imagemobile7.696e680f.png",height:197,width:148,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAZlBMVEX1379pbleKteBTUUyUlKCLpsTn3+KNhnS+tqGRkmvLt52RrstzdmWYm3776MzQyJeyoZOgpEyVveaZtd9kqft/tPF9lKN8u/9zgYzo18/f19WksLWjo5e3qJecjYLj0bbhzbKIgpnCsMaZAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBhQHAMAzAMBeTdsxM/z85iTBsMe6EcVqs57B9Ma+c+vlkUHEudaiYsmrgkrutIT9v5gdGkgJ46EG8/QAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},k={src:"/_next/static/media/imagemobile8.0250656e.png",height:196,width:421,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAQlBMVEVVVEeuvMdyb15weX11dWNlZlR7iYiJjVNhXV2wrZ9xeXl7gnuFkZQzLyVAQTyKjoKWlJSfp6mipYY+QDWwrZQ6PUs+a/sWAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAK0lEQVR4nAXBhwEAIAgDsIKA4p7/v2qCRWRWc8B4k1OSgn2A3mLEZRWF+wcUIgESGBVMMgAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:4},Q={src:"/_next/static/media/imagemobile9.c87f473d.png",height:197,width:422,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAOVBMVEVxYUqrqaQ3MClhW1NaVE2WkoqIgnl3aFVSTUZKPjB2cWZSV2NgYV9ZSzS4vLWxmnCanZWijWbLzMQSMcjEAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAKUlEQVR4nAXBCQIAEAgAwY2o3Pz/sWa4ft5qBpsqJROQXMzCmWmUqto/EhsA5aO1igQAAAAASUVORK5CYII=",blurWidth:8,blurHeight:4},O={src:"/_next/static/media/imagemobile10.e803bf00.png",height:197,width:457,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAADCAMAAACZFr56AAAAM1BMVEV7bW6CdW6joayAhWaHiI6di4RdWFZ+XlRza2dNSEdoZ1CBb1o2KS1bWT+TgXmckpeEdEZx0SCnAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAI0lEQVR4nGNg5uJm5OBgYWMQYOdjZWVg42RgYOdnYeLk4QUACPgAsA6WiHEAAAAASUVORK5CYII=",blurWidth:8,blurHeight:3},P={src:"/_next/static/media/imagemobile11.6bbe0f21.png",height:197,width:263,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAXVBMVEWXjICHh4Gct82uradoZG9hYFi4raJ8bGJ1c3B2ZWGUg3WQqsGlj383NDjPvLthVEtWYomFeHWzyda2qZOQh44qJimalIqztbJubGSquLJ9eUqGjWGLpqmkmY50anpaR3R3AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOUlEQVR4nAXBhwGAIBAAsaM+TVGwt/3HNEGZXefcPUFt5tFng8Rc5Lopoyctn8NGodrpQOLg1vDyAzitAic9dQW+AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6};var K=t(45173);t(50967);var T=t(17577),Z=t(52210);function z({language:A,event:e,isMobileSSR:t}){let a;let{t:l}=(0,Z.$G)(),[i,n]=(0,T.useState)();(0,r.Z)();let B={sector:l("PentabellSalestraining:sectorValue"),country:l("PentabellSalestraining:locationValue"),countryConcerned:l("PentabellSalestraining:countryConcernedValue"),organiser:l("PentabellSalestraining:organiserValue")};e&&(a={sector:e?.versions[0]?.sector,country:l(`country:${e?.country?.replace(/\s+/g,"")}`),countryConcerned:e?.versions[0]?.countryConcerned,organiser:e?.versions[0]?.organiser});let x=[{title:l("event:events.AfricaforumFrance.title"),eventDate:l("event:events.AfricaforumFrance.eventDate"),postingDate:l("event:events.AfricaforumFrance.postingDate"),exactPlace:l("event:events.AfricaforumFrance.exactPlace"),link:"Africa-France-forum-on-ecological-and-energy-transition-2025",type:"events",image:S.default},{title:l("event:events.eventGitex.title"),eventDate:l("event:events.eventGitex.eventDate"),postingDate:l("event:events.eventGitex.postingDate"),exactPlace:l("event:events.eventGitex.exactPlace"),type:"events",link:"Gitex-africa-morocco-2025",image:W.default},{title:l("event:events.eventLibya.title"),eventDate:l("event:events.eventLibya.eventDate"),postingDate:l("event:events.eventLibya.postingDate"),exactPlace:l("event:events.eventLibya.exactPlace"),link:"pentabell-salon-sme-and-european-microwave-week",link:"Libyan-French-economic-forum-2025",type:"events",image:X.default}];return s.jsx("div",{id:"event-page",children:e?(0,s.jsxs)("div",{id:"event-detail",children:[(0,s.jsxs)("div",{className:"custom-max-width",children:[s.jsx(h.Z,{t:l,infos:a})," ",s.jsx(m.Z,{htmlString:e?.versions[0]?.content}),(0,s.jsxs)(c.default,{className:"more-events-section",container:!0,rowSpacing:0,columnSpacing:3,children:[" ",i?.length>0&&i?.map((e,t)=>s.jsx(d.default,{eventData:e,language:A,isEvent:!0},t))," "]})]})," "]}):s.jsx("div",{id:"event-detail",children:(0,s.jsxs)(g.default,{className:"custom-max-width",children:[s.jsx(h.Z,{t:l,infos:B}),(0,s.jsxs)("div",{className:"details",children:[s.jsx("p",{className:"text",children:l("PentabellSalestraining:Description:description1")}),s.jsx("p",{className:"heading-h1",children:l("PentabellSalestraining:eventProgram:eventProgram")}),(0,s.jsxs)("ul",{children:[s.jsx("li",{className:"text",children:l("PentabellSalestraining:eventProgram:puce1")}),(0,s.jsxs)("li",{className:"text",children:[" ",l("PentabellSalestraining:eventProgram:puce2")]}),s.jsx("li",{className:"text",children:l("PentabellSalestraining:eventProgram:puce3")}),s.jsx("li",{className:"text",children:l("PentabellSalestraining:eventProgram:puce4")})]}),s.jsx("p",{className:"heading-h1",children:l("PentabellSalestraining:eventProgram2:eventProgram2")}),s.jsx("p",{className:"text",children:l("PentabellSalestraining:eventProgram2:data1")}),(0,s.jsxs)("ul",{children:[s.jsx("li",{className:"text",children:l("PentabellSalestraining:eventProgram2:puce1")}),(0,s.jsxs)("li",{className:"text",children:[" ",l("PentabellSalestraining:eventProgram2:puce2")]}),s.jsx("li",{className:"text",children:l("PentabellSalestraining:eventProgram2:puce3")}),s.jsx("li",{className:"text",children:l("PentabellSalestraining:eventProgram2:puce4")}),s.jsx("li",{className:"text",children:l("PentabellSalestraining:eventProgram2:puce5")})]}),(0,s.jsxs)("div",{class:"quote-box",children:[s.jsx("div",{class:"quote-icon-top",children:t?s.jsx(G,{}):s.jsx(R,{})}),s.jsx("div",{class:"quote-border top-border"}),(0,s.jsxs)("div",{class:"quote-content",children:[s.jsx("p",{children:l("PentabellSalestraining:quote:description")}),(0,s.jsxs)("div",{class:"quote-author",children:["— Wisssem Zarrouk, ",s.jsx("span",{children:l("PentabellSalestraining:quote:authorrole")})]})]}),s.jsx("div",{class:"quote-border bottom-border"}),s.jsx("div",{class:"quote-icon-bottom",children:t?s.jsx(I,{}):s.jsx(M,{})})]}),s.jsx("p",{className:"heading-h1",children:l("PentabellSalestraining:Description2:title")}),s.jsx("p",{className:"text",children:l("PentabellSalestraining:Description2:description2")}),s.jsx("p",{className:"text",children:l("PentabellSalestraining:Description2:description3")}),s.jsx(K.default,{slides:[o,b,u,v,p,E,w,q,f,U,V],options:{},slidesMobile:[L,J,H,N,y,Y,F,k,Q,O,P]}),s.jsx("p",{className:"heading-h1",children:l("PentabellSalestraining:moreEvents")})]}),s.jsx(c.default,{className:"more-events-section",container:!0,rowSpacing:0,columnSpacing:3,children:x?.map((e,t)=>s.jsx(d.default,{eventData:e,language:A,isEvent:!0},t))})]})})})}t(70580)},33487:(A,e,t)=>{"use strict";t.d(e,{Z:()=>n});var a=t(10326),l=t(45173);let i=A=>{if(!A)return null;let e=new DOMParser().parseFromString(A,"text/html"),t=[],i=[];return Array.from(e.body.childNodes).forEach((A,e)=>{if(A.nodeType===Node.ELEMENT_NODE){if(A.classList.contains("se-image-container")){let e=A.querySelector("figure img");e&&i.push({src:e.getAttribute("src")}),A.remove()}else i.length>2?(t.push(a.jsx(l.default,{slides:i},`carousel-${e}`)),i=[]):1===i.length&&(t.push(a.jsx("figure",{children:a.jsx("img",{src:i[0].src,alt:""})},`figure-${e}`)),i=[]),t.push(a.jsx("div",{dangerouslySetInnerHTML:{__html:A.outerHTML}},`content-${e}`))}}),i.length>1?t.push(a.jsx(l.default,{slides:i},"carousel-end")):1===i.length&&t.push(a.jsx("figure",{children:a.jsx("img",{src:i[0].src,alt:""})},"figure-end")),t},n=({htmlString:A})=>a.jsx("div",{children:i(A)})},74345:(A,e,t)=>{"use strict";t.d(e,{Z:()=>h});var a=t(10326),l=t(99063),i=t(16027),n=t(25609),s=t(97397),r=t(59788),c=t(31487),g=t(71557);let h=({t:A,infos:e})=>a.jsx(l.default,{className:"infoSection",children:(0,a.jsxs)(i.default,{container:!0,spacing:2,children:[(0,a.jsxs)(i.default,{item:!0,xs:12,sm:6,md:6,className:"leftSide",children:[(0,a.jsxs)(l.default,{className:"item",children:[a.jsx(s.default,{className:"icon"}),(0,a.jsxs)(l.default,{children:[a.jsx(n.default,{className:"label",children:A("eventDetails:sector")}),a.jsx(n.default,{className:"value",children:e.sector})]})]}),(0,a.jsxs)(l.default,{className:"item",children:[a.jsx(c.default,{className:"icon"}),(0,a.jsxs)(l.default,{children:[a.jsx(n.default,{className:"label",children:A("eventDetails:countryConcerned")}),(0,a.jsxs)(n.default,{className:"value",children:[" ",e.countryConcerned]})]})]})]}),a.jsx(i.default,{item:!0,xs:0,sm:1,md:1,className:"divider"}),(0,a.jsxs)(i.default,{item:!0,xs:12,sm:6,md:5,className:"right-side",children:[(0,a.jsxs)(l.default,{className:"item",children:[a.jsx(r.default,{className:"icon"}),(0,a.jsxs)(l.default,{children:[a.jsx(n.default,{className:"label",children:A("eventDetails:location")}),a.jsx(n.default,{className:"value",children:e.country})]})]}),(0,a.jsxs)(l.default,{className:"item",children:[a.jsx(g.default,{className:"icon"}),(0,a.jsxs)(l.default,{children:[a.jsx(n.default,{className:"label",children:A("eventDetails:organiser")}),a.jsx(n.default,{className:"value",children:e.organiser})]})]})]})]})})},65364:(A,e)=>{"use strict";var t=Symbol.for("react.element"),a=(Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.provider"),Symbol.for("react.context"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy"),Symbol.iterator,{isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}}),l=Object.assign,i={};function n(A,e,t){this.props=A,this.context=e,this.refs=i,this.updater=t||a}function s(){}function r(A,e,t){this.props=A,this.context=e,this.refs=i,this.updater=t||a}n.prototype.isReactComponent={},n.prototype.setState=function(A,e){if("object"!=typeof A&&"function"!=typeof A&&null!=A)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,A,e,"setState")},n.prototype.forceUpdate=function(A){this.updater.enqueueForceUpdate(this,A,"forceUpdate")},s.prototype=n.prototype;var c=r.prototype=new s;c.constructor=r,l(c,n.prototype),c.isPureReactComponent=!0;var g=Object.prototype.hasOwnProperty,h={current:null},m={key:!0,ref:!0,__self:!0,__source:!0};e.createElement=function(A,e,a){var l,i={},n=null,s=null;if(null!=e)for(l in void 0!==e.ref&&(s=e.ref),void 0!==e.key&&(n=""+e.key),e)g.call(e,l)&&!m.hasOwnProperty(l)&&(i[l]=e[l]);var r=arguments.length-2;if(1===r)i.children=a;else if(1<r){for(var c=Array(r),d=0;d<r;d++)c[d]=arguments[d+2];i.children=c}if(A&&A.defaultProps)for(l in r=A.defaultProps)void 0===i[l]&&(i[l]=r[l]);return{$$typeof:t,type:A,key:n,ref:s,props:i,_owner:h.current}}},1788:(A,e,t)=>{"use strict";A.exports=t(65364)},83694:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>Aa,generateMetadata:()=>Ae});var a,l,i,n,s,r=t(19510),c=t(24330);t(60166);var g=t(43207),h=t(44957),m=t(58585),d=t(71615),o=t(68570);let b=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\events\EventFrancoSaudiDecarbonization.jsx#default`);var u=t(55920),v=t(28868);let p=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\embla_slider\EmblaCarousel.jsx#default`),E={src:"/_next/static/media/image1.47c080e4.png",height:325,width:588,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAARVBMVEWllHGJgm10ZF2zqaiSfmZ3bVecjXCtl362l2e5o3vYvYuQhWdbSTetjWOokmikmmpsX0/KtIiqhliOdVd7cnu6j1dpa4Vg0zxRAAAAAXRSTlP9g+pWxwAAAAlwSFlzAAALEwAACxMBAJqcGAAAACtJREFUeJwFwYcBACAIA7CCIO6t/59qAnOWJkXgWmhRe8bagap6wTvMXkb5FLABLxyGWXcAAAAASUVORK5CYII=",blurWidth:8,blurHeight:4},w={src:"/_next/static/media/image2.59bcf008.png",height:325,width:296,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAICAMAAAAC2hU0AAAAVFBMVEWOkJF2eX+XmpvMspRaXGZFTVq/vbW/ppbKz8+ioJ+HiIpvcXWBenHU19aNiX3h4+FjZmp8cWKhimYpMz44Q08QHS66rqVRWF6YlY2PlIqrsKexrZ5KhUneAAAACXRSTlP+/////////f4sB9HIAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPUlEQVR4nAXBBwLAIAgEsAMBxd29/v/PJkhttJEMxGd9nw4p1C+54c449INZ3hWGGHTz6girChVGXGRmxw9MowIvQI+kgwAAAABJRU5ErkJggg==",blurWidth:7,blurHeight:8},q={src:"/_next/static/media/image3.fdebff43.png",height:325,width:296,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAICAMAAAAC2hU0AAAAbFBMVEWAaWdOVFWrsqpydICRiX2jpJyLhHd0c2iAeXqJkpaTj4yll4+Fc4Wyp5GgiVN6cnVzbHugi4CwvL+WnJVkxZ6Vl4F+ln3DzL7DvaaYlZCamqiAf4usqJR9iJiOWlTArJuJd6BwWFxPX2+2fl+p7SevAAAACHRSTlP9/////////CitBBMAAAAJcEhZcwAACxMAAAsTAQCanBgAAABASURBVHicBcGFAcAwDAMwF5eUO2b8/8dJoHebtbbId9lNZ1BVsVG2uKJLaWB8fPDqepyZQqART/WeBIBlkko0P2xdAyKQaZhDAAAAAElFTkSuQmCC",blurWidth:7,blurHeight:8};var f=t(47945);let U={src:"/_next/static/media/image4.6160aaef.png",height:325,width:296,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAICAMAAAAC2hU0AAAAWlBMVEWsn41lVluXnKCJi5F0dnyXk5enl4WZk4uaoaygqbqOkp99cXWZhn6Cen2Ie5SllaFoXm9nX2NTUGKsusy/p4jT0cKzmX24vJCLj4KAd5KzqJGPhHpgXWN8Z1qjvlIsAAAAB3RSTlP7///////+tAH4TgAAAAlwSFlzAAALEwAACxMBAJqcGAAAAD5JREFUeJwFwYUBwCAQBLADXnCr2/5rNoFa760jKIclRIJoDN4R1j1fzAXbcdfWKvQsfc6OlN7PDAM8w2SRH0icAo4Ei94qAAAAAElFTkSuQmCC",blurWidth:7,blurHeight:8},V={src:"/_next/static/media/image5.57d82acf.png",height:325,width:588,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAASFBMVEV+cWW4w6F0fX+kmYKppp7Fp49EUGKamZs6NiV+g32FdnBRSDWgd2mUc22thYRlboKroJDInZhzdXimm4ehi3VbXEuVoIVmcGEllNu6AAAACHRSTlP+/////////kgsYtwAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAsSURBVHicY2DnZGERYBZmYGAUY+LnZBXkZWAUZ2MTYuXjYRDl4ODgZuASAQAU9gE+vZekIQAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:4},B={src:"/_next/static/media/image6.dd888aef.png",height:325,width:296,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAICAMAAAAC2hU0AAAAXVBMVEU1N1F6fItfY4F3dpVZW35na4l1eJBCRVGPkq+JjaaRm7KaoLJQYI96f6FGR29XWG6loKBrbG5lapN8d7tvj8GCkZlAZLslbrwAeek1VZJRZqJic89daJg1aM1MUF3gFdxXAAAACHRSTlP+////////+zhGllMAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA+SURBVHicBcEHAsAgCASwU8GC2+79/2eaQFVF1EAkM3mPzDYGZ3FfxxrnhOc7dwkL6E1sNgfU3koBpv43qhhM1wKX2EI07QAAAABJRU5ErkJggg==",blurWidth:7,blurHeight:8};var x=t(86052),R=t(97052);let C={src:"/_next/static/media/leap1.712d294c.png",height:199,width:353,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAAUVBMVEWMmYeXoZZNiWODamixs6pbaGiTzofRv7m2pKWmqplUYFaTr5aijpO/j42dkYKPbnG/tqmmkpmir46uw5eEwH2Hi357p3R9iWl2rHN2aXskQD9qKMRTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAMUlEQVR4nAXBhQEAIAwDsALD3eX/Q0lAZG1vNUDKpdQoBswzzqE1jAfOTBHXPbGdyB8inQGi1QGZOAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:5},M={src:"/_next/static/media/leap2.70a93da6.png",height:195,width:146,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAV1BMVEWOkKJkaohxcHBtc4RfX3KfobR7epNyd5B9hJ2JjK5PUWmWoLZ0eZw7PGguMU6Fh4eDfrxUWY54fKYvV5V1c4NARFFnb8Mpb9CHhH9LTVBZaZ8NZtBDe8DtNXG0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBwLAIAgAsVNRQLv3+v87mwA5A+6mVnBV08Czfq2bOc53aRM1pC0Je71KP3DHOIr8MWcB/PzsQUQAAAAASUVORK5CYII=",blurWidth:6,blurHeight:8},X={src:"/_next/static/media/leap3.01624981.png",height:199,width:353,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAARVBMVEWgj3eDfWi1kWCWh2KKfmW8qX+tnoLMsoN7a2ChhWB0YEubjW62rrO1oo/EqHhlWUZJOyxjV1JwcFZYVFp9fJenk4mpoW5gbGggAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAMElEQVR4nAXBhwEAIAgDsCoo4N7/n2qCgiDRSUaNTeACwzwZrcdQA/uUE86eerWPDxlFAVwWyOi0AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:5},W={src:"/_next/static/media/leap4.4547b586.png",height:196,width:146,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAY1BMVEV6eHWxtauqppKKdXaOhH6NnpeYk4+VkoOjnZRwcnSpnoM8X2FoV2mkoZ6FZmmrnYvHzrrBqpZrZYR5bot9Vla1e2JFTlCDj5WSkpWEhXKxubugilGReYNmvZJ9h5F4cGaQjZ4nsYiSAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBBQLAIAwAsQMKLXN3+/8rl0Bp2hYwJC+KjNm7g97k1Mi8Pt92M4Vw5Z3GXFpeqrqLmR87tgJa7etHeAAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},S={src:"/_next/static/media/leap5.158c7206.png",height:196,width:146,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAATlBMVEXb4eZ1eHtqbXB1dXKIh4RVWmfDrp27uLGWlJXGxb8vOEqgoJ+Cf3iorrGKgHvR2eOlppXO1uCxq5+7wbs/R1N5blqJi5BASVKolnbCu7QwrwYYAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAN0lEQVR4nAXBhwHAIAzAMAMhA+je/z9aCYBlY4qIY6WmuWTBvZ9t57P6iKFvS6OjV81yo6O42Q8vogHCPcncBwAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},D={src:"/_next/static/media/leap6.6d2ba5b2.png",height:197,width:351,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAASFBMVEU6NSV1foB6hIahjHWDfHFGUGO3wqClmoWqqJ9/c2aroZHHnJe/p49RSDVzdnmUcm2uhoVkbYHOqpGfdWmampxbXEtncWKWoYYelLG5AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAK0lEQVR4nAXBBwIAEAwEsENbam///6kE24YQvVfIM4NmapDLXHNfOACKkvsXTQE80NulnQAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:4};var G=t(2504);let j=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\node_modules\@mui\material\Box\Box.js#default`);(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\node_modules\@mui\material\Typography\Typography.js#TypographyRoot`);let I=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\node_modules\@mui\material\Typography\Typography.js#default`);var L=t(1788);function J(){return(J=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let H=A=>L.createElement("svg",J({xmlns:"http://www.w3.org/2000/svg",width:26,height:30,fill:"none"},A),a||(a=L.createElement("path",{fill:"#E5F0FC",d:"M24.874 28.356h-.1V16.765l-1.992-.394v-1.494a.472.472 0 1 0-.945 0v1.308l-4.099-.81V7.458h-1.345V5.123h-1.27V2.788H12.67V.473a.472.472 0 1 0-.946 0v2.315H9.27v2.335H8v2.335H6.654v2.665H5.19v-.96a.472.472 0 1 0-.945 0v.96h-1.43v-1.68a.472.472 0 1 0-.946 0v1.68H.498v18.233H.473a.472.472 0 1 0 0 .945H24.87a.472.472 0 1 0 0-.945zm-1.046 0h-8.516V15.86l1.48.293.946.186 4.477.882 1.613.319zM10.218 3.734h3.96v1.389h-3.96zm-1.27 2.334h6.5v1.39h-6.5zm.397 22.288H1.446v-2.7h7.899zM1.446 11.068h7.899v2.7H1.446zm0 3.646h7.899v2.7H1.446zm7.587 9.993H1.446v-2.7h7.899v2.7zM1.446 18.36h7.899v2.7H1.446zm8.844 9.994V10.12H7.603V8.4h9.193v6.786l-2.426-.479v13.646h-4.08M21.664 24.6v1.415a.472.472 0 1 1-.945 0V24.6a.472.472 0 1 1 .945 0m-3.24 0v1.415a.47.47 0 0 1-.472.472.44.44 0 0 1-.21-.053.47.47 0 0 1-.262-.42V24.6c0-.186.107-.343.261-.419a.5.5 0 0 1 .211-.053c.262 0 .473.21.473.472m3.24-5.265v1.415a.472.472 0 1 1-.945 0v-1.415a.472.472 0 1 1 .945 0m-3.24 0v1.415a.47.47 0 0 1-.472.473.44.44 0 0 1-.21-.054.47.47 0 0 1-.262-.419v-1.415c0-.185.107-.343.261-.419a.5.5 0 0 1 .211-.053c.262 0 .473.211.473.473"})));function N(){return(N=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let y=A=>L.createElement("svg",N({xmlns:"http://www.w3.org/2000/svg",width:25,height:35,fill:"none"},A),l||(l=L.createElement("path",{fill:"#FAFCFF",d:"M12.503 0C6.405 0 1.448 4.997 1.448 11.145c0 1.629.696 3.565 1.699 5.624.942 1.935 2.17 3.963 3.412 5.846H2.934a.544.544 0 0 0-.528.432L.012 34.338a.55.55 0 0 0 .108.458.54.54 0 0 0 .42.204h23.92a.54.54 0 0 0 .42-.204.55.55 0 0 0 .108-.458l-2.394-11.291a.544.544 0 0 0-.528-.433h-3.624c1.243-1.88 2.47-3.91 3.413-5.846 1.003-2.059 1.698-3.994 1.698-5.623C23.553 4.997 18.601 0 12.503 0m0 1.094c5.513 0 9.968 4.495 9.968 10.051 0 1.298-.619 3.155-1.588 5.144-.968 1.989-2.271 4.122-3.576 6.075-.107.162-.214.317-.32.475-2.26 3.337-4.132 5.62-4.484 6.054-.352-.432-2.228-2.716-4.488-6.054l-.32-.475c-1.305-1.953-2.61-4.086-3.577-6.075-.968-1.99-1.587-3.845-1.587-5.144 0-5.556 4.459-10.051 9.972-10.051m0 4.44c-3.066 0-5.567 2.519-5.567 5.611 0 3.093 2.5 5.608 5.567 5.608 3.066 0 5.563-2.514 5.563-5.608 0-3.092-2.497-5.61-5.563-5.61m0 1.094c2.482 0 4.48 2.017 4.48 4.517s-1.998 4.518-4.48 4.518-4.485-2.017-4.485-4.518 2.002-4.517 4.485-4.517m-9.13 17.08H7.29a103 103 0 0 0 4.792 6.448h-.001c.103.126.257.2.418.2a.54.54 0 0 0 .418-.2s2.322-2.831 4.792-6.447h3.917l2.163 10.198H1.211z"})));function Y(){return(Y=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let F=A=>L.createElement("svg",Y({xmlns:"http://www.w3.org/2000/svg",width:25,height:30,fill:"none"},A),i||(i=L.createElement("path",{fill:"#E5F0FC",d:"M11.868 4.942c.624.346 2.643.59 3.79.704q.053.005.105.005c.352 0 .686-.173.89-.462a1.06 1.06 0 0 0 .087-1.08c-.461-.95-1.62-2.035-4.538-1.804a.97.97 0 0 0-.862.739c-.22.912-.032 1.587.528 1.898m.252-1.71a.16.16 0 0 1 .143-.126c1.95-.156 3.25.314 3.75 1.352a.27.27 0 0 1-.022.27.28.28 0 0 1-.258.12c-1.765-.176-3.13-.413-3.478-.607-.299-.167-.194-.763-.135-1.01M18.964 16.91a2.327 2.327 0 0 0-4.651 0 2.33 2.33 0 0 0 2.325 2.325 2.33 2.33 0 0 0 2.326-2.326m-3.845 0c0-.84.683-1.523 1.523-1.523.838 0 1.522.683 1.522 1.522s-.683 1.523-1.522 1.523a1.53 1.53 0 0 1-1.523-1.523"})),n||(n=L.createElement("path",{fill:"#E5F0FC",d:"M18.507 26.278c1.122-1.622 2.009-3.1 2.654-4.425l.182-.182A12.42 12.42 0 0 0 25 12.84C25 5.95 19.392.343 12.5.343c-1.484 0-2.942.258-4.335.77a12.5 12.5 0 0 0-5.87 4.509A12.4 12.4 0 0 0 0 12.843c0 1.745.352 3.426 1.041 5.004q.004.007.006.015a12.5 12.5 0 0 0 3.127 4.303 12.48 12.48 0 0 0 9.672 3.106q.325.497.683 1.011c-1.393.15-3.745.578-3.745 1.75 0 1.277 2.974 1.857 5.734 1.857 2.763 0 5.734-.581 5.734-1.856 0-1.177-2.352-1.604-3.745-1.754m-7.116-8.877a5.13 5.13 0 0 1 3.54-4.877 5.1 5.1 0 0 1 2.147-.218 5.135 5.135 0 0 1 4.563 5.095c0 1.006-.358 2.276-1.05 3.771q-.001.003-.003.006 0 .003-.003.005c-.79 1.698-2.016 3.687-3.65 5.91a.524.524 0 0 1-.846 0c-3.07-4.19-4.698-7.54-4.698-9.693m10.702 2.13q.354-1.18.355-2.13a5.936 5.936 0 0 0-4.783-5.815 1.78 1.78 0 0 0-.116-1.649c-.004-.003-.004-.01-.006-.011l-.01-.015a.78.78 0 0 1-.035-.754.8.8 0 0 1 .63-.444c1.35-.152 3.07-.017 3.613 1.497.41 1.146 1.253 1.865 2.44 2.09q.015.27.015.543a11.62 11.62 0 0 1-2.103 6.688M7.63 2.206c-.083.634.1 1.431.88 2.364 1.267 1.517 1.81 2.955 1.53 4.05-.22.865-.95 1.545-2.162 2.023-.038.015-.184.043-.31.07-.822.173-2.534.534-3.176 1.525a1.56 1.56 0 0 0-.214 1.214c.111.517.297 1.822-.417 2.766-.434.575-1.138.917-2.09 1.03a11.6 11.6 0 0 1-.866-4.411c0-4.571 2.71-8.74 6.825-10.63M2.012 18.02c1.056-.17 1.859-.61 2.39-1.314.911-1.211.695-2.796.56-3.42a.75.75 0 0 1 .103-.607c.46-.71 2.006-1.038 2.666-1.177.235-.05.351-.077.436-.109 1.47-.577 2.361-1.443 2.649-2.572.35-1.37-.235-3.017-1.692-4.762-.704-.844-.884-1.578-.546-2.235a11.7 11.7 0 0 1 3.922-.675c5.98 0 10.925 4.514 11.611 10.312-.792-.21-1.323-.71-1.613-1.525-.564-1.566-2.144-2.284-4.461-2.02a1.6 1.6 0 0 0-1.262.885 1.58 1.58 0 0 0 .074 1.53l.011.02q.15.239.15.518a.98.98 0 0 1-.22.609 5.932 5.932 0 0 0-6.204 5.922c0 1.011.303 2.217.898 3.613-.69.126-1.267.124-1.79-.017q-.053-.027-.105-.058c-1.256-.702-2.965-.546-5.077.449a11.9 11.9 0 0 1-2.5-3.367M12.5 24.536c-2.672 0-5.244-.912-7.317-2.575 1.71-.745 3.059-.857 4.015-.326q.087.048.173.094a.4.4 0 0 0 .07.027c.704.199 1.47.199 2.388.005q.611 1.276 1.528 2.743-.427.031-.857.032m7.681 4.139c-.973.264-2.276.41-3.666.41s-2.692-.146-3.666-.41c-1.047-.284-1.267-.592-1.267-.643 0-.158.874-.795 3.476-.994q.19.263.387.534c.252.344.643.543 1.07.543.429 0 .816-.197 1.07-.543q.199-.27.388-.534c2.598.2 3.476.836 3.476.994 0 .047-.22.355-1.268.643"})));function k(){return(k=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var a in t)({}).hasOwnProperty.call(t,a)&&(A[a]=t[a])}return A}).apply(null,arguments)}let Q=A=>L.createElement("svg",k({xmlns:"http://www.w3.org/2000/svg",width:25,height:26,fill:"none"},A),s||(s=L.createElement("path",{fill:"#fff",d:"M1.552 13.441c0-.2.153-.363.341-.363H6.72c.188 0 .341.163.341.363s-.153.362-.341.362H1.893c-.188 0-.341-.162-.341-.362m.341 2.227H6.72c.188 0 .341-.162.341-.362s-.153-.363-.341-.363H1.893c-.188 0-.341.162-.341.363s.153.362.341.362m0 1.865H6.72c.188 0 .341-.163.341-.363s-.153-.362-.341-.362H1.893c-.188 0-.341.162-.341.362s.153.363.341.363m0 1.862H6.72c.188 0 .341-.162.341-.363s-.153-.362-.341-.362H1.893c-.188 0-.341.162-.341.362s.153.363.341.363m0 1.866H6.72c.188 0 .341-.162.341-.363s-.153-.362-.341-.362H1.893c-.188 0-.341.162-.341.362s.153.363.341.363m0 1.863H6.72c.188 0 .341-.162.341-.362s-.153-.363-.341-.363H1.893c-.188 0-.341.162-.341.363s.153.362.341.362M25 21.677v3.261c0 .2-.153.363-.342.363H.342c-.189 0-.342-.162-.342-.363v-14.24c0-.14.077-.27.197-.328l5.631-2.789V5.912c0-.118.055-.23.146-.298L13.098.366a.33.33 0 0 1 .475.09l3.462 5.247c.04.06.061.133.061.208v1.086l1.121-.555a.33.33 0 0 1 .381.067l3.852 3.927a.37.37 0 0 1 .105.261v5.08c.512.443.84 1.116.84 1.87 0 .657-.25 1.254-.652 1.69h.052c1.215.002 2.205 1.05 2.205 2.34m-2.289-4.03c0-.93-.715-1.688-1.592-1.688-.88 0-1.593.758-1.593 1.689s.715 1.69 1.593 1.69 1.592-.758 1.592-1.69M18.703 7.622v11.853a2.1 2.1 0 0 1 .739-.136h.052a2.5 2.5 0 0 1-.652-1.69c0-1.33 1.021-2.414 2.276-2.414.264 0 .518.049.754.136v-4.517zm-1.607.175v12.058c.189.162.353.356.483.574.12-.202.27-.383.44-.537V7.34zm-3.461-5.981v13.458a2 2 0 0 1 .403-.039c1.256 0 2.277 1.083 2.277 2.414 0 .657-.25 1.255-.652 1.69h.053q.366.002.697.12V6.025zm.403 17.524c.878 0 1.593-.759 1.593-1.69s-.715-1.689-1.593-1.689c-.877 0-1.591.758-1.591 1.69 0 .93.715 1.689 1.591 1.689m-6.11-12L.685 10.93.684 24.575h7.243zm.579-.83 3.852 3.927a.37.37 0 0 1 .105.26v5.212c.145-.149.31-.277.487-.38V1.357L6.511 6.1v1.14l1.614-.8a.33.33 0 0 1 .382.067m.105 18.067h1.546v-2.899c0-1.29.99-2.338 2.205-2.338h.052a2.49 2.49 0 0 1-.635-1.985v-6.502l-3.168-3.23zm3.51 0h5.115v-2.899a1.64 1.64 0 0 0-.69-1.35 1.45 1.45 0 0 0-.831-.263h-3.353c-.838 0-1.521.724-1.521 1.613v2.899zm12.194-2.899c0-.89-.682-1.613-1.521-1.613h-3.353c-.327 0-.63.11-.878.297l-.016.012c-.38.294-.627.77-.627 1.304v2.899h6.394z"}))),O=({t:A,infos:e})=>r.jsx(j,{className:"infoSection",children:(0,r.jsxs)(v.ZP,{container:!0,spacing:2,children:[(0,r.jsxs)(v.ZP,{item:!0,xs:12,sm:6,md:6,className:"leftSide",children:[(0,r.jsxs)(j,{className:"item",children:[r.jsx(H,{className:"icon"}),(0,r.jsxs)(j,{children:[r.jsx(I,{className:"label",children:A("eventDetails:sector")}),r.jsx(I,{className:"value",children:e.sector})]})]}),(0,r.jsxs)(j,{className:"item",children:[r.jsx(F,{className:"icon"}),(0,r.jsxs)(j,{children:[r.jsx(I,{className:"label",children:A("eventDetails:countryConcerned")}),(0,r.jsxs)(I,{className:"value",children:[" ",e.countryConcerned]})]})]})]}),r.jsx(v.ZP,{item:!0,xs:0,sm:1,md:1,className:"divider"}),(0,r.jsxs)(v.ZP,{item:!0,xs:12,sm:6,md:5,className:"right-side",children:[(0,r.jsxs)(j,{className:"item",children:[r.jsx(y,{className:"icon"}),(0,r.jsxs)(j,{children:[r.jsx(I,{className:"label",children:A("eventDetails:location")}),r.jsx(I,{className:"value",children:e.country})]})]}),(0,r.jsxs)(j,{className:"item",children:[r.jsx(Q,{className:"icon"}),(0,r.jsxs)(j,{children:[r.jsx(I,{className:"label",children:A("eventDetails:organiser")}),r.jsx(I,{className:"value",children:e.organiser})]})]})]})]})});async function P({language:A}){let{t:e}=await (0,g.Z)(A,["eventDetailsLeap","eventDetails","event","global"]),t={sector:e("eventDetailsLeap:sectorValue"),country:e("eventDetailsLeap:locationValue"),countryConcerned:e("eventDetailsLeap:countryConcernedValue"),organiser:e("eventDetailsLeap:organiserValue")},a=[{title:e("event:events.eventLibya.title"),eventDate:e("event:events.eventLibya.eventDate"),postingDate:e("event:events.eventLibya.postingDate"),exactPlace:e("event:events.eventLibya.exactPlace"),link:"Libyan-French-economic-forum-2025",type:"events",image:f.Z},{title:e("event:events.event11.title"),eventDate:e("event:events.event11.eventDate"),postingDate:e("event:events.event11.postingDate"),exactPlace:e("event:events.event11.exactPlace"),type:"events",link:"leap-tech-conference-2025-riyadh",image:R.Z},{title:e("event:events.event2.title"),eventDate:e("event:events.event2.eventDate"),postingDate:e("event:events.event2.postingDate"),exactPlace:e("event:events.event2.exactPlace"),link:"pentabell-salon-sme-and-european-microwave-week",type:"blog",image:x.Z}];return r.jsx("div",{id:"event-page",children:r.jsx("div",{id:"event-detail",children:(0,r.jsxs)(u.Z,{className:"custom-max-width",children:[r.jsx(O,{t:e,infos:t}),(0,r.jsxs)("div",{className:"details",children:[(0,r.jsxs)("p",{className:"text",children:["  ",e("eventDetails:description")," "]}),r.jsx("p",{className:"heading-h1",children:e("eventDetailsLeap:aboutEvent:aboutEvent")}),r.jsx("p",{className:"text",children:e("eventDetailsLeap:aboutEvent:description1")}),r.jsx(p,{slides:[E,w,q,U,V,B],options:{},slidesMobile:[C,M,X,W,S,D]}),r.jsx("p",{className:"heading-h1",children:e("eventDetailsLeap:moreEvents")})]}),r.jsx(v.ZP,{className:"more-events-section",container:!0,rowSpacing:0,columnSpacing:3,children:a?.map((e,t)=>r.jsx(G.Z,{eventData:e,language:A,isEvent:!0},t))})]})})})}let K=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\events\EventGitex.jsx#default`),T=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\events\EventQHSEEXPO.jsx#default`),Z=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\events\EventLibya.jsx#default`);var z=t(71159);let _=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\events\EventForumAfricaFrance.jsx#default`),$=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\events\PentabellSalesTraining.jsx#default`);var AA=t(40618);async function Ae({params:{locale:A,url:e}}){let t=`https://www.pentabell.com/${"en"!==A?`${A}/`:""}events/${e}`,a={fr:`https://www.pentabell.com/fr/events/${e}`,en:`https://www.pentabell.com/events/${e}`,"x-default":`https://www.pentabell.com/events/${e}`},{t:l}=await (0,g.Z)(A,["eventDetails","eventDetailsLeap","global","eventDetailsLibya","eventDetailsGitex","ForumAfricaFrance","PentabellSalestraining","QHSEEXPO"]);return{title:"franco-saudi-decarbonization-days"===e?l("eventDetails:metaTitle"):"leap-tech-conference-2025-riyadh"===e?l("eventDetailsLeap:metaTitle"):"Libyan-French-economic-forum-2025"===e?l("eventDetailsLibya:metaTitle"):"Gitex-africa-morocco-2025"===e?l("eventDetailsGitex:metaTitle"):"Africa-France-forum-on-ecological-and-energy-transition-2025"===e?l("ForumAfricaFrance:metaTitle"):"Pentabell-sales-training-and-workshop"===e?l("PentabellSalestraining:metaTitle"):"QHSE-EXPO-2025"===e?l("QHSEEXPO:metaTitle"):"",description:"franco-saudi-decarbonization-days"===e?l("eventDetails:metaDescription"):"leap-tech-conference-2025-riyadh"===e?l("eventDetailsLeap:metaDescription"):"Libyan-French-economic-forum-2025"===e?l("eventDetailsLibya:metaDescription"):"Gitex-africa-morocco-2025"===e?l("eventDetailsGitex:metaDescription"):"Africa-France-forum-on-ecological-and-energy-transition-2025"===e?l("eventDetailsGitex:metaDescription"):"Pentabell-sales-training-and-workshop"===e?l("PentabellSalestraining:metaDescription"):"QHSE-EXPO-2025"===e?l("QHSEEXPO:metaDescription"):"",alternates:{canonical:t,languages:a}}}let At=async({searchParams:A,params:e})=>{let a=(0,z.lazy)(()=>t.e(7336).then(t.bind(t,7336))),l=(0,d.headers)(),i=(0,d.headers)().get("user-agent")||"",n=/mobile/i.test(i),s=l.get("cookie");try{let A=await h.xk.get(`/events/${e.locale}/${e.url}`,{headers:{Cookie:s}}),t=A?.data?.data,l="franco-saudi-decarbonization-days"===e.url?"decarbonization":"leap-tech-conference-2025-riyadh"===e.url?"leap":"Libyan-French-economic-forum-2025"===e.url?"libya":"Gitex-africa-morocco-2025"===e.url?"Gitex":"Africa-France-forum-on-ecological-and-energy-transition-2025"===e.url?"AfricaFranceForum":"Pentabell-sales-training-and-workshop"===e.url?"PentabellSalestraining":"QHSE-EXPO-2025"===e.url?"QHSEEXPO":"",{t:i}=await (0,g.Z)(e.locale,["eventDetails","eventDetailsLeap","eventDetailsLibya","eventDetailsGitex","ForumAfricaFrance","event","country","PentabellSalestraining","QHSEEXPO"]);return["franco-saudi-decarbonization-days","leap-tech-conference-2025-riyadh","Libyan-French-economic-forum-2025","Gitex-africa-morocco-2025","Africa-France-forum-on-ecological-and-energy-transition-2025","Pentabell-sales-training-and-workshop","QHSE-EXPO-2025"].includes(e.url)||t||(0,m.redirect)("en"===e.locale?"/events/":`/${e.locale}/events/`),r.jsx("div",{id:"events-page",children:t?(0,r.jsxs)(r.Fragment,{children:[r.jsx(a,{eventData:t,language:e.locale,event:l}),"libya"===l?r.jsx(Z,{event:t,language:e.locale}):"leap"===l?r.jsx(P,{event:t,language:e.locale}):"Gitex"===l?r.jsx(K,{event:t,language:e.locale}):"AfricaFranceForum"===l?r.jsx(_,{event:t,language:e.locale}):"PentabellSalestraining"===l?r.jsx($,{event:t,language:e.locale}):"QHSEEXPO"===l?r.jsx(T,{event:t,language:e.locale}):r.jsx(b,{event:t,language:e.locale})]}):"franco-saudi-decarbonization-days"===e.url?(0,r.jsxs)(r.Fragment,{children:[r.jsx(a,{language:e.locale,event:l}),r.jsx(b,{language:e.locale})]}):"leap-tech-conference-2025-riyadh"===e.url?(0,r.jsxs)(r.Fragment,{children:[r.jsx(a,{language:e.locale,event:l}),r.jsx(P,{language:e.locale})]}):"Libyan-French-economic-forum-2025"===e.url?(0,r.jsxs)(r.Fragment,{children:[r.jsx(a,{language:e.locale,event:l}),r.jsx(Z,{language:e.locale})]}):"Africa-France-forum-on-ecological-and-energy-transition-2025"===e.url?(0,r.jsxs)(r.Fragment,{children:[r.jsx(a,{language:e.locale,event:l}),r.jsx(_,{language:e.locale})]}):"QHSE-EXPO-2025"===e.url?(0,r.jsxs)(r.Fragment,{children:[r.jsx(a,{language:e.locale,event:l}),r.jsx(T,{language:e.locale})]}):"Gitex-africa-morocco-2025"===e.url?(0,r.jsxs)(r.Fragment,{children:[r.jsx(a,{language:e.locale,event:l}),r.jsx(K,{language:e.locale})]}):"Pentabell-sales-training-and-workshop"===e.url?(0,r.jsxs)(r.Fragment,{children:[r.jsx(a,{language:e.locale,event:l}),r.jsx($,{language:e.locale,isMobileSSR:"mobile"==(n?"mobile":"desktop")})]}):null})}catch(A){["franco-saudi-decarbonization-days","leap-tech-conference-2025-riyadh","Libyan-French-economic-forum-2025","Gitex-africa-morocco-2025","Africa-France-forum-on-ecological-and-energy-transition-2025","Pentabell-sales-training-and-workshop","QHSE-EXPO-2025"].includes(e.url)||(0,m.redirect)("en"===e.locale?"/events/":`/${e.locale}/events/`)}},Aa=At;(0,AA.h)([Ae,At]),(0,c.j)("5d4e1745b359194dee0f653f2deecc494f54833a",Ae),(0,c.j)("bccf3e1378c81b1c82b03b218e3d6aedbbdabae2",At)},44957:(A,e,t)=>{"use strict";t.d(e,{xk:()=>i,yX:()=>l});var a=t(29712);let l=a.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),i=a.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"});a.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"}),a.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(A=>(A.responseType="blob",A),A=>Promise.reject(A))},67142:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>a});let a={src:"/_next/static/media/image1.47c080e4.png",height:325,width:588,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAARVBMVEWllHGJgm10ZF2zqaiSfmZ3bVecjXCtl362l2e5o3vYvYuQhWdbSTetjWOokmikmmpsX0/KtIiqhliOdVd7cnu6j1dpa4Vg0zxRAAAAAXRSTlP9g+pWxwAAAAlwSFlzAAALEwAACxMBAJqcGAAAACtJREFUeJwFwYcBACAIA7CCIO6t/59qAnOWJkXgWmhRe8bagap6wTvMXkb5FLABLxyGWXcAAAAASUVORK5CYII=",blurWidth:8,blurHeight:4}},44181:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>a});let a={src:"/_next/static/media/image2.59bcf008.png",height:325,width:296,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAICAMAAAAC2hU0AAAAVFBMVEWOkJF2eX+XmpvMspRaXGZFTVq/vbW/ppbKz8+ioJ+HiIpvcXWBenHU19aNiX3h4+FjZmp8cWKhimYpMz44Q08QHS66rqVRWF6YlY2PlIqrsKexrZ5KhUneAAAACXRSTlP+/////////f4sB9HIAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPUlEQVR4nAXBBwLAIAgEsAMBxd29/v/PJkhttJEMxGd9nw4p1C+54c449INZ3hWGGHTz6girChVGXGRmxw9MowIvQI+kgwAAAABJRU5ErkJggg==",blurWidth:7,blurHeight:8}},60419:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>a});let a={src:"/_next/static/media/image3.fdebff43.png",height:325,width:296,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAICAMAAAAC2hU0AAAAbFBMVEWAaWdOVFWrsqpydICRiX2jpJyLhHd0c2iAeXqJkpaTj4yll4+Fc4Wyp5GgiVN6cnVzbHugi4CwvL+WnJVkxZ6Vl4F+ln3DzL7DvaaYlZCamqiAf4usqJR9iJiOWlTArJuJd6BwWFxPX2+2fl+p7SevAAAACHRSTlP9/////////CitBBMAAAAJcEhZcwAACxMAAAsTAQCanBgAAABASURBVHicBcGFAcAwDAMwF5eUO2b8/8dJoHebtbbId9lNZ1BVsVG2uKJLaWB8fPDqepyZQqART/WeBIBlkko0P2xdAyKQaZhDAAAAAElFTkSuQmCC",blurWidth:7,blurHeight:8}},87881:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>a});let a={src:"/_next/static/media/image4.6160aaef.png",height:325,width:296,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAICAMAAAAC2hU0AAAAWlBMVEWsn41lVluXnKCJi5F0dnyXk5enl4WZk4uaoaygqbqOkp99cXWZhn6Cen2Ie5SllaFoXm9nX2NTUGKsusy/p4jT0cKzmX24vJCLj4KAd5KzqJGPhHpgXWN8Z1qjvlIsAAAAB3RSTlP7///////+tAH4TgAAAAlwSFlzAAALEwAACxMBAJqcGAAAAD5JREFUeJwFwYUBwCAQBLADXnCr2/5rNoFa760jKIclRIJoDN4R1j1fzAXbcdfWKvQsfc6OlN7PDAM8w2SRH0icAo4Ei94qAAAAAElFTkSuQmCC",blurWidth:7,blurHeight:8}},35349:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>a});let a={src:"/_next/static/media/image5.57d82acf.png",height:325,width:588,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAASFBMVEV+cWW4w6F0fX+kmYKppp7Fp49EUGKamZs6NiV+g32FdnBRSDWgd2mUc22thYRlboKroJDInZhzdXimm4ehi3VbXEuVoIVmcGEllNu6AAAACHRSTlP+/////////kgsYtwAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAsSURBVHicY2DnZGERYBZmYGAUY+LnZBXkZWAUZ2MTYuXjYRDl4ODgZuASAQAU9gE+vZekIQAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:4}},10331:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>a});let a={src:"/_next/static/media/image6.dd888aef.png",height:325,width:296,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAICAMAAAAC2hU0AAAAXVBMVEU1N1F6fItfY4F3dpVZW35na4l1eJBCRVGPkq+JjaaRm7KaoLJQYI96f6FGR29XWG6loKBrbG5lapN8d7tvj8GCkZlAZLslbrwAeek1VZJRZqJic89daJg1aM1MUF3gFdxXAAAACHRSTlP+////////+zhGllMAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA+SURBVHicBcEHAsAgCASwU8GC2+79/2eaQFVF1EAkM3mPzDYGZ3FfxxrnhOc7dwkL6E1sNgfU3koBpv43qhhM1wKX2EI07QAAAABJRU5ErkJggg==",blurWidth:7,blurHeight:8}},54600:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>a});let a={src:"/_next/static/media/leap1.712d294c.png",height:199,width:353,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAAUVBMVEWMmYeXoZZNiWODamixs6pbaGiTzofRv7m2pKWmqplUYFaTr5aijpO/j42dkYKPbnG/tqmmkpmir46uw5eEwH2Hi357p3R9iWl2rHN2aXskQD9qKMRTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAMUlEQVR4nAXBhQEAIAwDsALD3eX/Q0lAZG1vNUDKpdQoBswzzqE1jAfOTBHXPbGdyB8inQGi1QGZOAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:5}},89279:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>a});let a={src:"/_next/static/media/leap2.70a93da6.png",height:195,width:146,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAV1BMVEWOkKJkaohxcHBtc4RfX3KfobR7epNyd5B9hJ2JjK5PUWmWoLZ0eZw7PGguMU6Fh4eDfrxUWY54fKYvV5V1c4NARFFnb8Mpb9CHhH9LTVBZaZ8NZtBDe8DtNXG0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOElEQVR4nAXBBwLAIAgAsVNRQLv3+v87mwA5A+6mVnBV08Czfq2bOc53aRM1pC0Je71KP3DHOIr8MWcB/PzsQUQAAAAASUVORK5CYII=",blurWidth:6,blurHeight:8}},17601:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>a});let a={src:"/_next/static/media/leap3.01624981.png",height:199,width:353,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAARVBMVEWgj3eDfWi1kWCWh2KKfmW8qX+tnoLMsoN7a2ChhWB0YEubjW62rrO1oo/EqHhlWUZJOyxjV1JwcFZYVFp9fJenk4mpoW5gbGggAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAMElEQVR4nAXBhwEAIAgDsCoo4N7/n2qCgiDRSUaNTeACwzwZrcdQA/uUE86eerWPDxlFAVwWyOi0AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:5}},71155:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>a});let a={src:"/_next/static/media/leap4.4547b586.png",height:196,width:146,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAAY1BMVEV6eHWxtauqppKKdXaOhH6NnpeYk4+VkoOjnZRwcnSpnoM8X2FoV2mkoZ6FZmmrnYvHzrrBqpZrZYR5bot9Vla1e2JFTlCDj5WSkpWEhXKxubugilGReYNmvZJ9h5F4cGaQjZ4nsYiSAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nAXBBQLAIAwAsQMKLXN3+/8rl0Bp2hYwJC+KjNm7g97k1Mi8Pt92M4Vw5Z3GXFpeqrqLmR87tgJa7etHeAAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8}},45978:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>a});let a={src:"/_next/static/media/leap5.158c7206.png",height:196,width:146,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAMAAADtGH4KAAAATlBMVEXb4eZ1eHtqbXB1dXKIh4RVWmfDrp27uLGWlJXGxb8vOEqgoJ+Cf3iorrGKgHvR2eOlppXO1uCxq5+7wbs/R1N5blqJi5BASVKolnbCu7QwrwYYAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAN0lEQVR4nAXBhwHAIAzAMAMhA+je/z9aCYBlY4qIY6WmuWTBvZ9t57P6iKFvS6OjV81yo6O42Q8vogHCPcncBwAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8}},42579:(A,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>a});let a={src:"/_next/static/media/leap6.6d2ba5b2.png",height:197,width:351,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAASFBMVEU6NSV1foB6hIahjHWDfHFGUGO3wqClmoWqqJ9/c2aroZHHnJe/p49RSDVzdnmUcm2uhoVkbYHOqpGfdWmampxbXEtncWKWoYYelLG5AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAK0lEQVR4nAXBBwIAEAwEsENbam///6kE24YQvVfIM4NmapDLXHNfOACKkvsXTQE80NulnQAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:4}}};var e=require("../../../../../webpack-runtime.js");e.C(A);var t=A=>e(e.s=A),a=e.X(0,[8948,1479,1619,1336,4227,8077,6027,3718,4289,1692,9712,481,273,1812,3969,4903,1312],()=>t(15814));module.exports=a})();