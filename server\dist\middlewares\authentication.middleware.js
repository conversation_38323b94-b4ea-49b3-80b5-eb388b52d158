"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.persistConnectedUser = void 0;
const services_1 = require("@/utils/services");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const isAuthenticated = async (request, response, next) => {
    if (!request.user)
        return next(new http_exception_1.default(401, 'Unauthorized. Please Login!'));
    return next();
};
const persistConnectedUser = async (request, response, next) => {
    const { accessToken, refreshToken } = request.cookies;
    if (!accessToken || !refreshToken)
        return next();
    const { payload, expired } = await services_1.auth.verifyToken(accessToken, String(process.env.ACCESS_TOKEN_PRIVATE_KEY));
    if (payload) {
        request.user = payload;
        return next();
    }
    const { payload: refresh } = expired && refreshToken && (await services_1.auth.verifyToken(refreshToken, String(process.env.REFRESH_TOKEN_PRIVATE_KEY)));
    if (refresh) {
        const accessToken = await services_1.auth.generateToken({ _id: refresh._id, roles: refresh.roles }, String(process.env.ACCESS_TOKEN_PRIVATE_KEY), String(process.env.ACCESS_TOKEN_TIME));
        response.cookie('accessToken', accessToken, {
            maxAge: Number(process.env.COOKIE_MAX_AGE),
            httpOnly: true,
        });
        request.user = (await services_1.auth.verifyToken(accessToken, String(process.env.ACCESS_TOKEN_PRIVATE_KEY))).payload;
        return next();
    }
};
exports.persistConnectedUser = persistConnectedUser;
exports.default = isAuthenticated;
//# sourceMappingURL=authentication.middleware.js.map