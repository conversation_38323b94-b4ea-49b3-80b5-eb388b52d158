"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/features/stats/charts/CommentByCategory.jsx":
/*!*********************************************************!*\
  !*** ./src/features/stats/charts/CommentByCategory.jsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CommentByCategory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon2_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon2.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon2.svg\");\n/* harmony import */ var _components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/charts/CustomPieChart */ \"(app-pages-browser)/./src/components/charts/CustomPieChart.jsx\");\n/* harmony import */ var _stats_hooks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../stats.hooks */ \"(app-pages-browser)/./src/features/stats/stats.hooks.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction CommentByCategory(param) {\n    let { transformedCategories, pieCharts } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    const pieChart = {\n        title: t(\"statsDash:commentsByCategory\"),\n        dataset: getDataPieComments?.data?.map((comment)=>({\n                label: comment.category,\n                value: comment.total\n            })) || [],\n        colors: [\n            \"#673ab7\",\n            \"#009688\",\n            \"#8bc34a\",\n            \"#ffc107\",\n            \"#ff9800\",\n            \"#ffc107\",\n            \"#3f51b5\",\n            \"#009688\",\n            \"#4caf50\",\n            \"#03a9f4\",\n            \"#ff9800\",\n            \"#8bc34a\",\n            \"#673ab7\"\n        ]\n    };\n    const [filteredCategories, setFilteredCategories] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [approve, setApprove] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dateFromComment, setDateFromComment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"2024-09-01\");\n    const [dateToComment, setDateToComment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchComment, setSearchComment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const resetSearchComments = ()=>{\n        setDateToComment(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromComment(\"2024-09-01\");\n        setFilteredCategories([]);\n        setApprove(true);\n        setCategories([]);\n        setSearchComment(!searchComment);\n    };\n    const getDataPieComments = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_7__.useGetCommentsStat)({\n        dateFrom: dateFromComment,\n        dateTo: dateToComment,\n        approve: approve,\n        categories: filteredCategories\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getDataPieComments.refetch();\n    }, [\n        searchComment\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        className: \"card\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"heading-h3\",\n                    gutterBottom: true,\n                    children: pieCharts[3].title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    elevation: 0,\n                    disableGutters: true,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            \"aria-controls\": \"panel1bh-content\",\n                            id: \"panel1bh-header\",\n                            className: \"svg-accordion\",\n                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                lineNumber: 98,\n                                columnNumber: 25\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"label-pentabell\",\n                                children: t(\"statsDash:filters\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            elevation: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                container: true,\n                                className: \"chart-grid\",\n                                spacing: 1,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"blue-text\",\n                                                    children: [\n                                                        \" \",\n                                                        t(\"statsDash:categories\"),\n                                                        \" :\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    multiple: true,\n                                                    id: `category`,\n                                                    options: transformedCategories ? transformedCategories : [],\n                                                    getOptionLabel: (option)=>option.name,\n                                                    value: categories.length > 0 ? transformedCategories.filter((category)=>categories.includes(category.name)) : [],\n                                                    onChange: (event, selectedOptions)=>{\n                                                        const categoryNames = selectedOptions.map((category)=>category.name);\n                                                        setCategories(categoryNames);\n                                                        setFilteredCategories(categoryNames.join(\",\"));\n                                                    },\n                                                    renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            ...params,\n                                                            className: \"\",\n                                                            variant: \"standard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        md: 12,\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"blue-text\",\n                                                children: [\n                                                    t(\"statsDash:approvedComments\"),\n                                                    \" :\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"select-pentabell blue-text\",\n                                                value: approve,\n                                                defaultValue: \"\",\n                                                onChange: (event)=>setApprove(event.target.value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        value: \"\",\n                                                        selected: true,\n                                                        disabled: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                            children: t(\"statsDash:approvedComments\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"blue-text\",\n                                                        value: \"\",\n                                                        children: t(\"statsDash:all\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"blue-text\",\n                                                        value: true,\n                                                        children: t(\"statsDash:approved\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"blue-text\",\n                                                        value: false,\n                                                        children: t(\"statsDash:notApproved\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            label: t(\"statsDash:fromDate\"),\n                                            type: \"date\",\n                                            value: dateFromComment,\n                                            onChange: (e)=>setDateFromComment(e.target.value),\n                                            fullWidth: true,\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            label: t(\"statsDash:toDate\"),\n                                            type: \"date\",\n                                            value: dateToComment,\n                                            onChange: (e)=>setDateToComment(e.target.value),\n                                            fullWidth: true,\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 3,\n                                        sm: 1,\n                                        md: 4,\n                                        className: \"btns-filter dashboard\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon2_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            className: \"btn btn-outlined btn-refresh full-width\",\n                                            onClick: resetSearchComments\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 11,\n                                        sm: 11,\n                                        md: 8,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            text: t(\"statsDash:filter\"),\n                                            onClick: ()=>{\n                                                setSearchComment(!searchComment);\n                                            },\n                                            className: \"btn btn-outlined btn-filter-stat full-width\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"chart-wrapper\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            donuts: true,\n                            chart: pieCharts[3]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(CommentByCategory, \"1ggJSwuZnVdzcMCH+wNE9uJZq+U=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_7__.useGetCommentsStat\n    ];\n});\n_c = CommentByCategory;\nvar _c;\n$RefreshReg$(_c, \"CommentByCategory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/stats/charts/CommentByCategory.jsx\n"));

/***/ })

});