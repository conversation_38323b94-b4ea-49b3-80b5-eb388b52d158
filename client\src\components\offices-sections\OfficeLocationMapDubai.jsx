import { Contain<PERSON>, <PERSON><PERSON>, <PERSON> } from "@mui/material";
import CustomButton from "../ui/CustomButton";
import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";
import { websiteRoutesList } from "@/helpers/routesList";

function OfficeLocationMapDubai({ t }) {
  return (
    <Container id="office-location-map" className="custom-max-width">
      <Grid
        className="container"
        justifyContent="space-between"
        container
        spacing={0}
      >
        <Grid item xs={12} sm={6}>
          <div className="content">
            <p className="heading-h2 text-white">
              {t("dubai:officeLocation:label")}
            </p>
            <p className="sub-heading text-white">
              {t("dubai:officeLocation:title")}
            </p>
            <div>
              <p className="paragraph text-white" key={"tn"}>
                <span>
                  <SvglocationPin />
                </span>
                {t("dubai:officeLocation:address")}
              </p>
              <p className="paragraph text-white">
                <span>
                  <SvgcallUs />
                </span>
                +971 (04) 4876 0672
              </p>

              <p className="paragraph text-white">
                <span>
                  <Svgemail />
                </span>
                <EMAIL>
              </p>
            </div>
            <Link
              href={`#service-page-form`}
              className={"btn btn-outlined white"}
            >
              {t("Tunisia:officeLocation:talk")}
            </Link>
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <div className="map-frame">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d115651.58473888831!2d55.13805!3d25.064192!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e5f6da910664d5f%3A0x5aacb31b12e9c24e!2sInternational%20Recruitment%20%26%20Staffing%20Agency%20Dubai%20%7C%20Pentabell%20Dubai!5e0!3m2!1sfr!2sus!4v1728638020968!5m2!1sfr!2sus"
              priority
              referrerpolicy="no-referrer-when-downgrade"
            ></iframe>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default OfficeLocationMapDubai;
