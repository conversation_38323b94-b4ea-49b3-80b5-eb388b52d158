"use strict";exports.id=9504,exports.ids=[9504],exports.modules={51156:(e,t,a)=>{a.d(t,{Z:()=>r});var i,n=a(95746);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var i in a)({}).hasOwnProperty.call(a,i)&&(e[i]=a[i])}return e}).apply(null,arguments)}let r=e=>n.createElement("svg",l({xmlns:"http://www.w3.org/2000/svg",width:22,height:22,fill:"none"},e),i||(i=n.createElement("path",{stroke:"#1E1E1E",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 2a2.827 2.827 0 1 1 4 4L6.5 19.5 1 21l1.5-5.5z"})))},71227:(e,t,a)=>{a.d(t,{Z:()=>s});var i,n,l=a(95746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var i in a)({}).hasOwnProperty.call(a,i)&&(e[i]=a[i])}return e}).apply(null,arguments)}let s=e=>l.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:24,height:18,fill:"none"},e),i||(i=l.createElement("path",{stroke:"#1E1E1E",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M1 9s4-8 11-8 11 8 11 8-4 8-11 8S1 9 1 9"})),n||(n=l.createElement("path",{stroke:"#1E1E1E",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 12a3 3 0 1 0 0-6 3 3 0 0 0 0 6"})))},23426:(e,t,a)=>{a.d(t,{Z:()=>l});var i=a(10326);a(17577);var n=a(99063);let l=function(e){let{children:t,value:a,index:l,...r}=e;return i.jsx("div",{role:"tabpanel",hidden:a!==l,id:`simple-tabpanel-${l}`,"aria-labelledby":`simple-tab-${l}`,...r,children:a===l&&i.jsx(n.default,{sx:{p:3},children:t})})}},32741:(e,t,a)=>{a.d(t,{Z:()=>N});var i=a(10326),n=a(17577),l=a(52210),r=a(51156),s=a(71227);a(18296),a(75486),a(71542);var o=a(39997),d=a(54815),p=a(84715),c=a(38447),u=a(33832),g=a(56390),h=a(15897),m=a(37841),A=a(70580),y=a(86851),b=a(72909),f=a(28236),x=a(31026),v=a(98089),E=a(90397),j=a(90308),w=a(83450),S=a(15082);a(78974);var C=a(26949);let N=({IdCandidat:e})=>{let{t,i18n:a}=(0,l.$G)(),N=e=>new Date(e)>new Date;o.Z,d.Z,p.Z,c.Z,u.Z;let[O,R]=(0,n.useState)(6),[P,Z]=(0,n.useState)(1),[k,L]=(0,n.useState)(""),[T,z]=(0,n.useState)(""),[D,F]=(0,n.useState)(null),[I,U]=(0,n.useState)("desc"),[M,Q]=(0,n.useState)(),[W,$]=(0,n.useState)("");JSON.parse(localStorage.getItem("user"));let[X,_]=(0,n.useState)(!1),[J,V]=(0,n.useState)(null),[B,G]=(0,n.useState)({pageSize:5,page:0}),H=(e,t)=>{let a=e.target.value;"edit"===a?handleEdit(t):"archive"===a&&handleDeleteOpportiniteConfirmation(t)},K=(0,b.JL)(e,{pageSize:B.pageSize,pageNumber:B.page+1,sortOrder:I,isActive:M,status:W,searchQuery:T,jobTitle:T});(0,n.useEffect)(()=>{K.refetch()&&F(K.data)},[K.data,B,I,M,W,T]);let Y=async()=>{try{await q(J)}catch(e){}_(!1)},q=async e=>{try{await A.default.delete(`api/v1/applications/${e}`),K.refetch()}catch(e){console.error("Error cancelling application:",e)}};if(K.isLoading)return i.jsx(y.Z,{});let ee=D?.applications?.map((e,t)=>({id:e._id,job:e?.opportunity?.versions[a.language]?.title,date:f.p6(e.createdAt),status:e.status,opportunityStatus:N(e?.opportunity?.dateOfExpiration)?"Active":"Expired",actions:e._id})),et=[{field:"job",headerName:t("application:job"),flex:1,minWidth:130,renderCell:e=>{let n=D?.applications.find(t=>t._id===e.id),l=n?.opportunity?.versions[a.language]?.title;return l?(0,i.jsxs)("div",{className:"job-title-cell",children:[(0,f.y9)(n?.opportunity?.industry),i.jsx("span",{children:(0,w.U)(l)})]}):i.jsx("span",{children:t("application:noTitle")})}},{field:"date",headerName:t("application:date"),flex:1,minWidth:0,headerClassName:"datagrid-header hide-on-mobile",cellClassName:"datagrid-cell hide-on-mobile"},{field:"status",headerName:t("application:status"),cellClassName:"datagrid-cell hide-on-mobile",headerClassName:"datagrid-header hide-on-mobile",minWidth:0,flex:1,renderCell:e=>(0,i.jsxs)(i.Fragment,{children:["Pending"===e.value&&(0,i.jsxs)("span",{children:[i.jsx("img",{src:x.Z.src,alt:"pending-icon",style:{marginRight:"10px"},loading:"lazy"}),t("application:pending")]}),"Accepted"===e.value&&(0,i.jsxs)("span",{children:[i.jsx("img",{src:v.Z.src,alt:"accepted-icon",style:{marginRight:"10px"},loading:"lazy"}),t("application:accepted")]}),"Rejected"===e.value&&(0,i.jsxs)("span",{children:[i.jsx("img",{src:x.Z.src,alt:"rejected-icon",style:{marginRight:"10px"},loading:"lazy"}),t("application:rejected")]})]})},{field:"actions",headerName:t("candidatures:actions"),headerClassName:"datagrid-header",cellClassName:"datagrid-cell",flex:1,renderCell:e=>{let a=e.row.id;return(0,i.jsxs)(g.Z,{value:"",onChange:e=>H(e,a),displayEmpty:!0,input:i.jsx(h.ZP,{}),style:{width:"100%"},renderValue:()=>t("candidatures:actions"),children:[i.jsx(m.Z,{value:"detail",children:i.jsx(S.default,{text:t("global:detail"),icon:i.jsx(s.Z,{}),leftIcon:!0,CandidaturesList:!0,link:`/backoffice/applications${C.Z.detail}/${a}`,className:"btn btn-ghost edit-blog"})}),i.jsx(m.Z,{value:"update",children:i.jsx(S.default,{text:t("global:update"),icon:i.jsx(r.Z,{}),leftIcon:!0,link:`/backoffice/applications/edit/${a}`,className:"btn btn-ghost edit-blog"})})]})}}];return i.jsx(i.Fragment,{children:i.jsx("div",{id:"container",className:"my-applications-pentabell",children:i.jsx("div",{style:{"align-items":"stretch"},className:"main-content",children:(0,i.jsxs)("div",{children:[i.jsx("div",{style:{height:"100%",width:"100%",padding:"20px 0px"},children:i.jsx(j._,{getRowHeight:()=>"auto",autosizeOptions:{includeOutliers:!0},sx:{"& .job-title-cell":{py:.5}},localeText:{noRowsLabel:"No result found."},rows:ee,columns:et,pagination:!0,paginationMode:"server",pageSizeOptions:[5,10,25],paginationModel:B,onPaginationModelChange:G,rowCount:D?.totalApplications||0,className:"pentabell-table"})}),X&&i.jsx(E.Z,{message:t("messages:cancelapplication"),onClose:()=>{_(!1)},onConfirm:Y})]})})})})}},78974:(e,t,a)=>{a.d(t,{nR:()=>p,gn:()=>o,UQ:()=>d});var i=a(2994),n=a(70580);let l=e=>new Promise(async(t,a)=>{try{let a=await n.xk.get("/applications",{params:{paginated:e.paginated,pageSize:e.pageSize,pageNumber:e.pageNumber,searchQuery:e.searchQuery,applicationDateFrom:e.applicationDateFrom,applicationDateTo:e.applicationDateTo,status:e.status,jobTitle:e.jobTitle,opportunity:e.opportunity}});t(a.data)}catch(e){a(e)}}),r=e=>new Promise(async(t,a)=>{try{let a=await n.xk.get(`/applications/${e}`);t(a.data)}catch(e){a(e)}}),s=e=>new Promise(async(t,a)=>{try{let a=await n.yX.get(`/candidates/byid/${e}`);t(a.data)}catch(e){a(e)}}),o=e=>(0,i.useQuery)("candidatures",async()=>await l(e)),d=e=>(0,i.useQuery)(["candidatures",e],async()=>await r(e)),p=e=>(0,i.useQuery)(["candidate",e],async()=>await s(e))},26949:(e,t,a)=>{a.d(t,{Z:()=>i});let i={home:"/",REGISTER:"/register",DASHBOARD:"/dashboard",UPDATE_PROFILE:"update-profile-informations",COMPLETE_PROFILE:"complete-profile-informations",SETTINGS:"settings",MY_APPLICATION:"myapplications",FORGETPASSWORD:"/forgot-password",RESETPASSWORD:"/reset-password/:token",RECENTAPPLICATIONS:"recent-applications",favoris:"/favoris",resend:"/resend-activation",confirmApplicationNotConnected:"/confirm-application",activation:"/activation/:token",blog:"/blog",blogFR:"/:language/blog",previewArticleFr:"/:language/blog/:urlArticle",previewArticleEn:"/blog/:urlArticle",previewFrO:"/:language/opportunities/:urlOpportunity",previewEnO:"/opportunities/:urlOpportunity",similar:"/RecommandedJobs",opportunities:"/opportunities",opportunitiesFR:"/:language/opportunities",opportunitiesPage:"/:language/opportunities?industry",applicationreceived:"/application-received",addArticle:"/add",editArticle:"/edit",listArticlesEN:"/articles",listArticleFr:"/:language/articles",activation:"/activation-account",addCategory:"/add",editCategory:"/edit",listCategoryEN:"/list-category",listcategoryFR:"/:language/list-category",categoryEN:"/blog/category/:urlCategory",categoryFR:"/:language/blog/category/:urlCategory",addOpportunity:"/add-opportunity",listOpportunities:"/list-opportunities",editOpportunity:"/edit-opportunity",updateprofile:"/update-profile",comments:"/comments",archived:"/archived",addUser:"/addUser",editUser:"/editUser",listUsers:"/listrtUsers",updateProfileCommun:"/update-profile",detail:"/detail"}},67140:(e,t,a)=>{a.d(t,{Z:()=>i});let i={src:"/_next/static/media/profilePic.8b6bd61b.png",height:106,width:106,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAKlBMVEXj7vro8/zn8vxMaXHf6fzr9/3m7vzm8//e7Pnp9P71///V4vO+z+fC0unW5GIrAAAACXRSTlP57bAAL7EtLS8soLJfAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAN0lEQVR4nEXKSQ6AQAzEQHcyC+kM//8u4gI3S2VyB8RKhlxlXUwDOKD63F36Y5rul745V0ix8wExcQFpDJCAxQAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8}}};