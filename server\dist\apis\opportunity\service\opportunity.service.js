"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const mongoose_1 = require("mongoose");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const opportunity_model_1 = __importDefault(require("../model/opportunity.model"));
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
const functions_1 = require("@/utils/helpers/functions");
class OpportunityService {
    constructor() {
        this.Opportunity = opportunity_model_1.default;
        this.experienceMapping = {
            'Entry Level': { min: 0, max: 2 },
            Intermediate: { min: 2, max: 10 },
            Expert: { min: 10, max: Infinity },
        };
    }
    async deleteOpportunityByLanguageAndId(language, opportunityId) {
        const foundOpportunity = await this.Opportunity.findById(opportunityId);
        if (!foundOpportunity)
            throw new http_exception_1.default(404, messages_1.MESSAGES.OPPORTUNITY.NOT_FOUND);
        delete foundOpportunity.versions[language];
        await foundOpportunity.save();
        if (Object.keys(foundOpportunity.versions).length === 0)
            await this.Opportunity.findByIdAndDelete(opportunityId);
    }
    async getOpportunityByUrl(language, url) {
        const opportunity = await this.Opportunity.findOne({ [`versions.${language}.url`]: url }, {
            versions: 1,
            industry: 1,
            country: 1,
            contractType: 1,
            reference: 1,
            dateOfExpiration: 1,
            dateOfRequisition: 1,
        });
        if (!opportunity)
            throw new http_exception_1.default(404, messages_1.MESSAGES.OPPORTUNITY.NOT_FOUND);
        return opportunity;
    }
    async get(opportunityId) {
        const opportunity = await this.Opportunity.findById(opportunityId).lean();
        if (!opportunity)
            throw new http_exception_1.default(404, messages_1.MESSAGES.OPPORTUNITY.NOT_FOUND);
        return opportunity;
    }
    async getAll(queries) {
        const { paginated = 'true', keyWord, language = 'en', sortOrder, industry, contractType, genre, createdAt, country, isPublished, pageNumber = 1, pageSize = 6, visibility, minExperience, maxExperience, jobDescriptionLanguages, reference, opportunityType, exclude, levelOfExperience, } = queries;
        const queryConditions = this.buildQueryConditions({
            language,
            exclude,
            createdAt,
            reference,
            opportunityType,
            genre,
            industry,
            contractType,
            country,
            isPublished,
            visibility,
            levelOfExperience,
            minExperience,
            maxExperience,
            jobDescriptionLanguages,
        });
        let opportunitySearchRequest = queryConditions;
        let opportunityMetadata = {};
        if (keyWord) {
            if (keyWord.toUpperCase().includes('H-') || keyWord.toUpperCase().includes('C-')) {
                queryConditions['reference'] = RegExp(`.*${keyWord}.*`, 'i');
            }
            else {
                opportunitySearchRequest = { $text: { $search: keyWord }, ...queryConditions };
                opportunityMetadata = { score: { $meta: 'textScore' } };
            }
        }
        const sortCriteria = {
            ...opportunityMetadata,
            [`versions.${language}.createdAt`]: sortOrder === 'asc' ? 1 : -1,
        };
        const skip = (Number(pageNumber) - 1) * Number(pageSize);
        const limit = Number(pageSize);
        const opportunities = await this.Opportunity.find(opportunitySearchRequest, opportunityMetadata)
            .sort(sortCriteria)
            .skip(skip)
            .limit(limit)
            .select('title industry country status reference opportunityType contractType expirationDate minExperience maxExperience dateOfExpiration createdAt versions')
            .lean();
        const filteredOpportunities = this.processOpportunities(opportunities);
        if (paginated === 'false') {
            return filteredOpportunities;
        }
        const totalOpportunities = await this.Opportunity.countDocuments(opportunitySearchRequest);
        const totalPages = Math.ceil(totalOpportunities / limit);
        return {
            pageNumber: Number(pageNumber),
            pageSize: limit,
            totalPages,
            totalOpportunities,
            opportunities: filteredOpportunities,
        };
    }
    buildQueryConditions({ language, exclude, createdAt, reference, opportunityType, genre, industry, contractType, country, isPublished, visibility, levelOfExperience, minExperience, maxExperience, jobDescriptionLanguages, }) {
        const queryConditions = {};
        if (exclude === 'true') {
            queryConditions['contractType'] = { $nin: ['Internship'] };
            queryConditions['opportunityType'] = { $nin: [constants_1.OpportunityType.IN_HOUSE] };
        }
        if (createdAt) {
            const date = new Date(createdAt);
            queryConditions['createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }
        if (reference) {
            queryConditions['reference'] = new RegExp(reference, 'i');
        }
        if (opportunityType) {
            queryConditions['opportunityType'] = opportunityType;
        }
        if (genre) {
            queryConditions[`versions.${language}.gender`] = genre;
        }
        if (industry) {
            queryConditions['industry'] = {
                $in: industry.split(',').map(i => i.trim()),
            };
        }
        if (contractType) {
            queryConditions['contractType'] = {
                $in: contractType.split(',').map(ct => ct.trim()),
            };
        }
        if (country) {
            queryConditions['country'] = new RegExp(country, 'i');
        }
        if (isPublished !== undefined) {
            queryConditions['isPublished'] = isPublished;
        }
        if (visibility) {
            queryConditions[`versions.${language}.visibility`] = visibility;
        }
        this.addExperienceFilters(queryConditions, levelOfExperience, minExperience, maxExperience);
        if (jobDescriptionLanguages) {
            const regex = jobDescriptionLanguages.split(',').map(lang => new RegExp(lang, 'i'));
            queryConditions[`versions.${language}.jobDescription`] = { $in: regex };
        }
        return queryConditions;
    }
    addExperienceFilters(queryConditions, levelOfExperience, minExperience, maxExperience) {
        if (!levelOfExperience && !minExperience && !maxExperience) {
            return;
        }
        queryConditions.$and = queryConditions.$and ?? [];
        this.addPredefinedExperienceLevel(queryConditions, levelOfExperience);
        this.addExplicitExperienceValues(queryConditions, minExperience, maxExperience);
    }
    addPredefinedExperienceLevel(queryConditions, levelOfExperience) {
        if (!levelOfExperience || !this.experienceMapping[levelOfExperience]) {
            return;
        }
        const { min, max } = this.experienceMapping[levelOfExperience];
        if (min !== undefined) {
            queryConditions.$and.push({ minExperience: { $gte: min } });
        }
        if (max !== Infinity) {
            queryConditions.$and.push({ maxExperience: { $lte: max } });
        }
    }
    addExplicitExperienceValues(queryConditions, minExperience, maxExperience) {
        if (!minExperience && !maxExperience) {
            return;
        }
        const mappedMin = this.mapExperienceValue('min', minExperience);
        const mappedMax = this.mapExperienceValue('max', maxExperience);
        if (mappedMin !== undefined) {
            queryConditions.$and.push({ minExperience: { $gte: mappedMin } });
        }
        if (mappedMax !== undefined && mappedMax !== Infinity) {
            queryConditions.$and.push({ maxExperience: { $lte: mappedMax } });
        }
    }
    mapExperienceValue(type, value) {
        if (typeof value === 'string' && this.experienceMapping[value]) {
            return this.experienceMapping[value][type];
        }
        else if (typeof value === 'number') {
            return value;
        }
        return undefined;
    }
    processOpportunities(opportunities) {
        return opportunities
            .map(opportunity => {
            const versions = opportunity?.versions ?? {};
            const existingLanguages = Object.keys(versions);
            return {
                ...opportunity,
                existingLanguages,
            };
        })
            .filter(opportunity => {
            const versions = opportunity?.versions ?? {};
            return Object.keys(versions).length > 0;
        });
    }
    async getDistinctCountries() {
        try {
            return (await opportunity_model_1.default.distinct('country'));
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async toggleArchivedOppportunity(language, opportunityId, archive) {
        const opportunity = await this.get(opportunityId);
        opportunity.versions[language].isArchived = archive;
        await opportunity.save();
    }
    async archiveOpportunity(opportunityId) {
        const opportunity = await this.get(opportunityId);
        constants_1.languages.forEach(lang => {
            opportunity.versions[lang].isArchived = true;
            opportunity.versions[lang].visibility = constants_1.Visibility.Private;
        });
        await opportunity.save();
    }
    async getAllUrgentLatest(queries) {
        const { keyWord, industry, contractType, genre, createdAt, country, urgent, isPublished, visibility, minExperience, maxExperience, jobDescriptionLanguages, language = constants_1.Language.ENGLISH, levelOfExperience, reference, opportunityType, exclude, } = queries;
        const queryConditions = this.buildQueryConditions({
            language,
            exclude,
            createdAt,
            reference,
            opportunityType,
            genre,
            industry,
            contractType,
            country,
            isPublished,
            visibility,
            levelOfExperience,
            minExperience,
            maxExperience,
            jobDescriptionLanguages,
        });
        queryConditions['dateOfExpiration'] = { $gt: new Date() };
        if (urgent !== undefined) {
            queryConditions['urgent'] = true;
        }
        if (language) {
            queryConditions[`versions.${language}.language`] = language;
        }
        const opportunitySearchRequest = keyWord ? { $text: { $search: keyWord }, ...queryConditions } : queryConditions;
        const opportunityMetadata = keyWord ? { score: { $meta: 'textScore' } } : {};
        const sortCriteria = {
            ...opportunityMetadata,
            createdAt: -1,
        };
        const opportunities = await this.Opportunity.find(opportunitySearchRequest, opportunityMetadata)
            .sort(sortCriteria)
            .limit(4)
            .select('title industry country status contractType reference minExperience maxExperience dateOfExpiration urgent latest createdAt versions')
            .lean();
        return this.processOpportunities(opportunities);
    }
    async updateMainOpportunityFields(opportunityId, updateData) {
        await this.get(opportunityId);
        return await this.Opportunity.findByIdAndUpdate(opportunityId, { $set: updateData }, { new: true });
    }
    async delete(opportunityId) {
        await this.get(opportunityId);
        await this.Opportunity.findByIdAndDelete(opportunityId);
    }
    async updateOpportuniteBylanguageandId(language, opportunityId, updateData) {
        await this.get(opportunityId);
        return await this.Opportunity.findByIdAndUpdate(opportunityId, { [`versions.${language}`]: { ...updateData, updatedAt: new Date() } }, { new: true });
    }
    async addVersionToOpportunity(opportunityId, newVersion) {
        const existingOpportunity = await this.Opportunity.findById(opportunityId);
        if (!existingOpportunity)
            throw new http_exception_1.default(404, messages_1.MESSAGES.OPPORTUNITY.NOT_FOUND);
        newVersion.url = this.createJobUrl(newVersion.title, existingOpportunity.reference);
        newVersion.alt = newVersion.alt ?? newVersion.title;
        existingOpportunity.versions[newVersion.language] = newVersion;
        const updatedOpportunity = await existingOpportunity.save();
        return updatedOpportunity;
    }
    async getSlugBySlug(language, url) {
        const targetLanguage = language === 'en' ? 'fr' : 'en';
        const opportunity = await this.Opportunity.findOne({
            [`versions.${language}.language`]: language,
            [`versions.${language}.url`]: url,
        });
        if (!opportunity)
            throw new http_exception_1.default(404, messages_1.MESSAGES.OPPORTUNITY.NOT_FOUND);
        return {
            slug: opportunity.versions.get(targetLanguage).url,
        };
    }
    async updateOpportunityVersion(opportunityId, language, versionData) {
        const existingOpportunity = await this.get(opportunityId);
        if (existingOpportunity.versions[language]) {
            return await this.updateOpportuniteBylanguageandId(language, opportunityId, versionData);
        }
        else {
            return await this.addVersionToOpportunity(opportunityId, {
                language: language,
                title: versionData.title,
                jobDescription: versionData.jobDescription,
                metaTitle: versionData.metaTitle,
                metaDescription: versionData.metaDescription,
                shareOnSocialMedia: versionData.shareOnSocialMedia,
                _id: new mongoose_1.Types.ObjectId(),
                createdAt: new Date(),
                updatedAt: new Date(),
                visibility: versionData.visibility,
                createdBy: versionData.createdBy,
                isArchived: versionData.isArchived,
            });
        }
    }
    async fetchOpportunities() {
        try {
            const response = await axios_1.default.get(process.env.OPENING_LINK, {
                headers: {
                    Accept: 'application/json',
                    Cookie: `accessToken=${process.env.HUNTER_ACCESS_TOKEN}; refreshToken=${process.env.HUNTER_REFRESH_TOKEN}`,
                },
            });
            if (response.status !== 200 || !response.data.length)
                return [];
            return response.data;
        }
        catch (error) {
            console.error('Error fetching opportunities: ', error.message);
            return [];
        }
    }
    mapIndustry(industry) {
        const industries = {
            'IT & TELECOM': 'It & Telecom',
            TRANSPORT: 'Transport',
            ENERGIES: 'Energies',
            OTHERS: 'Others',
            BANKING: 'Banking',
            PHARMACEUTICAL: 'Pharmaceutical',
        };
        return industries[industry] ?? 'Others';
    }
    createJobUrl(jobTitle, reference) {
        return `${jobTitle
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .trim()
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')}-${reference.toLowerCase()}`;
    }
    logToFile(message, overwrite = false) {
        const logDir = path_1.default.join(__dirname, '../../../../logs/cron_jobs/opportunity');
        const fileName = path_1.default.join(logDir, `${new Date().toISOString().substring(0, 10)}.log`);
        if (!fs_1.default.existsSync(logDir)) {
            fs_1.default.mkdirSync(logDir, { recursive: true });
        }
        if (!fs_1.default.existsSync(fileName)) {
            fs_1.default.writeFileSync(fileName, '');
        }
        overwrite === true ? fs_1.default.writeFileSync(fileName, `${message}\n`) : fs_1.default.appendFileSync(fileName, `${message}\n`);
    }
    async importOpportunitiesFromHunter() {
        try {
            const opportunities = await this.fetchOpportunities();
            if (!opportunities.length)
                return;
            this.logToFile(`Received ${opportunities.length} opportunities`, true);
            for (const opportunity of opportunities) {
                if (this.shouldSkipOpportunity(opportunity))
                    continue;
                const existingOpportunity = await this.Opportunity.findOne({ reference: opportunity.reference });
                const newOpportunity = await this.buildOpportunityData(opportunity, existingOpportunity);
                await this.saveOrUpdateOpportunity(existingOpportunity, newOpportunity, opportunity.reference);
                await (0, functions_1.delay)(2000);
            }
            this.updateOpportunitySitemap();
        }
        catch (error) {
            console.error('Error importing opportunities: ', error.message);
        }
    }
    shouldSkipOpportunity(opportunity) {
        const { project, compensation, reference } = opportunity;
        if (!compensation?.recruiters?.length && project.opportunityType !== constants_1.OpportunityType.IN_HOUSE) {
            this.logToFile(`Skipping ${reference}: No recruiters assigned`);
            return true;
        }
        if (project?.opportunityType === 'Capability') {
            this.logToFile(`Skipping ${reference}: Capability`);
            return true;
        }
        return false;
    }
    async buildOpportunityData(opportunity, existingOpportunity) {
        const { project, requirement, reference } = opportunity;
        const jobDescriptions = await this.getJobDescriptions(project, requirement, existingOpportunity);
        return {
            versions: this.buildVersions(project, reference, jobDescriptions, opportunity),
            opportunityType: project.opportunityType,
            dateOfExpiration: project.deadline ? new Date(project.deadline) : new Date(new Date().setMonth(new Date().getMonth() + 3)),
            industry: project.openingIndustry ? this.mapIndustry(project.openingIndustry) : this.mapIndustry(project.industry),
            country: project.country,
            reference,
            contractType: project.contractType.join(' ').trim(),
            urgent: opportunity.isUrgent,
            minExperience: Number(requirement?.yearsOfExperience?.split('-')[0]) || 2,
            maxExperience: Number(requirement?.yearsOfExperience?.split('-')[1]) || 15,
        };
    }
    async getJobDescriptions(project, requirement, existingOpportunity) {
        return {
            fr: existingOpportunity
                ? existingOpportunity?.versions['fr']?.jobDescription ?? existingOpportunity.versions.get('fr').jobDescription
                : await (0, functions_1.generateJobDescription)(this.mapIndustry(project.industry), project.jobTitle, Number(requirement?.yearsOfExperience?.split('-')[0]), 'fr'),
            en: existingOpportunity
                ? existingOpportunity?.versions['en']?.jobDescription ?? existingOpportunity.versions.get('en').jobDescription
                : await (0, functions_1.generateJobDescription)(this.mapIndustry(project.industry), project.jobTitle, Number(requirement?.yearsOfExperience?.split('-')[0])),
        };
    }
    buildVersions(project, reference, jobDescriptions, opportunity) {
        return {
            fr: {
                alt: this.createJobUrl(project.jobTitle, reference),
                language: constants_1.Language.FRENCH,
                title: project.jobTitle,
                jobDescription: jobDescriptions['fr'],
                shareOnSocialMedia: opportunity.toPublishOnSocialMedia,
                url: this.createJobUrl(project.jobTitle, reference),
                metaTitle: `Apply now for the ${project.jobTitle} job.`,
                metaDescription: `Explore the ${project.jobTitle} opportunity.`,
                createdAt: opportunity.createdAt,
                updatedAt: opportunity.updatedAt,
                visibility: constants_1.Visibility.Public,
            },
            en: {
                alt: this.createJobUrl(project.jobTitle, reference),
                language: constants_1.Language.ENGLISH,
                title: project.jobTitle,
                jobDescription: jobDescriptions['en'],
                shareOnSocialMedia: opportunity.toPublishOnSocialMedia,
                url: this.createJobUrl(project.jobTitle, reference),
                metaTitle: `Apply now for the ${project.jobTitle} job.`,
                metaDescription: `Explore the ${project.jobTitle} opportunity.`,
                createdAt: opportunity.createdAt,
                updatedAt: opportunity.updatedAt,
                visibility: constants_1.Visibility.Public,
            },
        };
    }
    async saveOrUpdateOpportunity(existingOpportunity, newOpportunity, reference) {
        if (existingOpportunity) {
            await this.Opportunity.findByIdAndUpdate(existingOpportunity._id, { $set: newOpportunity });
            this.logToFile(`Updated ${reference}`);
        }
        else {
            await this.Opportunity.create(newOpportunity);
            this.logToFile(`Created ${reference}`);
        }
    }
    async updateOpportunitySitemap(newOpportunity) {
        try {
            const staticUrls = [
                {
                    loc: 'https://www.pentabell.com/opportunities/',
                    lastmod: new Date().toISOString(),
                    alternates: [
                        { hreflang: 'en-US', href: 'https://www.pentabell.com/opportunities/' },
                        { hreflang: 'fr-FR', href: 'https://www.pentabell.com/fr/opportunities/' },
                    ],
                },
                {
                    loc: 'https://www.pentabell.com/fr/opportunities/',
                    lastmod: new Date().toISOString(),
                    alternates: [
                        { hreflang: 'en-US', href: 'https://www.pentabell.com/opportunities/' },
                        { hreflang: 'fr-FR', href: 'https://www.pentabell.com/fr/opportunities/' },
                    ],
                },
            ];
            const openings = newOpportunity ? [...(await this.Opportunity.find()), newOpportunity] : await this.Opportunity.find();
            let sitemapContent = '<?xml version="1.0" encoding="UTF-8"?>\n';
            sitemapContent += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"\n';
            sitemapContent += '        xmlns:xhtml="http://www.w3.org/1999/xhtml">\n';
            staticUrls.forEach(entry => {
                sitemapContent += '  <url>\n';
                sitemapContent += `    <loc>${entry.loc}</loc>\n`;
                sitemapContent += `    <lastmod>${entry.lastmod}</lastmod>\n`;
                sitemapContent += `    <xhtml:link rel='canonical' href='${entry.loc}' />\n`;
                entry.alternates.forEach(alt => {
                    sitemapContent += `    <xhtml:link rel='alternate' hreflang='${alt.hreflang}' href='${alt.href}' />\n`;
                });
                sitemapContent += '  </url>\n';
            });
            console.log(openings[0].versions.get('fr'));
            openings.forEach((opening) => {
                ['en', 'fr'].forEach(language => {
                    if (opening.versions?.get(language)?.url &&
                        !opening.versions?.get(language)?.isArchived &&
                        opening.versions?.get(language)?.visibility === constants_1.Visibility.Public) {
                        sitemapContent += '  <url>\n';
                        sitemapContent += `    <loc>https://www.pentabell.com/${language === 'fr' ? 'fr/' : ''}opportunities/${opening.versions?.get(language)?.url}/</loc>\n`;
                        sitemapContent += `    <lastmod>${new Date(opening.versions?.get(language)?.updatedAt).toISOString()}</lastmod>\n`;
                        sitemapContent += `    <xhtml:link rel='canonical' href='https://www.pentabell.com/${language === 'fr' ? 'fr/' : ''}opportunities/${opening.versions?.get(language)?.url}/' />\n`;
                        ['en', 'fr'].forEach(altLang => {
                            if (opening.versions?.get(language)?.url &&
                                !opening.versions?.get(language)?.isArchived &&
                                opening.versions?.get(language)?.visibility === constants_1.Visibility.Public) {
                                sitemapContent += `    <xhtml:link rel='alternate' hreflang='${altLang === 'en' ? 'en-US' : 'fr-FR'}' href='https://www.pentabell.com/${altLang === 'fr' ? 'fr/' : ''}opportunities/${opening.versions?.get(language)?.url}/' />\n`;
                            }
                        });
                        sitemapContent += '  </url>\n';
                    }
                });
            });
            sitemapContent += '</urlset>';
            const filePath = path_1.default.join(__dirname, '../../../../../client/public/sitemap_opportunity.xml');
            fs_1.default.writeFileSync(filePath, sitemapContent, 'utf-8');
            console.log(`Sitemap has been generated at ${filePath}`);
            console.log(openings.length > 1 ? `With ${openings.length} opportunities` : `With ${openings.length} opportunity`);
        }
        catch (error) {
            console.error('Error creating opportunity sitemap: ', error.message);
        }
    }
    async updateJobDescriptions() {
        const openings = await this.Opportunity.find({
            $or: [{ 'versions.fr.jobDescription': /N\/A/i }, { 'versions.en.jobDescription': /N\/A/i }],
        });
        console.log(`Found ${openings.length} openings needing job descriptions`);
        for (const opening of openings) {
            try {
                opening.versions.get('fr').jobDescription = await (0, functions_1.generateJobDescription)(opening.industry, opening.versions.get('fr').title, opening.minExperience, 'fr');
                console.log(`Updated JD for ${opening.versions.get('fr').language} version of (${opening.reference}) (${opening.versions.get('fr').title})`);
                opening.versions.get('en').jobDescription = await (0, functions_1.generateJobDescription)(opening.industry, opening.versions.get('en').title, opening.minExperience, 'en');
                console.log(`Updated JD for ${opening.versions.get('en').language} version of (${opening.reference}) (${opening.versions.get('en').title})`);
            }
            catch (err) {
                console.error(`Failed to generate JD for (${opening.reference}): ${err.message}`);
            }
            await opening.save();
        }
    }
}
exports.default = OpportunityService;
//# sourceMappingURL=opportunity.service.js.map