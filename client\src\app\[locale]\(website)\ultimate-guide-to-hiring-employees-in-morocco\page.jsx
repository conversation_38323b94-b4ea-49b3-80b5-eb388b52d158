import banner from "@/assets/images/Maroc/Pentabell-Morocco.webp";
import BannerComponents from "@/components/sections/BannerComponents";
import OurPartners from "@/components/sections/OurPartners";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import GlobalHRServicesSection from "@/components/sections/GlobalHRServicesSection";
import r1 from "@/assets/images/services/recrutement1.png";
import r2 from "@/assets/images/services/recrutement2.png";
import r3 from "@/assets/images/services/recrutement3.png";
import r4 from "@/assets/images/services/recrutement4.png";
import serviceImg from "@/assets/images/services/service1.png";
import TunisiaOfficePageForm from "@/features/forms/components/TunisiaOfficePageForm";
import ComplexityControlSection from "@/components/offices-sections/ComplexityControlSection";
import OfficeInfoMaroc from "@/components/offices-sections/OfficeInfoMaroc";
import BusinessInMaroc from "@/components/offices-sections/BusinessInMaroc";
import EORServiceMaroc from "@/components/offices-sections/EORServicesMaroc";
import OfficeLocationMapMaroc from "@/components/offices-sections/OfficeLocationMapMaroc";
import initTranslations from "@/app/i18n";
import MoroccoLaborLaws from "../../../../components/labor-laws/MoroccoLaborLaws";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJsonSSR } from "@/config/axios";
import serviceimgS1 from "@/assets/images/services/service1.png";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }ultimate-guide-to-hiring-employees-in-morocco/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/ultimate-guide-to-hiring-employees-in-morocco/`,
    en: `https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-morocco/`,
    "x-default": `https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-morocco/`,
  };

  const { t } = await initTranslations(locale, ["servicesByCountry"]);
  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/ultimate-guide-to-hiring-employees-in-morocco`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }

  return {
    title: t("servicesByCountry:morocco:metaTitle"),
    description: t("servicesByCountry:morocco:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

async function hiringEmployeesMarocGuide({ params: { locale } }) {
  const { t } = await initTranslations(locale, ["Tunisia", "morocco"]);
  const SERVICES = [
    {
      id: "s1",
      title: "Payroll Services",
      description:
        "Streamline payroll processes to ensure accuracy and compliance while enhancing employee satisfaction and engagement.",
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      linkText: "Check service",
      img: serviceImg,
      altImg: t("Tunisia:services:dataS1:altImg"),
    },
    {
      id: "s2",
      title: "Consulting RH",
      description:
        "Provide expert guidance in human resource management. Focusing on talent acquisition, performance management, and employee development strategies.",
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
      linkText: "Check service",
      img: r4,
      altImg: t("Tunisia:services:dataS2:altImg"),
    },
    {
      id: "s3",
      title: "Technical Assistance",
      description:
        "Provide ongoing support throughout the development and continuity of your project to ensure its success and sustainability",
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
      linkText: "Check service",
      img: r3,
      altImg: t("Tunisia:services:dataS3:altImg"),
    },
    {
      id: "s4",
      title: "IA Sourcing",
      description:
        "Identify top talent with AI-driven algorithms that analyze extensive data to align skills, experience, and potential cultural fit.",
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
      linkText: "Check service",
      img: r2,
      altImg: t("Tunisia:services:dataS4:altImg"),
    },
    {
      id: "s5",
      title: "Direct Hire",
      description:
        "Find and hire highly qualified candidates for specific job roles within your organization.Including recruitment, assessment, and onboarding processes.",
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      linkText: "Check service",
      img: r1,
      altImg: t("Tunisia:services:dataS5:altImg"),
    },
  ];

  return (
    <div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            name: t("contactUs:bureux:contacts:morocco"),
            address: {
              "@type": "PostalAddress",
              streetAddress: "Zenith 1, Sidi maarouf, lot CIVIM",
              addressLocality: "Casablanca",
              postalCode: "20270",
              addressCountry: "MA",
            },
            telephone: "+212 5 22 78 63 66",
            email: "<EMAIL>",
            url:
              locale === "en"
                ? "https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-morocco/"
                : `https://www.pentabell.com/${locale}/ultimate-guide-to-hiring-employees-in-morocco/`,
          }),
        }}
      />
      <BannerComponents
        title={t("morocco:title")}
        description={t("morocco:description")}
        bannerImg={banner}
        height={"100vh"}
        altImg={t("morocco:altImg")}
      />
      <OurPartners disableTxt={true} />
      <ResponsiveRowTitleText
        title={t("morocco:intro:title")}
        paragraph={t("morocco:intro:description")}
        paragraph2={t("morocco:intro:description2")}
      />
      <OfficeInfoMaroc t={t} />
      <BusinessInMaroc t={t} />
      <EORServiceMaroc t={t} />
      <OfficeLocationMapMaroc t={t} />
      <ComplexityControlSection t={t} />
      <GlobalHRServicesSection
        title={
          "Our HR services in Morocco Payroll, Recruitment & HR Consulting"
        }
        SERVICES={SERVICES}
        defaultImage={serviceimgS1}
      />
      <MoroccoLaborLaws />
      <TunisiaOfficePageForm country={"Morocco"} defaultCountryPhone="ma" />
    </div>
  );
}

export default hiringEmployeesMarocGuide;
