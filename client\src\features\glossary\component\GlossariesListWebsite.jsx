"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import SearchIcon from "@mui/icons-material/Search";
import BookIcon from "@mui/icons-material/Book";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import CustomButton from "@/components/ui/CustomButton";

// Empty state component for no glossaries
const EmptyGlossaryState = ({
  isEmpty,
  isEmptySearch,
  searchWord,
  locale,
  t,
}) => {
  if (isEmpty) {
    return (
      <Box
        className="empty-glossary-state"
        sx={{
          textAlign: "center",
          py: 8,
          px: 4,
          minHeight: "400px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <BookIcon
          sx={{
            fontSize: 80,
            color: "text.secondary",
            mb: 3,
            opacity: 0.5,
          }}
        />
        <Typography
          variant="h4"
          component="h2"
          gutterBottom
          sx={{
            fontWeight: 600,
            color: "text.primary",
            mb: 2,
          }}
        >
          {t?.("glossary:emptyState:title") || "No Glossary Terms Available"}
        </Typography>
        <Typography
          variant="body1"
          color="text.secondary"
          sx={{
            maxWidth: 600,
            mb: 4,
            lineHeight: 1.6,
          }}
        >
          {t?.("glossary:emptyState:description") ||
            "We're currently building our glossary. Check back soon for comprehensive definitions and explanations of industry terms."}
        </Typography>
        <CustomButton
          text={
            t?.("glossary:emptyState:exploreButton") || "Explore Our Services"
          }
          link={locale === "fr" ? "/fr/services" : "/services"}
          className="btn btn-filled"
        />
      </Box>
    );
  }

  if (isEmptySearch) {
    return (
      <Box
        className="empty-search-state"
        sx={{
          textAlign: "center",
          py: 8,
          px: 4,
          minHeight: "400px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <SearchIcon
          sx={{
            fontSize: 80,
            color: "text.secondary",
            mb: 3,
            opacity: 0.5,
          }}
        />
        <Typography
          variant="h4"
          component="h2"
          gutterBottom
          sx={{
            fontWeight: 600,
            color: "text.primary",
            mb: 2,
          }}
        >
          {t?.("glossary:searchEmpty:title") || "No Results Found"}
        </Typography>
        <Typography
          variant="body1"
          color="text.secondary"
          sx={{
            maxWidth: 600,
            mb: 2,
            lineHeight: 1.6,
          }}
        >
          {t?.("glossary:searchEmpty:description") ||
            `No glossary terms found for "${searchWord}". Try searching with different keywords or browse all terms.`}
        </Typography>
        <Box
          sx={{
            mt: 3,
            display: "flex",
            gap: 2,
            flexWrap: "wrap",
            justifyContent: "center",
          }}
        >
          <CustomButton
            text={t?.("glossary:searchEmpty:clearButton") || "Clear Search"}
            link={locale === "fr" ? "/fr/glossaries" : "/glossaries"}
            className="btn btn-outline"
          />
          <CustomButton
            text={
              t?.("glossary:searchEmpty:browseButton") || "Browse All Terms"
            }
            link={locale === "fr" ? "/fr/glossaries" : "/glossaries"}
            className="btn btn-filled"
          />
        </Box>
      </Box>
    );
  }

  return null;
};

// Error state component
const ErrorGlossaryState = ({ error, locale, t }) => (
  <Box
    className="error-glossary-state"
    sx={{
      textAlign: "center",
      py: 8,
      px: 4,
      minHeight: "400px",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
    }}
  >
    <ErrorOutlineIcon
      sx={{
        fontSize: 80,
        color: "error.main",
        mb: 3,
        opacity: 0.7,
      }}
    />
    <Typography
      variant="h4"
      component="h2"
      gutterBottom
      sx={{
        fontWeight: 600,
        color: "text.primary",
        mb: 2,
      }}
    >
      {t?.("glossary:error:title") || "Unable to Load Glossary"}
    </Typography>
    <Typography
      variant="body1"
      color="text.secondary"
      sx={{
        maxWidth: 600,
        mb: 4,
        lineHeight: 1.6,
      }}
    >
      {t?.("glossary:error:description") ||
        "We're experiencing technical difficulties loading the glossary. Please try again later."}
    </Typography>
    <Alert severity="error" sx={{ mb: 3, maxWidth: 500 }}>
      {error}
    </Alert>
    <CustomButton
      text={t?.("glossary:error:retryButton") || "Try Again"}
      onClick={() => window.location.reload()}
      className="btn btn-filled"
    />
  </Box>
);

export default function GlossaryListWebsite({
  glossaries,
  locale,
  error,
  isEmpty,
  isEmptySearch,
  searchWord,
  t,
}) {
  const { t: fallbackT } = useTranslation();
  const translationFunction = t || fallbackT;

  const letters = Object.keys(glossaries || {});
  const [expandedLetters, setExpandedLetters] = useState({});

  const handleToggle = (letter) => {
    setExpandedLetters((prev) => ({
      ...prev,
      [letter]: !prev[letter],
    }));
  };

  // Handle error state
  if (error) {
    return (
      <div id="glossary-page">
        <Container className="custom-max-width">
          <ErrorGlossaryState
            error={error}
            locale={locale}
            t={translationFunction}
          />
        </Container>
      </div>
    );
  }

  // Handle empty states
  if (isEmpty || isEmptySearch) {
    return (
      <div id="glossary-page">
        <Container className="custom-max-width">
          <EmptyGlossaryState
            isEmpty={isEmpty}
            isEmptySearch={isEmptySearch}
            searchWord={searchWord}
            locale={locale}
            t={translationFunction}
          />
        </Container>
      </div>
    );
  }

  // Render glossary content
  return (
    <div id="glossary-page">
      <Container className="custom-max-width">
        {/* Search results header */}
        {searchWord && letters.length > 0 && (
          <Box sx={{ mb: 4, textAlign: "center" }}>
            <Typography variant="h5" component="h2" gutterBottom>
              {translationFunction?.("glossary:searchResults:title") ||
                `Search Results for "${searchWord}"`}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {translationFunction?.("glossary:searchResults:count") ||
                `Found ${letters.reduce(
                  (total, letter) => total + (glossaries[letter]?.length || 0),
                  0
                )} terms`}
            </Typography>
          </Box>
        )}

        <Grid container spacing={3}>
          {letters?.length > 0 &&
            letters?.map((letter, index) => {
              const letterGlossaries = glossaries[letter] || [];
              const isExpanded = expandedLetters[letter] || false;
              const displayedGlossaries = isExpanded
                ? letterGlossaries
                : letterGlossaries.slice(0, 5);

              return (
                <Grid
                  item
                  lg={3}
                  md={4}
                  sm={6}
                  xs={12}
                  key={index}
                  className="letters"
                  id={letter}
                >
                  <div className="letter-section">
                    <p className="length">{letterGlossaries.length}</p>
                    <p className="letter">{letter}</p>
                    <div className="words">
                      {displayedGlossaries.map((glossary, glossaryIndex) => (
                        <a
                          className="word"
                          key={`${glossary.url}-${glossaryIndex}`}
                          href={`${locale === "fr" ? "/fr" : ""}/glossaries/${
                            glossary.url
                          }`}
                          title={glossary.word}
                        >
                          {glossary.word}
                        </a>
                      ))}
                    </div>
                    {letterGlossaries.length > 5 && (
                      <Button
                        className="glossary-button"
                        onClick={() => handleToggle(letter)}
                        size="small"
                        variant="text"
                      >
                        {isExpanded
                          ? translationFunction?.("glossary:showLess") ||
                            "Show less"
                          : translationFunction?.("glossary:showMore") ||
                            "Show more"}
                      </Button>
                    )}
                  </div>
                </Grid>
              );
            })}
        </Grid>

        {/* Back to top button for long lists */}
        {letters.length > 8 && (
          <Box sx={{ textAlign: "center", mt: 6 }}>
            <Button
              variant="outlined"
              onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
            >
              {translationFunction?.("glossary:backToTop") || "Back to Top"}
            </Button>
          </Box>
        )}
      </Container>
    </div>
  );
}
