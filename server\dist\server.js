"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv = __importStar(require("dotenv"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const envFilePath = path_1.default.resolve(__dirname, `../.env.${process.env.NODE_ENV}`);
dotenv.config({ path: envFilePath });
require("module-alias/register");
const validateEnv_1 = __importDefault(require("./utils/validateEnv"));
const app_1 = __importDefault(require("./app"));
const auth_controller_1 = __importDefault(require("@/apis/auth/auth.controller"));
const user_controller_1 = __importDefault(require("@/apis/user/controllers/user.controller"));
const account_controller_1 = __importDefault(require("@/apis/user/controllers/account.controller"));
const client_controller_1 = __importDefault(require("./apis/client/controllers/client.controller"));
const manager_controller_1 = __importDefault(require("./apis/client/controllers/manager.controller"));
const skill_controller_1 = __importDefault(require("./apis/skill/skill.controller"));
const candidat_controller_1 = __importDefault(require("./apis/candidat/candidat.controller"));
const candidat_certification_controller_1 = __importDefault(require("./apis/candidat/certification/candidat.certification.controller"));
const candidate_experience_controller_1 = __importDefault(require("./apis/candidat/experience/candidate.experience.controller"));
const candidat_education_controller_1 = __importDefault(require("./apis/candidat/education/candidat.education.controller"));
const files_controller_1 = __importDefault(require("./apis/storage/files.controller"));
const opportunity_controller_1 = __importDefault(require("./apis/opportunity/controller/opportunity.controller"));
const candidate_shortlist_controller_1 = __importDefault(require("./apis/candidat/shortList/candidate.shortlist.controller"));
const recruiter_shortlist_controller_1 = __importDefault(require("./apis/user/controllers/recruiter.shortlist.controller"));
const article_controller_1 = __importDefault(require("./apis/article/article.controller"));
const article_category_controller_1 = __importDefault(require("./apis/article/category/article.category.controller"));
const seoOpportunity_controller_1 = __importDefault(require("./apis/opportunity/controller/seoOpportunity.controller"));
const alert_controller_1 = __importDefault(require("./apis/alert/alert.controller"));
const commentaire_controller_1 = __importDefault(require("./apis/article/commentaire/commentaire.controller"));
const favourite_controller_1 = __importDefault(require("./apis/Favourite/favourite.controller"));
const notications_controller_1 = require("./apis/notifications/notications.controller");
const newsletter_controller_1 = require("./apis/newsletter/newsletter.controller");
const contact_controller_1 = require("./apis/contact/contact.controller");
const settings_controller_1 = __importDefault(require("./apis/settings/settings.controller"));
const express_1 = require("express");
const authentication_middleware_1 = __importDefault(require("./middlewares/authentication.middleware"));
const authorization_middleware_1 = require("./middlewares/authorization.middleware");
const constants_1 = require("./utils/helpers/constants");
const cache_middleware_1 = require("./middlewares/cache.middleware");
const seoTags_controller_1 = __importDefault(require("./apis/seoTags/seoTags.controller"));
const statistics_controller_1 = require("./apis/statistics/statistics.controller");
const slide_controller_1 = __importDefault(require("./apis/slide/slide.controller"));
const guide_controller_1 = __importDefault(require("./apis/guide/guide.controller"));
const event_controller_1 = __importDefault(require("./apis/events/event.controller"));
const guide_category_controller_1 = __importDefault(require("./apis/guide/categoryguide/guide.category.controller"));
const glossaries_controller_1 = __importDefault(require("./apis/glossaries/glossaries.controller"));
const downloadsreport_controller_1 = __importDefault(require("./apis/Coprofiledownloadreport/downloadsreport.controller"));
(0, validateEnv_1.default)();
class OtherControllers {
    constructor() {
        this.path = '';
        this.router = (0, express_1.Router)();
        this.router.get('/logs', authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), async (request, response, next) => {
            try {
                const { date, fullName } = request.query;
                let LOG_FILE = `${process.env.NODE_ENV}-requests-${new Date().toISOString().substring(0, 10)}.log`;
                if (date)
                    LOG_FILE = `${process.env.NODE_ENV}-requests-${date}.log`;
                const LOG_FILE_PATH = path_1.default.join(__dirname, '../logs/', LOG_FILE);
                const logs = await fs_1.default.readFileSync(LOG_FILE_PATH, 'utf-8').split('\n');
                logs.pop();
                if (fullName) {
                    const regex = new RegExp(`.*${fullName}.*`, 'i');
                    const parsedLogs = logs.filter((log) => regex.test(log));
                    return response.json({ logs: parsedLogs });
                }
                return response.json({ logs });
            }
            catch (error) {
                next(error);
            }
        });
        this.router.get('/cache', authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), (request, response, next) => {
            try {
                response.send({ cache: (0, cache_middleware_1.getCachedData)() });
            }
            catch (error) {
                next(error);
            }
        });
        this.router.delete('/cache', authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), cache_middleware_1.invalidateCache, (request, response, next) => {
            try {
                response.send({ message: 'Cache cleared!' });
            }
            catch (error) {
                next(error);
            }
        });
    }
}
const app = new app_1.default([
    new OtherControllers(),
    new manager_controller_1.default(),
    new user_controller_1.default(),
    new client_controller_1.default(),
    new auth_controller_1.default(),
    new candidat_controller_1.default(),
    new candidat_certification_controller_1.default(),
    new candidate_experience_controller_1.default(),
    new candidat_education_controller_1.default(),
    new account_controller_1.default(),
    new notications_controller_1.NotificationController(),
    new user_controller_1.default(),
    new skill_controller_1.default(),
    new files_controller_1.default(),
    new opportunity_controller_1.default(),
    new downloadsreport_controller_1.default(),
    new guide_controller_1.default(),
    new commentaire_controller_1.default(),
    new slide_controller_1.default(),
    new candidate_shortlist_controller_1.default(),
    new recruiter_shortlist_controller_1.default(),
    new article_controller_1.default(),
    new article_category_controller_1.default(),
    new settings_controller_1.default(),
    new guide_category_controller_1.default(),
    new seoOpportunity_controller_1.default(),
    new alert_controller_1.default(),
    new newsletter_controller_1.NewsletterController(),
    new contact_controller_1.ContactController(),
    new favourite_controller_1.default(),
    new statistics_controller_1.StatisticsController(),
    new seoTags_controller_1.default(),
    new event_controller_1.default(),
    new glossaries_controller_1.default(),
], Number(process.env.APP_PORT));
app.listen();
//# sourceMappingURL=server.js.map