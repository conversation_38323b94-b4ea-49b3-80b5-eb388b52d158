@import "./fonts";
@import "./utils/mixins";
@import "./utils/variables";

@import "./layout/index.scss";
@import "./components/index.scss";
@import "./components/myResumes.scss";

p,
a,
h1,
h2,
h3,
div,
.css-rizt0-MuiTypography-root {
  font-family: "Proxima-Nova-Regular" !important;
}
html {
  scroll-behavior: smooth;
}
body {
  margin: 0;
  background-color: $lightBlue;
  font-family: "Proxima-Nova-Regular", sans-serif;
}
.text-center {
  text-align: center !important;
}

.text-justify {
  text-align: justify !important;
}
p {
  margin-block-start: 0;
  margin-block-end: 0;
}
// maxWidth={false} sx={{ maxWidth: '1300px', mx: 'auto' }}
.custom-max-width {
  max-width: 90% !important;
  margin-left: auto;
  margin-right: auto;
  @include media-query(mobile) {
    max-width: 100% !important;
  }
}
