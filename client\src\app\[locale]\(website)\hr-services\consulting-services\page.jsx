import BannerComponents from "@/components/sections/BannerComponents";
import banner from "../../../../../assets/images/website/banner/HR-Consulting-Pentabell-Services_1.webp";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import ServiceRow from "@/components/ui/ServiceRow";

import consultingServiceImgS1 from "../../../../../assets/images/services/consultingServiceImgS1.png";

import consultingServiceImgS2 from "../../../../../assets/images/services/consultingServiceImgS2.png";

import consultingServiceImgS3 from "../../../../../assets/images/services/consultingServiceImgS3.png";

import consultingServiceImgS4 from "../../../../../assets/images/services/consultingServiceImgS4.png";
import ApproachToConsulting from "@/components/services/ApproachToConsulting";
import HowItWorks from "@/components/services/HowItWorks";
import AskQuestions from "@/components/services/AskQuestions";
// import WhyPentabell from "@/components/services/WhyPentabell";
import ConsultingServicePageForm from "@/features/forms/components/ConsultingServicePageForm";
import initTranslations from "@/app/i18n";
import WhyPentabell from "@/components/services/WhyPentabell";
import { axiosGetJsonSSR } from "@/config/axios";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }hr-services/consulting-services/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/hr-services/consulting-services/`,
    en: `https://www.pentabell.com/hr-services/consulting-services/`,
    "x-default": `https://www.pentabell.com/hr-services/consulting-services/`,
  };

  const { t } = await initTranslations(locale, [
    "consultingServices",
    "global",
  ]);
  try {
    const encodedSlug = encodeURIComponent("hr-services/consulting-services");
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/${encodedSlug}`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("consultingServices:metaTitle"),
    description: t("consultingServices:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

async function consultingServices({ params: { locale } }) {
  const { t, resources } = await initTranslations(locale, [
    "consultingServices",
    "global",
  ]);

  const dataS1 = {
    label: t("consultingServices:dataS1:label"),
    title: t("consultingServices:dataS1:title"),
    paragraph: t("consultingServices:dataS1:paragraph"),
    altImg: t("consultingServices:dataS1:altImg"),
    featureImg: consultingServiceImgS1,
  };

  const dataS2 = {
    label: t("consultingServices:dataS2:label"),
    title: t("consultingServices:dataS2:title"),
    paragraph: t("consultingServices:dataS2:paragraph"),
    altImg: t("consultingServices:dataS2:altImg"),
    featureImg: consultingServiceImgS2,
  };

  const dataS3 = {
    label: t("consultingServices:dataS3:label"),
    title: t("consultingServices:dataS3:title"),
    paragraph: t("consultingServices:dataS3:paragraph"),
    altImg: t("consultingServices:dataS3:altImg"),
    featureImg: consultingServiceImgS3,
  };

  const dataS4 = {
    label: t("consultingServices:dataS4:label"),
    title: t("consultingServices:dataS4:title"),
    paragraph: t("consultingServices:dataS4:paragraph"),
    altImg: t("consultingServices:dataS4:altImg"),
    featureImg: consultingServiceImgS4,
  };

  const steps = [
    {
      title: t("consultingServices:howItWorks:steps:step1:title"),
      description: t("consultingServices:howItWorks:steps:step1:description"),
    },
    {
      title: t("consultingServices:howItWorks:steps:step2:title"),
      description: t("consultingServices:howItWorks:steps:step2:description"),
    },
    {
      title: t("consultingServices:howItWorks:steps:step3:title"),
      description: t("consultingServices:howItWorks:steps:step3:description"),
    },
  ];

  const ASK_QUESTIONS = [
    {
      id: "s1",
      title: t("consultingServices:questions:QA1:question"),
      description: t("consultingServices:questions:QA1:answer"),
    },
    {
      id: "s2",
      title: t("consultingServices:questions:QA2:question"),
      description: t("consultingServices:questions:QA2:answer"),
    },
    {
      id: "s3",
      title: t("consultingServices:questions:QA3:question"),
      description: t("consultingServices:questions:QA3:answer"),
    },
  ];
  const reasons = [
    {
      heading: t("consultingServices:whyPentabell:reasons:reason1:title"),
      text: t("consultingServices:whyPentabell:reasons:reason1:description"),
    },
    {
      heading: t("consultingServices:whyPentabell:reasons:reason2:title"),
      text: t("consultingServices:whyPentabell:reasons:reason2:description"),
    },
    {
      heading: t("consultingServices:whyPentabell:reasons:reason3:title"),
      text: t("consultingServices:whyPentabell:reasons:reason3:description"),
    },
    {
      heading: t("consultingServices:whyPentabell:reasons:reason4:title"),
      text: t("consultingServices:whyPentabell:reasons:reason4:description"),
    },
  ];

  return (
    <div>
      <BannerComponents
        title={t("consultingServices:intro:title")}
        bannerImg={banner}
        height={"70vh"}
        altImg={t("consultingServices:intro:altImg")}
      />
      <ResponsiveRowTitleText
        title={t("consultingServices:overview:title")}
        paragraph={t("consultingServices:overview:paragraph1")}
        paragraph2={t("consultingServices:overview:paragraph2")}
      />
      <h2 className="heading-h1 text-center text-banking mb-0">
        {t("consultingServices:howWeHelp")}
      </h2>
      <ServiceRow data={dataS1} />
      <ServiceRow data={dataS2} reverse={true} darkBg={true} />
      <ServiceRow data={dataS3} />
      <ServiceRow data={dataS4} reverse={true} darkBg={true} />
      <ApproachToConsulting locale={locale} />
      <HowItWorks steps={steps} locale={locale} />
      <WhyPentabell locale={locale} reasons={reasons} />;
      <AskQuestions
        title={t("consultingServices:questions:title")}
        ASK_QUESTIONS={ASK_QUESTIONS}
      />
      {/* <InsightsSection /> */}
      <ConsultingServicePageForm />
    </div>
  );
}
export default consultingServices;
