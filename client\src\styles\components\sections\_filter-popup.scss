.filter-popup {
  width: 100%;
  height: auto;
  transition: all 0.3s ease;
  overflow: hidden;
  max-height: 0;
  opacity: 0;

  &.open {
    max-height: 2000px;
    opacity: 1;
  }

  .filter-popup-content {
    padding: 15px;
  }

  .filter-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;

    .btn {
      width: 100%;
      justify-content: center;
      min-width: 100px;
    }
  }
}

.filter-toggle {
  margin-left: 15px;
}

@media (max-width: 768px) {
  .filter-popup {
    &.open {
      max-height: 3000px; // Larger for mobile as content stacks
    }
  }
}
