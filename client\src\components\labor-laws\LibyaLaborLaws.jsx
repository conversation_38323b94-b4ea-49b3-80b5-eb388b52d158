"use client";
import {
  Container,
  Grid,
  AccordionSummary,
  AccordionDetails,
  Accordion,
} from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import SvgexpandIcon from "../../assets/images/icons/arrowUp.svg";

import SvgCallendar from "@/assets/images/icons/yellow/yellowCalendar.svg";

export default function LibyaLaborLaws() {
  const [expanded, setExpanded] = useState(1);
  const { t } = useTranslation();
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  return (
    <div id="labor-tn-laws">
      <Container className="custom-max-width">
        <h2 className="heading-h1">{t("libya:libyaLabor:title")}</h2>

        <Grid className="container" container columnSpacing={3} rowSpacing={2}>
          <Grid item xs={12} sm={12} key={1}>
            <Accordion
              elevation={0}
              expanded={expanded === 1}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(1)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("libya:libyaLabor:workingHours:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                <p className="service-sub-title">
                    {t("libya:libyaLabor:workingHours:subTitle1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:workingHours:description")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("libya:libyaLabor:workingHours:subTitle2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:workingHours:description2")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={2}>
            <Accordion
              elevation={0}
              expanded={expanded === 2}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(2)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("libya:libyaLabor:employmentContracts:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("libya:libyaLabor:employmentContracts:title1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>
                      {t("libya:libyaLabor:employmentContracts:data1")}
                    </li>
                    <li>
                      {t("libya:libyaLabor:employmentContracts:data2")}
                    </li>
                  
                  </ul>
                </div>
              
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={4}>
            <Accordion
              elevation={0}
              expanded={expanded === 4}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(4)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("libya:libyaLabor:payroll:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("libya:libyaLabor:payroll:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:payroll:description")}
                  </p>
                </div>
                <div className="payroll-tn">
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("libya:libyaLabor:payroll:fiscalYear:title")}
                    </p>
                    <p className="date">
                      {t("libya:libyaLabor:payroll:fiscalYear:date1")}
                      <br />
                      {t("libya:libyaLabor:payroll:fiscalYear:date2")}
                    </p>
                    <p className="paragraph">
                      {t("libya:libyaLabor:payroll:fiscalYear:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("libya:libyaLabor:payroll:payrollCycle:title")}
                    </p>
                    <p className="date">
                      {t("libya:libyaLabor:payroll:payrollCycle:date")}
                    </p>
                    <p className="paragraph">
                      {t(
                        "libya:libyaLabor:payroll:payrollCycle:description"
                      )}
                    </p>
                  </div>

                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("libya:libyaLabor:payroll:minimumWage:title")}
                    </p>
                    <p className="date">
                      {t("libya:libyaLabor:payroll:minimumWage:wage")}
                      <br />
                      {t("libya:libyaLabor:payroll:minimumWage:date")}​
                    </p>
                    <p className="paragraph">
                      {t(
                        "libya:libyaLabor:payroll:minimumWage:description"
                      )}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t(
                        "libya:libyaLabor:payroll:payrollManagement:title"
                      )}
                    </p>
                    <p className="date">
                      {t(
                        "libya:libyaLabor:payroll:payrollManagement:date1"
                      )}
                    </p>
                    <p className="paragraph">
                      {t(
                        "libya:libyaLabor:payroll:payrollManagement:description"
                      )}
                    </p>
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={3}>
            <Accordion
              elevation={0}
              expanded={expanded === 3}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(3)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("libya:libyaLabor:termination:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("libya:libyaLabor:termination:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:termination:description1")}
                  </p>
                 
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("libya:libyaLabor:termination:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:termination:description2")}
                  </p> 
                  <ul className="service-description paragraph">
                    <li>{t("libya:libyaLabor:termination:data1")}</li>
                    <li>{t("libya:libyaLabor:termination:data2")}</li>
                    <li>{t("libya:libyaLabor:termination:data3")}</li>
                    <li>{t("libya:libyaLabor:termination:data4")}</li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("libya:libyaLabor:termination:title3")}
                  </p>
                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:termination:description3")}{" "}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("libya:libyaLabor:termination:title4")}
                  </p>
                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:termination:description4")}{" "}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={5}>
            <Accordion
              elevation={0}
              expanded={expanded === 5}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(5)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("libya:libyaLabor:leaveEntitlements:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                <p className="service-description paragraph">
                    {t("libya:libyaLabor:leaveEntitlements:description")}
                  </p>
                  <br/>
                  <p className="service-sub-title">
                    {t("libya:libyaLabor:leaveEntitlements:subTitle")}
                  </p>
                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:leaveEntitlements:subDescription")}
                  </p>
                  <div className="holidays-dates">
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS1:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS1:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS2:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS2:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS3:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS3:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS4:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS4:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS5:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS5:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS6:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS6:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS7:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS7:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS8:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS8:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS9:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS9:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS10:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS10:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS11:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "libya:libyaLabor:leaveEntitlements:leaves:dataS11:title"
                        )}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "libya:libyaLabor:leaveEntitlements:leaves:paidTimeOff:title"
                    )}
                  </p>
                  <p className="service-description paragraph">
                    {t(
                      "libya:libyaLabor:leaveEntitlements:leaves:paidTimeOff:description1"
                    )}
                  </p>
                 
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "libya:libyaLabor:leaveEntitlements:leaves:maternityLeave:title"
                    )}
                  </p>
                    <p className="service-description paragraph">
                      {t(
                        "libya:libyaLabor:leaveEntitlements:leaves:maternityLeave:description1"
                      )}
                    </p>
                   
                </div>
              

                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "libya:libyaLabor:leaveEntitlements:leaves:sickLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "libya:libyaLabor:leaveEntitlements:leaves:sickLeave:description"
                    )}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={6}>
            <Accordion
              elevation={0}
              expanded={expanded === 6}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(6)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("libya:libyaLabor:tax:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:tax:description")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("libya:libyaLabor:tax:title1")}
                  </p>

                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:tax:description1")}
                  </p>

                  <ul className="service-description paragraph">
                    <li> {t("libya:libyaLabor:tax:data1")}</li>
                  </ul>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {" "}
                    {t("libya:libyaLabor:tax:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:tax:description2")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("libya:libyaLabor:tax:dataS1")}</li>
                    <li>{t("libya:libyaLabor:tax:dataS2")}</li>
                    <li>{t("libya:libyaLabor:tax:dataS3")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>

          <Grid item xs={12} sm={12} key={7}>
            <Accordion
              elevation={0}
              expanded={expanded === 7}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(7)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("libya:libyaLabor:visa:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:visa:description1")}
                    <br />
                    {t("libya:libyaLabor:visa:description2")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("libya:libyaLabor:visa:title1")}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t("libya:libyaLabor:visa:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:visa:description3")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("libya:libyaLabor:visa:title3")}
                  </p>
                  <p className="service-description paragraph">
                    {t("libya:libyaLabor:visa:description4")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("libya:libyaLabor:visa:data1")}</li>
                    <li>{t("libya:libyaLabor:visa:data2")}</li>
                    <li>{t("libya:libyaLabor:visa:data3")}</li>
                    <li>{t("libya:libyaLabor:visa:data4")}</li>
                    <li>{t("libya:libyaLabor:visa:data5")}</li>
                    <li>{t("libya:libyaLabor:visa:data6")}</li>
                    <li>{t("libya:libyaLabor:visa:data7")}</li>
                    <li>{t("libya:libyaLabor:visa:data8")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}
