<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="/unauthorized.css">
  <title>Access Restricted</title>
</head>
<body>
  
  <div class="container">
    <h1>4<div class="lock"><div class="top"></div><div class="bottom"></div></div>3</h1>
    <p>Access denied</p>
    <!-- <div class="logo-container">
      <img src="/pentabell.png" alt="Company Logo">
    </div> -->
  </div>
  <script>
    const interval = 500;

    function generateLocks() {
      const lock = document.createElement('div'),
            position = generatePosition();
      lock.innerHTML = '<div class="top"></div><div class="bottom"></div>';
      lock.style.top = position[0];
      lock.style.left = position[1];
      lock.classList.add('lock');
      document.body.appendChild(lock);
      setTimeout(() => {
        lock.style.opacity = '1';
        lock.classList.add('generated');
      }, 100);
      setTimeout(() => {
        lock.parentElement.removeChild(lock);
      }, 2000);
    }

    function generatePosition() {
      const x = Math.round((Math.random() * 100) - 10) + '%';
      const y = Math.round(Math.random() * 100) + '%';
      return [x, y];
    }

    setInterval(generateLocks, interval);
    generateLocks();
  </script>
</body>
</html>
