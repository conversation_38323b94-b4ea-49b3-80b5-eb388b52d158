"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_en_settings_json";
exports.ids = ["_ssr_src_locales_en_settings_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/en/settings.json":
/*!**************************************!*\
  !*** ./src/locales/en/settings.json ***!
  \**************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"account":"Settings","changePwd":"Change password","currentPwd":"Current password","newPwd":"New password","confirmNewPwd":"Confirm new password","jobAlerts":"Job alerts","JobTitle":"Job Title","updateSuccessalerts":"Job Alerts updated successfully","updateSuccessnotifications":"Notifications updated successfully","typeofalerts":"type of alerts","industry":"industry","country":"country","newjobalerts":"New Job Alerts","supdatepassword":"Password updated successfully","appliedjobs":"Applied Jobs Status Updates","newsletter":"Pentabell Newsletter","website":"Website","changePassword":"Change Password"}');

/***/ })

};
;