"use client";
import { Grid, CircularProgress } from "@mui/material";
import OpportunityItem from "./OpportunityItem";
import CustomPagination from "@/components/CustomPagination";

const OpportunityList = ({
  opportunitiesData,
  language,
  pageNumber,
  handlePageChange,
  searchParamsContent,
  t,
  isList,
  isLoading,
}) => {
  const totalOpportunities = opportunitiesData?.totalOpportunities || 0;

  return (
    <>
      <Grid item lg={9} md={9} sm={12} xs={12} container className="grid">
        {isLoading ? (
          <div
            className="loading-container"
            style={{ textAlign: "center", padding: "40px 0", width: "100%" }}
          >
            <CircularProgress size={40} />
            <p style={{ marginTop: "16px" }}>{t("global:loading")}</p>
          </div>
        ) : opportunitiesData?.opportunities?.length > 0 ? (
          opportunitiesData.opportunities.map((opportunity) => (
            <OpportunityItem
              key={opportunity?._id}
              opportunity={opportunity}
              language={language}
              isList={isList}
            />
          ))
        ) : (
          <div
            className="no-results-container"
            style={{ textAlign: "center", padding: "40px 0", width: "100%" }}
          >
            <p className="no-results-message">
              {t("opportunities:noOpportunitiesFound")}
            </p>
            <p>{t("opportunities:tryDifferentFilters")}</p>
          </div>
        )}
      </Grid>
      <Grid item xs={12} lg={12} md={12} sm={12} container spacing={0}>
        {totalOpportunities > 0 && (
          <CustomPagination
            type="ssr"
            totalPages={Math.ceil(totalOpportunities / 10)}
            currentPage={pageNumber}
            onPageChange={handlePageChange}
            searchQueryParams={searchParamsContent}
          />
        )}
      </Grid>
    </>
  );
};

export default OpportunityList;
