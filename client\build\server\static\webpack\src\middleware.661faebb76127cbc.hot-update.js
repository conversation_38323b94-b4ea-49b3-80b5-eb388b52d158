"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.js":
/*!***************************!*\
  !*** ./src/middleware.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-i18n-router */ \"(middleware)/./node_modules/next-i18n-router/dist/index.js\");\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_i18n_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! jose */ \"(middleware)/./node_modules/jose/dist/browser/jwt/verify.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cookie */ \"(middleware)/./node_modules/cookie/index.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../i18nConfig */ \"(middleware)/./i18nConfig.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_i18nConfig__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/constants */ \"(middleware)/./src/utils/constants.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/functions */ \"(middleware)/./src/utils/functions.js\");\n/* harmony import */ var _config_translations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./config/translations */ \"(middleware)/./src/config/translations.js\");\n/* harmony import */ var _config_allowedParams__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./config/allowedParams */ \"(middleware)/./src/config/allowedParams.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./helpers/routesList */ \"(middleware)/./src/helpers/routesList.js\");\n\n\n\n\n\n\n\n\n\n\nconst SECURITY_CONFIG = {\n    MAX_REQUESTS_PER_MINUTE: 60,\n    JWT_ALGORITHM: \"HS256\",\n    SUSPICIOUS_PATTERNS: [\n        /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n        /javascript:/gi,\n        /on\\w+\\s*=/gi,\n        /eval\\s*\\(/gi,\n        /expression\\s*\\(/gi,\n        /%3Cscript/gi,\n        /%3C%2Fscript%3E/gi\n    ]\n};\nconst rateLimitStore = new Map();\nconst logSecurityEvent = (event, details = {})=>{\n    if (true) {\n        console.warn(`[SECURITY] ${event}:`, {\n            timestamp: new Date().toISOString(),\n            ...details\n        });\n    }\n};\nconst verifyToken = async (token, clientIP = \"unknown\")=>{\n    try {\n        if (!token || typeof token !== \"string\") {\n            logSecurityEvent(\"INVALID_TOKEN_FORMAT\", {\n                clientIP,\n                reason: \"Missing or invalid token\"\n            });\n            return null;\n        }\n        const jwtRegex = /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/;\n        if (!jwtRegex.test(token)) {\n            logSecurityEvent(\"INVALID_JWT_FORMAT\", {\n                clientIP,\n                tokenPrefix: token.substring(0, 10)\n            });\n            return null;\n        }\n        const jwtSecret = process.env.NEXT_JWT_SECRET;\n        if (!jwtSecret || jwtSecret.length < 32) {\n            logSecurityEvent(\"WEAK_JWT_SECRET\", {\n                clientIP\n            });\n            throw new Error(\"JWT secret configuration error\");\n        }\n        const secret = new TextEncoder().encode(jwtSecret);\n        const { payload } = await (0,jose__WEBPACK_IMPORTED_MODULE_9__.jwtVerify)(token, secret, {\n            algorithms: [\n                SECURITY_CONFIG.JWT_ALGORITHM\n            ],\n            issuer: process.env.JWT_ISSUER || \"pentabell-api\",\n            audience: process.env.JWT_AUDIENCE || \"pentabell-client\"\n        });\n        if (!payload || !payload._id || !payload.roles || !Array.isArray(payload.roles)) {\n            logSecurityEvent(\"INVALID_TOKEN_PAYLOAD\", {\n                clientIP,\n                hasId: !!payload?._id,\n                hasRoles: !!payload?.roles\n            });\n            return null;\n        }\n        const tokenAge = Date.now() / 1000 - (payload.iat || 0);\n        if (tokenAge > 86400) {\n            logSecurityEvent(\"OLD_TOKEN_USAGE\", {\n                clientIP,\n                tokenAge,\n                userId: payload._id\n            });\n        }\n        return payload;\n    } catch (error) {\n        if (error.name === \"JWTExpired\") {\n            logSecurityEvent(\"TOKEN_EXPIRED\", {\n                clientIP\n            });\n        } else if (error.name === \"JWTInvalid\") {\n            logSecurityEvent(\"INVALID_TOKEN\", {\n                clientIP,\n                error: error.message\n            });\n        } else {\n            logSecurityEvent(\"TOKEN_VERIFICATION_ERROR\", {\n                clientIP,\n                error: error.message\n            });\n        }\n        return null;\n    }\n};\nconst checkRateLimit = (clientIP)=>{\n    const now = Date.now();\n    const windowStart = now - 60000;\n    if (!rateLimitStore.has(clientIP)) {\n        rateLimitStore.set(clientIP, []);\n    }\n    const requests = rateLimitStore.get(clientIP);\n    const validRequests = requests.filter((timestamp)=>timestamp > windowStart);\n    rateLimitStore.set(clientIP, validRequests);\n    if (validRequests.length >= SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE) {\n        return false;\n    }\n    validRequests.push(now);\n    return true;\n};\nconst sanitizeInput = (value)=>{\n    if (typeof value !== \"string\") return value;\n    let sanitized = value;\n    SECURITY_CONFIG.SUSPICIOUS_PATTERNS.forEach((pattern)=>{\n        sanitized = sanitized.replace(pattern, \"\");\n    });\n    return sanitized.trim();\n};\nconst setSecurityHeaders = (response)=>{\n    response.headers.set(\"X-Content-Type-Options\", \"nosniff\");\n    response.headers.set(\"X-Frame-Options\", \"DENY\");\n    response.headers.set(\"X-XSS-Protection\", \"1; mode=block\");\n    response.headers.set(\"Referrer-Policy\", \"strict-origin-when-cross-origin\");\n    response.headers.set(\"Permissions-Policy\", \"geolocation=(), microphone=(), camera=()\");\n    response.headers.delete(\"Server\");\n    response.headers.delete(\"X-Powered-By\");\n    return response;\n};\nasync function middleware(req) {\n    const url = req.nextUrl.clone();\n    const { defaultLocale } = (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default());\n    const { pathname } = req.nextUrl;\n    const clientIP = req.ip || req.headers.get(\"x-forwarded-for\") || req.headers.get(\"x-real-ip\") || \"unknown\";\n    let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    response = setSecurityHeaders(response);\n    if (!checkRateLimit(clientIP)) {\n        logSecurityEvent(\"RATE_LIMIT_EXCEEDED\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Too Many Requests\", {\n            status: 429,\n            headers: {\n                \"Retry-After\": \"60\",\n                \"X-RateLimit-Limit\": SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE.toString(),\n                \"X-RateLimit-Remaining\": \"0\"\n            }\n        });\n    }\n    let hasModifiedParams = false;\n    for (const [key, value] of url.searchParams.entries()){\n        const sanitizedValue = sanitizeInput(value);\n        if (sanitizedValue !== value) {\n            url.searchParams.set(key, sanitizedValue);\n            hasModifiedParams = true;\n            logSecurityEvent(\"SUSPICIOUS_QUERY_PARAM\", {\n                clientIP,\n                key,\n                originalValue: value.substring(0, 50)\n            });\n        }\n    }\n    // Check for suspicious patterns in pathname\n    const pathString = pathname.toLowerCase();\n    const hasSuspiciousPath = SECURITY_CONFIG.SUSPICIOUS_PATTERNS.some((pattern)=>pattern.test(pathString));\n    if (hasSuspiciousPath) {\n        logSecurityEvent(\"SUSPICIOUS_PATH_ACCESS\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Forbidden\", {\n            status: 403\n        });\n    }\n    // Enhanced authentication and authorization\n    const cookies = cookie__WEBPACK_IMPORTED_MODULE_2__.parse(req.headers.get(\"cookie\") || \"\");\n    const { accessToken, refreshToken } = cookies;\n    // Check for protected routes\n    const isProtectedRoute = pathname.includes(\"dashboard\") || pathname.includes(\"backoffice\");\n    if (isProtectedRoute && !(accessToken && refreshToken)) {\n        logSecurityEvent(\"UNAUTHORIZED_ACCESS_ATTEMPT\", {\n            clientIP,\n            pathname\n        });\n        url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // Enhanced token verification with proper token selection\n    let user = null;\n    if (refreshToken) {\n        user = await verifyToken(refreshToken, clientIP);\n        // Additional security check for protected routes\n        if (isProtectedRoute && !user) {\n            logSecurityEvent(\"INVALID_TOKEN_PROTECTED_ROUTE\", {\n                clientIP,\n                pathname\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n    }\n    // Handle logout route\n    if (pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/`) {\n        logSecurityEvent(\"USER_LOGOUT\", {\n            clientIP,\n            userId: user?._id\n        });\n        return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n    }\n    // Enhanced role-based access control\n    if (user) {\n        const menuList = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.getRoutesListByRole)(user);\n        // Validate user roles\n        if (!user.roles || !Array.isArray(user.roles) || user.roles.length === 0) {\n            logSecurityEvent(\"INVALID_USER_ROLES\", {\n                clientIP,\n                userId: user._id\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n        const checkRole = !menuList?.some((item)=>pathname.includes(item)) && (pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}`) || pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}`));\n        // Enhanced role checking with security logging\n        if (checkRole || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`) {\n            let redirectPath = null;\n            if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.home.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.EDITOR)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.blogs.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.CANDIDATE)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.candidateRoutes.myApplications.route}`;\n            }\n            if (redirectPath) {\n                logSecurityEvent(\"ROLE_BASED_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles,\n                    fromPath: pathname,\n                    toPath: redirectPath\n                });\n                url.pathname = redirectPath;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            } else {\n                logSecurityEvent(\"NO_VALID_ROLE_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles\n                });\n                url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            }\n        }\n    }\n    // Enhanced parameter filtering with security logging\n    let removedParams = [];\n    for (const param of url.searchParams.keys()){\n        if (!_config_allowedParams__WEBPACK_IMPORTED_MODULE_7__.allowedParams.has(param)) {\n            url.searchParams.delete(param);\n            removedParams.push(param);\n        }\n    }\n    if (removedParams.length > 0) {\n        logSecurityEvent(\"REMOVED_DISALLOWED_PARAMS\", {\n            clientIP,\n            pathname,\n            removedParams,\n            userId: user?._id\n        });\n    }\n    // Check if parameters were modified (either sanitized or removed)\n    if (hasModifiedParams || url.searchParams.toString() !== req.nextUrl.searchParams.toString()) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // Enhanced redirection paths with security checks\n    const frPath = _config_translations__WEBPACK_IMPORTED_MODULE_6__[\"default\"][req.nextUrl.pathname];\n    if (frPath) {\n        logSecurityEvent(\"FRENCH_PATH_REDIRECT\", {\n            clientIP,\n            fromPath: req.nextUrl.pathname,\n            toPath: frPath\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(frPath, req.url));\n    }\n    // Enhanced language handling with security validation\n    if (!pathname.startsWith(\"/fr\") && !pathname.startsWith(`/${defaultLocale}`) && !pathname.startsWith(\"/_next\") && !pathname.startsWith(\"/api\") && !pathname.startsWith(\"/static\") && !pathname.includes(\".\")) {\n        // Additional security check for suspicious paths\n        if (pathname.length > 200) {\n            logSecurityEvent(\"SUSPICIOUS_LONG_PATH\", {\n                clientIP,\n                pathLength: pathname.length\n            });\n            return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Bad Request\", {\n                status: 400\n            });\n        }\n        url.pathname = `/en${pathname}`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.rewrite(url);\n    }\n    // Log successful requests for monitoring (in development only)\n    if ( true && user) {\n        logSecurityEvent(\"SUCCESSFUL_REQUEST\", {\n            clientIP,\n            pathname,\n            userId: user._id,\n            roles: user.roles\n        });\n    }\n    return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     * - files with extensions (images, fonts, etc.)\r\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|public|.*\\\\..*).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL21pZGRsZXdhcmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQThDO0FBQ2I7QUFDVTtBQUNmO0FBRVc7QUFDRTtBQUNlO0FBQ1A7QUFDTTtBQU96QjtBQUU5QixNQUFNYyxrQkFBa0I7SUFDdEJDLHlCQUF5QjtJQUN6QkMsZUFBZTtJQUNmQyxxQkFBcUI7UUFDbkI7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtBQUNIO0FBRUEsTUFBTUMsaUJBQWlCLElBQUlDO0FBRTNCLE1BQU1DLG1CQUFtQixDQUFDQyxPQUFPQyxVQUFVLENBQUMsQ0FBQztJQUMzQyxJQUFJLElBQTZCLEVBQUU7UUFDakNDLFFBQVFDLElBQUksQ0FBQyxDQUFDLFdBQVcsRUFBRUgsTUFBTSxDQUFDLENBQUMsRUFBRTtZQUNuQ0ksV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ2pDLEdBQUdMLE9BQU87UUFDWjtJQUNGO0FBQ0Y7QUFFTyxNQUFNTSxjQUFjLE9BQU9DLE9BQU9DLFdBQVcsU0FBUztJQUMzRCxJQUFJO1FBQ0YsSUFBSSxDQUFDRCxTQUFTLE9BQU9BLFVBQVUsVUFBVTtZQUN2Q1QsaUJBQWlCLHdCQUF3QjtnQkFDdkNVO2dCQUNBQyxRQUFRO1lBQ1Y7WUFDQSxPQUFPO1FBQ1Q7UUFFQSxNQUFNQyxXQUFXO1FBQ2pCLElBQUksQ0FBQ0EsU0FBU0MsSUFBSSxDQUFDSixRQUFRO1lBQ3pCVCxpQkFBaUIsc0JBQXNCO2dCQUNyQ1U7Z0JBQ0FJLGFBQWFMLE1BQU1NLFNBQVMsQ0FBQyxHQUFHO1lBQ2xDO1lBQ0EsT0FBTztRQUNUO1FBRUEsTUFBTUMsWUFBWUMsUUFBUUMsR0FBRyxDQUFDQyxlQUFlO1FBQzdDLElBQUksQ0FBQ0gsYUFBYUEsVUFBVUksTUFBTSxHQUFHLElBQUk7WUFDdkNwQixpQkFBaUIsbUJBQW1CO2dCQUFFVTtZQUFTO1lBQy9DLE1BQU0sSUFBSVcsTUFBTTtRQUNsQjtRQUVBLE1BQU1DLFNBQVMsSUFBSUMsY0FBY0MsTUFBTSxDQUFDUjtRQUV4QyxNQUFNLEVBQUVTLE9BQU8sRUFBRSxHQUFHLE1BQU01QywrQ0FBU0EsQ0FBQzRCLE9BQU9hLFFBQVE7WUFDakRJLFlBQVk7Z0JBQUNoQyxnQkFBZ0JFLGFBQWE7YUFBQztZQUMzQytCLFFBQVFWLFFBQVFDLEdBQUcsQ0FBQ1UsVUFBVSxJQUFJO1lBQ2xDQyxVQUFVWixRQUFRQyxHQUFHLENBQUNZLFlBQVksSUFBSTtRQUN4QztRQUVBLElBQ0UsQ0FBQ0wsV0FDRCxDQUFDQSxRQUFRTSxHQUFHLElBQ1osQ0FBQ04sUUFBUU8sS0FBSyxJQUNkLENBQUNDLE1BQU1DLE9BQU8sQ0FBQ1QsUUFBUU8sS0FBSyxHQUM1QjtZQUNBaEMsaUJBQWlCLHlCQUF5QjtnQkFDeENVO2dCQUNBeUIsT0FBTyxDQUFDLENBQUNWLFNBQVNNO2dCQUNsQkssVUFBVSxDQUFDLENBQUNYLFNBQVNPO1lBQ3ZCO1lBQ0EsT0FBTztRQUNUO1FBRUEsTUFBTUssV0FBVy9CLEtBQUtnQyxHQUFHLEtBQUssT0FBUWIsQ0FBQUEsUUFBUWMsR0FBRyxJQUFJO1FBQ3JELElBQUlGLFdBQVcsT0FBTztZQUNwQnJDLGlCQUFpQixtQkFBbUI7Z0JBQ2xDVTtnQkFDQTJCO2dCQUNBRyxRQUFRZixRQUFRTSxHQUFHO1lBQ3JCO1FBQ0Y7UUFFQSxPQUFPTjtJQUNULEVBQUUsT0FBT2dCLE9BQU87UUFDZCxJQUFJQSxNQUFNQyxJQUFJLEtBQUssY0FBYztZQUMvQjFDLGlCQUFpQixpQkFBaUI7Z0JBQUVVO1lBQVM7UUFDL0MsT0FBTyxJQUFJK0IsTUFBTUMsSUFBSSxLQUFLLGNBQWM7WUFDdEMxQyxpQkFBaUIsaUJBQWlCO2dCQUFFVTtnQkFBVStCLE9BQU9BLE1BQU1FLE9BQU87WUFBQztRQUNyRSxPQUFPO1lBQ0wzQyxpQkFBaUIsNEJBQTRCO2dCQUMzQ1U7Z0JBQ0ErQixPQUFPQSxNQUFNRSxPQUFPO1lBQ3RCO1FBQ0Y7UUFDQSxPQUFPO0lBQ1Q7QUFDRixFQUFFO0FBRUYsTUFBTUMsaUJBQWlCLENBQUNsQztJQUN0QixNQUFNNEIsTUFBTWhDLEtBQUtnQyxHQUFHO0lBQ3BCLE1BQU1PLGNBQWNQLE1BQU07SUFFMUIsSUFBSSxDQUFDeEMsZUFBZWdELEdBQUcsQ0FBQ3BDLFdBQVc7UUFDakNaLGVBQWVpRCxHQUFHLENBQUNyQyxVQUFVLEVBQUU7SUFDakM7SUFFQSxNQUFNc0MsV0FBV2xELGVBQWVtRCxHQUFHLENBQUN2QztJQUVwQyxNQUFNd0MsZ0JBQWdCRixTQUFTRyxNQUFNLENBQUMsQ0FBQzlDLFlBQWNBLFlBQVl3QztJQUNqRS9DLGVBQWVpRCxHQUFHLENBQUNyQyxVQUFVd0M7SUFFN0IsSUFBSUEsY0FBYzlCLE1BQU0sSUFBSTFCLGdCQUFnQkMsdUJBQXVCLEVBQUU7UUFDbkUsT0FBTztJQUNUO0lBRUF1RCxjQUFjRSxJQUFJLENBQUNkO0lBQ25CLE9BQU87QUFDVDtBQUVBLE1BQU1lLGdCQUFnQixDQUFDQztJQUNyQixJQUFJLE9BQU9BLFVBQVUsVUFBVSxPQUFPQTtJQUV0QyxJQUFJQyxZQUFZRDtJQUNoQjVELGdCQUFnQkcsbUJBQW1CLENBQUMyRCxPQUFPLENBQUMsQ0FBQ0M7UUFDM0NGLFlBQVlBLFVBQVVHLE9BQU8sQ0FBQ0QsU0FBUztJQUN6QztJQUVBLE9BQU9GLFVBQVVJLElBQUk7QUFDdkI7QUFFQSxNQUFNQyxxQkFBcUIsQ0FBQ0M7SUFDMUJBLFNBQVNDLE9BQU8sQ0FBQ2YsR0FBRyxDQUFDLDBCQUEwQjtJQUMvQ2MsU0FBU0MsT0FBTyxDQUFDZixHQUFHLENBQUMsbUJBQW1CO0lBQ3hDYyxTQUFTQyxPQUFPLENBQUNmLEdBQUcsQ0FBQyxvQkFBb0I7SUFDekNjLFNBQVNDLE9BQU8sQ0FBQ2YsR0FBRyxDQUFDLG1CQUFtQjtJQUN4Q2MsU0FBU0MsT0FBTyxDQUFDZixHQUFHLENBQ2xCLHNCQUNBO0lBR0ZjLFNBQVNDLE9BQU8sQ0FBQ0MsTUFBTSxDQUFDO0lBQ3hCRixTQUFTQyxPQUFPLENBQUNDLE1BQU0sQ0FBQztJQUV4QixPQUFPRjtBQUNUO0FBRU8sZUFBZUcsV0FBV0MsR0FBRztJQUNsQyxNQUFNQyxNQUFNRCxJQUFJRSxPQUFPLENBQUNDLEtBQUs7SUFDN0IsTUFBTSxFQUFFQyxhQUFhLEVBQUUsR0FBR3JGLG9EQUFVQTtJQUNwQyxNQUFNLEVBQUVzRixRQUFRLEVBQUUsR0FBR0wsSUFBSUUsT0FBTztJQUNoQyxNQUFNekQsV0FDSnVELElBQUlNLEVBQUUsSUFDTk4sSUFBSUgsT0FBTyxDQUFDYixHQUFHLENBQUMsc0JBQ2hCZ0IsSUFBSUgsT0FBTyxDQUFDYixHQUFHLENBQUMsZ0JBQ2hCO0lBRUYsSUFBSVksV0FBVy9FLHFEQUFZQSxDQUFDMEYsSUFBSTtJQUNoQ1gsV0FBV0QsbUJBQW1CQztJQUU5QixJQUFJLENBQUNqQixlQUFlbEMsV0FBVztRQUM3QlYsaUJBQWlCLHVCQUF1QjtZQUFFVTtZQUFVNEQ7UUFBUztRQUM3RCxPQUFPLElBQUl4RixxREFBWUEsQ0FBQyxxQkFBcUI7WUFDM0MyRixRQUFRO1lBQ1JYLFNBQVM7Z0JBQ1AsZUFBZTtnQkFDZixxQkFBcUJwRSxnQkFBZ0JDLHVCQUF1QixDQUFDK0UsUUFBUTtnQkFDckUseUJBQXlCO1lBQzNCO1FBQ0Y7SUFDRjtJQUVBLElBQUlDLG9CQUFvQjtJQUN4QixLQUFLLE1BQU0sQ0FBQ0MsS0FBS3RCLE1BQU0sSUFBSVksSUFBSVcsWUFBWSxDQUFDQyxPQUFPLEdBQUk7UUFDckQsTUFBTUMsaUJBQWlCMUIsY0FBY0M7UUFDckMsSUFBSXlCLG1CQUFtQnpCLE9BQU87WUFDNUJZLElBQUlXLFlBQVksQ0FBQzlCLEdBQUcsQ0FBQzZCLEtBQUtHO1lBQzFCSixvQkFBb0I7WUFDcEIzRSxpQkFBaUIsMEJBQTBCO2dCQUN6Q1U7Z0JBQ0FrRTtnQkFDQUksZUFBZTFCLE1BQU12QyxTQUFTLENBQUMsR0FBRztZQUNwQztRQUNGO0lBQ0Y7SUFFQSw0Q0FBNEM7SUFDNUMsTUFBTWtFLGFBQWFYLFNBQVNZLFdBQVc7SUFDdkMsTUFBTUMsb0JBQW9CekYsZ0JBQWdCRyxtQkFBbUIsQ0FBQ3VGLElBQUksQ0FDaEUsQ0FBQzNCLFVBQVlBLFFBQVE1QyxJQUFJLENBQUNvRTtJQUc1QixJQUFJRSxtQkFBbUI7UUFDckJuRixpQkFBaUIsMEJBQTBCO1lBQUVVO1lBQVU0RDtRQUFTO1FBQ2hFLE9BQU8sSUFBSXhGLHFEQUFZQSxDQUFDLGFBQWE7WUFBRTJGLFFBQVE7UUFBSTtJQUNyRDtJQUVBLDRDQUE0QztJQUM1QyxNQUFNWSxVQUFVdEcseUNBQVksQ0FBQ2tGLElBQUlILE9BQU8sQ0FBQ2IsR0FBRyxDQUFDLGFBQWE7SUFDMUQsTUFBTSxFQUFFc0MsV0FBVyxFQUFFQyxZQUFZLEVBQUUsR0FBR0g7SUFFdEMsNkJBQTZCO0lBQzdCLE1BQU1JLG1CQUNKbkIsU0FBU29CLFFBQVEsQ0FBQyxnQkFBZ0JwQixTQUFTb0IsUUFBUSxDQUFDO0lBRXRELElBQUlELG9CQUFvQixDQUFFRixDQUFBQSxlQUFlQyxZQUFXLEdBQUk7UUFDdER4RixpQkFBaUIsK0JBQStCO1lBQUVVO1lBQVU0RDtRQUFTO1FBQ3JFSixJQUFJSSxRQUFRLEdBQUcsQ0FBQyxDQUFDLEVBQUVoRiwyREFBVUEsQ0FBQ3FHLEtBQUssQ0FBQ0MsS0FBSyxDQUFDLENBQUMsQ0FBQztRQUM1QyxPQUFPOUcscURBQVlBLENBQUMrRyxRQUFRLENBQUMzQjtJQUMvQjtJQUVBLDBEQUEwRDtJQUMxRCxJQUFJNEIsT0FBTztJQUNYLElBQUlOLGNBQWM7UUFDaEJNLE9BQU8sTUFBTXRGLFlBQVlnRixjQUFjOUU7UUFFdkMsaURBQWlEO1FBQ2pELElBQUkrRSxvQkFBb0IsQ0FBQ0ssTUFBTTtZQUM3QjlGLGlCQUFpQixpQ0FBaUM7Z0JBQUVVO2dCQUFVNEQ7WUFBUztZQUN2RUosSUFBSUksUUFBUSxHQUFHLENBQUMsQ0FBQyxFQUFFaEYsMkRBQVVBLENBQUNxRyxLQUFLLENBQUNDLEtBQUssQ0FBQyxDQUFDLENBQUM7WUFDNUMsT0FBTzlHLHFEQUFZQSxDQUFDK0csUUFBUSxDQUFDM0I7UUFDL0I7SUFDRjtJQUVBLHNCQUFzQjtJQUN0QixJQUNFSSxhQUFhLENBQUMsQ0FBQyxFQUFFaEYsMkRBQVVBLENBQUN5RyxNQUFNLENBQUNILEtBQUssQ0FBQyxDQUFDLENBQUMsSUFDM0N0QixhQUFhLENBQUMsSUFBSSxFQUFFaEYsMkRBQVVBLENBQUN5RyxNQUFNLENBQUNILEtBQUssQ0FBQyxDQUFDLENBQUMsRUFDOUM7UUFDQTVGLGlCQUFpQixlQUFlO1lBQUVVO1lBQVU4QixRQUFRc0QsTUFBTS9EO1FBQUk7UUFDOUQsT0FBT25ELDREQUFVQSxDQUFDcUYsS0FBS2pGLG9EQUFVQTtJQUNuQztJQUVBLHFDQUFxQztJQUNyQyxJQUFJOEcsTUFBTTtRQUNSLE1BQU1FLFdBQVc5RyxxRUFBbUJBLENBQUM0RztRQUVyQyxzQkFBc0I7UUFDdEIsSUFBSSxDQUFDQSxLQUFLOUQsS0FBSyxJQUFJLENBQUNDLE1BQU1DLE9BQU8sQ0FBQzRELEtBQUs5RCxLQUFLLEtBQUs4RCxLQUFLOUQsS0FBSyxDQUFDWixNQUFNLEtBQUssR0FBRztZQUN4RXBCLGlCQUFpQixzQkFBc0I7Z0JBQUVVO2dCQUFVOEIsUUFBUXNELEtBQUsvRCxHQUFHO1lBQUM7WUFDcEVtQyxJQUFJSSxRQUFRLEdBQUcsQ0FBQyxDQUFDLEVBQUVoRiwyREFBVUEsQ0FBQ3FHLEtBQUssQ0FBQ0MsS0FBSyxDQUFDLENBQUMsQ0FBQztZQUM1QyxPQUFPOUcscURBQVlBLENBQUMrRyxRQUFRLENBQUMzQjtRQUMvQjtRQUVBLE1BQU0rQixZQUNKLENBQUNELFVBQVVaLEtBQUssQ0FBQ2MsT0FBUzVCLFNBQVNvQixRQUFRLENBQUNRLFVBQzNDNUIsQ0FBQUEsVUFBVW9CLFNBQVMsQ0FBQyxDQUFDLEVBQUVsRyxtRUFBa0JBLENBQUMyRyxPQUFPLENBQUNQLEtBQUssQ0FBQyxDQUFDLEtBQ3hEdEIsVUFBVW9CLFNBQVMsQ0FBQyxDQUFDLEVBQUVuRyxrRUFBaUJBLENBQUM0RyxPQUFPLENBQUNQLEtBQUssQ0FBQyxDQUFDO1FBRTVELCtDQUErQztRQUMvQyxJQUNFSyxhQUNBM0IsYUFBYSxDQUFDLElBQUksRUFBRWhGLDJEQUFVQSxDQUFDOEcsUUFBUSxDQUFDUixLQUFLLENBQUMsQ0FBQyxDQUFDLElBQ2hEdEIsYUFBYSxDQUFDLElBQUksRUFBRWhGLDJEQUFVQSxDQUFDcUcsS0FBSyxDQUFDQyxLQUFLLENBQUMsQ0FBQyxDQUFDLElBQzdDdEIsYUFBYSxDQUFDLENBQUMsRUFBRWhGLDJEQUFVQSxDQUFDOEcsUUFBUSxDQUFDUixLQUFLLENBQUMsQ0FBQyxDQUFDLElBQzdDdEIsYUFBYSxDQUFDLENBQUMsRUFBRWhGLDJEQUFVQSxDQUFDcUcsS0FBSyxDQUFDQyxLQUFLLENBQUMsQ0FBQyxDQUFDLEVBQzFDO1lBQ0EsSUFBSVMsZUFBZTtZQUVuQixJQUFJUCxLQUFLOUQsS0FBSyxDQUFDMEQsUUFBUSxDQUFDekcsa0RBQUlBLENBQUNxSCxLQUFLLEdBQUc7Z0JBQ25DRCxlQUFlLENBQUMsQ0FBQyxFQUFFOUcsa0VBQWlCQSxDQUFDNEcsT0FBTyxDQUFDUCxLQUFLLENBQUMsQ0FBQyxFQUFFdkcsNERBQVdBLENBQUNrSCxJQUFJLENBQUNYLEtBQUssQ0FBQyxDQUFDLENBQUM7WUFDakYsT0FBTyxJQUFJRSxLQUFLOUQsS0FBSyxDQUFDMEQsUUFBUSxDQUFDekcsa0RBQUlBLENBQUN1SCxNQUFNLEdBQUc7Z0JBQzNDSCxlQUFlLENBQUMsQ0FBQyxFQUFFOUcsa0VBQWlCQSxDQUFDNEcsT0FBTyxDQUFDUCxLQUFLLENBQUMsQ0FBQyxFQUFFdkcsNERBQVdBLENBQUNvSCxLQUFLLENBQUNiLEtBQUssQ0FBQyxDQUFDLENBQUM7WUFDbEYsT0FBTyxJQUFJRSxLQUFLOUQsS0FBSyxDQUFDMEQsUUFBUSxDQUFDekcsa0RBQUlBLENBQUN5SCxTQUFTLEdBQUc7Z0JBQzlDTCxlQUFlLENBQUMsQ0FBQyxFQUFFN0csbUVBQWtCQSxDQUFDMkcsT0FBTyxDQUFDUCxLQUFLLENBQUMsQ0FBQyxFQUFFbkcsZ0VBQWVBLENBQUNrSCxjQUFjLENBQUNmLEtBQUssQ0FBQyxDQUFDO1lBQy9GO1lBRUEsSUFBSVMsY0FBYztnQkFDaEJyRyxpQkFBaUIsdUJBQXVCO29CQUN0Q1U7b0JBQ0E4QixRQUFRc0QsS0FBSy9ELEdBQUc7b0JBQ2hCQyxPQUFPOEQsS0FBSzlELEtBQUs7b0JBQ2pCNEUsVUFBVXRDO29CQUNWdUMsUUFBUVI7Z0JBQ1Y7Z0JBQ0FuQyxJQUFJSSxRQUFRLEdBQUcrQjtnQkFDZixPQUFPdkgscURBQVlBLENBQUMrRyxRQUFRLENBQUMzQjtZQUMvQixPQUFPO2dCQUNMbEUsaUJBQWlCLDBCQUEwQjtvQkFDekNVO29CQUNBOEIsUUFBUXNELEtBQUsvRCxHQUFHO29CQUNoQkMsT0FBTzhELEtBQUs5RCxLQUFLO2dCQUNuQjtnQkFDQWtDLElBQUlJLFFBQVEsR0FBRyxDQUFDLENBQUMsRUFBRWhGLDJEQUFVQSxDQUFDcUcsS0FBSyxDQUFDQyxLQUFLLENBQUMsQ0FBQyxDQUFDO2dCQUM1QyxPQUFPOUcscURBQVlBLENBQUMrRyxRQUFRLENBQUMzQjtZQUMvQjtRQUNGO0lBQ0Y7SUFFQSxxREFBcUQ7SUFDckQsSUFBSTRDLGdCQUFnQixFQUFFO0lBQ3RCLEtBQUssTUFBTUMsU0FBUzdDLElBQUlXLFlBQVksQ0FBQ21DLElBQUksR0FBSTtRQUMzQyxJQUFJLENBQUM1SCxnRUFBYUEsQ0FBQzBELEdBQUcsQ0FBQ2lFLFFBQVE7WUFDN0I3QyxJQUFJVyxZQUFZLENBQUNkLE1BQU0sQ0FBQ2dEO1lBQ3hCRCxjQUFjMUQsSUFBSSxDQUFDMkQ7UUFDckI7SUFDRjtJQUVBLElBQUlELGNBQWMxRixNQUFNLEdBQUcsR0FBRztRQUM1QnBCLGlCQUFpQiw2QkFBNkI7WUFDNUNVO1lBQ0E0RDtZQUNBd0M7WUFDQXRFLFFBQVFzRCxNQUFNL0Q7UUFDaEI7SUFDRjtJQUVBLGtFQUFrRTtJQUNsRSxJQUNFNEMscUJBQ0FULElBQUlXLFlBQVksQ0FBQ0gsUUFBUSxPQUFPVCxJQUFJRSxPQUFPLENBQUNVLFlBQVksQ0FBQ0gsUUFBUSxJQUNqRTtRQUNBLE9BQU81RixxREFBWUEsQ0FBQytHLFFBQVEsQ0FBQzNCO0lBQy9CO0lBRUEsa0RBQWtEO0lBQ2xELE1BQU0rQyxTQUFTOUgsNERBQVksQ0FBQzhFLElBQUlFLE9BQU8sQ0FBQ0csUUFBUSxDQUFDO0lBQ2pELElBQUkyQyxRQUFRO1FBQ1ZqSCxpQkFBaUIsd0JBQXdCO1lBQ3ZDVTtZQUNBa0csVUFBVTNDLElBQUlFLE9BQU8sQ0FBQ0csUUFBUTtZQUM5QnVDLFFBQVFJO1FBQ1Y7UUFDQSxPQUFPbkkscURBQVlBLENBQUMrRyxRQUFRLENBQUMsSUFBSXFCLElBQUlELFFBQVFoRCxJQUFJQyxHQUFHO0lBQ3REO0lBRUEsc0RBQXNEO0lBQ3RELElBQ0UsQ0FBQ0ksU0FBUzZDLFVBQVUsQ0FBQyxVQUNyQixDQUFDN0MsU0FBUzZDLFVBQVUsQ0FBQyxDQUFDLENBQUMsRUFBRTlDLGNBQWMsQ0FBQyxLQUN4QyxDQUFDQyxTQUFTNkMsVUFBVSxDQUFDLGFBQ3JCLENBQUM3QyxTQUFTNkMsVUFBVSxDQUFDLFdBQ3JCLENBQUM3QyxTQUFTNkMsVUFBVSxDQUFDLGNBQ3JCLENBQUM3QyxTQUFTb0IsUUFBUSxDQUFDLE1BQ25CO1FBQ0EsaURBQWlEO1FBQ2pELElBQUlwQixTQUFTbEQsTUFBTSxHQUFHLEtBQUs7WUFDekJwQixpQkFBaUIsd0JBQXdCO2dCQUN2Q1U7Z0JBQ0EwRyxZQUFZOUMsU0FBU2xELE1BQU07WUFDN0I7WUFDQSxPQUFPLElBQUl0QyxxREFBWUEsQ0FBQyxlQUFlO2dCQUFFMkYsUUFBUTtZQUFJO1FBQ3ZEO1FBRUFQLElBQUlJLFFBQVEsR0FBRyxDQUFDLEdBQUcsRUFBRUEsU0FBUyxDQUFDO1FBQy9CLE9BQU94RixxREFBWUEsQ0FBQ3VJLE9BQU8sQ0FBQ25EO0lBQzlCO0lBRUEsK0RBQStEO0lBQy9ELElBQUlqRCxLQUFzQyxJQUFJNkUsTUFBTTtRQUNsRDlGLGlCQUFpQixzQkFBc0I7WUFDckNVO1lBQ0E0RDtZQUNBOUIsUUFBUXNELEtBQUsvRCxHQUFHO1lBQ2hCQyxPQUFPOEQsS0FBSzlELEtBQUs7UUFDbkI7SUFDRjtJQUVBLE9BQU9wRCw0REFBVUEsQ0FBQ3FGLEtBQUtqRixvREFBVUE7QUFDbkM7QUFFTyxNQUFNc0ksU0FBUztJQUNwQkMsU0FBUztRQUNQOzs7Ozs7OztLQVFDLEdBQ0Q7S0FDRDtBQUNILEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL21pZGRsZXdhcmUuanM/YTQ5YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpMThuUm91dGVyIH0gZnJvbSBcIm5leHQtaTE4bi1yb3V0ZXJcIjtcclxuaW1wb3J0IHsgand0VmVyaWZ5IH0gZnJvbSBcImpvc2VcIjtcclxuaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSBcIm5leHQvc2VydmVyXCI7XHJcbmltcG9ydCBjb29raWUgZnJvbSBcImNvb2tpZVwiO1xyXG5cclxuaW1wb3J0IGkxOG5Db25maWcgZnJvbSBcIi4uL2kxOG5Db25maWdcIjtcclxuaW1wb3J0IHsgUm9sZSB9IGZyb20gXCIuL3V0aWxzL2NvbnN0YW50c1wiO1xyXG5pbXBvcnQgeyBnZXRSb3V0ZXNMaXN0QnlSb2xlIH0gZnJvbSBcIi4vdXRpbHMvZnVuY3Rpb25zXCI7XHJcbmltcG9ydCB0cmFuc2xhdGlvbnMgZnJvbSBcIi4vY29uZmlnL3RyYW5zbGF0aW9uc1wiO1xyXG5pbXBvcnQgeyBhbGxvd2VkUGFyYW1zIH0gZnJvbSBcIi4vY29uZmlnL2FsbG93ZWRQYXJhbXNcIjtcclxuaW1wb3J0IHtcclxuICBhZG1pblJvdXRlcyxcclxuICBhdXRoUm91dGVzLFxyXG4gIGJhc2VVcmxCYWNrb2ZmaWNlLFxyXG4gIGJhc2VVcmxGcm9udG9mZmljZSxcclxuICBjYW5kaWRhdGVSb3V0ZXMsXHJcbn0gZnJvbSBcIi4vaGVscGVycy9yb3V0ZXNMaXN0XCI7XHJcblxyXG5jb25zdCBTRUNVUklUWV9DT05GSUcgPSB7XHJcbiAgTUFYX1JFUVVFU1RTX1BFUl9NSU5VVEU6IDYwLFxyXG4gIEpXVF9BTEdPUklUSE06IFwiSFMyNTZcIixcclxuICBTVVNQSUNJT1VTX1BBVFRFUk5TOiBbXHJcbiAgICAvPHNjcmlwdFxcYltePF0qKD86KD8hPFxcL3NjcmlwdD4pPFtePF0qKSo8XFwvc2NyaXB0Pi9naSxcclxuICAgIC9qYXZhc2NyaXB0Oi9naSxcclxuICAgIC9vblxcdytcXHMqPS9naSxcclxuICAgIC9ldmFsXFxzKlxcKC9naSxcclxuICAgIC9leHByZXNzaW9uXFxzKlxcKC9naSxcclxuICAgIC8lM0NzY3JpcHQvZ2ksXHJcbiAgICAvJTNDJTJGc2NyaXB0JTNFL2dpLFxyXG4gIF0sXHJcbn07XHJcblxyXG5jb25zdCByYXRlTGltaXRTdG9yZSA9IG5ldyBNYXAoKTtcclxuXHJcbmNvbnN0IGxvZ1NlY3VyaXR5RXZlbnQgPSAoZXZlbnQsIGRldGFpbHMgPSB7fSkgPT4ge1xyXG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiKSB7XHJcbiAgICBjb25zb2xlLndhcm4oYFtTRUNVUklUWV0gJHtldmVudH06YCwge1xyXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgLi4uZGV0YWlscyxcclxuICAgIH0pO1xyXG4gIH1cclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB2ZXJpZnlUb2tlbiA9IGFzeW5jICh0b2tlbiwgY2xpZW50SVAgPSBcInVua25vd25cIikgPT4ge1xyXG4gIHRyeSB7XHJcbiAgICBpZiAoIXRva2VuIHx8IHR5cGVvZiB0b2tlbiAhPT0gXCJzdHJpbmdcIikge1xyXG4gICAgICBsb2dTZWN1cml0eUV2ZW50KFwiSU5WQUxJRF9UT0tFTl9GT1JNQVRcIiwge1xyXG4gICAgICAgIGNsaWVudElQLFxyXG4gICAgICAgIHJlYXNvbjogXCJNaXNzaW5nIG9yIGludmFsaWQgdG9rZW5cIixcclxuICAgICAgfSk7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGp3dFJlZ2V4ID0gL15bQS1aYS16MC05LV9dK1xcLltBLVphLXowLTktX10rXFwuW0EtWmEtejAtOS1fXSokLztcclxuICAgIGlmICghand0UmVnZXgudGVzdCh0b2tlbikpIHtcclxuICAgICAgbG9nU2VjdXJpdHlFdmVudChcIklOVkFMSURfSldUX0ZPUk1BVFwiLCB7XHJcbiAgICAgICAgY2xpZW50SVAsXHJcbiAgICAgICAgdG9rZW5QcmVmaXg6IHRva2VuLnN1YnN0cmluZygwLCAxMCksXHJcbiAgICAgIH0pO1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBqd3RTZWNyZXQgPSBwcm9jZXNzLmVudi5ORVhUX0pXVF9TRUNSRVQ7XHJcbiAgICBpZiAoIWp3dFNlY3JldCB8fCBqd3RTZWNyZXQubGVuZ3RoIDwgMzIpIHtcclxuICAgICAgbG9nU2VjdXJpdHlFdmVudChcIldFQUtfSldUX1NFQ1JFVFwiLCB7IGNsaWVudElQIH0pO1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJKV1Qgc2VjcmV0IGNvbmZpZ3VyYXRpb24gZXJyb3JcIik7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3Qgc2VjcmV0ID0gbmV3IFRleHRFbmNvZGVyKCkuZW5jb2RlKGp3dFNlY3JldCk7XHJcblxyXG4gICAgY29uc3QgeyBwYXlsb2FkIH0gPSBhd2FpdCBqd3RWZXJpZnkodG9rZW4sIHNlY3JldCwge1xyXG4gICAgICBhbGdvcml0aG1zOiBbU0VDVVJJVFlfQ09ORklHLkpXVF9BTEdPUklUSE1dLFxyXG4gICAgICBpc3N1ZXI6IHByb2Nlc3MuZW52LkpXVF9JU1NVRVIgfHwgXCJwZW50YWJlbGwtYXBpXCIsXHJcbiAgICAgIGF1ZGllbmNlOiBwcm9jZXNzLmVudi5KV1RfQVVESUVOQ0UgfHwgXCJwZW50YWJlbGwtY2xpZW50XCIsXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoXHJcbiAgICAgICFwYXlsb2FkIHx8XHJcbiAgICAgICFwYXlsb2FkLl9pZCB8fFxyXG4gICAgICAhcGF5bG9hZC5yb2xlcyB8fFxyXG4gICAgICAhQXJyYXkuaXNBcnJheShwYXlsb2FkLnJvbGVzKVxyXG4gICAgKSB7XHJcbiAgICAgIGxvZ1NlY3VyaXR5RXZlbnQoXCJJTlZBTElEX1RPS0VOX1BBWUxPQURcIiwge1xyXG4gICAgICAgIGNsaWVudElQLFxyXG4gICAgICAgIGhhc0lkOiAhIXBheWxvYWQ/Ll9pZCxcclxuICAgICAgICBoYXNSb2xlczogISFwYXlsb2FkPy5yb2xlcyxcclxuICAgICAgfSk7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHRva2VuQWdlID0gRGF0ZS5ub3coKSAvIDEwMDAgLSAocGF5bG9hZC5pYXQgfHwgMCk7XHJcbiAgICBpZiAodG9rZW5BZ2UgPiA4NjQwMCkge1xyXG4gICAgICBsb2dTZWN1cml0eUV2ZW50KFwiT0xEX1RPS0VOX1VTQUdFXCIsIHtcclxuICAgICAgICBjbGllbnRJUCxcclxuICAgICAgICB0b2tlbkFnZSxcclxuICAgICAgICB1c2VySWQ6IHBheWxvYWQuX2lkLFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gcGF5bG9hZDtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgaWYgKGVycm9yLm5hbWUgPT09IFwiSldURXhwaXJlZFwiKSB7XHJcbiAgICAgIGxvZ1NlY3VyaXR5RXZlbnQoXCJUT0tFTl9FWFBJUkVEXCIsIHsgY2xpZW50SVAgfSk7XHJcbiAgICB9IGVsc2UgaWYgKGVycm9yLm5hbWUgPT09IFwiSldUSW52YWxpZFwiKSB7XHJcbiAgICAgIGxvZ1NlY3VyaXR5RXZlbnQoXCJJTlZBTElEX1RPS0VOXCIsIHsgY2xpZW50SVAsIGVycm9yOiBlcnJvci5tZXNzYWdlIH0pO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgbG9nU2VjdXJpdHlFdmVudChcIlRPS0VOX1ZFUklGSUNBVElPTl9FUlJPUlwiLCB7XHJcbiAgICAgICAgY2xpZW50SVAsXHJcbiAgICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2UsXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfVxyXG59O1xyXG5cclxuY29uc3QgY2hlY2tSYXRlTGltaXQgPSAoY2xpZW50SVApID0+IHtcclxuICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xyXG4gIGNvbnN0IHdpbmRvd1N0YXJ0ID0gbm93IC0gNjAwMDA7IFxyXG5cclxuICBpZiAoIXJhdGVMaW1pdFN0b3JlLmhhcyhjbGllbnRJUCkpIHtcclxuICAgIHJhdGVMaW1pdFN0b3JlLnNldChjbGllbnRJUCwgW10pO1xyXG4gIH1cclxuXHJcbiAgY29uc3QgcmVxdWVzdHMgPSByYXRlTGltaXRTdG9yZS5nZXQoY2xpZW50SVApO1xyXG5cclxuICBjb25zdCB2YWxpZFJlcXVlc3RzID0gcmVxdWVzdHMuZmlsdGVyKCh0aW1lc3RhbXApID0+IHRpbWVzdGFtcCA+IHdpbmRvd1N0YXJ0KTtcclxuICByYXRlTGltaXRTdG9yZS5zZXQoY2xpZW50SVAsIHZhbGlkUmVxdWVzdHMpO1xyXG5cclxuICBpZiAodmFsaWRSZXF1ZXN0cy5sZW5ndGggPj0gU0VDVVJJVFlfQ09ORklHLk1BWF9SRVFVRVNUU19QRVJfTUlOVVRFKSB7XHJcbiAgICByZXR1cm4gZmFsc2U7XHJcbiAgfVxyXG5cclxuICB2YWxpZFJlcXVlc3RzLnB1c2gobm93KTtcclxuICByZXR1cm4gdHJ1ZTtcclxufTtcclxuXHJcbmNvbnN0IHNhbml0aXplSW5wdXQgPSAodmFsdWUpID0+IHtcclxuICBpZiAodHlwZW9mIHZhbHVlICE9PSBcInN0cmluZ1wiKSByZXR1cm4gdmFsdWU7XHJcblxyXG4gIGxldCBzYW5pdGl6ZWQgPSB2YWx1ZTtcclxuICBTRUNVUklUWV9DT05GSUcuU1VTUElDSU9VU19QQVRURVJOUy5mb3JFYWNoKChwYXR0ZXJuKSA9PiB7XHJcbiAgICBzYW5pdGl6ZWQgPSBzYW5pdGl6ZWQucmVwbGFjZShwYXR0ZXJuLCBcIlwiKTtcclxuICB9KTtcclxuXHJcbiAgcmV0dXJuIHNhbml0aXplZC50cmltKCk7XHJcbn07XHJcblxyXG5jb25zdCBzZXRTZWN1cml0eUhlYWRlcnMgPSAocmVzcG9uc2UpID0+IHtcclxuICByZXNwb25zZS5oZWFkZXJzLnNldChcIlgtQ29udGVudC1UeXBlLU9wdGlvbnNcIiwgXCJub3NuaWZmXCIpO1xyXG4gIHJlc3BvbnNlLmhlYWRlcnMuc2V0KFwiWC1GcmFtZS1PcHRpb25zXCIsIFwiREVOWVwiKTtcclxuICByZXNwb25zZS5oZWFkZXJzLnNldChcIlgtWFNTLVByb3RlY3Rpb25cIiwgXCIxOyBtb2RlPWJsb2NrXCIpO1xyXG4gIHJlc3BvbnNlLmhlYWRlcnMuc2V0KFwiUmVmZXJyZXItUG9saWN5XCIsIFwic3RyaWN0LW9yaWdpbi13aGVuLWNyb3NzLW9yaWdpblwiKTtcclxuICByZXNwb25zZS5oZWFkZXJzLnNldChcclxuICAgIFwiUGVybWlzc2lvbnMtUG9saWN5XCIsXHJcbiAgICBcImdlb2xvY2F0aW9uPSgpLCBtaWNyb3Bob25lPSgpLCBjYW1lcmE9KClcIlxyXG4gICk7XHJcblxyXG4gIHJlc3BvbnNlLmhlYWRlcnMuZGVsZXRlKFwiU2VydmVyXCIpO1xyXG4gIHJlc3BvbnNlLmhlYWRlcnMuZGVsZXRlKFwiWC1Qb3dlcmVkLUJ5XCIpO1xyXG5cclxuICByZXR1cm4gcmVzcG9uc2U7XHJcbn07XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbWlkZGxld2FyZShyZXEpIHtcclxuICBjb25zdCB1cmwgPSByZXEubmV4dFVybC5jbG9uZSgpO1xyXG4gIGNvbnN0IHsgZGVmYXVsdExvY2FsZSB9ID0gaTE4bkNvbmZpZztcclxuICBjb25zdCB7IHBhdGhuYW1lIH0gPSByZXEubmV4dFVybDtcclxuICBjb25zdCBjbGllbnRJUCA9XHJcbiAgICByZXEuaXAgfHxcclxuICAgIHJlcS5oZWFkZXJzLmdldChcIngtZm9yd2FyZGVkLWZvclwiKSB8fFxyXG4gICAgcmVxLmhlYWRlcnMuZ2V0KFwieC1yZWFsLWlwXCIpIHx8XHJcbiAgICBcInVua25vd25cIjtcclxuXHJcbiAgbGV0IHJlc3BvbnNlID0gTmV4dFJlc3BvbnNlLm5leHQoKTtcclxuICByZXNwb25zZSA9IHNldFNlY3VyaXR5SGVhZGVycyhyZXNwb25zZSk7XHJcblxyXG4gIGlmICghY2hlY2tSYXRlTGltaXQoY2xpZW50SVApKSB7XHJcbiAgICBsb2dTZWN1cml0eUV2ZW50KFwiUkFURV9MSU1JVF9FWENFRURFRFwiLCB7IGNsaWVudElQLCBwYXRobmFtZSB9KTtcclxuICAgIHJldHVybiBuZXcgTmV4dFJlc3BvbnNlKFwiVG9vIE1hbnkgUmVxdWVzdHNcIiwge1xyXG4gICAgICBzdGF0dXM6IDQyOSxcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgIFwiUmV0cnktQWZ0ZXJcIjogXCI2MFwiLFxyXG4gICAgICAgIFwiWC1SYXRlTGltaXQtTGltaXRcIjogU0VDVVJJVFlfQ09ORklHLk1BWF9SRVFVRVNUU19QRVJfTUlOVVRFLnRvU3RyaW5nKCksXHJcbiAgICAgICAgXCJYLVJhdGVMaW1pdC1SZW1haW5pbmdcIjogXCIwXCIsXHJcbiAgICAgIH0sXHJcbiAgICB9KTtcclxuICB9XHJcblxyXG4gIGxldCBoYXNNb2RpZmllZFBhcmFtcyA9IGZhbHNlO1xyXG4gIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIHVybC5zZWFyY2hQYXJhbXMuZW50cmllcygpKSB7XHJcbiAgICBjb25zdCBzYW5pdGl6ZWRWYWx1ZSA9IHNhbml0aXplSW5wdXQodmFsdWUpO1xyXG4gICAgaWYgKHNhbml0aXplZFZhbHVlICE9PSB2YWx1ZSkge1xyXG4gICAgICB1cmwuc2VhcmNoUGFyYW1zLnNldChrZXksIHNhbml0aXplZFZhbHVlKTtcclxuICAgICAgaGFzTW9kaWZpZWRQYXJhbXMgPSB0cnVlO1xyXG4gICAgICBsb2dTZWN1cml0eUV2ZW50KFwiU1VTUElDSU9VU19RVUVSWV9QQVJBTVwiLCB7XHJcbiAgICAgICAgY2xpZW50SVAsXHJcbiAgICAgICAga2V5LFxyXG4gICAgICAgIG9yaWdpbmFsVmFsdWU6IHZhbHVlLnN1YnN0cmluZygwLCA1MCksXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gQ2hlY2sgZm9yIHN1c3BpY2lvdXMgcGF0dGVybnMgaW4gcGF0aG5hbWVcclxuICBjb25zdCBwYXRoU3RyaW5nID0gcGF0aG5hbWUudG9Mb3dlckNhc2UoKTtcclxuICBjb25zdCBoYXNTdXNwaWNpb3VzUGF0aCA9IFNFQ1VSSVRZX0NPTkZJRy5TVVNQSUNJT1VTX1BBVFRFUk5TLnNvbWUoXHJcbiAgICAocGF0dGVybikgPT4gcGF0dGVybi50ZXN0KHBhdGhTdHJpbmcpXHJcbiAgKTtcclxuXHJcbiAgaWYgKGhhc1N1c3BpY2lvdXNQYXRoKSB7XHJcbiAgICBsb2dTZWN1cml0eUV2ZW50KFwiU1VTUElDSU9VU19QQVRIX0FDQ0VTU1wiLCB7IGNsaWVudElQLCBwYXRobmFtZSB9KTtcclxuICAgIHJldHVybiBuZXcgTmV4dFJlc3BvbnNlKFwiRm9yYmlkZGVuXCIsIHsgc3RhdHVzOiA0MDMgfSk7XHJcbiAgfVxyXG5cclxuICAvLyBFbmhhbmNlZCBhdXRoZW50aWNhdGlvbiBhbmQgYXV0aG9yaXphdGlvblxyXG4gIGNvbnN0IGNvb2tpZXMgPSBjb29raWUucGFyc2UocmVxLmhlYWRlcnMuZ2V0KFwiY29va2llXCIpIHx8IFwiXCIpO1xyXG4gIGNvbnN0IHsgYWNjZXNzVG9rZW4sIHJlZnJlc2hUb2tlbiB9ID0gY29va2llcztcclxuXHJcbiAgLy8gQ2hlY2sgZm9yIHByb3RlY3RlZCByb3V0ZXNcclxuICBjb25zdCBpc1Byb3RlY3RlZFJvdXRlID1cclxuICAgIHBhdGhuYW1lLmluY2x1ZGVzKFwiZGFzaGJvYXJkXCIpIHx8IHBhdGhuYW1lLmluY2x1ZGVzKFwiYmFja29mZmljZVwiKTtcclxuXHJcbiAgaWYgKGlzUHJvdGVjdGVkUm91dGUgJiYgIShhY2Nlc3NUb2tlbiAmJiByZWZyZXNoVG9rZW4pKSB7XHJcbiAgICBsb2dTZWN1cml0eUV2ZW50KFwiVU5BVVRIT1JJWkVEX0FDQ0VTU19BVFRFTVBUXCIsIHsgY2xpZW50SVAsIHBhdGhuYW1lIH0pO1xyXG4gICAgdXJsLnBhdGhuYW1lID0gYC8ke2F1dGhSb3V0ZXMubG9naW4ucm91dGV9L2A7XHJcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLnJlZGlyZWN0KHVybCk7XHJcbiAgfVxyXG5cclxuICAvLyBFbmhhbmNlZCB0b2tlbiB2ZXJpZmljYXRpb24gd2l0aCBwcm9wZXIgdG9rZW4gc2VsZWN0aW9uXHJcbiAgbGV0IHVzZXIgPSBudWxsO1xyXG4gIGlmIChyZWZyZXNoVG9rZW4pIHtcclxuICAgIHVzZXIgPSBhd2FpdCB2ZXJpZnlUb2tlbihyZWZyZXNoVG9rZW4sIGNsaWVudElQKTtcclxuXHJcbiAgICAvLyBBZGRpdGlvbmFsIHNlY3VyaXR5IGNoZWNrIGZvciBwcm90ZWN0ZWQgcm91dGVzXHJcbiAgICBpZiAoaXNQcm90ZWN0ZWRSb3V0ZSAmJiAhdXNlcikge1xyXG4gICAgICBsb2dTZWN1cml0eUV2ZW50KFwiSU5WQUxJRF9UT0tFTl9QUk9URUNURURfUk9VVEVcIiwgeyBjbGllbnRJUCwgcGF0aG5hbWUgfSk7XHJcbiAgICAgIHVybC5wYXRobmFtZSA9IGAvJHthdXRoUm91dGVzLmxvZ2luLnJvdXRlfS9gO1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLnJlZGlyZWN0KHVybCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBIYW5kbGUgbG9nb3V0IHJvdXRlXHJcbiAgaWYgKFxyXG4gICAgcGF0aG5hbWUgPT09IGAvJHthdXRoUm91dGVzLmxvZ291dC5yb3V0ZX0vYCB8fFxyXG4gICAgcGF0aG5hbWUgPT09IGAvZnIvJHthdXRoUm91dGVzLmxvZ291dC5yb3V0ZX0vYFxyXG4gICkge1xyXG4gICAgbG9nU2VjdXJpdHlFdmVudChcIlVTRVJfTE9HT1VUXCIsIHsgY2xpZW50SVAsIHVzZXJJZDogdXNlcj8uX2lkIH0pO1xyXG4gICAgcmV0dXJuIGkxOG5Sb3V0ZXIocmVxLCBpMThuQ29uZmlnKTtcclxuICB9XHJcblxyXG4gIC8vIEVuaGFuY2VkIHJvbGUtYmFzZWQgYWNjZXNzIGNvbnRyb2xcclxuICBpZiAodXNlcikge1xyXG4gICAgY29uc3QgbWVudUxpc3QgPSBnZXRSb3V0ZXNMaXN0QnlSb2xlKHVzZXIpO1xyXG5cclxuICAgIC8vIFZhbGlkYXRlIHVzZXIgcm9sZXNcclxuICAgIGlmICghdXNlci5yb2xlcyB8fCAhQXJyYXkuaXNBcnJheSh1c2VyLnJvbGVzKSB8fCB1c2VyLnJvbGVzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICBsb2dTZWN1cml0eUV2ZW50KFwiSU5WQUxJRF9VU0VSX1JPTEVTXCIsIHsgY2xpZW50SVAsIHVzZXJJZDogdXNlci5faWQgfSk7XHJcbiAgICAgIHVybC5wYXRobmFtZSA9IGAvJHthdXRoUm91dGVzLmxvZ2luLnJvdXRlfS9gO1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLnJlZGlyZWN0KHVybCk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgY2hlY2tSb2xlID1cclxuICAgICAgIW1lbnVMaXN0Py5zb21lKChpdGVtKSA9PiBwYXRobmFtZS5pbmNsdWRlcyhpdGVtKSkgJiZcclxuICAgICAgKHBhdGhuYW1lPy5pbmNsdWRlcyhgLyR7YmFzZVVybEZyb250b2ZmaWNlLmJhc2VVUkwucm91dGV9YCkgfHxcclxuICAgICAgICBwYXRobmFtZT8uaW5jbHVkZXMoYC8ke2Jhc2VVcmxCYWNrb2ZmaWNlLmJhc2VVUkwucm91dGV9YCkpO1xyXG5cclxuICAgIC8vIEVuaGFuY2VkIHJvbGUgY2hlY2tpbmcgd2l0aCBzZWN1cml0eSBsb2dnaW5nXHJcbiAgICBpZiAoXHJcbiAgICAgIGNoZWNrUm9sZSB8fFxyXG4gICAgICBwYXRobmFtZSA9PT0gYC9mci8ke2F1dGhSb3V0ZXMucmVnaXN0ZXIucm91dGV9L2AgfHxcclxuICAgICAgcGF0aG5hbWUgPT09IGAvZnIvJHthdXRoUm91dGVzLmxvZ2luLnJvdXRlfS9gIHx8XHJcbiAgICAgIHBhdGhuYW1lID09PSBgLyR7YXV0aFJvdXRlcy5yZWdpc3Rlci5yb3V0ZX0vYCB8fFxyXG4gICAgICBwYXRobmFtZSA9PT0gYC8ke2F1dGhSb3V0ZXMubG9naW4ucm91dGV9L2BcclxuICAgICkge1xyXG4gICAgICBsZXQgcmVkaXJlY3RQYXRoID0gbnVsbDtcclxuXHJcbiAgICAgIGlmICh1c2VyLnJvbGVzLmluY2x1ZGVzKFJvbGUuQURNSU4pKSB7XHJcbiAgICAgICAgcmVkaXJlY3RQYXRoID0gYC8ke2Jhc2VVcmxCYWNrb2ZmaWNlLmJhc2VVUkwucm91dGV9LyR7YWRtaW5Sb3V0ZXMuaG9tZS5yb3V0ZX0vYDtcclxuICAgICAgfSBlbHNlIGlmICh1c2VyLnJvbGVzLmluY2x1ZGVzKFJvbGUuRURJVE9SKSkge1xyXG4gICAgICAgIHJlZGlyZWN0UGF0aCA9IGAvJHtiYXNlVXJsQmFja29mZmljZS5iYXNlVVJMLnJvdXRlfS8ke2FkbWluUm91dGVzLmJsb2dzLnJvdXRlfS9gO1xyXG4gICAgICB9IGVsc2UgaWYgKHVzZXIucm9sZXMuaW5jbHVkZXMoUm9sZS5DQU5ESURBVEUpKSB7XHJcbiAgICAgICAgcmVkaXJlY3RQYXRoID0gYC8ke2Jhc2VVcmxGcm9udG9mZmljZS5iYXNlVVJMLnJvdXRlfS8ke2NhbmRpZGF0ZVJvdXRlcy5teUFwcGxpY2F0aW9ucy5yb3V0ZX1gO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAocmVkaXJlY3RQYXRoKSB7XHJcbiAgICAgICAgbG9nU2VjdXJpdHlFdmVudChcIlJPTEVfQkFTRURfUkVESVJFQ1RcIiwge1xyXG4gICAgICAgICAgY2xpZW50SVAsXHJcbiAgICAgICAgICB1c2VySWQ6IHVzZXIuX2lkLFxyXG4gICAgICAgICAgcm9sZXM6IHVzZXIucm9sZXMsXHJcbiAgICAgICAgICBmcm9tUGF0aDogcGF0aG5hbWUsXHJcbiAgICAgICAgICB0b1BhdGg6IHJlZGlyZWN0UGF0aCxcclxuICAgICAgICB9KTtcclxuICAgICAgICB1cmwucGF0aG5hbWUgPSByZWRpcmVjdFBhdGg7XHJcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdCh1cmwpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGxvZ1NlY3VyaXR5RXZlbnQoXCJOT19WQUxJRF9ST0xFX1JFRElSRUNUXCIsIHtcclxuICAgICAgICAgIGNsaWVudElQLFxyXG4gICAgICAgICAgdXNlcklkOiB1c2VyLl9pZCxcclxuICAgICAgICAgIHJvbGVzOiB1c2VyLnJvbGVzLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHVybC5wYXRobmFtZSA9IGAvJHthdXRoUm91dGVzLmxvZ2luLnJvdXRlfS9gO1xyXG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QodXJsKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gRW5oYW5jZWQgcGFyYW1ldGVyIGZpbHRlcmluZyB3aXRoIHNlY3VyaXR5IGxvZ2dpbmdcclxuICBsZXQgcmVtb3ZlZFBhcmFtcyA9IFtdO1xyXG4gIGZvciAoY29uc3QgcGFyYW0gb2YgdXJsLnNlYXJjaFBhcmFtcy5rZXlzKCkpIHtcclxuICAgIGlmICghYWxsb3dlZFBhcmFtcy5oYXMocGFyYW0pKSB7XHJcbiAgICAgIHVybC5zZWFyY2hQYXJhbXMuZGVsZXRlKHBhcmFtKTtcclxuICAgICAgcmVtb3ZlZFBhcmFtcy5wdXNoKHBhcmFtKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGlmIChyZW1vdmVkUGFyYW1zLmxlbmd0aCA+IDApIHtcclxuICAgIGxvZ1NlY3VyaXR5RXZlbnQoXCJSRU1PVkVEX0RJU0FMTE9XRURfUEFSQU1TXCIsIHtcclxuICAgICAgY2xpZW50SVAsXHJcbiAgICAgIHBhdGhuYW1lLFxyXG4gICAgICByZW1vdmVkUGFyYW1zLFxyXG4gICAgICB1c2VySWQ6IHVzZXI/Ll9pZCxcclxuICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgLy8gQ2hlY2sgaWYgcGFyYW1ldGVycyB3ZXJlIG1vZGlmaWVkIChlaXRoZXIgc2FuaXRpemVkIG9yIHJlbW92ZWQpXHJcbiAgaWYgKFxyXG4gICAgaGFzTW9kaWZpZWRQYXJhbXMgfHxcclxuICAgIHVybC5zZWFyY2hQYXJhbXMudG9TdHJpbmcoKSAhPT0gcmVxLm5leHRVcmwuc2VhcmNoUGFyYW1zLnRvU3RyaW5nKClcclxuICApIHtcclxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QodXJsKTtcclxuICB9XHJcblxyXG4gIC8vIEVuaGFuY2VkIHJlZGlyZWN0aW9uIHBhdGhzIHdpdGggc2VjdXJpdHkgY2hlY2tzXHJcbiAgY29uc3QgZnJQYXRoID0gdHJhbnNsYXRpb25zW3JlcS5uZXh0VXJsLnBhdGhuYW1lXTtcclxuICBpZiAoZnJQYXRoKSB7XHJcbiAgICBsb2dTZWN1cml0eUV2ZW50KFwiRlJFTkNIX1BBVEhfUkVESVJFQ1RcIiwge1xyXG4gICAgICBjbGllbnRJUCxcclxuICAgICAgZnJvbVBhdGg6IHJlcS5uZXh0VXJsLnBhdGhuYW1lLFxyXG4gICAgICB0b1BhdGg6IGZyUGF0aCxcclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdChuZXcgVVJMKGZyUGF0aCwgcmVxLnVybCkpO1xyXG4gIH1cclxuXHJcbiAgLy8gRW5oYW5jZWQgbGFuZ3VhZ2UgaGFuZGxpbmcgd2l0aCBzZWN1cml0eSB2YWxpZGF0aW9uXHJcbiAgaWYgKFxyXG4gICAgIXBhdGhuYW1lLnN0YXJ0c1dpdGgoXCIvZnJcIikgJiZcclxuICAgICFwYXRobmFtZS5zdGFydHNXaXRoKGAvJHtkZWZhdWx0TG9jYWxlfWApICYmXHJcbiAgICAhcGF0aG5hbWUuc3RhcnRzV2l0aChcIi9fbmV4dFwiKSAmJlxyXG4gICAgIXBhdGhuYW1lLnN0YXJ0c1dpdGgoXCIvYXBpXCIpICYmXHJcbiAgICAhcGF0aG5hbWUuc3RhcnRzV2l0aChcIi9zdGF0aWNcIikgJiZcclxuICAgICFwYXRobmFtZS5pbmNsdWRlcyhcIi5cIilcclxuICApIHtcclxuICAgIC8vIEFkZGl0aW9uYWwgc2VjdXJpdHkgY2hlY2sgZm9yIHN1c3BpY2lvdXMgcGF0aHNcclxuICAgIGlmIChwYXRobmFtZS5sZW5ndGggPiAyMDApIHtcclxuICAgICAgbG9nU2VjdXJpdHlFdmVudChcIlNVU1BJQ0lPVVNfTE9OR19QQVRIXCIsIHtcclxuICAgICAgICBjbGllbnRJUCxcclxuICAgICAgICBwYXRoTGVuZ3RoOiBwYXRobmFtZS5sZW5ndGgsXHJcbiAgICAgIH0pO1xyXG4gICAgICByZXR1cm4gbmV3IE5leHRSZXNwb25zZShcIkJhZCBSZXF1ZXN0XCIsIHsgc3RhdHVzOiA0MDAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgdXJsLnBhdGhuYW1lID0gYC9lbiR7cGF0aG5hbWV9YDtcclxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmV3cml0ZSh1cmwpO1xyXG4gIH1cclxuXHJcbiAgLy8gTG9nIHN1Y2Nlc3NmdWwgcmVxdWVzdHMgZm9yIG1vbml0b3JpbmcgKGluIGRldmVsb3BtZW50IG9ubHkpXHJcbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcImRldmVsb3BtZW50XCIgJiYgdXNlcikge1xyXG4gICAgbG9nU2VjdXJpdHlFdmVudChcIlNVQ0NFU1NGVUxfUkVRVUVTVFwiLCB7XHJcbiAgICAgIGNsaWVudElQLFxyXG4gICAgICBwYXRobmFtZSxcclxuICAgICAgdXNlcklkOiB1c2VyLl9pZCxcclxuICAgICAgcm9sZXM6IHVzZXIucm9sZXMsXHJcbiAgICB9KTtcclxuICB9XHJcblxyXG4gIHJldHVybiBpMThuUm91dGVyKHJlcSwgaTE4bkNvbmZpZyk7XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBjb25maWcgPSB7XHJcbiAgbWF0Y2hlcjogW1xyXG4gICAgLypcclxuICAgICAqIE1hdGNoIGFsbCByZXF1ZXN0IHBhdGhzIGV4Y2VwdCBmb3IgdGhlIG9uZXMgc3RhcnRpbmcgd2l0aDpcclxuICAgICAqIC0gYXBpIChBUEkgcm91dGVzKVxyXG4gICAgICogLSBfbmV4dC9zdGF0aWMgKHN0YXRpYyBmaWxlcylcclxuICAgICAqIC0gX25leHQvaW1hZ2UgKGltYWdlIG9wdGltaXphdGlvbiBmaWxlcylcclxuICAgICAqIC0gZmF2aWNvbi5pY28gKGZhdmljb24gZmlsZSlcclxuICAgICAqIC0gcHVibGljIGZvbGRlclxyXG4gICAgICogLSBmaWxlcyB3aXRoIGV4dGVuc2lvbnMgKGltYWdlcywgZm9udHMsIGV0Yy4pXHJcbiAgICAgKi9cclxuICAgIFwiLygoPyFhcGl8X25leHQvc3RhdGljfF9uZXh0L2ltYWdlfGZhdmljb24uaWNvfHB1YmxpY3wuKlxcXFwuLiopLiopXCIsXHJcbiAgXSxcclxufTtcclxuIl0sIm5hbWVzIjpbImkxOG5Sb3V0ZXIiLCJqd3RWZXJpZnkiLCJOZXh0UmVzcG9uc2UiLCJjb29raWUiLCJpMThuQ29uZmlnIiwiUm9sZSIsImdldFJvdXRlc0xpc3RCeVJvbGUiLCJ0cmFuc2xhdGlvbnMiLCJhbGxvd2VkUGFyYW1zIiwiYWRtaW5Sb3V0ZXMiLCJhdXRoUm91dGVzIiwiYmFzZVVybEJhY2tvZmZpY2UiLCJiYXNlVXJsRnJvbnRvZmZpY2UiLCJjYW5kaWRhdGVSb3V0ZXMiLCJTRUNVUklUWV9DT05GSUciLCJNQVhfUkVRVUVTVFNfUEVSX01JTlVURSIsIkpXVF9BTEdPUklUSE0iLCJTVVNQSUNJT1VTX1BBVFRFUk5TIiwicmF0ZUxpbWl0U3RvcmUiLCJNYXAiLCJsb2dTZWN1cml0eUV2ZW50IiwiZXZlbnQiLCJkZXRhaWxzIiwiY29uc29sZSIsIndhcm4iLCJ0aW1lc3RhbXAiLCJEYXRlIiwidG9JU09TdHJpbmciLCJ2ZXJpZnlUb2tlbiIsInRva2VuIiwiY2xpZW50SVAiLCJyZWFzb24iLCJqd3RSZWdleCIsInRlc3QiLCJ0b2tlblByZWZpeCIsInN1YnN0cmluZyIsImp3dFNlY3JldCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX0pXVF9TRUNSRVQiLCJsZW5ndGgiLCJFcnJvciIsInNlY3JldCIsIlRleHRFbmNvZGVyIiwiZW5jb2RlIiwicGF5bG9hZCIsImFsZ29yaXRobXMiLCJpc3N1ZXIiLCJKV1RfSVNTVUVSIiwiYXVkaWVuY2UiLCJKV1RfQVVESUVOQ0UiLCJfaWQiLCJyb2xlcyIsIkFycmF5IiwiaXNBcnJheSIsImhhc0lkIiwiaGFzUm9sZXMiLCJ0b2tlbkFnZSIsIm5vdyIsImlhdCIsInVzZXJJZCIsImVycm9yIiwibmFtZSIsIm1lc3NhZ2UiLCJjaGVja1JhdGVMaW1pdCIsIndpbmRvd1N0YXJ0IiwiaGFzIiwic2V0IiwicmVxdWVzdHMiLCJnZXQiLCJ2YWxpZFJlcXVlc3RzIiwiZmlsdGVyIiwicHVzaCIsInNhbml0aXplSW5wdXQiLCJ2YWx1ZSIsInNhbml0aXplZCIsImZvckVhY2giLCJwYXR0ZXJuIiwicmVwbGFjZSIsInRyaW0iLCJzZXRTZWN1cml0eUhlYWRlcnMiLCJyZXNwb25zZSIsImhlYWRlcnMiLCJkZWxldGUiLCJtaWRkbGV3YXJlIiwicmVxIiwidXJsIiwibmV4dFVybCIsImNsb25lIiwiZGVmYXVsdExvY2FsZSIsInBhdGhuYW1lIiwiaXAiLCJuZXh0Iiwic3RhdHVzIiwidG9TdHJpbmciLCJoYXNNb2RpZmllZFBhcmFtcyIsImtleSIsInNlYXJjaFBhcmFtcyIsImVudHJpZXMiLCJzYW5pdGl6ZWRWYWx1ZSIsIm9yaWdpbmFsVmFsdWUiLCJwYXRoU3RyaW5nIiwidG9Mb3dlckNhc2UiLCJoYXNTdXNwaWNpb3VzUGF0aCIsInNvbWUiLCJjb29raWVzIiwicGFyc2UiLCJhY2Nlc3NUb2tlbiIsInJlZnJlc2hUb2tlbiIsImlzUHJvdGVjdGVkUm91dGUiLCJpbmNsdWRlcyIsImxvZ2luIiwicm91dGUiLCJyZWRpcmVjdCIsInVzZXIiLCJsb2dvdXQiLCJtZW51TGlzdCIsImNoZWNrUm9sZSIsIml0ZW0iLCJiYXNlVVJMIiwicmVnaXN0ZXIiLCJyZWRpcmVjdFBhdGgiLCJBRE1JTiIsImhvbWUiLCJFRElUT1IiLCJibG9ncyIsIkNBTkRJREFURSIsIm15QXBwbGljYXRpb25zIiwiZnJvbVBhdGgiLCJ0b1BhdGgiLCJyZW1vdmVkUGFyYW1zIiwicGFyYW0iLCJrZXlzIiwiZnJQYXRoIiwiVVJMIiwic3RhcnRzV2l0aCIsInBhdGhMZW5ndGgiLCJyZXdyaXRlIiwiY29uZmlnIiwibWF0Y2hlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.js\n");

/***/ })

});