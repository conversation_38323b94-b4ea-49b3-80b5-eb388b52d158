"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/config/allowedParams.js":
/*!*************************************!*\
  !*** ./src/config/allowedParams.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allowedParams: () => (/* binding */ allowedParams)\n/* harmony export */ });\nconst allowedParams = new Set([\n    \"industry\",\n    \"keyWord\",\n    \"country\",\n    \"pageNumber\",\n    \"keyword\",\n    \"success\",\n    \"error\",\n    \"token\",\n    \"step\",\n    \"contractType\",\n    \"jobDescriptionLanguages\",\n    \"levelOfExperience\",\n    \"minExperience\",\n    \"maxExperience\",\n    \"opportunityType\",\n    \"list\"\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL2NvbmZpZy9hbGxvd2VkUGFyYW1zLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxNQUFNQSxnQkFBZ0IsSUFBSUMsSUFBSTtJQUNuQztJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtDQUNELEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbmZpZy9hbGxvd2VkUGFyYW1zLmpzPzdmMDgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGFsbG93ZWRQYXJhbXMgPSBuZXcgU2V0KFtcclxuICBcImluZHVzdHJ5XCIsXHJcbiAgXCJrZXlXb3JkXCIsXHJcbiAgXCJjb3VudHJ5XCIsXHJcbiAgXCJwYWdlTnVtYmVyXCIsXHJcbiAgXCJrZXl3b3JkXCIsXHJcbiAgXCJzdWNjZXNzXCIsXHJcbiAgXCJlcnJvclwiLFxyXG4gIFwidG9rZW5cIixcclxuICBcInN0ZXBcIixcclxuICBcImNvbnRyYWN0VHlwZVwiLFxyXG4gIFwiam9iRGVzY3JpcHRpb25MYW5ndWFnZXNcIixcclxuICBcImxldmVsT2ZFeHBlcmllbmNlXCIsXHJcbiAgXCJtaW5FeHBlcmllbmNlXCIsXHJcbiAgXCJtYXhFeHBlcmllbmNlXCIsXHJcbiAgXCJvcHBvcnR1bml0eVR5cGVcIixcclxuICBcImxpc3RcIlxyXG5dKTtcclxuIl0sIm5hbWVzIjpbImFsbG93ZWRQYXJhbXMiLCJTZXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(middleware)/./src/config/allowedParams.js\n");

/***/ }),

/***/ "(middleware)/./src/config/countries.js":
/*!*********************************!*\
  !*** ./src/config/countries.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COUNTRIES_LIST_FLAG: () => (/* binding */ COUNTRIES_LIST_FLAG),\n/* harmony export */   OFFICES_COUNTRIES_LIST: () => (/* binding */ OFFICES_COUNTRIES_LIST),\n/* harmony export */   OFFICES_ZONE_LIST: () => (/* binding */ OFFICES_ZONE_LIST),\n/* harmony export */   OfficesCountries: () => (/* binding */ OfficesCountries),\n/* harmony export */   TeamCountries: () => (/* binding */ TeamCountries)\n/* harmony export */ });\n/* harmony import */ var _assets_images_countries_tunisia_png__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/assets/images/countries/tunisia.png */ \"(middleware)/./src/assets/images/countries/tunisia.png\");\n/* harmony import */ var _assets_images_countries_algeria_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/assets/images/countries/algeria.png */ \"(middleware)/./src/assets/images/countries/algeria.png\");\n/* harmony import */ var _assets_images_countries_morocco_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/assets/images/countries/morocco.png */ \"(middleware)/./src/assets/images/countries/morocco.png\");\n/* harmony import */ var _assets_images_countries_libya_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/countries/libya.png */ \"(middleware)/./src/assets/images/countries/libya.png\");\n/* harmony import */ var _assets_images_countries_egypt_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/countries/egypt.png */ \"(middleware)/./src/assets/images/countries/egypt.png\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../helpers/routesList */ \"(middleware)/./src/helpers/routesList.js\");\n\n\n\n\n\n\nconst TeamCountries = {\n    TUNISIA: \"TUNISIA\",\n    ALGERIA: \"ALGERIA\",\n    MOROCCO: \"MOROCCO\"\n};\nconst OfficesCountries = {\n    TUNISIA: \"TUNISIA\",\n    ALGERIA: \"ALGERIA\",\n    Qatar: \"Qatar\",\n    UAE: \"UAE\",\n    IRAQ: \"IRAQ\",\n    SaudiArabia: \"Saudi Arabia\",\n    ALGERIAHASSI: \"ALGERIAHASSI\",\n    ALGERIAHYDRA: \"ALGERIAHYDRA\",\n    MOROCCO: \"MOROCCO\",\n    EGYPT: \"EGYPT\",\n    LIBYA: \"LIBYA\",\n    FRANCE: \"FRANCE\",\n    SWITZERLAND: \"SWITZERLAND\"\n};\nconst COUNTRIES_LIST_FLAG = [\n    {\n        value: \"TUNISIA\",\n        label: \"Tunisia\",\n        flag: _assets_images_countries_tunisia_png__WEBPACK_IMPORTED_MODULE_0__[\"default\"].src\n    },\n    {\n        value: \"ALGERIAHASSI\",\n        label: \"Hassi Messaoud, Algeria\",\n        flag: _assets_images_countries_algeria_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src\n    },\n    {\n        value: \"ALGERIAHYDRA\",\n        label: \"Hydra, Algeria\",\n        flag: _assets_images_countries_algeria_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src\n    },\n    {\n        value: \"ALGERIA\",\n        label: \"Algeria\",\n        flag: _assets_images_countries_algeria_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src\n    },\n    {\n        value: \"MOROCCO\",\n        label: \"morocco\",\n        flag: _assets_images_countries_morocco_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n    },\n    {\n        value: \"EGYPT\",\n        label: \"Egypt\",\n        flag: _assets_images_countries_egypt_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src\n    },\n    {\n        value: \"LIBYA\",\n        label: \"Libya\",\n        flag: _assets_images_countries_libya_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src\n    }\n];\nconst OFFICES_COUNTRIES_LIST = [\n    {\n        value: \"FRNCE\",\n        label: \"global:countryFrance\",\n        id: \"franceInfo\",\n        idFr: \"franceInfofr\",\n        idPin: \"france\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.francePage.route}`,\n        city: \"global:cityParis\"\n    },\n    {\n        value: \"SWITZERLAND\",\n        label: \"global:countrySwitzerland\",\n        id: \"switzerlandInfo\",\n        idFr: \"switzerlandInfofr\",\n        idPin: \"switzerland\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.contact.route}`,\n        city: \"global:cityMontreux\"\n    },\n    {\n        value: \"SAUDIARABIA\",\n        label: \"global:countrySaudiArabia\",\n        id: \"saudiarabiaInfo\",\n        idFr: \"saudiarabiaInfofr\",\n        idPin: \"saudiarabia\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.ksaPage.route}`,\n        city: \"global:cityRiyadh\"\n    },\n    {\n        value: \"UAE\",\n        label: \"global:countryUAE\",\n        id: \"uaeInfo\",\n        idFr: \"uaeInfofr\",\n        idPin: \"uae\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.dubaiPage.route}`,\n        city: \"global:cityDubai\"\n    },\n    {\n        value: \"QATAR\",\n        label: \"global:countryQatar\",\n        id: \"qatarInfo\",\n        idFr: \"qatarInfofr\",\n        idPin: \"qatar\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.qatarPage.route}`,\n        city: \"global:cityDoha\"\n    },\n    {\n        value: \"TUNISIA\",\n        label: \"global:countryTunisia\",\n        id: \"tunisInfo\",\n        idFr: \"tunisInfofr\",\n        idPin: \"tunisia\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.tunisiaPage.route}`,\n        city: \"global:cityTunis\"\n    },\n    {\n        value: \"ALGERIA\",\n        label: \"global:countryAlgeria\",\n        id: \"algeriaInfo\",\n        idFr: \"algeriaInfofr\",\n        idPin: \"algeria\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.algeriaPage.route}`,\n        city: \"global:cityAlger\"\n    },\n    {\n        value: \"MOROCCO\",\n        label: \"global:countryMorocco\",\n        id: \"moroccoInfo\",\n        idFr: \"moroccoInfofr\",\n        idPin: \"morocco\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.moroccoPage.route}`,\n        city: \"global:cityCasablanca\"\n    },\n    {\n        value: \"EGYPTE\",\n        label: \"global:countryEgypt\",\n        id: \"egypteInfo\",\n        idFr: \"egypteInfofr\",\n        idPin: \"egypte\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.egyptePage.route}`,\n        city: \"global:cityCairo\"\n    },\n    {\n        value: \"LIBYA\",\n        label: \"global:countryLibya\",\n        id: \"libyaInfo\",\n        idFr: \"libyaInfofr\",\n        idPin: \"libya\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.libyaPage.route}`,\n        city: \"global:cityTripoli\"\n    },\n    {\n        value: \"IRAQ\",\n        label: \"global:countryIraq\",\n        id: \"iraqInfo\",\n        idFr: \"iraqInfofr\",\n        idPin: \"iraq\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.contact.route}`,\n        city: \"global:cityBagdad\"\n    }\n];\nconst OFFICES_ZONE_LIST = [\n    {\n        value: \"EUROPEAN\",\n        label: \"global:officeZoneEuropean\",\n        id: \"europeanInfo\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.europePage.route}`\n    },\n    {\n        value: \"MIDDLEEAST\",\n        label: \"global:officeZoneMiddleEast\",\n        id: \"middleeastInfo\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.middleEastPage.route}`\n    },\n    {\n        value: \"AFRICA\",\n        label: \"global:officeZoneAfrica\",\n        id: \"africaInfo\",\n        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_5__.websiteRoutesList.africaPage.route}`\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL2NvbmZpZy9jb3VudHJpZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBZ0U7QUFDQTtBQUNBO0FBQ0o7QUFDQTtBQUNGO0FBRW5ELE1BQU1NLGdCQUFnQjtJQUMzQkMsU0FBUztJQUNUQyxTQUFTO0lBQ1RDLFNBQVM7QUFDWCxFQUFFO0FBRUssTUFBTUMsbUJBQW1CO0lBQzlCSCxTQUFTO0lBQ1RDLFNBQVM7SUFDVEcsT0FBTztJQUNQQyxLQUFLO0lBQ0xDLE1BQU07SUFDTkMsYUFBYTtJQUNiQyxjQUFjO0lBQ2RDLGNBQWM7SUFDZFAsU0FBUztJQUNUUSxPQUFPO0lBQ1BDLE9BQU87SUFDUEMsUUFBUTtJQUNSQyxhQUFhO0FBQ2YsRUFBRTtBQUdLLE1BQU1DLHNCQUFzQjtJQUNqQztRQUFFQyxPQUFPO1FBQVdDLE9BQU87UUFBV0MsTUFBTXhCLDRFQUFXQSxDQUFDeUIsR0FBRztJQUFDO0lBQzVEO1FBQ0VILE9BQU87UUFDUEMsT0FBTztRQUNQQyxNQUFNdkIsNEVBQVdBLENBQUN3QixHQUFHO0lBQ3ZCO0lBQ0E7UUFBRUgsT0FBTztRQUFnQkMsT0FBTztRQUFrQkMsTUFBTXZCLDRFQUFXQSxDQUFDd0IsR0FBRztJQUFDO0lBQ3hFO1FBQUVILE9BQU87UUFBV0MsT0FBTztRQUFXQyxNQUFNdkIsNEVBQVdBLENBQUN3QixHQUFHO0lBQUM7SUFFNUQ7UUFBRUgsT0FBTztRQUFXQyxPQUFPO1FBQVdDLE1BQU10Qiw0RUFBV0EsQ0FBQ3VCLEdBQUc7SUFBQztJQUM1RDtRQUFFSCxPQUFPO1FBQVNDLE9BQU87UUFBU0MsTUFBTXBCLDBFQUFTQSxDQUFDcUIsR0FBRztJQUFDO0lBQ3REO1FBQUVILE9BQU87UUFBU0MsT0FBTztRQUFTQyxNQUFNckIsMEVBQVNBLENBQUNzQixHQUFHO0lBQUM7Q0FDdkQsQ0FBQztBQUVLLE1BQU1DLHlCQUF5QjtJQUNwQztRQUNFSixPQUFPO1FBQ1BDLE9BQU87UUFDUEksSUFBSTtRQUNKQyxNQUFNO1FBRU5DLE9BQU87UUFDUEMsTUFBTSxDQUFDLENBQUMsRUFBRXpCLGtFQUFpQkEsQ0FBQzBCLFVBQVUsQ0FBQ0MsS0FBSyxDQUFDLENBQUM7UUFDOUNDLE1BQU07SUFDUjtJQUNBO1FBQ0VYLE9BQU87UUFDUEMsT0FBTztRQUNQSSxJQUFJO1FBQ0pDLE1BQU07UUFFTkMsT0FBTztRQUNQQyxNQUFNLENBQUMsQ0FBQyxFQUFFekIsa0VBQWlCQSxDQUFDNkIsT0FBTyxDQUFDRixLQUFLLENBQUMsQ0FBQztRQUMzQ0MsTUFBTTtJQUNSO0lBQ0E7UUFDRVgsT0FBTztRQUNQQyxPQUFPO1FBQ1BJLElBQUk7UUFDSkMsTUFBTTtRQUVOQyxPQUFPO1FBQ1BDLE1BQU0sQ0FBQyxDQUFDLEVBQUV6QixrRUFBaUJBLENBQUM4QixPQUFPLENBQUNILEtBQUssQ0FBQyxDQUFDO1FBQzNDQyxNQUFNO0lBQ1I7SUFDQTtRQUNFWCxPQUFPO1FBQ1BDLE9BQU87UUFDUEksSUFBSTtRQUNKQyxNQUFNO1FBRU5DLE9BQU87UUFDUEMsTUFBTSxDQUFDLENBQUMsRUFBRXpCLGtFQUFpQkEsQ0FBQytCLFNBQVMsQ0FBQ0osS0FBSyxDQUFDLENBQUM7UUFDN0NDLE1BQU07SUFDUjtJQUNBO1FBQ0VYLE9BQU87UUFDUEMsT0FBTztRQUNQSSxJQUFJO1FBQ0pDLE1BQU07UUFFTkMsT0FBTztRQUNQQyxNQUFNLENBQUMsQ0FBQyxFQUFFekIsa0VBQWlCQSxDQUFDZ0MsU0FBUyxDQUFDTCxLQUFLLENBQUMsQ0FBQztRQUM3Q0MsTUFBTTtJQUNSO0lBQ0E7UUFDRVgsT0FBTztRQUNQQyxPQUFPO1FBQ1BJLElBQUk7UUFDSkMsTUFBTTtRQUVOQyxPQUFPO1FBQ1BDLE1BQU0sQ0FBQyxDQUFDLEVBQUV6QixrRUFBaUJBLENBQUNpQyxXQUFXLENBQUNOLEtBQUssQ0FBQyxDQUFDO1FBQy9DQyxNQUFNO0lBQ1I7SUFDQTtRQUNFWCxPQUFPO1FBQ1BDLE9BQU87UUFDUEksSUFBSTtRQUNKQyxNQUFNO1FBRU5DLE9BQU87UUFDUEMsTUFBTSxDQUFDLENBQUMsRUFBRXpCLGtFQUFpQkEsQ0FBQ2tDLFdBQVcsQ0FBQ1AsS0FBSyxDQUFDLENBQUM7UUFDL0NDLE1BQU07SUFDUjtJQUNBO1FBQ0VYLE9BQU87UUFDUEMsT0FBTztRQUNQSSxJQUFJO1FBQ0pDLE1BQU07UUFFTkMsT0FBTztRQUNQQyxNQUFNLENBQUMsQ0FBQyxFQUFFekIsa0VBQWlCQSxDQUFDbUMsV0FBVyxDQUFDUixLQUFLLENBQUMsQ0FBQztRQUMvQ0MsTUFBTTtJQUNSO0lBQ0E7UUFDRVgsT0FBTztRQUNQQyxPQUFPO1FBQ1BJLElBQUk7UUFDSkMsTUFBTTtRQUVOQyxPQUFPO1FBQ1BDLE1BQU0sQ0FBQyxDQUFDLEVBQUV6QixrRUFBaUJBLENBQUNvQyxVQUFVLENBQUNULEtBQUssQ0FBQyxDQUFDO1FBQzlDQyxNQUFNO0lBQ1I7SUFDQTtRQUNFWCxPQUFPO1FBQ1BDLE9BQU87UUFDUEksSUFBSTtRQUNKQyxNQUFNO1FBRU5DLE9BQU87UUFDUEMsTUFBTSxDQUFDLENBQUMsRUFBRXpCLGtFQUFpQkEsQ0FBQ3FDLFNBQVMsQ0FBQ1YsS0FBSyxDQUFDLENBQUM7UUFDN0NDLE1BQU07SUFDUjtJQUNBO1FBQ0VYLE9BQU87UUFDUEMsT0FBTztRQUNQSSxJQUFJO1FBQ0pDLE1BQU07UUFFTkMsT0FBTztRQUNQQyxNQUFNLENBQUMsQ0FBQyxFQUFFekIsa0VBQWlCQSxDQUFDNkIsT0FBTyxDQUFDRixLQUFLLENBQUMsQ0FBQztRQUMzQ0MsTUFBTTtJQUNSO0NBQ0QsQ0FBQztBQUVLLE1BQU1VLG9CQUFvQjtJQUMvQjtRQUNFckIsT0FBTztRQUNQQyxPQUFPO1FBQ1BJLElBQUk7UUFDSkcsTUFBTSxDQUFDLENBQUMsRUFBRXpCLGtFQUFpQkEsQ0FBQ3VDLFVBQVUsQ0FBQ1osS0FBSyxDQUFDLENBQUM7SUFDaEQ7SUFDQTtRQUNFVixPQUFPO1FBQ1BDLE9BQU87UUFDUEksSUFBSTtRQUNKRyxNQUFNLENBQUMsQ0FBQyxFQUFFekIsa0VBQWlCQSxDQUFDd0MsY0FBYyxDQUFDYixLQUFLLENBQUMsQ0FBQztJQUNwRDtJQUNBO1FBQ0VWLE9BQU87UUFDUEMsT0FBTztRQUNQSSxJQUFJO1FBQ0pHLE1BQU0sQ0FBQyxDQUFDLEVBQUV6QixrRUFBaUJBLENBQUN5QyxVQUFVLENBQUNkLEtBQUssQ0FBQyxDQUFDO0lBQ2hEO0NBQ0QsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29uZmlnL2NvdW50cmllcy5qcz9kYjEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0dW5pc2lhRmxhZyBmcm9tIFwiQC9hc3NldHMvaW1hZ2VzL2NvdW50cmllcy90dW5pc2lhLnBuZ1wiO1xyXG5pbXBvcnQgYWxnZXJpYUZsYWcgZnJvbSBcIkAvYXNzZXRzL2ltYWdlcy9jb3VudHJpZXMvYWxnZXJpYS5wbmdcIjtcclxuaW1wb3J0IG1vcm9jY29GbGFnIGZyb20gXCJAL2Fzc2V0cy9pbWFnZXMvY291bnRyaWVzL21vcm9jY28ucG5nXCI7XHJcbmltcG9ydCBsaWJ5YUZsYWcgZnJvbSBcIkAvYXNzZXRzL2ltYWdlcy9jb3VudHJpZXMvbGlieWEucG5nXCI7XHJcbmltcG9ydCBlZ3lwdEZsYWcgZnJvbSBcIkAvYXNzZXRzL2ltYWdlcy9jb3VudHJpZXMvZWd5cHQucG5nXCI7XHJcbmltcG9ydCB7IHdlYnNpdGVSb3V0ZXNMaXN0IH0gZnJvbSBcIi4uL2hlbHBlcnMvcm91dGVzTGlzdFwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IFRlYW1Db3VudHJpZXMgPSB7XHJcbiAgVFVOSVNJQTogXCJUVU5JU0lBXCIsXHJcbiAgQUxHRVJJQTogXCJBTEdFUklBXCIsXHJcbiAgTU9ST0NDTzogXCJNT1JPQ0NPXCIsXHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgT2ZmaWNlc0NvdW50cmllcyA9IHtcclxuICBUVU5JU0lBOiBcIlRVTklTSUFcIixcclxuICBBTEdFUklBOiBcIkFMR0VSSUFcIixcclxuICBRYXRhcjogXCJRYXRhclwiLFxyXG4gIFVBRTogXCJVQUVcIixcclxuICBJUkFROiBcIklSQVFcIixcclxuICBTYXVkaUFyYWJpYTogXCJTYXVkaSBBcmFiaWFcIixcclxuICBBTEdFUklBSEFTU0k6IFwiQUxHRVJJQUhBU1NJXCIsXHJcbiAgQUxHRVJJQUhZRFJBOiBcIkFMR0VSSUFIWURSQVwiLFxyXG4gIE1PUk9DQ086IFwiTU9ST0NDT1wiLFxyXG4gIEVHWVBUOiBcIkVHWVBUXCIsXHJcbiAgTElCWUE6IFwiTElCWUFcIixcclxuICBGUkFOQ0U6IFwiRlJBTkNFXCIsXHJcbiAgU1dJVFpFUkxBTkQ6IFwiU1dJVFpFUkxBTkRcIixcclxufTtcclxuXHJcblxyXG5leHBvcnQgY29uc3QgQ09VTlRSSUVTX0xJU1RfRkxBRyA9IFtcclxuICB7IHZhbHVlOiBcIlRVTklTSUFcIiwgbGFiZWw6IFwiVHVuaXNpYVwiLCBmbGFnOiB0dW5pc2lhRmxhZy5zcmMgfSxcclxuICB7XHJcbiAgICB2YWx1ZTogXCJBTEdFUklBSEFTU0lcIixcclxuICAgIGxhYmVsOiBcIkhhc3NpIE1lc3Nhb3VkLCBBbGdlcmlhXCIsXHJcbiAgICBmbGFnOiBhbGdlcmlhRmxhZy5zcmMsXHJcbiAgfSxcclxuICB7IHZhbHVlOiBcIkFMR0VSSUFIWURSQVwiLCBsYWJlbDogXCJIeWRyYSwgQWxnZXJpYVwiLCBmbGFnOiBhbGdlcmlhRmxhZy5zcmMgfSxcclxuICB7IHZhbHVlOiBcIkFMR0VSSUFcIiwgbGFiZWw6IFwiQWxnZXJpYVwiLCBmbGFnOiBhbGdlcmlhRmxhZy5zcmMgfSxcclxuXHJcbiAgeyB2YWx1ZTogXCJNT1JPQ0NPXCIsIGxhYmVsOiBcIm1vcm9jY29cIiwgZmxhZzogbW9yb2Njb0ZsYWcuc3JjIH0sXHJcbiAgeyB2YWx1ZTogXCJFR1lQVFwiLCBsYWJlbDogXCJFZ3lwdFwiLCBmbGFnOiBlZ3lwdEZsYWcuc3JjIH0sXHJcbiAgeyB2YWx1ZTogXCJMSUJZQVwiLCBsYWJlbDogXCJMaWJ5YVwiLCBmbGFnOiBsaWJ5YUZsYWcuc3JjIH0sXHJcbl07XHJcblxyXG5leHBvcnQgY29uc3QgT0ZGSUNFU19DT1VOVFJJRVNfTElTVCA9IFtcclxuICB7XHJcbiAgICB2YWx1ZTogXCJGUk5DRVwiLFxyXG4gICAgbGFiZWw6IFwiZ2xvYmFsOmNvdW50cnlGcmFuY2VcIixcclxuICAgIGlkOiBcImZyYW5jZUluZm9cIixcclxuICAgIGlkRnI6IFwiZnJhbmNlSW5mb2ZyXCIsXHJcblxyXG4gICAgaWRQaW46IFwiZnJhbmNlXCIsXHJcbiAgICBsaW5rOiBgLyR7d2Vic2l0ZVJvdXRlc0xpc3QuZnJhbmNlUGFnZS5yb3V0ZX1gLFxyXG4gICAgY2l0eTogXCJnbG9iYWw6Y2l0eVBhcmlzXCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICB2YWx1ZTogXCJTV0lUWkVSTEFORFwiLFxyXG4gICAgbGFiZWw6IFwiZ2xvYmFsOmNvdW50cnlTd2l0emVybGFuZFwiLFxyXG4gICAgaWQ6IFwic3dpdHplcmxhbmRJbmZvXCIsXHJcbiAgICBpZEZyOiBcInN3aXR6ZXJsYW5kSW5mb2ZyXCIsXHJcblxyXG4gICAgaWRQaW46IFwic3dpdHplcmxhbmRcIixcclxuICAgIGxpbms6IGAvJHt3ZWJzaXRlUm91dGVzTGlzdC5jb250YWN0LnJvdXRlfWAsXHJcbiAgICBjaXR5OiBcImdsb2JhbDpjaXR5TW9udHJldXhcIixcclxuICB9LFxyXG4gIHtcclxuICAgIHZhbHVlOiBcIlNBVURJQVJBQklBXCIsXHJcbiAgICBsYWJlbDogXCJnbG9iYWw6Y291bnRyeVNhdWRpQXJhYmlhXCIsXHJcbiAgICBpZDogXCJzYXVkaWFyYWJpYUluZm9cIixcclxuICAgIGlkRnI6IFwic2F1ZGlhcmFiaWFJbmZvZnJcIixcclxuXHJcbiAgICBpZFBpbjogXCJzYXVkaWFyYWJpYVwiLFxyXG4gICAgbGluazogYC8ke3dlYnNpdGVSb3V0ZXNMaXN0LmtzYVBhZ2Uucm91dGV9YCxcclxuICAgIGNpdHk6IFwiZ2xvYmFsOmNpdHlSaXlhZGhcIixcclxuICB9LFxyXG4gIHtcclxuICAgIHZhbHVlOiBcIlVBRVwiLFxyXG4gICAgbGFiZWw6IFwiZ2xvYmFsOmNvdW50cnlVQUVcIixcclxuICAgIGlkOiBcInVhZUluZm9cIixcclxuICAgIGlkRnI6IFwidWFlSW5mb2ZyXCIsXHJcblxyXG4gICAgaWRQaW46IFwidWFlXCIsXHJcbiAgICBsaW5rOiBgLyR7d2Vic2l0ZVJvdXRlc0xpc3QuZHViYWlQYWdlLnJvdXRlfWAsXHJcbiAgICBjaXR5OiBcImdsb2JhbDpjaXR5RHViYWlcIixcclxuICB9LFxyXG4gIHtcclxuICAgIHZhbHVlOiBcIlFBVEFSXCIsXHJcbiAgICBsYWJlbDogXCJnbG9iYWw6Y291bnRyeVFhdGFyXCIsXHJcbiAgICBpZDogXCJxYXRhckluZm9cIixcclxuICAgIGlkRnI6IFwicWF0YXJJbmZvZnJcIixcclxuXHJcbiAgICBpZFBpbjogXCJxYXRhclwiLFxyXG4gICAgbGluazogYC8ke3dlYnNpdGVSb3V0ZXNMaXN0LnFhdGFyUGFnZS5yb3V0ZX1gLFxyXG4gICAgY2l0eTogXCJnbG9iYWw6Y2l0eURvaGFcIixcclxuICB9LFxyXG4gIHtcclxuICAgIHZhbHVlOiBcIlRVTklTSUFcIixcclxuICAgIGxhYmVsOiBcImdsb2JhbDpjb3VudHJ5VHVuaXNpYVwiLFxyXG4gICAgaWQ6IFwidHVuaXNJbmZvXCIsXHJcbiAgICBpZEZyOiBcInR1bmlzSW5mb2ZyXCIsXHJcblxyXG4gICAgaWRQaW46IFwidHVuaXNpYVwiLFxyXG4gICAgbGluazogYC8ke3dlYnNpdGVSb3V0ZXNMaXN0LnR1bmlzaWFQYWdlLnJvdXRlfWAsXHJcbiAgICBjaXR5OiBcImdsb2JhbDpjaXR5VHVuaXNcIixcclxuICB9LFxyXG4gIHtcclxuICAgIHZhbHVlOiBcIkFMR0VSSUFcIixcclxuICAgIGxhYmVsOiBcImdsb2JhbDpjb3VudHJ5QWxnZXJpYVwiLFxyXG4gICAgaWQ6IFwiYWxnZXJpYUluZm9cIixcclxuICAgIGlkRnI6IFwiYWxnZXJpYUluZm9mclwiLFxyXG5cclxuICAgIGlkUGluOiBcImFsZ2VyaWFcIixcclxuICAgIGxpbms6IGAvJHt3ZWJzaXRlUm91dGVzTGlzdC5hbGdlcmlhUGFnZS5yb3V0ZX1gLFxyXG4gICAgY2l0eTogXCJnbG9iYWw6Y2l0eUFsZ2VyXCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICB2YWx1ZTogXCJNT1JPQ0NPXCIsXHJcbiAgICBsYWJlbDogXCJnbG9iYWw6Y291bnRyeU1vcm9jY29cIixcclxuICAgIGlkOiBcIm1vcm9jY29JbmZvXCIsXHJcbiAgICBpZEZyOiBcIm1vcm9jY29JbmZvZnJcIixcclxuXHJcbiAgICBpZFBpbjogXCJtb3JvY2NvXCIsXHJcbiAgICBsaW5rOiBgLyR7d2Vic2l0ZVJvdXRlc0xpc3QubW9yb2Njb1BhZ2Uucm91dGV9YCxcclxuICAgIGNpdHk6IFwiZ2xvYmFsOmNpdHlDYXNhYmxhbmNhXCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICB2YWx1ZTogXCJFR1lQVEVcIixcclxuICAgIGxhYmVsOiBcImdsb2JhbDpjb3VudHJ5RWd5cHRcIixcclxuICAgIGlkOiBcImVneXB0ZUluZm9cIixcclxuICAgIGlkRnI6IFwiZWd5cHRlSW5mb2ZyXCIsXHJcblxyXG4gICAgaWRQaW46IFwiZWd5cHRlXCIsXHJcbiAgICBsaW5rOiBgLyR7d2Vic2l0ZVJvdXRlc0xpc3QuZWd5cHRlUGFnZS5yb3V0ZX1gLFxyXG4gICAgY2l0eTogXCJnbG9iYWw6Y2l0eUNhaXJvXCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICB2YWx1ZTogXCJMSUJZQVwiLFxyXG4gICAgbGFiZWw6IFwiZ2xvYmFsOmNvdW50cnlMaWJ5YVwiLFxyXG4gICAgaWQ6IFwibGlieWFJbmZvXCIsXHJcbiAgICBpZEZyOiBcImxpYnlhSW5mb2ZyXCIsXHJcblxyXG4gICAgaWRQaW46IFwibGlieWFcIixcclxuICAgIGxpbms6IGAvJHt3ZWJzaXRlUm91dGVzTGlzdC5saWJ5YVBhZ2Uucm91dGV9YCxcclxuICAgIGNpdHk6IFwiZ2xvYmFsOmNpdHlUcmlwb2xpXCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICB2YWx1ZTogXCJJUkFRXCIsXHJcbiAgICBsYWJlbDogXCJnbG9iYWw6Y291bnRyeUlyYXFcIixcclxuICAgIGlkOiBcImlyYXFJbmZvXCIsXHJcbiAgICBpZEZyOiBcImlyYXFJbmZvZnJcIixcclxuXHJcbiAgICBpZFBpbjogXCJpcmFxXCIsXHJcbiAgICBsaW5rOiBgLyR7d2Vic2l0ZVJvdXRlc0xpc3QuY29udGFjdC5yb3V0ZX1gLFxyXG4gICAgY2l0eTogXCJnbG9iYWw6Y2l0eUJhZ2RhZFwiLFxyXG4gIH0sXHJcbl07XHJcblxyXG5leHBvcnQgY29uc3QgT0ZGSUNFU19aT05FX0xJU1QgPSBbXHJcbiAge1xyXG4gICAgdmFsdWU6IFwiRVVST1BFQU5cIixcclxuICAgIGxhYmVsOiBcImdsb2JhbDpvZmZpY2Vab25lRXVyb3BlYW5cIixcclxuICAgIGlkOiBcImV1cm9wZWFuSW5mb1wiLFxyXG4gICAgbGluazogYC8ke3dlYnNpdGVSb3V0ZXNMaXN0LmV1cm9wZVBhZ2Uucm91dGV9YCxcclxuICB9LFxyXG4gIHtcclxuICAgIHZhbHVlOiBcIk1JRERMRUVBU1RcIixcclxuICAgIGxhYmVsOiBcImdsb2JhbDpvZmZpY2Vab25lTWlkZGxlRWFzdFwiLFxyXG4gICAgaWQ6IFwibWlkZGxlZWFzdEluZm9cIixcclxuICAgIGxpbms6IGAvJHt3ZWJzaXRlUm91dGVzTGlzdC5taWRkbGVFYXN0UGFnZS5yb3V0ZX1gLFxyXG4gIH0sXHJcbiAge1xyXG4gICAgdmFsdWU6IFwiQUZSSUNBXCIsXHJcbiAgICBsYWJlbDogXCJnbG9iYWw6b2ZmaWNlWm9uZUFmcmljYVwiLFxyXG4gICAgaWQ6IFwiYWZyaWNhSW5mb1wiLFxyXG4gICAgbGluazogYC8ke3dlYnNpdGVSb3V0ZXNMaXN0LmFmcmljYVBhZ2Uucm91dGV9YCxcclxuICB9LFxyXG5dO1xyXG4iXSwibmFtZXMiOlsidHVuaXNpYUZsYWciLCJhbGdlcmlhRmxhZyIsIm1vcm9jY29GbGFnIiwibGlieWFGbGFnIiwiZWd5cHRGbGFnIiwid2Vic2l0ZVJvdXRlc0xpc3QiLCJUZWFtQ291bnRyaWVzIiwiVFVOSVNJQSIsIkFMR0VSSUEiLCJNT1JPQ0NPIiwiT2ZmaWNlc0NvdW50cmllcyIsIlFhdGFyIiwiVUFFIiwiSVJBUSIsIlNhdWRpQXJhYmlhIiwiQUxHRVJJQUhBU1NJIiwiQUxHRVJJQUhZRFJBIiwiRUdZUFQiLCJMSUJZQSIsIkZSQU5DRSIsIlNXSVRaRVJMQU5EIiwiQ09VTlRSSUVTX0xJU1RfRkxBRyIsInZhbHVlIiwibGFiZWwiLCJmbGFnIiwic3JjIiwiT0ZGSUNFU19DT1VOVFJJRVNfTElTVCIsImlkIiwiaWRGciIsImlkUGluIiwibGluayIsImZyYW5jZVBhZ2UiLCJyb3V0ZSIsImNpdHkiLCJjb250YWN0Iiwia3NhUGFnZSIsImR1YmFpUGFnZSIsInFhdGFyUGFnZSIsInR1bmlzaWFQYWdlIiwiYWxnZXJpYVBhZ2UiLCJtb3JvY2NvUGFnZSIsImVneXB0ZVBhZ2UiLCJsaWJ5YVBhZ2UiLCJPRkZJQ0VTX1pPTkVfTElTVCIsImV1cm9wZVBhZ2UiLCJtaWRkbGVFYXN0UGFnZSIsImFmcmljYVBhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(middleware)/./src/config/countries.js\n");

/***/ }),

/***/ "(middleware)/./src/helpers/MenuList.js":
/*!*********************************!*\
  !*** ./src/helpers/MenuList.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MenuList: () => (/* binding */ MenuList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(middleware)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var _routesList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./routesList */ \"(middleware)/./src/helpers/routesList.js\");\n/* harmony import */ var _assets_images_icons_menu_items_svg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/assets/images/icons/menu-items.svg */ \"(middleware)/./src/assets/images/icons/menu-items.svg\");\n/* harmony import */ var _assets_images_icons_profilecandidat_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/profilecandidat.svg */ \"(middleware)/./src/assets/images/icons/profilecandidat.svg\");\n/* harmony import */ var _assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/applicationIcon.svg */ \"(middleware)/./src/assets/images/icons/applicationIcon.svg\");\n/* harmony import */ var _assets_images_icons_favoritsIcon_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/icons/favoritsIcon.svg */ \"(middleware)/./src/assets/images/icons/favoritsIcon.svg\");\n/* harmony import */ var _assets_images_icons_articles_icon_svg_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/articles-icon-svg.svg */ \"(middleware)/./src/assets/images/icons/articles-icon-svg.svg\");\n/* harmony import */ var _assets_images_icons_svgnotifdashboard_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/icons/svgnotifdashboard.svg */ \"(middleware)/./src/assets/images/icons/svgnotifdashboard.svg\");\n/* harmony import */ var _assets_images_icons_categoriesdasyhboard_svg__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/assets/images/icons/categoriesdasyhboard.svg */ \"(middleware)/./src/assets/images/icons/categoriesdasyhboard.svg\");\n/* harmony import */ var _assets_images_icons_opportunitydashboard_svg__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/assets/images/icons/opportunitydashboard.svg */ \"(middleware)/./src/assets/images/icons/opportunitydashboard.svg\");\n/* harmony import */ var _assets_images_icons_settintgs_icon_svg__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/assets/images/icons/settintgs-icon.svg */ \"(middleware)/./src/assets/images/icons/settintgs-icon.svg\");\n/* harmony import */ var _assets_images_icons_users_icons_svg__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/assets/images/icons/users-icons.svg */ \"(middleware)/./src/assets/images/icons/users-icons.svg\");\n/* harmony import */ var _assets_images_icons_svgResume_svg__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/assets/images/icons/svgResume.svg */ \"(middleware)/./src/assets/images/icons/svgResume.svg\");\n/* harmony import */ var _assets_images_icons_mail_svg__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/assets/images/icons/mail.svg */ \"(middleware)/./src/assets/images/icons/mail.svg\");\n/* harmony import */ var _assets_images_icons_logoutIcon_svg__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/assets/images/icons/logoutIcon.svg */ \"(middleware)/./src/assets/images/icons/logoutIcon.svg\");\n/* harmony import */ var _assets_images_icons_StatisticsIcon_svg__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/assets/images/icons/StatisticsIcon.svg */ \"(middleware)/./src/assets/images/icons/StatisticsIcon.svg\");\n\"use client\";\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MenuList = {\n    website: [\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.aboutUs.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.aboutUs.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.aboutUs.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.aboutUs.i18nName\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.services.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.services.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.services.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.services.i18nName,\n            subItems: [\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.services.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.payrollServices.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.payrollServices.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.payrollServices.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.payrollServices.i18nName\n                },\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.services.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.consultingServices.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.consultingServices.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.consultingServices.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.consultingServices.i18nName\n                },\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.services.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.technicalAssistance.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.technicalAssistance.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.technicalAssistance.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.technicalAssistance.i18nName\n                },\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.services.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.directHiring.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.directHiring.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.directHiring.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.directHiring.i18nName\n                },\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.services.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.aiSourcing.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.aiSourcing.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.aiSourcing.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.aiSourcing.i18nName\n                }\n            ]\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.opportunities.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.opportunities.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.opportunities.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.opportunities.i18nName,\n            subItems: [\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.jobCategory.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.transportation.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.transportation.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.transportation.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.transportation.i18nName,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"color-industry transport\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                        lineNumber: 85,\n                        columnNumber: 17\n                    }, undefined)\n                },\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.jobCategory.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.itTelecom.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.itTelecom.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.itTelecom.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.itTelecom.i18nName,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"color-industry it-telecom\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                        lineNumber: 92,\n                        columnNumber: 17\n                    }, undefined)\n                },\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.jobCategory.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.insuranceBanking.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.insuranceBanking.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.insuranceBanking.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.insuranceBanking.i18nName,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"color-industry banking-insurance\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                        lineNumber: 99,\n                        columnNumber: 17\n                    }, undefined)\n                },\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.jobCategory.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.energies.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.energies.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.energies.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.energies.i18nName,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"color-industry energy\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                        lineNumber: 106,\n                        columnNumber: 17\n                    }, undefined)\n                },\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.jobCategory.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.pharmaceutical.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.pharmaceutical.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.pharmaceutical.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.pharmaceutical.i18nName,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"color-industry pharmaceutical\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                        lineNumber: 114,\n                        columnNumber: 17\n                    }, undefined)\n                },\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.jobCategory.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.others.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.others.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.others.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.others.i18nName,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"color-industry other\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                        lineNumber: 121,\n                        columnNumber: 17\n                    }, undefined)\n                }\n            ]\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.resources.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.resources.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.resources.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.resources.i18nName,\n            subItems: [\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.blog.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.blog.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.blog.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.blog.i18nName\n                },\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.events.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.events.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.events.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.events.i18nName\n                },\n                {\n                    route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.guide.route}`,\n                    name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.guide.name,\n                    key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.guide.key,\n                    i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.guide.i18nName\n                }\n            ]\n        },\n        // {\n        //   route: `/${websiteRoutesList.blog.route}`,\n        //   name: websiteRoutesList.blog.name,\n        //   key: websiteRoutesList.blog.key,\n        //   i18nName: websiteRoutesList.blog.i18nName,\n        // },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.joinUs.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.joinUs.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.joinUs.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.joinUs.i18nName\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.contact.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.contact.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.contact.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.websiteRoutesList.contact.i18nName\n        }\n    ],\n    candidate: [\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlFrontoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.home.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.home.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.home.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.home.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_menu_items_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 178,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlFrontoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.myApplications.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.myApplications.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.myApplications.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.myApplications.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 185,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlFrontoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.resumes.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.resumes.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.resumes.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.resumes.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_svgResume_svg__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 192,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlFrontoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.favoris.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.favoris.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.favoris.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.favoris.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_favoritsIcon_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 199,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlFrontoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.notifications.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.notifications.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.notifications.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.notifications.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_svgnotifdashboard_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 206,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlFrontoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.myProfile.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.myProfile.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.myProfile.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.myProfile.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_profilecandidat_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 213,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlFrontoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.settings.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.settings.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.settings.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.candidateRoutes.settings.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_settintgs_icon_svg__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 221,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.authRoutes.logout.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.authRoutes.logout.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.authRoutes.logout.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.authRoutes.logout.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_logoutIcon_svg__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 228,\n                columnNumber: 16\n            }, undefined)\n        }\n    ],\n    admin: [\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.home.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.home.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.home.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.home.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_menu_items_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 237,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.statistics.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.statistics.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.statistics.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.statistics.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_StatisticsIcon_svg__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 244,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.myProfile.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.myProfile.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.myProfile.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.myProfile.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_profilecandidat_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 251,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.guides.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.guides.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.guides.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.guides.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_menu_items_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 259,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.blogs.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.blogs.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.blogs.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.blogs.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_articles_icon_svg_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 266,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.guides.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.categories.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.categoriesguide.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.categoriesguide.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.categoriesguide.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_categoriesdasyhboard_svg__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 273,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.comments.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.comments.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.comments.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.comments.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 280,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.opportunities.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.opportunities.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.opportunities.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.opportunities.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_opportunitydashboard_svg__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 287,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.events.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.events.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.events.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.events.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_articles_icon_svg_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 294,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.categories.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.categories.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.categories.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.categories.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_categoriesdasyhboard_svg__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 301,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.notifications.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.notifications.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.notifications.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.notifications.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_svgnotifdashboard_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 308,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.settings.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.settings.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.settings.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.settings.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_settintgs_icon_svg__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 316,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.contacts.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.contacts.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.contacts.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.contacts.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 323,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.newsletters.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.newsletters.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.newsletters.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.newsletters.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_mail_svg__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 330,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.seoSettings.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.seoSettings.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.seoSettings.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.seoSettings.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_settintgs_icon_svg__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 337,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.users.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.users.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.users.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.users.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_users_icons_svg__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 344,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.applications.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.applications.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.applications.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.applications.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_opportunitydashboard_svg__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 351,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.sliders.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.sliders.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.sliders.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.sliders.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_settintgs_icon_svg__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 358,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.downloadReport.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.downloadReport.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.downloadReport.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.adminRoutes.downloadReport.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 365,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.authRoutes.logout.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.authRoutes.logout.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.authRoutes.logout.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.authRoutes.logout.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_logoutIcon_svg__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 372,\n                columnNumber: 16\n            }, undefined)\n        }\n    ],\n    editor: [\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.home.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.home.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.home.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.home.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_menu_items_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 381,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.myProfile.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.myProfile.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.myProfile.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.myProfile.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_menu_items_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 388,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.blogs.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.blogs.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.blogs.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.blogs.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_articles_icon_svg_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 395,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.guides.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.guides.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.guides.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.guides.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_menu_items_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 402,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.comments.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.comments.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.comments.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.comments.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 409,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.opportunities.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.opportunities.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.opportunities.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.opportunities.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_opportunitydashboard_svg__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 416,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.events.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.events.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.events.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.events.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_articles_icon_svg_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 423,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.categories.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.categories.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.categories.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.categories.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_categoriesdasyhboard_svg__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 430,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.notifications.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.notifications.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.notifications.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.notifications.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_svgnotifdashboard_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 437,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.settings.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.settings.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.settings.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.settings.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_settintgs_icon_svg__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 445,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.sliders.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.sliders.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.sliders.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.sliders.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_settintgs_icon_svg__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 452,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.contacts.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.contacts.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.contacts.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.contacts.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 459,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.newsletters.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.newsletters.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.newsletters.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.newsletters.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_mail_svg__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 466,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.baseUrlBackoffice.baseURL.route}/${_routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.seoSettings.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.seoSettings.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.seoSettings.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.editorRoutes.seoSettings.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_settintgs_icon_svg__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 473,\n                columnNumber: 16\n            }, undefined)\n        },\n        {\n            route: `/${_routesList__WEBPACK_IMPORTED_MODULE_1__.authRoutes.logout.route}`,\n            name: _routesList__WEBPACK_IMPORTED_MODULE_1__.authRoutes.logout.name,\n            key: _routesList__WEBPACK_IMPORTED_MODULE_1__.authRoutes.logout.key,\n            i18nName: _routesList__WEBPACK_IMPORTED_MODULE_1__.authRoutes.logout.i18nName,\n            svgIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_logoutIcon_svg__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\helpers\\\\MenuList.js\",\n                lineNumber: 480,\n                columnNumber: 16\n            }, undefined)\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/helpers/MenuList.js\n");

/***/ }),

/***/ "(middleware)/./src/helpers/routesList.js":
/*!***********************************!*\
  !*** ./src/helpers/routesList.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminPermissionsRoutes: () => (/* binding */ adminPermissionsRoutes),\n/* harmony export */   adminRoutes: () => (/* binding */ adminRoutes),\n/* harmony export */   authRoutes: () => (/* binding */ authRoutes),\n/* harmony export */   baseUrlBackoffice: () => (/* binding */ baseUrlBackoffice),\n/* harmony export */   baseUrlFrontoffice: () => (/* binding */ baseUrlFrontoffice),\n/* harmony export */   candidatePermissionsRoutes: () => (/* binding */ candidatePermissionsRoutes),\n/* harmony export */   candidateRoutes: () => (/* binding */ candidateRoutes),\n/* harmony export */   commonRoutes: () => (/* binding */ commonRoutes),\n/* harmony export */   editorPermissionsRoutes: () => (/* binding */ editorPermissionsRoutes),\n/* harmony export */   editorRoutes: () => (/* binding */ editorRoutes),\n/* harmony export */   websiteRoutesList: () => (/* binding */ websiteRoutesList)\n/* harmony export */ });\nconst baseUrlBackoffice = {\n    baseURL: {\n        route: \"backoffice\",\n        name: \"Home\",\n        key: \"baseUrlBackoffice\"\n    }\n};\nconst baseUrlFrontoffice = {\n    baseURL: {\n        route: \"dashboard\",\n        name: \"Home\",\n        key: \"baseUrlBackoffice\"\n    }\n};\nconst websiteRoutesList = {\n    home: {\n        route: \"\",\n        name: \"Home\",\n        key: \"homePage\"\n    },\n    aboutUs: {\n        route: \"about-us\",\n        name: \"aboutUs\",\n        key: \"aboutUs\",\n        i18nName: \"menu:aboutUs\"\n    },\n    services: {\n        route: \"hr-services\",\n        name: \"services\",\n        key: \"services\",\n        i18nName: \"menu:services\"\n    },\n    resources: {\n        route: \"resources\",\n        name: \"resources\",\n        key: \"resources\",\n        i18nName: \"Resources\"\n    },\n    events: {\n        route: \"events\",\n        name: \"events\",\n        key: \"events\",\n        i18nName: \"Events\"\n    },\n    payrollServices: {\n        route: \"payroll-service\",\n        name: \"payrollServices\",\n        key: \"payrollServices\",\n        i18nName: \"menu:payrollServices\"\n    },\n    consultingServices: {\n        route: \"consulting-services\",\n        name: \"consultingServices\",\n        key: \"consultingServices\",\n        i18nName: \"menu:consultingServices\"\n    },\n    technicalAssistance: {\n        route: \"technical-assistance\",\n        name: \"technicalAssistance\",\n        key: \"technicalAssistance\",\n        i18nName: \"menu:technicalAssistance\"\n    },\n    aiSourcing: {\n        route: \"pentabell-ai-sourcing-coordinators\",\n        name: \"aiSourcing\",\n        key: \"aiSourcing\",\n        i18nName: \"menu:aiSourcing\"\n    },\n    directHiring: {\n        route: \"direct-hiring-solutions\",\n        name: \"directHiring\",\n        key: \"directHiring\",\n        i18nName: \"menu:directHiring\"\n    },\n    opportunities: {\n        route: \"opportunities\",\n        name: \"Opportunities\",\n        key: \"opportunities\",\n        i18nName: \"menu:opportunities\"\n    },\n    jobCategory: {\n        route: \"job-category\",\n        name: \"jobCategory\",\n        key: \"jobCategory\",\n        i18nName: \"menu:jobCategory\"\n    },\n    /*  oilGas: {\r\n    route: \"oil-and-gas\",\r\n    name: \"oilGas\",\r\n    key: \"oilGas\",\r\n    i18nName: \"menu:oilGas\",\r\n  }, */ transportation: {\n        route: \"transport\",\n        name: \"transportation\",\n        key: \"transportation\",\n        i18nName: \"menu:transportation\"\n    },\n    itTelecom: {\n        route: \"it-telecom\",\n        name: \"itTelecom\",\n        key: \"itTelecom\",\n        i18nName: \"menu:itTelecom\"\n    },\n    insuranceBanking: {\n        route: \"banking-insurance\",\n        name: \"insuranceBanking\",\n        key: \"insuranceBanking\",\n        i18nName: \"menu:insuranceBanking\"\n    },\n    energies: {\n        route: \"energies\",\n        name: \"energies\",\n        key: \"energies\",\n        i18nName: \"menu:energies\"\n    },\n    others: {\n        route: \"other\",\n        name: \"others\",\n        key: \"others\",\n        i18nName: \"menu:others\"\n    },\n    pharmaceutical: {\n        route: \"pharmaceutical\",\n        name: \"pharmaceutical\",\n        key: \"pharmaceutical\",\n        i18nName: \"menu:pharma\"\n    },\n    blog: {\n        route: \"blog\",\n        name: \"Blog\",\n        key: \"blog\",\n        i18nName: \"menu:blog\"\n    },\n    guide: {\n        route: \"guides\",\n        name: \"guides\",\n        key: \"guides\",\n        i18nName: \"menu:guides\"\n    },\n    joinUs: {\n        route: \"join-us\",\n        name: \"joinUs\",\n        key: \"joinUs\",\n        i18nName: \"menu:joinUs\"\n    },\n    contact: {\n        route: \"contact\",\n        name: \"contact\",\n        key: \"contact\",\n        i18nName: \"menu:contact\"\n    },\n    category: {\n        route: \"category\",\n        name: \"category\",\n        key: \"category\",\n        i18nName: \"menu:category\"\n    },\n    apply: {\n        route: \"apply\",\n        name: \"apply\",\n        key: \"apply\"\n    },\n    egyptePage: {\n        route: \"guide-to-hiring-employees-in-egypt\",\n        name: \"egypte\",\n        key: \"egyptePage\"\n    },\n    libyaPage: {\n        route: \"guide-to-hiring-employees-in-libya\",\n        name: \"libya\",\n        key: \"libyaPage\"\n    },\n    tunisiaPage: {\n        route: \"hiring-employees-tunisia-guide\",\n        name: \"tunisia\",\n        key: \"tunisiaPage\"\n    },\n    ksaPage: {\n        route: \"international-hr-services-recruitment-agency-ksa\",\n        name: \"ksa\",\n        key: \"ksaPage\"\n    },\n    qatarPage: {\n        route: \"international-hr-services-recruitment-agency-qatar\",\n        name: \"qatar\",\n        key: \"qatarPage\"\n    },\n    iraqPage: {\n        route: \"international-hr-services-recruitment-agency-iraq\",\n        name: \"iraq\",\n        key: \"iraqPage\"\n    },\n    africaPage: {\n        route: \"international-recruitment-staffing-company-in-africa\",\n        name: \"africa\",\n        key: \"africaPage\"\n    },\n    europePage: {\n        route: \"international-recruitment-staffing-company-in-europe\",\n        name: \"europe\",\n        key: \"europePage\"\n    },\n    middleEastPage: {\n        route: \"international-recruitment-staffing-company-in-middle-east\",\n        name: \"middleEast\",\n        key: \"middleEastPage\"\n    },\n    francePage: {\n        route: \"recruitment-agency-france\",\n        name: \"france\",\n        key: \"francePage\"\n    },\n    dubaiPage: {\n        route: \"recruitment-staffing-agency-dubai\",\n        name: \"dubai\",\n        key: \"dubaiPage\"\n    },\n    algeriaPage: {\n        route: \"ultimate-guide-to-hiring-employees-in-algeria\",\n        name: \"algeria\",\n        key: \"algeriaPage\"\n    },\n    moroccoPage: {\n        route: \"ultimate-guide-to-hiring-employees-in-morocco\",\n        name: \"morocco\",\n        key: \"moroccoPage\"\n    },\n    privacyPolicy: {\n        route: \"privacy-policy\",\n        name: \"privacyPolicy\",\n        key: \"privacyPolicy\"\n    },\n    termsAndConditions: {\n        route: \"terms-and-conditions\",\n        name: \"termsAndConditions\",\n        key: \"termsAndConditions\"\n    },\n    document: {\n        route: \"document\",\n        name: \"document\",\n        key: \"document\"\n    },\n    pfeBookLink: {\n        route: \"pfe-book-2024-2025\",\n        name: \"pfeBookLink\",\n        key: \"pfeBookLink\"\n    },\n    jobLocation: {\n        route: \"job-location\",\n        name: \"jobLocation\",\n        key: \"jobLocation\",\n        i18nName: \"menu:jobLocation\"\n    }\n};\nconst authRoutes = {\n    login: {\n        route: \"login\",\n        name: \"login\",\n        key: \"login\",\n        i18nName: \"menu:login\"\n    },\n    register: {\n        route: \"register\",\n        name: \"register\",\n        key: \"register\",\n        i18nName: \"menu:register\"\n    },\n    forgetPassword: {\n        route: \"forgot-password\",\n        name: \"forgetPassword\",\n        key: \"forgetPassword\"\n    },\n    logout: {\n        route: \"logout\",\n        name: \"logout\",\n        key: \"logout\",\n        i18nName: \"sidebar:logout\"\n    },\n    resetPassword: {\n        route: \"reset-password/:token\",\n        name: \"Reset Password\",\n        key: \"resetPassword\"\n    },\n    resend: {\n        route: \"resend-activation\",\n        name: \"Resend Activation\",\n        key: \"resend\"\n    },\n    activation: {\n        route: \"activation/:token\",\n        name: \"Activation\",\n        key: \"activation\"\n    }\n};\nconst commonRoutes = {\n    settings: {\n        route: \"settings\",\n        name: \"Settings\",\n        key: \"settings\",\n        i18nName: \"sidebar:settings\"\n    },\n    myProfile: {\n        route: \"my-profile\",\n        name: \"profile\",\n        key: \"myProfile\",\n        i18nName: \"menu:profile\"\n    },\n    notifications: {\n        route: \"notifications\",\n        name: \"notifications\",\n        key: \"notifications\",\n        i18nName: \"menu:notifications\"\n    }\n};\nconst candidateRoutes = {\n    resumes: {\n        route: \"my-resumes\",\n        name: \"my resumes\",\n        key: \"Resumes\",\n        i18nName: \"menu:myResumes\"\n    },\n    myApplications: {\n        route: \"my-applications\",\n        name: \"My Applications\",\n        key: \"myApplications\",\n        i18nName: \"menu:myApplications\"\n    },\n    favoris: {\n        route: \"favoris\",\n        name: \"Favoris\",\n        key: \"favoris\",\n        i18nName: \"menu:favoris\"\n    },\n    home: {\n        route: \"home\",\n        name: \"home\",\n        key: \"home\",\n        i18nName: \"menu:home\"\n    },\n    ...Object.assign({}, commonRoutes)\n};\nconst editorRoutes = {\n    home: {\n        route: \"home\",\n        name: \"Home\",\n        key: \"homePage\"\n    },\n    blogs: {\n        route: \"blogs\",\n        name: \"Blogs\",\n        key: \"blogs\",\n        i18nName: \"menu:blog\"\n    },\n    sliders: {\n        route: \"sliders\",\n        name: \"sliders\",\n        key: \"sliders\",\n        i18nName: \"menu:sliders\"\n    },\n    downloads: {\n        route: \"downloads\",\n        name: \"downloads\",\n        key: \"downloads\",\n        i18nName: \"menu:downloads\"\n    },\n    add: {\n        route: \"add\",\n        name: \"create\",\n        key: \"add\"\n    },\n    edit: {\n        route: \"edit\",\n        name: \"edit\",\n        key: \"edit\"\n    },\n    updateslider: {\n        route: \"updateslider\",\n        name: \"updateslider\",\n        key: \"updateslider\"\n    },\n    comments: {\n        route: \"comments\",\n        name: \"comments\",\n        key: \"comments\",\n        i18nName: \"menu:comments\"\n    },\n    archived: {\n        route: \"archived\",\n        name: \"archived\",\n        key: \"archived\"\n    },\n    candidate: {\n        route: \"candidate\",\n        name: \"candidate\",\n        key: \"candidate\"\n    },\n    categories: {\n        route: \"categories\",\n        name: \"categories\",\n        key: \"categories\"\n    },\n    detail: {\n        route: \"detail\",\n        name: \"detail\",\n        key: \"detail\"\n    },\n    newsletters: {\n        route: \"newsletters\",\n        name: \"newsletters\",\n        key: \"newsletters\"\n    },\n    opportunities: {\n        route: \"opportunities\",\n        name: \"Opportunities\",\n        key: \"opportunities\",\n        i18nName: \"menu:opportunities\"\n    },\n    categoriesguide: {\n        route: \"CategoriesGuide\",\n        name: \"CategoriesGuide\",\n        key: \"CategoriesGuide\",\n        i18nName: \"guides:categoriesGuide\"\n    },\n    opportunity: {\n        route: \"opportunity\",\n        name: \"opportunity\",\n        key: \"opportunity\"\n    },\n    editSEOTags: {\n        route: \"edit-seo-tags\",\n        name: \"edit SEO Tags\",\n        key: \"editSEOTags\",\n        i18nName: \"menu:editSEOTags\"\n    },\n    contacts: {\n        route: \"contacts\",\n        name: \"contacts\",\n        key: \"contacts\",\n        i18nName: \"menu:contact\"\n    },\n    seoSettings: {\n        route: \"seo-settings\",\n        name: \"seo-settings\",\n        key: \"seo-settings\",\n        i18nName: \"menu:seoSettings\"\n    },\n    guides: {\n        route: \"guides\",\n        name: \"guides\",\n        key: \"guides\"\n    },\n    sliders: {\n        route: \"sliders\",\n        name: \"sliders\",\n        key: \"sliders\"\n    },\n    events: {\n        route: \"events\",\n        name: \"Events\",\n        key: \"events\",\n        i18nName: \"menu:events\"\n    },\n    ...Object.assign({}, commonRoutes)\n};\nconst adminRoutes = {\n    statistics: {\n        route: \"statistics\",\n        name: \"statistics\",\n        key: \"statistics\",\n        i18nName: \"menu:statistics\"\n    },\n    applications: {\n        route: \"applications\",\n        name: \"applications\",\n        key: \"applications\",\n        i18nName: \"application:candidatures\"\n    },\n    downloadReport: {\n        route: \"downloadReport\",\n        name: \"downloadReport\",\n        key: \"downloadReport\",\n        i18nName: \"Downloads Report\"\n    },\n    users: {\n        route: \"users\",\n        name: \"users\",\n        key: \"users\",\n        i18nName: \"menu:users\"\n    },\n    user: {\n        route: \"user\",\n        name: \"user\",\n        key: \"user\"\n    },\n    // ...Object.assign({}, commonRoutes),\n    ...Object.assign({}, editorRoutes)\n};\nconst editorPermissionsRoutes = [\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.myProfile.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.home.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.guides.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.guides.route}/${editorRoutes.downloads.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.guides.route}/${editorRoutes.add.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.blogs.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.guides.route}/${editorRoutes.categories.route},${editorRoutes.add.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.guides.route}/${editorRoutes.categories.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.blogs.route}/${editorRoutes.add.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.blogs.route}/${editorRoutes.archived.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.blogs.route}/${editorRoutes.comments.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.blogs.route}/${editorRoutes.edit.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.comments.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.opportunities.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.opportunities.route}/${editorRoutes.add.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.opportunities.route}/${editorRoutes.edit.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.opportunities.route}/${editorRoutes.editSEOTags.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.categories.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.categories.route}/${editorRoutes.add.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.categories.route}/${editorRoutes.edit.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.notifications.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.settings.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.contacts.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.contacts.route}/${editorRoutes.edit.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.newsletters.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.seoSettings.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.statistics.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.events.route}`,\n    `/${authRoutes.logout.route}`\n];\nconst adminPermissionsRoutes = [\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.guides.route}/${editorRoutes.downloads.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.downloadReport.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}/${adminRoutes.edit.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}/${adminRoutes.detail.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}/${adminRoutes.opportunity.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.guides.route}/${adminRoutes.downloads.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.users.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.guides.route}/${adminRoutes.categories.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.guides.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.guides.route}/${adminRoutes.add.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.sliders.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.guides.route}/${adminRoutes.categories.route},${adminRoutes.add.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.sliders.route}/${adminRoutes.updateslider.route}`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.users.route}/${adminRoutes.detail.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.users.route}/${adminRoutes.edit.route}/:id`,\n    `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.users.route}/${adminRoutes.add.route}`,\n    ...editorPermissionsRoutes\n];\nconst candidatePermissionsRoutes = [\n    `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.favoris.route}`,\n    `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.home.route}`,\n    `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.myApplications.route}`,\n    `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.myProfile.route}`,\n    `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.resumes.route}`,\n    `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.notifications.route}`,\n    `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.settings.route}`,\n    `/${authRoutes.logout.route}`\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/helpers/routesList.js\n");

/***/ }),

/***/ "(middleware)/./src/middleware.js":
/*!***************************!*\
  !*** ./src/middleware.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-i18n-router */ \"(middleware)/./node_modules/next-i18n-router/dist/index.js\");\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_i18n_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! jose */ \"(middleware)/./node_modules/jose/dist/browser/jwt/verify.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cookie */ \"(middleware)/./node_modules/cookie/index.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../i18nConfig */ \"(middleware)/./i18nConfig.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_i18nConfig__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/constants */ \"(middleware)/./src/utils/constants.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/functions */ \"(middleware)/./src/utils/functions.js\");\n/* harmony import */ var _config_translations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./config/translations */ \"(middleware)/./src/config/translations.js\");\n/* harmony import */ var _config_allowedParams__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./config/allowedParams */ \"(middleware)/./src/config/allowedParams.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./helpers/routesList */ \"(middleware)/./src/helpers/routesList.js\");\n\n\n\n\n\n\n\n\n\n\nconst verifyToken = async (token)=>{\n    try {\n        const secret = new TextEncoder().encode(process.env.NEXT_JWT_SECRET);\n        const { payload } = await (0,jose__WEBPACK_IMPORTED_MODULE_9__.jwtVerify)(token, secret);\n        return payload;\n    } catch (error) {\n        return null;\n    }\n};\nasync function middleware(req) {\n    const url = req.nextUrl.clone();\n    const { defaultLocale } = (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default());\n    const { pathname } = req.nextUrl;\n    // authentication and authorisation\n    const cookies = cookie__WEBPACK_IMPORTED_MODULE_2__.parse(req.headers.get(\"cookie\") || \"\");\n    const { accessToken, refreshToken } = cookies;\n    if (!(accessToken && refreshToken) && (pathname.includes(`/dashboard`) || pathname.includes(`/backoffice`))) {\n        url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    const user = await verifyToken(refreshToken);\n    if (pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/`) {\n        return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n    }\n    if (user) {\n        const menuList = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.getRoutesListByRole)(user);\n        const checkRole = !menuList?.some((item)=>pathname.includes(item)) && (pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}`) || pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}`));\n        if (checkRole || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`) {\n            if (user.roles?.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN)) url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.home.route}/`;\n            if (user.roles?.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.EDITOR)) url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.blogs.route}/`;\n            if (user?.roles?.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.CANDIDATE)) url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.candidateRoutes.myApplications.route}`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n    }\n    // remove any unallowed params\n    for (const param of url.searchParams.keys()){\n        if (!_config_allowedParams__WEBPACK_IMPORTED_MODULE_7__.allowedParams.has(param)) {\n            url.searchParams.delete(param);\n        }\n    }\n    if (url.searchParams.toString() !== req.nextUrl.searchParams.toString()) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // redirection paths\n    const frPath = _config_translations__WEBPACK_IMPORTED_MODULE_6__[\"default\"][req.nextUrl.pathname];\n    if (frPath) return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(frPath, req.url));\n    // translate links\n    if (!pathname.startsWith(\"/fr\") && !pathname.startsWith(`/${defaultLocale}`)) {\n        url.pathname = `/en${pathname}`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.rewrite(url);\n    }\n    return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n}\nconst config = {\n    matcher: \"/((?!api|static|.*\\\\..*|_next).*)\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.js\n");

/***/ }),

/***/ "(middleware)/./src/utils/constants.js":
/*!********************************!*\
  !*** ./src/utils/constants.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContractType: () => (/* binding */ ContractType),\n/* harmony export */   Countries: () => (/* binding */ Countries),\n/* harmony export */   Frequence: () => (/* binding */ Frequence),\n/* harmony export */   Gender: () => (/* binding */ Gender),\n/* harmony export */   Industry: () => (/* binding */ Industry),\n/* harmony export */   IndustryCandidat: () => (/* binding */ IndustryCandidat),\n/* harmony export */   LabelContactFields: () => (/* binding */ LabelContactFields),\n/* harmony export */   Nationalities: () => (/* binding */ Nationalities),\n/* harmony export */   OpportunityType: () => (/* binding */ OpportunityType),\n/* harmony export */   RobotsMeta: () => (/* binding */ RobotsMeta),\n/* harmony export */   Role: () => (/* binding */ Role),\n/* harmony export */   Roles: () => (/* binding */ Roles),\n/* harmony export */   Status: () => (/* binding */ Status),\n/* harmony export */   TypeContactLabels: () => (/* binding */ TypeContactLabels),\n/* harmony export */   TypeContacts: () => (/* binding */ TypeContacts),\n/* harmony export */   Visibility: () => (/* binding */ Visibility),\n/* harmony export */   cible: () => (/* binding */ cible),\n/* harmony export */   contactData: () => (/* binding */ contactData),\n/* harmony export */   coporateProfileTestimonials: () => (/* binding */ coporateProfileTestimonials),\n/* harmony export */   defaultFonts: () => (/* binding */ defaultFonts),\n/* harmony export */   feedbacks: () => (/* binding */ feedbacks),\n/* harmony export */   skills: () => (/* binding */ skills),\n/* harmony export */   sortedFontOptions: () => (/* binding */ sortedFontOptions)\n/* harmony export */ });\nconst Countries = [\n    \"Afghanistan\",\n    \"\\xc5land Islands\",\n    \"Albania\",\n    \"Algeria\",\n    \"American Samoa\",\n    \"AndorrA\",\n    \"Angola\",\n    \"Anguilla\",\n    \"Antarctica\",\n    \"Antigua and Barbuda\",\n    \"Argentina\",\n    \"Armenia\",\n    \"Aruba\",\n    \"Australia\",\n    \"Austria\",\n    \"Azerbaijan\",\n    \"Bahamas\",\n    \"Bahrain\",\n    \"Bangladesh\",\n    \"Barbados\",\n    \"Belarus\",\n    \"Belgium\",\n    \"Belize\",\n    \"Benin\",\n    \"Bermuda\",\n    \"Bhutan\",\n    \"Bolivia\",\n    \"Bosnia and Herzegovina\",\n    \"Botswana\",\n    \"Bouvet Island\",\n    \"Brazil\",\n    \"British Indian Ocean Territory\",\n    \"Brunei Darussalam\",\n    \"Bulgaria\",\n    \"Burkina Faso\",\n    \"Burundi\",\n    \"Cambodia\",\n    \"Cameroon\",\n    \"Canada\",\n    \"Cape Verde\",\n    \"Cayman Islands\",\n    \"Central African Republic\",\n    \"Chad\",\n    \"Chile\",\n    \"China\",\n    \"Christmas Island\",\n    \"Cocos (Keeling) Islands\",\n    \"Colombia\",\n    \"Comoros\",\n    \"Congo\",\n    \"Cook Islands\",\n    \"Costa Rica\",\n    \"Cote D'Ivoire\",\n    \"Croatia\",\n    \"Cuba\",\n    \"Cyprus\",\n    \"Czech Republic\",\n    \"Denmark\",\n    \"Democratic Republic of the Congo\",\n    \"Djibouti\",\n    \"Dominica\",\n    \"Dominican Republic\",\n    \"Ecuador\",\n    \"Egypt\",\n    \"El Salvador\",\n    \"Equatorial Guinea\",\n    \"Eritrea\",\n    \"Estonia\",\n    \"Ethiopia\",\n    \"Falkland Islands (Malvinas)\",\n    \"Faroe Islands\",\n    \"Fiji\",\n    \"Finland\",\n    \"France\",\n    \"French Guiana\",\n    \"French Polynesia\",\n    \"French Southern Territories\",\n    \"Gabon\",\n    \"Gambia\",\n    \"Georgia\",\n    \"Germany\",\n    \"Ghana\",\n    \"Gibraltar\",\n    \"Greece\",\n    \"Greenland\",\n    \"Grenada\",\n    \"Guadeloupe\",\n    \"Guam\",\n    \"Guatemala\",\n    \"Guernsey\",\n    \"Guinea\",\n    \"Guinea-Bissau\",\n    \"Guyana\",\n    \"Haiti\",\n    \"Heard Island and Mcdonald Islands\",\n    \"Holy See (Vatican City State)\",\n    \"Honduras\",\n    \"Hong Kong\",\n    \"Hungary\",\n    \"Iceland\",\n    \"India\",\n    \"Indonesia\",\n    \"Iran, Islamic Republic Of\",\n    \"Iraq\",\n    \"Ireland\",\n    \"Isle of Man\",\n    \"Italy\",\n    \"Jamaica\",\n    \"Japan\",\n    \"Jersey\",\n    \"Jordan\",\n    \"Kazakhstan\",\n    \"Kenya\",\n    \"Kiribati\",\n    \"Korea, Democratic People'S Republic of\",\n    \"Korea, Republic of\",\n    \"Kuwait\",\n    \"Kyrgyzstan\",\n    \"Lao People'S Democratic Republic\",\n    \"Latvia\",\n    \"Lebanon\",\n    \"Lesotho\",\n    \"Liberia\",\n    \"Libya\",\n    \"Liechtenstein\",\n    \"Lithuania\",\n    \"Luxembourg\",\n    \"Macao\",\n    \"Macedonia, The Former Yugoslav Republic of\",\n    \"Madagascar\",\n    \"Malawi\",\n    \"Malaysia\",\n    \"Maldives\",\n    \"Mali\",\n    \"Malta\",\n    \"Marshall Islands\",\n    \"Martinique\",\n    \"Mauritania\",\n    \"Mauritius\",\n    \"Mayotte\",\n    \"Mexico\",\n    \"Micronesia, Federated States of\",\n    \"Moldova, Republic of\",\n    \"Monaco\",\n    \"Mongolia\",\n    \"Montserrat\",\n    \"Morocco\",\n    \"Mozambique\",\n    \"Myanmar\",\n    \"Namibia\",\n    \"Nauru\",\n    \"Nepal\",\n    \"Netherlands\",\n    \"Netherlands Antilles\",\n    \"New Caledonia\",\n    \"New Zealand\",\n    \"Nicaragua\",\n    \"Niger\",\n    \"Nigeria\",\n    \"Niue\",\n    \"Norfolk Island\",\n    \"Northern Mariana Islands\",\n    \"Norway\",\n    \"Oman\",\n    \"Pakistan\",\n    \"Palau\",\n    \"Palestine\",\n    \"Panama\",\n    \"Papua New Guinea\",\n    \"Paraguay\",\n    \"Peru\",\n    \"Philippines\",\n    \"Pitcairn\",\n    \"Poland\",\n    \"Portugal\",\n    \"Puerto Rico\",\n    \"Qatar\",\n    \"Reunion\",\n    \"Romania\",\n    \"Russian Federation\",\n    \"RWANDA\",\n    \"Saint Helena\",\n    \"Saint Kitts and Nevis\",\n    \"Saint Lucia\",\n    \"Saint Pierre and Miquelon\",\n    \"Saint Vincent and the Grenadines\",\n    \"Samoa\",\n    \"San Marino\",\n    \"Sao Tome and Principe\",\n    \"Saudi Arabia\",\n    \"Senegal\",\n    \"Serbia and Montenegro\",\n    \"Seychelles\",\n    \"Sierra Leone\",\n    \"Singapore\",\n    \"Slovakia\",\n    \"Slovenia\",\n    \"Solomon Islands\",\n    \"Somalia\",\n    \"South Africa\",\n    \"South Georgia and the South Sandwich Islands\",\n    \"Spain\",\n    \"Sri Lanka\",\n    \"Sudan\",\n    \"Suriname\",\n    \"Svalbard and Jan Mayen\",\n    \"Swaziland\",\n    \"Sweden\",\n    \"Switzerland\",\n    \"Syrian Arab Republic\",\n    \"Taiwan, Province of China\",\n    \"Tajikistan\",\n    \"Tanzania, United Republic of\",\n    \"Thailand\",\n    \"Timor-Leste\",\n    \"Togo\",\n    \"Tokelau\",\n    \"Tonga\",\n    \"Trinidad and Tobago\",\n    \"Tunisia\",\n    \"Turkey\",\n    \"Turkmenistan\",\n    \"Turks and Caicos Islands\",\n    \"Tuvalu\",\n    \"Uganda\",\n    \"Ukraine\",\n    \"United Arab Emirates\",\n    \"United Kingdom\",\n    \"United States\",\n    \"United States Minor Outlying Islands\",\n    \"Uruguay\",\n    \"Uzbekistan\",\n    \"Vanuatu\",\n    \"Venezuela\",\n    \"Viet Nam\",\n    \"Virgin Islands, British\",\n    \"Virgin Islands, U.S.\",\n    \"Wallis and Futuna\",\n    \"Western Sahara\",\n    \"Yemen\",\n    \"Zambia\",\n    \"Zimbabwe\"\n];\nconst ContractType = [\n    \"CDD\",\n    \"CDIC\",\n    \"Freelance\"\n];\nconst Nationalities = [\n    \"American\",\n    \"British\",\n    \"Canadian\",\n    \"French\",\n    \"German\",\n    \"Italian\",\n    \"Japanese\",\n    \"Chinese\",\n    \"Indian\",\n    \"Russian\",\n    \"Australian\",\n    \"Brazilian\",\n    \"Mexican\",\n    \"Spanish\",\n    \"South Korean\",\n    \"Dutch\",\n    \"Swedish\",\n    \"Tunisian\",\n    \"Norwegian\",\n    \"Swiss\",\n    \"Belgian\"\n];\nconst Gender = [\n    \"Male\",\n    \"Female\",\n    \"All\"\n];\nconst Frequence = [\n    \"monthly\",\n    \"weekly\"\n];\nconst Visibility = [\n    \"Public\",\n    \"Private\",\n    \"Draft\"\n];\n// export const OpportunityTypeLabel = {\n//   CONFIDENTIAL: \"Confidential\",\n//   DIRECT_HIRE: \"Direct Hire\",\n//   TENDER: \"Tender\",\n//   CAPABILITY: \"Capability\",\n//   PAYROLL: \"Payroll\",\n//   INTERNE: \"Intern\",\n//   RECRUTEMENT: \"Recrutement\",\n//   CONSULTING: \"Consulting\",\n//   PORTAGE: \"Portage\",\n//   NOT_SPECIFIED: \"Not specified\",\n// };\nconst OpportunityType = [\n    \"Confidential\",\n    \"Direct Hire\",\n    \"Tender\",\n    \"Capability\",\n    \"Payroll\",\n    \"In House\",\n    \"Recrutement\",\n    \"Consulting\",\n    \"Portage\",\n    \"Not specified\"\n];\n// export const ContractType = [\n// \"Permanent contract\",\n// \"Temporary\",\n// \"Freelance\",\n// \"Work study\",\n// \"Internship\",\n// \"Part-time\",\n// \"Graduate program\",\n// \"Volunteer work\",\n// \"Other\"\n// ]\nconst RobotsMeta = [\n    \"index\",\n    \"noindex\"\n];\nconst Roles = [\n    \"Candidate\",\n    \"Editor\",\n    \"Admin\"\n];\nconst Role = {\n    CANDIDATE: \"Candidate\",\n    EDITOR: \"Editor\",\n    ADMIN: \"Admin\"\n};\nconst Status = [\n    \"Pending\",\n    \"Accepted\",\n    \"Rejected\"\n];\nconst Industry = [\n    \"It & Telecom\",\n    \"Transport\",\n    \"Energies\",\n    \"Banking\",\n    \"Pharmaceutical\",\n    \"Other\"\n];\nconst IndustryCandidat = [\n    \"It & Telecom\",\n    \"Transport\",\n    \"Oil & gas\",\n    \"Energy\",\n    \"Banking\",\n    \"Pharmaceutical\"\n];\nconst cible = [\n    \"client\",\n    \"consultant\"\n];\nconst skills = [\n    // Compétences pour IT & TELECOM\n    {\n        name: \"D\\xe9veloppement logiciel\",\n        label: \"D\\xe9veloppement logiciel\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"Administration syst\\xe8me\",\n        label: \"Administration syst\\xe8me\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"D\\xe9veloppement d'applications mobiles\",\n        label: \"D\\xe9veloppement d'applications mobiles\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"Gestion de r\\xe9seau\",\n        label: \"Gestion de r\\xe9seau\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"Gestion de projet\",\n        label: \"Gestion de projet\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"Analyse de donn\\xe9es\",\n        label: \"Analyse de donn\\xe9es\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"Cybers\\xe9curit\\xe9\",\n        label: \"Cybers\\xe9curit\\xe9\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"Cloud computing\",\n        label: \"Cloud computing\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"abcdabcd\",\n        label: \"abcdabcd\",\n        industry: \"IT & TELECOM\"\n    },\n    // Compétences pour TRANSPORT\n    {\n        value: \"Transport routier\",\n        label: \"Transport routier\",\n        industry: \"TRANSPORT\"\n    },\n    {\n        value: \"Logistique\",\n        label: \"Logistique\",\n        industry: \"TRANSPORT\"\n    },\n    {\n        value: \"Gestion de flotte\",\n        label: \"Gestion de flotte\",\n        industry: \"TRANSPORT\"\n    },\n    {\n        value: \"Planification des itin\\xe9raires\",\n        label: \"Planification des itin\\xe9raires\",\n        industry: \"TRANSPORT\"\n    },\n    {\n        value: \"Logistique internationale\",\n        label: \"Logistique internationale\",\n        industry: \"TRANSPORT\"\n    },\n    // Compétences pour OIL & GAS\n    {\n        value: \"Forage p\\xe9trolier\",\n        label: \"Forage p\\xe9trolier\",\n        industry: \"OIL & GAS\"\n    },\n    {\n        value: \"Raffinage p\\xe9trolier\",\n        label: \"Raffinage p\\xe9trolier\",\n        industry: \"OIL & GAS\"\n    },\n    {\n        value: \"Exploration g\\xe9ologique\",\n        label: \"Exploration g\\xe9ologique\",\n        industry: \"OIL & GAS\"\n    },\n    {\n        value: \"Ing\\xe9nierie des r\\xe9servoirs\",\n        label: \"Ing\\xe9nierie des r\\xe9servoirs\",\n        industry: \"OIL & GAS\"\n    },\n    {\n        value: \"Gestion de la production\",\n        label: \"Gestion de la production\",\n        industry: \"OIL & GAS\"\n    },\n    // Compétences pour BANKING\n    {\n        value: \"Analyse financi\\xe8re\",\n        label: \"Analyse financi\\xe8re\",\n        industry: \"BANKING\"\n    },\n    {\n        value: \"Gestion des risques financiers\",\n        label: \"Gestion des risques financiers\",\n        industry: \"BANKING\"\n    },\n    {\n        value: \"Gestion des portefeuilles\",\n        label: \"Gestion des portefeuilles\",\n        industry: \"BANKING\"\n    },\n    {\n        value: \"Conformit\\xe9 r\\xe9glementaire\",\n        label: \"Conformit\\xe9 r\\xe9glementaire\",\n        industry: \"BANKING\"\n    },\n    {\n        value: \"Services bancaires en ligne\",\n        label: \"Services bancaires en ligne\",\n        industry: \"BANKING\"\n    }\n];\nconst defaultFonts = [\n    \"Arial\",\n    \"Comic Sans MS\",\n    \"Courier New\",\n    \"Impact\",\n    \"Georgia\",\n    \"Tahoma\",\n    \"Trebuchet MS\",\n    \"Verdana\"\n];\nconst sortedFontOptions = [\n    \"Logical\",\n    \"Salesforce Sans\",\n    \"Garamond\",\n    \"Sans-Serif\",\n    \"Serif\",\n    \"Times New Roman\",\n    \"Helvetica\",\n    ...defaultFonts\n].sort();\nconst TypeContacts = [\n    \"countryContact\",\n    \"joinUs\",\n    \"directHiringService\",\n    \"aiSourcingService\",\n    \"technicalAssistanceService\",\n    \"consultingService\",\n    \"payrollService\",\n    \"mainService\",\n    \"getInTouchContact\",\n    \"getInTouch\"\n];\nconst TypeContactLabels = {\n    countryContact: \"Country Contact\",\n    joinUs: \"Join Us\",\n    directHiringService: \"Direct Hiring Service\",\n    aiSourcingService: \"AI Sourcing Service\",\n    technicalAssistanceService: \"Technical Assistance Service\",\n    consultingService: \"Consulting Service\",\n    payrollService: \"Payroll Service\",\n    mainService: \"Main Service\",\n    getInTouchContact: \"Get in Touch Contact\",\n    getInTouch: \"Get in Touch\"\n};\nconst LabelContactFields = {\n    firstName: \"First Name\",\n    lastName: \"Last Name\",\n    fullName: \"Full Name\",\n    email: \"Email\",\n    phone: \"Phone\",\n    message: \"Message\",\n    type: \"Type\",\n    subject: \"Subject\",\n    youAre: \"You Are\",\n    companyName: \"Company Name\",\n    enquirySelect: \"Enquiry Select\",\n    jobTitle: \"Job Title\",\n    mission: \"Mission\",\n    resume: \"Resume\",\n    howToHelp: \"How To Help\",\n    createdAt: \"Created At\",\n    countryName: \"Country Name\",\n    field: \"Field\"\n};\nconst contactData = (t, locale)=>[\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:france\"),\n            logo: {\n                \"@type\": \"ImageObject\",\n                url: \"https://www.pentabell.com/logos/pentabell-logo.png\"\n            },\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Atlantic Building Montparnasse, Entrance No. 7, 3rd floor\",\n                addressLocality: \"Paris\",\n                postalCode: \"75015\",\n                addressCountry: \"FR\"\n            },\n            telephone: \"+33 1 73 07 42 54\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/recruitment-agency-france/\" : `https://www.pentabell.com/${locale}/recruitment-agency-france/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:switzerland\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Grand-Rue 92\",\n                addressLocality: \"Montreux\",\n                postalCode: \"1820\",\n                addressCountry: \"CH\"\n            },\n            telephone: \"+33 1 73 07 42 54\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/contact/\" : `https://www.pentabell.com/${locale}/contact/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:ksa\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"3530 Umar Ibn Abdul Aziz Br Rd, Az Zahra\",\n                addressLocality: \"Riyadh\",\n                postalCode: \"12815\",\n                addressCountry: \"SA\"\n            },\n            telephone: \"+33 1 73 07 42 54\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/international-hr-services-recruitment-agency-ksa/\" : `https://www.pentabell.com/${locale}/international-hr-services-recruitment-agency-ksa/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:uae\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"HDS Business Center Office 306 JLT\",\n                addressLocality: \"Dubai\",\n                addressCountry: \"AE\"\n            },\n            telephone: \"+971 4 4876 0672\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/recruitment-staffing-agency-dubai/\" : `https://www.pentabell.com/${locale}/recruitment-staffing-agency-dubai/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:qatar\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Level 14, Commercial Bank Plaza, West Bay\",\n                addressLocality: \"Doha\",\n                postalCode: \"27111\",\n                addressCountry: \"QA\"\n            },\n            telephone: \"+974 4452 7957\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/international-hr-services-recruitment-agency-qatar/\" : `https://www.pentabell.com/${locale}/international-hr-services-recruitment-agency-qatar/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:tunisia\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Imm. MADIBA, Rue Khawarizmi\",\n                addressLocality: \"La Goulette\",\n                postalCode: \"2015\",\n                addressCountry: \"TN\"\n            },\n            telephone: [\n                \"+216 31 385 510\"\n            ],\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/hiring-employees-tunisia-guide/\" : `https://www.pentabell.com/${locale}/hiring-employees-tunisia-guide/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:hydra\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Route les oliviers les cretes n\\xb014\",\n                addressLocality: \"Hydra, Alger\",\n                postalCode: \"16035\",\n                addressCountry: \"DZ\"\n            },\n            telephone: [\n                \"+213 23 48 59 10\",\n                \"+213 23 48 51 44\"\n            ],\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-algeria/\" : `https://www.pentabell.com/${locale}/ultimate-guide-to-hiring-employees-in-algeria/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:hassiMassoud\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Eurojapan Residence Route Nationale N\\xb03 BP 842\",\n                addressLocality: \"Hassi Messaoud\",\n                addressCountry: \"DZ\"\n            },\n            telephone: \"+33 1 73 07 42 54\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-algeria/\" : `https://www.pentabell.com/${locale}/ultimate-guide-to-hiring-employees-in-algeria/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:morocco\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Zenith 1, Sidi maarouf, lot CIVIM\",\n                addressLocality: \"Casablanca\",\n                postalCode: \"20270\",\n                addressCountry: \"MA\"\n            },\n            telephone: \"+212 5 22 78 63 66\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-morocco/\" : `https://www.pentabell.com/${locale}/ultimate-guide-to-hiring-employees-in-morocco/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:egypte\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"8 El Birgas street, Garden City\",\n                addressLocality: \"Cairo\",\n                addressCountry: \"EG\"\n            },\n            telephone: \"+33 1 73 07 42 54\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/guide-to-hiring-employees-in-egypt/\" : `https://www.pentabell.com/${locale}/guide-to-hiring-employees-in-egypt/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:lybia\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Al Serraj, AlMawashi Street P.O.Box 3000\",\n                addressLocality: \"Tripoli\",\n                addressCountry: \"LY\"\n            },\n            telephone: \"+33 1 73 07 42 54\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/guide-to-hiring-employees-in-libya/\" : `https://www.pentabell.com/${locale}/guide-to-hiring-employees-in-libya/`\n        }\n    ];\nconst feedbacks = [\n    {\n        id: 1,\n        description: \"I truly appreciate the opportunities provided and the unwavering trust in my abilities. A big thank you to the entire team for their continued support, especially Ridha R.\",\n        rating: 4,\n        author: \"Nabil.J\",\n        quality: \"IT Coordinator\"\n    },\n    {\n        id: 2,\n        description: \"Pentabell Tunisie team has been helpful and responsive whenever assistance is needed, particularly Mr. Ridha and Mr. Matthew. I appreciate the team’s efforts and look forward to seeing continued growth in the future.\",\n        rating: 4,\n        author: \"Maher.M\",\n        quality: \"A2P Service Delivery Engineer\"\n    },\n    {\n        id: 3,\n        description: \"We are very satisfied with the service provided by Pentabell. The professionalism and efficiency of the Tunisia office staff are commendable, especially the work of Mr. Matthew, Mr. Ridha, and Mr. Eric, who have been our direct contacts.\",\n        rating: 5,\n        author: \"Gabor.M\",\n        quality: \"Company\"\n    },\n    {\n        id: 4,\n        description: \"I truly appreciate the opportunities provided and the unwavering trust in my abilities. A big thank you to the entire team for their continued support, especially Ridha R.\",\n        rating: 4,\n        author: \"Nabil.J\",\n        quality: \"IT Coordinator\"\n    },\n    {\n        id: 5,\n        description: \"Pentabell Tunisie team has been helpful and responsive whenever assistance is needed, particularly Mr. Ridha and Mr. Matthew. I appreciate the team’s efforts and look forward to seeing continued growth in the future.\",\n        rating: 4,\n        author: \"Maher.M\",\n        quality: \"A2P Service Delivery Engineer\"\n    },\n    {\n        id: 6,\n        description: \"We are very satisfied with the service provided by Pentabell. The professionalism and efficiency of the Tunisia office staff are commendable, especially the work of Mr. Matthew, Mr. Ridha, and Mr. Eric, who have been our direct contacts.\",\n        rating: 5,\n        author: \"Gabor.M\",\n        quality: \"Company\"\n    }\n];\nconst coporateProfileTestimonials = [\n    {\n        id: 1,\n        description: \"I am pleased  with PENTABELL the exceptional services they have delivered during our recent collaborations on various projects within the Kingdom of Saudi Arabia (KSA). Throughout our partnership, PENTABELL has consistently demonstrated professionalism, expertise, and a strong commitment to delivering high-quality results.\",\n        author: \"NOKIA KSA\"\n    },\n    {\n        id: 2,\n        description: \"We are very satisfied with the service provided by Pentabell. The professionalism and efficiency of the Tunisia office staff are commendable, especially the work of Mr. Matthew, Mr. Ridha, and Mr. Eric, who have been our direct contacts.\",\n        author: \"Gabor.M, Company\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/utils/constants.js\n");

/***/ }),

/***/ "(middleware)/./src/utils/functions.js":
/*!********************************!*\
  !*** ./src/utils/functions.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirstLetter: () => (/* binding */ capitalizeFirstLetter),\n/* harmony export */   createList: () => (/* binding */ createList),\n/* harmony export */   findCountryFlag: () => (/* binding */ findCountryFlag),\n/* harmony export */   findCountryLabel: () => (/* binding */ findCountryLabel),\n/* harmony export */   findIndustryByLargeIcon: () => (/* binding */ findIndustryByLargeIcon),\n/* harmony export */   findIndustryClassname: () => (/* binding */ findIndustryClassname),\n/* harmony export */   findIndustryColoredIcon: () => (/* binding */ findIndustryColoredIcon),\n/* harmony export */   findIndustryIcon: () => (/* binding */ findIndustryIcon),\n/* harmony export */   findIndustryLabel: () => (/* binding */ findIndustryLabel),\n/* harmony export */   findIndustryLink: () => (/* binding */ findIndustryLink),\n/* harmony export */   findIndustryLogoSvg: () => (/* binding */ findIndustryLogoSvg),\n/* harmony export */   findnotificationColoredIcon: () => (/* binding */ findnotificationColoredIcon),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateArticle: () => (/* binding */ formatDateArticle),\n/* harmony export */   formatDatedownload: () => (/* binding */ formatDatedownload),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   formatResumeName: () => (/* binding */ formatResumeName),\n/* harmony export */   generateLocalizedSlug: () => (/* binding */ generateLocalizedSlug),\n/* harmony export */   getCountryEventImage: () => (/* binding */ getCountryEventImage),\n/* harmony export */   getCountryImage: () => (/* binding */ getCountryImage),\n/* harmony export */   getExtension: () => (/* binding */ getExtension),\n/* harmony export */   getMenuListByRole: () => (/* binding */ getMenuListByRole),\n/* harmony export */   getRoutesListByRole: () => (/* binding */ getRoutesListByRole),\n/* harmony export */   getSlugByIndustry: () => (/* binding */ getSlugByIndustry),\n/* harmony export */   highlightMatchingWords: () => (/* binding */ highlightMatchingWords),\n/* harmony export */   industryExists: () => (/* binding */ industryExists),\n/* harmony export */   isExpired: () => (/* binding */ isExpired),\n/* harmony export */   processContent: () => (/* binding */ processContent),\n/* harmony export */   splitFirstWord: () => (/* binding */ splitFirstWord),\n/* harmony export */   splitLastWord: () => (/* binding */ splitLastWord),\n/* harmony export */   stringAvatar: () => (/* binding */ stringAvatar),\n/* harmony export */   stringToColor: () => (/* binding */ stringToColor),\n/* harmony export */   truncateByCharacter: () => (/* binding */ truncateByCharacter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(middleware)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var _config_countries__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/countries */ \"(middleware)/./src/config/countries.js\");\n/* harmony import */ var _config_inustries__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/inustries */ \"(middleware)/./src/config/inustries.js\");\n/* harmony import */ var html_to_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! html-to-text */ \"(middleware)/./node_modules/html-to-text/lib/html-to-text.mjs\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./constants */ \"(middleware)/./src/utils/constants.js\");\n/* harmony import */ var _helpers_MenuList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/helpers/MenuList */ \"(middleware)/./src/helpers/MenuList.js\");\n/* harmony import */ var _config_Constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/config/Constants */ \"(middleware)/./src/config/Constants.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/helpers/routesList */ \"(middleware)/./src/helpers/routesList.js\");\n\n\n\n\n\n\n\n\nconst getExtension = (fileType)=>{\n    switch(fileType){\n        case \"application/pdf\":\n            return \"pdf\";\n        case \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\":\n            return \"docx\";\n        case \"image/png\":\n            return \"png\";\n        case \"image/jpg\":\n            return \"jpg\";\n        case \"image/jpeg\":\n            return \"jpeg\";\n        default:\n            return \"unknown\";\n    }\n};\n// functions.js\nfunction formatDateArticle(dateString) {\n    if (!dateString) return \"\"; // Handle empty or undefined dateString\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) {\n        console.error(`Invalid date string: ${dateString}`);\n        return \"\";\n    }\n    const dateOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    const timeOptions = {\n        hour: \"numeric\",\n        minute: \"2-digit\",\n        hour12: true\n    };\n    const formattedDate = date.toLocaleDateString(\"en-US\", dateOptions);\n    const formattedTime = date.toLocaleTimeString(\"en-US\", timeOptions);\n    return `${formattedDate}, ${formattedTime}`;\n}\nfunction formatDatedownload(dateString) {\n    if (!dateString) return \"\";\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) {\n        console.error(`Invalid date string: ${dateString}`);\n        return \"\";\n    }\n    const options = {\n        year: \"numeric\",\n        month: \"2-digit\",\n        day: \"2-digit\",\n        hour: \"2-digit\",\n        minute: \"2-digit\",\n        second: \"2-digit\",\n        hour12: false\n    };\n    return date.toLocaleString(\"en-US\", options);\n}\nfunction formatDate(dateString) {\n    if (!dateString) return \"\"; // Handle empty or undefined dateString\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) {\n        console.error(`Invalid date string: ${dateString}`);\n        return \"\";\n    }\n    const options = {\n        year: \"numeric\",\n        month: \"2-digit\",\n        day: \"2-digit\"\n    };\n    return date.toLocaleDateString(\"en-US\", options);\n}\nfunction formatResumeName(resume, fullName) {\n    if (typeof resume !== \"string\") {\n        console.error(\"Le nom du fichier de CV n'est pas valide.\");\n        return \"CV_Anonyme\";\n    }\n    const extension = resume.split(\".\").pop();\n    if (!extension || extension === resume) {\n        console.error(\"Le fichier n'a pas d'extension valide.\");\n        return `CV_${fullName}`;\n    }\n    return `CV_${fullName}.${extension}`;\n}\nconst processContent = (htmlContent)=>{\n    const plainTextContent = (0,html_to_text__WEBPACK_IMPORTED_MODULE_3__.htmlToText)(htmlContent, {\n        wordwrap: false\n    });\n    return plainTextContent.length > 150 ? plainTextContent.substring(0, 150) + \"...\" : plainTextContent;\n};\nconst industryExists = (text)=>{\n    const result = _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.some((item)=>item.value === text || item.pentabellValue === text);\n    // if (result == true) {\n    //   if (text == \"OTHER\") {\n    //     return false;\n    //   } else {\n    //     return true;\n    //   }\n    // }\n    return result;\n};\nconst findIndustryLogoSvg = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value.toLocaleLowerCase() === text.toLocaleLowerCase() || item.pentabellValue === text)?.logoSvg;\n};\nconst findIndustryLabel = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value.toLocaleLowerCase() === text.toLocaleLowerCase() || item.pentabellValue === text)?.label;\n};\nconst findIndustryIcon = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value.toLocaleLowerCase() === text.toLocaleLowerCase() || item.pentabellValue === text)?.icon;\n};\nconst findIndustryColoredIcon = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value && text && String(item.value).toLocaleLowerCase() === String(text).toLocaleLowerCase() || item.pentabellValue === text)?.iconColored;\n};\nconst findnotificationColoredIcon = (text)=>{\n    return _config_Constants__WEBPACK_IMPORTED_MODULE_6__.Notifications_LIST.find((item)=>item.value && text && String(item.value).toLocaleLowerCase() === String(text).toLocaleLowerCase() || item.pentabellValue === text)?.iconColored;\n};\nconst findIndustryClassname = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value.toLocaleLowerCase() === text.toLocaleLowerCase() || item.pentabellValue.toLocaleLowerCase() === text.toLocaleLowerCase())?.classname;\n};\nconst findIndustryLink = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value.toLocaleLowerCase() === text.toLocaleLowerCase() || item.pentabellValue.toLocaleLowerCase() === text.toLocaleLowerCase())?.link;\n};\nconst findIndustryByLargeIcon = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value.toLocaleLowerCase() === text.toLocaleLowerCase() || item.pentabellValue.toLocaleLowerCase() === text.toLocaleLowerCase())?.largeIcon;\n};\nconst findCountryFlag = (text)=>{\n    return _config_countries__WEBPACK_IMPORTED_MODULE_1__.COUNTRIES_LIST_FLAG.find((item)=>item.value === text || item.pentabellValue === text)?.flag;\n};\nconst findCountryLabel = (text)=>{\n    return _config_countries__WEBPACK_IMPORTED_MODULE_1__.COUNTRIES_LIST_FLAG.find((item)=>item.value === text || item.pentabellValue === text)?.label;\n};\nconst getMenuListByRole = (currentUser)=>{\n    if (currentUser?.roles?.includes(_constants__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN)) {\n        return _helpers_MenuList__WEBPACK_IMPORTED_MODULE_5__.MenuList?.admin;\n    }\n    if (currentUser?.roles?.includes(_constants__WEBPACK_IMPORTED_MODULE_4__.Role.CANDIDATE)) {\n        return _helpers_MenuList__WEBPACK_IMPORTED_MODULE_5__.MenuList?.candidate;\n    }\n    if (currentUser?.roles?.includes(_constants__WEBPACK_IMPORTED_MODULE_4__.Role.EDITOR)) {\n        return _helpers_MenuList__WEBPACK_IMPORTED_MODULE_5__.MenuList?.editor;\n    }\n};\nconst getRoutesListByRole = (currentUser)=>{\n    if (currentUser?.roles?.includes(_constants__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN)) {\n        return _helpers_routesList__WEBPACK_IMPORTED_MODULE_7__.adminPermissionsRoutes;\n    }\n    if (currentUser?.roles?.includes(_constants__WEBPACK_IMPORTED_MODULE_4__.Role.CANDIDATE)) {\n        return _helpers_routesList__WEBPACK_IMPORTED_MODULE_7__.candidatePermissionsRoutes;\n    }\n    if (currentUser?.roles?.includes(_constants__WEBPACK_IMPORTED_MODULE_4__.Role.EDITOR)) {\n        return _helpers_routesList__WEBPACK_IMPORTED_MODULE_7__.editorPermissionsRoutes;\n    }\n};\nconst getCountryImage = (country)=>{\n    const formattedCountry = country.toLowerCase().replace(/ /g, \"-\");\n    return `${\"http://localhost:4000\"}/api/v1/maps/${formattedCountry}.png`;\n};\nconst getCountryEventImage = (country)=>{\n    const formattedCountry = country.toLowerCase().replace(/ /g, \"-\");\n    return `https://www.pentabell.com/eventMaps/${formattedCountry}.svg`;\n};\nconst generateLocalizedSlug = (locale, slug)=>{\n    return locale === \"en\" ? `${slug}/` : `/fr${slug}/`;\n};\nconst createList = (items)=>{\n    return items.map((item)=>({\n            text: item\n        }));\n};\nconst capitalizeFirstLetter = (str)=>{\n    return str.split(\" \").map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(\" \");\n};\nfunction stringToColor(string) {\n    let hash = 0;\n    let i;\n    /* eslint-disable no-bitwise */ for(i = 0; i < string?.length; i += 1){\n        hash = string.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    let color = \"#\";\n    for(i = 0; i < 3; i += 1){\n        const value = hash >> i * 8 & 0xff;\n        color += `00${value.toString(16)}`.slice(-2);\n    }\n    /* eslint-enable no-bitwise */ return color;\n}\nfunction stringAvatar(name) {\n    return {\n        sx: {\n            bgcolor: stringToColor(name)\n        },\n        children: `${name?.split(\" \")[0][0]}${name?.split(\" \")[1][0]}`\n    };\n}\nconst splitFirstWord = (txt)=>{\n    const words = txt?.toString().split(\" \") || [];\n    const firstWord = words[0];\n    const restOfText = words.slice(1).join(\" \");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"first-word\",\n                children: [\n                    firstWord,\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\utils\\\\functions.js\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, undefined),\n            restOfText\n        ]\n    }, void 0, true);\n};\nconst highlightMatchingWords = (txt, wordsToHighlight)=>{\n    if (!txt) return null;\n    const regex = new RegExp(`\\\\b(${wordsToHighlight.join(\"|\")})\\\\b`, \"gi\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: txt.split(regex).map((segment, index)=>{\n            const isMatch = wordsToHighlight.includes(segment.trim());\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: isMatch ? \"last-word\" : \"\",\n                children: segment\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\utils\\\\functions.js\",\n                lineNumber: 299,\n                columnNumber: 11\n            }, undefined);\n        })\n    }, void 0, false);\n};\nconst splitLastWord = (txt)=>{\n    const words = txt?.toString().split(\" \") || [];\n    const lastWord = words[words.length - 1]; // Get the last word\n    const restOfText = words.slice(0, -1).join(\" \"); // Join all except the last word\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            restOfText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: [\n                    restOfText,\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\utils\\\\functions.js\",\n                lineNumber: 313,\n                columnNumber: 22\n            }, undefined),\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"last-word\",\n                children: lastWord\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\utils\\\\functions.js\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\nconst formatDuration = (receivedTime)=>{\n    const duration = moment.duration(moment().diff(moment(receivedTime)));\n    if (duration.asDays() >= 1) {\n        return `${Math.floor(duration.asDays())}j`;\n    } else if (duration.asHours() >= 1) {\n        return `${Math.floor(duration.asHours())}h`;\n    } else {\n        return `${Math.floor(duration.minutes())}min`;\n    }\n};\nconst isExpired = (dateOfExpiration)=>{\n    const currentDate = new Date();\n    let expirationDate = new Date(currentDate);\n    if (dateOfExpiration) expirationDate = new Date(dateOfExpiration);\n    else expirationDate.setMonth(expirationDate.getMonth() + 3);\n    return expirationDate < currentDate;\n};\nfunction truncateByCharacter(text, maxChars) {\n    if (text?.length <= maxChars) return text;\n    return text?.slice(0, maxChars).trim() + \"…\";\n}\nconst getSlugByIndustry = (industry)=>{\n    const industryValue = industry || \"\";\n    switch(industryValue){\n        case \"Energies\":\n            return \"energies\";\n        case \"It & Telecom\":\n            return \"it-telecom\";\n        case \"Banking\":\n            return \"banking-insurance\";\n        case \"Transport\":\n            return \"transport\";\n        case \"Pharmaceutical\":\n            return \"pharmaceutical\";\n        default:\n            return \"other\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL3V0aWxzL2Z1bmN0aW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBeUQ7QUFDSjtBQUVYO0FBQ1A7QUFDVztBQUNVO0FBSzFCO0FBRXZCLE1BQU1TLGVBQWUsQ0FBQ0M7SUFDM0IsT0FBUUE7UUFDTixLQUFLO1lBQ0gsT0FBTztRQUNULEtBQUs7WUFDSCxPQUFPO1FBQ1QsS0FBSztZQUNILE9BQU87UUFDVCxLQUFLO1lBQ0gsT0FBTztRQUNULEtBQUs7WUFDSCxPQUFPO1FBRVQ7WUFDRSxPQUFPO0lBQ1g7QUFDRixFQUFFO0FBRUYsZUFBZTtBQUNSLFNBQVNDLGtCQUFrQkMsVUFBVTtJQUMxQyxJQUFJLENBQUNBLFlBQVksT0FBTyxJQUFJLHVDQUF1QztJQUVuRSxNQUFNQyxPQUFPLElBQUlDLEtBQUtGO0lBQ3RCLElBQUlHLE1BQU1GLEtBQUtHLE9BQU8sS0FBSztRQUN6QkMsUUFBUUMsS0FBSyxDQUFDLENBQUMscUJBQXFCLEVBQUVOLFdBQVcsQ0FBQztRQUNsRCxPQUFPO0lBQ1Q7SUFFQSxNQUFNTyxjQUFjO1FBQUVDLE1BQU07UUFBV0MsT0FBTztRQUFRQyxLQUFLO0lBQVU7SUFDckUsTUFBTUMsY0FBYztRQUFFQyxNQUFNO1FBQVdDLFFBQVE7UUFBV0MsUUFBUTtJQUFLO0lBRXZFLE1BQU1DLGdCQUFnQmQsS0FBS2Usa0JBQWtCLENBQUMsU0FBU1Q7SUFDdkQsTUFBTVUsZ0JBQWdCaEIsS0FBS2lCLGtCQUFrQixDQUFDLFNBQVNQO0lBRXZELE9BQU8sQ0FBQyxFQUFFSSxjQUFjLEVBQUUsRUFBRUUsY0FBYyxDQUFDO0FBQzdDO0FBQ08sU0FBU0UsbUJBQW1CbkIsVUFBVTtJQUMzQyxJQUFJLENBQUNBLFlBQVksT0FBTztJQUV4QixNQUFNQyxPQUFPLElBQUlDLEtBQUtGO0lBQ3RCLElBQUlHLE1BQU1GLEtBQUtHLE9BQU8sS0FBSztRQUN6QkMsUUFBUUMsS0FBSyxDQUFDLENBQUMscUJBQXFCLEVBQUVOLFdBQVcsQ0FBQztRQUNsRCxPQUFPO0lBQ1Q7SUFFQSxNQUFNb0IsVUFBVTtRQUNkWixNQUFNO1FBQ05DLE9BQU87UUFDUEMsS0FBSztRQUNMRSxNQUFNO1FBQ05DLFFBQVE7UUFDUlEsUUFBUTtRQUNSUCxRQUFRO0lBQ1Y7SUFFQSxPQUFPYixLQUFLcUIsY0FBYyxDQUFDLFNBQVNGO0FBQ3RDO0FBR08sU0FBU0csV0FBV3ZCLFVBQVU7SUFDbkMsSUFBSSxDQUFDQSxZQUFZLE9BQU8sSUFBSSx1Q0FBdUM7SUFFbkUsTUFBTUMsT0FBTyxJQUFJQyxLQUFLRjtJQUN0QixJQUFJRyxNQUFNRixLQUFLRyxPQUFPLEtBQUs7UUFDekJDLFFBQVFDLEtBQUssQ0FBQyxDQUFDLHFCQUFxQixFQUFFTixXQUFXLENBQUM7UUFDbEQsT0FBTztJQUNUO0lBRUEsTUFBTW9CLFVBQVU7UUFBRVosTUFBTTtRQUFXQyxPQUFPO1FBQVdDLEtBQUs7SUFBVTtJQUNwRSxPQUFPVCxLQUFLZSxrQkFBa0IsQ0FBQyxTQUFTSTtBQUMxQztBQUNPLFNBQVNJLGlCQUFpQkMsTUFBTSxFQUFFQyxRQUFRO0lBQy9DLElBQUksT0FBT0QsV0FBVyxVQUFVO1FBQzlCcEIsUUFBUUMsS0FBSyxDQUFDO1FBQ2QsT0FBTztJQUNUO0lBRUEsTUFBTXFCLFlBQVlGLE9BQU9HLEtBQUssQ0FBQyxLQUFLQyxHQUFHO0lBRXZDLElBQUksQ0FBQ0YsYUFBYUEsY0FBY0YsUUFBUTtRQUN0Q3BCLFFBQVFDLEtBQUssQ0FBQztRQUNkLE9BQU8sQ0FBQyxHQUFHLEVBQUVvQixTQUFTLENBQUM7SUFDekI7SUFFQSxPQUFPLENBQUMsR0FBRyxFQUFFQSxTQUFTLENBQUMsRUFBRUMsVUFBVSxDQUFDO0FBQ3RDO0FBRU8sTUFBTUcsaUJBQWlCLENBQUNDO0lBQzdCLE1BQU1DLG1CQUFtQjFDLHdEQUFVQSxDQUFDeUMsYUFBYTtRQUFFRSxVQUFVO0lBQU07SUFDbkUsT0FBT0QsaUJBQWlCRSxNQUFNLEdBQUcsTUFDN0JGLGlCQUFpQkcsU0FBUyxDQUFDLEdBQUcsT0FBTyxRQUNyQ0g7QUFDTixFQUFFO0FBRUssTUFBTUksaUJBQWlCLENBQUNDO0lBQzdCLE1BQU1DLFNBQVNqRCw4REFBZUEsQ0FBQ2tELElBQUksQ0FDakMsQ0FBQ0MsT0FBU0EsS0FBS0MsS0FBSyxLQUFLSixRQUFRRyxLQUFLRSxjQUFjLEtBQUtMO0lBRTNELHdCQUF3QjtJQUN4QiwyQkFBMkI7SUFDM0Isb0JBQW9CO0lBQ3BCLGFBQWE7SUFDYixtQkFBbUI7SUFDbkIsTUFBTTtJQUNOLElBQUk7SUFDSixPQUFPQztBQUNULEVBQUU7QUFFSyxNQUFNSyxzQkFBc0IsQ0FBQ047SUFDbEMsT0FBT2hELDhEQUFlQSxDQUFDdUQsSUFBSSxDQUN6QixDQUFDSixPQUNDQSxLQUFLQyxLQUFLLENBQUNJLGlCQUFpQixPQUFPUixLQUFLUSxpQkFBaUIsTUFDekRMLEtBQUtFLGNBQWMsS0FBS0wsT0FDekJTO0FBQ0wsRUFBRTtBQUVLLE1BQU1DLG9CQUFvQixDQUFDVjtJQUNoQyxPQUFPaEQsOERBQWVBLENBQUN1RCxJQUFJLENBQ3pCLENBQUNKLE9BQ0NBLEtBQUtDLEtBQUssQ0FBQ0ksaUJBQWlCLE9BQU9SLEtBQUtRLGlCQUFpQixNQUN6REwsS0FBS0UsY0FBYyxLQUFLTCxPQUN6Qlc7QUFDTCxFQUFFO0FBQ0ssTUFBTUMsbUJBQW1CLENBQUNaO0lBQy9CLE9BQU9oRCw4REFBZUEsQ0FBQ3VELElBQUksQ0FDekIsQ0FBQ0osT0FDQ0EsS0FBS0MsS0FBSyxDQUFDSSxpQkFBaUIsT0FBT1IsS0FBS1EsaUJBQWlCLE1BQ3pETCxLQUFLRSxjQUFjLEtBQUtMLE9BQ3pCYTtBQUNMLEVBQUU7QUFDSyxNQUFNQywwQkFBMEIsQ0FBQ2Q7SUFDdEMsT0FBT2hELDhEQUFlQSxDQUFDdUQsSUFBSSxDQUN6QixDQUFDSixPQUNDLEtBQU1DLEtBQUssSUFDVEosUUFDQWUsT0FBT1osS0FBS0MsS0FBSyxFQUFFSSxpQkFBaUIsT0FDcENPLE9BQU9mLE1BQU1RLGlCQUFpQixNQUNoQ0wsS0FBS0UsY0FBYyxLQUFLTCxPQUN6QmdCO0FBQ0wsRUFBRTtBQUNLLE1BQU1DLDhCQUE4QixDQUFDakI7SUFDMUMsT0FBTzVDLGlFQUFrQkEsQ0FBQ21ELElBQUksQ0FDNUIsQ0FBQ0osT0FDQyxLQUFNQyxLQUFLLElBQ1RKLFFBQ0FlLE9BQU9aLEtBQUtDLEtBQUssRUFBRUksaUJBQWlCLE9BQ3BDTyxPQUFPZixNQUFNUSxpQkFBaUIsTUFDaENMLEtBQUtFLGNBQWMsS0FBS0wsT0FDekJnQjtBQUNMLEVBQUU7QUFDSyxNQUFNRSx3QkFBd0IsQ0FBQ2xCO0lBQ3BDLE9BQU9oRCw4REFBZUEsQ0FBQ3VELElBQUksQ0FDekIsQ0FBQ0osT0FDQ0EsS0FBS0MsS0FBSyxDQUFDSSxpQkFBaUIsT0FBT1IsS0FBS1EsaUJBQWlCLE1BQ3pETCxLQUFLRSxjQUFjLENBQUNHLGlCQUFpQixPQUFPUixLQUFLUSxpQkFBaUIsS0FDbkVXO0FBQ0wsRUFBRTtBQUNLLE1BQU1DLG1CQUFtQixDQUFDcEI7SUFDL0IsT0FBT2hELDhEQUFlQSxDQUFDdUQsSUFBSSxDQUN6QixDQUFDSixPQUNDQSxLQUFLQyxLQUFLLENBQUNJLGlCQUFpQixPQUFPUixLQUFLUSxpQkFBaUIsTUFDekRMLEtBQUtFLGNBQWMsQ0FBQ0csaUJBQWlCLE9BQU9SLEtBQUtRLGlCQUFpQixLQUNuRWE7QUFDTCxFQUFFO0FBQ0ssTUFBTUMsMEJBQTBCLENBQUN0QjtJQUN0QyxPQUFPaEQsOERBQWVBLENBQUN1RCxJQUFJLENBQ3pCLENBQUNKLE9BQ0NBLEtBQUtDLEtBQUssQ0FBQ0ksaUJBQWlCLE9BQU9SLEtBQUtRLGlCQUFpQixNQUN6REwsS0FBS0UsY0FBYyxDQUFDRyxpQkFBaUIsT0FBT1IsS0FBS1EsaUJBQWlCLEtBQ25FZTtBQUNMLEVBQUU7QUFFSyxNQUFNQyxrQkFBa0IsQ0FBQ3hCO0lBQzlCLE9BQU9qRCxrRUFBbUJBLENBQUN3RCxJQUFJLENBQzdCLENBQUNKLE9BQVNBLEtBQUtDLEtBQUssS0FBS0osUUFBUUcsS0FBS0UsY0FBYyxLQUFLTCxPQUN4RHlCO0FBQ0wsRUFBRTtBQUNLLE1BQU1DLG1CQUFtQixDQUFDMUI7SUFDL0IsT0FBT2pELGtFQUFtQkEsQ0FBQ3dELElBQUksQ0FDN0IsQ0FBQ0osT0FBU0EsS0FBS0MsS0FBSyxLQUFLSixRQUFRRyxLQUFLRSxjQUFjLEtBQUtMLE9BQ3hEVztBQUNMLEVBQUU7QUFFSyxNQUFNZ0Isb0JBQW9CLENBQUNDO0lBQ2hDLElBQUlBLGFBQWFDLE9BQU9DLFNBQVM1RSw0Q0FBSUEsQ0FBQzZFLEtBQUssR0FBRztRQUM1QyxPQUFPNUUsdURBQVFBLEVBQUU2RTtJQUNuQjtJQUNBLElBQUlKLGFBQWFDLE9BQU9DLFNBQVM1RSw0Q0FBSUEsQ0FBQytFLFNBQVMsR0FBRztRQUNoRCxPQUFPOUUsdURBQVFBLEVBQUUrRTtJQUNuQjtJQUNBLElBQUlOLGFBQWFDLE9BQU9DLFNBQVM1RSw0Q0FBSUEsQ0FBQ2lGLE1BQU0sR0FBRztRQUM3QyxPQUFPaEYsdURBQVFBLEVBQUVpRjtJQUNuQjtBQUNGLEVBQUU7QUFFSyxNQUFNQyxzQkFBc0IsQ0FBQ1Q7SUFDbEMsSUFBSUEsYUFBYUMsT0FBT0MsU0FBUzVFLDRDQUFJQSxDQUFDNkUsS0FBSyxHQUFHO1FBQzVDLE9BQU8xRSx1RUFBc0JBO0lBQy9CO0lBQ0EsSUFBSXVFLGFBQWFDLE9BQU9DLFNBQVM1RSw0Q0FBSUEsQ0FBQytFLFNBQVMsR0FBRztRQUNoRCxPQUFPM0UsMkVBQTBCQTtJQUNuQztJQUNBLElBQUlzRSxhQUFhQyxPQUFPQyxTQUFTNUUsNENBQUlBLENBQUNpRixNQUFNLEdBQUc7UUFDN0MsT0FBTzVFLHdFQUF1QkE7SUFDaEM7QUFDRixFQUFFO0FBRUssTUFBTStFLGtCQUFrQixDQUFDQztJQUM5QixNQUFNQyxtQkFBbUJELFFBQVFFLFdBQVcsR0FBR0MsT0FBTyxDQUFDLE1BQU07SUFFN0QsT0FBTyxDQUFDLEVBQUVDLHVCQUFnQyxDQUFDLGFBQWEsRUFBRUgsaUJBQWlCLElBQUksQ0FBQztBQUNsRixFQUFFO0FBRUssTUFBTU0sdUJBQXVCLENBQUNQO0lBQ25DLE1BQU1DLG1CQUFtQkQsUUFBUUUsV0FBVyxHQUFHQyxPQUFPLENBQUMsTUFBTTtJQUU3RCxPQUFPLENBQUMsb0NBQW9DLEVBQUVGLGlCQUFpQixJQUFJLENBQUM7QUFDdEUsRUFBRTtBQUVLLE1BQU1PLHdCQUF3QixDQUFDQyxRQUFRQztJQUM1QyxPQUFPRCxXQUFXLE9BQU8sQ0FBQyxFQUFFQyxLQUFLLENBQUMsQ0FBQyxHQUFHLENBQUMsR0FBRyxFQUFFQSxLQUFLLENBQUMsQ0FBQztBQUNyRCxFQUFFO0FBRUssTUFBTUMsYUFBYSxDQUFDQztJQUN6QixPQUFPQSxNQUFNQyxHQUFHLENBQUNqRCxDQUFBQSxPQUFTO1lBQUVILE1BQU1HO1FBQUs7QUFDekMsRUFBRTtBQUdLLE1BQU1rRCx3QkFBd0IsQ0FBQ0M7SUFDcEMsT0FBT0EsSUFDSi9ELEtBQUssQ0FBQyxLQUNONkQsR0FBRyxDQUFDLENBQUNHLE9BQVNBLEtBQUtDLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUtGLEtBQUtHLEtBQUssQ0FBQyxJQUN4REMsSUFBSSxDQUFDO0FBQ1YsRUFBRTtBQUVLLFNBQVNDLGNBQWNDLE1BQU07SUFDbEMsSUFBSUMsT0FBTztJQUNYLElBQUlDO0lBRUosNkJBQTZCLEdBQzdCLElBQUtBLElBQUksR0FBR0EsSUFBSUYsUUFBUWhFLFFBQVFrRSxLQUFLLEVBQUc7UUFDdENELE9BQU9ELE9BQU9HLFVBQVUsQ0FBQ0QsS0FBTSxFQUFDRCxRQUFRLEtBQUtBLElBQUc7SUFDbEQ7SUFFQSxJQUFJRyxRQUFRO0lBRVosSUFBS0YsSUFBSSxHQUFHQSxJQUFJLEdBQUdBLEtBQUssRUFBRztRQUN6QixNQUFNM0QsUUFBUSxRQUFVMkQsSUFBSSxJQUFNO1FBQ2xDRSxTQUFTLENBQUMsRUFBRSxFQUFFN0QsTUFBTThELFFBQVEsQ0FBQyxJQUFJLENBQUMsQ0FBQ1IsS0FBSyxDQUFDLENBQUM7SUFDNUM7SUFDQSw0QkFBNEIsR0FFNUIsT0FBT087QUFDVDtBQUVPLFNBQVNFLGFBQWFDLElBQUk7SUFDL0IsT0FBTztRQUNMQyxJQUFJO1lBQ0ZDLFNBQVNWLGNBQWNRO1FBQ3pCO1FBQ0FHLFVBQVUsQ0FBQyxFQUFFSCxNQUFNN0UsTUFBTSxJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFNkUsTUFBTTdFLE1BQU0sSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQztJQUNoRTtBQUNGO0FBRU8sTUFBTWlGLGlCQUFpQixDQUFDQztJQUM3QixNQUFNQyxRQUFRRCxLQUFLUCxXQUFXM0UsTUFBTSxRQUFRLEVBQUU7SUFDOUMsTUFBTW9GLFlBQVlELEtBQUssQ0FBQyxFQUFFO0lBQzFCLE1BQU1FLGFBQWFGLE1BQU1oQixLQUFLLENBQUMsR0FBR0MsSUFBSSxDQUFDO0lBQ3ZDLHFCQUNFOzswQkFDRSw4REFBQ2tCO2dCQUFLQyxXQUFVOztvQkFBY0g7b0JBQVU7Ozs7Ozs7WUFDdkNDOzs7QUFHUCxFQUFFO0FBQ0ssTUFBTUcseUJBQXlCLENBQUNOLEtBQUtPO0lBQzFDLElBQUksQ0FBQ1AsS0FBSyxPQUFPO0lBRWpCLE1BQU1RLFFBQVEsSUFBSUMsT0FBTyxDQUFDLElBQUksRUFBRUYsaUJBQWlCckIsSUFBSSxDQUFDLEtBQUssSUFBSSxDQUFDLEVBQUU7SUFFbEUscUJBQ0U7a0JBQ0djLElBQUlsRixLQUFLLENBQUMwRixPQUFPN0IsR0FBRyxDQUFDLENBQUMrQixTQUFTQztZQUM5QixNQUFNQyxVQUFVTCxpQkFBaUJsRCxRQUFRLENBQUNxRCxRQUFRRyxJQUFJO1lBQ3RELHFCQUNFLDhEQUFDVDtnQkFBaUJDLFdBQVdPLFVBQVUsY0FBYzswQkFDbERGO2VBRFFDOzs7OztRQUlmOztBQUdOLEVBQUU7QUFDSyxNQUFNRyxnQkFBZ0IsQ0FBQ2Q7SUFDNUIsTUFBTUMsUUFBUUQsS0FBS1AsV0FBVzNFLE1BQU0sUUFBUSxFQUFFO0lBQzlDLE1BQU1pRyxXQUFXZCxLQUFLLENBQUNBLE1BQU03RSxNQUFNLEdBQUcsRUFBRSxFQUFFLG9CQUFvQjtJQUM5RCxNQUFNK0UsYUFBYUYsTUFBTWhCLEtBQUssQ0FBQyxHQUFHLENBQUMsR0FBR0MsSUFBSSxDQUFDLE1BQU0sZ0NBQWdDO0lBQ2pGLHFCQUNFOztZQUNHaUIsNEJBQWMsOERBQUNDOztvQkFBTUQ7b0JBQVc7Ozs7Ozs7WUFBVTswQkFFM0MsOERBQUNDO2dCQUFLQyxXQUFVOzBCQUFhVTs7Ozs7Ozs7QUFHbkMsRUFBRTtBQUNLLE1BQU1DLGlCQUFpQixDQUFDQztJQUM3QixNQUFNQyxXQUFXQyxPQUFPRCxRQUFRLENBQUNDLFNBQVNDLElBQUksQ0FBQ0QsT0FBT0Y7SUFFdEQsSUFBSUMsU0FBU0csTUFBTSxNQUFNLEdBQUc7UUFDMUIsT0FBTyxDQUFDLEVBQUVDLEtBQUtDLEtBQUssQ0FBQ0wsU0FBU0csTUFBTSxJQUFJLENBQUMsQ0FBQztJQUM1QyxPQUFPLElBQUlILFNBQVNNLE9BQU8sTUFBTSxHQUFHO1FBQ2xDLE9BQU8sQ0FBQyxFQUFFRixLQUFLQyxLQUFLLENBQUNMLFNBQVNNLE9BQU8sSUFBSSxDQUFDLENBQUM7SUFDN0MsT0FBTztRQUNMLE9BQU8sQ0FBQyxFQUFFRixLQUFLQyxLQUFLLENBQUNMLFNBQVNPLE9BQU8sSUFBSSxHQUFHLENBQUM7SUFDL0M7QUFDRixFQUFFO0FBRUssTUFBTUMsWUFBWSxDQUFDQztJQUN4QixNQUFNQyxjQUFjLElBQUl4STtJQUN4QixJQUFJeUksaUJBQWlCLElBQUl6SSxLQUFLd0k7SUFFOUIsSUFBSUQsa0JBQWtCRSxpQkFBaUIsSUFBSXpJLEtBQUt1STtTQUMzQ0UsZUFBZUMsUUFBUSxDQUFDRCxlQUFlRSxRQUFRLEtBQUs7SUFFekQsT0FBT0YsaUJBQWlCRDtBQUMxQixFQUFFO0FBRUssU0FBU0ksb0JBQW9CekcsSUFBSSxFQUFFMEcsUUFBUTtJQUNoRCxJQUFJMUcsTUFBTUgsVUFBVTZHLFVBQVUsT0FBTzFHO0lBQ3JDLE9BQU9BLE1BQU0wRCxNQUFNLEdBQUdnRCxVQUFVcEIsU0FBUztBQUMzQztBQUVPLE1BQU1xQixvQkFBb0IsQ0FBQ0M7SUFDaEMsTUFBTUMsZ0JBQWdCRCxZQUFZO0lBQ2xDLE9BQVFDO1FBQ04sS0FBSztZQUNILE9BQU87UUFDVCxLQUFLO1lBQ0gsT0FBTztRQUNULEtBQUs7WUFDSCxPQUFPO1FBQ1QsS0FBSztZQUNILE9BQU87UUFDVCxLQUFLO1lBQ0gsT0FBTztRQUNUO1lBQ0UsT0FBTztJQUNYO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvdXRpbHMvZnVuY3Rpb25zLmpzPzA2MGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ09VTlRSSUVTX0xJU1RfRkxBRyB9IGZyb20gXCJAL2NvbmZpZy9jb3VudHJpZXNcIjtcclxuaW1wb3J0IHsgSU5EVVNUUklFU19MSVNUIH0gZnJvbSBcIkAvY29uZmlnL2ludXN0cmllc1wiO1xyXG5cclxuaW1wb3J0IHsgaHRtbFRvVGV4dCB9IGZyb20gXCJodG1sLXRvLXRleHRcIjtcclxuaW1wb3J0IHsgUm9sZSB9IGZyb20gXCIuL2NvbnN0YW50c1wiO1xyXG5pbXBvcnQgeyBNZW51TGlzdCB9IGZyb20gXCJAL2hlbHBlcnMvTWVudUxpc3RcIjtcclxuaW1wb3J0IHsgTm90aWZpY2F0aW9uc19MSVNUIH0gZnJvbSBcIkAvY29uZmlnL0NvbnN0YW50c1wiO1xyXG5pbXBvcnQge1xyXG4gIGFkbWluUGVybWlzc2lvbnNSb3V0ZXMsXHJcbiAgY2FuZGlkYXRlUGVybWlzc2lvbnNSb3V0ZXMsXHJcbiAgZWRpdG9yUGVybWlzc2lvbnNSb3V0ZXMsXHJcbn0gZnJvbSBcIkAvaGVscGVycy9yb3V0ZXNMaXN0XCI7XHJcblxyXG5leHBvcnQgY29uc3QgZ2V0RXh0ZW5zaW9uID0gKGZpbGVUeXBlKSA9PiB7XHJcbiAgc3dpdGNoIChmaWxlVHlwZSkge1xyXG4gICAgY2FzZSBcImFwcGxpY2F0aW9uL3BkZlwiOlxyXG4gICAgICByZXR1cm4gXCJwZGZcIjtcclxuICAgIGNhc2UgXCJhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudFwiOlxyXG4gICAgICByZXR1cm4gXCJkb2N4XCI7XHJcbiAgICBjYXNlIFwiaW1hZ2UvcG5nXCI6XHJcbiAgICAgIHJldHVybiBcInBuZ1wiO1xyXG4gICAgY2FzZSBcImltYWdlL2pwZ1wiOlxyXG4gICAgICByZXR1cm4gXCJqcGdcIjtcclxuICAgIGNhc2UgXCJpbWFnZS9qcGVnXCI6XHJcbiAgICAgIHJldHVybiBcImpwZWdcIjtcclxuXHJcbiAgICBkZWZhdWx0OlxyXG4gICAgICByZXR1cm4gXCJ1bmtub3duXCI7XHJcbiAgfVxyXG59O1xyXG5cclxuLy8gZnVuY3Rpb25zLmpzXHJcbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXREYXRlQXJ0aWNsZShkYXRlU3RyaW5nKSB7XHJcbiAgaWYgKCFkYXRlU3RyaW5nKSByZXR1cm4gXCJcIjsgLy8gSGFuZGxlIGVtcHR5IG9yIHVuZGVmaW5lZCBkYXRlU3RyaW5nXHJcblxyXG4gIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShkYXRlU3RyaW5nKTtcclxuICBpZiAoaXNOYU4oZGF0ZS5nZXRUaW1lKCkpKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKGBJbnZhbGlkIGRhdGUgc3RyaW5nOiAke2RhdGVTdHJpbmd9YCk7XHJcbiAgICByZXR1cm4gXCJcIjtcclxuICB9XHJcblxyXG4gIGNvbnN0IGRhdGVPcHRpb25zID0geyB5ZWFyOiBcIm51bWVyaWNcIiwgbW9udGg6IFwibG9uZ1wiLCBkYXk6IFwibnVtZXJpY1wiIH07XHJcbiAgY29uc3QgdGltZU9wdGlvbnMgPSB7IGhvdXI6IFwibnVtZXJpY1wiLCBtaW51dGU6IFwiMi1kaWdpdFwiLCBob3VyMTI6IHRydWUgfTtcclxuXHJcbiAgY29uc3QgZm9ybWF0dGVkRGF0ZSA9IGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKFwiZW4tVVNcIiwgZGF0ZU9wdGlvbnMpO1xyXG4gIGNvbnN0IGZvcm1hdHRlZFRpbWUgPSBkYXRlLnRvTG9jYWxlVGltZVN0cmluZyhcImVuLVVTXCIsIHRpbWVPcHRpb25zKTtcclxuXHJcbiAgcmV0dXJuIGAke2Zvcm1hdHRlZERhdGV9LCAke2Zvcm1hdHRlZFRpbWV9YDtcclxufVxyXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0RGF0ZWRvd25sb2FkKGRhdGVTdHJpbmcpIHtcclxuICBpZiAoIWRhdGVTdHJpbmcpIHJldHVybiBcIlwiO1xyXG5cclxuICBjb25zdCBkYXRlID0gbmV3IERhdGUoZGF0ZVN0cmluZyk7XHJcbiAgaWYgKGlzTmFOKGRhdGUuZ2V0VGltZSgpKSkge1xyXG4gICAgY29uc29sZS5lcnJvcihgSW52YWxpZCBkYXRlIHN0cmluZzogJHtkYXRlU3RyaW5nfWApO1xyXG4gICAgcmV0dXJuIFwiXCI7XHJcbiAgfVxyXG5cclxuICBjb25zdCBvcHRpb25zID0ge1xyXG4gICAgeWVhcjogXCJudW1lcmljXCIsXHJcbiAgICBtb250aDogXCIyLWRpZ2l0XCIsXHJcbiAgICBkYXk6IFwiMi1kaWdpdFwiLFxyXG4gICAgaG91cjogXCIyLWRpZ2l0XCIsXHJcbiAgICBtaW51dGU6IFwiMi1kaWdpdFwiLFxyXG4gICAgc2Vjb25kOiBcIjItZGlnaXRcIixcclxuICAgIGhvdXIxMjogZmFsc2UsIC8vIG91IHRydWUgc2Vsb24gbGUgZm9ybWF0IHNvdWhhaXTDqVxyXG4gIH07XHJcblxyXG4gIHJldHVybiBkYXRlLnRvTG9jYWxlU3RyaW5nKFwiZW4tVVNcIiwgb3B0aW9ucyk7XHJcbn1cclxuXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0RGF0ZShkYXRlU3RyaW5nKSB7XHJcbiAgaWYgKCFkYXRlU3RyaW5nKSByZXR1cm4gXCJcIjsgLy8gSGFuZGxlIGVtcHR5IG9yIHVuZGVmaW5lZCBkYXRlU3RyaW5nXHJcblxyXG4gIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShkYXRlU3RyaW5nKTtcclxuICBpZiAoaXNOYU4oZGF0ZS5nZXRUaW1lKCkpKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKGBJbnZhbGlkIGRhdGUgc3RyaW5nOiAke2RhdGVTdHJpbmd9YCk7XHJcbiAgICByZXR1cm4gXCJcIjtcclxuICB9XHJcblxyXG4gIGNvbnN0IG9wdGlvbnMgPSB7IHllYXI6IFwibnVtZXJpY1wiLCBtb250aDogXCIyLWRpZ2l0XCIsIGRheTogXCIyLWRpZ2l0XCIgfTtcclxuICByZXR1cm4gZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoXCJlbi1VU1wiLCBvcHRpb25zKTtcclxufVxyXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0UmVzdW1lTmFtZShyZXN1bWUsIGZ1bGxOYW1lKSB7XHJcbiAgaWYgKHR5cGVvZiByZXN1bWUgIT09IFwic3RyaW5nXCIpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJMZSBub20gZHUgZmljaGllciBkZSBDViBuJ2VzdCBwYXMgdmFsaWRlLlwiKTtcclxuICAgIHJldHVybiBcIkNWX0Fub255bWVcIjtcclxuICB9XHJcblxyXG4gIGNvbnN0IGV4dGVuc2lvbiA9IHJlc3VtZS5zcGxpdChcIi5cIikucG9wKCk7XHJcblxyXG4gIGlmICghZXh0ZW5zaW9uIHx8IGV4dGVuc2lvbiA9PT0gcmVzdW1lKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiTGUgZmljaGllciBuJ2EgcGFzIGQnZXh0ZW5zaW9uIHZhbGlkZS5cIik7XHJcbiAgICByZXR1cm4gYENWXyR7ZnVsbE5hbWV9YDtcclxuICB9XHJcblxyXG4gIHJldHVybiBgQ1ZfJHtmdWxsTmFtZX0uJHtleHRlbnNpb259YDtcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IHByb2Nlc3NDb250ZW50ID0gKGh0bWxDb250ZW50KSA9PiB7XHJcbiAgY29uc3QgcGxhaW5UZXh0Q29udGVudCA9IGh0bWxUb1RleHQoaHRtbENvbnRlbnQsIHsgd29yZHdyYXA6IGZhbHNlIH0pO1xyXG4gIHJldHVybiBwbGFpblRleHRDb250ZW50Lmxlbmd0aCA+IDE1MFxyXG4gICAgPyBwbGFpblRleHRDb250ZW50LnN1YnN0cmluZygwLCAxNTApICsgXCIuLi5cIlxyXG4gICAgOiBwbGFpblRleHRDb250ZW50O1xyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGluZHVzdHJ5RXhpc3RzID0gKHRleHQpID0+IHtcclxuICBjb25zdCByZXN1bHQgPSBJTkRVU1RSSUVTX0xJU1Quc29tZShcclxuICAgIChpdGVtKSA9PiBpdGVtLnZhbHVlID09PSB0ZXh0IHx8IGl0ZW0ucGVudGFiZWxsVmFsdWUgPT09IHRleHRcclxuICApO1xyXG4gIC8vIGlmIChyZXN1bHQgPT0gdHJ1ZSkge1xyXG4gIC8vICAgaWYgKHRleHQgPT0gXCJPVEhFUlwiKSB7XHJcbiAgLy8gICAgIHJldHVybiBmYWxzZTtcclxuICAvLyAgIH0gZWxzZSB7XHJcbiAgLy8gICAgIHJldHVybiB0cnVlO1xyXG4gIC8vICAgfVxyXG4gIC8vIH1cclxuICByZXR1cm4gcmVzdWx0O1xyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGZpbmRJbmR1c3RyeUxvZ29TdmcgPSAodGV4dCkgPT4ge1xyXG4gIHJldHVybiBJTkRVU1RSSUVTX0xJU1QuZmluZChcclxuICAgIChpdGVtKSA9PlxyXG4gICAgICBpdGVtLnZhbHVlLnRvTG9jYWxlTG93ZXJDYXNlKCkgPT09IHRleHQudG9Mb2NhbGVMb3dlckNhc2UoKSB8fFxyXG4gICAgICBpdGVtLnBlbnRhYmVsbFZhbHVlID09PSB0ZXh0XHJcbiAgKT8ubG9nb1N2ZztcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBmaW5kSW5kdXN0cnlMYWJlbCA9ICh0ZXh0KSA9PiB7XHJcbiAgcmV0dXJuIElORFVTVFJJRVNfTElTVC5maW5kKFxyXG4gICAgKGl0ZW0pID0+XHJcbiAgICAgIGl0ZW0udmFsdWUudG9Mb2NhbGVMb3dlckNhc2UoKSA9PT0gdGV4dC50b0xvY2FsZUxvd2VyQ2FzZSgpIHx8XHJcbiAgICAgIGl0ZW0ucGVudGFiZWxsVmFsdWUgPT09IHRleHRcclxuICApPy5sYWJlbDtcclxufTtcclxuZXhwb3J0IGNvbnN0IGZpbmRJbmR1c3RyeUljb24gPSAodGV4dCkgPT4ge1xyXG4gIHJldHVybiBJTkRVU1RSSUVTX0xJU1QuZmluZChcclxuICAgIChpdGVtKSA9PlxyXG4gICAgICBpdGVtLnZhbHVlLnRvTG9jYWxlTG93ZXJDYXNlKCkgPT09IHRleHQudG9Mb2NhbGVMb3dlckNhc2UoKSB8fFxyXG4gICAgICBpdGVtLnBlbnRhYmVsbFZhbHVlID09PSB0ZXh0XHJcbiAgKT8uaWNvbjtcclxufTtcclxuZXhwb3J0IGNvbnN0IGZpbmRJbmR1c3RyeUNvbG9yZWRJY29uID0gKHRleHQpID0+IHtcclxuICByZXR1cm4gSU5EVVNUUklFU19MSVNULmZpbmQoXHJcbiAgICAoaXRlbSkgPT5cclxuICAgICAgKGl0ZW0udmFsdWUgJiZcclxuICAgICAgICB0ZXh0ICYmXHJcbiAgICAgICAgU3RyaW5nKGl0ZW0udmFsdWUpLnRvTG9jYWxlTG93ZXJDYXNlKCkgPT09XHJcbiAgICAgICAgU3RyaW5nKHRleHQpLnRvTG9jYWxlTG93ZXJDYXNlKCkpIHx8XHJcbiAgICAgIGl0ZW0ucGVudGFiZWxsVmFsdWUgPT09IHRleHRcclxuICApPy5pY29uQ29sb3JlZDtcclxufTtcclxuZXhwb3J0IGNvbnN0IGZpbmRub3RpZmljYXRpb25Db2xvcmVkSWNvbiA9ICh0ZXh0KSA9PiB7XHJcbiAgcmV0dXJuIE5vdGlmaWNhdGlvbnNfTElTVC5maW5kKFxyXG4gICAgKGl0ZW0pID0+XHJcbiAgICAgIChpdGVtLnZhbHVlICYmXHJcbiAgICAgICAgdGV4dCAmJlxyXG4gICAgICAgIFN0cmluZyhpdGVtLnZhbHVlKS50b0xvY2FsZUxvd2VyQ2FzZSgpID09PVxyXG4gICAgICAgIFN0cmluZyh0ZXh0KS50b0xvY2FsZUxvd2VyQ2FzZSgpKSB8fFxyXG4gICAgICBpdGVtLnBlbnRhYmVsbFZhbHVlID09PSB0ZXh0XHJcbiAgKT8uaWNvbkNvbG9yZWQ7XHJcbn07XHJcbmV4cG9ydCBjb25zdCBmaW5kSW5kdXN0cnlDbGFzc25hbWUgPSAodGV4dCkgPT4ge1xyXG4gIHJldHVybiBJTkRVU1RSSUVTX0xJU1QuZmluZChcclxuICAgIChpdGVtKSA9PlxyXG4gICAgICBpdGVtLnZhbHVlLnRvTG9jYWxlTG93ZXJDYXNlKCkgPT09IHRleHQudG9Mb2NhbGVMb3dlckNhc2UoKSB8fFxyXG4gICAgICBpdGVtLnBlbnRhYmVsbFZhbHVlLnRvTG9jYWxlTG93ZXJDYXNlKCkgPT09IHRleHQudG9Mb2NhbGVMb3dlckNhc2UoKVxyXG4gICk/LmNsYXNzbmFtZTtcclxufTtcclxuZXhwb3J0IGNvbnN0IGZpbmRJbmR1c3RyeUxpbmsgPSAodGV4dCkgPT4ge1xyXG4gIHJldHVybiBJTkRVU1RSSUVTX0xJU1QuZmluZChcclxuICAgIChpdGVtKSA9PlxyXG4gICAgICBpdGVtLnZhbHVlLnRvTG9jYWxlTG93ZXJDYXNlKCkgPT09IHRleHQudG9Mb2NhbGVMb3dlckNhc2UoKSB8fFxyXG4gICAgICBpdGVtLnBlbnRhYmVsbFZhbHVlLnRvTG9jYWxlTG93ZXJDYXNlKCkgPT09IHRleHQudG9Mb2NhbGVMb3dlckNhc2UoKVxyXG4gICk/Lmxpbms7XHJcbn07XHJcbmV4cG9ydCBjb25zdCBmaW5kSW5kdXN0cnlCeUxhcmdlSWNvbiA9ICh0ZXh0KSA9PiB7XHJcbiAgcmV0dXJuIElORFVTVFJJRVNfTElTVC5maW5kKFxyXG4gICAgKGl0ZW0pID0+XHJcbiAgICAgIGl0ZW0udmFsdWUudG9Mb2NhbGVMb3dlckNhc2UoKSA9PT0gdGV4dC50b0xvY2FsZUxvd2VyQ2FzZSgpIHx8XHJcbiAgICAgIGl0ZW0ucGVudGFiZWxsVmFsdWUudG9Mb2NhbGVMb3dlckNhc2UoKSA9PT0gdGV4dC50b0xvY2FsZUxvd2VyQ2FzZSgpXHJcbiAgKT8ubGFyZ2VJY29uO1xyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGZpbmRDb3VudHJ5RmxhZyA9ICh0ZXh0KSA9PiB7XHJcbiAgcmV0dXJuIENPVU5UUklFU19MSVNUX0ZMQUcuZmluZChcclxuICAgIChpdGVtKSA9PiBpdGVtLnZhbHVlID09PSB0ZXh0IHx8IGl0ZW0ucGVudGFiZWxsVmFsdWUgPT09IHRleHRcclxuICApPy5mbGFnO1xyXG59O1xyXG5leHBvcnQgY29uc3QgZmluZENvdW50cnlMYWJlbCA9ICh0ZXh0KSA9PiB7XHJcbiAgcmV0dXJuIENPVU5UUklFU19MSVNUX0ZMQUcuZmluZChcclxuICAgIChpdGVtKSA9PiBpdGVtLnZhbHVlID09PSB0ZXh0IHx8IGl0ZW0ucGVudGFiZWxsVmFsdWUgPT09IHRleHRcclxuICApPy5sYWJlbDtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRNZW51TGlzdEJ5Um9sZSA9IChjdXJyZW50VXNlcikgPT4ge1xyXG4gIGlmIChjdXJyZW50VXNlcj8ucm9sZXM/LmluY2x1ZGVzKFJvbGUuQURNSU4pKSB7XHJcbiAgICByZXR1cm4gTWVudUxpc3Q/LmFkbWluO1xyXG4gIH1cclxuICBpZiAoY3VycmVudFVzZXI/LnJvbGVzPy5pbmNsdWRlcyhSb2xlLkNBTkRJREFURSkpIHtcclxuICAgIHJldHVybiBNZW51TGlzdD8uY2FuZGlkYXRlO1xyXG4gIH1cclxuICBpZiAoY3VycmVudFVzZXI/LnJvbGVzPy5pbmNsdWRlcyhSb2xlLkVESVRPUikpIHtcclxuICAgIHJldHVybiBNZW51TGlzdD8uZWRpdG9yO1xyXG4gIH1cclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRSb3V0ZXNMaXN0QnlSb2xlID0gKGN1cnJlbnRVc2VyKSA9PiB7XHJcbiAgaWYgKGN1cnJlbnRVc2VyPy5yb2xlcz8uaW5jbHVkZXMoUm9sZS5BRE1JTikpIHtcclxuICAgIHJldHVybiBhZG1pblBlcm1pc3Npb25zUm91dGVzO1xyXG4gIH1cclxuICBpZiAoY3VycmVudFVzZXI/LnJvbGVzPy5pbmNsdWRlcyhSb2xlLkNBTkRJREFURSkpIHtcclxuICAgIHJldHVybiBjYW5kaWRhdGVQZXJtaXNzaW9uc1JvdXRlcztcclxuICB9XHJcbiAgaWYgKGN1cnJlbnRVc2VyPy5yb2xlcz8uaW5jbHVkZXMoUm9sZS5FRElUT1IpKSB7XHJcbiAgICByZXR1cm4gZWRpdG9yUGVybWlzc2lvbnNSb3V0ZXM7XHJcbiAgfVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGdldENvdW50cnlJbWFnZSA9IChjb3VudHJ5KSA9PiB7XHJcbiAgY29uc3QgZm9ybWF0dGVkQ291bnRyeSA9IGNvdW50cnkudG9Mb3dlckNhc2UoKS5yZXBsYWNlKC8gL2csIFwiLVwiKTtcclxuXHJcbiAgcmV0dXJuIGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0JBU0VfVVJMfS9hcGkvdjEvbWFwcy8ke2Zvcm1hdHRlZENvdW50cnl9LnBuZ2A7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgZ2V0Q291bnRyeUV2ZW50SW1hZ2UgPSAoY291bnRyeSkgPT4ge1xyXG4gIGNvbnN0IGZvcm1hdHRlZENvdW50cnkgPSBjb3VudHJ5LnRvTG93ZXJDYXNlKCkucmVwbGFjZSgvIC9nLCBcIi1cIik7XHJcblxyXG4gIHJldHVybiBgaHR0cHM6Ly93d3cucGVudGFiZWxsLmNvbS9ldmVudE1hcHMvJHtmb3JtYXR0ZWRDb3VudHJ5fS5zdmdgO1xyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGdlbmVyYXRlTG9jYWxpemVkU2x1ZyA9IChsb2NhbGUsIHNsdWcpID0+IHtcclxuICByZXR1cm4gbG9jYWxlID09PSBcImVuXCIgPyBgJHtzbHVnfS9gIDogYC9mciR7c2x1Z30vYDtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBjcmVhdGVMaXN0ID0gKGl0ZW1zKSA9PiB7XHJcbiAgcmV0dXJuIGl0ZW1zLm1hcChpdGVtID0+ICh7IHRleHQ6IGl0ZW0gfSkpO1xyXG59O1xyXG5cclxuXHJcbmV4cG9ydCBjb25zdCBjYXBpdGFsaXplRmlyc3RMZXR0ZXIgPSAoc3RyKSA9PiB7XHJcbiAgcmV0dXJuIHN0clxyXG4gICAgLnNwbGl0KFwiIFwiKVxyXG4gICAgLm1hcCgod29yZCkgPT4gd29yZC5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIHdvcmQuc2xpY2UoMSkpXHJcbiAgICAuam9pbihcIiBcIik7XHJcbn07XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gc3RyaW5nVG9Db2xvcihzdHJpbmcpIHtcclxuICBsZXQgaGFzaCA9IDA7XHJcbiAgbGV0IGk7XHJcblxyXG4gIC8qIGVzbGludC1kaXNhYmxlIG5vLWJpdHdpc2UgKi9cclxuICBmb3IgKGkgPSAwOyBpIDwgc3RyaW5nPy5sZW5ndGg7IGkgKz0gMSkge1xyXG4gICAgaGFzaCA9IHN0cmluZy5jaGFyQ29kZUF0KGkpICsgKChoYXNoIDw8IDUpIC0gaGFzaCk7XHJcbiAgfVxyXG5cclxuICBsZXQgY29sb3IgPSBcIiNcIjtcclxuXHJcbiAgZm9yIChpID0gMDsgaSA8IDM7IGkgKz0gMSkge1xyXG4gICAgY29uc3QgdmFsdWUgPSAoaGFzaCA+PiAoaSAqIDgpKSAmIDB4ZmY7XHJcbiAgICBjb2xvciArPSBgMDAke3ZhbHVlLnRvU3RyaW5nKDE2KX1gLnNsaWNlKC0yKTtcclxuICB9XHJcbiAgLyogZXNsaW50LWVuYWJsZSBuby1iaXR3aXNlICovXHJcblxyXG4gIHJldHVybiBjb2xvcjtcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIHN0cmluZ0F2YXRhcihuYW1lKSB7XHJcbiAgcmV0dXJuIHtcclxuICAgIHN4OiB7XHJcbiAgICAgIGJnY29sb3I6IHN0cmluZ1RvQ29sb3IobmFtZSksXHJcbiAgICB9LFxyXG4gICAgY2hpbGRyZW46IGAke25hbWU/LnNwbGl0KFwiIFwiKVswXVswXX0ke25hbWU/LnNwbGl0KFwiIFwiKVsxXVswXX1gLFxyXG4gIH07XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBzcGxpdEZpcnN0V29yZCA9ICh0eHQpID0+IHtcclxuICBjb25zdCB3b3JkcyA9IHR4dD8udG9TdHJpbmcoKS5zcGxpdChcIiBcIikgfHwgW107XHJcbiAgY29uc3QgZmlyc3RXb3JkID0gd29yZHNbMF07XHJcbiAgY29uc3QgcmVzdE9mVGV4dCA9IHdvcmRzLnNsaWNlKDEpLmpvaW4oXCIgXCIpO1xyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJmaXJzdC13b3JkXCI+e2ZpcnN0V29yZH0gPC9zcGFuPlxyXG4gICAgICB7cmVzdE9mVGV4dH1cclxuICAgIDwvPlxyXG4gICk7XHJcbn07XHJcbmV4cG9ydCBjb25zdCBoaWdobGlnaHRNYXRjaGluZ1dvcmRzID0gKHR4dCwgd29yZHNUb0hpZ2hsaWdodCkgPT4ge1xyXG4gIGlmICghdHh0KSByZXR1cm4gbnVsbDtcclxuXHJcbiAgY29uc3QgcmVnZXggPSBuZXcgUmVnRXhwKGBcXFxcYigke3dvcmRzVG9IaWdobGlnaHQuam9pbihcInxcIil9KVxcXFxiYCwgXCJnaVwiKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIHt0eHQuc3BsaXQocmVnZXgpLm1hcCgoc2VnbWVudCwgaW5kZXgpID0+IHtcclxuICAgICAgICBjb25zdCBpc01hdGNoID0gd29yZHNUb0hpZ2hsaWdodC5pbmNsdWRlcyhzZWdtZW50LnRyaW0oKSk7XHJcbiAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgIDxzcGFuIGtleT17aW5kZXh9IGNsYXNzTmFtZT17aXNNYXRjaCA/IFwibGFzdC13b3JkXCIgOiBcIlwifT5cclxuICAgICAgICAgICAge3NlZ21lbnR9XHJcbiAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgKTtcclxuICAgICAgfSl9XHJcbiAgICA8Lz5cclxuICApO1xyXG59O1xyXG5leHBvcnQgY29uc3Qgc3BsaXRMYXN0V29yZCA9ICh0eHQpID0+IHtcclxuICBjb25zdCB3b3JkcyA9IHR4dD8udG9TdHJpbmcoKS5zcGxpdChcIiBcIikgfHwgW107XHJcbiAgY29uc3QgbGFzdFdvcmQgPSB3b3Jkc1t3b3Jkcy5sZW5ndGggLSAxXTsgLy8gR2V0IHRoZSBsYXN0IHdvcmRcclxuICBjb25zdCByZXN0T2ZUZXh0ID0gd29yZHMuc2xpY2UoMCwgLTEpLmpvaW4oXCIgXCIpOyAvLyBKb2luIGFsbCBleGNlcHQgdGhlIGxhc3Qgd29yZFxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICB7cmVzdE9mVGV4dCAmJiA8c3Bhbj57cmVzdE9mVGV4dH0gPC9zcGFuPn17XCIgXCJ9XHJcbiAgICAgIHsvKiBBZGQgYSBzcGFjZSBhZnRlciByZXN0T2ZUZXh0IGlmIGl0IGV4aXN0cyAqL31cclxuICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibGFzdC13b3JkXCI+e2xhc3RXb3JkfTwvc3Bhbj5cclxuICAgIDwvPlxyXG4gICk7XHJcbn07XHJcbmV4cG9ydCBjb25zdCBmb3JtYXREdXJhdGlvbiA9IChyZWNlaXZlZFRpbWUpID0+IHtcclxuICBjb25zdCBkdXJhdGlvbiA9IG1vbWVudC5kdXJhdGlvbihtb21lbnQoKS5kaWZmKG1vbWVudChyZWNlaXZlZFRpbWUpKSk7XHJcblxyXG4gIGlmIChkdXJhdGlvbi5hc0RheXMoKSA+PSAxKSB7XHJcbiAgICByZXR1cm4gYCR7TWF0aC5mbG9vcihkdXJhdGlvbi5hc0RheXMoKSl9amA7XHJcbiAgfSBlbHNlIGlmIChkdXJhdGlvbi5hc0hvdXJzKCkgPj0gMSkge1xyXG4gICAgcmV0dXJuIGAke01hdGguZmxvb3IoZHVyYXRpb24uYXNIb3VycygpKX1oYDtcclxuICB9IGVsc2Uge1xyXG4gICAgcmV0dXJuIGAke01hdGguZmxvb3IoZHVyYXRpb24ubWludXRlcygpKX1taW5gO1xyXG4gIH1cclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBpc0V4cGlyZWQgPSAoZGF0ZU9mRXhwaXJhdGlvbikgPT4ge1xyXG4gIGNvbnN0IGN1cnJlbnREYXRlID0gbmV3IERhdGUoKTtcclxuICBsZXQgZXhwaXJhdGlvbkRhdGUgPSBuZXcgRGF0ZShjdXJyZW50RGF0ZSk7XHJcblxyXG4gIGlmIChkYXRlT2ZFeHBpcmF0aW9uKSBleHBpcmF0aW9uRGF0ZSA9IG5ldyBEYXRlKGRhdGVPZkV4cGlyYXRpb24pO1xyXG4gIGVsc2UgZXhwaXJhdGlvbkRhdGUuc2V0TW9udGgoZXhwaXJhdGlvbkRhdGUuZ2V0TW9udGgoKSArIDMpO1xyXG5cclxuICByZXR1cm4gZXhwaXJhdGlvbkRhdGUgPCBjdXJyZW50RGF0ZTtcclxufTtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiB0cnVuY2F0ZUJ5Q2hhcmFjdGVyKHRleHQsIG1heENoYXJzKSB7XHJcbiAgaWYgKHRleHQ/Lmxlbmd0aCA8PSBtYXhDaGFycykgcmV0dXJuIHRleHQ7XHJcbiAgcmV0dXJuIHRleHQ/LnNsaWNlKDAsIG1heENoYXJzKS50cmltKCkgKyBcIuKAplwiO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgZ2V0U2x1Z0J5SW5kdXN0cnkgPSAoaW5kdXN0cnkpID0+IHtcclxuICBjb25zdCBpbmR1c3RyeVZhbHVlID0gaW5kdXN0cnkgfHwgXCJcIjtcclxuICBzd2l0Y2ggKGluZHVzdHJ5VmFsdWUpIHtcclxuICAgIGNhc2UgXCJFbmVyZ2llc1wiOlxyXG4gICAgICByZXR1cm4gXCJlbmVyZ2llc1wiO1xyXG4gICAgY2FzZSBcIkl0ICYgVGVsZWNvbVwiOlxyXG4gICAgICByZXR1cm4gXCJpdC10ZWxlY29tXCI7XHJcbiAgICBjYXNlIFwiQmFua2luZ1wiOlxyXG4gICAgICByZXR1cm4gXCJiYW5raW5nLWluc3VyYW5jZVwiO1xyXG4gICAgY2FzZSBcIlRyYW5zcG9ydFwiOlxyXG4gICAgICByZXR1cm4gXCJ0cmFuc3BvcnRcIjtcclxuICAgIGNhc2UgXCJQaGFybWFjZXV0aWNhbFwiOlxyXG4gICAgICByZXR1cm4gXCJwaGFybWFjZXV0aWNhbFwiO1xyXG4gICAgZGVmYXVsdDpcclxuICAgICAgcmV0dXJuIFwib3RoZXJcIjtcclxuICB9XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJDT1VOVFJJRVNfTElTVF9GTEFHIiwiSU5EVVNUUklFU19MSVNUIiwiaHRtbFRvVGV4dCIsIlJvbGUiLCJNZW51TGlzdCIsIk5vdGlmaWNhdGlvbnNfTElTVCIsImFkbWluUGVybWlzc2lvbnNSb3V0ZXMiLCJjYW5kaWRhdGVQZXJtaXNzaW9uc1JvdXRlcyIsImVkaXRvclBlcm1pc3Npb25zUm91dGVzIiwiZ2V0RXh0ZW5zaW9uIiwiZmlsZVR5cGUiLCJmb3JtYXREYXRlQXJ0aWNsZSIsImRhdGVTdHJpbmciLCJkYXRlIiwiRGF0ZSIsImlzTmFOIiwiZ2V0VGltZSIsImNvbnNvbGUiLCJlcnJvciIsImRhdGVPcHRpb25zIiwieWVhciIsIm1vbnRoIiwiZGF5IiwidGltZU9wdGlvbnMiLCJob3VyIiwibWludXRlIiwiaG91cjEyIiwiZm9ybWF0dGVkRGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImZvcm1hdHRlZFRpbWUiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJmb3JtYXREYXRlZG93bmxvYWQiLCJvcHRpb25zIiwic2Vjb25kIiwidG9Mb2NhbGVTdHJpbmciLCJmb3JtYXREYXRlIiwiZm9ybWF0UmVzdW1lTmFtZSIsInJlc3VtZSIsImZ1bGxOYW1lIiwiZXh0ZW5zaW9uIiwic3BsaXQiLCJwb3AiLCJwcm9jZXNzQ29udGVudCIsImh0bWxDb250ZW50IiwicGxhaW5UZXh0Q29udGVudCIsIndvcmR3cmFwIiwibGVuZ3RoIiwic3Vic3RyaW5nIiwiaW5kdXN0cnlFeGlzdHMiLCJ0ZXh0IiwicmVzdWx0Iiwic29tZSIsIml0ZW0iLCJ2YWx1ZSIsInBlbnRhYmVsbFZhbHVlIiwiZmluZEluZHVzdHJ5TG9nb1N2ZyIsImZpbmQiLCJ0b0xvY2FsZUxvd2VyQ2FzZSIsImxvZ29TdmciLCJmaW5kSW5kdXN0cnlMYWJlbCIsImxhYmVsIiwiZmluZEluZHVzdHJ5SWNvbiIsImljb24iLCJmaW5kSW5kdXN0cnlDb2xvcmVkSWNvbiIsIlN0cmluZyIsImljb25Db2xvcmVkIiwiZmluZG5vdGlmaWNhdGlvbkNvbG9yZWRJY29uIiwiZmluZEluZHVzdHJ5Q2xhc3NuYW1lIiwiY2xhc3NuYW1lIiwiZmluZEluZHVzdHJ5TGluayIsImxpbmsiLCJmaW5kSW5kdXN0cnlCeUxhcmdlSWNvbiIsImxhcmdlSWNvbiIsImZpbmRDb3VudHJ5RmxhZyIsImZsYWciLCJmaW5kQ291bnRyeUxhYmVsIiwiZ2V0TWVudUxpc3RCeVJvbGUiLCJjdXJyZW50VXNlciIsInJvbGVzIiwiaW5jbHVkZXMiLCJBRE1JTiIsImFkbWluIiwiQ0FORElEQVRFIiwiY2FuZGlkYXRlIiwiRURJVE9SIiwiZWRpdG9yIiwiZ2V0Um91dGVzTGlzdEJ5Um9sZSIsImdldENvdW50cnlJbWFnZSIsImNvdW50cnkiLCJmb3JtYXR0ZWRDb3VudHJ5IiwidG9Mb3dlckNhc2UiLCJyZXBsYWNlIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0JBU0VfVVJMIiwiZ2V0Q291bnRyeUV2ZW50SW1hZ2UiLCJnZW5lcmF0ZUxvY2FsaXplZFNsdWciLCJsb2NhbGUiLCJzbHVnIiwiY3JlYXRlTGlzdCIsIml0ZW1zIiwibWFwIiwiY2FwaXRhbGl6ZUZpcnN0TGV0dGVyIiwic3RyIiwid29yZCIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJqb2luIiwic3RyaW5nVG9Db2xvciIsInN0cmluZyIsImhhc2giLCJpIiwiY2hhckNvZGVBdCIsImNvbG9yIiwidG9TdHJpbmciLCJzdHJpbmdBdmF0YXIiLCJuYW1lIiwic3giLCJiZ2NvbG9yIiwiY2hpbGRyZW4iLCJzcGxpdEZpcnN0V29yZCIsInR4dCIsIndvcmRzIiwiZmlyc3RXb3JkIiwicmVzdE9mVGV4dCIsInNwYW4iLCJjbGFzc05hbWUiLCJoaWdobGlnaHRNYXRjaGluZ1dvcmRzIiwid29yZHNUb0hpZ2hsaWdodCIsInJlZ2V4IiwiUmVnRXhwIiwic2VnbWVudCIsImluZGV4IiwiaXNNYXRjaCIsInRyaW0iLCJzcGxpdExhc3RXb3JkIiwibGFzdFdvcmQiLCJmb3JtYXREdXJhdGlvbiIsInJlY2VpdmVkVGltZSIsImR1cmF0aW9uIiwibW9tZW50IiwiZGlmZiIsImFzRGF5cyIsIk1hdGgiLCJmbG9vciIsImFzSG91cnMiLCJtaW51dGVzIiwiaXNFeHBpcmVkIiwiZGF0ZU9mRXhwaXJhdGlvbiIsImN1cnJlbnREYXRlIiwiZXhwaXJhdGlvbkRhdGUiLCJzZXRNb250aCIsImdldE1vbnRoIiwidHJ1bmNhdGVCeUNoYXJhY3RlciIsIm1heENoYXJzIiwiZ2V0U2x1Z0J5SW5kdXN0cnkiLCJpbmR1c3RyeSIsImluZHVzdHJ5VmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(middleware)/./src/utils/functions.js\n");

/***/ })

});