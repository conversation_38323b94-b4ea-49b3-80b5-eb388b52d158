import { Schema, model, Types } from 'mongoose';

import { GlossaryI } from './glossaries.interface';
import { Language, Visibility, robotsMeta } from '@/utils/helpers/constants';

const versionGlossaryFieldsSchema = {
    word: { type: String },
    url: { type: String },
    letter: { type: String },
    content: { type: String },
    metaTitle: { type: String },
    metaDescription: { type: String },
    language: {
        type: String,
        enum: Language,
    },
    visibility: {
        type: String,
        enum: Object.values(Visibility),
        default: Visibility.Draft,
    },
    isArchived: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
    createdBy: { type: Types.ObjectId, ref: 'User' },
    updatedBy: { type: Types.ObjectId, ref: 'User' },
};

// These schemas are no longer needed with the versions map approach
// const englishVersionSchema = new Schema<EnglishVersion>(versionGlossaryFieldsSchema);
// const frenchVersionSchema = new Schema<FrenchVersion>(versionGlossaryFieldsSchema);

const glossarySchema = new Schema<GlossaryI>(
    {
        versions: { type: Map, of: versionGlossaryFieldsSchema, required: true },
        robotsMeta: {
            type: String,
            enum: Object.values(robotsMeta),
            default: robotsMeta.index,
        },
    },
    {
        timestamps: true,
        toJSON: {
            transform: function (_doc, ret) {
                delete ret.__v;
            },
        },
    },
);

export default model<GlossaryI>('Glossary', glossarySchema);
