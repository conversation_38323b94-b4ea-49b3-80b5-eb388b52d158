import { Checkbox, FormControlLabel } from "@mui/material";

const CheckboxGroup = ({ options, values, category, onChange }) => (
  <div className="filter-options">
    {options.map((option) => (
      <FormControlLabel
        control={
          <Checkbox
            checked={values?.[category]?.includes(option)}
            onChange={() => onChange(category, option)}
            className="checkbox-custom-color"
          />
        }
        label={option}
        key={option}
        className="checkbox-pentabell-filter blue"
      />
    ))}
  </div>
);

export default CheckboxGroup;
