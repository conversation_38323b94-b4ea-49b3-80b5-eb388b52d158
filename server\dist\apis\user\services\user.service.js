"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const services_1 = require("@/utils/services");
const user_model_1 = __importDefault(require("../user.model"));
const crypto_1 = __importDefault(require("crypto"));
const bcrypt_1 = __importDefault(require("bcrypt"));
const mailchecker_1 = __importDefault(require("mailchecker"));
const fs = __importStar(require("fs"));
const axios_1 = __importDefault(require("axios"));
const mammoth_1 = __importDefault(require("mammoth"));
const word_extractor_1 = __importDefault(require("word-extractor"));
const pdf_parse_1 = __importDefault(require("pdf-parse"));
const uuid_1 = require("uuid");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const validator_1 = __importDefault(require("validator"));
const https_1 = __importDefault(require("https"));
const functions_1 = require("@/utils/helpers/functions");
const constants_1 = require("@/utils/helpers/constants");
const candidat_model_1 = __importDefault(require("../../candidat/candidat.model"));
const messages_1 = require("@/utils/helpers/messages");
const files_service_1 = __importDefault(require("@/apis/storage/files.service"));
const candidat_model_2 = __importDefault(require("../../candidat/candidat.model"));
const alert_model_1 = __importDefault(require("@/apis/alert/alert.model"));
const user_model_2 = __importDefault(require("../user.model"));
const settings_model_1 = __importDefault(require("../../settings/settings.model"));
const socket_1 = require("@/utils/config/socket");
const commentaire_model_1 = require("@/apis/article/commentaire/commentaire.model");
const notification_model_1 = __importDefault(require("@/apis/notifications/notification.model"));
const login_records_model_1 = __importDefault(require("@/apis/auth/login-records/login-records.model"));
const favourite_model_1 = __importDefault(require("@/apis/Favourite/favourite.model"));
const opportunity_application_model_1 = __importDefault(require("@/apis/opportunity/model/opportunity.application.model"));
const candidat_experience_model_1 = __importDefault(require("@/apis/candidat/experience/candidat.experience.model"));
const candidat_education_model_1 = __importDefault(require("@/apis/candidat/education/candidat.education.model"));
const candidat_certification_model_1 = __importDefault(require("@/apis/candidat/certification/candidat.certification.model"));
class UserService {
    constructor() {
        this.User = user_model_1.default;
        this.Comment = commentaire_model_1.CommentModel;
        this.Alert = alert_model_1.default;
        this.userSettings = settings_model_1.default;
        this.notifications = notification_model_1.default;
        this.loginRecord = login_records_model_1.default;
        this.favorite = favourite_model_1.default;
        this.Applications = opportunity_application_model_1.default;
        this.Candidat = candidat_model_1.default;
        this.fileService = new files_service_1.default();
    }
    async getAll(queries) {
        const { roles, status, country, isArchived, paginated, searchQuery } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 5;
        const queryConditions = {};
        if (roles)
            queryConditions['roles'] = { $in: roles };
        if (status)
            queryConditions['status'] = status;
        if (country)
            queryConditions['country'] = country;
        if (isArchived)
            queryConditions['isArchived'] = isArchived;
        if (searchQuery) {
            queryConditions['$or'] = [
                { firstName: new RegExp(`.*${searchQuery}.*`, 'i') },
                { lastName: new RegExp(`.*${searchQuery}.*`, 'i') },
                { jobTitle: new RegExp(`.*${searchQuery}.*`, 'i') },
            ];
        }
        const totalUsers = await this.User.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalUsers / pageSize);
        let users;
        if (paginated) {
            users = await this.User.find(queryConditions)
                .sort({ createdAt: 'desc' })
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize);
        }
        else {
            users = await this.User.find(queryConditions).sort({ createdAt: 'desc' });
        }
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalUsers,
            users,
        };
    }
    async createUsers(user) {
        if (!mailchecker_1.default.isValid(user.email)) {
            throw new http_exception_1.default(400, messages_1.MESSAGES.AUTH.INVALID_EMAIL);
        }
        const oldUser = await this.User.findOne({ email: user.email });
        if (oldUser)
            throw new http_exception_1.default(409, messages_1.MESSAGES.USER.EMAIL_UNIQUE);
        const password = await services_1.auth.randomPassword();
        const hashedPassword = await services_1.auth.hashPassword(password);
        const token = crypto_1.default.randomBytes(20).toString('hex');
        const formatfirstName = (firstName) => firstName
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
        user.firstName = formatfirstName(user.firstName);
        const newUser = await this.User.create({
            ...user,
            password: hashedPassword,
            confirmAccountToken: token,
            isActive: true,
        });
        const defaultUserSettings = {
            user: newUser._id,
            notifications: {
                newJobAlerts: {
                    email: true,
                    website: true,
                },
                appliedJobStatusUpdates: {
                    email: true,
                    website: true,
                },
                newsLetter: {
                    email: true,
                    website: true,
                },
            },
        };
        await settings_model_1.default.create(defaultUserSettings);
        const alertData = {
            isActive: true,
        };
        const createdAlert = await alert_model_1.default.create({
            ...alertData,
            createdBy: newUser._id,
        });
        let updatedUser = { alerts: createdAlert._id };
        if (user.roles.includes(constants_1.Role.CANDIDATE)) {
            const candidate = await new candidat_model_2.default({
                user: newUser._id,
                emails: [newUser.email],
            }).save();
            updatedUser = { ...updatedUser, candidate: candidate._id };
        }
        await user_model_2.default.findByIdAndUpdate(newUser._id, updatedUser);
        const notificationMessage = 'Welcome to Pentabell, we are happy to see you here!';
        await (0, socket_1.sendNotification)({
            receiver: newUser._id,
            sender: null,
            message: notificationMessage,
            link: `${process.env.LOGIN_LINK}`,
        });
        (0, services_1.sendEmail)({
            to: newUser.email,
            subject: 'Welcome to Pentabell website',
            template: 'newUser',
            context: {
                firstName: newUser.firstName,
                email: newUser.email,
                password: password,
                link: process.env.LOGIN_LINK,
            },
        });
    }
    async deleteUser(id) {
        try {
            const oldUser = await this.User.findById(id);
            if (!oldUser)
                throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
            await this.User.findByIdAndUpdate(id, { isArchived: !oldUser.isArchived }, { new: true });
        }
        catch (error) {
            console.error(error);
        }
    }
    async desactiverAccount(id) {
        try {
            const oldUser = await this.User.findById(id);
            if (!oldUser) {
                throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
            }
            const candidat = await this.Candidat.findOne({ user: id });
            if (candidat) {
                const experienceIds = candidat.experiences;
                const educationIds = candidat.educations;
                const certificationIds = candidat.certifications;
                await this.Applications.deleteMany({ candidat: candidat._id });
                await Promise.all([
                    candidat_experience_model_1.default.deleteMany({ _id: { $in: experienceIds } }),
                    candidat_education_model_1.default.deleteMany({ _id: { $in: educationIds } }),
                    candidat_certification_model_1.default.deleteMany({ _id: { $in: certificationIds } }),
                    this.Candidat.deleteOne({ user: id })
                ]);
            }
            await this.Alert.deleteMany({ createdBy: id });
            await this.userSettings.deleteMany({ user: id });
            await this.loginRecord.deleteMany({ user: id });
            await this.favorite.deleteMany({ user: id });
            await this.notifications.deleteMany({ receiver: id });
            await this.Comment.deleteMany({ user: id });
            await this.User.findByIdAndDelete(id);
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async updateUserDetails(userId, updates, adminId) {
        const admin = await this.User.findById(adminId);
        if (!admin || !(0, functions_1.verifyRoles)(admin.roles, [constants_1.Role.ADMIN])) {
            throw new http_exception_1.default(403, messages_1.MESSAGES.AUTH.UNAUTHORIZED);
        }
        const user = await this.User.findById(userId);
        if (!user) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
        }
        if (updates.password) {
            updates.password = await bcrypt_1.default.hash(updates.password, 10);
        }
        const updatedUser = await this.User.findByIdAndUpdate(userId, updates, { new: true });
        return updatedUser;
    }
    async updateUser(id, user, currentUser) {
        const existingUser = await this.get(id);
        if ((0, functions_1.verifyRole)(constants_1.Role.ADMIN, currentUser) === true) {
            if (user.email && user.email !== existingUser.email) {
                const isEmailUnique = await this.User.exists({ email: user.email });
                if (isEmailUnique) {
                    throw new http_exception_1.default(409, messages_1.MESSAGES.USER.EMAIL_UNIQUE);
                }
            }
            return await this.User.findOneAndUpdate({ email: existingUser.email }, { $set: { ...user } }, { new: true });
        }
        if (currentUser._id.toString() === id) {
            if (user.nationalities) {
                await this.Candidat.findOneAndUpdate({ user: id }, { nationalities: user.nationalities });
            }
            return await this.User.findByIdAndUpdate(id, { ...user }, { new: true });
        }
    }
    async get(id) {
        const user = await this.User.findById(id).populate({
            path: 'candidate',
            populate: [
                { path: 'certifications', model: 'Certification' },
                { path: 'educations', model: 'Education' },
                { path: 'experiences', model: 'Experience' },
            ],
        });
        if (!user) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
        }
        return user;
    }
    async addPhonetouser(id, phone) {
        try {
            const candidat = await this.Candidat.findOne({ user: id });
            if (!candidat) {
                throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
            }
            if (candidat.phones.includes(phone)) {
                throw new http_exception_1.default(400, messages_1.MESSAGES.USER.PHONE_ALREADY_EXISTS);
            }
            candidat.phones.push(phone);
            await candidat.save();
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async addEmailToUser(id, email) {
        try {
            (0, functions_1.validateEmailFormat)(email);
            const candidat = await this.Candidat.findOne({ user: id });
            if (!candidat) {
                throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
            }
            if (candidat.emails.includes(email)) {
                throw new http_exception_1.default(400, messages_1.MESSAGES.USER.EMAIL_ALREADY_EXISTS);
            }
            candidat.emails.push(email);
            await candidat.save();
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async getByEmail(email) {
        const user = await this.User.findOne({ email });
        if (!user)
            throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
        return user;
    }
    async EditEmail(id, oldEmail, newEmail) {
        try {
            const candidat = await this.Candidat.findOne({ user: id });
            if (!candidat) {
                throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
            }
            const emailIndex = candidat.emails.indexOf(oldEmail);
            if (emailIndex === -1) {
                throw new http_exception_1.default(400, messages_1.MESSAGES.USER.EMAIL_NOT_FOUND);
            }
            if (candidat.emails.includes(newEmail)) {
                throw new http_exception_1.default(400, messages_1.MESSAGES.USER.EMAIL_ALREADY_EXISTS);
            }
            candidat.emails[emailIndex] = newEmail;
            await candidat.save();
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async deleteEmail(id, email) {
        const candidat = await this.Candidat.findOne({ user: id });
        if (!candidat) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
        }
        candidat.emails = candidat.emails.filter((p) => p !== email);
        await candidat.save();
    }
    async deletePhone(id, phone) {
        const candidat = await this.Candidat.findOne({ user: id });
        if (!candidat) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
        }
        candidat.phones = candidat.phones.filter((p) => p !== phone);
        await candidat.save();
    }
    async EditPhone(id, oldPhone, newPhone) {
        try {
            const candidat = await this.Candidat.findOne({ user: id });
            if (!candidat) {
                throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
            }
            const phoneIndex = candidat.phones.indexOf(oldPhone);
            if (phoneIndex === -1) {
                throw new http_exception_1.default(400, messages_1.MESSAGES.CANDIDATE.NOT_FOUND);
            }
            candidat.phones[phoneIndex] = newPhone;
            await candidat.save();
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async importUsers(users) {
        users = JSON.parse(users.buffer.toString('utf-8'));
        const createdUsers = [];
        for (const user of users) {
            const foundUser = await this.User.findOne({ email: user.user_email });
            if (!foundUser && mailchecker_1.default.isValid(user.user_email)) {
                if (!user.display_name || user.display_name === '')
                    continue;
                const createdUser = await this.User.create({
                    firstName: this.extractFirstAndLastName(user.display_name).firstName,
                    lastName: this.extractFirstAndLastName(user.display_name).lastName,
                    email: user.user_email.toLowerCase().trim(),
                    roles: constants_1.Role.CANDIDATE,
                    isActive: true,
                    createdAt: new Date(user.user_registered),
                });
                const createdCandidate = await this.Candidat.create({
                    user: createdUser._id,
                    createdAt: new Date(user.user_registered),
                    updatedAt: new Date(),
                });
                const resetPasswordToken = jsonwebtoken_1.default.sign({ userId: createdUser._id }, process.env.RESET_PASSWORD_TOKEN_PRIVATE_KEY, {
                    expiresIn: '336h',
                });
                await this.User.findByIdAndUpdate(createdUser._id, { $set: { candidate: createdCandidate._id, resetPasswordToken } });
                createdUsers.push({
                    fullName: `${createdUser.firstName} ${createdUser.lastName}`,
                    email: createdUser.email,
                    link: `https://www.pentabell.com/reset-password?token=${resetPasswordToken}`,
                });
            }
        }
        fs.writeFileSync('new_users.json', JSON.stringify(createdUsers), 'utf-8');
    }
    async updateUsers() {
        const users = await this.User.find();
        const updatedUsers = [];
        for (const user of users) {
            const resetPasswordToken = jsonwebtoken_1.default.sign({ userId: user._id }, process.env.RESET_PASSWORD_TOKEN_PRIVATE_KEY, {
                expiresIn: '336h',
            });
            user.resetPasswordToken = resetPasswordToken;
            const updatedUser = await this.User.findByIdAndUpdate(user._id, user, { new: true });
            updatedUsers.push({
                fullName: `${updatedUser?.firstName} ${updatedUser?.lastName}`,
                email: updatedUser?.email,
                link: `https://www.pentabell.com/reset-password?token=${resetPasswordToken}`,
            });
        }
        fs.writeFileSync('updated_users.json', JSON.stringify(updatedUsers), 'utf-8');
    }
    async attachResumeToCandidate(postmeta) {
        postmeta = JSON.parse(postmeta.buffer.toString('utf-8'));
        const agent = new https_1.default.Agent({
            rejectUnauthorized: false,
        });
        const extractUrl = (string) => {
            const regex = /(https?:\/\/[^\s"]+)/;
            const match = string.match(regex);
            return match ? match[0] : '';
        };
        const extractEmails = (text) => {
            const words = text.split(/\s+/);
            return words.filter(word => validator_1.default.isEmail(word));
        };
        for (const post of postmeta) {
            let extractedUrl = '';
            if (post.meta_key === '_candidate_cv_attachment')
                extractedUrl = extractUrl(post.meta_value.replace(/\/wp-job-board-pro-uploads\/_candidate_cv_attachment/g, ''));
            else
                extractedUrl = `https://www.pentabell.com/wp-content/uploads/${post.meta_value.replace(/\/wp-job-board-pro-uploads\/_candidate_cv_attachment/g, '')}`;
            try {
                const response = await axios_1.default.get(extractedUrl, { responseType: 'arraybuffer', httpsAgent: agent });
                if (response.status !== 200) {
                    continue;
                }
                let extractedText;
                let extension;
                let mimeType;
                if (extractedUrl.endsWith('.docx')) {
                    const textResult = await mammoth_1.default.extractRawText({ buffer: response.data });
                    extractedText = textResult.value;
                    extension = 'docx';
                    mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                }
                else if (extractedUrl.endsWith('.doc')) {
                    const extractor = new word_extractor_1.default();
                    const doc = await extractor.extract(response.data);
                    extractedText = doc.getBody();
                    extension = 'doc';
                    mimeType = 'application/msword';
                }
                else if (extractedUrl.endsWith('.pdf')) {
                    const data = await (0, pdf_parse_1.default)(response.data);
                    extractedText = data.text;
                    extension = 'pdf';
                    mimeType = 'application/pdf';
                }
                else {
                    continue;
                }
                const emails = extractEmails(extractedText);
                const foundUsers = await this.User.find({ email: { $in: emails } });
                if (foundUsers.length === 0) {
                    continue;
                }
                const resource = 'candidates';
                const extractYear = (string) => {
                    const regex = /\b(20\d{2})\b/;
                    const match = string.match(regex);
                    return match ? match[0] : null;
                };
                const folder = Number(extractYear(post.meta_value));
                const uuid = (0, uuid_1.v4)().replace(/-/g, '');
                const fileName = uuid + '.' + extension;
                const urlParts = extractedUrl.split('/');
                const originalName = decodeURIComponent(urlParts[urlParts.length - 1]);
                const fileData = {
                    resource: resource,
                    folder: String(folder),
                    ipSender: undefined,
                    uuid: uuid,
                    originalName: originalName,
                    fileName: fileName,
                    fileType: mimeType,
                    fileSize: response.data.length,
                    checksum: (0, functions_1.computeChecksumFromBuffer)(response.data),
                };
                const folderPath = `uploads/${resource}/${folder}`;
                const uploadPath = `uploads/${resource}/${folder}/${fileName}`;
                if (!fs.existsSync(folderPath)) {
                    fs.mkdirSync(folderPath, { recursive: true });
                }
                await this.fileService.createFile(fileData);
                await fs.writeFileSync(uploadPath, response.data);
                for (const user of foundUsers) {
                    const foundCandidate = await this.Candidat.findById(user.candidate);
                    if (!foundCandidate)
                        continue;
                    await this.Candidat.findByIdAndUpdate(foundCandidate?._id, {
                        $set: { cv: Array.from(new Set([...foundCandidate.cv, fileName])), flatText: extractedText },
                    });
                }
            }
            catch (error) {
                throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
            }
        }
    }
    async archivedAndDisarchiveUsers(userId, isArchived) {
        try {
            const updatedUser = await this.User.findByIdAndUpdate(userId, { $set: { isArchived } }, { new: true });
            if (!updatedUser) {
                throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
            }
            return isArchived
                ? `User ${userId} archived successfully`
                : `User ${userId} unarchived successfully`;
        }
        catch (error) {
            console.error(error);
            throw new Error(`Error archiving/unarchiving user: `);
        }
    }
    extractFirstAndLastName(fullName) {
        const cleanedName = fullName?.replace(/[^a-zA-Z\s]/g, '').trim();
        const nameParts = cleanedName?.split(/\s+/);
        const length = nameParts.length;
        let firstName, lastName;
        if (length === 0)
            return { firstName: '', lastName: '' };
        if (length % 2 === 0 && length > 2) {
            firstName = nameParts.slice(0, 2).join(' ');
            lastName = nameParts.slice(2).join(' ');
        }
        else {
            firstName = nameParts[0];
            lastName = nameParts.slice(1).join(' ');
        }
        return { firstName, lastName: lastName.toUpperCase() };
    }
}
exports.default = UserService;
//# sourceMappingURL=user.service.js.map