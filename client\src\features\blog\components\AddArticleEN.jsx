"use client";
import { <PERSON>rrorMessage, Field } from "formik";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Visibility } from "../../../utils/constants";
import "react-datepicker/dist/react-datepicker.css";
import { API_URLS } from "../../../utils/urls";
import upload from "@/assets/images/add.png";
import { slug } from "@feelinglovelynow/slug";
import FaqSection from "./FaqSection";

import {
  Autocomplete,
  FormGroup,
  FormLabel,
  Stack,
  TextField,
} from "@mui/material";
import { WithContext as ReactTags } from "react-tag-input";
import { useSaveFile } from "@/features/opportunity/hooks/opportunity.hooks";
import { v4 as uuidv4 } from "uuid";
import CustomTextInput from "@/components/ui/CustomTextInput";
import CustomSunEditor from "@/components/ui/CustomSunEditor";
import CustomSelect from "@/components/ui/CustomSelect";
import CustomDatePicker from "@/components/ui/CustomDatePicker";
import DocumentImporter from "./DocumentImporter";

function AddArticleEN({
  errors,
  touched,
  setFieldValue,
  values,
  onImageSelect,
  filteredCategories,
  categories,
  onCategoriesSelect,
  debounce,
}) {
  const KeyCodes = {
    comma: 188,
    enter: 13,
  };
  const delimiters = [KeyCodes.comma, KeyCodes.enter];
  const [tags, setTags] = useState([]);
  const [highlights, setHighlights] = useState([]);
  const { t } = useTranslation();
  const [description, setDescription] = useState(values.descriptionEN || "");
  const [url, setUrl] = useState(values.urlEN || "");
  const [publishNow, setPublishNow] = useState(false);
  const [publishDate, setPublishDate] = useState(new Date());
  const imageInputRef = useRef(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const language = "en";
  const [title, setTitle] = useState(() => {
    const savedTitle = localStorage.getItem("title");
    return savedTitle ? JSON.parse(savedTitle) : "";
  });

  const [metatitle, setMetatitle] = useState(() => {
    const savedMetatitle = localStorage.getItem("metatitle");
    return savedMetatitle ? JSON.parse(savedMetatitle) : "";
  });

  const [metaDescription, setMetaDescription] = useState(() => {
    const savedMetadescription = localStorage.getItem("metaDescription");
    return savedMetadescription ? JSON.parse(savedMetadescription) : "";
  });

  const [content, setContent] = useState(() => {
    const savedContent = localStorage.getItem("content");
    return savedContent ? JSON.parse(savedContent) : "";
  });

  const handlePaste = (event, cleanData, maxCharCount) => {
    let html = cleanData;
    html = html.replace(/<strong>(.*?)$/g, "<strong>$1</strong>");
    return html;
  };

  const handleEditorChange = (newContent) => {
    debounce();
    setContent(newContent);
    setFieldValue("contentEN", newContent);
  };
  useEffect(() => {
    if (title) {
      localStorage.setItem("title", JSON.stringify(title));
    }
    if (content) {
      localStorage.setItem("content", JSON.stringify(content));
    }
    if (metatitle) {
      localStorage.setItem("metatitle", JSON.stringify(metatitle));
    }
    if (metaDescription) {
      localStorage.setItem("metaDescription", JSON.stringify(metaDescription));
    }
  }, [title, content, metatitle, metaDescription]);

  const handlePhotoChange = async () => {
    const selectedFile = imageInputRef.current.files[0];
    setSelectedImage(imageInputRef.current.files[0]);

    if (selectedFile) {
      onImageSelect(selectedFile, language);
    }
  };
  useEffect(() => {
    setFieldValue("titleEN", title);
    setFieldValue("descriptionEN", description);
    setFieldValue("urlEN", url);
    setFieldValue(
      "keywordsEN",
      tags.map((t) => t.text)
    );
    setFieldValue(
      "highlightsEN",
      highlights.map((h) => h.text)
    );
    setFieldValue("contentEN", content);
    setFieldValue("metaTitleEN", metatitle);
    setFieldValue("metaDescriptionEN", metaDescription);
  }, [
    title,
    description,
    url,
    tags,
    highlights,
    content,
    metatitle,
    metaDescription,
  ]);

  const useSaveFileHook = useSaveFile();

  let uuidPhoto;
  uuidPhoto = uuidv4().replace(/-/g, "");

  const handlePhotoBlogChange = async (file, info, core, uploadHandler) => {
    if (file instanceof HTMLImageElement) {
      const src = file.src;

      if (src.startsWith("data:image")) {
        const base64Data = src.split(",")[1];
        const contentType = src.match(/data:(.*?);base64/)[1];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length)
          .fill(0)
          .map((_, i) => byteCharacters.charCodeAt(i));
        const byteArray = new Uint8Array(byteNumbers);

        const blob = new Blob([byteArray], { type: contentType });
        const fileName = `image_${Date.now()}.${contentType.split("/")[1]}`;
        const selectedFile = new File([blob], fileName, { type: contentType });

        await uploadFile(selectedFile, uploadHandler, core, file);
      } else {
        fetch(src)
          .then((response) => response.blob())
          .then((blob) => {
            const contentType = blob.type;
            const fileName = `image_${Date.now()}.${contentType.split("/")[1]}`;
            const selectedFile = new File([blob], fileName, {
              type: contentType,
            });

            uploadFile(selectedFile, uploadHandler, core, file);
          })
          .catch((error) =>
            console.error("Error converting image URL to Blob:", error)
          );
      }
    } else {
      console.error("File is not an HTMLImageElement.");
    }
  };

  const uploadFile = (selectedFile, uploadHandler, core, originalImage) => {
    let uuidPhoto;
    uuidPhoto = uuidv4().replace(/-/g, "");

    const formData = new FormData();
    formData.append("file", selectedFile);

    const extension = selectedFile.name.split(".").pop();
    const currentYear = new Date().getFullYear();

    useSaveFileHook.mutate(
      {
        resource: "blogs",
        folder: currentYear.toString(),
        filename: uuidPhoto,
        body: { formData, t },
      },
      {
        onSuccess: (dataUUID) => {
          const uuidPhotoFileName =
            dataUUID.message === "uuid exist"
              ? dataUUID.uuid
              : `${uuidPhoto}.${extension}`;

          const imageUrl = `${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${uuidPhotoFileName}`;

          originalImage.src = imageUrl;

          uploadHandler({
            result: [
              {
                id: uuidPhotoFileName,
                url: imageUrl,
              },
            ],
          });
        },
        onError: (error) => {
          console.error("Error uploading file:", error);
        },
      }
    );
  };

  const handleContentExtracted = (extractedContent) => {
    setFieldValue("contentEN", extractedContent);
    setContent(extractedContent);
    localStorage.setItem("content", JSON.stringify(extractedContent));
    debounce(); 
  };

  const handleMetadataExtracted = (metadata) => {
    if (metadata.title && !values.titleEN) {
      setFieldValue("titleEN", metadata.title);
      const url = slug(metadata.title);
      setFieldValue("urlEN", url);
    }

    if (metadata.description && !values.descriptionEN) {
      setFieldValue("descriptionEN", metadata.description);
    }

    if (metadata.keywords && metadata.keywords.length > 0) {
      const keywordTags = metadata.keywords.map((keyword, index) => ({
        id: `extracted-${index}`,
        text: keyword,
      }));

      const existingKeywords = values.keywordsEN || [];
      const mergedKeywords = [...existingKeywords, ...keywordTags];
      setFieldValue(
        "keywordsEN",
        mergedKeywords.map((tag) => tag.text)
      );
    }

    debounce();
  };

  return (
    <>
      <p className="label-pentabell">Add article English : </p>
      <div className="inline-group">
        <div>
          {" "}
          <FormGroup>
            <CustomTextInput
              label={t("createArticle:title")}
              name="titleEN"
              value={title}
              onChange={(e) => {
                const v = e.target.value;
                setTitle(v);
                setUrl(slug(v));
                debounce();
              }}
              error={touched.titleEN && errors.titleEN}
            />
          </FormGroup>
        </div>
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:categories")}
              <Stack>
                <Autocomplete
                  multiple
                  className="input-pentabell"
                  id="tags-standard"
                  options={
                    filteredCategories.length > 0
                      ? filteredCategories
                      : categories
                  }
                  getOptionLabel={(option) => option.name}
                  selected={
                    values.categoryEN.length > 0
                      ? (filteredCategories.length > 0
                          ? filteredCategories
                          : categories
                        ).filter((category) =>
                          values.categoryEN.includes(category.id)
                        )
                      : []
                  }
                  onChange={(event, selectedOptions) => {
                    const categoryIds = selectedOptions.map(
                      (category) => category.id
                    );
                    setFieldValue("categoryEN", categoryIds);
                    onCategoriesSelect(categoryIds);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      className="input-pentabell  multiple-select"
                      variant="standard"
                      // placeholder="nationalities"
                    />
                  )}
                />
              </Stack>
            </FormLabel>
          </FormGroup>
          {touched.category && errors.category && (
            <div className="label-error">{errors.category}</div>
          )}
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <CustomTextInput
              label="Description"
              name="descriptionEN"
              rows={3}
              multiline={true}
              value={values.descriptionEN}
              onChange={(e) => {
                const descriptionEN = e.target.value;
                setFieldValue("descriptionEN", descriptionEN);
              }}
            />
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              Highlights
              <div id="tags">
                <ReactTags
                  tags={highlights}
                  className={
                    "input-pentabell" +
                    (errors.highlightsEN && touched.highlightsEN
                      ? " is-invalid"
                      : "")
                  }
                  delimiters={delimiters}
                  handleDelete={(i) => {
                    const updatedTags = highlights.filter(
                      (tag, index) => index !== i
                    );
                    setHighlights(updatedTags);
                    setFieldValue(
                      "highlightsEN",
                      updatedTags.map((tag) => tag.text)
                    );
                  }}
                  handleAddition={(tag) => {
                    setHighlights([...highlights, tag]);
                    setFieldValue(
                      "highlightsEN",
                      [...highlights, tag].map((item) => item.text)
                    );
                  }}
                  inputFieldPosition="bottom"
                  autocomplete
                  allowDragDrop={false}
                />
              </div>
              <ErrorMessage
                className="label-error"
                name="keywordsEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <FormGroup>
        <DocumentImporter
          onContentExtracted={handleContentExtracted}
          onMetadataExtracted={handleMetadataExtracted}
          language="EN"
        />
        <CustomSunEditor
          content={content || values?.contentEN || ""}
          onChange={(newContent) => {
            setContent(newContent);
            setFieldValue("contentEN", newContent);
            debounce();
          }}
          onPaste={handlePaste}
          onImageUpload={handlePhotoBlogChange}
        />
      </FormGroup>

      <br></br>
      <FaqSection
        values={values}
        setFieldValue={setFieldValue}
        errors={errors}
        touched={touched}
        language="EN"
        debounce={debounce}
      />
      <br></br>
      <div className="inline-group">
        <div>
          {" "}
          <FormGroup>
            <CustomTextInput
              label={t("createArticle:metaTitle")}
              name="metaTitleEN"
              value={metatitle || values.metaTitleEN}
              onChange={(e) => {
                const metaTitleEN = e.target.value;
                setFieldValue("metaTitleEN", metaTitleEN);
                setMetatitle(metaTitleEN);
                debounce();
              }}
              showLength={true}
              maxLength={65}
            />
          </FormGroup>
        </div>
        <div>
          <FormGroup>
            <CustomTextInput
              label={t("createArticle:url")}
              name="urlEN"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              error={touched.urlEN && errors.urlEN}
            />
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <CustomTextInput
              label={t("createArticle:metaDescription")}
              name="metaDescriptionEN"
              value={metaDescription || values.metaDescriptionEN}
              onChange={(e) => {
                const metaDescriptionEN = e.target.value;
                setFieldValue("metaDescriptionEN", metaDescriptionEN);
                setMetaDescription(metaDescriptionEN);
                debounce();
              }}
              showLength={true}
              maxLength={160}
            />
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:featuredImage")}

              <div className="upload-container">
                <label htmlFor={`image-upload-en`} className="file-labels">
                  <input
                    type="file"
                    id={`image-upload-en`}
                    name="imageEN"
                    accept=".png, .jpg, .jpeg, .webp"
                    ref={imageInputRef}
                    onChange={(e) => {
                      setFieldValue("imageEN", e.target.files[0]);
                      handlePhotoChange();
                    }}
                    className={
                      "file-input" +
                      (errors.imageEN && touched.imageEN ? " is-invalid" : "")
                    }
                  />
                  <div className="upload-area">
                    <div>
                      <div
                        className="icon-pic"
                        style={{
                          backgroundImage: `url("${
                            selectedImage
                              ? URL.createObjectURL(selectedImage)
                              : values.imageEN
                              ? `${process.env.REACT_APP_API_URL}${API_URLS.files}/${values.imageEN}`
                              : upload.src
                          }")`,
                          backgroundSize: "cover",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "center",
                          //backgroundColor: "#40bd3921",
                        }}
                      ></div>
                    </div>
                    <div>
                      <p className="upload-text">
                        {t("createArticle:addFeatImg")}
                      </p>
                      <p className="upload-description">
                        {t("createArticle:clickBox")}
                      </p>
                    </div>
                  </div>
                  <ErrorMessage
                    name="image"
                    component="div"
                    className="invalid-feedback error"
                  />
                </label>
              </div>
            </FormLabel>
          </FormGroup>
        </div>
      </div>

      <div className="inline-group">
        <div>
          <FormGroup>
            <CustomTextInput
              label={t("createArticle:alt")}
              name="altEN"
              value={values.altEN}
              onChange={(e) => {
                setFieldValue("altEN", e.target.value);
                debounce();
              }}
            />
          </FormGroup>
        </div>
        <div>
          {" "}
          <FormGroup>
            <CustomSelect
              label={t("createArticle:visibility")}
              name="visibilityEN"
              value={values.visibilityEN}
              onChange={(e) => setFieldValue("visibilityEN", e.target.value)}
              options={Visibility}
              error={touched.visibilityEN && errors.visibilityEN}
              getOptionLabel={(item) => item}
              getOptionValue={(item) => item}
            />
          </FormGroup>
        </div>

        <div>
          {" "}
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:keyword")}

              <div id="tags">
                <ReactTags
                  tags={tags}
                  className={
                    "input-pentabell" +
                    (errors.keywordsEN && touched.keywordsEN
                      ? " is-invalid"
                      : "")
                  }
                  delimiters={delimiters}
                  handleDelete={(i) => {
                    const updatedTags = tags.filter(
                      (tag, index) => index !== i
                    );
                    setTags(updatedTags);
                    setFieldValue(
                      "keywordsEN",
                      updatedTags.map((tag) => tag.text)
                    );
                  }}
                  handleAddition={(tag) => {
                    setTags([...tags, tag]);
                    setFieldValue(
                      "keywordsEN",
                      [...tags, tag].map((item) => item.text)
                    );
                    debounce();
                  }}
                  inputFieldPosition="bottom"
                  autocomplete
                  allowDragDrop={false}
                />
              </div>
              <ErrorMessage
                className="label-error"
                name="keywordsEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <label className="label-form">
            <Field
              type="checkbox"
              name="publishNow"
              checked={publishNow}
              onChange={(e) => {
                setPublishNow(e.target.checked);
                if (e.target.checked) {
                  setFieldValue("publishDateEN", new Date().toISOString());
                }
              }}
            />
            {t("createArticle:publishNow")}
          </label>

          {!publishNow && (
            <div>
              <FormGroup>
                <CustomDatePicker
                  label={t("createArticle:publishDate")}
                  value={values.publishDateEN || new Date()}
                  onChange={(date) => setFieldValue("publishDateEN", date)}
                  error={touched.publishDateEN && errors.publishDateEN}
                />
              </FormGroup>
            </div>
          )}
        </div>
      </div>

      <Field
        type="hidden"
        name="publishDateEN"
        value={
          publishNow ? new Date().toISOString() : publishDate.toISOString()
        }
      />
    </>
  );
}

export default AddArticleEN;
