"use client";
import { ErrorMessage, Field } from "formik";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import SunEditor from "suneditor-react";

import { Visibility } from "../../../utils/constants";
import "react-datepicker/dist/react-datepicker.css";
import { API_URLS } from "../../../utils/urls";
import upload from "@/assets/images/add.png";
import "suneditor/dist/css/suneditor.min.css";
import { slug } from "@feelinglovelynow/slug";
import plugins from "suneditor/src/plugins";
import FaqSection from "./FaqSection";

import {
  Autocomplete,
  FormGroup,
  FormLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import dayjs from "dayjs";
import { WithContext as ReactTags } from "react-tag-input";
import { useSaveFile } from "@/features/opportunity/hooks/opportunity.hooks";
import { v4 as uuidv4 } from "uuid";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";

function AddArticleEN({
  errors,
  touched,
  setFieldValue,
  values,
  onImageSelect,
  filteredCategories,
  categories,
  onCategoriesSelect,
  debounce,
}) {
  const KeyCodes = {
    comma: 188,
    enter: 13,
  };
  const delimiters = [KeyCodes.comma, KeyCodes.enter];
  const [tags, setTags] = useState([]);
  const [highlights, setHighlights] = useState([]);
  const { t } = useTranslation();
  const [publishNow, setPublishNow] = useState(false);
  const [publishDate, setPublishDate] = useState(new Date());
  const imageInputRef = useRef(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const { user } = useCurrentUser();
  const language = "en";
  const [title, setTitle] = useState(() => {
    const savedTitle = localStorage.getItem("title");
    return savedTitle ? JSON.parse(savedTitle) : "";
  });
  const [metatitle, setMetatitle] = useState(() => {
    const savedMetatitle = localStorage.getItem("metatitle");
    return savedMetatitle ? JSON.parse(savedMetatitle) : "";
  });
  const [metaDescription, setMetaDescription] = useState(() => {
    const savedMetadescription = localStorage.getItem("metaDescription");
    return savedMetadescription ? JSON.parse(savedMetadescription) : "";
  });

  const [content, setContent] = useState(() => {
    const savedContent = localStorage.getItem("content");
    return savedContent ? JSON.parse(savedContent) : "";
  });
  const handlePaste = (event, cleanData, maxCharCount) => {
    let html = cleanData;

    // Correction des balises non fermées
    html = html.replace(/<strong>(.*?)$/g, "<strong>$1</strong>");

    return html;
  };

  const handleEditorChange = (newContent) => {
    debounce();
    setContent(newContent);
    setFieldValue("contentEN", newContent);
  };
  useEffect(() => {
    if (title) {
      localStorage.setItem("title", JSON.stringify(title));
    }
    if (content) {
      localStorage.setItem("content", JSON.stringify(content));
    }
    if (metatitle) {
      localStorage.setItem("metatitle", JSON.stringify(metatitle));
    }
    if (metaDescription) {
      localStorage.setItem("metaDescription", JSON.stringify(metaDescription));
    }
  }, [title, content, metatitle, metaDescription]);

  const handlePhotoChange = async () => {
    const selectedFile = imageInputRef.current.files[0];
    setSelectedImage(imageInputRef.current.files[0]);

    if (selectedFile) {
      onImageSelect(selectedFile, language);
    }
  };

  const useSaveFileHook = useSaveFile();

  let uuidPhoto;
  uuidPhoto = uuidv4().replace(/-/g, "");

  const handlePhotoBlogChange = async (file, info, core, uploadHandler) => {
    if (file instanceof HTMLImageElement) {
      const src = file.src;

      if (src.startsWith("data:image")) {
        const base64Data = src.split(",")[1];
        const contentType = src.match(/data:(.*?);base64/)[1];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length)
          .fill(0)
          .map((_, i) => byteCharacters.charCodeAt(i));
        const byteArray = new Uint8Array(byteNumbers);

        const blob = new Blob([byteArray], { type: contentType });
        const fileName = `image_${Date.now()}.${contentType.split("/")[1]}`;
        const selectedFile = new File([blob], fileName, { type: contentType });

        await uploadFile(selectedFile, uploadHandler, core, file);
      } else {
        fetch(src)
          .then((response) => response.blob())
          .then((blob) => {
            const contentType = blob.type;
            const fileName = `image_${Date.now()}.${contentType.split("/")[1]}`;
            const selectedFile = new File([blob], fileName, {
              type: contentType,
            });

            uploadFile(selectedFile, uploadHandler, core, file);
          })
          .catch((error) =>
            console.error("Error converting image URL to Blob:", error)
          );
      }
    } else {
      console.error("File is not an HTMLImageElement.");
    }
  };

  const uploadFile = (selectedFile, uploadHandler, core, originalImage) => {
    let uuidPhoto;
    uuidPhoto = uuidv4().replace(/-/g, "");

    const formData = new FormData();
    formData.append("file", selectedFile);

    const extension = selectedFile.name.split(".").pop();
    const currentYear = new Date().getFullYear();

    useSaveFileHook.mutate(
      {
        resource: "blogs",
        folder: currentYear.toString(),
        filename: uuidPhoto,
        body: { formData, t },
      },
      {
        onSuccess: (dataUUID) => {
          const uuidPhotoFileName =
            dataUUID.message === "uuid exist"
              ? dataUUID.uuid
              : `${uuidPhoto}.${extension}`;

          const imageUrl = `${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${uuidPhotoFileName}`;

          originalImage.src = imageUrl;

          uploadHandler({
            result: [
              {
                id: uuidPhotoFileName,
                url: imageUrl,
              },
            ],
          });
        },
        onError: (error) => {
          console.error("Error uploading file:", error);
        },
      }
    );
  };

  return (
    <>
      <p className="label-pentabell">Add article English : </p>

      <div className="inline-group">
        <div>
          {" "}
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:title")}
              <TextField
                variant="standard"
                name="titleEN"
                type="text"
                value={title || values.titleEN}
                onChange={(e) => {
                  const titleEN = e.target.value;
                  setFieldValue("titleEN", titleEN);
                  setTitle(titleEN);
                  const url = slug(titleEN);
                  setFieldValue("urlEN", url);
                  debounce();
                }}
                className={
                  "input-pentabell" +
                  (errors.titleEN && touched.titleEN ? " is-invalid" : "")
                }
              />{" "}
              <ErrorMessage
                className="label-error"
                name="titleEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>

        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:categories")}
              <Stack>
                <Autocomplete
                  multiple
                  className="input-pentabell"
                  id="tags-standard"
                  options={
                    filteredCategories.length > 0
                      ? filteredCategories
                      : categories
                  }
                  getOptionLabel={(option) => option.name}
                  selected={
                    values.categoryEN.length > 0
                      ? (filteredCategories.length > 0
                          ? filteredCategories
                          : categories
                        ).filter((category) =>
                          values.categoryEN.includes(category.id)
                        )
                      : []
                  }
                  onChange={(event, selectedOptions) => {
                    const categoryIds = selectedOptions.map(
                      (category) => category.id
                    );
                    setFieldValue("categoryEN", categoryIds);
                    onCategoriesSelect(categoryIds);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      className="input-pentabell  multiple-select"
                      variant="standard"
                      // placeholder="nationalities"
                    />
                  )}
                />
              </Stack>
            </FormLabel>
          </FormGroup>

          {touched.category && errors.category && (
            <div className="label-error">{errors.category}</div>
          )}
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              Description
              <TextField
                variant="standard"
                name="descriptionEN"
                type="text"
                multiline
                rows={3}
                value={values.descriptionEN}
                onChange={(e) => {
                  const descriptionEN = e.target.value;
                  setFieldValue("descriptionEN", descriptionEN);
                }}
                className={
                  "textArea-pentabell" +
                  (errors.descriptionEN && touched.descriptionEN
                    ? " is-invalid"
                    : "")
                }
              />{" "}
              <ErrorMessage
                className="label-error"
                name="descriptionEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              Highlights
              <div id="tags">
                <ReactTags
                  tags={highlights}
                  className={
                    "input-pentabell" +
                    (errors.highlightsEN && touched.highlightsEN
                      ? " is-invalid"
                      : "")
                  }
                  delimiters={delimiters}
                  handleDelete={(i) => {
                    const updatedTags = highlights.filter(
                      (tag, index) => index !== i
                    );
                    setHighlights(updatedTags);
                    setFieldValue(
                      "highlightsEN",
                      updatedTags.map((tag) => tag.text)
                    );
                  }}
                  handleAddition={(tag) => {
                    setHighlights([...highlights, tag]);
                    setFieldValue(
                      "highlightsEN",
                      [...highlights, tag].map((item) => item.text)
                    );
                  }}
                  inputFieldPosition="bottom"
                  autocomplete
                  allowDragDrop={false}
                />
              </div>
              <ErrorMessage
                className="label-error"
                name="keywordsEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <SunEditor
        setContents={content || values?.contentEN || ""}
        onChange={(newContent) => {
          setContent(newContent);
          setFieldValue("contentEN", newContent);
          debounce();
        }}
        onPaste={handlePaste}
        setOptions={{
          cleanHTML: false,
          disableHtmlSanitizer: true,
          addTagsWhitelist:
            "h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button",
          plugins: plugins,
          buttonList: [
            ["undo", "redo"],
            ["font", "fontSize", "formatBlock"],
            [
              "bold",
              "underline",
              "italic",
              "strike",
              "subscript",
              "superscript",
            ],
            ["fontColor", "hiliteColor"],
            ["align", "list", "lineHeight"],
            ["outdent", "indent"],
            ["table", "horizontalRule", "link", "image", "video"],
            ["fullScreen", "showBlocks", "codeView"],
            ["preview", "print"],
            ["removeFormat"],
          ],
          imageUploadHandler: handlePhotoBlogChange,
          defaultTag: "div",
          minHeight: "300px",
          maxHeight: "400px",
          showPathLabel: false,
          font: [
            "Proxima-Nova-Regular",
            "Proxima-Nova-Medium",
            "Proxima-Nova-Semibold",
            "Proxima-Nova-Bold",
            "Proxima-Nova-Extrabold",
            "Proxima-Nova-Black",
            "Proxima-Nova-Light",
            "Proxima-Nova-Thin",
            "Arial",
            "Times New Roman",
            "Sans-Serif",
          ],
          charCounter: true, // Show character counter
          charCounterType: "byte",
          resizingBar: false, // Hide resizing bar for a cleaner UI
          colorList: [
            // Standard Colors
            [
              "#234791",
              "#d69b19",
              "#cc3233",
              "#009966",
              "#0b3051",
              "#2BBFAD",
              "#0b305100",
              "#0a305214",
              "#743794",
              "#ff0000",
              "#ff5e00",
              "#ffe400",
              "#abf200",
              "#00d8ff",
              "#0055ff",
              "#6600ff",
              "#ff00dd",
              "#000000",
              "#ffd8d8",
              "#fae0d4",
              "#faf4c0",
              "#e4f7ba",
              "#d4f4fa",
              "#d9e5ff",
              "#e8d9ff",
              "#ffd9fa",
              "#f1f1f1",
              "#ffa7a7",
              "#ffc19e",
              "#faed7d",
              "#cef279",
              "#b2ebf4",
              "#b2ccff",
              "#d1b2ff",
              "#ffb2f5",
              "#bdbdbd",
              "#f15f5f",
              "#f29661",
              "#e5d85c",
              "#bce55c",
              "#5cd1e5",
              "#6699ff",
              "#a366ff",
              "#f261df",
              "#8c8c8c",
              "#980000",
              "#993800",
              "#998a00",
              "#6b9900",
              "#008299",
              "#003399",
              "#3d0099",
              "#990085",
              "#353535",
              "#670000",
              "#662500",
              "#665c00",
              "#476600",
              "#005766",
              "#002266",
              "#290066",
              "#660058",
              "#222222",
            ], // For box shadow with opacity
          ],
        }}
        onImageUpload={handlePhotoBlogChange}
      />
      <br></br>

      <FaqSection
        values={values}
        setFieldValue={setFieldValue}
        errors={errors}
        touched={touched}
        language="EN"
        debounce={debounce}
      />
      <br></br>
      <div className="inline-group">
        <div>
          {" "}
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:metaTitle")} ({" "}
              <span
                className={
                  values.metaTitleEN?.length > 65 ? " text-danger" : ""
                }
              >
                {" "}
                {values.metaTitleEN?.length} / 65{" "}
              </span>{" "}
              )
              <TextField
                variant="standard"
                name="metaTitleEN"
                type="text"
                value={metatitle || values.metaTitleEN}
                onChange={(e) => {
                  const metaTitleEN = e.target.value;
                  setFieldValue("metaTitleEN", metaTitleEN);
                  setMetatitle(metaTitleEN);
                  debounce();
                }}
                className={
                  "input-pentabell" +
                  (errors.metaTitleEN && touched.metaTitleEN
                    ? " is-invalid"
                    : "")
                }
              />{" "}
              <ErrorMessage
                className="label-error"
                name="metaTitleEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
        <div>
          {" "}
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:url")}
              <TextField
                variant="standard"
                name="urlEN"
                type="text"
                value={values.urlEN}
                onChange={(e) => {
                  setFieldValue("urlEN", e.target.value);
                }}
                className={
                  "input-pentabell" +
                  (errors.urlEN && touched.urlEN ? " is-invalid" : "")
                }
              />{" "}
              <ErrorMessage
                className="label-error"
                name="urlEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:metaDescription")} ({" "}
              <span
                className={
                  values.metaDescriptionEN?.length > 160 ? " text-danger" : ""
                }
              >
                {values.metaDescriptionEN?.length} / 160
              </span>{" "}
              )
              <TextField
                variant="standard"
                name="metaDescriptionEN"
                type="text"
                multiline
                rows={3}
                value={metaDescription || values.metaDescriptionEN}
                onChange={(e) => {
                  const metaDescriptionEN = e.target.value;
                  setFieldValue("metaDescriptionEN", metaDescriptionEN);
                  setMetaDescription(metaDescriptionEN);
                  debounce();
                }}
                className={
                  "textArea-pentabell" +
                  (errors.metaDescriptionEN && touched.metaDescriptionEN
                    ? " is-invalid"
                    : "")
                }
              />{" "}
              <ErrorMessage
                className="label-error"
                name="metaDescriptionEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>

      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:featuredImage")}

              <div className="upload-container">
                <label htmlFor={`image-upload-en`} className="file-labels">
                  <input
                    type="file"
                    id={`image-upload-en`}
                    name="imageEN"
                    accept=".png, .jpg, .jpeg, .webp"
                    ref={imageInputRef}
                    onChange={(e) => {
                      setFieldValue("imageEN", e.target.files[0]);
                      handlePhotoChange();
                    }}
                    className={
                      "file-input" +
                      (errors.imageEN && touched.imageEN ? " is-invalid" : "")
                    }
                  />
                  <div className="upload-area">
                    <div>
                      <div
                        className="icon-pic"
                        style={{
                          backgroundImage: `url("${
                            selectedImage
                              ? URL.createObjectURL(selectedImage)
                              : values.imageEN
                              ? `${process.env.REACT_APP_API_URL}${API_URLS.files}/${values.imageEN}`
                              : upload.src
                          }")`,
                          backgroundSize: "cover",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "center",
                          //backgroundColor: "#40bd3921",
                        }}
                      ></div>
                    </div>
                    <div>
                      <p className="upload-text">
                        {t("createArticle:addFeatImg")}
                      </p>
                      <p className="upload-description">
                        {t("createArticle:clickBox")}
                      </p>
                    </div>
                  </div>
                  <ErrorMessage
                    name="image"
                    component="div"
                    className="invalid-feedback error"
                  />
                </label>
              </div>
            </FormLabel>
          </FormGroup>
        </div>
      </div>

      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:alt")}
              <TextField
                variant="standard"
                name="altEN"
                type="text"
                value={values.altEN}
                onChange={(e) => {
                  setFieldValue("altEN", e.target.value);
                  debounce();
                }}
                className={
                  "input-pentabell" +
                  (errors.altEN && touched.altEN ? " is-invalid" : "")
                }
              />{" "}
              <ErrorMessage
                className="label-error"
                name="altEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
        <div>
          {" "}
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:visibility")}

              <Select
                className="select-pentabell"
                variant="standard"
                value={Visibility.filter(
                  (option) => values.visibilityEN === option
                )}
                selected={values.visibilityEN}
                onChange={(event) => {
                  setFieldValue("visibilityEN", event.target.value);
                }}
              >
                {Visibility.map((item, index) => (
                  <MenuItem key={index} value={item}>
                    {item}
                  </MenuItem>
                ))}
              </Select>

              <ErrorMessage
                className="label-error"
                name="visibilityEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>

        <div>
          {" "}
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:keyword")}

              <div id="tags">
                <ReactTags
                  tags={tags}
                  className={
                    "input-pentabell" +
                    (errors.keywordsEN && touched.keywordsEN
                      ? " is-invalid"
                      : "")
                  }
                  delimiters={delimiters}
                  handleDelete={(i) => {
                    const updatedTags = tags.filter(
                      (tag, index) => index !== i
                    );
                    setTags(updatedTags);
                    setFieldValue(
                      "keywordsEN",
                      updatedTags.map((tag) => tag.text)
                    );
                  }}
                  handleAddition={(tag) => {
                    setTags([...tags, tag]);
                    setFieldValue(
                      "keywordsEN",
                      [...tags, tag].map((item) => item.text)
                    );
                    debounce();
                  }}
                  inputFieldPosition="bottom"
                  autocomplete
                  allowDragDrop={false}
                />
              </div>
              <ErrorMessage
                className="label-error"
                name="keywordsEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <label className="label-form">
            <Field
              type="checkbox"
              name="publishNow"
              checked={publishNow}
              onChange={(e) => {
                setPublishNow(e.target.checked);
                if (e.target.checked) {
                  setFieldValue("publishDateEN", new Date().toISOString());
                }
              }}
            />
            {t("createArticle:publishNow")}
          </label>

          {!publishNow && (
            <div>
              <FormGroup>
                <FormLabel className="label-form">
                  {t("createArticle:publishDate")}
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DemoContainer components={["DatePicker"]}>
                      <DatePicker
                        variant="standard"
                        className="input-date"
                        format="DD/MM/YYYY"
                        value={dayjs(values.publishDateEN || new Date())}
                        onChange={(date) => {
                          setFieldValue(
                            "publishDateEN",
                            dayjs(date).format("YYYY-MM-DD")
                          );
                        }}
                      />{" "}
                    </DemoContainer>
                  </LocalizationProvider>
                </FormLabel>{" "}
                <ErrorMessage
                  className="label-error"
                  name="publishDateEN"
                  component="div"
                />
              </FormGroup>
            </div>
          )}
        </div>
      </div>

      <Field
        type="hidden"
        name="publishDateEN"
        value={
          publishNow ? new Date().toISOString() : publishDate.toISOString()
        }
      />
    </>
  );
}

export default AddArticleEN;
