"use client";
import useEmblaCarousel from "embla-carousel-react";
import { useTranslation } from "react-i18next";
import Autoplay from "embla-carousel-autoplay";
import React, { useState, useCallback, useEffect, useMemo } from "react";
import {
  Container,
  Link,
  useMediaQuery,
  useTheme,
  Skeleton,
} from "@mui/material";
import Image from "next/image";

import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from "./EmblaCarouselArrowButtons";
import CustomButton from "../CustomButton";
import SvgArrowwhite from "../../../assets/images/icons/arrowwhite.svg";
import SvgArrowLeft from "../../../assets/images/icons/arrowLeft.svg";
import SvgArrowRight from "../../../assets/images/icons/arrowRight.svg";

// Performance optimized image component with layout shift prevention
const OptimizedCarouselImage = React.memo(
  ({ src, alt, isMobile, priority = false, onLoad, onError }) => {
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);

    const handleLoad = useCallback(() => {
      setImageLoaded(true);
      onLoad?.();
    }, [onLoad]);

    const handleError = useCallback(() => {
      setImageError(true);
      onError?.();
    }, [onError]);

    // Define consistent aspect ratios to prevent layout shifts
    const aspectRatio = isMobile ? "16/9" : "21/9";
    const width = isMobile ? 390 : 1200;
    const height = isMobile ? 220 : 514;

    return (
      <div
        className="carousel-image-container"
        style={{
          position: "relative",
          width: "100%",
          aspectRatio,
          backgroundColor: "#f5f5f5",
          overflow: "hidden",
        }}
      >
        {!imageLoaded && !imageError && (
          <Skeleton
            variant="rectangular"
            width="100%"
            height="100%"
            animation="wave"
            sx={{
              position: "absolute",
              top: 0,
              left: 0,
              zIndex: 1,
            }}
          />
        )}

        <Image
          src={src}
          alt={alt}
          width={width}
          height={height}
          priority={priority}
          quality={85}
          onLoad={handleLoad}
          onError={handleError}
          style={{
            width: "100%",
            height: "100%",
            objectFit: "cover",
            objectPosition: "center",
            opacity: imageLoaded ? 1 : 0,
            transition: "opacity 0.3s ease-in-out",
          }}
          sizes={
            isMobile
              ? "(max-width: 600px) 100vw"
              : "(max-width: 1200px) 100vw, 1200px"
          }
        />

        {imageError && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "#e0e0e0",
              color: "#666",
              fontSize: "14px",
            }}
          >
            Image failed to load
          </div>
        )}
      </div>
    );
  }
);

// Memoized title formatting to prevent unnecessary re-renders
const formatTitleWithLineBreak = React.memo(({ title, wordsPerLine = 5 }) => {
  if (!title) return "";

  const words = title.trim().split(/\s+/);
  const lines = [];

  for (let i = 0; i < words.length; i += wordsPerLine) {
    lines.push(words.slice(i, i + wordsPerLine).join(" "));
  }

  return (
    <>
      {lines.map((line, index) => (
        <React.Fragment key={index}>
          {line}
          {index < lines.length - 1 && <br />}
        </React.Fragment>
      ))}
    </>
  );
});

// Optimized carousel slide component
const CarouselSlide = React.memo(({ item, index, isMobile, isActive, t }) => {
  const [contentLoaded, setContentLoaded] = useState(false);

  const handleImageLoad = useCallback(() => {
    setContentLoaded(true);
  }, []);

  return (
    <div className="embla__slide" key={index}>
      <OptimizedCarouselImage
        src={isMobile ? item.imgMobile : item.img}
        alt={item.altImg}
        isMobile={isMobile}
        priority={index === 0} // Prioritize first image
        onLoad={handleImageLoad}
      />

      <Container className="slide__container custom-max-width">
        <div
          className="embla__slide__content"
          style={{
            opacity: contentLoaded ? 1 : 0,
            transition: "opacity 0.3s ease-in-out",
          }}
        >
          <p className="embla__slide__title">
            <formatTitleWithLineBreak
              title={item.title}
              wordsPerLine={isMobile ? 12 : 5}
            />
          </p>

          {item.customLink ? (
            <Link href={item.link} className="btn btn-slider">
              {t("global:discoverBtn")}
              <SvgArrowwhite />
            </Link>
          ) : item.link ? (
            <CustomButton
              text={item.linkBtn}
              className="explore-btn"
              link={item.link}
              samePage
              externalLink
              icon={<SvgArrowwhite />}
            />
          ) : (
            <br />
          )}
        </div>
      </Container>
    </div>
  );
});

function CustomEmblaCarousel({ slides = [], options, slideId, isMobileSSR }) {
  const { t } = useTranslation();
  const theme = useTheme();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [carouselReady, setCarouselReady] = useState(false);

  // Memoize mobile detection to prevent unnecessary re-renders
  const isMobile = useMemo(() => {
    return (
      useMediaQuery(theme.breakpoints.down("sm"), { noSsr: true }) ||
      isMobileSSR
    );
  }, [theme.breakpoints, isMobileSSR]);

  // Memoize autoplay options to prevent recreation
  const autoplayOptions = useMemo(
    () => [
      Autoplay({
        playOnInit: !isMobile,
        delay: 3500,
        stopOnInteraction: false,
        stopOnMouseEnter: true,
        stopOnFocusIn: false,
      }),
    ],
    [isMobile]
  );

  const [emblaRef, emblaApi] = useEmblaCarousel(options, autoplayOptions);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  // Track selected slide for performance optimizations
  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;

    setCarouselReady(true);
    onSelect();
    emblaApi.on("select", onSelect);

    return () => {
      emblaApi.off("select", onSelect);
    };
  }, [emblaApi, onSelect]);

  // Preload next and previous images for better performance
  useEffect(() => {
    if (!carouselReady || slides.length <= 1) return;

    const preloadImages = () => {
      const nextIndex = (selectedIndex + 1) % slides.length;
      const prevIndex =
        selectedIndex === 0 ? slides.length - 1 : selectedIndex - 1;

      [nextIndex, prevIndex].forEach((index) => {
        const slide = slides[index];
        if (slide) {
          const img = new Image();
          img.src = isMobile ? slide.imgMobile : slide.img;
        }
      });
    };

    const timeoutId = setTimeout(preloadImages, 100);
    return () => clearTimeout(timeoutId);
  }, [selectedIndex, slides, isMobile, carouselReady]);

  // Memoize slides to prevent unnecessary re-renders
  const memoizedSlides = useMemo(() => {
    return slides.map((item, index) => (
      <CarouselSlide
        key={`${slideId}-slide-${index}`}
        item={item}
        index={index}
        isMobile={isMobile}
        isActive={index === selectedIndex}
        t={t}
      />
    ));
  }, [slides, slideId, isMobile, selectedIndex, t]);

  return (
    <section
      className="embla"
      id={slideId}
      style={{
        // Reserve space to prevent layout shift
        minHeight: isMobile ? "220px" : "514px",
        position: "relative",
      }}
    >
      <div className="embla__viewport" ref={emblaRef}>
        <div className="embla__container">{memoizedSlides}</div>

        {!!slides.length && carouselReady && (
          <Container className="embla__controls">
            <div className="embla__buttons">
              <PrevButton
                onClick={onPrevButtonClick}
                disabled={prevBtnDisabled}
                aria-label="Previous slide"
              >
                <SvgArrowLeft />
              </PrevButton>
              <NextButton
                onClick={onNextButtonClick}
                disabled={nextBtnDisabled}
                aria-label="Next slide"
              >
                <SvgArrowRight />
              </NextButton>
            </div>
          </Container>
        )}
      </div>

      {/* Loading skeleton for initial render */}
      {!carouselReady && (
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            zIndex: 1,
          }}
        >
          <Skeleton
            variant="rectangular"
            width="100%"
            height="100%"
            animation="wave"
          />
        </div>
      )}
    </section>
  );
}

export default CustomEmblaCarousel;
