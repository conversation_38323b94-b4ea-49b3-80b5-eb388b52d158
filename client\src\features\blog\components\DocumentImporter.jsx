import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import mammoth from "mammoth";
import {
  Box,
  Typography,
  LinearProgress,
  Alert,
  FormLabel,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import upload from "@/assets/images/add.png";

// Inject drag state styles
const dragActiveStyles = `
  .file-labels.drag-active {
    border-color: #1976d2 !important;
    background-color: rgba(25, 118, 210, 0.04) !important;
  }
  .file-labels.disabled {
    cursor: not-allowed !important;
    opacity: 0.6 !important;
  }
`;
if (
  typeof document !== "undefined" &&
  !document.getElementById("document-importer-styles")
) {
  const styleSheet = document.createElement("style");
  styleSheet.id = "document-importer-styles";
  styleSheet.textContent = dragActiveStyles;
  document.head.appendChild(styleSheet);
}

const DocumentImporter = ({
  onContentExtracted,
  onMetadataExtracted,
  language = "EN",
  disabled = false,
}) => {
  const { t } = useTranslation();
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const acceptedFileTypes = {
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [
      ".docx",
    ],
    "application/msword": [".doc"],
    "application/pdf": [".pdf"],
    "text/plain": [".txt"],
  };

  const extractMetadataFromContent = (htmlContent) => {
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = htmlContent;

    const headings = tempDiv.querySelectorAll("h1, h2, h3, strong");
    const potentialTitle =
      headings.length > 0 ? headings[0].textContent.trim() : "";
    const paragraphs = tempDiv.querySelectorAll("p");
    const potentialDescription =
      paragraphs.length > 0
        ? paragraphs[0].textContent.trim().substring(0, 160)
        : "";

    const keywords = Array.from(headings)
      .map((h) => h.textContent.trim())
      .filter((text) => text.length > 2 && text.length < 50)
      .slice(0, 10);

    return {
      title: potentialTitle,
      description: potentialDescription,
      keywords,
    };
  };

  const processWordDocument = async (file) => {
    try {
      setProgress(25);
      const arrayBuffer = await file.arrayBuffer();

      setProgress(50);
      const result = await mammoth.convertToHtml({
        arrayBuffer,
        options: {
          styleMap: [
            "p[style-name='Heading 1'] => h1:fresh",
            "p[style-name='Heading 2'] => h2:fresh",
            "p[style-name='Heading 3'] => h3:fresh",
            "p[style-name='Title'] => h1.title:fresh",
            "p[style-name='Subtitle'] => h2.subtitle:fresh",
          ],
          includeDefaultStyleMap: true,
          convertImage: mammoth.images.imgElement((image) =>
            image.read("base64").then((imageBuffer) => ({
              src: `data:${image.contentType};base64,${imageBuffer}`,
            }))
          ),
        },
      });

      setProgress(75);
      const cleanContent = result.value
        .replace(/<p><\/p>/g, "")
        .replace(/\s+/g, " ")
        .trim();
      const metadata = extractMetadataFromContent(cleanContent);
      setProgress(100);

      return {
        content: cleanContent,
        metadata,
        warnings: result.messages || [],
      };
    } catch (error) {
      throw new Error(`Failed to process Word document: ${error.message}`);
    }
  };

  const processTextFile = async (file) => {
    try {
      setProgress(50);
      const text = await file.text();
      const htmlContent = text
        .split("\n")
        .map((line) => line.trim())
        .filter((line) => line.length > 0)
        .map((line) => `<p>${line}</p>`)
        .join("");

      const metadata = extractMetadataFromContent(htmlContent);
      setProgress(100);
      return { content: htmlContent, metadata, warnings: [] };
    } catch (error) {
      throw new Error(`Failed to process text file: ${error.message}`);
    }
  };

  const processFile = async (file) => {
    setIsProcessing(true);
    setProgress(0);
    setError(null);
    setSuccess(false);

    try {
      let result;

      if (
        file.type.includes("wordprocessingml") ||
        file.type.includes("msword")
      ) {
        result = await processWordDocument(file);
      } else if (file.type === "text/plain") {
        result = await processTextFile(file);
      } else {
        throw new Error("Unsupported file type");
      }

      // ✅ Directly apply the extracted content and metadata
      onContentExtracted(result.content);
      if (onMetadataExtracted) {
        onMetadataExtracted(result.metadata);
      }

      setSuccess(true);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  };

  const onDrop = useCallback((acceptedFiles) => {
    if (acceptedFiles.length > 0) processFile(acceptedFiles[0]);
  }, []);

  const { getRootProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes,
    maxFiles: 1,
    disabled: disabled || isProcessing,
    noClick: true,
    noKeyboard: true,
  });

  const handleFileChange = (event) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      processFile(files[0]);
    }
    event.target.value = "";
  };

  return (
    <Box sx={{ mb: 3, mt: 1 }}>
      <FormLabel className="label-form">
        {t("createArticle:content")} ({language})
      </FormLabel>
      <div className="upload-container">
        <label
          {...getRootProps()}
          className={`file-labels ${isDragActive ? "drag-active" : ""} ${
            disabled || isProcessing ? "disabled" : ""
          }`}
          style={{
            opacity: disabled || isProcessing ? 0.6 : 1,
            cursor: disabled || isProcessing ? "not-allowed" : "pointer",
          }}
        >
          <input
            type="file"
            accept=".docx,.doc,.txt"
            onChange={handleFileChange}
            className="file-input"
            disabled={disabled || isProcessing}
          />
          <div className="upload-area">
            <div
              className="icon-pic"
              style={{
                backgroundImage: `url("${upload}")`,
                backgroundSize: "cover",
                backgroundRepeat: "no-repeat",
                backgroundPosition: "center",
              }}
            ></div>
            <div>
              <p className="upload-text">
                {isDragActive
                  ? t("createArticle:dropFileHere")
                  : t("createArticle:importFromDocument")}
              </p>
              <p className="upload-description">
                {t("createArticle:supportedFormats")}: .docx, .doc, .txt
              </p>
            </div>
          </div>
        </label>
      </div>

      {isProcessing && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2">
            {t("createArticle:processingDocument")}...
          </Typography>
          <LinearProgress variant="determinate" value={progress} />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mt: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert
          severity="success"
          sx={{ mt: 2 }}
          onClose={() => setSuccess(false)}
        >
          {t("createArticle:documentProcessedSuccessfully")}
        </Alert>
      )}
    </Box>
  );
};

export default DocumentImporter;
