import { useState, useCallback, useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";

const useOpportunityFilters = (
  fetchOpportunities,
  language,
  initialListView
) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const searchParamsContent = searchParams?.toString() || "";
  const searchQueryParams = new URLSearchParams(searchParamsContent);

  const [keyWord, setKeyWord] = useState(searchParams?.get("keyWord") || "");
  const [country, setCountry] = useState(searchParams?.get("country") || "");
  const [pageNumber, setPageNumber] = useState(
    parseInt(searchParams?.get("pageNumber") || "1", 10)
  );
  const [industry, setIndustry] = useState(
    searchParams?.get("industry")?.split(",") || []
  );
  const [contractType, setContractType] = useState(
    searchParams?.get("contractType")?.split(",") || []
  );
  const [levelOfExperience, setLevelOfExperience] = useState(
    searchParams?.get("levelOfExperience")?.split(",") || []
  );
  const [jobDescriptionLanguages, setJobDescriptionLanguages] = useState(
    searchParams?.get("jobDescriptionLanguages")?.split(",") || []
  );
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [isFilterOpen, setIsFilterOpen] = useState(true);

  const [isList, setIsList] = useState(
    initialListView !== undefined
      ? initialListView
      : searchParams?.get("list") === "Yes"
  );

  useEffect(() => {
    const filtersArray = [];

    if (searchParams?.get("industry")) {
      const values = searchParams.get("industry").split(",");
      filtersArray.push(
        ...values.map((item) => ({ category: "industry", label: item.trim() }))
      );
    }

    if (searchParams?.get("contractType")) {
      const values = searchParams.get("contractType").split(",");
      filtersArray.push(
        ...values.map((item) => ({
          category: "contractType",
          label: item.trim(),
        }))
      );
    }

    if (searchParams?.get("levelOfExperience")) {
      const values = searchParams.get("levelOfExperience").split(",");
      filtersArray.push(
        ...values.map((item) => ({
          category: "levelOfExperience",
          label: item.trim(),
        }))
      );
    }

    if (searchParams?.get("jobDescriptionLanguages")) {
      const values = searchParams.get("jobDescriptionLanguages").split(",");
      filtersArray.push(
        ...values.map((item) => ({
          category: "jobDescriptionLanguages",
          label: item.trim(),
        }))
      );
    }

    if (searchParams?.get("country")) {
      const values = searchParams.get("country").split(",");
      filtersArray.push(
        ...values.map((item) => ({ category: "country", label: item.trim() }))
      );
    }

    if (searchParams?.get("keyWord")) {
      const values = searchParams.get("keyWord").split(",");
      filtersArray.push(
        ...values.map((item) => ({ category: "keyWord", label: item.trim() }))
      );
    }

    setSelectedFilters(filtersArray);
  }, [searchParams]);

  const updateUrlWithParams = useCallback(
    (scrollPosition) => {
      const currentScrollPosition =
        scrollPosition || window.scrollY || document.documentElement.scrollTop;

      window._isSearching = true;

      if (industry && industry.length > 0) {
        searchQueryParams.set(
          "industry",
          Array.isArray(industry) ? industry.join(",") : industry
        );
      }
      if (contractType && contractType.length > 0) {
        searchQueryParams.set(
          "contractType",
          Array.isArray(contractType) ? contractType.join(",") : contractType
        );
      }
      if (keyWord) {
        searchQueryParams.set("keyWord", keyWord);
      }
      if (country) {
        searchQueryParams.set(
          "country",
          Array.isArray(country) ? country.join(",") : country
        );
      }
      searchQueryParams.set("pageNumber", pageNumber || 1);

      if (isList) {
        searchQueryParams.set("list", "Yes");
      } else {
        searchQueryParams.delete("list");
      }

      if (jobDescriptionLanguages && jobDescriptionLanguages.length > 0) {
        searchQueryParams.set(
          "jobDescriptionLanguages",
          Array.isArray(jobDescriptionLanguages)
            ? jobDescriptionLanguages.join(",")
            : jobDescriptionLanguages
        );
      }

      if (levelOfExperience && levelOfExperience.length > 0) {
        searchQueryParams.set(
          "levelOfExperience",
          Array.isArray(levelOfExperience)
            ? levelOfExperience.join(",")
            : levelOfExperience
        );
      }

      const newUrl = `${pathname}?${searchQueryParams.toString()}`;
      window.history.replaceState({ path: newUrl }, "", newUrl);

      const params = {};
      for (const [key, value] of searchQueryParams.entries()) {
        params[key] = value;
      }

      params.language = language;

      fetchOpportunities(params);

      window.scrollTo({
        top: currentScrollPosition,
        behavior: "instant",
      });

      window._isSearching = false;
    },
    [
      industry,
      contractType,
      keyWord,
      country,
      pageNumber,
      jobDescriptionLanguages,
      levelOfExperience,
      searchQueryParams,
      pathname,
      fetchOpportunities,
      language,
    ]
  );

  const handleSearchChange = useCallback((e) => {
    const newValue = e.target.value;
    setKeyWord(newValue);
  }, []);

  const handleSearchClick = useCallback(
    (e) => {
      if (e && e.preventDefault) {
        e.preventDefault();
      }

      const scrollPosition =
        window.scrollY || document.documentElement.scrollTop;

      if (keyWord === "") {
        searchQueryParams.delete("keyWord");
      } else if (keyWord) {
        searchQueryParams.set("keyWord", keyWord);
      }

      if (!country || country.length === 0) {
        searchQueryParams.delete("country");
      } else if (country) {
        searchQueryParams.set(
          "country",
          Array.isArray(country) ? country.join(",") : country
        );
      }

      updateUrlWithParams(scrollPosition);

      window.dispatchEvent(
        new CustomEvent("searchPerformed", {
          detail: {
            keyWord,
            country,
            industry,
            contractType,
            levelOfExperience,
            jobDescriptionLanguages,
            maintainScroll: true,
            scrollPosition: scrollPosition,
          },
        })
      );

      return false;
    },
    [
      updateUrlWithParams,
      keyWord,
      country,
      industry,
      contractType,
      levelOfExperience,
      jobDescriptionLanguages,
      searchQueryParams,
    ]
  );

  const handleKeyDown = useCallback((e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      return false;
    }
  }, []);

  const resetSearch = useCallback(
    (e, options = {}) => {
      if (e && e.preventDefault) {
        e.preventDefault();
      }

      const scrollPosition =
        window.scrollY || document.documentElement.scrollTop;

      window._isResetting = true;

      setKeyWord("");
      setPageNumber(1);

      if (!options.preserveCountry) {
        setCountry("");
      }

      if (!options.preserveIndustry) {
        setIndustry([]);
      }

      setContractType([]);
      setLevelOfExperience([]);
      setJobDescriptionLanguages([]);

      const cleanParams = new URLSearchParams();
      cleanParams.set("pageNumber", 1);

      if (isList) {
        cleanParams.set("list", "Yes");
      }

      if (options.preserveIndustry && industry && industry.length > 0) {
        cleanParams.set(
          "industry",
          Array.isArray(industry) ? industry.join(",") : industry
        );
      }

      if (options.preserveCountry && options.countryName) {
        cleanParams.set("country", options.countryName);
      }

      const newUrl = `${pathname}?${cleanParams.toString()}`;
      window.history.replaceState({ path: newUrl }, "", newUrl);

      const fetchParams = { pageNumber: 1, language };

      if (options.preserveIndustry) {
        if (options.industryName) {
          fetchParams.industry = options.industryName;
        } else if (industry && industry.length > 0) {
          fetchParams.industry = Array.isArray(industry)
            ? industry.join(",")
            : industry;
        }
      }

      if (options.preserveCountry && options.countryName) {
        fetchParams.country = options.countryName;
      }

      fetchOpportunities(fetchParams);

      let newSelectedFilters = [];
      if (options.preserveIndustry && selectedFilters.length > 0) {
        newSelectedFilters = selectedFilters.filter(
          (filter) => filter.category === "industry"
        );
      }
      setSelectedFilters(newSelectedFilters);

      window.dispatchEvent(
        new CustomEvent("filtersReset", {
          detail: {
            pageNumber: 1,
            maintainScroll: true,
            scrollPosition: scrollPosition,
            preserveIndustry: options.preserveIndustry,
            preserveCountry: options.preserveCountry,
          },
        })
      );

      window.scrollTo({
        top: scrollPosition,
        behavior: "instant",
      });

      window._isResetting = false;

      return false;
    },
    [
      setKeyWord,
      setPageNumber,
      setCountry,
      setIndustry,
      setContractType,
      setLevelOfExperience,
      setJobDescriptionLanguages,
      setSelectedFilters,
      pathname,
      fetchOpportunities,
      language,
      industry,
      selectedFilters,
    ]
  );

  const handlePageChange = useCallback(
    (page) => {
      setPageNumber(page);
      searchQueryParams.set("pageNumber", page);

      if (isList) {
        searchQueryParams.set("list", "Yes");
      } else {
        searchQueryParams.delete("list");
      }

      const newUrl = `${pathname}?${searchQueryParams.toString()}`;
      window.history.replaceState({ path: newUrl }, "", newUrl);
      const params = {};
      for (const [key, value] of searchQueryParams.entries()) {
        params[key] = value;
      }
      params.language = language;
      fetchOpportunities(params);
    },
    [searchQueryParams, pathname, fetchOpportunities, language]
  );

  const handleViewModeChange = useCallback(
    (newIsListValue) => {
      const scrollPosition =
        window.scrollY || document.documentElement.scrollTop;

      setIsList(newIsListValue);
      const currentParams = new URLSearchParams(window.location.search);
      const apiParams = {};

      for (const [key, value] of currentParams.entries()) {
        if (key !== "list") {
          apiParams[key] = value;
        }
      }

      if (newIsListValue) {
        currentParams.set("list", "Yes");
        apiParams.list = "Yes";
      } else {
        currentParams.delete("list");
      }

      apiParams.pageSize = 10;
      const newUrl = `${pathname}?${currentParams.toString()}`;
      window.history.replaceState({ path: newUrl }, "", newUrl);

      window.scrollTo({
        top: scrollPosition,
        behavior: "instant",
      });

      fetchOpportunities(apiParams);

      window.dispatchEvent(
        new CustomEvent("viewModeChanged", {
          detail: {
            isList: newIsListValue,
            params: apiParams,
            maintainScroll: true,
            scrollPosition: scrollPosition,
          },
        })
      );
    },
    [fetchOpportunities, pathname]
  );

  useEffect(() => {
    const isListInUrl = searchParams?.get("list") === "Yes";
    if (isListInUrl !== isList) {
      setIsList(isListInUrl);
    }
  }, [searchParams, isList]);

  return {
    keyWord,
    setKeyWord,
    country,
    setCountry,
    pageNumber,
    setPageNumber,
    industry,
    setIndustry,
    contractType,
    setContractType,
    levelOfExperience,
    setLevelOfExperience,
    jobDescriptionLanguages,
    setJobDescriptionLanguages,
    selectedFilters,
    setSelectedFilters,
    isFilterOpen,
    setIsFilterOpen,
    handleSearchChange,
    handleSearchClick,
    handleKeyDown,
    resetSearch,
    handlePageChange,
    updateUrlWithParams,
    searchQueryParams,
    searchParamsContent,
    isList,
    setIsList,
    handleViewModeChange,
  };
};

export default useOpportunityFilters;
