"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/features/stats/ResumesChart.jsx":
/*!*********************************************!*\
  !*** ./src/features/stats/ResumesChart.jsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ResumesChart; },\n/* harmony export */   valueFormatter: function() { return /* binding */ valueFormatter; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _stats_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stats.hooks */ \"(app-pages-browser)/./src/features/stats/stats.hooks.jsx\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon.svg\");\n/* harmony import */ var _components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../components/charts/CustomMultiBarchart */ \"(app-pages-browser)/./src/components/charts/CustomMultiBarchart.jsx\");\n/* harmony import */ var _components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../components/charts/CustomPieChart */ \"(app-pages-browser)/./src/components/charts/CustomPieChart.jsx\");\n/* harmony import */ var _blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../blog/hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _charts_CommentByCategory__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./charts/CommentByCategory */ \"(app-pages-browser)/./src/features/stats/charts/CommentByCategory.jsx\");\n/* harmony import */ var _charts_UsersActivities__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./charts/UsersActivities */ \"(app-pages-browser)/./src/features/stats/charts/UsersActivities.jsx\");\n/* harmony import */ var _charts_ApplicationsByStatus__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./charts/ApplicationsByStatus */ \"(app-pages-browser)/./src/features/stats/charts/ApplicationsByStatus.jsx\");\n/* harmony import */ var _charts_OpportunititesType__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./charts/OpportunititesType */ \"(app-pages-browser)/./src/features/stats/charts/OpportunititesType.jsx\");\n/* __next_internal_client_entry_do_not_use__ valueFormatter,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction valueFormatter(value) {\n    return `${value}`;\n}\nfunction ResumesChart() {\n    _s();\n    const getCategoriesLangEN = (0,_blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"en\");\n    const transformedCategories = getCategoriesLangEN?.data?.categories?.map((category)=>({\n            id: category.versionscategory[0]?.id,\n            name: category.versionscategory[0]?.name\n        })) || [];\n    /// filter user Activity ///\n    const [dateFromUser, setDateFromUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToUser, setDateToUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchUser, setSearchUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchActivity = ()=>{\n        setDateToActivity(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromActivity(\"2024-09-01\");\n        setSearchActivity(!searchActivity);\n    };\n    /// filter platform activity ///\n    const [dateFromPlatform, setDateFromPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToPlatform, setDateToPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchPlatform, setSearchPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchPlatform = ()=>{\n        setDateToPlatform(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromPlatform(\"2024-09-01\");\n        setSearchPlatform(!searchPlatform);\n    };\n    /// application filter pie chart ///\n    const [dateFromApplication, setDateFromApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToApplication, setDateToApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchApplication, setSearchApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchApplication = ()=>{\n        setDateToApplication(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromApplication(\"2024-09-01\");\n        setSearchApplication(!searchApplication);\n    };\n    /// article filter pie chart ///\n    const [dateFromArticle, setDateFromArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToArticle, setDateToArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchArticle, setSearchArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchArticles = ()=>{\n        setDateToArticle(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromArticle(\"2024-09-01\");\n        setFilteredCategories([]);\n        setApprove(true);\n        setCategories([]);\n        setSearchArticle(!searchArticle);\n    };\n    /// comment filter pie chart ///\n    const [filteredCategories, setFilteredCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [approve, setApprove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dateFromComment, setDateFromComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToComment, setDateToComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchComment, setSearchComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchComments = ()=>{\n        setDateToComment(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromComment(\"2024-09-01\");\n        setFilteredCategories([]);\n        setApprove(true);\n        setCategories([]);\n        setSearchComment(!searchComment);\n    };\n    /// opportunity filter pie chart ////\n    const [dateFromOpportunity, setDateFromOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToOpportunity, setDateToOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [opportunityType, setOpportunityType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [industry, setIndustry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchOpportunity, setSearchOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchOpportunity = ()=>{\n        setDateToOpportunity(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromOpportunity(\"2024-09-01\");\n        setOpportunityType(\"\");\n        setIndustry(\"\");\n        setSearchOpportunity(!searchOpportunity);\n    };\n    const getDataPieComments = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetCommentsStat)({\n        dateFrom: dateFromComment,\n        dateTo: dateToComment,\n        approve: approve,\n        categories: filteredCategories\n    });\n    const getDataPieArticles = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetArticlesStat)({\n        dateFrom: dateFromArticle,\n        dateTo: dateToArticle,\n        barChart: null\n    });\n    const getDataPieOpportunities = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetOpportunitiesStat)({\n        dateFrom: dateFromOpportunity,\n        dateTo: dateToOpportunity,\n        opportunityType,\n        industry,\n        barChart: null\n    });\n    const getDAtaPieApplications = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetApplicationsStat)({\n        dateFrom: dateFromApplication,\n        dateTo: dateToApplication,\n        barChart: null\n    });\n    const getDataUserActivity = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetUserStat)({\n        dateFrom: dateFromUser,\n        dateTo: dateToUser\n    });\n    const getDataPlatforActivity = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetPlatformStat)({\n        dateFrom: dateFromPlatform,\n        dateTo: dateToPlatform\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataUserActivity.refetch();\n    }, [\n        searchUser\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPlatforActivity.refetch();\n    }, [\n        searchPlatform\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieOpportunities.refetch();\n    }, [\n        searchOpportunity\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDAtaPieApplications.refetch();\n    }, [\n        searchApplication\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieArticles.refetch();\n    }, [\n        searchArticle\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieComments.refetch();\n    }, [\n        searchComment\n    ]);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const isTablet = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(theme.breakpoints.down(\"md\"));\n    const chartSettings1 = {\n        width: isMobile ? 290 : isTablet ? 500 : 580,\n        height: 250,\n        layout: \"vertical\"\n    };\n    if (getDataUserActivity.isLoading || getDataPlatforActivity.isLoading || getDataPieArticles.isLoading || getDataPieOpportunities.isLoading || getDAtaPieApplications.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n            lineNumber: 243,\n            columnNumber: 12\n        }, this);\n    }\n    const pieCharts = [\n        {\n            title: t(\"statsDash:applicationsByStatus\"),\n            dataset: getDAtaPieApplications?.data?.map((app)=>({\n                    label: app.status,\n                    value: app.totalApplications\n                })),\n            colors: [\n                \"#E97611\",\n                \"#018055\",\n                \"#D73232\"\n            ]\n        },\n        {\n            title: t(\"statsDash:articlesByVisibility\"),\n            dataset: getDataPieArticles?.data?.map((article)=>({\n                    label: article.visibility,\n                    value: article.totalArticles\n                })),\n            colors: [\n                \"#234791\",\n                \"#FFCA00\",\n                \"#006A67\"\n            ]\n        },\n        {\n            title: t(\"statsDash:opportunities\"),\n            dataset: getDataPieOpportunities?.data?.map((opp)=>({\n                    label: opp.visibility,\n                    value: opp.totalOpportunities\n                })),\n            colors: [\n                \"#234791\",\n                \"#D5E5FF\",\n                \"#227B94\"\n            ]\n        },\n        {\n            title: t(\"statsDash:commentsByCategory\"),\n            dataset: getDataPieComments?.data?.map((comment)=>({\n                    label: comment.category,\n                    value: comment.total\n                })) || [],\n            colors: [\n                \"#673ab7\",\n                \"#009688\",\n                \"#8bc34a\",\n                \"#ffc107\",\n                \"#ff9800\",\n                \"#ffc107\",\n                \"#3f51b5\",\n                \"#009688\",\n                \"#4caf50\",\n                \"#03a9f4\",\n                \"#ff9800\",\n                \"#8bc34a\",\n                \"#673ab7\"\n            ]\n        }\n    ];\n    const userAactivity = {\n        title: t(\"statsDash:usersActivities\"),\n        dataKey: [\n            \"login\",\n            \"register\",\n            \"resumes\",\n            \"applications\"\n        ],\n        dataset: getDataUserActivity?.data,\n        color: [\n            \"#30B0C7\",\n            \"#234791\",\n            \"#007AFF\",\n            \"#32ADE6\"\n        ]\n    };\n    const platformAactivity = {\n        title: t(\"statsDash:platformActivity\"),\n        dataKey: [\n            \"opportunities\",\n            \"articles\",\n            \"newsletters\",\n            \"contacts\"\n        ],\n        dataset: getDataPlatforActivity?.data,\n        color: [\n            \"#FFCC00\",\n            \"#FFA135\",\n            \"#FFD985\",\n            \"#FF7700\"\n        ]\n    };\n    console.log(pieCharts);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"menu:statistics\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"stats\",\n                className: \"div-wrapper\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    container: true,\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_CommentByCategory__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                transformedCategories: transformedCategories,\n                                dateFromComment: dateFromComment,\n                                dateToComment: dateToComment,\n                                approve: approve,\n                                categories: categories,\n                                setCategories: setCategories,\n                                setFilteredCategories: setFilteredCategories,\n                                setSearchComment: setSearchComment,\n                                searchComment: searchComment,\n                                resetSearchComments: resetSearchComments,\n                                pieCharts: pieCharts,\n                                setApprove: setApprove,\n                                setDateFromComment: setDateFromComment,\n                                setDateToComment: setDateToComment\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_UsersActivities__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                dateFromUser: dateFromUser,\n                                dateToUser: dateToUser,\n                                searchUser: searchUser,\n                                setSearchUser: setSearchUser,\n                                resetSearchActivity: resetSearchActivity,\n                                userAactivity: userAactivity,\n                                chartSettings1: chartSettings1,\n                                setDateFromUser: setDateFromUser,\n                                setDateToUser: setDateToUser\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_ApplicationsByStatus__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                dateFromApplication: dateFromApplication,\n                                dateToApplication: dateToApplication,\n                                searchApplication: searchApplication,\n                                setSearchApplication: setSearchApplication,\n                                resetSearchApplication: resetSearchApplication,\n                                pieCharts: pieCharts,\n                                setDateFromApplication: setDateFromApplication,\n                                setDateToApplication: setDateToApplication\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_OpportunititesType__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                dateFromOpportunity: dateFromOpportunity,\n                                dateToOpportunity: dateToOpportunity,\n                                searchOpportunity: searchOpportunity,\n                                setSearchOpportunity: setSearchOpportunity,\n                                resetSearchOpportunity: resetSearchOpportunity,\n                                pieCharts: pieCharts,\n                                opportunityType: opportunityType,\n                                setOpportunityType: setOpportunityType,\n                                industry: industry,\n                                setIndustry: setIndustry,\n                                setDateFromOpportunity: setDateFromOpportunity,\n                                setDateToOpportunity: setDateToOpportunity,\n                                Industry: _utils_constants__WEBPACK_IMPORTED_MODULE_4__.Industry,\n                                OpportunityType: _utils_constants__WEBPACK_IMPORTED_MODULE_4__.OpportunityType\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 360,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"barchartfilter-wrapper\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                container: true,\n                                                className: \"chart-grid\",\n                                                spacing: 1,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 12,\n                                                        md: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"heading-h3\",\n                                                            gutterBottom: true,\n                                                            children: platformAactivity.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 6,\n                                                        md: 2.5,\n                                                        xl: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            label: t(\"statsDash:fromDate\"),\n                                                            type: \"date\",\n                                                            value: dateFromPlatform,\n                                                            onChange: (e)=>setDateFromPlatform(e.target.value),\n                                                            fullWidth: true,\n                                                            InputLabelProps: {\n                                                                shrink: true\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 6,\n                                                        md: 2.5,\n                                                        xl: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            label: t(\"statsDash:toDate\"),\n                                                            type: \"date\",\n                                                            value: dateToPlatform,\n                                                            onChange: (e)=>setDateToPlatform(e.target.value),\n                                                            fullWidth: true,\n                                                            InputLabelProps: {\n                                                                shrink: true\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        item: true,\n                                                        xs: 2,\n                                                        sm: 1,\n                                                        md: 1.5,\n                                                        xl: 1,\n                                                        className: \"btns-filter dashboard\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            className: \"btn btn-outlined btn-refresh full-width\",\n                                                            onClick: resetSearchPlatform\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        item: true,\n                                                        xs: 10,\n                                                        sm: 11,\n                                                        md: 2.5,\n                                                        xl: 2,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            text: t(\"statsDash:filter\"),\n                                                            onClick: ()=>{\n                                                                setSearchPlatform(!searchPlatform);\n                                                            },\n                                                            className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                platformAactivity.dataset?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newopportunities-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newOpportunities\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"neswarticles-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newArticles\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newsletters-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newslettersSubscriptions\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newcontact-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newContacts\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    chart: platformAactivity,\n                                                    chartSettings: chartSettings1\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"heading-h3\",\n                                            gutterBottom: true,\n                                            children: pieCharts[1].title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            elevation: 0,\n                                            disableGutters: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    \"aria-controls\": \"panel1bh-content\",\n                                                    id: \"panel1bh-header\",\n                                                    className: \"svg-accordion\",\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 33\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"label-pentabell\",\n                                                        children: t(\"statsDash:filters\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    elevation: 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        container: true,\n                                                        className: \"chart-grid\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    label: t(\"statsDash:fromDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateFromArticle,\n                                                                    onChange: (e)=>setDateFromArticle(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    label: t(\"statsDash:toDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateToArticle,\n                                                                    onChange: (e)=>setDateToArticle(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                item: true,\n                                                                xs: 3,\n                                                                sm: 1,\n                                                                md: 4,\n                                                                className: \"btns-filter dashboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                                                    onClick: resetSearchArticles\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                item: true,\n                                                                xs: 11,\n                                                                sm: 11,\n                                                                md: 8,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    text: t(\"statsDash:filter\"),\n                                                                    onClick: ()=>{\n                                                                        setSearchArticle(!searchArticle);\n                                                                    },\n                                                                    className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    donuts: false,\n                                                    chart: pieCharts[1]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                pieCharts[1].dataset?.some((item)=>item[\"value\"] > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"public-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:public\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"privatearticles-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:private\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"draft-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:draft\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 474,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ResumesChart, \"5avvH6cEosRudI6naxYJ3BUU5WU=\", false, function() {\n    return [\n        _blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetCommentsStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetArticlesStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetOpportunitiesStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetApplicationsStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetUserStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetPlatformStat,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    ];\n});\n_c = ResumesChart;\nvar _c;\n$RefreshReg$(_c, \"ResumesChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/stats/ResumesChart.jsx\n"));

/***/ })

});