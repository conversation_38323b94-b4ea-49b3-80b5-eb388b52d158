(()=>{var e={};e.id=5732,e.ids=[5732],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},6005:e=>{"use strict";e.exports=require("node:crypto")},83969:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a,r=s(95746);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let l=e=>r.createElement("svg",n({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),a||(a=r.createElement("path",{fill:"#234791",d:"m16.172 11-5.364-5.364 1.414-1.414L20 12l-7.778 7.778-1.414-1.414L16.172 13H4v-2z"})))},10936:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a,r,n=s(95746);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let i=e=>n.createElement("svg",l({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,fill:"none"},e),a||(a=n.createElement("rect",{width:32,height:32,fill:"#699BD4",rx:2})),r||(r=n.createElement("path",{stroke:"#fff",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M26.5 21.42v3a2 2 0 0 1-2.18 2 19.8 19.8 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.8 19.8 0 0 1-3.07-8.67A2 2 0 0 1 8.61 6.5h3a2 2 0 0 1 2 1.72c.127.96.362 1.903.7 2.81a2 2 0 0 1-.45 2.11l-1.27 1.27a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45c.908.339 1.85.573 2.81.7a2 2 0 0 1 1.72 2.03"})))},82020:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a,r,n=s(95746);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let i=e=>n.createElement("svg",l({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,fill:"none"},e),a||(a=n.createElement("rect",{width:32,height:32,fill:"#699BD4",rx:2})),r||(r=n.createElement("path",{stroke:"#fff",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M26.5 10.5c0-1.1-.9-2-2-2h-16c-1.1 0-2 .9-2 2m20 0v12c0 1.1-.9 2-2 2h-16c-1.1 0-2-.9-2-2v-12m20 0-10 7-10-7"})))},48507:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a,r,n,l=s(95746);function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let o=e=>l.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,fill:"none"},e),a||(a=l.createElement("rect",{width:32,height:32,fill:"#699BD4",rx:2})),r||(r=l.createElement("g",{stroke:"#fff",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,clipPath:"url(#locationPin_svg__a)"},l.createElement("path",{d:"M25.5 14.5c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0"}),l.createElement("path",{d:"M16.5 17.5a3 3 0 1 0 0-6 3 3 0 0 0 0 6"}))),n||(n=l.createElement("defs",null,l.createElement("clipPath",{id:"locationPin_svg__a"},l.createElement("path",{fill:"#fff",d:"M4.5 4.5h24v24h-24z"})))))},77394:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var a,r,n,l=s(95746);function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let o=e=>l.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:48,height:49,fill:"none"},e),a||(a=l.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M24 25a1.5 1.5 0 0 1 1.5 1.5v16a1.5 1.5 0 0 1-3 0v-16A1.5 1.5 0 0 1 24 25",clipRule:"evenodd"})),r||(r=l.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M22.573 8.869A12.5 12.5 0 0 0 9.072 29.247a1.5 1.5 0 1 1-2.144 2.099 15.5 15.5 0 1 1 25.566-16.347H35a10.501 10.501 0 0 1 5.834 19.23 1.5 1.5 0 1 1-1.666-2.494A7.5 7.5 0 0 0 35 18h-3.58a1.5 1.5 0 0 1-1.438-1.071 12.5 12.5 0 0 0-7.409-8.06",clipRule:"evenodd"})),n||(n=l.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M22.94 25.44a1.5 1.5 0 0 1 2.12 0l8 8a1.5 1.5 0 0 1-2.12 2.12L24 28.622l-6.94 6.94a1.5 1.5 0 0 1-2.12-2.122z",clipRule:"evenodd"})))},44294:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>m,tree:()=>c}),s(41429),s(30962),s(23658),s(54864);var a=s(23191),r=s(88716),n=s(37922),l=s.n(n),i=s(95231),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let c=["",{children:["[locale]",{children:["(website)",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,41429)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\contact\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,30962)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\layout.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,23658)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54864)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\not-found.js"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\contact\\page.jsx"],d="/[locale]/(website)/contact/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/[locale]/(website)/contact/page",pathname:"/[locale]/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},23749:(e,t,s)=>{Promise.resolve().then(s.bind(s,90394)),Promise.resolve().then(s.bind(s,66964)),Promise.resolve().then(s.bind(s,9435))},47463:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c});var a=s(6005),r=s.n(a);let n={randomUUID:r().randomUUID},l=new Uint8Array(256),i=l.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let c=function(e,t,s){if(n.randomUUID&&!t&&!e)return n.randomUUID();let a=(e=e||{}).random||(e.rng||function(){return i>l.length-16&&(r().randomFillSync(l),i=0),l.slice(i,i+=16)})();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){s=s||0;for(let e=0;e<16;++e)t[s+e]=a[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(a)}},90394:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var a=s(10326),r=s(90423),n=s(48507),l=s(10936),i=s(82020),o=s(52210);let c=function(){let{t:e}=(0,o.$G)();return(0,a.jsxs)(r.default,{id:"locations-section",className:"custom-max-width",children:[a.jsx("h2",{className:"heading-h1",children:e("contactUs:locations:title")}),a.jsx("p",{className:"sub-heading",children:e("contactUs:locations:description")}),(0,a.jsxs)("div",{className:"locations",children:[(0,a.jsxs)("div",{className:"location-item",children:[(0,a.jsxs)("p",{className:"label",children:[" ",a.jsx(n.default,{})," ",e("contactUs:locations:address")]}),a.jsx("p",{className:"value paragraph",children:e("contactUs:locations:addressDescription")})]}),(0,a.jsxs)("div",{className:"location-item",children:[(0,a.jsxs)("p",{className:"label",children:[" ",a.jsx(l.default,{})," ",e("contactUs:locations:call")]}),a.jsx("p",{className:"value paragraph",children:"+33 1 73 07 42 54"})]}),(0,a.jsxs)("div",{className:"location-item",children:[(0,a.jsxs)("p",{className:"label",children:[" ",a.jsx(i.default,{})," Email"]}),a.jsx("p",{className:"value paragraph",children:"<EMAIL>"})]})]})]})}},5926:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var a=s(10326),r=s(17577);s(11148);let n=({src:e,alt:t})=>{let[s,n]=(0,r.useState)(!1),l=(0,r.useRef)();return(0,r.useEffect)(()=>{let e=new IntersectionObserver(([t])=>{t.isIntersecting&&(n(!0),e.unobserve(t.target))},{threshold:.1});return l.current&&e.observe(l.current),()=>e.disconnect()},[]),a.jsx("img",{ref:l,src:s?e:void 0,"data-src":e,alt:t,loading:"lazy",style:{opacity:s?1:.5,transition:"opacity 0.3s"}})}},87419:(e,t,s)=>{"use strict";s.d(t,{uu:()=>c,eo:()=>d,cl:()=>u});var a=s(2994),r=s(70580),n=s(50967);let l=async(e,t,s)=>new Promise(async(a,l)=>{r.xk.post(`${n.Y.contact}`,e).then(e=>{t("Submitted."),s(""),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(500===e.response.status?(t(!1),s("Internal error server")):(t(!1),s(e.response.data.message))),e&&l(e)})}),i=e=>new Promise(async(t,s)=>{try{let s=await r.yX.get(`${n.Y.contact}`,{params:{paginated:e.paginated,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyword:e.keyword,createdAt:e.createdAt,type:e.type,email:e.email}});t(s.data)}catch(e){s(e)}}),o=e=>new Promise(async(t,s)=>{try{let s=await r.yX.get(`${n.Y.contact}/${e}`);t(s.data)}catch(e){s(e)}}),c=(e,t)=>{let s=(0,a.useQueryClient)();return(0,a.useMutation)({mutationFn:s=>l(s,e,t),onSuccess:e=>{s.invalidateQueries("user")},onError:e=>{e.message=""}})},u=e=>(0,a.useQuery)("contact",async()=>await i(e)),d=e=>(0,a.useQuery)(["contact",e],async()=>await o(e))},9435:(e,t,s)=>{"use strict";s.d(t,{default:()=>S});var a=s(10326),r=s(90423),n=s(84648),l=s(9861),i=s(63568),o=s(16027),c=s(87638),u=s(90943),d=s(78077),p=s(5394),m=s(76971),h=s(9252);s(11148);var g=s(10123),f=s(96672),x=s(91288),y=s(15082),v=s(17577),j=s(52210),b=s(87419),w=s(4563),N=s(55618),C=s(5248),P=s(18970),E=s(26066),A=s(77394),_=s(86184),Z=s(47463),I=s(5926);let S=function(){let e,t;let[s,S]=(0,v.useState)(""),[T,U]=(0,v.useState)(!1),$=h.PhoneNumberUtil.getInstance(),[O,k]=(0,v.useState)(!1),[L,q]=(0,v.useState)(null),[F,M]=(0,v.useState)(""),{t:D}=(0,j.$G)(),[B,z]=(0,v.useState)(""),R=(0,b.uu)(U,S),X=(0,_.jd)(),Y=new FormData,Q=(s,a)=>{s.preventDefault(),e=(0,Z.Z)().replace(/-/g,""),M(""),q(null);let r=s.target.files[0];if(r){Y.append("file",r);let s=r.name.split(".").pop();t=`${e}.${s}`;let n=new Date().getFullYear();X.mutate({resource:"candidates",folder:n,filename:e,body:{formData:Y,t:D}},{onSuccess:e=>{"uuid exist"===e.message?(q(e.uuid),a("resume",e.uuid)):(q(t),a("resume",t))},onError:e=>{400===e.response.data.status?M(D("messages:requireResume")):500===e.response.data.status?M("Internal Server Error"):M(e.response.data.message)}})}},H=async(e,{resetForm:t})=>{let s={...Object.fromEntries(Object.entries(e).filter(([e,t])=>"acceptTerms"!==e&&""!==t&&null!=t))};await R.mutateAsync({...s,to:`${process.env.NEXT_PUBLIC_EMAIL_FORM_DESTINATION}`,team:"digital",type:"getInTouchContact"}),z(e.fullName),t(),q(null),window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"Contact_form",button_id:"contact_form_button"})},G=e=>{try{return $.isValidNumber($.parseAndKeepRawInput(e))}catch(e){return!1}},W=g.Z_().test("is-valid-phone",D("validations:phoneFormat"),e=>G(e)),J=e=>(0,w.ie)(D).shape({phone:W,email:g.Z_().email(D("validations:invalidEmail")).required(D("validations:required")).test("is-company-email",D("validations:companyEmailRequired"),function(e){let{youAre:t}=this.parent;return!!e&&("Company"!==t||E.isCompanyEmail(e))})});return(0,a.jsxs)("div",{id:"contact-page-form",children:[a.jsx(P.Z,{}),a.jsx(r.default,{className:"custom-max-width",children:T?a.jsx("div",{className:"section-guide",children:(0,a.jsxs)("div",{className:"form-success",children:[a.jsx(x.Z,{}),(0,a.jsxs)("p",{className:"sub-heading",children:[" ",D("messages:thankyou")," ",B," ",D("messages:messagesuccess")]})]})}):(0,a.jsxs)(a.Fragment,{children:[a.jsx("h2",{className:"heading-h1",children:D("getInTouch:getInTouch")}),a.jsx("p",{className:"sub-heading",children:D("getInTouch:description")}),a.jsx(i.J9,{initialValues:{fullName:"",email:"",phone:"",youAre:"",subject:"",message:"",country:"",resume:"",field:"",acceptTerms:!1},validationSchema:()=>J(O),onSubmit:H,children:({values:e,handleChange:t,errors:s,touched:r,setFieldValue:h})=>a.jsx(i.l0,{className:"pentabell-form",children:(0,a.jsxs)(o.default,{container:!0,rowSpacing:4,columnSpacing:3,children:[(0,a.jsxs)(o.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(c.Z,{className:"form-group ",children:[(0,a.jsxs)(u.Z,{className:"label-pentabell ",children:[D("getInTouch:fullName"),"*"]}),a.jsx(d.Z,{autoComplete:"off",className:"input-pentabell ",placeholder:D("getInTouch:fullName"),variant:"standard",type:"text",name:"fullName",value:e.fullName,onChange:t,error:!!(s.fullName&&r.fullName)})]}),a.jsx(i.Bc,{name:"fullName",children:e=>a.jsx("span",{className:"error-span",children:e})})]}),(0,a.jsxs)(o.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(c.Z,{className:"form-group ",children:[(0,a.jsxs)(u.Z,{className:"label-pentabell ",children:[D("getInTouch:email"),"*"]}),a.jsx(d.Z,{autoComplete:"off",className:"input-pentabell ",placeholder:D("getInTouch:email"),variant:"standard",type:"email",name:"email",value:e.email,onChange:t,error:!!(s.email&&r.email)})]}),a.jsx(i.Bc,{name:"email",children:e=>a.jsx("span",{className:"error-span",children:e})})]}),(0,a.jsxs)(o.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(c.Z,{className:"form-group ",children:[a.jsx(u.Z,{className:"label-pentabell ",children:D("getInTouch:phone")}),a.jsx(f.sb,{defaultCountry:"fr",className:"input-pentabell",value:e.phone,onChange:e=>{h("phone",e),S("")},flagComponent:e=>a.jsx(I.Z,{...e})})]}),a.jsx(i.Bc,{name:"phone",children:e=>a.jsx("span",{className:"error-span",children:e})})]}),(0,a.jsxs)(o.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(c.Z,{className:"form-group",children:[(0,a.jsxs)(u.Z,{className:"label-pentabell",children:[D("getInTouch:youAre"),"*"]}),a.jsx(n.Z,{className:"input-pentabell",id:"tags-standard",options:["Consultant","Company"],getOptionLabel:e=>e,name:"youAre",value:e.youAre,onChange:(e,t)=>{h("youAre",t),k("Company"===t)},renderInput:e=>a.jsx(d.Z,{...e,className:"input-pentabell multiple-select",variant:"standard",placeholder:D("aiSourcingService:servicePageForm:chooseOne"),error:!!(s.youAre&&r.youAre)})})]}),a.jsx(i.Bc,{name:"youAre",children:e=>a.jsx("span",{className:"error-span",children:e})})]}),(0,a.jsxs)(o.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(c.Z,{className:"form-group ",children:[a.jsx(u.Z,{className:"label-pentabell ",children:D("getInTouch:subject")}),a.jsx(d.Z,{autoComplete:"off",className:"input-pentabell ",placeholder:D("getInTouch:subject"),variant:"standard",type:"text",name:"subject",value:e.subject,onChange:t,error:!!(s.subject&&r.subject)})]}),a.jsx(i.Bc,{name:"subject",children:e=>a.jsx("span",{className:"error-span",children:e})})]}),(0,a.jsxs)(o.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(c.Z,{className:"form-group ",children:[a.jsx(u.Z,{className:"label-pentabell ",children:D("getInTouch:countryName")}),a.jsx(n.Z,{className:"input-pentabell",id:"tags-standard",options:C.nh,getOptionLabel:e=>e,name:"country",value:e.country,onChange:(e,t)=>h("country",t),renderInput:e=>a.jsx(d.Z,{...e,className:"input-pentabell multiple-select",variant:"standard",placeholder:"Choose country",error:!!(s.country&&r.country)})})]}),a.jsx(i.Bc,{name:"country",children:e=>a.jsx("span",{className:"error-span",children:e})})]}),"Consultant"===e.youAre&&(0,a.jsxs)(o.default,{item:!0,xs:12,sm:4,children:[(0,a.jsxs)(c.Z,{className:"form-group",children:[(0,a.jsxs)(u.Z,{className:"label-pentabell",children:[D("consultingServices:servicePageForm:industry"),"*"]}),a.jsx(n.Z,{className:"input-pentabell",id:"tags-standard",options:Object.values(C.b5),getOptionLabel:e=>e,name:"field",value:e.field,onChange:(e,t)=>{h("field",t)},renderInput:e=>a.jsx(d.Z,{...e,className:"input-pentabell multiple-select",variant:"standard",placeholder:D("consultingServices:servicePageForm:chooseOne"),error:!!(s.field&&r.field)})})]}),a.jsx(i.Bc,{name:"field",children:e=>a.jsx("span",{className:"error-span",children:e})})]}),(0,a.jsxs)(o.default,{item:!0,xs:12,sm:"Consultant"===e.youAre?8:12,children:[(0,a.jsxs)(c.Z,{className:"form-group ",children:[(0,a.jsxs)(u.Z,{className:"label-pentabell ",children:[D("getInTouch:message"),"*"]}),a.jsx(d.Z,{autoComplete:"off",className:"input-pentabell ",placeholder:D("getInTouch:typeMessage"),variant:"standard",type:"text",name:"message",value:e.message,onChange:t,error:!!(s.message&&r.message)})]}),a.jsx(i.Bc,{name:"message",children:e=>a.jsx("span",{className:"error-span",children:e})})]}),"Consultant"===e.youAre&&(0,a.jsxs)(o.default,{item:!0,xs:12,sm:12,children:[a.jsx(c.Z,{className:"form-group light form-section",children:(0,a.jsxs)("div",{className:"custom-file-upload-wbg",onClick:()=>document.getElementById("file-upload").click(),children:[(0,a.jsxs)("div",{children:[a.jsx(A.Z,{}),L?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(u.Z,{className:"label-pentabell light",children:[D("joinUs:form:uploadCv"),"*"]}),a.jsx("p",{className:"sub-label",children:L})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(u.Z,{className:"label-pentabell light",children:D("joinUs:form:uploadCv")}),a.jsx("p",{className:"sub-label",children:D("joinUs:form:control")})]}),a.jsx(y.default,{text:"Choose a file",className:"btn btn-outlined white"}),F&&a.jsx(l.Z,{variant:"filled",severity:"error",children:F})]}),a.jsx("input",{id:"file-upload",type:"file",name:"resume",accept:"application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword",style:{display:"none"},onChange:e=>{Q(e,h)},error:!!(s.resume&&r.resume)})]})}),a.jsx(i.Bc,{name:"resume",children:e=>a.jsx(l.Z,{variant:"filled",severity:"error",children:e})})]}),a.jsx(o.default,{item:!0,xs:12,sm:8,children:(0,a.jsxs)(c.Z,{children:[a.jsx(p.Z,{className:"checkbox-pentabell ",control:a.jsx(m.Z,{name:"acceptTerms",checked:e.acceptTerms,onChange:t,error:!!(s.acceptTerms&&r.acceptTerms)}),label:D("aiSourcingService:servicePageForm:formSubmissionAgreement")}),a.jsx(i.Bc,{name:"acceptTerms",children:e=>a.jsx("span",{className:"error-span",children:e})})]})}),a.jsx(o.default,{item:!0,xs:12,sm:4,className:"flex-end",children:a.jsx(y.default,{text:D("getInTouch:submit"),className:"btn btn-filled btn-submit",type:"submit"})})]})})}),a.jsx(N.Z,{errMsg:s,success:T})]})})]})}},86184:(e,t,s)=>{"use strict";s.d(t,{$i:()=>g,BF:()=>h,Fe:()=>l,Gc:()=>u,HF:()=>n,Hr:()=>o,IZ:()=>m,NF:()=>c,PM:()=>i,UJ:()=>d,jd:()=>p});var a=s(2994),r=s(21464);s(35047),s(97980);let n=()=>(0,a.useMutation)({mutationFn:e=>(0,r.W3)(e),onError:e=>{e.message=""}}),l=e=>(0,a.useQuery)("opportunities",async()=>await (0,r.fH)(e)),i=()=>(0,a.useMutation)(()=>(0,r.AE)()),o=e=>(0,a.useQuery)(["opportunities",e],async()=>await (0,r.Mq)(e)),c=()=>(0,a.useMutation)({mutationFn:(e,t,s)=>(0,r.rE)(e,t,s),onError:e=>{e.message=""}}),u=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t)=>(0,r.S1)(e,t),onError:e=>{e.message=""}})),d=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t,s)=>(0,r.lU)(e,t,s),onError:e=>{e.message=""}})),p=()=>{let e=(0,a.useQueryClient)();return(0,a.useMutation)({mutationFn:(e,t,s,a)=>(0,r.yH)(e,t,s,a),onSuccess:t=>{e.invalidateQueries("files")}})},m=()=>(0,a.useQuery)("SeoOpportunities",async()=>await (0,r.yJ)()),h=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t)=>(0,r.mt)(e,t),onError:e=>{e.message=""}})),g=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:({language:e,id:t,archive:s})=>(0,r.TK)(e,t,s),onError:e=>{console.error("Error during mutation",e),e.message=""}}))},21464:(e,t,s)=>{"use strict";s.d(t,{AE:()=>c,Mq:()=>o,S1:()=>d,TK:()=>g,W3:()=>l,fH:()=>i,lU:()=>p,mt:()=>f,rE:()=>u,yH:()=>m,yJ:()=>h});var a=s(50967),r=s(70580),n=s(31190);let l=e=>(e.t,new Promise(async(t,s)=>{r.yX.post(`/opportunities${a.Y.applications}/${e.opportunityId}`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&s(e)})})),i=e=>new Promise(async(t,s)=>{try{let s=await r.yX.get(`${a.Y.opportunity}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyWord:e.keyWord,visibility:e.visibility,title:e.title,createdAt:e.createdAt,publishDate:e.publishDate,country:e.country,industry:e.industry,contractType:e.contractType,minExperience:e.minExperience,maxExperience:e.maxExperience,jobDescriptionLanguages:e.jobDescriptionLanguages,reference:e.reference,opportunityType:e.opportunityType,exclude:"In House"!==e.opportunityType&&"true"}});t(s.data)}catch(e){s(e)}}),o=e=>new Promise(async(t,s)=>{try{let s=await r.yX.get(`${a.Y.opportunity}/${e}`);t(s.data)}catch(e){s(e)}}),c=async()=>(await r.xk.put("/UpdateJobdescription")).data,u=({data:e,language:t,id:s})=>new Promise(async(l,i)=>{r.yX.post(`${a.Y.opportunity}/${t}/${s}`,e).then(e=>{"en"===t&&n.Am.success("Opportunity english updated successfully"),"fr"===t&&n.Am.success("Opportunity french updated successfully"),e?.data&&l(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})}),d=({data:e,id:t})=>new Promise(async(s,l)=>{r.yX.put(`${a.Y.opportunity}/${t}`,e).then(e=>{n.Am.success("Opportunity Commun fields updated successfully"),e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&l(e)})}),p=({id:e,title:t,typeOfFavourite:s})=>new Promise(async(l,i)=>{r.yX.put(`${a.Y.baseUrl}/favourite/${e}`,{type:s}).then(e=>{n.Am.success(`${s} : ${t} saved to your favorites.`),e?.data&&l(e.data)}).catch(e=>{e&&e.response&&e.response.data&&409===e.response.status&&n.Am.warning(` ${t} already in shortlist`),e&&i(e)})}),m=({resource:e,folder:t,filename:s,body:l})=>new Promise(async(i,o)=>{r.cU.post(`${a.Y.files}/uploadResume/${e}/${t}/${s}`,l.formData).then(e=>{e.data&&i(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(400===e.response.status?e.response.data.message.includes("The resume lacks essential information")?n.Am.warn(l.t("messages:requireResume")):n.Am.warn(e.response.data.message):500===e.response.status&&n.Am.error("Internal Server Error")),e&&o(e)})}),h=()=>new Promise(async(e,t)=>{try{let t=await r.yX.get(`${a.Y.seoOpportunity}`);e(t.data)}catch(e){t(e)}}),g=(e,t,s)=>new Promise(async(l,i)=>{try{let i=await r.yX.put(`${a.Y.opportunity}/${e}/${t}/desarchiver`,{archive:s});i?.data&&(n.Am.success(`opportunity ${s?"archived":"desarchived"} successfully`),l(i.data))}catch(e){n.Am.error(`Failed to ${s?"archive":"desarchive"} the opportunity.`),i(e)}}),f=({data:e,id:t})=>new Promise(async(s,l)=>{r.yX.put(`${a.Y.seoOpportunity}/${t}`,e).then(e=>{n.Am.success("Opportunity seo updated successfully"),e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&l(e)})})},41429:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,generateMetadata:()=>u});var a=s(19510),r=s(68570);let n=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\sections\Locations.jsx#default`),l=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\sections\OfficesListForContactPage.jsx#default`),i=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\features\forms\components\ContactPageForm.jsx#default`);var o=s(43207),c=s(44957);async function u({params:{locale:e}}){let t=`https://www.pentabell.com/${"en"!==e?`${e}/`:""}contact/`,{t:s}=await (0,o.Z)(e,["global"]),a={fr:"https://www.pentabell.com/fr/contact/",en:"https://www.pentabell.com/contact/","x-default":"https://www.pentabell.com/contact/"};try{let s=await c.xk.get(`${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${e}/contact`);if(s?.data?.status===200)return{title:s?.data?.data?.versions[0]?.metaTitle,description:s?.data?.data?.versions[0]?.metaDescription,robots:s?.data?.data?.robotMeta,alternates:{canonical:t,languages:a}}}catch(e){console.error("Error fetching SEO tags:",e)}return{title:s("global:contactPage:metaTitle"),description:s("global:contactPage:metaDescription"),alternates:{canonical:t,languages:a},robots:"follow, index, max-snippet:-1, max-image-preview:large"}}let d=function(){return(0,a.jsxs)("div",{children:[a.jsx(l,{}),a.jsx(n,{}),a.jsx(i,{})]})}},44957:(e,t,s)=>{"use strict";s.d(t,{xk:()=>n,yX:()=>r});var a=s(29712);let r=a.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),n=a.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"});a.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"}),a.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,1479,1619,1336,4227,8077,6027,3718,5560,6636,9645,4289,1692,9712,9433,9984,1812,3969,4903,426],()=>s(44294));module.exports=a})();