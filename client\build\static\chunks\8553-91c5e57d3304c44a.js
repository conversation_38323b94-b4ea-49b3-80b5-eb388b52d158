"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8553],{76508:function(e){e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(i=n;0!=i--;)if(!e(t[i],r[i]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(o=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(i=n;0!=i--;)if(!Object.prototype.hasOwnProperty.call(r,o[i]))return!1;for(i=n;0!=i--;){var n,i,o,s=o[i];if(!e(t[s],r[s]))return!1}return!0}return t!=t&&r!=r}},49360:function(e,t,r){r.d(t,{Z:function(){return u}});for(var n,i={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},o=new Uint8Array(16),s=[],a=0;a<256;++a)s.push((a+256).toString(16).slice(1));var u=function(e,t,r){if(i.randomUUID&&!t&&!e)return i.randomUUID();var a=(e=e||{}).random||(e.rng||function(){if(!n&&!(n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return n(o)})();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){r=r||0;for(var u=0;u<16;++u)t[r+u]=a[u];return t}return function(e,t=0){return(s[e[t+0]]+s[e[t+1]]+s[e[t+2]]+s[e[t+3]]+"-"+s[e[t+4]]+s[e[t+5]]+"-"+s[e[t+6]]+s[e[t+7]]+"-"+s[e[t+8]]+s[e[t+9]]+"-"+s[e[t+10]]+s[e[t+11]]+s[e[t+12]]+s[e[t+13]]+s[e[t+14]]+s[e[t+15]]).toLowerCase()}(a)}},45097:function(e,t,r){let n,i;r.d(t,{V:function(){return ni}});var o,s,a,u,l,c,d={};r.r(d),r.d(d,{FILE:function(){return eg},HTML:function(){return ep},TEXT:function(){return ef},URL:function(){return eh}});var g=r(57437);function h(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var f="function"==typeof Symbol&&Symbol.observable||"@@observable",p=function(){return Math.random().toString(36).substring(7).split("").join(".")},v={INIT:"@@redux/INIT"+p(),REPLACE:"@@redux/REPLACE"+p(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+p()}},y=r(40257);function m(e,t,...r){if(void 0!==y&&void 0===t)throw Error("invariant requires an error message argument");if(!e){let e;if(void 0===t)e=Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let n=0;(e=Error(t.replace(/%s/g,function(){return r[n++]}))).name="Invariant Violation"}throw e.framesToPop=1,e}}function b(e){return"object"==typeof e}let O="dnd-core/INIT_COORDS",D="dnd-core/BEGIN_DRAG",T="dnd-core/PUBLISH_DRAG_SOURCE",S="dnd-core/HOVER",I="dnd-core/DROP",w="dnd-core/END_DRAG";function E(e,t){return{type:O,payload:{sourceClientOffset:t||null,clientOffset:e||null}}}let C={type:O,payload:{clientOffset:null,sourceClientOffset:null}};function _(e,t){return null===t?null===e:Array.isArray(e)?e.some(e=>e===t):e===t}class j{receiveBackend(e){this.backend=e}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){var e,t,r,n,i;let o=this,{dispatch:s}=this.store,a={beginDrag:(e=this,function(t=[],r={publishSource:!0}){let{publishSource:n=!0,clientOffset:i,getSourceClientOffset:o}=r,s=e.getMonitor(),a=e.getRegistry();e.dispatch(E(i)),m(!s.isDragging(),"Cannot call beginDrag while dragging."),t.forEach(function(e){m(a.getSource(e),"Expected sourceIds to be registered.")});let u=function(e,t){let r=null;for(let n=e.length-1;n>=0;n--)if(t.canDragSource(e[n])){r=e[n];break}return r}(t,s);if(null==u){e.dispatch(C);return}let l=null;if(i){if(!o)throw Error("getSourceClientOffset must be defined");m("function"==typeof o,"When clientOffset is provided, getSourceClientOffset must be a function."),l=o(u)}e.dispatch(E(i,l));let c=a.getSource(u).beginDrag(s,u);if(null!=c)return m(b(c),"Item must be an object."),a.pinSource(u),{type:D,payload:{itemType:a.getSourceType(u),item:c,sourceId:u,clientOffset:i||null,sourceClientOffset:l||null,isSourcePublic:!!n}}}),publishDragSource:(t=this,function(){if(t.getMonitor().isDragging())return{type:T}}),hover:(r=this,function(e,{clientOffset:t}={}){m(Array.isArray(e),"Expected targetIds to be an array.");let n=e.slice(0),i=r.getMonitor(),o=r.getRegistry();return function(e,t,r){for(let n=e.length-1;n>=0;n--){let i=e[n];_(t.getTargetType(i),r)||e.splice(n,1)}}(n,o,i.getItemType()),function(e,t,r){m(t.isDragging(),"Cannot call hover while not dragging."),m(!t.didDrop(),"Cannot call hover after drop.");for(let t=0;t<e.length;t++){let n=e[t];m(e.lastIndexOf(n)===t,"Expected targetIds to be unique in the passed array."),m(r.getTarget(n),"Expected targetIds to be registered.")}}(n,i,o),function(e,t,r){e.forEach(function(e){r.getTarget(e).hover(t,e)})}(n,i,o),{type:S,payload:{targetIds:n,clientOffset:t||null}}}),drop:(n=this,function(e={}){let t=n.getMonitor(),r=n.getRegistry();m(t.isDragging(),"Cannot call drop while not dragging."),m(!t.didDrop(),"Cannot call drop twice during one drag operation."),(function(e){let t=e.getTargetIds().filter(e.canDropOnTarget,e);return t.reverse(),t})(t).forEach((i,o)=>{let s={type:I,payload:{dropResult:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n})}return e}({},e,function(e,t,r,n){var i;let o=r.getTarget(e),s=o?o.drop(n,e):void 0;return m(void 0===(i=s)||b(i),"Drop result must either be an object or undefined."),void 0===s&&(s=0===t?{}:n.getDropResult()),s}(i,o,r,t))}};n.dispatch(s)})}),endDrag:(i=this,function(){let e=i.getMonitor(),t=i.getRegistry();m(e.isDragging(),"Cannot call endDrag while not dragging.");let r=e.getSourceId();return null!=r&&(t.getSource(r,!0).endDrag(e,r),t.unpinSource()),{type:w}})};return Object.keys(a).reduce((e,t)=>{let r=a[t];return e[t]=(...e)=>{let t=r.apply(o,e);void 0!==t&&s(t)},e},{})}dispatch(e){this.store.dispatch(e)}constructor(e,t){this.isSetUp=!1,this.handleRefCountChange=()=>{let e=this.store.getState().refCount>0;this.backend&&(e&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!e&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=e,this.monitor=t,e.subscribe(this.handleRefCountChange)}}function x(e,t){return{x:e.x-t.x,y:e.y-t.y}}let P=[],N=[];P.__IS_NONE__=!0,N.__IS_ALL__=!0;class R{subscribeToStateChange(e,t={}){let{handlerIds:r}=t;m("function"==typeof e,"listener must be a function."),m(void 0===r||Array.isArray(r),"handlerIds, when specified, must be an array of strings.");let n=this.store.getState().stateId;return this.store.subscribe(()=>{let t=this.store.getState(),i=t.stateId;try{var o,s;i!==n&&(i!==n+1||(o=t.dirtyHandlerIds,s=r,o!==P&&(o===N||void 0===s||s.filter(e=>o.indexOf(e)>-1).length>0)))&&e()}finally{n=i}})}subscribeToOffsetChange(e){m("function"==typeof e,"listener must be a function.");let t=this.store.getState().dragOffset;return this.store.subscribe(()=>{let r=this.store.getState().dragOffset;r!==t&&(t=r,e())})}canDragSource(e){if(!e)return!1;let t=this.registry.getSource(e);return m(t,`Expected to find a valid source. sourceId=${e}`),!this.isDragging()&&t.canDrag(this,e)}canDropOnTarget(e){if(!e)return!1;let t=this.registry.getTarget(e);return m(t,`Expected to find a valid target. targetId=${e}`),!(!this.isDragging()||this.didDrop())&&_(this.registry.getTargetType(e),this.getItemType())&&t.canDrop(this,e)}isDragging(){return!!this.getItemType()}isDraggingSource(e){if(!e)return!1;let t=this.registry.getSource(e,!0);return m(t,`Expected to find a valid source. sourceId=${e}`),!!(this.isDragging()&&this.isSourcePublic())&&this.registry.getSourceType(e)===this.getItemType()&&t.isDragging(this,e)}isOverTarget(e,t={shallow:!1}){if(!e)return!1;let{shallow:r}=t;if(!this.isDragging())return!1;let n=this.registry.getTargetType(e),i=this.getItemType();if(i&&!_(n,i))return!1;let o=this.getTargetIds();if(!o.length)return!1;let s=o.indexOf(e);return r?s===o.length-1:s>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return!!this.store.getState().dragOperation.isSourcePublic}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return function(e){let{clientOffset:t,initialClientOffset:r,initialSourceClientOffset:n}=e;return t&&r&&n?x({x:t.x+n.x,y:t.y+n.y},r):null}(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return function(e){let{clientOffset:t,initialClientOffset:r}=e;return t&&r?x(t,r):null}(this.store.getState().dragOffset)}constructor(e,t){this.store=e,this.registry=t}}let A="undefined"!=typeof global?global:self,M=A.MutationObserver||A.WebKitMutationObserver;function k(e){return function(){let t=setTimeout(n,0),r=setInterval(n,50);function n(){clearTimeout(t),clearInterval(r),e()}}}let L="function"==typeof M?function(e){let t=1,r=new M(e),n=document.createTextNode("");return r.observe(n,{characterData:!0}),function(){t=-t,n.data=t}}:k;class H{enqueueTask(e){let{queue:t,requestFlush:r}=this;t.length||(r(),this.flushing=!0),t[t.length]=e}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{let{queue:e}=this;for(;this.index<e.length;){let t=this.index;if(this.index++,e[t].call(),this.index>this.capacity){for(let t=0,r=e.length-this.index;t<r;t++)e[t]=e[t+this.index];e.length-=this.index,this.index=0}}e.length=0,this.index=0,this.flushing=!1},this.registerPendingError=e=>{this.pendingErrors.push(e),this.requestErrorThrow()},this.requestFlush=L(this.flush),this.requestErrorThrow=k(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class U{call(){try{this.task&&this.task()}catch(e){this.onError(e)}finally{this.task=null,this.release(this)}}constructor(e,t){this.onError=e,this.release=t,this.task=null}}class F{create(e){let t=this.freeTasks,r=t.length?t.pop():new U(this.onError,e=>t[t.length]=e);return r.task=e,r}constructor(e){this.onError=e,this.freeTasks=[]}}let B=new H,$=new F(B.registerPendingError),z="dnd-core/ADD_SOURCE",q="dnd-core/ADD_TARGET",V="dnd-core/REMOVE_SOURCE",G="dnd-core/REMOVE_TARGET";function W(e,t){if(t&&Array.isArray(e)){e.forEach(e=>W(e,!1));return}m("string"==typeof e||"symbol"==typeof e,t?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}(o=c||(c={})).SOURCE="SOURCE",o.TARGET="TARGET";let K=0;function X(e){switch(e[0]){case"S":return c.SOURCE;case"T":return c.TARGET;default:throw Error(`Cannot parse handler ID: ${e}`)}}function Y(e,t){let r=e.entries(),n=!1;do{let{done:e,value:[,i]}=r.next();if(i===t)return!0;n=!!e}while(!n);return!1}class Q{addSource(e,t){W(e),m("function"==typeof t.canDrag,"Expected canDrag to be a function."),m("function"==typeof t.beginDrag,"Expected beginDrag to be a function."),m("function"==typeof t.endDrag,"Expected endDrag to be a function.");let r=this.addHandler(c.SOURCE,e,t);return this.store.dispatch({type:z,payload:{sourceId:r}}),r}addTarget(e,t){W(e,!0),m("function"==typeof t.canDrop,"Expected canDrop to be a function."),m("function"==typeof t.hover,"Expected hover to be a function."),m("function"==typeof t.drop,"Expected beginDrag to be a function.");let r=this.addHandler(c.TARGET,e,t);return this.store.dispatch({type:q,payload:{targetId:r}}),r}containsHandler(e){return Y(this.dragSources,e)||Y(this.dropTargets,e)}getSource(e,t=!1){return m(this.isSourceId(e),"Expected a valid source ID."),t&&e===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(e)}getTarget(e){return m(this.isTargetId(e),"Expected a valid target ID."),this.dropTargets.get(e)}getSourceType(e){return m(this.isSourceId(e),"Expected a valid source ID."),this.types.get(e)}getTargetType(e){return m(this.isTargetId(e),"Expected a valid target ID."),this.types.get(e)}isSourceId(e){return X(e)===c.SOURCE}isTargetId(e){return X(e)===c.TARGET}removeSource(e){var t;m(this.getSource(e),"Expected an existing source."),this.store.dispatch({type:V,payload:{sourceId:e}}),t=()=>{this.dragSources.delete(e),this.types.delete(e)},B.enqueueTask($.create(t))}removeTarget(e){m(this.getTarget(e),"Expected an existing target."),this.store.dispatch({type:G,payload:{targetId:e}}),this.dropTargets.delete(e),this.types.delete(e)}pinSource(e){let t=this.getSource(e);m(t,"Expected an existing source."),this.pinnedSourceId=e,this.pinnedSource=t}unpinSource(){m(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(e,t,r){let n=function(e){let t=(K++).toString();switch(e){case c.SOURCE:return`S${t}`;case c.TARGET:return`T${t}`;default:throw Error(`Unknown Handler Role: ${e}`)}}(e);return this.types.set(n,t),e===c.SOURCE?this.dragSources.set(n,r):e===c.TARGET&&this.dropTargets.set(n,r),n}constructor(e){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=e}}let Z=(e,t)=>e===t,J={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function ee(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n})}return e}let et={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function er(e={},t){var r;return{dirtyHandlerIds:function(e=P,t){switch(t.type){case S:break;case z:case q:case G:case V:return P;default:return N}let{targetIds:r=[],prevTargetIds:n=[]}=t.payload,i=function(e,t){let r=new Map,n=e=>{r.set(e,r.has(e)?r.get(e)+1:1)};e.forEach(n),t.forEach(n);let i=[];return r.forEach((e,t)=>{1===e&&i.push(t)}),i}(r,n);if(!(i.length>0||!function(e,t,r=Z){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!r(e[n],t[n]))return!1;return!0}(r,n)))return P;let o=n[n.length-1],s=r[r.length-1];return o!==s&&(o&&i.push(o),s&&i.push(s)),i}(e.dirtyHandlerIds,{type:t.type,payload:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n})}return e}({},t.payload,{prevTargetIds:(r=[],"dragOperation.targetIds".split(".").reduce((e,t)=>e&&e[t]?e[t]:r||null,e))})}),dragOffset:function(e=J,t){let{payload:r}=t;switch(t.type){case O:case D:return{initialSourceClientOffset:r.sourceClientOffset,initialClientOffset:r.clientOffset,clientOffset:r.clientOffset};case S:var n,i;if(n=e.clientOffset,i=r.clientOffset,!n&&!i||n&&i&&n.x===i.x&&n.y===i.y)return e;return function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n})}return e}({},e,{clientOffset:r.clientOffset});case w:case I:return J;default:return e}}(e.dragOffset,t),refCount:function(e=0,t){switch(t.type){case z:case q:return e+1;case V:case G:return e-1;default:return e}}(e.refCount,t),dragOperation:function(e=et,t){let{payload:r}=t;switch(t.type){case D:return ee({},e,{itemType:r.itemType,item:r.item,sourceId:r.sourceId,isSourcePublic:r.isSourcePublic,dropResult:null,didDrop:!1});case T:return ee({},e,{isSourcePublic:!0});case S:return ee({},e,{targetIds:r.targetIds});case G:var n,i;if(-1===e.targetIds.indexOf(r.targetId))return e;return ee({},e,{targetIds:(n=e.targetIds,i=r.targetId,n.filter(e=>e!==i))});case I:return ee({},e,{dropResult:r.dropResult,didDrop:!0,targetIds:[]});case w:return ee({},e,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return e}}(e.dragOperation,t),stateId:function(e=0){return e+1}(e.stateId)}}var en=r(2265);let ei=(0,en.createContext)({dragDropManager:void 0}),eo=0,es=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var ea=(0,en.memo)(function(e){var t,{children:r}=e;let[n,i]="manager"in(t=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,["children"]))?[{dragDropManager:t.manager},!1]:[function(e,t=eu(),r,n){return t[es]||(t[es]={dragDropManager:function(e,t,r={},n=!1){let i=function(e){let t="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__;return function e(t,r,n){if("function"==typeof r&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw Error(h(0));if("function"==typeof r&&void 0===n&&(n=r,r=void 0),void 0!==n){if("function"!=typeof n)throw Error(h(1));return n(e)(t,r)}if("function"!=typeof t)throw Error(h(2));var i,o=t,s=r,a=[],u=a,l=!1;function c(){u===a&&(u=a.slice())}function d(){if(l)throw Error(h(3));return s}function g(e){if("function"!=typeof e)throw Error(h(4));if(l)throw Error(h(5));var t=!0;return c(),u.push(e),function(){if(t){if(l)throw Error(h(6));t=!1,c();var r=u.indexOf(e);u.splice(r,1),a=null}}}function p(e){if(!function(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(e))throw Error(h(7));if(void 0===e.type)throw Error(h(8));if(l)throw Error(h(9));try{l=!0,s=o(s,e)}finally{l=!1}for(var t=a=u,r=0;r<t.length;r++)(0,t[r])();return e}return p({type:v.INIT}),(i={dispatch:p,subscribe:g,getState:d,replaceReducer:function(e){if("function"!=typeof e)throw Error(h(10));o=e,p({type:v.REPLACE})}})[f]=function(){var e;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw Error(h(11));function t(){e.next&&e.next(d())}return t(),{unsubscribe:g(t)}}})[f]=function(){return this},e},i}(er,e&&t&&t({name:"dnd-core",instanceId:"dnd-core"}))}(n),o=new R(i,new Q(i)),s=new j(i,o),a=e(s,t,r);return s.receiveBackend(a),s}(e,t,r,n)}),t[es]}(t.backend,t.context,t.options,t.debugMode),!t.context];return(0,en.useEffect)(()=>{if(i){let e=eu();return++eo,()=>{0==--eo&&(e[es]=null)}}},[]),(0,g.jsx)(ei.Provider,{value:n,children:r})});function eu(){return"undefined"!=typeof global?global:window}function el(e){let t=null;return()=>(null==t&&(t=e()),t)}class ec{enter(e){let t=this.entered.length;return this.entered=function(e,t){let r=new Set,n=e=>r.add(e);e.forEach(n),t.forEach(n);let i=[];return r.forEach(e=>i.push(e)),i}(this.entered.filter(t=>this.isNodeInDocument(t)&&(!t.contains||t.contains(e))),[e]),0===t&&this.entered.length>0}leave(e){let t=this.entered.length;return this.entered=this.entered.filter(this.isNodeInDocument).filter(t=>t!==e),t>0&&0===this.entered.length}reset(){this.entered=[]}constructor(e){this.entered=[],this.isNodeInDocument=e}}class ed{initializeExposedProperties(){Object.keys(this.config.exposeProperties).forEach(e=>{Object.defineProperty(this.item,e,{configurable:!0,enumerable:!0,get:()=>(console.warn(`Browser doesn't allow reading "${e}" until the drop event.`),null)})})}loadDataTransfer(e){if(e){let t={};Object.keys(this.config.exposeProperties).forEach(r=>{let n=this.config.exposeProperties[r];null!=n&&(t[r]={value:n(e,this.config.matchesTypes),configurable:!0,enumerable:!0})}),Object.defineProperties(this.item,t)}}canDrag(){return!0}beginDrag(){return this.item}isDragging(e,t){return t===e.getSourceId()}endDrag(){}constructor(e){this.config=e,this.item={},this.initializeExposedProperties()}}let eg="__NATIVE_FILE__",eh="__NATIVE_URL__",ef="__NATIVE_TEXT__",ep="__NATIVE_HTML__";function ev(e,t,r){let n=t.reduce((t,r)=>t||e.getData(r),"");return null!=n?n:r}let ey={[eg]:{exposeProperties:{files:e=>Array.prototype.slice.call(e.files),items:e=>e.items,dataTransfer:e=>e},matchesTypes:["Files"]},[ep]:{exposeProperties:{html:(e,t)=>ev(e,t,""),dataTransfer:e=>e},matchesTypes:["Html","text/html"]},[eh]:{exposeProperties:{urls:(e,t)=>ev(e,t,"").split("\n"),dataTransfer:e=>e},matchesTypes:["Url","text/uri-list"]},[ef]:{exposeProperties:{text:(e,t)=>ev(e,t,""),dataTransfer:e=>e},matchesTypes:["Text","text/plain"]}};function em(e){if(!e)return null;let t=Array.prototype.slice.call(e.types||[]);return Object.keys(ey).filter(e=>{let r=ey[e];return null!=r&&!!r.matchesTypes&&r.matchesTypes.some(e=>t.indexOf(e)>-1)})[0]||null}let eb=el(()=>/firefox/i.test(navigator.userAgent)),eO=el(()=>!!window.safari);class eD{interpolate(e){let t;let{xs:r,ys:n,c1s:i,c2s:o,c3s:s}=this,a=r.length-1;if(e===r[a])return n[a];let u=0,l=s.length-1;for(;u<=l;){let i=r[t=Math.floor(.5*(u+l))];if(i<e)u=t+1;else{if(!(i>e))return n[t];l=t-1}}let c=e-r[a=Math.max(0,l)],d=c*c;return n[a]+i[a]*c+o[a]*d+s[a]*c*d}constructor(e,t){let r,n,i;let{length:o}=e,s=[];for(let e=0;e<o;e++)s.push(e);s.sort((t,r)=>e[t]<e[r]?-1:1);let a=[],u=[],l=[];for(let i=0;i<o-1;i++)r=e[i+1]-e[i],n=t[i+1]-t[i],u.push(r),a.push(n),l.push(n/r);let c=[l[0]];for(let e=0;e<u.length-1;e++){let t=l[e],n=l[e+1];if(t*n<=0)c.push(0);else{r=u[e];let i=u[e+1],o=r+i;c.push(3*o/((o+i)/t+(o+r)/n))}}c.push(l[l.length-1]);let d=[],g=[];for(let e=0;e<c.length-1;e++){i=l[e];let t=c[e],r=1/u[e],n=t+c[e+1]-i-i;d.push((i-t-n)*r),g.push(n*r*r)}this.xs=e,this.ys=t,this.c1s=c,this.c2s=d,this.c3s=g}}function eT(e){let t=1===e.nodeType?e:e.parentElement;if(!t)return null;let{top:r,left:n}=t.getBoundingClientRect();return{x:n,y:r}}function eS(e){return{x:e.clientX,y:e.clientY}}class eI{get window(){return this.globalContext?this.globalContext:"undefined"!=typeof window?window:void 0}get document(){var e;return(null===(e=this.globalContext)||void 0===e?void 0:e.document)?this.globalContext.document:this.window?this.window.document:void 0}get rootElement(){var e;return(null===(e=this.optionsArgs)||void 0===e?void 0:e.rootElement)||this.window}constructor(e,t){this.ownerDocument=null,this.globalContext=e,this.optionsArgs=t}}function ew(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n})}return e}class eE{profile(){var e,t;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:(null===(e=this.dragStartSourceIds)||void 0===e?void 0:e.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:(null===(t=this.dragOverTargetIds)||void 0===t?void 0:t.length)||0}}get window(){return this.options.window}get document(){return this.options.document}get rootElement(){return this.options.rootElement}setup(){let e=this.rootElement;if(void 0!==e){if(e.__isReactDndBackendSetUp)throw Error("Cannot have two HTML5 backends at the same time.");e.__isReactDndBackendSetUp=!0,this.addEventListeners(e)}}teardown(){let e=this.rootElement;if(void 0!==e&&(e.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId)){var t;null===(t=this.window)||void 0===t||t.cancelAnimationFrame(this.asyncEndDragFrameId)}}connectDragPreview(e,t,r){return this.sourcePreviewNodeOptions.set(e,r),this.sourcePreviewNodes.set(e,t),()=>{this.sourcePreviewNodes.delete(e),this.sourcePreviewNodeOptions.delete(e)}}connectDragSource(e,t,r){this.sourceNodes.set(e,t),this.sourceNodeOptions.set(e,r);let n=t=>this.handleDragStart(t,e),i=e=>this.handleSelectStart(e);return t.setAttribute("draggable","true"),t.addEventListener("dragstart",n),t.addEventListener("selectstart",i),()=>{this.sourceNodes.delete(e),this.sourceNodeOptions.delete(e),t.removeEventListener("dragstart",n),t.removeEventListener("selectstart",i),t.setAttribute("draggable","false")}}connectDropTarget(e,t){let r=t=>this.handleDragEnter(t,e),n=t=>this.handleDragOver(t,e),i=t=>this.handleDrop(t,e);return t.addEventListener("dragenter",r),t.addEventListener("dragover",n),t.addEventListener("drop",i),()=>{t.removeEventListener("dragenter",r),t.removeEventListener("dragover",n),t.removeEventListener("drop",i)}}addEventListeners(e){e.addEventListener&&(e.addEventListener("dragstart",this.handleTopDragStart),e.addEventListener("dragstart",this.handleTopDragStartCapture,!0),e.addEventListener("dragend",this.handleTopDragEndCapture,!0),e.addEventListener("dragenter",this.handleTopDragEnter),e.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.addEventListener("dragover",this.handleTopDragOver),e.addEventListener("dragover",this.handleTopDragOverCapture,!0),e.addEventListener("drop",this.handleTopDrop),e.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(e){e.removeEventListener&&(e.removeEventListener("dragstart",this.handleTopDragStart),e.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),e.removeEventListener("dragend",this.handleTopDragEndCapture,!0),e.removeEventListener("dragenter",this.handleTopDragEnter),e.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.removeEventListener("dragover",this.handleTopDragOver),e.removeEventListener("dragover",this.handleTopDragOverCapture,!0),e.removeEventListener("drop",this.handleTopDrop),e.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){let e=this.monitor.getSourceId(),t=this.sourceNodeOptions.get(e);return ew({dropEffect:this.altKeyPressed?"copy":"move"},t||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){let e=this.monitor.getSourceId();return ew({anchorX:.5,anchorY:.5,captureDraggingState:!1},this.sourcePreviewNodeOptions.get(e)||{})}isDraggingNativeItem(){let e=this.monitor.getItemType();return Object.keys(d).some(t=>d[t]===e)}beginDragNativeItem(e,t){this.clearCurrentDragSourceNode(),this.currentNativeSource=function(e,t){let r=ey[e];if(!r)throw Error(`native type ${e} has no configuration`);let n=new ed(r);return n.loadDataTransfer(t),n}(e,t),this.currentNativeHandle=this.registry.addSource(e,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(e){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=e,this.mouseMoveTimeoutTimer=setTimeout(()=>{var e;return null===(e=this.rootElement)||void 0===e?void 0:e.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)},1e3)}clearCurrentDragSourceNode(){if(this.currentDragSourceNode){if(this.currentDragSourceNode=null,this.rootElement){var e;null===(e=this.window)||void 0===e||e.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}return this.mouseMoveTimeoutTimer=null,!0}return!1}handleDragStart(e,t){e.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(t))}handleDragEnter(e,t){this.dragEnterTargetIds.unshift(t)}handleDragOver(e,t){null===this.dragOverTargetIds&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(t)}handleDrop(e,t){this.dropTargetIds.unshift(t)}constructor(e,t,r){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.lastClientOffset=null,this.hoverRafId=null,this.getSourceClientOffset=e=>{let t=this.sourceNodes.get(e);return t&&eT(t)||null},this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.currentNativeHandle&&this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=e=>!!(e&&this.document&&this.document.body&&this.document.body.contains(e)),this.endDragIfSourceWasRemovedFromDOM=()=>{let e=this.currentDragSourceNode;null==e||this.isNodeInDocument(e)||(this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover())},this.scheduleHover=e=>{null===this.hoverRafId&&"undefined"!=typeof requestAnimationFrame&&(this.hoverRafId=requestAnimationFrame(()=>{this.monitor.isDragging()&&this.actions.hover(e||[],{clientOffset:this.lastClientOffset}),this.hoverRafId=null}))},this.cancelHover=()=>{null!==this.hoverRafId&&"undefined"!=typeof cancelAnimationFrame&&(cancelAnimationFrame(this.hoverRafId),this.hoverRafId=null)},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=e=>{if(e.defaultPrevented)return;let{dragStartSourceIds:t}=this;this.dragStartSourceIds=null;let r=eS(e);this.monitor.isDragging()&&(this.actions.endDrag(),this.cancelHover()),this.actions.beginDrag(t||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:r});let{dataTransfer:n}=e,i=em(n);if(this.monitor.isDragging()){if(n&&"function"==typeof n.setDragImage){let e=this.monitor.getSourceId(),t=this.sourceNodes.get(e),i=this.sourcePreviewNodes.get(e)||t;if(i){let{anchorX:e,anchorY:o,offsetX:s,offsetY:a}=this.getCurrentSourcePreviewNodeOptions(),u=function(e,t,r,n,i){var o;let s,a,u;let l="IMG"===t.nodeName&&(eb()||!(null===(o=document.documentElement)||void 0===o?void 0:o.contains(t))),c=eT(l?e:t),d={x:r.x-c.x,y:r.y-c.y},{offsetWidth:g,offsetHeight:h}=e,{anchorX:f,anchorY:p}=n,{dragPreviewWidth:v,dragPreviewHeight:y}=(s=l?t.width:g,a=l?t.height:h,eO()&&l&&(a/=window.devicePixelRatio,s/=window.devicePixelRatio),{dragPreviewWidth:s,dragPreviewHeight:a}),{offsetX:m,offsetY:b}=i;return{x:0===m||m?m:new eD([0,.5,1],[d.x,d.x/g*v,d.x+v-g]).interpolate(f),y:0===b||b?b:(u=new eD([0,.5,1],[d.y,d.y/h*y,d.y+y-h]).interpolate(p),eO()&&l&&(u+=(window.devicePixelRatio-1)*y),u)}}(t,i,r,{anchorX:e,anchorY:o},{offsetX:s,offsetY:a});n.setDragImage(i,u.x,u.y)}}try{null==n||n.setData("application/json",{})}catch(e){}this.setCurrentDragSourceNode(e.target);let{captureDraggingState:t}=this.getCurrentSourcePreviewNodeOptions();t?this.actions.publishDragSource():setTimeout(()=>this.actions.publishDragSource(),0)}else if(i)this.beginDragNativeItem(i);else{if(n&&!n.types&&(e.target&&!e.target.hasAttribute||!e.target.hasAttribute("draggable")))return;e.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleTopDragEnterCapture=e=>{if(this.dragEnterTargetIds=[],this.isDraggingNativeItem()){var t;null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer)}if(!this.enterLeaveCounter.enter(e.target)||this.monitor.isDragging())return;let{dataTransfer:r}=e,n=em(r);n&&this.beginDragNativeItem(n,r)},this.handleTopDragEnter=e=>{let{dragEnterTargetIds:t}=this;this.dragEnterTargetIds=[],this.monitor.isDragging()&&(this.altKeyPressed=e.altKey,t.length>0&&this.actions.hover(t,{clientOffset:eS(e)}),t.some(e=>this.monitor.canDropOnTarget(e))&&(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect=this.getCurrentDropEffect())))},this.handleTopDragOverCapture=e=>{if(this.dragOverTargetIds=[],this.isDraggingNativeItem()){var t;null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer)}},this.handleTopDragOver=e=>{let{dragOverTargetIds:t}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging()){e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="none");return}this.altKeyPressed=e.altKey,this.lastClientOffset=eS(e),this.scheduleHover(t),(t||[]).some(e=>this.monitor.canDropOnTarget(e))?(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?e.preventDefault():(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=e=>{this.isDraggingNativeItem()&&e.preventDefault(),this.enterLeaveCounter.leave(e.target)&&(this.isDraggingNativeItem()&&setTimeout(()=>this.endDragNativeItem(),0),this.cancelHover())},this.handleTopDropCapture=e=>{if(this.dropTargetIds=[],this.isDraggingNativeItem()){var t;e.preventDefault(),null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer)}else em(e.dataTransfer)&&e.preventDefault();this.enterLeaveCounter.reset()},this.handleTopDrop=e=>{let{dropTargetIds:t}=this;this.dropTargetIds=[],this.actions.hover(t,{clientOffset:eS(e)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleSelectStart=e=>{let t=e.target;"function"!=typeof t.dragDrop||"INPUT"===t.tagName||"SELECT"===t.tagName||"TEXTAREA"===t.tagName||t.isContentEditable||(e.preventDefault(),t.dragDrop())},this.options=new eI(t,r),this.actions=e.getActions(),this.monitor=e.getMonitor(),this.registry=e.getRegistry(),this.enterLeaveCounter=new ec(this.isNodeInDocument)}}let eC=function(e,t,r){return new eE(e,t,r)};var e_=r(76508);let ej="undefined"!=typeof window?en.useLayoutEffect:en.useEffect;function ex(e,t,r){return function(e,t,r){let[n,i]=function(e,t,r){let[n,i]=(0,en.useState)(()=>t(e)),o=(0,en.useCallback)(()=>{let o=t(e);!e_(n,o)&&(i(o),r&&r())},[n,e,r]);return ej(o),[n,o]}(e,t,r);return ej(function(){let t=e.getHandlerId();if(null!=t)return e.subscribeToStateChange(i,{handlerIds:[t]})},[e,i]),n}(t,e||(()=>({})),()=>r.reconnect())}function eP(e,t){let r=[...t||[]];return null==t&&"function"!=typeof e&&r.push(e),(0,en.useMemo)(()=>"function"==typeof e?e():e,r)}function eN(e,t,r,n){let i=r?r.call(n,e,t):void 0;if(void 0!==i)return!!i;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;let o=Object.keys(e),s=Object.keys(t);if(o.length!==s.length)return!1;let a=Object.prototype.hasOwnProperty.bind(t);for(let s=0;s<o.length;s++){let u=o[s];if(!a(u))return!1;let l=e[u],c=t[u];if(!1===(i=r?r.call(n,l,c,u):void 0)||void 0===i&&l!==c)return!1}return!0}function eR(e){return null!==e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function eA(e){let t={};return Object.keys(e).forEach(r=>{let n=e[r];if(r.endsWith("Ref"))t[r]=e[r];else{let e=(e=null,t=null)=>(0,en.isValidElement)(e)?(function(e){if("string"==typeof e.type)return;let t=e.type.displayName||e.type.name||"the component";throw Error(`Only native element nodes can now be passed to React DnD connectors.You can either wrap ${t} into a <div>, or turn it into a drag source or a drop target itself.`)}(e),function(e,t){let r=e.ref;return(m("string"!=typeof r,"Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),r)?(0,en.cloneElement)(e,{ref:e=>{eM(r,e),eM(t,e)}}):(0,en.cloneElement)(e,{ref:t})}(e,t?e=>n(e,t):n)):(n(e,t),e);t[r]=()=>e}}),t}function eM(e,t){"function"==typeof e?e(t):e.current=t}class ek{receiveHandlerId(e){this.handlerId!==e&&(this.handlerId=e,this.reconnect())}get connectTarget(){return this.dragSource}get dragSourceOptions(){return this.dragSourceOptionsInternal}set dragSourceOptions(e){this.dragSourceOptionsInternal=e}get dragPreviewOptions(){return this.dragPreviewOptionsInternal}set dragPreviewOptions(e){this.dragPreviewOptionsInternal=e}reconnect(){let e=this.reconnectDragSource();this.reconnectDragPreview(e)}reconnectDragSource(){let e=this.dragSource,t=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();return t&&this.disconnectDragSource(),this.handlerId&&(e?t&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=e,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,e,this.dragSourceOptions)):this.lastConnectedDragSource=e),t}reconnectDragPreview(e=!1){let t=this.dragPreview,r=e||this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();if(r&&this.disconnectDragPreview(),this.handlerId){if(!t){this.lastConnectedDragPreview=t;return}r&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=t,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,t,this.dragPreviewOptions))}}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didConnectedDragSourceChange(){return this.lastConnectedDragSource!==this.dragSource}didConnectedDragPreviewChange(){return this.lastConnectedDragPreview!==this.dragPreview}didDragSourceOptionsChange(){return!eN(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}didDragPreviewOptionsChange(){return!eN(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}disconnectDragSource(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}disconnectDragPreview(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}get dragSource(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}get dragPreview(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}clearDragSource(){this.dragSourceNode=null,this.dragSourceRef=null}clearDragPreview(){this.dragPreviewNode=null,this.dragPreviewRef=null}constructor(e){this.hooks=eA({dragSource:(e,t)=>{this.clearDragSource(),this.dragSourceOptions=t||null,eR(e)?this.dragSourceRef=e:this.dragSourceNode=e,this.reconnectDragSource()},dragPreview:(e,t)=>{this.clearDragPreview(),this.dragPreviewOptions=t||null,eR(e)?this.dragPreviewRef=e:this.dragPreviewNode=e,this.reconnectDragPreview()}}),this.handlerId=null,this.dragSourceRef=null,this.dragSourceOptionsInternal=null,this.dragPreviewRef=null,this.dragPreviewOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDragSource=null,this.lastConnectedDragSourceOptions=null,this.lastConnectedDragPreview=null,this.lastConnectedDragPreviewOptions=null,this.backend=e}}function eL(){let{dragDropManager:e}=(0,en.useContext)(ei);return m(null!=e,"Expected drag drop context"),e}let eH=!1,eU=!1;class eF{receiveHandlerId(e){this.sourceId=e}getHandlerId(){return this.sourceId}canDrag(){m(!eH,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return eH=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{eH=!1}}isDragging(){if(!this.sourceId)return!1;m(!eU,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return eU=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{eU=!1}}subscribeToStateChange(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}isDraggingSource(e){return this.internalMonitor.isDraggingSource(e)}isOverTarget(e,t){return this.internalMonitor.isOverTarget(e,t)}getTargetIds(){return this.internalMonitor.getTargetIds()}isSourcePublic(){return this.internalMonitor.isSourcePublic()}getSourceId(){return this.internalMonitor.getSourceId()}subscribeToOffsetChange(e){return this.internalMonitor.subscribeToOffsetChange(e)}canDragSource(e){return this.internalMonitor.canDragSource(e)}canDropOnTarget(e){return this.internalMonitor.canDropOnTarget(e)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.sourceId=null,this.internalMonitor=e.getMonitor()}}class eB{beginDrag(){let e=this.spec,t=this.monitor,r=null;return null!=(r="object"==typeof e.item?e.item:"function"==typeof e.item?e.item(t):{})?r:null}canDrag(){let e=this.spec,t=this.monitor;return"boolean"==typeof e.canDrag?e.canDrag:"function"!=typeof e.canDrag||e.canDrag(t)}isDragging(e,t){let r=this.spec,n=this.monitor,{isDragging:i}=r;return i?i(n):t===e.getSourceId()}endDrag(){let e=this.spec,t=this.monitor,r=this.connector,{end:n}=e;n&&n(t.getItem(),t),r.reconnect()}constructor(e,t,r){this.spec=e,this.monitor=t,this.connector=r}}class e${get connectTarget(){return this.dropTarget}reconnect(){let e=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();e&&this.disconnectDropTarget();let t=this.dropTarget;if(this.handlerId){if(!t){this.lastConnectedDropTarget=t;return}e&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=t,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,t,this.dropTargetOptions))}}receiveHandlerId(e){e!==this.handlerId&&(this.handlerId=e,this.reconnect())}get dropTargetOptions(){return this.dropTargetOptionsInternal}set dropTargetOptions(e){this.dropTargetOptionsInternal=e}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didDropTargetChange(){return this.lastConnectedDropTarget!==this.dropTarget}didOptionsChange(){return!eN(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}disconnectDropTarget(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}get dropTarget(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}clearDropTarget(){this.dropTargetRef=null,this.dropTargetNode=null}constructor(e){this.hooks=eA({dropTarget:(e,t)=>{this.clearDropTarget(),this.dropTargetOptions=t,eR(e)?this.dropTargetRef=e:this.dropTargetNode=e,this.reconnect()}}),this.handlerId=null,this.dropTargetRef=null,this.dropTargetOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDropTarget=null,this.lastConnectedDropTargetOptions=null,this.backend=e}}let ez=!1;class eq{receiveHandlerId(e){this.targetId=e}getHandlerId(){return this.targetId}subscribeToStateChange(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}canDrop(){if(!this.targetId)return!1;m(!ez,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return ez=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{ez=!1}}isOver(e){return!!this.targetId&&this.internalMonitor.isOverTarget(this.targetId,e)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.targetId=null,this.internalMonitor=e.getMonitor()}}class eV{canDrop(){let e=this.spec,t=this.monitor;return!e.canDrop||e.canDrop(t.getItem(),t)}hover(){let e=this.spec,t=this.monitor;e.hover&&e.hover(t.getItem(),t)}drop(){let e=this.spec,t=this.monitor;if(e.drop)return e.drop(t.getItem(),t)}constructor(e,t){this.spec=e,this.monitor=t}}var eG=Object.create,eW=Object.defineProperty,eK=Object.getOwnPropertyDescriptor,eX=Object.getOwnPropertyNames,eY=Object.getPrototypeOf,eQ=Object.prototype.hasOwnProperty,eZ=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of eX(t))eQ.call(e,i)||i===r||eW(e,i,{get:()=>t[i],enumerable:!(n=eK(t,i))||n.enumerable});return e},eJ=(e,t,r)=>(r=null!=e?eG(eY(e)):{},eZ(!t&&e&&e.__esModule?r:eW(r,"default",{value:e,enumerable:!0}),e)),e0=(n={"node_modules/classnames/index.js"(e,t){!function(){var e={}.hasOwnProperty;function r(){for(var t=[],n=0;n<arguments.length;n++){var i=arguments[n];if(i){var o=typeof i;if("string"===o||"number"===o)t.push(i);else if(Array.isArray(i)){if(i.length){var s=r.apply(null,i);s&&t.push(s)}}else if("object"===o){if(i.toString!==Object.prototype.toString&&!i.toString.toString().includes("[native code]")){t.push(i.toString());continue}for(var a in i)e.call(i,a)&&i[a]&&t.push(a)}}}return t.join(" ")}void 0!==t&&t.exports?(r.default=r,t.exports=r):"function"==typeof define&&"object"==typeof define.amd&&define.amd?define("classnames",[],function(){return r}):window.classNames=r}()}},function(){return i||(0,n[eX(n)[0]])((i={exports:{}}).exports,i),i.exports}),e1=eJ(e0(),1),e2="object"==typeof global&&global&&global.Object===Object&&global,e3="object"==typeof self&&self&&self.Object===Object&&self,e4=e2||e3||Function("return this")(),e5=e4.Symbol,e6=Object.prototype,e7=e6.hasOwnProperty,e8=e6.toString,e9=e5?e5.toStringTag:void 0,te=function(e){var t=e7.call(e,e9),r=e[e9];try{e[e9]=void 0;var n=!0}catch(e){}var i=e8.call(e);return n&&(t?e[e9]=r:delete e[e9]),i},tt=Object.prototype.toString,tr=e5?e5.toStringTag:void 0,tn=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":tr&&tr in Object(e)?te(e):tt.call(e)},ti=function(e){return null!=e&&"object"==typeof e},to=function(e,t){for(var r=-1,n=null==e?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i},ts=Array.isArray,ta=1/0,tu=e5?e5.prototype:void 0,tl=tu?tu.toString:void 0,tc=function e(t){if("string"==typeof t)return t;if(ts(t))return to(t,e)+"";if("symbol"==typeof t||ti(t)&&"[object Symbol]"==tn(t))return tl?tl.call(t):"";var r=t+"";return"0"==r&&1/t==-ta?"-0":r},td=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},tg=function(e){if(!td(e))return!1;var t=tn(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},th=e4["__core-js_shared__"],tf=(s=/[^.]+$/.exec(th&&th.keys&&th.keys.IE_PROTO||""))?"Symbol(src)_1."+s:"",tp=Function.prototype.toString,tv=function(e){if(null!=e){try{return tp.call(e)}catch(e){}try{return e+""}catch(e){}}return""},ty=/^\[object .+?Constructor\]$/,tm=Object.prototype,tb=Function.prototype.toString,tO=tm.hasOwnProperty,tD=RegExp("^"+tb.call(tO).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),tT=function(e,t){var r,n=null==e?void 0:e[t];return td(r=n)&&(!tf||!(tf in r))&&(tg(r)?tD:ty).test(tv(r))?n:void 0},tS=tT(e4,"WeakMap"),tI=function(e,t,r,n){for(var i=e.length,o=r+(n?1:-1);n?o--:++o<i;)if(t(e[o],o,e))return o;return -1},tw=function(e){return e!=e},tE=function(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return -1},tC=function(e,t){return!!(null==e?0:e.length)&&(t==t?tE(e,t,0):tI(e,tw,0))>-1},t_=/^(?:0|[1-9]\d*)$/,tj=function(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&t_.test(e))&&e>-1&&e%1==0&&e<t},tx=function(e,t){return e===t||e!=e&&t!=t},tP=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991},tN=Object.prototype,tR=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||tN)},tA=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n},tM=function(e){return ti(e)&&"[object Arguments]"==tn(e)},tk=Object.prototype,tL=tk.hasOwnProperty,tH=tk.propertyIsEnumerable,tU=tM(function(){return arguments}())?tM:function(e){return ti(e)&&tL.call(e,"callee")&&!tH.call(e,"callee")},tF="object"==typeof exports&&exports&&!exports.nodeType&&exports,tB=tF&&"object"==typeof module&&module&&!module.nodeType&&module,t$=tB&&tB.exports===tF?e4.Buffer:void 0,tz=(t$?t$.isBuffer:void 0)||function(){return!1},tq={};tq["[object Float32Array]"]=tq["[object Float64Array]"]=tq["[object Int8Array]"]=tq["[object Int16Array]"]=tq["[object Int32Array]"]=tq["[object Uint8Array]"]=tq["[object Uint8ClampedArray]"]=tq["[object Uint16Array]"]=tq["[object Uint32Array]"]=!0,tq["[object Arguments]"]=tq["[object Array]"]=tq["[object ArrayBuffer]"]=tq["[object Boolean]"]=tq["[object DataView]"]=tq["[object Date]"]=tq["[object Error]"]=tq["[object Function]"]=tq["[object Map]"]=tq["[object Number]"]=tq["[object Object]"]=tq["[object RegExp]"]=tq["[object Set]"]=tq["[object String]"]=tq["[object WeakMap]"]=!1;var tV="object"==typeof exports&&exports&&!exports.nodeType&&exports,tG=tV&&"object"==typeof module&&module&&!module.nodeType&&module,tW=tG&&tG.exports===tV&&e2.process,tK=function(){try{var e=tG&&tG.require&&tG.require("util").types;if(e)return e;return tW&&tW.binding&&tW.binding("util")}catch(e){}}(),tX=tK&&tK.isTypedArray,tY=tX?function(e){return tX(e)}:function(e){return ti(e)&&tP(e.length)&&!!tq[tn(e)]},tQ=Object.prototype.hasOwnProperty,tZ=function(e,t){var r=ts(e),n=!r&&tU(e),i=!r&&!n&&tz(e),o=!r&&!n&&!i&&tY(e),s=r||n||i||o,a=s?tA(e.length,String):[],u=a.length;for(var l in e)(t||tQ.call(e,l))&&!(s&&("length"==l||i&&("offset"==l||"parent"==l)||o&&("buffer"==l||"byteLength"==l||"byteOffset"==l)||tj(l,u)))&&a.push(l);return a},tJ=(a=Object.keys,u=Object,function(e){return a(u(e))}),t0=Object.prototype.hasOwnProperty,t1=function(e){if(!tR(e))return tJ(e);var t=[];for(var r in Object(e))t0.call(e,r)&&"constructor"!=r&&t.push(r);return t},t2=function(e){return null!=e&&tP(e.length)&&!tg(e)?tZ(e):t1(e)},t3=tT(Object,"create"),t4=Object.prototype.hasOwnProperty,t5=Object.prototype.hasOwnProperty;function t6(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}t6.prototype.clear=function(){this.__data__=t3?t3(null):{},this.size=0},t6.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},t6.prototype.get=function(e){var t=this.__data__;if(t3){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return t4.call(t,e)?t[e]:void 0},t6.prototype.has=function(e){var t=this.__data__;return t3?void 0!==t[e]:t5.call(t,e)},t6.prototype.set=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=t3&&void 0===t?"__lodash_hash_undefined__":t,this};var t7=function(e,t){for(var r=e.length;r--;)if(tx(e[r][0],t))return r;return -1},t8=Array.prototype.splice;function t9(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}t9.prototype.clear=function(){this.__data__=[],this.size=0},t9.prototype.delete=function(e){var t=this.__data__,r=t7(t,e);return!(r<0)&&(r==t.length-1?t.pop():t8.call(t,r,1),--this.size,!0)},t9.prototype.get=function(e){var t=this.__data__,r=t7(t,e);return r<0?void 0:t[r][1]},t9.prototype.has=function(e){return t7(this.__data__,e)>-1},t9.prototype.set=function(e,t){var r=this.__data__,n=t7(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this};var re=tT(e4,"Map"),rt=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e},rr=function(e,t){var r=e.__data__;return rt(t)?r["string"==typeof t?"string":"hash"]:r.map};function rn(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}rn.prototype.clear=function(){this.size=0,this.__data__={hash:new t6,map:new(re||t9),string:new t6}},rn.prototype.delete=function(e){var t=rr(this,e).delete(e);return this.size-=t?1:0,t},rn.prototype.get=function(e){return rr(this,e).get(e)},rn.prototype.has=function(e){return rr(this,e).has(e)},rn.prototype.set=function(e,t){var r=rr(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this};var ri=function(e){return null==e?"":tc(e)},ro=function(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e};function rs(e){var t=this.__data__=new t9(e);this.size=t.size}rs.prototype.clear=function(){this.__data__=new t9,this.size=0},rs.prototype.delete=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r},rs.prototype.get=function(e){return this.__data__.get(e)},rs.prototype.has=function(e){return this.__data__.has(e)},rs.prototype.set=function(e,t){var r=this.__data__;if(r instanceof t9){var n=r.__data__;if(!re||n.length<199)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new rn(n)}return r.set(e,t),this.size=r.size,this};var ra=function(e,t){for(var r=-1,n=null==e?0:e.length,i=0,o=[];++r<n;){var s=e[r];t(s,r,e)&&(o[i++]=s)}return o},ru=Object.prototype.propertyIsEnumerable,rl=Object.getOwnPropertySymbols,rc=rl?function(e){return null==e?[]:ra(rl(e=Object(e)),function(t){return ru.call(e,t)})}:function(){return[]},rd=function(e,t,r){var n=t(e);return ts(e)?n:ro(n,r(e))},rg=function(e){return rd(e,t2,rc)},rh=tT(e4,"DataView"),rf=tT(e4,"Promise"),rp=tT(e4,"Set"),rv="[object Map]",ry="[object Promise]",rm="[object Set]",rb="[object WeakMap]",rO="[object DataView]",rD=tv(rh),rT=tv(re),rS=tv(rf),rI=tv(rp),rw=tv(tS),rE=tn;(rh&&rE(new rh(new ArrayBuffer(1)))!=rO||re&&rE(new re)!=rv||rf&&rE(rf.resolve())!=ry||rp&&rE(new rp)!=rm||tS&&rE(new tS)!=rb)&&(rE=function(e){var t=tn(e),r="[object Object]"==t?e.constructor:void 0,n=r?tv(r):"";if(n)switch(n){case rD:return rO;case rT:return rv;case rS:return ry;case rI:return rm;case rw:return rb}return t});var rC=rE,r_=e4.Uint8Array;function rj(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new rn;++t<r;)this.add(e[t])}rj.prototype.add=rj.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},rj.prototype.has=function(e){return this.__data__.has(e)};var rx=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1},rP=function(e,t){return e.has(t)},rN=function(e,t,r,n,i,o){var s=1&r,a=e.length,u=t.length;if(a!=u&&!(s&&u>a))return!1;var l=o.get(e),c=o.get(t);if(l&&c)return l==t&&c==e;var d=-1,g=!0,h=2&r?new rj:void 0;for(o.set(e,t),o.set(t,e);++d<a;){var f=e[d],p=t[d];if(n)var v=s?n(p,f,d,t,e,o):n(f,p,d,e,t,o);if(void 0!==v){if(v)continue;g=!1;break}if(h){if(!rx(t,function(e,t){if(!rP(h,t)&&(f===e||i(f,e,r,n,o)))return h.push(t)})){g=!1;break}}else if(!(f===p||i(f,p,r,n,o))){g=!1;break}}return o.delete(e),o.delete(t),g},rR=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e,n){r[++t]=[n,e]}),r},rA=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e){r[++t]=e}),r},rM=e5?e5.prototype:void 0,rk=rM?rM.valueOf:void 0,rL=function(e,t,r,n,i,o,s){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":if(e.byteLength!=t.byteLength||!o(new r_(e),new r_(t)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return tx(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var a=rR;case"[object Set]":var u=1&n;if(a||(a=rA),e.size!=t.size&&!u)break;var l=s.get(e);if(l)return l==t;n|=2,s.set(e,t);var c=rN(a(e),a(t),n,i,o,s);return s.delete(e),c;case"[object Symbol]":if(rk)return rk.call(e)==rk.call(t)}return!1},rH=Object.prototype.hasOwnProperty,rU=function(e,t,r,n,i,o){var s=1&r,a=rg(e),u=a.length;if(u!=rg(t).length&&!s)return!1;for(var l=u;l--;){var c=a[l];if(!(s?c in t:rH.call(t,c)))return!1}var d=o.get(e),g=o.get(t);if(d&&g)return d==t&&g==e;var h=!0;o.set(e,t),o.set(t,e);for(var f=s;++l<u;){var p=e[c=a[l]],v=t[c];if(n)var y=s?n(v,p,c,t,e,o):n(p,v,c,e,t,o);if(!(void 0===y?p===v||i(p,v,r,n,o):y)){h=!1;break}f||(f="constructor"==c)}if(h&&!f){var m=e.constructor,b=t.constructor;m!=b&&"constructor"in e&&"constructor"in t&&!("function"==typeof m&&m instanceof m&&"function"==typeof b&&b instanceof b)&&(h=!1)}return o.delete(e),o.delete(t),h},rF="[object Arguments]",rB="[object Array]",r$="[object Object]",rz=Object.prototype.hasOwnProperty,rq=function(e,t,r,n,i,o){var s=ts(e),a=ts(t),u=s?rB:rC(e),l=a?rB:rC(t);u=u==rF?r$:u,l=l==rF?r$:l;var c=u==r$,d=l==r$,g=u==l;if(g&&tz(e)){if(!tz(t))return!1;s=!0,c=!1}if(g&&!c)return o||(o=new rs),s||tY(e)?rN(e,t,r,n,i,o):rL(e,t,u,r,n,i,o);if(!(1&r)){var h=c&&rz.call(e,"__wrapped__"),f=d&&rz.call(t,"__wrapped__");if(h||f){var p=h?e.value():e,v=f?t.value():t;return o||(o=new rs),i(p,v,r,n,o)}}return!!g&&(o||(o=new rs),rU(e,t,r,n,i,o))},rV=function(e,t,r){for(var n=-1,i=null==e?0:e.length;++n<i;)if(r(t,e[n]))return!0;return!1},rG=(l={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},function(e){return null==l?void 0:l[e]}),rW=/[&<>"']/g,rK=RegExp(rW.source),rX=/[\\^$.*+?()[\]{}|]/g,rY=RegExp(rX.source),rQ=rp&&1/rA(new rp([,-0]))[1]==1/0?function(e){return new rp(e)}:function(){},rZ=function(e,t,r){var n=-1,i=tC,o=e.length,s=!0,a=[],u=a;if(r)s=!1,i=rV;else if(o>=200){var l=t?null:rQ(e);if(l)return rA(l);s=!1,i=rP,u=new rj}else u=t?[]:a;e:for(;++n<o;){var c=e[n],d=t?t(c):c;if(c=r||0!==c?c:0,s&&d==d){for(var g=u.length;g--;)if(u[g]===d)continue e;t&&u.push(d),a.push(c)}else i(u,d,r)||(u!==a&&u.push(d),a.push(c))}return a},rJ={ENTER:[10,13],TAB:9,BACKSPACE:8,UP_ARROW:38,DOWN_ARROW:40,SPACE:32},r0={ENTER:"Enter",TAB:"Tab",COMMA:",",SPACE:" ",SEMICOLON:";"},r1={tags:"ReactTags__tags",tagInput:"ReactTags__tagInput",tagInputField:"ReactTags__tagInputField",selected:"ReactTags__selected",tag:"ReactTags__tag",remove:"ReactTags__remove",suggestions:"ReactTags__suggestions",activeSuggestion:"ReactTags__activeSuggestion",editTagInput:"ReactTags__editTagInput",editTagInputField:"ReactTags__editTagInputField",clearAll:"ReactTags__clearAll"},r2={INLINE:"inline",TOP:"top",BOTTOM:"bottom"},r3={TAG_LIMIT:"Tag limit reached!"};function r4(e){let{moveTag:t,readOnly:r,allowDragDrop:n}=e;return void 0!==t&&!r&&n}var r5=e=>{let{readOnly:t,removeComponent:r,onRemove:n,className:i,tag:o,index:s}=e,a=e=>{if(rJ.ENTER.includes(e.keyCode)||e.keyCode===rJ.SPACE){e.preventDefault(),e.stopPropagation();return}e.keyCode===rJ.BACKSPACE&&n(e)};if(t)return(0,g.jsx)("span",{});let u=`Tag at index ${s} with value ${o.id} focussed. Press backspace to remove`;return r?(0,g.jsx)(r,{"data-testid":"remove",onRemove:n,onKeyDown:a,className:i,"aria-label":u,tag:o,index:s}):(0,g.jsx)("button",{"data-testid":"remove",onClick:n,onKeyDown:a,className:i,type:"button","aria-label":u,children:(0,g.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",height:"12",width:"12",fill:"#fff",children:(0,g.jsx)("path",{d:"M376.6 84.5c11.3-13.6 9.5-33.8-4.1-45.1s-33.8-9.5-45.1 4.1L192 206 56.6 43.5C45.3 29.9 25.1 28.1 11.5 39.4S-3.9 70.9 7.4 84.5L150.3 256 7.4 427.5c-11.3 13.6-9.5 33.8 4.1 45.1s33.8 9.5 45.1-4.1L192 306 327.4 468.5c11.3 13.6 31.5 15.4 45.1 4.1s15.4-31.5 4.1-45.1L233.7 256 376.6 84.5z"})})})},r6=e=>{let t=(0,en.useRef)(null),{readOnly:r=!1,tag:n,classNames:i,index:o,moveTag:s,allowDragDrop:a=!0,labelField:u="text",tags:l}=e,[{isDragging:c},d]=function(e,t){let r=eP(e,t);m(!r.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");let n=function(){let e=eL();return(0,en.useMemo)(()=>new eF(e),[e])}(),i=function(e,t){let r=eL(),n=(0,en.useMemo)(()=>new ek(r.getBackend()),[r]);return ej(()=>(n.dragSourceOptions=e||null,n.reconnect(),()=>n.disconnectDragSource()),[n,e]),ej(()=>(n.dragPreviewOptions=t||null,n.reconnect(),()=>n.disconnectDragPreview()),[n,t]),n}(r.options,r.previewOptions);return!function(e,t,r){let n=eL(),i=function(e,t,r){let n=(0,en.useMemo)(()=>new eB(e,t,r),[t,r]);return(0,en.useEffect)(()=>{n.spec=e},[e]),n}(e,t,r),o=(0,en.useMemo)(()=>{let t=e.type;return m(null!=t,"spec.type must be defined"),t},[e]);ej(function(){if(null!=o){let[e,s]=function(e,t,r){let n=r.getRegistry(),i=n.addSource(e,t);return[i,()=>n.removeSource(i)]}(o,i,n);return t.receiveHandlerId(e),r.receiveHandlerId(e),s}},[n,t,r,i,o])}(r,n,i),[ex(r.collect,n,i),(0,en.useMemo)(()=>i.hooks.dragSource(),[i]),(0,en.useMemo)(()=>i.hooks.dragPreview(),[i])]}(()=>({type:"tag",collect:e=>({isDragging:!!e.isDragging()}),item:e,canDrag:()=>r4({moveTag:s,readOnly:r,allowDragDrop:a})}),[l]),[,h]=function(e,t){let r=eP(e,t),n=function(){let e=eL();return(0,en.useMemo)(()=>new eq(e),[e])}(),i=function(e){let t=eL(),r=(0,en.useMemo)(()=>new e$(t.getBackend()),[t]);return ej(()=>(r.dropTargetOptions=e||null,r.reconnect(),()=>r.disconnectDropTarget()),[e]),r}(r.options);return!function(e,t,r){let n=eL(),i=function(e,t){let r=(0,en.useMemo)(()=>new eV(e,t),[t]);return(0,en.useEffect)(()=>{r.spec=e},[e]),r}(e,t),o=function(e){let{accept:t}=e;return(0,en.useMemo)(()=>(m(null!=e.accept,"accept must be defined"),Array.isArray(t)?t:[t]),[t])}(e);ej(function(){let[e,s]=function(e,t,r){let n=r.getRegistry(),i=n.addTarget(e,t);return[i,()=>n.removeTarget(i)]}(o,i,n);return t.receiveHandlerId(e),r.receiveHandlerId(e),s},[n,t,i,r,o.map(e=>e.toString()).join("|")])}(r,n,i),[ex(r.collect,n,i),(0,en.useMemo)(()=>i.hooks.dropTarget(),[i])]}(()=>({accept:"tag",drop:t=>{let r=t.index;r!==o&&e?.moveTag?.(r,o)},canDrop:e=>(function(e){let{readOnly:t,allowDragDrop:r}=e;return!t&&r})(e)}),[l]);d(h(t));let f=e.tag[u],{className:p=""}=n;return(0,g.jsxs)("span",{ref:t,className:(0,e1.default)("tag-wrapper",i.tag,p),style:{opacity:c?0:1,cursor:r4({moveTag:s,readOnly:r,allowDragDrop:a})?"move":"auto"},"data-testid":"tag",onClick:e.onTagClicked,onTouchStart:e.onTagClicked,children:[f,(0,g.jsx)(r5,{tag:e.tag,className:i.remove,removeComponent:e.removeComponent,onRemove:e.onDelete,readOnly:r,index:o})]})},r7=e=>(0,g.jsx)("button",{"aria-label":e["aria-label"],className:e.classNames.clearAll,onClick:e.onClick,children:"Clear all"}),r8=(e,t)=>{let r=t.offsetHeight,n=e.offsetHeight,i=e.offsetTop-t.scrollTop;i+n>=r?t.scrollTop+=i-r+n:i<0&&(t.scrollTop+=i)},r9=(e,t,r,n)=>"function"==typeof n?n(e):e.length>=t&&r,ne=(0,en.memo)(e=>{let t=(0,en.createRef)(),{labelField:r,minQueryLength:n,isFocused:i,classNames:o,selectedIndex:s,query:a}=e;(0,en.useEffect)(()=>{if(!t.current)return;let e=t.current.querySelector(`.${o.activeSuggestion}`);e&&r8(e,t.current)},[s]);let u=(e,t)=>{let n=t.trim().replace(/[-\\^$*+?.()|[\]{}]/g,"\\$&"),{[r]:i}=e;return{__html:i.replace(RegExp(n,"gi"),e=>{var t;return`<mark>${(t=ri(t=e))&&rK.test(t)?t.replace(rW,rG):t}</mark>`})}},l=(t,r)=>"function"==typeof e.renderSuggestion?e.renderSuggestion(t,r):(0,g.jsx)("span",{dangerouslySetInnerHTML:u(t,r)}),c=e.suggestions.map((t,r)=>(0,g.jsx)("li",{onMouseDown:e.handleClick.bind(null,r),onTouchStart:e.handleClick.bind(null,r),onMouseOver:e.handleHover.bind(null,r),className:r===e.selectedIndex?e.classNames.activeSuggestion:"",children:l(t,e.query)},r));return 0!==c.length&&r9(a,n||2,i,e.shouldRenderSuggestions)?(0,g.jsx)("div",{ref:t,className:o.suggestions,"data-testid":"suggestions",children:(0,g.jsxs)("ul",{children:[" ",c," "]})}):null},(e,t)=>{let{query:r,minQueryLength:n=2,isFocused:i,suggestions:o}=t;return!!(e.isFocused===i&&function e(t,r,n,i,o){return t===r||(null!=t&&null!=r&&(ti(t)||ti(r))?rq(t,r,n,i,e,o):t!=t&&r!=r)}(e.suggestions,o))&&r9(r,n,i,t.shouldRenderSuggestions)===r9(e.query,e.minQueryLength??2,e.isFocused,e.shouldRenderSuggestions)&&e.selectedIndex===t.selectedIndex}),nt=eJ(e0(),1),nr=e=>{let{autofocus:t,autoFocus:r,readOnly:n,labelField:i,allowDeleteFromEmptyInput:o,allowAdditionFromPaste:s,allowDragDrop:a,minQueryLength:u,shouldRenderSuggestions:l,removeComponent:c,autocomplete:d,inline:h,maxTags:f,allowUnique:p,editable:v,placeholder:y,delimiters:m,separators:b,tags:O,inputFieldPosition:D,inputProps:T,classNames:S,maxLength:I,inputValue:w,clearAll:E,ariaAttrs:C}=e,[_,j]=(0,en.useState)(e.suggestions),[x,P]=(0,en.useState)(""),[N,R]=(0,en.useState)(!1),[A,M]=(0,en.useState)(-1),[k,L]=(0,en.useState)(!1),[H,U]=(0,en.useState)(""),[F,B]=(0,en.useState)(-1),[$,z]=(0,en.useState)(""),q=(0,en.createRef)(),V=(0,en.useRef)(null),G=(0,en.useRef)(null);(0,en.useEffect)(()=>{m.length&&console.warn("[Deprecation] The delimiters prop is deprecated and will be removed in v7.x.x, please use separators instead. If you have any concerns regarding this, please share your thoughts in https://github.com/react-tags/react-tags/issues/960")},[]),(0,en.useEffect)(()=>{void 0!==h&&console.warn("[Deprecation] The inline attribute is deprecated and will be removed in v7.x.x, please use inputFieldPosition instead.")},[h]),(0,en.useEffect)(()=>{void 0!==t&&console.warn("[Deprecated] autofocus prop will be removed in 7.x so please migrate to autoFocus prop."),(t||r&&!1!==t)&&!n&&X()},[r,r,n]),(0,en.useEffect)(()=>{ee()},[x,e.suggestions]);let W=t=>{let r=e.suggestions.slice();if(p){let e=O.map(e=>e.id.trim().toLowerCase());r=r.filter(t=>!e.includes(t.id.toLowerCase()))}if(e.handleFilterSuggestions)return e.handleFilterSuggestions(t,r);let n=r.filter(e=>0===K(t,e)),i=r.filter(e=>K(t,e)>0);return n.concat(i)},K=(e,t)=>t[i].toLowerCase().indexOf(e.toLowerCase()),X=()=>{P(""),V.current&&(V.current.value="",V.current.focus())},Y=(t,r)=>{r.preventDefault(),r.stopPropagation();let n=O.slice();0!==n.length&&(z(""),e?.handleDelete?.(t,r),Q(t,n))},Q=(e,t)=>{if(!q?.current)return;let r=q.current.querySelectorAll(".ReactTags__remove"),n="";0===e&&t.length>1?(n=`Tag at index ${e} with value ${t[e].id} deleted. Tag at index 0 with value ${t[1].id} focussed. Press backspace to remove`,r[0].focus()):e>0?(n=`Tag at index ${e} with value ${t[e].id} deleted. Tag at index ${e-1} with value ${t[e-1].id} focussed. Press backspace to remove`,r[e-1].focus()):(n=`Tag at index ${e} with value ${t[e].id} deleted. Input focussed. Press enter to add a new tag`,V.current?.focus()),U(n)},Z=(t,r,o)=>{n||(v&&(B(t),P(r[i]),G.current?.focus()),e.handleTagClick?.(t,o))},J=t=>{e.handleInputChange&&e.handleInputChange(t.target.value,t),P(t.target.value.trim())},ee=()=>{let e=W(x);j(e),M(A>=e.length?e.length-1:A)},et=t=>{let r=t.target.value;e.handleInputFocus&&e.handleInputFocus(r,t),R(!0)},er=t=>{let r=t.target.value;e.handleInputBlur&&(e.handleInputBlur(r,t),V.current&&(V.current.value="")),R(!1),B(-1)},ei=e=>{if(!e.nativeEvent.isComposing){if("Escape"===e.key&&(e.preventDefault(),e.stopPropagation(),M(-1),L(!1),j([]),B(-1)),(-1!==b.indexOf(e.key)||-1!==m.indexOf(e.keyCode))&&!e.shiftKey){(e.keyCode!==rJ.TAB||""!==x)&&e.preventDefault();let t=k&&-1!==A?_[A]:{id:x.trim(),[i]:x.trim(),className:""};Object.keys(t)&&ea(t)}"Backspace"===e.key&&""===x&&(o||D===r2.INLINE)&&Y(O.length-1,e),e.keyCode===rJ.UP_ARROW&&(e.preventDefault(),M(A<=0?_.length-1:A-1),L(!0)),e.keyCode===rJ.DOWN_ARROW&&(e.preventDefault(),L(!0),0===_.length?M(-1):M((A+1)%_.length))}},eo=()=>f&&O.length>=f,es=t=>{var r;if(!s)return;if(eo()){z(r3.TAG_LIMIT),X();return}z(""),t.preventDefault();let n=t.clipboardData||window.clipboardData,o=n.getData("text"),{maxLength:a=o.length}=e,u=Math.min(a,o.length),l=n.getData("text").substr(0,u),c=m;b.length&&(c=[],b.forEach(e=>{let t=function(e){switch(e){case r0.ENTER:return[10,13];case r0.TAB:return 9;case r0.COMMA:return 188;case r0.SPACE:return 32;case r0.SEMICOLON:return 186;default:return 0}}(e);Array.isArray(t)?c=[...c,...t]:c.push(t)}));let d=function(e){var t;let r=(t=ri(t=e.map(e=>String.fromCharCode(96<=e?e-48*Math.floor(e/48):e)).join("")))&&rY.test(t)?t.replace(rX,"\\$&"):t;return RegExp(`[${r}]+`)}(c);((r=l.split(d).map(e=>e.trim()))&&r.length?rZ(r):[]).forEach(e=>ea({id:e.trim(),[i]:e.trim(),className:""}))},ea=t=>{if(!t.id||!t[i])return;if(-1===F){if(eo()){z(r3.TAG_LIMIT),X();return}z("")}let r=O.map(e=>e.id.toLowerCase());if(!(p&&r.indexOf(t.id.trim().toLowerCase())>=0)){if(d){let e=W(t[i]);console.warn("[Deprecation] The autocomplete prop will be removed in 7.x to simplify the integration and make it more intutive. If you have any concerns regarding this, please share your thoughts in https://github.com/react-tags/react-tags/issues/949"),(1===d&&1===e.length||!0===d&&e.length)&&(t=e[0])}-1!==F&&e.onTagUpdate?e.onTagUpdate(F,t):e?.handleAddition?.(t),P(""),L(!1),M(-1),B(-1),X()}},eu=(t,r)=>{let n=O[t];e?.handleDrag?.(n,t,r)},el=(()=>{let t={...r1,...e.classNames};return O.map((e,r)=>(0,g.jsx)(en.Fragment,{children:F===r?(0,g.jsx)("div",{className:t.editTagInput,children:(0,g.jsx)("input",{ref:e=>{G.current=e},onFocus:et,value:x,onChange:J,onKeyDown:ei,onBlur:er,className:t.editTagInputField,onPaste:es,"data-testid":"tag-edit"})}):(0,g.jsx)(r6,{index:r,tag:e,tags:O,labelField:i,onDelete:e=>Y(r,e),moveTag:a?eu:void 0,removeComponent:c,onTagClicked:t=>Z(r,e,t),readOnly:n,classNames:t,allowDragDrop:a})},r))})(),ec={...r1,...S},{name:ed,id:eg}=e,eh=!1===h?r2.BOTTOM:D,ef=n?null:(0,g.jsxs)("div",{className:ec.tagInput,children:[(0,g.jsx)("input",{...T,ref:e=>{V.current=e},className:ec.tagInputField,type:"text",placeholder:y,"aria-label":y,onFocus:et,onBlur:er,onChange:J,onKeyDown:ei,onPaste:es,name:ed,id:eg,maxLength:I,value:w,"data-automation":"input","data-testid":"input"}),(0,g.jsx)(ne,{query:x.trim(),suggestions:_,labelField:i,selectedIndex:A,handleClick:e=>{ea(_[e])},handleHover:e=>{M(e),L(!0)},minQueryLength:u,shouldRenderSuggestions:l,isFocused:N,classNames:ec,renderSuggestion:e.renderSuggestion}),E&&O.length>0&&(0,g.jsx)(r7,{"aria-label":C?.clearAllLabel,classNames:ec,onClick:()=>{e.onClearAll&&e.onClearAll(),z(""),X()}}),$&&(0,g.jsxs)("div",{"data-testid":"error",className:"ReactTags__error",children:[(0,g.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",height:"24",width:"24",fill:"#e03131",children:(0,g.jsx)("path",{d:"M256 32c14.2 0 27.3 7.5 34.5 19.8l216 368c7.3 12.4 7.3 27.7 .2 40.1S486.3 480 472 480H40c-14.3 0-27.6-7.7-34.7-20.1s-7-27.8 .2-40.1l216-368C228.7 39.5 241.8 32 256 32zm0 128c-13.3 0-24 10.7-24 24V296c0 13.3 10.7 24 24 24s24-10.7 24-24V184c0-13.3-10.7-24-24-24zm32 224a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"})}),$]})]});return(0,g.jsxs)("div",{className:(0,nt.default)(ec.tags,"react-tags-wrapper"),ref:q,children:[(0,g.jsx)("p",{role:"alert",className:"sr-only",style:{position:"absolute",overflow:"hidden",clip:"rect(0 0 0 0)",margin:"-1px",padding:0,width:"1px",height:"1px",border:0},children:H}),eh===r2.TOP&&ef,(0,g.jsxs)("div",{className:ec.selected,children:[el,eh===r2.INLINE&&ef]}),eh===r2.BOTTOM&&ef]})},nn=e=>{let{placeholder:t="Press enter to add new tag",labelField:r="text",suggestions:n=[],delimiters:i=[],separators:o=e.delimiters?.length?[]:[r0.ENTER,r0.TAB],autofocus:s,autoFocus:a=!0,inline:u,inputFieldPosition:l="inline",allowDeleteFromEmptyInput:c=!1,allowAdditionFromPaste:d=!0,autocomplete:h=!1,readOnly:f=!1,allowUnique:p=!0,allowDragDrop:v=!0,tags:y=[],inputProps:m={},editable:b=!1,clearAll:O=!1,ariaAttrs:D={clearAllLabel:"clear all tags"},handleDelete:T,handleAddition:S,onTagUpdate:I,handleDrag:w,handleFilterSuggestions:E,handleTagClick:C,handleInputChange:_,handleInputFocus:j,handleInputBlur:x,minQueryLength:P,shouldRenderSuggestions:N,removeComponent:R,onClearAll:A,classNames:M,name:k,id:L,maxLength:H,inputValue:U,maxTags:F,renderSuggestion:B}=e;return(0,g.jsx)(nr,{placeholder:t,labelField:r,suggestions:n,delimiters:i,separators:o,autofocus:s,autoFocus:a,inline:u,inputFieldPosition:l,allowDeleteFromEmptyInput:c,allowAdditionFromPaste:d,autocomplete:h,readOnly:f,allowUnique:p,allowDragDrop:v,tags:y,inputProps:m,editable:b,clearAll:O,ariaAttrs:D,handleDelete:T,handleAddition:S,onTagUpdate:I,handleDrag:w,handleFilterSuggestions:E,handleTagClick:C,handleInputChange:_,handleInputFocus:j,handleInputBlur:x,minQueryLength:P,shouldRenderSuggestions:N,removeComponent:R,onClearAll:A,classNames:M,name:k,id:L,maxLength:H,inputValue:U,maxTags:F,renderSuggestion:B})},ni=({...e})=>(0,g.jsx)(ea,{backend:eC,children:(0,g.jsx)(nn,{...e})})}}]);