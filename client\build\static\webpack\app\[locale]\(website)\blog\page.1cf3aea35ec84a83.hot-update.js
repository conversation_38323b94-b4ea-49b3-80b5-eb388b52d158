"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/blog/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/BlogItem.jsx":
/*!***************************************************!*\
  !*** ./src/features/blog/components/BlogItem.jsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _assets_images_website_blog_img_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/website/blog-img.png */ \"(app-pages-browser)/./src/assets/images/website/blog-img.png\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardMedia,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardMedia,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardMedia,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardMedia/CardMedia.js\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardMedia,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* harmony import */ var html_to_text__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! html-to-text */ \"(app-pages-browser)/./node_modules/html-to-text/lib/html-to-text.mjs\");\n\nvar _s = $RefreshSig$();\n\"use client \";\n\n\n\n\n\n\nfunction BlogItem(param) {\n    let { blogData, language, withoutCategory } = param;\n    _s();\n    // Import helper functions\n    const { getBlogVersion, getBlogUrl, getBlogTitle, getBlogImage, getBlogDescription, getBlogContent } = __webpack_require__(/*! @/utils/blogHelpers */ \"(app-pages-browser)/./src/utils/blogHelpers.js\");\n    const handleClick = (event, href)=>{\n        event.preventDefault();\n        window.location.href = href;\n    };\n    const truncateDescription = (title)=>{\n        title = (0,html_to_text__WEBPACK_IMPORTED_MODULE_5__.htmlToText)(title.replace(/<a[^>]*>|<\\/a>/g, \"\"), {\n            wordwrap: false\n        });\n        const words = title.split(\" \");\n        if (words?.length >= 20) {\n            return words.slice(0, 20).join(\" \");\n        } else {\n            return title;\n        }\n    };\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    // Get version data for the current language\n    const versionData = getBlogVersion(blogData, language);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        className: \"blog-item\",\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        sx: {\n            marginBottom: \"10px\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"card\",\n            children: [\n                blogData?.category?.name && !withoutCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    locale: language === \"en\" ? \"en\" : \"fr\",\n                    href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/category/${blogData?.category?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/category/${blogData?.category?.url}`}/`,\n                    onClick: (e)=>handleClick(e, `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/category/${blogData?.category?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/category/${blogData?.category?.url}`}/`),\n                    className: \"label-category\",\n                    children: blogData?.category?.name\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                    lineNumber: 54,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    locale: language === \"en\" ? \"en\" : \"fr\",\n                    href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}`}/`,\n                    onClick: (e)=>handleClick(e, `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}`}/`),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"card-image\",\n                        component: \"img\",\n                        image: blogData?.versions[0]?.image ? `${\"http://localhost:4000/api/v1\"}/files/${blogData?.versions[0]?.image}` : _assets_images_website_blog_img_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                        alt: blogData?.versions[0]?.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"card-content\",\n                    sx: {\n                        padding: \"8px !important\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            locale: language === \"en\" ? \"en\" : \"fr\",\n                            href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}`}/`,\n                            onClick: (e)=>handleClick(e, `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}`}/`),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"blog-title\",\n                                    children: blogData?.versions[0]?.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                \" \"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"blog-description\",\n                            children: blogData?.versions[0]?.description ? blogData?.versions[0]?.description : truncateDescription(blogData?.versions[0]?.content)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, blogData?.versions[0]?.title, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogItem, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = BlogItem;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BlogItem);\nvar _c;\n$RefreshReg$(_c, \"BlogItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/BlogItem.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/blogHelpers.js":
/*!**********************************!*\
  !*** ./src/utils/blogHelpers.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBlogContent: function() { return /* binding */ getBlogContent; },\n/* harmony export */   getBlogCreatedAt: function() { return /* binding */ getBlogCreatedAt; },\n/* harmony export */   getBlogDescription: function() { return /* binding */ getBlogDescription; },\n/* harmony export */   getBlogHighlights: function() { return /* binding */ getBlogHighlights; },\n/* harmony export */   getBlogImage: function() { return /* binding */ getBlogImage; },\n/* harmony export */   getBlogKeywords: function() { return /* binding */ getBlogKeywords; },\n/* harmony export */   getBlogLanguages: function() { return /* binding */ getBlogLanguages; },\n/* harmony export */   getBlogMetaDescription: function() { return /* binding */ getBlogMetaDescription; },\n/* harmony export */   getBlogMetaTitle: function() { return /* binding */ getBlogMetaTitle; },\n/* harmony export */   getBlogPublishDate: function() { return /* binding */ getBlogPublishDate; },\n/* harmony export */   getBlogTitle: function() { return /* binding */ getBlogTitle; },\n/* harmony export */   getBlogUrl: function() { return /* binding */ getBlogUrl; },\n/* harmony export */   getBlogVersion: function() { return /* binding */ getBlogVersion; },\n/* harmony export */   getBlogVisibility: function() { return /* binding */ getBlogVisibility; },\n/* harmony export */   getPrimaryBlogLanguage: function() { return /* binding */ getPrimaryBlogLanguage; },\n/* harmony export */   hasBlogLanguage: function() { return /* binding */ hasBlogLanguage; },\n/* harmony export */   isBlogArchived: function() { return /* binding */ isBlogArchived; },\n/* harmony export */   transformBlogToArrayFormat: function() { return /* binding */ transformBlogToArrayFormat; },\n/* harmony export */   transformBlogToMapFormat: function() { return /* binding */ transformBlogToMapFormat; }\n/* harmony export */ });\n/**\n * Blog utility functions to handle the new Map-based version structure\n */ /**\n * Get the version data for a specific language from blog data\n * Handles both old array format and new Map format for backward compatibility\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code ('en' or 'fr')\n * @returns {Object|null} The version data for the specified language\n */ const getBlogVersion = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    if (!blogData || !blogData.versions) {\n        return null;\n    }\n    // Handle new Map structure (versions is an object with language keys)\n    if (typeof blogData.versions === \"object\" && !Array.isArray(blogData.versions)) {\n        return blogData.versions[language] || blogData.versions[Object.keys(blogData.versions)[0]];\n    }\n    // Handle old array structure for backward compatibility\n    if (Array.isArray(blogData.versions)) {\n        const version = blogData.versions.find((v)=>v.language === language);\n        return version || blogData.versions[0];\n    }\n    return null;\n};\n/**\n * Get all available languages for a blog post\n * @param {Object} blogData - The blog data object\n * @returns {Array} Array of language codes\n */ const getBlogLanguages = (blogData)=>{\n    if (!blogData || !blogData.versions) {\n        return [];\n    }\n    // Handle new Map structure\n    if (typeof blogData.versions === \"object\" && !Array.isArray(blogData.versions)) {\n        return Object.keys(blogData.versions);\n    }\n    // Handle old array structure\n    if (Array.isArray(blogData.versions)) {\n        return blogData.versions.map((v)=>v.language);\n    }\n    return [];\n};\n/**\n * Check if a blog has a version in a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code to check\n * @returns {boolean} True if the language version exists\n */ const hasBlogLanguage = (blogData, language)=>{\n    const languages = getBlogLanguages(blogData);\n    return languages.includes(language);\n};\n/**\n * Get the primary language for a blog post (first available language)\n * @param {Object} blogData - The blog data object\n * @returns {string} The primary language code\n */ const getPrimaryBlogLanguage = (blogData)=>{\n    const languages = getBlogLanguages(blogData);\n    return languages[0] || \"en\";\n};\n/**\n * Transform blog data from old array format to new Map format\n * @param {Object} blogData - The blog data object with array versions\n * @returns {Object} The blog data with Map-based versions\n */ const transformBlogToMapFormat = (blogData)=>{\n    if (!blogData || !blogData.versions || !Array.isArray(blogData.versions)) {\n        return blogData;\n    }\n    const versionsMap = {};\n    blogData.versions.forEach((version)=>{\n        if (version.language) {\n            versionsMap[version.language] = version;\n        }\n    });\n    return {\n        ...blogData,\n        versions: versionsMap\n    };\n};\n/**\n * Transform blog data from new Map format to old array format (for backward compatibility)\n * @param {Object} blogData - The blog data object with Map versions\n * @returns {Object} The blog data with array-based versions\n */ const transformBlogToArrayFormat = (blogData)=>{\n    if (!blogData || !blogData.versions || Array.isArray(blogData.versions)) {\n        return blogData;\n    }\n    const versionsArray = Object.values(blogData.versions);\n    return {\n        ...blogData,\n        versions: versionsArray\n    };\n};\n/**\n * Get blog URL for a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code\n * @returns {string|null} The URL for the blog in the specified language\n */ const getBlogUrl = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const version = getBlogVersion(blogData, language);\n    return version?.url || null;\n};\n/**\n * Get blog title for a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code\n * @returns {string|null} The title for the blog in the specified language\n */ const getBlogTitle = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const version = getBlogVersion(blogData, language);\n    return version?.title || null;\n};\n/**\n * Get blog image for a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code\n * @returns {string|null} The image URL for the blog in the specified language\n */ const getBlogImage = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const version = getBlogVersion(blogData, language);\n    return version?.image || null;\n};\n/**\n * Get blog content for a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code\n * @returns {string|null} The content for the blog in the specified language\n */ const getBlogContent = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const version = getBlogVersion(blogData, language);\n    return version?.content || null;\n};\n/**\n * Get blog description for a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code\n * @returns {string|null} The description for the blog in the specified language\n */ const getBlogDescription = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const version = getBlogVersion(blogData, language);\n    return version?.description || null;\n};\n/**\n * Get blog meta title for a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code\n * @returns {string|null} The meta title for the blog in the specified language\n */ const getBlogMetaTitle = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const version = getBlogVersion(blogData, language);\n    return version?.metaTitle || null;\n};\n/**\n * Get blog meta description for a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code\n * @returns {string|null} The meta description for the blog in the specified language\n */ const getBlogMetaDescription = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const version = getBlogVersion(blogData, language);\n    return version?.metaDescription || null;\n};\n/**\n * Get blog creation date for a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code\n * @returns {Date|null} The creation date for the blog in the specified language\n */ const getBlogCreatedAt = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const version = getBlogVersion(blogData, language);\n    return version?.createdAt || null;\n};\n/**\n * Get blog publish date for a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code\n * @returns {Date|null} The publish date for the blog in the specified language\n */ const getBlogPublishDate = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const version = getBlogVersion(blogData, language);\n    return version?.publishDate || null;\n};\n/**\n * Check if blog is archived for a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code\n * @returns {boolean} True if the blog is archived in the specified language\n */ const isBlogArchived = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const version = getBlogVersion(blogData, language);\n    return version?.isArchived || false;\n};\n/**\n * Get blog visibility for a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code\n * @returns {string|null} The visibility status for the blog in the specified language\n */ const getBlogVisibility = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const version = getBlogVersion(blogData, language);\n    return version?.visibility || null;\n};\n/**\n * Get blog keywords for a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code\n * @returns {Array} The keywords array for the blog in the specified language\n */ const getBlogKeywords = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const version = getBlogVersion(blogData, language);\n    return version?.keywords || [];\n};\n/**\n * Get blog highlights for a specific language\n * @param {Object} blogData - The blog data object\n * @param {string} language - The language code\n * @returns {Array} The highlights array for the blog in the specified language\n */ const getBlogHighlights = function(blogData) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const version = getBlogVersion(blogData, language);\n    return version?.highlights || [];\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/blogHelpers.js\n"));

/***/ })

});