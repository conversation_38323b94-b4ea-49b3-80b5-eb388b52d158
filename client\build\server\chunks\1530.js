"use strict";exports.id=1530,exports.ids=[1530],exports.modules={66042:(e,t,a)=>{a.d(t,{Z:()=>i});var s=a(27522),l=a(10326);let i=(0,s.Z)((0,l.jsx)("path",{d:"M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5M12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5m0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3"}),"Visibility")},96741:(e,t,a)=>{a.d(t,{Z:()=>i});var s=a(27522),l=a(10326);let i=(0,s.Z)((0,l.jsx)("path",{d:"M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7M2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2m4.31-.78 3.15 3.15.02-.16c0-1.66-1.34-3-3-3z"}),"VisibilityOff")},57329:(e,t,a)=>{a.d(t,{Z:()=>Z});var s,l=a(17577),i=a(41135),r=a(88634),n=a(54641),o=a(25609),d=a(45011),c=a(65656),m=a(91703),h=a(30990),p=a(2791),u=a(71685),g=a(97898);function f(e){return(0,g.ZP)("MuiInputAdornment",e)}let v=(0,u.Z)("MuiInputAdornment",["root","filled","standard","outlined","positionStart","positionEnd","disablePointerEvents","hiddenLabel","sizeSmall"]);var x=a(10326);let j=e=>{let{classes:t,disablePointerEvents:a,hiddenLabel:s,position:l,size:i,variant:o}=e,d={root:["root",a&&"disablePointerEvents",l&&`position${(0,n.Z)(l)}`,o,s&&"hiddenLabel",i&&`size${(0,n.Z)(i)}`]};return(0,r.Z)(d,f,t)},b=(0,m.ZP)("div",{name:"MuiInputAdornment",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:a}=e;return[t.root,t[`position${(0,n.Z)(a.position)}`],!0===a.disablePointerEvents&&t.disablePointerEvents,t[a.variant]]}})((0,h.Z)(({theme:e})=>({display:"flex",maxHeight:"2em",alignItems:"center",whiteSpace:"nowrap",color:(e.vars||e).palette.action.active,variants:[{props:{variant:"filled"},style:{[`&.${v.positionStart}&:not(.${v.hiddenLabel})`]:{marginTop:16}}},{props:{position:"start"},style:{marginRight:8}},{props:{position:"end"},style:{marginLeft:8}},{props:{disablePointerEvents:!0},style:{pointerEvents:"none"}}]}))),Z=l.forwardRef(function(e,t){let a=(0,p.i)({props:e,name:"MuiInputAdornment"}),{children:r,className:n,component:m="div",disablePointerEvents:h=!1,disableTypography:u=!1,position:g,variant:f,...v}=a,Z=(0,c.Z)()||{},y=f;f&&Z.variant,Z&&!y&&(y=Z.variant);let N={...a,hiddenLabel:Z.hiddenLabel,size:Z.size,disablePointerEvents:h,position:g,variant:y},A=j(N);return(0,x.jsx)(d.Z.Provider,{value:null,children:(0,x.jsx)(b,{as:m,ownerState:N,className:(0,i.Z)(A.root,n),ref:t,...v,children:"string"!=typeof r||u?(0,x.jsxs)(l.Fragment,{children:["start"===g?s||(s=(0,x.jsx)("span",{className:"notranslate","aria-hidden":!0,children:"​"})):null,r]}):(0,x.jsx)(o.default,{color:"textSecondary",children:r})})})})},5926:(e,t,a)=>{a.d(t,{Z:()=>i});var s=a(10326),l=a(17577);a(11148);let i=({src:e,alt:t})=>{let[a,i]=(0,l.useState)(!1),r=(0,l.useRef)();return(0,l.useEffect)(()=>{let e=new IntersectionObserver(([t])=>{t.isIntersecting&&(i(!0),e.unobserve(t.target))},{threshold:.1});return r.current&&e.observe(r.current),()=>e.disconnect()},[]),s.jsx("img",{ref:r,src:a?e:void 0,"data-src":e,alt:t,loading:"lazy",style:{opacity:a?1:.5,transition:"opacity 0.3s"}})}},46127:(e,t,a)=>{a.d(t,{Z:()=>v});var s,l,i,r,n,o,d=a(10326),c=a(15082),m=a(95746);function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let p=e=>m.createElement("svg",h({xmlns:"http://www.w3.org/2000/svg",width:25,height:24,fill:"none"},e),s||(s=m.createElement("g",{clipPath:"url(#coloredMicrosoft_svg__a)"},m.createElement("path",{fill:"#fff",d:"M3.5 3h18v18h-18z"}),m.createElement("path",{fill:"#F35325",d:"M4.281 3.782h7.826v7.826H4.281z"}),m.createElement("path",{fill:"#81BC06",d:"M12.89 3.782h7.827v7.826H12.89z"}),m.createElement("path",{fill:"#05A6F0",d:"M4.281 12.392h7.826v7.826H4.281z"}),m.createElement("path",{fill:"#FFBA08",d:"M12.89 12.392h7.827v7.826H12.89z"}))),l||(l=m.createElement("defs",null,m.createElement("clipPath",{id:"coloredMicrosoft_svg__a"},m.createElement("path",{fill:"#fff",d:"M3.5 3h18v18h-18z"})))));function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let g=e=>m.createElement("svg",u({xmlns:"http://www.w3.org/2000/svg",width:25,height:24,fill:"none"},e),i||(i=m.createElement("path",{fill:"#FFC107",d:"M22.306 10.042H21.5V10h-9v4h5.651A5.998 5.998 0 0 1 6.5 12a6 6 0 0 1 6-6c1.53 0 2.921.577 3.98 1.52L19.31 4.69A9.95 9.95 0 0 0 12.5 2c-5.522 0-10 4.478-10 10s4.478 10 10 10 10-4.477 10-10c0-.67-.069-1.325-.195-1.959"})),r||(r=m.createElement("path",{fill:"#FF3D00",d:"m3.652 7.346 3.286 2.409A6 6 0 0 1 12.499 6c1.53 0 2.921.577 3.98 1.52l2.83-2.829A9.95 9.95 0 0 0 12.498 2a9.99 9.99 0 0 0-8.847 5.346"})),n||(n=m.createElement("path",{fill:"#4CAF50",d:"M12.5 22c2.583 0 4.93-.988 6.705-2.596l-3.095-2.619A5.96 5.96 0 0 1 12.5 18a6 6 0 0 1-5.641-3.973L3.598 16.54C5.253 19.778 8.614 22 12.5 22"})),o||(o=m.createElement("path",{fill:"#1976D2",d:"M22.306 10.042H21.5V10h-9v4h5.651a6 6 0 0 1-2.043 2.785h.002l3.095 2.619C18.985 19.602 22.5 17 22.5 12c0-.67-.069-1.325-.195-1.959"})));var f=a(97980);let v=function({type:e,t,redirection:a,removeBottomSection:s,locale:l}){let i=async()=>{window.location.href=`${process.env.NEXT_PUBLIC_BASE_API_URL}/auth/google?state=${encodeURIComponent(JSON.stringify({isSignup:"register"===e,redirection:a}))}`},r=async()=>{window.location.href=`${process.env.NEXT_PUBLIC_BASE_API_URL}/auth/microsoft?state=${encodeURIComponent(JSON.stringify({isSignup:"register"===e,redirection:a}))}`};return(0,d.jsxs)("div",{id:"connect-social-media",children:[(0,d.jsxs)("div",{className:"top-section",children:[d.jsx("span",{className:"line"}),d.jsx("p",{className:"title",children:t("register"==e?"register:registerWith":"login:orConnect")}),d.jsx("span",{className:"line"})]}),(0,d.jsxs)("div",{className:"btns-section",children:[d.jsx(c.default,{leftIcon:!0,icon:d.jsx(g,{}),text:"Google",onClick:i,className:"btn-social-media"}),d.jsx(c.default,{leftIcon:!0,icon:d.jsx(p,{}),text:"Microsoft",onClick:r,className:"btn-social-media"})]}),!s&&(0,d.jsxs)("div",{className:"bottom-section",children:[t("register"==e?"register:haveAnAccount":"login:haveAnAccount"),"register"==e?d.jsx(c.default,{text:t("login:login"),link:`/${f.jb.login.route}`,locale:l,className:"btn yellow text-left"}):d.jsx(c.default,{text:t("register:register"),link:`/${f.jb.register.route}`,locale:l,className:"btn yellow text-left"})]})]})}},93765:(e,t,a)=>{a.d(t,{Z:()=>P});var s=a(10326),l=a(55618),i=a(15082),r=a(41107),n=a(5248),o=a(9252);a(11148);var d=a(10123),c=a(96741),m=a(66042),h=a(16027),p=a(87638),u=a(90943),g=a(78077),f=a(9861),v=a(84648),x=a(52321),j=a(57329),b=a(48260),Z=a(5394),y=a(76971),N=a(98139),A=a(63568),w=a(17577),E=a(96672),C=a(5926);function P({initialValues:e,handleSubmit:t,successMsg:a,errorMsg:P,t:M,loading:S,setRegisterSuccess:F,setErrMsg:I}){let _=o.PhoneNumberUtil.getInstance(),L=e=>{try{return _.isValidNumber(_.parseAndKeepRawInput(e))}catch(e){return!1}};d.Z_().test("is-valid-phone",M("validations:phoneFormat"),e=>L(e));let[B,O]=(0,w.useState)(!1),[R,z]=(0,w.useState)(!1),U=()=>{O(!B)},$=()=>{z(!R)},T=e=>d.Ry().shape({firstName:d.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),lastName:d.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:d.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),country:d.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),industry:d.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),password:d.Z_().matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).+$/,e("validations:invalidPassword")).required(e("validations:emptyField")),confirmPassword:d.Z_().oneOf([d.iH("password"),null],e("validations:passwordMatch")).required(e("validations:emptyField")),acceptTerms:d.O7().oneOf([!0],e("validations:emptyField"))});return s.jsx(A.J9,{initialValues:e,onSubmit:t,validationSchema:()=>T(M),children:({values:e,handleChange:t,errors:o,touched:d,setFieldValue:w})=>(0,s.jsxs)(A.l0,{id:"login-form",className:"pentabell-form",children:[(0,s.jsxs)(h.default,{container:!0,rowSpacing:4,columnSpacing:3,className:"form-content",children:[(0,s.jsxs)(h.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(p.Z,{className:"form-group light",children:[(0,s.jsxs)(u.Z,{className:"label-pentabell light",children:[M("register:firstName"),"*"]}),s.jsx(g.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:M("register:firstName"),variant:"standard",type:"text",name:"firstName",value:e.firstName,onChange:e=>{t(e),I(""),F(!1)},error:!!(o.firstName&&d.firstName),disabled:S})]}),s.jsx(A.Bc,{name:"firstName",children:e=>s.jsx(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,s.jsxs)(h.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(p.Z,{className:"form-group light",children:[(0,s.jsxs)(u.Z,{className:"label-pentabell light",children:[M("register:lastName"),"*"]}),s.jsx(g.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:M("register:lastName"),variant:"standard",type:"text",name:"lastName",value:e.lastName,onChange:e=>{t(e),I(""),F(!1)},error:!!(o.lastName&&d.lastName),disabled:S})]}),s.jsx(A.Bc,{name:"lastName",children:e=>s.jsx(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,s.jsxs)(h.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(p.Z,{className:"form-group light",children:[s.jsx(u.Z,{className:"label-pentabell light",children:"Email*"}),s.jsx(g.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:"Email",variant:"standard",name:"email",type:"email",value:e.email,onChange:e=>{t(e),I(""),F(!1)},error:!!(o.email&&d.email),disabled:S})]}),s.jsx(A.Bc,{name:"email",children:e=>s.jsx(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,s.jsxs)(h.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(p.Z,{className:"form-group light",children:[s.jsx(u.Z,{className:"label-pentabell light",children:M("register:phoneNumber")}),s.jsx(E.sb,{defaultCountry:"fr",className:"input-pentabell light",value:e.phone,onChange:e=>{w("phone",e),I(""),F(!1)},flagComponent:e=>s.jsx(C.Z,{...e})})]}),s.jsx(A.Bc,{name:"phone",children:e=>s.jsx(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,s.jsxs)(h.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(p.Z,{className:"form-group light",children:[(0,s.jsxs)(u.Z,{className:"label-pentabell light",children:[M("register:country"),"*"]}),s.jsx(v.Z,{className:"input-pentabell light",id:"tags-standard",options:n.nh,getOptionLabel:e=>e,name:"country",value:e.country,onChange:(e,t)=>{w("country",t),I(""),F(!1)},renderInput:e=>s.jsx(g.Z,{...e,className:"input-pentabell multiple-select  light",variant:"standard",placeholder:M("aiSourcingService:servicePageForm:chooseOne"),error:!!(o.country&&d.country),disabled:S})})]}),s.jsx(A.Bc,{name:"country",children:e=>s.jsx(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,s.jsxs)(h.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(p.Z,{className:"form-group light",children:[(0,s.jsxs)(u.Z,{className:"label-pentabell light",children:[M("register:industry"),"*"]}),s.jsx(v.Z,{className:"input-pentabell light",id:"tags-standard",options:r.RI,getOptionLabel:e=>e,name:"industry",value:e.industry,onChange:(e,t)=>{w("industry",t),I(""),F(!1)},renderInput:e=>s.jsx(g.Z,{...e,className:"input-pentabell multiple-select  light",variant:"standard",placeholder:M("aiSourcingService:servicePageForm:chooseOne"),error:!!(o.industry&&d.industry),disabled:S})})]}),s.jsx(A.Bc,{name:"industry",children:e=>s.jsx(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,s.jsxs)(h.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(p.Z,{className:"form-group light",children:[(0,s.jsxs)(u.Z,{className:"label-pentabell light",children:[M("register:password"),"*"]}),s.jsx(x.Z,{autoComplete:"new-password",className:"input-pentabell light",placeholder:M("register:password"),variant:"standard",type:B?"text":"password",value:e.password,name:"password",onChange:e=>{t(e),I(""),F(!1)},error:!!(o.password&&d.password),disabled:S,endAdornment:s.jsx(j.Z,{position:"end",children:s.jsx(b.Z,{className:`toggle-password fa fa-fw ${B?"fa-eye":"fa-eye-slash"}`,onClick:U,"aria-label":"toggle password visibility",edge:"end",children:B?s.jsx(c.Z,{}):s.jsx(m.Z,{})})})})]}),s.jsx(A.Bc,{name:"password",children:e=>s.jsx(f.Z,{variant:"filled",severity:"error",children:e})})]}),(0,s.jsxs)(h.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(p.Z,{className:"form-group light",children:[(0,s.jsxs)(u.Z,{className:"label-pentabell light",children:[M("register:confirmPassword"),"*"]}),s.jsx(x.Z,{autoComplete:"new-password",className:"input-pentabell light",placeholder:M("register:confirmPassword"),value:e.confirmPassword,type:R?"text":"password",onChange:e=>{t(e),I(""),F(!1)},name:"confirmPassword",disabled:S,error:!!(o.confirmPassword&&d.confirmPassword),endAdornment:s.jsx(j.Z,{position:"end",children:s.jsx(b.Z,{className:`toggle-password fa fa-fw ${R?"fa-eye":"fa-eye-slash"}`,onClick:$,"aria-label":"toggle password visibility",edge:"end",children:R?s.jsx(c.Z,{}):s.jsx(m.Z,{})})})})]}),s.jsx(A.Bc,{name:"confirmPassword",children:e=>s.jsx(f.Z,{variant:"filled",severity:"error",children:e})})]})]}),s.jsx(Z.Z,{className:"checkbox-pentabell light",control:s.jsx(y.Z,{name:"acceptTerms",checked:e.acceptTerms,onChange:e=>{t(e),I(""),F(!1)}}),label:M("register:message")}),s.jsx(A.Bc,{name:"acceptTerms",children:e=>s.jsx(f.Z,{variant:"filled",severity:"error",children:e})}),s.jsx(l.Z,{errMsg:P,success:a}),s.jsx(i.default,{text:S?s.jsx(N.default,{}):M("register:register"),className:"btn btn-filled full-width btn-submit",type:"submit",disabled:S})]})})}},6362:(e,t,a)=>{a.r(t),a.d(t,{default:()=>s});let s={src:"/_next/static/media/bg-auth.1842cff2.png",height:738,width:1440,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAElBMVEU7Tl4WKTkxRFQ/UmIlOEhOYXEF1jp9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIUlEQVR4nBXBgQ0AMAzCsEDo/y9Ps1G962gLUJpsIvmQBwVgAD+bqpS2AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:4}}};