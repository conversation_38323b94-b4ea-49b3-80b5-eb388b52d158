"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-dnd-html5-backend";
exports.ids = ["vendor-chunks/react-dnd-html5-backend"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/BrowserDetector.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/BrowserDetector.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isFirefox: () => (/* binding */ isFirefox),\n/* harmony export */   isSafari: () => (/* binding */ isSafari)\n/* harmony export */ });\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/js_utils.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/utils/js_utils.js\");\n\nconst isFirefox = (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoize)(()=>/firefox/i.test(navigator.userAgent)\n);\nconst isSafari = (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoize)(()=>Boolean(window.safari)\n);\n\n//# sourceMappingURL=BrowserDetector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9Ccm93c2VyRGV0ZWN0b3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBQ3ZDLGtCQUFrQiwyREFBTztBQUNoQztBQUNPLGlCQUFpQiwyREFBTztBQUMvQjs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9yZWFjdC1kbmQtaHRtbDUtYmFja2VuZC9kaXN0L0Jyb3dzZXJEZXRlY3Rvci5qcz9kYmQ5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1lbW9pemUgfSBmcm9tICcuL3V0aWxzL2pzX3V0aWxzLmpzJztcbmV4cG9ydCBjb25zdCBpc0ZpcmVmb3ggPSBtZW1vaXplKCgpPT4vZmlyZWZveC9pLnRlc3QobmF2aWdhdG9yLnVzZXJBZ2VudClcbik7XG5leHBvcnQgY29uc3QgaXNTYWZhcmkgPSBtZW1vaXplKCgpPT5Cb29sZWFuKHdpbmRvdy5zYWZhcmkpXG4pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1Ccm93c2VyRGV0ZWN0b3IuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/BrowserDetector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/EnterLeaveCounter.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/EnterLeaveCounter.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnterLeaveCounter: () => (/* binding */ EnterLeaveCounter)\n/* harmony export */ });\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/js_utils.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/utils/js_utils.js\");\n\nclass EnterLeaveCounter {\n    enter(enteringNode) {\n        const previousLength = this.entered.length;\n        const isNodeEntered = (node)=>this.isNodeInDocument(node) && (!node.contains || node.contains(enteringNode))\n        ;\n        this.entered = (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_0__.union)(this.entered.filter(isNodeEntered), [\n            enteringNode\n        ]);\n        return previousLength === 0 && this.entered.length > 0;\n    }\n    leave(leavingNode) {\n        const previousLength = this.entered.length;\n        this.entered = (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_0__.without)(this.entered.filter(this.isNodeInDocument), leavingNode);\n        return previousLength > 0 && this.entered.length === 0;\n    }\n    reset() {\n        this.entered = [];\n    }\n    constructor(isNodeInDocument){\n        this.entered = [];\n        this.isNodeInDocument = isNodeInDocument;\n    }\n}\n\n//# sourceMappingURL=EnterLeaveCounter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9FbnRlckxlYXZlQ291bnRlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUM5QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHlEQUFLO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QiwyREFBTztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9yZWFjdC1kbmQtaHRtbDUtYmFja2VuZC9kaXN0L0VudGVyTGVhdmVDb3VudGVyLmpzPzFiYzgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdW5pb24sIHdpdGhvdXQgfSBmcm9tICcuL3V0aWxzL2pzX3V0aWxzLmpzJztcbmV4cG9ydCBjbGFzcyBFbnRlckxlYXZlQ291bnRlciB7XG4gICAgZW50ZXIoZW50ZXJpbmdOb2RlKSB7XG4gICAgICAgIGNvbnN0IHByZXZpb3VzTGVuZ3RoID0gdGhpcy5lbnRlcmVkLmxlbmd0aDtcbiAgICAgICAgY29uc3QgaXNOb2RlRW50ZXJlZCA9IChub2RlKT0+dGhpcy5pc05vZGVJbkRvY3VtZW50KG5vZGUpICYmICghbm9kZS5jb250YWlucyB8fCBub2RlLmNvbnRhaW5zKGVudGVyaW5nTm9kZSkpXG4gICAgICAgIDtcbiAgICAgICAgdGhpcy5lbnRlcmVkID0gdW5pb24odGhpcy5lbnRlcmVkLmZpbHRlcihpc05vZGVFbnRlcmVkKSwgW1xuICAgICAgICAgICAgZW50ZXJpbmdOb2RlXG4gICAgICAgIF0pO1xuICAgICAgICByZXR1cm4gcHJldmlvdXNMZW5ndGggPT09IDAgJiYgdGhpcy5lbnRlcmVkLmxlbmd0aCA+IDA7XG4gICAgfVxuICAgIGxlYXZlKGxlYXZpbmdOb2RlKSB7XG4gICAgICAgIGNvbnN0IHByZXZpb3VzTGVuZ3RoID0gdGhpcy5lbnRlcmVkLmxlbmd0aDtcbiAgICAgICAgdGhpcy5lbnRlcmVkID0gd2l0aG91dCh0aGlzLmVudGVyZWQuZmlsdGVyKHRoaXMuaXNOb2RlSW5Eb2N1bWVudCksIGxlYXZpbmdOb2RlKTtcbiAgICAgICAgcmV0dXJuIHByZXZpb3VzTGVuZ3RoID4gMCAmJiB0aGlzLmVudGVyZWQubGVuZ3RoID09PSAwO1xuICAgIH1cbiAgICByZXNldCgpIHtcbiAgICAgICAgdGhpcy5lbnRlcmVkID0gW107XG4gICAgfVxuICAgIGNvbnN0cnVjdG9yKGlzTm9kZUluRG9jdW1lbnQpe1xuICAgICAgICB0aGlzLmVudGVyZWQgPSBbXTtcbiAgICAgICAgdGhpcy5pc05vZGVJbkRvY3VtZW50ID0gaXNOb2RlSW5Eb2N1bWVudDtcbiAgICB9XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUVudGVyTGVhdmVDb3VudGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/EnterLeaveCounter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/HTML5BackendImpl.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/HTML5BackendImpl.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTML5BackendImpl: () => (/* binding */ HTML5BackendImpl)\n/* harmony export */ });\n/* harmony import */ var _EnterLeaveCounter_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnterLeaveCounter.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/EnterLeaveCounter.js\");\n/* harmony import */ var _NativeDragSources_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NativeDragSources/index.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/index.js\");\n/* harmony import */ var _NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NativeTypes.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeTypes.js\");\n/* harmony import */ var _OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OffsetUtils.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/OffsetUtils.js\");\n/* harmony import */ var _OptionsReader_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OptionsReader.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/OptionsReader.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\n\n\nclass HTML5BackendImpl {\n    /**\n\t * Generate profiling statistics for the HTML5Backend.\n\t */ profile() {\n        var ref, ref1;\n        return {\n            sourcePreviewNodes: this.sourcePreviewNodes.size,\n            sourcePreviewNodeOptions: this.sourcePreviewNodeOptions.size,\n            sourceNodeOptions: this.sourceNodeOptions.size,\n            sourceNodes: this.sourceNodes.size,\n            dragStartSourceIds: ((ref = this.dragStartSourceIds) === null || ref === void 0 ? void 0 : ref.length) || 0,\n            dropTargetIds: this.dropTargetIds.length,\n            dragEnterTargetIds: this.dragEnterTargetIds.length,\n            dragOverTargetIds: ((ref1 = this.dragOverTargetIds) === null || ref1 === void 0 ? void 0 : ref1.length) || 0\n        };\n    }\n    // public for test\n    get window() {\n        return this.options.window;\n    }\n    get document() {\n        return this.options.document;\n    }\n    /**\n\t * Get the root element to use for event subscriptions\n\t */ get rootElement() {\n        return this.options.rootElement;\n    }\n    setup() {\n        const root = this.rootElement;\n        if (root === undefined) {\n            return;\n        }\n        if (root.__isReactDndBackendSetUp) {\n            throw new Error('Cannot have two HTML5 backends at the same time.');\n        }\n        root.__isReactDndBackendSetUp = true;\n        this.addEventListeners(root);\n    }\n    teardown() {\n        const root = this.rootElement;\n        if (root === undefined) {\n            return;\n        }\n        root.__isReactDndBackendSetUp = false;\n        this.removeEventListeners(this.rootElement);\n        this.clearCurrentDragSourceNode();\n        if (this.asyncEndDragFrameId) {\n            var ref;\n            (ref = this.window) === null || ref === void 0 ? void 0 : ref.cancelAnimationFrame(this.asyncEndDragFrameId);\n        }\n    }\n    connectDragPreview(sourceId, node, options) {\n        this.sourcePreviewNodeOptions.set(sourceId, options);\n        this.sourcePreviewNodes.set(sourceId, node);\n        return ()=>{\n            this.sourcePreviewNodes.delete(sourceId);\n            this.sourcePreviewNodeOptions.delete(sourceId);\n        };\n    }\n    connectDragSource(sourceId, node, options) {\n        this.sourceNodes.set(sourceId, node);\n        this.sourceNodeOptions.set(sourceId, options);\n        const handleDragStart = (e)=>this.handleDragStart(e, sourceId)\n        ;\n        const handleSelectStart = (e)=>this.handleSelectStart(e)\n        ;\n        node.setAttribute('draggable', 'true');\n        node.addEventListener('dragstart', handleDragStart);\n        node.addEventListener('selectstart', handleSelectStart);\n        return ()=>{\n            this.sourceNodes.delete(sourceId);\n            this.sourceNodeOptions.delete(sourceId);\n            node.removeEventListener('dragstart', handleDragStart);\n            node.removeEventListener('selectstart', handleSelectStart);\n            node.setAttribute('draggable', 'false');\n        };\n    }\n    connectDropTarget(targetId, node) {\n        const handleDragEnter = (e)=>this.handleDragEnter(e, targetId)\n        ;\n        const handleDragOver = (e)=>this.handleDragOver(e, targetId)\n        ;\n        const handleDrop = (e)=>this.handleDrop(e, targetId)\n        ;\n        node.addEventListener('dragenter', handleDragEnter);\n        node.addEventListener('dragover', handleDragOver);\n        node.addEventListener('drop', handleDrop);\n        return ()=>{\n            node.removeEventListener('dragenter', handleDragEnter);\n            node.removeEventListener('dragover', handleDragOver);\n            node.removeEventListener('drop', handleDrop);\n        };\n    }\n    addEventListeners(target) {\n        // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n        if (!target.addEventListener) {\n            return;\n        }\n        target.addEventListener('dragstart', this.handleTopDragStart);\n        target.addEventListener('dragstart', this.handleTopDragStartCapture, true);\n        target.addEventListener('dragend', this.handleTopDragEndCapture, true);\n        target.addEventListener('dragenter', this.handleTopDragEnter);\n        target.addEventListener('dragenter', this.handleTopDragEnterCapture, true);\n        target.addEventListener('dragleave', this.handleTopDragLeaveCapture, true);\n        target.addEventListener('dragover', this.handleTopDragOver);\n        target.addEventListener('dragover', this.handleTopDragOverCapture, true);\n        target.addEventListener('drop', this.handleTopDrop);\n        target.addEventListener('drop', this.handleTopDropCapture, true);\n    }\n    removeEventListeners(target) {\n        // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n        if (!target.removeEventListener) {\n            return;\n        }\n        target.removeEventListener('dragstart', this.handleTopDragStart);\n        target.removeEventListener('dragstart', this.handleTopDragStartCapture, true);\n        target.removeEventListener('dragend', this.handleTopDragEndCapture, true);\n        target.removeEventListener('dragenter', this.handleTopDragEnter);\n        target.removeEventListener('dragenter', this.handleTopDragEnterCapture, true);\n        target.removeEventListener('dragleave', this.handleTopDragLeaveCapture, true);\n        target.removeEventListener('dragover', this.handleTopDragOver);\n        target.removeEventListener('dragover', this.handleTopDragOverCapture, true);\n        target.removeEventListener('drop', this.handleTopDrop);\n        target.removeEventListener('drop', this.handleTopDropCapture, true);\n    }\n    getCurrentSourceNodeOptions() {\n        const sourceId = this.monitor.getSourceId();\n        const sourceNodeOptions = this.sourceNodeOptions.get(sourceId);\n        return _objectSpread({\n            dropEffect: this.altKeyPressed ? 'copy' : 'move'\n        }, sourceNodeOptions || {});\n    }\n    getCurrentDropEffect() {\n        if (this.isDraggingNativeItem()) {\n            // It makes more sense to default to 'copy' for native resources\n            return 'copy';\n        }\n        return this.getCurrentSourceNodeOptions().dropEffect;\n    }\n    getCurrentSourcePreviewNodeOptions() {\n        const sourceId = this.monitor.getSourceId();\n        const sourcePreviewNodeOptions = this.sourcePreviewNodeOptions.get(sourceId);\n        return _objectSpread({\n            anchorX: 0.5,\n            anchorY: 0.5,\n            captureDraggingState: false\n        }, sourcePreviewNodeOptions || {});\n    }\n    isDraggingNativeItem() {\n        const itemType = this.monitor.getItemType();\n        return Object.keys(_NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__).some((key)=>_NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__[key] === itemType\n        );\n    }\n    beginDragNativeItem(type, dataTransfer) {\n        this.clearCurrentDragSourceNode();\n        this.currentNativeSource = (0,_NativeDragSources_index_js__WEBPACK_IMPORTED_MODULE_1__.createNativeDragSource)(type, dataTransfer);\n        this.currentNativeHandle = this.registry.addSource(type, this.currentNativeSource);\n        this.actions.beginDrag([\n            this.currentNativeHandle\n        ]);\n    }\n    setCurrentDragSourceNode(node) {\n        this.clearCurrentDragSourceNode();\n        this.currentDragSourceNode = node;\n        // A timeout of > 0 is necessary to resolve Firefox issue referenced\n        // See:\n        //   * https://github.com/react-dnd/react-dnd/pull/928\n        //   * https://github.com/react-dnd/react-dnd/issues/869\n        const MOUSE_MOVE_TIMEOUT = 1000;\n        // Receiving a mouse event in the middle of a dragging operation\n        // means it has ended and the drag source node disappeared from DOM,\n        // so the browser didn't dispatch the dragend event.\n        //\n        // We need to wait before we start listening for mousemove events.\n        // This is needed because the drag preview needs to be drawn or else it fires an 'mousemove' event\n        // immediately in some browsers.\n        //\n        // See:\n        //   * https://github.com/react-dnd/react-dnd/pull/928\n        //   * https://github.com/react-dnd/react-dnd/issues/869\n        //\n        this.mouseMoveTimeoutTimer = setTimeout(()=>{\n            var ref;\n            return (ref = this.rootElement) === null || ref === void 0 ? void 0 : ref.addEventListener('mousemove', this.endDragIfSourceWasRemovedFromDOM, true);\n        }, MOUSE_MOVE_TIMEOUT);\n    }\n    clearCurrentDragSourceNode() {\n        if (this.currentDragSourceNode) {\n            this.currentDragSourceNode = null;\n            if (this.rootElement) {\n                var ref;\n                (ref = this.window) === null || ref === void 0 ? void 0 : ref.clearTimeout(this.mouseMoveTimeoutTimer || undefined);\n                this.rootElement.removeEventListener('mousemove', this.endDragIfSourceWasRemovedFromDOM, true);\n            }\n            this.mouseMoveTimeoutTimer = null;\n            return true;\n        }\n        return false;\n    }\n    handleDragStart(e, sourceId) {\n        if (e.defaultPrevented) {\n            return;\n        }\n        if (!this.dragStartSourceIds) {\n            this.dragStartSourceIds = [];\n        }\n        this.dragStartSourceIds.unshift(sourceId);\n    }\n    handleDragEnter(_e, targetId) {\n        this.dragEnterTargetIds.unshift(targetId);\n    }\n    handleDragOver(_e, targetId) {\n        if (this.dragOverTargetIds === null) {\n            this.dragOverTargetIds = [];\n        }\n        this.dragOverTargetIds.unshift(targetId);\n    }\n    handleDrop(_e, targetId) {\n        this.dropTargetIds.unshift(targetId);\n    }\n    constructor(manager, globalContext, options){\n        this.sourcePreviewNodes = new Map();\n        this.sourcePreviewNodeOptions = new Map();\n        this.sourceNodes = new Map();\n        this.sourceNodeOptions = new Map();\n        this.dragStartSourceIds = null;\n        this.dropTargetIds = [];\n        this.dragEnterTargetIds = [];\n        this.currentNativeSource = null;\n        this.currentNativeHandle = null;\n        this.currentDragSourceNode = null;\n        this.altKeyPressed = false;\n        this.mouseMoveTimeoutTimer = null;\n        this.asyncEndDragFrameId = null;\n        this.dragOverTargetIds = null;\n        this.lastClientOffset = null;\n        this.hoverRafId = null;\n        this.getSourceClientOffset = (sourceId)=>{\n            const source = this.sourceNodes.get(sourceId);\n            return source && (0,_OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__.getNodeClientOffset)(source) || null;\n        };\n        this.endDragNativeItem = ()=>{\n            if (!this.isDraggingNativeItem()) {\n                return;\n            }\n            this.actions.endDrag();\n            if (this.currentNativeHandle) {\n                this.registry.removeSource(this.currentNativeHandle);\n            }\n            this.currentNativeHandle = null;\n            this.currentNativeSource = null;\n        };\n        this.isNodeInDocument = (node)=>{\n            // Check the node either in the main document or in the current context\n            return Boolean(node && this.document && this.document.body && this.document.body.contains(node));\n        };\n        this.endDragIfSourceWasRemovedFromDOM = ()=>{\n            const node = this.currentDragSourceNode;\n            if (node == null || this.isNodeInDocument(node)) {\n                return;\n            }\n            if (this.clearCurrentDragSourceNode() && this.monitor.isDragging()) {\n                this.actions.endDrag();\n            }\n            this.cancelHover();\n        };\n        this.scheduleHover = (dragOverTargetIds)=>{\n            if (this.hoverRafId === null && typeof requestAnimationFrame !== 'undefined') {\n                this.hoverRafId = requestAnimationFrame(()=>{\n                    if (this.monitor.isDragging()) {\n                        this.actions.hover(dragOverTargetIds || [], {\n                            clientOffset: this.lastClientOffset\n                        });\n                    }\n                    this.hoverRafId = null;\n                });\n            }\n        };\n        this.cancelHover = ()=>{\n            if (this.hoverRafId !== null && typeof cancelAnimationFrame !== 'undefined') {\n                cancelAnimationFrame(this.hoverRafId);\n                this.hoverRafId = null;\n            }\n        };\n        this.handleTopDragStartCapture = ()=>{\n            this.clearCurrentDragSourceNode();\n            this.dragStartSourceIds = [];\n        };\n        this.handleTopDragStart = (e)=>{\n            if (e.defaultPrevented) {\n                return;\n            }\n            const { dragStartSourceIds  } = this;\n            this.dragStartSourceIds = null;\n            const clientOffset = (0,_OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__.getEventClientOffset)(e);\n            // Avoid crashing if we missed a drop event or our previous drag died\n            if (this.monitor.isDragging()) {\n                this.actions.endDrag();\n                this.cancelHover();\n            }\n            // Don't publish the source just yet (see why below)\n            this.actions.beginDrag(dragStartSourceIds || [], {\n                publishSource: false,\n                getSourceClientOffset: this.getSourceClientOffset,\n                clientOffset\n            });\n            const { dataTransfer  } = e;\n            const nativeType = (0,_NativeDragSources_index_js__WEBPACK_IMPORTED_MODULE_1__.matchNativeItemType)(dataTransfer);\n            if (this.monitor.isDragging()) {\n                if (dataTransfer && typeof dataTransfer.setDragImage === 'function') {\n                    // Use custom drag image if user specifies it.\n                    // If child drag source refuses drag but parent agrees,\n                    // use parent's node as drag image. Neither works in IE though.\n                    const sourceId = this.monitor.getSourceId();\n                    const sourceNode = this.sourceNodes.get(sourceId);\n                    const dragPreview = this.sourcePreviewNodes.get(sourceId) || sourceNode;\n                    if (dragPreview) {\n                        const { anchorX , anchorY , offsetX , offsetY  } = this.getCurrentSourcePreviewNodeOptions();\n                        const anchorPoint = {\n                            anchorX,\n                            anchorY\n                        };\n                        const offsetPoint = {\n                            offsetX,\n                            offsetY\n                        };\n                        const dragPreviewOffset = (0,_OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__.getDragPreviewOffset)(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint);\n                        dataTransfer.setDragImage(dragPreview, dragPreviewOffset.x, dragPreviewOffset.y);\n                    }\n                }\n                try {\n                    // Firefox won't drag without setting data\n                    dataTransfer === null || dataTransfer === void 0 ? void 0 : dataTransfer.setData('application/json', {});\n                } catch (err) {\n                // IE doesn't support MIME types in setData\n                }\n                // Store drag source node so we can check whether\n                // it is removed from DOM and trigger endDrag manually.\n                this.setCurrentDragSourceNode(e.target);\n                // Now we are ready to publish the drag source.. or are we not?\n                const { captureDraggingState  } = this.getCurrentSourcePreviewNodeOptions();\n                if (!captureDraggingState) {\n                    // Usually we want to publish it in the next tick so that browser\n                    // is able to screenshot the current (not yet dragging) state.\n                    //\n                    // It also neatly avoids a situation where render() returns null\n                    // in the same tick for the source element, and browser freaks out.\n                    setTimeout(()=>this.actions.publishDragSource()\n                    , 0);\n                } else {\n                    // In some cases the user may want to override this behavior, e.g.\n                    // to work around IE not supporting custom drag previews.\n                    //\n                    // When using a custom drag layer, the only way to prevent\n                    // the default drag preview from drawing in IE is to screenshot\n                    // the dragging state in which the node itself has zero opacity\n                    // and height. In this case, though, returning null from render()\n                    // will abruptly end the dragging, which is not obvious.\n                    //\n                    // This is the reason such behavior is strictly opt-in.\n                    this.actions.publishDragSource();\n                }\n            } else if (nativeType) {\n                // A native item (such as URL) dragged from inside the document\n                this.beginDragNativeItem(nativeType);\n            } else if (dataTransfer && !dataTransfer.types && (e.target && !e.target.hasAttribute || !e.target.hasAttribute('draggable'))) {\n                // Looks like a Safari bug: dataTransfer.types is null, but there was no draggable.\n                // Just let it drag. It's a native type (URL or text) and will be picked up in\n                // dragenter handler.\n                return;\n            } else {\n                // If by this time no drag source reacted, tell browser not to drag.\n                e.preventDefault();\n            }\n        };\n        this.handleTopDragEndCapture = ()=>{\n            if (this.clearCurrentDragSourceNode() && this.monitor.isDragging()) {\n                // Firefox can dispatch this event in an infinite loop\n                // if dragend handler does something like showing an alert.\n                // Only proceed if we have not handled it already.\n                this.actions.endDrag();\n            }\n            this.cancelHover();\n        };\n        this.handleTopDragEnterCapture = (e)=>{\n            this.dragEnterTargetIds = [];\n            if (this.isDraggingNativeItem()) {\n                var ref;\n                (ref = this.currentNativeSource) === null || ref === void 0 ? void 0 : ref.loadDataTransfer(e.dataTransfer);\n            }\n            const isFirstEnter = this.enterLeaveCounter.enter(e.target);\n            if (!isFirstEnter || this.monitor.isDragging()) {\n                return;\n            }\n            const { dataTransfer  } = e;\n            const nativeType = (0,_NativeDragSources_index_js__WEBPACK_IMPORTED_MODULE_1__.matchNativeItemType)(dataTransfer);\n            if (nativeType) {\n                // A native item (such as file or URL) dragged from outside the document\n                this.beginDragNativeItem(nativeType, dataTransfer);\n            }\n        };\n        this.handleTopDragEnter = (e)=>{\n            const { dragEnterTargetIds  } = this;\n            this.dragEnterTargetIds = [];\n            if (!this.monitor.isDragging()) {\n                // This is probably a native item type we don't understand.\n                return;\n            }\n            this.altKeyPressed = e.altKey;\n            // If the target changes position as the result of `dragenter`, `dragover` might still\n            // get dispatched despite target being no longer there. The easy solution is to check\n            // whether there actually is a target before firing `hover`.\n            if (dragEnterTargetIds.length > 0) {\n                this.actions.hover(dragEnterTargetIds, {\n                    clientOffset: (0,_OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__.getEventClientOffset)(e)\n                });\n            }\n            const canDrop = dragEnterTargetIds.some((targetId)=>this.monitor.canDropOnTarget(targetId)\n            );\n            if (canDrop) {\n                // IE requires this to fire dragover events\n                e.preventDefault();\n                if (e.dataTransfer) {\n                    e.dataTransfer.dropEffect = this.getCurrentDropEffect();\n                }\n            }\n        };\n        this.handleTopDragOverCapture = (e)=>{\n            this.dragOverTargetIds = [];\n            if (this.isDraggingNativeItem()) {\n                var ref;\n                (ref = this.currentNativeSource) === null || ref === void 0 ? void 0 : ref.loadDataTransfer(e.dataTransfer);\n            }\n        };\n        this.handleTopDragOver = (e)=>{\n            const { dragOverTargetIds  } = this;\n            this.dragOverTargetIds = [];\n            if (!this.monitor.isDragging()) {\n                // This is probably a native item type we don't understand.\n                // Prevent default \"drop and blow away the whole document\" action.\n                e.preventDefault();\n                if (e.dataTransfer) {\n                    e.dataTransfer.dropEffect = 'none';\n                }\n                return;\n            }\n            this.altKeyPressed = e.altKey;\n            this.lastClientOffset = (0,_OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__.getEventClientOffset)(e);\n            this.scheduleHover(dragOverTargetIds);\n            const canDrop = (dragOverTargetIds || []).some((targetId)=>this.monitor.canDropOnTarget(targetId)\n            );\n            if (canDrop) {\n                // Show user-specified drop effect.\n                e.preventDefault();\n                if (e.dataTransfer) {\n                    e.dataTransfer.dropEffect = this.getCurrentDropEffect();\n                }\n            } else if (this.isDraggingNativeItem()) {\n                // Don't show a nice cursor but still prevent default\n                // \"drop and blow away the whole document\" action.\n                e.preventDefault();\n            } else {\n                e.preventDefault();\n                if (e.dataTransfer) {\n                    e.dataTransfer.dropEffect = 'none';\n                }\n            }\n        };\n        this.handleTopDragLeaveCapture = (e)=>{\n            if (this.isDraggingNativeItem()) {\n                e.preventDefault();\n            }\n            const isLastLeave = this.enterLeaveCounter.leave(e.target);\n            if (!isLastLeave) {\n                return;\n            }\n            if (this.isDraggingNativeItem()) {\n                setTimeout(()=>this.endDragNativeItem()\n                , 0);\n            }\n            this.cancelHover();\n        };\n        this.handleTopDropCapture = (e)=>{\n            this.dropTargetIds = [];\n            if (this.isDraggingNativeItem()) {\n                var ref;\n                e.preventDefault();\n                (ref = this.currentNativeSource) === null || ref === void 0 ? void 0 : ref.loadDataTransfer(e.dataTransfer);\n            } else if ((0,_NativeDragSources_index_js__WEBPACK_IMPORTED_MODULE_1__.matchNativeItemType)(e.dataTransfer)) {\n                // Dragging some elements, like <a> and <img> may still behave like a native drag event,\n                // even if the current drag event matches a user-defined type.\n                // Stop the default behavior when we're not expecting a native item to be dropped.\n                e.preventDefault();\n            }\n            this.enterLeaveCounter.reset();\n        };\n        this.handleTopDrop = (e)=>{\n            const { dropTargetIds  } = this;\n            this.dropTargetIds = [];\n            this.actions.hover(dropTargetIds, {\n                clientOffset: (0,_OffsetUtils_js__WEBPACK_IMPORTED_MODULE_2__.getEventClientOffset)(e)\n            });\n            this.actions.drop({\n                dropEffect: this.getCurrentDropEffect()\n            });\n            if (this.isDraggingNativeItem()) {\n                this.endDragNativeItem();\n            } else if (this.monitor.isDragging()) {\n                this.actions.endDrag();\n            }\n            this.cancelHover();\n        };\n        this.handleSelectStart = (e)=>{\n            const target = e.target;\n            // Only IE requires us to explicitly say\n            // we want drag drop operation to start\n            if (typeof target.dragDrop !== 'function') {\n                return;\n            }\n            // Inputs and textareas should be selectable\n            if (target.tagName === 'INPUT' || target.tagName === 'SELECT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {\n                return;\n            }\n            // For other targets, ask IE\n            // to enable drag and drop\n            e.preventDefault();\n            target.dragDrop();\n        };\n        this.options = new _OptionsReader_js__WEBPACK_IMPORTED_MODULE_3__.OptionsReader(globalContext, options);\n        this.actions = manager.getActions();\n        this.monitor = manager.getMonitor();\n        this.registry = manager.getRegistry();\n        this.enterLeaveCounter = new _EnterLeaveCounter_js__WEBPACK_IMPORTED_MODULE_4__.EnterLeaveCounter(this.isNodeInDocument);\n    }\n}\n\n//# sourceMappingURL=HTML5BackendImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9IVE1MNUJhY2tlbmRJbXBsLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHNCQUFzQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDMkQ7QUFDZ0M7QUFDM0M7QUFDbUQ7QUFDaEQ7QUFDNUM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMseUJBQXlCO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsZ0NBQWdDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQiw0Q0FBVyxjQUFjLDRDQUFXO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLG1GQUFzQjtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsb0VBQW1CO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixzQkFBc0I7QUFDMUM7QUFDQSxpQ0FBaUMscUVBQW9CO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLG9CQUFvQixnQkFBZ0I7QUFDcEMsK0JBQStCLGdGQUFtQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MseUNBQXlDO0FBQ3pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrREFBa0QscUVBQW9CO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwySEFBMkg7QUFDM0gsa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix3QkFBd0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGdCQUFnQjtBQUNwQywrQkFBK0IsZ0ZBQW1CO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixzQkFBc0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxxRUFBb0I7QUFDdEQsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHFCQUFxQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLHFFQUFvQjtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLFNBQVMsZ0ZBQW1CO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsaUJBQWlCO0FBQ3JDO0FBQ0E7QUFDQSw4QkFBOEIscUVBQW9CO0FBQ2xELGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQiw0REFBYTtBQUN4QztBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsb0VBQWlCO0FBQ3REO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9IVE1MNUJhY2tlbmRJbXBsLmpzPzQyYTgiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gX2RlZmluZVByb3BlcnR5KG9iaiwga2V5LCB2YWx1ZSkge1xuICAgIGlmIChrZXkgaW4gb2JqKSB7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvYmosIGtleSwge1xuICAgICAgICAgICAgdmFsdWU6IHZhbHVlLFxuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlXG4gICAgICAgIH0pO1xuICAgIH0gZWxzZSB7XG4gICAgICAgIG9ialtrZXldID0gdmFsdWU7XG4gICAgfVxuICAgIHJldHVybiBvYmo7XG59XG5mdW5jdGlvbiBfb2JqZWN0U3ByZWFkKHRhcmdldCkge1xuICAgIGZvcih2YXIgaSA9IDE7IGkgPCBhcmd1bWVudHMubGVuZ3RoOyBpKyspe1xuICAgICAgICB2YXIgc291cmNlID0gYXJndW1lbnRzW2ldICE9IG51bGwgPyBhcmd1bWVudHNbaV0gOiB7fTtcbiAgICAgICAgdmFyIG93bktleXMgPSBPYmplY3Qua2V5cyhzb3VyY2UpO1xuICAgICAgICBpZiAodHlwZW9mIE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgIG93bktleXMgPSBvd25LZXlzLmNvbmNhdChPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKHNvdXJjZSkuZmlsdGVyKGZ1bmN0aW9uKHN5bSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKHNvdXJjZSwgc3ltKS5lbnVtZXJhYmxlO1xuICAgICAgICAgICAgfSkpO1xuICAgICAgICB9XG4gICAgICAgIG93bktleXMuZm9yRWFjaChmdW5jdGlvbihrZXkpIHtcbiAgICAgICAgICAgIF9kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIGtleSwgc291cmNlW2tleV0pO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgcmV0dXJuIHRhcmdldDtcbn1cbmltcG9ydCB7IEVudGVyTGVhdmVDb3VudGVyIH0gZnJvbSAnLi9FbnRlckxlYXZlQ291bnRlci5qcyc7XG5pbXBvcnQgeyBjcmVhdGVOYXRpdmVEcmFnU291cmNlLCBtYXRjaE5hdGl2ZUl0ZW1UeXBlIH0gZnJvbSAnLi9OYXRpdmVEcmFnU291cmNlcy9pbmRleC5qcyc7XG5pbXBvcnQgKiBhcyBOYXRpdmVUeXBlcyBmcm9tICcuL05hdGl2ZVR5cGVzLmpzJztcbmltcG9ydCB7IGdldERyYWdQcmV2aWV3T2Zmc2V0LCBnZXRFdmVudENsaWVudE9mZnNldCwgZ2V0Tm9kZUNsaWVudE9mZnNldCB9IGZyb20gJy4vT2Zmc2V0VXRpbHMuanMnO1xuaW1wb3J0IHsgT3B0aW9uc1JlYWRlciB9IGZyb20gJy4vT3B0aW9uc1JlYWRlci5qcyc7XG5leHBvcnQgY2xhc3MgSFRNTDVCYWNrZW5kSW1wbCB7XG4gICAgLyoqXG5cdCAqIEdlbmVyYXRlIHByb2ZpbGluZyBzdGF0aXN0aWNzIGZvciB0aGUgSFRNTDVCYWNrZW5kLlxuXHQgKi8gcHJvZmlsZSgpIHtcbiAgICAgICAgdmFyIHJlZiwgcmVmMTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHNvdXJjZVByZXZpZXdOb2RlczogdGhpcy5zb3VyY2VQcmV2aWV3Tm9kZXMuc2l6ZSxcbiAgICAgICAgICAgIHNvdXJjZVByZXZpZXdOb2RlT3B0aW9uczogdGhpcy5zb3VyY2VQcmV2aWV3Tm9kZU9wdGlvbnMuc2l6ZSxcbiAgICAgICAgICAgIHNvdXJjZU5vZGVPcHRpb25zOiB0aGlzLnNvdXJjZU5vZGVPcHRpb25zLnNpemUsXG4gICAgICAgICAgICBzb3VyY2VOb2RlczogdGhpcy5zb3VyY2VOb2Rlcy5zaXplLFxuICAgICAgICAgICAgZHJhZ1N0YXJ0U291cmNlSWRzOiAoKHJlZiA9IHRoaXMuZHJhZ1N0YXJ0U291cmNlSWRzKSA9PT0gbnVsbCB8fCByZWYgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHJlZi5sZW5ndGgpIHx8IDAsXG4gICAgICAgICAgICBkcm9wVGFyZ2V0SWRzOiB0aGlzLmRyb3BUYXJnZXRJZHMubGVuZ3RoLFxuICAgICAgICAgICAgZHJhZ0VudGVyVGFyZ2V0SWRzOiB0aGlzLmRyYWdFbnRlclRhcmdldElkcy5sZW5ndGgsXG4gICAgICAgICAgICBkcmFnT3ZlclRhcmdldElkczogKChyZWYxID0gdGhpcy5kcmFnT3ZlclRhcmdldElkcykgPT09IG51bGwgfHwgcmVmMSA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVmMS5sZW5ndGgpIHx8IDBcbiAgICAgICAgfTtcbiAgICB9XG4gICAgLy8gcHVibGljIGZvciB0ZXN0XG4gICAgZ2V0IHdpbmRvdygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMub3B0aW9ucy53aW5kb3c7XG4gICAgfVxuICAgIGdldCBkb2N1bWVudCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMub3B0aW9ucy5kb2N1bWVudDtcbiAgICB9XG4gICAgLyoqXG5cdCAqIEdldCB0aGUgcm9vdCBlbGVtZW50IHRvIHVzZSBmb3IgZXZlbnQgc3Vic2NyaXB0aW9uc1xuXHQgKi8gZ2V0IHJvb3RFbGVtZW50KCkge1xuICAgICAgICByZXR1cm4gdGhpcy5vcHRpb25zLnJvb3RFbGVtZW50O1xuICAgIH1cbiAgICBzZXR1cCgpIHtcbiAgICAgICAgY29uc3Qgcm9vdCA9IHRoaXMucm9vdEVsZW1lbnQ7XG4gICAgICAgIGlmIChyb290ID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAocm9vdC5fX2lzUmVhY3REbmRCYWNrZW5kU2V0VXApIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignQ2Fubm90IGhhdmUgdHdvIEhUTUw1IGJhY2tlbmRzIGF0IHRoZSBzYW1lIHRpbWUuJyk7XG4gICAgICAgIH1cbiAgICAgICAgcm9vdC5fX2lzUmVhY3REbmRCYWNrZW5kU2V0VXAgPSB0cnVlO1xuICAgICAgICB0aGlzLmFkZEV2ZW50TGlzdGVuZXJzKHJvb3QpO1xuICAgIH1cbiAgICB0ZWFyZG93bigpIHtcbiAgICAgICAgY29uc3Qgcm9vdCA9IHRoaXMucm9vdEVsZW1lbnQ7XG4gICAgICAgIGlmIChyb290ID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICByb290Ll9faXNSZWFjdERuZEJhY2tlbmRTZXRVcCA9IGZhbHNlO1xuICAgICAgICB0aGlzLnJlbW92ZUV2ZW50TGlzdGVuZXJzKHRoaXMucm9vdEVsZW1lbnQpO1xuICAgICAgICB0aGlzLmNsZWFyQ3VycmVudERyYWdTb3VyY2VOb2RlKCk7XG4gICAgICAgIGlmICh0aGlzLmFzeW5jRW5kRHJhZ0ZyYW1lSWQpIHtcbiAgICAgICAgICAgIHZhciByZWY7XG4gICAgICAgICAgICAocmVmID0gdGhpcy53aW5kb3cpID09PSBudWxsIHx8IHJlZiA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVmLmNhbmNlbEFuaW1hdGlvbkZyYW1lKHRoaXMuYXN5bmNFbmREcmFnRnJhbWVJZCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY29ubmVjdERyYWdQcmV2aWV3KHNvdXJjZUlkLCBub2RlLCBvcHRpb25zKSB7XG4gICAgICAgIHRoaXMuc291cmNlUHJldmlld05vZGVPcHRpb25zLnNldChzb3VyY2VJZCwgb3B0aW9ucyk7XG4gICAgICAgIHRoaXMuc291cmNlUHJldmlld05vZGVzLnNldChzb3VyY2VJZCwgbm9kZSk7XG4gICAgICAgIHJldHVybiAoKT0+e1xuICAgICAgICAgICAgdGhpcy5zb3VyY2VQcmV2aWV3Tm9kZXMuZGVsZXRlKHNvdXJjZUlkKTtcbiAgICAgICAgICAgIHRoaXMuc291cmNlUHJldmlld05vZGVPcHRpb25zLmRlbGV0ZShzb3VyY2VJZCk7XG4gICAgICAgIH07XG4gICAgfVxuICAgIGNvbm5lY3REcmFnU291cmNlKHNvdXJjZUlkLCBub2RlLCBvcHRpb25zKSB7XG4gICAgICAgIHRoaXMuc291cmNlTm9kZXMuc2V0KHNvdXJjZUlkLCBub2RlKTtcbiAgICAgICAgdGhpcy5zb3VyY2VOb2RlT3B0aW9ucy5zZXQoc291cmNlSWQsIG9wdGlvbnMpO1xuICAgICAgICBjb25zdCBoYW5kbGVEcmFnU3RhcnQgPSAoZSk9PnRoaXMuaGFuZGxlRHJhZ1N0YXJ0KGUsIHNvdXJjZUlkKVxuICAgICAgICA7XG4gICAgICAgIGNvbnN0IGhhbmRsZVNlbGVjdFN0YXJ0ID0gKGUpPT50aGlzLmhhbmRsZVNlbGVjdFN0YXJ0KGUpXG4gICAgICAgIDtcbiAgICAgICAgbm9kZS5zZXRBdHRyaWJ1dGUoJ2RyYWdnYWJsZScsICd0cnVlJyk7XG4gICAgICAgIG5vZGUuYWRkRXZlbnRMaXN0ZW5lcignZHJhZ3N0YXJ0JywgaGFuZGxlRHJhZ1N0YXJ0KTtcbiAgICAgICAgbm9kZS5hZGRFdmVudExpc3RlbmVyKCdzZWxlY3RzdGFydCcsIGhhbmRsZVNlbGVjdFN0YXJ0KTtcbiAgICAgICAgcmV0dXJuICgpPT57XG4gICAgICAgICAgICB0aGlzLnNvdXJjZU5vZGVzLmRlbGV0ZShzb3VyY2VJZCk7XG4gICAgICAgICAgICB0aGlzLnNvdXJjZU5vZGVPcHRpb25zLmRlbGV0ZShzb3VyY2VJZCk7XG4gICAgICAgICAgICBub2RlLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2RyYWdzdGFydCcsIGhhbmRsZURyYWdTdGFydCk7XG4gICAgICAgICAgICBub2RlLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3NlbGVjdHN0YXJ0JywgaGFuZGxlU2VsZWN0U3RhcnQpO1xuICAgICAgICAgICAgbm9kZS5zZXRBdHRyaWJ1dGUoJ2RyYWdnYWJsZScsICdmYWxzZScpO1xuICAgICAgICB9O1xuICAgIH1cbiAgICBjb25uZWN0RHJvcFRhcmdldCh0YXJnZXRJZCwgbm9kZSkge1xuICAgICAgICBjb25zdCBoYW5kbGVEcmFnRW50ZXIgPSAoZSk9PnRoaXMuaGFuZGxlRHJhZ0VudGVyKGUsIHRhcmdldElkKVxuICAgICAgICA7XG4gICAgICAgIGNvbnN0IGhhbmRsZURyYWdPdmVyID0gKGUpPT50aGlzLmhhbmRsZURyYWdPdmVyKGUsIHRhcmdldElkKVxuICAgICAgICA7XG4gICAgICAgIGNvbnN0IGhhbmRsZURyb3AgPSAoZSk9PnRoaXMuaGFuZGxlRHJvcChlLCB0YXJnZXRJZClcbiAgICAgICAgO1xuICAgICAgICBub2RlLmFkZEV2ZW50TGlzdGVuZXIoJ2RyYWdlbnRlcicsIGhhbmRsZURyYWdFbnRlcik7XG4gICAgICAgIG5vZGUuYWRkRXZlbnRMaXN0ZW5lcignZHJhZ292ZXInLCBoYW5kbGVEcmFnT3Zlcik7XG4gICAgICAgIG5vZGUuYWRkRXZlbnRMaXN0ZW5lcignZHJvcCcsIGhhbmRsZURyb3ApO1xuICAgICAgICByZXR1cm4gKCk9PntcbiAgICAgICAgICAgIG5vZGUucmVtb3ZlRXZlbnRMaXN0ZW5lcignZHJhZ2VudGVyJywgaGFuZGxlRHJhZ0VudGVyKTtcbiAgICAgICAgICAgIG5vZGUucmVtb3ZlRXZlbnRMaXN0ZW5lcignZHJhZ292ZXInLCBoYW5kbGVEcmFnT3Zlcik7XG4gICAgICAgICAgICBub2RlLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2Ryb3AnLCBoYW5kbGVEcm9wKTtcbiAgICAgICAgfTtcbiAgICB9XG4gICAgYWRkRXZlbnRMaXN0ZW5lcnModGFyZ2V0KSB7XG4gICAgICAgIC8vIFNTUiBGaXggKGh0dHBzOi8vZ2l0aHViLmNvbS9yZWFjdC1kbmQvcmVhY3QtZG5kL3B1bGwvODEzXG4gICAgICAgIGlmICghdGFyZ2V0LmFkZEV2ZW50TGlzdGVuZXIpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB0YXJnZXQuYWRkRXZlbnRMaXN0ZW5lcignZHJhZ3N0YXJ0JywgdGhpcy5oYW5kbGVUb3BEcmFnU3RhcnQpO1xuICAgICAgICB0YXJnZXQuYWRkRXZlbnRMaXN0ZW5lcignZHJhZ3N0YXJ0JywgdGhpcy5oYW5kbGVUb3BEcmFnU3RhcnRDYXB0dXJlLCB0cnVlKTtcbiAgICAgICAgdGFyZ2V0LmFkZEV2ZW50TGlzdGVuZXIoJ2RyYWdlbmQnLCB0aGlzLmhhbmRsZVRvcERyYWdFbmRDYXB0dXJlLCB0cnVlKTtcbiAgICAgICAgdGFyZ2V0LmFkZEV2ZW50TGlzdGVuZXIoJ2RyYWdlbnRlcicsIHRoaXMuaGFuZGxlVG9wRHJhZ0VudGVyKTtcbiAgICAgICAgdGFyZ2V0LmFkZEV2ZW50TGlzdGVuZXIoJ2RyYWdlbnRlcicsIHRoaXMuaGFuZGxlVG9wRHJhZ0VudGVyQ2FwdHVyZSwgdHJ1ZSk7XG4gICAgICAgIHRhcmdldC5hZGRFdmVudExpc3RlbmVyKCdkcmFnbGVhdmUnLCB0aGlzLmhhbmRsZVRvcERyYWdMZWF2ZUNhcHR1cmUsIHRydWUpO1xuICAgICAgICB0YXJnZXQuYWRkRXZlbnRMaXN0ZW5lcignZHJhZ292ZXInLCB0aGlzLmhhbmRsZVRvcERyYWdPdmVyKTtcbiAgICAgICAgdGFyZ2V0LmFkZEV2ZW50TGlzdGVuZXIoJ2RyYWdvdmVyJywgdGhpcy5oYW5kbGVUb3BEcmFnT3ZlckNhcHR1cmUsIHRydWUpO1xuICAgICAgICB0YXJnZXQuYWRkRXZlbnRMaXN0ZW5lcignZHJvcCcsIHRoaXMuaGFuZGxlVG9wRHJvcCk7XG4gICAgICAgIHRhcmdldC5hZGRFdmVudExpc3RlbmVyKCdkcm9wJywgdGhpcy5oYW5kbGVUb3BEcm9wQ2FwdHVyZSwgdHJ1ZSk7XG4gICAgfVxuICAgIHJlbW92ZUV2ZW50TGlzdGVuZXJzKHRhcmdldCkge1xuICAgICAgICAvLyBTU1IgRml4IChodHRwczovL2dpdGh1Yi5jb20vcmVhY3QtZG5kL3JlYWN0LWRuZC9wdWxsLzgxM1xuICAgICAgICBpZiAoIXRhcmdldC5yZW1vdmVFdmVudExpc3RlbmVyKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdGFyZ2V0LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2RyYWdzdGFydCcsIHRoaXMuaGFuZGxlVG9wRHJhZ1N0YXJ0KTtcbiAgICAgICAgdGFyZ2V0LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2RyYWdzdGFydCcsIHRoaXMuaGFuZGxlVG9wRHJhZ1N0YXJ0Q2FwdHVyZSwgdHJ1ZSk7XG4gICAgICAgIHRhcmdldC5yZW1vdmVFdmVudExpc3RlbmVyKCdkcmFnZW5kJywgdGhpcy5oYW5kbGVUb3BEcmFnRW5kQ2FwdHVyZSwgdHJ1ZSk7XG4gICAgICAgIHRhcmdldC5yZW1vdmVFdmVudExpc3RlbmVyKCdkcmFnZW50ZXInLCB0aGlzLmhhbmRsZVRvcERyYWdFbnRlcik7XG4gICAgICAgIHRhcmdldC5yZW1vdmVFdmVudExpc3RlbmVyKCdkcmFnZW50ZXInLCB0aGlzLmhhbmRsZVRvcERyYWdFbnRlckNhcHR1cmUsIHRydWUpO1xuICAgICAgICB0YXJnZXQucmVtb3ZlRXZlbnRMaXN0ZW5lcignZHJhZ2xlYXZlJywgdGhpcy5oYW5kbGVUb3BEcmFnTGVhdmVDYXB0dXJlLCB0cnVlKTtcbiAgICAgICAgdGFyZ2V0LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2RyYWdvdmVyJywgdGhpcy5oYW5kbGVUb3BEcmFnT3Zlcik7XG4gICAgICAgIHRhcmdldC5yZW1vdmVFdmVudExpc3RlbmVyKCdkcmFnb3ZlcicsIHRoaXMuaGFuZGxlVG9wRHJhZ092ZXJDYXB0dXJlLCB0cnVlKTtcbiAgICAgICAgdGFyZ2V0LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2Ryb3AnLCB0aGlzLmhhbmRsZVRvcERyb3ApO1xuICAgICAgICB0YXJnZXQucmVtb3ZlRXZlbnRMaXN0ZW5lcignZHJvcCcsIHRoaXMuaGFuZGxlVG9wRHJvcENhcHR1cmUsIHRydWUpO1xuICAgIH1cbiAgICBnZXRDdXJyZW50U291cmNlTm9kZU9wdGlvbnMoKSB7XG4gICAgICAgIGNvbnN0IHNvdXJjZUlkID0gdGhpcy5tb25pdG9yLmdldFNvdXJjZUlkKCk7XG4gICAgICAgIGNvbnN0IHNvdXJjZU5vZGVPcHRpb25zID0gdGhpcy5zb3VyY2VOb2RlT3B0aW9ucy5nZXQoc291cmNlSWQpO1xuICAgICAgICByZXR1cm4gX29iamVjdFNwcmVhZCh7XG4gICAgICAgICAgICBkcm9wRWZmZWN0OiB0aGlzLmFsdEtleVByZXNzZWQgPyAnY29weScgOiAnbW92ZSdcbiAgICAgICAgfSwgc291cmNlTm9kZU9wdGlvbnMgfHwge30pO1xuICAgIH1cbiAgICBnZXRDdXJyZW50RHJvcEVmZmVjdCgpIHtcbiAgICAgICAgaWYgKHRoaXMuaXNEcmFnZ2luZ05hdGl2ZUl0ZW0oKSkge1xuICAgICAgICAgICAgLy8gSXQgbWFrZXMgbW9yZSBzZW5zZSB0byBkZWZhdWx0IHRvICdjb3B5JyBmb3IgbmF0aXZlIHJlc291cmNlc1xuICAgICAgICAgICAgcmV0dXJuICdjb3B5JztcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5nZXRDdXJyZW50U291cmNlTm9kZU9wdGlvbnMoKS5kcm9wRWZmZWN0O1xuICAgIH1cbiAgICBnZXRDdXJyZW50U291cmNlUHJldmlld05vZGVPcHRpb25zKCkge1xuICAgICAgICBjb25zdCBzb3VyY2VJZCA9IHRoaXMubW9uaXRvci5nZXRTb3VyY2VJZCgpO1xuICAgICAgICBjb25zdCBzb3VyY2VQcmV2aWV3Tm9kZU9wdGlvbnMgPSB0aGlzLnNvdXJjZVByZXZpZXdOb2RlT3B0aW9ucy5nZXQoc291cmNlSWQpO1xuICAgICAgICByZXR1cm4gX29iamVjdFNwcmVhZCh7XG4gICAgICAgICAgICBhbmNob3JYOiAwLjUsXG4gICAgICAgICAgICBhbmNob3JZOiAwLjUsXG4gICAgICAgICAgICBjYXB0dXJlRHJhZ2dpbmdTdGF0ZTogZmFsc2VcbiAgICAgICAgfSwgc291cmNlUHJldmlld05vZGVPcHRpb25zIHx8IHt9KTtcbiAgICB9XG4gICAgaXNEcmFnZ2luZ05hdGl2ZUl0ZW0oKSB7XG4gICAgICAgIGNvbnN0IGl0ZW1UeXBlID0gdGhpcy5tb25pdG9yLmdldEl0ZW1UeXBlKCk7XG4gICAgICAgIHJldHVybiBPYmplY3Qua2V5cyhOYXRpdmVUeXBlcykuc29tZSgoa2V5KT0+TmF0aXZlVHlwZXNba2V5XSA9PT0gaXRlbVR5cGVcbiAgICAgICAgKTtcbiAgICB9XG4gICAgYmVnaW5EcmFnTmF0aXZlSXRlbSh0eXBlLCBkYXRhVHJhbnNmZXIpIHtcbiAgICAgICAgdGhpcy5jbGVhckN1cnJlbnREcmFnU291cmNlTm9kZSgpO1xuICAgICAgICB0aGlzLmN1cnJlbnROYXRpdmVTb3VyY2UgPSBjcmVhdGVOYXRpdmVEcmFnU291cmNlKHR5cGUsIGRhdGFUcmFuc2Zlcik7XG4gICAgICAgIHRoaXMuY3VycmVudE5hdGl2ZUhhbmRsZSA9IHRoaXMucmVnaXN0cnkuYWRkU291cmNlKHR5cGUsIHRoaXMuY3VycmVudE5hdGl2ZVNvdXJjZSk7XG4gICAgICAgIHRoaXMuYWN0aW9ucy5iZWdpbkRyYWcoW1xuICAgICAgICAgICAgdGhpcy5jdXJyZW50TmF0aXZlSGFuZGxlXG4gICAgICAgIF0pO1xuICAgIH1cbiAgICBzZXRDdXJyZW50RHJhZ1NvdXJjZU5vZGUobm9kZSkge1xuICAgICAgICB0aGlzLmNsZWFyQ3VycmVudERyYWdTb3VyY2VOb2RlKCk7XG4gICAgICAgIHRoaXMuY3VycmVudERyYWdTb3VyY2VOb2RlID0gbm9kZTtcbiAgICAgICAgLy8gQSB0aW1lb3V0IG9mID4gMCBpcyBuZWNlc3NhcnkgdG8gcmVzb2x2ZSBGaXJlZm94IGlzc3VlIHJlZmVyZW5jZWRcbiAgICAgICAgLy8gU2VlOlxuICAgICAgICAvLyAgICogaHR0cHM6Ly9naXRodWIuY29tL3JlYWN0LWRuZC9yZWFjdC1kbmQvcHVsbC85MjhcbiAgICAgICAgLy8gICAqIGh0dHBzOi8vZ2l0aHViLmNvbS9yZWFjdC1kbmQvcmVhY3QtZG5kL2lzc3Vlcy84NjlcbiAgICAgICAgY29uc3QgTU9VU0VfTU9WRV9USU1FT1VUID0gMTAwMDtcbiAgICAgICAgLy8gUmVjZWl2aW5nIGEgbW91c2UgZXZlbnQgaW4gdGhlIG1pZGRsZSBvZiBhIGRyYWdnaW5nIG9wZXJhdGlvblxuICAgICAgICAvLyBtZWFucyBpdCBoYXMgZW5kZWQgYW5kIHRoZSBkcmFnIHNvdXJjZSBub2RlIGRpc2FwcGVhcmVkIGZyb20gRE9NLFxuICAgICAgICAvLyBzbyB0aGUgYnJvd3NlciBkaWRuJ3QgZGlzcGF0Y2ggdGhlIGRyYWdlbmQgZXZlbnQuXG4gICAgICAgIC8vXG4gICAgICAgIC8vIFdlIG5lZWQgdG8gd2FpdCBiZWZvcmUgd2Ugc3RhcnQgbGlzdGVuaW5nIGZvciBtb3VzZW1vdmUgZXZlbnRzLlxuICAgICAgICAvLyBUaGlzIGlzIG5lZWRlZCBiZWNhdXNlIHRoZSBkcmFnIHByZXZpZXcgbmVlZHMgdG8gYmUgZHJhd24gb3IgZWxzZSBpdCBmaXJlcyBhbiAnbW91c2Vtb3ZlJyBldmVudFxuICAgICAgICAvLyBpbW1lZGlhdGVseSBpbiBzb21lIGJyb3dzZXJzLlxuICAgICAgICAvL1xuICAgICAgICAvLyBTZWU6XG4gICAgICAgIC8vICAgKiBodHRwczovL2dpdGh1Yi5jb20vcmVhY3QtZG5kL3JlYWN0LWRuZC9wdWxsLzkyOFxuICAgICAgICAvLyAgICogaHR0cHM6Ly9naXRodWIuY29tL3JlYWN0LWRuZC9yZWFjdC1kbmQvaXNzdWVzLzg2OVxuICAgICAgICAvL1xuICAgICAgICB0aGlzLm1vdXNlTW92ZVRpbWVvdXRUaW1lciA9IHNldFRpbWVvdXQoKCk9PntcbiAgICAgICAgICAgIHZhciByZWY7XG4gICAgICAgICAgICByZXR1cm4gKHJlZiA9IHRoaXMucm9vdEVsZW1lbnQpID09PSBudWxsIHx8IHJlZiA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVmLmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIHRoaXMuZW5kRHJhZ0lmU291cmNlV2FzUmVtb3ZlZEZyb21ET00sIHRydWUpO1xuICAgICAgICB9LCBNT1VTRV9NT1ZFX1RJTUVPVVQpO1xuICAgIH1cbiAgICBjbGVhckN1cnJlbnREcmFnU291cmNlTm9kZSgpIHtcbiAgICAgICAgaWYgKHRoaXMuY3VycmVudERyYWdTb3VyY2VOb2RlKSB7XG4gICAgICAgICAgICB0aGlzLmN1cnJlbnREcmFnU291cmNlTm9kZSA9IG51bGw7XG4gICAgICAgICAgICBpZiAodGhpcy5yb290RWxlbWVudCkge1xuICAgICAgICAgICAgICAgIHZhciByZWY7XG4gICAgICAgICAgICAgICAgKHJlZiA9IHRoaXMud2luZG93KSA9PT0gbnVsbCB8fCByZWYgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHJlZi5jbGVhclRpbWVvdXQodGhpcy5tb3VzZU1vdmVUaW1lb3V0VGltZXIgfHwgdW5kZWZpbmVkKTtcbiAgICAgICAgICAgICAgICB0aGlzLnJvb3RFbGVtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIHRoaXMuZW5kRHJhZ0lmU291cmNlV2FzUmVtb3ZlZEZyb21ET00sIHRydWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5tb3VzZU1vdmVUaW1lb3V0VGltZXIgPSBudWxsO1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBoYW5kbGVEcmFnU3RhcnQoZSwgc291cmNlSWQpIHtcbiAgICAgICAgaWYgKGUuZGVmYXVsdFByZXZlbnRlZCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmICghdGhpcy5kcmFnU3RhcnRTb3VyY2VJZHMpIHtcbiAgICAgICAgICAgIHRoaXMuZHJhZ1N0YXJ0U291cmNlSWRzID0gW107XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5kcmFnU3RhcnRTb3VyY2VJZHMudW5zaGlmdChzb3VyY2VJZCk7XG4gICAgfVxuICAgIGhhbmRsZURyYWdFbnRlcihfZSwgdGFyZ2V0SWQpIHtcbiAgICAgICAgdGhpcy5kcmFnRW50ZXJUYXJnZXRJZHMudW5zaGlmdCh0YXJnZXRJZCk7XG4gICAgfVxuICAgIGhhbmRsZURyYWdPdmVyKF9lLCB0YXJnZXRJZCkge1xuICAgICAgICBpZiAodGhpcy5kcmFnT3ZlclRhcmdldElkcyA9PT0gbnVsbCkge1xuICAgICAgICAgICAgdGhpcy5kcmFnT3ZlclRhcmdldElkcyA9IFtdO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuZHJhZ092ZXJUYXJnZXRJZHMudW5zaGlmdCh0YXJnZXRJZCk7XG4gICAgfVxuICAgIGhhbmRsZURyb3AoX2UsIHRhcmdldElkKSB7XG4gICAgICAgIHRoaXMuZHJvcFRhcmdldElkcy51bnNoaWZ0KHRhcmdldElkKTtcbiAgICB9XG4gICAgY29uc3RydWN0b3IobWFuYWdlciwgZ2xvYmFsQ29udGV4dCwgb3B0aW9ucyl7XG4gICAgICAgIHRoaXMuc291cmNlUHJldmlld05vZGVzID0gbmV3IE1hcCgpO1xuICAgICAgICB0aGlzLnNvdXJjZVByZXZpZXdOb2RlT3B0aW9ucyA9IG5ldyBNYXAoKTtcbiAgICAgICAgdGhpcy5zb3VyY2VOb2RlcyA9IG5ldyBNYXAoKTtcbiAgICAgICAgdGhpcy5zb3VyY2VOb2RlT3B0aW9ucyA9IG5ldyBNYXAoKTtcbiAgICAgICAgdGhpcy5kcmFnU3RhcnRTb3VyY2VJZHMgPSBudWxsO1xuICAgICAgICB0aGlzLmRyb3BUYXJnZXRJZHMgPSBbXTtcbiAgICAgICAgdGhpcy5kcmFnRW50ZXJUYXJnZXRJZHMgPSBbXTtcbiAgICAgICAgdGhpcy5jdXJyZW50TmF0aXZlU291cmNlID0gbnVsbDtcbiAgICAgICAgdGhpcy5jdXJyZW50TmF0aXZlSGFuZGxlID0gbnVsbDtcbiAgICAgICAgdGhpcy5jdXJyZW50RHJhZ1NvdXJjZU5vZGUgPSBudWxsO1xuICAgICAgICB0aGlzLmFsdEtleVByZXNzZWQgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5tb3VzZU1vdmVUaW1lb3V0VGltZXIgPSBudWxsO1xuICAgICAgICB0aGlzLmFzeW5jRW5kRHJhZ0ZyYW1lSWQgPSBudWxsO1xuICAgICAgICB0aGlzLmRyYWdPdmVyVGFyZ2V0SWRzID0gbnVsbDtcbiAgICAgICAgdGhpcy5sYXN0Q2xpZW50T2Zmc2V0ID0gbnVsbDtcbiAgICAgICAgdGhpcy5ob3ZlclJhZklkID0gbnVsbDtcbiAgICAgICAgdGhpcy5nZXRTb3VyY2VDbGllbnRPZmZzZXQgPSAoc291cmNlSWQpPT57XG4gICAgICAgICAgICBjb25zdCBzb3VyY2UgPSB0aGlzLnNvdXJjZU5vZGVzLmdldChzb3VyY2VJZCk7XG4gICAgICAgICAgICByZXR1cm4gc291cmNlICYmIGdldE5vZGVDbGllbnRPZmZzZXQoc291cmNlKSB8fCBudWxsO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLmVuZERyYWdOYXRpdmVJdGVtID0gKCk9PntcbiAgICAgICAgICAgIGlmICghdGhpcy5pc0RyYWdnaW5nTmF0aXZlSXRlbSgpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5hY3Rpb25zLmVuZERyYWcoKTtcbiAgICAgICAgICAgIGlmICh0aGlzLmN1cnJlbnROYXRpdmVIYW5kbGUpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnJlZ2lzdHJ5LnJlbW92ZVNvdXJjZSh0aGlzLmN1cnJlbnROYXRpdmVIYW5kbGUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5jdXJyZW50TmF0aXZlSGFuZGxlID0gbnVsbDtcbiAgICAgICAgICAgIHRoaXMuY3VycmVudE5hdGl2ZVNvdXJjZSA9IG51bGw7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuaXNOb2RlSW5Eb2N1bWVudCA9IChub2RlKT0+e1xuICAgICAgICAgICAgLy8gQ2hlY2sgdGhlIG5vZGUgZWl0aGVyIGluIHRoZSBtYWluIGRvY3VtZW50IG9yIGluIHRoZSBjdXJyZW50IGNvbnRleHRcbiAgICAgICAgICAgIHJldHVybiBCb29sZWFuKG5vZGUgJiYgdGhpcy5kb2N1bWVudCAmJiB0aGlzLmRvY3VtZW50LmJvZHkgJiYgdGhpcy5kb2N1bWVudC5ib2R5LmNvbnRhaW5zKG5vZGUpKTtcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5lbmREcmFnSWZTb3VyY2VXYXNSZW1vdmVkRnJvbURPTSA9ICgpPT57XG4gICAgICAgICAgICBjb25zdCBub2RlID0gdGhpcy5jdXJyZW50RHJhZ1NvdXJjZU5vZGU7XG4gICAgICAgICAgICBpZiAobm9kZSA9PSBudWxsIHx8IHRoaXMuaXNOb2RlSW5Eb2N1bWVudChub2RlKSkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0aGlzLmNsZWFyQ3VycmVudERyYWdTb3VyY2VOb2RlKCkgJiYgdGhpcy5tb25pdG9yLmlzRHJhZ2dpbmcoKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuYWN0aW9ucy5lbmREcmFnKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmNhbmNlbEhvdmVyKCk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuc2NoZWR1bGVIb3ZlciA9IChkcmFnT3ZlclRhcmdldElkcyk9PntcbiAgICAgICAgICAgIGlmICh0aGlzLmhvdmVyUmFmSWQgPT09IG51bGwgJiYgdHlwZW9mIHJlcXVlc3RBbmltYXRpb25GcmFtZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmhvdmVyUmFmSWQgPSByZXF1ZXN0QW5pbWF0aW9uRnJhbWUoKCk9PntcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMubW9uaXRvci5pc0RyYWdnaW5nKCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuYWN0aW9ucy5ob3ZlcihkcmFnT3ZlclRhcmdldElkcyB8fCBbXSwge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsaWVudE9mZnNldDogdGhpcy5sYXN0Q2xpZW50T2Zmc2V0XG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB0aGlzLmhvdmVyUmFmSWQgPSBudWxsO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICB0aGlzLmNhbmNlbEhvdmVyID0gKCk9PntcbiAgICAgICAgICAgIGlmICh0aGlzLmhvdmVyUmFmSWQgIT09IG51bGwgJiYgdHlwZW9mIGNhbmNlbEFuaW1hdGlvbkZyYW1lICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgICAgIGNhbmNlbEFuaW1hdGlvbkZyYW1lKHRoaXMuaG92ZXJSYWZJZCk7XG4gICAgICAgICAgICAgICAgdGhpcy5ob3ZlclJhZklkID0gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5oYW5kbGVUb3BEcmFnU3RhcnRDYXB0dXJlID0gKCk9PntcbiAgICAgICAgICAgIHRoaXMuY2xlYXJDdXJyZW50RHJhZ1NvdXJjZU5vZGUoKTtcbiAgICAgICAgICAgIHRoaXMuZHJhZ1N0YXJ0U291cmNlSWRzID0gW107XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuaGFuZGxlVG9wRHJhZ1N0YXJ0ID0gKGUpPT57XG4gICAgICAgICAgICBpZiAoZS5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgeyBkcmFnU3RhcnRTb3VyY2VJZHMgIH0gPSB0aGlzO1xuICAgICAgICAgICAgdGhpcy5kcmFnU3RhcnRTb3VyY2VJZHMgPSBudWxsO1xuICAgICAgICAgICAgY29uc3QgY2xpZW50T2Zmc2V0ID0gZ2V0RXZlbnRDbGllbnRPZmZzZXQoZSk7XG4gICAgICAgICAgICAvLyBBdm9pZCBjcmFzaGluZyBpZiB3ZSBtaXNzZWQgYSBkcm9wIGV2ZW50IG9yIG91ciBwcmV2aW91cyBkcmFnIGRpZWRcbiAgICAgICAgICAgIGlmICh0aGlzLm1vbml0b3IuaXNEcmFnZ2luZygpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5hY3Rpb25zLmVuZERyYWcoKTtcbiAgICAgICAgICAgICAgICB0aGlzLmNhbmNlbEhvdmVyKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBEb24ndCBwdWJsaXNoIHRoZSBzb3VyY2UganVzdCB5ZXQgKHNlZSB3aHkgYmVsb3cpXG4gICAgICAgICAgICB0aGlzLmFjdGlvbnMuYmVnaW5EcmFnKGRyYWdTdGFydFNvdXJjZUlkcyB8fCBbXSwge1xuICAgICAgICAgICAgICAgIHB1Ymxpc2hTb3VyY2U6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGdldFNvdXJjZUNsaWVudE9mZnNldDogdGhpcy5nZXRTb3VyY2VDbGllbnRPZmZzZXQsXG4gICAgICAgICAgICAgICAgY2xpZW50T2Zmc2V0XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnN0IHsgZGF0YVRyYW5zZmVyICB9ID0gZTtcbiAgICAgICAgICAgIGNvbnN0IG5hdGl2ZVR5cGUgPSBtYXRjaE5hdGl2ZUl0ZW1UeXBlKGRhdGFUcmFuc2Zlcik7XG4gICAgICAgICAgICBpZiAodGhpcy5tb25pdG9yLmlzRHJhZ2dpbmcoKSkge1xuICAgICAgICAgICAgICAgIGlmIChkYXRhVHJhbnNmZXIgJiYgdHlwZW9mIGRhdGFUcmFuc2Zlci5zZXREcmFnSW1hZ2UgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gVXNlIGN1c3RvbSBkcmFnIGltYWdlIGlmIHVzZXIgc3BlY2lmaWVzIGl0LlxuICAgICAgICAgICAgICAgICAgICAvLyBJZiBjaGlsZCBkcmFnIHNvdXJjZSByZWZ1c2VzIGRyYWcgYnV0IHBhcmVudCBhZ3JlZXMsXG4gICAgICAgICAgICAgICAgICAgIC8vIHVzZSBwYXJlbnQncyBub2RlIGFzIGRyYWcgaW1hZ2UuIE5laXRoZXIgd29ya3MgaW4gSUUgdGhvdWdoLlxuICAgICAgICAgICAgICAgICAgICBjb25zdCBzb3VyY2VJZCA9IHRoaXMubW9uaXRvci5nZXRTb3VyY2VJZCgpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBzb3VyY2VOb2RlID0gdGhpcy5zb3VyY2VOb2Rlcy5nZXQoc291cmNlSWQpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBkcmFnUHJldmlldyA9IHRoaXMuc291cmNlUHJldmlld05vZGVzLmdldChzb3VyY2VJZCkgfHwgc291cmNlTm9kZTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGRyYWdQcmV2aWV3KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB7IGFuY2hvclggLCBhbmNob3JZICwgb2Zmc2V0WCAsIG9mZnNldFkgIH0gPSB0aGlzLmdldEN1cnJlbnRTb3VyY2VQcmV2aWV3Tm9kZU9wdGlvbnMoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGFuY2hvclBvaW50ID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFuY2hvclgsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYW5jaG9yWVxuICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG9mZnNldFBvaW50ID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9mZnNldFgsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb2Zmc2V0WVxuICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRyYWdQcmV2aWV3T2Zmc2V0ID0gZ2V0RHJhZ1ByZXZpZXdPZmZzZXQoc291cmNlTm9kZSwgZHJhZ1ByZXZpZXcsIGNsaWVudE9mZnNldCwgYW5jaG9yUG9pbnQsIG9mZnNldFBvaW50KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGFUcmFuc2Zlci5zZXREcmFnSW1hZ2UoZHJhZ1ByZXZpZXcsIGRyYWdQcmV2aWV3T2Zmc2V0LngsIGRyYWdQcmV2aWV3T2Zmc2V0LnkpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIEZpcmVmb3ggd29uJ3QgZHJhZyB3aXRob3V0IHNldHRpbmcgZGF0YVxuICAgICAgICAgICAgICAgICAgICBkYXRhVHJhbnNmZXIgPT09IG51bGwgfHwgZGF0YVRyYW5zZmVyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBkYXRhVHJhbnNmZXIuc2V0RGF0YSgnYXBwbGljYXRpb24vanNvbicsIHt9KTtcbiAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgICAgICAvLyBJRSBkb2Vzbid0IHN1cHBvcnQgTUlNRSB0eXBlcyBpbiBzZXREYXRhXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIFN0b3JlIGRyYWcgc291cmNlIG5vZGUgc28gd2UgY2FuIGNoZWNrIHdoZXRoZXJcbiAgICAgICAgICAgICAgICAvLyBpdCBpcyByZW1vdmVkIGZyb20gRE9NIGFuZCB0cmlnZ2VyIGVuZERyYWcgbWFudWFsbHkuXG4gICAgICAgICAgICAgICAgdGhpcy5zZXRDdXJyZW50RHJhZ1NvdXJjZU5vZGUoZS50YXJnZXQpO1xuICAgICAgICAgICAgICAgIC8vIE5vdyB3ZSBhcmUgcmVhZHkgdG8gcHVibGlzaCB0aGUgZHJhZyBzb3VyY2UuLiBvciBhcmUgd2Ugbm90P1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgY2FwdHVyZURyYWdnaW5nU3RhdGUgIH0gPSB0aGlzLmdldEN1cnJlbnRTb3VyY2VQcmV2aWV3Tm9kZU9wdGlvbnMoKTtcbiAgICAgICAgICAgICAgICBpZiAoIWNhcHR1cmVEcmFnZ2luZ1N0YXRlKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFVzdWFsbHkgd2Ugd2FudCB0byBwdWJsaXNoIGl0IGluIHRoZSBuZXh0IHRpY2sgc28gdGhhdCBicm93c2VyXG4gICAgICAgICAgICAgICAgICAgIC8vIGlzIGFibGUgdG8gc2NyZWVuc2hvdCB0aGUgY3VycmVudCAobm90IHlldCBkcmFnZ2luZykgc3RhdGUuXG4gICAgICAgICAgICAgICAgICAgIC8vXG4gICAgICAgICAgICAgICAgICAgIC8vIEl0IGFsc28gbmVhdGx5IGF2b2lkcyBhIHNpdHVhdGlvbiB3aGVyZSByZW5kZXIoKSByZXR1cm5zIG51bGxcbiAgICAgICAgICAgICAgICAgICAgLy8gaW4gdGhlIHNhbWUgdGljayBmb3IgdGhlIHNvdXJjZSBlbGVtZW50LCBhbmQgYnJvd3NlciBmcmVha3Mgb3V0LlxuICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpPT50aGlzLmFjdGlvbnMucHVibGlzaERyYWdTb3VyY2UoKVxuICAgICAgICAgICAgICAgICAgICAsIDApO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIEluIHNvbWUgY2FzZXMgdGhlIHVzZXIgbWF5IHdhbnQgdG8gb3ZlcnJpZGUgdGhpcyBiZWhhdmlvciwgZS5nLlxuICAgICAgICAgICAgICAgICAgICAvLyB0byB3b3JrIGFyb3VuZCBJRSBub3Qgc3VwcG9ydGluZyBjdXN0b20gZHJhZyBwcmV2aWV3cy5cbiAgICAgICAgICAgICAgICAgICAgLy9cbiAgICAgICAgICAgICAgICAgICAgLy8gV2hlbiB1c2luZyBhIGN1c3RvbSBkcmFnIGxheWVyLCB0aGUgb25seSB3YXkgdG8gcHJldmVudFxuICAgICAgICAgICAgICAgICAgICAvLyB0aGUgZGVmYXVsdCBkcmFnIHByZXZpZXcgZnJvbSBkcmF3aW5nIGluIElFIGlzIHRvIHNjcmVlbnNob3RcbiAgICAgICAgICAgICAgICAgICAgLy8gdGhlIGRyYWdnaW5nIHN0YXRlIGluIHdoaWNoIHRoZSBub2RlIGl0c2VsZiBoYXMgemVybyBvcGFjaXR5XG4gICAgICAgICAgICAgICAgICAgIC8vIGFuZCBoZWlnaHQuIEluIHRoaXMgY2FzZSwgdGhvdWdoLCByZXR1cm5pbmcgbnVsbCBmcm9tIHJlbmRlcigpXG4gICAgICAgICAgICAgICAgICAgIC8vIHdpbGwgYWJydXB0bHkgZW5kIHRoZSBkcmFnZ2luZywgd2hpY2ggaXMgbm90IG9idmlvdXMuXG4gICAgICAgICAgICAgICAgICAgIC8vXG4gICAgICAgICAgICAgICAgICAgIC8vIFRoaXMgaXMgdGhlIHJlYXNvbiBzdWNoIGJlaGF2aW9yIGlzIHN0cmljdGx5IG9wdC1pbi5cbiAgICAgICAgICAgICAgICAgICAgdGhpcy5hY3Rpb25zLnB1Ymxpc2hEcmFnU291cmNlKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIGlmIChuYXRpdmVUeXBlKSB7XG4gICAgICAgICAgICAgICAgLy8gQSBuYXRpdmUgaXRlbSAoc3VjaCBhcyBVUkwpIGRyYWdnZWQgZnJvbSBpbnNpZGUgdGhlIGRvY3VtZW50XG4gICAgICAgICAgICAgICAgdGhpcy5iZWdpbkRyYWdOYXRpdmVJdGVtKG5hdGl2ZVR5cGUpO1xuICAgICAgICAgICAgfSBlbHNlIGlmIChkYXRhVHJhbnNmZXIgJiYgIWRhdGFUcmFuc2Zlci50eXBlcyAmJiAoZS50YXJnZXQgJiYgIWUudGFyZ2V0Lmhhc0F0dHJpYnV0ZSB8fCAhZS50YXJnZXQuaGFzQXR0cmlidXRlKCdkcmFnZ2FibGUnKSkpIHtcbiAgICAgICAgICAgICAgICAvLyBMb29rcyBsaWtlIGEgU2FmYXJpIGJ1ZzogZGF0YVRyYW5zZmVyLnR5cGVzIGlzIG51bGwsIGJ1dCB0aGVyZSB3YXMgbm8gZHJhZ2dhYmxlLlxuICAgICAgICAgICAgICAgIC8vIEp1c3QgbGV0IGl0IGRyYWcuIEl0J3MgYSBuYXRpdmUgdHlwZSAoVVJMIG9yIHRleHQpIGFuZCB3aWxsIGJlIHBpY2tlZCB1cCBpblxuICAgICAgICAgICAgICAgIC8vIGRyYWdlbnRlciBoYW5kbGVyLlxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgLy8gSWYgYnkgdGhpcyB0aW1lIG5vIGRyYWcgc291cmNlIHJlYWN0ZWQsIHRlbGwgYnJvd3NlciBub3QgdG8gZHJhZy5cbiAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuaGFuZGxlVG9wRHJhZ0VuZENhcHR1cmUgPSAoKT0+e1xuICAgICAgICAgICAgaWYgKHRoaXMuY2xlYXJDdXJyZW50RHJhZ1NvdXJjZU5vZGUoKSAmJiB0aGlzLm1vbml0b3IuaXNEcmFnZ2luZygpKSB7XG4gICAgICAgICAgICAgICAgLy8gRmlyZWZveCBjYW4gZGlzcGF0Y2ggdGhpcyBldmVudCBpbiBhbiBpbmZpbml0ZSBsb29wXG4gICAgICAgICAgICAgICAgLy8gaWYgZHJhZ2VuZCBoYW5kbGVyIGRvZXMgc29tZXRoaW5nIGxpa2Ugc2hvd2luZyBhbiBhbGVydC5cbiAgICAgICAgICAgICAgICAvLyBPbmx5IHByb2NlZWQgaWYgd2UgaGF2ZSBub3QgaGFuZGxlZCBpdCBhbHJlYWR5LlxuICAgICAgICAgICAgICAgIHRoaXMuYWN0aW9ucy5lbmREcmFnKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmNhbmNlbEhvdmVyKCk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuaGFuZGxlVG9wRHJhZ0VudGVyQ2FwdHVyZSA9IChlKT0+e1xuICAgICAgICAgICAgdGhpcy5kcmFnRW50ZXJUYXJnZXRJZHMgPSBbXTtcbiAgICAgICAgICAgIGlmICh0aGlzLmlzRHJhZ2dpbmdOYXRpdmVJdGVtKCkpIHtcbiAgICAgICAgICAgICAgICB2YXIgcmVmO1xuICAgICAgICAgICAgICAgIChyZWYgPSB0aGlzLmN1cnJlbnROYXRpdmVTb3VyY2UpID09PSBudWxsIHx8IHJlZiA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVmLmxvYWREYXRhVHJhbnNmZXIoZS5kYXRhVHJhbnNmZXIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgaXNGaXJzdEVudGVyID0gdGhpcy5lbnRlckxlYXZlQ291bnRlci5lbnRlcihlLnRhcmdldCk7XG4gICAgICAgICAgICBpZiAoIWlzRmlyc3RFbnRlciB8fCB0aGlzLm1vbml0b3IuaXNEcmFnZ2luZygpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgeyBkYXRhVHJhbnNmZXIgIH0gPSBlO1xuICAgICAgICAgICAgY29uc3QgbmF0aXZlVHlwZSA9IG1hdGNoTmF0aXZlSXRlbVR5cGUoZGF0YVRyYW5zZmVyKTtcbiAgICAgICAgICAgIGlmIChuYXRpdmVUeXBlKSB7XG4gICAgICAgICAgICAgICAgLy8gQSBuYXRpdmUgaXRlbSAoc3VjaCBhcyBmaWxlIG9yIFVSTCkgZHJhZ2dlZCBmcm9tIG91dHNpZGUgdGhlIGRvY3VtZW50XG4gICAgICAgICAgICAgICAgdGhpcy5iZWdpbkRyYWdOYXRpdmVJdGVtKG5hdGl2ZVR5cGUsIGRhdGFUcmFuc2Zlcik7XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuaGFuZGxlVG9wRHJhZ0VudGVyID0gKGUpPT57XG4gICAgICAgICAgICBjb25zdCB7IGRyYWdFbnRlclRhcmdldElkcyAgfSA9IHRoaXM7XG4gICAgICAgICAgICB0aGlzLmRyYWdFbnRlclRhcmdldElkcyA9IFtdO1xuICAgICAgICAgICAgaWYgKCF0aGlzLm1vbml0b3IuaXNEcmFnZ2luZygpKSB7XG4gICAgICAgICAgICAgICAgLy8gVGhpcyBpcyBwcm9iYWJseSBhIG5hdGl2ZSBpdGVtIHR5cGUgd2UgZG9uJ3QgdW5kZXJzdGFuZC5cbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmFsdEtleVByZXNzZWQgPSBlLmFsdEtleTtcbiAgICAgICAgICAgIC8vIElmIHRoZSB0YXJnZXQgY2hhbmdlcyBwb3NpdGlvbiBhcyB0aGUgcmVzdWx0IG9mIGBkcmFnZW50ZXJgLCBgZHJhZ292ZXJgIG1pZ2h0IHN0aWxsXG4gICAgICAgICAgICAvLyBnZXQgZGlzcGF0Y2hlZCBkZXNwaXRlIHRhcmdldCBiZWluZyBubyBsb25nZXIgdGhlcmUuIFRoZSBlYXN5IHNvbHV0aW9uIGlzIHRvIGNoZWNrXG4gICAgICAgICAgICAvLyB3aGV0aGVyIHRoZXJlIGFjdHVhbGx5IGlzIGEgdGFyZ2V0IGJlZm9yZSBmaXJpbmcgYGhvdmVyYC5cbiAgICAgICAgICAgIGlmIChkcmFnRW50ZXJUYXJnZXRJZHMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgIHRoaXMuYWN0aW9ucy5ob3ZlcihkcmFnRW50ZXJUYXJnZXRJZHMsIHtcbiAgICAgICAgICAgICAgICAgICAgY2xpZW50T2Zmc2V0OiBnZXRFdmVudENsaWVudE9mZnNldChlKVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgY2FuRHJvcCA9IGRyYWdFbnRlclRhcmdldElkcy5zb21lKCh0YXJnZXRJZCk9PnRoaXMubW9uaXRvci5jYW5Ecm9wT25UYXJnZXQodGFyZ2V0SWQpXG4gICAgICAgICAgICApO1xuICAgICAgICAgICAgaWYgKGNhbkRyb3ApIHtcbiAgICAgICAgICAgICAgICAvLyBJRSByZXF1aXJlcyB0aGlzIHRvIGZpcmUgZHJhZ292ZXIgZXZlbnRzXG4gICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgIGlmIChlLmRhdGFUcmFuc2Zlcikge1xuICAgICAgICAgICAgICAgICAgICBlLmRhdGFUcmFuc2Zlci5kcm9wRWZmZWN0ID0gdGhpcy5nZXRDdXJyZW50RHJvcEVmZmVjdCgpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5oYW5kbGVUb3BEcmFnT3ZlckNhcHR1cmUgPSAoZSk9PntcbiAgICAgICAgICAgIHRoaXMuZHJhZ092ZXJUYXJnZXRJZHMgPSBbXTtcbiAgICAgICAgICAgIGlmICh0aGlzLmlzRHJhZ2dpbmdOYXRpdmVJdGVtKCkpIHtcbiAgICAgICAgICAgICAgICB2YXIgcmVmO1xuICAgICAgICAgICAgICAgIChyZWYgPSB0aGlzLmN1cnJlbnROYXRpdmVTb3VyY2UpID09PSBudWxsIHx8IHJlZiA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVmLmxvYWREYXRhVHJhbnNmZXIoZS5kYXRhVHJhbnNmZXIpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICB0aGlzLmhhbmRsZVRvcERyYWdPdmVyID0gKGUpPT57XG4gICAgICAgICAgICBjb25zdCB7IGRyYWdPdmVyVGFyZ2V0SWRzICB9ID0gdGhpcztcbiAgICAgICAgICAgIHRoaXMuZHJhZ092ZXJUYXJnZXRJZHMgPSBbXTtcbiAgICAgICAgICAgIGlmICghdGhpcy5tb25pdG9yLmlzRHJhZ2dpbmcoKSkge1xuICAgICAgICAgICAgICAgIC8vIFRoaXMgaXMgcHJvYmFibHkgYSBuYXRpdmUgaXRlbSB0eXBlIHdlIGRvbid0IHVuZGVyc3RhbmQuXG4gICAgICAgICAgICAgICAgLy8gUHJldmVudCBkZWZhdWx0IFwiZHJvcCBhbmQgYmxvdyBhd2F5IHRoZSB3aG9sZSBkb2N1bWVudFwiIGFjdGlvbi5cbiAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgaWYgKGUuZGF0YVRyYW5zZmVyKSB7XG4gICAgICAgICAgICAgICAgICAgIGUuZGF0YVRyYW5zZmVyLmRyb3BFZmZlY3QgPSAnbm9uZSc7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuYWx0S2V5UHJlc3NlZCA9IGUuYWx0S2V5O1xuICAgICAgICAgICAgdGhpcy5sYXN0Q2xpZW50T2Zmc2V0ID0gZ2V0RXZlbnRDbGllbnRPZmZzZXQoZSk7XG4gICAgICAgICAgICB0aGlzLnNjaGVkdWxlSG92ZXIoZHJhZ092ZXJUYXJnZXRJZHMpO1xuICAgICAgICAgICAgY29uc3QgY2FuRHJvcCA9IChkcmFnT3ZlclRhcmdldElkcyB8fCBbXSkuc29tZSgodGFyZ2V0SWQpPT50aGlzLm1vbml0b3IuY2FuRHJvcE9uVGFyZ2V0KHRhcmdldElkKVxuICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIGlmIChjYW5Ecm9wKSB7XG4gICAgICAgICAgICAgICAgLy8gU2hvdyB1c2VyLXNwZWNpZmllZCBkcm9wIGVmZmVjdC5cbiAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgaWYgKGUuZGF0YVRyYW5zZmVyKSB7XG4gICAgICAgICAgICAgICAgICAgIGUuZGF0YVRyYW5zZmVyLmRyb3BFZmZlY3QgPSB0aGlzLmdldEN1cnJlbnREcm9wRWZmZWN0KCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmlzRHJhZ2dpbmdOYXRpdmVJdGVtKCkpIHtcbiAgICAgICAgICAgICAgICAvLyBEb24ndCBzaG93IGEgbmljZSBjdXJzb3IgYnV0IHN0aWxsIHByZXZlbnQgZGVmYXVsdFxuICAgICAgICAgICAgICAgIC8vIFwiZHJvcCBhbmQgYmxvdyBhd2F5IHRoZSB3aG9sZSBkb2N1bWVudFwiIGFjdGlvbi5cbiAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgICBpZiAoZS5kYXRhVHJhbnNmZXIpIHtcbiAgICAgICAgICAgICAgICAgICAgZS5kYXRhVHJhbnNmZXIuZHJvcEVmZmVjdCA9ICdub25lJztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuaGFuZGxlVG9wRHJhZ0xlYXZlQ2FwdHVyZSA9IChlKT0+e1xuICAgICAgICAgICAgaWYgKHRoaXMuaXNEcmFnZ2luZ05hdGl2ZUl0ZW0oKSkge1xuICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGlzTGFzdExlYXZlID0gdGhpcy5lbnRlckxlYXZlQ291bnRlci5sZWF2ZShlLnRhcmdldCk7XG4gICAgICAgICAgICBpZiAoIWlzTGFzdExlYXZlKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHRoaXMuaXNEcmFnZ2luZ05hdGl2ZUl0ZW0oKSkge1xuICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCk9PnRoaXMuZW5kRHJhZ05hdGl2ZUl0ZW0oKVxuICAgICAgICAgICAgICAgICwgMCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmNhbmNlbEhvdmVyKCk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuaGFuZGxlVG9wRHJvcENhcHR1cmUgPSAoZSk9PntcbiAgICAgICAgICAgIHRoaXMuZHJvcFRhcmdldElkcyA9IFtdO1xuICAgICAgICAgICAgaWYgKHRoaXMuaXNEcmFnZ2luZ05hdGl2ZUl0ZW0oKSkge1xuICAgICAgICAgICAgICAgIHZhciByZWY7XG4gICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgIChyZWYgPSB0aGlzLmN1cnJlbnROYXRpdmVTb3VyY2UpID09PSBudWxsIHx8IHJlZiA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVmLmxvYWREYXRhVHJhbnNmZXIoZS5kYXRhVHJhbnNmZXIpO1xuICAgICAgICAgICAgfSBlbHNlIGlmIChtYXRjaE5hdGl2ZUl0ZW1UeXBlKGUuZGF0YVRyYW5zZmVyKSkge1xuICAgICAgICAgICAgICAgIC8vIERyYWdnaW5nIHNvbWUgZWxlbWVudHMsIGxpa2UgPGE+IGFuZCA8aW1nPiBtYXkgc3RpbGwgYmVoYXZlIGxpa2UgYSBuYXRpdmUgZHJhZyBldmVudCxcbiAgICAgICAgICAgICAgICAvLyBldmVuIGlmIHRoZSBjdXJyZW50IGRyYWcgZXZlbnQgbWF0Y2hlcyBhIHVzZXItZGVmaW5lZCB0eXBlLlxuICAgICAgICAgICAgICAgIC8vIFN0b3AgdGhlIGRlZmF1bHQgYmVoYXZpb3Igd2hlbiB3ZSdyZSBub3QgZXhwZWN0aW5nIGEgbmF0aXZlIGl0ZW0gdG8gYmUgZHJvcHBlZC5cbiAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmVudGVyTGVhdmVDb3VudGVyLnJlc2V0KCk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuaGFuZGxlVG9wRHJvcCA9IChlKT0+e1xuICAgICAgICAgICAgY29uc3QgeyBkcm9wVGFyZ2V0SWRzICB9ID0gdGhpcztcbiAgICAgICAgICAgIHRoaXMuZHJvcFRhcmdldElkcyA9IFtdO1xuICAgICAgICAgICAgdGhpcy5hY3Rpb25zLmhvdmVyKGRyb3BUYXJnZXRJZHMsIHtcbiAgICAgICAgICAgICAgICBjbGllbnRPZmZzZXQ6IGdldEV2ZW50Q2xpZW50T2Zmc2V0KGUpXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRoaXMuYWN0aW9ucy5kcm9wKHtcbiAgICAgICAgICAgICAgICBkcm9wRWZmZWN0OiB0aGlzLmdldEN1cnJlbnREcm9wRWZmZWN0KClcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKHRoaXMuaXNEcmFnZ2luZ05hdGl2ZUl0ZW0oKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuZW5kRHJhZ05hdGl2ZUl0ZW0oKTtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5tb25pdG9yLmlzRHJhZ2dpbmcoKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuYWN0aW9ucy5lbmREcmFnKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmNhbmNlbEhvdmVyKCk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuaGFuZGxlU2VsZWN0U3RhcnQgPSAoZSk9PntcbiAgICAgICAgICAgIGNvbnN0IHRhcmdldCA9IGUudGFyZ2V0O1xuICAgICAgICAgICAgLy8gT25seSBJRSByZXF1aXJlcyB1cyB0byBleHBsaWNpdGx5IHNheVxuICAgICAgICAgICAgLy8gd2Ugd2FudCBkcmFnIGRyb3Agb3BlcmF0aW9uIHRvIHN0YXJ0XG4gICAgICAgICAgICBpZiAodHlwZW9mIHRhcmdldC5kcmFnRHJvcCAhPT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElucHV0cyBhbmQgdGV4dGFyZWFzIHNob3VsZCBiZSBzZWxlY3RhYmxlXG4gICAgICAgICAgICBpZiAodGFyZ2V0LnRhZ05hbWUgPT09ICdJTlBVVCcgfHwgdGFyZ2V0LnRhZ05hbWUgPT09ICdTRUxFQ1QnIHx8IHRhcmdldC50YWdOYW1lID09PSAnVEVYVEFSRUEnIHx8IHRhcmdldC5pc0NvbnRlbnRFZGl0YWJsZSkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIEZvciBvdGhlciB0YXJnZXRzLCBhc2sgSUVcbiAgICAgICAgICAgIC8vIHRvIGVuYWJsZSBkcmFnIGFuZCBkcm9wXG4gICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICB0YXJnZXQuZHJhZ0Ryb3AoKTtcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5vcHRpb25zID0gbmV3IE9wdGlvbnNSZWFkZXIoZ2xvYmFsQ29udGV4dCwgb3B0aW9ucyk7XG4gICAgICAgIHRoaXMuYWN0aW9ucyA9IG1hbmFnZXIuZ2V0QWN0aW9ucygpO1xuICAgICAgICB0aGlzLm1vbml0b3IgPSBtYW5hZ2VyLmdldE1vbml0b3IoKTtcbiAgICAgICAgdGhpcy5yZWdpc3RyeSA9IG1hbmFnZXIuZ2V0UmVnaXN0cnkoKTtcbiAgICAgICAgdGhpcy5lbnRlckxlYXZlQ291bnRlciA9IG5ldyBFbnRlckxlYXZlQ291bnRlcih0aGlzLmlzTm9kZUluRG9jdW1lbnQpO1xuICAgIH1cbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9SFRNTDVCYWNrZW5kSW1wbC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/HTML5BackendImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/MonotonicInterpolant.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/MonotonicInterpolant.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonotonicInterpolant: () => (/* binding */ MonotonicInterpolant)\n/* harmony export */ });\nclass MonotonicInterpolant {\n    interpolate(x) {\n        const { xs , ys , c1s , c2s , c3s  } = this;\n        // The rightmost point in the dataset should give an exact result\n        let i = xs.length - 1;\n        if (x === xs[i]) {\n            return ys[i];\n        }\n        // Search for the interval x is in, returning the corresponding y if x is one of the original xs\n        let low = 0;\n        let high = c3s.length - 1;\n        let mid;\n        while(low <= high){\n            mid = Math.floor(0.5 * (low + high));\n            const xHere = xs[mid];\n            if (xHere < x) {\n                low = mid + 1;\n            } else if (xHere > x) {\n                high = mid - 1;\n            } else {\n                return ys[mid];\n            }\n        }\n        i = Math.max(0, high);\n        // Interpolate\n        const diff = x - xs[i];\n        const diffSq = diff * diff;\n        return ys[i] + c1s[i] * diff + c2s[i] * diffSq + c3s[i] * diff * diffSq;\n    }\n    constructor(xs, ys){\n        const { length  } = xs;\n        // Rearrange xs and ys so that xs is sorted\n        const indexes = [];\n        for(let i = 0; i < length; i++){\n            indexes.push(i);\n        }\n        indexes.sort((a, b)=>xs[a] < xs[b] ? -1 : 1\n        );\n        // Get consecutive differences and slopes\n        const dys = [];\n        const dxs = [];\n        const ms = [];\n        let dx;\n        let dy;\n        for(let i1 = 0; i1 < length - 1; i1++){\n            dx = xs[i1 + 1] - xs[i1];\n            dy = ys[i1 + 1] - ys[i1];\n            dxs.push(dx);\n            dys.push(dy);\n            ms.push(dy / dx);\n        }\n        // Get degree-1 coefficients\n        const c1s = [\n            ms[0]\n        ];\n        for(let i2 = 0; i2 < dxs.length - 1; i2++){\n            const m2 = ms[i2];\n            const mNext = ms[i2 + 1];\n            if (m2 * mNext <= 0) {\n                c1s.push(0);\n            } else {\n                dx = dxs[i2];\n                const dxNext = dxs[i2 + 1];\n                const common = dx + dxNext;\n                c1s.push(3 * common / ((common + dxNext) / m2 + (common + dx) / mNext));\n            }\n        }\n        c1s.push(ms[ms.length - 1]);\n        // Get degree-2 and degree-3 coefficients\n        const c2s = [];\n        const c3s = [];\n        let m;\n        for(let i3 = 0; i3 < c1s.length - 1; i3++){\n            m = ms[i3];\n            const c1 = c1s[i3];\n            const invDx = 1 / dxs[i3];\n            const common = c1 + c1s[i3 + 1] - m - m;\n            c2s.push((m - c1 - common) * invDx);\n            c3s.push(common * invDx * invDx);\n        }\n        this.xs = xs;\n        this.ys = ys;\n        this.c1s = c1s;\n        this.c2s = c2s;\n        this.c3s = c3s;\n    }\n}\n\n//# sourceMappingURL=MonotonicInterpolant.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/MonotonicInterpolant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/NativeDragSource.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/NativeDragSources/NativeDragSource.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NativeDragSource: () => (/* binding */ NativeDragSource)\n/* harmony export */ });\nclass NativeDragSource {\n    initializeExposedProperties() {\n        Object.keys(this.config.exposeProperties).forEach((property)=>{\n            Object.defineProperty(this.item, property, {\n                configurable: true,\n                enumerable: true,\n                get () {\n                    // eslint-disable-next-line no-console\n                    console.warn(`Browser doesn't allow reading \"${property}\" until the drop event.`);\n                    return null;\n                }\n            });\n        });\n    }\n    loadDataTransfer(dataTransfer) {\n        if (dataTransfer) {\n            const newProperties = {};\n            Object.keys(this.config.exposeProperties).forEach((property)=>{\n                const propertyFn = this.config.exposeProperties[property];\n                if (propertyFn != null) {\n                    newProperties[property] = {\n                        value: propertyFn(dataTransfer, this.config.matchesTypes),\n                        configurable: true,\n                        enumerable: true\n                    };\n                }\n            });\n            Object.defineProperties(this.item, newProperties);\n        }\n    }\n    canDrag() {\n        return true;\n    }\n    beginDrag() {\n        return this.item;\n    }\n    isDragging(monitor, handle) {\n        return handle === monitor.getSourceId();\n    }\n    endDrag() {\n    // empty\n    }\n    constructor(config){\n        this.config = config;\n        this.item = {};\n        this.initializeExposedProperties();\n    }\n}\n\n//# sourceMappingURL=NativeDragSource.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/NativeDragSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/getDataFromDataTransfer.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/NativeDragSources/getDataFromDataTransfer.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDataFromDataTransfer: () => (/* binding */ getDataFromDataTransfer)\n/* harmony export */ });\nfunction getDataFromDataTransfer(dataTransfer, typesToTry, defaultValue) {\n    const result = typesToTry.reduce((resultSoFar, typeToTry)=>resultSoFar || dataTransfer.getData(typeToTry)\n    , '');\n    return result != null ? result : defaultValue;\n}\n\n//# sourceMappingURL=getDataFromDataTransfer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9OYXRpdmVEcmFnU291cmNlcy9nZXREYXRhRnJvbURhdGFUcmFuc2Zlci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9yZWFjdC1kbmQtaHRtbDUtYmFja2VuZC9kaXN0L05hdGl2ZURyYWdTb3VyY2VzL2dldERhdGFGcm9tRGF0YVRyYW5zZmVyLmpzP2MyNWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGdldERhdGFGcm9tRGF0YVRyYW5zZmVyKGRhdGFUcmFuc2ZlciwgdHlwZXNUb1RyeSwgZGVmYXVsdFZhbHVlKSB7XG4gICAgY29uc3QgcmVzdWx0ID0gdHlwZXNUb1RyeS5yZWR1Y2UoKHJlc3VsdFNvRmFyLCB0eXBlVG9UcnkpPT5yZXN1bHRTb0ZhciB8fCBkYXRhVHJhbnNmZXIuZ2V0RGF0YSh0eXBlVG9UcnkpXG4gICAgLCAnJyk7XG4gICAgcmV0dXJuIHJlc3VsdCAhPSBudWxsID8gcmVzdWx0IDogZGVmYXVsdFZhbHVlO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXREYXRhRnJvbURhdGFUcmFuc2Zlci5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/getDataFromDataTransfer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/NativeDragSources/index.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNativeDragSource: () => (/* binding */ createNativeDragSource),\n/* harmony export */   matchNativeItemType: () => (/* binding */ matchNativeItemType)\n/* harmony export */ });\n/* harmony import */ var _NativeDragSource_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NativeDragSource.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/NativeDragSource.js\");\n/* harmony import */ var _nativeTypesConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./nativeTypesConfig.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/nativeTypesConfig.js\");\n\n\nfunction createNativeDragSource(type, dataTransfer) {\n    const config = _nativeTypesConfig_js__WEBPACK_IMPORTED_MODULE_0__.nativeTypesConfig[type];\n    if (!config) {\n        throw new Error(`native type ${type} has no configuration`);\n    }\n    const result = new _NativeDragSource_js__WEBPACK_IMPORTED_MODULE_1__.NativeDragSource(config);\n    result.loadDataTransfer(dataTransfer);\n    return result;\n}\nfunction matchNativeItemType(dataTransfer) {\n    if (!dataTransfer) {\n        return null;\n    }\n    const dataTransferTypes = Array.prototype.slice.call(dataTransfer.types || []);\n    return Object.keys(_nativeTypesConfig_js__WEBPACK_IMPORTED_MODULE_0__.nativeTypesConfig).filter((nativeItemType)=>{\n        const typeConfig = _nativeTypesConfig_js__WEBPACK_IMPORTED_MODULE_0__.nativeTypesConfig[nativeItemType];\n        if (!(typeConfig === null || typeConfig === void 0 ? void 0 : typeConfig.matchesTypes)) {\n            return false;\n        }\n        return typeConfig.matchesTypes.some((t)=>dataTransferTypes.indexOf(t) > -1\n        );\n    })[0] || null;\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/nativeTypesConfig.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/NativeDragSources/nativeTypesConfig.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   nativeTypesConfig: () => (/* binding */ nativeTypesConfig)\n/* harmony export */ });\n/* harmony import */ var _NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../NativeTypes.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeTypes.js\");\n/* harmony import */ var _getDataFromDataTransfer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getDataFromDataTransfer.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/getDataFromDataTransfer.js\");\n\n\nconst nativeTypesConfig = {\n    [_NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__.FILE]: {\n        exposeProperties: {\n            files: (dataTransfer)=>Array.prototype.slice.call(dataTransfer.files)\n            ,\n            items: (dataTransfer)=>dataTransfer.items\n            ,\n            dataTransfer: (dataTransfer)=>dataTransfer\n        },\n        matchesTypes: [\n            'Files'\n        ]\n    },\n    [_NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__.HTML]: {\n        exposeProperties: {\n            html: (dataTransfer, matchesTypes)=>(0,_getDataFromDataTransfer_js__WEBPACK_IMPORTED_MODULE_1__.getDataFromDataTransfer)(dataTransfer, matchesTypes, '')\n            ,\n            dataTransfer: (dataTransfer)=>dataTransfer\n        },\n        matchesTypes: [\n            'Html',\n            'text/html'\n        ]\n    },\n    [_NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__.URL]: {\n        exposeProperties: {\n            urls: (dataTransfer, matchesTypes)=>(0,_getDataFromDataTransfer_js__WEBPACK_IMPORTED_MODULE_1__.getDataFromDataTransfer)(dataTransfer, matchesTypes, '').split('\\n')\n            ,\n            dataTransfer: (dataTransfer)=>dataTransfer\n        },\n        matchesTypes: [\n            'Url',\n            'text/uri-list'\n        ]\n    },\n    [_NativeTypes_js__WEBPACK_IMPORTED_MODULE_0__.TEXT]: {\n        exposeProperties: {\n            text: (dataTransfer, matchesTypes)=>(0,_getDataFromDataTransfer_js__WEBPACK_IMPORTED_MODULE_1__.getDataFromDataTransfer)(dataTransfer, matchesTypes, '')\n            ,\n            dataTransfer: (dataTransfer)=>dataTransfer\n        },\n        matchesTypes: [\n            'Text',\n            'text/plain'\n        ]\n    }\n};\n\n//# sourceMappingURL=nativeTypesConfig.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeDragSources/nativeTypesConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeTypes.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/NativeTypes.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FILE: () => (/* binding */ FILE),\n/* harmony export */   HTML: () => (/* binding */ HTML),\n/* harmony export */   TEXT: () => (/* binding */ TEXT),\n/* harmony export */   URL: () => (/* binding */ URL)\n/* harmony export */ });\nconst FILE = '__NATIVE_FILE__';\nconst URL = '__NATIVE_URL__';\nconst TEXT = '__NATIVE_TEXT__';\nconst HTML = '__NATIVE_HTML__';\n\n//# sourceMappingURL=NativeTypes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9OYXRpdmVUeXBlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7O0FBRVAiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9OYXRpdmVUeXBlcy5qcz9hNGYzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBGSUxFID0gJ19fTkFUSVZFX0ZJTEVfXyc7XG5leHBvcnQgY29uc3QgVVJMID0gJ19fTkFUSVZFX1VSTF9fJztcbmV4cG9ydCBjb25zdCBURVhUID0gJ19fTkFUSVZFX1RFWFRfXyc7XG5leHBvcnQgY29uc3QgSFRNTCA9ICdfX05BVElWRV9IVE1MX18nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1OYXRpdmVUeXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeTypes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/OffsetUtils.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/OffsetUtils.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDragPreviewOffset: () => (/* binding */ getDragPreviewOffset),\n/* harmony export */   getEventClientOffset: () => (/* binding */ getEventClientOffset),\n/* harmony export */   getNodeClientOffset: () => (/* binding */ getNodeClientOffset)\n/* harmony export */ });\n/* harmony import */ var _BrowserDetector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BrowserDetector.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/BrowserDetector.js\");\n/* harmony import */ var _MonotonicInterpolant_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MonotonicInterpolant.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/MonotonicInterpolant.js\");\n\n\nconst ELEMENT_NODE = 1;\nfunction getNodeClientOffset(node) {\n    const el = node.nodeType === ELEMENT_NODE ? node : node.parentElement;\n    if (!el) {\n        return null;\n    }\n    const { top , left  } = el.getBoundingClientRect();\n    return {\n        x: left,\n        y: top\n    };\n}\nfunction getEventClientOffset(e) {\n    return {\n        x: e.clientX,\n        y: e.clientY\n    };\n}\nfunction isImageNode(node) {\n    var ref;\n    return node.nodeName === 'IMG' && ((0,_BrowserDetector_js__WEBPACK_IMPORTED_MODULE_0__.isFirefox)() || !((ref = document.documentElement) === null || ref === void 0 ? void 0 : ref.contains(node)));\n}\nfunction getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight) {\n    let dragPreviewWidth = isImage ? dragPreview.width : sourceWidth;\n    let dragPreviewHeight = isImage ? dragPreview.height : sourceHeight;\n    // Work around @2x coordinate discrepancies in browsers\n    if ((0,_BrowserDetector_js__WEBPACK_IMPORTED_MODULE_0__.isSafari)() && isImage) {\n        dragPreviewHeight /= window.devicePixelRatio;\n        dragPreviewWidth /= window.devicePixelRatio;\n    }\n    return {\n        dragPreviewWidth,\n        dragPreviewHeight\n    };\n}\nfunction getDragPreviewOffset(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint) {\n    // The browsers will use the image intrinsic size under different conditions.\n    // Firefox only cares if it's an image, but WebKit also wants it to be detached.\n    const isImage = isImageNode(dragPreview);\n    const dragPreviewNode = isImage ? sourceNode : dragPreview;\n    const dragPreviewNodeOffsetFromClient = getNodeClientOffset(dragPreviewNode);\n    const offsetFromDragPreview = {\n        x: clientOffset.x - dragPreviewNodeOffsetFromClient.x,\n        y: clientOffset.y - dragPreviewNodeOffsetFromClient.y\n    };\n    const { offsetWidth: sourceWidth , offsetHeight: sourceHeight  } = sourceNode;\n    const { anchorX , anchorY  } = anchorPoint;\n    const { dragPreviewWidth , dragPreviewHeight  } = getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight);\n    const calculateYOffset = ()=>{\n        const interpolantY = new _MonotonicInterpolant_js__WEBPACK_IMPORTED_MODULE_1__.MonotonicInterpolant([\n            0,\n            0.5,\n            1\n        ], [\n            // Dock to the top\n            offsetFromDragPreview.y,\n            // Align at the center\n            (offsetFromDragPreview.y / sourceHeight) * dragPreviewHeight,\n            // Dock to the bottom\n            offsetFromDragPreview.y + dragPreviewHeight - sourceHeight, \n        ]);\n        let y = interpolantY.interpolate(anchorY);\n        // Work around Safari 8 positioning bug\n        if ((0,_BrowserDetector_js__WEBPACK_IMPORTED_MODULE_0__.isSafari)() && isImage) {\n            // We'll have to wait for @3x to see if this is entirely correct\n            y += (window.devicePixelRatio - 1) * dragPreviewHeight;\n        }\n        return y;\n    };\n    const calculateXOffset = ()=>{\n        // Interpolate coordinates depending on anchor point\n        // If you know a simpler way to do this, let me know\n        const interpolantX = new _MonotonicInterpolant_js__WEBPACK_IMPORTED_MODULE_1__.MonotonicInterpolant([\n            0,\n            0.5,\n            1\n        ], [\n            // Dock to the left\n            offsetFromDragPreview.x,\n            // Align at the center\n            (offsetFromDragPreview.x / sourceWidth) * dragPreviewWidth,\n            // Dock to the right\n            offsetFromDragPreview.x + dragPreviewWidth - sourceWidth, \n        ]);\n        return interpolantX.interpolate(anchorX);\n    };\n    // Force offsets if specified in the options.\n    const { offsetX , offsetY  } = offsetPoint;\n    const isManualOffsetX = offsetX === 0 || offsetX;\n    const isManualOffsetY = offsetY === 0 || offsetY;\n    return {\n        x: isManualOffsetX ? offsetX : calculateXOffset(),\n        y: isManualOffsetY ? offsetY : calculateYOffset()\n    };\n}\n\n//# sourceMappingURL=OffsetUtils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/OffsetUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/OptionsReader.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/OptionsReader.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptionsReader: () => (/* binding */ OptionsReader)\n/* harmony export */ });\nclass OptionsReader {\n    get window() {\n        if (this.globalContext) {\n            return this.globalContext;\n        } else if (typeof window !== 'undefined') {\n            return window;\n        }\n        return undefined;\n    }\n    get document() {\n        var ref;\n        if ((ref = this.globalContext) === null || ref === void 0 ? void 0 : ref.document) {\n            return this.globalContext.document;\n        } else if (this.window) {\n            return this.window.document;\n        } else {\n            return undefined;\n        }\n    }\n    get rootElement() {\n        var ref;\n        return ((ref = this.optionsArgs) === null || ref === void 0 ? void 0 : ref.rootElement) || this.window;\n    }\n    constructor(globalContext, options){\n        this.ownerDocument = null;\n        this.globalContext = globalContext;\n        this.optionsArgs = options;\n    }\n}\n\n//# sourceMappingURL=OptionsReader.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9PcHRpb25zUmVhZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGVudGFiZWxsLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC1odG1sNS1iYWNrZW5kL2Rpc3QvT3B0aW9uc1JlYWRlci5qcz8wYWZmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBPcHRpb25zUmVhZGVyIHtcbiAgICBnZXQgd2luZG93KCkge1xuICAgICAgICBpZiAodGhpcy5nbG9iYWxDb250ZXh0KSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5nbG9iYWxDb250ZXh0O1xuICAgICAgICB9IGVsc2UgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICByZXR1cm4gd2luZG93O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxuICAgIGdldCBkb2N1bWVudCgpIHtcbiAgICAgICAgdmFyIHJlZjtcbiAgICAgICAgaWYgKChyZWYgPSB0aGlzLmdsb2JhbENvbnRleHQpID09PSBudWxsIHx8IHJlZiA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVmLmRvY3VtZW50KSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5nbG9iYWxDb250ZXh0LmRvY3VtZW50O1xuICAgICAgICB9IGVsc2UgaWYgKHRoaXMud2luZG93KSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy53aW5kb3cuZG9jdW1lbnQ7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgICAgICB9XG4gICAgfVxuICAgIGdldCByb290RWxlbWVudCgpIHtcbiAgICAgICAgdmFyIHJlZjtcbiAgICAgICAgcmV0dXJuICgocmVmID0gdGhpcy5vcHRpb25zQXJncykgPT09IG51bGwgfHwgcmVmID09PSB2b2lkIDAgPyB2b2lkIDAgOiByZWYucm9vdEVsZW1lbnQpIHx8IHRoaXMud2luZG93O1xuICAgIH1cbiAgICBjb25zdHJ1Y3RvcihnbG9iYWxDb250ZXh0LCBvcHRpb25zKXtcbiAgICAgICAgdGhpcy5vd25lckRvY3VtZW50ID0gbnVsbDtcbiAgICAgICAgdGhpcy5nbG9iYWxDb250ZXh0ID0gZ2xvYmFsQ29udGV4dDtcbiAgICAgICAgdGhpcy5vcHRpb25zQXJncyA9IG9wdGlvbnM7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1PcHRpb25zUmVhZGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/OptionsReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/getEmptyImage.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/getEmptyImage.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEmptyImage: () => (/* binding */ getEmptyImage)\n/* harmony export */ });\nlet emptyImage;\nfunction getEmptyImage() {\n    if (!emptyImage) {\n        emptyImage = new Image();\n        emptyImage.src = 'data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==';\n    }\n    return emptyImage;\n}\n\n//# sourceMappingURL=getEmptyImage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9nZXRFbXB0eUltYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLHlDQUF5QztBQUN6QztBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9nZXRFbXB0eUltYWdlLmpzPzMzNjYiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IGVtcHR5SW1hZ2U7XG5leHBvcnQgZnVuY3Rpb24gZ2V0RW1wdHlJbWFnZSgpIHtcbiAgICBpZiAoIWVtcHR5SW1hZ2UpIHtcbiAgICAgICAgZW1wdHlJbWFnZSA9IG5ldyBJbWFnZSgpO1xuICAgICAgICBlbXB0eUltYWdlLnNyYyA9ICdkYXRhOmltYWdlL2dpZjtiYXNlNjQsUjBsR09EbGhBUUFCQUFBQUFDSDVCQUVLQUFFQUxBQUFBQUFCQUFFQUFBSUNUQUVBT3c9PSc7XG4gICAgfVxuICAgIHJldHVybiBlbXB0eUltYWdlO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRFbXB0eUltYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/getEmptyImage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/index.js":
/*!************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTML5Backend: () => (/* binding */ HTML5Backend),\n/* harmony export */   NativeTypes: () => (/* reexport module object */ _NativeTypes_js__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   getEmptyImage: () => (/* reexport safe */ _getEmptyImage_js__WEBPACK_IMPORTED_MODULE_0__.getEmptyImage)\n/* harmony export */ });\n/* harmony import */ var _HTML5BackendImpl_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HTML5BackendImpl.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/HTML5BackendImpl.js\");\n/* harmony import */ var _NativeTypes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NativeTypes.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/NativeTypes.js\");\n/* harmony import */ var _getEmptyImage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getEmptyImage.js */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/getEmptyImage.js\");\n\n\n\n\nconst HTML5Backend = function createBackend(manager, context, options) {\n    return new _HTML5BackendImpl_js__WEBPACK_IMPORTED_MODULE_2__.HTML5BackendImpl(manager, context, options);\n};\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBeUQ7QUFDUjtBQUNFO0FBQ1o7QUFDaEM7QUFDUCxlQUFlLGtFQUFnQjtBQUMvQjs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9yZWFjdC1kbmQtaHRtbDUtYmFja2VuZC9kaXN0L2luZGV4LmpzPzVjY2YiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSFRNTDVCYWNrZW5kSW1wbCB9IGZyb20gJy4vSFRNTDVCYWNrZW5kSW1wbC5qcyc7XG5pbXBvcnQgKiBhcyBfTmF0aXZlVHlwZXMgZnJvbSAnLi9OYXRpdmVUeXBlcy5qcyc7XG5leHBvcnQgeyBnZXRFbXB0eUltYWdlIH0gZnJvbSAnLi9nZXRFbXB0eUltYWdlLmpzJztcbmV4cG9ydCB7IF9OYXRpdmVUeXBlcyBhcyBOYXRpdmVUeXBlcyB9O1xuZXhwb3J0IGNvbnN0IEhUTUw1QmFja2VuZCA9IGZ1bmN0aW9uIGNyZWF0ZUJhY2tlbmQobWFuYWdlciwgY29udGV4dCwgb3B0aW9ucykge1xuICAgIHJldHVybiBuZXcgSFRNTDVCYWNrZW5kSW1wbChtYW5hZ2VyLCBjb250ZXh0LCBvcHRpb25zKTtcbn07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/utils/js_utils.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/utils/js_utils.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoize: () => (/* binding */ memoize),\n/* harmony export */   union: () => (/* binding */ union),\n/* harmony export */   without: () => (/* binding */ without)\n/* harmony export */ });\n// cheap lodash replacements\nfunction memoize(fn) {\n    let result = null;\n    const memoized = ()=>{\n        if (result == null) {\n            result = fn();\n        }\n        return result;\n    };\n    return memoized;\n}\n/**\n * drop-in replacement for _.without\n */ function without(items, item) {\n    return items.filter((i)=>i !== item\n    );\n}\nfunction union(itemsA, itemsB) {\n    const set = new Set();\n    const insertItem = (item)=>set.add(item)\n    ;\n    itemsA.forEach(insertItem);\n    itemsB.forEach(insertItem);\n    const result = [];\n    set.forEach((key)=>result.push(key)\n    );\n    return result;\n}\n\n//# sourceMappingURL=js_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC91dGlscy9qc191dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGVudGFiZWxsLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC1odG1sNS1iYWNrZW5kL2Rpc3QvdXRpbHMvanNfdXRpbHMuanM/NDA5ZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBjaGVhcCBsb2Rhc2ggcmVwbGFjZW1lbnRzXG5leHBvcnQgZnVuY3Rpb24gbWVtb2l6ZShmbikge1xuICAgIGxldCByZXN1bHQgPSBudWxsO1xuICAgIGNvbnN0IG1lbW9pemVkID0gKCk9PntcbiAgICAgICAgaWYgKHJlc3VsdCA9PSBudWxsKSB7XG4gICAgICAgICAgICByZXN1bHQgPSBmbigpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfTtcbiAgICByZXR1cm4gbWVtb2l6ZWQ7XG59XG4vKipcbiAqIGRyb3AtaW4gcmVwbGFjZW1lbnQgZm9yIF8ud2l0aG91dFxuICovIGV4cG9ydCBmdW5jdGlvbiB3aXRob3V0KGl0ZW1zLCBpdGVtKSB7XG4gICAgcmV0dXJuIGl0ZW1zLmZpbHRlcigoaSk9PmkgIT09IGl0ZW1cbiAgICApO1xufVxuZXhwb3J0IGZ1bmN0aW9uIHVuaW9uKGl0ZW1zQSwgaXRlbXNCKSB7XG4gICAgY29uc3Qgc2V0ID0gbmV3IFNldCgpO1xuICAgIGNvbnN0IGluc2VydEl0ZW0gPSAoaXRlbSk9PnNldC5hZGQoaXRlbSlcbiAgICA7XG4gICAgaXRlbXNBLmZvckVhY2goaW5zZXJ0SXRlbSk7XG4gICAgaXRlbXNCLmZvckVhY2goaW5zZXJ0SXRlbSk7XG4gICAgY29uc3QgcmVzdWx0ID0gW107XG4gICAgc2V0LmZvckVhY2goKGtleSk9PnJlc3VsdC5wdXNoKGtleSlcbiAgICApO1xuICAgIHJldHVybiByZXN1bHQ7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWpzX3V0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/utils/js_utils.js\n");

/***/ })

};
;