"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2296],{63804:function(e,t,n){n.d(t,{Z:function(){return v}});var r=n(2265),o=n(61994),i=n(20801),l=n(16210),u=n(37053),a=n(79114),s=n(90486),c=n(94143),d=n(50738);function f(e){return(0,d.ZP)("MuiBackdrop",e)}(0,c.Z)("MuiBackdrop",["root","invisible"]);var p=n(57437);let m=e=>{let{classes:t,invisible:n}=e;return(0,i.Z)({root:["root",n&&"invisible"]},f,t)},h=(0,l.ZP)("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,n.invisible&&t.invisible]}})({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent",variants:[{props:{invisible:!0},style:{backgroundColor:"transparent"}}]});var v=r.forwardRef(function(e,t){let n=(0,u.i)({props:e,name:"MuiBackdrop"}),{children:r,className:i,component:l="div",invisible:c=!1,open:d,components:f={},componentsProps:v={},slotProps:y={},slots:b={},TransitionComponent:g,transitionDuration:E,...Z}=n,x={...n,component:l,invisible:c},R=m(x),k={slots:{transition:g,root:f.Root,...b},slotProps:{...v,...y}},[w,T]=(0,a.Z)("root",{elementType:h,externalForwardedProps:k,className:(0,o.Z)(R.root,i),ownerState:x}),[I,P]=(0,a.Z)("transition",{elementType:s.Z,externalForwardedProps:k,ownerState:x});return(0,p.jsx)(I,{in:d,timeout:E,...Z,...P,children:(0,p.jsx)(w,{"aria-hidden":!0,...T,classes:R,ref:t,children:r})})})},90486:function(e,t,n){var r=n(2265),o=n(52836),i=n(30628),l=n(31691),u=n(31090),a=n(60118),s=n(57437);let c={entering:{opacity:1},entered:{opacity:1}},d=r.forwardRef(function(e,t){let n=(0,l.Z)(),d={enter:n.transitions.duration.enteringScreen,exit:n.transitions.duration.leavingScreen},{addEndListener:f,appear:p=!0,children:m,easing:h,in:v,onEnter:y,onEntered:b,onEntering:g,onExit:E,onExited:Z,onExiting:x,style:R,timeout:k=d,TransitionComponent:w=o.ZP,...T}=e,I=r.useRef(null),P=(0,a.Z)(I,(0,i.Z)(m),t),C=e=>t=>{if(e){let n=I.current;void 0===t?e(n):e(n,t)}},N=C(g),M=C((e,t)=>{(0,u.n)(e);let r=(0,u.C)({style:R,timeout:k,easing:h},{mode:"enter"});e.style.webkitTransition=n.transitions.create("opacity",r),e.style.transition=n.transitions.create("opacity",r),y&&y(e,t)}),S=C(b),A=C(x),O=C(e=>{let t=(0,u.C)({style:R,timeout:k,easing:h},{mode:"exit"});e.style.webkitTransition=n.transitions.create("opacity",t),e.style.transition=n.transitions.create("opacity",t),E&&E(e)}),L=C(Z);return(0,s.jsx)(w,{appear:p,in:v,nodeRef:I,onEnter:M,onEntered:S,onEntering:N,onExit:O,onExited:L,onExiting:A,addEndListener:e=>{f&&f(I.current,e)},timeout:k,...T,children:(e,t)=>{let{ownerState:n,...o}=t;return r.cloneElement(m,{style:{opacity:0,visibility:"exited"!==e||v?void 0:"hidden",...c[e],...R,...m.props.style},ref:P,...o})}})});t.Z=d},76501:function(e,t,n){n.d(t,{Z:function(){return L}});var r=n(2265),o=n(61994),i=n(20801),l=n(29464),u=n(1866),a=n(16210),s=n(76301),c=n(37053),d=n(63804),f=n(23947),p=n(72786),m=n(8659),h=n(16973),v=n(44393),y=n(42109),b=n(3974);function g(e,t){t?e.setAttribute("aria-hidden","true"):e.removeAttribute("aria-hidden")}function E(e){return parseInt((0,y.Z)(e).getComputedStyle(e).paddingRight,10)||0}function Z(e,t,n,r,o){let i=[t,n,...r];[].forEach.call(e.children,e=>{let t=!i.includes(e),n=!function(e){let t=["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].includes(e.tagName),n="INPUT"===e.tagName&&"hidden"===e.getAttribute("type");return t||n}(e);t&&n&&g(e,o)})}function x(e,t){let n=-1;return e.some((e,r)=>!!t(e)&&(n=r,!0)),n}class R{constructor(){this.modals=[],this.containers=[]}add(e,t){let n=this.modals.indexOf(e);if(-1!==n)return n;n=this.modals.length,this.modals.push(e),e.modalRef&&g(e.modalRef,!1);let r=function(e){let t=[];return[].forEach.call(e.children,e=>{"true"===e.getAttribute("aria-hidden")&&t.push(e)}),t}(t);Z(t,e.mount,e.modalRef,r,!0);let o=x(this.containers,e=>e.container===t);return -1!==o?this.containers[o].modals.push(e):this.containers.push({modals:[e],container:t,restore:null,hiddenSiblings:r}),n}mount(e,t){let n=x(this.containers,t=>t.modals.includes(e)),r=this.containers[n];r.restore||(r.restore=function(e,t){let n=[],r=e.container;if(!t.disableScrollLock){let e;if(function(e){let t=(0,p.Z)(e);return t.body===e?(0,y.Z)(e).innerWidth>t.documentElement.clientWidth:e.scrollHeight>e.clientHeight}(r)){let e=(0,b.Z)((0,y.Z)(r));n.push({value:r.style.paddingRight,property:"padding-right",el:r}),r.style.paddingRight=`${E(r)+e}px`;let t=(0,p.Z)(r).querySelectorAll(".mui-fixed");[].forEach.call(t,t=>{n.push({value:t.style.paddingRight,property:"padding-right",el:t}),t.style.paddingRight=`${E(t)+e}px`})}if(r.parentNode instanceof DocumentFragment)e=(0,p.Z)(r).body;else{let t=r.parentElement,n=(0,y.Z)(r);e=t?.nodeName==="HTML"&&"scroll"===n.getComputedStyle(t).overflowY?t:r}n.push({value:e.style.overflow,property:"overflow",el:e},{value:e.style.overflowX,property:"overflow-x",el:e},{value:e.style.overflowY,property:"overflow-y",el:e}),e.style.overflow="hidden"}return()=>{n.forEach(e=>{let{value:t,el:n,property:r}=e;t?n.style.setProperty(r,t):n.style.removeProperty(r)})}}(r,t))}remove(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],n=this.modals.indexOf(e);if(-1===n)return n;let r=x(this.containers,t=>t.modals.includes(e)),o=this.containers[r];if(o.modals.splice(o.modals.indexOf(e),1),this.modals.splice(n,1),0===o.modals.length)o.restore&&o.restore(),e.modalRef&&g(e.modalRef,t),Z(o.container,e.mount,e.modalRef,o.hiddenSiblings,!1),this.containers.splice(r,1);else{let e=o.modals[o.modals.length-1];e.modalRef&&g(e.modalRef,!1)}return n}isTopModal(e){return this.modals.length>0&&this.modals[this.modals.length-1]===e}}let k=()=>{},w=new R;var T=function(e){let{container:t,disableEscapeKeyDown:n=!1,disableScrollLock:o=!1,closeAfterTransition:i=!1,onTransitionEnter:l,onTransitionExited:u,children:a,onClose:s,open:c,rootRef:d}=e,y=r.useRef({}),b=r.useRef(null),E=r.useRef(null),Z=(0,f.Z)(E,d),[x,R]=r.useState(!c),T=!!a&&a.props.hasOwnProperty("in"),I=!0;("false"===e["aria-hidden"]||!1===e["aria-hidden"])&&(I=!1);let P=()=>(0,p.Z)(b.current),C=()=>(y.current.modalRef=E.current,y.current.mount=b.current,y.current),N=()=>{w.mount(C(),{disableScrollLock:o}),E.current&&(E.current.scrollTop=0)},M=(0,m.Z)(()=>{let e=("function"==typeof t?t():t)||P().body;w.add(C(),e),E.current&&N()}),S=()=>w.isTopModal(C()),A=(0,m.Z)(e=>{b.current=e,e&&(c&&S()?N():E.current&&g(E.current,I))}),O=r.useCallback(()=>{w.remove(C(),I)},[I]);r.useEffect(()=>()=>{O()},[O]),r.useEffect(()=>{c?M():T&&i||O()},[c,O,T,i,M]);let L=e=>t=>{e.onKeyDown?.(t),"Escape"===t.key&&229!==t.which&&S()&&!n&&(t.stopPropagation(),s&&s(t,"escapeKeyDown"))},F=e=>t=>{e.onClick?.(t),t.target===t.currentTarget&&s&&s(t,"backdropClick")};return{getRootProps:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=(0,v.Z)(e);delete n.onTransitionEnter,delete n.onTransitionExited;let r={...n,...t};return{role:"presentation",...r,onKeyDown:L(r),ref:Z}},getBackdropProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"aria-hidden":!0,...e,onClick:F(e),open:c}},getTransitionProps:()=>({onEnter:(0,h.Z)(()=>{R(!1),l&&l()},a?.props.onEnter??k),onExited:(0,h.Z)(()=>{R(!0),u&&u(),i&&O()},a?.props.onExited??k)}),rootRef:Z,portalRef:A,isTopModal:S,exited:x,hasTransition:T}},I=n(94143),P=n(50738);function C(e){return(0,P.ZP)("MuiModal",e)}(0,I.Z)("MuiModal",["root","hidden","backdrop"]);var N=n(79114),M=n(57437);let S=e=>{let{open:t,exited:n,classes:r}=e;return(0,i.Z)({root:["root",!t&&n&&"hidden"],backdrop:["backdrop"]},C,r)},A=(0,a.ZP)("div",{name:"MuiModal",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,!n.open&&n.exited&&t.hidden]}})((0,s.Z)(e=>{let{theme:t}=e;return{position:"fixed",zIndex:(t.vars||t).zIndex.modal,right:0,bottom:0,top:0,left:0,variants:[{props:e=>{let{ownerState:t}=e;return!t.open&&t.exited},style:{visibility:"hidden"}}]}})),O=(0,a.ZP)(d.Z,{name:"MuiModal",slot:"Backdrop",overridesResolver:(e,t)=>t.backdrop})({zIndex:-1});var L=r.forwardRef(function(e,t){let n=(0,c.i)({name:"MuiModal",props:e}),{BackdropComponent:i=O,BackdropProps:a,classes:s,className:d,closeAfterTransition:f=!1,children:p,container:m,component:h,components:v={},componentsProps:y={},disableAutoFocus:b=!1,disableEnforceFocus:g=!1,disableEscapeKeyDown:E=!1,disablePortal:Z=!1,disableRestoreFocus:x=!1,disableScrollLock:R=!1,hideBackdrop:k=!1,keepMounted:w=!1,onBackdropClick:I,onClose:P,onTransitionEnter:C,onTransitionExited:L,open:F,slotProps:j={},slots:D={},theme:B,...K}=n,U={...n,closeAfterTransition:f,disableAutoFocus:b,disableEnforceFocus:g,disableEscapeKeyDown:E,disablePortal:Z,disableRestoreFocus:x,disableScrollLock:R,hideBackdrop:k,keepMounted:w},{getRootProps:$,getBackdropProps:W,getTransitionProps:H,portalRef:V,isTopModal:Y,exited:_,hasTransition:q}=T({...U,rootRef:t}),z={...U,exited:_},G=S(z),X={};if(void 0===p.props.tabIndex&&(X.tabIndex="-1"),q){let{onEnter:e,onExited:t}=H();X.onEnter=e,X.onExited=t}let J={slots:{root:v.Root,backdrop:v.Backdrop,...D},slotProps:{...y,...j}},[Q,ee]=(0,N.Z)("root",{ref:t,elementType:A,externalForwardedProps:{...J,...K,component:h},getSlotProps:$,ownerState:z,className:(0,o.Z)(d,G?.root,!z.open&&z.exited&&G?.hidden)}),[et,en]=(0,N.Z)("backdrop",{ref:a?.ref,elementType:i,externalForwardedProps:J,shouldForwardComponentProp:!0,additionalProps:a,getSlotProps:e=>W({...e,onClick:t=>{I&&I(t),e?.onClick&&e.onClick(t)}}),className:(0,o.Z)(a?.className,G?.backdrop),ownerState:z});return w||F||q&&!_?(0,M.jsx)(u.Z,{ref:V,container:m,disablePortal:Z,children:(0,M.jsxs)(Q,{...ee,children:[!k&&i?(0,M.jsx)(et,{...en}):null,(0,M.jsx)(l.Z,{disableEnforceFocus:g,disableAutoFocus:b,disableRestoreFocus:x,isEnabled:Y,open:F,children:r.cloneElement(p,X)})]})}):null})},1866:function(e,t,n){var r=n(2265),o=n(54887),i=n(23947),l=n(30628),u=n(3450),a=n(29419);let s=r.forwardRef(function(e,t){let{children:n,container:s,disablePortal:c=!1}=e,[d,f]=r.useState(null),p=(0,i.Z)(r.isValidElement(n)?(0,l.Z)(n):null,t);return((0,u.Z)(()=>{!c&&f(("function"==typeof s?s():s)||document.body)},[s,c]),(0,u.Z)(()=>{if(d&&!c)return(0,a.Z)(t,d),()=>{(0,a.Z)(t,null)}},[t,d,c]),c)?r.isValidElement(n)?r.cloneElement(n,{ref:p}):n:d?o.createPortal(n,d):d});t.Z=s},29464:function(e,t,n){var r=n(2265),o=n(23947),i=n(30628),l=n(72786),u=n(57437);function a(e){let t=[],n=[];return Array.from(e.querySelectorAll('input,select,textarea,a[href],button,[tabindex],audio[controls],video[controls],[contenteditable]:not([contenteditable="false"])')).forEach((e,r)=>{let o=function(e){let t=parseInt(e.getAttribute("tabindex")||"",10);return Number.isNaN(t)?"true"===e.contentEditable||("AUDIO"===e.nodeName||"VIDEO"===e.nodeName||"DETAILS"===e.nodeName)&&null===e.getAttribute("tabindex")?0:e.tabIndex:t}(e);-1===o||e.disabled||"INPUT"===e.tagName&&"hidden"===e.type||function(e){if("INPUT"!==e.tagName||"radio"!==e.type||!e.name)return!1;let t=t=>e.ownerDocument.querySelector(`input[type="radio"]${t}`),n=t(`[name="${e.name}"]:checked`);return n||(n=t(`[name="${e.name}"]`)),n!==e}(e)||(0===o?t.push(e):n.push({documentOrder:r,tabIndex:o,node:e}))}),n.sort((e,t)=>e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex).map(e=>e.node).concat(t)}function s(){return!0}t.Z=function(e){let{children:t,disableAutoFocus:n=!1,disableEnforceFocus:c=!1,disableRestoreFocus:d=!1,getTabbable:f=a,isEnabled:p=s,open:m}=e,h=r.useRef(!1),v=r.useRef(null),y=r.useRef(null),b=r.useRef(null),g=r.useRef(null),E=r.useRef(!1),Z=r.useRef(null),x=(0,o.Z)((0,i.Z)(t),Z),R=r.useRef(null);r.useEffect(()=>{m&&Z.current&&(E.current=!n)},[n,m]),r.useEffect(()=>{if(!m||!Z.current)return;let e=(0,l.Z)(Z.current);return!Z.current.contains(e.activeElement)&&(Z.current.hasAttribute("tabIndex")||Z.current.setAttribute("tabIndex","-1"),E.current&&Z.current.focus()),()=>{d||(b.current&&b.current.focus&&(h.current=!0,b.current.focus()),b.current=null)}},[m]),r.useEffect(()=>{if(!m||!Z.current)return;let e=(0,l.Z)(Z.current),t=t=>{R.current=t,!c&&p()&&"Tab"===t.key&&e.activeElement===Z.current&&t.shiftKey&&(h.current=!0,y.current&&y.current.focus())},n=()=>{let t=Z.current;if(null===t)return;if(!e.hasFocus()||!p()||h.current){h.current=!1;return}if(t.contains(e.activeElement)||c&&e.activeElement!==v.current&&e.activeElement!==y.current)return;if(e.activeElement!==g.current)g.current=null;else if(null!==g.current)return;if(!E.current)return;let n=[];if((e.activeElement===v.current||e.activeElement===y.current)&&(n=f(Z.current)),n.length>0){let e=!!(R.current?.shiftKey&&R.current?.key==="Tab"),t=n[0],r=n[n.length-1];"string"!=typeof t&&"string"!=typeof r&&(e?r.focus():t.focus())}else t.focus()};e.addEventListener("focusin",n),e.addEventListener("keydown",t,!0);let r=setInterval(()=>{e.activeElement&&"BODY"===e.activeElement.tagName&&n()},50);return()=>{clearInterval(r),e.removeEventListener("focusin",n),e.removeEventListener("keydown",t,!0)}},[n,c,d,p,m,f]);let k=e=>{null===b.current&&(b.current=e.relatedTarget),E.current=!0};return(0,u.jsxs)(r.Fragment,{children:[(0,u.jsx)("div",{tabIndex:m?0:-1,onFocus:k,ref:v,"data-testid":"sentinelStart"}),r.cloneElement(t,{ref:x,onFocus:e=>{null===b.current&&(b.current=e.relatedTarget),E.current=!0,g.current=e.target;let n=t.props.onFocus;n&&n(e)}}),(0,u.jsx)("div",{tabIndex:m?0:-1,onFocus:k,ref:y,"data-testid":"sentinelEnd"})]})}},16973:function(e,t,n){n.d(t,{Z:function(){return r}});function r(...e){return e.reduce((e,t)=>null==t?e:function(...n){e.apply(this,n),t.apply(this,n)},()=>{})}},30628:function(e,t,n){n.d(t,{Z:function(){return o}});var r=n(2265);function o(e){return parseInt(r.version,10)>=19?e?.props?.ref||null:e?.ref||null}},3974:function(e,t,n){n.d(t,{Z:function(){return r}});function r(e=window){let t=e.document.documentElement.clientWidth;return e.innerWidth-t}},72786:function(e,t,n){n.d(t,{Z:function(){return r}});function r(e){return e&&e.ownerDocument||document}},42109:function(e,t,n){n.d(t,{Z:function(){return o}});var r=n(72786);function o(e){return(0,r.Z)(e).defaultView||window}},29419:function(e,t,n){n.d(t,{Z:function(){return r}});function r(e,t){"function"==typeof e?e(t):e&&(e.current=t)}},53025:function(e,t,n){n.d(t,{Z:function(){return u}});var r,o=n(2265);let i=0,l={...r||(r=n.t(o,2))}.useId;function u(e){if(void 0!==l){let t=l();return e??t}return function(e){let[t,n]=o.useState(e),r=e||t;return o.useEffect(()=>{null==t&&(i+=1,n(`mui-${i}`))},[t]),r}(e)}}}]);