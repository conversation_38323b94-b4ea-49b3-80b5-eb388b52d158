"use strict";
var Uint8ArrayReader = require("./Uint8ArrayReader");
var utils = require("../utils");

function NodeBufferReader(data) {
    Uint8ArrayReader.call(this, data);
}
utils.inherits(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Uint8ArrayReader);

/**
 * @see DataReader.readData
 */
NodeBufferReader.prototype.readData = function(size) {
    this.checkOffset(size);
    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);
    this.index += size;
    return result;
};
module.exports = NodeBufferReader;
