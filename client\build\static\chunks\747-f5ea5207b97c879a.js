"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[747],{78826:function(e,t,r){var n=r(2265),o=r(73207),i=r(30628),l=r(52836),a=r(31691),s=r(31090),u=r(60118),p=r(57437);function c(e){return`scale(${e}, ${e**2})`}let d={entering:{opacity:1,transform:c(1)},entered:{opacity:1,transform:"none"}},f="undefined"!=typeof navigator&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),h=n.forwardRef(function(e,t){let{addEndListener:r,appear:h=!0,children:v,easing:m,in:y,onEnter:g,onEntered:Z,onEntering:x,onExit:b,onExited:P,onExiting:w,style:E,timeout:k="auto",TransitionComponent:M=l.ZP,...C}=e,R=(0,o.Z)(),S=n.useRef(),A=(0,a.Z)(),T=n.useRef(null),N=(0,u.Z)(T,(0,i.Z)(v),t),j=e=>t=>{if(e){let r=T.current;void 0===t?e(r):e(r,t)}},z=j(x),D=j((e,t)=>{let r;(0,s.n)(e);let{duration:n,delay:o,easing:i}=(0,s.C)({style:E,timeout:k,easing:m},{mode:"enter"});"auto"===k?(r=A.transitions.getAutoHeightDuration(e.clientHeight),S.current=r):r=n,e.style.transition=[A.transitions.create("opacity",{duration:r,delay:o}),A.transitions.create("transform",{duration:f?r:.666*r,delay:o,easing:i})].join(","),g&&g(e,t)}),H=j(Z),L=j(w),F=j(e=>{let t;let{duration:r,delay:n,easing:o}=(0,s.C)({style:E,timeout:k,easing:m},{mode:"exit"});"auto"===k?(t=A.transitions.getAutoHeightDuration(e.clientHeight),S.current=t):t=r,e.style.transition=[A.transitions.create("opacity",{duration:t,delay:n}),A.transitions.create("transform",{duration:f?t:.666*t,delay:f?n:n||.333*t,easing:o})].join(","),e.style.opacity=0,e.style.transform=c(.75),b&&b(e)}),K=j(P);return(0,p.jsx)(M,{appear:h,in:y,nodeRef:T,onEnter:D,onEntered:H,onEntering:z,onExit:F,onExited:K,onExiting:L,addEndListener:e=>{"auto"===k&&R.start(S.current||0,e),r&&r(T.current,e)},timeout:"auto"===k?null:k,...C,children:(e,t)=>{let{ownerState:r,...o}=t;return n.cloneElement(v,{style:{opacity:0,transform:c(.75),visibility:"exited"!==e||y?void 0:"hidden",...d[e],...E,...v.props.style},ref:N,...o})}})});h&&(h.muiSupportAuto=!0),t.Z=h},15273:function(e,t,r){r.d(t,{Z:function(){return v}});var n=r(2265),o=r(61994),i=r(20801),l=r(16210),a=r(37053),s=r(15566),u=r(94143),p=r(50738);function c(e){return(0,p.ZP)("MuiList",e)}(0,u.Z)("MuiList",["root","padding","dense","subheader"]);var d=r(57437);let f=e=>{let{classes:t,disablePadding:r,dense:n,subheader:o}=e;return(0,i.Z)({root:["root",!r&&"padding",n&&"dense",o&&"subheader"]},c,t)},h=(0,l.ZP)("ul",{name:"MuiList",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,!r.disablePadding&&t.padding,r.dense&&t.dense,r.subheader&&t.subheader]}})({listStyle:"none",margin:0,padding:0,position:"relative",variants:[{props:e=>{let{ownerState:t}=e;return!t.disablePadding},style:{paddingTop:8,paddingBottom:8}},{props:e=>{let{ownerState:t}=e;return t.subheader},style:{paddingTop:0}}]});var v=n.forwardRef(function(e,t){let r=(0,a.i)({props:e,name:"MuiList"}),{children:i,className:l,component:u="ul",dense:p=!1,disablePadding:c=!1,subheader:v,...m}=r,y=n.useMemo(()=>({dense:p}),[p]),g={...r,component:u,dense:p,disablePadding:c},Z=f(g);return(0,d.jsx)(s.Z.Provider,{value:y,children:(0,d.jsxs)(h,{as:u,className:(0,o.Z)(Z.root,l),ref:t,ownerState:g,...m,children:[v,i]})})})},15566:function(e,t,r){let n=r(2265).createContext({});t.Z=n},35108:function(e,t,r){r.d(t,{Z:function(){return v}});var n=r(2265),o=r(2262),i=r(15273),l=r(3974).Z,a=r(60118),s=r(84217),u=r(77636),p=r(57437);function c(e,t,r){return e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:r?null:e.firstChild}function d(e,t,r){return e===t?r?e.firstChild:e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:r?null:e.lastChild}function f(e,t){if(void 0===t)return!0;let r=e.innerText;return void 0===r&&(r=e.textContent),0!==(r=r.trim().toLowerCase()).length&&(t.repeating?r[0]===t.keys[0]:r.startsWith(t.keys.join("")))}function h(e,t,r,n,o,i){let l=!1,a=o(e,t,!!t&&r);for(;a;){if(a===e.firstChild){if(l)return!1;l=!0}let t=!n&&(a.disabled||"true"===a.getAttribute("aria-disabled"));if(a.hasAttribute("tabindex")&&f(a,i)&&!t)return a.focus(),!0;a=o(e,a,r)}return!1}var v=n.forwardRef(function(e,t){let{actions:r,autoFocus:v=!1,autoFocusItem:m=!1,children:y,className:g,disabledItemsFocusable:Z=!1,disableListWrap:x=!1,onKeyDown:b,variant:P="selectedMenu",...w}=e,E=n.useRef(null),k=n.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});(0,s.Z)(()=>{v&&E.current.focus()},[v]),n.useImperativeHandle(r,()=>({adjustStyleForScrollbar:(e,t)=>{let{direction:r}=t,n=!E.current.style.width;if(e.clientHeight<E.current.clientHeight&&n){let t=`${l((0,u.Z)(e))}px`;E.current.style["rtl"===r?"paddingLeft":"paddingRight"]=t,E.current.style.width=`calc(100% + ${t})`}return E.current}}),[]);let M=(0,a.Z)(E,t),C=-1;n.Children.forEach(y,(e,t)=>{if(!n.isValidElement(e)){C===t&&(C+=1)>=y.length&&(C=-1);return}e.props.disabled||("selectedMenu"===P&&e.props.selected?C=t:-1!==C||(C=t)),C===t&&(e.props.disabled||e.props.muiSkipListHighlight||e.type.muiSkipListHighlight)&&(C+=1)>=y.length&&(C=-1)});let R=n.Children.map(y,(e,t)=>{if(t===C){let t={};return m&&(t.autoFocus=!0),void 0===e.props.tabIndex&&"selectedMenu"===P&&(t.tabIndex=0),n.cloneElement(e,t)}return e});return(0,p.jsx)(i.Z,{role:"menu",ref:M,className:g,onKeyDown:e=>{let t=E.current,r=e.key;if(e.ctrlKey||e.metaKey||e.altKey){b&&b(e);return}let n=(0,o.Z)(t).activeElement;if("ArrowDown"===r)e.preventDefault(),h(t,n,x,Z,c);else if("ArrowUp"===r)e.preventDefault(),h(t,n,x,Z,d);else if("Home"===r)e.preventDefault(),h(t,null,x,Z,c);else if("End"===r)e.preventDefault(),h(t,null,x,Z,d);else if(1===r.length){let o=k.current,i=r.toLowerCase(),l=performance.now();o.keys.length>0&&(l-o.lastTime>500?(o.keys=[],o.repeating=!0,o.previousKeyMatched=!0):o.repeating&&i!==o.keys[0]&&(o.repeating=!1)),o.lastTime=l,o.keys.push(i);let a=n&&!o.repeating&&f(n,o);o.previousKeyMatched&&(a||h(t,n,!1,Z,c,o))?e.preventDefault():o.previousKeyMatched=!1}b&&b(e)},tabIndex:v?0:-1,...w,children:R})})},8710:function(e,t,r){r.d(t,{Z:function(){return O}});var n=r(2265),o=r(61994),i=r(20801),l=r(39963),a=r(15988),s=r(35108),u=r(80022),p=r(16210),c=r(37053),d=r(24801),f=r(2262),h=r(77636),v=r(78826),m=r(76501),y=r(53410),g=r(94143),Z=r(50738);function x(e){return(0,Z.ZP)("MuiPopover",e)}(0,g.Z)("MuiPopover",["root","paper"]);var b=r(79114),P=r(17419),w=r(57437);function E(e,t){let r=0;return"number"==typeof t?r=t:"center"===t?r=e.height/2:"bottom"===t&&(r=e.height),r}function k(e,t){let r=0;return"number"==typeof t?r=t:"center"===t?r=e.width/2:"right"===t&&(r=e.width),r}function M(e){return[e.horizontal,e.vertical].map(e=>"number"==typeof e?`${e}px`:e).join(" ")}function C(e){return"function"==typeof e?e():e}let R=e=>{let{classes:t}=e;return(0,i.Z)({root:["root"],paper:["paper"]},x,t)},S=(0,p.ZP)(m.Z,{name:"MuiPopover",slot:"Root",overridesResolver:(e,t)=>t.root})({}),A=(0,p.ZP)(y.Z,{name:"MuiPopover",slot:"Paper",overridesResolver:(e,t)=>t.paper})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),T=n.forwardRef(function(e,t){let r=(0,c.i)({props:e,name:"MuiPopover"}),{action:i,anchorEl:l,anchorOrigin:a={vertical:"top",horizontal:"left"},anchorPosition:s,anchorReference:p="anchorEl",children:m,className:y,container:g,elevation:Z=8,marginThreshold:x=16,open:T,PaperProps:N={},slots:j={},slotProps:z={},transformOrigin:D={vertical:"top",horizontal:"left"},TransitionComponent:H,transitionDuration:L="auto",TransitionProps:F={},disableScrollLock:K=!1,...O}=r,$=n.useRef(),I={...r,anchorOrigin:a,anchorReference:p,elevation:Z,marginThreshold:x,transformOrigin:D,TransitionComponent:H,transitionDuration:L,TransitionProps:F},W=R(I),_=n.useCallback(()=>{if("anchorPosition"===p)return s;let e=C(l),t=(e&&1===e.nodeType?e:(0,f.Z)($.current).body).getBoundingClientRect();return{top:t.top+E(t,a.vertical),left:t.left+k(t,a.horizontal)}},[l,a.horizontal,a.vertical,s,p]),V=n.useCallback(e=>({vertical:E(e,D.vertical),horizontal:k(e,D.horizontal)}),[D.horizontal,D.vertical]),B=n.useCallback(e=>{let t={width:e.offsetWidth,height:e.offsetHeight},r=V(t);if("none"===p)return{top:null,left:null,transformOrigin:M(r)};let n=_(),o=n.top-r.vertical,i=n.left-r.horizontal,a=o+t.height,s=i+t.width,u=(0,h.Z)(C(l)),c=u.innerHeight-x,d=u.innerWidth-x;if(null!==x&&o<x){let e=o-x;o-=e,r.vertical+=e}else if(null!==x&&a>c){let e=a-c;o-=e,r.vertical+=e}if(null!==x&&i<x){let e=i-x;i-=e,r.horizontal+=e}else if(s>d){let e=s-d;i-=e,r.horizontal+=e}return{top:`${Math.round(o)}px`,left:`${Math.round(i)}px`,transformOrigin:M(r)}},[l,p,_,V,x]),[U,X]=n.useState(T),Y=n.useCallback(()=>{let e=$.current;if(!e)return;let t=B(e);null!==t.top&&e.style.setProperty("top",t.top),null!==t.left&&(e.style.left=t.left),e.style.transformOrigin=t.transformOrigin,X(!0)},[B]);n.useEffect(()=>(K&&window.addEventListener("scroll",Y),()=>window.removeEventListener("scroll",Y)),[l,K,Y]);let q=()=>{Y()},G=()=>{X(!1)};n.useEffect(()=>{T&&Y()}),n.useImperativeHandle(i,()=>T?{updatePosition:()=>{Y()}}:null,[T,Y]),n.useEffect(()=>{if(!T)return;let e=(0,d.Z)(()=>{Y()}),t=(0,h.Z)(C(l));return t.addEventListener("resize",e),()=>{e.clear(),t.removeEventListener("resize",e)}},[l,T,Y]);let J=L,Q={slots:{transition:H,...j},slotProps:{transition:F,paper:N,...z}},[ee,et]=(0,b.Z)("transition",{elementType:v.Z,externalForwardedProps:Q,ownerState:I,getSlotProps:e=>({...e,onEntering:(t,r)=>{e.onEntering?.(t,r),q()},onExited:t=>{e.onExited?.(t),G()}}),additionalProps:{appear:!0,in:T}});"auto"!==L||ee.muiSupportAuto||(J=void 0);let er=g||(l?(0,f.Z)(C(l)).body:void 0),[en,{slots:eo,slotProps:ei,...el}]=(0,b.Z)("root",{ref:t,elementType:S,externalForwardedProps:{...Q,...O},shouldForwardComponentProp:!0,additionalProps:{slots:{backdrop:j.backdrop},slotProps:{backdrop:(0,P.Z)("function"==typeof z.backdrop?z.backdrop(I):z.backdrop,{invisible:!0})},container:er,open:T},ownerState:I,className:(0,o.Z)(W.root,y)}),[ea,es]=(0,b.Z)("paper",{ref:$,className:W.paper,elementType:A,externalForwardedProps:Q,shouldForwardComponentProp:!0,additionalProps:{elevation:Z,style:U?void 0:{opacity:0}},ownerState:I});return(0,w.jsx)(en,{...el,...!(0,u.Z)(en)&&{slots:eo,slotProps:ei,disableScrollLock:K},children:(0,w.jsx)(ee,{...et,timeout:J,children:(0,w.jsx)(ea,{...es,children:m})})})});var N=r(34765);function j(e){return(0,Z.ZP)("MuiMenu",e)}(0,g.Z)("MuiMenu",["root","paper","list"]);let z={vertical:"top",horizontal:"right"},D={vertical:"top",horizontal:"left"},H=e=>{let{classes:t}=e;return(0,i.Z)({root:["root"],paper:["paper"],list:["list"]},j,t)},L=(0,p.ZP)(T,{shouldForwardProp:e=>(0,N.Z)(e)||"classes"===e,name:"MuiMenu",slot:"Root",overridesResolver:(e,t)=>t.root})({}),F=(0,p.ZP)(A,{name:"MuiMenu",slot:"Paper",overridesResolver:(e,t)=>t.paper})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),K=(0,p.ZP)(s.Z,{name:"MuiMenu",slot:"List",overridesResolver:(e,t)=>t.list})({outline:0});var O=n.forwardRef(function(e,t){let r=(0,c.i)({props:e,name:"MuiMenu"}),{autoFocus:i=!0,children:s,className:u,disableAutoFocusItem:p=!1,MenuListProps:d={},onClose:f,open:h,PaperProps:v={},PopoverClasses:m,transitionDuration:y="auto",TransitionProps:{onEntering:g,...Z}={},variant:x="selectedMenu",slots:P={},slotProps:E={},...k}=r,M=(0,l.V)(),C={...r,autoFocus:i,disableAutoFocusItem:p,MenuListProps:d,onEntering:g,PaperProps:v,transitionDuration:y,TransitionProps:Z,variant:x},R=H(C),S=i&&!p&&h,A=n.useRef(null),T=(e,t)=>{A.current&&A.current.adjustStyleForScrollbar(e,{direction:M?"rtl":"ltr"}),g&&g(e,t)},N=e=>{"Tab"===e.key&&(e.preventDefault(),f&&f(e,"tabKeyDown"))},j=-1;n.Children.map(s,(e,t)=>{n.isValidElement(e)&&(e.props.disabled||("selectedMenu"===x&&e.props.selected?j=t:-1!==j||(j=t)))});let O={slots:P,slotProps:{list:d,transition:Z,paper:v,...E}},$=(0,a.Z)({elementType:P.root,externalSlotProps:E.root,ownerState:C,className:[R.root,u]}),[I,W]=(0,b.Z)("paper",{className:R.paper,elementType:F,externalForwardedProps:O,shouldForwardComponentProp:!0,ownerState:C}),[_,V]=(0,b.Z)("list",{className:(0,o.Z)(R.list,d.className),elementType:K,shouldForwardComponentProp:!0,externalForwardedProps:O,getSlotProps:e=>({...e,onKeyDown:t=>{N(t),e.onKeyDown?.(t)}}),ownerState:C}),B="function"==typeof O.slotProps.transition?O.slotProps.transition(C):O.slotProps.transition;return(0,w.jsx)(L,{onClose:f,anchorOrigin:{vertical:"bottom",horizontal:M?"right":"left"},transformOrigin:M?z:D,slots:{root:P.root,paper:I,backdrop:P.backdrop,...P.transition&&{transition:P.transition}},slotProps:{root:$,paper:W,backdrop:"function"==typeof E.backdrop?E.backdrop(C):E.backdrop,transition:{...B,onEntering:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];T(...t),B?.onEntering?.(...t)}}},open:h,ref:t,transitionDuration:y,ownerState:C,...k,classes:m,children:(0,w.jsx)(_,{actions:A,autoFocus:i&&(-1===j||p),autoFocusItem:S,variant:x,...V,children:s})})})},39963:function(e,t,r){r.d(t,{V:function(){return l}});var n=r(2265),o=r(57437);let i=n.createContext(),l=()=>n.useContext(i)??!1;t.Z=function(e){let{value:t,...r}=e;return(0,o.jsx)(i.Provider,{value:t??!0,...r})}},24801:function(e,t,r){var n=r(50888);t.Z=n.Z},80022:function(e,t){t.Z=function(e){return"string"==typeof e}},93513:function(e,t,r){r.d(t,{Z:function(){return o}});var n=r(2265),o=function(e,t){return n.isValidElement(e)&&-1!==t.indexOf(e.type.muiName??e.type?._payload?.value?.muiName)}},17419:function(e,t,r){r.d(t,{Z:function(){return o}});var n=r(61994);function o(e,t){if(!e)return t;if("function"==typeof e||"function"==typeof t)return r=>{let o="function"==typeof t?t(r):t,i="function"==typeof e?e({...r,...o}):e,l=(0,n.Z)(r?.className,o?.className,i?.className);return{...o,...i,...!!l&&{className:l},...o?.style&&i?.style&&{style:{...o.style,...i.style}},...o?.sx&&i?.sx&&{sx:[...Array.isArray(o.sx)?o.sx:[o.sx],...Array.isArray(i.sx)?i.sx:[i.sx]]}}};let r=(0,n.Z)(t?.className,e?.className);return{...t,...e,...!!r&&{className:r},...t?.style&&e?.style&&{style:{...t.style,...e.style}},...t?.sx&&e?.sx&&{sx:[...Array.isArray(t.sx)?t.sx:[t.sx],...Array.isArray(e.sx)?e.sx:[e.sx]]}}}},2262:function(e,t,r){var n=r(72786);t.Z=n.Z},77636:function(e,t,r){var n=r(42109);t.Z=n.Z},67184:function(e,t,r){var n=r(38462);t.Z=n.Z},84217:function(e,t,r){var n=r(3450);t.Z=n.Z},50888:function(e,t,r){r.d(t,{Z:function(){return n}});function n(e,t=166){let r;function n(...o){clearTimeout(r),r=setTimeout(()=>{e.apply(this,o)},t)}return n.clear=()=>{clearTimeout(r)},n}},38462:function(e,t,r){r.d(t,{Z:function(){return o}});var n=r(2265);function o(e){let{controlled:t,default:r,name:o,state:i="value"}=e,{current:l}=n.useRef(void 0!==t),[a,s]=n.useState(r),u=n.useCallback(e=>{l||s(e)},[]);return[l?t:a,u]}},15988:function(e,t,r){var n=r(23947),o=r(26710),i=r(73810),l=r(13366);t.Z=function(e){let{elementType:t,externalSlotProps:r,ownerState:a,skipResolvingSlotProps:s=!1,...u}=e,p=s?{}:(0,l.Z)(r,a),{props:c,internalRef:d}=(0,i.Z)({...u,externalSlotProps:p}),f=(0,n.Z)(d,p?.ref,e.additionalProps?.ref);return(0,o.Z)(t,{...c,ref:f},a)}}}]);