"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/FaqSection.jsx":
/*!*****************************************************!*\
  !*** ./src/features/blog/components/FaqSection.jsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Button,FormGroup,FormLabel,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Button,FormGroup,FormLabel,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Button,FormGroup,FormLabel,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Button,FormGroup,FormLabel,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Button,FormGroup,FormLabel,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Button,FormGroup,FormLabel,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Button,FormGroup,FormLabel,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Button,FormGroup,FormLabel,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Button,FormGroup,FormLabel,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _mui_icons_material_Delete__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/icons-material/Delete */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Delete.js\");\n/* harmony import */ var _mui_icons_material_Add__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/icons-material/Add */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction FaqSection(param) {\n    let { values, setFieldValue, errors, touched, language, debounce, isEdit = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    // Use different field names for edit mode vs add mode\n    const faqTitleField = isEdit ? \"faqTitle\" : `faqTitle${language}`;\n    const faqField = isEdit ? \"faq\" : `faq${language}`;\n    const faqTitle = values[faqTitleField] || \"\";\n    const faqItems = values[faqField] || [];\n    const handleAddFaqItem = ()=>{\n        const newFaqItems = [\n            ...faqItems,\n            {\n                question: \"\",\n                answer: \"\"\n            }\n        ];\n        setFieldValue(faqField, newFaqItems);\n        if (debounce) debounce();\n    };\n    const handleRemoveFaqItem = (index)=>{\n        const newFaqItems = faqItems.filter((_, i)=>i !== index);\n        setFieldValue(faqField, newFaqItems);\n        if (debounce) debounce();\n    };\n    const handleFaqTitleChange = (value)=>{\n        setFieldValue(faqTitleField, value);\n        if (debounce) debounce();\n    };\n    const handleFaqItemChange = (index, field, value)=>{\n        const newFaqItems = [\n            ...faqItems\n        ];\n        newFaqItems[index] = {\n            ...newFaqItems[index],\n            [field]: value\n        };\n        setFieldValue(faqField, newFaqItems);\n        if (debounce) debounce();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        defaultExpanded: false,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                    lineNumber: 67,\n                    columnNumber: 21\n                }, void 0),\n                \"aria-controls\": \"faq-content\",\n                id: \"faq-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    variant: \"h6\",\n                    children: [\n                        t(\"createArticle:faqSection\"),\n                        \" (\",\n                        language === \"EN\" ? \"English\" : \"Fran\\xe7ais\",\n                        \")\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"faq-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:faqTitle\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: faqTitleField,\n                                        type: \"text\",\n                                        value: faqTitle,\n                                        onChange: (e)=>handleFaqTitleChange(e.target.value),\n                                        className: \"input-pentabell\" + (errors[faqTitleField] && touched[faqTitleField] ? \" is-invalid\" : \"\"),\n                                        placeholder: t(\"createArticle:faqTitlePlaceholder\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_10__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: faqTitleField,\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"faq-items-container\",\n                            style: {\n                                marginTop: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        justifyContent: \"space-between\",\n                                        alignItems: \"center\",\n                                        marginBottom: \"15px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            variant: \"subtitle1\",\n                                            children: t(\"createArticle:faqItems\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            variant: \"outlined\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Add__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 28\n                                            }, void 0),\n                                            onClick: handleAddFaqItem,\n                                            size: \"small\",\n                                            children: t(\"createArticle:addFaqItem\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                faqItems.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"textSecondary\",\n                                    style: {\n                                        textAlign: \"center\",\n                                        padding: \"20px\"\n                                    },\n                                    children: t(\"createArticle:noFaqItems\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this),\n                                faqItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"faq-item\",\n                                        style: {\n                                            border: \"1px solid #e0e0e0\",\n                                            borderRadius: \"8px\",\n                                            padding: \"15px\",\n                                            marginBottom: \"15px\",\n                                            backgroundColor: \"#fafafa\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    alignItems: \"center\",\n                                                    marginBottom: \"10px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"subtitle2\",\n                                                        children: t(\"createArticle:faqItemNumber\", {\n                                                            number: index + 1\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        onClick: ()=>handleRemoveFaqItem(index),\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Delete__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                style: {\n                                                    marginBottom: \"15px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"label-form\",\n                                                    children: [\n                                                        t(\"createArticle:question\"),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            variant: \"standard\",\n                                                            name: `${faqField}[${index}].question`,\n                                                            type: \"text\",\n                                                            value: item.question || \"\",\n                                                            onChange: (e)=>handleFaqItemChange(index, \"question\", e.target.value),\n                                                            className: \"input-pentabell\",\n                                                            placeholder: t(\"createArticle:questionPlaceholder\"),\n                                                            multiline: true,\n                                                            rows: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"label-form\",\n                                                    children: [\n                                                        t(\"createArticle:answer\"),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Button_FormGroup_FormLabel_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            variant: \"standard\",\n                                                            name: `${faqField}[${index}].answer`,\n                                                            type: \"text\",\n                                                            value: item.answer || \"\",\n                                                            onChange: (e)=>handleFaqItemChange(index, \"answer\", e.target.value),\n                                                            className: \"textArea-pentabell\",\n                                                            placeholder: t(\"createArticle:answerPlaceholder\"),\n                                                            multiline: true,\n                                                            rows: 4\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\FaqSection.jsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(FaqSection, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation\n    ];\n});\n_c = FaqSection;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FaqSection);\nvar _c;\n$RefreshReg$(_c, \"FaqSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/FaqSection.jsx\n"));

/***/ })

});