import { Container, Grid } from "@mui/material";
import Image from "next/image";

import CustomButton from "../ui/CustomButton";
import africaMap from "@/assets/images/offices/AfricaMap.png";
import { websiteRoutesList } from "../../helpers/routesList";
function CTAContact({t}) {
  return (
    <div id="cta-contact">
      <Container className="custom-max-width">
        <Grid container className="container" spacing={2}>
          <Grid item xs={12} sm={6} className="left-section">
            <p className="heading-h2 text-white">
            {t("africa:contact:title")}
            </p>
            <p className="sub-heading text-yellow">
            {t("africa:contact:label")}
            </p>
            <p className="paragraph text-white">
            {t("africa:contact:description")}
            </p>
            <CustomButton
              text= {t("africa:contact:contactUs")}
              className={"btn btn-filled"}
              link={`/${websiteRoutesList.contact.route}`}
            />
          </Grid>

          <Grid item xs={12} sm={5} className="africa-img">
            <img
              width={275}
              height={324}
              src={africaMap.src}
              alt="africa map"
              loading="lazy"
            />
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default CTAContact;
