"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/features/stats/ResumesChart.jsx":
/*!*********************************************!*\
  !*** ./src/features/stats/ResumesChart.jsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ResumesChart; },\n/* harmony export */   valueFormatter: function() { return /* binding */ valueFormatter; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,FormLabel,Grid,MenuItem,Select,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,FormLabel,Grid,MenuItem,Select,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,FormLabel,Grid,MenuItem,Select,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,FormLabel,Grid,MenuItem,Select,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,FormLabel,Grid,MenuItem,Select,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,FormLabel,Grid,MenuItem,Select,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,FormLabel,Grid,MenuItem,Select,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,FormLabel,Grid,MenuItem,Select,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,FormLabel,Grid,MenuItem,Select,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,FormLabel,Grid,MenuItem,Select,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,FormLabel,Grid,MenuItem,Select,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon2_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon2.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon2.svg\");\n/* harmony import */ var _stats_hooks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./stats.hooks */ \"(app-pages-browser)/./src/features/stats/stats.hooks.jsx\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../components/charts/CustomMultiBarchart */ \"(app-pages-browser)/./src/components/charts/CustomMultiBarchart.jsx\");\n/* harmony import */ var _components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../components/charts/CustomPieChart */ \"(app-pages-browser)/./src/components/charts/CustomPieChart.jsx\");\n/* harmony import */ var _blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../blog/hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* __next_internal_client_entry_do_not_use__ valueFormatter,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction valueFormatter(value) {\n    return `${value}`;\n}\nfunction ResumesChart() {\n    _s();\n    const getCategoriesLangEN = (0,_blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetCategories)(\"en\");\n    const transformedCategories = getCategoriesLangEN?.data?.categories?.map((category)=>({\n            id: category.versionscategory[0]?.id,\n            name: category.versionscategory[0]?.name\n        })) || [];\n    /// filter user Activity ///\n    const [dateFromUser, setDateFromUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToUser, setDateToUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchUser, setSearchUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchActivity = ()=>{\n        setDateToActivity(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromActivity(\"2024-09-01\");\n        setSearchActivity(!searchActivity);\n    };\n    /// filter platform activity ///\n    const [dateFromPlatform, setDateFromPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToPlatform, setDateToPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchPlatform, setSearchPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchPlatform = ()=>{\n        setDateToPlatform(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromPlatform(\"2024-09-01\");\n        setSearchPlatform(!searchPlatform);\n    };\n    /// application filter pie chart ///\n    const [dateFromApplication, setDateFromApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToApplication, setDateToApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchApplication, setSearchApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchApplication = ()=>{\n        setDateToApplication(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromApplication(\"2024-09-01\");\n        setSearchApplication(!searchApplication);\n    };\n    /// article filter pie chart ///\n    const [dateFromArticle, setDateFromArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToArticle, setDateToArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchArticle, setSearchArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchArticles = ()=>{\n        setDateToArticle(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromArticle(\"2024-09-01\");\n        setFilteredCategories([]);\n        setApprove(true);\n        setCategories([]);\n        setSearchArticle(!searchArticle);\n    };\n    /// comment filter pie chart ///\n    const [filteredCategories, setFilteredCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [approve, setApprove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dateFromComment, setDateFromComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToComment, setDateToComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchComment, setSearchComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchComments = ()=>{\n        setDateToComment(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromComment(\"2024-09-01\");\n        setFilteredCategories([]);\n        setApprove(true);\n        setCategories([]);\n        setSearchComment(!searchComment);\n    };\n    /// opportunity filter pie chart ////\n    const [dateFromOpportunity, setDateFromOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToOpportunity, setDateToOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [opportunityType, setOpportunityType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [industry, setIndustry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchOpportunity, setSearchOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchOpportunity = ()=>{\n        setDateToOpportunity(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromOpportunity(\"2024-09-01\");\n        setOpportunityType(\"\");\n        setIndustry(\"\");\n        setSearchOpportunity(!searchOpportunity);\n    };\n    const getDataPieComments = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetCommentsStat)({\n        dateFrom: dateFromComment,\n        dateTo: dateToComment,\n        approve: approve,\n        categories: filteredCategories\n    });\n    const getDataPieArticles = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetArticlesStat)({\n        dateFrom: dateFromArticle,\n        dateTo: dateToArticle,\n        barChart: null\n    });\n    const getDataPieOpportunities = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetOpportunitiesStat)({\n        dateFrom: dateFromOpportunity,\n        dateTo: dateToOpportunity,\n        opportunityType,\n        industry,\n        barChart: null\n    });\n    const getDAtaPieApplications = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetApplicationsStat)({\n        dateFrom: dateFromApplication,\n        dateTo: dateToApplication,\n        barChart: null\n    });\n    const getDataUserActivity = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetUserStat)({\n        dateFrom: dateFromUser,\n        dateTo: dateToUser\n    });\n    const getDataPlatforActivity = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetPlatformStat)({\n        dateFrom: dateFromPlatform,\n        dateTo: dateToPlatform\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataUserActivity.refetch();\n    }, [\n        searchUser\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPlatforActivity.refetch();\n    }, [\n        searchPlatform\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieOpportunities.refetch();\n    }, [\n        searchOpportunity\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDAtaPieApplications.refetch();\n    }, [\n        searchApplication\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieArticles.refetch();\n    }, [\n        searchArticle\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieComments.refetch();\n    }, [\n        searchComment\n    ]);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const isTablet = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(theme.breakpoints.down(\"md\"));\n    const chartSettings1 = {\n        width: isMobile ? 290 : isTablet ? 500 : 580,\n        height: 250,\n        layout: \"vertical\"\n    };\n    if (getDataUserActivity.isLoading || getDataPlatforActivity.isLoading || getDataPieArticles.isLoading || getDataPieOpportunities.isLoading || getDAtaPieApplications.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n            lineNumber: 249,\n            columnNumber: 12\n        }, this);\n    }\n    const pieCharts = [\n        {\n            title: t(\"statsDash:applicationsByStatus\"),\n            dataset: getDAtaPieApplications?.data?.map((app)=>({\n                    label: app.status,\n                    value: app.totalApplications\n                })),\n            colors: [\n                \"#E97611\",\n                \"#018055\",\n                \"#D73232\"\n            ]\n        },\n        {\n            title: t(\"statsDash:articlesByVisibility\"),\n            dataset: getDataPieArticles?.data?.map((article)=>({\n                    label: article.visibility,\n                    value: article.totalArticles\n                })),\n            colors: [\n                \"#234791\",\n                \"#FFCA00\",\n                \"#006A67\"\n            ]\n        },\n        {\n            title: t(\"statsDash:opportunities\"),\n            dataset: getDataPieOpportunities?.data?.map((opp)=>({\n                    label: opp.visibility,\n                    value: opp.totalOpportunities\n                })),\n            colors: [\n                \"#234791\",\n                \"#D5E5FF\",\n                \"#227B94\"\n            ]\n        },\n        {\n            title: t(\"statsDash:commentsByCategory\"),\n            dataset: getDataPieComments?.data?.map((comment)=>({\n                    label: comment.category,\n                    value: comment.total\n                })) || [],\n            colors: [\n                \"#673ab7\",\n                \"#009688\",\n                \"#8bc34a\",\n                \"#ffc107\",\n                \"#ff9800\",\n                \"#ffc107\",\n                \"#3f51b5\",\n                \"#009688\",\n                \"#4caf50\",\n                \"#03a9f4\",\n                \"#ff9800\",\n                \"#8bc34a\",\n                \"#673ab7\"\n            ]\n        }\n    ];\n    const userAactivity = {\n        title: t(\"statsDash:usersActivities\"),\n        dataKey: [\n            \"login\",\n            \"register\",\n            \"resumes\",\n            \"applications\"\n        ],\n        dataset: getDataUserActivity?.data,\n        color: [\n            \"#30B0C7\",\n            \"#234791\",\n            \"#007AFF\",\n            \"#32ADE6\"\n        ]\n    };\n    const platformAactivity = {\n        title: t(\"statsDash:platformActivity\"),\n        dataKey: [\n            \"opportunities\",\n            \"articles\",\n            \"newsletters\",\n            \"contacts\"\n        ],\n        dataset: getDataPlatforActivity?.data,\n        color: [\n            \"#FFCC00\",\n            \"#FFA135\",\n            \"#FFD985\",\n            \"#FF7700\"\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"menu:statistics\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                lineNumber: 318,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"stats\",\n                className: \"div-wrapper\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    container: true,\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"heading-h3\",\n                                            gutterBottom: true,\n                                            children: pieCharts[3].title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            elevation: 0,\n                                            disableGutters: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    \"aria-controls\": \"panel1bh-content\",\n                                                    id: \"panel1bh-header\",\n                                                    className: \"svg-accordion\",\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 33\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"label-pentabell\",\n                                                        children: t(\"statsDash:filters\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    elevation: 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        container: true,\n                                                        className: \"chart-grid\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 12,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"blue-text\",\n                                                                            children: [\n                                                                                \" \",\n                                                                                t(\"statsDash:categories\"),\n                                                                                \" :\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                            lineNumber: 342,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            multiple: true,\n                                                                            id: `category`,\n                                                                            options: transformedCategories ? transformedCategories : [],\n                                                                            getOptionLabel: (option)=>option.name,\n                                                                            value: categories.length > 0 ? transformedCategories.filter((category)=>categories.includes(category.name)) : [],\n                                                                            onChange: (event, selectedOptions)=>{\n                                                                                const categoryNames = selectedOptions.map((category)=>category.name);\n                                                                                setCategories(categoryNames);\n                                                                                setFilteredCategories(categoryNames.join(\",\"));\n                                                                            },\n                                                                            renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    ...params,\n                                                                                    className: \"\",\n                                                                                    variant: \"standard\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 369,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                md: 12,\n                                                                children: [\n                                                                    \" \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"blue-text\",\n                                                                        children: [\n                                                                            t(\"statsDash:approvedComments\"),\n                                                                            \" :\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"select-pentabell blue-text\",\n                                                                        value: approve,\n                                                                        defaultValue: \"\",\n                                                                        onChange: (event)=>setApprove(event.target.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                value: \"\",\n                                                                                selected: true,\n                                                                                disabled: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                                                    children: t(\"statsDash:approvedComments\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 390,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 389,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                children: t(\"statsDash:all\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 392,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: true,\n                                                                                children: t(\"statsDash:approved\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 395,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \" \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: false,\n                                                                                children: t(\"statsDash:notApproved\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 398,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    label: t(\"statsDash:fromDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateFromComment,\n                                                                    onChange: (e)=>setDateFromComment(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    label: t(\"statsDash:toDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateToComment,\n                                                                    onChange: (e)=>setDateToComment(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 3,\n                                                                sm: 1,\n                                                                md: 4,\n                                                                className: \"btns-filter dashboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon2_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                                                    onClick: resetSearchComments\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 11,\n                                                                sm: 11,\n                                                                md: 8,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    text: t(\"statsDash:filter\"),\n                                                                    onClick: ()=>{\n                                                                        setSearchComment(!searchComment);\n                                                                    },\n                                                                    className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    donuts: true,\n                                                    chart: pieCharts[3]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"barchartfilter-wrapper\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                container: true,\n                                                className: \"chart-grid\",\n                                                spacing: 1,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 12,\n                                                        md: 3,\n                                                        alignContent: \"left\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"heading-h3\",\n                                                            gutterBottom: true,\n                                                            children: userAactivity.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 6,\n                                                        md: 2.5,\n                                                        xl: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            label: t(\"statsDash:fromDate\"),\n                                                            type: \"date\",\n                                                            value: dateFromUser,\n                                                            onChange: (e)=>setDateFromUser(e.target.value),\n                                                            fullWidth: true,\n                                                            InputLabelProps: {\n                                                                shrink: true\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 6,\n                                                        md: 2.5,\n                                                        xl: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            label: t(\"statsDash:toDate\"),\n                                                            type: \"date\",\n                                                            value: dateToUser,\n                                                            onChange: (e)=>setDateToUser(e.target.value),\n                                                            fullWidth: true,\n                                                            InputLabelProps: {\n                                                                shrink: true\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        item: true,\n                                                        xs: 2,\n                                                        sm: 1,\n                                                        md: 1.5,\n                                                        xl: 1,\n                                                        className: \"btns-filter dashboard\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            className: \"btn btn-outlined btn-refresh full-width\",\n                                                            onClick: resetSearchActivity\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        item: true,\n                                                        xs: 10,\n                                                        sm: 11,\n                                                        md: 2.5,\n                                                        xl: 2,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            text: t(\"statsDash:filter\"),\n                                                            onClick: ()=>{\n                                                                setSearchUser(!searchUser);\n                                                            },\n                                                            className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                userAactivity.dataset?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"logins-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:logins\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newacccounts-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newAccounts\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"uploadedresumes-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:uploadedResumes\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 533,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"applications-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:applications\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    chart: userAactivity,\n                                                    chartSettings: chartSettings1\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 458,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"heading-h3\",\n                                            gutterBottom: true,\n                                            children: pieCharts[0].title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            elevation: 0,\n                                            disableGutters: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    \"aria-controls\": \"panel1bh-content\",\n                                                    id: \"panel1bh-header\",\n                                                    className: \"svg-accordion\",\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 33\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"label-pentabell\",\n                                                        children: t(\"statsDash:filters\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    elevation: 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        container: true,\n                                                        className: \"chart-grid\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    label: t(\"statsDash:fromDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateFromApplication,\n                                                                    onChange: (e)=>setDateFromApplication(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    label: t(\"statsDash:toDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateToApplication,\n                                                                    onChange: (e)=>setDateToApplication(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 3,\n                                                                sm: 1,\n                                                                md: 4,\n                                                                className: \"btns-filter dashboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                                                    onClick: resetSearchApplication\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 11,\n                                                                sm: 11,\n                                                                md: 8,\n                                                                children: [\n                                                                    \" \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        text: t(\"statsDash:filter\"),\n                                                                        onClick: ()=>{\n                                                                            setSearchApplication(!searchApplication);\n                                                                        },\n                                                                        className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 610,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    donuts: true,\n                                                    chart: pieCharts[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                pieCharts[0].dataset?.some((item)=>item[\"value\"] > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"accepted-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 629,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:accepted\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 630,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"rejected-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:rejected\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 636,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"pending-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:pending\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 642,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 554,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 553,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"heading-h3\",\n                                            gutterBottom: true,\n                                            children: pieCharts[2].title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            elevation: 0,\n                                            disableGutters: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    \"aria-controls\": \"panel1bh-content\",\n                                                    id: \"panel1bh-header\",\n                                                    className: \"svg-accordion\",\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 33\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"label-pentabell\",\n                                                        children: t(\"statsDash:filters\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    elevation: 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        container: true,\n                                                        className: \"chart-grid\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"blue-text\",\n                                                                        children: [\n                                                                            t(\"statsDash:type\"),\n                                                                            \" :\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 673,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"select-pentabell blue-text\",\n                                                                        value: opportunityType || \"\",\n                                                                        defaultValue: \"\",\n                                                                        onChange: (event)=>setOpportunityType(event.target.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                selected: true,\n                                                                                disabled: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                                                    children: t(\"statsDash:opportunityType\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 690,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 684,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                children: t(\"statsDash:all\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 692,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            _utils_constants__WEBPACK_IMPORTED_MODULE_7__.OpportunityType.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"blue-text\",\n                                                                                    value: item,\n                                                                                    children: item\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 696,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 676,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                md: 6,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"blue-text\",\n                                                                        children: [\n                                                                            t(\"statsDash:industry\"),\n                                                                            \" :\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"select-pentabell blue-text\",\n                                                                        value: industry || \"\",\n                                                                        onChange: (event)=>setIndustry(event.target.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                selected: true,\n                                                                                disabled: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                                                    children: t(\"statsDash:industry\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 721,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 715,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                children: t(\"statsDash:all\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 723,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            _utils_constants__WEBPACK_IMPORTED_MODULE_7__.Industry.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"blue-text\",\n                                                                                    value: item,\n                                                                                    children: item\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 727,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 710,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 706,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    label: t(\"statsDash:fromDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateFromOpportunity,\n                                                                    onChange: (e)=>setDateFromOpportunity(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    label: t(\"statsDash:toDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateToOpportunity,\n                                                                    onChange: (e)=>setDateToOpportunity(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 750,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 3,\n                                                                sm: 1,\n                                                                md: 4,\n                                                                className: \"btns-filter dashboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 767,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                                                    onClick: resetSearchOpportunity\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 11,\n                                                                sm: 11,\n                                                                md: 8,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    text: t(\"statsDash:filter\"),\n                                                                    onClick: ()=>{\n                                                                        setSearchOpportunity(!searchOpportunity);\n                                                                    },\n                                                                    className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    donuts: false,\n                                                    chart: pieCharts[2]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                pieCharts[2].dataset?.some((item)=>item[\"value\"] > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"public-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 792,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:public\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 793,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"privateopportunity-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:private\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 797,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"draft-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:draft\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 786,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 653,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 652,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"barchartfilter-wrapper\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                container: true,\n                                                className: \"chart-grid\",\n                                                spacing: 1,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 12,\n                                                        md: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"heading-h3\",\n                                                            gutterBottom: true,\n                                                            children: platformAactivity.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 821,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 6,\n                                                        md: 2.5,\n                                                        xl: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            label: t(\"statsDash:fromDate\"),\n                                                            type: \"date\",\n                                                            value: dateFromPlatform,\n                                                            onChange: (e)=>setDateFromPlatform(e.target.value),\n                                                            fullWidth: true,\n                                                            InputLabelProps: {\n                                                                shrink: true\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 827,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 826,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 6,\n                                                        md: 2.5,\n                                                        xl: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            label: t(\"statsDash:toDate\"),\n                                                            type: \"date\",\n                                                            value: dateToPlatform,\n                                                            onChange: (e)=>setDateToPlatform(e.target.value),\n                                                            fullWidth: true,\n                                                            InputLabelProps: {\n                                                                shrink: true\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 836,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        item: true,\n                                                        xs: 2,\n                                                        sm: 1,\n                                                        md: 1.5,\n                                                        xl: 1,\n                                                        className: \"btns-filter dashboard\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            className: \"btn btn-outlined btn-refresh full-width\",\n                                                            onClick: resetSearchPlatform\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        item: true,\n                                                        xs: 10,\n                                                        sm: 11,\n                                                        md: 2.5,\n                                                        xl: 2,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            text: t(\"statsDash:filter\"),\n                                                            onClick: ()=>{\n                                                                setSearchPlatform(!searchPlatform);\n                                                            },\n                                                            className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 861,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 860,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                platformAactivity.dataset?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newopportunities-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 877,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newOpportunities\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 878,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 876,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"neswarticles-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 883,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newArticles\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 884,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newsletters-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 889,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newslettersSubscriptions\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 890,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 888,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newcontact-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newContacts\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 896,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 894,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    chart: platformAactivity,\n                                                    chartSettings: chartSettings1\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 903,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 873,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 816,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 815,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"heading-h3\",\n                                            gutterBottom: true,\n                                            children: pieCharts[1].title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            elevation: 0,\n                                            disableGutters: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    \"aria-controls\": \"panel1bh-content\",\n                                                    id: \"panel1bh-header\",\n                                                    className: \"svg-accordion\",\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 33\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"label-pentabell\",\n                                                        children: t(\"statsDash:filters\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 924,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    elevation: 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        container: true,\n                                                        className: \"chart-grid\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    label: t(\"statsDash:fromDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateFromArticle,\n                                                                    onChange: (e)=>setDateFromArticle(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 931,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 930,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    label: t(\"statsDash:toDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateToArticle,\n                                                                    onChange: (e)=>setDateToArticle(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 941,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 940,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 3,\n                                                                sm: 1,\n                                                                md: 4,\n                                                                className: \"btns-filter dashboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 958,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                                                    onClick: resetSearchArticles\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 957,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 950,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_FormLabel_Grid_MenuItem_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                item: true,\n                                                                xs: 11,\n                                                                sm: 11,\n                                                                md: 8,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    text: t(\"statsDash:filter\"),\n                                                                    onClick: ()=>{\n                                                                        setSearchArticle(!searchArticle);\n                                                                    },\n                                                                    className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 964,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 963,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 928,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    donuts: false,\n                                                    chart: pieCharts[1]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 979,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                pieCharts[1].dataset?.some((item)=>item[\"value\"] > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"public-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 983,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:public\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 984,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 982,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"privatearticles-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 989,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:private\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 990,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"draft-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 995,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:draft\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 996,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 994,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 981,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 977,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 913,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 912,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 911,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ResumesChart, \"5avvH6cEosRudI6naxYJ3BUU5WU=\", false, function() {\n    return [\n        _blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetCategories,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetCommentsStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetArticlesStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetOpportunitiesStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetApplicationsStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetUserStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetPlatformStat,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    ];\n});\n_c = ResumesChart;\nvar _c;\n$RefreshReg$(_c, \"ResumesChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/stats/ResumesChart.jsx\n"));

/***/ })

});