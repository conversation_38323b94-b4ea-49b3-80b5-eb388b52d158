"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_locales_en_createOpportunity_json";
exports.ids = ["_rsc_src_locales_en_createOpportunity_json"];
exports.modules = {

/***/ "(rsc)/./src/locales/en/createOpportunity.json":
/*!***********************************************!*\
  !*** ./src/locales/en/createOpportunity.json ***!
  \***********************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"englishVersion":"English version","frenchVersion":"french version","addOpportunity":"Create opportunity","industry":"Industry","country":"Country","dateOfExpiration":"Date of expiration","title":"Title","jobDescription":"Job description","summary":"Summary","visibility":"Visibility","gender":"Gender","publishDate":"Publish Date","contratType":"Contract type","placeholderIndustry":"Choose an indutry...","placeholderCountry":"Choose a country...","placeholderGender":"Choose a gender...","placeholderPublishDate":"Choose publish date...","placeholderDateOfExpiration":"Choose Expiration date...","placeholderVisibility":"Choose a visibility","settings":"Settings","job":"Job","actions":"Actions","search":"Search with Job title...","levelOfExperience":"Level of Experience","url":"Url","publishNow":"Publish Now","editopportunity":"Edit Opportunity"}');

/***/ })

};
;