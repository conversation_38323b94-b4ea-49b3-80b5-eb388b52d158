"use client";
import {
  Form<PERSON>roup,
  FormLabel,
  TextField,
  Button,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
} from "@mui/material";
import { ErrorMessage } from "formik";
import { useTranslation } from "react-i18next";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

function FaqSection({
  values,
  setFieldValue,
  errors,
  touched,
  language, // 'EN' or 'FR'
  debounce,
  isEdit = false, // New prop to determine if it's edit mode
}) {
  const { t } = useTranslation();

  // Use different field names for edit mode vs add mode
  const faqTitleField = isEdit ? "faqTitle" : `faqTitle${language}`;
  const faqField = isEdit ? "faq" : `faq${language}`;

  const faqTitle = values[faqTitleField] || "";
  const faqItems = values[faqField] || [];

  const handleAddFaqItem = () => {
    const newFaqItems = [...faqItems, { question: "", answer: "" }];
    setFieldValue(faqField, newFaqItems);
    if (debounce) debounce();
  };

  const handleRemoveFaqItem = (index) => {
    const newFaqItems = faqItems.filter((_, i) => i !== index);
    setFieldValue(faqField, newFaqItems);
    if (debounce) debounce();
  };

  const handleFaqTitleChange = (value) => {
    setFieldValue(faqTitleField, value);
    if (debounce) debounce();
  };

  const handleFaqItemChange = (index, field, value) => {
    const newFaqItems = [...faqItems];
    newFaqItems[index] = {
      ...newFaqItems[index],
      [field]: value,
    };
    setFieldValue(faqField, newFaqItems);
    if (debounce) debounce();
  };

  return (
    <Accordion defaultExpanded={false}>
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls="faq-content"
        id="faq-header"
      >
        <Typography variant="h6">
          {t("createArticle:faqSection")} (
          {language === "EN" ? "English" : "Français"})
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <div className="faq-section">
          {/* FAQ Title */}
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:faqTitle")}
              <TextField
                variant="standard"
                name={faqTitleField}
                type="text"
                value={faqTitle}
                onChange={(e) => handleFaqTitleChange(e.target.value)}
                className={
                  "input-pentabell" +
                  (errors[faqTitleField] && touched[faqTitleField]
                    ? " is-invalid"
                    : "")
                }
                placeholder={t("createArticle:faqTitlePlaceholder")}
              />
              <ErrorMessage
                className="label-error"
                name={faqTitleField}
                component="div"
              />
            </FormLabel>
          </FormGroup>

          {/* FAQ Items */}
          <div className="faq-items-container" style={{ marginTop: "20px" }}>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "15px",
              }}
            >
              <Typography variant="subtitle1">
                {t("createArticle:faqItems")}
              </Typography>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={handleAddFaqItem}
                size="small"
              >
                {t("createArticle:addFaqItem")}
              </Button>
            </div>

            {faqItems.length === 0 && (
              <Typography
                variant="body2"
                color="textSecondary"
                style={{ textAlign: "center", padding: "20px" }}
              >
                {t("createArticle:noFaqItems")}
              </Typography>
            )}

            {faqItems.map((item, index) => (
              <div
                key={index}
                className="faq-item"
                style={{
                  border: "1px solid #e0e0e0",
                  borderRadius: "8px",
                  padding: "15px",
                  marginBottom: "15px",
                  backgroundColor: "#fafafa",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "10px",
                  }}
                >
                  <Typography variant="subtitle2">
                    {t("createArticle:faqItemNumber", { number: index + 1 })}
                  </Typography>
                  <IconButton
                    onClick={() => handleRemoveFaqItem(index)}
                    size="small"
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </div>

                {/* Question Field */}
                <FormGroup style={{ marginBottom: "15px" }}>
                  <FormLabel className="label-form">
                    {t("createArticle:question")}
                    <TextField
                      variant="standard"
                      name={`${faqField}[${index}].question`}
                      type="text"
                      value={item.question || ""}
                      onChange={(e) =>
                        handleFaqItemChange(index, "question", e.target.value)
                      }
                      className="input-pentabell"
                      placeholder={t("createArticle:questionPlaceholder")}
                      multiline
                      rows={2}
                    />
                  </FormLabel>
                </FormGroup>

                {/* Answer Field */}
                <FormGroup>
                  <FormLabel className="label-form">
                    {t("createArticle:answer")}
                    <TextField
                      variant="standard"
                      name={`${faqField}[${index}].answer`}
                      type="text"
                      value={item.answer || ""}
                      onChange={(e) =>
                        handleFaqItemChange(index, "answer", e.target.value)
                      }
                      className="textArea-pentabell"
                      placeholder={t("createArticle:answerPlaceholder")}
                      multiline
                      rows={4}
                    />
                  </FormLabel>
                </FormGroup>
              </div>
            ))}
          </div>
        </div>
      </AccordionDetails>
    </Accordion>
  );
}

export default FaqSection;
