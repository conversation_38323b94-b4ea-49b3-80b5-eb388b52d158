"use client ";
import { DataGrid } from "@mui/x-data-grid";
import { useTranslation } from "react-i18next";
import { InputBase, MenuItem, Select } from "@mui/material";
import { useEffect, useReducer, useRef, useState } from "react";
import CustomButton from "@/components/ui/CustomButton";
import { useGetContacts } from "../contact/hooks/Contact.hooks";
import DialogModal from "@/features/user/component/updateProfile/experience/DialogModal";
import Svgarchivepopup from "@/assets/images/icons/archiveicon-popup.svg";
import StatisticsAdminHome from "@/components/Statistics/StatisticsAdminHome";
import Svgedit from "@/assets/images/icons/edit-icon.svg";
import { TypeContactLabels } from "@/utils/constants";
import { ROUTES } from "@/utils/routes";
import Svgpreview from "@/assets/images/icons/preview-icon.svg";

import { useGetArticlesDashboard } from "../blog/hooks/blog.hook";
import { useGetOpportunities } from "../opportunity/hooks/opportunity.hooks";
import { useGetTotal } from "../stats/stats.hooks";
import {
  useApproveComment,
  useGetComments,
} from "../comments/hooks/comments.hooks";
import { axiosGetJson } from "@/config/axios";
import { toast } from "react-toastify";

function DashboardHome({ selectedLanguage }) {
  const { t, i18n } = useTranslation();
  const [action, setAction] = useState("");
  const [articleToDelete, setArticleToDelete] = useState("");
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [showDeleteArticleConfirmation, setShowDeleteArticleConfirmation] =
    useState(false);

  const [showApproveConfirmation, setShowApproveConfirmation] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [opportuniteToDelete, setOpportuniteToDelete] = useState("");
  const [commentsList, setCommentsList] = useState([]);
  const [selectedCommentId, setSelectedCommentId] = useState(null);
  const [selectedAction, setSelectedAction] = useState(null);
  const useApproveCommentHook = useApproveComment();

  const handleOpenDialog = (action, commentId) => {
    setSelectedAction(action);
    setSelectedCommentId(commentId);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };
  const handleDeleteArticles = async (id) => {
    try {
      await axiosGetJson.delete(`articles/archiver/${id}`);
      getArticles.refetch();
      toast.success("article archived successfully");
    } catch {}
  };
  const handleDeleteArticle = () => {
    setShowDeleteArticleConfirmation(false);
  };
  const handleDeleteArticleConfirmation = (id) => {
    setArticleToDelete(id);
    setShowDeleteArticleConfirmation(true);
  };
  const handleToggleCancelApplications = async () => {
    await handleDeleteArticles(articleToDelete);

    setShowDeleteArticleConfirmation(false);
  };
  const getTotals = useGetTotal();
  const getContacts = useGetContacts({
    pageSize: 5,
    pageNumber: 1,
    paginated: true,
    sortOrder: "desc",
  });
  const getArticles = useGetArticlesDashboard({
    language: selectedLanguage,
    pageSize: 5,
    pageNumber: 1,
    sortOrder: "desc",
  });
  const getOpportunities = useGetOpportunities({
    language: selectedLanguage,
    pageSize: 5,
    pageNumber: 1,
    sortOrder: "desc",
  });
  const getComments = useGetComments({
    pageSize: 5,
    pageNumber: 1,
    sortOrder: "desc",
    approved: false,
  });
  useEffect(() => {
    const fetchAllData = async () => {
      try {
        const [contactsData, articlesData, opportunitiesData, commentsData] =
          await Promise.all([
            getContacts.refetch(),
            getArticles.refetch(),
            getOpportunities.refetch(),
            getComments.refetch(),
          ]);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchAllData();
  }, []);
  const closeDeleteModal = () => {
    setShowApproveConfirmation(false);
  };
  const getCommentsList = async () => {
    if (getComments?.data) {
      setCommentsList(getComments.data.comments || []);
    }
  };
  const handleConfirmAction = () => {
    if (selectedAction === "approve") {
      approveOrDisapproveComment(selectedCommentId, true);
      setOpenDialog(false);
    } else if (selectedAction === "disapprove") {
      approveOrDisapproveComment(selectedCommentId, false);
      setOpenDialog(false);
    }
    setOpenDialog(false);
  };

  const approveOrDisapproveComment = (id, approve) => {
    useApproveCommentHook.mutate(
      { id, approve },
      {
        onSuccess: () => {
          getComments?.refetch() && getCommentsList();
        },
      }
    );
  };
  useEffect(() => {
    getComments?.refetch() && getCommentsList();
  }, [getComments?.data]);

  if (
    getArticles.isLoading ||
    getOpportunities.isLoading ||
    getComments.isLoading ||
    getContacts.isLoading
  ) {
    return <p>Loading...</p>;
  }
  const truncateTitle = (title) => {
    const words = title.split(" ");
    if (words?.length > 3) {
      return words.slice(0, 2).join(" ") + "...";
    } else {
      return title;
    }
  };
  const handleChange = (item, event) => {
    setAction(event.target.value);

    switch (event.target.value) {
      case "edit":
        handleEdit(item.id);
        break;
      case "preview":
        router.push(`/blog/${item?.url}`);
        break;
      case "delete":
        handleDeleteArticleConfirmation(item.id);
        break;
      case "comment":
        router.push(`/comments/${item.id}`);
        break;
      default:
        break;
    }
  };

  const handleActionChange = (e, id) => {
    const action = e.target.value;
    if (action === "edit") {
      handleEdit(id);
    }
  };
  const columnsComments = [
    {
      field: "user",
      headerName: "User",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      flex: 1,

      renderCell: (params) => {
        return (
          <a
            href={
              params.row.user?.firstName
                ? `/backoffice/users/detail/${params.row.user?._id}`
                : selectedLanguage === "en"
                ? "/backoffice/home"
                : `/${selectedLanguage}/backoffice/home`
            }
          >
            {params.row.user?.firstName
              ? `${params.row.user?.firstName} ${params.row.user?.lastName}`
              : params.row?.email}
          </a>
        );
      },
    },
    {
      field: "article",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      headerName: "article",
      flex: 1,
      renderCell: (params) => {
        return (
          <a
            href={`/${params.row.article?.versions[0]?.language}/blog/${params.row.article?.versions[0]?.url}`}
          >
            {params.row?.article?.versions[0]?.title}
          </a>
        );
      },
    },
    {
      field: "comment",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      headerName: "comment",
      flex: 1,
    },

    {
      field: "actions",
      headerName: "Actions",
      flex: 1,
      renderCell: (params) => (
        <>
          <CustomButton
            text={params.row.approved === true ? "Disapprove" : "Approve"}
            className={"btn btn-link"}
            onClick={() =>
              handleOpenDialog(
                params.row.approved ? "disapprove" : "approve",
                params.row._id
              )
            }
          />
        </>
      ),
    },
  ];

  const rowsArticles =
    getArticles?.data?.articles?.map((item, index) => ({
      id: item._id,
      title: item?.versions[0]?.title
        ? truncateTitle(item?.versions[0]?.title)
        : "No title",
      actions: item._id,

      visibility: item?.versions?.[0]?.visibility || "N/A",
      url: item?.versions?.[0]?.url || "N/A",
      totalCommentaires: item?.totalCommentaires || "0",
    })) || [];

  const columnsArticles = [
    {
      field: "title",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      headerName: t("listArticle:title"),
      flex: 1,
      renderCell: (params) => (
        <a href={`/en/blog/${params.row?.url}`} className="link">
          {params.row.title}
        </a>
      ),
    },
    {
      field: "totalCommentaires",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      headerName: t("listArticle:nbOfComments"),
      flex: 1,
    },
    {
      field: "actions",
      headerName: "",
      renderCell: (params) => (
        <Select
          onChange={(e) => handleChange(params.row, e)}
          displayEmpty
          input={<InputBase />}
          style={{ width: "100%" }}
          renderValue={() => t("listArticle:Actions")}
        >
          <CustomButton
            text={t("global:edit")}
            icon={<Svgedit />}
            leftIcon={true}
            link={`/backoffice/blogs${ROUTES.editArticle}/${params.row.id}`}
            className="btn btn-ghost edit-blog"
          />
          <CustomButton
            text={t("global:comments")}
            icon={<Svgedit />}
            leftIcon={true}
            link={`/backoffice/blogs/comments/${params.row.id}`}
            className="btn btn-ghost edit-blog"
          />
          <CustomButton
            text={t("global:preview")}
            icon={<Svgpreview />}
            leftIcon={true}
            link={`/blog/${params.row?.url}`}
            className="btn btn-ghost edit-blog"
          />
        </Select>
      ),
      flex: 1,
    },
  ];
  const rowsOpportunities =
    getOpportunities?.data?.opportunities?.map((item, index) => ({
      id: index,
      opportunityId: item?._id,
      title: item?.versions[i18n.language]?.title,
      industry: item?.industry,
      actions: item?._id,
      url: item?.versions[i18n.language].url,
      currentLaguage: item.versions[i18n.language]?.language,
    })) || [];

  const columnsOpportunities = [
    {
      field: "title",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      headerName: t("listopportunity:job"),

      flex: 1,
      renderCell: (params) => (
        <a href={`/en/opportunities/${params.row?.url}`} className="link">
          {params.row.title}
        </a>
      ),
    },
    {
      field: "industry",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      headerName: t("listopportunity:industry"),
      flex: 1,
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      renderCell: (params) => {
        const id = params.row.actions;
        return (
          <Select
            value=""
            onChange={(e) => handleActionChange(e, id)}
            displayEmpty
            input={<InputBase />}
            style={{ width: "100%" }}
            renderValue={() => t("listArticle:Actions")}
          >
            <CustomButton
              text={t("global:preview")}
              icon={<Svgpreview />}
              leftIcon={true}
              link={`/opportunities/${params.row.url}/`}
              className="btn btn-ghost edit-blog"
            />
            <CustomButton
              text={t("global:edit")}
              icon={<Svgedit />}
              leftIcon={true}
              link={`/backoffice/opportunities/edit/${id}/`}
              className="btn btn-ghost edit-blog"
            />
            <CustomButton
              text={t("global:applications")}
              icon={<Svgpreview />}
              leftIcon={true}
              link={`/backoffice/applications/opportunity/${id}`}
              className="btn btn-ghost edit-blog"
            />
          </Select>
        );
      },
    },
  ];

  const rowsContacts =
    getContacts?.data?.contacts?.map((item, index) => ({
      id: item._id,
      fullName: item.fullName
        ? item.fullName
        : item.firstName + " " + item.lastName || "N/A",
      email: item?.email || "N/A",
      type: TypeContactLabels[item.type] || "N/A",
      actions: item?._id,
      userId: item?.userId,
    })) || [];

  const columnsContacts = [
    {
      field: "fullName",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: t("contact:fullName"),
      renderCell: (params) =>
        params.row.userId === null ? (
          <span>{params.value}</span>
        ) : (
          <CustomButton
            text={params.value}
            link={`/backoffice/users/detail/${params.row.userId}`}
            className="btn btn-ghost edit-blog"
          />
        ),
      flex: 1,
    },

    {
      field: "type",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: t("contact:type"),
      flex: 1,
    },
    {
      field: "actions",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: t("contact:actions"),
      renderCell: (params) => (
        <div id="datagrid">
          <div className="action-buttons">
            <CustomButton
              text={t("contact:detail")}
              link={`/backoffice/contacts${ROUTES.detail}/${params.row.id}`}
              className="btn btn-ghost edit-blog"
            />
          </div>
        </div>
      ),
      flex: 1,
    },
  ];

  return (
    <>
      <p className="heading-h2 semi-bold"> Home Page </p>

      <div id="container">
        <StatisticsAdminHome
          applications={getTotals?.data?.totalApplications}
          opportunities={getTotals?.data?.totalOpportunity}
          comments={getTotals?.data?.totalComments}
          contacts={getTotals?.data?.totalContacts}
          candidates={getTotals?.data?.totalCandidates}
          articles={getTotals?.data?.totalArticles}
          logins={getTotals?.data?.totalLogins}
          cvUploaded={getTotals?.data?.totalFilesUploaded}
        />
        <div id="dashboard-grid">
          <div className="dashboard-grid">
            <div className="table-container disable-pagination">
              <div className="flex">
                <p className="heading-h2 semi-bold">
                  {t("contact:listOfArticles")}
                </p>
                <CustomButton
                  text={t("global:viewMore")}
                  className="btn btn-ghost"
                  link={`/backoffice/blogs`}
                />
              </div>

              <div style={{ height: "100%", width: "100%" }}>
                <DataGrid
                  localeText={{ noRowsLabel: "No result found." }}
                  rows={rowsArticles}
                  columns={columnsArticles}
                  rowHeight={40}
                  autoHeight
                />
              </div>
            </div>{" "}
            <div className="table-container disable-pagination">
              <div className="flex">
                <p className="heading-h2 semi-bold">
                  {t("contact:listOfOpportunities")}
                </p>
                <CustomButton
                  text={t("global:viewMore")}
                  className="btn btn-ghost"
                  link={`/backoffice/opportunities`}
                />
              </div>

              <div style={{ height: "100%", width: "100%" }}>
                <DataGrid
                  localeText={{ noRowsLabel: "No result found." }}
                  pagination={false}
                  rows={rowsOpportunities}
                  columns={columnsOpportunities}
                  rowHeight={40}
                  autoHeight
                />
              </div>
            </div>{" "}
            <div className="table-container disable-pagination">
              <div className="flex">
                {" "}
                <p
                  className="heading-h2 semi-bold"
                  style={{ marginTop: "5px !important" }}
                >
                  {t("contact:listOfContact")}
                </p>
                <CustomButton
                  text={t("global:viewMore")}
                  className="btn btn-ghost"
                  link={`/backoffice/contacts`}
                />
              </div>

              <div style={{ height: "100%", width: "100%" }}>
                <DataGrid
                  localeText={{ noRowsLabel: "No result found." }}
                  autoHeight
                  rows={rowsContacts}
                  columns={columnsContacts}
                  rowHeight={40}
                  disableSelectionOnClick
                />
              </div>
            </div>{" "}
            <div className="table-container disable-pagination">
              <div className="flex">
                {" "}
                <p className="heading-h2 semi-bold">
                  {t("contact:listOfComments")}
                </p>
                <CustomButton
                  text={t("global:viewMore")}
                  className="btn btn-ghost"
                  link={`/backoffice/comments`}
                />
              </div>

              <div style={{ height: "100%", width: "100%" }}>
                <DataGrid
                  localeText={{ noRowsLabel: "No result found." }}
                  autoHeight
                  getRowId={(row) => row._id}
                  rows={commentsList}
                  columns={columnsComments}
                  rowHeight={40}
                />
              </div>
            </div>{" "}
          </div>{" "}
          {showDeleteArticleConfirmation && (
            <DialogModal
              icon={<Svgarchivepopup />}
              message={t("messages:deleteArticle")}
              onClose={handleDeleteArticle}
              onConfirm={handleToggleCancelApplications}
            />
          )}
          {showDeleteConfirmation && (
            <DialogModal
              message={t("messages:deleteOpportunity")}
              icon={<Svgarchivepopup />}
              onClose={handleDeleteOpportunite}
              onConfirm={handleToggleCancelOpportunity}
            />
          )}
          <DialogModal
            open={openDialog}
            message={
              selectedAction === "delete"
                ? t("messages:deleteComment")
                : selectedAction === "approve"
                ? t("messages:approveComment")
                : t("messages:disapproveComment")
            }
            icon={<Svgarchivepopup />}
            onClose={handleCloseDialog}
            onConfirm={handleConfirmAction}
          />
        </div>
      </div>
    </>
  );
}
export default DashboardHome;
