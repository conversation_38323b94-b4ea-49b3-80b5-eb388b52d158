"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Description!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Add custom styles for drag state\nconst dragActiveStyles = `\n  .file-labels.drag-active {\n    border-color: #1976d2 !important;\n    background-color: rgba(25, 118, 210, 0.04) !important;\n  }\n  .file-labels.disabled {\n    cursor: not-allowed !important;\n    opacity: 0.6 !important;\n  }\n`;\n// Inject styles if not already present\nif (typeof document !== \"undefined\" && !document.getElementById(\"document-importer-styles\")) {\n    const styleSheet = document.createElement(\"style\");\n    styleSheet.id = \"document-importer-styles\";\n    styleSheet.textContent = dragActiveStyles;\n    document.head.appendChild(styleSheet);\n}\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [extractedData, setExtractedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Supported file types\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords: keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement(function(image) {\n                        return image.read(\"base64\").then(function(imageBuffer) {\n                            return {\n                                src: \"data:\" + image.contentType + \";base64,\" + imageBuffer\n                            };\n                        });\n                    })\n                }\n            });\n            setProgress(75);\n            // Clean up the HTML content\n            let cleanContent = result.value.replace(/<p><\\/p>/g, \"\") // Remove empty paragraphs\n            .replace(/\\s+/g, \" \") // Normalize whitespace\n            .trim();\n            // Extract metadata\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata: metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            // Convert plain text to basic HTML\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata: metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            setExtractedData(result);\n            setSuccess(true);\n            handleApplyContent();\n        } catch (err) {\n            console.error(\"File processing error:\", err);\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) {\n            processFile(acceptedFiles[0]);\n        }\n    }, []);\n    const { getRootProps, getInputProps, isDragActive, acceptedFiles } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing\n    });\n    const handleApplyContent = ()=>{\n        if (extractedData) {\n            onContentExtracted(extractedData.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(extractedData.metadata);\n            }\n            setExtractedData(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        sx: {\n            mb: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                variant: \"h6\",\n                gutterBottom: true,\n                className: \"label-form\",\n                children: [\n                    t(\"createArticle:importDocument\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"upload-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    ...getRootProps(),\n                    className: `file-labels ${isDragActive ? \"drag-active\" : \"\"} ${disabled || isProcessing ? \"disabled\" : \"\"}`,\n                    style: {\n                        opacity: disabled || isProcessing ? 0.6 : 1,\n                        cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ...getInputProps(),\n                            className: \"file-input\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"upload-area\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"icon-pic\",\n                                        style: {\n                                            backgroundImage: `url(\"${_assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"]}\")`,\n                                            backgroundSize: \"cover\",\n                                            backgroundRepeat: \"no-repeat\",\n                                            backgroundPosition: \"center\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-text\",\n                                            children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:importFromDocument\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-description\",\n                                            children: [\n                                                t(\"createArticle:supportedFormats\"),\n                                                \": .docx, .doc, .txt\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            sx: {\n                                                display: \"flex\",\n                                                justifyContent: \"flex-start\",\n                                                gap: 1,\n                                                flexWrap: \"wrap\",\n                                                mt: 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Word (.docx)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Word (.doc)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Text (.txt)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"body2\",\n                        gutterBottom: true,\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 274,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, undefined),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 289,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"pRjwRruKHWvwgpXaB93/Sv/fHgE=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});