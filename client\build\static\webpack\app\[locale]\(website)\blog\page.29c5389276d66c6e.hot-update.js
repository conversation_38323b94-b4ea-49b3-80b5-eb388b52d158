"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/blog/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/ArticlesList.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/ArticlesList.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ArticlesList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,InputAdornment,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,InputAdornment,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,InputAdornment,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,InputAdornment,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/InputAdornment/InputAdornment.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! embla-carousel-react */ \"(app-pages-browser)/./node_modules/embla-carousel-react/esm/embla-carousel-react.esm.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _components_CustomPagination__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/CustomPagination */ \"(app-pages-browser)/./src/components/CustomPagination.jsx\");\n/* harmony import */ var _assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../assets/images/icons/searchIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/searchIcon.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../assets/images/icons/refreshIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon.svg\");\n/* harmony import */ var _BlogItem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./BlogItem */ \"(app-pages-browser)/./src/features/blog/components/BlogItem.jsx\");\n/* harmony import */ var _guides_components_GuideItem__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../guides/components/GuideItem */ \"(app-pages-browser)/./src/features/guides/components/GuideItem.jsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var embla_carousel_autoplay__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! embla-carousel-autoplay */ \"(app-pages-browser)/./node_modules/embla-carousel-autoplay/esm/embla-carousel-autoplay.esm.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* harmony import */ var html_to_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! html-to-text */ \"(app-pages-browser)/./node_modules/html-to-text/lib/html-to-text.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import SvgCategoriesIcon from \"../../../assets/images/icons/categoriesIcon.svg\";\n\n\n\n\nfunction ArticlesList(param) {\n    let { data, language, searchParams, isCategory } = param;\n    _s();\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(parseInt(searchParams?.pageNumber || 1));\n    const [keyword, setkeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(searchParams?.keyword);\n    const searchQueryParams = new URLSearchParams();\n    const searchParamsVar = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.useSearchParams)();\n    const searchParamsContent = searchParamsVar.toString();\n    const [slides, setSlides] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const gradients = [\n        \"linear-gradient(to bottom, rgba(214, 155, 25, 0), rgba(214, 155, 25, 0.6))\",\n        \"linear-gradient(to bottom, rgba(116, 55, 148, 0), rgba(116, 55, 148, 0.6))\",\n        \"linear-gradient(to bottom, rgba(0, 153, 102, 0), rgba(0, 153, 102, 0.6))\",\n        \"linear-gradient(to bottom, rgba(204, 50, 51, 0), rgba(204, 50, 51, 0.6))\"\n    ];\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.usePathname)();\n    const truncateDescription = (title)=>{\n        title = (0,html_to_text__WEBPACK_IMPORTED_MODULE_13__.htmlToText)(title, {\n            wordwrap: false\n        });\n        const words = title.split(\" \");\n        if (words?.length >= 20) {\n            return words.slice(0, 20).join(\" \");\n        } else {\n            return title;\n        }\n    };\n    const updateUrlWithParams = ()=>{\n        if (keyword) searchQueryParams.set(\"keyword\", keyword);\n        if (pageNumber) searchQueryParams.set(\"pageNumber\", 1);\n        router.push(`${pathname}?${searchQueryParams.toString()}`);\n    };\n    const resetSearch = ()=>{\n        setkeyword(\"\");\n        setPageNumber(1);\n        searchQueryParams.set(\"pageNumber\", 1);\n        router.push(`${pathname}?${searchQueryParams.toString()}`);\n    };\n    const handlePageChange = (page)=>{\n        setPageNumber(page);\n    };\n    let lastColor = \"\";\n    const getRandomGradient = ()=>{\n        // return gradients[Math.floor(Math.random() * gradients.length)];\n        let randomColor;\n        do {\n            randomColor = gradients[Math.floor(Math.random() * gradients.length)];\n        }while (randomColor === lastColor);\n        lastColor = randomColor;\n        return randomColor;\n    };\n    const fetchCategories = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_5__.axiosGetJsonSSR.get(`${language}${_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.category}`);\n            const categoriesData = response.data.categoriesData.map((category)=>{\n                const firstVersion = category?.versionscategory[0];\n                return {\n                    img: firstVersion?.image ? `${\"http://localhost:4000/api/v1\"}/files/${firstVersion?.image}` : null,\n                    link: `${firstVersion?.url}`,\n                    name: firstVersion?.name || \"N/A\"\n                };\n            });\n            setSlides(categoriesData);\n        } catch (error) {\n            setError(t(\"messages:fetchCategoriesFailed\"));\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCategories();\n    }, []);\n    const OPTIONS = {\n        loop: true,\n        align: \"start\"\n    };\n    const [emblaRef] = (0,embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(OPTIONS, [\n        (0,embla_carousel_autoplay__WEBPACK_IMPORTED_MODULE_16__[\"default\"])({\n            playOnInit: true,\n            delay: 1000,\n            stopOnMouseEnter: true\n        })\n    ]);\n    const OPTIONS_Right = {\n        loop: true,\n        align: \"start\",\n        direction: \"rtl\"\n    };\n    const [emblaRefRight] = (0,embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(OPTIONS_Right, [\n        (0,embla_carousel_autoplay__WEBPACK_IMPORTED_MODULE_16__[\"default\"])({\n            playOnInit: true,\n            delay: 1000,\n            stopOnMouseEnter: true\n        })\n    ]);\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\") {\n            updateUrlWithParams();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"blogs-page\",\n        children: [\n            !isCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"search-bar-blogs\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"custom-max-width\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"container\",\n                        container: true,\n                        spacing: 0,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 8,\n                                container: true,\n                                spacing: 0,\n                                className: \"filter-inputs\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"input-pentabell\",\n                                        autoComplete: \"off\",\n                                        slotProps: {\n                                            input: {\n                                                startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    position: \"start\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            }\n                                        },\n                                        variant: \"standard\",\n                                        type: \"text\",\n                                        value: keyword,\n                                        onChange: (e)=>setkeyword(e.target.value),\n                                        onKeyDown: handleKeyDown,\n                                        placeholder: \"Keywords\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 4,\n                                className: \"btns-filter\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 25\n                                        }, void 0),\n                                        className: \"btn btn-outlined\",\n                                        onClick: resetSearch\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        text: \"Search\",\n                                        onClick: updateUrlWithParams,\n                                        className: \"btn btn-filled full-width\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 144,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                className: \"custom-max-width\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"heading-h1\",\n                        children: \"Browse by topic\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"embla\",\n                        id: \"blog__categories__slider\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"embla__viewport\",\n                            ref: emblaRef,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"embla__container categories-list\",\n                                children: slides.map((category, index)=>category?.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"category-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.category.route}/${category.link}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.category.route}/${category.link}`}/`,\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, category.link, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"embla\",\n                        style: {\n                            marginBottom: \"20px\"\n                        },\n                        id: \"blog__categories__slider\",\n                        dir: \"rtl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"embla__viewport\",\n                            ref: emblaRefRight,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"embla__container categories-list\",\n                                children: slides.map((category, index)=>category?.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"category-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.category.route}/${category.link}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.category.route}/${category.link}`}/`,\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, category.link, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                className: \"custom-max-width\",\n                children: [\n                    data?.firstArticle && (()=>{\n                        const { getBlogVersion } = __webpack_require__(/*! @/utils/blogHelpers */ \"(app-pages-browser)/./src/utils/blogHelpers.js\");\n                        const featuredVersionData = getBlogVersion(data.firstArticle, language);\n                        if (!featuredVersionData) {\n                            return null;\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"first-blog\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"last-blog\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                locale: language === \"en\" ? \"en\" : \"fr\",\n                                                href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${featuredVersionData?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${featuredVersionData?.url}`}/`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"title featured-title\",\n                                                    children: featuredVersionData?.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"description\",\n                                                children: featuredVersionData?.description ? featuredVersionData?.description : truncateDescription(featuredVersionData?.content)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                text: t(\"global:readMore\"),\n                                                className: \"btn btn-outlined\",\n                                                link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${featuredVersionData?.url}`,\n                                                aHref: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        locale: language === \"en\" ? \"en\" : \"fr\",\n                                        href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${featuredVersionData?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${featuredVersionData?.url}`}/`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"blog-img-section\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"img-section\",\n                                                style: {\n                                                    backgroundImage: `url(${\"http://localhost:4000/api/v1\"}/files/${featuredVersionData?.image})`\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 259,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false);\n                    })(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        container: true,\n                        rowSpacing: 0,\n                        columnSpacing: 2,\n                        children: data?.items?.map((item, index)=>{\n                            if (item.type === \"guide\") {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_guides_components_GuideItem__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    guideData: item,\n                                    language: language\n                                }, `guide-${index}`, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 17\n                                }, this);\n                            }\n                            if (item.type === \"article\") {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BlogItem__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    blogData: item,\n                                    language: language\n                                }, `article-${index}`, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 17\n                                }, this);\n                            }\n                            return null;\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, this),\n                    data?.totalItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomPagination__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        type: \"ssr\",\n                        totalPages: Math.ceil(data?.totalItems / data?.pageSize),\n                        currentPage: pageNumber,\n                        onPageChange: handlePageChange,\n                        searchQueryParams: searchParamsContent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n_s(ArticlesList, \"kCCrGwijBur6MosTsACv7Pj+5/c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.useSearchParams,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.usePathname,\n        embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    ];\n});\n_c = ArticlesList;\nvar _c;\n$RefreshReg$(_c, \"ArticlesList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/ArticlesList.jsx\n"));

/***/ })

});