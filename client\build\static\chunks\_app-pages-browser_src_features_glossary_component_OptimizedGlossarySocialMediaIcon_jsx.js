"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_features_glossary_component_OptimizedGlossarySocialMediaIcon_jsx"],{

/***/ "(app-pages-browser)/./src/features/glossary/component/OptimizedGlossarySocialMediaIcon.jsx":
/*!******************************************************************************!*\
  !*** ./src/features/glossary/component/OptimizedGlossarySocialMediaIcon.jsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n\nvar _s = $RefreshSig$();\n\n\n// Lazy load SVG icons with better loading states\nconst SvgInstagram = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_c = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_assets_images_icons_instagram_svg\").then(__webpack_require__.bind(__webpack_require__, /*! @/assets/images/icons/instagram.svg */ \"(app-pages-browser)/./src/assets/images/icons/instagram.svg\")), {\n    loadableGenerated: {\n        modules: [\n            \"features\\\\glossary\\\\component\\\\OptimizedGlossarySocialMediaIcon.jsx -> \" + \"@/assets/images/icons/instagram.svg\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-12 h-12 bg-gray-200 rounded-full animate-pulse\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossarySocialMediaIcon.jsx\",\n            lineNumber: 6,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c1 = SvgInstagram;\nconst SvgLinkedin = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_c2 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_assets_images_icons_linkedin_svg\").then(__webpack_require__.bind(__webpack_require__, /*! @/assets/images/icons/linkedin.svg */ \"(app-pages-browser)/./src/assets/images/icons/linkedin.svg\")), {\n    loadableGenerated: {\n        modules: [\n            \"features\\\\glossary\\\\component\\\\OptimizedGlossarySocialMediaIcon.jsx -> \" + \"@/assets/images/icons/linkedin.svg\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-12 h-12 bg-gray-200 rounded-full animate-pulse\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossarySocialMediaIcon.jsx\",\n            lineNumber: 11,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c3 = SvgLinkedin;\nconst SvgFacebook = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_c4 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_assets_images_icons_facebook_svg\").then(__webpack_require__.bind(__webpack_require__, /*! @/assets/images/icons/facebook.svg */ \"(app-pages-browser)/./src/assets/images/icons/facebook.svg\")), {\n    loadableGenerated: {\n        modules: [\n            \"features\\\\glossary\\\\component\\\\OptimizedGlossarySocialMediaIcon.jsx -> \" + \"@/assets/images/icons/facebook.svg\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-12 h-12 bg-gray-200 rounded-full animate-pulse\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossarySocialMediaIcon.jsx\",\n            lineNumber: 16,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c5 = SvgFacebook;\nconst SvgX = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_c6 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_assets_images_icons_x_svg\").then(__webpack_require__.bind(__webpack_require__, /*! @/assets/images/icons/x.svg */ \"(app-pages-browser)/./src/assets/images/icons/x.svg\")), {\n    loadableGenerated: {\n        modules: [\n            \"features\\\\glossary\\\\component\\\\OptimizedGlossarySocialMediaIcon.jsx -> \" + \"@/assets/images/icons/x.svg\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-12 h-12 bg-gray-200 rounded-full animate-pulse\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossarySocialMediaIcon.jsx\",\n            lineNumber: 21,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c7 = SvgX;\nconst OptimizedGlossarySocialMediaIcon = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c8 = _s(function OptimizedGlossarySocialMediaIcon() {\n    _s();\n    const handleSocialMediaClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((url, platform)=>{\n        // Add analytics tracking if needed\n        if (typeof gtag !== \"undefined\") {\n            gtag(\"event\", \"social_click\", {\n                platform: platform,\n                page_location: window.location.href\n            });\n        }\n        window.open(url, \"_blank\", \"noopener,noreferrer\");\n    }, []);\n    const socialLinks = [\n        {\n            component: SvgFacebook,\n            url: \"https://www.facebook.com/Pentabell\",\n            platform: \"facebook\",\n            label: \"Follow us on Facebook\"\n        },\n        {\n            component: SvgLinkedin,\n            url: \"https://www.linkedin.com/company/pentabell/\",\n            platform: \"linkedin\",\n            label: \"Connect with us on LinkedIn\"\n        },\n        {\n            component: SvgX,\n            url: \"https://twitter.com/pentabell_group\",\n            platform: \"twitter\",\n            label: \"Follow us on X (Twitter)\"\n        },\n        {\n            component: SvgInstagram,\n            url: \"https://www.instagram.com/pentabell/\",\n            platform: \"instagram\",\n            label: \"Follow us on Instagram\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"glossary-social-media-icons\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col lg:flex-row lg:justify-end items-center gap-4 p-4\",\n            children: socialLinks.map((param)=>{\n                let { component: IconComponent, url, platform, label } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleSocialMediaClick(url, platform),\n                    className: \"social-icon-button p-3 rounded-full border border-gray-300 hover:border-blue-500 transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-300\",\n                    \"aria-label\": label,\n                    type: \"button\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                        className: \"w-6 h-6 text-gray-600 hover:text-blue-600 transition-colors\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossarySocialMediaIcon.jsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, this)\n                }, platform, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossarySocialMediaIcon.jsx\",\n                    lineNumber: 69,\n                    columnNumber: 11\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossarySocialMediaIcon.jsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossarySocialMediaIcon.jsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}, \"MLGnMDxdKbk1JlPDIqGBqS3fLYQ=\")), \"MLGnMDxdKbk1JlPDIqGBqS3fLYQ=\");\n_c9 = OptimizedGlossarySocialMediaIcon;\n/* harmony default export */ __webpack_exports__[\"default\"] = (OptimizedGlossarySocialMediaIcon);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"SvgInstagram$dynamic\");\n$RefreshReg$(_c1, \"SvgInstagram\");\n$RefreshReg$(_c2, \"SvgLinkedin$dynamic\");\n$RefreshReg$(_c3, \"SvgLinkedin\");\n$RefreshReg$(_c4, \"SvgFacebook$dynamic\");\n$RefreshReg$(_c5, \"SvgFacebook\");\n$RefreshReg$(_c6, \"SvgX$dynamic\");\n$RefreshReg$(_c7, \"SvgX\");\n$RefreshReg$(_c8, \"OptimizedGlossarySocialMediaIcon$memo\");\n$RefreshReg$(_c9, \"OptimizedGlossarySocialMediaIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/OptimizedGlossarySocialMediaIcon.jsx\n"));

/***/ })

}]);