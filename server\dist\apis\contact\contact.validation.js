"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DirectHiringSchema = exports.CountryContactSchema = exports.getInTouchFormContactSchema = exports.mainServiceFormSchema = exports.JoinUsFormContactSchema = exports.payrollServiceFormSchema = exports.GetInTouchFormConsultingService = exports.technicalAssistanceContactSchema = exports.getInTouchFormSchema = void 0;
const joi_1 = __importDefault(require("joi"));
const getInTouchFormSchema = joi_1.default.object({
    to: joi_1.default.string().email().required(),
    firstName: joi_1.default.string().required(),
    lastName: joi_1.default.string().required(),
    email: joi_1.default.string().email().required(),
    phone: joi_1.default.string().allow(''),
    message: joi_1.default.string().required(),
    type: joi_1.default.string().required(),
    team: joi_1.default.string().required(),
    resume: joi_1.default.string().optional(),
    field: joi_1.default.string().optional(),
});
exports.getInTouchFormSchema = getInTouchFormSchema;
const getInTouchFormContactSchema = joi_1.default.object({
    to: joi_1.default.string().email().required(),
    fullName: joi_1.default.string().required(),
    email: joi_1.default.string().email().required(),
    phone: joi_1.default.string().allow(''),
    youAre: joi_1.default.string().required(),
    subject: joi_1.default.string().allow(''),
    country: joi_1.default.string().allow(''),
    message: joi_1.default.string().required(),
    type: joi_1.default.string().required(),
    team: joi_1.default.string().required(),
    resume: joi_1.default.string().optional(),
    field: joi_1.default.string().when('youAre', {
        is: 'Consultant',
        then: joi_1.default.required(),
        otherwise: joi_1.default.optional(),
    }),
});
exports.getInTouchFormContactSchema = getInTouchFormContactSchema;
const payrollServiceFormSchema = joi_1.default.object({
    to: joi_1.default.string().email().required(),
    type: joi_1.default.string().required(),
    firstName: joi_1.default.string().required(),
    lastName: joi_1.default.string().required(),
    email: joi_1.default.string().email().required(),
    phone: joi_1.default.string().allow(''),
    message: joi_1.default.string().required(),
    youAre: joi_1.default.string().required(),
    companyName: joi_1.default.string().allow(''),
    resume: joi_1.default.string().optional(),
    field: joi_1.default.string().when('youAre', {
        is: 'Consultant',
        then: joi_1.default.required(),
        otherwise: joi_1.default.optional(),
    }),
});
exports.payrollServiceFormSchema = payrollServiceFormSchema;
const mainServiceFormSchema = joi_1.default.object({
    to: joi_1.default.string().email().required(),
    type: joi_1.default.string().required(),
    firstName: joi_1.default.string().required(),
    lastName: joi_1.default.string().required(),
    email: joi_1.default.string().email().required(),
    phone: joi_1.default.string().allow(''),
    message: joi_1.default.string().required(),
    companyName: joi_1.default.string().allow(''),
    enquirySelect: joi_1.default.string().required(),
    resume: joi_1.default.string().optional(),
    field: joi_1.default.string().optional(),
});
exports.mainServiceFormSchema = mainServiceFormSchema;
const technicalAssistanceContactSchema = joi_1.default.object({
    to: joi_1.default.string().email().required(),
    fullName: joi_1.default.string().required(),
    jobTitle: joi_1.default.string().allow(''),
    phone: joi_1.default.string().allow(''),
    companyName: joi_1.default.string().allow(''),
    email: joi_1.default.string().email().required(),
    message: joi_1.default.string().required(),
    type: joi_1.default.string().required(),
    team: joi_1.default.string().required(),
    resume: joi_1.default.string().optional(),
    field: joi_1.default.string().optional(),
});
exports.technicalAssistanceContactSchema = technicalAssistanceContactSchema;
const GetInTouchFormConsultingService = joi_1.default.object({
    to: joi_1.default.string().email().required(),
    fullName: joi_1.default.string().required(),
    jobTitle: joi_1.default.string().allow(''),
    email: joi_1.default.string().email().required(),
    phone: joi_1.default.string().allow(''),
    companyName: joi_1.default.string().allow(''),
    youAre: joi_1.default.string().required(),
    message: joi_1.default.string().required(),
    type: joi_1.default.string().required(),
    team: joi_1.default.string().required(),
    resume: joi_1.default.string().optional(),
    field: joi_1.default.string().when('youAre', {
        is: 'Consultant',
        then: joi_1.default.required(),
        otherwise: joi_1.default.optional(),
    }),
});
exports.GetInTouchFormConsultingService = GetInTouchFormConsultingService;
const DirectHiringSchema = joi_1.default.object({
    to: joi_1.default.string().email().required(),
    fullName: joi_1.default.string().required(),
    jobTitle: joi_1.default.string().allow(''),
    email: joi_1.default.string().email().required(),
    phone: joi_1.default.string().allow(''),
    youAre: joi_1.default.string().required(),
    message: joi_1.default.string().required(),
    type: joi_1.default.string().required(),
    resume: joi_1.default.string().optional(),
    field: joi_1.default.string().when('youAre', {
        is: 'Consultant',
        then: joi_1.default.required(),
        otherwise: joi_1.default.optional(),
    }),
});
exports.DirectHiringSchema = DirectHiringSchema;
const JoinUsFormContactSchema = joi_1.default.object({
    to: joi_1.default.alternatives().try(joi_1.default.string().email().required(), joi_1.default.array().items(joi_1.default.string().email()).min(1).required()),
    fullName: joi_1.default.string().required(),
    email: joi_1.default.string().email().required(),
    phone: joi_1.default.string().allow(''),
    field: joi_1.default.string().required(),
    subject: joi_1.default.string().required(),
    message: joi_1.default.string().required(),
    type: joi_1.default.string().required(),
    team: joi_1.default.string().required(),
    mission: joi_1.default.boolean().required(),
    resume: joi_1.default.string().required(),
});
exports.JoinUsFormContactSchema = JoinUsFormContactSchema;
const CountryContactSchema = joi_1.default.object({
    to: joi_1.default.string().email().required(),
    type: joi_1.default.string().required(),
    fullName: joi_1.default.string().required(),
    email: joi_1.default.string().email().required(),
    phone: joi_1.default.string().allow(''),
    message: joi_1.default.string().required(),
    companyName: joi_1.default.string().allow(''),
    howToHelp: joi_1.default.string().required(),
    youAre: joi_1.default.string().required(),
    countryName: joi_1.default.string().required(),
    country: joi_1.default.string().allow(''),
    resume: joi_1.default.string().optional(),
    field: joi_1.default.string().when('youAre', {
        is: 'Consultant',
        then: joi_1.default.required(),
        otherwise: joi_1.default.optional(),
    }),
});
exports.CountryContactSchema = CountryContactSchema;
//# sourceMappingURL=contact.validation.js.map