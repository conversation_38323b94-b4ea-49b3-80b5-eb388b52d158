import BannerComponents from "@/components/sections/BannerComponents";
import banner from "../../../../assets/images/website/banner/Pentabell-Services.webp";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import ServiceRow from "@/components/ui/ServiceRow";
import serviceimgS1 from "../../../../assets/images/services/service1.png";
import serviceimgS2 from "../../../../assets/images/services/serviceimgS2.png";
import serviceimgS3 from "../../../../assets/images/services/serviceimgS3.png";
import serviceimgS4 from "../../../../assets/images/services/serviceimgS4.png";
import serviceimgS5 from "../../../../assets/images/services/serviceimgS5.png";
import OurIndustries from "@/components/sections/OurIndustries";
import OurPartners from "@/components/sections/OurPartners";
import initTranslations from "@/app/i18n";
import MainServicePageForm from "@/features/forms/components/MainServicePageForm";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJsonSSR } from "@/config/axios";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }hr-services/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/hr-services/`,
    en: `https://www.pentabell.com/hr-services/`,
    "x-default": `https://www.pentabell.com/hr-services/`,
  };

  const { t } = await initTranslations(locale, ["mainService", "global"]);

  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/hr-services`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("mainService:metaTitle"),
    description: t("mainService:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}
async function hrService({ params: { locale } }) {
  const { t } = await initTranslations(locale, ["mainService", "global"]);
  const dataS1 = {
    title: t("mainService:dataS1:title"),
    altImg: t("mainService:dataS1:altImg"),
    subTitle: t("mainService:dataS1:label"),
    btnLabel: "Learn more",
    btnLink: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
    featureImg: serviceimgS1,
    items: [
      t("mainService:dataS1:items:item1"),
      t("mainService:dataS1:items:item2"),
      t("mainService:dataS1:items:item3"),
    ],
  };
  const dataS2 = {
    title: t("mainService:dataS2:title"),
    altImg: t("mainService:dataS2:altImg"),
    subTitle: t("mainService:dataS2:label"),
    btnLabel: "Learn more",
    btnLink: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
    featureImg: serviceimgS2,
    items: [
      t("mainService:dataS2:items:item1"),
      t("mainService:dataS2:items:item2"),
      t("mainService:dataS2:items:item3"),
    ],
  };
  const dataS3 = {
    title: t("mainService:dataS3:title"),
    altImg: t("mainService:dataS3:altImg"),
    subTitle: t("mainService:dataS3:label"),
    btnLabel: "Learn more",
    btnLink: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
    featureImg: serviceimgS3,
    items: [
      t("mainService:dataS3:items:item1"),
      t("mainService:dataS3:items:item2"),
      t("mainService:dataS3:items:item3"),
    ],
  };
  const dataS4 = {
    title: t("mainService:dataS4:title"),
    altImg: t("mainService:dataS4:altImg"),
    subTitle: t("mainService:dataS4:label"),
    btnLabel: "Learn more",
    btnLink: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
    featureImg: serviceimgS4,
    items: [
      t("mainService:dataS4:items:item1"),
      t("mainService:dataS4:items:item2"),
      t("mainService:dataS4:items:item3"),
    ],
  };

  const dataS5 = {
    title: t("mainService:dataS5:title"),
    altImg: t("mainService:dataS5:altImg"),
    subTitle: t("mainService:dataS5:label"),
    btnLabel: "Learn more",
    btnLink: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
    featureImg: serviceimgS5,
    items: [
      t("mainService:dataS5:items:item1"),
      t("mainService:dataS5:items:item2"),
      t("mainService:dataS5:items:item3"),
    ],
  };
  return (
    <div id="services-page">
      <BannerComponents
        title={t("mainService:intro:title")}
        description={t("mainService:intro:description")}
        bannerImg={banner}
        height={"70vh"}
        altImg={t("mainService:intro:altImg")}
      />
      <OurPartners disableTxt={true} />
      <ResponsiveRowTitleText
        title={t("mainService:overview:title")}
        paragraph={t("mainService:overview:description")}
      />
      <p className="heading-h1 text-center text-banking">
        {t("mainService:howWeHelp")}
      </p>
      <ServiceRow data={dataS1} />
      <ServiceRow data={dataS2} reverse={true} darkBg={true} />
      <ServiceRow data={dataS3} />
      <ServiceRow data={dataS4} reverse={true} darkBg={true} />
      <ServiceRow data={dataS5} />
      <OurIndustries />
      {/* <OurExperts /> */}
      {/* <ServicePageForm /> */}
      <MainServicePageForm />
    </div>
  );
}
export default hrService;
