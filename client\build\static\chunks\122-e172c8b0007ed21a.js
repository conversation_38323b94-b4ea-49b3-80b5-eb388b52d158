"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[122],{40458:function(e,t,n){var r,a,s=n(94746);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}t.Z=e=>s.createElement("svg",l({xmlns:"http://www.w3.org/2000/svg",width:29,height:28,fill:"none"},e),r||(r=s.createElement("rect",{width:27,height:27,x:28,y:27.5,stroke:"#798BA3",rx:13.5,transform:"rotate(-180 28 27.5)"})),a||(a=s.createElement("path",{stroke:"#798BA3",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"m16.75 9.5-4.5 4.5 4.5 4.5"})))},64812:function(e,t,n){n.r(t);var r,a=n(94746);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}t.default=e=>a.createElement("svg",s({xmlns:"http://www.w3.org/2000/svg",width:12,height:14,fill:"none"},e),r||(r=a.createElement("path",{fill:"#fff",fillRule:"evenodd",d:"M6 1.465a4.56 4.56 0 0 0-3.182 1.287A4.34 4.34 0 0 0 1.5 5.86c0 1.7 1.137 3.373 2.42 4.696A17 17 0 0 0 6 12.365q.155-.114.353-.267a17 17 0 0 0 1.728-1.542C9.363 9.233 10.5 7.56 10.5 5.86a4.34 4.34 0 0 0-1.318-3.108A4.56 4.56 0 0 0 6 1.465m0 11.802-.416.61-.002-.002-.004-.002-.014-.01a7 7 0 0 1-.229-.156 18.345 18.345 0 0 1-2.505-2.143C1.489 10.178 0 8.148 0 5.86c0-1.554.632-3.044 1.757-4.144A6.07 6.07 0 0 1 6 0c1.591 0 3.117.617 4.243 1.716A5.8 5.8 0 0 1 12 5.86c0 2.29-1.488 4.32-2.83 5.703a18.4 18.4 0 0 1-2.734 2.3l-.014.01-.004.003h-.001s-.001.001-.417-.609m0 0 .416.61a.77.77 0 0 1-.832 0zm0-8.383c-.552 0-1 .437-1 .976 0 .54.448.977 1 .977S7 6.4 7 5.86a.99.99 0 0 0-1-.976m-2.5.976C3.5 4.512 4.62 3.42 6 3.42S8.5 4.512 8.5 5.86c0 1.35-1.12 2.442-2.5 2.442S3.5 7.21 3.5 5.86",clipRule:"evenodd"})))},51765:function(e,t,n){var r,a,s=n(94746);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}t.Z=e=>s.createElement("svg",l({xmlns:"http://www.w3.org/2000/svg",width:18,height:18,fill:"none"},e),r||(r=s.createElement("g",{clipPath:"url(#refreshIcon_svg__a)"},s.createElement("path",{fill:"#234791",fillRule:"evenodd",d:"M6.85 1.557a7.75 7.75 0 0 1 9.46 4.86 1 1 0 1 1-1.885.667 5.75 5.75 0 0 0-9.51-2.125L3.275 6.5H5.25a1 1 0 1 1 0 2H.75a1 1 0 0 1-1-1V3a1 1 0 1 1 2 0v2.188l1.784-1.676a7.75 7.75 0 0 1 3.317-1.955m4.9 8.943a1 1 0 0 1 1-1h4.5a1 1 0 0 1 1 1V15a1 1 0 1 1-2 0v-2.188l-1.784 1.677A7.75 7.75 0 0 1 1.69 11.584a1 1 0 1 1 1.885-.667 5.75 5.75 0 0 0 9.51 2.124l1.64-1.54H12.75a1 1 0 0 1-1-1",clipRule:"evenodd"}))),a||(a=s.createElement("defs",null,s.createElement("clipPath",{id:"refreshIcon_svg__a"},s.createElement("path",{fill:"#fff",d:"M0 0h18v18H0z"})))))},81006:function(e,t,n){var r,a=n(94746);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}t.Z=e=>a.createElement("svg",s({xmlns:"http://www.w3.org/2000/svg",width:18,height:18,fill:"none"},e),r||(r=a.createElement("path",{stroke:"#798BA3",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"m15.75 15.75-3.262-3.262M14.25 8.25a6 6 0 1 1-12 0 6 6 0 0 1 12 0"})))},68895:function(e,t,n){n.d(t,{Z:function(){return f}});var r,a,s=n(57437),l=n(2265),i=n(57384),o=n(40718),c=n.n(o),d=n(33833),u=n(31817),p=n(94746);function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}var h=e=>p.createElement("svg",g({xmlns:"http://www.w3.org/2000/svg",width:29,height:28,fill:"none"},e),r||(r=p.createElement("rect",{width:27,height:27,x:1,y:.5,stroke:"#798BA3",rx:13.5})),a||(a=p.createElement("path",{stroke:"#798BA3",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"m12.25 18.5 4.5-4.5-4.5-4.5"}))),m=n(40458);let y=e=>{let{totalPages:t,currentPage:n,onPageChange:r,onPageSizeChange:a,showSelectPageSize:o,type:c,searchQueryParams:p}=e,[g,y]=(0,l.useState)(5);return(0,l.useEffect)(()=>{y(g)},[g]),(0,s.jsxs)("center",{className:"pagination",children:[o&&(0,s.jsx)("div",{className:"page-size-container",children:(0,s.jsxs)(d.Z,{value:g,onChange:e=>{let t=Number(e.target.value);y(t),a(t)},className:"page-size-select",children:[(0,s.jsx)("option",{value:"3",children:"3"}),(0,s.jsx)("option",{value:"5",children:"5"}),(0,s.jsx)("option",{value:"8",children:"8"})]})}),(0,s.jsx)("div",{className:"pagination-wrapper",children:(0,s.jsx)(i.Z,{count:t,page:n,onChange:(e,t)=>{if("ssr"===c){let e=new URLSearchParams(p);e.set("pageNumber",t),window.location.href=`?${e.toString()}`}else r(parseInt(t))},siblingCount:0,boundaryCount:1,renderItem:e=>(0,s.jsx)(u.Z,{slots:{previous:m.Z,next:h},...e}),sx:{"& .MuiPaginationItem-root":{fontWeight:"400",color:"#798BA3"},"& .Mui-selected":{backgroundColor:"transparent !important",color:"#0B3051",fontWeight:"700"}}})})]})};y.propTypes={totalPages:c().number.isRequired,currentPage:c().number.isRequired,onPageChange:c().func.isRequired,onPageSizeChange:c().func.isRequired};var f=y},80122:function(e,t,n){n.d(t,{default:function(){return er}});var r,a,s,l=n(57437),i=n(2265),o=n(55788),c=n(98489),d=n(89414),u=n(31691),p=n(59873),g=n(49443),h=n(99376),m=(e,t,n)=>{let r=(0,h.usePathname)(),a=(0,h.useSearchParams)(),s=a?.toString()||"",l=new URLSearchParams(s),[o,c]=(0,i.useState)(a?.get("keyWord")||""),[d,u]=(0,i.useState)(a?.get("country")||""),[p,g]=(0,i.useState)(parseInt(a?.get("pageNumber")||"1",10)),[m,y]=(0,i.useState)(a?.get("industry")?.split(",")||[]),[f,v]=(0,i.useState)(a?.get("contractType")?.split(",")||[]),[w,b]=(0,i.useState)(a?.get("levelOfExperience")?.split(",")||[]),[x,j]=(0,i.useState)(a?.get("jobDescriptionLanguages")?.split(",")||[]),[C,E]=(0,i.useState)([]),[k,S]=(0,i.useState)(!0),[A,N]=(0,i.useState)(void 0!==n?n:a?.get("list")==="Yes");(0,i.useEffect)(()=>{let e=[];if(a?.get("industry")){let t=a.get("industry").split(",");e.push(...t.map(e=>({category:"industry",label:e.trim()})))}if(a?.get("contractType")){let t=a.get("contractType").split(",");e.push(...t.map(e=>({category:"contractType",label:e.trim()})))}if(a?.get("levelOfExperience")){let t=a.get("levelOfExperience").split(",");e.push(...t.map(e=>({category:"levelOfExperience",label:e.trim()})))}if(a?.get("jobDescriptionLanguages")){let t=a.get("jobDescriptionLanguages").split(",");e.push(...t.map(e=>({category:"jobDescriptionLanguages",label:e.trim()})))}if(a?.get("country")){let t=a.get("country").split(",");e.push(...t.map(e=>({category:"country",label:e.trim()})))}if(a?.get("keyWord")){let t=a.get("keyWord").split(",");e.push(...t.map(e=>({category:"keyWord",label:e.trim()})))}E(e)},[a]);let L=(0,i.useCallback)(n=>{let a=n||window.scrollY||document.documentElement.scrollTop;window._isSearching=!0,m&&m.length>0&&l.set("industry",Array.isArray(m)?m.join(","):m),f&&f.length>0&&l.set("contractType",Array.isArray(f)?f.join(","):f),o&&l.set("keyWord",o),d&&l.set("country",Array.isArray(d)?d.join(","):d),l.set("pageNumber",p||1),A?l.set("list","Yes"):l.delete("list"),x&&x.length>0&&l.set("jobDescriptionLanguages",Array.isArray(x)?x.join(","):x),w&&w.length>0&&l.set("levelOfExperience",Array.isArray(w)?w.join(","):w);let s=`${r}?${l.toString()}`;window.history.replaceState({path:s},"",s);let i={};for(let[e,t]of l.entries())i[e]=t;i.language=t,e(i),window.scrollTo({top:a,behavior:"instant"}),window._isSearching=!1},[m,f,o,d,p,x,w,l,r,e,t]),P=(0,i.useCallback)(e=>{c(e.target.value)},[]),O=(0,i.useCallback)(e=>{e&&e.preventDefault&&e.preventDefault();let t=window.scrollY||document.documentElement.scrollTop;return""===o?l.delete("keyWord"):o&&l.set("keyWord",o),d&&0!==d.length?d&&l.set("country",Array.isArray(d)?d.join(","):d):l.delete("country"),L(t),window.dispatchEvent(new CustomEvent("searchPerformed",{detail:{keyWord:o,country:d,industry:m,contractType:f,levelOfExperience:w,jobDescriptionLanguages:x,maintainScroll:!0,scrollPosition:t}})),!1},[L,o,d,m,f,w,x,l]),T=(0,i.useCallback)(e=>{if("Enter"===e.key)return e.preventDefault(),!1},[]),W=(0,i.useCallback)(function(n){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n&&n.preventDefault&&n.preventDefault();let s=window.scrollY||document.documentElement.scrollTop;window._isResetting=!0,c(""),g(1),a.preserveCountry||u(""),a.preserveIndustry||y([]),v([]),b([]),j([]);let l=new URLSearchParams;l.set("pageNumber",1),A&&l.set("list","Yes"),a.preserveIndustry&&m&&m.length>0&&l.set("industry",Array.isArray(m)?m.join(","):m),a.preserveCountry&&a.countryName&&l.set("country",a.countryName);let i=`${r}?${l.toString()}`;window.history.replaceState({path:i},"",i);let o={pageNumber:1,language:t};a.preserveIndustry&&(a.industryName?o.industry=a.industryName:m&&m.length>0&&(o.industry=Array.isArray(m)?m.join(","):m)),a.preserveCountry&&a.countryName&&(o.country=a.countryName),e(o);let d=[];return a.preserveIndustry&&C.length>0&&(d=C.filter(e=>"industry"===e.category)),E(d),window.dispatchEvent(new CustomEvent("filtersReset",{detail:{pageNumber:1,maintainScroll:!0,scrollPosition:s,preserveIndustry:a.preserveIndustry,preserveCountry:a.preserveCountry}})),window.scrollTo({top:s,behavior:"instant"}),window._isResetting=!1,!1},[c,g,u,y,v,b,j,E,r,e,t,m,C]),D=(0,i.useCallback)(n=>{g(n),l.set("pageNumber",n),A?l.set("list","Yes"):l.delete("list");let a=`${r}?${l.toString()}`;window.history.replaceState({path:a},"",a);let s={};for(let[e,t]of l.entries())s[e]=t;s.language=t,e(s)},[l,r,e,t]),I=(0,i.useCallback)(t=>{let n=window.scrollY||document.documentElement.scrollTop;N(t);let a=new URLSearchParams(window.location.search),s={};for(let[e,t]of a.entries())"list"!==e&&(s[e]=t);t?(a.set("list","Yes"),s.list="Yes"):a.delete("list"),s.pageSize=10;let l=`${r}?${a.toString()}`;window.history.replaceState({path:l},"",l),window.scrollTo({top:n,behavior:"instant"}),e(s),window.dispatchEvent(new CustomEvent("viewModeChanged",{detail:{isList:t,params:s,maintainScroll:!0,scrollPosition:n}}))},[e,r]);return(0,i.useEffect)(()=>{let e=a?.get("list")==="Yes";e!==A&&N(e)},[a,A]),{keyWord:o,setKeyWord:c,country:d,setCountry:u,pageNumber:p,setPageNumber:g,industry:m,setIndustry:y,contractType:f,setContractType:v,levelOfExperience:w,setLevelOfExperience:b,jobDescriptionLanguages:x,setJobDescriptionLanguages:j,selectedFilters:C,setSelectedFilters:E,isFilterOpen:k,setIsFilterOpen:S,handleSearchChange:P,handleSearchClick:O,handleKeyDown:T,resetSearch:W,handlePageChange:D,updateUrlWithParams:L,searchQueryParams:l,searchParamsContent:s,isList:A,setIsList:N,handleViewModeChange:I}},y=n(77584),f=n(23996),v=n(81799),w=n(81006),b=n(64812),x=n(51765),j=n(41774),C=e=>{let{keyWord:t,country:n,countries:r,handleSearchChange:a,setCountry:s,resetSearch:i,handleSearchClick:o,setPageNumber:c,jobLocation:u,t:p}=e;return(0,l.jsxs)(d.default,{className:"container",container:!0,spacing:0,children:[(0,l.jsxs)(d.default,{item:!0,xs:12,sm:9,container:!0,spacing:0,className:"filter-inputs",children:[(0,l.jsx)(d.default,{item:!0,xs:12,sm:!0===u?8:6,children:(0,l.jsx)(y.Z,{className:"input-pentabell",autoComplete:"off",slotProps:{input:{startAdornment:(0,l.jsx)(f.Z,{position:"start",children:(0,l.jsx)(w.Z,{})})}},variant:"standard",type:"text",onKeyDown:e=>{if("Enter"===e.key)return e.preventDefault(),!1},onChange:e=>{a(e)},value:t,placeholder:p("Search")})}),!0===u?(0,l.jsx)(l.Fragment,{}):(0,l.jsx)(d.default,{item:!0,xs:12,sm:6,children:(0,l.jsx)(v.Z,{className:"input-pentabell maps",id:"tags-standard",options:r,getOptionLabel:e=>e,value:n,onChange:(e,t)=>{s(t)},renderInput:e=>(0,l.jsx)(y.Z,{...e,className:"input-pentabell multiple-select",variant:"standard",placeholder:"Country",InputProps:{...e.InputProps,startAdornment:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(f.Z,{position:"start",children:(0,l.jsx)(b.default,{})}),e.InputProps.startAdornment]})}})})})]}),(0,l.jsxs)(d.default,{item:!0,xs:12,sm:3,className:"btns-filter search-bar-opportunities filter-inputs",children:[(0,l.jsx)(j.default,{icon:(0,l.jsx)(x.Z,{}),className:"btn btn-outlined btn-refresh",onClick:e=>i(e)}),(0,l.jsx)(j.default,{text:"Search",onClick:e=>{o(e),c(1)},className:" btn btn-search btn-filled"})]})]})},E=n(63582),k=n(67571),S=e=>{let{selectedFilters:t}=e;return(0,l.jsx)(E.Z,{className:"checkbox-pentabell-delete check",direction:"row",spacing:1,sx:{marginTop:"20px",flexWrap:"wrap",mb:2},children:Array.isArray(t)&&t.map((e,t)=>(0,l.jsx)(k.Z,{label:`${e.label}`,sx:{backgroundColor:"transparent",borderColor:"#1D5A9F !important",marginTop:"10px !important",border:"1px solid",padding:"8px 20px","&:hover":{backgroundColor:"transparent"},".css-1dybbl5-MuiChip-label":{fontSize:"14px",fontFamily:"Proxima-Nova-Medium",color:"#1D5A9F","!important":!0}}},t))})},A=n(35389),N=n(23390),L=n(68895),P=e=>{let{opportunitiesData:t,language:n,pageNumber:r,handlePageChange:a,searchParamsContent:s,t:i,isList:o,isLoading:c}=e,u=t?.totalOpportunities||0;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(d.default,{item:!0,lg:9,md:9,sm:12,xs:12,container:!0,className:"grid",children:c?(0,l.jsxs)("div",{className:"loading-container",style:{textAlign:"center",padding:"40px 0",width:"100%"},children:[(0,l.jsx)(A.default,{size:40}),(0,l.jsx)("p",{style:{marginTop:"16px"},children:i("global:loading")})]}):t?.opportunities?.length>0?t.opportunities.map(e=>(0,l.jsx)(N.default,{opportunity:e,language:n,isList:o},e?._id)):(0,l.jsxs)("div",{className:"no-results-container",style:{textAlign:"center",padding:"40px 0",width:"100%"},children:[(0,l.jsx)("p",{className:"no-results-message",children:i("opportunities:noOpportunitiesFound")}),(0,l.jsx)("p",{children:i("opportunities:tryDifferentFilters")})]})}),(0,l.jsx)(d.default,{item:!0,xs:12,lg:12,md:12,sm:12,container:!0,spacing:0,children:u>0&&(0,l.jsx)(L.Z,{type:"ssr",totalPages:Math.ceil(u/10),currentPage:r,onPageChange:a,searchQueryParams:s})})]})},O=n(63993),T=n(30731),W=n(96369),D=n(46387),I=n(44164),Z=n(52700),R=e=>{let{title:t,expanded:n,onChange:r,children:a}=e;return(0,l.jsxs)(T.Z,{expanded:n,onChange:r,sx:{backgroundColor:"transparent",boxShadow:"none",margin:0,padding:0},children:[(0,l.jsx)(W.Z,{expandIcon:(0,l.jsx)(Z.Z,{}),children:(0,l.jsx)(D.default,{component:"span",sx:{width:"auto",flexShrink:0},className:"title-filter",children:t})}),(0,l.jsx)(I.Z,{children:a})]})},F=n(85860),z=n(11953),_=e=>{let{options:t,values:n,category:r,onChange:a}=e;return(0,l.jsx)("div",{className:"filter-options",children:t.map(e=>(0,l.jsx)(F.Z,{control:(0,l.jsx)(z.Z,{checked:n?.[r]?.includes(e),onChange:()=>a(r,e),className:"checkbox-custom-color"}),label:e,className:"checkbox-pentabell-filter blue"},e))})},M=e=>{let{value:t,onChange:n,placeholder:r}=e;return(0,l.jsx)(y.Z,{className:"input-pentabell",autoComplete:"off",slotProps:{input:{startAdornment:(0,l.jsx)(f.Z,{position:"start",children:(0,l.jsx)(w.Z,{})})}},variant:"standard",type:"text",value:Array.isArray(t)?t.length>0?t[0]:"":t||"",onChange:n,placeholder:r})},$=e=>{let{value:t,options:n,onChange:r}=e;return(0,l.jsx)("div",{className:"filter-options",children:(0,l.jsx)(v.Z,{className:"input-pentabell maps",id:"tags-standard",options:n,getOptionLabel:e=>e,value:Array.isArray(t)?t.length>0?t[0]:"":t||"",onChange:r,renderInput:e=>(0,l.jsx)(y.Z,{...e,className:"input-pentabell multiple-select",variant:"standard",placeholder:"Country",InputProps:{...e.InputProps,startAdornment:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(f.Z,{position:"start",children:(0,l.jsx)(b.default,{})}),e.InputProps.startAdornment]})}})})})},U=e=>{let{onClear:t}=e;return(0,l.jsx)("div",{className:"filter-actions",children:(0,l.jsx)(j.default,{text:"Clear",onClick:t,className:"btn btn-search btn-filled apply"})})};let Y=["Banking","Energies","IT & Telecom","Transport","Pharmaceutical","Other"],B=["CDD","CDIC","FREELANCE"],V=["French","English","Spanish","Arabic","German"],H=["Entry level","Intermediate","Expert"];var q=(e,t)=>{let[n,r]=(0,i.useState)({industry:!e&&!t,contract:!e&&!t,search:e||t,country:!1,language:!1,experience:!1}),a=(0,i.useCallback)(e=>{r(t=>({...t,[e]:!t[e]}))},[]);return(0,i.useEffect)(()=>{r(n=>({...n,industry:!e&&!t,contract:!e&&!t,search:e||t}))},[e,t]),{expandedSections:n,toggleSection:a}},G=e=>{let{setFieldValue:t,values:n,pathname:r,setPageNumber:a,setSelectedFilters:s}=e,l=(0,u.Z)(),o=(0,p.Z)(l.breakpoints.down("sm")),c=(0,p.Z)(l.breakpoints.down("md"));(0,i.useEffect)(()=>{let e=e=>{let n=e.detail?.preserveIndustry,r=e.detail?.preserveCountry;t("jobDescriptionLanguages",[]),t("levelOfExperience",[]),t("contractType",[]),t("keyWord",[]),n||t("industry",[]),r||t("country",[])};return window.addEventListener("filtersReset",e),()=>{window.removeEventListener("filtersReset",e)}},[t]);let d=(0,i.useCallback)(e=>{let t=window.scrollY||document.documentElement.scrollTop,n=new URLSearchParams(window.location.search);n.has("list")&&!e.has("list")&&e.set("list",n.get("list"));let a=`${r}?${e.toString()}`;window.history.replaceState({path:a},"",a);let s={};for(let[t,n]of e.entries())s[t]=n;window.dispatchEvent(new CustomEvent("filterChanged",{detail:{params:s,maintainScroll:!0,scrollPosition:t}})),window.scrollTo({top:t,behavior:"instant"})},[r]),g=(0,i.useCallback)((e,r)=>{let l=n[e]?.includes(r)?n[e]?.filter(e=>e!==r):[...n[e]||[],r];t(e,l);let i=new URLSearchParams(window.location.search);l.length>0?i.set(e,l.join(",")):i.delete(e);let o=new URLSearchParams(window.location.search);o.has("list")&&!i.has("list")&&i.set("list",o.get("list")),i.set("pageNumber",1),a(1),d(i),s(t=>{let n=t.filter(t=>t.category!==e);return l.length>0?[...n,...l.map(t=>({category:e,label:t}))]:n}),window.dispatchEvent(new CustomEvent("checkboxFilterChanged",{detail:{category:e,value:r,newValues:l,allValues:n,maintainScroll:!0,scrollPosition:window.scrollY||document.documentElement.scrollTop}}))},[n,t,a,s,d]);return{handleCheckboxChange:g,handleSearchChange:(0,i.useCallback)(e=>{let r=e.target.value,l=r?[r]:[];if(t("keyWord",l),o||c){let e=new URLSearchParams(window.location.search);l.length>0&&l[0].trim()?e.set("keyWord",l[0]):e.delete("keyWord");let t=new URLSearchParams(window.location.search);t.has("list")&&!e.has("list")&&e.set("list",t.get("list")),e.set("pageNumber",1),a(1),d(e),s(e=>{let t=e.filter(e=>"keyWord"!==e.category);return l.length>0&&l[0].trim()?[...t,{category:"keyWord",label:l[0]}]:t}),window.dispatchEvent(new CustomEvent("checkboxFilterChanged",{detail:{category:"keyWord",value:l[0]||"",newValues:l,allValues:n,maintainScroll:!0,scrollPosition:window.scrollY||document.documentElement.scrollTop}}))}},[t,o,c,a,s,d,n]),handleCountryChange:(0,i.useCallback)((e,r)=>{if(t("country",r),o||c){let e=new URLSearchParams(window.location.search);r&&r.trim()?e.set("country",r):e.delete("country");let t=new URLSearchParams(window.location.search);t.has("list")&&!e.has("list")&&e.set("list",t.get("list")),e.set("pageNumber",1),a(1),d(e),s(e=>{let t=e.filter(e=>"country"!==e.category);return r&&r.trim()?[...t,{category:"country",label:r}]:t}),window.dispatchEvent(new CustomEvent("checkboxFilterChanged",{detail:{category:"country",value:r||"",newValues:r?[r]:[],allValues:n,maintainScroll:!0,scrollPosition:window.scrollY||document.documentElement.scrollTop}}))}},[t,o,c,a,s,d,n]),handleClearFilters:(0,i.useCallback)(e=>{e&&e.preventDefault&&e.preventDefault();let n=window.scrollY||document.documentElement.scrollTop;window._isClearing=!0,t("jobDescriptionLanguages",[]),t("levelOfExperience",[]),t("industry",[]),t("contractType",[]),(o||c)&&(t("keyWord",[]),t("country",[]));let a=new URLSearchParams(window.location.search);a.delete("jobDescriptionLanguages"),a.delete("levelOfExperience"),a.delete("industry"),a.delete("contractType"),(o||c)&&(a.delete("keyWord"),a.delete("country"));let l=`${r}?${a.toString()}`;window.history.replaceState({path:l},"",l);let i={};for(let[e,t]of a.entries())i[e]=t;return window.dispatchEvent(new CustomEvent("filterChanged",{detail:{params:i,maintainScroll:!0,scrollPosition:n}})),s(e=>o||c?[]:e.filter(e=>"keyWord"===e.category||"country"===e.category)),window.scrollTo({top:n,behavior:"instant"}),window._isClearing=!1,!1},[t,s,r,o,c])}},J=e=>{let{isOpen:t,onClose:n,setFieldValue:r,values:a,t:s,countries:i,setPageNumber:o,jobIndustry:c,setSelectedFilters:d}=e,g=(0,u.Z)(),m=(0,p.Z)(g.breakpoints.down("sm")),y=(0,p.Z)(g.breakpoints.down("md")),f=(0,h.usePathname)(),{expandedSections:v,toggleSection:w}=q(m,y),{handleCheckboxChange:b,handleSearchChange:x,handleCountryChange:j,handleClearFilters:C}=G({setFieldValue:r,values:a,pathname:f,setPageNumber:o,setSelectedFilters:d});return(0,l.jsx)("div",{id:"filter-actions",className:`filter-popup ${t?"open":""}`,children:(0,l.jsxs)("div",{className:"filter-popup-content",children:[m&&(0,l.jsx)(R,{title:"Search",expanded:v.search,onChange:()=>w("search"),children:(0,l.jsx)(M,{value:a.keyWord,onChange:x,placeholder:s("Search")})}),m&&(0,l.jsx)(R,{title:"Country",expanded:v.country,onChange:()=>w("country"),children:(0,l.jsx)($,{value:a.country,options:i,onChange:j})}),!c&&(0,l.jsx)(R,{title:"Industry",expanded:v.industry,onChange:()=>w("industry"),children:(0,l.jsx)(_,{options:Y,values:a,category:"industry",onChange:b})}),(0,l.jsx)(R,{title:"Contract Type",expanded:v.contract,onChange:()=>w("contract"),children:(0,l.jsx)(_,{options:B,values:a,category:"contractType",onChange:b})}),(0,l.jsx)(R,{title:"Language",expanded:v.language,onChange:()=>w("language"),children:(0,l.jsx)(_,{options:V,values:a,category:"jobDescriptionLanguages",onChange:b})}),(0,l.jsx)(R,{title:"Level of Experience",expanded:v.experience,onChange:()=>w("experience"),children:(0,l.jsx)(_,{options:H,values:a,category:"levelOfExperience",onChange:b})}),(0,l.jsx)(U,{onClear:C})]})})},K=e=>{let{initialValues:t,isFilterOpen:n,setIsFilterOpen:r,t:a,jobLocation:s,jobIndustry:o,countries:c,setSelectedFilters:u,setPageNumber:p,handleSubmitFilter:g}=e,h=(0,i.useRef)(null);return(0,i.useEffect)(()=>{let e=e=>{if(h.current){let{setFieldValue:t}=h.current,{keyWord:n,country:r,industry:a,contractType:s,levelOfExperience:l,jobDescriptionLanguages:i}=e.detail;n&&t("keyWord",Array.isArray(n)?n:[n]),r&&t("country",Array.isArray(r)?r:[r]),a&&t("industry",Array.isArray(a)?a:[a]),s&&t("contractType",Array.isArray(s)?s:[s]),l&&t("levelOfExperience",Array.isArray(l)?l:[l]),i&&t("jobDescriptionLanguages",Array.isArray(i)?i:[i])}};return window.addEventListener("searchPerformed",e),()=>{window.removeEventListener("searchPerformed",e)}},[]),(0,l.jsx)(d.default,{item:!0,lg:3,md:3,sm:12,xs:12,children:(0,l.jsx)(O.J9,{initialValues:t,enableReinitialize:"true",onSubmit:(e,t)=>g(e,t),innerRef:h,children:e=>{let{setFieldValue:t,values:i}=e;return(0,l.jsx)(O.l0,{children:(0,l.jsx)(J,{isOpen:n,onClose:()=>r(!1),t:a,jobLocation:s,setFieldValue:t,jobIndustry:o,values:i,countries:c,setSelectedFilters:u,setPageNumber:p})})}})})},Q=n(94746);function X(){return(X=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}var ee=e=>Q.createElement("svg",X({xmlns:"http://www.w3.org/2000/svg",width:21,height:21,fill:"none"},e),r||(r=Q.createElement("g",{fill:"#0B3051",clipPath:"url(#GridIcon_svg__a)"},Q.createElement("path",{d:"M1.5 1.5v6h6v-6zm7.5 6A1.5 1.5 0 0 1 7.5 9h-6A1.5 1.5 0 0 1 0 7.5v-6A1.5 1.5 0 0 1 1.5 0h6A1.5 1.5 0 0 1 9 1.5zM13.5 1.5v6h6v-6zm7.5 6A1.5 1.5 0 0 1 19.5 9h-6A1.5 1.5 0 0 1 12 7.5v-6A1.5 1.5 0 0 1 13.5 0h6A1.5 1.5 0 0 1 21 1.5zM1.5 13.5v6h6v-6zm7.5 6A1.5 1.5 0 0 1 7.5 21h-6A1.5 1.5 0 0 1 0 19.5v-6A1.5 1.5 0 0 1 1.5 12h6A1.5 1.5 0 0 1 9 13.5zM13.5 13.5v6h6v-6zm7.5 6a1.5 1.5 0 0 1-1.5 1.5h-6a1.5 1.5 0 0 1-1.5-1.5v-6a1.5 1.5 0 0 1 1.5-1.5h6a1.5 1.5 0 0 1 1.5 1.5z"}))),a||(a=Q.createElement("defs",null,Q.createElement("clipPath",{id:"GridIcon_svg__a"},Q.createElement("path",{fill:"#fff",d:"M0 0h21v21H0z"})))));function et(){return(et=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}var en=e=>Q.createElement("svg",et({xmlns:"http://www.w3.org/2000/svg",width:27,height:24,fill:"none"},e),s||(s=Q.createElement("path",{fill:"#0B3051",d:"M5.633 4.365a2.17 2.17 0 0 1-2.16 2.18 2.17 2.17 0 0 1-2.148-1.959l-.012-.221.012-.224c.11-1.1 1.03-1.96 2.149-1.96l.219.013a2.173 2.173 0 0 1 1.94 2.17M23.75 3.273l.22.022a1.092 1.092 0 0 1 0 2.139l-.22.021H9.731a1.09 1.09 0 0 1 0-2.182zM5.633 12.001a2.17 2.17 0 0 1-2.16 2.181 2.17 2.17 0 0 1-2.148-1.96l-.012-.22.012-.224c.11-1.1 1.03-1.96 2.149-1.96l.219.013A2.173 2.173 0 0 1 5.633 12M23.75 10.908l.22.022a1.092 1.092 0 0 1 0 2.139l-.22.021H9.731a1.09 1.09 0 0 1 0-2.182zM5.633 19.638a2.17 2.17 0 0 1-2.16 2.18 2.17 2.17 0 0 1-2.148-1.959l-.012-.22.012-.225c.11-1.1 1.03-1.959 2.149-1.959l.219.013a2.173 2.173 0 0 1 1.94 2.17M23.75 18.545l.22.021a1.092 1.092 0 0 1 0 2.14l-.22.02H9.731a1.09 1.09 0 0 1 0-2.181z"})));function er(e){let{language:t,initialOpportunities:n,searchParams:r,countries:a,typeCategory:s,jobIndustry:h,countryName:y,industryName:f,jobLocation:v,initialListView:w}=e,{t:b}=(0,o.$G)(),x=(0,u.Z)(),E=(0,p.Z)(x.breakpoints.down("sm")),[k,A]=(0,i.useState)(n||null),[N,L]=(0,i.useState)(!1),O=k?.totalOpportunities,T=(0,i.useCallback)(async e=>{try{L(!0);let n={language:t,pageSize:10,pageNumber:e.pageNumber||1,visibility:"Public",keyWord:e.keyWord||"",levelOfExperience:e.levelOfExperience||"",contractType:e.contractType||"",jobDescriptionLanguages:e.jobDescriptionLanguages||"",opportunityType:e.opportunityType||""};h&&f?n.industry=f:e.industry?n.industry=e.industry.replace(/\bIT\b/g,"It").replace(/\bOther\b/g,"Others"):n.industry="",v&&y?n.country=y:e.country?n.country=e.country:n.country="";let r=new Promise((e,t)=>setTimeout(()=>t(Error("Request timeout")),1e4)),a=await Promise.race([(0,g.fH)(n),r]);A(a),L(!1)}catch(e){console.error("Error fetching opportunities:",e),L(!1)}},[t,h,f,v,y]),{keyWord:W,setKeyWord:D,country:I,setCountry:Z,pageNumber:R,setPageNumber:F,setIndustry:z,setContractType:_,setLevelOfExperience:M,setJobDescriptionLanguages:$,selectedFilters:U,setSelectedFilters:Y,isFilterOpen:B,setIsFilterOpen:V,handleSearchChange:H,handleSearchClick:q,resetSearch:G,handlePageChange:J,searchParamsContent:Q,isList:X,setIsList:et,handleViewModeChange:er}=m(T,t,w);(0,i.useEffect)(()=>{let e={};r?.pageNumber&&(e.pageNumber=r.pageNumber),r?.country&&(e.country=r.country),r?.keyWord&&(e.keyWord=r.keyWord),r?.levelOfExperience&&(e.levelOfExperience=r.levelOfExperience),r?.industry&&(e.industry=r.industry),r?.contractType&&(e.contractType=r.contractType),r?.jobDescriptionLanguages&&(e.jobDescriptionLanguages=r.jobDescriptionLanguages),r?.isList&&(e.isList=r.isList),n&&(!(Object.keys(e).length>0)||r?.pageNumber||r?.isList)||T(e)},[T,r,n]),(0,i.useEffect)(()=>{let e=e=>{let t=e.detail.params,n=e.detail.maintainScroll,r=e.detail.scrollPosition||window.scrollY||document.documentElement.scrollTop,a=[];[{param:"industry",category:"industry"},{param:"contractType",category:"contractType"},{param:"levelOfExperience",category:"levelOfExperience"},{param:"jobDescriptionLanguages",category:"jobDescriptionLanguages"},{param:"country",category:"country"},{param:"keyWord",category:"keyWord"}].forEach(e=>{let{param:n,category:r}=e;if(t[n]){let e=t[n].split(",");a=[...a,...e.map(e=>({category:r,label:e.trim()}))]}}),t.hasOwnProperty("list")&&et("Yes"===t.list),Y(a),t.pageSize=10,T(t),n&&window.scrollTo({top:r,behavior:"instant"})},t=e=>{let{category:t,newValues:n}=e.detail;"keyWord"===t?D(Array.isArray(n)&&n.length>0?n.join(","):""):"country"===t&&Z(Array.isArray(n)&&n.length>0?n.join(","):"")};return window.addEventListener("filterChanged",e),window.addEventListener("checkboxFilterChanged",t),()=>{window.removeEventListener("filterChanged",e),window.removeEventListener("checkboxFilterChanged",t)}},[T,Y,D,Z]);let ea={industry:r?.industry&&r.industry.split(",").map(e=>e.trim()).length>0?decodeURIComponent(r?.industry).split(","):"",contractType:r?.contractType&&r.contractType.split(",").map(e=>e.trim()).length>0?decodeURIComponent(r?.contractType).split(","):"",jobDescriptionLanguages:r?.jobDescriptionLanguages&&r.jobDescriptionLanguages.split(",").map(e=>e.trim()).length>0?decodeURIComponent(r?.jobDescriptionLanguages).split(","):"",levelOfExperience:r?.levelOfExperience&&r.levelOfExperience.split(",").map(e=>e.trim()).length>0?decodeURIComponent(r?.levelOfExperience).split(","):"",keyWord:r?.keyWord&&r.keyWord.split(",").map(e=>e.trim()).length>0?decodeURIComponent(r?.keyWord).split(","):"",country:r?.country&&r.country.split(",").map(e=>e.trim()).length>0?decodeURIComponent(r?.country).split(","):""};return(0,l.jsxs)(l.Fragment,{children:[s&&(0,l.jsx)("div",{id:"search-bar-opportunities",children:(0,l.jsxs)(c.default,{className:"custom-max-width",children:[(0,l.jsx)("p",{className:"sub-heading text-banking",children:b("global:findCareer")}),!E&&(0,l.jsx)(C,{keyWord:W,country:I,countries:a,handleSearchChange:H,setCountry:Z,resetSearch:e=>G(e,{preserveIndustry:h,industryName:f,preserveCountry:v,countryName:y}),handleSearchClick:q,setPageNumber:F,jobLocation:v,jobIndustry:h,industryName:f,countryName:y,t:b}),(0,l.jsx)(S,{selectedFilters:U})]})}),(0,l.jsx)("div",{id:"opportunities",children:(0,l.jsxs)(c.default,{className:"custom-max-width",children:[(0,l.jsxs)("div",{className:"display",children:[(0,l.jsxs)("div",{className:"opportunity-chip",children:[(0,l.jsxs)("p",{className:"sub-heading text-banking",children:["Opportunities"," ",(0,l.jsx)("span",{className:"opportunities-nbr",children:O})]})," "]}),!E&&(0,l.jsxs)("div",{className:"grid-list-buttons",children:[(0,l.jsx)(j.default,{icon:(0,l.jsx)(ee,{}),className:`btn btn-ghost ${X?"":"active"}`,onClick:()=>er(!1)}),(0,l.jsx)(j.default,{icon:(0,l.jsx)(en,{}),className:`btn btn-ghost ${X?"active":""}`,onClick:()=>er(!0)})]})]}),(0,l.jsxs)(d.default,{className:"container opportunity-card",container:!0,columnSpacing:2,children:[(0,l.jsx)(K,{initialValues:ea,isFilterOpen:B,setIsFilterOpen:V,t:b,jobLocation:v,jobIndustry:h,countries:a,setSelectedFilters:Y,setPageNumber:F,handleSubmitFilter:(e,t)=>{let{setFieldValue:n}=t,r=new URLSearchParams(Q);e.industry?.length&&(r.set("industry",e.industry.join(",")),z(e.industry.join(","))),e.contractType?.length&&(r.set("contractType",e.contractType.join(",")),_(e.contractType.join(","))),e.levelOfExperience?.length&&(r.set("levelOfExperience",e.levelOfExperience.join(",")),M(e.levelOfExperience.join(","))),e.jobDescriptionLanguages?.length&&(r.set("jobDescriptionLanguages",e.jobDescriptionLanguages.join(",")),$(e.jobDescriptionLanguages.join(","))),e.keyWord?.length&&(r.set("keyWord",e.keyWord.join(",")),D(e.keyWord.join(","))),e.country&&(r.set("country",e.country),Z(e.country));let a=window.scrollY||document.documentElement.scrollTop,s=`${window.location.pathname}?${r.toString()}`;window.history.replaceState({path:s},"",s);let l={};for(let[e,t]of r.entries())l[e]=t;T(l);let i=[];e.industry?.length&&i.push(...e.industry.map(e=>({category:"industry",label:e}))),e.contractType?.length&&i.push(...e.contractType.map(e=>({category:"contractType",label:e}))),e.levelOfExperience?.length&&i.push(...e.levelOfExperience.map(e=>({category:"levelOfExperience",label:e}))),e.jobDescriptionLanguages?.length&&i.push(...e.jobDescriptionLanguages.map(e=>({category:"jobDescriptionLanguages",label:e}))),e.country&&i.push(...e.country.map(e=>({category:"country",label:e}))),e.keyWord&&i.push(...e.keyWord.map(e=>({category:"keyWord",label:e}))),Y(i),n("filters",i.map(e=>e.label)),window.scrollTo({top:a,behavior:"instant"})}}),(0,l.jsx)(P,{opportunitiesData:k,language:t,pageNumber:R,handlePageChange:J,searchParamsContent:Q,t:b,isList:X,isLoading:N})]})]})})]})}}}]);