exports.id=6756,exports.ids=[6756],exports.modules={5551:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var a,n=s(95746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let i=e=>n.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:40,height:41,fill:"none"},e),a||(a=n.createElement("path",{fill:"#fff",d:"m18.047 20.501 8.25 8.25-2.357 2.357L13.333 20.5 23.94 9.895l2.357 2.357z"})))},58996:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var a,n=s(95746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let i=e=>n.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:40,height:41,fill:"none"},e),a||(a=n.createElement("path",{fill:"#fff",d:"m21.953 20.499-8.25-8.25 2.357-2.357L26.667 20.5 16.06 31.106l-2.357-2.358z"})))},3243:(e,t,s)=>{Promise.resolve().then(s.bind(s,31190)),Promise.resolve().then(s.bind(s,15968)),Promise.resolve().then(s.bind(s,81373)),Promise.resolve().then(s.bind(s,25788))},15968:(e,t,s)=>{"use strict";s.d(t,{default:()=>N});var a=s(10326),n=s(17577),r=s(90434),i=s(5551),o=s(58996),l=s(15082),c=s(35047),u=s(83708),d=s(52210),p=s(28236),m=s(33198),h=s(20026);let g={src:"/_next/static/media/new-logo-dark.566c90ae.png"},y=function({menuList:e,isOpen:t,toggleSidebar:s,locale:n}){let y=(0,c.usePathname)(),{t:f}=(0,d.$G)(),{data:A}=(0,h.iQ)(f);return(0,a.jsxs)("div",{id:"sidebar-component",className:`${t?"toggled":""}`,children:[t?a.jsx(l.default,{icon:a.jsx("span",{className:"menu-icon",children:a.jsx(o.Z,{})}),onClick:s,className:"btn btn-ghost toggle-menu"}):(0,a.jsxs)("div",{className:"pentabell-logo",children:[a.jsx("img",{src:g.src,alt:"Pentabell logo",loading:"lazy",onClick:()=>window.location.href=(0,p.jJ)(n,"")}),a.jsx(l.default,{icon:a.jsx("span",{className:"menu-icon",children:a.jsx(i.Z,{})}),onClick:s,className:"btn btn-ghost toggle-menu"})]}),(0,a.jsxs)("div",{id:"sidebar-menu",children:[a.jsx("div",{className:"menu-main",children:e?.map(e=>{let t=p.jJ(n,e.route);return a.jsxs(r.default,{className:`nav-link ${y===t?"active":""}`,locale:"en"===n?"en":"fr",href:t,children:[a.jsx(u.Z,{title:e.i18nName?f(e.i18nName):e.name,placement:"right-start",componentsProps:{tooltip:{sx:{color:"#798BA3",backgroundColor:"white",fontWeight:"bold",fontSize:"0.8em"}}},children:a.jsx("span",{className:"menu-icon",children:e.svgIcon})}),a.jsx("span",{className:"menu-title",children:e.i18nName?f(e.i18nName):e.name})]},e.key)})}),(0,a.jsxs)("div",{className:"nav-link",children:[a.jsx(m.Z,{...(0,p.GZ)(A?.firstName+" "+A?.lastName),alt:A?.firstName,src:`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${A?.profilePicture}`,className:"menu-icon avatar"}),!t&&a.jsx("span",{className:"menu-title",children:(e=>{let t=e.split(" ");return t?.length>2?t.slice(0,2).join(" ")+"...":e})(A?.firstName+" "+A?.lastName)})]})]})]})};var f=s(22304),A=s(23743),x=s(88441),b=s(90423),v=s(4230),j=s(20325),w=s(67247);let $=function({resources:e,locale:t}){let{user:s}=(0,f.Z)(!1);return a.jsx("div",{id:"dashboard-header",children:s?(0,a.jsxs)(a.Fragment,{children:[a.jsx(v.Z,{locale:t}),a.jsx(j.Z,{locale:t,disableIconArrow:!0}),a.jsx(w.Z,{withFlag:!0,onlyWebVersion:!0})," "]}):null})},P=function({isOpen:e,children:t}){return(0,a.jsxs)(b.default,{maxWidth:!1,className:`${e?"toggled":""}`,id:"main-component",children:[a.jsx($,{}),a.jsx("main",{id:"outlet",children:t})]})},E=function(){return a.jsx("center",{id:"dashboard-footer",children:(0,a.jsxs)("p",{className:"paragraph",children:["\xa9 ",a.jsx("a",{href:"https://www.pentabell.com/",children:"Pentabell"})," ",new Date().getFullYear(),", All rights reserved"]})})},N=function({children:e,locale:t}){let[s,r]=(0,n.useState)(!1),[i,o]=(0,n.useState)(),l=(0,A.Z)();(0,x.Z)(l.breakpoints.down("sm")),(0,x.Z)(l.breakpoints.down("md"));let{user:c}=(0,f.Z)();return(0,a.jsxs)("div",{id:"dashboard-layout",className:"toggled",children:[a.jsx(y,{menuList:(0,p.TP)(i),isOpen:s,toggleSidebar:()=>{r(!s)},locale:t}),a.jsx(P,{isOpen:s,children:e}),a.jsx(E,{})]})}},86184:(e,t,s)=>{"use strict";s.d(t,{$i:()=>g,BF:()=>h,Fe:()=>i,Gc:()=>u,HF:()=>r,Hr:()=>l,IZ:()=>m,NF:()=>c,PM:()=>o,UJ:()=>d,jd:()=>p});var a=s(2994),n=s(21464);s(35047),s(97980);let r=()=>(0,a.useMutation)({mutationFn:e=>(0,n.W3)(e),onError:e=>{e.message=""}}),i=e=>(0,a.useQuery)("opportunities",async()=>await (0,n.fH)(e)),o=()=>(0,a.useMutation)(()=>(0,n.AE)()),l=e=>(0,a.useQuery)(["opportunities",e],async()=>await (0,n.Mq)(e)),c=()=>(0,a.useMutation)({mutationFn:(e,t,s)=>(0,n.rE)(e,t,s),onError:e=>{e.message=""}}),u=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t)=>(0,n.S1)(e,t),onError:e=>{e.message=""}})),d=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t,s)=>(0,n.lU)(e,t,s),onError:e=>{e.message=""}})),p=()=>{let e=(0,a.useQueryClient)();return(0,a.useMutation)({mutationFn:(e,t,s,a)=>(0,n.yH)(e,t,s,a),onSuccess:t=>{e.invalidateQueries("files")}})},m=()=>(0,a.useQuery)("SeoOpportunities",async()=>await (0,n.yJ)()),h=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:(e,t)=>(0,n.mt)(e,t),onError:e=>{e.message=""}})),g=()=>((0,a.useQueryClient)(),(0,a.useMutation)({mutationFn:({language:e,id:t,archive:s})=>(0,n.TK)(e,t,s),onError:e=>{console.error("Error during mutation",e),e.message=""}}))},21464:(e,t,s)=>{"use strict";s.d(t,{AE:()=>c,Mq:()=>l,S1:()=>d,TK:()=>g,W3:()=>i,fH:()=>o,lU:()=>p,mt:()=>y,rE:()=>u,yH:()=>m,yJ:()=>h});var a=s(50967),n=s(70580),r=s(31190);let i=e=>(e.t,new Promise(async(t,s)=>{n.yX.post(`/opportunities${a.Y.applications}/${e.opportunityId}`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&s(e)})})),o=e=>new Promise(async(t,s)=>{try{let s=await n.yX.get(`${a.Y.opportunity}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyWord:e.keyWord,visibility:e.visibility,title:e.title,createdAt:e.createdAt,publishDate:e.publishDate,country:e.country,industry:e.industry,contractType:e.contractType,minExperience:e.minExperience,maxExperience:e.maxExperience,jobDescriptionLanguages:e.jobDescriptionLanguages,reference:e.reference,opportunityType:e.opportunityType,exclude:"In House"!==e.opportunityType&&"true"}});t(s.data)}catch(e){s(e)}}),l=e=>new Promise(async(t,s)=>{try{let s=await n.yX.get(`${a.Y.opportunity}/${e}`);t(s.data)}catch(e){s(e)}}),c=async()=>(await n.xk.put("/UpdateJobdescription")).data,u=({data:e,language:t,id:s})=>new Promise(async(i,o)=>{n.yX.post(`${a.Y.opportunity}/${t}/${s}`,e).then(e=>{"en"===t&&r.Am.success("Opportunity english updated successfully"),"fr"===t&&r.Am.success("Opportunity french updated successfully"),e?.data&&i(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&o(e)})}),d=({data:e,id:t})=>new Promise(async(s,i)=>{n.yX.put(`${a.Y.opportunity}/${t}`,e).then(e=>{r.Am.success("Opportunity Commun fields updated successfully"),e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})}),p=({id:e,title:t,typeOfFavourite:s})=>new Promise(async(i,o)=>{n.yX.put(`${a.Y.baseUrl}/favourite/${e}`,{type:s}).then(e=>{r.Am.success(`${s} : ${t} saved to your favorites.`),e?.data&&i(e.data)}).catch(e=>{e&&e.response&&e.response.data&&409===e.response.status&&r.Am.warning(` ${t} already in shortlist`),e&&o(e)})}),m=({resource:e,folder:t,filename:s,body:i})=>new Promise(async(o,l)=>{n.cU.post(`${a.Y.files}/uploadResume/${e}/${t}/${s}`,i.formData).then(e=>{e.data&&o(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(400===e.response.status?e.response.data.message.includes("The resume lacks essential information")?r.Am.warn(i.t("messages:requireResume")):r.Am.warn(e.response.data.message):500===e.response.status&&r.Am.error("Internal Server Error")),e&&l(e)})}),h=()=>new Promise(async(e,t)=>{try{let t=await n.yX.get(`${a.Y.seoOpportunity}`);e(t.data)}catch(e){t(e)}}),g=(e,t,s)=>new Promise(async(i,o)=>{try{let o=await n.yX.put(`${a.Y.opportunity}/${e}/${t}/desarchiver`,{archive:s});o?.data&&(r.Am.success(`opportunity ${s?"archived":"desarchived"} successfully`),i(o.data))}catch(e){r.Am.error(`Failed to ${s?"archive":"desarchive"} the opportunity.`),o(e)}}),y=({data:e,id:t})=>new Promise(async(s,i)=>{n.yX.put(`${a.Y.seoOpportunity}/${t}`,e).then(e=>{r.Am.success("Opportunity seo updated successfully"),e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})})},75545:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>u});var a=s(19510),n=s(96557),r=s(18177),i=s(80283),o=s(55367),l=s(43207);s(97001);let c=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\layouts\Dashboard.jsx#default`),u={title:"Dashboard",robots:"noindex, nofollow"},d=async({children:e,params:{locale:t}})=>{let{resources:s}=await (0,l.Z)(t,["personalinformation","ProfessionalInformations","global","listopportunity","application","statisticsDash","statisticsApp","messages","validations","HomeDashboard","listCategory","listArticle","guides","listCommentaire","createArticle","sliders","createOpportunity","experience","education","certification","listusers","menu","comments","footer","register","settings","validations","sidebar","favourite","resumes","contact","getInTouch","statsTotalNumbers","seoSettings","statsDash","steps","eventForm","event","country"]);return a.jsx("html",{lang:t,dir:(0,r.cp)(t),children:a.jsx("body",{children:(0,a.jsxs)(o.m,{children:[a.jsx(n.Ix,{}),a.jsx(i.Z,{namespaces:["personalinformation","ProfessionalInformations","global","listCategory","listArticle","HomeDashboard","listCommentaire","application","createArticle","sidebar","listusers","createOpportunity","statisticsDash","statisticsApp","messages","validations","experience","sliders","guides","comments","education","listopportunity","certification","menu","footer","register","settings"],locale:t,resources:s,children:a.jsx(c,{locale:t,children:e})})]})})})}},96940:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a={src:"/_next/static/media/add.7d9a0730.png",height:29,width:29,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEUjT5YfPJMjTpUjT5chT5UjTpckT5YkT5SyXEFwAAAACHRSTlOKAQ+aI3pIMEwTVZQAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAvSURBVHicJYrJEQAgEIPIofbfsbOaFxPAi1lhS9IJSLbEwMIPnPQ/7VOd2GF6yL4OogBsGLyWkgAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8}}};