import HttpException from '@/utils/exceptions/http.exception';
import { Response, NextFunction } from 'express';
import UserModel from '../apis/user/user.model';

const isUserExist = async (request: any, response: Response, next: NextFunction) => {
    const id: string = request.user._id;
    const user = await UserModel.findById(id);

    request.user = user;
    if (!user) throw new HttpException(404, 'User Not Found');
    next();
};

export default isUserExist;
