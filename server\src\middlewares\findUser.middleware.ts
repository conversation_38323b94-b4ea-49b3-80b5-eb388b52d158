import HttpException from '@/utils/exceptions/http.exception';
import { Response, NextFunction } from 'express';
import UserModel from '../apis/user/user.model';
import { MESSAGES } from '@/utils/helpers/messages';

const isUserExist = async (request: any, response: Response, next: NextFunction) => {
    const id: string = request.user._id;
    const user = await UserModel.findById(id);

    request.user = user;
    if (!user) throw new HttpException(404, MESSAGES.USER.USER_NOT_FOUND);
    next();
};

export default isUserExist;
