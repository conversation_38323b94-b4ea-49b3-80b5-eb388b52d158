"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8166],{18166:function(e){e.exports=JSON.parse('{"invalidEmail":"Email invalide","emptyField":"Veuillez remplir ce champ obligatoire !","endDate":"La date de fin doit \xeatre apr\xe8s la date de d\xe9but","minDate":"La date de naissance doit \xeatre apr\xe8s 1950","maxDate":"La date doit \xeatre avant 2005","minLength":"Le champ doit comporter au moins 3 caract\xe8res","maxLength":"Le champ doit comporter au plus 20 caract\xe8res","required":"Ce champ est requis !","invalidPassword":"Le mot de passe doit comporter au moins une lettre majuscule, une lettre minuscule, un chiffre et un caract\xe8re sp\xe9cial","passwordMatch":"Les mots de passe doivent correspondre","minOne":"Au moins une comp\xe9tence est requise","minNationality":"Au moins une nationalit\xe9 est requise","minRoles":"Au moins un role est requis","phoneFormat":"Format de num\xe9ro de t\xe9l\xe9phone invalide","companyEmailRequired":"Veuillez utiliser votre adresse e-mail professionnelle"}')}}]);