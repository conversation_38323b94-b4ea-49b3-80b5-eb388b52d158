"use client";
import {
  Container,
  Grid,
  AccordionSummary,
  AccordionDetails,
  Accordion,
} from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import SvgexpandIcon from "../../assets/images/icons/arrowUp.svg";

import SvgCallendar from "@/assets/images/icons/yellow/yellowCalendar.svg";

export default function QatarLaborLaws() {
  const [expanded, setExpanded] = useState(1);
  const { t } = useTranslation();
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  return (
    <div id="labor-tn-laws" className="custom-max-width">
      <Container>
        <h2 className="heading-h1">{t("qatar:QatarLabor:title")}</h2>

        <Grid className="container" container columnSpacing={3} rowSpacing={2}>
          <Grid item xs={12} sm={12} key={1}>
            <Accordion
              elevation={0}
              expanded={expanded === 1}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(1)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("qatar:QatarLabor:workingHours:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("qatar:QatarLabor:workingHours:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:workingHours:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("qatar:QatarLabor:workingHours:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:workingHours:description2")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={2}>
            <Accordion
              elevation={0}
              expanded={expanded === 2}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(2)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("qatar:QatarLabor:employmentContracts:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:employmentContracts:description")}
                  </p>
                  <br />
                  <p className="service-sub-title">
                    {t("qatar:QatarLabor:employmentContracts:title1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("qatar:QatarLabor:employmentContracts:data1")}</li>
                    <li>{t("qatar:QatarLabor:employmentContracts:data2")}</li>
                    <li>{t("qatar:QatarLabor:employmentContracts:data3")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={3}>
            <Accordion
              elevation={0}
              expanded={expanded === 3}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(3)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("qatar:QatarLabor:termination:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:termination:description")}
                  </p>
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:termination:description1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("qatar:QatarLabor:termination:data1")}</li>
                    <li>{t("qatar:QatarLabor:termination:data2")}</li>
                    <li>{t("qatar:QatarLabor:termination:data3")}</li>
                    <li>{t("qatar:QatarLabor:termination:data4")}</li>
                    <li>{t("qatar:QatarLabor:termination:data5")}</li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("qatar:QatarLabor:termination:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:termination:description2")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("qatar:QatarLabor:termination:title3")}
                  </p>
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:termination:description3")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={4}>
            <Accordion
              elevation={0}
              expanded={expanded === 4}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(4)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("qatar:QatarLabor:payroll:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:payroll:description")}
                  </p>
                </div>
                <div className="payroll-tn">
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("qatar:QatarLabor:payroll:fiscalYear:title")}
                    </p>
                    <p className="date">
                      {t("qatar:QatarLabor:payroll:fiscalYear:date1")}
                      <br />
                      {t("qatar:QatarLabor:payroll:fiscalYear:date2")}
                    </p>
                    <p className="paragraph">
                      {t("qatar:QatarLabor:payroll:fiscalYear:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("qatar:QatarLabor:payroll:payrollCycle:title")}
                    </p>
                    <p className="date">
                      {t("qatar:QatarLabor:payroll:payrollCycle:date")}
                    </p>
                    <p className="paragraph">
                      {t("qatar:QatarLabor:payroll:payrollCycle:description")}
                    </p>
                  </div>

                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("qatar:QatarLabor:payroll:minimumWage:title")}
                    </p>
                    <p className="date">
                      {t("qatar:QatarLabor:payroll:minimumWage:wage")}
                      <br />
                      {t("qatar:QatarLabor:payroll:minimumWage:date")}​
                    </p>
                    <p className="paragraph">
                      {t("qatar:QatarLabor:payroll:minimumWage:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("qatar:QatarLabor:payroll:payrollManagement:title")}
                    </p>
                    <p className="date">
                      {t("qatar:QatarLabor:payroll:payrollManagement:date1")}
                      <br />
                      {t("qatar:QatarLabor:payroll:payrollManagement:date2")}
                    </p>
                    <p className="paragraph">
                      {t(
                        "qatar:QatarLabor:payroll:payrollManagement:description"
                      )}
                    </p>
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={5}>
            <Accordion
              elevation={0}
              expanded={expanded === 5}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(5)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("qatar:QatarLabor:leaveEntitlements:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:leaveEntitlements:description")}
                  </p>
                  <br />
                  <p className="service-sub-title">
                    {t("qatar:QatarLabor:leaveEntitlements:subTitle")}
                  </p>
                  <div className="holidays-dates">
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "qatar:QatarLabor:leaveEntitlements:leaves:dataS1:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "qatar:QatarLabor:leaveEntitlements:leaves:dataS1:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "qatar:QatarLabor:leaveEntitlements:leaves:dataS2:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "qatar:QatarLabor:leaveEntitlements:leaves:dataS2:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "qatar:QatarLabor:leaveEntitlements:leaves:dataS3:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "qatar:QatarLabor:leaveEntitlements:leaves:dataS3:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "qatar:QatarLabor:leaveEntitlements:leaves:dataS4:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "qatar:QatarLabor:leaveEntitlements:leaves:dataS4:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "qatar:QatarLabor:leaveEntitlements:leaves:dataS5:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "qatar:QatarLabor:leaveEntitlements:leaves:dataS5:title"
                        )}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "qatar:QatarLabor:leaveEntitlements:leaves:maternityLeave:title"
                    )}
                  </p>
                  <p className="service-description paragraph">
                    {t(
                      "qatar:QatarLabor:leaveEntitlements:leaves:maternityLeave:description1"
                    )}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "qatar:QatarLabor:leaveEntitlements:leaves:sickLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "qatar:QatarLabor:leaveEntitlements:leaves:sickLeave:description"
                    )}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={6}>
            <Accordion
              elevation={0}
              expanded={expanded === 6}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(6)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("qatar:QatarLabor:tax:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item"></div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("qatar:QatarLabor:tax:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:tax:description1")}
                  </p>
                 
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("qatar:QatarLabor:tax:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:tax:description2")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>

          <Grid item xs={12} sm={12} key={7}>
            <Accordion
              elevation={0}
              expanded={expanded === 7}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(7)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("qatar:QatarLabor:visa:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:visa:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("qatar:QatarLabor:visa:title1")}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t("qatar:QatarLabor:visa:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:visa:description2")}
                  </p>
                  <br />
                  <p className="service-sub-title">
                    {t("qatar:QatarLabor:visa:title3")}
                  </p>
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:visa:description3")}
                  </p>
                </div>
                <div className="item">
                  <ul className="service-description paragraph">
                    <li>{t("qatar:QatarLabor:visa:data1")}</li>
                    <li>{t("qatar:QatarLabor:visa:data2")}</li>
                    <li>{t("qatar:QatarLabor:visa:data3")}</li>
                    <li>{t("qatar:QatarLabor:visa:data4")}</li>
                    <li>{t("qatar:QatarLabor:visa:data5")}</li>
                    <li>{t("qatar:QatarLabor:visa:data6")}</li>
                    <li>{t("qatar:QatarLabor:visa:data7")}</li>
                  </ul>
                  <p className="service-description paragraph">
                    {t("qatar:QatarLabor:visa:description4")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}
