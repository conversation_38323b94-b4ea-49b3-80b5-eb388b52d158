import { Container } from "@mui/material";

import { axiosGetJson } from "@/config/axios";
import initTranslations from "@/app/i18n";
import CustomButton from "@/components/ui/CustomButton";
import { websiteRoutesList } from "@/helpers/routesList";
import OpportunityItem from "./OpportunityComponents/OpportunityItem";

const LastOpportunitiesInHouse = async ({ locale }) => {
  const { t } = await initTranslations(locale, ["opportunities", "joinUs"]);

  try {
    const res = await axiosGetJson.get("/opportunities", {
      params: {
        pageSize: 6,
        pageNumber: 1,
        opportunityType: "In House",
      },
    });

    const initialOpportunities = res.data;
    return (
      <Container id="last-opportunities-inhouse-section">
        <h2 className="heading-h1 text-center">{t("joinUs:discover")}</h2>
        <div>
          {initialOpportunities?.opportunities?.map((opportunity) => (
            <OpportunityItem
              key={opportunity?._id}
              opportunity={opportunity}
              language={locale}
              isList
            />
          ))}
        </div>
        <CustomButton
          link={`/${websiteRoutesList.opportunities.route}/?opportunityType=In%20House`}
          text={t("joinUs:viewOffers")}
          className={"btn btn-filled view-more"}
          externalLink
        />
      </Container>
    );
  } catch (error) {
    console.log("error", error);
  }
};

export default LastOpportunitiesInHouse;
