"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_features_glossary_component_OptimizedGlossaryHeader_jsx"],{

/***/ "(app-pages-browser)/./src/features/glossary/component/OptimizedGlossaryHeader.jsx":
/*!*********************************************************************!*\
  !*** ./src/features/glossary/component/OptimizedGlossaryHeader.jsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n\n\n\n// Lazy load SVG icon\nconst SvgArrowRight = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_assets_images_icons_arrowRight_svg\").then(__webpack_require__.bind(__webpack_require__, /*! @/assets/images/icons/arrowRight.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowRight.svg\")), {\n    loadableGenerated: {\n        modules: [\n            \"features\\\\glossary\\\\component\\\\OptimizedGlossaryHeader.jsx -> \" + \"@/assets/images/icons/arrowRight.svg\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"w-4 h-4 bg-gray-300 inline-block\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossaryHeader.jsx\",\n            lineNumber: 6,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c = SvgArrowRight;\nconst OptimizedGlossaryHeader = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c1 = function OptimizedGlossaryHeader(param) {\n    let { language, glossaryPath, glossary } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-header\",\n        className: \"mb-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"custom-max-width\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glossary-path flex items-center text-sm text-gray-600 mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"\".concat(glossaryPath, \"/\"),\n                            className: \"link text-blue-600 hover:text-blue-800 transition-colors\",\n                            \"aria-label\": \"Go to \".concat(language === \"en\" ? \"Glossary\" : \"Glossaire\"),\n                            children: language === \"en\" ? \"Glossary\" : \"Glossaire\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossaryHeader.jsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        (glossary === null || glossary === void 0 ? void 0 : glossary.word) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SvgArrowRight, {\n                                    className: \"mx-2 w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossaryHeader.jsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"word font-medium text-gray-900\",\n                                    children: glossary.word\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossaryHeader.jsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossaryHeader.jsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                (glossary === null || glossary === void 0 ? void 0 : glossary.letter) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"letter text-4xl font-bold text-blue-600 mb-4\",\n                    children: glossary.letter\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossaryHeader.jsx\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossaryHeader.jsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedGlossaryHeader.jsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n});\n_c2 = OptimizedGlossaryHeader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (OptimizedGlossaryHeader);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"SvgArrowRight\");\n$RefreshReg$(_c1, \"OptimizedGlossaryHeader$memo\");\n$RefreshReg$(_c2, \"OptimizedGlossaryHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/OptimizedGlossaryHeader.jsx\n"));

/***/ })

}]);