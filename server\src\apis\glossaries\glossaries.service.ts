import { Language, Role, Visibility } from '@/utils/helpers/constants';
import glossaryModel from './glossaries.model';
import HttpException from '@/utils/exceptions/http.exception';
import { GlossaryI, GlossaryVersionFields } from './glossaries.interface';
import { UserI } from '../user/user.interfaces';
import { GlossaryQuery } from 'types/request/GlossaryQuery';

export class GlossaryService {
    private slugify(text: string): string {
        return text?.toLowerCase().trim().replace(/\s+/g, '-');
    }

    private async getExistingUrls(excludeId?: string): Promise<Set<string>> {
        const filter = excludeId ? { _id: { $ne: excludeId } } : {};
        const glossaries = await glossaryModel.find(filter);

        const urls = new Set<string>();

        glossaries.forEach(glossary => {
            if (glossary.versions instanceof Map) {
                for (const version of glossary.versions.values()) {
                    if (version?.url) {
                        urls.add(version.url);
                    }
                }
            }
        });

        return urls;
    }

    public async createGlossary(glossaryData: GlossaryI, currentUser: UserI): Promise<any> {
        const existingUrls = await this.getExistingUrls();

        if (!glossaryData.versions || Object.keys(glossaryData.versions).length === 0) {
            throw new HttpException(400, 'At least one language version is required');
        }

        if (!glossaryData.versions) {
            glossaryData.versions = {};
        }

        Object.entries(glossaryData.versions).forEach(([language, version]) => {
            if (existingUrls.has(version.url)) {
                throw new HttpException(409, `${language.toUpperCase()} ${version.url} URL already exists`);
            }

            version.url = version.url || this.slugify(version.word);

            version.createdBy = currentUser;
            version.updatedBy = currentUser;
            version.isArchived = false;
            version.createdAt = new Date();
            version.updatedAt = new Date();
            version.language = language as Language;
        });

        const newGlossary = new glossaryModel(glossaryData);
        return await newGlossary.save();
    }

    public async importJsonGlossaries(file: any, currentUser: UserI) {
        if (!file) {
            throw new HttpException(400, 'No file uploaded');
        }

        const jsonData = JSON.parse(file.buffer.toString());
        const createdEntries = [];

        if (!Array.isArray(jsonData.glossariesList)) {
            throw new HttpException(400, 'Invalid JSON structure. Expected "glossariesList" as an array.');
        }

        const existingUrls = await this.getExistingUrls();

        for (const entry of jsonData.glossariesList) {
            const glossaryData: GlossaryI = { ...entry };

            if (!glossaryData.versions || Object.keys(glossaryData.versions).length === 0) continue;

            let shouldSkip = false;

            for (const [language, version] of Object.entries(glossaryData.versions)) {
                const finalUrl = version.url || this.slugify(version.word);

                if (existingUrls.has(finalUrl)) {
                    console.log(`URL '${finalUrl}' already exists. Skipping the entire entry...`);
                    shouldSkip = true;
                    break;
                }
            }

            if (shouldSkip) continue;

            for (const [language, version] of Object.entries(glossaryData.versions)) {
                const finalUrl = version.url || this.slugify(version.word);
                version.url = finalUrl;
                version.language = language === 'en' ? Language.ENGLISH : Language.FRENCH;
                version.createdBy = currentUser;
                version.updatedBy = currentUser;
                version.isArchived = false;
                version.createdAt = new Date();
                version.updatedAt = new Date();

                existingUrls.add(finalUrl);
            }

            const newGlossary = new glossaryModel(glossaryData);
            try {
                await newGlossary.save();
                createdEntries.push(newGlossary);
            } catch (err) {
                console.error(`Failed to save glossary with word "${Object.values(glossaryData.versions)[0].word}":`, err);
            }
        }

        return createdEntries;
    }

    public async getGlossaries(queries: GlossaryQuery, language: Language, currentUser: UserI): Promise<any> {
        const {
            paginated = 'true',
            searchQuery,
            sortOrder = 'asc',
            sortBy = 'createdAt',
            letter,
            word,
            visibility,
            isArchived,
            createdAt,
            pageNumber = 1,
            pageSize = 5,
            dashboard = 'true',
        } = queries;

        const langKey = language.toLowerCase();

        const hasRole = [Role.ADMIN, Role.EDITEUR].some(role => currentUser?.roles?.includes(role));

        const queryConditions: any = {
            [`versions.${langKey}`]: { $exists: true },
        };

        queryConditions[`versions.${langKey}.isArchived`] = isArchived && hasRole ? true : false;

        if (letter) queryConditions[`versions.${langKey}.letter`] = letter;

        if (word) queryConditions[`versions.${langKey}.word`] = new RegExp(`.*${word}.*`, 'i');

        if (searchQuery) {
            const regex = new RegExp(`.*${searchQuery}.*`, 'i');
            queryConditions['$or'] = searchQuery.includes('https://')
                ? [{ [`versions.${langKey}.content`]: regex }]
                : [
                      { [`versions.${langKey}.word`]: regex },
                      { [`versions.${langKey}.metaTitle`]: regex },
                      { [`versions.${langKey}.metaDescription`]: regex },
                  ];
        }

        if (visibility) {
            queryConditions[`versions.${langKey}.visibility`] = visibility;
        }

        if (createdAt) {
            const date = new Date(createdAt);
            const startOfDay = new Date(date);
            startOfDay.setHours(0, 0, 0, 0);

            const endOfDay = new Date(date);
            endOfDay.setHours(23, 59, 59, 999);

            queryConditions[`versions.${langKey}.createdAt`] = {
                $gte: startOfDay,
                $lte: endOfDay,
            };
        }

        let sortField: string;

        switch (sortBy) {
            case 'alphabetical':
                sortField = `versions.${langKey}.word`;
                break;
            case 'createdAt':
            default:
                sortField = `versions.${langKey}.createdAt`;
                break;
        }

        const sortCriteria: any = { [sortField]: sortOrder === 'asc' ? 1 : -1 };

        const skip = (Number(pageNumber) - 1) * Number(pageSize);

        try {
            const glossariesQuery = glossaryModel
                .find(queryConditions)
                .populate(`versions.${langKey}.createdBy`, 'firstName lastName')
                .populate(`versions.${langKey}.updatedBy`, 'firstName lastName')
                .sort(sortCriteria);

            if (paginated !== 'false' && dashboard !== 'false') {
                glossariesQuery.skip(skip).limit(Number(pageSize));
            }

            const glossaries = await glossariesQuery.lean();

            if (!glossaries.length) {
                return paginated === 'false' || dashboard === 'false'
                    ? []
                    : {
                          pageNumber,
                          pageSize,
                          totalPages: 0,
                          totalGlossaries: 0,
                          glossaries: [],
                      };
            }

            const transformedGlossaries =
                dashboard === 'true'
                    ? glossaries.map(glossary => {
                          const existingLanguages = Object.keys(glossary.versions || {}).map(lang =>
                              lang === 'en' ? Language.ENGLISH : Language.FRENCH,
                          );

                          const versions = glossary.versions?.[langKey];

                          return {
                              _id: glossary._id,
                              robotsMeta: glossary.robotsMeta,
                              existingLanguages,
                              versions: {
                                  ...versions,
                                  language,
                              },
                          };
                      })
                    : glossaries.reduce((acc: any, entry: any) => {
                          const letter = entry.versions?.[langKey]?.letter.toUpperCase();
                          const word = entry.versions?.[langKey]?.word;
                          if (!acc[letter]) {
                              acc[letter] = [];
                          }
                          acc[letter].push({ word, url: entry.versions?.[langKey]?.url });
                          return acc;
                      }, {} as any);

            if (paginated === 'false' || dashboard === 'false') {
                return transformedGlossaries;
            }

            const totalGlossaries = await glossaryModel.countDocuments(queryConditions);
            const totalPages = Math.ceil(totalGlossaries / Number(pageSize));

            return {
                pageNumber,
                pageSize,
                totalPages,
                totalGlossaries,
                glossaries: transformedGlossaries,
            };
        } catch (error) {
            console.error('Error fetching glossaries:', error);
            throw error;
        }
    }

    public async getSlugBySlug(language: string, url: string): Promise<{ slug: string }> {
        const langKey = language.toLowerCase();
        const targetLanguage = langKey === 'en' ? 'fr' : 'en';

        const glossary: any = await glossaryModel.findOne({
            [`versions.${langKey}.url`]: url.toLowerCase(),
            [`versions.${langKey}`]: { $exists: true },
        });

        if (!glossary) throw new HttpException(404, 'No glossary found');

        const version = glossary.versions?.get(targetLanguage);

        if (!version) throw new HttpException(404, 'No glossary version found');

        return {
            slug: version.url,
        };
    }

    public async get(glossaryId: string): Promise<any> {
        const glossary: GlossaryI | null = await glossaryModel.findById(glossaryId).lean();
        if (!glossary) throw new HttpException(404, 'Glossary Not found ');
        return glossary;
    }

    public async getGlossaryByLanguageAndId(language: string, id: string): Promise<any> {
        const langKey = language.toLowerCase();

        const glossary: any = await glossaryModel
            .findById(id)
            .populate(`versions.${langKey}.createdBy`, 'firstName lastName')
            .populate(`versions.${langKey}.updatedBy`, 'firstName lastName')
            .lean();

        if (!glossary) throw new HttpException(404, 'Glossary not found');

        if (!glossary.versions?.[langKey]) {
            throw new HttpException(404, `No ${language} version found for glossary with ID ${id}`);
        }

        const version = glossary.versions.get(langKey);

        return {
            _id: glossary._id,
            ...version,
            language,
            robotsMeta: glossary.robotsMeta,
        };
    }

    public async getGlossaryByUrl(language: string, url: string, currentUser: UserI): Promise<any> {
        const langKey = language.toLowerCase();

        const glossary = await glossaryModel
            .findOne({
                [`versions.${langKey}.url`]: url.toLowerCase(),
                [`versions.${langKey}`]: { $exists: true },
            })
            .populate(`versions.${langKey}.createdBy`, 'firstName lastName')
            .populate(`versions.${langKey}.updatedBy`, 'firstName lastName')
            .lean();

        if (!glossary) throw new HttpException(404, 'No glossary found');

        const version = glossary.versions?.[langKey];

        if (!version) throw new HttpException(404, 'No glossary version found');

        const hasRole = currentUser?.roles?.includes(Role.ADMIN) || currentUser?.roles?.includes(Role.EDITEUR);

        if (
            (version.visibility !== Visibility.Public && (!currentUser || currentUser?.roles?.includes(Role.CANDIDATE))) ||
            (version.isArchived === true && !hasRole)
        ) {
            throw new HttpException(404, 'No glossary found');
        }

        return {
            _id: glossary._id,
            ...version,
            language,
            robotsMeta: glossary.robotsMeta,
        };
    }

    public async updateGlossaryByLanguageAndId(glossaryId: string, updateData: Record<string, any>, currentUser: UserI): Promise<any> {
        const glossary: any = await glossaryModel.findById(glossaryId);
        if (!glossary) throw new HttpException(404, `No glossary found with ID ${glossaryId}.`);

        if (!glossary.versions || !(glossary.versions instanceof Map)) {
            glossary.versions = new Map<string, any>();
        }

        let versions: Record<string, any> = {};

        if (updateData.versions) {
            versions = updateData.versions;
        } else if (typeof updateData === 'object' && updateData.language && typeof updateData.language === 'string') {
            const langKey = updateData.language.toLowerCase();
            versions[langKey] = updateData;
        } else if (Object.keys(updateData).some(k => ['en', 'fr'].includes(k))) {
            versions = updateData;
        } else {
            throw new HttpException(400, 'Invalid glossary version update structure');
        }

        for (const [langKeyRaw, versionDataRaw] of Object.entries(versions)) {
            if (langKeyRaw === 'robotsMeta') {
                glossary.robotsMeta = versionDataRaw;
                continue;
            }

            const langKey = langKeyRaw.toLowerCase();
            const versionData = versionDataRaw as Partial<GlossaryVersionFields>;
            const existingVersion = glossary.versions.get(langKey);
            const now = new Date();

            if (!existingVersion) {
                const baseWord = versionData.word || '';
                const url = versionData.url || this.slugify(baseWord);

                const urlExists = await glossaryModel.exists({
                    _id: { $ne: glossaryId },
                    [`versions.${langKey}.url`]: url,
                });

                if (urlExists) {
                    throw new HttpException(409, `${langKey.toUpperCase()} URL '${url}' already exists in another glossary`);
                }

                glossary.versions.set(langKey, {
                    word: versionData.word || '',
                    letter: versionData.letter || '',
                    content: versionData.content || '',
                    metaTitle: versionData.metaTitle || '',
                    metaDescription: versionData.metaDescription || '',
                    url,
                    language: versionData.language || (langKey === 'en' ? Language.ENGLISH : Language.FRENCH),
                    visibility: versionData.visibility || Visibility.Draft,
                    isArchived: false,
                    createdAt: now,
                    updatedAt: now,
                    createdBy: currentUser._id,
                    updatedBy: currentUser._id,
                });
            } else {
                const urlChanged = versionData.url && versionData.url !== existingVersion.url;
                const wordChanged = versionData.word && versionData.word !== existingVersion.word;

                if (urlChanged) {
                    const urlExists = await glossaryModel.exists({
                        _id: { $ne: glossaryId },
                        [`versions.${langKey}.url`]: versionData.url,
                    });

                    if (urlExists) {
                        throw new HttpException(409, `${langKey.toUpperCase()} URL '${versionData.url}' already exists in another glossary`);
                    }
                } else if (wordChanged) {
                    const base = versionData.word || '';
                    const newUrl = this.slugify(base);

                    const urlExists = await glossaryModel.exists({
                        _id: { $ne: glossaryId },
                        [`versions.${langKey}.url`]: newUrl,
                    });

                    if (urlExists) {
                        throw new HttpException(409, `Generated URL '${newUrl}' from word '${base}' already exists in another glossary`);
                    }

                    versionData.url = newUrl;
                }

                Object.assign(existingVersion, versionData);
                existingVersion.updatedAt = now;
                existingVersion.updatedBy = currentUser._id;
                existingVersion.createdBy = currentUser._id;

                glossary.markModified(`versions.${langKey}`);
            }
        }

        if ('robotsMeta' in updateData) {
            glossary.robotsMeta = updateData.robotsMeta;
        }

        await glossary.save();
        return glossary;
    }

    public async updateGlossaryVersion(
        glossaryId: string,
        language: Language,
        versionData: Partial<GlossaryVersionFields>,
        currentUser: UserI,
    ): Promise<any> {
        const existingGlossary: any = await glossaryModel.findById(glossaryId);
        if (!existingGlossary) throw new HttpException(404, 'Glossary not found');

        const langKey = language.toLowerCase();
        const versionExists = existingGlossary.versions && !!existingGlossary.versions.get(langKey);

        if (versionExists) {
            const updateDataByLanguage = { [langKey]: versionData };
            return this.updateGlossaryByLanguageAndId(glossaryId, updateDataByLanguage, currentUser);
        }

        const base: string = versionData.url || versionData.word || '';
        const newUrl = this.slugify(base);

        const urlExists = await glossaryModel.exists({
            _id: { $ne: glossaryId },
            $or: [{ 'versions.en.url': newUrl }, { 'versions.fr.url': newUrl }],
        });

        if (urlExists) {
            throw new HttpException(409, `URL '${newUrl}' already exists in another glossary`);
        }

        const newVersionData = {
            word: versionData.word || '',
            letter: versionData.letter || '',
            content: versionData.content || '',
            metaTitle: versionData.metaTitle || '',
            metaDescription: versionData.metaDescription || '',
            url: newUrl,
            language,
            visibility: versionData.visibility || Visibility.Draft,
            isArchived: false,
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: currentUser,
            updatedBy: currentUser,
        };

        const updateData = { [langKey]: newVersionData };

        return this.updateGlossaryByLanguageAndId(glossaryId, updateData, currentUser);
    }

    public async archiveOrRestoreGlossaryByLanguageAndId(language: Language, glossaryId: string): Promise<any> {
        const glossary: any = await glossaryModel.findById(glossaryId);
        if (!glossary) throw new HttpException(404, `No glossary found with ID ${glossaryId}.`);

        const langKey = language.toLowerCase();

        if (!glossary.versions || !glossary.versions.get(langKey)) {
            throw new HttpException(404, `No ${language} version found for glossary ID ${glossaryId}.`);
        }

        glossary.versions.get(langKey).isArchived = !glossary.versions.get(langKey).isArchived;
        await glossary.save();

        return glossary.versions.get(langKey);
    }

    public async deleteGlossary(language: Language, glossaryId: string): Promise<any> {
        const glossary: any = await glossaryModel.findById(glossaryId);
        if (!glossary) throw new HttpException(404, `No glossary found with ID ${glossaryId}.`);

        const langKey = language.toLowerCase();

        if (!glossary.versions || !glossary.versions.get(langKey)) {
            throw new HttpException(404, `No ${language} version found for glossary ID ${glossaryId}.`);
        }

        glossary.versions.delete(langKey);

        if (Object.keys(glossary.versions).length === 0) {
            await glossaryModel.deleteOne({ _id: glossaryId });
            return { message: `Glossary with ID ${glossaryId} has been completely deleted.` };
        }

        await glossary.save();
        return glossary;
    }
}

export default GlossaryService;
