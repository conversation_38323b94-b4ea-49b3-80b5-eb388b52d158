"use client";
import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Grid, useTheme, useMediaQuery, FormGroup ,FormLabel,TextField,InputAdornment } from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import SvgRefreshIcon from "@/assets/images/icons/refreshIcon.svg";
import SvgSearchIcon from "@/assets/images/icons/searchIcon.svg";
import Loading from "@/components/loading/Loading";
import CustomButton from "@/components/ui/CustomButton";
import { useGetDownloadsReport } from "../hooks/downloadreport.hooks";
import { formatDate } from "../../../utils/functions";
const PAGINATION_KEY = "PAGINATION_KEY_DOWNLOADSREPORT";
const KEYWORD_KEY = "KeyWord_DOWNLOADSREPORT";

const ListDownloadsReport = () => {
  const { t } = useTranslation();

  const savedPagination =
    typeof window !== "undefined"
      ? localStorage.getItem(PAGINATION_KEY)
      : null;

  const savedKeyword =
    typeof window !== "undefined"
      ? localStorage.getItem(KEYWORD_KEY)
      : null;

  const [paginationModel, setPaginationModel] = useState(
    savedPagination
      ? JSON.parse(savedPagination)
      : {
          page: 0,
          pageSize: 10,
        }
  );

  const [searchValue, setSearchValue] = useState("");
  const [keyword, setKeyword] = useState(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem(KEYWORD_KEY);
      return saved ? JSON.parse(saved) : "";
    }
    return "";
  }); 

  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);



    const handleDateFromChange = (date) => {
    setFromDate(date);
  };
  const handleDateToChange = (date) => {
    setToDate(date);
  };
  const [modalOpen, setModalOpen] = useState(false);
const [modalDates, setModalDates] = useState([]);


  const getDownloads = useGetDownloadsReport({
    pageSize: paginationModel.pageSize,
    pageNumber: paginationModel.page + 1,
    keyWord,
  });

  useEffect(() => {
    getDownloads.refetch();
  }, [search, paginationModel]);

  const handleSearchClick = () => {
    setKeyWord(searchValue);
    localStorage.setItem(KEYWORD_KEY, JSON.stringify(searchValue));
    setSearch(!search);
  };
  const resetSearch = () => {
   
    setKeyWord("");
    setSearch(!search);
    setPaginationModel({ page: 0, pageSize: 10 });
 
    localStorage.setItem("KeyWord_DOWNLOADSREPORT", JSON.stringify(""));

  };
  
  const handlePaginationChange = (newModel) => {
    setPaginationModel(newModel);
    localStorage.setItem(PAGINATION_KEY, JSON.stringify(newModel));
  };

  const handleSearchChange = (e) => {
    setSearchValue(e.target.value);
  };
  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSearchClick();
    }
  };
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const rows =
    getDownloads?.data?.downloads?.map((item, index) => ({
      id: index,
      downloadId: item._id,
      email: item?.email,
      createdAt: formatDate(item.createdAt),
      fullName: item?.fullName,
      actions: item._id,
    })) || [];

  const columns = [
    {
      field: "email",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell hide-on-mobile",
      headerName: t("email"),
      flex: 1,
    },
    {
      field: "fullName",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell hide-on-mobile",
      headerName: t("full Name"),
      flex: 1,
    },
    {
        field: "createdAt",
        headerClassName: "datagrid-header",
        cellClassName: "datagrid-cell hide-on-mobile",
        headerName: t("Date"),
        valueFormatter: formatDate,
        flex: 1,
      },
  ];

  if (getDownloads.isLoading) {
    return <Loading />;
  }

  return (
    <>
      <div className="display-inline">
        <p className="heading-h2 semi-bold">
          {t("Report Downloads List")}
          <span className="opportunities-nbr">
            {getDownloads?.data?.totalDownloads}
          </span>
        </p>
      </div>

      <div id="container" className="recent-application-pentabell">
        <div className="main-content">
          <Grid container spacing={3}>
          <Grid item xs={12}>
              <Grid
                id="filter"
                container
                spacing={2}
                justifyContent="flex-start"
                alignItems="center"
              >
            <Grid item xs={12} sm={6}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("search With Email")}
                    </FormLabel>
                  </FormGroup>
                  <TextField
                    className="input-pentabell"
                    autoComplete="off"
                    slotProps={{
                      input: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <SvgSearchIcon />
                          </InputAdornment>
                        ),
                      },
                    }}
                    variant="standard"
                    type="text"
                    onKeyDown={handleKeyDown}
                    onChange={handleSearchChange}
                    value={searchValue}
                    placeholder={t("Search")}
                  />
                </Grid>

                <Grid item xs={12} sm={1} className="btns-filter dashboard">
                  <CustomButton
                    icon={<SvgRefreshIcon />}
                    className={"btn btn-outlined btn-refresh"}
                    onClick={resetSearch}
                  />
                </Grid>
                <Grid item xs={12} sm={2} className="btns-filter dashboard">
                  <CustomButton
                    text={t("comments:search")}
                    onClick={handleSearchClick}
                    className={"btn btn-filled"}
                  />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <div style={{ height: "100%", width: "100%" }}>
                <DataGrid
                  rows={rows}
                  columns={columns}
                  pageSize={paginationModel.pageSize}
                  pagination
                  paginationMode="server"
                  paginationModel={paginationModel}
                  onPaginationModelChange={handlePaginationChange}
                  pageSizeOptions={[5, 10, 25]}
                  rowCount={getDownloads?.data?.totalDownloads || 0}
                  autoHeight
                  disableSelectionOnClick
                  className="pentabell-table"
                />
              </div>
            </Grid>
          </Grid>
        </div>
      </div>
    </>
  );
};

export default ListDownloadsReport;
