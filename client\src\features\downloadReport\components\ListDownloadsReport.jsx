"use client";
import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Grid,
  useTheme,
  useMediaQuery,
  FormGroup,
  FormLabel,
  TextField,
  InputAdornment,
  Modal,
  Box,
  Typography,
  Button,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import SvgRefreshIcon from "@/assets/images/icons/refreshIcon.svg";
import SvgSearchIcon from "@/assets/images/icons/searchIcon.svg";
import Loading from "@/components/loading/Loading";
import CustomButton from "@/components/ui/CustomButton";
import { useGetDownloadsReport } from "../hooks/downloadreport.hooks";
import { formatDate, formatDatedownload } from "../../../utils/functions";
import DatesModal from "./DatesModal";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";

const PAGINATION_KEY = "PAGINATION_KEY_DOWNLOADSREPORT";
const KEYWORD_KEY = "KeyWord_DOWNLOADSREPORT";

const ListDownloadsReport = () => {
  const { t } = useTranslation();
  const theme = useTheme();

  const savedPagination =
    typeof window !== "undefined"
      ? localStorage.getItem(PAGINATION_KEY)
      : null;

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [paginationModel, setPaginationModel] = useState(
    savedPagination
      ? JSON.parse(savedPagination)
      : {
          page: 0,
          pageSize: 10,
        }
  );
  const [searchValue, setSearchValue] = useState("");
  const [keyword, setKeyword] = useState(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem(KEYWORD_KEY);
      return saved ? JSON.parse(saved) : "";
    }
    return "";
  }); 

  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);



    const handleDateFromChange = (date) => {
    setFromDate(date);
  };
  const handleDateToChange = (date) => {
    setToDate(date);
  };
  const [modalOpen, setModalOpen] = useState(false);
const [modalDates, setModalDates] = useState([]);


  const getDownloads = useGetDownloadsReport({
    pageSize: paginationModel.pageSize,
    pageNumber: paginationModel.page + 1,
    keyWord: keyword,
    fromDate,
    toDate
  });

  useEffect(() => {
    getDownloads.refetch();
  }, [keyword, paginationModel]);

  const handleSearchClick = () => {
    setKeyword(searchValue);
    localStorage.setItem(KEYWORD_KEY, JSON.stringify(searchValue));
  };

  const resetSearch = () => {
    setKeyword("");
    setSearchValue("");
    setPaginationModel({ page: 0, pageSize: 10 });
    setToDate(null);
    setFromDate(null);
    localStorage.setItem(KEYWORD_KEY, JSON.stringify(""));
    localStorage.setItem(PAGINATION_KEY, JSON.stringify({ page: 0, pageSize: 10 }));
  };

  const handlePaginationChange = (newModel) => {
    setPaginationModel(newModel);
    localStorage.setItem(PAGINATION_KEY, JSON.stringify(newModel));
  };

  const handleSearchChange = (e) => {
    setSearchValue(e.target.value);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") handleSearchClick();
  };

const handleOpenModal = (dates) => {
  setModalDates(dates);
  setModalOpen(true);
};


  const handleCloseModal = () => {
    setModalOpen(false);
    setModalDates([]);
  };

  const rows =
    getDownloads?.data?.downloads?.map((item, index) => ({
      id: index,
      downloadId: item._id,
      email: item.email,
      fullName: item.fullName,
      createdAt: formatDate(item.createdAt),
      downloadHistory: item.downloadHistory || [],
      
    })) || [];

  const columns = [
    {
      field: "email",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell hide-on-mobile",
      headerName: t("email"),
      flex: 1,
    },
    {
      field: "fullName",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell hide-on-mobile",
      headerName: t("full Name"),
      flex: 1,
    },
{
  field: "downloadHistory",
  headerClassName: "datagrid-header",
  cellClassName: "datagrid-cell",
  headerName: t("Download History"),
  flex: 2,
  renderCell: (params) => {
    const rawHistory = params.row.downloadHistory;
    const historyArray = Array.isArray(rawHistory) ? rawHistory : [];
    const formatted = historyArray.slice(0, 3).map(date => formatDatedownload(date));
    const display = formatted.join(", ");
    const suffix = historyArray.length > 3 ? (
      <button
        onClick={() => handleOpenModal(historyArray.map(date => formatDatedownload(date)))}
        style={{
          background: "none",
          border: "none",
          color: "#234791",
          cursor: "pointer",
          textDecoration: "underline",
          marginLeft: "15px",
          padding: 0,
          fontSize: "16px",
        }}
      >
         ... see more
      </button>
    ) : "";
    return <span>{display}{suffix}</span>;
  },
}
  ];

  if (getDownloads.isLoading) return <Loading />;

  return (
    <>
      <div className="display-inline">
        <p className="heading-h2 semi-bold">
          {t("Report Downloads List")}
          <span className="opportunities-nbr">
            {getDownloads?.data?.totalDownloads}
          </span>
        </p>
      </div>

      <div id="container" className="recent-application-pentabell">
        <div className="main-content">
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Grid container spacing={2} justifyContent="flex-start" alignItems="center">
                <Grid item xs={12} sm={8}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">{t("search With Email")}</FormLabel>
                  </FormGroup>
                  <TextField
                    className="input-pentabell"
                    autoComplete="off"
                    variant="standard"
                    type="text"
                    onKeyDown={handleKeyDown}
                    onChange={handleSearchChange}
                    value={searchValue}
                    placeholder={t("Search")}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SvgSearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                    <Grid item xs={12} sm={4}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("application:from")}
                    </FormLabel>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DemoContainer components={["DatePicker"]}>
                        <DatePicker
                          selected={fromDate}
                          onChange={handleDateFromChange}
                          placeholderText="Filter by creation date"
                          className="input-date"
                          
                        />
                      </DemoContainer>
                    </LocalizationProvider>
                  </FormGroup>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <FormGroup className="form-group">
                    <FormLabel className="label-pentabell">
                      {t("application:to")}
                    </FormLabel>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DemoContainer components={["DatePicker"]}>
                        <DatePicker
                          selected={toDate}
                          onChange={handleDateToChange}
                          placeholderText="Filter by creation date"
                          className="input-date"
                          
                        />
                      </DemoContainer>
                    </LocalizationProvider>
                  </FormGroup>
                </Grid>
                <Grid item xs={12} sm={1} className="btns-filter dashboard">
                  <CustomButton
                    icon={<SvgRefreshIcon />}
                    className="btn btn-outlined btn-refresh"
                    onClick={resetSearch}
                  />
                </Grid>
                <Grid item xs={12} sm={2} className="btns-filter dashboard">
                  <CustomButton
                    text={t("comments:search")}
                    onClick={handleSearchClick}
                    className="btn btn-filled"
                  />
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12}>
              <div style={{ height: "100%", width: "100%" }}>
                <DataGrid
                  rows={rows}
                  columns={columns}
                  pageSize={paginationModel.pageSize}
                  pagination
                  paginationMode="server"
                  paginationModel={paginationModel}
                  onPaginationModelChange={handlePaginationChange}
                  pageSizeOptions={[5, 10, 25]}
                  rowCount={getDownloads?.data?.totalDownloads || 0}
                  autoHeight
                  disableSelectionOnClick
                  className="pentabell-table"
                />
              </div>
            </Grid>
          </Grid>
        </div>
      </div>
      <DatesModal open={modalOpen} onClose={handleCloseModal} dates={modalDates} />
    </>
  );
};

export default ListDownloadsReport;
