"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/features/stats/charts/ArticlesByVisibility.jsx":
/*!************************************************************!*\
  !*** ./src/features/stats/charts/ArticlesByVisibility.jsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ArticlesByVisibility; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon.svg\");\n/* harmony import */ var _components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/charts/CustomPieChart */ \"(app-pages-browser)/./src/components/charts/CustomPieChart.jsx\");\n/* harmony import */ var _stats_hooks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../stats.hooks */ \"(app-pages-browser)/./src/features/stats/stats.hooks.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ArticlesByVisibility(param) {\n    let { t } = param;\n    _s();\n    const [dateFromArticle, setDateFromArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToArticle, setDateToArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchArticle, setSearchArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchArticles = ()=>{\n        setDateToArticle(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromArticle(\"2024-09-01\");\n        setSearchArticle(!searchArticle);\n    };\n    const getDataPieArticles = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetArticlesStat)({\n        dateFrom: dateFromArticle,\n        dateTo: dateToArticle,\n        barChart: null\n    });\n    const pieChart = {\n        title: t(\"statsDash:articlesByVisibility\"),\n        dataset: getDataPieArticles?.data?.map((article)=>({\n                label: article.visibility,\n                value: article.totalArticles\n            })),\n        colors: [\n            \"#234791\",\n            \"#FFCA00\",\n            \"#006A67\"\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieArticles.refetch();\n    }, [\n        searchArticle\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        className: \"card\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"heading-h3\",\n                    gutterBottom: true,\n                    children: pieChart.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    elevation: 0,\n                    disableGutters: true,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            \"aria-controls\": \"panel1bh-content\",\n                            id: \"panel1bh-header\",\n                            className: \"svg-accordion\",\n                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                lineNumber: 65,\n                                columnNumber: 25\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"label-pentabell\",\n                                children: t(\"statsDash:filters\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            elevation: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                container: true,\n                                className: \"chart-grid\",\n                                spacing: 1,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            label: t(\"statsDash:fromDate\"),\n                                            type: \"date\",\n                                            value: dateFromArticle,\n                                            onChange: (e)=>setDateFromArticle(e.target.value),\n                                            fullWidth: true,\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            label: t(\"statsDash:toDate\"),\n                                            type: \"date\",\n                                            value: dateToArticle,\n                                            onChange: (e)=>setDateToArticle(e.target.value),\n                                            fullWidth: true,\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        item: true,\n                                        xs: 3,\n                                        sm: 1,\n                                        md: 4,\n                                        className: \"btns-filter dashboard\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            className: \"btn btn-outlined btn-refresh full-width\",\n                                            onClick: resetSearchArticles\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        item: true,\n                                        xs: 11,\n                                        sm: 11,\n                                        md: 8,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            text: t(\"statsDash:filter\"),\n                                            onClick: ()=>{\n                                                setSearchArticle(!searchArticle);\n                                            },\n                                            className: \"btn btn-outlined btn-filter-stat full-width\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"chart-wrapper\",\n                    children: [\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            donuts: false,\n                            chart: pieChart\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        \" \",\n                        pieChart.dataset?.some((item)=>item[\"value\"] > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"labelstats-wrapper\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"public-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:public\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"privatearticles-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:private\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"draft-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:draft\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\ArticlesByVisibility.jsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(ArticlesByVisibility, \"KBPtcvjKtIoeIrHPS142uE5G/JI=\", false, function() {\n    return [\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_6__.useGetArticlesStat\n    ];\n});\n_c = ArticlesByVisibility;\nvar _c;\n$RefreshReg$(_c, \"ArticlesByVisibility\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/stats/charts/ArticlesByVisibility.jsx\n"));

/***/ })

});