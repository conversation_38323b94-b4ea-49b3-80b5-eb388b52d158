"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlossaryService = void 0;
const constants_1 = require("@/utils/helpers/constants");
const glossaries_model_1 = __importDefault(require("./glossaries.model"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
class GlossaryService {
    slugify(text) {
        return text?.toLowerCase().trim().replace(/\s+/g, '-');
    }
    async getExistingUrls(excludeId) {
        const filter = excludeId ? { _id: { $ne: excludeId } } : {};
        const glossaries = await glossaries_model_1.default.find(filter);
        const urls = new Set();
        glossaries.forEach(glossary => {
            if (glossary.versions instanceof Map) {
                for (const version of glossary.versions.values()) {
                    if (version?.url) {
                        urls.add(version.url);
                    }
                }
            }
        });
        return urls;
    }
    async createGlossary(glossaryData, currentUser) {
        const existingUrls = await this.getExistingUrls();
        if (!glossaryData.versions || Object.keys(glossaryData.versions).length === 0) {
            throw new http_exception_1.default(400, 'At least one language version is required');
        }
        if (!glossaryData.versions) {
            glossaryData.versions = {};
        }
        Object.entries(glossaryData.versions).forEach(([language, version]) => {
            if (existingUrls.has(version.url)) {
                throw new http_exception_1.default(409, `${language.toUpperCase()} ${version.url} URL already exists`);
            }
            version.url = version.url || this.slugify(version.word);
            version.createdBy = currentUser;
            version.updatedBy = currentUser;
            version.isArchived = false;
            version.createdAt = new Date();
            version.updatedAt = new Date();
            version.language = language;
        });
        const newGlossary = new glossaries_model_1.default(glossaryData);
        return await newGlossary.save();
    }
    async importJsonGlossaries(file, currentUser) {
        if (!file) {
            throw new http_exception_1.default(400, 'No file uploaded');
        }
        const jsonData = JSON.parse(file.buffer.toString());
        const createdEntries = [];
        if (!Array.isArray(jsonData.glossariesList)) {
            throw new http_exception_1.default(400, 'Invalid JSON structure. Expected "glossariesList" as an array.');
        }
        const existingUrls = await this.getExistingUrls();
        for (const entry of jsonData.glossariesList) {
            const glossaryData = { ...entry };
            if (!glossaryData.versions || Object.keys(glossaryData.versions).length === 0)
                continue;
            let shouldSkip = false;
            for (const [language, version] of Object.entries(glossaryData.versions)) {
                const finalUrl = version.url || this.slugify(version.word);
                if (existingUrls.has(finalUrl)) {
                    console.log(`URL '${finalUrl}' already exists. Skipping the entire entry...`);
                    shouldSkip = true;
                    break;
                }
            }
            if (shouldSkip)
                continue;
            for (const [language, version] of Object.entries(glossaryData.versions)) {
                const finalUrl = version.url || this.slugify(version.word);
                version.url = finalUrl;
                version.language = language === 'en' ? constants_1.Language.ENGLISH : constants_1.Language.FRENCH;
                version.createdBy = currentUser;
                version.updatedBy = currentUser;
                version.isArchived = false;
                version.createdAt = new Date();
                version.updatedAt = new Date();
                existingUrls.add(finalUrl);
            }
            const newGlossary = new glossaries_model_1.default(glossaryData);
            try {
                await newGlossary.save();
                createdEntries.push(newGlossary);
            }
            catch (err) {
                console.error(`Failed to save glossary with word "${Object.values(glossaryData.versions)[0].word}":`, err);
            }
        }
        return createdEntries;
    }
    async getGlossaries(queries, language, currentUser) {
        const { paginated = 'true', searchQuery, sortOrder = 'asc', sortBy = 'createdAt', letter, word, visibility, isArchived, createdAt, pageNumber = 1, pageSize = 5, dashboard = 'true', } = queries;
        const langKey = language.toLowerCase();
        const hasRole = [constants_1.Role.ADMIN, constants_1.Role.EDITEUR].some(role => currentUser?.roles?.includes(role));
        const queryConditions = {
            [`versions.${langKey}`]: { $exists: true },
        };
        queryConditions[`versions.${langKey}.isArchived`] = isArchived && hasRole ? true : false;
        if (letter)
            queryConditions[`versions.${langKey}.letter`] = letter;
        if (word)
            queryConditions[`versions.${langKey}.word`] = new RegExp(`.*${word}.*`, 'i');
        if (searchQuery) {
            const regex = new RegExp(`.*${searchQuery}.*`, 'i');
            queryConditions['$or'] = searchQuery.includes('https://')
                ? [{ [`versions.${langKey}.content`]: regex }]
                : [
                    { [`versions.${langKey}.word`]: regex },
                    { [`versions.${langKey}.metaTitle`]: regex },
                    { [`versions.${langKey}.metaDescription`]: regex },
                ];
        }
        if (visibility) {
            queryConditions[`versions.${langKey}.visibility`] = visibility;
        }
        if (createdAt) {
            const date = new Date(createdAt);
            const startOfDay = new Date(date);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(date);
            endOfDay.setHours(23, 59, 59, 999);
            queryConditions[`versions.${langKey}.createdAt`] = {
                $gte: startOfDay,
                $lte: endOfDay,
            };
        }
        let sortField;
        switch (sortBy) {
            case 'alphabetical':
                sortField = `versions.${langKey}.word`;
                break;
            case 'createdAt':
            default:
                sortField = `versions.${langKey}.createdAt`;
                break;
        }
        const sortCriteria = { [sortField]: sortOrder === 'asc' ? 1 : -1 };
        const skip = (Number(pageNumber) - 1) * Number(pageSize);
        try {
            const glossariesQuery = glossaries_model_1.default
                .find(queryConditions)
                .populate(`versions.${langKey}.createdBy`, 'firstName lastName')
                .populate(`versions.${langKey}.updatedBy`, 'firstName lastName')
                .sort(sortCriteria);
            if (paginated !== 'false' && dashboard !== 'false') {
                glossariesQuery.skip(skip).limit(Number(pageSize));
            }
            const glossaries = await glossariesQuery.lean();
            if (!glossaries.length) {
                return paginated === 'false' || dashboard === 'false'
                    ? []
                    : {
                        pageNumber,
                        pageSize,
                        totalPages: 0,
                        totalGlossaries: 0,
                        glossaries: [],
                    };
            }
            const transformedGlossaries = dashboard === 'true'
                ? glossaries.map(glossary => {
                    const existingLanguages = Object.keys(glossary.versions || {}).map(lang => lang === 'en' ? constants_1.Language.ENGLISH : constants_1.Language.FRENCH);
                    const versions = glossary.versions?.[langKey];
                    return {
                        _id: glossary._id,
                        robotsMeta: glossary.robotsMeta,
                        existingLanguages,
                        versions: {
                            ...versions,
                            language,
                        },
                    };
                })
                : glossaries.reduce((acc, entry) => {
                    const letter = entry.versions?.[langKey]?.letter.toUpperCase();
                    const word = entry.versions?.[langKey]?.word;
                    if (!acc[letter]) {
                        acc[letter] = [];
                    }
                    acc[letter].push({ word, url: entry.versions?.[langKey]?.url });
                    return acc;
                }, {});
            if (paginated === 'false' || dashboard === 'false') {
                return transformedGlossaries;
            }
            const totalGlossaries = await glossaries_model_1.default.countDocuments(queryConditions);
            const totalPages = Math.ceil(totalGlossaries / Number(pageSize));
            return {
                pageNumber,
                pageSize,
                totalPages,
                totalGlossaries,
                glossaries: transformedGlossaries,
            };
        }
        catch (error) {
            console.error('Error fetching glossaries:', error);
            throw error;
        }
    }
    async getSlugBySlug(language, url) {
        const langKey = language.toLowerCase();
        const glossary = await glossaries_model_1.default.findOne({
            [`versions.${langKey}.url`]: url.toLowerCase(),
            [`versions.${langKey}`]: { $exists: true },
        });
        if (!glossary)
            throw new http_exception_1.default(404, 'No glossary found');
        const version = glossary.versions?.[langKey];
        if (!version)
            throw new http_exception_1.default(404, 'No glossary version found');
        return {
            slug: version.url,
        };
    }
    async get(glossaryId) {
        const glossary = await glossaries_model_1.default.findById(glossaryId).lean();
        if (!glossary)
            throw new http_exception_1.default(404, 'Glossary Not found ');
        return glossary;
    }
    async getGlossaryByLanguageAndId(language, id) {
        const langKey = language.toLowerCase();
        const glossary = await glossaries_model_1.default
            .findById(id)
            .populate(`versions.${langKey}.createdBy`, 'firstName lastName')
            .populate(`versions.${langKey}.updatedBy`, 'firstName lastName')
            .lean();
        if (!glossary)
            throw new http_exception_1.default(404, 'Glossary not found');
        if (!glossary.versions?.[langKey]) {
            throw new http_exception_1.default(404, `No ${language} version found for glossary with ID ${id}`);
        }
        const version = glossary.versions.get(langKey);
        return {
            _id: glossary._id,
            ...version,
            language,
            robotsMeta: glossary.robotsMeta,
        };
    }
    async getGlossaryByUrl(language, url, currentUser) {
        const langKey = language.toLowerCase();
        const glossary = await glossaries_model_1.default
            .findOne({
            [`versions.${langKey}.url`]: url.toLowerCase(),
            [`versions.${langKey}`]: { $exists: true },
        })
            .populate(`versions.${langKey}.createdBy`, 'firstName lastName')
            .populate(`versions.${langKey}.updatedBy`, 'firstName lastName')
            .lean();
        if (!glossary)
            throw new http_exception_1.default(404, 'No glossary found');
        const version = glossary.versions?.[langKey];
        if (!version)
            throw new http_exception_1.default(404, 'No glossary version found');
        const hasRole = currentUser?.roles?.includes(constants_1.Role.ADMIN) || currentUser?.roles?.includes(constants_1.Role.EDITEUR);
        if ((version.visibility !== constants_1.Visibility.Public && (!currentUser || currentUser?.roles?.includes(constants_1.Role.CANDIDATE))) ||
            (version.isArchived === true && !hasRole)) {
            throw new http_exception_1.default(404, 'No glossary found');
        }
        return {
            _id: glossary._id,
            ...version,
            language,
            robotsMeta: glossary.robotsMeta,
        };
    }
    async updateGlossaryByLanguageAndId(glossaryId, updateData, currentUser) {
        const glossary = await glossaries_model_1.default.findById(glossaryId);
        if (!glossary)
            throw new http_exception_1.default(404, `No glossary found with ID ${glossaryId}.`);
        if (!glossary.versions || !(glossary.versions instanceof Map)) {
            glossary.versions = new Map();
        }
        let versions = {};
        if (updateData.versions) {
            versions = updateData.versions;
        }
        else if (typeof updateData === 'object' && updateData.language && typeof updateData.language === 'string') {
            const langKey = updateData.language.toLowerCase();
            versions[langKey] = updateData;
        }
        else if (Object.keys(updateData).some(k => ['en', 'fr'].includes(k))) {
            versions = updateData;
        }
        else {
            throw new http_exception_1.default(400, 'Invalid glossary version update structure');
        }
        for (const [langKeyRaw, versionDataRaw] of Object.entries(versions)) {
            if (langKeyRaw === 'robotsMeta') {
                glossary.robotsMeta = versionDataRaw;
                continue;
            }
            const langKey = langKeyRaw.toLowerCase();
            const versionData = versionDataRaw;
            const existingVersion = glossary.versions.get(langKey);
            const now = new Date();
            if (!existingVersion) {
                const baseWord = versionData.word || '';
                const url = versionData.url || this.slugify(baseWord);
                const urlExists = await glossaries_model_1.default.exists({
                    _id: { $ne: glossaryId },
                    [`versions.${langKey}.url`]: url,
                });
                if (urlExists) {
                    throw new http_exception_1.default(409, `${langKey.toUpperCase()} URL '${url}' already exists in another glossary`);
                }
                glossary.versions.set(langKey, {
                    word: versionData.word || '',
                    letter: versionData.letter || '',
                    content: versionData.content || '',
                    metaTitle: versionData.metaTitle || '',
                    metaDescription: versionData.metaDescription || '',
                    url,
                    language: versionData.language || (langKey === 'en' ? constants_1.Language.ENGLISH : constants_1.Language.FRENCH),
                    visibility: versionData.visibility || constants_1.Visibility.Draft,
                    isArchived: false,
                    createdAt: now,
                    updatedAt: now,
                    createdBy: currentUser._id,
                    updatedBy: currentUser._id,
                });
            }
            else {
                const urlChanged = versionData.url && versionData.url !== existingVersion.url;
                const wordChanged = versionData.word && versionData.word !== existingVersion.word;
                if (urlChanged) {
                    const urlExists = await glossaries_model_1.default.exists({
                        _id: { $ne: glossaryId },
                        [`versions.${langKey}.url`]: versionData.url,
                    });
                    if (urlExists) {
                        throw new http_exception_1.default(409, `${langKey.toUpperCase()} URL '${versionData.url}' already exists in another glossary`);
                    }
                }
                else if (wordChanged) {
                    const base = versionData.word || '';
                    const newUrl = this.slugify(base);
                    const urlExists = await glossaries_model_1.default.exists({
                        _id: { $ne: glossaryId },
                        [`versions.${langKey}.url`]: newUrl,
                    });
                    if (urlExists) {
                        throw new http_exception_1.default(409, `Generated URL '${newUrl}' from word '${base}' already exists in another glossary`);
                    }
                    versionData.url = newUrl;
                }
                Object.assign(existingVersion, versionData);
                existingVersion.updatedAt = now;
                existingVersion.updatedBy = currentUser._id;
                existingVersion.createdBy = currentUser._id;
                glossary.markModified(`versions.${langKey}`);
            }
        }
        if ('robotsMeta' in updateData) {
            glossary.robotsMeta = updateData.robotsMeta;
        }
        await glossary.save();
        return glossary;
    }
    async updateGlossaryVersion(glossaryId, language, versionData, currentUser) {
        const existingGlossary = await glossaries_model_1.default.findById(glossaryId);
        if (!existingGlossary)
            throw new http_exception_1.default(404, 'Glossary not found');
        const langKey = language.toLowerCase();
        const versionExists = existingGlossary.versions && !!existingGlossary.versions.get(langKey);
        if (versionExists) {
            const updateDataByLanguage = { [langKey]: versionData };
            return this.updateGlossaryByLanguageAndId(glossaryId, updateDataByLanguage, currentUser);
        }
        const base = versionData.url || versionData.word || '';
        const newUrl = this.slugify(base);
        const urlExists = await glossaries_model_1.default.exists({
            _id: { $ne: glossaryId },
            $or: [{ 'versions.en.url': newUrl }, { 'versions.fr.url': newUrl }],
        });
        if (urlExists) {
            throw new http_exception_1.default(409, `URL '${newUrl}' already exists in another glossary`);
        }
        const newVersionData = {
            word: versionData.word || '',
            letter: versionData.letter || '',
            content: versionData.content || '',
            metaTitle: versionData.metaTitle || '',
            metaDescription: versionData.metaDescription || '',
            url: newUrl,
            language,
            visibility: versionData.visibility || constants_1.Visibility.Draft,
            isArchived: false,
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: currentUser,
            updatedBy: currentUser,
        };
        const updateData = { [langKey]: newVersionData };
        return this.updateGlossaryByLanguageAndId(glossaryId, updateData, currentUser);
    }
    async archiveOrRestoreGlossaryByLanguageAndId(language, glossaryId) {
        const glossary = await glossaries_model_1.default.findById(glossaryId);
        if (!glossary)
            throw new http_exception_1.default(404, `No glossary found with ID ${glossaryId}.`);
        const langKey = language.toLowerCase();
        if (!glossary.versions || !glossary.versions.get(langKey)) {
            throw new http_exception_1.default(404, `No ${language} version found for glossary ID ${glossaryId}.`);
        }
        glossary.versions.get(langKey).isArchived = !glossary.versions.get(langKey).isArchived;
        await glossary.save();
        return glossary.versions.get(langKey);
    }
    async deleteGlossary(language, glossaryId) {
        const glossary = await glossaries_model_1.default.findById(glossaryId);
        if (!glossary)
            throw new http_exception_1.default(404, `No glossary found with ID ${glossaryId}.`);
        const langKey = language.toLowerCase();
        if (!glossary.versions || !glossary.versions.get(langKey)) {
            throw new http_exception_1.default(404, `No ${language} version found for glossary ID ${glossaryId}.`);
        }
        glossary.versions.delete(langKey);
        if (Object.keys(glossary.versions).length === 0) {
            await glossaries_model_1.default.deleteOne({ _id: glossaryId });
            return { message: `Glossary with ID ${glossaryId} has been completely deleted.` };
        }
        await glossary.save();
        return glossary;
    }
}
exports.GlossaryService = GlossaryService;
exports.default = GlossaryService;
//# sourceMappingURL=glossaries.service.js.map