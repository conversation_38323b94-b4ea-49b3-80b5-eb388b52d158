"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.js":
/*!***************************!*\
  !*** ./src/middleware.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-i18n-router */ \"(middleware)/./node_modules/next-i18n-router/dist/index.js\");\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_i18n_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! jose */ \"(middleware)/./node_modules/jose/dist/browser/jwt/verify.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cookie */ \"(middleware)/./node_modules/cookie/index.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../i18nConfig */ \"(middleware)/./i18nConfig.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_i18nConfig__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/constants */ \"(middleware)/./src/utils/constants.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/functions */ \"(middleware)/./src/utils/functions.js\");\n/* harmony import */ var _config_translations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./config/translations */ \"(middleware)/./src/config/translations.js\");\n/* harmony import */ var _config_allowedParams__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./config/allowedParams */ \"(middleware)/./src/config/allowedParams.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./helpers/routesList */ \"(middleware)/./src/helpers/routesList.js\");\n\n\n\n\n\n\n\n\n\n\nconst SECURITY_CONFIG = {\n    MAX_REQUESTS_PER_MINUTE: 60,\n    JWT_ALGORITHM: \"HS256\",\n    SUSPICIOUS_PATTERNS: [\n        /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n        /javascript:/gi,\n        /on\\w+\\s*=/gi,\n        /eval\\s*\\(/gi,\n        /expression\\s*\\(/gi,\n        /%3Cscript/gi,\n        /%3C%2Fscript%3E/gi\n    ]\n};\nconst rateLimitStore = new Map();\nconst logSecurityEvent = (event, details = {})=>{\n    if (true) {\n        console.warn(`[SECURITY] ${event}:`, {\n            timestamp: new Date().toISOString(),\n            ...details\n        });\n    }\n};\nconst verifyToken = async (token, clientIP = \"unknown\")=>{\n    try {\n        if (!token || typeof token !== \"string\") {\n            logSecurityEvent(\"INVALID_TOKEN_FORMAT\", {\n                clientIP,\n                reason: \"Missing or invalid token\"\n            });\n            return null;\n        }\n        const jwtRegex = /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/;\n        if (!jwtRegex.test(token)) {\n            logSecurityEvent(\"INVALID_JWT_FORMAT\", {\n                clientIP,\n                tokenPrefix: token.substring(0, 10)\n            });\n            return null;\n        }\n        const jwtSecret = process.env.NEXT_JWT_SECRET;\n        if (!jwtSecret || jwtSecret.length < 32) {\n            logSecurityEvent(\"WEAK_JWT_SECRET\", {\n                clientIP\n            });\n            throw new Error(\"JWT secret configuration error\");\n        }\n        const secret = new TextEncoder().encode(jwtSecret);\n        const { payload } = await (0,jose__WEBPACK_IMPORTED_MODULE_9__.jwtVerify)(token, secret, {\n            algorithms: [\n                SECURITY_CONFIG.JWT_ALGORITHM\n            ],\n            issuer: process.env.JWT_ISSUER || \"pentabell-api\",\n            audience: process.env.JWT_AUDIENCE\n        });\n        if (!payload || !payload._id || !payload.roles || !Array.isArray(payload.roles)) {\n            logSecurityEvent(\"INVALID_TOKEN_PAYLOAD\", {\n                clientIP,\n                hasId: !!payload?._id,\n                hasRoles: !!payload?.roles\n            });\n            return null;\n        }\n        const tokenAge = Date.now() / 1000 - (payload.iat || 0);\n        if (tokenAge > 86400) {\n            logSecurityEvent(\"OLD_TOKEN_USAGE\", {\n                clientIP,\n                tokenAge,\n                userId: payload._id\n            });\n        }\n        return payload;\n    } catch (error) {\n        if (error.name === \"JWTExpired\") {\n            logSecurityEvent(\"TOKEN_EXPIRED\", {\n                clientIP\n            });\n        } else if (error.name === \"JWTInvalid\") {\n            logSecurityEvent(\"INVALID_TOKEN\", {\n                clientIP,\n                error: error.message\n            });\n        } else {\n            logSecurityEvent(\"TOKEN_VERIFICATION_ERROR\", {\n                clientIP,\n                error: error.message\n            });\n        }\n        return null;\n    }\n};\nconst checkRateLimit = (clientIP)=>{\n    const now = Date.now();\n    const windowStart = now - 60000;\n    if (!rateLimitStore.has(clientIP)) {\n        rateLimitStore.set(clientIP, []);\n    }\n    const requests = rateLimitStore.get(clientIP);\n    const validRequests = requests.filter((timestamp)=>timestamp > windowStart);\n    rateLimitStore.set(clientIP, validRequests);\n    if (validRequests.length >= SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE) {\n        return false;\n    }\n    validRequests.push(now);\n    return true;\n};\nconst sanitizeInput = (value)=>{\n    if (typeof value !== \"string\") return value;\n    let sanitized = value;\n    SECURITY_CONFIG.SUSPICIOUS_PATTERNS.forEach((pattern)=>{\n        sanitized = sanitized.replace(pattern, \"\");\n    });\n    return sanitized.trim();\n};\nconst setSecurityHeaders = (response)=>{\n    response.headers.set(\"X-Content-Type-Options\", \"nosniff\");\n    response.headers.set(\"X-Frame-Options\", \"DENY\");\n    response.headers.set(\"X-XSS-Protection\", \"1; mode=block\");\n    response.headers.set(\"Referrer-Policy\", \"strict-origin-when-cross-origin\");\n    response.headers.set(\"Permissions-Policy\", \"geolocation=(), microphone=(), camera=()\");\n    response.headers.delete(\"Server\");\n    response.headers.delete(\"X-Powered-By\");\n    return response;\n};\nasync function middleware(req) {\n    const url = req.nextUrl.clone();\n    const { defaultLocale } = (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default());\n    const { pathname } = req.nextUrl;\n    const clientIP = req.ip || req.headers.get(\"x-forwarded-for\") || req.headers.get(\"x-real-ip\") || \"unknown\";\n    let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    response = setSecurityHeaders(response);\n    if (!checkRateLimit(clientIP)) {\n        logSecurityEvent(\"RATE_LIMIT_EXCEEDED\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Too Many Requests\", {\n            status: 429,\n            headers: {\n                \"Retry-After\": \"60\",\n                \"X-RateLimit-Limit\": SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE.toString(),\n                \"X-RateLimit-Remaining\": \"0\"\n            }\n        });\n    }\n    let hasModifiedParams = false;\n    for (const [key, value] of url.searchParams.entries()){\n        const sanitizedValue = sanitizeInput(value);\n        if (sanitizedValue !== value) {\n            url.searchParams.set(key, sanitizedValue);\n            hasModifiedParams = true;\n            logSecurityEvent(\"SUSPICIOUS_QUERY_PARAM\", {\n                clientIP,\n                key,\n                originalValue: value.substring(0, 50)\n            });\n        }\n    }\n    const pathString = pathname.toLowerCase();\n    const hasSuspiciousPath = SECURITY_CONFIG.SUSPICIOUS_PATTERNS.some((pattern)=>pattern.test(pathString));\n    if (hasSuspiciousPath) {\n        logSecurityEvent(\"SUSPICIOUS_PATH_ACCESS\", {\n            clientIP,\n            pathname\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Forbidden\", {\n            status: 403\n        });\n    }\n    const cookies = cookie__WEBPACK_IMPORTED_MODULE_2__.parse(req.headers.get(\"cookie\") || \"\");\n    const { accessToken, refreshToken } = cookies;\n    const isProtectedRoute = pathname.includes(\"dashboard\") || pathname.includes(\"backoffice\");\n    if (isProtectedRoute && !(accessToken && refreshToken)) {\n        logSecurityEvent(\"UNAUTHORIZED_ACCESS_ATTEMPT\", {\n            clientIP,\n            pathname\n        });\n        url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    let user = null;\n    if (refreshToken) {\n        user = await verifyToken(refreshToken, clientIP);\n        if (isProtectedRoute && !user) {\n            logSecurityEvent(\"INVALID_TOKEN_PROTECTED_ROUTE\", {\n                clientIP,\n                pathname\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n    }\n    // Handle logout route\n    if (pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/`) {\n        logSecurityEvent(\"USER_LOGOUT\", {\n            clientIP,\n            userId: user?._id\n        });\n        return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n    }\n    // Enhanced role-based access control\n    if (user) {\n        const menuList = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.getRoutesListByRole)(user);\n        // Validate user roles\n        if (!user.roles || !Array.isArray(user.roles) || user.roles.length === 0) {\n            logSecurityEvent(\"INVALID_USER_ROLES\", {\n                clientIP,\n                userId: user._id\n            });\n            url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n        const checkRole = !menuList?.some((item)=>pathname.includes(item)) && (pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}`) || pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}`));\n        // Enhanced role checking with security logging\n        if (checkRole || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`) {\n            let redirectPath = null;\n            if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.home.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.EDITOR)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.blogs.route}/`;\n            } else if (user.roles.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.CANDIDATE)) {\n                redirectPath = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.candidateRoutes.myApplications.route}`;\n            }\n            if (redirectPath) {\n                logSecurityEvent(\"ROLE_BASED_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles,\n                    fromPath: pathname,\n                    toPath: redirectPath\n                });\n                url.pathname = redirectPath;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            } else {\n                logSecurityEvent(\"NO_VALID_ROLE_REDIRECT\", {\n                    clientIP,\n                    userId: user._id,\n                    roles: user.roles\n                });\n                url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n            }\n        }\n    }\n    // Enhanced parameter filtering with security logging\n    let removedParams = [];\n    for (const param of url.searchParams.keys()){\n        if (!_config_allowedParams__WEBPACK_IMPORTED_MODULE_7__.allowedParams.has(param)) {\n            url.searchParams.delete(param);\n            removedParams.push(param);\n        }\n    }\n    if (removedParams.length > 0) {\n        logSecurityEvent(\"REMOVED_DISALLOWED_PARAMS\", {\n            clientIP,\n            pathname,\n            removedParams,\n            userId: user?._id\n        });\n    }\n    // Check if parameters were modified (either sanitized or removed)\n    if (hasModifiedParams || url.searchParams.toString() !== req.nextUrl.searchParams.toString()) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // Enhanced redirection paths with security checks\n    const frPath = _config_translations__WEBPACK_IMPORTED_MODULE_6__[\"default\"][req.nextUrl.pathname];\n    if (frPath) {\n        logSecurityEvent(\"FRENCH_PATH_REDIRECT\", {\n            clientIP,\n            fromPath: req.nextUrl.pathname,\n            toPath: frPath\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(frPath, req.url));\n    }\n    // Enhanced language handling with security validation\n    if (!pathname.startsWith(\"/fr\") && !pathname.startsWith(`/${defaultLocale}`) && !pathname.startsWith(\"/_next\") && !pathname.startsWith(\"/api\") && !pathname.startsWith(\"/static\") && !pathname.includes(\".\")) {\n        // Additional security check for suspicious paths\n        if (pathname.length > 200) {\n            logSecurityEvent(\"SUSPICIOUS_LONG_PATH\", {\n                clientIP,\n                pathLength: pathname.length\n            });\n            return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(\"Bad Request\", {\n                status: 400\n            });\n        }\n        url.pathname = `/en${pathname}`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.rewrite(url);\n    }\n    // Log successful requests for monitoring (in development only)\n    if ( true && user) {\n        logSecurityEvent(\"SUCCESSFUL_REQUEST\", {\n            clientIP,\n            pathname,\n            userId: user._id,\n            roles: user.roles\n        });\n    }\n    return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     * - files with extensions (images, fonts, etc.)\r\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|public|.*\\\\..*).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL21pZGRsZXdhcmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQThDO0FBQ2I7QUFDVTtBQUNmO0FBRVc7QUFDRTtBQUNlO0FBQ1A7QUFDTTtBQU96QjtBQUU5QixNQUFNYyxrQkFBa0I7SUFDdEJDLHlCQUF5QjtJQUN6QkMsZUFBZTtJQUNmQyxxQkFBcUI7UUFDbkI7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtBQUNIO0FBRUEsTUFBTUMsaUJBQWlCLElBQUlDO0FBRTNCLE1BQU1DLG1CQUFtQixDQUFDQyxPQUFPQyxVQUFVLENBQUMsQ0FBQztJQUMzQyxJQUFJLElBQTZCLEVBQUU7UUFDakNDLFFBQVFDLElBQUksQ0FBQyxDQUFDLFdBQVcsRUFBRUgsTUFBTSxDQUFDLENBQUMsRUFBRTtZQUNuQ0ksV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ2pDLEdBQUdMLE9BQU87UUFDWjtJQUNGO0FBQ0Y7QUFFTyxNQUFNTSxjQUFjLE9BQU9DLE9BQU9DLFdBQVcsU0FBUztJQUMzRCxJQUFJO1FBQ0YsSUFBSSxDQUFDRCxTQUFTLE9BQU9BLFVBQVUsVUFBVTtZQUN2Q1QsaUJBQWlCLHdCQUF3QjtnQkFDdkNVO2dCQUNBQyxRQUFRO1lBQ1Y7WUFDQSxPQUFPO1FBQ1Q7UUFFQSxNQUFNQyxXQUFXO1FBQ2pCLElBQUksQ0FBQ0EsU0FBU0MsSUFBSSxDQUFDSixRQUFRO1lBQ3pCVCxpQkFBaUIsc0JBQXNCO2dCQUNyQ1U7Z0JBQ0FJLGFBQWFMLE1BQU1NLFNBQVMsQ0FBQyxHQUFHO1lBQ2xDO1lBQ0EsT0FBTztRQUNUO1FBRUEsTUFBTUMsWUFBWUMsUUFBUUMsR0FBRyxDQUFDQyxlQUFlO1FBQzdDLElBQUksQ0FBQ0gsYUFBYUEsVUFBVUksTUFBTSxHQUFHLElBQUk7WUFDdkNwQixpQkFBaUIsbUJBQW1CO2dCQUFFVTtZQUFTO1lBQy9DLE1BQU0sSUFBSVcsTUFBTTtRQUNsQjtRQUVBLE1BQU1DLFNBQVMsSUFBSUMsY0FBY0MsTUFBTSxDQUFDUjtRQUV4QyxNQUFNLEVBQUVTLE9BQU8sRUFBRSxHQUFHLE1BQU01QywrQ0FBU0EsQ0FBQzRCLE9BQU9hLFFBQVE7WUFDakRJLFlBQVk7Z0JBQUNoQyxnQkFBZ0JFLGFBQWE7YUFBQztZQUMzQytCLFFBQVFWLFFBQVFDLEdBQUcsQ0FBQ1UsVUFBVSxJQUFJO1lBQ2xDQyxVQUFVWixRQUFRQyxHQUFHLENBQUNZLFlBQVk7UUFDcEM7UUFFQSxJQUNFLENBQUNMLFdBQ0QsQ0FBQ0EsUUFBUU0sR0FBRyxJQUNaLENBQUNOLFFBQVFPLEtBQUssSUFDZCxDQUFDQyxNQUFNQyxPQUFPLENBQUNULFFBQVFPLEtBQUssR0FDNUI7WUFDQWhDLGlCQUFpQix5QkFBeUI7Z0JBQ3hDVTtnQkFDQXlCLE9BQU8sQ0FBQyxDQUFDVixTQUFTTTtnQkFDbEJLLFVBQVUsQ0FBQyxDQUFDWCxTQUFTTztZQUN2QjtZQUNBLE9BQU87UUFDVDtRQUVBLE1BQU1LLFdBQVcvQixLQUFLZ0MsR0FBRyxLQUFLLE9BQVFiLENBQUFBLFFBQVFjLEdBQUcsSUFBSTtRQUNyRCxJQUFJRixXQUFXLE9BQU87WUFDcEJyQyxpQkFBaUIsbUJBQW1CO2dCQUNsQ1U7Z0JBQ0EyQjtnQkFDQUcsUUFBUWYsUUFBUU0sR0FBRztZQUNyQjtRQUNGO1FBRUEsT0FBT047SUFDVCxFQUFFLE9BQU9nQixPQUFPO1FBQ2QsSUFBSUEsTUFBTUMsSUFBSSxLQUFLLGNBQWM7WUFDL0IxQyxpQkFBaUIsaUJBQWlCO2dCQUFFVTtZQUFTO1FBQy9DLE9BQU8sSUFBSStCLE1BQU1DLElBQUksS0FBSyxjQUFjO1lBQ3RDMUMsaUJBQWlCLGlCQUFpQjtnQkFBRVU7Z0JBQVUrQixPQUFPQSxNQUFNRSxPQUFPO1lBQUM7UUFDckUsT0FBTztZQUNMM0MsaUJBQWlCLDRCQUE0QjtnQkFDM0NVO2dCQUNBK0IsT0FBT0EsTUFBTUUsT0FBTztZQUN0QjtRQUNGO1FBQ0EsT0FBTztJQUNUO0FBQ0YsRUFBRTtBQUVGLE1BQU1DLGlCQUFpQixDQUFDbEM7SUFDdEIsTUFBTTRCLE1BQU1oQyxLQUFLZ0MsR0FBRztJQUNwQixNQUFNTyxjQUFjUCxNQUFNO0lBRTFCLElBQUksQ0FBQ3hDLGVBQWVnRCxHQUFHLENBQUNwQyxXQUFXO1FBQ2pDWixlQUFlaUQsR0FBRyxDQUFDckMsVUFBVSxFQUFFO0lBQ2pDO0lBRUEsTUFBTXNDLFdBQVdsRCxlQUFlbUQsR0FBRyxDQUFDdkM7SUFFcEMsTUFBTXdDLGdCQUFnQkYsU0FBU0csTUFBTSxDQUFDLENBQUM5QyxZQUFjQSxZQUFZd0M7SUFDakUvQyxlQUFlaUQsR0FBRyxDQUFDckMsVUFBVXdDO0lBRTdCLElBQUlBLGNBQWM5QixNQUFNLElBQUkxQixnQkFBZ0JDLHVCQUF1QixFQUFFO1FBQ25FLE9BQU87SUFDVDtJQUVBdUQsY0FBY0UsSUFBSSxDQUFDZDtJQUNuQixPQUFPO0FBQ1Q7QUFFQSxNQUFNZSxnQkFBZ0IsQ0FBQ0M7SUFDckIsSUFBSSxPQUFPQSxVQUFVLFVBQVUsT0FBT0E7SUFFdEMsSUFBSUMsWUFBWUQ7SUFDaEI1RCxnQkFBZ0JHLG1CQUFtQixDQUFDMkQsT0FBTyxDQUFDLENBQUNDO1FBQzNDRixZQUFZQSxVQUFVRyxPQUFPLENBQUNELFNBQVM7SUFDekM7SUFFQSxPQUFPRixVQUFVSSxJQUFJO0FBQ3ZCO0FBRUEsTUFBTUMscUJBQXFCLENBQUNDO0lBQzFCQSxTQUFTQyxPQUFPLENBQUNmLEdBQUcsQ0FBQywwQkFBMEI7SUFDL0NjLFNBQVNDLE9BQU8sQ0FBQ2YsR0FBRyxDQUFDLG1CQUFtQjtJQUN4Q2MsU0FBU0MsT0FBTyxDQUFDZixHQUFHLENBQUMsb0JBQW9CO0lBQ3pDYyxTQUFTQyxPQUFPLENBQUNmLEdBQUcsQ0FBQyxtQkFBbUI7SUFDeENjLFNBQVNDLE9BQU8sQ0FBQ2YsR0FBRyxDQUNsQixzQkFDQTtJQUdGYyxTQUFTQyxPQUFPLENBQUNDLE1BQU0sQ0FBQztJQUN4QkYsU0FBU0MsT0FBTyxDQUFDQyxNQUFNLENBQUM7SUFFeEIsT0FBT0Y7QUFDVDtBQUVPLGVBQWVHLFdBQVdDLEdBQUc7SUFDbEMsTUFBTUMsTUFBTUQsSUFBSUUsT0FBTyxDQUFDQyxLQUFLO0lBQzdCLE1BQU0sRUFBRUMsYUFBYSxFQUFFLEdBQUdyRixvREFBVUE7SUFDcEMsTUFBTSxFQUFFc0YsUUFBUSxFQUFFLEdBQUdMLElBQUlFLE9BQU87SUFDaEMsTUFBTXpELFdBQ0p1RCxJQUFJTSxFQUFFLElBQ05OLElBQUlILE9BQU8sQ0FBQ2IsR0FBRyxDQUFDLHNCQUNoQmdCLElBQUlILE9BQU8sQ0FBQ2IsR0FBRyxDQUFDLGdCQUNoQjtJQUVGLElBQUlZLFdBQVcvRSxxREFBWUEsQ0FBQzBGLElBQUk7SUFDaENYLFdBQVdELG1CQUFtQkM7SUFFOUIsSUFBSSxDQUFDakIsZUFBZWxDLFdBQVc7UUFDN0JWLGlCQUFpQix1QkFBdUI7WUFBRVU7WUFBVTREO1FBQVM7UUFDN0QsT0FBTyxJQUFJeEYscURBQVlBLENBQUMscUJBQXFCO1lBQzNDMkYsUUFBUTtZQUNSWCxTQUFTO2dCQUNQLGVBQWU7Z0JBQ2YscUJBQXFCcEUsZ0JBQWdCQyx1QkFBdUIsQ0FBQytFLFFBQVE7Z0JBQ3JFLHlCQUF5QjtZQUMzQjtRQUNGO0lBQ0Y7SUFFQSxJQUFJQyxvQkFBb0I7SUFDeEIsS0FBSyxNQUFNLENBQUNDLEtBQUt0QixNQUFNLElBQUlZLElBQUlXLFlBQVksQ0FBQ0MsT0FBTyxHQUFJO1FBQ3JELE1BQU1DLGlCQUFpQjFCLGNBQWNDO1FBQ3JDLElBQUl5QixtQkFBbUJ6QixPQUFPO1lBQzVCWSxJQUFJVyxZQUFZLENBQUM5QixHQUFHLENBQUM2QixLQUFLRztZQUMxQkosb0JBQW9CO1lBQ3BCM0UsaUJBQWlCLDBCQUEwQjtnQkFDekNVO2dCQUNBa0U7Z0JBQ0FJLGVBQWUxQixNQUFNdkMsU0FBUyxDQUFDLEdBQUc7WUFDcEM7UUFDRjtJQUNGO0lBRUEsTUFBTWtFLGFBQWFYLFNBQVNZLFdBQVc7SUFDdkMsTUFBTUMsb0JBQW9CekYsZ0JBQWdCRyxtQkFBbUIsQ0FBQ3VGLElBQUksQ0FDaEUsQ0FBQzNCLFVBQVlBLFFBQVE1QyxJQUFJLENBQUNvRTtJQUc1QixJQUFJRSxtQkFBbUI7UUFDckJuRixpQkFBaUIsMEJBQTBCO1lBQUVVO1lBQVU0RDtRQUFTO1FBQ2hFLE9BQU8sSUFBSXhGLHFEQUFZQSxDQUFDLGFBQWE7WUFBRTJGLFFBQVE7UUFBSTtJQUNyRDtJQUVBLE1BQU1ZLFVBQVV0Ryx5Q0FBWSxDQUFDa0YsSUFBSUgsT0FBTyxDQUFDYixHQUFHLENBQUMsYUFBYTtJQUMxRCxNQUFNLEVBQUVzQyxXQUFXLEVBQUVDLFlBQVksRUFBRSxHQUFHSDtJQUV0QyxNQUFNSSxtQkFDSm5CLFNBQVNvQixRQUFRLENBQUMsZ0JBQWdCcEIsU0FBU29CLFFBQVEsQ0FBQztJQUV0RCxJQUFJRCxvQkFBb0IsQ0FBRUYsQ0FBQUEsZUFBZUMsWUFBVyxHQUFJO1FBQ3REeEYsaUJBQWlCLCtCQUErQjtZQUFFVTtZQUFVNEQ7UUFBUztRQUNyRUosSUFBSUksUUFBUSxHQUFHLENBQUMsQ0FBQyxFQUFFaEYsMkRBQVVBLENBQUNxRyxLQUFLLENBQUNDLEtBQUssQ0FBQyxDQUFDLENBQUM7UUFDNUMsT0FBTzlHLHFEQUFZQSxDQUFDK0csUUFBUSxDQUFDM0I7SUFDL0I7SUFFQSxJQUFJNEIsT0FBTztJQUNYLElBQUlOLGNBQWM7UUFDaEJNLE9BQU8sTUFBTXRGLFlBQVlnRixjQUFjOUU7UUFFdkMsSUFBSStFLG9CQUFvQixDQUFDSyxNQUFNO1lBQzdCOUYsaUJBQWlCLGlDQUFpQztnQkFBRVU7Z0JBQVU0RDtZQUFTO1lBQ3ZFSixJQUFJSSxRQUFRLEdBQUcsQ0FBQyxDQUFDLEVBQUVoRiwyREFBVUEsQ0FBQ3FHLEtBQUssQ0FBQ0MsS0FBSyxDQUFDLENBQUMsQ0FBQztZQUM1QyxPQUFPOUcscURBQVlBLENBQUMrRyxRQUFRLENBQUMzQjtRQUMvQjtJQUNGO0lBRUEsc0JBQXNCO0lBQ3RCLElBQ0VJLGFBQWEsQ0FBQyxDQUFDLEVBQUVoRiwyREFBVUEsQ0FBQ3lHLE1BQU0sQ0FBQ0gsS0FBSyxDQUFDLENBQUMsQ0FBQyxJQUMzQ3RCLGFBQWEsQ0FBQyxJQUFJLEVBQUVoRiwyREFBVUEsQ0FBQ3lHLE1BQU0sQ0FBQ0gsS0FBSyxDQUFDLENBQUMsQ0FBQyxFQUM5QztRQUNBNUYsaUJBQWlCLGVBQWU7WUFBRVU7WUFBVThCLFFBQVFzRCxNQUFNL0Q7UUFBSTtRQUM5RCxPQUFPbkQsNERBQVVBLENBQUNxRixLQUFLakYsb0RBQVVBO0lBQ25DO0lBRUEscUNBQXFDO0lBQ3JDLElBQUk4RyxNQUFNO1FBQ1IsTUFBTUUsV0FBVzlHLHFFQUFtQkEsQ0FBQzRHO1FBRXJDLHNCQUFzQjtRQUN0QixJQUFJLENBQUNBLEtBQUs5RCxLQUFLLElBQUksQ0FBQ0MsTUFBTUMsT0FBTyxDQUFDNEQsS0FBSzlELEtBQUssS0FBSzhELEtBQUs5RCxLQUFLLENBQUNaLE1BQU0sS0FBSyxHQUFHO1lBQ3hFcEIsaUJBQWlCLHNCQUFzQjtnQkFBRVU7Z0JBQVU4QixRQUFRc0QsS0FBSy9ELEdBQUc7WUFBQztZQUNwRW1DLElBQUlJLFFBQVEsR0FBRyxDQUFDLENBQUMsRUFBRWhGLDJEQUFVQSxDQUFDcUcsS0FBSyxDQUFDQyxLQUFLLENBQUMsQ0FBQyxDQUFDO1lBQzVDLE9BQU85RyxxREFBWUEsQ0FBQytHLFFBQVEsQ0FBQzNCO1FBQy9CO1FBRUEsTUFBTStCLFlBQ0osQ0FBQ0QsVUFBVVosS0FBSyxDQUFDYyxPQUFTNUIsU0FBU29CLFFBQVEsQ0FBQ1EsVUFDM0M1QixDQUFBQSxVQUFVb0IsU0FBUyxDQUFDLENBQUMsRUFBRWxHLG1FQUFrQkEsQ0FBQzJHLE9BQU8sQ0FBQ1AsS0FBSyxDQUFDLENBQUMsS0FDeER0QixVQUFVb0IsU0FBUyxDQUFDLENBQUMsRUFBRW5HLGtFQUFpQkEsQ0FBQzRHLE9BQU8sQ0FBQ1AsS0FBSyxDQUFDLENBQUM7UUFFNUQsK0NBQStDO1FBQy9DLElBQ0VLLGFBQ0EzQixhQUFhLENBQUMsSUFBSSxFQUFFaEYsMkRBQVVBLENBQUM4RyxRQUFRLENBQUNSLEtBQUssQ0FBQyxDQUFDLENBQUMsSUFDaER0QixhQUFhLENBQUMsSUFBSSxFQUFFaEYsMkRBQVVBLENBQUNxRyxLQUFLLENBQUNDLEtBQUssQ0FBQyxDQUFDLENBQUMsSUFDN0N0QixhQUFhLENBQUMsQ0FBQyxFQUFFaEYsMkRBQVVBLENBQUM4RyxRQUFRLENBQUNSLEtBQUssQ0FBQyxDQUFDLENBQUMsSUFDN0N0QixhQUFhLENBQUMsQ0FBQyxFQUFFaEYsMkRBQVVBLENBQUNxRyxLQUFLLENBQUNDLEtBQUssQ0FBQyxDQUFDLENBQUMsRUFDMUM7WUFDQSxJQUFJUyxlQUFlO1lBRW5CLElBQUlQLEtBQUs5RCxLQUFLLENBQUMwRCxRQUFRLENBQUN6RyxrREFBSUEsQ0FBQ3FILEtBQUssR0FBRztnQkFDbkNELGVBQWUsQ0FBQyxDQUFDLEVBQUU5RyxrRUFBaUJBLENBQUM0RyxPQUFPLENBQUNQLEtBQUssQ0FBQyxDQUFDLEVBQUV2Ryw0REFBV0EsQ0FBQ2tILElBQUksQ0FBQ1gsS0FBSyxDQUFDLENBQUMsQ0FBQztZQUNqRixPQUFPLElBQUlFLEtBQUs5RCxLQUFLLENBQUMwRCxRQUFRLENBQUN6RyxrREFBSUEsQ0FBQ3VILE1BQU0sR0FBRztnQkFDM0NILGVBQWUsQ0FBQyxDQUFDLEVBQUU5RyxrRUFBaUJBLENBQUM0RyxPQUFPLENBQUNQLEtBQUssQ0FBQyxDQUFDLEVBQUV2Ryw0REFBV0EsQ0FBQ29ILEtBQUssQ0FBQ2IsS0FBSyxDQUFDLENBQUMsQ0FBQztZQUNsRixPQUFPLElBQUlFLEtBQUs5RCxLQUFLLENBQUMwRCxRQUFRLENBQUN6RyxrREFBSUEsQ0FBQ3lILFNBQVMsR0FBRztnQkFDOUNMLGVBQWUsQ0FBQyxDQUFDLEVBQUU3RyxtRUFBa0JBLENBQUMyRyxPQUFPLENBQUNQLEtBQUssQ0FBQyxDQUFDLEVBQUVuRyxnRUFBZUEsQ0FBQ2tILGNBQWMsQ0FBQ2YsS0FBSyxDQUFDLENBQUM7WUFDL0Y7WUFFQSxJQUFJUyxjQUFjO2dCQUNoQnJHLGlCQUFpQix1QkFBdUI7b0JBQ3RDVTtvQkFDQThCLFFBQVFzRCxLQUFLL0QsR0FBRztvQkFDaEJDLE9BQU84RCxLQUFLOUQsS0FBSztvQkFDakI0RSxVQUFVdEM7b0JBQ1Z1QyxRQUFRUjtnQkFDVjtnQkFDQW5DLElBQUlJLFFBQVEsR0FBRytCO2dCQUNmLE9BQU92SCxxREFBWUEsQ0FBQytHLFFBQVEsQ0FBQzNCO1lBQy9CLE9BQU87Z0JBQ0xsRSxpQkFBaUIsMEJBQTBCO29CQUN6Q1U7b0JBQ0E4QixRQUFRc0QsS0FBSy9ELEdBQUc7b0JBQ2hCQyxPQUFPOEQsS0FBSzlELEtBQUs7Z0JBQ25CO2dCQUNBa0MsSUFBSUksUUFBUSxHQUFHLENBQUMsQ0FBQyxFQUFFaEYsMkRBQVVBLENBQUNxRyxLQUFLLENBQUNDLEtBQUssQ0FBQyxDQUFDLENBQUM7Z0JBQzVDLE9BQU85RyxxREFBWUEsQ0FBQytHLFFBQVEsQ0FBQzNCO1lBQy9CO1FBQ0Y7SUFDRjtJQUVBLHFEQUFxRDtJQUNyRCxJQUFJNEMsZ0JBQWdCLEVBQUU7SUFDdEIsS0FBSyxNQUFNQyxTQUFTN0MsSUFBSVcsWUFBWSxDQUFDbUMsSUFBSSxHQUFJO1FBQzNDLElBQUksQ0FBQzVILGdFQUFhQSxDQUFDMEQsR0FBRyxDQUFDaUUsUUFBUTtZQUM3QjdDLElBQUlXLFlBQVksQ0FBQ2QsTUFBTSxDQUFDZ0Q7WUFDeEJELGNBQWMxRCxJQUFJLENBQUMyRDtRQUNyQjtJQUNGO0lBRUEsSUFBSUQsY0FBYzFGLE1BQU0sR0FBRyxHQUFHO1FBQzVCcEIsaUJBQWlCLDZCQUE2QjtZQUM1Q1U7WUFDQTREO1lBQ0F3QztZQUNBdEUsUUFBUXNELE1BQU0vRDtRQUNoQjtJQUNGO0lBRUEsa0VBQWtFO0lBQ2xFLElBQ0U0QyxxQkFDQVQsSUFBSVcsWUFBWSxDQUFDSCxRQUFRLE9BQU9ULElBQUlFLE9BQU8sQ0FBQ1UsWUFBWSxDQUFDSCxRQUFRLElBQ2pFO1FBQ0EsT0FBTzVGLHFEQUFZQSxDQUFDK0csUUFBUSxDQUFDM0I7SUFDL0I7SUFFQSxrREFBa0Q7SUFDbEQsTUFBTStDLFNBQVM5SCw0REFBWSxDQUFDOEUsSUFBSUUsT0FBTyxDQUFDRyxRQUFRLENBQUM7SUFDakQsSUFBSTJDLFFBQVE7UUFDVmpILGlCQUFpQix3QkFBd0I7WUFDdkNVO1lBQ0FrRyxVQUFVM0MsSUFBSUUsT0FBTyxDQUFDRyxRQUFRO1lBQzlCdUMsUUFBUUk7UUFDVjtRQUNBLE9BQU9uSSxxREFBWUEsQ0FBQytHLFFBQVEsQ0FBQyxJQUFJcUIsSUFBSUQsUUFBUWhELElBQUlDLEdBQUc7SUFDdEQ7SUFFQSxzREFBc0Q7SUFDdEQsSUFDRSxDQUFDSSxTQUFTNkMsVUFBVSxDQUFDLFVBQ3JCLENBQUM3QyxTQUFTNkMsVUFBVSxDQUFDLENBQUMsQ0FBQyxFQUFFOUMsY0FBYyxDQUFDLEtBQ3hDLENBQUNDLFNBQVM2QyxVQUFVLENBQUMsYUFDckIsQ0FBQzdDLFNBQVM2QyxVQUFVLENBQUMsV0FDckIsQ0FBQzdDLFNBQVM2QyxVQUFVLENBQUMsY0FDckIsQ0FBQzdDLFNBQVNvQixRQUFRLENBQUMsTUFDbkI7UUFDQSxpREFBaUQ7UUFDakQsSUFBSXBCLFNBQVNsRCxNQUFNLEdBQUcsS0FBSztZQUN6QnBCLGlCQUFpQix3QkFBd0I7Z0JBQ3ZDVTtnQkFDQTBHLFlBQVk5QyxTQUFTbEQsTUFBTTtZQUM3QjtZQUNBLE9BQU8sSUFBSXRDLHFEQUFZQSxDQUFDLGVBQWU7Z0JBQUUyRixRQUFRO1lBQUk7UUFDdkQ7UUFFQVAsSUFBSUksUUFBUSxHQUFHLENBQUMsR0FBRyxFQUFFQSxTQUFTLENBQUM7UUFDL0IsT0FBT3hGLHFEQUFZQSxDQUFDdUksT0FBTyxDQUFDbkQ7SUFDOUI7SUFFQSwrREFBK0Q7SUFDL0QsSUFBSWpELEtBQXNDLElBQUk2RSxNQUFNO1FBQ2xEOUYsaUJBQWlCLHNCQUFzQjtZQUNyQ1U7WUFDQTREO1lBQ0E5QixRQUFRc0QsS0FBSy9ELEdBQUc7WUFDaEJDLE9BQU84RCxLQUFLOUQsS0FBSztRQUNuQjtJQUNGO0lBRUEsT0FBT3BELDREQUFVQSxDQUFDcUYsS0FBS2pGLG9EQUFVQTtBQUNuQztBQUVPLE1BQU1zSSxTQUFTO0lBQ3BCQyxTQUFTO1FBQ1A7Ozs7Ozs7O0tBUUMsR0FDRDtLQUNEO0FBQ0gsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvbWlkZGxld2FyZS5qcz9hNDlhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGkxOG5Sb3V0ZXIgfSBmcm9tIFwibmV4dC1pMThuLXJvdXRlclwiO1xyXG5pbXBvcnQgeyBqd3RWZXJpZnkgfSBmcm9tIFwiam9zZVwiO1xyXG5pbXBvcnQgeyBOZXh0UmVzcG9uc2UgfSBmcm9tIFwibmV4dC9zZXJ2ZXJcIjtcclxuaW1wb3J0IGNvb2tpZSBmcm9tIFwiY29va2llXCI7XHJcblxyXG5pbXBvcnQgaTE4bkNvbmZpZyBmcm9tIFwiLi4vaTE4bkNvbmZpZ1wiO1xyXG5pbXBvcnQgeyBSb2xlIH0gZnJvbSBcIi4vdXRpbHMvY29uc3RhbnRzXCI7XHJcbmltcG9ydCB7IGdldFJvdXRlc0xpc3RCeVJvbGUgfSBmcm9tIFwiLi91dGlscy9mdW5jdGlvbnNcIjtcclxuaW1wb3J0IHRyYW5zbGF0aW9ucyBmcm9tIFwiLi9jb25maWcvdHJhbnNsYXRpb25zXCI7XHJcbmltcG9ydCB7IGFsbG93ZWRQYXJhbXMgfSBmcm9tIFwiLi9jb25maWcvYWxsb3dlZFBhcmFtc1wiO1xyXG5pbXBvcnQge1xyXG4gIGFkbWluUm91dGVzLFxyXG4gIGF1dGhSb3V0ZXMsXHJcbiAgYmFzZVVybEJhY2tvZmZpY2UsXHJcbiAgYmFzZVVybEZyb250b2ZmaWNlLFxyXG4gIGNhbmRpZGF0ZVJvdXRlcyxcclxufSBmcm9tIFwiLi9oZWxwZXJzL3JvdXRlc0xpc3RcIjtcclxuXHJcbmNvbnN0IFNFQ1VSSVRZX0NPTkZJRyA9IHtcclxuICBNQVhfUkVRVUVTVFNfUEVSX01JTlVURTogNjAsXHJcbiAgSldUX0FMR09SSVRITTogXCJIUzI1NlwiLFxyXG4gIFNVU1BJQ0lPVVNfUEFUVEVSTlM6IFtcclxuICAgIC88c2NyaXB0XFxiW148XSooPzooPyE8XFwvc2NyaXB0Pik8W148XSopKjxcXC9zY3JpcHQ+L2dpLFxyXG4gICAgL2phdmFzY3JpcHQ6L2dpLFxyXG4gICAgL29uXFx3K1xccyo9L2dpLFxyXG4gICAgL2V2YWxcXHMqXFwoL2dpLFxyXG4gICAgL2V4cHJlc3Npb25cXHMqXFwoL2dpLFxyXG4gICAgLyUzQ3NjcmlwdC9naSxcclxuICAgIC8lM0MlMkZzY3JpcHQlM0UvZ2ksXHJcbiAgXSxcclxufTtcclxuXHJcbmNvbnN0IHJhdGVMaW1pdFN0b3JlID0gbmV3IE1hcCgpO1xyXG5cclxuY29uc3QgbG9nU2VjdXJpdHlFdmVudCA9IChldmVudCwgZGV0YWlscyA9IHt9KSA9PiB7XHJcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09IFwidW5kZWZpbmVkXCIpIHtcclxuICAgIGNvbnNvbGUud2FybihgW1NFQ1VSSVRZXSAke2V2ZW50fTpgLCB7XHJcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICAuLi5kZXRhaWxzLFxyXG4gICAgfSk7XHJcbiAgfVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IHZlcmlmeVRva2VuID0gYXN5bmMgKHRva2VuLCBjbGllbnRJUCA9IFwidW5rbm93blwiKSA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGlmICghdG9rZW4gfHwgdHlwZW9mIHRva2VuICE9PSBcInN0cmluZ1wiKSB7XHJcbiAgICAgIGxvZ1NlY3VyaXR5RXZlbnQoXCJJTlZBTElEX1RPS0VOX0ZPUk1BVFwiLCB7XHJcbiAgICAgICAgY2xpZW50SVAsXHJcbiAgICAgICAgcmVhc29uOiBcIk1pc3Npbmcgb3IgaW52YWxpZCB0b2tlblwiLFxyXG4gICAgICB9KTtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3Qgand0UmVnZXggPSAvXltBLVphLXowLTktX10rXFwuW0EtWmEtejAtOS1fXStcXC5bQS1aYS16MC05LV9dKiQvO1xyXG4gICAgaWYgKCFqd3RSZWdleC50ZXN0KHRva2VuKSkge1xyXG4gICAgICBsb2dTZWN1cml0eUV2ZW50KFwiSU5WQUxJRF9KV1RfRk9STUFUXCIsIHtcclxuICAgICAgICBjbGllbnRJUCxcclxuICAgICAgICB0b2tlblByZWZpeDogdG9rZW4uc3Vic3RyaW5nKDAsIDEwKSxcclxuICAgICAgfSk7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGp3dFNlY3JldCA9IHByb2Nlc3MuZW52Lk5FWFRfSldUX1NFQ1JFVDtcclxuICAgIGlmICghand0U2VjcmV0IHx8IGp3dFNlY3JldC5sZW5ndGggPCAzMikge1xyXG4gICAgICBsb2dTZWN1cml0eUV2ZW50KFwiV0VBS19KV1RfU0VDUkVUXCIsIHsgY2xpZW50SVAgfSk7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihcIkpXVCBzZWNyZXQgY29uZmlndXJhdGlvbiBlcnJvclwiKTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBzZWNyZXQgPSBuZXcgVGV4dEVuY29kZXIoKS5lbmNvZGUoand0U2VjcmV0KTtcclxuXHJcbiAgICBjb25zdCB7IHBheWxvYWQgfSA9IGF3YWl0IGp3dFZlcmlmeSh0b2tlbiwgc2VjcmV0LCB7XHJcbiAgICAgIGFsZ29yaXRobXM6IFtTRUNVUklUWV9DT05GSUcuSldUX0FMR09SSVRITV0sXHJcbiAgICAgIGlzc3VlcjogcHJvY2Vzcy5lbnYuSldUX0lTU1VFUiB8fCBcInBlbnRhYmVsbC1hcGlcIixcclxuICAgICAgYXVkaWVuY2U6IHByb2Nlc3MuZW52LkpXVF9BVURJRU5DRVxyXG4gICAgfSk7XHJcblxyXG4gICAgaWYgKFxyXG4gICAgICAhcGF5bG9hZCB8fFxyXG4gICAgICAhcGF5bG9hZC5faWQgfHxcclxuICAgICAgIXBheWxvYWQucm9sZXMgfHxcclxuICAgICAgIUFycmF5LmlzQXJyYXkocGF5bG9hZC5yb2xlcylcclxuICAgICkge1xyXG4gICAgICBsb2dTZWN1cml0eUV2ZW50KFwiSU5WQUxJRF9UT0tFTl9QQVlMT0FEXCIsIHtcclxuICAgICAgICBjbGllbnRJUCxcclxuICAgICAgICBoYXNJZDogISFwYXlsb2FkPy5faWQsXHJcbiAgICAgICAgaGFzUm9sZXM6ICEhcGF5bG9hZD8ucm9sZXMsXHJcbiAgICAgIH0pO1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCB0b2tlbkFnZSA9IERhdGUubm93KCkgLyAxMDAwIC0gKHBheWxvYWQuaWF0IHx8IDApO1xyXG4gICAgaWYgKHRva2VuQWdlID4gODY0MDApIHtcclxuICAgICAgbG9nU2VjdXJpdHlFdmVudChcIk9MRF9UT0tFTl9VU0FHRVwiLCB7XHJcbiAgICAgICAgY2xpZW50SVAsXHJcbiAgICAgICAgdG9rZW5BZ2UsXHJcbiAgICAgICAgdXNlcklkOiBwYXlsb2FkLl9pZCxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHBheWxvYWQ7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGlmIChlcnJvci5uYW1lID09PSBcIkpXVEV4cGlyZWRcIikge1xyXG4gICAgICBsb2dTZWN1cml0eUV2ZW50KFwiVE9LRU5fRVhQSVJFRFwiLCB7IGNsaWVudElQIH0pO1xyXG4gICAgfSBlbHNlIGlmIChlcnJvci5uYW1lID09PSBcIkpXVEludmFsaWRcIikge1xyXG4gICAgICBsb2dTZWN1cml0eUV2ZW50KFwiSU5WQUxJRF9UT0tFTlwiLCB7IGNsaWVudElQLCBlcnJvcjogZXJyb3IubWVzc2FnZSB9KTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGxvZ1NlY3VyaXR5RXZlbnQoXCJUT0tFTl9WRVJJRklDQVRJT05fRVJST1JcIiwge1xyXG4gICAgICAgIGNsaWVudElQLFxyXG4gICAgICAgIGVycm9yOiBlcnJvci5tZXNzYWdlLFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxufTtcclxuXHJcbmNvbnN0IGNoZWNrUmF0ZUxpbWl0ID0gKGNsaWVudElQKSA9PiB7XHJcbiAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcclxuICBjb25zdCB3aW5kb3dTdGFydCA9IG5vdyAtIDYwMDAwOyBcclxuXHJcbiAgaWYgKCFyYXRlTGltaXRTdG9yZS5oYXMoY2xpZW50SVApKSB7XHJcbiAgICByYXRlTGltaXRTdG9yZS5zZXQoY2xpZW50SVAsIFtdKTtcclxuICB9XHJcblxyXG4gIGNvbnN0IHJlcXVlc3RzID0gcmF0ZUxpbWl0U3RvcmUuZ2V0KGNsaWVudElQKTtcclxuXHJcbiAgY29uc3QgdmFsaWRSZXF1ZXN0cyA9IHJlcXVlc3RzLmZpbHRlcigodGltZXN0YW1wKSA9PiB0aW1lc3RhbXAgPiB3aW5kb3dTdGFydCk7XHJcbiAgcmF0ZUxpbWl0U3RvcmUuc2V0KGNsaWVudElQLCB2YWxpZFJlcXVlc3RzKTtcclxuXHJcbiAgaWYgKHZhbGlkUmVxdWVzdHMubGVuZ3RoID49IFNFQ1VSSVRZX0NPTkZJRy5NQVhfUkVRVUVTVFNfUEVSX01JTlVURSkge1xyXG4gICAgcmV0dXJuIGZhbHNlO1xyXG4gIH1cclxuXHJcbiAgdmFsaWRSZXF1ZXN0cy5wdXNoKG5vdyk7XHJcbiAgcmV0dXJuIHRydWU7XHJcbn07XHJcblxyXG5jb25zdCBzYW5pdGl6ZUlucHV0ID0gKHZhbHVlKSA9PiB7XHJcbiAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gXCJzdHJpbmdcIikgcmV0dXJuIHZhbHVlO1xyXG5cclxuICBsZXQgc2FuaXRpemVkID0gdmFsdWU7XHJcbiAgU0VDVVJJVFlfQ09ORklHLlNVU1BJQ0lPVVNfUEFUVEVSTlMuZm9yRWFjaCgocGF0dGVybikgPT4ge1xyXG4gICAgc2FuaXRpemVkID0gc2FuaXRpemVkLnJlcGxhY2UocGF0dGVybiwgXCJcIik7XHJcbiAgfSk7XHJcblxyXG4gIHJldHVybiBzYW5pdGl6ZWQudHJpbSgpO1xyXG59O1xyXG5cclxuY29uc3Qgc2V0U2VjdXJpdHlIZWFkZXJzID0gKHJlc3BvbnNlKSA9PiB7XHJcbiAgcmVzcG9uc2UuaGVhZGVycy5zZXQoXCJYLUNvbnRlbnQtVHlwZS1PcHRpb25zXCIsIFwibm9zbmlmZlwiKTtcclxuICByZXNwb25zZS5oZWFkZXJzLnNldChcIlgtRnJhbWUtT3B0aW9uc1wiLCBcIkRFTllcIik7XHJcbiAgcmVzcG9uc2UuaGVhZGVycy5zZXQoXCJYLVhTUy1Qcm90ZWN0aW9uXCIsIFwiMTsgbW9kZT1ibG9ja1wiKTtcclxuICByZXNwb25zZS5oZWFkZXJzLnNldChcIlJlZmVycmVyLVBvbGljeVwiLCBcInN0cmljdC1vcmlnaW4td2hlbi1jcm9zcy1vcmlnaW5cIik7XHJcbiAgcmVzcG9uc2UuaGVhZGVycy5zZXQoXHJcbiAgICBcIlBlcm1pc3Npb25zLVBvbGljeVwiLFxyXG4gICAgXCJnZW9sb2NhdGlvbj0oKSwgbWljcm9waG9uZT0oKSwgY2FtZXJhPSgpXCJcclxuICApO1xyXG5cclxuICByZXNwb25zZS5oZWFkZXJzLmRlbGV0ZShcIlNlcnZlclwiKTtcclxuICByZXNwb25zZS5oZWFkZXJzLmRlbGV0ZShcIlgtUG93ZXJlZC1CeVwiKTtcclxuXHJcbiAgcmV0dXJuIHJlc3BvbnNlO1xyXG59O1xyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIG1pZGRsZXdhcmUocmVxKSB7XHJcbiAgY29uc3QgdXJsID0gcmVxLm5leHRVcmwuY2xvbmUoKTtcclxuICBjb25zdCB7IGRlZmF1bHRMb2NhbGUgfSA9IGkxOG5Db25maWc7XHJcbiAgY29uc3QgeyBwYXRobmFtZSB9ID0gcmVxLm5leHRVcmw7XHJcbiAgY29uc3QgY2xpZW50SVAgPVxyXG4gICAgcmVxLmlwIHx8XHJcbiAgICByZXEuaGVhZGVycy5nZXQoXCJ4LWZvcndhcmRlZC1mb3JcIikgfHxcclxuICAgIHJlcS5oZWFkZXJzLmdldChcIngtcmVhbC1pcFwiKSB8fFxyXG4gICAgXCJ1bmtub3duXCI7XHJcblxyXG4gIGxldCByZXNwb25zZSA9IE5leHRSZXNwb25zZS5uZXh0KCk7XHJcbiAgcmVzcG9uc2UgPSBzZXRTZWN1cml0eUhlYWRlcnMocmVzcG9uc2UpO1xyXG5cclxuICBpZiAoIWNoZWNrUmF0ZUxpbWl0KGNsaWVudElQKSkge1xyXG4gICAgbG9nU2VjdXJpdHlFdmVudChcIlJBVEVfTElNSVRfRVhDRUVERURcIiwgeyBjbGllbnRJUCwgcGF0aG5hbWUgfSk7XHJcbiAgICByZXR1cm4gbmV3IE5leHRSZXNwb25zZShcIlRvbyBNYW55IFJlcXVlc3RzXCIsIHtcclxuICAgICAgc3RhdHVzOiA0MjksXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICBcIlJldHJ5LUFmdGVyXCI6IFwiNjBcIixcclxuICAgICAgICBcIlgtUmF0ZUxpbWl0LUxpbWl0XCI6IFNFQ1VSSVRZX0NPTkZJRy5NQVhfUkVRVUVTVFNfUEVSX01JTlVURS50b1N0cmluZygpLFxyXG4gICAgICAgIFwiWC1SYXRlTGltaXQtUmVtYWluaW5nXCI6IFwiMFwiLFxyXG4gICAgICB9LFxyXG4gICAgfSk7XHJcbiAgfVxyXG5cclxuICBsZXQgaGFzTW9kaWZpZWRQYXJhbXMgPSBmYWxzZTtcclxuICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiB1cmwuc2VhcmNoUGFyYW1zLmVudHJpZXMoKSkge1xyXG4gICAgY29uc3Qgc2FuaXRpemVkVmFsdWUgPSBzYW5pdGl6ZUlucHV0KHZhbHVlKTtcclxuICAgIGlmIChzYW5pdGl6ZWRWYWx1ZSAhPT0gdmFsdWUpIHtcclxuICAgICAgdXJsLnNlYXJjaFBhcmFtcy5zZXQoa2V5LCBzYW5pdGl6ZWRWYWx1ZSk7XHJcbiAgICAgIGhhc01vZGlmaWVkUGFyYW1zID0gdHJ1ZTtcclxuICAgICAgbG9nU2VjdXJpdHlFdmVudChcIlNVU1BJQ0lPVVNfUVVFUllfUEFSQU1cIiwge1xyXG4gICAgICAgIGNsaWVudElQLFxyXG4gICAgICAgIGtleSxcclxuICAgICAgICBvcmlnaW5hbFZhbHVlOiB2YWx1ZS5zdWJzdHJpbmcoMCwgNTApLFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IHBhdGhTdHJpbmcgPSBwYXRobmFtZS50b0xvd2VyQ2FzZSgpO1xyXG4gIGNvbnN0IGhhc1N1c3BpY2lvdXNQYXRoID0gU0VDVVJJVFlfQ09ORklHLlNVU1BJQ0lPVVNfUEFUVEVSTlMuc29tZShcclxuICAgIChwYXR0ZXJuKSA9PiBwYXR0ZXJuLnRlc3QocGF0aFN0cmluZylcclxuICApO1xyXG5cclxuICBpZiAoaGFzU3VzcGljaW91c1BhdGgpIHtcclxuICAgIGxvZ1NlY3VyaXR5RXZlbnQoXCJTVVNQSUNJT1VTX1BBVEhfQUNDRVNTXCIsIHsgY2xpZW50SVAsIHBhdGhuYW1lIH0pO1xyXG4gICAgcmV0dXJuIG5ldyBOZXh0UmVzcG9uc2UoXCJGb3JiaWRkZW5cIiwgeyBzdGF0dXM6IDQwMyB9KTtcclxuICB9XHJcblxyXG4gIGNvbnN0IGNvb2tpZXMgPSBjb29raWUucGFyc2UocmVxLmhlYWRlcnMuZ2V0KFwiY29va2llXCIpIHx8IFwiXCIpO1xyXG4gIGNvbnN0IHsgYWNjZXNzVG9rZW4sIHJlZnJlc2hUb2tlbiB9ID0gY29va2llcztcclxuXHJcbiAgY29uc3QgaXNQcm90ZWN0ZWRSb3V0ZSA9XHJcbiAgICBwYXRobmFtZS5pbmNsdWRlcyhcImRhc2hib2FyZFwiKSB8fCBwYXRobmFtZS5pbmNsdWRlcyhcImJhY2tvZmZpY2VcIik7XHJcblxyXG4gIGlmIChpc1Byb3RlY3RlZFJvdXRlICYmICEoYWNjZXNzVG9rZW4gJiYgcmVmcmVzaFRva2VuKSkge1xyXG4gICAgbG9nU2VjdXJpdHlFdmVudChcIlVOQVVUSE9SSVpFRF9BQ0NFU1NfQVRURU1QVFwiLCB7IGNsaWVudElQLCBwYXRobmFtZSB9KTtcclxuICAgIHVybC5wYXRobmFtZSA9IGAvJHthdXRoUm91dGVzLmxvZ2luLnJvdXRlfS9gO1xyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdCh1cmwpO1xyXG4gIH1cclxuXHJcbiAgbGV0IHVzZXIgPSBudWxsO1xyXG4gIGlmIChyZWZyZXNoVG9rZW4pIHtcclxuICAgIHVzZXIgPSBhd2FpdCB2ZXJpZnlUb2tlbihyZWZyZXNoVG9rZW4sIGNsaWVudElQKTtcclxuXHJcbiAgICBpZiAoaXNQcm90ZWN0ZWRSb3V0ZSAmJiAhdXNlcikge1xyXG4gICAgICBsb2dTZWN1cml0eUV2ZW50KFwiSU5WQUxJRF9UT0tFTl9QUk9URUNURURfUk9VVEVcIiwgeyBjbGllbnRJUCwgcGF0aG5hbWUgfSk7XHJcbiAgICAgIHVybC5wYXRobmFtZSA9IGAvJHthdXRoUm91dGVzLmxvZ2luLnJvdXRlfS9gO1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLnJlZGlyZWN0KHVybCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBIYW5kbGUgbG9nb3V0IHJvdXRlXHJcbiAgaWYgKFxyXG4gICAgcGF0aG5hbWUgPT09IGAvJHthdXRoUm91dGVzLmxvZ291dC5yb3V0ZX0vYCB8fFxyXG4gICAgcGF0aG5hbWUgPT09IGAvZnIvJHthdXRoUm91dGVzLmxvZ291dC5yb3V0ZX0vYFxyXG4gICkge1xyXG4gICAgbG9nU2VjdXJpdHlFdmVudChcIlVTRVJfTE9HT1VUXCIsIHsgY2xpZW50SVAsIHVzZXJJZDogdXNlcj8uX2lkIH0pO1xyXG4gICAgcmV0dXJuIGkxOG5Sb3V0ZXIocmVxLCBpMThuQ29uZmlnKTtcclxuICB9XHJcblxyXG4gIC8vIEVuaGFuY2VkIHJvbGUtYmFzZWQgYWNjZXNzIGNvbnRyb2xcclxuICBpZiAodXNlcikge1xyXG4gICAgY29uc3QgbWVudUxpc3QgPSBnZXRSb3V0ZXNMaXN0QnlSb2xlKHVzZXIpO1xyXG5cclxuICAgIC8vIFZhbGlkYXRlIHVzZXIgcm9sZXNcclxuICAgIGlmICghdXNlci5yb2xlcyB8fCAhQXJyYXkuaXNBcnJheSh1c2VyLnJvbGVzKSB8fCB1c2VyLnJvbGVzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICBsb2dTZWN1cml0eUV2ZW50KFwiSU5WQUxJRF9VU0VSX1JPTEVTXCIsIHsgY2xpZW50SVAsIHVzZXJJZDogdXNlci5faWQgfSk7XHJcbiAgICAgIHVybC5wYXRobmFtZSA9IGAvJHthdXRoUm91dGVzLmxvZ2luLnJvdXRlfS9gO1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLnJlZGlyZWN0KHVybCk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgY2hlY2tSb2xlID1cclxuICAgICAgIW1lbnVMaXN0Py5zb21lKChpdGVtKSA9PiBwYXRobmFtZS5pbmNsdWRlcyhpdGVtKSkgJiZcclxuICAgICAgKHBhdGhuYW1lPy5pbmNsdWRlcyhgLyR7YmFzZVVybEZyb250b2ZmaWNlLmJhc2VVUkwucm91dGV9YCkgfHxcclxuICAgICAgICBwYXRobmFtZT8uaW5jbHVkZXMoYC8ke2Jhc2VVcmxCYWNrb2ZmaWNlLmJhc2VVUkwucm91dGV9YCkpO1xyXG5cclxuICAgIC8vIEVuaGFuY2VkIHJvbGUgY2hlY2tpbmcgd2l0aCBzZWN1cml0eSBsb2dnaW5nXHJcbiAgICBpZiAoXHJcbiAgICAgIGNoZWNrUm9sZSB8fFxyXG4gICAgICBwYXRobmFtZSA9PT0gYC9mci8ke2F1dGhSb3V0ZXMucmVnaXN0ZXIucm91dGV9L2AgfHxcclxuICAgICAgcGF0aG5hbWUgPT09IGAvZnIvJHthdXRoUm91dGVzLmxvZ2luLnJvdXRlfS9gIHx8XHJcbiAgICAgIHBhdGhuYW1lID09PSBgLyR7YXV0aFJvdXRlcy5yZWdpc3Rlci5yb3V0ZX0vYCB8fFxyXG4gICAgICBwYXRobmFtZSA9PT0gYC8ke2F1dGhSb3V0ZXMubG9naW4ucm91dGV9L2BcclxuICAgICkge1xyXG4gICAgICBsZXQgcmVkaXJlY3RQYXRoID0gbnVsbDtcclxuXHJcbiAgICAgIGlmICh1c2VyLnJvbGVzLmluY2x1ZGVzKFJvbGUuQURNSU4pKSB7XHJcbiAgICAgICAgcmVkaXJlY3RQYXRoID0gYC8ke2Jhc2VVcmxCYWNrb2ZmaWNlLmJhc2VVUkwucm91dGV9LyR7YWRtaW5Sb3V0ZXMuaG9tZS5yb3V0ZX0vYDtcclxuICAgICAgfSBlbHNlIGlmICh1c2VyLnJvbGVzLmluY2x1ZGVzKFJvbGUuRURJVE9SKSkge1xyXG4gICAgICAgIHJlZGlyZWN0UGF0aCA9IGAvJHtiYXNlVXJsQmFja29mZmljZS5iYXNlVVJMLnJvdXRlfS8ke2FkbWluUm91dGVzLmJsb2dzLnJvdXRlfS9gO1xyXG4gICAgICB9IGVsc2UgaWYgKHVzZXIucm9sZXMuaW5jbHVkZXMoUm9sZS5DQU5ESURBVEUpKSB7XHJcbiAgICAgICAgcmVkaXJlY3RQYXRoID0gYC8ke2Jhc2VVcmxGcm9udG9mZmljZS5iYXNlVVJMLnJvdXRlfS8ke2NhbmRpZGF0ZVJvdXRlcy5teUFwcGxpY2F0aW9ucy5yb3V0ZX1gO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAocmVkaXJlY3RQYXRoKSB7XHJcbiAgICAgICAgbG9nU2VjdXJpdHlFdmVudChcIlJPTEVfQkFTRURfUkVESVJFQ1RcIiwge1xyXG4gICAgICAgICAgY2xpZW50SVAsXHJcbiAgICAgICAgICB1c2VySWQ6IHVzZXIuX2lkLFxyXG4gICAgICAgICAgcm9sZXM6IHVzZXIucm9sZXMsXHJcbiAgICAgICAgICBmcm9tUGF0aDogcGF0aG5hbWUsXHJcbiAgICAgICAgICB0b1BhdGg6IHJlZGlyZWN0UGF0aCxcclxuICAgICAgICB9KTtcclxuICAgICAgICB1cmwucGF0aG5hbWUgPSByZWRpcmVjdFBhdGg7XHJcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdCh1cmwpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGxvZ1NlY3VyaXR5RXZlbnQoXCJOT19WQUxJRF9ST0xFX1JFRElSRUNUXCIsIHtcclxuICAgICAgICAgIGNsaWVudElQLFxyXG4gICAgICAgICAgdXNlcklkOiB1c2VyLl9pZCxcclxuICAgICAgICAgIHJvbGVzOiB1c2VyLnJvbGVzLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHVybC5wYXRobmFtZSA9IGAvJHthdXRoUm91dGVzLmxvZ2luLnJvdXRlfS9gO1xyXG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QodXJsKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gRW5oYW5jZWQgcGFyYW1ldGVyIGZpbHRlcmluZyB3aXRoIHNlY3VyaXR5IGxvZ2dpbmdcclxuICBsZXQgcmVtb3ZlZFBhcmFtcyA9IFtdO1xyXG4gIGZvciAoY29uc3QgcGFyYW0gb2YgdXJsLnNlYXJjaFBhcmFtcy5rZXlzKCkpIHtcclxuICAgIGlmICghYWxsb3dlZFBhcmFtcy5oYXMocGFyYW0pKSB7XHJcbiAgICAgIHVybC5zZWFyY2hQYXJhbXMuZGVsZXRlKHBhcmFtKTtcclxuICAgICAgcmVtb3ZlZFBhcmFtcy5wdXNoKHBhcmFtKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGlmIChyZW1vdmVkUGFyYW1zLmxlbmd0aCA+IDApIHtcclxuICAgIGxvZ1NlY3VyaXR5RXZlbnQoXCJSRU1PVkVEX0RJU0FMTE9XRURfUEFSQU1TXCIsIHtcclxuICAgICAgY2xpZW50SVAsXHJcbiAgICAgIHBhdGhuYW1lLFxyXG4gICAgICByZW1vdmVkUGFyYW1zLFxyXG4gICAgICB1c2VySWQ6IHVzZXI/Ll9pZCxcclxuICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgLy8gQ2hlY2sgaWYgcGFyYW1ldGVycyB3ZXJlIG1vZGlmaWVkIChlaXRoZXIgc2FuaXRpemVkIG9yIHJlbW92ZWQpXHJcbiAgaWYgKFxyXG4gICAgaGFzTW9kaWZpZWRQYXJhbXMgfHxcclxuICAgIHVybC5zZWFyY2hQYXJhbXMudG9TdHJpbmcoKSAhPT0gcmVxLm5leHRVcmwuc2VhcmNoUGFyYW1zLnRvU3RyaW5nKClcclxuICApIHtcclxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QodXJsKTtcclxuICB9XHJcblxyXG4gIC8vIEVuaGFuY2VkIHJlZGlyZWN0aW9uIHBhdGhzIHdpdGggc2VjdXJpdHkgY2hlY2tzXHJcbiAgY29uc3QgZnJQYXRoID0gdHJhbnNsYXRpb25zW3JlcS5uZXh0VXJsLnBhdGhuYW1lXTtcclxuICBpZiAoZnJQYXRoKSB7XHJcbiAgICBsb2dTZWN1cml0eUV2ZW50KFwiRlJFTkNIX1BBVEhfUkVESVJFQ1RcIiwge1xyXG4gICAgICBjbGllbnRJUCxcclxuICAgICAgZnJvbVBhdGg6IHJlcS5uZXh0VXJsLnBhdGhuYW1lLFxyXG4gICAgICB0b1BhdGg6IGZyUGF0aCxcclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdChuZXcgVVJMKGZyUGF0aCwgcmVxLnVybCkpO1xyXG4gIH1cclxuXHJcbiAgLy8gRW5oYW5jZWQgbGFuZ3VhZ2UgaGFuZGxpbmcgd2l0aCBzZWN1cml0eSB2YWxpZGF0aW9uXHJcbiAgaWYgKFxyXG4gICAgIXBhdGhuYW1lLnN0YXJ0c1dpdGgoXCIvZnJcIikgJiZcclxuICAgICFwYXRobmFtZS5zdGFydHNXaXRoKGAvJHtkZWZhdWx0TG9jYWxlfWApICYmXHJcbiAgICAhcGF0aG5hbWUuc3RhcnRzV2l0aChcIi9fbmV4dFwiKSAmJlxyXG4gICAgIXBhdGhuYW1lLnN0YXJ0c1dpdGgoXCIvYXBpXCIpICYmXHJcbiAgICAhcGF0aG5hbWUuc3RhcnRzV2l0aChcIi9zdGF0aWNcIikgJiZcclxuICAgICFwYXRobmFtZS5pbmNsdWRlcyhcIi5cIilcclxuICApIHtcclxuICAgIC8vIEFkZGl0aW9uYWwgc2VjdXJpdHkgY2hlY2sgZm9yIHN1c3BpY2lvdXMgcGF0aHNcclxuICAgIGlmIChwYXRobmFtZS5sZW5ndGggPiAyMDApIHtcclxuICAgICAgbG9nU2VjdXJpdHlFdmVudChcIlNVU1BJQ0lPVVNfTE9OR19QQVRIXCIsIHtcclxuICAgICAgICBjbGllbnRJUCxcclxuICAgICAgICBwYXRoTGVuZ3RoOiBwYXRobmFtZS5sZW5ndGgsXHJcbiAgICAgIH0pO1xyXG4gICAgICByZXR1cm4gbmV3IE5leHRSZXNwb25zZShcIkJhZCBSZXF1ZXN0XCIsIHsgc3RhdHVzOiA0MDAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgdXJsLnBhdGhuYW1lID0gYC9lbiR7cGF0aG5hbWV9YDtcclxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmV3cml0ZSh1cmwpO1xyXG4gIH1cclxuXHJcbiAgLy8gTG9nIHN1Y2Nlc3NmdWwgcmVxdWVzdHMgZm9yIG1vbml0b3JpbmcgKGluIGRldmVsb3BtZW50IG9ubHkpXHJcbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcImRldmVsb3BtZW50XCIgJiYgdXNlcikge1xyXG4gICAgbG9nU2VjdXJpdHlFdmVudChcIlNVQ0NFU1NGVUxfUkVRVUVTVFwiLCB7XHJcbiAgICAgIGNsaWVudElQLFxyXG4gICAgICBwYXRobmFtZSxcclxuICAgICAgdXNlcklkOiB1c2VyLl9pZCxcclxuICAgICAgcm9sZXM6IHVzZXIucm9sZXMsXHJcbiAgICB9KTtcclxuICB9XHJcblxyXG4gIHJldHVybiBpMThuUm91dGVyKHJlcSwgaTE4bkNvbmZpZyk7XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBjb25maWcgPSB7XHJcbiAgbWF0Y2hlcjogW1xyXG4gICAgLypcclxuICAgICAqIE1hdGNoIGFsbCByZXF1ZXN0IHBhdGhzIGV4Y2VwdCBmb3IgdGhlIG9uZXMgc3RhcnRpbmcgd2l0aDpcclxuICAgICAqIC0gYXBpIChBUEkgcm91dGVzKVxyXG4gICAgICogLSBfbmV4dC9zdGF0aWMgKHN0YXRpYyBmaWxlcylcclxuICAgICAqIC0gX25leHQvaW1hZ2UgKGltYWdlIG9wdGltaXphdGlvbiBmaWxlcylcclxuICAgICAqIC0gZmF2aWNvbi5pY28gKGZhdmljb24gZmlsZSlcclxuICAgICAqIC0gcHVibGljIGZvbGRlclxyXG4gICAgICogLSBmaWxlcyB3aXRoIGV4dGVuc2lvbnMgKGltYWdlcywgZm9udHMsIGV0Yy4pXHJcbiAgICAgKi9cclxuICAgIFwiLygoPyFhcGl8X25leHQvc3RhdGljfF9uZXh0L2ltYWdlfGZhdmljb24uaWNvfHB1YmxpY3wuKlxcXFwuLiopLiopXCIsXHJcbiAgXSxcclxufTtcclxuIl0sIm5hbWVzIjpbImkxOG5Sb3V0ZXIiLCJqd3RWZXJpZnkiLCJOZXh0UmVzcG9uc2UiLCJjb29raWUiLCJpMThuQ29uZmlnIiwiUm9sZSIsImdldFJvdXRlc0xpc3RCeVJvbGUiLCJ0cmFuc2xhdGlvbnMiLCJhbGxvd2VkUGFyYW1zIiwiYWRtaW5Sb3V0ZXMiLCJhdXRoUm91dGVzIiwiYmFzZVVybEJhY2tvZmZpY2UiLCJiYXNlVXJsRnJvbnRvZmZpY2UiLCJjYW5kaWRhdGVSb3V0ZXMiLCJTRUNVUklUWV9DT05GSUciLCJNQVhfUkVRVUVTVFNfUEVSX01JTlVURSIsIkpXVF9BTEdPUklUSE0iLCJTVVNQSUNJT1VTX1BBVFRFUk5TIiwicmF0ZUxpbWl0U3RvcmUiLCJNYXAiLCJsb2dTZWN1cml0eUV2ZW50IiwiZXZlbnQiLCJkZXRhaWxzIiwiY29uc29sZSIsIndhcm4iLCJ0aW1lc3RhbXAiLCJEYXRlIiwidG9JU09TdHJpbmciLCJ2ZXJpZnlUb2tlbiIsInRva2VuIiwiY2xpZW50SVAiLCJyZWFzb24iLCJqd3RSZWdleCIsInRlc3QiLCJ0b2tlblByZWZpeCIsInN1YnN0cmluZyIsImp3dFNlY3JldCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX0pXVF9TRUNSRVQiLCJsZW5ndGgiLCJFcnJvciIsInNlY3JldCIsIlRleHRFbmNvZGVyIiwiZW5jb2RlIiwicGF5bG9hZCIsImFsZ29yaXRobXMiLCJpc3N1ZXIiLCJKV1RfSVNTVUVSIiwiYXVkaWVuY2UiLCJKV1RfQVVESUVOQ0UiLCJfaWQiLCJyb2xlcyIsIkFycmF5IiwiaXNBcnJheSIsImhhc0lkIiwiaGFzUm9sZXMiLCJ0b2tlbkFnZSIsIm5vdyIsImlhdCIsInVzZXJJZCIsImVycm9yIiwibmFtZSIsIm1lc3NhZ2UiLCJjaGVja1JhdGVMaW1pdCIsIndpbmRvd1N0YXJ0IiwiaGFzIiwic2V0IiwicmVxdWVzdHMiLCJnZXQiLCJ2YWxpZFJlcXVlc3RzIiwiZmlsdGVyIiwicHVzaCIsInNhbml0aXplSW5wdXQiLCJ2YWx1ZSIsInNhbml0aXplZCIsImZvckVhY2giLCJwYXR0ZXJuIiwicmVwbGFjZSIsInRyaW0iLCJzZXRTZWN1cml0eUhlYWRlcnMiLCJyZXNwb25zZSIsImhlYWRlcnMiLCJkZWxldGUiLCJtaWRkbGV3YXJlIiwicmVxIiwidXJsIiwibmV4dFVybCIsImNsb25lIiwiZGVmYXVsdExvY2FsZSIsInBhdGhuYW1lIiwiaXAiLCJuZXh0Iiwic3RhdHVzIiwidG9TdHJpbmciLCJoYXNNb2RpZmllZFBhcmFtcyIsImtleSIsInNlYXJjaFBhcmFtcyIsImVudHJpZXMiLCJzYW5pdGl6ZWRWYWx1ZSIsIm9yaWdpbmFsVmFsdWUiLCJwYXRoU3RyaW5nIiwidG9Mb3dlckNhc2UiLCJoYXNTdXNwaWNpb3VzUGF0aCIsInNvbWUiLCJjb29raWVzIiwicGFyc2UiLCJhY2Nlc3NUb2tlbiIsInJlZnJlc2hUb2tlbiIsImlzUHJvdGVjdGVkUm91dGUiLCJpbmNsdWRlcyIsImxvZ2luIiwicm91dGUiLCJyZWRpcmVjdCIsInVzZXIiLCJsb2dvdXQiLCJtZW51TGlzdCIsImNoZWNrUm9sZSIsIml0ZW0iLCJiYXNlVVJMIiwicmVnaXN0ZXIiLCJyZWRpcmVjdFBhdGgiLCJBRE1JTiIsImhvbWUiLCJFRElUT1IiLCJibG9ncyIsIkNBTkRJREFURSIsIm15QXBwbGljYXRpb25zIiwiZnJvbVBhdGgiLCJ0b1BhdGgiLCJyZW1vdmVkUGFyYW1zIiwicGFyYW0iLCJrZXlzIiwiZnJQYXRoIiwiVVJMIiwic3RhcnRzV2l0aCIsInBhdGhMZW5ndGgiLCJyZXdyaXRlIiwiY29uZmlnIiwibWF0Y2hlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.js\n");

/***/ })

});