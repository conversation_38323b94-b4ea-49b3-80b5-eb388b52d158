(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5732],{23913:function(e,a,t){"use strict";var s,n,r,l=t(94746);function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var s in t)({}).hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(null,arguments)}a.Z=e=>l.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:48,height:49,fill:"none"},e),s||(s=l.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M24 25a1.5 1.5 0 0 1 1.5 1.5v16a1.5 1.5 0 0 1-3 0v-16A1.5 1.5 0 0 1 24 25",clipRule:"evenodd"})),n||(n=l.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M22.573 8.869A12.5 12.5 0 0 0 9.072 29.247a1.5 1.5 0 1 1-2.144 2.099 15.5 15.5 0 1 1 25.566-16.347H35a10.501 10.501 0 0 1 5.834 19.23 1.5 1.5 0 1 1-1.666-2.494A7.5 7.5 0 0 0 35 18h-3.58a1.5 1.5 0 0 1-1.438-1.071 12.5 12.5 0 0 0-7.409-8.06",clipRule:"evenodd"})),r||(r=l.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M22.94 25.44a1.5 1.5 0 0 1 2.12 0l8 8a1.5 1.5 0 0 1-2.12 2.12L24 28.622l-6.94 6.94a1.5 1.5 0 0 1-2.12-2.122z",clipRule:"evenodd"})))},99326:function(e,a,t){Promise.resolve().then(t.bind(t,27192)),Promise.resolve().then(t.bind(t,87518)),Promise.resolve().then(t.bind(t,47341))},98489:function(e,a,t){"use strict";t.d(a,{default:function(){return v}});var s=t(2265),n=t(61994),r=t(50738),l=t(20801),i=t(4647),o=t(20956),c=t(95045),d=t(58698),u=t(57437);let m=(0,d.Z)(),p=(0,c.Z)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(e,a)=>{let{ownerState:t}=e;return[a.root,a[`maxWidth${(0,i.Z)(String(t.maxWidth))}`],t.fixed&&a.fixed,t.disableGutters&&a.disableGutters]}}),h=e=>(0,o.Z)({props:e,name:"MuiContainer",defaultTheme:m}),f=(e,a)=>{let{classes:t,fixed:s,disableGutters:n,maxWidth:o}=e,c={root:["root",o&&`maxWidth${(0,i.Z)(String(o))}`,s&&"fixed",n&&"disableGutters"]};return(0,l.Z)(c,e=>(0,r.ZP)(a,e),t)};var x=t(85657),g=t(16210),j=t(37053),v=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{createStyledComponent:a=p,useThemeProps:t=h,componentName:r="MuiContainer"}=e,l=a(e=>{let{theme:a,ownerState:t}=e;return{width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",...!t.disableGutters&&{paddingLeft:a.spacing(2),paddingRight:a.spacing(2),[a.breakpoints.up("sm")]:{paddingLeft:a.spacing(3),paddingRight:a.spacing(3)}}}},e=>{let{theme:a,ownerState:t}=e;return t.fixed&&Object.keys(a.breakpoints.values).reduce((e,t)=>{let s=a.breakpoints.values[t];return 0!==s&&(e[a.breakpoints.up(t)]={maxWidth:`${s}${a.breakpoints.unit}`}),e},{})},e=>{let{theme:a,ownerState:t}=e;return{..."xs"===t.maxWidth&&{[a.breakpoints.up("xs")]:{maxWidth:Math.max(a.breakpoints.values.xs,444)}},...t.maxWidth&&"xs"!==t.maxWidth&&{[a.breakpoints.up(t.maxWidth)]:{maxWidth:`${a.breakpoints.values[t.maxWidth]}${a.breakpoints.unit}`}}}});return s.forwardRef(function(e,a){let s=t(e),{className:i,component:o="div",disableGutters:c=!1,fixed:d=!1,maxWidth:m="lg",classes:p,...h}=s,x={...s,component:o,disableGutters:c,fixed:d,maxWidth:m},g=f(x,r);return(0,u.jsx)(l,{as:o,ownerState:x,className:(0,n.Z)(g.root,i),ref:a,...h})})}({createStyledComponent:(0,g.ZP)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(e,a)=>{let{ownerState:t}=e;return[a.root,a[`maxWidth${(0,x.Z)(String(t.maxWidth))}`],t.fixed&&a.fixed,t.disableGutters&&a.disableGutters]}}),useThemeProps:e=>(0,j.i)({props:e,name:"MuiContainer"})})},95045:function(e,a,t){"use strict";let s=(0,t(29418).ZP)();a.Z=s},93826:function(e,a,t){"use strict";t.d(a,{Z:function(){return n}});var s=t(53232);function n(e){let{theme:a,name:t,props:n}=e;return a&&a.components&&a.components[t]&&a.components[t].defaultProps?(0,s.Z)(a.components[t].defaultProps,n):n}},20956:function(e,a,t){"use strict";t.d(a,{Z:function(){return r}});var s=t(93826),n=t(49695);function r(e){let{props:a,name:t,defaultTheme:r,themeId:l}=e,i=(0,n.Z)(r);return l&&(i=i[l]||i),(0,s.Z)({theme:i,name:t,props:a})}},64821:function(e,a,t){"use strict";var s=t(13859),n=t(53731);a.isCompanyEmail=function(e){if(!n.validate(e))return!1;let a=e.split("@")[1];return!s.has(a)},a.isCompanyDomain=function(e){return!s.has(e)}},53731:function(e,a){"use strict";var t=/^[-!#$%&'*+\/0-9=?A-Z^_a-z{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/;a.validate=function(e){if(!e||e.length>254||!t.test(e))return!1;var a=e.split("@");return!(a[0].length>64||a[1].split(".").some(function(e){return e.length>63}))}},69780:function(e,a,t){"use strict";var s,n=(s=t(78227))&&s.__esModule?s:{default:s};e.exports={tags:function(e){var a=e.id,t=e.events,s=e.dataLayer,r=e.dataLayerName,l=e.preview,i="&gtm_auth="+e.auth,o="&gtm_preview="+l;a||(0,n.default)("GTM Id is required");var c="\n      (function(w,d,s,l,i){w[l]=w[l]||[];\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js', "+JSON.stringify(t).slice(1,-1)+"});\n        var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';\n        j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl+'"+i+o+"&gtm_cookies_win=x';\n        f.parentNode.insertBefore(j,f);\n      })(window,document,'script','"+r+"','"+a+"');";return{iframe:'\n      <iframe src="https://www.googletagmanager.com/ns.html?id='+a+i+o+'&gtm_cookies_win=x"\n        height="0" width="0" style="display:none;visibility:hidden" id="tag-manager"></iframe>',script:c,dataLayerVar:this.dataLayer(s,r)}},dataLayer:function(e,a){return"\n      window."+a+" = window."+a+" || [];\n      window."+a+".push("+JSON.stringify(e)+")"}}},90761:function(e,a,t){"use strict";var s,n=(s=t(69780))&&s.__esModule?s:{default:s};e.exports={dataScript:function(e){var a=document.createElement("script");return a.innerHTML=e,a},gtm:function(e){var a=n.default.tags(e);return{noScript:function(){var e=document.createElement("noscript");return e.innerHTML=a.iframe,e},script:function(){var e=document.createElement("script");return e.innerHTML=a.script,e},dataScript:this.dataScript(a.dataLayerVar)}},initialize:function(e){var a=e.gtmId,t=e.events,s=e.dataLayer,n=e.dataLayerName,r=e.auth,l=e.preview,i=this.gtm({id:a,events:void 0===t?{}:t,dataLayer:s||void 0,dataLayerName:void 0===n?"dataLayer":n,auth:void 0===r?"":r,preview:void 0===l?"":l});s&&document.head.appendChild(i.dataScript),document.head.insertBefore(i.script(),document.head.childNodes[0]),document.body.insertBefore(i.noScript(),document.body.childNodes[0])},dataLayer:function(e){var a=e.dataLayer,t=e.dataLayerName,s=void 0===t?"dataLayer":t;if(window[s])return window[s].push(a);var r=n.default.dataLayer(a,s),l=this.dataScript(r);document.head.insertBefore(l,document.head.childNodes[0])}}},4828:function(e,a,t){"use strict";var s,n=(s=t(90761))&&s.__esModule?s:{default:s};e.exports=n.default},78227:function(e,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=function(e){console.warn("[react-gtm]",e)}},27192:function(e,a,t){"use strict";var s=t(57437),n=t(98489),r=t(88833),l=t(97160),i=t(87311),o=t(55788);a.default=function(){let{t:e}=(0,o.$G)();return(0,s.jsxs)(n.default,{id:"locations-section",className:"custom-max-width",children:[(0,s.jsx)("h2",{className:"heading-h1",children:e("contactUs:locations:title")}),(0,s.jsx)("p",{className:"sub-heading",children:e("contactUs:locations:description")}),(0,s.jsxs)("div",{className:"locations",children:[(0,s.jsxs)("div",{className:"location-item",children:[(0,s.jsxs)("p",{className:"label",children:[" ",(0,s.jsx)(r.default,{})," ",e("contactUs:locations:address")]}),(0,s.jsx)("p",{className:"value paragraph",children:e("contactUs:locations:addressDescription")})]}),(0,s.jsxs)("div",{className:"location-item",children:[(0,s.jsxs)("p",{className:"label",children:[" ",(0,s.jsx)(l.default,{})," ",e("contactUs:locations:call")]}),(0,s.jsx)("p",{className:"value paragraph",children:"+33 1 73 07 42 54"})]}),(0,s.jsxs)("div",{className:"location-item",children:[(0,s.jsxs)("p",{className:"label",children:[" ",(0,s.jsx)(i.default,{})," Email"]}),(0,s.jsx)("p",{className:"value paragraph",children:"<EMAIL>"})]})]})]})}},47341:function(e,a,t){"use strict";var s=t(57437),n=t(98489),r=t(81799),l=t(15735),i=t(63993),o=t(89414),c=t(89126),d=t(64393),u=t(77584),m=t(85860),p=t(11953),h=t(49651);t(25330);var f=t(34422),x=t(24086),g=t(48658),j=t(41774),v=t(2265),b=t(55788),N=t(50933),y=t(93770),Z=t(30100),w=t(28397),C=t(75638),S=t(64821),T=t(23913),L=t(62953),_=t(49360),I=t(88415),A=t(40257);a.default=function(){let e,a;let[t,E]=(0,v.useState)(""),[k,M]=(0,v.useState)(!1),O=h.PhoneNumberUtil.getInstance(),[R,B]=(0,v.useState)(!1),[F,P]=(0,v.useState)(null),[$,W]=(0,v.useState)(""),{t:U}=(0,b.$G)(),[z,G]=(0,v.useState)(""),D=(0,N.uu)(M,E),q=(0,L.jd)(),H=new FormData,V=(t,s)=>{t.preventDefault(),e=(0,_.Z)().replace(/-/g,""),W(""),P(null);let n=t.target.files[0];if(n){H.append("file",n);let t=n.name.split(".").pop();a=`${e}.${t}`;let r=new Date().getFullYear();q.mutate({resource:"candidates",folder:r,filename:e,body:{formData:H,t:U}},{onSuccess:e=>{"uuid exist"===e.message?(P(e.uuid),s("resume",e.uuid)):(P(a),s("resume",a))},onError:e=>{400===e.response.data.status?W(U("messages:requireResume")):500===e.response.data.status?W("Internal Server Error"):W(e.response.data.message)}})}},J=async(e,a)=>{let{resetForm:t}=a,s={...Object.fromEntries(Object.entries(e).filter(e=>{let[a,t]=e;return"acceptTerms"!==a&&""!==t&&null!=t}))};await D.mutateAsync({...s,to:`${A.env.NEXT_PUBLIC_EMAIL_FORM_DESTINATION}`,team:"digital",type:"getInTouchContact"}),G(e.fullName),t(),P(null),window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"Contact_form",button_id:"contact_form_button"})},K=e=>{try{return O.isValidNumber(O.parseAndKeepRawInput(e))}catch(e){return!1}},X=f.Z_().test("is-valid-phone",U("validations:phoneFormat"),e=>K(e)),Y=e=>(0,y.ie)(U).shape({phone:X,email:f.Z_().email(U("validations:invalidEmail")).required(U("validations:required")).test("is-company-email",U("validations:companyEmailRequired"),function(e){let{youAre:a}=this.parent;return!!e&&("Company"!==a||S.isCompanyEmail(e))})});return(0,s.jsxs)("div",{id:"contact-page-form",children:[(0,s.jsx)(C.Z,{}),(0,s.jsx)(n.default,{className:"custom-max-width",children:k?(0,s.jsx)("div",{className:"section-guide",children:(0,s.jsxs)("div",{className:"form-success",children:[(0,s.jsx)(g.Z,{}),(0,s.jsxs)("p",{className:"sub-heading",children:[" ",U("messages:thankyou")," ",z," ",U("messages:messagesuccess")]})]})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h2",{className:"heading-h1",children:U("getInTouch:getInTouch")}),(0,s.jsx)("p",{className:"sub-heading",children:U("getInTouch:description")}),(0,s.jsx)(i.J9,{initialValues:{fullName:"",email:"",phone:"",youAre:"",subject:"",message:"",country:"",resume:"",field:"",acceptTerms:!1},validationSchema:()=>Y(R),onSubmit:J,children:e=>{let{values:a,handleChange:t,errors:n,touched:h,setFieldValue:f}=e;return(0,s.jsx)(i.l0,{className:"pentabell-form",children:(0,s.jsxs)(o.default,{container:!0,rowSpacing:4,columnSpacing:3,children:[(0,s.jsxs)(o.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(c.Z,{className:"form-group ",children:[(0,s.jsxs)(d.Z,{className:"label-pentabell ",children:[U("getInTouch:fullName"),"*"]}),(0,s.jsx)(u.Z,{autoComplete:"off",className:"input-pentabell ",placeholder:U("getInTouch:fullName"),variant:"standard",type:"text",name:"fullName",value:a.fullName,onChange:t,error:!!(n.fullName&&h.fullName)})]}),(0,s.jsx)(i.Bc,{name:"fullName",children:e=>(0,s.jsx)("span",{className:"error-span",children:e})})]}),(0,s.jsxs)(o.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(c.Z,{className:"form-group ",children:[(0,s.jsxs)(d.Z,{className:"label-pentabell ",children:[U("getInTouch:email"),"*"]}),(0,s.jsx)(u.Z,{autoComplete:"off",className:"input-pentabell ",placeholder:U("getInTouch:email"),variant:"standard",type:"email",name:"email",value:a.email,onChange:t,error:!!(n.email&&h.email)})]}),(0,s.jsx)(i.Bc,{name:"email",children:e=>(0,s.jsx)("span",{className:"error-span",children:e})})]}),(0,s.jsxs)(o.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(c.Z,{className:"form-group ",children:[(0,s.jsx)(d.Z,{className:"label-pentabell ",children:U("getInTouch:phone")}),(0,s.jsx)(x.sb,{defaultCountry:"fr",className:"input-pentabell",value:a.phone,onChange:e=>{f("phone",e),E("")},flagComponent:e=>(0,s.jsx)(I.Z,{...e})})]}),(0,s.jsx)(i.Bc,{name:"phone",children:e=>(0,s.jsx)("span",{className:"error-span",children:e})})]}),(0,s.jsxs)(o.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(c.Z,{className:"form-group",children:[(0,s.jsxs)(d.Z,{className:"label-pentabell",children:[U("getInTouch:youAre"),"*"]}),(0,s.jsx)(r.Z,{className:"input-pentabell",id:"tags-standard",options:["Consultant","Company"],getOptionLabel:e=>e,name:"youAre",value:a.youAre,onChange:(e,a)=>{f("youAre",a),B("Company"===a)},renderInput:e=>(0,s.jsx)(u.Z,{...e,className:"input-pentabell multiple-select",variant:"standard",placeholder:U("aiSourcingService:servicePageForm:chooseOne"),error:!!(n.youAre&&h.youAre)})})]}),(0,s.jsx)(i.Bc,{name:"youAre",children:e=>(0,s.jsx)("span",{className:"error-span",children:e})})]}),(0,s.jsxs)(o.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(c.Z,{className:"form-group ",children:[(0,s.jsx)(d.Z,{className:"label-pentabell ",children:U("getInTouch:subject")}),(0,s.jsx)(u.Z,{autoComplete:"off",className:"input-pentabell ",placeholder:U("getInTouch:subject"),variant:"standard",type:"text",name:"subject",value:a.subject,onChange:t,error:!!(n.subject&&h.subject)})]}),(0,s.jsx)(i.Bc,{name:"subject",children:e=>(0,s.jsx)("span",{className:"error-span",children:e})})]}),(0,s.jsxs)(o.default,{item:!0,xs:12,sm:6,children:[(0,s.jsxs)(c.Z,{className:"form-group ",children:[(0,s.jsx)(d.Z,{className:"label-pentabell ",children:U("getInTouch:countryName")}),(0,s.jsx)(r.Z,{className:"input-pentabell",id:"tags-standard",options:w.nh,getOptionLabel:e=>e,name:"country",value:a.country,onChange:(e,a)=>f("country",a),renderInput:e=>(0,s.jsx)(u.Z,{...e,className:"input-pentabell multiple-select",variant:"standard",placeholder:"Choose country",error:!!(n.country&&h.country)})})]}),(0,s.jsx)(i.Bc,{name:"country",children:e=>(0,s.jsx)("span",{className:"error-span",children:e})})]}),"Consultant"===a.youAre&&(0,s.jsxs)(o.default,{item:!0,xs:12,sm:4,children:[(0,s.jsxs)(c.Z,{className:"form-group",children:[(0,s.jsxs)(d.Z,{className:"label-pentabell",children:[U("consultingServices:servicePageForm:industry"),"*"]}),(0,s.jsx)(r.Z,{className:"input-pentabell",id:"tags-standard",options:Object.values(w.b5),getOptionLabel:e=>e,name:"field",value:a.field,onChange:(e,a)=>{f("field",a)},renderInput:e=>(0,s.jsx)(u.Z,{...e,className:"input-pentabell multiple-select",variant:"standard",placeholder:U("consultingServices:servicePageForm:chooseOne"),error:!!(n.field&&h.field)})})]}),(0,s.jsx)(i.Bc,{name:"field",children:e=>(0,s.jsx)("span",{className:"error-span",children:e})})]}),(0,s.jsxs)(o.default,{item:!0,xs:12,sm:"Consultant"===a.youAre?8:12,children:[(0,s.jsxs)(c.Z,{className:"form-group ",children:[(0,s.jsxs)(d.Z,{className:"label-pentabell ",children:[U("getInTouch:message"),"*"]}),(0,s.jsx)(u.Z,{autoComplete:"off",className:"input-pentabell ",placeholder:U("getInTouch:typeMessage"),variant:"standard",type:"text",name:"message",value:a.message,onChange:t,error:!!(n.message&&h.message)})]}),(0,s.jsx)(i.Bc,{name:"message",children:e=>(0,s.jsx)("span",{className:"error-span",children:e})})]}),"Consultant"===a.youAre&&(0,s.jsxs)(o.default,{item:!0,xs:12,sm:12,children:[(0,s.jsx)(c.Z,{className:"form-group light form-section",children:(0,s.jsxs)("div",{className:"custom-file-upload-wbg",onClick:()=>document.getElementById("file-upload").click(),children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(T.Z,{}),F?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(d.Z,{className:"label-pentabell light",children:[U("joinUs:form:uploadCv"),"*"]}),(0,s.jsx)("p",{className:"sub-label",children:F})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.Z,{className:"label-pentabell light",children:U("joinUs:form:uploadCv")}),(0,s.jsx)("p",{className:"sub-label",children:U("joinUs:form:control")})]}),(0,s.jsx)(j.default,{text:"Choose a file",className:"btn btn-outlined white"}),$&&(0,s.jsx)(l.Z,{variant:"filled",severity:"error",children:$})]}),(0,s.jsx)("input",{id:"file-upload",type:"file",name:"resume",accept:"application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword",style:{display:"none"},onChange:e=>{V(e,f)},error:!!(n.resume&&h.resume)})]})}),(0,s.jsx)(i.Bc,{name:"resume",children:e=>(0,s.jsx)(l.Z,{variant:"filled",severity:"error",children:e})})]}),(0,s.jsx)(o.default,{item:!0,xs:12,sm:8,children:(0,s.jsxs)(c.Z,{children:[(0,s.jsx)(m.Z,{className:"checkbox-pentabell ",control:(0,s.jsx)(p.Z,{name:"acceptTerms",checked:a.acceptTerms,onChange:t,error:!!(n.acceptTerms&&h.acceptTerms)}),label:U("aiSourcingService:servicePageForm:formSubmissionAgreement")}),(0,s.jsx)(i.Bc,{name:"acceptTerms",children:e=>(0,s.jsx)("span",{className:"error-span",children:e})})]})}),(0,s.jsx)(o.default,{item:!0,xs:12,sm:4,className:"flex-end",children:(0,s.jsx)(j.default,{text:U("getInTouch:submit"),className:"btn btn-filled btn-submit",type:"submit"})})]})})}}),(0,s.jsx)(Z.Z,{errMsg:t,success:k})]})})]})}}},function(e){e.O(0,[3661,8760,226,775,948,5788,2996,7648,3464,455,2662,7183,2296,747,3200,7584,6484,9832,9414,7261,8467,7571,1799,5719,3993,2412,9175,4244,1774,7388,2971,2117,1744],function(){return e(e.s=99326)}),_N_E=e.O()}]);