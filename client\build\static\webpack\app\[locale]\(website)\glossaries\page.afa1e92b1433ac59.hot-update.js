"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/page",{

/***/ "(app-pages-browser)/./src/components/sections/GlossaryBanner.jsx":
/*!****************************************************!*\
  !*** ./src/components/sections/GlossaryBanner.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Input/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _assets_images_icons_BookIcon_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/BookIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/BookIcon.svg\");\n/* harmony import */ var _assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/searchIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/searchIcon.svg\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction GlossaryBanner(param) {\n    let { bannerImg, height, altImg, letters = [], searchWord = \"\", hasContent = true } = param;\n    _s();\n    const searchQueryParams = new URLSearchParams();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [keyword, setKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(searchWord || searchParams?.get(\"word\") || \"\");\n    const handleSearchChange = (e)=>{\n        setKeyword(e.target.value);\n    };\n    const handleSearchClick = ()=>{\n        const params = new URLSearchParams();\n        if (keyword.trim()) {\n            params.set(\"word\", keyword.trim());\n        }\n        const queryString = params.toString();\n        router.push(`${pathname}${queryString ? `?${queryString}` : \"\"}`);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\") {\n            handleSearchClick();\n        }\n    };\n    const handleClearSearch = ()=>{\n        setKeyword(\"\");\n        router.push(pathname);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-banner\",\n        className: \"center-banner\",\n        style: {\n            backgroundImage: `url(${bannerImg.src})`,\n            height: height\n        },\n        children: [\n            altImg && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                width: 0,\n                height: 0,\n                alt: altImg,\n                src: \"\",\n                style: {\n                    display: \"none\"\n                },\n                loading: \"lazy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"top-section custom-max-width\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_BookIcon_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"heading-h1 text-white\",\n                        children: \"Global Work Glossary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glossary-search\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"glossary-search-input\",\n                                type: \"text\",\n                                placeholder: \"What are you looking for ?\",\n                                onChange: handleSearchChange,\n                                onKeyDown: handleKeyDown,\n                                value: keyword\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"btn-search\",\n                                onClick: handleSearchClick,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            keyword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"btn-clear\",\n                                onClick: handleClearSearch,\n                                size: \"small\",\n                                style: {\n                                    marginLeft: \"8px\",\n                                    color: \"white\",\n                                    textDecoration: \"underline\"\n                                },\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    hasContent && letters?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"letters\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"letter selected\",\n                                children: \"#\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            letters.map((letter, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: `#${letter}`,\n                                    className: \"letter\",\n                                    children: letter\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    !hasContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"letters-empty\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: \"white\",\n                                opacity: 0.8,\n                                fontSize: \"14px\",\n                                marginTop: \"20px\"\n                            },\n                            children: searchWord ? `No results found for \"${searchWord}\"` : \"No glossary terms available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_s(GlossaryBanner, \"5cK5uZLgiTLM+7PkB8Pjb+OyIXg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GlossaryBanner;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GlossaryBanner);\nvar _c;\n$RefreshReg$(_c, \"GlossaryBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/GlossaryBanner.jsx\n"));

/***/ })

});