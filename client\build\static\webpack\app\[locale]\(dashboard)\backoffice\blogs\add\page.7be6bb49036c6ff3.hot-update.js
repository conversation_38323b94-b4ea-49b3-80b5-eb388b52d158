"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/AddArticleFroala.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _AddArticleEN__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AddArticleEN */ \"(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\");\n/* harmony import */ var _AddArticleFR__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AddArticleFR */ \"(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx\");\n/* harmony import */ var _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_15__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AddArticle = ()=>{\n    _s();\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const savedArticle = localStorage.getItem(\"savedArticle\");\n    const useUpdateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave)();\n    const useCreateArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle)();\n    const useCreateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave)();\n    const useSaveFileHook = (0,_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentYear = new Date().getFullYear();\n    const formikRefAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(`panel`);\n    const [formdataEN, setFormDataEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [formdataFR, setFormDataFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedLanguages, setSelectedLanguages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        en: true,\n        fr: false\n    });\n    const [categoriesEN, setCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesFR, setCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesEN, setFilteredCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesFR, setFilteredCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesEN, setSelectedCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesFR, setSelectedCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const getCategoriesEN = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"en\");\n    const getCategoriesFR = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"fr\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesEN.data?.categories) {\n            const transformedCategories = getCategoriesEN.data.categories.map((category)=>({\n                    id: category.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesEN(transformedCategories);\n        }\n    }, [\n        getCategoriesEN.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesFR.data?.categories) {\n            const transformedCategories = getCategoriesFR.data.categories.map((category)=>({\n                    id: category?.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesFR(transformedCategories);\n        }\n    }, [\n        getCategoriesFR.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedCategoriesEN.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesEN, \"en\");\n        } else if (selectedCategoriesFR.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesFR, \"fr\");\n        }\n    }, [\n        selectedCategoriesEN,\n        selectedCategoriesFR\n    ]);\n    const fetchTranslatedCategories = async (selectedCategories, language)=>{\n        try {\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_12__.axiosGetJson.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.categories}/${language}/${selectedCategories}`);\n            if (language === \"en\") {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesFR(transformedCategories);\n            } else {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesEN(transformedCategories);\n            }\n        } catch (error) {\n            console.error(\"Error fetching translated categories:\", error);\n        }\n    };\n    const handleImageSelect = async (selectedFile, language)=>{\n        if (language === \"en\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataEN(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n        } else if (language === \"fr\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataFR(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n        }\n    };\n    // Helper function to safely parse localStorage items\n    const getLocalStorageItem = (key)=>{\n        try {\n            const item = localStorage.getItem(key);\n            return item ? JSON.parse(item) : \"\";\n        } catch (error) {\n            console.warn(`Error parsing localStorage item \"${key}\":`, error);\n            return \"\";\n        }\n    };\n    const titleEN = getLocalStorageItem(\"title\");\n    const metaTitleEN = getLocalStorageItem(\"metatitle\");\n    const metaDescriptionEN = getLocalStorageItem(\"metaDescription\");\n    const contentEN = getLocalStorageItem(\"content\");\n    const contentFR = getLocalStorageItem(\"contentfr\");\n    const titleFR = getLocalStorageItem(\"titlefr\");\n    const metaDescriptionFR = getLocalStorageItem(\"metaDescriptionfr\");\n    const metaTitleFR = getLocalStorageItem(\"metatitlefr\");\n    const initialValues = {\n        robotsMeta: \"index\",\n        metaTitleEN: metaTitleEN,\n        metaDescriptionEN: metaDescriptionEN,\n        descriptionEN: \"\",\n        visibilityEN: \"\",\n        categoryEN: [],\n        imageEN: null,\n        keywordsEN: \"\",\n        titleEN: titleEN,\n        urlEN: \"\",\n        altEN: \"\",\n        contentEN: contentEN,\n        highlightsEN: [],\n        publishDateEN: \"\",\n        faqTitleEN: \"\",\n        faqEN: [],\n        titleFR: titleFR,\n        metaTitleFR: metaTitleFR,\n        metaDescriptionFR: metaDescriptionFR,\n        descriptionFR: \"\",\n        visibilityFR: \"\",\n        categoryFR: [],\n        imageFR: null,\n        keywordsFR: \"\",\n        urlFR: \"\",\n        altFR: \"\",\n        contentFR: contentFR,\n        publishDateFR: \"\",\n        highlightsFR: [],\n        faqTitleFR: \"\",\n        faqFR: []\n    };\n    const validationSchema = yup__WEBPACK_IMPORTED_MODULE_3__.object().shape({\n        ...selectedLanguages.en && {\n            metaTitleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            category: yup__WEBPACK_IMPORTED_MODULE_3__.array().min(1, t(\"validations:minCategory\")),\n            visibilityEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        },\n        ...selectedLanguages.fr && {\n            metaTitleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            visibilityFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        }\n    });\n    const uploadFile = (filename, formData, lang)=>{\n        return new Promise((resolve)=>{\n            useSaveFileHook.mutate({\n                resource: \"blogs\",\n                folder: currentYear,\n                filename,\n                body: {\n                    formData,\n                    t\n                }\n            }, {\n                onSuccess: (data)=>{\n                    if (data.message === \"uuid exist\") {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    } else {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    }\n                },\n                onError: (error)=>{\n                    console.error(`Error uploading ${lang} image:`, error);\n                    resolve({\n                        lang,\n                        uuid: null\n                    });\n                }\n            });\n        });\n    };\n    const clearLocalStorage = ()=>{\n        const keysToRemove = [\n            \"title\",\n            \"content\",\n            \"titlefr\",\n            \"contentfr\",\n            \"metaDescription\",\n            \"metaDescriptionfr\",\n            \"metatitle\",\n            \"metatitlefr\",\n            \"savedArticle\"\n        ];\n        keysToRemove.forEach((key)=>{\n            try {\n                localStorage.removeItem(key);\n            } catch (error) {\n                console.warn(`Error removing localStorage item \"${key}\":`, error);\n            }\n        });\n    };\n    const handleSubmit = async (values)=>{\n        const data = {\n            robotsMeta: values.robotsMeta,\n            versions: []\n        };\n        if (!selectedLanguages.en && !selectedLanguages.fr) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select at least one version!\");\n            return;\n        }\n        try {\n            let resultEN = {\n                uuid: null\n            }, resultFR = {\n                uuid: null\n            };\n            // Upload files if they exist\n            if (selectedLanguages.en && uuidPhotoFileNameEN && formdataEN) {\n                console.log(\"Uploading English image...\");\n                resultEN = await uploadFile(uuidPhotoFileNameEN, formdataEN, \"English\");\n                console.log(\"English upload result:\", resultEN);\n            } else if (selectedLanguages.en) {\n                console.log(\"No English image to upload, proceeding without image\");\n                resultEN = {\n                    uuid: null\n                }; // Allow article creation without image\n            }\n            if (selectedLanguages.fr && uuidPhotoFileNameFR && formdataFR) {\n                console.log(\"Uploading French image...\");\n                resultFR = await uploadFile(uuidPhotoFileNameFR, formdataFR, \"French\");\n                console.log(\"French upload result:\", resultFR);\n            } else if (selectedLanguages.fr) {\n                console.log(\"No French image to upload, proceeding without image\");\n                resultFR = {\n                    uuid: null\n                }; // Allow article creation without image\n            }\n            if (selectedLanguages.en && resultEN.uuid) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    description: values.descriptionEN,\n                    url: values.urlEN,\n                    visibility: values.visibilityEN,\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    highlights: values.highlightsEN,\n                    image: resultEN.uuid,\n                    category: values.categoryEN === \"\" ? [] : values.categoryEN,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr && resultFR.uuid) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: values.visibilityFR,\n                    description: values.descriptionFR,\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    highlights: values.highlightsFR,\n                    image: resultFR.uuid,\n                    category: values.categoryFR === \"\" ? [] : values.categoryFR,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (data.versions.length > 0) {\n                if (isSavedArticle !== \"\") {\n                    useUpdateAutoSaveHook.mutate({\n                        data: data,\n                        id: isSavedArticle\n                    }, {\n                        onSuccess: (response)=>{\n                            console.log(\"Article updated successfully:\", response);\n                            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Article updated successfully!\");\n                            clearLocalStorage();\n                        //window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        },\n                        onError: (error)=>{\n                            console.error(\"Error updating article:\", error);\n                            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to update article. Please try again.\");\n                        }\n                    });\n                } else {\n                    useCreateArticleHook.mutate({\n                        data\n                    }, {\n                        onSuccess: (response)=>{\n                            console.log(\"Article created successfully:\", response);\n                            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Article created successfully!\");\n                            clearLocalStorage();\n                        // window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        },\n                        onError: (error)=>{\n                            console.error(\"Error creating article:\", error);\n                            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to create article. Please try again.\");\n                        }\n                    });\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No valid image uploads found. Article not created.\");\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"An error occurred while processing uploads.\");\n        }\n    };\n    const handleChangeAccordion = (panel)=>(_, newExpanded)=>{\n            setExpanded(newExpanded ? panel : false);\n        };\n    const handleCategoriesENSelect = (selectedCategories)=>{\n        setSelectedCategoriesEN(selectedCategories);\n    };\n    const handleCategoriesFRSelect = (selectedCategories)=>{\n        setSelectedCategoriesFR(selectedCategories);\n    };\n    const [isSavedArticle, setIsSavedArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedArticle ? savedArticle : \"\");\n    const isSavedArticleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isSavedArticle);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        isSavedArticleRef.current = isSavedArticle;\n    }, [\n        isSavedArticle\n    ]);\n    const autosave = async ()=>{\n        try {\n            const values = formikRefAll.current?.values;\n            if (!values) return;\n            const data = {\n                robotsMeta: values?.robotsMeta,\n                versions: []\n            };\n            if (selectedLanguages.en) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    url: values.urlEN,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    category: values.categoryEN,\n                    highlights: values.highlightsEN,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    category: values.categoryFR,\n                    highlights: values.highlightsFR,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (isSavedArticleRef.current != \"\") {\n                useUpdateAutoSaveHook.mutate({\n                    data: data,\n                    id: isSavedArticleRef.current\n                });\n            } else {\n                if (data.versions.length > 0) {\n                    useCreateAutoSaveHook.mutate({\n                        data\n                    }, {\n                        onSuccess: (data)=>{\n                            setIsSavedArticle(data.articleId);\n                            localStorage.setItem(\"savedArticle\", data.articleId);\n                        }\n                    });\n                }\n            }\n        } catch (error) {\n            console.warn(\"Auto-save failed:\", error);\n        }\n    };\n    const handleChange = lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default()(autosave, 30000);\n    const handleClear = ()=>{\n        formikRefAll.current?.resetForm();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"createArticle:addArticle\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 483,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"commun\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.en,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            en: !prev.en\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"English\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.fr,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            fr: !prev.fr\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"French\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 487,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"experiences\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"form\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Formik, {\n                                        initialValues: initialValues,\n                                        validationSchema: validationSchema,\n                                        innerRef: formikRefAll,\n                                        onSubmit: handleSubmit,\n                                        className: \"formik-form\",\n                                        children: (param)=>{\n                                            let { errors, touched, setFieldValue, values } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Form, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        id: \"accordion\",\n                                                        disableGutters: true,\n                                                        expanded: expanded === `panel`,\n                                                        onChange: handleChangeAccordion(`panel`),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 39\n                                                                }, void 0),\n                                                                \"aria-controls\": `panel-content`,\n                                                                id: `panel-header`,\n                                                                children: t(\"createArticle:settings\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"accordion-detail\",\n                                                                elevation: 0,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"label-form\",\n                                                                                    children: [\n                                                                                        \"Robots meta\",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"select-pentabell\",\n                                                                                            variant: \"standard\",\n                                                                                            sx: {\n                                                                                                m: 1,\n                                                                                                minWidth: 120\n                                                                                            },\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                                value: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.filter((option)=>values.robotsMeta === option),\n                                                                                                selected: values.robotsMeta,\n                                                                                                onChange: (event)=>{\n                                                                                                    setFieldValue(\"robotsMeta\", event.target.value);\n                                                                                                },\n                                                                                                children: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                                        value: item,\n                                                                                                        children: item\n                                                                                                    }, item, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                        lineNumber: 567,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                lineNumber: 554,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 549,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                            className: \"label-error\",\n                                                                                            name: \"robotsMeta\",\n                                                                                            component: \"div\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 573,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                    lineNumber: 547,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 546,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                name: \"robotsMeta\",\n                                                                                component: \"div\",\n                                                                                className: \"label-error\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 581,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 540,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, `panel`, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    selectedLanguages.en && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleEN__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        debounce: handleChange,\n                                                        categories: categoriesEN,\n                                                        filteredCategories: filteredCategoriesEN,\n                                                        onCategoriesSelect: handleCategoriesENSelect\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    selectedLanguages.fr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleFR__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        categories: categoriesFR,\n                                                        filteredCategories: filteredCategoriesFR,\n                                                        onCategoriesSelect: handleCategoriesFRSelect,\n                                                        debounce: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"btn-container\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"button\",\n                                                                text: \"Clear\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: handleClear\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"submit\",\n                                                                text: \"Save\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: ()=>{}\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 515,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                        lineNumber: 486,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                    lineNumber: 485,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 484,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AddArticle, \"B+cNPaZHMPFNJqb0o7Rak/frCrI=\", false, function() {\n    return [\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave,\n        _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories\n    ];\n});\n_c = AddArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticle);\nvar _c;\n$RefreshReg$(_c, \"AddArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx\n"));

/***/ })

});