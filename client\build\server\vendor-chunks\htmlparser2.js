"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/htmlparser2";
exports.ids = ["vendor-chunks/htmlparser2"];
exports.modules = {

/***/ "(ssr)/./node_modules/htmlparser2/lib/esm/Parser.js":
/*!****************************************************!*\
  !*** ./node_modules/htmlparser2/lib/esm/Parser.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/* harmony import */ var _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Tokenizer.js */ \"(ssr)/./node_modules/htmlparser2/lib/esm/Tokenizer.js\");\n/* harmony import */ var entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! entities/lib/decode.js */ \"(ssr)/./node_modules/entities/lib/esm/decode.js\");\n\n\nconst formTags = new Set([\n    \"input\",\n    \"option\",\n    \"optgroup\",\n    \"select\",\n    \"button\",\n    \"datalist\",\n    \"textarea\",\n]);\nconst pTag = new Set([\"p\"]);\nconst tableSectionTags = new Set([\"thead\", \"tbody\"]);\nconst ddtTags = new Set([\"dd\", \"dt\"]);\nconst rtpTags = new Set([\"rt\", \"rp\"]);\nconst openImpliesClose = new Map([\n    [\"tr\", new Set([\"tr\", \"th\", \"td\"])],\n    [\"th\", new Set([\"th\"])],\n    [\"td\", new Set([\"thead\", \"th\", \"td\"])],\n    [\"body\", new Set([\"head\", \"link\", \"script\"])],\n    [\"li\", new Set([\"li\"])],\n    [\"p\", pTag],\n    [\"h1\", pTag],\n    [\"h2\", pTag],\n    [\"h3\", pTag],\n    [\"h4\", pTag],\n    [\"h5\", pTag],\n    [\"h6\", pTag],\n    [\"select\", formTags],\n    [\"input\", formTags],\n    [\"output\", formTags],\n    [\"button\", formTags],\n    [\"datalist\", formTags],\n    [\"textarea\", formTags],\n    [\"option\", new Set([\"option\"])],\n    [\"optgroup\", new Set([\"optgroup\", \"option\"])],\n    [\"dd\", ddtTags],\n    [\"dt\", ddtTags],\n    [\"address\", pTag],\n    [\"article\", pTag],\n    [\"aside\", pTag],\n    [\"blockquote\", pTag],\n    [\"details\", pTag],\n    [\"div\", pTag],\n    [\"dl\", pTag],\n    [\"fieldset\", pTag],\n    [\"figcaption\", pTag],\n    [\"figure\", pTag],\n    [\"footer\", pTag],\n    [\"form\", pTag],\n    [\"header\", pTag],\n    [\"hr\", pTag],\n    [\"main\", pTag],\n    [\"nav\", pTag],\n    [\"ol\", pTag],\n    [\"pre\", pTag],\n    [\"section\", pTag],\n    [\"table\", pTag],\n    [\"ul\", pTag],\n    [\"rt\", rtpTags],\n    [\"rp\", rtpTags],\n    [\"tbody\", tableSectionTags],\n    [\"tfoot\", tableSectionTags],\n]);\nconst voidElements = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\nconst foreignContextElements = new Set([\"math\", \"svg\"]);\nconst htmlIntegrationElements = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignobject\",\n    \"desc\",\n    \"title\",\n]);\nconst reNameEnd = /\\s|\\//;\nclass Parser {\n    constructor(cbs, options = {}) {\n        var _a, _b, _c, _d, _e;\n        this.options = options;\n        /** The start index of the last event. */\n        this.startIndex = 0;\n        /** The end index of the last event. */\n        this.endIndex = 0;\n        /**\n         * Store the start index of the current open tag,\n         * so we can update the start index for attributes.\n         */\n        this.openTagStart = 0;\n        this.tagname = \"\";\n        this.attribname = \"\";\n        this.attribvalue = \"\";\n        this.attribs = null;\n        this.stack = [];\n        this.foreignContext = [];\n        this.buffers = [];\n        this.bufferOffset = 0;\n        /** The index of the last written buffer. Used when resuming after a `pause()`. */\n        this.writeIndex = 0;\n        /** Indicates whether the parser has finished running / `.end` has been called. */\n        this.ended = false;\n        this.cbs = cbs !== null && cbs !== void 0 ? cbs : {};\n        this.lowerCaseTagNames = (_a = options.lowerCaseTags) !== null && _a !== void 0 ? _a : !options.xmlMode;\n        this.lowerCaseAttributeNames =\n            (_b = options.lowerCaseAttributeNames) !== null && _b !== void 0 ? _b : !options.xmlMode;\n        this.tokenizer = new ((_c = options.Tokenizer) !== null && _c !== void 0 ? _c : _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.options, this);\n        (_e = (_d = this.cbs).onparserinit) === null || _e === void 0 ? void 0 : _e.call(_d, this);\n    }\n    // Tokenizer event handlers\n    /** @internal */\n    ontext(start, endIndex) {\n        var _a, _b;\n        const data = this.getSlice(start, endIndex);\n        this.endIndex = endIndex - 1;\n        (_b = (_a = this.cbs).ontext) === null || _b === void 0 ? void 0 : _b.call(_a, data);\n        this.startIndex = endIndex;\n    }\n    /** @internal */\n    ontextentity(cp) {\n        var _a, _b;\n        /*\n         * Entities can be emitted on the character, or directly after.\n         * We use the section start here to get accurate indices.\n         */\n        const index = this.tokenizer.getSectionStart();\n        this.endIndex = index - 1;\n        (_b = (_a = this.cbs).ontext) === null || _b === void 0 ? void 0 : _b.call(_a, (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__.fromCodePoint)(cp));\n        this.startIndex = index;\n    }\n    isVoidElement(name) {\n        return !this.options.xmlMode && voidElements.has(name);\n    }\n    /** @internal */\n    onopentagname(start, endIndex) {\n        this.endIndex = endIndex;\n        let name = this.getSlice(start, endIndex);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        this.emitOpenTag(name);\n    }\n    emitOpenTag(name) {\n        var _a, _b, _c, _d;\n        this.openTagStart = this.startIndex;\n        this.tagname = name;\n        const impliesClose = !this.options.xmlMode && openImpliesClose.get(name);\n        if (impliesClose) {\n            while (this.stack.length > 0 &&\n                impliesClose.has(this.stack[this.stack.length - 1])) {\n                const element = this.stack.pop();\n                (_b = (_a = this.cbs).onclosetag) === null || _b === void 0 ? void 0 : _b.call(_a, element, true);\n            }\n        }\n        if (!this.isVoidElement(name)) {\n            this.stack.push(name);\n            if (foreignContextElements.has(name)) {\n                this.foreignContext.push(true);\n            }\n            else if (htmlIntegrationElements.has(name)) {\n                this.foreignContext.push(false);\n            }\n        }\n        (_d = (_c = this.cbs).onopentagname) === null || _d === void 0 ? void 0 : _d.call(_c, name);\n        if (this.cbs.onopentag)\n            this.attribs = {};\n    }\n    endOpenTag(isImplied) {\n        var _a, _b;\n        this.startIndex = this.openTagStart;\n        if (this.attribs) {\n            (_b = (_a = this.cbs).onopentag) === null || _b === void 0 ? void 0 : _b.call(_a, this.tagname, this.attribs, isImplied);\n            this.attribs = null;\n        }\n        if (this.cbs.onclosetag && this.isVoidElement(this.tagname)) {\n            this.cbs.onclosetag(this.tagname, true);\n        }\n        this.tagname = \"\";\n    }\n    /** @internal */\n    onopentagend(endIndex) {\n        this.endIndex = endIndex;\n        this.endOpenTag(false);\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onclosetag(start, endIndex) {\n        var _a, _b, _c, _d, _e, _f;\n        this.endIndex = endIndex;\n        let name = this.getSlice(start, endIndex);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        if (foreignContextElements.has(name) ||\n            htmlIntegrationElements.has(name)) {\n            this.foreignContext.pop();\n        }\n        if (!this.isVoidElement(name)) {\n            const pos = this.stack.lastIndexOf(name);\n            if (pos !== -1) {\n                if (this.cbs.onclosetag) {\n                    let count = this.stack.length - pos;\n                    while (count--) {\n                        // We know the stack has sufficient elements.\n                        this.cbs.onclosetag(this.stack.pop(), count !== 0);\n                    }\n                }\n                else\n                    this.stack.length = pos;\n            }\n            else if (!this.options.xmlMode && name === \"p\") {\n                // Implicit open before close\n                this.emitOpenTag(\"p\");\n                this.closeCurrentTag(true);\n            }\n        }\n        else if (!this.options.xmlMode && name === \"br\") {\n            // We can't use `emitOpenTag` for implicit open, as `br` would be implicitly closed.\n            (_b = (_a = this.cbs).onopentagname) === null || _b === void 0 ? void 0 : _b.call(_a, \"br\");\n            (_d = (_c = this.cbs).onopentag) === null || _d === void 0 ? void 0 : _d.call(_c, \"br\", {}, true);\n            (_f = (_e = this.cbs).onclosetag) === null || _f === void 0 ? void 0 : _f.call(_e, \"br\", false);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onselfclosingtag(endIndex) {\n        this.endIndex = endIndex;\n        if (this.options.xmlMode ||\n            this.options.recognizeSelfClosing ||\n            this.foreignContext[this.foreignContext.length - 1]) {\n            this.closeCurrentTag(false);\n            // Set `startIndex` for next node\n            this.startIndex = endIndex + 1;\n        }\n        else {\n            // Ignore the fact that the tag is self-closing.\n            this.onopentagend(endIndex);\n        }\n    }\n    closeCurrentTag(isOpenImplied) {\n        var _a, _b;\n        const name = this.tagname;\n        this.endOpenTag(isOpenImplied);\n        // Self-closing tags will be on the top of the stack\n        if (this.stack[this.stack.length - 1] === name) {\n            // If the opening tag isn't implied, the closing tag has to be implied.\n            (_b = (_a = this.cbs).onclosetag) === null || _b === void 0 ? void 0 : _b.call(_a, name, !isOpenImplied);\n            this.stack.pop();\n        }\n    }\n    /** @internal */\n    onattribname(start, endIndex) {\n        this.startIndex = start;\n        const name = this.getSlice(start, endIndex);\n        this.attribname = this.lowerCaseAttributeNames\n            ? name.toLowerCase()\n            : name;\n    }\n    /** @internal */\n    onattribdata(start, endIndex) {\n        this.attribvalue += this.getSlice(start, endIndex);\n    }\n    /** @internal */\n    onattribentity(cp) {\n        this.attribvalue += (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__.fromCodePoint)(cp);\n    }\n    /** @internal */\n    onattribend(quote, endIndex) {\n        var _a, _b;\n        this.endIndex = endIndex;\n        (_b = (_a = this.cbs).onattribute) === null || _b === void 0 ? void 0 : _b.call(_a, this.attribname, this.attribvalue, quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.Double\n            ? '\"'\n            : quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.Single\n                ? \"'\"\n                : quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.NoValue\n                    ? undefined\n                    : null);\n        if (this.attribs &&\n            !Object.prototype.hasOwnProperty.call(this.attribs, this.attribname)) {\n            this.attribs[this.attribname] = this.attribvalue;\n        }\n        this.attribvalue = \"\";\n    }\n    getInstructionName(value) {\n        const index = value.search(reNameEnd);\n        let name = index < 0 ? value : value.substr(0, index);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        return name;\n    }\n    /** @internal */\n    ondeclaration(start, endIndex) {\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex);\n        if (this.cbs.onprocessinginstruction) {\n            const name = this.getInstructionName(value);\n            this.cbs.onprocessinginstruction(`!${name}`, `!${value}`);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onprocessinginstruction(start, endIndex) {\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex);\n        if (this.cbs.onprocessinginstruction) {\n            const name = this.getInstructionName(value);\n            this.cbs.onprocessinginstruction(`?${name}`, `?${value}`);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    oncomment(start, endIndex, offset) {\n        var _a, _b, _c, _d;\n        this.endIndex = endIndex;\n        (_b = (_a = this.cbs).oncomment) === null || _b === void 0 ? void 0 : _b.call(_a, this.getSlice(start, endIndex - offset));\n        (_d = (_c = this.cbs).oncommentend) === null || _d === void 0 ? void 0 : _d.call(_c);\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    oncdata(start, endIndex, offset) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex - offset);\n        if (this.options.xmlMode || this.options.recognizeCDATA) {\n            (_b = (_a = this.cbs).oncdatastart) === null || _b === void 0 ? void 0 : _b.call(_a);\n            (_d = (_c = this.cbs).ontext) === null || _d === void 0 ? void 0 : _d.call(_c, value);\n            (_f = (_e = this.cbs).oncdataend) === null || _f === void 0 ? void 0 : _f.call(_e);\n        }\n        else {\n            (_h = (_g = this.cbs).oncomment) === null || _h === void 0 ? void 0 : _h.call(_g, `[CDATA[${value}]]`);\n            (_k = (_j = this.cbs).oncommentend) === null || _k === void 0 ? void 0 : _k.call(_j);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onend() {\n        var _a, _b;\n        if (this.cbs.onclosetag) {\n            // Set the end index for all remaining tags\n            this.endIndex = this.startIndex;\n            for (let index = this.stack.length; index > 0; this.cbs.onclosetag(this.stack[--index], true))\n                ;\n        }\n        (_b = (_a = this.cbs).onend) === null || _b === void 0 ? void 0 : _b.call(_a);\n    }\n    /**\n     * Resets the parser to a blank state, ready to parse a new HTML document\n     */\n    reset() {\n        var _a, _b, _c, _d;\n        (_b = (_a = this.cbs).onreset) === null || _b === void 0 ? void 0 : _b.call(_a);\n        this.tokenizer.reset();\n        this.tagname = \"\";\n        this.attribname = \"\";\n        this.attribs = null;\n        this.stack.length = 0;\n        this.startIndex = 0;\n        this.endIndex = 0;\n        (_d = (_c = this.cbs).onparserinit) === null || _d === void 0 ? void 0 : _d.call(_c, this);\n        this.buffers.length = 0;\n        this.bufferOffset = 0;\n        this.writeIndex = 0;\n        this.ended = false;\n    }\n    /**\n     * Resets the parser, then parses a complete document and\n     * pushes it to the handler.\n     *\n     * @param data Document to parse.\n     */\n    parseComplete(data) {\n        this.reset();\n        this.end(data);\n    }\n    getSlice(start, end) {\n        while (start - this.bufferOffset >= this.buffers[0].length) {\n            this.shiftBuffer();\n        }\n        let slice = this.buffers[0].slice(start - this.bufferOffset, end - this.bufferOffset);\n        while (end - this.bufferOffset > this.buffers[0].length) {\n            this.shiftBuffer();\n            slice += this.buffers[0].slice(0, end - this.bufferOffset);\n        }\n        return slice;\n    }\n    shiftBuffer() {\n        this.bufferOffset += this.buffers[0].length;\n        this.writeIndex--;\n        this.buffers.shift();\n    }\n    /**\n     * Parses a chunk of data and calls the corresponding callbacks.\n     *\n     * @param chunk Chunk to parse.\n     */\n    write(chunk) {\n        var _a, _b;\n        if (this.ended) {\n            (_b = (_a = this.cbs).onerror) === null || _b === void 0 ? void 0 : _b.call(_a, new Error(\".write() after done!\"));\n            return;\n        }\n        this.buffers.push(chunk);\n        if (this.tokenizer.running) {\n            this.tokenizer.write(chunk);\n            this.writeIndex++;\n        }\n    }\n    /**\n     * Parses the end of the buffer and clears the stack, calls onend.\n     *\n     * @param chunk Optional final chunk to parse.\n     */\n    end(chunk) {\n        var _a, _b;\n        if (this.ended) {\n            (_b = (_a = this.cbs).onerror) === null || _b === void 0 ? void 0 : _b.call(_a, new Error(\".end() after done!\"));\n            return;\n        }\n        if (chunk)\n            this.write(chunk);\n        this.ended = true;\n        this.tokenizer.end();\n    }\n    /**\n     * Pauses parsing. The parser won't emit events until `resume` is called.\n     */\n    pause() {\n        this.tokenizer.pause();\n    }\n    /**\n     * Resumes parsing after `pause` was called.\n     */\n    resume() {\n        this.tokenizer.resume();\n        while (this.tokenizer.running &&\n            this.writeIndex < this.buffers.length) {\n            this.tokenizer.write(this.buffers[this.writeIndex++]);\n        }\n        if (this.ended)\n            this.tokenizer.end();\n    }\n    /**\n     * Alias of `write`, for backwards compatibility.\n     *\n     * @param chunk Chunk to parse.\n     * @deprecated\n     */\n    parseChunk(chunk) {\n        this.write(chunk);\n    }\n    /**\n     * Alias of `end`, for backwards compatibility.\n     *\n     * @param chunk Optional final chunk to parse.\n     * @deprecated\n     */\n    done(chunk) {\n        this.end(chunk);\n    }\n}\n//# sourceMappingURL=Parser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/htmlparser2/lib/esm/Parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/htmlparser2/lib/esm/Tokenizer.js":
/*!*******************************************************!*\
  !*** ./node_modules/htmlparser2/lib/esm/Tokenizer.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuoteType: () => (/* binding */ QuoteType),\n/* harmony export */   \"default\": () => (/* binding */ Tokenizer)\n/* harmony export */ });\n/* harmony import */ var entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! entities/lib/decode.js */ \"(ssr)/./node_modules/entities/lib/esm/decode.js\");\n\nvar CharCodes;\n(function (CharCodes) {\n    CharCodes[CharCodes[\"Tab\"] = 9] = \"Tab\";\n    CharCodes[CharCodes[\"NewLine\"] = 10] = \"NewLine\";\n    CharCodes[CharCodes[\"FormFeed\"] = 12] = \"FormFeed\";\n    CharCodes[CharCodes[\"CarriageReturn\"] = 13] = \"CarriageReturn\";\n    CharCodes[CharCodes[\"Space\"] = 32] = \"Space\";\n    CharCodes[CharCodes[\"ExclamationMark\"] = 33] = \"ExclamationMark\";\n    CharCodes[CharCodes[\"Number\"] = 35] = \"Number\";\n    CharCodes[CharCodes[\"Amp\"] = 38] = \"Amp\";\n    CharCodes[CharCodes[\"SingleQuote\"] = 39] = \"SingleQuote\";\n    CharCodes[CharCodes[\"DoubleQuote\"] = 34] = \"DoubleQuote\";\n    CharCodes[CharCodes[\"Dash\"] = 45] = \"Dash\";\n    CharCodes[CharCodes[\"Slash\"] = 47] = \"Slash\";\n    CharCodes[CharCodes[\"Zero\"] = 48] = \"Zero\";\n    CharCodes[CharCodes[\"Nine\"] = 57] = \"Nine\";\n    CharCodes[CharCodes[\"Semi\"] = 59] = \"Semi\";\n    CharCodes[CharCodes[\"Lt\"] = 60] = \"Lt\";\n    CharCodes[CharCodes[\"Eq\"] = 61] = \"Eq\";\n    CharCodes[CharCodes[\"Gt\"] = 62] = \"Gt\";\n    CharCodes[CharCodes[\"Questionmark\"] = 63] = \"Questionmark\";\n    CharCodes[CharCodes[\"UpperA\"] = 65] = \"UpperA\";\n    CharCodes[CharCodes[\"LowerA\"] = 97] = \"LowerA\";\n    CharCodes[CharCodes[\"UpperF\"] = 70] = \"UpperF\";\n    CharCodes[CharCodes[\"LowerF\"] = 102] = \"LowerF\";\n    CharCodes[CharCodes[\"UpperZ\"] = 90] = \"UpperZ\";\n    CharCodes[CharCodes[\"LowerZ\"] = 122] = \"LowerZ\";\n    CharCodes[CharCodes[\"LowerX\"] = 120] = \"LowerX\";\n    CharCodes[CharCodes[\"OpeningSquareBracket\"] = 91] = \"OpeningSquareBracket\";\n})(CharCodes || (CharCodes = {}));\n/** All the states the tokenizer can be in. */\nvar State;\n(function (State) {\n    State[State[\"Text\"] = 1] = \"Text\";\n    State[State[\"BeforeTagName\"] = 2] = \"BeforeTagName\";\n    State[State[\"InTagName\"] = 3] = \"InTagName\";\n    State[State[\"InSelfClosingTag\"] = 4] = \"InSelfClosingTag\";\n    State[State[\"BeforeClosingTagName\"] = 5] = \"BeforeClosingTagName\";\n    State[State[\"InClosingTagName\"] = 6] = \"InClosingTagName\";\n    State[State[\"AfterClosingTagName\"] = 7] = \"AfterClosingTagName\";\n    // Attributes\n    State[State[\"BeforeAttributeName\"] = 8] = \"BeforeAttributeName\";\n    State[State[\"InAttributeName\"] = 9] = \"InAttributeName\";\n    State[State[\"AfterAttributeName\"] = 10] = \"AfterAttributeName\";\n    State[State[\"BeforeAttributeValue\"] = 11] = \"BeforeAttributeValue\";\n    State[State[\"InAttributeValueDq\"] = 12] = \"InAttributeValueDq\";\n    State[State[\"InAttributeValueSq\"] = 13] = \"InAttributeValueSq\";\n    State[State[\"InAttributeValueNq\"] = 14] = \"InAttributeValueNq\";\n    // Declarations\n    State[State[\"BeforeDeclaration\"] = 15] = \"BeforeDeclaration\";\n    State[State[\"InDeclaration\"] = 16] = \"InDeclaration\";\n    // Processing instructions\n    State[State[\"InProcessingInstruction\"] = 17] = \"InProcessingInstruction\";\n    // Comments & CDATA\n    State[State[\"BeforeComment\"] = 18] = \"BeforeComment\";\n    State[State[\"CDATASequence\"] = 19] = \"CDATASequence\";\n    State[State[\"InSpecialComment\"] = 20] = \"InSpecialComment\";\n    State[State[\"InCommentLike\"] = 21] = \"InCommentLike\";\n    // Special tags\n    State[State[\"BeforeSpecialS\"] = 22] = \"BeforeSpecialS\";\n    State[State[\"SpecialStartSequence\"] = 23] = \"SpecialStartSequence\";\n    State[State[\"InSpecialTag\"] = 24] = \"InSpecialTag\";\n    State[State[\"BeforeEntity\"] = 25] = \"BeforeEntity\";\n    State[State[\"BeforeNumericEntity\"] = 26] = \"BeforeNumericEntity\";\n    State[State[\"InNamedEntity\"] = 27] = \"InNamedEntity\";\n    State[State[\"InNumericEntity\"] = 28] = \"InNumericEntity\";\n    State[State[\"InHexEntity\"] = 29] = \"InHexEntity\";\n})(State || (State = {}));\nfunction isWhitespace(c) {\n    return (c === CharCodes.Space ||\n        c === CharCodes.NewLine ||\n        c === CharCodes.Tab ||\n        c === CharCodes.FormFeed ||\n        c === CharCodes.CarriageReturn);\n}\nfunction isEndOfTagSection(c) {\n    return c === CharCodes.Slash || c === CharCodes.Gt || isWhitespace(c);\n}\nfunction isNumber(c) {\n    return c >= CharCodes.Zero && c <= CharCodes.Nine;\n}\nfunction isASCIIAlpha(c) {\n    return ((c >= CharCodes.LowerA && c <= CharCodes.LowerZ) ||\n        (c >= CharCodes.UpperA && c <= CharCodes.UpperZ));\n}\nfunction isHexDigit(c) {\n    return ((c >= CharCodes.UpperA && c <= CharCodes.UpperF) ||\n        (c >= CharCodes.LowerA && c <= CharCodes.LowerF));\n}\nvar QuoteType;\n(function (QuoteType) {\n    QuoteType[QuoteType[\"NoValue\"] = 0] = \"NoValue\";\n    QuoteType[QuoteType[\"Unquoted\"] = 1] = \"Unquoted\";\n    QuoteType[QuoteType[\"Single\"] = 2] = \"Single\";\n    QuoteType[QuoteType[\"Double\"] = 3] = \"Double\";\n})(QuoteType || (QuoteType = {}));\n/**\n * Sequences used to match longer strings.\n *\n * We don't have `Script`, `Style`, or `Title` here. Instead, we re-use the *End\n * sequences with an increased offset.\n */\nconst Sequences = {\n    Cdata: new Uint8Array([0x43, 0x44, 0x41, 0x54, 0x41, 0x5b]),\n    CdataEnd: new Uint8Array([0x5d, 0x5d, 0x3e]),\n    CommentEnd: new Uint8Array([0x2d, 0x2d, 0x3e]),\n    ScriptEnd: new Uint8Array([0x3c, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74]),\n    StyleEnd: new Uint8Array([0x3c, 0x2f, 0x73, 0x74, 0x79, 0x6c, 0x65]),\n    TitleEnd: new Uint8Array([0x3c, 0x2f, 0x74, 0x69, 0x74, 0x6c, 0x65]), // `</title`\n};\nclass Tokenizer {\n    constructor({ xmlMode = false, decodeEntities = true, }, cbs) {\n        this.cbs = cbs;\n        /** The current state the tokenizer is in. */\n        this.state = State.Text;\n        /** The read buffer. */\n        this.buffer = \"\";\n        /** The beginning of the section that is currently being read. */\n        this.sectionStart = 0;\n        /** The index within the buffer that we are currently looking at. */\n        this.index = 0;\n        /** Some behavior, eg. when decoding entities, is done while we are in another state. This keeps track of the other state type. */\n        this.baseState = State.Text;\n        /** For special parsing behavior inside of script and style tags. */\n        this.isSpecial = false;\n        /** Indicates whether the tokenizer has been paused. */\n        this.running = true;\n        /** The offset of the current buffer. */\n        this.offset = 0;\n        this.currentSequence = undefined;\n        this.sequenceIndex = 0;\n        this.trieIndex = 0;\n        this.trieCurrent = 0;\n        /** For named entities, the index of the value. For numeric entities, the code point. */\n        this.entityResult = 0;\n        this.entityExcess = 0;\n        this.xmlMode = xmlMode;\n        this.decodeEntities = decodeEntities;\n        this.entityTrie = xmlMode ? entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.xmlDecodeTree : entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.htmlDecodeTree;\n    }\n    reset() {\n        this.state = State.Text;\n        this.buffer = \"\";\n        this.sectionStart = 0;\n        this.index = 0;\n        this.baseState = State.Text;\n        this.currentSequence = undefined;\n        this.running = true;\n        this.offset = 0;\n    }\n    write(chunk) {\n        this.offset += this.buffer.length;\n        this.buffer = chunk;\n        this.parse();\n    }\n    end() {\n        if (this.running)\n            this.finish();\n    }\n    pause() {\n        this.running = false;\n    }\n    resume() {\n        this.running = true;\n        if (this.index < this.buffer.length + this.offset) {\n            this.parse();\n        }\n    }\n    /**\n     * The current index within all of the written data.\n     */\n    getIndex() {\n        return this.index;\n    }\n    /**\n     * The start of the current section.\n     */\n    getSectionStart() {\n        return this.sectionStart;\n    }\n    stateText(c) {\n        if (c === CharCodes.Lt ||\n            (!this.decodeEntities && this.fastForwardTo(CharCodes.Lt))) {\n            if (this.index > this.sectionStart) {\n                this.cbs.ontext(this.sectionStart, this.index);\n            }\n            this.state = State.BeforeTagName;\n            this.sectionStart = this.index;\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.state = State.BeforeEntity;\n        }\n    }\n    stateSpecialStartSequence(c) {\n        const isEnd = this.sequenceIndex === this.currentSequence.length;\n        const isMatch = isEnd\n            ? // If we are at the end of the sequence, make sure the tag name has ended\n                isEndOfTagSection(c)\n            : // Otherwise, do a case-insensitive comparison\n                (c | 0x20) === this.currentSequence[this.sequenceIndex];\n        if (!isMatch) {\n            this.isSpecial = false;\n        }\n        else if (!isEnd) {\n            this.sequenceIndex++;\n            return;\n        }\n        this.sequenceIndex = 0;\n        this.state = State.InTagName;\n        this.stateInTagName(c);\n    }\n    /** Look for an end tag. For <title> tags, also decode entities. */\n    stateInSpecialTag(c) {\n        if (this.sequenceIndex === this.currentSequence.length) {\n            if (c === CharCodes.Gt || isWhitespace(c)) {\n                const endOfText = this.index - this.currentSequence.length;\n                if (this.sectionStart < endOfText) {\n                    // Spoof the index so that reported locations match up.\n                    const actualIndex = this.index;\n                    this.index = endOfText;\n                    this.cbs.ontext(this.sectionStart, endOfText);\n                    this.index = actualIndex;\n                }\n                this.isSpecial = false;\n                this.sectionStart = endOfText + 2; // Skip over the `</`\n                this.stateInClosingTagName(c);\n                return; // We are done; skip the rest of the function.\n            }\n            this.sequenceIndex = 0;\n        }\n        if ((c | 0x20) === this.currentSequence[this.sequenceIndex]) {\n            this.sequenceIndex += 1;\n        }\n        else if (this.sequenceIndex === 0) {\n            if (this.currentSequence === Sequences.TitleEnd) {\n                // We have to parse entities in <title> tags.\n                if (this.decodeEntities && c === CharCodes.Amp) {\n                    this.state = State.BeforeEntity;\n                }\n            }\n            else if (this.fastForwardTo(CharCodes.Lt)) {\n                // Outside of <title> tags, we can fast-forward.\n                this.sequenceIndex = 1;\n            }\n        }\n        else {\n            // If we see a `<`, set the sequence index to 1; useful for eg. `<</script>`.\n            this.sequenceIndex = Number(c === CharCodes.Lt);\n        }\n    }\n    stateCDATASequence(c) {\n        if (c === Sequences.Cdata[this.sequenceIndex]) {\n            if (++this.sequenceIndex === Sequences.Cdata.length) {\n                this.state = State.InCommentLike;\n                this.currentSequence = Sequences.CdataEnd;\n                this.sequenceIndex = 0;\n                this.sectionStart = this.index + 1;\n            }\n        }\n        else {\n            this.sequenceIndex = 0;\n            this.state = State.InDeclaration;\n            this.stateInDeclaration(c); // Reconsume the character\n        }\n    }\n    /**\n     * When we wait for one specific character, we can speed things up\n     * by skipping through the buffer until we find it.\n     *\n     * @returns Whether the character was found.\n     */\n    fastForwardTo(c) {\n        while (++this.index < this.buffer.length + this.offset) {\n            if (this.buffer.charCodeAt(this.index - this.offset) === c) {\n                return true;\n            }\n        }\n        /*\n         * We increment the index at the end of the `parse` loop,\n         * so set it to `buffer.length - 1` here.\n         *\n         * TODO: Refactor `parse` to increment index before calling states.\n         */\n        this.index = this.buffer.length + this.offset - 1;\n        return false;\n    }\n    /**\n     * Comments and CDATA end with `-->` and `]]>`.\n     *\n     * Their common qualities are:\n     * - Their end sequences have a distinct character they start with.\n     * - That character is then repeated, so we have to check multiple repeats.\n     * - All characters but the start character of the sequence can be skipped.\n     */\n    stateInCommentLike(c) {\n        if (c === this.currentSequence[this.sequenceIndex]) {\n            if (++this.sequenceIndex === this.currentSequence.length) {\n                if (this.currentSequence === Sequences.CdataEnd) {\n                    this.cbs.oncdata(this.sectionStart, this.index, 2);\n                }\n                else {\n                    this.cbs.oncomment(this.sectionStart, this.index, 2);\n                }\n                this.sequenceIndex = 0;\n                this.sectionStart = this.index + 1;\n                this.state = State.Text;\n            }\n        }\n        else if (this.sequenceIndex === 0) {\n            // Fast-forward to the first character of the sequence\n            if (this.fastForwardTo(this.currentSequence[0])) {\n                this.sequenceIndex = 1;\n            }\n        }\n        else if (c !== this.currentSequence[this.sequenceIndex - 1]) {\n            // Allow long sequences, eg. --->, ]]]>\n            this.sequenceIndex = 0;\n        }\n    }\n    /**\n     * HTML only allows ASCII alpha characters (a-z and A-Z) at the beginning of a tag name.\n     *\n     * XML allows a lot more characters here (@see https://www.w3.org/TR/REC-xml/#NT-NameStartChar).\n     * We allow anything that wouldn't end the tag.\n     */\n    isTagStartChar(c) {\n        return this.xmlMode ? !isEndOfTagSection(c) : isASCIIAlpha(c);\n    }\n    startSpecial(sequence, offset) {\n        this.isSpecial = true;\n        this.currentSequence = sequence;\n        this.sequenceIndex = offset;\n        this.state = State.SpecialStartSequence;\n    }\n    stateBeforeTagName(c) {\n        if (c === CharCodes.ExclamationMark) {\n            this.state = State.BeforeDeclaration;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.Questionmark) {\n            this.state = State.InProcessingInstruction;\n            this.sectionStart = this.index + 1;\n        }\n        else if (this.isTagStartChar(c)) {\n            const lower = c | 0x20;\n            this.sectionStart = this.index;\n            if (!this.xmlMode && lower === Sequences.TitleEnd[2]) {\n                this.startSpecial(Sequences.TitleEnd, 3);\n            }\n            else {\n                this.state =\n                    !this.xmlMode && lower === Sequences.ScriptEnd[2]\n                        ? State.BeforeSpecialS\n                        : State.InTagName;\n            }\n        }\n        else if (c === CharCodes.Slash) {\n            this.state = State.BeforeClosingTagName;\n        }\n        else {\n            this.state = State.Text;\n            this.stateText(c);\n        }\n    }\n    stateInTagName(c) {\n        if (isEndOfTagSection(c)) {\n            this.cbs.onopentagname(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n    }\n    stateBeforeClosingTagName(c) {\n        if (isWhitespace(c)) {\n            // Ignore\n        }\n        else if (c === CharCodes.Gt) {\n            this.state = State.Text;\n        }\n        else {\n            this.state = this.isTagStartChar(c)\n                ? State.InClosingTagName\n                : State.InSpecialComment;\n            this.sectionStart = this.index;\n        }\n    }\n    stateInClosingTagName(c) {\n        if (c === CharCodes.Gt || isWhitespace(c)) {\n            this.cbs.onclosetag(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.AfterClosingTagName;\n            this.stateAfterClosingTagName(c);\n        }\n    }\n    stateAfterClosingTagName(c) {\n        // Skip everything until \">\"\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.state = State.Text;\n            this.baseState = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeAttributeName(c) {\n        if (c === CharCodes.Gt) {\n            this.cbs.onopentagend(this.index);\n            if (this.isSpecial) {\n                this.state = State.InSpecialTag;\n                this.sequenceIndex = 0;\n            }\n            else {\n                this.state = State.Text;\n            }\n            this.baseState = this.state;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.Slash) {\n            this.state = State.InSelfClosingTag;\n        }\n        else if (!isWhitespace(c)) {\n            this.state = State.InAttributeName;\n            this.sectionStart = this.index;\n        }\n    }\n    stateInSelfClosingTag(c) {\n        if (c === CharCodes.Gt) {\n            this.cbs.onselfclosingtag(this.index);\n            this.state = State.Text;\n            this.baseState = State.Text;\n            this.sectionStart = this.index + 1;\n            this.isSpecial = false; // Reset special state, in case of self-closing special tags\n        }\n        else if (!isWhitespace(c)) {\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n    }\n    stateInAttributeName(c) {\n        if (c === CharCodes.Eq || isEndOfTagSection(c)) {\n            this.cbs.onattribname(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.AfterAttributeName;\n            this.stateAfterAttributeName(c);\n        }\n    }\n    stateAfterAttributeName(c) {\n        if (c === CharCodes.Eq) {\n            this.state = State.BeforeAttributeValue;\n        }\n        else if (c === CharCodes.Slash || c === CharCodes.Gt) {\n            this.cbs.onattribend(QuoteType.NoValue, this.index);\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n        else if (!isWhitespace(c)) {\n            this.cbs.onattribend(QuoteType.NoValue, this.index);\n            this.state = State.InAttributeName;\n            this.sectionStart = this.index;\n        }\n    }\n    stateBeforeAttributeValue(c) {\n        if (c === CharCodes.DoubleQuote) {\n            this.state = State.InAttributeValueDq;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.SingleQuote) {\n            this.state = State.InAttributeValueSq;\n            this.sectionStart = this.index + 1;\n        }\n        else if (!isWhitespace(c)) {\n            this.sectionStart = this.index;\n            this.state = State.InAttributeValueNq;\n            this.stateInAttributeValueNoQuotes(c); // Reconsume token\n        }\n    }\n    handleInAttributeValue(c, quote) {\n        if (c === quote ||\n            (!this.decodeEntities && this.fastForwardTo(quote))) {\n            this.cbs.onattribdata(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.cbs.onattribend(quote === CharCodes.DoubleQuote\n                ? QuoteType.Double\n                : QuoteType.Single, this.index);\n            this.state = State.BeforeAttributeName;\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.baseState = this.state;\n            this.state = State.BeforeEntity;\n        }\n    }\n    stateInAttributeValueDoubleQuotes(c) {\n        this.handleInAttributeValue(c, CharCodes.DoubleQuote);\n    }\n    stateInAttributeValueSingleQuotes(c) {\n        this.handleInAttributeValue(c, CharCodes.SingleQuote);\n    }\n    stateInAttributeValueNoQuotes(c) {\n        if (isWhitespace(c) || c === CharCodes.Gt) {\n            this.cbs.onattribdata(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.cbs.onattribend(QuoteType.Unquoted, this.index);\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.baseState = this.state;\n            this.state = State.BeforeEntity;\n        }\n    }\n    stateBeforeDeclaration(c) {\n        if (c === CharCodes.OpeningSquareBracket) {\n            this.state = State.CDATASequence;\n            this.sequenceIndex = 0;\n        }\n        else {\n            this.state =\n                c === CharCodes.Dash\n                    ? State.BeforeComment\n                    : State.InDeclaration;\n        }\n    }\n    stateInDeclaration(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.ondeclaration(this.sectionStart, this.index);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateInProcessingInstruction(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.onprocessinginstruction(this.sectionStart, this.index);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeComment(c) {\n        if (c === CharCodes.Dash) {\n            this.state = State.InCommentLike;\n            this.currentSequence = Sequences.CommentEnd;\n            // Allow short comments (eg. <!-->)\n            this.sequenceIndex = 2;\n            this.sectionStart = this.index + 1;\n        }\n        else {\n            this.state = State.InDeclaration;\n        }\n    }\n    stateInSpecialComment(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.oncomment(this.sectionStart, this.index, 0);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeSpecialS(c) {\n        const lower = c | 0x20;\n        if (lower === Sequences.ScriptEnd[3]) {\n            this.startSpecial(Sequences.ScriptEnd, 4);\n        }\n        else if (lower === Sequences.StyleEnd[3]) {\n            this.startSpecial(Sequences.StyleEnd, 4);\n        }\n        else {\n            this.state = State.InTagName;\n            this.stateInTagName(c); // Consume the token again\n        }\n    }\n    stateBeforeEntity(c) {\n        // Start excess with 1 to include the '&'\n        this.entityExcess = 1;\n        this.entityResult = 0;\n        if (c === CharCodes.Number) {\n            this.state = State.BeforeNumericEntity;\n        }\n        else if (c === CharCodes.Amp) {\n            // We have two `&` characters in a row. Stay in the current state.\n        }\n        else {\n            this.trieIndex = 0;\n            this.trieCurrent = this.entityTrie[0];\n            this.state = State.InNamedEntity;\n            this.stateInNamedEntity(c);\n        }\n    }\n    stateInNamedEntity(c) {\n        this.entityExcess += 1;\n        this.trieIndex = (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.determineBranch)(this.entityTrie, this.trieCurrent, this.trieIndex + 1, c);\n        if (this.trieIndex < 0) {\n            this.emitNamedEntity();\n            this.index--;\n            return;\n        }\n        this.trieCurrent = this.entityTrie[this.trieIndex];\n        const masked = this.trieCurrent & entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.BinTrieFlags.VALUE_LENGTH;\n        // If the branch is a value, store it and continue\n        if (masked) {\n            // The mask is the number of bytes of the value, including the current byte.\n            const valueLength = (masked >> 14) - 1;\n            // If we have a legacy entity while parsing strictly, just skip the number of bytes\n            if (!this.allowLegacyEntity() && c !== CharCodes.Semi) {\n                this.trieIndex += valueLength;\n            }\n            else {\n                // Add 1 as we have already incremented the excess\n                const entityStart = this.index - this.entityExcess + 1;\n                if (entityStart > this.sectionStart) {\n                    this.emitPartial(this.sectionStart, entityStart);\n                }\n                // If this is a surrogate pair, consume the next two bytes\n                this.entityResult = this.trieIndex;\n                this.trieIndex += valueLength;\n                this.entityExcess = 0;\n                this.sectionStart = this.index + 1;\n                if (valueLength === 0) {\n                    this.emitNamedEntity();\n                }\n            }\n        }\n    }\n    emitNamedEntity() {\n        this.state = this.baseState;\n        if (this.entityResult === 0) {\n            return;\n        }\n        const valueLength = (this.entityTrie[this.entityResult] & entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.BinTrieFlags.VALUE_LENGTH) >>\n            14;\n        switch (valueLength) {\n            case 1: {\n                this.emitCodePoint(this.entityTrie[this.entityResult] &\n                    ~entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.BinTrieFlags.VALUE_LENGTH);\n                break;\n            }\n            case 2: {\n                this.emitCodePoint(this.entityTrie[this.entityResult + 1]);\n                break;\n            }\n            case 3: {\n                this.emitCodePoint(this.entityTrie[this.entityResult + 1]);\n                this.emitCodePoint(this.entityTrie[this.entityResult + 2]);\n            }\n        }\n    }\n    stateBeforeNumericEntity(c) {\n        if ((c | 0x20) === CharCodes.LowerX) {\n            this.entityExcess++;\n            this.state = State.InHexEntity;\n        }\n        else {\n            this.state = State.InNumericEntity;\n            this.stateInNumericEntity(c);\n        }\n    }\n    emitNumericEntity(strict) {\n        const entityStart = this.index - this.entityExcess - 1;\n        const numberStart = entityStart + 2 + Number(this.state === State.InHexEntity);\n        if (numberStart !== this.index) {\n            // Emit leading data if any\n            if (entityStart > this.sectionStart) {\n                this.emitPartial(this.sectionStart, entityStart);\n            }\n            this.sectionStart = this.index + Number(strict);\n            this.emitCodePoint((0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.replaceCodePoint)(this.entityResult));\n        }\n        this.state = this.baseState;\n    }\n    stateInNumericEntity(c) {\n        if (c === CharCodes.Semi) {\n            this.emitNumericEntity(true);\n        }\n        else if (isNumber(c)) {\n            this.entityResult = this.entityResult * 10 + (c - CharCodes.Zero);\n            this.entityExcess++;\n        }\n        else {\n            if (this.allowLegacyEntity()) {\n                this.emitNumericEntity(false);\n            }\n            else {\n                this.state = this.baseState;\n            }\n            this.index--;\n        }\n    }\n    stateInHexEntity(c) {\n        if (c === CharCodes.Semi) {\n            this.emitNumericEntity(true);\n        }\n        else if (isNumber(c)) {\n            this.entityResult = this.entityResult * 16 + (c - CharCodes.Zero);\n            this.entityExcess++;\n        }\n        else if (isHexDigit(c)) {\n            this.entityResult =\n                this.entityResult * 16 + ((c | 0x20) - CharCodes.LowerA + 10);\n            this.entityExcess++;\n        }\n        else {\n            if (this.allowLegacyEntity()) {\n                this.emitNumericEntity(false);\n            }\n            else {\n                this.state = this.baseState;\n            }\n            this.index--;\n        }\n    }\n    allowLegacyEntity() {\n        return (!this.xmlMode &&\n            (this.baseState === State.Text ||\n                this.baseState === State.InSpecialTag));\n    }\n    /**\n     * Remove data that has already been consumed from the buffer.\n     */\n    cleanup() {\n        // If we are inside of text or attributes, emit what we already have.\n        if (this.running && this.sectionStart !== this.index) {\n            if (this.state === State.Text ||\n                (this.state === State.InSpecialTag && this.sequenceIndex === 0)) {\n                this.cbs.ontext(this.sectionStart, this.index);\n                this.sectionStart = this.index;\n            }\n            else if (this.state === State.InAttributeValueDq ||\n                this.state === State.InAttributeValueSq ||\n                this.state === State.InAttributeValueNq) {\n                this.cbs.onattribdata(this.sectionStart, this.index);\n                this.sectionStart = this.index;\n            }\n        }\n    }\n    shouldContinue() {\n        return this.index < this.buffer.length + this.offset && this.running;\n    }\n    /**\n     * Iterates through the buffer, calling the function corresponding to the current state.\n     *\n     * States that are more likely to be hit are higher up, as a performance improvement.\n     */\n    parse() {\n        while (this.shouldContinue()) {\n            const c = this.buffer.charCodeAt(this.index - this.offset);\n            switch (this.state) {\n                case State.Text: {\n                    this.stateText(c);\n                    break;\n                }\n                case State.SpecialStartSequence: {\n                    this.stateSpecialStartSequence(c);\n                    break;\n                }\n                case State.InSpecialTag: {\n                    this.stateInSpecialTag(c);\n                    break;\n                }\n                case State.CDATASequence: {\n                    this.stateCDATASequence(c);\n                    break;\n                }\n                case State.InAttributeValueDq: {\n                    this.stateInAttributeValueDoubleQuotes(c);\n                    break;\n                }\n                case State.InAttributeName: {\n                    this.stateInAttributeName(c);\n                    break;\n                }\n                case State.InCommentLike: {\n                    this.stateInCommentLike(c);\n                    break;\n                }\n                case State.InSpecialComment: {\n                    this.stateInSpecialComment(c);\n                    break;\n                }\n                case State.BeforeAttributeName: {\n                    this.stateBeforeAttributeName(c);\n                    break;\n                }\n                case State.InTagName: {\n                    this.stateInTagName(c);\n                    break;\n                }\n                case State.InClosingTagName: {\n                    this.stateInClosingTagName(c);\n                    break;\n                }\n                case State.BeforeTagName: {\n                    this.stateBeforeTagName(c);\n                    break;\n                }\n                case State.AfterAttributeName: {\n                    this.stateAfterAttributeName(c);\n                    break;\n                }\n                case State.InAttributeValueSq: {\n                    this.stateInAttributeValueSingleQuotes(c);\n                    break;\n                }\n                case State.BeforeAttributeValue: {\n                    this.stateBeforeAttributeValue(c);\n                    break;\n                }\n                case State.BeforeClosingTagName: {\n                    this.stateBeforeClosingTagName(c);\n                    break;\n                }\n                case State.AfterClosingTagName: {\n                    this.stateAfterClosingTagName(c);\n                    break;\n                }\n                case State.BeforeSpecialS: {\n                    this.stateBeforeSpecialS(c);\n                    break;\n                }\n                case State.InAttributeValueNq: {\n                    this.stateInAttributeValueNoQuotes(c);\n                    break;\n                }\n                case State.InSelfClosingTag: {\n                    this.stateInSelfClosingTag(c);\n                    break;\n                }\n                case State.InDeclaration: {\n                    this.stateInDeclaration(c);\n                    break;\n                }\n                case State.BeforeDeclaration: {\n                    this.stateBeforeDeclaration(c);\n                    break;\n                }\n                case State.BeforeComment: {\n                    this.stateBeforeComment(c);\n                    break;\n                }\n                case State.InProcessingInstruction: {\n                    this.stateInProcessingInstruction(c);\n                    break;\n                }\n                case State.InNamedEntity: {\n                    this.stateInNamedEntity(c);\n                    break;\n                }\n                case State.BeforeEntity: {\n                    this.stateBeforeEntity(c);\n                    break;\n                }\n                case State.InHexEntity: {\n                    this.stateInHexEntity(c);\n                    break;\n                }\n                case State.InNumericEntity: {\n                    this.stateInNumericEntity(c);\n                    break;\n                }\n                default: {\n                    // `this._state === State.BeforeNumericEntity`\n                    this.stateBeforeNumericEntity(c);\n                }\n            }\n            this.index++;\n        }\n        this.cleanup();\n    }\n    finish() {\n        if (this.state === State.InNamedEntity) {\n            this.emitNamedEntity();\n        }\n        // If there is remaining data, emit it in a reasonable way\n        if (this.sectionStart < this.index) {\n            this.handleTrailingData();\n        }\n        this.cbs.onend();\n    }\n    /** Handle any trailing data. */\n    handleTrailingData() {\n        const endIndex = this.buffer.length + this.offset;\n        if (this.state === State.InCommentLike) {\n            if (this.currentSequence === Sequences.CdataEnd) {\n                this.cbs.oncdata(this.sectionStart, endIndex, 0);\n            }\n            else {\n                this.cbs.oncomment(this.sectionStart, endIndex, 0);\n            }\n        }\n        else if (this.state === State.InNumericEntity &&\n            this.allowLegacyEntity()) {\n            this.emitNumericEntity(false);\n            // All trailing data will have been consumed\n        }\n        else if (this.state === State.InHexEntity &&\n            this.allowLegacyEntity()) {\n            this.emitNumericEntity(false);\n            // All trailing data will have been consumed\n        }\n        else if (this.state === State.InTagName ||\n            this.state === State.BeforeAttributeName ||\n            this.state === State.BeforeAttributeValue ||\n            this.state === State.AfterAttributeName ||\n            this.state === State.InAttributeName ||\n            this.state === State.InAttributeValueSq ||\n            this.state === State.InAttributeValueDq ||\n            this.state === State.InAttributeValueNq ||\n            this.state === State.InClosingTagName) {\n            /*\n             * If we are currently in an opening or closing tag, us not calling the\n             * respective callback signals that the tag should be ignored.\n             */\n        }\n        else {\n            this.cbs.ontext(this.sectionStart, endIndex);\n        }\n    }\n    emitPartial(start, endIndex) {\n        if (this.baseState !== State.Text &&\n            this.baseState !== State.InSpecialTag) {\n            this.cbs.onattribdata(start, endIndex);\n        }\n        else {\n            this.cbs.ontext(start, endIndex);\n        }\n    }\n    emitCodePoint(cp) {\n        if (this.baseState !== State.Text &&\n            this.baseState !== State.InSpecialTag) {\n            this.cbs.onattribentity(cp);\n        }\n        else {\n            this.cbs.ontextentity(cp);\n        }\n    }\n}\n//# sourceMappingURL=Tokenizer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaHRtbHBhcnNlcjIvbGliL2VzbS9Ub2tlbml6ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXlIO0FBQ3pIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDhCQUE4QjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsc0JBQXNCO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsOEJBQThCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDZTtBQUNmLGtCQUFrQix5Q0FBeUM7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxpRUFBYSxHQUFHLGtFQUFjO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbURBQW1EO0FBQ25EO0FBQ0Esd0JBQXdCLGdCQUFnQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZEQUE2RDtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0M7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0M7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1EQUFtRDtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsdUVBQWU7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLGdFQUFZO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtFQUFrRSxnRUFBWTtBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixnRUFBWTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQix3RUFBZ0I7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGVudGFiZWxsLy4vbm9kZV9tb2R1bGVzL2h0bWxwYXJzZXIyL2xpYi9lc20vVG9rZW5pemVyLmpzPzJmNDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaHRtbERlY29kZVRyZWUsIHhtbERlY29kZVRyZWUsIEJpblRyaWVGbGFncywgZGV0ZXJtaW5lQnJhbmNoLCByZXBsYWNlQ29kZVBvaW50LCB9IGZyb20gXCJlbnRpdGllcy9saWIvZGVjb2RlLmpzXCI7XG52YXIgQ2hhckNvZGVzO1xuKGZ1bmN0aW9uIChDaGFyQ29kZXMpIHtcbiAgICBDaGFyQ29kZXNbQ2hhckNvZGVzW1wiVGFiXCJdID0gOV0gPSBcIlRhYlwiO1xuICAgIENoYXJDb2Rlc1tDaGFyQ29kZXNbXCJOZXdMaW5lXCJdID0gMTBdID0gXCJOZXdMaW5lXCI7XG4gICAgQ2hhckNvZGVzW0NoYXJDb2Rlc1tcIkZvcm1GZWVkXCJdID0gMTJdID0gXCJGb3JtRmVlZFwiO1xuICAgIENoYXJDb2Rlc1tDaGFyQ29kZXNbXCJDYXJyaWFnZVJldHVyblwiXSA9IDEzXSA9IFwiQ2FycmlhZ2VSZXR1cm5cIjtcbiAgICBDaGFyQ29kZXNbQ2hhckNvZGVzW1wiU3BhY2VcIl0gPSAzMl0gPSBcIlNwYWNlXCI7XG4gICAgQ2hhckNvZGVzW0NoYXJDb2Rlc1tcIkV4Y2xhbWF0aW9uTWFya1wiXSA9IDMzXSA9IFwiRXhjbGFtYXRpb25NYXJrXCI7XG4gICAgQ2hhckNvZGVzW0NoYXJDb2Rlc1tcIk51bWJlclwiXSA9IDM1XSA9IFwiTnVtYmVyXCI7XG4gICAgQ2hhckNvZGVzW0NoYXJDb2Rlc1tcIkFtcFwiXSA9IDM4XSA9IFwiQW1wXCI7XG4gICAgQ2hhckNvZGVzW0NoYXJDb2Rlc1tcIlNpbmdsZVF1b3RlXCJdID0gMzldID0gXCJTaW5nbGVRdW90ZVwiO1xuICAgIENoYXJDb2Rlc1tDaGFyQ29kZXNbXCJEb3VibGVRdW90ZVwiXSA9IDM0XSA9IFwiRG91YmxlUXVvdGVcIjtcbiAgICBDaGFyQ29kZXNbQ2hhckNvZGVzW1wiRGFzaFwiXSA9IDQ1XSA9IFwiRGFzaFwiO1xuICAgIENoYXJDb2Rlc1tDaGFyQ29kZXNbXCJTbGFzaFwiXSA9IDQ3XSA9IFwiU2xhc2hcIjtcbiAgICBDaGFyQ29kZXNbQ2hhckNvZGVzW1wiWmVyb1wiXSA9IDQ4XSA9IFwiWmVyb1wiO1xuICAgIENoYXJDb2Rlc1tDaGFyQ29kZXNbXCJOaW5lXCJdID0gNTddID0gXCJOaW5lXCI7XG4gICAgQ2hhckNvZGVzW0NoYXJDb2Rlc1tcIlNlbWlcIl0gPSA1OV0gPSBcIlNlbWlcIjtcbiAgICBDaGFyQ29kZXNbQ2hhckNvZGVzW1wiTHRcIl0gPSA2MF0gPSBcIkx0XCI7XG4gICAgQ2hhckNvZGVzW0NoYXJDb2Rlc1tcIkVxXCJdID0gNjFdID0gXCJFcVwiO1xuICAgIENoYXJDb2Rlc1tDaGFyQ29kZXNbXCJHdFwiXSA9IDYyXSA9IFwiR3RcIjtcbiAgICBDaGFyQ29kZXNbQ2hhckNvZGVzW1wiUXVlc3Rpb25tYXJrXCJdID0gNjNdID0gXCJRdWVzdGlvbm1hcmtcIjtcbiAgICBDaGFyQ29kZXNbQ2hhckNvZGVzW1wiVXBwZXJBXCJdID0gNjVdID0gXCJVcHBlckFcIjtcbiAgICBDaGFyQ29kZXNbQ2hhckNvZGVzW1wiTG93ZXJBXCJdID0gOTddID0gXCJMb3dlckFcIjtcbiAgICBDaGFyQ29kZXNbQ2hhckNvZGVzW1wiVXBwZXJGXCJdID0gNzBdID0gXCJVcHBlckZcIjtcbiAgICBDaGFyQ29kZXNbQ2hhckNvZGVzW1wiTG93ZXJGXCJdID0gMTAyXSA9IFwiTG93ZXJGXCI7XG4gICAgQ2hhckNvZGVzW0NoYXJDb2Rlc1tcIlVwcGVyWlwiXSA9IDkwXSA9IFwiVXBwZXJaXCI7XG4gICAgQ2hhckNvZGVzW0NoYXJDb2Rlc1tcIkxvd2VyWlwiXSA9IDEyMl0gPSBcIkxvd2VyWlwiO1xuICAgIENoYXJDb2Rlc1tDaGFyQ29kZXNbXCJMb3dlclhcIl0gPSAxMjBdID0gXCJMb3dlclhcIjtcbiAgICBDaGFyQ29kZXNbQ2hhckNvZGVzW1wiT3BlbmluZ1NxdWFyZUJyYWNrZXRcIl0gPSA5MV0gPSBcIk9wZW5pbmdTcXVhcmVCcmFja2V0XCI7XG59KShDaGFyQ29kZXMgfHwgKENoYXJDb2RlcyA9IHt9KSk7XG4vKiogQWxsIHRoZSBzdGF0ZXMgdGhlIHRva2VuaXplciBjYW4gYmUgaW4uICovXG52YXIgU3RhdGU7XG4oZnVuY3Rpb24gKFN0YXRlKSB7XG4gICAgU3RhdGVbU3RhdGVbXCJUZXh0XCJdID0gMV0gPSBcIlRleHRcIjtcbiAgICBTdGF0ZVtTdGF0ZVtcIkJlZm9yZVRhZ05hbWVcIl0gPSAyXSA9IFwiQmVmb3JlVGFnTmFtZVwiO1xuICAgIFN0YXRlW1N0YXRlW1wiSW5UYWdOYW1lXCJdID0gM10gPSBcIkluVGFnTmFtZVwiO1xuICAgIFN0YXRlW1N0YXRlW1wiSW5TZWxmQ2xvc2luZ1RhZ1wiXSA9IDRdID0gXCJJblNlbGZDbG9zaW5nVGFnXCI7XG4gICAgU3RhdGVbU3RhdGVbXCJCZWZvcmVDbG9zaW5nVGFnTmFtZVwiXSA9IDVdID0gXCJCZWZvcmVDbG9zaW5nVGFnTmFtZVwiO1xuICAgIFN0YXRlW1N0YXRlW1wiSW5DbG9zaW5nVGFnTmFtZVwiXSA9IDZdID0gXCJJbkNsb3NpbmdUYWdOYW1lXCI7XG4gICAgU3RhdGVbU3RhdGVbXCJBZnRlckNsb3NpbmdUYWdOYW1lXCJdID0gN10gPSBcIkFmdGVyQ2xvc2luZ1RhZ05hbWVcIjtcbiAgICAvLyBBdHRyaWJ1dGVzXG4gICAgU3RhdGVbU3RhdGVbXCJCZWZvcmVBdHRyaWJ1dGVOYW1lXCJdID0gOF0gPSBcIkJlZm9yZUF0dHJpYnV0ZU5hbWVcIjtcbiAgICBTdGF0ZVtTdGF0ZVtcIkluQXR0cmlidXRlTmFtZVwiXSA9IDldID0gXCJJbkF0dHJpYnV0ZU5hbWVcIjtcbiAgICBTdGF0ZVtTdGF0ZVtcIkFmdGVyQXR0cmlidXRlTmFtZVwiXSA9IDEwXSA9IFwiQWZ0ZXJBdHRyaWJ1dGVOYW1lXCI7XG4gICAgU3RhdGVbU3RhdGVbXCJCZWZvcmVBdHRyaWJ1dGVWYWx1ZVwiXSA9IDExXSA9IFwiQmVmb3JlQXR0cmlidXRlVmFsdWVcIjtcbiAgICBTdGF0ZVtTdGF0ZVtcIkluQXR0cmlidXRlVmFsdWVEcVwiXSA9IDEyXSA9IFwiSW5BdHRyaWJ1dGVWYWx1ZURxXCI7XG4gICAgU3RhdGVbU3RhdGVbXCJJbkF0dHJpYnV0ZVZhbHVlU3FcIl0gPSAxM10gPSBcIkluQXR0cmlidXRlVmFsdWVTcVwiO1xuICAgIFN0YXRlW1N0YXRlW1wiSW5BdHRyaWJ1dGVWYWx1ZU5xXCJdID0gMTRdID0gXCJJbkF0dHJpYnV0ZVZhbHVlTnFcIjtcbiAgICAvLyBEZWNsYXJhdGlvbnNcbiAgICBTdGF0ZVtTdGF0ZVtcIkJlZm9yZURlY2xhcmF0aW9uXCJdID0gMTVdID0gXCJCZWZvcmVEZWNsYXJhdGlvblwiO1xuICAgIFN0YXRlW1N0YXRlW1wiSW5EZWNsYXJhdGlvblwiXSA9IDE2XSA9IFwiSW5EZWNsYXJhdGlvblwiO1xuICAgIC8vIFByb2Nlc3NpbmcgaW5zdHJ1Y3Rpb25zXG4gICAgU3RhdGVbU3RhdGVbXCJJblByb2Nlc3NpbmdJbnN0cnVjdGlvblwiXSA9IDE3XSA9IFwiSW5Qcm9jZXNzaW5nSW5zdHJ1Y3Rpb25cIjtcbiAgICAvLyBDb21tZW50cyAmIENEQVRBXG4gICAgU3RhdGVbU3RhdGVbXCJCZWZvcmVDb21tZW50XCJdID0gMThdID0gXCJCZWZvcmVDb21tZW50XCI7XG4gICAgU3RhdGVbU3RhdGVbXCJDREFUQVNlcXVlbmNlXCJdID0gMTldID0gXCJDREFUQVNlcXVlbmNlXCI7XG4gICAgU3RhdGVbU3RhdGVbXCJJblNwZWNpYWxDb21tZW50XCJdID0gMjBdID0gXCJJblNwZWNpYWxDb21tZW50XCI7XG4gICAgU3RhdGVbU3RhdGVbXCJJbkNvbW1lbnRMaWtlXCJdID0gMjFdID0gXCJJbkNvbW1lbnRMaWtlXCI7XG4gICAgLy8gU3BlY2lhbCB0YWdzXG4gICAgU3RhdGVbU3RhdGVbXCJCZWZvcmVTcGVjaWFsU1wiXSA9IDIyXSA9IFwiQmVmb3JlU3BlY2lhbFNcIjtcbiAgICBTdGF0ZVtTdGF0ZVtcIlNwZWNpYWxTdGFydFNlcXVlbmNlXCJdID0gMjNdID0gXCJTcGVjaWFsU3RhcnRTZXF1ZW5jZVwiO1xuICAgIFN0YXRlW1N0YXRlW1wiSW5TcGVjaWFsVGFnXCJdID0gMjRdID0gXCJJblNwZWNpYWxUYWdcIjtcbiAgICBTdGF0ZVtTdGF0ZVtcIkJlZm9yZUVudGl0eVwiXSA9IDI1XSA9IFwiQmVmb3JlRW50aXR5XCI7XG4gICAgU3RhdGVbU3RhdGVbXCJCZWZvcmVOdW1lcmljRW50aXR5XCJdID0gMjZdID0gXCJCZWZvcmVOdW1lcmljRW50aXR5XCI7XG4gICAgU3RhdGVbU3RhdGVbXCJJbk5hbWVkRW50aXR5XCJdID0gMjddID0gXCJJbk5hbWVkRW50aXR5XCI7XG4gICAgU3RhdGVbU3RhdGVbXCJJbk51bWVyaWNFbnRpdHlcIl0gPSAyOF0gPSBcIkluTnVtZXJpY0VudGl0eVwiO1xuICAgIFN0YXRlW1N0YXRlW1wiSW5IZXhFbnRpdHlcIl0gPSAyOV0gPSBcIkluSGV4RW50aXR5XCI7XG59KShTdGF0ZSB8fCAoU3RhdGUgPSB7fSkpO1xuZnVuY3Rpb24gaXNXaGl0ZXNwYWNlKGMpIHtcbiAgICByZXR1cm4gKGMgPT09IENoYXJDb2Rlcy5TcGFjZSB8fFxuICAgICAgICBjID09PSBDaGFyQ29kZXMuTmV3TGluZSB8fFxuICAgICAgICBjID09PSBDaGFyQ29kZXMuVGFiIHx8XG4gICAgICAgIGMgPT09IENoYXJDb2Rlcy5Gb3JtRmVlZCB8fFxuICAgICAgICBjID09PSBDaGFyQ29kZXMuQ2FycmlhZ2VSZXR1cm4pO1xufVxuZnVuY3Rpb24gaXNFbmRPZlRhZ1NlY3Rpb24oYykge1xuICAgIHJldHVybiBjID09PSBDaGFyQ29kZXMuU2xhc2ggfHwgYyA9PT0gQ2hhckNvZGVzLkd0IHx8IGlzV2hpdGVzcGFjZShjKTtcbn1cbmZ1bmN0aW9uIGlzTnVtYmVyKGMpIHtcbiAgICByZXR1cm4gYyA+PSBDaGFyQ29kZXMuWmVybyAmJiBjIDw9IENoYXJDb2Rlcy5OaW5lO1xufVxuZnVuY3Rpb24gaXNBU0NJSUFscGhhKGMpIHtcbiAgICByZXR1cm4gKChjID49IENoYXJDb2Rlcy5Mb3dlckEgJiYgYyA8PSBDaGFyQ29kZXMuTG93ZXJaKSB8fFxuICAgICAgICAoYyA+PSBDaGFyQ29kZXMuVXBwZXJBICYmIGMgPD0gQ2hhckNvZGVzLlVwcGVyWikpO1xufVxuZnVuY3Rpb24gaXNIZXhEaWdpdChjKSB7XG4gICAgcmV0dXJuICgoYyA+PSBDaGFyQ29kZXMuVXBwZXJBICYmIGMgPD0gQ2hhckNvZGVzLlVwcGVyRikgfHxcbiAgICAgICAgKGMgPj0gQ2hhckNvZGVzLkxvd2VyQSAmJiBjIDw9IENoYXJDb2Rlcy5Mb3dlckYpKTtcbn1cbmV4cG9ydCB2YXIgUXVvdGVUeXBlO1xuKGZ1bmN0aW9uIChRdW90ZVR5cGUpIHtcbiAgICBRdW90ZVR5cGVbUXVvdGVUeXBlW1wiTm9WYWx1ZVwiXSA9IDBdID0gXCJOb1ZhbHVlXCI7XG4gICAgUXVvdGVUeXBlW1F1b3RlVHlwZVtcIlVucXVvdGVkXCJdID0gMV0gPSBcIlVucXVvdGVkXCI7XG4gICAgUXVvdGVUeXBlW1F1b3RlVHlwZVtcIlNpbmdsZVwiXSA9IDJdID0gXCJTaW5nbGVcIjtcbiAgICBRdW90ZVR5cGVbUXVvdGVUeXBlW1wiRG91YmxlXCJdID0gM10gPSBcIkRvdWJsZVwiO1xufSkoUXVvdGVUeXBlIHx8IChRdW90ZVR5cGUgPSB7fSkpO1xuLyoqXG4gKiBTZXF1ZW5jZXMgdXNlZCB0byBtYXRjaCBsb25nZXIgc3RyaW5ncy5cbiAqXG4gKiBXZSBkb24ndCBoYXZlIGBTY3JpcHRgLCBgU3R5bGVgLCBvciBgVGl0bGVgIGhlcmUuIEluc3RlYWQsIHdlIHJlLXVzZSB0aGUgKkVuZFxuICogc2VxdWVuY2VzIHdpdGggYW4gaW5jcmVhc2VkIG9mZnNldC5cbiAqL1xuY29uc3QgU2VxdWVuY2VzID0ge1xuICAgIENkYXRhOiBuZXcgVWludDhBcnJheShbMHg0MywgMHg0NCwgMHg0MSwgMHg1NCwgMHg0MSwgMHg1Yl0pLFxuICAgIENkYXRhRW5kOiBuZXcgVWludDhBcnJheShbMHg1ZCwgMHg1ZCwgMHgzZV0pLFxuICAgIENvbW1lbnRFbmQ6IG5ldyBVaW50OEFycmF5KFsweDJkLCAweDJkLCAweDNlXSksXG4gICAgU2NyaXB0RW5kOiBuZXcgVWludDhBcnJheShbMHgzYywgMHgyZiwgMHg3MywgMHg2MywgMHg3MiwgMHg2OSwgMHg3MCwgMHg3NF0pLFxuICAgIFN0eWxlRW5kOiBuZXcgVWludDhBcnJheShbMHgzYywgMHgyZiwgMHg3MywgMHg3NCwgMHg3OSwgMHg2YywgMHg2NV0pLFxuICAgIFRpdGxlRW5kOiBuZXcgVWludDhBcnJheShbMHgzYywgMHgyZiwgMHg3NCwgMHg2OSwgMHg3NCwgMHg2YywgMHg2NV0pLCAvLyBgPC90aXRsZWBcbn07XG5leHBvcnQgZGVmYXVsdCBjbGFzcyBUb2tlbml6ZXIge1xuICAgIGNvbnN0cnVjdG9yKHsgeG1sTW9kZSA9IGZhbHNlLCBkZWNvZGVFbnRpdGllcyA9IHRydWUsIH0sIGNicykge1xuICAgICAgICB0aGlzLmNicyA9IGNicztcbiAgICAgICAgLyoqIFRoZSBjdXJyZW50IHN0YXRlIHRoZSB0b2tlbml6ZXIgaXMgaW4uICovXG4gICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5UZXh0O1xuICAgICAgICAvKiogVGhlIHJlYWQgYnVmZmVyLiAqL1xuICAgICAgICB0aGlzLmJ1ZmZlciA9IFwiXCI7XG4gICAgICAgIC8qKiBUaGUgYmVnaW5uaW5nIG9mIHRoZSBzZWN0aW9uIHRoYXQgaXMgY3VycmVudGx5IGJlaW5nIHJlYWQuICovXG4gICAgICAgIHRoaXMuc2VjdGlvblN0YXJ0ID0gMDtcbiAgICAgICAgLyoqIFRoZSBpbmRleCB3aXRoaW4gdGhlIGJ1ZmZlciB0aGF0IHdlIGFyZSBjdXJyZW50bHkgbG9va2luZyBhdC4gKi9cbiAgICAgICAgdGhpcy5pbmRleCA9IDA7XG4gICAgICAgIC8qKiBTb21lIGJlaGF2aW9yLCBlZy4gd2hlbiBkZWNvZGluZyBlbnRpdGllcywgaXMgZG9uZSB3aGlsZSB3ZSBhcmUgaW4gYW5vdGhlciBzdGF0ZS4gVGhpcyBrZWVwcyB0cmFjayBvZiB0aGUgb3RoZXIgc3RhdGUgdHlwZS4gKi9cbiAgICAgICAgdGhpcy5iYXNlU3RhdGUgPSBTdGF0ZS5UZXh0O1xuICAgICAgICAvKiogRm9yIHNwZWNpYWwgcGFyc2luZyBiZWhhdmlvciBpbnNpZGUgb2Ygc2NyaXB0IGFuZCBzdHlsZSB0YWdzLiAqL1xuICAgICAgICB0aGlzLmlzU3BlY2lhbCA9IGZhbHNlO1xuICAgICAgICAvKiogSW5kaWNhdGVzIHdoZXRoZXIgdGhlIHRva2VuaXplciBoYXMgYmVlbiBwYXVzZWQuICovXG4gICAgICAgIHRoaXMucnVubmluZyA9IHRydWU7XG4gICAgICAgIC8qKiBUaGUgb2Zmc2V0IG9mIHRoZSBjdXJyZW50IGJ1ZmZlci4gKi9cbiAgICAgICAgdGhpcy5vZmZzZXQgPSAwO1xuICAgICAgICB0aGlzLmN1cnJlbnRTZXF1ZW5jZSA9IHVuZGVmaW5lZDtcbiAgICAgICAgdGhpcy5zZXF1ZW5jZUluZGV4ID0gMDtcbiAgICAgICAgdGhpcy50cmllSW5kZXggPSAwO1xuICAgICAgICB0aGlzLnRyaWVDdXJyZW50ID0gMDtcbiAgICAgICAgLyoqIEZvciBuYW1lZCBlbnRpdGllcywgdGhlIGluZGV4IG9mIHRoZSB2YWx1ZS4gRm9yIG51bWVyaWMgZW50aXRpZXMsIHRoZSBjb2RlIHBvaW50LiAqL1xuICAgICAgICB0aGlzLmVudGl0eVJlc3VsdCA9IDA7XG4gICAgICAgIHRoaXMuZW50aXR5RXhjZXNzID0gMDtcbiAgICAgICAgdGhpcy54bWxNb2RlID0geG1sTW9kZTtcbiAgICAgICAgdGhpcy5kZWNvZGVFbnRpdGllcyA9IGRlY29kZUVudGl0aWVzO1xuICAgICAgICB0aGlzLmVudGl0eVRyaWUgPSB4bWxNb2RlID8geG1sRGVjb2RlVHJlZSA6IGh0bWxEZWNvZGVUcmVlO1xuICAgIH1cbiAgICByZXNldCgpIHtcbiAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLlRleHQ7XG4gICAgICAgIHRoaXMuYnVmZmVyID0gXCJcIjtcbiAgICAgICAgdGhpcy5zZWN0aW9uU3RhcnQgPSAwO1xuICAgICAgICB0aGlzLmluZGV4ID0gMDtcbiAgICAgICAgdGhpcy5iYXNlU3RhdGUgPSBTdGF0ZS5UZXh0O1xuICAgICAgICB0aGlzLmN1cnJlbnRTZXF1ZW5jZSA9IHVuZGVmaW5lZDtcbiAgICAgICAgdGhpcy5ydW5uaW5nID0gdHJ1ZTtcbiAgICAgICAgdGhpcy5vZmZzZXQgPSAwO1xuICAgIH1cbiAgICB3cml0ZShjaHVuaykge1xuICAgICAgICB0aGlzLm9mZnNldCArPSB0aGlzLmJ1ZmZlci5sZW5ndGg7XG4gICAgICAgIHRoaXMuYnVmZmVyID0gY2h1bms7XG4gICAgICAgIHRoaXMucGFyc2UoKTtcbiAgICB9XG4gICAgZW5kKCkge1xuICAgICAgICBpZiAodGhpcy5ydW5uaW5nKVxuICAgICAgICAgICAgdGhpcy5maW5pc2goKTtcbiAgICB9XG4gICAgcGF1c2UoKSB7XG4gICAgICAgIHRoaXMucnVubmluZyA9IGZhbHNlO1xuICAgIH1cbiAgICByZXN1bWUoKSB7XG4gICAgICAgIHRoaXMucnVubmluZyA9IHRydWU7XG4gICAgICAgIGlmICh0aGlzLmluZGV4IDwgdGhpcy5idWZmZXIubGVuZ3RoICsgdGhpcy5vZmZzZXQpIHtcbiAgICAgICAgICAgIHRoaXMucGFyc2UoKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBUaGUgY3VycmVudCBpbmRleCB3aXRoaW4gYWxsIG9mIHRoZSB3cml0dGVuIGRhdGEuXG4gICAgICovXG4gICAgZ2V0SW5kZXgoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmluZGV4O1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBUaGUgc3RhcnQgb2YgdGhlIGN1cnJlbnQgc2VjdGlvbi5cbiAgICAgKi9cbiAgICBnZXRTZWN0aW9uU3RhcnQoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnNlY3Rpb25TdGFydDtcbiAgICB9XG4gICAgc3RhdGVUZXh0KGMpIHtcbiAgICAgICAgaWYgKGMgPT09IENoYXJDb2Rlcy5MdCB8fFxuICAgICAgICAgICAgKCF0aGlzLmRlY29kZUVudGl0aWVzICYmIHRoaXMuZmFzdEZvcndhcmRUbyhDaGFyQ29kZXMuTHQpKSkge1xuICAgICAgICAgICAgaWYgKHRoaXMuaW5kZXggPiB0aGlzLnNlY3Rpb25TdGFydCkge1xuICAgICAgICAgICAgICAgIHRoaXMuY2JzLm9udGV4dCh0aGlzLnNlY3Rpb25TdGFydCwgdGhpcy5pbmRleCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlVGFnTmFtZTtcbiAgICAgICAgICAgIHRoaXMuc2VjdGlvblN0YXJ0ID0gdGhpcy5pbmRleDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICh0aGlzLmRlY29kZUVudGl0aWVzICYmIGMgPT09IENoYXJDb2Rlcy5BbXApIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5CZWZvcmVFbnRpdHk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVTcGVjaWFsU3RhcnRTZXF1ZW5jZShjKSB7XG4gICAgICAgIGNvbnN0IGlzRW5kID0gdGhpcy5zZXF1ZW5jZUluZGV4ID09PSB0aGlzLmN1cnJlbnRTZXF1ZW5jZS5sZW5ndGg7XG4gICAgICAgIGNvbnN0IGlzTWF0Y2ggPSBpc0VuZFxuICAgICAgICAgICAgPyAvLyBJZiB3ZSBhcmUgYXQgdGhlIGVuZCBvZiB0aGUgc2VxdWVuY2UsIG1ha2Ugc3VyZSB0aGUgdGFnIG5hbWUgaGFzIGVuZGVkXG4gICAgICAgICAgICAgICAgaXNFbmRPZlRhZ1NlY3Rpb24oYylcbiAgICAgICAgICAgIDogLy8gT3RoZXJ3aXNlLCBkbyBhIGNhc2UtaW5zZW5zaXRpdmUgY29tcGFyaXNvblxuICAgICAgICAgICAgICAgIChjIHwgMHgyMCkgPT09IHRoaXMuY3VycmVudFNlcXVlbmNlW3RoaXMuc2VxdWVuY2VJbmRleF07XG4gICAgICAgIGlmICghaXNNYXRjaCkge1xuICAgICAgICAgICAgdGhpcy5pc1NwZWNpYWwgPSBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICghaXNFbmQpIHtcbiAgICAgICAgICAgIHRoaXMuc2VxdWVuY2VJbmRleCsrO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuc2VxdWVuY2VJbmRleCA9IDA7XG4gICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5JblRhZ05hbWU7XG4gICAgICAgIHRoaXMuc3RhdGVJblRhZ05hbWUoYyk7XG4gICAgfVxuICAgIC8qKiBMb29rIGZvciBhbiBlbmQgdGFnLiBGb3IgPHRpdGxlPiB0YWdzLCBhbHNvIGRlY29kZSBlbnRpdGllcy4gKi9cbiAgICBzdGF0ZUluU3BlY2lhbFRhZyhjKSB7XG4gICAgICAgIGlmICh0aGlzLnNlcXVlbmNlSW5kZXggPT09IHRoaXMuY3VycmVudFNlcXVlbmNlLmxlbmd0aCkge1xuICAgICAgICAgICAgaWYgKGMgPT09IENoYXJDb2Rlcy5HdCB8fCBpc1doaXRlc3BhY2UoYykpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBlbmRPZlRleHQgPSB0aGlzLmluZGV4IC0gdGhpcy5jdXJyZW50U2VxdWVuY2UubGVuZ3RoO1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLnNlY3Rpb25TdGFydCA8IGVuZE9mVGV4dCkge1xuICAgICAgICAgICAgICAgICAgICAvLyBTcG9vZiB0aGUgaW5kZXggc28gdGhhdCByZXBvcnRlZCBsb2NhdGlvbnMgbWF0Y2ggdXAuXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGFjdHVhbEluZGV4ID0gdGhpcy5pbmRleDtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5pbmRleCA9IGVuZE9mVGV4dDtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jYnMub250ZXh0KHRoaXMuc2VjdGlvblN0YXJ0LCBlbmRPZlRleHQpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmluZGV4ID0gYWN0dWFsSW5kZXg7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMuaXNTcGVjaWFsID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgdGhpcy5zZWN0aW9uU3RhcnQgPSBlbmRPZlRleHQgKyAyOyAvLyBTa2lwIG92ZXIgdGhlIGA8L2BcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlSW5DbG9zaW5nVGFnTmFtZShjKTtcbiAgICAgICAgICAgICAgICByZXR1cm47IC8vIFdlIGFyZSBkb25lOyBza2lwIHRoZSByZXN0IG9mIHRoZSBmdW5jdGlvbi5cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuc2VxdWVuY2VJbmRleCA9IDA7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKChjIHwgMHgyMCkgPT09IHRoaXMuY3VycmVudFNlcXVlbmNlW3RoaXMuc2VxdWVuY2VJbmRleF0pIHtcbiAgICAgICAgICAgIHRoaXMuc2VxdWVuY2VJbmRleCArPSAxO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHRoaXMuc2VxdWVuY2VJbmRleCA9PT0gMCkge1xuICAgICAgICAgICAgaWYgKHRoaXMuY3VycmVudFNlcXVlbmNlID09PSBTZXF1ZW5jZXMuVGl0bGVFbmQpIHtcbiAgICAgICAgICAgICAgICAvLyBXZSBoYXZlIHRvIHBhcnNlIGVudGl0aWVzIGluIDx0aXRsZT4gdGFncy5cbiAgICAgICAgICAgICAgICBpZiAodGhpcy5kZWNvZGVFbnRpdGllcyAmJiBjID09PSBDaGFyQ29kZXMuQW1wKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5CZWZvcmVFbnRpdHk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAodGhpcy5mYXN0Rm9yd2FyZFRvKENoYXJDb2Rlcy5MdCkpIHtcbiAgICAgICAgICAgICAgICAvLyBPdXRzaWRlIG9mIDx0aXRsZT4gdGFncywgd2UgY2FuIGZhc3QtZm9yd2FyZC5cbiAgICAgICAgICAgICAgICB0aGlzLnNlcXVlbmNlSW5kZXggPSAxO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgLy8gSWYgd2Ugc2VlIGEgYDxgLCBzZXQgdGhlIHNlcXVlbmNlIGluZGV4IHRvIDE7IHVzZWZ1bCBmb3IgZWcuIGA8PC9zY3JpcHQ+YC5cbiAgICAgICAgICAgIHRoaXMuc2VxdWVuY2VJbmRleCA9IE51bWJlcihjID09PSBDaGFyQ29kZXMuTHQpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlQ0RBVEFTZXF1ZW5jZShjKSB7XG4gICAgICAgIGlmIChjID09PSBTZXF1ZW5jZXMuQ2RhdGFbdGhpcy5zZXF1ZW5jZUluZGV4XSkge1xuICAgICAgICAgICAgaWYgKCsrdGhpcy5zZXF1ZW5jZUluZGV4ID09PSBTZXF1ZW5jZXMuQ2RhdGEubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkluQ29tbWVudExpa2U7XG4gICAgICAgICAgICAgICAgdGhpcy5jdXJyZW50U2VxdWVuY2UgPSBTZXF1ZW5jZXMuQ2RhdGFFbmQ7XG4gICAgICAgICAgICAgICAgdGhpcy5zZXF1ZW5jZUluZGV4ID0gMDtcbiAgICAgICAgICAgICAgICB0aGlzLnNlY3Rpb25TdGFydCA9IHRoaXMuaW5kZXggKyAxO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5zZXF1ZW5jZUluZGV4ID0gMDtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5JbkRlY2xhcmF0aW9uO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZUluRGVjbGFyYXRpb24oYyk7IC8vIFJlY29uc3VtZSB0aGUgY2hhcmFjdGVyXG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogV2hlbiB3ZSB3YWl0IGZvciBvbmUgc3BlY2lmaWMgY2hhcmFjdGVyLCB3ZSBjYW4gc3BlZWQgdGhpbmdzIHVwXG4gICAgICogYnkgc2tpcHBpbmcgdGhyb3VnaCB0aGUgYnVmZmVyIHVudGlsIHdlIGZpbmQgaXQuXG4gICAgICpcbiAgICAgKiBAcmV0dXJucyBXaGV0aGVyIHRoZSBjaGFyYWN0ZXIgd2FzIGZvdW5kLlxuICAgICAqL1xuICAgIGZhc3RGb3J3YXJkVG8oYykge1xuICAgICAgICB3aGlsZSAoKyt0aGlzLmluZGV4IDwgdGhpcy5idWZmZXIubGVuZ3RoICsgdGhpcy5vZmZzZXQpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLmJ1ZmZlci5jaGFyQ29kZUF0KHRoaXMuaW5kZXggLSB0aGlzLm9mZnNldCkgPT09IGMpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAvKlxuICAgICAgICAgKiBXZSBpbmNyZW1lbnQgdGhlIGluZGV4IGF0IHRoZSBlbmQgb2YgdGhlIGBwYXJzZWAgbG9vcCxcbiAgICAgICAgICogc28gc2V0IGl0IHRvIGBidWZmZXIubGVuZ3RoIC0gMWAgaGVyZS5cbiAgICAgICAgICpcbiAgICAgICAgICogVE9ETzogUmVmYWN0b3IgYHBhcnNlYCB0byBpbmNyZW1lbnQgaW5kZXggYmVmb3JlIGNhbGxpbmcgc3RhdGVzLlxuICAgICAgICAgKi9cbiAgICAgICAgdGhpcy5pbmRleCA9IHRoaXMuYnVmZmVyLmxlbmd0aCArIHRoaXMub2Zmc2V0IC0gMTtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDb21tZW50cyBhbmQgQ0RBVEEgZW5kIHdpdGggYC0tPmAgYW5kIGBdXT5gLlxuICAgICAqXG4gICAgICogVGhlaXIgY29tbW9uIHF1YWxpdGllcyBhcmU6XG4gICAgICogLSBUaGVpciBlbmQgc2VxdWVuY2VzIGhhdmUgYSBkaXN0aW5jdCBjaGFyYWN0ZXIgdGhleSBzdGFydCB3aXRoLlxuICAgICAqIC0gVGhhdCBjaGFyYWN0ZXIgaXMgdGhlbiByZXBlYXRlZCwgc28gd2UgaGF2ZSB0byBjaGVjayBtdWx0aXBsZSByZXBlYXRzLlxuICAgICAqIC0gQWxsIGNoYXJhY3RlcnMgYnV0IHRoZSBzdGFydCBjaGFyYWN0ZXIgb2YgdGhlIHNlcXVlbmNlIGNhbiBiZSBza2lwcGVkLlxuICAgICAqL1xuICAgIHN0YXRlSW5Db21tZW50TGlrZShjKSB7XG4gICAgICAgIGlmIChjID09PSB0aGlzLmN1cnJlbnRTZXF1ZW5jZVt0aGlzLnNlcXVlbmNlSW5kZXhdKSB7XG4gICAgICAgICAgICBpZiAoKyt0aGlzLnNlcXVlbmNlSW5kZXggPT09IHRoaXMuY3VycmVudFNlcXVlbmNlLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLmN1cnJlbnRTZXF1ZW5jZSA9PT0gU2VxdWVuY2VzLkNkYXRhRW5kKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY2JzLm9uY2RhdGEodGhpcy5zZWN0aW9uU3RhcnQsIHRoaXMuaW5kZXgsIDIpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jYnMub25jb21tZW50KHRoaXMuc2VjdGlvblN0YXJ0LCB0aGlzLmluZGV4LCAyKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhpcy5zZXF1ZW5jZUluZGV4ID0gMDtcbiAgICAgICAgICAgICAgICB0aGlzLnNlY3Rpb25TdGFydCA9IHRoaXMuaW5kZXggKyAxO1xuICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5UZXh0O1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHRoaXMuc2VxdWVuY2VJbmRleCA9PT0gMCkge1xuICAgICAgICAgICAgLy8gRmFzdC1mb3J3YXJkIHRvIHRoZSBmaXJzdCBjaGFyYWN0ZXIgb2YgdGhlIHNlcXVlbmNlXG4gICAgICAgICAgICBpZiAodGhpcy5mYXN0Rm9yd2FyZFRvKHRoaXMuY3VycmVudFNlcXVlbmNlWzBdKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuc2VxdWVuY2VJbmRleCA9IDE7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYyAhPT0gdGhpcy5jdXJyZW50U2VxdWVuY2VbdGhpcy5zZXF1ZW5jZUluZGV4IC0gMV0pIHtcbiAgICAgICAgICAgIC8vIEFsbG93IGxvbmcgc2VxdWVuY2VzLCBlZy4gLS0tPiwgXV1dPlxuICAgICAgICAgICAgdGhpcy5zZXF1ZW5jZUluZGV4ID0gMDtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBIVE1MIG9ubHkgYWxsb3dzIEFTQ0lJIGFscGhhIGNoYXJhY3RlcnMgKGEteiBhbmQgQS1aKSBhdCB0aGUgYmVnaW5uaW5nIG9mIGEgdGFnIG5hbWUuXG4gICAgICpcbiAgICAgKiBYTUwgYWxsb3dzIGEgbG90IG1vcmUgY2hhcmFjdGVycyBoZXJlIChAc2VlIGh0dHBzOi8vd3d3LnczLm9yZy9UUi9SRUMteG1sLyNOVC1OYW1lU3RhcnRDaGFyKS5cbiAgICAgKiBXZSBhbGxvdyBhbnl0aGluZyB0aGF0IHdvdWxkbid0IGVuZCB0aGUgdGFnLlxuICAgICAqL1xuICAgIGlzVGFnU3RhcnRDaGFyKGMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMueG1sTW9kZSA/ICFpc0VuZE9mVGFnU2VjdGlvbihjKSA6IGlzQVNDSUlBbHBoYShjKTtcbiAgICB9XG4gICAgc3RhcnRTcGVjaWFsKHNlcXVlbmNlLCBvZmZzZXQpIHtcbiAgICAgICAgdGhpcy5pc1NwZWNpYWwgPSB0cnVlO1xuICAgICAgICB0aGlzLmN1cnJlbnRTZXF1ZW5jZSA9IHNlcXVlbmNlO1xuICAgICAgICB0aGlzLnNlcXVlbmNlSW5kZXggPSBvZmZzZXQ7XG4gICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5TcGVjaWFsU3RhcnRTZXF1ZW5jZTtcbiAgICB9XG4gICAgc3RhdGVCZWZvcmVUYWdOYW1lKGMpIHtcbiAgICAgICAgaWYgKGMgPT09IENoYXJDb2Rlcy5FeGNsYW1hdGlvbk1hcmspIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5CZWZvcmVEZWNsYXJhdGlvbjtcbiAgICAgICAgICAgIHRoaXMuc2VjdGlvblN0YXJ0ID0gdGhpcy5pbmRleCArIDE7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYyA9PT0gQ2hhckNvZGVzLlF1ZXN0aW9ubWFyaykge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkluUHJvY2Vzc2luZ0luc3RydWN0aW9uO1xuICAgICAgICAgICAgdGhpcy5zZWN0aW9uU3RhcnQgPSB0aGlzLmluZGV4ICsgMTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICh0aGlzLmlzVGFnU3RhcnRDaGFyKGMpKSB7XG4gICAgICAgICAgICBjb25zdCBsb3dlciA9IGMgfCAweDIwO1xuICAgICAgICAgICAgdGhpcy5zZWN0aW9uU3RhcnQgPSB0aGlzLmluZGV4O1xuICAgICAgICAgICAgaWYgKCF0aGlzLnhtbE1vZGUgJiYgbG93ZXIgPT09IFNlcXVlbmNlcy5UaXRsZUVuZFsyXSkge1xuICAgICAgICAgICAgICAgIHRoaXMuc3RhcnRTcGVjaWFsKFNlcXVlbmNlcy5UaXRsZUVuZCwgMyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID1cbiAgICAgICAgICAgICAgICAgICAgIXRoaXMueG1sTW9kZSAmJiBsb3dlciA9PT0gU2VxdWVuY2VzLlNjcmlwdEVuZFsyXVxuICAgICAgICAgICAgICAgICAgICAgICAgPyBTdGF0ZS5CZWZvcmVTcGVjaWFsU1xuICAgICAgICAgICAgICAgICAgICAgICAgOiBTdGF0ZS5JblRhZ05hbWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYyA9PT0gQ2hhckNvZGVzLlNsYXNoKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlQ2xvc2luZ1RhZ05hbWU7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuVGV4dDtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVUZXh0KGMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlSW5UYWdOYW1lKGMpIHtcbiAgICAgICAgaWYgKGlzRW5kT2ZUYWdTZWN0aW9uKGMpKSB7XG4gICAgICAgICAgICB0aGlzLmNicy5vbm9wZW50YWduYW1lKHRoaXMuc2VjdGlvblN0YXJ0LCB0aGlzLmluZGV4KTtcbiAgICAgICAgICAgIHRoaXMuc2VjdGlvblN0YXJ0ID0gLTE7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlQXR0cmlidXRlTmFtZTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVCZWZvcmVBdHRyaWJ1dGVOYW1lKGMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlQmVmb3JlQ2xvc2luZ1RhZ05hbWUoYykge1xuICAgICAgICBpZiAoaXNXaGl0ZXNwYWNlKGMpKSB7XG4gICAgICAgICAgICAvLyBJZ25vcmVcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChjID09PSBDaGFyQ29kZXMuR3QpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5UZXh0O1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IHRoaXMuaXNUYWdTdGFydENoYXIoYylcbiAgICAgICAgICAgICAgICA/IFN0YXRlLkluQ2xvc2luZ1RhZ05hbWVcbiAgICAgICAgICAgICAgICA6IFN0YXRlLkluU3BlY2lhbENvbW1lbnQ7XG4gICAgICAgICAgICB0aGlzLnNlY3Rpb25TdGFydCA9IHRoaXMuaW5kZXg7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVJbkNsb3NpbmdUYWdOYW1lKGMpIHtcbiAgICAgICAgaWYgKGMgPT09IENoYXJDb2Rlcy5HdCB8fCBpc1doaXRlc3BhY2UoYykpIHtcbiAgICAgICAgICAgIHRoaXMuY2JzLm9uY2xvc2V0YWcodGhpcy5zZWN0aW9uU3RhcnQsIHRoaXMuaW5kZXgpO1xuICAgICAgICAgICAgdGhpcy5zZWN0aW9uU3RhcnQgPSAtMTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5BZnRlckNsb3NpbmdUYWdOYW1lO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZUFmdGVyQ2xvc2luZ1RhZ05hbWUoYyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVBZnRlckNsb3NpbmdUYWdOYW1lKGMpIHtcbiAgICAgICAgLy8gU2tpcCBldmVyeXRoaW5nIHVudGlsIFwiPlwiXG4gICAgICAgIGlmIChjID09PSBDaGFyQ29kZXMuR3QgfHwgdGhpcy5mYXN0Rm9yd2FyZFRvKENoYXJDb2Rlcy5HdCkpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5UZXh0O1xuICAgICAgICAgICAgdGhpcy5iYXNlU3RhdGUgPSBTdGF0ZS5UZXh0O1xuICAgICAgICAgICAgdGhpcy5zZWN0aW9uU3RhcnQgPSB0aGlzLmluZGV4ICsgMTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZUJlZm9yZUF0dHJpYnV0ZU5hbWUoYykge1xuICAgICAgICBpZiAoYyA9PT0gQ2hhckNvZGVzLkd0KSB7XG4gICAgICAgICAgICB0aGlzLmNicy5vbm9wZW50YWdlbmQodGhpcy5pbmRleCk7XG4gICAgICAgICAgICBpZiAodGhpcy5pc1NwZWNpYWwpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuSW5TcGVjaWFsVGFnO1xuICAgICAgICAgICAgICAgIHRoaXMuc2VxdWVuY2VJbmRleCA9IDA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuVGV4dDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuYmFzZVN0YXRlID0gdGhpcy5zdGF0ZTtcbiAgICAgICAgICAgIHRoaXMuc2VjdGlvblN0YXJ0ID0gdGhpcy5pbmRleCArIDE7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYyA9PT0gQ2hhckNvZGVzLlNsYXNoKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuSW5TZWxmQ2xvc2luZ1RhZztcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICghaXNXaGl0ZXNwYWNlKGMpKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuSW5BdHRyaWJ1dGVOYW1lO1xuICAgICAgICAgICAgdGhpcy5zZWN0aW9uU3RhcnQgPSB0aGlzLmluZGV4O1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlSW5TZWxmQ2xvc2luZ1RhZyhjKSB7XG4gICAgICAgIGlmIChjID09PSBDaGFyQ29kZXMuR3QpIHtcbiAgICAgICAgICAgIHRoaXMuY2JzLm9uc2VsZmNsb3Npbmd0YWcodGhpcy5pbmRleCk7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuVGV4dDtcbiAgICAgICAgICAgIHRoaXMuYmFzZVN0YXRlID0gU3RhdGUuVGV4dDtcbiAgICAgICAgICAgIHRoaXMuc2VjdGlvblN0YXJ0ID0gdGhpcy5pbmRleCArIDE7XG4gICAgICAgICAgICB0aGlzLmlzU3BlY2lhbCA9IGZhbHNlOyAvLyBSZXNldCBzcGVjaWFsIHN0YXRlLCBpbiBjYXNlIG9mIHNlbGYtY2xvc2luZyBzcGVjaWFsIHRhZ3NcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICghaXNXaGl0ZXNwYWNlKGMpKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlQXR0cmlidXRlTmFtZTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVCZWZvcmVBdHRyaWJ1dGVOYW1lKGMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlSW5BdHRyaWJ1dGVOYW1lKGMpIHtcbiAgICAgICAgaWYgKGMgPT09IENoYXJDb2Rlcy5FcSB8fCBpc0VuZE9mVGFnU2VjdGlvbihjKSkge1xuICAgICAgICAgICAgdGhpcy5jYnMub25hdHRyaWJuYW1lKHRoaXMuc2VjdGlvblN0YXJ0LCB0aGlzLmluZGV4KTtcbiAgICAgICAgICAgIHRoaXMuc2VjdGlvblN0YXJ0ID0gLTE7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQWZ0ZXJBdHRyaWJ1dGVOYW1lO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZUFmdGVyQXR0cmlidXRlTmFtZShjKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZUFmdGVyQXR0cmlidXRlTmFtZShjKSB7XG4gICAgICAgIGlmIChjID09PSBDaGFyQ29kZXMuRXEpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5CZWZvcmVBdHRyaWJ1dGVWYWx1ZTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChjID09PSBDaGFyQ29kZXMuU2xhc2ggfHwgYyA9PT0gQ2hhckNvZGVzLkd0KSB7XG4gICAgICAgICAgICB0aGlzLmNicy5vbmF0dHJpYmVuZChRdW90ZVR5cGUuTm9WYWx1ZSwgdGhpcy5pbmRleCk7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlQXR0cmlidXRlTmFtZTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVCZWZvcmVBdHRyaWJ1dGVOYW1lKGMpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKCFpc1doaXRlc3BhY2UoYykpIHtcbiAgICAgICAgICAgIHRoaXMuY2JzLm9uYXR0cmliZW5kKFF1b3RlVHlwZS5Ob1ZhbHVlLCB0aGlzLmluZGV4KTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5JbkF0dHJpYnV0ZU5hbWU7XG4gICAgICAgICAgICB0aGlzLnNlY3Rpb25TdGFydCA9IHRoaXMuaW5kZXg7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVCZWZvcmVBdHRyaWJ1dGVWYWx1ZShjKSB7XG4gICAgICAgIGlmIChjID09PSBDaGFyQ29kZXMuRG91YmxlUXVvdGUpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5JbkF0dHJpYnV0ZVZhbHVlRHE7XG4gICAgICAgICAgICB0aGlzLnNlY3Rpb25TdGFydCA9IHRoaXMuaW5kZXggKyAxO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGMgPT09IENoYXJDb2Rlcy5TaW5nbGVRdW90ZSkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkluQXR0cmlidXRlVmFsdWVTcTtcbiAgICAgICAgICAgIHRoaXMuc2VjdGlvblN0YXJ0ID0gdGhpcy5pbmRleCArIDE7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoIWlzV2hpdGVzcGFjZShjKSkge1xuICAgICAgICAgICAgdGhpcy5zZWN0aW9uU3RhcnQgPSB0aGlzLmluZGV4O1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkluQXR0cmlidXRlVmFsdWVOcTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVJbkF0dHJpYnV0ZVZhbHVlTm9RdW90ZXMoYyk7IC8vIFJlY29uc3VtZSB0b2tlblxuICAgICAgICB9XG4gICAgfVxuICAgIGhhbmRsZUluQXR0cmlidXRlVmFsdWUoYywgcXVvdGUpIHtcbiAgICAgICAgaWYgKGMgPT09IHF1b3RlIHx8XG4gICAgICAgICAgICAoIXRoaXMuZGVjb2RlRW50aXRpZXMgJiYgdGhpcy5mYXN0Rm9yd2FyZFRvKHF1b3RlKSkpIHtcbiAgICAgICAgICAgIHRoaXMuY2JzLm9uYXR0cmliZGF0YSh0aGlzLnNlY3Rpb25TdGFydCwgdGhpcy5pbmRleCk7XG4gICAgICAgICAgICB0aGlzLnNlY3Rpb25TdGFydCA9IC0xO1xuICAgICAgICAgICAgdGhpcy5jYnMub25hdHRyaWJlbmQocXVvdGUgPT09IENoYXJDb2Rlcy5Eb3VibGVRdW90ZVxuICAgICAgICAgICAgICAgID8gUXVvdGVUeXBlLkRvdWJsZVxuICAgICAgICAgICAgICAgIDogUXVvdGVUeXBlLlNpbmdsZSwgdGhpcy5pbmRleCk7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlQXR0cmlidXRlTmFtZTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICh0aGlzLmRlY29kZUVudGl0aWVzICYmIGMgPT09IENoYXJDb2Rlcy5BbXApIHtcbiAgICAgICAgICAgIHRoaXMuYmFzZVN0YXRlID0gdGhpcy5zdGF0ZTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5CZWZvcmVFbnRpdHk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVJbkF0dHJpYnV0ZVZhbHVlRG91YmxlUXVvdGVzKGMpIHtcbiAgICAgICAgdGhpcy5oYW5kbGVJbkF0dHJpYnV0ZVZhbHVlKGMsIENoYXJDb2Rlcy5Eb3VibGVRdW90ZSk7XG4gICAgfVxuICAgIHN0YXRlSW5BdHRyaWJ1dGVWYWx1ZVNpbmdsZVF1b3RlcyhjKSB7XG4gICAgICAgIHRoaXMuaGFuZGxlSW5BdHRyaWJ1dGVWYWx1ZShjLCBDaGFyQ29kZXMuU2luZ2xlUXVvdGUpO1xuICAgIH1cbiAgICBzdGF0ZUluQXR0cmlidXRlVmFsdWVOb1F1b3RlcyhjKSB7XG4gICAgICAgIGlmIChpc1doaXRlc3BhY2UoYykgfHwgYyA9PT0gQ2hhckNvZGVzLkd0KSB7XG4gICAgICAgICAgICB0aGlzLmNicy5vbmF0dHJpYmRhdGEodGhpcy5zZWN0aW9uU3RhcnQsIHRoaXMuaW5kZXgpO1xuICAgICAgICAgICAgdGhpcy5zZWN0aW9uU3RhcnQgPSAtMTtcbiAgICAgICAgICAgIHRoaXMuY2JzLm9uYXR0cmliZW5kKFF1b3RlVHlwZS5VbnF1b3RlZCwgdGhpcy5pbmRleCk7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlQXR0cmlidXRlTmFtZTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVCZWZvcmVBdHRyaWJ1dGVOYW1lKGMpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHRoaXMuZGVjb2RlRW50aXRpZXMgJiYgYyA9PT0gQ2hhckNvZGVzLkFtcCkge1xuICAgICAgICAgICAgdGhpcy5iYXNlU3RhdGUgPSB0aGlzLnN0YXRlO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkJlZm9yZUVudGl0eTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZUJlZm9yZURlY2xhcmF0aW9uKGMpIHtcbiAgICAgICAgaWYgKGMgPT09IENoYXJDb2Rlcy5PcGVuaW5nU3F1YXJlQnJhY2tldCkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkNEQVRBU2VxdWVuY2U7XG4gICAgICAgICAgICB0aGlzLnNlcXVlbmNlSW5kZXggPSAwO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9XG4gICAgICAgICAgICAgICAgYyA9PT0gQ2hhckNvZGVzLkRhc2hcbiAgICAgICAgICAgICAgICAgICAgPyBTdGF0ZS5CZWZvcmVDb21tZW50XG4gICAgICAgICAgICAgICAgICAgIDogU3RhdGUuSW5EZWNsYXJhdGlvbjtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZUluRGVjbGFyYXRpb24oYykge1xuICAgICAgICBpZiAoYyA9PT0gQ2hhckNvZGVzLkd0IHx8IHRoaXMuZmFzdEZvcndhcmRUbyhDaGFyQ29kZXMuR3QpKSB7XG4gICAgICAgICAgICB0aGlzLmNicy5vbmRlY2xhcmF0aW9uKHRoaXMuc2VjdGlvblN0YXJ0LCB0aGlzLmluZGV4KTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5UZXh0O1xuICAgICAgICAgICAgdGhpcy5zZWN0aW9uU3RhcnQgPSB0aGlzLmluZGV4ICsgMTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZUluUHJvY2Vzc2luZ0luc3RydWN0aW9uKGMpIHtcbiAgICAgICAgaWYgKGMgPT09IENoYXJDb2Rlcy5HdCB8fCB0aGlzLmZhc3RGb3J3YXJkVG8oQ2hhckNvZGVzLkd0KSkge1xuICAgICAgICAgICAgdGhpcy5jYnMub25wcm9jZXNzaW5naW5zdHJ1Y3Rpb24odGhpcy5zZWN0aW9uU3RhcnQsIHRoaXMuaW5kZXgpO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLlRleHQ7XG4gICAgICAgICAgICB0aGlzLnNlY3Rpb25TdGFydCA9IHRoaXMuaW5kZXggKyAxO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlQmVmb3JlQ29tbWVudChjKSB7XG4gICAgICAgIGlmIChjID09PSBDaGFyQ29kZXMuRGFzaCkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkluQ29tbWVudExpa2U7XG4gICAgICAgICAgICB0aGlzLmN1cnJlbnRTZXF1ZW5jZSA9IFNlcXVlbmNlcy5Db21tZW50RW5kO1xuICAgICAgICAgICAgLy8gQWxsb3cgc2hvcnQgY29tbWVudHMgKGVnLiA8IS0tPilcbiAgICAgICAgICAgIHRoaXMuc2VxdWVuY2VJbmRleCA9IDI7XG4gICAgICAgICAgICB0aGlzLnNlY3Rpb25TdGFydCA9IHRoaXMuaW5kZXggKyAxO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkluRGVjbGFyYXRpb247XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVJblNwZWNpYWxDb21tZW50KGMpIHtcbiAgICAgICAgaWYgKGMgPT09IENoYXJDb2Rlcy5HdCB8fCB0aGlzLmZhc3RGb3J3YXJkVG8oQ2hhckNvZGVzLkd0KSkge1xuICAgICAgICAgICAgdGhpcy5jYnMub25jb21tZW50KHRoaXMuc2VjdGlvblN0YXJ0LCB0aGlzLmluZGV4LCAwKTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5UZXh0O1xuICAgICAgICAgICAgdGhpcy5zZWN0aW9uU3RhcnQgPSB0aGlzLmluZGV4ICsgMTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZUJlZm9yZVNwZWNpYWxTKGMpIHtcbiAgICAgICAgY29uc3QgbG93ZXIgPSBjIHwgMHgyMDtcbiAgICAgICAgaWYgKGxvd2VyID09PSBTZXF1ZW5jZXMuU2NyaXB0RW5kWzNdKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXJ0U3BlY2lhbChTZXF1ZW5jZXMuU2NyaXB0RW5kLCA0KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChsb3dlciA9PT0gU2VxdWVuY2VzLlN0eWxlRW5kWzNdKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXJ0U3BlY2lhbChTZXF1ZW5jZXMuU3R5bGVFbmQsIDQpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkluVGFnTmFtZTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVJblRhZ05hbWUoYyk7IC8vIENvbnN1bWUgdGhlIHRva2VuIGFnYWluXG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVCZWZvcmVFbnRpdHkoYykge1xuICAgICAgICAvLyBTdGFydCBleGNlc3Mgd2l0aCAxIHRvIGluY2x1ZGUgdGhlICcmJ1xuICAgICAgICB0aGlzLmVudGl0eUV4Y2VzcyA9IDE7XG4gICAgICAgIHRoaXMuZW50aXR5UmVzdWx0ID0gMDtcbiAgICAgICAgaWYgKGMgPT09IENoYXJDb2Rlcy5OdW1iZXIpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5CZWZvcmVOdW1lcmljRW50aXR5O1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGMgPT09IENoYXJDb2Rlcy5BbXApIHtcbiAgICAgICAgICAgIC8vIFdlIGhhdmUgdHdvIGAmYCBjaGFyYWN0ZXJzIGluIGEgcm93LiBTdGF5IGluIHRoZSBjdXJyZW50IHN0YXRlLlxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy50cmllSW5kZXggPSAwO1xuICAgICAgICAgICAgdGhpcy50cmllQ3VycmVudCA9IHRoaXMuZW50aXR5VHJpZVswXTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5Jbk5hbWVkRW50aXR5O1xuICAgICAgICAgICAgdGhpcy5zdGF0ZUluTmFtZWRFbnRpdHkoYyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVJbk5hbWVkRW50aXR5KGMpIHtcbiAgICAgICAgdGhpcy5lbnRpdHlFeGNlc3MgKz0gMTtcbiAgICAgICAgdGhpcy50cmllSW5kZXggPSBkZXRlcm1pbmVCcmFuY2godGhpcy5lbnRpdHlUcmllLCB0aGlzLnRyaWVDdXJyZW50LCB0aGlzLnRyaWVJbmRleCArIDEsIGMpO1xuICAgICAgICBpZiAodGhpcy50cmllSW5kZXggPCAwKSB7XG4gICAgICAgICAgICB0aGlzLmVtaXROYW1lZEVudGl0eSgpO1xuICAgICAgICAgICAgdGhpcy5pbmRleC0tO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMudHJpZUN1cnJlbnQgPSB0aGlzLmVudGl0eVRyaWVbdGhpcy50cmllSW5kZXhdO1xuICAgICAgICBjb25zdCBtYXNrZWQgPSB0aGlzLnRyaWVDdXJyZW50ICYgQmluVHJpZUZsYWdzLlZBTFVFX0xFTkdUSDtcbiAgICAgICAgLy8gSWYgdGhlIGJyYW5jaCBpcyBhIHZhbHVlLCBzdG9yZSBpdCBhbmQgY29udGludWVcbiAgICAgICAgaWYgKG1hc2tlZCkge1xuICAgICAgICAgICAgLy8gVGhlIG1hc2sgaXMgdGhlIG51bWJlciBvZiBieXRlcyBvZiB0aGUgdmFsdWUsIGluY2x1ZGluZyB0aGUgY3VycmVudCBieXRlLlxuICAgICAgICAgICAgY29uc3QgdmFsdWVMZW5ndGggPSAobWFza2VkID4+IDE0KSAtIDE7XG4gICAgICAgICAgICAvLyBJZiB3ZSBoYXZlIGEgbGVnYWN5IGVudGl0eSB3aGlsZSBwYXJzaW5nIHN0cmljdGx5LCBqdXN0IHNraXAgdGhlIG51bWJlciBvZiBieXRlc1xuICAgICAgICAgICAgaWYgKCF0aGlzLmFsbG93TGVnYWN5RW50aXR5KCkgJiYgYyAhPT0gQ2hhckNvZGVzLlNlbWkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnRyaWVJbmRleCArPSB2YWx1ZUxlbmd0aDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIC8vIEFkZCAxIGFzIHdlIGhhdmUgYWxyZWFkeSBpbmNyZW1lbnRlZCB0aGUgZXhjZXNzXG4gICAgICAgICAgICAgICAgY29uc3QgZW50aXR5U3RhcnQgPSB0aGlzLmluZGV4IC0gdGhpcy5lbnRpdHlFeGNlc3MgKyAxO1xuICAgICAgICAgICAgICAgIGlmIChlbnRpdHlTdGFydCA+IHRoaXMuc2VjdGlvblN0YXJ0KSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZW1pdFBhcnRpYWwodGhpcy5zZWN0aW9uU3RhcnQsIGVudGl0eVN0YXJ0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gSWYgdGhpcyBpcyBhIHN1cnJvZ2F0ZSBwYWlyLCBjb25zdW1lIHRoZSBuZXh0IHR3byBieXRlc1xuICAgICAgICAgICAgICAgIHRoaXMuZW50aXR5UmVzdWx0ID0gdGhpcy50cmllSW5kZXg7XG4gICAgICAgICAgICAgICAgdGhpcy50cmllSW5kZXggKz0gdmFsdWVMZW5ndGg7XG4gICAgICAgICAgICAgICAgdGhpcy5lbnRpdHlFeGNlc3MgPSAwO1xuICAgICAgICAgICAgICAgIHRoaXMuc2VjdGlvblN0YXJ0ID0gdGhpcy5pbmRleCArIDE7XG4gICAgICAgICAgICAgICAgaWYgKHZhbHVlTGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZW1pdE5hbWVkRW50aXR5KCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGVtaXROYW1lZEVudGl0eSgpIHtcbiAgICAgICAgdGhpcy5zdGF0ZSA9IHRoaXMuYmFzZVN0YXRlO1xuICAgICAgICBpZiAodGhpcy5lbnRpdHlSZXN1bHQgPT09IDApIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB2YWx1ZUxlbmd0aCA9ICh0aGlzLmVudGl0eVRyaWVbdGhpcy5lbnRpdHlSZXN1bHRdICYgQmluVHJpZUZsYWdzLlZBTFVFX0xFTkdUSCkgPj5cbiAgICAgICAgICAgIDE0O1xuICAgICAgICBzd2l0Y2ggKHZhbHVlTGVuZ3RoKSB7XG4gICAgICAgICAgICBjYXNlIDE6IHtcbiAgICAgICAgICAgICAgICB0aGlzLmVtaXRDb2RlUG9pbnQodGhpcy5lbnRpdHlUcmllW3RoaXMuZW50aXR5UmVzdWx0XSAmXG4gICAgICAgICAgICAgICAgICAgIH5CaW5UcmllRmxhZ3MuVkFMVUVfTEVOR1RIKTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhc2UgMjoge1xuICAgICAgICAgICAgICAgIHRoaXMuZW1pdENvZGVQb2ludCh0aGlzLmVudGl0eVRyaWVbdGhpcy5lbnRpdHlSZXN1bHQgKyAxXSk7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXNlIDM6IHtcbiAgICAgICAgICAgICAgICB0aGlzLmVtaXRDb2RlUG9pbnQodGhpcy5lbnRpdHlUcmllW3RoaXMuZW50aXR5UmVzdWx0ICsgMV0pO1xuICAgICAgICAgICAgICAgIHRoaXMuZW1pdENvZGVQb2ludCh0aGlzLmVudGl0eVRyaWVbdGhpcy5lbnRpdHlSZXN1bHQgKyAyXSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVCZWZvcmVOdW1lcmljRW50aXR5KGMpIHtcbiAgICAgICAgaWYgKChjIHwgMHgyMCkgPT09IENoYXJDb2Rlcy5Mb3dlclgpIHtcbiAgICAgICAgICAgIHRoaXMuZW50aXR5RXhjZXNzKys7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuSW5IZXhFbnRpdHk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuSW5OdW1lcmljRW50aXR5O1xuICAgICAgICAgICAgdGhpcy5zdGF0ZUluTnVtZXJpY0VudGl0eShjKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbWl0TnVtZXJpY0VudGl0eShzdHJpY3QpIHtcbiAgICAgICAgY29uc3QgZW50aXR5U3RhcnQgPSB0aGlzLmluZGV4IC0gdGhpcy5lbnRpdHlFeGNlc3MgLSAxO1xuICAgICAgICBjb25zdCBudW1iZXJTdGFydCA9IGVudGl0eVN0YXJ0ICsgMiArIE51bWJlcih0aGlzLnN0YXRlID09PSBTdGF0ZS5JbkhleEVudGl0eSk7XG4gICAgICAgIGlmIChudW1iZXJTdGFydCAhPT0gdGhpcy5pbmRleCkge1xuICAgICAgICAgICAgLy8gRW1pdCBsZWFkaW5nIGRhdGEgaWYgYW55XG4gICAgICAgICAgICBpZiAoZW50aXR5U3RhcnQgPiB0aGlzLnNlY3Rpb25TdGFydCkge1xuICAgICAgICAgICAgICAgIHRoaXMuZW1pdFBhcnRpYWwodGhpcy5zZWN0aW9uU3RhcnQsIGVudGl0eVN0YXJ0KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuc2VjdGlvblN0YXJ0ID0gdGhpcy5pbmRleCArIE51bWJlcihzdHJpY3QpO1xuICAgICAgICAgICAgdGhpcy5lbWl0Q29kZVBvaW50KHJlcGxhY2VDb2RlUG9pbnQodGhpcy5lbnRpdHlSZXN1bHQpKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnN0YXRlID0gdGhpcy5iYXNlU3RhdGU7XG4gICAgfVxuICAgIHN0YXRlSW5OdW1lcmljRW50aXR5KGMpIHtcbiAgICAgICAgaWYgKGMgPT09IENoYXJDb2Rlcy5TZW1pKSB7XG4gICAgICAgICAgICB0aGlzLmVtaXROdW1lcmljRW50aXR5KHRydWUpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGlzTnVtYmVyKGMpKSB7XG4gICAgICAgICAgICB0aGlzLmVudGl0eVJlc3VsdCA9IHRoaXMuZW50aXR5UmVzdWx0ICogMTAgKyAoYyAtIENoYXJDb2Rlcy5aZXJvKTtcbiAgICAgICAgICAgIHRoaXMuZW50aXR5RXhjZXNzKys7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBpZiAodGhpcy5hbGxvd0xlZ2FjeUVudGl0eSgpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0TnVtZXJpY0VudGl0eShmYWxzZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gdGhpcy5iYXNlU3RhdGU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmluZGV4LS07XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVJbkhleEVudGl0eShjKSB7XG4gICAgICAgIGlmIChjID09PSBDaGFyQ29kZXMuU2VtaSkge1xuICAgICAgICAgICAgdGhpcy5lbWl0TnVtZXJpY0VudGl0eSh0cnVlKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChpc051bWJlcihjKSkge1xuICAgICAgICAgICAgdGhpcy5lbnRpdHlSZXN1bHQgPSB0aGlzLmVudGl0eVJlc3VsdCAqIDE2ICsgKGMgLSBDaGFyQ29kZXMuWmVybyk7XG4gICAgICAgICAgICB0aGlzLmVudGl0eUV4Y2VzcysrO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGlzSGV4RGlnaXQoYykpIHtcbiAgICAgICAgICAgIHRoaXMuZW50aXR5UmVzdWx0ID1cbiAgICAgICAgICAgICAgICB0aGlzLmVudGl0eVJlc3VsdCAqIDE2ICsgKChjIHwgMHgyMCkgLSBDaGFyQ29kZXMuTG93ZXJBICsgMTApO1xuICAgICAgICAgICAgdGhpcy5lbnRpdHlFeGNlc3MrKztcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGlmICh0aGlzLmFsbG93TGVnYWN5RW50aXR5KCkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmVtaXROdW1lcmljRW50aXR5KGZhbHNlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSB0aGlzLmJhc2VTdGF0ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuaW5kZXgtLTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBhbGxvd0xlZ2FjeUVudGl0eSgpIHtcbiAgICAgICAgcmV0dXJuICghdGhpcy54bWxNb2RlICYmXG4gICAgICAgICAgICAodGhpcy5iYXNlU3RhdGUgPT09IFN0YXRlLlRleHQgfHxcbiAgICAgICAgICAgICAgICB0aGlzLmJhc2VTdGF0ZSA9PT0gU3RhdGUuSW5TcGVjaWFsVGFnKSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlbW92ZSBkYXRhIHRoYXQgaGFzIGFscmVhZHkgYmVlbiBjb25zdW1lZCBmcm9tIHRoZSBidWZmZXIuXG4gICAgICovXG4gICAgY2xlYW51cCgpIHtcbiAgICAgICAgLy8gSWYgd2UgYXJlIGluc2lkZSBvZiB0ZXh0IG9yIGF0dHJpYnV0ZXMsIGVtaXQgd2hhdCB3ZSBhbHJlYWR5IGhhdmUuXG4gICAgICAgIGlmICh0aGlzLnJ1bm5pbmcgJiYgdGhpcy5zZWN0aW9uU3RhcnQgIT09IHRoaXMuaW5kZXgpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLnN0YXRlID09PSBTdGF0ZS5UZXh0IHx8XG4gICAgICAgICAgICAgICAgKHRoaXMuc3RhdGUgPT09IFN0YXRlLkluU3BlY2lhbFRhZyAmJiB0aGlzLnNlcXVlbmNlSW5kZXggPT09IDApKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5jYnMub250ZXh0KHRoaXMuc2VjdGlvblN0YXJ0LCB0aGlzLmluZGV4KTtcbiAgICAgICAgICAgICAgICB0aGlzLnNlY3Rpb25TdGFydCA9IHRoaXMuaW5kZXg7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICh0aGlzLnN0YXRlID09PSBTdGF0ZS5JbkF0dHJpYnV0ZVZhbHVlRHEgfHxcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID09PSBTdGF0ZS5JbkF0dHJpYnV0ZVZhbHVlU3EgfHxcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID09PSBTdGF0ZS5JbkF0dHJpYnV0ZVZhbHVlTnEpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmNicy5vbmF0dHJpYmRhdGEodGhpcy5zZWN0aW9uU3RhcnQsIHRoaXMuaW5kZXgpO1xuICAgICAgICAgICAgICAgIHRoaXMuc2VjdGlvblN0YXJ0ID0gdGhpcy5pbmRleDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBzaG91bGRDb250aW51ZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuaW5kZXggPCB0aGlzLmJ1ZmZlci5sZW5ndGggKyB0aGlzLm9mZnNldCAmJiB0aGlzLnJ1bm5pbmc7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEl0ZXJhdGVzIHRocm91Z2ggdGhlIGJ1ZmZlciwgY2FsbGluZyB0aGUgZnVuY3Rpb24gY29ycmVzcG9uZGluZyB0byB0aGUgY3VycmVudCBzdGF0ZS5cbiAgICAgKlxuICAgICAqIFN0YXRlcyB0aGF0IGFyZSBtb3JlIGxpa2VseSB0byBiZSBoaXQgYXJlIGhpZ2hlciB1cCwgYXMgYSBwZXJmb3JtYW5jZSBpbXByb3ZlbWVudC5cbiAgICAgKi9cbiAgICBwYXJzZSgpIHtcbiAgICAgICAgd2hpbGUgKHRoaXMuc2hvdWxkQ29udGludWUoKSkge1xuICAgICAgICAgICAgY29uc3QgYyA9IHRoaXMuYnVmZmVyLmNoYXJDb2RlQXQodGhpcy5pbmRleCAtIHRoaXMub2Zmc2V0KTtcbiAgICAgICAgICAgIHN3aXRjaCAodGhpcy5zdGF0ZSkge1xuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuVGV4dDoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlVGV4dChjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuU3BlY2lhbFN0YXJ0U2VxdWVuY2U6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZVNwZWNpYWxTdGFydFNlcXVlbmNlKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5JblNwZWNpYWxUYWc6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUluU3BlY2lhbFRhZyhjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuQ0RBVEFTZXF1ZW5jZToge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlQ0RBVEFTZXF1ZW5jZShjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuSW5BdHRyaWJ1dGVWYWx1ZURxOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVJbkF0dHJpYnV0ZVZhbHVlRG91YmxlUXVvdGVzKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5JbkF0dHJpYnV0ZU5hbWU6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUluQXR0cmlidXRlTmFtZShjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuSW5Db21tZW50TGlrZToge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlSW5Db21tZW50TGlrZShjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuSW5TcGVjaWFsQ29tbWVudDoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlSW5TcGVjaWFsQ29tbWVudChjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuQmVmb3JlQXR0cmlidXRlTmFtZToge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlQmVmb3JlQXR0cmlidXRlTmFtZShjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuSW5UYWdOYW1lOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVJblRhZ05hbWUoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkluQ2xvc2luZ1RhZ05hbWU6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUluQ2xvc2luZ1RhZ05hbWUoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkJlZm9yZVRhZ05hbWU6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUJlZm9yZVRhZ05hbWUoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkFmdGVyQXR0cmlidXRlTmFtZToge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlQWZ0ZXJBdHRyaWJ1dGVOYW1lKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5JbkF0dHJpYnV0ZVZhbHVlU3E6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUluQXR0cmlidXRlVmFsdWVTaW5nbGVRdW90ZXMoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkJlZm9yZUF0dHJpYnV0ZVZhbHVlOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVCZWZvcmVBdHRyaWJ1dGVWYWx1ZShjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuQmVmb3JlQ2xvc2luZ1RhZ05hbWU6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUJlZm9yZUNsb3NpbmdUYWdOYW1lKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5BZnRlckNsb3NpbmdUYWdOYW1lOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVBZnRlckNsb3NpbmdUYWdOYW1lKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5CZWZvcmVTcGVjaWFsUzoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlQmVmb3JlU3BlY2lhbFMoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkluQXR0cmlidXRlVmFsdWVOcToge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlSW5BdHRyaWJ1dGVWYWx1ZU5vUXVvdGVzKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5JblNlbGZDbG9zaW5nVGFnOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVJblNlbGZDbG9zaW5nVGFnKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5JbkRlY2xhcmF0aW9uOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVJbkRlY2xhcmF0aW9uKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5CZWZvcmVEZWNsYXJhdGlvbjoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlQmVmb3JlRGVjbGFyYXRpb24oYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkJlZm9yZUNvbW1lbnQ6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUJlZm9yZUNvbW1lbnQoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkluUHJvY2Vzc2luZ0luc3RydWN0aW9uOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVJblByb2Nlc3NpbmdJbnN0cnVjdGlvbihjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuSW5OYW1lZEVudGl0eToge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlSW5OYW1lZEVudGl0eShjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuQmVmb3JlRW50aXR5OiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVCZWZvcmVFbnRpdHkoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkluSGV4RW50aXR5OiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVJbkhleEVudGl0eShjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuSW5OdW1lcmljRW50aXR5OiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVJbk51bWVyaWNFbnRpdHkoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgICAgICAgICAgIC8vIGB0aGlzLl9zdGF0ZSA9PT0gU3RhdGUuQmVmb3JlTnVtZXJpY0VudGl0eWBcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUJlZm9yZU51bWVyaWNFbnRpdHkoYyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5pbmRleCsrO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuY2xlYW51cCgpO1xuICAgIH1cbiAgICBmaW5pc2goKSB7XG4gICAgICAgIGlmICh0aGlzLnN0YXRlID09PSBTdGF0ZS5Jbk5hbWVkRW50aXR5KSB7XG4gICAgICAgICAgICB0aGlzLmVtaXROYW1lZEVudGl0eSgpO1xuICAgICAgICB9XG4gICAgICAgIC8vIElmIHRoZXJlIGlzIHJlbWFpbmluZyBkYXRhLCBlbWl0IGl0IGluIGEgcmVhc29uYWJsZSB3YXlcbiAgICAgICAgaWYgKHRoaXMuc2VjdGlvblN0YXJ0IDwgdGhpcy5pbmRleCkge1xuICAgICAgICAgICAgdGhpcy5oYW5kbGVUcmFpbGluZ0RhdGEoKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmNicy5vbmVuZCgpO1xuICAgIH1cbiAgICAvKiogSGFuZGxlIGFueSB0cmFpbGluZyBkYXRhLiAqL1xuICAgIGhhbmRsZVRyYWlsaW5nRGF0YSgpIHtcbiAgICAgICAgY29uc3QgZW5kSW5kZXggPSB0aGlzLmJ1ZmZlci5sZW5ndGggKyB0aGlzLm9mZnNldDtcbiAgICAgICAgaWYgKHRoaXMuc3RhdGUgPT09IFN0YXRlLkluQ29tbWVudExpa2UpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLmN1cnJlbnRTZXF1ZW5jZSA9PT0gU2VxdWVuY2VzLkNkYXRhRW5kKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5jYnMub25jZGF0YSh0aGlzLnNlY3Rpb25TdGFydCwgZW5kSW5kZXgsIDApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgdGhpcy5jYnMub25jb21tZW50KHRoaXMuc2VjdGlvblN0YXJ0LCBlbmRJbmRleCwgMCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodGhpcy5zdGF0ZSA9PT0gU3RhdGUuSW5OdW1lcmljRW50aXR5ICYmXG4gICAgICAgICAgICB0aGlzLmFsbG93TGVnYWN5RW50aXR5KCkpIHtcbiAgICAgICAgICAgIHRoaXMuZW1pdE51bWVyaWNFbnRpdHkoZmFsc2UpO1xuICAgICAgICAgICAgLy8gQWxsIHRyYWlsaW5nIGRhdGEgd2lsbCBoYXZlIGJlZW4gY29uc3VtZWRcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICh0aGlzLnN0YXRlID09PSBTdGF0ZS5JbkhleEVudGl0eSAmJlxuICAgICAgICAgICAgdGhpcy5hbGxvd0xlZ2FjeUVudGl0eSgpKSB7XG4gICAgICAgICAgICB0aGlzLmVtaXROdW1lcmljRW50aXR5KGZhbHNlKTtcbiAgICAgICAgICAgIC8vIEFsbCB0cmFpbGluZyBkYXRhIHdpbGwgaGF2ZSBiZWVuIGNvbnN1bWVkXG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodGhpcy5zdGF0ZSA9PT0gU3RhdGUuSW5UYWdOYW1lIHx8XG4gICAgICAgICAgICB0aGlzLnN0YXRlID09PSBTdGF0ZS5CZWZvcmVBdHRyaWJ1dGVOYW1lIHx8XG4gICAgICAgICAgICB0aGlzLnN0YXRlID09PSBTdGF0ZS5CZWZvcmVBdHRyaWJ1dGVWYWx1ZSB8fFxuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9PT0gU3RhdGUuQWZ0ZXJBdHRyaWJ1dGVOYW1lIHx8XG4gICAgICAgICAgICB0aGlzLnN0YXRlID09PSBTdGF0ZS5JbkF0dHJpYnV0ZU5hbWUgfHxcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPT09IFN0YXRlLkluQXR0cmlidXRlVmFsdWVTcSB8fFxuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9PT0gU3RhdGUuSW5BdHRyaWJ1dGVWYWx1ZURxIHx8XG4gICAgICAgICAgICB0aGlzLnN0YXRlID09PSBTdGF0ZS5JbkF0dHJpYnV0ZVZhbHVlTnEgfHxcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPT09IFN0YXRlLkluQ2xvc2luZ1RhZ05hbWUpIHtcbiAgICAgICAgICAgIC8qXG4gICAgICAgICAgICAgKiBJZiB3ZSBhcmUgY3VycmVudGx5IGluIGFuIG9wZW5pbmcgb3IgY2xvc2luZyB0YWcsIHVzIG5vdCBjYWxsaW5nIHRoZVxuICAgICAgICAgICAgICogcmVzcGVjdGl2ZSBjYWxsYmFjayBzaWduYWxzIHRoYXQgdGhlIHRhZyBzaG91bGQgYmUgaWdub3JlZC5cbiAgICAgICAgICAgICAqL1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5jYnMub250ZXh0KHRoaXMuc2VjdGlvblN0YXJ0LCBlbmRJbmRleCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZW1pdFBhcnRpYWwoc3RhcnQsIGVuZEluZGV4KSB7XG4gICAgICAgIGlmICh0aGlzLmJhc2VTdGF0ZSAhPT0gU3RhdGUuVGV4dCAmJlxuICAgICAgICAgICAgdGhpcy5iYXNlU3RhdGUgIT09IFN0YXRlLkluU3BlY2lhbFRhZykge1xuICAgICAgICAgICAgdGhpcy5jYnMub25hdHRyaWJkYXRhKHN0YXJ0LCBlbmRJbmRleCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmNicy5vbnRleHQoc3RhcnQsIGVuZEluZGV4KTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbWl0Q29kZVBvaW50KGNwKSB7XG4gICAgICAgIGlmICh0aGlzLmJhc2VTdGF0ZSAhPT0gU3RhdGUuVGV4dCAmJlxuICAgICAgICAgICAgdGhpcy5iYXNlU3RhdGUgIT09IFN0YXRlLkluU3BlY2lhbFRhZykge1xuICAgICAgICAgICAgdGhpcy5jYnMub25hdHRyaWJlbnRpdHkoY3ApO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5jYnMub250ZXh0ZW50aXR5KGNwKTtcbiAgICAgICAgfVxuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVRva2VuaXplci5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/htmlparser2/lib/esm/Tokenizer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/htmlparser2/lib/esm/index.js":
/*!***************************************************!*\
  !*** ./node_modules/htmlparser2/lib/esm/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultHandler: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler),\n/* harmony export */   DomHandler: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler),\n/* harmony export */   DomUtils: () => (/* reexport module object */ domutils__WEBPACK_IMPORTED_MODULE_4__),\n/* harmony export */   ElementType: () => (/* reexport module object */ domelementtype__WEBPACK_IMPORTED_MODULE_3__),\n/* harmony export */   Parser: () => (/* reexport safe */ _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser),\n/* harmony export */   Tokenizer: () => (/* reexport safe */ _Tokenizer_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   createDomStream: () => (/* binding */ createDomStream),\n/* harmony export */   getFeed: () => (/* reexport safe */ domutils__WEBPACK_IMPORTED_MODULE_4__.getFeed),\n/* harmony export */   parseDOM: () => (/* binding */ parseDOM),\n/* harmony export */   parseDocument: () => (/* binding */ parseDocument),\n/* harmony export */   parseFeed: () => (/* binding */ parseFeed)\n/* harmony export */ });\n/* harmony import */ var _Parser_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Parser.js */ \"(ssr)/./node_modules/htmlparser2/lib/esm/Parser.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var _Tokenizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Tokenizer.js */ \"(ssr)/./node_modules/htmlparser2/lib/esm/Tokenizer.js\");\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! domelementtype */ \"(ssr)/./node_modules/domelementtype/lib/esm/index.js\");\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! domutils */ \"(ssr)/./node_modules/domutils/lib/esm/index.js\");\n\n\n\n\n// Helper methods\n/**\n * Parses the data, returns the resulting document.\n *\n * @param data The data that should be parsed.\n * @param options Optional options for the parser and DOM builder.\n */\nfunction parseDocument(data, options) {\n    const handler = new domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler(undefined, options);\n    new _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser(handler, options).end(data);\n    return handler.root;\n}\n/**\n * Parses data, returns an array of the root nodes.\n *\n * Note that the root nodes still have a `Document` node as their parent.\n * Use `parseDocument` to get the `Document` node instead.\n *\n * @param data The data that should be parsed.\n * @param options Optional options for the parser and DOM builder.\n * @deprecated Use `parseDocument` instead.\n */\nfunction parseDOM(data, options) {\n    return parseDocument(data, options).children;\n}\n/**\n * Creates a parser instance, with an attached DOM handler.\n *\n * @param callback A callback that will be called once parsing has been completed.\n * @param options Optional options for the parser and DOM builder.\n * @param elementCallback An optional callback that will be called every time a tag has been completed inside of the DOM.\n */\nfunction createDomStream(callback, options, elementCallback) {\n    const handler = new domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler(callback, options, elementCallback);\n    return new _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser(handler, options);\n}\n\n/*\n * All of the following exports exist for backwards-compatibility.\n * They should probably be removed eventually.\n */\n\n\n\nconst parseFeedDefaultOptions = { xmlMode: true };\n/**\n * Parse a feed.\n *\n * @param feed The feed that should be parsed, as a string.\n * @param options Optionally, options for parsing. When using this, you should set `xmlMode` to `true`.\n */\nfunction parseFeed(feed, options = parseFeedDefaultOptions) {\n    return (0,domutils__WEBPACK_IMPORTED_MODULE_4__.getFeed)(parseDOM(feed, options));\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/htmlparser2/lib/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/htmlparser2/lib/esm/Parser.js":
/*!****************************************************!*\
  !*** ./node_modules/htmlparser2/lib/esm/Parser.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/* harmony import */ var _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Tokenizer.js */ \"(rsc)/./node_modules/htmlparser2/lib/esm/Tokenizer.js\");\n/* harmony import */ var entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! entities/lib/decode.js */ \"(rsc)/./node_modules/entities/lib/esm/decode.js\");\n\n\nconst formTags = new Set([\n    \"input\",\n    \"option\",\n    \"optgroup\",\n    \"select\",\n    \"button\",\n    \"datalist\",\n    \"textarea\",\n]);\nconst pTag = new Set([\"p\"]);\nconst tableSectionTags = new Set([\"thead\", \"tbody\"]);\nconst ddtTags = new Set([\"dd\", \"dt\"]);\nconst rtpTags = new Set([\"rt\", \"rp\"]);\nconst openImpliesClose = new Map([\n    [\"tr\", new Set([\"tr\", \"th\", \"td\"])],\n    [\"th\", new Set([\"th\"])],\n    [\"td\", new Set([\"thead\", \"th\", \"td\"])],\n    [\"body\", new Set([\"head\", \"link\", \"script\"])],\n    [\"li\", new Set([\"li\"])],\n    [\"p\", pTag],\n    [\"h1\", pTag],\n    [\"h2\", pTag],\n    [\"h3\", pTag],\n    [\"h4\", pTag],\n    [\"h5\", pTag],\n    [\"h6\", pTag],\n    [\"select\", formTags],\n    [\"input\", formTags],\n    [\"output\", formTags],\n    [\"button\", formTags],\n    [\"datalist\", formTags],\n    [\"textarea\", formTags],\n    [\"option\", new Set([\"option\"])],\n    [\"optgroup\", new Set([\"optgroup\", \"option\"])],\n    [\"dd\", ddtTags],\n    [\"dt\", ddtTags],\n    [\"address\", pTag],\n    [\"article\", pTag],\n    [\"aside\", pTag],\n    [\"blockquote\", pTag],\n    [\"details\", pTag],\n    [\"div\", pTag],\n    [\"dl\", pTag],\n    [\"fieldset\", pTag],\n    [\"figcaption\", pTag],\n    [\"figure\", pTag],\n    [\"footer\", pTag],\n    [\"form\", pTag],\n    [\"header\", pTag],\n    [\"hr\", pTag],\n    [\"main\", pTag],\n    [\"nav\", pTag],\n    [\"ol\", pTag],\n    [\"pre\", pTag],\n    [\"section\", pTag],\n    [\"table\", pTag],\n    [\"ul\", pTag],\n    [\"rt\", rtpTags],\n    [\"rp\", rtpTags],\n    [\"tbody\", tableSectionTags],\n    [\"tfoot\", tableSectionTags],\n]);\nconst voidElements = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\nconst foreignContextElements = new Set([\"math\", \"svg\"]);\nconst htmlIntegrationElements = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignobject\",\n    \"desc\",\n    \"title\",\n]);\nconst reNameEnd = /\\s|\\//;\nclass Parser {\n    constructor(cbs, options = {}) {\n        var _a, _b, _c, _d, _e;\n        this.options = options;\n        /** The start index of the last event. */\n        this.startIndex = 0;\n        /** The end index of the last event. */\n        this.endIndex = 0;\n        /**\n         * Store the start index of the current open tag,\n         * so we can update the start index for attributes.\n         */\n        this.openTagStart = 0;\n        this.tagname = \"\";\n        this.attribname = \"\";\n        this.attribvalue = \"\";\n        this.attribs = null;\n        this.stack = [];\n        this.foreignContext = [];\n        this.buffers = [];\n        this.bufferOffset = 0;\n        /** The index of the last written buffer. Used when resuming after a `pause()`. */\n        this.writeIndex = 0;\n        /** Indicates whether the parser has finished running / `.end` has been called. */\n        this.ended = false;\n        this.cbs = cbs !== null && cbs !== void 0 ? cbs : {};\n        this.lowerCaseTagNames = (_a = options.lowerCaseTags) !== null && _a !== void 0 ? _a : !options.xmlMode;\n        this.lowerCaseAttributeNames =\n            (_b = options.lowerCaseAttributeNames) !== null && _b !== void 0 ? _b : !options.xmlMode;\n        this.tokenizer = new ((_c = options.Tokenizer) !== null && _c !== void 0 ? _c : _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.options, this);\n        (_e = (_d = this.cbs).onparserinit) === null || _e === void 0 ? void 0 : _e.call(_d, this);\n    }\n    // Tokenizer event handlers\n    /** @internal */\n    ontext(start, endIndex) {\n        var _a, _b;\n        const data = this.getSlice(start, endIndex);\n        this.endIndex = endIndex - 1;\n        (_b = (_a = this.cbs).ontext) === null || _b === void 0 ? void 0 : _b.call(_a, data);\n        this.startIndex = endIndex;\n    }\n    /** @internal */\n    ontextentity(cp) {\n        var _a, _b;\n        /*\n         * Entities can be emitted on the character, or directly after.\n         * We use the section start here to get accurate indices.\n         */\n        const index = this.tokenizer.getSectionStart();\n        this.endIndex = index - 1;\n        (_b = (_a = this.cbs).ontext) === null || _b === void 0 ? void 0 : _b.call(_a, (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__.fromCodePoint)(cp));\n        this.startIndex = index;\n    }\n    isVoidElement(name) {\n        return !this.options.xmlMode && voidElements.has(name);\n    }\n    /** @internal */\n    onopentagname(start, endIndex) {\n        this.endIndex = endIndex;\n        let name = this.getSlice(start, endIndex);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        this.emitOpenTag(name);\n    }\n    emitOpenTag(name) {\n        var _a, _b, _c, _d;\n        this.openTagStart = this.startIndex;\n        this.tagname = name;\n        const impliesClose = !this.options.xmlMode && openImpliesClose.get(name);\n        if (impliesClose) {\n            while (this.stack.length > 0 &&\n                impliesClose.has(this.stack[this.stack.length - 1])) {\n                const element = this.stack.pop();\n                (_b = (_a = this.cbs).onclosetag) === null || _b === void 0 ? void 0 : _b.call(_a, element, true);\n            }\n        }\n        if (!this.isVoidElement(name)) {\n            this.stack.push(name);\n            if (foreignContextElements.has(name)) {\n                this.foreignContext.push(true);\n            }\n            else if (htmlIntegrationElements.has(name)) {\n                this.foreignContext.push(false);\n            }\n        }\n        (_d = (_c = this.cbs).onopentagname) === null || _d === void 0 ? void 0 : _d.call(_c, name);\n        if (this.cbs.onopentag)\n            this.attribs = {};\n    }\n    endOpenTag(isImplied) {\n        var _a, _b;\n        this.startIndex = this.openTagStart;\n        if (this.attribs) {\n            (_b = (_a = this.cbs).onopentag) === null || _b === void 0 ? void 0 : _b.call(_a, this.tagname, this.attribs, isImplied);\n            this.attribs = null;\n        }\n        if (this.cbs.onclosetag && this.isVoidElement(this.tagname)) {\n            this.cbs.onclosetag(this.tagname, true);\n        }\n        this.tagname = \"\";\n    }\n    /** @internal */\n    onopentagend(endIndex) {\n        this.endIndex = endIndex;\n        this.endOpenTag(false);\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onclosetag(start, endIndex) {\n        var _a, _b, _c, _d, _e, _f;\n        this.endIndex = endIndex;\n        let name = this.getSlice(start, endIndex);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        if (foreignContextElements.has(name) ||\n            htmlIntegrationElements.has(name)) {\n            this.foreignContext.pop();\n        }\n        if (!this.isVoidElement(name)) {\n            const pos = this.stack.lastIndexOf(name);\n            if (pos !== -1) {\n                if (this.cbs.onclosetag) {\n                    let count = this.stack.length - pos;\n                    while (count--) {\n                        // We know the stack has sufficient elements.\n                        this.cbs.onclosetag(this.stack.pop(), count !== 0);\n                    }\n                }\n                else\n                    this.stack.length = pos;\n            }\n            else if (!this.options.xmlMode && name === \"p\") {\n                // Implicit open before close\n                this.emitOpenTag(\"p\");\n                this.closeCurrentTag(true);\n            }\n        }\n        else if (!this.options.xmlMode && name === \"br\") {\n            // We can't use `emitOpenTag` for implicit open, as `br` would be implicitly closed.\n            (_b = (_a = this.cbs).onopentagname) === null || _b === void 0 ? void 0 : _b.call(_a, \"br\");\n            (_d = (_c = this.cbs).onopentag) === null || _d === void 0 ? void 0 : _d.call(_c, \"br\", {}, true);\n            (_f = (_e = this.cbs).onclosetag) === null || _f === void 0 ? void 0 : _f.call(_e, \"br\", false);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onselfclosingtag(endIndex) {\n        this.endIndex = endIndex;\n        if (this.options.xmlMode ||\n            this.options.recognizeSelfClosing ||\n            this.foreignContext[this.foreignContext.length - 1]) {\n            this.closeCurrentTag(false);\n            // Set `startIndex` for next node\n            this.startIndex = endIndex + 1;\n        }\n        else {\n            // Ignore the fact that the tag is self-closing.\n            this.onopentagend(endIndex);\n        }\n    }\n    closeCurrentTag(isOpenImplied) {\n        var _a, _b;\n        const name = this.tagname;\n        this.endOpenTag(isOpenImplied);\n        // Self-closing tags will be on the top of the stack\n        if (this.stack[this.stack.length - 1] === name) {\n            // If the opening tag isn't implied, the closing tag has to be implied.\n            (_b = (_a = this.cbs).onclosetag) === null || _b === void 0 ? void 0 : _b.call(_a, name, !isOpenImplied);\n            this.stack.pop();\n        }\n    }\n    /** @internal */\n    onattribname(start, endIndex) {\n        this.startIndex = start;\n        const name = this.getSlice(start, endIndex);\n        this.attribname = this.lowerCaseAttributeNames\n            ? name.toLowerCase()\n            : name;\n    }\n    /** @internal */\n    onattribdata(start, endIndex) {\n        this.attribvalue += this.getSlice(start, endIndex);\n    }\n    /** @internal */\n    onattribentity(cp) {\n        this.attribvalue += (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__.fromCodePoint)(cp);\n    }\n    /** @internal */\n    onattribend(quote, endIndex) {\n        var _a, _b;\n        this.endIndex = endIndex;\n        (_b = (_a = this.cbs).onattribute) === null || _b === void 0 ? void 0 : _b.call(_a, this.attribname, this.attribvalue, quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.Double\n            ? '\"'\n            : quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.Single\n                ? \"'\"\n                : quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.NoValue\n                    ? undefined\n                    : null);\n        if (this.attribs &&\n            !Object.prototype.hasOwnProperty.call(this.attribs, this.attribname)) {\n            this.attribs[this.attribname] = this.attribvalue;\n        }\n        this.attribvalue = \"\";\n    }\n    getInstructionName(value) {\n        const index = value.search(reNameEnd);\n        let name = index < 0 ? value : value.substr(0, index);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        return name;\n    }\n    /** @internal */\n    ondeclaration(start, endIndex) {\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex);\n        if (this.cbs.onprocessinginstruction) {\n            const name = this.getInstructionName(value);\n            this.cbs.onprocessinginstruction(`!${name}`, `!${value}`);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onprocessinginstruction(start, endIndex) {\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex);\n        if (this.cbs.onprocessinginstruction) {\n            const name = this.getInstructionName(value);\n            this.cbs.onprocessinginstruction(`?${name}`, `?${value}`);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    oncomment(start, endIndex, offset) {\n        var _a, _b, _c, _d;\n        this.endIndex = endIndex;\n        (_b = (_a = this.cbs).oncomment) === null || _b === void 0 ? void 0 : _b.call(_a, this.getSlice(start, endIndex - offset));\n        (_d = (_c = this.cbs).oncommentend) === null || _d === void 0 ? void 0 : _d.call(_c);\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    oncdata(start, endIndex, offset) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex - offset);\n        if (this.options.xmlMode || this.options.recognizeCDATA) {\n            (_b = (_a = this.cbs).oncdatastart) === null || _b === void 0 ? void 0 : _b.call(_a);\n            (_d = (_c = this.cbs).ontext) === null || _d === void 0 ? void 0 : _d.call(_c, value);\n            (_f = (_e = this.cbs).oncdataend) === null || _f === void 0 ? void 0 : _f.call(_e);\n        }\n        else {\n            (_h = (_g = this.cbs).oncomment) === null || _h === void 0 ? void 0 : _h.call(_g, `[CDATA[${value}]]`);\n            (_k = (_j = this.cbs).oncommentend) === null || _k === void 0 ? void 0 : _k.call(_j);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onend() {\n        var _a, _b;\n        if (this.cbs.onclosetag) {\n            // Set the end index for all remaining tags\n            this.endIndex = this.startIndex;\n            for (let index = this.stack.length; index > 0; this.cbs.onclosetag(this.stack[--index], true))\n                ;\n        }\n        (_b = (_a = this.cbs).onend) === null || _b === void 0 ? void 0 : _b.call(_a);\n    }\n    /**\n     * Resets the parser to a blank state, ready to parse a new HTML document\n     */\n    reset() {\n        var _a, _b, _c, _d;\n        (_b = (_a = this.cbs).onreset) === null || _b === void 0 ? void 0 : _b.call(_a);\n        this.tokenizer.reset();\n        this.tagname = \"\";\n        this.attribname = \"\";\n        this.attribs = null;\n        this.stack.length = 0;\n        this.startIndex = 0;\n        this.endIndex = 0;\n        (_d = (_c = this.cbs).onparserinit) === null || _d === void 0 ? void 0 : _d.call(_c, this);\n        this.buffers.length = 0;\n        this.bufferOffset = 0;\n        this.writeIndex = 0;\n        this.ended = false;\n    }\n    /**\n     * Resets the parser, then parses a complete document and\n     * pushes it to the handler.\n     *\n     * @param data Document to parse.\n     */\n    parseComplete(data) {\n        this.reset();\n        this.end(data);\n    }\n    getSlice(start, end) {\n        while (start - this.bufferOffset >= this.buffers[0].length) {\n            this.shiftBuffer();\n        }\n        let slice = this.buffers[0].slice(start - this.bufferOffset, end - this.bufferOffset);\n        while (end - this.bufferOffset > this.buffers[0].length) {\n            this.shiftBuffer();\n            slice += this.buffers[0].slice(0, end - this.bufferOffset);\n        }\n        return slice;\n    }\n    shiftBuffer() {\n        this.bufferOffset += this.buffers[0].length;\n        this.writeIndex--;\n        this.buffers.shift();\n    }\n    /**\n     * Parses a chunk of data and calls the corresponding callbacks.\n     *\n     * @param chunk Chunk to parse.\n     */\n    write(chunk) {\n        var _a, _b;\n        if (this.ended) {\n            (_b = (_a = this.cbs).onerror) === null || _b === void 0 ? void 0 : _b.call(_a, new Error(\".write() after done!\"));\n            return;\n        }\n        this.buffers.push(chunk);\n        if (this.tokenizer.running) {\n            this.tokenizer.write(chunk);\n            this.writeIndex++;\n        }\n    }\n    /**\n     * Parses the end of the buffer and clears the stack, calls onend.\n     *\n     * @param chunk Optional final chunk to parse.\n     */\n    end(chunk) {\n        var _a, _b;\n        if (this.ended) {\n            (_b = (_a = this.cbs).onerror) === null || _b === void 0 ? void 0 : _b.call(_a, new Error(\".end() after done!\"));\n            return;\n        }\n        if (chunk)\n            this.write(chunk);\n        this.ended = true;\n        this.tokenizer.end();\n    }\n    /**\n     * Pauses parsing. The parser won't emit events until `resume` is called.\n     */\n    pause() {\n        this.tokenizer.pause();\n    }\n    /**\n     * Resumes parsing after `pause` was called.\n     */\n    resume() {\n        this.tokenizer.resume();\n        while (this.tokenizer.running &&\n            this.writeIndex < this.buffers.length) {\n            this.tokenizer.write(this.buffers[this.writeIndex++]);\n        }\n        if (this.ended)\n            this.tokenizer.end();\n    }\n    /**\n     * Alias of `write`, for backwards compatibility.\n     *\n     * @param chunk Chunk to parse.\n     * @deprecated\n     */\n    parseChunk(chunk) {\n        this.write(chunk);\n    }\n    /**\n     * Alias of `end`, for backwards compatibility.\n     *\n     * @param chunk Optional final chunk to parse.\n     * @deprecated\n     */\n    done(chunk) {\n        this.end(chunk);\n    }\n}\n//# sourceMappingURL=Parser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/htmlparser2/lib/esm/Parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/htmlparser2/lib/esm/Tokenizer.js":
/*!*******************************************************!*\
  !*** ./node_modules/htmlparser2/lib/esm/Tokenizer.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuoteType: () => (/* binding */ QuoteType),\n/* harmony export */   \"default\": () => (/* binding */ Tokenizer)\n/* harmony export */ });\n/* harmony import */ var entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! entities/lib/decode.js */ \"(rsc)/./node_modules/entities/lib/esm/decode.js\");\n\nvar CharCodes;\n(function (CharCodes) {\n    CharCodes[CharCodes[\"Tab\"] = 9] = \"Tab\";\n    CharCodes[CharCodes[\"NewLine\"] = 10] = \"NewLine\";\n    CharCodes[CharCodes[\"FormFeed\"] = 12] = \"FormFeed\";\n    CharCodes[CharCodes[\"CarriageReturn\"] = 13] = \"CarriageReturn\";\n    CharCodes[CharCodes[\"Space\"] = 32] = \"Space\";\n    CharCodes[CharCodes[\"ExclamationMark\"] = 33] = \"ExclamationMark\";\n    CharCodes[CharCodes[\"Number\"] = 35] = \"Number\";\n    CharCodes[CharCodes[\"Amp\"] = 38] = \"Amp\";\n    CharCodes[CharCodes[\"SingleQuote\"] = 39] = \"SingleQuote\";\n    CharCodes[CharCodes[\"DoubleQuote\"] = 34] = \"DoubleQuote\";\n    CharCodes[CharCodes[\"Dash\"] = 45] = \"Dash\";\n    CharCodes[CharCodes[\"Slash\"] = 47] = \"Slash\";\n    CharCodes[CharCodes[\"Zero\"] = 48] = \"Zero\";\n    CharCodes[CharCodes[\"Nine\"] = 57] = \"Nine\";\n    CharCodes[CharCodes[\"Semi\"] = 59] = \"Semi\";\n    CharCodes[CharCodes[\"Lt\"] = 60] = \"Lt\";\n    CharCodes[CharCodes[\"Eq\"] = 61] = \"Eq\";\n    CharCodes[CharCodes[\"Gt\"] = 62] = \"Gt\";\n    CharCodes[CharCodes[\"Questionmark\"] = 63] = \"Questionmark\";\n    CharCodes[CharCodes[\"UpperA\"] = 65] = \"UpperA\";\n    CharCodes[CharCodes[\"LowerA\"] = 97] = \"LowerA\";\n    CharCodes[CharCodes[\"UpperF\"] = 70] = \"UpperF\";\n    CharCodes[CharCodes[\"LowerF\"] = 102] = \"LowerF\";\n    CharCodes[CharCodes[\"UpperZ\"] = 90] = \"UpperZ\";\n    CharCodes[CharCodes[\"LowerZ\"] = 122] = \"LowerZ\";\n    CharCodes[CharCodes[\"LowerX\"] = 120] = \"LowerX\";\n    CharCodes[CharCodes[\"OpeningSquareBracket\"] = 91] = \"OpeningSquareBracket\";\n})(CharCodes || (CharCodes = {}));\n/** All the states the tokenizer can be in. */\nvar State;\n(function (State) {\n    State[State[\"Text\"] = 1] = \"Text\";\n    State[State[\"BeforeTagName\"] = 2] = \"BeforeTagName\";\n    State[State[\"InTagName\"] = 3] = \"InTagName\";\n    State[State[\"InSelfClosingTag\"] = 4] = \"InSelfClosingTag\";\n    State[State[\"BeforeClosingTagName\"] = 5] = \"BeforeClosingTagName\";\n    State[State[\"InClosingTagName\"] = 6] = \"InClosingTagName\";\n    State[State[\"AfterClosingTagName\"] = 7] = \"AfterClosingTagName\";\n    // Attributes\n    State[State[\"BeforeAttributeName\"] = 8] = \"BeforeAttributeName\";\n    State[State[\"InAttributeName\"] = 9] = \"InAttributeName\";\n    State[State[\"AfterAttributeName\"] = 10] = \"AfterAttributeName\";\n    State[State[\"BeforeAttributeValue\"] = 11] = \"BeforeAttributeValue\";\n    State[State[\"InAttributeValueDq\"] = 12] = \"InAttributeValueDq\";\n    State[State[\"InAttributeValueSq\"] = 13] = \"InAttributeValueSq\";\n    State[State[\"InAttributeValueNq\"] = 14] = \"InAttributeValueNq\";\n    // Declarations\n    State[State[\"BeforeDeclaration\"] = 15] = \"BeforeDeclaration\";\n    State[State[\"InDeclaration\"] = 16] = \"InDeclaration\";\n    // Processing instructions\n    State[State[\"InProcessingInstruction\"] = 17] = \"InProcessingInstruction\";\n    // Comments & CDATA\n    State[State[\"BeforeComment\"] = 18] = \"BeforeComment\";\n    State[State[\"CDATASequence\"] = 19] = \"CDATASequence\";\n    State[State[\"InSpecialComment\"] = 20] = \"InSpecialComment\";\n    State[State[\"InCommentLike\"] = 21] = \"InCommentLike\";\n    // Special tags\n    State[State[\"BeforeSpecialS\"] = 22] = \"BeforeSpecialS\";\n    State[State[\"SpecialStartSequence\"] = 23] = \"SpecialStartSequence\";\n    State[State[\"InSpecialTag\"] = 24] = \"InSpecialTag\";\n    State[State[\"BeforeEntity\"] = 25] = \"BeforeEntity\";\n    State[State[\"BeforeNumericEntity\"] = 26] = \"BeforeNumericEntity\";\n    State[State[\"InNamedEntity\"] = 27] = \"InNamedEntity\";\n    State[State[\"InNumericEntity\"] = 28] = \"InNumericEntity\";\n    State[State[\"InHexEntity\"] = 29] = \"InHexEntity\";\n})(State || (State = {}));\nfunction isWhitespace(c) {\n    return (c === CharCodes.Space ||\n        c === CharCodes.NewLine ||\n        c === CharCodes.Tab ||\n        c === CharCodes.FormFeed ||\n        c === CharCodes.CarriageReturn);\n}\nfunction isEndOfTagSection(c) {\n    return c === CharCodes.Slash || c === CharCodes.Gt || isWhitespace(c);\n}\nfunction isNumber(c) {\n    return c >= CharCodes.Zero && c <= CharCodes.Nine;\n}\nfunction isASCIIAlpha(c) {\n    return ((c >= CharCodes.LowerA && c <= CharCodes.LowerZ) ||\n        (c >= CharCodes.UpperA && c <= CharCodes.UpperZ));\n}\nfunction isHexDigit(c) {\n    return ((c >= CharCodes.UpperA && c <= CharCodes.UpperF) ||\n        (c >= CharCodes.LowerA && c <= CharCodes.LowerF));\n}\nvar QuoteType;\n(function (QuoteType) {\n    QuoteType[QuoteType[\"NoValue\"] = 0] = \"NoValue\";\n    QuoteType[QuoteType[\"Unquoted\"] = 1] = \"Unquoted\";\n    QuoteType[QuoteType[\"Single\"] = 2] = \"Single\";\n    QuoteType[QuoteType[\"Double\"] = 3] = \"Double\";\n})(QuoteType || (QuoteType = {}));\n/**\n * Sequences used to match longer strings.\n *\n * We don't have `Script`, `Style`, or `Title` here. Instead, we re-use the *End\n * sequences with an increased offset.\n */\nconst Sequences = {\n    Cdata: new Uint8Array([0x43, 0x44, 0x41, 0x54, 0x41, 0x5b]),\n    CdataEnd: new Uint8Array([0x5d, 0x5d, 0x3e]),\n    CommentEnd: new Uint8Array([0x2d, 0x2d, 0x3e]),\n    ScriptEnd: new Uint8Array([0x3c, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74]),\n    StyleEnd: new Uint8Array([0x3c, 0x2f, 0x73, 0x74, 0x79, 0x6c, 0x65]),\n    TitleEnd: new Uint8Array([0x3c, 0x2f, 0x74, 0x69, 0x74, 0x6c, 0x65]), // `</title`\n};\nclass Tokenizer {\n    constructor({ xmlMode = false, decodeEntities = true, }, cbs) {\n        this.cbs = cbs;\n        /** The current state the tokenizer is in. */\n        this.state = State.Text;\n        /** The read buffer. */\n        this.buffer = \"\";\n        /** The beginning of the section that is currently being read. */\n        this.sectionStart = 0;\n        /** The index within the buffer that we are currently looking at. */\n        this.index = 0;\n        /** Some behavior, eg. when decoding entities, is done while we are in another state. This keeps track of the other state type. */\n        this.baseState = State.Text;\n        /** For special parsing behavior inside of script and style tags. */\n        this.isSpecial = false;\n        /** Indicates whether the tokenizer has been paused. */\n        this.running = true;\n        /** The offset of the current buffer. */\n        this.offset = 0;\n        this.currentSequence = undefined;\n        this.sequenceIndex = 0;\n        this.trieIndex = 0;\n        this.trieCurrent = 0;\n        /** For named entities, the index of the value. For numeric entities, the code point. */\n        this.entityResult = 0;\n        this.entityExcess = 0;\n        this.xmlMode = xmlMode;\n        this.decodeEntities = decodeEntities;\n        this.entityTrie = xmlMode ? entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.xmlDecodeTree : entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.htmlDecodeTree;\n    }\n    reset() {\n        this.state = State.Text;\n        this.buffer = \"\";\n        this.sectionStart = 0;\n        this.index = 0;\n        this.baseState = State.Text;\n        this.currentSequence = undefined;\n        this.running = true;\n        this.offset = 0;\n    }\n    write(chunk) {\n        this.offset += this.buffer.length;\n        this.buffer = chunk;\n        this.parse();\n    }\n    end() {\n        if (this.running)\n            this.finish();\n    }\n    pause() {\n        this.running = false;\n    }\n    resume() {\n        this.running = true;\n        if (this.index < this.buffer.length + this.offset) {\n            this.parse();\n        }\n    }\n    /**\n     * The current index within all of the written data.\n     */\n    getIndex() {\n        return this.index;\n    }\n    /**\n     * The start of the current section.\n     */\n    getSectionStart() {\n        return this.sectionStart;\n    }\n    stateText(c) {\n        if (c === CharCodes.Lt ||\n            (!this.decodeEntities && this.fastForwardTo(CharCodes.Lt))) {\n            if (this.index > this.sectionStart) {\n                this.cbs.ontext(this.sectionStart, this.index);\n            }\n            this.state = State.BeforeTagName;\n            this.sectionStart = this.index;\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.state = State.BeforeEntity;\n        }\n    }\n    stateSpecialStartSequence(c) {\n        const isEnd = this.sequenceIndex === this.currentSequence.length;\n        const isMatch = isEnd\n            ? // If we are at the end of the sequence, make sure the tag name has ended\n                isEndOfTagSection(c)\n            : // Otherwise, do a case-insensitive comparison\n                (c | 0x20) === this.currentSequence[this.sequenceIndex];\n        if (!isMatch) {\n            this.isSpecial = false;\n        }\n        else if (!isEnd) {\n            this.sequenceIndex++;\n            return;\n        }\n        this.sequenceIndex = 0;\n        this.state = State.InTagName;\n        this.stateInTagName(c);\n    }\n    /** Look for an end tag. For <title> tags, also decode entities. */\n    stateInSpecialTag(c) {\n        if (this.sequenceIndex === this.currentSequence.length) {\n            if (c === CharCodes.Gt || isWhitespace(c)) {\n                const endOfText = this.index - this.currentSequence.length;\n                if (this.sectionStart < endOfText) {\n                    // Spoof the index so that reported locations match up.\n                    const actualIndex = this.index;\n                    this.index = endOfText;\n                    this.cbs.ontext(this.sectionStart, endOfText);\n                    this.index = actualIndex;\n                }\n                this.isSpecial = false;\n                this.sectionStart = endOfText + 2; // Skip over the `</`\n                this.stateInClosingTagName(c);\n                return; // We are done; skip the rest of the function.\n            }\n            this.sequenceIndex = 0;\n        }\n        if ((c | 0x20) === this.currentSequence[this.sequenceIndex]) {\n            this.sequenceIndex += 1;\n        }\n        else if (this.sequenceIndex === 0) {\n            if (this.currentSequence === Sequences.TitleEnd) {\n                // We have to parse entities in <title> tags.\n                if (this.decodeEntities && c === CharCodes.Amp) {\n                    this.state = State.BeforeEntity;\n                }\n            }\n            else if (this.fastForwardTo(CharCodes.Lt)) {\n                // Outside of <title> tags, we can fast-forward.\n                this.sequenceIndex = 1;\n            }\n        }\n        else {\n            // If we see a `<`, set the sequence index to 1; useful for eg. `<</script>`.\n            this.sequenceIndex = Number(c === CharCodes.Lt);\n        }\n    }\n    stateCDATASequence(c) {\n        if (c === Sequences.Cdata[this.sequenceIndex]) {\n            if (++this.sequenceIndex === Sequences.Cdata.length) {\n                this.state = State.InCommentLike;\n                this.currentSequence = Sequences.CdataEnd;\n                this.sequenceIndex = 0;\n                this.sectionStart = this.index + 1;\n            }\n        }\n        else {\n            this.sequenceIndex = 0;\n            this.state = State.InDeclaration;\n            this.stateInDeclaration(c); // Reconsume the character\n        }\n    }\n    /**\n     * When we wait for one specific character, we can speed things up\n     * by skipping through the buffer until we find it.\n     *\n     * @returns Whether the character was found.\n     */\n    fastForwardTo(c) {\n        while (++this.index < this.buffer.length + this.offset) {\n            if (this.buffer.charCodeAt(this.index - this.offset) === c) {\n                return true;\n            }\n        }\n        /*\n         * We increment the index at the end of the `parse` loop,\n         * so set it to `buffer.length - 1` here.\n         *\n         * TODO: Refactor `parse` to increment index before calling states.\n         */\n        this.index = this.buffer.length + this.offset - 1;\n        return false;\n    }\n    /**\n     * Comments and CDATA end with `-->` and `]]>`.\n     *\n     * Their common qualities are:\n     * - Their end sequences have a distinct character they start with.\n     * - That character is then repeated, so we have to check multiple repeats.\n     * - All characters but the start character of the sequence can be skipped.\n     */\n    stateInCommentLike(c) {\n        if (c === this.currentSequence[this.sequenceIndex]) {\n            if (++this.sequenceIndex === this.currentSequence.length) {\n                if (this.currentSequence === Sequences.CdataEnd) {\n                    this.cbs.oncdata(this.sectionStart, this.index, 2);\n                }\n                else {\n                    this.cbs.oncomment(this.sectionStart, this.index, 2);\n                }\n                this.sequenceIndex = 0;\n                this.sectionStart = this.index + 1;\n                this.state = State.Text;\n            }\n        }\n        else if (this.sequenceIndex === 0) {\n            // Fast-forward to the first character of the sequence\n            if (this.fastForwardTo(this.currentSequence[0])) {\n                this.sequenceIndex = 1;\n            }\n        }\n        else if (c !== this.currentSequence[this.sequenceIndex - 1]) {\n            // Allow long sequences, eg. --->, ]]]>\n            this.sequenceIndex = 0;\n        }\n    }\n    /**\n     * HTML only allows ASCII alpha characters (a-z and A-Z) at the beginning of a tag name.\n     *\n     * XML allows a lot more characters here (@see https://www.w3.org/TR/REC-xml/#NT-NameStartChar).\n     * We allow anything that wouldn't end the tag.\n     */\n    isTagStartChar(c) {\n        return this.xmlMode ? !isEndOfTagSection(c) : isASCIIAlpha(c);\n    }\n    startSpecial(sequence, offset) {\n        this.isSpecial = true;\n        this.currentSequence = sequence;\n        this.sequenceIndex = offset;\n        this.state = State.SpecialStartSequence;\n    }\n    stateBeforeTagName(c) {\n        if (c === CharCodes.ExclamationMark) {\n            this.state = State.BeforeDeclaration;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.Questionmark) {\n            this.state = State.InProcessingInstruction;\n            this.sectionStart = this.index + 1;\n        }\n        else if (this.isTagStartChar(c)) {\n            const lower = c | 0x20;\n            this.sectionStart = this.index;\n            if (!this.xmlMode && lower === Sequences.TitleEnd[2]) {\n                this.startSpecial(Sequences.TitleEnd, 3);\n            }\n            else {\n                this.state =\n                    !this.xmlMode && lower === Sequences.ScriptEnd[2]\n                        ? State.BeforeSpecialS\n                        : State.InTagName;\n            }\n        }\n        else if (c === CharCodes.Slash) {\n            this.state = State.BeforeClosingTagName;\n        }\n        else {\n            this.state = State.Text;\n            this.stateText(c);\n        }\n    }\n    stateInTagName(c) {\n        if (isEndOfTagSection(c)) {\n            this.cbs.onopentagname(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n    }\n    stateBeforeClosingTagName(c) {\n        if (isWhitespace(c)) {\n            // Ignore\n        }\n        else if (c === CharCodes.Gt) {\n            this.state = State.Text;\n        }\n        else {\n            this.state = this.isTagStartChar(c)\n                ? State.InClosingTagName\n                : State.InSpecialComment;\n            this.sectionStart = this.index;\n        }\n    }\n    stateInClosingTagName(c) {\n        if (c === CharCodes.Gt || isWhitespace(c)) {\n            this.cbs.onclosetag(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.AfterClosingTagName;\n            this.stateAfterClosingTagName(c);\n        }\n    }\n    stateAfterClosingTagName(c) {\n        // Skip everything until \">\"\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.state = State.Text;\n            this.baseState = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeAttributeName(c) {\n        if (c === CharCodes.Gt) {\n            this.cbs.onopentagend(this.index);\n            if (this.isSpecial) {\n                this.state = State.InSpecialTag;\n                this.sequenceIndex = 0;\n            }\n            else {\n                this.state = State.Text;\n            }\n            this.baseState = this.state;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.Slash) {\n            this.state = State.InSelfClosingTag;\n        }\n        else if (!isWhitespace(c)) {\n            this.state = State.InAttributeName;\n            this.sectionStart = this.index;\n        }\n    }\n    stateInSelfClosingTag(c) {\n        if (c === CharCodes.Gt) {\n            this.cbs.onselfclosingtag(this.index);\n            this.state = State.Text;\n            this.baseState = State.Text;\n            this.sectionStart = this.index + 1;\n            this.isSpecial = false; // Reset special state, in case of self-closing special tags\n        }\n        else if (!isWhitespace(c)) {\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n    }\n    stateInAttributeName(c) {\n        if (c === CharCodes.Eq || isEndOfTagSection(c)) {\n            this.cbs.onattribname(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.AfterAttributeName;\n            this.stateAfterAttributeName(c);\n        }\n    }\n    stateAfterAttributeName(c) {\n        if (c === CharCodes.Eq) {\n            this.state = State.BeforeAttributeValue;\n        }\n        else if (c === CharCodes.Slash || c === CharCodes.Gt) {\n            this.cbs.onattribend(QuoteType.NoValue, this.index);\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n        else if (!isWhitespace(c)) {\n            this.cbs.onattribend(QuoteType.NoValue, this.index);\n            this.state = State.InAttributeName;\n            this.sectionStart = this.index;\n        }\n    }\n    stateBeforeAttributeValue(c) {\n        if (c === CharCodes.DoubleQuote) {\n            this.state = State.InAttributeValueDq;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.SingleQuote) {\n            this.state = State.InAttributeValueSq;\n            this.sectionStart = this.index + 1;\n        }\n        else if (!isWhitespace(c)) {\n            this.sectionStart = this.index;\n            this.state = State.InAttributeValueNq;\n            this.stateInAttributeValueNoQuotes(c); // Reconsume token\n        }\n    }\n    handleInAttributeValue(c, quote) {\n        if (c === quote ||\n            (!this.decodeEntities && this.fastForwardTo(quote))) {\n            this.cbs.onattribdata(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.cbs.onattribend(quote === CharCodes.DoubleQuote\n                ? QuoteType.Double\n                : QuoteType.Single, this.index);\n            this.state = State.BeforeAttributeName;\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.baseState = this.state;\n            this.state = State.BeforeEntity;\n        }\n    }\n    stateInAttributeValueDoubleQuotes(c) {\n        this.handleInAttributeValue(c, CharCodes.DoubleQuote);\n    }\n    stateInAttributeValueSingleQuotes(c) {\n        this.handleInAttributeValue(c, CharCodes.SingleQuote);\n    }\n    stateInAttributeValueNoQuotes(c) {\n        if (isWhitespace(c) || c === CharCodes.Gt) {\n            this.cbs.onattribdata(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.cbs.onattribend(QuoteType.Unquoted, this.index);\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.baseState = this.state;\n            this.state = State.BeforeEntity;\n        }\n    }\n    stateBeforeDeclaration(c) {\n        if (c === CharCodes.OpeningSquareBracket) {\n            this.state = State.CDATASequence;\n            this.sequenceIndex = 0;\n        }\n        else {\n            this.state =\n                c === CharCodes.Dash\n                    ? State.BeforeComment\n                    : State.InDeclaration;\n        }\n    }\n    stateInDeclaration(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.ondeclaration(this.sectionStart, this.index);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateInProcessingInstruction(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.onprocessinginstruction(this.sectionStart, this.index);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeComment(c) {\n        if (c === CharCodes.Dash) {\n            this.state = State.InCommentLike;\n            this.currentSequence = Sequences.CommentEnd;\n            // Allow short comments (eg. <!-->)\n            this.sequenceIndex = 2;\n            this.sectionStart = this.index + 1;\n        }\n        else {\n            this.state = State.InDeclaration;\n        }\n    }\n    stateInSpecialComment(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.oncomment(this.sectionStart, this.index, 0);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeSpecialS(c) {\n        const lower = c | 0x20;\n        if (lower === Sequences.ScriptEnd[3]) {\n            this.startSpecial(Sequences.ScriptEnd, 4);\n        }\n        else if (lower === Sequences.StyleEnd[3]) {\n            this.startSpecial(Sequences.StyleEnd, 4);\n        }\n        else {\n            this.state = State.InTagName;\n            this.stateInTagName(c); // Consume the token again\n        }\n    }\n    stateBeforeEntity(c) {\n        // Start excess with 1 to include the '&'\n        this.entityExcess = 1;\n        this.entityResult = 0;\n        if (c === CharCodes.Number) {\n            this.state = State.BeforeNumericEntity;\n        }\n        else if (c === CharCodes.Amp) {\n            // We have two `&` characters in a row. Stay in the current state.\n        }\n        else {\n            this.trieIndex = 0;\n            this.trieCurrent = this.entityTrie[0];\n            this.state = State.InNamedEntity;\n            this.stateInNamedEntity(c);\n        }\n    }\n    stateInNamedEntity(c) {\n        this.entityExcess += 1;\n        this.trieIndex = (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.determineBranch)(this.entityTrie, this.trieCurrent, this.trieIndex + 1, c);\n        if (this.trieIndex < 0) {\n            this.emitNamedEntity();\n            this.index--;\n            return;\n        }\n        this.trieCurrent = this.entityTrie[this.trieIndex];\n        const masked = this.trieCurrent & entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.BinTrieFlags.VALUE_LENGTH;\n        // If the branch is a value, store it and continue\n        if (masked) {\n            // The mask is the number of bytes of the value, including the current byte.\n            const valueLength = (masked >> 14) - 1;\n            // If we have a legacy entity while parsing strictly, just skip the number of bytes\n            if (!this.allowLegacyEntity() && c !== CharCodes.Semi) {\n                this.trieIndex += valueLength;\n            }\n            else {\n                // Add 1 as we have already incremented the excess\n                const entityStart = this.index - this.entityExcess + 1;\n                if (entityStart > this.sectionStart) {\n                    this.emitPartial(this.sectionStart, entityStart);\n                }\n                // If this is a surrogate pair, consume the next two bytes\n                this.entityResult = this.trieIndex;\n                this.trieIndex += valueLength;\n                this.entityExcess = 0;\n                this.sectionStart = this.index + 1;\n                if (valueLength === 0) {\n                    this.emitNamedEntity();\n                }\n            }\n        }\n    }\n    emitNamedEntity() {\n        this.state = this.baseState;\n        if (this.entityResult === 0) {\n            return;\n        }\n        const valueLength = (this.entityTrie[this.entityResult] & entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.BinTrieFlags.VALUE_LENGTH) >>\n            14;\n        switch (valueLength) {\n            case 1: {\n                this.emitCodePoint(this.entityTrie[this.entityResult] &\n                    ~entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.BinTrieFlags.VALUE_LENGTH);\n                break;\n            }\n            case 2: {\n                this.emitCodePoint(this.entityTrie[this.entityResult + 1]);\n                break;\n            }\n            case 3: {\n                this.emitCodePoint(this.entityTrie[this.entityResult + 1]);\n                this.emitCodePoint(this.entityTrie[this.entityResult + 2]);\n            }\n        }\n    }\n    stateBeforeNumericEntity(c) {\n        if ((c | 0x20) === CharCodes.LowerX) {\n            this.entityExcess++;\n            this.state = State.InHexEntity;\n        }\n        else {\n            this.state = State.InNumericEntity;\n            this.stateInNumericEntity(c);\n        }\n    }\n    emitNumericEntity(strict) {\n        const entityStart = this.index - this.entityExcess - 1;\n        const numberStart = entityStart + 2 + Number(this.state === State.InHexEntity);\n        if (numberStart !== this.index) {\n            // Emit leading data if any\n            if (entityStart > this.sectionStart) {\n                this.emitPartial(this.sectionStart, entityStart);\n            }\n            this.sectionStart = this.index + Number(strict);\n            this.emitCodePoint((0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.replaceCodePoint)(this.entityResult));\n        }\n        this.state = this.baseState;\n    }\n    stateInNumericEntity(c) {\n        if (c === CharCodes.Semi) {\n            this.emitNumericEntity(true);\n        }\n        else if (isNumber(c)) {\n            this.entityResult = this.entityResult * 10 + (c - CharCodes.Zero);\n            this.entityExcess++;\n        }\n        else {\n            if (this.allowLegacyEntity()) {\n                this.emitNumericEntity(false);\n            }\n            else {\n                this.state = this.baseState;\n            }\n            this.index--;\n        }\n    }\n    stateInHexEntity(c) {\n        if (c === CharCodes.Semi) {\n            this.emitNumericEntity(true);\n        }\n        else if (isNumber(c)) {\n            this.entityResult = this.entityResult * 16 + (c - CharCodes.Zero);\n            this.entityExcess++;\n        }\n        else if (isHexDigit(c)) {\n            this.entityResult =\n                this.entityResult * 16 + ((c | 0x20) - CharCodes.LowerA + 10);\n            this.entityExcess++;\n        }\n        else {\n            if (this.allowLegacyEntity()) {\n                this.emitNumericEntity(false);\n            }\n            else {\n                this.state = this.baseState;\n            }\n            this.index--;\n        }\n    }\n    allowLegacyEntity() {\n        return (!this.xmlMode &&\n            (this.baseState === State.Text ||\n                this.baseState === State.InSpecialTag));\n    }\n    /**\n     * Remove data that has already been consumed from the buffer.\n     */\n    cleanup() {\n        // If we are inside of text or attributes, emit what we already have.\n        if (this.running && this.sectionStart !== this.index) {\n            if (this.state === State.Text ||\n                (this.state === State.InSpecialTag && this.sequenceIndex === 0)) {\n                this.cbs.ontext(this.sectionStart, this.index);\n                this.sectionStart = this.index;\n            }\n            else if (this.state === State.InAttributeValueDq ||\n                this.state === State.InAttributeValueSq ||\n                this.state === State.InAttributeValueNq) {\n                this.cbs.onattribdata(this.sectionStart, this.index);\n                this.sectionStart = this.index;\n            }\n        }\n    }\n    shouldContinue() {\n        return this.index < this.buffer.length + this.offset && this.running;\n    }\n    /**\n     * Iterates through the buffer, calling the function corresponding to the current state.\n     *\n     * States that are more likely to be hit are higher up, as a performance improvement.\n     */\n    parse() {\n        while (this.shouldContinue()) {\n            const c = this.buffer.charCodeAt(this.index - this.offset);\n            switch (this.state) {\n                case State.Text: {\n                    this.stateText(c);\n                    break;\n                }\n                case State.SpecialStartSequence: {\n                    this.stateSpecialStartSequence(c);\n                    break;\n                }\n                case State.InSpecialTag: {\n                    this.stateInSpecialTag(c);\n                    break;\n                }\n                case State.CDATASequence: {\n                    this.stateCDATASequence(c);\n                    break;\n                }\n                case State.InAttributeValueDq: {\n                    this.stateInAttributeValueDoubleQuotes(c);\n                    break;\n                }\n                case State.InAttributeName: {\n                    this.stateInAttributeName(c);\n                    break;\n                }\n                case State.InCommentLike: {\n                    this.stateInCommentLike(c);\n                    break;\n                }\n                case State.InSpecialComment: {\n                    this.stateInSpecialComment(c);\n                    break;\n                }\n                case State.BeforeAttributeName: {\n                    this.stateBeforeAttributeName(c);\n                    break;\n                }\n                case State.InTagName: {\n                    this.stateInTagName(c);\n                    break;\n                }\n                case State.InClosingTagName: {\n                    this.stateInClosingTagName(c);\n                    break;\n                }\n                case State.BeforeTagName: {\n                    this.stateBeforeTagName(c);\n                    break;\n                }\n                case State.AfterAttributeName: {\n                    this.stateAfterAttributeName(c);\n                    break;\n                }\n                case State.InAttributeValueSq: {\n                    this.stateInAttributeValueSingleQuotes(c);\n                    break;\n                }\n                case State.BeforeAttributeValue: {\n                    this.stateBeforeAttributeValue(c);\n                    break;\n                }\n                case State.BeforeClosingTagName: {\n                    this.stateBeforeClosingTagName(c);\n                    break;\n                }\n                case State.AfterClosingTagName: {\n                    this.stateAfterClosingTagName(c);\n                    break;\n                }\n                case State.BeforeSpecialS: {\n                    this.stateBeforeSpecialS(c);\n                    break;\n                }\n                case State.InAttributeValueNq: {\n                    this.stateInAttributeValueNoQuotes(c);\n                    break;\n                }\n                case State.InSelfClosingTag: {\n                    this.stateInSelfClosingTag(c);\n                    break;\n                }\n                case State.InDeclaration: {\n                    this.stateInDeclaration(c);\n                    break;\n                }\n                case State.BeforeDeclaration: {\n                    this.stateBeforeDeclaration(c);\n                    break;\n                }\n                case State.BeforeComment: {\n                    this.stateBeforeComment(c);\n                    break;\n                }\n                case State.InProcessingInstruction: {\n                    this.stateInProcessingInstruction(c);\n                    break;\n                }\n                case State.InNamedEntity: {\n                    this.stateInNamedEntity(c);\n                    break;\n                }\n                case State.BeforeEntity: {\n                    this.stateBeforeEntity(c);\n                    break;\n                }\n                case State.InHexEntity: {\n                    this.stateInHexEntity(c);\n                    break;\n                }\n                case State.InNumericEntity: {\n                    this.stateInNumericEntity(c);\n                    break;\n                }\n                default: {\n                    // `this._state === State.BeforeNumericEntity`\n                    this.stateBeforeNumericEntity(c);\n                }\n            }\n            this.index++;\n        }\n        this.cleanup();\n    }\n    finish() {\n        if (this.state === State.InNamedEntity) {\n            this.emitNamedEntity();\n        }\n        // If there is remaining data, emit it in a reasonable way\n        if (this.sectionStart < this.index) {\n            this.handleTrailingData();\n        }\n        this.cbs.onend();\n    }\n    /** Handle any trailing data. */\n    handleTrailingData() {\n        const endIndex = this.buffer.length + this.offset;\n        if (this.state === State.InCommentLike) {\n            if (this.currentSequence === Sequences.CdataEnd) {\n                this.cbs.oncdata(this.sectionStart, endIndex, 0);\n            }\n            else {\n                this.cbs.oncomment(this.sectionStart, endIndex, 0);\n            }\n        }\n        else if (this.state === State.InNumericEntity &&\n            this.allowLegacyEntity()) {\n            this.emitNumericEntity(false);\n            // All trailing data will have been consumed\n        }\n        else if (this.state === State.InHexEntity &&\n            this.allowLegacyEntity()) {\n            this.emitNumericEntity(false);\n            // All trailing data will have been consumed\n        }\n        else if (this.state === State.InTagName ||\n            this.state === State.BeforeAttributeName ||\n            this.state === State.BeforeAttributeValue ||\n            this.state === State.AfterAttributeName ||\n            this.state === State.InAttributeName ||\n            this.state === State.InAttributeValueSq ||\n            this.state === State.InAttributeValueDq ||\n            this.state === State.InAttributeValueNq ||\n            this.state === State.InClosingTagName) {\n            /*\n             * If we are currently in an opening or closing tag, us not calling the\n             * respective callback signals that the tag should be ignored.\n             */\n        }\n        else {\n            this.cbs.ontext(this.sectionStart, endIndex);\n        }\n    }\n    emitPartial(start, endIndex) {\n        if (this.baseState !== State.Text &&\n            this.baseState !== State.InSpecialTag) {\n            this.cbs.onattribdata(start, endIndex);\n        }\n        else {\n            this.cbs.ontext(start, endIndex);\n        }\n    }\n    emitCodePoint(cp) {\n        if (this.baseState !== State.Text &&\n            this.baseState !== State.InSpecialTag) {\n            this.cbs.onattribentity(cp);\n        }\n        else {\n            this.cbs.ontextentity(cp);\n        }\n    }\n}\n//# sourceMappingURL=Tokenizer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/htmlparser2/lib/esm/Tokenizer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/htmlparser2/lib/esm/index.js":
/*!***************************************************!*\
  !*** ./node_modules/htmlparser2/lib/esm/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultHandler: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler),\n/* harmony export */   DomHandler: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler),\n/* harmony export */   DomUtils: () => (/* reexport module object */ domutils__WEBPACK_IMPORTED_MODULE_4__),\n/* harmony export */   ElementType: () => (/* reexport module object */ domelementtype__WEBPACK_IMPORTED_MODULE_3__),\n/* harmony export */   Parser: () => (/* reexport safe */ _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser),\n/* harmony export */   Tokenizer: () => (/* reexport safe */ _Tokenizer_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   createDomStream: () => (/* binding */ createDomStream),\n/* harmony export */   getFeed: () => (/* reexport safe */ domutils__WEBPACK_IMPORTED_MODULE_4__.getFeed),\n/* harmony export */   parseDOM: () => (/* binding */ parseDOM),\n/* harmony export */   parseDocument: () => (/* binding */ parseDocument),\n/* harmony export */   parseFeed: () => (/* binding */ parseFeed)\n/* harmony export */ });\n/* harmony import */ var _Parser_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Parser.js */ \"(rsc)/./node_modules/htmlparser2/lib/esm/Parser.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var _Tokenizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Tokenizer.js */ \"(rsc)/./node_modules/htmlparser2/lib/esm/Tokenizer.js\");\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/domutils/lib/esm/index.js\");\n\n\n\n\n// Helper methods\n/**\n * Parses the data, returns the resulting document.\n *\n * @param data The data that should be parsed.\n * @param options Optional options for the parser and DOM builder.\n */\nfunction parseDocument(data, options) {\n    const handler = new domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler(undefined, options);\n    new _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser(handler, options).end(data);\n    return handler.root;\n}\n/**\n * Parses data, returns an array of the root nodes.\n *\n * Note that the root nodes still have a `Document` node as their parent.\n * Use `parseDocument` to get the `Document` node instead.\n *\n * @param data The data that should be parsed.\n * @param options Optional options for the parser and DOM builder.\n * @deprecated Use `parseDocument` instead.\n */\nfunction parseDOM(data, options) {\n    return parseDocument(data, options).children;\n}\n/**\n * Creates a parser instance, with an attached DOM handler.\n *\n * @param callback A callback that will be called once parsing has been completed.\n * @param options Optional options for the parser and DOM builder.\n * @param elementCallback An optional callback that will be called every time a tag has been completed inside of the DOM.\n */\nfunction createDomStream(callback, options, elementCallback) {\n    const handler = new domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler(callback, options, elementCallback);\n    return new _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser(handler, options);\n}\n\n/*\n * All of the following exports exist for backwards-compatibility.\n * They should probably be removed eventually.\n */\n\n\n\nconst parseFeedDefaultOptions = { xmlMode: true };\n/**\n * Parse a feed.\n *\n * @param feed The feed that should be parsed, as a string.\n * @param options Optionally, options for parsing. When using this, you should set `xmlMode` to `true`.\n */\nfunction parseFeed(feed, options = parseFeedDefaultOptions) {\n    return (0,domutils__WEBPACK_IMPORTED_MODULE_4__.getFeed)(parseDOM(feed, options));\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/htmlparser2/lib/esm/index.js\n");

/***/ })

};
;