#search-bar-blogs {
  @extend #search-bar-opportunities;

  .btn {
    margin: 0 5px;

    svg {
      margin: 0 !important;
    }
  }
}

.blog-content {
  // padding: 15px;
  p,
  span {
    font-size: 18px !important;
    @include media-query(mobile) {
      font-size: 17px !important;
    }
  }

  h2 {
    font-size: 26px !important;
    color: $blue;
  }
  h3 {
    font-size: 22px !important;
    color: rgb(73, 69, 69);
  }
  h4 {
    font-size: 20;
    color: rgb(112, 108, 108);
  }

  ul {
    list-style: none;
    padding-left: 1em;

    & > li::before {
      content: "";
      display: inline-block;
      width: 12px;
      height: 12px;
      background-color: $yellow;
      margin-right: 0.5em;
    }
  }

  p,
  ul,
  div,
  span,
  li {
    background: transparent !important;
    font-size: 18px;
  }

  figure {
    width: 100% !important;
    margin: 10px 0;
    height: auto;
    min-width: 300px;
    border: 1px solid #699bd4;
    background-color: transparent;

    img {
      width: -webkit-fill-available !important;
      height: auto !important;
      padding: 10px;
    }
  }

  img {
    width: -webkit-fill-available !important;
    height: auto;
  }

  table {
    width: -webkit-fill-available !important;
  }

  .summary {
    margin: 15px 0;

    .dropdown-header {
      display: flex;
      align-items: center;
    }

    svg {
      path {
        fill: $blue;
      }
    }
  }
  button {
    display: inline-block;
    background: linear-gradient(to right, #5ce6e6, #0052cc);
    color: white;
    padding: 17px 24px;
    border-radius: 999px;
    text-decoration: none;
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    transition: background 0.3s ease;
    border: none;
  }

  button:hover {
    opacity: 0.9;
  }
}

#blogs-page {
  .pagination {
    margin-top: 20px;
  }
  .featured-title {
    font-size: 2.5rem;
    font-family: "Proxima-Nova-SemiBold" !important;
    @include media-query(mobile, tablet) {
      font-size: 1.75rem !important;
    }
  }
  padding-bottom: 50px;

  @include media-query(mobile) {
    padding-bottom: 20px;
  }

  .btn.white {
    color: $white;
    border-color: $white;

    @include media-query(mobile) {
      width: auto;
      display: flex;
      justify-content: center;
    }
  }

  .first-blog {
    display: flex;
    justify-content: space-between;

    @include media-query(mobile, tablet) {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      flex-direction: column-reverse;
      align-content: flex-start;
    }
  }

  .blog-img-section {
    border: 1px solid #699bd4;
    background-color: transparent;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: auto;
    min-width: 675px;

    box-shadow: none;
    position: relative;
    margin: 10px 0px 20px 30px;

    @include media-query(mobile, tablet) {
      min-width: 200px;
      margin: 50px 0px 20px 0px;
    }

    .img-section {
      background-size: cover;
      background-position: center;
      padding: 100px;
      margin: 15px;
      background-color: #dbe8f6;
      height: 100%;
      min-height: 150px;
      @include media-query(mobile, tablet) {
        min-height: 100px;
      }
    }
  }

  .last-blog {
    min-height: 220px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    margin-bottom: 20px;
    .description {
      font-size: 18px;
      @include media-query(mobile) {
        font-size: 16px;
      }
    }
    .title,
    .description {
      color: $blue;
      margin-bottom: 20px;

      @include media-query(mobile, tablet) {
        margin-bottom: 20px;
      }
    }
  }
}
#last-blog {
  margin-top: 24px;
  width: -webkit-fill-available;
  min-height: 270px;
  padding: 45px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  @include media-query(mobile, tablet) {
    padding: 24px 20px;
  }

  .title {
    font-size: 36px;
    color: $white;
    font-family: "Proxima-Nova-Medium" !important;
    margin-bottom: 15px;
  }

  .description {
    font-size: 20px;
    color: $white;
    margin-bottom: 15px;
  }
}

#blog__categories__slider {
  overflow: hidden;

  .embla__container {
    display: flex;
  }

  .embla__slide {
    background-color: antiquewhite;
    flex: 0 0 20%;
    min-width: 0;
    height: 216px;
    margin: 6px 10px;
    padding: 10px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;

    @include media-query(tablet) {
      margin: 15px 15px 15px 0;
      flex: 0 0 30%;
    }

    @include media-query(mobile) {
      margin: 15px 15px 15px 0;
      flex: 0 0 56%;
      height: 190px;
    }

    .slide__container {
      text-decoration: none;

      .embla__slide__category-title {
        color: $white;
        font-size: 24px;
        font-family: "Proxima-Nova-Medium" !important;
      }
    }
  }
}

#social-media-share {
  @include media-query(mobile) {
    margin-top: 20px;
  }

  background-color: $lightBlue2;
  padding: 15px;
  margin-bottom: 20px;

  .title {
    text-align: center;
    font-size: 20px;
    font-family: "Proxima-Nova-Semibold" !important;
    color: $bankingColor;
    margin: 10px 0;
  }

  div {
    display: flex;

    .btn {
      margin: auto;
      padding: 10px;
      svg {
        margin: 0 0 0 0 !important;
      }
    }
  }
}
#blog__categories__slider {
  margin-top: 20px;
}
.categories-list {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  .category-2 {
    flex: 0 0 13%;
    min-width: max-content;
  }
  .category-2 a {
    text-align: center;
    display: block;
    text-decoration: none;
    font-size: 20px;
    margin-right: 10px;
    color: $grey;
    border: 1px solid $grey;
    border-radius: 18px;
    padding: 8px 20px;
    margin-bottom: 5px;
  }
  .category a {
    display: block;
    text-decoration: none;
    font-size: 14px;
    margin-right: 10px;
    color: $grey;
    border: 1px solid $grey;
    border-radius: 18px;
    padding: 4px 10px;
    margin-bottom: 5px;
    &.light {
      border: 1px solid $white;
      color: $white;
    }
  }
}
#one-blog-details {
  .categories-path {
    padding: 15px 0;
    display: flex;
    align-items: center;

    svg {
      // margin-right: 6px;
      transform: scale(0.5);

      path {
        fill: $grey;
      }
    }

    .link {
      font-size: 18px;
      color: $grey;

      // margin-right: 6px;
      &:last-child {
        color: $bankingColor;
      }
    }
  }

  .blog-content {
    padding: 15px;
    @include dropShadow();
    p {
      font-size: 18px;
    }
    p,
    ul,
    div,
    li {
      background: transparent !important;
      font-size: 18px !important;
    }
    figure {
      margin: 10px 0;

      img {
      }
    }
    img {
    }
    table {
      width: -webkit-fill-available !important;
    }
    .summary {
      margin: 15px 0;

      .dropdown-header {
        display: flex;
        align-items: center;
      }

      svg {
        path {
          fill: $blue;
        }
      }
    }
  }

  .date {
    font-size: 16px;
    display: flex;
    align-items: center;

    svg {
      margin-right: 10px;

      path {
        fill: $white;
      }
    }
  }

  .sidebar {
    .last-blog {
      margin-bottom: 10px;

      .btn-ghost.white {
        margin-top: 10px;
        padding: 4px 0;
        width: 100%;
      }
    }
  }
}
.btn-filled-yellow {
  fill: $yellow !important;
}
#blog-page-details {
  background-color: white !important;
  #blog-header {
    padding-top: 10%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    padding-bottom: 15px;
    background-color: $lightBlue2;
    .heading-h1 {
      font-size: 2.75rem !important;
      margin-top: 5px !important;
    }

    .css-o1xwpy-MuiGrid-root > .MuiGrid-item {
      padding-left: 7% !important;
    }
    @include media-query(mobile) {
      .css-zow5z4-MuiGrid-root > .MuiGrid-item {
        padding-top: 0 !important;
      }
      align-items: center;
      padding-top: 20%;
      .heading-h1 {
        font-size: 1.75rem !important;
      }
    }

    .MuiGrid-container {
      align-items: center;
    }
  }

  .blog-content {
    // padding: 15px;
    p,
    span {
      font-size: 18px !important;
      @include media-query(mobile) {
        font-size: 17px !important;
      }
    }

    h2 {
      font-size: 26px !important;
      color: $blue;
    }
    h3 {
      font-size: 22px !important;
      color: rgb(73, 69, 69);
    }
    h4 {
      font-size: 20;
      color: rgb(112, 108, 108);
    }

    ul {
      list-style: none;
      padding-left: 1em;

      & > li::before {
        content: "";
        display: inline-block;
        width: 12px;
        height: 12px;
        background-color: $yellow;
        margin-right: 0.5em;
      }
    }

    p,
    ul,
    div,
    span,
    li {
      background: transparent !important;
      font-size: 18px;
    }

    figure {
      width: 100% !important;
      margin: 10px 0;
      height: auto;
      min-width: 300px;
      border: 1px solid #699bd4;
      background-color: transparent;

      img {
        width: -webkit-fill-available !important;
        height: auto !important;
        padding: 10px;
      }
    }

    img {
      width: -webkit-fill-available !important;
      height: auto;
    }

    table {
      width: -webkit-fill-available !important;
    }

    .summary {
      margin: 15px 0;

      .dropdown-header {
        display: flex;
        align-items: center;
      }

      svg {
        path {
          fill: $blue;
        }
      }
    }
    button {
      display: inline-block;
      background: linear-gradient(to right, #5ce6e6, #0052cc);
      color: white;
      padding: 17px 24px;
      border-radius: 999px;
      text-decoration: none;
      font-family: Arial, sans-serif;
      font-size: 14px;
      font-weight: bold;
      transition: background 0.3s ease;
      border: none;
    }

    button:hover {
      opacity: 0.9;
    }
  }

  .categories-list {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    margin-top: 20px;

    .category-2 {
      flex: 0 0 13%;
      min-width: max-content;
    }

    .category-2 a {
      text-align: center;
      display: block;
      text-decoration: none;
      font-size: 20px;
      margin-right: 10px;
      color: $blue;
      border: 1px solid $blue;
      border-radius: 18px;
      padding: 8px 20px;
      margin-bottom: 5px;
    }

    .category a {
      display: block;
      text-decoration: none;
      font-size: 14px;
      margin-right: 10px;
      color: $blue;
      border: 1px solid $blue;
      border-radius: 18px;
      padding: 4px 10px;
      margin-bottom: 5px;

      &.light {
        border: 1px solid $white;
        color: $white;
      }
    }
  }

  .date {
    font-size: 16px;
    display: flex;
    align-items: center;
    color: $blue;
    font-weight: 500;

    svg {
      margin-right: 10px;

      path {
        fill: $blue;
      }

      &:last-child {
        margin-left: 20px;
      }
    }
  }

  .blog-img {
    background-color: $blue;
    max-width: 90%;
    height: auto;
    max-height: 80%;
    margin-bottom: 20px;

    // margin-right: 16px;
    img {
      height: auto;
      width: 100%;
      margin: auto;
      margin-left: 20px;
      margin-bottom: -20px;
      margin-top: 20px;
    }
  }

  .last-word {
    background-color: $yellow;
  }

  .categories-path {
    padding: 15px 0;
    display: flex;
    align-items: center;

    svg {
      transform: scale(0.5);

      path {
        fill: $grey;
      }
    }

    .link {
      font-size: 18px;
      color: $grey;
      text-decoration: none;

      &:last-child {
        color: $bankingColor;
      }
    }
  }

  .section-related-blog {
    margin-bottom: 20px;

    .last-blog {
      .card {
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            to top,
            $bankingColor,
            $bankingColorOpacity0
          );
          opacity: 0.6;
          pointer-events: none;
        }

        .card-media {
          height: 185px;

          @include media-query(tablet) {
            height: 136px;
          }
        }

        .card-text {
          color: $white;
          position: absolute;
          bottom: 10px;
          left: 10px;
          font-size: 18px;
          padding: 0px 2px;
          font-family: "Proxima-Nova-Semibold" !important;

          button {
            margin: 0;
            padding: 5px 0px;
            justify-content: left;
          }

          a {
            margin: 0;
            padding: 5px 0px;
            justify-content: left;
          }
        }
      }
    }

    .btn-filled.blue {
      width: max-content;
      margin: 15px auto;
    }
  }

  .section {
    background-color: $lightBlue2;
    padding: 15px;
    margin-bottom: 20px;

    .title {
      font-size: 20px;
      font-family: "Proxima-Nova-Semibold" !important;
      color: $bankingColor;
      margin: 10px 0;
    }

    .description {
      color: $bankingColor;
      margin-bottom: 10px;
      font-size: 16px;
    }

    .btn {
      margin-top: 20px;
      width: -webkit-fill-available;
      text-align: center;
      display: flex;
      justify-content: center;
    }
  }

  #sticky-sidebar {
    padding-left: 7%;
    position: sticky;
    top: 110px;
    left: 0;
    width: 100%;
    z-index: 1000;

    max-height: 80vh;
  }

  #content-table {
    &::-webkit-scrollbar {
      border-radius: 8px;

      background: $white;
      width: 12px;
      // height: 50%;
      //   margin-top: 20px;
      // margin-bottom: 20px;
    }

    &::-webkit-scrollbar-thumb {
      background: $yellow;
      border-radius: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      // margin-bottom: 50px;
      // margin-top: 10px;
    }

    padding: 10px;
    max-height: 50vh;
    margin-bottom: 20px;

    @include media-query(mobile) {
      max-height: 65vh;
    }

    // background-color: $blue;
    @extend .gradient-blue;
    overflow: auto;

    ul {
      list-style: none;
      padding-left: 1em;

      li {
        margin-bottom: 10px;

        a {
          color: $white;
          text-decoration: none;
          font-family: "Proxima-Nova-Medium";
          font-size: 16px;
        }
      }

      & > li::before {
        content: "";
        display: inline-block;
        width: 12px;
        height: 12px;
        background-color: $yellow;
        margin-right: 0.5em;
      }
    }
  }
}

#faq-container {
  background-color: #ebece6;
  padding: 32px;
  border-radius: 4px;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.1);
  margin: 24px 0px 12px;

  .faq-title {
    font-weight: 600;
    color: #d6990b;
    font-size: 24px !important;
    margin-bottom: 18px;
  }

  .faq-item {
    border: none;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;

    @include media-query(mobile, tablet) {
      padding: 5px 0px;
    }
  }

  .faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    background-color: transparent;
    cursor: pointer;
    margin-bottom: 12px;
  }

  .faq-icon {
    svg {
      scale: 1.2;
      path {
        fill: #019967;
      }
    }
  }

  .faq-question-text {
    font-weight: 600;
    font-size: 18px;
    color: #019967;
  }

  .faq-number {
    background-color: #019967;
    color: white;
    padding: 8px;
    border-radius: 20px;
    font-size: 14px;
    margin-right: 5px;
  }

  .faq-answer {
    padding: 0px 25px 16px 45px;
    background-color: transparent;
    font-size: 16px;
    color: #36546e;

    @include media-query(mobile, tablet) {
      padding: 0px 0px 16px 0px;
    }
  }
}

#faq-header {
  background: #e4effcb3;
  border-radius: 10px;
  border: none;

  .accordion-faq-title {
    color: $blue;
    font-size: 16px;
    font-family: "Proxima-Nova-Semibold" !important;
  }
}

#faq-header:before {
  background: none;
  border: none;
}
