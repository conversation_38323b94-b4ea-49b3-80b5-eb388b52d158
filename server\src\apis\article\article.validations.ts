import Joi from 'joi';

// FAQ item schema
const faqItemSchema = Joi.object({
    question: Joi.string().required(),
    answer: Joi.string().required(),
});

const articleSchema = Joi.object({
    versions: Joi.array()
        .items(
            Joi.object({
                language: Joi.string().required(),
                title: Joi.string().required(),
                metaTitle: Joi.string().required(),
                metaDescription: Joi.string().required(),
                content: Joi.string().required(),
                // Optional FAQ fields
                faqTitle: Joi.string().allow('').optional(),
                faq: Joi.array().items(faqItemSchema).optional(),
                // Other optional fields that might be sent
                description: Joi.string().allow('').optional(),
                url: Joi.string().optional(),
                alt: Joi.string().allow('').optional(),
                keywords: Joi.array().items(Joi.string()).optional(),
                highlights: Joi.array().items(Joi.string()).optional(),
                category: Joi.array().optional(),
                visibility: Joi.string().optional(),
                publishDate: Joi.date().optional(),
                image: Joi.string().optional(),
                shareOnSocialMedia: Joi.boolean().optional(),
                canonical: Joi.string().allow('').optional(),
                isArchived: Joi.boolean().optional(),
                createdBy: Joi.string().optional(),
            }),
        )
        .required(),
    // Optional root level fields
    robotsMeta: Joi.string().optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    createdBy: Joi.string().optional(),
});

export { articleSchema };
