import { Schema, model } from 'mongoose';
import { FavouriteType } from '@/utils/helpers/constants';

const favouriteSchema = new Schema(
    {
        user: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true,
        },
        favouriteType: {
            type: String,
            enum: FavouriteType,
            required: true,
        },
        favouriteId: {
            type: Schema.Types.ObjectId,
            required: true,
        },
    },
    {
        timestamps: true,
    },
);
 
export default model('Favourite', favouriteSchema);
