import { memo } from "react";

const GlossaryLoading = memo(function GlossaryLoading() {
  const loadingStyles = {
    glossaryLoading: {
      padding: "20px",
      maxWidth: "1200px",
      margin: "0 auto",
    },
    loadingHeader: {
      marginBottom: "30px",
    },
    loadingBreadcrumb: {
      display: "flex",
      gap: "10px",
      alignItems: "center",
      marginBottom: "20px",
    },
    loadingContent: {
      maxWidth: "800px",
    },
    loadingSkeleton: {
      background:
        "linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)",
      backgroundSize: "200% 100%",
      animation: "loading 1.5s infinite",
      borderRadius: "4px",
      marginBottom: "12px",
    },
    loadingTextSm: {
      height: "16px",
      width: "80px",
    },
    loadingTitle: {
      height: "32px",
      width: "60%",
      marginBottom: "20px",
    },
    loadingText: {
      height: "16px",
      width: "100%",
    },
    loadingTextShort: {
      height: "16px",
      width: "70%",
    },
  };

  return (
    <>
      <style>
        {`
          @keyframes loading {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }
        `}
      </style>
      <div style={loadingStyles.glossaryLoading}>
        <div style={loadingStyles.loadingHeader}>
          <div style={loadingStyles.loadingBreadcrumb}>
            <div
              style={{
                ...loadingStyles.loadingSkeleton,
                ...loadingStyles.loadingTextSm,
              }}
            ></div>
            <div
              style={{
                ...loadingStyles.loadingSkeleton,
                ...loadingStyles.loadingTextSm,
              }}
            ></div>
          </div>
        </div>
        <div style={loadingStyles.loadingContent}>
          <div
            style={{
              ...loadingStyles.loadingSkeleton,
              ...loadingStyles.loadingTitle,
            }}
          ></div>
          <div
            style={{
              ...loadingStyles.loadingSkeleton,
              ...loadingStyles.loadingText,
            }}
          ></div>
          <div
            style={{
              ...loadingStyles.loadingSkeleton,
              ...loadingStyles.loadingText,
            }}
          ></div>
          <div
            style={{
              ...loadingStyles.loadingSkeleton,
              ...loadingStyles.loadingTextShort,
            }}
          ></div>
        </div>
      </div>
    </>
  );
});

export default GlossaryLoading;
