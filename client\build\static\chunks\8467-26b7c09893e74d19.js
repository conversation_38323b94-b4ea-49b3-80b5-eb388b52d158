"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8467],{48467:function(e,t,n){n.d(t,{Z:function(){return eM}});var r,o,i,a,s,f=n(39963),c=n(2265),p=n(23947),l=n(3450),u=n(72786);function d(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function m(e){var t=d(e).Element;return e instanceof t||e instanceof Element}function h(e){var t=d(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function v(e){if("undefined"==typeof ShadowRoot)return!1;var t=d(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}var y=Math.max,g=Math.min,b=Math.round;function w(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function x(){return!/^((?!chrome|android).)*safari/i.test(w())}function O(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),o=1,i=1;t&&h(e)&&(o=e.offsetWidth>0&&b(r.width)/e.offsetWidth||1,i=e.offsetHeight>0&&b(r.height)/e.offsetHeight||1);var a=(m(e)?d(e):window).visualViewport,s=!x()&&n,f=(r.left+(s&&a?a.offsetLeft:0))/o,c=(r.top+(s&&a?a.offsetTop:0))/i,p=r.width/o,l=r.height/i;return{width:p,height:l,top:c,right:f+p,bottom:c+l,left:f,x:f,y:c}}function j(e){var t=d(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function E(e){return e?(e.nodeName||"").toLowerCase():null}function P(e){return((m(e)?e.ownerDocument:e.document)||window.document).documentElement}function D(e){return O(P(e)).left+j(e).scrollLeft}function A(e){return d(e).getComputedStyle(e)}function R(e){var t=A(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function k(e){var t=O(e),n=e.offsetWidth,r=e.offsetHeight;return 1>=Math.abs(t.width-n)&&(n=t.width),1>=Math.abs(t.height-r)&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function M(e){return"html"===E(e)?e:e.assignedSlot||e.parentNode||(v(e)?e.host:null)||P(e)}function L(e,t){void 0===t&&(t=[]);var n,r=function e(t){return["html","body","#document"].indexOf(E(t))>=0?t.ownerDocument.body:h(t)&&R(t)?t:e(M(t))}(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),i=d(r),a=o?[i].concat(i.visualViewport||[],R(r)?r:[]):r,s=t.concat(a);return o?s:s.concat(L(M(a)))}function W(e){return h(e)&&"fixed"!==A(e).position?e.offsetParent:null}function B(e){for(var t=d(e),n=W(e);n&&["table","td","th"].indexOf(E(n))>=0&&"static"===A(n).position;)n=W(n);return n&&("html"===E(n)||"body"===E(n)&&"static"===A(n).position)?t:n||function(e){var t=/firefox/i.test(w());if(/Trident/i.test(w())&&h(e)&&"fixed"===A(e).position)return null;var n=M(e);for(v(n)&&(n=n.host);h(n)&&0>["html","body"].indexOf(E(n));){var r=A(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}var T="bottom",H="right",S="left",Z="auto",C=["top",T,H,S],V="start",q="viewport",N="popper",_=C.reduce(function(e,t){return e.concat([t+"-"+V,t+"-end"])},[]),I=[].concat(C,[Z]).reduce(function(e,t){return e.concat([t,t+"-"+V,t+"-end"])},[]),U=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"],F={placement:"bottom",modifiers:[],strategy:"absolute"};function z(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}var X={passive:!0};function Y(e){return e.split("-")[0]}function G(e){return e.split("-")[1]}function J(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function K(e){var t,n=e.reference,r=e.element,o=e.placement,i=o?Y(o):null,a=o?G(o):null,s=n.x+n.width/2-r.width/2,f=n.y+n.height/2-r.height/2;switch(i){case"top":t={x:s,y:n.y-r.height};break;case T:t={x:s,y:n.y+n.height};break;case H:t={x:n.x+n.width,y:f};break;case S:t={x:n.x-r.width,y:f};break;default:t={x:n.x,y:n.y}}var c=i?J(i):null;if(null!=c){var p="y"===c?"height":"width";switch(a){case V:t[c]=t[c]-(n[p]/2-r[p]/2);break;case"end":t[c]=t[c]+(n[p]/2-r[p]/2)}}return t}var Q={top:"auto",right:"auto",bottom:"auto",left:"auto"};function $(e){var t,n,r,o,i,a,s,f=e.popper,c=e.popperRect,p=e.placement,l=e.variation,u=e.offsets,m=e.position,h=e.gpuAcceleration,v=e.adaptive,y=e.roundOffsets,g=e.isFixed,w=u.x,x=void 0===w?0:w,O=u.y,j=void 0===O?0:O,E="function"==typeof y?y({x:x,y:j}):{x:x,y:j};x=E.x,j=E.y;var D=u.hasOwnProperty("x"),R=u.hasOwnProperty("y"),k=S,M="top",L=window;if(v){var W=B(f),Z="clientHeight",C="clientWidth";W===d(f)&&"static"!==A(W=P(f)).position&&"absolute"===m&&(Z="scrollHeight",C="scrollWidth"),("top"===p||(p===S||p===H)&&"end"===l)&&(M=T,j-=(g&&W===L&&L.visualViewport?L.visualViewport.height:W[Z])-c.height,j*=h?1:-1),(p===S||("top"===p||p===T)&&"end"===l)&&(k=H,x-=(g&&W===L&&L.visualViewport?L.visualViewport.width:W[C])-c.width,x*=h?1:-1)}var V=Object.assign({position:m},v&&Q),q=!0===y?(t={x:x,y:j},n=d(f),r=t.x,o=t.y,{x:b(r*(i=n.devicePixelRatio||1))/i||0,y:b(o*i)/i||0}):{x:x,y:j};return(x=q.x,j=q.y,h)?Object.assign({},V,((s={})[M]=R?"0":"",s[k]=D?"0":"",s.transform=1>=(L.devicePixelRatio||1)?"translate("+x+"px, "+j+"px)":"translate3d("+x+"px, "+j+"px, 0)",s)):Object.assign({},V,((a={})[M]=R?j+"px":"",a[k]=D?x+"px":"",a.transform="",a))}var ee={left:"right",right:"left",bottom:"top",top:"bottom"};function et(e){return e.replace(/left|right|bottom|top/g,function(e){return ee[e]})}var en={start:"end",end:"start"};function er(e){return e.replace(/start|end/g,function(e){return en[e]})}function eo(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&v(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function ei(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ea(e,t,n){var r,o,i,a,s,f,c,p,l,u;return t===q?ei(function(e,t){var n=d(e),r=P(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,s=0,f=0;if(o){i=o.width,a=o.height;var c=x();(c||!c&&"fixed"===t)&&(s=o.offsetLeft,f=o.offsetTop)}return{width:i,height:a,x:s+D(e),y:f}}(e,n)):m(t)?((r=O(t,!1,"fixed"===n)).top=r.top+t.clientTop,r.left=r.left+t.clientLeft,r.bottom=r.top+t.clientHeight,r.right=r.left+t.clientWidth,r.width=t.clientWidth,r.height=t.clientHeight,r.x=r.left,r.y=r.top,r):ei((o=P(e),a=P(o),s=j(o),f=null==(i=o.ownerDocument)?void 0:i.body,c=y(a.scrollWidth,a.clientWidth,f?f.scrollWidth:0,f?f.clientWidth:0),p=y(a.scrollHeight,a.clientHeight,f?f.scrollHeight:0,f?f.clientHeight:0),l=-s.scrollLeft+D(o),u=-s.scrollTop,"rtl"===A(f||a).direction&&(l+=y(a.clientWidth,f?f.clientWidth:0)-c),{width:c,height:p,x:l,y:u}))}function es(){return{top:0,right:0,bottom:0,left:0}}function ef(e){return Object.assign({},es(),e)}function ec(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}function ep(e,t){void 0===t&&(t={});var n,r,o,i,a,s,f,c,p=t,l=p.placement,u=void 0===l?e.placement:l,d=p.strategy,v=void 0===d?e.strategy:d,b=p.boundary,w=p.rootBoundary,x=p.elementContext,j=void 0===x?N:x,D=p.altBoundary,R=p.padding,k=void 0===R?0:R,W=ef("number"!=typeof k?k:ec(k,C)),S=e.rects.popper,Z=e.elements[void 0!==D&&D?j===N?"reference":N:j],V=(n=m(Z)?Z:Z.contextElement||P(e.elements.popper),r=void 0===b?"clippingParents":b,o=void 0===w?q:w,f=(s=[].concat("clippingParents"===r?(i=L(M(n)),m(a=["absolute","fixed"].indexOf(A(n).position)>=0&&h(n)?B(n):n)?i.filter(function(e){return m(e)&&eo(e,a)&&"body"!==E(e)}):[]):[].concat(r),[o]))[0],(c=s.reduce(function(e,t){var r=ea(n,t,v);return e.top=y(r.top,e.top),e.right=g(r.right,e.right),e.bottom=g(r.bottom,e.bottom),e.left=y(r.left,e.left),e},ea(n,f,v))).width=c.right-c.left,c.height=c.bottom-c.top,c.x=c.left,c.y=c.top,c),_=O(e.elements.reference),I=K({reference:_,element:S,strategy:"absolute",placement:u}),U=ei(Object.assign({},S,I)),F=j===N?U:_,z={top:V.top-F.top+W.top,bottom:F.bottom-V.bottom+W.bottom,left:V.left-F.left+W.left,right:F.right-V.right+W.right},X=e.modifiersData.offset;if(j===N&&X){var Y=X[u];Object.keys(z).forEach(function(e){var t=[H,T].indexOf(e)>=0?1:-1,n=["top",T].indexOf(e)>=0?"y":"x";z[e]+=Y[n]*t})}return z}function el(e,t,n){return y(e,g(t,n))}function eu(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function ed(e){return["top",H,T,S].some(function(t){return e[t]>=0})}var em=(i=void 0===(o=(r={defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,i=void 0===o||o,a=r.resize,s=void 0===a||a,f=d(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&c.forEach(function(e){e.addEventListener("scroll",n.update,X)}),s&&f.addEventListener("resize",n.update,X),function(){i&&c.forEach(function(e){e.removeEventListener("scroll",n.update,X)}),s&&f.removeEventListener("resize",n.update,X)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=K({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=n.adaptive,i=n.roundOffsets,a=void 0===i||i,s={placement:Y(t.placement),variation:G(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===r||r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,$(Object.assign({},s,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===o||o,roundOffsets:a})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,$(Object.assign({},s,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];h(o)&&E(o)&&(Object.assign(o.style,n),Object.keys(r).forEach(function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],o=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});h(r)&&E(r)&&(Object.assign(r.style,i),Object.keys(o).forEach(function(e){r.removeAttribute(e)}))})}},requires:["computeStyles"]},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,i=void 0===o?[0,0]:o,a=I.reduce(function(e,n){var r,o,a,s,f,c;return e[n]=(r=t.rects,a=[S,"top"].indexOf(o=Y(n))>=0?-1:1,f=(s="function"==typeof i?i(Object.assign({},r,{placement:n})):i)[0],c=s[1],f=f||0,c=(c||0)*a,[S,H].indexOf(o)>=0?{x:c,y:f}:{x:f,y:c}),e},{}),s=a[t.placement],f=s.x,c=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=f,t.modifiersData.popperOffsets.y+=c),t.modifiersData[r]=a}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,i=void 0===o||o,a=n.altAxis,s=void 0===a||a,f=n.fallbackPlacements,c=n.padding,p=n.boundary,l=n.rootBoundary,u=n.altBoundary,d=n.flipVariations,m=void 0===d||d,h=n.allowedAutoPlacements,v=t.options.placement,y=Y(v)===v,g=f||(y||!m?[et(v)]:function(e){if(Y(e)===Z)return[];var t=et(e);return[er(e),t,er(t)]}(v)),b=[v].concat(g).reduce(function(e,n){var r,o,i,a,s,f,u,d,v,y,g,b;return e.concat(Y(n)===Z?(o=(r={placement:n,boundary:p,rootBoundary:l,padding:c,flipVariations:m,allowedAutoPlacements:h}).placement,i=r.boundary,a=r.rootBoundary,s=r.padding,f=r.flipVariations,d=void 0===(u=r.allowedAutoPlacements)?I:u,0===(g=(y=(v=G(o))?f?_:_.filter(function(e){return G(e)===v}):C).filter(function(e){return d.indexOf(e)>=0})).length&&(g=y),Object.keys(b=g.reduce(function(e,n){return e[n]=ep(t,{placement:n,boundary:i,rootBoundary:a,padding:s})[Y(n)],e},{})).sort(function(e,t){return b[e]-b[t]})):n)},[]),w=t.rects.reference,x=t.rects.popper,O=new Map,j=!0,E=b[0],P=0;P<b.length;P++){var D=b[P],A=Y(D),R=G(D)===V,k=["top",T].indexOf(A)>=0,M=k?"width":"height",L=ep(t,{placement:D,boundary:p,rootBoundary:l,altBoundary:u,padding:c}),W=k?R?H:S:R?T:"top";w[M]>x[M]&&(W=et(W));var B=et(W),q=[];if(i&&q.push(L[A]<=0),s&&q.push(L[W]<=0,L[B]<=0),q.every(function(e){return e})){E=D,j=!1;break}O.set(D,q)}if(j)for(var N=m?3:1,U=function(e){var t=b.find(function(t){var n=O.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return E=t,"break"},F=N;F>0&&"break"!==U(F);F--);t.placement!==E&&(t.modifiersData[r]._skip=!0,t.placement=E,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,i=n.altAxis,a=n.boundary,s=n.rootBoundary,f=n.altBoundary,c=n.padding,p=n.tether,l=void 0===p||p,u=n.tetherOffset,d=void 0===u?0:u,m=ep(t,{boundary:a,rootBoundary:s,padding:c,altBoundary:f}),h=Y(t.placement),v=G(t.placement),b=!v,w=J(h),x="x"===w?"y":"x",O=t.modifiersData.popperOffsets,j=t.rects.reference,E=t.rects.popper,P="function"==typeof d?d(Object.assign({},t.rects,{placement:t.placement})):d,D="number"==typeof P?{mainAxis:P,altAxis:P}:Object.assign({mainAxis:0,altAxis:0},P),A=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,R={x:0,y:0};if(O){if(void 0===o||o){var M,L="y"===w?"top":S,W="y"===w?T:H,Z="y"===w?"height":"width",C=O[w],q=C+m[L],N=C-m[W],_=l?-E[Z]/2:0,I=v===V?j[Z]:E[Z],U=v===V?-E[Z]:-j[Z],F=t.elements.arrow,z=l&&F?k(F):{width:0,height:0},X=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:es(),K=X[L],Q=X[W],$=el(0,j[Z],z[Z]),ee=b?j[Z]/2-_-$-K-D.mainAxis:I-$-K-D.mainAxis,et=b?-j[Z]/2+_+$+Q+D.mainAxis:U+$+Q+D.mainAxis,en=t.elements.arrow&&B(t.elements.arrow),er=en?"y"===w?en.clientTop||0:en.clientLeft||0:0,eo=null!=(M=null==A?void 0:A[w])?M:0,ei=el(l?g(q,C+ee-eo-er):q,C,l?y(N,C+et-eo):N);O[w]=ei,R[w]=ei-C}if(void 0!==i&&i){var ea,ef,ec="x"===w?"top":S,eu="x"===w?T:H,ed=O[x],em="y"===x?"height":"width",eh=ed+m[ec],ev=ed-m[eu],ey=-1!==["top",S].indexOf(h),eg=null!=(ef=null==A?void 0:A[x])?ef:0,eb=ey?eh:ed-j[em]-E[em]-eg+D.altAxis,ew=ey?ed+j[em]+E[em]-eg-D.altAxis:ev,ex=l&&ey?(ea=el(eb,ed,ew))>ew?ew:ea:el(l?eb:eh,ed,l?ew:ev);O[x]=ex,R[x]=ex-ed}t.modifiersData[r]=R}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n,r=e.state,o=e.name,i=e.options,a=r.elements.arrow,s=r.modifiersData.popperOffsets,f=Y(r.placement),c=J(f),p=[S,H].indexOf(f)>=0?"height":"width";if(a&&s){var l=ef("number"!=typeof(t="function"==typeof(t=i.padding)?t(Object.assign({},r.rects,{placement:r.placement})):t)?t:ec(t,C)),u=k(a),d="y"===c?"top":S,m="y"===c?T:H,h=r.rects.reference[p]+r.rects.reference[c]-s[c]-r.rects.popper[p],v=s[c]-r.rects.reference[c],y=B(a),g=y?"y"===c?y.clientHeight||0:y.clientWidth||0:0,b=l[d],w=g-u[p]-l[m],x=g/2-u[p]/2+(h/2-v/2),O=el(b,x,w);r.modifiersData[o]=((n={})[c]=O,n.centerOffset=O-x,n)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&eo(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=ep(t,{elementContext:"reference"}),s=ep(t,{altBoundary:!0}),f=eu(a,r),c=eu(s,o,i),p=ed(f),l=ed(c);t.modifiersData[n]={referenceClippingOffsets:f,popperEscapeOffsets:c,isReferenceHidden:p,hasPopperEscaped:l},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":l})}}]}).defaultModifiers)?[]:o,s=void 0===(a=r.defaultOptions)?F:a,function(e,t,n){void 0===n&&(n=s);var r,o,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},F,s),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},f=[],c=!1,p={state:a,setOptions:function(n){var r,o,c,u,d,h="function"==typeof n?n(a.options):n;l(),a.options=Object.assign({},s,a.options,h),a.scrollParents={reference:m(e)?L(e):e.contextElement?L(e.contextElement):[],popper:L(t)};var v=(o=Object.keys(r=[].concat(i,a.options.modifiers).reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{})).map(function(e){return r[e]}),c=new Map,u=new Set,d=[],o.forEach(function(e){c.set(e.name,e)}),o.forEach(function(e){u.has(e.name)||function e(t){u.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!u.has(t)){var n=c.get(t);n&&e(n)}}),d.push(t)}(e)}),U.reduce(function(e,t){return e.concat(d.filter(function(e){return e.phase===t}))},[]));return a.orderedModifiers=v.filter(function(e){return e.enabled}),a.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=e.effect;if("function"==typeof r){var o=r({state:a,name:t,instance:p,options:void 0===n?{}:n});f.push(o||function(){})}}),p.update()},forceUpdate:function(){if(!c){var e,t,n,r,o,i,s,f,l,u,m,v,y=a.elements,g=y.reference,w=y.popper;if(z(g,w)){a.rects={reference:(t=B(w),n="fixed"===a.options.strategy,r=h(t),f=h(t)&&(i=b((o=t.getBoundingClientRect()).width)/t.offsetWidth||1,s=b(o.height)/t.offsetHeight||1,1!==i||1!==s),l=P(t),u=O(g,f,n),m={scrollLeft:0,scrollTop:0},v={x:0,y:0},(r||!r&&!n)&&(("body"!==E(t)||R(l))&&(m=(e=t)!==d(e)&&h(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:j(e)),h(t)?(v=O(t,!0),v.x+=t.clientLeft,v.y+=t.clientTop):l&&(v.x=D(l))),{x:u.left+m.scrollLeft-v.x,y:u.top+m.scrollTop-v.y,width:u.width,height:u.height}),popper:k(w)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach(function(e){return a.modifiersData[e.name]=Object.assign({},e.data)});for(var x=0;x<a.orderedModifiers.length;x++){if(!0===a.reset){a.reset=!1,x=-1;continue}var A=a.orderedModifiers[x],M=A.fn,L=A.options,W=void 0===L?{}:L,T=A.name;"function"==typeof M&&(a=M({state:a,options:W,name:T,instance:p})||a)}}}},update:(r=function(){return new Promise(function(e){p.forceUpdate(),e(a)})},function(){return o||(o=new Promise(function(e){Promise.resolve().then(function(){o=void 0,e(r())})})),o}),destroy:function(){l(),c=!0}};if(!z(e,t))return p;function l(){f.forEach(function(e){return e()}),f=[]}return p.setOptions(n).then(function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)}),p}),eh=n(20801),ev=n(15988),ey=n(1866),eg=n(94143),eb=n(50738);function ew(e){return(0,eb.ZP)("MuiPopper",e)}(0,eg.Z)("MuiPopper",["root"]);var ex=n(57437);function eO(e){return"function"==typeof e?e():e}let ej=e=>{let{classes:t}=e;return(0,eh.Z)({root:["root"]},ew,t)},eE={},eP=c.forwardRef(function(e,t){let{anchorEl:n,children:r,direction:o,disablePortal:i,modifiers:a,open:s,placement:f,popperOptions:u,popperRef:d,slotProps:m={},slots:h={},TransitionProps:v,ownerState:y,...g}=e,b=c.useRef(null),w=(0,p.Z)(b,t),x=c.useRef(null),O=(0,p.Z)(x,d),j=c.useRef(O);(0,l.Z)(()=>{j.current=O},[O]),c.useImperativeHandle(d,()=>x.current,[]);let E=function(e,t){if("ltr"===t)return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}(f,o),[P,D]=c.useState(E),[A,R]=c.useState(eO(n));c.useEffect(()=>{x.current&&x.current.forceUpdate()}),c.useEffect(()=>{n&&R(eO(n))},[n]),(0,l.Z)(()=>{if(!A||!s)return;let e=e=>{D(e.placement)},t=[{name:"preventOverflow",options:{altBoundary:i}},{name:"flip",options:{altBoundary:i}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:t=>{let{state:n}=t;e(n)}}];null!=a&&(t=t.concat(a)),u&&null!=u.modifiers&&(t=t.concat(u.modifiers));let n=em(A,b.current,{placement:E,...u,modifiers:t});return j.current(n),()=>{n.destroy(),j.current(null)}},[A,i,a,s,u,E]);let k={placement:P};null!==v&&(k.TransitionProps=v);let M=ej(e),L=h.root??"div",W=(0,ev.Z)({elementType:L,externalSlotProps:m.root,externalForwardedProps:g,additionalProps:{role:"tooltip",ref:w},ownerState:e,className:M.root});return(0,ex.jsx)(L,{...W,children:"function"==typeof r?r(k):r})}),eD=c.forwardRef(function(e,t){let n;let{anchorEl:r,children:o,container:i,direction:a="ltr",disablePortal:s=!1,keepMounted:f=!1,modifiers:p,open:l,placement:d="bottom",popperOptions:m=eE,popperRef:h,style:v,transition:y=!1,slotProps:g={},slots:b={},...w}=e,[x,O]=c.useState(!0);if(!f&&!l&&(!y||x))return null;if(i)n=i;else if(r){let e=eO(r);n=e&&void 0!==e.nodeType?(0,u.Z)(e).body:(0,u.Z)(null).body}let j=!l&&f&&(!y||x)?"none":void 0,E=y?{in:l,onEnter:()=>{O(!1)},onExited:()=>{O(!0)}}:void 0;return(0,ex.jsx)(ey.Z,{disablePortal:s,container:n,children:(0,ex.jsx)(eP,{anchorEl:r,direction:a,disablePortal:s,modifiers:p,ref:t,open:y?!x:l,placement:d,popperOptions:m,popperRef:h,slotProps:g,slots:b,...w,style:{position:"fixed",top:0,left:0,display:j,...v},TransitionProps:E,children:o})})});var eA=n(16210),eR=n(37053);let ek=(0,eA.ZP)(eD,{name:"MuiPopper",slot:"Root",overridesResolver:(e,t)=>t.root})({});var eM=c.forwardRef(function(e,t){let n=(0,f.V)(),{anchorEl:r,component:o,components:i,componentsProps:a,container:s,disablePortal:c,keepMounted:p,modifiers:l,open:u,placement:d,popperOptions:m,popperRef:h,transition:v,slots:y,slotProps:g,...b}=(0,eR.i)({props:e,name:"MuiPopper"}),w=y?.root??i?.Root,x={anchorEl:r,container:s,disablePortal:c,keepMounted:p,modifiers:l,open:u,placement:d,popperOptions:m,popperRef:h,transition:v,...b};return(0,ex.jsx)(ek,{as:o,direction:n?"rtl":"ltr",slots:{root:w},slotProps:g??a,...x,ref:t})})}}]);