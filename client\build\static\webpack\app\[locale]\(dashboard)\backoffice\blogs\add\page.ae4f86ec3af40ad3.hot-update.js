"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,Description,PictureAsPdf!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/PictureAsPdf.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,Description,PictureAsPdf!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,Description,PictureAsPdf!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,Description,PictureAsPdf!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Add custom styles for drag state\nconst dragActiveStyles = `\n  .file-labels.drag-active {\n    border-color: #1976d2 !important;\n    background-color: rgba(25, 118, 210, 0.04) !important;\n  }\n  .file-labels.disabled {\n    cursor: not-allowed !important;\n    opacity: 0.6 !important;\n  }\n`;\n// Inject styles if not already present\nif (typeof document !== \"undefined\" && !document.getElementById(\"document-importer-styles\")) {\n    const styleSheet = document.createElement(\"style\");\n    styleSheet.id = \"document-importer-styles\";\n    styleSheet.textContent = dragActiveStyles;\n    document.head.appendChild(styleSheet);\n}\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewOpen, setPreviewOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [extractedData, setExtractedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Supported file types\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes(\"pdf\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            color: \"error\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 74,\n            columnNumber: 42\n        }, undefined);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            color: \"primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 75,\n            columnNumber: 12\n        }, undefined);\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        // Create a temporary DOM element to parse HTML\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        // Extract potential title (first h1, h2, or strong text)\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        // Extract first paragraph as potential description\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        // Extract keywords from headings and strong text\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords: keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement(function(image) {\n                        return image.read(\"base64\").then(function(imageBuffer) {\n                            return {\n                                src: \"data:\" + image.contentType + \";base64,\" + imageBuffer\n                            };\n                        });\n                    })\n                }\n            });\n            setProgress(75);\n            // Clean up the HTML content\n            let cleanContent = result.value.replace(/<p><\\/p>/g, \"\") // Remove empty paragraphs\n            .replace(/\\s+/g, \" \") // Normalize whitespace\n            .trim();\n            // Extract metadata\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata: metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            // Convert plain text to basic HTML\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata: metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            setExtractedData(result);\n            setSuccess(true);\n            // Show preview dialog\n            setPreviewOpen(true);\n        } catch (err) {\n            console.error(\"File processing error:\", err);\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) {\n            processFile(acceptedFiles[0]);\n        }\n    }, []);\n    const { getRootProps, getInputProps, isDragActive, acceptedFiles } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing\n    });\n    const handleApplyContent = ()=>{\n        if (extractedData) {\n            onContentExtracted(extractedData.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(extractedData.metadata);\n            }\n            setPreviewOpen(false);\n            setExtractedData(null);\n        }\n    };\n    const handleClosePreview = ()=>{\n        setPreviewOpen(false);\n        setExtractedData(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        sx: {\n            mb: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                variant: \"h6\",\n                gutterBottom: true,\n                className: \"label-form\",\n                children: [\n                    t(\"createArticle:importDocument\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"upload-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    ...getRootProps(),\n                    className: `file-labels ${isDragActive ? \"drag-active\" : \"\"} ${disabled || isProcessing ? \"disabled\" : \"\"}`,\n                    style: {\n                        opacity: disabled || isProcessing ? 0.6 : 1,\n                        cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ...getInputProps(),\n                            className: \"file-input\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"upload-area\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"icon-pic\",\n                                        style: {\n                                            backgroundImage: `url(\"${_assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"]}\")`,\n                                            backgroundSize: \"cover\",\n                                            backgroundRepeat: \"no-repeat\",\n                                            backgroundPosition: \"center\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-text\",\n                                            children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:importFromDocument\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-description\",\n                                            children: [\n                                                t(\"createArticle:supportedFormats\"),\n                                                \": .docx, .doc, .txt\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            sx: {\n                                                display: \"flex\",\n                                                justifyContent: \"flex-start\",\n                                                gap: 1,\n                                                flexWrap: \"wrap\",\n                                                mt: 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Word (.docx)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Word (.doc)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Text (.txt)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        variant: \"body2\",\n                        gutterBottom: true,\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 317,\n                columnNumber: 9\n            }, undefined),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 323,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                open: previewOpen,\n                onClose: handleClosePreview,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    variant: \"h6\",\n                                    children: t(\"createArticle:previewExtractedContent\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    onClick: handleClosePreview,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        children: extractedData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: [\n                                extractedData.metadata.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    sx: {\n                                        mb: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedTitle\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: extractedData.metadata.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 17\n                                }, undefined),\n                                extractedData.metadata.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    sx: {\n                                        mb: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedDescription\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            variant: \"body2\",\n                                            children: extractedData.metadata.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    variant: \"subtitle2\",\n                                    color: \"primary\",\n                                    sx: {\n                                        mb: 1\n                                    },\n                                    children: [\n                                        t(\"createArticle:extractedContent\"),\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    sx: {\n                                        maxHeight: 400,\n                                        overflow: \"auto\",\n                                        border: \"1px solid\",\n                                        borderColor: \"grey.300\",\n                                        p: 2,\n                                        borderRadius: 1,\n                                        backgroundColor: \"grey.50\"\n                                    },\n                                    dangerouslySetInnerHTML: {\n                                        __html: extractedData.content\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, undefined),\n                                extractedData.warnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    severity: \"warning\",\n                                    sx: {\n                                        mt: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            children: [\n                                                t(\"createArticle:conversionWarnings\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: extractedData.warnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: warning.message\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 358,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                onClick: handleClosePreview,\n                                children: t(\"global:cancel\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 417,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                onClick: handleApplyContent,\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 24\n                                }, void 0),\n                                children: t(\"createArticle:applyContent\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 418,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"e7V5N1Q8+U90zRa31VhbBxq3qFc=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});