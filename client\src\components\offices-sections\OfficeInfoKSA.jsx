"use client";
import { Container, useMediaQuery, useTheme } from "@mui/material";
import SvgUsers from "@/assets/images/icons/yellow/Users.svg";
import Svglanguage from "@/assets/images/icons/yellow/language.svg";
import Svggdp from "@/assets/images/icons/yellow/gdp.svg";
import Svgcurrency from "@/assets/images/icons/yellow/currency.svg";
import SvgcapitalCity from "@/assets/images/icons/yellow/capitalCity.svg";
import Svggross from "@/assets/images/icons/yellow/gross.svg";
/* Mobile */
import SvgUsersMobile from "@/assets/images/icons/yellow/UsersMobile.svg";
import SvglanguageMobile from "@/assets/images/icons/yellow/languageMobile.svg";
import SvggdpMobile from "@/assets/images/icons/yellow/gdpMobile.svg";
import SvgcurrencyMobile from "@/assets/images/icons/yellow/currencyMobile.svg";
import SvgcapitalCityMobile from "@/assets/images/icons/yellow/capitalCityMobile.svg";
import SvggrossMobile from "@/assets/images/icons/yellow/grossMobile.svg";
import { useTranslation } from "react-i18next";

function OfficeInfoKSA() {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Container className="custom-max-width">
      <div id="office-info-ksa">
        <div className="info">
          <div className="icon">
            {isMobile ? <SvgUsersMobile /> : <SvgUsers />}
          </div>
          <div>
            <p className="label sub-heading text-white">
              {t("ksa:officeInfoKSA:title1")}
            </p>
            <p className="value paragraph text-white">
              {t("ksa:officeInfoKSA:description1")}
            </p>
          </div>
        </div>
        <div className="info">
          <div className="icon">
            {isMobile ? <SvglanguageMobile /> : <Svglanguage />}
          </div>
          <div>
            <p className="label sub-heading text-white">
              {t("ksa:officeInfoKSA:title2")}
            </p>
            <p className="value paragraph text-white">
              {t("ksa:officeInfoKSA:description2")}
            </p>
          </div>
        </div>
        <div className="info">
          <div className="icon">
            {isMobile ? <SvggdpMobile /> : <Svglanguage />}
          </div>
          <div>
            <p className="label sub-heading text-white">
              {t("ksa:officeInfoKSA:title3")}
            </p>
            <p className="value paragraph text-white">
              {t("ksa:officeInfoKSA:description3")}{" "}
            </p>
          </div>
        </div>
        <div className="info">
          <div className="icon">
            {isMobile ? <SvgcurrencyMobile /> : <Svgcurrency />}
          </div>
          <div>
            <p className="label sub-heading text-white">
              {t("ksa:officeInfoKSA:title4")}
            </p>
            <p className="value paragraph text-white">
              {t("ksa:officeInfoKSA:description4")}
            </p>
          </div>
        </div>
        <div className="info">
          <div className="icon">
            {isMobile ? <SvgcapitalCityMobile /> : <SvgcapitalCity />}
          </div>
          <div>
            <p className="label sub-heading text-white">
              {t("ksa:officeInfoKSA:title5")}
            </p>
            <p className="value paragraph text-white">
              {t("ksa:officeInfoKSA:description5")}
            </p>
          </div>
        </div>
        <div className="info">
          <div className="icon">
            {isMobile ? <SvggrossMobile /> : <Svggross />}
          </div>
          <div>
            <p className="label sub-heading text-white">
              {t("ksa:officeInfoKSA:title6")}
            </p>
            <p className="value paragraph text-white">
              {t("ksa:officeInfoKSA:description6")}
            </p>
          </div>
        </div>
      </div>
    </Container>
  );
}

export default OfficeInfoKSA;
