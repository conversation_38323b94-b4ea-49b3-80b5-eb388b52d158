{"version": 3, "file": "file-selector.js", "sourceRoot": "", "sources": ["../src/file-selector.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,8BASC;AA7BD,+BAAoD;AAGpD,IAAM,eAAe,GAAG;IACpB,8CAA8C;IAC9C,WAAW,EAAE,QAAQ;IACrB,WAAW,CAAE,UAAU;CAC1B,CAAC;AAGF;;;;;;;;;GASG;AACH,SAAsB,SAAS,CAAC,GAAgB;;;YAC5C,IAAI,QAAQ,CAAY,GAAG,CAAC,IAAI,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC/D,sBAAO,oBAAoB,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,EAAC;YAC5D,CAAC;iBAAM,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,sBAAO,aAAa,CAAC,GAAG,CAAC,EAAC;YAC9B,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,UAAA,IAAI,IAAI,OAAA,SAAS,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,EAAvD,CAAuD,CAAC,EAAE,CAAC;gBAC1G,sBAAO,gBAAgB,CAAC,GAAG,CAAC,EAAA;YAChC,CAAC;YACD,sBAAO,EAAE,EAAC;;;CACb;AAED,SAAS,cAAc,CAAC,KAAU;IAC9B,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED,SAAS,WAAW,CAAC,KAAU;IAC3B,OAAO,QAAQ,CAAQ,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC5D,CAAC;AAED,SAAS,QAAQ,CAAI,CAAM;IACvB,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAA;AAC9C,CAAC;AAED,SAAS,aAAa,CAAC,GAAU;IAC7B,OAAO,QAAQ,CAAgB,GAAG,CAAC,MAA2B,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAA,qBAAc,EAAC,IAAI,CAAC,EAApB,CAAoB,CAAC,CAAC;AAC5G,CAAC;AAED,oGAAoG;AACpG,SAAe,gBAAgB,CAAC,OAAc;;;;;wBAC5B,qBAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,EAAE,EAAX,CAAW,CAAC,CAAC,EAAA;;oBAAxD,KAAK,GAAG,SAAgD;oBAC9D,sBAAO,KAAK,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAA,qBAAc,EAAC,IAAI,CAAC,EAApB,CAAoB,CAAC,EAAC;;;;CAClD;AAGD,SAAe,oBAAoB,CAAC,EAAgB,EAAE,IAAY;;;;;;yBAG1D,EAAE,CAAC,KAAK,EAAR,wBAAQ;oBACF,KAAK,GAAG,QAAQ,CAAmB,EAAE,CAAC,KAAK,CAAC;yBAC7C,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,IAAI,KAAK,MAAM,EAApB,CAAoB,CAAC,CAAC;oBAC1C,0EAA0E;oBAC1E,mEAAmE;oBACnE,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;wBAClB,sBAAO,KAAK,EAAC;oBACjB,CAAC;oBACa,qBAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,EAAA;;oBAApD,KAAK,GAAG,SAA4C;oBAC1D,sBAAO,cAAc,CAAC,OAAO,CAAe,KAAK,CAAC,CAAC,EAAC;wBAGxD,sBAAO,cAAc,CAAC,QAAQ,CAAe,EAAE,CAAC,KAAK,CAAC;yBACjD,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAA,qBAAc,EAAC,IAAI,CAAC,EAApB,CAAoB,CAAC,CAAC,EAAC;;;;CAC3C;AAED,SAAS,cAAc,CAAC,KAAqB;IACzC,OAAO,KAAK,CAAC,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAzC,CAAyC,CAAC,CAAC;AAC3E,CAAC;AAED,qCAAqC;AACrC,oHAAoH;AACpH,4DAA4D;AAC5D,wEAAwE;AACxE,SAAS,QAAQ,CAAI,KAA6C;IAC9D,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;QACjB,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAM,KAAK,GAAG,EAAE,CAAC;IAEjB,gCAAgC;IAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACpC,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC;IAED,OAAO,KAAY,CAAC;AACxB,CAAC;AAED,oEAAoE;AACpE,SAAS,cAAc,CAAC,IAAsB;IAC1C,IAAI,OAAO,IAAI,CAAC,gBAAgB,KAAK,UAAU,EAAE,CAAC;QAC9C,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,IAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAEtC,4FAA4F;IAC5F,uCAAuC;IACvC,gEAAgE;IAChE,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;QAC7B,OAAO,YAAY,CAAC,KAAK,CAAQ,CAAC;IACtC,CAAC;IAED,OAAO,oBAAoB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC7C,CAAC;AAED,SAAS,OAAO,CAAI,KAAY;IAC5B,OAAO,KAAK,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK,IAAK,8CAC7B,GAAG,kBACH,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAFpB,CAGnC,EAAE,EAAE,CAAC,CAAC;AACX,CAAC;AAED,SAAe,oBAAoB,CAAC,IAAsB,EAAE,KAA8B;;;;;;;yBAOlF,CAAA,UAAU,CAAC,eAAe,IAAI,OAAQ,IAAY,CAAC,qBAAqB,KAAK,UAAU,CAAA,EAAvF,wBAAuF;oBAC7E,qBAAO,IAAY,CAAC,qBAAqB,EAAE,EAAA;;oBAA/C,CAAC,GAAG,SAA2C;oBACrD,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;wBACb,MAAM,IAAI,KAAK,CAAC,UAAG,IAAI,mBAAgB,CAAC,CAAC;oBAC7C,CAAC;yBAGG,CAAA,CAAC,KAAK,SAAS,CAAA,EAAf,wBAAe;oBACF,qBAAM,CAAC,CAAC,OAAO,EAAE,EAAA;;oBAAxB,SAAO,SAAiB;oBAC9B,MAAI,CAAC,MAAM,GAAG,CAAC,CAAC;oBAChB,sBAAO,IAAA,qBAAc,EAAC,MAAI,CAAC,EAAC;;oBAG9B,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;oBAC9B,IAAI,CAAC,IAAI,EAAE,CAAC;wBACR,MAAM,IAAI,KAAK,CAAC,UAAG,IAAI,mBAAgB,CAAC,CAAC;oBAC7C,CAAC;oBACK,GAAG,GAAG,IAAA,qBAAc,EAAC,IAAI,EAAE,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,QAAQ,mCAAI,SAAS,CAAC,CAAC;oBAC/D,sBAAO,GAAG,EAAC;;;;CACd;AAED,mEAAmE;AACnE,SAAe,SAAS,CAAC,KAAU;;;YAC/B,sBAAO,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,EAAC;;;CACzE;AAED,4EAA4E;AAC5E,SAAS,YAAY,CAAC,KAAU;IAC5B,IAAM,MAAM,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;IAEpC,OAAO,IAAI,OAAO,CAAc,UAAC,OAAO,EAAE,MAAM;QAC5C,IAAM,OAAO,GAA2B,EAAE,CAAC;QAE3C,SAAS,WAAW;YAApB,iBAsBC;YArBG,yFAAyF;YACzF,yFAAyF;YACzF,MAAM,CAAC,WAAW,CAAC,UAAO,KAAY;;;;;iCAC9B,CAAC,KAAK,CAAC,MAAM,EAAb,wBAAa;;;;4BAGK,qBAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAA;;4BAAlC,KAAK,GAAG,SAA0B;4BACxC,OAAO,CAAC,KAAK,CAAC,CAAC;;;;4BAEf,MAAM,CAAC,KAAG,CAAC,CAAC;;;;4BAGV,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;4BAChD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;4BAEpB,mBAAmB;4BACnB,WAAW,EAAE,CAAC;;;;;iBAErB,EAAE,UAAC,GAAQ;gBACR,MAAM,CAAC,GAAG,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC;QAED,WAAW,EAAE,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC;AAED,uEAAuE;AACvE,SAAe,aAAa,CAAC,KAAU;;;YACnC,sBAAO,IAAI,OAAO,CAAe,UAAC,OAAO,EAAE,MAAM;oBAC7C,KAAK,CAAC,IAAI,CAAC,UAAC,IAAkB;wBAC1B,IAAM,GAAG,GAAG,IAAA,qBAAc,EAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;wBACjD,OAAO,CAAC,GAAG,CAAC,CAAC;oBACjB,CAAC,EAAE,UAAC,GAAQ;wBACR,MAAM,CAAC,GAAG,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,EAAC;;;CACN"}