import { Request, Response, NextFunction } from 'express';
import { auth } from '@/utils/services';
import HttpException from '@/utils/exceptions/http.exception';
import { MESSAGES } from '@/utils/helpers/messages';

const isAuthenticated = async (request: Request, response: Response, next: NextFunction) => {
    if (!request.user) return next(new HttpException(401, MESSAGES.AUTH.UNAUTHORIZED));
    return next();
};

export const persistConnectedUser = async (request: Request, response: Response, next: NextFunction) => {
    const { accessToken, refreshToken } = request.cookies;

    if (!accessToken || !refreshToken) return next();
    const { payload, expired } = await auth.verifyToken(accessToken, String(process.env.ACCESS_TOKEN_PRIVATE_KEY));
    if (payload) {
        request.user = payload;
        return next();
    }
    const { payload: refresh } = expired && refreshToken && (await auth.verifyToken(refreshToken, String(process.env.REFRESH_TOKEN_PRIVATE_KEY)));

    if (refresh) {
        const accessToken = await auth.generateToken(
            { _id: refresh._id, roles: refresh.roles },
            String(process.env.ACCESS_TOKEN_PRIVATE_KEY),
            String(process.env.ACCESS_TOKEN_TIME),
        );

        response.cookie('accessToken', accessToken, {
            maxAge: Number(process.env.COOKIE_MAX_AGE),
            httpOnly: true,
        });

        request.user = (await auth.verifyToken(accessToken, String(process.env.ACCESS_TOKEN_PRIVATE_KEY))).payload as any;
        return next();                                                  
    }
};

export default isAuthenticated;
