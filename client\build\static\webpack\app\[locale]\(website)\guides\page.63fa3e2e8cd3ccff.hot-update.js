"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/guides/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/BlogItem.jsx":
/*!***************************************************!*\
  !*** ./src/features/blog/components/BlogItem.jsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _assets_images_website_blog_img_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/website/blog-img.png */ \"(app-pages-browser)/./src/assets/images/website/blog-img.png\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardMedia,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardMedia,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardMedia,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardMedia/CardMedia.js\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardMedia,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* harmony import */ var html_to_text__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! html-to-text */ \"(app-pages-browser)/./node_modules/html-to-text/lib/html-to-text.mjs\");\n\nvar _s = $RefreshSig$();\n\"use client \";\n\n\n\n\n\n\nfunction BlogItem(param) {\n    let { blogData, language, withoutCategory } = param;\n    _s();\n    // Import helper functions\n    const { getBlogVersion, getBlogUrl, getBlogTitle, getBlogImage, getBlogDescription, getBlogContent } = __webpack_require__(/*! @/utils/blogHelpers */ \"(app-pages-browser)/./src/utils/blogHelpers.js\");\n    const handleClick = (event, href)=>{\n        event.preventDefault();\n        window.location.href = href;\n    };\n    const truncateDescription = (title)=>{\n        title = (0,html_to_text__WEBPACK_IMPORTED_MODULE_5__.htmlToText)(title.replace(/<a[^>]*>|<\\/a>/g, \"\"), {\n            wordwrap: false\n        });\n        const words = title.split(\" \");\n        if (words?.length >= 20) {\n            return words.slice(0, 20).join(\" \");\n        } else {\n            return title;\n        }\n    };\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    // Get version data for the current language\n    const versionData = getBlogVersion(blogData, language);\n    // If no version data is available, don't render the component\n    if (!versionData) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        className: \"blog-item\",\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        sx: {\n            marginBottom: \"10px\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"card\",\n            children: [\n                blogData?.category?.name && !withoutCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    locale: language === \"en\" ? \"en\" : \"fr\",\n                    href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/category/${blogData?.category?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/category/${blogData?.category?.url}`}/`,\n                    onClick: (e)=>handleClick(e, `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/category/${blogData?.category?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/category/${blogData?.category?.url}`}/`),\n                    className: \"label-category\",\n                    children: blogData?.category?.name\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    locale: language === \"en\" ? \"en\" : \"fr\",\n                    href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}`}/`,\n                    onClick: (e)=>handleClick(e, `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${versionData?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${versionData?.url}`}/`),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"card-image\",\n                        component: \"img\",\n                        image: blogData?.versions[0]?.image ? `${\"http://localhost:4000/api/v1\"}/files/${blogData?.versions[0]?.image}` : _assets_images_website_blog_img_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                        alt: blogData?.versions[0]?.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardMedia_Grid_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"card-content\",\n                    sx: {\n                        padding: \"8px !important\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            locale: language === \"en\" ? \"en\" : \"fr\",\n                            href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${versionData?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${versionData?.url}`}/`,\n                            onClick: (e)=>handleClick(e, `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_4__.websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}`}/`),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"blog-title\",\n                                    children: blogData?.versions[0]?.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                \" \"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"blog-description\",\n                            children: blogData?.versions[0]?.description ? blogData?.versions[0]?.description : truncateDescription(blogData?.versions[0]?.content)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this)\n    }, versionData?.title, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\BlogItem.jsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogItem, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = BlogItem;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BlogItem);\nvar _c;\n$RefreshReg$(_c, \"BlogItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/BlogItem.jsx\n"));

/***/ })

});