"use client";
import { <PERSON>rrorMessage, Field } from "formik";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import SunEditor from "suneditor-react";
import { v4 as uuidv4 } from "uuid";

import { Visibility } from "../../../utils/constants";
import "react-datepicker/dist/react-datepicker.css";
import { slug } from "@feelinglovelynow/slug";
import { API_URLS } from "../../../utils/urls";
import upload from "@/assets/images/add.png";
import "suneditor/dist/css/suneditor.min.css";
import { WithContext as ReactTags } from "react-tag-input";
import plugins from "suneditor/src/plugins";

import {
  Autocomplete,
  FormGroup,
  FormLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import dayjs from "dayjs";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { useSaveFile } from "@/features/opportunity/hooks/opportunity.hooks";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
function AddArticleFR({
  errors,
  touched,
  setFieldValue,
  values,
  onImageSelect,
  categories,
  filteredCategories,
  onCategoriesSelect,
  debounce,
}) {
  const handlePaste = (event, cleanData, maxCharCount) => {
    let html = cleanData;

    // Correction des balises non fermées
    html = html.replace(/<strong>(.*?)$/g, "<strong>$1</strong>");

    return html;
  };
  const KeyCodes = {
    comma: 188,
    enter: 13,
  };
  const delimiters = [KeyCodes.comma, KeyCodes.enter];
  const [tags, setTags] = useState([]);
  const [highlights, setHighlights] = useState([]);

  const { user } = useCurrentUser();
  const { t } = useTranslation();
  const [publishNow, setPublishNow] = useState(false);
  const [publishDate, setPublishDate] = useState(new Date());
  const imageInputRef = useRef(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const language = "fr";

  const handlePhotoChange = async () => {
    const selectedFile = imageInputRef.current.files[0];
    setSelectedImage(imageInputRef.current.files[0]);

    if (selectedFile) {
      onImageSelect(selectedFile, language);
    }
  };

  const useSaveFileHook = useSaveFile();

  const handlePhotoBlogChange = async (file, info, core, uploadHandler) => {
    if (file instanceof HTMLImageElement) {
      const src = file.src;

      if (src.startsWith("data:image")) {
        const base64Data = src.split(",")[1];
        const contentType = src.match(/data:(.*?);base64/)[1];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length)
          .fill(0)
          .map((_, i) => byteCharacters.charCodeAt(i));
        const byteArray = new Uint8Array(byteNumbers);

        const blob = new Blob([byteArray], { type: contentType });
        const fileName = `image_${Date.now()}.${contentType.split("/")[1]}`;
        const selectedFile = new File([blob], fileName, { type: contentType });

        await uploadFile(selectedFile, uploadHandler, core, file);
      } else {
        fetch(src)
          .then((response) => response.blob())
          .then((blob) => {
            const contentType = blob.type;
            const fileName = `image_${Date.now()}.${contentType.split("/")[1]}`;
            const selectedFile = new File([blob], fileName, {
              type: contentType,
            });

            uploadFile(selectedFile, uploadHandler, core, file);
          })
          .catch((error) =>
            console.error("Error converting image URL to Blob:", error)
          );
      }
    } else {
      console.error("File is not an HTMLImageElement.");
    }
  };

  const uploadFile = (selectedFile, uploadHandler, core, originalImage) => {
    let uuidPhoto;
    uuidPhoto = uuidv4().replace(/-/g, "");

    const formData = new FormData();
    formData.append("file", selectedFile);

    const extension = selectedFile.name.split(".").pop();
    const currentYear = new Date().getFullYear();

    useSaveFileHook.mutate(
      {
        resource: "blogs",
        folder: currentYear.toString(),
        filename: uuidPhoto,
        body: { formData, t },
      },
      {
        onSuccess: (dataUUID) => {
          const uuidPhotoFileName =
            dataUUID.message === "uuid exist"
              ? dataUUID.uuid
              : `${uuidPhoto}.${extension}`;

          const imageUrl = `${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${uuidPhotoFileName}`;

          originalImage.src = imageUrl;

          uploadHandler({
            result: [
              {
                id: uuidPhotoFileName,
                url: imageUrl,
              },
            ],
          });
        },
        onError: (error) => {
          console.error("Error uploading file:", error);
        },
      }
    );
  };
  const [titlefr, setTitlefr] = useState(() => {
    const savedTitlefr = localStorage.getItem("titlefr");
    return savedTitlefr ? JSON.parse(savedTitlefr) : "";
  });
  const [metatitlefr, setMetatitlefr] = useState(() => {
    const savedMetatitlefr = localStorage.getItem("metatitlefr");
    return savedMetatitlefr ? JSON.parse(savedMetatitlefr) : "";
  });
  const [metaDescriptionfr, setMetaDescriptionfr] = useState(() => {
    const savedMetadescriptionfr = localStorage.getItem("metaDescriptionfr");
    return savedMetadescriptionfr ? JSON.parse(savedMetadescriptionfr) : "";
  });

  const [contentfr, setContentfr] = useState(() => {
    const savedContentfr = localStorage.getItem("contentfr");
    return savedContentfr ? JSON.parse(savedContentfr) : "";
  });

  const handleEditorChange = (newContentfr) => {
    setContentfr(newContentfr);
    setFieldValue("contentFR", newContentfr);
  };
  useEffect(() => {
    if (titlefr) {
      localStorage.setItem("titlefr", JSON.stringify(titlefr));
    }
    if (contentfr) {
      localStorage.setItem("contentfr", JSON.stringify(contentfr));
    }
    if (metatitlefr) {
      localStorage.setItem("metatitlefr", JSON.stringify(metatitlefr));
    }
    if (metaDescriptionfr) {
      localStorage.setItem(
        "metaDescriptionfr",
        JSON.stringify(metaDescriptionfr)
      );
    }
  }, [titlefr, contentfr, metatitlefr, metaDescriptionfr]);

  return (
    <>
      <p className="label-pentabell">Add article French : </p>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:title")}
              <TextField
                variant="standard"
                name="titleFR"
                type="text"
                value={titlefr || values.titleFR}
                onChange={(e) => {
                  const titleFR = e.target.value;
                  setFieldValue("titleFR", titleFR);
                  setTitlefr(titleFR);
                  const url = slug(titleFR);
                  setFieldValue("urlFR", url);
                }}
                className={
                  "input-pentabell" +
                  (errors.titleFR && touched.titleFR ? " is-invalid" : "")
                }
              />{" "}
              <ErrorMessage
                className="label-error"
                name="titleFR"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:categories")}
              <Stack>
                <Autocomplete
                  multiple
                  className="input-pentabell"
                  id="tags-standard"
                  options={
                    filteredCategories.length > 0
                      ? filteredCategories
                      : categories
                  }
                  getOptionLabel={(option) => option.name}
                  selected={
                    values.categoryFR.length > 0
                      ? (filteredCategories.length > 0
                          ? filteredCategories
                          : categories
                        ).filter((category) =>
                          values.categoryFR.includes(category.id)
                        )
                      : []
                  }
                  onChange={(event, selectedOptions) => {
                    const categoryIds = selectedOptions.map(
                      (category) => category.id
                    );
                    setFieldValue("categoryFR", categoryIds);
                    onCategoriesSelect(categoryIds);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      className="input-pentabell  multiple-select"
                      variant="standard"
                    />
                  )}
                />
              </Stack>
            </FormLabel>
          </FormGroup>

          {touched.categoryFR && errors.categoryFR && (
            <div className="error">{errors.categoryFR}</div>
          )}
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              Description
              <TextField
                variant="standard"
                name="descriptionFR"
                type="text"
                multiline
                rows={3}
                value={values.descriptionFR}
                onChange={(e) => {
                  const descriptionFR = e.target.value;
                  setFieldValue("descriptionFR", descriptionFR);
                }}
                className={
                  "textArea-pentabell" +
                  (errors.descriptionFR && touched.descriptionFR
                    ? " is-invalid"
                    : "")
                }
              />{" "}
              <ErrorMessage
                className="label-error"
                name="descriptionFR"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              Highlights
              <div id="tags">
                <ReactTags
                  tags={highlights}
                  className={
                    "input-pentabell" +
                    (errors.highlightsFR && touched.highlightsFR
                      ? " is-invalid"
                      : "")
                  }
                  delimiters={delimiters}
                  handleDelete={(i) => {
                    const updatedTags = highlights.filter(
                      (tag, index) => index !== i
                    );
                    setHighlights(updatedTags);
                    setFieldValue(
                      "highlightsFR",
                      updatedTags.map((tag) => tag.text)
                    );
                  }}
                  handleAddition={(tag) => {
                    setHighlights([...highlights, tag]);
                    setFieldValue(
                      "highlightsFR",
                      [...highlights, tag].map((item) => item.text)
                    );
                  }}
                  inputFieldPosition="bottom"
                  autocomplete
                  allowDragDrop={false}
                />
              </div>
              <ErrorMessage
                className="label-error"
                name="keywordsEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <SunEditor
        setContents={contentfr || values?.contentFR || ""}
        onChange={handleEditorChange}
        onPaste={handlePaste}
        setOptions={{
          cleanHTML: false,
          disableHtmlSanitizer: true,
          addTagsWhitelist:
            "h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button",
          plugins: plugins,
          buttonList: [
            ["undo", "redo"],
            ["font", "fontSize", "formatBlock"],
            [
              "bold",
              "underline",
              "italic",
              "strike",
              "subscript",
              "superscript",
            ],
            ["fontColor", "hiliteColor"],
            ["align", "list", "lineHeight"],
            ["outdent", "indent"],
            ["table", "horizontalRule", "link", "image", "video"],
            ["fullScreen", "showBlocks", "codeView"],
            ["preview", "print"],
            ["removeFormat"],
          ],
          imageUploadHandler: handlePhotoBlogChange,
          defaultTag: "div",
          minHeight: "300px",
          maxHeight: "400px",
          showPathLabel: false,
          font: [
            "Proxima-Nova-Regular",
            "Proxima-Nova-Medium",
            "Proxima-Nova-Semibold",
            "Proxima-Nova-Bold",
            "Proxima-Nova-Extrabold",
            "Proxima-Nova-Black",
            "Proxima-Nova-Light",
            "Proxima-Nova-Thin",
            "Arial",
            "Times New Roman",
            "Sans-Serif",
          ],
          charCounter: true, // Show character counter
          charCounterType: "byte",
          resizingBar: false, // Hide resizing bar for a cleaner UI
          colorList: [
            // Standard Colors
            [
              "#234791",
              "#d69b19",
              "#cc3233",
              "#009966",
              "#0b3051",
              "#2BBFAD",
              "#0b305100",
              "#0a305214",
              "#743794",
              "#ff0000",
              "#ff5e00",
              "#ffe400",
              "#abf200",
              "#00d8ff",
              "#0055ff",
              "#6600ff",
              "#ff00dd",
              "#000000",
              "#ffd8d8",
              "#fae0d4",
              "#faf4c0",
              "#e4f7ba",
              "#d4f4fa",
              "#d9e5ff",
              "#e8d9ff",
              "#ffd9fa",
              "#f1f1f1",
              "#ffa7a7",
              "#ffc19e",
              "#faed7d",
              "#cef279",
              "#b2ebf4",
              "#b2ccff",
              "#d1b2ff",
              "#ffb2f5",
              "#bdbdbd",
              "#f15f5f",
              "#f29661",
              "#e5d85c",
              "#bce55c",
              "#5cd1e5",
              "#6699ff",
              "#a366ff",
              "#f261df",
              "#8c8c8c",
              "#980000",
              "#993800",
              "#998a00",
              "#6b9900",
              "#008299",
              "#003399",
              "#3d0099",
              "#990085",
              "#353535",
              "#670000",
              "#662500",
              "#665c00",
              "#476600",
              "#005766",
              "#002266",
              "#290066",
              "#660058",
              "#222222",
            ], // For box shadow with opacity
          ],
        }}
        onImageUpload={handlePhotoBlogChange}
      />
      <br></br>

      {/* FAQ Section */}
      <FaqSection
        values={values}
        setFieldValue={setFieldValue}
        errors={errors}
        touched={touched}
        language="FR"
        debounce={debounce}
      />
      <br></br>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:metaTitle")} ({" "}
              <span
                className={
                  values.metaTitleFR?.length > 65 ? " text-danger" : ""
                }
              >
                {" "}
                {values.metaTitleFR?.length} / 65{" "}
              </span>{" "}
              )
              <TextField
                variant="standard"
                name="metaTitleFR"
                type="text"
                value={metatitlefr || values.metaTitleFR}
                onChange={(e) => {
                  const metaTitleFR = e.target.value;
                  setFieldValue("metaTitleFR", metaTitleFR);
                  setMetatitlefr(metaTitleFR);
                }}
                className={
                  "input-pentabell" +
                  (errors.metaTitleFR && touched.metaTitleFR
                    ? " is-invalid"
                    : "")
                }
              />{" "}
              <ErrorMessage
                className="label-error"
                name="metaTitleFR"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:url")}
              <TextField
                variant="standard"
                name="urlFR"
                type="text"
                value={values.urlFR}
                onChange={(e) => {
                  setFieldValue("urlFR", e.target.value);
                }}
                className={
                  "input-pentabell" +
                  (errors.urlFR && touched.urlFR ? " is-invalid" : "")
                }
              />{" "}
              <ErrorMessage
                className="label-error"
                name="urlFR"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:metaDescription")} ({" "}
              <span
                className={
                  values.metaDescriptionFR?.length > 160 ? " text-danger" : ""
                }
              >
                {values.metaDescriptionFR?.length} / 160
              </span>{" "}
              )
              <TextField
                variant="standard"
                name="metaDescriptionFR"
                type="text"
                multiline
                rows={2}
                value={metaDescriptionfr || values.metaDescriptionFR}
                onChange={(e) => {
                  const metaDescriptionFR = e.target.value;
                  setFieldValue("metaDescriptionFR", metaDescriptionFR);
                  setMetaDescriptionfr(metaDescriptionFR);
                }}
                className={
                  "textArea-pentabell" +
                  (errors.metaDescriptionFR && touched.metaDescriptionFR
                    ? " is-invalid"
                    : "")
                }
              />{" "}
              <ErrorMessage
                className="label-error"
                name="metaDescriptionFR"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:featuredImage")}

              <div className="upload-container">
                <label htmlFor={`image-upload-fr`} className="file-labels">
                  <input
                    type="file"
                    id={`image-upload-fr`}
                    name="imageEN"
                    accept=".png, .jpg, .jpeg, .webp"
                    ref={imageInputRef}
                    onChange={(e) => {
                      setFieldValue("imageFR", e.target.files[0]);
                      handlePhotoChange();
                    }}
                    className={
                      "file-input" +
                      (errors.imageFR && touched.imageFR ? " is-invalid" : "")
                    }
                  />
                  <div className="upload-area">
                    <div>
                      <div
                        className="icon-pic"
                        style={{
                          backgroundImage: `url("${
                            selectedImage
                              ? URL.createObjectURL(selectedImage)
                              : values.imageFR
                              ? `${process.env.REACT_APP_API_URL}${API_URLS.files}/${values.imageFR}`
                              : upload.src
                          }")`,
                          backgroundSize: "cover",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "center",
                          //backgroundColor: "#40bd3921",
                        }}
                      ></div>
                    </div>
                    <div>
                      <p className="upload-text">
                        {t("createArticle:addFeatImg")}
                      </p>
                      <p className="upload-description">
                        {t("createArticle:clickBox")}
                      </p>
                    </div>
                  </div>
                  <ErrorMessage
                    name="imageFR"
                    component="div"
                    className="invalid-feedback error"
                  />
                </label>
              </div>
            </FormLabel>
          </FormGroup>
        </div>
      </div>

      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:alt")}
              <TextField
                variant="standard"
                name="altFR"
                type="text"
                value={values.altFR}
                onChange={(e) => {
                  setFieldValue("altFR", e.target.value);
                }}
                className={
                  "input-pentabell" +
                  (errors.altFR && touched.altFR ? " is-invalid" : "")
                }
              />{" "}
              <ErrorMessage
                className="label-error"
                name="altFR"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:visibility")}

              <Select
                className="select-pentabell"
                variant="standard"
                value={Visibility.filter(
                  (option) => values.visibilityFR === option
                )}
                selected={values.visibilityFR}
                onChange={(event) => {
                  setFieldValue("visibilityFR", event.target.value);
                }}
              >
                {Visibility.map((item, index) => (
                  <MenuItem key={index} value={item}>
                    {item}
                  </MenuItem>
                ))}
              </Select>
              <ErrorMessage
                className="label-error"
                name="visibilityEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:keyword")}
              <div id="tags">
                <ReactTags
                  tags={tags}
                  className={
                    "input-pentabell" +
                    (errors.keywordsFR && touched.keywordsFR
                      ? " is-invalid"
                      : "")
                  }
                  delimiters={delimiters}
                  handleDelete={(i) => {
                    const updatedTags = tags.filter(
                      (tag, index) => index !== i
                    );
                    setTags(updatedTags);
                    setFieldValue(
                      "keywordsFR",
                      updatedTags.map((tag) => tag.text)
                    );
                  }}
                  handleAddition={(tag) => {
                    setTags([...tags, tag]);
                    setFieldValue(
                      "keywordsFR",
                      [...tags, tag].map((item) => item.text)
                    );
                  }}
                  inputFieldPosition="bottom"
                  autocomplete
                  allowDragDrop={false}
                />
              </div>
              <ErrorMessage
                className="label-error"
                name="keywordsFR"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <label className="label-form">
        <Field
          type="checkbox"
          name="publishNow"
          checked={publishNow}
          onChange={(e) => {
            setPublishNow(e.target.checked);
            if (e.target.checked) {
              setFieldValue("publishDateFR", new Date().toISOString());
            }
          }}
        />
        {t("createArticle:publishNow")}
      </label>

      {!publishNow && (
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:publishDate")}
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DemoContainer components={["DatePicker"]}>
                  <DatePicker
                    variant="standard"
                    className="input-date"
                    format="DD/MM/YYYY"
                    value={dayjs(values.publishDateEN)}
                    onChange={(date) => {
                      setFieldValue(
                        "publishDateEN",
                        dayjs(date).format("YYYY-MM-DD")
                      );
                    }}
                  />{" "}
                  <ErrorMessage
                    className="label-error"
                    name="publishDateEN"
                    component="div"
                  />
                </DemoContainer>
              </LocalizationProvider>
            </FormLabel>
          </FormGroup>
        </div>
      )}

      <Field
        type="hidden"
        name="publishDateFR"
        value={
          publishNow ? new Date().toISOString() : publishDate.toISOString()
        }
      />
    </>
  );
}

export default AddArticleFR;
