"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[725],{42596:function(e,t,r){r.d(t,{V:function(){return i}});var n=r(94143),o=r(50738);function i(e){return(0,o.ZP)("MuiDivider",e)}let l=(0,n.Z)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]);t.Z=l},67752:function(e,t,r){r.d(t,{f:function(){return i}});var n=r(94143),o=r(50738);function i(e){return(0,o.ZP)("MuiListItemIcon",e)}let l=(0,n.Z)("MuiListItemIcon",["root","alignItemsFlexStart"]);t.Z=l},3127:function(e,t,r){r.d(t,{L:function(){return i}});var n=r(94143),o=r(50738);function i(e){return(0,o.ZP)("MuiListItemText",e)}let l=(0,n.Z)("MuiListItemText",["root","multiline","dense","inset","primary","secondary"]);t.Z=l},42187:function(e,t,r){r.d(t,{Z:function(){return O}});var n=r(2265),o=r(61994),i=r(20801),l=r(82590),a=r(34765),s=r(16210),p=r(76301),u=r(37053),d=r(15566),c=r(82662),f=r(84217),m=r(60118),y=r(42596),g=r(67752),h=r(3127),b=r(94143),v=r(50738);function x(e){return(0,v.ZP)("MuiMenuItem",e)}let w=(0,b.Z)("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]);var k=r(57437);let C=e=>{let{disabled:t,dense:r,divider:n,disableGutters:o,selected:l,classes:a}=e,s=(0,i.Z)({root:["root",r&&"dense",t&&"disabled",!o&&"gutters",n&&"divider",l&&"selected"]},x,a);return{...a,...s}},$=(0,s.ZP)(c.Z,{shouldForwardProp:e=>(0,a.Z)(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.dense&&t.dense,r.divider&&t.divider,!r.disableGutters&&t.gutters]}})((0,p.Z)(e=>{let{theme:t}=e;return{...t.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${w.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,l.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${w.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,l.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},[`&.${w.selected}:hover`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,l.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,l.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity)}},[`&.${w.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${w.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity},[`& + .${y.Z.root}`]:{marginTop:t.spacing(1),marginBottom:t.spacing(1)},[`& + .${y.Z.inset}`]:{marginLeft:52},[`& .${h.Z.root}`]:{marginTop:0,marginBottom:0},[`& .${h.Z.inset}`]:{paddingLeft:36},[`& .${g.Z.root}`]:{minWidth:36},variants:[{props:e=>{let{ownerState:t}=e;return!t.disableGutters},style:{paddingLeft:16,paddingRight:16}},{props:e=>{let{ownerState:t}=e;return t.divider},style:{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"}},{props:e=>{let{ownerState:t}=e;return!t.dense},style:{[t.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:e=>{let{ownerState:t}=e;return t.dense},style:{minHeight:32,paddingTop:4,paddingBottom:4,...t.typography.body2,[`& .${g.Z.root} svg`]:{fontSize:"1.25rem"}}}]}}));var O=n.forwardRef(function(e,t){let r;let i=(0,u.i)({props:e,name:"MuiMenuItem"}),{autoFocus:l=!1,component:a="li",dense:s=!1,divider:p=!1,disableGutters:c=!1,focusVisibleClassName:y,role:g="menuitem",tabIndex:h,className:b,...v}=i,x=n.useContext(d.Z),w=n.useMemo(()=>({dense:s||x.dense||!1,disableGutters:c}),[x.dense,s,c]),O=n.useRef(null);(0,f.Z)(()=>{l&&O.current&&O.current.focus()},[l]);let S={...i,dense:w.dense,divider:p,disableGutters:c},Z=C(i),P=(0,m.Z)(O,t);return i.disabled||(r=void 0!==h?h:-1),(0,k.jsx)(d.Z.Provider,{value:w,children:(0,k.jsx)($,{ref:P,role:g,tabIndex:r,component:a,focusVisibleClassName:(0,o.Z)(Z.focusVisible,y),className:(0,o.Z)(Z.root,b),...v,ownerState:S,classes:Z})})})},93826:function(e,t,r){r.d(t,{Z:function(){return o}});var n=r(53232);function o(e){let{theme:t,name:r,props:o}=e;return t&&t.components&&t.components[r]&&t.components[r].defaultProps?(0,n.Z)(t.components[r].defaultProps,o):o}},20956:function(e,t,r){r.d(t,{Z:function(){return i}});var n=r(93826),o=r(49695);function i(e){let{props:t,name:r,defaultTheme:i,themeId:l}=e,a=(0,o.Z)(i);return l&&(a=a[l]||a),(0,n.Z)({theme:a,name:r,props:t})}},64119:function(e,t,r){r.d(t,{Z:function(){return l}});var n=r(20956),o=r(55201),i=r(22166);function l(e){let{props:t,name:r}=e;return(0,n.Z)({props:t,name:r,defaultTheme:o.Z,themeId:i.Z})}},60445:function(e,t,r){r.d(t,{V:function(){return i}});var n=r(2265);r(57437);let o=n.createContext(),i=()=>n.useContext(o)??!1},38145:function(e,t,r){r.d(t,{ZP:function(){return ek},x9:function(){return ev}});var n=r(85968),o=r(29896);let i=[];function l(e){return i[0]=e,(0,o.O)(i)}var a=r(2265),s=r(59679);function p(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function u(e,t,r={clone:!0}){let n=r.clone?{...e}:e;return p(e)&&p(t)&&Object.keys(t).forEach(o=>{a.isValidElement(t[o])||(0,s.iY)(t[o])?n[o]=t[o]:p(t[o])&&Object.prototype.hasOwnProperty.call(e,o)&&p(e[o])?n[o]=u(e[o],t[o],r):r.clone?n[o]=p(t[o])?function e(t){if(a.isValidElement(t)||(0,s.iY)(t)||!p(t))return t;let r={};return Object.keys(t).forEach(n=>{r[n]=e(t[n])}),r}(t[o]):t[o]:n[o]=t[o]}),n}let d=e=>{let t=Object.keys(e).map(t=>({key:t,val:e[t]}))||[];return t.sort((e,t)=>e.val-t.val),t.reduce((e,t)=>({...e,[t.key]:t.val}),{})};var c={borderRadius:4};let f={xs:0,sm:600,md:900,lg:1200,xl:1536},m={keys:["xs","sm","md","lg","xl"],up:e=>`@media (min-width:${f[e]}px)`},y={containerQueries:e=>({up:t=>{let r="number"==typeof t?t:f[t]||t;return"number"==typeof r&&(r=`${r}px`),e?`@container ${e} (min-width:${r})`:`@container (min-width:${r})`}})};function g(e,t,r){let n=e.theme||{};if(Array.isArray(t)){let e=n.breakpoints||m;return t.reduce((n,o,i)=>(n[e.up(e.keys[i])]=r(t[i]),n),{})}if("object"==typeof t){let e=n.breakpoints||m;return Object.keys(t).reduce((o,i)=>{var l;if(l=e.keys,"@"===i||i.startsWith("@")&&(l.some(e=>i.startsWith(`@${e}`))||i.match(/^@\d/))){let e=function(e,t){let r=t.match(/^@([^/]+)?\/?(.+)?$/);if(!r)return null;let[,n,o]=r,i=Number.isNaN(+n)?n||0:+n;return e.containerQueries(o).up(i)}(n.containerQueries?n:y,i);e&&(o[e]=r(t[i],i))}else Object.keys(e.values||f).includes(i)?o[e.up(i)]=r(t[i],i):o[i]=t[i];return o},{})}return r(t)}function h(e){if("string"!=typeof e)throw Error(function(e){let t=new URL("https://mui.com/production-error/?code=7");return[].forEach(e=>t.searchParams.append("args[]",e)),`Minified MUI error #7; visit ${t} for the full message.`}(0));return e.charAt(0).toUpperCase()+e.slice(1)}function b(e,t,r=!0){if(!t||"string"!=typeof t)return null;if(e&&e.vars&&r){let r=`vars.${t}`.split(".").reduce((e,t)=>e&&e[t]?e[t]:null,e);if(null!=r)return r}return t.split(".").reduce((e,t)=>e&&null!=e[t]?e[t]:null,e)}function v(e,t,r,n=r){let o;return o="function"==typeof e?e(r):Array.isArray(e)?e[r]||n:b(e,r)||n,t&&(o=t(o,n,e)),o}var x=function(e){let{prop:t,cssProperty:r=e.prop,themeKey:n,transform:o}=e,i=e=>{if(null==e[t])return null;let i=e[t],l=b(e.theme,n)||{};return g(e,i,e=>{let n=v(l,o,e);return(e===n&&"string"==typeof e&&(n=v(l,o,`${t}${"default"===e?"":h(e)}`,e)),!1===r)?n:{[r]:n}})};return i.propTypes={},i.filterProps=[t],i},w=function(e,t){return t?u(e,t,{clone:!1}):e};let k={m:"margin",p:"padding"},C={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},$={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},O=function(e){let t={};return r=>(void 0===t[r]&&(t[r]=e(r)),t[r])}(e=>{if(e.length>2){if(!$[e])return[e];e=$[e]}let[t,r]=e.split(""),n=k[t],o=C[r]||"";return Array.isArray(o)?o.map(e=>n+e):[n+o]}),S=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Z=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"],P=[...S,...Z];function A(e,t,r,n){let o=b(e,t,!0)??r;return"number"==typeof o||"string"==typeof o?e=>"string"==typeof e?e:"string"==typeof o?o.startsWith("var(")&&0===e?0:o.startsWith("var(")&&1===e?o:`calc(${e} * ${o})`:o*e:Array.isArray(o)?e=>{if("string"==typeof e)return e;let t=o[Math.abs(e)];return e>=0?t:"number"==typeof t?-t:"string"==typeof t&&t.startsWith("var(")?`calc(-1 * ${t})`:`-${t}`}:"function"==typeof o?o:()=>void 0}function R(e){return A(e,"spacing",8,"spacing")}function T(e,t){return"string"==typeof t||null==t?t:e(t)}function I(e,t){let r=R(e.theme);return Object.keys(e).map(n=>(function(e,t,r,n){var o;if(!t.includes(r))return null;let i=(o=O(r),e=>o.reduce((t,r)=>(t[r]=T(n,e),t),{})),l=e[r];return g(e,l,i)})(e,t,n,r)).reduce(w,{})}function j(e){return I(e,S)}function B(e){return I(e,Z)}function K(e){return I(e,P)}j.propTypes={},j.filterProps=S,B.propTypes={},B.filterProps=Z,K.propTypes={},K.filterProps=P;var L=function(...e){let t=e.reduce((e,t)=>(t.filterProps.forEach(r=>{e[r]=t}),e),{}),r=e=>Object.keys(e).reduce((r,n)=>t[n]?w(r,t[n](e)):r,{});return r.propTypes={},r.filterProps=e.reduce((e,t)=>e.concat(t.filterProps),[]),r};function W(e){return"number"!=typeof e?e:`${e}px solid`}function _(e,t){return x({prop:e,themeKey:"borders",transform:t})}let E=_("border",W),M=_("borderTop",W),G=_("borderRight",W),V=_("borderBottom",W),z=_("borderLeft",W),F=_("borderColor"),N=_("borderTopColor"),H=_("borderRightColor"),Y=_("borderBottomColor"),X=_("borderLeftColor"),D=_("outline",W),q=_("outlineColor"),Q=e=>{if(void 0!==e.borderRadius&&null!==e.borderRadius){let t=A(e.theme,"shape.borderRadius",4,"borderRadius");return g(e,e.borderRadius,e=>({borderRadius:T(t,e)}))}return null};Q.propTypes={},Q.filterProps=["borderRadius"],L(E,M,G,V,z,F,N,H,Y,X,Q,D,q);let U=e=>{if(void 0!==e.gap&&null!==e.gap){let t=A(e.theme,"spacing",8,"gap");return g(e,e.gap,e=>({gap:T(t,e)}))}return null};U.propTypes={},U.filterProps=["gap"];let J=e=>{if(void 0!==e.columnGap&&null!==e.columnGap){let t=A(e.theme,"spacing",8,"columnGap");return g(e,e.columnGap,e=>({columnGap:T(t,e)}))}return null};J.propTypes={},J.filterProps=["columnGap"];let ee=e=>{if(void 0!==e.rowGap&&null!==e.rowGap){let t=A(e.theme,"spacing",8,"rowGap");return g(e,e.rowGap,e=>({rowGap:T(t,e)}))}return null};ee.propTypes={},ee.filterProps=["rowGap"];let et=x({prop:"gridColumn"}),er=x({prop:"gridRow"}),en=x({prop:"gridAutoFlow"}),eo=x({prop:"gridAutoColumns"}),ei=x({prop:"gridAutoRows"}),el=x({prop:"gridTemplateColumns"});function ea(e,t){return"grey"===t?t:e}function es(e){return e<=1&&0!==e?`${100*e}%`:e}L(U,J,ee,et,er,en,eo,ei,el,x({prop:"gridTemplateRows"}),x({prop:"gridTemplateAreas"}),x({prop:"gridArea"})),L(x({prop:"color",themeKey:"palette",transform:ea}),x({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:ea}),x({prop:"backgroundColor",themeKey:"palette",transform:ea}));let ep=x({prop:"width",transform:es}),eu=e=>void 0!==e.maxWidth&&null!==e.maxWidth?g(e,e.maxWidth,t=>{let r=e.theme?.breakpoints?.values?.[t]||f[t];return r?e.theme?.breakpoints?.unit!=="px"?{maxWidth:`${r}${e.theme.breakpoints.unit}`}:{maxWidth:r}:{maxWidth:es(t)}}):null;eu.filterProps=["maxWidth"];let ed=x({prop:"minWidth",transform:es}),ec=x({prop:"height",transform:es}),ef=x({prop:"maxHeight",transform:es}),em=x({prop:"minHeight",transform:es});x({prop:"size",cssProperty:"width",transform:es}),x({prop:"size",cssProperty:"height",transform:es}),L(ep,eu,ed,ec,ef,em,x({prop:"boxSizing"}));var ey={border:{themeKey:"borders",transform:W},borderTop:{themeKey:"borders",transform:W},borderRight:{themeKey:"borders",transform:W},borderBottom:{themeKey:"borders",transform:W},borderLeft:{themeKey:"borders",transform:W},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:W},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Q},color:{themeKey:"palette",transform:ea},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:ea},backgroundColor:{themeKey:"palette",transform:ea},p:{style:B},pt:{style:B},pr:{style:B},pb:{style:B},pl:{style:B},px:{style:B},py:{style:B},padding:{style:B},paddingTop:{style:B},paddingRight:{style:B},paddingBottom:{style:B},paddingLeft:{style:B},paddingX:{style:B},paddingY:{style:B},paddingInline:{style:B},paddingInlineStart:{style:B},paddingInlineEnd:{style:B},paddingBlock:{style:B},paddingBlockStart:{style:B},paddingBlockEnd:{style:B},m:{style:j},mt:{style:j},mr:{style:j},mb:{style:j},ml:{style:j},mx:{style:j},my:{style:j},margin:{style:j},marginTop:{style:j},marginRight:{style:j},marginBottom:{style:j},marginLeft:{style:j},marginX:{style:j},marginY:{style:j},marginInline:{style:j},marginInlineStart:{style:j},marginInlineEnd:{style:j},marginBlock:{style:j},marginBlockStart:{style:j},marginBlockEnd:{style:j},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:U},rowGap:{style:ee},columnGap:{style:J},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:es},maxWidth:{style:eu},minWidth:{transform:es},height:{transform:es},maxHeight:{transform:es},minHeight:{transform:es},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};let eg=function(){function e(e,t,r,n){let o={[e]:t,theme:r},i=n[e];if(!i)return{[e]:t};let{cssProperty:l=e,themeKey:a,transform:s,style:p}=i;if(null==t)return null;if("typography"===a&&"inherit"===t)return{[e]:t};let u=b(r,a)||{};return p?p(o):g(o,t,t=>{let r=v(u,s,t);return(t===r&&"string"==typeof t&&(r=v(u,s,`${e}${"default"===t?"":h(t)}`,t)),!1===l)?r:{[l]:r}})}return function t(r){let{sx:n,theme:o={}}=r||{};if(!n)return null;let i=o.unstable_sxConfig??ey;function l(r){var n;let l=r;if("function"==typeof r)l=r(o);else if("object"!=typeof r)return r;if(!l)return null;let a=function(e={}){return e.keys?.reduce((t,r)=>(t[e.up(r)]={},t),{})||{}}(o.breakpoints),s=Object.keys(a),p=a;return Object.keys(l).forEach(r=>{var n;let a="function"==typeof(n=l[r])?n(o):n;if(null!=a){if("object"==typeof a){if(i[r])p=w(p,e(r,a,o,i));else{let e=g({theme:o},a,e=>({[r]:e}));(function(...e){let t=new Set(e.reduce((e,t)=>e.concat(Object.keys(t)),[]));return e.every(e=>t.size===Object.keys(e).length)})(e,a)?p[r]=t({sx:a,theme:o}):p=w(p,e)}}else p=w(p,e(r,a,o,i))}}),function(e,t){if(!e.containerQueries)return t;let r=Object.keys(t).filter(e=>e.startsWith("@container")).sort((e,t)=>{let r=/min-width:\s*([0-9.]+)/;return+(e.match(r)?.[1]||0)-+(t.match(r)?.[1]||0)});return r.length?r.reduce((e,r)=>{let n=t[r];return delete e[r],e[r]=n,e},{...t}):t}(o,(n=p,s.reduce((e,t)=>{let r=e[t];return r&&0!==Object.keys(r).length||delete e[t],e},n)))}return Array.isArray(n)?n.map(l):l(n)}}();function eh(e,t){if(this.vars){if(!this.colorSchemes?.[e]||"function"!=typeof this.getColorSchemeSelector)return{};let r=this.getColorSchemeSelector(e);return"&"===r?t:((r.includes("data-")||r.includes("."))&&(r=`*:where(${r.replace(/\s*&$/,"")}) &`),{[r]:t})}return this.palette.mode===e?t:{}}eg.filterProps=["sx"];let eb=function(e={}){let{breakpoints:t={},palette:r={},spacing:n,shape:o={},...i}=e,l=function(e){let{values:t={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:r="px",step:n=5,...o}=e,i=d(t),l=Object.keys(i);function a(e){let n="number"==typeof t[e]?t[e]:e;return`@media (min-width:${n}${r})`}function s(e){let o="number"==typeof t[e]?t[e]:e;return`@media (max-width:${o-n/100}${r})`}function p(e,o){let i=l.indexOf(o);return`@media (min-width:${"number"==typeof t[e]?t[e]:e}${r}) and (max-width:${(-1!==i&&"number"==typeof t[l[i]]?t[l[i]]:o)-n/100}${r})`}return{keys:l,values:i,up:a,down:s,between:p,only:function(e){return l.indexOf(e)+1<l.length?p(e,l[l.indexOf(e)+1]):a(e)},not:function(e){let t=l.indexOf(e);return 0===t?a(l[1]):t===l.length-1?s(l[t]):p(e,l[l.indexOf(e)+1]).replace("@media","@media not all and")},unit:r,...o}}(t),a=function(e=8,t=R({spacing:e})){if(e.mui)return e;let r=(...e)=>(0===e.length?[1]:e).map(e=>{let r=t(e);return"number"==typeof r?`${r}px`:r}).join(" ");return r.mui=!0,r}(n),s=u({breakpoints:l,direction:"ltr",components:{},palette:{mode:"light",...r},spacing:a,shape:{...c,...o}},i);return(s=function(e){let t=(e,t)=>e.replace("@media",t?`@container ${t}`:"@container");function r(r,n){r.up=(...r)=>t(e.breakpoints.up(...r),n),r.down=(...r)=>t(e.breakpoints.down(...r),n),r.between=(...r)=>t(e.breakpoints.between(...r),n),r.only=(...r)=>t(e.breakpoints.only(...r),n),r.not=(...r)=>{let o=t(e.breakpoints.not(...r),n);return o.includes("not all and")?o.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):o}}let n={},o=e=>(r(n,e),n);return r(o),{...e,containerQueries:o}}(s)).applyStyles=eh,(s=[].reduce((e,t)=>u(e,t),s)).unstable_sxConfig={...ey,...i?.unstable_sxConfig},s.unstable_sx=function(e){return eg({sx:e,theme:this})},s}();function ev(e){return"ownerState"!==e&&"theme"!==e&&"sx"!==e&&"as"!==e}function ex(e,t){let r="function"==typeof t?t(e):t;if(Array.isArray(r))return r.flatMap(t=>ex(e,t));if(Array.isArray(r?.variants)){let t;if(r.isProcessed)t=r.style;else{let{variants:e,...n}=r;t=n}return ew(e,r.variants,[t])}return r?.isProcessed?r.style:r}function ew(e,t,r=[]){let n;e:for(let o=0;o<t.length;o+=1){let i=t[o];if("function"==typeof i.props){if(n??={...e,...e.ownerState,ownerState:e.ownerState},!i.props(n))continue}else for(let t in i.props)if(e[t]!==i.props[t]&&e.ownerState?.[t]!==i.props[t])continue e;"function"==typeof i.style?(n??={...e,...e.ownerState,ownerState:e.ownerState},r.push(i.style(n))):r.push(i.style)}return r}function ek(e={}){let{themeId:t,defaultTheme:r=eb,rootShouldForwardProp:o=ev,slotShouldForwardProp:i=ev}=e;function a(e){e.theme=!function(e){for(let t in e)return!1;return!0}(e.theme)?e.theme[t]||e.theme:r}return(e,t={})=>{var r,s,u;r=e=>e.filter(e=>e!==eg),Array.isArray(e.__emotion_styles)&&(e.__emotion_styles=r(e.__emotion_styles));let{name:d,slot:c,skipVariantsResolver:f,skipSx:m,overridesResolver:y=(s=c?c.charAt(0).toLowerCase()+c.slice(1):c)?(e,t)=>t[s]:null,...g}=t,h=void 0!==f?f:c&&"Root"!==c&&"root"!==c||!1,b=m||!1,v=ev;"Root"===c||"root"===c?v=o:c?v=i:"string"==typeof e&&e.charCodeAt(0)>96&&(v=void 0);let x=(u={shouldForwardProp:v,label:void 0,...g},(0,n.Z)(e,u)),w=e=>{if("function"==typeof e&&e.__emotion_real!==e)return function(t){return ex(t,e)};if(p(e)){let t=function(e){let{variants:t,...r}=e,n={variants:t,style:l(r),isProcessed:!0};return n.style===r||t&&t.forEach(e=>{"function"!=typeof e.style&&(e.style=l(e.style))}),n}(e);return t.variants?function(e){return ex(e,t)}:t.style}return e},k=(...t)=>{let r=[],n=t.map(w),o=[];if(r.push(a),d&&y&&o.push(function(e){let t=e.theme,r=t.components?.[d]?.styleOverrides;if(!r)return null;let n={};for(let t in r)n[t]=ex(e,r[t]);return y(e,n)}),d&&!h&&o.push(function(e){let t=e.theme,r=t?.components?.[d]?.variants;return r?ew(e,r):null}),b||o.push(eg),Array.isArray(n[0])){let e;let t=n.shift(),i=Array(r.length).fill(""),l=Array(o.length).fill("");(e=[...i,...t,...l]).raw=[...i,...t.raw,...l],r.unshift(e)}let i=x(...r,...n,...o);return e.muiName&&(i.muiName=e.muiName),i};return x.withConfig&&(k.withConfig=x.withConfig),k}}}}]);