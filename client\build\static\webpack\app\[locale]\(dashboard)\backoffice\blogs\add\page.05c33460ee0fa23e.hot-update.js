"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/AddArticleFR.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @feelinglovelynow/slug */ \"(app-pages-browser)/./node_modules/@feelinglovelynow/slug/dist/index.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n/* harmony import */ var suneditor_dist_css_suneditor_min_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! suneditor/dist/css/suneditor.min.css */ \"(app-pages-browser)/./node_modules/suneditor/dist/css/suneditor.min.css\");\n/* harmony import */ var react_tag_input__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! react-tag-input */ \"(app-pages-browser)/./node_modules/react-tag-input/dist/index.js\");\n/* harmony import */ var _components_ui_CustomSunEditor__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/CustomSunEditor */ \"(app-pages-browser)/./src/components/ui/CustomSunEditor.jsx\");\n/* harmony import */ var _FaqSection__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./FaqSection */ \"(app-pages-browser)/./src/features/blog/components/FaqSection.jsx\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/DatePicker/DatePicker.js\");\n/* harmony import */ var _mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/x-date-pickers/internals/demo */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/internals/demo/DemoContainer.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/x-date-pickers/AdapterDayjs */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/AdapterDayjs/AdapterDayjs.js\");\n/* harmony import */ var _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/features/opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _DocumentImporter__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./DocumentImporter */ \"(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AddArticleFR(param) {\n    let { errors, touched, setFieldValue, values, onImageSelect, categories, filteredCategories, onCategoriesSelect, debounce } = param;\n    _s();\n    const handlePaste = (event, cleanData, maxCharCount)=>{\n        let html = cleanData;\n        // Correction des balises non fermées\n        html = html.replace(/<strong>(.*?)$/g, \"<strong>$1</strong>\");\n        return html;\n    };\n    const KeyCodes = {\n        comma: 188,\n        enter: 13\n    };\n    const delimiters = [\n        KeyCodes.comma,\n        KeyCodes.enter\n    ];\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [highlights, setHighlights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [publishNow, setPublishNow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const language = \"fr\";\n    const handlePhotoChange = async ()=>{\n        const selectedFile = imageInputRef.current.files[0];\n        setSelectedImage(imageInputRef.current.files[0]);\n        if (selectedFile) {\n            onImageSelect(selectedFile, language);\n        }\n    };\n    const useSaveFileHook = (0,_features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_12__.useSaveFile)();\n    const handlePhotoBlogChange = async (file, info, core, uploadHandler)=>{\n        if (file instanceof HTMLImageElement) {\n            const src = file.src;\n            if (src.startsWith(\"data:image\")) {\n                const base64Data = src.split(\",\")[1];\n                const contentType = src.match(/data:(.*?);base64/)[1];\n                const byteCharacters = atob(base64Data);\n                const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i)=>byteCharacters.charCodeAt(i));\n                const byteArray = new Uint8Array(byteNumbers);\n                const blob = new Blob([\n                    byteArray\n                ], {\n                    type: contentType\n                });\n                const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                const selectedFile = new File([\n                    blob\n                ], fileName, {\n                    type: contentType\n                });\n                await uploadFile(selectedFile, uploadHandler, core, file);\n            } else {\n                fetch(src).then((response)=>response.blob()).then((blob)=>{\n                    const contentType = blob.type;\n                    const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                    const selectedFile = new File([\n                        blob\n                    ], fileName, {\n                        type: contentType\n                    });\n                    uploadFile(selectedFile, uploadHandler, core, file);\n                }).catch((error)=>console.error(\"Error converting image URL to Blob:\", error));\n            }\n        } else {\n            console.error(\"File is not an HTMLImageElement.\");\n        }\n    };\n    const uploadFile = (selectedFile, uploadHandler, core, originalImage)=>{\n        let uuidPhoto;\n        uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n        const formData = new FormData();\n        formData.append(\"file\", selectedFile);\n        const extension = selectedFile.name.split(\".\").pop();\n        const currentYear = new Date().getFullYear();\n        useSaveFileHook.mutate({\n            resource: \"blogs\",\n            folder: currentYear.toString(),\n            filename: uuidPhoto,\n            body: {\n                formData,\n                t\n            }\n        }, {\n            onSuccess: (dataUUID)=>{\n                const uuidPhotoFileName = dataUUID.message === \"uuid exist\" ? dataUUID.uuid : `${uuidPhoto}.${extension}`;\n                const imageUrl = `${\"http://localhost:4000/api/v1\"}/files/${uuidPhotoFileName}`;\n                originalImage.src = imageUrl;\n                uploadHandler({\n                    result: [\n                        {\n                            id: uuidPhotoFileName,\n                            url: imageUrl\n                        }\n                    ]\n                });\n            },\n            onError: (error)=>{\n                console.error(\"Error uploading file:\", error);\n            }\n        });\n    };\n    const [titlefr, setTitlefr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedTitlefr = localStorage.getItem(\"titlefr\");\n        return savedTitlefr ? JSON.parse(savedTitlefr) : \"\";\n    });\n    const [metatitlefr, setMetatitlefr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetatitlefr = localStorage.getItem(\"metatitlefr\");\n        return savedMetatitlefr ? JSON.parse(savedMetatitlefr) : \"\";\n    });\n    const [metaDescriptionfr, setMetaDescriptionfr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetadescriptionfr = localStorage.getItem(\"metaDescriptionfr\");\n        return savedMetadescriptionfr ? JSON.parse(savedMetadescriptionfr) : \"\";\n    });\n    const [contentfr, setContentfr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedContentfr = localStorage.getItem(\"contentfr\");\n        return savedContentfr ? JSON.parse(savedContentfr) : \"\";\n    });\n    const handleEditorChange = (newContentfr)=>{\n        setContentfr(newContentfr);\n        setFieldValue(\"contentFR\", newContentfr);\n    };\n    const handleContentExtracted = (extractedContent)=>{\n        console.log(\"extractedContent\", extractedContent);\n        setContentfr(extractedContent);\n        setFieldValue(\"contentFR\", extractedContent);\n        localStorage.setItem(\"contentfr\", JSON.stringify(extractedContent));\n        debounce();\n    };\n    const handleMetadataExtracted = (metadata)=>{\n        if (metadata.title && !values.titleFR) {\n            setFieldValue(\"titleFR\", metadata.title);\n            setTitlefr(metadata.title);\n            const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_5__.slug)(metadata.title);\n            setFieldValue(\"urlFR\", url);\n        }\n        if (metadata.description && !values.descriptionFR) {\n            setFieldValue(\"descriptionFR\", metadata.description);\n        }\n        if (metadata.keywords && metadata.keywords.length > 0) {\n            const keywordTags = metadata.keywords.map((keyword, index)=>({\n                    id: `extracted-${index}`,\n                    text: keyword\n                }));\n            const existingKeywords = values.keywordsFR || [];\n            const mergedKeywords = [\n                ...existingKeywords,\n                ...keywordTags\n            ];\n            setFieldValue(\"keywordsFR\", mergedKeywords.map((tag)=>tag.text));\n        }\n        debounce();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (titlefr) {\n            localStorage.setItem(\"titlefr\", JSON.stringify(titlefr));\n        }\n        if (contentfr) {\n            localStorage.setItem(\"contentfr\", JSON.stringify(contentfr));\n        }\n        if (metatitlefr) {\n            localStorage.setItem(\"metatitlefr\", JSON.stringify(metatitlefr));\n        }\n        if (metaDescriptionfr) {\n            localStorage.setItem(\"metaDescriptionfr\", JSON.stringify(metaDescriptionfr));\n        }\n    }, [\n        titlefr,\n        contentfr,\n        metatitlefr,\n        metaDescriptionfr\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"label-pentabell\",\n                children: \"Add article French : \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:title\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"titleFR\",\n                                        type: \"text\",\n                                        value: titlefr || values.titleFR,\n                                        onChange: (e)=>{\n                                            const titleFR = e.target.value;\n                                            setFieldValue(\"titleFR\", titleFR);\n                                            setTitlefr(titleFR);\n                                            const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_5__.slug)(titleFR);\n                                            setFieldValue(\"urlFR\", url);\n                                        },\n                                        className: \"input-pentabell\" + (errors.titleFR && touched.titleFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"titleFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:categories\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                multiple: true,\n                                                className: \"input-pentabell\",\n                                                id: \"tags-standard\",\n                                                options: filteredCategories.length > 0 ? filteredCategories : categories,\n                                                getOptionLabel: (option)=>option.name,\n                                                selected: values.categoryFR.length > 0 ? (filteredCategories.length > 0 ? filteredCategories : categories).filter((category)=>values.categoryFR.includes(category.id)) : [],\n                                                onChange: (event, selectedOptions)=>{\n                                                    const categoryIds = selectedOptions.map((category)=>category.id);\n                                                    setFieldValue(\"categoryFR\", categoryIds);\n                                                    onCategoriesSelect(categoryIds);\n                                                },\n                                                renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        ...params,\n                                                        className: \"input-pentabell  multiple-select\",\n                                                        variant: \"standard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this),\n                            touched.categoryFR && errors.categoryFR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"error\",\n                                children: errors.categoryFR\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Description\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"descriptionFR\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: values.descriptionFR,\n                                    onChange: (e)=>{\n                                        const descriptionFR = e.target.value;\n                                        setFieldValue(\"descriptionFR\", descriptionFR);\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.descriptionFR && touched.descriptionFR ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"descriptionFR\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Highlights\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"tags\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                        tags: highlights,\n                                        className: \"input-pentabell\" + (errors.highlightsFR && touched.highlightsFR ? \" is-invalid\" : \"\"),\n                                        delimiters: delimiters,\n                                        handleDelete: (i)=>{\n                                            const updatedTags = highlights.filter((tag, index)=>index !== i);\n                                            setHighlights(updatedTags);\n                                            setFieldValue(\"highlightsFR\", updatedTags.map((tag)=>tag.text));\n                                        },\n                                        handleAddition: (tag)=>{\n                                            setHighlights([\n                                                ...highlights,\n                                                tag\n                                            ]);\n                                            setFieldValue(\"highlightsFR\", [\n                                                ...highlights,\n                                                tag\n                                            ].map((item)=>item.text));\n                                        },\n                                        inputFieldPosition: \"bottom\",\n                                        autocomplete: true,\n                                        allowDragDrop: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"keywordsEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 353,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentImporter__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        onContentExtracted: handleContentExtracted,\n                        onMetadataExtracted: handleMetadataExtracted,\n                        language: \"FR\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomSunEditor__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        setContents: contentfr || values?.contentFR || \"\",\n                        onChange: handleEditorChange,\n                        onPaste: handlePaste,\n                        onImageUpload: handlePhotoBlogChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 412,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaqSection__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                values: values,\n                setFieldValue: setFieldValue,\n                errors: errors,\n                touched: touched,\n                language: \"FR\",\n                debounce: debounce\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:metaTitle\"),\n                                    \" (\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: values.metaTitleFR?.length > 65 ? \" text-danger\" : \"\",\n                                        children: [\n                                            \" \",\n                                            values.metaTitleFR?.length,\n                                            \" / 65\",\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    \")\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"metaTitleFR\",\n                                        type: \"text\",\n                                        value: metatitlefr || values.metaTitleFR,\n                                        onChange: (e)=>{\n                                            const metaTitleFR = e.target.value;\n                                            setFieldValue(\"metaTitleFR\", metaTitleFR);\n                                            setMetatitlefr(metaTitleFR);\n                                        },\n                                        className: \"input-pentabell\" + (errors.metaTitleFR && touched.metaTitleFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"metaTitleFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 426,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 425,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 424,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:url\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"urlFR\",\n                                        type: \"text\",\n                                        value: values.urlFR,\n                                        onChange: (e)=>{\n                                            setFieldValue(\"urlFR\", e.target.value);\n                                        },\n                                        className: \"input-pentabell\" + (errors.urlFR && touched.urlFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"urlFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 463,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 462,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:metaDescription\"),\n                                \" (\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: values.metaDescriptionFR?.length > 160 ? \" text-danger\" : \"\",\n                                    children: [\n                                        values.metaDescriptionFR?.length,\n                                        \" / 160\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                \")\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"metaDescriptionFR\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 2,\n                                    value: metaDescriptionfr || values.metaDescriptionFR,\n                                    onChange: (e)=>{\n                                        const metaDescriptionFR = e.target.value;\n                                        setFieldValue(\"metaDescriptionFR\", metaDescriptionFR);\n                                        setMetaDescriptionfr(metaDescriptionFR);\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.metaDescriptionFR && touched.metaDescriptionFR ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"metaDescriptionFR\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 491,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 490,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 489,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:featuredImage\"),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"upload-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: `image-upload-fr`,\n                                        className: \"file-labels\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                id: `image-upload-fr`,\n                                                name: \"imageEN\",\n                                                accept: \".png, .jpg, .jpeg, .webp\",\n                                                ref: imageInputRef,\n                                                onChange: (e)=>{\n                                                    setFieldValue(\"imageFR\", e.target.files[0]);\n                                                    handlePhotoChange();\n                                                },\n                                                className: \"file-input\" + (errors.imageFR && touched.imageFR ? \" is-invalid\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"upload-area\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"icon-pic\",\n                                                            style: {\n                                                                backgroundImage: `url(\"${selectedImage ? URL.createObjectURL(selectedImage) : values.imageFR ? `${process.env.REACT_APP_API_URL}${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.files}/${values.imageFR}` : _assets_images_add_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"].src}\")`,\n                                                                backgroundSize: \"cover\",\n                                                                backgroundRepeat: \"no-repeat\",\n                                                                backgroundPosition: \"center\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-text\",\n                                                                children: t(\"createArticle:addFeatImg\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-description\",\n                                                                children: t(\"createArticle:clickBox\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                                name: \"imageFR\",\n                                                component: \"div\",\n                                                className: \"invalid-feedback error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 532,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 531,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 530,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 529,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:alt\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"altFR\",\n                                        type: \"text\",\n                                        value: values.altFR,\n                                        onChange: (e)=>{\n                                            setFieldValue(\"altFR\", e.target.value);\n                                        },\n                                        className: \"input-pentabell\" + (errors.altFR && touched.altFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"altFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 595,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 593,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:visibility\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"select-pentabell\",\n                                        variant: \"standard\",\n                                        value: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.Visibility.filter((option)=>values.visibilityFR === option),\n                                        selected: values.visibilityFR,\n                                        onChange: (event)=>{\n                                            setFieldValue(\"visibilityFR\", event.target.value);\n                                        },\n                                        children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.Visibility.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                value: item,\n                                                children: item\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"visibilityEN\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 620,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 619,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 618,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:keyword\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        id: \"tags\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                            tags: tags,\n                                            className: \"input-pentabell\" + (errors.keywordsFR && touched.keywordsFR ? \" is-invalid\" : \"\"),\n                                            delimiters: delimiters,\n                                            handleDelete: (i)=>{\n                                                const updatedTags = tags.filter((tag, index)=>index !== i);\n                                                setTags(updatedTags);\n                                                setFieldValue(\"keywordsFR\", updatedTags.map((tag)=>tag.text));\n                                            },\n                                            handleAddition: (tag)=>{\n                                                setTags([\n                                                    ...tags,\n                                                    tag\n                                                ]);\n                                                setFieldValue(\"keywordsFR\", [\n                                                    ...tags,\n                                                    tag\n                                                ].map((item)=>item.text));\n                                            },\n                                            inputFieldPosition: \"bottom\",\n                                            autocomplete: true,\n                                            allowDragDrop: false\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"keywordsFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 650,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 649,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 648,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 592,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"label-form\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.Field, {\n                        type: \"checkbox\",\n                        name: \"publishNow\",\n                        checked: publishNow,\n                        onChange: (e)=>{\n                            setPublishNow(e.target.checked);\n                            if (e.target.checked) {\n                                setFieldValue(\"publishDateFR\", new Date().toISOString());\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 694,\n                        columnNumber: 9\n                    }, this),\n                    t(\"createArticle:publishNow\")\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 693,\n                columnNumber: 7\n            }, this),\n            !publishNow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"label-form\",\n                        children: [\n                            t(\"createArticle:publishDate\"),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_25__.LocalizationProvider, {\n                                dateAdapter: _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_26__.AdapterDayjs,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_27__.DemoContainer, {\n                                    components: [\n                                        \"DatePicker\"\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_28__.DatePicker, {\n                                            variant: \"standard\",\n                                            className: \"input-date\",\n                                            format: \"DD/MM/YYYY\",\n                                            value: dayjs__WEBPACK_IMPORTED_MODULE_11___default()(values.publishDateEN),\n                                            onChange: (date)=>{\n                                                setFieldValue(\"publishDateEN\", dayjs__WEBPACK_IMPORTED_MODULE_11___default()(date).format(\"YYYY-MM-DD\"));\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"publishDateEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 713,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 711,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 710,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 709,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.Field, {\n                type: \"hidden\",\n                name: \"publishDateFR\",\n                value: publishNow ? new Date().toISOString() : publishDate.toISOString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 739,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AddArticleFR, \"+ONfVgda+KvaQf3X16NXIW62f9w=\", false, function() {\n    return [\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_12__.useSaveFile\n    ];\n});\n_c = AddArticleFR;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticleFR);\nvar _c;\n$RefreshReg$(_c, \"AddArticleFR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx\n"));

/***/ })

});