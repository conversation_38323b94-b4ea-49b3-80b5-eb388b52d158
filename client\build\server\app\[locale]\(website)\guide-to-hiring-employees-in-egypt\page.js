(()=>{var e={};e.id=5817,e.ids=[5817],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},6005:e=>{"use strict";e.exports=require("node:crypto")},62984:(e,t,A)=>{"use strict";A.d(t,{Z:()=>r});var i,s=A(1788);function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var i in A)({}).hasOwnProperty.call(A,i)&&(e[i]=A[i])}return e}).apply(null,arguments)}let r=e=>s.createElement("svg",a({xmlns:"http://www.w3.org/2000/svg",width:49,height:48,fill:"none"},e),i||(i=s.createElement("path",{stroke:"#0B3051",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:4,d:"M36.666 40V20m-12 20V8m-12 32V28"})))},27735:(e,t,A)=>{"use strict";A.r(t),A.d(t,{default:()=>r});var i,s=A(95746);function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var i in A)({}).hasOwnProperty.call(A,i)&&(e[i]=A[i])}return e}).apply(null,arguments)}let r=e=>s.createElement("svg",a({xmlns:"http://www.w3.org/2000/svg",width:49,height:48,fill:"none"},e),i||(i=s.createElement("path",{stroke:"#0B3051",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:4,d:"M36.666 40V20m-12 20V8m-12 32V28"})))},44222:(e,t,A)=>{"use strict";A.d(t,{Z:()=>r});var i,s=A(1788);function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var i in A)({}).hasOwnProperty.call(A,i)&&(e[i]=A[i])}return e}).apply(null,arguments)}let r=e=>s.createElement("svg",a({xmlns:"http://www.w3.org/2000/svg",width:49,height:48,fill:"none"},e),i||(i=s.createElement("path",{stroke:"#D69B19",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:4,d:"M36.334 40V20m-12 20V8m-12 32V28"})))},80522:(e,t,A)=>{"use strict";A.r(t),A.d(t,{default:()=>r});var i,s=A(95746);function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var i in A)({}).hasOwnProperty.call(A,i)&&(e[i]=A[i])}return e}).apply(null,arguments)}let r=e=>s.createElement("svg",a({xmlns:"http://www.w3.org/2000/svg",width:49,height:48,fill:"none"},e),i||(i=s.createElement("path",{stroke:"#D69B19",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:4,d:"M36.334 40V20m-12 20V8m-12 32V28"})))},84045:(e,t,A)=>{"use strict";A.d(t,{Z:()=>r});var i,s=A(1788);function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var i in A)({}).hasOwnProperty.call(A,i)&&(e[i]=A[i])}return e}).apply(null,arguments)}let r=e=>s.createElement("svg",a({xmlns:"http://www.w3.org/2000/svg",width:48,height:48,fill:"none"},e),i||(i=s.createElement("path",{stroke:"#234791",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:4,d:"M36 40V20M24 40V8M12 40V28"})))},34371:(e,t,A)=>{"use strict";A.r(t),A.d(t,{default:()=>r});var i,s=A(95746);function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var A=arguments[t];for(var i in A)({}).hasOwnProperty.call(A,i)&&(e[i]=A[i])}return e}).apply(null,arguments)}let r=e=>s.createElement("svg",a({xmlns:"http://www.w3.org/2000/svg",width:48,height:48,fill:"none"},e),i||(i=s.createElement("path",{stroke:"#234791",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:4,d:"M36 40V20M24 40V8M12 40V28"})))},32513:(e,t,A)=>{"use strict";A.r(t),A.d(t,{GlobalError:()=>r.a,__next_app__:()=>g,originalPathname:()=>d,pages:()=>p,routeModule:()=>E,tree:()=>l}),A(93322),A(30962),A(23658),A(54864);var i=A(23191),s=A(88716),a=A(37922),r=A.n(a),n=A(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);A.d(t,o);let l=["",{children:["[locale]",{children:["(website)",{children:["guide-to-hiring-employees-in-egypt",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(A.bind(A,93322)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\guide-to-hiring-employees-in-egypt\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(A.bind(A,30962)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\layout.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(A.bind(A,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(A.bind(A,23658)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(A.bind(A,54864)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\not-found.js"],metadata:{icon:[async e=>(await Promise.resolve().then(A.bind(A,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\guide-to-hiring-employees-in-egypt\\page.jsx"],d="/[locale]/(website)/guide-to-hiring-employees-in-egypt/page",g={require:A,loadChunk:()=>Promise.resolve()},E=new i.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/[locale]/(website)/guide-to-hiring-employees-in-egypt/page",pathname:"/[locale]/guide-to-hiring-employees-in-egypt",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},49570:(e,t,A)=>{Promise.resolve().then(A.bind(A,90423)),Promise.resolve().then(A.bind(A,16027)),Promise.resolve().then(A.bind(A,52188)),Promise.resolve().then(A.bind(A,9782)),Promise.resolve().then(A.bind(A,26887)),Promise.resolve().then(A.bind(A,94705)),Promise.resolve().then(A.bind(A,18054)),Promise.resolve().then(A.bind(A,40162)),Promise.resolve().then(A.bind(A,56323)),Promise.resolve().then(A.bind(A,87893)),Promise.resolve().then(A.bind(A,10936)),Promise.resolve().then(A.bind(A,82639)),Promise.resolve().then(A.bind(A,82020)),Promise.resolve().then(A.bind(A,86682)),Promise.resolve().then(A.bind(A,85127)),Promise.resolve().then(A.bind(A,2851)),Promise.resolve().then(A.bind(A,80918)),Promise.resolve().then(A.bind(A,79189)),Promise.resolve().then(A.bind(A,10827)),Promise.resolve().then(A.bind(A,48507)),Promise.resolve().then(A.bind(A,55854)),Promise.resolve().then(A.bind(A,28578)),Promise.resolve().then(A.bind(A,92669)),Promise.resolve().then(A.bind(A,1956)),Promise.resolve().then(A.bind(A,57015)),Promise.resolve().then(A.bind(A,94342)),Promise.resolve().then(A.bind(A,80695)),Promise.resolve().then(A.bind(A,96314)),Promise.resolve().then(A.bind(A,84791)),Promise.resolve().then(A.bind(A,16190)),Promise.resolve().then(A.bind(A,26668)),Promise.resolve().then(A.bind(A,70719)),Promise.resolve().then(A.bind(A,22522)),Promise.resolve().then(A.bind(A,72150)),Promise.resolve().then(A.bind(A,66871)),Promise.resolve().then(A.bind(A,85260)),Promise.resolve().then(A.bind(A,20964)),Promise.resolve().then(A.bind(A,95487)),Promise.resolve().then(A.bind(A,59505)),Promise.resolve().then(A.bind(A,5728)),Promise.resolve().then(A.bind(A,95053)),Promise.resolve().then(A.bind(A,63572)),Promise.resolve().then(A.bind(A,42021)),Promise.resolve().then(A.bind(A,58536)),Promise.resolve().then(A.bind(A,94599)),Promise.resolve().then(A.bind(A,32923)),Promise.resolve().then(A.bind(A,81957)),Promise.resolve().then(A.bind(A,18990)),Promise.resolve().then(A.bind(A,40166)),Promise.resolve().then(A.bind(A,6690)),Promise.resolve().then(A.bind(A,21447)),Promise.resolve().then(A.bind(A,27735)),Promise.resolve().then(A.bind(A,25513)),Promise.resolve().then(A.bind(A,80522)),Promise.resolve().then(A.bind(A,34371)),Promise.resolve().then(A.bind(A,41783)),Promise.resolve().then(A.bind(A,6007)),Promise.resolve().then(A.bind(A,71479)),Promise.resolve().then(A.bind(A,49950)),Promise.resolve().then(A.bind(A,56668)),Promise.resolve().then(A.bind(A,92451)),Promise.resolve().then(A.bind(A,39135)),Promise.resolve().then(A.bind(A,90131)),Promise.resolve().then(A.bind(A,75113)),Promise.resolve().then(A.bind(A,359)),Promise.resolve().then(A.bind(A,70685)),Promise.resolve().then(A.bind(A,55071)),Promise.resolve().then(A.bind(A,28453)),Promise.resolve().then(A.bind(A,65449)),Promise.resolve().then(A.bind(A,44790)),Promise.resolve().then(A.bind(A,63123)),Promise.resolve().then(A.bind(A,54523)),Promise.resolve().then(A.bind(A,84965)),Promise.resolve().then(A.bind(A,3608)),Promise.resolve().then(A.bind(A,96852)),Promise.resolve().then(A.bind(A,78231)),Promise.resolve().then(A.bind(A,91732)),Promise.resolve().then(A.bind(A,69497)),Promise.resolve().then(A.bind(A,9741)),Promise.resolve().then(A.bind(A,51607))},96852:(e,t,A)=>{"use strict";A.d(t,{default:()=>o});var i=A(10326),s=A(33814),a=A(28236);let r={workingHours:{title:"Egypte:EgypteLabor:workingHours:title"},employmentContracts:{title:"Egypte:EgypteLabor:employmentContracts:title"},visa:{data1:"Egypte:EgypteLabor:visa:data1",data2:"Egypte:EgypteLabor:visa:data2",data3:"Egypte:EgypteLabor:visa:data3",data4:"Egypte:EgypteLabor:visa:data4",data5:"Egypte:EgypteLabor:visa:data5",data6:"Egypte:EgypteLabor:visa:data6",data7:"Egypte:EgypteLabor:visa:data7",data8:"Egypte:EgypteLabor:visa:data8",data9:"Egypte:EgypteLabor:visa:data9",data10:"Egypte:EgypteLabor:visa:data10",data11:"Egypte:EgypteLabor:visa:data11",data12:"Egypte:EgypteLabor:visa:data12",data13:"Egypte:EgypteLabor:visa:data13"}},n={title:"Egypte:EgypteLabor:title",sections:[{id:1,title:r.workingHours.title,subsections:[{title:"Egypte:EgypteLabor:workingHours:title1",description:"Egypte:EgypteLabor:workingHours:description1"},{title:"Egypte:EgypteLabor:workingHours:title2",description:"gypte:EgypteLabor:employmentContracts:description"}]},{id:2,title:r.employmentContracts.title,subsections:[{title:"Egypte:EgypteLabor:employmentContracts:title",description:"Egypte:EgypteLabor:employmentContracts:data"},{title:"Egypte:EgypteLabor:employmentContracts:title1",description:"Egypte:EgypteLabor:employmentContracts:data1"},{title:"Egypte:EgypteLabor:employmentContracts:title2",description:"Egypte:EgypteLabor:employmentContracts:description"}]},{id:3,title:"Egypte:EgypteLabor:payroll:title",type:"payroll",titleKey:"Egypte:EgypteLabor:payroll:title1",items:[{titleKey:"Egypte:EgypteLabor:payroll:fiscalYear:title",dateKeys:["Egypte:EgypteLabor:payroll:fiscalYear:date1","Egypte:EgypteLabor:payroll:fiscalYear:date2"],descriptionKey:"Egypte:EgypteLabor:payroll:fiscalYear:description"},{titleKey:"Egypte:EgypteLabor:payroll:payrollCycle:title",dateKeys:["Egypte:EgypteLabor:payroll:payrollCycle:date"],descriptionKey:"Egypte:EgypteLabor:payroll:payrollCycle:description"},{titleKey:"Egypte:EgypteLabor:payroll:minimumWage:title",dateKeys:["Egypte:EgypteLabor:payroll:minimumWage:wage","Egypte:EgypteLabor:payroll:minimumWage:date"],descriptionKey:"Egypte:EgypteLabor:payroll:minimumWage:description"}]},{id:4,title:"Egypte:EgypteLabor:termination:title",subsections:[{title:"Egypte:EgypteLabor:termination:title1",description:"Egypte:EgypteLabor:termination:description1"},{title:"Egypte:EgypteLabor:termination:titleTermination",description:"Egypte:EgypteLabor:termination:descriptionTermination"},{list:(0,a.$l)(["Egypte:EgypteLabor:termination:data1","Egypte:EgypteLabor:termination:data2","Egypte:EgypteLabor:termination:data3","Egypte:EgypteLabor:termination:data4"])},{title:"Egypte:EgypteLabor:termination:title2",description:"Egypte:EgypteLabor:termination:description2"},{title:"Egypte:EgypteLabor:termination:title3",description:"Egypte:EgypteLabor:termination:description3"}]},{id:5,title:"Egypte:EgypteLabor:leaveEntitlements:title",type:"leaveEntitlements",data:{description:"Egypte:EgypteLabor:leaveEntitlements:description",leaves:[{date:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS1:date",title:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS1:title"},{date:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS2:date",title:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS2:title"},{date:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS3:date",title:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS3:title"},{date:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS4:date",title:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS4:title"},{date:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS5:date",title:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS5:title"},{date:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS6:date",title:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS6:title"},{date:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS7:date",title:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS7:title"},{date:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS8:date",title:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS8:title"},{date:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS9:date",title:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS9:title"},{date:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS10:date",title:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS10:title"},{date:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS11:date",title:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS11:title"},{date:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS12:date",title:"Egypte:EgypteLabor:leaveEntitlements:leaves:dataS12:title"}],annualLeave:{title:"Egypte:EgypteLabor:leaveEntitlements:leaves:annualLeave:title",description:["Egypte:EgypteLabor:leaveEntitlements:leaves:annualLeave:description1"]},maternityLeave:{title:"Egypte:EgypteLabor:leaveEntitlements:leaves:maternityLeave:title",description:["Egypte:EgypteLabor:leaveEntitlements:leaves:maternityLeave:description1"]},sickLeave:{title:"Egypte:EgypteLabor:leaveEntitlements:leaves:sickLeave:title",description:"Egypte:EgypteLabor:leaveEntitlements:leaves:sickLeave:description"},pilgrimageleave:{title:"Egypte:EgypteLabor:leaveEntitlements:leaves:PilgrimageLeave:title",description:"Egypte:EgypteLabor:leaveEntitlements:leaves:PilgrimageLeave:description"},parentalLeave:{title:"Egypte:EgypteLabor:leaveEntitlements:leaves:parentalLeave:title",description:"Egypte:EgypteLabor:leaveEntitlements:leaves:parentalLeave:description"},casualLeave:{title:"Egypte:EgypteLabor:leaveEntitlements:leaves:casualLeave:title",description:"Egypte:EgypteLabor:leaveEntitlements:leaves:casualLeave:description"}}},{id:6,title:"Egypte:EgypteLabor:tax:title",subsections:[{description:"Egypte:EgypteLabor:tax:description"},{title:"Egypte:EgypteLabor:tax:title1",description:"Egypte:EgypteLabor:tax:description1"},{list:(0,a.$l)(["Egypte:EgypteLabor:tax:data1","Egypte:EgypteLabor:tax:data2","Egypte:EgypteLabor:tax:data3","Egypte:EgypteLabor:tax:data4"])},{title:"Egypte:EgypteLabor:tax:title2",description:"Egypte:EgypteLabor:tax:description2",list:(0,a.$l)(["Egypte:EgypteLabor:tax:dataS1","Egypte:EgypteLabor:tax:dataS2","Egypte:EgypteLabor:tax:dataS3"])}]},{id:7,title:"Egypte:EgypteLabor:visa:title",subsections:[{description:"Egypte:EgypteLabor:visa:description1"},{description:"Egypte:EgypteLabor:visa:description2"},{title:"Egypte:EgypteLabor:visa:title1"},{title:"Egypte:EgypteLabor:visa:title2",description:"Egypte:EgypteLabor:visa:description3"},{title:"Egypte:EgypteLabor:visa:title3"},{list:(0,a.$l)([r.visa.data1,r.visa.data2,r.visa.data3,r.visa.data5,r.visa.data6,r.visa.data7,r.visa.data8,r.visa.data9,r.visa.data10,r.visa.data11,r.visa.data12,r.visa.data13])}]}]};function o(){return i.jsx(s.Z,{data:n})}},69497:(e,t,A)=>{"use strict";A.d(t,{default:()=>n});var i=A(10326),s=A(17577),a=A(481);A(46226);let r=e=>{let{selected:t,index:A,onClick:s,teampic:a}=e;return i.jsx("div",{className:"embla-thumbs__slide".concat(t?" embla-thumbs__slide--selected":""),children:i.jsx("button",{onClick:s,type:"button",className:"embla-thumbs__slide__number",children:i.jsx("img",{width:197,height:148,src:a.src,alt:"Carousel",loading:"lazy"})})})},n=function({slides:e,options:t}){let[A,n]=(0,s.useState)(0),[o,l]=(0,a.Z)(t),[p,d]=(0,a.Z)({containScroll:"keepSnaps",dragFree:!0}),g=(0,s.useCallback)(e=>{l&&d&&l.scrollTo(e)},[l,d]);return(0,s.useCallback)(()=>{l&&d&&(n(l.selectedScrollSnap()),d.scrollTo(l.selectedScrollSnap()))},[l,d,n]),(0,i.jsxs)("div",{className:"embla",id:"embla-carousel-thumbs-team-pic",children:[i.jsx("div",{className:"embla__viewport",ref:o,children:i.jsx("div",{className:"embla__container",children:e.map((e,t)=>i.jsx("div",{className:"embla__slide",children:i.jsx("div",{className:"embla__slide__pic",children:i.jsx("img",{width:584,height:438,src:e.src.src,alt:"Algeria team pentabell",loading:"lazy"})})},t))})}),i.jsx("div",{className:"embla-thumbs",children:i.jsx("div",{className:"embla-thumbs__viewport",ref:p,children:i.jsx("div",{className:"embla-thumbs__container",children:e.map((e,t)=>i.jsx(r,{onClick:()=>g(t),selected:t===A,index:t,teampic:e.src},t))})})})]})}},93322:(e,t,A)=>{"use strict";A.r(t),A.d(t,{default:()=>J,generateMetadata:()=>V});var i=A(19510);let s={src:"/_next/static/media/Pentabell-Egypt.eae24556.webp",height:738,width:1440,blurDataURL:"data:image/webp;base64,UklGRkgAAABXRUJQVlA4IDwAAABQAgCdASoIAAQAAkA4JQBOj+DOAPX/A2f9dAAA/slCycwhy+ngk2RwUTvBdpSVoC4+dcPN/zs8L5CjQAA=",blurWidth:8,blurHeight:4};var a=A(94034),r=A(36435),n=A(82305),o=A(27752),l=A(16402),p=A(79604),d=A(11065),g=A(43738),E=A(28409),c=A(71279),y=A(36061),m=A(64161),v=A(79973),b=A(57186),h=A(54643),u=A(30628),x=A(46705);let B=function({t:e}){let t=[{icon:i.jsx(y.Z,{}),titleKey:"Egypte:officeInfoTN:title1",descriptionKey:"Egypte:officeInfoTN:description1"},{icon:i.jsx(m.Z,{}),titleKey:"Egypte:officeInfoTN:title2",descriptionKey:"Egypte:officeInfoTN:description2"},{icon:i.jsx(v.Z,{}),titleKey:"Egypte:officeInfoTN:title3",descriptionKey:"Egypte:officeInfoTN:description3"},{icon:i.jsx(b.Z,{}),titleKey:"Egypte:officeInfoTN:title4",descriptionKey:"Egypte:officeInfoTN:description4"},{icon:i.jsx(h.Z,{}),titleKey:"Egypte:officeInfoTN:title5",descriptionKey:"Egypte:officeInfoTN:description5"},{icon:i.jsx(u.Z,{}),titleKey:"Egypte:officeInfoTN:title6",descriptionKey:"Egypte:officeInfoTN:description6"}];return i.jsx(x.Z,{data:t,t:e})};var L=A(25491),w=A(29612),Q=A(65507),f=A(44222),P=A(62984),C=A(84045),j=A(66789);let z=function({t:e}){let t=[{icon:i.jsx(L.Z,{}),titleKey:"Egypte:BusinessInTunisia:s1:title"},{icon:i.jsx(w.Z,{}),titleKey:"Egypte:BusinessInTunisia:s2:title"},{icon:i.jsx(Q.Z,{}),titleKey:"Egypte:BusinessInTunisia:s3:title"},{icon:i.jsx(f.Z,{}),titleKey:"Egypte:BusinessInTunisia:s4:title"},{icon:i.jsx(P.Z,{}),titleKey:"Egypte:BusinessInTunisia:s5:title"},{icon:i.jsx(C.Z,{}),titleKey:"Egypte:BusinessInTunisia:s6:title"}];return i.jsx(j.Z,{titleKey:"Egypte:BusinessInTunisia:title",descriptionKey:"Egypte:BusinessInTunisia:description",sectors:t,t:e})};var O=A(88543);let I=function({t:e}){return i.jsx(O.Z,{title:e("Egypte:officeLocation:label"),subtitle:e("Egypte:officeLocation:title"),address:e("Egypte:officeLocation:address"),tel:e("Egypte:officeLocation:tel1"),email:e("Egypte:officeLocation:mail"),linkText:e("Egypte:officeLocation:talk"),mapSrc:"https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d6908.***********!2d31.232283!3d30.035415999999998!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x145840ccc19a9c45%3A0x9eaeba178c16fd08!2s8%20Al%20Bergas%2C%20Qasr%20El%20Nil%2C%20Cairo%20Governorate%204272015%2C%20%C3%89gypte!5e0!3m2!1sfr!2sus!4v1728580479664!5m2!1sfr!2sus"})};var D=A(32082),S=A(16412),T=A(5770),k=A(3662),N=A(94170);let Z=[{src:D.Z},{src:S.Z},{src:T.Z},{src:k.Z}],R=function({t:e}){return i.jsx(N.Z,{t:e,imgSlides:Z,translationKeyPrefix:"Egypte:EORServicesTN",Description:""})};var M=A(43207),U=A(68570);(0,U.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\labor-laws\labor-data\EgyptLaborData.jsx#LABOR_KEYS`),(0,U.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\labor-laws\labor-data\EgyptLaborData.jsx#egyptLaborData`);let _=(0,U.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\labor-laws\labor-data\EgyptLaborData.jsx#default`);var K=A(90606),H=A(44957),G=A(26105);async function V({params:{locale:e}}){let t=`https://www.pentabell.com/${"en"!==e?`${e}/`:""}guide-to-hiring-employees-in-egypt/`,A={fr:"https://www.pentabell.com/fr/guide-to-hiring-employees-in-egypt/",en:"https://www.pentabell.com/guide-to-hiring-employees-in-egypt/","x-default":"https://www.pentabell.com/guide-to-hiring-employees-in-egypt/"},{t:i}=await (0,M.Z)(e,["servicesByCountry"]);try{let i=await H.xk.get(`${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${e}/guide-to-hiring-employees-in-egypt`);if(i?.data?.status===200)return{title:i?.data?.data?.versions[0]?.metaTitle,description:i?.data?.data?.versions[0]?.metaDescription,robots:i?.data?.data?.robotMeta,alternates:{canonical:t,languages:A}}}catch(e){console.error("Error fetching SEO tags:",e)}return{title:i("servicesByCountry:egypt:metaTitle"),description:i("servicesByCountry:egypt:metaDescription"),alternates:{canonical:t,languages:A},robots:"follow, index, max-snippet:-1, max-image-preview:large"}}let J=async function({params:{locale:e}}){let{t}=await (0,M.Z)(e,["Tunisia","Egypte"]),A=[{id:"s1",title:t("Tunisia:services:dataS1:title"),description:t("Tunisia:services:dataS1:description"),link:`/${K.Bi.services.route}/${K.Bi.payrollServices.route}`,linkText:t("Tunisia:services:linkText"),img:G.Z,altImg:t("Tunisia:services:dataS1:altImg")},{id:"s2",title:t("Tunisia:services:dataS2:title"),description:t("Tunisia:services:dataS2:description"),link:`/${K.Bi.services.route}/${K.Bi.consultingServices.route}`,linkText:t("Tunisia:services:linkText"),img:g.Z,altImg:t("Tunisia:services:dataS2:altImg")},{id:"s3",title:t("Tunisia:services:dataS3:title"),description:t("Tunisia:services:dataS3:description"),link:`/${K.Bi.services.route}/${K.Bi.technicalAssistance.route}`,linkText:t("Tunisia:services:linkText"),img:d.Z,altImg:t("Tunisia:services:dataS3:altImg")},{id:"s4",title:t("Tunisia:services:dataS4:title"),description:t("Tunisia:services:dataS4:description"),link:`/${K.Bi.services.route}/${K.Bi.aiSourcing.route}`,linkText:t("Tunisia:services:linkText"),img:p.Z,altImg:t("Tunisia:services:dataS4:altImg")},{id:"s5",title:t("Tunisia:services:dataS5:title"),description:t("Tunisia:services:dataS5:description"),link:`/${K.Bi.services.route}/${K.Bi.directHiring.route}`,linkText:t("Tunisia:services:linkText"),img:l.Z,altImg:t("Tunisia:services:dataS5:altImg")}];return(0,i.jsxs)("div",{children:[i.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Organization",name:t("contactUs:bureux:contacts:egypte"),address:{"@type":"PostalAddress",streetAddress:"8 El Birgas street, Garden City",addressLocality:"Cairo",addressCountry:"EG"},telephone:"+33 1 73 07 42 54",email:"<EMAIL>",url:"en"===e?"https://www.pentabell.com/guide-to-hiring-employees-in-egypt/":`https://www.pentabell.com/${e}/guide-to-hiring-employees-in-egypt/`})}}),i.jsx(a.Z,{title:t("Egypte:title"),description:t("Egypte:description"),bannerImg:s,altImg:t("Egypte:altImg"),height:"100vh"}),i.jsx(r.Z,{disableTxt:!0}),i.jsx(n.Z,{title:t("Egypte:intro:title"),paragraph:t("Egypte:intro:description")}),i.jsx(B,{t:t}),i.jsx(z,{t:t}),i.jsx(R,{t:t}),i.jsx(I,{t:t}),i.jsx(c.Z,{t:t}),i.jsx(o.Z,{title:t("Egypte:services:title"),SERVICES:A,defaultImage:G.Z}),i.jsx(_,{}),i.jsx(E.Z,{country:"Egypt",defaultCountryPhone:"eg"})]})}},71279:(e,t,A)=>{"use strict";A.d(t,{Z:()=>d});var i=A(19510),s=A(13061),a=A(35986),r=A(64289),n=A(57559),o=A(74584),l=A(6340),p=A(84079);let d=function({t:e}){let t=[{icon:s.Z,title:e("Tunisia:complexity:dataS1:title"),description:e("Tunisia:complexity:dataS1:description")},{icon:a.Z,title:e("Tunisia:complexity:dataS2:title"),description:e("Tunisia:complexity:dataS2:description")},{icon:r.Z,title:e("Tunisia:complexity:dataS3:title"),description:e("Tunisia:complexity:dataS3:description")},{icon:l.Z,title:e("Tunisia:complexity:dataS4:title "),description:e("Tunisia:complexity:dataS4:description")},{icon:o.Z,title:e("Tunisia:complexity:dataS5:title"),description:e("Tunisia:complexity:dataS5:description")},{icon:n.Z,title:e("Tunisia:complexity:dataS6:title"),description:e("Tunisia:complexity:dataS6:description")}];return i.jsx(p.Z,{title1:e("Tunisia:complexity:title1"),title2:e("Tunisia:complexity:title2"),description:"",items:t})}},84079:(e,t,A)=>{"use strict";A.d(t,{Z:()=>a});var i=A(19510),s=A(55920);let a=function({title1:e,title2:t,items:A,description:a}){return(0,i.jsxs)(s.Z,{id:"approach-payroll-section",className:"custom-max-width",children:[(0,i.jsxs)("p",{className:"heading-h1 text-center",children:[e," ",i.jsx("br",{})," ",t]}),i.jsx("p",{className:"sub-heading text-center",children:a}),i.jsx("div",{className:"locations",children:A.map(({icon:e,title:t,description:A},s)=>(0,i.jsxs)("div",{className:"location-item",children:[(0,i.jsxs)("p",{className:"label",children:[" ",e&&(0,i.jsxs)("span",{children:[" ",i.jsx(e,{})]})," "," ",t]}),i.jsx("p",{className:"value paragraph",children:A})]},s))})]})}},94170:(e,t,A)=>{"use strict";A.d(t,{Z:()=>n});var i=A(19510),s=A(55920),a=A(28868);let r=(0,A(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\ui\emblaCarousel\EmblaCarouselThumbsTeamPic.jsx#default`),n=function({t:e,imgSlides:t=[],translationKeyPrefix:A="",Description:n}){return i.jsx(s.Z,{id:"eor-services-tn",className:"custom-max-width",children:(0,i.jsxs)(a.ZP,{className:"container",justifyContent:"space-between",container:!0,spacing:2,children:[i.jsx(a.ZP,{item:!0,xs:12,sm:6,children:i.jsx("div",{className:"team-thumbs",children:i.jsx(r,{slides:t,options:{}})})}),i.jsx(a.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsxs)("div",{className:"right-section",children:[i.jsx("p",{className:"paragraph text-yellow",children:e(`${A}:subTitle`)}),i.jsx("p",{className:"heading-h2 text-white",children:e(`${A}:title`)}),i.jsx("p",{className:"paragraph text-white",children:e(`${A}:description`)}),i.jsx("p",{className:"paragraph text-white",children:e(`${n}`)})]})})]})})}},5770:(e,t,A)=>{"use strict";A.d(t,{Z:()=>i});let i={src:"/_next/static/media/tn1.af454a8b.jpeg",height:960,width:1280,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wgARCAAGAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAT/xAAVAQEBAAAAAAAAAAAAAAAAAAABAv/aAAwDAQACEAMQAAABpEP/xAAWEAADAAAAAAAAAAAAAAAAAAACEhP/2gAIAQEAAQUCkbf/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oACAEDAQE/AX//xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oACAECAQE/AX//xAAYEAACAwAAAAAAAAAAAAAAAAABEQACUf/aAAgBAQAGPwJmwTyf/8QAGBAAAgMAAAAAAAAAAAAAAAAAAREAIUH/2gAIAQEAAT8hJWvCrE//2gAMAwEAAgADAAAAEAv/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oACAEDAQE/EH//xAAWEQADAAAAAAAAAAAAAAAAAAAAATH/2gAIAQIBAT8QdP/EABgQAQADAQAAAAAAAAAAAAAAAAEAETEh/9oACAEBAAE/EAFqOTo1J//Z",blurWidth:8,blurHeight:6}},54523:(e,t,A)=>{"use strict";A.r(t),A.d(t,{default:()=>i});let i={src:"/_next/static/media/tn1.af454a8b.jpeg",height:960,width:1280,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wgARCAAGAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAT/xAAVAQEBAAAAAAAAAAAAAAAAAAABAv/aAAwDAQACEAMQAAABpEP/xAAWEAADAAAAAAAAAAAAAAAAAAACEhP/2gAIAQEAAQUCkbf/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oACAEDAQE/AX//xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oACAECAQE/AX//xAAYEAACAwAAAAAAAAAAAAAAAAABEQACUf/aAAgBAQAGPwJmwTyf/8QAGBAAAgMAAAAAAAAAAAAAAAAAAREAIUH/2gAIAQEAAT8hJWvCrE//2gAMAwEAAgADAAAAEAv/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oACAEDAQE/EH//xAAWEQADAAAAAAAAAAAAAAAAAAAAATH/2gAIAQIBAT8QdP/EABgQAQADAQAAAAAAAAAAAAAAAAEAETEh/9oACAEBAAE/EAFqOTo1J//Z",blurWidth:8,blurHeight:6}},16412:(e,t,A)=>{"use strict";A.d(t,{Z:()=>i});let i={src:"/_next/static/media/tn3.a54e6b20.jpeg",height:960,width:1280,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wgARCAAGAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAH/xAAUAQEAAAAAAAAAAAAAAAAAAAAD/9oADAMBAAIQAxAAAAGgV//EABcQAAMBAAAAAAAAAAAAAAAAAAECAxT/2gAIAQEAAQUC0MLf/8QAFhEBAQEAAAAAAAAAAAAAAAAAAQAR/9oACAEDAQE/AQ2//8QAFhEBAQEAAAAAAAAAAAAAAAAAAQAh/9oACAECAQE/AXAv/8QAGBAAAgMAAAAAAAAAAAAAAAAAABEBITH/2gAIAQEABj8CWqoP/8QAGRABAAIDAAAAAAAAAAAAAAAAAQARITFh/9oACAEBAAE/IV5UEYNnZ//aAAwDAQACAAMAAAAQD//EABYRAQEBAAAAAAAAAAAAAAAAAAEAEf/aAAgBAwEBPxAk7f/EABcRAQADAAAAAAAAAAAAAAAAAAEAEVH/2gAIAQIBAT8QbQyf/8QAGRABAAIDAAAAAAAAAAAAAAAAAQARIUFh/9oACAEBAAE/EDzuMyRF6a3P/9k=",blurWidth:8,blurHeight:6}},84965:(e,t,A)=>{"use strict";A.r(t),A.d(t,{default:()=>i});let i={src:"/_next/static/media/tn3.a54e6b20.jpeg",height:960,width:1280,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wgARCAAGAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAH/xAAUAQEAAAAAAAAAAAAAAAAAAAAD/9oADAMBAAIQAxAAAAGgV//EABcQAAMBAAAAAAAAAAAAAAAAAAECAxT/2gAIAQEAAQUC0MLf/8QAFhEBAQEAAAAAAAAAAAAAAAAAAQAR/9oACAEDAQE/AQ2//8QAFhEBAQEAAAAAAAAAAAAAAAAAAQAh/9oACAECAQE/AXAv/8QAGBAAAgMAAAAAAAAAAAAAAAAAABEBITH/2gAIAQEABj8CWqoP/8QAGRABAAIDAAAAAAAAAAAAAAAAAQARITFh/9oACAEBAAE/IV5UEYNnZ//aAAwDAQACAAMAAAAQD//EABYRAQEBAAAAAAAAAAAAAAAAAAEAEf/aAAgBAwEBPxAk7f/EABcRAQADAAAAAAAAAAAAAAAAAAEAEVH/2gAIAQIBAT8QbQyf/8QAGRABAAIDAAAAAAAAAAAAAAAAAQARIUFh/9oACAEBAAE/EDzuMyRF6a3P/9k=",blurWidth:8,blurHeight:6}},32082:(e,t,A)=>{"use strict";A.d(t,{Z:()=>i});let i={src:"/_next/static/media/tn4.5ff5325d.jpeg",height:1200,width:1600,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wgARCAAGAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAL/xAAUAQEAAAAAAAAAAAAAAAAAAAAC/9oADAMBAAIQAxAAAAGwT//EABYQAAMAAAAAAAAAAAAAAAAAAAACEv/aAAgBAQABBQK3P//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQMBAT8Bf//EABURAQEAAAAAAAAAAAAAAAAAAAAB/9oACAECAQE/Aa//xAAYEAACAwAAAAAAAAAAAAAAAAAAARESIv/aAAgBAQAGPwK2YZ//xAAWEAEBAQAAAAAAAAAAAAAAAAARAIH/2gAIAQEAAT8hNGhv/9oADAMBAAIAAwAAABAP/8QAFREBAQAAAAAAAAAAAAAAAAAAABH/2gAIAQMBAT8Qr//EABYRAQEBAAAAAAAAAAAAAAAAABEAAf/aAAgBAgEBPxDYX//EABsQAAEEAwAAAAAAAAAAAAAAABEAASExQXGR/9oACAEBAAE/EIoIL5N9pf/Z",blurWidth:8,blurHeight:6}},3608:(e,t,A)=>{"use strict";A.r(t),A.d(t,{default:()=>i});let i={src:"/_next/static/media/tn4.5ff5325d.jpeg",height:1200,width:1600,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wgARCAAGAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAL/xAAUAQEAAAAAAAAAAAAAAAAAAAAC/9oADAMBAAIQAxAAAAGwT//EABYQAAMAAAAAAAAAAAAAAAAAAAACEv/aAAgBAQABBQK3P//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQMBAT8Bf//EABURAQEAAAAAAAAAAAAAAAAAAAAB/9oACAECAQE/Aa//xAAYEAACAwAAAAAAAAAAAAAAAAAAARESIv/aAAgBAQAGPwK2YZ//xAAWEAEBAQAAAAAAAAAAAAAAAAARAIH/2gAIAQEAAT8hNGhv/9oADAMBAAIAAwAAABAP/8QAFREBAQAAAAAAAAAAAAAAAAAAABH/2gAIAQMBAT8Qr//EABYRAQEBAAAAAAAAAAAAAAAAABEAAf/aAAgBAgEBPxDYX//EABsQAAEEAwAAAAAAAAAAAAAAABEAASExQXGR/9oACAEBAAE/EIoIL5N9pf/Z",blurWidth:8,blurHeight:6}},3662:(e,t,A)=>{"use strict";A.d(t,{Z:()=>i});let i={src:"/_next/static/media/tnh7.1129a33f.JPG",height:3024,width:4032,blurWidth:0,blurHeight:0}},56323:(e,t,A)=>{"use strict";A.r(t),A.d(t,{default:()=>i});let i={src:"/_next/static/media/Pentabell-Egypt.eae24556.webp",height:738,width:1440,blurDataURL:"data:image/webp;base64,UklGRkgAAABXRUJQVlA4IDwAAABQAgCdASoIAAQAAkA4JQBOj+DOAPX/A2f9dAAA/slCycwhy+ngk2RwUTvBdpSVoC4+dcPN/zs8L5CjQAA=",blurWidth:8,blurHeight:4}}};var t=require("../../../../webpack-runtime.js");t.C(e);var A=e=>t(t.s=e),i=t.X(0,[8948,1479,1619,1336,4227,8077,6027,3718,5560,6636,9645,4289,1692,9712,8582,2510,9433,481,9984,1812,3969,4903,8031,3195,6345,2601,7619,1503],()=>A(32513));module.exports=i})();