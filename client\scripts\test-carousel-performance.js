#!/usr/bin/env node

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

console.log('🎠 Carousel Performance Testing Suite');
console.log('====================================\n');

// Test configuration
const TEST_CONFIG = {
  url: process.env.TEST_URL || 'http://localhost:3000',
  viewport: {
    width: 1200,
    height: 800
  },
  mobileViewport: {
    width: 390,
    height: 844
  },
  timeout: 30000,
  carouselSelector: '#home__slider',
  imageSelector: '#home__slider img'
};

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  loadTime: 3000,        // 3 seconds
  firstImageLoad: 1000,  // 1 second
  layoutShift: 0.1,      // CLS score
  renderTime: 100,       // 100ms
  interactionDelay: 50   // 50ms
};

// Test carousel performance
async function testCarouselPerformance() {
  let browser;
  
  try {
    console.log('🚀 Starting browser...');
    browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const results = {
      desktop: await testDevice(browser, 'desktop'),
      mobile: await testDevice(browser, 'mobile')
    };

    // Generate report
    generateReport(results);
    
    console.log('\n🎉 Performance testing completed!');
    return results;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    throw error;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Test specific device
async function testDevice(browser, deviceType) {
  console.log(`\n📱 Testing ${deviceType} performance...`);
  
  const page = await browser.newPage();
  
  try {
    // Set viewport
    const viewport = deviceType === 'mobile' ? 
      TEST_CONFIG.mobileViewport : 
      TEST_CONFIG.viewport;
    
    await page.setViewport(viewport);

    // Enable performance monitoring
    await page.evaluateOnNewDocument(() => {
      window.performanceMetrics = {
        startTime: Date.now(),
        loadTime: 0,
        firstImageLoad: 0,
        layoutShifts: [],
        renderTime: 0
      };

      // Monitor layout shifts
      if ('LayoutShift' in window && PerformanceObserver.supportedEntryTypes.includes('layout-shift')) {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              window.performanceMetrics.layoutShifts.push(entry.value);
            }
          }
        });
        observer.observe({ entryTypes: ['layout-shift'] });
      }
    });

    // Navigate to page
    console.log(`   📄 Loading page: ${TEST_CONFIG.url}`);
    const startTime = Date.now();
    
    await page.goto(TEST_CONFIG.url, { 
      waitUntil: 'networkidle0',
      timeout: TEST_CONFIG.timeout 
    });

    // Wait for carousel to be present
    console.log('   🎠 Waiting for carousel...');
    await page.waitForSelector(TEST_CONFIG.carouselSelector, { 
      timeout: 10000 
    });

    // Measure carousel load time
    const carouselLoadTime = Date.now() - startTime;

    // Wait for first image to load
    console.log('   🖼️ Waiting for first image...');
    await page.waitForFunction(() => {
      const firstImg = document.querySelector('#home__slider img');
      return firstImg && firstImg.complete && firstImg.naturalHeight !== 0;
    }, { timeout: 10000 });

    const firstImageLoadTime = Date.now() - startTime;

    // Measure layout shifts
    const layoutShifts = await page.evaluate(() => {
      const shifts = window.performanceMetrics?.layoutShifts || [];
      return shifts.reduce((sum, shift) => sum + shift, 0);
    });

    // Test interaction performance
    console.log('   🖱️ Testing interactions...');
    const interactionDelay = await testInteractions(page);

    // Measure render performance
    const renderMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart
      };
    });

    const results = {
      deviceType,
      viewport,
      loadTime: carouselLoadTime,
      firstImageLoad: firstImageLoadTime,
      layoutShifts: layoutShifts,
      renderTime: renderMetrics.domContentLoaded,
      interactionDelay: interactionDelay,
      timestamp: new Date().toISOString()
    };

    // Evaluate performance
    const grade = evaluatePerformance(results);
    results.grade = grade;

    console.log(`   📊 Performance Grade: ${grade}`);
    console.log(`   ⏱️ Load Time: ${carouselLoadTime}ms`);
    console.log(`   🖼️ First Image: ${firstImageLoadTime}ms`);
    console.log(`   📐 Layout Shifts: ${layoutShifts.toFixed(4)}`);
    console.log(`   🎨 Render Time: ${renderMetrics.domContentLoaded}ms`);
    console.log(`   🖱️ Interaction Delay: ${interactionDelay}ms`);

    return results;

  } catch (error) {
    console.error(`   ❌ ${deviceType} test failed:`, error.message);
    throw error;
  } finally {
    await page.close();
  }
}

// Test carousel interactions
async function testInteractions(page) {
  try {
    // Test next button click
    const startTime = Date.now();
    
    await page.click('.embla__button.right', { delay: 10 });
    
    // Wait for transition to complete
    await page.waitForTimeout(500);
    
    const endTime = Date.now();
    return endTime - startTime;
    
  } catch (error) {
    console.warn('   ⚠️ Interaction test failed:', error.message);
    return 0;
  }
}

// Evaluate performance grade
function evaluatePerformance(results) {
  let score = 100;

  // Deduct points for poor metrics
  if (results.loadTime > PERFORMANCE_THRESHOLDS.loadTime) score -= 20;
  if (results.firstImageLoad > PERFORMANCE_THRESHOLDS.firstImageLoad) score -= 15;
  if (results.layoutShifts > PERFORMANCE_THRESHOLDS.layoutShift) score -= 25;
  if (results.renderTime > PERFORMANCE_THRESHOLDS.renderTime) score -= 10;
  if (results.interactionDelay > PERFORMANCE_THRESHOLDS.interactionDelay) score -= 10;

  if (score >= 90) return 'A';
  else if (score >= 80) return 'B';
  else if (score >= 70) return 'C';
  else if (score >= 60) return 'D';
  else return 'F';
}

// Generate performance report
function generateReport(results) {
  console.log('\n📊 Performance Report');
  console.log('====================');

  const report = {
    timestamp: new Date().toISOString(),
    testConfig: TEST_CONFIG,
    thresholds: PERFORMANCE_THRESHOLDS,
    results: results,
    summary: {
      desktop: {
        grade: results.desktop.grade,
        passed: results.desktop.grade <= 'B',
        issues: getPerformanceIssues(results.desktop)
      },
      mobile: {
        grade: results.mobile.grade,
        passed: results.mobile.grade <= 'B',
        issues: getPerformanceIssues(results.mobile)
      }
    }
  };

  // Save report to file
  const reportPath = path.join(__dirname, '..', 'performance-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

  console.log(`\n📄 Report saved to: ${reportPath}`);
  
  // Display summary
  console.log('\n📋 Summary:');
  console.log(`Desktop: ${results.desktop.grade} ${report.summary.desktop.passed ? '✅' : '❌'}`);
  console.log(`Mobile:  ${results.mobile.grade} ${report.summary.mobile.passed ? '✅' : '❌'}`);

  // Display issues
  const allIssues = [...report.summary.desktop.issues, ...report.summary.mobile.issues];
  if (allIssues.length > 0) {
    console.log('\n⚠️ Performance Issues:');
    allIssues.forEach(issue => console.log(`   • ${issue}`));
  } else {
    console.log('\n✅ No performance issues detected!');
  }
}

// Get performance issues
function getPerformanceIssues(results) {
  const issues = [];

  if (results.loadTime > PERFORMANCE_THRESHOLDS.loadTime) {
    issues.push(`Slow load time: ${results.loadTime}ms (threshold: ${PERFORMANCE_THRESHOLDS.loadTime}ms)`);
  }

  if (results.firstImageLoad > PERFORMANCE_THRESHOLDS.firstImageLoad) {
    issues.push(`Slow first image load: ${results.firstImageLoad}ms (threshold: ${PERFORMANCE_THRESHOLDS.firstImageLoad}ms)`);
  }

  if (results.layoutShifts > PERFORMANCE_THRESHOLDS.layoutShift) {
    issues.push(`High layout shifts: ${results.layoutShifts.toFixed(4)} (threshold: ${PERFORMANCE_THRESHOLDS.layoutShift})`);
  }

  if (results.renderTime > PERFORMANCE_THRESHOLDS.renderTime) {
    issues.push(`Slow render time: ${results.renderTime}ms (threshold: ${PERFORMANCE_THRESHOLDS.renderTime}ms)`);
  }

  if (results.interactionDelay > PERFORMANCE_THRESHOLDS.interactionDelay) {
    issues.push(`High interaction delay: ${results.interactionDelay}ms (threshold: ${PERFORMANCE_THRESHOLDS.interactionDelay}ms)`);
  }

  return issues;
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Performance testing interrupted by user');
  process.exit(0);
});

// Run the tests
if (require.main === module) {
  testCarouselPerformance()
    .then(() => {
      console.log('\n🎉 All tests completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Performance testing failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testCarouselPerformance };
