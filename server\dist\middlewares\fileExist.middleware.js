"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileExistMiddleware = fileExistMiddleware;
const files_model_1 = __importDefault(require("../apis/storage/files.model"));
async function fileExistMiddleware(req, res, next) {
    const uuid = req.params.filename;
    const findFile = await files_model_1.default.findOne({ uuid: uuid });
    if (findFile)
        return res.status(409).json({ message: 'File already exists' });
    next();
}
//# sourceMappingURL=fileExist.middleware.js.map