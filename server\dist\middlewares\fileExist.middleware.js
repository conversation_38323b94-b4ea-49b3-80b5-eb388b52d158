"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileExistMiddleware = fileExistMiddleware;
const files_model_1 = __importDefault(require("../apis/storage/files.model"));
const messages_1 = require("@/utils/helpers/messages");
async function fileExistMiddleware(req, res, next) {
    const uuid = req.params.filename;
    const findFile = await files_model_1.default.findOne({ uuid: uuid });
    if (findFile)
        return res.status(409).json({ message: messages_1.MESSAGES.FILE.ALREADY_EXISTS });
    next();
}
//# sourceMappingURL=fileExist.middleware.js.map