"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_locales_fr_sliders_json"],{

/***/ "(app-pages-browser)/./src/locales/fr/sliders.json":
/*!*************************************!*\
  !*** ./src/locales/fr/sliders.json ***!
  \*************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"title":"Titre","link":"Lien","visibility":"Visibilité","sliderslist":"Liste des sliders","availablelanguage":"Langue existante","addslider":"Ajouter slider","createslider":"Ajouter slider","addimgmobile":"Ajouter Image Mobile","imgmobile":"Image Mobile","Alt":"Alt Image","editslider":"Modifier slider","updateorder":"Modifier l\'ordre"}');

/***/ })

}]);