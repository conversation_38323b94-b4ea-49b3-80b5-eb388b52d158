"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/AddArticleFroala.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _AddArticleEN__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AddArticleEN */ \"(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\");\n/* harmony import */ var _AddArticleFR__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AddArticleFR */ \"(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx\");\n/* harmony import */ var _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_16__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AddArticle = ()=>{\n    _s();\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const savedArticle = localStorage.getItem(\"savedArticle\");\n    const useUpdateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave)();\n    const useCreateArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle)();\n    const useCreateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave)();\n    const useSaveFileHook = (0,_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentYear = new Date().getFullYear();\n    const formikRefAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(`panel`);\n    const [formdataEN, setFormDataEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [formdataFR, setFormDataFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const formdata = new FormData();\n    const formdatafr = new FormData();\n    const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedEnCategories, setSelectedEnCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredFrCategories, setFilteredFrCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidPhotoEN, setUuidPhotoEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uuidPhotoFR, setUuidPhotoFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedLanguages, setSelectedLanguages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        en: true,\n        fr: false\n    });\n    const [categoriesEN, setCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesFR, setCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesEN, setFilteredCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesFR, setFilteredCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesEN, setSelectedCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesFR, setSelectedCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const getCategoriesEN = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"en\");\n    const getCategoriesFR = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"fr\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesEN.data?.categories) {\n            const transformedCategories = getCategoriesEN.data.categories.map((category)=>({\n                    id: category.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesEN(transformedCategories);\n        }\n    }, [\n        getCategoriesEN.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesFR.data?.categories) {\n            const transformedCategories = getCategoriesFR.data.categories.map((category)=>({\n                    id: category?.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesFR(transformedCategories);\n        }\n    }, [\n        getCategoriesFR.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedCategoriesEN.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesEN, \"en\");\n        } else if (selectedCategoriesFR.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesFR, \"fr\");\n        }\n    }, [\n        selectedCategoriesEN,\n        selectedCategoriesFR\n    ]);\n    const fetchTranslatedCategories = async (selectedCategories, language)=>{\n        try {\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_12__.axiosGetJson.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.categories}/${language}/${selectedCategories}`);\n            if (language === \"en\") {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesFR(transformedCategories);\n            } else {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesEN(transformedCategories);\n            }\n        } catch (error) {\n            console.error(\"Error fetching translated categories:\", error);\n        }\n    };\n    const handleImageSelect = async (selectedFile, language)=>{\n        if (language === \"en\") {\n            let uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_17__[\"default\"])().replace(/-/g, \"\");\n            setUuidPhotoEN(uuidPhotos);\n            formdata.append(\"file\", selectedFile);\n            setFormDataEN(formdata);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n        } else if (language === \"fr\") {\n            let uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_17__[\"default\"])().replace(/-/g, \"\");\n            setUuidPhotoFR(uuidPhotos);\n            formdatafr.append(\"file\", selectedFile);\n            setFormDataFR(formdatafr);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n        }\n    };\n    const titleEN = localStorage.getItem(\"title\");\n    const metaTitleEN = localStorage.getItem(\"metatitle\");\n    const metaDescriptionEN = localStorage.getItem(\"metaDescription\");\n    const contentEN = localStorage.getItem(\"content\");\n    const contentFR = localStorage.getItem(\"contentfr\");\n    const titleFR = localStorage.getItem(\"titlefr\");\n    const metaDescriptionFR = localStorage.getItem(\"metaDescriptionfr\");\n    const metaTitleFR = localStorage.getItem(\"metatitlefr\");\n    const initialValues = {\n        robotsMeta: \"index\",\n        metaTitleEN: metaTitleEN ? JSON.parse(metaTitleEN) : \"\",\n        metaDescriptionEN: metaDescriptionEN ? JSON.parse(metaDescriptionEN) : \"\",\n        descriptionEN: \"\",\n        visibilityEN: \"\",\n        categoryEN: [],\n        imageEN: null,\n        keywordsEN: \"\",\n        titleEN: titleEN ? JSON.parse(titleEN) : \"\",\n        urlEN: \"\",\n        altEN: \"\",\n        contentEN: contentEN ? JSON.parse(contentEN) : \"\",\n        highlightsEN: [],\n        publishDateEN: \"\",\n        faqTitleEN: \"\",\n        faqEN: [],\n        titleFR: titleFR ? JSON.parse(titleFR) : \"\",\n        metaTitleFR: metaTitleFR ? JSON.parse(metaTitleFR) : \"\",\n        metaDescriptionFR: metaDescriptionFR ? JSON.parse(metaDescriptionFR) : \"\",\n        descriptionFR: \"\",\n        visibilityFR: \"\",\n        categoryFR: [],\n        imageFR: null,\n        keywordsFR: \"\",\n        urlFR: \"\",\n        altFR: \"\",\n        contentFR: contentFR ? JSON.parse(contentFR) : \"\",\n        publishDateFR: \"\",\n        highlightsFR: [],\n        faqTitleFR: \"\",\n        faqFR: []\n    };\n    const validationSchema = yup__WEBPACK_IMPORTED_MODULE_3__.object().shape({\n        ...selectedLanguages.en && {\n            metaTitleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            category: yup__WEBPACK_IMPORTED_MODULE_3__.array().min(1, t(\"validations:minCategory\")),\n            visibilityEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        },\n        ...selectedLanguages.fr && {\n            metaTitleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            visibilityFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        }\n    });\n    const uploadFile = (filename, file, formData, lang)=>{\n        return new Promise((resolve, reject)=>{\n            useSaveFileHook.mutate({\n                resource: \"blogs\",\n                folder: currentYear,\n                filename,\n                body: {\n                    formData,\n                    t\n                }\n            }, {\n                onSuccess: (data)=>{\n                    if (data.message === \"uuid exist\") {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    } else {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    }\n                },\n                onError: (error)=>{\n                    console.error(`Error uploading ${lang} image:`, error);\n                    resolve({\n                        lang,\n                        uuid: null\n                    });\n                }\n            });\n        });\n    };\n    const handleSubmit = async (values)=>{\n        const data = {\n            robotsMeta: values.robotsMeta,\n            versions: []\n        };\n        if (!selectedLanguages.en && !selectedLanguages.fr) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select at least one version!\");\n            return;\n        }\n        try {\n            let resultEN, resultFR;\n            if (selectedLanguages.en) {\n                resultEN = await uploadFile(uuidPhotoEN, uuidPhotoFileNameEN, formdataEN, \"English\");\n            }\n            if (selectedLanguages.fr) {\n                resultFR = await uploadFile(uuidPhotoFR, uuidPhotoFileNameFR, formdataFR, \"French\");\n            }\n            if (selectedLanguages.en && resultEN.uuid) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    description: values.descriptionEN,\n                    url: values.urlEN,\n                    visibility: values.visibilityEN,\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    highlights: values.highlightsEN,\n                    image: resultEN.uuid,\n                    category: values.categoryEN === \"\" ? [] : values.categoryEN,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr && resultFR.uuid) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: values.visibilityFR,\n                    description: values.descriptionFR,\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    highlights: values.highlightsFR,\n                    image: resultFR.uuid,\n                    category: values.categoryFR === \"\" ? [] : values.categoryFR,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (data.versions.length > 0) {\n                if (isSavedArticle !== \"\") {\n                    useUpdateAutoSaveHook.mutate({\n                        data: data,\n                        id: isSavedArticle\n                    }, {\n                        onSuccess: ()=>{\n                            localStorage.removeItem(\"title\");\n                            localStorage.removeItem(\"content\");\n                            localStorage.removeItem(\"titlefr\");\n                            localStorage.removeItem(\"contentfr\");\n                            localStorage.removeItem(\"metaDescription\");\n                            localStorage.removeItem(\"metaDescriptionfr\");\n                            localStorage.removeItem(\"metatitle\");\n                            localStorage.removeItem(\"metatitlefr\");\n                            localStorage.removeItem(\"savedArticle\");\n                        //window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        }\n                    });\n                } else {\n                    useCreateArticleHook.mutate({\n                        data\n                    }, {\n                        onSuccess: ()=>{\n                            localStorage.removeItem(\"title\");\n                            localStorage.removeItem(\"content\");\n                            localStorage.removeItem(\"titlefr\");\n                            localStorage.removeItem(\"contentfr\");\n                            localStorage.removeItem(\"metaDescription\");\n                            localStorage.removeItem(\"metaDescriptionfr\");\n                            localStorage.removeItem(\"metatitle\");\n                            localStorage.removeItem(\"metatitlefr\");\n                            localStorage.removeItem(\"savedArticle\");\n                        // window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        }\n                    });\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No valid image uploads found. Article not created.\");\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"An error occurred while processing uploads.\");\n        }\n    };\n    const handleChangeAccordion = (panel)=>(event, newExpanded)=>{\n            setExpanded(newExpanded ? panel : false);\n        };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchFrenchCategories = async ()=>{\n            const response = await axios.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.categories}/en/${selectedEnCategories}`);\n            setFilteredFrCategories(response.data);\n        };\n        if (selectedEnCategories.length > 0) {\n            fetchFrenchCategories();\n        } else {\n            setFilteredFrCategories([]);\n        }\n    }, [\n        selectedEnCategories\n    ]);\n    const handleCategoriesENSelect = (selectedCategories)=>{\n        setSelectedCategoriesEN(selectedCategories);\n    };\n    const handleCategoriesFRSelect = (selectedCategories)=>{\n        setSelectedCategoriesFR(selectedCategories);\n    };\n    const [isSavedArticle, setIsSavedArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedArticle ? savedArticle : \"\");\n    const isSavedArticleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isSavedArticle);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        isSavedArticleRef.current = isSavedArticle;\n    }, [\n        isSavedArticle\n    ]);\n    const autosave = async ()=>{\n        const values = formikRefAll.current?.values;\n        const data = {\n            robotsMeta: values.robotsMeta,\n            versions: []\n        };\n        if (selectedLanguages.en) {\n            data.versions.push({\n                language: \"en\",\n                metaTitle: values.metaTitleEN,\n                title: values.titleEN,\n                metaDescription: values.metaDescriptionEN,\n                url: values.urlEN,\n                visibility: \"Draft\",\n                publishDate: values.publishDateEN,\n                content: values.contentEN,\n                alt: values.altEN,\n                keywords: values.keywordsEN,\n                category: values.categoryEN,\n                highlights: values.highlightsEN,\n                faqTitle: values.faqTitleEN || \"\",\n                faq: values.faqEN || []\n            });\n        }\n        if (selectedLanguages.fr) {\n            data.versions.push({\n                language: \"fr\",\n                metaTitle: values.metaTitleFR,\n                title: values.titleFR,\n                metaDescription: values.metaDescriptionFR,\n                url: values.urlFR,\n                visibility: \"Draft\",\n                publishDate: values.publishDateFR,\n                content: values.contentFR,\n                alt: values.altFR,\n                keywords: values.keywordsFR,\n                category: values.categoryFR,\n                highlights: values.highlightsFR,\n                faqTitle: values.faqTitleFR || \"\",\n                faq: values.faqFR || []\n            });\n        }\n        if (isSavedArticleRef.current != \"\") {\n            useUpdateAutoSaveHook.mutate({\n                data: data,\n                id: isSavedArticleRef.current\n            });\n        } else {\n            if (data.versions.length > 0) {\n                useCreateAutoSaveHook.mutate({\n                    data\n                }, {\n                    onSuccess: (data)=>{\n                        setIsSavedArticle(data.articleId);\n                        localStorage.setItem(\"savedArticle\", data.articleId);\n                    }\n                });\n            }\n        }\n    };\n    const handleChange = lodash_debounce__WEBPACK_IMPORTED_MODULE_16___default()(autosave, 120000);\n    const handleClear = ()=>{\n        formikRefAll.current?.resetForm();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"createArticle:addArticle\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"commun\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.en,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            en: !prev.en\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"English\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.fr,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            fr: !prev.fr\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"French\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"experiences\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"form\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_18__.Formik, {\n                                        initialValues: initialValues,\n                                        validationSchema: validationSchema,\n                                        innerRef: formikRefAll,\n                                        onSubmit: handleSubmit,\n                                        className: \"formik-form\",\n                                        children: (param)=>{\n                                            let { errors, touched, setFieldValue, values } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_18__.Form, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        id: \"accordion\",\n                                                        disableGutters: true,\n                                                        expanded: expanded === `panel`,\n                                                        onChange: handleChangeAccordion(`panel`),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 39\n                                                                }, void 0),\n                                                                \"aria-controls\": `panel-content`,\n                                                                id: `panel-header`,\n                                                                children: t(\"createArticle:settings\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"accordion-detail\",\n                                                                elevation: 0,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"label-form\",\n                                                                                    children: [\n                                                                                        \"Robots meta\",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                            className: \"select-pentabell\",\n                                                                                            variant: \"standard\",\n                                                                                            sx: {\n                                                                                                m: 1,\n                                                                                                minWidth: 120\n                                                                                            },\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                                value: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.filter((option)=>values.robotsMeta === option),\n                                                                                                selected: values.robotsMeta,\n                                                                                                onChange: (event)=>{\n                                                                                                    setFieldValue(\"robotsMeta\", event.target.value);\n                                                                                                },\n                                                                                                children: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                                        value: item,\n                                                                                                        children: item\n                                                                                                    }, index, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                        lineNumber: 549,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                lineNumber: 536,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 531,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_18__.ErrorMessage, {\n                                                                                            className: \"label-error\",\n                                                                                            name: \"robotsMeta\",\n                                                                                            component: \"div\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 555,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                    lineNumber: 529,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 528,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_18__.ErrorMessage, {\n                                                                                name: \"robotsMeta\",\n                                                                                component: \"div\",\n                                                                                className: \"label-error\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 563,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                        lineNumber: 527,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, `panel`, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    selectedLanguages.en && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleEN__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        debounce: handleChange,\n                                                        categories: categoriesEN,\n                                                        filteredCategories: filteredCategoriesEN,\n                                                        onCategoriesSelect: handleCategoriesENSelect\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    selectedLanguages.fr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleFR__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        categories: categoriesFR,\n                                                        filteredCategories: filteredCategoriesFR,\n                                                        onCategoriesSelect: handleCategoriesFRSelect,\n                                                        debounce: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"btn-container\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"button\",\n                                                                text: \"Clear\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: handleClear\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"submit\",\n                                                                text: \"Save\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: ()=>{}\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 497,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                        lineNumber: 468,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                    lineNumber: 467,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AddArticle, \"57/0ap54x2D1ClkLvO5TpuJwM1g=\", false, function() {\n    return [\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave,\n        _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories\n    ];\n});\n_c = AddArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticle);\nvar _c;\n$RefreshReg$(_c, \"AddArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx\n"));

/***/ })

});