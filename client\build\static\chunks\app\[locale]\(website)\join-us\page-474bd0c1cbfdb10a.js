(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4889],{23913:function(e,i,a){"use strict";var t,s,n,l=a(94746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var i=1;i<arguments.length;i++){var a=arguments[i];for(var t in a)({}).hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e}).apply(null,arguments)}i.Z=e=>l.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:48,height:49,fill:"none"},e),t||(t=l.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M24 25a1.5 1.5 0 0 1 1.5 1.5v16a1.5 1.5 0 0 1-3 0v-16A1.5 1.5 0 0 1 24 25",clipRule:"evenodd"})),s||(s=l.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M22.573 8.869A12.5 12.5 0 0 0 9.072 29.247a1.5 1.5 0 1 1-2.144 2.099 15.5 15.5 0 1 1 25.566-16.347H35a10.501 10.501 0 0 1 5.834 19.23 1.5 1.5 0 1 1-1.666-2.494A7.5 7.5 0 0 0 35 18h-3.58a1.5 1.5 0 0 1-1.438-1.071 12.5 12.5 0 0 0-7.409-8.06",clipRule:"evenodd"})),n||(n=l.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M22.94 25.44a1.5 1.5 0 0 1 2.12 0l8 8a1.5 1.5 0 0 1-2.12 2.12L24 28.622l-6.94 6.94a1.5 1.5 0 0 1-2.12-2.122z",clipRule:"evenodd"})))},18155:function(e,i,a){Promise.resolve().then(a.bind(a,98489)),Promise.resolve().then(a.bind(a,33466)),Promise.resolve().then(a.bind(a,3199)),Promise.resolve().then(a.bind(a,62842)),Promise.resolve().then(a.bind(a,54675)),Promise.resolve().then(a.bind(a,32912)),Promise.resolve().then(a.bind(a,93257)),Promise.resolve().then(a.bind(a,41774)),Promise.resolve().then(a.bind(a,24250)),Promise.resolve().then(a.bind(a,23390))},3199:function(e,i,a){"use strict";a.d(i,{default:function(){return d}});var t=a(57437),s=a(98489),n=a(41774),l=a(55788);a(33145),a(80657);var r=a(27648),d=function(e){let{bannerImg:i,height:a,altImg:d}=e,{t:o}=(0,l.$G)();return(0,t.jsxs)("div",{id:"join-us-banner",className:"center-banner",style:{backgroundImage:`url(${i.src})`,height:a},children:[d&&(0,t.jsx)("img",{width:0,height:0,alt:d,src:"",style:{display:"none"},loading:"lazy"}),(0,t.jsxs)(s.default,{className:"top-section custom-max-width",children:[(0,t.jsxs)("h1",{className:"heading-h1 text-white",children:[o("joinUs:intro:title1"),(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:"heading-h2 text-yellow",children:o("joinUs:intro:title2")})]}),(0,t.jsx)("p",{className:"sub-heading text-white  ",children:o("joinUs:intro:description")}),(0,t.jsx)(r.default,{href:"#service-page-form",style:{textDecoration:"none"},children:(0,t.jsx)(n.default,{text:o("joinUs:intro:button"),className:"btn btn-filled"})})]}),(0,t.jsx)("div",{className:"avatars",style:{backgroundImage:"url(/_next/static/media/team1.f6350403.png)"}}),(0,t.jsx)("div",{className:"avatars last-one",style:{backgroundImage:"url(/_next/static/media/team2.b9c289c9.png)"}})]})}},62842:function(e,i,a){"use strict";a.d(i,{default:function(){return l}});var t=a(57437),s=a(98489),n=a(55788),l=function(){let{t:e}=(0,n.$G)();return(0,t.jsx)("div",{id:"candidate-journey-section",className:"light-bg",children:(0,t.jsxs)(s.default,{className:"custom-max-width",children:[(0,t.jsx)("h2",{className:"heading-h1 text-blue text-center",children:e("joinUs:candidateJourney:title")}),(0,t.jsxs)("div",{className:"support-reasons",children:[(0,t.jsxs)("div",{className:"support-reason",children:[(0,t.jsx)("div",{className:"img",children:(0,t.jsx)("img",{src:"/_next/static/media/candJourneyImg1.01b88191.png",alt:"Candidate Journey 1",loading:"lazy"})}),(0,t.jsx)("p",{className:"support-reason-title",children:e("joinUs:candidateJourney:item1:title")}),(0,t.jsx)("p",{className:"support-reason-item",children:e("joinUs:candidateJourney:item1:description")})]}),(0,t.jsxs)("div",{className:"support-reason",children:[(0,t.jsx)("div",{className:"img",children:(0,t.jsx)("img",{width:303,height:103,src:"/_next/static/media/candJourneyImg2.cc40dace.png",alt:"Candidate Journey 2",loading:"lazy"})}),(0,t.jsx)("p",{className:"support-reason-title",children:e("joinUs:candidateJourney:item2:title")}),(0,t.jsx)("p",{className:"support-reason-item",children:e("joinUs:candidateJourney:item2:description")})]}),(0,t.jsxs)("div",{className:"support-reason",children:[(0,t.jsxs)("div",{className:"img",children:[" ",(0,t.jsx)("img",{width:303,height:103,src:"/_next/static/media/candJourneyImg3.0162c59d.png",alt:"Candidate Journey 2",loading:"lazy"})]}),(0,t.jsx)("p",{className:"support-reason-title",children:e("joinUs:candidateJourney:item3:title")}),(0,t.jsx)("p",{className:"support-reason-item",children:e("joinUs:candidateJourney:item2:description")})]}),(0,t.jsxs)("div",{className:"support-reason",children:[(0,t.jsx)("div",{className:"img",children:(0,t.jsx)("img",{width:303,height:103,src:"/_next/static/media/candJourneyImg4.cb445ed9.png",alt:"Candidate Journey 3",loading:"lazy"})}),(0,t.jsx)("p",{className:"support-reason-title",children:e("joinUs:candidateJourney:item4:title")}),(0,t.jsx)("p",{className:"support-reason-item",children:e("joinUs:candidateJourney:item4:description")})]})]})]})})}},54675:function(e,i,a){"use strict";var t=a(57437),s=a(98489),n=a(55788);i.default=function(){let{t:e}=(0,n.$G)();return(0,t.jsxs)(s.default,{id:"approach-payroll-section",className:"custom-max-width",children:[(0,t.jsxs)("h2",{className:"heading-h1 text-center",children:[" ",e("joinUs:gloablBenefits:title")]}),(0,t.jsxs)("div",{className:"locations",children:[(0,t.jsxs)("div",{className:"location-item four-items",children:[(0,t.jsx)("p",{className:"label",children:e("joinUs:gloablBenefits:diversity:title")}),(0,t.jsx)("p",{className:"value paragraph",children:e("joinUs:gloablBenefits:diversity:description")})]}),(0,t.jsxs)("div",{className:"location-item four-items",children:[(0,t.jsx)("p",{className:"label",children:e("joinUs:gloablBenefits:health:title")}),(0,t.jsx)("p",{className:"value paragraph",children:e("joinUs:gloablBenefits:health:description")})]}),(0,t.jsxs)("div",{className:"location-item four-items",children:[(0,t.jsx)("p",{className:"label",children:e("joinUs:gloablBenefits:engaging:title")}),(0,t.jsx)("p",{className:"value paragraph",children:e("joinUs:gloablBenefits:engaging:description")})]}),(0,t.jsxs)("div",{className:"location-item four-items",children:[(0,t.jsxs)("p",{className:"label",children:[" ",e("joinUs:gloablBenefits:career:title")]}),(0,t.jsxs)("p",{className:"value paragraph",children:[e("joinUs:gloablBenefits:career:description")," "]})]})]})]})}},32912:function(e,i,a){"use strict";a.d(i,{default:function(){return n}});var t=a(57437),s=a(55788);a(33145);var n=function(){let{t:e}=(0,s.$G)();return(0,t.jsxs)("div",{id:"team",children:[(0,t.jsx)("h2",{className:"heading-h1 text-white text-center",children:e("joinUs:dreamTeam")}),(0,t.jsx)("div",{className:"team-img",children:(0,t.jsx)("img",{width:1429,height:666,src:"/_next/static/media/teamPic.de89c27d.webp",alt:"pentabell teamPic",loading:"lazy"})})]})}},93257:function(e,i,a){"use strict";var t=a(57437),s=a(98489),n=a(89414),l=a(41774),r=a(55788),d=a(80657);i.default=function(e){let{locale:i}=e,{t:a}=(0,r.$G)();return(0,t.jsx)(s.default,{id:"our-culture-location",className:"custom-max-width",children:(0,t.jsxs)(n.default,{className:"container",container:!0,columnSpacing:0,children:[(0,t.jsx)(n.default,{item:!0,xs:12,sm:5,children:(0,t.jsxs)("div",{className:"culture-section",children:[(0,t.jsx)("p",{className:"heading-h2 text-white",children:a("joinUs:ourCulture:title")}),(0,t.jsx)("p",{className:"paragraph text-white",children:a("joinUs:ourCulture:description")}),(0,t.jsx)(l.default,{text:a("joinUs:ourCulture:readMore"),link:`/${d.Bi.aboutUs.route}`,className:"btn btn-outlined white",aHref:!0})]})}),(0,t.jsx)(n.default,{item:!0,xs:12,sm:5,children:(0,t.jsxs)("div",{className:"location-section",children:[(0,t.jsx)("p",{className:"heading-h2 text-white",children:a("joinUs:ourLocation:title")}),(0,t.jsx)("p",{className:"paragraph text-white",children:a("joinUs:ourLocation:description")}),(0,t.jsx)(l.default,{text:a("joinUs:ourLocation:readMore"),link:`/${d.Bi.contact.route}#our-location-section`,className:"btn btn-outlined white",aHref:!0})]})})]})})}},30100:function(e,i,a){"use strict";a.d(i,{Z:function(){return s}});var t=a(57437);function s(e){let{errMsg:i,success:a}=e;return(0,t.jsxs)(t.Fragment,{children:[i&&(0,t.jsx)("div",{className:"errorMsgBanner",children:i}),a&&(0,t.jsx)("div",{className:"successMsgBanner",children:a})]})}},88415:function(e,i,a){"use strict";var t=a(57437),s=a(2265);a(25330),i.Z=e=>{let{src:i,alt:a}=e,[n,l]=(0,s.useState)(!1),r=(0,s.useRef)();return(0,s.useEffect)(()=>{let e=new IntersectionObserver(i=>{let[a]=i;a.isIntersecting&&(l(!0),e.unobserve(a.target))},{threshold:.1});return r.current&&e.observe(r.current),()=>e.disconnect()},[]),(0,t.jsx)("img",{ref:r,src:n?i:void 0,"data-src":i,alt:a,loading:"lazy",style:{opacity:n?1:.5,transition:"opacity 0.3s"}})}},50933:function(e,i,a){"use strict";a.d(i,{uu:function(){return o},eo:function(){return m},cl:function(){return c}});var t=a(86484),s=a(93214),n=a(46172);let l=async(e,i,a)=>new Promise(async(t,l)=>{s.xk.post(`${n.Y.contact}`,e).then(e=>{i("Submitted."),a(""),e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(500===e.response.status?(i(!1),a("Internal error server")):(i(!1),a(e.response.data.message))),e&&l(e)})}),r=e=>new Promise(async(i,a)=>{try{let a=await s.yX.get(`${n.Y.contact}`,{params:{paginated:e.paginated,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyword:e.keyword,createdAt:e.createdAt,type:e.type,email:e.email}});i(a.data)}catch(e){a(e)}}),d=e=>new Promise(async(i,a)=>{try{let a=await s.yX.get(`${n.Y.contact}/${e}`);i(a.data)}catch(e){a(e)}}),o=(e,i)=>{let a=(0,t.useQueryClient)();return(0,t.useMutation)({mutationFn:a=>l(a,e,i),onSuccess:e=>{a.invalidateQueries("user")},onError:e=>{e.message=""}})},c=e=>(0,t.useQuery)("contact",async()=>await r(e)),m=e=>(0,t.useQuery)(["contact",e],async()=>await d(e))},24250:function(e,i,a){"use strict";var t=a(57437),s=a(63993),n=a(98489),l=a(89414),r=a(89126),d=a(64393),o=a(77584),c=a(15735),m=a(81799),u=a(26225),h=a(85860),p=a(47087),f=a(11953),v=a(49651);a(25330);var j=a(34422),x=a(24086),g=a(49360),y=a(41774),b=a(2265),N=a(55788),Z=a(50933),w=a(30100),F=a(23913),_=a(93770),U=a(62953),q=a(75638),A=a(88415),C=a(40257);i.default=function(){let e,i;let[a,O]=(0,b.useState)(""),[B,L]=(0,b.useState)(!1),[E,R]=(0,b.useState)(null),T=v.PhoneNumberUtil.getInstance(),{t:P}=(0,N.$G)(),S=(0,Z.uu)(L,O),J=(0,U.jd)(),I=new FormData,k=async(e,i)=>{let{resetForm:a}=i,t={...Object.fromEntries(Object.entries(e).filter(e=>{let[i,a]=e;return"acceptTerms"!==i&&""!==a&&null!=a}))};window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"join_us_form",button_id:"my_button"}),await S.mutateAsync({...t,to:`${C.env.NEXT_PUBLIC_EMAIL_FORM_JOINUS}`,team:"digital",type:"joinUs"}),a(),R(null),setTimeout(()=>{L(!1)},3e3)},$=(a,t)=>{e=(0,g.Z)().replace(/-/g,""),O("");let s=a.target.files[0];if(s){I.append("file",s);let a=s.name.split(".").pop();i=`${e}.${a}`;let n=new Date().getFullYear();J.mutate({resource:"candidates",folder:n,filename:e,body:{formData:I}},{onSuccess:e=>{"uuid exist"===e.message?(R(e.uuid),t("resume",e.uuid)):(R(i),t("resume",i))},onError:e=>{O(e.message)}})}},M=e=>{try{return T.isValidNumber(T.parseAndKeepRawInput(e))}catch(e){return!1}},z=j.Z_().test("is-valid-phone",P("validations:phoneFormat"),e=>M(e)),H=e=>(0,_.BH)(e).shape({phone:z});return(0,t.jsxs)("div",{id:"service-page-form",children:[(0,t.jsx)(q.Z,{}),(0,t.jsxs)(n.default,{className:"custom-max-width",children:[(0,t.jsxs)("h2",{className:"heading-h1 text-white text-center",children:[P("joinUs:form:title1")," ",(0,t.jsx)("span",{className:"text-yellow",children:P("joinUs:form:title2")})]}),(0,t.jsx)("p",{className:"sub-heading text-white text-center",children:P("joinUs:form:description")}),(0,t.jsx)(s.J9,{initialValues:{fullName:"",email:"",phone:"",field:"",subject:"",message:"",jobTitle:"",companyName:"",acceptTerms:!1,mission:"",resume:""},validationSchema:()=>H(P),onSubmit:k,children:e=>{let{values:i,handleChange:n,errors:v,touched:j,setFieldValue:g}=e;return(0,t.jsx)(s.l0,{className:"pentabell-form",children:(0,t.jsxs)(l.default,{container:!0,rowSpacing:4,columnSpacing:3,children:[(0,t.jsxs)(l.default,{item:!0,xs:12,sm:6,children:[(0,t.jsxs)(r.Z,{className:"form-group light",children:[(0,t.jsxs)(d.Z,{className:"label-pentabell light",children:[P("joinUs:form:fullName"),"*"]}),(0,t.jsx)(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:P("joinUs:form:fullName"),variant:"standard",type:"text",name:"fullName",value:i.fullName,onChange:n,error:!!(v.fullName&&j.fullName)})]}),(0,t.jsx)(s.Bc,{name:"fullName",children:e=>(0,t.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsxs)(l.default,{item:!0,xs:12,sm:6,children:[(0,t.jsxs)(r.Z,{className:"form-group light",children:[(0,t.jsxs)(d.Z,{className:"label-pentabell light",children:[P("joinUs:form:email"),"*"]}),(0,t.jsx)(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:"Email",variant:"standard",type:"email",name:"email",value:i.email,onChange:n,error:!!(v.email&&j.email)})]}),(0,t.jsx)(s.Bc,{name:"email",children:e=>(0,t.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsxs)(l.default,{item:!0,xs:12,sm:6,children:[(0,t.jsxs)(r.Z,{className:"form-group light",children:[(0,t.jsx)(d.Z,{className:"label-pentabell light",children:P("joinUs:form:phoneNumber")}),(0,t.jsx)(x.sb,{defaultCountry:"fr",className:"input-pentabell light",value:i.phone,onChange:e=>{g("phone",e),O("")},flagComponent:e=>(0,t.jsx)(A.Z,{...e})})]}),(0,t.jsx)(s.Bc,{name:"phone",children:e=>(0,t.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsxs)(l.default,{item:!0,xs:12,sm:6,children:[(0,t.jsxs)(r.Z,{className:"form-group light",children:[(0,t.jsxs)(d.Z,{className:"label-pentabell light",children:[P("consultingServices:servicePageForm:field"),"*"]}),(0,t.jsx)(m.Z,{className:"input-pentabell light",id:"tags-standard",options:["Human Resources","Administration","sourcing","Finance","Sales","Marketing","Developement","Other"],getOptionLabel:e=>e,name:"field",value:i.field,onChange:(e,i)=>{g("field",i)},renderInput:e=>(0,t.jsx)(o.Z,{...e,className:"input-pentabell multiple-select  light",variant:"standard",placeholder:P("consultingServices:servicePageForm:chooseOne"),error:!!(v.field&&j.field)})})]}),(0,t.jsx)(s.Bc,{name:"field",children:e=>(0,t.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsxs)(l.default,{item:!0,xs:12,sm:12,children:[(0,t.jsxs)(r.Z,{className:"form-group light",children:[(0,t.jsxs)(d.Z,{className:"label-pentabell light",children:[P("joinUs:form:subject"),"*"]}),(0,t.jsx)(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:P("joinUs:form:subject"),variant:"standard",type:"text",name:"subject",value:i.subject,onChange:n,error:!!(v.subject&&j.subject)})]}),(0,t.jsx)(s.Bc,{name:"subject",children:e=>(0,t.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsxs)(l.default,{item:!0,xs:12,sm:12,children:[(0,t.jsxs)(r.Z,{className:"form-group light",children:[(0,t.jsxs)(d.Z,{className:"label-pentabell light",children:[P("joinUs:form:message"),"*"]}),(0,t.jsx)(o.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:"Message",variant:"standard",type:"text",name:"message",value:i.message,onChange:n,error:!!(v.message&&j.message)})]}),(0,t.jsx)(s.Bc,{name:"message",children:e=>(0,t.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsx)(l.default,{item:!0,xs:12,sm:12,children:(0,t.jsxs)(r.Z,{className:"form-group light flex-row-center",children:[(0,t.jsxs)(d.Z,{id:"mission-radio-btn",className:"label-pentabell light",children:[P("joinUs:form:mission"),"*"]}),(0,t.jsxs)(u.Z,{row:!0,"aria-labelledby":"mission-radio-btn",name:"mission",value:String(i.mission),onChange:e=>g("mission","true"===e.target.value),children:[(0,t.jsx)(h.Z,{value:"true",className:"label-pentabell light",control:(0,t.jsx)(p.Z,{}),label:P("joinUs:form:yes")}),(0,t.jsx)(h.Z,{value:"false",className:"label-pentabell light",control:(0,t.jsx)(p.Z,{}),label:P("joinUs:form:no")})]}),(0,t.jsx)(s.Bc,{name:"mission",children:e=>(0,t.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]})}),(0,t.jsxs)(l.default,{item:!0,xs:12,sm:12,children:[(0,t.jsx)(r.Z,{className:"form-group light form-section",children:(0,t.jsxs)("div",{className:"custom-file-upload",onClick:()=>document.getElementById("file-upload").click(),children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(F.Z,{}),E?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(d.Z,{className:"label-pentabell light",children:[P("joinUs:form:uploadCv"),"*"]}),(0,t.jsx)("p",{className:"sub-label",children:E})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.Z,{className:"label-pentabell light",children:P("joinUs:form:uploadCv")}),(0,t.jsx)("p",{className:"sub-label",children:P("joinUs:form:control")})]}),(0,t.jsx)(y.default,{text:"Choose a file",className:"btn btn-outlined white"}),a&&(0,t.jsx)(c.Z,{variant:"filled",severity:"error",children:a})]}),(0,t.jsx)("input",{id:"file-upload",type:"file",name:"resume",accept:"application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword",style:{display:"none"},onChange:e=>{$(e,g)},error:!!(v.resume&&j.resume)})]})}),(0,t.jsx)(s.Bc,{name:"resume",children:e=>(0,t.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsxs)(l.default,{item:!0,xs:12,sm:8,children:[(0,t.jsx)(h.Z,{className:"checkbox-pentabell light",control:(0,t.jsx)(f.Z,{name:"acceptTerms",checked:i.acceptTerms,onChange:n,error:!!(v.acceptTerms&&j.acceptTerms)}),label:P("payrollService:servicePageForm:formSubmissionAgreement")}),(0,t.jsx)(s.Bc,{name:"acceptTerms",children:e=>(0,t.jsx)(c.Z,{variant:"filled",severity:"error",children:e})})]}),(0,t.jsx)(l.default,{item:!0,xs:12,sm:4,className:"flex-end",children:(0,t.jsx)(y.default,{text:P("joinUs:form:send"),className:"btn btn-filled btn-submit",type:"submit"})})]})})}}),(0,t.jsx)(w.Z,{errMsg:a,success:B})]})]})}},93770:function(e,i,a){"use strict";a.d(i,{BH:function(){return u},Bj:function(){return o},Ju:function(){return v},Ld:function(){return m},ZI:function(){return l},_Q:function(){return h},eo:function(){return p},ft:function(){return r},iP:function(){return d},ie:function(){return c},it:function(){return f},lg:function(){return n},oc:function(){return s}});var t=a(34422);let s=e=>t.Ry().shape({email:t.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")).matches(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,e("validations:invalidEmail"))}),n=e=>t.Ry().shape({password:t.Z_().matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).+$/,e("validations:invalidPassword")).required(e("validations:emptyField")),confirmPassword:t.Z_().oneOf([t.iH("password"),null],e("validations:passwordMatch")).required(e("validations:emptyField"))}),l=e=>t.Ry().shape({firstName:t.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),lastName:t.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:t.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),message:t.Z_().required(e("validations:emptyField")),acceptTerms:t.O7().oneOf([!0],e("validations:emptyField"))}),r=e=>t.Ry().shape({email:t.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),firstName:t.Z_().required(e("validations:emptyField")),lastName:t.Z_().required(e("validations:emptyField"))}),d=e=>t.Ry().shape({firstName:t.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),lastName:t.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:t.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),message:t.Z_().required(e("validations:emptyField")),acceptTerms:t.O7().oneOf([!0],e("validations:emptyField")),field:t.Z_().when("youAre",{is:e=>"Consultant"===e,then:i=>i.required(e("validations:emptyField")),otherwise:e=>e})}),o=e=>t.Ry().shape({firstName:t.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),lastName:t.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:t.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),enquirySelect:t.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),message:t.Z_().required(e("validations:emptyField")),acceptTerms:t.O7().oneOf([!0],e("validations:emptyField"))}),c=e=>t.Ry().shape({fullName:t.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),youAre:t.Z_().required(e("validations:emptyField")),message:t.Z_().required(e("validations:emptyField")),acceptTerms:t.O7().oneOf([!0],e("validations:emptyField")),field:t.Z_().when("youAre",{is:e=>"Consultant"===e,then:i=>i.required(e("validations:emptyField")),otherwise:e=>e})}),m=e=>t.Ry().shape({fullName:t.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:t.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),message:t.Z_().required(e("validations:emptyField")),acceptTerms:t.O7().oneOf([!0],e("validations:emptyField"))}),u=e=>t.Ry().shape({fullName:t.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:t.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),phone:t.Z_().min(8,e("validations:minLength")).max(13,e("validations:maxLength")).required(e("validations:emptyField")),field:t.Z_().required(e("validations:emptyField")),subject:t.Z_().required(e("validations:emptyField")),message:t.Z_().required(e("validations:emptyField")),resume:t.Z_().required(e("validations:emptyField")),mission:t.nK().oneOf([!0,!1],e("validations:emptyField")).required(e("validations:emptyField")),acceptTerms:t.O7().oneOf([!0],e("validations:emptyField"))}),h=e=>t.Ry().shape({fullName:t.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:t.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),message:t.Z_().required(e("validations:emptyField")),acceptTerms:t.O7().oneOf([!0],e("validations:emptyField"))}),p=e=>t.Ry().shape({fullName:t.Z_().min(3,e("validations:minLength")).required(e("validations:emptyField")),email:t.Z_().email(e("validations:invalidEmail")).required(e("validations:emptyField")),message:t.Z_().required(e("validations:emptyField")),howToHelp:t.Z_().required(e("validations:emptyField")),youAre:t.Z_().required(e("validations:emptyField")),acceptTerms:t.O7().oneOf([!0],e("validations:emptyField")),field:t.Z_().when("youAre",{is:e=>"Consultant"===e,then:i=>i.required(e("validations:emptyField")),otherwise:e=>e})}),f=e=>t.Ry().shape({resume:t.Z_().required(e("validations:emptyField")),acceptTerms:t.O7().oneOf([!0],e("validations:emptyField"))}),v=(e,i)=>t.Ry().shape({resume:i?t.Z_():t.Z_().required(e("validations:emptyField")),acceptTerms:t.O7().oneOf([!0],e("validations:emptyField"))})},33466:function(e,i,a){"use strict";a.r(i),i.default={src:"/_next/static/media/Pentabell-joinUs.d02d21c2.webp",height:738,width:1440,blurDataURL:"data:image/webp;base64,UklGRkQAAABXRUJQVlA4IDgAAAAwAgCdASoIAAQAAkA4JYgCdLoAAwxQ8HDwAAD++S+HfhMLwTTFMq1ETxWj27FgIO06TzXxAtwAAA==",blurWidth:8,blurHeight:4}}},function(e){e.O(0,[3661,8760,6990,775,948,5788,2996,7648,3464,455,2662,7183,2296,747,3200,7584,6484,9832,9414,7261,8467,7571,1799,5719,3993,2412,5878,9175,4303,4524,4244,1774,3390,2971,2117,1744],function(){return e(e.s=18155)}),_N_E=e.O()}]);