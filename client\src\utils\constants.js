
export const Countries = [
  "Afghanistan",
  "Åland Islands",
  "Albania",
  "Algeria",
  "American Samoa",
  "AndorrA",
  "Angola",
  "Anguilla",
  "Antarctica",
  "Antigua and Barbuda",
  "Argentina",
  "Armenia",
  "Aruba",
  "Australia",
  "Austria",
  "Azerbaijan",
  "Bahamas",
  "Bahrain",
  "Bangladesh",
  "Barbados",
  "Belarus",
  "Belgium",
  "Belize",
  "Benin",
  "Bermuda",
  "Bhutan",
  "Bolivia",
  "Bosnia and Herzegovina",
  "Botswana",
  "Bouvet Island",
  "Brazil",
  "British Indian Ocean Territory",
  "Brunei Darussalam",
  "Bulgaria",
  "Burkina Faso",
  "Burundi",
  "Cambodia",
  "Cameroon",
  "Canada",
  "Cape Verde",
  "Cayman Islands",
  "Central African Republic",
  "Chad",
  "Chile",
  "China",
  "Christmas Island",
  "Cocos (Keeling) Islands",
  "Colombia",
  "Comoros",
  "Congo",
  "Cook Islands",
  "Costa Rica",
  "Cote D'Ivoire",
  "Croatia",
  "Cuba",
  "Cyprus",
  "Czech Republic",
  "Denmark",
  "Democratic Republic of the Congo",
  "Djibouti",
  "Dominica",
  "Dominican Republic",
  "Ecuador",
  "Egypt",
  "El Salvador",
  "Equatorial Guinea",
  "Eritrea",
  "Estonia",
  "Ethiopia",
  "Falkland Islands (Malvinas)",
  "Faroe Islands",
  "Fiji",
  "Finland",
  "France",
  "French Guiana",
  "French Polynesia",
  "French Southern Territories",
  "Gabon",
  "Gambia",
  "Georgia",
  "Germany",
  "Ghana",
  "Gibraltar",
  "Greece",
  "Greenland",
  "Grenada",
  "Guadeloupe",
  "Guam",
  "Guatemala",
  "Guernsey",
  "Guinea",
  "Guinea-Bissau",
  "Guyana",
  "Haiti",
  "Heard Island and Mcdonald Islands",
  "Holy See (Vatican City State)",
  "Honduras",
  "Hong Kong",
  "Hungary",
  "Iceland",
  "India",
  "Indonesia",
  "Iran, Islamic Republic Of",
  "Iraq",
  "Ireland",
  "Isle of Man",
  "Italy",
  "Jamaica",
  "Japan",
  "Jersey",
  "Jordan",
  "Kazakhstan",
  "Kenya",
  "Kiribati",
  "Korea, Democratic People'S Republic of",
  "Korea, Republic of",
  "Kuwait",
  "Kyrgyzstan",
  "Lao People'S Democratic Republic",
  "Latvia",
  "Lebanon",
  "Lesotho",
  "Liberia",
  "Libya",
  "Liechtenstein",
  "Lithuania",
  "Luxembourg",
  "Macao",
  "Macedonia, The Former Yugoslav Republic of",
  "Madagascar",
  "Malawi",
  "Malaysia",
  "Maldives",
  "Mali",
  "Malta",
  "Marshall Islands",
  "Martinique",
  "Mauritania",
  "Mauritius",
  "Mayotte",
  "Mexico",
  "Micronesia, Federated States of",
  "Moldova, Republic of",
  "Monaco",
  "Mongolia",
  "Montserrat",
  "Morocco",
  "Mozambique",
  "Myanmar",
  "Namibia",
  "Nauru",
  "Nepal",
  "Netherlands",
  "Netherlands Antilles",
  "New Caledonia",
  "New Zealand",
  "Nicaragua",
  "Niger",
  "Nigeria",
  "Niue",
  "Norfolk Island",
  "Northern Mariana Islands",
  "Norway",
  "Oman",
  "Pakistan",
  "Palau",
  "Palestine",
  "Panama",
  "Papua New Guinea",
  "Paraguay",
  "Peru",
  "Philippines",
  "Pitcairn",
  "Poland",
  "Portugal",
  "Puerto Rico",
  "Qatar",
  "Reunion",
  "Romania",
  "Russian Federation",
  "RWANDA",
  "Saint Helena",
  "Saint Kitts and Nevis",
  "Saint Lucia",
  "Saint Pierre and Miquelon",
  "Saint Vincent and the Grenadines",
  "Samoa",
  "San Marino",
  "Sao Tome and Principe",
  "Saudi Arabia",
  "Senegal",
  "Serbia and Montenegro",
  "Seychelles",
  "Sierra Leone",
  "Singapore",
  "Slovakia",
  "Slovenia",
  "Solomon Islands",
  "Somalia",
  "South Africa",
  "South Georgia and the South Sandwich Islands",
  "Spain",
  "Sri Lanka",
  "Sudan",
  "Suriname",
  "Svalbard and Jan Mayen",
  "Swaziland",
  "Sweden",
  "Switzerland",
  "Syrian Arab Republic",
  "Taiwan, Province of China",
  "Tajikistan",
  "Tanzania, United Republic of",
  "Thailand",
  "Timor-Leste",
  "Togo",
  "Tokelau",
  "Tonga",
  "Trinidad and Tobago",
  "Tunisia",
  "Turkey",
  "Turkmenistan",
  "Turks and Caicos Islands",
  "Tuvalu",
  "Uganda",
  "Ukraine",
  "United Arab Emirates",
  "United Kingdom",
  "United States",
  "United States Minor Outlying Islands",
  "Uruguay",
  "Uzbekistan",
  "Vanuatu",
  "Venezuela",
  "Viet Nam",
  "Virgin Islands, British",
  "Virgin Islands, U.S.",
  "Wallis and Futuna",
  "Western Sahara",
  "Yemen",
  "Zambia",
  "Zimbabwe",
];
export const ContractType = ["CDD", "CDIC", "Freelance"];
export const Nationalities = [
  "American",
  "British",
  "Canadian",
  "French",
  "German",
  "Italian",
  "Japanese",
  "Chinese",
  "Indian",
  "Russian",
  "Australian",
  "Brazilian",
  "Mexican",
  "Spanish",
  "South Korean",
  "Dutch",
  "Swedish",
  "Tunisian",
  "Norwegian",
  "Swiss",
  "Belgian",
];
export const Gender = ["Male", "Female", "All"];

export const Frequence = ["monthly", "weekly"];
export const Visibility = ["Public", "Private", "Draft"];

// export const OpportunityTypeLabel = {
//   CONFIDENTIAL: "Confidential",
//   DIRECT_HIRE: "Direct Hire",
//   TENDER: "Tender",
//   CAPABILITY: "Capability",
//   PAYROLL: "Payroll",
//   INTERNE: "Intern",
//   RECRUTEMENT: "Recrutement",
//   CONSULTING: "Consulting",
//   PORTAGE: "Portage",
//   NOT_SPECIFIED: "Not specified",
// };

export const OpportunityType = [
  "Confidential",
  "Direct Hire",
  "Tender",
  "Capability",
  "Payroll",
  "In House",
  "Recrutement",
  "Consulting",
  "Portage",
  "Not specified",
];

// export const ContractType = [
// "Permanent contract",
// "Temporary",
// "Freelance",
// "Work study",
// "Internship",
// "Part-time",
// "Graduate program",
// "Volunteer work",
// "Other"
// ]

export const RobotsMeta = ["index", "noindex"];

export const Roles = ["Candidate", "Editor", "Admin"];

export const Role = {
  CANDIDATE: "Candidate",
  EDITOR: "Editor",
  ADMIN: "Admin",
};
export const Status = ["Pending", "Accepted", "Rejected"];
export const Industry = [
  "It & Telecom",
  "Transport",
  "Energies",
  "Banking",
  "Pharmaceutical",
  "Other"
];
export const IndustryCandidat = [
  "It & Telecom",
  "Transport",
  "Oil & gas",
  "Energy",
  "Banking",
  "Pharmaceutical",
];
export const cible = [
  "client",
  "consultant"


]
export const skills = [
  // Compétences pour IT & TELECOM
  {
    name: "Développement logiciel",
    label: "Développement logiciel",
    industry: "IT & TELECOM",
  },
  {
    value: "Administration système",
    label: "Administration système",
    industry: "IT & TELECOM",
  },
  {
    value: "Développement d'applications mobiles",
    label: "Développement d'applications mobiles",
    industry: "IT & TELECOM",
  },
  {
    value: "Gestion de réseau",
    label: "Gestion de réseau",
    industry: "IT & TELECOM",
  },
  {
    value: "Gestion de projet",
    label: "Gestion de projet",
    industry: "IT & TELECOM",
  },
  {
    value: "Analyse de données",
    label: "Analyse de données",
    industry: "IT & TELECOM",
  },
  { value: "Cybersécurité", label: "Cybersécurité", industry: "IT & TELECOM" },
  {
    value: "Cloud computing",
    label: "Cloud computing",
    industry: "IT & TELECOM",
  },
  { value: "abcdabcd", label: "abcdabcd", industry: "IT & TELECOM" },
  // Compétences pour TRANSPORT
  {
    value: "Transport routier",
    label: "Transport routier",
    industry: "TRANSPORT",
  },
  { value: "Logistique", label: "Logistique", industry: "TRANSPORT" },
  {
    value: "Gestion de flotte",
    label: "Gestion de flotte",
    industry: "TRANSPORT",
  },
  {
    value: "Planification des itinéraires",
    label: "Planification des itinéraires",
    industry: "TRANSPORT",
  },
  {
    value: "Logistique internationale",
    label: "Logistique internationale",
    industry: "TRANSPORT",
  },
  // Compétences pour OIL & GAS
  {
    value: "Forage pétrolier",
    label: "Forage pétrolier",
    industry: "OIL & GAS",
  },
  {
    value: "Raffinage pétrolier",
    label: "Raffinage pétrolier",
    industry: "OIL & GAS",
  },
  {
    value: "Exploration géologique",
    label: "Exploration géologique",
    industry: "OIL & GAS",
  },
  {
    value: "Ingénierie des réservoirs",
    label: "Ingénierie des réservoirs",
    industry: "OIL & GAS",
  },
  {
    value: "Gestion de la production",
    label: "Gestion de la production",
    industry: "OIL & GAS",
  },
  // Compétences pour BANKING
  {
    value: "Analyse financière",
    label: "Analyse financière",
    industry: "BANKING",
  },
  {
    value: "Gestion des risques financiers",
    label: "Gestion des risques financiers",
    industry: "BANKING",
  },
  {
    value: "Gestion des portefeuilles",
    label: "Gestion des portefeuilles",
    industry: "BANKING",
  },
  {
    value: "Conformité réglementaire",
    label: "Conformité réglementaire",
    industry: "BANKING",
  },
  {
    value: "Services bancaires en ligne",
    label: "Services bancaires en ligne",
    industry: "BANKING",
  },
];

export const defaultFonts = [
  "Arial",
  "Comic Sans MS",
  "Courier New",
  "Impact",
  "Georgia",
  "Tahoma",
  "Trebuchet MS",
  "Verdana",
];

export const sortedFontOptions = [
  "Logical",
  "Salesforce Sans",
  "Garamond",
  "Sans-Serif",
  "Serif",
  "Times New Roman",
  "Helvetica",
  ...defaultFonts,
].sort();

export const TypeContacts = [
  "countryContact",
  "joinUs",
  "directHiringService",
  "aiSourcingService",
  "technicalAssistanceService",
  "consultingService",
  "payrollService",
  "mainService",
  "getInTouchContact",
  "getInTouch",
];

export const TypeContactLabels = {
  countryContact: "Country Contact",
  joinUs: "Join Us",
  directHiringService: "Direct Hiring Service",
  aiSourcingService: "AI Sourcing Service",
  technicalAssistanceService: "Technical Assistance Service",
  consultingService: "Consulting Service",
  payrollService: "Payroll Service",
  mainService: "Main Service",
  getInTouchContact: "Get in Touch Contact",
  getInTouch: "Get in Touch",
};

export const LabelContactFields = {
  firstName: "First Name",
  lastName: "Last Name",
  fullName: "Full Name",
  email: "Email",
  phone: "Phone",
  message: "Message",
  type: "Type",
  subject: "Subject",
  youAre: "You Are",
  companyName: "Company Name",
  enquirySelect: "Enquiry Select",
  jobTitle: "Job Title",
  mission: "Mission",
  resume: "Resume",
  howToHelp: "How To Help",
  createdAt: "Created At",
  countryName: "Country Name",
  field: "Field",
};

export const contactData = (t, locale) => [
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: t("contactUs:bureux:contacts:france"),
    logo: {
      "@type": "ImageObject",
      url: "https://www.pentabell.com/logos/pentabell-logo.png",
    },
    address: {
      "@type": "PostalAddress",
      streetAddress:
        "Atlantic Building Montparnasse, Entrance No. 7, 3rd floor",
      addressLocality: "Paris",
      postalCode: "75015",
      addressCountry: "FR",
    },
    telephone: "+33 1 73 07 42 54",
    email: "<EMAIL>",
    url:
      locale === "en"
        ? "https://www.pentabell.com/recruitment-agency-france/"
        : `https://www.pentabell.com/${locale}/recruitment-agency-france/`,
  },
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: t("contactUs:bureux:contacts:switzerland"),
    address: {
      "@type": "PostalAddress",
      streetAddress: "Grand-Rue 92",
      addressLocality: "Montreux",
      postalCode: "1820",
      addressCountry: "CH",
    },
    telephone: "+33 1 73 07 42 54",
    email: "<EMAIL>",
    url:
      locale === "en"
        ? "https://www.pentabell.com/contact/"
        : `https://www.pentabell.com/${locale}/contact/`,
  },
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: t("contactUs:bureux:contacts:ksa"),
    address: {
      "@type": "PostalAddress",
      streetAddress: "3530 Umar Ibn Abdul Aziz Br Rd, Az Zahra",
      addressLocality: "Riyadh",
      postalCode: "12815",
      addressCountry: "SA",
    },
    telephone: "+33 1 73 07 42 54",
    email: "<EMAIL>",
    url:
      locale === "en"
        ? "https://www.pentabell.com/international-hr-services-recruitment-agency-ksa/"
        : `https://www.pentabell.com/${locale}/international-hr-services-recruitment-agency-ksa/`,
  },
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: t("contactUs:bureux:contacts:uae"),
    address: {
      "@type": "PostalAddress",
      streetAddress: "HDS Business Center Office 306 JLT",
      addressLocality: "Dubai",
      addressCountry: "AE",
    },
    telephone: "+971 4 4876 0672",
    email: "<EMAIL>",
    url:
      locale === "en"
        ? "https://www.pentabell.com/recruitment-staffing-agency-dubai/"
        : `https://www.pentabell.com/${locale}/recruitment-staffing-agency-dubai/`,
  },
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: t("contactUs:bureux:contacts:qatar"),
    address: {
      "@type": "PostalAddress",
      streetAddress: "Level 14, Commercial Bank Plaza, West Bay",
      addressLocality: "Doha",
      postalCode: "27111",
      addressCountry: "QA",
    },
    telephone: "+974 4452 7957",
    email: "<EMAIL>",
    url:
      locale === "en"
        ? "https://www.pentabell.com/international-hr-services-recruitment-agency-qatar/"
        : `https://www.pentabell.com/${locale}/international-hr-services-recruitment-agency-qatar/`,
  },
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: t("contactUs:bureux:contacts:tunisia"),
    address: {
      "@type": "PostalAddress",
      streetAddress: "Imm. MADIBA, Rue Khawarizmi",
      addressLocality: "La Goulette",
      postalCode: "2015",
      addressCountry: "TN",
    },
    telephone: ["+216 31 385 510"],
    email: "<EMAIL>",
    url:
      locale === "en"
        ? "https://www.pentabell.com/hiring-employees-tunisia-guide/"
        : `https://www.pentabell.com/${locale}/hiring-employees-tunisia-guide/`,
  },
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: t("contactUs:bureux:contacts:hydra"),
    address: {
      "@type": "PostalAddress",
      streetAddress: "Route les oliviers les cretes n°14",
      addressLocality: "Hydra, Alger",
      postalCode: "16035",
      addressCountry: "DZ",
    },
    telephone: ["+213 23 48 59 10", "+213 23 48 51 44"],
    email: "<EMAIL>",
    url:
      locale === "en"
        ? "https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-algeria/"
        : `https://www.pentabell.com/${locale}/ultimate-guide-to-hiring-employees-in-algeria/`,
  },
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: t("contactUs:bureux:contacts:hassiMassoud"),
    address: {
      "@type": "PostalAddress",
      streetAddress: "Eurojapan Residence Route Nationale N°3 BP 842",
      addressLocality: "Hassi Messaoud",
      addressCountry: "DZ",
    },
    telephone: "+33 1 73 07 42 54",
    email: "<EMAIL>",
    url:
      locale === "en"
        ? "https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-algeria/"
        : `https://www.pentabell.com/${locale}/ultimate-guide-to-hiring-employees-in-algeria/`,
  },
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: t("contactUs:bureux:contacts:morocco"),
    address: {
      "@type": "PostalAddress",
      streetAddress: "Zenith 1, Sidi maarouf, lot CIVIM",
      addressLocality: "Casablanca",
      postalCode: "20270",
      addressCountry: "MA",
    },
    telephone: "+212 5 22 78 63 66",
    email: "<EMAIL>",
    url:
      locale === "en"
        ? "https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-morocco/"
        : `https://www.pentabell.com/${locale}/ultimate-guide-to-hiring-employees-in-morocco/`,
  },
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: t("contactUs:bureux:contacts:egypte"),
    address: {
      "@type": "PostalAddress",
      streetAddress: "8 El Birgas street, Garden City",
      addressLocality: "Cairo",
      addressCountry: "EG",
    },
    telephone: "+33 1 73 07 42 54",
    email: "<EMAIL>",
    url:
      locale === "en"
        ? "https://www.pentabell.com/guide-to-hiring-employees-in-egypt/"
        : `https://www.pentabell.com/${locale}/guide-to-hiring-employees-in-egypt/`,
  },
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: t("contactUs:bureux:contacts:lybia"),
    address: {
      "@type": "PostalAddress",
      streetAddress: "Al Serraj, AlMawashi Street P.O.Box 3000",
      addressLocality: "Tripoli",
      addressCountry: "LY",
    },
    telephone: "+33 1 73 07 42 54",
    email: "<EMAIL>",
    url:
      locale === "en"
        ? "https://www.pentabell.com/guide-to-hiring-employees-in-libya/"
        : `https://www.pentabell.com/${locale}/guide-to-hiring-employees-in-libya/`,
  },
];

export const feedbacks = [
  {
    id: 1,
    description:
      "I truly appreciate the opportunities provided and the unwavering trust in my abilities. A big thank you to the entire team for their continued support, especially Ridha R.",
    rating: 4,
    author: "Nabil.J",
    quality: "IT Coordinator",
  },
  {
    id: 2,
    description:
      "Pentabell Tunisie team has been helpful and responsive whenever assistance is needed, particularly Mr. Ridha and Mr. Matthew. I appreciate the team’s efforts and look forward to seeing continued growth in the future.",
    rating: 4,
    author: "Maher.M",
    quality: "A2P Service Delivery Engineer",
  },
  {
    id: 3,
    description:
      "We are very satisfied with the service provided by Pentabell. The professionalism and efficiency of the Tunisia office staff are commendable, especially the work of Mr. Matthew, Mr. Ridha, and Mr. Eric, who have been our direct contacts.",
    rating: 5,
    author: "Gabor.M",
    quality: "Company",
  },
  {
    id: 4,
    description:
      "I truly appreciate the opportunities provided and the unwavering trust in my abilities. A big thank you to the entire team for their continued support, especially Ridha R.",
    rating: 4,
    author: "Nabil.J",
    quality: "IT Coordinator",
  },
  {
    id: 5,
    description:
      "Pentabell Tunisie team has been helpful and responsive whenever assistance is needed, particularly Mr. Ridha and Mr. Matthew. I appreciate the team’s efforts and look forward to seeing continued growth in the future.",
    rating: 4,
    author: "Maher.M",
    quality: "A2P Service Delivery Engineer",
  },
  {
    id: 6,
    description:
      "We are very satisfied with the service provided by Pentabell. The professionalism and efficiency of the Tunisia office staff are commendable, especially the work of Mr. Matthew, Mr. Ridha, and Mr. Eric, who have been our direct contacts.",
    rating: 5,
    author: "Gabor.M",
    quality: "Company",
  },
];

export const coporateProfileTestimonials = [
  {
    id: 1,
    description:
      "I am pleased  with PENTABELL the exceptional services they have delivered during our recent collaborations on various projects within the Kingdom of Saudi Arabia (KSA). Throughout our partnership, PENTABELL has consistently demonstrated professionalism, expertise, and a strong commitment to delivering high-quality results.",
    author: "NOKIA KSA",
  },

  {
    id: 2,
    description:
      "We are very satisfied with the service provided by Pentabell. The professionalism and efficiency of the Tunisia office staff are commendable, especially the work of Mr. Matthew, Mr. Ridha, and Mr. Eric, who have been our direct contacts.",
    author: "Gabor.M, Company",
  }
]


