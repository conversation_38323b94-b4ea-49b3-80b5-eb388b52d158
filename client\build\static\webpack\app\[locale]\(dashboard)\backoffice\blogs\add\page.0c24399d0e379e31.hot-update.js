"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Description!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Add custom styles for drag state\nconst dragActiveStyles = `\n  .file-labels.drag-active {\n    border-color: #1976d2 !important;\n    background-color: rgba(25, 118, 210, 0.04) !important;\n  }\n  .file-labels.disabled {\n    cursor: not-allowed !important;\n    opacity: 0.6 !important;\n  }\n`;\n// Inject styles if not already present\nif (typeof document !== \"undefined\" && !document.getElementById(\"document-importer-styles\")) {\n    const styleSheet = document.createElement(\"style\");\n    styleSheet.id = \"document-importer-styles\";\n    styleSheet.textContent = dragActiveStyles;\n    document.head.appendChild(styleSheet);\n}\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [extractedData, setExtractedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Supported file types\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords: keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement(function(image) {\n                        return image.read(\"base64\").then(function(imageBuffer) {\n                            return {\n                                src: \"data:\" + image.contentType + \";base64,\" + imageBuffer\n                            };\n                        });\n                    })\n                }\n            });\n            setProgress(75);\n            let cleanContent = result.value.replace(/<p><\\/p>/g, \"\") // Remove empty paragraphs\n            .replace(/\\s+/g, \" \") // Normalize whitespace\n            .trim();\n            // Extract metadata\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata: metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            // Convert plain text to basic HTML\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata: metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            setExtractedData(result);\n            setSuccess(true);\n            if (extractedData) {\n                onContentExtracted(extractedData.content);\n                if (onMetadataExtracted) {\n                    onMetadataExtracted(extractedData.metadata);\n                }\n                setExtractedData(null);\n            }\n        } catch (err) {\n            console.error(\"File processing error:\", err);\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) {\n            processFile(acceptedFiles[0]);\n        }\n    }, []);\n    const { getRootProps, getInputProps, isDragActive, acceptedFiles } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        sx: {\n            mb: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                variant: \"h6\",\n                gutterBottom: true,\n                className: \"label-form\",\n                children: [\n                    t(\"createArticle:importDocument\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"upload-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    ...getRootProps(),\n                    className: `file-labels ${isDragActive ? \"drag-active\" : \"\"} ${disabled || isProcessing ? \"disabled\" : \"\"}`,\n                    style: {\n                        opacity: disabled || isProcessing ? 0.6 : 1,\n                        cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ...getInputProps(),\n                            className: \"file-input\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"upload-area\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"icon-pic\",\n                                        style: {\n                                            backgroundImage: `url(\"${_assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"]}\")`,\n                                            backgroundSize: \"cover\",\n                                            backgroundRepeat: \"no-repeat\",\n                                            backgroundPosition: \"center\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-text\",\n                                            children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:importFromDocument\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-description\",\n                                            children: [\n                                                t(\"createArticle:supportedFormats\"),\n                                                \": .docx, .doc, .txt\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            sx: {\n                                                display: \"flex\",\n                                                justifyContent: \"flex-start\",\n                                                gap: 1,\n                                                flexWrap: \"wrap\",\n                                                mt: 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Word (.docx)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Word (.doc)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Text (.txt)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"body2\",\n                        gutterBottom: true,\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, undefined),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"pRjwRruKHWvwgpXaB93/Sv/fHgE=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});