"use strict";exports.id=6636,exports.ids=[6636],exports.modules={84648:(e,t,r)=>{r.d(t,{Z:()=>U});var o,n,a=r(17577),i=r(41135),l=r(88634),p=r(92014),s=r(85010),u=r(69035),d=r(91703),c=r(30990),g=r(2791),f=r(54641),h=r(71685),m=r(97898);function b(e){return(0,m.ZP)("MuiListSubheader",e)}(0,h.Z)("MuiListSubheader",["root","colorPrimary","colorInherit","gutters","inset","sticky"]);var v=r(10326);let x=e=>{let{classes:t,color:r,disableGutters:o,inset:n,disableSticky:a}=e,i={root:["root","default"!==r&&`color${(0,f.Z)(r)}`,!o&&"gutters",n&&"inset",!a&&"sticky"]};return(0,l.Z)(i,b,t)},y=(0,d.ZP)("li",{name:"MuiListSubheader",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,"default"!==r.color&&t[`color${(0,f.Z)(r.color)}`],!r.disableGutters&&t.gutters,r.inset&&t.inset,!r.disableSticky&&t.sticky]}})((0,c.Z)(({theme:e})=>({boxSizing:"border-box",lineHeight:"48px",listStyle:"none",color:(e.vars||e).palette.text.secondary,fontFamily:e.typography.fontFamily,fontWeight:e.typography.fontWeightMedium,fontSize:e.typography.pxToRem(14),variants:[{props:{color:"primary"},style:{color:(e.vars||e).palette.primary.main}},{props:{color:"inherit"},style:{color:"inherit"}},{props:({ownerState:e})=>!e.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:e})=>e.inset,style:{paddingLeft:72}},{props:({ownerState:e})=>!e.disableSticky,style:{position:"sticky",top:0,zIndex:1,backgroundColor:(e.vars||e).palette.background.paper}}]}))),$=a.forwardRef(function(e,t){let r=(0,g.i)({props:e,name:"MuiListSubheader"}),{className:o,color:n="default",component:a="li",disableGutters:l=!1,disableSticky:p=!1,inset:s=!1,...u}=r,d={...r,color:n,component:a,disableGutters:l,disableSticky:p,inset:s},c=x(d);return(0,v.jsx)(y,{as:a,className:(0,i.Z)(c.root,o),ref:t,ownerState:d,...u})});$&&($.muiSkipListHighlight=!0);var Z=r(89178),P=r(48260),I=r(85560),k=r(81454),S=r(69258),R=r(36546),A=r(50729),C=r(65368),w=r(49352);function L(e){return(0,m.ZP)("MuiAutocomplete",e)}let O=(0,h.Z)("MuiAutocomplete",["root","expanded","fullWidth","focused","focusVisible","tag","tagSizeSmall","tagSizeMedium","hasPopupIcon","hasClearIcon","inputRoot","input","inputFocused","endAdornment","clearIndicator","popupIndicator","popupIndicatorOpen","popper","popperDisablePortal","paper","listbox","loading","noOptions","option","groupLabel","groupUl"]);var M=r(31121);let D=e=>{let{classes:t,disablePortal:r,expanded:o,focused:n,fullWidth:a,hasClearIcon:i,hasPopupIcon:p,inputFocused:s,popupOpen:u,size:d}=e,c={root:["root",o&&"expanded",n&&"focused",a&&"fullWidth",i&&"hasClearIcon",p&&"hasPopupIcon"],inputRoot:["inputRoot"],input:["input",s&&"inputFocused"],tag:["tag",`tagSize${(0,f.Z)(d)}`],endAdornment:["endAdornment"],clearIndicator:["clearIndicator"],popupIndicator:["popupIndicator",u&&"popupIndicatorOpen"],popper:["popper",r&&"popperDisablePortal"],paper:["paper"],listbox:["listbox"],loading:["loading"],noOptions:["noOptions"],option:["option"],groupLabel:["groupLabel"],groupUl:["groupUl"]};return(0,l.Z)(c,L,t)},T=(0,d.ZP)("div",{name:"MuiAutocomplete",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e,{fullWidth:o,hasClearIcon:n,hasPopupIcon:a,inputFocused:i,size:l}=r;return[{[`& .${O.tag}`]:t.tag},{[`& .${O.tag}`]:t[`tagSize${(0,f.Z)(l)}`]},{[`& .${O.inputRoot}`]:t.inputRoot},{[`& .${O.input}`]:t.input},{[`& .${O.input}`]:i&&t.inputFocused},t.root,o&&t.fullWidth,a&&t.hasPopupIcon,n&&t.hasClearIcon]}})({[`&.${O.focused} .${O.clearIndicator}`]:{visibility:"visible"},"@media (pointer: fine)":{[`&:hover .${O.clearIndicator}`]:{visibility:"visible"}},[`& .${O.tag}`]:{margin:3,maxWidth:"calc(100% - 6px)"},[`& .${O.inputRoot}`]:{[`.${O.hasPopupIcon}&, .${O.hasClearIcon}&`]:{paddingRight:30},[`.${O.hasPopupIcon}.${O.hasClearIcon}&`]:{paddingRight:56},[`& .${O.input}`]:{width:0,minWidth:30}},[`& .${k.Z.root}`]:{paddingBottom:1,"& .MuiInput-input":{padding:"4px 4px 4px 0px"}},[`& .${k.Z.root}.${S.Z.sizeSmall}`]:{[`& .${k.Z.input}`]:{padding:"2px 4px 3px 0"}},[`& .${R.Z.root}`]:{padding:9,[`.${O.hasPopupIcon}&, .${O.hasClearIcon}&`]:{paddingRight:39},[`.${O.hasPopupIcon}.${O.hasClearIcon}&`]:{paddingRight:65},[`& .${O.input}`]:{padding:"7.5px 4px 7.5px 5px"},[`& .${O.endAdornment}`]:{right:9}},[`& .${R.Z.root}.${S.Z.sizeSmall}`]:{paddingTop:6,paddingBottom:6,paddingLeft:6,[`& .${O.input}`]:{padding:"2.5px 4px 2.5px 8px"}},[`& .${A.Z.root}`]:{paddingTop:19,paddingLeft:8,[`.${O.hasPopupIcon}&, .${O.hasClearIcon}&`]:{paddingRight:39},[`.${O.hasPopupIcon}.${O.hasClearIcon}&`]:{paddingRight:65},[`& .${A.Z.input}`]:{padding:"7px 4px"},[`& .${O.endAdornment}`]:{right:9}},[`& .${A.Z.root}.${S.Z.sizeSmall}`]:{paddingBottom:1,[`& .${A.Z.input}`]:{padding:"2.5px 4px"}},[`& .${S.Z.hiddenLabel}`]:{paddingTop:8},[`& .${A.Z.root}.${S.Z.hiddenLabel}`]:{paddingTop:0,paddingBottom:0,[`& .${O.input}`]:{paddingTop:16,paddingBottom:17}},[`& .${A.Z.root}.${S.Z.hiddenLabel}.${S.Z.sizeSmall}`]:{[`& .${O.input}`]:{paddingTop:8,paddingBottom:9}},[`& .${O.input}`]:{flexGrow:1,textOverflow:"ellipsis",opacity:0},variants:[{props:{fullWidth:!0},style:{width:"100%"}},{props:{size:"small"},style:{[`& .${O.tag}`]:{margin:2,maxWidth:"calc(100% - 4px)"}}},{props:{inputFocused:!0},style:{[`& .${O.input}`]:{opacity:1}}},{props:{multiple:!0},style:{[`& .${O.inputRoot}`]:{flexWrap:"wrap"}}}]}),N=(0,d.ZP)("div",{name:"MuiAutocomplete",slot:"EndAdornment",overridesResolver:(e,t)=>t.endAdornment})({position:"absolute",right:0,top:"50%",transform:"translate(0, -50%)"}),j=(0,d.ZP)(P.Z,{name:"MuiAutocomplete",slot:"ClearIndicator",overridesResolver:(e,t)=>t.clearIndicator})({marginRight:-2,padding:4,visibility:"hidden"}),z=(0,d.ZP)(P.Z,{name:"MuiAutocomplete",slot:"PopupIndicator",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.popupIndicator,r.popupOpen&&t.popupIndicatorOpen]}})({padding:2,marginRight:-2,variants:[{props:{popupOpen:!0},style:{transform:"rotate(180deg)"}}]}),F=(0,d.ZP)(u.Z,{name:"MuiAutocomplete",slot:"Popper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{[`& .${O.option}`]:t.option},t.popper,r.disablePortal&&t.popperDisablePortal]}})((0,c.Z)(({theme:e})=>({zIndex:(e.vars||e).zIndex.modal,variants:[{props:{disablePortal:!0},style:{position:"absolute"}}]}))),H=(0,d.ZP)(Z.Z,{name:"MuiAutocomplete",slot:"Paper",overridesResolver:(e,t)=>t.paper})((0,c.Z)(({theme:e})=>({...e.typography.body1,overflow:"auto"}))),W=(0,d.ZP)("div",{name:"MuiAutocomplete",slot:"Loading",overridesResolver:(e,t)=>t.loading})((0,c.Z)(({theme:e})=>({color:(e.vars||e).palette.text.secondary,padding:"14px 16px"}))),E=(0,d.ZP)("div",{name:"MuiAutocomplete",slot:"NoOptions",overridesResolver:(e,t)=>t.noOptions})((0,c.Z)(({theme:e})=>({color:(e.vars||e).palette.text.secondary,padding:"14px 16px"}))),V=(0,d.ZP)("ul",{name:"MuiAutocomplete",slot:"Listbox",overridesResolver:(e,t)=>t.listbox})((0,c.Z)(({theme:e})=>({listStyle:"none",margin:0,padding:"8px 0",maxHeight:"40vh",overflow:"auto",position:"relative",[`& .${O.option}`]:{minHeight:48,display:"flex",overflow:"hidden",justifyContent:"flex-start",alignItems:"center",cursor:"pointer",paddingTop:6,boxSizing:"border-box",outline:"0",WebkitTapHighlightColor:"transparent",paddingBottom:6,paddingLeft:16,paddingRight:16,[e.breakpoints.up("sm")]:{minHeight:"auto"},[`&.${O.focused}`]:{backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},'&[aria-disabled="true"]':{opacity:(e.vars||e).palette.action.disabledOpacity,pointerEvents:"none"},[`&.${O.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},'&[aria-selected="true"]':{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:(0,p.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity),[`&.${O.focused}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:(0,p.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:(e.vars||e).palette.action.selected}},[`&.${O.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:(0,p.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}}}}))),q=(0,d.ZP)($,{name:"MuiAutocomplete",slot:"GroupLabel",overridesResolver:(e,t)=>t.groupLabel})((0,c.Z)(({theme:e})=>({backgroundColor:(e.vars||e).palette.background.paper,top:-8}))),B=(0,d.ZP)("ul",{name:"MuiAutocomplete",slot:"GroupUl",overridesResolver:(e,t)=>t.groupUl})({padding:0,[`& .${O.option}`]:{paddingLeft:24}}),U=a.forwardRef(function(e,t){let r;let l=(0,g.i)({props:e,name:"MuiAutocomplete"}),{autoComplete:p=!1,autoHighlight:d=!1,autoSelect:c=!1,blurOnSelect:f=!1,ChipProps:h,className:m,clearIcon:b=o||(o=(0,v.jsx)(C.Z,{fontSize:"small"})),clearOnBlur:x=!l.freeSolo,clearOnEscape:y=!1,clearText:$="Clear",closeText:P="Close",componentsProps:k,defaultValue:S=l.multiple?[]:null,disableClearable:R=!1,disableCloseOnSelect:A=!1,disabled:L=!1,disabledItemsFocusable:O=!1,disableListWrap:U=!1,disablePortal:G=!1,filterOptions:K,filterSelectedOptions:J=!1,forcePopupIcon:Q="auto",freeSolo:X=!1,fullWidth:Y=!1,getLimitTagsText:_=e=>`+${e}`,getOptionDisabled:ee,getOptionKey:et,getOptionLabel:er,isOptionEqualToValue:eo,groupBy:en,handleHomeEndKeys:ea=!l.freeSolo,id:ei,includeInputInList:el=!1,inputValue:ep,limitTags:es=-1,ListboxComponent:eu,ListboxProps:ed,loading:ec=!1,loadingText:eg="Loading…",multiple:ef=!1,noOptionsText:eh="No options",onChange:em,onClose:eb,onHighlightChange:ev,onInputChange:ex,onOpen:ey,open:e$,openOnFocus:eZ=!1,openText:eP="Open",options:eI,PaperComponent:ek,PopperComponent:eS,popupIcon:eR=n||(n=(0,v.jsx)(w.Z,{})),readOnly:eA=!1,renderGroup:eC,renderInput:ew,renderOption:eL,renderTags:eO,selectOnFocus:eM=!l.freeSolo,size:eD="medium",slots:eT={},slotProps:eN={},value:ej,...ez}=l,{getRootProps:eF,getInputProps:eH,getInputLabelProps:eW,getPopupIndicatorProps:eE,getClearProps:eV,getTagProps:eq,getListboxProps:eB,getOptionProps:eU,value:eG,dirty:eK,expanded:eJ,id:eQ,popupOpen:eX,focused:eY,focusedTag:e_,anchorEl:e1,setAnchorEl:e0,inputValue:e5,groupedOptions:e6}=(0,s.Z)({...l,componentName:"Autocomplete"}),e4=!R&&!L&&eK&&!eA,e9=(!X||!0===Q)&&!1!==Q,{onMouseDown:e8}=eH(),{ref:e7,...e2}=eB(),e3=er||(e=>e.label??e),te={...l,disablePortal:G,expanded:eJ,focused:eY,fullWidth:Y,getOptionLabel:e3,hasClearIcon:e4,hasPopupIcon:e9,inputFocused:-1===e_,popupOpen:eX,size:eD},tt=D(te),tr={slots:{paper:ek,popper:eS,...eT},slotProps:{chip:h,listbox:ed,...k,...eN}},[to,tn]=(0,M.Z)("listbox",{elementType:V,externalForwardedProps:tr,ownerState:te,className:tt.listbox,additionalProps:e2,ref:e7}),[ta,ti]=(0,M.Z)("paper",{elementType:Z.Z,externalForwardedProps:tr,ownerState:te,className:tt.paper}),[tl,tp]=(0,M.Z)("popper",{elementType:u.Z,externalForwardedProps:tr,ownerState:te,className:tt.popper,additionalProps:{disablePortal:G,style:{width:e1?e1.clientWidth:null},role:"presentation",anchorEl:e1,open:eX}});if(ef&&eG.length>0){let e=e=>({className:tt.tag,disabled:L,...eq(e)});r=eO?eO(eG,e,te):eG.map((t,r)=>{let{key:o,...n}=e({index:r});return(0,v.jsx)(I.Z,{label:e3(t),size:eD,...n,...tr.slotProps.chip},o)})}if(es>-1&&Array.isArray(r)){let e=r.length-es;!eY&&e>0&&(r=r.splice(0,es)).push((0,v.jsx)("span",{className:tt.tag,children:_(e)},r.length))}let ts=eC||(e=>(0,v.jsxs)("li",{children:[(0,v.jsx)(q,{className:tt.groupLabel,ownerState:te,component:"div",children:e.group}),(0,v.jsx)(B,{className:tt.groupUl,ownerState:te,children:e.children})]},e.key)),tu=eL||((e,t)=>{let{key:r,...o}=e;return(0,v.jsx)("li",{...o,children:e3(t)},r)}),td=(e,t)=>{let r=eU({option:e,index:t});return tu({...r,className:tt.option},e,{selected:r["aria-selected"],index:t,inputValue:e5},te)},tc=tr.slotProps.clearIndicator,tg=tr.slotProps.popupIndicator;return(0,v.jsxs)(a.Fragment,{children:[(0,v.jsx)(T,{ref:t,className:(0,i.Z)(tt.root,m),ownerState:te,...eF(ez),children:ew({id:eQ,disabled:L,fullWidth:!0,size:"small"===eD?"small":void 0,InputLabelProps:eW(),InputProps:{ref:e0,className:tt.inputRoot,startAdornment:r,onMouseDown:e=>{e.target===e.currentTarget&&e8(e)},...(e4||e9)&&{endAdornment:(0,v.jsxs)(N,{className:tt.endAdornment,ownerState:te,children:[e4?(0,v.jsx)(j,{...eV(),"aria-label":$,title:$,ownerState:te,...tc,className:(0,i.Z)(tt.clearIndicator,tc?.className),children:b}):null,e9?(0,v.jsx)(z,{...eE(),disabled:L,"aria-label":eX?P:eP,title:eX?P:eP,ownerState:te,...tg,className:(0,i.Z)(tt.popupIndicator,tg?.className),children:eR}):null]})}},inputProps:{className:tt.input,disabled:L,readOnly:eA,...eH()}})}),e1?(0,v.jsx)(F,{as:tl,...tp,children:(0,v.jsxs)(H,{as:ta,...ti,children:[ec&&0===e6.length?(0,v.jsx)(W,{className:tt.loading,ownerState:te,children:eg}):null,0!==e6.length||X||ec?null:(0,v.jsx)(E,{className:tt.noOptions,ownerState:te,role:"presentation",onMouseDown:e=>{e.preventDefault()},children:eh}),e6.length>0?(0,v.jsx)(to,{as:eu,...tn,children:e6.map((e,t)=>en?ts({key:e.key,group:e.group,children:e.options.map((t,r)=>td(t,e.index+r))}):td(e,t))}):null]})}):null]})})},65368:(e,t,r)=>{r.d(t,{Z:()=>a}),r(17577);var o=r(27522),n=r(10326);let a=(0,o.Z)((0,n.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},85010:(e,t,r)=>{r.d(t,{D:()=>u,Z:()=>h});var o=r(17577),n=r(34018),a=r(18680),i=r(50591),l=r(11987),p=r(85890);function s(e){return e.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}function u(e={}){let{ignoreAccents:t=!0,ignoreCase:r=!0,limit:o,matchFrom:n="any",stringify:a,trim:i=!1}=e;return(e,{inputValue:l,getOptionLabel:p})=>{let u=i?l.trim():l;r&&(u=u.toLowerCase()),t&&(u=s(u));let d=u?e.filter(e=>{let o=(a||p)(e);return r&&(o=o.toLowerCase()),t&&(o=s(o)),"start"===n?o.startsWith(u):o.includes(u)}):e;return"number"==typeof o?d.slice(0,o):d}}let d=u(),c=e=>null!==e.current&&e.current.parentElement?.contains(document.activeElement),g=[];function f(e,t,r){if(t||null==e)return"";let o=r(e);return"string"==typeof o?o:""}let h=function(e){let{unstable_isActiveElementInListbox:t=c,unstable_classNamePrefix:r="Mui",autoComplete:s=!1,autoHighlight:u=!1,autoSelect:h=!1,blurOnSelect:m=!1,clearOnBlur:b=!e.freeSolo,clearOnEscape:v=!1,componentName:x="useAutocomplete",defaultValue:y=e.multiple?g:null,disableClearable:$=!1,disableCloseOnSelect:Z=!1,disabled:P,disabledItemsFocusable:I=!1,disableListWrap:k=!1,filterOptions:S=d,filterSelectedOptions:R=!1,freeSolo:A=!1,getOptionDisabled:C,getOptionKey:w,getOptionLabel:L=e=>e.label??e,groupBy:O,handleHomeEndKeys:M=!e.freeSolo,id:D,includeInputInList:T=!1,inputValue:N,isOptionEqualToValue:j=(e,t)=>e===t,multiple:z=!1,onChange:F,onClose:H,onHighlightChange:W,onInputChange:E,onOpen:V,open:q,openOnFocus:B=!1,options:U,readOnly:G=!1,selectOnFocus:K=!e.freeSolo,value:J}=e,Q=(0,n.Z)(D),X=L;X=e=>{let t=L(e);return"string"!=typeof t?String(t):t};let Y=o.useRef(!1),_=o.useRef(!0),ee=o.useRef(null),et=o.useRef(null),[er,eo]=o.useState(null),[en,ea]=o.useState(-1),ei=u?0:-1,el=o.useRef(ei),ep=o.useRef(f(y??J,z,X)).current,[es,eu]=(0,a.Z)({controlled:J,default:y,name:x}),[ed,ec]=(0,a.Z)({controlled:N,default:ep,name:x,state:"inputValue"}),[eg,ef]=o.useState(!1),eh=o.useCallback((e,t,r)=>{if(!(z?es.length<t.length:null!==t)&&!b)return;let o=f(t,z,X);ed!==o&&(ec(o),E&&E(e,o,r))},[X,ed,z,E,ec,b,es]),[em,eb]=(0,a.Z)({controlled:q,default:!1,name:x,state:"open"}),[ev,ex]=o.useState(!0),ey=!z&&null!=es&&ed===X(es),e$=em&&!G,eZ=e$?S(U.filter(e=>!(R&&(z?es:[es]).some(t=>null!==t&&j(e,t)))),{inputValue:ey&&ev?"":ed,getOptionLabel:X}):[],eP=(0,i.Z)({filteredOptions:eZ,value:es,inputValue:ed});o.useEffect(()=>{let e=es!==eP.value;(!eg||e)&&(!A||e)&&eh(null,es,"reset")},[es,eh,eg,eP.value,A]);let eI=em&&eZ.length>0&&!G,ek=(0,l.Z)(e=>{-1===e?ee.current.focus():er.querySelector(`[data-tag-index="${e}"]`).focus()});o.useEffect(()=>{z&&en>es.length-1&&(ea(-1),ek(-1))},[es,z,en,ek]);let eS=(0,l.Z)(({event:e,index:t,reason:o})=>{if(el.current=t,-1===t?ee.current.removeAttribute("aria-activedescendant"):ee.current.setAttribute("aria-activedescendant",`${Q}-option-${t}`),W&&["mouse","keyboard","touch"].includes(o)&&W(e,-1===t?null:eZ[t],o),!et.current)return;let n=et.current.querySelector(`[role="option"].${r}-focused`);n&&(n.classList.remove(`${r}-focused`),n.classList.remove(`${r}-focusVisible`));let a=et.current;if("listbox"!==et.current.getAttribute("role")&&(a=et.current.parentElement.querySelector('[role="listbox"]')),!a)return;if(-1===t){a.scrollTop=0;return}let i=et.current.querySelector(`[data-option-index="${t}"]`);if(i&&(i.classList.add(`${r}-focused`),"keyboard"===o&&i.classList.add(`${r}-focusVisible`),a.scrollHeight>a.clientHeight&&"mouse"!==o&&"touch"!==o)){let e=a.clientHeight+a.scrollTop,t=i.offsetTop+i.offsetHeight;t>e?a.scrollTop=t-a.clientHeight:i.offsetTop-i.offsetHeight*(O?1.3:0)<a.scrollTop&&(a.scrollTop=i.offsetTop-i.offsetHeight*(O?1.3:0))}}),eR=(0,l.Z)(({event:e,diff:t,direction:r="next",reason:o})=>{if(!e$)return;let n=function(e,t){if(!et.current||e<0||e>=eZ.length)return -1;let r=e;for(;;){let o=et.current.querySelector(`[data-option-index="${r}"]`),n=!I&&(!o||o.disabled||"true"===o.getAttribute("aria-disabled"));if(o&&o.hasAttribute("tabindex")&&!n)return r;if((r="next"===t?(r+1)%eZ.length:(r-1+eZ.length)%eZ.length)===e)return -1}}((()=>{let e=eZ.length-1;if("reset"===t)return ei;if("start"===t)return 0;if("end"===t)return e;let r=el.current+t;return r<0?-1===r&&T?-1:k&&-1!==el.current||Math.abs(t)>1?0:e:r>e?r===e+1&&T?-1:k||Math.abs(t)>1?e:0:r})(),r);if(eS({index:n,reason:o,event:e}),s&&"reset"!==t){if(-1===n)ee.current.value=ed;else{let e=X(eZ[n]);ee.current.value=e,0===e.toLowerCase().indexOf(ed.toLowerCase())&&ed.length>0&&ee.current.setSelectionRange(ed.length,e.length)}}}),eA=()=>{var e;if(-1!==el.current&&eP.filteredOptions&&eP.filteredOptions.length!==eZ.length&&eP.inputValue===ed&&(z?es.length===eP.value.length&&eP.value.every((e,t)=>X(es[t])===X(e)):((e=eP.value)?X(e):"")===(es?X(es):""))){let e=eP.filteredOptions[el.current];if(e)return eZ.findIndex(t=>X(t)===X(e))}return -1},eC=o.useCallback(()=>{if(!e$)return;let e=eA();if(-1!==e){el.current=e;return}let t=z?es[0]:es;if(0===eZ.length||null==t){eR({diff:"reset"});return}if(et.current){if(null!=t){let e=eZ[el.current];if(z&&e&&-1!==es.findIndex(t=>j(e,t)))return;let r=eZ.findIndex(e=>j(e,t));-1===r?eR({diff:"reset"}):eS({index:r});return}if(el.current>=eZ.length-1){eS({index:eZ.length-1});return}eS({index:el.current})}},[eZ.length,!z&&es,R,eR,eS,e$,ed,z]),ew=(0,l.Z)(e=>{(0,p.Z)(et,e),e&&eC()});o.useEffect(()=>{eC()},[eC]);let eL=e=>{!em&&(eb(!0),ex(!0),V&&V(e))},eO=(e,t)=>{em&&(eb(!1),H&&H(e,t))},eM=(e,t,r,o)=>{if(z){if(es.length===t.length&&es.every((e,r)=>e===t[r]))return}else if(es===t)return;F&&F(e,t,r,o),eu(t)},eD=o.useRef(!1),eT=(e,t,r="selectOption",o="options")=>{let n=r,a=t;if(z){let e=(a=Array.isArray(es)?es.slice():[]).findIndex(e=>j(t,e));-1===e?a.push(t):"freeSolo"!==o&&(a.splice(e,1),n="removeOption")}eh(e,a,n),eM(e,a,n,{option:t}),Z||e&&(e.ctrlKey||e.metaKey)||eO(e,n),(!0===m||"touch"===m&&eD.current||"mouse"===m&&!eD.current)&&ee.current.blur()},eN=(e,t)=>{if(!z)return;""===ed&&eO(e,"toggleInput");let r=en;-1===en?""===ed&&"previous"===t&&(r=es.length-1):((r+="next"===t?1:-1)<0&&(r=0),r===es.length&&(r=-1)),ea(r=function(e,t){if(-1===e)return -1;let r=e;for(;;){if("next"===t&&r===es.length||"previous"===t&&-1===r)return -1;let e=er.querySelector(`[data-tag-index="${r}"]`);if(e&&e.hasAttribute("tabindex")&&!e.disabled&&"true"!==e.getAttribute("aria-disabled"))return r;r+="next"===t?1:-1}}(r,t)),ek(r)},ej=e=>{Y.current=!0,ec(""),E&&E(e,"","clear"),eM(e,z?[]:null,"clear")},ez=e=>t=>{if(e.onKeyDown&&e.onKeyDown(t),!t.defaultMuiPrevented&&(-1===en||["ArrowLeft","ArrowRight"].includes(t.key)||(ea(-1),ek(-1)),229!==t.which))switch(t.key){case"Home":e$&&M&&(t.preventDefault(),eR({diff:"start",direction:"next",reason:"keyboard",event:t}));break;case"End":e$&&M&&(t.preventDefault(),eR({diff:"end",direction:"previous",reason:"keyboard",event:t}));break;case"PageUp":t.preventDefault(),eR({diff:-5,direction:"previous",reason:"keyboard",event:t}),eL(t);break;case"PageDown":t.preventDefault(),eR({diff:5,direction:"next",reason:"keyboard",event:t}),eL(t);break;case"ArrowDown":t.preventDefault(),eR({diff:1,direction:"next",reason:"keyboard",event:t}),eL(t);break;case"ArrowUp":t.preventDefault(),eR({diff:-1,direction:"previous",reason:"keyboard",event:t}),eL(t);break;case"ArrowLeft":eN(t,"previous");break;case"ArrowRight":eN(t,"next");break;case"Enter":if(-1!==el.current&&e$){let e=eZ[el.current],r=!!C&&C(e);if(t.preventDefault(),r)return;eT(t,e,"selectOption"),s&&ee.current.setSelectionRange(ee.current.value.length,ee.current.value.length)}else A&&""!==ed&&!1===ey&&(z&&t.preventDefault(),eT(t,ed,"createOption","freeSolo"));break;case"Escape":e$?(t.preventDefault(),t.stopPropagation(),eO(t,"escape")):v&&(""!==ed||z&&es.length>0)&&(t.preventDefault(),t.stopPropagation(),ej(t));break;case"Backspace":if(z&&!G&&""===ed&&es.length>0){let e=-1===en?es.length-1:en,r=es.slice();r.splice(e,1),eM(t,r,"removeOption",{option:es[e]})}break;case"Delete":if(z&&!G&&""===ed&&es.length>0&&-1!==en){let e=es.slice();e.splice(en,1),eM(t,e,"removeOption",{option:es[en]})}}},eF=e=>{ef(!0),B&&!Y.current&&eL(e)},eH=e=>{if(t(et)){ee.current.focus();return}ef(!1),_.current=!0,Y.current=!1,h&&-1!==el.current&&e$?eT(e,eZ[el.current],"blur"):h&&A&&""!==ed?eT(e,ed,"blur","freeSolo"):b&&eh(e,es,"blur"),eO(e,"blur")},eW=e=>{let t=e.target.value;ed!==t&&(ec(t),ex(!1),E&&E(e,t,"input")),""===t?$||z||eM(e,null,"clear"):eL(e)},eE=e=>{let t=Number(e.currentTarget.getAttribute("data-option-index"));el.current!==t&&eS({event:e,index:t,reason:"mouse"})},eV=e=>{eS({event:e,index:Number(e.currentTarget.getAttribute("data-option-index")),reason:"touch"}),eD.current=!0},eq=e=>{let t=Number(e.currentTarget.getAttribute("data-option-index"));eT(e,eZ[t],"selectOption"),eD.current=!1},eB=e=>t=>{let r=es.slice();r.splice(e,1),eM(t,r,"removeOption",{option:es[e]})},eU=e=>{em?eO(e,"toggleInput"):eL(e)},eG=e=>{e.currentTarget.contains(e.target)&&e.target.getAttribute("id")!==Q&&e.preventDefault()},eK=e=>{e.currentTarget.contains(e.target)&&(ee.current.focus(),K&&_.current&&ee.current.selectionEnd-ee.current.selectionStart==0&&ee.current.select(),_.current=!1)},eJ=e=>{P||""!==ed&&em||eU(e)},eQ=A&&ed.length>0;eQ=eQ||(z?es.length>0:null!==es);let eX=eZ;return O&&(eX=eZ.reduce((e,t,r)=>{let o=O(t);return e.length>0&&e[e.length-1].group===o?e[e.length-1].options.push(t):e.push({key:r,index:r,group:o,options:[t]}),e},[])),P&&eg&&eH(),{getRootProps:(e={})=>({...e,onKeyDown:ez(e),onMouseDown:eG,onClick:eK}),getInputLabelProps:()=>({id:`${Q}-label`,htmlFor:Q}),getInputProps:()=>({id:Q,value:ed,onBlur:eH,onFocus:eF,onChange:eW,onMouseDown:eJ,"aria-activedescendant":e$?"":null,"aria-autocomplete":s?"both":"list","aria-controls":eI?`${Q}-listbox`:void 0,"aria-expanded":eI,autoComplete:"off",ref:ee,autoCapitalize:"none",spellCheck:"false",role:"combobox",disabled:P}),getClearProps:()=>({tabIndex:-1,type:"button",onClick:ej}),getPopupIndicatorProps:()=>({tabIndex:-1,type:"button",onClick:eU}),getTagProps:({index:e})=>({key:e,"data-tag-index":e,tabIndex:-1,...!G&&{onDelete:eB(e)}}),getListboxProps:()=>({role:"listbox",id:`${Q}-listbox`,"aria-labelledby":`${Q}-label`,ref:ew,onMouseDown:e=>{e.preventDefault()}}),getOptionProps:({index:e,option:t})=>{let r=(z?es:[es]).some(e=>null!=e&&j(t,e)),o=!!C&&C(t);return{key:w?.(t)??X(t),tabIndex:-1,role:"option",id:`${Q}-option-${e}`,onMouseMove:eE,onClick:eq,onTouchStart:eV,"data-option-index":e,"aria-disabled":o,"aria-selected":r}},id:Q,inputValue:ed,value:es,dirty:eQ,expanded:e$&&er,popupOpen:e$,focused:eg||-1!==en,anchorEl:er,setAnchorEl:eo,focusedTag:en,groupedOptions:eX}}},50591:(e,t,r)=>{r.d(t,{Z:()=>n});var o=r(17577);let n=e=>{let t=o.useRef({});return o.useEffect(()=>{t.current=e}),t.current}}};