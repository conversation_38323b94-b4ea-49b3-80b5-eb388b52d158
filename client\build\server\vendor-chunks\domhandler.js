"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/domhandler";
exports.ids = ["vendor-chunks/domhandler"];
exports.modules = {

/***/ "(ssr)/./node_modules/domhandler/lib/esm/index.js":
/*!**************************************************!*\
  !*** ./node_modules/domhandler/lib/esm/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CDATA: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.CDATA),\n/* harmony export */   Comment: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Comment),\n/* harmony export */   DataNode: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.DataNode),\n/* harmony export */   Document: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Document),\n/* harmony export */   DomHandler: () => (/* binding */ DomHandler),\n/* harmony export */   Element: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Element),\n/* harmony export */   Node: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Node),\n/* harmony export */   NodeWithChildren: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.NodeWithChildren),\n/* harmony export */   ProcessingInstruction: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.ProcessingInstruction),\n/* harmony export */   Text: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Text),\n/* harmony export */   cloneNode: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.cloneNode),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   hasChildren: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.hasChildren),\n/* harmony export */   isCDATA: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isCDATA),\n/* harmony export */   isComment: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isComment),\n/* harmony export */   isDirective: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isDirective),\n/* harmony export */   isDocument: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isDocument),\n/* harmony export */   isTag: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isTag),\n/* harmony export */   isText: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isText)\n/* harmony export */ });\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domelementtype */ \"(ssr)/./node_modules/domelementtype/lib/esm/index.js\");\n/* harmony import */ var _node_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node.js */ \"(ssr)/./node_modules/domhandler/lib/esm/node.js\");\n\n\n\n// Default options\nconst defaultOpts = {\n    withStartIndices: false,\n    withEndIndices: false,\n    xmlMode: false,\n};\nclass DomHandler {\n    /**\n     * @param callback Called once parsing has completed.\n     * @param options Settings for the handler.\n     * @param elementCB Callback whenever a tag is closed.\n     */\n    constructor(callback, options, elementCB) {\n        /** The elements of the DOM */\n        this.dom = [];\n        /** The root element for the DOM */\n        this.root = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Document(this.dom);\n        /** Indicated whether parsing has been completed. */\n        this.done = false;\n        /** Stack of open tags. */\n        this.tagStack = [this.root];\n        /** A data node that is still being written to. */\n        this.lastNode = null;\n        /** Reference to the parser instance. Used for location information. */\n        this.parser = null;\n        // Make it possible to skip arguments, for backwards-compatibility\n        if (typeof options === \"function\") {\n            elementCB = options;\n            options = defaultOpts;\n        }\n        if (typeof callback === \"object\") {\n            options = callback;\n            callback = undefined;\n        }\n        this.callback = callback !== null && callback !== void 0 ? callback : null;\n        this.options = options !== null && options !== void 0 ? options : defaultOpts;\n        this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n    }\n    onparserinit(parser) {\n        this.parser = parser;\n    }\n    // Resets the handler back to starting state\n    onreset() {\n        this.dom = [];\n        this.root = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Document(this.dom);\n        this.done = false;\n        this.tagStack = [this.root];\n        this.lastNode = null;\n        this.parser = null;\n    }\n    // Signals the handler that parsing is done\n    onend() {\n        if (this.done)\n            return;\n        this.done = true;\n        this.parser = null;\n        this.handleCallback(null);\n    }\n    onerror(error) {\n        this.handleCallback(error);\n    }\n    onclosetag() {\n        this.lastNode = null;\n        const elem = this.tagStack.pop();\n        if (this.options.withEndIndices) {\n            elem.endIndex = this.parser.endIndex;\n        }\n        if (this.elementCB)\n            this.elementCB(elem);\n    }\n    onopentag(name, attribs) {\n        const type = this.options.xmlMode ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Tag : undefined;\n        const element = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Element(name, attribs, undefined, type);\n        this.addNode(element);\n        this.tagStack.push(element);\n    }\n    ontext(data) {\n        const { lastNode } = this;\n        if (lastNode && lastNode.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text) {\n            lastNode.data += data;\n            if (this.options.withEndIndices) {\n                lastNode.endIndex = this.parser.endIndex;\n            }\n        }\n        else {\n            const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Text(data);\n            this.addNode(node);\n            this.lastNode = node;\n        }\n    }\n    oncomment(data) {\n        if (this.lastNode && this.lastNode.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment) {\n            this.lastNode.data += data;\n            return;\n        }\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Comment(data);\n        this.addNode(node);\n        this.lastNode = node;\n    }\n    oncommentend() {\n        this.lastNode = null;\n    }\n    oncdatastart() {\n        const text = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Text(\"\");\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.CDATA([text]);\n        this.addNode(node);\n        text.parent = node;\n        this.lastNode = text;\n    }\n    oncdataend() {\n        this.lastNode = null;\n    }\n    onprocessinginstruction(name, data) {\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.ProcessingInstruction(name, data);\n        this.addNode(node);\n    }\n    handleCallback(error) {\n        if (typeof this.callback === \"function\") {\n            this.callback(error, this.dom);\n        }\n        else if (error) {\n            throw error;\n        }\n    }\n    addNode(node) {\n        const parent = this.tagStack[this.tagStack.length - 1];\n        const previousSibling = parent.children[parent.children.length - 1];\n        if (this.options.withStartIndices) {\n            node.startIndex = this.parser.startIndex;\n        }\n        if (this.options.withEndIndices) {\n            node.endIndex = this.parser.endIndex;\n        }\n        parent.children.push(node);\n        if (previousSibling) {\n            node.prev = previousSibling;\n            previousSibling.next = node;\n        }\n        node.parent = parent;\n        this.lastNode = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DomHandler);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domhandler/lib/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domhandler/lib/esm/node.js":
/*!*************************************************!*\
  !*** ./node_modules/domhandler/lib/esm/node.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CDATA: () => (/* binding */ CDATA),\n/* harmony export */   Comment: () => (/* binding */ Comment),\n/* harmony export */   DataNode: () => (/* binding */ DataNode),\n/* harmony export */   Document: () => (/* binding */ Document),\n/* harmony export */   Element: () => (/* binding */ Element),\n/* harmony export */   Node: () => (/* binding */ Node),\n/* harmony export */   NodeWithChildren: () => (/* binding */ NodeWithChildren),\n/* harmony export */   ProcessingInstruction: () => (/* binding */ ProcessingInstruction),\n/* harmony export */   Text: () => (/* binding */ Text),\n/* harmony export */   cloneNode: () => (/* binding */ cloneNode),\n/* harmony export */   hasChildren: () => (/* binding */ hasChildren),\n/* harmony export */   isCDATA: () => (/* binding */ isCDATA),\n/* harmony export */   isComment: () => (/* binding */ isComment),\n/* harmony export */   isDirective: () => (/* binding */ isDirective),\n/* harmony export */   isDocument: () => (/* binding */ isDocument),\n/* harmony export */   isTag: () => (/* binding */ isTag),\n/* harmony export */   isText: () => (/* binding */ isText)\n/* harmony export */ });\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domelementtype */ \"(ssr)/./node_modules/domelementtype/lib/esm/index.js\");\n\n/**\n * This object will be used as the prototype for Nodes when creating a\n * DOM-Level-1-compliant structure.\n */\nclass Node {\n    constructor() {\n        /** Parent of the node */\n        this.parent = null;\n        /** Previous sibling */\n        this.prev = null;\n        /** Next sibling */\n        this.next = null;\n        /** The start index of the node. Requires `withStartIndices` on the handler to be `true. */\n        this.startIndex = null;\n        /** The end index of the node. Requires `withEndIndices` on the handler to be `true. */\n        this.endIndex = null;\n    }\n    // Read-write aliases for properties\n    /**\n     * Same as {@link parent}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get parentNode() {\n        return this.parent;\n    }\n    set parentNode(parent) {\n        this.parent = parent;\n    }\n    /**\n     * Same as {@link prev}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get previousSibling() {\n        return this.prev;\n    }\n    set previousSibling(prev) {\n        this.prev = prev;\n    }\n    /**\n     * Same as {@link next}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nextSibling() {\n        return this.next;\n    }\n    set nextSibling(next) {\n        this.next = next;\n    }\n    /**\n     * Clone this node, and optionally its children.\n     *\n     * @param recursive Clone child nodes as well.\n     * @returns A clone of the node.\n     */\n    cloneNode(recursive = false) {\n        return cloneNode(this, recursive);\n    }\n}\n/**\n * A node that contains some data.\n */\nclass DataNode extends Node {\n    /**\n     * @param data The content of the data node\n     */\n    constructor(data) {\n        super();\n        this.data = data;\n    }\n    /**\n     * Same as {@link data}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nodeValue() {\n        return this.data;\n    }\n    set nodeValue(data) {\n        this.data = data;\n    }\n}\n/**\n * Text within the document.\n */\nclass Text extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text;\n    }\n    get nodeType() {\n        return 3;\n    }\n}\n/**\n * Comments within the document.\n */\nclass Comment extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment;\n    }\n    get nodeType() {\n        return 8;\n    }\n}\n/**\n * Processing instructions, including doc types.\n */\nclass ProcessingInstruction extends DataNode {\n    constructor(name, data) {\n        super(data);\n        this.name = name;\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Directive;\n    }\n    get nodeType() {\n        return 1;\n    }\n}\n/**\n * A `Node` that can have children.\n */\nclass NodeWithChildren extends Node {\n    /**\n     * @param children Children of the node. Only certain node types can have children.\n     */\n    constructor(children) {\n        super();\n        this.children = children;\n    }\n    // Aliases\n    /** First child of the node. */\n    get firstChild() {\n        var _a;\n        return (_a = this.children[0]) !== null && _a !== void 0 ? _a : null;\n    }\n    /** Last child of the node. */\n    get lastChild() {\n        return this.children.length > 0\n            ? this.children[this.children.length - 1]\n            : null;\n    }\n    /**\n     * Same as {@link children}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get childNodes() {\n        return this.children;\n    }\n    set childNodes(children) {\n        this.children = children;\n    }\n}\nclass CDATA extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.CDATA;\n    }\n    get nodeType() {\n        return 4;\n    }\n}\n/**\n * The root node of the document.\n */\nclass Document extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Root;\n    }\n    get nodeType() {\n        return 9;\n    }\n}\n/**\n * An element within the DOM.\n */\nclass Element extends NodeWithChildren {\n    /**\n     * @param name Name of the tag, eg. `div`, `span`.\n     * @param attribs Object mapping attribute names to attribute values.\n     * @param children Children of the node.\n     */\n    constructor(name, attribs, children = [], type = name === \"script\"\n        ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Script\n        : name === \"style\"\n            ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Style\n            : domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Tag) {\n        super(children);\n        this.name = name;\n        this.attribs = attribs;\n        this.type = type;\n    }\n    get nodeType() {\n        return 1;\n    }\n    // DOM Level 1 aliases\n    /**\n     * Same as {@link name}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get tagName() {\n        return this.name;\n    }\n    set tagName(name) {\n        this.name = name;\n    }\n    get attributes() {\n        return Object.keys(this.attribs).map((name) => {\n            var _a, _b;\n            return ({\n                name,\n                value: this.attribs[name],\n                namespace: (_a = this[\"x-attribsNamespace\"]) === null || _a === void 0 ? void 0 : _a[name],\n                prefix: (_b = this[\"x-attribsPrefix\"]) === null || _b === void 0 ? void 0 : _b[name],\n            });\n        });\n    }\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node is a `Element`, `false` otherwise.\n */\nfunction isTag(node) {\n    return (0,domelementtype__WEBPACK_IMPORTED_MODULE_0__.isTag)(node);\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `CDATA`, `false` otherwise.\n */\nfunction isCDATA(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.CDATA;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Text`, `false` otherwise.\n */\nfunction isText(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Comment`, `false` otherwise.\n */\nfunction isComment(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDirective(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Directive;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDocument(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Root;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has children, `false` otherwise.\n */\nfunction hasChildren(node) {\n    return Object.prototype.hasOwnProperty.call(node, \"children\");\n}\n/**\n * Clone a node, and optionally its children.\n *\n * @param recursive Clone child nodes as well.\n * @returns A clone of the node.\n */\nfunction cloneNode(node, recursive = false) {\n    let result;\n    if (isText(node)) {\n        result = new Text(node.data);\n    }\n    else if (isComment(node)) {\n        result = new Comment(node.data);\n    }\n    else if (isTag(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Element(node.name, { ...node.attribs }, children);\n        children.forEach((child) => (child.parent = clone));\n        if (node.namespace != null) {\n            clone.namespace = node.namespace;\n        }\n        if (node[\"x-attribsNamespace\"]) {\n            clone[\"x-attribsNamespace\"] = { ...node[\"x-attribsNamespace\"] };\n        }\n        if (node[\"x-attribsPrefix\"]) {\n            clone[\"x-attribsPrefix\"] = { ...node[\"x-attribsPrefix\"] };\n        }\n        result = clone;\n    }\n    else if (isCDATA(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new CDATA(children);\n        children.forEach((child) => (child.parent = clone));\n        result = clone;\n    }\n    else if (isDocument(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Document(children);\n        children.forEach((child) => (child.parent = clone));\n        if (node[\"x-mode\"]) {\n            clone[\"x-mode\"] = node[\"x-mode\"];\n        }\n        result = clone;\n    }\n    else if (isDirective(node)) {\n        const instruction = new ProcessingInstruction(node.name, node.data);\n        if (node[\"x-name\"] != null) {\n            instruction[\"x-name\"] = node[\"x-name\"];\n            instruction[\"x-publicId\"] = node[\"x-publicId\"];\n            instruction[\"x-systemId\"] = node[\"x-systemId\"];\n        }\n        result = instruction;\n    }\n    else {\n        throw new Error(`Not implemented yet: ${node.type}`);\n    }\n    result.startIndex = node.startIndex;\n    result.endIndex = node.endIndex;\n    if (node.sourceCodeLocation != null) {\n        result.sourceCodeLocation = node.sourceCodeLocation;\n    }\n    return result;\n}\nfunction cloneChildren(childs) {\n    const children = childs.map((child) => cloneNode(child, true));\n    for (let i = 1; i < children.length; i++) {\n        children[i].prev = children[i - 1];\n        children[i - 1].next = children[i];\n    }\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domhandler/lib/esm/node.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domhandler/lib/esm/index.js":
/*!**************************************************!*\
  !*** ./node_modules/domhandler/lib/esm/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CDATA: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.CDATA),\n/* harmony export */   Comment: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Comment),\n/* harmony export */   DataNode: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.DataNode),\n/* harmony export */   Document: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Document),\n/* harmony export */   DomHandler: () => (/* binding */ DomHandler),\n/* harmony export */   Element: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Element),\n/* harmony export */   Node: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Node),\n/* harmony export */   NodeWithChildren: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.NodeWithChildren),\n/* harmony export */   ProcessingInstruction: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.ProcessingInstruction),\n/* harmony export */   Text: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Text),\n/* harmony export */   cloneNode: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.cloneNode),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   hasChildren: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.hasChildren),\n/* harmony export */   isCDATA: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isCDATA),\n/* harmony export */   isComment: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isComment),\n/* harmony export */   isDirective: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isDirective),\n/* harmony export */   isDocument: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isDocument),\n/* harmony export */   isTag: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isTag),\n/* harmony export */   isText: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isText)\n/* harmony export */ });\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n/* harmony import */ var _node_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node.js */ \"(rsc)/./node_modules/domhandler/lib/esm/node.js\");\n\n\n\n// Default options\nconst defaultOpts = {\n    withStartIndices: false,\n    withEndIndices: false,\n    xmlMode: false,\n};\nclass DomHandler {\n    /**\n     * @param callback Called once parsing has completed.\n     * @param options Settings for the handler.\n     * @param elementCB Callback whenever a tag is closed.\n     */\n    constructor(callback, options, elementCB) {\n        /** The elements of the DOM */\n        this.dom = [];\n        /** The root element for the DOM */\n        this.root = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Document(this.dom);\n        /** Indicated whether parsing has been completed. */\n        this.done = false;\n        /** Stack of open tags. */\n        this.tagStack = [this.root];\n        /** A data node that is still being written to. */\n        this.lastNode = null;\n        /** Reference to the parser instance. Used for location information. */\n        this.parser = null;\n        // Make it possible to skip arguments, for backwards-compatibility\n        if (typeof options === \"function\") {\n            elementCB = options;\n            options = defaultOpts;\n        }\n        if (typeof callback === \"object\") {\n            options = callback;\n            callback = undefined;\n        }\n        this.callback = callback !== null && callback !== void 0 ? callback : null;\n        this.options = options !== null && options !== void 0 ? options : defaultOpts;\n        this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n    }\n    onparserinit(parser) {\n        this.parser = parser;\n    }\n    // Resets the handler back to starting state\n    onreset() {\n        this.dom = [];\n        this.root = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Document(this.dom);\n        this.done = false;\n        this.tagStack = [this.root];\n        this.lastNode = null;\n        this.parser = null;\n    }\n    // Signals the handler that parsing is done\n    onend() {\n        if (this.done)\n            return;\n        this.done = true;\n        this.parser = null;\n        this.handleCallback(null);\n    }\n    onerror(error) {\n        this.handleCallback(error);\n    }\n    onclosetag() {\n        this.lastNode = null;\n        const elem = this.tagStack.pop();\n        if (this.options.withEndIndices) {\n            elem.endIndex = this.parser.endIndex;\n        }\n        if (this.elementCB)\n            this.elementCB(elem);\n    }\n    onopentag(name, attribs) {\n        const type = this.options.xmlMode ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Tag : undefined;\n        const element = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Element(name, attribs, undefined, type);\n        this.addNode(element);\n        this.tagStack.push(element);\n    }\n    ontext(data) {\n        const { lastNode } = this;\n        if (lastNode && lastNode.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text) {\n            lastNode.data += data;\n            if (this.options.withEndIndices) {\n                lastNode.endIndex = this.parser.endIndex;\n            }\n        }\n        else {\n            const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Text(data);\n            this.addNode(node);\n            this.lastNode = node;\n        }\n    }\n    oncomment(data) {\n        if (this.lastNode && this.lastNode.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment) {\n            this.lastNode.data += data;\n            return;\n        }\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Comment(data);\n        this.addNode(node);\n        this.lastNode = node;\n    }\n    oncommentend() {\n        this.lastNode = null;\n    }\n    oncdatastart() {\n        const text = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Text(\"\");\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.CDATA([text]);\n        this.addNode(node);\n        text.parent = node;\n        this.lastNode = text;\n    }\n    oncdataend() {\n        this.lastNode = null;\n    }\n    onprocessinginstruction(name, data) {\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.ProcessingInstruction(name, data);\n        this.addNode(node);\n    }\n    handleCallback(error) {\n        if (typeof this.callback === \"function\") {\n            this.callback(error, this.dom);\n        }\n        else if (error) {\n            throw error;\n        }\n    }\n    addNode(node) {\n        const parent = this.tagStack[this.tagStack.length - 1];\n        const previousSibling = parent.children[parent.children.length - 1];\n        if (this.options.withStartIndices) {\n            node.startIndex = this.parser.startIndex;\n        }\n        if (this.options.withEndIndices) {\n            node.endIndex = this.parser.endIndex;\n        }\n        parent.children.push(node);\n        if (previousSibling) {\n            node.prev = previousSibling;\n            previousSibling.next = node;\n        }\n        node.parent = parent;\n        this.lastNode = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DomHandler);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domhandler/lib/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domhandler/lib/esm/node.js":
/*!*************************************************!*\
  !*** ./node_modules/domhandler/lib/esm/node.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CDATA: () => (/* binding */ CDATA),\n/* harmony export */   Comment: () => (/* binding */ Comment),\n/* harmony export */   DataNode: () => (/* binding */ DataNode),\n/* harmony export */   Document: () => (/* binding */ Document),\n/* harmony export */   Element: () => (/* binding */ Element),\n/* harmony export */   Node: () => (/* binding */ Node),\n/* harmony export */   NodeWithChildren: () => (/* binding */ NodeWithChildren),\n/* harmony export */   ProcessingInstruction: () => (/* binding */ ProcessingInstruction),\n/* harmony export */   Text: () => (/* binding */ Text),\n/* harmony export */   cloneNode: () => (/* binding */ cloneNode),\n/* harmony export */   hasChildren: () => (/* binding */ hasChildren),\n/* harmony export */   isCDATA: () => (/* binding */ isCDATA),\n/* harmony export */   isComment: () => (/* binding */ isComment),\n/* harmony export */   isDirective: () => (/* binding */ isDirective),\n/* harmony export */   isDocument: () => (/* binding */ isDocument),\n/* harmony export */   isTag: () => (/* binding */ isTag),\n/* harmony export */   isText: () => (/* binding */ isText)\n/* harmony export */ });\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n\n/**\n * This object will be used as the prototype for Nodes when creating a\n * DOM-Level-1-compliant structure.\n */\nclass Node {\n    constructor() {\n        /** Parent of the node */\n        this.parent = null;\n        /** Previous sibling */\n        this.prev = null;\n        /** Next sibling */\n        this.next = null;\n        /** The start index of the node. Requires `withStartIndices` on the handler to be `true. */\n        this.startIndex = null;\n        /** The end index of the node. Requires `withEndIndices` on the handler to be `true. */\n        this.endIndex = null;\n    }\n    // Read-write aliases for properties\n    /**\n     * Same as {@link parent}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get parentNode() {\n        return this.parent;\n    }\n    set parentNode(parent) {\n        this.parent = parent;\n    }\n    /**\n     * Same as {@link prev}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get previousSibling() {\n        return this.prev;\n    }\n    set previousSibling(prev) {\n        this.prev = prev;\n    }\n    /**\n     * Same as {@link next}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nextSibling() {\n        return this.next;\n    }\n    set nextSibling(next) {\n        this.next = next;\n    }\n    /**\n     * Clone this node, and optionally its children.\n     *\n     * @param recursive Clone child nodes as well.\n     * @returns A clone of the node.\n     */\n    cloneNode(recursive = false) {\n        return cloneNode(this, recursive);\n    }\n}\n/**\n * A node that contains some data.\n */\nclass DataNode extends Node {\n    /**\n     * @param data The content of the data node\n     */\n    constructor(data) {\n        super();\n        this.data = data;\n    }\n    /**\n     * Same as {@link data}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nodeValue() {\n        return this.data;\n    }\n    set nodeValue(data) {\n        this.data = data;\n    }\n}\n/**\n * Text within the document.\n */\nclass Text extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text;\n    }\n    get nodeType() {\n        return 3;\n    }\n}\n/**\n * Comments within the document.\n */\nclass Comment extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment;\n    }\n    get nodeType() {\n        return 8;\n    }\n}\n/**\n * Processing instructions, including doc types.\n */\nclass ProcessingInstruction extends DataNode {\n    constructor(name, data) {\n        super(data);\n        this.name = name;\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Directive;\n    }\n    get nodeType() {\n        return 1;\n    }\n}\n/**\n * A `Node` that can have children.\n */\nclass NodeWithChildren extends Node {\n    /**\n     * @param children Children of the node. Only certain node types can have children.\n     */\n    constructor(children) {\n        super();\n        this.children = children;\n    }\n    // Aliases\n    /** First child of the node. */\n    get firstChild() {\n        var _a;\n        return (_a = this.children[0]) !== null && _a !== void 0 ? _a : null;\n    }\n    /** Last child of the node. */\n    get lastChild() {\n        return this.children.length > 0\n            ? this.children[this.children.length - 1]\n            : null;\n    }\n    /**\n     * Same as {@link children}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get childNodes() {\n        return this.children;\n    }\n    set childNodes(children) {\n        this.children = children;\n    }\n}\nclass CDATA extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.CDATA;\n    }\n    get nodeType() {\n        return 4;\n    }\n}\n/**\n * The root node of the document.\n */\nclass Document extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Root;\n    }\n    get nodeType() {\n        return 9;\n    }\n}\n/**\n * An element within the DOM.\n */\nclass Element extends NodeWithChildren {\n    /**\n     * @param name Name of the tag, eg. `div`, `span`.\n     * @param attribs Object mapping attribute names to attribute values.\n     * @param children Children of the node.\n     */\n    constructor(name, attribs, children = [], type = name === \"script\"\n        ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Script\n        : name === \"style\"\n            ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Style\n            : domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Tag) {\n        super(children);\n        this.name = name;\n        this.attribs = attribs;\n        this.type = type;\n    }\n    get nodeType() {\n        return 1;\n    }\n    // DOM Level 1 aliases\n    /**\n     * Same as {@link name}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get tagName() {\n        return this.name;\n    }\n    set tagName(name) {\n        this.name = name;\n    }\n    get attributes() {\n        return Object.keys(this.attribs).map((name) => {\n            var _a, _b;\n            return ({\n                name,\n                value: this.attribs[name],\n                namespace: (_a = this[\"x-attribsNamespace\"]) === null || _a === void 0 ? void 0 : _a[name],\n                prefix: (_b = this[\"x-attribsPrefix\"]) === null || _b === void 0 ? void 0 : _b[name],\n            });\n        });\n    }\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node is a `Element`, `false` otherwise.\n */\nfunction isTag(node) {\n    return (0,domelementtype__WEBPACK_IMPORTED_MODULE_0__.isTag)(node);\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `CDATA`, `false` otherwise.\n */\nfunction isCDATA(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.CDATA;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Text`, `false` otherwise.\n */\nfunction isText(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Comment`, `false` otherwise.\n */\nfunction isComment(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDirective(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Directive;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDocument(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Root;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has children, `false` otherwise.\n */\nfunction hasChildren(node) {\n    return Object.prototype.hasOwnProperty.call(node, \"children\");\n}\n/**\n * Clone a node, and optionally its children.\n *\n * @param recursive Clone child nodes as well.\n * @returns A clone of the node.\n */\nfunction cloneNode(node, recursive = false) {\n    let result;\n    if (isText(node)) {\n        result = new Text(node.data);\n    }\n    else if (isComment(node)) {\n        result = new Comment(node.data);\n    }\n    else if (isTag(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Element(node.name, { ...node.attribs }, children);\n        children.forEach((child) => (child.parent = clone));\n        if (node.namespace != null) {\n            clone.namespace = node.namespace;\n        }\n        if (node[\"x-attribsNamespace\"]) {\n            clone[\"x-attribsNamespace\"] = { ...node[\"x-attribsNamespace\"] };\n        }\n        if (node[\"x-attribsPrefix\"]) {\n            clone[\"x-attribsPrefix\"] = { ...node[\"x-attribsPrefix\"] };\n        }\n        result = clone;\n    }\n    else if (isCDATA(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new CDATA(children);\n        children.forEach((child) => (child.parent = clone));\n        result = clone;\n    }\n    else if (isDocument(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Document(children);\n        children.forEach((child) => (child.parent = clone));\n        if (node[\"x-mode\"]) {\n            clone[\"x-mode\"] = node[\"x-mode\"];\n        }\n        result = clone;\n    }\n    else if (isDirective(node)) {\n        const instruction = new ProcessingInstruction(node.name, node.data);\n        if (node[\"x-name\"] != null) {\n            instruction[\"x-name\"] = node[\"x-name\"];\n            instruction[\"x-publicId\"] = node[\"x-publicId\"];\n            instruction[\"x-systemId\"] = node[\"x-systemId\"];\n        }\n        result = instruction;\n    }\n    else {\n        throw new Error(`Not implemented yet: ${node.type}`);\n    }\n    result.startIndex = node.startIndex;\n    result.endIndex = node.endIndex;\n    if (node.sourceCodeLocation != null) {\n        result.sourceCodeLocation = node.sourceCodeLocation;\n    }\n    return result;\n}\nfunction cloneChildren(childs) {\n    const children = childs.map((child) => cloneNode(child, true));\n    for (let i = 1; i < children.length; i++) {\n        children[i].prev = children[i - 1];\n        children[i - 1].next = children[i];\n    }\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domhandler/lib/esm/node.js\n");

/***/ })

};
;