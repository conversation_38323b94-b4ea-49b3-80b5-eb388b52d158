import { Contain<PERSON>, <PERSON><PERSON>, <PERSON> } from "@mui/material";
import CustomButton from "../ui/CustomButton";
import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";
import { websiteRoutesList } from "@/helpers/routesList";

function OfficeLocationMapAlgeria2({ t }) {
  return (
    <Container id="office-location-map" className="custom-max-width">
      <Grid
        className="container"
        justifyContent="space-between"
        container
        spacing={0}
      >
        <Grid item xs={12} sm={6}>
          <div className="content">
            <p className="sub-heading text-white">
              {t("Algeria:officeLocation2:title")}
            </p>
            <div>
              <p className="paragraph text-white" key={"tn"}>
                <span>
                  <SvglocationPin />
                </span>
                {t("Algeria:officeLocation2:address")}
              </p>
              <p className="paragraph text-white">
                <span>
                  <SvgcallUs />
                </span>
                {t("Algeria:officeLocation2:tel1")}
              </p>

              <p className="paragraph text-white">
                <span>
                  <Svgemail />
                </span>
                {t("Algeria:officeLocation2:mail")}
              </p>
            </div>
            <Link
              href={`#service-page-form`}
              className={"btn btn-outlined white"}
            >
              {t("Algeria:officeLocation2:talk")}
            </Link>
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <div className="map-frame">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d27148.861492992393!2d6.04719!3d31.726610000000004!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x125cc730a32b0f71%3A0x6befb37de361c422!2sBase%2024%20F%C3%A9vrier%20Division%20Production!5e0!3m2!1sfr!2sus!4v1728630917480!5m2!1sfr!2sus"
              allowfullscreen=""
              priority
              referrerpolicy="no-referrer-when-downgrade"
            ></iframe>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default OfficeLocationMapAlgeria2;
