"use client";

import { useTranslation } from "react-i18next";

import AuthLayout from "@/components/layouts/AuthLayout";
import ForgetPwdForm from "@/features/auth/component/ForgetPwdForm";

const page = () => {
  const { t } = useTranslation();
  return (
    <AuthLayout id="auth-layout" title={t("forgotPassword:reset")}>
      <ForgetPwdForm t={t} />
    </AuthLayout>
  );
};

export default page;
