"use client";

import { ErrorMessage, Field } from "formik";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { slug } from "@feelinglovelynow/slug";
import { debounce, FormGroup, FormLabel } from "@mui/material";

import { Visibility } from "../../../utils/constants";
import CustomTextInput from "../../../components/ui/CustomTextInput";
import "react-datepicker/dist/react-datepicker.css";
import "suneditor/dist/css/suneditor.min.css";
import CustomSunEditor from "@/components/ui/CustomSunEditor";
import CustomSelect from "@/components/ui/CustomSelect";
import CustomDatePicker from "@/components/ui/CustomDatePicker";
import DocumentImporter from "../../blog/components/DocumentImporter";

function GlossaryAddFormByLang({
  errors,
  touched,
  setFieldValue,
  values,
  language,
  update,
}) {
  const { t } = useTranslation();
  const [publishNow, setPublishNow] = useState(false);

  const hasError = (fieldName) => {
    return (
      errors[language] &&
      errors[language][fieldName] &&
      touched[language] &&
      touched[language][fieldName]
    );
  };

  const handlePaste = (event, cleanData, maxCharCount) => {
    let html = cleanData;
    html = html.replace(/<strong>(.*?)$/g, "<strong>$1</strong>");
    return html;
  };

  const frenchTitle = update
    ? t("createGlossary:editGlossaryFr")
    : t("createGlossary:addGlossaryFr");
  const englishTitle = update
    ? t("createGlossary:editGlossaryEng")
    : t("createGlossary:addGlossaryEng");

  const handleContentExtracted = (extractedContent) => {
    setFieldValue(`${language}.content`, extractedContent);
    debounce();
  };

  return (
    <>
      <p className="label-pentabell">
        {language === "en" ? englishTitle : frenchTitle}
      </p>
      <div className="inline-group">
        <div>
          <CustomTextInput
            label={t("listGlossary:word")}
            name={`${language}.word`}
            value={values.word}
            onChange={(e) => {
              const word = e.target.value;
              setFieldValue(`${language}.word`, word);
              setFieldValue(`${language}.url`, slug(word));
              setFieldValue(`${language}.letter`, word[0]?.toUpperCase());
            }}
            error={hasError("word")}
          />
        </div>
        <div>
          <CustomTextInput
            label={t("listGlossary:letter")}
            name={`${language}.letter`}
            value={values.letter}
            disabled
            error={hasError("letter")}
          />
        </div>
      </div>
      <div className="inline-group">
        <div>
          <CustomTextInput
            label={t("listGlossary:url")}
            name={`${language}.url`}
            value={values.url}
            onChange={(e) => {
              const url = e.target.value;
              setFieldValue(`${language}.url`, url);
            }}
            error={hasError("url")}
          />
        </div>
        <div>
          <CustomSelect
            label={t("createGlossary:visibility")}
            name={`${language}.visibility`}
            value={values.visibility}
            onChange={(e) =>
              setFieldValue(`${language}.visibility`, e.target.value)
            }
            options={Visibility}
            error={touched.visibility && errors.visibility}
            getOptionLabel={(item) => item}
            getOptionValue={(item) => item}
          />
        </div>
      </div>
      <br></br>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createGlossary:content")}
            </FormLabel>
            <DocumentImporter
              onContentExtracted={handleContentExtracted}
              language={language}
            />
            <CustomSunEditor
              label={t("createGlossary:content")}
              name={`${language}.content`}
              content={values.content}
              onChange={(newContent) => {
                setFieldValue(`${language}.content`, newContent);
              }}
              error={hasError("content")}
              onPaste={handlePaste}
              buttonList={[
                ["undo", "redo"],
                ["font", "fontSize", "formatBlock"],
                [
                  "bold",
                  "underline",
                  "italic",
                  "strike",
                  "subscript",
                  "superscript",
                ],
                ["fontColor", "hiliteColor"],
                ["align", "list", "lineHeight"],
                ["outdent", "indent"],
                ["table", "horizontalRule", "link", "image", "video"],
                ["fullScreen", "showBlocks", "codeView"],
                ["preview", "print"],
                ["removeFormat"],
              ]}
            />
            <ErrorMessage
              className="label-error"
              name={`${language}.content`}
              component="div"
            />
          </FormGroup>
        </div>
      </div>
      <br></br>
      <div className="inline-group">
        <div>
          <CustomTextInput
            label={t("createGlossary:metaTitle")}
            name={`${language}.metaTitle`}
            value={values.metaTitle}
            onChange={(e) => {
              const metaTitle = e.target.value;
              setFieldValue(`${language}.metaTitle`, metaTitle);
            }}
            error={hasError("metaTitle")}
            maxLength={65}
            showLength
          />
        </div>
      </div>
      <div className="inline-group">
        <div>
          <CustomTextInput
            label={t("createGlossary:metaDescription")}
            name={`${language}.metaDescription`}
            value={values.metaDescription}
            onChange={(e) => {
              const metaDescription = e.target.value;
              setFieldValue(`${language}.metaDescription`, metaDescription);
            }}
            error={hasError("metaDescription")}
            maxLength={160}
            showLength
          />
        </div>
      </div>
      <div className="inline-group">
        <div>
          <label className="label-form">
            <Field
              type="checkbox"
              name="publishNow"
              checked={publishNow}
              onChange={(e) => {
                setPublishNow(e.target.checked);
                if (e.target.checked) {
                  setFieldValue(
                    `${language}.createdAt`,
                    new Date().toISOString()
                  );
                }
              }}
            />
            {t("createGlossary:publishNow")}
          </label>
          {!publishNow && (
            <CustomDatePicker
              label={t("createGlossary:publishDate")}
              value={values.createdAt || new Date()}
              onChange={(date) => setFieldValue(`${language}.createdAt`, date)}
              error={touched.createdAt && errors.createdAt}
            />
          )}
        </div>
      </div>
      <Field
        type="hidden"
        name={`${language}.createdAt`}
        value={
          publishNow && values.createdAt
            ? new Date().toISOString()
            : values.createdAt
        }
      />
    </>
  );
}

export default GlossaryAddFormByLang;
