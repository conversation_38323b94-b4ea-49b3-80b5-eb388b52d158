"use client";

import { ErrorMessage, Field } from "formik";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import SunEditor from "suneditor-react";
import plugins from "suneditor/src/plugins";
import { slug } from "@feelinglovelynow/slug";
import {
  FormGroup,
  FormLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

import { Visibility } from "../../../utils/constants";
import "react-datepicker/dist/react-datepicker.css";
import "suneditor/dist/css/suneditor.min.css";
import { useSaveFile } from "@/features/opportunity/hooks/opportunity.hooks";

function GlossaryAddFormByLang({
  errors,
  touched,
  setFieldValue,
  values,
  language,
  update,
}) {
  const { t } = useTranslation();
  const [publishNow, setPublishNow] = useState(false);

  const hasError = (fieldName) => {
    return (
      errors[language] &&
      errors[language][fieldName] &&
      touched[language] &&
      touched[language][fieldName]
    );
  };

  const handlePaste = (event, cleanData, maxCharCount) => {
    let html = cleanData;
    html = html.replace(/<strong>(.*?)$/g, "<strong>$1</strong>");
    return html;
  };

  const useSaveFileHook = useSaveFile();

  const frenchTitle = update
    ? t("createGlossary:editGlossaryFr")
    : t("createGlossary:addGlossaryFr");
  const englishTitle = update
    ? t("createGlossary:editGlossaryEng")
    : t("createGlossary:addGlossaryEng");

  return (
    <>
      <p className="label-pentabell">
        {language === "en" ? englishTitle : frenchTitle}
      </p>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("listGlossary:word")}
              <TextField
                variant="standard"
                name={`${language}.word`}
                type="text"
                value={values.word}
                onChange={(e) => {
                  const word = e.target.value;
                  setFieldValue(`${language}.word`, word);
                  setFieldValue(`${language}.url`, slug(word));
                  setFieldValue(`${language}.letter`, word[0]?.toUpperCase());
                }}
                className={
                  "input-pentabell" + (hasError("word") ? " is-invalid" : "")
                }
              />
              <ErrorMessage
                className="label-error"
                name={`${language}.word`}
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("listGlossary:letter")}
              <TextField
                variant="standard"
                name={`${language}.letter`}
                type="text"
                value={values.letter}
                className={
                  "input-pentabell" + (hasError("letter") ? " is-invalid" : "")
                }
                disabled
              />
              <ErrorMessage
                className="label-error"
                name={`${language}.letter`}
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createGlossary:url")}
              <TextField
                variant="standard"
                name={`${language}.url`}
                type="text"
                value={values.url}
                onChange={(e) => {
                  const url = e.target.value;
                  setFieldValue(`${language}.url`, url);
                }}
                className={
                  "input-pentabell" + (hasError("url") ? " is-invalid" : "")
                }
              />
              <ErrorMessage
                className="label-error"
                name={`${language}.url`}
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createGlossary:visibility")}
              <Select
                className="select-pentabell"
                variant="standard"
                value={values.visibility || ""}
                onChange={(event) => {
                  setFieldValue(`${language}.visibility`, event.target.value);
                }}
              >
                {Visibility.map((item, index) => (
                  <MenuItem key={index} value={item}>
                    {item}
                  </MenuItem>
                ))}
              </Select>
              <ErrorMessage
                className="label-error"
                name={`${language}.visibility`}
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <br></br>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createGlossary:content")}
              <SunEditor
                setContents={values?.content || ""}
                onChange={(newContent) => {
                  setFieldValue(`${language}.content`, newContent);
                }}
                onPaste={handlePaste}
                setOptions={{
                  cleanHTML: false,
                  plugins: plugins,
                  buttonList: [
                    ["undo", "redo"],
                    ["font", "fontSize", "formatBlock"],
                    [
                      "bold",
                      "underline",
                      "italic",
                      "strike",
                      "subscript",
                      "superscript",
                    ],
                    ["fontColor", "hiliteColor"],
                    ["align", "list", "lineHeight"],
                    ["outdent", "indent"],
                    ["table", "horizontalRule", "link", "image", "video"],
                    ["fullScreen", "showBlocks", "codeView"],
                    ["preview", "print"],
                    ["removeFormat"],
                  ],
                  // imageUploadHandler: handlePhotoBlogChange,
                  defaultTag: "div",
                  minHeight: "300px",
                  maxHeight: "400px",
                  showPathLabel: false,
                  font: [
                    "Proxima-Nova-Regular",
                    "Proxima-Nova-Medium",
                    "Proxima-Nova-Semibold",
                    "Proxima-Nova-Bold",
                    "Proxima-Nova-Extrabold",
                    "Proxima-Nova-Black",
                    "Proxima-Nova-Light",
                    "Proxima-Nova-Thin",
                    "Arial",
                    "Times New Roman",
                    "Sans-Serif",
                  ],
                  charCounter: true,
                  charCounterType: "byte",
                  resizingBar: false,
                  colorList: [
                    [
                      "#234791",
                      "#d69b19",
                      "#cc3233",
                      "#009966",
                      "#0b3051",
                      "#2BBFAD",
                      "#0b305100",
                      "#0a305214",
                      "#743794",
                      "#ff0000",
                      "#ff5e00",
                      "#ffe400",
                      "#abf200",
                      "#00d8ff",
                      "#0055ff",
                      "#6600ff",
                      "#ff00dd",
                      "#000000",
                      "#ffd8d8",
                      "#fae0d4",
                      "#faf4c0",
                      "#e4f7ba",
                      "#d4f4fa",
                      "#d9e5ff",
                      "#e8d9ff",
                      "#ffd9fa",
                      "#f1f1f1",
                      "#ffa7a7",
                      "#ffc19e",
                      "#faed7d",
                      "#cef279",
                      "#b2ebf4",
                      "#b2ccff",
                      "#d1b2ff",
                      "#ffb2f5",
                      "#bdbdbd",
                      "#f15f5f",
                      "#f29661",
                      "#e5d85c",
                      "#bce55c",
                      "#5cd1e5",
                      "#6699ff",
                      "#a366ff",
                      "#f261df",
                      "#8c8c8c",
                      "#980000",
                      "#993800",
                      "#998a00",
                      "#6b9900",
                      "#008299",
                      "#003399",
                      "#3d0099",
                      "#990085",
                      "#353535",
                      "#670000",
                      "#662500",
                      "#665c00",
                      "#476600",
                      "#005766",
                      "#002266",
                      "#290066",
                      "#660058",
                      "#222222",
                    ],
                  ],
                }}
                // onImageUpload={handlePhotoBlogChange}
              />
              <ErrorMessage
                className="label-error"
                name={`${language}.content`}
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <br></br>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createGlossary:metaTitle")} (
              <span
                className={values.metaTitle?.length > 65 ? " text-danger" : ""}
              >
                {values.metaTitle?.length} / 65
              </span>
              )
              <TextField
                variant="standard"
                name={`${language}.metaTitle`}
                type="text"
                value={values.metaTitle}
                onChange={(e) => {
                  const metaTitle = e.target.value;
                  setFieldValue(`${language}.metaTitle`, metaTitle);
                }}
                className={
                  "input-pentabell" +
                  (hasError("metaTitle") ? " is-invalid" : "")
                }
              />
              <ErrorMessage
                className="label-error"
                name={`${language}.metaTitle`}
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createGlossary:metaDescription")} ({" "}
              <span
                className={
                  values.metaDescription?.length > 160 ? " text-danger" : ""
                }
              >
                {values.metaDescription?.length} / 160
              </span>{" "}
              )
              <TextField
                variant="standard"
                name={`${language}.metaDescription`}
                type="text"
                value={values.metaDescription}
                onChange={(e) => {
                  const metaDescription = e.target.value;
                  setFieldValue(`${language}.metaDescription`, metaDescription);
                }}
                className={
                  "input-pentabell" +
                  (hasError("metaDescription") ? " is-invalid" : "")
                }
              />
              <ErrorMessage
                className="label-error"
                name={`${language}.metaDescription`}
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <label className="label-form">
            <Field
              type="checkbox"
              name="publishNow"
              checked={publishNow}
              onChange={(e) => {
                setPublishNow(e.target.checked);
                if (e.target.checked) {
                  setFieldValue(
                    `${language}.createdAt`,
                    new Date().toISOString()
                  );
                }
              }}
            />
            {t("createGlossary:publishNow")}
          </label>
          {!publishNow && (
            <div>
              <FormGroup>
                <FormLabel className="label-form">
                  {t("createGlossary:publishDate")}
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DemoContainer components={["DatePicker"]}>
                      <DatePicker
                        variant="standard"
                        className="input-date"
                        format="DD/MM/YYYY"
                        value={dayjs(values.createdAt || new Date())}
                        onChange={(date) => {
                          setFieldValue(
                            `${language}.createdAt`,
                            dayjs(date).format("YYYY-MM-DD")
                          );
                        }}
                      />
                    </DemoContainer>
                  </LocalizationProvider>
                </FormLabel>
                <ErrorMessage
                  className="label-error"
                  name={`${language}.createdAt`}
                  component="div"
                />
              </FormGroup>
            </div>
          )}
        </div>
      </div>
      <Field
        type="hidden"
        name={`${language}.createdAt`}
        value={
          publishNow && values.createdAt
            ? new Date().toISOString()
            : values.createdAt
        }
      />
    </>
  );
}

export default GlossaryAddFormByLang;
