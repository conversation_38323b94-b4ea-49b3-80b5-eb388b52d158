import * as dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
const envFilePath = path.resolve(__dirname, `../.env.${process.env.NODE_ENV}`);
dotenv.config({ path: envFilePath });
import 'module-alias/register';
import validateEnv from './utils/validateEnv';
import App from './app';
import AuthController from '@/apis/auth/auth.controller';
import UserController from '@/apis/user/controllers/user.controller';
import AccountController from '@/apis/user/controllers/account.controller';
import ClientController from './apis/client/controllers/client.controller';
import ManagerController from './apis/client/controllers/manager.controller';
import SkillController from './apis/skill/skill.controller';
import CandidatController from './apis/candidat/candidat.controller';
import CandidateCertificationController from './apis/candidat/certification/candidat.certification.controller';
import CandidateExperienceController from './apis/candidat/experience/candidate.experience.controller';
import CandidateEducationController from './apis/candidat/education/candidat.education.controller';
import FilesController from './apis/storage/files.controller';
import OpportunityController from './apis/opportunity/controller/opportunity.controller';
import ShortlistController from './apis/candidat/shortList/candidate.shortlist.controller';
import RecruiterShortlistController from './apis/user/controllers/recruiter.shortlist.controller';
import ArticleController from './apis/article/article.controller';
import CategoryController from './apis/article/category/article.category.controller';
import SeoOpportunityController from './apis/opportunity/controller/seoOpportunity.controller';
import AlertController from './apis/alert/alert.controller';
import CommentController from './apis/article/commentaire/commentaire.controller';
import FavouriteController from './apis/Favourite/favourite.controller';
import { NotificationController } from './apis/notifications/notications.controller';
import { NewsletterController } from './apis/newsletter/newsletter.controller';
import { ContactController } from './apis/contact/contact.controller';
import UserSettingsController from './apis/settings/settings.controller';
import Controller from './utils/interfaces/controller.interface';
import { Router } from 'express';
import isAuthenticated from './middlewares/authentication.middleware';
import { hasRoles } from './middlewares/authorization.middleware';
import { Role } from './utils/helpers/constants';
import { getCachedData, invalidateCache } from './middlewares/cache.middleware';
import SeoTagsController from './apis/seoTags/seoTags.controller';
import { StatisticsController } from './apis/statistics/statistics.controller';
import SlideController from './apis/slide/slide.controller';
import GuideController from './apis/guide/guide.controller';
import EventController from './apis/events/event.controller';
import CategoryGuideController from './apis/guide/categoryguide/guide.category.controller';
import DownloadReport from './apis/Coprofiledownloadreport/downloadsreport.controller';
validateEnv();
class OtherControllers implements Controller {
    readonly path: string = '';
    readonly router: Router = Router();

    constructor() {
        this.router.get('/logs', isAuthenticated, hasRoles([Role.ADMIN]), async (request, response, next) => {
            try {
                const { date, fullName } = request.query;
                let LOG_FILE = `${process.env.NODE_ENV}-requests-${new Date().toISOString().substring(0, 10)}.log`;
                if (date) LOG_FILE = `${process.env.NODE_ENV}-requests-${date}.log`;
                const LOG_FILE_PATH = path.join(__dirname, '../logs/', LOG_FILE);
                const logs = await fs.readFileSync(LOG_FILE_PATH, 'utf-8').split('\n');
                logs.pop();

                if (fullName) {
                    const regex = new RegExp(`.*${fullName}.*`, 'i');
                    const parsedLogs = logs.filter((log: any) => regex.test(log));
                    return response.json({ logs: parsedLogs });
                }

                return response.json({ logs });
            } catch (error) {
                next(error);
            }
        });

        this.router.get('/cache', isAuthenticated, hasRoles([Role.ADMIN]), (request, response, next) => {
            try {
                response.send({ cache: getCachedData() });
            } catch (error) {
                next(error);
            }
        });

        this.router.delete('/cache', isAuthenticated, hasRoles([Role.ADMIN]), invalidateCache, (request, response, next) => {
            try {
                response.send({ message: 'Cache cleared!' });
            } catch (error) {
                next(error);
            }
        });
    }
}

const app = new App(
    [
        new OtherControllers(),
        new ManagerController(),
        new UserController(),
        new ClientController(),
        new AuthController(),
        new CandidatController(),
        new CandidateCertificationController(),
        new CandidateExperienceController(),
        new CandidateEducationController(),
        new AccountController(),
        new NotificationController(),
        new UserController(),
        new SkillController(),
        new FilesController(),
        new OpportunityController(),
        new DownloadReport(),
        new GuideController(),
        new CommentController(),
        new SlideController(),
        new ShortlistController(),
        new RecruiterShortlistController(),
        new ArticleController(),
        new CategoryController(),
        new UserSettingsController(),
        new CategoryGuideController(),
        new SeoOpportunityController(),
        new AlertController(),
        new NewsletterController(),
        new ContactController(),
        new FavouriteController(),
        new StatisticsController(),
        new SeoTagsController(),
        new EventController(),
    ],
    Number(process.env.APP_PORT),
);

app.listen();
