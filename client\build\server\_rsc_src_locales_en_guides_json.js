"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_locales_en_guides_json";
exports.ids = ["_rsc_src_locales_en_guides_json"];
exports.modules = {

/***/ "(rsc)/./src/locales/en/guides.json":
/*!************************************!*\
  !*** ./src/locales/en/guides.json ***!
  \************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"listGuide":"listGuide","bannerTitle":"Pentabell Industry Insights","bannerDescription":"Essential guides, checklists, and templates for payroll management, HR consulting, and workforce management.","subtitle":"Subtitle","title":"Title","subtitleDescription":"Subtitle description","addSubtitle":"Add a subtitle","addItemList":"Add Item List","remove":"Remove","categoriesGuide":"Guide Categories"}');

/***/ })

};
;