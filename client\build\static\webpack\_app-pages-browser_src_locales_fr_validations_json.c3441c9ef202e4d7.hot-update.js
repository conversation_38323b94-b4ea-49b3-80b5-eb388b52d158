"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_locales_fr_validations_json",{

/***/ "(app-pages-browser)/./src/locales/fr/validations.json":
/*!*****************************************!*\
  !*** ./src/locales/fr/validations.json ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"invalidEmail":"Email invalide","emptyField":"Veuillez remplir ce champ obligatoire !","endDate":"La date de fin doit être après la date de début","minDate":"La date de naissance doit être après 1950","maxDate":"La date doit être avant 2005","minLength":"Le champ doit comporter au moins 3 caractères","maxLength":"Le champ doit comporter au plus 20 caractères","required":"Ce champ est requis !","invalidPassword":"Le mot de passe doit comporter au moins une lettre majuscule, une lettre minuscule, un chiffre et un caractère spécial","passwordMatch":"Les mots de passe doivent correspondre","minOne":"Au moins une compétence est requise","minNationality":"Au moins une nationalité est requise","minRoles":"Au moins un role est requis","phoneFormat":"Format de numéro de téléphone invalide","companyEmailRequired":"Veuillez utiliser votre adresse e-mail professionnelle"}');

/***/ })

});