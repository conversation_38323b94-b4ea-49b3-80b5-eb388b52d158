{"version": 3, "file": "glossaries.validations.js", "sourceRoot": "", "sources": ["../../../src/apis/glossaries/glossaries.validations.ts"], "names": [], "mappings": ";;;;;;AAAA,8CAAsB;AACtB,iEAA0E;AAE1E,uDAAuD;AACvD,MAAM,mBAAmB,GAAG;IACxB,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC3C,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC/B,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAChC,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAClC,eAAe,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACxC,GAAG,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,QAAQ,EAAE,aAAG,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,+BAAQ,CAAC,CAAC;IAC/C,UAAU,EAAE,aAAG,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,iCAAU,CAAC,CAAC;IACnD,UAAU,EAAE,aAAG,CAAC,OAAO,EAAE;IACzB,SAAS,EAAE,aAAG,CAAC,IAAI,EAAE;IACrB,SAAS,EAAE,aAAG,CAAC,IAAI,EAAE;CACxB,CAAC;AAEF,qCAAqC;AACrC,MAAM,cAAc,GAAG,aAAG,CAAC,MAAM,CAAC;IAC9B,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,OAAO,CACJ,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,4BAA4B;IAC5D,aAAG,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAClC;SACA,GAAG,CAAC,CAAC,CAAC;SACN,QAAQ,EAAE,EAAE,4CAA4C;IAC7D,UAAU,EAAE,aAAG,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,iCAAU,CAAC,CAAC;CACtD,CAAC,CAAC;AAqBM,wCAAc;AAnBvB,iCAAiC;AACjC,MAAM,oBAAoB,GAAG,aAAG,CAAC,MAAM,CAAC;IACpC,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,CAC1B,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,4BAA4B;IAC5D,aAAG,CAAC,MAAM,CAAC;QACP,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAChC,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;QACpB,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE;QACrB,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE;QACvB,eAAe,EAAE,aAAG,CAAC,MAAM,EAAE;QAC7B,GAAG,EAAE,aAAG,CAAC,MAAM,EAAE;QACjB,QAAQ,EAAE,aAAG,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,+BAAQ,CAAC,CAAC;QAC/C,UAAU,EAAE,aAAG,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,iCAAU,CAAC,CAAC;QACnD,UAAU,EAAE,aAAG,CAAC,OAAO,EAAE;KAC5B,CAAC,CACL;IACD,UAAU,EAAE,aAAG,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,iCAAU,CAAC,CAAC;CACtD,CAAC,CAAC;AAEsB,oDAAoB"}