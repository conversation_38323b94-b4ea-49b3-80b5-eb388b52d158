exports.id=3428,exports.ids=[3428],exports.modules={35766:(e,a,t)=>{var s={"./ckb.js":5228,"./cs.js":88020,"./da.js":38280,"./de.js":16272,"./en.js":79175,"./es.js":35537,"./fa.js":73767,"./fr.js":47259,"./he.js":94438,"./hu.js":37503,"./index.js":22962,"./it.js":92603,"./ja.js":73302,"./ko.js":66045,"./lv.js":12972,"./nl.js":63969,"./pl.js":44751,"./pt_br.js":69738,"./ro.js":37499,"./ru.js":47429,"./se.js":31622,"./tr.js":38612,"./ua.js":59668,"./ur.js":96635,"./zh_cn.js":50869};function r(e){return t(i(e))}function i(e){if(!t.o(s,e)){var a=Error("Cannot find module '"+e+"'");throw a.code="MODULE_NOT_FOUND",a}return s[e]}r.keys=function(){return Object.keys(s)},r.resolve=i,e.exports=r,r.id=35766},39404:(e,a,t)=>{"use strict";t.d(a,{Z:()=>i});var s=t(27522),r=t(10326);let i=(0,s.Z)((0,r.jsx)("path",{d:"M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z"}),"ExpandMore")},64504:(e,a,t)=>{"use strict";t.d(a,{QZ:()=>v,qA:()=>A,$F:()=>S,He:()=>X,wv:()=>F,bh:()=>Z,b$:()=>E,KK:()=>k,Py:()=>Q,hb:()=>I,Yg:()=>O,mg:()=>Y,P0:()=>N,Cb:()=>P,el:()=>f,IX:()=>C});var s=t(2994),r=t(50967),i=t(70580),n=t(31190);let c=e=>(e.t,new Promise(async(a,t)=>{i.yX.post(r.Y.articles,e.data).then(e=>{n.Am.success("Article added successfully"),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&t(e)})})),o=e=>(e.t,new Promise(async(a,t)=>{i.yX.post(`${r.Y.articles}/auto`,e.data).then(e=>{e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&t(e)})})),l=({data:e,id:a})=>new Promise(async(t,s)=>{i.yX.put(`${r.Y.articles}/${a}/auto`,e).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&s(e)})}),u=({data:e,id:a})=>new Promise(async(t,s)=>{i.yX.put(`${r.Y.articles}/${a}`,e).then(e=>{n.Am.success("article Commun fields updated successfully"),e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&s(e)})}),d=e=>new Promise(async(a,t)=>{try{let t={};t=await i.yX.get(`${r.Y.articles}/${e.language}/blog/${e.urlArticle}`),a(t.data)}catch(e){e&&e.response&&e.response.data&&e.response.status,t(e)}}),m=({data:e,language:a,id:t})=>new Promise(async(s,c)=>{i.yX.post(`${r.Y.articles}/${a}/${t}`,e).then(e=>{"en"===a&&n.Am.success("Article english updated successfully"),"fr"===a&&n.Am.success("Article french updated successfully"),e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&c(e)})}),g=(e,a,t)=>new Promise(async(s,c)=>{try{let c=await i.xk.put(`${r.Y.articles}/${e}/${a}/desarchiver`,{archive:t});c?.data&&(n.Am.success(`Article ${t?"archived":"desarchived"} successfully`),s(c.data))}catch(e){n.Am.error(`Failed to ${t?"archive":"desarchive"} the article.`),c(e)}}),y=e=>new Promise(async(a,t)=>{try{let t=await i.yX.get(`${r.Y.categories}/${e}/all`);a(t.data)}catch(e){t(e)}}),p=e=>new Promise(async(a,t)=>{try{let t=await i.yX.get(`${r.Y.articles}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,searchQuery:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,publishDate:e.publishDate,isThreeLastArticles:e.isThreeLastArticles,isArchived:e.isArchived,categoryName:e.categoryName}});a(t.data)}catch(e){t(e)}}),h=e=>new Promise(async(a,t)=>{try{let t=await i.yX.get(`${r.Y.articles}/dashboard`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,searchQuery:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,publishDate:e.publishDate,isArchived:e.isArchived,categoryName:e.categoryName}});a(t.data)}catch(e){t(e)}}),w=e=>new Promise(async(a,t)=>{try{let t=await i.yX.get(`${r.Y.articles}/${e.language}/listarticle`);a(t.data)}catch(e){t(e)}}),$=({articleId:e,pageNumber:a,pageSize:t,sortOrder:s,name:n,approved:c,createdAt:o,paginated:l})=>new Promise(async(u,d)=>{try{let d=`${r.v}/comments/${e}?pageSize=${encodeURIComponent(t)}&pageNumber=${encodeURIComponent(a)}&sortOrder=${encodeURIComponent(s)}&paginated=${encodeURIComponent(l)}`;n&&(d+=`&name=${encodeURIComponent(n)}`),c&&(d+=`&approved=${encodeURIComponent(c)}`),o&&(d+=`&createdAt=${encodeURIComponent(new Date(o).toISOString())}`);let m=await i.yX.get(d);u(m.data)}catch(e){d(e)}}),x=(e,a)=>new Promise(async(t,s)=>{try{let s=await i.yX.get(`${r.Y.articles}/${a}/${e}`);t(s.data)}catch(e){s(e)}}),b=e=>new Promise(async(a,t)=>{try{let t=await i.xk.get(`${r.Y.comments}/detail/${e}`);a(t.data)}catch(e){t(e)}}),j=(e,a)=>new Promise(async(a,t)=>{try{let t=await i.yX.get(`${r.Y.articles}/${e}`);a(t.data)}catch(e){t(e)}});t(97980);let v=()=>(0,s.useMutation)({mutationFn:e=>c(e),onError:e=>{e.message=""}}),A=()=>(0,s.useMutation)({mutationFn:e=>o(e),onError:e=>{e.message=""}}),f=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,a)=>l(e,a),onError:e=>{e.message=""}})),N=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,a,t)=>m(e,a,t),onError:e=>{e.message=""}})),C=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:({language:e,id:a,archive:t})=>g(e,a,t),onError:e=>{console.error("Error during mutation",e),e.message=""}})),P=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,a)=>u(e,a),onError:e=>{e.message=""}})),Q=e=>(0,s.useQuery)(["category",e],async()=>await y(e)),Y=e=>(0,s.useQuery)(["service",e],async()=>await y(e)),Z=e=>(0,s.useQuery)("article",async()=>await p(e)),E=e=>(0,s.useQuery)(`articles${e.language}`,async()=>await h(e)),k=e=>(0,s.useQuery)(`articlestitles${e.language}`,async()=>await w(e)),I=(e,a={})=>(0,s.useQuery)("comment",async()=>await $(e),{...a}),F=(e,a={})=>(0,s.useQuery)(["article",e],async()=>{try{return await d(e)}catch(a){throw a.response&&404===a.response.status&&("en"===e.language?window.location.href="/blog/":window.location.href="/fr/blog/"),a}},{onError:e=>{console.error("Error fetching article:",e.message)},...a}),S=(e,a)=>(0,s.useQuery)(["article",e,a],async()=>await x(e,a)),X=e=>(0,s.useQuery)(["articleall",e],async()=>await j(e)),O=e=>(0,s.useQuery)(["comment",e],async()=>await b(e))},38626:(e,a,t)=>{"use strict";t.d(a,{yR:()=>g,Py:()=>y,xv:()=>h,xY:()=>w,VO:()=>p,Ny:()=>$});var s=t(2994),r=t(31190),i=t(50967),n=t(70580);let c=e=>{let a=e.t;return new Promise(async(t,s)=>{n.xk.post(i.Y.categoryGuides,e.data).then(e=>{r.Am.success(a("messages:categoryAdded")),e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(e?.response?.data?.status===409||e?.status===409)&&r.Am.warning(a("messages:categoryNameExists")),e&&s(e)})})},o=e=>new Promise(async(a,t)=>{try{let t=await (0,n.xk)(`${i.Y.categoryGuides}/catgory/${e}/all`);a(t.data)}catch(e){t(e)}}),l=e=>new Promise(async(a,t)=>{try{let t=await n.xk.get(`${i.Y.guides}/${e.language}/listguide`);a(t.data)}catch(e){t(e)}}),u=e=>new Promise(async(a,t)=>{try{let t=await n.xk.get(`${i.Y.categoryGuides}`,{params:{language:e.language,pageSize:e.pageSize,name:e.name,pageNumber:e.pageNumber,sortOrder:e.sortOrder}});a(t.data)}catch(e){t(e)}}),d=e=>new Promise(async(a,t)=>{try{let t=await n.xk.get(`${i.Y.categoryGuides}/${e}`);a(t.data)}catch(e){t(e)}}),m=({data:e,language:a,id:t})=>new Promise(async(s,c)=>{n.xk.post(`${i.Y.categoryGuides}/${a}/${t}`,e).then(e=>{"en"===a&&r.Am.success("Category english updated successfully"),"fr"===a&&r.Am.success("Category french updated successfully"),e?.data&&s(e.data)}).catch(e=>{e?.response?.data?.status===500||e?.status===500?r.Am.error("Internal Server Error"):r.Am.error(e.response.data.message)})}),g=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:e=>c(e),onError:e=>{e.message=""}})),y=e=>(0,s.useQuery)(["categoryguides",e],async()=>await o(e)),p=e=>(0,s.useQuery)(`guidestitles${e.language}`,async()=>await l(e)),h=e=>(0,s.useQuery)(["categoryguide",e],async()=>await u(e)),w=e=>(0,s.useQuery)(["categoriesGuideData",e],async()=>await d(e)),$=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,a,t)=>m(e,a,t),onError:e=>{e.message=""}}))},74562:(e,a,t)=>{"use strict";t.d(a,{Z:()=>h});var s=t(10326),r=t(88948),i=t(21418),n=t(50295),c=t(87638),o=t(90943),l=t(53913),u=t(52321),d=t(78077),m=t(42265),g=t(39404),y=t(15082),p=t(17577);function h({t:e,itemGuideList:a,setItemGuideList:t}){let h;let[w,$]=(0,p.useState)("panel"),[x,b]=(0,p.useState)({title:"",description:""});return(0,s.jsxs)(r.Z,{id:"accordion",disableGutters:!0,expanded:"panel"===w,onChange:(h="panel",(e,a)=>{$(!!a&&h)}),children:[s.jsx(i.Z,{expandIcon:s.jsx(g.Z,{}),"aria-controls":"panel-content",id:"panel-header",children:e("guides:addItemList")}),(0,s.jsxs)(n.Z,{className:"accordion-detail",elevation:0,children:[s.jsx(c.Z,{children:(0,s.jsxs)(o.Z,{className:"label-form",children:[e("guides:title"),s.jsx(l.Z,{className:"select-pentabell",variant:"standard",sx:{m:1,minWidth:120},children:s.jsx(u.Z,{type:"text",name:"itemTitle",value:a?.itemTitle,onChange:e=>{t({...a,itemTitle:e.target.value})}})})]})}),s.jsx(c.Z,{children:(0,s.jsxs)(o.Z,{className:"label-form",children:["Description",s.jsx(d.Z,{variant:"standard",type:"text",rows:4,multiline:!0,name:"itemDescription",value:a?.itemDescription,onChange:e=>{t({...a,itemDescription:e.target.value})},className:"textArea-pentabell"})]})}),(0,s.jsxs)("div",{className:"inline-group flex-end",children:[s.jsx(c.Z,{children:(0,s.jsxs)(o.Z,{className:"label-form",children:[e("guides:subtitle"),s.jsx(l.Z,{className:"select-pentabell",variant:"standard",sx:{m:1,minWidth:120},children:s.jsx(u.Z,{type:"text",name:"title",value:x.title,onChange:e=>{b({...x,title:e.target.value})}})})]})}),s.jsx(c.Z,{children:(0,s.jsxs)(o.Z,{className:"label-form",children:[e("guides:subtitleDescription"),s.jsx(l.Z,{className:"select-pentabell",variant:"standard",sx:{m:1,minWidth:120},children:s.jsx(d.Z,{variant:"standard",type:"text",rows:6,name:"description",value:x.description,onChange:e=>{b({...x,description:e.target.value})},className:"textArea-pentabell"})})]})}),s.jsx(y.default,{text:e("guides:addSubtitle"),className:"btn btn-filled blue",onClick:()=>{""!==x.title.trim()&&""!==x.description.trim()&&(t(e=>{let a=Array.isArray(e?.subtitles)?[...e.subtitles,x]:[x];return{...e,subtitles:a}}),b({title:"",description:""}))}})]}),s.jsx("div",{children:a?.subtitles?.map((a,r)=>s.jsxs("div",{className:"item-list-guide",children:[s.jsxs("div",{className:"subtitle-item",children:[s.jsxs("p",{children:[e("guides:subtitle")," ",r+1]}),s.jsx(m.Z,{className:"remove-button",onClick:()=>t(e=>({...e,subtitles:e?.subtitles?.filter((e,a)=>a!==r)||[]})),children:e("guides:remove")})]}),s.jsxs("p",{className:"subtitle-title",children:[s.jsxs("strong",{children:[e("guides:title"),":"]})," ",a.title]}),s.jsxs("p",{className:"subtitle-description",children:[s.jsx("strong",{children:"Description:"})," ",s.jsx("br",{}),a.description]})]},r))})]})]},"panel")}},86243:(e,a,t)=>{"use strict";t.d(a,{_r:()=>v,Zm:()=>p,Fx:()=>w,Px:()=>$,cp:()=>x,XJ:()=>h,sv:()=>b,Qv:()=>j});var s=t(2994),r=t(50967),i=t(70580),n=t(31190);let c=e=>(e.t,new Promise(async(a,t)=>{i.xk.post("/guides",e.data).then(e=>{n.Am.success("guide added successfully"),e?.data&&a(e.data)}).catch(e=>{e?.response?.data?.status!==500&&n.Am.error(e.response.data.message),e&&t(e)})})),o=({guideId:e,pageNumber:a,pageSize:t,sortOrder:s,firstName:r,createdAt:n,paginated:c})=>new Promise(async(o,l)=>{try{let l=`/downloads/${e}?pageSize=${encodeURIComponent(t)}&pageNumber=${encodeURIComponent(a)}&sortOrder=${encodeURIComponent(s)}&paginated=${encodeURIComponent(c)}`;r&&(l+=`&firstName=${encodeURIComponent(r)}`),n&&(l+=`&createdAt=${encodeURIComponent(new Date(n).toISOString())}`);let u=await i.xk.get(l);o(u.data)}catch(e){l(e)}}),l=e=>new Promise(async(a,t)=>{try{let t=await i.yX.get(`${r.Y.guides}/${e}`);a(t.data)}catch(e){t(e)}}),u=(e,a)=>new Promise(async(t,s)=>{try{let s=await i.yX.get(`${r.Y.guides}/guide/${a}/${e}`);t(s.data)}catch(e){s(e)}}),d=({data:e,id:a})=>new Promise(async(t,s)=>{i.yX.put(`${r.Y.guides}/${a}`,e).then(e=>{n.Am.success("guide Commun fields updated successfully"),e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&s(e)})}),m=({data:e,language:a,id:t})=>new Promise(async(s,c)=>{i.xk.post(`${r.Y.guides}/update/${a}/${t}`,e).then(e=>{"en"===a&&n.Am.success("guide english updated successfully"),"fr"===a&&n.Am.success("guide french updated successfully"),e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&c(e)})}),g=e=>new Promise(async(a,t)=>{try{let t=await i.xk.get(`${r.Y.guides}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,cible:e.cible,pageNumber:e.pageNumber,sortOrder:e.sortOrder,searchQuery:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,dashboard:!0}});a(t.data)}catch(e){t(e)}}),y=(e,a)=>new Promise(async(t,s)=>{try{let s=await i.xk.delete(`${r.Y.guides}/${e}/${a}`);s?.data&&(n.Am.success(`${"en"===e?"English":"fr"===e?"French":""} article version archived successfully`),t(s.data))}catch(e){n.Am.error("Failed to archive the article"),s(e)}}),p=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:e=>c(e),onError:e=>{e.message=""}})),h=e=>(0,s.useQuery)("guides",async()=>await g(e)),w=(e,a={})=>(0,s.useQuery)("downloadsGuides",async()=>await o(e),{...a}),$=e=>(0,s.useQuery)(["guide",e],async()=>await l(e)),x=(e,a)=>(0,s.useQuery)(["guide",e,a],async()=>await u(e,a)),b=()=>(0,s.useMutation)({mutationFn:(e,a,t)=>m(e,a,t),onError:e=>{e.message=""}}),j=()=>(0,s.useMutation)({mutationFn:(e,a)=>d(e,a),onError:e=>{e.message=""}}),v=()=>(0,s.useMutation)({mutationFn:({language:e,guideId:a})=>y(e,a),onError:e=>{console.error("Error during mutation",e),e.message=""}})}};