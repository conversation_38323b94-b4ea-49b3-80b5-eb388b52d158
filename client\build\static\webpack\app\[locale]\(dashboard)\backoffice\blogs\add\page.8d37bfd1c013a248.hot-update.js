"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,Description,PictureAsPdf!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/PictureAsPdf.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close,Description,PictureAsPdf!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Add custom styles for drag state\nconst dragActiveStyles = `\n  .file-labels.drag-active {\n    border-color: #1976d2 !important;\n    background-color: rgba(25, 118, 210, 0.04) !important;\n  }\n  .file-labels.disabled {\n    cursor: not-allowed !important;\n    opacity: 0.6 !important;\n  }\n`;\n// Inject styles if not already present\nif (typeof document !== \"undefined\" && !document.getElementById(\"document-importer-styles\")) {\n    const styleSheet = document.createElement(\"style\");\n    styleSheet.id = \"document-importer-styles\";\n    styleSheet.textContent = dragActiveStyles;\n    document.head.appendChild(styleSheet);\n}\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewOpen, setPreviewOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [extractedData, setExtractedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Supported file types\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes(\"pdf\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            color: \"error\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 74,\n            columnNumber: 42\n        }, undefined);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            color: \"primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 75,\n            columnNumber: 12\n        }, undefined);\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        // Create a temporary DOM element to parse HTML\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        // Extract potential title (first h1, h2, or strong text)\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        // Extract first paragraph as potential description\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        // Extract keywords from headings and strong text\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords: keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement(function(image) {\n                        return image.read(\"base64\").then(function(imageBuffer) {\n                            return {\n                                src: \"data:\" + image.contentType + \";base64,\" + imageBuffer\n                            };\n                        });\n                    })\n                }\n            });\n            setProgress(75);\n            // Clean up the HTML content\n            let cleanContent = result.value.replace(/<p><\\/p>/g, \"\") // Remove empty paragraphs\n            .replace(/\\s+/g, \" \") // Normalize whitespace\n            .trim();\n            // Extract metadata\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata: metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            // Convert plain text to basic HTML\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata: metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            setExtractedData(result);\n            setSuccess(true);\n            // Show preview dialog\n            setPreviewOpen(true);\n        } catch (err) {\n            console.error(\"File processing error:\", err);\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) {\n            processFile(acceptedFiles[0]);\n        }\n    }, []);\n    const { getRootProps, getInputProps, isDragActive, acceptedFiles } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing\n    });\n    const handleApplyContent = ()=>{\n        if (extractedData) {\n            onContentExtracted(extractedData.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(extractedData.metadata);\n            }\n            setPreviewOpen(false);\n            setExtractedData(null);\n        }\n    };\n    const handleClosePreview = ()=>{\n        setPreviewOpen(false);\n        setExtractedData(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        sx: {\n            mb: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                variant: \"h6\",\n                gutterBottom: true,\n                className: \"label-form\",\n                children: [\n                    t(\"createArticle:importDocument\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"upload-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    ...getRootProps(),\n                    className: `file-labels ${isDragActive ? \"drag-active\" : \"\"} ${disabled || isProcessing ? \"disabled\" : \"\"}`,\n                    style: {\n                        opacity: disabled || isProcessing ? 0.6 : 1,\n                        cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ...getInputProps(),\n                            className: \"file-input\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"upload-area\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"icon-pic\",\n                                        style: {\n                                            backgroundImage: `url(\"${_assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"]}\")`,\n                                            backgroundSize: \"cover\",\n                                            backgroundRepeat: \"no-repeat\",\n                                            backgroundPosition: \"center\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-text\",\n                                            children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:importFromDocument\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-description\",\n                                            children: [\n                                                t(\"createArticle:supportedFormats\"),\n                                                \": .docx, .doc, .txt\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            sx: {\n                                                display: \"flex\",\n                                                justifyContent: \"flex-start\",\n                                                gap: 1,\n                                                flexWrap: \"wrap\",\n                                                mt: 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Word (.docx)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Word (.doc)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Text (.txt)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"e7V5N1Q8+U90zRa31VhbBxq3qFc=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});