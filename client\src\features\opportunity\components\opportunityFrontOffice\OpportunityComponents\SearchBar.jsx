"use client";
import { Grid, TextField, InputAdornment, Autocomplete } from "@mui/material";
import SvgSearchIcon from "@/assets/images/icons/searchIcon.svg";
import SvgMapPin from "@/assets/images/icons/MapPin.svg";
import SvgRefreshIcon from "@/assets/images/icons/refreshIcon.svg";
import CustomButton from "@/components/ui/CustomButton";

const SearchBar = ({
  keyWord,
  country,
  countries,
  handleSearchChange,
  setCountry,
  resetSearch,
  handleSearchClick,
  setPageNumber,
  jobLocation,
  t,
}) => {
  return (
    <Grid className="container" container spacing={0}>
      <Grid item xs={12} sm={9} container spacing={0} className="filter-inputs">
        <Grid item xs={12} sm={jobLocation === true ? 8 : 6}>
          <TextField
            className="input-pentabell"
            autoComplete="off"
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <SvgSearchIcon />
                  </InputAdornment>
                ),
              },
            }}
            variant="standard"
            type="text"
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.preventDefault();
                return false;
              }
            }}
            onChange={(e) => {
              handleSearchChange(e);
            }}
            value={keyWord}
            placeholder={t("Search")}
          />
        </Grid>
        {jobLocation === true ? (
          <></>
        ) : (
          <Grid item xs={12} sm={6}>
            <Autocomplete
              className="input-pentabell maps"
              id="tags-standard"
              options={countries}
              getOptionLabel={(option) => option}
              value={country}
              onChange={(_, newValue) => {
                setCountry(newValue);
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  className="input-pentabell multiple-select"
                  variant="standard"
                  placeholder={"Country"}
                  InputProps={{
                    ...params.InputProps,
                    startAdornment: (
                      <>
                        <InputAdornment position="start">
                          <SvgMapPin />
                        </InputAdornment>
                        {params.InputProps.startAdornment}
                      </>
                    ),
                  }}
                />
              )}
            />
          </Grid>
        )}
      </Grid>
      <Grid
        item
        xs={12}
        sm={3}
        className="btns-filter search-bar-opportunities filter-inputs"
      >
        <CustomButton
          icon={<SvgRefreshIcon />}
          className={"btn btn-outlined btn-refresh"}
          onClick={(e) => resetSearch(e)}
        />
        <CustomButton
          text={"Search"}
          onClick={(e) => {
            handleSearchClick(e);
            setPageNumber(1);
          }}
          className={" btn btn-search btn-filled"}
        />
      </Grid>
    </Grid>
  );
};

export default SearchBar;
