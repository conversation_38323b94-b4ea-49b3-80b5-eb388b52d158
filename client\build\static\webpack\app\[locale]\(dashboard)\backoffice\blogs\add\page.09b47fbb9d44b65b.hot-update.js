"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Description,PictureAsPdf!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/PictureAsPdf.js\");\n/* harmony import */ var _barrel_optimize_names_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Description,PictureAsPdf!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Add custom styles for drag state\nconst dragActiveStyles = `\n  .file-labels.drag-active {\n    border-color: #1976d2 !important;\n    background-color: rgba(25, 118, 210, 0.04) !important;\n  }\n  .file-labels.disabled {\n    cursor: not-allowed !important;\n    opacity: 0.6 !important;\n  }\n`;\n// Inject styles if not already present\nif (typeof document !== \"undefined\" && !document.getElementById(\"document-importer-styles\")) {\n    const styleSheet = document.createElement(\"style\");\n    styleSheet.id = \"document-importer-styles\";\n    styleSheet.textContent = dragActiveStyles;\n    document.head.appendChild(styleSheet);\n}\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewOpen, setPreviewOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [extractedData, setExtractedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Supported file types\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes(\"pdf\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            color: \"error\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 60,\n            columnNumber: 42\n        }, undefined);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            color: \"primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n            lineNumber: 61,\n            columnNumber: 12\n        }, undefined);\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        // Create a temporary DOM element to parse HTML\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        // Extract potential title (first h1, h2, or strong text)\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        // Extract first paragraph as potential description\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        // Extract keywords from headings and strong text\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords: keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement(function(image) {\n                        return image.read(\"base64\").then(function(imageBuffer) {\n                            return {\n                                src: \"data:\" + image.contentType + \";base64,\" + imageBuffer\n                            };\n                        });\n                    })\n                }\n            });\n            setProgress(75);\n            // Clean up the HTML content\n            let cleanContent = result.value.replace(/<p><\\/p>/g, \"\") // Remove empty paragraphs\n            .replace(/\\s+/g, \" \") // Normalize whitespace\n            .trim();\n            // Extract metadata\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata: metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            // Convert plain text to basic HTML\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata: metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            setExtractedData(result);\n            setSuccess(true);\n            // Show preview dialog\n            handleApplyContent();\n        } catch (err) {\n            console.error(\"File processing error:\", err);\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) {\n            processFile(acceptedFiles[0]);\n        }\n    }, []);\n    const { getRootProps, getInputProps, isDragActive, acceptedFiles } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing\n    });\n    const handleApplyContent = ()=>{\n        if (extractedData) {\n            onContentExtracted(extractedData.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(extractedData.metadata);\n            }\n            setPreviewOpen(false);\n            setExtractedData(null);\n        }\n    };\n    const handleClosePreview = ()=>{\n        setPreviewOpen(false);\n        setExtractedData(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        sx: {\n            mb: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                variant: \"h6\",\n                gutterBottom: true,\n                className: \"label-form\",\n                children: [\n                    t(\"createArticle:importDocument\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"upload-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    ...getRootProps(),\n                    className: `file-labels ${isDragActive ? \"drag-active\" : \"\"} ${disabled || isProcessing ? \"disabled\" : \"\"}`,\n                    style: {\n                        opacity: disabled || isProcessing ? 0.6 : 1,\n                        cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ...getInputProps(),\n                            className: \"file-input\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"upload-area\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"icon-pic\",\n                                        style: {\n                                            backgroundImage: `url(\"${_assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"]}\")`,\n                                            backgroundSize: \"cover\",\n                                            backgroundRepeat: \"no-repeat\",\n                                            backgroundPosition: \"center\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-text\",\n                                            children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:importFromDocument\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-description\",\n                                            children: [\n                                                t(\"createArticle:supportedFormats\"),\n                                                \": .docx, .doc, .txt\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            sx: {\n                                                display: \"flex\",\n                                                justifyContent: \"flex-start\",\n                                                gap: 1,\n                                                flexWrap: \"wrap\",\n                                                mt: 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Word (.docx)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Word (.doc)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_PictureAsPdf_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    label: \"Text (.txt)\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        variant: \"body2\",\n                        gutterBottom: true,\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 303,\n                columnNumber: 9\n            }, undefined),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"e7V5N1Q8+U90zRa31VhbBxq3qFc=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});