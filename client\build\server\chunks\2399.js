"use strict";exports.id=2399,exports.ids=[2399],exports.modules={64504:(a,e,t)=>{t.d(e,{QZ:()=>P,qA:()=>v,$F:()=>R,He:()=>U,wv:()=>S,bh:()=>D,b$:()=>N,KK:()=>E,Py:()=>f,hb:()=>I,Yg:()=>M,mg:()=>T,P0:()=>Y,Cb:()=>F,el:()=>C,IX:()=>X});var s=t(2994),r=t(50967),i=t(70580),c=t(31190);let n=a=>(a.t,new Promise(async(e,t)=>{i.yX.post(r.Y.articles,a.data).then(a=>{c.Am.success("Article added successfully"),a?.data&&e(a.data)}).catch(a=>{a&&a.response&&a.response.data,a&&t(a)})})),o=a=>(a.t,new Promise(async(e,t)=>{i.yX.post(`${r.Y.articles}/auto`,a.data).then(a=>{a?.data&&e(a.data)}).catch(a=>{a&&a.response&&a.response.data,a&&t(a)})})),y=({data:a,id:e})=>new Promise(async(t,s)=>{i.yX.put(`${r.Y.articles}/${e}/auto`,a).then(a=>{a?.data&&t(a.data)}).catch(a=>{a&&a.response&&a.response.data,a&&s(a)})}),u=({data:a,id:e})=>new Promise(async(t,s)=>{i.yX.put(`${r.Y.articles}/${e}`,a).then(a=>{c.Am.success("article Commun fields updated successfully"),a?.data&&t(a.data)}).catch(a=>{a&&a.response&&a.response.data,a&&s(a)})}),d=a=>new Promise(async(e,t)=>{try{let t={};t=await i.yX.get(`${r.Y.articles}/${a.language}/blog/${a.urlArticle}`),e(t.data)}catch(a){a&&a.response&&a.response.data&&a.response.status,t(a)}}),l=({data:a,language:e,id:t})=>new Promise(async(s,n)=>{i.yX.post(`${r.Y.articles}/${e}/${t}`,a).then(a=>{"en"===e&&c.Am.success("Article english updated successfully"),"fr"===e&&c.Am.success("Article french updated successfully"),a?.data&&s(a.data)}).catch(a=>{a&&a.response&&a.response.data,a&&n(a)})}),m=(a,e,t)=>new Promise(async(s,n)=>{try{let n=await i.xk.put(`${r.Y.articles}/${a}/${e}/desarchiver`,{archive:t});n?.data&&(c.Am.success(`Article ${t?"archived":"desarchived"} successfully`),s(n.data))}catch(a){c.Am.error(`Failed to ${t?"archive":"desarchive"} the article.`),n(a)}}),p=a=>new Promise(async(e,t)=>{try{let t=await i.yX.get(`${r.Y.categories}/${a}/all`);e(t.data)}catch(a){t(a)}}),g=a=>new Promise(async(e,t)=>{try{let t=await i.yX.get(`${r.Y.articles}`,{params:{paginated:a.paginated,language:a.language,pageSize:a.pageSize,pageNumber:a.pageNumber,sortOrder:a.sortOrder,searchQuery:a.searchQuery,visibility:a.visibility,createdAt:a.createdAt,publishDate:a.publishDate,isThreeLastArticles:a.isThreeLastArticles,isArchived:a.isArchived,categoryName:a.categoryName}});e(t.data)}catch(a){t(a)}}),h=a=>new Promise(async(e,t)=>{try{let t=await i.yX.get(`${r.Y.articles}/dashboard`,{params:{paginated:a.paginated,language:a.language,pageSize:a.pageSize,pageNumber:a.pageNumber,sortOrder:a.sortOrder,searchQuery:a.searchQuery,visibility:a.visibility,createdAt:a.createdAt,publishDate:a.publishDate,isArchived:a.isArchived,categoryName:a.categoryName}});e(t.data)}catch(a){t(a)}}),w=a=>new Promise(async(e,t)=>{try{let t=await i.yX.get(`${r.Y.articles}/${a.language}/listarticle`);e(t.data)}catch(a){t(a)}}),$=({articleId:a,pageNumber:e,pageSize:t,sortOrder:s,name:c,approved:n,createdAt:o,paginated:y})=>new Promise(async(u,d)=>{try{let d=`${r.v}/comments/${a}?pageSize=${encodeURIComponent(t)}&pageNumber=${encodeURIComponent(e)}&sortOrder=${encodeURIComponent(s)}&paginated=${encodeURIComponent(y)}`;c&&(d+=`&name=${encodeURIComponent(c)}`),n&&(d+=`&approved=${encodeURIComponent(n)}`),o&&(d+=`&createdAt=${encodeURIComponent(new Date(o).toISOString())}`);let l=await i.yX.get(d);u(l.data)}catch(a){d(a)}}),b=(a,e)=>new Promise(async(t,s)=>{try{let s=await i.yX.get(`${r.Y.articles}/${e}/${a}`);t(s.data)}catch(a){s(a)}}),Q=a=>new Promise(async(e,t)=>{try{let t=await i.xk.get(`${r.Y.comments}/detail/${a}`);e(t.data)}catch(a){t(a)}}),A=(a,e)=>new Promise(async(e,t)=>{try{let t=await i.yX.get(`${r.Y.articles}/${a}`);e(t.data)}catch(a){t(a)}});t(97980);let P=()=>(0,s.useMutation)({mutationFn:a=>n(a),onError:a=>{a.message=""}}),v=()=>(0,s.useMutation)({mutationFn:a=>o(a),onError:a=>{a.message=""}}),C=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(a,e)=>y(a,e),onError:a=>{a.message=""}})),Y=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(a,e,t)=>l(a,e,t),onError:a=>{a.message=""}})),X=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:({language:a,id:e,archive:t})=>m(a,e,t),onError:a=>{console.error("Error during mutation",a),a.message=""}})),F=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(a,e)=>u(a,e),onError:a=>{a.message=""}})),f=a=>(0,s.useQuery)(["category",a],async()=>await p(a)),T=a=>(0,s.useQuery)(["service",a],async()=>await p(a)),D=a=>(0,s.useQuery)("article",async()=>await g(a)),N=a=>(0,s.useQuery)(`articles${a.language}`,async()=>await h(a)),E=a=>(0,s.useQuery)(`articlestitles${a.language}`,async()=>await w(a)),I=(a,e={})=>(0,s.useQuery)("comment",async()=>await $(a),{...e}),S=(a,e={})=>(0,s.useQuery)(["article",a],async()=>{try{return await d(a)}catch(e){throw e.response&&404===e.response.status&&("en"===a.language?window.location.href="/blog/":window.location.href="/fr/blog/"),e}},{onError:a=>{console.error("Error fetching article:",a.message)},...e}),R=(a,e)=>(0,s.useQuery)(["article",a,e],async()=>await b(a,e)),U=a=>(0,s.useQuery)(["articleall",a],async()=>await A(a)),M=a=>(0,s.useQuery)(["comment",a],async()=>await Q(a))},54528:(a,e,t)=>{t.d(e,{ZT:()=>h,NR:()=>w,f3:()=>b,rB:()=>$,$3:()=>g,gu:()=>m,V3:()=>p});var s=t(2994),r=t(50967),i=t(70580);let c=()=>new Promise(async(a,e)=>{try{let e=await i.yX.get(`${r.Y.statistics}`);a(e.data)}catch(a){e(a)}}),n=a=>new Promise(async(e,t)=>{try{let t=await i.yX.get(`${r.Y.statistics}/useractivity`,{params:{dateFrom:a.dateFrom,dateTo:a.dateTo}});e(t.data)}catch(a){t(a)}}),o=a=>new Promise(async(e,t)=>{try{let t=await i.yX.get(`${r.Y.statistics}/platformactivity`,{params:{dateFrom:a.dateFrom,dateTo:a.dateTo}});e(t.data)}catch(a){t(a)}}),y=a=>new Promise(async(e,t)=>{try{let t=await i.yX.get(`${r.Y.statistics}/applications`,{params:{startDate:a.dateFrom,endDate:a.dateTo,barChart:a.barChart}});e(t.data)}catch(a){t(a)}}),u=a=>new Promise(async(e,t)=>{try{let t=await i.yX.get(`${r.Y.statistics}/articles`,{params:{startDate:a.dateFrom,endDate:a.dateTo,barChart:a.barChart}});e(t.data)}catch(a){t(a)}}),d=a=>new Promise(async(e,t)=>{try{let t=await i.yX.get(`${r.Y.statistics}/opportunities`,{params:{startDate:a.dateFrom,endDate:a.dateTo,opportunityType:a.opportunityType,barChart:a.barChart,industry:a.industry}});e(t.data)}catch(a){t(a)}}),l=a=>new Promise(async(e,t)=>{try{let t=await i.yX.get(`${r.Y.statistics}/comments`,{params:{dateFrom:a.dateFrom,dateTo:a.dateTo,approve:a.approve,categories:a.categories}});e(t.data)}catch(a){t(a)}}),m=()=>(0,s.useQuery)("stats",async()=>await c()),p=a=>(0,s.useQuery)("userStat",async()=>await n(a)),g=a=>(0,s.useQuery)("platformStat",async()=>await o(a)),h=a=>(0,s.useQuery)(`statsApplications${a.barChart}`,async()=>await y(a)),w=a=>(0,s.useQuery)(`statsarticles${a.barChart}`,async()=>await u(a)),$=a=>(0,s.useQuery)(`statsopportunities${a.barChart}`,async()=>await d(a)),b=a=>(0,s.useQuery)("statscomments",async()=>await l(a))}};