"use client";

import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import { useTranslation } from "react-i18next";
import i18nConfig from "../../i18nConfig";
import { API_URLS } from "@/utils/urls";
import DropdownMenu from "./ui/DropdownMenu";
import { useTheme, useMediaQuery } from "@mui/material";

import DropdownMenuMobile from "./ui/DropdownMenuMobile";
import { axiosGetJson } from "@/config/axios";
import frFlag from "@/assets/images/flag/fr.png";
import enFlag from "@/assets/images/flag/en.png";

export default function LanguageChanger({ withFlag, onlyWebVersion }) {
  const theme = useTheme();
  const isTablet = useMediaQuery(theme.breakpoints.down("md"));

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const { i18n } = useTranslation();
  const currentLocale = i18n.language;
  const router = useRouter();
  const currentPathname = usePathname();
  const pathSegments = currentPathname.split("/");

  const handleChange = async (newLocale) => {
    const days = 30;
    document.cookie = `NEXT_LOCALE=${newLocale};expires=${new Date(
      Date.now() + days * 24 * 60 * 60 * 1000
    ).toUTCString()};path=/`;

    const translateAndNavigate = async (apiUrl, slug, routeBase) => {
      try {
        const response = await axiosGetJson.get(
          `${apiUrl}/opposite/${currentLocale}/${slug}`
        );
        const translatedSlug = response.data.slug;
        const translatedLink = translatedSlug
          ? `${
              newLocale === "en" ? "" : `/${newLocale}`
            }/${routeBase}/${translatedSlug}`
          : newLocale === "en"
          ? ""
          : `/events`;

        router.push(translatedLink);
      } catch (error) {}
    };

    const translateAndNavigateCategory = async (apiUrl, slug, routeBase) => {
      try {
        const response = await axiosGetJson.get(
          `${apiUrl}/${currentLocale}/${routeBase}/${slug}`
        );
        const translatedSlug = response.data.slug;

        const translatedLink = translatedSlug
          ? `${
              newLocale === "en" ? "" : `/${newLocale}`
            }/blog/category/${translatedSlug}`
          : currentPathname;

        router.push(translatedLink);
      } catch (error) {}
    };

    const isPathMatch = (path, length) =>
      (pathSegments[1] === path && pathSegments.length === length) ||
      (pathSegments[2] === path && pathSegments.length === length + 1);
    const isPathMatchCategory = (path, length) =>
      (pathSegments[2] === path && pathSegments.length === length) ||
      (pathSegments[3] === path && pathSegments.length === length + 1);
    if (currentLocale !== newLocale) {
      if (isPathMatch("opportunities", 4)) {
        await translateAndNavigate(
          API_URLS.opportunity,
          pathSegments[pathSegments.length - 2],
          "opportunities"
        );
      } else if (isPathMatch("apply", 4)) {
        await translateAndNavigate(
          API_URLS.opportunity,
          pathSegments[pathSegments.length - 2],
          "apply"
        );
      } else if (isPathMatch("blog", 4)) {
        await translateAndNavigate(
          API_URLS.articles,
          pathSegments[pathSegments.length - 2],
          "blog"
        );
      }   else if (isPathMatch("guides", 4)) {
        await translateAndNavigate(
          API_URLS.guides,
          pathSegments[pathSegments.length - 2],
          "guides"
        )}
      // must be returned when event feature becomes static

      // else if (isPathMatch("events", 4)) {
      //   await translateAndNavigate(
      //     API_URLS.events,
      //     pathSegments[pathSegments.length - 2],
      //     "events"
      //   );
      // }
  
       else if (isPathMatchCategory("category", 5)) {
        await translateAndNavigateCategory(
          API_URLS.category,
          pathSegments[pathSegments.length - 2],
          "blog"
        );
      } else if (
        currentLocale === i18nConfig.defaultLocale &&
        !i18nConfig.prefixDefault
      ) {
        router.push(`/${newLocale}${currentPathname}`);
      } else {
        router.push(
          currentPathname.replace(`/${currentLocale}`, `/${newLocale}`)
        );
      }
    }

    router.refresh();
  };

  const menuItems = [
    { name: "En", onClick: () => handleChange("en"), flag: enFlag, route: "" },
    { name: "Fr", onClick: () => handleChange("fr"), flag: frFlag, route: "" },
  ];

  return (
    <>
      {onlyWebVersion ? (
        <DropdownMenu
          buttonLabel={currentLocale === "en" ? "En" : "Fr"}
          selectedFlag={currentLocale === "en" ? enFlag : frFlag}
          buttonHref=""
          menuItems={menuItems}
          withFlag={withFlag}
        />
      ) : isMobile || isTablet ? (
        <DropdownMenuMobile
          buttonLabel={currentLocale === "en" ? "En" : "Fr"}
          selectedFlag={currentLocale === "en" ? enFlag : frFlag}
          buttonHref=""
          locale={currentLocale}
          menuItems={menuItems}
          withFlag={withFlag}
        />
      ) : (
        <DropdownMenu
          buttonLabel={currentLocale === "en" ? "En" : "Fr"}
          selectedFlag={currentLocale === "en" ? frFlag : enFlag}
          buttonHref=""
          menuItems={menuItems}
          withFlag={withFlag}
        />
      )}
    </>
  );
}
