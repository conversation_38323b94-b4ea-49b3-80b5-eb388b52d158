"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/AddArticleFroala.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _AddArticleEN__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AddArticleEN */ \"(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\");\n/* harmony import */ var _AddArticleFR__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AddArticleFR */ \"(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx\");\n/* harmony import */ var _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_15__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AddArticle = ()=>{\n    _s();\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const savedArticle = localStorage.getItem(\"savedArticle\");\n    const useUpdateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave)();\n    const useCreateArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle)();\n    const useCreateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave)();\n    const useSaveFileHook = (0,_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentYear = new Date().getFullYear();\n    const formikRefAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(`panel`);\n    const [formdataEN, setFormDataEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [formdataFR, setFormDataFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedLanguages, setSelectedLanguages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        en: true,\n        fr: false\n    });\n    const [categoriesEN, setCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesFR, setCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesEN, setFilteredCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesFR, setFilteredCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesEN, setSelectedCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesFR, setSelectedCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const getCategoriesEN = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"en\");\n    const getCategoriesFR = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"fr\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesEN.data?.categories) {\n            const transformedCategories = getCategoriesEN.data.categories.map((category)=>({\n                    id: category.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesEN(transformedCategories);\n        }\n    }, [\n        getCategoriesEN.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesFR.data?.categories) {\n            const transformedCategories = getCategoriesFR.data.categories.map((category)=>({\n                    id: category?.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesFR(transformedCategories);\n        }\n    }, [\n        getCategoriesFR.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedCategoriesEN.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesEN, \"en\");\n        } else if (selectedCategoriesFR.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesFR, \"fr\");\n        }\n    }, [\n        selectedCategoriesEN,\n        selectedCategoriesFR\n    ]);\n    const fetchTranslatedCategories = async (selectedCategories, language)=>{\n        try {\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_12__.axiosGetJson.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.categories}/${language}/${selectedCategories}`);\n            if (language === \"en\") {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesFR(transformedCategories);\n            } else {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesEN(transformedCategories);\n            }\n        } catch (error) {\n            console.error(\"Error fetching translated categories:\", error);\n        }\n    };\n    const handleImageSelect = async (selectedFile, language)=>{\n        if (language === \"en\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataEN(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n        } else if (language === \"fr\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataFR(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n        }\n    };\n    // Helper function to safely parse localStorage items\n    const getLocalStorageItem = (key)=>{\n        try {\n            const item = localStorage.getItem(key);\n            return item ? JSON.parse(item) : \"\";\n        } catch (error) {\n            console.warn(`Error parsing localStorage item \"${key}\":`, error);\n            return \"\";\n        }\n    };\n    const titleEN = getLocalStorageItem(\"title\");\n    const metaTitleEN = getLocalStorageItem(\"metatitle\");\n    const metaDescriptionEN = getLocalStorageItem(\"metaDescription\");\n    const contentEN = getLocalStorageItem(\"content\");\n    const contentFR = getLocalStorageItem(\"contentfr\");\n    const titleFR = getLocalStorageItem(\"titlefr\");\n    const metaDescriptionFR = getLocalStorageItem(\"metaDescriptionfr\");\n    const metaTitleFR = getLocalStorageItem(\"metatitlefr\");\n    const initialValues = {\n        robotsMeta: \"index\",\n        metaTitleEN: metaTitleEN,\n        metaDescriptionEN: metaDescriptionEN,\n        descriptionEN: \"\",\n        visibilityEN: \"\",\n        categoryEN: [],\n        imageEN: null,\n        keywordsEN: \"\",\n        titleEN: titleEN,\n        urlEN: \"\",\n        altEN: \"\",\n        contentEN: contentEN,\n        highlightsEN: [],\n        publishDateEN: \"\",\n        faqTitleEN: \"\",\n        faqEN: [],\n        titleFR: titleFR,\n        metaTitleFR: metaTitleFR,\n        metaDescriptionFR: metaDescriptionFR,\n        descriptionFR: \"\",\n        visibilityFR: \"\",\n        categoryFR: [],\n        imageFR: null,\n        keywordsFR: \"\",\n        urlFR: \"\",\n        altFR: \"\",\n        contentFR: contentFR,\n        publishDateFR: \"\",\n        highlightsFR: [],\n        faqTitleFR: \"\",\n        faqFR: []\n    };\n    const validationSchema = yup__WEBPACK_IMPORTED_MODULE_3__.object().shape({\n        ...selectedLanguages.en && {\n            metaTitleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            category: yup__WEBPACK_IMPORTED_MODULE_3__.array().min(1, t(\"validations:minCategory\")),\n            visibilityEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        },\n        ...selectedLanguages.fr && {\n            metaTitleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            visibilityFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        }\n    });\n    const uploadFile = (filename, formData, lang)=>{\n        return new Promise((resolve)=>{\n            useSaveFileHook.mutate({\n                resource: \"blogs\",\n                folder: currentYear,\n                filename,\n                body: {\n                    formData,\n                    t\n                }\n            }, {\n                onSuccess: (data)=>{\n                    if (data.message === \"uuid exist\") {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    } else {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    }\n                },\n                onError: (error)=>{\n                    console.error(`Error uploading ${lang} image:`, error);\n                    resolve({\n                        lang,\n                        uuid: null\n                    });\n                }\n            });\n        });\n    };\n    const clearLocalStorage = ()=>{\n        const keysToRemove = [\n            \"title\",\n            \"content\",\n            \"titlefr\",\n            \"contentfr\",\n            \"metaDescription\",\n            \"metaDescriptionfr\",\n            \"metatitle\",\n            \"metatitlefr\",\n            \"savedArticle\"\n        ];\n        keysToRemove.forEach((key)=>{\n            try {\n                localStorage.removeItem(key);\n            } catch (error) {\n                console.warn(`Error removing localStorage item \"${key}\":`, error);\n            }\n        });\n    };\n    const handleSubmit = async (values)=>{\n        const data = {\n            robotsMeta: values.robotsMeta,\n            versions: []\n        };\n        if (!selectedLanguages.en && !selectedLanguages.fr) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select at least one version!\");\n            return;\n        }\n        try {\n            let resultEN, resultFR;\n            // Validate required files\n            if (selectedLanguages.en && (!uuidPhotoFileNameEN || !formdataEN)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image for the English version!\");\n                return;\n            }\n            if (selectedLanguages.fr && (!uuidPhotoFileNameFR || !formdataFR)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image for the French version!\");\n                return;\n            }\n            if (selectedLanguages.en) {\n                resultEN = await uploadFile(uuidPhotoFileNameEN, formdataEN, \"English\");\n            }\n            if (selectedLanguages.fr) {\n                resultFR = await uploadFile(uuidPhotoFileNameFR, formdataFR, \"French\");\n            }\n            if (selectedLanguages.en && resultEN.uuid) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    description: values.descriptionEN,\n                    url: values.urlEN,\n                    visibility: values.visibilityEN,\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    highlights: values.highlightsEN,\n                    image: resultEN.uuid,\n                    category: values.categoryEN === \"\" ? [] : values.categoryEN,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr && resultFR.uuid) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: values.visibilityFR,\n                    description: values.descriptionFR,\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    highlights: values.highlightsFR,\n                    image: resultFR.uuid,\n                    category: values.categoryFR === \"\" ? [] : values.categoryFR,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (data.versions.length > 0) {\n                if (isSavedArticle !== \"\") {\n                    useUpdateAutoSaveHook.mutate({\n                        data: data,\n                        id: isSavedArticle\n                    }, {\n                        onSuccess: (response)=>{\n                            console.log(\"Article updated successfully:\", response);\n                            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Article updated successfully!\");\n                            clearLocalStorage();\n                        //window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        },\n                        onError: (error)=>{\n                            console.error(\"Error updating article:\", error);\n                            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to update article. Please try again.\");\n                        }\n                    });\n                } else {\n                    useCreateArticleHook.mutate({\n                        data\n                    }, {\n                        onSuccess: (response)=>{\n                            console.log(\"Article created successfully:\", response);\n                            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Article created successfully!\");\n                            clearLocalStorage();\n                        // window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        },\n                        onError: (error)=>{\n                            console.error(\"Error creating article:\", error);\n                            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to create article. Please try again.\");\n                        }\n                    });\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No valid image uploads found. Article not created.\");\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"An error occurred while processing uploads.\");\n        }\n    };\n    const handleChangeAccordion = (panel)=>(_, newExpanded)=>{\n            setExpanded(newExpanded ? panel : false);\n        };\n    const handleCategoriesENSelect = (selectedCategories)=>{\n        setSelectedCategoriesEN(selectedCategories);\n    };\n    const handleCategoriesFRSelect = (selectedCategories)=>{\n        setSelectedCategoriesFR(selectedCategories);\n    };\n    const [isSavedArticle, setIsSavedArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedArticle ? savedArticle : \"\");\n    const isSavedArticleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isSavedArticle);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        isSavedArticleRef.current = isSavedArticle;\n    }, [\n        isSavedArticle\n    ]);\n    const autosave = async ()=>{\n        try {\n            const values = formikRefAll.current?.values;\n            if (!values) return;\n            const data = {\n                robotsMeta: values?.robotsMeta,\n                versions: []\n            };\n            if (selectedLanguages.en) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    url: values.urlEN,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    category: values.categoryEN,\n                    highlights: values.highlightsEN,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    category: values.categoryFR,\n                    highlights: values.highlightsFR,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (isSavedArticleRef.current != \"\") {\n                useUpdateAutoSaveHook.mutate({\n                    data: data,\n                    id: isSavedArticleRef.current\n                });\n            } else {\n                if (data.versions.length > 0) {\n                    useCreateAutoSaveHook.mutate({\n                        data\n                    }, {\n                        onSuccess: (data)=>{\n                            setIsSavedArticle(data.articleId);\n                            localStorage.setItem(\"savedArticle\", data.articleId);\n                        }\n                    });\n                }\n            }\n        } catch (error) {\n            console.warn(\"Auto-save failed:\", error);\n        }\n    };\n    const handleChange = lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default()(autosave, 30000);\n    const handleClear = ()=>{\n        formikRefAll.current?.resetForm();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"createArticle:addArticle\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 482,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"commun\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.en,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            en: !prev.en\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"English\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.fr,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            fr: !prev.fr\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"French\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 486,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"experiences\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"form\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Formik, {\n                                        initialValues: initialValues,\n                                        validationSchema: validationSchema,\n                                        innerRef: formikRefAll,\n                                        onSubmit: handleSubmit,\n                                        className: \"formik-form\",\n                                        children: (param)=>{\n                                            let { errors, touched, setFieldValue, values } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Form, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        id: \"accordion\",\n                                                        disableGutters: true,\n                                                        expanded: expanded === `panel`,\n                                                        onChange: handleChangeAccordion(`panel`),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 533,\n                                                                    columnNumber: 39\n                                                                }, void 0),\n                                                                \"aria-controls\": `panel-content`,\n                                                                id: `panel-header`,\n                                                                children: t(\"createArticle:settings\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"accordion-detail\",\n                                                                elevation: 0,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"label-form\",\n                                                                                    children: [\n                                                                                        \"Robots meta\",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"select-pentabell\",\n                                                                                            variant: \"standard\",\n                                                                                            sx: {\n                                                                                                m: 1,\n                                                                                                minWidth: 120\n                                                                                            },\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                                value: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.filter((option)=>values.robotsMeta === option),\n                                                                                                selected: values.robotsMeta,\n                                                                                                onChange: (event)=>{\n                                                                                                    setFieldValue(\"robotsMeta\", event.target.value);\n                                                                                                },\n                                                                                                children: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                                        value: item,\n                                                                                                        children: item\n                                                                                                    }, item, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                        lineNumber: 566,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                lineNumber: 553,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 548,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                            className: \"label-error\",\n                                                                                            name: \"robotsMeta\",\n                                                                                            component: \"div\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 572,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                    lineNumber: 546,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 545,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                name: \"robotsMeta\",\n                                                                                component: \"div\",\n                                                                                className: \"label-error\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 580,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                        lineNumber: 544,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, `panel`, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    selectedLanguages.en && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleEN__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        debounce: handleChange,\n                                                        categories: categoriesEN,\n                                                        filteredCategories: filteredCategoriesEN,\n                                                        onCategoriesSelect: handleCategoriesENSelect\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    selectedLanguages.fr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleFR__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        categories: categoriesFR,\n                                                        filteredCategories: filteredCategoriesFR,\n                                                        onCategoriesSelect: handleCategoriesFRSelect,\n                                                        debounce: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"btn-container\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"button\",\n                                                                text: \"Clear\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: handleClear\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"submit\",\n                                                                text: \"Save\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: ()=>{}\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 514,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                        lineNumber: 485,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                    lineNumber: 484,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 483,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AddArticle, \"B+cNPaZHMPFNJqb0o7Rak/frCrI=\", false, function() {\n    return [\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave,\n        _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories\n    ];\n});\n_c = AddArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticle);\nvar _c;\n$RefreshReg$(_c, \"AddArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx\n"));

/***/ })

});