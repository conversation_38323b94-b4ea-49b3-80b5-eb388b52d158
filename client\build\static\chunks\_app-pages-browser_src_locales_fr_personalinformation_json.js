"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_locales_fr_personalinformation_json"],{

/***/ "(app-pages-browser)/./src/locales/fr/personalinformation.json":
/*!*************************************************!*\
  !*** ./src/locales/fr/personalinformation.json ***!
  \*************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"ProfilePicture":"Photo de profil","title":"Informations Personnelles","professionalInformation":"Informations Professionnelles","upload-text":" Ajouter votre photo de profil","update-text":" Télécharger une photo","upload-description":" Parcourez les photos ou déposez-les ici","text-label":"Votre CV/Resume*","upload-text-cv":"Ajouter votre CV/Resume","upload-description-cv":"  Parcourez votre cv ou déposez-les ici. Pdf ","firstname":"Prénom*","fileRequirements":"JPG, PNG de moins de 15 Mo","profilePicture":"Photo de profil","lastname":"Nom*","jobtitle":"Titre de poste","dateofbirth":"Date de naissance","gender":"Genre","nationalities":"Nationalités *","nationaliteplaceholder":"selectionnez votre Nationalités","genderplaceholder":"selectionnez votre genre","currentadress":"Adress actuelle","emailAdress":"Adresse Email","phoneNumber":"numéro de télephone*","confirmRemoveProfilePicture":"Voulez-vous vraiment supprimer votre photo de profil ?"}');

/***/ })

}]);