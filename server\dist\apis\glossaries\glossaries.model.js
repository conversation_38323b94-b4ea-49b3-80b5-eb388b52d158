"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("@/utils/helpers/constants");
const versionGlossaryFieldsSchema = {
    word: { type: String },
    url: { type: String },
    letter: { type: String },
    content: { type: String },
    metaTitle: { type: String },
    metaDescription: { type: String },
    language: {
        type: String,
        enum: constants_1.Language,
    },
    visibility: {
        type: String,
        enum: Object.values(constants_1.Visibility),
        default: constants_1.Visibility.Draft,
    },
    isArchived: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
    createdBy: { type: mongoose_1.Types.ObjectId, ref: 'User' },
    updatedBy: { type: mongoose_1.Types.ObjectId, ref: 'User' },
};
// These schemas are no longer needed with the versions map approach
// const englishVersionSchema = new Schema<EnglishVersion>(versionGlossaryFieldsSchema);
// const frenchVersionSchema = new Schema<FrenchVersion>(versionGlossaryFieldsSchema);
const glossarySchema = new mongoose_1.Schema({
    versions: { type: Map, of: versionGlossaryFieldsSchema, required: true },
    robotsMeta: {
        type: String,
        enum: Object.values(constants_1.robotsMeta),
        default: constants_1.robotsMeta.index,
    },
}, {
    timestamps: true,
    toJSON: {
        transform: function (_doc, ret) {
            delete ret.__v;
        },
    },
});
exports.default = (0, mongoose_1.model)('Glossary', glossarySchema);
//# sourceMappingURL=glossaries.model.js.map