"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Description!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_icons_uploadIcon_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/icons/uploadIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/uploadIcon.svg\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Inject drag state styles\nconst dragActiveStyles = `\n  .file-labels.drag-active {\n    border-color: #1976d2 !important;\n    background-color: rgba(25, 118, 210, 0.04) !important;\n  }\n  .file-labels.disabled {\n    cursor: not-allowed !important;\n    opacity: 0.6 !important;\n  }\n`;\nif (typeof document !== \"undefined\" && !document.getElementById(\"document-importer-styles\")) {\n    const styleSheet = document.createElement(\"style\");\n    styleSheet.id = \"document-importer-styles\";\n    styleSheet.textContent = dragActiveStyles;\n    document.head.appendChild(styleSheet);\n}\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewOpen, setPreviewOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [extractedData, setExtractedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement((image)=>image.read(\"base64\").then((imageBuffer)=>({\n                                src: `data:${image.contentType};base64,${imageBuffer}`\n                            })))\n                }\n            });\n            setProgress(75);\n            const cleanContent = result.value.replace(/<p><\\/p>/g, \"\").replace(/\\s+/g, \" \").trim();\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            setExtractedData(result);\n            setSuccess(true);\n            setPreviewOpen(true);\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) processFile(acceptedFiles[0]);\n    }, []);\n    const { getRootProps, getInputProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing\n    });\n    const handleApplyContent = ()=>{\n        if (extractedData) {\n            onContentExtracted(extractedData.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(extractedData.metadata);\n            }\n            setPreviewOpen(false);\n            setExtractedData(null);\n        }\n    };\n    const handleClosePreview = ()=>{\n        setPreviewOpen(false);\n        setExtractedData(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        sx: {\n            mb: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: [\n                    t(\"createArticle:importDocument\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                sx: {\n                    p: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    ...getRootProps(),\n                    className: `file-labels ${isDragActive ? \"drag-active\" : \"\"} ${disabled || isProcessing ? \"disabled\" : \"\"}`,\n                    style: {\n                        display: \"block\",\n                        border: \"2px dashed #ccc\",\n                        padding: \"20px\",\n                        textAlign: \"center\",\n                        cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\",\n                        opacity: disabled || isProcessing ? 0.6 : 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ...getInputProps()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundImage: `url(\"${_assets_images_icons_uploadIcon_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"]}\")`,\n                                        backgroundSize: \"contain\",\n                                        backgroundRepeat: \"no-repeat\",\n                                        backgroundPosition: \"center\",\n                                        width: 60,\n                                        height: 60,\n                                        margin: \"auto\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"body1\",\n                                    mt: 2,\n                                    children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:importFromDocument\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"caption\",\n                                    children: [\n                                        t(\"createArticle:supportedFormats\"),\n                                        \": .docx, .doc, .txt\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        mt: 1,\n                                        display: \"flex\",\n                                        justifyContent: \"center\",\n                                        gap: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            label: \".docx\",\n                                            size: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            label: \".doc\",\n                                            size: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            label: \".txt\",\n                                            size: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"body2\",\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 261,\n                columnNumber: 9\n            }, undefined),\n            success && !previewOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 267,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dialog, {\n                open: previewOpen,\n                onClose: handleClosePreview,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogTitle, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"h6\",\n                                    children: t(\"createArticle:previewExtractedContent\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconButton, {\n                                    onClick: handleClosePreview,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CloseIcon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogContent, {\n                        children: extractedData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            children: [\n                                extractedData.metadata.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    mb: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedTitle\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            children: extractedData.metadata.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 17\n                                }, undefined),\n                                extractedData.metadata.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    mb: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedDescription\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"body2\",\n                                            children: extractedData.metadata.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"subtitle2\",\n                                    color: \"primary\",\n                                    sx: {\n                                        mb: 1\n                                    },\n                                    children: [\n                                        t(\"createArticle:extractedContent\"),\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        maxHeight: 400,\n                                        overflow: \"auto\",\n                                        border: \"1px solid\",\n                                        borderColor: \"grey.300\",\n                                        p: 2,\n                                        borderRadius: 1,\n                                        backgroundColor: \"grey.50\"\n                                    },\n                                    dangerouslySetInnerHTML: {\n                                        __html: extractedData.content\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, undefined),\n                                extractedData.warnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    severity: \"warning\",\n                                    sx: {\n                                        mt: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            children: [\n                                                t(\"createArticle:conversionWarnings\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: extractedData.warnings.map((w, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: w.message\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogActions, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                onClick: handleClosePreview,\n                                children: t(\"global:cancel\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                onClick: handleApplyContent,\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SuccessIcon, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 24\n                                }, void 0),\n                                children: t(\"createArticle:applyContent\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"7cFeiPgqFhGSzHeGTiXZN3jgdSs=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});