/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@xmldom";
exports.ids = ["vendor-chunks/@xmldom"];
exports.modules = {

/***/ "(ssr)/./node_modules/@xmldom/xmldom/lib/conventions.js":
/*!********************************************************!*\
  !*** ./node_modules/@xmldom/xmldom/lib/conventions.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\n/**\n * Ponyfill for `Array.prototype.find` which is only available in ES6 runtimes.\n *\n * Works with anything that has a `length` property and index access properties, including NodeList.\n *\n * @template {unknown} T\n * @param {Array<T> | ({length:number, [number]: T})} list\n * @param {function (item: T, index: number, list:Array<T> | ({length:number, [number]: T})):boolean} predicate\n * @param {Partial<Pick<ArrayConstructor['prototype'], 'find'>>?} ac `Array.prototype` by default,\n * \t\t\t\tallows injecting a custom implementation in tests\n * @returns {T | undefined}\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/find\n * @see https://tc39.es/ecma262/multipage/indexed-collections.html#sec-array.prototype.find\n */\nfunction find(list, predicate, ac) {\n\tif (ac === undefined) {\n\t\tac = Array.prototype;\n\t}\n\tif (list && typeof ac.find === 'function') {\n\t\treturn ac.find.call(list, predicate);\n\t}\n\tfor (var i = 0; i < list.length; i++) {\n\t\tif (Object.prototype.hasOwnProperty.call(list, i)) {\n\t\t\tvar item = list[i];\n\t\t\tif (predicate.call(undefined, item, i, list)) {\n\t\t\t\treturn item;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * \"Shallow freezes\" an object to render it immutable.\n * Uses `Object.freeze` if available,\n * otherwise the immutability is only in the type.\n *\n * Is used to create \"enum like\" objects.\n *\n * @template T\n * @param {T} object the object to freeze\n * @param {Pick<ObjectConstructor, 'freeze'> = Object} oc `Object` by default,\n * \t\t\t\tallows to inject custom object constructor for tests\n * @returns {Readonly<T>}\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/freeze\n */\nfunction freeze(object, oc) {\n\tif (oc === undefined) {\n\t\toc = Object\n\t}\n\treturn oc && typeof oc.freeze === 'function' ? oc.freeze(object) : object\n}\n\n/**\n * Since we can not rely on `Object.assign` we provide a simplified version\n * that is sufficient for our needs.\n *\n * @param {Object} target\n * @param {Object | null | undefined} source\n *\n * @returns {Object} target\n * @throws TypeError if target is not an object\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n * @see https://tc39.es/ecma262/multipage/fundamental-objects.html#sec-object.assign\n */\nfunction assign(target, source) {\n\tif (target === null || typeof target !== 'object') {\n\t\tthrow new TypeError('target is not an object')\n\t}\n\tfor (var key in source) {\n\t\tif (Object.prototype.hasOwnProperty.call(source, key)) {\n\t\t\ttarget[key] = source[key]\n\t\t}\n\t}\n\treturn target\n}\n\n/**\n * All mime types that are allowed as input to `DOMParser.parseFromString`\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString#Argument02 MDN\n * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#domparsersupportedtype WHATWG HTML Spec\n * @see DOMParser.prototype.parseFromString\n */\nvar MIME_TYPE = freeze({\n\t/**\n\t * `text/html`, the only mime type that triggers treating an XML document as HTML.\n\t *\n\t * @see DOMParser.SupportedType.isHTML\n\t * @see https://www.iana.org/assignments/media-types/text/html IANA MimeType registration\n\t * @see https://en.wikipedia.org/wiki/HTML Wikipedia\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString MDN\n\t * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-domparser-parsefromstring WHATWG HTML Spec\n\t */\n\tHTML: 'text/html',\n\n\t/**\n\t * Helper method to check a mime type if it indicates an HTML document\n\t *\n\t * @param {string} [value]\n\t * @returns {boolean}\n\t *\n\t * @see https://www.iana.org/assignments/media-types/text/html IANA MimeType registration\n\t * @see https://en.wikipedia.org/wiki/HTML Wikipedia\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString MDN\n\t * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-domparser-parsefromstring \t */\n\tisHTML: function (value) {\n\t\treturn value === MIME_TYPE.HTML\n\t},\n\n\t/**\n\t * `application/xml`, the standard mime type for XML documents.\n\t *\n\t * @see https://www.iana.org/assignments/media-types/application/xml IANA MimeType registration\n\t * @see https://tools.ietf.org/html/rfc7303#section-9.1 RFC 7303\n\t * @see https://en.wikipedia.org/wiki/XML_and_MIME Wikipedia\n\t */\n\tXML_APPLICATION: 'application/xml',\n\n\t/**\n\t * `text/html`, an alias for `application/xml`.\n\t *\n\t * @see https://tools.ietf.org/html/rfc7303#section-9.2 RFC 7303\n\t * @see https://www.iana.org/assignments/media-types/text/xml IANA MimeType registration\n\t * @see https://en.wikipedia.org/wiki/XML_and_MIME Wikipedia\n\t */\n\tXML_TEXT: 'text/xml',\n\n\t/**\n\t * `application/xhtml+xml`, indicates an XML document that has the default HTML namespace,\n\t * but is parsed as an XML document.\n\t *\n\t * @see https://www.iana.org/assignments/media-types/application/xhtml+xml IANA MimeType registration\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocument WHATWG DOM Spec\n\t * @see https://en.wikipedia.org/wiki/XHTML Wikipedia\n\t */\n\tXML_XHTML_APPLICATION: 'application/xhtml+xml',\n\n\t/**\n\t * `image/svg+xml`,\n\t *\n\t * @see https://www.iana.org/assignments/media-types/image/svg+xml IANA MimeType registration\n\t * @see https://www.w3.org/TR/SVG11/ W3C SVG 1.1\n\t * @see https://en.wikipedia.org/wiki/Scalable_Vector_Graphics Wikipedia\n\t */\n\tXML_SVG_IMAGE: 'image/svg+xml',\n})\n\n/**\n * Namespaces that are used in this code base.\n *\n * @see http://www.w3.org/TR/REC-xml-names\n */\nvar NAMESPACE = freeze({\n\t/**\n\t * The XHTML namespace.\n\t *\n\t * @see http://www.w3.org/1999/xhtml\n\t */\n\tHTML: 'http://www.w3.org/1999/xhtml',\n\n\t/**\n\t * Checks if `uri` equals `NAMESPACE.HTML`.\n\t *\n\t * @param {string} [uri]\n\t *\n\t * @see NAMESPACE.HTML\n\t */\n\tisHTML: function (uri) {\n\t\treturn uri === NAMESPACE.HTML\n\t},\n\n\t/**\n\t * The SVG namespace.\n\t *\n\t * @see http://www.w3.org/2000/svg\n\t */\n\tSVG: 'http://www.w3.org/2000/svg',\n\n\t/**\n\t * The `xml:` namespace.\n\t *\n\t * @see http://www.w3.org/XML/1998/namespace\n\t */\n\tXML: 'http://www.w3.org/XML/1998/namespace',\n\n\t/**\n\t * The `xmlns:` namespace\n\t *\n\t * @see https://www.w3.org/2000/xmlns/\n\t */\n\tXMLNS: 'http://www.w3.org/2000/xmlns/',\n})\n\nexports.assign = assign;\nexports.find = find;\nexports.freeze = freeze;\nexports.MIME_TYPE = MIME_TYPE;\nexports.NAMESPACE = NAMESPACE;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xmldom/xmldom/lib/conventions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xmldom/xmldom/lib/dom-parser.js":
/*!*******************************************************!*\
  !*** ./node_modules/@xmldom/xmldom/lib/dom-parser.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var conventions = __webpack_require__(/*! ./conventions */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/conventions.js\");\nvar dom = __webpack_require__(/*! ./dom */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/dom.js\")\nvar entities = __webpack_require__(/*! ./entities */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/entities.js\");\nvar sax = __webpack_require__(/*! ./sax */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/sax.js\");\n\nvar DOMImplementation = dom.DOMImplementation;\n\nvar NAMESPACE = conventions.NAMESPACE;\n\nvar ParseError = sax.ParseError;\nvar XMLReader = sax.XMLReader;\n\n/**\n * Normalizes line ending according to https://www.w3.org/TR/xml11/#sec-line-ends:\n *\n * > XML parsed entities are often stored in computer files which,\n * > for editing convenience, are organized into lines.\n * > These lines are typically separated by some combination\n * > of the characters CARRIAGE RETURN (#xD) and LINE FEED (#xA).\n * >\n * > To simplify the tasks of applications, the XML processor must behave\n * > as if it normalized all line breaks in external parsed entities (including the document entity)\n * > on input, before parsing, by translating all of the following to a single #xA character:\n * >\n * > 1. the two-character sequence #xD #xA\n * > 2. the two-character sequence #xD #x85\n * > 3. the single character #x85\n * > 4. the single character #x2028\n * > 5. any #xD character that is not immediately followed by #xA or #x85.\n *\n * @param {string} input\n * @returns {string}\n */\nfunction normalizeLineEndings(input) {\n\treturn input\n\t\t.replace(/\\r[\\n\\u0085]/g, '\\n')\n\t\t.replace(/[\\r\\u0085\\u2028]/g, '\\n')\n}\n\n/**\n * @typedef Locator\n * @property {number} [columnNumber]\n * @property {number} [lineNumber]\n */\n\n/**\n * @typedef DOMParserOptions\n * @property {DOMHandler} [domBuilder]\n * @property {Function} [errorHandler]\n * @property {(string) => string} [normalizeLineEndings] used to replace line endings before parsing\n * \t\t\t\t\t\tdefaults to `normalizeLineEndings`\n * @property {Locator} [locator]\n * @property {Record<string, string>} [xmlns]\n *\n * @see normalizeLineEndings\n */\n\n/**\n * The DOMParser interface provides the ability to parse XML or HTML source code\n * from a string into a DOM `Document`.\n *\n * _xmldom is different from the spec in that it allows an `options` parameter,\n * to override the default behavior._\n *\n * @param {DOMParserOptions} [options]\n * @constructor\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser\n * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-parsing-and-serialization\n */\nfunction DOMParser(options){\n\tthis.options = options ||{locator:{}};\n}\n\nDOMParser.prototype.parseFromString = function(source,mimeType){\n\tvar options = this.options;\n\tvar sax =  new XMLReader();\n\tvar domBuilder = options.domBuilder || new DOMHandler();//contentHandler and LexicalHandler\n\tvar errorHandler = options.errorHandler;\n\tvar locator = options.locator;\n\tvar defaultNSMap = options.xmlns||{};\n\tvar isHTML = /\\/x?html?$/.test(mimeType);//mimeType.toLowerCase().indexOf('html') > -1;\n  \tvar entityMap = isHTML ? entities.HTML_ENTITIES : entities.XML_ENTITIES;\n\tif(locator){\n\t\tdomBuilder.setDocumentLocator(locator)\n\t}\n\n\tsax.errorHandler = buildErrorHandler(errorHandler,domBuilder,locator);\n\tsax.domBuilder = options.domBuilder || domBuilder;\n\tif(isHTML){\n\t\tdefaultNSMap[''] = NAMESPACE.HTML;\n\t}\n\tdefaultNSMap.xml = defaultNSMap.xml || NAMESPACE.XML;\n\tvar normalize = options.normalizeLineEndings || normalizeLineEndings;\n\tif (source && typeof source === 'string') {\n\t\tsax.parse(\n\t\t\tnormalize(source),\n\t\t\tdefaultNSMap,\n\t\t\tentityMap\n\t\t)\n\t} else {\n\t\tsax.errorHandler.error('invalid doc source')\n\t}\n\treturn domBuilder.doc;\n}\nfunction buildErrorHandler(errorImpl,domBuilder,locator){\n\tif(!errorImpl){\n\t\tif(domBuilder instanceof DOMHandler){\n\t\t\treturn domBuilder;\n\t\t}\n\t\terrorImpl = domBuilder ;\n\t}\n\tvar errorHandler = {}\n\tvar isCallback = errorImpl instanceof Function;\n\tlocator = locator||{}\n\tfunction build(key){\n\t\tvar fn = errorImpl[key];\n\t\tif(!fn && isCallback){\n\t\t\tfn = errorImpl.length == 2?function(msg){errorImpl(key,msg)}:errorImpl;\n\t\t}\n\t\terrorHandler[key] = fn && function(msg){\n\t\t\tfn('[xmldom '+key+']\\t'+msg+_locator(locator));\n\t\t}||function(){};\n\t}\n\tbuild('warning');\n\tbuild('error');\n\tbuild('fatalError');\n\treturn errorHandler;\n}\n\n//console.log('#\\n\\n\\n\\n\\n\\n\\n####')\n/**\n * +ContentHandler+ErrorHandler\n * +LexicalHandler+EntityResolver2\n * -DeclHandler-DTDHandler\n *\n * DefaultHandler:EntityResolver, DTDHandler, ContentHandler, ErrorHandler\n * DefaultHandler2:DefaultHandler,LexicalHandler, DeclHandler, EntityResolver2\n * @link http://www.saxproject.org/apidoc/org/xml/sax/helpers/DefaultHandler.html\n */\nfunction DOMHandler() {\n    this.cdata = false;\n}\nfunction position(locator,node){\n\tnode.lineNumber = locator.lineNumber;\n\tnode.columnNumber = locator.columnNumber;\n}\n/**\n * @see org.xml.sax.ContentHandler#startDocument\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ContentHandler.html\n */\nDOMHandler.prototype = {\n\tstartDocument : function() {\n    \tthis.doc = new DOMImplementation().createDocument(null, null, null);\n    \tif (this.locator) {\n        \tthis.doc.documentURI = this.locator.systemId;\n    \t}\n\t},\n\tstartElement:function(namespaceURI, localName, qName, attrs) {\n\t\tvar doc = this.doc;\n\t    var el = doc.createElementNS(namespaceURI, qName||localName);\n\t    var len = attrs.length;\n\t    appendElement(this, el);\n\t    this.currentElement = el;\n\n\t\tthis.locator && position(this.locator,el)\n\t    for (var i = 0 ; i < len; i++) {\n\t        var namespaceURI = attrs.getURI(i);\n\t        var value = attrs.getValue(i);\n\t        var qName = attrs.getQName(i);\n\t\t\tvar attr = doc.createAttributeNS(namespaceURI, qName);\n\t\t\tthis.locator &&position(attrs.getLocator(i),attr);\n\t\t\tattr.value = attr.nodeValue = value;\n\t\t\tel.setAttributeNode(attr)\n\t    }\n\t},\n\tendElement:function(namespaceURI, localName, qName) {\n\t\tvar current = this.currentElement\n\t\tvar tagName = current.tagName;\n\t\tthis.currentElement = current.parentNode;\n\t},\n\tstartPrefixMapping:function(prefix, uri) {\n\t},\n\tendPrefixMapping:function(prefix) {\n\t},\n\tprocessingInstruction:function(target, data) {\n\t    var ins = this.doc.createProcessingInstruction(target, data);\n\t    this.locator && position(this.locator,ins)\n\t    appendElement(this, ins);\n\t},\n\tignorableWhitespace:function(ch, start, length) {\n\t},\n\tcharacters:function(chars, start, length) {\n\t\tchars = _toString.apply(this,arguments)\n\t\t//console.log(chars)\n\t\tif(chars){\n\t\t\tif (this.cdata) {\n\t\t\t\tvar charNode = this.doc.createCDATASection(chars);\n\t\t\t} else {\n\t\t\t\tvar charNode = this.doc.createTextNode(chars);\n\t\t\t}\n\t\t\tif(this.currentElement){\n\t\t\t\tthis.currentElement.appendChild(charNode);\n\t\t\t}else if(/^\\s*$/.test(chars)){\n\t\t\t\tthis.doc.appendChild(charNode);\n\t\t\t\t//process xml\n\t\t\t}\n\t\t\tthis.locator && position(this.locator,charNode)\n\t\t}\n\t},\n\tskippedEntity:function(name) {\n\t},\n\tendDocument:function() {\n\t\tthis.doc.normalize();\n\t},\n\tsetDocumentLocator:function (locator) {\n\t    if(this.locator = locator){// && !('lineNumber' in locator)){\n\t    \tlocator.lineNumber = 0;\n\t    }\n\t},\n\t//LexicalHandler\n\tcomment:function(chars, start, length) {\n\t\tchars = _toString.apply(this,arguments)\n\t    var comm = this.doc.createComment(chars);\n\t    this.locator && position(this.locator,comm)\n\t    appendElement(this, comm);\n\t},\n\n\tstartCDATA:function() {\n\t    //used in characters() methods\n\t    this.cdata = true;\n\t},\n\tendCDATA:function() {\n\t    this.cdata = false;\n\t},\n\n\tstartDTD:function(name, publicId, systemId) {\n\t\tvar impl = this.doc.implementation;\n\t    if (impl && impl.createDocumentType) {\n\t        var dt = impl.createDocumentType(name, publicId, systemId);\n\t        this.locator && position(this.locator,dt)\n\t        appendElement(this, dt);\n\t\t\t\t\tthis.doc.doctype = dt;\n\t    }\n\t},\n\t/**\n\t * @see org.xml.sax.ErrorHandler\n\t * @link http://www.saxproject.org/apidoc/org/xml/sax/ErrorHandler.html\n\t */\n\twarning:function(error) {\n\t\tconsole.warn('[xmldom warning]\\t'+error,_locator(this.locator));\n\t},\n\terror:function(error) {\n\t\tconsole.error('[xmldom error]\\t'+error,_locator(this.locator));\n\t},\n\tfatalError:function(error) {\n\t\tthrow new ParseError(error, this.locator);\n\t}\n}\nfunction _locator(l){\n\tif(l){\n\t\treturn '\\n@'+(l.systemId ||'')+'#[line:'+l.lineNumber+',col:'+l.columnNumber+']'\n\t}\n}\nfunction _toString(chars,start,length){\n\tif(typeof chars == 'string'){\n\t\treturn chars.substr(start,length)\n\t}else{//java sax connect width xmldom on rhino(what about: \"? && !(chars instanceof String)\")\n\t\tif(chars.length >= start+length || start){\n\t\t\treturn new java.lang.String(chars,start,length)+'';\n\t\t}\n\t\treturn chars;\n\t}\n}\n\n/*\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/LexicalHandler.html\n * used method of org.xml.sax.ext.LexicalHandler:\n *  #comment(chars, start, length)\n *  #startCDATA()\n *  #endCDATA()\n *  #startDTD(name, publicId, systemId)\n *\n *\n * IGNORED method of org.xml.sax.ext.LexicalHandler:\n *  #endDTD()\n *  #startEntity(name)\n *  #endEntity(name)\n *\n *\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/DeclHandler.html\n * IGNORED method of org.xml.sax.ext.DeclHandler\n * \t#attributeDecl(eName, aName, type, mode, value)\n *  #elementDecl(name, model)\n *  #externalEntityDecl(name, publicId, systemId)\n *  #internalEntityDecl(name, value)\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/EntityResolver2.html\n * IGNORED method of org.xml.sax.EntityResolver2\n *  #resolveEntity(String name,String publicId,String baseURI,String systemId)\n *  #resolveEntity(publicId, systemId)\n *  #getExternalSubset(name, baseURI)\n * @link http://www.saxproject.org/apidoc/org/xml/sax/DTDHandler.html\n * IGNORED method of org.xml.sax.DTDHandler\n *  #notationDecl(name, publicId, systemId) {};\n *  #unparsedEntityDecl(name, publicId, systemId, notationName) {};\n */\n\"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl\".replace(/\\w+/g,function(key){\n\tDOMHandler.prototype[key] = function(){return null}\n})\n\n/* Private static helpers treated below as private instance methods, so don't need to add these to the public API; we might use a Relator to also get rid of non-standard public properties */\nfunction appendElement (hander,node) {\n    if (!hander.currentElement) {\n        hander.doc.appendChild(node);\n    } else {\n        hander.currentElement.appendChild(node);\n    }\n}//appendChild and setAttributeNS are preformance key\n\nexports.__DOMHandler = DOMHandler;\nexports.normalizeLineEndings = normalizeLineEndings;\nexports.DOMParser = DOMParser;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xmldom/xmldom/lib/dom-parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xmldom/xmldom/lib/dom.js":
/*!************************************************!*\
  !*** ./node_modules/@xmldom/xmldom/lib/dom.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var conventions = __webpack_require__(/*! ./conventions */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/conventions.js\");\n\nvar find = conventions.find;\nvar NAMESPACE = conventions.NAMESPACE;\n\n/**\n * A prerequisite for `[].filter`, to drop elements that are empty\n * @param {string} input\n * @returns {boolean}\n */\nfunction notEmptyString (input) {\n\treturn input !== ''\n}\n/**\n * @see https://infra.spec.whatwg.org/#split-on-ascii-whitespace\n * @see https://infra.spec.whatwg.org/#ascii-whitespace\n *\n * @param {string} input\n * @returns {string[]} (can be empty)\n */\nfunction splitOnASCIIWhitespace(input) {\n\t// U+0009 TAB, U+000A LF, U+000C FF, U+000D CR, U+0020 SPACE\n\treturn input ? input.split(/[\\t\\n\\f\\r ]+/).filter(notEmptyString) : []\n}\n\n/**\n * Adds element as a key to current if it is not already present.\n *\n * @param {Record<string, boolean | undefined>} current\n * @param {string} element\n * @returns {Record<string, boolean | undefined>}\n */\nfunction orderedSetReducer (current, element) {\n\tif (!current.hasOwnProperty(element)) {\n\t\tcurrent[element] = true;\n\t}\n\treturn current;\n}\n\n/**\n * @see https://infra.spec.whatwg.org/#ordered-set\n * @param {string} input\n * @returns {string[]}\n */\nfunction toOrderedSet(input) {\n\tif (!input) return [];\n\tvar list = splitOnASCIIWhitespace(input);\n\treturn Object.keys(list.reduce(orderedSetReducer, {}))\n}\n\n/**\n * Uses `list.indexOf` to implement something like `Array.prototype.includes`,\n * which we can not rely on being available.\n *\n * @param {any[]} list\n * @returns {function(any): boolean}\n */\nfunction arrayIncludes (list) {\n\treturn function(element) {\n\t\treturn list && list.indexOf(element) !== -1;\n\t}\n}\n\nfunction copy(src,dest){\n\tfor(var p in src){\n\t\tif (Object.prototype.hasOwnProperty.call(src, p)) {\n\t\t\tdest[p] = src[p];\n\t\t}\n\t}\n}\n\n/**\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*((?:.*\\{\\s*?[\\r\\n][\\s\\S]*?^})|\\S.*?(?=[;\\r\\n]));?\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*(\\S.*?(?=[;\\r\\n]));?\n */\nfunction _extends(Class,Super){\n\tvar pt = Class.prototype;\n\tif(!(pt instanceof Super)){\n\t\tfunction t(){};\n\t\tt.prototype = Super.prototype;\n\t\tt = new t();\n\t\tcopy(pt,t);\n\t\tClass.prototype = pt = t;\n\t}\n\tif(pt.constructor != Class){\n\t\tif(typeof Class != 'function'){\n\t\t\tconsole.error(\"unknown Class:\"+Class)\n\t\t}\n\t\tpt.constructor = Class\n\t}\n}\n\n// Node Types\nvar NodeType = {}\nvar ELEMENT_NODE                = NodeType.ELEMENT_NODE                = 1;\nvar ATTRIBUTE_NODE              = NodeType.ATTRIBUTE_NODE              = 2;\nvar TEXT_NODE                   = NodeType.TEXT_NODE                   = 3;\nvar CDATA_SECTION_NODE          = NodeType.CDATA_SECTION_NODE          = 4;\nvar ENTITY_REFERENCE_NODE       = NodeType.ENTITY_REFERENCE_NODE       = 5;\nvar ENTITY_NODE                 = NodeType.ENTITY_NODE                 = 6;\nvar PROCESSING_INSTRUCTION_NODE = NodeType.PROCESSING_INSTRUCTION_NODE = 7;\nvar COMMENT_NODE                = NodeType.COMMENT_NODE                = 8;\nvar DOCUMENT_NODE               = NodeType.DOCUMENT_NODE               = 9;\nvar DOCUMENT_TYPE_NODE          = NodeType.DOCUMENT_TYPE_NODE          = 10;\nvar DOCUMENT_FRAGMENT_NODE      = NodeType.DOCUMENT_FRAGMENT_NODE      = 11;\nvar NOTATION_NODE               = NodeType.NOTATION_NODE               = 12;\n\n// ExceptionCode\nvar ExceptionCode = {}\nvar ExceptionMessage = {};\nvar INDEX_SIZE_ERR              = ExceptionCode.INDEX_SIZE_ERR              = ((ExceptionMessage[1]=\"Index size error\"),1);\nvar DOMSTRING_SIZE_ERR          = ExceptionCode.DOMSTRING_SIZE_ERR          = ((ExceptionMessage[2]=\"DOMString size error\"),2);\nvar HIERARCHY_REQUEST_ERR       = ExceptionCode.HIERARCHY_REQUEST_ERR       = ((ExceptionMessage[3]=\"Hierarchy request error\"),3);\nvar WRONG_DOCUMENT_ERR          = ExceptionCode.WRONG_DOCUMENT_ERR          = ((ExceptionMessage[4]=\"Wrong document\"),4);\nvar INVALID_CHARACTER_ERR       = ExceptionCode.INVALID_CHARACTER_ERR       = ((ExceptionMessage[5]=\"Invalid character\"),5);\nvar NO_DATA_ALLOWED_ERR         = ExceptionCode.NO_DATA_ALLOWED_ERR         = ((ExceptionMessage[6]=\"No data allowed\"),6);\nvar NO_MODIFICATION_ALLOWED_ERR = ExceptionCode.NO_MODIFICATION_ALLOWED_ERR = ((ExceptionMessage[7]=\"No modification allowed\"),7);\nvar NOT_FOUND_ERR               = ExceptionCode.NOT_FOUND_ERR               = ((ExceptionMessage[8]=\"Not found\"),8);\nvar NOT_SUPPORTED_ERR           = ExceptionCode.NOT_SUPPORTED_ERR           = ((ExceptionMessage[9]=\"Not supported\"),9);\nvar INUSE_ATTRIBUTE_ERR         = ExceptionCode.INUSE_ATTRIBUTE_ERR         = ((ExceptionMessage[10]=\"Attribute in use\"),10);\n//level2\nvar INVALID_STATE_ERR        \t= ExceptionCode.INVALID_STATE_ERR        \t= ((ExceptionMessage[11]=\"Invalid state\"),11);\nvar SYNTAX_ERR               \t= ExceptionCode.SYNTAX_ERR               \t= ((ExceptionMessage[12]=\"Syntax error\"),12);\nvar INVALID_MODIFICATION_ERR \t= ExceptionCode.INVALID_MODIFICATION_ERR \t= ((ExceptionMessage[13]=\"Invalid modification\"),13);\nvar NAMESPACE_ERR            \t= ExceptionCode.NAMESPACE_ERR           \t= ((ExceptionMessage[14]=\"Invalid namespace\"),14);\nvar INVALID_ACCESS_ERR       \t= ExceptionCode.INVALID_ACCESS_ERR      \t= ((ExceptionMessage[15]=\"Invalid access\"),15);\n\n/**\n * DOM Level 2\n * Object DOMException\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/ecma-script-binding.html\n * @see http://www.w3.org/TR/REC-DOM-Level-1/ecma-script-language-binding.html\n */\nfunction DOMException(code, message) {\n\tif(message instanceof Error){\n\t\tvar error = message;\n\t}else{\n\t\terror = this;\n\t\tError.call(this, ExceptionMessage[code]);\n\t\tthis.message = ExceptionMessage[code];\n\t\tif(Error.captureStackTrace) Error.captureStackTrace(this, DOMException);\n\t}\n\terror.code = code;\n\tif(message) this.message = this.message + \": \" + message;\n\treturn error;\n};\nDOMException.prototype = Error.prototype;\ncopy(ExceptionCode,DOMException)\n\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-536297177\n * The NodeList interface provides the abstraction of an ordered collection of nodes, without defining or constraining how this collection is implemented. NodeList objects in the DOM are live.\n * The items in the NodeList are accessible via an integral index, starting from 0.\n */\nfunction NodeList() {\n};\nNodeList.prototype = {\n\t/**\n\t * The number of nodes in the list. The range of valid child node indices is 0 to length-1 inclusive.\n\t * @standard level1\n\t */\n\tlength:0,\n\t/**\n\t * Returns the indexth item in the collection. If index is greater than or equal to the number of nodes in the list, this returns null.\n\t * @standard level1\n\t * @param index  unsigned long\n\t *   Index into the collection.\n\t * @return Node\n\t * \tThe node at the indexth position in the NodeList, or null if that is not a valid index.\n\t */\n\titem: function(index) {\n\t\treturn index >= 0 && index < this.length ? this[index] : null;\n\t},\n\ttoString:function(isHTML,nodeFilter){\n\t\tfor(var buf = [], i = 0;i<this.length;i++){\n\t\t\tserializeToString(this[i],buf,isHTML,nodeFilter);\n\t\t}\n\t\treturn buf.join('');\n\t},\n\t/**\n\t * @private\n\t * @param {function (Node):boolean} predicate\n\t * @returns {Node[]}\n\t */\n\tfilter: function (predicate) {\n\t\treturn Array.prototype.filter.call(this, predicate);\n\t},\n\t/**\n\t * @private\n\t * @param {Node} item\n\t * @returns {number}\n\t */\n\tindexOf: function (item) {\n\t\treturn Array.prototype.indexOf.call(this, item);\n\t},\n};\n\nfunction LiveNodeList(node,refresh){\n\tthis._node = node;\n\tthis._refresh = refresh\n\t_updateLiveList(this);\n}\nfunction _updateLiveList(list){\n\tvar inc = list._node._inc || list._node.ownerDocument._inc;\n\tif (list._inc !== inc) {\n\t\tvar ls = list._refresh(list._node);\n\t\t__set__(list,'length',ls.length);\n\t\tif (!list.$$length || ls.length < list.$$length) {\n\t\t\tfor (var i = ls.length; i in list; i++) {\n\t\t\t\tif (Object.prototype.hasOwnProperty.call(list, i)) {\n\t\t\t\t\tdelete list[i];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tcopy(ls,list);\n\t\tlist._inc = inc;\n\t}\n}\nLiveNodeList.prototype.item = function(i){\n\t_updateLiveList(this);\n\treturn this[i] || null;\n}\n\n_extends(LiveNodeList,NodeList);\n\n/**\n * Objects implementing the NamedNodeMap interface are used\n * to represent collections of nodes that can be accessed by name.\n * Note that NamedNodeMap does not inherit from NodeList;\n * NamedNodeMaps are not maintained in any particular order.\n * Objects contained in an object implementing NamedNodeMap may also be accessed by an ordinal index,\n * but this is simply to allow convenient enumeration of the contents of a NamedNodeMap,\n * and does not imply that the DOM specifies an order to these Nodes.\n * NamedNodeMap objects in the DOM are live.\n * used for attributes or DocumentType entities\n */\nfunction NamedNodeMap() {\n};\n\nfunction _findNodeIndex(list,node){\n\tvar i = list.length;\n\twhile(i--){\n\t\tif(list[i] === node){return i}\n\t}\n}\n\nfunction _addNamedNode(el,list,newAttr,oldAttr){\n\tif(oldAttr){\n\t\tlist[_findNodeIndex(list,oldAttr)] = newAttr;\n\t}else{\n\t\tlist[list.length++] = newAttr;\n\t}\n\tif(el){\n\t\tnewAttr.ownerElement = el;\n\t\tvar doc = el.ownerDocument;\n\t\tif(doc){\n\t\t\toldAttr && _onRemoveAttribute(doc,el,oldAttr);\n\t\t\t_onAddAttribute(doc,el,newAttr);\n\t\t}\n\t}\n}\nfunction _removeNamedNode(el,list,attr){\n\t//console.log('remove attr:'+attr)\n\tvar i = _findNodeIndex(list,attr);\n\tif(i>=0){\n\t\tvar lastIndex = list.length-1\n\t\twhile(i<lastIndex){\n\t\t\tlist[i] = list[++i]\n\t\t}\n\t\tlist.length = lastIndex;\n\t\tif(el){\n\t\t\tvar doc = el.ownerDocument;\n\t\t\tif(doc){\n\t\t\t\t_onRemoveAttribute(doc,el,attr);\n\t\t\t\tattr.ownerElement = null;\n\t\t\t}\n\t\t}\n\t}else{\n\t\tthrow new DOMException(NOT_FOUND_ERR,new Error(el.tagName+'@'+attr))\n\t}\n}\nNamedNodeMap.prototype = {\n\tlength:0,\n\titem:NodeList.prototype.item,\n\tgetNamedItem: function(key) {\n//\t\tif(key.indexOf(':')>0 || key == 'xmlns'){\n//\t\t\treturn null;\n//\t\t}\n\t\t//console.log()\n\t\tvar i = this.length;\n\t\twhile(i--){\n\t\t\tvar attr = this[i];\n\t\t\t//console.log(attr.nodeName,key)\n\t\t\tif(attr.nodeName == key){\n\t\t\t\treturn attr;\n\t\t\t}\n\t\t}\n\t},\n\tsetNamedItem: function(attr) {\n\t\tvar el = attr.ownerElement;\n\t\tif(el && el!=this._ownerElement){\n\t\t\tthrow new DOMException(INUSE_ATTRIBUTE_ERR);\n\t\t}\n\t\tvar oldAttr = this.getNamedItem(attr.nodeName);\n\t\t_addNamedNode(this._ownerElement,this,attr,oldAttr);\n\t\treturn oldAttr;\n\t},\n\t/* returns Node */\n\tsetNamedItemNS: function(attr) {// raises: WRONG_DOCUMENT_ERR,NO_MODIFICATION_ALLOWED_ERR,INUSE_ATTRIBUTE_ERR\n\t\tvar el = attr.ownerElement, oldAttr;\n\t\tif(el && el!=this._ownerElement){\n\t\t\tthrow new DOMException(INUSE_ATTRIBUTE_ERR);\n\t\t}\n\t\toldAttr = this.getNamedItemNS(attr.namespaceURI,attr.localName);\n\t\t_addNamedNode(this._ownerElement,this,attr,oldAttr);\n\t\treturn oldAttr;\n\t},\n\n\t/* returns Node */\n\tremoveNamedItem: function(key) {\n\t\tvar attr = this.getNamedItem(key);\n\t\t_removeNamedNode(this._ownerElement,this,attr);\n\t\treturn attr;\n\n\n\t},// raises: NOT_FOUND_ERR,NO_MODIFICATION_ALLOWED_ERR\n\n\t//for level2\n\tremoveNamedItemNS:function(namespaceURI,localName){\n\t\tvar attr = this.getNamedItemNS(namespaceURI,localName);\n\t\t_removeNamedNode(this._ownerElement,this,attr);\n\t\treturn attr;\n\t},\n\tgetNamedItemNS: function(namespaceURI, localName) {\n\t\tvar i = this.length;\n\t\twhile(i--){\n\t\t\tvar node = this[i];\n\t\t\tif(node.localName == localName && node.namespaceURI == namespaceURI){\n\t\t\t\treturn node;\n\t\t\t}\n\t\t}\n\t\treturn null;\n\t}\n};\n\n/**\n * The DOMImplementation interface represents an object providing methods\n * which are not dependent on any particular document.\n * Such an object is returned by the `Document.implementation` property.\n *\n * __The individual methods describe the differences compared to the specs.__\n *\n * @constructor\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation MDN\n * @see https://www.w3.org/TR/REC-DOM-Level-1/level-one-core.html#ID-102161490 DOM Level 1 Core (Initial)\n * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#ID-102161490 DOM Level 2 Core\n * @see https://www.w3.org/TR/DOM-Level-3-Core/core.html#ID-102161490 DOM Level 3 Core\n * @see https://dom.spec.whatwg.org/#domimplementation DOM Living Standard\n */\nfunction DOMImplementation() {\n}\n\nDOMImplementation.prototype = {\n\t/**\n\t * The DOMImplementation.hasFeature() method returns a Boolean flag indicating if a given feature is supported.\n\t * The different implementations fairly diverged in what kind of features were reported.\n\t * The latest version of the spec settled to force this method to always return true, where the functionality was accurate and in use.\n\t *\n\t * @deprecated It is deprecated and modern browsers return true in all cases.\n\t *\n\t * @param {string} feature\n\t * @param {string} [version]\n\t * @returns {boolean} always true\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/hasFeature MDN\n\t * @see https://www.w3.org/TR/REC-DOM-Level-1/level-one-core.html#ID-5CED94D7 DOM Level 1 Core\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-hasfeature DOM Living Standard\n\t */\n\thasFeature: function(feature, version) {\n\t\t\treturn true;\n\t},\n\t/**\n\t * Creates an XML Document object of the specified type with its document element.\n\t *\n\t * __It behaves slightly different from the description in the living standard__:\n\t * - There is no interface/class `XMLDocument`, it returns a `Document` instance.\n\t * - `contentType`, `encoding`, `mode`, `origin`, `url` fields are currently not declared.\n\t * - this implementation is not validating names or qualified names\n\t *   (when parsing XML strings, the SAX parser takes care of that)\n\t *\n\t * @param {string|null} namespaceURI\n\t * @param {string} qualifiedName\n\t * @param {DocumentType=null} doctype\n\t * @returns {Document}\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/createDocument MDN\n\t * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#Level-2-Core-DOM-createDocument DOM Level 2 Core (initial)\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocument  DOM Level 2 Core\n\t *\n\t * @see https://dom.spec.whatwg.org/#validate-and-extract DOM: Validate and extract\n\t * @see https://www.w3.org/TR/xml/#NT-NameStartChar XML Spec: Names\n\t * @see https://www.w3.org/TR/xml-names/#ns-qualnames XML Namespaces: Qualified names\n\t */\n\tcreateDocument: function(namespaceURI,  qualifiedName, doctype){\n\t\tvar doc = new Document();\n\t\tdoc.implementation = this;\n\t\tdoc.childNodes = new NodeList();\n\t\tdoc.doctype = doctype || null;\n\t\tif (doctype){\n\t\t\tdoc.appendChild(doctype);\n\t\t}\n\t\tif (qualifiedName){\n\t\t\tvar root = doc.createElementNS(namespaceURI, qualifiedName);\n\t\t\tdoc.appendChild(root);\n\t\t}\n\t\treturn doc;\n\t},\n\t/**\n\t * Returns a doctype, with the given `qualifiedName`, `publicId`, and `systemId`.\n\t *\n\t * __This behavior is slightly different from the in the specs__:\n\t * - this implementation is not validating names or qualified names\n\t *   (when parsing XML strings, the SAX parser takes care of that)\n\t *\n\t * @param {string} qualifiedName\n\t * @param {string} [publicId]\n\t * @param {string} [systemId]\n\t * @returns {DocumentType} which can either be used with `DOMImplementation.createDocument` upon document creation\n\t * \t\t\t\t  or can be put into the document via methods like `Node.insertBefore()` or `Node.replaceChild()`\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/createDocumentType MDN\n\t * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#Level-2-Core-DOM-createDocType DOM Level 2 Core\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocumenttype DOM Living Standard\n\t *\n\t * @see https://dom.spec.whatwg.org/#validate-and-extract DOM: Validate and extract\n\t * @see https://www.w3.org/TR/xml/#NT-NameStartChar XML Spec: Names\n\t * @see https://www.w3.org/TR/xml-names/#ns-qualnames XML Namespaces: Qualified names\n\t */\n\tcreateDocumentType: function(qualifiedName, publicId, systemId){\n\t\tvar node = new DocumentType();\n\t\tnode.name = qualifiedName;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.publicId = publicId || '';\n\t\tnode.systemId = systemId || '';\n\n\t\treturn node;\n\t}\n};\n\n\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-1950641247\n */\n\nfunction Node() {\n};\n\nNode.prototype = {\n\tfirstChild : null,\n\tlastChild : null,\n\tpreviousSibling : null,\n\tnextSibling : null,\n\tattributes : null,\n\tparentNode : null,\n\tchildNodes : null,\n\townerDocument : null,\n\tnodeValue : null,\n\tnamespaceURI : null,\n\tprefix : null,\n\tlocalName : null,\n\t// Modified in DOM Level 2:\n\tinsertBefore:function(newChild, refChild){//raises\n\t\treturn _insertBefore(this,newChild,refChild);\n\t},\n\treplaceChild:function(newChild, oldChild){//raises\n\t\t_insertBefore(this, newChild,oldChild, assertPreReplacementValidityInDocument);\n\t\tif(oldChild){\n\t\t\tthis.removeChild(oldChild);\n\t\t}\n\t},\n\tremoveChild:function(oldChild){\n\t\treturn _removeChild(this,oldChild);\n\t},\n\tappendChild:function(newChild){\n\t\treturn this.insertBefore(newChild,null);\n\t},\n\thasChildNodes:function(){\n\t\treturn this.firstChild != null;\n\t},\n\tcloneNode:function(deep){\n\t\treturn cloneNode(this.ownerDocument||this,this,deep);\n\t},\n\t// Modified in DOM Level 2:\n\tnormalize:function(){\n\t\tvar child = this.firstChild;\n\t\twhile(child){\n\t\t\tvar next = child.nextSibling;\n\t\t\tif(next && next.nodeType == TEXT_NODE && child.nodeType == TEXT_NODE){\n\t\t\t\tthis.removeChild(next);\n\t\t\t\tchild.appendData(next.data);\n\t\t\t}else{\n\t\t\t\tchild.normalize();\n\t\t\t\tchild = next;\n\t\t\t}\n\t\t}\n\t},\n  \t// Introduced in DOM Level 2:\n\tisSupported:function(feature, version){\n\t\treturn this.ownerDocument.implementation.hasFeature(feature,version);\n\t},\n    // Introduced in DOM Level 2:\n    hasAttributes:function(){\n    \treturn this.attributes.length>0;\n    },\n\t/**\n\t * Look up the prefix associated to the given namespace URI, starting from this node.\n\t * **The default namespace declarations are ignored by this method.**\n\t * See Namespace Prefix Lookup for details on the algorithm used by this method.\n\t *\n\t * _Note: The implementation seems to be incomplete when compared to the algorithm described in the specs._\n\t *\n\t * @param {string | null} namespaceURI\n\t * @returns {string | null}\n\t * @see https://www.w3.org/TR/DOM-Level-3-Core/core.html#Node3-lookupNamespacePrefix\n\t * @see https://www.w3.org/TR/DOM-Level-3-Core/namespaces-algorithms.html#lookupNamespacePrefixAlgo\n\t * @see https://dom.spec.whatwg.org/#dom-node-lookupprefix\n\t * @see https://github.com/xmldom/xmldom/issues/322\n\t */\n    lookupPrefix:function(namespaceURI){\n    \tvar el = this;\n    \twhile(el){\n    \t\tvar map = el._nsMap;\n    \t\t//console.dir(map)\n    \t\tif(map){\n    \t\t\tfor(var n in map){\n\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(map, n) && map[n] === namespaceURI) {\n\t\t\t\t\t\t\treturn n;\n\t\t\t\t\t\t}\n    \t\t\t}\n    \t\t}\n    \t\tel = el.nodeType == ATTRIBUTE_NODE?el.ownerDocument : el.parentNode;\n    \t}\n    \treturn null;\n    },\n    // Introduced in DOM Level 3:\n    lookupNamespaceURI:function(prefix){\n    \tvar el = this;\n    \twhile(el){\n    \t\tvar map = el._nsMap;\n    \t\t//console.dir(map)\n    \t\tif(map){\n    \t\t\tif(Object.prototype.hasOwnProperty.call(map, prefix)){\n    \t\t\t\treturn map[prefix] ;\n    \t\t\t}\n    \t\t}\n    \t\tel = el.nodeType == ATTRIBUTE_NODE?el.ownerDocument : el.parentNode;\n    \t}\n    \treturn null;\n    },\n    // Introduced in DOM Level 3:\n    isDefaultNamespace:function(namespaceURI){\n    \tvar prefix = this.lookupPrefix(namespaceURI);\n    \treturn prefix == null;\n    }\n};\n\n\nfunction _xmlEncoder(c){\n\treturn c == '<' && '&lt;' ||\n         c == '>' && '&gt;' ||\n         c == '&' && '&amp;' ||\n         c == '\"' && '&quot;' ||\n         '&#'+c.charCodeAt()+';'\n}\n\n\ncopy(NodeType,Node);\ncopy(NodeType,Node.prototype);\n\n/**\n * @param callback return true for continue,false for break\n * @return boolean true: break visit;\n */\nfunction _visitNode(node,callback){\n\tif(callback(node)){\n\t\treturn true;\n\t}\n\tif(node = node.firstChild){\n\t\tdo{\n\t\t\tif(_visitNode(node,callback)){return true}\n        }while(node=node.nextSibling)\n    }\n}\n\n\n\nfunction Document(){\n\tthis.ownerDocument = this;\n}\n\nfunction _onAddAttribute(doc,el,newAttr){\n\tdoc && doc._inc++;\n\tvar ns = newAttr.namespaceURI ;\n\tif(ns === NAMESPACE.XMLNS){\n\t\t//update namespace\n\t\tel._nsMap[newAttr.prefix?newAttr.localName:''] = newAttr.value\n\t}\n}\n\nfunction _onRemoveAttribute(doc,el,newAttr,remove){\n\tdoc && doc._inc++;\n\tvar ns = newAttr.namespaceURI ;\n\tif(ns === NAMESPACE.XMLNS){\n\t\t//update namespace\n\t\tdelete el._nsMap[newAttr.prefix?newAttr.localName:'']\n\t}\n}\n\n/**\n * Updates `el.childNodes`, updating the indexed items and it's `length`.\n * Passing `newChild` means it will be appended.\n * Otherwise it's assumed that an item has been removed,\n * and `el.firstNode` and it's `.nextSibling` are used\n * to walk the current list of child nodes.\n *\n * @param {Document} doc\n * @param {Node} el\n * @param {Node} [newChild]\n * @private\n */\nfunction _onUpdateChild (doc, el, newChild) {\n\tif(doc && doc._inc){\n\t\tdoc._inc++;\n\t\t//update childNodes\n\t\tvar cs = el.childNodes;\n\t\tif (newChild) {\n\t\t\tcs[cs.length++] = newChild;\n\t\t} else {\n\t\t\tvar child = el.firstChild;\n\t\t\tvar i = 0;\n\t\t\twhile (child) {\n\t\t\t\tcs[i++] = child;\n\t\t\t\tchild = child.nextSibling;\n\t\t\t}\n\t\t\tcs.length = i;\n\t\t\tdelete cs[cs.length];\n\t\t}\n\t}\n}\n\n/**\n * Removes the connections between `parentNode` and `child`\n * and any existing `child.previousSibling` or `child.nextSibling`.\n *\n * @see https://github.com/xmldom/xmldom/issues/135\n * @see https://github.com/xmldom/xmldom/issues/145\n *\n * @param {Node} parentNode\n * @param {Node} child\n * @returns {Node} the child that was removed.\n * @private\n */\nfunction _removeChild (parentNode, child) {\n\tvar previous = child.previousSibling;\n\tvar next = child.nextSibling;\n\tif (previous) {\n\t\tprevious.nextSibling = next;\n\t} else {\n\t\tparentNode.firstChild = next;\n\t}\n\tif (next) {\n\t\tnext.previousSibling = previous;\n\t} else {\n\t\tparentNode.lastChild = previous;\n\t}\n\tchild.parentNode = null;\n\tchild.previousSibling = null;\n\tchild.nextSibling = null;\n\t_onUpdateChild(parentNode.ownerDocument, parentNode);\n\treturn child;\n}\n\n/**\n * Returns `true` if `node` can be a parent for insertion.\n * @param {Node} node\n * @returns {boolean}\n */\nfunction hasValidParentNodeType(node) {\n\treturn (\n\t\tnode &&\n\t\t(node.nodeType === Node.DOCUMENT_NODE || node.nodeType === Node.DOCUMENT_FRAGMENT_NODE || node.nodeType === Node.ELEMENT_NODE)\n\t);\n}\n\n/**\n * Returns `true` if `node` can be inserted according to it's `nodeType`.\n * @param {Node} node\n * @returns {boolean}\n */\nfunction hasInsertableNodeType(node) {\n\treturn (\n\t\tnode &&\n\t\t(isElementNode(node) ||\n\t\t\tisTextNode(node) ||\n\t\t\tisDocTypeNode(node) ||\n\t\t\tnode.nodeType === Node.DOCUMENT_FRAGMENT_NODE ||\n\t\t\tnode.nodeType === Node.COMMENT_NODE ||\n\t\t\tnode.nodeType === Node.PROCESSING_INSTRUCTION_NODE)\n\t);\n}\n\n/**\n * Returns true if `node` is a DOCTYPE node\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isDocTypeNode(node) {\n\treturn node && node.nodeType === Node.DOCUMENT_TYPE_NODE;\n}\n\n/**\n * Returns true if the node is an element\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isElementNode(node) {\n\treturn node && node.nodeType === Node.ELEMENT_NODE;\n}\n/**\n * Returns true if `node` is a text node\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isTextNode(node) {\n\treturn node && node.nodeType === Node.TEXT_NODE;\n}\n\n/**\n * Check if en element node can be inserted before `child`, or at the end if child is falsy,\n * according to the presence and position of a doctype node on the same level.\n *\n * @param {Document} doc The document node\n * @param {Node} child the node that would become the nextSibling if the element would be inserted\n * @returns {boolean} `true` if an element can be inserted before child\n * @private\n * https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction isElementInsertionPossible(doc, child) {\n\tvar parentChildNodes = doc.childNodes || [];\n\tif (find(parentChildNodes, isElementNode) || isDocTypeNode(child)) {\n\t\treturn false;\n\t}\n\tvar docTypeNode = find(parentChildNodes, isDocTypeNode);\n\treturn !(child && docTypeNode && parentChildNodes.indexOf(docTypeNode) > parentChildNodes.indexOf(child));\n}\n\n/**\n * Check if en element node can be inserted before `child`, or at the end if child is falsy,\n * according to the presence and position of a doctype node on the same level.\n *\n * @param {Node} doc The document node\n * @param {Node} child the node that would become the nextSibling if the element would be inserted\n * @returns {boolean} `true` if an element can be inserted before child\n * @private\n * https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction isElementReplacementPossible(doc, child) {\n\tvar parentChildNodes = doc.childNodes || [];\n\n\tfunction hasElementChildThatIsNotChild(node) {\n\t\treturn isElementNode(node) && node !== child;\n\t}\n\n\tif (find(parentChildNodes, hasElementChildThatIsNotChild)) {\n\t\treturn false;\n\t}\n\tvar docTypeNode = find(parentChildNodes, isDocTypeNode);\n\treturn !(child && docTypeNode && parentChildNodes.indexOf(docTypeNode) > parentChildNodes.indexOf(child));\n}\n\n/**\n * @private\n * Steps 1-5 of the checks before inserting and before replacing a child are the same.\n *\n * @param {Node} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node=} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreInsertionValidity1to5(parent, node, child) {\n\t// 1. If `parent` is not a Document, DocumentFragment, or Element node, then throw a \"HierarchyRequestError\" DOMException.\n\tif (!hasValidParentNodeType(parent)) {\n\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Unexpected parent node type ' + parent.nodeType);\n\t}\n\t// 2. If `node` is a host-including inclusive ancestor of `parent`, then throw a \"HierarchyRequestError\" DOMException.\n\t// not implemented!\n\t// 3. If `child` is non-null and its parent is not `parent`, then throw a \"NotFoundError\" DOMException.\n\tif (child && child.parentNode !== parent) {\n\t\tthrow new DOMException(NOT_FOUND_ERR, 'child not in parent');\n\t}\n\tif (\n\t\t// 4. If `node` is not a DocumentFragment, DocumentType, Element, or CharacterData node, then throw a \"HierarchyRequestError\" DOMException.\n\t\t!hasInsertableNodeType(node) ||\n\t\t// 5. If either `node` is a Text node and `parent` is a document,\n\t\t// the sax parser currently adds top level text nodes, this will be fixed in 0.9.0\n\t\t// || (node.nodeType === Node.TEXT_NODE && parent.nodeType === Node.DOCUMENT_NODE)\n\t\t// or `node` is a doctype and `parent` is not a document, then throw a \"HierarchyRequestError\" DOMException.\n\t\t(isDocTypeNode(node) && parent.nodeType !== Node.DOCUMENT_NODE)\n\t) {\n\t\tthrow new DOMException(\n\t\t\tHIERARCHY_REQUEST_ERR,\n\t\t\t'Unexpected node type ' + node.nodeType + ' for parent node type ' + parent.nodeType\n\t\t);\n\t}\n}\n\n/**\n * @private\n * Step 6 of the checks before inserting and before replacing a child are different.\n *\n * @param {Document} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node | undefined} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreInsertionValidityInDocument(parent, node, child) {\n\tvar parentChildNodes = parent.childNodes || [];\n\tvar nodeChildNodes = node.childNodes || [];\n\n\t// DocumentFragment\n\tif (node.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n\t\tvar nodeChildElements = nodeChildNodes.filter(isElementNode);\n\t\t// If node has more than one element child or has a Text node child.\n\t\tif (nodeChildElements.length > 1 || find(nodeChildNodes, isTextNode)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'More than one element or text in fragment');\n\t\t}\n\t\t// Otherwise, if `node` has one element child and either `parent` has an element child,\n\t\t// `child` is a doctype, or `child` is non-null and a doctype is following `child`.\n\t\tif (nodeChildElements.length === 1 && !isElementInsertionPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Element in fragment can not be inserted before doctype');\n\t\t}\n\t}\n\t// Element\n\tif (isElementNode(node)) {\n\t\t// `parent` has an element child, `child` is a doctype,\n\t\t// or `child` is non-null and a doctype is following `child`.\n\t\tif (!isElementInsertionPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one element can be added and only after doctype');\n\t\t}\n\t}\n\t// DocumentType\n\tif (isDocTypeNode(node)) {\n\t\t// `parent` has a doctype child,\n\t\tif (find(parentChildNodes, isDocTypeNode)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one doctype is allowed');\n\t\t}\n\t\tvar parentElementChild = find(parentChildNodes, isElementNode);\n\t\t// `child` is non-null and an element is preceding `child`,\n\t\tif (child && parentChildNodes.indexOf(parentElementChild) < parentChildNodes.indexOf(child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can only be inserted before an element');\n\t\t}\n\t\t// or `child` is null and `parent` has an element child.\n\t\tif (!child && parentElementChild) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can not be appended since element is present');\n\t\t}\n\t}\n}\n\n/**\n * @private\n * Step 6 of the checks before inserting and before replacing a child are different.\n *\n * @param {Document} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node | undefined} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreReplacementValidityInDocument(parent, node, child) {\n\tvar parentChildNodes = parent.childNodes || [];\n\tvar nodeChildNodes = node.childNodes || [];\n\n\t// DocumentFragment\n\tif (node.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n\t\tvar nodeChildElements = nodeChildNodes.filter(isElementNode);\n\t\t// If `node` has more than one element child or has a Text node child.\n\t\tif (nodeChildElements.length > 1 || find(nodeChildNodes, isTextNode)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'More than one element or text in fragment');\n\t\t}\n\t\t// Otherwise, if `node` has one element child and either `parent` has an element child that is not `child` or a doctype is following `child`.\n\t\tif (nodeChildElements.length === 1 && !isElementReplacementPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Element in fragment can not be inserted before doctype');\n\t\t}\n\t}\n\t// Element\n\tif (isElementNode(node)) {\n\t\t// `parent` has an element child that is not `child` or a doctype is following `child`.\n\t\tif (!isElementReplacementPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one element can be added and only after doctype');\n\t\t}\n\t}\n\t// DocumentType\n\tif (isDocTypeNode(node)) {\n\t\tfunction hasDoctypeChildThatIsNotChild(node) {\n\t\t\treturn isDocTypeNode(node) && node !== child;\n\t\t}\n\n\t\t// `parent` has a doctype child that is not `child`,\n\t\tif (find(parentChildNodes, hasDoctypeChildThatIsNotChild)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one doctype is allowed');\n\t\t}\n\t\tvar parentElementChild = find(parentChildNodes, isElementNode);\n\t\t// or an element is preceding `child`.\n\t\tif (child && parentChildNodes.indexOf(parentElementChild) < parentChildNodes.indexOf(child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can only be inserted before an element');\n\t\t}\n\t}\n}\n\n/**\n * @private\n * @param {Node} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node=} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction _insertBefore(parent, node, child, _inDocumentAssertion) {\n\t// To ensure pre-insertion validity of a node into a parent before a child, run these steps:\n\tassertPreInsertionValidity1to5(parent, node, child);\n\n\t// If parent is a document, and any of the statements below, switched on the interface node implements,\n\t// are true, then throw a \"HierarchyRequestError\" DOMException.\n\tif (parent.nodeType === Node.DOCUMENT_NODE) {\n\t\t(_inDocumentAssertion || assertPreInsertionValidityInDocument)(parent, node, child);\n\t}\n\n\tvar cp = node.parentNode;\n\tif(cp){\n\t\tcp.removeChild(node);//remove and update\n\t}\n\tif(node.nodeType === DOCUMENT_FRAGMENT_NODE){\n\t\tvar newFirst = node.firstChild;\n\t\tif (newFirst == null) {\n\t\t\treturn node;\n\t\t}\n\t\tvar newLast = node.lastChild;\n\t}else{\n\t\tnewFirst = newLast = node;\n\t}\n\tvar pre = child ? child.previousSibling : parent.lastChild;\n\n\tnewFirst.previousSibling = pre;\n\tnewLast.nextSibling = child;\n\n\n\tif(pre){\n\t\tpre.nextSibling = newFirst;\n\t}else{\n\t\tparent.firstChild = newFirst;\n\t}\n\tif(child == null){\n\t\tparent.lastChild = newLast;\n\t}else{\n\t\tchild.previousSibling = newLast;\n\t}\n\tdo{\n\t\tnewFirst.parentNode = parent;\n\t}while(newFirst !== newLast && (newFirst= newFirst.nextSibling))\n\t_onUpdateChild(parent.ownerDocument||parent, parent);\n\t//console.log(parent.lastChild.nextSibling == null)\n\tif (node.nodeType == DOCUMENT_FRAGMENT_NODE) {\n\t\tnode.firstChild = node.lastChild = null;\n\t}\n\treturn node;\n}\n\n/**\n * Appends `newChild` to `parentNode`.\n * If `newChild` is already connected to a `parentNode` it is first removed from it.\n *\n * @see https://github.com/xmldom/xmldom/issues/135\n * @see https://github.com/xmldom/xmldom/issues/145\n * @param {Node} parentNode\n * @param {Node} newChild\n * @returns {Node}\n * @private\n */\nfunction _appendSingleChild (parentNode, newChild) {\n\tif (newChild.parentNode) {\n\t\tnewChild.parentNode.removeChild(newChild);\n\t}\n\tnewChild.parentNode = parentNode;\n\tnewChild.previousSibling = parentNode.lastChild;\n\tnewChild.nextSibling = null;\n\tif (newChild.previousSibling) {\n\t\tnewChild.previousSibling.nextSibling = newChild;\n\t} else {\n\t\tparentNode.firstChild = newChild;\n\t}\n\tparentNode.lastChild = newChild;\n\t_onUpdateChild(parentNode.ownerDocument, parentNode, newChild);\n\treturn newChild;\n}\n\nDocument.prototype = {\n\t//implementation : null,\n\tnodeName :  '#document',\n\tnodeType :  DOCUMENT_NODE,\n\t/**\n\t * The DocumentType node of the document.\n\t *\n\t * @readonly\n\t * @type DocumentType\n\t */\n\tdoctype :  null,\n\tdocumentElement :  null,\n\t_inc : 1,\n\n\tinsertBefore :  function(newChild, refChild){//raises\n\t\tif(newChild.nodeType == DOCUMENT_FRAGMENT_NODE){\n\t\t\tvar child = newChild.firstChild;\n\t\t\twhile(child){\n\t\t\t\tvar next = child.nextSibling;\n\t\t\t\tthis.insertBefore(child,refChild);\n\t\t\t\tchild = next;\n\t\t\t}\n\t\t\treturn newChild;\n\t\t}\n\t\t_insertBefore(this, newChild, refChild);\n\t\tnewChild.ownerDocument = this;\n\t\tif (this.documentElement === null && newChild.nodeType === ELEMENT_NODE) {\n\t\t\tthis.documentElement = newChild;\n\t\t}\n\n\t\treturn newChild;\n\t},\n\tremoveChild :  function(oldChild){\n\t\tif(this.documentElement == oldChild){\n\t\t\tthis.documentElement = null;\n\t\t}\n\t\treturn _removeChild(this,oldChild);\n\t},\n\treplaceChild: function (newChild, oldChild) {\n\t\t//raises\n\t\t_insertBefore(this, newChild, oldChild, assertPreReplacementValidityInDocument);\n\t\tnewChild.ownerDocument = this;\n\t\tif (oldChild) {\n\t\t\tthis.removeChild(oldChild);\n\t\t}\n\t\tif (isElementNode(newChild)) {\n\t\t\tthis.documentElement = newChild;\n\t\t}\n\t},\n\t// Introduced in DOM Level 2:\n\timportNode : function(importedNode,deep){\n\t\treturn importNode(this,importedNode,deep);\n\t},\n\t// Introduced in DOM Level 2:\n\tgetElementById :\tfunction(id){\n\t\tvar rtv = null;\n\t\t_visitNode(this.documentElement,function(node){\n\t\t\tif(node.nodeType == ELEMENT_NODE){\n\t\t\t\tif(node.getAttribute('id') == id){\n\t\t\t\t\trtv = node;\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t\treturn rtv;\n\t},\n\n\t/**\n\t * The `getElementsByClassName` method of `Document` interface returns an array-like object\n\t * of all child elements which have **all** of the given class name(s).\n\t *\n\t * Returns an empty list if `classeNames` is an empty string or only contains HTML white space characters.\n\t *\n\t *\n\t * Warning: This is a live LiveNodeList.\n\t * Changes in the DOM will reflect in the array as the changes occur.\n\t * If an element selected by this array no longer qualifies for the selector,\n\t * it will automatically be removed. Be aware of this for iteration purposes.\n\t *\n\t * @param {string} classNames is a string representing the class name(s) to match; multiple class names are separated by (ASCII-)whitespace\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/Document/getElementsByClassName\n\t * @see https://dom.spec.whatwg.org/#concept-getelementsbyclassname\n\t */\n\tgetElementsByClassName: function(classNames) {\n\t\tvar classNamesSet = toOrderedSet(classNames)\n\t\treturn new LiveNodeList(this, function(base) {\n\t\t\tvar ls = [];\n\t\t\tif (classNamesSet.length > 0) {\n\t\t\t\t_visitNode(base.documentElement, function(node) {\n\t\t\t\t\tif(node !== base && node.nodeType === ELEMENT_NODE) {\n\t\t\t\t\t\tvar nodeClassNames = node.getAttribute('class')\n\t\t\t\t\t\t// can be null if the attribute does not exist\n\t\t\t\t\t\tif (nodeClassNames) {\n\t\t\t\t\t\t\t// before splitting and iterating just compare them for the most common case\n\t\t\t\t\t\t\tvar matches = classNames === nodeClassNames;\n\t\t\t\t\t\t\tif (!matches) {\n\t\t\t\t\t\t\t\tvar nodeClassNamesSet = toOrderedSet(nodeClassNames)\n\t\t\t\t\t\t\t\tmatches = classNamesSet.every(arrayIncludes(nodeClassNamesSet))\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(matches) {\n\t\t\t\t\t\t\t\tls.push(node);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\treturn ls;\n\t\t});\n\t},\n\n\t//document factory method:\n\tcreateElement :\tfunction(tagName){\n\t\tvar node = new Element();\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = tagName;\n\t\tnode.tagName = tagName;\n\t\tnode.localName = tagName;\n\t\tnode.childNodes = new NodeList();\n\t\tvar attrs\t= node.attributes = new NamedNodeMap();\n\t\tattrs._ownerElement = node;\n\t\treturn node;\n\t},\n\tcreateDocumentFragment :\tfunction(){\n\t\tvar node = new DocumentFragment();\n\t\tnode.ownerDocument = this;\n\t\tnode.childNodes = new NodeList();\n\t\treturn node;\n\t},\n\tcreateTextNode :\tfunction(data){\n\t\tvar node = new Text();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateComment :\tfunction(data){\n\t\tvar node = new Comment();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateCDATASection :\tfunction(data){\n\t\tvar node = new CDATASection();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateProcessingInstruction :\tfunction(target,data){\n\t\tvar node = new ProcessingInstruction();\n\t\tnode.ownerDocument = this;\n\t\tnode.tagName = node.nodeName = node.target = target;\n\t\tnode.nodeValue = node.data = data;\n\t\treturn node;\n\t},\n\tcreateAttribute :\tfunction(name){\n\t\tvar node = new Attr();\n\t\tnode.ownerDocument\t= this;\n\t\tnode.name = name;\n\t\tnode.nodeName\t= name;\n\t\tnode.localName = name;\n\t\tnode.specified = true;\n\t\treturn node;\n\t},\n\tcreateEntityReference :\tfunction(name){\n\t\tvar node = new EntityReference();\n\t\tnode.ownerDocument\t= this;\n\t\tnode.nodeName\t= name;\n\t\treturn node;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateElementNS :\tfunction(namespaceURI,qualifiedName){\n\t\tvar node = new Element();\n\t\tvar pl = qualifiedName.split(':');\n\t\tvar attrs\t= node.attributes = new NamedNodeMap();\n\t\tnode.childNodes = new NodeList();\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.tagName = qualifiedName;\n\t\tnode.namespaceURI = namespaceURI;\n\t\tif(pl.length == 2){\n\t\t\tnode.prefix = pl[0];\n\t\t\tnode.localName = pl[1];\n\t\t}else{\n\t\t\t//el.prefix = null;\n\t\t\tnode.localName = qualifiedName;\n\t\t}\n\t\tattrs._ownerElement = node;\n\t\treturn node;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateAttributeNS :\tfunction(namespaceURI,qualifiedName){\n\t\tvar node = new Attr();\n\t\tvar pl = qualifiedName.split(':');\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.name = qualifiedName;\n\t\tnode.namespaceURI = namespaceURI;\n\t\tnode.specified = true;\n\t\tif(pl.length == 2){\n\t\t\tnode.prefix = pl[0];\n\t\t\tnode.localName = pl[1];\n\t\t}else{\n\t\t\t//el.prefix = null;\n\t\t\tnode.localName = qualifiedName;\n\t\t}\n\t\treturn node;\n\t}\n};\n_extends(Document,Node);\n\n\nfunction Element() {\n\tthis._nsMap = {};\n};\nElement.prototype = {\n\tnodeType : ELEMENT_NODE,\n\thasAttribute : function(name){\n\t\treturn this.getAttributeNode(name)!=null;\n\t},\n\tgetAttribute : function(name){\n\t\tvar attr = this.getAttributeNode(name);\n\t\treturn attr && attr.value || '';\n\t},\n\tgetAttributeNode : function(name){\n\t\treturn this.attributes.getNamedItem(name);\n\t},\n\tsetAttribute : function(name, value){\n\t\tvar attr = this.ownerDocument.createAttribute(name);\n\t\tattr.value = attr.nodeValue = \"\" + value;\n\t\tthis.setAttributeNode(attr)\n\t},\n\tremoveAttribute : function(name){\n\t\tvar attr = this.getAttributeNode(name)\n\t\tattr && this.removeAttributeNode(attr);\n\t},\n\n\t//four real opeartion method\n\tappendChild:function(newChild){\n\t\tif(newChild.nodeType === DOCUMENT_FRAGMENT_NODE){\n\t\t\treturn this.insertBefore(newChild,null);\n\t\t}else{\n\t\t\treturn _appendSingleChild(this,newChild);\n\t\t}\n\t},\n\tsetAttributeNode : function(newAttr){\n\t\treturn this.attributes.setNamedItem(newAttr);\n\t},\n\tsetAttributeNodeNS : function(newAttr){\n\t\treturn this.attributes.setNamedItemNS(newAttr);\n\t},\n\tremoveAttributeNode : function(oldAttr){\n\t\t//console.log(this == oldAttr.ownerElement)\n\t\treturn this.attributes.removeNamedItem(oldAttr.nodeName);\n\t},\n\t//get real attribute name,and remove it by removeAttributeNode\n\tremoveAttributeNS : function(namespaceURI, localName){\n\t\tvar old = this.getAttributeNodeNS(namespaceURI, localName);\n\t\told && this.removeAttributeNode(old);\n\t},\n\n\thasAttributeNS : function(namespaceURI, localName){\n\t\treturn this.getAttributeNodeNS(namespaceURI, localName)!=null;\n\t},\n\tgetAttributeNS : function(namespaceURI, localName){\n\t\tvar attr = this.getAttributeNodeNS(namespaceURI, localName);\n\t\treturn attr && attr.value || '';\n\t},\n\tsetAttributeNS : function(namespaceURI, qualifiedName, value){\n\t\tvar attr = this.ownerDocument.createAttributeNS(namespaceURI, qualifiedName);\n\t\tattr.value = attr.nodeValue = \"\" + value;\n\t\tthis.setAttributeNode(attr)\n\t},\n\tgetAttributeNodeNS : function(namespaceURI, localName){\n\t\treturn this.attributes.getNamedItemNS(namespaceURI, localName);\n\t},\n\n\tgetElementsByTagName : function(tagName){\n\t\treturn new LiveNodeList(this,function(base){\n\t\t\tvar ls = [];\n\t\t\t_visitNode(base,function(node){\n\t\t\t\tif(node !== base && node.nodeType == ELEMENT_NODE && (tagName === '*' || node.tagName == tagName)){\n\t\t\t\t\tls.push(node);\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ls;\n\t\t});\n\t},\n\tgetElementsByTagNameNS : function(namespaceURI, localName){\n\t\treturn new LiveNodeList(this,function(base){\n\t\t\tvar ls = [];\n\t\t\t_visitNode(base,function(node){\n\t\t\t\tif(node !== base && node.nodeType === ELEMENT_NODE && (namespaceURI === '*' || node.namespaceURI === namespaceURI) && (localName === '*' || node.localName == localName)){\n\t\t\t\t\tls.push(node);\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ls;\n\n\t\t});\n\t}\n};\nDocument.prototype.getElementsByTagName = Element.prototype.getElementsByTagName;\nDocument.prototype.getElementsByTagNameNS = Element.prototype.getElementsByTagNameNS;\n\n\n_extends(Element,Node);\nfunction Attr() {\n};\nAttr.prototype.nodeType = ATTRIBUTE_NODE;\n_extends(Attr,Node);\n\n\nfunction CharacterData() {\n};\nCharacterData.prototype = {\n\tdata : '',\n\tsubstringData : function(offset, count) {\n\t\treturn this.data.substring(offset, offset+count);\n\t},\n\tappendData: function(text) {\n\t\ttext = this.data+text;\n\t\tthis.nodeValue = this.data = text;\n\t\tthis.length = text.length;\n\t},\n\tinsertData: function(offset,text) {\n\t\tthis.replaceData(offset,0,text);\n\n\t},\n\tappendChild:function(newChild){\n\t\tthrow new Error(ExceptionMessage[HIERARCHY_REQUEST_ERR])\n\t},\n\tdeleteData: function(offset, count) {\n\t\tthis.replaceData(offset,count,\"\");\n\t},\n\treplaceData: function(offset, count, text) {\n\t\tvar start = this.data.substring(0,offset);\n\t\tvar end = this.data.substring(offset+count);\n\t\ttext = start + text + end;\n\t\tthis.nodeValue = this.data = text;\n\t\tthis.length = text.length;\n\t}\n}\n_extends(CharacterData,Node);\nfunction Text() {\n};\nText.prototype = {\n\tnodeName : \"#text\",\n\tnodeType : TEXT_NODE,\n\tsplitText : function(offset) {\n\t\tvar text = this.data;\n\t\tvar newText = text.substring(offset);\n\t\ttext = text.substring(0, offset);\n\t\tthis.data = this.nodeValue = text;\n\t\tthis.length = text.length;\n\t\tvar newNode = this.ownerDocument.createTextNode(newText);\n\t\tif(this.parentNode){\n\t\t\tthis.parentNode.insertBefore(newNode, this.nextSibling);\n\t\t}\n\t\treturn newNode;\n\t}\n}\n_extends(Text,CharacterData);\nfunction Comment() {\n};\nComment.prototype = {\n\tnodeName : \"#comment\",\n\tnodeType : COMMENT_NODE\n}\n_extends(Comment,CharacterData);\n\nfunction CDATASection() {\n};\nCDATASection.prototype = {\n\tnodeName : \"#cdata-section\",\n\tnodeType : CDATA_SECTION_NODE\n}\n_extends(CDATASection,CharacterData);\n\n\nfunction DocumentType() {\n};\nDocumentType.prototype.nodeType = DOCUMENT_TYPE_NODE;\n_extends(DocumentType,Node);\n\nfunction Notation() {\n};\nNotation.prototype.nodeType = NOTATION_NODE;\n_extends(Notation,Node);\n\nfunction Entity() {\n};\nEntity.prototype.nodeType = ENTITY_NODE;\n_extends(Entity,Node);\n\nfunction EntityReference() {\n};\nEntityReference.prototype.nodeType = ENTITY_REFERENCE_NODE;\n_extends(EntityReference,Node);\n\nfunction DocumentFragment() {\n};\nDocumentFragment.prototype.nodeName =\t\"#document-fragment\";\nDocumentFragment.prototype.nodeType =\tDOCUMENT_FRAGMENT_NODE;\n_extends(DocumentFragment,Node);\n\n\nfunction ProcessingInstruction() {\n}\nProcessingInstruction.prototype.nodeType = PROCESSING_INSTRUCTION_NODE;\n_extends(ProcessingInstruction,Node);\nfunction XMLSerializer(){}\nXMLSerializer.prototype.serializeToString = function(node,isHtml,nodeFilter){\n\treturn nodeSerializeToString.call(node,isHtml,nodeFilter);\n}\nNode.prototype.toString = nodeSerializeToString;\nfunction nodeSerializeToString(isHtml,nodeFilter){\n\tvar buf = [];\n\tvar refNode = this.nodeType == 9 && this.documentElement || this;\n\tvar prefix = refNode.prefix;\n\tvar uri = refNode.namespaceURI;\n\n\tif(uri && prefix == null){\n\t\t//console.log(prefix)\n\t\tvar prefix = refNode.lookupPrefix(uri);\n\t\tif(prefix == null){\n\t\t\t//isHTML = true;\n\t\t\tvar visibleNamespaces=[\n\t\t\t{namespace:uri,prefix:null}\n\t\t\t//{namespace:uri,prefix:''}\n\t\t\t]\n\t\t}\n\t}\n\tserializeToString(this,buf,isHtml,nodeFilter,visibleNamespaces);\n\t//console.log('###',this.nodeType,uri,prefix,buf.join(''))\n\treturn buf.join('');\n}\n\nfunction needNamespaceDefine(node, isHTML, visibleNamespaces) {\n\tvar prefix = node.prefix || '';\n\tvar uri = node.namespaceURI;\n\t// According to [Namespaces in XML 1.0](https://www.w3.org/TR/REC-xml-names/#ns-using) ,\n\t// and more specifically https://www.w3.org/TR/REC-xml-names/#nsc-NoPrefixUndecl :\n\t// > In a namespace declaration for a prefix [...], the attribute value MUST NOT be empty.\n\t// in a similar manner [Namespaces in XML 1.1](https://www.w3.org/TR/xml-names11/#ns-using)\n\t// and more specifically https://www.w3.org/TR/xml-names11/#nsc-NSDeclared :\n\t// > [...] Furthermore, the attribute value [...] must not be an empty string.\n\t// so serializing empty namespace value like xmlns:ds=\"\" would produce an invalid XML document.\n\tif (!uri) {\n\t\treturn false;\n\t}\n\tif (prefix === \"xml\" && uri === NAMESPACE.XML || uri === NAMESPACE.XMLNS) {\n\t\treturn false;\n\t}\n\n\tvar i = visibleNamespaces.length\n\twhile (i--) {\n\t\tvar ns = visibleNamespaces[i];\n\t\t// get namespace prefix\n\t\tif (ns.prefix === prefix) {\n\t\t\treturn ns.namespace !== uri;\n\t\t}\n\t}\n\treturn true;\n}\n/**\n * Well-formed constraint: No < in Attribute Values\n * > The replacement text of any entity referred to directly or indirectly\n * > in an attribute value must not contain a <.\n * @see https://www.w3.org/TR/xml11/#CleanAttrVals\n * @see https://www.w3.org/TR/xml11/#NT-AttValue\n *\n * Literal whitespace other than space that appear in attribute values\n * are serialized as their entity references, so they will be preserved.\n * (In contrast to whitespace literals in the input which are normalized to spaces)\n * @see https://www.w3.org/TR/xml11/#AVNormalize\n * @see https://w3c.github.io/DOM-Parsing/#serializing-an-element-s-attributes\n */\nfunction addSerializedAttribute(buf, qualifiedName, value) {\n\tbuf.push(' ', qualifiedName, '=\"', value.replace(/[<>&\"\\t\\n\\r]/g, _xmlEncoder), '\"')\n}\n\nfunction serializeToString(node,buf,isHTML,nodeFilter,visibleNamespaces){\n\tif (!visibleNamespaces) {\n\t\tvisibleNamespaces = [];\n\t}\n\n\tif(nodeFilter){\n\t\tnode = nodeFilter(node);\n\t\tif(node){\n\t\t\tif(typeof node == 'string'){\n\t\t\t\tbuf.push(node);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}else{\n\t\t\treturn;\n\t\t}\n\t\t//buf.sort.apply(attrs, attributeSorter);\n\t}\n\n\tswitch(node.nodeType){\n\tcase ELEMENT_NODE:\n\t\tvar attrs = node.attributes;\n\t\tvar len = attrs.length;\n\t\tvar child = node.firstChild;\n\t\tvar nodeName = node.tagName;\n\n\t\tisHTML = NAMESPACE.isHTML(node.namespaceURI) || isHTML\n\n\t\tvar prefixedNodeName = nodeName\n\t\tif (!isHTML && !node.prefix && node.namespaceURI) {\n\t\t\tvar defaultNS\n\t\t\t// lookup current default ns from `xmlns` attribute\n\t\t\tfor (var ai = 0; ai < attrs.length; ai++) {\n\t\t\t\tif (attrs.item(ai).name === 'xmlns') {\n\t\t\t\t\tdefaultNS = attrs.item(ai).value\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (!defaultNS) {\n\t\t\t\t// lookup current default ns in visibleNamespaces\n\t\t\t\tfor (var nsi = visibleNamespaces.length - 1; nsi >= 0; nsi--) {\n\t\t\t\t\tvar namespace = visibleNamespaces[nsi]\n\t\t\t\t\tif (namespace.prefix === '' && namespace.namespace === node.namespaceURI) {\n\t\t\t\t\t\tdefaultNS = namespace.namespace\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (defaultNS !== node.namespaceURI) {\n\t\t\t\tfor (var nsi = visibleNamespaces.length - 1; nsi >= 0; nsi--) {\n\t\t\t\t\tvar namespace = visibleNamespaces[nsi]\n\t\t\t\t\tif (namespace.namespace === node.namespaceURI) {\n\t\t\t\t\t\tif (namespace.prefix) {\n\t\t\t\t\t\t\tprefixedNodeName = namespace.prefix + ':' + nodeName\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tbuf.push('<', prefixedNodeName);\n\n\t\tfor(var i=0;i<len;i++){\n\t\t\t// add namespaces for attributes\n\t\t\tvar attr = attrs.item(i);\n\t\t\tif (attr.prefix == 'xmlns') {\n\t\t\t\tvisibleNamespaces.push({ prefix: attr.localName, namespace: attr.value });\n\t\t\t}else if(attr.nodeName == 'xmlns'){\n\t\t\t\tvisibleNamespaces.push({ prefix: '', namespace: attr.value });\n\t\t\t}\n\t\t}\n\n\t\tfor(var i=0;i<len;i++){\n\t\t\tvar attr = attrs.item(i);\n\t\t\tif (needNamespaceDefine(attr,isHTML, visibleNamespaces)) {\n\t\t\t\tvar prefix = attr.prefix||'';\n\t\t\t\tvar uri = attr.namespaceURI;\n\t\t\t\taddSerializedAttribute(buf, prefix ? 'xmlns:' + prefix : \"xmlns\", uri);\n\t\t\t\tvisibleNamespaces.push({ prefix: prefix, namespace:uri });\n\t\t\t}\n\t\t\tserializeToString(attr,buf,isHTML,nodeFilter,visibleNamespaces);\n\t\t}\n\n\t\t// add namespace for current node\n\t\tif (nodeName === prefixedNodeName && needNamespaceDefine(node, isHTML, visibleNamespaces)) {\n\t\t\tvar prefix = node.prefix||'';\n\t\t\tvar uri = node.namespaceURI;\n\t\t\taddSerializedAttribute(buf, prefix ? 'xmlns:' + prefix : \"xmlns\", uri);\n\t\t\tvisibleNamespaces.push({ prefix: prefix, namespace:uri });\n\t\t}\n\n\t\tif(child || isHTML && !/^(?:meta|link|img|br|hr|input)$/i.test(nodeName)){\n\t\t\tbuf.push('>');\n\t\t\t//if is cdata child node\n\t\t\tif(isHTML && /^script$/i.test(nodeName)){\n\t\t\t\twhile(child){\n\t\t\t\t\tif(child.data){\n\t\t\t\t\t\tbuf.push(child.data);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tserializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n\t\t\t\t\t}\n\t\t\t\t\tchild = child.nextSibling;\n\t\t\t\t}\n\t\t\t}else\n\t\t\t{\n\t\t\t\twhile(child){\n\t\t\t\t\tserializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n\t\t\t\t\tchild = child.nextSibling;\n\t\t\t\t}\n\t\t\t}\n\t\t\tbuf.push('</',prefixedNodeName,'>');\n\t\t}else{\n\t\t\tbuf.push('/>');\n\t\t}\n\t\t// remove added visible namespaces\n\t\t//visibleNamespaces.length = startVisibleNamespaces;\n\t\treturn;\n\tcase DOCUMENT_NODE:\n\tcase DOCUMENT_FRAGMENT_NODE:\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tserializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t\treturn;\n\tcase ATTRIBUTE_NODE:\n\t\treturn addSerializedAttribute(buf, node.name, node.value);\n\tcase TEXT_NODE:\n\t\t/**\n\t\t * The ampersand character (&) and the left angle bracket (<) must not appear in their literal form,\n\t\t * except when used as markup delimiters, or within a comment, a processing instruction, or a CDATA section.\n\t\t * If they are needed elsewhere, they must be escaped using either numeric character references or the strings\n\t\t * `&amp;` and `&lt;` respectively.\n\t\t * The right angle bracket (>) may be represented using the string \" &gt; \", and must, for compatibility,\n\t\t * be escaped using either `&gt;` or a character reference when it appears in the string `]]>` in content,\n\t\t * when that string is not marking the end of a CDATA section.\n\t\t *\n\t\t * In the content of elements, character data is any string of characters\n\t\t * which does not contain the start-delimiter of any markup\n\t\t * and does not include the CDATA-section-close delimiter, `]]>`.\n\t\t *\n\t\t * @see https://www.w3.org/TR/xml/#NT-CharData\n\t\t * @see https://w3c.github.io/DOM-Parsing/#xml-serializing-a-text-node\n\t\t */\n\t\treturn buf.push(node.data\n\t\t\t.replace(/[<&>]/g,_xmlEncoder)\n\t\t);\n\tcase CDATA_SECTION_NODE:\n\t\treturn buf.push( '<![CDATA[',node.data,']]>');\n\tcase COMMENT_NODE:\n\t\treturn buf.push( \"<!--\",node.data,\"-->\");\n\tcase DOCUMENT_TYPE_NODE:\n\t\tvar pubid = node.publicId;\n\t\tvar sysid = node.systemId;\n\t\tbuf.push('<!DOCTYPE ',node.name);\n\t\tif(pubid){\n\t\t\tbuf.push(' PUBLIC ', pubid);\n\t\t\tif (sysid && sysid!='.') {\n\t\t\t\tbuf.push(' ', sysid);\n\t\t\t}\n\t\t\tbuf.push('>');\n\t\t}else if(sysid && sysid!='.'){\n\t\t\tbuf.push(' SYSTEM ', sysid, '>');\n\t\t}else{\n\t\t\tvar sub = node.internalSubset;\n\t\t\tif(sub){\n\t\t\t\tbuf.push(\" [\",sub,\"]\");\n\t\t\t}\n\t\t\tbuf.push(\">\");\n\t\t}\n\t\treturn;\n\tcase PROCESSING_INSTRUCTION_NODE:\n\t\treturn buf.push( \"<?\",node.target,\" \",node.data,\"?>\");\n\tcase ENTITY_REFERENCE_NODE:\n\t\treturn buf.push( '&',node.nodeName,';');\n\t//case ENTITY_NODE:\n\t//case NOTATION_NODE:\n\tdefault:\n\t\tbuf.push('??',node.nodeName);\n\t}\n}\nfunction importNode(doc,node,deep){\n\tvar node2;\n\tswitch (node.nodeType) {\n\tcase ELEMENT_NODE:\n\t\tnode2 = node.cloneNode(false);\n\t\tnode2.ownerDocument = doc;\n\t\t//var attrs = node2.attributes;\n\t\t//var len = attrs.length;\n\t\t//for(var i=0;i<len;i++){\n\t\t\t//node2.setAttributeNodeNS(importNode(doc,attrs.item(i),deep));\n\t\t//}\n\tcase DOCUMENT_FRAGMENT_NODE:\n\t\tbreak;\n\tcase ATTRIBUTE_NODE:\n\t\tdeep = true;\n\t\tbreak;\n\t//case ENTITY_REFERENCE_NODE:\n\t//case PROCESSING_INSTRUCTION_NODE:\n\t////case TEXT_NODE:\n\t//case CDATA_SECTION_NODE:\n\t//case COMMENT_NODE:\n\t//\tdeep = false;\n\t//\tbreak;\n\t//case DOCUMENT_NODE:\n\t//case DOCUMENT_TYPE_NODE:\n\t//cannot be imported.\n\t//case ENTITY_NODE:\n\t//case NOTATION_NODE：\n\t//can not hit in level3\n\t//default:throw e;\n\t}\n\tif(!node2){\n\t\tnode2 = node.cloneNode(false);//false\n\t}\n\tnode2.ownerDocument = doc;\n\tnode2.parentNode = null;\n\tif(deep){\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tnode2.appendChild(importNode(doc,child,deep));\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t}\n\treturn node2;\n}\n//\n//var _relationMap = {firstChild:1,lastChild:1,previousSibling:1,nextSibling:1,\n//\t\t\t\t\tattributes:1,childNodes:1,parentNode:1,documentElement:1,doctype,};\nfunction cloneNode(doc,node,deep){\n\tvar node2 = new node.constructor();\n\tfor (var n in node) {\n\t\tif (Object.prototype.hasOwnProperty.call(node, n)) {\n\t\t\tvar v = node[n];\n\t\t\tif (typeof v != \"object\") {\n\t\t\t\tif (v != node2[n]) {\n\t\t\t\t\tnode2[n] = v;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\tif(node.childNodes){\n\t\tnode2.childNodes = new NodeList();\n\t}\n\tnode2.ownerDocument = doc;\n\tswitch (node2.nodeType) {\n\tcase ELEMENT_NODE:\n\t\tvar attrs\t= node.attributes;\n\t\tvar attrs2\t= node2.attributes = new NamedNodeMap();\n\t\tvar len = attrs.length\n\t\tattrs2._ownerElement = node2;\n\t\tfor(var i=0;i<len;i++){\n\t\t\tnode2.setAttributeNode(cloneNode(doc,attrs.item(i),true));\n\t\t}\n\t\tbreak;;\n\tcase ATTRIBUTE_NODE:\n\t\tdeep = true;\n\t}\n\tif(deep){\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tnode2.appendChild(cloneNode(doc,child,deep));\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t}\n\treturn node2;\n}\n\nfunction __set__(object,key,value){\n\tobject[key] = value\n}\n//do dynamic\ntry{\n\tif(Object.defineProperty){\n\t\tObject.defineProperty(LiveNodeList.prototype,'length',{\n\t\t\tget:function(){\n\t\t\t\t_updateLiveList(this);\n\t\t\t\treturn this.$$length;\n\t\t\t}\n\t\t});\n\n\t\tObject.defineProperty(Node.prototype,'textContent',{\n\t\t\tget:function(){\n\t\t\t\treturn getTextContent(this);\n\t\t\t},\n\n\t\t\tset:function(data){\n\t\t\t\tswitch(this.nodeType){\n\t\t\t\tcase ELEMENT_NODE:\n\t\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\t\t\twhile(this.firstChild){\n\t\t\t\t\t\tthis.removeChild(this.firstChild);\n\t\t\t\t\t}\n\t\t\t\t\tif(data || String(data)){\n\t\t\t\t\t\tthis.appendChild(this.ownerDocument.createTextNode(data));\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tthis.data = data;\n\t\t\t\t\tthis.value = data;\n\t\t\t\t\tthis.nodeValue = data;\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\n\t\tfunction getTextContent(node){\n\t\t\tswitch(node.nodeType){\n\t\t\tcase ELEMENT_NODE:\n\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\t\tvar buf = [];\n\t\t\t\tnode = node.firstChild;\n\t\t\t\twhile(node){\n\t\t\t\t\tif(node.nodeType!==7 && node.nodeType !==8){\n\t\t\t\t\t\tbuf.push(getTextContent(node));\n\t\t\t\t\t}\n\t\t\t\t\tnode = node.nextSibling;\n\t\t\t\t}\n\t\t\t\treturn buf.join('');\n\t\t\tdefault:\n\t\t\t\treturn node.nodeValue;\n\t\t\t}\n\t\t}\n\n\t\t__set__ = function(object,key,value){\n\t\t\t//console.log(value)\n\t\t\tobject['$$'+key] = value\n\t\t}\n\t}\n}catch(e){//ie8\n}\n\n//if(typeof require == 'function'){\n\texports.DocumentType = DocumentType;\n\texports.DOMException = DOMException;\n\texports.DOMImplementation = DOMImplementation;\n\texports.Element = Element;\n\texports.Node = Node;\n\texports.NodeList = NodeList;\n\texports.XMLSerializer = XMLSerializer;\n//}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xmldom/xmldom/lib/dom.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xmldom/xmldom/lib/entities.js":
/*!*****************************************************!*\
  !*** ./node_modules/@xmldom/xmldom/lib/entities.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar freeze = (__webpack_require__(/*! ./conventions */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/conventions.js\").freeze);\n\n/**\n * The entities that are predefined in every XML document.\n *\n * @see https://www.w3.org/TR/2006/REC-xml11-20060816/#sec-predefined-ent W3C XML 1.1\n * @see https://www.w3.org/TR/2008/REC-xml-20081126/#sec-predefined-ent W3C XML 1.0\n * @see https://en.wikipedia.org/wiki/List_of_XML_and_HTML_character_entity_references#Predefined_entities_in_XML Wikipedia\n */\nexports.XML_ENTITIES = freeze({\n\tamp: '&',\n\tapos: \"'\",\n\tgt: '>',\n\tlt: '<',\n\tquot: '\"',\n});\n\n/**\n * A map of all entities that are detected in an HTML document.\n * They contain all entries from `XML_ENTITIES`.\n *\n * @see XML_ENTITIES\n * @see DOMParser.parseFromString\n * @see DOMImplementation.prototype.createHTMLDocument\n * @see https://html.spec.whatwg.org/#named-character-references WHATWG HTML(5) Spec\n * @see https://html.spec.whatwg.org/entities.json JSON\n * @see https://www.w3.org/TR/xml-entity-names/ W3C XML Entity Names\n * @see https://www.w3.org/TR/html4/sgml/entities.html W3C HTML4/SGML\n * @see https://en.wikipedia.org/wiki/List_of_XML_and_HTML_character_entity_references#Character_entity_references_in_HTML Wikipedia (HTML)\n * @see https://en.wikipedia.org/wiki/List_of_XML_and_HTML_character_entity_references#Entities_representing_special_characters_in_XHTML Wikpedia (XHTML)\n */\nexports.HTML_ENTITIES = freeze({\n\tAacute: '\\u00C1',\n\taacute: '\\u00E1',\n\tAbreve: '\\u0102',\n\tabreve: '\\u0103',\n\tac: '\\u223E',\n\tacd: '\\u223F',\n\tacE: '\\u223E\\u0333',\n\tAcirc: '\\u00C2',\n\tacirc: '\\u00E2',\n\tacute: '\\u00B4',\n\tAcy: '\\u0410',\n\tacy: '\\u0430',\n\tAElig: '\\u00C6',\n\taelig: '\\u00E6',\n\taf: '\\u2061',\n\tAfr: '\\uD835\\uDD04',\n\tafr: '\\uD835\\uDD1E',\n\tAgrave: '\\u00C0',\n\tagrave: '\\u00E0',\n\talefsym: '\\u2135',\n\taleph: '\\u2135',\n\tAlpha: '\\u0391',\n\talpha: '\\u03B1',\n\tAmacr: '\\u0100',\n\tamacr: '\\u0101',\n\tamalg: '\\u2A3F',\n\tAMP: '\\u0026',\n\tamp: '\\u0026',\n\tAnd: '\\u2A53',\n\tand: '\\u2227',\n\tandand: '\\u2A55',\n\tandd: '\\u2A5C',\n\tandslope: '\\u2A58',\n\tandv: '\\u2A5A',\n\tang: '\\u2220',\n\tange: '\\u29A4',\n\tangle: '\\u2220',\n\tangmsd: '\\u2221',\n\tangmsdaa: '\\u29A8',\n\tangmsdab: '\\u29A9',\n\tangmsdac: '\\u29AA',\n\tangmsdad: '\\u29AB',\n\tangmsdae: '\\u29AC',\n\tangmsdaf: '\\u29AD',\n\tangmsdag: '\\u29AE',\n\tangmsdah: '\\u29AF',\n\tangrt: '\\u221F',\n\tangrtvb: '\\u22BE',\n\tangrtvbd: '\\u299D',\n\tangsph: '\\u2222',\n\tangst: '\\u00C5',\n\tangzarr: '\\u237C',\n\tAogon: '\\u0104',\n\taogon: '\\u0105',\n\tAopf: '\\uD835\\uDD38',\n\taopf: '\\uD835\\uDD52',\n\tap: '\\u2248',\n\tapacir: '\\u2A6F',\n\tapE: '\\u2A70',\n\tape: '\\u224A',\n\tapid: '\\u224B',\n\tapos: '\\u0027',\n\tApplyFunction: '\\u2061',\n\tapprox: '\\u2248',\n\tapproxeq: '\\u224A',\n\tAring: '\\u00C5',\n\taring: '\\u00E5',\n\tAscr: '\\uD835\\uDC9C',\n\tascr: '\\uD835\\uDCB6',\n\tAssign: '\\u2254',\n\tast: '\\u002A',\n\tasymp: '\\u2248',\n\tasympeq: '\\u224D',\n\tAtilde: '\\u00C3',\n\tatilde: '\\u00E3',\n\tAuml: '\\u00C4',\n\tauml: '\\u00E4',\n\tawconint: '\\u2233',\n\tawint: '\\u2A11',\n\tbackcong: '\\u224C',\n\tbackepsilon: '\\u03F6',\n\tbackprime: '\\u2035',\n\tbacksim: '\\u223D',\n\tbacksimeq: '\\u22CD',\n\tBackslash: '\\u2216',\n\tBarv: '\\u2AE7',\n\tbarvee: '\\u22BD',\n\tBarwed: '\\u2306',\n\tbarwed: '\\u2305',\n\tbarwedge: '\\u2305',\n\tbbrk: '\\u23B5',\n\tbbrktbrk: '\\u23B6',\n\tbcong: '\\u224C',\n\tBcy: '\\u0411',\n\tbcy: '\\u0431',\n\tbdquo: '\\u201E',\n\tbecaus: '\\u2235',\n\tBecause: '\\u2235',\n\tbecause: '\\u2235',\n\tbemptyv: '\\u29B0',\n\tbepsi: '\\u03F6',\n\tbernou: '\\u212C',\n\tBernoullis: '\\u212C',\n\tBeta: '\\u0392',\n\tbeta: '\\u03B2',\n\tbeth: '\\u2136',\n\tbetween: '\\u226C',\n\tBfr: '\\uD835\\uDD05',\n\tbfr: '\\uD835\\uDD1F',\n\tbigcap: '\\u22C2',\n\tbigcirc: '\\u25EF',\n\tbigcup: '\\u22C3',\n\tbigodot: '\\u2A00',\n\tbigoplus: '\\u2A01',\n\tbigotimes: '\\u2A02',\n\tbigsqcup: '\\u2A06',\n\tbigstar: '\\u2605',\n\tbigtriangledown: '\\u25BD',\n\tbigtriangleup: '\\u25B3',\n\tbiguplus: '\\u2A04',\n\tbigvee: '\\u22C1',\n\tbigwedge: '\\u22C0',\n\tbkarow: '\\u290D',\n\tblacklozenge: '\\u29EB',\n\tblacksquare: '\\u25AA',\n\tblacktriangle: '\\u25B4',\n\tblacktriangledown: '\\u25BE',\n\tblacktriangleleft: '\\u25C2',\n\tblacktriangleright: '\\u25B8',\n\tblank: '\\u2423',\n\tblk12: '\\u2592',\n\tblk14: '\\u2591',\n\tblk34: '\\u2593',\n\tblock: '\\u2588',\n\tbne: '\\u003D\\u20E5',\n\tbnequiv: '\\u2261\\u20E5',\n\tbNot: '\\u2AED',\n\tbnot: '\\u2310',\n\tBopf: '\\uD835\\uDD39',\n\tbopf: '\\uD835\\uDD53',\n\tbot: '\\u22A5',\n\tbottom: '\\u22A5',\n\tbowtie: '\\u22C8',\n\tboxbox: '\\u29C9',\n\tboxDL: '\\u2557',\n\tboxDl: '\\u2556',\n\tboxdL: '\\u2555',\n\tboxdl: '\\u2510',\n\tboxDR: '\\u2554',\n\tboxDr: '\\u2553',\n\tboxdR: '\\u2552',\n\tboxdr: '\\u250C',\n\tboxH: '\\u2550',\n\tboxh: '\\u2500',\n\tboxHD: '\\u2566',\n\tboxHd: '\\u2564',\n\tboxhD: '\\u2565',\n\tboxhd: '\\u252C',\n\tboxHU: '\\u2569',\n\tboxHu: '\\u2567',\n\tboxhU: '\\u2568',\n\tboxhu: '\\u2534',\n\tboxminus: '\\u229F',\n\tboxplus: '\\u229E',\n\tboxtimes: '\\u22A0',\n\tboxUL: '\\u255D',\n\tboxUl: '\\u255C',\n\tboxuL: '\\u255B',\n\tboxul: '\\u2518',\n\tboxUR: '\\u255A',\n\tboxUr: '\\u2559',\n\tboxuR: '\\u2558',\n\tboxur: '\\u2514',\n\tboxV: '\\u2551',\n\tboxv: '\\u2502',\n\tboxVH: '\\u256C',\n\tboxVh: '\\u256B',\n\tboxvH: '\\u256A',\n\tboxvh: '\\u253C',\n\tboxVL: '\\u2563',\n\tboxVl: '\\u2562',\n\tboxvL: '\\u2561',\n\tboxvl: '\\u2524',\n\tboxVR: '\\u2560',\n\tboxVr: '\\u255F',\n\tboxvR: '\\u255E',\n\tboxvr: '\\u251C',\n\tbprime: '\\u2035',\n\tBreve: '\\u02D8',\n\tbreve: '\\u02D8',\n\tbrvbar: '\\u00A6',\n\tBscr: '\\u212C',\n\tbscr: '\\uD835\\uDCB7',\n\tbsemi: '\\u204F',\n\tbsim: '\\u223D',\n\tbsime: '\\u22CD',\n\tbsol: '\\u005C',\n\tbsolb: '\\u29C5',\n\tbsolhsub: '\\u27C8',\n\tbull: '\\u2022',\n\tbullet: '\\u2022',\n\tbump: '\\u224E',\n\tbumpE: '\\u2AAE',\n\tbumpe: '\\u224F',\n\tBumpeq: '\\u224E',\n\tbumpeq: '\\u224F',\n\tCacute: '\\u0106',\n\tcacute: '\\u0107',\n\tCap: '\\u22D2',\n\tcap: '\\u2229',\n\tcapand: '\\u2A44',\n\tcapbrcup: '\\u2A49',\n\tcapcap: '\\u2A4B',\n\tcapcup: '\\u2A47',\n\tcapdot: '\\u2A40',\n\tCapitalDifferentialD: '\\u2145',\n\tcaps: '\\u2229\\uFE00',\n\tcaret: '\\u2041',\n\tcaron: '\\u02C7',\n\tCayleys: '\\u212D',\n\tccaps: '\\u2A4D',\n\tCcaron: '\\u010C',\n\tccaron: '\\u010D',\n\tCcedil: '\\u00C7',\n\tccedil: '\\u00E7',\n\tCcirc: '\\u0108',\n\tccirc: '\\u0109',\n\tCconint: '\\u2230',\n\tccups: '\\u2A4C',\n\tccupssm: '\\u2A50',\n\tCdot: '\\u010A',\n\tcdot: '\\u010B',\n\tcedil: '\\u00B8',\n\tCedilla: '\\u00B8',\n\tcemptyv: '\\u29B2',\n\tcent: '\\u00A2',\n\tCenterDot: '\\u00B7',\n\tcenterdot: '\\u00B7',\n\tCfr: '\\u212D',\n\tcfr: '\\uD835\\uDD20',\n\tCHcy: '\\u0427',\n\tchcy: '\\u0447',\n\tcheck: '\\u2713',\n\tcheckmark: '\\u2713',\n\tChi: '\\u03A7',\n\tchi: '\\u03C7',\n\tcir: '\\u25CB',\n\tcirc: '\\u02C6',\n\tcirceq: '\\u2257',\n\tcirclearrowleft: '\\u21BA',\n\tcirclearrowright: '\\u21BB',\n\tcircledast: '\\u229B',\n\tcircledcirc: '\\u229A',\n\tcircleddash: '\\u229D',\n\tCircleDot: '\\u2299',\n\tcircledR: '\\u00AE',\n\tcircledS: '\\u24C8',\n\tCircleMinus: '\\u2296',\n\tCirclePlus: '\\u2295',\n\tCircleTimes: '\\u2297',\n\tcirE: '\\u29C3',\n\tcire: '\\u2257',\n\tcirfnint: '\\u2A10',\n\tcirmid: '\\u2AEF',\n\tcirscir: '\\u29C2',\n\tClockwiseContourIntegral: '\\u2232',\n\tCloseCurlyDoubleQuote: '\\u201D',\n\tCloseCurlyQuote: '\\u2019',\n\tclubs: '\\u2663',\n\tclubsuit: '\\u2663',\n\tColon: '\\u2237',\n\tcolon: '\\u003A',\n\tColone: '\\u2A74',\n\tcolone: '\\u2254',\n\tcoloneq: '\\u2254',\n\tcomma: '\\u002C',\n\tcommat: '\\u0040',\n\tcomp: '\\u2201',\n\tcompfn: '\\u2218',\n\tcomplement: '\\u2201',\n\tcomplexes: '\\u2102',\n\tcong: '\\u2245',\n\tcongdot: '\\u2A6D',\n\tCongruent: '\\u2261',\n\tConint: '\\u222F',\n\tconint: '\\u222E',\n\tContourIntegral: '\\u222E',\n\tCopf: '\\u2102',\n\tcopf: '\\uD835\\uDD54',\n\tcoprod: '\\u2210',\n\tCoproduct: '\\u2210',\n\tCOPY: '\\u00A9',\n\tcopy: '\\u00A9',\n\tcopysr: '\\u2117',\n\tCounterClockwiseContourIntegral: '\\u2233',\n\tcrarr: '\\u21B5',\n\tCross: '\\u2A2F',\n\tcross: '\\u2717',\n\tCscr: '\\uD835\\uDC9E',\n\tcscr: '\\uD835\\uDCB8',\n\tcsub: '\\u2ACF',\n\tcsube: '\\u2AD1',\n\tcsup: '\\u2AD0',\n\tcsupe: '\\u2AD2',\n\tctdot: '\\u22EF',\n\tcudarrl: '\\u2938',\n\tcudarrr: '\\u2935',\n\tcuepr: '\\u22DE',\n\tcuesc: '\\u22DF',\n\tcularr: '\\u21B6',\n\tcularrp: '\\u293D',\n\tCup: '\\u22D3',\n\tcup: '\\u222A',\n\tcupbrcap: '\\u2A48',\n\tCupCap: '\\u224D',\n\tcupcap: '\\u2A46',\n\tcupcup: '\\u2A4A',\n\tcupdot: '\\u228D',\n\tcupor: '\\u2A45',\n\tcups: '\\u222A\\uFE00',\n\tcurarr: '\\u21B7',\n\tcurarrm: '\\u293C',\n\tcurlyeqprec: '\\u22DE',\n\tcurlyeqsucc: '\\u22DF',\n\tcurlyvee: '\\u22CE',\n\tcurlywedge: '\\u22CF',\n\tcurren: '\\u00A4',\n\tcurvearrowleft: '\\u21B6',\n\tcurvearrowright: '\\u21B7',\n\tcuvee: '\\u22CE',\n\tcuwed: '\\u22CF',\n\tcwconint: '\\u2232',\n\tcwint: '\\u2231',\n\tcylcty: '\\u232D',\n\tDagger: '\\u2021',\n\tdagger: '\\u2020',\n\tdaleth: '\\u2138',\n\tDarr: '\\u21A1',\n\tdArr: '\\u21D3',\n\tdarr: '\\u2193',\n\tdash: '\\u2010',\n\tDashv: '\\u2AE4',\n\tdashv: '\\u22A3',\n\tdbkarow: '\\u290F',\n\tdblac: '\\u02DD',\n\tDcaron: '\\u010E',\n\tdcaron: '\\u010F',\n\tDcy: '\\u0414',\n\tdcy: '\\u0434',\n\tDD: '\\u2145',\n\tdd: '\\u2146',\n\tddagger: '\\u2021',\n\tddarr: '\\u21CA',\n\tDDotrahd: '\\u2911',\n\tddotseq: '\\u2A77',\n\tdeg: '\\u00B0',\n\tDel: '\\u2207',\n\tDelta: '\\u0394',\n\tdelta: '\\u03B4',\n\tdemptyv: '\\u29B1',\n\tdfisht: '\\u297F',\n\tDfr: '\\uD835\\uDD07',\n\tdfr: '\\uD835\\uDD21',\n\tdHar: '\\u2965',\n\tdharl: '\\u21C3',\n\tdharr: '\\u21C2',\n\tDiacriticalAcute: '\\u00B4',\n\tDiacriticalDot: '\\u02D9',\n\tDiacriticalDoubleAcute: '\\u02DD',\n\tDiacriticalGrave: '\\u0060',\n\tDiacriticalTilde: '\\u02DC',\n\tdiam: '\\u22C4',\n\tDiamond: '\\u22C4',\n\tdiamond: '\\u22C4',\n\tdiamondsuit: '\\u2666',\n\tdiams: '\\u2666',\n\tdie: '\\u00A8',\n\tDifferentialD: '\\u2146',\n\tdigamma: '\\u03DD',\n\tdisin: '\\u22F2',\n\tdiv: '\\u00F7',\n\tdivide: '\\u00F7',\n\tdivideontimes: '\\u22C7',\n\tdivonx: '\\u22C7',\n\tDJcy: '\\u0402',\n\tdjcy: '\\u0452',\n\tdlcorn: '\\u231E',\n\tdlcrop: '\\u230D',\n\tdollar: '\\u0024',\n\tDopf: '\\uD835\\uDD3B',\n\tdopf: '\\uD835\\uDD55',\n\tDot: '\\u00A8',\n\tdot: '\\u02D9',\n\tDotDot: '\\u20DC',\n\tdoteq: '\\u2250',\n\tdoteqdot: '\\u2251',\n\tDotEqual: '\\u2250',\n\tdotminus: '\\u2238',\n\tdotplus: '\\u2214',\n\tdotsquare: '\\u22A1',\n\tdoublebarwedge: '\\u2306',\n\tDoubleContourIntegral: '\\u222F',\n\tDoubleDot: '\\u00A8',\n\tDoubleDownArrow: '\\u21D3',\n\tDoubleLeftArrow: '\\u21D0',\n\tDoubleLeftRightArrow: '\\u21D4',\n\tDoubleLeftTee: '\\u2AE4',\n\tDoubleLongLeftArrow: '\\u27F8',\n\tDoubleLongLeftRightArrow: '\\u27FA',\n\tDoubleLongRightArrow: '\\u27F9',\n\tDoubleRightArrow: '\\u21D2',\n\tDoubleRightTee: '\\u22A8',\n\tDoubleUpArrow: '\\u21D1',\n\tDoubleUpDownArrow: '\\u21D5',\n\tDoubleVerticalBar: '\\u2225',\n\tDownArrow: '\\u2193',\n\tDownarrow: '\\u21D3',\n\tdownarrow: '\\u2193',\n\tDownArrowBar: '\\u2913',\n\tDownArrowUpArrow: '\\u21F5',\n\tDownBreve: '\\u0311',\n\tdowndownarrows: '\\u21CA',\n\tdownharpoonleft: '\\u21C3',\n\tdownharpoonright: '\\u21C2',\n\tDownLeftRightVector: '\\u2950',\n\tDownLeftTeeVector: '\\u295E',\n\tDownLeftVector: '\\u21BD',\n\tDownLeftVectorBar: '\\u2956',\n\tDownRightTeeVector: '\\u295F',\n\tDownRightVector: '\\u21C1',\n\tDownRightVectorBar: '\\u2957',\n\tDownTee: '\\u22A4',\n\tDownTeeArrow: '\\u21A7',\n\tdrbkarow: '\\u2910',\n\tdrcorn: '\\u231F',\n\tdrcrop: '\\u230C',\n\tDscr: '\\uD835\\uDC9F',\n\tdscr: '\\uD835\\uDCB9',\n\tDScy: '\\u0405',\n\tdscy: '\\u0455',\n\tdsol: '\\u29F6',\n\tDstrok: '\\u0110',\n\tdstrok: '\\u0111',\n\tdtdot: '\\u22F1',\n\tdtri: '\\u25BF',\n\tdtrif: '\\u25BE',\n\tduarr: '\\u21F5',\n\tduhar: '\\u296F',\n\tdwangle: '\\u29A6',\n\tDZcy: '\\u040F',\n\tdzcy: '\\u045F',\n\tdzigrarr: '\\u27FF',\n\tEacute: '\\u00C9',\n\teacute: '\\u00E9',\n\teaster: '\\u2A6E',\n\tEcaron: '\\u011A',\n\tecaron: '\\u011B',\n\tecir: '\\u2256',\n\tEcirc: '\\u00CA',\n\tecirc: '\\u00EA',\n\tecolon: '\\u2255',\n\tEcy: '\\u042D',\n\tecy: '\\u044D',\n\teDDot: '\\u2A77',\n\tEdot: '\\u0116',\n\teDot: '\\u2251',\n\tedot: '\\u0117',\n\tee: '\\u2147',\n\tefDot: '\\u2252',\n\tEfr: '\\uD835\\uDD08',\n\tefr: '\\uD835\\uDD22',\n\teg: '\\u2A9A',\n\tEgrave: '\\u00C8',\n\tegrave: '\\u00E8',\n\tegs: '\\u2A96',\n\tegsdot: '\\u2A98',\n\tel: '\\u2A99',\n\tElement: '\\u2208',\n\telinters: '\\u23E7',\n\tell: '\\u2113',\n\tels: '\\u2A95',\n\telsdot: '\\u2A97',\n\tEmacr: '\\u0112',\n\temacr: '\\u0113',\n\tempty: '\\u2205',\n\temptyset: '\\u2205',\n\tEmptySmallSquare: '\\u25FB',\n\temptyv: '\\u2205',\n\tEmptyVerySmallSquare: '\\u25AB',\n\temsp: '\\u2003',\n\temsp13: '\\u2004',\n\temsp14: '\\u2005',\n\tENG: '\\u014A',\n\teng: '\\u014B',\n\tensp: '\\u2002',\n\tEogon: '\\u0118',\n\teogon: '\\u0119',\n\tEopf: '\\uD835\\uDD3C',\n\teopf: '\\uD835\\uDD56',\n\tepar: '\\u22D5',\n\teparsl: '\\u29E3',\n\teplus: '\\u2A71',\n\tepsi: '\\u03B5',\n\tEpsilon: '\\u0395',\n\tepsilon: '\\u03B5',\n\tepsiv: '\\u03F5',\n\teqcirc: '\\u2256',\n\teqcolon: '\\u2255',\n\teqsim: '\\u2242',\n\teqslantgtr: '\\u2A96',\n\teqslantless: '\\u2A95',\n\tEqual: '\\u2A75',\n\tequals: '\\u003D',\n\tEqualTilde: '\\u2242',\n\tequest: '\\u225F',\n\tEquilibrium: '\\u21CC',\n\tequiv: '\\u2261',\n\tequivDD: '\\u2A78',\n\teqvparsl: '\\u29E5',\n\terarr: '\\u2971',\n\terDot: '\\u2253',\n\tEscr: '\\u2130',\n\tescr: '\\u212F',\n\tesdot: '\\u2250',\n\tEsim: '\\u2A73',\n\tesim: '\\u2242',\n\tEta: '\\u0397',\n\teta: '\\u03B7',\n\tETH: '\\u00D0',\n\teth: '\\u00F0',\n\tEuml: '\\u00CB',\n\teuml: '\\u00EB',\n\teuro: '\\u20AC',\n\texcl: '\\u0021',\n\texist: '\\u2203',\n\tExists: '\\u2203',\n\texpectation: '\\u2130',\n\tExponentialE: '\\u2147',\n\texponentiale: '\\u2147',\n\tfallingdotseq: '\\u2252',\n\tFcy: '\\u0424',\n\tfcy: '\\u0444',\n\tfemale: '\\u2640',\n\tffilig: '\\uFB03',\n\tfflig: '\\uFB00',\n\tffllig: '\\uFB04',\n\tFfr: '\\uD835\\uDD09',\n\tffr: '\\uD835\\uDD23',\n\tfilig: '\\uFB01',\n\tFilledSmallSquare: '\\u25FC',\n\tFilledVerySmallSquare: '\\u25AA',\n\tfjlig: '\\u0066\\u006A',\n\tflat: '\\u266D',\n\tfllig: '\\uFB02',\n\tfltns: '\\u25B1',\n\tfnof: '\\u0192',\n\tFopf: '\\uD835\\uDD3D',\n\tfopf: '\\uD835\\uDD57',\n\tForAll: '\\u2200',\n\tforall: '\\u2200',\n\tfork: '\\u22D4',\n\tforkv: '\\u2AD9',\n\tFouriertrf: '\\u2131',\n\tfpartint: '\\u2A0D',\n\tfrac12: '\\u00BD',\n\tfrac13: '\\u2153',\n\tfrac14: '\\u00BC',\n\tfrac15: '\\u2155',\n\tfrac16: '\\u2159',\n\tfrac18: '\\u215B',\n\tfrac23: '\\u2154',\n\tfrac25: '\\u2156',\n\tfrac34: '\\u00BE',\n\tfrac35: '\\u2157',\n\tfrac38: '\\u215C',\n\tfrac45: '\\u2158',\n\tfrac56: '\\u215A',\n\tfrac58: '\\u215D',\n\tfrac78: '\\u215E',\n\tfrasl: '\\u2044',\n\tfrown: '\\u2322',\n\tFscr: '\\u2131',\n\tfscr: '\\uD835\\uDCBB',\n\tgacute: '\\u01F5',\n\tGamma: '\\u0393',\n\tgamma: '\\u03B3',\n\tGammad: '\\u03DC',\n\tgammad: '\\u03DD',\n\tgap: '\\u2A86',\n\tGbreve: '\\u011E',\n\tgbreve: '\\u011F',\n\tGcedil: '\\u0122',\n\tGcirc: '\\u011C',\n\tgcirc: '\\u011D',\n\tGcy: '\\u0413',\n\tgcy: '\\u0433',\n\tGdot: '\\u0120',\n\tgdot: '\\u0121',\n\tgE: '\\u2267',\n\tge: '\\u2265',\n\tgEl: '\\u2A8C',\n\tgel: '\\u22DB',\n\tgeq: '\\u2265',\n\tgeqq: '\\u2267',\n\tgeqslant: '\\u2A7E',\n\tges: '\\u2A7E',\n\tgescc: '\\u2AA9',\n\tgesdot: '\\u2A80',\n\tgesdoto: '\\u2A82',\n\tgesdotol: '\\u2A84',\n\tgesl: '\\u22DB\\uFE00',\n\tgesles: '\\u2A94',\n\tGfr: '\\uD835\\uDD0A',\n\tgfr: '\\uD835\\uDD24',\n\tGg: '\\u22D9',\n\tgg: '\\u226B',\n\tggg: '\\u22D9',\n\tgimel: '\\u2137',\n\tGJcy: '\\u0403',\n\tgjcy: '\\u0453',\n\tgl: '\\u2277',\n\tgla: '\\u2AA5',\n\tglE: '\\u2A92',\n\tglj: '\\u2AA4',\n\tgnap: '\\u2A8A',\n\tgnapprox: '\\u2A8A',\n\tgnE: '\\u2269',\n\tgne: '\\u2A88',\n\tgneq: '\\u2A88',\n\tgneqq: '\\u2269',\n\tgnsim: '\\u22E7',\n\tGopf: '\\uD835\\uDD3E',\n\tgopf: '\\uD835\\uDD58',\n\tgrave: '\\u0060',\n\tGreaterEqual: '\\u2265',\n\tGreaterEqualLess: '\\u22DB',\n\tGreaterFullEqual: '\\u2267',\n\tGreaterGreater: '\\u2AA2',\n\tGreaterLess: '\\u2277',\n\tGreaterSlantEqual: '\\u2A7E',\n\tGreaterTilde: '\\u2273',\n\tGscr: '\\uD835\\uDCA2',\n\tgscr: '\\u210A',\n\tgsim: '\\u2273',\n\tgsime: '\\u2A8E',\n\tgsiml: '\\u2A90',\n\tGt: '\\u226B',\n\tGT: '\\u003E',\n\tgt: '\\u003E',\n\tgtcc: '\\u2AA7',\n\tgtcir: '\\u2A7A',\n\tgtdot: '\\u22D7',\n\tgtlPar: '\\u2995',\n\tgtquest: '\\u2A7C',\n\tgtrapprox: '\\u2A86',\n\tgtrarr: '\\u2978',\n\tgtrdot: '\\u22D7',\n\tgtreqless: '\\u22DB',\n\tgtreqqless: '\\u2A8C',\n\tgtrless: '\\u2277',\n\tgtrsim: '\\u2273',\n\tgvertneqq: '\\u2269\\uFE00',\n\tgvnE: '\\u2269\\uFE00',\n\tHacek: '\\u02C7',\n\thairsp: '\\u200A',\n\thalf: '\\u00BD',\n\thamilt: '\\u210B',\n\tHARDcy: '\\u042A',\n\thardcy: '\\u044A',\n\thArr: '\\u21D4',\n\tharr: '\\u2194',\n\tharrcir: '\\u2948',\n\tharrw: '\\u21AD',\n\tHat: '\\u005E',\n\thbar: '\\u210F',\n\tHcirc: '\\u0124',\n\thcirc: '\\u0125',\n\thearts: '\\u2665',\n\theartsuit: '\\u2665',\n\thellip: '\\u2026',\n\thercon: '\\u22B9',\n\tHfr: '\\u210C',\n\thfr: '\\uD835\\uDD25',\n\tHilbertSpace: '\\u210B',\n\thksearow: '\\u2925',\n\thkswarow: '\\u2926',\n\thoarr: '\\u21FF',\n\thomtht: '\\u223B',\n\thookleftarrow: '\\u21A9',\n\thookrightarrow: '\\u21AA',\n\tHopf: '\\u210D',\n\thopf: '\\uD835\\uDD59',\n\thorbar: '\\u2015',\n\tHorizontalLine: '\\u2500',\n\tHscr: '\\u210B',\n\thscr: '\\uD835\\uDCBD',\n\thslash: '\\u210F',\n\tHstrok: '\\u0126',\n\thstrok: '\\u0127',\n\tHumpDownHump: '\\u224E',\n\tHumpEqual: '\\u224F',\n\thybull: '\\u2043',\n\thyphen: '\\u2010',\n\tIacute: '\\u00CD',\n\tiacute: '\\u00ED',\n\tic: '\\u2063',\n\tIcirc: '\\u00CE',\n\ticirc: '\\u00EE',\n\tIcy: '\\u0418',\n\ticy: '\\u0438',\n\tIdot: '\\u0130',\n\tIEcy: '\\u0415',\n\tiecy: '\\u0435',\n\tiexcl: '\\u00A1',\n\tiff: '\\u21D4',\n\tIfr: '\\u2111',\n\tifr: '\\uD835\\uDD26',\n\tIgrave: '\\u00CC',\n\tigrave: '\\u00EC',\n\tii: '\\u2148',\n\tiiiint: '\\u2A0C',\n\tiiint: '\\u222D',\n\tiinfin: '\\u29DC',\n\tiiota: '\\u2129',\n\tIJlig: '\\u0132',\n\tijlig: '\\u0133',\n\tIm: '\\u2111',\n\tImacr: '\\u012A',\n\timacr: '\\u012B',\n\timage: '\\u2111',\n\tImaginaryI: '\\u2148',\n\timagline: '\\u2110',\n\timagpart: '\\u2111',\n\timath: '\\u0131',\n\timof: '\\u22B7',\n\timped: '\\u01B5',\n\tImplies: '\\u21D2',\n\tin: '\\u2208',\n\tincare: '\\u2105',\n\tinfin: '\\u221E',\n\tinfintie: '\\u29DD',\n\tinodot: '\\u0131',\n\tInt: '\\u222C',\n\tint: '\\u222B',\n\tintcal: '\\u22BA',\n\tintegers: '\\u2124',\n\tIntegral: '\\u222B',\n\tintercal: '\\u22BA',\n\tIntersection: '\\u22C2',\n\tintlarhk: '\\u2A17',\n\tintprod: '\\u2A3C',\n\tInvisibleComma: '\\u2063',\n\tInvisibleTimes: '\\u2062',\n\tIOcy: '\\u0401',\n\tiocy: '\\u0451',\n\tIogon: '\\u012E',\n\tiogon: '\\u012F',\n\tIopf: '\\uD835\\uDD40',\n\tiopf: '\\uD835\\uDD5A',\n\tIota: '\\u0399',\n\tiota: '\\u03B9',\n\tiprod: '\\u2A3C',\n\tiquest: '\\u00BF',\n\tIscr: '\\u2110',\n\tiscr: '\\uD835\\uDCBE',\n\tisin: '\\u2208',\n\tisindot: '\\u22F5',\n\tisinE: '\\u22F9',\n\tisins: '\\u22F4',\n\tisinsv: '\\u22F3',\n\tisinv: '\\u2208',\n\tit: '\\u2062',\n\tItilde: '\\u0128',\n\titilde: '\\u0129',\n\tIukcy: '\\u0406',\n\tiukcy: '\\u0456',\n\tIuml: '\\u00CF',\n\tiuml: '\\u00EF',\n\tJcirc: '\\u0134',\n\tjcirc: '\\u0135',\n\tJcy: '\\u0419',\n\tjcy: '\\u0439',\n\tJfr: '\\uD835\\uDD0D',\n\tjfr: '\\uD835\\uDD27',\n\tjmath: '\\u0237',\n\tJopf: '\\uD835\\uDD41',\n\tjopf: '\\uD835\\uDD5B',\n\tJscr: '\\uD835\\uDCA5',\n\tjscr: '\\uD835\\uDCBF',\n\tJsercy: '\\u0408',\n\tjsercy: '\\u0458',\n\tJukcy: '\\u0404',\n\tjukcy: '\\u0454',\n\tKappa: '\\u039A',\n\tkappa: '\\u03BA',\n\tkappav: '\\u03F0',\n\tKcedil: '\\u0136',\n\tkcedil: '\\u0137',\n\tKcy: '\\u041A',\n\tkcy: '\\u043A',\n\tKfr: '\\uD835\\uDD0E',\n\tkfr: '\\uD835\\uDD28',\n\tkgreen: '\\u0138',\n\tKHcy: '\\u0425',\n\tkhcy: '\\u0445',\n\tKJcy: '\\u040C',\n\tkjcy: '\\u045C',\n\tKopf: '\\uD835\\uDD42',\n\tkopf: '\\uD835\\uDD5C',\n\tKscr: '\\uD835\\uDCA6',\n\tkscr: '\\uD835\\uDCC0',\n\tlAarr: '\\u21DA',\n\tLacute: '\\u0139',\n\tlacute: '\\u013A',\n\tlaemptyv: '\\u29B4',\n\tlagran: '\\u2112',\n\tLambda: '\\u039B',\n\tlambda: '\\u03BB',\n\tLang: '\\u27EA',\n\tlang: '\\u27E8',\n\tlangd: '\\u2991',\n\tlangle: '\\u27E8',\n\tlap: '\\u2A85',\n\tLaplacetrf: '\\u2112',\n\tlaquo: '\\u00AB',\n\tLarr: '\\u219E',\n\tlArr: '\\u21D0',\n\tlarr: '\\u2190',\n\tlarrb: '\\u21E4',\n\tlarrbfs: '\\u291F',\n\tlarrfs: '\\u291D',\n\tlarrhk: '\\u21A9',\n\tlarrlp: '\\u21AB',\n\tlarrpl: '\\u2939',\n\tlarrsim: '\\u2973',\n\tlarrtl: '\\u21A2',\n\tlat: '\\u2AAB',\n\tlAtail: '\\u291B',\n\tlatail: '\\u2919',\n\tlate: '\\u2AAD',\n\tlates: '\\u2AAD\\uFE00',\n\tlBarr: '\\u290E',\n\tlbarr: '\\u290C',\n\tlbbrk: '\\u2772',\n\tlbrace: '\\u007B',\n\tlbrack: '\\u005B',\n\tlbrke: '\\u298B',\n\tlbrksld: '\\u298F',\n\tlbrkslu: '\\u298D',\n\tLcaron: '\\u013D',\n\tlcaron: '\\u013E',\n\tLcedil: '\\u013B',\n\tlcedil: '\\u013C',\n\tlceil: '\\u2308',\n\tlcub: '\\u007B',\n\tLcy: '\\u041B',\n\tlcy: '\\u043B',\n\tldca: '\\u2936',\n\tldquo: '\\u201C',\n\tldquor: '\\u201E',\n\tldrdhar: '\\u2967',\n\tldrushar: '\\u294B',\n\tldsh: '\\u21B2',\n\tlE: '\\u2266',\n\tle: '\\u2264',\n\tLeftAngleBracket: '\\u27E8',\n\tLeftArrow: '\\u2190',\n\tLeftarrow: '\\u21D0',\n\tleftarrow: '\\u2190',\n\tLeftArrowBar: '\\u21E4',\n\tLeftArrowRightArrow: '\\u21C6',\n\tleftarrowtail: '\\u21A2',\n\tLeftCeiling: '\\u2308',\n\tLeftDoubleBracket: '\\u27E6',\n\tLeftDownTeeVector: '\\u2961',\n\tLeftDownVector: '\\u21C3',\n\tLeftDownVectorBar: '\\u2959',\n\tLeftFloor: '\\u230A',\n\tleftharpoondown: '\\u21BD',\n\tleftharpoonup: '\\u21BC',\n\tleftleftarrows: '\\u21C7',\n\tLeftRightArrow: '\\u2194',\n\tLeftrightarrow: '\\u21D4',\n\tleftrightarrow: '\\u2194',\n\tleftrightarrows: '\\u21C6',\n\tleftrightharpoons: '\\u21CB',\n\tleftrightsquigarrow: '\\u21AD',\n\tLeftRightVector: '\\u294E',\n\tLeftTee: '\\u22A3',\n\tLeftTeeArrow: '\\u21A4',\n\tLeftTeeVector: '\\u295A',\n\tleftthreetimes: '\\u22CB',\n\tLeftTriangle: '\\u22B2',\n\tLeftTriangleBar: '\\u29CF',\n\tLeftTriangleEqual: '\\u22B4',\n\tLeftUpDownVector: '\\u2951',\n\tLeftUpTeeVector: '\\u2960',\n\tLeftUpVector: '\\u21BF',\n\tLeftUpVectorBar: '\\u2958',\n\tLeftVector: '\\u21BC',\n\tLeftVectorBar: '\\u2952',\n\tlEg: '\\u2A8B',\n\tleg: '\\u22DA',\n\tleq: '\\u2264',\n\tleqq: '\\u2266',\n\tleqslant: '\\u2A7D',\n\tles: '\\u2A7D',\n\tlescc: '\\u2AA8',\n\tlesdot: '\\u2A7F',\n\tlesdoto: '\\u2A81',\n\tlesdotor: '\\u2A83',\n\tlesg: '\\u22DA\\uFE00',\n\tlesges: '\\u2A93',\n\tlessapprox: '\\u2A85',\n\tlessdot: '\\u22D6',\n\tlesseqgtr: '\\u22DA',\n\tlesseqqgtr: '\\u2A8B',\n\tLessEqualGreater: '\\u22DA',\n\tLessFullEqual: '\\u2266',\n\tLessGreater: '\\u2276',\n\tlessgtr: '\\u2276',\n\tLessLess: '\\u2AA1',\n\tlesssim: '\\u2272',\n\tLessSlantEqual: '\\u2A7D',\n\tLessTilde: '\\u2272',\n\tlfisht: '\\u297C',\n\tlfloor: '\\u230A',\n\tLfr: '\\uD835\\uDD0F',\n\tlfr: '\\uD835\\uDD29',\n\tlg: '\\u2276',\n\tlgE: '\\u2A91',\n\tlHar: '\\u2962',\n\tlhard: '\\u21BD',\n\tlharu: '\\u21BC',\n\tlharul: '\\u296A',\n\tlhblk: '\\u2584',\n\tLJcy: '\\u0409',\n\tljcy: '\\u0459',\n\tLl: '\\u22D8',\n\tll: '\\u226A',\n\tllarr: '\\u21C7',\n\tllcorner: '\\u231E',\n\tLleftarrow: '\\u21DA',\n\tllhard: '\\u296B',\n\tlltri: '\\u25FA',\n\tLmidot: '\\u013F',\n\tlmidot: '\\u0140',\n\tlmoust: '\\u23B0',\n\tlmoustache: '\\u23B0',\n\tlnap: '\\u2A89',\n\tlnapprox: '\\u2A89',\n\tlnE: '\\u2268',\n\tlne: '\\u2A87',\n\tlneq: '\\u2A87',\n\tlneqq: '\\u2268',\n\tlnsim: '\\u22E6',\n\tloang: '\\u27EC',\n\tloarr: '\\u21FD',\n\tlobrk: '\\u27E6',\n\tLongLeftArrow: '\\u27F5',\n\tLongleftarrow: '\\u27F8',\n\tlongleftarrow: '\\u27F5',\n\tLongLeftRightArrow: '\\u27F7',\n\tLongleftrightarrow: '\\u27FA',\n\tlongleftrightarrow: '\\u27F7',\n\tlongmapsto: '\\u27FC',\n\tLongRightArrow: '\\u27F6',\n\tLongrightarrow: '\\u27F9',\n\tlongrightarrow: '\\u27F6',\n\tlooparrowleft: '\\u21AB',\n\tlooparrowright: '\\u21AC',\n\tlopar: '\\u2985',\n\tLopf: '\\uD835\\uDD43',\n\tlopf: '\\uD835\\uDD5D',\n\tloplus: '\\u2A2D',\n\tlotimes: '\\u2A34',\n\tlowast: '\\u2217',\n\tlowbar: '\\u005F',\n\tLowerLeftArrow: '\\u2199',\n\tLowerRightArrow: '\\u2198',\n\tloz: '\\u25CA',\n\tlozenge: '\\u25CA',\n\tlozf: '\\u29EB',\n\tlpar: '\\u0028',\n\tlparlt: '\\u2993',\n\tlrarr: '\\u21C6',\n\tlrcorner: '\\u231F',\n\tlrhar: '\\u21CB',\n\tlrhard: '\\u296D',\n\tlrm: '\\u200E',\n\tlrtri: '\\u22BF',\n\tlsaquo: '\\u2039',\n\tLscr: '\\u2112',\n\tlscr: '\\uD835\\uDCC1',\n\tLsh: '\\u21B0',\n\tlsh: '\\u21B0',\n\tlsim: '\\u2272',\n\tlsime: '\\u2A8D',\n\tlsimg: '\\u2A8F',\n\tlsqb: '\\u005B',\n\tlsquo: '\\u2018',\n\tlsquor: '\\u201A',\n\tLstrok: '\\u0141',\n\tlstrok: '\\u0142',\n\tLt: '\\u226A',\n\tLT: '\\u003C',\n\tlt: '\\u003C',\n\tltcc: '\\u2AA6',\n\tltcir: '\\u2A79',\n\tltdot: '\\u22D6',\n\tlthree: '\\u22CB',\n\tltimes: '\\u22C9',\n\tltlarr: '\\u2976',\n\tltquest: '\\u2A7B',\n\tltri: '\\u25C3',\n\tltrie: '\\u22B4',\n\tltrif: '\\u25C2',\n\tltrPar: '\\u2996',\n\tlurdshar: '\\u294A',\n\tluruhar: '\\u2966',\n\tlvertneqq: '\\u2268\\uFE00',\n\tlvnE: '\\u2268\\uFE00',\n\tmacr: '\\u00AF',\n\tmale: '\\u2642',\n\tmalt: '\\u2720',\n\tmaltese: '\\u2720',\n\tMap: '\\u2905',\n\tmap: '\\u21A6',\n\tmapsto: '\\u21A6',\n\tmapstodown: '\\u21A7',\n\tmapstoleft: '\\u21A4',\n\tmapstoup: '\\u21A5',\n\tmarker: '\\u25AE',\n\tmcomma: '\\u2A29',\n\tMcy: '\\u041C',\n\tmcy: '\\u043C',\n\tmdash: '\\u2014',\n\tmDDot: '\\u223A',\n\tmeasuredangle: '\\u2221',\n\tMediumSpace: '\\u205F',\n\tMellintrf: '\\u2133',\n\tMfr: '\\uD835\\uDD10',\n\tmfr: '\\uD835\\uDD2A',\n\tmho: '\\u2127',\n\tmicro: '\\u00B5',\n\tmid: '\\u2223',\n\tmidast: '\\u002A',\n\tmidcir: '\\u2AF0',\n\tmiddot: '\\u00B7',\n\tminus: '\\u2212',\n\tminusb: '\\u229F',\n\tminusd: '\\u2238',\n\tminusdu: '\\u2A2A',\n\tMinusPlus: '\\u2213',\n\tmlcp: '\\u2ADB',\n\tmldr: '\\u2026',\n\tmnplus: '\\u2213',\n\tmodels: '\\u22A7',\n\tMopf: '\\uD835\\uDD44',\n\tmopf: '\\uD835\\uDD5E',\n\tmp: '\\u2213',\n\tMscr: '\\u2133',\n\tmscr: '\\uD835\\uDCC2',\n\tmstpos: '\\u223E',\n\tMu: '\\u039C',\n\tmu: '\\u03BC',\n\tmultimap: '\\u22B8',\n\tmumap: '\\u22B8',\n\tnabla: '\\u2207',\n\tNacute: '\\u0143',\n\tnacute: '\\u0144',\n\tnang: '\\u2220\\u20D2',\n\tnap: '\\u2249',\n\tnapE: '\\u2A70\\u0338',\n\tnapid: '\\u224B\\u0338',\n\tnapos: '\\u0149',\n\tnapprox: '\\u2249',\n\tnatur: '\\u266E',\n\tnatural: '\\u266E',\n\tnaturals: '\\u2115',\n\tnbsp: '\\u00A0',\n\tnbump: '\\u224E\\u0338',\n\tnbumpe: '\\u224F\\u0338',\n\tncap: '\\u2A43',\n\tNcaron: '\\u0147',\n\tncaron: '\\u0148',\n\tNcedil: '\\u0145',\n\tncedil: '\\u0146',\n\tncong: '\\u2247',\n\tncongdot: '\\u2A6D\\u0338',\n\tncup: '\\u2A42',\n\tNcy: '\\u041D',\n\tncy: '\\u043D',\n\tndash: '\\u2013',\n\tne: '\\u2260',\n\tnearhk: '\\u2924',\n\tneArr: '\\u21D7',\n\tnearr: '\\u2197',\n\tnearrow: '\\u2197',\n\tnedot: '\\u2250\\u0338',\n\tNegativeMediumSpace: '\\u200B',\n\tNegativeThickSpace: '\\u200B',\n\tNegativeThinSpace: '\\u200B',\n\tNegativeVeryThinSpace: '\\u200B',\n\tnequiv: '\\u2262',\n\tnesear: '\\u2928',\n\tnesim: '\\u2242\\u0338',\n\tNestedGreaterGreater: '\\u226B',\n\tNestedLessLess: '\\u226A',\n\tNewLine: '\\u000A',\n\tnexist: '\\u2204',\n\tnexists: '\\u2204',\n\tNfr: '\\uD835\\uDD11',\n\tnfr: '\\uD835\\uDD2B',\n\tngE: '\\u2267\\u0338',\n\tnge: '\\u2271',\n\tngeq: '\\u2271',\n\tngeqq: '\\u2267\\u0338',\n\tngeqslant: '\\u2A7E\\u0338',\n\tnges: '\\u2A7E\\u0338',\n\tnGg: '\\u22D9\\u0338',\n\tngsim: '\\u2275',\n\tnGt: '\\u226B\\u20D2',\n\tngt: '\\u226F',\n\tngtr: '\\u226F',\n\tnGtv: '\\u226B\\u0338',\n\tnhArr: '\\u21CE',\n\tnharr: '\\u21AE',\n\tnhpar: '\\u2AF2',\n\tni: '\\u220B',\n\tnis: '\\u22FC',\n\tnisd: '\\u22FA',\n\tniv: '\\u220B',\n\tNJcy: '\\u040A',\n\tnjcy: '\\u045A',\n\tnlArr: '\\u21CD',\n\tnlarr: '\\u219A',\n\tnldr: '\\u2025',\n\tnlE: '\\u2266\\u0338',\n\tnle: '\\u2270',\n\tnLeftarrow: '\\u21CD',\n\tnleftarrow: '\\u219A',\n\tnLeftrightarrow: '\\u21CE',\n\tnleftrightarrow: '\\u21AE',\n\tnleq: '\\u2270',\n\tnleqq: '\\u2266\\u0338',\n\tnleqslant: '\\u2A7D\\u0338',\n\tnles: '\\u2A7D\\u0338',\n\tnless: '\\u226E',\n\tnLl: '\\u22D8\\u0338',\n\tnlsim: '\\u2274',\n\tnLt: '\\u226A\\u20D2',\n\tnlt: '\\u226E',\n\tnltri: '\\u22EA',\n\tnltrie: '\\u22EC',\n\tnLtv: '\\u226A\\u0338',\n\tnmid: '\\u2224',\n\tNoBreak: '\\u2060',\n\tNonBreakingSpace: '\\u00A0',\n\tNopf: '\\u2115',\n\tnopf: '\\uD835\\uDD5F',\n\tNot: '\\u2AEC',\n\tnot: '\\u00AC',\n\tNotCongruent: '\\u2262',\n\tNotCupCap: '\\u226D',\n\tNotDoubleVerticalBar: '\\u2226',\n\tNotElement: '\\u2209',\n\tNotEqual: '\\u2260',\n\tNotEqualTilde: '\\u2242\\u0338',\n\tNotExists: '\\u2204',\n\tNotGreater: '\\u226F',\n\tNotGreaterEqual: '\\u2271',\n\tNotGreaterFullEqual: '\\u2267\\u0338',\n\tNotGreaterGreater: '\\u226B\\u0338',\n\tNotGreaterLess: '\\u2279',\n\tNotGreaterSlantEqual: '\\u2A7E\\u0338',\n\tNotGreaterTilde: '\\u2275',\n\tNotHumpDownHump: '\\u224E\\u0338',\n\tNotHumpEqual: '\\u224F\\u0338',\n\tnotin: '\\u2209',\n\tnotindot: '\\u22F5\\u0338',\n\tnotinE: '\\u22F9\\u0338',\n\tnotinva: '\\u2209',\n\tnotinvb: '\\u22F7',\n\tnotinvc: '\\u22F6',\n\tNotLeftTriangle: '\\u22EA',\n\tNotLeftTriangleBar: '\\u29CF\\u0338',\n\tNotLeftTriangleEqual: '\\u22EC',\n\tNotLess: '\\u226E',\n\tNotLessEqual: '\\u2270',\n\tNotLessGreater: '\\u2278',\n\tNotLessLess: '\\u226A\\u0338',\n\tNotLessSlantEqual: '\\u2A7D\\u0338',\n\tNotLessTilde: '\\u2274',\n\tNotNestedGreaterGreater: '\\u2AA2\\u0338',\n\tNotNestedLessLess: '\\u2AA1\\u0338',\n\tnotni: '\\u220C',\n\tnotniva: '\\u220C',\n\tnotnivb: '\\u22FE',\n\tnotnivc: '\\u22FD',\n\tNotPrecedes: '\\u2280',\n\tNotPrecedesEqual: '\\u2AAF\\u0338',\n\tNotPrecedesSlantEqual: '\\u22E0',\n\tNotReverseElement: '\\u220C',\n\tNotRightTriangle: '\\u22EB',\n\tNotRightTriangleBar: '\\u29D0\\u0338',\n\tNotRightTriangleEqual: '\\u22ED',\n\tNotSquareSubset: '\\u228F\\u0338',\n\tNotSquareSubsetEqual: '\\u22E2',\n\tNotSquareSuperset: '\\u2290\\u0338',\n\tNotSquareSupersetEqual: '\\u22E3',\n\tNotSubset: '\\u2282\\u20D2',\n\tNotSubsetEqual: '\\u2288',\n\tNotSucceeds: '\\u2281',\n\tNotSucceedsEqual: '\\u2AB0\\u0338',\n\tNotSucceedsSlantEqual: '\\u22E1',\n\tNotSucceedsTilde: '\\u227F\\u0338',\n\tNotSuperset: '\\u2283\\u20D2',\n\tNotSupersetEqual: '\\u2289',\n\tNotTilde: '\\u2241',\n\tNotTildeEqual: '\\u2244',\n\tNotTildeFullEqual: '\\u2247',\n\tNotTildeTilde: '\\u2249',\n\tNotVerticalBar: '\\u2224',\n\tnpar: '\\u2226',\n\tnparallel: '\\u2226',\n\tnparsl: '\\u2AFD\\u20E5',\n\tnpart: '\\u2202\\u0338',\n\tnpolint: '\\u2A14',\n\tnpr: '\\u2280',\n\tnprcue: '\\u22E0',\n\tnpre: '\\u2AAF\\u0338',\n\tnprec: '\\u2280',\n\tnpreceq: '\\u2AAF\\u0338',\n\tnrArr: '\\u21CF',\n\tnrarr: '\\u219B',\n\tnrarrc: '\\u2933\\u0338',\n\tnrarrw: '\\u219D\\u0338',\n\tnRightarrow: '\\u21CF',\n\tnrightarrow: '\\u219B',\n\tnrtri: '\\u22EB',\n\tnrtrie: '\\u22ED',\n\tnsc: '\\u2281',\n\tnsccue: '\\u22E1',\n\tnsce: '\\u2AB0\\u0338',\n\tNscr: '\\uD835\\uDCA9',\n\tnscr: '\\uD835\\uDCC3',\n\tnshortmid: '\\u2224',\n\tnshortparallel: '\\u2226',\n\tnsim: '\\u2241',\n\tnsime: '\\u2244',\n\tnsimeq: '\\u2244',\n\tnsmid: '\\u2224',\n\tnspar: '\\u2226',\n\tnsqsube: '\\u22E2',\n\tnsqsupe: '\\u22E3',\n\tnsub: '\\u2284',\n\tnsubE: '\\u2AC5\\u0338',\n\tnsube: '\\u2288',\n\tnsubset: '\\u2282\\u20D2',\n\tnsubseteq: '\\u2288',\n\tnsubseteqq: '\\u2AC5\\u0338',\n\tnsucc: '\\u2281',\n\tnsucceq: '\\u2AB0\\u0338',\n\tnsup: '\\u2285',\n\tnsupE: '\\u2AC6\\u0338',\n\tnsupe: '\\u2289',\n\tnsupset: '\\u2283\\u20D2',\n\tnsupseteq: '\\u2289',\n\tnsupseteqq: '\\u2AC6\\u0338',\n\tntgl: '\\u2279',\n\tNtilde: '\\u00D1',\n\tntilde: '\\u00F1',\n\tntlg: '\\u2278',\n\tntriangleleft: '\\u22EA',\n\tntrianglelefteq: '\\u22EC',\n\tntriangleright: '\\u22EB',\n\tntrianglerighteq: '\\u22ED',\n\tNu: '\\u039D',\n\tnu: '\\u03BD',\n\tnum: '\\u0023',\n\tnumero: '\\u2116',\n\tnumsp: '\\u2007',\n\tnvap: '\\u224D\\u20D2',\n\tnVDash: '\\u22AF',\n\tnVdash: '\\u22AE',\n\tnvDash: '\\u22AD',\n\tnvdash: '\\u22AC',\n\tnvge: '\\u2265\\u20D2',\n\tnvgt: '\\u003E\\u20D2',\n\tnvHarr: '\\u2904',\n\tnvinfin: '\\u29DE',\n\tnvlArr: '\\u2902',\n\tnvle: '\\u2264\\u20D2',\n\tnvlt: '\\u003C\\u20D2',\n\tnvltrie: '\\u22B4\\u20D2',\n\tnvrArr: '\\u2903',\n\tnvrtrie: '\\u22B5\\u20D2',\n\tnvsim: '\\u223C\\u20D2',\n\tnwarhk: '\\u2923',\n\tnwArr: '\\u21D6',\n\tnwarr: '\\u2196',\n\tnwarrow: '\\u2196',\n\tnwnear: '\\u2927',\n\tOacute: '\\u00D3',\n\toacute: '\\u00F3',\n\toast: '\\u229B',\n\tocir: '\\u229A',\n\tOcirc: '\\u00D4',\n\tocirc: '\\u00F4',\n\tOcy: '\\u041E',\n\tocy: '\\u043E',\n\todash: '\\u229D',\n\tOdblac: '\\u0150',\n\todblac: '\\u0151',\n\todiv: '\\u2A38',\n\todot: '\\u2299',\n\todsold: '\\u29BC',\n\tOElig: '\\u0152',\n\toelig: '\\u0153',\n\tofcir: '\\u29BF',\n\tOfr: '\\uD835\\uDD12',\n\tofr: '\\uD835\\uDD2C',\n\togon: '\\u02DB',\n\tOgrave: '\\u00D2',\n\tograve: '\\u00F2',\n\togt: '\\u29C1',\n\tohbar: '\\u29B5',\n\tohm: '\\u03A9',\n\toint: '\\u222E',\n\tolarr: '\\u21BA',\n\tolcir: '\\u29BE',\n\tolcross: '\\u29BB',\n\toline: '\\u203E',\n\tolt: '\\u29C0',\n\tOmacr: '\\u014C',\n\tomacr: '\\u014D',\n\tOmega: '\\u03A9',\n\tomega: '\\u03C9',\n\tOmicron: '\\u039F',\n\tomicron: '\\u03BF',\n\tomid: '\\u29B6',\n\tominus: '\\u2296',\n\tOopf: '\\uD835\\uDD46',\n\toopf: '\\uD835\\uDD60',\n\topar: '\\u29B7',\n\tOpenCurlyDoubleQuote: '\\u201C',\n\tOpenCurlyQuote: '\\u2018',\n\toperp: '\\u29B9',\n\toplus: '\\u2295',\n\tOr: '\\u2A54',\n\tor: '\\u2228',\n\torarr: '\\u21BB',\n\tord: '\\u2A5D',\n\torder: '\\u2134',\n\torderof: '\\u2134',\n\tordf: '\\u00AA',\n\tordm: '\\u00BA',\n\torigof: '\\u22B6',\n\toror: '\\u2A56',\n\torslope: '\\u2A57',\n\torv: '\\u2A5B',\n\toS: '\\u24C8',\n\tOscr: '\\uD835\\uDCAA',\n\toscr: '\\u2134',\n\tOslash: '\\u00D8',\n\toslash: '\\u00F8',\n\tosol: '\\u2298',\n\tOtilde: '\\u00D5',\n\totilde: '\\u00F5',\n\tOtimes: '\\u2A37',\n\totimes: '\\u2297',\n\totimesas: '\\u2A36',\n\tOuml: '\\u00D6',\n\touml: '\\u00F6',\n\tovbar: '\\u233D',\n\tOverBar: '\\u203E',\n\tOverBrace: '\\u23DE',\n\tOverBracket: '\\u23B4',\n\tOverParenthesis: '\\u23DC',\n\tpar: '\\u2225',\n\tpara: '\\u00B6',\n\tparallel: '\\u2225',\n\tparsim: '\\u2AF3',\n\tparsl: '\\u2AFD',\n\tpart: '\\u2202',\n\tPartialD: '\\u2202',\n\tPcy: '\\u041F',\n\tpcy: '\\u043F',\n\tpercnt: '\\u0025',\n\tperiod: '\\u002E',\n\tpermil: '\\u2030',\n\tperp: '\\u22A5',\n\tpertenk: '\\u2031',\n\tPfr: '\\uD835\\uDD13',\n\tpfr: '\\uD835\\uDD2D',\n\tPhi: '\\u03A6',\n\tphi: '\\u03C6',\n\tphiv: '\\u03D5',\n\tphmmat: '\\u2133',\n\tphone: '\\u260E',\n\tPi: '\\u03A0',\n\tpi: '\\u03C0',\n\tpitchfork: '\\u22D4',\n\tpiv: '\\u03D6',\n\tplanck: '\\u210F',\n\tplanckh: '\\u210E',\n\tplankv: '\\u210F',\n\tplus: '\\u002B',\n\tplusacir: '\\u2A23',\n\tplusb: '\\u229E',\n\tpluscir: '\\u2A22',\n\tplusdo: '\\u2214',\n\tplusdu: '\\u2A25',\n\tpluse: '\\u2A72',\n\tPlusMinus: '\\u00B1',\n\tplusmn: '\\u00B1',\n\tplussim: '\\u2A26',\n\tplustwo: '\\u2A27',\n\tpm: '\\u00B1',\n\tPoincareplane: '\\u210C',\n\tpointint: '\\u2A15',\n\tPopf: '\\u2119',\n\tpopf: '\\uD835\\uDD61',\n\tpound: '\\u00A3',\n\tPr: '\\u2ABB',\n\tpr: '\\u227A',\n\tprap: '\\u2AB7',\n\tprcue: '\\u227C',\n\tprE: '\\u2AB3',\n\tpre: '\\u2AAF',\n\tprec: '\\u227A',\n\tprecapprox: '\\u2AB7',\n\tpreccurlyeq: '\\u227C',\n\tPrecedes: '\\u227A',\n\tPrecedesEqual: '\\u2AAF',\n\tPrecedesSlantEqual: '\\u227C',\n\tPrecedesTilde: '\\u227E',\n\tpreceq: '\\u2AAF',\n\tprecnapprox: '\\u2AB9',\n\tprecneqq: '\\u2AB5',\n\tprecnsim: '\\u22E8',\n\tprecsim: '\\u227E',\n\tPrime: '\\u2033',\n\tprime: '\\u2032',\n\tprimes: '\\u2119',\n\tprnap: '\\u2AB9',\n\tprnE: '\\u2AB5',\n\tprnsim: '\\u22E8',\n\tprod: '\\u220F',\n\tProduct: '\\u220F',\n\tprofalar: '\\u232E',\n\tprofline: '\\u2312',\n\tprofsurf: '\\u2313',\n\tprop: '\\u221D',\n\tProportion: '\\u2237',\n\tProportional: '\\u221D',\n\tpropto: '\\u221D',\n\tprsim: '\\u227E',\n\tprurel: '\\u22B0',\n\tPscr: '\\uD835\\uDCAB',\n\tpscr: '\\uD835\\uDCC5',\n\tPsi: '\\u03A8',\n\tpsi: '\\u03C8',\n\tpuncsp: '\\u2008',\n\tQfr: '\\uD835\\uDD14',\n\tqfr: '\\uD835\\uDD2E',\n\tqint: '\\u2A0C',\n\tQopf: '\\u211A',\n\tqopf: '\\uD835\\uDD62',\n\tqprime: '\\u2057',\n\tQscr: '\\uD835\\uDCAC',\n\tqscr: '\\uD835\\uDCC6',\n\tquaternions: '\\u210D',\n\tquatint: '\\u2A16',\n\tquest: '\\u003F',\n\tquesteq: '\\u225F',\n\tQUOT: '\\u0022',\n\tquot: '\\u0022',\n\trAarr: '\\u21DB',\n\trace: '\\u223D\\u0331',\n\tRacute: '\\u0154',\n\tracute: '\\u0155',\n\tradic: '\\u221A',\n\traemptyv: '\\u29B3',\n\tRang: '\\u27EB',\n\trang: '\\u27E9',\n\trangd: '\\u2992',\n\trange: '\\u29A5',\n\trangle: '\\u27E9',\n\traquo: '\\u00BB',\n\tRarr: '\\u21A0',\n\trArr: '\\u21D2',\n\trarr: '\\u2192',\n\trarrap: '\\u2975',\n\trarrb: '\\u21E5',\n\trarrbfs: '\\u2920',\n\trarrc: '\\u2933',\n\trarrfs: '\\u291E',\n\trarrhk: '\\u21AA',\n\trarrlp: '\\u21AC',\n\trarrpl: '\\u2945',\n\trarrsim: '\\u2974',\n\tRarrtl: '\\u2916',\n\trarrtl: '\\u21A3',\n\trarrw: '\\u219D',\n\trAtail: '\\u291C',\n\tratail: '\\u291A',\n\tratio: '\\u2236',\n\trationals: '\\u211A',\n\tRBarr: '\\u2910',\n\trBarr: '\\u290F',\n\trbarr: '\\u290D',\n\trbbrk: '\\u2773',\n\trbrace: '\\u007D',\n\trbrack: '\\u005D',\n\trbrke: '\\u298C',\n\trbrksld: '\\u298E',\n\trbrkslu: '\\u2990',\n\tRcaron: '\\u0158',\n\trcaron: '\\u0159',\n\tRcedil: '\\u0156',\n\trcedil: '\\u0157',\n\trceil: '\\u2309',\n\trcub: '\\u007D',\n\tRcy: '\\u0420',\n\trcy: '\\u0440',\n\trdca: '\\u2937',\n\trdldhar: '\\u2969',\n\trdquo: '\\u201D',\n\trdquor: '\\u201D',\n\trdsh: '\\u21B3',\n\tRe: '\\u211C',\n\treal: '\\u211C',\n\trealine: '\\u211B',\n\trealpart: '\\u211C',\n\treals: '\\u211D',\n\trect: '\\u25AD',\n\tREG: '\\u00AE',\n\treg: '\\u00AE',\n\tReverseElement: '\\u220B',\n\tReverseEquilibrium: '\\u21CB',\n\tReverseUpEquilibrium: '\\u296F',\n\trfisht: '\\u297D',\n\trfloor: '\\u230B',\n\tRfr: '\\u211C',\n\trfr: '\\uD835\\uDD2F',\n\trHar: '\\u2964',\n\trhard: '\\u21C1',\n\trharu: '\\u21C0',\n\trharul: '\\u296C',\n\tRho: '\\u03A1',\n\trho: '\\u03C1',\n\trhov: '\\u03F1',\n\tRightAngleBracket: '\\u27E9',\n\tRightArrow: '\\u2192',\n\tRightarrow: '\\u21D2',\n\trightarrow: '\\u2192',\n\tRightArrowBar: '\\u21E5',\n\tRightArrowLeftArrow: '\\u21C4',\n\trightarrowtail: '\\u21A3',\n\tRightCeiling: '\\u2309',\n\tRightDoubleBracket: '\\u27E7',\n\tRightDownTeeVector: '\\u295D',\n\tRightDownVector: '\\u21C2',\n\tRightDownVectorBar: '\\u2955',\n\tRightFloor: '\\u230B',\n\trightharpoondown: '\\u21C1',\n\trightharpoonup: '\\u21C0',\n\trightleftarrows: '\\u21C4',\n\trightleftharpoons: '\\u21CC',\n\trightrightarrows: '\\u21C9',\n\trightsquigarrow: '\\u219D',\n\tRightTee: '\\u22A2',\n\tRightTeeArrow: '\\u21A6',\n\tRightTeeVector: '\\u295B',\n\trightthreetimes: '\\u22CC',\n\tRightTriangle: '\\u22B3',\n\tRightTriangleBar: '\\u29D0',\n\tRightTriangleEqual: '\\u22B5',\n\tRightUpDownVector: '\\u294F',\n\tRightUpTeeVector: '\\u295C',\n\tRightUpVector: '\\u21BE',\n\tRightUpVectorBar: '\\u2954',\n\tRightVector: '\\u21C0',\n\tRightVectorBar: '\\u2953',\n\tring: '\\u02DA',\n\trisingdotseq: '\\u2253',\n\trlarr: '\\u21C4',\n\trlhar: '\\u21CC',\n\trlm: '\\u200F',\n\trmoust: '\\u23B1',\n\trmoustache: '\\u23B1',\n\trnmid: '\\u2AEE',\n\troang: '\\u27ED',\n\troarr: '\\u21FE',\n\trobrk: '\\u27E7',\n\tropar: '\\u2986',\n\tRopf: '\\u211D',\n\tropf: '\\uD835\\uDD63',\n\troplus: '\\u2A2E',\n\trotimes: '\\u2A35',\n\tRoundImplies: '\\u2970',\n\trpar: '\\u0029',\n\trpargt: '\\u2994',\n\trppolint: '\\u2A12',\n\trrarr: '\\u21C9',\n\tRrightarrow: '\\u21DB',\n\trsaquo: '\\u203A',\n\tRscr: '\\u211B',\n\trscr: '\\uD835\\uDCC7',\n\tRsh: '\\u21B1',\n\trsh: '\\u21B1',\n\trsqb: '\\u005D',\n\trsquo: '\\u2019',\n\trsquor: '\\u2019',\n\trthree: '\\u22CC',\n\trtimes: '\\u22CA',\n\trtri: '\\u25B9',\n\trtrie: '\\u22B5',\n\trtrif: '\\u25B8',\n\trtriltri: '\\u29CE',\n\tRuleDelayed: '\\u29F4',\n\truluhar: '\\u2968',\n\trx: '\\u211E',\n\tSacute: '\\u015A',\n\tsacute: '\\u015B',\n\tsbquo: '\\u201A',\n\tSc: '\\u2ABC',\n\tsc: '\\u227B',\n\tscap: '\\u2AB8',\n\tScaron: '\\u0160',\n\tscaron: '\\u0161',\n\tsccue: '\\u227D',\n\tscE: '\\u2AB4',\n\tsce: '\\u2AB0',\n\tScedil: '\\u015E',\n\tscedil: '\\u015F',\n\tScirc: '\\u015C',\n\tscirc: '\\u015D',\n\tscnap: '\\u2ABA',\n\tscnE: '\\u2AB6',\n\tscnsim: '\\u22E9',\n\tscpolint: '\\u2A13',\n\tscsim: '\\u227F',\n\tScy: '\\u0421',\n\tscy: '\\u0441',\n\tsdot: '\\u22C5',\n\tsdotb: '\\u22A1',\n\tsdote: '\\u2A66',\n\tsearhk: '\\u2925',\n\tseArr: '\\u21D8',\n\tsearr: '\\u2198',\n\tsearrow: '\\u2198',\n\tsect: '\\u00A7',\n\tsemi: '\\u003B',\n\tseswar: '\\u2929',\n\tsetminus: '\\u2216',\n\tsetmn: '\\u2216',\n\tsext: '\\u2736',\n\tSfr: '\\uD835\\uDD16',\n\tsfr: '\\uD835\\uDD30',\n\tsfrown: '\\u2322',\n\tsharp: '\\u266F',\n\tSHCHcy: '\\u0429',\n\tshchcy: '\\u0449',\n\tSHcy: '\\u0428',\n\tshcy: '\\u0448',\n\tShortDownArrow: '\\u2193',\n\tShortLeftArrow: '\\u2190',\n\tshortmid: '\\u2223',\n\tshortparallel: '\\u2225',\n\tShortRightArrow: '\\u2192',\n\tShortUpArrow: '\\u2191',\n\tshy: '\\u00AD',\n\tSigma: '\\u03A3',\n\tsigma: '\\u03C3',\n\tsigmaf: '\\u03C2',\n\tsigmav: '\\u03C2',\n\tsim: '\\u223C',\n\tsimdot: '\\u2A6A',\n\tsime: '\\u2243',\n\tsimeq: '\\u2243',\n\tsimg: '\\u2A9E',\n\tsimgE: '\\u2AA0',\n\tsiml: '\\u2A9D',\n\tsimlE: '\\u2A9F',\n\tsimne: '\\u2246',\n\tsimplus: '\\u2A24',\n\tsimrarr: '\\u2972',\n\tslarr: '\\u2190',\n\tSmallCircle: '\\u2218',\n\tsmallsetminus: '\\u2216',\n\tsmashp: '\\u2A33',\n\tsmeparsl: '\\u29E4',\n\tsmid: '\\u2223',\n\tsmile: '\\u2323',\n\tsmt: '\\u2AAA',\n\tsmte: '\\u2AAC',\n\tsmtes: '\\u2AAC\\uFE00',\n\tSOFTcy: '\\u042C',\n\tsoftcy: '\\u044C',\n\tsol: '\\u002F',\n\tsolb: '\\u29C4',\n\tsolbar: '\\u233F',\n\tSopf: '\\uD835\\uDD4A',\n\tsopf: '\\uD835\\uDD64',\n\tspades: '\\u2660',\n\tspadesuit: '\\u2660',\n\tspar: '\\u2225',\n\tsqcap: '\\u2293',\n\tsqcaps: '\\u2293\\uFE00',\n\tsqcup: '\\u2294',\n\tsqcups: '\\u2294\\uFE00',\n\tSqrt: '\\u221A',\n\tsqsub: '\\u228F',\n\tsqsube: '\\u2291',\n\tsqsubset: '\\u228F',\n\tsqsubseteq: '\\u2291',\n\tsqsup: '\\u2290',\n\tsqsupe: '\\u2292',\n\tsqsupset: '\\u2290',\n\tsqsupseteq: '\\u2292',\n\tsqu: '\\u25A1',\n\tSquare: '\\u25A1',\n\tsquare: '\\u25A1',\n\tSquareIntersection: '\\u2293',\n\tSquareSubset: '\\u228F',\n\tSquareSubsetEqual: '\\u2291',\n\tSquareSuperset: '\\u2290',\n\tSquareSupersetEqual: '\\u2292',\n\tSquareUnion: '\\u2294',\n\tsquarf: '\\u25AA',\n\tsquf: '\\u25AA',\n\tsrarr: '\\u2192',\n\tSscr: '\\uD835\\uDCAE',\n\tsscr: '\\uD835\\uDCC8',\n\tssetmn: '\\u2216',\n\tssmile: '\\u2323',\n\tsstarf: '\\u22C6',\n\tStar: '\\u22C6',\n\tstar: '\\u2606',\n\tstarf: '\\u2605',\n\tstraightepsilon: '\\u03F5',\n\tstraightphi: '\\u03D5',\n\tstrns: '\\u00AF',\n\tSub: '\\u22D0',\n\tsub: '\\u2282',\n\tsubdot: '\\u2ABD',\n\tsubE: '\\u2AC5',\n\tsube: '\\u2286',\n\tsubedot: '\\u2AC3',\n\tsubmult: '\\u2AC1',\n\tsubnE: '\\u2ACB',\n\tsubne: '\\u228A',\n\tsubplus: '\\u2ABF',\n\tsubrarr: '\\u2979',\n\tSubset: '\\u22D0',\n\tsubset: '\\u2282',\n\tsubseteq: '\\u2286',\n\tsubseteqq: '\\u2AC5',\n\tSubsetEqual: '\\u2286',\n\tsubsetneq: '\\u228A',\n\tsubsetneqq: '\\u2ACB',\n\tsubsim: '\\u2AC7',\n\tsubsub: '\\u2AD5',\n\tsubsup: '\\u2AD3',\n\tsucc: '\\u227B',\n\tsuccapprox: '\\u2AB8',\n\tsucccurlyeq: '\\u227D',\n\tSucceeds: '\\u227B',\n\tSucceedsEqual: '\\u2AB0',\n\tSucceedsSlantEqual: '\\u227D',\n\tSucceedsTilde: '\\u227F',\n\tsucceq: '\\u2AB0',\n\tsuccnapprox: '\\u2ABA',\n\tsuccneqq: '\\u2AB6',\n\tsuccnsim: '\\u22E9',\n\tsuccsim: '\\u227F',\n\tSuchThat: '\\u220B',\n\tSum: '\\u2211',\n\tsum: '\\u2211',\n\tsung: '\\u266A',\n\tSup: '\\u22D1',\n\tsup: '\\u2283',\n\tsup1: '\\u00B9',\n\tsup2: '\\u00B2',\n\tsup3: '\\u00B3',\n\tsupdot: '\\u2ABE',\n\tsupdsub: '\\u2AD8',\n\tsupE: '\\u2AC6',\n\tsupe: '\\u2287',\n\tsupedot: '\\u2AC4',\n\tSuperset: '\\u2283',\n\tSupersetEqual: '\\u2287',\n\tsuphsol: '\\u27C9',\n\tsuphsub: '\\u2AD7',\n\tsuplarr: '\\u297B',\n\tsupmult: '\\u2AC2',\n\tsupnE: '\\u2ACC',\n\tsupne: '\\u228B',\n\tsupplus: '\\u2AC0',\n\tSupset: '\\u22D1',\n\tsupset: '\\u2283',\n\tsupseteq: '\\u2287',\n\tsupseteqq: '\\u2AC6',\n\tsupsetneq: '\\u228B',\n\tsupsetneqq: '\\u2ACC',\n\tsupsim: '\\u2AC8',\n\tsupsub: '\\u2AD4',\n\tsupsup: '\\u2AD6',\n\tswarhk: '\\u2926',\n\tswArr: '\\u21D9',\n\tswarr: '\\u2199',\n\tswarrow: '\\u2199',\n\tswnwar: '\\u292A',\n\tszlig: '\\u00DF',\n\tTab: '\\u0009',\n\ttarget: '\\u2316',\n\tTau: '\\u03A4',\n\ttau: '\\u03C4',\n\ttbrk: '\\u23B4',\n\tTcaron: '\\u0164',\n\ttcaron: '\\u0165',\n\tTcedil: '\\u0162',\n\ttcedil: '\\u0163',\n\tTcy: '\\u0422',\n\ttcy: '\\u0442',\n\ttdot: '\\u20DB',\n\ttelrec: '\\u2315',\n\tTfr: '\\uD835\\uDD17',\n\ttfr: '\\uD835\\uDD31',\n\tthere4: '\\u2234',\n\tTherefore: '\\u2234',\n\ttherefore: '\\u2234',\n\tTheta: '\\u0398',\n\ttheta: '\\u03B8',\n\tthetasym: '\\u03D1',\n\tthetav: '\\u03D1',\n\tthickapprox: '\\u2248',\n\tthicksim: '\\u223C',\n\tThickSpace: '\\u205F\\u200A',\n\tthinsp: '\\u2009',\n\tThinSpace: '\\u2009',\n\tthkap: '\\u2248',\n\tthksim: '\\u223C',\n\tTHORN: '\\u00DE',\n\tthorn: '\\u00FE',\n\tTilde: '\\u223C',\n\ttilde: '\\u02DC',\n\tTildeEqual: '\\u2243',\n\tTildeFullEqual: '\\u2245',\n\tTildeTilde: '\\u2248',\n\ttimes: '\\u00D7',\n\ttimesb: '\\u22A0',\n\ttimesbar: '\\u2A31',\n\ttimesd: '\\u2A30',\n\ttint: '\\u222D',\n\ttoea: '\\u2928',\n\ttop: '\\u22A4',\n\ttopbot: '\\u2336',\n\ttopcir: '\\u2AF1',\n\tTopf: '\\uD835\\uDD4B',\n\ttopf: '\\uD835\\uDD65',\n\ttopfork: '\\u2ADA',\n\ttosa: '\\u2929',\n\ttprime: '\\u2034',\n\tTRADE: '\\u2122',\n\ttrade: '\\u2122',\n\ttriangle: '\\u25B5',\n\ttriangledown: '\\u25BF',\n\ttriangleleft: '\\u25C3',\n\ttrianglelefteq: '\\u22B4',\n\ttriangleq: '\\u225C',\n\ttriangleright: '\\u25B9',\n\ttrianglerighteq: '\\u22B5',\n\ttridot: '\\u25EC',\n\ttrie: '\\u225C',\n\ttriminus: '\\u2A3A',\n\tTripleDot: '\\u20DB',\n\ttriplus: '\\u2A39',\n\ttrisb: '\\u29CD',\n\ttritime: '\\u2A3B',\n\ttrpezium: '\\u23E2',\n\tTscr: '\\uD835\\uDCAF',\n\ttscr: '\\uD835\\uDCC9',\n\tTScy: '\\u0426',\n\ttscy: '\\u0446',\n\tTSHcy: '\\u040B',\n\ttshcy: '\\u045B',\n\tTstrok: '\\u0166',\n\ttstrok: '\\u0167',\n\ttwixt: '\\u226C',\n\ttwoheadleftarrow: '\\u219E',\n\ttwoheadrightarrow: '\\u21A0',\n\tUacute: '\\u00DA',\n\tuacute: '\\u00FA',\n\tUarr: '\\u219F',\n\tuArr: '\\u21D1',\n\tuarr: '\\u2191',\n\tUarrocir: '\\u2949',\n\tUbrcy: '\\u040E',\n\tubrcy: '\\u045E',\n\tUbreve: '\\u016C',\n\tubreve: '\\u016D',\n\tUcirc: '\\u00DB',\n\tucirc: '\\u00FB',\n\tUcy: '\\u0423',\n\tucy: '\\u0443',\n\tudarr: '\\u21C5',\n\tUdblac: '\\u0170',\n\tudblac: '\\u0171',\n\tudhar: '\\u296E',\n\tufisht: '\\u297E',\n\tUfr: '\\uD835\\uDD18',\n\tufr: '\\uD835\\uDD32',\n\tUgrave: '\\u00D9',\n\tugrave: '\\u00F9',\n\tuHar: '\\u2963',\n\tuharl: '\\u21BF',\n\tuharr: '\\u21BE',\n\tuhblk: '\\u2580',\n\tulcorn: '\\u231C',\n\tulcorner: '\\u231C',\n\tulcrop: '\\u230F',\n\tultri: '\\u25F8',\n\tUmacr: '\\u016A',\n\tumacr: '\\u016B',\n\tuml: '\\u00A8',\n\tUnderBar: '\\u005F',\n\tUnderBrace: '\\u23DF',\n\tUnderBracket: '\\u23B5',\n\tUnderParenthesis: '\\u23DD',\n\tUnion: '\\u22C3',\n\tUnionPlus: '\\u228E',\n\tUogon: '\\u0172',\n\tuogon: '\\u0173',\n\tUopf: '\\uD835\\uDD4C',\n\tuopf: '\\uD835\\uDD66',\n\tUpArrow: '\\u2191',\n\tUparrow: '\\u21D1',\n\tuparrow: '\\u2191',\n\tUpArrowBar: '\\u2912',\n\tUpArrowDownArrow: '\\u21C5',\n\tUpDownArrow: '\\u2195',\n\tUpdownarrow: '\\u21D5',\n\tupdownarrow: '\\u2195',\n\tUpEquilibrium: '\\u296E',\n\tupharpoonleft: '\\u21BF',\n\tupharpoonright: '\\u21BE',\n\tuplus: '\\u228E',\n\tUpperLeftArrow: '\\u2196',\n\tUpperRightArrow: '\\u2197',\n\tUpsi: '\\u03D2',\n\tupsi: '\\u03C5',\n\tupsih: '\\u03D2',\n\tUpsilon: '\\u03A5',\n\tupsilon: '\\u03C5',\n\tUpTee: '\\u22A5',\n\tUpTeeArrow: '\\u21A5',\n\tupuparrows: '\\u21C8',\n\turcorn: '\\u231D',\n\turcorner: '\\u231D',\n\turcrop: '\\u230E',\n\tUring: '\\u016E',\n\turing: '\\u016F',\n\turtri: '\\u25F9',\n\tUscr: '\\uD835\\uDCB0',\n\tuscr: '\\uD835\\uDCCA',\n\tutdot: '\\u22F0',\n\tUtilde: '\\u0168',\n\tutilde: '\\u0169',\n\tutri: '\\u25B5',\n\tutrif: '\\u25B4',\n\tuuarr: '\\u21C8',\n\tUuml: '\\u00DC',\n\tuuml: '\\u00FC',\n\tuwangle: '\\u29A7',\n\tvangrt: '\\u299C',\n\tvarepsilon: '\\u03F5',\n\tvarkappa: '\\u03F0',\n\tvarnothing: '\\u2205',\n\tvarphi: '\\u03D5',\n\tvarpi: '\\u03D6',\n\tvarpropto: '\\u221D',\n\tvArr: '\\u21D5',\n\tvarr: '\\u2195',\n\tvarrho: '\\u03F1',\n\tvarsigma: '\\u03C2',\n\tvarsubsetneq: '\\u228A\\uFE00',\n\tvarsubsetneqq: '\\u2ACB\\uFE00',\n\tvarsupsetneq: '\\u228B\\uFE00',\n\tvarsupsetneqq: '\\u2ACC\\uFE00',\n\tvartheta: '\\u03D1',\n\tvartriangleleft: '\\u22B2',\n\tvartriangleright: '\\u22B3',\n\tVbar: '\\u2AEB',\n\tvBar: '\\u2AE8',\n\tvBarv: '\\u2AE9',\n\tVcy: '\\u0412',\n\tvcy: '\\u0432',\n\tVDash: '\\u22AB',\n\tVdash: '\\u22A9',\n\tvDash: '\\u22A8',\n\tvdash: '\\u22A2',\n\tVdashl: '\\u2AE6',\n\tVee: '\\u22C1',\n\tvee: '\\u2228',\n\tveebar: '\\u22BB',\n\tveeeq: '\\u225A',\n\tvellip: '\\u22EE',\n\tVerbar: '\\u2016',\n\tverbar: '\\u007C',\n\tVert: '\\u2016',\n\tvert: '\\u007C',\n\tVerticalBar: '\\u2223',\n\tVerticalLine: '\\u007C',\n\tVerticalSeparator: '\\u2758',\n\tVerticalTilde: '\\u2240',\n\tVeryThinSpace: '\\u200A',\n\tVfr: '\\uD835\\uDD19',\n\tvfr: '\\uD835\\uDD33',\n\tvltri: '\\u22B2',\n\tvnsub: '\\u2282\\u20D2',\n\tvnsup: '\\u2283\\u20D2',\n\tVopf: '\\uD835\\uDD4D',\n\tvopf: '\\uD835\\uDD67',\n\tvprop: '\\u221D',\n\tvrtri: '\\u22B3',\n\tVscr: '\\uD835\\uDCB1',\n\tvscr: '\\uD835\\uDCCB',\n\tvsubnE: '\\u2ACB\\uFE00',\n\tvsubne: '\\u228A\\uFE00',\n\tvsupnE: '\\u2ACC\\uFE00',\n\tvsupne: '\\u228B\\uFE00',\n\tVvdash: '\\u22AA',\n\tvzigzag: '\\u299A',\n\tWcirc: '\\u0174',\n\twcirc: '\\u0175',\n\twedbar: '\\u2A5F',\n\tWedge: '\\u22C0',\n\twedge: '\\u2227',\n\twedgeq: '\\u2259',\n\tweierp: '\\u2118',\n\tWfr: '\\uD835\\uDD1A',\n\twfr: '\\uD835\\uDD34',\n\tWopf: '\\uD835\\uDD4E',\n\twopf: '\\uD835\\uDD68',\n\twp: '\\u2118',\n\twr: '\\u2240',\n\twreath: '\\u2240',\n\tWscr: '\\uD835\\uDCB2',\n\twscr: '\\uD835\\uDCCC',\n\txcap: '\\u22C2',\n\txcirc: '\\u25EF',\n\txcup: '\\u22C3',\n\txdtri: '\\u25BD',\n\tXfr: '\\uD835\\uDD1B',\n\txfr: '\\uD835\\uDD35',\n\txhArr: '\\u27FA',\n\txharr: '\\u27F7',\n\tXi: '\\u039E',\n\txi: '\\u03BE',\n\txlArr: '\\u27F8',\n\txlarr: '\\u27F5',\n\txmap: '\\u27FC',\n\txnis: '\\u22FB',\n\txodot: '\\u2A00',\n\tXopf: '\\uD835\\uDD4F',\n\txopf: '\\uD835\\uDD69',\n\txoplus: '\\u2A01',\n\txotime: '\\u2A02',\n\txrArr: '\\u27F9',\n\txrarr: '\\u27F6',\n\tXscr: '\\uD835\\uDCB3',\n\txscr: '\\uD835\\uDCCD',\n\txsqcup: '\\u2A06',\n\txuplus: '\\u2A04',\n\txutri: '\\u25B3',\n\txvee: '\\u22C1',\n\txwedge: '\\u22C0',\n\tYacute: '\\u00DD',\n\tyacute: '\\u00FD',\n\tYAcy: '\\u042F',\n\tyacy: '\\u044F',\n\tYcirc: '\\u0176',\n\tycirc: '\\u0177',\n\tYcy: '\\u042B',\n\tycy: '\\u044B',\n\tyen: '\\u00A5',\n\tYfr: '\\uD835\\uDD1C',\n\tyfr: '\\uD835\\uDD36',\n\tYIcy: '\\u0407',\n\tyicy: '\\u0457',\n\tYopf: '\\uD835\\uDD50',\n\tyopf: '\\uD835\\uDD6A',\n\tYscr: '\\uD835\\uDCB4',\n\tyscr: '\\uD835\\uDCCE',\n\tYUcy: '\\u042E',\n\tyucy: '\\u044E',\n\tYuml: '\\u0178',\n\tyuml: '\\u00FF',\n\tZacute: '\\u0179',\n\tzacute: '\\u017A',\n\tZcaron: '\\u017D',\n\tzcaron: '\\u017E',\n\tZcy: '\\u0417',\n\tzcy: '\\u0437',\n\tZdot: '\\u017B',\n\tzdot: '\\u017C',\n\tzeetrf: '\\u2128',\n\tZeroWidthSpace: '\\u200B',\n\tZeta: '\\u0396',\n\tzeta: '\\u03B6',\n\tZfr: '\\u2128',\n\tzfr: '\\uD835\\uDD37',\n\tZHcy: '\\u0416',\n\tzhcy: '\\u0436',\n\tzigrarr: '\\u21DD',\n\tZopf: '\\u2124',\n\tzopf: '\\uD835\\uDD6B',\n\tZscr: '\\uD835\\uDCB5',\n\tzscr: '\\uD835\\uDCCF',\n\tzwj: '\\u200D',\n\tzwnj: '\\u200C',\n});\n\n/**\n * @deprecated use `HTML_ENTITIES` instead\n * @see HTML_ENTITIES\n */\nexports.entityMap = exports.HTML_ENTITIES;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHhtbGRvbS94bWxkb20vbGliL2VudGl0aWVzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGFBQWEsMkdBQStCOztBQUU1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9AeG1sZG9tL3htbGRvbS9saWIvZW50aXRpZXMuanM/ZjE4MCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBmcmVlemUgPSByZXF1aXJlKCcuL2NvbnZlbnRpb25zJykuZnJlZXplO1xuXG4vKipcbiAqIFRoZSBlbnRpdGllcyB0aGF0IGFyZSBwcmVkZWZpbmVkIGluIGV2ZXJ5IFhNTCBkb2N1bWVudC5cbiAqXG4gKiBAc2VlIGh0dHBzOi8vd3d3LnczLm9yZy9UUi8yMDA2L1JFQy14bWwxMS0yMDA2MDgxNi8jc2VjLXByZWRlZmluZWQtZW50IFczQyBYTUwgMS4xXG4gKiBAc2VlIGh0dHBzOi8vd3d3LnczLm9yZy9UUi8yMDA4L1JFQy14bWwtMjAwODExMjYvI3NlYy1wcmVkZWZpbmVkLWVudCBXM0MgWE1MIDEuMFxuICogQHNlZSBodHRwczovL2VuLndpa2lwZWRpYS5vcmcvd2lraS9MaXN0X29mX1hNTF9hbmRfSFRNTF9jaGFyYWN0ZXJfZW50aXR5X3JlZmVyZW5jZXMjUHJlZGVmaW5lZF9lbnRpdGllc19pbl9YTUwgV2lraXBlZGlhXG4gKi9cbmV4cG9ydHMuWE1MX0VOVElUSUVTID0gZnJlZXplKHtcblx0YW1wOiAnJicsXG5cdGFwb3M6IFwiJ1wiLFxuXHRndDogJz4nLFxuXHRsdDogJzwnLFxuXHRxdW90OiAnXCInLFxufSk7XG5cbi8qKlxuICogQSBtYXAgb2YgYWxsIGVudGl0aWVzIHRoYXQgYXJlIGRldGVjdGVkIGluIGFuIEhUTUwgZG9jdW1lbnQuXG4gKiBUaGV5IGNvbnRhaW4gYWxsIGVudHJpZXMgZnJvbSBgWE1MX0VOVElUSUVTYC5cbiAqXG4gKiBAc2VlIFhNTF9FTlRJVElFU1xuICogQHNlZSBET01QYXJzZXIucGFyc2VGcm9tU3RyaW5nXG4gKiBAc2VlIERPTUltcGxlbWVudGF0aW9uLnByb3RvdHlwZS5jcmVhdGVIVE1MRG9jdW1lbnRcbiAqIEBzZWUgaHR0cHM6Ly9odG1sLnNwZWMud2hhdHdnLm9yZy8jbmFtZWQtY2hhcmFjdGVyLXJlZmVyZW5jZXMgV0hBVFdHIEhUTUwoNSkgU3BlY1xuICogQHNlZSBodHRwczovL2h0bWwuc3BlYy53aGF0d2cub3JnL2VudGl0aWVzLmpzb24gSlNPTlxuICogQHNlZSBodHRwczovL3d3dy53My5vcmcvVFIveG1sLWVudGl0eS1uYW1lcy8gVzNDIFhNTCBFbnRpdHkgTmFtZXNcbiAqIEBzZWUgaHR0cHM6Ly93d3cudzMub3JnL1RSL2h0bWw0L3NnbWwvZW50aXRpZXMuaHRtbCBXM0MgSFRNTDQvU0dNTFxuICogQHNlZSBodHRwczovL2VuLndpa2lwZWRpYS5vcmcvd2lraS9MaXN0X29mX1hNTF9hbmRfSFRNTF9jaGFyYWN0ZXJfZW50aXR5X3JlZmVyZW5jZXMjQ2hhcmFjdGVyX2VudGl0eV9yZWZlcmVuY2VzX2luX0hUTUwgV2lraXBlZGlhIChIVE1MKVxuICogQHNlZSBodHRwczovL2VuLndpa2lwZWRpYS5vcmcvd2lraS9MaXN0X29mX1hNTF9hbmRfSFRNTF9jaGFyYWN0ZXJfZW50aXR5X3JlZmVyZW5jZXMjRW50aXRpZXNfcmVwcmVzZW50aW5nX3NwZWNpYWxfY2hhcmFjdGVyc19pbl9YSFRNTCBXaWtwZWRpYSAoWEhUTUwpXG4gKi9cbmV4cG9ydHMuSFRNTF9FTlRJVElFUyA9IGZyZWV6ZSh7XG5cdEFhY3V0ZTogJ1xcdTAwQzEnLFxuXHRhYWN1dGU6ICdcXHUwMEUxJyxcblx0QWJyZXZlOiAnXFx1MDEwMicsXG5cdGFicmV2ZTogJ1xcdTAxMDMnLFxuXHRhYzogJ1xcdTIyM0UnLFxuXHRhY2Q6ICdcXHUyMjNGJyxcblx0YWNFOiAnXFx1MjIzRVxcdTAzMzMnLFxuXHRBY2lyYzogJ1xcdTAwQzInLFxuXHRhY2lyYzogJ1xcdTAwRTInLFxuXHRhY3V0ZTogJ1xcdTAwQjQnLFxuXHRBY3k6ICdcXHUwNDEwJyxcblx0YWN5OiAnXFx1MDQzMCcsXG5cdEFFbGlnOiAnXFx1MDBDNicsXG5cdGFlbGlnOiAnXFx1MDBFNicsXG5cdGFmOiAnXFx1MjA2MScsXG5cdEFmcjogJ1xcdUQ4MzVcXHVERDA0Jyxcblx0YWZyOiAnXFx1RDgzNVxcdUREMUUnLFxuXHRBZ3JhdmU6ICdcXHUwMEMwJyxcblx0YWdyYXZlOiAnXFx1MDBFMCcsXG5cdGFsZWZzeW06ICdcXHUyMTM1Jyxcblx0YWxlcGg6ICdcXHUyMTM1Jyxcblx0QWxwaGE6ICdcXHUwMzkxJyxcblx0YWxwaGE6ICdcXHUwM0IxJyxcblx0QW1hY3I6ICdcXHUwMTAwJyxcblx0YW1hY3I6ICdcXHUwMTAxJyxcblx0YW1hbGc6ICdcXHUyQTNGJyxcblx0QU1QOiAnXFx1MDAyNicsXG5cdGFtcDogJ1xcdTAwMjYnLFxuXHRBbmQ6ICdcXHUyQTUzJyxcblx0YW5kOiAnXFx1MjIyNycsXG5cdGFuZGFuZDogJ1xcdTJBNTUnLFxuXHRhbmRkOiAnXFx1MkE1QycsXG5cdGFuZHNsb3BlOiAnXFx1MkE1OCcsXG5cdGFuZHY6ICdcXHUyQTVBJyxcblx0YW5nOiAnXFx1MjIyMCcsXG5cdGFuZ2U6ICdcXHUyOUE0Jyxcblx0YW5nbGU6ICdcXHUyMjIwJyxcblx0YW5nbXNkOiAnXFx1MjIyMScsXG5cdGFuZ21zZGFhOiAnXFx1MjlBOCcsXG5cdGFuZ21zZGFiOiAnXFx1MjlBOScsXG5cdGFuZ21zZGFjOiAnXFx1MjlBQScsXG5cdGFuZ21zZGFkOiAnXFx1MjlBQicsXG5cdGFuZ21zZGFlOiAnXFx1MjlBQycsXG5cdGFuZ21zZGFmOiAnXFx1MjlBRCcsXG5cdGFuZ21zZGFnOiAnXFx1MjlBRScsXG5cdGFuZ21zZGFoOiAnXFx1MjlBRicsXG5cdGFuZ3J0OiAnXFx1MjIxRicsXG5cdGFuZ3J0dmI6ICdcXHUyMkJFJyxcblx0YW5ncnR2YmQ6ICdcXHUyOTlEJyxcblx0YW5nc3BoOiAnXFx1MjIyMicsXG5cdGFuZ3N0OiAnXFx1MDBDNScsXG5cdGFuZ3phcnI6ICdcXHUyMzdDJyxcblx0QW9nb246ICdcXHUwMTA0Jyxcblx0YW9nb246ICdcXHUwMTA1Jyxcblx0QW9wZjogJ1xcdUQ4MzVcXHVERDM4Jyxcblx0YW9wZjogJ1xcdUQ4MzVcXHVERDUyJyxcblx0YXA6ICdcXHUyMjQ4Jyxcblx0YXBhY2lyOiAnXFx1MkE2RicsXG5cdGFwRTogJ1xcdTJBNzAnLFxuXHRhcGU6ICdcXHUyMjRBJyxcblx0YXBpZDogJ1xcdTIyNEInLFxuXHRhcG9zOiAnXFx1MDAyNycsXG5cdEFwcGx5RnVuY3Rpb246ICdcXHUyMDYxJyxcblx0YXBwcm94OiAnXFx1MjI0OCcsXG5cdGFwcHJveGVxOiAnXFx1MjI0QScsXG5cdEFyaW5nOiAnXFx1MDBDNScsXG5cdGFyaW5nOiAnXFx1MDBFNScsXG5cdEFzY3I6ICdcXHVEODM1XFx1REM5QycsXG5cdGFzY3I6ICdcXHVEODM1XFx1RENCNicsXG5cdEFzc2lnbjogJ1xcdTIyNTQnLFxuXHRhc3Q6ICdcXHUwMDJBJyxcblx0YXN5bXA6ICdcXHUyMjQ4Jyxcblx0YXN5bXBlcTogJ1xcdTIyNEQnLFxuXHRBdGlsZGU6ICdcXHUwMEMzJyxcblx0YXRpbGRlOiAnXFx1MDBFMycsXG5cdEF1bWw6ICdcXHUwMEM0Jyxcblx0YXVtbDogJ1xcdTAwRTQnLFxuXHRhd2NvbmludDogJ1xcdTIyMzMnLFxuXHRhd2ludDogJ1xcdTJBMTEnLFxuXHRiYWNrY29uZzogJ1xcdTIyNEMnLFxuXHRiYWNrZXBzaWxvbjogJ1xcdTAzRjYnLFxuXHRiYWNrcHJpbWU6ICdcXHUyMDM1Jyxcblx0YmFja3NpbTogJ1xcdTIyM0QnLFxuXHRiYWNrc2ltZXE6ICdcXHUyMkNEJyxcblx0QmFja3NsYXNoOiAnXFx1MjIxNicsXG5cdEJhcnY6ICdcXHUyQUU3Jyxcblx0YmFydmVlOiAnXFx1MjJCRCcsXG5cdEJhcndlZDogJ1xcdTIzMDYnLFxuXHRiYXJ3ZWQ6ICdcXHUyMzA1Jyxcblx0YmFyd2VkZ2U6ICdcXHUyMzA1Jyxcblx0YmJyazogJ1xcdTIzQjUnLFxuXHRiYnJrdGJyazogJ1xcdTIzQjYnLFxuXHRiY29uZzogJ1xcdTIyNEMnLFxuXHRCY3k6ICdcXHUwNDExJyxcblx0YmN5OiAnXFx1MDQzMScsXG5cdGJkcXVvOiAnXFx1MjAxRScsXG5cdGJlY2F1czogJ1xcdTIyMzUnLFxuXHRCZWNhdXNlOiAnXFx1MjIzNScsXG5cdGJlY2F1c2U6ICdcXHUyMjM1Jyxcblx0YmVtcHR5djogJ1xcdTI5QjAnLFxuXHRiZXBzaTogJ1xcdTAzRjYnLFxuXHRiZXJub3U6ICdcXHUyMTJDJyxcblx0QmVybm91bGxpczogJ1xcdTIxMkMnLFxuXHRCZXRhOiAnXFx1MDM5MicsXG5cdGJldGE6ICdcXHUwM0IyJyxcblx0YmV0aDogJ1xcdTIxMzYnLFxuXHRiZXR3ZWVuOiAnXFx1MjI2QycsXG5cdEJmcjogJ1xcdUQ4MzVcXHVERDA1Jyxcblx0YmZyOiAnXFx1RDgzNVxcdUREMUYnLFxuXHRiaWdjYXA6ICdcXHUyMkMyJyxcblx0YmlnY2lyYzogJ1xcdTI1RUYnLFxuXHRiaWdjdXA6ICdcXHUyMkMzJyxcblx0Ymlnb2RvdDogJ1xcdTJBMDAnLFxuXHRiaWdvcGx1czogJ1xcdTJBMDEnLFxuXHRiaWdvdGltZXM6ICdcXHUyQTAyJyxcblx0Ymlnc3FjdXA6ICdcXHUyQTA2Jyxcblx0Ymlnc3RhcjogJ1xcdTI2MDUnLFxuXHRiaWd0cmlhbmdsZWRvd246ICdcXHUyNUJEJyxcblx0YmlndHJpYW5nbGV1cDogJ1xcdTI1QjMnLFxuXHRiaWd1cGx1czogJ1xcdTJBMDQnLFxuXHRiaWd2ZWU6ICdcXHUyMkMxJyxcblx0Ymlnd2VkZ2U6ICdcXHUyMkMwJyxcblx0Ymthcm93OiAnXFx1MjkwRCcsXG5cdGJsYWNrbG96ZW5nZTogJ1xcdTI5RUInLFxuXHRibGFja3NxdWFyZTogJ1xcdTI1QUEnLFxuXHRibGFja3RyaWFuZ2xlOiAnXFx1MjVCNCcsXG5cdGJsYWNrdHJpYW5nbGVkb3duOiAnXFx1MjVCRScsXG5cdGJsYWNrdHJpYW5nbGVsZWZ0OiAnXFx1MjVDMicsXG5cdGJsYWNrdHJpYW5nbGVyaWdodDogJ1xcdTI1QjgnLFxuXHRibGFuazogJ1xcdTI0MjMnLFxuXHRibGsxMjogJ1xcdTI1OTInLFxuXHRibGsxNDogJ1xcdTI1OTEnLFxuXHRibGszNDogJ1xcdTI1OTMnLFxuXHRibG9jazogJ1xcdTI1ODgnLFxuXHRibmU6ICdcXHUwMDNEXFx1MjBFNScsXG5cdGJuZXF1aXY6ICdcXHUyMjYxXFx1MjBFNScsXG5cdGJOb3Q6ICdcXHUyQUVEJyxcblx0Ym5vdDogJ1xcdTIzMTAnLFxuXHRCb3BmOiAnXFx1RDgzNVxcdUREMzknLFxuXHRib3BmOiAnXFx1RDgzNVxcdURENTMnLFxuXHRib3Q6ICdcXHUyMkE1Jyxcblx0Ym90dG9tOiAnXFx1MjJBNScsXG5cdGJvd3RpZTogJ1xcdTIyQzgnLFxuXHRib3hib3g6ICdcXHUyOUM5Jyxcblx0Ym94REw6ICdcXHUyNTU3Jyxcblx0Ym94RGw6ICdcXHUyNTU2Jyxcblx0Ym94ZEw6ICdcXHUyNTU1Jyxcblx0Ym94ZGw6ICdcXHUyNTEwJyxcblx0Ym94RFI6ICdcXHUyNTU0Jyxcblx0Ym94RHI6ICdcXHUyNTUzJyxcblx0Ym94ZFI6ICdcXHUyNTUyJyxcblx0Ym94ZHI6ICdcXHUyNTBDJyxcblx0Ym94SDogJ1xcdTI1NTAnLFxuXHRib3hoOiAnXFx1MjUwMCcsXG5cdGJveEhEOiAnXFx1MjU2NicsXG5cdGJveEhkOiAnXFx1MjU2NCcsXG5cdGJveGhEOiAnXFx1MjU2NScsXG5cdGJveGhkOiAnXFx1MjUyQycsXG5cdGJveEhVOiAnXFx1MjU2OScsXG5cdGJveEh1OiAnXFx1MjU2NycsXG5cdGJveGhVOiAnXFx1MjU2OCcsXG5cdGJveGh1OiAnXFx1MjUzNCcsXG5cdGJveG1pbnVzOiAnXFx1MjI5RicsXG5cdGJveHBsdXM6ICdcXHUyMjlFJyxcblx0Ym94dGltZXM6ICdcXHUyMkEwJyxcblx0Ym94VUw6ICdcXHUyNTVEJyxcblx0Ym94VWw6ICdcXHUyNTVDJyxcblx0Ym94dUw6ICdcXHUyNTVCJyxcblx0Ym94dWw6ICdcXHUyNTE4Jyxcblx0Ym94VVI6ICdcXHUyNTVBJyxcblx0Ym94VXI6ICdcXHUyNTU5Jyxcblx0Ym94dVI6ICdcXHUyNTU4Jyxcblx0Ym94dXI6ICdcXHUyNTE0Jyxcblx0Ym94VjogJ1xcdTI1NTEnLFxuXHRib3h2OiAnXFx1MjUwMicsXG5cdGJveFZIOiAnXFx1MjU2QycsXG5cdGJveFZoOiAnXFx1MjU2QicsXG5cdGJveHZIOiAnXFx1MjU2QScsXG5cdGJveHZoOiAnXFx1MjUzQycsXG5cdGJveFZMOiAnXFx1MjU2MycsXG5cdGJveFZsOiAnXFx1MjU2MicsXG5cdGJveHZMOiAnXFx1MjU2MScsXG5cdGJveHZsOiAnXFx1MjUyNCcsXG5cdGJveFZSOiAnXFx1MjU2MCcsXG5cdGJveFZyOiAnXFx1MjU1RicsXG5cdGJveHZSOiAnXFx1MjU1RScsXG5cdGJveHZyOiAnXFx1MjUxQycsXG5cdGJwcmltZTogJ1xcdTIwMzUnLFxuXHRCcmV2ZTogJ1xcdTAyRDgnLFxuXHRicmV2ZTogJ1xcdTAyRDgnLFxuXHRicnZiYXI6ICdcXHUwMEE2Jyxcblx0QnNjcjogJ1xcdTIxMkMnLFxuXHRic2NyOiAnXFx1RDgzNVxcdURDQjcnLFxuXHRic2VtaTogJ1xcdTIwNEYnLFxuXHRic2ltOiAnXFx1MjIzRCcsXG5cdGJzaW1lOiAnXFx1MjJDRCcsXG5cdGJzb2w6ICdcXHUwMDVDJyxcblx0YnNvbGI6ICdcXHUyOUM1Jyxcblx0YnNvbGhzdWI6ICdcXHUyN0M4Jyxcblx0YnVsbDogJ1xcdTIwMjInLFxuXHRidWxsZXQ6ICdcXHUyMDIyJyxcblx0YnVtcDogJ1xcdTIyNEUnLFxuXHRidW1wRTogJ1xcdTJBQUUnLFxuXHRidW1wZTogJ1xcdTIyNEYnLFxuXHRCdW1wZXE6ICdcXHUyMjRFJyxcblx0YnVtcGVxOiAnXFx1MjI0RicsXG5cdENhY3V0ZTogJ1xcdTAxMDYnLFxuXHRjYWN1dGU6ICdcXHUwMTA3Jyxcblx0Q2FwOiAnXFx1MjJEMicsXG5cdGNhcDogJ1xcdTIyMjknLFxuXHRjYXBhbmQ6ICdcXHUyQTQ0Jyxcblx0Y2FwYnJjdXA6ICdcXHUyQTQ5Jyxcblx0Y2FwY2FwOiAnXFx1MkE0QicsXG5cdGNhcGN1cDogJ1xcdTJBNDcnLFxuXHRjYXBkb3Q6ICdcXHUyQTQwJyxcblx0Q2FwaXRhbERpZmZlcmVudGlhbEQ6ICdcXHUyMTQ1Jyxcblx0Y2FwczogJ1xcdTIyMjlcXHVGRTAwJyxcblx0Y2FyZXQ6ICdcXHUyMDQxJyxcblx0Y2Fyb246ICdcXHUwMkM3Jyxcblx0Q2F5bGV5czogJ1xcdTIxMkQnLFxuXHRjY2FwczogJ1xcdTJBNEQnLFxuXHRDY2Fyb246ICdcXHUwMTBDJyxcblx0Y2Nhcm9uOiAnXFx1MDEwRCcsXG5cdENjZWRpbDogJ1xcdTAwQzcnLFxuXHRjY2VkaWw6ICdcXHUwMEU3Jyxcblx0Q2NpcmM6ICdcXHUwMTA4Jyxcblx0Y2NpcmM6ICdcXHUwMTA5Jyxcblx0Q2NvbmludDogJ1xcdTIyMzAnLFxuXHRjY3VwczogJ1xcdTJBNEMnLFxuXHRjY3Vwc3NtOiAnXFx1MkE1MCcsXG5cdENkb3Q6ICdcXHUwMTBBJyxcblx0Y2RvdDogJ1xcdTAxMEInLFxuXHRjZWRpbDogJ1xcdTAwQjgnLFxuXHRDZWRpbGxhOiAnXFx1MDBCOCcsXG5cdGNlbXB0eXY6ICdcXHUyOUIyJyxcblx0Y2VudDogJ1xcdTAwQTInLFxuXHRDZW50ZXJEb3Q6ICdcXHUwMEI3Jyxcblx0Y2VudGVyZG90OiAnXFx1MDBCNycsXG5cdENmcjogJ1xcdTIxMkQnLFxuXHRjZnI6ICdcXHVEODM1XFx1REQyMCcsXG5cdENIY3k6ICdcXHUwNDI3Jyxcblx0Y2hjeTogJ1xcdTA0NDcnLFxuXHRjaGVjazogJ1xcdTI3MTMnLFxuXHRjaGVja21hcms6ICdcXHUyNzEzJyxcblx0Q2hpOiAnXFx1MDNBNycsXG5cdGNoaTogJ1xcdTAzQzcnLFxuXHRjaXI6ICdcXHUyNUNCJyxcblx0Y2lyYzogJ1xcdTAyQzYnLFxuXHRjaXJjZXE6ICdcXHUyMjU3Jyxcblx0Y2lyY2xlYXJyb3dsZWZ0OiAnXFx1MjFCQScsXG5cdGNpcmNsZWFycm93cmlnaHQ6ICdcXHUyMUJCJyxcblx0Y2lyY2xlZGFzdDogJ1xcdTIyOUInLFxuXHRjaXJjbGVkY2lyYzogJ1xcdTIyOUEnLFxuXHRjaXJjbGVkZGFzaDogJ1xcdTIyOUQnLFxuXHRDaXJjbGVEb3Q6ICdcXHUyMjk5Jyxcblx0Y2lyY2xlZFI6ICdcXHUwMEFFJyxcblx0Y2lyY2xlZFM6ICdcXHUyNEM4Jyxcblx0Q2lyY2xlTWludXM6ICdcXHUyMjk2Jyxcblx0Q2lyY2xlUGx1czogJ1xcdTIyOTUnLFxuXHRDaXJjbGVUaW1lczogJ1xcdTIyOTcnLFxuXHRjaXJFOiAnXFx1MjlDMycsXG5cdGNpcmU6ICdcXHUyMjU3Jyxcblx0Y2lyZm5pbnQ6ICdcXHUyQTEwJyxcblx0Y2lybWlkOiAnXFx1MkFFRicsXG5cdGNpcnNjaXI6ICdcXHUyOUMyJyxcblx0Q2xvY2t3aXNlQ29udG91ckludGVncmFsOiAnXFx1MjIzMicsXG5cdENsb3NlQ3VybHlEb3VibGVRdW90ZTogJ1xcdTIwMUQnLFxuXHRDbG9zZUN1cmx5UXVvdGU6ICdcXHUyMDE5Jyxcblx0Y2x1YnM6ICdcXHUyNjYzJyxcblx0Y2x1YnN1aXQ6ICdcXHUyNjYzJyxcblx0Q29sb246ICdcXHUyMjM3Jyxcblx0Y29sb246ICdcXHUwMDNBJyxcblx0Q29sb25lOiAnXFx1MkE3NCcsXG5cdGNvbG9uZTogJ1xcdTIyNTQnLFxuXHRjb2xvbmVxOiAnXFx1MjI1NCcsXG5cdGNvbW1hOiAnXFx1MDAyQycsXG5cdGNvbW1hdDogJ1xcdTAwNDAnLFxuXHRjb21wOiAnXFx1MjIwMScsXG5cdGNvbXBmbjogJ1xcdTIyMTgnLFxuXHRjb21wbGVtZW50OiAnXFx1MjIwMScsXG5cdGNvbXBsZXhlczogJ1xcdTIxMDInLFxuXHRjb25nOiAnXFx1MjI0NScsXG5cdGNvbmdkb3Q6ICdcXHUyQTZEJyxcblx0Q29uZ3J1ZW50OiAnXFx1MjI2MScsXG5cdENvbmludDogJ1xcdTIyMkYnLFxuXHRjb25pbnQ6ICdcXHUyMjJFJyxcblx0Q29udG91ckludGVncmFsOiAnXFx1MjIyRScsXG5cdENvcGY6ICdcXHUyMTAyJyxcblx0Y29wZjogJ1xcdUQ4MzVcXHVERDU0Jyxcblx0Y29wcm9kOiAnXFx1MjIxMCcsXG5cdENvcHJvZHVjdDogJ1xcdTIyMTAnLFxuXHRDT1BZOiAnXFx1MDBBOScsXG5cdGNvcHk6ICdcXHUwMEE5Jyxcblx0Y29weXNyOiAnXFx1MjExNycsXG5cdENvdW50ZXJDbG9ja3dpc2VDb250b3VySW50ZWdyYWw6ICdcXHUyMjMzJyxcblx0Y3JhcnI6ICdcXHUyMUI1Jyxcblx0Q3Jvc3M6ICdcXHUyQTJGJyxcblx0Y3Jvc3M6ICdcXHUyNzE3Jyxcblx0Q3NjcjogJ1xcdUQ4MzVcXHVEQzlFJyxcblx0Y3NjcjogJ1xcdUQ4MzVcXHVEQ0I4Jyxcblx0Y3N1YjogJ1xcdTJBQ0YnLFxuXHRjc3ViZTogJ1xcdTJBRDEnLFxuXHRjc3VwOiAnXFx1MkFEMCcsXG5cdGNzdXBlOiAnXFx1MkFEMicsXG5cdGN0ZG90OiAnXFx1MjJFRicsXG5cdGN1ZGFycmw6ICdcXHUyOTM4Jyxcblx0Y3VkYXJycjogJ1xcdTI5MzUnLFxuXHRjdWVwcjogJ1xcdTIyREUnLFxuXHRjdWVzYzogJ1xcdTIyREYnLFxuXHRjdWxhcnI6ICdcXHUyMUI2Jyxcblx0Y3VsYXJycDogJ1xcdTI5M0QnLFxuXHRDdXA6ICdcXHUyMkQzJyxcblx0Y3VwOiAnXFx1MjIyQScsXG5cdGN1cGJyY2FwOiAnXFx1MkE0OCcsXG5cdEN1cENhcDogJ1xcdTIyNEQnLFxuXHRjdXBjYXA6ICdcXHUyQTQ2Jyxcblx0Y3VwY3VwOiAnXFx1MkE0QScsXG5cdGN1cGRvdDogJ1xcdTIyOEQnLFxuXHRjdXBvcjogJ1xcdTJBNDUnLFxuXHRjdXBzOiAnXFx1MjIyQVxcdUZFMDAnLFxuXHRjdXJhcnI6ICdcXHUyMUI3Jyxcblx0Y3VyYXJybTogJ1xcdTI5M0MnLFxuXHRjdXJseWVxcHJlYzogJ1xcdTIyREUnLFxuXHRjdXJseWVxc3VjYzogJ1xcdTIyREYnLFxuXHRjdXJseXZlZTogJ1xcdTIyQ0UnLFxuXHRjdXJseXdlZGdlOiAnXFx1MjJDRicsXG5cdGN1cnJlbjogJ1xcdTAwQTQnLFxuXHRjdXJ2ZWFycm93bGVmdDogJ1xcdTIxQjYnLFxuXHRjdXJ2ZWFycm93cmlnaHQ6ICdcXHUyMUI3Jyxcblx0Y3V2ZWU6ICdcXHUyMkNFJyxcblx0Y3V3ZWQ6ICdcXHUyMkNGJyxcblx0Y3djb25pbnQ6ICdcXHUyMjMyJyxcblx0Y3dpbnQ6ICdcXHUyMjMxJyxcblx0Y3lsY3R5OiAnXFx1MjMyRCcsXG5cdERhZ2dlcjogJ1xcdTIwMjEnLFxuXHRkYWdnZXI6ICdcXHUyMDIwJyxcblx0ZGFsZXRoOiAnXFx1MjEzOCcsXG5cdERhcnI6ICdcXHUyMUExJyxcblx0ZEFycjogJ1xcdTIxRDMnLFxuXHRkYXJyOiAnXFx1MjE5MycsXG5cdGRhc2g6ICdcXHUyMDEwJyxcblx0RGFzaHY6ICdcXHUyQUU0Jyxcblx0ZGFzaHY6ICdcXHUyMkEzJyxcblx0ZGJrYXJvdzogJ1xcdTI5MEYnLFxuXHRkYmxhYzogJ1xcdTAyREQnLFxuXHREY2Fyb246ICdcXHUwMTBFJyxcblx0ZGNhcm9uOiAnXFx1MDEwRicsXG5cdERjeTogJ1xcdTA0MTQnLFxuXHRkY3k6ICdcXHUwNDM0Jyxcblx0REQ6ICdcXHUyMTQ1Jyxcblx0ZGQ6ICdcXHUyMTQ2Jyxcblx0ZGRhZ2dlcjogJ1xcdTIwMjEnLFxuXHRkZGFycjogJ1xcdTIxQ0EnLFxuXHRERG90cmFoZDogJ1xcdTI5MTEnLFxuXHRkZG90c2VxOiAnXFx1MkE3NycsXG5cdGRlZzogJ1xcdTAwQjAnLFxuXHREZWw6ICdcXHUyMjA3Jyxcblx0RGVsdGE6ICdcXHUwMzk0Jyxcblx0ZGVsdGE6ICdcXHUwM0I0Jyxcblx0ZGVtcHR5djogJ1xcdTI5QjEnLFxuXHRkZmlzaHQ6ICdcXHUyOTdGJyxcblx0RGZyOiAnXFx1RDgzNVxcdUREMDcnLFxuXHRkZnI6ICdcXHVEODM1XFx1REQyMScsXG5cdGRIYXI6ICdcXHUyOTY1Jyxcblx0ZGhhcmw6ICdcXHUyMUMzJyxcblx0ZGhhcnI6ICdcXHUyMUMyJyxcblx0RGlhY3JpdGljYWxBY3V0ZTogJ1xcdTAwQjQnLFxuXHREaWFjcml0aWNhbERvdDogJ1xcdTAyRDknLFxuXHREaWFjcml0aWNhbERvdWJsZUFjdXRlOiAnXFx1MDJERCcsXG5cdERpYWNyaXRpY2FsR3JhdmU6ICdcXHUwMDYwJyxcblx0RGlhY3JpdGljYWxUaWxkZTogJ1xcdTAyREMnLFxuXHRkaWFtOiAnXFx1MjJDNCcsXG5cdERpYW1vbmQ6ICdcXHUyMkM0Jyxcblx0ZGlhbW9uZDogJ1xcdTIyQzQnLFxuXHRkaWFtb25kc3VpdDogJ1xcdTI2NjYnLFxuXHRkaWFtczogJ1xcdTI2NjYnLFxuXHRkaWU6ICdcXHUwMEE4Jyxcblx0RGlmZmVyZW50aWFsRDogJ1xcdTIxNDYnLFxuXHRkaWdhbW1hOiAnXFx1MDNERCcsXG5cdGRpc2luOiAnXFx1MjJGMicsXG5cdGRpdjogJ1xcdTAwRjcnLFxuXHRkaXZpZGU6ICdcXHUwMEY3Jyxcblx0ZGl2aWRlb250aW1lczogJ1xcdTIyQzcnLFxuXHRkaXZvbng6ICdcXHUyMkM3Jyxcblx0REpjeTogJ1xcdTA0MDInLFxuXHRkamN5OiAnXFx1MDQ1MicsXG5cdGRsY29ybjogJ1xcdTIzMUUnLFxuXHRkbGNyb3A6ICdcXHUyMzBEJyxcblx0ZG9sbGFyOiAnXFx1MDAyNCcsXG5cdERvcGY6ICdcXHVEODM1XFx1REQzQicsXG5cdGRvcGY6ICdcXHVEODM1XFx1REQ1NScsXG5cdERvdDogJ1xcdTAwQTgnLFxuXHRkb3Q6ICdcXHUwMkQ5Jyxcblx0RG90RG90OiAnXFx1MjBEQycsXG5cdGRvdGVxOiAnXFx1MjI1MCcsXG5cdGRvdGVxZG90OiAnXFx1MjI1MScsXG5cdERvdEVxdWFsOiAnXFx1MjI1MCcsXG5cdGRvdG1pbnVzOiAnXFx1MjIzOCcsXG5cdGRvdHBsdXM6ICdcXHUyMjE0Jyxcblx0ZG90c3F1YXJlOiAnXFx1MjJBMScsXG5cdGRvdWJsZWJhcndlZGdlOiAnXFx1MjMwNicsXG5cdERvdWJsZUNvbnRvdXJJbnRlZ3JhbDogJ1xcdTIyMkYnLFxuXHREb3VibGVEb3Q6ICdcXHUwMEE4Jyxcblx0RG91YmxlRG93bkFycm93OiAnXFx1MjFEMycsXG5cdERvdWJsZUxlZnRBcnJvdzogJ1xcdTIxRDAnLFxuXHREb3VibGVMZWZ0UmlnaHRBcnJvdzogJ1xcdTIxRDQnLFxuXHREb3VibGVMZWZ0VGVlOiAnXFx1MkFFNCcsXG5cdERvdWJsZUxvbmdMZWZ0QXJyb3c6ICdcXHUyN0Y4Jyxcblx0RG91YmxlTG9uZ0xlZnRSaWdodEFycm93OiAnXFx1MjdGQScsXG5cdERvdWJsZUxvbmdSaWdodEFycm93OiAnXFx1MjdGOScsXG5cdERvdWJsZVJpZ2h0QXJyb3c6ICdcXHUyMUQyJyxcblx0RG91YmxlUmlnaHRUZWU6ICdcXHUyMkE4Jyxcblx0RG91YmxlVXBBcnJvdzogJ1xcdTIxRDEnLFxuXHREb3VibGVVcERvd25BcnJvdzogJ1xcdTIxRDUnLFxuXHREb3VibGVWZXJ0aWNhbEJhcjogJ1xcdTIyMjUnLFxuXHREb3duQXJyb3c6ICdcXHUyMTkzJyxcblx0RG93bmFycm93OiAnXFx1MjFEMycsXG5cdGRvd25hcnJvdzogJ1xcdTIxOTMnLFxuXHREb3duQXJyb3dCYXI6ICdcXHUyOTEzJyxcblx0RG93bkFycm93VXBBcnJvdzogJ1xcdTIxRjUnLFxuXHREb3duQnJldmU6ICdcXHUwMzExJyxcblx0ZG93bmRvd25hcnJvd3M6ICdcXHUyMUNBJyxcblx0ZG93bmhhcnBvb25sZWZ0OiAnXFx1MjFDMycsXG5cdGRvd25oYXJwb29ucmlnaHQ6ICdcXHUyMUMyJyxcblx0RG93bkxlZnRSaWdodFZlY3RvcjogJ1xcdTI5NTAnLFxuXHREb3duTGVmdFRlZVZlY3RvcjogJ1xcdTI5NUUnLFxuXHREb3duTGVmdFZlY3RvcjogJ1xcdTIxQkQnLFxuXHREb3duTGVmdFZlY3RvckJhcjogJ1xcdTI5NTYnLFxuXHREb3duUmlnaHRUZWVWZWN0b3I6ICdcXHUyOTVGJyxcblx0RG93blJpZ2h0VmVjdG9yOiAnXFx1MjFDMScsXG5cdERvd25SaWdodFZlY3RvckJhcjogJ1xcdTI5NTcnLFxuXHREb3duVGVlOiAnXFx1MjJBNCcsXG5cdERvd25UZWVBcnJvdzogJ1xcdTIxQTcnLFxuXHRkcmJrYXJvdzogJ1xcdTI5MTAnLFxuXHRkcmNvcm46ICdcXHUyMzFGJyxcblx0ZHJjcm9wOiAnXFx1MjMwQycsXG5cdERzY3I6ICdcXHVEODM1XFx1REM5RicsXG5cdGRzY3I6ICdcXHVEODM1XFx1RENCOScsXG5cdERTY3k6ICdcXHUwNDA1Jyxcblx0ZHNjeTogJ1xcdTA0NTUnLFxuXHRkc29sOiAnXFx1MjlGNicsXG5cdERzdHJvazogJ1xcdTAxMTAnLFxuXHRkc3Ryb2s6ICdcXHUwMTExJyxcblx0ZHRkb3Q6ICdcXHUyMkYxJyxcblx0ZHRyaTogJ1xcdTI1QkYnLFxuXHRkdHJpZjogJ1xcdTI1QkUnLFxuXHRkdWFycjogJ1xcdTIxRjUnLFxuXHRkdWhhcjogJ1xcdTI5NkYnLFxuXHRkd2FuZ2xlOiAnXFx1MjlBNicsXG5cdERaY3k6ICdcXHUwNDBGJyxcblx0ZHpjeTogJ1xcdTA0NUYnLFxuXHRkemlncmFycjogJ1xcdTI3RkYnLFxuXHRFYWN1dGU6ICdcXHUwMEM5Jyxcblx0ZWFjdXRlOiAnXFx1MDBFOScsXG5cdGVhc3RlcjogJ1xcdTJBNkUnLFxuXHRFY2Fyb246ICdcXHUwMTFBJyxcblx0ZWNhcm9uOiAnXFx1MDExQicsXG5cdGVjaXI6ICdcXHUyMjU2Jyxcblx0RWNpcmM6ICdcXHUwMENBJyxcblx0ZWNpcmM6ICdcXHUwMEVBJyxcblx0ZWNvbG9uOiAnXFx1MjI1NScsXG5cdEVjeTogJ1xcdTA0MkQnLFxuXHRlY3k6ICdcXHUwNDREJyxcblx0ZUREb3Q6ICdcXHUyQTc3Jyxcblx0RWRvdDogJ1xcdTAxMTYnLFxuXHRlRG90OiAnXFx1MjI1MScsXG5cdGVkb3Q6ICdcXHUwMTE3Jyxcblx0ZWU6ICdcXHUyMTQ3Jyxcblx0ZWZEb3Q6ICdcXHUyMjUyJyxcblx0RWZyOiAnXFx1RDgzNVxcdUREMDgnLFxuXHRlZnI6ICdcXHVEODM1XFx1REQyMicsXG5cdGVnOiAnXFx1MkE5QScsXG5cdEVncmF2ZTogJ1xcdTAwQzgnLFxuXHRlZ3JhdmU6ICdcXHUwMEU4Jyxcblx0ZWdzOiAnXFx1MkE5NicsXG5cdGVnc2RvdDogJ1xcdTJBOTgnLFxuXHRlbDogJ1xcdTJBOTknLFxuXHRFbGVtZW50OiAnXFx1MjIwOCcsXG5cdGVsaW50ZXJzOiAnXFx1MjNFNycsXG5cdGVsbDogJ1xcdTIxMTMnLFxuXHRlbHM6ICdcXHUyQTk1Jyxcblx0ZWxzZG90OiAnXFx1MkE5NycsXG5cdEVtYWNyOiAnXFx1MDExMicsXG5cdGVtYWNyOiAnXFx1MDExMycsXG5cdGVtcHR5OiAnXFx1MjIwNScsXG5cdGVtcHR5c2V0OiAnXFx1MjIwNScsXG5cdEVtcHR5U21hbGxTcXVhcmU6ICdcXHUyNUZCJyxcblx0ZW1wdHl2OiAnXFx1MjIwNScsXG5cdEVtcHR5VmVyeVNtYWxsU3F1YXJlOiAnXFx1MjVBQicsXG5cdGVtc3A6ICdcXHUyMDAzJyxcblx0ZW1zcDEzOiAnXFx1MjAwNCcsXG5cdGVtc3AxNDogJ1xcdTIwMDUnLFxuXHRFTkc6ICdcXHUwMTRBJyxcblx0ZW5nOiAnXFx1MDE0QicsXG5cdGVuc3A6ICdcXHUyMDAyJyxcblx0RW9nb246ICdcXHUwMTE4Jyxcblx0ZW9nb246ICdcXHUwMTE5Jyxcblx0RW9wZjogJ1xcdUQ4MzVcXHVERDNDJyxcblx0ZW9wZjogJ1xcdUQ4MzVcXHVERDU2Jyxcblx0ZXBhcjogJ1xcdTIyRDUnLFxuXHRlcGFyc2w6ICdcXHUyOUUzJyxcblx0ZXBsdXM6ICdcXHUyQTcxJyxcblx0ZXBzaTogJ1xcdTAzQjUnLFxuXHRFcHNpbG9uOiAnXFx1MDM5NScsXG5cdGVwc2lsb246ICdcXHUwM0I1Jyxcblx0ZXBzaXY6ICdcXHUwM0Y1Jyxcblx0ZXFjaXJjOiAnXFx1MjI1NicsXG5cdGVxY29sb246ICdcXHUyMjU1Jyxcblx0ZXFzaW06ICdcXHUyMjQyJyxcblx0ZXFzbGFudGd0cjogJ1xcdTJBOTYnLFxuXHRlcXNsYW50bGVzczogJ1xcdTJBOTUnLFxuXHRFcXVhbDogJ1xcdTJBNzUnLFxuXHRlcXVhbHM6ICdcXHUwMDNEJyxcblx0RXF1YWxUaWxkZTogJ1xcdTIyNDInLFxuXHRlcXVlc3Q6ICdcXHUyMjVGJyxcblx0RXF1aWxpYnJpdW06ICdcXHUyMUNDJyxcblx0ZXF1aXY6ICdcXHUyMjYxJyxcblx0ZXF1aXZERDogJ1xcdTJBNzgnLFxuXHRlcXZwYXJzbDogJ1xcdTI5RTUnLFxuXHRlcmFycjogJ1xcdTI5NzEnLFxuXHRlckRvdDogJ1xcdTIyNTMnLFxuXHRFc2NyOiAnXFx1MjEzMCcsXG5cdGVzY3I6ICdcXHUyMTJGJyxcblx0ZXNkb3Q6ICdcXHUyMjUwJyxcblx0RXNpbTogJ1xcdTJBNzMnLFxuXHRlc2ltOiAnXFx1MjI0MicsXG5cdEV0YTogJ1xcdTAzOTcnLFxuXHRldGE6ICdcXHUwM0I3Jyxcblx0RVRIOiAnXFx1MDBEMCcsXG5cdGV0aDogJ1xcdTAwRjAnLFxuXHRFdW1sOiAnXFx1MDBDQicsXG5cdGV1bWw6ICdcXHUwMEVCJyxcblx0ZXVybzogJ1xcdTIwQUMnLFxuXHRleGNsOiAnXFx1MDAyMScsXG5cdGV4aXN0OiAnXFx1MjIwMycsXG5cdEV4aXN0czogJ1xcdTIyMDMnLFxuXHRleHBlY3RhdGlvbjogJ1xcdTIxMzAnLFxuXHRFeHBvbmVudGlhbEU6ICdcXHUyMTQ3Jyxcblx0ZXhwb25lbnRpYWxlOiAnXFx1MjE0NycsXG5cdGZhbGxpbmdkb3RzZXE6ICdcXHUyMjUyJyxcblx0RmN5OiAnXFx1MDQyNCcsXG5cdGZjeTogJ1xcdTA0NDQnLFxuXHRmZW1hbGU6ICdcXHUyNjQwJyxcblx0ZmZpbGlnOiAnXFx1RkIwMycsXG5cdGZmbGlnOiAnXFx1RkIwMCcsXG5cdGZmbGxpZzogJ1xcdUZCMDQnLFxuXHRGZnI6ICdcXHVEODM1XFx1REQwOScsXG5cdGZmcjogJ1xcdUQ4MzVcXHVERDIzJyxcblx0ZmlsaWc6ICdcXHVGQjAxJyxcblx0RmlsbGVkU21hbGxTcXVhcmU6ICdcXHUyNUZDJyxcblx0RmlsbGVkVmVyeVNtYWxsU3F1YXJlOiAnXFx1MjVBQScsXG5cdGZqbGlnOiAnXFx1MDA2NlxcdTAwNkEnLFxuXHRmbGF0OiAnXFx1MjY2RCcsXG5cdGZsbGlnOiAnXFx1RkIwMicsXG5cdGZsdG5zOiAnXFx1MjVCMScsXG5cdGZub2Y6ICdcXHUwMTkyJyxcblx0Rm9wZjogJ1xcdUQ4MzVcXHVERDNEJyxcblx0Zm9wZjogJ1xcdUQ4MzVcXHVERDU3Jyxcblx0Rm9yQWxsOiAnXFx1MjIwMCcsXG5cdGZvcmFsbDogJ1xcdTIyMDAnLFxuXHRmb3JrOiAnXFx1MjJENCcsXG5cdGZvcmt2OiAnXFx1MkFEOScsXG5cdEZvdXJpZXJ0cmY6ICdcXHUyMTMxJyxcblx0ZnBhcnRpbnQ6ICdcXHUyQTBEJyxcblx0ZnJhYzEyOiAnXFx1MDBCRCcsXG5cdGZyYWMxMzogJ1xcdTIxNTMnLFxuXHRmcmFjMTQ6ICdcXHUwMEJDJyxcblx0ZnJhYzE1OiAnXFx1MjE1NScsXG5cdGZyYWMxNjogJ1xcdTIxNTknLFxuXHRmcmFjMTg6ICdcXHUyMTVCJyxcblx0ZnJhYzIzOiAnXFx1MjE1NCcsXG5cdGZyYWMyNTogJ1xcdTIxNTYnLFxuXHRmcmFjMzQ6ICdcXHUwMEJFJyxcblx0ZnJhYzM1OiAnXFx1MjE1NycsXG5cdGZyYWMzODogJ1xcdTIxNUMnLFxuXHRmcmFjNDU6ICdcXHUyMTU4Jyxcblx0ZnJhYzU2OiAnXFx1MjE1QScsXG5cdGZyYWM1ODogJ1xcdTIxNUQnLFxuXHRmcmFjNzg6ICdcXHUyMTVFJyxcblx0ZnJhc2w6ICdcXHUyMDQ0Jyxcblx0ZnJvd246ICdcXHUyMzIyJyxcblx0RnNjcjogJ1xcdTIxMzEnLFxuXHRmc2NyOiAnXFx1RDgzNVxcdURDQkInLFxuXHRnYWN1dGU6ICdcXHUwMUY1Jyxcblx0R2FtbWE6ICdcXHUwMzkzJyxcblx0Z2FtbWE6ICdcXHUwM0IzJyxcblx0R2FtbWFkOiAnXFx1MDNEQycsXG5cdGdhbW1hZDogJ1xcdTAzREQnLFxuXHRnYXA6ICdcXHUyQTg2Jyxcblx0R2JyZXZlOiAnXFx1MDExRScsXG5cdGdicmV2ZTogJ1xcdTAxMUYnLFxuXHRHY2VkaWw6ICdcXHUwMTIyJyxcblx0R2NpcmM6ICdcXHUwMTFDJyxcblx0Z2NpcmM6ICdcXHUwMTFEJyxcblx0R2N5OiAnXFx1MDQxMycsXG5cdGdjeTogJ1xcdTA0MzMnLFxuXHRHZG90OiAnXFx1MDEyMCcsXG5cdGdkb3Q6ICdcXHUwMTIxJyxcblx0Z0U6ICdcXHUyMjY3Jyxcblx0Z2U6ICdcXHUyMjY1Jyxcblx0Z0VsOiAnXFx1MkE4QycsXG5cdGdlbDogJ1xcdTIyREInLFxuXHRnZXE6ICdcXHUyMjY1Jyxcblx0Z2VxcTogJ1xcdTIyNjcnLFxuXHRnZXFzbGFudDogJ1xcdTJBN0UnLFxuXHRnZXM6ICdcXHUyQTdFJyxcblx0Z2VzY2M6ICdcXHUyQUE5Jyxcblx0Z2VzZG90OiAnXFx1MkE4MCcsXG5cdGdlc2RvdG86ICdcXHUyQTgyJyxcblx0Z2VzZG90b2w6ICdcXHUyQTg0Jyxcblx0Z2VzbDogJ1xcdTIyREJcXHVGRTAwJyxcblx0Z2VzbGVzOiAnXFx1MkE5NCcsXG5cdEdmcjogJ1xcdUQ4MzVcXHVERDBBJyxcblx0Z2ZyOiAnXFx1RDgzNVxcdUREMjQnLFxuXHRHZzogJ1xcdTIyRDknLFxuXHRnZzogJ1xcdTIyNkInLFxuXHRnZ2c6ICdcXHUyMkQ5Jyxcblx0Z2ltZWw6ICdcXHUyMTM3Jyxcblx0R0pjeTogJ1xcdTA0MDMnLFxuXHRnamN5OiAnXFx1MDQ1MycsXG5cdGdsOiAnXFx1MjI3NycsXG5cdGdsYTogJ1xcdTJBQTUnLFxuXHRnbEU6ICdcXHUyQTkyJyxcblx0Z2xqOiAnXFx1MkFBNCcsXG5cdGduYXA6ICdcXHUyQThBJyxcblx0Z25hcHByb3g6ICdcXHUyQThBJyxcblx0Z25FOiAnXFx1MjI2OScsXG5cdGduZTogJ1xcdTJBODgnLFxuXHRnbmVxOiAnXFx1MkE4OCcsXG5cdGduZXFxOiAnXFx1MjI2OScsXG5cdGduc2ltOiAnXFx1MjJFNycsXG5cdEdvcGY6ICdcXHVEODM1XFx1REQzRScsXG5cdGdvcGY6ICdcXHVEODM1XFx1REQ1OCcsXG5cdGdyYXZlOiAnXFx1MDA2MCcsXG5cdEdyZWF0ZXJFcXVhbDogJ1xcdTIyNjUnLFxuXHRHcmVhdGVyRXF1YWxMZXNzOiAnXFx1MjJEQicsXG5cdEdyZWF0ZXJGdWxsRXF1YWw6ICdcXHUyMjY3Jyxcblx0R3JlYXRlckdyZWF0ZXI6ICdcXHUyQUEyJyxcblx0R3JlYXRlckxlc3M6ICdcXHUyMjc3Jyxcblx0R3JlYXRlclNsYW50RXF1YWw6ICdcXHUyQTdFJyxcblx0R3JlYXRlclRpbGRlOiAnXFx1MjI3MycsXG5cdEdzY3I6ICdcXHVEODM1XFx1RENBMicsXG5cdGdzY3I6ICdcXHUyMTBBJyxcblx0Z3NpbTogJ1xcdTIyNzMnLFxuXHRnc2ltZTogJ1xcdTJBOEUnLFxuXHRnc2ltbDogJ1xcdTJBOTAnLFxuXHRHdDogJ1xcdTIyNkInLFxuXHRHVDogJ1xcdTAwM0UnLFxuXHRndDogJ1xcdTAwM0UnLFxuXHRndGNjOiAnXFx1MkFBNycsXG5cdGd0Y2lyOiAnXFx1MkE3QScsXG5cdGd0ZG90OiAnXFx1MjJENycsXG5cdGd0bFBhcjogJ1xcdTI5OTUnLFxuXHRndHF1ZXN0OiAnXFx1MkE3QycsXG5cdGd0cmFwcHJveDogJ1xcdTJBODYnLFxuXHRndHJhcnI6ICdcXHUyOTc4Jyxcblx0Z3RyZG90OiAnXFx1MjJENycsXG5cdGd0cmVxbGVzczogJ1xcdTIyREInLFxuXHRndHJlcXFsZXNzOiAnXFx1MkE4QycsXG5cdGd0cmxlc3M6ICdcXHUyMjc3Jyxcblx0Z3Ryc2ltOiAnXFx1MjI3MycsXG5cdGd2ZXJ0bmVxcTogJ1xcdTIyNjlcXHVGRTAwJyxcblx0Z3ZuRTogJ1xcdTIyNjlcXHVGRTAwJyxcblx0SGFjZWs6ICdcXHUwMkM3Jyxcblx0aGFpcnNwOiAnXFx1MjAwQScsXG5cdGhhbGY6ICdcXHUwMEJEJyxcblx0aGFtaWx0OiAnXFx1MjEwQicsXG5cdEhBUkRjeTogJ1xcdTA0MkEnLFxuXHRoYXJkY3k6ICdcXHUwNDRBJyxcblx0aEFycjogJ1xcdTIxRDQnLFxuXHRoYXJyOiAnXFx1MjE5NCcsXG5cdGhhcnJjaXI6ICdcXHUyOTQ4Jyxcblx0aGFycnc6ICdcXHUyMUFEJyxcblx0SGF0OiAnXFx1MDA1RScsXG5cdGhiYXI6ICdcXHUyMTBGJyxcblx0SGNpcmM6ICdcXHUwMTI0Jyxcblx0aGNpcmM6ICdcXHUwMTI1Jyxcblx0aGVhcnRzOiAnXFx1MjY2NScsXG5cdGhlYXJ0c3VpdDogJ1xcdTI2NjUnLFxuXHRoZWxsaXA6ICdcXHUyMDI2Jyxcblx0aGVyY29uOiAnXFx1MjJCOScsXG5cdEhmcjogJ1xcdTIxMEMnLFxuXHRoZnI6ICdcXHVEODM1XFx1REQyNScsXG5cdEhpbGJlcnRTcGFjZTogJ1xcdTIxMEInLFxuXHRoa3NlYXJvdzogJ1xcdTI5MjUnLFxuXHRoa3N3YXJvdzogJ1xcdTI5MjYnLFxuXHRob2FycjogJ1xcdTIxRkYnLFxuXHRob210aHQ6ICdcXHUyMjNCJyxcblx0aG9va2xlZnRhcnJvdzogJ1xcdTIxQTknLFxuXHRob29rcmlnaHRhcnJvdzogJ1xcdTIxQUEnLFxuXHRIb3BmOiAnXFx1MjEwRCcsXG5cdGhvcGY6ICdcXHVEODM1XFx1REQ1OScsXG5cdGhvcmJhcjogJ1xcdTIwMTUnLFxuXHRIb3Jpem9udGFsTGluZTogJ1xcdTI1MDAnLFxuXHRIc2NyOiAnXFx1MjEwQicsXG5cdGhzY3I6ICdcXHVEODM1XFx1RENCRCcsXG5cdGhzbGFzaDogJ1xcdTIxMEYnLFxuXHRIc3Ryb2s6ICdcXHUwMTI2Jyxcblx0aHN0cm9rOiAnXFx1MDEyNycsXG5cdEh1bXBEb3duSHVtcDogJ1xcdTIyNEUnLFxuXHRIdW1wRXF1YWw6ICdcXHUyMjRGJyxcblx0aHlidWxsOiAnXFx1MjA0MycsXG5cdGh5cGhlbjogJ1xcdTIwMTAnLFxuXHRJYWN1dGU6ICdcXHUwMENEJyxcblx0aWFjdXRlOiAnXFx1MDBFRCcsXG5cdGljOiAnXFx1MjA2MycsXG5cdEljaXJjOiAnXFx1MDBDRScsXG5cdGljaXJjOiAnXFx1MDBFRScsXG5cdEljeTogJ1xcdTA0MTgnLFxuXHRpY3k6ICdcXHUwNDM4Jyxcblx0SWRvdDogJ1xcdTAxMzAnLFxuXHRJRWN5OiAnXFx1MDQxNScsXG5cdGllY3k6ICdcXHUwNDM1Jyxcblx0aWV4Y2w6ICdcXHUwMEExJyxcblx0aWZmOiAnXFx1MjFENCcsXG5cdElmcjogJ1xcdTIxMTEnLFxuXHRpZnI6ICdcXHVEODM1XFx1REQyNicsXG5cdElncmF2ZTogJ1xcdTAwQ0MnLFxuXHRpZ3JhdmU6ICdcXHUwMEVDJyxcblx0aWk6ICdcXHUyMTQ4Jyxcblx0aWlpaW50OiAnXFx1MkEwQycsXG5cdGlpaW50OiAnXFx1MjIyRCcsXG5cdGlpbmZpbjogJ1xcdTI5REMnLFxuXHRpaW90YTogJ1xcdTIxMjknLFxuXHRJSmxpZzogJ1xcdTAxMzInLFxuXHRpamxpZzogJ1xcdTAxMzMnLFxuXHRJbTogJ1xcdTIxMTEnLFxuXHRJbWFjcjogJ1xcdTAxMkEnLFxuXHRpbWFjcjogJ1xcdTAxMkInLFxuXHRpbWFnZTogJ1xcdTIxMTEnLFxuXHRJbWFnaW5hcnlJOiAnXFx1MjE0OCcsXG5cdGltYWdsaW5lOiAnXFx1MjExMCcsXG5cdGltYWdwYXJ0OiAnXFx1MjExMScsXG5cdGltYXRoOiAnXFx1MDEzMScsXG5cdGltb2Y6ICdcXHUyMkI3Jyxcblx0aW1wZWQ6ICdcXHUwMUI1Jyxcblx0SW1wbGllczogJ1xcdTIxRDInLFxuXHRpbjogJ1xcdTIyMDgnLFxuXHRpbmNhcmU6ICdcXHUyMTA1Jyxcblx0aW5maW46ICdcXHUyMjFFJyxcblx0aW5maW50aWU6ICdcXHUyOUREJyxcblx0aW5vZG90OiAnXFx1MDEzMScsXG5cdEludDogJ1xcdTIyMkMnLFxuXHRpbnQ6ICdcXHUyMjJCJyxcblx0aW50Y2FsOiAnXFx1MjJCQScsXG5cdGludGVnZXJzOiAnXFx1MjEyNCcsXG5cdEludGVncmFsOiAnXFx1MjIyQicsXG5cdGludGVyY2FsOiAnXFx1MjJCQScsXG5cdEludGVyc2VjdGlvbjogJ1xcdTIyQzInLFxuXHRpbnRsYXJoazogJ1xcdTJBMTcnLFxuXHRpbnRwcm9kOiAnXFx1MkEzQycsXG5cdEludmlzaWJsZUNvbW1hOiAnXFx1MjA2MycsXG5cdEludmlzaWJsZVRpbWVzOiAnXFx1MjA2MicsXG5cdElPY3k6ICdcXHUwNDAxJyxcblx0aW9jeTogJ1xcdTA0NTEnLFxuXHRJb2dvbjogJ1xcdTAxMkUnLFxuXHRpb2dvbjogJ1xcdTAxMkYnLFxuXHRJb3BmOiAnXFx1RDgzNVxcdURENDAnLFxuXHRpb3BmOiAnXFx1RDgzNVxcdURENUEnLFxuXHRJb3RhOiAnXFx1MDM5OScsXG5cdGlvdGE6ICdcXHUwM0I5Jyxcblx0aXByb2Q6ICdcXHUyQTNDJyxcblx0aXF1ZXN0OiAnXFx1MDBCRicsXG5cdElzY3I6ICdcXHUyMTEwJyxcblx0aXNjcjogJ1xcdUQ4MzVcXHVEQ0JFJyxcblx0aXNpbjogJ1xcdTIyMDgnLFxuXHRpc2luZG90OiAnXFx1MjJGNScsXG5cdGlzaW5FOiAnXFx1MjJGOScsXG5cdGlzaW5zOiAnXFx1MjJGNCcsXG5cdGlzaW5zdjogJ1xcdTIyRjMnLFxuXHRpc2ludjogJ1xcdTIyMDgnLFxuXHRpdDogJ1xcdTIwNjInLFxuXHRJdGlsZGU6ICdcXHUwMTI4Jyxcblx0aXRpbGRlOiAnXFx1MDEyOScsXG5cdEl1a2N5OiAnXFx1MDQwNicsXG5cdGl1a2N5OiAnXFx1MDQ1NicsXG5cdEl1bWw6ICdcXHUwMENGJyxcblx0aXVtbDogJ1xcdTAwRUYnLFxuXHRKY2lyYzogJ1xcdTAxMzQnLFxuXHRqY2lyYzogJ1xcdTAxMzUnLFxuXHRKY3k6ICdcXHUwNDE5Jyxcblx0amN5OiAnXFx1MDQzOScsXG5cdEpmcjogJ1xcdUQ4MzVcXHVERDBEJyxcblx0amZyOiAnXFx1RDgzNVxcdUREMjcnLFxuXHRqbWF0aDogJ1xcdTAyMzcnLFxuXHRKb3BmOiAnXFx1RDgzNVxcdURENDEnLFxuXHRqb3BmOiAnXFx1RDgzNVxcdURENUInLFxuXHRKc2NyOiAnXFx1RDgzNVxcdURDQTUnLFxuXHRqc2NyOiAnXFx1RDgzNVxcdURDQkYnLFxuXHRKc2VyY3k6ICdcXHUwNDA4Jyxcblx0anNlcmN5OiAnXFx1MDQ1OCcsXG5cdEp1a2N5OiAnXFx1MDQwNCcsXG5cdGp1a2N5OiAnXFx1MDQ1NCcsXG5cdEthcHBhOiAnXFx1MDM5QScsXG5cdGthcHBhOiAnXFx1MDNCQScsXG5cdGthcHBhdjogJ1xcdTAzRjAnLFxuXHRLY2VkaWw6ICdcXHUwMTM2Jyxcblx0a2NlZGlsOiAnXFx1MDEzNycsXG5cdEtjeTogJ1xcdTA0MUEnLFxuXHRrY3k6ICdcXHUwNDNBJyxcblx0S2ZyOiAnXFx1RDgzNVxcdUREMEUnLFxuXHRrZnI6ICdcXHVEODM1XFx1REQyOCcsXG5cdGtncmVlbjogJ1xcdTAxMzgnLFxuXHRLSGN5OiAnXFx1MDQyNScsXG5cdGtoY3k6ICdcXHUwNDQ1Jyxcblx0S0pjeTogJ1xcdTA0MEMnLFxuXHRramN5OiAnXFx1MDQ1QycsXG5cdEtvcGY6ICdcXHVEODM1XFx1REQ0MicsXG5cdGtvcGY6ICdcXHVEODM1XFx1REQ1QycsXG5cdEtzY3I6ICdcXHVEODM1XFx1RENBNicsXG5cdGtzY3I6ICdcXHVEODM1XFx1RENDMCcsXG5cdGxBYXJyOiAnXFx1MjFEQScsXG5cdExhY3V0ZTogJ1xcdTAxMzknLFxuXHRsYWN1dGU6ICdcXHUwMTNBJyxcblx0bGFlbXB0eXY6ICdcXHUyOUI0Jyxcblx0bGFncmFuOiAnXFx1MjExMicsXG5cdExhbWJkYTogJ1xcdTAzOUInLFxuXHRsYW1iZGE6ICdcXHUwM0JCJyxcblx0TGFuZzogJ1xcdTI3RUEnLFxuXHRsYW5nOiAnXFx1MjdFOCcsXG5cdGxhbmdkOiAnXFx1Mjk5MScsXG5cdGxhbmdsZTogJ1xcdTI3RTgnLFxuXHRsYXA6ICdcXHUyQTg1Jyxcblx0TGFwbGFjZXRyZjogJ1xcdTIxMTInLFxuXHRsYXF1bzogJ1xcdTAwQUInLFxuXHRMYXJyOiAnXFx1MjE5RScsXG5cdGxBcnI6ICdcXHUyMUQwJyxcblx0bGFycjogJ1xcdTIxOTAnLFxuXHRsYXJyYjogJ1xcdTIxRTQnLFxuXHRsYXJyYmZzOiAnXFx1MjkxRicsXG5cdGxhcnJmczogJ1xcdTI5MUQnLFxuXHRsYXJyaGs6ICdcXHUyMUE5Jyxcblx0bGFycmxwOiAnXFx1MjFBQicsXG5cdGxhcnJwbDogJ1xcdTI5MzknLFxuXHRsYXJyc2ltOiAnXFx1Mjk3MycsXG5cdGxhcnJ0bDogJ1xcdTIxQTInLFxuXHRsYXQ6ICdcXHUyQUFCJyxcblx0bEF0YWlsOiAnXFx1MjkxQicsXG5cdGxhdGFpbDogJ1xcdTI5MTknLFxuXHRsYXRlOiAnXFx1MkFBRCcsXG5cdGxhdGVzOiAnXFx1MkFBRFxcdUZFMDAnLFxuXHRsQmFycjogJ1xcdTI5MEUnLFxuXHRsYmFycjogJ1xcdTI5MEMnLFxuXHRsYmJyazogJ1xcdTI3NzInLFxuXHRsYnJhY2U6ICdcXHUwMDdCJyxcblx0bGJyYWNrOiAnXFx1MDA1QicsXG5cdGxicmtlOiAnXFx1Mjk4QicsXG5cdGxicmtzbGQ6ICdcXHUyOThGJyxcblx0bGJya3NsdTogJ1xcdTI5OEQnLFxuXHRMY2Fyb246ICdcXHUwMTNEJyxcblx0bGNhcm9uOiAnXFx1MDEzRScsXG5cdExjZWRpbDogJ1xcdTAxM0InLFxuXHRsY2VkaWw6ICdcXHUwMTNDJyxcblx0bGNlaWw6ICdcXHUyMzA4Jyxcblx0bGN1YjogJ1xcdTAwN0InLFxuXHRMY3k6ICdcXHUwNDFCJyxcblx0bGN5OiAnXFx1MDQzQicsXG5cdGxkY2E6ICdcXHUyOTM2Jyxcblx0bGRxdW86ICdcXHUyMDFDJyxcblx0bGRxdW9yOiAnXFx1MjAxRScsXG5cdGxkcmRoYXI6ICdcXHUyOTY3Jyxcblx0bGRydXNoYXI6ICdcXHUyOTRCJyxcblx0bGRzaDogJ1xcdTIxQjInLFxuXHRsRTogJ1xcdTIyNjYnLFxuXHRsZTogJ1xcdTIyNjQnLFxuXHRMZWZ0QW5nbGVCcmFja2V0OiAnXFx1MjdFOCcsXG5cdExlZnRBcnJvdzogJ1xcdTIxOTAnLFxuXHRMZWZ0YXJyb3c6ICdcXHUyMUQwJyxcblx0bGVmdGFycm93OiAnXFx1MjE5MCcsXG5cdExlZnRBcnJvd0JhcjogJ1xcdTIxRTQnLFxuXHRMZWZ0QXJyb3dSaWdodEFycm93OiAnXFx1MjFDNicsXG5cdGxlZnRhcnJvd3RhaWw6ICdcXHUyMUEyJyxcblx0TGVmdENlaWxpbmc6ICdcXHUyMzA4Jyxcblx0TGVmdERvdWJsZUJyYWNrZXQ6ICdcXHUyN0U2Jyxcblx0TGVmdERvd25UZWVWZWN0b3I6ICdcXHUyOTYxJyxcblx0TGVmdERvd25WZWN0b3I6ICdcXHUyMUMzJyxcblx0TGVmdERvd25WZWN0b3JCYXI6ICdcXHUyOTU5Jyxcblx0TGVmdEZsb29yOiAnXFx1MjMwQScsXG5cdGxlZnRoYXJwb29uZG93bjogJ1xcdTIxQkQnLFxuXHRsZWZ0aGFycG9vbnVwOiAnXFx1MjFCQycsXG5cdGxlZnRsZWZ0YXJyb3dzOiAnXFx1MjFDNycsXG5cdExlZnRSaWdodEFycm93OiAnXFx1MjE5NCcsXG5cdExlZnRyaWdodGFycm93OiAnXFx1MjFENCcsXG5cdGxlZnRyaWdodGFycm93OiAnXFx1MjE5NCcsXG5cdGxlZnRyaWdodGFycm93czogJ1xcdTIxQzYnLFxuXHRsZWZ0cmlnaHRoYXJwb29uczogJ1xcdTIxQ0InLFxuXHRsZWZ0cmlnaHRzcXVpZ2Fycm93OiAnXFx1MjFBRCcsXG5cdExlZnRSaWdodFZlY3RvcjogJ1xcdTI5NEUnLFxuXHRMZWZ0VGVlOiAnXFx1MjJBMycsXG5cdExlZnRUZWVBcnJvdzogJ1xcdTIxQTQnLFxuXHRMZWZ0VGVlVmVjdG9yOiAnXFx1Mjk1QScsXG5cdGxlZnR0aHJlZXRpbWVzOiAnXFx1MjJDQicsXG5cdExlZnRUcmlhbmdsZTogJ1xcdTIyQjInLFxuXHRMZWZ0VHJpYW5nbGVCYXI6ICdcXHUyOUNGJyxcblx0TGVmdFRyaWFuZ2xlRXF1YWw6ICdcXHUyMkI0Jyxcblx0TGVmdFVwRG93blZlY3RvcjogJ1xcdTI5NTEnLFxuXHRMZWZ0VXBUZWVWZWN0b3I6ICdcXHUyOTYwJyxcblx0TGVmdFVwVmVjdG9yOiAnXFx1MjFCRicsXG5cdExlZnRVcFZlY3RvckJhcjogJ1xcdTI5NTgnLFxuXHRMZWZ0VmVjdG9yOiAnXFx1MjFCQycsXG5cdExlZnRWZWN0b3JCYXI6ICdcXHUyOTUyJyxcblx0bEVnOiAnXFx1MkE4QicsXG5cdGxlZzogJ1xcdTIyREEnLFxuXHRsZXE6ICdcXHUyMjY0Jyxcblx0bGVxcTogJ1xcdTIyNjYnLFxuXHRsZXFzbGFudDogJ1xcdTJBN0QnLFxuXHRsZXM6ICdcXHUyQTdEJyxcblx0bGVzY2M6ICdcXHUyQUE4Jyxcblx0bGVzZG90OiAnXFx1MkE3RicsXG5cdGxlc2RvdG86ICdcXHUyQTgxJyxcblx0bGVzZG90b3I6ICdcXHUyQTgzJyxcblx0bGVzZzogJ1xcdTIyREFcXHVGRTAwJyxcblx0bGVzZ2VzOiAnXFx1MkE5MycsXG5cdGxlc3NhcHByb3g6ICdcXHUyQTg1Jyxcblx0bGVzc2RvdDogJ1xcdTIyRDYnLFxuXHRsZXNzZXFndHI6ICdcXHUyMkRBJyxcblx0bGVzc2VxcWd0cjogJ1xcdTJBOEInLFxuXHRMZXNzRXF1YWxHcmVhdGVyOiAnXFx1MjJEQScsXG5cdExlc3NGdWxsRXF1YWw6ICdcXHUyMjY2Jyxcblx0TGVzc0dyZWF0ZXI6ICdcXHUyMjc2Jyxcblx0bGVzc2d0cjogJ1xcdTIyNzYnLFxuXHRMZXNzTGVzczogJ1xcdTJBQTEnLFxuXHRsZXNzc2ltOiAnXFx1MjI3MicsXG5cdExlc3NTbGFudEVxdWFsOiAnXFx1MkE3RCcsXG5cdExlc3NUaWxkZTogJ1xcdTIyNzInLFxuXHRsZmlzaHQ6ICdcXHUyOTdDJyxcblx0bGZsb29yOiAnXFx1MjMwQScsXG5cdExmcjogJ1xcdUQ4MzVcXHVERDBGJyxcblx0bGZyOiAnXFx1RDgzNVxcdUREMjknLFxuXHRsZzogJ1xcdTIyNzYnLFxuXHRsZ0U6ICdcXHUyQTkxJyxcblx0bEhhcjogJ1xcdTI5NjInLFxuXHRsaGFyZDogJ1xcdTIxQkQnLFxuXHRsaGFydTogJ1xcdTIxQkMnLFxuXHRsaGFydWw6ICdcXHUyOTZBJyxcblx0bGhibGs6ICdcXHUyNTg0Jyxcblx0TEpjeTogJ1xcdTA0MDknLFxuXHRsamN5OiAnXFx1MDQ1OScsXG5cdExsOiAnXFx1MjJEOCcsXG5cdGxsOiAnXFx1MjI2QScsXG5cdGxsYXJyOiAnXFx1MjFDNycsXG5cdGxsY29ybmVyOiAnXFx1MjMxRScsXG5cdExsZWZ0YXJyb3c6ICdcXHUyMURBJyxcblx0bGxoYXJkOiAnXFx1Mjk2QicsXG5cdGxsdHJpOiAnXFx1MjVGQScsXG5cdExtaWRvdDogJ1xcdTAxM0YnLFxuXHRsbWlkb3Q6ICdcXHUwMTQwJyxcblx0bG1vdXN0OiAnXFx1MjNCMCcsXG5cdGxtb3VzdGFjaGU6ICdcXHUyM0IwJyxcblx0bG5hcDogJ1xcdTJBODknLFxuXHRsbmFwcHJveDogJ1xcdTJBODknLFxuXHRsbkU6ICdcXHUyMjY4Jyxcblx0bG5lOiAnXFx1MkE4NycsXG5cdGxuZXE6ICdcXHUyQTg3Jyxcblx0bG5lcXE6ICdcXHUyMjY4Jyxcblx0bG5zaW06ICdcXHUyMkU2Jyxcblx0bG9hbmc6ICdcXHUyN0VDJyxcblx0bG9hcnI6ICdcXHUyMUZEJyxcblx0bG9icms6ICdcXHUyN0U2Jyxcblx0TG9uZ0xlZnRBcnJvdzogJ1xcdTI3RjUnLFxuXHRMb25nbGVmdGFycm93OiAnXFx1MjdGOCcsXG5cdGxvbmdsZWZ0YXJyb3c6ICdcXHUyN0Y1Jyxcblx0TG9uZ0xlZnRSaWdodEFycm93OiAnXFx1MjdGNycsXG5cdExvbmdsZWZ0cmlnaHRhcnJvdzogJ1xcdTI3RkEnLFxuXHRsb25nbGVmdHJpZ2h0YXJyb3c6ICdcXHUyN0Y3Jyxcblx0bG9uZ21hcHN0bzogJ1xcdTI3RkMnLFxuXHRMb25nUmlnaHRBcnJvdzogJ1xcdTI3RjYnLFxuXHRMb25ncmlnaHRhcnJvdzogJ1xcdTI3RjknLFxuXHRsb25ncmlnaHRhcnJvdzogJ1xcdTI3RjYnLFxuXHRsb29wYXJyb3dsZWZ0OiAnXFx1MjFBQicsXG5cdGxvb3BhcnJvd3JpZ2h0OiAnXFx1MjFBQycsXG5cdGxvcGFyOiAnXFx1Mjk4NScsXG5cdExvcGY6ICdcXHVEODM1XFx1REQ0MycsXG5cdGxvcGY6ICdcXHVEODM1XFx1REQ1RCcsXG5cdGxvcGx1czogJ1xcdTJBMkQnLFxuXHRsb3RpbWVzOiAnXFx1MkEzNCcsXG5cdGxvd2FzdDogJ1xcdTIyMTcnLFxuXHRsb3diYXI6ICdcXHUwMDVGJyxcblx0TG93ZXJMZWZ0QXJyb3c6ICdcXHUyMTk5Jyxcblx0TG93ZXJSaWdodEFycm93OiAnXFx1MjE5OCcsXG5cdGxvejogJ1xcdTI1Q0EnLFxuXHRsb3plbmdlOiAnXFx1MjVDQScsXG5cdGxvemY6ICdcXHUyOUVCJyxcblx0bHBhcjogJ1xcdTAwMjgnLFxuXHRscGFybHQ6ICdcXHUyOTkzJyxcblx0bHJhcnI6ICdcXHUyMUM2Jyxcblx0bHJjb3JuZXI6ICdcXHUyMzFGJyxcblx0bHJoYXI6ICdcXHUyMUNCJyxcblx0bHJoYXJkOiAnXFx1Mjk2RCcsXG5cdGxybTogJ1xcdTIwMEUnLFxuXHRscnRyaTogJ1xcdTIyQkYnLFxuXHRsc2FxdW86ICdcXHUyMDM5Jyxcblx0THNjcjogJ1xcdTIxMTInLFxuXHRsc2NyOiAnXFx1RDgzNVxcdURDQzEnLFxuXHRMc2g6ICdcXHUyMUIwJyxcblx0bHNoOiAnXFx1MjFCMCcsXG5cdGxzaW06ICdcXHUyMjcyJyxcblx0bHNpbWU6ICdcXHUyQThEJyxcblx0bHNpbWc6ICdcXHUyQThGJyxcblx0bHNxYjogJ1xcdTAwNUInLFxuXHRsc3F1bzogJ1xcdTIwMTgnLFxuXHRsc3F1b3I6ICdcXHUyMDFBJyxcblx0THN0cm9rOiAnXFx1MDE0MScsXG5cdGxzdHJvazogJ1xcdTAxNDInLFxuXHRMdDogJ1xcdTIyNkEnLFxuXHRMVDogJ1xcdTAwM0MnLFxuXHRsdDogJ1xcdTAwM0MnLFxuXHRsdGNjOiAnXFx1MkFBNicsXG5cdGx0Y2lyOiAnXFx1MkE3OScsXG5cdGx0ZG90OiAnXFx1MjJENicsXG5cdGx0aHJlZTogJ1xcdTIyQ0InLFxuXHRsdGltZXM6ICdcXHUyMkM5Jyxcblx0bHRsYXJyOiAnXFx1Mjk3NicsXG5cdGx0cXVlc3Q6ICdcXHUyQTdCJyxcblx0bHRyaTogJ1xcdTI1QzMnLFxuXHRsdHJpZTogJ1xcdTIyQjQnLFxuXHRsdHJpZjogJ1xcdTI1QzInLFxuXHRsdHJQYXI6ICdcXHUyOTk2Jyxcblx0bHVyZHNoYXI6ICdcXHUyOTRBJyxcblx0bHVydWhhcjogJ1xcdTI5NjYnLFxuXHRsdmVydG5lcXE6ICdcXHUyMjY4XFx1RkUwMCcsXG5cdGx2bkU6ICdcXHUyMjY4XFx1RkUwMCcsXG5cdG1hY3I6ICdcXHUwMEFGJyxcblx0bWFsZTogJ1xcdTI2NDInLFxuXHRtYWx0OiAnXFx1MjcyMCcsXG5cdG1hbHRlc2U6ICdcXHUyNzIwJyxcblx0TWFwOiAnXFx1MjkwNScsXG5cdG1hcDogJ1xcdTIxQTYnLFxuXHRtYXBzdG86ICdcXHUyMUE2Jyxcblx0bWFwc3RvZG93bjogJ1xcdTIxQTcnLFxuXHRtYXBzdG9sZWZ0OiAnXFx1MjFBNCcsXG5cdG1hcHN0b3VwOiAnXFx1MjFBNScsXG5cdG1hcmtlcjogJ1xcdTI1QUUnLFxuXHRtY29tbWE6ICdcXHUyQTI5Jyxcblx0TWN5OiAnXFx1MDQxQycsXG5cdG1jeTogJ1xcdTA0M0MnLFxuXHRtZGFzaDogJ1xcdTIwMTQnLFxuXHRtRERvdDogJ1xcdTIyM0EnLFxuXHRtZWFzdXJlZGFuZ2xlOiAnXFx1MjIyMScsXG5cdE1lZGl1bVNwYWNlOiAnXFx1MjA1RicsXG5cdE1lbGxpbnRyZjogJ1xcdTIxMzMnLFxuXHRNZnI6ICdcXHVEODM1XFx1REQxMCcsXG5cdG1mcjogJ1xcdUQ4MzVcXHVERDJBJyxcblx0bWhvOiAnXFx1MjEyNycsXG5cdG1pY3JvOiAnXFx1MDBCNScsXG5cdG1pZDogJ1xcdTIyMjMnLFxuXHRtaWRhc3Q6ICdcXHUwMDJBJyxcblx0bWlkY2lyOiAnXFx1MkFGMCcsXG5cdG1pZGRvdDogJ1xcdTAwQjcnLFxuXHRtaW51czogJ1xcdTIyMTInLFxuXHRtaW51c2I6ICdcXHUyMjlGJyxcblx0bWludXNkOiAnXFx1MjIzOCcsXG5cdG1pbnVzZHU6ICdcXHUyQTJBJyxcblx0TWludXNQbHVzOiAnXFx1MjIxMycsXG5cdG1sY3A6ICdcXHUyQURCJyxcblx0bWxkcjogJ1xcdTIwMjYnLFxuXHRtbnBsdXM6ICdcXHUyMjEzJyxcblx0bW9kZWxzOiAnXFx1MjJBNycsXG5cdE1vcGY6ICdcXHVEODM1XFx1REQ0NCcsXG5cdG1vcGY6ICdcXHVEODM1XFx1REQ1RScsXG5cdG1wOiAnXFx1MjIxMycsXG5cdE1zY3I6ICdcXHUyMTMzJyxcblx0bXNjcjogJ1xcdUQ4MzVcXHVEQ0MyJyxcblx0bXN0cG9zOiAnXFx1MjIzRScsXG5cdE11OiAnXFx1MDM5QycsXG5cdG11OiAnXFx1MDNCQycsXG5cdG11bHRpbWFwOiAnXFx1MjJCOCcsXG5cdG11bWFwOiAnXFx1MjJCOCcsXG5cdG5hYmxhOiAnXFx1MjIwNycsXG5cdE5hY3V0ZTogJ1xcdTAxNDMnLFxuXHRuYWN1dGU6ICdcXHUwMTQ0Jyxcblx0bmFuZzogJ1xcdTIyMjBcXHUyMEQyJyxcblx0bmFwOiAnXFx1MjI0OScsXG5cdG5hcEU6ICdcXHUyQTcwXFx1MDMzOCcsXG5cdG5hcGlkOiAnXFx1MjI0QlxcdTAzMzgnLFxuXHRuYXBvczogJ1xcdTAxNDknLFxuXHRuYXBwcm94OiAnXFx1MjI0OScsXG5cdG5hdHVyOiAnXFx1MjY2RScsXG5cdG5hdHVyYWw6ICdcXHUyNjZFJyxcblx0bmF0dXJhbHM6ICdcXHUyMTE1Jyxcblx0bmJzcDogJ1xcdTAwQTAnLFxuXHRuYnVtcDogJ1xcdTIyNEVcXHUwMzM4Jyxcblx0bmJ1bXBlOiAnXFx1MjI0RlxcdTAzMzgnLFxuXHRuY2FwOiAnXFx1MkE0MycsXG5cdE5jYXJvbjogJ1xcdTAxNDcnLFxuXHRuY2Fyb246ICdcXHUwMTQ4Jyxcblx0TmNlZGlsOiAnXFx1MDE0NScsXG5cdG5jZWRpbDogJ1xcdTAxNDYnLFxuXHRuY29uZzogJ1xcdTIyNDcnLFxuXHRuY29uZ2RvdDogJ1xcdTJBNkRcXHUwMzM4Jyxcblx0bmN1cDogJ1xcdTJBNDInLFxuXHROY3k6ICdcXHUwNDFEJyxcblx0bmN5OiAnXFx1MDQzRCcsXG5cdG5kYXNoOiAnXFx1MjAxMycsXG5cdG5lOiAnXFx1MjI2MCcsXG5cdG5lYXJoazogJ1xcdTI5MjQnLFxuXHRuZUFycjogJ1xcdTIxRDcnLFxuXHRuZWFycjogJ1xcdTIxOTcnLFxuXHRuZWFycm93OiAnXFx1MjE5NycsXG5cdG5lZG90OiAnXFx1MjI1MFxcdTAzMzgnLFxuXHROZWdhdGl2ZU1lZGl1bVNwYWNlOiAnXFx1MjAwQicsXG5cdE5lZ2F0aXZlVGhpY2tTcGFjZTogJ1xcdTIwMEInLFxuXHROZWdhdGl2ZVRoaW5TcGFjZTogJ1xcdTIwMEInLFxuXHROZWdhdGl2ZVZlcnlUaGluU3BhY2U6ICdcXHUyMDBCJyxcblx0bmVxdWl2OiAnXFx1MjI2MicsXG5cdG5lc2VhcjogJ1xcdTI5MjgnLFxuXHRuZXNpbTogJ1xcdTIyNDJcXHUwMzM4Jyxcblx0TmVzdGVkR3JlYXRlckdyZWF0ZXI6ICdcXHUyMjZCJyxcblx0TmVzdGVkTGVzc0xlc3M6ICdcXHUyMjZBJyxcblx0TmV3TGluZTogJ1xcdTAwMEEnLFxuXHRuZXhpc3Q6ICdcXHUyMjA0Jyxcblx0bmV4aXN0czogJ1xcdTIyMDQnLFxuXHROZnI6ICdcXHVEODM1XFx1REQxMScsXG5cdG5mcjogJ1xcdUQ4MzVcXHVERDJCJyxcblx0bmdFOiAnXFx1MjI2N1xcdTAzMzgnLFxuXHRuZ2U6ICdcXHUyMjcxJyxcblx0bmdlcTogJ1xcdTIyNzEnLFxuXHRuZ2VxcTogJ1xcdTIyNjdcXHUwMzM4Jyxcblx0bmdlcXNsYW50OiAnXFx1MkE3RVxcdTAzMzgnLFxuXHRuZ2VzOiAnXFx1MkE3RVxcdTAzMzgnLFxuXHRuR2c6ICdcXHUyMkQ5XFx1MDMzOCcsXG5cdG5nc2ltOiAnXFx1MjI3NScsXG5cdG5HdDogJ1xcdTIyNkJcXHUyMEQyJyxcblx0bmd0OiAnXFx1MjI2RicsXG5cdG5ndHI6ICdcXHUyMjZGJyxcblx0bkd0djogJ1xcdTIyNkJcXHUwMzM4Jyxcblx0bmhBcnI6ICdcXHUyMUNFJyxcblx0bmhhcnI6ICdcXHUyMUFFJyxcblx0bmhwYXI6ICdcXHUyQUYyJyxcblx0bmk6ICdcXHUyMjBCJyxcblx0bmlzOiAnXFx1MjJGQycsXG5cdG5pc2Q6ICdcXHUyMkZBJyxcblx0bml2OiAnXFx1MjIwQicsXG5cdE5KY3k6ICdcXHUwNDBBJyxcblx0bmpjeTogJ1xcdTA0NUEnLFxuXHRubEFycjogJ1xcdTIxQ0QnLFxuXHRubGFycjogJ1xcdTIxOUEnLFxuXHRubGRyOiAnXFx1MjAyNScsXG5cdG5sRTogJ1xcdTIyNjZcXHUwMzM4Jyxcblx0bmxlOiAnXFx1MjI3MCcsXG5cdG5MZWZ0YXJyb3c6ICdcXHUyMUNEJyxcblx0bmxlZnRhcnJvdzogJ1xcdTIxOUEnLFxuXHRuTGVmdHJpZ2h0YXJyb3c6ICdcXHUyMUNFJyxcblx0bmxlZnRyaWdodGFycm93OiAnXFx1MjFBRScsXG5cdG5sZXE6ICdcXHUyMjcwJyxcblx0bmxlcXE6ICdcXHUyMjY2XFx1MDMzOCcsXG5cdG5sZXFzbGFudDogJ1xcdTJBN0RcXHUwMzM4Jyxcblx0bmxlczogJ1xcdTJBN0RcXHUwMzM4Jyxcblx0bmxlc3M6ICdcXHUyMjZFJyxcblx0bkxsOiAnXFx1MjJEOFxcdTAzMzgnLFxuXHRubHNpbTogJ1xcdTIyNzQnLFxuXHRuTHQ6ICdcXHUyMjZBXFx1MjBEMicsXG5cdG5sdDogJ1xcdTIyNkUnLFxuXHRubHRyaTogJ1xcdTIyRUEnLFxuXHRubHRyaWU6ICdcXHUyMkVDJyxcblx0bkx0djogJ1xcdTIyNkFcXHUwMzM4Jyxcblx0bm1pZDogJ1xcdTIyMjQnLFxuXHROb0JyZWFrOiAnXFx1MjA2MCcsXG5cdE5vbkJyZWFraW5nU3BhY2U6ICdcXHUwMEEwJyxcblx0Tm9wZjogJ1xcdTIxMTUnLFxuXHRub3BmOiAnXFx1RDgzNVxcdURENUYnLFxuXHROb3Q6ICdcXHUyQUVDJyxcblx0bm90OiAnXFx1MDBBQycsXG5cdE5vdENvbmdydWVudDogJ1xcdTIyNjInLFxuXHROb3RDdXBDYXA6ICdcXHUyMjZEJyxcblx0Tm90RG91YmxlVmVydGljYWxCYXI6ICdcXHUyMjI2Jyxcblx0Tm90RWxlbWVudDogJ1xcdTIyMDknLFxuXHROb3RFcXVhbDogJ1xcdTIyNjAnLFxuXHROb3RFcXVhbFRpbGRlOiAnXFx1MjI0MlxcdTAzMzgnLFxuXHROb3RFeGlzdHM6ICdcXHUyMjA0Jyxcblx0Tm90R3JlYXRlcjogJ1xcdTIyNkYnLFxuXHROb3RHcmVhdGVyRXF1YWw6ICdcXHUyMjcxJyxcblx0Tm90R3JlYXRlckZ1bGxFcXVhbDogJ1xcdTIyNjdcXHUwMzM4Jyxcblx0Tm90R3JlYXRlckdyZWF0ZXI6ICdcXHUyMjZCXFx1MDMzOCcsXG5cdE5vdEdyZWF0ZXJMZXNzOiAnXFx1MjI3OScsXG5cdE5vdEdyZWF0ZXJTbGFudEVxdWFsOiAnXFx1MkE3RVxcdTAzMzgnLFxuXHROb3RHcmVhdGVyVGlsZGU6ICdcXHUyMjc1Jyxcblx0Tm90SHVtcERvd25IdW1wOiAnXFx1MjI0RVxcdTAzMzgnLFxuXHROb3RIdW1wRXF1YWw6ICdcXHUyMjRGXFx1MDMzOCcsXG5cdG5vdGluOiAnXFx1MjIwOScsXG5cdG5vdGluZG90OiAnXFx1MjJGNVxcdTAzMzgnLFxuXHRub3RpbkU6ICdcXHUyMkY5XFx1MDMzOCcsXG5cdG5vdGludmE6ICdcXHUyMjA5Jyxcblx0bm90aW52YjogJ1xcdTIyRjcnLFxuXHRub3RpbnZjOiAnXFx1MjJGNicsXG5cdE5vdExlZnRUcmlhbmdsZTogJ1xcdTIyRUEnLFxuXHROb3RMZWZ0VHJpYW5nbGVCYXI6ICdcXHUyOUNGXFx1MDMzOCcsXG5cdE5vdExlZnRUcmlhbmdsZUVxdWFsOiAnXFx1MjJFQycsXG5cdE5vdExlc3M6ICdcXHUyMjZFJyxcblx0Tm90TGVzc0VxdWFsOiAnXFx1MjI3MCcsXG5cdE5vdExlc3NHcmVhdGVyOiAnXFx1MjI3OCcsXG5cdE5vdExlc3NMZXNzOiAnXFx1MjI2QVxcdTAzMzgnLFxuXHROb3RMZXNzU2xhbnRFcXVhbDogJ1xcdTJBN0RcXHUwMzM4Jyxcblx0Tm90TGVzc1RpbGRlOiAnXFx1MjI3NCcsXG5cdE5vdE5lc3RlZEdyZWF0ZXJHcmVhdGVyOiAnXFx1MkFBMlxcdTAzMzgnLFxuXHROb3ROZXN0ZWRMZXNzTGVzczogJ1xcdTJBQTFcXHUwMzM4Jyxcblx0bm90bmk6ICdcXHUyMjBDJyxcblx0bm90bml2YTogJ1xcdTIyMEMnLFxuXHRub3RuaXZiOiAnXFx1MjJGRScsXG5cdG5vdG5pdmM6ICdcXHUyMkZEJyxcblx0Tm90UHJlY2VkZXM6ICdcXHUyMjgwJyxcblx0Tm90UHJlY2VkZXNFcXVhbDogJ1xcdTJBQUZcXHUwMzM4Jyxcblx0Tm90UHJlY2VkZXNTbGFudEVxdWFsOiAnXFx1MjJFMCcsXG5cdE5vdFJldmVyc2VFbGVtZW50OiAnXFx1MjIwQycsXG5cdE5vdFJpZ2h0VHJpYW5nbGU6ICdcXHUyMkVCJyxcblx0Tm90UmlnaHRUcmlhbmdsZUJhcjogJ1xcdTI5RDBcXHUwMzM4Jyxcblx0Tm90UmlnaHRUcmlhbmdsZUVxdWFsOiAnXFx1MjJFRCcsXG5cdE5vdFNxdWFyZVN1YnNldDogJ1xcdTIyOEZcXHUwMzM4Jyxcblx0Tm90U3F1YXJlU3Vic2V0RXF1YWw6ICdcXHUyMkUyJyxcblx0Tm90U3F1YXJlU3VwZXJzZXQ6ICdcXHUyMjkwXFx1MDMzOCcsXG5cdE5vdFNxdWFyZVN1cGVyc2V0RXF1YWw6ICdcXHUyMkUzJyxcblx0Tm90U3Vic2V0OiAnXFx1MjI4MlxcdTIwRDInLFxuXHROb3RTdWJzZXRFcXVhbDogJ1xcdTIyODgnLFxuXHROb3RTdWNjZWVkczogJ1xcdTIyODEnLFxuXHROb3RTdWNjZWVkc0VxdWFsOiAnXFx1MkFCMFxcdTAzMzgnLFxuXHROb3RTdWNjZWVkc1NsYW50RXF1YWw6ICdcXHUyMkUxJyxcblx0Tm90U3VjY2VlZHNUaWxkZTogJ1xcdTIyN0ZcXHUwMzM4Jyxcblx0Tm90U3VwZXJzZXQ6ICdcXHUyMjgzXFx1MjBEMicsXG5cdE5vdFN1cGVyc2V0RXF1YWw6ICdcXHUyMjg5Jyxcblx0Tm90VGlsZGU6ICdcXHUyMjQxJyxcblx0Tm90VGlsZGVFcXVhbDogJ1xcdTIyNDQnLFxuXHROb3RUaWxkZUZ1bGxFcXVhbDogJ1xcdTIyNDcnLFxuXHROb3RUaWxkZVRpbGRlOiAnXFx1MjI0OScsXG5cdE5vdFZlcnRpY2FsQmFyOiAnXFx1MjIyNCcsXG5cdG5wYXI6ICdcXHUyMjI2Jyxcblx0bnBhcmFsbGVsOiAnXFx1MjIyNicsXG5cdG5wYXJzbDogJ1xcdTJBRkRcXHUyMEU1Jyxcblx0bnBhcnQ6ICdcXHUyMjAyXFx1MDMzOCcsXG5cdG5wb2xpbnQ6ICdcXHUyQTE0Jyxcblx0bnByOiAnXFx1MjI4MCcsXG5cdG5wcmN1ZTogJ1xcdTIyRTAnLFxuXHRucHJlOiAnXFx1MkFBRlxcdTAzMzgnLFxuXHRucHJlYzogJ1xcdTIyODAnLFxuXHRucHJlY2VxOiAnXFx1MkFBRlxcdTAzMzgnLFxuXHRuckFycjogJ1xcdTIxQ0YnLFxuXHRucmFycjogJ1xcdTIxOUInLFxuXHRucmFycmM6ICdcXHUyOTMzXFx1MDMzOCcsXG5cdG5yYXJydzogJ1xcdTIxOURcXHUwMzM4Jyxcblx0blJpZ2h0YXJyb3c6ICdcXHUyMUNGJyxcblx0bnJpZ2h0YXJyb3c6ICdcXHUyMTlCJyxcblx0bnJ0cmk6ICdcXHUyMkVCJyxcblx0bnJ0cmllOiAnXFx1MjJFRCcsXG5cdG5zYzogJ1xcdTIyODEnLFxuXHRuc2NjdWU6ICdcXHUyMkUxJyxcblx0bnNjZTogJ1xcdTJBQjBcXHUwMzM4Jyxcblx0TnNjcjogJ1xcdUQ4MzVcXHVEQ0E5Jyxcblx0bnNjcjogJ1xcdUQ4MzVcXHVEQ0MzJyxcblx0bnNob3J0bWlkOiAnXFx1MjIyNCcsXG5cdG5zaG9ydHBhcmFsbGVsOiAnXFx1MjIyNicsXG5cdG5zaW06ICdcXHUyMjQxJyxcblx0bnNpbWU6ICdcXHUyMjQ0Jyxcblx0bnNpbWVxOiAnXFx1MjI0NCcsXG5cdG5zbWlkOiAnXFx1MjIyNCcsXG5cdG5zcGFyOiAnXFx1MjIyNicsXG5cdG5zcXN1YmU6ICdcXHUyMkUyJyxcblx0bnNxc3VwZTogJ1xcdTIyRTMnLFxuXHRuc3ViOiAnXFx1MjI4NCcsXG5cdG5zdWJFOiAnXFx1MkFDNVxcdTAzMzgnLFxuXHRuc3ViZTogJ1xcdTIyODgnLFxuXHRuc3Vic2V0OiAnXFx1MjI4MlxcdTIwRDInLFxuXHRuc3Vic2V0ZXE6ICdcXHUyMjg4Jyxcblx0bnN1YnNldGVxcTogJ1xcdTJBQzVcXHUwMzM4Jyxcblx0bnN1Y2M6ICdcXHUyMjgxJyxcblx0bnN1Y2NlcTogJ1xcdTJBQjBcXHUwMzM4Jyxcblx0bnN1cDogJ1xcdTIyODUnLFxuXHRuc3VwRTogJ1xcdTJBQzZcXHUwMzM4Jyxcblx0bnN1cGU6ICdcXHUyMjg5Jyxcblx0bnN1cHNldDogJ1xcdTIyODNcXHUyMEQyJyxcblx0bnN1cHNldGVxOiAnXFx1MjI4OScsXG5cdG5zdXBzZXRlcXE6ICdcXHUyQUM2XFx1MDMzOCcsXG5cdG50Z2w6ICdcXHUyMjc5Jyxcblx0TnRpbGRlOiAnXFx1MDBEMScsXG5cdG50aWxkZTogJ1xcdTAwRjEnLFxuXHRudGxnOiAnXFx1MjI3OCcsXG5cdG50cmlhbmdsZWxlZnQ6ICdcXHUyMkVBJyxcblx0bnRyaWFuZ2xlbGVmdGVxOiAnXFx1MjJFQycsXG5cdG50cmlhbmdsZXJpZ2h0OiAnXFx1MjJFQicsXG5cdG50cmlhbmdsZXJpZ2h0ZXE6ICdcXHUyMkVEJyxcblx0TnU6ICdcXHUwMzlEJyxcblx0bnU6ICdcXHUwM0JEJyxcblx0bnVtOiAnXFx1MDAyMycsXG5cdG51bWVybzogJ1xcdTIxMTYnLFxuXHRudW1zcDogJ1xcdTIwMDcnLFxuXHRudmFwOiAnXFx1MjI0RFxcdTIwRDInLFxuXHRuVkRhc2g6ICdcXHUyMkFGJyxcblx0blZkYXNoOiAnXFx1MjJBRScsXG5cdG52RGFzaDogJ1xcdTIyQUQnLFxuXHRudmRhc2g6ICdcXHUyMkFDJyxcblx0bnZnZTogJ1xcdTIyNjVcXHUyMEQyJyxcblx0bnZndDogJ1xcdTAwM0VcXHUyMEQyJyxcblx0bnZIYXJyOiAnXFx1MjkwNCcsXG5cdG52aW5maW46ICdcXHUyOURFJyxcblx0bnZsQXJyOiAnXFx1MjkwMicsXG5cdG52bGU6ICdcXHUyMjY0XFx1MjBEMicsXG5cdG52bHQ6ICdcXHUwMDNDXFx1MjBEMicsXG5cdG52bHRyaWU6ICdcXHUyMkI0XFx1MjBEMicsXG5cdG52ckFycjogJ1xcdTI5MDMnLFxuXHRudnJ0cmllOiAnXFx1MjJCNVxcdTIwRDInLFxuXHRudnNpbTogJ1xcdTIyM0NcXHUyMEQyJyxcblx0bndhcmhrOiAnXFx1MjkyMycsXG5cdG53QXJyOiAnXFx1MjFENicsXG5cdG53YXJyOiAnXFx1MjE5NicsXG5cdG53YXJyb3c6ICdcXHUyMTk2Jyxcblx0bnduZWFyOiAnXFx1MjkyNycsXG5cdE9hY3V0ZTogJ1xcdTAwRDMnLFxuXHRvYWN1dGU6ICdcXHUwMEYzJyxcblx0b2FzdDogJ1xcdTIyOUInLFxuXHRvY2lyOiAnXFx1MjI5QScsXG5cdE9jaXJjOiAnXFx1MDBENCcsXG5cdG9jaXJjOiAnXFx1MDBGNCcsXG5cdE9jeTogJ1xcdTA0MUUnLFxuXHRvY3k6ICdcXHUwNDNFJyxcblx0b2Rhc2g6ICdcXHUyMjlEJyxcblx0T2RibGFjOiAnXFx1MDE1MCcsXG5cdG9kYmxhYzogJ1xcdTAxNTEnLFxuXHRvZGl2OiAnXFx1MkEzOCcsXG5cdG9kb3Q6ICdcXHUyMjk5Jyxcblx0b2Rzb2xkOiAnXFx1MjlCQycsXG5cdE9FbGlnOiAnXFx1MDE1MicsXG5cdG9lbGlnOiAnXFx1MDE1MycsXG5cdG9mY2lyOiAnXFx1MjlCRicsXG5cdE9mcjogJ1xcdUQ4MzVcXHVERDEyJyxcblx0b2ZyOiAnXFx1RDgzNVxcdUREMkMnLFxuXHRvZ29uOiAnXFx1MDJEQicsXG5cdE9ncmF2ZTogJ1xcdTAwRDInLFxuXHRvZ3JhdmU6ICdcXHUwMEYyJyxcblx0b2d0OiAnXFx1MjlDMScsXG5cdG9oYmFyOiAnXFx1MjlCNScsXG5cdG9obTogJ1xcdTAzQTknLFxuXHRvaW50OiAnXFx1MjIyRScsXG5cdG9sYXJyOiAnXFx1MjFCQScsXG5cdG9sY2lyOiAnXFx1MjlCRScsXG5cdG9sY3Jvc3M6ICdcXHUyOUJCJyxcblx0b2xpbmU6ICdcXHUyMDNFJyxcblx0b2x0OiAnXFx1MjlDMCcsXG5cdE9tYWNyOiAnXFx1MDE0QycsXG5cdG9tYWNyOiAnXFx1MDE0RCcsXG5cdE9tZWdhOiAnXFx1MDNBOScsXG5cdG9tZWdhOiAnXFx1MDNDOScsXG5cdE9taWNyb246ICdcXHUwMzlGJyxcblx0b21pY3JvbjogJ1xcdTAzQkYnLFxuXHRvbWlkOiAnXFx1MjlCNicsXG5cdG9taW51czogJ1xcdTIyOTYnLFxuXHRPb3BmOiAnXFx1RDgzNVxcdURENDYnLFxuXHRvb3BmOiAnXFx1RDgzNVxcdURENjAnLFxuXHRvcGFyOiAnXFx1MjlCNycsXG5cdE9wZW5DdXJseURvdWJsZVF1b3RlOiAnXFx1MjAxQycsXG5cdE9wZW5DdXJseVF1b3RlOiAnXFx1MjAxOCcsXG5cdG9wZXJwOiAnXFx1MjlCOScsXG5cdG9wbHVzOiAnXFx1MjI5NScsXG5cdE9yOiAnXFx1MkE1NCcsXG5cdG9yOiAnXFx1MjIyOCcsXG5cdG9yYXJyOiAnXFx1MjFCQicsXG5cdG9yZDogJ1xcdTJBNUQnLFxuXHRvcmRlcjogJ1xcdTIxMzQnLFxuXHRvcmRlcm9mOiAnXFx1MjEzNCcsXG5cdG9yZGY6ICdcXHUwMEFBJyxcblx0b3JkbTogJ1xcdTAwQkEnLFxuXHRvcmlnb2Y6ICdcXHUyMkI2Jyxcblx0b3JvcjogJ1xcdTJBNTYnLFxuXHRvcnNsb3BlOiAnXFx1MkE1NycsXG5cdG9ydjogJ1xcdTJBNUInLFxuXHRvUzogJ1xcdTI0QzgnLFxuXHRPc2NyOiAnXFx1RDgzNVxcdURDQUEnLFxuXHRvc2NyOiAnXFx1MjEzNCcsXG5cdE9zbGFzaDogJ1xcdTAwRDgnLFxuXHRvc2xhc2g6ICdcXHUwMEY4Jyxcblx0b3NvbDogJ1xcdTIyOTgnLFxuXHRPdGlsZGU6ICdcXHUwMEQ1Jyxcblx0b3RpbGRlOiAnXFx1MDBGNScsXG5cdE90aW1lczogJ1xcdTJBMzcnLFxuXHRvdGltZXM6ICdcXHUyMjk3Jyxcblx0b3RpbWVzYXM6ICdcXHUyQTM2Jyxcblx0T3VtbDogJ1xcdTAwRDYnLFxuXHRvdW1sOiAnXFx1MDBGNicsXG5cdG92YmFyOiAnXFx1MjMzRCcsXG5cdE92ZXJCYXI6ICdcXHUyMDNFJyxcblx0T3ZlckJyYWNlOiAnXFx1MjNERScsXG5cdE92ZXJCcmFja2V0OiAnXFx1MjNCNCcsXG5cdE92ZXJQYXJlbnRoZXNpczogJ1xcdTIzREMnLFxuXHRwYXI6ICdcXHUyMjI1Jyxcblx0cGFyYTogJ1xcdTAwQjYnLFxuXHRwYXJhbGxlbDogJ1xcdTIyMjUnLFxuXHRwYXJzaW06ICdcXHUyQUYzJyxcblx0cGFyc2w6ICdcXHUyQUZEJyxcblx0cGFydDogJ1xcdTIyMDInLFxuXHRQYXJ0aWFsRDogJ1xcdTIyMDInLFxuXHRQY3k6ICdcXHUwNDFGJyxcblx0cGN5OiAnXFx1MDQzRicsXG5cdHBlcmNudDogJ1xcdTAwMjUnLFxuXHRwZXJpb2Q6ICdcXHUwMDJFJyxcblx0cGVybWlsOiAnXFx1MjAzMCcsXG5cdHBlcnA6ICdcXHUyMkE1Jyxcblx0cGVydGVuazogJ1xcdTIwMzEnLFxuXHRQZnI6ICdcXHVEODM1XFx1REQxMycsXG5cdHBmcjogJ1xcdUQ4MzVcXHVERDJEJyxcblx0UGhpOiAnXFx1MDNBNicsXG5cdHBoaTogJ1xcdTAzQzYnLFxuXHRwaGl2OiAnXFx1MDNENScsXG5cdHBobW1hdDogJ1xcdTIxMzMnLFxuXHRwaG9uZTogJ1xcdTI2MEUnLFxuXHRQaTogJ1xcdTAzQTAnLFxuXHRwaTogJ1xcdTAzQzAnLFxuXHRwaXRjaGZvcms6ICdcXHUyMkQ0Jyxcblx0cGl2OiAnXFx1MDNENicsXG5cdHBsYW5jazogJ1xcdTIxMEYnLFxuXHRwbGFuY2toOiAnXFx1MjEwRScsXG5cdHBsYW5rdjogJ1xcdTIxMEYnLFxuXHRwbHVzOiAnXFx1MDAyQicsXG5cdHBsdXNhY2lyOiAnXFx1MkEyMycsXG5cdHBsdXNiOiAnXFx1MjI5RScsXG5cdHBsdXNjaXI6ICdcXHUyQTIyJyxcblx0cGx1c2RvOiAnXFx1MjIxNCcsXG5cdHBsdXNkdTogJ1xcdTJBMjUnLFxuXHRwbHVzZTogJ1xcdTJBNzInLFxuXHRQbHVzTWludXM6ICdcXHUwMEIxJyxcblx0cGx1c21uOiAnXFx1MDBCMScsXG5cdHBsdXNzaW06ICdcXHUyQTI2Jyxcblx0cGx1c3R3bzogJ1xcdTJBMjcnLFxuXHRwbTogJ1xcdTAwQjEnLFxuXHRQb2luY2FyZXBsYW5lOiAnXFx1MjEwQycsXG5cdHBvaW50aW50OiAnXFx1MkExNScsXG5cdFBvcGY6ICdcXHUyMTE5Jyxcblx0cG9wZjogJ1xcdUQ4MzVcXHVERDYxJyxcblx0cG91bmQ6ICdcXHUwMEEzJyxcblx0UHI6ICdcXHUyQUJCJyxcblx0cHI6ICdcXHUyMjdBJyxcblx0cHJhcDogJ1xcdTJBQjcnLFxuXHRwcmN1ZTogJ1xcdTIyN0MnLFxuXHRwckU6ICdcXHUyQUIzJyxcblx0cHJlOiAnXFx1MkFBRicsXG5cdHByZWM6ICdcXHUyMjdBJyxcblx0cHJlY2FwcHJveDogJ1xcdTJBQjcnLFxuXHRwcmVjY3VybHllcTogJ1xcdTIyN0MnLFxuXHRQcmVjZWRlczogJ1xcdTIyN0EnLFxuXHRQcmVjZWRlc0VxdWFsOiAnXFx1MkFBRicsXG5cdFByZWNlZGVzU2xhbnRFcXVhbDogJ1xcdTIyN0MnLFxuXHRQcmVjZWRlc1RpbGRlOiAnXFx1MjI3RScsXG5cdHByZWNlcTogJ1xcdTJBQUYnLFxuXHRwcmVjbmFwcHJveDogJ1xcdTJBQjknLFxuXHRwcmVjbmVxcTogJ1xcdTJBQjUnLFxuXHRwcmVjbnNpbTogJ1xcdTIyRTgnLFxuXHRwcmVjc2ltOiAnXFx1MjI3RScsXG5cdFByaW1lOiAnXFx1MjAzMycsXG5cdHByaW1lOiAnXFx1MjAzMicsXG5cdHByaW1lczogJ1xcdTIxMTknLFxuXHRwcm5hcDogJ1xcdTJBQjknLFxuXHRwcm5FOiAnXFx1MkFCNScsXG5cdHBybnNpbTogJ1xcdTIyRTgnLFxuXHRwcm9kOiAnXFx1MjIwRicsXG5cdFByb2R1Y3Q6ICdcXHUyMjBGJyxcblx0cHJvZmFsYXI6ICdcXHUyMzJFJyxcblx0cHJvZmxpbmU6ICdcXHUyMzEyJyxcblx0cHJvZnN1cmY6ICdcXHUyMzEzJyxcblx0cHJvcDogJ1xcdTIyMUQnLFxuXHRQcm9wb3J0aW9uOiAnXFx1MjIzNycsXG5cdFByb3BvcnRpb25hbDogJ1xcdTIyMUQnLFxuXHRwcm9wdG86ICdcXHUyMjFEJyxcblx0cHJzaW06ICdcXHUyMjdFJyxcblx0cHJ1cmVsOiAnXFx1MjJCMCcsXG5cdFBzY3I6ICdcXHVEODM1XFx1RENBQicsXG5cdHBzY3I6ICdcXHVEODM1XFx1RENDNScsXG5cdFBzaTogJ1xcdTAzQTgnLFxuXHRwc2k6ICdcXHUwM0M4Jyxcblx0cHVuY3NwOiAnXFx1MjAwOCcsXG5cdFFmcjogJ1xcdUQ4MzVcXHVERDE0Jyxcblx0cWZyOiAnXFx1RDgzNVxcdUREMkUnLFxuXHRxaW50OiAnXFx1MkEwQycsXG5cdFFvcGY6ICdcXHUyMTFBJyxcblx0cW9wZjogJ1xcdUQ4MzVcXHVERDYyJyxcblx0cXByaW1lOiAnXFx1MjA1NycsXG5cdFFzY3I6ICdcXHVEODM1XFx1RENBQycsXG5cdHFzY3I6ICdcXHVEODM1XFx1RENDNicsXG5cdHF1YXRlcm5pb25zOiAnXFx1MjEwRCcsXG5cdHF1YXRpbnQ6ICdcXHUyQTE2Jyxcblx0cXVlc3Q6ICdcXHUwMDNGJyxcblx0cXVlc3RlcTogJ1xcdTIyNUYnLFxuXHRRVU9UOiAnXFx1MDAyMicsXG5cdHF1b3Q6ICdcXHUwMDIyJyxcblx0ckFhcnI6ICdcXHUyMURCJyxcblx0cmFjZTogJ1xcdTIyM0RcXHUwMzMxJyxcblx0UmFjdXRlOiAnXFx1MDE1NCcsXG5cdHJhY3V0ZTogJ1xcdTAxNTUnLFxuXHRyYWRpYzogJ1xcdTIyMUEnLFxuXHRyYWVtcHR5djogJ1xcdTI5QjMnLFxuXHRSYW5nOiAnXFx1MjdFQicsXG5cdHJhbmc6ICdcXHUyN0U5Jyxcblx0cmFuZ2Q6ICdcXHUyOTkyJyxcblx0cmFuZ2U6ICdcXHUyOUE1Jyxcblx0cmFuZ2xlOiAnXFx1MjdFOScsXG5cdHJhcXVvOiAnXFx1MDBCQicsXG5cdFJhcnI6ICdcXHUyMUEwJyxcblx0ckFycjogJ1xcdTIxRDInLFxuXHRyYXJyOiAnXFx1MjE5MicsXG5cdHJhcnJhcDogJ1xcdTI5NzUnLFxuXHRyYXJyYjogJ1xcdTIxRTUnLFxuXHRyYXJyYmZzOiAnXFx1MjkyMCcsXG5cdHJhcnJjOiAnXFx1MjkzMycsXG5cdHJhcnJmczogJ1xcdTI5MUUnLFxuXHRyYXJyaGs6ICdcXHUyMUFBJyxcblx0cmFycmxwOiAnXFx1MjFBQycsXG5cdHJhcnJwbDogJ1xcdTI5NDUnLFxuXHRyYXJyc2ltOiAnXFx1Mjk3NCcsXG5cdFJhcnJ0bDogJ1xcdTI5MTYnLFxuXHRyYXJydGw6ICdcXHUyMUEzJyxcblx0cmFycnc6ICdcXHUyMTlEJyxcblx0ckF0YWlsOiAnXFx1MjkxQycsXG5cdHJhdGFpbDogJ1xcdTI5MUEnLFxuXHRyYXRpbzogJ1xcdTIyMzYnLFxuXHRyYXRpb25hbHM6ICdcXHUyMTFBJyxcblx0UkJhcnI6ICdcXHUyOTEwJyxcblx0ckJhcnI6ICdcXHUyOTBGJyxcblx0cmJhcnI6ICdcXHUyOTBEJyxcblx0cmJicms6ICdcXHUyNzczJyxcblx0cmJyYWNlOiAnXFx1MDA3RCcsXG5cdHJicmFjazogJ1xcdTAwNUQnLFxuXHRyYnJrZTogJ1xcdTI5OEMnLFxuXHRyYnJrc2xkOiAnXFx1Mjk4RScsXG5cdHJicmtzbHU6ICdcXHUyOTkwJyxcblx0UmNhcm9uOiAnXFx1MDE1OCcsXG5cdHJjYXJvbjogJ1xcdTAxNTknLFxuXHRSY2VkaWw6ICdcXHUwMTU2Jyxcblx0cmNlZGlsOiAnXFx1MDE1NycsXG5cdHJjZWlsOiAnXFx1MjMwOScsXG5cdHJjdWI6ICdcXHUwMDdEJyxcblx0UmN5OiAnXFx1MDQyMCcsXG5cdHJjeTogJ1xcdTA0NDAnLFxuXHRyZGNhOiAnXFx1MjkzNycsXG5cdHJkbGRoYXI6ICdcXHUyOTY5Jyxcblx0cmRxdW86ICdcXHUyMDFEJyxcblx0cmRxdW9yOiAnXFx1MjAxRCcsXG5cdHJkc2g6ICdcXHUyMUIzJyxcblx0UmU6ICdcXHUyMTFDJyxcblx0cmVhbDogJ1xcdTIxMUMnLFxuXHRyZWFsaW5lOiAnXFx1MjExQicsXG5cdHJlYWxwYXJ0OiAnXFx1MjExQycsXG5cdHJlYWxzOiAnXFx1MjExRCcsXG5cdHJlY3Q6ICdcXHUyNUFEJyxcblx0UkVHOiAnXFx1MDBBRScsXG5cdHJlZzogJ1xcdTAwQUUnLFxuXHRSZXZlcnNlRWxlbWVudDogJ1xcdTIyMEInLFxuXHRSZXZlcnNlRXF1aWxpYnJpdW06ICdcXHUyMUNCJyxcblx0UmV2ZXJzZVVwRXF1aWxpYnJpdW06ICdcXHUyOTZGJyxcblx0cmZpc2h0OiAnXFx1Mjk3RCcsXG5cdHJmbG9vcjogJ1xcdTIzMEInLFxuXHRSZnI6ICdcXHUyMTFDJyxcblx0cmZyOiAnXFx1RDgzNVxcdUREMkYnLFxuXHRySGFyOiAnXFx1Mjk2NCcsXG5cdHJoYXJkOiAnXFx1MjFDMScsXG5cdHJoYXJ1OiAnXFx1MjFDMCcsXG5cdHJoYXJ1bDogJ1xcdTI5NkMnLFxuXHRSaG86ICdcXHUwM0ExJyxcblx0cmhvOiAnXFx1MDNDMScsXG5cdHJob3Y6ICdcXHUwM0YxJyxcblx0UmlnaHRBbmdsZUJyYWNrZXQ6ICdcXHUyN0U5Jyxcblx0UmlnaHRBcnJvdzogJ1xcdTIxOTInLFxuXHRSaWdodGFycm93OiAnXFx1MjFEMicsXG5cdHJpZ2h0YXJyb3c6ICdcXHUyMTkyJyxcblx0UmlnaHRBcnJvd0JhcjogJ1xcdTIxRTUnLFxuXHRSaWdodEFycm93TGVmdEFycm93OiAnXFx1MjFDNCcsXG5cdHJpZ2h0YXJyb3d0YWlsOiAnXFx1MjFBMycsXG5cdFJpZ2h0Q2VpbGluZzogJ1xcdTIzMDknLFxuXHRSaWdodERvdWJsZUJyYWNrZXQ6ICdcXHUyN0U3Jyxcblx0UmlnaHREb3duVGVlVmVjdG9yOiAnXFx1Mjk1RCcsXG5cdFJpZ2h0RG93blZlY3RvcjogJ1xcdTIxQzInLFxuXHRSaWdodERvd25WZWN0b3JCYXI6ICdcXHUyOTU1Jyxcblx0UmlnaHRGbG9vcjogJ1xcdTIzMEInLFxuXHRyaWdodGhhcnBvb25kb3duOiAnXFx1MjFDMScsXG5cdHJpZ2h0aGFycG9vbnVwOiAnXFx1MjFDMCcsXG5cdHJpZ2h0bGVmdGFycm93czogJ1xcdTIxQzQnLFxuXHRyaWdodGxlZnRoYXJwb29uczogJ1xcdTIxQ0MnLFxuXHRyaWdodHJpZ2h0YXJyb3dzOiAnXFx1MjFDOScsXG5cdHJpZ2h0c3F1aWdhcnJvdzogJ1xcdTIxOUQnLFxuXHRSaWdodFRlZTogJ1xcdTIyQTInLFxuXHRSaWdodFRlZUFycm93OiAnXFx1MjFBNicsXG5cdFJpZ2h0VGVlVmVjdG9yOiAnXFx1Mjk1QicsXG5cdHJpZ2h0dGhyZWV0aW1lczogJ1xcdTIyQ0MnLFxuXHRSaWdodFRyaWFuZ2xlOiAnXFx1MjJCMycsXG5cdFJpZ2h0VHJpYW5nbGVCYXI6ICdcXHUyOUQwJyxcblx0UmlnaHRUcmlhbmdsZUVxdWFsOiAnXFx1MjJCNScsXG5cdFJpZ2h0VXBEb3duVmVjdG9yOiAnXFx1Mjk0RicsXG5cdFJpZ2h0VXBUZWVWZWN0b3I6ICdcXHUyOTVDJyxcblx0UmlnaHRVcFZlY3RvcjogJ1xcdTIxQkUnLFxuXHRSaWdodFVwVmVjdG9yQmFyOiAnXFx1Mjk1NCcsXG5cdFJpZ2h0VmVjdG9yOiAnXFx1MjFDMCcsXG5cdFJpZ2h0VmVjdG9yQmFyOiAnXFx1Mjk1MycsXG5cdHJpbmc6ICdcXHUwMkRBJyxcblx0cmlzaW5nZG90c2VxOiAnXFx1MjI1MycsXG5cdHJsYXJyOiAnXFx1MjFDNCcsXG5cdHJsaGFyOiAnXFx1MjFDQycsXG5cdHJsbTogJ1xcdTIwMEYnLFxuXHRybW91c3Q6ICdcXHUyM0IxJyxcblx0cm1vdXN0YWNoZTogJ1xcdTIzQjEnLFxuXHRybm1pZDogJ1xcdTJBRUUnLFxuXHRyb2FuZzogJ1xcdTI3RUQnLFxuXHRyb2FycjogJ1xcdTIxRkUnLFxuXHRyb2JyazogJ1xcdTI3RTcnLFxuXHRyb3BhcjogJ1xcdTI5ODYnLFxuXHRSb3BmOiAnXFx1MjExRCcsXG5cdHJvcGY6ICdcXHVEODM1XFx1REQ2MycsXG5cdHJvcGx1czogJ1xcdTJBMkUnLFxuXHRyb3RpbWVzOiAnXFx1MkEzNScsXG5cdFJvdW5kSW1wbGllczogJ1xcdTI5NzAnLFxuXHRycGFyOiAnXFx1MDAyOScsXG5cdHJwYXJndDogJ1xcdTI5OTQnLFxuXHRycHBvbGludDogJ1xcdTJBMTInLFxuXHRycmFycjogJ1xcdTIxQzknLFxuXHRScmlnaHRhcnJvdzogJ1xcdTIxREInLFxuXHRyc2FxdW86ICdcXHUyMDNBJyxcblx0UnNjcjogJ1xcdTIxMUInLFxuXHRyc2NyOiAnXFx1RDgzNVxcdURDQzcnLFxuXHRSc2g6ICdcXHUyMUIxJyxcblx0cnNoOiAnXFx1MjFCMScsXG5cdHJzcWI6ICdcXHUwMDVEJyxcblx0cnNxdW86ICdcXHUyMDE5Jyxcblx0cnNxdW9yOiAnXFx1MjAxOScsXG5cdHJ0aHJlZTogJ1xcdTIyQ0MnLFxuXHRydGltZXM6ICdcXHUyMkNBJyxcblx0cnRyaTogJ1xcdTI1QjknLFxuXHRydHJpZTogJ1xcdTIyQjUnLFxuXHRydHJpZjogJ1xcdTI1QjgnLFxuXHRydHJpbHRyaTogJ1xcdTI5Q0UnLFxuXHRSdWxlRGVsYXllZDogJ1xcdTI5RjQnLFxuXHRydWx1aGFyOiAnXFx1Mjk2OCcsXG5cdHJ4OiAnXFx1MjExRScsXG5cdFNhY3V0ZTogJ1xcdTAxNUEnLFxuXHRzYWN1dGU6ICdcXHUwMTVCJyxcblx0c2JxdW86ICdcXHUyMDFBJyxcblx0U2M6ICdcXHUyQUJDJyxcblx0c2M6ICdcXHUyMjdCJyxcblx0c2NhcDogJ1xcdTJBQjgnLFxuXHRTY2Fyb246ICdcXHUwMTYwJyxcblx0c2Nhcm9uOiAnXFx1MDE2MScsXG5cdHNjY3VlOiAnXFx1MjI3RCcsXG5cdHNjRTogJ1xcdTJBQjQnLFxuXHRzY2U6ICdcXHUyQUIwJyxcblx0U2NlZGlsOiAnXFx1MDE1RScsXG5cdHNjZWRpbDogJ1xcdTAxNUYnLFxuXHRTY2lyYzogJ1xcdTAxNUMnLFxuXHRzY2lyYzogJ1xcdTAxNUQnLFxuXHRzY25hcDogJ1xcdTJBQkEnLFxuXHRzY25FOiAnXFx1MkFCNicsXG5cdHNjbnNpbTogJ1xcdTIyRTknLFxuXHRzY3BvbGludDogJ1xcdTJBMTMnLFxuXHRzY3NpbTogJ1xcdTIyN0YnLFxuXHRTY3k6ICdcXHUwNDIxJyxcblx0c2N5OiAnXFx1MDQ0MScsXG5cdHNkb3Q6ICdcXHUyMkM1Jyxcblx0c2RvdGI6ICdcXHUyMkExJyxcblx0c2RvdGU6ICdcXHUyQTY2Jyxcblx0c2VhcmhrOiAnXFx1MjkyNScsXG5cdHNlQXJyOiAnXFx1MjFEOCcsXG5cdHNlYXJyOiAnXFx1MjE5OCcsXG5cdHNlYXJyb3c6ICdcXHUyMTk4Jyxcblx0c2VjdDogJ1xcdTAwQTcnLFxuXHRzZW1pOiAnXFx1MDAzQicsXG5cdHNlc3dhcjogJ1xcdTI5MjknLFxuXHRzZXRtaW51czogJ1xcdTIyMTYnLFxuXHRzZXRtbjogJ1xcdTIyMTYnLFxuXHRzZXh0OiAnXFx1MjczNicsXG5cdFNmcjogJ1xcdUQ4MzVcXHVERDE2Jyxcblx0c2ZyOiAnXFx1RDgzNVxcdUREMzAnLFxuXHRzZnJvd246ICdcXHUyMzIyJyxcblx0c2hhcnA6ICdcXHUyNjZGJyxcblx0U0hDSGN5OiAnXFx1MDQyOScsXG5cdHNoY2hjeTogJ1xcdTA0NDknLFxuXHRTSGN5OiAnXFx1MDQyOCcsXG5cdHNoY3k6ICdcXHUwNDQ4Jyxcblx0U2hvcnREb3duQXJyb3c6ICdcXHUyMTkzJyxcblx0U2hvcnRMZWZ0QXJyb3c6ICdcXHUyMTkwJyxcblx0c2hvcnRtaWQ6ICdcXHUyMjIzJyxcblx0c2hvcnRwYXJhbGxlbDogJ1xcdTIyMjUnLFxuXHRTaG9ydFJpZ2h0QXJyb3c6ICdcXHUyMTkyJyxcblx0U2hvcnRVcEFycm93OiAnXFx1MjE5MScsXG5cdHNoeTogJ1xcdTAwQUQnLFxuXHRTaWdtYTogJ1xcdTAzQTMnLFxuXHRzaWdtYTogJ1xcdTAzQzMnLFxuXHRzaWdtYWY6ICdcXHUwM0MyJyxcblx0c2lnbWF2OiAnXFx1MDNDMicsXG5cdHNpbTogJ1xcdTIyM0MnLFxuXHRzaW1kb3Q6ICdcXHUyQTZBJyxcblx0c2ltZTogJ1xcdTIyNDMnLFxuXHRzaW1lcTogJ1xcdTIyNDMnLFxuXHRzaW1nOiAnXFx1MkE5RScsXG5cdHNpbWdFOiAnXFx1MkFBMCcsXG5cdHNpbWw6ICdcXHUyQTlEJyxcblx0c2ltbEU6ICdcXHUyQTlGJyxcblx0c2ltbmU6ICdcXHUyMjQ2Jyxcblx0c2ltcGx1czogJ1xcdTJBMjQnLFxuXHRzaW1yYXJyOiAnXFx1Mjk3MicsXG5cdHNsYXJyOiAnXFx1MjE5MCcsXG5cdFNtYWxsQ2lyY2xlOiAnXFx1MjIxOCcsXG5cdHNtYWxsc2V0bWludXM6ICdcXHUyMjE2Jyxcblx0c21hc2hwOiAnXFx1MkEzMycsXG5cdHNtZXBhcnNsOiAnXFx1MjlFNCcsXG5cdHNtaWQ6ICdcXHUyMjIzJyxcblx0c21pbGU6ICdcXHUyMzIzJyxcblx0c210OiAnXFx1MkFBQScsXG5cdHNtdGU6ICdcXHUyQUFDJyxcblx0c210ZXM6ICdcXHUyQUFDXFx1RkUwMCcsXG5cdFNPRlRjeTogJ1xcdTA0MkMnLFxuXHRzb2Z0Y3k6ICdcXHUwNDRDJyxcblx0c29sOiAnXFx1MDAyRicsXG5cdHNvbGI6ICdcXHUyOUM0Jyxcblx0c29sYmFyOiAnXFx1MjMzRicsXG5cdFNvcGY6ICdcXHVEODM1XFx1REQ0QScsXG5cdHNvcGY6ICdcXHVEODM1XFx1REQ2NCcsXG5cdHNwYWRlczogJ1xcdTI2NjAnLFxuXHRzcGFkZXN1aXQ6ICdcXHUyNjYwJyxcblx0c3BhcjogJ1xcdTIyMjUnLFxuXHRzcWNhcDogJ1xcdTIyOTMnLFxuXHRzcWNhcHM6ICdcXHUyMjkzXFx1RkUwMCcsXG5cdHNxY3VwOiAnXFx1MjI5NCcsXG5cdHNxY3VwczogJ1xcdTIyOTRcXHVGRTAwJyxcblx0U3FydDogJ1xcdTIyMUEnLFxuXHRzcXN1YjogJ1xcdTIyOEYnLFxuXHRzcXN1YmU6ICdcXHUyMjkxJyxcblx0c3FzdWJzZXQ6ICdcXHUyMjhGJyxcblx0c3FzdWJzZXRlcTogJ1xcdTIyOTEnLFxuXHRzcXN1cDogJ1xcdTIyOTAnLFxuXHRzcXN1cGU6ICdcXHUyMjkyJyxcblx0c3FzdXBzZXQ6ICdcXHUyMjkwJyxcblx0c3FzdXBzZXRlcTogJ1xcdTIyOTInLFxuXHRzcXU6ICdcXHUyNUExJyxcblx0U3F1YXJlOiAnXFx1MjVBMScsXG5cdHNxdWFyZTogJ1xcdTI1QTEnLFxuXHRTcXVhcmVJbnRlcnNlY3Rpb246ICdcXHUyMjkzJyxcblx0U3F1YXJlU3Vic2V0OiAnXFx1MjI4RicsXG5cdFNxdWFyZVN1YnNldEVxdWFsOiAnXFx1MjI5MScsXG5cdFNxdWFyZVN1cGVyc2V0OiAnXFx1MjI5MCcsXG5cdFNxdWFyZVN1cGVyc2V0RXF1YWw6ICdcXHUyMjkyJyxcblx0U3F1YXJlVW5pb246ICdcXHUyMjk0Jyxcblx0c3F1YXJmOiAnXFx1MjVBQScsXG5cdHNxdWY6ICdcXHUyNUFBJyxcblx0c3JhcnI6ICdcXHUyMTkyJyxcblx0U3NjcjogJ1xcdUQ4MzVcXHVEQ0FFJyxcblx0c3NjcjogJ1xcdUQ4MzVcXHVEQ0M4Jyxcblx0c3NldG1uOiAnXFx1MjIxNicsXG5cdHNzbWlsZTogJ1xcdTIzMjMnLFxuXHRzc3RhcmY6ICdcXHUyMkM2Jyxcblx0U3RhcjogJ1xcdTIyQzYnLFxuXHRzdGFyOiAnXFx1MjYwNicsXG5cdHN0YXJmOiAnXFx1MjYwNScsXG5cdHN0cmFpZ2h0ZXBzaWxvbjogJ1xcdTAzRjUnLFxuXHRzdHJhaWdodHBoaTogJ1xcdTAzRDUnLFxuXHRzdHJuczogJ1xcdTAwQUYnLFxuXHRTdWI6ICdcXHUyMkQwJyxcblx0c3ViOiAnXFx1MjI4MicsXG5cdHN1YmRvdDogJ1xcdTJBQkQnLFxuXHRzdWJFOiAnXFx1MkFDNScsXG5cdHN1YmU6ICdcXHUyMjg2Jyxcblx0c3ViZWRvdDogJ1xcdTJBQzMnLFxuXHRzdWJtdWx0OiAnXFx1MkFDMScsXG5cdHN1Ym5FOiAnXFx1MkFDQicsXG5cdHN1Ym5lOiAnXFx1MjI4QScsXG5cdHN1YnBsdXM6ICdcXHUyQUJGJyxcblx0c3VicmFycjogJ1xcdTI5NzknLFxuXHRTdWJzZXQ6ICdcXHUyMkQwJyxcblx0c3Vic2V0OiAnXFx1MjI4MicsXG5cdHN1YnNldGVxOiAnXFx1MjI4NicsXG5cdHN1YnNldGVxcTogJ1xcdTJBQzUnLFxuXHRTdWJzZXRFcXVhbDogJ1xcdTIyODYnLFxuXHRzdWJzZXRuZXE6ICdcXHUyMjhBJyxcblx0c3Vic2V0bmVxcTogJ1xcdTJBQ0InLFxuXHRzdWJzaW06ICdcXHUyQUM3Jyxcblx0c3Vic3ViOiAnXFx1MkFENScsXG5cdHN1YnN1cDogJ1xcdTJBRDMnLFxuXHRzdWNjOiAnXFx1MjI3QicsXG5cdHN1Y2NhcHByb3g6ICdcXHUyQUI4Jyxcblx0c3VjY2N1cmx5ZXE6ICdcXHUyMjdEJyxcblx0U3VjY2VlZHM6ICdcXHUyMjdCJyxcblx0U3VjY2VlZHNFcXVhbDogJ1xcdTJBQjAnLFxuXHRTdWNjZWVkc1NsYW50RXF1YWw6ICdcXHUyMjdEJyxcblx0U3VjY2VlZHNUaWxkZTogJ1xcdTIyN0YnLFxuXHRzdWNjZXE6ICdcXHUyQUIwJyxcblx0c3VjY25hcHByb3g6ICdcXHUyQUJBJyxcblx0c3VjY25lcXE6ICdcXHUyQUI2Jyxcblx0c3VjY25zaW06ICdcXHUyMkU5Jyxcblx0c3VjY3NpbTogJ1xcdTIyN0YnLFxuXHRTdWNoVGhhdDogJ1xcdTIyMEInLFxuXHRTdW06ICdcXHUyMjExJyxcblx0c3VtOiAnXFx1MjIxMScsXG5cdHN1bmc6ICdcXHUyNjZBJyxcblx0U3VwOiAnXFx1MjJEMScsXG5cdHN1cDogJ1xcdTIyODMnLFxuXHRzdXAxOiAnXFx1MDBCOScsXG5cdHN1cDI6ICdcXHUwMEIyJyxcblx0c3VwMzogJ1xcdTAwQjMnLFxuXHRzdXBkb3Q6ICdcXHUyQUJFJyxcblx0c3VwZHN1YjogJ1xcdTJBRDgnLFxuXHRzdXBFOiAnXFx1MkFDNicsXG5cdHN1cGU6ICdcXHUyMjg3Jyxcblx0c3VwZWRvdDogJ1xcdTJBQzQnLFxuXHRTdXBlcnNldDogJ1xcdTIyODMnLFxuXHRTdXBlcnNldEVxdWFsOiAnXFx1MjI4NycsXG5cdHN1cGhzb2w6ICdcXHUyN0M5Jyxcblx0c3VwaHN1YjogJ1xcdTJBRDcnLFxuXHRzdXBsYXJyOiAnXFx1Mjk3QicsXG5cdHN1cG11bHQ6ICdcXHUyQUMyJyxcblx0c3VwbkU6ICdcXHUyQUNDJyxcblx0c3VwbmU6ICdcXHUyMjhCJyxcblx0c3VwcGx1czogJ1xcdTJBQzAnLFxuXHRTdXBzZXQ6ICdcXHUyMkQxJyxcblx0c3Vwc2V0OiAnXFx1MjI4MycsXG5cdHN1cHNldGVxOiAnXFx1MjI4NycsXG5cdHN1cHNldGVxcTogJ1xcdTJBQzYnLFxuXHRzdXBzZXRuZXE6ICdcXHUyMjhCJyxcblx0c3Vwc2V0bmVxcTogJ1xcdTJBQ0MnLFxuXHRzdXBzaW06ICdcXHUyQUM4Jyxcblx0c3Vwc3ViOiAnXFx1MkFENCcsXG5cdHN1cHN1cDogJ1xcdTJBRDYnLFxuXHRzd2FyaGs6ICdcXHUyOTI2Jyxcblx0c3dBcnI6ICdcXHUyMUQ5Jyxcblx0c3dhcnI6ICdcXHUyMTk5Jyxcblx0c3dhcnJvdzogJ1xcdTIxOTknLFxuXHRzd253YXI6ICdcXHUyOTJBJyxcblx0c3psaWc6ICdcXHUwMERGJyxcblx0VGFiOiAnXFx1MDAwOScsXG5cdHRhcmdldDogJ1xcdTIzMTYnLFxuXHRUYXU6ICdcXHUwM0E0Jyxcblx0dGF1OiAnXFx1MDNDNCcsXG5cdHRicms6ICdcXHUyM0I0Jyxcblx0VGNhcm9uOiAnXFx1MDE2NCcsXG5cdHRjYXJvbjogJ1xcdTAxNjUnLFxuXHRUY2VkaWw6ICdcXHUwMTYyJyxcblx0dGNlZGlsOiAnXFx1MDE2MycsXG5cdFRjeTogJ1xcdTA0MjInLFxuXHR0Y3k6ICdcXHUwNDQyJyxcblx0dGRvdDogJ1xcdTIwREInLFxuXHR0ZWxyZWM6ICdcXHUyMzE1Jyxcblx0VGZyOiAnXFx1RDgzNVxcdUREMTcnLFxuXHR0ZnI6ICdcXHVEODM1XFx1REQzMScsXG5cdHRoZXJlNDogJ1xcdTIyMzQnLFxuXHRUaGVyZWZvcmU6ICdcXHUyMjM0Jyxcblx0dGhlcmVmb3JlOiAnXFx1MjIzNCcsXG5cdFRoZXRhOiAnXFx1MDM5OCcsXG5cdHRoZXRhOiAnXFx1MDNCOCcsXG5cdHRoZXRhc3ltOiAnXFx1MDNEMScsXG5cdHRoZXRhdjogJ1xcdTAzRDEnLFxuXHR0aGlja2FwcHJveDogJ1xcdTIyNDgnLFxuXHR0aGlja3NpbTogJ1xcdTIyM0MnLFxuXHRUaGlja1NwYWNlOiAnXFx1MjA1RlxcdTIwMEEnLFxuXHR0aGluc3A6ICdcXHUyMDA5Jyxcblx0VGhpblNwYWNlOiAnXFx1MjAwOScsXG5cdHRoa2FwOiAnXFx1MjI0OCcsXG5cdHRoa3NpbTogJ1xcdTIyM0MnLFxuXHRUSE9STjogJ1xcdTAwREUnLFxuXHR0aG9ybjogJ1xcdTAwRkUnLFxuXHRUaWxkZTogJ1xcdTIyM0MnLFxuXHR0aWxkZTogJ1xcdTAyREMnLFxuXHRUaWxkZUVxdWFsOiAnXFx1MjI0MycsXG5cdFRpbGRlRnVsbEVxdWFsOiAnXFx1MjI0NScsXG5cdFRpbGRlVGlsZGU6ICdcXHUyMjQ4Jyxcblx0dGltZXM6ICdcXHUwMEQ3Jyxcblx0dGltZXNiOiAnXFx1MjJBMCcsXG5cdHRpbWVzYmFyOiAnXFx1MkEzMScsXG5cdHRpbWVzZDogJ1xcdTJBMzAnLFxuXHR0aW50OiAnXFx1MjIyRCcsXG5cdHRvZWE6ICdcXHUyOTI4Jyxcblx0dG9wOiAnXFx1MjJBNCcsXG5cdHRvcGJvdDogJ1xcdTIzMzYnLFxuXHR0b3BjaXI6ICdcXHUyQUYxJyxcblx0VG9wZjogJ1xcdUQ4MzVcXHVERDRCJyxcblx0dG9wZjogJ1xcdUQ4MzVcXHVERDY1Jyxcblx0dG9wZm9yazogJ1xcdTJBREEnLFxuXHR0b3NhOiAnXFx1MjkyOScsXG5cdHRwcmltZTogJ1xcdTIwMzQnLFxuXHRUUkFERTogJ1xcdTIxMjInLFxuXHR0cmFkZTogJ1xcdTIxMjInLFxuXHR0cmlhbmdsZTogJ1xcdTI1QjUnLFxuXHR0cmlhbmdsZWRvd246ICdcXHUyNUJGJyxcblx0dHJpYW5nbGVsZWZ0OiAnXFx1MjVDMycsXG5cdHRyaWFuZ2xlbGVmdGVxOiAnXFx1MjJCNCcsXG5cdHRyaWFuZ2xlcTogJ1xcdTIyNUMnLFxuXHR0cmlhbmdsZXJpZ2h0OiAnXFx1MjVCOScsXG5cdHRyaWFuZ2xlcmlnaHRlcTogJ1xcdTIyQjUnLFxuXHR0cmlkb3Q6ICdcXHUyNUVDJyxcblx0dHJpZTogJ1xcdTIyNUMnLFxuXHR0cmltaW51czogJ1xcdTJBM0EnLFxuXHRUcmlwbGVEb3Q6ICdcXHUyMERCJyxcblx0dHJpcGx1czogJ1xcdTJBMzknLFxuXHR0cmlzYjogJ1xcdTI5Q0QnLFxuXHR0cml0aW1lOiAnXFx1MkEzQicsXG5cdHRycGV6aXVtOiAnXFx1MjNFMicsXG5cdFRzY3I6ICdcXHVEODM1XFx1RENBRicsXG5cdHRzY3I6ICdcXHVEODM1XFx1RENDOScsXG5cdFRTY3k6ICdcXHUwNDI2Jyxcblx0dHNjeTogJ1xcdTA0NDYnLFxuXHRUU0hjeTogJ1xcdTA0MEInLFxuXHR0c2hjeTogJ1xcdTA0NUInLFxuXHRUc3Ryb2s6ICdcXHUwMTY2Jyxcblx0dHN0cm9rOiAnXFx1MDE2NycsXG5cdHR3aXh0OiAnXFx1MjI2QycsXG5cdHR3b2hlYWRsZWZ0YXJyb3c6ICdcXHUyMTlFJyxcblx0dHdvaGVhZHJpZ2h0YXJyb3c6ICdcXHUyMUEwJyxcblx0VWFjdXRlOiAnXFx1MDBEQScsXG5cdHVhY3V0ZTogJ1xcdTAwRkEnLFxuXHRVYXJyOiAnXFx1MjE5RicsXG5cdHVBcnI6ICdcXHUyMUQxJyxcblx0dWFycjogJ1xcdTIxOTEnLFxuXHRVYXJyb2NpcjogJ1xcdTI5NDknLFxuXHRVYnJjeTogJ1xcdTA0MEUnLFxuXHR1YnJjeTogJ1xcdTA0NUUnLFxuXHRVYnJldmU6ICdcXHUwMTZDJyxcblx0dWJyZXZlOiAnXFx1MDE2RCcsXG5cdFVjaXJjOiAnXFx1MDBEQicsXG5cdHVjaXJjOiAnXFx1MDBGQicsXG5cdFVjeTogJ1xcdTA0MjMnLFxuXHR1Y3k6ICdcXHUwNDQzJyxcblx0dWRhcnI6ICdcXHUyMUM1Jyxcblx0VWRibGFjOiAnXFx1MDE3MCcsXG5cdHVkYmxhYzogJ1xcdTAxNzEnLFxuXHR1ZGhhcjogJ1xcdTI5NkUnLFxuXHR1ZmlzaHQ6ICdcXHUyOTdFJyxcblx0VWZyOiAnXFx1RDgzNVxcdUREMTgnLFxuXHR1ZnI6ICdcXHVEODM1XFx1REQzMicsXG5cdFVncmF2ZTogJ1xcdTAwRDknLFxuXHR1Z3JhdmU6ICdcXHUwMEY5Jyxcblx0dUhhcjogJ1xcdTI5NjMnLFxuXHR1aGFybDogJ1xcdTIxQkYnLFxuXHR1aGFycjogJ1xcdTIxQkUnLFxuXHR1aGJsazogJ1xcdTI1ODAnLFxuXHR1bGNvcm46ICdcXHUyMzFDJyxcblx0dWxjb3JuZXI6ICdcXHUyMzFDJyxcblx0dWxjcm9wOiAnXFx1MjMwRicsXG5cdHVsdHJpOiAnXFx1MjVGOCcsXG5cdFVtYWNyOiAnXFx1MDE2QScsXG5cdHVtYWNyOiAnXFx1MDE2QicsXG5cdHVtbDogJ1xcdTAwQTgnLFxuXHRVbmRlckJhcjogJ1xcdTAwNUYnLFxuXHRVbmRlckJyYWNlOiAnXFx1MjNERicsXG5cdFVuZGVyQnJhY2tldDogJ1xcdTIzQjUnLFxuXHRVbmRlclBhcmVudGhlc2lzOiAnXFx1MjNERCcsXG5cdFVuaW9uOiAnXFx1MjJDMycsXG5cdFVuaW9uUGx1czogJ1xcdTIyOEUnLFxuXHRVb2dvbjogJ1xcdTAxNzInLFxuXHR1b2dvbjogJ1xcdTAxNzMnLFxuXHRVb3BmOiAnXFx1RDgzNVxcdURENEMnLFxuXHR1b3BmOiAnXFx1RDgzNVxcdURENjYnLFxuXHRVcEFycm93OiAnXFx1MjE5MScsXG5cdFVwYXJyb3c6ICdcXHUyMUQxJyxcblx0dXBhcnJvdzogJ1xcdTIxOTEnLFxuXHRVcEFycm93QmFyOiAnXFx1MjkxMicsXG5cdFVwQXJyb3dEb3duQXJyb3c6ICdcXHUyMUM1Jyxcblx0VXBEb3duQXJyb3c6ICdcXHUyMTk1Jyxcblx0VXBkb3duYXJyb3c6ICdcXHUyMUQ1Jyxcblx0dXBkb3duYXJyb3c6ICdcXHUyMTk1Jyxcblx0VXBFcXVpbGlicml1bTogJ1xcdTI5NkUnLFxuXHR1cGhhcnBvb25sZWZ0OiAnXFx1MjFCRicsXG5cdHVwaGFycG9vbnJpZ2h0OiAnXFx1MjFCRScsXG5cdHVwbHVzOiAnXFx1MjI4RScsXG5cdFVwcGVyTGVmdEFycm93OiAnXFx1MjE5NicsXG5cdFVwcGVyUmlnaHRBcnJvdzogJ1xcdTIxOTcnLFxuXHRVcHNpOiAnXFx1MDNEMicsXG5cdHVwc2k6ICdcXHUwM0M1Jyxcblx0dXBzaWg6ICdcXHUwM0QyJyxcblx0VXBzaWxvbjogJ1xcdTAzQTUnLFxuXHR1cHNpbG9uOiAnXFx1MDNDNScsXG5cdFVwVGVlOiAnXFx1MjJBNScsXG5cdFVwVGVlQXJyb3c6ICdcXHUyMUE1Jyxcblx0dXB1cGFycm93czogJ1xcdTIxQzgnLFxuXHR1cmNvcm46ICdcXHUyMzFEJyxcblx0dXJjb3JuZXI6ICdcXHUyMzFEJyxcblx0dXJjcm9wOiAnXFx1MjMwRScsXG5cdFVyaW5nOiAnXFx1MDE2RScsXG5cdHVyaW5nOiAnXFx1MDE2RicsXG5cdHVydHJpOiAnXFx1MjVGOScsXG5cdFVzY3I6ICdcXHVEODM1XFx1RENCMCcsXG5cdHVzY3I6ICdcXHVEODM1XFx1RENDQScsXG5cdHV0ZG90OiAnXFx1MjJGMCcsXG5cdFV0aWxkZTogJ1xcdTAxNjgnLFxuXHR1dGlsZGU6ICdcXHUwMTY5Jyxcblx0dXRyaTogJ1xcdTI1QjUnLFxuXHR1dHJpZjogJ1xcdTI1QjQnLFxuXHR1dWFycjogJ1xcdTIxQzgnLFxuXHRVdW1sOiAnXFx1MDBEQycsXG5cdHV1bWw6ICdcXHUwMEZDJyxcblx0dXdhbmdsZTogJ1xcdTI5QTcnLFxuXHR2YW5ncnQ6ICdcXHUyOTlDJyxcblx0dmFyZXBzaWxvbjogJ1xcdTAzRjUnLFxuXHR2YXJrYXBwYTogJ1xcdTAzRjAnLFxuXHR2YXJub3RoaW5nOiAnXFx1MjIwNScsXG5cdHZhcnBoaTogJ1xcdTAzRDUnLFxuXHR2YXJwaTogJ1xcdTAzRDYnLFxuXHR2YXJwcm9wdG86ICdcXHUyMjFEJyxcblx0dkFycjogJ1xcdTIxRDUnLFxuXHR2YXJyOiAnXFx1MjE5NScsXG5cdHZhcnJobzogJ1xcdTAzRjEnLFxuXHR2YXJzaWdtYTogJ1xcdTAzQzInLFxuXHR2YXJzdWJzZXRuZXE6ICdcXHUyMjhBXFx1RkUwMCcsXG5cdHZhcnN1YnNldG5lcXE6ICdcXHUyQUNCXFx1RkUwMCcsXG5cdHZhcnN1cHNldG5lcTogJ1xcdTIyOEJcXHVGRTAwJyxcblx0dmFyc3Vwc2V0bmVxcTogJ1xcdTJBQ0NcXHVGRTAwJyxcblx0dmFydGhldGE6ICdcXHUwM0QxJyxcblx0dmFydHJpYW5nbGVsZWZ0OiAnXFx1MjJCMicsXG5cdHZhcnRyaWFuZ2xlcmlnaHQ6ICdcXHUyMkIzJyxcblx0VmJhcjogJ1xcdTJBRUInLFxuXHR2QmFyOiAnXFx1MkFFOCcsXG5cdHZCYXJ2OiAnXFx1MkFFOScsXG5cdFZjeTogJ1xcdTA0MTInLFxuXHR2Y3k6ICdcXHUwNDMyJyxcblx0VkRhc2g6ICdcXHUyMkFCJyxcblx0VmRhc2g6ICdcXHUyMkE5Jyxcblx0dkRhc2g6ICdcXHUyMkE4Jyxcblx0dmRhc2g6ICdcXHUyMkEyJyxcblx0VmRhc2hsOiAnXFx1MkFFNicsXG5cdFZlZTogJ1xcdTIyQzEnLFxuXHR2ZWU6ICdcXHUyMjI4Jyxcblx0dmVlYmFyOiAnXFx1MjJCQicsXG5cdHZlZWVxOiAnXFx1MjI1QScsXG5cdHZlbGxpcDogJ1xcdTIyRUUnLFxuXHRWZXJiYXI6ICdcXHUyMDE2Jyxcblx0dmVyYmFyOiAnXFx1MDA3QycsXG5cdFZlcnQ6ICdcXHUyMDE2Jyxcblx0dmVydDogJ1xcdTAwN0MnLFxuXHRWZXJ0aWNhbEJhcjogJ1xcdTIyMjMnLFxuXHRWZXJ0aWNhbExpbmU6ICdcXHUwMDdDJyxcblx0VmVydGljYWxTZXBhcmF0b3I6ICdcXHUyNzU4Jyxcblx0VmVydGljYWxUaWxkZTogJ1xcdTIyNDAnLFxuXHRWZXJ5VGhpblNwYWNlOiAnXFx1MjAwQScsXG5cdFZmcjogJ1xcdUQ4MzVcXHVERDE5Jyxcblx0dmZyOiAnXFx1RDgzNVxcdUREMzMnLFxuXHR2bHRyaTogJ1xcdTIyQjInLFxuXHR2bnN1YjogJ1xcdTIyODJcXHUyMEQyJyxcblx0dm5zdXA6ICdcXHUyMjgzXFx1MjBEMicsXG5cdFZvcGY6ICdcXHVEODM1XFx1REQ0RCcsXG5cdHZvcGY6ICdcXHVEODM1XFx1REQ2NycsXG5cdHZwcm9wOiAnXFx1MjIxRCcsXG5cdHZydHJpOiAnXFx1MjJCMycsXG5cdFZzY3I6ICdcXHVEODM1XFx1RENCMScsXG5cdHZzY3I6ICdcXHVEODM1XFx1RENDQicsXG5cdHZzdWJuRTogJ1xcdTJBQ0JcXHVGRTAwJyxcblx0dnN1Ym5lOiAnXFx1MjI4QVxcdUZFMDAnLFxuXHR2c3VwbkU6ICdcXHUyQUNDXFx1RkUwMCcsXG5cdHZzdXBuZTogJ1xcdTIyOEJcXHVGRTAwJyxcblx0VnZkYXNoOiAnXFx1MjJBQScsXG5cdHZ6aWd6YWc6ICdcXHUyOTlBJyxcblx0V2NpcmM6ICdcXHUwMTc0Jyxcblx0d2NpcmM6ICdcXHUwMTc1Jyxcblx0d2VkYmFyOiAnXFx1MkE1RicsXG5cdFdlZGdlOiAnXFx1MjJDMCcsXG5cdHdlZGdlOiAnXFx1MjIyNycsXG5cdHdlZGdlcTogJ1xcdTIyNTknLFxuXHR3ZWllcnA6ICdcXHUyMTE4Jyxcblx0V2ZyOiAnXFx1RDgzNVxcdUREMUEnLFxuXHR3ZnI6ICdcXHVEODM1XFx1REQzNCcsXG5cdFdvcGY6ICdcXHVEODM1XFx1REQ0RScsXG5cdHdvcGY6ICdcXHVEODM1XFx1REQ2OCcsXG5cdHdwOiAnXFx1MjExOCcsXG5cdHdyOiAnXFx1MjI0MCcsXG5cdHdyZWF0aDogJ1xcdTIyNDAnLFxuXHRXc2NyOiAnXFx1RDgzNVxcdURDQjInLFxuXHR3c2NyOiAnXFx1RDgzNVxcdURDQ0MnLFxuXHR4Y2FwOiAnXFx1MjJDMicsXG5cdHhjaXJjOiAnXFx1MjVFRicsXG5cdHhjdXA6ICdcXHUyMkMzJyxcblx0eGR0cmk6ICdcXHUyNUJEJyxcblx0WGZyOiAnXFx1RDgzNVxcdUREMUInLFxuXHR4ZnI6ICdcXHVEODM1XFx1REQzNScsXG5cdHhoQXJyOiAnXFx1MjdGQScsXG5cdHhoYXJyOiAnXFx1MjdGNycsXG5cdFhpOiAnXFx1MDM5RScsXG5cdHhpOiAnXFx1MDNCRScsXG5cdHhsQXJyOiAnXFx1MjdGOCcsXG5cdHhsYXJyOiAnXFx1MjdGNScsXG5cdHhtYXA6ICdcXHUyN0ZDJyxcblx0eG5pczogJ1xcdTIyRkInLFxuXHR4b2RvdDogJ1xcdTJBMDAnLFxuXHRYb3BmOiAnXFx1RDgzNVxcdURENEYnLFxuXHR4b3BmOiAnXFx1RDgzNVxcdURENjknLFxuXHR4b3BsdXM6ICdcXHUyQTAxJyxcblx0eG90aW1lOiAnXFx1MkEwMicsXG5cdHhyQXJyOiAnXFx1MjdGOScsXG5cdHhyYXJyOiAnXFx1MjdGNicsXG5cdFhzY3I6ICdcXHVEODM1XFx1RENCMycsXG5cdHhzY3I6ICdcXHVEODM1XFx1RENDRCcsXG5cdHhzcWN1cDogJ1xcdTJBMDYnLFxuXHR4dXBsdXM6ICdcXHUyQTA0Jyxcblx0eHV0cmk6ICdcXHUyNUIzJyxcblx0eHZlZTogJ1xcdTIyQzEnLFxuXHR4d2VkZ2U6ICdcXHUyMkMwJyxcblx0WWFjdXRlOiAnXFx1MDBERCcsXG5cdHlhY3V0ZTogJ1xcdTAwRkQnLFxuXHRZQWN5OiAnXFx1MDQyRicsXG5cdHlhY3k6ICdcXHUwNDRGJyxcblx0WWNpcmM6ICdcXHUwMTc2Jyxcblx0eWNpcmM6ICdcXHUwMTc3Jyxcblx0WWN5OiAnXFx1MDQyQicsXG5cdHljeTogJ1xcdTA0NEInLFxuXHR5ZW46ICdcXHUwMEE1Jyxcblx0WWZyOiAnXFx1RDgzNVxcdUREMUMnLFxuXHR5ZnI6ICdcXHVEODM1XFx1REQzNicsXG5cdFlJY3k6ICdcXHUwNDA3Jyxcblx0eWljeTogJ1xcdTA0NTcnLFxuXHRZb3BmOiAnXFx1RDgzNVxcdURENTAnLFxuXHR5b3BmOiAnXFx1RDgzNVxcdURENkEnLFxuXHRZc2NyOiAnXFx1RDgzNVxcdURDQjQnLFxuXHR5c2NyOiAnXFx1RDgzNVxcdURDQ0UnLFxuXHRZVWN5OiAnXFx1MDQyRScsXG5cdHl1Y3k6ICdcXHUwNDRFJyxcblx0WXVtbDogJ1xcdTAxNzgnLFxuXHR5dW1sOiAnXFx1MDBGRicsXG5cdFphY3V0ZTogJ1xcdTAxNzknLFxuXHR6YWN1dGU6ICdcXHUwMTdBJyxcblx0WmNhcm9uOiAnXFx1MDE3RCcsXG5cdHpjYXJvbjogJ1xcdTAxN0UnLFxuXHRaY3k6ICdcXHUwNDE3Jyxcblx0emN5OiAnXFx1MDQzNycsXG5cdFpkb3Q6ICdcXHUwMTdCJyxcblx0emRvdDogJ1xcdTAxN0MnLFxuXHR6ZWV0cmY6ICdcXHUyMTI4Jyxcblx0WmVyb1dpZHRoU3BhY2U6ICdcXHUyMDBCJyxcblx0WmV0YTogJ1xcdTAzOTYnLFxuXHR6ZXRhOiAnXFx1MDNCNicsXG5cdFpmcjogJ1xcdTIxMjgnLFxuXHR6ZnI6ICdcXHVEODM1XFx1REQzNycsXG5cdFpIY3k6ICdcXHUwNDE2Jyxcblx0emhjeTogJ1xcdTA0MzYnLFxuXHR6aWdyYXJyOiAnXFx1MjFERCcsXG5cdFpvcGY6ICdcXHUyMTI0Jyxcblx0em9wZjogJ1xcdUQ4MzVcXHVERDZCJyxcblx0WnNjcjogJ1xcdUQ4MzVcXHVEQ0I1Jyxcblx0enNjcjogJ1xcdUQ4MzVcXHVEQ0NGJyxcblx0endqOiAnXFx1MjAwRCcsXG5cdHp3bmo6ICdcXHUyMDBDJyxcbn0pO1xuXG4vKipcbiAqIEBkZXByZWNhdGVkIHVzZSBgSFRNTF9FTlRJVElFU2AgaW5zdGVhZFxuICogQHNlZSBIVE1MX0VOVElUSUVTXG4gKi9cbmV4cG9ydHMuZW50aXR5TWFwID0gZXhwb3J0cy5IVE1MX0VOVElUSUVTO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xmldom/xmldom/lib/entities.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xmldom/xmldom/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/@xmldom/xmldom/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var dom = __webpack_require__(/*! ./dom */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/dom.js\")\nexports.DOMImplementation = dom.DOMImplementation\nexports.XMLSerializer = dom.XMLSerializer\nexports.DOMParser = __webpack_require__(/*! ./dom-parser */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/dom-parser.js\").DOMParser\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHhtbGRvbS94bWxkb20vbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBLFVBQVUsbUJBQU8sQ0FBQyw2REFBTztBQUN6Qix5QkFBeUI7QUFDekIscUJBQXFCO0FBQ3JCLDhIQUFxRCIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9AeG1sZG9tL3htbGRvbS9saWIvaW5kZXguanM/NDkwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgZG9tID0gcmVxdWlyZSgnLi9kb20nKVxuZXhwb3J0cy5ET01JbXBsZW1lbnRhdGlvbiA9IGRvbS5ET01JbXBsZW1lbnRhdGlvblxuZXhwb3J0cy5YTUxTZXJpYWxpemVyID0gZG9tLlhNTFNlcmlhbGl6ZXJcbmV4cG9ydHMuRE9NUGFyc2VyID0gcmVxdWlyZSgnLi9kb20tcGFyc2VyJykuRE9NUGFyc2VyXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xmldom/xmldom/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xmldom/xmldom/lib/sax.js":
/*!************************************************!*\
  !*** ./node_modules/@xmldom/xmldom/lib/sax.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var NAMESPACE = (__webpack_require__(/*! ./conventions */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/conventions.js\").NAMESPACE);\n\n//[4]   \tNameStartChar\t   ::=   \t\":\" | [A-Z] | \"_\" | [a-z] | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x2FF] | [#x370-#x37D] | [#x37F-#x1FFF] | [#x200C-#x200D] | [#x2070-#x218F] | [#x2C00-#x2FEF] | [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n//[4a]   \tNameChar\t   ::=   \tNameStartChar | \"-\" | \".\" | [0-9] | #xB7 | [#x0300-#x036F] | [#x203F-#x2040]\n//[5]   \tName\t   ::=   \tNameStartChar (NameChar)*\nvar nameStartChar = /[A-Z_a-z\\xC0-\\xD6\\xD8-\\xF6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]///\\u10000-\\uEFFFF\nvar nameChar = new RegExp(\"[\\\\-\\\\.0-9\"+nameStartChar.source.slice(1,-1)+\"\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040]\");\nvar tagNamePattern = new RegExp('^'+nameStartChar.source+nameChar.source+'*(?:\\:'+nameStartChar.source+nameChar.source+'*)?$');\n//var tagNamePattern = /^[a-zA-Z_][\\w\\-\\.]*(?:\\:[a-zA-Z_][\\w\\-\\.]*)?$/\n//var handlers = 'resolveEntity,getExternalSubset,characters,endDocument,endElement,endPrefixMapping,ignorableWhitespace,processingInstruction,setDocumentLocator,skippedEntity,startDocument,startElement,startPrefixMapping,notationDecl,unparsedEntityDecl,error,fatalError,warning,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,comment,endCDATA,endDTD,endEntity,startCDATA,startDTD,startEntity'.split(',')\n\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\nvar S_TAG = 0;//tag name offerring\nvar S_ATTR = 1;//attr name offerring\nvar S_ATTR_SPACE=2;//attr name end and space offer\nvar S_EQ = 3;//=space?\nvar S_ATTR_NOQUOT_VALUE = 4;//attr value(no quot value only)\nvar S_ATTR_END = 5;//attr value end and no space(quot end)\nvar S_TAG_SPACE = 6;//(attr value end || tag end ) && (space offer)\nvar S_TAG_CLOSE = 7;//closed el<el />\n\n/**\n * Creates an error that will not be caught by XMLReader aka the SAX parser.\n *\n * @param {string} message\n * @param {any?} locator Optional, can provide details about the location in the source\n * @constructor\n */\nfunction ParseError(message, locator) {\n\tthis.message = message\n\tthis.locator = locator\n\tif(Error.captureStackTrace) Error.captureStackTrace(this, ParseError);\n}\nParseError.prototype = new Error();\nParseError.prototype.name = ParseError.name\n\nfunction XMLReader(){\n\n}\n\nXMLReader.prototype = {\n\tparse:function(source,defaultNSMap,entityMap){\n\t\tvar domBuilder = this.domBuilder;\n\t\tdomBuilder.startDocument();\n\t\t_copy(defaultNSMap ,defaultNSMap = {})\n\t\tparse(source,defaultNSMap,entityMap,\n\t\t\t\tdomBuilder,this.errorHandler);\n\t\tdomBuilder.endDocument();\n\t}\n}\nfunction parse(source,defaultNSMapCopy,entityMap,domBuilder,errorHandler){\n\tfunction fixedFromCharCode(code) {\n\t\t// String.prototype.fromCharCode does not supports\n\t\t// > 2 bytes unicode chars directly\n\t\tif (code > 0xffff) {\n\t\t\tcode -= 0x10000;\n\t\t\tvar surrogate1 = 0xd800 + (code >> 10)\n\t\t\t\t, surrogate2 = 0xdc00 + (code & 0x3ff);\n\n\t\t\treturn String.fromCharCode(surrogate1, surrogate2);\n\t\t} else {\n\t\t\treturn String.fromCharCode(code);\n\t\t}\n\t}\n\tfunction entityReplacer(a){\n\t\tvar k = a.slice(1,-1);\n\t\tif (Object.hasOwnProperty.call(entityMap, k)) {\n\t\t\treturn entityMap[k];\n\t\t}else if(k.charAt(0) === '#'){\n\t\t\treturn fixedFromCharCode(parseInt(k.substr(1).replace('x','0x')))\n\t\t}else{\n\t\t\terrorHandler.error('entity not found:'+a);\n\t\t\treturn a;\n\t\t}\n\t}\n\tfunction appendText(end){//has some bugs\n\t\tif(end>start){\n\t\t\tvar xt = source.substring(start,end).replace(/&#?\\w+;/g,entityReplacer);\n\t\t\tlocator&&position(start);\n\t\t\tdomBuilder.characters(xt,0,end-start);\n\t\t\tstart = end\n\t\t}\n\t}\n\tfunction position(p,m){\n\t\twhile(p>=lineEnd && (m = linePattern.exec(source))){\n\t\t\tlineStart = m.index;\n\t\t\tlineEnd = lineStart + m[0].length;\n\t\t\tlocator.lineNumber++;\n\t\t\t//console.log('line++:',locator,startPos,endPos)\n\t\t}\n\t\tlocator.columnNumber = p-lineStart+1;\n\t}\n\tvar lineStart = 0;\n\tvar lineEnd = 0;\n\tvar linePattern = /.*(?:\\r\\n?|\\n)|.*$/g\n\tvar locator = domBuilder.locator;\n\n\tvar parseStack = [{currentNSMap:defaultNSMapCopy}]\n\tvar closeMap = {};\n\tvar start = 0;\n\twhile(true){\n\t\ttry{\n\t\t\tvar tagStart = source.indexOf('<',start);\n\t\t\tif(tagStart<0){\n\t\t\t\tif(!source.substr(start).match(/^\\s*$/)){\n\t\t\t\t\tvar doc = domBuilder.doc;\n\t    \t\t\tvar text = doc.createTextNode(source.substr(start));\n\t    \t\t\tdoc.appendChild(text);\n\t    \t\t\tdomBuilder.currentElement = text;\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif(tagStart>start){\n\t\t\t\tappendText(tagStart);\n\t\t\t}\n\t\t\tswitch(source.charAt(tagStart+1)){\n\t\t\tcase '/':\n\t\t\t\tvar end = source.indexOf('>',tagStart+3);\n\t\t\t\tvar tagName = source.substring(tagStart + 2, end).replace(/[ \\t\\n\\r]+$/g, '');\n\t\t\t\tvar config = parseStack.pop();\n\t\t\t\tif(end<0){\n\n\t        \t\ttagName = source.substring(tagStart+2).replace(/[\\s<].*/,'');\n\t        \t\terrorHandler.error(\"end tag name: \"+tagName+' is not complete:'+config.tagName);\n\t        \t\tend = tagStart+1+tagName.length;\n\t        \t}else if(tagName.match(/\\s</)){\n\t        \t\ttagName = tagName.replace(/[\\s<].*/,'');\n\t        \t\terrorHandler.error(\"end tag name: \"+tagName+' maybe not complete');\n\t        \t\tend = tagStart+1+tagName.length;\n\t\t\t\t}\n\t\t\t\tvar localNSMap = config.localNSMap;\n\t\t\t\tvar endMatch = config.tagName == tagName;\n\t\t\t\tvar endIgnoreCaseMach = endMatch || config.tagName&&config.tagName.toLowerCase() == tagName.toLowerCase()\n\t\t        if(endIgnoreCaseMach){\n\t\t        \tdomBuilder.endElement(config.uri,config.localName,tagName);\n\t\t\t\t\tif(localNSMap){\n\t\t\t\t\t\tfor (var prefix in localNSMap) {\n\t\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(localNSMap, prefix)) {\n\t\t\t\t\t\t\t\tdomBuilder.endPrefixMapping(prefix);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(!endMatch){\n\t\t            \terrorHandler.fatalError(\"end tag name: \"+tagName+' is not match the current start tagName:'+config.tagName ); // No known test case\n\t\t\t\t\t}\n\t\t        }else{\n\t\t        \tparseStack.push(config)\n\t\t        }\n\n\t\t\t\tend++;\n\t\t\t\tbreak;\n\t\t\t\t// end elment\n\t\t\tcase '?':// <?...?>\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tend = parseInstruction(source,tagStart,domBuilder);\n\t\t\t\tbreak;\n\t\t\tcase '!':// <!doctype,<![CDATA,<!--\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tend = parseDCC(source,tagStart,domBuilder,errorHandler);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tvar el = new ElementAttributes();\n\t\t\t\tvar currentNSMap = parseStack[parseStack.length-1].currentNSMap;\n\t\t\t\t//elStartEnd\n\t\t\t\tvar end = parseElementStartPart(source,tagStart,el,currentNSMap,entityReplacer,errorHandler);\n\t\t\t\tvar len = el.length;\n\n\n\t\t\t\tif(!el.closed && fixSelfClosed(source,end,el.tagName,closeMap)){\n\t\t\t\t\tel.closed = true;\n\t\t\t\t\tif(!entityMap.nbsp){\n\t\t\t\t\t\terrorHandler.warning('unclosed xml attribute');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(locator && len){\n\t\t\t\t\tvar locator2 = copyLocator(locator,{});\n\t\t\t\t\t//try{//attribute position fixed\n\t\t\t\t\tfor(var i = 0;i<len;i++){\n\t\t\t\t\t\tvar a = el[i];\n\t\t\t\t\t\tposition(a.offset);\n\t\t\t\t\t\ta.locator = copyLocator(locator,{});\n\t\t\t\t\t}\n\t\t\t\t\tdomBuilder.locator = locator2\n\t\t\t\t\tif(appendElement(el,domBuilder,currentNSMap)){\n\t\t\t\t\t\tparseStack.push(el)\n\t\t\t\t\t}\n\t\t\t\t\tdomBuilder.locator = locator;\n\t\t\t\t}else{\n\t\t\t\t\tif(appendElement(el,domBuilder,currentNSMap)){\n\t\t\t\t\t\tparseStack.push(el)\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (NAMESPACE.isHTML(el.uri) && !el.closed) {\n\t\t\t\t\tend = parseHtmlSpecialContent(source,end,el.tagName,entityReplacer,domBuilder)\n\t\t\t\t} else {\n\t\t\t\t\tend++;\n\t\t\t\t}\n\t\t\t}\n\t\t}catch(e){\n\t\t\tif (e instanceof ParseError) {\n\t\t\t\tthrow e;\n\t\t\t}\n\t\t\terrorHandler.error('element parse error: '+e)\n\t\t\tend = -1;\n\t\t}\n\t\tif(end>start){\n\t\t\tstart = end;\n\t\t}else{\n\t\t\t//TODO: 这里有可能sax回退，有位置错误风险\n\t\t\tappendText(Math.max(tagStart,start)+1);\n\t\t}\n\t}\n}\nfunction copyLocator(f,t){\n\tt.lineNumber = f.lineNumber;\n\tt.columnNumber = f.columnNumber;\n\treturn t;\n}\n\n/**\n * @see #appendElement(source,elStartEnd,el,selfClosed,entityReplacer,domBuilder,parseStack);\n * @return end of the elementStartPart(end of elementEndPart for selfClosed el)\n */\nfunction parseElementStartPart(source,start,el,currentNSMap,entityReplacer,errorHandler){\n\n\t/**\n\t * @param {string} qname\n\t * @param {string} value\n\t * @param {number} startIndex\n\t */\n\tfunction addAttribute(qname, value, startIndex) {\n\t\tif (el.attributeNames.hasOwnProperty(qname)) {\n\t\t\terrorHandler.fatalError('Attribute ' + qname + ' redefined')\n\t\t}\n\t\tel.addValue(\n\t\t\tqname,\n\t\t\t// @see https://www.w3.org/TR/xml/#AVNormalize\n\t\t\t// since the xmldom sax parser does not \"interpret\" DTD the following is not implemented:\n\t\t\t// - recursive replacement of (DTD) entity references\n\t\t\t// - trimming and collapsing multiple spaces into a single one for attributes that are not of type CDATA\n\t\t\tvalue.replace(/[\\t\\n\\r]/g, ' ').replace(/&#?\\w+;/g, entityReplacer),\n\t\t\tstartIndex\n\t\t)\n\t}\n\tvar attrName;\n\tvar value;\n\tvar p = ++start;\n\tvar s = S_TAG;//status\n\twhile(true){\n\t\tvar c = source.charAt(p);\n\t\tswitch(c){\n\t\tcase '=':\n\t\t\tif(s === S_ATTR){//attrName\n\t\t\t\tattrName = source.slice(start,p);\n\t\t\t\ts = S_EQ;\n\t\t\t}else if(s === S_ATTR_SPACE){\n\t\t\t\ts = S_EQ;\n\t\t\t}else{\n\t\t\t\t//fatalError: equal must after attrName or space after attrName\n\t\t\t\tthrow new Error('attribute equal must after attrName'); // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase '\\'':\n\t\tcase '\"':\n\t\t\tif(s === S_EQ || s === S_ATTR //|| s == S_ATTR_SPACE\n\t\t\t\t){//equal\n\t\t\t\tif(s === S_ATTR){\n\t\t\t\t\terrorHandler.warning('attribute value must after \"=\"')\n\t\t\t\t\tattrName = source.slice(start,p)\n\t\t\t\t}\n\t\t\t\tstart = p+1;\n\t\t\t\tp = source.indexOf(c,start)\n\t\t\t\tif(p>0){\n\t\t\t\t\tvalue = source.slice(start, p);\n\t\t\t\t\taddAttribute(attrName, value, start-1);\n\t\t\t\t\ts = S_ATTR_END;\n\t\t\t\t}else{\n\t\t\t\t\t//fatalError: no end quot match\n\t\t\t\t\tthrow new Error('attribute value no end \\''+c+'\\' match');\n\t\t\t\t}\n\t\t\t}else if(s == S_ATTR_NOQUOT_VALUE){\n\t\t\t\tvalue = source.slice(start, p);\n\t\t\t\taddAttribute(attrName, value, start);\n\t\t\t\terrorHandler.warning('attribute \"'+attrName+'\" missed start quot('+c+')!!');\n\t\t\t\tstart = p+1;\n\t\t\t\ts = S_ATTR_END\n\t\t\t}else{\n\t\t\t\t//fatalError: no equal before\n\t\t\t\tthrow new Error('attribute value must after \"=\"'); // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase '/':\n\t\t\tswitch(s){\n\t\t\tcase S_TAG:\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\tcase S_ATTR_END:\n\t\t\tcase S_TAG_SPACE:\n\t\t\tcase S_TAG_CLOSE:\n\t\t\t\ts =S_TAG_CLOSE;\n\t\t\t\tel.closed = true;\n\t\t\tcase S_ATTR_NOQUOT_VALUE:\n\t\t\tcase S_ATTR:\n\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_SPACE:\n\t\t\t\t\tel.closed = true;\n\t\t\t\tbreak;\n\t\t\t//case S_EQ:\n\t\t\tdefault:\n\t\t\t\tthrow new Error(\"attribute invalid close char('/')\") // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase ''://end document\n\t\t\terrorHandler.error('unexpected end of input');\n\t\t\tif(s == S_TAG){\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\t}\n\t\t\treturn p;\n\t\tcase '>':\n\t\t\tswitch(s){\n\t\t\tcase S_TAG:\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\tcase S_ATTR_END:\n\t\t\tcase S_TAG_SPACE:\n\t\t\tcase S_TAG_CLOSE:\n\t\t\t\tbreak;//normal\n\t\t\tcase S_ATTR_NOQUOT_VALUE://Compatible state\n\t\t\tcase S_ATTR:\n\t\t\t\tvalue = source.slice(start,p);\n\t\t\t\tif(value.slice(-1) === '/'){\n\t\t\t\t\tel.closed  = true;\n\t\t\t\t\tvalue = value.slice(0,-1)\n\t\t\t\t}\n\t\t\tcase S_ATTR_SPACE:\n\t\t\t\tif(s === S_ATTR_SPACE){\n\t\t\t\t\tvalue = attrName;\n\t\t\t\t}\n\t\t\t\tif(s == S_ATTR_NOQUOT_VALUE){\n\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed quot(\")!');\n\t\t\t\t\taddAttribute(attrName, value, start)\n\t\t\t\t}else{\n\t\t\t\t\tif(!NAMESPACE.isHTML(currentNSMap['']) || !value.match(/^(?:disabled|checked|selected)$/i)){\n\t\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed value!! \"'+value+'\" instead!!')\n\t\t\t\t\t}\n\t\t\t\t\taddAttribute(value, value, start)\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase S_EQ:\n\t\t\t\tthrow new Error('attribute value missed!!');\n\t\t\t}\n//\t\t\tconsole.log(tagName,tagNamePattern,tagNamePattern.test(tagName))\n\t\t\treturn p;\n\t\t/*xml space '\\x20' | #x9 | #xD | #xA; */\n\t\tcase '\\u0080':\n\t\t\tc = ' ';\n\t\tdefault:\n\t\t\tif(c<= ' '){//space\n\t\t\t\tswitch(s){\n\t\t\t\tcase S_TAG:\n\t\t\t\t\tel.setTagName(source.slice(start,p));//tagName\n\t\t\t\t\ts = S_TAG_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR:\n\t\t\t\t\tattrName = source.slice(start,p)\n\t\t\t\t\ts = S_ATTR_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_NOQUOT_VALUE:\n\t\t\t\t\tvar value = source.slice(start, p);\n\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed quot(\")!!');\n\t\t\t\t\taddAttribute(attrName, value, start)\n\t\t\t\tcase S_ATTR_END:\n\t\t\t\t\ts = S_TAG_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\t//case S_TAG_SPACE:\n\t\t\t\t//case S_EQ:\n\t\t\t\t//case S_ATTR_SPACE:\n\t\t\t\t//\tvoid();break;\n\t\t\t\t//case S_TAG_CLOSE:\n\t\t\t\t\t//ignore warning\n\t\t\t\t}\n\t\t\t}else{//not space\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\n\t\t\t\tswitch(s){\n\t\t\t\t//case S_TAG:void();break;\n\t\t\t\t//case S_ATTR:void();break;\n\t\t\t\t//case S_ATTR_NOQUOT_VALUE:void();break;\n\t\t\t\tcase S_ATTR_SPACE:\n\t\t\t\t\tvar tagName =  el.tagName;\n\t\t\t\t\tif (!NAMESPACE.isHTML(currentNSMap['']) || !attrName.match(/^(?:disabled|checked|selected)$/i)) {\n\t\t\t\t\t\terrorHandler.warning('attribute \"'+attrName+'\" missed value!! \"'+attrName+'\" instead2!!')\n\t\t\t\t\t}\n\t\t\t\t\taddAttribute(attrName, attrName, start);\n\t\t\t\t\tstart = p;\n\t\t\t\t\ts = S_ATTR;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_END:\n\t\t\t\t\terrorHandler.warning('attribute space is required\"'+attrName+'\"!!')\n\t\t\t\tcase S_TAG_SPACE:\n\t\t\t\t\ts = S_ATTR;\n\t\t\t\t\tstart = p;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_EQ:\n\t\t\t\t\ts = S_ATTR_NOQUOT_VALUE;\n\t\t\t\t\tstart = p;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_TAG_CLOSE:\n\t\t\t\t\tthrow new Error(\"elements closed character '/' and '>' must be connected to\");\n\t\t\t\t}\n\t\t\t}\n\t\t}//end outer switch\n\t\t//console.log('p++',p)\n\t\tp++;\n\t}\n}\n/**\n * @return true if has new namespace define\n */\nfunction appendElement(el,domBuilder,currentNSMap){\n\tvar tagName = el.tagName;\n\tvar localNSMap = null;\n\t//var currentNSMap = parseStack[parseStack.length-1].currentNSMap;\n\tvar i = el.length;\n\twhile(i--){\n\t\tvar a = el[i];\n\t\tvar qName = a.qName;\n\t\tvar value = a.value;\n\t\tvar nsp = qName.indexOf(':');\n\t\tif(nsp>0){\n\t\t\tvar prefix = a.prefix = qName.slice(0,nsp);\n\t\t\tvar localName = qName.slice(nsp+1);\n\t\t\tvar nsPrefix = prefix === 'xmlns' && localName\n\t\t}else{\n\t\t\tlocalName = qName;\n\t\t\tprefix = null\n\t\t\tnsPrefix = qName === 'xmlns' && ''\n\t\t}\n\t\t//can not set prefix,because prefix !== ''\n\t\ta.localName = localName ;\n\t\t//prefix == null for no ns prefix attribute\n\t\tif(nsPrefix !== false){//hack!!\n\t\t\tif(localNSMap == null){\n\t\t\t\tlocalNSMap = {}\n\t\t\t\t//console.log(currentNSMap,0)\n\t\t\t\t_copy(currentNSMap,currentNSMap={})\n\t\t\t\t//console.log(currentNSMap,1)\n\t\t\t}\n\t\t\tcurrentNSMap[nsPrefix] = localNSMap[nsPrefix] = value;\n\t\t\ta.uri = NAMESPACE.XMLNS\n\t\t\tdomBuilder.startPrefixMapping(nsPrefix, value)\n\t\t}\n\t}\n\tvar i = el.length;\n\twhile(i--){\n\t\ta = el[i];\n\t\tvar prefix = a.prefix;\n\t\tif(prefix){//no prefix attribute has no namespace\n\t\t\tif(prefix === 'xml'){\n\t\t\t\ta.uri = NAMESPACE.XML;\n\t\t\t}if(prefix !== 'xmlns'){\n\t\t\t\ta.uri = currentNSMap[prefix || '']\n\n\t\t\t\t//{console.log('###'+a.qName,domBuilder.locator.systemId+'',currentNSMap,a.uri)}\n\t\t\t}\n\t\t}\n\t}\n\tvar nsp = tagName.indexOf(':');\n\tif(nsp>0){\n\t\tprefix = el.prefix = tagName.slice(0,nsp);\n\t\tlocalName = el.localName = tagName.slice(nsp+1);\n\t}else{\n\t\tprefix = null;//important!!\n\t\tlocalName = el.localName = tagName;\n\t}\n\t//no prefix element has default namespace\n\tvar ns = el.uri = currentNSMap[prefix || ''];\n\tdomBuilder.startElement(ns,localName,tagName,el);\n\t//endPrefixMapping and startPrefixMapping have not any help for dom builder\n\t//localNSMap = null\n\tif(el.closed){\n\t\tdomBuilder.endElement(ns,localName,tagName);\n\t\tif(localNSMap){\n\t\t\tfor (prefix in localNSMap) {\n\t\t\t\tif (Object.prototype.hasOwnProperty.call(localNSMap, prefix)) {\n\t\t\t\t\tdomBuilder.endPrefixMapping(prefix);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}else{\n\t\tel.currentNSMap = currentNSMap;\n\t\tel.localNSMap = localNSMap;\n\t\t//parseStack.push(el);\n\t\treturn true;\n\t}\n}\nfunction parseHtmlSpecialContent(source,elStartEnd,tagName,entityReplacer,domBuilder){\n\tif(/^(?:script|textarea)$/i.test(tagName)){\n\t\tvar elEndStart =  source.indexOf('</'+tagName+'>',elStartEnd);\n\t\tvar text = source.substring(elStartEnd+1,elEndStart);\n\t\tif(/[&<]/.test(text)){\n\t\t\tif(/^script$/i.test(tagName)){\n\t\t\t\t//if(!/\\]\\]>/.test(text)){\n\t\t\t\t\t//lexHandler.startCDATA();\n\t\t\t\t\tdomBuilder.characters(text,0,text.length);\n\t\t\t\t\t//lexHandler.endCDATA();\n\t\t\t\t\treturn elEndStart;\n\t\t\t\t//}\n\t\t\t}//}else{//text area\n\t\t\t\ttext = text.replace(/&#?\\w+;/g,entityReplacer);\n\t\t\t\tdomBuilder.characters(text,0,text.length);\n\t\t\t\treturn elEndStart;\n\t\t\t//}\n\n\t\t}\n\t}\n\treturn elStartEnd+1;\n}\nfunction fixSelfClosed(source,elStartEnd,tagName,closeMap){\n\t//if(tagName in closeMap){\n\tvar pos = closeMap[tagName];\n\tif(pos == null){\n\t\t//console.log(tagName)\n\t\tpos =  source.lastIndexOf('</'+tagName+'>')\n\t\tif(pos<elStartEnd){//忘记闭合\n\t\t\tpos = source.lastIndexOf('</'+tagName)\n\t\t}\n\t\tcloseMap[tagName] =pos\n\t}\n\treturn pos<elStartEnd;\n\t//}\n}\n\nfunction _copy (source, target) {\n\tfor (var n in source) {\n\t\tif (Object.prototype.hasOwnProperty.call(source, n)) {\n\t\t\ttarget[n] = source[n];\n\t\t}\n\t}\n}\n\nfunction parseDCC(source,start,domBuilder,errorHandler){//sure start with '<!'\n\tvar next= source.charAt(start+2)\n\tswitch(next){\n\tcase '-':\n\t\tif(source.charAt(start + 3) === '-'){\n\t\t\tvar end = source.indexOf('-->',start+4);\n\t\t\t//append comment source.substring(4,end)//<!--\n\t\t\tif(end>start){\n\t\t\t\tdomBuilder.comment(source,start+4,end-start-4);\n\t\t\t\treturn end+3;\n\t\t\t}else{\n\t\t\t\terrorHandler.error(\"Unclosed comment\");\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t}else{\n\t\t\t//error\n\t\t\treturn -1;\n\t\t}\n\tdefault:\n\t\tif(source.substr(start+3,6) == 'CDATA['){\n\t\t\tvar end = source.indexOf(']]>',start+9);\n\t\t\tdomBuilder.startCDATA();\n\t\t\tdomBuilder.characters(source,start+9,end-start-9);\n\t\t\tdomBuilder.endCDATA()\n\t\t\treturn end+3;\n\t\t}\n\t\t//<!DOCTYPE\n\t\t//startDTD(java.lang.String name, java.lang.String publicId, java.lang.String systemId)\n\t\tvar matchs = split(source,start);\n\t\tvar len = matchs.length;\n\t\tif(len>1 && /!doctype/i.test(matchs[0][0])){\n\t\t\tvar name = matchs[1][0];\n\t\t\tvar pubid = false;\n\t\t\tvar sysid = false;\n\t\t\tif(len>3){\n\t\t\t\tif(/^public$/i.test(matchs[2][0])){\n\t\t\t\t\tpubid = matchs[3][0];\n\t\t\t\t\tsysid = len>4 && matchs[4][0];\n\t\t\t\t}else if(/^system$/i.test(matchs[2][0])){\n\t\t\t\t\tsysid = matchs[3][0];\n\t\t\t\t}\n\t\t\t}\n\t\t\tvar lastMatch = matchs[len-1]\n\t\t\tdomBuilder.startDTD(name, pubid, sysid);\n\t\t\tdomBuilder.endDTD();\n\n\t\t\treturn lastMatch.index+lastMatch[0].length\n\t\t}\n\t}\n\treturn -1;\n}\n\n\n\nfunction parseInstruction(source,start,domBuilder){\n\tvar end = source.indexOf('?>',start);\n\tif(end){\n\t\tvar match = source.substring(start,end).match(/^<\\?(\\S*)\\s*([\\s\\S]*?)\\s*$/);\n\t\tif(match){\n\t\t\tvar len = match[0].length;\n\t\t\tdomBuilder.processingInstruction(match[1], match[2]) ;\n\t\t\treturn end+2;\n\t\t}else{//error\n\t\t\treturn -1;\n\t\t}\n\t}\n\treturn -1;\n}\n\nfunction ElementAttributes(){\n\tthis.attributeNames = {}\n}\nElementAttributes.prototype = {\n\tsetTagName:function(tagName){\n\t\tif(!tagNamePattern.test(tagName)){\n\t\t\tthrow new Error('invalid tagName:'+tagName)\n\t\t}\n\t\tthis.tagName = tagName\n\t},\n\taddValue:function(qName, value, offset) {\n\t\tif(!tagNamePattern.test(qName)){\n\t\t\tthrow new Error('invalid attribute:'+qName)\n\t\t}\n\t\tthis.attributeNames[qName] = this.length;\n\t\tthis[this.length++] = {qName:qName,value:value,offset:offset}\n\t},\n\tlength:0,\n\tgetLocalName:function(i){return this[i].localName},\n\tgetLocator:function(i){return this[i].locator},\n\tgetQName:function(i){return this[i].qName},\n\tgetURI:function(i){return this[i].uri},\n\tgetValue:function(i){return this[i].value}\n//\t,getIndex:function(uri, localName)){\n//\t\tif(localName){\n//\n//\t\t}else{\n//\t\t\tvar qName = uri\n//\t\t}\n//\t},\n//\tgetValue:function(){return this.getValue(this.getIndex.apply(this,arguments))},\n//\tgetType:function(uri,localName){}\n//\tgetType:function(i){},\n}\n\n\n\nfunction split(source,start){\n\tvar match;\n\tvar buf = [];\n\tvar reg = /'[^']+'|\"[^\"]+\"|[^\\s<>\\/=]+=?|(\\/?\\s*>|<)/g;\n\treg.lastIndex = start;\n\treg.exec(source);//skip <\n\twhile(match = reg.exec(source)){\n\t\tbuf.push(match);\n\t\tif(match[1])return buf;\n\t}\n}\n\nexports.XMLReader = XMLReader;\nexports.ParseError = ParseError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xmldom/xmldom/lib/sax.js\n");

/***/ })

};
;