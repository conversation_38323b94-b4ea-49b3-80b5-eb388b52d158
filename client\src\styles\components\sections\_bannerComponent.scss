#banner-component {
  background-color: $lightBlue;
  min-height: 50vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;

  .heading-h1,
  .sub-heading {
    width: 70%;

    @include media-query(mobile, tablet) {
      width: 100%;
    }
  }

  &.center-banner {
    text-align: center;

    .heading-h1,
    .sub-heading {
      margin: auto;
    }
  }
  &.expert-care-banner{
      align-items: flex-end;
    @include media-query(mobile){
    text-align: center;
    }
    .btn{
      @include media-query(mobile){
        margin: auto;
      }
    }
    .sub-heading{
      width: 100%;
      @include media-query(mobile){
        display: none;
      }
    }
  }
}
#join-us-banner {
  @extend #banner-component;
  flex-direction: column;
  .top-section {
    margin-top: 150px;
    @include media-query(mobile) {
      margin-top: 100px;
    }
    @include media-query(tablet) {
      margin-top: 100px;
    }
  }
  .btn {
    width: fit-content;
    margin: 10px auto;
  }
  .avatars {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    width: 100%;
    margin: auto auto 20px auto;
    min-height: 80px;
    justify-content: flex-end;
    background-repeat: repeat-x;
    background-position: 0 0;
    background-size: auto 100%;
    height: 80px;

    animation: moveBackground 10s linear infinite;
    &.last-one {
      margin-top: 0 !important;
      animation: moveBackgroundLeft 10s linear infinite;
    }
  }

  @keyframes moveBackgroundLeft {
    from {
      background-position-x: 0;
    }
    to {
      background-position-x: 100%; /* Negative value to create continuous scroll */
    }
  }
  @keyframes moveBackground {
    from {
      background-position-x: 0;
    }
    to {
      background-position-x: -100%; /* Negative value to create continuous scroll */
    }
  }
}

#glossary-banner {
  @extend #banner-component;
  flex-direction: column;
  .top-section {
    margin-bottom: 260px;

    @include media-query(extraLargeScreens) {
      margin-bottom: 250px;
    }

    @include media-query(mobile) {
      margin-top: 50px;
    }
    @include media-query(tablet) {
      margin-top: 30px;
    }

    svg {
      scale: 0.8;
    }

    .glossary-search {
      display: ruby;
    }

    .glossary-search-input {
      background: white;
      border-radius: 25px;
      height: 50px;
      width: 100%;
      max-width: 650px;
      padding: 4px 20px;
      margin: auto;
    }

    .glossary-search-input:before {
      border-bottom: none;
    }

    .glossary-search-input:after {
      border-bottom: none;
    }

    .letters {
      display: flex;
      justify-self: center;
      margin-top: 20px;
      flex-wrap: wrap;

      .letter {
        margin: 0px 6px;
        font-size: 20px;
        text-decoration: none;
        color: $white;

        @include media-query(mobile, tablet) {
          font-size: 18px;
        }

        &.selected {
          color: $yellow;
        }
      }
    }

    .btn-search {
      background-color: $blue;
      padding: 10px 0px;
      position: absolute;
      margin: 6px -55px 0px;
      border-radius: 20px;
      min-width: 40px;

      svg {
        scale: 1;
        path {
          stroke: $white;
        }
      }
    }
  }

  .btn {
    width: fit-content;
    margin: 10px auto;
  }
}
#africa-banner {
  background-color: $lightBlue;
  min-height: 50vh;
  display: flex;
  justify-content: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;

  .container {
    flex-direction: row;
    display: flex;
    align-items: center;
    justify-content: space-between;
    align-content: center;
    height: 100%;
    @include media-query(mobile) {
      // margin-top:10px;
      align-content: flex-end;
    }
  }

  .right-section {
    padding: 30px 20px;
    background-color: rgba(11, 48, 81, 0.68);
    @include media-query(mobile) {
      margin-top: 20px;
      margin-bottom: 20px;
      padding: 16px;
    }
    .bold {
      margin-top: 20px;
      font-family: "Proxima-Nova-Semibold" !important;
    }

    .btns {
      margin-top: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .btn {
        font-family: "Proxima-Nova-Semibold" !important;
        text-transform: none;

        padding: 12px;

        &:first-child {
          margin-right: 10px;
        }

        &:last-child {
          margin-left: 10px;
        }
      }
    }
  }
}
#expert-care-benefits{
  .grid-item{
    .sub-heading{
    display: flex;
    align-items: center;
    flex-direction: row;

    }
    svg{
      margin-right: 10px;
      path{
        stroke: green;
      }
    }
  }
}