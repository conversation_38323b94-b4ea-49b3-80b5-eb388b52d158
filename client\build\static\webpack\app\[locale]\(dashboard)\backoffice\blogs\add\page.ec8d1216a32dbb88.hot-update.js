"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js":
/*!*******************************************************!*\
  !*** ./node_modules/@mui/icons-material/esm/Close.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _utils_createSvgIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/createSvgIcon.js */ \"(app-pages-browser)/./node_modules/@mui/material/utils/createSvgIcon.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/* harmony default export */ __webpack_exports__[\"default\"] = ((0,_utils_createSvgIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n    d: \"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n}), \"Close\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AbXVpL2ljb25zLW1hdGVyaWFsL2VzbS9DbG9zZS5qcyIsIm1hcHBpbmdzIjoiOzs7NkRBRXFEO0FBQ0w7QUFDaEQsK0RBQWVBLG1FQUFhQSxDQUFDLFdBQVcsR0FBRUUsc0RBQUlBLENBQUMsUUFBUTtJQUNyREMsR0FBRztBQUNMLElBQUksVUFBUyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQG11aS9pY29ucy1tYXRlcmlhbC9lc20vQ2xvc2UuanM/ZGIwNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IGNyZWF0ZVN2Z0ljb24gZnJvbSBcIi4vdXRpbHMvY3JlYXRlU3ZnSWNvbi5qc1wiO1xuaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmV4cG9ydCBkZWZhdWx0IGNyZWF0ZVN2Z0ljb24oLyojX19QVVJFX18qL19qc3goXCJwYXRoXCIsIHtcbiAgZDogXCJNMTkgNi40MSAxNy41OSA1IDEyIDEwLjU5IDYuNDEgNSA1IDYuNDEgMTAuNTkgMTIgNSAxNy41OSA2LjQxIDE5IDEyIDEzLjQxIDE3LjU5IDE5IDE5IDE3LjU5IDEzLjQxIDEyelwiXG59KSwgJ0Nsb3NlJyk7Il0sIm5hbWVzIjpbImNyZWF0ZVN2Z0ljb24iLCJqc3giLCJfanN4IiwiZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Dialog,DialogTitle,IconButton,LinearProgress,Paper,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Description!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_icons_uploadIcon_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/icons/uploadIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/uploadIcon.svg\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Inject drag state styles\nconst dragActiveStyles = `\n  .file-labels.drag-active {\n    border-color: #1976d2 !important;\n    background-color: rgba(25, 118, 210, 0.04) !important;\n  }\n  .file-labels.disabled {\n    cursor: not-allowed !important;\n    opacity: 0.6 !important;\n  }\n`;\nif (typeof document !== \"undefined\" && !document.getElementById(\"document-importer-styles\")) {\n    const styleSheet = document.createElement(\"style\");\n    styleSheet.id = \"document-importer-styles\";\n    styleSheet.textContent = dragActiveStyles;\n    document.head.appendChild(styleSheet);\n}\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewOpen, setPreviewOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [extractedData, setExtractedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement((image)=>image.read(\"base64\").then((imageBuffer)=>({\n                                src: `data:${image.contentType};base64,${imageBuffer}`\n                            })))\n                }\n            });\n            setProgress(75);\n            const cleanContent = result.value.replace(/<p><\\/p>/g, \"\").replace(/\\s+/g, \" \").trim();\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            setExtractedData(result);\n            setSuccess(true);\n            setPreviewOpen(true);\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) processFile(acceptedFiles[0]);\n    }, []);\n    const { getRootProps, getInputProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing\n    });\n    const handleApplyContent = ()=>{\n        if (extractedData) {\n            onContentExtracted(extractedData.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(extractedData.metadata);\n            }\n            setPreviewOpen(false);\n            setExtractedData(null);\n        }\n    };\n    const handleClosePreview = ()=>{\n        setPreviewOpen(false);\n        setExtractedData(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        sx: {\n            mb: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: [\n                    t(\"createArticle:importDocument\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                sx: {\n                    p: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    ...getRootProps(),\n                    className: `file-labels ${isDragActive ? \"drag-active\" : \"\"} ${disabled || isProcessing ? \"disabled\" : \"\"}`,\n                    style: {\n                        display: \"block\",\n                        border: \"2px dashed #ccc\",\n                        padding: \"20px\",\n                        textAlign: \"center\",\n                        cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\",\n                        opacity: disabled || isProcessing ? 0.6 : 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ...getInputProps()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundImage: `url(\"${_assets_images_icons_uploadIcon_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"]}\")`,\n                                        backgroundSize: \"contain\",\n                                        backgroundRepeat: \"no-repeat\",\n                                        backgroundPosition: \"center\",\n                                        width: 60,\n                                        height: 60,\n                                        margin: \"auto\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"body1\",\n                                    mt: 2,\n                                    children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:importFromDocument\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"caption\",\n                                    children: [\n                                        t(\"createArticle:supportedFormats\"),\n                                        \": .docx, .doc, .txt\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        mt: 1,\n                                        display: \"flex\",\n                                        justifyContent: \"center\",\n                                        gap: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            label: \".docx\",\n                                            size: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            label: \".doc\",\n                                            size: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Description_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            label: \".txt\",\n                                            size: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"body2\",\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 262,\n                columnNumber: 9\n            }, undefined),\n            success && !previewOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 268,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                open: previewOpen,\n                onClose: handleClosePreview,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"h6\",\n                                    children: t(\"createArticle:previewExtractedContent\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    onClick: handleClosePreview,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogContent, {\n                        children: extractedData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            children: [\n                                extractedData.metadata.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    mb: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedTitle\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            children: extractedData.metadata.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 17\n                                }, undefined),\n                                extractedData.metadata.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    mb: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedDescription\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"body2\",\n                                            children: extractedData.metadata.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"subtitle2\",\n                                    color: \"primary\",\n                                    sx: {\n                                        mb: 1\n                                    },\n                                    children: [\n                                        t(\"createArticle:extractedContent\"),\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        maxHeight: 400,\n                                        overflow: \"auto\",\n                                        border: \"1px solid\",\n                                        borderColor: \"grey.300\",\n                                        p: 2,\n                                        borderRadius: 1,\n                                        backgroundColor: \"grey.50\"\n                                    },\n                                    dangerouslySetInnerHTML: {\n                                        __html: extractedData.content\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, undefined),\n                                extractedData.warnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    severity: \"warning\",\n                                    sx: {\n                                        mt: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Dialog_DialogTitle_IconButton_LinearProgress_Paper_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            children: [\n                                                t(\"createArticle:conversionWarnings\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: extractedData.warnings.map((w, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: w.message\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 300,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogActions, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                onClick: handleClosePreview,\n                                children: t(\"global:cancel\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                onClick: handleApplyContent,\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SuccessIcon, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 24\n                                }, void 0),\n                                children: t(\"createArticle:applyContent\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"7cFeiPgqFhGSzHeGTiXZN3jgdSs=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});