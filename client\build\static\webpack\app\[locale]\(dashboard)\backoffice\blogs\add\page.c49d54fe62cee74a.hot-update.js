"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/AddArticleFR.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @feelinglovelynow/slug */ \"(app-pages-browser)/./node_modules/@feelinglovelynow/slug/dist/index.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n/* harmony import */ var suneditor_dist_css_suneditor_min_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! suneditor/dist/css/suneditor.min.css */ \"(app-pages-browser)/./node_modules/suneditor/dist/css/suneditor.min.css\");\n/* harmony import */ var react_tag_input__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! react-tag-input */ \"(app-pages-browser)/./node_modules/react-tag-input/dist/index.js\");\n/* harmony import */ var _components_ui_CustomSunEditor__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/CustomSunEditor */ \"(app-pages-browser)/./src/components/ui/CustomSunEditor.jsx\");\n/* harmony import */ var _FaqSection__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./FaqSection */ \"(app-pages-browser)/./src/features/blog/components/FaqSection.jsx\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/DatePicker/DatePicker.js\");\n/* harmony import */ var _mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/x-date-pickers/internals/demo */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/internals/demo/DemoContainer.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/x-date-pickers/AdapterDayjs */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/AdapterDayjs/AdapterDayjs.js\");\n/* harmony import */ var _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/features/opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _DocumentImporter__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./DocumentImporter */ \"(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AddArticleFR(param) {\n    let { errors, touched, setFieldValue, values, onImageSelect, categories, filteredCategories, onCategoriesSelect, debounce } = param;\n    _s();\n    const handlePaste = (event, cleanData, maxCharCount)=>{\n        let html = cleanData;\n        // Correction des balises non fermées\n        html = html.replace(/<strong>(.*?)$/g, \"<strong>$1</strong>\");\n        return html;\n    };\n    const KeyCodes = {\n        comma: 188,\n        enter: 13\n    };\n    const delimiters = [\n        KeyCodes.comma,\n        KeyCodes.enter\n    ];\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [highlights, setHighlights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [publishNow, setPublishNow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const language = \"fr\";\n    const handlePhotoChange = async ()=>{\n        const selectedFile = imageInputRef.current.files[0];\n        setSelectedImage(imageInputRef.current.files[0]);\n        if (selectedFile) {\n            onImageSelect(selectedFile, language);\n        }\n    };\n    const useSaveFileHook = (0,_features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_12__.useSaveFile)();\n    const handlePhotoBlogChange = async (file, info, core, uploadHandler)=>{\n        if (file instanceof HTMLImageElement) {\n            const src = file.src;\n            if (src.startsWith(\"data:image\")) {\n                const base64Data = src.split(\",\")[1];\n                const contentType = src.match(/data:(.*?);base64/)[1];\n                const byteCharacters = atob(base64Data);\n                const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i)=>byteCharacters.charCodeAt(i));\n                const byteArray = new Uint8Array(byteNumbers);\n                const blob = new Blob([\n                    byteArray\n                ], {\n                    type: contentType\n                });\n                const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                const selectedFile = new File([\n                    blob\n                ], fileName, {\n                    type: contentType\n                });\n                await uploadFile(selectedFile, uploadHandler, core, file);\n            } else {\n                fetch(src).then((response)=>response.blob()).then((blob)=>{\n                    const contentType = blob.type;\n                    const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                    const selectedFile = new File([\n                        blob\n                    ], fileName, {\n                        type: contentType\n                    });\n                    uploadFile(selectedFile, uploadHandler, core, file);\n                }).catch((error)=>console.error(\"Error converting image URL to Blob:\", error));\n            }\n        } else {\n            console.error(\"File is not an HTMLImageElement.\");\n        }\n    };\n    const uploadFile = (selectedFile, uploadHandler, core, originalImage)=>{\n        let uuidPhoto;\n        uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n        const formData = new FormData();\n        formData.append(\"file\", selectedFile);\n        const extension = selectedFile.name.split(\".\").pop();\n        const currentYear = new Date().getFullYear();\n        useSaveFileHook.mutate({\n            resource: \"blogs\",\n            folder: currentYear.toString(),\n            filename: uuidPhoto,\n            body: {\n                formData,\n                t\n            }\n        }, {\n            onSuccess: (dataUUID)=>{\n                const uuidPhotoFileName = dataUUID.message === \"uuid exist\" ? dataUUID.uuid : `${uuidPhoto}.${extension}`;\n                const imageUrl = `${\"http://localhost:4000/api/v1\"}/files/${uuidPhotoFileName}`;\n                originalImage.src = imageUrl;\n                uploadHandler({\n                    result: [\n                        {\n                            id: uuidPhotoFileName,\n                            url: imageUrl\n                        }\n                    ]\n                });\n            },\n            onError: (error)=>{\n                console.error(\"Error uploading file:\", error);\n            }\n        });\n    };\n    const [titlefr, setTitlefr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedTitlefr = localStorage.getItem(\"titlefr\");\n        return savedTitlefr ? JSON.parse(savedTitlefr) : \"\";\n    });\n    const [metatitlefr, setMetatitlefr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetatitlefr = localStorage.getItem(\"metatitlefr\");\n        return savedMetatitlefr ? JSON.parse(savedMetatitlefr) : \"\";\n    });\n    const [metaDescriptionfr, setMetaDescriptionfr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetadescriptionfr = localStorage.getItem(\"metaDescriptionfr\");\n        return savedMetadescriptionfr ? JSON.parse(savedMetadescriptionfr) : \"\";\n    });\n    const [contentfr, setContentfr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedContentfr = localStorage.getItem(\"contentfr\");\n        return savedContentfr ? JSON.parse(savedContentfr) : \"\";\n    });\n    const handleEditorChange = (newContentfr)=>{\n        setContentfr(newContentfr);\n        setFieldValue(\"contentFR\", newContentfr);\n    };\n    const handleContentExtracted = (extractedContent)=>{\n        setContentfr(extractedContent);\n        setFieldValue(\"contentFR\", extractedContent);\n        localStorage.setItem(\"contentfr\", JSON.stringify(extractedContent));\n        debounce();\n    };\n    const handleMetadataExtracted = (metadata)=>{\n        if (metadata.title && !values.titleFR) {\n            setFieldValue(\"titleFR\", metadata.title);\n            setTitlefr(metadata.title);\n            const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_5__.slug)(metadata.title);\n            setFieldValue(\"urlFR\", url);\n        }\n        if (metadata.description && !values.descriptionFR) {\n            setFieldValue(\"descriptionFR\", metadata.description);\n        }\n        if (metadata.keywords && metadata.keywords.length > 0) {\n            const keywordTags = metadata.keywords.map((keyword, index)=>({\n                    id: `extracted-${index}`,\n                    text: keyword\n                }));\n            const existingKeywords = values.keywordsFR || [];\n            const mergedKeywords = [\n                ...existingKeywords,\n                ...keywordTags\n            ];\n            setFieldValue(\"keywordsFR\", mergedKeywords.map((tag)=>tag.text));\n        }\n        debounce();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (titlefr) {\n            localStorage.setItem(\"titlefr\", JSON.stringify(titlefr));\n        }\n        if (contentfr) {\n            localStorage.setItem(\"contentfr\", JSON.stringify(contentfr));\n        }\n        if (metatitlefr) {\n            localStorage.setItem(\"metatitlefr\", JSON.stringify(metatitlefr));\n        }\n        if (metaDescriptionfr) {\n            localStorage.setItem(\"metaDescriptionfr\", JSON.stringify(metaDescriptionfr));\n        }\n    }, [\n        titlefr,\n        contentfr,\n        metatitlefr,\n        metaDescriptionfr\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"label-pentabell\",\n                children: \"Add article French : \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:title\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"titleFR\",\n                                        type: \"text\",\n                                        value: titlefr || values.titleFR,\n                                        onChange: (e)=>{\n                                            const titleFR = e.target.value;\n                                            setFieldValue(\"titleFR\", titleFR);\n                                            setTitlefr(titleFR);\n                                            const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_5__.slug)(titleFR);\n                                            setFieldValue(\"urlFR\", url);\n                                        },\n                                        className: \"input-pentabell\" + (errors.titleFR && touched.titleFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"titleFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:categories\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                multiple: true,\n                                                className: \"input-pentabell\",\n                                                id: \"tags-standard\",\n                                                options: filteredCategories.length > 0 ? filteredCategories : categories,\n                                                getOptionLabel: (option)=>option.name,\n                                                selected: values.categoryFR.length > 0 ? (filteredCategories.length > 0 ? filteredCategories : categories).filter((category)=>values.categoryFR.includes(category.id)) : [],\n                                                onChange: (event, selectedOptions)=>{\n                                                    const categoryIds = selectedOptions.map((category)=>category.id);\n                                                    setFieldValue(\"categoryFR\", categoryIds);\n                                                    onCategoriesSelect(categoryIds);\n                                                },\n                                                renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        ...params,\n                                                        className: \"input-pentabell  multiple-select\",\n                                                        variant: \"standard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            touched.categoryFR && errors.categoryFR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"error\",\n                                children: errors.categoryFR\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Description\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"descriptionFR\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: values.descriptionFR,\n                                    onChange: (e)=>{\n                                        const descriptionFR = e.target.value;\n                                        setFieldValue(\"descriptionFR\", descriptionFR);\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.descriptionFR && touched.descriptionFR ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"descriptionFR\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Highlights\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"tags\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                        tags: highlights,\n                                        className: \"input-pentabell\" + (errors.highlightsFR && touched.highlightsFR ? \" is-invalid\" : \"\"),\n                                        delimiters: delimiters,\n                                        handleDelete: (i)=>{\n                                            const updatedTags = highlights.filter((tag, index)=>index !== i);\n                                            setHighlights(updatedTags);\n                                            setFieldValue(\"highlightsFR\", updatedTags.map((tag)=>tag.text));\n                                        },\n                                        handleAddition: (tag)=>{\n                                            setHighlights([\n                                                ...highlights,\n                                                tag\n                                            ]);\n                                            setFieldValue(\"highlightsFR\", [\n                                                ...highlights,\n                                                tag\n                                            ].map((item)=>item.text));\n                                        },\n                                        inputFieldPosition: \"bottom\",\n                                        autocomplete: true,\n                                        allowDragDrop: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"keywordsEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 352,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentImporter__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        onContentExtracted: handleContentExtracted,\n                        onMetadataExtracted: handleMetadataExtracted,\n                        language: \"FR\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 399,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomSunEditor__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        setContents: contentfr || values?.contentFR || \"\",\n                        onChange: handleEditorChange,\n                        onPaste: handlePaste,\n                        onImageUpload: handlePhotoBlogChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 404,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 398,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaqSection__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                values: values,\n                setFieldValue: setFieldValue,\n                errors: errors,\n                touched: touched,\n                language: \"FR\",\n                debounce: debounce\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 413,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 421,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:metaTitle\"),\n                                    \" (\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: values.metaTitleFR?.length > 65 ? \" text-danger\" : \"\",\n                                        children: [\n                                            \" \",\n                                            values.metaTitleFR?.length,\n                                            \" / 65\",\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    \")\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"metaTitleFR\",\n                                        type: \"text\",\n                                        value: metatitlefr || values.metaTitleFR,\n                                        onChange: (e)=>{\n                                            const metaTitleFR = e.target.value;\n                                            setFieldValue(\"metaTitleFR\", metaTitleFR);\n                                            setMetatitlefr(metaTitleFR);\n                                        },\n                                        className: \"input-pentabell\" + (errors.metaTitleFR && touched.metaTitleFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"metaTitleFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:url\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"urlFR\",\n                                        type: \"text\",\n                                        value: values.urlFR,\n                                        onChange: (e)=>{\n                                            setFieldValue(\"urlFR\", e.target.value);\n                                        },\n                                        className: \"input-pentabell\" + (errors.urlFR && touched.urlFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"urlFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:metaDescription\"),\n                                \" (\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: values.metaDescriptionFR?.length > 160 ? \" text-danger\" : \"\",\n                                    children: [\n                                        values.metaDescriptionFR?.length,\n                                        \" / 160\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                \")\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"metaDescriptionFR\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 2,\n                                    value: metaDescriptionfr || values.metaDescriptionFR,\n                                    onChange: (e)=>{\n                                        const metaDescriptionFR = e.target.value;\n                                        setFieldValue(\"metaDescriptionFR\", metaDescriptionFR);\n                                        setMetaDescriptionfr(metaDescriptionFR);\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.metaDescriptionFR && touched.metaDescriptionFR ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"metaDescriptionFR\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 490,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 489,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 488,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:featuredImage\"),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"upload-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: `image-upload-fr`,\n                                        className: \"file-labels\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                id: `image-upload-fr`,\n                                                name: \"imageEN\",\n                                                accept: \".png, .jpg, .jpeg, .webp\",\n                                                ref: imageInputRef,\n                                                onChange: (e)=>{\n                                                    setFieldValue(\"imageFR\", e.target.files[0]);\n                                                    handlePhotoChange();\n                                                },\n                                                className: \"file-input\" + (errors.imageFR && touched.imageFR ? \" is-invalid\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"upload-area\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"icon-pic\",\n                                                            style: {\n                                                                backgroundImage: `url(\"${selectedImage ? URL.createObjectURL(selectedImage) : values.imageFR ? `${process.env.REACT_APP_API_URL}${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.files}/${values.imageFR}` : _assets_images_add_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"].src}\")`,\n                                                                backgroundSize: \"cover\",\n                                                                backgroundRepeat: \"no-repeat\",\n                                                                backgroundPosition: \"center\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-text\",\n                                                                children: t(\"createArticle:addFeatImg\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-description\",\n                                                                children: t(\"createArticle:clickBox\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                                name: \"imageFR\",\n                                                component: \"div\",\n                                                className: \"invalid-feedback error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 531,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 530,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 529,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 528,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:alt\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"altFR\",\n                                        type: \"text\",\n                                        value: values.altFR,\n                                        onChange: (e)=>{\n                                            setFieldValue(\"altFR\", e.target.value);\n                                        },\n                                        className: \"input-pentabell\" + (errors.altFR && touched.altFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"altFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 593,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 592,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:visibility\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"select-pentabell\",\n                                        variant: \"standard\",\n                                        value: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.Visibility.filter((option)=>values.visibilityFR === option),\n                                        selected: values.visibilityFR,\n                                        onChange: (event)=>{\n                                            setFieldValue(\"visibilityFR\", event.target.value);\n                                        },\n                                        children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.Visibility.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                value: item,\n                                                children: item\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"visibilityEN\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 619,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 618,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 617,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:keyword\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        id: \"tags\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                            tags: tags,\n                                            className: \"input-pentabell\" + (errors.keywordsFR && touched.keywordsFR ? \" is-invalid\" : \"\"),\n                                            delimiters: delimiters,\n                                            handleDelete: (i)=>{\n                                                const updatedTags = tags.filter((tag, index)=>index !== i);\n                                                setTags(updatedTags);\n                                                setFieldValue(\"keywordsFR\", updatedTags.map((tag)=>tag.text));\n                                            },\n                                            handleAddition: (tag)=>{\n                                                setTags([\n                                                    ...tags,\n                                                    tag\n                                                ]);\n                                                setFieldValue(\"keywordsFR\", [\n                                                    ...tags,\n                                                    tag\n                                                ].map((item)=>item.text));\n                                            },\n                                            inputFieldPosition: \"bottom\",\n                                            autocomplete: true,\n                                            allowDragDrop: false\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"keywordsFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 649,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 648,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 647,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 591,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"label-form\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.Field, {\n                        type: \"checkbox\",\n                        name: \"publishNow\",\n                        checked: publishNow,\n                        onChange: (e)=>{\n                            setPublishNow(e.target.checked);\n                            if (e.target.checked) {\n                                setFieldValue(\"publishDateFR\", new Date().toISOString());\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 693,\n                        columnNumber: 9\n                    }, this),\n                    t(\"createArticle:publishNow\")\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 692,\n                columnNumber: 7\n            }, this),\n            !publishNow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"label-form\",\n                        children: [\n                            t(\"createArticle:publishDate\"),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_25__.LocalizationProvider, {\n                                dateAdapter: _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_26__.AdapterDayjs,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_27__.DemoContainer, {\n                                    components: [\n                                        \"DatePicker\"\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_28__.DatePicker, {\n                                            variant: \"standard\",\n                                            className: \"input-date\",\n                                            format: \"DD/MM/YYYY\",\n                                            value: dayjs__WEBPACK_IMPORTED_MODULE_11___default()(values.publishDateEN),\n                                            onChange: (date)=>{\n                                                setFieldValue(\"publishDateEN\", dayjs__WEBPACK_IMPORTED_MODULE_11___default()(date).format(\"YYYY-MM-DD\"));\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"publishDateEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 712,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 710,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 709,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 708,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_19__.Field, {\n                type: \"hidden\",\n                name: \"publishDateFR\",\n                value: publishNow ? new Date().toISOString() : publishDate.toISOString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 738,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AddArticleFR, \"+ONfVgda+KvaQf3X16NXIW62f9w=\", false, function() {\n    return [\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_12__.useSaveFile\n    ];\n});\n_c = AddArticleFR;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticleFR);\nvar _c;\n$RefreshReg$(_c, \"AddArticleFR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx\n"));

/***/ })

});