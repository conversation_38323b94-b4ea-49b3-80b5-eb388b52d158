"use client ";
import blogIMG from "../../../assets/images/website/blog-img.png";

import { Card, CardContent, CardMedia, Grid } from "@mui/material";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { websiteRoutesList } from "@/helpers/routesList";
import { htmlToText } from "html-to-text";

function BlogItem({ blogData, language, withoutCategory }) {
  // Import helper functions
  const {
    getBlogVersion,
    getBlogUrl,
    getBlogTitle,
    getBlogImage,
    getBlogDescription,
    getBlogContent,
  } = require("@/utils/blogHelpers");

  const handleClick = (event, href) => {
    event.preventDefault();
    window.location.href = href;
  };

  const truncateDescription = (title) => {
    title = htmlToText(title.replace(/<a[^>]*>|<\/a>/g, ""), {
      wordwrap: false,
    });
    const words = title.split(" ");
    if (words?.length >= 20) {
      return words.slice(0, 20).join(" ");
    } else {
      return title;
    }
  };

  const { t } = useTranslation();

  // Get version data for the current language
  const versionData = getBlogVersion(blogData, language);

  // If no version data is available, don't render the component
  if (!versionData) {
    return null;
  }

  return (
    <Grid
      className="blog-item"
      item
      xs={12}
      sm={6}
      md={4}
      key={versionData?.title}
      sx={{ marginBottom: "10px" }}
    >
      <Card className="card">
        {blogData?.category?.name && !withoutCategory && (
          <Link
            locale={language === "en" ? "en" : "fr"}
            href={`${
              language === "en"
                ? `/${websiteRoutesList.blog.route}/category/${blogData?.category?.url}`
                : `/${language}/${websiteRoutesList.blog.route}/category/${blogData?.category?.url}`
            }/`}
            onClick={(e) =>
              handleClick(
                e,
                `${
                  language === "en"
                    ? `/${websiteRoutesList.blog.route}/category/${blogData?.category?.url}`
                    : `/${language}/${websiteRoutesList.blog.route}/category/${blogData?.category?.url}`
                }/`
              )
            }
            className="label-category"
          >
            {blogData?.category?.name}
          </Link>
        )}
        <Link
          locale={language === "en" ? "en" : "fr"}
          href={`${
            language === "en"
              ? `/${websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}`
              : `/${language}/${websiteRoutesList.blog.route}/${blogData?.versions[0]?.url}`
          }/`}
          onClick={(e) =>
            handleClick(
              e,
              `${
                language === "en"
                  ? `/${websiteRoutesList.blog.route}/${versionData?.url}`
                  : `/${language}/${websiteRoutesList.blog.route}/${versionData?.url}`
              }/`
            )
          }
        >
          <CardMedia
            className="card-image"
            component="img"
            image={
              versionData?.image
                ? `${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${versionData?.image}`
                : blogIMG.src
            }
            alt={versionData?.title}
          />
        </Link>
        <CardContent
          className="card-content"
          sx={{ padding: "8px !important" }}
        >
          <Link
            locale={language === "en" ? "en" : "fr"}
            href={`${
              language === "en"
                ? `/${websiteRoutesList.blog.route}/${versionData?.url}`
                : `/${language}/${websiteRoutesList.blog.route}/${versionData?.url}`
            }/`}
            onClick={(e) =>
              handleClick(
                e,
                `${
                  language === "en"
                    ? `/${websiteRoutesList.blog.route}/${versionData?.url}`
                    : `/${language}/${websiteRoutesList.blog.route}/${versionData?.url}`
                }/`
              )
            }
          >
            <p className="blog-title">{versionData?.title}</p>{" "}
          </Link>
          <p className="blog-description">
            {versionData?.description
              ? versionData?.description
              : truncateDescription(versionData?.content)}
          </p>
        </CardContent>
      </Card>
    </Grid>
  );
}

export default BlogItem;
