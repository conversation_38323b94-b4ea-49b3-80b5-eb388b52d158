(()=>{var e={};e.id=5956,e.ids=[5956],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},91288:(e,s,t)=>{"use strict";t.d(s,{Z:()=>n});var r,i=t(95746);function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(null,arguments)}let n=e=>i.createElement("svg",a({xmlns:"http://www.w3.org/2000/svg",width:39,height:34,fill:"none"},e),r||(r=i.createElement("path",{fill:"#096",d:"M38.462 3.043c-3.363-.891-6.893-.171-9.712 1.935C25.597 1.809 21.291 0 16.826 0 7.551.002 0 7.552 0 16.83s7.55 16.828 16.828 16.828 16.824-7.55 16.824-16.828c0-2.647-.638-5.266-1.834-7.62 2.066-1.871 4.315-3.519 6.796-4.83a.72.72 0 0 0 .381-.722.73.73 0 0 0-.533-.615m-6.26 13.787c0 8.48-6.896 15.384-15.38 15.384-8.481 0-15.384-6.903-15.384-15.384S8.34 1.445 16.822 1.445c4.047 0 7.947 1.619 10.822 4.471-.025.02-.05.038-.069.063-.281.272-.565.56-.847.844a14.08 14.08 0 0 0-9.906-4.081c-7.77 0-14.087 6.318-14.087 14.087 0 7.771 6.318 14.087 14.087 14.087 7.766 0 14.084-6.318 14.084-14.087 0-1.96-.394-3.84-1.165-5.606.322-.331.643-.663.975-.984a15.3 15.3 0 0 1 1.487 6.59M8.376 15.636c3.32 2.69 4.944 4.43 7.26 7.756.54.78 1.39 1.224 2.32 1.224h.02a2.93 2.93 0 0 0 2.403-1.312c2.534-3.753 5.211-7.55 8.277-10.919.537 1.413.81 2.906.81 4.45 0 6.972-5.67 12.646-12.641 12.646-6.972 0-12.646-5.669-12.646-12.646 0-6.971 5.669-12.645 12.646-12.645 3.34 0 6.547 1.33 8.902 3.67-2.544 2.697-5.084 5.87-7.702 9.644-2.623-2.89-5.007-4.304-7.084-4.197-1.803.088-2.666 1.306-2.706 1.356a.73.73 0 0 0 .141.973m22.13-7.181a.6.6 0 0 0-.155.147 47 47 0 0 0-2.032 2.024c-3.432 3.622-6.366 7.77-9.13 11.865-.288.425-.732.672-1.22.678-.472.02-.877-.219-1.146-.604-2.213-3.183-3.894-5.034-6.874-7.512.262-.15.628-.293 1.106-.306 1.21-.044 3.329.687 6.493 4.397a.74.74 0 0 0 .59.253.73.73 0 0 0 .557-.312c3.384-4.963 6.619-8.907 9.89-12.066 2.097-2.022 4.816-3.04 7.562-2.89-2.037 1.25-3.9 2.717-5.64 4.326"})))},66060:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d}),t(71738),t(30962),t(23658),t(54864);var r=t(23191),i=t(88716),a=t(37922),n=t.n(a),l=t(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["[locale]",{children:["(website)",{children:["guides",{children:["[url]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,71738)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\guides\\[url]\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30962)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\layout.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,23658)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54864)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\not-found.js"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\guides\\[url]\\page.jsx"],u="/[locale]/(website)/guides/[url]/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/[locale]/(website)/guides/[url]/page",pathname:"/[locale]/guides/[url]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},95510:(e,s,t)=>{Promise.resolve().then(t.bind(t,90423)),Promise.resolve().then(t.bind(t,9782)),Promise.resolve().then(t.bind(t,26887)),Promise.resolve().then(t.bind(t,94705)),Promise.resolve().then(t.bind(t,18054)),Promise.resolve().then(t.bind(t,40162)),Promise.resolve().then(t.bind(t,87893)),Promise.resolve().then(t.bind(t,82639)),Promise.resolve().then(t.bind(t,86682)),Promise.resolve().then(t.bind(t,85127)),Promise.resolve().then(t.bind(t,2851)),Promise.resolve().then(t.bind(t,80918)),Promise.resolve().then(t.bind(t,79189)),Promise.resolve().then(t.bind(t,10827)),Promise.resolve().then(t.bind(t,55854)),Promise.resolve().then(t.bind(t,96314)),Promise.resolve().then(t.bind(t,84791)),Promise.resolve().then(t.bind(t,16190)),Promise.resolve().then(t.bind(t,26668)),Promise.resolve().then(t.bind(t,70719)),Promise.resolve().then(t.bind(t,22522)),Promise.resolve().then(t.bind(t,72150)),Promise.resolve().then(t.bind(t,66871)),Promise.resolve().then(t.bind(t,85260)),Promise.resolve().then(t.bind(t,20964)),Promise.resolve().then(t.bind(t,95487)),Promise.resolve().then(t.bind(t,59505)),Promise.resolve().then(t.bind(t,5728)),Promise.resolve().then(t.bind(t,95053)),Promise.resolve().then(t.bind(t,63572)),Promise.resolve().then(t.bind(t,42021)),Promise.resolve().then(t.bind(t,58536)),Promise.resolve().then(t.bind(t,94599)),Promise.resolve().then(t.bind(t,32923)),Promise.resolve().then(t.bind(t,81957)),Promise.resolve().then(t.bind(t,18990)),Promise.resolve().then(t.bind(t,40166)),Promise.resolve().then(t.bind(t,6690)),Promise.resolve().then(t.bind(t,21447)),Promise.resolve().then(t.bind(t,65449)),Promise.resolve().then(t.bind(t,44790)),Promise.resolve().then(t.bind(t,63123)),Promise.resolve().then(t.bind(t,73209)),Promise.resolve().then(t.bind(t,51607))},65368:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a}),t(17577);var r=t(27522),i=t(10326);let a=(0,r.Z)((0,i.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},38932:function(e,s,t){(function(e){"use strict";var s=/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?|janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,t=[/^janv/i,/^févr/i,/^mars/i,/^avr/i,/^mai/i,/^juin/i,/^juil/i,/^août/i,/^sept/i,/^oct/i,/^nov/i,/^déc/i];e.defineLocale("fr",{months:"janvier_f\xe9vrier_mars_avril_mai_juin_juillet_ao\xfbt_septembre_octobre_novembre_d\xe9cembre".split("_"),monthsShort:"janv._f\xe9vr._mars_avr._mai_juin_juil._ao\xfbt_sept._oct._nov._d\xe9c.".split("_"),monthsRegex:s,monthsShortRegex:s,monthsStrictRegex:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,monthsShortStrictRegex:/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?)/i,monthsParse:t,longMonthsParse:t,shortMonthsParse:t,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui \xe0] LT",nextDay:"[Demain \xe0] LT",nextWeek:"dddd [\xe0] LT",lastDay:"[Hier \xe0] LT",lastWeek:"dddd [dernier \xe0] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",w:"une semaine",ww:"%d semaines",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|)/,ordinal:function(e,s){switch(s){case"D":return e+(1===e?"er":"");default:case"M":case"Q":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}},week:{dow:1,doy:4}})})(t(57967))},57993:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});var r=t(10326);function i({htmlContent:e}){return r.jsx("div",{dangerouslySetInnerHTML:{__html:e}})}},73209:(e,s,t)=>{"use strict";t.d(s,{default:()=>Z});var r,i,a,n=t(10326),l=t(17577),o=t(57967),d=t.n(o),c=t(23743),u=t(88441),m=t(90423),h=t(16027);t(38932);var p=t(52210),g=t(22304),x=t(57993),v=t(87638),b=t(78077),f=t(5394),j=t(76971),w=t(10123),y=t(63568),_=t(15082),P=t(95746);function N(){return(N=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(null,arguments)}let M=e=>P.createElement("svg",N({xmlns:"http://www.w3.org/2000/svg",width:35,height:39,fill:"none",viewBox:"0 0 41 39"},e),r||(r=P.createElement("path",{fill:"#234791",d:"M24.09 24.036c0-.715-.599-1.263-1.283-1.263H9.159c-.728 0-1.284.59-1.284 1.263 0 .716.599 1.263 1.284 1.263h13.648c.684 0 1.283-.547 1.283-1.263M9.159 18.862h13.648c.727 0 1.283-.59 1.283-1.263 0-.716-.599-1.263-1.283-1.263H9.159c-.728 0-1.284.59-1.284 1.263s.599 1.263 1.284 1.263M7.875 30.52c0 .715.599 1.263 1.284 1.263h11.123l.6-2.568H9.158c-.728 0-1.284.59-1.284 1.305"})),i||(i=P.createElement("path",{fill:"#234791",d:"M29.009 34.896c0 .421-.342.8-.813.8H3.724a.806.806 0 0 1-.813-.8V3.326c0-.421.342-.8.813-.8H19.04V9.05c0 1.852 1.498 3.326 3.38 3.326h6.589v7.661l2.61-2.568v-5.514c0-.884-.385-1.726-1.027-2.357L21.864.968A3.4 3.4 0 0 0 19.468 0H3.724C1.84 0 .344 1.515.344 3.325V34.94c0 1.81 1.497 3.325 3.38 3.325h24.472c1.882 0 3.423-1.516 3.423-3.326v-4.251l-2.61 2.568zM21.65 4.336l5.562 5.472H22.42c-.47 0-.813-.337-.813-.8V4.336z"})),a||(a=P.createElement("path",{fill:"#234791",d:"m23.324 33.172 3.295-.715c.128-.042.256-.127.385-.21l.941-.927-3.637-3.578-.94.926c-.13.127-.215.253-.215.38l-.77 3.24c-.128.506.385 1.01.941.884M25.668 26.393l3.637 3.578 8.428-8.292-3.637-3.62zM40.774 17.594l-2.567-2.525a.76.76 0 0 0-1.07 0l-1.625 1.6 3.636 3.577 1.626-1.557c.3-.295.3-.8 0-1.095"})));t(4563);var q=t(70580);t(50967);var C=t(31190),k=t(91288),$=t(18970),L=t(26066);function D({user:e,id:s,fileName:t,Name:r,cible:i,refetch:a}){let[o,d]=(0,l.useState)(""),[c,u]=(0,l.useState)(!1),[m,g]=(0,l.useState)(!1),{t:x,i18n:P}=(0,p.$G)();P.language;let N=async t=>{let{acceptTerms:r,...i}=t;if(!s){console.error("Guide ID is not defined");return}if(!r){d("You must accept the terms and conditions."),C.Am.error("Please accept the terms and conditions before submitting.");return}try{window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"Download_guide_How_to_choose_a_global_payroll_provider_trigger",button_id:"download_guide_button"});let r={firstName:t.firstName,lastName:t.lastName,companysize:t.companysize,Headquarters:t.Headquarters,email:t.email};e?._id&&(r.user=e._id);let i=await q.yX.post(`/guides/download/${s}`,r);200===i.status?(u(!0),d(""),a()):(console.error("Error submitting form:",i.statusText),d("Error: "+i.statusText))}catch(e){console.error("Error submitting form:",e),d(e.message)}},D=e=>w.Ry().shape({firstName:w.Z_().required(x("validations:emptyField")),lastName:w.Z_().required(x("validations:emptyField")),email:w.Z_().email(x("validations:invalidEmail")).required(x("validations:required")).test("is-company-email",x("validations:companyEmailRequired"),s=>!e||L.isCompanyEmail(s))});return(0,n.jsxs)(n.Fragment,{children:[n.jsx($.Z,{}),c?n.jsx("div",{className:"section-guide",children:(0,n.jsxs)("div",{className:"form-success",children:[n.jsx(k.Z,{}),n.jsx("p",{className:"heading-h2",children:" Thank you! "}),(0,n.jsxs)("p",{className:"sub-heading",children:[" ","Your guide is on its way. Please check your inbox."]})]})}):(0,n.jsxs)(n.Fragment,{children:[" ",(0,n.jsxs)("div",{className:"section-guide",children:[(0,n.jsxs)(h.default,{marginBottom:"20px",container:!0,gap:1,spacing:2,marginLeft:"0px",children:[n.jsx("div",{className:"icon",children:n.jsx(M,{})}),n.jsx("p",{className:"title",children:"Download the guide for free"})]}),n.jsx(y.J9,{initialValues:{firstName:"",lastName:"",companysize:"",Headquarters:"",email:"",acceptTerms:!1},validationSchema:()=>D(m),onSubmit:N,children:({errors:e,touched:s,values:t,handleChange:r})=>(0,n.jsxs)(y.l0,{children:[(0,n.jsxs)(h.default,{container:!0,rowSpacing:1,columnSpacing:{xs:1,sm:2,md:3},children:[(0,n.jsxs)(h.default,{item:!0,xs:6,children:[n.jsx(v.Z,{className:"form-group",children:n.jsx(b.Z,{placeholder:"First Name",name:"firstName",value:t.firstName,onChange:r,error:!!(e.firstName&&s.firstName),fullWidth:!0,sx:{backgroundColor:"white",borderRadius:2,"& .MuiOutlinedInput-root":{"&:hover fieldset":{borderColor:"#ccc"},"&.Mui-focused fieldset":{borderColor:"#1976d2"}}}})}),n.jsx(y.Bc,{name:"firstName",children:e=>n.jsx("span",{className:"error-span",children:e})})]}),(0,n.jsxs)(h.default,{item:!0,xs:6,children:[n.jsx(v.Z,{className:"form-group",children:n.jsx(b.Z,{fullWidth:!0,sx:{backgroundColor:"white",borderRadius:2,"& .MuiOutlinedInput-root":{"&:hover fieldset":{borderColor:"#ccc"},"&.Mui-focused fieldset":{borderColor:"#1976d2"}}},placeholder:"Last Name",name:"lastName",value:t.lastName,onChange:r,error:!!(e.lastName&&s.lastName)})}),n.jsx(y.Bc,{name:"lastName",children:e=>n.jsx("span",{className:"error-span",children:e})})]}),(0,n.jsxs)(h.default,{item:!0,xs:12,children:[n.jsx(v.Z,{className:"form-group",children:n.jsx(b.Z,{fullWidth:!0,sx:{backgroundColor:"white",borderRadius:2,"& .MuiOutlinedInput-root":{"&:hover fieldset":{borderColor:"#ccc"},"&.Mui-focused fieldset":{borderColor:"#1976d2"}}},placeholder:"Work Email",name:"email",value:t.email,onChange:r,error:!!(e.email&&s.email)})}),n.jsx(y.Bc,{name:"email",children:e=>n.jsx("span",{className:"error-span",children:e})})]}),"client"===i&&(0,n.jsxs)(h.default,{item:!0,xs:6,children:[n.jsx(v.Z,{className:"form-group",children:n.jsx(b.Z,{fullWidth:!0,sx:{backgroundColor:"white",borderRadius:2,"& .MuiOutlinedInput-root":{"&:hover fieldset":{borderColor:"#ccc"},"&.Mui-focused fieldset":{borderColor:"#1976d2"}}},placeholder:"Company Size",name:"companysize",value:t.companysize,onChange:r,error:!!(e.companysize&&s.companysize)})}),n.jsx(y.Bc,{name:"companysize",children:e=>n.jsx("span",{className:"error-span",children:e})})]}),"client"===i&&n.jsx(h.default,{item:!0,xs:6,children:n.jsx(v.Z,{className:"form-group",children:n.jsx(b.Z,{placeholder:"Headquarters",fullWidth:!0,sx:{backgroundColor:"white",borderRadius:2,"& .MuiOutlinedInput-root":{"&:hover fieldset":{borderColor:"#ccc"},"&.Mui-focused fieldset":{borderColor:"#1976d2"}}},name:"Headquarters",value:t.Headquarters,onChange:r,error:!!(e.Headquarters&&s.Headquarters)})})})]}),n.jsx(h.default,{paddingTop:"20px",children:n.jsx(f.Z,{className:"checkbox-pentabell blue",control:n.jsx(j.Z,{name:"acceptTerms",checked:t.acceptTerms,onChange:r}),label:x("I’d also like to receive Pentabell’s monthly updates on industry insights and resources.")})}),n.jsx("p",{className:"description-guide",children:"We value your privacy. By submitting this form, you agree to be contacted about Pentabvell’s services, as outlined in our Privacy Policy."}),n.jsx(_.default,{text:"Download Now",className:"btn btn-filled",type:"submit"})]})})]})]})]})}let Z=function({id:e,guide:s,language:t,fileName:r,Name:i,cible:a}){let o=(0,c.Z)(),{user:v}=(0,g.Z)(),{i18n:b}=(0,p.$G)(),f=(0,u.Z)(o.breakpoints.down("sm"));d().locale(b.language||"en");let[j,w]=(0,l.useState)(s?.content);return(0,n.jsxs)("div",{id:"blog-page-details",children:[n.jsx("div",{id:"guide-header",children:n.jsx(m.default,{className:"custom-max-width",children:n.jsx(h.default,{container:!0,spacing:3,children:n.jsx(h.default,{item:!0,xs:12,sm:6,children:(s?.categoryguides?.length>0||s?.categories?.length>0)&&n.jsx("div",{className:"categories-list",children:[...s.categoryguides||[],...s.categories||[]].map(e=>{let r=s.categoryguides?.some(s=>s.id===e.id)?`${"en"===t?`/blog/category/${e.url}`:`/${t}/blog/category/${e.url}`}/`:`/${t}/blog/category/${e.url}`;return e?.url&&n.jsx("span",{className:"category light",children:n.jsx("a",{href:r,children:e.name})},e.url)})})})})})}),(0,n.jsxs)(m.default,{className:"custom-max-width",children:[(0,n.jsxs)(h.default,{className:"container",container:!0,columnSpacing:2,children:[n.jsx(h.default,{item:!0,xs:12,sm:6,children:n.jsx("div",{className:"blog-content",children:n.jsx(x.Z,{htmlContent:j})})}),n.jsx(h.default,{item:!0,xs:12,sm:5,id:"sticky-sidebar-guide",children:f?null:n.jsx(D,{user:v,fileName:r,cible:a,Name:i,id:e})})]}),n.jsx(h.default,{className:"container",container:!0,columnSpacing:2,children:n.jsx(h.default,{item:!0,xs:12,sm:8,children:f?n.jsx(D,{user:v,fileName:r,cible:a,Name:i,id:e}):null})})]})]})}},71738:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u,generateMetadata:()=>c});var r=t(19510),i=t(44957),a=t(71615);let n=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\features\guides\components\GuidePageDetails.jsx#default`);var l=t(94034),o=t(43207),d=t(58585);async function c({params:e}){let s=(0,a.headers)().get("cookie"),t=e.locale,r=e?.url,n=`https://www.pentabell.com/${"en"!==t?`${t}/`:""}guides/${r}/`;try{let a=await i.xk.get(`/guides/${t}/${r}`,{headers:{Cookie:s}}),l=a?.data?.versionsguide[0],o={};try{let e=(await i.xk.get(`/guides/opposite/${t}/${r}`)).data.slug;"fr"===t&&(o.fr=`https://www.pentabell.com/fr/guides/${r}/`,e&&(o.en=`https://www.pentabell.com/guides/${e}/`,o["x-default"]=`https://www.pentabell.com/guides/${e}/`),n=`https://www.pentabell.com/fr/guides/${r}/`),"en"===t&&(e&&(o.fr=`https://www.pentabell.com/fr/guides/${e}/`),o.en=`https://www.pentabell.com/guides/${r}/`,o["x-default"]=`https://www.pentabell.com/guides/${r}/`,n=`https://www.pentabell.com/guides/${r}/`)}catch(e){console.log("error",e.response.data)}if(l)return{title:l.metaTitle,description:l.metaDescription,robots:a?.data?.robotsMeta==="index"?"follow, index, max-snippet:-1, max-image-preview:large":"noindex",alternates:{canonical:n,languages:o},openGraph:{title:l?.title,description:l?.content?.replace(/<\/?[^>]+(>|$)/g," ")?.replace(/&[a-z]+;/gi,"")?.replace(/\s\s+/g," ")?.slice(0,500),url:"en"===e.locale?`https://www.pentabell.com/guides/${l?.url}/`:`https://www.pentabell.com/guides/${e.locale}/${l?.url}/`,images:[{url:l?.image?`https://www.pentabell.com/api/v1/files/${l?.image}`:null,alt:l?.alt}]}}}catch(e){return{title:"Error",description:"An error occurred while fetching the guide"}}return{title:"Guide",description:"Guide page with guides and categories"}}async function u({params:e}){let s=e.locale,t=e?.url,{t:c}=await (0,o.Z)(e.locale,["eventDetails","global","guides"]),u=(0,a.headers)().get("cookie");try{let e=await i.xk.get(`/guides/${s}/${t}`,{headers:{Cookie:u}}),a=e?.data,o=a?.versionsguide[0],d=a?._id,c=a?.cible,m=a?.versionsguide[0]?.file,h=a?.versionsguide[0]?.image,p=a?.versionsguide[0]?.title,g=a?.versionsguide[0]?.guideList;return(0,r.jsxs)(r.Fragment,{children:[r.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"ItemList",name:g?.itemTitle,description:g?.itemDescription,url:t,itemListOrder:"Ascending",numberOfItems:g?.subtitles?.length||0,itemListElement:g?.subtitles?.map((e,s)=>({"@type":"ListItem",position:s+1,name:e.title,description:e.description}))})}}),r.jsx(l.Z,{bannerImgDynamic:`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${h}`,isEvent:!0,titleHighlight:o.title,highlights:o.highlights?o.highlights:null,category:o.categories.name,height:"70vh",altImg:o.alt}),r.jsx(n,{id:d,guide:o,cible:c,fileName:m,Name:p,language:s,url:t})]})}catch(s){(0,d.redirect)("en"===e.locale?"/guides/":`/${e.locale}/guides/`)}}},94034:(e,s,t)=>{"use strict";t.d(s,{Z:()=>n});var r=t(19510),i=t(55920),a=t(70064);let n=function({bannerImg:e,height:s,title:t,IconImg:n,description:l,subtitle:o,bottomChildren:d,opportunitysubTitle:c,centerValue:u,altImg:m,topChildren:h,isEvent:p,title2:g,titleHighlight:x,bannerImgDynamic:v,link:b,linkTitle:f,highlights:j}){return(0,r.jsxs)("div",{id:"banner-component",className:!0===u?"center-banner":"",style:{backgroundImage:e?.src?`url(${e.src})`:v?`url(${v})`:"none",height:s||"auto"},children:[m&&r.jsx("img",{width:0,height:0,alt:m,src:"",style:{display:"none"},loading:"lazy"}),(0,r.jsxs)(i.Z,{className:"custom-max-width",children:[h&&h,n&&r.jsx("img",{src:n.src}),p?r.jsx(r.Fragment,{children:j&&j.length>0?(0,r.jsxs)("h1",{className:"heading-h1 text-white",children:[" ",(0,a.q1)(x,j)," "]}):(0,r.jsxs)(r.Fragment,{children:[" ",(0,r.jsxs)("h1",{className:"heading-h1 text-white",children:[(0,r.jsxs)("span",{className:"text-yellow",children:[t," "]}),x,g]}),r.jsx("p",{className:"sub-heading text-slide text-white  ",children:o})]})}):(0,r.jsxs)(r.Fragment,{children:[" ",r.jsx("h1",{className:"heading-h1 text-white",children:t||"Services We Offer"}),r.jsx("p",{className:"sub-heading text-slide text-white  ",children:o}),r.jsx("p",{className:"sub-heading text-slide text-white  ",children:c}),r.jsx("p",{className:"sub-heading text-slide text-white  ",children:l||null}),b&&r.jsx("a",{href:b,style:{textDecoration:"none",width:"fit-content"},className:"btn btn-filled ",children:f})]}),d&&d]})]})}}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,1479,1619,1336,4227,8077,6027,3718,9645,4289,1692,9712,8582,9984,1812,3969,4903,8031,3195],()=>t(66060));module.exports=r})();