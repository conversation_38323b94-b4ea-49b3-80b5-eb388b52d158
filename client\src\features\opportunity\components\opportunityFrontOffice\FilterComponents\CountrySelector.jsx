import { Autocomplete, TextField, InputAdornment } from "@mui/material";
import SvgMapPin from "@/assets/images/icons/MapPin.svg";

const CountrySelector = ({ value, options, onChange }) => (
  <div className="filter-options">
    <Autocomplete
      className="input-pentabell maps"
      id="tags-standard"
      options={options}
      getOptionLabel={(option) => option}
      value={
        Array.isArray(value) ? (value.length > 0 ? value[0] : "") : value || ""
      }
      onChange={onChange}
      renderInput={(params) => (
        <TextField
          {...params}
          className="input-pentabell multiple-select"
          variant="standard"
          placeholder="Country"
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <>
                <InputAdornment position="start">
                  <SvgMapPin />
                </InputAdornment>
                {params.InputProps.startAdornment}
              </>
            ),
          }}
        />
      )}
    />
  </div>
);

export default CountrySelector;
