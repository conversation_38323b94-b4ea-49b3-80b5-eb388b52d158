import { Container, Grid } from "@mui/material";

import CustomButton from "../ui/CustomButton";
import { websiteRoutesList } from "@/helpers/routesList";
function CTMContact({ t }) {
  return (
    <div id="cta-contact">
      <Container className="custom-max-width">
        <Grid container className="container" spacing={2}>
          <Grid item xs={12} sm={6} className="left-section">
            <p className="heading-h2 text-white">
              {t("middleeast:contact:title")}
            </p>
            <p className="sub-heading text-yellow">
              {t("middleeast:contact:label")}
            </p>
            <p className="paragraph text-white">
              {t("middleeast:contact:description")}
            </p>
            <CustomButton
              text={t("middleeast:contact:contactUs")}
              className={"btn btn-filled"}
              link={`/${websiteRoutesList.contact.route}`}
            />
          </Grid>

          <Grid item xs={12} sm={5} className="africa-img"></Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default CTMContact;
