import * as nodemailer from 'nodemailer';
import * as expressHandlebars from 'express-handlebars';
import nodemailerExpressHandlebars from 'nodemailer-express-handlebars';
import Handlebars from 'handlebars';
import { allowInsecurePrototypeAccess } from '@handlebars/allow-prototype-access';
import * as path from 'path';

export const sendEmail = (data: any) => {
    const transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: Number(process.env.SMTP_PORT),
        secure: true,
        auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASSWORD,
        },
    });

    const handlebars = expressHandlebars.create({
        handlebars: allowInsecurePrototypeAccess(Handlebars),
        extname: '.handlebars',
        defaultLayout: 'main',
        layoutsDir: path.join(__dirname, 'views/layouts'),
        partialsDir: path.join(__dirname, 'views/partials'),
        helpers: {
            formatKey: function (key: any) {
                if (key.toLowerCase().includes('team')) {
                    return '';
                }
                return key.replace(/([A-Z])/g, ' $1').replace(/^./, (str: any) => str.toUpperCase());
            },
            eq: function (a: any, b: any) {
                return a === b;
            },
            or: function (a: any, b: any) {
                return a ?? b;
            },
            and: function (a: any, b: any) {
                return a && b;
            },
        },
    });

    transporter.use(
        'compile',
        nodemailerExpressHandlebars({
            viewEngine: handlebars,
            viewPath: path.join(__dirname, 'views/partials'),
        }),
    );

    // const defaultAttachments = [
    //     {
    //         filename: 'energy.png',
    //         path: `${__dirname}/../../../public/api/v1/images/energy.png`,
    //         cid: 'energy',
    //     },
    //     {
    //         filename: 'transport.png',
    //         path: `${__dirname}/../../../public/api/v1/images/transport.png`,
    //         cid: 'transport',
    //     },
    //     {
    //         filename: 'itandtelecom.png',
    //         path: `${__dirname}/../../../public/api/v1/images/itandtelecom.png`,
    //         cid: 'itandtelecom',
    //     },
    //     {
    //         filename: 'oilandgas.png',
    //         path: `${__dirname}/../../../public/api/v1/images/oilandgas.png`,
    //         cid: 'oilandgas',
    //     },
    //     {
    //         filename: 'banking.png',
    //         path: `${__dirname}/../../../public/api/v1/images/banking.png`,
    //         cid: 'banking',
    //     },
    //     {
    //         filename: 'x.png',
    //         path: `${__dirname}/../../../public/api/v1/images/x.png`,
    //         cid: 'x',
    //     },
    //     {
    //         filename: 'linkedin.png',
    //         path: `${__dirname}/../../../public/api/v1/images/linkedin.png`,
    //         cid: 'linkedin',
    //     },
    //     {
    //         filename: 'youtube.png',
    //         path: `${__dirname}/../../../public/api/v1/images/youtube.png`,
    //         cid: 'youtube',
    //     },
    //     {
    //         filename: 'instagram.png',
    //         path: `${__dirname}/../../../public/api/v1/images/instagram.png`,
    //         cid: 'instagram',
    //     },
    //     {
    //         filename: 'facebook.png',
    //         path: `${__dirname}/../../../public/api/v1/images/facebook.png`,
    //         cid: 'facebook',
    //     },
    //     {
    //         filename: 'industriesList.png',
    //         path: `${__dirname}/../../../public/api/v1/images/industriesList.png`,
    //         cid: 'industriesList',
    //     },
    // ];

    const { to, cc, subject, template, context, attachments } = data;

    let mailOptions: any = {
        from: '"Pentabell" <<EMAIL>>',
        to: to,
        subject: subject,
        template: template,
        cc: cc,
        context: context,
    };

    if (attachments) mailOptions = { ...mailOptions, attachments };

    handlebars
        .render(`${handlebars.partialsDir}/${template}.handlebars`, mailOptions.context)
        .then(() => {
            transporter.sendMail(mailOptions, (error, info) => {
                if (error) {
                    console.error(error);
                    return;
                }
            });
        })
        .catch(error => {
            console.error('Error during rendering:', error);
        });
};
