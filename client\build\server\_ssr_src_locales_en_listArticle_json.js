"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_en_listArticle_json";
exports.ids = ["_ssr_src_locales_en_listArticle_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/en/listArticle.json":
/*!*****************************************!*\
  !*** ./src/locales/en/listArticle.json ***!
  \*****************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"comments":"Comments","listOfArticles":"Blogs list","author":"Author","title":"Title","createdBy":"Created by","createdAt":"Created at","language":"Language","Archived":"Archived","visibility":"Visibility","archived":"Archived","nbOfComments":"Number of comments","dateOfCreaction":"Date of creation","category":"Category ","actions":"Actions","dateOfCreation":"Date of creation","url":"Url","archivage":"Archiving","metaTitleArticle":"Top Insights & Expert Tips on ","metaTitleArticle2":"| Pentabell Blog","metaDescriptionArticle":"news and insights, brought to you by Pentabell. Your source for valuable analysis to empower your decision-making in the","metaDescriptionArticle2":"landscape."}');

/***/ })

};
;