"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const cache_middleware_1 = require("@/middlewares/cache.middleware");
const validation_middleware_1 = require("@/middlewares/validation.middleware");
const glossaries_validations_1 = require("./glossaries.validations");
const glossaries_service_1 = __importDefault(require("./glossaries.service"));
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const upload = (0, multer_1.default)();
class GlossaryController {
    constructor() {
        this.path = '/glossaries';
        this.glossaryService = new glossaries_service_1.default();
        this.router = (0, express_1.Router)();
        this.importJsonGlossaries = async (request, response, next) => {
            try {
                const currentUser = request.user;
                const file = request.file;
                const createdEntries = await this.glossaryService.importJsonGlossaries(file, currentUser);
                response.status(201).send(createdEntries);
            }
            catch (error) {
                next(error);
            }
        };
        this.createGlossary = async (request, response, next) => {
            try {
                const glossaryData = request.body;
                const currentUser = request.user;
                if (glossaryData.en || glossaryData.fr) {
                    const versions = {};
                    if (glossaryData.en) {
                        versions['en'] = {
                            ...glossaryData.en,
                            language: constants_1.Language.ENGLISH,
                        };
                        delete glossaryData.en;
                    }
                    if (glossaryData.fr) {
                        versions['fr'] = {
                            ...glossaryData.fr,
                            language: constants_1.Language.FRENCH,
                        };
                        delete glossaryData.fr;
                    }
                    glossaryData.versions = versions;
                }
                const newGlossary = await this.glossaryService.createGlossary(glossaryData, currentUser);
                response.status(201).send(newGlossary);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllGlossaries = async (request, response, next) => {
            const queries = request.query;
            const language = request.query.language || constants_1.Language.ENGLISH;
            const currentUser = request.user;
            try {
                const glossary = await this.glossaryService.getGlossaries(queries, language, currentUser);
                response.status(200).send(glossary);
            }
            catch (error) {
                next(error);
            }
        };
        this.getSlugBySlug = async (request, response, next) => {
            try {
                const { url, language } = request.params;
                const glossaries = await this.glossaryService.getSlugBySlug(language, url);
                response.status(200).send(glossaries);
            }
            catch (error) {
                next(error);
            }
        };
        this.getGlossaryByLanguageAndId = async (request, response, next) => {
            try {
                const { id, language } = request.params;
                const glossary = await this.glossaryService.getGlossaryByLanguageAndId(language, id);
                if (!glossary) {
                    if (language === 'fr') {
                        response.status(204).send();
                        return;
                    }
                    else {
                        response.status(404).json({ message: 'Glossary not found' });
                        return;
                    }
                }
                response.status(200).json(glossary);
            }
            catch (error) {
                next(error);
            }
        };
        this.getGlossaryByUrl = async (request, response, next) => {
            try {
                const { url, language } = request.params;
                const currentUser = request.user;
                const glossaries = await this.glossaryService.getGlossaryByUrl(language, url, currentUser);
                response.send(glossaries);
            }
            catch (error) {
                next(error);
            }
        };
        this.getGlossaryById = async (request, response, next) => {
            try {
                const { id } = request.params;
                const glossaries = await this.glossaryService.get(id);
                response.send(glossaries);
            }
            catch (error) {
                next(error);
            }
        };
        this.updateGlossaryVersionByLanguageAndId = async (request, response, next) => {
            try {
                const { glossaryId } = request.params;
                const currentUser = request.user;
                const updateData = request.body;
                if (!glossaryId) {
                    response.status(400).send('Missing glossary ID.');
                    return;
                }
                let updatedGlossary = await this.glossaryService.updateGlossaryByLanguageAndId(glossaryId, updateData, currentUser);
                response.send(updatedGlossary);
            }
            catch (error) {
                next(error);
            }
        };
        this.deleteByLanguageAndId = async (request, response, next) => {
            try {
                const { language, glossaryId } = request.params;
                const hardDelete = request.query.hardDelete === 'true';
                if (!language || !glossaryId) {
                    throw new http_exception_1.default(400, 'Missing language or glossaryId parameter.');
                }
                let result;
                let actionMessage;
                if (hardDelete) {
                    result = await this.glossaryService.deleteGlossary(language, glossaryId);
                    actionMessage = `deleted`;
                }
                else {
                    result = await this.glossaryService.archiveOrRestoreGlossaryByLanguageAndId(language, glossaryId);
                    if (result) {
                        actionMessage = result.isArchived ? 'archived' : 'restored';
                    }
                }
                if (!result) {
                    throw new http_exception_1.default(404, `No glossary or version found for language ${language} with glossaryId ${glossaryId}.`);
                }
                response.send({
                    message: `Version with language ${language} ${actionMessage} successfully from glossary with ID ${glossaryId}.`,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, cache_middleware_1.validateCache, this.getAllGlossaries);
        this.router.get(`${this.path}/dashboard/:id`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.getGlossaryById);
        this.router.get(`${this.path}/opposite/:language/:url`, validateApiKey_middleware_1.default, this.getSlugBySlug);
        this.router.get(`${this.path}/dashboard/:language/:id`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.getGlossaryByLanguageAndId);
        this.router.get(`${this.path}/:language/:url`, validateApiKey_middleware_1.default, this.getGlossaryByUrl);
        this.router.post(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, (0, validation_middleware_1.validationMiddleware)(glossaries_validations_1.glossarySchema), this.createGlossary);
        this.router.post(`${this.path}/import-json`, validateApiKey_middleware_1.default, authentication_middleware_1.default, upload.single('file'), (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.importJsonGlossaries);
        this.router.put(`${this.path}/:glossaryId`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.updateGlossaryVersionByLanguageAndId);
        this.router.delete(`${this.path}/:language/:glossaryId`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.deleteByLanguageAndId);
    }
}
exports.default = GlossaryController;
//# sourceMappingURL=glossaries.controller.js.map