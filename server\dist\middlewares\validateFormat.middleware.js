"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateFormatMiddleware = validateFormatMiddleware;
exports.checkUserPermission = checkUserPermission;
const candidat_model_1 = __importDefault(require("@/apis/candidat/candidat.model"));
const files_model_1 = __importDefault(require("@/apis/storage/files.model"));
const user_model_1 = __importDefault(require("@/apis/user/user.model"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
const User = user_model_1.default;
const File = files_model_1.default;
const Candidate = candidat_model_1.default;
function validateFormatMiddleware(request, response, next) {
    const getMethod = request.method === 'GET';
    const { filename } = request.params;
    const mediaExtension = getMethod ? filename.split('.').pop() : '';
    const uuid = getMethod ? filename.split('.').shift() : '';
    const allowedTypes = ['png', 'jpg', 'jpeg', 'pdf', 'jfif', 'docx', 'webp'];
    if (getMethod && !allowedTypes.includes(mediaExtension)) {
        throw new http_exception_1.default(400, messages_1.MESSAGES.FILE.INVALID_FORMAT);
    }
    if (getMethod) {
        request.params.uuid = uuid;
        request.params.mediaExtension = mediaExtension;
    }
    next();
}
async function checkUserPermission(request, response, next) {
    const file = await File.findOne({ fileName: request.params.filename });
    if (!file)
        return next(new http_exception_1.default(404, messages_1.MESSAGES.FILE.NOT_FOUND));
    if (file.resource !== 'candidates')
        return next();
    if (!request.user)
        return next(new http_exception_1.default(403, messages_1.MESSAGES.AUTH.FORBIDDEN));
    const user = await User.findById(request.user._id);
    if (!user)
        return next(new http_exception_1.default(401, messages_1.MESSAGES.AUTH.UNAUTHORIZED));
    if ((user.roles.includes(constants_1.Role.ADMIN)) || (user.roles.includes(constants_1.Role.EDITEUR)))
        return next();
    const candidate = await Candidate.findById(user.candidate);
    if (!candidate)
        return next(new http_exception_1.default(401, messages_1.MESSAGES.AUTH.UNAUTHORIZED));
    if (candidate.cv.some((c) => c.fileName === request.params.filename))
        return next();
    else
        return next(new http_exception_1.default(403, messages_1.MESSAGES.AUTH.FORBIDDEN));
}
//# sourceMappingURL=validateFormat.middleware.js.map