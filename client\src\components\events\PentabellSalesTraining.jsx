"use client";
import { Container, Grid, useTheme } from "@mui/material";
import InfoSection from "./InfoSection";
import ProcessHtml from "./ProcessHtml";
import EventsCard from "./EventsCard";
import image1 from "@/assets/images/PentabellSalesTraining/image1.png";
import image2 from "@/assets/images/PentabellSalesTraining/image2.png";
import image3 from "@/assets/images/PentabellSalesTraining/image3.png";
import image4 from "@/assets/images/PentabellSalesTraining/image4.png";
import image5 from "@/assets/images/PentabellSalesTraining/image5.png";
import image6 from "@/assets/images/PentabellSalesTraining/image6.png";
import image7 from "@/assets/images/PentabellSalesTraining/image7.png";
import image8 from "@/assets/images/PentabellSalesTraining/image8.png";
import image9 from "@/assets/images/PentabellSalesTraining/image9.png";
import image10 from "@/assets/images/PentabellSalesTraining/image10.png";
import image11 from "@/assets/images/PentabellSalesTraining/image11.png";
import SvgQUote from "@/assets/images/icons/quote.svg";
import SvgQUoteRight from "@/assets/images/icons/quoteRight.svg";
import libya from "../../assets/images/events/libya.png";
import moroccoevent from "../../assets/images/events/moroccoevent.png";
import eventafricaforum from "../../assets/images/events/eventafricaforum.png";
import SvgQuoteMobile from "@/assets/images/icons/quotemobile.svg";
import SvgQuoteMobileRight from "@/assets/images/icons/quotemobileRight.svg";
import imagemobile1 from "@/assets/images/PentabellSalesTraining/imagemobile1.png";
import imagemobile2 from "@/assets/images/PentabellSalesTraining/imagemobile2.png";
import imagemobile3 from "@/assets/images/PentabellSalesTraining/imagemobile3.png";
import imagemobile4 from "@/assets/images/PentabellSalesTraining/imagemobile4.png";
import imagemobile5 from "@/assets/images/PentabellSalesTraining/imagemobile5.png";
import imagemobile6 from "@/assets/images/PentabellSalesTraining/imagemobile6.png";
import imagemobile7 from "@/assets/images/PentabellSalesTraining/imagemobile7.png";
import imagemobile8 from "@/assets/images/PentabellSalesTraining/imagemobile8.png";
import imagemobile9 from "@/assets/images/PentabellSalesTraining/imagemobile9.png";
import imagemobile10 from "@/assets/images/PentabellSalesTraining/imagemobile10.png";
import imagemobile11 from "@/assets/images/PentabellSalesTraining/imagemobile11.png";
import EmblaCarousel from "@/components/embla_slider/EmblaCarousel";
import { API_URLS } from "@/utils/urls";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { axiosGetJson } from "@/config/axios";
import { getEventsDashboard } from "@/features/events/services/event.services";
export default function PentabellSalesTraining({ language, event, isMobileSSR }) {
    const { t } = useTranslation();
    const [events, setEvents] = useState();
    const theme = useTheme();

   const fetchEvents = async () => {
        try {
            const response = await axiosGetJson.get(API_URLS.events, {
                params: {
                    visibility: "Public",
                    pageNumber: 1,
                    pageSize: 4,
                    language: language,
                },
            });
            let { Events } = response.data;

            const eventsFiltered = Events?.filter(
                (currentEvent) => currentEvent._id !== event?._id
            ).slice(0, 3);
            setEvents(eventsFiltered);
        } catch (error) {
            console.error("Failed to fetch events:", error);
        }
    };

    useEffect(() => {
        fetchEvents();
    }, [event, language]); 

    const OPTIONS = {};
    const SLIDES = [
        image1,
        image2,
        image3,
        image4,
        image5,
        image6,
        image7,
        image8,
        image9,
        image10,
        image11,


    ];
    const SlidesMobile = [
        imagemobile1,
        imagemobile2,
        imagemobile3,
        imagemobile4,
        imagemobile5,
        imagemobile6,
        imagemobile7,
        imagemobile8,
        imagemobile9,
        imagemobile10,
        imagemobile11,
    ]
    const decarbonizationSection = {
        sector: t("PentabellSalestraining:sectorValue"),
        country: t("PentabellSalestraining:locationValue"),
        countryConcerned: t("PentabellSalestraining:countryConcernedValue"),
        organiser: t("PentabellSalestraining:organiserValue"),
    };
    let infoSection;
    if (event) {
        infoSection = {
            sector: event?.versions[0]?.sector,
            country: t(`country:${event?.country?.replace(/\s+/g, "")}`),
            countryConcerned: event?.versions[0]?.countryConcerned,
            organiser: event?.versions[0]?.organiser,
        };
    }
    const eventData = [
        {
            title: t("event:events.AfricaforumFrance.title"),
            eventDate: t("event:events.AfricaforumFrance.eventDate"),
            postingDate: t("event:events.AfricaforumFrance.postingDate"),
            exactPlace: t("event:events.AfricaforumFrance.exactPlace"),
            link: "Africa-France-forum-on-ecological-and-energy-transition-2025",
            type: "events",
            image: eventafricaforum,
        },
        {
            title: t("event:events.eventGitex.title"),
            eventDate: t("event:events.eventGitex.eventDate"),
            postingDate: t("event:events.eventGitex.postingDate"),
            exactPlace: t("event:events.eventGitex.exactPlace"),
            type: "events",
            link: `Gitex-africa-morocco-2025`,
            image: moroccoevent,
        },
        {
            title: t("event:events.eventLibya.title"),
            eventDate: t("event:events.eventLibya.eventDate"),
            postingDate: t("event:events.eventLibya.postingDate"),
            exactPlace: t("event:events.eventLibya.exactPlace"),
            link:
                language === "en"
                    ? `pentabell-salon-sme-and-european-microwave-week`
                    : "pentabell-salon-sme-and-european-microwave-week",
            link: "Libyan-French-economic-forum-2025",
            type: "events",
            image: libya,
        },
    ];
    return (
        <div id="event-page">
            {event ? (
                <div id="event-detail">
                    <div className="custom-max-width">
                        <InfoSection t={t} infos={infoSection} />{" "}
                        <ProcessHtml htmlString={event?.versions[0]?.content} />
                        <Grid
                            className="more-events-section"
                            container
                            rowSpacing={0}
                            columnSpacing={3}
                        >
                            {" "}
                            {events?.length > 0 &&
                                events?.map((event, index) => (
                                    <EventsCard
                                        key={index}
                                        eventData={event}
                                        language={language}
                                        isEvent={true}
                                    />
                                ))}{" "}
                        </Grid>
                    </div>{" "}
                </div>
            ) : (
                <div id="event-detail">
                    <Container className="custom-max-width">
                        <InfoSection t={t} infos={decarbonizationSection} />
                        <div className="details">
                            <p className="text">
                                {t("PentabellSalestraining:Description:description1")}
                            </p>
                            <p className="heading-h1">
                                {t("PentabellSalestraining:eventProgram:eventProgram")}
                            </p>
                            <ul>
                                <li className="text">{t("PentabellSalestraining:eventProgram:puce1")}</li>
                                <li className="text">
                                    {" "}
                                    {t("PentabellSalestraining:eventProgram:puce2")}
                                </li>
                                <li className="text">{t("PentabellSalestraining:eventProgram:puce3")}</li>
                                <li className="text">{t("PentabellSalestraining:eventProgram:puce4")}</li>
                            </ul>




                            <p className="heading-h1">
                                {t("PentabellSalestraining:eventProgram2:eventProgram2")}
                            </p>
                            <p className="text">{t("PentabellSalestraining:eventProgram2:data1")}</p>

                            <ul>
                                <li className="text">{t("PentabellSalestraining:eventProgram2:puce1")}</li>
                                <li className="text">
                                    {" "}
                                    {t("PentabellSalestraining:eventProgram2:puce2")}
                                </li>
                                <li className="text">{t("PentabellSalestraining:eventProgram2:puce3")}</li>
                                <li className="text">{t("PentabellSalestraining:eventProgram2:puce4")}</li>
                                <li className="text">{t("PentabellSalestraining:eventProgram2:puce5")}</li>
                            </ul>
                            <div class="quote-box">

                                <div class="quote-icon-top">
                                    {isMobileSSR ? <SvgQuoteMobile /> : <SvgQUote />}
                                </div>

                                <div class="quote-border top-border"></div>


                                <div class="quote-content">
                                    <p>
                                        {t("PentabellSalestraining:quote:description")}
                                    </p>
                                    <div class="quote-author">
                                        — Wisssem Zarrouk, <span>{t("PentabellSalestraining:quote:authorrole")}</span>
                                    </div>
                                </div>


                                <div class="quote-border bottom-border"></div>


                                <div class="quote-icon-bottom">
                                    {isMobileSSR ? <SvgQuoteMobileRight /> : <SvgQUoteRight />}

                                </div>
                            </div>


                            <p className="heading-h1">
                                {t("PentabellSalestraining:Description2:title")}
                            </p>
                            <p className="text">
                                {t("PentabellSalestraining:Description2:description2")}
                            </p>
                            <p className="text">
                                {t("PentabellSalestraining:Description2:description3")}
                            </p>

                            <EmblaCarousel slides={SLIDES} options={OPTIONS} slidesMobile={SlidesMobile} />
                            <p className="heading-h1">{t("PentabellSalestraining:moreEvents")}</p>
                        </div>
                        <Grid
                            className="more-events-section"
                            container
                            rowSpacing={0}
                            columnSpacing={3}
                        >
                            {eventData?.map((event, index) => (
                                <EventsCard
                                    key={index}
                                    eventData={event}
                                    language={language}
                                    isEvent={true}
                                />
                            ))}
                        </Grid>
                    </Container>
                </div>
            )}
        </div>
    );
}
