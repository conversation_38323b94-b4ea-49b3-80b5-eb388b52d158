"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/features/stats/charts/PlateformActivities.jsx":
/*!***********************************************************!*\
  !*** ./src/features/stats/charts/PlateformActivities.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PlateformActivities; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,Grid,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon.svg\");\n/* harmony import */ var _components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/charts/CustomMultiBarchart */ \"(app-pages-browser)/./src/components/charts/CustomMultiBarchart.jsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction PlateformActivities(param) {\n    let { platformAactivity, chartSettings1 } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    const [dateFromPlatform, setDateFromPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"2024-09-01\");\n    const [dateToPlatform, setDateToPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchPlatform, setSearchPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const resetSearchPlatform = ()=>{\n        setDateToPlatform(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromPlatform(\"2024-09-01\");\n        setSearchPlatform(!searchPlatform);\n    };\n    const getDataPlatforActivity = useGetPlatformStat({\n        dateFrom: dateFromPlatform,\n        dateTo: dateToPlatform\n    });\n    const pieChart = {\n        title: t(\"statsDash:articlesByVisibility\"),\n        dataset: getDataPieArticles?.data?.map((article)=>({\n                label: article.visibility,\n                value: article.totalArticles\n            })),\n        colors: [\n            \"#234791\",\n            \"#FFCA00\",\n            \"#006A67\"\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        getDataPlatforActivity.refetch();\n    }, [\n        searchPlatform\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        className: \"card\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"barchartfilter-wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        container: true,\n                        className: \"chart-grid\",\n                        spacing: 1,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 12,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"heading-h3\",\n                                    gutterBottom: true,\n                                    children: platformAactivity.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2.5,\n                                xl: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    label: t(\"statsDash:fromDate\"),\n                                    type: \"date\",\n                                    value: dateFromPlatform,\n                                    onChange: (e)=>setDateFromPlatform(e.target.value),\n                                    fullWidth: true,\n                                    InputLabelProps: {\n                                        shrink: true\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2.5,\n                                xl: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    label: t(\"statsDash:toDate\"),\n                                    type: \"date\",\n                                    value: dateToPlatform,\n                                    onChange: (e)=>setDateToPlatform(e.target.value),\n                                    fullWidth: true,\n                                    InputLabelProps: {\n                                        shrink: true\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                item: true,\n                                xs: 2,\n                                sm: 1,\n                                md: 1.5,\n                                xl: 1,\n                                className: \"btns-filter dashboard\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 23\n                                    }, void 0),\n                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                    onClick: resetSearchPlatform\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_Grid_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                item: true,\n                                xs: 10,\n                                sm: 11,\n                                md: 2.5,\n                                xl: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    text: t(\"statsDash:filter\"),\n                                    onClick: ()=>{\n                                        setSearchPlatform(!searchPlatform);\n                                    },\n                                    className: \"btn btn-outlined btn-filter-stat full-width\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"chart-wrapper\",\n                    children: [\n                        platformAactivity.dataset?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"labelstats-wrapper\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"newopportunities-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:newOpportunities\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"neswarticles-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:newArticles\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"newsletters-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:newslettersSubscriptions\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"label-wrapper\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"newcontact-dot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"label-chart\",\n                                            children: t(\"statsDash:newContacts\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            chart: platformAactivity,\n                            chartSettings: chartSettings1\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\PlateformActivities.jsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(PlateformActivities, \"Gy4mgj/lA0rrpG3TVXfwugC89gI=\", true, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation\n    ];\n});\n_c = PlateformActivities;\nvar _c;\n$RefreshReg$(_c, \"PlateformActivities\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/stats/charts/PlateformActivities.jsx\n"));

/***/ })

});