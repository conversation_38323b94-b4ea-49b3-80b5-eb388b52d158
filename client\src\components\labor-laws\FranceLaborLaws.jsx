"use client";
import {
  Container,
  Grid,
  AccordionSummary,
  AccordionDetails,
  Accordion,
} from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import SvgexpandIcon from "../../assets/images/icons/arrowUp.svg";

import SvgCallendar from "@/assets/images/icons/yellow/yellowCalendar.svg";

export default function FranceLaborLaws() {
  const [expanded, setExpanded] = useState(1);
  const { t } = useTranslation();
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  return (
    <div id="labor-tn-laws">
      <Container className="custom-max-width">
        <h2 className="heading-h1">{t("france:franceLabor:title")}</h2>

        <Grid className="container" container columnSpacing={3} rowSpacing={2}>
          <Grid item xs={12} sm={12} key={1}>
            <Accordion
              elevation={0}
              expanded={expanded === 1}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(1)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("france:franceLabor:workingHours:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                <p className="service-sub-title">
                    {t("france:franceLabor:workingHours:subTitle1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("france:franceLabor:workingHours:description")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("france:franceLabor:workingHours:subTitle2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("france:franceLabor:workingHours:description2")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>
                      {t("france:franceLabor:workingHours:data1")}
                    </li>
                    <li>
                      {t("france:franceLabor:workingHours:data2")}
                    </li>
                    <li>
                      {t("france:franceLabor:workingHours:data3")}
                    </li>
                  </ul>
                    <p className="service-description paragraph">
                    {t("france:franceLabor:workingHours:description3")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>
                      {t("france:franceLabor:workingHours:data4")}
                    </li>
                    <li>
                      {t("france:franceLabor:workingHours:data5")}
                    </li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={2}>
            <Accordion
              elevation={0}
              expanded={expanded === 2}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(2)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("france:franceLabor:employmentContracts:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("france:franceLabor:employmentContracts:title1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>
                      {t("france:franceLabor:employmentContracts:data1")}
                    </li>
                    <li>
                      {t("france:franceLabor:employmentContracts:data2")}
                    </li>
                    <li>
                      {t("france:franceLabor:employmentContracts:data3")}
                    </li>
                  </ul>
                </div>
              
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={4}>
            <Accordion
              elevation={0}
              expanded={expanded === 4}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(4)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("france:franceLabor:payroll:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("france:franceLabor:payroll:title1")}
                  </p>
                
                </div>
                <div className="payroll-tn">
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("france:franceLabor:payroll:fiscalYear:title")}
                    </p>
                    <p className="date">
                      {t("france:franceLabor:payroll:fiscalYear:date1")}
                      <br />
                      {t("france:franceLabor:payroll:fiscalYear:date2")}
                    </p>
                    <p className="paragraph">
                      {t("france:franceLabor:payroll:fiscalYear:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("france:franceLabor:payroll:payrollCycle:title")}
                    </p>
                    <p className="date">
                      {t("france:franceLabor:payroll:payrollCycle:date")}
                    </p>
                    <p className="paragraph">
                      {t(
                        "france:franceLabor:payroll:payrollCycle:description"
                      )}
                    </p>
                  </div>

                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("france:franceLabor:payroll:minimumWage:title")}
                    </p>
                    <p className="date">
                      {t("france:franceLabor:payroll:minimumWage:wage")}
                      <br />
                      {t("france:franceLabor:payroll:minimumWage:date")}​
                    </p>
                    <p className="paragraph">
                      {t(
                        "france:franceLabor:payroll:minimumWage:description"
                      )}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t(
                        "france:franceLabor:payroll:payrollManagement:title"
                      )}
                    </p>
                    <p className="date">
                      {t(
                        "france:franceLabor:payroll:payrollManagement:date1"
                      )}
                    </p>
                    <p className="paragraph">
                      {t(
                        "france:franceLabor:payroll:payrollManagement:description"
                      )}
                    </p>
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={3}>
            <Accordion
              elevation={0}
              expanded={expanded === 3}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(3)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("france:franceLabor:termination:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("france:franceLabor:termination:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("france:franceLabor:termination:description1")}
                  </p>
                 
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("france:franceLabor:termination:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("france:franceLabor:termination:description2")}
                  </p> 
                  <ul className="service-description paragraph">
                    <li>{t("france:franceLabor:termination:data1")}</li>
                    <li>{t("france:franceLabor:termination:data2")}</li>
                    <li>{t("france:franceLabor:termination:data3")}</li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("france:franceLabor:termination:title3")}
                  </p>
                
                  <ul className="service-description paragraph">
                    <li>{t("france:franceLabor:termination:dataNoticePeriod1")}</li>
                    <li>{t("france:franceLabor:termination:dataNoticePeriod2")}</li>
                    <li>{t("france:franceLabor:termination:dataNoticePeriod3")}</li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("france:franceLabor:termination:title4")}
                  </p>
                  <p className="service-description paragraph">
                    {t("france:franceLabor:termination:description4")}{" "}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={5}>
            <Accordion
              elevation={0}
              expanded={expanded === 5}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(5)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("france:franceLabor:leaveEntitlements:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                <p className="service-description paragraph">
                    {t("france:franceLabor:leaveEntitlements:description")}
                  </p>
                  <br/>
                  <p className="service-sub-title">
                    {t("france:franceLabor:leaveEntitlements:subTitle")}
                  </p>
                  <p className="service-description paragraph">
                    {t("france:franceLabor:leaveEntitlements:subDescription")}
                  </p>
                  <div className="holidays-dates">
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS1:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS1:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS2:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS2:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS3:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS3:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS4:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS4:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS5:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS5:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS6:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS6:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS7:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "france:franceLabor:leaveEntitlements:leaves:dataS7:title"
                        )}
                      </p>
                    </div>
                   
                  </div>
                </div>
             
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "france:franceLabor:leaveEntitlements:leaves:maternityLeave:title"
                    )}
                  </p>
                    <p className="service-description paragraph">
                      {t(
                        "france:franceLabor:leaveEntitlements:leaves:maternityLeave:description1"
                      )}
                    </p>
                   
                </div>
              

                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "france:franceLabor:leaveEntitlements:leaves:sickLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "france:franceLabor:leaveEntitlements:leaves:sickLeave:description"
                    )}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={6}>
            <Accordion
              elevation={0}
              expanded={expanded === 6}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(6)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("france:franceLabor:tax:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("france:franceLabor:tax:description")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("france:franceLabor:tax:title1")}
                  </p>

                  <p className="service-description paragraph">
                    {t("france:franceLabor:tax:description1")}
                  </p>

                  <ul className="service-description paragraph">
                    <li> {t("france:franceLabor:tax:data1")}</li>
                    <li> {t("france:franceLabor:tax:data2")}</li>
                    <li> {t("france:franceLabor:tax:data3")}</li>
                    <li> {t("france:franceLabor:tax:data4")}</li>

                  </ul>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {" "}
                    {t("france:franceLabor:tax:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("france:franceLabor:tax:description2")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("france:franceLabor:tax:dataS1")}</li>
                    <li>{t("france:franceLabor:tax:dataS2")}</li>
                    <li>{t("france:franceLabor:tax:dataS3")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>

          <Grid item xs={12} sm={12} key={7}>
            <Accordion
              elevation={0}
              expanded={expanded === 7}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(7)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("france:franceLabor:visa:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("france:franceLabor:visa:description1")}
                    </p>
                    <ul className="service-description paragraph">
                    <li>{t("france:franceLabor:visa:ul1")}</li>
                    <li>{t("france:franceLabor:visa:ul2")}</li>
                    </ul>
                    
                  <p className="service-description paragraph">
                    {t("france:franceLabor:visa:description2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("france:franceLabor:visa:description22")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("france:franceLabor:visa:title1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("france:franceLabor:visa:dataVisa1")}</li>
                    <li>{t("france:franceLabor:visa:dataVisa2")}</li>
                    <li>{t("france:franceLabor:visa:dataVisa3")}</li>
                    <li>{t("france:franceLabor:visa:dataVisa4")}</li>
                    <li>{t("france:franceLabor:visa:dataVisa5")}</li>
                    <li>{t("france:franceLabor:visa:dataVisa6")}</li>
                  </ul>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t("france:franceLabor:visa:title2")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("france:franceLabor:visa:data1")}</li>
                    <li>{t("france:franceLabor:visa:data2")}</li>
                    <li>{t("france:franceLabor:visa:data3")}</li>
                    <li>{t("france:franceLabor:visa:data4")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}
