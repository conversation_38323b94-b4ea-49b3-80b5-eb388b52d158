"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/AddArticleFroala.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _AddArticleEN__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AddArticleEN */ \"(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\");\n/* harmony import */ var _AddArticleFR__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AddArticleFR */ \"(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx\");\n/* harmony import */ var _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_15__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AddArticle = ()=>{\n    _s();\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const savedArticle = localStorage.getItem(\"savedArticle\");\n    const useUpdateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave)();\n    const useCreateArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle)();\n    const useCreateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave)();\n    const useSaveFileHook = (0,_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentYear = new Date().getFullYear();\n    const formikRefAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(`panel`);\n    const [formdataEN, setFormDataEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [formdataFR, setFormDataFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedLanguages, setSelectedLanguages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        en: true,\n        fr: false\n    });\n    const [categoriesEN, setCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesFR, setCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesEN, setFilteredCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesFR, setFilteredCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesEN, setSelectedCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesFR, setSelectedCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const getCategoriesEN = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"en\");\n    const getCategoriesFR = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"fr\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesEN.data?.categories) {\n            const transformedCategories = getCategoriesEN.data.categories.map((category)=>({\n                    id: category.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesEN(transformedCategories);\n        }\n    }, [\n        getCategoriesEN.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesFR.data?.categories) {\n            const transformedCategories = getCategoriesFR.data.categories.map((category)=>({\n                    id: category?.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesFR(transformedCategories);\n        }\n    }, [\n        getCategoriesFR.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedCategoriesEN.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesEN, \"en\");\n        } else if (selectedCategoriesFR.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesFR, \"fr\");\n        }\n    }, [\n        selectedCategoriesEN,\n        selectedCategoriesFR\n    ]);\n    const fetchTranslatedCategories = async (selectedCategories, language)=>{\n        try {\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_12__.axiosGetJson.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.categories}/${language}/${selectedCategories}`);\n            if (language === \"en\") {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesFR(transformedCategories);\n            } else {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesEN(transformedCategories);\n            }\n        } catch (error) {\n            console.error(\"Error fetching translated categories:\", error);\n        }\n    };\n    const handleImageSelect = async (selectedFile, language)=>{\n        if (language === \"en\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataEN(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n        } else if (language === \"fr\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataFR(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n        }\n    };\n    // Helper function to safely parse localStorage items\n    const getLocalStorageItem = (key)=>{\n        try {\n            const item = localStorage.getItem(key);\n            return item ? JSON.parse(item) : \"\";\n        } catch (error) {\n            console.warn(`Error parsing localStorage item \"${key}\":`, error);\n            return \"\";\n        }\n    };\n    const titleEN = getLocalStorageItem(\"title\");\n    const metaTitleEN = getLocalStorageItem(\"metatitle\");\n    const metaDescriptionEN = getLocalStorageItem(\"metaDescription\");\n    const contentEN = getLocalStorageItem(\"content\");\n    const contentFR = getLocalStorageItem(\"contentfr\");\n    const titleFR = getLocalStorageItem(\"titlefr\");\n    const metaDescriptionFR = getLocalStorageItem(\"metaDescriptionfr\");\n    const metaTitleFR = getLocalStorageItem(\"metatitlefr\");\n    const initialValues = {\n        robotsMeta: \"index\",\n        metaTitleEN: metaTitleEN,\n        metaDescriptionEN: metaDescriptionEN,\n        descriptionEN: \"\",\n        visibilityEN: \"\",\n        categoryEN: [],\n        imageEN: null,\n        keywordsEN: \"\",\n        titleEN: titleEN,\n        urlEN: \"\",\n        altEN: \"\",\n        contentEN: contentEN,\n        highlightsEN: [],\n        publishDateEN: \"\",\n        faqTitleEN: \"\",\n        faqEN: [],\n        titleFR: titleFR,\n        metaTitleFR: metaTitleFR,\n        metaDescriptionFR: metaDescriptionFR,\n        descriptionFR: \"\",\n        visibilityFR: \"\",\n        categoryFR: [],\n        imageFR: null,\n        keywordsFR: \"\",\n        urlFR: \"\",\n        altFR: \"\",\n        contentFR: contentFR,\n        publishDateFR: \"\",\n        highlightsFR: [],\n        faqTitleFR: \"\",\n        faqFR: []\n    };\n    const validationSchema = yup__WEBPACK_IMPORTED_MODULE_3__.object().shape({\n        ...selectedLanguages.en && {\n            metaTitleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            category: yup__WEBPACK_IMPORTED_MODULE_3__.array().min(1, t(\"validations:minCategory\")),\n            visibilityEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        },\n        ...selectedLanguages.fr && {\n            metaTitleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            visibilityFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        }\n    });\n    const uploadFile = (filename, formData, lang)=>{\n        return new Promise((resolve)=>{\n            useSaveFileHook.mutate({\n                resource: \"blogs\",\n                folder: currentYear,\n                filename,\n                body: {\n                    formData,\n                    t\n                }\n            }, {\n                onSuccess: (data)=>{\n                    if (data.message === \"uuid exist\") {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    } else {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    }\n                },\n                onError: (error)=>{\n                    console.error(`Error uploading ${lang} image:`, error);\n                    resolve({\n                        lang,\n                        uuid: null\n                    });\n                }\n            });\n        });\n    };\n    const clearLocalStorage = ()=>{\n        const keysToRemove = [\n            \"title\",\n            \"content\",\n            \"titlefr\",\n            \"contentfr\",\n            \"metaDescription\",\n            \"metaDescriptionfr\",\n            \"metatitle\",\n            \"metatitlefr\",\n            \"savedArticle\"\n        ];\n        keysToRemove.forEach((key)=>{\n            try {\n                localStorage.removeItem(key);\n            } catch (error) {\n                console.warn(`Error removing localStorage item \"${key}\":`, error);\n            }\n        });\n    };\n    const handleSubmit = async (values)=>{\n        const data = {\n            robotsMeta: values.robotsMeta,\n            versions: []\n        };\n        if (!selectedLanguages.en && !selectedLanguages.fr) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select at least one version!\");\n            return;\n        }\n        try {\n            let resultEN, resultFR;\n            // Validate required files\n            if (selectedLanguages.en && (!uuidPhotoFileNameEN || !formdataEN)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image for the English version!\");\n                return;\n            }\n            if (selectedLanguages.fr && (!uuidPhotoFileNameFR || !formdataFR)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image for the French version!\");\n                return;\n            }\n            if (selectedLanguages.en) {\n                resultEN = await uploadFile(uuidPhotoFileNameEN, formdataEN, \"English\");\n            }\n            if (selectedLanguages.fr) {\n                resultFR = await uploadFile(uuidPhotoFileNameFR, formdataFR, \"French\");\n            }\n            if (selectedLanguages.en && resultEN.uuid) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    description: values.descriptionEN,\n                    url: values.urlEN,\n                    visibility: values.visibilityEN,\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    highlights: values.highlightsEN,\n                    image: resultEN.uuid,\n                    category: values.categoryEN === \"\" ? [] : values.categoryEN,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr && resultFR.uuid) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: values.visibilityFR,\n                    description: values.descriptionFR,\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    highlights: values.highlightsFR,\n                    image: resultFR.uuid,\n                    category: values.categoryFR === \"\" ? [] : values.categoryFR,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (data.versions.length > 0) {\n                if (isSavedArticle !== \"\") {\n                    useUpdateAutoSaveHook.mutate({\n                        data: data,\n                        id: isSavedArticle\n                    }, {\n                        onSuccess: ()=>{\n                            clearLocalStorage();\n                        //window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        }\n                    });\n                } else {\n                    useCreateArticleHook.mutate({\n                        data\n                    }, {\n                        onSuccess: ()=>{\n                            clearLocalStorage();\n                        // window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        }\n                    });\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No valid image uploads found. Article not created.\");\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"An error occurred while processing uploads.\");\n        }\n    };\n    const handleChangeAccordion = (panel)=>(_, newExpanded)=>{\n            setExpanded(newExpanded ? panel : false);\n        };\n    const handleCategoriesENSelect = (selectedCategories)=>{\n        setSelectedCategoriesEN(selectedCategories);\n    };\n    const handleCategoriesFRSelect = (selectedCategories)=>{\n        setSelectedCategoriesFR(selectedCategories);\n    };\n    const [isSavedArticle, setIsSavedArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedArticle ? savedArticle : \"\");\n    const isSavedArticleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isSavedArticle);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        isSavedArticleRef.current = isSavedArticle;\n    }, [\n        isSavedArticle\n    ]);\n    const autosave = async ()=>{\n        try {\n            const values = formikRefAll.current?.values;\n            if (!values) return;\n            const data = {\n                robotsMeta: values?.robotsMeta,\n                versions: []\n            };\n            if (selectedLanguages.en) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    url: values.urlEN,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    category: values.categoryEN,\n                    highlights: values.highlightsEN,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    category: values.categoryFR,\n                    highlights: values.highlightsFR,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (isSavedArticleRef.current != \"\") {\n                useUpdateAutoSaveHook.mutate({\n                    data: data,\n                    id: isSavedArticleRef.current\n                });\n            } else {\n                if (data.versions.length > 0) {\n                    useCreateAutoSaveHook.mutate({\n                        data\n                    }, {\n                        onSuccess: (data)=>{\n                            setIsSavedArticle(data.articleId);\n                            localStorage.setItem(\"savedArticle\", data.articleId);\n                        }\n                    });\n                }\n            }\n        } catch (error) {\n            console.warn(\"Auto-save failed:\", error);\n        }\n    };\n    const handleChange = lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default()(autosave, 30000); // 30 seconds for better UX\n    const handleClear = ()=>{\n        formikRefAll.current?.resetForm();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"createArticle:addArticle\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 470,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"commun\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.en,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            en: !prev.en\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"English\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.fr,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            fr: !prev.fr\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"French\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"experiences\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"form\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Formik, {\n                                        initialValues: initialValues,\n                                        validationSchema: validationSchema,\n                                        innerRef: formikRefAll,\n                                        onSubmit: handleSubmit,\n                                        className: \"formik-form\",\n                                        children: (param)=>{\n                                            let { errors, touched, setFieldValue, values } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Form, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        id: \"accordion\",\n                                                        disableGutters: true,\n                                                        expanded: expanded === `panel`,\n                                                        onChange: handleChangeAccordion(`panel`),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 39\n                                                                }, void 0),\n                                                                \"aria-controls\": `panel-content`,\n                                                                id: `panel-header`,\n                                                                children: t(\"createArticle:settings\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"accordion-detail\",\n                                                                elevation: 0,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"label-form\",\n                                                                                    children: [\n                                                                                        \"Robots meta\",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"select-pentabell\",\n                                                                                            variant: \"standard\",\n                                                                                            sx: {\n                                                                                                m: 1,\n                                                                                                minWidth: 120\n                                                                                            },\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                                value: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.filter((option)=>values.robotsMeta === option),\n                                                                                                selected: values.robotsMeta,\n                                                                                                onChange: (event)=>{\n                                                                                                    setFieldValue(\"robotsMeta\", event.target.value);\n                                                                                                },\n                                                                                                children: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                                        value: item,\n                                                                                                        children: item\n                                                                                                    }, item, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                        lineNumber: 554,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                lineNumber: 541,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 536,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                            className: \"label-error\",\n                                                                                            name: \"robotsMeta\",\n                                                                                            component: \"div\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 560,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                    lineNumber: 534,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 533,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                name: \"robotsMeta\",\n                                                                                component: \"div\",\n                                                                                className: \"label-error\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 568,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                        lineNumber: 532,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, `panel`, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    selectedLanguages.en && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleEN__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        debounce: handleChange,\n                                                        categories: categoriesEN,\n                                                        filteredCategories: filteredCategoriesEN,\n                                                        onCategoriesSelect: handleCategoriesENSelect\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    selectedLanguages.fr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleFR__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        categories: categoriesFR,\n                                                        filteredCategories: filteredCategoriesFR,\n                                                        onCategoriesSelect: handleCategoriesFRSelect,\n                                                        debounce: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"btn-container\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"button\",\n                                                                text: \"Clear\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: handleClear\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"submit\",\n                                                                text: \"Save\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: ()=>{}\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                        lineNumber: 473,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                    lineNumber: 472,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 471,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AddArticle, \"B+cNPaZHMPFNJqb0o7Rak/frCrI=\", false, function() {\n    return [\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave,\n        _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories\n    ];\n});\n_c = AddArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticle);\nvar _c;\n$RefreshReg$(_c, \"AddArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx\n"));

/***/ })

});