"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_en_login_json";
exports.ids = ["_ssr_src_locales_en_login_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/en/login.json":
/*!***********************************!*\
  !*** ./src/locales/en/login.json ***!
  \***********************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"login":"Login","keepMeSignedIn":"Keep Me Signed In","forgotPassword":"Forgot password","connectWithUs":"Connect with us","haveAnAccount":"Do you have an account ?","orConnect":"Or connect"}');

/***/ })

};
;