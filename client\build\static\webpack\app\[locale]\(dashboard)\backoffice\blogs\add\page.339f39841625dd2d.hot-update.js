"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/AddArticleEN.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! suneditor-react */ \"(app-pages-browser)/./node_modules/suneditor-react/dist/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(suneditor_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n/* harmony import */ var suneditor_dist_css_suneditor_min_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! suneditor/dist/css/suneditor.min.css */ \"(app-pages-browser)/./node_modules/suneditor/dist/css/suneditor.min.css\");\n/* harmony import */ var _feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @feelinglovelynow/slug */ \"(app-pages-browser)/./node_modules/@feelinglovelynow/slug/dist/index.js\");\n/* harmony import */ var suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! suneditor/src/plugins */ \"(app-pages-browser)/./node_modules/suneditor/src/plugins/index.js\");\n/* harmony import */ var _FaqSection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./FaqSection */ \"(app-pages-browser)/./src/features/blog/components/FaqSection.jsx\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/DatePicker/DatePicker.js\");\n/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/x-date-pickers/AdapterDayjs */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/AdapterDayjs/AdapterDayjs.js\");\n/* harmony import */ var _mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/x-date-pickers/internals/demo */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/internals/demo/DemoContainer.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var react_tag_input__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! react-tag-input */ \"(app-pages-browser)/./node_modules/react-tag-input/dist/index.js\");\n/* harmony import */ var _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomTextInput */ \"(app-pages-browser)/./src/components/ui/CustomTextInput.jsx\");\n/* harmony import */ var _DocumentImporter__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./DocumentImporter */ \"(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AddArticleEN(param) {\n    let { errors, touched, setFieldValue, values, onImageSelect, filteredCategories, categories, onCategoriesSelect, debounce } = param;\n    _s();\n    const KeyCodes = {\n        comma: 188,\n        enter: 13\n    };\n    const delimiters = [\n        KeyCodes.comma,\n        KeyCodes.enter\n    ];\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [highlights, setHighlights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(values.descriptionEN || \"\");\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(values.urlEN || \"\");\n    const [publishNow, setPublishNow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const language = \"en\";\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedTitle = localStorage.getItem(\"title\");\n        return savedTitle ? JSON.parse(savedTitle) : \"\";\n    });\n    const [metatitle, setMetatitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetatitle = localStorage.getItem(\"metatitle\");\n        return savedMetatitle ? JSON.parse(savedMetatitle) : \"\";\n    });\n    const [metaDescription, setMetaDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetadescription = localStorage.getItem(\"metaDescription\");\n        return savedMetadescription ? JSON.parse(savedMetadescription) : \"\";\n    });\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedContent = localStorage.getItem(\"content\");\n        return savedContent ? JSON.parse(savedContent) : \"\";\n    });\n    const handlePaste = (event, cleanData, maxCharCount)=>{\n        let html = cleanData;\n        // Correction des balises non fermées\n        html = html.replace(/<strong>(.*?)$/g, \"<strong>$1</strong>\");\n        return html;\n    };\n    const handleEditorChange = (newContent)=>{\n        debounce();\n        setContent(newContent);\n        setFieldValue(\"contentEN\", newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (title) {\n            localStorage.setItem(\"title\", JSON.stringify(title));\n        }\n        if (content) {\n            localStorage.setItem(\"content\", JSON.stringify(content));\n        }\n        if (metatitle) {\n            localStorage.setItem(\"metatitle\", JSON.stringify(metatitle));\n        }\n        if (metaDescription) {\n            localStorage.setItem(\"metaDescription\", JSON.stringify(metaDescription));\n        }\n    }, [\n        title,\n        content,\n        metatitle,\n        metaDescription\n    ]);\n    const handlePhotoChange = async ()=>{\n        const selectedFile = imageInputRef.current.files[0];\n        setSelectedImage(imageInputRef.current.files[0]);\n        if (selectedFile) {\n            onImageSelect(selectedFile, language);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setFieldValue(\"titleEN\", title);\n        setFieldValue(\"descriptionEN\", description);\n        setFieldValue(\"urlEN\", url);\n        setFieldValue(\"keywordsEN\", tags.map((t)=>t.text));\n        setFieldValue(\"highlightsEN\", highlights.map((h)=>h.text));\n        setFieldValue(\"contentEN\", content);\n        setFieldValue(\"metaTitleEN\", metatitle);\n        setFieldValue(\"metaDescriptionEN\", metaDescription);\n    }, [\n        title,\n        description,\n        url,\n        tags,\n        highlights,\n        content,\n        metatitle,\n        metaDescription\n    ]);\n    const useSaveFileHook = (0,_features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__.useSaveFile)();\n    let uuidPhoto;\n    uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n    const handlePhotoBlogChange = async (file, info, core, uploadHandler)=>{\n        if (file instanceof HTMLImageElement) {\n            const src = file.src;\n            if (src.startsWith(\"data:image\")) {\n                const base64Data = src.split(\",\")[1];\n                const contentType = src.match(/data:(.*?);base64/)[1];\n                const byteCharacters = atob(base64Data);\n                const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i)=>byteCharacters.charCodeAt(i));\n                const byteArray = new Uint8Array(byteNumbers);\n                const blob = new Blob([\n                    byteArray\n                ], {\n                    type: contentType\n                });\n                const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                const selectedFile = new File([\n                    blob\n                ], fileName, {\n                    type: contentType\n                });\n                await uploadFile(selectedFile, uploadHandler, core, file);\n            } else {\n                fetch(src).then((response)=>response.blob()).then((blob)=>{\n                    const contentType = blob.type;\n                    const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                    const selectedFile = new File([\n                        blob\n                    ], fileName, {\n                        type: contentType\n                    });\n                    uploadFile(selectedFile, uploadHandler, core, file);\n                }).catch((error)=>console.error(\"Error converting image URL to Blob:\", error));\n            }\n        } else {\n            console.error(\"File is not an HTMLImageElement.\");\n        }\n    };\n    const uploadFile = (selectedFile, uploadHandler, core, originalImage)=>{\n        let uuidPhoto;\n        uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n        const formData = new FormData();\n        formData.append(\"file\", selectedFile);\n        const extension = selectedFile.name.split(\".\").pop();\n        const currentYear = new Date().getFullYear();\n        useSaveFileHook.mutate({\n            resource: \"blogs\",\n            folder: currentYear.toString(),\n            filename: uuidPhoto,\n            body: {\n                formData,\n                t\n            }\n        }, {\n            onSuccess: (dataUUID)=>{\n                const uuidPhotoFileName = dataUUID.message === \"uuid exist\" ? dataUUID.uuid : `${uuidPhoto}.${extension}`;\n                const imageUrl = `${\"http://localhost:4000/api/v1\"}/files/${uuidPhotoFileName}`;\n                originalImage.src = imageUrl;\n                uploadHandler({\n                    result: [\n                        {\n                            id: uuidPhotoFileName,\n                            url: imageUrl\n                        }\n                    ]\n                });\n            },\n            onError: (error)=>{\n                console.error(\"Error uploading file:\", error);\n            }\n        });\n    };\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleContentExtracted = (extractedContent)=>{\n        localStorage.setItem(\"content\");\n        setFieldValue(\"contentEN\", extractedContent);\n        setContent(extractedContent);\n        debounce(); // Trigger autosave\n    };\n    const handleMetadataExtracted = (metadata)=>{\n        if (metadata.title && !values.titleEN) {\n            setFieldValue(\"titleEN\", metadata.title);\n            const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_10__.slug)(metadata.title);\n            setFieldValue(\"urlEN\", url);\n        }\n        if (metadata.description && !values.descriptionEN) {\n            setFieldValue(\"descriptionEN\", metadata.description);\n        }\n        if (metadata.keywords && metadata.keywords.length > 0) {\n            const keywordTags = metadata.keywords.map((keyword, index)=>({\n                    id: `extracted-${index}`,\n                    text: keyword\n                }));\n            const existingKeywords = values.keywordsEN || [];\n            const mergedKeywords = [\n                ...existingKeywords,\n                ...keywordTags\n            ];\n            setFieldValue(\"keywordsEN\", mergedKeywords.map((tag)=>tag.text));\n        }\n        debounce();\n    };\n    const handleFileChange = async (event)=>{\n        const file = event.target.files[0];\n        if (!file || file.type !== \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\") {\n            setError(\"Please upload a valid .docx file.\");\n            return;\n        }\n        const reader = new FileReader();\n        reader.onload = async (event)=>{\n            const arrayBuffer = event.target.result;\n            try {\n                const result = await mammoth__WEBPACK_IMPORTED_MODULE_4__.convertToHtml({\n                    arrayBuffer\n                });\n                setFieldValue(\"contentEN\", result.value);\n                setContent(result.value);\n                setError(null);\n            } catch (err) {\n                console.error(\"Mammoth conversion error:\", err);\n                setError(\"Failed to convert the DOCX file.\");\n            }\n        };\n        reader.readAsArrayBuffer(file);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"label-pentabell\",\n                children: \"Add article English : \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    label: t(\"createArticle:title\"),\n                                    name: \"titleEN\",\n                                    value: title,\n                                    onChange: (e)=>{\n                                        const v = e.target.value;\n                                        setTitle(v);\n                                        setUrl((0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_10__.slug)(v));\n                                        debounce();\n                                    },\n                                    error: touched.titleEN && errors.titleEN\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:categories\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                multiple: true,\n                                                className: \"input-pentabell\",\n                                                id: \"tags-standard\",\n                                                options: filteredCategories.length > 0 ? filteredCategories : categories,\n                                                getOptionLabel: (option)=>option.name,\n                                                selected: values.categoryEN.length > 0 ? (filteredCategories.length > 0 ? filteredCategories : categories).filter((category)=>values.categoryEN.includes(category.id)) : [],\n                                                onChange: (event, selectedOptions)=>{\n                                                    const categoryIds = selectedOptions.map((category)=>category.id);\n                                                    setFieldValue(\"categoryEN\", categoryIds);\n                                                    onCategoriesSelect(categoryIds);\n                                                },\n                                                renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        ...params,\n                                                        className: \"input-pentabell  multiple-select\",\n                                                        variant: \"standard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, this),\n                            touched.category && errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"label-error\",\n                                children: errors.category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 318,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Description\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"descriptionEN\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: values.descriptionEN,\n                                    onChange: (e)=>{\n                                        const descriptionEN = e.target.value;\n                                        setFieldValue(\"descriptionEN\", descriptionEN);\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.descriptionEN && touched.descriptionEN ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"descriptionEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 369,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 367,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Highlights\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"tags\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_23__.WithContext, {\n                                        tags: highlights,\n                                        className: \"input-pentabell\" + (errors.highlightsEN && touched.highlightsEN ? \" is-invalid\" : \"\"),\n                                        delimiters: delimiters,\n                                        handleDelete: (i)=>{\n                                            const updatedTags = highlights.filter((tag, index)=>index !== i);\n                                            setHighlights(updatedTags);\n                                            setFieldValue(\"highlightsEN\", updatedTags.map((tag)=>tag.text));\n                                        },\n                                        handleAddition: (tag)=>{\n                                            setHighlights([\n                                                ...highlights,\n                                                tag\n                                            ]);\n                                            setFieldValue(\"highlightsEN\", [\n                                                ...highlights,\n                                                tag\n                                            ].map((item)=>item.text));\n                                        },\n                                        inputFieldPosition: \"bottom\",\n                                        autocomplete: true,\n                                        allowDragDrop: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"keywordsEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 402,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 401,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 400,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentImporter__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                onContentExtracted: handleContentExtracted,\n                onMetadataExtracted: handleMetadataExtracted,\n                language: \"EN\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 446,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"none\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"file\",\n                        accept: \".docx\",\n                        onChange: handleFileChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 454,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"red\"\n                        },\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 455,\n                        columnNumber: 19\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 453,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((suneditor_react__WEBPACK_IMPORTED_MODULE_3___default()), {\n                setContents: content || values?.contentEN || \"\",\n                onChange: (newContent)=>{\n                    setContent(newContent);\n                    setFieldValue(\"contentEN\", newContent);\n                    debounce();\n                },\n                onPaste: handlePaste,\n                setOptions: {\n                    cleanHTML: false,\n                    disableHtmlSanitizer: true,\n                    addTagsWhitelist: \"h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button\",\n                    plugins: suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                    buttonList: [\n                        [\n                            \"undo\",\n                            \"redo\"\n                        ],\n                        [\n                            \"font\",\n                            \"fontSize\",\n                            \"formatBlock\"\n                        ],\n                        [\n                            \"bold\",\n                            \"underline\",\n                            \"italic\",\n                            \"strike\",\n                            \"subscript\",\n                            \"superscript\"\n                        ],\n                        [\n                            \"fontColor\",\n                            \"hiliteColor\"\n                        ],\n                        [\n                            \"align\",\n                            \"list\",\n                            \"lineHeight\"\n                        ],\n                        [\n                            \"outdent\",\n                            \"indent\"\n                        ],\n                        [\n                            \"table\",\n                            \"horizontalRule\",\n                            \"link\",\n                            \"image\",\n                            \"video\"\n                        ],\n                        [\n                            \"fullScreen\",\n                            \"showBlocks\",\n                            \"codeView\"\n                        ],\n                        [\n                            \"preview\",\n                            \"print\"\n                        ],\n                        [\n                            \"removeFormat\"\n                        ]\n                    ],\n                    imageUploadHandler: handlePhotoBlogChange,\n                    defaultTag: \"div\",\n                    minHeight: \"300px\",\n                    maxHeight: \"400px\",\n                    showPathLabel: false,\n                    font: [\n                        \"Proxima-Nova-Regular\",\n                        \"Proxima-Nova-Medium\",\n                        \"Proxima-Nova-Semibold\",\n                        \"Proxima-Nova-Bold\",\n                        \"Proxima-Nova-Extrabold\",\n                        \"Proxima-Nova-Black\",\n                        \"Proxima-Nova-Light\",\n                        \"Proxima-Nova-Thin\",\n                        \"Arial\",\n                        \"Times New Roman\",\n                        \"Sans-Serif\"\n                    ],\n                    charCounter: true,\n                    charCounterType: \"byte\",\n                    resizingBar: false,\n                    colorList: [\n                        // Standard Colors\n                        [\n                            \"#234791\",\n                            \"#d69b19\",\n                            \"#cc3233\",\n                            \"#009966\",\n                            \"#0b3051\",\n                            \"#2BBFAD\",\n                            \"#0b305100\",\n                            \"#0a305214\",\n                            \"#743794\",\n                            \"#ff0000\",\n                            \"#ff5e00\",\n                            \"#ffe400\",\n                            \"#abf200\",\n                            \"#00d8ff\",\n                            \"#0055ff\",\n                            \"#6600ff\",\n                            \"#ff00dd\",\n                            \"#000000\",\n                            \"#ffd8d8\",\n                            \"#fae0d4\",\n                            \"#faf4c0\",\n                            \"#e4f7ba\",\n                            \"#d4f4fa\",\n                            \"#d9e5ff\",\n                            \"#e8d9ff\",\n                            \"#ffd9fa\",\n                            \"#f1f1f1\",\n                            \"#ffa7a7\",\n                            \"#ffc19e\",\n                            \"#faed7d\",\n                            \"#cef279\",\n                            \"#b2ebf4\",\n                            \"#b2ccff\",\n                            \"#d1b2ff\",\n                            \"#ffb2f5\",\n                            \"#bdbdbd\",\n                            \"#f15f5f\",\n                            \"#f29661\",\n                            \"#e5d85c\",\n                            \"#bce55c\",\n                            \"#5cd1e5\",\n                            \"#6699ff\",\n                            \"#a366ff\",\n                            \"#f261df\",\n                            \"#8c8c8c\",\n                            \"#980000\",\n                            \"#993800\",\n                            \"#998a00\",\n                            \"#6b9900\",\n                            \"#008299\",\n                            \"#003399\",\n                            \"#3d0099\",\n                            \"#990085\",\n                            \"#353535\",\n                            \"#670000\",\n                            \"#662500\",\n                            \"#665c00\",\n                            \"#476600\",\n                            \"#005766\",\n                            \"#002266\",\n                            \"#290066\",\n                            \"#660058\",\n                            \"#222222\"\n                        ]\n                    ]\n                },\n                onImageUpload: handlePhotoBlogChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 457,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 582,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaqSection__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                values: values,\n                setFieldValue: setFieldValue,\n                errors: errors,\n                touched: touched,\n                language: \"EN\",\n                debounce: debounce\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 583,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 591,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:metaTitle\"),\n                                        \" (\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: values.metaTitleEN?.length > 65 ? \" text-danger\" : \"\",\n                                            children: [\n                                                \" \",\n                                                values.metaTitleEN?.length,\n                                                \" / 65\",\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        \")\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            variant: \"standard\",\n                                            name: \"metaTitleEN\",\n                                            type: \"text\",\n                                            value: metatitle || values.metaTitleEN,\n                                            onChange: (e)=>{\n                                                const metaTitleEN = e.target.value;\n                                                setFieldValue(\"metaTitleEN\", metaTitleEN);\n                                                setMetatitle(metaTitleEN);\n                                                debounce();\n                                            },\n                                            className: \"input-pentabell\" + (errors.metaTitleEN && touched.metaTitleEN ? \" is-invalid\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"metaTitleEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 595,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 593,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                label: t(\"createArticle:url\"),\n                                name: \"urlEN\",\n                                value: url,\n                                onChange: (e)=>setUrl(e.target.value),\n                                error: touched.urlEN && errors.urlEN\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 635,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 634,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 633,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 592,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:metaDescription\"),\n                                \" (\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: values.metaDescriptionEN?.length > 160 ? \" text-danger\" : \"\",\n                                    children: [\n                                        values.metaDescriptionEN?.length,\n                                        \" / 160\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                \")\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"metaDescriptionEN\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: metaDescription || values.metaDescriptionEN,\n                                    onChange: (e)=>{\n                                        const metaDescriptionEN = e.target.value;\n                                        setFieldValue(\"metaDescriptionEN\", metaDescriptionEN);\n                                        setMetaDescription(metaDescriptionEN);\n                                        debounce();\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.metaDescriptionEN && touched.metaDescriptionEN ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"metaDescriptionEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 648,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 647,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 646,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 645,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:featuredImage\"),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"upload-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: `image-upload-en`,\n                                        className: \"file-labels\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                id: `image-upload-en`,\n                                                name: \"imageEN\",\n                                                accept: \".png, .jpg, .jpeg, .webp\",\n                                                ref: imageInputRef,\n                                                onChange: (e)=>{\n                                                    setFieldValue(\"imageEN\", e.target.files[0]);\n                                                    handlePhotoChange();\n                                                },\n                                                className: \"file-input\" + (errors.imageEN && touched.imageEN ? \" is-invalid\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"upload-area\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"icon-pic\",\n                                                            style: {\n                                                                backgroundImage: `url(\"${selectedImage ? URL.createObjectURL(selectedImage) : values.imageEN ? `${process.env.REACT_APP_API_URL}${_utils_urls__WEBPACK_IMPORTED_MODULE_7__.API_URLS.files}/${values.imageEN}` : _assets_images_add_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"].src}\")`,\n                                                                backgroundSize: \"cover\",\n                                                                backgroundRepeat: \"no-repeat\",\n                                                                backgroundPosition: \"center\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-text\",\n                                                                children: t(\"createArticle:addFeatImg\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-description\",\n                                                                children: t(\"createArticle:clickBox\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                                name: \"image\",\n                                                component: \"div\",\n                                                className: \"invalid-feedback error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 690,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 689,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 688,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 687,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:alt\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"altEN\",\n                                        type: \"text\",\n                                        value: values.altEN,\n                                        onChange: (e)=>{\n                                            setFieldValue(\"altEN\", e.target.value);\n                                            debounce();\n                                        },\n                                        className: \"input-pentabell\" + (errors.altEN && touched.altEN ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"altEN\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 769,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 753,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 752,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 751,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:visibility\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"select-pentabell\",\n                                            variant: \"standard\",\n                                            value: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.Visibility.filter((option)=>values.visibilityEN === option),\n                                            selected: values.visibilityEN,\n                                            onChange: (event)=>{\n                                                setFieldValue(\"visibilityEN\", event.target.value);\n                                            },\n                                            children: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.Visibility.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    value: item,\n                                                    children: item\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"visibilityEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 779,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 777,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:keyword\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            id: \"tags\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_23__.WithContext, {\n                                                tags: tags,\n                                                className: \"input-pentabell\" + (errors.keywordsEN && touched.keywordsEN ? \" is-invalid\" : \"\"),\n                                                delimiters: delimiters,\n                                                handleDelete: (i)=>{\n                                                    const updatedTags = tags.filter((tag, index)=>index !== i);\n                                                    setTags(updatedTags);\n                                                    setFieldValue(\"keywordsEN\", updatedTags.map((tag)=>tag.text));\n                                                },\n                                                handleAddition: (tag)=>{\n                                                    setTags([\n                                                        ...tags,\n                                                        tag\n                                                    ]);\n                                                    setFieldValue(\"keywordsEN\", [\n                                                        ...tags,\n                                                        tag\n                                                    ].map((item)=>item.text));\n                                                    debounce();\n                                                },\n                                                inputFieldPosition: \"bottom\",\n                                                autocomplete: true,\n                                                allowDragDrop: false\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"keywordsEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 849,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 813,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 812,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 810,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 750,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"label-form\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.Field, {\n                                    type: \"checkbox\",\n                                    name: \"publishNow\",\n                                    checked: publishNow,\n                                    onChange: (e)=>{\n                                        setPublishNow(e.target.checked);\n                                        if (e.target.checked) {\n                                            setFieldValue(\"publishDateEN\", new Date().toISOString());\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 861,\n                                    columnNumber: 13\n                                }, this),\n                                t(\"createArticle:publishNow\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 860,\n                            columnNumber: 11\n                        }, this),\n                        !publishNow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"label-form\",\n                                        children: [\n                                            t(\"createArticle:publishDate\"),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_27__.LocalizationProvider, {\n                                                dateAdapter: _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_28__.AdapterDayjs,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_29__.DemoContainer, {\n                                                    components: [\n                                                        \"DatePicker\"\n                                                    ],\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_30__.DatePicker, {\n                                                            variant: \"standard\",\n                                                            className: \"input-date\",\n                                                            format: \"DD/MM/YYYY\",\n                                                            value: dayjs__WEBPACK_IMPORTED_MODULE_12___default()(values.publishDateEN || new Date()),\n                                                            onChange: (date)=>{\n                                                                setFieldValue(\"publishDateEN\", dayjs__WEBPACK_IMPORTED_MODULE_12___default()(date).format(\"YYYY-MM-DD\"));\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                    lineNumber: 881,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 880,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 878,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"publishDateEN\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 897,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 877,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 876,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 859,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 858,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.Field, {\n                type: \"hidden\",\n                name: \"publishDateEN\",\n                value: publishNow ? new Date().toISOString() : publishDate.toISOString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 908,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AddArticleEN, \"nDc9FkDcP0selbZlc9Kghllvj14=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__.useSaveFile\n    ];\n});\n_c = AddArticleEN;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticleEN);\nvar _c;\n$RefreshReg$(_c, \"AddArticleEN\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\n"));

/***/ })

});