"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/ListArticles.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/ListArticles.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/CustomTooltip */ \"(app-pages-browser)/./src/components/ui/CustomTooltip.jsx\");\n/* harmony import */ var _components_loading_Loading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/loading/Loading */ \"(app-pages-browser)/./src/components/loading/Loading.jsx\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _assets_images_icons_preview_icon_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/preview-icon.svg */ \"(app-pages-browser)/./src/assets/images/icons/preview-icon.svg\");\n/* harmony import */ var _assets_images_icons_edit_icon_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/icons/edit-icon.svg */ \"(app-pages-browser)/./src/assets/images/icons/edit-icon.svg\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/assets/images/icons/applicationIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/applicationIcon.svg\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n/* harmony import */ var _components_ui_CustomFilters__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/CustomFilters */ \"(app-pages-browser)/./src/components/ui/CustomFilters.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ListArticles = (param)=>{\n    let { language } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [createdAt, setCreatedAt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const savedVisibility = localStorage.getItem(\"Visibility\");\n    const [visibility, setVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedVisibility || \"\");\n    const [isArchived, setIsArchivedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const savedPagination = localStorage.getItem(\"PAGINATION_KEY\");\n    const savedSeachValue = localStorage.getItem(\"SearchValue\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedSeachValue || \"\");\n    const [paginationModel, setPaginationModel] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(savedPagination ? JSON.parse(savedPagination) : {\n        page: 0,\n        pageSize: 10\n    });\n    const isOpen = true;\n    const [selectedLanguage, setSelectedLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(language ? language : \"en\");\n    const resetSearch = ()=>{\n        setCategory(\"\");\n        setSearchQuery(\"\");\n        setVisibility(\"\");\n        setSortOrder(\"\");\n        setCreatedAt(null);\n        setPublishDate(null);\n        setSelectedLanguage(language ? language : \"en\");\n        setPaginationModel({\n            page: 0,\n            pageSize: 10\n        });\n        setIsArchivedFilter(\"\");\n        setSearch(!search);\n        localStorage.setItem(\"Visibility\", \"\");\n        localStorage.setItem(\"SearchValue\", \"\");\n        localStorage.setItem(\"PAGINATION_KEY\", JSON.stringify({\n            page: 0,\n            pageSize: 10\n        }));\n    };\n    const truncateTitle = (title)=>{\n        const words = title.split(\" \");\n        if (words?.length > 4) {\n            return words.slice(0, 4).join(\" \") + \"...\";\n        } else {\n            return title;\n        }\n    };\n    const getCategoriesLang = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories)(selectedLanguage || \"en\");\n    const transformedCategoriesLang = getCategoriesLang?.data?.categories?.map((category)=>({\n            name: category?.versionscategory[0]?.name,\n            value: category?.versionscategory[0]?.name,\n            label: category?.versionscategory[0]?.name\n        })) || [];\n    const getArticles = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetArticlesDashboard)({\n        language: selectedLanguage,\n        pageSize: paginationModel.pageSize,\n        pageNumber: paginationModel.page + 1,\n        sortOrder,\n        searchQuery,\n        visibility,\n        createdAt,\n        isArchived,\n        publishDate,\n        categoryName: category\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSelectedLanguage(language);\n        getCategoriesLang.refetch();\n    }, [\n        language\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getArticles.refetch();\n    }, [\n        search,\n        paginationModel\n    ]);\n    const visibilityOption = [\n        {\n            value: \"Public\",\n            label: \"Public\"\n        },\n        {\n            value: \"Private\",\n            label: \"Private\"\n        },\n        {\n            value: \"Draft\",\n            label: \"Draft\"\n        }\n    ];\n    const handlePaginationChange = (newPaginationModel)=>{\n        setPaginationModel(newPaginationModel);\n        localStorage.setItem(\"PAGINATION_KEY\", JSON.stringify(newPaginationModel));\n    };\n    if (getArticles.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loading_Loading__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n            lineNumber: 127,\n            columnNumber: 12\n        }, undefined);\n    }\n    const rows = getArticles?.data?.articles?.map((item, index)=>({\n            id: item._id,\n            title: item?.versions?.[0]?.title ? truncateTitle(item?.versions?.[0]?.title) : \"No title\",\n            createdBy: item?.versions[0].createdBy || \"N/A\",\n            createdAt: item?.versions[0].createdAt,\n            language: item?.existingLanguages?.join(\"/\") || \"N/A\",\n            actions: item._id,\n            visibility: item?.versions?.[0]?.visibility || \"N/A\",\n            url: item?.versions?.[0]?.url || \"N/A\",\n            totalCommentaires: item?.totalCommentaires || \"0\",\n            isArchived: item?.versions[0].isArchived\n        })) || [];\n    const columns = [\n        {\n            field: \"title\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            headerName: t(\"listArticle:title\"),\n            flex: 1,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: `/${selectedLanguage}/blog/${params.row?.url}`,\n                    className: \"link\",\n                    children: params.row.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"createdBy\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            headerName: t(\"listArticle:createdBy\"),\n            flex: 0.4\n        },\n        {\n            field: \"createdAt\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            flex: 0.4,\n            headerName: t(\"listArticle:createdAt\"),\n            valueFormatter: _utils_functions__WEBPACK_IMPORTED_MODULE_11__.formatDate\n        },\n        {\n            field: \"language\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            flex: 0.4,\n            headerName: t(\"listopportunity:availablelanguage\")\n        },\n        {\n            field: \"visibility\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            flex: 0.4,\n            headerName: t(\"listArticle:visibility\")\n        },\n        {\n            field: \"isArchived\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            flex: 0.4,\n            headerName: t(\"listArticle:archived\"),\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: params.row.isArchived ? t(\"global:yes\") : t(\"global:no\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"totalCommentaires\",\n            headerClassName: \"datagrid-header\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            headerName: t(\"listArticle:nbOfComments\"),\n            flex: 0.4\n        },\n        {\n            field: \"actions\",\n            cellClassName: \"datagrid-cell\",\n            headerClassName: \"datagrid-header\",\n            headerName: \"\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"action-buttons\",\n                    style: {\n                        gridColumn: \"span 2\",\n                        width: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_edit_icon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.edit.route}/${params.row.id}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:edit\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.comments.route}/${params.row.id}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 237,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:comments\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_preview_icon_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.websiteRoutesList.blog.route}/${params.row?.url}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:preview\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, undefined),\n            flex: 1\n        }\n    ];\n    const archivedOptions = [\n        {\n            value: true,\n            label: \"Archived\"\n        },\n        {\n            value: false,\n            label: \"Not Archived\"\n        }\n    ];\n    const filters = [\n        {\n            type: \"text\",\n            label: \"Search By Title\",\n            value: searchQuery,\n            onChange: (e)=>setSearchQuery(e.target.value),\n            placeholder: \"Search\",\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:visibility\"),\n            value: visibility ? {\n                value: visibility,\n                label: visibilityOption.find((opt)=>opt.value === visibility)?.label || visibility\n            } : null,\n            onChange: (e, val)=>setVisibility(val?.value || \"\"),\n            options: visibilityOption,\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:archivage\"),\n            value: isArchived ? {\n                value: isArchived,\n                label: archivedOptions.find((opt)=>opt.value === isArchived)?.label || isArchived\n            } : null,\n            onChange: (e, val)=>setIsArchivedFilter(val?.value || \"\"),\n            options: archivedOptions,\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"global:sort\"),\n            value: sortOrder ? {\n                value: sortOrder,\n                label: t(sortOrder === \"desc\" ? \"global:newest\" : \"global:oldest\")\n            } : null,\n            onChange: (e, val)=>setSortOrder(val?.value || \"\"),\n            options: [\n                {\n                    value: \"desc\",\n                    label: t(\"global:newest\")\n                },\n                {\n                    value: \"asc\",\n                    label: t(\"global:oldest\")\n                }\n            ],\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:category\"),\n            value: category ? {\n                value: category,\n                label: transformedCategoriesLang.find((c)=>c.value === category)?.label || category\n            } : null,\n            onChange: (e, val)=>setCategory(val?.value || \"\"),\n            options: transformedCategoriesLang,\n            condition: true\n        },\n        {\n            type: \"date\",\n            label: t(\"listArticle:createdAt\"),\n            value: createdAt,\n            onChange: (newValue)=>setCreatedAt(newValue),\n            condition: true\n        },\n        {\n            type: \"date\",\n            label: t(\"listArticle:PublishDate\"),\n            value: publishDate,\n            onChange: (newValue)=>setPublishDate(newValue),\n            condition: true\n        }\n    ];\n    const handleSearch = ()=>{\n        localStorage.setItem(\"SearchValue\", searchQuery);\n        localStorage.setItem(\"Visibility\", visibility);\n        setPaginationModel({\n            page: 0,\n            pageSize: paginationModel.pageSize\n        });\n        setSearch((prev)=>!prev);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"display-inline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"heading-h2 semi-bold\",\n                        children: [\n                            t(\"listArticle:listOfArticles\"),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"opportunities-nbr\",\n                                children: getArticles?.data?.totalArticles\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"btn btn-filled\",\n                        text: t(\"global:addarticle\"),\n                        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.add.route}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 367,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content ${isOpen ? \"open\" : \"closed\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"table-Grid\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomFilters__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    filters: filters,\n                                    onSearch: handleSearch,\n                                    onReset: resetSearch,\n                                    searchLabel: t(\"global:filter\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    height: \"100%\",\n                                    width: \"100%\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_data_grid__WEBPACK_IMPORTED_MODULE_16__.DataGrid, {\n                                    rows: rows,\n                                    columns: columns,\n                                    pagination: true,\n                                    className: \"pentabell-table\",\n                                    paginationMode: \"server\",\n                                    paginationModel: paginationModel,\n                                    onPaginationModelChange: handlePaginationChange,\n                                    pageSizeOptions: [\n                                        5,\n                                        10,\n                                        25\n                                    ],\n                                    rowCount: getArticles?.data?.totalArticles || 0,\n                                    autoHeight: true,\n                                    disableSelectionOnClick: true,\n                                    columnVisibilityModel: {\n                                        createdBy: !isMobile,\n                                        createdAt: !isMobile,\n                                        totalCommentaires: !isMobile,\n                                        visibility: !isMobile,\n                                        language: !isMobile,\n                                        archived: !isMobile\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 383,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 382,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ListArticles, \"wVAUuV3YcnVKSMMLI20KZyXnGv8=\", false, function() {\n    return [\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetArticlesDashboard\n    ];\n});\n_c = ListArticles;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ListArticles);\nvar _c;\n$RefreshReg$(_c, \"ListArticles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/ListArticles.jsx\n"));

/***/ })

});