import fs from 'fs';
import path from 'path';

import { Request, Response, NextFunction } from 'express';

const apiKeyMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    if (req.headers?.accept !== 'application/json' && req.method == 'GET'){
        try {
            const unauthorizedHtmlPath = path.join(__dirname, '../public/unauthorized.html');
            const unauthorizedHtml = fs.readFileSync(unauthorizedHtmlPath, 'utf-8');

            res.status(403).send(unauthorizedHtml);
        } catch (error) {
            console.error('Error reading unauthorized HTML file:', error);
            res.status(500).send('Internal Server Error');
        }
        
    } else {
        next();
    }
};

export default apiKeyMiddleware;