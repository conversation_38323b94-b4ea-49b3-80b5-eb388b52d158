import fs from 'fs';
import path from 'path';

import { Request, Response, NextFunction } from 'express';
import { MESSAGES } from '@/utils/helpers/messages';
import HttpException from '@/utils/exceptions/http.exception';

const apiKeyMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    if (req.headers?.accept !== 'application/json' && req.method == 'GET') {
        try {
            const unauthorizedHtmlPath = path.join(__dirname, '../public/unauthorized.html');
            const unauthorizedHtml = fs.readFileSync(unauthorizedHtmlPath, 'utf-8');

            res.status(403).send(unauthorizedHtml);
        } catch (error) {
            throw new HttpException(500, MESSAGES.GENERAL.SERVER_ERROR)
        }

    } else {
        next();
    }
};

export default apiKeyMiddleware;