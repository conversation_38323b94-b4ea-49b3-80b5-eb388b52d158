"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_locales_fr_statsTotalNumbers_json"],{

/***/ "(app-pages-browser)/./src/locales/fr/statsTotalNumbers.json":
/*!***********************************************!*\
  !*** ./src/locales/fr/statsTotalNumbers.json ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"totalOpportunities":"opportunités","totalArticles":"articles","totalComments":"commentaires","totalCandidates":"candidats","totalContacts":"contacts","totalApplications":"candidatures","totalCvUploaded":"C.V. téléchargés","logins":"Connexion"}');

/***/ })

}]);