"use client";
import { memo, useMemo } from "react";
import { Container, Grid, useMediaQuery, useTheme } from "@mui/material";

import ArticleContent from "../../blog/components/ArticleContent";
import PentabellCompanySection from "./PentabellCompanySection";
import GlossaryHeader from "./GlossaryHeader";
import GlossarySocialMediaIcon from "./GlossarySocialMediaIcon";

const GlossaryDetails = memo(function GlossaryDetails({
  glossary,
  language,
  isMobileSSR,
}) {
  const theme = useTheme();

  const isMobile = useMediaQuery(theme.breakpoints.down("sm")) || isMobileSSR;
  const isTablet = useMediaQuery(theme.breakpoints.down("md")) || isMobileSSR;

  const breadcrumbSchema = useMemo(
    () => ({
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          item: {
            "@id":
              language === "en"
                ? `https://www.pentabell.com/glossaries/`
                : `https://www.pentabell.com/${language}/glossaries/`,
            name: "Glossary",
          },
        },
      ],
    }),
    [language]
  );

  const glossaryPath = useMemo(
    () => (language === "en" ? `/glossaries` : `/${language}/glossaries`),
    [language]
  );

  return (
    <div id="glossary-page-details">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema),
        }}
      />
      <Grid className="container" container columnSpacing={2}>
        <Grid item sm={12} md={12} lg={9}>
          <GlossaryHeader
            language={language}
            glossaryPath={glossaryPath}
            glossary={glossary}
          />
          <Container className="custom-max-width">
            <ArticleContent htmlContent={glossary?.content} />
            <PentabellCompanySection language={language} />
            {isMobile && isTablet && <GlossarySocialMediaIcon />}
          </Container>
        </Grid>
        {!(isMobile && isTablet) && (
          <Grid item sm={12} md={12} lg={3}>
            <Container className="custom-max-width">
              <GlossarySocialMediaIcon />
            </Container>
          </Grid>
        )}
      </Grid>
    </div>
  );
});

export default GlossaryDetails;
