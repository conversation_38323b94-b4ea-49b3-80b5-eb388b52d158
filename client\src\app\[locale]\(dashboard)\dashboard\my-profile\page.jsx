"use client";;
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import ProfileInfos from "@/features/user/component/updateProfile/ProfileInfos";
import { useTranslation } from "react-i18next";


const page = () => {
  const { user } = useCurrentUser();
  const { t, i18n } = useTranslation();
  return (
    <>
      <div>
      <p className="heading-h2 semi-bold">{t("menu:profile")}</p>
      </div>
      <ProfileInfos />
    </>
  );
};

export default page;
