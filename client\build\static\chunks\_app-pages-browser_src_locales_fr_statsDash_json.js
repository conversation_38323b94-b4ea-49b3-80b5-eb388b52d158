"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_locales_fr_statsDash_json"],{

/***/ "(app-pages-browser)/./src/locales/fr/statsDash.json":
/*!***************************************!*\
  !*** ./src/locales/fr/statsDash.json ***!
  \***************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"commentsByCategory":"Commentaires par catégorie","filters":"Filtres","categories":"Catégories","approvedComments":"Commentaires approuvés","approved":"Approuvé","notApproved":"Non approuvé","fromDate":"À partir de la date","toDate":"Jusqu\'à la date","filter":"Filtrer","usersActivities":"Activités des utilisateurs","logins":"Connexions","newAccounts":"Nouveaux comptes","uploadedResumes":"CV téléchargés","applications":"Candidatures","applicationsByStatus":"Candidatures par statut","opportunities":"Opportunités","type":"Type","industry":"Secteur","platformActivity":"Activités de la plateforme","newOpportunities":"Nouvelles opportunités","newArticles":"Nouveaux articles","newslettersSubscriptions":"Abonnements aux newsletters","newContacts":"Nouveaux contacts","articlesByVisibility":"Articles par visibilité","public":"Public","private":"Privé","draft":"Brouillon","accepted":"Accepté","pending":"En attente","rejected":"Rejeté","all":"Tous","opportunityType":"Type d\'opportunité"}');

/***/ })

}]);