"use client";

import { useTranslation } from "react-i18next";

import AuthLayout from "@/components/layouts/AuthLayout";
import Confirmnotconnected from "../../../../features/application/component/confirmApplicationNotConnected";

const page = () => {
  const { t } = useTranslation();
  return (
    <AuthLayout id="auth-layout" >
      <Confirmnotconnected t={t} />
    </AuthLayout>
  );
};

export default page;
