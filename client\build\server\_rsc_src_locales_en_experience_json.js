"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_locales_en_experience_json";
exports.ids = ["_rsc_src_locales_en_experience_json"];
exports.modules = {

/***/ "(rsc)/./src/locales/en/experience.json":
/*!****************************************!*\
  !*** ./src/locales/en/experience.json ***!
  \****************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"titeleeditex":"Update Experience","titleexperince":"Work Experience ","titleajouterex":"Add Experience","company":"company","contratType":"ContratType","location":"location","currentJob":"currentJob","jobDescription":"job description","Name":"title*","startdate":"start date *","enddate":"end date *","academy":"Academy*","from":"from","Until":"Until","message":" Do you really want to delete experience","noExperiences":"No experience"}');

/***/ })

};
;