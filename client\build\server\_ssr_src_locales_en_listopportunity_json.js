"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_en_listopportunity_json";
exports.ids = ["_ssr_src_locales_en_listopportunity_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/en/listopportunity.json":
/*!*********************************************!*\
  !*** ./src/locales/en/listopportunity.json ***!
  \*********************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"oppotuhnities":"Opportunities list","job":"Job","contratType":"Contract type","description":"Description","Publishdate":"Publish Date","actions":"Actions","availablelanguage":"Available language","visibility":"Visibility","expirationDate":"Expiration date","contract":"Contract","industry":"Industry","postingDate":"Posting Date"}');

/***/ })

};
;