"use client";
import { useState, useEffect } from "react";
import { axiosGetJson } from "../../../config/axios";
import { useTranslation } from "react-i18next";

//import Sidebar from "../../../components/Dashboard/Sidebar/Sidebar";
import Energie from "@/assets/images/Energy.svg";
import Ittelecom from "@/assets/images/It & Telecom.svg";
import Transport from "@/assets/images/Transport.svg";
import Oilgas from "@/assets/images/Oil & gaz.svg";
import Banking from "@/assets/images/Banking and insurance.svg";
import nodata from "@/assets/images/No data.png";
import Statistics3 from "../../../components/Statistics/Statistics3";

import CustomButton from "../../../components/ui/CustomButton";
import { useGetFavourites } from "../hooks/application.hooks";

import ArticleFavourites from "./ArticleFavourite";
import JobFavourites from "./JobFavourites";

const ApplicationFavoris = () => {
  const { t } = useTranslation();
  const industryImages = {
    Energy: Energie,
    "It & Telecom": Ittelecom,
    Transport: Transport,
    "Oil & gas": Oilgas,
    "Banking & Insurance": Banking,
  };
  const [paginationModel, setPaginationModel] = useState({
    pageSize: 5,
    page: 0,
  });

  const [searchValue, setSearchValue] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [shortlists, setShortLists] = useState([]);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [jobsTodelete, setJobsTodelete] = useState(false);
  const [showJobs, setShowJobs] = useState(true);
  const [total, setTotal] = useState(0);
  const [totalArticles, setTotalArticles] = useState(0);

  const [typeOfFavourite, setTypeOfFavourite] = useState("opportunity");
  const getFavourites = useGetFavourites({
    pageSize: paginationModel.pageSize,
    pageNumber: paginationModel.page + 1,
    keyword: searchQuery,
    typeOfFavourite,
  });

  const isJob =
    shortlists?.filter(
      (item) => item.versions && Object.keys(item.versions ?? {}).length > 0
    ).length > 0;
  const isArticle =
    shortlists?.filter((item) => item.versions && item.versions.length > 0)
      .length > 0;

  useEffect(() => {
    getFavourites.refetch();
    setShortLists(getFavourites?.data?.favourites);
    setTotal(getFavourites?.data?.totalFavouriteOpportunities);
    setTotalArticles(getFavourites?.data?.totalFavouriteArticles);
  }, [paginationModel, searchQuery, typeOfFavourite, getFavourites.data]);

  const handleSearchChange = (e) => {
    setSearchValue(e.target.value);
  };
  const handeleDeleteconfirmation = (opportunityId) => {
    setJobsTodelete(opportunityId);
    setShowDeleteConfirmation(true);
  };
  const handlecanceldelete = () => {
    setShowDeleteConfirmation(false);
  };
  const deleteOpportunityFromShortlist = async (opportunityId) => {
    try {
      await axiosGetJson.delete(`/favourite/${opportunityId}`, {
        data: { type: typeOfFavourite },
      });
      getFavourites.refetch();
      setShortLists(
        shortlists.filter((opportunity) => opportunity._id !== opportunityId)
      );
    } catch (error) {}
  };
  const handleToggleOpportunity = async () => {
    try {
      await deleteOpportunityFromShortlist(jobsTodelete);
    } catch (error) {}
    setShowDeleteConfirmation(false);
  };

  const isStatusActive = (dateOfExpiration) => {
    const expirationDate = new Date(dateOfExpiration);
    const currentDate = new Date();

    return expirationDate > currentDate;
  };

  const handlePageChange = (page) => {
    setPageNumber(page);
  };

  const handlePageSizeChange = (size) => {
    setPageSize(size);
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };
  const handleSearchClick = () => {
    setSearchQuery(searchValue);
  };

  return (
    <>
      <Statistics3 totalOpp={total} totalArticles={totalArticles} />

      <div id="container" className="my-applications-pentabell">
        <div className={`main-content`}>
          <div id="filter-btns">
            <CustomButton
              onClick={() => {
                setShowJobs(true);
                setTypeOfFavourite("opportunity");
              }}
              text={t("global:jobs")}
              className={` btnPanel ${showJobs ? " btnPanel-filled" : ""}`}
            />
            <CustomButton
              text={t("global:articles")}
              className={`btnPanel ${!showJobs ? "btnPanel-filled" : ""}`}
              onClick={() => {
                setShowJobs(false);
                setTypeOfFavourite("article");
              }}
            />{" "}
          </div>
          <div>
            {total === 0 && showJobs ? (
              <img
                src={nodata.src}
                className="centered-image"
                alt="Application favorite"
                loading="lazy"
              />
            ) : (
              <>
                { (
                  <JobFavourites
                    totalOpportunities={total}
                    jobFavourites={shortlists}
                    typeOfFavourite={typeOfFavourite}
                    setShortLists={setShortLists}
                    getFavourites={getFavourites}
                    paginationModel={paginationModel}
                    setPaginationModel={setPaginationModel}
                  />
                )}
              </>
            )}
            {totalArticles === 0 && showJobs === false ? (
              <img
                src={nodata.src}
                className="centered-image"
                alt="Application favorite"
                loading="lazy"
              />
            ) : (
              <>
                {isArticle && showJobs === false && (
                  <ArticleFavourites
                    totalArticles={totalArticles}
                    articleFavourites={shortlists}
                    typeOfFavourite={typeOfFavourite}
                    setShortLists={setShortLists}
                    getFavourites={getFavourites}
                    paginationModel={paginationModel}
                    setPaginationModel={setPaginationModel}
                  />
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ApplicationFavoris;
