import { Router, Request, Response, NextFunction } from 'express';
import * as path from 'path';
import * as fs from 'fs';
import pdf from 'pdf-parse';
import WordExtractor from 'word-extractor';
import * as crypto from 'crypto';

import FilesService from './files.service';
import { fileExistMiddleware } from '@/middlewares/fileExist.middleware';
import { uploadMiddleware } from '@/middlewares/upload.middleware';
import { validateFormatMiddleware } from '@/middlewares/validateFormat.middleware';
import HttpException from '@/utils/exceptions/http.exception';
import { FileI } from './files.interface';
import Controller from '@/utils/interfaces/controller.interface';
import { validateParams, validateUUID } from '@/middlewares/validation.middleware';
import isAuthenticated from '@/middlewares/authentication.middleware';
import UserService from '../user/services/user.service';
import { extractTextFromScannedPDF, validate } from '@/utils/helpers/functions';
import { hasRoles } from '@/middlewares/authorization.middleware';
import { Role } from '@/utils/helpers/constants';
import sharp from 'sharp';
import { MESSAGES } from '@/utils/helpers/messages';

class FilesController implements Controller {
    public path = '/files';
    public router = Router();
    private userService = new UserService();
    private filesService = new FilesService();

    constructor() {
        this.initialiseRoutes();
    }

    private initialiseRoutes(): void {
        this.router.get(`${this.path}/:filename`, validateUUID, validateFormatMiddleware, this.getFileByUuid);
        this.router.get(`${this.path}/byresource/:resource`, this.getFileByResource);
        this.router.get(`${this.path}`, isAuthenticated, this.getUserFileByUuid);
        this.router.get(`${this.path}/candidate/:filename`, this.findFileCandidateBase64);
        this.router.post(
            `${this.path}/:resource/:folder/:filename`,
            validateParams,
            validateUUID,
            fileExistMiddleware,
            uploadMiddleware,
            this.createFile,
        );
        this.router.post(
            `${this.path}/uploadResume/:resource/:folder/:filename`,
            validateParams,
            validateUUID,
            fileExistMiddleware,
            uploadMiddleware,
            this.uploadResume,
        );
        this.router.get(`/file/:originalName`, this.getFileByName);
        this.router.get(`/eventMaps/:originalName`, this.getFileByNameeventmaps);
        this.router.put(`${this.path}/compress`, isAuthenticated, hasRoles([Role.ADMIN]), this.compressImage);
        this.router.put(`${this.path}/convert-image-to-webp`, isAuthenticated, hasRoles([Role.ADMIN, Role.EDITEUR]), this.convertImagesToWebp);

        this.router.delete(`${this.path}/:filename`, isAuthenticated, validateUUID, this.deleteFile);
    }

    public findFileCandidateBase64 = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const fileName = request.params.filename;
            const fileBase64 = await this.filesService.findFileCandidateBase64(fileName);

            response.status(200).json({
                fileName,
                fileContent: fileBase64,
            });
        } catch (error) {
            next(error);
        }
    };
    private createFile = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const { filename, resource, folder } = request.params;
            const file = request.file;
            let resumeText = '';
            if (!file) {
                return next(new HttpException(400, MESSAGES.FILE.PROVIDE_FILE));
            }

            const ipAddress = request.socket.remoteAddress || request.ip;
            const fileNameParts = file.originalname.split('.');

            const dataBuffer = fs.readFileSync(file.path);

            const checksum = crypto.createHash('sha256').update(dataBuffer).digest('hex');
            const existingFile = await this.filesService.getFileByChecksum(checksum);
            if (existingFile) {
                throw new HttpException(409, MESSAGES.FILE.DUPLICATE_CHECKSUM);
            }

            const fileData: FileI = {
                resource: resource,
                folder: folder,
                ipSender: ipAddress,
                originalName: file.originalname,
                uuid: filename,
                fileName: filename + '.' + fileNameParts[fileNameParts.length - 1].toLowerCase(),
                fileType: file.mimetype,
                fileSize: file.size,
                checksum: checksum,
            };

            if (resource === 'candidates') {
                if (file.mimetype === 'application/pdf') {
                    const data = await pdf(dataBuffer);
                    resumeText = data.text;
                    resumeText = resumeText.replace(/\s+/g, ' ').trim();
                    if (!validate(resumeText)) {
                        resumeText = await extractTextFromScannedPDF(dataBuffer);
                        resumeText = resumeText.replace(/\s+/g, ' ').trim();
                    }
                } else if (
                    file.mimetype === 'application/msword' ||
                    file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                ) {
                    const extractor = new WordExtractor();
                    const doc = await extractor.extract(dataBuffer);
                    resumeText = doc.getBody();
                } else {
                    throw new HttpException(400, MESSAGES.FILE.FILE_TYPE_NOT_ALLOWED);
                }

                const validateContent = validate(resumeText);
                if (!validateContent) {
                    throw new HttpException(400, MESSAGES.FILE.RESUME_MISSING_INFO);
                }
            }

            await this.filesService.createFile(fileData);

            if (resource === 'candidates') {
                response.status(201).send({ message: MESSAGES.FILE.CREATED_SUCCESS, data: resumeText });
            } else {
                response.status(201).send({ message: MESSAGES.FILE.CREATED_SUCCESS });
            }
        } catch (error) {
            next(error);
        }
    };


    private uploadResume = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const { filename, resource, folder } = request.params;
            const file = request.file;
            let resumeText = '';
            if (!file) {
                return next(new HttpException(400, MESSAGES.FILE.PROVIDE_FILE));
            }
            const ipAddress = request.socket.remoteAddress || request.ip;
            const originalExt = path.extname(file.originalname).toLowerCase();
            const allowedImageTypes = ['.png', '.jpg', '.jpeg'];
            let finalPath = file.path;
            let finalExt = originalExt;
            let finalMimeType = file.mimetype;
            let originalName = file.originalname;
            const dataBuffer = fs.readFileSync(file.path);
            const checksum = crypto.createHash('sha256').update(dataBuffer).digest('hex');
            const existingFiles = await this.filesService.getFilesByChecksum(checksum);
            const hasWebp = existingFiles.some(f => path.extname(f.fileName).toLowerCase() === '.webp');
            const hasPngOrJpg = existingFiles.some(f => allowedImageTypes.includes(path.extname(f.fileName).toLowerCase()));
            if (hasWebp) {
                const webpFile = existingFiles.find(f => path.extname(f.fileName).toLowerCase() === '.webp');
                return response.status(201).json({
                    message: MESSAGES.FILE.UUID_EXIST,
                    uuid: webpFile?.fileName
                });
            }

            if (hasPngOrJpg || allowedImageTypes.includes(originalExt)) {
                try {
                    const buffer = fs.readFileSync(file.path);
                    const webpPath = file.path.replace(/\.[^/.]+$/, '.webp');

                    await sharp(buffer)
                        .rotate()
                        .webp({ quality: 80 })
                        .toFile(webpPath);

                    fs.unlinkSync(file.path);
                    finalPath = webpPath;
                    finalExt = '.webp';
                    finalMimeType = 'image/webp';
                    originalName = path.basename(webpPath);
                } catch (err) {
                    return next(new HttpException(500, MESSAGES.GENERAL.SERVER_ERROR));
                }
            }


            const fileData: FileI = {
                resource,
                folder,
                ipSender: ipAddress,
                originalName,
                uuid: filename,
                fileName: filename + finalExt,
                fileType: finalMimeType,
                fileSize: fs.statSync(finalPath).size,
                checksum,
            };

            if (resource === 'candidates') {
                if (finalMimeType === 'application/pdf') {
                    const data = await pdf(dataBuffer);
                    resumeText = data.text.replace(/\s+/g, ' ').trim();

                    if (!validate(resumeText)) {
                        resumeText = await extractTextFromScannedPDF(dataBuffer);
                        resumeText = resumeText.replace(/\s+/g, ' ').trim();
                    }

                } else if (
                    finalMimeType === 'application/msword' ||
                    finalMimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                ) {
                    const extractor = new WordExtractor();
                    const doc = await extractor.extract(dataBuffer);
                    resumeText = doc.getBody().replace(/\s+/g, ' ').trim();

                } else if (!finalMimeType.startsWith('image/')) {
                    return next(new HttpException(400, MESSAGES.FILE.FILE_TYPE_NOT_ALLOWED));
                }
                if (!validate(resumeText)) {
                    return next(new HttpException(400, MESSAGES.FILE.RESUME_MISSING_INFO));
                }
            }

            await this.filesService.createFile(fileData);
            return response.status(201).json(
                resource === 'candidates'
                    ? { message: MESSAGES.FILE.CREATED_SUCCESS, data: resumeText }
                    : { message: MESSAGES.FILE.CREATED_SUCCESS, uuid: filename + finalExt }
            );

        } catch (error) {
            next(error);
        }
    };
    private getFileByResource = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const ressource = request.params.resource;
            const queries = request.query;
            const result = await this.filesService.findByresource(ressource, queries);
            response.send(result);
        } catch (error) {
            next(error);
        }
    };

    private getFileByUuid = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const fileName = request.params.filename;

            const findOnePath = await this.filesService.findFile(fileName);

            response.status(200).sendFile(path.resolve(findOnePath), err => {
                if (err) {
                    return next(new HttpException(404, MESSAGES.FILE.INVALID_RESSOURCE));
                }
            });
        } catch (error) {
            next(error);
        }
    };
    private getFileByName = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const originalName = request.params.originalName;

            const findOnePath = await this.filesService.findFileByName(originalName);

            response.status(200).sendFile(path.resolve(findOnePath), err => {
                if (err) {
                    return next(new HttpException(404, MESSAGES.FILE.INVALID_RESSOURCE));
                }
            });
        } catch (error) {
            next(error);
        }
    };
    private getFileByNameeventmaps = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const originalName = request.params.originalName;

            const findOnePath = await this.filesService.findFileeventMaps(originalName);

            response.status(200).sendFile(path.resolve(findOnePath), err => {
                if (err) {
                    return next(new HttpException(404, MESSAGES.FILE.INVALID_RESSOURCE));
                }
            });
        } catch (error) {
            next(error);
        }
    };

    private getUserFileByUuid = async (request: any, response: Response, next: NextFunction) => {
        try {
            const userId = request.user._id;
            const userdata = await this.userService.get(userId);
            const findOnePath = await this.filesService.findFile(userdata?.profilePicture);

            response.status(200).sendFile(path.resolve(findOnePath), err => {
                if (err) {
                    return next(new HttpException(404, MESSAGES.FILE.INVALID_RESSOURCE));
                }
            });
        } catch (error) {
            next(error);
        }
    };

    private deleteFile = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const filename = request.params.filename;
            await this.filesService.deleteFile(filename);
            response.status(200).json({ message: MESSAGES.FILE.DELETED });
        } catch (error) {
            next(error);
        }
    };

    private countFiles = async (request: any, response: Response, next: NextFunction) => {
        try {
            const query = request.query;
            const result = await this.filesService.countFiles('./uploads', query);
            response.send(result);
        } catch (error) {
            next(error);
        }
    };

    private readonly compressImage = async (request: any, response: Response, next: NextFunction) => {
        try {
            const { resource, folder, quality } = request.body;
            const result = await this.filesService.compressImage(resource, folder, quality);
            response.send(result);
        } catch (error) {
            next(error);
        }
    };

    private readonly convertImagesToWebp = async (request: any, response: Response, next: NextFunction) => {
        try {
            const { imagesPath } = request.body;
            const result = await this.filesService.convertImagesToWebp(imagesPath);
            response.send(result);
        } catch (error) {
            next(error);
        }
    };
}

export default FilesController;
