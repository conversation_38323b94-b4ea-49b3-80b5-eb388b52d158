"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/ListArticles.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/ListArticles.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/CustomTooltip */ \"(app-pages-browser)/./src/components/ui/CustomTooltip.jsx\");\n/* harmony import */ var _components_loading_Loading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/loading/Loading */ \"(app-pages-browser)/./src/components/loading/Loading.jsx\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _assets_images_icons_preview_icon_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/preview-icon.svg */ \"(app-pages-browser)/./src/assets/images/icons/preview-icon.svg\");\n/* harmony import */ var _assets_images_icons_edit_icon_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/icons/edit-icon.svg */ \"(app-pages-browser)/./src/assets/images/icons/edit-icon.svg\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/assets/images/icons/applicationIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/applicationIcon.svg\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n/* harmony import */ var _components_ui_CustomFilters__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/CustomFilters */ \"(app-pages-browser)/./src/components/ui/CustomFilters.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ListArticles = (param)=>{\n    let { language } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [createdAt, setCreatedAt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const savedVisibility = localStorage.getItem(\"Visibility\");\n    const [visibility, setVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedVisibility || \"\");\n    const [isArchived, setIsArchivedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Default to show non-archived articles\n    const savedPagination = localStorage.getItem(\"PAGINATION_KEY\");\n    const savedSeachValue = localStorage.getItem(\"SearchValue\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedSeachValue || \"\");\n    const [paginationModel, setPaginationModel] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(savedPagination ? JSON.parse(savedPagination) : {\n        page: 0,\n        pageSize: 10\n    });\n    const isOpen = true;\n    const [selectedLanguage, setSelectedLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(language ? language : \"en\");\n    // State for confirmation dialogs\n    const [openArchiveDialog, setOpenArchiveDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openDeleteDialog, setOpenDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedArticle, setSelectedArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Hooks for archive and delete operations\n    const archiveArticleMutation = usearchivedarticle();\n    const deleteArticleMutation = useDeleteArticle();\n    // Handler functions for archive and delete\n    const handleArchiveClick = (article)=>{\n        setSelectedArticle(article);\n        setOpenArchiveDialog(true);\n    };\n    const handleDeleteClick = (article)=>{\n        setSelectedArticle(article);\n        setOpenDeleteDialog(true);\n    };\n    const handleArchiveConfirm = async ()=>{\n        if (selectedArticle) {\n            try {\n                await archiveArticleMutation.mutateAsync({\n                    language: selectedLanguage,\n                    id: selectedArticle.id,\n                    archive: !selectedArticle.isArchived\n                });\n                getArticles.refetch();\n            } catch (error) {\n                console.error(\"Error archiving article:\", error);\n            }\n        }\n        setOpenArchiveDialog(false);\n        setSelectedArticle(null);\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (selectedArticle) {\n            try {\n                await deleteArticleMutation.mutateAsync({\n                    language: selectedLanguage,\n                    id: selectedArticle.id\n                });\n                getArticles.refetch();\n            } catch (error) {\n                console.error(\"Error deleting article:\", error);\n            }\n        }\n        setOpenDeleteDialog(false);\n        setSelectedArticle(null);\n    };\n    const handleDialogClose = ()=>{\n        setOpenArchiveDialog(false);\n        setOpenDeleteDialog(false);\n        setSelectedArticle(null);\n    };\n    const resetSearch = ()=>{\n        setCategory(\"\");\n        setSearchQuery(\"\");\n        setVisibility(\"\");\n        setSortOrder(\"\");\n        setCreatedAt(null);\n        setPublishDate(null);\n        setSelectedLanguage(language ? language : \"en\");\n        setPaginationModel({\n            page: 0,\n            pageSize: 10\n        });\n        setIsArchivedFilter(false); // Reset to show non-archived articles\n        setSearch(!search);\n        localStorage.setItem(\"Visibility\", \"\");\n        localStorage.setItem(\"SearchValue\", \"\");\n        localStorage.setItem(\"PAGINATION_KEY\", JSON.stringify({\n            page: 0,\n            pageSize: 10\n        }));\n    };\n    const truncateTitle = (title)=>{\n        const words = title.split(\" \");\n        if (words?.length > 4) {\n            return words.slice(0, 4).join(\" \") + \"...\";\n        } else {\n            return title;\n        }\n    };\n    const getCategoriesLang = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories)(selectedLanguage || \"en\");\n    const transformedCategoriesLang = getCategoriesLang?.data?.categories?.map((category)=>({\n            name: category?.versionscategory[0]?.name,\n            value: category?.versionscategory[0]?.name,\n            label: category?.versionscategory[0]?.name\n        })) || [];\n    const getArticles = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetArticlesDashboard)({\n        language: selectedLanguage,\n        pageSize: paginationModel.pageSize,\n        pageNumber: paginationModel.page + 1,\n        sortOrder,\n        searchQuery,\n        visibility,\n        createdAt,\n        isArchived,\n        publishDate,\n        categoryName: category\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSelectedLanguage(language);\n        getCategoriesLang.refetch();\n    }, [\n        language\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getArticles.refetch();\n    }, [\n        search,\n        paginationModel\n    ]);\n    const visibilityOption = [\n        {\n            value: \"Public\",\n            label: \"Public\"\n        },\n        {\n            value: \"Private\",\n            label: \"Private\"\n        },\n        {\n            value: \"Draft\",\n            label: \"Draft\"\n        }\n    ];\n    const handlePaginationChange = (newPaginationModel)=>{\n        setPaginationModel(newPaginationModel);\n        localStorage.setItem(\"PAGINATION_KEY\", JSON.stringify(newPaginationModel));\n    };\n    if (getArticles.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loading_Loading__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n            lineNumber: 182,\n            columnNumber: 12\n        }, undefined);\n    }\n    const rows = getArticles?.data?.articles?.map((item, index)=>({\n            id: item._id,\n            title: item?.versions?.[0]?.title ? truncateTitle(item?.versions?.[0]?.title) : \"No title\",\n            createdBy: item?.versions?.[0]?.createdBy || \"N/A\",\n            createdAt: item?.versions?.[0]?.createdAt,\n            language: item?.existingLanguages?.join(\"/\") || \"N/A\",\n            actions: item._id,\n            visibility: item?.versions?.[0]?.visibility || \"N/A\",\n            url: item?.versions?.[0]?.url || \"N/A\",\n            totalCommentaires: item?.totalCommentaires || \"0\",\n            isArchived: item?.versions?.[0]?.isArchived || false\n        })) || [];\n    const columns = [\n        {\n            field: \"title\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            headerName: t(\"listArticle:title\"),\n            flex: 1,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: `/${selectedLanguage}/blog/${params.row?.url}`,\n                    className: \"link\",\n                    children: params.row.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"createdBy\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            headerName: t(\"listArticle:createdBy\"),\n            flex: 0.4\n        },\n        {\n            field: \"createdAt\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            flex: 0.4,\n            headerName: t(\"listArticle:createdAt\"),\n            valueFormatter: _utils_functions__WEBPACK_IMPORTED_MODULE_11__.formatDate\n        },\n        {\n            field: \"language\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            flex: 0.4,\n            headerName: t(\"listopportunity:availablelanguage\")\n        },\n        {\n            field: \"visibility\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            flex: 0.4,\n            headerName: t(\"listArticle:visibility\")\n        },\n        {\n            field: \"isArchived\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell\",\n            flex: 0.4,\n            headerName: t(\"listArticle:archived\"),\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: params.row.isArchived ? t(\"global:yes\") : t(\"global:no\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"totalCommentaires\",\n            headerClassName: \"datagrid-header\",\n            headerClassName: \"datagrid-header\",\n            cellClassName: \"datagrid-cell \",\n            headerName: t(\"listArticle:nbOfComments\"),\n            flex: 0.4\n        },\n        {\n            field: \"actions\",\n            cellClassName: \"datagrid-cell\",\n            headerClassName: \"datagrid-header\",\n            headerName: \"\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"action-buttons\",\n                    style: {\n                        gridColumn: \"span 2\",\n                        width: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_edit_icon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.edit.route}/${params.row.id}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 278,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:edit\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"btn-download\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_applicationIcon_svg__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.comments.route}/${params.row.id}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:comments\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_preview_icon_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    leftIcon: true,\n                                    link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.websiteRoutesList.blog.route}/${params.row?.url}`,\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 306,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:preview\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"22\",\n                                        height: \"20\",\n                                        viewBox: \"0 0 22 20\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M21 17C21 17.5304 20.7893 18.0391 20.4142 18.4142C20.0391 18.7893 19.5304 19 19 19H3C2.46957 19 1.96086 18.7893 1.58579 18.4142C1.21071 18.0391 1 17.5304 1 17V3C1 2.46957 1.21071 1.96086 1.58579 1.58579C1.96086 1.21071 2.46957 1 3 1H8L10 4H19C19.5304 4 20.0391 4.21071 20.4142 4.58579C20.7893 4.96086 21 5.46957 21 6V17Z\",\n                                            stroke: \"#1E1E1E\",\n                                            strokeWidth: \"2\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    leftIcon: true,\n                                    onClick: ()=>handleArchiveClick(params.row),\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 320,\n                                columnNumber: 15\n                            }, void 0),\n                            title: params.row.isArchived ? t(\"global:unarchive\") : t(\"global:archive\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            child: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"20\",\n                                        height: \"22\",\n                                        viewBox: \"0 0 20 22\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M1 5H3M3 5H19M3 5V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H15C15.5304 21 16.0391 20.7893 16.4142 20.4142C16.7893 20.0391 17 19.5304 17 19V5M6 5V3C6 2.46957 6.21071 1.96086 6.58579 1.58579C6.96086 1.21071 7.46957 1 8 1H12C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V5M8 10V16M12 10V16\",\n                                            stroke: \"#1E1E1E\",\n                                            strokeWidth: \"2\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    leftIcon: true,\n                                    onClick: ()=>handleDeleteClick(params.row),\n                                    className: \"btn btn-ghost edit-blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 354,\n                                columnNumber: 15\n                            }, void 0),\n                            title: t(\"global:delete\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, undefined),\n            flex: 1\n        }\n    ];\n    const archivedOptions = [\n        {\n            value: true,\n            label: \"Archived\"\n        },\n        {\n            value: false,\n            label: \"Not Archived\"\n        }\n    ];\n    const filters = [\n        {\n            type: \"text\",\n            label: \"Search By Title\",\n            value: searchQuery,\n            onChange: (e)=>setSearchQuery(e.target.value),\n            placeholder: \"Search\",\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:visibility\"),\n            value: visibility ? {\n                value: visibility,\n                label: visibilityOption.find((opt)=>opt.value === visibility)?.label || visibility\n            } : null,\n            onChange: (e, val)=>setVisibility(val?.value || \"\"),\n            options: visibilityOption,\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:archivage\"),\n            value: isArchived ? {\n                value: isArchived,\n                label: archivedOptions.find((opt)=>opt.value === isArchived)?.label || isArchived\n            } : null,\n            onChange: (e, val)=>setIsArchivedFilter(val?.value || \"\"),\n            options: archivedOptions,\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"global:sort\"),\n            value: sortOrder ? {\n                value: sortOrder,\n                label: t(sortOrder === \"desc\" ? \"global:newest\" : \"global:oldest\")\n            } : null,\n            onChange: (e, val)=>setSortOrder(val?.value || \"\"),\n            options: [\n                {\n                    value: \"desc\",\n                    label: t(\"global:newest\")\n                },\n                {\n                    value: \"asc\",\n                    label: t(\"global:oldest\")\n                }\n            ],\n            condition: true\n        },\n        {\n            type: \"select\",\n            label: t(\"listArticle:category\"),\n            value: category ? {\n                value: category,\n                label: transformedCategoriesLang.find((c)=>c.value === category)?.label || category\n            } : null,\n            onChange: (e, val)=>setCategory(val?.value || \"\"),\n            options: transformedCategoriesLang,\n            condition: true\n        },\n        {\n            type: \"date\",\n            label: t(\"listArticle:createdAt\"),\n            value: createdAt,\n            onChange: (newValue)=>setCreatedAt(newValue),\n            condition: true\n        },\n        {\n            type: \"date\",\n            label: t(\"listArticle:publishDate\"),\n            value: publishDate,\n            onChange: (newValue)=>setPublishDate(newValue),\n            condition: true\n        }\n    ];\n    const handleSearch = ()=>{\n        localStorage.setItem(\"SearchValue\", searchQuery);\n        localStorage.setItem(\"Visibility\", visibility);\n        setPaginationModel({\n            page: 0,\n            pageSize: paginationModel.pageSize\n        });\n        setSearch((prev)=>!prev);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"display-inline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"heading-h2 semi-bold\",\n                        children: [\n                            t(\"listArticle:listOfArticles\"),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"opportunities-nbr\",\n                                children: getArticles?.data?.totalArticles\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 488,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"btn btn-filled\",\n                        text: t(\"global:addarticle\"),\n                        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.blogs.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_10__.adminRoutes.add.route}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                        lineNumber: 495,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content ${isOpen ? \"open\" : \"closed\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"table-Grid\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomFilters__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    filters: filters,\n                                    onSearch: handleSearch,\n                                    onReset: resetSearch,\n                                    searchLabel: t(\"global:search\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    height: \"100%\",\n                                    width: \"100%\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_data_grid__WEBPACK_IMPORTED_MODULE_16__.DataGrid, {\n                                    rows: rows,\n                                    columns: columns,\n                                    pagination: true,\n                                    className: \"pentabell-table\",\n                                    paginationMode: \"server\",\n                                    paginationModel: paginationModel,\n                                    onPaginationModelChange: handlePaginationChange,\n                                    pageSizeOptions: [\n                                        5,\n                                        10,\n                                        25\n                                    ],\n                                    rowCount: getArticles?.data?.totalArticles || 0,\n                                    autoHeight: true,\n                                    disableSelectionOnClick: true,\n                                    columnVisibilityModel: {\n                                        createdBy: !isMobile,\n                                        createdAt: !isMobile,\n                                        totalCommentaires: !isMobile,\n                                        visibility: !isMobile,\n                                        language: !isMobile,\n                                        archived: !isMobile\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                                lineNumber: 516,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                            lineNumber: 515,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                    lineNumber: 503,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ListArticles.jsx\",\n                lineNumber: 502,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ListArticles, \"5YZ611sJ6OtD1N+zmncpv3mb7mI=\", true, function() {\n    return [\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetArticlesDashboard\n    ];\n});\n_c = ListArticles;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ListArticles);\nvar _c;\n$RefreshReg$(_c, \"ListArticles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9ibG9nL2NvbXBvbmVudHMvTGlzdEFydGljbGVzLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBbUQ7QUFDSjtBQUNIO0FBQ1A7QUFDcUI7QUFDQTtBQUNxQjtBQUNmO0FBQ047QUFDRjtBQUNtQjtBQUNuQjtBQUsxQjtBQUNpQjtBQUNXO0FBRTFELE1BQU1xQixlQUFlO1FBQUMsRUFBRUMsUUFBUSxFQUFFOztJQUNoQyxNQUFNQyxRQUFRVCx1R0FBUUE7SUFDdEIsTUFBTVUsV0FBV1QsdUdBQWFBLENBQUNRLE1BQU1FLFdBQVcsQ0FBQ0MsSUFBSSxDQUFDO0lBQ3RELE1BQU0sRUFBRUMsQ0FBQyxFQUFFLEdBQUd4Qiw2REFBY0E7SUFDNUIsTUFBTSxDQUFDeUIsVUFBVUMsWUFBWSxHQUFHNUIsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDNkIsV0FBV0MsYUFBYSxHQUFHOUIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDK0IsV0FBV0MsYUFBYSxHQUFHaEMsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDaUMsYUFBYUMsZUFBZSxHQUFHbEMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDbUMsUUFBUUMsVUFBVSxHQUFHcEMsK0NBQVFBLENBQUM7SUFDckMsTUFBTXFDLGtCQUFrQkMsYUFBYUMsT0FBTyxDQUFDO0lBQzdDLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHekMsK0NBQVFBLENBQUNxQyxtQkFBbUI7SUFDaEUsTUFBTSxDQUFDSyxZQUFZQyxvQkFBb0IsR0FBRzNDLCtDQUFRQSxDQUFDLFFBQVEsd0NBQXdDO0lBQ25HLE1BQU00QyxrQkFBa0JOLGFBQWFDLE9BQU8sQ0FBQztJQUM3QyxNQUFNTSxrQkFBa0JQLGFBQWFDLE9BQU8sQ0FBQztJQUM3QyxNQUFNLENBQUNPLGFBQWFDLGVBQWUsR0FBRy9DLCtDQUFRQSxDQUFDNkMsbUJBQW1CO0lBQ2xFLE1BQU0sQ0FBQ0csaUJBQWlCQyxtQkFBbUIsR0FBR2xELHFEQUFjLENBQzFENkMsa0JBQ0lNLEtBQUtDLEtBQUssQ0FBQ1AsbUJBQ1g7UUFDRVEsTUFBTTtRQUNOQyxVQUFVO0lBQ1o7SUFHTixNQUFNQyxTQUFTO0lBQ2YsTUFBTSxDQUFDQyxrQkFBa0JDLG9CQUFvQixHQUFHeEQsK0NBQVFBLENBQ3REcUIsV0FBV0EsV0FBVztJQUd4QixpQ0FBaUM7SUFDakMsTUFBTSxDQUFDb0MsbUJBQW1CQyxxQkFBcUIsR0FBRzFELCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQzJELGtCQUFrQkMsb0JBQW9CLEdBQUc1RCwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUM2RCxpQkFBaUJDLG1CQUFtQixHQUFHOUQsK0NBQVFBLENBQUM7SUFFdkQsMENBQTBDO0lBQzFDLE1BQU0rRCx5QkFBeUJDO0lBQy9CLE1BQU1DLHdCQUF3QkM7SUFDOUIsMkNBQTJDO0lBQzNDLE1BQU1DLHFCQUFxQixDQUFDQztRQUMxQk4sbUJBQW1CTTtRQUNuQlYscUJBQXFCO0lBQ3ZCO0lBRUEsTUFBTVcsb0JBQW9CLENBQUNEO1FBQ3pCTixtQkFBbUJNO1FBQ25CUixvQkFBb0I7SUFDdEI7SUFFQSxNQUFNVSx1QkFBdUI7UUFDM0IsSUFBSVQsaUJBQWlCO1lBQ25CLElBQUk7Z0JBQ0YsTUFBTUUsdUJBQXVCUSxXQUFXLENBQUM7b0JBQ3ZDbEQsVUFBVWtDO29CQUNWaUIsSUFBSVgsZ0JBQWdCVyxFQUFFO29CQUN0QkMsU0FBUyxDQUFDWixnQkFBZ0JuQixVQUFVO2dCQUN0QztnQkFDQWdDLFlBQVlDLE9BQU87WUFDckIsRUFBRSxPQUFPQyxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUM1QztRQUNGO1FBQ0FsQixxQkFBcUI7UUFDckJJLG1CQUFtQjtJQUNyQjtJQUVBLE1BQU1nQixzQkFBc0I7UUFDMUIsSUFBSWpCLGlCQUFpQjtZQUNuQixJQUFJO2dCQUNGLE1BQU1JLHNCQUFzQk0sV0FBVyxDQUFDO29CQUN0Q2xELFVBQVVrQztvQkFDVmlCLElBQUlYLGdCQUFnQlcsRUFBRTtnQkFDeEI7Z0JBQ0FFLFlBQVlDLE9BQU87WUFDckIsRUFBRSxPQUFPQyxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtZQUMzQztRQUNGO1FBQ0FoQixvQkFBb0I7UUFDcEJFLG1CQUFtQjtJQUNyQjtJQUVBLE1BQU1pQixvQkFBb0I7UUFDeEJyQixxQkFBcUI7UUFDckJFLG9CQUFvQjtRQUNwQkUsbUJBQW1CO0lBQ3JCO0lBRUEsTUFBTWtCLGNBQWM7UUFDbEJwRCxZQUFZO1FBQ1ptQixlQUFlO1FBQ2ZOLGNBQWM7UUFDZFgsYUFBYTtRQUNiRSxhQUFhO1FBQ2JFLGVBQWU7UUFDZnNCLG9CQUFvQm5DLFdBQVdBLFdBQVc7UUFDMUM0QixtQkFBbUI7WUFBRUcsTUFBTTtZQUFHQyxVQUFVO1FBQUc7UUFDM0NWLG9CQUFvQixRQUFRLHNDQUFzQztRQUNsRVAsVUFBVSxDQUFDRDtRQUNYRyxhQUFhMkMsT0FBTyxDQUFDLGNBQWM7UUFDbkMzQyxhQUFhMkMsT0FBTyxDQUFDLGVBQWU7UUFDcEMzQyxhQUFhMkMsT0FBTyxDQUNsQixrQkFDQS9CLEtBQUtnQyxTQUFTLENBQUM7WUFDYjlCLE1BQU07WUFDTkMsVUFBVTtRQUNaO0lBRUo7SUFFQSxNQUFNOEIsZ0JBQWdCLENBQUNDO1FBQ3JCLE1BQU1DLFFBQVFELE1BQU1FLEtBQUssQ0FBQztRQUMxQixJQUFJRCxPQUFPRSxTQUFTLEdBQUc7WUFDckIsT0FBT0YsTUFBTUcsS0FBSyxDQUFDLEdBQUcsR0FBR0MsSUFBSSxDQUFDLE9BQU87UUFDdkMsT0FBTztZQUNMLE9BQU9MO1FBQ1Q7SUFDRjtJQUVBLE1BQU1NLG9CQUFvQmxGLGtFQUFnQkEsQ0FBQytDLG9CQUFvQjtJQUUvRCxNQUFNb0MsNEJBQ0pELG1CQUFtQkUsTUFBTUMsWUFBWUMsSUFBSSxDQUFDbkUsV0FBYztZQUN0RG9FLE1BQU1wRSxVQUFVcUUsZ0JBQWdCLENBQUMsRUFBRSxFQUFFRDtZQUNyQ0UsT0FBT3RFLFVBQVVxRSxnQkFBZ0IsQ0FBQyxFQUFFLEVBQUVEO1lBQ3RDRyxPQUFPdkUsVUFBVXFFLGdCQUFnQixDQUFDLEVBQUUsRUFBRUQ7UUFDeEMsT0FBTyxFQUFFO0lBRVgsTUFBTXJCLGNBQWNuRSx5RUFBdUJBLENBQUM7UUFDMUNjLFVBQVVrQztRQUNWRixVQUFVTCxnQkFBZ0JLLFFBQVE7UUFDbEM4QyxZQUFZbkQsZ0JBQWdCSSxJQUFJLEdBQUc7UUFDbkN2QjtRQUNBaUI7UUFDQU47UUFDQVQ7UUFDQVc7UUFDQVQ7UUFDQW1FLGNBQWN6RTtJQUNoQjtJQUVBMUIsZ0RBQVNBLENBQUM7UUFDUnVELG9CQUFvQm5DO1FBQ3BCcUUsa0JBQWtCZixPQUFPO0lBQzNCLEdBQUc7UUFBQ3REO0tBQVM7SUFFYnBCLGdEQUFTQSxDQUFDO1FBQ1J5RSxZQUFZQyxPQUFPO0lBQ3JCLEdBQUc7UUFBQ3hDO1FBQVFhO0tBQWdCO0lBRTVCLE1BQU1xRCxtQkFBbUI7UUFDdkI7WUFBRUosT0FBTztZQUFVQyxPQUFPO1FBQVM7UUFDbkM7WUFBRUQsT0FBTztZQUFXQyxPQUFPO1FBQVU7UUFDckM7WUFBRUQsT0FBTztZQUFTQyxPQUFPO1FBQVE7S0FDbEM7SUFFRCxNQUFNSSx5QkFBeUIsQ0FBQ0M7UUFDOUJ0RCxtQkFBbUJzRDtRQUNuQmpFLGFBQWEyQyxPQUFPLENBQUMsa0JBQWtCL0IsS0FBS2dDLFNBQVMsQ0FBQ3FCO0lBQ3hEO0lBRUEsSUFBSTdCLFlBQVk4QixTQUFTLEVBQUU7UUFDekIscUJBQU8sOERBQUNsRyxtRUFBT0E7Ozs7O0lBQ2pCO0lBRUEsTUFBTW1HLE9BQ0ovQixhQUFha0IsTUFBTWMsVUFBVVosSUFBSSxDQUFDYSxNQUFNQyxRQUFXO1lBQ2pEcEMsSUFBSW1DLEtBQUtFLEdBQUc7WUFDWnpCLE9BQU91QixNQUFNRyxVQUFVLENBQUMsRUFBRSxFQUFFMUIsUUFDeEJELGNBQWN3QixNQUFNRyxVQUFVLENBQUMsRUFBRSxFQUFFMUIsU0FDbkM7WUFDSjJCLFdBQVdKLE1BQU1HLFVBQVUsQ0FBQyxFQUFFLEVBQUVDLGFBQWE7WUFDN0NoRixXQUFXNEUsTUFBTUcsVUFBVSxDQUFDLEVBQUUsRUFBRS9FO1lBQ2hDVixVQUFVc0YsTUFBTUssbUJBQW1CdkIsS0FBSyxRQUFRO1lBQ2hEd0IsU0FBU04sS0FBS0UsR0FBRztZQUNqQnJFLFlBQVltRSxNQUFNRyxVQUFVLENBQUMsRUFBRSxFQUFFdEUsY0FBYztZQUMvQzBFLEtBQUtQLE1BQU1HLFVBQVUsQ0FBQyxFQUFFLEVBQUVJLE9BQU87WUFDakNDLG1CQUFtQlIsTUFBTVEscUJBQXFCO1lBQzlDekUsWUFBWWlFLE1BQU1HLFVBQVUsQ0FBQyxFQUFFLEVBQUVwRSxjQUFjO1FBQ2pELE9BQU8sRUFBRTtJQUVYLE1BQU0wRSxVQUFVO1FBQ2Q7WUFDRUMsT0FBTztZQUNQQyxpQkFBaUI7WUFDakJDLGVBQWU7WUFDZkMsWUFBWTlGLEVBQUU7WUFFZCtGLE1BQU07WUFDTkMsWUFBWSxDQUFDQyx1QkFDWCw4REFBQ0M7b0JBQ0NDLE1BQU0sQ0FBQyxDQUFDLEVBQUV0RSxpQkFBaUIsTUFBTSxFQUFFb0UsT0FBT0csR0FBRyxFQUFFWixJQUFJLENBQUM7b0JBQ3BEYSxXQUFVOzhCQUVUSixPQUFPRyxHQUFHLENBQUMxQyxLQUFLOzs7Ozs7UUFHdkI7UUFDQTtZQUNFaUMsT0FBTztZQUNQQyxpQkFBaUI7WUFDakJDLGVBQWU7WUFDZkMsWUFBWTlGLEVBQUU7WUFDZCtGLE1BQU07UUFDUjtRQUNBO1lBQ0VKLE9BQU87WUFDUEMsaUJBQWlCO1lBQ2pCQyxlQUFlO1lBQ2ZFLE1BQU07WUFDTkQsWUFBWTlGLEVBQUU7WUFDZHNHLGdCQUFnQjlHLHlEQUFVQTtRQUM1QjtRQUNBO1lBQ0VtRyxPQUFPO1lBQ1BDLGlCQUFpQjtZQUNqQkMsZUFBZTtZQUNmRSxNQUFNO1lBQ05ELFlBQVk5RixFQUFFO1FBQ2hCO1FBQ0E7WUFDRTJGLE9BQU87WUFDUEMsaUJBQWlCO1lBQ2pCQyxlQUFlO1lBQ2ZFLE1BQU07WUFDTkQsWUFBWTlGLEVBQUU7UUFDaEI7UUFDQTtZQUNFMkYsT0FBTztZQUNQQyxpQkFBaUI7WUFDakJDLGVBQWU7WUFDZkUsTUFBTTtZQUNORCxZQUFZOUYsRUFBRTtZQUNkZ0csWUFBWSxDQUFDQyx1QkFDWCw4REFBQ007OEJBQUdOLE9BQU9HLEdBQUcsQ0FBQ3BGLFVBQVUsR0FBR2hCLEVBQUUsZ0JBQWdCQSxFQUFFOzs7Ozs7UUFFcEQ7UUFDQTtZQUNFMkYsT0FBTztZQUNQQyxpQkFBaUI7WUFDakJBLGlCQUFpQjtZQUNqQkMsZUFBZTtZQUNmQyxZQUFZOUYsRUFBRTtZQUNkK0YsTUFBTTtRQUNSO1FBRUE7WUFDRUosT0FBTztZQUNQRSxlQUFlO1lBQ2ZELGlCQUFpQjtZQUNqQkUsWUFBWTtZQUNaRSxZQUFZLENBQUNDLHVCQUNYLDhEQUFDTztvQkFDQ0gsV0FBVTtvQkFDVkksT0FBTzt3QkFBRUMsWUFBWTt3QkFBVUMsT0FBTztvQkFBTzs7c0NBRTdDLDhEQUFDaEksb0VBQWFBOzRCQUNaaUkscUJBQ0UsOERBQUNKOzBDQUNDLDRFQUFDdkgsbUVBQVlBO29DQUNYNEgsb0JBQU0sOERBQUM3SCwwRUFBT0E7Ozs7O29DQUNkOEgsVUFBVTtvQ0FDVkMsTUFBTSxDQUFDLENBQUMsRUFBRXpILG1FQUFpQkEsQ0FBQzBILE9BQU8sQ0FBQ0MsS0FBSyxDQUFDLENBQUMsRUFBRTVILDZEQUFXQSxDQUFDNkgsS0FBSyxDQUFDRCxLQUFLLENBQUMsQ0FBQyxFQUFFNUgsNkRBQVdBLENBQUM4SCxJQUFJLENBQUNGLEtBQUssQ0FBQyxDQUFDLEVBQUVoQixPQUFPRyxHQUFHLENBQUN0RCxFQUFFLENBQUMsQ0FBQztvQ0FDakh1RCxXQUFVOzs7Ozs7Ozs7Ozs0QkFJaEIzQyxPQUFPMUQsRUFBRTs7Ozs7O3NDQUdYLDhEQUFDckIsb0VBQWFBOzRCQUNaaUkscUJBQ0UsOERBQUNKO2dDQUFJSCxXQUFVOzBDQUNiLDRFQUFDcEgsbUVBQVlBO29DQUNYNEgsb0JBQU0sOERBQUMzSCxnRkFBa0JBOzs7OztvQ0FDekI0SCxVQUFVO29DQUNWQyxNQUFNLENBQUMsQ0FBQyxFQUFFekgsbUVBQWlCQSxDQUFDMEgsT0FBTyxDQUFDQyxLQUFLLENBQUMsQ0FBQyxFQUFFNUgsNkRBQVdBLENBQUM2SCxLQUFLLENBQUNELEtBQUssQ0FBQyxDQUFDLEVBQUU1SCw2REFBV0EsQ0FBQytILFFBQVEsQ0FBQ0gsS0FBSyxDQUFDLENBQUMsRUFBRWhCLE9BQU9HLEdBQUcsQ0FBQ3RELEVBQUUsQ0FBQyxDQUFDO29DQUNySHVELFdBQVU7Ozs7Ozs7Ozs7OzRCQUloQjNDLE9BQU8xRCxFQUFFOzs7Ozs7c0NBR1gsOERBQUNyQixvRUFBYUE7NEJBQ1ppSSxxQkFDRSw4REFBQ0o7MENBQ0MsNEVBQUN2SCxtRUFBWUE7b0NBQ1g0SCxvQkFBTSw4REFBQzlILDZFQUFVQTs7Ozs7b0NBQ2pCK0gsVUFBVTtvQ0FDVkMsTUFBTSxDQUFDLENBQUMsRUFBRXhILG1FQUFpQkEsQ0FBQzhILElBQUksQ0FBQ0osS0FBSyxDQUFDLENBQUMsRUFBRWhCLE9BQU9HLEdBQUcsRUFBRVosSUFBSSxDQUFDO29DQUMzRGEsV0FBVTs7Ozs7Ozs7Ozs7NEJBSWhCM0MsT0FBTzFELEVBQUU7Ozs7OztzQ0FHWCw4REFBQ3JCLG9FQUFhQTs0QkFDWmlJLHFCQUNFLDhEQUFDSjswQ0FDQyw0RUFBQ3ZILG1FQUFZQTtvQ0FDWDRILG9CQUNFLDhEQUFDUzt3Q0FDQ1gsT0FBTTt3Q0FDTlksUUFBTzt3Q0FDUEMsU0FBUTt3Q0FDUkMsTUFBSzt3Q0FDTEMsT0FBTTtrREFFTiw0RUFBQ0M7NENBQ0NDLEdBQUU7NENBQ0ZDLFFBQU87NENBQ1BDLGFBQVk7NENBQ1pDLGVBQWM7NENBQ2RDLGdCQUFlOzs7Ozs7Ozs7OztvQ0FJckJsQixVQUFVO29DQUNWbUIsU0FBUyxJQUFNeEYsbUJBQW1Cd0QsT0FBT0csR0FBRztvQ0FDNUNDLFdBQVU7Ozs7Ozs7Ozs7OzRCQUloQjNDLE9BQ0V1QyxPQUFPRyxHQUFHLENBQUNwRixVQUFVLEdBQ2pCaEIsRUFBRSxzQkFDRkEsRUFBRTs7Ozs7O3NDQUlWLDhEQUFDckIsb0VBQWFBOzRCQUNaaUkscUJBQ0UsOERBQUNKOzBDQUNDLDRFQUFDdkgsbUVBQVlBO29DQUNYNEgsb0JBQ0UsOERBQUNTO3dDQUNDWCxPQUFNO3dDQUNOWSxRQUFPO3dDQUNQQyxTQUFRO3dDQUNSQyxNQUFLO3dDQUNMQyxPQUFNO2tEQUVOLDRFQUFDQzs0Q0FDQ0MsR0FBRTs0Q0FDRkMsUUFBTzs0Q0FDUEMsYUFBWTs0Q0FDWkMsZUFBYzs0Q0FDZEMsZ0JBQWU7Ozs7Ozs7Ozs7O29DQUlyQmxCLFVBQVU7b0NBQ1ZtQixTQUFTLElBQU10RixrQkFBa0JzRCxPQUFPRyxHQUFHO29DQUMzQ0MsV0FBVTs7Ozs7Ozs7Ozs7NEJBSWhCM0MsT0FBTzFELEVBQUU7Ozs7Ozs7Ozs7OztZQUlmK0YsTUFBTTtRQUNSO0tBQ0Q7SUFDRCxNQUFNbUMsa0JBQWtCO1FBQ3RCO1lBQUUzRCxPQUFPO1lBQU1DLE9BQU87UUFBVztRQUNqQztZQUFFRCxPQUFPO1lBQU9DLE9BQU87UUFBZTtLQUN2QztJQUVELE1BQU0yRCxVQUFVO1FBQ2Q7WUFDRUMsTUFBTTtZQUNONUQsT0FBTztZQUNQRCxPQUFPbkQ7WUFDUGlILFVBQVUsQ0FBQ0MsSUFBTWpILGVBQWVpSCxFQUFFQyxNQUFNLENBQUNoRSxLQUFLO1lBQzlDaUUsYUFBYTtZQUNiQyxXQUFXO1FBQ2I7UUFFQTtZQUNFTCxNQUFNO1lBQ041RCxPQUFPeEUsRUFBRTtZQUNUdUUsT0FBT3pELGFBQ0g7Z0JBQ0V5RCxPQUFPekQ7Z0JBQ1AwRCxPQUNFRyxpQkFBaUIrRCxJQUFJLENBQUMsQ0FBQ0MsTUFBUUEsSUFBSXBFLEtBQUssS0FBS3pELGFBQWEwRCxTQUMxRDFEO1lBQ0osSUFDQTtZQUNKdUgsVUFBVSxDQUFDQyxHQUFHTSxNQUFRN0gsY0FBYzZILEtBQUtyRSxTQUFTO1lBQ2xEc0UsU0FBU2xFO1lBQ1Q4RCxXQUFXO1FBQ2I7UUFDQTtZQUNFTCxNQUFNO1lBQ041RCxPQUFPeEUsRUFBRTtZQUNUdUUsT0FBT3ZELGFBQ0g7Z0JBQ0V1RCxPQUFPdkQ7Z0JBQ1B3RCxPQUNFMEQsZ0JBQWdCUSxJQUFJLENBQUMsQ0FBQ0MsTUFBUUEsSUFBSXBFLEtBQUssS0FBS3ZELGFBQWF3RCxTQUN6RHhEO1lBQ0osSUFDQTtZQUNKcUgsVUFBVSxDQUFDQyxHQUFHTSxNQUFRM0gsb0JBQW9CMkgsS0FBS3JFLFNBQVM7WUFDeERzRSxTQUFTWDtZQUNUTyxXQUFXO1FBQ2I7UUFDQTtZQUNFTCxNQUFNO1lBQ041RCxPQUFPeEUsRUFBRTtZQUNUdUUsT0FBT3BFLFlBQ0g7Z0JBQ0VvRSxPQUFPcEU7Z0JBQ1BxRSxPQUFPeEUsRUFBRUcsY0FBYyxTQUFTLGtCQUFrQjtZQUNwRCxJQUNBO1lBQ0prSSxVQUFVLENBQUNDLEdBQUdNLE1BQVF4SSxhQUFhd0ksS0FBS3JFLFNBQVM7WUFDakRzRSxTQUFTO2dCQUNQO29CQUFFdEUsT0FBTztvQkFBUUMsT0FBT3hFLEVBQUU7Z0JBQWlCO2dCQUMzQztvQkFBRXVFLE9BQU87b0JBQU9DLE9BQU94RSxFQUFFO2dCQUFpQjthQUMzQztZQUNEeUksV0FBVztRQUNiO1FBQ0E7WUFDRUwsTUFBTTtZQUNONUQsT0FBT3hFLEVBQUU7WUFDVHVFLE9BQU90RSxXQUNIO2dCQUNFc0UsT0FBT3RFO2dCQUNQdUUsT0FDRVAsMEJBQTBCeUUsSUFBSSxDQUFDLENBQUNJLElBQU1BLEVBQUV2RSxLQUFLLEtBQUt0RSxXQUM5Q3VFLFNBQVN2RTtZQUNqQixJQUNBO1lBQ0pvSSxVQUFVLENBQUNDLEdBQUdNLE1BQVExSSxZQUFZMEksS0FBS3JFLFNBQVM7WUFDaERzRSxTQUFTNUU7WUFDVHdFLFdBQVc7UUFDYjtRQUNBO1lBQ0VMLE1BQU07WUFDTjVELE9BQU94RSxFQUFFO1lBQ1R1RSxPQUFPbEU7WUFDUGdJLFVBQVUsQ0FBQ1UsV0FBYXpJLGFBQWF5STtZQUNyQ04sV0FBVztRQUNiO1FBQ0E7WUFDRUwsTUFBTTtZQUNONUQsT0FBT3hFLEVBQUU7WUFDVHVFLE9BQU9oRTtZQUNQOEgsVUFBVSxDQUFDVSxXQUFhdkksZUFBZXVJO1lBQ3ZDTixXQUFXO1FBQ2I7S0FDRDtJQUVELE1BQU1PLGVBQWU7UUFDbkJwSSxhQUFhMkMsT0FBTyxDQUFDLGVBQWVuQztRQUNwQ1IsYUFBYTJDLE9BQU8sQ0FBQyxjQUFjekM7UUFDbkNTLG1CQUFtQjtZQUFFRyxNQUFNO1lBQUdDLFVBQVVMLGdCQUFnQkssUUFBUTtRQUFDO1FBQ2pFakIsVUFBVSxDQUFDdUksT0FBUyxDQUFDQTtJQUN2QjtJQUVBLHFCQUNFOzswQkFDRSw4REFBQ3pDO2dCQUFJSCxXQUFVOztrQ0FDYiw4REFBQ0U7d0JBQUVGLFdBQVU7OzRCQUNWckcsRUFBRTswQ0FDSCw4REFBQ2tKO2dDQUFLN0MsV0FBVTswQ0FDYnJELGFBQWFrQixNQUFNaUY7Ozs7Ozs7Ozs7OztrQ0FJeEIsOERBQUNsSyxtRUFBWUE7d0JBQ1hvSCxXQUFVO3dCQUNWK0MsTUFBTXBKLEVBQUU7d0JBQ1IrRyxNQUFNLENBQUMsQ0FBQyxFQUFFekgsbUVBQWlCQSxDQUFDMEgsT0FBTyxDQUFDQyxLQUFLLENBQUMsQ0FBQyxFQUFFNUgsNkRBQVdBLENBQUM2SCxLQUFLLENBQUNELEtBQUssQ0FBQyxDQUFDLEVBQUU1SCw2REFBV0EsQ0FBQ2dLLEdBQUcsQ0FBQ3BDLEtBQUssQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7MEJBSW5HLDhEQUFDVDtnQkFBSTFELElBQUc7Z0JBQVl1RCxXQUFVOzBCQUM1Qiw0RUFBQ0c7b0JBQUlILFdBQVcsQ0FBQyxhQUFhLEVBQUV6RSxTQUFTLFNBQVMsU0FBUyxDQUFDOztzQ0FDMUQsOERBQUM0RTs0QkFBSUgsV0FBVTtzQ0FDYiw0RUFBQzNILGlGQUFJQTtnQ0FBQ3VHLElBQUk7Z0NBQUNxRSxJQUFJOzBDQUNiLDRFQUFDN0oscUVBQWFBO29DQUNaMEksU0FBU0E7b0NBQ1RvQixVQUFVUDtvQ0FDVlEsU0FBU2xHO29DQUNUbUcsYUFBYXpKLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS3JCLDhEQUFDdEIsaUZBQUlBOzRCQUFDdUcsSUFBSTs0QkFBQ3FFLElBQUk7c0NBQ2IsNEVBQUM5QztnQ0FBSUMsT0FBTztvQ0FBRWMsUUFBUTtvQ0FBUVosT0FBTztnQ0FBTzswQ0FDMUMsNEVBQUNsSSx1REFBUUE7b0NBQ1BzRyxNQUFNQTtvQ0FDTlcsU0FBU0E7b0NBQ1RnRSxVQUFVO29DQUNWckQsV0FBVTtvQ0FDVnNELGdCQUFlO29DQUNmckksaUJBQWlCQTtvQ0FDakJzSSx5QkFBeUJoRjtvQ0FDekJpRixpQkFBaUI7d0NBQUM7d0NBQUc7d0NBQUk7cUNBQUc7b0NBQzVCQyxVQUFVOUcsYUFBYWtCLE1BQU1pRixpQkFBaUI7b0NBQzlDWSxVQUFVO29DQUNWQyx1QkFBdUI7b0NBQ3ZCQyx1QkFBdUI7d0NBQ3JCNUUsV0FBVyxDQUFDeEY7d0NBQ1pRLFdBQVcsQ0FBQ1I7d0NBQ1o0RixtQkFBbUIsQ0FBQzVGO3dDQUNwQmlCLFlBQVksQ0FBQ2pCO3dDQUNiRixVQUFVLENBQUNFO3dDQUNYcUssVUFBVSxDQUFDcks7b0NBQ2I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWhCO0dBM2dCTUg7O1FBQ1VQLG1HQUFRQTtRQUNMQyxtR0FBYUE7UUFDaEJaLHlEQUFjQTtRQW1IRk0sOERBQWdCQTtRQVN0QkQscUVBQXVCQTs7O0tBL0h2Q2E7QUE2Z0JOLCtEQUFlQSxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9mZWF0dXJlcy9ibG9nL2NvbXBvbmVudHMvTGlzdEFydGljbGVzLmpzeD9lZDNiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSBcInJlYWN0LWkxOG5leHRcIjtcclxuaW1wb3J0IHsgRGF0YUdyaWQgfSBmcm9tIFwiQG11aS94LWRhdGEtZ3JpZFwiO1xyXG5pbXBvcnQgeyBHcmlkIH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcclxuaW1wb3J0IEN1c3RvbVRvb2x0aXAgZnJvbSBcIkAvY29tcG9uZW50cy91aS9DdXN0b21Ub29sdGlwXCI7XHJcbmltcG9ydCBMb2FkaW5nIGZyb20gXCIuLi8uLi8uLi9jb21wb25lbnRzL2xvYWRpbmcvTG9hZGluZ1wiO1xyXG5pbXBvcnQgeyB1c2VHZXRBcnRpY2xlc0Rhc2hib2FyZCwgdXNlR2V0Q2F0ZWdvcmllcyB9IGZyb20gXCIuLi9ob29rcy9ibG9nLmhvb2tcIjtcclxuaW1wb3J0IFN2Z3ByZXZpZXcgZnJvbSBcIkAvYXNzZXRzL2ltYWdlcy9pY29ucy9wcmV2aWV3LWljb24uc3ZnXCI7XHJcbmltcG9ydCBTdmdlZGl0IGZyb20gXCJAL2Fzc2V0cy9pbWFnZXMvaWNvbnMvZWRpdC1pY29uLnN2Z1wiO1xyXG5pbXBvcnQgQ3VzdG9tQnV0dG9uIGZyb20gXCJAL2NvbXBvbmVudHMvdWkvQ3VzdG9tQnV0dG9uXCI7XHJcbmltcG9ydCBTdmdBcHBsaWNhdGlvbkljb24gZnJvbSBcIkAvYXNzZXRzL2ltYWdlcy9pY29ucy9hcHBsaWNhdGlvbkljb24uc3ZnXCI7XHJcbmltcG9ydCB7IHVzZVRoZW1lLCB1c2VNZWRpYVF1ZXJ5IH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcclxuaW1wb3J0IHtcclxuICBhZG1pblJvdXRlcyxcclxuICBiYXNlVXJsQmFja29mZmljZSxcclxuICB3ZWJzaXRlUm91dGVzTGlzdCxcclxufSBmcm9tIFwiQC9oZWxwZXJzL3JvdXRlc0xpc3RcIjtcclxuaW1wb3J0IHsgZm9ybWF0RGF0ZSB9IGZyb20gXCJAL3V0aWxzL2Z1bmN0aW9uc1wiO1xyXG5pbXBvcnQgQ3VzdG9tRmlsdGVycyBmcm9tIFwiQC9jb21wb25lbnRzL3VpL0N1c3RvbUZpbHRlcnNcIjtcclxuXHJcbmNvbnN0IExpc3RBcnRpY2xlcyA9ICh7IGxhbmd1YWdlIH0pID0+IHtcclxuICBjb25zdCB0aGVtZSA9IHVzZVRoZW1lKCk7XHJcbiAgY29uc3QgaXNNb2JpbGUgPSB1c2VNZWRpYVF1ZXJ5KHRoZW1lLmJyZWFrcG9pbnRzLmRvd24oXCJzbVwiKSk7XHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigpO1xyXG4gIGNvbnN0IFtjYXRlZ29yeSwgc2V0Q2F0ZWdvcnldID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW3NvcnRPcmRlciwgc2V0U29ydE9yZGVyXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtjcmVhdGVkQXQsIHNldENyZWF0ZWRBdF0gPSB1c2VTdGF0ZShudWxsKTtcclxuICBjb25zdCBbcHVibGlzaERhdGUsIHNldFB1Ymxpc2hEYXRlXSA9IHVzZVN0YXRlKG51bGwpO1xyXG4gIGNvbnN0IFtzZWFyY2gsIHNldFNlYXJjaF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3Qgc2F2ZWRWaXNpYmlsaXR5ID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJWaXNpYmlsaXR5XCIpO1xyXG4gIGNvbnN0IFt2aXNpYmlsaXR5LCBzZXRWaXNpYmlsaXR5XSA9IHVzZVN0YXRlKHNhdmVkVmlzaWJpbGl0eSB8fCBcIlwiKTtcclxuICBjb25zdCBbaXNBcmNoaXZlZCwgc2V0SXNBcmNoaXZlZEZpbHRlcl0gPSB1c2VTdGF0ZShmYWxzZSk7IC8vIERlZmF1bHQgdG8gc2hvdyBub24tYXJjaGl2ZWQgYXJ0aWNsZXNcclxuICBjb25zdCBzYXZlZFBhZ2luYXRpb24gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcIlBBR0lOQVRJT05fS0VZXCIpO1xyXG4gIGNvbnN0IHNhdmVkU2VhY2hWYWx1ZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiU2VhcmNoVmFsdWVcIik7XHJcbiAgY29uc3QgW3NlYXJjaFF1ZXJ5LCBzZXRTZWFyY2hRdWVyeV0gPSB1c2VTdGF0ZShzYXZlZFNlYWNoVmFsdWUgfHwgXCJcIik7XHJcbiAgY29uc3QgW3BhZ2luYXRpb25Nb2RlbCwgc2V0UGFnaW5hdGlvbk1vZGVsXSA9IFJlYWN0LnVzZVN0YXRlKFxyXG4gICAgc2F2ZWRQYWdpbmF0aW9uXHJcbiAgICAgID8gSlNPTi5wYXJzZShzYXZlZFBhZ2luYXRpb24pXHJcbiAgICAgIDoge1xyXG4gICAgICAgICAgcGFnZTogMCxcclxuICAgICAgICAgIHBhZ2VTaXplOiAxMCxcclxuICAgICAgICB9XHJcbiAgKTtcclxuXHJcbiAgY29uc3QgaXNPcGVuID0gdHJ1ZTtcclxuICBjb25zdCBbc2VsZWN0ZWRMYW5ndWFnZSwgc2V0U2VsZWN0ZWRMYW5ndWFnZV0gPSB1c2VTdGF0ZShcclxuICAgIGxhbmd1YWdlID8gbGFuZ3VhZ2UgOiBcImVuXCJcclxuICApO1xyXG5cclxuICAvLyBTdGF0ZSBmb3IgY29uZmlybWF0aW9uIGRpYWxvZ3NcclxuICBjb25zdCBbb3BlbkFyY2hpdmVEaWFsb2csIHNldE9wZW5BcmNoaXZlRGlhbG9nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbb3BlbkRlbGV0ZURpYWxvZywgc2V0T3BlbkRlbGV0ZURpYWxvZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3NlbGVjdGVkQXJ0aWNsZSwgc2V0U2VsZWN0ZWRBcnRpY2xlXSA9IHVzZVN0YXRlKG51bGwpO1xyXG5cclxuICAvLyBIb29rcyBmb3IgYXJjaGl2ZSBhbmQgZGVsZXRlIG9wZXJhdGlvbnNcclxuICBjb25zdCBhcmNoaXZlQXJ0aWNsZU11dGF0aW9uID0gdXNlYXJjaGl2ZWRhcnRpY2xlKCk7XHJcbiAgY29uc3QgZGVsZXRlQXJ0aWNsZU11dGF0aW9uID0gdXNlRGVsZXRlQXJ0aWNsZSgpO1xyXG4gIC8vIEhhbmRsZXIgZnVuY3Rpb25zIGZvciBhcmNoaXZlIGFuZCBkZWxldGVcclxuICBjb25zdCBoYW5kbGVBcmNoaXZlQ2xpY2sgPSAoYXJ0aWNsZSkgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWRBcnRpY2xlKGFydGljbGUpO1xyXG4gICAgc2V0T3BlbkFyY2hpdmVEaWFsb2codHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRGVsZXRlQ2xpY2sgPSAoYXJ0aWNsZSkgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWRBcnRpY2xlKGFydGljbGUpO1xyXG4gICAgc2V0T3BlbkRlbGV0ZURpYWxvZyh0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVBcmNoaXZlQ29uZmlybSA9IGFzeW5jICgpID0+IHtcclxuICAgIGlmIChzZWxlY3RlZEFydGljbGUpIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBhd2FpdCBhcmNoaXZlQXJ0aWNsZU11dGF0aW9uLm11dGF0ZUFzeW5jKHtcclxuICAgICAgICAgIGxhbmd1YWdlOiBzZWxlY3RlZExhbmd1YWdlLFxyXG4gICAgICAgICAgaWQ6IHNlbGVjdGVkQXJ0aWNsZS5pZCxcclxuICAgICAgICAgIGFyY2hpdmU6ICFzZWxlY3RlZEFydGljbGUuaXNBcmNoaXZlZCxcclxuICAgICAgICB9KTtcclxuICAgICAgICBnZXRBcnRpY2xlcy5yZWZldGNoKCk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGFyY2hpdmluZyBhcnRpY2xlOlwiLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIHNldE9wZW5BcmNoaXZlRGlhbG9nKGZhbHNlKTtcclxuICAgIHNldFNlbGVjdGVkQXJ0aWNsZShudWxsKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVEZWxldGVDb25maXJtID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKHNlbGVjdGVkQXJ0aWNsZSkge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGF3YWl0IGRlbGV0ZUFydGljbGVNdXRhdGlvbi5tdXRhdGVBc3luYyh7XHJcbiAgICAgICAgICBsYW5ndWFnZTogc2VsZWN0ZWRMYW5ndWFnZSxcclxuICAgICAgICAgIGlkOiBzZWxlY3RlZEFydGljbGUuaWQsXHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgZ2V0QXJ0aWNsZXMucmVmZXRjaCgpO1xyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBkZWxldGluZyBhcnRpY2xlOlwiLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIHNldE9wZW5EZWxldGVEaWFsb2coZmFsc2UpO1xyXG4gICAgc2V0U2VsZWN0ZWRBcnRpY2xlKG51bGwpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZURpYWxvZ0Nsb3NlID0gKCkgPT4ge1xyXG4gICAgc2V0T3BlbkFyY2hpdmVEaWFsb2coZmFsc2UpO1xyXG4gICAgc2V0T3BlbkRlbGV0ZURpYWxvZyhmYWxzZSk7XHJcbiAgICBzZXRTZWxlY3RlZEFydGljbGUobnVsbCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgcmVzZXRTZWFyY2ggPSAoKSA9PiB7XHJcbiAgICBzZXRDYXRlZ29yeShcIlwiKTtcclxuICAgIHNldFNlYXJjaFF1ZXJ5KFwiXCIpO1xyXG4gICAgc2V0VmlzaWJpbGl0eShcIlwiKTtcclxuICAgIHNldFNvcnRPcmRlcihcIlwiKTtcclxuICAgIHNldENyZWF0ZWRBdChudWxsKTtcclxuICAgIHNldFB1Ymxpc2hEYXRlKG51bGwpO1xyXG4gICAgc2V0U2VsZWN0ZWRMYW5ndWFnZShsYW5ndWFnZSA/IGxhbmd1YWdlIDogXCJlblwiKTtcclxuICAgIHNldFBhZ2luYXRpb25Nb2RlbCh7IHBhZ2U6IDAsIHBhZ2VTaXplOiAxMCB9KTtcclxuICAgIHNldElzQXJjaGl2ZWRGaWx0ZXIoZmFsc2UpOyAvLyBSZXNldCB0byBzaG93IG5vbi1hcmNoaXZlZCBhcnRpY2xlc1xyXG4gICAgc2V0U2VhcmNoKCFzZWFyY2gpO1xyXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJWaXNpYmlsaXR5XCIsIFwiXCIpO1xyXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJTZWFyY2hWYWx1ZVwiLCBcIlwiKTtcclxuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFxyXG4gICAgICBcIlBBR0lOQVRJT05fS0VZXCIsXHJcbiAgICAgIEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICBwYWdlOiAwLFxyXG4gICAgICAgIHBhZ2VTaXplOiAxMCxcclxuICAgICAgfSlcclxuICAgICk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgdHJ1bmNhdGVUaXRsZSA9ICh0aXRsZSkgPT4ge1xyXG4gICAgY29uc3Qgd29yZHMgPSB0aXRsZS5zcGxpdChcIiBcIik7XHJcbiAgICBpZiAod29yZHM/Lmxlbmd0aCA+IDQpIHtcclxuICAgICAgcmV0dXJuIHdvcmRzLnNsaWNlKDAsIDQpLmpvaW4oXCIgXCIpICsgXCIuLi5cIjtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHJldHVybiB0aXRsZTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBnZXRDYXRlZ29yaWVzTGFuZyA9IHVzZUdldENhdGVnb3JpZXMoc2VsZWN0ZWRMYW5ndWFnZSB8fCBcImVuXCIpO1xyXG5cclxuICBjb25zdCB0cmFuc2Zvcm1lZENhdGVnb3JpZXNMYW5nID1cclxuICAgIGdldENhdGVnb3JpZXNMYW5nPy5kYXRhPy5jYXRlZ29yaWVzPy5tYXAoKGNhdGVnb3J5KSA9PiAoe1xyXG4gICAgICBuYW1lOiBjYXRlZ29yeT8udmVyc2lvbnNjYXRlZ29yeVswXT8ubmFtZSxcclxuICAgICAgdmFsdWU6IGNhdGVnb3J5Py52ZXJzaW9uc2NhdGVnb3J5WzBdPy5uYW1lLFxyXG4gICAgICBsYWJlbDogY2F0ZWdvcnk/LnZlcnNpb25zY2F0ZWdvcnlbMF0/Lm5hbWUsXHJcbiAgICB9KSkgfHwgW107XHJcblxyXG4gIGNvbnN0IGdldEFydGljbGVzID0gdXNlR2V0QXJ0aWNsZXNEYXNoYm9hcmQoe1xyXG4gICAgbGFuZ3VhZ2U6IHNlbGVjdGVkTGFuZ3VhZ2UsXHJcbiAgICBwYWdlU2l6ZTogcGFnaW5hdGlvbk1vZGVsLnBhZ2VTaXplLFxyXG4gICAgcGFnZU51bWJlcjogcGFnaW5hdGlvbk1vZGVsLnBhZ2UgKyAxLFxyXG4gICAgc29ydE9yZGVyLFxyXG4gICAgc2VhcmNoUXVlcnksXHJcbiAgICB2aXNpYmlsaXR5LFxyXG4gICAgY3JlYXRlZEF0LFxyXG4gICAgaXNBcmNoaXZlZCxcclxuICAgIHB1Ymxpc2hEYXRlLFxyXG4gICAgY2F0ZWdvcnlOYW1lOiBjYXRlZ29yeSxcclxuICB9KTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHNldFNlbGVjdGVkTGFuZ3VhZ2UobGFuZ3VhZ2UpO1xyXG4gICAgZ2V0Q2F0ZWdvcmllc0xhbmcucmVmZXRjaCgpO1xyXG4gIH0sIFtsYW5ndWFnZV0pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgZ2V0QXJ0aWNsZXMucmVmZXRjaCgpO1xyXG4gIH0sIFtzZWFyY2gsIHBhZ2luYXRpb25Nb2RlbF0pO1xyXG5cclxuICBjb25zdCB2aXNpYmlsaXR5T3B0aW9uID0gW1xyXG4gICAgeyB2YWx1ZTogXCJQdWJsaWNcIiwgbGFiZWw6IFwiUHVibGljXCIgfSxcclxuICAgIHsgdmFsdWU6IFwiUHJpdmF0ZVwiLCBsYWJlbDogXCJQcml2YXRlXCIgfSxcclxuICAgIHsgdmFsdWU6IFwiRHJhZnRcIiwgbGFiZWw6IFwiRHJhZnRcIiB9LFxyXG4gIF07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVBhZ2luYXRpb25DaGFuZ2UgPSAobmV3UGFnaW5hdGlvbk1vZGVsKSA9PiB7XHJcbiAgICBzZXRQYWdpbmF0aW9uTW9kZWwobmV3UGFnaW5hdGlvbk1vZGVsKTtcclxuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwiUEFHSU5BVElPTl9LRVlcIiwgSlNPTi5zdHJpbmdpZnkobmV3UGFnaW5hdGlvbk1vZGVsKSk7XHJcbiAgfTtcclxuXHJcbiAgaWYgKGdldEFydGljbGVzLmlzTG9hZGluZykge1xyXG4gICAgcmV0dXJuIDxMb2FkaW5nIC8+O1xyXG4gIH1cclxuXHJcbiAgY29uc3Qgcm93cyA9XHJcbiAgICBnZXRBcnRpY2xlcz8uZGF0YT8uYXJ0aWNsZXM/Lm1hcCgoaXRlbSwgaW5kZXgpID0+ICh7XHJcbiAgICAgIGlkOiBpdGVtLl9pZCxcclxuICAgICAgdGl0bGU6IGl0ZW0/LnZlcnNpb25zPy5bMF0/LnRpdGxlXHJcbiAgICAgICAgPyB0cnVuY2F0ZVRpdGxlKGl0ZW0/LnZlcnNpb25zPy5bMF0/LnRpdGxlKVxyXG4gICAgICAgIDogXCJObyB0aXRsZVwiLFxyXG4gICAgICBjcmVhdGVkQnk6IGl0ZW0/LnZlcnNpb25zPy5bMF0/LmNyZWF0ZWRCeSB8fCBcIk4vQVwiLFxyXG4gICAgICBjcmVhdGVkQXQ6IGl0ZW0/LnZlcnNpb25zPy5bMF0/LmNyZWF0ZWRBdCxcclxuICAgICAgbGFuZ3VhZ2U6IGl0ZW0/LmV4aXN0aW5nTGFuZ3VhZ2VzPy5qb2luKFwiL1wiKSB8fCBcIk4vQVwiLFxyXG4gICAgICBhY3Rpb25zOiBpdGVtLl9pZCxcclxuICAgICAgdmlzaWJpbGl0eTogaXRlbT8udmVyc2lvbnM/LlswXT8udmlzaWJpbGl0eSB8fCBcIk4vQVwiLFxyXG4gICAgICB1cmw6IGl0ZW0/LnZlcnNpb25zPy5bMF0/LnVybCB8fCBcIk4vQVwiLFxyXG4gICAgICB0b3RhbENvbW1lbnRhaXJlczogaXRlbT8udG90YWxDb21tZW50YWlyZXMgfHwgXCIwXCIsXHJcbiAgICAgIGlzQXJjaGl2ZWQ6IGl0ZW0/LnZlcnNpb25zPy5bMF0/LmlzQXJjaGl2ZWQgfHwgZmFsc2UsXHJcbiAgICB9KSkgfHwgW107XHJcblxyXG4gIGNvbnN0IGNvbHVtbnMgPSBbXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcInRpdGxlXCIsXHJcbiAgICAgIGhlYWRlckNsYXNzTmFtZTogXCJkYXRhZ3JpZC1oZWFkZXJcIixcclxuICAgICAgY2VsbENsYXNzTmFtZTogXCJkYXRhZ3JpZC1jZWxsXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IHQoXCJsaXN0QXJ0aWNsZTp0aXRsZVwiKSxcclxuXHJcbiAgICAgIGZsZXg6IDEsXHJcbiAgICAgIHJlbmRlckNlbGw6IChwYXJhbXMpID0+IChcclxuICAgICAgICA8YVxyXG4gICAgICAgICAgaHJlZj17YC8ke3NlbGVjdGVkTGFuZ3VhZ2V9L2Jsb2cvJHtwYXJhbXMucm93Py51cmx9YH1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cImxpbmtcIlxyXG4gICAgICAgID5cclxuICAgICAgICAgIHtwYXJhbXMucm93LnRpdGxlfVxyXG4gICAgICAgIDwvYT5cclxuICAgICAgKSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcImNyZWF0ZWRCeVwiLFxyXG4gICAgICBoZWFkZXJDbGFzc05hbWU6IFwiZGF0YWdyaWQtaGVhZGVyXCIsXHJcbiAgICAgIGNlbGxDbGFzc05hbWU6IFwiZGF0YWdyaWQtY2VsbCBcIixcclxuICAgICAgaGVhZGVyTmFtZTogdChcImxpc3RBcnRpY2xlOmNyZWF0ZWRCeVwiKSxcclxuICAgICAgZmxleDogMC40LFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwiY3JlYXRlZEF0XCIsXHJcbiAgICAgIGhlYWRlckNsYXNzTmFtZTogXCJkYXRhZ3JpZC1oZWFkZXJcIixcclxuICAgICAgY2VsbENsYXNzTmFtZTogXCJkYXRhZ3JpZC1jZWxsIFwiLFxyXG4gICAgICBmbGV4OiAwLjQsXHJcbiAgICAgIGhlYWRlck5hbWU6IHQoXCJsaXN0QXJ0aWNsZTpjcmVhdGVkQXRcIiksXHJcbiAgICAgIHZhbHVlRm9ybWF0dGVyOiBmb3JtYXREYXRlLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwibGFuZ3VhZ2VcIixcclxuICAgICAgaGVhZGVyQ2xhc3NOYW1lOiBcImRhdGFncmlkLWhlYWRlclwiLFxyXG4gICAgICBjZWxsQ2xhc3NOYW1lOiBcImRhdGFncmlkLWNlbGwgXCIsXHJcbiAgICAgIGZsZXg6IDAuNCxcclxuICAgICAgaGVhZGVyTmFtZTogdChcImxpc3RvcHBvcnR1bml0eTphdmFpbGFibGVsYW5ndWFnZVwiKSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcInZpc2liaWxpdHlcIixcclxuICAgICAgaGVhZGVyQ2xhc3NOYW1lOiBcImRhdGFncmlkLWhlYWRlclwiLFxyXG4gICAgICBjZWxsQ2xhc3NOYW1lOiBcImRhdGFncmlkLWNlbGxcIixcclxuICAgICAgZmxleDogMC40LFxyXG4gICAgICBoZWFkZXJOYW1lOiB0KFwibGlzdEFydGljbGU6dmlzaWJpbGl0eVwiKSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcImlzQXJjaGl2ZWRcIixcclxuICAgICAgaGVhZGVyQ2xhc3NOYW1lOiBcImRhdGFncmlkLWhlYWRlclwiLFxyXG4gICAgICBjZWxsQ2xhc3NOYW1lOiBcImRhdGFncmlkLWNlbGxcIixcclxuICAgICAgZmxleDogMC40LFxyXG4gICAgICBoZWFkZXJOYW1lOiB0KFwibGlzdEFydGljbGU6YXJjaGl2ZWRcIiksXHJcbiAgICAgIHJlbmRlckNlbGw6IChwYXJhbXMpID0+IChcclxuICAgICAgICA8cD57cGFyYW1zLnJvdy5pc0FyY2hpdmVkID8gdChcImdsb2JhbDp5ZXNcIikgOiB0KFwiZ2xvYmFsOm5vXCIpfTwvcD5cclxuICAgICAgKSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcInRvdGFsQ29tbWVudGFpcmVzXCIsXHJcbiAgICAgIGhlYWRlckNsYXNzTmFtZTogXCJkYXRhZ3JpZC1oZWFkZXJcIixcclxuICAgICAgaGVhZGVyQ2xhc3NOYW1lOiBcImRhdGFncmlkLWhlYWRlclwiLFxyXG4gICAgICBjZWxsQ2xhc3NOYW1lOiBcImRhdGFncmlkLWNlbGwgXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IHQoXCJsaXN0QXJ0aWNsZTpuYk9mQ29tbWVudHNcIiksXHJcbiAgICAgIGZsZXg6IDAuNCxcclxuICAgIH0sXHJcblxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJhY3Rpb25zXCIsXHJcbiAgICAgIGNlbGxDbGFzc05hbWU6IFwiZGF0YWdyaWQtY2VsbFwiLFxyXG4gICAgICBoZWFkZXJDbGFzc05hbWU6IFwiZGF0YWdyaWQtaGVhZGVyXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiXCIsXHJcbiAgICAgIHJlbmRlckNlbGw6IChwYXJhbXMpID0+IChcclxuICAgICAgICA8ZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJhY3Rpb24tYnV0dG9uc1wiXHJcbiAgICAgICAgICBzdHlsZT17eyBncmlkQ29sdW1uOiBcInNwYW4gMlwiLCB3aWR0aDogXCIxMDAlXCIgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8Q3VzdG9tVG9vbHRpcFxyXG4gICAgICAgICAgICBjaGlsZD17XHJcbiAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgIDxDdXN0b21CdXR0b25cclxuICAgICAgICAgICAgICAgICAgaWNvbj17PFN2Z2VkaXQgLz59XHJcbiAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICBsaW5rPXtgLyR7YmFzZVVybEJhY2tvZmZpY2UuYmFzZVVSTC5yb3V0ZX0vJHthZG1pblJvdXRlcy5ibG9ncy5yb3V0ZX0vJHthZG1pblJvdXRlcy5lZGl0LnJvdXRlfS8ke3BhcmFtcy5yb3cuaWR9YH1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuIGJ0bi1naG9zdCBlZGl0LWJsb2dcIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB0aXRsZT17dChcImdsb2JhbDplZGl0XCIpfVxyXG4gICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICA8Q3VzdG9tVG9vbHRpcFxyXG4gICAgICAgICAgICBjaGlsZD17XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJidG4tZG93bmxvYWRcIj5cclxuICAgICAgICAgICAgICAgIDxDdXN0b21CdXR0b25cclxuICAgICAgICAgICAgICAgICAgaWNvbj17PFN2Z0FwcGxpY2F0aW9uSWNvbiAvPn1cclxuICAgICAgICAgICAgICAgICAgbGVmdEljb249e3RydWV9XHJcbiAgICAgICAgICAgICAgICAgIGxpbms9e2AvJHtiYXNlVXJsQmFja29mZmljZS5iYXNlVVJMLnJvdXRlfS8ke2FkbWluUm91dGVzLmJsb2dzLnJvdXRlfS8ke2FkbWluUm91dGVzLmNvbW1lbnRzLnJvdXRlfS8ke3BhcmFtcy5yb3cuaWR9YH1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuIGJ0bi1naG9zdCBlZGl0LWJsb2dcIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB0aXRsZT17dChcImdsb2JhbDpjb21tZW50c1wiKX1cclxuICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgPEN1c3RvbVRvb2x0aXBcclxuICAgICAgICAgICAgY2hpbGQ9e1xyXG4gICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICA8Q3VzdG9tQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIGljb249ezxTdmdwcmV2aWV3IC8+fVxyXG4gICAgICAgICAgICAgICAgICBsZWZ0SWNvbj17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgbGluaz17YC8ke3dlYnNpdGVSb3V0ZXNMaXN0LmJsb2cucm91dGV9LyR7cGFyYW1zLnJvdz8udXJsfWB9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0biBidG4tZ2hvc3QgZWRpdC1ibG9nXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgdGl0bGU9e3QoXCJnbG9iYWw6cHJldmlld1wiKX1cclxuICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgPEN1c3RvbVRvb2x0aXBcclxuICAgICAgICAgICAgY2hpbGQ9e1xyXG4gICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICA8Q3VzdG9tQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIGljb249e1xyXG4gICAgICAgICAgICAgICAgICAgIDxzdmdcclxuICAgICAgICAgICAgICAgICAgICAgIHdpZHRoPVwiMjJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PVwiMjBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyMiAyMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTIxIDE3QzIxIDE3LjUzMDQgMjAuNzg5MyAxOC4wMzkxIDIwLjQxNDIgMTguNDE0MkMyMC4wMzkxIDE4Ljc4OTMgMTkuNTMwNCAxOSAxOSAxOUgzQzIuNDY5NTcgMTkgMS45NjA4NiAxOC43ODkzIDEuNTg1NzkgMTguNDE0MkMxLjIxMDcxIDE4LjAzOTEgMSAxNy41MzA0IDEgMTdWM0MxIDIuNDY5NTcgMS4yMTA3MSAxLjk2MDg2IDEuNTg1NzkgMS41ODU3OUMxLjk2MDg2IDEuMjEwNzEgMi40Njk1NyAxIDMgMUg4TDEwIDRIMTlDMTkuNTMwNCA0IDIwLjAzOTEgNC4yMTA3MSAyMC40MTQyIDQuNTg1NzlDMjAuNzg5MyA0Ljk2MDg2IDIxIDUuNDY5NTcgMjEgNlYxN1pcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCIjMUUxRTFFXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVBcmNoaXZlQ2xpY2socGFyYW1zLnJvdyl9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0biBidG4tZ2hvc3QgZWRpdC1ibG9nXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgdGl0bGU9e1xyXG4gICAgICAgICAgICAgIHBhcmFtcy5yb3cuaXNBcmNoaXZlZFxyXG4gICAgICAgICAgICAgICAgPyB0KFwiZ2xvYmFsOnVuYXJjaGl2ZVwiKVxyXG4gICAgICAgICAgICAgICAgOiB0KFwiZ2xvYmFsOmFyY2hpdmVcIilcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICA8Q3VzdG9tVG9vbHRpcFxyXG4gICAgICAgICAgICBjaGlsZD17XHJcbiAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgIDxDdXN0b21CdXR0b25cclxuICAgICAgICAgICAgICAgICAgaWNvbj17XHJcbiAgICAgICAgICAgICAgICAgICAgPHN2Z1xyXG4gICAgICAgICAgICAgICAgICAgICAgd2lkdGg9XCIyMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9XCIyMlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDIwIDIyXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNMSA1SDNNMyA1SDE5TTMgNVYxOUMzIDE5LjUzMDQgMy4yMTA3MSAyMC4wMzkxIDMuNTg1NzkgMjAuNDE0MkMzLjk2MDg2IDIwLjc4OTMgNC40Njk1NyAyMSA1IDIxSDE1QzE1LjUzMDQgMjEgMTYuMDM5MSAyMC43ODkzIDE2LjQxNDIgMjAuNDE0MkMxNi43ODkzIDIwLjAzOTEgMTcgMTkuNTMwNCAxNyAxOVY1TTYgNVYzQzYgMi40Njk1NyA2LjIxMDcxIDEuOTYwODYgNi41ODU3OSAxLjU4NTc5QzYuOTYwODYgMS4yMTA3MSA3LjQ2OTU3IDEgOCAxSDEyQzEyLjUzMDQgMSAxMy4wMzkxIDEuMjEwNzEgMTMuNDE0MiAxLjU4NTc5QzEzLjc4OTMgMS45NjA4NiAxNCAyLjQ2OTU3IDE0IDNWNU04IDEwVjE2TTEyIDEwVjE2XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiIzFFMUUxRVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICBsZWZ0SWNvbj17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGVsZXRlQ2xpY2socGFyYW1zLnJvdyl9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0biBidG4tZ2hvc3QgZWRpdC1ibG9nXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgdGl0bGU9e3QoXCJnbG9iYWw6ZGVsZXRlXCIpfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKSxcclxuICAgICAgZmxleDogMSxcclxuICAgIH0sXHJcbiAgXTtcclxuICBjb25zdCBhcmNoaXZlZE9wdGlvbnMgPSBbXHJcbiAgICB7IHZhbHVlOiB0cnVlLCBsYWJlbDogXCJBcmNoaXZlZFwiIH0sXHJcbiAgICB7IHZhbHVlOiBmYWxzZSwgbGFiZWw6IFwiTm90IEFyY2hpdmVkXCIgfSxcclxuICBdO1xyXG5cclxuICBjb25zdCBmaWx0ZXJzID0gW1xyXG4gICAge1xyXG4gICAgICB0eXBlOiBcInRleHRcIixcclxuICAgICAgbGFiZWw6IFwiU2VhcmNoIEJ5IFRpdGxlXCIsXHJcbiAgICAgIHZhbHVlOiBzZWFyY2hRdWVyeSxcclxuICAgICAgb25DaGFuZ2U6IChlKSA9PiBzZXRTZWFyY2hRdWVyeShlLnRhcmdldC52YWx1ZSksXHJcbiAgICAgIHBsYWNlaG9sZGVyOiBcIlNlYXJjaFwiLFxyXG4gICAgICBjb25kaXRpb246IHRydWUsXHJcbiAgICB9LFxyXG5cclxuICAgIHtcclxuICAgICAgdHlwZTogXCJzZWxlY3RcIixcclxuICAgICAgbGFiZWw6IHQoXCJsaXN0QXJ0aWNsZTp2aXNpYmlsaXR5XCIpLFxyXG4gICAgICB2YWx1ZTogdmlzaWJpbGl0eVxyXG4gICAgICAgID8ge1xyXG4gICAgICAgICAgICB2YWx1ZTogdmlzaWJpbGl0eSxcclxuICAgICAgICAgICAgbGFiZWw6XHJcbiAgICAgICAgICAgICAgdmlzaWJpbGl0eU9wdGlvbi5maW5kKChvcHQpID0+IG9wdC52YWx1ZSA9PT0gdmlzaWJpbGl0eSk/LmxhYmVsIHx8XHJcbiAgICAgICAgICAgICAgdmlzaWJpbGl0eSxcclxuICAgICAgICAgIH1cclxuICAgICAgICA6IG51bGwsXHJcbiAgICAgIG9uQ2hhbmdlOiAoZSwgdmFsKSA9PiBzZXRWaXNpYmlsaXR5KHZhbD8udmFsdWUgfHwgXCJcIiksXHJcbiAgICAgIG9wdGlvbnM6IHZpc2liaWxpdHlPcHRpb24sXHJcbiAgICAgIGNvbmRpdGlvbjogdHJ1ZSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIHR5cGU6IFwic2VsZWN0XCIsXHJcbiAgICAgIGxhYmVsOiB0KFwibGlzdEFydGljbGU6YXJjaGl2YWdlXCIpLFxyXG4gICAgICB2YWx1ZTogaXNBcmNoaXZlZFxyXG4gICAgICAgID8ge1xyXG4gICAgICAgICAgICB2YWx1ZTogaXNBcmNoaXZlZCxcclxuICAgICAgICAgICAgbGFiZWw6XHJcbiAgICAgICAgICAgICAgYXJjaGl2ZWRPcHRpb25zLmZpbmQoKG9wdCkgPT4gb3B0LnZhbHVlID09PSBpc0FyY2hpdmVkKT8ubGFiZWwgfHxcclxuICAgICAgICAgICAgICBpc0FyY2hpdmVkLFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIDogbnVsbCxcclxuICAgICAgb25DaGFuZ2U6IChlLCB2YWwpID0+IHNldElzQXJjaGl2ZWRGaWx0ZXIodmFsPy52YWx1ZSB8fCBcIlwiKSxcclxuICAgICAgb3B0aW9uczogYXJjaGl2ZWRPcHRpb25zLFxyXG4gICAgICBjb25kaXRpb246IHRydWUsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB0eXBlOiBcInNlbGVjdFwiLFxyXG4gICAgICBsYWJlbDogdChcImdsb2JhbDpzb3J0XCIpLFxyXG4gICAgICB2YWx1ZTogc29ydE9yZGVyXHJcbiAgICAgICAgPyB7XHJcbiAgICAgICAgICAgIHZhbHVlOiBzb3J0T3JkZXIsXHJcbiAgICAgICAgICAgIGxhYmVsOiB0KHNvcnRPcmRlciA9PT0gXCJkZXNjXCIgPyBcImdsb2JhbDpuZXdlc3RcIiA6IFwiZ2xvYmFsOm9sZGVzdFwiKSxcclxuICAgICAgICAgIH1cclxuICAgICAgICA6IG51bGwsXHJcbiAgICAgIG9uQ2hhbmdlOiAoZSwgdmFsKSA9PiBzZXRTb3J0T3JkZXIodmFsPy52YWx1ZSB8fCBcIlwiKSxcclxuICAgICAgb3B0aW9uczogW1xyXG4gICAgICAgIHsgdmFsdWU6IFwiZGVzY1wiLCBsYWJlbDogdChcImdsb2JhbDpuZXdlc3RcIikgfSxcclxuICAgICAgICB7IHZhbHVlOiBcImFzY1wiLCBsYWJlbDogdChcImdsb2JhbDpvbGRlc3RcIikgfSxcclxuICAgICAgXSxcclxuICAgICAgY29uZGl0aW9uOiB0cnVlLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgdHlwZTogXCJzZWxlY3RcIixcclxuICAgICAgbGFiZWw6IHQoXCJsaXN0QXJ0aWNsZTpjYXRlZ29yeVwiKSxcclxuICAgICAgdmFsdWU6IGNhdGVnb3J5XHJcbiAgICAgICAgPyB7XHJcbiAgICAgICAgICAgIHZhbHVlOiBjYXRlZ29yeSxcclxuICAgICAgICAgICAgbGFiZWw6XHJcbiAgICAgICAgICAgICAgdHJhbnNmb3JtZWRDYXRlZ29yaWVzTGFuZy5maW5kKChjKSA9PiBjLnZhbHVlID09PSBjYXRlZ29yeSlcclxuICAgICAgICAgICAgICAgID8ubGFiZWwgfHwgY2F0ZWdvcnksXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgOiBudWxsLFxyXG4gICAgICBvbkNoYW5nZTogKGUsIHZhbCkgPT4gc2V0Q2F0ZWdvcnkodmFsPy52YWx1ZSB8fCBcIlwiKSxcclxuICAgICAgb3B0aW9uczogdHJhbnNmb3JtZWRDYXRlZ29yaWVzTGFuZyxcclxuICAgICAgY29uZGl0aW9uOiB0cnVlLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgdHlwZTogXCJkYXRlXCIsXHJcbiAgICAgIGxhYmVsOiB0KFwibGlzdEFydGljbGU6Y3JlYXRlZEF0XCIpLFxyXG4gICAgICB2YWx1ZTogY3JlYXRlZEF0LFxyXG4gICAgICBvbkNoYW5nZTogKG5ld1ZhbHVlKSA9PiBzZXRDcmVhdGVkQXQobmV3VmFsdWUpLFxyXG4gICAgICBjb25kaXRpb246IHRydWUsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB0eXBlOiBcImRhdGVcIixcclxuICAgICAgbGFiZWw6IHQoXCJsaXN0QXJ0aWNsZTpwdWJsaXNoRGF0ZVwiKSxcclxuICAgICAgdmFsdWU6IHB1Ymxpc2hEYXRlLFxyXG4gICAgICBvbkNoYW5nZTogKG5ld1ZhbHVlKSA9PiBzZXRQdWJsaXNoRGF0ZShuZXdWYWx1ZSksXHJcbiAgICAgIGNvbmRpdGlvbjogdHJ1ZSxcclxuICAgIH0sXHJcbiAgXTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU2VhcmNoID0gKCkgPT4ge1xyXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJTZWFyY2hWYWx1ZVwiLCBzZWFyY2hRdWVyeSk7XHJcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcIlZpc2liaWxpdHlcIiwgdmlzaWJpbGl0eSk7XHJcbiAgICBzZXRQYWdpbmF0aW9uTW9kZWwoeyBwYWdlOiAwLCBwYWdlU2l6ZTogcGFnaW5hdGlvbk1vZGVsLnBhZ2VTaXplIH0pO1xyXG4gICAgc2V0U2VhcmNoKChwcmV2KSA9PiAhcHJldik7XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGlzcGxheS1pbmxpbmVcIj5cclxuICAgICAgICA8cCBjbGFzc05hbWU9XCJoZWFkaW5nLWgyIHNlbWktYm9sZFwiPlxyXG4gICAgICAgICAge3QoXCJsaXN0QXJ0aWNsZTpsaXN0T2ZBcnRpY2xlc1wiKX1cclxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm9wcG9ydHVuaXRpZXMtbmJyXCI+XHJcbiAgICAgICAgICAgIHtnZXRBcnRpY2xlcz8uZGF0YT8udG90YWxBcnRpY2xlc31cclxuICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICA8L3A+XHJcblxyXG4gICAgICAgIDxDdXN0b21CdXR0b25cclxuICAgICAgICAgIGNsYXNzTmFtZT1cImJ0biBidG4tZmlsbGVkXCJcclxuICAgICAgICAgIHRleHQ9e3QoXCJnbG9iYWw6YWRkYXJ0aWNsZVwiKX1cclxuICAgICAgICAgIGxpbms9e2AvJHtiYXNlVXJsQmFja29mZmljZS5iYXNlVVJMLnJvdXRlfS8ke2FkbWluUm91dGVzLmJsb2dzLnJvdXRlfS8ke2FkbWluUm91dGVzLmFkZC5yb3V0ZX1gfVxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgPGRpdiBpZD1cImNvbnRhaW5lclwiIGNsYXNzTmFtZT1cInJlY2VudC1hcHBsaWNhdGlvbi1wZW50YWJlbGxcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YG1haW4tY29udGVudCAke2lzT3BlbiA/IFwib3BlblwiIDogXCJjbG9zZWRcIn1gfT5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFibGUtR3JpZFwiPlxyXG4gICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0+XHJcbiAgICAgICAgICAgICAgPEN1c3RvbUZpbHRlcnNcclxuICAgICAgICAgICAgICAgIGZpbHRlcnM9e2ZpbHRlcnN9XHJcbiAgICAgICAgICAgICAgICBvblNlYXJjaD17aGFuZGxlU2VhcmNofVxyXG4gICAgICAgICAgICAgICAgb25SZXNldD17cmVzZXRTZWFyY2h9XHJcbiAgICAgICAgICAgICAgICBzZWFyY2hMYWJlbD17dChcImdsb2JhbDpzZWFyY2hcIil9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9PlxyXG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGhlaWdodDogXCIxMDAlXCIsIHdpZHRoOiBcIjEwMCVcIiB9fT5cclxuICAgICAgICAgICAgICA8RGF0YUdyaWRcclxuICAgICAgICAgICAgICAgIHJvd3M9e3Jvd3N9XHJcbiAgICAgICAgICAgICAgICBjb2x1bW5zPXtjb2x1bW5zfVxyXG4gICAgICAgICAgICAgICAgcGFnaW5hdGlvblxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicGVudGFiZWxsLXRhYmxlXCJcclxuICAgICAgICAgICAgICAgIHBhZ2luYXRpb25Nb2RlPVwic2VydmVyXCJcclxuICAgICAgICAgICAgICAgIHBhZ2luYXRpb25Nb2RlbD17cGFnaW5hdGlvbk1vZGVsfVxyXG4gICAgICAgICAgICAgICAgb25QYWdpbmF0aW9uTW9kZWxDaGFuZ2U9e2hhbmRsZVBhZ2luYXRpb25DaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICBwYWdlU2l6ZU9wdGlvbnM9e1s1LCAxMCwgMjVdfVxyXG4gICAgICAgICAgICAgICAgcm93Q291bnQ9e2dldEFydGljbGVzPy5kYXRhPy50b3RhbEFydGljbGVzIHx8IDB9XHJcbiAgICAgICAgICAgICAgICBhdXRvSGVpZ2h0XHJcbiAgICAgICAgICAgICAgICBkaXNhYmxlU2VsZWN0aW9uT25DbGlja1xyXG4gICAgICAgICAgICAgICAgY29sdW1uVmlzaWJpbGl0eU1vZGVsPXt7XHJcbiAgICAgICAgICAgICAgICAgIGNyZWF0ZWRCeTogIWlzTW9iaWxlLFxyXG4gICAgICAgICAgICAgICAgICBjcmVhdGVkQXQ6ICFpc01vYmlsZSxcclxuICAgICAgICAgICAgICAgICAgdG90YWxDb21tZW50YWlyZXM6ICFpc01vYmlsZSxcclxuICAgICAgICAgICAgICAgICAgdmlzaWJpbGl0eTogIWlzTW9iaWxlLFxyXG4gICAgICAgICAgICAgICAgICBsYW5ndWFnZTogIWlzTW9iaWxlLFxyXG4gICAgICAgICAgICAgICAgICBhcmNoaXZlZDogIWlzTW9iaWxlLFxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvR3JpZD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8Lz5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgTGlzdEFydGljbGVzO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVRyYW5zbGF0aW9uIiwiRGF0YUdyaWQiLCJHcmlkIiwiQ3VzdG9tVG9vbHRpcCIsIkxvYWRpbmciLCJ1c2VHZXRBcnRpY2xlc0Rhc2hib2FyZCIsInVzZUdldENhdGVnb3JpZXMiLCJTdmdwcmV2aWV3IiwiU3ZnZWRpdCIsIkN1c3RvbUJ1dHRvbiIsIlN2Z0FwcGxpY2F0aW9uSWNvbiIsInVzZVRoZW1lIiwidXNlTWVkaWFRdWVyeSIsImFkbWluUm91dGVzIiwiYmFzZVVybEJhY2tvZmZpY2UiLCJ3ZWJzaXRlUm91dGVzTGlzdCIsImZvcm1hdERhdGUiLCJDdXN0b21GaWx0ZXJzIiwiTGlzdEFydGljbGVzIiwibGFuZ3VhZ2UiLCJ0aGVtZSIsImlzTW9iaWxlIiwiYnJlYWtwb2ludHMiLCJkb3duIiwidCIsImNhdGVnb3J5Iiwic2V0Q2F0ZWdvcnkiLCJzb3J0T3JkZXIiLCJzZXRTb3J0T3JkZXIiLCJjcmVhdGVkQXQiLCJzZXRDcmVhdGVkQXQiLCJwdWJsaXNoRGF0ZSIsInNldFB1Ymxpc2hEYXRlIiwic2VhcmNoIiwic2V0U2VhcmNoIiwic2F2ZWRWaXNpYmlsaXR5IiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInZpc2liaWxpdHkiLCJzZXRWaXNpYmlsaXR5IiwiaXNBcmNoaXZlZCIsInNldElzQXJjaGl2ZWRGaWx0ZXIiLCJzYXZlZFBhZ2luYXRpb24iLCJzYXZlZFNlYWNoVmFsdWUiLCJzZWFyY2hRdWVyeSIsInNldFNlYXJjaFF1ZXJ5IiwicGFnaW5hdGlvbk1vZGVsIiwic2V0UGFnaW5hdGlvbk1vZGVsIiwiSlNPTiIsInBhcnNlIiwicGFnZSIsInBhZ2VTaXplIiwiaXNPcGVuIiwic2VsZWN0ZWRMYW5ndWFnZSIsInNldFNlbGVjdGVkTGFuZ3VhZ2UiLCJvcGVuQXJjaGl2ZURpYWxvZyIsInNldE9wZW5BcmNoaXZlRGlhbG9nIiwib3BlbkRlbGV0ZURpYWxvZyIsInNldE9wZW5EZWxldGVEaWFsb2ciLCJzZWxlY3RlZEFydGljbGUiLCJzZXRTZWxlY3RlZEFydGljbGUiLCJhcmNoaXZlQXJ0aWNsZU11dGF0aW9uIiwidXNlYXJjaGl2ZWRhcnRpY2xlIiwiZGVsZXRlQXJ0aWNsZU11dGF0aW9uIiwidXNlRGVsZXRlQXJ0aWNsZSIsImhhbmRsZUFyY2hpdmVDbGljayIsImFydGljbGUiLCJoYW5kbGVEZWxldGVDbGljayIsImhhbmRsZUFyY2hpdmVDb25maXJtIiwibXV0YXRlQXN5bmMiLCJpZCIsImFyY2hpdmUiLCJnZXRBcnRpY2xlcyIsInJlZmV0Y2giLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVEZWxldGVDb25maXJtIiwiaGFuZGxlRGlhbG9nQ2xvc2UiLCJyZXNldFNlYXJjaCIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJ0cnVuY2F0ZVRpdGxlIiwidGl0bGUiLCJ3b3JkcyIsInNwbGl0IiwibGVuZ3RoIiwic2xpY2UiLCJqb2luIiwiZ2V0Q2F0ZWdvcmllc0xhbmciLCJ0cmFuc2Zvcm1lZENhdGVnb3JpZXNMYW5nIiwiZGF0YSIsImNhdGVnb3JpZXMiLCJtYXAiLCJuYW1lIiwidmVyc2lvbnNjYXRlZ29yeSIsInZhbHVlIiwibGFiZWwiLCJwYWdlTnVtYmVyIiwiY2F0ZWdvcnlOYW1lIiwidmlzaWJpbGl0eU9wdGlvbiIsImhhbmRsZVBhZ2luYXRpb25DaGFuZ2UiLCJuZXdQYWdpbmF0aW9uTW9kZWwiLCJpc0xvYWRpbmciLCJyb3dzIiwiYXJ0aWNsZXMiLCJpdGVtIiwiaW5kZXgiLCJfaWQiLCJ2ZXJzaW9ucyIsImNyZWF0ZWRCeSIsImV4aXN0aW5nTGFuZ3VhZ2VzIiwiYWN0aW9ucyIsInVybCIsInRvdGFsQ29tbWVudGFpcmVzIiwiY29sdW1ucyIsImZpZWxkIiwiaGVhZGVyQ2xhc3NOYW1lIiwiY2VsbENsYXNzTmFtZSIsImhlYWRlck5hbWUiLCJmbGV4IiwicmVuZGVyQ2VsbCIsInBhcmFtcyIsImEiLCJocmVmIiwicm93IiwiY2xhc3NOYW1lIiwidmFsdWVGb3JtYXR0ZXIiLCJwIiwiZGl2Iiwic3R5bGUiLCJncmlkQ29sdW1uIiwid2lkdGgiLCJjaGlsZCIsImljb24iLCJsZWZ0SWNvbiIsImxpbmsiLCJiYXNlVVJMIiwicm91dGUiLCJibG9ncyIsImVkaXQiLCJjb21tZW50cyIsImJsb2ciLCJzdmciLCJoZWlnaHQiLCJ2aWV3Qm94IiwiZmlsbCIsInhtbG5zIiwicGF0aCIsImQiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsIm9uQ2xpY2siLCJhcmNoaXZlZE9wdGlvbnMiLCJmaWx0ZXJzIiwidHlwZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwiY29uZGl0aW9uIiwiZmluZCIsIm9wdCIsInZhbCIsIm9wdGlvbnMiLCJjIiwibmV3VmFsdWUiLCJoYW5kbGVTZWFyY2giLCJwcmV2Iiwic3BhbiIsInRvdGFsQXJ0aWNsZXMiLCJ0ZXh0IiwiYWRkIiwieHMiLCJvblNlYXJjaCIsIm9uUmVzZXQiLCJzZWFyY2hMYWJlbCIsInBhZ2luYXRpb24iLCJwYWdpbmF0aW9uTW9kZSIsIm9uUGFnaW5hdGlvbk1vZGVsQ2hhbmdlIiwicGFnZVNpemVPcHRpb25zIiwicm93Q291bnQiLCJhdXRvSGVpZ2h0IiwiZGlzYWJsZVNlbGVjdGlvbk9uQ2xpY2siLCJjb2x1bW5WaXNpYmlsaXR5TW9kZWwiLCJhcmNoaXZlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/ListArticles.jsx\n"));

/***/ })

});