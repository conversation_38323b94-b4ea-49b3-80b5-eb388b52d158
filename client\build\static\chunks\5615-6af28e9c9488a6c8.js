(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5615],{14485:function(e,t,a){var n={"./ckb.js":9706,"./cs.js":79089,"./da.js":83138,"./de.js":80043,"./en.js":96775,"./es.js":61288,"./fa.js":42058,"./fr.js":23147,"./he.js":38210,"./hu.js":10200,"./index.js":47905,"./it.js":47758,"./ja.js":36473,"./ko.js":7747,"./lv.js":17242,"./nl.js":16833,"./pl.js":37370,"./pt_br.js":86548,"./ro.js":20319,"./ru.js":659,"./se.js":9296,"./tr.js":44584,"./ua.js":34699,"./ur.js":9870,"./zh_cn.js":23428};function s(e){return a(r(e))}function r(e){if(!a.o(n,e)){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}s.keys=function(){return Object.keys(n)},s.resolve=r,e.exports=s,s.id=14485},93214:function(e,t,a){"use strict";a.d(t,{cU:function(){return l},xk:function(){return i},yX:function(){return r}});var n=a(83464),s=a(40257);let r=n.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),i=n.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),l=n.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"});n.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))},17828:function(e,t,a){"use strict";a.d(t,{Z:function(){return c}});var n=a(83464),s=a(40257),r=n.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0});n.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},withCredentials:!0}),n.Z.create({baseURL:s.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e));var i=a(40257);let l=()=>new Promise(async(e,t)=>{try{let t=await r.get(`${i.env.NEXT_PUBLIC_BASE_API_URL}/account`);e(t.data)}catch(e){t(e)}});var o=a(86484);function c(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],{data:t,error:a,isLoading:n,refetch:s}=(0,o.useQuery)({queryKey:["currentUser"],queryFn:l,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1,retry:e});return{user:t,error:a,isLoading:n,refetch:s}}},66849:function(e,t,a){"use strict";var n=a(57437),s=a(63993),r=a(2265),i=a(55788),l=a(17382),o=a.n(l),c=a(61053),d=a(28397);a(21005),a(46172),a(19841),a(42623);var u=a(74269),m=a(89126),p=a(64393),h=a(77584),f=a(33833),v=a(42187),g=a(69250),x=a(25694),b=a(3989),N=a(5100),j=a(71096),y=a.n(j),E=a(45097),A=a(62953),F=a(49360),R=a(17828),w=a(40257);t.Z=function(e){let{errors:t,touched:a,setFieldValue:l,values:j}=e,[C,Z]=(0,r.useState)([]),[T,B]=(0,r.useState)([]),{t:D}=(0,i.$G)(),[P,S]=(0,r.useState)(!1),[_,U]=(0,r.useState)(new Date);(0,r.useRef)(null);let[$,L]=(0,r.useState)(null),{user:k}=(0,R.Z)(),I=(0,A.jd)();(0,F.Z)().replace(/-/g,"");let M=async(e,t,a,n)=>{if(e instanceof HTMLImageElement){let t=e.src;if(t.startsWith("data:image")){let s=t.split(",")[1],r=t.match(/data:(.*?);base64/)[1],i=atob(s),l=new File([new Blob([new Uint8Array(Array(i.length).fill(0).map((e,t)=>i.charCodeAt(t)))],{type:r})],`image_${Date.now()}.${r.split("/")[1]}`,{type:r});await Y(l,n,a,e)}else fetch(t).then(e=>e.blob()).then(t=>{let s=t.type;Y(new File([t],`image_${Date.now()}.${s.split("/")[1]}`,{type:s}),n,a,e)}).catch(e=>console.error("Error converting image URL to Blob:",e))}else console.error("File is not an HTMLImageElement.")},Y=(e,t,a,n)=>{let s;s=(0,F.Z)().replace(/-/g,"");let r=new FormData;r.append("file",e);let i=e.name.split(".").pop(),l=new Date().getFullYear();I.mutate({resource:"blogs",folder:l.toString(),filename:s,body:{formData:r,t:D}},{onSuccess:e=>{let a="uuid exist"===e.message?e.uuid:`${s}.${i}`,r=`${w.env.NEXT_PUBLIC_BASE_API_URL}/files/${a}`;n.src=r,t({result:[{id:a,url:r}]})},onError:e=>{console.error("Error uploading file:",e)}})};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{className:"label-pentabell",children:"Add Event English : "}),(0,n.jsxs)("div",{className:"inline-group",children:[(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(m.Z,{children:(0,n.jsxs)(p.Z,{className:"label-form",children:[D("createArticle:title"),(0,n.jsx)(h.Z,{variant:"standard",name:"titleEN",type:"text",value:j.titleEN,onChange:e=>{let t=e.target.value;l("titleEN",t),l("urlEN",(0,u.o)(t))},className:"input-pentabell"+(t.titleEN&&a.titleEN?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"titleEN",component:"div"})]})})]}),(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(m.Z,{children:(0,n.jsxs)(p.Z,{className:"label-form",children:[D("eventForm:subTitle"),(0,n.jsx)(h.Z,{variant:"standard",name:"subTitleEN",type:"text",value:j.subTitleEN,onChange:e=>{let t=e.target.value;l("subTitleEN",t),(0,u.o)(t)},className:"input-pentabell"+(t.subTitleEN&&a.subTitleEN?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"subTitleEN",component:"div"})]})})]})]}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsx)("div",{children:(0,n.jsx)(m.Z,{children:(0,n.jsxs)(p.Z,{className:"label-form",children:[D("BreadCrumbs"),(0,n.jsx)(h.Z,{variant:"standard",name:"nameEN",type:"text",value:j.nameEN,onChange:e=>{e.target.value,l("nameEN",nameEN),(0,u.o)(nameEN)},className:"input-pentabell"+(t.nameEN&&a.nameEN?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"nameEN",component:"div"})]})})})}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsx)("div",{children:(0,n.jsx)(m.Z,{children:(0,n.jsxs)(p.Z,{className:"label-form",children:["Country Concerned",(0,n.jsx)(h.Z,{variant:"standard",name:"countryConcernedEN",type:"text",value:j.countryConcernedEN,onChange:e=>{l("countryConcernedEN",e.target.value)},className:"textArea-pentabell"+(t.countryConcernedEN&&a.countryConcernedEN?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"countryConcernedEN",component:"div"})]})})})}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsx)("div",{children:(0,n.jsx)(m.Z,{children:(0,n.jsxs)(p.Z,{className:"label-form",children:["Sector",(0,n.jsx)(h.Z,{variant:"standard",name:"sectorEN",type:"text",value:j.sectorEN,onChange:e=>{l("sectorEN",e.target.value)},className:"textArea-pentabell"+(t.sectorEN&&a.sectorEN?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"sectorEN",component:"div"})]})})})}),(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(m.Z,{children:(0,n.jsxs)(p.Z,{className:"label-form",children:[D("createArticle:Organiser"),(0,n.jsx)(h.Z,{variant:"standard",name:"organiserEN",type:"text",value:j.organiserEN,onChange:e=>{l("organiserEN",e.target.value)},className:"input-pentabell"+(t.organiserEN&&a.organiserEN?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"organiserEN",component:"div"})]})})]}),(0,n.jsx)(o(),{setContents:j?.contentEN||"",onChange:e=>{l("contentEN",e)},onPaste:(e,t,a)=>t.replace(/<strong>(.*?)$/g,"<strong>$1</strong>"),setOptions:{cleanHTML:!1,disableHtmlSanitizer:!0,addTagsWhitelist:"h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button",plugins:c.default,buttonList:[["undo","redo"],["font","fontSize","formatBlock"],["bold","underline","italic","strike","subscript","superscript"],["fontColor","hiliteColor"],["align","list","lineHeight"],["outdent","indent"],["table","horizontalRule","link","image","video"],["fullScreen","showBlocks","codeView"],["preview","print"],["removeFormat"]],imageUploadHandler:M,defaultTag:"div",minHeight:"300px",maxHeight:"400px",showPathLabel:!1,font:["Proxima-Nova-Regular","Proxima-Nova-Medium","Proxima-Nova-Semibold","Proxima-Nova-Bold","Proxima-Nova-Extrabold","Proxima-Nova-Black","Proxima-Nova-Light","Proxima-Nova-Thin","Arial","Times New Roman","Sans-Serif"],charCounter:!0,charCounterType:"byte",resizingBar:!1,colorList:[["#234791","#d69b19","#cc3233","#009966","#0b3051","#2BBFAD","#0b305100","#0a305214","#743794","#ff0000","#ff5e00","#ffe400","#abf200","#00d8ff","#0055ff","#6600ff","#ff00dd","#000000","#ffd8d8","#fae0d4","#faf4c0","#e4f7ba","#d4f4fa","#d9e5ff","#e8d9ff","#ffd9fa","#f1f1f1","#ffa7a7","#ffc19e","#faed7d","#cef279","#b2ebf4","#b2ccff","#d1b2ff","#ffb2f5","#bdbdbd","#f15f5f","#f29661","#e5d85c","#bce55c","#5cd1e5","#6699ff","#a366ff","#f261df","#8c8c8c","#980000","#993800","#998a00","#6b9900","#008299","#003399","#3d0099","#990085","#353535","#670000","#662500","#665c00","#476600","#005766","#002266","#290066","#660058","#222222"]]},onImageUpload:M}),(0,n.jsx)("br",{}),(0,n.jsxs)("div",{className:"inline-group",children:[(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(m.Z,{children:(0,n.jsxs)(p.Z,{className:"label-form",children:[D("createArticle:metaTitle")," ("," ",(0,n.jsxs)("span",{className:j.metaTitleEN?.length>65?" text-danger":"",children:[" ",j.metaTitleEN?.length," / 65"," "]})," ",")",(0,n.jsx)(h.Z,{variant:"standard",name:"metaTitleEN",type:"text",value:j.metaTitleEN,onChange:e=>{l("metaTitleEN",e.target.value)},className:"input-pentabell"+(t.metaTitleEN&&a.metaTitleEN?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"metaTitleEN",component:"div"})]})})]}),(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(m.Z,{children:(0,n.jsxs)(p.Z,{className:"label-form",children:[D("createArticle:url"),(0,n.jsx)(h.Z,{variant:"standard",name:"urlEN",type:"text",value:j.urlEN,onChange:e=>{l("urlEN",e.target.value)},className:"input-pentabell"+(t.urlEN&&a.urlEN?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"urlEN",component:"div"})]})})]})]}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsx)("div",{children:(0,n.jsx)(m.Z,{children:(0,n.jsxs)(p.Z,{className:"label-form",children:[D("createArticle:metaDescription")," ("," ",(0,n.jsxs)("span",{className:j.metaDescriptionEN?.length>160?" text-danger":"",children:[j.metaDescriptionEN?.length," / 160"]})," ",")",(0,n.jsx)(h.Z,{variant:"standard",name:"metaDescriptionEN",type:"text",multiline:!0,rows:3,value:j.metaDescriptionEN,onChange:e=>{l("metaDescriptionEN",e.target.value)},className:"textArea-pentabell"+(t.metaDescriptionEN&&a.metaDescriptionEN?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"metaDescriptionEN",component:"div"})]})})})}),(0,n.jsxs)("div",{className:"inline-group",children:[(0,n.jsx)("div",{children:(0,n.jsx)(m.Z,{children:(0,n.jsxs)(p.Z,{className:"label-form",children:[D("createArticle:alt"),(0,n.jsx)(h.Z,{variant:"standard",name:"altEN",type:"text",value:j.altEN,onChange:e=>{l("altEN",e.target.value)},className:"input-pentabell"+(t.altEN&&a.altEN?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"altEN",component:"div"})]})})}),(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(m.Z,{children:(0,n.jsxs)(p.Z,{className:"label-form",children:[D("createArticle:visibility"),(0,n.jsx)(f.Z,{className:"select-pentabell",variant:"standard",value:d.EE.filter(e=>j.visibilityEN===e),selected:j.visibilityEN,onChange:e=>{l("visibilityEN",e.target.value)},children:d.EE.map((e,t)=>(0,n.jsx)(v.Z,{value:e,children:e},t))}),(0,n.jsx)(s.Bc,{className:"label-error",name:"visibilityEN",component:"div"})]})})]}),(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(m.Z,{children:(0,n.jsxs)(p.Z,{className:"label-form",children:[D("createArticle:keyword"),(0,n.jsx)("div",{id:"tags",children:(0,n.jsx)(E.V,{tags:C,className:"input-pentabell"+(t.keywordsEN&&a.keywordsEN?" is-invalid":""),delimiters:[188,13],handleDelete:e=>{let t=C.filter((t,a)=>a!==e);Z(t),l("keywordsEN",t.map(e=>e.text))},handleAddition:e=>{Z([...C,e]),l("keywordsEN",[...C,e].map(e=>e.text))},inputFieldPosition:"bottom",autocomplete:!0,allowDragDrop:!1})}),(0,n.jsx)(s.Bc,{className:"label-error",name:"keywordsEN",component:"div"})]})})]})]}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"label-form",children:[(0,n.jsx)(s.gN,{type:"checkbox",name:"publishNow",checked:P,onChange:e=>{S(e.target.checked),e.target.checked&&l("publishDateEN",new Date().toISOString())}}),D("createArticle:publishNow")]}),!P&&(0,n.jsx)("div",{children:(0,n.jsxs)(m.Z,{children:[(0,n.jsxs)(p.Z,{className:"label-form",children:[D("createArticle:publishDate"),(0,n.jsx)(g._,{dateAdapter:b.y,children:(0,n.jsxs)(N.C,{components:["DatePicker"],children:[(0,n.jsx)(x.M,{variant:"standard",className:"input-date",format:"DD/MM/YYYY",value:y()(j.publishDateEN||new Date),onChange:e=>{l("publishDateEN",y()(e).format("YYYY-MM-DD"))}})," "]})})]})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"publishDateEN",component:"div"})]})})]})}),(0,n.jsx)(s.gN,{type:"hidden",name:"publishDateEN",value:P?new Date().toISOString():_.toISOString()})]})}},7456:function(e,t,a){"use strict";var n=a(57437),s=a(63993),r=a(2265),i=a(55788),l=a(17382),o=a.n(l),c=a(49360),d=a(28397);a(21005);var u=a(74269);a(46172),a(19841),a(42623);var m=a(45097),p=a(61053),h=a(89126),f=a(64393),v=a(77584),g=a(33833),x=a(42187),b=a(69250),N=a(25694),j=a(5100),y=a(71096),E=a.n(y),A=a(3989),F=a(62953),R=a(17828),w=a(40257);t.Z=function(e){let{errors:t,touched:a,setFieldValue:l,values:y,onImageSelect:C,categories:Z,filteredCategories:T,onCategoriesSelect:B}=e,[D,P]=(0,r.useState)([]),{user:S}=(0,R.Z)(),{t:_}=(0,i.$G)(),[U,$]=(0,r.useState)(!1),[L,k]=(0,r.useState)(new Date);(0,r.useRef)(null);let[I,M]=(0,r.useState)(null),Y=(0,F.jd)(),O=async(e,t,a,n)=>{if(e instanceof HTMLImageElement){let t=e.src;if(t.startsWith("data:image")){let s=t.split(",")[1],r=t.match(/data:(.*?);base64/)[1],i=atob(s),l=new File([new Blob([new Uint8Array(Array(i.length).fill(0).map((e,t)=>i.charCodeAt(t)))],{type:r})],`image_${Date.now()}.${r.split("/")[1]}`,{type:r});await H(l,n,a,e)}else fetch(t).then(e=>e.blob()).then(t=>{let s=t.type;H(new File([t],`image_${Date.now()}.${s.split("/")[1]}`,{type:s}),n,a,e)}).catch(e=>console.error("Error converting image URL to Blob:",e))}else console.error("File is not an HTMLImageElement.")},H=(e,t,a,n)=>{let s;s=(0,c.Z)().replace(/-/g,"");let r=new FormData;r.append("file",e);let i=e.name.split(".").pop(),l=new Date().getFullYear();Y.mutate({resource:"blogs",folder:l.toString(),filename:s,body:{formData:r,t:_}},{onSuccess:e=>{let a="uuid exist"===e.message?e.uuid:`${s}.${i}`,r=`${w.env.NEXT_PUBLIC_BASE_API_URL}/files/${a}`;n.src=r,t({result:[{id:a,url:r}]})},onError:e=>{console.error("Error uploading file:",e)}})};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{className:"label-pentabell",children:"Add Event French : "}),(0,n.jsxs)("div",{className:"inline-group",children:[(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(h.Z,{children:(0,n.jsxs)(f.Z,{className:"label-form",children:[_("createArticle:title"),(0,n.jsx)(v.Z,{variant:"standard",name:"titleFR",type:"text",value:y.titleFR,onChange:e=>{let t=e.target.value;l("titleFR",t),l("urlEN",(0,u.o)(t))},className:"input-pentabell"+(t.titleFR&&a.titleFR?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"titleFR",component:"div"})]})})]}),(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(h.Z,{children:(0,n.jsxs)(f.Z,{className:"label-form",children:[_("eventForm:subTitle"),(0,n.jsx)(v.Z,{variant:"standard",name:"subTitleFR",type:"text",value:y.subTitleFR,onChange:e=>{let t=e.target.value;l("subTitleFR",t),(0,u.o)(t)},className:"input-pentabell"+(t.subTitleFR&&a.subTitleFR?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"subTitleFR",component:"div"})]})})]})]}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsx)("div",{children:(0,n.jsx)(h.Z,{children:(0,n.jsxs)(f.Z,{className:"label-form",children:[_("BreadCrumbs"),(0,n.jsx)(v.Z,{variant:"standard",name:"nameFR",type:"text",value:y.nameFR,onChange:e=>{let t=e.target.value;l("nameFR",t),(0,u.o)(t)},className:"input-pentabell"+(t.nameFR&&a.nameFR?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"nameFR",component:"div"})]})})})}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsx)("div",{children:(0,n.jsx)(h.Z,{children:(0,n.jsxs)(f.Z,{className:"label-form",children:["Country Concerned",(0,n.jsx)(v.Z,{variant:"standard",name:"countryConcernedFR",type:"text",value:y.countryConcernedFR,onChange:e=>{l("countryConcernedFR",e.target.value)},className:"textArea-pentabell"+(t.countryConcernedFR&&a.countryConcernedFR?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"countryConcernedFR",component:"div"})]})})})}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsx)("div",{children:(0,n.jsx)(h.Z,{children:(0,n.jsxs)(f.Z,{className:"label-form",children:["Sector",(0,n.jsx)(v.Z,{variant:"standard",name:"sectorFR",type:"text",value:y.sectorFR,onChange:e=>{l("sectorFR",e.target.value)},className:"textArea-pentabell"+(t.sectorFR&&a.sectorFR?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"sectorFR",component:"div"})]})})})}),(0,n.jsxs)("div",{children:[" ",(0,n.jsx)(h.Z,{children:(0,n.jsxs)(f.Z,{className:"label-form",children:[_("createArticle:Organiser"),(0,n.jsx)(v.Z,{variant:"standard",name:"organiserFR",type:"text",value:y.organiserFR,onChange:e=>{l("organiserFR",e.target.value)},className:"input-pentabell"+(t.organiserFR&&a.organiserFR?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"organiserFR",component:"div"})]})})]}),(0,n.jsx)(o(),{setContents:y?.contentFR||"",onChange:e=>{l("contentFR",e)},onPaste:(e,t,a)=>t.replace(/<strong>(.*?)$/g,"<strong>$1</strong>"),setOptions:{cleanHTML:!1,disableHtmlSanitizer:!0,addTagsWhitelist:"h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button",plugins:p.default,buttonList:[["undo","redo"],["font","fontSize","formatBlock"],["bold","underline","italic","strike","subscript","superscript"],["fontColor","hiliteColor"],["align","list","lineHeight"],["outdent","indent"],["table","horizontalRule","link","image","video"],["fullScreen","showBlocks","codeView"],["preview","print"],["removeFormat"]],imageUploadHandler:O,defaultTag:"div",minHeight:"300px",maxHeight:"400px",showPathLabel:!1,font:["Proxima-Nova-Regular","Proxima-Nova-Medium","Proxima-Nova-Semibold","Proxima-Nova-Bold","Proxima-Nova-Extrabold","Proxima-Nova-Black","Proxima-Nova-Light","Proxima-Nova-Thin","Arial","Times New Roman","Sans-Serif"],charCounter:!0,charCounterType:"byte",resizingBar:!1,colorList:[["#234791","#d69b19","#cc3233","#009966","#0b3051","#2BBFAD","#0b305100","#0a305214","#743794","#ff0000","#ff5e00","#ffe400","#abf200","#00d8ff","#0055ff","#6600ff","#ff00dd","#000000","#ffd8d8","#fae0d4","#faf4c0","#e4f7ba","#d4f4fa","#d9e5ff","#e8d9ff","#ffd9fa","#f1f1f1","#ffa7a7","#ffc19e","#faed7d","#cef279","#b2ebf4","#b2ccff","#d1b2ff","#ffb2f5","#bdbdbd","#f15f5f","#f29661","#e5d85c","#bce55c","#5cd1e5","#6699ff","#a366ff","#f261df","#8c8c8c","#980000","#993800","#998a00","#6b9900","#008299","#003399","#3d0099","#990085","#353535","#670000","#662500","#665c00","#476600","#005766","#002266","#290066","#660058","#222222"]]},onImageUpload:O}),(0,n.jsx)("br",{}),(0,n.jsxs)("div",{className:"inline-group",children:[(0,n.jsx)("div",{children:(0,n.jsx)(h.Z,{children:(0,n.jsxs)(f.Z,{className:"label-form",children:[_("createArticle:metaTitle")," ("," ",(0,n.jsxs)("span",{className:y.metaTitleFR?.length>65?" text-danger":"",children:[" ",y.metaTitleFR?.length," / 65"," "]})," ",")",(0,n.jsx)(v.Z,{variant:"standard",name:"metaTitleFR",type:"text",value:y.metaTitleFR,onChange:e=>{l("metaTitleFR",e.target.value)},className:"input-pentabell"+(t.metaTitleFR&&a.metaTitleFR?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"metaTitleFR",component:"div"})]})})}),(0,n.jsx)("div",{children:(0,n.jsx)(h.Z,{children:(0,n.jsxs)(f.Z,{className:"label-form",children:[_("createArticle:url"),(0,n.jsx)(v.Z,{variant:"standard",name:"urlFR",type:"text",value:y.urlFR,onChange:e=>{l("urlFR",e.target.value)},className:"input-pentabell"+(t.urlFR&&a.urlFR?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"urlFR",component:"div"})]})})})]}),(0,n.jsx)("div",{className:"inline-group",children:(0,n.jsx)("div",{children:(0,n.jsx)(h.Z,{children:(0,n.jsxs)(f.Z,{className:"label-form",children:[_("createArticle:metaDescription")," ("," ",(0,n.jsxs)("span",{className:y.metaDescriptionFR?.length>160?" text-danger":"",children:[y.metaDescriptionFR?.length," / 160"]})," ",")",(0,n.jsx)(v.Z,{variant:"standard",name:"metaDescriptionFR",type:"text",multiline:!0,rows:2,value:y.metaDescriptionFR,onChange:e=>{l("metaDescriptionFR",e.target.value)},className:"textArea-pentabell"+(t.metaDescriptionFR&&a.metaDescriptionFR?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"metaDescriptionFR",component:"div"})]})})})}),(0,n.jsxs)("div",{className:"inline-group",children:[(0,n.jsx)("div",{children:(0,n.jsx)(h.Z,{children:(0,n.jsxs)(f.Z,{className:"label-form",children:[_("createArticle:alt"),(0,n.jsx)(v.Z,{variant:"standard",name:"altFR",type:"text",value:y.altFR,onChange:e=>{l("altFR",e.target.value)},className:"input-pentabell"+(t.altFR&&a.altFR?" is-invalid":"")})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"altFR",component:"div"})]})})}),(0,n.jsx)("div",{children:(0,n.jsx)(h.Z,{children:(0,n.jsxs)(f.Z,{className:"label-form",children:[_("createArticle:visibility"),(0,n.jsx)(g.Z,{className:"select-pentabell",variant:"standard",value:d.EE.filter(e=>y.visibilityFR===e),selected:y.visibilityFR,onChange:e=>{l("visibilityFR",e.target.value)},children:d.EE.map((e,t)=>(0,n.jsx)(x.Z,{value:e,children:e},t))}),(0,n.jsx)(s.Bc,{className:"label-error",name:"visibilityEN",component:"div"})]})})}),(0,n.jsx)("div",{children:(0,n.jsx)(h.Z,{children:(0,n.jsxs)(f.Z,{className:"label-form",children:[_("createArticle:keyword"),(0,n.jsx)("div",{id:"tags",children:(0,n.jsx)(m.V,{tags:D,className:"input-pentabell"+(t.keywordsFR&&a.keywordsFR?" is-invalid":""),delimiters:[188,13],handleDelete:e=>{let t=D.filter((t,a)=>a!==e);P(t),l("keywordsFR",t.map(e=>e.text))},handleAddition:e=>{P([...D,e]),l("keywordsFR",[...D,e].map(e=>e.text))},inputFieldPosition:"bottom",autocomplete:!0,allowDragDrop:!1})}),(0,n.jsx)(s.Bc,{className:"label-error",name:"keywordsFR",component:"div"})]})})})]}),(0,n.jsxs)("label",{className:"label-form",children:[(0,n.jsx)(s.gN,{type:"checkbox",name:"publishNow",checked:U,onChange:e=>{$(e.target.checked),e.target.checked&&l("publishDateFR",new Date().toISOString())}}),_("createArticle:publishNow")]}),!U&&(0,n.jsx)("div",{children:(0,n.jsx)(h.Z,{children:(0,n.jsxs)(f.Z,{className:"label-form",children:[_("createArticle:publishDate"),(0,n.jsx)(b._,{dateAdapter:A.y,children:(0,n.jsxs)(j.C,{components:["DatePicker"],children:[(0,n.jsx)(N.M,{variant:"standard",className:"input-date",format:"DD/MM/YYYY",value:E()(y.publishDateEN),onChange:e=>{l("publishDateEN",E()(e).format("YYYY-MM-DD"))}})," ",(0,n.jsx)(s.Bc,{className:"label-error",name:"publishDateEN",component:"div"})]})})]})})}),(0,n.jsx)(s.gN,{type:"hidden",name:"publishDateFR",value:U?new Date().toISOString():L.toISOString()})]})}},92951:function(e,t,a){"use strict";a.d(t,{_x:function(){return u},Cr:function(){return h},dB:function(){return p},ff:function(){return m}});var n=a(86484);a(80657);var s=a(46172),r=a(93214),i=a(7261);let l=e=>(e.t,new Promise(async(t,a)=>{r.yX.post(s.Y.events,e.data).then(e=>{i.Am.success("Event added successfully"),e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&a(e)})})),o=e=>{let{data:t,id:a}=e;return new Promise(async(e,n)=>{r.yX.put(`${s.Y.events}/${a}`,t).then(t=>{i.Am.success("Event updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&n(e)})})},c=e=>new Promise(async(t,a)=>{try{let a=await r.yX.get(`${s.Y.events}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,slug:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,publishDate:e.publishDate,isArchived:e.isArchived}});t(a.data)}catch(e){a(e)}}),d=e=>new Promise(async(t,a)=>{try{let a=await r.yX.get(`${s.Y.events}/${e}`);t(a.data)}catch(e){a(e)}}),u=()=>(0,n.useMutation)({mutationFn:e=>l(e),onError:e=>{e.message=""}}),m=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t)=>o(e,t),onError:e=>{e.message=""}})),p=e=>(0,n.useQuery)(`Events${e.language}`,async()=>await c(e)),h=e=>(0,n.useQuery)(["event",e],async()=>await d(e))},62953:function(e,t,a){"use strict";a.d(t,{$i:function(){return f},BF:function(){return h},Fe:function(){return i},Gc:function(){return d},HF:function(){return r},Hr:function(){return o},IZ:function(){return p},NF:function(){return c},PM:function(){return l},UJ:function(){return u},jd:function(){return m}});var n=a(86484),s=a(49443);a(99376),a(80657);let r=()=>(0,n.useMutation)({mutationFn:e=>(0,s.W3)(e),onError:e=>{e.message=""}}),i=e=>(0,n.useQuery)("opportunities",async()=>await (0,s.fH)(e)),l=()=>(0,n.useMutation)(()=>(0,s.AE)()),o=e=>(0,n.useQuery)(["opportunities",e],async()=>await (0,s.Mq)(e)),c=()=>(0,n.useMutation)({mutationFn:(e,t,a)=>(0,s.rE)(e,t,a),onError:e=>{e.message=""}}),d=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t)=>(0,s.S1)(e,t),onError:e=>{e.message=""}})),u=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t,a)=>(0,s.lU)(e,t,a),onError:e=>{e.message=""}})),m=()=>{let e=(0,n.useQueryClient)();return(0,n.useMutation)({mutationFn:(e,t,a,n)=>(0,s.yH)(e,t,a,n),onSuccess:t=>{e.invalidateQueries("files")}})},p=()=>(0,n.useQuery)("SeoOpportunities",async()=>await (0,s.yJ)()),h=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:(e,t)=>(0,s.mt)(e,t),onError:e=>{e.message=""}})),f=()=>((0,n.useQueryClient)(),(0,n.useMutation)({mutationFn:e=>{let{language:t,id:a,archive:n}=e;return(0,s.TK)(t,a,n)},onError:e=>{console.error("Error during mutation",e),e.message=""}}))},49443:function(e,t,a){"use strict";a.d(t,{AE:function(){return c},Mq:function(){return o},S1:function(){return u},TK:function(){return f},W3:function(){return i},fH:function(){return l},lU:function(){return m},mt:function(){return v},rE:function(){return d},yH:function(){return p},yJ:function(){return h}});var n=a(46172),s=a(93214),r=a(7261);let i=e=>(e.t,new Promise(async(t,a)=>{s.yX.post(`/opportunities${n.Y.applications}/${e.opportunityId}`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&a(e)})})),l=e=>new Promise(async(t,a)=>{try{let a=await s.yX.get(`${n.Y.opportunity}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyWord:e.keyWord,visibility:e.visibility,title:e.title,createdAt:e.createdAt,publishDate:e.publishDate,country:e.country,industry:e.industry,contractType:e.contractType,minExperience:e.minExperience,maxExperience:e.maxExperience,jobDescriptionLanguages:e.jobDescriptionLanguages,reference:e.reference,opportunityType:e.opportunityType,exclude:"In House"!==e.opportunityType&&"true"}});t(a.data)}catch(e){a(e)}}),o=e=>new Promise(async(t,a)=>{try{let a=await s.yX.get(`${n.Y.opportunity}/${e}`);t(a.data)}catch(e){a(e)}}),c=async()=>(await s.xk.put("/UpdateJobdescription")).data,d=e=>{let{data:t,language:a,id:i}=e;return new Promise(async(e,l)=>{s.yX.post(`${n.Y.opportunity}/${a}/${i}`,t).then(t=>{"en"===a&&r.Am.success("Opportunity english updated successfully"),"fr"===a&&r.Am.success("Opportunity french updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&l(e)})})},u=e=>{let{data:t,id:a}=e;return new Promise(async(e,i)=>{s.yX.put(`${n.Y.opportunity}/${a}`,t).then(t=>{r.Am.success("Opportunity Commun fields updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})})},m=e=>{let{id:t,title:a,typeOfFavourite:i}=e;return new Promise(async(e,l)=>{s.yX.put(`${n.Y.baseUrl}/favourite/${t}`,{type:i}).then(t=>{r.Am.success(`${i} : ${a} saved to your favorites.`),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data&&409===e.response.status&&r.Am.warning(` ${a} already in shortlist`),e&&l(e)})})},p=e=>{let{resource:t,folder:a,filename:i,body:l}=e;return new Promise(async(e,o)=>{s.cU.post(`${n.Y.files}/uploadResume/${t}/${a}/${i}`,l.formData).then(t=>{t.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data&&(400===e.response.status?e.response.data.message.includes("The resume lacks essential information")?r.Am.warn(l.t("messages:requireResume")):r.Am.warn(e.response.data.message):500===e.response.status&&r.Am.error("Internal Server Error")),e&&o(e)})})},h=()=>new Promise(async(e,t)=>{try{let t=await s.yX.get(`${n.Y.seoOpportunity}`);e(t.data)}catch(e){t(e)}}),f=(e,t,a)=>new Promise(async(i,l)=>{try{let l=await s.yX.put(`${n.Y.opportunity}/${e}/${t}/desarchiver`,{archive:a});l?.data&&(r.Am.success(`opportunity ${a?"archived":"desarchived"} successfully`),i(l.data))}catch(e){r.Am.error(`Failed to ${a?"archive":"desarchive"} the opportunity.`),l(e)}}),v=e=>{let{data:t,id:a}=e;return new Promise(async(e,i)=>{s.yX.put(`${n.Y.seoOpportunity}/${a}`,t).then(t=>{r.Am.success("Opportunity seo updated successfully"),t?.data&&e(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&i(e)})})}},46172:function(e,t,a){"use strict";a.d(t,{Y:function(){return s},v:function(){return n}});let n=a(40257).env.NEXT_PUBLIC_BASE_API_URL,s={seo:"seoTags",auth:"/auth/signin",logout:"/auth/logout",candidatures:"/applications",signup:"/auth/signup",forgetPassword:"/auth/forgot-password",resetPassword:"/auth/reset-password",guides:"/guides",currentUser:"/users/current",updateUser:"/users",users:"/users",categoryGuides:"guidecategory",candidate:"/candidates",report:"/report",skills:"/skills",files:"/files",applications:"/applications",sliders:"/sliders",favoris:"/candidate/favourite",articles:"/articles",categories:"/categories",blog:"/blog",category:"/categories",opportunity:"/opportunities",seoOpportunity:"/seoOpportunity",newsletter:"/newsletter",contact:"/contact",favourite:"/favourite",contacts:"contacts",comments:"/comments",statistics:"/statistics",events:"/events",baseUrl:`${n}`}},21005:function(){},19841:function(e,t){"use strict";t.Z={src:"/_next/static/media/add.7d9a0730.png",height:29,width:29,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEUjT5YfPJMjTpUjT5chT5UjTpckT5YkT5SyXEFwAAAACHRSTlOKAQ+aI3pIMEwTVZQAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAvSURBVHicJYrJEQAgEIPIofbfsbOaFxPAi1lhS9IJSLbEwMIPnPQ/7VOd2GF6yL4OogBsGLyWkgAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8}}}]);