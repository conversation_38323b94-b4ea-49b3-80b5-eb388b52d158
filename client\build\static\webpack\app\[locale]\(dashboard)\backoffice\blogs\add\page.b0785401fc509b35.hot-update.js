"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,FormLabel,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,FormLabel,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,FormLabel,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,FormLabel,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,FormLabel,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst dragActiveStyles = `\r\n  .file-labels.drag-active {\r\n    border-color: #1976d2 !important;\r\n    background-color: rgba(25, 118, 210, 0.04) !important;\r\n  }\r\n  .file-labels.disabled {\r\n    cursor: not-allowed !important;\r\n    opacity: 0.6 !important;\r\n  }\r\n`;\nif (typeof document !== \"undefined\" && !document.getElementById(\"document-importer-styles\")) {\n    const styleSheet = document.createElement(\"style\");\n    styleSheet.id = \"document-importer-styles\";\n    styleSheet.textContent = dragActiveStyles;\n    document.head.appendChild(styleSheet);\n}\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement((image)=>image.read(\"base64\").then((imageBuffer)=>({\n                                src: `data:${image.contentType};base64,${imageBuffer}`\n                            })))\n                }\n            });\n            setProgress(75);\n            const cleanContent = result.value.replace(/<p><\\/p>/g, \"\").replace(/\\s+/g, \" \").trim();\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            onContentExtracted(result.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(result.metadata);\n            }\n            setSuccess(true);\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) processFile(acceptedFiles[0]);\n    }, []);\n    const { getRootProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing,\n        noClick: true,\n        noKeyboard: true\n    });\n    const handleFileChange = (event)=>{\n        const files = event.target.files;\n        if (files && files.length > 0) {\n            processFile(files[0]);\n        }\n        event.target.value = \"\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        sx: {\n            mb: 3,\n            mt: 1\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"label-form\",\n                children: [\n                    t(\"createArticle:content\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"upload-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    ...getRootProps(),\n                    className: `file-labels ${isDragActive ? \"drag-active\" : \"\"} ${disabled || isProcessing ? \"disabled\" : \"\"}`,\n                    style: {\n                        opacity: disabled || isProcessing ? 0.6 : 1,\n                        cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"file\",\n                            accept: \".docx,.doc,.txt\",\n                            onChange: handleFileChange,\n                            className: \"file-input\",\n                            disabled: disabled || isProcessing\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"upload-area\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"icon-pic\",\n                                    style: {\n                                        backgroundImage: `url(\"${_assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"]}\")`,\n                                        backgroundSize: \"cover\",\n                                        backgroundRepeat: \"no-repeat\",\n                                        backgroundPosition: \"center\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-text\",\n                                            children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:importFromDocument\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-description\",\n                                            children: [\n                                                t(\"createArticle:supportedFormats\"),\n                                                \": .docx, .doc, .txt\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 245,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, undefined),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_FormLabel_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"RXmj7XEvBZCVJUKRSpxVlTpxjkM=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});