"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[775],{78242:function(e,t,r){r.d(t,{Z:function(){return K}});var n=function(){function e(e){var t=this;this._insertTag=function(e){var r;r=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,r),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){if(this.ctr%(this.isSpeedy?65e3:1)==0){var t;this._insertTag(((t=document.createElement("style")).setAttribute("data-emotion",this.key),void 0!==this.nonce&&t.setAttribute("nonce",this.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t))}var r=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(r);try{n.insertRule(e,n.cssRules.length)}catch(e){}}else r.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach(function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),this.tags=[],this.ctr=0},e}(),o=Math.abs,a=String.fromCharCode,i=Object.assign;function l(e,t,r){return e.replace(t,r)}function c(e,t){return e.indexOf(t)}function s(e,t){return 0|e.charCodeAt(t)}function u(e,t,r){return e.slice(t,r)}function f(e){return e.length}function d(e,t){return t.push(e),e}var p=1,g=1,h=0,m=0,y=0,b="";function v(e,t,r,n,o,a,i){return{value:e,root:t,parent:r,type:n,props:o,children:a,line:p,column:g,length:i,return:""}}function k(e,t){return i(v("",null,null,"",null,null,0),e,{length:-e.length},t)}function x(){return y=m<h?s(b,m++):0,g++,10===y&&(g=1,p++),y}function S(){return s(b,m)}function $(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function A(e){return p=g=1,h=f(b=e),m=0,[]}function w(e){var t,r;return(t=m-1,r=function e(t){for(;x();)switch(y){case t:return m;case 34:case 39:34!==t&&39!==t&&e(y);break;case 40:41===t&&e(t);break;case 92:x()}return m}(91===e?e+2:40===e?e+1:e),u(b,t,r)).trim()}var C="-ms-",B="-moz-",O="-webkit-",P="comm",T="rule",Z="decl",j="@keyframes";function E(e,t){for(var r="",n=e.length,o=0;o<n;o++)r+=t(e[o],o,e,t)||"";return r}function I(e,t,r,n){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case Z:return e.return=e.return||e.value;case P:return"";case j:return e.return=e.value+"{"+E(e.children,n)+"}";case T:e.value=e.props.join(",")}return f(r=E(e.children,n))?e.return=e.value+"{"+r+"}":""}function R(e,t,r,n,a,i,c,s,f,d,p){for(var g=a-1,h=0===a?i:[""],m=h.length,y=0,b=0,k=0;y<n;++y)for(var x=0,S=u(e,g+1,g=o(b=c[y])),$=e;x<m;++x)($=(b>0?h[x]+" "+S:l(S,/&\f/g,h[x])).trim())&&(f[k++]=$);return v(e,t,r,0===a?T:s,f,d,p)}function _(e,t,r,n){return v(e,t,r,Z,u(e,0,n),u(e,n+1,-1),n)}var L=function(e,t,r){for(var n=0,o=0;n=o,o=S(),38===n&&12===o&&(t[r]=1),!$(o);)x();return u(b,e,m)},W=function(e,t){var r=-1,n=44;do switch($(n)){case 0:38===n&&12===S()&&(t[r]=1),e[r]+=L(m-1,t,r);break;case 2:e[r]+=w(n);break;case 4:if(44===n){e[++r]=58===S()?"&\f":"",t[r]=e[r].length;break}default:e[r]+=a(n)}while(n=x());return e},F=function(e,t){var r;return r=W(A(e),t),b="",r},q=new WeakMap,M=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,r=e.parent,n=e.column===r.column&&e.line===r.line;"rule"!==r.type;)if(!(r=r.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||q.get(r))&&!n){q.set(e,!0);for(var o=[],a=F(t,o),i=r.props,l=0,c=0;l<a.length;l++)for(var s=0;s<i.length;s++,c++)e.props[c]=o[l]?a[l].replace(/&\f/g,i[s]):i[s]+" "+a[l]}}},N=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}},z=[function(e,t,r,n){if(e.length>-1&&!e.return)switch(e.type){case Z:e.return=function e(t,r){switch(45^s(t,0)?(((r<<2^s(t,0))<<2^s(t,1))<<2^s(t,2))<<2^s(t,3):0){case 5103:return O+"print-"+t+t;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return O+t+t;case 5349:case 4246:case 4810:case 6968:case 2756:return O+t+B+t+C+t+t;case 6828:case 4268:return O+t+C+t+t;case 6165:return O+t+C+"flex-"+t+t;case 5187:return O+t+l(t,/(\w+).+(:[^]+)/,O+"box-$1$2"+C+"flex-$1$2")+t;case 5443:return O+t+C+"flex-item-"+l(t,/flex-|-self/,"")+t;case 4675:return O+t+C+"flex-line-pack"+l(t,/align-content|flex-|-self/,"")+t;case 5548:return O+t+C+l(t,"shrink","negative")+t;case 5292:return O+t+C+l(t,"basis","preferred-size")+t;case 6060:return O+"box-"+l(t,"-grow","")+O+t+C+l(t,"grow","positive")+t;case 4554:return O+l(t,/([^-])(transform)/g,"$1"+O+"$2")+t;case 6187:return l(l(l(t,/(zoom-|grab)/,O+"$1"),/(image-set)/,O+"$1"),t,"")+t;case 5495:case 3959:return l(t,/(image-set\([^]*)/,O+"$1$`$1");case 4968:return l(l(t,/(.+:)(flex-)?(.*)/,O+"box-pack:$3"+C+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+O+t+t;case 4095:case 3583:case 4068:case 2532:return l(t,/(.+)-inline(.+)/,O+"$1$2")+t;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(f(t)-1-r>6)switch(s(t,r+1)){case 109:if(45!==s(t,r+4))break;case 102:return l(t,/(.+:)(.+)-([^]+)/,"$1"+O+"$2-$3$1"+B+(108==s(t,r+3)?"$3":"$2-$3"))+t;case 115:return~c(t,"stretch")?e(l(t,"stretch","fill-available"),r)+t:t}break;case 4949:if(115!==s(t,r+1))break;case 6444:switch(s(t,f(t)-3-(~c(t,"!important")&&10))){case 107:return l(t,":",":"+O)+t;case 101:return l(t,/(.+:)([^;!]+)(;|!.+)?/,"$1"+O+(45===s(t,14)?"inline-":"")+"box$3$1"+O+"$2$3$1"+C+"$2box$3")+t}break;case 5936:switch(s(t,r+11)){case 114:return O+t+C+l(t,/[svh]\w+-[tblr]{2}/,"tb")+t;case 108:return O+t+C+l(t,/[svh]\w+-[tblr]{2}/,"tb-rl")+t;case 45:return O+t+C+l(t,/[svh]\w+-[tblr]{2}/,"lr")+t}return O+t+C+t+t}return t}(e.value,e.length);break;case j:return E([k(e,{value:l(e.value,"@","@"+O)})],n);case T:if(e.length){var o,a;return o=e.props,a=function(t){var r;switch(r=t,(r=/(::plac\w+|:read-\w+)/.exec(r))?r[0]:r){case":read-only":case":read-write":return E([k(e,{props:[l(t,/:(read-\w+)/,":"+B+"$1")]})],n);case"::placeholder":return E([k(e,{props:[l(t,/:(plac\w+)/,":"+O+"input-$1")]}),k(e,{props:[l(t,/:(plac\w+)/,":"+B+"$1")]}),k(e,{props:[l(t,/:(plac\w+)/,C+"input-$1")]})],n)}return""},o.map(a).join("")}}}],K=function(e){var t,r,o,i,h,k,C=e.key;if("css"===C){var B=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(B,function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))})}var O=e.stylisPlugins||z,T={},Z=[];i=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+C+' "]'),function(e){for(var t=e.getAttribute("data-emotion").split(" "),r=1;r<t.length;r++)T[t[r]]=!0;Z.push(e)});var j=(r=(t=[M,N].concat(O,[I,(o=function(e){k.insert(e)},function(e){!e.root&&(e=e.return)&&o(e)})])).length,function(e,n,o,a){for(var i="",l=0;l<r;l++)i+=t[l](e,n,o,a)||"";return i}),L=function(e){var t,r;return E((r=function e(t,r,n,o,i,h,k,A,C){for(var B,O=0,T=0,Z=k,j=0,E=0,I=0,L=1,W=1,F=1,q=0,M="",N=i,z=h,K=o,D=M;W;)switch(I=q,q=x()){case 40:if(108!=I&&58==s(D,Z-1)){-1!=c(D+=l(w(q),"&","&\f"),"&\f")&&(F=-1);break}case 34:case 39:case 91:D+=w(q);break;case 9:case 10:case 13:case 32:D+=function(e){for(;y=S();)if(y<33)x();else break;return $(e)>2||$(y)>3?"":" "}(I);break;case 92:D+=function(e,t){for(var r;--t&&x()&&!(y<48)&&!(y>102)&&(!(y>57)||!(y<65))&&(!(y>70)||!(y<97)););return r=m+(t<6&&32==S()&&32==x()),u(b,e,r)}(m-1,7);continue;case 47:switch(S()){case 42:case 47:d(v(B=function(e,t){for(;x();)if(e+y===57)break;else if(e+y===84&&47===S())break;return"/*"+u(b,t,m-1)+"*"+a(47===e?e:x())}(x(),m),r,n,P,a(y),u(B,2,-2),0),C);break;default:D+="/"}break;case 123*L:A[O++]=f(D)*F;case 125*L:case 59:case 0:switch(q){case 0:case 125:W=0;case 59+T:-1==F&&(D=l(D,/\f/g,"")),E>0&&f(D)-Z&&d(E>32?_(D+";",o,n,Z-1):_(l(D," ","")+";",o,n,Z-2),C);break;case 59:D+=";";default:if(d(K=R(D,r,n,O,T,i,A,M,N=[],z=[],Z),h),123===q){if(0===T)e(D,r,K,K,N,h,Z,A,z);else switch(99===j&&110===s(D,3)?100:j){case 100:case 108:case 109:case 115:e(t,K,K,o&&d(R(t,K,K,0,0,i,A,M,i,N=[],Z),z),i,z,Z,A,o?N:z);break;default:e(D,K,K,K,[""],z,0,A,z)}}}O=T=E=0,L=F=1,M=D="",Z=k;break;case 58:Z=1+f(D),E=I;default:if(L<1){if(123==q)--L;else if(125==q&&0==L++&&125==(y=m>0?s(b,--m):0,g--,10===y&&(g=1,p--),y))continue}switch(D+=a(q),q*L){case 38:F=T>0?1:(D+="\f",-1);break;case 44:A[O++]=(f(D)-1)*F,F=1;break;case 64:45===S()&&(D+=w(x())),j=S(),T=Z=f(M=D+=function(e){for(;!$(S());)x();return u(b,e,m)}(m)),q++;break;case 45:45===I&&2==f(D)&&(L=0)}}return h}("",null,null,null,[""],t=A(t=e),0,[0],t),b="",r),j)};h=function(e,t,r,n){k=r,L(e?e+"{"+t.styles+"}":t.styles),n&&(W.inserted[t.name]=!0)};var W={key:C,sheet:new n({key:C,container:i,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:T,registered:{},insert:h};return W.sheet.hydrate(Z),W}},5772:function(e,t,r){r.d(t,{Z:function(){return n}});function n(e){var t=Object.create(null);return function(r){return void 0===t[r]&&(t[r]=e(r)),t[r]}}},25246:function(e,t,r){r.d(t,{E:function(){return h},T:function(){return u},c:function(){return p},h:function(){return f},w:function(){return s}});var n=r(2265),o=r(78242),a=r(32820),i=r(29896),l=r(24006),c=n.createContext("undefined"!=typeof HTMLElement?(0,o.Z)({key:"css"}):null);c.Provider;var s=function(e){return(0,n.forwardRef)(function(t,r){return e(t,(0,n.useContext)(c),r)})},u=n.createContext({}),f={}.hasOwnProperty,d="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",p=function(e,t){var r={};for(var n in t)f.call(t,n)&&(r[n]=t[n]);return r[d]=e,r},g=function(e){var t=e.cache,r=e.serialized,n=e.isStringTag;return(0,a.hC)(t,r,n),(0,l.L)(function(){return(0,a.My)(t,r,n)}),null},h=s(function(e,t,r){var o=e.css;"string"==typeof o&&void 0!==t.registered[o]&&(o=t.registered[o]);var l=e[d],c=[o],s="";"string"==typeof e.className?s=(0,a.fp)(t.registered,c,e.className):null!=e.className&&(s=e.className+" ");var p=(0,i.O)(c,void 0,n.useContext(u));s+=t.key+"-"+p.name;var h={};for(var m in e)f.call(e,m)&&"css"!==m&&m!==d&&(h[m]=e[m]);return h.className=s,r&&(h.ref=r),n.createElement(n.Fragment,null,n.createElement(g,{cache:t,serialized:p,isStringTag:"string"==typeof l}),n.createElement(l,h))})},29896:function(e,t,r){r.d(t,{O:function(){return g}});var n,o={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},a=r(5772),i=/[A-Z]|^ms/g,l=/_EMO_([^_]+?)_([^]*?)_EMO_/g,c=function(e){return 45===e.charCodeAt(1)},s=function(e){return null!=e&&"boolean"!=typeof e},u=(0,a.Z)(function(e){return c(e)?e:e.replace(i,"-$&").toLowerCase()}),f=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(l,function(e,t,r){return n={name:t,styles:r,next:n},t})}return 1===o[e]||c(e)||"number"!=typeof t||0===t?t:t+"px"};function d(e,t,r){if(null==r)return"";if(void 0!==r.__emotion_styles)return r;switch(typeof r){case"boolean":return"";case"object":if(1===r.anim)return n={name:r.name,styles:r.styles,next:n},r.name;if(void 0!==r.styles){var o=r.next;if(void 0!==o)for(;void 0!==o;)n={name:o.name,styles:o.styles,next:n},o=o.next;return r.styles+";"}return function(e,t,r){var n="";if(Array.isArray(r))for(var o=0;o<r.length;o++)n+=d(e,t,r[o])+";";else for(var a in r){var i=r[a];if("object"!=typeof i)null!=t&&void 0!==t[i]?n+=a+"{"+t[i]+"}":s(i)&&(n+=u(a)+":"+f(a,i)+";");else if(Array.isArray(i)&&"string"==typeof i[0]&&(null==t||void 0===t[i[0]]))for(var l=0;l<i.length;l++)s(i[l])&&(n+=u(a)+":"+f(a,i[l])+";");else{var c=d(e,t,i);switch(a){case"animation":case"animationName":n+=u(a)+":"+c+";";break;default:n+=a+"{"+c+"}"}}}return n}(e,t,r);case"function":if(void 0!==e){var a=n,i=r(e);return n=a,d(e,t,i)}}if(null==t)return r;var l=t[r];return void 0!==l?l:r}var p=/label:\s*([^\s;{]+)\s*(;|$)/g;function g(e,t,r){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var o,a=!0,i="";n=void 0;var l=e[0];null==l||void 0===l.raw?(a=!1,i+=d(r,t,l)):i+=l[0];for(var c=1;c<e.length;c++)i+=d(r,t,e[c]),a&&(i+=l[c]);p.lastIndex=0;for(var s="";null!==(o=p.exec(i));)s+="-"+o[1];return{name:function(e){for(var t,r=0,n=0,o=e.length;o>=4;++n,o-=4)t=(65535&(t=255&e.charCodeAt(n)|(255&e.charCodeAt(++n))<<8|(255&e.charCodeAt(++n))<<16|(255&e.charCodeAt(++n))<<24))*1540483477+((t>>>16)*59797<<16),t^=t>>>24,r=(65535&t)*1540483477+((t>>>16)*59797<<16)^(65535&r)*1540483477+((r>>>16)*59797<<16);switch(o){case 3:r^=(255&e.charCodeAt(n+2))<<16;case 2:r^=(255&e.charCodeAt(n+1))<<8;case 1:r^=255&e.charCodeAt(n),r=(65535&r)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,(((r=(65535&r)*1540483477+((r>>>16)*59797<<16))^r>>>15)>>>0).toString(36)}(i)+s,styles:i,next:n}}},24006:function(e,t,r){r.d(t,{L:function(){return i},j:function(){return l}});var n,o=r(2265),a=!!(n||(n=r.t(o,2))).useInsertionEffect&&(n||(n=r.t(o,2))).useInsertionEffect,i=a||function(e){return e()},l=a||o.useLayoutEffect},32820:function(e,t,r){function n(e,t,r){var n="";return r.split(" ").forEach(function(r){void 0!==e[r]?t.push(e[r]+";"):r&&(n+=r+" ")}),n}r.d(t,{My:function(){return a},fp:function(){return n},hC:function(){return o}});var o=function(e,t,r){var n=e.key+"-"+t.name;!1===r&&void 0===e.registered[n]&&(e.registered[n]=t.styles)},a=function(e,t,r){o(e,t,r);var n=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var a=t;do e.insert(t===a?"."+n:"",a,e.sheet,!0),a=a.next;while(void 0!==a)}}},84586:function(e,t,r){r.d(t,{L7:function(){return u},P$:function(){return d},VO:function(){return a},W8:function(){return s},dt:function(){return f},k9:function(){return c}});var n=r(87354),o=r(72521);let a={xs:0,sm:600,md:900,lg:1200,xl:1536},i={keys:["xs","sm","md","lg","xl"],up:e=>`@media (min-width:${a[e]}px)`},l={containerQueries:e=>({up:t=>{let r="number"==typeof t?t:a[t]||t;return"number"==typeof r&&(r=`${r}px`),e?`@container ${e} (min-width:${r})`:`@container (min-width:${r})`}})};function c(e,t,r){let n=e.theme||{};if(Array.isArray(t)){let e=n.breakpoints||i;return t.reduce((n,o,a)=>(n[e.up(e.keys[a])]=r(t[a]),n),{})}if("object"==typeof t){let e=n.breakpoints||i;return Object.keys(t).reduce((i,c)=>{if((0,o.WX)(e.keys,c)){let e=(0,o.ue)(n.containerQueries?n:l,c);e&&(i[e]=r(t[c],c))}else Object.keys(e.values||a).includes(c)?i[e.up(c)]=r(t[c],c):i[c]=t[c];return i},{})}return r(t)}function s(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.keys?.reduce((t,r)=>(t[e.up(r)]={},t),{})||{}}function u(e,t){return e.reduce((e,t)=>{let r=e[t];return r&&0!==Object.keys(r).length||delete e[t],e},t)}function f(e){for(var t=arguments.length,r=Array(t>1?t-1:0),o=1;o<t;o++)r[o-1]=arguments[o];let a=s(e),i=[a,...r].reduce((e,t)=>(0,n.Z)(e,t),{});return u(Object.keys(a),i)}function d(e){let t,{values:r,breakpoints:n,base:o}=e,a=Object.keys(o||function(e,t){if("object"!=typeof e)return{};let r={},n=Object.keys(t);return Array.isArray(e)?n.forEach((t,n)=>{n<e.length&&(r[t]=!0)}):n.forEach(t=>{null!=e[t]&&(r[t]=!0)}),r}(r,n));return 0===a.length?r:a.reduce((e,n,o)=>(Array.isArray(r)?(e[n]=null!=r[o]?r[o]:r[t],t=o):"object"==typeof r?(e[n]=null!=r[n]?r[n]:r[t],t=n):e[n]=r,e),{})}},82590:function(e,t,r){r.d(t,{Fq:function(){return d},_j:function(){return g},tB:function(){return a},mi:function(){return f},ve:function(){return s},$n:function(){return m},zp:function(){return p},LR:function(){return l},q8:function(){return h},fk:function(){return b},ux:function(){return y},wy:function(){return c}});var n=r(80399);function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return function(e,t=Number.MIN_SAFE_INTEGER,r=Number.MAX_SAFE_INTEGER){return Math.max(t,Math.min(e,r))}(e,t,r)}function a(e){let t;if(e.type)return e;if("#"===e.charAt(0))return a(function(e){e=e.slice(1);let t=RegExp(`.{1,${e.length>=6?2:1}}`,"g"),r=e.match(t);return r&&1===r[0].length&&(r=r.map(e=>e+e)),r?`rgb${4===r.length?"a":""}(${r.map((e,t)=>t<3?parseInt(e,16):Math.round(parseInt(e,16)/255*1e3)/1e3).join(", ")})`:""}(e));let r=e.indexOf("("),o=e.substring(0,r);if(!["rgb","rgba","hsl","hsla","color"].includes(o))throw Error((0,n.Z)(9,e));let i=e.substring(r+1,e.length-1);if("color"===o){if(t=(i=i.split(" ")).shift(),4===i.length&&"/"===i[3].charAt(0)&&(i[3]=i[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(t))throw Error((0,n.Z)(10,t))}else i=i.split(",");return{type:o,values:i=i.map(e=>parseFloat(e)),colorSpace:t}}let i=e=>{let t=a(e);return t.values.slice(0,3).map((e,r)=>t.type.includes("hsl")&&0!==r?`${e}%`:e).join(" ")},l=(e,t)=>{try{return i(e)}catch(t){return e}};function c(e){let{type:t,colorSpace:r}=e,{values:n}=e;return t.includes("rgb")?n=n.map((e,t)=>t<3?parseInt(e,10):e):t.includes("hsl")&&(n[1]=`${n[1]}%`,n[2]=`${n[2]}%`),n=t.includes("color")?`${r} ${n.join(" ")}`:`${n.join(", ")}`,`${t}(${n})`}function s(e){let{values:t}=e=a(e),r=t[0],n=t[1]/100,o=t[2]/100,i=n*Math.min(o,1-o),l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(e+r/30)%12;return o-i*Math.max(Math.min(t-3,9-t,1),-1)},s="rgb",u=[Math.round(255*l(0)),Math.round(255*l(8)),Math.round(255*l(4))];return"hsla"===e.type&&(s+="a",u.push(t[3])),c({type:s,values:u})}function u(e){let t="hsl"===(e=a(e)).type||"hsla"===e.type?a(s(e)).values:e.values;return Number((.2126*(t=t.map(t=>("color"!==e.type&&(t/=255),t<=.03928?t/12.92:((t+.055)/1.055)**2.4)))[0]+.7152*t[1]+.0722*t[2]).toFixed(3))}function f(e,t){let r=u(e),n=u(t);return(Math.max(r,n)+.05)/(Math.min(r,n)+.05)}function d(e,t){return e=a(e),t=o(t),("rgb"===e.type||"hsl"===e.type)&&(e.type+="a"),"color"===e.type?e.values[3]=`/${t}`:e.values[3]=t,c(e)}function p(e,t,r){try{return d(e,t)}catch(t){return e}}function g(e,t){if(e=a(e),t=o(t),e.type.includes("hsl"))e.values[2]*=1-t;else if(e.type.includes("rgb")||e.type.includes("color"))for(let r=0;r<3;r+=1)e.values[r]*=1-t;return c(e)}function h(e,t,r){try{return g(e,t)}catch(t){return e}}function m(e,t){if(e=a(e),t=o(t),e.type.includes("hsl"))e.values[2]+=(100-e.values[2])*t;else if(e.type.includes("rgb"))for(let r=0;r<3;r+=1)e.values[r]+=(255-e.values[r])*t;else if(e.type.includes("color"))for(let r=0;r<3;r+=1)e.values[r]+=(1-e.values[r])*t;return c(e)}function y(e,t,r){try{return m(e,t)}catch(t){return e}}function b(e,t,r){try{return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.15;return u(e)>.5?g(e,t):m(e,t)}(e,t)}catch(t){return e}}},42156:function(e,t,r){r.d(t,{Z:function(){return o}});var n=r(85055);function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:8,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(0,n.hB)({spacing:e});if(e.mui)return e;let r=function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return(0===r.length?[1]:r).map(e=>{let r=t(e);return"number"==typeof r?`${r}px`:r}).join(" ")};return r.mui=!0,r}},58698:function(e,t,r){r.d(t,{Z:function(){return f}});var n=r(87354);let o=e=>{let t=Object.keys(e).map(t=>({key:t,val:e[t]}))||[];return t.sort((e,t)=>e.val-t.val),t.reduce((e,t)=>({...e,[t.key]:t.val}),{})};var a=r(72521),i={borderRadius:4},l=r(42156),c=r(41823),s=r(24190);function u(e,t){if(this.vars){if(!this.colorSchemes?.[e]||"function"!=typeof this.getColorSchemeSelector)return{};let r=this.getColorSchemeSelector(e);return"&"===r?t:((r.includes("data-")||r.includes("."))&&(r=`*:where(${r.replace(/\s*&$/,"")}) &`),{[r]:t})}return this.palette.mode===e?t:{}}var f=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};for(var t=arguments.length,r=Array(t>1?t-1:0),f=1;f<t;f++)r[f-1]=arguments[f];let{breakpoints:d={},palette:p={},spacing:g,shape:h={},...m}=e,y=function(e){let{values:t={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:r="px",step:n=5,...a}=e,i=o(t),l=Object.keys(i);function c(e){let n="number"==typeof t[e]?t[e]:e;return`@media (min-width:${n}${r})`}function s(e){let o="number"==typeof t[e]?t[e]:e;return`@media (max-width:${o-n/100}${r})`}function u(e,o){let a=l.indexOf(o);return`@media (min-width:${"number"==typeof t[e]?t[e]:e}${r}) and (max-width:${(-1!==a&&"number"==typeof t[l[a]]?t[l[a]]:o)-n/100}${r})`}return{keys:l,values:i,up:c,down:s,between:u,only:function(e){return l.indexOf(e)+1<l.length?u(e,l[l.indexOf(e)+1]):c(e)},not:function(e){let t=l.indexOf(e);return 0===t?c(l[1]):t===l.length-1?s(l[t]):u(e,l[l.indexOf(e)+1]).replace("@media","@media not all and")},unit:r,...a}}(d),b=(0,l.Z)(g),v=(0,n.Z)({breakpoints:y,direction:"ltr",components:{},palette:{mode:"light",...p},spacing:b,shape:{...i,...h}},m);return(v=(0,a.ZP)(v)).applyStyles=u,(v=r.reduce((e,t)=>(0,n.Z)(e,t),v)).unstable_sxConfig={...s.Z,...m?.unstable_sxConfig},v.unstable_sx=function(e){return(0,c.Z)({sx:e,theme:this})},v}},72521:function(e,t,r){function n(e,t){if(!e.containerQueries)return t;let r=Object.keys(t).filter(e=>e.startsWith("@container")).sort((e,t)=>{let r=/min-width:\s*([0-9.]+)/;return+(e.match(r)?.[1]||0)-+(t.match(r)?.[1]||0)});return r.length?r.reduce((e,r)=>{let n=t[r];return delete e[r],e[r]=n,e},{...t}):t}function o(e,t){return"@"===t||t.startsWith("@")&&(e.some(e=>t.startsWith(`@${e}`))||!!t.match(/^@\d/))}function a(e,t){let r=t.match(/^@([^/]+)?\/?(.+)?$/);if(!r)return null;let[,n,o]=r,a=Number.isNaN(+n)?n||0:+n;return e.containerQueries(o).up(a)}function i(e){let t=(e,t)=>e.replace("@media",t?`@container ${t}`:"@container");function r(r,n){r.up=function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];return t(e.breakpoints.up(...o),n)},r.down=function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];return t(e.breakpoints.down(...o),n)},r.between=function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];return t(e.breakpoints.between(...o),n)},r.only=function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];return t(e.breakpoints.only(...o),n)},r.not=function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];let i=t(e.breakpoints.not(...o),n);return i.includes("not all and")?i.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):i}}let n={},o=e=>(r(n,e),n);return r(o),{...e,containerQueries:o}}r.d(t,{WX:function(){return o},ZP:function(){return i},ar:function(){return n},ue:function(){return a}})},72097:function(e,t,r){var n=r(87354);t.Z=function(e,t){return t?(0,n.Z)(e,t,{clone:!1}):e}},85055:function(e,t,r){r.d(t,{hB:function(){return g},eI:function(){return p},NA:function(){return h},e6:function(){return y},o3:function(){return b}});var n=r(84586),o=r(44845),a=r(72097);let i={m:"margin",p:"padding"},l={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},c={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},s=function(e){let t={};return r=>(void 0===t[r]&&(t[r]=e(r)),t[r])}(e=>{if(e.length>2){if(!c[e])return[e];e=c[e]}let[t,r]=e.split(""),n=i[t],o=l[r]||"";return Array.isArray(o)?o.map(e=>n+e):[n+o]}),u=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],f=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"],d=[...u,...f];function p(e,t,r,n){let a=(0,o.DW)(e,t,!0)??r;return"number"==typeof a||"string"==typeof a?e=>"string"==typeof e?e:"string"==typeof a?`calc(${e} * ${a})`:a*e:Array.isArray(a)?e=>{if("string"==typeof e)return e;let t=a[Math.abs(e)];return e>=0?t:"number"==typeof t?-t:`-${t}`}:"function"==typeof a?a:()=>void 0}function g(e){return p(e,"spacing",8,"spacing")}function h(e,t){return"string"==typeof t||null==t?t:e(t)}function m(e,t){let r=g(e.theme);return Object.keys(e).map(o=>(function(e,t,r,o){var a;if(!t.includes(r))return null;let i=(a=s(r),e=>a.reduce((t,r)=>(t[r]=h(o,e),t),{})),l=e[r];return(0,n.k9)(e,l,i)})(e,t,o,r)).reduce(a.Z,{})}function y(e){return m(e,u)}function b(e){return m(e,f)}function v(e){return m(e,d)}y.propTypes={},y.filterProps=u,b.propTypes={},b.filterProps=f,v.propTypes={},v.filterProps=d},24190:function(e,t,r){r.d(t,{Z:function(){return q}});var n=r(85055),o=r(44845),a=r(72097),i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=t.reduce((e,t)=>(t.filterProps.forEach(r=>{e[r]=t}),e),{}),o=e=>Object.keys(e).reduce((t,r)=>n[r]?(0,a.Z)(t,n[r](e)):t,{});return o.propTypes={},o.filterProps=t.reduce((e,t)=>e.concat(t.filterProps),[]),o},l=r(84586);function c(e){return"number"!=typeof e?e:`${e}px solid`}function s(e,t){return(0,o.ZP)({prop:e,themeKey:"borders",transform:t})}let u=s("border",c),f=s("borderTop",c),d=s("borderRight",c),p=s("borderBottom",c),g=s("borderLeft",c),h=s("borderColor"),m=s("borderTopColor"),y=s("borderRightColor"),b=s("borderBottomColor"),v=s("borderLeftColor"),k=s("outline",c),x=s("outlineColor"),S=e=>{if(void 0!==e.borderRadius&&null!==e.borderRadius){let t=(0,n.eI)(e.theme,"shape.borderRadius",4,"borderRadius");return(0,l.k9)(e,e.borderRadius,e=>({borderRadius:(0,n.NA)(t,e)}))}return null};S.propTypes={},S.filterProps=["borderRadius"],i(u,f,d,p,g,h,m,y,b,v,S,k,x);let $=e=>{if(void 0!==e.gap&&null!==e.gap){let t=(0,n.eI)(e.theme,"spacing",8,"gap");return(0,l.k9)(e,e.gap,e=>({gap:(0,n.NA)(t,e)}))}return null};$.propTypes={},$.filterProps=["gap"];let A=e=>{if(void 0!==e.columnGap&&null!==e.columnGap){let t=(0,n.eI)(e.theme,"spacing",8,"columnGap");return(0,l.k9)(e,e.columnGap,e=>({columnGap:(0,n.NA)(t,e)}))}return null};A.propTypes={},A.filterProps=["columnGap"];let w=e=>{if(void 0!==e.rowGap&&null!==e.rowGap){let t=(0,n.eI)(e.theme,"spacing",8,"rowGap");return(0,l.k9)(e,e.rowGap,e=>({rowGap:(0,n.NA)(t,e)}))}return null};w.propTypes={},w.filterProps=["rowGap"];let C=(0,o.ZP)({prop:"gridColumn"}),B=(0,o.ZP)({prop:"gridRow"}),O=(0,o.ZP)({prop:"gridAutoFlow"}),P=(0,o.ZP)({prop:"gridAutoColumns"}),T=(0,o.ZP)({prop:"gridAutoRows"}),Z=(0,o.ZP)({prop:"gridTemplateColumns"});function j(e,t){return"grey"===t?t:e}function E(e){return e<=1&&0!==e?`${100*e}%`:e}i($,A,w,C,B,O,P,T,Z,(0,o.ZP)({prop:"gridTemplateRows"}),(0,o.ZP)({prop:"gridTemplateAreas"}),(0,o.ZP)({prop:"gridArea"})),i((0,o.ZP)({prop:"color",themeKey:"palette",transform:j}),(0,o.ZP)({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:j}),(0,o.ZP)({prop:"backgroundColor",themeKey:"palette",transform:j}));let I=(0,o.ZP)({prop:"width",transform:E}),R=e=>void 0!==e.maxWidth&&null!==e.maxWidth?(0,l.k9)(e,e.maxWidth,t=>{let r=e.theme?.breakpoints?.values?.[t]||l.VO[t];return r?e.theme?.breakpoints?.unit!=="px"?{maxWidth:`${r}${e.theme.breakpoints.unit}`}:{maxWidth:r}:{maxWidth:E(t)}}):null;R.filterProps=["maxWidth"];let _=(0,o.ZP)({prop:"minWidth",transform:E}),L=(0,o.ZP)({prop:"height",transform:E}),W=(0,o.ZP)({prop:"maxHeight",transform:E}),F=(0,o.ZP)({prop:"minHeight",transform:E});(0,o.ZP)({prop:"size",cssProperty:"width",transform:E}),(0,o.ZP)({prop:"size",cssProperty:"height",transform:E}),i(I,R,_,L,W,F,(0,o.ZP)({prop:"boxSizing"}));var q={border:{themeKey:"borders",transform:c},borderTop:{themeKey:"borders",transform:c},borderRight:{themeKey:"borders",transform:c},borderBottom:{themeKey:"borders",transform:c},borderLeft:{themeKey:"borders",transform:c},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:c},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:S},color:{themeKey:"palette",transform:j},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:j},backgroundColor:{themeKey:"palette",transform:j},p:{style:n.o3},pt:{style:n.o3},pr:{style:n.o3},pb:{style:n.o3},pl:{style:n.o3},px:{style:n.o3},py:{style:n.o3},padding:{style:n.o3},paddingTop:{style:n.o3},paddingRight:{style:n.o3},paddingBottom:{style:n.o3},paddingLeft:{style:n.o3},paddingX:{style:n.o3},paddingY:{style:n.o3},paddingInline:{style:n.o3},paddingInlineStart:{style:n.o3},paddingInlineEnd:{style:n.o3},paddingBlock:{style:n.o3},paddingBlockStart:{style:n.o3},paddingBlockEnd:{style:n.o3},m:{style:n.e6},mt:{style:n.e6},mr:{style:n.e6},mb:{style:n.e6},ml:{style:n.e6},mx:{style:n.e6},my:{style:n.e6},margin:{style:n.e6},marginTop:{style:n.e6},marginRight:{style:n.e6},marginBottom:{style:n.e6},marginLeft:{style:n.e6},marginX:{style:n.e6},marginY:{style:n.e6},marginInline:{style:n.e6},marginInlineStart:{style:n.e6},marginInlineEnd:{style:n.e6},marginBlock:{style:n.e6},marginBlockStart:{style:n.e6},marginBlockEnd:{style:n.e6},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:$},rowGap:{style:w},columnGap:{style:A},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:E},maxWidth:{style:R},minWidth:{transform:E},height:{transform:E},maxHeight:{transform:E},minHeight:{transform:E},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}}},41823:function(e,t,r){var n=r(4647),o=r(72097),a=r(44845),i=r(84586),l=r(72521),c=r(24190);let s=function(){function e(e,t,r,o){let l={[e]:t,theme:r},c=o[e];if(!c)return{[e]:t};let{cssProperty:s=e,themeKey:u,transform:f,style:d}=c;if(null==t)return null;if("typography"===u&&"inherit"===t)return{[e]:t};let p=(0,a.DW)(r,u)||{};return d?d(l):(0,i.k9)(l,t,t=>{let r=(0,a.Jq)(p,f,t);return(t===r&&"string"==typeof t&&(r=(0,a.Jq)(p,f,`${e}${"default"===t?"":(0,n.Z)(t)}`,t)),!1===s)?r:{[s]:r}})}return function t(r){let{sx:n,theme:a={}}=r||{};if(!n)return null;let s=a.unstable_sxConfig??c.Z;function u(r){let n=r;if("function"==typeof r)n=r(a);else if("object"!=typeof r)return r;if(!n)return null;let c=(0,i.W8)(a.breakpoints),u=Object.keys(c),f=c;return Object.keys(n).forEach(r=>{var l;let c="function"==typeof(l=n[r])?l(a):l;if(null!=c){if("object"==typeof c){if(s[r])f=(0,o.Z)(f,e(r,c,a,s));else{let e=(0,i.k9)({theme:a},c,e=>({[r]:e}));(function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=new Set(t.reduce((e,t)=>e.concat(Object.keys(t)),[]));return t.every(e=>n.size===Object.keys(e).length)})(e,c)?f[r]=t({sx:c,theme:a}):f=(0,o.Z)(f,e)}}else f=(0,o.Z)(f,e(r,c,a,s))}}),(0,l.ar)(a,(0,i.L7)(u,f))}return Array.isArray(n)?n.map(u):u(n)}}();s.filterProps=["sx"],t.Z=s},44845:function(e,t,r){r.d(t,{DW:function(){return a},Jq:function(){return i}});var n=r(4647),o=r(84586);function a(e,t){let r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||"string"!=typeof t)return null;if(e&&e.vars&&r){let r=`vars.${t}`.split(".").reduce((e,t)=>e&&e[t]?e[t]:null,e);if(null!=r)return r}return t.split(".").reduce((e,t)=>e&&null!=e[t]?e[t]:null,e)}function i(e,t,r){let n,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:r;return n="function"==typeof e?e(r):Array.isArray(e)?e[r]||o:a(e,r)||o,t&&(n=t(n,o,e)),n}t.ZP=function(e){let{prop:t,cssProperty:r=e.prop,themeKey:l,transform:c}=e,s=e=>{if(null==e[t])return null;let s=e[t],u=a(e.theme,l)||{};return(0,o.k9)(e,s,e=>{let o=i(u,c,e);return(e===o&&"string"==typeof e&&(o=i(u,c,`${t}${"default"===e?"":(0,n.Z)(e)}`,e)),!1===r)?o:{[r]:o}})};return s.propTypes={},s.filterProps=[t],s}},26792:function(e,t,r){r.d(t,{Z:function(){return U}});var n=r(80399),o=r(87354),a=r(82590),i={black:"#000",white:"#fff"},l={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},c={50:"#f3e5f5",100:"#e1bee7",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",600:"#8e24aa",700:"#7b1fa2",800:"#6a1b9a",900:"#4a148c",A100:"#ea80fc",A200:"#e040fb",A400:"#d500f9",A700:"#aa00ff"},s={50:"#ffebee",100:"#ffcdd2",200:"#ef9a9a",300:"#e57373",400:"#ef5350",500:"#f44336",600:"#e53935",700:"#d32f2f",800:"#c62828",900:"#b71c1c",A100:"#ff8a80",A200:"#ff5252",A400:"#ff1744",A700:"#d50000"},u={50:"#fff3e0",100:"#ffe0b2",200:"#ffcc80",300:"#ffb74d",400:"#ffa726",500:"#ff9800",600:"#fb8c00",700:"#f57c00",800:"#ef6c00",900:"#e65100",A100:"#ffd180",A200:"#ffab40",A400:"#ff9100",A700:"#ff6d00"},f={50:"#e3f2fd",100:"#bbdefb",200:"#90caf9",300:"#64b5f6",400:"#42a5f5",500:"#2196f3",600:"#1e88e5",700:"#1976d2",800:"#1565c0",900:"#0d47a1",A100:"#82b1ff",A200:"#448aff",A400:"#2979ff",A700:"#2962ff"},d={50:"#e1f5fe",100:"#b3e5fc",200:"#81d4fa",300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",600:"#039be5",700:"#0288d1",800:"#0277bd",900:"#01579b",A100:"#80d8ff",A200:"#40c4ff",A400:"#00b0ff",A700:"#0091ea"},p={50:"#e8f5e9",100:"#c8e6c9",200:"#a5d6a7",300:"#81c784",400:"#66bb6a",500:"#4caf50",600:"#43a047",700:"#388e3c",800:"#2e7d32",900:"#1b5e20",A100:"#b9f6ca",A200:"#69f0ae",A400:"#00e676",A700:"#00c853"};function g(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:i.white,default:i.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}let h=g();function m(){return{text:{primary:i.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:i.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}let y=m();function b(e,t,r,n){let o=n.light||n,i=n.dark||1.5*n;e[t]||(e.hasOwnProperty(r)?e[t]=e[r]:"light"===t?e.light=(0,a.$n)(e.main,o):"dark"===t&&(e.dark=(0,a._j)(e.main,i)))}function v(e){let t;let{mode:r="light",contrastThreshold:v=3,tonalOffset:k=.2,...x}=e,S=e.primary||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light";return"dark"===e?{main:f[200],light:f[50],dark:f[400]}:{main:f[700],light:f[400],dark:f[800]}}(r),$=e.secondary||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light";return"dark"===e?{main:c[200],light:c[50],dark:c[400]}:{main:c[500],light:c[300],dark:c[700]}}(r),A=e.error||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light";return"dark"===e?{main:s[500],light:s[300],dark:s[700]}:{main:s[700],light:s[400],dark:s[800]}}(r),w=e.info||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light";return"dark"===e?{main:d[400],light:d[300],dark:d[700]}:{main:d[700],light:d[500],dark:d[900]}}(r),C=e.success||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light";return"dark"===e?{main:p[400],light:p[300],dark:p[700]}:{main:p[800],light:p[500],dark:p[900]}}(r),B=e.warning||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light";return"dark"===e?{main:u[400],light:u[300],dark:u[700]}:{main:"#ed6c02",light:u[500],dark:u[900]}}(r);function O(e){return(0,a.mi)(e,y.text.primary)>=v?y.text.primary:h.text.primary}let P=e=>{let{color:t,name:r,mainShade:o=500,lightShade:a=300,darkShade:i=700}=e;if(!(t={...t}).main&&t[o]&&(t.main=t[o]),!t.hasOwnProperty("main"))throw Error((0,n.Z)(11,r?` (${r})`:"",o));if("string"!=typeof t.main)throw Error((0,n.Z)(12,r?` (${r})`:"",JSON.stringify(t.main)));return b(t,"light",a,k),b(t,"dark",i,k),t.contrastText||(t.contrastText=O(t.main)),t};return"light"===r?t=g():"dark"===r&&(t=m()),(0,o.Z)({common:{...i},mode:r,primary:P({color:S,name:"primary"}),secondary:P({color:$,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:P({color:A,name:"error"}),warning:P({color:B,name:"warning"}),info:P({color:w,name:"info"}),success:P({color:C,name:"success"}),grey:l,contrastThreshold:v,getContrastText:O,augmentColor:P,tonalOffset:k,...t},x)}var k=r(42156),x=r(85055);let S=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],o=e;t.forEach((e,a)=>{a===t.length-1?Array.isArray(o)?o[Number(e)]=r:o&&"object"==typeof o&&(o[e]=r):o&&"object"==typeof o&&(o[e]||(o[e]=n.includes(e)?[]:{}),o=o[e])})},$=(e,t,r)=>{!function e(n){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];Object.entries(n).forEach(n=>{let[i,l]=n;r&&(!r||r([...o,i]))||null==l||("object"==typeof l&&Object.keys(l).length>0?e(l,[...o,i],Array.isArray(l)?[...a,i]:a):t([...o,i],l,a))})}(e)},A=(e,t)=>"number"==typeof t?["lineHeight","fontWeight","opacity","zIndex"].some(t=>e.includes(t))||e[e.length-1].toLowerCase().includes("opacity")?t:`${t}px`:t;function w(e,t){let{prefix:r,shouldSkipGeneratingVar:n}=t||{},o={},a={},i={};return $(e,(e,t,l)=>{if(("string"==typeof t||"number"==typeof t)&&(!n||!n(e,t))){let n=`--${r?`${r}-`:""}${e.join("-")}`,c=A(e,t);Object.assign(o,{[n]:c}),S(a,e,`var(${n})`,l),S(i,e,`var(${n}, ${c})`,l)}},e=>"vars"===e[0]),{css:o,vars:a,varsWithDefaults:i}}var C=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{getSelector:r=function(t,r){let n=a;if("class"===a&&(n=".%s"),"data"===a&&(n="[data-%s]"),a?.startsWith("data-")&&!a.includes("%s")&&(n=`[${a}="%s"]`),t){if("media"===n){if(e.defaultColorScheme===t)return":root";let n=i[t]?.palette?.mode||t;return{[`@media (prefers-color-scheme: ${n})`]:{":root":r}}}if(n)return e.defaultColorScheme===t?`:root, ${n.replace("%s",String(t))}`:n.replace("%s",String(t))}return":root"},disableCssColorScheme:n,colorSchemeSelector:a}=t,{colorSchemes:i={},components:l,defaultColorScheme:c="light",...s}=e,{vars:u,css:f,varsWithDefaults:d}=w(s,t),p=d,g={},{[c]:h,...m}=i;if(Object.entries(m||{}).forEach(e=>{let[r,n]=e,{vars:a,css:i,varsWithDefaults:l}=w(n,t);p=(0,o.Z)(p,l),g[r]={css:i,vars:a}}),h){let{css:e,vars:r,varsWithDefaults:n}=w(h,t);p=(0,o.Z)(p,n),g[c]={css:e,vars:r}}return{vars:p,generateThemeVars:()=>{let e={...u};return Object.entries(g).forEach(t=>{let[,{vars:r}]=t;e=(0,o.Z)(e,r)}),e},generateStyleSheets:()=>{let t=[],o=e.defaultColorScheme||"light";function a(e,r){Object.keys(r).length&&t.push("string"==typeof e?{[e]:{...r}}:e)}a(r(void 0,{...f}),f);let{[o]:l,...c}=g;if(l){let{css:e}=l,t=i[o]?.palette?.mode,c=!n&&t?{colorScheme:t,...e}:{...e};a(r(o,{...c}),c)}return Object.entries(c).forEach(e=>{let[t,{css:o}]=e,l=i[t]?.palette?.mode,c=!n&&l?{colorScheme:l,...o}:{...o};a(r(t,{...c}),c)}),t}}},B=r(24190),O=r(41823),P=r(58698),T=r(84792);function Z(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return`${t[0]}px ${t[1]}px ${t[2]}px ${t[3]}px rgba(0,0,0,0.2),${t[4]}px ${t[5]}px ${t[6]}px ${t[7]}px rgba(0,0,0,0.14),${t[8]}px ${t[9]}px ${t[10]}px ${t[11]}px rgba(0,0,0,0.12)`}let j=["none",Z(0,2,1,-1,0,1,1,0,0,1,3,0),Z(0,3,1,-2,0,2,2,0,0,1,5,0),Z(0,3,3,-2,0,3,4,0,0,1,8,0),Z(0,2,4,-1,0,4,5,0,0,1,10,0),Z(0,3,5,-1,0,5,8,0,0,1,14,0),Z(0,3,5,-1,0,6,10,0,0,1,18,0),Z(0,4,5,-2,0,7,10,1,0,2,16,1),Z(0,5,5,-3,0,8,10,1,0,3,14,2),Z(0,5,6,-3,0,9,12,1,0,3,16,2),Z(0,6,6,-3,0,10,14,1,0,4,18,3),Z(0,6,7,-4,0,11,15,1,0,4,20,3),Z(0,7,8,-4,0,12,17,2,0,5,22,4),Z(0,7,8,-4,0,13,19,2,0,5,24,4),Z(0,7,9,-4,0,14,21,2,0,5,26,4),Z(0,8,9,-5,0,15,22,2,0,6,28,5),Z(0,8,10,-5,0,16,24,2,0,6,30,5),Z(0,8,11,-5,0,17,26,2,0,6,32,5),Z(0,9,11,-5,0,18,28,2,0,7,34,6),Z(0,9,12,-6,0,19,29,2,0,7,36,6),Z(0,10,13,-6,0,20,31,3,0,8,38,7),Z(0,10,13,-6,0,21,33,3,0,8,40,7),Z(0,10,14,-6,0,22,35,3,0,8,42,7),Z(0,11,14,-7,0,23,36,3,0,9,44,8),Z(0,11,15,-7,0,24,38,3,0,9,46,8)];var E=r(73220),I={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function R(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={...e};return!function e(t){let r=Object.entries(t);for(let n=0;n<r.length;n++){let[a,i]=r[n];!((0,o.P)(i)||void 0===i||"string"==typeof i||"boolean"==typeof i||"number"==typeof i||Array.isArray(i))||a.startsWith("unstable_")?delete t[a]:(0,o.P)(i)&&(t[a]={...i},e(t[a]))}}(t),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(t,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}var _=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};for(var t,r=arguments.length,a=Array(r>1?r-1:0),i=1;i<r;i++)a[i-1]=arguments[i];let{breakpoints:l,mixins:c={},spacing:s,palette:u={},transitions:f={},typography:d={},shape:p,...g}=e;if(e.vars&&void 0===e.generateThemeVars)throw Error((0,n.Z)(20));let h=v(u),m=(0,P.Z)(e),y=(0,o.Z)(m,{mixins:{toolbar:{minHeight:56,[(t=m.breakpoints).up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[t.up("sm")]:{minHeight:64}},...c},palette:h,shadows:j.slice(),typography:(0,T.Z)(h,d),transitions:(0,E.ZP)(f),zIndex:{...I}});return y=(0,o.Z)(y,g),(y=a.reduce((e,t)=>(0,o.Z)(e,t),y)).unstable_sxConfig={...B.Z,...g?.unstable_sxConfig},y.unstable_sx=function(e){return(0,O.Z)({sx:e,theme:this})},y.toRuntimeSource=R,y},L=r(46821);let W=[...Array(25)].map((e,t)=>{if(0===t)return"none";let r=(0,L.Z)(t);return`linear-gradient(rgba(255 255 255 / ${r}), rgba(255 255 255 / ${r}))`});function F(e){return{inputPlaceholder:"dark"===e?.5:.42,inputUnderline:"dark"===e?.7:.42,switchTrackDisabled:"dark"===e?.2:.12,switchTrack:"dark"===e?.3:.38}}function q(e){return"dark"===e?W:[]}function M(e){return!!e[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!e[0].match(/sxConfig$/)||"palette"===e[0]&&!!e[1]?.match(/(mode|contrastThreshold|tonalOffset)/)}var N=e=>[...[...Array(25)].map((t,r)=>`--${e?`${e}-`:""}overlays-${r}`),`--${e?`${e}-`:""}palette-AppBar-darkBg`,`--${e?`${e}-`:""}palette-AppBar-darkColor`],z=e=>(t,r)=>{let n=e.rootSelector||":root",o=e.colorSchemeSelector,a=o;if("class"===o&&(a=".%s"),"data"===o&&(a="[data-%s]"),o?.startsWith("data-")&&!o.includes("%s")&&(a=`[${o}="%s"]`),e.defaultColorScheme===t){if("dark"===t){let o={};return(N(e.cssVarPrefix).forEach(e=>{o[e]=r[e],delete r[e]}),"media"===a)?{[n]:r,"@media (prefers-color-scheme: dark)":{[n]:o}}:a?{[a.replace("%s",t)]:o,[`${n}, ${a.replace("%s",t)}`]:r}:{[n]:{...r,...o}}}if(a&&"media"!==a)return`${n}, ${a.replace("%s",String(t))}`}else if(t){if("media"===a)return{[`@media (prefers-color-scheme: ${String(t)})`]:{[n]:r}};if(a)return a.replace("%s",String(t))}return n};function K(e,t,r){!e[t]&&r&&(e[t]=r)}function D(e){return"string"==typeof e&&e.startsWith("hsl")?(0,a.ve)(e):e}function G(e,t){`${t}Channel` in e||(e[`${t}Channel`]=(0,a.LR)(D(e[t]),`MUI: Can't create \`palette.${t}Channel\` because \`palette.${t}\` is not one of these formats: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color().
To suppress this warning, you need to explicitly provide the \`palette.${t}Channel\` as a string (in rgb format, for example "12 12 12") or undefined if you want to remove the channel token.`))}let H=e=>{try{return e()}catch(e){}},V=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"mui";return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return`var(--${e?`${e}-`:""}${t}${function t(){for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];if(!n.length)return"";let a=n[0];return"string"!=typeof a||a.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, ${a}`:`, var(--${e?`${e}-`:""}${a}${t(...n.slice(1))})`}(...n)})`}}(e)};function Y(e,t,r,n){if(!t)return;t=!0===t?{}:t;let o="dark"===n?"dark":"light";if(!r){e[n]=function(e){let{palette:t={mode:"light"},opacity:r,overlays:n,...o}=e,a=v(t);return{palette:a,opacity:{...F(a.mode),...r},overlays:n||q(a.mode),...o}}({...t,palette:{mode:o,...t?.palette}});return}let{palette:a,...i}=_({...r,palette:{mode:o,...t?.palette}});return e[n]={...t,palette:a,opacity:{...F(o),...t?.opacity},overlays:t?.overlays||q(o)},i}function X(e,t,r){e.colorSchemes&&r&&(e.colorSchemes[t]={...!0!==r&&r,palette:v({...!0===r?{}:r.palette,mode:t})})}function U(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];let{palette:l,cssVariables:c=!1,colorSchemes:s=l?void 0:{light:!0},defaultColorScheme:u=l?.mode,...f}=e,d=u||"light",p=s?.[d],g={...s,...l?{[d]:{..."boolean"!=typeof p&&p,palette:l}}:void 0};if(!1===c){if(!("colorSchemes"in e))return _(e,...r);let t=l;"palette"in e||!g[d]||(!0!==g[d]?t=g[d].palette:"dark"!==d||(t={mode:"dark"}));let n=_({...e,palette:t},...r);return n.defaultColorScheme=d,n.colorSchemes=g,"light"===n.palette.mode&&(n.colorSchemes.light={...!0!==g.light&&g.light,palette:n.palette},X(n,"dark",g.dark)),"dark"===n.palette.mode&&(n.colorSchemes.dark={...!0!==g.dark&&g.dark,palette:n.palette},X(n,"light",g.light)),n}return l||"light"in g||"light"!==d||(g.light=!0),function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};for(var t,r=arguments.length,i=Array(r>1?r-1:0),l=1;l<r;l++)i[l-1]=arguments[l];let{colorSchemes:c={light:!0},defaultColorScheme:s,disableCssColorScheme:u=!1,cssVarPrefix:f="mui",shouldSkipGeneratingVar:d=M,colorSchemeSelector:p=c.light&&c.dark?"media":void 0,rootSelector:g=":root",...h}=e,m=Object.keys(c)[0],y=s||(c.light&&"light"!==m?"light":m),b=V(f),{[y]:v,light:S,dark:$,...A}=c,w={...A},P=v;if(("dark"!==y||"dark"in c)&&("light"!==y||"light"in c)||(P=!0),!P)throw Error((0,n.Z)(21,y));let T=Y(w,P,h,y);S&&!w.light&&Y(w,S,void 0,"light"),$&&!w.dark&&Y(w,$,void 0,"dark");let Z={defaultColorScheme:y,...T,cssVarPrefix:f,colorSchemeSelector:p,rootSelector:g,getCssVar:b,colorSchemes:w,font:{...function(e){let t={};return Object.entries(e).forEach(e=>{let[r,n]=e;"object"==typeof n&&(t[r]=`${n.fontStyle?`${n.fontStyle} `:""}${n.fontVariant?`${n.fontVariant} `:""}${n.fontWeight?`${n.fontWeight} `:""}${n.fontStretch?`${n.fontStretch} `:""}${n.fontSize||""}${n.lineHeight?`/${n.lineHeight} `:""}${n.fontFamily||""}`)}),t}(T.typography),...T.font},spacing:"number"==typeof(t=h.spacing)?`${t}px`:"string"==typeof t||"function"==typeof t||Array.isArray(t)?t:"8px"};Object.keys(Z.colorSchemes).forEach(e=>{let t=Z.colorSchemes[e].palette,r=e=>{let r=e.split("-"),n=r[1],o=r[2];return b(e,t[n][o])};if("light"===t.mode&&(K(t.common,"background","#fff"),K(t.common,"onBackground","#000")),"dark"===t.mode&&(K(t.common,"background","#000"),K(t.common,"onBackground","#fff")),function(e,t){t.forEach(t=>{e[t]||(e[t]={})})}(t,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),"light"===t.mode){K(t.Alert,"errorColor",(0,a.q8)(t.error.light,.6)),K(t.Alert,"infoColor",(0,a.q8)(t.info.light,.6)),K(t.Alert,"successColor",(0,a.q8)(t.success.light,.6)),K(t.Alert,"warningColor",(0,a.q8)(t.warning.light,.6)),K(t.Alert,"errorFilledBg",r("palette-error-main")),K(t.Alert,"infoFilledBg",r("palette-info-main")),K(t.Alert,"successFilledBg",r("palette-success-main")),K(t.Alert,"warningFilledBg",r("palette-warning-main")),K(t.Alert,"errorFilledColor",H(()=>t.getContrastText(t.error.main))),K(t.Alert,"infoFilledColor",H(()=>t.getContrastText(t.info.main))),K(t.Alert,"successFilledColor",H(()=>t.getContrastText(t.success.main))),K(t.Alert,"warningFilledColor",H(()=>t.getContrastText(t.warning.main))),K(t.Alert,"errorStandardBg",(0,a.ux)(t.error.light,.9)),K(t.Alert,"infoStandardBg",(0,a.ux)(t.info.light,.9)),K(t.Alert,"successStandardBg",(0,a.ux)(t.success.light,.9)),K(t.Alert,"warningStandardBg",(0,a.ux)(t.warning.light,.9)),K(t.Alert,"errorIconColor",r("palette-error-main")),K(t.Alert,"infoIconColor",r("palette-info-main")),K(t.Alert,"successIconColor",r("palette-success-main")),K(t.Alert,"warningIconColor",r("palette-warning-main")),K(t.AppBar,"defaultBg",r("palette-grey-100")),K(t.Avatar,"defaultBg",r("palette-grey-400")),K(t.Button,"inheritContainedBg",r("palette-grey-300")),K(t.Button,"inheritContainedHoverBg",r("palette-grey-A100")),K(t.Chip,"defaultBorder",r("palette-grey-400")),K(t.Chip,"defaultAvatarColor",r("palette-grey-700")),K(t.Chip,"defaultIconColor",r("palette-grey-700")),K(t.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),K(t.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),K(t.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),K(t.LinearProgress,"primaryBg",(0,a.ux)(t.primary.main,.62)),K(t.LinearProgress,"secondaryBg",(0,a.ux)(t.secondary.main,.62)),K(t.LinearProgress,"errorBg",(0,a.ux)(t.error.main,.62)),K(t.LinearProgress,"infoBg",(0,a.ux)(t.info.main,.62)),K(t.LinearProgress,"successBg",(0,a.ux)(t.success.main,.62)),K(t.LinearProgress,"warningBg",(0,a.ux)(t.warning.main,.62)),K(t.Skeleton,"bg",`rgba(${r("palette-text-primaryChannel")} / 0.11)`),K(t.Slider,"primaryTrack",(0,a.ux)(t.primary.main,.62)),K(t.Slider,"secondaryTrack",(0,a.ux)(t.secondary.main,.62)),K(t.Slider,"errorTrack",(0,a.ux)(t.error.main,.62)),K(t.Slider,"infoTrack",(0,a.ux)(t.info.main,.62)),K(t.Slider,"successTrack",(0,a.ux)(t.success.main,.62)),K(t.Slider,"warningTrack",(0,a.ux)(t.warning.main,.62));let e=(0,a.fk)(t.background.default,.8);K(t.SnackbarContent,"bg",e),K(t.SnackbarContent,"color",H(()=>t.getContrastText(e))),K(t.SpeedDialAction,"fabHoverBg",(0,a.fk)(t.background.paper,.15)),K(t.StepConnector,"border",r("palette-grey-400")),K(t.StepContent,"border",r("palette-grey-400")),K(t.Switch,"defaultColor",r("palette-common-white")),K(t.Switch,"defaultDisabledColor",r("palette-grey-100")),K(t.Switch,"primaryDisabledColor",(0,a.ux)(t.primary.main,.62)),K(t.Switch,"secondaryDisabledColor",(0,a.ux)(t.secondary.main,.62)),K(t.Switch,"errorDisabledColor",(0,a.ux)(t.error.main,.62)),K(t.Switch,"infoDisabledColor",(0,a.ux)(t.info.main,.62)),K(t.Switch,"successDisabledColor",(0,a.ux)(t.success.main,.62)),K(t.Switch,"warningDisabledColor",(0,a.ux)(t.warning.main,.62)),K(t.TableCell,"border",(0,a.ux)((0,a.zp)(t.divider,1),.88)),K(t.Tooltip,"bg",(0,a.zp)(t.grey[700],.92))}if("dark"===t.mode){K(t.Alert,"errorColor",(0,a.ux)(t.error.light,.6)),K(t.Alert,"infoColor",(0,a.ux)(t.info.light,.6)),K(t.Alert,"successColor",(0,a.ux)(t.success.light,.6)),K(t.Alert,"warningColor",(0,a.ux)(t.warning.light,.6)),K(t.Alert,"errorFilledBg",r("palette-error-dark")),K(t.Alert,"infoFilledBg",r("palette-info-dark")),K(t.Alert,"successFilledBg",r("palette-success-dark")),K(t.Alert,"warningFilledBg",r("palette-warning-dark")),K(t.Alert,"errorFilledColor",H(()=>t.getContrastText(t.error.dark))),K(t.Alert,"infoFilledColor",H(()=>t.getContrastText(t.info.dark))),K(t.Alert,"successFilledColor",H(()=>t.getContrastText(t.success.dark))),K(t.Alert,"warningFilledColor",H(()=>t.getContrastText(t.warning.dark))),K(t.Alert,"errorStandardBg",(0,a.q8)(t.error.light,.9)),K(t.Alert,"infoStandardBg",(0,a.q8)(t.info.light,.9)),K(t.Alert,"successStandardBg",(0,a.q8)(t.success.light,.9)),K(t.Alert,"warningStandardBg",(0,a.q8)(t.warning.light,.9)),K(t.Alert,"errorIconColor",r("palette-error-main")),K(t.Alert,"infoIconColor",r("palette-info-main")),K(t.Alert,"successIconColor",r("palette-success-main")),K(t.Alert,"warningIconColor",r("palette-warning-main")),K(t.AppBar,"defaultBg",r("palette-grey-900")),K(t.AppBar,"darkBg",r("palette-background-paper")),K(t.AppBar,"darkColor",r("palette-text-primary")),K(t.Avatar,"defaultBg",r("palette-grey-600")),K(t.Button,"inheritContainedBg",r("palette-grey-800")),K(t.Button,"inheritContainedHoverBg",r("palette-grey-700")),K(t.Chip,"defaultBorder",r("palette-grey-700")),K(t.Chip,"defaultAvatarColor",r("palette-grey-300")),K(t.Chip,"defaultIconColor",r("palette-grey-300")),K(t.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),K(t.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),K(t.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),K(t.LinearProgress,"primaryBg",(0,a.q8)(t.primary.main,.5)),K(t.LinearProgress,"secondaryBg",(0,a.q8)(t.secondary.main,.5)),K(t.LinearProgress,"errorBg",(0,a.q8)(t.error.main,.5)),K(t.LinearProgress,"infoBg",(0,a.q8)(t.info.main,.5)),K(t.LinearProgress,"successBg",(0,a.q8)(t.success.main,.5)),K(t.LinearProgress,"warningBg",(0,a.q8)(t.warning.main,.5)),K(t.Skeleton,"bg",`rgba(${r("palette-text-primaryChannel")} / 0.13)`),K(t.Slider,"primaryTrack",(0,a.q8)(t.primary.main,.5)),K(t.Slider,"secondaryTrack",(0,a.q8)(t.secondary.main,.5)),K(t.Slider,"errorTrack",(0,a.q8)(t.error.main,.5)),K(t.Slider,"infoTrack",(0,a.q8)(t.info.main,.5)),K(t.Slider,"successTrack",(0,a.q8)(t.success.main,.5)),K(t.Slider,"warningTrack",(0,a.q8)(t.warning.main,.5));let e=(0,a.fk)(t.background.default,.98);K(t.SnackbarContent,"bg",e),K(t.SnackbarContent,"color",H(()=>t.getContrastText(e))),K(t.SpeedDialAction,"fabHoverBg",(0,a.fk)(t.background.paper,.15)),K(t.StepConnector,"border",r("palette-grey-600")),K(t.StepContent,"border",r("palette-grey-600")),K(t.Switch,"defaultColor",r("palette-grey-300")),K(t.Switch,"defaultDisabledColor",r("palette-grey-600")),K(t.Switch,"primaryDisabledColor",(0,a.q8)(t.primary.main,.55)),K(t.Switch,"secondaryDisabledColor",(0,a.q8)(t.secondary.main,.55)),K(t.Switch,"errorDisabledColor",(0,a.q8)(t.error.main,.55)),K(t.Switch,"infoDisabledColor",(0,a.q8)(t.info.main,.55)),K(t.Switch,"successDisabledColor",(0,a.q8)(t.success.main,.55)),K(t.Switch,"warningDisabledColor",(0,a.q8)(t.warning.main,.55)),K(t.TableCell,"border",(0,a.q8)((0,a.zp)(t.divider,1),.68)),K(t.Tooltip,"bg",(0,a.zp)(t.grey[700],.92))}G(t.background,"default"),G(t.background,"paper"),G(t.common,"background"),G(t.common,"onBackground"),G(t,"divider"),Object.keys(t).forEach(e=>{let r=t[e];"tonalOffset"!==e&&r&&"object"==typeof r&&(r.main&&K(t[e],"mainChannel",(0,a.LR)(D(r.main))),r.light&&K(t[e],"lightChannel",(0,a.LR)(D(r.light))),r.dark&&K(t[e],"darkChannel",(0,a.LR)(D(r.dark))),r.contrastText&&K(t[e],"contrastTextChannel",(0,a.LR)(D(r.contrastText))),"text"===e&&(G(t[e],"primary"),G(t[e],"secondary")),"action"===e&&(r.active&&G(t[e],"active"),r.selected&&G(t[e],"selected")))})});let j={prefix:f,disableCssColorScheme:u,shouldSkipGeneratingVar:d,getSelector:z(Z=i.reduce((e,t)=>(0,o.Z)(e,t),Z))},{vars:E,generateThemeVars:I,generateStyleSheets:_}=C(Z,j);return Z.vars=E,Object.entries(Z.colorSchemes[Z.defaultColorScheme]).forEach(e=>{let[t,r]=e;Z[t]=r}),Z.generateThemeVars=I,Z.generateStyleSheets=_,Z.generateSpacing=function(){return(0,k.Z)(h.spacing,(0,x.hB)(this))},Z.getColorSchemeSelector=function(e){return"media"===p?`@media (prefers-color-scheme: ${e})`:p?p.startsWith("data-")&&!p.includes("%s")?`[${p}="${e}"] &`:"class"===p?`.${e} &`:"data"===p?`[data-${e}] &`:`${p.replace("%s",e)} &`:"&"},Z.spacing=Z.generateSpacing(),Z.shouldSkipGeneratingVar=d,Z.unstable_sxConfig={...B.Z,...h?.unstable_sxConfig},Z.unstable_sx=function(e){return(0,O.Z)({sx:e,theme:this})},Z.toRuntimeSource=R,Z}({...f,colorSchemes:g,defaultColorScheme:d,..."boolean"!=typeof c&&c},...r)}},73220:function(e,t,r){r.d(t,{ZP:function(){return l},x9:function(){return o}});let n={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},o={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function a(e){return`${Math.round(e)}ms`}function i(e){if(!e)return 0;let t=e/36;return Math.min(Math.round((4+15*t**.25+t/5)*10),3e3)}function l(e){let t={...n,...e.easing},r={...o,...e.duration};return{getAutoHeightDuration:i,create:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["all"],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{duration:o=r.standard,easing:i=t.easeInOut,delay:l=0,...c}=n;return(Array.isArray(e)?e:[e]).map(e=>`${e} ${"string"==typeof o?o:a(o)} ${i} ${"string"==typeof l?l:a(l)}`).join(",")},...e,easing:t,duration:r}}},84792:function(e,t,r){r.d(t,{Z:function(){return i}});var n=r(87354);let o={textTransform:"uppercase"},a='"Roboto", "Helvetica", "Arial", sans-serif';function i(e,t){let{fontFamily:r=a,fontSize:i=14,fontWeightLight:l=300,fontWeightRegular:c=400,fontWeightMedium:s=500,fontWeightBold:u=700,htmlFontSize:f=16,allVariants:d,pxToRem:p,...g}="function"==typeof t?t(e):t,h=i/14,m=p||(e=>`${e/f*h}rem`),y=(e,t,n,o,i)=>({fontFamily:r,fontWeight:e,fontSize:m(t),lineHeight:n,...r===a?{letterSpacing:`${Math.round(o/t*1e5)/1e5}em`}:{},...i,...d}),b={h1:y(l,96,1.167,-1.5),h2:y(l,60,1.2,-.5),h3:y(c,48,1.167,0),h4:y(c,34,1.235,.25),h5:y(c,24,1.334,0),h6:y(s,20,1.6,.15),subtitle1:y(c,16,1.75,.15),subtitle2:y(s,14,1.57,.1),body1:y(c,16,1.5,.15),body2:y(c,14,1.43,.15),button:y(s,14,1.75,.4,o),caption:y(c,12,1.66,.4),overline:y(c,12,2.66,1,o),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return(0,n.Z)({htmlFontSize:f,pxToRem:m,fontFamily:r,fontSize:i,fontWeightLight:l,fontWeightRegular:c,fontWeightMedium:s,fontWeightBold:u,...b},g,{clone:!1})}},55201:function(e,t,r){let n=(0,r(26792).Z)();t.Z=n},46821:function(e,t,r){r.d(t,{Z:function(){return n}});function n(e){return Math.round(10*(e<1?5.11916*e**2:4.5*Math.log(e+1)+2))/1e3}},22166:function(e,t){t.Z="$$material"},4647:function(e,t,r){r.d(t,{Z:function(){return o}});var n=r(80399);function o(e){if("string"!=typeof e)throw Error((0,n.Z)(7));return e.charAt(0).toUpperCase()+e.slice(1)}},87354:function(e,t,r){r.d(t,{P:function(){return a},Z:function(){return function e(t,r,i={clone:!0}){let l=i.clone?{...t}:t;return a(t)&&a(r)&&Object.keys(r).forEach(c=>{n.isValidElement(r[c])||(0,o.iY)(r[c])?l[c]=r[c]:a(r[c])&&Object.prototype.hasOwnProperty.call(t,c)&&a(t[c])?l[c]=e(t[c],r[c],i):i.clone?l[c]=a(r[c])?function e(t){if(n.isValidElement(t)||(0,o.iY)(t)||!a(t))return t;let r={};return Object.keys(t).forEach(n=>{r[n]=e(t[n])}),r}(r[c]):r[c]:l[c]=r[c]}),l}}});var n=r(2265),o=r(59679);function a(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}},80399:function(e,t,r){r.d(t,{Z:function(){return n}});function n(e,...t){let r=new URL(`https://mui.com/production-error/?code=${e}`);return t.forEach(e=>r.searchParams.append("args[]",e)),`Minified MUI error #${e}; visit ${r} for the full message.`}},53232:function(e,t,r){r.d(t,{Z:function(){return function e(t,r){let n={...r};for(let o in t)if(Object.prototype.hasOwnProperty.call(t,o)){if("components"===o||"slots"===o)n[o]={...t[o],...n[o]};else if("componentsProps"===o||"slotProps"===o){let a=t[o],i=r[o];if(i){if(a)for(let t in n[o]={...i},a)Object.prototype.hasOwnProperty.call(a,t)&&(n[o][t]=e(a[t],i[t]));else n[o]=i}else n[o]=a||{}}else void 0===n[o]&&(n[o]=t[o])}return n}}})},59679:function(e,t){Symbol.for("react.transitional.element"),Symbol.for("react.portal");var r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler");Symbol.for("react.provider");var a=Symbol.for("react.consumer"),i=Symbol.for("react.context"),l=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),s=Symbol.for("react.suspense_list"),u=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),d=(Symbol.for("react.view_transition"),Symbol.for("react.client.reference"));t.iY=function(e){return"string"==typeof e||"function"==typeof e||e===r||e===o||e===n||e===c||e===s||"object"==typeof e&&null!==e&&(e.$$typeof===f||e.$$typeof===u||e.$$typeof===i||e.$$typeof===a||e.$$typeof===l||e.$$typeof===d||void 0!==e.getModuleId)}}}]);