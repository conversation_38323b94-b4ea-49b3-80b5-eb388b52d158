(()=>{var e={};e.id=6814,e.ids=[6814],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},89022:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,originalPathname:()=>u,pages:()=>p,routeModule:()=>v,tree:()=>c}),r(83436),r(30962),r(23658),r(54864);var s=r(23191),i=r(88716),a=r(37922),n=r.n(a),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["[locale]",{children:["(website)",{children:["activation",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,83436)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\activation\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,30962)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\layout.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,23658)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54864)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\not-found.js"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\activation\\page.jsx"],u="/[locale]/(website)/activation/page",d={require:r,loadChunk:()=>Promise.resolve()},v=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/[locale]/(website)/activation/page",pathname:"/[locale]/activation",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},60344:(e,t,r)=>{Promise.resolve().then(r.bind(r,77041))},65368:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a}),r(17577);var s=r(27522),i=r(10326);let a=(0,s.Z)((0,i.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},77041:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(10326),i=r(17577),a=r(70580);class n extends Error{}n.prototype.name="InvalidTokenError";var o=r(50967),l=r(35047),c=r(97980),p=r(86851);let u=({token:e,t})=>{let[r,u]=(0,i.useState)(!1),[d,v]=(0,i.useState)("pending"),x=(0,i.useRef)(d),f=(0,l.useRouter)();return(0,i.useEffect)(()=>{let t=async e=>{if(u(!0),e)try{let t=function(e,t){let r;if("string"!=typeof e)throw new n("Invalid token specified: must be a string");t||(t={});let s=!0===t.header?0:1,i=e.split(".")[s];if("string"!=typeof i)throw new n(`Invalid token specified: missing part #${s+1}`);try{r=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw Error("base64 string is not of the correct length")}try{var r;return r=t,decodeURIComponent(atob(r).replace(/(.)/g,(e,t)=>{let r=t.charCodeAt(0).toString(16).toUpperCase();return r.length<2&&(r="0"+r),"%"+r}))}catch(e){return atob(t)}}(i)}catch(e){throw new n(`Invalid token specified: invalid base64 for part #${s+1} (${e.message})`)}try{return JSON.parse(r)}catch(e){throw new n(`Invalid token specified: invalid json for part #${s+1} (${e.message})`)}}(e),r=new Date;if(1e3*t.exp<r.getTime()){f.push(`/${c.jb.resend.route}`);return}(await a.yX.get(`${o.v}/activation/${e}`)).data.alreadyActivated?v("alreadyActivated"):v("success")}catch(e){v("failed")}finally{u(!1)}};"pending"===x.current&&(t(e),x.current="processing")},[e,f,t,r]),(0,i.useEffect)(()=>{"pending"===d?window.location.href=`/${c.jb.login.route}/?error=${t("activation:messagepending")}`:"success"===d?window.location.href="/activation-account":"alreadyActivated"===d&&(window.location.href=`/${c.jb.login.route}/?error=${t("activation:messagepending")}`)},[d,t]),s.jsx("div",{children:s.jsx(p.Z,{})})};var d=r(52210);function v({searchParams:e}){let{token:t}=e,{t:r}=(0,d.$G)();return s.jsx(u,{token:t,t:r})}},86851:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(10326),i=r(98139);let a=function(){return s.jsx("div",{className:"spinner",children:s.jsx(i.default,{})})}},83436:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\app\[locale]\(website)\activation\page.jsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1479,1619,1336,4227,8077,6027,3718,4289,1692,1812,3969,4903],()=>r(89022));module.exports=s})();