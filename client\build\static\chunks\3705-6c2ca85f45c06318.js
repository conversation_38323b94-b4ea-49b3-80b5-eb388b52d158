"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3705],{44164:function(e,t,r){r.d(t,{Z:function(){return m}});var n=r(2265),i=r(61994),a=r(20801),o=r(16210),s=r(76301),l=r(37053),u=r(94143),d=r(50738);function c(e){return(0,d.ZP)("MuiAccordionDetails",e)}(0,u.Z)("MuiAccordionDetails",["root"]);var h=r(57437);let p=e=>{let{classes:t}=e;return(0,a.Z)({root:["root"]},c,t)},f=(0,o.ZP)("div",{name:"MuiAccordionDetails",slot:"Root",overridesResolver:(e,t)=>t.root})((0,s.Z)(e=>{let{theme:t}=e;return{padding:t.spacing(1,2,2)}}));var m=n.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiAccordionDetails"}),{className:n,...a}=r,o=p(r);return(0,h.jsx)(f,{className:(0,i.Z)(o.root,n),ref:t,ownerState:r,...a})})},96369:function(e,t,r){r.d(t,{Z:function(){return Z}});var n=r(2265),i=r(61994),a=r(20801),o=r(16210),s=r(76301),l=r(37053),u=r(82662),d=r(31288),c=r(94143),h=r(50738);function p(e){return(0,h.ZP)("MuiAccordionSummary",e)}let f=(0,c.Z)("MuiAccordionSummary",["root","expanded","focusVisible","disabled","gutters","contentGutters","content","expandIconWrapper"]);var m=r(79114),g=r(57437);let x=e=>{let{classes:t,expanded:r,disabled:n,disableGutters:i}=e;return(0,a.Z)({root:["root",r&&"expanded",n&&"disabled",!i&&"gutters"],focusVisible:["focusVisible"],content:["content",r&&"expanded",!i&&"contentGutters"],expandIconWrapper:["expandIconWrapper",r&&"expanded"]},p,t)},y=(0,o.ZP)(u.Z,{name:"MuiAccordionSummary",slot:"Root",overridesResolver:(e,t)=>t.root})((0,s.Z)(e=>{let{theme:t}=e,r={duration:t.transitions.duration.shortest};return{display:"flex",width:"100%",minHeight:48,padding:t.spacing(0,2),transition:t.transitions.create(["min-height","background-color"],r),[`&.${f.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${f.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity},[`&:hover:not(.${f.disabled})`]:{cursor:"pointer"},variants:[{props:e=>!e.disableGutters,style:{[`&.${f.expanded}`]:{minHeight:64}}}]}})),v=(0,o.ZP)("span",{name:"MuiAccordionSummary",slot:"Content",overridesResolver:(e,t)=>t.content})((0,s.Z)(e=>{let{theme:t}=e;return{display:"flex",textAlign:"start",flexGrow:1,margin:"12px 0",variants:[{props:e=>!e.disableGutters,style:{transition:t.transitions.create(["margin"],{duration:t.transitions.duration.shortest}),[`&.${f.expanded}`]:{margin:"20px 0"}}}]}})),b=(0,o.ZP)("span",{name:"MuiAccordionSummary",slot:"ExpandIconWrapper",overridesResolver:(e,t)=>t.expandIconWrapper})((0,s.Z)(e=>{let{theme:t}=e;return{display:"flex",color:(t.vars||t).palette.action.active,transform:"rotate(0deg)",transition:t.transitions.create("transform",{duration:t.transitions.duration.shortest}),[`&.${f.expanded}`]:{transform:"rotate(180deg)"}}}));var Z=n.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiAccordionSummary"}),{children:a,className:o,expandIcon:s,focusVisibleClassName:u,onClick:c,slots:h,slotProps:p,...f}=r,{disabled:Z=!1,disableGutters:w,expanded:A,toggle:k}=n.useContext(d.Z),I=e=>{k&&k(e),c&&c(e)},C={...r,expanded:A,disabled:Z,disableGutters:w},M=x(C),P={slots:h,slotProps:p},[j,R]=(0,m.Z)("root",{ref:t,shouldForwardComponentProp:!0,className:(0,i.Z)(M.root,o),elementType:y,externalForwardedProps:{...P,...f},ownerState:C,additionalProps:{focusRipple:!1,disableRipple:!0,disabled:Z,"aria-expanded":A,focusVisibleClassName:(0,i.Z)(M.focusVisible,u)},getSlotProps:e=>({...e,onClick:t=>{e.onClick?.(t),I(t)}})}),[S,_]=(0,m.Z)("content",{className:M.content,elementType:v,externalForwardedProps:P,ownerState:C}),[$,E]=(0,m.Z)("expandIconWrapper",{className:M.expandIconWrapper,elementType:b,externalForwardedProps:P,ownerState:C});return(0,g.jsxs)(j,{...R,children:[(0,g.jsx)(S,{..._,children:a}),s&&(0,g.jsx)($,{...E,children:s})]})})},30731:function(e,t,r){r.d(t,{Z:function(){return w}});var n=r(2265),i=r(61994),a=r(20801),o=r(16210),s=r(76301),l=r(37053),u=r(17162),d=r(53410),c=r(31288),h=r(67184),p=r(79114),f=r(94143),m=r(50738);function g(e){return(0,m.ZP)("MuiAccordion",e)}let x=(0,f.Z)("MuiAccordion",["root","heading","rounded","expanded","disabled","gutters","region"]);var y=r(57437);let v=e=>{let{classes:t,square:r,expanded:n,disabled:i,disableGutters:o}=e;return(0,a.Z)({root:["root",!r&&"rounded",n&&"expanded",i&&"disabled",!o&&"gutters"],heading:["heading"],region:["region"]},g,t)},b=(0,o.ZP)(d.Z,{name:"MuiAccordion",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{[`& .${x.region}`]:t.region},t.root,!r.square&&t.rounded,!r.disableGutters&&t.gutters]}})((0,s.Z)(e=>{let{theme:t}=e,r={duration:t.transitions.duration.shortest};return{position:"relative",transition:t.transitions.create(["margin"],r),overflowAnchor:"none","&::before":{position:"absolute",left:0,top:-1,right:0,height:1,content:'""',opacity:1,backgroundColor:(t.vars||t).palette.divider,transition:t.transitions.create(["opacity","background-color"],r)},"&:first-of-type":{"&::before":{display:"none"}},[`&.${x.expanded}`]:{"&::before":{opacity:0},"&:first-of-type":{marginTop:0},"&:last-of-type":{marginBottom:0},"& + &":{"&::before":{display:"none"}}},[`&.${x.disabled}`]:{backgroundColor:(t.vars||t).palette.action.disabledBackground}}}),(0,s.Z)(e=>{let{theme:t}=e;return{variants:[{props:e=>!e.square,style:{borderRadius:0,"&:first-of-type":{borderTopLeftRadius:(t.vars||t).shape.borderRadius,borderTopRightRadius:(t.vars||t).shape.borderRadius},"&:last-of-type":{borderBottomLeftRadius:(t.vars||t).shape.borderRadius,borderBottomRightRadius:(t.vars||t).shape.borderRadius,"@supports (-ms-ime-align: auto)":{borderBottomLeftRadius:0,borderBottomRightRadius:0}}}},{props:e=>!e.disableGutters,style:{[`&.${x.expanded}`]:{margin:"16px 0"}}}]}})),Z=(0,o.ZP)("h3",{name:"MuiAccordion",slot:"Heading",overridesResolver:(e,t)=>t.heading})({all:"unset"});var w=n.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiAccordion"}),{children:a,className:o,defaultExpanded:s=!1,disabled:d=!1,disableGutters:f=!1,expanded:m,onChange:g,square:x=!1,slots:w={},slotProps:A={},TransitionComponent:k,TransitionProps:I,...C}=r,[M,P]=(0,h.Z)({controlled:m,default:s,name:"Accordion",state:"expanded"}),j=n.useCallback(e=>{P(!M),g&&g(e,!M)},[M,g,P]),[R,...S]=n.Children.toArray(a),_=n.useMemo(()=>({expanded:M,disabled:d,disableGutters:f,toggle:j}),[M,d,f,j]),$={...r,square:x,disabled:d,disableGutters:f,expanded:M},E=v($),L={slots:{transition:k,...w},slotProps:{transition:I,...A}},[z,T]=(0,p.Z)("root",{elementType:b,externalForwardedProps:{...L,...C},className:(0,i.Z)(E.root,o),shouldForwardComponentProp:!0,ownerState:$,ref:t,additionalProps:{square:x}}),[O,N]=(0,p.Z)("heading",{elementType:Z,externalForwardedProps:L,className:E.heading,ownerState:$}),[F,V]=(0,p.Z)("transition",{elementType:u.Z,externalForwardedProps:L,ownerState:$});return(0,y.jsxs)(z,{...T,children:[(0,y.jsx)(O,{...N,children:(0,y.jsx)(c.Z.Provider,{value:_,children:R})}),(0,y.jsx)(F,{in:M,timeout:"auto",...V,children:(0,y.jsx)("div",{"aria-labelledby":R.props.id,id:R.props["aria-controls"],role:"region",className:E.region,children:S})})]})})},31288:function(e,t,r){let n=r(2265).createContext({});t.Z=n},36137:function(e,t,r){r.d(t,{Z:function(){return f}});var n=r(2265),i=r(61994),a=r(20801),o=r(16210),s=r(37053),l=r(94143),u=r(50738);function d(e){return(0,u.ZP)("MuiCardContent",e)}(0,l.Z)("MuiCardContent",["root"]);var c=r(57437);let h=e=>{let{classes:t}=e;return(0,a.Z)({root:["root"]},d,t)},p=(0,o.ZP)("div",{name:"MuiCardContent",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:16,"&:last-child":{paddingBottom:24}});var f=n.forwardRef(function(e,t){let r=(0,s.i)({props:e,name:"MuiCardContent"}),{className:n,component:a="div",...o}=r,l={...r,component:a},u=h(l);return(0,c.jsx)(p,{as:a,className:(0,i.Z)(u.root,n),ownerState:l,ref:t,...o})})},17162:function(e,t,r){r.d(t,{Z:function(){return k}});var n=r(2265),i=r(61994),a=r(52836),o=r(73207),s=r(20801),l=r(16210),u=r(31691),d=r(76301),c=r(37053),h=r(73220),p=r(31090),f=r(60118),m=r(94143),g=r(50738);function x(e){return(0,g.ZP)("MuiCollapse",e)}(0,m.Z)("MuiCollapse",["root","horizontal","vertical","entered","hidden","wrapper","wrapperInner"]);var y=r(57437);let v=e=>{let{orientation:t,classes:r}=e,n={root:["root",`${t}`],entered:["entered"],hidden:["hidden"],wrapper:["wrapper",`${t}`],wrapperInner:["wrapperInner",`${t}`]};return(0,s.Z)(n,x,r)},b=(0,l.ZP)("div",{name:"MuiCollapse",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.orientation],"entered"===r.state&&t.entered,"exited"===r.state&&!r.in&&"0px"===r.collapsedSize&&t.hidden]}})((0,d.Z)(e=>{let{theme:t}=e;return{height:0,overflow:"hidden",transition:t.transitions.create("height"),variants:[{props:{orientation:"horizontal"},style:{height:"auto",width:0,transition:t.transitions.create("width")}},{props:{state:"entered"},style:{height:"auto",overflow:"visible"}},{props:{state:"entered",orientation:"horizontal"},style:{width:"auto"}},{props:e=>{let{ownerState:t}=e;return"exited"===t.state&&!t.in&&"0px"===t.collapsedSize},style:{visibility:"hidden"}}]}})),Z=(0,l.ZP)("div",{name:"MuiCollapse",slot:"Wrapper",overridesResolver:(e,t)=>t.wrapper})({display:"flex",width:"100%",variants:[{props:{orientation:"horizontal"},style:{width:"auto",height:"100%"}}]}),w=(0,l.ZP)("div",{name:"MuiCollapse",slot:"WrapperInner",overridesResolver:(e,t)=>t.wrapperInner})({width:"100%",variants:[{props:{orientation:"horizontal"},style:{width:"auto",height:"100%"}}]}),A=n.forwardRef(function(e,t){let r=(0,c.i)({props:e,name:"MuiCollapse"}),{addEndListener:s,children:l,className:d,collapsedSize:m="0px",component:g,easing:x,in:A,onEnter:k,onEntered:I,onEntering:C,onExit:M,onExited:P,onExiting:j,orientation:R="vertical",style:S,timeout:_=h.x9.standard,TransitionComponent:$=a.ZP,...E}=r,L={...r,orientation:R,collapsedSize:m},z=v(L),T=(0,u.Z)(),O=(0,o.Z)(),N=n.useRef(null),F=n.useRef(),V="number"==typeof m?`${m}px`:m,H="horizontal"===R,B=H?"width":"height",G=n.useRef(null),D=(0,f.Z)(t,G),U=e=>t=>{if(e){let r=G.current;void 0===t?e(r):e(r,t)}},W=()=>N.current?N.current[H?"clientWidth":"clientHeight"]:0,q=U((e,t)=>{N.current&&H&&(N.current.style.position="absolute"),e.style[B]=V,k&&k(e,t)}),X=U((e,t)=>{let r=W();N.current&&H&&(N.current.style.position="");let{duration:n,easing:i}=(0,p.C)({style:S,timeout:_,easing:x},{mode:"enter"});if("auto"===_){let t=T.transitions.getAutoHeightDuration(r);e.style.transitionDuration=`${t}ms`,F.current=t}else e.style.transitionDuration="string"==typeof n?n:`${n}ms`;e.style[B]=`${r}px`,e.style.transitionTimingFunction=i,C&&C(e,t)}),K=U((e,t)=>{e.style[B]="auto",I&&I(e,t)}),Y=U(e=>{e.style[B]=`${W()}px`,M&&M(e)}),Q=U(P),J=U(e=>{let t=W(),{duration:r,easing:n}=(0,p.C)({style:S,timeout:_,easing:x},{mode:"exit"});if("auto"===_){let r=T.transitions.getAutoHeightDuration(t);e.style.transitionDuration=`${r}ms`,F.current=r}else e.style.transitionDuration="string"==typeof r?r:`${r}ms`;e.style[B]=V,e.style.transitionTimingFunction=n,j&&j(e)});return(0,y.jsx)($,{in:A,onEnter:q,onEntered:K,onEntering:X,onExit:Y,onExited:Q,onExiting:J,addEndListener:e=>{"auto"===_&&O.start(F.current||0,e),s&&s(G.current,e)},nodeRef:G,timeout:"auto"===_?null:_,...E,children:(e,t)=>{let{ownerState:r,...n}=t;return(0,y.jsx)(b,{as:g,className:(0,i.Z)(z.root,d,{entered:z.entered,exited:!A&&"0px"===V&&z.hidden}[e]),style:{[H?"minWidth":"minHeight"]:V,...S},ref:D,ownerState:{...L,state:e},...n,children:(0,y.jsx)(Z,{ownerState:{...L,state:e},className:z.wrapper,ref:N,children:(0,y.jsx)(w,{ownerState:{...L,state:e},className:z.wrapperInner,children:l})})})}})});A&&(A.muiSupportAuto=!0);var k=A},89126:function(e,t,r){r.d(t,{Z:function(){return g}});var n=r(2265),i=r(61994),a=r(20801),o=r(16210),s=r(37053),l=r(94143),u=r(50738);function d(e){return(0,u.ZP)("MuiFormGroup",e)}(0,l.Z)("MuiFormGroup",["root","row","error"]);var c=r(66515),h=r(48904),p=r(57437);let f=e=>{let{classes:t,row:r,error:n}=e;return(0,a.Z)({root:["root",r&&"row",n&&"error"]},d,t)},m=(0,o.ZP)("div",{name:"MuiFormGroup",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.row&&t.row]}})({display:"flex",flexDirection:"column",flexWrap:"wrap",variants:[{props:{row:!0},style:{flexDirection:"row"}}]});var g=n.forwardRef(function(e,t){let r=(0,s.i)({props:e,name:"MuiFormGroup"}),{className:n,row:a=!1,...o}=r,l=(0,c.Z)(),u=(0,h.Z)({props:r,muiFormControl:l,states:["error"]}),d={...r,row:a,error:u.error},g=f(d);return(0,p.jsx)(m,{className:(0,i.Z)(g.root,n),ownerState:d,ref:t,...o})})},53610:function(e,t,r){var n=r(2265),i=r(3450);t.Z=function(e){let{children:t,defer:r=!1,fallback:a=null}=e,[o,s]=n.useState(!1);return(0,i.Z)(()=>{r||s(!0)},[r]),n.useEffect(()=>{r&&s(!0)},[r]),o?t:a}},68155:function(e,t,r){r.d(t,{v:function(){return eI}});var n=r(1119),i=r(2265),a=r(64119),o=r(74610),s=r(91434),l=r(94102),u=r(20801),d=r(15988),c=r(50738),h=r(16210),p=r(94143),f=r(26910),m=r(72636),g=r(42457);function x(e){let t=(0,g.j)();if(!e)return{isHighlighted:!1,isFaded:!1};let r=t.isHighlighted(e),n=!r&&t.isFaded(e);return{isHighlighted:r,isFaded:n}}var y=r(57437);let v=["id","dataIndex","classes","color","slots","slotProps","style","onClick"];function b(e){return(0,c.ZP)("MuiBarElement",e)}(0,p.Z)("MuiBarElement",["root"]);let Z=e=>{let{classes:t,id:r}=e,n={root:["root",`series-${r}`]};return(0,u.Z)(n,b,t)},w=(0,h.ZP)(s.q.rect,{name:"MuiBarElement",slot:"Root",overridesResolver:(e,t)=>t.root})(e=>{let{ownerState:t}=e;return{stroke:"none",fill:t.isHighlighted?(0,f.ZP)(t.color).brighter(.5).formatHex():t.color,transition:"opacity 0.2s ease-in, fill 0.2s ease-in",opacity:t.isFaded&&.3||1}});function A(e){let{id:t,dataIndex:r,classes:i,color:a,slots:s,slotProps:l,style:u,onClick:c}=e,h=(0,o.Z)(e,v),p=(0,m.J)(),{isFaded:f,isHighlighted:g}=x({seriesId:t,dataIndex:r}),b={id:t,dataIndex:r,classes:i,color:a,isFaded:f,isHighlighted:g},A=Z(b),k=s?.bar??w,I=(0,d.Z)({elementType:k,externalSlotProps:l?.bar,externalForwardedProps:h,additionalProps:(0,n.Z)({},p({type:"bar",seriesId:t,dataIndex:r}),{style:u,onClick:c,cursor:c?"pointer":"unset"}),className:A.root,ownerState:b});return(0,y.jsx)(k,(0,n.Z)({},I))}var k=r(36314),I=r(96100);let C=(e,{hasNegative:t,hasPositive:r,borderRadius:n,layout:i})=>{if(!n)return 0;let a="vertical"===i;return"top-left"===e&&(a&&r||!a&&t)||"top-right"===e&&(a&&r||!a&&r)||"bottom-right"===e&&(a&&t||!a&&r)||"bottom-left"===e&&(a&&t||!a&&t)?n:0},M=["style","maskId"],P=e=>`inset(0px round ${e.topLeft}px ${e.topRight}px ${e.bottomRight}px ${e.bottomLeft}px)`;function j(e){let t=e.ownerState;return(0,y.jsx)(s.q.rect,{style:(0,n.Z)({},e.style,{clipPath:("vertical"===e.ownerState.layout?e.style?.height:e.style?.width).to(e=>P({topLeft:Math.min(e,C("top-left",t)),topRight:Math.min(e,C("top-right",t)),bottomRight:Math.min(e,C("bottom-right",t)),bottomLeft:Math.min(e,C("bottom-left",t))}))})})}function R(e){let{style:t,maskId:r}=e,n=(0,o.Z)(e,M);return!e.borderRadius||e.borderRadius<=0?null:(0,y.jsx)("clipPath",{id:r,children:(0,y.jsx)(j,{ownerState:n,style:t})})}function S(e){return(0,c.ZP)("MuiBarLabel",e)}let _=(0,p.Z)("MuiBarLabel",["root","highlighted","faded"]),$=e=>{let{classes:t,seriesId:r,isFaded:n,isHighlighted:i}=e,a={root:["root",`series-${r}`,i&&"highlighted",n&&"faded"]};return(0,u.Z)(a,S,t)},E=e=>{let{barLabel:t,value:r,dataIndex:n,seriesId:i,height:a,width:o}=e;return"value"===t?r?r?.toString():null:t({seriesId:i,dataIndex:n,value:r},{bar:{height:a,width:o}})},L=["seriesId","dataIndex","color","isFaded","isHighlighted","classes"],z=(0,h.ZP)(s.q.text,{name:"MuiBarLabel",slot:"Root",overridesResolver:(e,t)=>[{[`&.${_.faded}`]:t.faded},{[`&.${_.highlighted}`]:t.highlighted},t.root]})(e=>{let{theme:t}=e;return(0,n.Z)({},t?.typography?.body2,{stroke:"none",fill:(t.vars||t)?.palette?.text?.primary,transition:"opacity 0.2s ease-in, fill 0.2s ease-in",textAnchor:"middle",dominantBaseline:"central",pointerEvents:"none",opacity:1,[`&.${_.faded}`]:{opacity:.3}})});function T(e){let t=(0,a.Z)({props:e,name:"MuiBarLabel"}),r=(0,o.Z)(t,L);return(0,y.jsx)(z,(0,n.Z)({},r))}let O=["seriesId","classes","color","style","dataIndex","barLabel","slots","slotProps","height","width","value"],N=["ownerState"];function F(e){let{seriesId:t,classes:r,color:i,style:a,dataIndex:s,barLabel:l,slots:u,slotProps:c,height:h,width:p,value:f}=e,m=(0,o.Z)(e,O),{isFaded:g,isHighlighted:v}=x({seriesId:t,dataIndex:s}),b={seriesId:t,classes:r,color:i,isFaded:g,isHighlighted:v,dataIndex:s},Z=$(b),w=u?.barLabel??T,A=(0,d.Z)({elementType:w,externalSlotProps:c?.barLabel,additionalProps:(0,n.Z)({},m,{style:a,className:Z.root}),ownerState:b}),{ownerState:k}=A,I=(0,o.Z)(A,N);if(!l)return null;let C=E({barLabel:l,value:f,dataIndex:s,seriesId:t,height:h,width:p});return C?(0,y.jsx)(w,(0,n.Z)({},I,k,{children:C})):null}let V=["bars","skipAnimation"],H=({layout:e,yOrigin:t,x:r,width:i,y:a,xOrigin:o,height:s})=>(0,n.Z)({},"vertical"===e?{y:t,x:r+i/2,height:0,width:i}:{y:a+s/2,x:o,height:s,width:0}),B=({x:e,width:t,y:r,height:n})=>({x:e+t/2,y:r+n/2,height:n,width:t});function G(e){let{bars:t,skipAnimation:r}=e,a=(0,o.Z)(e,V),l=(0,s.Yz)(t,{keys:e=>`${e.seriesId}-${e.dataIndex}`,from:H,leave:null,enter:B,update:B,immediate:r});return(0,y.jsx)(i.Fragment,{children:l((e,{seriesId:t,dataIndex:r,color:i,value:o,width:s,height:l})=>(0,y.jsx)(F,(0,n.Z)({seriesId:t,dataIndex:r,value:o,color:i,width:s,height:l},a,{style:e})))})}var D=r(70337),U=r(35836);let W=(e,t)=>{let r=`${e}-axis`,n=`${e}Axis`;return t===("x"===e?D.nk:D.Vd)?`The first \`${n}\``:`The ${r} with id "${t}"`};var q=r(78405),X=r(66924);let K=["skipAnimation","onItemClick","borderRadius","barLabel"],Y=()=>{let e=(0,q.qZ)()??{series:{},stackingGroups:[],seriesOrder:[]},t=(0,l.r)(),r=function(){let{chartId:e}=i.useContext(I.SV);return i.useMemo(()=>e,[e])}(),{series:n,stackingGroups:a}=e,{xAxis:o,yAxis:s,xAxisIds:u,yAxisIds:d}=t,c=u[0],h=d[0],p={};return{completedData:a.flatMap((e,t)=>{let{ids:i}=e;return i.flatMap(e=>{let i=n[e].xAxisId??n[e].xAxisKey??c,l=n[e].yAxisId??n[e].yAxisKey??h,u=o[i],d=s[l],f="vertical"===n[e].layout;!function(e,t,r,n,i,a){let o=n[r],s=a[i],l=e?o:s,u=e?s:o,d=e?r:i,c=e?"x":"y";if(!(0,U.Q)(l))throw Error(`MUI X: ${W(c,d)} should be of type "band" to display the bar series of id "${t}".`);if(void 0===l.data)throw Error(`MUI X: ${W(c,d)} should have data property.`);if((0,U.Q)(u)||(0,U.T)(u))throw Error(`MUI X: ${W(e?"y":"x",e?i:r)} should be a continuous type to display the bar series of id "${t}".`)}(f,e,i,o,l,s);let m=f?u:d,g=u.scale,x=d.scale,y=(0,k.Z)(n[e],o[i],s[l]),{barWidth:v,offset:b}=function(e){let{bandWidth:t,numberOfGroups:r,gapRatio:n}=e;if(0===n)return{barWidth:t/r,offset:0};let i=t/(r+(r-1)*n);return{barWidth:i,offset:n*i}}({bandWidth:m.scale.bandwidth(),numberOfGroups:a.length,gapRatio:m.barGapRatio}),Z=t*(v+b),{stackedData:w}=n[e];return w.map((a,u)=>{let d=a.map(e=>f?x(e):g(e)),c=Math.round(Math.min(...d)),h=Math.round(Math.max(...d)),m=n[e].stack,b={seriesId:e,dataIndex:u,layout:n[e].layout,x:f?g(o[i].data?.[u])+Z:c,y:f?c:x(s[l].data?.[u])+Z,xOrigin:g(0),yOrigin:x(0),height:f?h-c:v,width:f?v:h-c,color:y(u),value:n[e].data[u],maskId:`${r}_${m||e}_${t}_${u}`};p[b.maskId]||(p[b.maskId]={id:b.maskId,width:0,height:0,hasNegative:!1,hasPositive:!1,layout:b.layout,xOrigin:g(0),yOrigin:x(0),x:0,y:0});let w=p[b.maskId];return w.width="vertical"===b.layout?b.width:w.width+b.width,w.height="vertical"===b.layout?w.height+b.height:b.height,w.x=Math.min(0===w.x?1/0:w.x,b.x),w.y=Math.min(0===w.y?1/0:w.y,b.y),w.hasNegative=w.hasNegative||(b.value??0)<0,w.hasPositive=w.hasPositive||(b.value??0)>0,b})})}),masksData:Object.values(p)}},Q=e=>{let{layout:t,yOrigin:r,x:i,width:a,y:o,xOrigin:s,height:l}=e;return(0,n.Z)({},"vertical"===t?{y:r,x:i,height:0,width:a}:{y:o,x:s,height:l,width:0})},J=e=>{let{x:t,width:r,y:n,height:i}=e;return{y:n,x:t,height:i,width:r}};function ee(e){let{completedData:t,masksData:r}=Y(),{skipAnimation:a,onItemClick:l,borderRadius:u,barLabel:d}=e,c=(0,o.Z)(e,K),h=(0,X.r)(a),p=!u||u<=0,f=(0,s.Yz)(t,{keys:e=>`${e.seriesId}-${e.dataIndex}`,from:Q,leave:Q,enter:J,update:J,immediate:h}),m=(0,s.Yz)(p?[]:r,{keys:e=>e.id,from:Q,leave:Q,enter:J,update:J,immediate:h});return(0,y.jsxs)(i.Fragment,{children:[!p&&m((e,t)=>{let{id:r,hasPositive:n,hasNegative:i,layout:a}=t;return(0,y.jsx)(R,{maskId:r,borderRadius:u,hasNegative:i,hasPositive:n,layout:a,style:e})}),f((e,t)=>{let{seriesId:r,dataIndex:i,color:a,maskId:o}=t,s=(0,y.jsx)(A,(0,n.Z)({id:r,dataIndex:i,color:a},c,{onClick:l&&(e=>{l(e,{type:"bar",seriesId:r,dataIndex:i})}),style:e}));return p?s:(0,y.jsx)("g",{clipPath:`url(#${o})`,children:s})}),d&&(0,y.jsx)(G,(0,n.Z)({bars:t,skipAnimation:h,barLabel:d},c))]})}var et=r(49507),er=r(62216),en=r(99276),ei=r(84943),ea=r(82878),eo=r(87811);function es(e){let{id:t,offset:r}=e,{left:i,top:a,width:o,height:s}=(0,eo.z)(),l=(0,n.Z)({top:0,right:0,bottom:0,left:0},r);return(0,y.jsx)("clipPath",{id:t,children:(0,y.jsx)("rect",{x:i-l.left,y:a-l.top,width:o+l.left+l.right,height:s+l.top+l.bottom})})}function el(e){return(0,c.ZP)("MuiChartsGrid",e)}let eu=(0,p.Z)("MuiChartsGrid",["root","line","horizontalLine","verticalLine"]),ed=(0,h.ZP)("g",{name:"MuiChartsGrid",slot:"Root",overridesResolver:(e,t)=>[{[`&.${eu.verticalLine}`]:t.verticalLine},{[`&.${eu.horizontalLine}`]:t.horizontalLine},t.root]})({}),ec=(0,h.ZP)("line",{name:"MuiChartsGrid",slot:"Line",overridesResolver:(e,t)=>t.line})(({theme:e})=>({stroke:(e.vars||e).palette.divider,shapeRendering:"crispEdges",strokeWidth:1}));var eh=r(28576);function ep(e){let{axis:t,drawingArea:r,classes:n}=e,{scale:a,tickNumber:o,tickInterval:s}=t,l=(0,eh.g)({scale:a,tickNumber:o,tickInterval:s});return(0,y.jsx)(i.Fragment,{children:l.map(({value:e,offset:t})=>(0,y.jsx)(ec,{y1:r.top,y2:r.top+r.height,x1:t,x2:t,className:n.verticalLine},`vertical-${e}`))})}function ef(e){let{axis:t,drawingArea:r,classes:n}=e,{scale:a,tickNumber:o,tickInterval:s}=t,l=(0,eh.g)({scale:a,tickNumber:o,tickInterval:s});return(0,y.jsx)(i.Fragment,{children:l.map(({value:e,offset:t})=>(0,y.jsx)(ec,{y1:t,y2:t,x1:r.left,x2:r.left+r.width,className:n.horizontalLine},`horizontal-${e}`))})}let em=["vertical","horizontal"],eg=e=>{let{classes:t}=e;return(0,u.Z)({root:["root"],verticalLine:["line","verticalLine"],horizontalLine:["line","horizontalLine"]},el,t)};function ex(e){let t=(0,a.Z)({props:e,name:"MuiChartsGrid"}),r=(0,eo.z)(),{vertical:i,horizontal:s}=t,u=(0,o.Z)(t,em),{xAxis:d,xAxisIds:c,yAxis:h,yAxisIds:p}=(0,l.r)(),f=eg(t),m=h[p[0]],g=d[c[0]];return(0,y.jsxs)(ed,(0,n.Z)({},u,{className:f.root,children:[i&&(0,y.jsx)(ep,{axis:g,drawingArea:r,classes:f}),s&&(0,y.jsx)(ef,{axis:m,drawingArea:r,classes:f})]}))}var ey=r(79795),ev=r(80414);function eb(e){let{onAxisClick:t}=e,r=(0,ev.S)(),n=(0,q.us)(),{axis:a}=i.useContext(ey.s),{xAxisIds:o,xAxis:s,yAxisIds:u,yAxis:d}=(0,l.r)();return i.useEffect(()=>{let e=r.current;if(null===e||!t)return()=>{};let i=e=>{e.preventDefault();let r=a.x&&-1!==a.x.index,i=r?o[0]:u[0],l=r?a.x&&a.x.index:a.y&&a.y.index;if(null==l)return;let c={};Object.keys(n).filter(e=>["bar","line"].includes(e)).forEach(e=>{n[e]?.seriesOrder.forEach(t=>{let a=n[e].series[t],o=a.xAxisId??a.xAxisKey,s=a.yAxisId??a.yAxisKey,u=r?o:s;(void 0===u||u===i)&&(c[t]=a.data[l])})});let h=(r?s:d)[i].data?.[l];t(e,{dataIndex:l,axisValue:h,seriesValues:c})};return e.addEventListener("click",i),()=>{e.removeEventListener("click",i)}},[a.x,a.y,t,n,r,s,o,d,u]),(0,y.jsx)(i.Fragment,{})}var eZ=r(30329),ew=r(53025);let eA=["xAxis","yAxis","series","width","height","margin","colors","dataset","sx","tooltip","onAxisClick","axisHighlight","legend","grid","topAxis","leftAxis","rightAxis","bottomAxis","children","slots","slotProps","skipAnimation","loading","layout","onItemClick","highlightedItem","onHighlightChange","borderRadius","barLabel","className"],ek=e=>{let{xAxis:t,yAxis:r,series:i,width:a,height:s,margin:l,colors:u,dataset:d,sx:c,tooltip:h,onAxisClick:p,axisHighlight:f,legend:m,grid:g,topAxis:x,leftAxis:y,rightAxis:v,bottomAxis:b,children:Z,slots:w,slotProps:A,skipAnimation:k,loading:I,layout:C,onItemClick:M,highlightedItem:P,onHighlightChange:j,borderRadius:R,barLabel:S,className:_}=e,$=(0,o.Z)(e,eA),E=(0,ew.Z)(),L=`${E}-clip-path`,z="horizontal"===C||void 0===C&&i.some(e=>"horizontal"===e.layout),T={scaleType:"band",data:Array.from({length:Math.max(...i.map(e=>(e.data??d??[]).length))},(e,t)=>t)},O=(0,n.Z)({},$,{series:i.map(e=>(0,n.Z)({type:"bar"},e,{layout:z?"horizontal":"vertical"})),width:a,height:s,margin:l,colors:u,dataset:d,xAxis:t??(z?void 0:[(0,n.Z)({id:D.nk},T)]),yAxis:r??(z?[(0,n.Z)({id:D.Vd},T)]:void 0),sx:c,highlightedItem:P,onHighlightChange:j,disableAxisListener:h?.trigger!=="axis"&&f?.x==="none"&&f?.y==="none"&&!p,className:_,skipAnimation:k}),N={vertical:g?.vertical,horizontal:g?.horizontal},F={clipPath:`url(#${L})`},V=(0,n.Z)({},z?{y:"band"}:{x:"band"},f),H=(0,n.Z)({},m,{slots:w,slotProps:A}),B=(0,n.Z)({},h,{slots:w,slotProps:A});return{chartContainerProps:O,barPlotProps:{onItemClick:M,slots:w,slotProps:A,borderRadius:R,barLabel:S},axisClickHandlerProps:{onAxisClick:p},gridProps:N,clipPathProps:{id:L},clipPathGroupProps:F,overlayProps:{slots:w,slotProps:A,loading:I},chartsAxisProps:{topAxis:x,leftAxis:y,rightAxis:v,bottomAxis:b,slots:w,slotProps:A},axisHighlightProps:V,legendProps:H,tooltipProps:B,children:Z}},eI=i.forwardRef(function(e,t){let r=(0,a.Z)({props:e,name:"MuiBarChart"}),{chartContainerProps:i,barPlotProps:o,axisClickHandlerProps:s,gridProps:l,clipPathProps:u,clipPathGroupProps:d,overlayProps:c,chartsAxisProps:h,axisHighlightProps:p,legendProps:f,tooltipProps:m,children:g}=ek(r);return(0,y.jsxs)(et.D,(0,n.Z)({ref:t},i,{children:[r.onAxisClick&&(0,y.jsx)(eb,(0,n.Z)({},s)),(0,y.jsx)(ex,(0,n.Z)({},l)),(0,y.jsxs)("g",(0,n.Z)({},d,{children:[(0,y.jsx)(ee,(0,n.Z)({},o)),(0,y.jsx)(eZ.I,(0,n.Z)({},c)),(0,y.jsx)(ea.qe,(0,n.Z)({},p))]})),(0,y.jsx)(er.q,(0,n.Z)({},h)),(0,y.jsx)(ei.G,(0,n.Z)({},f)),!r.loading&&(0,y.jsx)(en.a,(0,n.Z)({},m)),(0,y.jsx)(es,(0,n.Z)({},u)),g]}))})},36314:function(e,t){t.Z=(e,t,r)=>{let n="vertical"===e.layout,i=n?t?.colorScale:r?.colorScale,a=n?r?.colorScale:t?.colorScale,o=n?t?.data:r?.data;return a?t=>{let r=e.data[t],n=null===r?e.color:a(r);return null===n?e.color:n}:i&&o?t=>{let r=o[t],n=null===r?e.color:i(r);return null===n?e.color:n}:()=>e.color}},82878:function(e,t,r){r.d(t,{qe:function(){return x}});var n=r(1119),i=r(2265),a=r(20801),o=r(50738),s=r(94143),l=r(16210),u=r(79795),d=r(94102),c=r(34881);function h(e){return(0,c.o)(e)?t=>(e(t)??0)+e.bandwidth()/2:t=>e(t)}var p=r(57437);function f(e){return(0,o.ZP)("MuiChartsAxisHighlight",e)}(0,s.Z)("MuiChartsAxisHighlight",["root"]);let m=()=>(0,a.Z)({root:["root"]},f),g=(0,l.ZP)("path",{name:"MuiChartsAxisHighlight",slot:"Root",overridesResolver:(e,t)=>t.root})(e=>{let{theme:t}=e;return{pointerEvents:"none",variants:[{props:{axisHighlight:"band"},style:(0,n.Z)({fill:"white",fillOpacity:.1},t.applyStyles("light",{fill:"gray"}))},{props:{axisHighlight:"line"},style:(0,n.Z)({strokeDasharray:"5 2",stroke:"#ffffff"},t.applyStyles("light",{stroke:"#000000"}))}]}});function x(e){let{x:t,y:r}=e,{xAxisIds:n,xAxis:a,yAxisIds:o,yAxis:s}=(0,d.r)(),l=m(),f=n[0],x=o[0],y=a[f].scale,v=s[x].scale,{axis:b}=i.useContext(u.s),Z=h(y),w=h(v),A=b.x,k=b.y,I="band"===t&&null!==A&&(0,c.o)(y),C="band"===r&&null!==k&&(0,c.o)(v);return(0,p.jsxs)(i.Fragment,{children:[I&&void 0!==y(A.value)&&(0,p.jsx)(g,{d:`M ${y(A.value)-(y.step()-y.bandwidth())/2} ${v.range()[0]} l ${y.step()} 0 l 0 ${v.range()[1]-v.range()[0]} l ${-y.step()} 0 Z`,className:l.root,ownerState:{axisHighlight:"band"}}),C&&void 0!==v(k.value)&&(0,p.jsx)(g,{d:`M ${y.range()[0]} ${v(k.value)-(v.step()-v.bandwidth())/2} l 0 ${v.step()} l ${y.range()[1]-y.range()[0]} 0 l 0 ${-v.step()} Z`,className:l.root,ownerState:{axisHighlight:"band"}}),"line"===t&&null!==b.x&&(0,p.jsx)(g,{d:`M ${Z(b.x.value)} ${v.range()[0]} L ${Z(b.x.value)} ${v.range()[1]}`,className:l.root,ownerState:{axisHighlight:"line"}}),"line"===r&&null!==b.y&&(0,p.jsx)(g,{d:`M ${y.range()[0]} ${w(b.y.value)} L ${y.range()[1]} ${w(b.y.value)}`,className:l.root,ownerState:{axisHighlight:"line"}})]})}},62216:function(e,t,r){r.d(t,{q:function(){return T}});var n=r(1119),i=r(2265),a=r(94102),o=r(74610),s=r(15988),l=r(20801),u=r(16210),d=r(64119),c=r(31691),h=r(28576),p=r(50738);function f(e){return(0,p.ZP)("MuiChartsAxis",e)}let m=(0,r(94143).Z)("MuiChartsAxis",["root","line","tickContainer","tick","tickLabel","label","directionX","directionY","top","bottom","left","right"]),g=(0,u.ZP)("g",{name:"MuiChartsAxis",slot:"Root",overridesResolver:(e,t)=>t.root})(({theme:e})=>({[`& .${m.tickLabel}`]:(0,n.Z)({},e.typography.caption,{fill:(e.vars||e).palette.text.primary}),[`& .${m.label}`]:(0,n.Z)({},e.typography.body1,{fill:(e.vars||e).palette.text.primary}),[`& .${m.line}`]:{stroke:(e.vars||e).palette.text.primary,shapeRendering:"crispEdges",strokeWidth:1},[`& .${m.tick}`]:{stroke:(e.vars||e).palette.text.primary,shapeRendering:"crispEdges"}}));var x=r(43440),y=r(3450),v=r(87811),b=r(62020),Z=r(57527),w=r(34881),A=r(57437);let k=["scale","tickNumber","reverse"],I=e=>{let{classes:t,position:r}=e;return(0,l.Z)({root:["root","directionX",r],line:["line"],tickContainer:["tickContainer"],tick:["tick"],tickLabel:["tickLabel"],label:["label"]},f,t)},C=(0,u.ZP)(g,{name:"MuiChartsXAxis",slot:"Root",overridesResolver:(e,t)=>t.root})({}),M={position:"bottom",disableLine:!1,disableTicks:!1,tickSize:6};function P(e){let{xAxisIds:t,xAxis:r}=(0,a.r)(),l=r[e.axisId??t[0]],{scale:u,tickNumber:p,reverse:f}=l,m=(0,o.Z)(l,k),g=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],[t,r]=i.useState(!1);return(0,y.Z)(()=>{e||r(!0)},[e]),i.useEffect(()=>{e&&r(!0)},[e]),t}(),P=(0,d.Z)({props:(0,n.Z)({},m,e),name:"MuiChartsXAxis"}),j=(0,n.Z)({},M,P),{position:R,disableLine:S,disableTicks:_,tickLabelStyle:$,label:E,labelStyle:L,tickFontSize:z,labelFontSize:T,tickSize:O,valueFormatter:N,slots:F,slotProps:V,tickInterval:H,tickLabelInterval:B,tickPlacement:G,tickLabelPlacement:D,sx:U}=j,W=(0,c.Z)(),q=I((0,n.Z)({},j,{theme:W})),{left:X,top:K,width:Y,height:Q,isPointInside:J}=(0,v.z)(),ee=_?4:O,et="bottom"===R?1:-1,er=F?.axisLine??"line",en=F?.axisTick??"line",ei=F?.axisTickLabel??x.k,ea=F?.axisLabel??x.k,eo=(0,s.Z)({elementType:ei,externalSlotProps:V?.axisTickLabel,additionalProps:{style:(0,n.Z)({textAnchor:"middle",dominantBaseline:"bottom"===R?"hanging":"auto",fontSize:z??12},$)},className:q.tickLabel,ownerState:{}}),es=function(e,t){let{tickLabelStyle:r,tickLabelInterval:i,reverse:a,isMounted:o}=t,s=e.map(e=>{if(!o||void 0===e.formattedValue)return(0,n.Z)({},e,{width:0,height:0});let t=(0,b.i)({style:r,needsComputation:!0,text:e.formattedValue});return(0,n.Z)({},e,{width:Math.max(...t.map(e=>e.width)),height:Math.max(t.length*t[0].height)})});if("function"==typeof i)return s.map((e,t)=>(0,n.Z)({},e,{skipLabel:!i(e.value,t)}));let l=0,u=0,d=a?-1:1;return s.map((e,t)=>{let{width:i,offset:a,labelOffset:o,height:s}=e,c=function(e,t,r=0){let n=Math.min(Math.abs(r)%180,Math.abs(Math.abs(r)%180-180)%180);if(n<5)return e;if(n>85)return t;let i=n*Math.PI/180;return i<Math.atan2(t,e)?e/Math.cos(i):t/Math.sin(i)}(i,s,r?.angle),h=a+o;return(l=h-1.2*c*d/2,t>0&&d*l<d*u)?(0,n.Z)({},e,{skipLabel:!0}):(u=h+1.2*c*d/2,e)})}((0,h.g)({scale:u,tickNumber:p,valueFormatter:N,tickInterval:H,tickPlacement:G,tickLabelPlacement:D}),{tickLabelStyle:eo.style,tickLabelInterval:B,reverse:f,isMounted:g}),el=(0,s.Z)({elementType:ea,externalSlotProps:V?.axisLabel,additionalProps:{style:(0,n.Z)({fontSize:T??14,textAnchor:"middle",dominantBaseline:"bottom"===R?"hanging":"auto"},L)},ownerState:{}}),eu=u.domain(),ed=(0,w.o)(u);return ed&&0===eu.length||!ed&&eu.some(Z.A)?null:(0,A.jsxs)(C,{transform:`translate(0, ${"bottom"===R?K+Q:K})`,className:q.root,sx:U,children:[!S&&(0,A.jsx)(er,(0,n.Z)({x1:X,x2:X+Y,className:q.line},V?.axisLine)),es.map((e,t)=>{let{formattedValue:r,offset:i,labelOffset:a,skipLabel:o}=e,s=a??0,l=J({x:i,y:-1},{direction:"x"}),u=J({x:i+s,y:-1},{direction:"x"});return(0,A.jsxs)("g",{transform:`translate(${i}, 0)`,className:q.tickContainer,children:[!_&&l&&(0,A.jsx)(en,(0,n.Z)({y2:et*ee,className:q.tick},V?.axisTick)),void 0!==r&&!o&&u&&(0,A.jsx)(ei,(0,n.Z)({x:s,y:et*(ee+3)},eo,{text:r.toString()}))]},t)}),E&&(0,A.jsx)("g",{className:q.label,children:(0,A.jsx)(ea,(0,n.Z)({},{x:X+Y/2,y:et*(ee+22)},el,{text:E}))})]})}var j=r(60445);let R=["scale","tickNumber"],S=e=>{let{classes:t,position:r}=e;return(0,l.Z)({root:["root","directionY",r],line:["line"],tickContainer:["tickContainer"],tick:["tick"],tickLabel:["tickLabel"],label:["label"]},f,t)},_=(0,u.ZP)(g,{name:"MuiChartsYAxis",slot:"Root",overridesResolver:(e,t)=>t.root})({}),$={position:"left",disableLine:!1,disableTicks:!1,tickFontSize:12,labelFontSize:14,tickSize:6};function E(e){let{yAxisIds:t,yAxis:r}=(0,a.r)(),i=r[e.axisId??t[0]],{scale:l,tickNumber:u}=i,p=(0,o.Z)(i,R),f=(0,d.Z)({props:(0,n.Z)({},p,e),name:"MuiChartsYAxis"}),m=(0,n.Z)({},$,f),{position:g,disableLine:y,disableTicks:b,tickFontSize:k,label:I,labelFontSize:C,labelStyle:M,tickLabelStyle:P,tickSize:E,valueFormatter:L,slots:z,slotProps:T,tickPlacement:O,tickLabelPlacement:N,tickInterval:F,tickLabelInterval:V,sx:H}=m,B=(0,c.Z)(),G=(0,j.V)(),D=S((0,n.Z)({},m,{theme:B})),{left:U,top:W,width:q,height:X,isPointInside:K}=(0,v.z)(),Y=b?4:E,Q=(0,h.g)({scale:l,tickNumber:u,valueFormatter:L,tickPlacement:O,tickLabelPlacement:N,tickInterval:F}),J="right"===g?1:-1,ee=z?.axisLine??"line",et=z?.axisTick??"line",er=z?.axisTickLabel??x.k,en=z?.axisLabel??x.k,ei=(0,s.Z)({elementType:er,externalSlotProps:T?.axisTickLabel,additionalProps:{style:(0,n.Z)({fontSize:k,textAnchor:!G&&"right"===g||G&&"right"!==g?"start":"end",dominantBaseline:"central"},P)},className:D.tickLabel,ownerState:{}}),ea=(0,s.Z)({elementType:en,externalSlotProps:T?.axisLabel,additionalProps:{style:(0,n.Z)({fontSize:C,angle:90*J,textAnchor:"middle",dominantBaseline:"auto"},M)},ownerState:{}}),eo=(0,s.Z)({elementType:ee,externalSlotProps:T?.axisLine,additionalProps:{strokeLinecap:"square"},ownerState:{}}),es=l.domain(),el=(0,w.o)(l);return el&&0===es.length||!el&&es.some(Z.A)?null:(0,A.jsxs)(_,{transform:`translate(${"right"===g?U+q:U}, 0)`,className:D.root,sx:H,children:[!y&&(0,A.jsx)(ee,(0,n.Z)({y1:W,y2:W+X,className:D.line},eo)),Q.map((e,t)=>{let{formattedValue:r,offset:i,labelOffset:a,value:o}=e,s="function"==typeof V&&!V?.(o,t);return K({x:-1,y:i},{direction:"y"})?(0,A.jsxs)("g",{transform:`translate(0, ${i})`,className:D.tickContainer,children:[!b&&(0,A.jsx)(et,(0,n.Z)({x2:J*Y,className:D.tick},T?.axisTick)),void 0!==r&&!s&&(0,A.jsx)(er,(0,n.Z)({x:J*(Y+2),y:a,text:r.toString()},ei))]},t):null}),I&&(0,A.jsx)("g",{className:D.label,children:(0,A.jsx)(en,(0,n.Z)({},{x:J*(k+Y+10),y:W+X/2},ea,{text:I}))})]})}let L=(e,t)=>null==e?null:"object"==typeof e?e.axisId??t??null:e,z=(e,t,r)=>"object"==typeof e?(0,n.Z)({},e,{slots:(0,n.Z)({},t,e?.slots),slotProps:(0,n.Z)({},r,e?.slotProps)}):{slots:t,slotProps:r};function T(e){let{topAxis:t,leftAxis:r,rightAxis:o,bottomAxis:s,slots:l,slotProps:u}=e,{xAxis:d,xAxisIds:c,yAxis:h,yAxisIds:p}=(0,a.r)(),f=L(void 0===r?p[0]:r,p[0]),m=L(void 0===s?c[0]:s,c[0]),g=L(t,c[0]),x=L(o,p[0]);if(null!==g&&!d[g])throw Error(`MUI X: id used for top axis "${g}" is not defined.
Available ids are: ${c.join(", ")}.`);if(null!==f&&!h[f])throw Error(`MUI X: id used for left axis "${f}" is not defined.
Available ids are: ${p.join(", ")}.`);if(null!==x&&!h[x])throw Error(`MUI X: id used for right axis "${x}" is not defined.
Available ids are: ${p.join(", ")}.`);if(null!==m&&!d[m])throw Error(`MUI X: id used for bottom axis "${m}" is not defined.
Available ids are: ${c.join(", ")}.`);let y=z(t,l,u),v=z(s,l,u),b=z(r,l,u),Z=z(o,l,u);return(0,A.jsxs)(i.Fragment,{children:[g&&(0,A.jsx)(P,(0,n.Z)({},y,{position:"top",axisId:g})),m&&(0,A.jsx)(P,(0,n.Z)({},v,{position:"bottom",axisId:m})),f&&(0,A.jsx)(E,(0,n.Z)({},b,{position:"left",axisId:f})),x&&(0,A.jsx)(E,(0,n.Z)({},Z,{position:"right",axisId:x}))]})}},84943:function(e,t,r){r.d(t,{G:function(){return E}});var n=r(74610),i=r(1119),a=r(2265),o=r(15988),s=r(20801),l=r(64119),u=r(31691),d=r(87463);let c={bar:e=>{let{seriesOrder:t,series:r}=e;return t.reduce((e,t)=>{let n=(0,d.i)(r[t].label,"legend");return void 0===n||e.push({id:t,seriesId:t,color:r[t].color,label:n}),e},[])},scatter:e=>{let{seriesOrder:t,series:r}=e;return t.reduce((e,t)=>{let n=(0,d.i)(r[t].label,"legend");return void 0===n||e.push({id:t,seriesId:t,color:r[t].color,label:n}),e},[])},line:e=>{let{seriesOrder:t,series:r}=e;return t.reduce((e,t)=>{let n=(0,d.i)(r[t].label,"legend");return void 0===n||e.push({id:t,seriesId:t,color:r[t].color,label:n}),e},[])},pie:e=>{let{seriesOrder:t,series:r}=e;return t.reduce((e,t)=>(r[t].data.forEach(r=>{let n=(0,d.i)(r.label,"legend");void 0!==n&&e.push({id:r.id,seriesId:t,color:r.color,label:n,itemId:r.id})}),e),[])}};var h=r(50738);function p(e){return(0,h.ZP)("MuiChartsLegend",e)}(0,r(94143).Z)("MuiChartsLegend",["root","series","itemBackground","mark","label","column","row"]);var f=r(53610),m=r(16210),g=r(62020);let x=["label"];var y=r(87811),v=r(61994),b=r(60445),Z=r(43440),w=r(57437);function A(e){let t=(0,b.V)(),{id:r,positionY:n,label:a,positionX:o,innerHeight:s,innerWidth:l,legendWidth:u,color:d,gapX:c,gapY:h,itemMarkHeight:p,itemMarkWidth:f,markGap:m,labelStyle:g,classes:x,onClick:y}=e;return(0,w.jsxs)("g",{className:(0,v.Z)(x?.series,`${x?.series}-${r}`),transform:`translate(${c+(t?u-o:o)} ${h+n})`,children:[(0,w.jsx)("rect",{x:t?-(l+2):-2,y:-p/2-2,width:l+4,height:s+4,fill:"transparent",className:x?.itemBackground,onClick:y,style:{pointerEvents:y?"all":"none",cursor:y?"pointer":"unset"}}),(0,w.jsx)("rect",{className:x?.mark,x:t?-f:0,y:-p/2,width:f,height:p,fill:d,style:{pointerEvents:"none"}}),(0,w.jsx)(Z.k,{style:(0,i.Z)({pointerEvents:"none"},g),text:a,x:(t?-1:1)*(f+m),y:0})]})}let k=["rotate","dominantBaseline"],I=(0,m.ZP)("g",{name:"MuiChartsLegend",slot:"Root",overridesResolver:(e,t)=>t.root})({}),C=e=>"number"==typeof e?{left:e,right:e,top:e,bottom:e}:(0,i.Z)({left:0,right:0,top:0,bottom:0},e);function M(e){let{position:t,direction:r,itemsToDisplay:o,classes:s,itemMarkWidth:l=20,itemMarkHeight:d=20,markGap:c=5,itemGap:h=10,padding:p=10,labelStyle:m,onItemClick:v}=e,b=(0,u.Z)(),Z=(0,y.z)(),M=a.useMemo(()=>(0,i.Z)({},b.typography.subtitle1,{color:"inherit",dominantBaseline:"central",textAnchor:"start",fill:(b.vars||b).palette.text.primary,lineHeight:1},m),[m,b]),P=a.useMemo(()=>C(p),[p]),j=a.useCallback(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=(0,n.Z)(t,k),a=(0,g.i)({style:r,needsComputation:!0,text:e}),o={innerWidth:l+c+Math.max(...a.map(e=>e.width)),innerHeight:Math.max(d,a.length*a[0].height)};return(0,i.Z)({},o,{outerWidth:o.innerWidth+h,outerHeight:o.innerHeight+h})},[h,d,l,c]),R=Z.left+Z.width+Z.right,S=Z.top+Z.height+Z.bottom,_=R-P.left-P.right,$=S-P.top-P.bottom,[E,L,z]=a.useMemo(()=>(function(e,t,r,a,o,s,l){let u=0,d=0,c=0,h=0,p=0,f=[0];return[e.map(e=>{let{label:m}=e,g=(0,n.Z)(e,x),y=t(m,r),v=(0,i.Z)({},g,{label:m,positionX:u,positionY:d,innerHeight:y.innerHeight,innerWidth:y.innerWidth,outerHeight:y.outerHeight,outerWidth:y.outerWidth,rowIndex:p});return"row"===a&&(u+y.innerWidth>o&&(u=0,d+=f[p],p+=1,f.length<=p&&f.push(0),v.positionX=u,v.positionY=d,v.rowIndex=p),c=Math.max(c,u+y.outerWidth),h=Math.max(h,d+y.outerHeight),f[p]=Math.max(f[p],y.outerHeight),u+=y.outerWidth),"column"===a&&(d+y.innerHeight>s&&(u=c+l,d=0,p=0,v.positionX=u,v.positionY=d,v.rowIndex=p),f.length<=p&&f.push(0),c=Math.max(c,u+y.outerWidth),h=Math.max(h,d+y.outerHeight),p+=1,d+=y.outerHeight),v}).map(e=>(0,i.Z)({},e,{positionY:e.positionY+("row"===a?f[e.rowIndex]/2:e.outerHeight/2)})),c,h]})(o,j,M,r,_,$,h),[o,j,M,r,_,$,h]),T=a.useMemo(()=>{switch(t.horizontal){case"left":return P.left;case"right":return R-P.right-L;default:return(R-L)/2}},[t.horizontal,P.left,P.right,R,L]),O=a.useMemo(()=>{switch(t.vertical){case"top":return P.top;case"bottom":return S-P.bottom-z;default:return(S-z)/2}},[t.vertical,P.top,P.bottom,S,z]);return(0,w.jsx)(f.Z,{children:(0,w.jsx)(I,{className:s?.root,children:E.map((e,t)=>(0,a.createElement)(A,(0,i.Z)({},e,{key:e.id,gapX:T,gapY:O,legendWidth:L,itemMarkHeight:d,itemMarkWidth:l,markGap:c,labelStyle:M,classes:s,onClick:v?e=>v(e,t):void 0})))})})}let P=["drawingArea","seriesToDisplay","hidden","onItemClick"],j=e=>({type:"series",color:e.color,label:e.label,seriesId:e.seriesId,itemId:e.itemId});function R(e){let{seriesToDisplay:t,hidden:r,onItemClick:a}=e,o=(0,n.Z)(e,P);return r?null:(0,w.jsx)(M,(0,i.Z)({},o,{itemsToDisplay:t,onItemClick:a?(e,r)=>a(e,j(t[r]),r):void 0}))}var S=r(78405);let _=["slots","slotProps"],$=e=>{let{classes:t,direction:r}=e;return(0,s.Z)({root:["root",r],mark:["mark"],label:["label"],series:["series"],itemBackground:["itemBackground"]},p,t)};function E(e){let t=(0,l.Z)({props:e,name:"MuiChartsLegend"}),r=(0,i.Z)({direction:"row"},t,{position:(0,i.Z)({horizontal:"middle",vertical:"top"},t.position)}),{slots:a,slotProps:s}=r,d=(0,n.Z)(r,_),h=(0,u.Z)(),p=$((0,i.Z)({},r,{theme:h})),f=(0,y.z)(),m=(0,S.us)(),g=Object.keys(m).flatMap(e=>{let t=c[e];return void 0===t?[]:t(m[e])}),x=a?.legend??R,v=(0,o.Z)({elementType:x,externalSlotProps:s?.legend,additionalProps:(0,i.Z)({},d,{classes:p,drawingArea:f,series:m,seriesToDisplay:g}),ownerState:{}});return(0,w.jsx)(x,(0,i.Z)({},v))}},30329:function(e,t,r){r.d(t,{I:function(){return m}});var n=r(1119);r(2265);var i=r(74610),a=r(16210),o=r(87811),s=r(57437);let l=["message"],u=(0,a.ZP)("text")(e=>{let{theme:t}=e;return(0,n.Z)({},t.typography.body2,{stroke:"none",fill:t.palette.text.primary,shapeRendering:"crispEdges",textAnchor:"middle",dominantBaseline:"middle"})});function d(e){let{message:t}=e,r=(0,i.Z)(e,l),{top:a,left:d,height:c,width:h}=(0,o.z)();return(0,s.jsx)(u,(0,n.Z)({x:d+h/2,y:a+c/2},r,{children:t??"Loading data…"}))}var c=r(78405);let h=["message"],p=(0,a.ZP)("text")(e=>{let{theme:t}=e;return(0,n.Z)({},t.typography.body2,{stroke:"none",fill:t.palette.text.primary,shapeRendering:"crispEdges",textAnchor:"middle",dominantBaseline:"middle"})});function f(e){let{message:t}=e,r=(0,i.Z)(e,h),{top:a,left:l,height:u,width:d}=(0,o.z)();return(0,s.jsx)(p,(0,n.Z)({x:l+d/2,y:a+u/2},r,{children:t??"No data to display"}))}function m(e){let t=Object.values((0,c.us)()).every(e=>{if(!e)return!0;let{series:t,seriesOrder:r}=e;return r.every(e=>0===t[e].data.length)});if(e.loading){let t=e.slots?.loadingOverlay??d;return(0,s.jsx)(t,(0,n.Z)({},e.slotProps?.loadingOverlay))}if(t){let t=e.slots?.noDataOverlay??f;return(0,s.jsx)(t,(0,n.Z)({},e.slotProps?.noDataOverlay))}return null}},43440:function(e,t,r){r.d(t,{k:function(){return d}});var n=r(1119),i=r(74610),a=r(2265),o=r(62020),s=r(57437);let l=["x","y","style","text","ownerState"],u=["angle","textAnchor","dominantBaseline"];function d(e){let t;let{x:r,y:d,style:c,text:h}=e,p=(0,i.Z)(e,l),f=c??{},{angle:m,textAnchor:g,dominantBaseline:x}=f,y=(0,i.Z)(f,u),v=a.useMemo(()=>(0,o.i)({style:y,needsComputation:h.includes("\n"),text:h}),[y,h]);switch(x){case"hanging":t=0;break;case"central":t=-((v.length-1)/2*v[0].height);break;default:t=-((v.length-1)*v[0].height)}let b=[];return m&&b.push(`rotate(${m}, ${r}, ${d})`),(0,s.jsx)("text",(0,n.Z)({},p,{transform:b.length>0?b.join(" "):void 0,x:r,y:d,textAnchor:g,dominantBaseline:x,style:y,children:v.map((e,n)=>(0,s.jsx)("tspan",{x:r,dy:`${0===n?t:v[0].height}px`,dominantBaseline:x,children:e.text},n))}))}},99276:function(e,t,r){let n;r.d(t,{a:function(){return H}});var i=r(1119),a=r(2265),o=r(20801),s=r(16210),l=r(64119),u=r(48467),d=r(53610),c=r(15988),h=r(79795),p=r(80414),f=r(61994),m=r(38145),g=r(50738);function x(e){return(0,g.ZP)("MuiChartsTooltip",e)}let y=(0,r(94143).Z)("MuiChartsTooltip",["root","paper","table","row","cell","mark","markCell","labelCell","valueCell"]),v=(0,s.ZP)("div",{name:"MuiChartsTooltip",slot:"Container",overridesResolver:(e,t)=>t.paper})(({theme:e})=>({boxShadow:e.shadows[1],backgroundColor:(e.vars||e).palette.background.paper,color:(e.vars||e).palette.text.primary,transition:e.transitions.create("box-shadow"),borderRadius:e.shape.borderRadius})),b=(0,s.ZP)("table",{name:"MuiChartsTooltip",slot:"Table",overridesResolver:(e,t)=>t.table})(({theme:e})=>({borderSpacing:0,"& thead td":{borderBottom:`solid ${(e.vars||e).palette.divider} 1px`}})),Z=(0,s.ZP)("tr",{name:"MuiChartsTooltip",slot:"Row",overridesResolver:(e,t)=>t.row})(({theme:e})=>({"tr:first-of-type& td":{paddingTop:e.spacing(1)},"tr:last-of-type& td":{paddingBottom:e.spacing(1)}})),w=(0,s.ZP)("td",{name:"MuiChartsTooltip",slot:"Cell",overridesResolver:(e,t)=>t.cell})(({theme:e})=>({verticalAlign:"middle",color:(e.vars||e).palette.text.secondary,[`&.${y.labelCell}`]:{paddingLeft:e.spacing(1)},[`&.${y.valueCell}`]:{paddingLeft:e.spacing(4),color:(e.vars||e).palette.text.primary},"td:first-of-type&":{paddingLeft:e.spacing(2)},"td:last-of-type&":{paddingRight:e.spacing(2)}})),A=(0,s.ZP)("div",{name:"MuiChartsTooltip",slot:"Mark",overridesResolver:(e,t)=>t.mark,shouldForwardProp:e=>(0,m.x9)(e)&&"color"!==e})(({theme:e,color:t})=>({width:e.spacing(1),height:e.spacing(1),borderRadius:"50%",boxShadow:e.shadows[1],background:t,borderColor:(e.vars||e).palette.background.paper,border:`solid ${(e.vars||e).palette.background.paper} ${e.spacing(.25)}`,boxSizing:"content-box"}));var k=r(87463),I=r(57437);function C(e){let{series:t,itemData:r,sx:n,classes:a,getColor:o}=e;if(void 0===r.dataIndex||!t.data[r.dataIndex])return null;let{displayedLabel:s,color:l}="pie"===t.type?{color:o(r.dataIndex),displayedLabel:(0,k.i)(t.data[r.dataIndex].label,"tooltip")}:{color:o(r.dataIndex),displayedLabel:(0,k.i)(t.label,"tooltip")},u="pie"===t.type?(0,i.Z)({},t.data[r.dataIndex],{label:(0,k.i)(t.data[r.dataIndex].label,"tooltip")}):t.data[r.dataIndex],d=t.valueFormatter?.(u,{dataIndex:r.dataIndex});return(0,I.jsx)(v,{sx:n,className:a.paper,children:(0,I.jsx)(b,{className:a.table,children:(0,I.jsx)("tbody",{children:(0,I.jsxs)(Z,{className:a.row,children:[(0,I.jsx)(w,{className:(0,f.Z)(a.markCell,a.cell),children:(0,I.jsx)(A,{color:l,className:a.mark})}),(0,I.jsx)(w,{className:(0,f.Z)(a.labelCell,a.cell),children:s}),(0,I.jsx)(w,{className:(0,f.Z)(a.valueCell,a.cell),children:d})]})})})})}var M=r(94102),P=r(57332),j=r(77981);function R(e){let{isInitialized:t,data:r}=a.useContext(j.Z);if(!t)throw Error("MUI X: Could not find the plugin context.\nIt looks like you rendered your component outside of a ChartsContainer parent component.");return e?r.colorProcessors[e]:r.colorProcessors}var S=r(78405);function _(e){let{content:t,itemData:r,sx:n,classes:o,contentProps:s}=e,l=(0,S.us)()[r.type].series[r.seriesId],{xAxis:u,yAxis:d,xAxisIds:h,yAxisIds:p}=(0,M.r)(),{zAxis:f,zAxisIds:m}=a.useContext(P.W),g=R(),x=l.xAxisId??l.xAxisKey??h[0],y=l.yAxisId??l.yAxisKey??p[0],v=l.zAxisId??l.zAxisKey??m[0],b=g[l.type]?.(l,x&&u[x],y&&d[y],v&&f[v])??(()=>""),Z=t??C,w=(0,c.Z)({elementType:Z,externalSlotProps:s,additionalProps:{itemData:r,series:l,sx:n,classes:o,getColor:b},ownerState:{}});return(0,I.jsx)(Z,(0,i.Z)({},w))}var $=r(46387);class E{constructor(){if(this.types=new Set,n)throw Error("You can only create one instance!");n=this.types}addType(e){this.types.add(e)}getTypes(){return this.types}}let L=new E;function z(e){return L.getTypes().has(e)}function T(e){return z(e.type)}function O(e){let{series:t,axis:r,dataIndex:n,axisValue:i,sx:a,classes:o}=e;if(null==n)return null;let s=r.valueFormatter??(e=>"utc"===r.scaleType&&e instanceof Date?e.toUTCString():e.toLocaleString());return(0,I.jsx)(v,{sx:a,className:o.paper,children:(0,I.jsxs)(b,{className:o.table,children:[null!=i&&!r.hideTooltip&&(0,I.jsx)("thead",{children:(0,I.jsx)(Z,{children:(0,I.jsx)(w,{colSpan:3,children:(0,I.jsx)($.default,{children:s(i,{location:"tooltip"})})})})}),(0,I.jsx)("tbody",{children:t.filter(T).map(e=>{let{id:t,label:r,valueFormatter:i,data:a,getColor:s}=e,l=i(a[n]??null,{dataIndex:n});if(null==l)return null;let u=(0,k.i)(r,"tooltip"),d=s(n);return(0,I.jsxs)(Z,{className:o.row,children:[(0,I.jsx)(w,{className:(0,f.Z)(o.markCell,o.cell),children:d&&(0,I.jsx)(A,{color:d,className:o.mark})}),(0,I.jsx)(w,{className:(0,f.Z)(o.labelCell,o.cell),children:u?(0,I.jsx)($.default,{children:u}):null}),(0,I.jsx)(w,{className:(0,f.Z)(o.valueCell,o.cell),children:(0,I.jsx)($.default,{children:l})})]},t)})})]})})}function N(e){let{content:t,contentProps:r,axisData:n,sx:o,classes:s}=e,l=n.x&&-1!==n.x.index,u=l?n.x&&n.x.index:n.y&&n.y.index,d=l?n.x&&n.x.value:n.y&&n.y.value,{xAxisIds:h,xAxis:p,yAxisIds:f,yAxis:m}=(0,M.r)(),{zAxisIds:g,zAxis:x}=a.useContext(P.W),y=(0,S.us)(),v=R(),b=l?h[0]:f[0],Z=a.useMemo(()=>{let e=[];return Object.keys(y).filter(z).forEach(t=>{y[t].seriesOrder.forEach(r=>{let n=y[t].series[r],a=n.xAxisId??n.xAxisKey,o=n.yAxisId??n.yAxisKey,s=l?a:o;if(void 0===s||s===b){let n=y[t].series[r],s=a??h[0],l=o??f[0],u=n.zAxisId??n.zAxisKey??g[0],d=v[t]?.(n,p[s],m[l],u&&x[u])??(()=>"");e.push((0,i.Z)({},n,{getColor:d}))}})}),e},[b,v,l,y,p,h,m,f,x,g]),w=a.useMemo(()=>l?p[b]:m[b],[b,l,p,m]),A=t??O,k=(0,c.Z)({elementType:A,externalSlotProps:r,additionalProps:{axisData:n,series:Z,axis:w,dataIndex:u,axisValue:d,sx:o,classes:s},ownerState:{}});return(0,I.jsx)(A,(0,i.Z)({},k))}L.addType("bar"),L.addType("line"),L.addType("scatter");let F=e=>{let{classes:t}=e;return(0,o.Z)({root:["root"],paper:["paper"],table:["table"],row:["row"],cell:["cell"],mark:["mark"],markCell:["markCell"],labelCell:["labelCell"],valueCell:["valueCell"]},x,t)},V=(0,s.ZP)(u.Z,{name:"MuiChartsTooltip",slot:"Root",overridesResolver:(e,t)=>t.root})(e=>{let{theme:t}=e;return{pointerEvents:"none",zIndex:t.zIndex.modal}});function H(e){let t=(0,l.Z)({props:e,name:"MuiChartsTooltip"}),{trigger:r="axis",itemContent:n,axisContent:o,slots:s,slotProps:u}=t,f=function(){let e=(0,p.S)(),[t,r]=a.useState(null);return a.useEffect(()=>{let t=e.current;if(null===t)return()=>{};let n=e=>{"mouse"!==e.pointerType&&r(null)},i=e=>{r({x:e.clientX,y:e.clientY,height:e.height,pointerType:e.pointerType})};return t.addEventListener("pointerdown",i),t.addEventListener("pointermove",i),t.addEventListener("pointerup",n),()=>{t.removeEventListener("pointerdown",i),t.removeEventListener("pointermove",i),t.removeEventListener("pointerup",n)}},[e]),t}(),{item:m,axis:g}=a.useContext(h.s),x="item"===r?m:g,y=function(e,t){if("item"===e)return null!==t;let r=null!==t.x,n=null!==t.y;return r||n}(r,x),v=null!==f&&y,b=F({classes:t.classes}),Z=s?.popper??V,w=(0,c.Z)({elementType:Z,externalSlotProps:u?.popper,additionalProps:{open:v,placement:f?.pointerType==="mouse"?"right-start":"top",anchorEl:function(e){if(null===e)return{getBoundingClientRect:()=>({width:0,height:0,x:0,y:0,top:0,right:0,bottom:0,left:0,toJSON:()=>""})};let{x:t,y:r}=e,n={width:0,height:0,x:t,y:r,top:r,right:t,bottom:r,left:t};return{getBoundingClientRect:()=>(0,i.Z)({},n,{toJSON:()=>JSON.stringify(n)})}}(f),modifiers:[{name:"offset",options:{offset:[0,f?.pointerType==="touch"?40-f.height:0]}}]},ownerState:{}});return"none"===r?null:(0,I.jsx)(d.Z,{children:v&&(0,I.jsx)(Z,(0,i.Z)({},w,{className:b.root,children:"item"===r?(0,I.jsx)(_,{itemData:x,content:s?.itemContent??n,contentProps:u?.itemContent,sx:{mx:2},classes:b}):(0,I.jsx)(N,{axisData:x,content:s?.axisContent??o,contentProps:u?.axisContent,sx:{mx:2},classes:b})}))})}},64143:function(e,t,r){r.d(t,{u:function(){return eu}});var n=r(1119),i=r(74610),a=r(2265),o=r(60445),s=r(64119),l=r(49507),u=r(62216),d=r(70337),c=r(99276),h=r(84943),p=r(82878),f=r(96100),m=r(91434),g=r(76115),x=r(88425),y=r(67790);function v(e){return e.innerRadius}function b(e){return e.outerRadius}function Z(e){return e.startAngle}function w(e){return e.endAngle}function A(e){return e&&e.padAngle}function k(e,t,r,n,i,a,o){var s=e-r,l=t-n,u=(o?a:-a)/(0,x._b)(s*s+l*l),d=u*l,c=-u*s,h=e+d,p=t+c,f=r+d,m=n+c,g=(h+f)/2,y=(p+m)/2,v=f-h,b=m-p,Z=v*v+b*b,w=i-a,A=h*m-f*p,k=(b<0?-1:1)*(0,x._b)((0,x.Fp)(0,w*w*Z-A*A)),I=(A*b-v*k)/Z,C=(-A*v-b*k)/Z,M=(A*b+v*k)/Z,P=(-A*v+b*k)/Z,j=I-g,R=C-y,S=M-g,_=P-y;return j*j+R*R>S*S+_*_&&(I=M,C=P),{cx:I,cy:C,x01:-d,y01:-c,x11:I*(i/w-1),y11:C*(i/w-1)}}function I(){var e=v,t=b,r=(0,g.Z)(0),n=null,i=Z,a=w,o=A,s=null,l=(0,y.d)(u);function u(){var u,d,c=+e.apply(this,arguments),h=+t.apply(this,arguments),p=i.apply(this,arguments)-x.ou,f=a.apply(this,arguments)-x.ou,m=(0,x.Wn)(f-p),g=f>p;if(s||(s=u=l()),h<c&&(d=h,h=c,c=d),h>x.Ho){if(m>x.BZ-x.Ho)s.moveTo(h*(0,x.mC)(p),h*(0,x.O$)(p)),s.arc(0,0,h,p,f,!g),c>x.Ho&&(s.moveTo(c*(0,x.mC)(f),c*(0,x.O$)(f)),s.arc(0,0,c,f,p,g));else{var y,v,b=p,Z=f,w=p,A=f,I=m,C=m,M=o.apply(this,arguments)/2,P=M>x.Ho&&(n?+n.apply(this,arguments):(0,x._b)(c*c+h*h)),j=(0,x.VV)((0,x.Wn)(h-c)/2,+r.apply(this,arguments)),R=j,S=j;if(P>x.Ho){var _=(0,x.ZR)(P/c*(0,x.O$)(M)),$=(0,x.ZR)(P/h*(0,x.O$)(M));(I-=2*_)>x.Ho?(_*=g?1:-1,w+=_,A-=_):(I=0,w=A=(p+f)/2),(C-=2*$)>x.Ho?($*=g?1:-1,b+=$,Z-=$):(C=0,b=Z=(p+f)/2)}var E=h*(0,x.mC)(b),L=h*(0,x.O$)(b),z=c*(0,x.mC)(A),T=c*(0,x.O$)(A);if(j>x.Ho){var O,N=h*(0,x.mC)(Z),F=h*(0,x.O$)(Z),V=c*(0,x.mC)(w),H=c*(0,x.O$)(w);if(m<x.pi){if(O=function(e,t,r,n,i,a,o,s){var l=r-e,u=n-t,d=o-i,c=s-a,h=c*l-d*u;if(!(h*h<x.Ho))return h=(d*(t-a)-c*(e-i))/h,[e+h*l,t+h*u]}(E,L,V,H,N,F,z,T)){var B=E-O[0],G=L-O[1],D=N-O[0],U=F-O[1],W=1/(0,x.O$)((0,x.Kh)((B*D+G*U)/((0,x._b)(B*B+G*G)*(0,x._b)(D*D+U*U)))/2),q=(0,x._b)(O[0]*O[0]+O[1]*O[1]);R=(0,x.VV)(j,(c-q)/(W-1)),S=(0,x.VV)(j,(h-q)/(W+1))}else R=S=0}}C>x.Ho?S>x.Ho?(y=k(V,H,E,L,h,S,g),v=k(N,F,z,T,h,S,g),s.moveTo(y.cx+y.x01,y.cy+y.y01),S<j?s.arc(y.cx,y.cy,S,(0,x.fv)(y.y01,y.x01),(0,x.fv)(v.y01,v.x01),!g):(s.arc(y.cx,y.cy,S,(0,x.fv)(y.y01,y.x01),(0,x.fv)(y.y11,y.x11),!g),s.arc(0,0,h,(0,x.fv)(y.cy+y.y11,y.cx+y.x11),(0,x.fv)(v.cy+v.y11,v.cx+v.x11),!g),s.arc(v.cx,v.cy,S,(0,x.fv)(v.y11,v.x11),(0,x.fv)(v.y01,v.x01),!g))):(s.moveTo(E,L),s.arc(0,0,h,b,Z,!g)):s.moveTo(E,L),c>x.Ho&&I>x.Ho?R>x.Ho?(y=k(z,T,N,F,c,-R,g),v=k(E,L,V,H,c,-R,g),s.lineTo(y.cx+y.x01,y.cy+y.y01),R<j?s.arc(y.cx,y.cy,R,(0,x.fv)(y.y01,y.x01),(0,x.fv)(v.y01,v.x01),!g):(s.arc(y.cx,y.cy,R,(0,x.fv)(y.y01,y.x01),(0,x.fv)(y.y11,y.x11),!g),s.arc(0,0,c,(0,x.fv)(y.cy+y.y11,y.cx+y.x11),(0,x.fv)(v.cy+v.y11,v.cx+v.x11),g),s.arc(v.cx,v.cy,R,(0,x.fv)(v.y11,v.x11),(0,x.fv)(v.y01,v.x01),!g))):s.arc(0,0,c,A,w,g):s.lineTo(z,T)}}else s.moveTo(0,0);if(s.closePath(),u)return s=null,u+""||null}return u.centroid=function(){var r=(+e.apply(this,arguments)+ +t.apply(this,arguments))/2,n=(+i.apply(this,arguments)+ +a.apply(this,arguments))/2-x.pi/2;return[(0,x.mC)(n)*r,(0,x.O$)(n)*r]},u.innerRadius=function(t){return arguments.length?(e="function"==typeof t?t:(0,g.Z)(+t),u):e},u.outerRadius=function(e){return arguments.length?(t="function"==typeof e?e:(0,g.Z)(+e),u):t},u.cornerRadius=function(e){return arguments.length?(r="function"==typeof e?e:(0,g.Z)(+e),u):r},u.padRadius=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,g.Z)(+e),u):n},u.startAngle=function(e){return arguments.length?(i="function"==typeof e?e:(0,g.Z)(+e),u):i},u.endAngle=function(e){return arguments.length?(a="function"==typeof e?e:(0,g.Z)(+e),u):a},u.padAngle=function(e){return arguments.length?(o="function"==typeof e?e:(0,g.Z)(+e),u):o},u.context=function(e){return arguments.length?(s=null==e?null:e,u):s},u}var C=r(20801),M=r(50738),P=r(16210),j=r(94143),R=r(72636),S=r(57437);let _=["classes","color","cornerRadius","dataIndex","endAngle","id","innerRadius","isFaded","isHighlighted","onClick","outerRadius","paddingAngle","startAngle","highlightScope"];function $(e){return(0,M.ZP)("MuiPieArc",e)}(0,j.Z)("MuiPieArc",["root","highlighted","faded"]);let E=e=>{let{classes:t,id:r,isFaded:n,isHighlighted:i,dataIndex:a}=e,o={root:["root",`series-${r}`,`data-index-${a}`,i&&"highlighted",n&&"faded"]};return(0,C.Z)(o,$,t)},L=(0,P.ZP)(m.q.path,{name:"MuiPieArc",slot:"Root",overridesResolver:(e,t)=>t.arc})(e=>{let{theme:t}=e;return{stroke:(t.vars||t).palette.background.paper,transition:"opacity 0.2s ease-in, fill 0.2s ease-in, filter 0.2s ease-in"}});function z(e){let{classes:t,color:r,cornerRadius:a,dataIndex:o,endAngle:s,id:l,innerRadius:u,isFaded:d,isHighlighted:c,onClick:h,outerRadius:p,paddingAngle:f,startAngle:g}=e,x=(0,i.Z)(e,_),y={id:l,dataIndex:o,classes:t,color:r,isFaded:d,isHighlighted:c},v=E(y),b=(0,R.J)();return(0,S.jsx)(L,(0,n.Z)({d:(0,m.to)([g,s,f,u,p,a],(e,t,r,n,i,a)=>I().cornerRadius(a)({padAngle:r,startAngle:e,endAngle:t,innerRadius:n,outerRadius:i})),visibility:(0,m.to)([g,s],(e,t)=>e===t?"hidden":"visible"),onClick:h,cursor:h?"pointer":"unset",ownerState:y,className:v.root,fill:y.color,opacity:y.isFaded?.3:1,filter:y.isHighlighted?"brightness(120%)":"none",strokeWidth:1,strokeLinejoin:"round"},x,b({type:"pie",seriesId:l,dataIndex:o})))}let T={keys:e=>e.id,from:({innerRadius:e,outerRadius:t,cornerRadius:r,startAngle:n,endAngle:i,paddingAngle:a,color:o,isFaded:s})=>({innerRadius:e,outerRadius:(e+t)/2,cornerRadius:r,startAngle:(n+i)/2,endAngle:(n+i)/2,paddingAngle:a,fill:o,opacity:s?.3:1}),leave:({innerRadius:e,startAngle:t,endAngle:r})=>({innerRadius:e,outerRadius:e,startAngle:(t+r)/2,endAngle:(t+r)/2}),enter:({innerRadius:e,outerRadius:t,startAngle:r,endAngle:n})=>({innerRadius:e,outerRadius:t,startAngle:r,endAngle:n}),update:({innerRadius:e,outerRadius:t,cornerRadius:r,startAngle:n,endAngle:i,paddingAngle:a,color:o,isFaded:s})=>({innerRadius:e,outerRadius:t,cornerRadius:r,startAngle:n,endAngle:i,paddingAngle:a,fill:o,opacity:s?.3:1}),config:{tension:120,friction:14,clamp:!0}},O={keys:e=>e.id,from:({innerRadius:e,outerRadius:t,arcLabelRadius:r,cornerRadius:n,startAngle:i,endAngle:a,paddingAngle:o})=>({innerRadius:e,outerRadius:(e+t)/2,cornerRadius:n,arcLabelRadius:r,startAngle:(i+a)/2,endAngle:(i+a)/2,paddingAngle:o,opacity:0}),leave:({innerRadius:e,startAngle:t,endAngle:r})=>({innerRadius:e,outerRadius:e,arcLabelRadius:e,startAngle:(t+r)/2,endAngle:(t+r)/2,opacity:0}),enter:({innerRadius:e,outerRadius:t,startAngle:r,endAngle:n,arcLabelRadius:i})=>({innerRadius:e,outerRadius:t,startAngle:r,endAngle:n,arcLabelRadius:i,opacity:1}),update:({innerRadius:e,outerRadius:t,cornerRadius:r,startAngle:n,endAngle:i,paddingAngle:a,arcLabelRadius:o})=>({innerRadius:e,outerRadius:t,cornerRadius:r,startAngle:n,endAngle:i,paddingAngle:a,arcLabelRadius:o,opacity:1}),config:{tension:120,friction:14,clamp:!0}};var N=r(42457);function F(e){let{id:t,data:r,faded:i,highlighted:o,paddingAngle:s=0,innerRadius:l=0,arcLabelRadius:u,outerRadius:d,cornerRadius:c=0}=e,{isFaded:h,isHighlighted:p}=(0,N.j)();return a.useMemo(()=>r.map((e,r)=>{let a={seriesId:t,dataIndex:r},f=p(a),m=!f&&h(a),g=(0,n.Z)({additionalRadius:0},m&&i||f&&o||{}),x=Math.max(0,Math.PI*(g.paddingAngle??s)/180),y=Math.max(0,g.innerRadius??l),v=Math.max(0,g.outerRadius??d+g.additionalRadius),b=g.cornerRadius??c,Z=g.arcLabelRadius??u??(y+v)/2;return(0,n.Z)({},e,g,{isFaded:m,isHighlighted:f,paddingAngle:x,innerRadius:y,outerRadius:v,cornerRadius:b,arcLabelRadius:Z})}),[c,l,d,s,u,r,i,o,h,p,t])}let V=["slots","slotProps","innerRadius","outerRadius","cornerRadius","paddingAngle","id","highlighted","faded","data","onItemClick","skipAnimation"];function H(e){let{slots:t,slotProps:r,innerRadius:a=0,outerRadius:o,cornerRadius:s=0,paddingAngle:l=0,id:u,highlighted:d,faded:c={additionalRadius:-5},data:h,onItemClick:p,skipAnimation:f}=e,g=(0,i.Z)(e,V),x=F({innerRadius:a,outerRadius:o,cornerRadius:s,paddingAngle:l,id:u,highlighted:d,faded:c,data:h}),y=(0,m.Yz)(x,(0,n.Z)({},T,{immediate:f})),{highlightScope:v}=(0,N.j)();if(0===h.length)return null;let b=t?.pieArc??z;return(0,S.jsx)("g",(0,n.Z)({},g,{children:y((e,t,i,a)=>{let{startAngle:o,endAngle:s,paddingAngle:l,innerRadius:d,outerRadius:c,cornerRadius:h}=e;return(0,S.jsx)(b,(0,n.Z)({startAngle:o,endAngle:s,paddingAngle:l,innerRadius:d,outerRadius:c,cornerRadius:h,id:u,color:t.color,dataIndex:a,highlightScope:v,isFaded:t.isFaded,isHighlighted:t.isHighlighted,onClick:p&&(e=>{p(e,{type:"pie",seriesId:u,dataIndex:a},t)})},r?.pieArc))})}))}let B=["id","classes","color","startAngle","endAngle","paddingAngle","arcLabelRadius","innerRadius","outerRadius","cornerRadius","formattedArcLabel","isHighlighted","isFaded","style"];function G(e){return(0,M.ZP)("MuiPieArcLabel",e)}(0,j.Z)("MuiPieArcLabel",["root","highlighted","faded"]);let D=e=>{let{classes:t,id:r,isFaded:n,isHighlighted:i}=e,a={root:["root",`series-${r}`,i&&"highlighted",n&&"faded"]};return(0,C.Z)(a,G,t)},U=(0,P.ZP)(m.q.text,{name:"MuiPieArcLabel",slot:"Root",overridesResolver:(e,t)=>t.root})(e=>{let{theme:t}=e;return{fill:(t.vars||t).palette.text.primary,textAnchor:"middle",dominantBaseline:"middle",pointerEvents:"none"}}),W=(e,t)=>(r,n,i,a,o)=>{if(!e)return 0;let[s,l]=I().cornerRadius(o).centroid({padAngle:i,startAngle:r,endAngle:n,innerRadius:a,outerRadius:a});return"x"===t?s:l};function q(e){let{id:t,classes:r,color:a,startAngle:o,endAngle:s,paddingAngle:l,arcLabelRadius:u,cornerRadius:d,formattedArcLabel:c,isHighlighted:h,isFaded:p,style:f}=e,g=(0,i.Z)(e,B),x=D({id:t,classes:r,color:a,isFaded:p,isHighlighted:h});return(0,S.jsx)(U,(0,n.Z)({className:x.root},g,{style:(0,n.Z)({x:(0,m.to)([o,s,l,u,d],W(c,"x")),y:(0,m.to)([o,s,l,u,d],W(c,"y"))},f),children:c}))}var X=r(87463);let K=["arcLabel","arcLabelMinAngle","arcLabelRadius","cornerRadius","data","faded","highlighted","id","innerRadius","outerRadius","paddingAngle","skipAnimation","slotProps","slots"],Y=["startAngle","endAngle","paddingAngle","innerRadius","outerRadius","arcLabelRadius","cornerRadius"],Q=180/Math.PI;function J(e){let{arcLabel:t,arcLabelMinAngle:r=0,arcLabelRadius:a,cornerRadius:o=0,data:s,faded:l={additionalRadius:-5},highlighted:u,id:d,innerRadius:c,outerRadius:h,paddingAngle:p=0,skipAnimation:f,slotProps:g,slots:x}=e,y=(0,i.Z)(e,K),v=F({innerRadius:c,outerRadius:h,arcLabelRadius:a,cornerRadius:o,paddingAngle:p,id:d,highlighted:u,faded:l,data:s}),b=(0,m.Yz)(v,(0,n.Z)({},O,{immediate:f}));if(0===s.length)return null;let Z=x?.pieArcLabel??q;return(0,S.jsx)("g",(0,n.Z)({},y,{children:b((e,a)=>{let{startAngle:o,endAngle:s,paddingAngle:l,innerRadius:u,outerRadius:c,arcLabelRadius:h,cornerRadius:p}=e,f=(0,i.Z)(e,Y);return(0,S.jsx)(Z,(0,n.Z)({startAngle:o,endAngle:s,paddingAngle:l,innerRadius:u,outerRadius:c,arcLabelRadius:h,cornerRadius:p,style:f,id:d,color:a.color,isFaded:a.isFaded,isHighlighted:a.isHighlighted,formattedArcLabel:function(e,t,r){if(!e||(r.endAngle-r.startAngle)*Q<t)return null;switch(e){case"label":return(0,X.i)(r.label,"arc");case"value":return r.value?.toString();case"formattedValue":return r.formattedValue;default:return e((0,n.Z)({},r,{label:(0,X.i)(r.label,"arc")}))}}(t,r,a)},g?.pieArcLabel))})}))}function ee(e,t){if("number"==typeof e)return e;if("100%"===e)return t;if(e.endsWith("%")){let r=Number.parseFloat(e.slice(0,e.length-1));if(!Number.isNaN(r))return r*t/100}if(e.endsWith("px")){let t=Number.parseFloat(e.slice(0,e.length-2));if(!Number.isNaN(t))return t}throw Error(`MUI X: Received an unknown value "${e}". It should be a number, or a string with a percentage value.`)}function et(e,t){let{height:r,width:n}=t,{cx:i,cy:a}=e,o=Math.min(n,r)/2;return{cx:ee(i??"50%",n),cy:ee(a??"50%",r),availableRadius:o}}var er=r(78405),en=r(66924);function ei(e){let{skipAnimation:t,slots:r,slotProps:n,onItemClick:i}=e,o=(0,er.aH)(),{left:s,top:l,width:u,height:d}=a.useContext(f.SV),c=(0,en.r)(t);if(void 0===o)return null;let{series:h,seriesOrder:p}=o;return(0,S.jsxs)("g",{children:[p.map(e=>{let{innerRadius:t,outerRadius:a,cornerRadius:o,paddingAngle:p,data:f,cx:m,cy:g,highlighted:x,faded:y}=h[e],{cx:v,cy:b,availableRadius:Z}=et({cx:m,cy:g},{width:u,height:d}),w=ee(a??Z,Z),A=ee(t??0,Z);return(0,S.jsx)("g",{transform:`translate(${s+v}, ${l+b})`,children:(0,S.jsx)(H,{innerRadius:A,outerRadius:w,cornerRadius:o,paddingAngle:p,id:e,data:f,skipAnimation:c,highlighted:x,faded:y,onItemClick:i,slots:r,slotProps:n})},e)}),p.map(e=>{let{innerRadius:t,outerRadius:i,arcLabelRadius:a,cornerRadius:o,paddingAngle:p,arcLabel:f,arcLabelMinAngle:m,data:g,cx:x,cy:y}=h[e],{cx:v,cy:b,availableRadius:Z}=et({cx:x,cy:y},{width:u,height:d}),w=ee(i??Z,Z),A=ee(t??0,Z),k=void 0===a?(w+A)/2:ee(a,Z);return(0,S.jsx)("g",{transform:`translate(${s+v}, ${l+b})`,children:(0,S.jsx)(J,{innerRadius:A,outerRadius:w??Z,arcLabelRadius:k,cornerRadius:o,paddingAngle:p,id:e,data:g,skipAnimation:c,arcLabel:f,arcLabelMinAngle:m,slots:r,slotProps:n})},e)})]})}var ea=r(30329);let eo=["xAxis","yAxis","series","width","height","margin","colors","sx","tooltip","axisHighlight","skipAnimation","legend","topAxis","leftAxis","rightAxis","bottomAxis","children","slots","slotProps","onItemClick","loading","highlightedItem","onHighlightChange","className"],es={top:5,bottom:5,left:5,right:100},el={top:5,bottom:5,left:100,right:5},eu=a.forwardRef(function(e,t){let r=(0,s.Z)({props:e,name:"MuiPieChart"}),{xAxis:a,yAxis:f,series:m,width:g,height:x,margin:y,colors:v,sx:b,tooltip:Z={trigger:"item"},axisHighlight:w={x:"none",y:"none"},skipAnimation:A,legend:k,topAxis:I=null,leftAxis:C=null,rightAxis:M=null,bottomAxis:P=null,children:j,slots:R,slotProps:_,onItemClick:$,loading:E,highlightedItem:L,onHighlightChange:z,className:T}=r,O=(0,i.Z)(r,eo),N=(0,o.V)(),F=(0,n.Z)({},N?el:es,y),V=(0,n.Z)({direction:"column",position:{vertical:"middle",horizontal:N?"left":"right"}},k);return(0,S.jsxs)(l.D,(0,n.Z)({},O,{ref:t,series:m.map(e=>(0,n.Z)({type:"pie"},e)),width:g,height:x,margin:F,xAxis:a??[{id:d.nk,scaleType:"point",data:[...Array(Math.max(0,...m.map(e=>e.data.length)))].map((e,t)=>t)}],yAxis:f,colors:v,sx:b,disableAxisListener:Z?.trigger!=="axis"&&w?.x==="none"&&w?.y==="none",highlightedItem:L,onHighlightChange:z,className:T,skipAnimation:A,children:[(0,S.jsx)(u.q,{topAxis:I,leftAxis:C,rightAxis:M,bottomAxis:P,slots:R,slotProps:_}),(0,S.jsx)(ei,{slots:R,slotProps:_,onItemClick:$}),(0,S.jsx)(ea.I,{loading:E,slots:R,slotProps:_}),(0,S.jsx)(h.G,(0,n.Z)({},V,{slots:R,slotProps:_})),(0,S.jsx)(p.qe,(0,n.Z)({},w)),!E&&(0,S.jsx)(c.a,(0,n.Z)({},Z,{slots:R,slotProps:_})),j]}))})},49507:function(e,t,r){r.d(t,{D:function(){return eK}});var n=r(1119),i=r(2265),a=r(96100),o=r(31691);let s=["#02B2AF","#2E96FF","#B800D8","#60009B","#2731C8","#03008D"],l=["#02B2AF","#72CCFF","#DA00FF","#9001CB","#2E96FF","#3B48E0"],u=e=>"dark"===e?l:s;var d=r(70532);let c=["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"],h=({series:e,colors:t,seriesFormatters:r,dataset:i})=>{let a={};e.forEach((e,r)=>{let{id:i=`auto-generated-id-${r}`,type:o}=e;if(void 0===a[o]&&(a[o]={series:{},seriesOrder:[]}),a[o]?.series[i]!==void 0)throw Error(`MUI X: series' id "${i}" is not unique.`);a[o].series[i]=(0,n.Z)({id:i},function(e,t,r=c){return"pie"===e.type?(0,n.Z)({},e,{data:e.data.map((e,t)=>(0,n.Z)({color:r[t%r.length]},e))}):(0,n.Z)({color:r[t%r.length]},e)}(e,r,t)),a[o].seriesOrder.push(i)});let o={};return Object.keys(r).forEach(e=>{let t=a[e];void 0!==t&&(o[e]=r[e]?.(t,i)??a[e])}),o};var p=r(77981),f=r(57437);function m(e){let{series:t,dataset:r,colors:n=u,children:a}=e,s=function(e){let{isInitialized:t,data:r}=i.useContext(p.Z);if(!t)throw Error("MUI X: Could not find the plugin context.\nIt looks like you rendered your component outside of a ChartsContainer parent component.");return r.seriesFormatters}(),l=(0,o.Z)(),c=i.useMemo(()=>({isInitialized:!0,data:h({series:t,colors:"function"==typeof n?n(l.palette.mode):n,seriesFormatters:s,dataset:r})}),[t,n,l.palette.mode,s,r]);return(0,f.jsx)(d.a.Provider,{value:c,children:a})}var g=r(79795),x=r(74610),y=r(16210),v=r(64119),b=r(94102),Z=r(34881),w=r(80414),A=r(87811);function k(e){return e instanceof Date?e.getTime():e}let I=e=>{let t=(0,w.S)(),r=(0,A.z)(),{xAxis:n,yAxis:a,xAxisIds:o,yAxisIds:s}=(0,b.r)(),{dispatch:l}=i.useContext(g.s),u=o[0],d=s[0],c=i.useRef({isInChart:!1,x:-1,y:-1});i.useEffect(()=>{let i=t.current;if(null===i||e)return()=>{};function o(e,t){let{scale:r,data:n,reverse:i}=e;if(!(0,Z.o)(r)){let e=r.invert(t);if(void 0===n)return{value:e,index:-1};let i=k(e),a=n?.findIndex((t,r)=>{let a=k(t);return!!(a>i&&(0===r||Math.abs(i-a)<=Math.abs(i-k(n[r-1])))||a<=i&&(r===n.length-1||Math.abs(k(e)-a)<Math.abs(k(e)-k(n[r+1]))))});return{value:void 0!==a&&a>=0?n[a]:e,index:a}}let a=0===r.bandwidth()?Math.floor((t-Math.min(...r.range())+r.step()/2)/r.step()):Math.floor((t-Math.min(...r.range()))/r.step());if(a<0||a>=n.length)return null;if(i){let e=n.length-1-a;return{index:e,value:n[e]}}return{index:a,value:n[a]}}let s=()=>{c.current={isInChart:!1,x:-1,y:-1},l({type:"exitChart"})},h=e=>{let t=function(e,t){let r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,r.matrixTransform(e.getScreenCTM().inverse())}(i,"targetTouches"in e?e.targetTouches[0]:e);if(c.current.x=t.x,c.current.y=t.y,!r.isPointInside(t,{targetElement:e.target})){c.current.isInChart&&(l({type:"exitChart"}),c.current.isInChart=!1);return}c.current.isInChart=!0,l({type:"updateAxis",data:{x:o(n[u],t.x),y:o(a[d],t.y)}})},p=e=>{let t=e.currentTarget;t&&t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId)};return i.addEventListener("pointerdown",p),i.addEventListener("pointermove",h),i.addEventListener("pointerout",s),i.addEventListener("pointercancel",s),i.addEventListener("pointerleave",s),()=>{i.removeEventListener("pointerdown",p),i.removeEventListener("pointermove",h),i.removeEventListener("pointerout",s),i.removeEventListener("pointercancel",s),i.removeEventListener("pointerleave",s)}},[t,l,d,a,u,n,e,r])},C=["children","width","height","viewBox","disableAxisListener","className","title","desc"],M=(0,y.ZP)("svg",{name:"MuiChartsSurface",slot:"Root"})(()=>({touchAction:"none"})),P=i.forwardRef(function(e,t){let r=(0,v.Z)({props:e,name:"MuiChartsSurface"}),{children:i,width:a,height:o,viewBox:s,disableAxisListener:l=!1,className:u,title:d,desc:c}=r,h=(0,x.Z)(r,C),p=(0,n.Z)({width:a,height:o,x:0,y:0},s);return I(l),(0,f.jsxs)(M,(0,n.Z)({width:a,height:o,viewBox:`${p.x} ${p.y} ${p.width} ${p.height}`,ref:t,className:u},h,{children:[(0,f.jsx)("title",{children:d}),(0,f.jsx)("desc",{children:c}),i]}))});var j=r(93330),R=r(55284),S=r(35836),_=r(98235),$=r(28576),E=r(42756),L=r(58347),z=r(23327),T=r(94534);let O=(e,t)=>{let r=e[1]-e[0],n=t[1]-t[0];return[e[0]-t[0]*r/n,e[1]+(100-t[1])*r/n]},N=(e,t,r,n,i,a,o)=>{let s=n[t],l=a[t]?.series??{},[u,d]=s?.({series:l,axis:r,axisIndex:i,isDefaultAxis:0===i,getFilters:o})??[1/0,-1/0],[c,h]=e;return[Math.min(u,c),Math.max(d,h)]},F=(e,t,r,n,i)=>{let a=Object.keys(t).reduce((a,o)=>N(a,o,e,t,r,n,i),[1/0,-1/0]);return Number.isNaN(a[0])||Number.isNaN(a[1])?[1/0,-1/0]:a},V=e=>e?.[0] instanceof Date;function H(e,t){let r=(0,j.Z)(e.data,t);return(t,{location:n})=>"tick"===n?r.tickFormat(e.tickNumber)(t):`${t.toLocaleString()}`}function B({drawingArea:e,formattedSeries:t,axis:r,extremumGetters:i,axisDirection:a,zoomData:o,zoomOptions:s,getFilters:l}){let u={};return r.forEach((r,d)=>{let c=s?.[r.id],h=o?.find(({axisId:e})=>e===r.id),p=h?[h.start,h.end]:[0,100],f=function(e,t,r){if("rotation"===t){let{startAngle:e=0,endAngle:t=e+360}=r;return r.reverse?[Math.PI*e/180,Math.PI*t/180]:[Math.PI*t/180,Math.PI*e/180]}if("radius"===t){let{minRadius:t=0,maxRadius:n=Math.min(e.width,e.height)/2}=r;return[t,n]}let n="x"===t?[e.left,e.left+e.width]:[e.top+e.height,e.top];return r.reverse?[n[1],n[0]]:n}(e,a,r),[m,g]=F(r,i,d,t,void 0!==h||c?void 0:l),x=r.data??[];if((0,S.Q)(r)){let e=r.categoryGapRatio??.2,t=r.barGapRatio??.1,i="y"===a?[f[1],f[0]]:f,o=O(i,p);if(u[r.id]=(0,n.Z)({categoryGapRatio:e,barGapRatio:t},r,{data:x,scale:(0,R.Z)(r.data,o).paddingInner(e).paddingOuter(e/2),tickNumber:r.data.length,colorScale:r.colorMap&&("ordinal"===r.colorMap.type?(0,_.Kc)((0,n.Z)({values:r.data},r.colorMap)):(0,_.pI)(r.colorMap))}),V(r.data)){let e=H(r,i);u[r.id].valueFormatter=r.valueFormatter??e}}if((0,S.T)(r)){let e="y"===a?[...f].reverse():f,t=O(e,p);if(u[r.id]=(0,n.Z)({},r,{data:x,scale:(0,R.x)(r.data,t),tickNumber:r.data.length,colorScale:r.colorMap&&("ordinal"===r.colorMap.type?(0,_.Kc)((0,n.Z)({values:r.data},r.colorMap)):(0,_.pI)(r.colorMap))}),V(r.data)){let t=H(r,e);u[r.id].valueFormatter=r.valueFormatter??t}}if("band"===r.scaleType||"point"===r.scaleType)return;let y=r.scaleType??"linear",v=r.domainLimit??"nice",b=[r.min??m,r.max??g];if("function"==typeof v){let{min:e,max:t}=v(m,g);b[0]=e,b[1]=t}let Z=(0,$.f)((0,n.Z)({},r,{range:f,domain:b})),w=Z/((p[1]-p[0])/100),A=function(e,t,r){switch(e){case"log":return(0,E.Z)(t,r);case"pow":return(0,L.ZP)(t,r);case"sqrt":return(0,L._b)(t,r);case"time":return(0,j.Z)(t,r);case"utc":return(0,z.Z)(t,r);default:return(0,T.Z)(t,r)}}(y,b,O(f,p)),k="nice"===v?A.nice(Z):A,[I,C]=k.domain(),M=[r.min??I,r.max??C];u[r.id]=(0,n.Z)({},r,{data:x,scaleType:y,scale:k.domain(M),tickNumber:w,colorScale:r.colorMap&&(0,_.pI)(r.colorMap)})}),{axis:u,axisIds:r.map(({id:e})=>e)}}var G=r(78405),D=r(20818);function U(e){let{xAxis:t,yAxis:r,children:n}=e,a=(0,G.us)(),o=(0,A.z)(),s=function(e){let{isInitialized:t,data:r}=i.useContext(p.Z);if(!t)throw Error("MUI X: Could not find the plugin context.\nIt looks like you rendered your component outside of a ChartsContainer parent component.");return r.xExtremumGetters}(),l=function(e){let{isInitialized:t,data:r}=i.useContext(p.Z);if(!t)throw Error("MUI X: Could not find the plugin context.\nIt looks like you rendered your component outside of a ChartsContainer parent component.");return r.yExtremumGetters}(),u=i.useMemo(()=>B({drawingArea:o,formattedSeries:a,axis:t,extremumGetters:s,axisDirection:"x"}),[o,a,t,s]),d=i.useMemo(()=>B({drawingArea:o,formattedSeries:a,axis:r,extremumGetters:l,axisDirection:"y"}),[o,a,r,l]),c=i.useMemo(()=>({isInitialized:!0,data:{xAxis:u.axis,yAxis:d.axis,xAxisIds:u.axisIds,yAxisIds:d.axisIds}}),[u,d]);return(0,f.jsx)(D.S.Provider,{value:c,children:n})}function W(e){let{isReversed:t,gradientId:r,size:n,direction:a,scale:o,colorMap:s}=e;return n<=0?null:(0,f.jsx)("linearGradient",{id:r,x1:"0",x2:"0",y1:"0",y2:"0",[`${a}${t?1:2}`]:`${n}px`,gradientUnits:"userSpaceOnUse",children:s.thresholds.map((e,r)=>{let a=o(e);if(void 0===a)return null;let l=t?1-a/n:a/n;return(0,f.jsxs)(i.Fragment,{children:[(0,f.jsx)("stop",{offset:l,stopColor:s.colors[r],stopOpacity:1}),(0,f.jsx)("stop",{offset:l,stopColor:s.colors[r+1],stopOpacity:1})]},e.toString()+r)})})}var q=r(44193),X=r(16673);function K(e){let{gradientUnits:t,isReversed:r,gradientId:n,size:i,direction:a,scale:o,colorScale:s,colorMap:l}=e,u=[l.min??0,l.max??100],d=u.map(o).filter(e=>void 0!==e);if(2!==d.length)return null;let c="number"==typeof u[0]?(0,q.Z)(u[0],u[1]):(0,X.Z)(u[0],u[1]),h=Math.round((Math.max(...d)-Math.min(...d))/10),p=`${u[0]}-${u[1]}-`;return(0,f.jsx)("linearGradient",{id:n,x1:"0",x2:"0",y1:"0",y2:"0",[`${a}${r?1:2}`]:"objectBoundingBox"===t?1:`${i}px`,gradientUnits:t??"userSpaceOnUse",children:Array.from({length:h+1},(e,t)=>{let n=c(t/h);if(void 0===n)return null;let a=o(n);if(void 0===a)return null;let l=s(n);return null===l?null:(0,f.jsx)("stop",{offset:r?1-a/i:a/i,stopColor:l,stopOpacity:1},p+t)})})}function Y(){let{top:e,height:t,bottom:r,left:n,width:o,right:s}=(0,A.z)(),l=e+t+r,u=n+o+s,d=function(){let{chartId:e}=i.useContext(a.SV);return i.useCallback((t,r)=>`${e}-gradient-${r}-${t}`,[e])}(),{xAxisIds:c,xAxis:h,yAxisIds:p,yAxis:m}=(0,b.r)();return(0,f.jsxs)("defs",{children:[p.filter(e=>void 0!==m[e].colorMap).map(e=>{let t=d(e,"y"),{colorMap:r,scale:n,colorScale:i,reverse:a}=m[e];return r?.type==="piecewise"?(0,f.jsx)(W,{isReversed:!a,scale:n,colorMap:r,size:l,gradientId:t,direction:"y"},t):r?.type==="continuous"?(0,f.jsx)(K,{isReversed:!a,scale:n,colorScale:i,colorMap:r,size:l,gradientId:t,direction:"y"},t):null}),c.filter(e=>void 0!==h[e].colorMap).map(e=>{let t=d(e,"x"),{colorMap:r,scale:n,reverse:i,colorScale:a}=h[e];return r?.type==="piecewise"?(0,f.jsx)(W,{isReversed:i,scale:n,colorMap:r,size:u,gradientId:t,direction:"x"},t):r?.type==="continuous"?(0,f.jsx)(K,{isReversed:i,scale:n,colorScale:a,colorMap:r,size:u,gradientId:t,direction:"x"},t):null})]})}var Q=r(57332),J=r(38462),ee=r(50661);let et=(e,t)=>r=>!!e&&("series"===e.fade?r.seriesId===t?.seriesId&&r.dataIndex!==t?.dataIndex:"global"===e.fade&&(r.seriesId!==t?.seriesId||r.dataIndex!==t?.dataIndex)),er=(e,t)=>r=>!!e&&("series"===e.highlight?r.seriesId===t?.seriesId:"item"===e.highlight&&r.dataIndex===t?.dataIndex&&r.seriesId===t?.seriesId),en=["highlighted","faded"],ei=e=>{let t=e??{},{highlighted:r,faded:i}=t,a=(0,x.Z)(t,en);return(0,n.Z)({highlight:r,fade:i},a)};function ea(e){let{children:t,highlightedItem:r,onHighlightChange:n}=e,[a,o]=(0,J.Z)({controlled:r,default:null,name:"HighlightedProvider",state:"highlightedItem"}),s=(0,G.us)(),l=i.useMemo(()=>{let e=new Map;return Object.keys(s).forEach(t=>{let r=s[t];Object.keys(r?.series??{}).forEach(t=>{let n=r?.series[t];e.set(t,ei(n?.highlightScope))})}),e},[s]),u=a&&a.seriesId?l.get(a.seriesId)??void 0:void 0,d=i.useMemo(()=>({isInitialized:!0,data:{highlightScope:u,highlightedItem:a,setHighlighted:e=>{o(e),n?.(e)},clearHighlighted:()=>{o(null),n?.(null)},isHighlighted:er(u,a),isFaded:et(u,a)}}),[a,u,o,n]);return(0,f.jsx)(ee.t.Provider,{value:d,children:t})}let eo=(e,t)=>"x"===t?{x:e,y:null}:{x:null,y:e},es=e=>{let{axis:t,getFilters:r,isDefaultAxis:n}=e,i=r?.({currentAxisId:t.id,isDefaultAxis:n}),a=i?t.data?.filter((e,t)=>i({x:null,y:null},t)):t.data;return[Math.min(...a??[]),Math.max(...a??[])]},el=e=>t=>{let{series:r,axis:n,getFilters:i,isDefaultAxis:a}=t;return Object.keys(r).filter(e=>{let t=r[e].yAxisId??r[e].yAxisKey;return t===n.id||a&&void 0===t}).reduce((t,o)=>{let{stackedData:s}=r[o],l=i?.({currentAxisId:n.id,isDefaultAxis:a,seriesXAxisId:r[o].xAxisId??r[o].xAxisKey,seriesYAxisId:r[o].yAxisId??r[o].yAxisKey}),[u,d]=s?.reduce((t,r,n)=>!l||l(eo(r[0],e),n)&&l(eo(r[1],e),n)?[Math.min(...r,t[0]),Math.max(...r,t[1])]:t,[1/0,-1/0])??[1/0,-1/0];return[Math.min(u,t[0]),Math.max(d,t[1])]},[1/0,-1/0])};var eu=r(63263),ed=r(76585);function ec(e){var t=e.map(eh);return(0,ed.Z)(e).sort(function(e,r){return t[e]-t[r]})}function eh(e){for(var t,r=-1,n=0,i=e.length,a=-1/0;++r<i;)(t=+e[r][1])>a&&(a=t,n=r);return n}function ep(e){var t=e.map(ef);return(0,ed.Z)(e).sort(function(e,r){return t[e]-t[r]})}function ef(e){for(var t,r=0,n=-1,i=e.length;++n<i;)(t=+e[n][1])&&(r+=t);return r}var em=r(87565),eg=r(17033),ex=r(37889),ey=r(43928);let ev={appearance:ec,ascending:ep,descending:function(e){return ep(e).reverse()},insideOut:function(e){var t,r,n=e.length,i=e.map(ef),a=ec(e),o=0,s=0,l=[],u=[];for(t=0;t<n;++t)r=a[t],o<s?(o+=i[r],l.push(r)):(s+=i[r],u.push(r));return u.reverse().concat(l)},none:ed.Z,reverse:function(e){return(0,ed.Z)(e).reverse()}},eb={expand:em.Z,diverging:function(e,t){if((s=e.length)>0)for(var r,n,i,a,o,s,l=0,u=e[t[0]].length;l<u;++l)for(a=o=0,r=0;r<s;++r)(i=(n=e[t[r]][l])[1]-n[0])>0?(n[0]=a,n[1]=a+=i):i<0?(n[1]=o,n[0]=o+=i):(n[0]=0,n[1]=i)},none:eg.Z,silhouette:ex.Z,wiggle:ey.Z},eZ=e=>{let{series:t,seriesOrder:r,defaultStrategy:n}=e,i=[],a={};return r.forEach(e=>{let{stack:r,stackOrder:o,stackOffset:s}=t[e];void 0===r?i.push({ids:[e],stackingOrder:ev.none,stackingOffset:eb.none}):void 0===a[r]?(a[r]=i.length,i.push({ids:[e],stackingOrder:ev[o??n?.stackOrder??"none"],stackingOffset:eb[s??n?.stackOffset??"diverging"]})):(i[a[r]].ids.push(e),void 0!==o&&(i[a[r]].stackingOrder=ev[o]),void 0!==s&&(i[a[r]].stackingOffset=eb[s]))}),i};function ew(e,t){let r={};return Object.keys(e).forEach(i=>{r[i]=(0,n.Z)({},e[i],{valueFormatter:e[i].valueFormatter??t})}),r}let eA={seriesType:"bar",seriesFormatter:(e,t)=>{let{seriesOrder:r,series:i}=e,a=eZ(e),o=t??[];r.forEach(e=>{let r=i[e].data;if(void 0!==r)r.forEach((t,r)=>{o.length<=r?o.push({[e]:t}):o[r][e]=t});else if(void 0===t)throw Error(`MUI X: bar series with id='${e}' has no data.
Either provide a data property to the series or use the dataset prop.`)});let s={};return a.forEach(e=>{let{ids:r,stackingOffset:a,stackingOrder:l}=e,u=(0,eu.Z)().keys(r.map(e=>{let t=i[e].dataKey;return void 0===i[e].data&&void 0!==t?t:e})).value((e,t)=>e[t]??0).order(l).offset(a)(o);r.forEach((e,r)=>{let a=i[e].dataKey;s[e]=(0,n.Z)({layout:"vertical"},i[e],{data:a?t.map(e=>{let t=e[a];return"number"!=typeof t?0:t}):i[e].data,stackedData:u[r].map(([e,t])=>[e,t])})})}),{seriesOrder:r,stackingGroups:a,series:ew(s,e=>null==e?"":e.toLocaleString())}},colorProcessor:r(36314).Z,xExtremumGetter:e=>Object.keys(e.series).some(t=>"horizontal"===e.series[t].layout)?el("x")(e):es(e),yExtremumGetter:e=>Object.keys(e.series).some(t=>"horizontal"===e.series[t].layout)?es(e):el("y")(e)},ek=(e,t)=>[null===t[0]?e[0]:Math.min(e[0],t[0]),null===t[1]?e[1]:Math.max(e[1],t[1])];var eI=r(22516),eC=r(76115);function eM(e,t){return t<e?-1:t>e?1:t>=e?0:NaN}function eP(e){return e}var ej=r(88425),eR=r(87463);let eS=(e="none")=>{if("function"==typeof e)return e;switch(e){case"none":default:return null;case"desc":return(e,t)=>t-e;case"asc":return(e,t)=>e-t}},e_=[eA,{seriesType:"scatter",seriesFormatter:({series:e,seriesOrder:t},r)=>({series:Object.fromEntries(Object.entries(e).map(([e,t])=>{let i=t?.datasetKeys,a=["x","y","id"].filter(e=>"string"!=typeof i?.[e]);if(t?.datasetKeys&&a.length>0)throw Error(`MUI X: scatter series with id='${e}' has incomplete datasetKeys.
Properties ${a.map(e=>`"${e}"`).join(", ")} are missing.`);let o=i?r?.map(e=>({x:e[i.x]??null,y:e[i.y]??null,z:i.z&&e[i.z],id:e[i.id]}))??[]:t.data??[];return[e,(0,n.Z)({},t,{data:o,valueFormatter:t.valueFormatter??(e=>`(${e.x}, ${e.y})`)})]})),seriesOrder:t}),colorProcessor:(e,t,r,n)=>{let i=n?.colorScale,a=r?.colorScale,o=t?.colorScale;return i?t=>{if(n?.data?.[t]!==void 0){let e=i(n?.data?.[t]);if(null!==e)return e}let r=e.data[t],a=null===r?e.color:i(r.z);return null===a?e.color:a}:a?t=>{let r=e.data[t],n=null===r?e.color:a(r.y);return null===n?e.color:n}:o?t=>{let r=e.data[t],n=null===r?e.color:o(r.x);return null===n?e.color:n}:()=>e.color},xExtremumGetter:e=>{let{series:t,axis:r,isDefaultAxis:n,getFilters:i}=e;return Object.keys(t).filter(e=>{let i=t[e].xAxisId??t[e].xAxisKey;return i===r.id||void 0===i&&n}).reduce((e,a)=>{let o=i?.({currentAxisId:r.id,isDefaultAxis:n,seriesXAxisId:t[a].xAxisId??t[a].xAxisKey,seriesYAxisId:t[a].yAxisId??t[a].yAxisKey}),s=t[a].data?.reduce((e,t,r)=>o&&!o(t,r)?e:ek(e,[t.x,t.x]),[1/0,-1/0]);return ek(e,s??[1/0,-1/0])},[1/0,-1/0])},yExtremumGetter:e=>{let{series:t,axis:r,isDefaultAxis:n,getFilters:i}=e;return Object.keys(t).filter(e=>{let i=t[e].yAxisId??t[e].yAxisKey;return i===r.id||void 0===i&&n}).reduce((e,a)=>{let o=i?.({currentAxisId:r.id,isDefaultAxis:n,seriesXAxisId:t[a].xAxisId??t[a].xAxisKey,seriesYAxisId:t[a].yAxisId??t[a].yAxisKey}),s=t[a].data?.reduce((e,t,r)=>o&&!o(t,r)?e:ek(e,[t.y,t.y]),[1/0,-1/0]);return ek(e,s??[1/0,-1/0])},[1/0,-1/0])}},{seriesType:"line",colorProcessor:(e,t,r)=>{let n=r?.colorScale,i=t?.colorScale;return n?t=>{let r=e.data[t],i=null===r?e.color:n(r);return null===i?e.color:i}:i?r=>{let n=t.data?.[r],a=null===n?e.color:i(n);return null===a?e.color:a}:()=>e.color},seriesFormatter:(e,t)=>{let{seriesOrder:r,series:i}=e,a=eZ((0,n.Z)({},e,{defaultStrategy:{stackOffset:"none"}})),o=t??[];r.forEach(e=>{let t=i[e].data;void 0!==t&&t.forEach((t,r)=>{o.length<=r?o.push({[e]:t}):o[r][e]=t})});let s={};return a.forEach(e=>{let{ids:r,stackingOrder:a,stackingOffset:l}=e,u=(0,eu.Z)().keys(r.map(e=>{let t=i[e].dataKey;return void 0===i[e].data&&void 0!==t?t:e})).value((e,t)=>e[t]??0).order(a).offset(l)(o);r.forEach((e,r)=>{let a=i[e].dataKey;s[e]=(0,n.Z)({},i[e],{data:a?t.map(e=>{let t=e[a];return"number"!=typeof t?null:t}):i[e].data,stackedData:u[r].map(([e,t])=>[e,t])})})}),{seriesOrder:r,stackingGroups:a,series:ew(s,e=>null==e?"":e.toLocaleString())}},xExtremumGetter:e=>{let{axis:t}=e;return[Math.min(...t.data??[]),Math.max(...t.data??[])]},yExtremumGetter:e=>{let{series:t,axis:r,isDefaultAxis:n,getFilters:i}=e;return Object.keys(t).filter(e=>{let i=t[e].yAxisId??t[e].yAxisKey;return i===r.id||n&&void 0===i}).reduce((e,a)=>{var o;let{area:s,stackedData:l}=t[a],u=i?.({currentAxisId:r.id,isDefaultAxis:n,seriesXAxisId:t[a].xAxisId??t[a].xAxisKey,seriesYAxisId:t[a].yAxisId??t[a].yAxisKey}),[d,c]=(o=void 0!==s&&"log"!==r.scaleType&&"string"!=typeof t[a].baseline?e=>e:e=>[e[1],e[1]],l.reduce((e,t,r)=>{let[n,i]=o(t);return!u||u({y:n,x:null},r)&&u({y:i,x:null},r)?[Math.min(n,i,e[0]),Math.max(n,i,e[1])]:e},[1/0,-1/0]));return[Math.min(d,e[0]),Math.max(c,e[1])]},[1/0,-1/0])}},{seriesType:"pie",colorProcessor:e=>t=>e.data[t].color,seriesFormatter:e=>{let{seriesOrder:t,series:r}=e,i={};return t.forEach(e=>{let t=(function(){var e=eP,t=eM,r=null,n=(0,eC.Z)(0),i=(0,eC.Z)(ej.BZ),a=(0,eC.Z)(0);function o(o){var s,l,u,d,c,h=(o=(0,eI.Z)(o)).length,p=0,f=Array(h),m=Array(h),g=+n.apply(this,arguments),x=Math.min(ej.BZ,Math.max(-ej.BZ,i.apply(this,arguments)-g)),y=Math.min(Math.abs(x)/h,a.apply(this,arguments)),v=y*(x<0?-1:1);for(s=0;s<h;++s)(c=m[f[s]=s]=+e(o[s],s,o))>0&&(p+=c);for(null!=t?f.sort(function(e,r){return t(m[e],m[r])}):null!=r&&f.sort(function(e,t){return r(o[e],o[t])}),s=0,u=p?(x-h*v)/p:0;s<h;++s,g=d)d=g+((c=m[l=f[s]])>0?c*u:0)+v,m[l]={data:o[l],index:s,value:c,startAngle:g,endAngle:d,padAngle:y};return m}return o.value=function(t){return arguments.length?(e="function"==typeof t?t:(0,eC.Z)(+t),o):e},o.sortValues=function(e){return arguments.length?(t=e,r=null,o):t},o.sort=function(e){return arguments.length?(r=e,t=null,o):r},o.startAngle=function(e){return arguments.length?(n="function"==typeof e?e:(0,eC.Z)(+e),o):n},o.endAngle=function(e){return arguments.length?(i="function"==typeof e?e:(0,eC.Z)(+e),o):i},o.padAngle=function(e){return arguments.length?(a="function"==typeof e?e:(0,eC.Z)(+e),o):a},o})().startAngle(2*Math.PI*(r[e].startAngle??0)/360).endAngle(2*Math.PI*(r[e].endAngle??360)/360).padAngle(2*Math.PI*(r[e].paddingAngle??0)/360).sortValues(eS(r[e].sortingValues??"none"))(r[e].data.map(e=>e.value));i[e]=(0,n.Z)({valueFormatter:e=>e.value.toLocaleString()},r[e],{data:r[e].data.map((r,i)=>(0,n.Z)({},r,{id:r.id??`auto-generated-pie-id-${e}-${i}`},t[i])).map((t,i)=>(0,n.Z)({},t,{formattedValue:r[e].valueFormatter?.(n.Z({},t,{label:eR.i(t.label,"arc")}),{dataIndex:i})??t.value.toLocaleString()}))})}),{seriesOrder:t,series:i}}}];function e$(e){let{children:t,plugins:r}=e,n=i.useMemo(()=>({isInitialized:!0,data:function(e){let t=e??e_,r={},n={},i={},a={},o={},s={};for(let e=0;e<t.length;e+=1){let l=t[e],u=l.seriesType;r[u]=l.seriesFormatter,n[u]=l.colorProcessor,l.xExtremumGetter&&(i[u]=l.xExtremumGetter),l.yExtremumGetter&&(a[u]=l.yExtremumGetter),l.rotationExtremumGetter&&(o[u]=l.rotationExtremumGetter),l.radiusExtremumGetter&&(s[u]=l.radiusExtremumGetter)}return{seriesFormatters:r,colorProcessors:n,xExtremumGetters:i,yExtremumGetters:a,rotationExtremumGetters:o,radiusExtremumGetters:s}}(r)}),[r]);return(0,f.jsx)(p.Z.Provider,{value:n,children:t})}var eE=r(23947),eL=r(70337);let ez=(e,t,r)=>{let i="x"===r?eL.nk:eL.Vd;return[...e?.map((e,t)=>n.Z({id:`defaultized-${r}-axis-${t}`},e))??[],...void 0===e||-1===e.findIndex(e=>{let{id:t}=e;return t===i})?[{id:i,scaleType:"linear"}]:[]].map(e=>{let i=e.dataKey;if(void 0===i||void 0!==e.data)return e;if(void 0===t)throw Error(`MUI X: ${r}-axis uses \`dataKey\` but no \`dataset\` is provided.`);return(0,n.Z)({},e,{data:t.map(e=>e[i])})})},eT=(e,t,r)=>[i.useMemo(()=>ez(e,r,"x"),[e,r]),i.useMemo(()=>ez(t,r,"y"),[t,r])],eO=["width","height","series","margin","xAxis","yAxis","zAxis","colors","dataset","sx","title","desc","disableAxisListener","highlightedItem","onHighlightChange","plugins","children","skipAnimation"],eN=(e,t)=>{let{width:r,height:a,series:o,margin:s,xAxis:l,yAxis:u,zAxis:d,colors:c,dataset:h,sx:p,title:f,desc:m,disableAxisListener:g,highlightedItem:y,onHighlightChange:v,plugins:b,children:Z,skipAnimation:w}=e,A=(0,x.Z)(e,eO),k=i.useRef(null),I=(0,eE.Z)(t,k),[C,M]=eT(l,u,h),P=(0,n.Z)({},A,{width:r,height:a,ref:I,sx:p,title:f,desc:m,disableAxisListener:g});return{children:Z,drawingProviderProps:{width:r,height:a,margin:s,svgRef:k},seriesProviderProps:{series:o,colors:c,dataset:h},cartesianProviderProps:{xAxis:C,yAxis:M,dataset:h},zAxisContextProps:{zAxis:d,dataset:h},highlightedProviderProps:{highlightedItem:y,onHighlightChange:v},chartsSurfaceProps:P,pluginProviderProps:{plugins:b},animationProviderProps:{skipAnimation:w},xAxis:C,yAxis:M}};var eF=r(91434),eV=r(49887);function eH(e){let{children:t,skipAnimation:r}=e,n="undefined"==typeof window||!window?.matchMedia,[a,o]=i.useState(n||void 0);(0,eF.LI)(()=>{if(n)return;let e=e=>{let t=e.matches||void 0;o(t),eF.OH.assign({skipAnimation:t})},t=window.matchMedia("(prefers-reduced-motion)");return e(t),t?.addEventListener?.("change",e),()=>{t?.removeEventListener?.("change",e)}},[]);let s=i.useMemo(()=>({isInitialized:!0,data:{skipAnimation:r||a}}),[a,r]);return(0,f.jsx)(eV.s.Provider,{value:s,children:t})}let eB=i.forwardRef(function(e,t){let{children:r,drawingProviderProps:i,seriesProviderProps:o,cartesianProviderProps:s,zAxisContextProps:l,highlightedProviderProps:u,chartsSurfaceProps:d,pluginProviderProps:c,animationProviderProps:h}=eN(e,t);return(0,f.jsx)(a.X_,(0,n.Z)({},i,{children:(0,f.jsx)(e$,(0,n.Z)({},c,{children:(0,f.jsx)(m,(0,n.Z)({},o,{children:(0,f.jsx)(U,(0,n.Z)({},s,{children:(0,f.jsx)(Q.n,(0,n.Z)({},l,{children:(0,f.jsx)(g.o,{children:(0,f.jsx)(ea,(0,n.Z)({},u,{children:(0,f.jsxs)(P,(0,n.Z)({},d,{children:[(0,f.jsx)(Y,{}),(0,f.jsx)(eH,(0,n.Z)({},h,{children:r}))]}))}))})}))}))}))}))}))}),eG=(0,y.ZP)("div",{name:"MuiResponsiveChart",slot:"Container"})(({ownerState:e})=>({width:e.width??"100%",height:e.height??"100%",display:"flex",position:"relative",flexGrow:1,flexDirection:"column",alignItems:"center",justifyContent:"center",overflow:"hidden","&>svg":{width:"100%",height:"100%"}}));var eD=r(3450),eU=r(42109);let eW=(e,t,r)=>{let n=i.useRef({displayError:!1,initialCompute:!0,computeRun:0}),a=i.useRef(null),[o,s]=i.useState(0),[l,u]=i.useState(0),d=i.useCallback(()=>{let e=a?.current;if(!e)return{};let t=(0,eU.Z)(e).getComputedStyle(e),r=Math.floor(parseFloat(t.height))||0,n=Math.floor(parseFloat(t.width))||0;return s(n),u(r),{width:n,height:r}},[]);return i.useEffect(()=>{n.current.displayError=!0},[]),(0,eD.Z)(()=>{if(!r||!n.current.initialCompute||n.current.computeRun>20)return;let e=d();e.width!==o||e.height!==l?n.current.computeRun+=1:n.current.initialCompute&&(n.current.initialCompute=!1)},[o,l,d,r]),(0,eD.Z)(()=>{let r;if(void 0!==e&&void 0!==t)return()=>{};d();let n=a.current;if("undefined"==typeof ResizeObserver)return()=>{};let i=new ResizeObserver(()=>{r=requestAnimationFrame(()=>{d()})});return n&&i.observe(n),()=>{r&&cancelAnimationFrame(r),n&&i.unobserve(n)}},[d,t,e]),{containerRef:a,width:e??o,height:t??l}},eq=["width","height","resolveSizeBeforeRender","margin","children","series","colors","dataset","desc","disableAxisListener","highlightedItem","onHighlightChange","plugins","sx","title","viewBox","xAxis","yAxis","zAxis","skipAnimation"],eX=(e,t)=>{let{width:r,height:i,resolveSizeBeforeRender:a,margin:o,children:s,series:l,colors:u,dataset:d,desc:c,disableAxisListener:h,highlightedItem:p,onHighlightChange:f,plugins:m,sx:g,title:y,viewBox:v,xAxis:b,yAxis:Z,zAxis:w,skipAnimation:A}=e,k=(0,x.Z)(e,eq),{containerRef:I,width:C,height:M}=eW(r,i,a);return{hasIntrinsicSize:C&&M,chartContainerProps:{margin:o,children:s,series:l,colors:u,dataset:d,desc:c,disableAxisListener:h,highlightedItem:p,onHighlightChange:f,plugins:m,sx:g,title:y,viewBox:v,xAxis:b,yAxis:Z,zAxis:w,skipAnimation:A,width:C,height:M,ref:t},resizableChartContainerProps:(0,n.Z)({},k,{ownerState:{width:r,height:i},ref:I})}},eK=i.forwardRef(function(e,t){let{hasIntrinsicSize:r,chartContainerProps:i,resizableChartContainerProps:a}=eX(e,t);return(0,f.jsx)(eG,(0,n.Z)({},a,{children:r?(0,f.jsx)(eB,(0,n.Z)({},i)):null}))})},70337:function(e,t,r){r.d(t,{Ae:function(){return a},Vd:function(){return i},nk:function(){return n}});let n="DEFAULT_X_AXIS_KEY",i="DEFAULT_Y_AXIS_KEY",a={top:50,bottom:50,left:50,right:50}},49887:function(e,t,r){r.d(t,{s:function(){return n}});let n=r(2265).createContext({isInitialized:!1,data:{skipAnimation:void 0}})},66924:function(e,t,r){r.d(t,{r:function(){return a}});var n=r(2265),i=r(49887);function a(e){let{isInitialized:t,data:r}=n.useContext(i.s);if(!t)throw Error("MUI X: Could not find the animation ref context.\nIt looks like you rendered your component outside of a ChartsContainer parent component.");return e||r.skipAnimation}},20818:function(e,t,r){r.d(t,{S:function(){return n}});let n=r(2265).createContext({isInitialized:!1,data:{xAxis:{},yAxis:{},xAxisIds:[],yAxisIds:[]}})},94102:function(e,t,r){r.d(t,{r:function(){return a}});var n=r(2265),i=r(20818);let a=()=>{let{data:e}=n.useContext(i.S);return e}},96100:function(e,t,r){r.d(t,{SV:function(){return u},X_:function(){return c},XV:function(){return d}});var n=r(1119),i=r(2265),a=r(53025),o=r(70337),s=(e,t,r)=>{let a=(0,n.Z)({},o.Ae,r);return i.useMemo(()=>({left:a.left,top:a.top,right:a.right,bottom:a.bottom,width:Math.max(0,e-a.left-a.right),height:Math.max(0,t-a.top-a.bottom)}),[e,t,a.top,a.bottom,a.left,a.right])},l=r(57437);let u=i.createContext({top:0,left:0,bottom:0,right:0,height:300,width:400,chartId:"",isPointInside:()=>!1}),d=i.createContext({isInitialized:!1,data:{current:null}});function c(e){let{width:t,height:r,margin:o,svgRef:c,children:h}=e,p=s(t,r,o),f=(0,a.Z)(),m=i.useCallback((e,t)=>{let{x:r,y:n}=e;if(t?.targetElement&&t?.targetElement.closest("[data-drawing-container]"))return!0;let i=r>=p.left-1&&r<=p.left+p.width,a=n>=p.top-1&&n<=p.top+p.height;return t?.direction==="x"?i:t?.direction==="y"?a:i&&a},[p]),g=i.useMemo(()=>(0,n.Z)({chartId:f??""},p,{isPointInside:m}),[f,p,m]),x=i.useMemo(()=>({isInitialized:!0,data:c}),[c]);return(0,l.jsx)(d.Provider,{value:x,children:(0,l.jsx)(u.Provider,{value:g,children:h})})}},50661:function(e,t,r){r.d(t,{t:function(){return n}});let n=r(2265).createContext({isInitialized:!1,data:{highlightedItem:null,setHighlighted:()=>{},clearHighlighted:()=>{},isHighlighted:()=>!1,isFaded:()=>!1}})},42457:function(e,t,r){r.d(t,{j:function(){return a}});var n=r(2265),i=r(50661);function a(){let{isInitialized:e,data:t}=n.useContext(i.t);if(!e)throw Error("MUI X: Could not find the highlighted ref context.\nIt looks like you rendered your component outside of a ChartsContainer parent component.");return t}},79795:function(e,t,r){r.d(t,{o:function(){return l},s:function(){return o}});var n=r(1119),i=r(2265),a=r(57437);let o=i.createContext({item:null,axis:{x:null,y:null},useVoronoiInteraction:!1,dispatch:()=>null}),s=(e,t)=>{switch(t.type){case"enterItem":return(0,n.Z)({},e,{item:t.data});case"exitChart":if(null===e.item&&null===e.axis.x&&null===e.axis.y)return e;return(0,n.Z)({},e,{axis:{x:null,y:null},item:null});case"updateVoronoiUsage":return(0,n.Z)({},e,{useVoronoiInteraction:t.useVoronoiInteraction});case"leaveItem":if(null===e.item||Object.keys(t.data).some(r=>t.data[r]!==e.item[r]))return e;return(0,n.Z)({},e,{item:null});case"updateAxis":if(t.data.x===e.axis.x&&t.data.y===e.axis.y)return e;return(0,n.Z)({},e,{axis:t.data});default:return e}};function l(e){let{children:t}=e,[r,l]=i.useReducer(s,{item:null,axis:{x:null,y:null},useVoronoiInteraction:!1}),u=i.useMemo(()=>(0,n.Z)({},r,{dispatch:l}),[r]);return(0,a.jsx)(o.Provider,{value:u,children:t})}},77981:function(e,t,r){r.d(t,{Z:function(){return n}});let n=r(2265).createContext({isInitialized:!1,data:{colorProcessors:{},seriesFormatters:{},xExtremumGetters:{},yExtremumGetters:{},rotationExtremumGetters:{},radiusExtremumGetters:{}}})},70532:function(e,t,r){r.d(t,{a:function(){return n}});let n=r(2265).createContext({isInitialized:!1,data:{}})},57332:function(e,t,r){r.d(t,{W:function(){return s},n:function(){return l}});var n=r(1119),i=r(2265),a=r(98235),o=r(57437);let s=i.createContext({zAxis:{},zAxisIds:[]});function l(e){let{zAxis:t,dataset:r,children:l}=e,u=i.useMemo(()=>t?.map(e=>{let t=e.dataKey;if(void 0===t||void 0!==e.data)return e;if(void 0===r)throw Error("MUI X: z-axis uses `dataKey` but no `dataset` is provided.");return n.Z({},e,{data:r.map(e=>e[t])})}),[t,r]),d=i.useMemo(()=>{let e=u?.map((e,t)=>n.Z({id:`defaultized-z-axis-${t}`},e))??[],t={};return e.forEach(e=>{t[e.id]=(0,n.Z)({},e,{colorScale:e.colorMap&&("ordinal"===e.colorMap.type&&e.data?(0,a.Kc)((0,n.Z)({values:e.data},e.colorMap)):(0,a.pI)("continuous"===e.colorMap.type?(0,n.Z)({min:e.min,max:e.max},e.colorMap):e.colorMap))})}),{zAxis:t,zAxisIds:e.map(e=>{let{id:t}=e;return t})}},[u]);return(0,o.jsx)(s.Provider,{value:d,children:l})}},87811:function(e,t,r){r.d(t,{z:function(){return a}});var n=r(2265),i=r(96100);function a(){let{left:e,top:t,width:r,height:a,bottom:o,right:s,isPointInside:l}=n.useContext(i.SV);return n.useMemo(()=>({left:e,top:t,width:r,height:a,bottom:o,right:s,isPointInside:l}),[a,e,t,r,o,s,l])}},72636:function(e,t,r){r.d(t,{J:function(){return o}});var n=r(2265),i=r(79795),a=r(42457);let o=e=>{let{dispatch:t}=n.useContext(i.s),{setHighlighted:r,clearHighlighted:o}=(0,a.j)();return e?()=>({}):e=>({onPointerEnter:()=>{t({type:"enterItem",data:e}),r({seriesId:e.seriesId,dataIndex:e.dataIndex})},onPointerLeave:r=>{r.currentTarget.hasPointerCapture(r.pointerId)&&r.currentTarget.releasePointerCapture(r.pointerId),t({type:"leaveItem",data:e}),o()},onPointerDown:e=>{e.currentTarget.hasPointerCapture(e.pointerId)&&e.currentTarget.releasePointerCapture(e.pointerId)}})}},78405:function(e,t,r){r.d(t,{aH:function(){return o},qZ:function(){return s},us:function(){return a}});var n=r(2265),i=r(70532);function a(){let{isInitialized:e,data:t}=n.useContext(i.a);if(!e)throw Error("MUI X: Could not find the series ref context.\nIt looks like you rendered your component outside of a ChartsContainer parent component.");return t}function o(){let e=a();return n.useMemo(()=>e.pie,[e.pie])}function s(){let e=a();return n.useMemo(()=>e.bar,[e.bar])}},80414:function(e,t,r){r.d(t,{S:function(){return a}});var n=r(2265),i=r(96100);function a(){let{isInitialized:e,data:t}=n.useContext(i.XV);if(!e)throw Error("MUI X: Could not find the svg ref context.\nIt looks like you rendered your component outside of a ChartsContainer parent component.");return t}},28576:function(e,t,r){r.d(t,{f:function(){return o},g:function(){return l}});var n=r(2265),i=r(34881),a=r(57527);function o(e){let{tickMaxStep:t,tickMinStep:r,tickNumber:n,range:i,domain:a}=e;return Math.min(void 0===r?999:Math.floor(Math.abs(a[1]-a[0])/r),Math.max(void 0===t?2:Math.ceil(Math.abs(a[1]-a[0])/t),n??Math.floor(Math.abs(i[1]-i[0])/50)))}let s={start:0,extremities:0,end:1,middle:.5};function l(e){let{scale:t,tickNumber:r,valueFormatter:o,tickInterval:l,tickPlacement:u="extremities",tickLabelPlacement:d="middle"}=e;return n.useMemo(()=>{if((0,i.o)(t)){let e=t.domain();return t.bandwidth()>0?[...("function"==typeof l&&e.filter(l)||"object"==typeof l&&l||e).map(e=>({value:e,formattedValue:o?.(e,{location:"tick"})??`${e}`,offset:t(e)-(t.step()-t.bandwidth())/2+s[u]*t.step(),labelOffset:"tick"===d?0:t.step()*(s[d]-s[u])})),..."extremities"===u?[{formattedValue:void 0,offset:t.range()[1],labelOffset:0}]:[]]:("function"==typeof l&&e.filter(l)||"object"==typeof l&&l||e).map(e=>({value:e,formattedValue:o?.(e,{location:"tick"})??`${e}`,offset:t(e),labelOffset:0}))}return t.domain().some(a.A)?[]:("object"==typeof l?l:t.ticks(r)).map(e=>({value:e,formattedValue:o?.(e,{location:"tick"})??t.tickFormat(r)(e),offset:t(e),labelOffset:0}))},[t,l,r,o,u,d])}},98235:function(e,t,r){r.d(t,{Kc:function(){return o},pI:function(){return s}});var n=r(16264),i=r(12214),a=r(36967);function o(e){return e.values?(0,a.Z)(e.values,e.colors).unknown(e.unknownColor??null):(0,a.Z)(e.colors.map((e,t)=>t),e.colors).unknown(e.unknownColor??null)}function s(e){return"ordinal"===e.type?o(e):"piecewise"===e.type?(0,n.Z)(e.thresholds,e.colors):(0,i.ZP)([e.min??0,e.max??100],e.color)}},87463:function(e,t,r){r.d(t,{i:function(){return n}});function n(e,t){return"function"==typeof e?e(t):e}},62020:function(e,t,r){let n;r.d(t,{i:function(){return p}});var i=r(1119);let a={widthCache:{},cacheCount:0},o={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s=["minWidth","maxWidth","width","minHeight","maxHeight","height","top","left","fontSize","padding","margin","paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom"],l="mui_measurement_span";function u(e,t){return s.indexOf(e)>=0&&t===+t?`${t}px`:t}function d(e){return e.split("").reduce((e,t)=>t===t.toUpperCase()?[...e,"-",t.toLowerCase()]:[...e,t],[]).join("")}let c=e=>Object.keys(e).sort().reduce((t,r)=>`${t}${d(r)}:${u(r,e[r])};`,""),h=(e,t={})=>{if(null==e||"undefined"==typeof window)return{width:0,height:0};let r=`${e}`,s=c(t),h=`${r}-${s}`;if(a.widthCache[h])return a.widthCache[h];try{let e=document.getElementById(l);null===e&&((e=document.createElement("span")).setAttribute("id",l),e.setAttribute("aria-hidden","true"),document.body.appendChild(e));let s=(0,i.Z)({},o,t);Object.keys(s).map(t=>(e.style[d(t)]=u(t,s[t]),t)),e.textContent=r;let c=e.getBoundingClientRect(),p={width:c.width,height:c.height};return a.widthCache[h]=p,a.cacheCount+1>2e3?(a.cacheCount=0,a.widthCache={}):a.cacheCount+=1,n&&clearTimeout(n),n=setTimeout(()=>{e.textContent=""},0),p}catch{return{width:0,height:0}}};function p({style:e,needsComputation:t,text:r}){return r.split("\n").map(r=>(0,i.Z)({text:r},t?h(r,e):{width:0,height:0}))}},34881:function(e,t,r){r.d(t,{o:function(){return n}});function n(e){return void 0!==e.bandwidth}},57527:function(e,t,r){r.d(t,{A:function(){return n}});function n(e){return"number"==typeof e&&!Number.isFinite(e)}},35836:function(e,t,r){function n(e){return"band"===e.scaleType}function i(e){return"point"===e.scaleType}r.d(t,{Q:function(){return n},T:function(){return i}})},91434:function(e,t,r){r.d(t,{OH:function(){return M},q:function(){return rl},to:function(){return t3},LI:function(){return eV},Yz:function(){return tQ}});var n,i,a,o,s=A(),l=e=>v(e,s),u=A();l.write=e=>v(e,u);var d=A();l.onStart=e=>v(e,d);var c=A();l.onFrame=e=>v(e,c);var h=A();l.onFinish=e=>v(e,h);var p=[];l.setTimeout=(e,t)=>{let r=l.now()+t,n=()=>{let e=p.findIndex(e=>e.cancel==n);~e&&p.splice(e,1),x-=~e?1:0},i={time:r,handler:e,cancel:n};return p.splice(f(r),0,i),x+=1,b(),i};var f=e=>~(~p.findIndex(t=>t.time>e)||~p.length);l.cancel=e=>{d.delete(e),c.delete(e),h.delete(e),s.delete(e),u.delete(e)},l.sync=e=>{y=!0,l.batchedUpdates(e),y=!1},l.throttle=e=>{let t;function r(){try{e(...t)}finally{t=null}}function n(...e){t=e,l.onStart(r)}return n.handler=e,n.cancel=()=>{d.delete(r),t=null},n};var m="undefined"!=typeof window?window.requestAnimationFrame:()=>{};l.use=e=>m=e,l.now="undefined"!=typeof performance?()=>performance.now():Date.now,l.batchedUpdates=e=>e(),l.catch=console.error,l.frameLoop="always",l.advance=()=>{"demand"!==l.frameLoop?console.warn("Cannot call the manual advancement of rafz whilst frameLoop is not set as demand"):w()};var g=-1,x=0,y=!1;function v(e,t){y?(t.delete(e),e(0)):(t.add(e),b())}function b(){g<0&&(g=0,"demand"!==l.frameLoop&&m(Z))}function Z(){~g&&(m(Z),l.batchedUpdates(w))}function w(){let e=g,t=f(g=l.now());if(t&&(k(p.splice(0,t),e=>e.handler()),x-=t),!x){g=-1;return}d.flush(),s.flush(e?Math.min(64,g-e):16.667),c.flush(),u.flush(),h.flush()}function A(){let e=new Set,t=e;return{add(r){x+=t!=e||e.has(r)?0:1,e.add(r)},delete:r=>(x-=t==e&&e.has(r)?1:0,e.delete(r)),flush(r){t.size&&(e=new Set,x-=t.size,k(t,t=>t(r)&&e.add(t)),x+=e.size,t=e)}}}function k(e,t){e.forEach(e=>{try{t(e)}catch(e){l.catch(e)}})}var I=r(2265),C=Object.defineProperty,M={};function P(){}((e,t)=>{for(var r in t)C(e,r,{get:t[r],enumerable:!0})})(M,{assign:()=>V,colors:()=>O,createStringInterpolator:()=>i,skipAnimation:()=>N,to:()=>a,willAdvance:()=>F});var j=(e,t,r)=>Object.defineProperty(e,t,{value:r,writable:!0,configurable:!0}),R={arr:Array.isArray,obj:e=>!!e&&"Object"===e.constructor.name,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,und:e=>void 0===e};function S(e,t){if(R.arr(e)){if(!R.arr(t)||e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}return e===t}var _=(e,t)=>e.forEach(t);function $(e,t,r){if(R.arr(e)){for(let n=0;n<e.length;n++)t.call(r,e[n],`${n}`);return}for(let n in e)e.hasOwnProperty(n)&&t.call(r,e[n],n)}var E=e=>R.und(e)?[]:R.arr(e)?e:[e];function L(e,t){if(e.size){let r=Array.from(e);e.clear(),_(r,t)}}var z=(e,...t)=>L(e,e=>e(...t)),T=()=>"undefined"==typeof window||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent),O=null,N=!1,F=P,V=e=>{e.to&&(a=e.to),e.now&&(l.now=e.now),void 0!==e.colors&&(O=e.colors),null!=e.skipAnimation&&(N=e.skipAnimation),e.createStringInterpolator&&(i=e.createStringInterpolator),e.requestAnimationFrame&&l.use(e.requestAnimationFrame),e.batchedUpdates&&(l.batchedUpdates=e.batchedUpdates),e.willAdvance&&(F=e.willAdvance),e.frameLoop&&(l.frameLoop=e.frameLoop)},H=new Set,B=[],G=[],D=0,U={get idle(){return!H.size&&!B.length},start(e){D>e.priority?(H.add(e),l.onStart(W)):(q(e),l(K))},advance:K,sort(e){if(D)l.onFrame(()=>U.sort(e));else{let t=B.indexOf(e);~t&&(B.splice(t,1),X(e))}},clear(){B=[],H.clear()}};function W(){H.forEach(q),H.clear(),l(K)}function q(e){B.includes(e)||X(e)}function X(e){B.splice(function(e,t){let r=e.findIndex(t);return r<0?e.length:r}(B,t=>t.priority>e.priority),0,e)}function K(e){let t=G;for(let r=0;r<B.length;r++){let n=B[r];D=n.priority,n.idle||(F(n),n.advance(e),n.idle||t.push(n))}return D=0,(G=B).length=0,(B=t).length>0}var Y="[-+]?\\d*\\.?\\d+",Q=Y+"%";function J(...e){return"\\(\\s*("+e.join(")\\s*,\\s*(")+")\\s*\\)"}var ee=RegExp("rgb"+J(Y,Y,Y)),et=RegExp("rgba"+J(Y,Y,Y,Y)),er=RegExp("hsl"+J(Y,Q,Q)),en=RegExp("hsla"+J(Y,Q,Q,Y)),ei=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,ea=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,eo=/^#([0-9a-fA-F]{6})$/,es=/^#([0-9a-fA-F]{8})$/;function el(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function eu(e,t,r){let n=r<.5?r*(1+t):r+t-r*t,i=2*r-n;return Math.round(255*el(i,n,e+1/3))<<24|Math.round(255*el(i,n,e))<<16|Math.round(255*el(i,n,e-1/3))<<8}function ed(e){let t=parseInt(e,10);return t<0?0:t>255?255:t}function ec(e){return(parseFloat(e)%360+360)%360/360}function eh(e){let t=parseFloat(e);return t<0?0:t>1?255:Math.round(255*t)}function ep(e){let t=parseFloat(e);return t<0?0:t>100?1:t/100}function ef(e){let t;let r="number"==typeof e?e>>>0===e&&e>=0&&e<=4294967295?e:null:(t=eo.exec(e))?parseInt(t[1]+"ff",16)>>>0:O&&void 0!==O[e]?O[e]:(t=ee.exec(e))?(ed(t[1])<<24|ed(t[2])<<16|ed(t[3])<<8|255)>>>0:(t=et.exec(e))?(ed(t[1])<<24|ed(t[2])<<16|ed(t[3])<<8|eh(t[4]))>>>0:(t=ei.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+"ff",16)>>>0:(t=es.exec(e))?parseInt(t[1],16)>>>0:(t=ea.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+t[4]+t[4],16)>>>0:(t=er.exec(e))?(255|eu(ec(t[1]),ep(t[2]),ep(t[3])))>>>0:(t=en.exec(e))?(eu(ec(t[1]),ep(t[2]),ep(t[3]))|eh(t[4]))>>>0:null;if(null===r)return e;let n=(4278190080&(r=r||0))>>>24,i=(16711680&r)>>>16,a=(65280&r)>>>8,o=(255&r)/255;return`rgba(${n}, ${i}, ${a}, ${o})`}var em=(e,t,r)=>{if(R.fun(e))return e;if(R.arr(e))return em({range:e,output:t,extrapolate:r});if(R.str(e.output[0]))return i(e);let n=e.output,a=e.range||[0,1],o=e.extrapolateLeft||e.extrapolate||"extend",s=e.extrapolateRight||e.extrapolate||"extend",l=e.easing||(e=>e);return t=>{let r=function(e,t){for(var r=1;r<t.length-1&&!(t[r]>=e);++r);return r-1}(t,a);return function(e,t,r,n,i,a,o,s,l){let u=l?l(e):e;if(u<t){if("identity"===o)return u;"clamp"===o&&(u=t)}if(u>r){if("identity"===s)return u;"clamp"===s&&(u=r)}return n===i?n:t===r?e<=t?n:i:(t===-1/0?u=-u:r===1/0?u-=t:u=(u-t)/(r-t),u=a(u),n===-1/0?u=-u:i===1/0?u+=n:u=u*(i-n)+n,u)}(t,a[r],a[r+1],n[r],n[r+1],l,o,s,e.map)}},eg=Symbol.for("FluidValue.get"),ex=Symbol.for("FluidValue.observers"),ey=e=>!!(e&&e[eg]),ev=e=>e&&e[eg]?e[eg]():e,eb=e=>e[ex]||null;function eZ(e,t){let r=e[ex];r&&r.forEach(e=>{e.eventObserved?e.eventObserved(t):e(t)})}var ew=class{constructor(e){if(!e&&!(e=this.get))throw Error("Unknown getter");eA(this,e)}},eA=(e,t)=>eC(e,eg,t);function ek(e,t){if(e[eg]){let r=e[ex];r||eC(e,ex,r=new Set),!r.has(t)&&(r.add(t),e.observerAdded&&e.observerAdded(r.size,t))}return t}function eI(e,t){let r=e[ex];if(r&&r.has(t)){let n=r.size-1;n?r.delete(t):e[ex]=null,e.observerRemoved&&e.observerRemoved(n,t)}}var eC=(e,t,r)=>Object.defineProperty(e,t,{value:r,writable:!0,configurable:!0}),eM=/[+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,eP=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d\.]+%?\))/gi,ej=RegExp(`(${eM.source})(%|[a-z]+)`,"i"),eR=/rgba\(([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+)\)/gi,eS=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/,e_=e=>{let[t,r]=e$(e);if(!t||T())return e;let n=window.getComputedStyle(document.documentElement).getPropertyValue(t);if(n)return n.trim();if(r&&r.startsWith("--")){let e=window.getComputedStyle(document.documentElement).getPropertyValue(r);if(e)return e}else if(r&&eS.test(r))return e_(r);else if(r)return r;return e},e$=e=>{let t=eS.exec(e);if(!t)return[,];let[,r,n]=t;return[r,n]},eE=(e,t,r,n,i)=>`rgba(${Math.round(t)}, ${Math.round(r)}, ${Math.round(n)}, ${i})`,eL=e=>{o||(o=O?RegExp(`(${Object.keys(O).join("|")})(?!\\w)`,"g"):/^\b$/);let t=e.output.map(e=>ev(e).replace(eS,e_).replace(eP,ef).replace(o,ef)),r=t.map(e=>e.match(eM).map(Number)),n=r[0].map((e,t)=>r.map(e=>{if(!(t in e))throw Error('The arity of each "output" value must be equal');return e[t]})).map(t=>em({...e,output:t}));return e=>{let r=!ej.test(t[0])&&t.find(e=>ej.test(e))?.replace(eM,""),i=0;return t[0].replace(eM,()=>`${n[i++](e)}${r||""}`).replace(eR,eE)}},ez="react-spring: ",eT=e=>{let t=!1;if("function"!=typeof e)throw TypeError(`${ez}once requires a function parameter`);return(...r)=>{t||(e(...r),t=!0)}},eO=eT(console.warn),eN=eT(console.warn);function eF(e){return R.str(e)&&("#"==e[0]||/\d/.test(e)||!T()&&eS.test(e)||e in(O||{}))}var eV=T()?I.useEffect:I.useLayoutEffect,eH=()=>{let e=(0,I.useRef)(!1);return eV(()=>(e.current=!0,()=>{e.current=!1}),[]),e};function eB(){let e=(0,I.useState)()[1],t=eH();return()=>{t.current&&e(Math.random())}}var eG=e=>(0,I.useEffect)(e,eD),eD=[],eU=Symbol.for("Animated:node"),eW=e=>!!e&&e[eU]===e,eq=e=>e&&e[eU],eX=(e,t)=>j(e,eU,t),eK=e=>e&&e[eU]&&e[eU].getPayload(),eY=class{constructor(){eX(this,this)}getPayload(){return this.payload||[]}},eQ=class extends eY{constructor(e){super(),this._value=e,this.done=!0,this.durationProgress=0,R.num(this._value)&&(this.lastPosition=this._value)}static create(e){return new eQ(e)}getPayload(){return[this]}getValue(){return this._value}setValue(e,t){return R.num(e)&&(this.lastPosition=e,t&&(e=Math.round(e/t)*t,this.done&&(this.lastPosition=e))),this._value!==e&&(this._value=e,!0)}reset(){let{done:e}=this;this.done=!1,R.num(this._value)&&(this.elapsedTime=0,this.durationProgress=0,this.lastPosition=this._value,e&&(this.lastVelocity=null),this.v0=null)}},eJ=class extends eQ{constructor(e){super(0),this._string=null,this._toString=em({output:[e,e]})}static create(e){return new eJ(e)}getValue(){let e=this._string;return null==e?this._string=this._toString(this._value):e}setValue(e){if(R.str(e)){if(e==this._string)return!1;this._string=e,this._value=1}else{if(!super.setValue(e))return!1;this._string=null}return!0}reset(e){e&&(this._toString=em({output:[this.getValue(),e]})),this._value=0,super.reset()}},e0={dependencies:null},e1=class extends eY{constructor(e){super(),this.source=e,this.setValue(e)}getValue(e){let t={};return $(this.source,(r,n)=>{eW(r)?t[n]=r.getValue(e):ey(r)?t[n]=ev(r):e||(t[n]=r)}),t}setValue(e){this.source=e,this.payload=this._makePayload(e)}reset(){this.payload&&_(this.payload,e=>e.reset())}_makePayload(e){if(e){let t=new Set;return $(e,this._addToPayload,t),Array.from(t)}}_addToPayload(e){e0.dependencies&&ey(e)&&e0.dependencies.add(e);let t=eK(e);t&&_(t,e=>this.add(e))}},e2=class extends e1{constructor(e){super(e)}static create(e){return new e2(e)}getValue(){return this.source.map(e=>e.getValue())}setValue(e){let t=this.getPayload();return e.length==t.length?t.map((t,r)=>t.setValue(e[r])).some(Boolean):(super.setValue(e.map(e5)),!0)}};function e5(e){return(eF(e)?eJ:eQ).create(e)}function e3(e){let t=eq(e);return t?t.constructor:R.arr(e)?e2:eF(e)?eJ:eQ}var e4=(e,t)=>{let r=!R.fun(e)||e.prototype&&e.prototype.isReactComponent;return(0,I.forwardRef)((n,i)=>{let a=(0,I.useRef)(null),o=r&&(0,I.useCallback)(e=>{a.current=(i&&(R.fun(i)?i(e):i.current=e),e)},[i]),[s,u]=function(e,t){let r=new Set;return e0.dependencies=r,e.style&&(e={...e,style:t.createAnimatedStyle(e.style)}),e=new e1(e),e0.dependencies=null,[e,r]}(n,t),d=eB(),c=()=>{let e=a.current;(!r||e)&&!1===(!!e&&t.applyAnimatedValues(e,s.getValue(!0)))&&d()},h=new e7(c,u),p=(0,I.useRef)();eV(()=>(p.current=h,_(u,e=>ek(e,h)),()=>{p.current&&(_(p.current.deps,e=>eI(e,p.current)),l.cancel(p.current.update))})),(0,I.useEffect)(c,[]),eG(()=>()=>{let e=p.current;_(e.deps,t=>eI(t,e))});let f=t.getComponentProps(s.getValue());return I.createElement(e,{...f,ref:o})})},e7=class{constructor(e,t){this.update=e,this.deps=t}eventObserved(e){"change"==e.type&&l.write(this.update)}},e9=Symbol.for("AnimatedComponent"),e6=e=>R.str(e)?e:e&&R.str(e.displayName)?e.displayName:R.fun(e)&&e.name||null;function e8(e,...t){return R.fun(e)?e(...t):e}var te=(e,t)=>!0===e||!!(t&&e&&(R.fun(e)?e(t):E(e).includes(t))),tt=(e,t)=>R.obj(e)?t&&e[t]:e,tr=(e,t)=>!0===e.default?e[t]:e.default?e.default[t]:void 0,tn=e=>e,ti=(e,t=tn)=>{let r=ta;e.default&&!0!==e.default&&(r=Object.keys(e=e.default));let n={};for(let i of r){let r=t(e[i],i);R.und(r)||(n[i]=r)}return n},ta=["config","onProps","onStart","onChange","onPause","onResume","onRest"],to={config:1,from:1,to:1,ref:1,loop:1,reset:1,pause:1,cancel:1,reverse:1,immediate:1,default:1,delay:1,onProps:1,onStart:1,onChange:1,onPause:1,onResume:1,onRest:1,onResolve:1,items:1,trail:1,sort:1,expires:1,initial:1,enter:1,update:1,leave:1,children:1,onDestroyed:1,keys:1,callId:1,parentId:1};function ts(e){let t=function(e){let t={},r=0;if($(e,(e,n)=>{!to[n]&&(t[n]=e,r++)}),r)return t}(e);if(t){let r={to:t};return $(e,(e,n)=>n in t||(r[n]=e)),r}return{...e}}function tl(e){return e=ev(e),R.arr(e)?e.map(tl):eF(e)?M.createStringInterpolator({range:[0,1],output:[e,e]})(1):e}function tu(e){return R.fun(e)||R.arr(e)&&R.obj(e[0])}function td(e,t){e.ref?.delete(e),t?.delete(e)}var tc={tension:170,friction:26,mass:1,damping:1,easing:e=>e,clamp:!1},th=class{constructor(){this.velocity=0,Object.assign(this,tc)}};function tp(e,t){if(R.und(t.decay)){let r=!R.und(t.tension)||!R.und(t.friction);!r&&R.und(t.frequency)&&R.und(t.damping)&&R.und(t.mass)||(e.duration=void 0,e.decay=void 0),r&&(e.frequency=void 0)}else e.duration=void 0}var tf=[],tm=class{constructor(){this.changed=!1,this.values=tf,this.toValues=null,this.fromValues=tf,this.config=new th,this.immediate=!1}};function tg(e,{key:t,props:r,defaultProps:n,state:i,actions:a}){return new Promise((o,s)=>{let u,d;let c=te(r.cancel??n?.cancel,t);if(c)f();else{R.und(r.pause)||(i.paused=te(r.pause,t));let e=n?.pause;!0!==e&&(e=i.paused||te(e,t)),u=e8(r.delay||0,t),e?(i.resumeQueue.add(p),a.pause()):(a.resume(),p())}function h(){i.resumeQueue.add(p),i.timeouts.delete(d),d.cancel(),u=d.time-l.now()}function p(){u>0&&!M.skipAnimation?(i.delayed=!0,d=l.setTimeout(f,u),i.pauseQueue.add(h),i.timeouts.add(d)):f()}function f(){i.delayed&&(i.delayed=!1),i.pauseQueue.delete(h),i.timeouts.delete(d),e<=(i.cancelId||0)&&(c=!0);try{a.start({...r,callId:e,cancel:c},o)}catch(e){s(e)}}})}var tx=(e,t)=>1==t.length?t[0]:t.some(e=>e.cancelled)?tb(e.get()):t.every(e=>e.noop)?ty(e.get()):tv(e.get(),t.every(e=>e.finished)),ty=e=>({value:e,noop:!0,finished:!0,cancelled:!1}),tv=(e,t,r=!1)=>({value:e,finished:t,cancelled:r}),tb=e=>({value:e,cancelled:!0,finished:!1});function tZ(e,t,r,n){let{callId:i,parentId:a,onRest:o}=t,{asyncTo:s,promise:u}=r;return a||e!==s||t.reset?r.promise=(async()=>{let d,c,h;r.asyncId=i,r.asyncTo=e;let p=ti(t,(e,t)=>"onRest"===t?void 0:e),f=new Promise((e,t)=>(d=e,c=t)),m=e=>{let t=i<=(r.cancelId||0)&&tb(n)||i!==r.asyncId&&tv(n,!1);if(t)throw e.result=t,c(e),e},g=(e,t)=>{let a=new tA,o=new tk;return(async()=>{if(M.skipAnimation)throw tw(r),o.result=tv(n,!1),c(o),o;m(a);let s=R.obj(e)?{...e}:{...t,to:e};s.parentId=i,$(p,(e,t)=>{R.und(s[t])&&(s[t]=e)});let l=await n.start(s);return m(a),r.paused&&await new Promise(e=>{r.resumeQueue.add(e)}),l})()};if(M.skipAnimation)return tw(r),tv(n,!1);try{let t;t=R.arr(e)?(async e=>{for(let t of e)await g(t)})(e):Promise.resolve(e(g,n.stop.bind(n))),await Promise.all([t.then(d),f]),h=tv(n.get(),!0,!1)}catch(e){if(e instanceof tA)h=e.result;else if(e instanceof tk)h=e.result;else throw e}finally{i==r.asyncId&&(r.asyncId=a,r.asyncTo=a?s:void 0,r.promise=a?u:void 0)}return R.fun(o)&&l.batchedUpdates(()=>{o(h,n,n.item)}),h})():u}function tw(e,t){L(e.timeouts,e=>e.cancel()),e.pauseQueue.clear(),e.resumeQueue.clear(),e.asyncId=e.asyncTo=e.promise=void 0,t&&(e.cancelId=t)}var tA=class extends Error{constructor(){super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.")}},tk=class extends Error{constructor(){super("SkipAnimationSignal")}},tI=e=>e instanceof tM,tC=1,tM=class extends ew{constructor(){super(...arguments),this.id=tC++,this._priority=0}get priority(){return this._priority}set priority(e){this._priority!=e&&(this._priority=e,this._onPriorityChange(e))}get(){let e=eq(this);return e&&e.getValue()}to(...e){return M.to(this,e)}interpolate(...e){return eO(`${ez}The "interpolate" function is deprecated in v9 (use "to" instead)`),M.to(this,e)}toJSON(){return this.get()}observerAdded(e){1==e&&this._attach()}observerRemoved(e){0==e&&this._detach()}_attach(){}_detach(){}_onChange(e,t=!1){eZ(this,{type:"change",parent:this,value:e,idle:t})}_onPriorityChange(e){this.idle||U.sort(this),eZ(this,{type:"priority",parent:this,priority:e})}},tP=Symbol.for("SpringPhase"),tj=e=>(1&e[tP])>0,tR=e=>(2&e[tP])>0,tS=e=>(4&e[tP])>0,t_=(e,t)=>t?e[tP]|=3:e[tP]&=-3,t$=(e,t)=>t?e[tP]|=4:e[tP]&=-5,tE=class extends tM{constructor(e,t){if(super(),this.animation=new tm,this.defaultProps={},this._state={paused:!1,delayed:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._pendingCalls=new Set,this._lastCallId=0,this._lastToId=0,this._memoizedDuration=0,!R.und(e)||!R.und(t)){let r=R.obj(e)?{...e}:{...t,from:e};R.und(r.default)&&(r.default=!0),this.start(r)}}get idle(){return!(tR(this)||this._state.asyncTo)||tS(this)}get goal(){return ev(this.animation.to)}get velocity(){let e=eq(this);return e instanceof eQ?e.lastVelocity||0:e.getPayload().map(e=>e.lastVelocity||0)}get hasAnimated(){return tj(this)}get isAnimating(){return tR(this)}get isPaused(){return tS(this)}get isDelayed(){return this._state.delayed}advance(e){let t=!0,r=!1,n=this.animation,{toValues:i}=n,{config:a}=n,o=eK(n.to);!o&&ey(n.to)&&(i=E(ev(n.to))),n.values.forEach((s,l)=>{if(s.done)return;let u=s.constructor==eJ?1:o?o[l].lastPosition:i[l],d=n.immediate,c=u;if(!d){let t;if(c=s.lastPosition,a.tension<=0){s.done=!0;return}let r=s.elapsedTime+=e,i=n.fromValues[l],o=null!=s.v0?s.v0:s.v0=R.arr(a.velocity)?a.velocity[l]:a.velocity,h=a.precision||(i==u?.005:Math.min(1,.001*Math.abs(u-i)));if(R.und(a.duration)){if(a.decay){let e=!0===a.decay?.998:a.decay,n=Math.exp(-(1-e)*r);c=i+o/(1-e)*(1-n),d=Math.abs(s.lastPosition-c)<=h,t=o*n}else{t=null==s.lastVelocity?o:s.lastVelocity;let r=a.restVelocity||h/10,n=a.clamp?0:a.bounce,l=!R.und(n),p=i==u?s.v0>0:i<u,f=Math.ceil(e/1);for(let e=0;e<f&&!(!(Math.abs(t)>r)&&(d=Math.abs(u-c)<=h));++e){l&&(c==u||c>u==p)&&(t=-t*n,c=u);let e=(-(1e-6*a.tension)*(c-u)+-(.001*a.friction)*t)/a.mass;t+=1*e,c+=1*t}}}else{let n=1;a.duration>0&&(this._memoizedDuration!==a.duration&&(this._memoizedDuration=a.duration,s.durationProgress>0&&(s.elapsedTime=a.duration*s.durationProgress,r=s.elapsedTime+=e)),n=(n=(a.progress||0)+r/this._memoizedDuration)>1?1:n<0?0:n,s.durationProgress=n),t=((c=i+a.easing(n)*(u-i))-s.lastPosition)/e,d=1==n}s.lastVelocity=t,Number.isNaN(c)&&(console.warn("Got NaN while animating:",this),d=!0)}o&&!o[l].done&&(d=!1),d?s.done=!0:t=!1,s.setValue(c,a.round)&&(r=!0)});let s=eq(this),l=s.getValue();if(t){let e=ev(n.to);(l!==e||r)&&!a.decay?(s.setValue(e),this._onChange(e)):r&&a.decay&&this._onChange(l),this._stop()}else r&&this._onChange(l)}set(e){return l.batchedUpdates(()=>{this._stop(),this._focus(e),this._set(e)}),this}pause(){this._update({pause:!0})}resume(){this._update({pause:!1})}finish(){if(tR(this)){let{to:e,config:t}=this.animation;l.batchedUpdates(()=>{this._onStart(),t.decay||this._set(e,!1),this._stop()})}return this}update(e){return(this.queue||(this.queue=[])).push(e),this}start(e,t){let r;return R.und(e)?(r=this.queue||[],this.queue=[]):r=[R.obj(e)?e:{...t,to:e}],Promise.all(r.map(e=>this._update(e))).then(e=>tx(this,e))}stop(e){let{to:t}=this.animation;return this._focus(this.get()),tw(this._state,e&&this._lastCallId),l.batchedUpdates(()=>this._stop(t,e)),this}reset(){this._update({reset:!0})}eventObserved(e){"change"==e.type?this._start():"priority"==e.type&&(this.priority=e.priority+1)}_prepareNode(e){let t=this.key||"",{to:r,from:n}=e;(null==(r=R.obj(r)?r[t]:r)||tu(r))&&(r=void 0),null==(n=R.obj(n)?n[t]:n)&&(n=void 0);let i={to:r,from:n};return tj(this)||(e.reverse&&([r,n]=[n,r]),n=ev(n),R.und(n)?eq(this)||this._set(r):this._set(n)),i}_update({...e},t){let{key:r,defaultProps:n}=this;e.default&&Object.assign(n,ti(e,(e,t)=>/^on/.test(t)?tt(e,r):e)),tF(this,e,"onProps"),tV(this,"onProps",e,this);let i=this._prepareNode(e);if(Object.isFrozen(this))throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");let a=this._state;return tg(++this._lastCallId,{key:r,props:e,defaultProps:n,state:a,actions:{pause:()=>{tS(this)||(t$(this,!0),z(a.pauseQueue),tV(this,"onPause",tv(this,tL(this,this.animation.to)),this))},resume:()=>{tS(this)&&(t$(this,!1),tR(this)&&this._resume(),z(a.resumeQueue),tV(this,"onResume",tv(this,tL(this,this.animation.to)),this))},start:this._merge.bind(this,i)}}).then(r=>{if(e.loop&&r.finished&&!(t&&r.noop)){let t=tz(e);if(t)return this._update(t,!0)}return r})}_merge(e,t,r){if(t.cancel)return this.stop(!0),r(tb(this));let n=!R.und(e.to),i=!R.und(e.from);if(n||i){if(!(t.callId>this._lastToId))return r(tb(this));this._lastToId=t.callId}let{key:a,defaultProps:o,animation:s}=this,{to:u,from:d}=s,{to:c=u,from:h=d}=e;i&&!n&&(!t.default||R.und(c))&&(c=h),t.reverse&&([c,h]=[h,c]);let p=!S(h,d);p&&(s.from=h),h=ev(h);let f=!S(c,u);f&&this._focus(c);let m=tu(t.to),{config:g}=s,{decay:x,velocity:y}=g;(n||i)&&(g.velocity=0),t.config&&!m&&function(e,t,r){for(let n in r&&(tp(r={...r},t),t={...r,...t}),tp(e,t),Object.assign(e,t),tc)null==e[n]&&(e[n]=tc[n]);let{frequency:n,damping:i}=e,{mass:a}=e;R.und(n)||(n<.01&&(n=.01),i<0&&(i=0),e.tension=Math.pow(2*Math.PI/n,2)*a,e.friction=4*Math.PI*i*a/n)}(g,e8(t.config,a),t.config!==o.config?e8(o.config,a):void 0);let v=eq(this);if(!v||R.und(c))return r(tv(this,!0));let b=R.und(t.reset)?i&&!t.default:!R.und(h)&&te(t.reset,a),Z=b?h:this.get(),w=tl(c),A=R.num(w)||R.arr(w)||eF(w),k=!m&&(!A||te(o.immediate||t.immediate,a));if(f){let e=e3(c);if(e!==v.constructor){if(k)v=this._set(w);else throw Error(`Cannot animate between ${v.constructor.name} and ${e.name}, as the "to" prop suggests`)}}let I=v.constructor,C=ey(c),M=!1;if(!C){let e=b||!tj(this)&&p;(f||e)&&(C=!(M=S(tl(Z),w))),(S(s.immediate,k)||k)&&S(g.decay,x)&&S(g.velocity,y)||(C=!0)}if(M&&tR(this)&&(s.changed&&!b?C=!0:C||this._stop(u)),!m&&((C||ey(u))&&(s.values=v.getPayload(),s.toValues=ey(c)?null:I==eJ?[1]:E(w)),s.immediate==k||(s.immediate=k,k||b||this._set(u)),C)){let{onRest:e}=s;_(tN,e=>tF(this,t,e));let n=tv(this,tL(this,u));z(this._pendingCalls,n),this._pendingCalls.add(r),s.changed&&l.batchedUpdates(()=>{s.changed=!b,e?.(n,this),b?e8(o.onRest,n):s.onStart?.(n,this)})}b&&this._set(Z),m?r(tZ(t.to,t,this._state,this)):C?this._start():tR(this)&&!f?this._pendingCalls.add(r):r(ty(Z))}_focus(e){let t=this.animation;e!==t.to&&(eb(this)&&this._detach(),t.to=e,eb(this)&&this._attach())}_attach(){let e=0,{to:t}=this.animation;ey(t)&&(ek(t,this),tI(t)&&(e=t.priority+1)),this.priority=e}_detach(){let{to:e}=this.animation;ey(e)&&eI(e,this)}_set(e,t=!0){let r=ev(e);if(!R.und(r)){let e=eq(this);if(!e||!S(r,e.getValue())){let n=e3(r);e&&e.constructor==n?e.setValue(r):eX(this,n.create(r)),e&&l.batchedUpdates(()=>{this._onChange(r,t)})}}return eq(this)}_onStart(){let e=this.animation;e.changed||(e.changed=!0,tV(this,"onStart",tv(this,tL(this,e.to)),this))}_onChange(e,t){t||(this._onStart(),e8(this.animation.onChange,e,this)),e8(this.defaultProps.onChange,e,this),super._onChange(e,t)}_start(){let e=this.animation;eq(this).reset(ev(e.to)),e.immediate||(e.fromValues=e.values.map(e=>e.lastPosition)),tR(this)||(t_(this,!0),tS(this)||this._resume())}_resume(){M.skipAnimation?this.finish():U.start(this)}_stop(e,t){if(tR(this)){t_(this,!1);let r=this.animation;_(r.values,e=>{e.done=!0}),r.toValues&&(r.onChange=r.onPause=r.onResume=void 0),eZ(this,{type:"idle",parent:this});let n=t?tb(this.get()):tv(this.get(),tL(this,e??r.to));z(this._pendingCalls,n),r.changed&&(r.changed=!1,tV(this,"onRest",n,this))}}};function tL(e,t){let r=tl(t);return S(tl(e.get()),r)}function tz(e,t=e.loop,r=e.to){let n=e8(t);if(n){let i=!0!==n&&ts(n),a=(i||e).reverse,o=!i||i.reset;return tT({...e,loop:t,default:!1,pause:void 0,to:!a||tu(r)?r:void 0,from:o?e.from:void 0,reset:o,...i})}}function tT(e){let{to:t,from:r}=e=ts(e),n=new Set;return R.obj(t)&&tO(t,n),R.obj(r)&&tO(r,n),e.keys=n.size?Array.from(n):null,e}function tO(e,t){$(e,(e,r)=>null!=e&&t.add(r))}var tN=["onStart","onRest","onChange","onPause","onResume"];function tF(e,t,r){e.animation[r]=t[r]!==tr(t,r)?tt(t[r],e.key):void 0}function tV(e,t,...r){e.animation[t]?.(...r),e.defaultProps[t]?.(...r)}var tH=["onStart","onChange","onRest"],tB=1,tG=class{constructor(e,t){this.id=tB++,this.springs={},this.queue=[],this._lastAsyncId=0,this._active=new Set,this._changed=new Set,this._started=!1,this._state={paused:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._events={onStart:new Map,onChange:new Map,onRest:new Map},this._onFrame=this._onFrame.bind(this),t&&(this._flush=t),e&&this.start({default:!0,...e})}get idle(){return!this._state.asyncTo&&Object.values(this.springs).every(e=>e.idle&&!e.isDelayed&&!e.isPaused)}get item(){return this._item}set item(e){this._item=e}get(){let e={};return this.each((t,r)=>e[r]=t.get()),e}set(e){for(let t in e){let r=e[t];R.und(r)||this.springs[t].set(r)}}update(e){return e&&this.queue.push(tT(e)),this}start(e){var t;let{queue:r}=this;return(e?r=E(e).map(tT):this.queue=[],this._flush)?this._flush(this,r):(tq(this,r),t=this,Promise.all(r.map(e=>tD(t,e))).then(e=>tx(t,e)))}stop(e,t){if(!!e!==e&&(t=e),t){let r=this.springs;_(E(t),t=>r[t].stop(!!e))}else tw(this._state,this._lastAsyncId),this.each(t=>t.stop(!!e));return this}pause(e){if(R.und(e))this.start({pause:!0});else{let t=this.springs;_(E(e),e=>t[e].pause())}return this}resume(e){if(R.und(e))this.start({pause:!1});else{let t=this.springs;_(E(e),e=>t[e].resume())}return this}each(e){$(this.springs,e)}_onFrame(){let{onStart:e,onChange:t,onRest:r}=this._events,n=this._active.size>0,i=this._changed.size>0;(n&&!this._started||i&&!this._started)&&(this._started=!0,L(e,([e,t])=>{t.value=this.get(),e(t,this,this._item)}));let a=!n&&this._started,o=i||a&&r.size?this.get():null;i&&t.size&&L(t,([e,t])=>{t.value=o,e(t,this,this._item)}),a&&(this._started=!1,L(r,([e,t])=>{t.value=o,e(t,this,this._item)}))}eventObserved(e){if("change"==e.type)this._changed.add(e.parent),e.idle||this._active.add(e.parent);else{if("idle"!=e.type)return;this._active.delete(e.parent)}l.onFrame(this._onFrame)}};async function tD(e,t,r){let{keys:n,to:i,from:a,loop:o,onRest:s,onResolve:u}=t,d=R.obj(t.default)&&t.default;o&&(t.loop=!1),!1===i&&(t.to=null),!1===a&&(t.from=null);let c=R.arr(i)||R.fun(i)?i:void 0;c?(t.to=void 0,t.onRest=void 0,d&&(d.onRest=void 0)):_(tH,r=>{let n=t[r];if(R.fun(n)){let i=e._events[r];t[r]=({finished:e,cancelled:t})=>{let r=i.get(n);r?(e||(r.finished=!1),t&&(r.cancelled=!0)):i.set(n,{value:null,finished:e||!1,cancelled:t||!1})},d&&(d[r]=t[r])}});let h=e._state;!h.paused===t.pause?(h.paused=t.pause,z(t.pause?h.pauseQueue:h.resumeQueue)):h.paused&&(t.pause=!0);let p=(n||Object.keys(e.springs)).map(r=>e.springs[r].start(t)),f=!0===t.cancel||!0===tr(t,"cancel");(c||f&&h.asyncId)&&p.push(tg(++e._lastAsyncId,{props:t,state:h,actions:{pause:P,resume:P,start(t,r){f?(tw(h,e._lastAsyncId),r(tb(e))):(t.onRest=s,r(tZ(c,t,h,e)))}}})),h.paused&&await new Promise(e=>{h.resumeQueue.add(e)});let m=tx(e,await Promise.all(p));if(o&&m.finished&&!(r&&m.noop)){let r=tz(t,o,i);if(r)return tq(e,[r]),tD(e,r,!0)}return u&&l.batchedUpdates(()=>u(m,e,e.item)),m}function tU(e,t){let r=new tE;return r.key=e,t&&ek(r,t),r}function tW(e,t,r){t.keys&&_(t.keys,n=>{(e[n]||(e[n]=r(n)))._prepareNode(t)})}function tq(e,t){_(t,t=>{tW(e.springs,t,t=>tU(t,e))})}var tX=({children:e,...t})=>{let r=(0,I.useContext)(tK),n=t.pause||!!r.pause,i=t.immediate||!!r.immediate;t=function(e,t){let[r]=(0,I.useState)(()=>({inputs:t,result:e()})),n=(0,I.useRef)(),i=n.current,a=i;return a?t&&a.inputs&&function(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(t,a.inputs)||(a={inputs:t,result:e()}):a=r,(0,I.useEffect)(()=>{n.current=a,i==r&&(r.inputs=r.result=void 0)},[a]),a.result}(()=>({pause:n,immediate:i}),[n,i]);let{Provider:a}=tK;return I.createElement(a,{value:t},e)},tK=(n={},Object.assign(tX,I.createContext(n)),tX.Provider._context=tX,tX.Consumer._context=tX,tX);tX.Provider=tK.Provider,tX.Consumer=tK.Consumer;var tY=()=>{let e=[],t=function(t){eN(`${ez}Directly calling start instead of using the api object is deprecated in v9 (use ".start" instead), this will be removed in later 0.X.0 versions`);let n=[];return _(e,(e,i)=>{if(R.und(t))n.push(e.start());else{let a=r(t,e,i);a&&n.push(e.start(a))}}),n};t.current=e,t.add=function(t){e.includes(t)||e.push(t)},t.delete=function(t){let r=e.indexOf(t);~r&&e.splice(r,1)},t.pause=function(){return _(e,e=>e.pause(...arguments)),this},t.resume=function(){return _(e,e=>e.resume(...arguments)),this},t.set=function(t){_(e,(e,r)=>{let n=R.fun(t)?t(r,e):t;n&&e.set(n)})},t.start=function(t){let r=[];return _(e,(e,n)=>{if(R.und(t))r.push(e.start());else{let i=this._getProps(t,e,n);i&&r.push(e.start(i))}}),r},t.stop=function(){return _(e,e=>e.stop(...arguments)),this},t.update=function(t){return _(e,(e,r)=>e.update(this._getProps(t,e,r))),this};let r=function(e,t,r){return R.fun(e)?e(r,t):e};return t._getProps=r,t};function tQ(e,t,r){let n=R.fun(t)&&t,{reset:i,sort:a,trail:o=0,expires:s=!0,exitBeforeEnter:l=!1,onDestroyed:u,ref:d,config:c}=n?n():t,h=(0,I.useMemo)(()=>n||3==arguments.length?tY():void 0,[]),p=E(e),f=[],m=(0,I.useRef)(null),g=i?null:m.current;eV(()=>{m.current=f}),eG(()=>(_(f,e=>{h?.add(e.ctrl),e.ctrl.ref=h}),()=>{_(m.current,e=>{e.expired&&clearTimeout(e.expirationId),td(e.ctrl,h),e.ctrl.stop(!0)})}));let x=function(e,{key:t,keys:r=t},n){if(null===r){let t=new Set;return e.map(e=>{let r=n&&n.find(r=>r.item===e&&"leave"!==r.phase&&!t.has(r));return r?(t.add(r),r.key):tJ++})}return R.und(r)?e:R.fun(r)?e.map(r):E(r)}(p,n?n():t,g),y=i&&m.current||[];eV(()=>_(y,({ctrl:e,item:t,key:r})=>{td(e,h),e8(u,t,r)}));let v=[];if(g&&_(g,(e,t)=>{e.expired?(clearTimeout(e.expirationId),y.push(e)):~(t=v[t]=x.indexOf(e.key))&&(f[t]=e)}),_(p,(e,t)=>{f[t]||(f[t]={key:x[t],item:e,phase:"mount",ctrl:new tG},f[t].ctrl.item=e)}),v.length){let e=-1,{leave:r}=n?n():t;_(v,(t,n)=>{let i=g[n];~t?(e=f.indexOf(i),f[e]={...i,item:p[t]}):r&&f.splice(++e,0,i)})}R.fun(a)&&f.sort((e,t)=>a(e.item,t.item));let b=-o,Z=eB(),w=ti(t),A=new Map,k=(0,I.useRef)(new Map),C=(0,I.useRef)(!1);_(f,(e,r)=>{let i,a;let u=e.key,h=e.phase,p=n?n():t,f=e8(p.delay||0,u);if("mount"==h)i=p.enter,a="enter";else{let e=0>x.indexOf(u);if("leave"!=h){if(e)i=p.leave,a="leave";else{if(!(i=p.update))return;a="update"}}else{if(e)return;i=p.enter,a="enter"}}if(i=e8(i,e.item,r),!(i=R.obj(i)?ts(i):{to:i}).config){let t=c||w.config;i.config=e8(t,e.item,r,a)}b+=o;let y={...w,delay:f+b,ref:d,immediate:p.immediate,reset:!1,...i};if("enter"==a&&R.und(y.from)){let i=n?n():t,a=R.und(i.initial)||g?i.from:i.initial;y.from=e8(a,e.item,r)}let{onResolve:v}=y;y.onResolve=e=>{e8(v,e);let t=m.current,r=t.find(e=>e.key===u);if(r){if(e.cancelled&&"update"!=r.phase)return;if(r.ctrl.idle){let e=t.every(e=>e.ctrl.idle);if("leave"==r.phase){let t=e8(s,r.item);if(!1!==t){let n=!0===t?0:t;if(r.expired=!0,!e&&n>0){n<=2147483647&&(r.expirationId=setTimeout(Z,n));return}}}e&&t.some(e=>e.expired)&&(k.current.delete(r),l&&(C.current=!0),Z())}}};let I=function(e,t){let r={...e.springs};return t&&_(E(t),e=>{R.und(e.keys)&&(e=tT(e)),R.obj(e.to)||(e={...e,to:void 0}),tW(r,e,e=>tU(e))}),$(r,(t,r)=>{e.springs[r]||(e.springs[r]=t,ek(t,e))}),r}(e.ctrl,y);"leave"===a&&l?k.current.set(e,{phase:a,springs:I,payload:y}):A.set(e,{phase:a,springs:I,payload:y})});let M=(0,I.useContext)(tX),P=function(e){let t=(0,I.useRef)();return(0,I.useEffect)(()=>{t.current=e}),t.current}(M),j=M!==P&&function(e){for(let t in e)return!0;return!1}(M);eV(()=>{j&&_(f,e=>{e.ctrl.start({default:M})})},[M]),_(A,(e,t)=>{if(k.current.size){let e=f.findIndex(e=>e.key===t.key);f.splice(e,1)}}),eV(()=>{_(k.current.size?k.current:A,({phase:e,payload:t},r)=>{let{ctrl:n}=r;if(r.phase=e,h?.add(n),j&&"enter"==e&&n.start({default:M}),t){var i;(i=t.ref)&&n.ref!==i&&(n.ref?.delete(n),i.add(n),n.ref=i),(n.ref||h)&&!C.current?n.update(t):(n.start(t),C.current&&(C.current=!1))}})},i?void 0:r);let S=e=>I.createElement(I.Fragment,null,f.map((t,r)=>{let{springs:n}=A.get(t)||t.ctrl,i=e({...n},t.item,t,r);return i&&i.type?I.createElement(i.type,{...i.props,key:R.str(t.key)||R.num(t.key)?t.key:t.ctrl.id,ref:i.ref}):i}));return h?[S,h]:S}var tJ=1,t0=class extends tM{constructor(e,t){super(),this.source=e,this.idle=!0,this._active=new Set,this.calc=em(...t);let r=this._get();eX(this,e3(r).create(r))}advance(e){let t=this._get();S(t,this.get())||(eq(this).setValue(t),this._onChange(t,this.idle)),!this.idle&&t2(this._active)&&t5(this)}_get(){let e=R.arr(this.source)?this.source.map(ev):E(ev(this.source));return this.calc(...e)}_start(){this.idle&&!t2(this._active)&&(this.idle=!1,_(eK(this),e=>{e.done=!1}),M.skipAnimation?(l.batchedUpdates(()=>this.advance()),t5(this)):U.start(this))}_attach(){let e=1;_(E(this.source),t=>{ey(t)&&ek(t,this),tI(t)&&(t.idle||this._active.add(t),e=Math.max(e,t.priority+1))}),this.priority=e,this._start()}_detach(){_(E(this.source),e=>{ey(e)&&eI(e,this)}),this._active.clear(),t5(this)}eventObserved(e){"change"==e.type?e.idle?this.advance():(this._active.add(e.parent),this._start()):"idle"==e.type?this._active.delete(e.parent):"priority"==e.type&&(this.priority=E(this.source).reduce((e,t)=>Math.max(e,(tI(t)?t.priority:0)+1),0))}};function t1(e){return!1!==e.idle}function t2(e){return!e.size||Array.from(e).every(t1)}function t5(e){e.idle||(e.idle=!0,_(eK(e),e=>{e.done=!0}),eZ(e,{type:"idle",parent:e}))}var t3=(e,...t)=>new t0(e,t);M.assign({createStringInterpolator:eL,to:(e,t)=>new t0(e,t)}),U.advance;var t4=r(54887),t7=/^--/,t9={},t6={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},t8=(e,t)=>e+t.charAt(0).toUpperCase()+t.substring(1),re=["Webkit","Ms","Moz","O"];t6=Object.keys(t6).reduce((e,t)=>(re.forEach(r=>e[t8(r,t)]=e[t]),e),t6);var rt=/^(matrix|translate|scale|rotate|skew)/,rr=/^(translate)/,rn=/^(rotate|skew)/,ri=(e,t)=>R.num(e)&&0!==e?e+t:e,ra=(e,t)=>R.arr(e)?e.every(e=>ra(e,t)):R.num(e)?e===t:parseFloat(e)===t,ro=class extends e1{constructor({x:e,y:t,z:r,...n}){let i=[],a=[];(e||t||r)&&(i.push([e||0,t||0,r||0]),a.push(e=>[`translate3d(${e.map(e=>ri(e,"px")).join(",")})`,ra(e,0)])),$(n,(e,t)=>{if("transform"===t)i.push([e||""]),a.push(e=>[e,""===e]);else if(rt.test(t)){if(delete n[t],R.und(e))return;let r=rr.test(t)?"px":rn.test(t)?"deg":"";i.push(E(e)),a.push("rotate3d"===t?([e,t,n,i])=>[`rotate3d(${e},${t},${n},${ri(i,r)})`,ra(i,0)]:e=>[`${t}(${e.map(e=>ri(e,r)).join(",")})`,ra(e,t.startsWith("scale")?1:0)])}}),i.length&&(n.transform=new rs(i,a)),super(n)}},rs=class extends ew{constructor(e,t){super(),this.inputs=e,this.transforms=t,this._value=null}get(){return this._value||(this._value=this._get())}_get(){let e="",t=!0;return _(this.inputs,(r,n)=>{let i=ev(r[0]),[a,o]=this.transforms[n](R.arr(i)?i:r.map(ev));e+=" "+a,t=t&&o}),t?"none":e}observerAdded(e){1==e&&_(this.inputs,e=>_(e,e=>ey(e)&&ek(e,this)))}observerRemoved(e){0==e&&_(this.inputs,e=>_(e,e=>ey(e)&&eI(e,this)))}eventObserved(e){"change"==e.type&&(this._value=null),eZ(this,e)}};M.assign({batchedUpdates:t4.unstable_batchedUpdates,createStringInterpolator:eL,colors:{transparent:0,aliceblue:4042850303,antiquewhite:4209760255,aqua:16777215,aquamarine:2147472639,azure:4043309055,beige:4126530815,bisque:4293182719,black:255,blanchedalmond:4293643775,blue:65535,blueviolet:2318131967,brown:2771004159,burlywood:3736635391,burntsienna:3934150143,cadetblue:1604231423,chartreuse:2147418367,chocolate:3530104575,coral:4286533887,cornflowerblue:1687547391,cornsilk:4294499583,crimson:3692313855,cyan:16777215,darkblue:35839,darkcyan:9145343,darkgoldenrod:3095792639,darkgray:2846468607,darkgreen:6553855,darkgrey:2846468607,darkkhaki:3182914559,darkmagenta:2332068863,darkolivegreen:1433087999,darkorange:4287365375,darkorchid:2570243327,darkred:2332033279,darksalmon:3918953215,darkseagreen:2411499519,darkslateblue:1211993087,darkslategray:793726975,darkslategrey:793726975,darkturquoise:13554175,darkviolet:2483082239,deeppink:4279538687,deepskyblue:12582911,dimgray:1768516095,dimgrey:1768516095,dodgerblue:512819199,firebrick:2988581631,floralwhite:4294635775,forestgreen:579543807,fuchsia:4278255615,gainsboro:3705462015,ghostwhite:4177068031,gold:4292280575,goldenrod:3668254975,gray:2155905279,green:8388863,greenyellow:2919182335,grey:2155905279,honeydew:4043305215,hotpink:4285117695,indianred:3445382399,indigo:1258324735,ivory:4294963455,khaki:4041641215,lavender:3873897215,lavenderblush:4293981695,lawngreen:2096890111,lemonchiffon:4294626815,lightblue:2916673279,lightcoral:4034953471,lightcyan:3774873599,lightgoldenrodyellow:4210742015,lightgray:3553874943,lightgreen:2431553791,lightgrey:3553874943,lightpink:4290167295,lightsalmon:4288707327,lightseagreen:548580095,lightskyblue:2278488831,lightslategray:2005441023,lightslategrey:2005441023,lightsteelblue:2965692159,lightyellow:4294959359,lime:16711935,limegreen:852308735,linen:4210091775,magenta:4278255615,maroon:2147483903,mediumaquamarine:1724754687,mediumblue:52735,mediumorchid:3126187007,mediumpurple:2473647103,mediumseagreen:1018393087,mediumslateblue:2070474495,mediumspringgreen:16423679,mediumturquoise:1221709055,mediumvioletred:3340076543,midnightblue:421097727,mintcream:4127193855,mistyrose:4293190143,moccasin:4293178879,navajowhite:4292783615,navy:33023,oldlace:4260751103,olive:2155872511,olivedrab:1804477439,orange:4289003775,orangered:4282712319,orchid:3664828159,palegoldenrod:4008225535,palegreen:2566625535,paleturquoise:2951671551,palevioletred:3681588223,papayawhip:4293907967,peachpuff:4292524543,peru:3448061951,pink:4290825215,plum:3718307327,powderblue:2967529215,purple:2147516671,rebeccapurple:1714657791,red:4278190335,rosybrown:3163525119,royalblue:1097458175,saddlebrown:2336560127,salmon:4202722047,sandybrown:4104413439,seagreen:780883967,seashell:4294307583,sienna:2689740287,silver:3233857791,skyblue:2278484991,slateblue:1784335871,slategray:1887473919,slategrey:1887473919,snow:4294638335,springgreen:16744447,steelblue:1182971135,tan:3535047935,teal:8421631,thistle:3636451583,tomato:4284696575,turquoise:1088475391,violet:4001558271,wheat:4125012991,white:4294967295,whitesmoke:4126537215,yellow:4294902015,yellowgreen:2597139199}});var rl=((e,{applyAnimatedValues:t=()=>!1,createAnimatedStyle:r=e=>new e1(e),getComponentProps:n=e=>e}={})=>{let i={applyAnimatedValues:t,createAnimatedStyle:r,getComponentProps:n},a=e=>{let t=e6(e)||"Anonymous";return(e=R.str(e)?a[e]||(a[e]=e4(e,i)):e[e9]||(e[e9]=e4(e,i))).displayName=`Animated(${t})`,e};return $(e,(t,r)=>{R.arr(e)&&(r=e6(t)),a[r]=a(t)}),{animated:a}})(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],{applyAnimatedValues:function(e,t){if(!e.nodeType||!e.setAttribute)return!1;let r="filter"===e.nodeName||e.parentNode&&"filter"===e.parentNode.nodeName,{className:n,style:i,children:a,scrollTop:o,scrollLeft:s,viewBox:l,...u}=t,d=Object.values(u),c=Object.keys(u).map(t=>r||e.hasAttribute(t)?t:t9[t]||(t9[t]=t.replace(/([A-Z])/g,e=>"-"+e.toLowerCase())));for(let t in void 0!==a&&(e.textContent=a),i)if(i.hasOwnProperty(t)){var h;let r=null==(h=i[t])||"boolean"==typeof h||""===h?"":"number"!=typeof h||0===h||t7.test(t)||t6.hasOwnProperty(t)&&t6[t]?(""+h).trim():h+"px";t7.test(t)?e.style.setProperty(t,r):e.style[t]=r}c.forEach((t,r)=>{e.setAttribute(t,d[r])}),void 0!==n&&(e.className=n),void 0!==o&&(e.scrollTop=o),void 0!==s&&(e.scrollLeft=s),void 0!==l&&e.setAttribute("viewBox",l)},createAnimatedStyle:e=>new ro(e),getComponentProps:({scrollTop:e,scrollLeft:t,...r})=>r}).animated}}]);