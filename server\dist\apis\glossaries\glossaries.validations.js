"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.glossaryUpdateSchema = exports.glossarySchema = void 0;
const joi_1 = __importDefault(require("joi"));
const glossaries_interface_1 = require("./glossaries.interface");
// Schema for version fields (common to both languages)
const versionFieldsSchema = {
    word: joi_1.default.string().trim().min(1).required(),
    letter: joi_1.default.string().required(),
    content: joi_1.default.string().required(),
    metaTitle: joi_1.default.string().required(),
    metaDescription: joi_1.default.string().required(),
    url: joi_1.default.string().required(),
    language: joi_1.default.valid(...Object.values(glossaries_interface_1.Language)),
    visibility: joi_1.default.valid(...Object.values(glossaries_interface_1.Visibility)),
    isArchived: joi_1.default.boolean(),
    createdAt: joi_1.default.date(),
    updatedAt: joi_1.default.date(),
};
// Schema for creating a new glossary
const glossarySchema = joi_1.default.object({
    versions: joi_1.default.object()
        .pattern(joi_1.default.string().valid('en', 'fr'), // Keys must be 'en' or 'fr'
    joi_1.default.object(versionFieldsSchema))
        .min(1)
        .required(), // At least one language version is required
    robotsMeta: joi_1.default.valid(...Object.values(glossaries_interface_1.robotsMeta)),
});
exports.glossarySchema = glossarySchema;
// Schema for updating a glossary
const glossaryUpdateSchema = joi_1.default.object({
    versions: joi_1.default.object().pattern(joi_1.default.string().valid('en', 'fr'), // Keys must be 'en' or 'fr'
    joi_1.default.object({
        word: joi_1.default.string().trim().min(1),
        letter: joi_1.default.string(),
        content: joi_1.default.string(),
        metaTitle: joi_1.default.string(),
        metaDescription: joi_1.default.string(),
        url: joi_1.default.string(),
        language: joi_1.default.valid(...Object.values(glossaries_interface_1.Language)),
        visibility: joi_1.default.valid(...Object.values(glossaries_interface_1.Visibility)),
        isArchived: joi_1.default.boolean(),
    })),
    robotsMeta: joi_1.default.valid(...Object.values(glossaries_interface_1.robotsMeta)),
});
exports.glossaryUpdateSchema = glossaryUpdateSchema;
//# sourceMappingURL=glossaries.validations.js.map