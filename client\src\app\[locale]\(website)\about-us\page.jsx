import BannerComponents from "@/components/sections/BannerComponents";
import banner from "../../../../assets/images/website/banner/aboutUsBanner.webp";
import { Container, Grid } from "@mui/material";
import avatar from "../../../../assets/images/team/walid.jpg";
import signwalid from "../../../../assets/images/team/signwalid.png";

import PentabellByNumbers from "@/components/sections/PentabellByNumbers";
import WhatWeBeleiveSection from "@/components/sections/WhatWeBeleiveSection";
import OurValues from "@/components/sections/OurValues";
import Offices from "@/components/sections/Offices";
import ExploreMore from "@/components/sections/ExploreMore";
import OurPartners from "@/components/sections/OurPartners";
import initTranslations from "@/app/i18n";
import { websiteRoutesList } from "../../../../helpers/routesList";
import { axiosGetJsonSSR } from "@/config/axios";
export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }${websiteRoutesList.aboutUs.route}/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/${websiteRoutesList.aboutUs.route}/`,
    en: `https://www.pentabell.com/${websiteRoutesList.aboutUs.route}/`,
    "x-default": `https://www.pentabell.com/${websiteRoutesList.aboutUs.route}/`,
  };

  const { t } = await initTranslations(locale, ["aboutUs", "global"]);

  try {
    const encodedSlug = encodeURIComponent(websiteRoutesList.aboutUs.route);
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/${encodedSlug}`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("aboutUs:metaTitle"),
    description: t("aboutUs:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

async function page({ params: { locale } }) {
  const { t } = await initTranslations(locale, ["aboutUs", "global"]);
  return (
    <div id="about-us-page">
      <BannerComponents
        bannerImg={banner}
        height={"100vh"}
        title={t("aboutUs:intro:title")}
        description={t("aboutUs:intro:description")}
        altImg={t("aboutUs:intro:altImg")}
      />
      <div className="intro-section">
        <Container>
          <Grid className="container" container spacing={2}>
            <Grid item xs={12} sm={6}>
              <h2 className="heading-h1 text-white">
                {t("aboutUs:overview:title")}
              </h2>
            </Grid>
            <Grid item xs={12} sm={6}>
              <p className="sub-heading text-white">
                {t("aboutUs:overview:paragraph1")}
              </p>
              <p className="sub-heading text-white">
                {t("aboutUs:overview:paragraph2")}
              </p>
            </Grid>
          </Grid>
        </Container>
        <Container className="quote-section">
          <div className="first-row">
            <img
              alt={t("aboutUs:card:altImg")}
              src={avatar.src}
              loading="lazy"
              className="user-img"
            />
            <p className="sub-heading text-yellow text-center">
              Walid BEN JEMAA
            </p>

            <p className="paragraph text-white text-center">
              {" "}
              {t("aboutUs:card:position")}
            </p>
          </div>
          <div>
            <p className="quote">“{t("aboutUs:card:description")}”</p>
            <img
              src={signwalid.src}
              className="user-img sign"
              alt="Sign Walid"
              loading="lazy"
            />
          </div>
        </Container>
      </div>
      <PentabellByNumbers locale={locale} />
      <WhatWeBeleiveSection locale={locale} />
      <OurValues />
      <Offices />
      <ExploreMore />
      <OurPartners />
    </div>
  );
}

export default page;
