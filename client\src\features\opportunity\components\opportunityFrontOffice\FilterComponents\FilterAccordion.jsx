import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

const FilterAccordion = ({ title, expanded, onChange, children }) => (
  <Accordion
    expanded={expanded}
    onChange={onChange}
    sx={{
      backgroundColor: "transparent",
      boxShadow: "none",
      margin: 0,
      padding: 0,
    }}
  >
    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
      <Typography
        component="span"
        sx={{ width: "auto", flexShrink: 0 }}
        className="title-filter"
      >
        {title}
      </Typography>
    </AccordionSummary>
    <AccordionDetails>{children}</AccordionDetails>
  </Accordion>
);

export default FilterAccordion;
