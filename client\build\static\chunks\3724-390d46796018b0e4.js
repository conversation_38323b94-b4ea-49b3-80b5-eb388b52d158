"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3724,8990],{44164:function(t,e,i){i.d(e,{Z:function(){return f}});var n=i(2265),r=i(61994),s=i(20801),o=i(16210),a=i(76301),l=i(37053),u=i(94143),h=i(50738);function d(t){return(0,h.ZP)("MuiAccordionDetails",t)}(0,u.Z)("MuiAccordionDetails",["root"]);var c=i(57437);let p=t=>{let{classes:e}=t;return(0,s.Z)({root:["root"]},d,e)},m=(0,o.ZP)("div",{name:"MuiAccordionDetails",slot:"Root",overridesResolver:(t,e)=>e.root})((0,a.Z)(t=>{let{theme:e}=t;return{padding:e.spacing(1,2,2)}}));var f=n.forwardRef(function(t,e){let i=(0,l.i)({props:t,name:"MuiAccordionDetails"}),{className:n,...s}=i,o=p(i);return(0,c.jsx)(m,{className:(0,r.Z)(o.root,n),ref:e,ownerState:i,...s})})},96369:function(t,e,i){i.d(e,{Z:function(){return b}});var n=i(2265),r=i(61994),s=i(20801),o=i(16210),a=i(76301),l=i(37053),u=i(82662),h=i(31288),d=i(94143),c=i(50738);function p(t){return(0,c.ZP)("MuiAccordionSummary",t)}let m=(0,d.Z)("MuiAccordionSummary",["root","expanded","focusVisible","disabled","gutters","contentGutters","content","expandIconWrapper"]);var f=i(79114),g=i(57437);let y=t=>{let{classes:e,expanded:i,disabled:n,disableGutters:r}=t;return(0,s.Z)({root:["root",i&&"expanded",n&&"disabled",!r&&"gutters"],focusVisible:["focusVisible"],content:["content",i&&"expanded",!r&&"contentGutters"],expandIconWrapper:["expandIconWrapper",i&&"expanded"]},p,e)},v=(0,o.ZP)(u.Z,{name:"MuiAccordionSummary",slot:"Root",overridesResolver:(t,e)=>e.root})((0,a.Z)(t=>{let{theme:e}=t,i={duration:e.transitions.duration.shortest};return{display:"flex",width:"100%",minHeight:48,padding:e.spacing(0,2),transition:e.transitions.create(["min-height","background-color"],i),[`&.${m.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`&.${m.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity},[`&:hover:not(.${m.disabled})`]:{cursor:"pointer"},variants:[{props:t=>!t.disableGutters,style:{[`&.${m.expanded}`]:{minHeight:64}}}]}})),x=(0,o.ZP)("span",{name:"MuiAccordionSummary",slot:"Content",overridesResolver:(t,e)=>e.content})((0,a.Z)(t=>{let{theme:e}=t;return{display:"flex",textAlign:"start",flexGrow:1,margin:"12px 0",variants:[{props:t=>!t.disableGutters,style:{transition:e.transitions.create(["margin"],{duration:e.transitions.duration.shortest}),[`&.${m.expanded}`]:{margin:"20px 0"}}}]}})),w=(0,o.ZP)("span",{name:"MuiAccordionSummary",slot:"ExpandIconWrapper",overridesResolver:(t,e)=>e.expandIconWrapper})((0,a.Z)(t=>{let{theme:e}=t;return{display:"flex",color:(e.vars||e).palette.action.active,transform:"rotate(0deg)",transition:e.transitions.create("transform",{duration:e.transitions.duration.shortest}),[`&.${m.expanded}`]:{transform:"rotate(180deg)"}}}));var b=n.forwardRef(function(t,e){let i=(0,l.i)({props:t,name:"MuiAccordionSummary"}),{children:s,className:o,expandIcon:a,focusVisibleClassName:u,onClick:d,slots:c,slotProps:p,...m}=i,{disabled:b=!1,disableGutters:T,expanded:P,toggle:S}=n.useContext(h.Z),A=t=>{S&&S(t),d&&d(t)},M={...i,expanded:P,disabled:b,disableGutters:T},C=y(M),E={slots:c,slotProps:p},[V,D]=(0,f.Z)("root",{ref:e,shouldForwardComponentProp:!0,className:(0,r.Z)(C.root,o),elementType:v,externalForwardedProps:{...E,...m},ownerState:M,additionalProps:{focusRipple:!1,disableRipple:!0,disabled:b,"aria-expanded":P,focusVisibleClassName:(0,r.Z)(C.focusVisible,u)},getSlotProps:t=>({...t,onClick:e=>{t.onClick?.(e),A(e)}})}),[R,k]=(0,f.Z)("content",{className:C.content,elementType:x,externalForwardedProps:E,ownerState:M}),[L,j]=(0,f.Z)("expandIconWrapper",{className:C.expandIconWrapper,elementType:w,externalForwardedProps:E,ownerState:M});return(0,g.jsxs)(V,{...D,children:[(0,g.jsx)(R,{...k,children:s}),a&&(0,g.jsx)(L,{...j,children:a})]})})},30731:function(t,e,i){i.d(e,{Z:function(){return T}});var n=i(2265),r=i(61994),s=i(20801),o=i(16210),a=i(76301),l=i(37053),u=i(17162),h=i(53410),d=i(31288),c=i(67184),p=i(79114),m=i(94143),f=i(50738);function g(t){return(0,f.ZP)("MuiAccordion",t)}let y=(0,m.Z)("MuiAccordion",["root","heading","rounded","expanded","disabled","gutters","region"]);var v=i(57437);let x=t=>{let{classes:e,square:i,expanded:n,disabled:r,disableGutters:o}=t;return(0,s.Z)({root:["root",!i&&"rounded",n&&"expanded",r&&"disabled",!o&&"gutters"],heading:["heading"],region:["region"]},g,e)},w=(0,o.ZP)(h.Z,{name:"MuiAccordion",slot:"Root",overridesResolver:(t,e)=>{let{ownerState:i}=t;return[{[`& .${y.region}`]:e.region},e.root,!i.square&&e.rounded,!i.disableGutters&&e.gutters]}})((0,a.Z)(t=>{let{theme:e}=t,i={duration:e.transitions.duration.shortest};return{position:"relative",transition:e.transitions.create(["margin"],i),overflowAnchor:"none","&::before":{position:"absolute",left:0,top:-1,right:0,height:1,content:'""',opacity:1,backgroundColor:(e.vars||e).palette.divider,transition:e.transitions.create(["opacity","background-color"],i)},"&:first-of-type":{"&::before":{display:"none"}},[`&.${y.expanded}`]:{"&::before":{opacity:0},"&:first-of-type":{marginTop:0},"&:last-of-type":{marginBottom:0},"& + &":{"&::before":{display:"none"}}},[`&.${y.disabled}`]:{backgroundColor:(e.vars||e).palette.action.disabledBackground}}}),(0,a.Z)(t=>{let{theme:e}=t;return{variants:[{props:t=>!t.square,style:{borderRadius:0,"&:first-of-type":{borderTopLeftRadius:(e.vars||e).shape.borderRadius,borderTopRightRadius:(e.vars||e).shape.borderRadius},"&:last-of-type":{borderBottomLeftRadius:(e.vars||e).shape.borderRadius,borderBottomRightRadius:(e.vars||e).shape.borderRadius,"@supports (-ms-ime-align: auto)":{borderBottomLeftRadius:0,borderBottomRightRadius:0}}}},{props:t=>!t.disableGutters,style:{[`&.${y.expanded}`]:{margin:"16px 0"}}}]}})),b=(0,o.ZP)("h3",{name:"MuiAccordion",slot:"Heading",overridesResolver:(t,e)=>e.heading})({all:"unset"});var T=n.forwardRef(function(t,e){let i=(0,l.i)({props:t,name:"MuiAccordion"}),{children:s,className:o,defaultExpanded:a=!1,disabled:h=!1,disableGutters:m=!1,expanded:f,onChange:g,square:y=!1,slots:T={},slotProps:P={},TransitionComponent:S,TransitionProps:A,...M}=i,[C,E]=(0,c.Z)({controlled:f,default:a,name:"Accordion",state:"expanded"}),V=n.useCallback(t=>{E(!C),g&&g(t,!C)},[C,g,E]),[D,...R]=n.Children.toArray(s),k=n.useMemo(()=>({expanded:C,disabled:h,disableGutters:m,toggle:V}),[C,h,m,V]),L={...i,square:y,disabled:h,disableGutters:m,expanded:C},j=x(L),F={slots:{transition:S,...T},slotProps:{transition:A,...P}},[B,O]=(0,p.Z)("root",{elementType:w,externalForwardedProps:{...F,...M},className:(0,r.Z)(j.root,o),shouldForwardComponentProp:!0,ownerState:L,ref:e,additionalProps:{square:y}}),[Z,I]=(0,p.Z)("heading",{elementType:b,externalForwardedProps:F,className:j.heading,ownerState:L}),[$,N]=(0,p.Z)("transition",{elementType:u.Z,externalForwardedProps:F,ownerState:L});return(0,v.jsxs)(B,{...O,children:[(0,v.jsx)(Z,{...I,children:(0,v.jsx)(d.Z.Provider,{value:k,children:D})}),(0,v.jsx)($,{in:C,timeout:"auto",...N,children:(0,v.jsx)("div",{"aria-labelledby":D.props.id,id:D.props["aria-controls"],role:"region",className:j.region,children:R})})]})})},31288:function(t,e,i){let n=i(2265).createContext({});e.Z=n},36137:function(t,e,i){i.d(e,{Z:function(){return m}});var n=i(2265),r=i(61994),s=i(20801),o=i(16210),a=i(37053),l=i(94143),u=i(50738);function h(t){return(0,u.ZP)("MuiCardContent",t)}(0,l.Z)("MuiCardContent",["root"]);var d=i(57437);let c=t=>{let{classes:e}=t;return(0,s.Z)({root:["root"]},h,e)},p=(0,o.ZP)("div",{name:"MuiCardContent",slot:"Root",overridesResolver:(t,e)=>e.root})({padding:16,"&:last-child":{paddingBottom:24}});var m=n.forwardRef(function(t,e){let i=(0,a.i)({props:t,name:"MuiCardContent"}),{className:n,component:s="div",...o}=i,l={...i,component:s},u=c(l);return(0,d.jsx)(p,{as:s,className:(0,r.Z)(u.root,n),ownerState:l,ref:e,...o})})},45841:function(t,e,i){i.d(e,{Z:function(){return g}});var n=i(2265),r=i(61994),s=i(20801),o=i(16210),a=i(37053),l=i(94143),u=i(50738);function h(t){return(0,u.ZP)("MuiCardMedia",t)}(0,l.Z)("MuiCardMedia",["root","media","img"]);var d=i(57437);let c=t=>{let{classes:e,isMediaComponent:i,isImageComponent:n}=t;return(0,s.Z)({root:["root",i&&"media",n&&"img"]},h,e)},p=(0,o.ZP)("div",{name:"MuiCardMedia",slot:"Root",overridesResolver:(t,e)=>{let{ownerState:i}=t,{isMediaComponent:n,isImageComponent:r}=i;return[e.root,n&&e.media,r&&e.img]}})({display:"block",backgroundSize:"cover",backgroundRepeat:"no-repeat",backgroundPosition:"center",variants:[{props:{isMediaComponent:!0},style:{width:"100%"}},{props:{isImageComponent:!0},style:{objectFit:"cover"}}]}),m=["video","audio","picture","iframe","img"],f=["picture","img"];var g=n.forwardRef(function(t,e){let i=(0,a.i)({props:t,name:"MuiCardMedia"}),{children:n,className:s,component:o="div",image:l,src:u,style:h,...g}=i,y=m.includes(o),v=!y&&l?{backgroundImage:`url("${l}")`,...h}:h,x={...i,component:o,isMediaComponent:y,isImageComponent:f.includes(o)},w=c(x);return(0,d.jsx)(p,{className:(0,r.Z)(w.root,s),as:o,role:!y&&l?"img":void 0,ref:e,style:v,ownerState:x,src:y?l||u:void 0,...g,children:n})})},67208:function(t,e,i){i.d(e,{Z:function(){return f}});var n=i(2265),r=i(61994),s=i(20801),o=i(16210),a=i(37053),l=i(53410),u=i(94143),h=i(50738);function d(t){return(0,h.ZP)("MuiCard",t)}(0,u.Z)("MuiCard",["root"]);var c=i(57437);let p=t=>{let{classes:e}=t;return(0,s.Z)({root:["root"]},d,e)},m=(0,o.ZP)(l.Z,{name:"MuiCard",slot:"Root",overridesResolver:(t,e)=>e.root})({overflow:"hidden"});var f=n.forwardRef(function(t,e){let i=(0,a.i)({props:t,name:"MuiCard"}),{className:n,raised:s=!1,...o}=i,l={...i,raised:s},u=p(l);return(0,c.jsx)(m,{className:(0,r.Z)(u.root,n),elevation:s?8:void 0,ref:e,ownerState:l,...o})})},17162:function(t,e,i){i.d(e,{Z:function(){return S}});var n=i(2265),r=i(61994),s=i(52836),o=i(73207),a=i(20801),l=i(16210),u=i(31691),h=i(76301),d=i(37053),c=i(73220),p=i(31090),m=i(60118),f=i(94143),g=i(50738);function y(t){return(0,g.ZP)("MuiCollapse",t)}(0,f.Z)("MuiCollapse",["root","horizontal","vertical","entered","hidden","wrapper","wrapperInner"]);var v=i(57437);let x=t=>{let{orientation:e,classes:i}=t,n={root:["root",`${e}`],entered:["entered"],hidden:["hidden"],wrapper:["wrapper",`${e}`],wrapperInner:["wrapperInner",`${e}`]};return(0,a.Z)(n,y,i)},w=(0,l.ZP)("div",{name:"MuiCollapse",slot:"Root",overridesResolver:(t,e)=>{let{ownerState:i}=t;return[e.root,e[i.orientation],"entered"===i.state&&e.entered,"exited"===i.state&&!i.in&&"0px"===i.collapsedSize&&e.hidden]}})((0,h.Z)(t=>{let{theme:e}=t;return{height:0,overflow:"hidden",transition:e.transitions.create("height"),variants:[{props:{orientation:"horizontal"},style:{height:"auto",width:0,transition:e.transitions.create("width")}},{props:{state:"entered"},style:{height:"auto",overflow:"visible"}},{props:{state:"entered",orientation:"horizontal"},style:{width:"auto"}},{props:t=>{let{ownerState:e}=t;return"exited"===e.state&&!e.in&&"0px"===e.collapsedSize},style:{visibility:"hidden"}}]}})),b=(0,l.ZP)("div",{name:"MuiCollapse",slot:"Wrapper",overridesResolver:(t,e)=>e.wrapper})({display:"flex",width:"100%",variants:[{props:{orientation:"horizontal"},style:{width:"auto",height:"100%"}}]}),T=(0,l.ZP)("div",{name:"MuiCollapse",slot:"WrapperInner",overridesResolver:(t,e)=>e.wrapperInner})({width:"100%",variants:[{props:{orientation:"horizontal"},style:{width:"auto",height:"100%"}}]}),P=n.forwardRef(function(t,e){let i=(0,d.i)({props:t,name:"MuiCollapse"}),{addEndListener:a,children:l,className:h,collapsedSize:f="0px",component:g,easing:y,in:P,onEnter:S,onEntered:A,onEntering:M,onExit:C,onExited:E,onExiting:V,orientation:D="vertical",style:R,timeout:k=c.x9.standard,TransitionComponent:L=s.ZP,...j}=i,F={...i,orientation:D,collapsedSize:f},B=x(F),O=(0,u.Z)(),Z=(0,o.Z)(),I=n.useRef(null),$=n.useRef(),N="number"==typeof f?`${f}px`:f,U="horizontal"===D,W=U?"width":"height",z=n.useRef(null),H=(0,m.Z)(e,z),Y=t=>e=>{if(t){let i=z.current;void 0===e?t(i):t(i,e)}},X=()=>I.current?I.current[U?"clientWidth":"clientHeight"]:0,_=Y((t,e)=>{I.current&&U&&(I.current.style.position="absolute"),t.style[W]=N,S&&S(t,e)}),q=Y((t,e)=>{let i=X();I.current&&U&&(I.current.style.position="");let{duration:n,easing:r}=(0,p.C)({style:R,timeout:k,easing:y},{mode:"enter"});if("auto"===k){let e=O.transitions.getAutoHeightDuration(i);t.style.transitionDuration=`${e}ms`,$.current=e}else t.style.transitionDuration="string"==typeof n?n:`${n}ms`;t.style[W]=`${i}px`,t.style.transitionTimingFunction=r,M&&M(t,e)}),K=Y((t,e)=>{t.style[W]="auto",A&&A(t,e)}),G=Y(t=>{t.style[W]=`${X()}px`,C&&C(t)}),J=Y(E),Q=Y(t=>{let e=X(),{duration:i,easing:n}=(0,p.C)({style:R,timeout:k,easing:y},{mode:"exit"});if("auto"===k){let i=O.transitions.getAutoHeightDuration(e);t.style.transitionDuration=`${i}ms`,$.current=i}else t.style.transitionDuration="string"==typeof i?i:`${i}ms`;t.style[W]=N,t.style.transitionTimingFunction=n,V&&V(t)});return(0,v.jsx)(L,{in:P,onEnter:_,onEntered:K,onEntering:q,onExit:G,onExited:J,onExiting:Q,addEndListener:t=>{"auto"===k&&Z.start($.current||0,t),a&&a(z.current,t)},nodeRef:z,timeout:"auto"===k?null:k,...j,children:(t,e)=>{let{ownerState:i,...n}=e;return(0,v.jsx)(w,{as:g,className:(0,r.Z)(B.root,h,{entered:B.entered,exited:!P&&"0px"===N&&B.hidden}[t]),style:{[U?"minWidth":"minHeight"]:N,...R},ref:H,ownerState:{...F,state:t},...n,children:(0,v.jsx)(b,{ownerState:{...F,state:t},className:B.wrapper,ref:I,children:(0,v.jsx)(T,{ownerState:{...F,state:t},className:B.wrapperInner,children:l})})})}})});P&&(P.muiSupportAuto=!0);var S=P},97312:function(t,e,i){i.d(e,{default:function(){return A}});var n=i(2265),r=i(61994),s=i(82590),o=i(20801),a=i(62919),l=i(85657),u=i(16210),h=i(31691),d=i(76301),c=i(3858),p=i(37053),m=i(46387),f=i(94143),g=i(50738);function y(t){return(0,g.ZP)("MuiLink",t)}let v=(0,f.Z)("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]);var x=i(44845),w=t=>{let{theme:e,ownerState:i}=t,n=i.color,r=(0,x.DW)(e,`palette.${n}.main`,!1)||(0,x.DW)(e,`palette.${n}`,!1)||i.color,o=(0,x.DW)(e,`palette.${n}.mainChannel`)||(0,x.DW)(e,`palette.${n}Channel`);return"vars"in e&&o?`rgba(${o} / 0.4)`:(0,s.Fq)(r,.4)},b=i(57437);let T={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},P=t=>{let{classes:e,component:i,focusVisible:n,underline:r}=t,s={root:["root",`underline${(0,l.Z)(r)}`,"button"===i&&"button",n&&"focusVisible"]};return(0,o.Z)(s,y,e)},S=(0,u.ZP)(m.default,{name:"MuiLink",slot:"Root",overridesResolver:(t,e)=>{let{ownerState:i}=t;return[e.root,e[`underline${(0,l.Z)(i.underline)}`],"button"===i.component&&e.button]}})((0,d.Z)(t=>{let{theme:e}=t;return{variants:[{props:{underline:"none"},style:{textDecoration:"none"}},{props:{underline:"hover"},style:{textDecoration:"none","&:hover":{textDecoration:"underline"}}},{props:{underline:"always"},style:{textDecoration:"underline","&:hover":{textDecorationColor:"inherit"}}},{props:t=>{let{underline:e,ownerState:i}=t;return"always"===e&&"inherit"!==i.color},style:{textDecorationColor:"var(--Link-underlineColor)"}},...Object.entries(e.palette).filter((0,c.Z)()).map(t=>{let[i]=t;return{props:{underline:"always",color:i},style:{"--Link-underlineColor":e.vars?`rgba(${e.vars.palette[i].mainChannel} / 0.4)`:(0,s.Fq)(e.palette[i].main,.4)}}}),{props:{underline:"always",color:"textPrimary"},style:{"--Link-underlineColor":e.vars?`rgba(${e.vars.palette.text.primaryChannel} / 0.4)`:(0,s.Fq)(e.palette.text.primary,.4)}},{props:{underline:"always",color:"textSecondary"},style:{"--Link-underlineColor":e.vars?`rgba(${e.vars.palette.text.secondaryChannel} / 0.4)`:(0,s.Fq)(e.palette.text.secondary,.4)}},{props:{underline:"always",color:"textDisabled"},style:{"--Link-underlineColor":(e.vars||e).palette.text.disabled}},{props:{component:"button"},style:{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},[`&.${v.focusVisible}`]:{outline:"auto"}}}]}}));var A=n.forwardRef(function(t,e){let i=(0,p.i)({props:t,name:"MuiLink"}),s=(0,h.Z)(),{className:o,color:l="primary",component:u="a",onBlur:d,onFocus:c,TypographyClasses:m,underline:f="always",variant:g="inherit",sx:y,...v}=i,[x,A]=n.useState(!1),M={...i,color:l,component:u,focusVisible:x,underline:f,variant:g},C=P(M);return(0,b.jsx)(S,{color:l,className:(0,r.Z)(C.root,o),classes:m,component:u,onBlur:t=>{(0,a.Z)(t.target)||A(!1),d&&d(t)},onFocus:t=>{(0,a.Z)(t.target)&&A(!0),c&&c(t)},ref:e,ownerState:M,variant:g,...v,sx:[...void 0===T[l]?[{color:l}]:[],...Array.isArray(y)?y:[y]],style:{...v.style,..."always"===f&&"inherit"!==l&&!T[l]&&{"--Link-underlineColor":w({theme:s,ownerState:M})}}})})},59873:function(t,e,i){i.d(e,{Z:function(){return h}});var n=i(2265),r=i.t(n,2),s=i(3450),o=i(93826),a=i(42827);let l={...r}.useSyncExternalStore;function u(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{themeId:e}=t;return function(t){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=(0,a.Z)();r&&e&&(r=r[e]||r);let u="undefined"!=typeof window&&void 0!==window.matchMedia,{defaultMatches:h=!1,matchMedia:d=u?window.matchMedia:null,ssrMatchMedia:c=null,noSsr:p=!1}=(0,o.Z)({name:"MuiUseMediaQuery",props:i,theme:r}),m="function"==typeof t?t(r):t;return(void 0!==l?function(t,e,i,r,s){let o=n.useCallback(()=>e,[e]),a=n.useMemo(()=>{if(s&&i)return()=>i(t).matches;if(null!==r){let{matches:e}=r(t);return()=>e}return o},[o,t,r,s,i]),[u,h]=n.useMemo(()=>{if(null===i)return[o,()=>()=>{}];let e=i(t);return[()=>e.matches,t=>(e.addEventListener("change",t),()=>{e.removeEventListener("change",t)})]},[o,i,t]);return l(h,u,a)}:function(t,e,i,r,o){let[a,l]=n.useState(()=>o&&i?i(t).matches:r?r(t).matches:e);return(0,s.Z)(()=>{if(!i)return;let e=i(t),n=()=>{l(e.matches)};return n(),e.addEventListener("change",n),()=>{e.removeEventListener("change",n)}},[t,i]),a})(m=m.replace(/^@media( ?)/m,""),h,d,c,p)}}u();var h=u({themeId:i(22166).Z})},64821:function(t,e,i){var n=i(13859),r=i(53731);e.isCompanyEmail=function(t){if(!r.validate(t))return!1;let e=t.split("@")[1];return!n.has(e)},e.isCompanyDomain=function(t){return!n.has(t)}},53731:function(t,e){var i=/^[-!#$%&'*+\/0-9=?A-Z^_a-z{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/;e.validate=function(t){if(!t||t.length>254||!i.test(t))return!1;var e=t.split("@");return!(e[0].length>64||e[1].split(".").some(function(t){return t.length>63}))}},69780:function(t,e,i){var n,r=(n=i(78227))&&n.__esModule?n:{default:n};t.exports={tags:function(t){var e=t.id,i=t.events,n=t.dataLayer,s=t.dataLayerName,o=t.preview,a="&gtm_auth="+t.auth,l="&gtm_preview="+o;e||(0,r.default)("GTM Id is required");var u="\n      (function(w,d,s,l,i){w[l]=w[l]||[];\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js', "+JSON.stringify(i).slice(1,-1)+"});\n        var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';\n        j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl+'"+a+l+"&gtm_cookies_win=x';\n        f.parentNode.insertBefore(j,f);\n      })(window,document,'script','"+s+"','"+e+"');";return{iframe:'\n      <iframe src="https://www.googletagmanager.com/ns.html?id='+e+a+l+'&gtm_cookies_win=x"\n        height="0" width="0" style="display:none;visibility:hidden" id="tag-manager"></iframe>',script:u,dataLayerVar:this.dataLayer(n,s)}},dataLayer:function(t,e){return"\n      window."+e+" = window."+e+" || [];\n      window."+e+".push("+JSON.stringify(t)+")"}}},90761:function(t,e,i){var n,r=(n=i(69780))&&n.__esModule?n:{default:n};t.exports={dataScript:function(t){var e=document.createElement("script");return e.innerHTML=t,e},gtm:function(t){var e=r.default.tags(t);return{noScript:function(){var t=document.createElement("noscript");return t.innerHTML=e.iframe,t},script:function(){var t=document.createElement("script");return t.innerHTML=e.script,t},dataScript:this.dataScript(e.dataLayerVar)}},initialize:function(t){var e=t.gtmId,i=t.events,n=t.dataLayer,r=t.dataLayerName,s=t.auth,o=t.preview,a=this.gtm({id:e,events:void 0===i?{}:i,dataLayer:n||void 0,dataLayerName:void 0===r?"dataLayer":r,auth:void 0===s?"":s,preview:void 0===o?"":o});n&&document.head.appendChild(a.dataScript),document.head.insertBefore(a.script(),document.head.childNodes[0]),document.body.insertBefore(a.noScript(),document.body.childNodes[0])},dataLayer:function(t){var e=t.dataLayer,i=t.dataLayerName,n=void 0===i?"dataLayer":i;if(window[n])return window[n].push(e);var s=r.default.dataLayer(e,n),o=this.dataScript(s);document.head.insertBefore(o,document.head.childNodes[0])}}},4828:function(t,e,i){var n,r=(n=i(90761))&&n.__esModule?n:{default:n};t.exports=r.default},78227:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){console.warn("[react-gtm]",t)}},61984:function(t,e,i){i.d(e,{Z:function(){return r}});let n={active:!0,breakpoints:{},delay:4e3,jump:!1,playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,stopOnLastSnap:!1,rootNode:null};function r(t={}){let e,i,s,o;let a=null,l=0,u=!1,h=!1,d=!1,c=!1;function p(){if(!s){if(g()){d=!0;return}u||i.emit("autoplay:play"),function(){let{ownerWindow:t}=i.internalEngine();t.clearTimeout(l),l=t.setTimeout(b,o[i.selectedScrollSnap()]),a=new Date().getTime(),i.emit("autoplay:timerset")}(),u=!0}}function m(){s||(u&&i.emit("autoplay:stop"),function(){let{ownerWindow:t}=i.internalEngine();t.clearTimeout(l),l=0,a=null,i.emit("autoplay:timerstopped")}(),u=!1)}function f(){if(g())return d=u,m();d&&p()}function g(){let{ownerDocument:t}=i.internalEngine();return"hidden"===t.visibilityState}function y(){h||m()}function v(){h||p()}function x(){h=!0,m()}function w(){h=!1,p()}function b(){let{index:t}=i.internalEngine(),n=t.clone().add(1).get(),r=i.scrollSnapList().length-1,s=e.stopOnLastSnap&&n===r;if(i.canScrollNext()?i.scrollNext(c):i.scrollTo(0,c),i.emit("autoplay:select"),s)return m();p()}return{name:"autoplay",options:t,init:function(a,l){i=a;let{mergeOptions:u,optionsAtMedia:h}=l,d=u(n,r.globalOptions);if(e=h(u(d,t)),i.scrollSnapList().length<=1)return;c=e.jump,s=!1,o=function(t,e){let i=t.scrollSnapList();return"number"==typeof e?i.map(()=>e):e(i,t)}(i,e.delay);let{eventStore:g,ownerDocument:b}=i.internalEngine(),T=!!i.internalEngine().options.watchDrag,P=function(t,e){let i=t.rootNode();return e&&e(i)||i}(i,e.rootNode);g.add(b,"visibilitychange",f),T&&i.on("pointerDown",y),T&&!e.stopOnInteraction&&i.on("pointerUp",v),e.stopOnMouseEnter&&g.add(P,"mouseenter",x),e.stopOnMouseEnter&&!e.stopOnInteraction&&g.add(P,"mouseleave",w),e.stopOnFocusIn&&i.on("slideFocusStart",m),e.stopOnFocusIn&&!e.stopOnInteraction&&g.add(i.containerNode(),"focusout",p),e.playOnInit&&p()},destroy:function(){i.off("pointerDown",y).off("pointerUp",v).off("slideFocusStart",m),m(),s=!0,u=!1},play:function(t){void 0!==t&&(c=t),p()},stop:function(){u&&m()},reset:function(){u&&p()},isPlaying:function(){return u},timeUntilNext:function(){return a?o[i.selectedScrollSnap()]-(new Date().getTime()-a):null}}}r.globalOptions=void 0},88090:function(t,e,i){let n;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,n){if("function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}return e}function a(t,e,i){let n=t.getProps();return o(n,e,void 0!==i?i:n.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{E:function(){return sS}});let u=t=>t,h={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],c={value:null,addProjectionMetrics:null};function p(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=d.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&r?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(a=t,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),e&&c.value&&c.value.frameloop[e].push(l),l=0,i.clear(),r=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:p,update:m,preRender:f,render:g,postRender:y}=o,v=()=>{let s=h.useManualTiming?r.timestamp:performance.now();i=!1,h.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),p.process(r),m.process(r),f.process(r),g.process(r),y.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(v))},x=()=>{i=!0,n=!0,r.isProcessing||t(v)};return{schedule:d.reduce((t,e)=>{let n=o[e];return t[e]=(t,e=!1,r=!1)=>(i||x(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<d.length;e++)o[d[e]].cancel(t)},state:r,steps:o}}let{schedule:m,cancel:f,state:g,steps:y}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(v),w=new Set(["width","height","top","left","right","bottom",...v]);function b(t,e){-1===t.indexOf(e)&&t.push(e)}function T(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class P{constructor(){this.subscriptions=[]}add(t){return b(this.subscriptions,t),()=>T(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function S(){n=void 0}let A={now:()=>(void 0===n&&A.set(g.isProcessing||h.useManualTiming?g.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(S)}},M=t=>!isNaN(parseFloat(t)),C={current:void 0};class E{constructor(t,e={}){this.version="12.8.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=A.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=A.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=M(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new P);let i=this.events[t].add(e);return"change"===t?()=>{i(),m.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return C.current&&C.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=A.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function V(t,e){return new E(t,e)}let D=t=>Array.isArray(t),R=t=>!!(t&&t.getVelocity);function k(t,e){let i=t.getValue("willChange");if(R(i)&&i.add)return i.add(e);if(!i&&h.WillChange){let i=new h.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let L=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),j="data-"+L("framerAppearId"),F=(t,e)=>i=>e(t(i)),B=(...t)=>t.reduce(F),O=(t,e,i)=>i>e?e:i<t?t:i,Z=t=>1e3*t,I=t=>t/1e3,$={layout:0,mainThread:0,waapi:0},N=()=>{},U=()=>{},W=t=>e=>"string"==typeof e&&e.startsWith(t),z=W("--"),H=W("var(--"),Y=t=>!!H(t)&&X.test(t.split("/*")[0].trim()),X=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,_={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},q={..._,transform:t=>O(0,1,t)},K={..._,default:1},G=t=>Math.round(1e5*t)/1e5,J=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,Q=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&Q.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(J);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ti=t=>O(0,255,t),tn={..._,transform:t=>Math.round(ti(t))},tr={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tn.transform(t)+", "+tn.transform(e)+", "+tn.transform(i)+", "+G(q.transform(n))+")"},ts={test:tt("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:tr.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=to("deg"),tl=to("%"),tu=to("px"),th=to("vh"),td=to("vw"),tc={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tp={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+tl.transform(G(e))+", "+tl.transform(G(i))+", "+G(q.transform(n))+")"},tm={test:t=>tr.test(t)||ts.test(t)||tp.test(t),parse:t=>tr.test(t)?tr.parse(t):tp.test(t)?tp.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tr.transform(t):tp.transform(t)},tf=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tg="number",ty="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tx(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(tv,t=>(tm.test(t)?(n.color.push(s),r.push(ty),i.push(tm.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tg),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function tw(t){return tx(t).values}function tb(t){let{split:e,types:i}=tx(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tg?r+=G(t[s]):e===ty?r+=tm.transform(t[s]):r+=t[s]}return r}}let tT=t=>"number"==typeof t?0:t,tP={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(J)?.length||0)+(t.match(tf)?.length||0)>0},parse:tw,createTransformer:tb,getAnimatableNone:function(t){let e=tw(t);return tb(t)(e.map(tT))}};function tS(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tA(t,e){return i=>i>0?e:t}let tM=(t,e,i)=>t+(e-t)*i,tC=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},tE=[ts,tr,tp],tV=t=>tE.find(e=>e.test(t));function tD(t){let e=tV(t);if(N(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tp&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=tS(a,n,t+1/3),s=tS(a,n,t),o=tS(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let tR=(t,e)=>{let i=tD(t),n=tD(e);if(!i||!n)return tA(t,e);let r={...i};return t=>(r.red=tC(i.red,n.red,t),r.green=tC(i.green,n.green,t),r.blue=tC(i.blue,n.blue,t),r.alpha=tM(i.alpha,n.alpha,t),tr.transform(r))},tk=new Set(["none","hidden"]);function tL(t,e){return i=>tM(t,e,i)}function tj(t){return"number"==typeof t?tL:"string"==typeof t?Y(t)?tA:tm.test(t)?tR:tO:Array.isArray(t)?tF:"object"==typeof t?tm.test(t)?tR:tB:tA}function tF(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>tj(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function tB(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=tj(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tO=(t,e)=>{let i=tP.createTransformer(e),n=tx(t),r=tx(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?tk.has(t)&&!r.values.length||tk.has(e)&&!n.values.length?tk.has(t)?i=>i<=0?t:e:i=>i>=1?e:t:B(tF(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(n,r),r.values),i):(N(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tA(t,e))};function tZ(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tM(t,e,i):tj(t)(t,e)}let tI=t=>{let e=({timestamp:e})=>t(e);return{start:()=>m.update(e,!0),stop:()=>f(e),now:()=>g.isProcessing?g.timestamp:A.now()}},t$=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=t(e/(r-1))+", ";return`linear(${n.substring(0,n.length-2)})`};function tN(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tU(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let tW={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tz(t,e){return t*Math.sqrt(1-e*e)}let tH=["duration","bounce"],tY=["stiffness","damping","mass"];function tX(t,e){return e.some(e=>void 0!==t[e])}function t_(t=tW.visualDuration,e=tW.bounce){let i;let n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tW.velocity,stiffness:tW.stiffness,damping:tW.damping,mass:tW.mass,isResolvedFromDuration:!1,...t};if(!tX(t,tY)&&tX(t,tH)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*O(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:tW.mass,stiffness:n,damping:r}}else{let i=function({duration:t=tW.duration,bounce:e=tW.bounce,velocity:i=tW.velocity,mass:n=tW.mass}){let r,s;N(t<=Z(tW.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=O(tW.minDamping,tW.maxDamping,o),t=O(tW.minDuration,tW.maxDuration,I(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/tz(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=tz(Math.pow(e,2),o);return(n*i+i-s)*Math.exp(-n)*(-r(e)+.001>0?-1:1)/a}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=Z(t),isNaN(a))return{stiffness:tW.stiffness,damping:tW.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:tW.mass}).isResolvedFromDuration=!0}}return e}({...n,velocity:-I(n.velocity||0)}),f=p||0,g=h/(2*Math.sqrt(u*d)),y=a-o,v=I(Math.sqrt(u/d)),x=5>Math.abs(y);if(r||(r=x?tW.restSpeed.granular:tW.restSpeed.default),s||(s=x?tW.restDelta.granular:tW.restDelta.default),g<1){let t=tz(v,g);i=e=>a-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),n=Math.min(t*e,300);return a-i*((f+g*v*y)*Math.sinh(n)+t*y*Math.cosh(n))/t}}let w={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let n=0===t?f:0;g<1&&(n=0===t?Z(f):tU(i,t,e));let o=Math.abs(n)<=r,u=Math.abs(a-e)<=s;l.done=o&&u}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(tN(w),2e4),e=t$(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function tq({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,y=i*e,v=p+y,x=void 0===o?v:o(v);x!==v&&(y=x-p);let w=t=>-y*Math.exp(-t/n),b=t=>x+w(t),T=t=>{let e=w(t),i=b(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},P=t=>{f(m.value)&&(d=t,c=t_({keyframes:[m.value,g(m.value)],velocity:tU(b,t,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,T(t),P(t)),void 0!==d&&t>=d)?c.next(t-d):(e||T(t),m)}}}t_.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(tN(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:I(r)}}(t,100,t_);return t.ease=e.ease,t.duration=Z(e.duration),t.type="keyframes",t};let tK=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tG(t,e,i,n){if(t===e&&i===n)return u;let r=e=>(function(t,e,i,n,r){let s,o;let a=0;do(s=tK(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tK(r(t),e,n)}let tJ=tG(.42,0,1,1),tQ=tG(0,0,.58,1),t0=tG(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t3=t=>e=>1-t(1-e),t5=tG(.33,1.53,.69,.99),t4=t3(t5),t9=t2(t4),t6=t=>(t*=2)<1?.5*t4(t):.5*(2-Math.pow(2,-10*(t-1))),t7=t=>1-Math.sin(Math.acos(t)),t8=t3(t7),et=t2(t7),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:u,easeIn:tJ,easeInOut:t0,easeOut:tQ,circIn:t7,circInOut:et,circOut:t8,backIn:t4,backInOut:t9,backOut:t5,anticipate:t6},en=t=>"string"==typeof t,er=t=>{if(ee(t)){U(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return tG(e,i,n,r)}return en(t)?(U(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},es=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function eo({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){let r=t1(n)?n.map(er):er(n),s={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if(U(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],r=i||h.mix||tZ,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=B(Array.isArray(e)?e[i]||u:e,s)),n.push(s)}return n}(e,n,r),l=a.length,d=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=es(t[n],t[n+1],i);return a[n](r)};return i?e=>d(O(t[0],t[s-1],e)):d}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=es(0,e,n);t.push(tM(i,1,r))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(r)?r:e.map(()=>r||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=o(e),s.done=e>=t,s)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},n,r=1){let s=t.filter(ea),o=r<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let eu={decay:tq,inertia:tq,tween:eo,keyframes:eo,spring:t_};function eh(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ed{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ec=t=>t/100;class ep extends ed{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;if(t&&t.updatedAt!==A.now()&&this.tick(A.now()),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()},$.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eh(t);let{type:e=eo,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=B(ec,tZ(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=tN(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(x=s)),v=O(0,1,i)*o}let w=y?{done:!1,value:u[0]}:x.next(v);r&&(w.value=r(w.value));let{done:b}=w;y||null===a||(b=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return T&&p!==tq&&(w.value=el(u,this.options,f,this.speed)),m&&m(w.value),T&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return I(this.calculatedDuration)}get time(){return I(this.currentTime)}set time(t){t=Z(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(A.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=I(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tI,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let n=this.driver.now();null!==this.holdTime?this.startTime=n-this.holdTime:"finished"===this.state?(this.updateFinished(),this.startTime=n):this.startTime||(this.startTime=i??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(A.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,$.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),t.observe(this)}}let em=t=>180*t/Math.PI,ef=t=>ey(em(Math.atan2(t[1],t[0]))),eg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ef,rotateZ:ef,skewX:t=>em(Math.atan(t[1])),skewY:t=>em(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ey=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ex=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ew={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:ex,scale:t=>(ev(t)+ex(t))/2,rotateX:t=>ey(em(Math.atan2(t[6],t[5]))),rotateY:t=>ey(em(Math.atan2(-t[2],t[0]))),rotateZ:ef,rotate:ef,skewX:t=>em(Math.atan(t[4])),skewY:t=>em(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eb(t){return t.includes("scale")?1:0}function eT(t,e){let i,n;if(!t||"none"===t)return eb(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=ew,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eg,n=e}if(!n)return eb(e);let s=i[e],o=n[1].split(",").map(eS);return"function"==typeof s?s(o):o[s]}let eP=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eT(i,e)};function eS(t){return parseFloat(t.trim())}let eA=t=>t===_||t===tu,eM=new Set(["x","y","z"]),eC=v.filter(t=>!eM.has(t)),eE={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eT(e,"x"),y:(t,{transform:e})=>eT(e,"y")};eE.translateX=eE.x,eE.translateY=eE.y;let eV=new Set,eD=!1,eR=!1,ek=!1;function eL(){if(eR){let t=Array.from(eV).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eC.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(i.startsWith("scale")?1:0))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eR=!1,eD=!1,eV.forEach(t=>t.complete(ek)),eV.clear()}function ej(){eV.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eR=!0)})}class eF{constructor(t,e,i,n,r,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(eV.add(this),eD||(eD=!0,m.read(ej),m.resolveKeyframes(eL))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eV.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,eV.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let eB=t=>t.startsWith("--");function eO(t){let e;return()=>(void 0===e&&(e=t()),e)}let eZ=eO(()=>void 0!==window.ScrollTimeline),eI={},e$=function(t,e){let i=eO(t);return()=>eI[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eN=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,eU={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eN([0,.65,.55,1]),circOut:eN([.55,0,1,.45]),backIn:eN([.31,.01,.66,-.59]),backOut:eN([.33,1.53,.69,.99])};function eW(t){return"function"==typeof t&&"applyToOptions"in t}class ez extends ed{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,U("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let a=function({type:t,...e}){return eW(t)&&e$()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?e$()?t$(e,i):"ease-out":ee(e)?eN(e):Array.isArray(e)?e.map(e=>t(e,i)||eU.easeOut):eU[e]}(a,r);Array.isArray(d)&&(h.easing=d),c.value&&$.waapi++;let p={delay:n,duration:r,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(p.pseudoElement=u);let m=t.animate(h,p);return c.value&&m.finished.finally(()=>{$.waapi--}),m}(e,i,n,a,r),!1===a.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=el(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):eB(i)?e.style.setProperty(i,t):e.style[i]=t,this.animation.cancel()}this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return I(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return I(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=Z(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eZ())?(this.animation.timeline=t,u):e(this)}}let eH={anticipate:t6,backInOut:t9,circInOut:et};class eY extends ez{constructor(t){"string"==typeof t.ease&&t.ease in eH&&(t.ease=eH[t.ease]),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t){e.set(t);return}let o=new ep({...s,autoplay:!1}),a=Z(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eX=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tP.test(t)||"0"===t)&&!t.startsWith("url(")),e_=new Set(["opacity","clipPath","filter","transform"]),eq=eO(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eK extends ed{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation?(this._animation.stop(),this.stopTimeline?.()):this.keyframeResolver?.cancel()},this.createdAt=A.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},c=u?.KeyframeResolver||eF;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:d,onComplete:c}=i;this.resolvedAt=A.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eX(r,e),a=eX(s,e);return N(o===a,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eW(i))&&n)}(t,r,s,o)&&((h.instantAnimations||!a)&&d?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let p={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},m=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eq()&&i&&e_.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(p)?new eY({...p,element:p.motionValue.owner.current}):new ep(p);m.finished.then(()=>{c?.(),this.notifyFinished()}).catch(u),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(ek=!0,ej(),eL(),ek=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this.animation.cancel()}}let eG=t=>null!==t,eJ={type:"spring",stiffness:500,damping:25,restSpeed:10},eQ=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e0={type:"keyframes",duration:.8},e1={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e2=(t,{keyframes:e})=>e.length>2?e0:x.has(t)?t.startsWith("scale")?eQ(e[1]):eJ:e1,e3=(t,e,i,n={},r,s)=>o=>{let a=l(n,t)||{},u=a.delay||n.delay||0,{elapsed:d=0}=n;d-=Z(u);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-d,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(c,e2(t,c)),c.duration&&(c.duration=Z(c.duration)),c.repeatDelay&&(c.repeatDelay=Z(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0!==c.delay||(p=!0)),(h.instantAnimations||h.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,p&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(eG),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(c.keyframes,a);if(void 0!==t){m.update(()=>{c.onUpdate(t),c.onComplete()});return}}return new eK(c)};function e5(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...u}=e;n&&(s=n);let h=[],d=r&&t.animationState&&t.animationState.getState()[r];for(let e in u){let n=t.getValue(e,t.latestValues[e]??null),r=u[e];if(void 0===r||d&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(d,e))continue;let o={delay:i,...l(s||{},e)},a=!1;if(window.MotionHandoffAnimation){let i=t.props[j];if(i){let t=window.MotionHandoffAnimation(i,e,m);null!==t&&(o.startTime=t,a=!0)}}k(t,e),n.start(e3(e,n,r,t.shouldReduceMotion&&w.has(e)?{type:!1}:o,t,a));let c=n.animation;c&&h.push(c)}return o&&Promise.all(h).then(()=>{m.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=a(t,e)||{};for(let e in r={...r,...i}){var s;let i=D(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,V(i))}}(t,o)})}),h}function e4(t,e,i={}){let n=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(e5(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,r=1,s){let o=[],a=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(e9).forEach((t,n)=>{t.notify("AnimationStart",e),o.push(e4(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+n,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[s,o]:[o,s];return t().then(()=>e())}}function e9(t,e){return t.sortNodePosition(e)}function e6(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function e7(t){return"string"==typeof t||Array.isArray(t)}let e8=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],it=["initial",...e8],ie=it.length,ii=[...e8].reverse(),ir=e8.length;function is(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function io(){return{animate:is(!0),whileInView:is(),whileHover:is(),whileTap:is(),whileDrag:is(),whileFocus:is(),exit:is()}}class ia{constructor(t){this.isMounted=!1,this.node=t}update(){}}class il extends ia{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>e4(t,e,i)));else if("string"==typeof e)n=e4(t,e,i);else{let r="function"==typeof e?a(t,e,i.custom):e;n=Promise.all(e5(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=io(),n=!0,s=e=>(i,n)=>{let r=a(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ie;t++){let n=it[t],r=e.props[n];(e7(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},h=[],d=new Set,c={},p=1/0;for(let e=0;e<ir;e++){var m;let a=ii[e],f=i[a],g=void 0!==l[a]?l[a]:u[a],y=e7(g),v=a===o?f.isActive:null;!1===v&&(p=e);let x=g===u[a]&&g!==l[a]&&y;if(x&&n&&t.manuallyAnimateOnMount&&(x=!1),f.protectedKeys={...c},!f.isActive&&null===v||!g&&!f.prevProp||r(g)||"boolean"==typeof g)continue;let w=(m=f.prevProp,"string"==typeof g?g!==m:!!Array.isArray(g)&&!e6(g,m)),b=w||a===o&&f.isActive&&!x&&y||e>p&&y,T=!1,P=Array.isArray(g)?g:[g],S=P.reduce(s(a),{});!1===v&&(S={});let{prevResolvedValues:A={}}=f,M={...A,...S},C=e=>{b=!0,d.has(e)&&(T=!0,d.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=S[t],i=A[t];if(!c.hasOwnProperty(t))(D(e)&&D(i)?e6(e,i):e===i)?void 0!==e&&d.has(t)?C(t):f.protectedKeys[t]=!0:null!=e?C(t):d.add(t)}f.prevProp=g,f.prevResolvedValues=S,f.isActive&&(c={...c,...S}),n&&t.blockInitialAnimation&&(b=!1);let E=!(x&&w)||T;b&&E&&h.push(...P.map(t=>({animation:t,options:{type:a}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),h.push({animation:e})}let f=!!h.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(f=!1),n=!1,f?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=o(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=io(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let iu=0;class ih extends ia{constructor(){super(...arguments),this.id=iu++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let id={x:!1,y:!1};function ic(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let ip=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function im(t){return{point:{x:t.pageX,y:t.pageY}}}let ig=t=>e=>ip(e)&&t(e,im(e));function iy(t,e,i,n){return ic(t,e,ig(i),n)}function iv({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function ix(t){return t.max-t.min}function iw(t,e,i,n=.5){t.origin=n,t.originPoint=tM(e.min,e.max,t.origin),t.scale=ix(i)/ix(e),t.translate=tM(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function ib(t,e,i,n){iw(t.x,e.x,i.x,n?n.originX:void 0),iw(t.y,e.y,i.y,n?n.originY:void 0)}function iT(t,e,i){t.min=i.min+e.min,t.max=t.min+ix(e)}function iP(t,e,i){t.min=e.min-i.min,t.max=t.min+ix(e)}function iS(t,e,i){iP(t.x,e.x,i.x),iP(t.y,e.y,i.y)}let iA=()=>({translate:0,scale:1,origin:0,originPoint:0}),iM=()=>({x:iA(),y:iA()}),iC=()=>({min:0,max:0}),iE=()=>({x:iC(),y:iC()});function iV(t){return[t("x"),t("y")]}function iD(t){return void 0===t||1===t}function iR({scale:t,scaleX:e,scaleY:i}){return!iD(t)||!iD(e)||!iD(i)}function ik(t){return iR(t)||iL(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iL(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function ij(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function iF(t,e=0,i=1,n,r){t.min=ij(t.min,e,i,n,r),t.max=ij(t.max,e,i,n,r)}function iB(t,{x:e,y:i}){iF(t.x,e.translate,e.scale,e.originPoint),iF(t.y,i.translate,i.scale,i.originPoint)}function iO(t,e){t.min=t.min+e,t.max=t.max+e}function iZ(t,e,i,n,r=.5){let s=tM(t.min,t.max,r);iF(t,e,i,s,n)}function iI(t,e){iZ(t.x,e.x,e.scaleX,e.scale,e.originX),iZ(t.y,e.y,e.scaleY,e.scale,e.originY)}function i$(t,e){return iv(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let iN=({current:t})=>t?t.ownerDocument.defaultView:null;function iU(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iW=(t,e)=>Math.abs(t-e);class iz{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{var t,e;if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let i=iX(this.lastMoveEventInfo,this.history),n=null!==this.startEvent,r=(t=i.offset,e={x:0,y:0},Math.sqrt(iW(t.x,e.x)**2+iW(t.y,e.y)**2)>=3);if(!n&&!r)return;let{point:s}=i,{timestamp:o}=g;this.history.push({...s,timestamp:o});let{onStart:a,onMove:l}=this.handlers;n||(a&&a(this.lastMoveEvent,i),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,i)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iH(e,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iX("pointercancel"===t.type?this.lastMoveEventInfo:iH(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!ip(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let s=iH(im(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=g;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iX(s,this.history)),this.removeListeners=B(iy(this.contextWindow,"pointermove",this.handlePointerMove),iy(this.contextWindow,"pointerup",this.handlePointerUp),iy(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function iH(t,e){return e?{point:e(t.point)}:t}function iY(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iX({point:t},e){return{point:t,delta:iY(t,i_(e)),offset:iY(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=i_(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>Z(.1)));)i--;if(!n)return{x:0,y:0};let s=I(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function i_(t){return t[t.length-1]}function iq(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iK(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function iG(t,e,i){return{min:iJ(t,e),max:iJ(t,i)}}function iJ(t,e){return"number"==typeof t?t:t[e]||0}let iQ=new WeakMap;class i0{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iE(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new iz(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(im(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===i||"y"===i?id[i]?null:(id[i]=!0,()=>{id[i]=!1}):id.x||id.y?null:(id.x=id.y=!0,()=>{id.x=id.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iV(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];if(n){let t=ix(n);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),r&&m.postRender(()=>r(t,e)),k(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iV(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:iN(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&m.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!i1(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?tM(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?tM(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&iU(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:iq(t.x,i,r),y:iq(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iG(t,"left","right"),y:iG(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iV(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iU(e))return!1;let n=e.current;U(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=i$(t,i),{scroll:r}=e;return r&&(iO(n.x,r.offset.x),iO(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o={x:iK((t=r.layout.layoutBox).x,s.x),y:iK(t.y,s.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iv(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iV(o=>{if(!i1(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return k(this.visualElement,t),i.start(e3(t,i,0,e,this.visualElement,!1))}stopAnimation(){iV(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iV(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iV(e=>{let{drag:i}=this.getProps();if(!i1(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-tM(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iU(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iV(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=ix(t),r=ix(e);return r>n?i=es(e.min,e.max-n,t.min):n>r&&(i=es(t.min,t.max-r,e.min)),O(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iV(e=>{if(!i1(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(tM(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;iQ.set(this.visualElement,this);let t=iy(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iU(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.read(e);let r=ic(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iV(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function i1(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i2 extends ia{constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new i0(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let i3=t=>(e,i)=>{t&&m.postRender(()=>t(e,i))};class i5 extends ia{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(t){this.session=new iz(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iN(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:i3(t),onStart:i3(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&m.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=iy(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i4,i9,i6,i7=i(57437);let{schedule:i8,cancel:nt}=p(queueMicrotask,!1);var ne=i(2265);let ni=(0,ne.createContext)(null),nn=(0,ne.createContext)({}),nr=(0,ne.createContext)({}),ns={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function no(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let na={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!tu.test(t))return t;t=parseFloat(t)}let i=no(t,e.target.x),n=no(t,e.target.y);return`${i}% ${n}%`}},nl={};class nu extends ne.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;!function(t){for(let e in t)nl[e]=t[e],z(e)&&(nl[e].isCSSVariable=!0)}(nd),r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),ns.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,s=i.projection;return s&&(s.isPresent=r,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent===r||(r?s.promote():s.relegate()||m.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),i8.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nh(t){let[e,i]=function(t=!0){let e=(0,ne.useContext)(ni);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:n,register:r}=e,s=(0,ne.useId)();(0,ne.useEffect)(()=>{if(t)return r(s)},[t]);let o=(0,ne.useCallback)(()=>t&&n&&n(s),[s,n,t]);return!i&&n?[!1,o]:[!0]}(),n=(0,ne.useContext)(nn);return(0,i7.jsx)(nu,{...t,layoutGroup:n,switchLayoutGroup:(0,ne.useContext)(nr),isPresent:e,safeToRemove:i})}let nd={borderRadius:{...na,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:na,borderTopRightRadius:na,borderBottomLeftRadius:na,borderBottomRightRadius:na,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tP.parse(t);if(n.length>5)return t;let r=tP.createTransformer(t),s="number"!=typeof n[0]?1:0,o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=tM(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}},nc=(t,e)=>t.depth-e.depth;class np{constructor(){this.children=[],this.isDirty=!1}add(t){b(this.children,t),this.isDirty=!0}remove(t){T(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nc),this.isDirty=!1,this.children.forEach(t)}}function nm(t){return R(t)?t.get():t}let nf=["TopLeft","TopRight","BottomLeft","BottomRight"],ng=nf.length,ny=t=>"string"==typeof t?parseFloat(t):t,nv=t=>"number"==typeof t||tu.test(t);function nx(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nw=nT(0,.5,t8),nb=nT(.5,.95,u);function nT(t,e,i){return n=>n<t?0:n>e?1:i(es(t,e,n))}function nP(t,e){t.min=e.min,t.max=e.max}function nS(t,e){nP(t.x,e.x),nP(t.y,e.y)}function nA(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nM(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nC(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(tl.test(e)&&(e=parseFloat(e),e=tM(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tM(s.min,s.max,n);t===s&&(a-=e),t.min=nM(t.min,e,i,a,r),t.max=nM(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let nE=["x","scaleX","originX"],nV=["y","scaleY","originY"];function nD(t,e,i,n){nC(t.x,e,nE,i?i.x:void 0,n?n.x:void 0),nC(t.y,e,nV,i?i.y:void 0,n?n.y:void 0)}function nR(t){return 0===t.translate&&1===t.scale}function nk(t){return nR(t.x)&&nR(t.y)}function nL(t,e){return t.min===e.min&&t.max===e.max}function nj(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nF(t,e){return nj(t.x,e.x)&&nj(t.y,e.y)}function nB(t){return ix(t.x)/ix(t.y)}function nO(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nZ{constructor(){this.members=[]}add(t){b(this.members,t),t.scheduleRender()}remove(t){if(T(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nI={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},n$=["","X","Y","Z"],nN={visibility:"hidden"},nU=0;function nW(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nz({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=nU++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,c.value&&(nI.nodes=nI.calculatedTargetDeltas=nI.calculatedProjections=0),this.nodes.forEach(nX),this.nodes.forEach(n0),this.nodes.forEach(n1),this.nodes.forEach(n_),c.addProjectionMetrics&&c.addProjectionMetrics(nI)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new np)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new P),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:n,layout:r,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(r||n)&&(this.isLayoutDirty=!0),t){let i;let n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=A.now(),n=({timestamp:e})=>{let r=e-i;r>=250&&(f(n),t(r-250))};return m.setup(n,!0),()=>f(n)}(n,0),ns.hasAnimatedSinceResize&&(ns.hasAnimatedSinceResize=!1,this.nodes.forEach(nQ))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&s&&(n||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||s.getDefaultTransition()||n6,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),u=!this.targetLayout||!nF(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,h);let e={...l(r,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||nQ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n2),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[j];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",m,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nK);return}this.isUpdating||this.nodes.forEach(nG),this.isUpdating=!1,this.nodes.forEach(nJ),this.nodes.forEach(nH),this.nodes.forEach(nY),this.clearAllSnapshots();let t=A.now();g.delta=O(0,1e3/60,t-g.timestamp),g.timestamp=t,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i8.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nq),this.sharedNodes.forEach(n3)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||ix(this.snapshot.measuredBox.x)||ix(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iE(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nk(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&(e||ik(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),rt((e=n).x),rt(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iE();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(ri))){let{scroll:t}=this.root;t&&(iO(e.x,t.offset.x),iO(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iE();if(nS(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nS(e,t),iO(e.x,r.offset.x),iO(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=iE();nS(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iI(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),ik(n.latestValues)&&iI(i,n.latestValues)}return ik(this.latestValues)&&iI(i,this.latestValues),i}removeTransform(t){let e=iE();nS(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!ik(i.latestValues))continue;iR(i.latestValues)&&i.updateSnapshot();let n=iE();nS(n,i.measurePageBox()),nD(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return ik(this.latestValues)&&nD(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iE(),this.relativeTargetOrigin=iE(),iS(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nS(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iE(),this.targetWithTransforms=iE()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,iT(s.x,o.x,a.x),iT(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nS(this.target,this.layout.layoutBox),iB(this.target,this.targetDelta)):nS(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iE(),this.relativeTargetOrigin=iE(),iS(this.relativeTargetOrigin,this.target,t.target),nS(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}c.value&&nI.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||iR(this.parent.latestValues)||iL(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===g.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;nS(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,n=!1){let r,s;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iI(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iB(t,s)),n&&ik(r.latestValues)&&iI(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iE());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nA(this.prevProjectionDelta.x,this.projectionDelta.x),nA(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),ib(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nO(this.projectionDelta.x,this.prevProjectionDelta.x)&&nO(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),c.value&&nI.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iM(),this.projectionDelta=iM(),this.projectionDeltaWithTransform=iM()}setAnimationOrigin(t,e=!1){let i;let n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=iM();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iE(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(n9));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(n5(o.x,t.x,n),n5(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m;iS(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,n4(p.x,m.x,a.x,n),n4(p.y,m.y,a.y,n),i&&(u=this.relativeTarget,c=i,nL(u.x,c.x)&&nL(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iE()),nS(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=tM(0,i.opacity??1,nw(n)),t.opacityExit=tM(e.opacity??1,0,nb(n))):s&&(t.opacity=tM(e.opacity??1,i.opacity??1,n));for(let r=0;r<ng;r++){let s=`border${nf[r]}Radius`,o=nx(e,s),a=nx(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nv(o)===nv(a)?(t[s]=Math.max(tM(ny(o),ny(a),n),0),(tl.test(a)||tl.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=tM(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{ns.hasAnimatedSinceResize=!0,$.layout++,this.currentAnimation=function(t,e,i){let n=R(0)?0:V(0);return n.start(e3("",n,1e3,i)),n.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{$.layout--},onComplete:()=>{$.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&re(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iE();let e=ix(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=ix(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nS(e,i),iI(e,r),ib(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nZ),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&nW("z",t,n,this.animationValues);for(let e=0;e<n$.length;e++)nW(`rotate${n$[e]}`,t,n,this.animationValues),nW(`skew${n$[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return nN;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=nm(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nm(t?.pointerEvents)||""),this.hasProjected&&!ik(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let r=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(e.transform=i(r,e.transform));let{x:s,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,n.animationValues?e.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,nl){if(void 0===r[t])continue;let{correct:i,applyTo:s,isCSSVariable:o}=nl[t],a="none"===e.transform?r[t]:i(r[t],n);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=n===this?nm(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(nK),this.root.sharedNodes.clear()}}}function nH(t){t.updateLayout()}function nY(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?iV(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=ix(n);n.min=i[t].min,n.max=n.min+r}):re(r,e.layoutBox,i)&&iV(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],o=ix(i[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=iM();ib(o,i,e.layoutBox);let a=iM();s?ib(a,t.applyTransform(n,!0),e.measuredBox):ib(a,i,e.layoutBox);let l=!nk(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=iE();iS(o,e.layoutBox,r.layoutBox);let a=iE();iS(a,i,s.layoutBox),nF(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nX(t){c.value&&nI.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function n_(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nq(t){t.clearSnapshot()}function nK(t){t.clearMeasurements()}function nG(t){t.isLayoutDirty=!1}function nJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nQ(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function n0(t){t.resolveTargetDelta()}function n1(t){t.calcProjection()}function n2(t){t.resetSkewAndRotation()}function n3(t){t.removeLeadSnapshot()}function n5(t,e,i){t.translate=tM(e.translate,0,i),t.scale=tM(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function n4(t,e,i,n){t.min=tM(e.min,i.min,n),t.max=tM(e.max,i.max,n)}function n9(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let n6={duration:.45,ease:[.4,0,.1,1]},n7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),n8=n7("applewebkit/")&&!n7("chrome/")?Math.round:u;function rt(t){t.min=n8(t.min),t.max=n8(t.max)}function re(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nB(e)-nB(i)))}function ri(t){return t!==t.root&&t.scroll?.wasRoot}let rn=nz({attachResizeListener:(t,e)=>ic(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rr={current:void 0},rs=nz({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rr.current){let t=new rn({});t.mount(window),t.setOptions({layoutScroll:!0}),rr.current=t}return rr.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ro(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function ra(t){return!("touch"===t.pointerType||id.x||id.y)}function rl(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&m.postRender(()=>r(e,im(e)))}class ru extends ia{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ro(t,i),o=t=>{if(!ra(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{ra(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(rl(this.node,e,"Start"),t=>rl(this.node,t,"End"))))}unmount(){}}class rh extends ia{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=B(ic(this.node.current,"focus",()=>this.onFocus()),ic(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rd=(t,e)=>!!e&&(t===e||rd(t,e.parentElement)),rc=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rp=new WeakSet;function rm(t){return e=>{"Enter"===e.key&&t(e)}}function rf(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let rg=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=rm(()=>{if(rp.has(i))return;rf(i,"down");let t=rm(()=>{rf(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>rf(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function ry(t){return ip(t)&&!(id.x||id.y)}function rv(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&m.postRender(()=>r(e,im(e)))}class rx extends ia{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ro(t,i),o=t=>{let n=t.currentTarget;if(!ry(t)||rp.has(n))return;rp.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ry(t)&&rp.has(n)&&(rp.delete(n),"function"==typeof s&&s(t,{success:e}))},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||rd(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),t instanceof HTMLElement&&(t.addEventListener("focus",t=>rg(t,r)),rc.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(rv(this.node,e,"Start"),(t,{success:e})=>rv(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rw=new WeakMap,rb=new WeakMap,rT=t=>{let e=rw.get(t.target);e&&e(t)},rP=t=>{t.forEach(rT)},rS={some:0,all:1};class rA extends ia{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rS[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rb.has(i)||rb.set(i,{});let n=rb.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(rP,{root:t,...e})),n[r]}(e);return rw.set(t,i),n.observe(t),()=>{rw.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rM=(0,ne.createContext)({strict:!1}),rC=(0,ne.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),rE=(0,ne.createContext)({});function rV(t){return r(t.animate)||it.some(e=>e7(t[e]))}function rD(t){return!!(rV(t)||t.variants)}function rR(t){return Array.isArray(t)?t.join(" "):t}let rk="undefined"!=typeof window,rL={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rj={};for(let t in rL)rj[t]={isEnabled:e=>rL[t].some(t=>!!e[t])};let rF=Symbol.for("motionComponentSymbol"),rB=rk?ne.useLayoutEffect:ne.useEffect;function rO(t,{layout:e,layoutId:i}){return x.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!nl[t]||"opacity"===t)}let rZ=(t,e)=>e&&"number"==typeof t?e.transform(t):t,rI={..._,transform:Math.round},r$={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:K,scaleX:K,scaleY:K,scaleZ:K,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:q,originX:tc,originY:tc,originZ:tu,zIndex:rI,fillOpacity:q,strokeOpacity:q,numOctaves:rI},rN={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rU=v.length;function rW(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(x.has(t)){o=!0;continue}if(z(t)){r[t]=i;continue}{let e=rZ(i,r$[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<rU;s++){let o=v[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a))||i){let t=rZ(a,r$[o]);if(!l){r=!1;let e=rN[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin=`${t} ${e} ${i}`}}let rz=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rH(t,e,i){for(let n in e)R(e[n])||rO(n,i)||(t[n]=e[n])}let rY=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function rX(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rY.has(t)}let r_=t=>!rX(t);try{(i4=require("@emotion/is-prop-valid").default)&&(r_=t=>t.startsWith("on")?!rX(t):i4(t))}catch{}let rq=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function rK(t){if("string"!=typeof t||t.includes("-"));else if(rq.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let rG={offset:"stroke-dashoffset",array:"stroke-dasharray"},rJ={offset:"strokeDashoffset",array:"strokeDasharray"};function rQ(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u){if(rW(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox="fill-box",delete h.transformBox),void 0!==e&&(h.x=e),void 0!==i&&(h.y=i),void 0!==n&&(h.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?rG:rJ;t[s.offset]=tu.transform(-n);let o=tu.transform(e),a=tu.transform(i);t[s.array]=`${o} ${a}`}(h,r,s,o,!1)}let r0=()=>({...rz(),attrs:{}}),r1=t=>"string"==typeof t&&"svg"===t.toLowerCase(),r2=t=>(e,i)=>{let n=(0,ne.useContext)(rE),s=(0,ne.useContext)(ni),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,s){return{latestValues:function(t,e,i,n){let s={},a=n(t,{});for(let t in a)s[t]=nm(a[t]);let{initial:l,animate:u}=t,h=rV(t),d=rD(t);e&&d&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!r(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=o(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,n,s,t),renderState:e()}})(t,e,n,s);return i?a():function(t){let e=(0,ne.useRef)(null);return null===e.current&&(e.current=t()),e.current}(a)};function r3(t,e,i){let{style:n}=t,r={};for(let s in n)(R(n[s])||e.style&&R(e.style[s])||rO(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}let r5={useVisualState:r2({scrapeMotionValuesFromProps:r3,createRenderState:rz})};function r4(t,e,i){let n=r3(t,e,i);for(let i in t)(R(t[i])||R(e[i]))&&(n[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let r9={useVisualState:r2({scrapeMotionValuesFromProps:r4,createRenderState:r0})},r6=t=>e=>e.test(t),r7=[_,tu,tl,ta,td,th,{test:t=>"auto"===t,parse:t=>t}],r8=t=>r7.find(r6(t)),st=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),se=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,si=t=>/^0[^.\s]+$/u.test(t),sn=new Set(["brightness","contrast","saturate","opacity"]);function sr(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(J)||[];if(!n)return t;let r=i.replace(n,""),s=sn.has(e)?1:0;return n!==i&&(s*=100),e+"("+s+r+")"}let ss=/\b([a-z-]*)\(.*?\)/gu,so={...tP,getAnimatableNone:t=>{let e=t.match(ss);return e?e.map(sr).join(" "):t}},sa={...r$,color:tm,backgroundColor:tm,outlineColor:tm,fill:tm,stroke:tm,borderColor:tm,borderTopColor:tm,borderRightColor:tm,borderBottomColor:tm,borderLeftColor:tm,filter:so,WebkitFilter:so},sl=t=>sa[t];function su(t,e){let i=sl(t);return i!==so&&(i=tP),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let sh=new Set(["auto","none","0"]);class sd extends eF{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&Y(n=n.trim())){let r=function t(e,i,n=1){U(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=se.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return st(t)?parseFloat(t):t}return Y(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!w.has(i)||2!==t.length)return;let[n,r]=t,s=r8(n),o=r8(r);if(s!==o){if(eA(s)&&eA(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||si(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!sh.has(e)&&tx(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=su(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eE[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=eE[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sc=[...r7,tm,tP],sp=t=>sc.find(r6(t)),sm={current:null},sf={current:!1},sg=new WeakMap,sy=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sv{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eF,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=A.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,m.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rV(e),this.isVariantNode=rD(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&R(e)&&e.set(a[t],!1)}}mount(t){this.current=t,sg.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sf.current||function(){if(sf.current=!0,rk){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sm.current=t.matches;t.addListener(e),e()}else sm.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sm.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=x.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&m.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rj){let e=rj[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iE()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sy.length;e++){let i=sy[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(R(r))t.addValue(n,r);else if(R(s))t.addValue(n,V(r,{owner:t}));else if(s!==r){if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,V(void 0!==e?e:r,{owner:t}))}}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=V(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(st(i)||si(i))?i=parseFloat(i):!sp(i)&&tP.test(e)&&(i=su(t,e)),this.setBaseTarget(t,R(i)?i.get():i)),R(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e;let{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=o(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||R(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new P),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sx extends sv{constructor(){super(...arguments),this.KeyframeResolver=sd}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;R(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sw(t,{style:e,vars:i},n,r){for(let s in Object.assign(t.style,e,r&&r.getProjectionStyles(n)),i)t.style.setProperty(s,i[s])}class sb extends sx{constructor(){super(...arguments),this.type="html",this.renderInstance=sw}readValueFromInstance(t,e){if(x.has(e))return eP(t,e);{let i=window.getComputedStyle(t),n=(z(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i$(t,e)}build(t,e,i){rW(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r3(t,e,i)}}let sT=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sP extends sx{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iE}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(x.has(e)){let t=sl(e);return t&&t.default||0}return e=sT.has(e)?e:L(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r4(t,e,i)}build(t,e,i){rQ(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,n){!function(t,e,i,n){for(let i in sw(t,e,void 0,n),e.attrs)t.setAttribute(sT.has(i)?i:L(i),e.attrs[i])}(t,e,0,n)}mount(t){this.isSVGTag=r1(t.tagName),super.mount(t)}}let sS=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((i9={animation:{Feature:il},exit:{Feature:ih},inView:{Feature:rA},tap:{Feature:rx},focus:{Feature:rh},hover:{Feature:ru},pan:{Feature:i5},drag:{Feature:i2,ProjectionNode:rs,MeasureLayout:nh},layout:{ProjectionNode:rs,MeasureLayout:nh}},i6=(t,e)=>rK(t)?new sP(e):new sb(e,{allowProjection:t!==ne.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){let{preloadedFeatures:e,createVisualElement:i,useRender:n,useVisualState:r,Component:s}=t;function o(t,e){var o;let a;let l={...(0,ne.useContext)(rC),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,ne.useContext)(nn).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:u}=l,h=function(t){let{initial:e,animate:i}=function(t,e){if(rV(t)){let{initial:e,animate:i}=t;return{initial:!1===e||e7(e)?e:void 0,animate:e7(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,ne.useContext)(rE));return(0,ne.useMemo)(()=>({initial:e,animate:i}),[rR(e),rR(i)])}(t),d=r(t,u);if(!u&&rk){(0,ne.useContext)(rM).strict;let t=function(t){let{drag:e,layout:i}=rj;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(l);a=t.MeasureLayout,h.visualElement=function(t,e,i,n,r){let{visualElement:s}=(0,ne.useContext)(rE),o=(0,ne.useContext)(rM),a=(0,ne.useContext)(ni),l=(0,ne.useContext)(rC).reducedMotion,u=(0,ne.useRef)(null);n=n||o.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:s,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,d=(0,ne.useContext)(nr);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&iU(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,r,d);let c=(0,ne.useRef)(!1);(0,ne.useInsertionEffect)(()=>{h&&c.current&&h.update(i,a)});let p=i[j],m=(0,ne.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return rB(()=>{h&&(c.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),i8.render(h.render),m.current&&h.animationState&&h.animationState.animateChanges())}),(0,ne.useEffect)(()=>{h&&(!m.current&&h.animationState&&h.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),h}(s,d,l,i,t.ProjectionNode)}return(0,i7.jsxs)(rE.Provider,{value:h,children:[a&&h.visualElement?(0,i7.jsx)(a,{visualElement:h.visualElement,...l}):null,n(s,t,(o=h.visualElement,(0,ne.useCallback)(t=>{t&&d.onMount&&d.onMount(t),o&&(t?o.mount(t):o.unmount()),e&&("function"==typeof e?e(t):iU(e)&&(e.current=t))},[o])),d,u,h.visualElement)]})}e&&function(t){for(let e in t)rj[e]={...rj[e],...t[e]}}(e),o.displayName=`motion.${"string"==typeof s?s:`create(${s.displayName??s.name??""})`}`;let a=(0,ne.forwardRef)(o);return a[rF]=s,a}({...rK(t)?r9:r5,preloadedFeatures:i9,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let o=(rK(e)?function(t,e,i,n){let r=(0,ne.useMemo)(()=>{let i=r0();return rQ(i,e,r1(n),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};rH(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return rH(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,ne.useMemo)(()=>{let i=rz();return rW(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,e),a=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(r_(r)||!0===i&&rX(r)||!e&&!rX(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),l=e!==ne.Fragment?{...a,...o,ref:n}:{},{children:u}=i,h=(0,ne.useMemo)(()=>R(u)?u.get():u,[u]);return(0,ne.createElement)(e,{...l,children:h})}}(e),createVisualElement:i6,Component:t})}))}}]);