"use client";
import {
  Container,
  Grid,
  AccordionSummary,
  AccordionDetails,
  Accordion,
} from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import SvgexpandIcon from "../../assets/images/icons/arrowUp.svg";

import SvgCallendar from "@/assets/images/icons/yellow/yellowCalendar.svg";

export default function MoroccoLaborLaws() {
  const [expanded, setExpanded] = useState(1);
  const { t } = useTranslation();
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  return (
    <div id="labor-tn-laws">
      <Container  className="custom-max-width">
        <h2 className="heading-h1">{t("morocco:MoroccoLabor:title")}</h2>
        <Grid className="container" container columnSpacing={3} rowSpacing={2}>
          <Grid item xs={12} sm={12} key={1}>
            <Accordion
              elevation={0}
              expanded={expanded === 1}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(1)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("morocco:MoroccoLabor:workingHours:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("morocco:MoroccoLabor:workingHours:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:workingHours:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("morocco:MoroccoLabor:workingHours:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:workingHours:description2")}
                  </p>
                </div>
                <ul className="service-description paragraph">
                  <li>{t("morocco:MoroccoLabor:workingHours:data1")}</li>
                  <li>{t("morocco:MoroccoLabor:workingHours:data2")}</li>
                  <li>{t("morocco:MoroccoLabor:workingHours:data3")}</li>
                  <ul className="service-description paragraph">
                    <li>{t("morocco:MoroccoLabor:workingHours:item1")}</li>
                    <li>{t("morocco:MoroccoLabor:workingHours:item2")}</li>
                    <li>{t("morocco:MoroccoLabor:workingHours:item3")}</li>
                  </ul>
                  <li>{t("morocco:MoroccoLabor:workingHours:data4")}</li>
                </ul>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={2}>
            <Accordion
              elevation={0}
              expanded={expanded === 2}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(2)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("morocco:MoroccoLabor:employmentContracts:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:employmentContracts:description")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>
                      {t("morocco:MoroccoLabor:employmentContracts:data1")}
                    </li>
                    <li>
                      {t("morocco:MoroccoLabor:employmentContracts:data2")}
                    </li>
                    <li>
                      {t("morocco:MoroccoLabor:employmentContracts:data3")}
                    </li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={3}>
            <Accordion
              elevation={0}
              expanded={expanded === 3}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(3)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("morocco:MoroccoLabor:termination:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("morocco:MoroccoLabor:termination:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:termination:description1")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("morocco:MoroccoLabor:termination:data1")}</li>
                    <li>{t("morocco:MoroccoLabor:termination:data2")}</li>
                    <li>{t("morocco:MoroccoLabor:termination:data3")}</li>
                    <li>{t("morocco:MoroccoLabor:termination:data4")}</li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("morocco:MoroccoLabor:termination:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:termination:description2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:termination:data5")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("morocco:MoroccoLabor:termination:item1")}</li>
                    <li>{t("morocco:MoroccoLabor:termination:item2")}</li>
                    <li>{t("morocco:MoroccoLabor:termination:item3")}</li>
                  </ul>
                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:termination:data6")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("morocco:MoroccoLabor:termination:item4")}</li>
                    <li>{t("morocco:MoroccoLabor:termination:item5")}</li>
                    <li>{t("morocco:MoroccoLabor:termination:item6")}</li>
                  </ul>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t("morocco:MoroccoLabor:termination:title3")}
                  </p>
                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:termination:description3")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("morocco:MoroccoLabor:termination:data7")}</li>
                    <li>{t("morocco:MoroccoLabor:termination:data8")}</li>
                    <li>{t("morocco:MoroccoLabor:termination:data9")}</li>
                    <li>{t("morocco:MoroccoLabor:termination:data10")}</li>
                  </ul>
                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:termination:description4")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={4}>
            <Accordion
              elevation={0}
              expanded={expanded === 4}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(4)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("morocco:MoroccoLabor:payroll:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("morocco:MoroccoLabor:payroll:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:payroll:description")}
                  </p>
                </div>
                <div className="payroll-tn">
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("morocco:MoroccoLabor:payroll:fiscalYear:title")}
                    </p>
                    <p className="date">
                      {t("morocco:MoroccoLabor:payroll:fiscalYear:date1")}
                      <br />
                      {t("morocco:MoroccoLabor:payroll:fiscalYear:date2")}
                    </p>
                    <p className="paragraph">
                      {t("morocco:MoroccoLabor:payroll:fiscalYear:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("morocco:MoroccoLabor:payroll:payrollCycle:title")}
                    </p>
                    <p className="date">
                      {t("morocco:MoroccoLabor:payroll:payrollCycle:date")}
                    </p>
                    <p className="paragraph">
                      {t(
                        "morocco:MoroccoLabor:payroll:payrollCycle:description"
                      )}
                    </p>
                  </div>

                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("morocco:MoroccoLabor:payroll:minimumWage:title")}
                    </p>
                    <p className="date">
                      {t("morocco:MoroccoLabor:payroll:minimumWage:wage")}
                      <br />
                      {t("morocco:MoroccoLabor:payroll:minimumWage:date")}​
                    </p>
                    <p className="paragraph">
                      {t(
                        "morocco:MoroccoLabor:payroll:minimumWage:description"
                      )}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t(
                        "morocco:MoroccoLabor:payroll:payrollManagement:title"
                      )}
                    </p>
                    <p className="date">
                      {t(
                        "morocco:MoroccoLabor:payroll:payrollManagement:date1"
                      )}
                      <br />
                      {t(
                        "morocco:MoroccoLabor:payroll:payrollManagement:date2"
                      )}
                    </p>
                    <p className="paragraph">
                      {t(
                        "morocco:MoroccoLabor:payroll:payrollManagement:description"
                      )}
                    </p>
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={5}>
            <Accordion
              elevation={0}
              expanded={expanded === 5}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(5)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("morocco:MoroccoLabor:leaveEntitlements:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("morocco:MoroccoLabor:leaveEntitlements:description")}
                  </p>
                  <div className="holidays-dates">
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS1:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS1:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS2:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS2:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS3:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS3:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS4:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS4:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS5:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS5:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS6:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS6:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS7:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS7:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS8:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS8:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS9:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS9:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS10:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS10:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS11:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS11:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS12:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS12:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS13:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS13:title"
                        )}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "morocco:MoroccoLabor:leaveEntitlements:leaves:annualLeave:title"
                    )}
                  </p>
                  <ul className="service-description paragraph">
                    <li>
                      {t(
                        "morocco:MoroccoLabor:leaveEntitlements:leaves:annualLeave:description1"
                      )}
                    </li>
                    <li>
                      {t(
                        "morocco:MoroccoLabor:leaveEntitlements:leaves:annualLeave:description2"
                      )}
                    </li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "morocco:MoroccoLabor:leaveEntitlements:leaves:maternityLeave:title"
                    )}
                  </p>
                  <ul className="service-description paragraph">
                    <li>
                      {t(
                        "morocco:MoroccoLabor:leaveEntitlements:leaves:maternityLeave:description1"
                      )}
                    </li>
                    <li>
                      {t(
                        "morocco:MoroccoLabor:leaveEntitlements:leaves:maternityLeave:description2"
                      )}
                    </li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "morocco:MoroccoLabor:leaveEntitlements:leaves:paternityLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "morocco:MoroccoLabor:leaveEntitlements:leaves:paternityLeave:description"
                    )}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "morocco:MoroccoLabor:leaveEntitlements:leaves:sickLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "morocco:MoroccoLabor:leaveEntitlements:leaves:sickLeave:description"
                    )}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "morocco:MoroccoLabor:leaveEntitlements:leaves:marrigageLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "morocco:MoroccoLabor:leaveEntitlements:leaves:marrigageLeave:description"
                    )}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "morocco:MoroccoLabor:leaveEntitlements:leaves:bereavementLeave:title"
                    )}
                  </p>
                  <p className="service-description paragraph">
                    {t(
                      "morocco:MoroccoLabor:leaveEntitlements:leaves:bereavementLeave:description"
                    )}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={6}>
            <Accordion
              elevation={0}
              expanded={expanded === 6}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(6)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("morocco:MoroccoLabor:tax:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:tax:description")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("morocco:MoroccoLabor:tax:title1")}
                  </p>

                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:tax:description1")}
                  </p>

                  <ul className="service-description paragraph">
                    <li> {t("morocco:MoroccoLabor:tax:data1")}</li>
                    <li> {t("morocco:MoroccoLabor:tax:data2")}</li>
                  </ul>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t("morocco:MoroccoLabor:tax:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:tax:description2")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("morocco:MoroccoLabor:tax:dataS1")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>

          <Grid item xs={12} sm={12} key={7}>
            <Accordion
              elevation={0}
              expanded={expanded === 7}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(7)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("morocco:MoroccoLabor:visa:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:visa:description1")}
                    <br />
                    {t("morocco:MoroccoLabor:visa:description2")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("morocco:MoroccoLabor:visa:title1")}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t("morocco:MoroccoLabor:visa:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("morocco:MoroccoLabor:visa:description3")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("morocco:MoroccoLabor:visa:title3")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("morocco:MoroccoLabor:visa:data1")}</li>
                    <li>{t("morocco:MoroccoLabor:visa:data2")}</li>
                    <li>{t("morocco:MoroccoLabor:visa:data3")}</li>
                    <li>{t("morocco:MoroccoLabor:visa:data4")}</li>
                    <li>{t("morocco:MoroccoLabor:visa:data5")}</li>
                    <li>{t("morocco:MoroccoLabor:visa:data6")}</li>
                    <li>{t("morocco:MoroccoLabor:visa:data7")}</li>
                    <li>{t("morocco:MoroccoLabor:visa:data8")}</li>
                    <li>{t("morocco:MoroccoLabor:visa:data9")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}
