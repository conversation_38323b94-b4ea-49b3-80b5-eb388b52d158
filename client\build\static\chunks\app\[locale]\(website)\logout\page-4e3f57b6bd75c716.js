(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9020],{1611:function(e,t,r){Promise.resolve().then(r.bind(r,53530))},3146:function(e,t,r){"use strict";r.d(t,{F4:function(){return d},iv:function(){return p},xB:function(){return f}});var n,o,i=r(25246),s=r(2265),a=r(32820),c=r(24006),u=r(29896);r(78242),r(63285);var l=function(e,t){var r=arguments;if(null==t||!i.h.call(t,"css"))return s.createElement.apply(void 0,r);var n=r.length,o=Array(n);o[0]=i.E,o[1]=(0,i.c)(e,t);for(var a=2;a<n;a++)o[a]=r[a];return s.createElement.apply(null,o)};n=l||(l={}),o||(o=n.JSX||(n.JSX={}));var f=(0,i.w)(function(e,t){var r=e.styles,n=(0,u.O)([r],void 0,s.useContext(i.T)),o=s.useRef();return(0,c.j)(function(){var e=t.key+"-global",r=new t.sheet.constructor({key:e,nonce:t.sheet.nonce,container:t.sheet.container,speedy:t.sheet.isSpeedy}),i=!1,s=document.querySelector('style[data-emotion="'+e+" "+n.name+'"]');return t.sheet.tags.length&&(r.before=t.sheet.tags[0]),null!==s&&(i=!0,s.setAttribute("data-emotion",e),r.hydrate([s])),o.current=[r,i],function(){r.flush()}},[t]),(0,c.j)(function(){var e=o.current,r=e[0];if(e[1]){e[1]=!1;return}if(void 0!==n.next&&(0,a.My)(t,n.next,!0),r.tags.length){var i=r.tags[r.tags.length-1].nextElementSibling;r.before=i,r.flush()}t.insert("",n,r,!1)},[t,n.name]),null});function p(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,u.O)(t)}function d(){var e=p.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}},35389:function(e,t,r){"use strict";r.d(t,{default:function(){return $}});var n=r(2265),o=r(61994),i=r(20801),s=r(3146),a=r(16210),c=r(76301),u=r(37053),l=r(85657),f=r(3858),p=r(94143),d=r(50738);function y(e){return(0,d.ZP)("MuiCircularProgress",e)}(0,p.Z)("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);var m=r(57437);let h=(0,s.F4)`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,v=(0,s.F4)`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,g="string"!=typeof h?(0,s.iv)`
        animation: ${h} 1.4s linear infinite;
      `:null,b="string"!=typeof v?(0,s.iv)`
        animation: ${v} 1.4s ease-in-out infinite;
      `:null,S=e=>{let{classes:t,variant:r,color:n,disableShrink:o}=e,s={root:["root",r,`color${(0,l.Z)(n)}`],svg:["svg"],circle:["circle",`circle${(0,l.Z)(r)}`,o&&"circleDisableShrink"]};return(0,i.Z)(s,y,t)},P=(0,a.ZP)("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],t[`color${(0,l.Z)(r.color)}`]]}})((0,c.Z)(e=>{let{theme:t}=e;return{display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:t.transitions.create("transform")}},{props:{variant:"indeterminate"},style:g||{animation:`${h} 1.4s linear infinite`}},...Object.entries(t.palette).filter((0,f.Z)()).map(e=>{let[r]=e;return{props:{color:r},style:{color:(t.vars||t).palette[r].main}}})]}})),x=(0,a.ZP)("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,t)=>t.svg})({display:"block"}),w=(0,a.ZP)("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.circle,t[`circle${(0,l.Z)(r.variant)}`],r.disableShrink&&t.circleDisableShrink]}})((0,c.Z)(e=>{let{theme:t}=e;return{stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:t.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:e=>{let{ownerState:t}=e;return"indeterminate"===t.variant&&!t.disableShrink},style:b||{animation:`${v} 1.4s ease-in-out infinite`}}]}}));var $=n.forwardRef(function(e,t){let r=(0,u.i)({props:e,name:"MuiCircularProgress"}),{className:n,color:i="primary",disableShrink:s=!1,size:a=40,style:c,thickness:l=3.6,value:f=0,variant:p="indeterminate",...d}=r,y={...r,color:i,disableShrink:s,size:a,thickness:l,value:f,variant:p},h=S(y),v={},g={},b={};if("determinate"===p){let e=2*Math.PI*((44-l)/2);v.strokeDasharray=e.toFixed(3),b["aria-valuenow"]=Math.round(f),v.strokeDashoffset=`${((100-f)/100*e).toFixed(3)}px`,g.transform="rotate(-90deg)"}return(0,m.jsx)(P,{className:(0,o.Z)(h.root,n),style:{width:a,height:a,...g,...c},ownerState:y,ref:t,role:"progressbar",...b,...d,children:(0,m.jsx)(x,{className:h.svg,ownerState:y,viewBox:"22 22 44 44",children:(0,m.jsx)(w,{className:h.circle,style:v,ownerState:y,cx:44,cy:44,r:(44-l)/2,fill:"none",strokeWidth:l})})})})},3858:function(e,t,r){"use strict";function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t=>{let[,r]=t;return r&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if("string"!=typeof e.main)return!1;for(let r of t)if(!e.hasOwnProperty(r)||"string"!=typeof e[r])return!1;return!0}(r,e)}}r.d(t,{Z:function(){return n}})},76301:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var n=r(31683);let o={theme:void 0};var i=function(e){let t,r;return function(i){let s=t;return(void 0===s||i.theme!==r)&&(o.theme=i.theme,t=s=(0,n.Z)(e(o)),r=i.theme),s}}},94143:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var n=r(50738);function o(e,t,r="Mui"){let o={};return t.forEach(t=>{o[t]=(0,n.ZP)(e,t,r)}),o}},63285:function(e,t,r){"use strict";var n=r(44300),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},s={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},a={};function c(e){return n.isMemo(e)?s:a[e.$$typeof]||o}a[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a[n.Memo]=s;var u=Object.defineProperty,l=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,p=Object.getOwnPropertyDescriptor,d=Object.getPrototypeOf,y=Object.prototype;e.exports=function e(t,r,n){if("string"!=typeof r){if(y){var o=d(r);o&&o!==y&&e(t,o,n)}var s=l(r);f&&(s=s.concat(f(r)));for(var a=c(t),m=c(r),h=0;h<s.length;++h){var v=s[h];if(!i[v]&&!(n&&n[v])&&!(m&&m[v])&&!(a&&a[v])){var g=p(r,v);try{u(t,v,g)}catch(e){}}}}return t}},57618:function(e,t){"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,o=r?Symbol.for("react.portal"):60106,i=r?Symbol.for("react.fragment"):60107,s=r?Symbol.for("react.strict_mode"):60108,a=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,u=r?Symbol.for("react.context"):60110,l=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,p=r?Symbol.for("react.forward_ref"):60112,d=r?Symbol.for("react.suspense"):60113,y=r?Symbol.for("react.suspense_list"):60120,m=r?Symbol.for("react.memo"):60115,h=r?Symbol.for("react.lazy"):60116,v=r?Symbol.for("react.block"):60121,g=r?Symbol.for("react.fundamental"):60117,b=r?Symbol.for("react.responder"):60118,S=r?Symbol.for("react.scope"):60119;function P(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case l:case f:case i:case a:case s:case d:return e;default:switch(e=e&&e.$$typeof){case u:case p:case h:case m:case c:return e;default:return t}}case o:return t}}}function x(e){return P(e)===f}t.AsyncMode=l,t.ConcurrentMode=f,t.ContextConsumer=u,t.ContextProvider=c,t.Element=n,t.ForwardRef=p,t.Fragment=i,t.Lazy=h,t.Memo=m,t.Portal=o,t.Profiler=a,t.StrictMode=s,t.Suspense=d,t.isAsyncMode=function(e){return x(e)||P(e)===l},t.isConcurrentMode=x,t.isContextConsumer=function(e){return P(e)===u},t.isContextProvider=function(e){return P(e)===c},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return P(e)===p},t.isFragment=function(e){return P(e)===i},t.isLazy=function(e){return P(e)===h},t.isMemo=function(e){return P(e)===m},t.isPortal=function(e){return P(e)===o},t.isProfiler=function(e){return P(e)===a},t.isStrictMode=function(e){return P(e)===s},t.isSuspense=function(e){return P(e)===d},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===f||e===a||e===s||e===d||e===y||"object"==typeof e&&null!==e&&(e.$$typeof===h||e.$$typeof===m||e.$$typeof===c||e.$$typeof===u||e.$$typeof===p||e.$$typeof===g||e.$$typeof===b||e.$$typeof===S||e.$$typeof===v)},t.typeOf=P},44300:function(e,t,r){"use strict";e.exports=r(57618)},99376:function(e,t,r){"use strict";var n=r(35475);r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},40257:function(e,t,r){"use strict";var n,o;e.exports=(null==(n=r.g.process)?void 0:n.env)&&"object"==typeof(null==(o=r.g.process)?void 0:o.env)?r.g.process:r(44227)},44227:function(e){!function(){var t={229:function(e){var t,r,n,o=e.exports={};function i(){throw Error("setTimeout has not been defined")}function s(){throw Error("clearTimeout has not been defined")}function a(e){if(t===setTimeout)return setTimeout(e,0);if((t===i||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:i}catch(e){t=i}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(e){r=s}}();var c=[],u=!1,l=-1;function f(){u&&n&&(u=!1,n.length?c=n.concat(c):l=-1,c.length&&p())}function p(){if(!u){var e=a(f);u=!0;for(var t=c.length;t;){for(n=c,c=[];++l<t;)n&&n[l].run();l=-1,t=c.length}n=null,u=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function d(e,t){this.fun=e,this.array=t}function y(){}o.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];c.push(new d(e,t)),1!==c.length||u||a(p)},d.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=y,o.addListener=y,o.once=y,o.off=y,o.removeListener=y,o.removeAllListeners=y,o.emit=y,o.prependListener=y,o.prependOnceListener=y,o.listeners=function(e){return[]},o.binding=function(e){throw Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw Error("process.chdir is not supported")},o.umask=function(){return 0}}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var i=r[e]={exports:{}},s=!0;try{t[e](i,i.exports,n),s=!1}finally{s&&delete r[e]}return i.exports}n.ab="//";var o=n(229);e.exports=o}()},53530:function(e,t,r){"use strict";r.r(t);var n=r(57437),o=r(2265),i=r(99376),s=r(7261),a=r(93214),c=r(46172),u=r(52499);t.default=()=>{let e=(0,i.useRouter)();return(0,o.useEffect)(()=>{(async()=>{try{await a.yX.post(c.Y.logout),window.location.href="/"}catch(e){s.Am.error("Logout failed",e)}})()},[e]),(0,n.jsx)(u.Z,{})}},52499:function(e,t,r){"use strict";var n=r(57437),o=r(35389);t.Z=function(){return(0,n.jsx)("div",{className:"spinner",children:(0,n.jsx)(o.default,{})})}},93214:function(e,t,r){"use strict";r.d(t,{cU:function(){return a},xk:function(){return s},yX:function(){return i}});var n=r(83464),o=r(40257);let i=n.Z.create({baseURL:o.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),s=n.Z.create({baseURL:o.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),a=n.Z.create({baseURL:o.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"});n.Z.create({baseURL:o.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))},46172:function(e,t,r){"use strict";r.d(t,{Y:function(){return o},v:function(){return n}});let n=r(40257).env.NEXT_PUBLIC_BASE_API_URL,o={seo:"seoTags",auth:"/auth/signin",logout:"/auth/logout",candidatures:"/applications",signup:"/auth/signup",forgetPassword:"/auth/forgot-password",resetPassword:"/auth/reset-password",guides:"/guides",currentUser:"/users/current",updateUser:"/users",users:"/users",categoryGuides:"guidecategory",candidate:"/candidates",report:"/report",skills:"/skills",files:"/files",applications:"/applications",sliders:"/sliders",favoris:"/candidate/favourite",articles:"/articles",categories:"/categories",blog:"/blog",category:"/categories",opportunity:"/opportunities",seoOpportunity:"/seoOpportunity",newsletter:"/newsletter",contact:"/contact",favourite:"/favourite",contacts:"contacts",comments:"/comments",statistics:"/statistics",events:"/events",baseUrl:`${n}`}}},function(e){e.O(0,[775,948,3464,7261,2971,2117,1744],function(){return e(e.s=1611)}),_N_E=e.O()}]);