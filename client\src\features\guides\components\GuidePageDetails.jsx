"use client";

import { useEffect, useState } from "react";
import moment from "moment";
import { Container, Grid, useTheme, useMediaQuery } from "@mui/material";
import "moment/locale/fr";
import { useTranslation } from "react-i18next";

import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import ArticleContent from "../../blog/components/ArticleContent";
import Uploadguide from "./Uploadguide";

function GuidePageDetails({ id, guide, language, fileName, Name, cible }) {
  const theme = useTheme();
  const { user } = useCurrentUser();
  const { i18n } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  moment.locale(i18n.language || "en");

  const [modifiedHtmlContent, setModifiedHtmlContent] = useState(
    guide?.content
  );

  useEffect(() => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(guide?.content, "text/html");
    const extractedHeadings = [];
    Array.from(doc.querySelectorAll("h2, h3")).forEach((heading) => {
      const id = heading.innerText
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[^a-z0-9\-]/g, "");
      heading.id = id;
      extractedHeadings.push({
        tagName: heading.tagName.toLowerCase(),
        content: heading.innerText,
        id,
      });
    });
    setModifiedHtmlContent(doc.body.innerHTML);
  }, [guide?.content]);

  return (
      <div id="blog-page-details">
        <div id="guide-header">
          <Container className="custom-max-width">
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                {(guide?.categoryguides?.length > 0 ||
                  guide?.categories?.length > 0) && (
                  <div className="categories-list">
                    {[
                      ...(guide.categoryguides || []),
                      ...(guide.categories || []),
                    ].map((category) => {
                      const isCategoryGuide = guide.categoryguides?.some(
                        (cg) => cg.id === category.id
                      );
                      const href = isCategoryGuide
                        ? `${
                            language === "en"
                              ? `/blog/category/${category.url}`
                              : `/${language}/blog/category/${category.url}`
                          }/`
                        : `/${language}/blog/category/${category.url}`;
                      return (
                        category?.url && (
                          <span className="category light" key={category.url}>
                            <a href={href}>{category.name}</a>
                          </span>
                        )
                      );
                    })}
                  </div>
                )}
              </Grid>
            </Grid>
          </Container>
        </div>
        <Container className="custom-max-width">
          <Grid className="container" container columnSpacing={2}>
            <Grid item xs={12} sm={6}>
              <div className="blog-content">
                <ArticleContent htmlContent={modifiedHtmlContent} />
              </div>
            </Grid>
            <Grid item xs={12} sm={5} id="sticky-sidebar-guide">
              {!isMobile ? (
                  <Uploadguide
                    user={user}
                    fileName={fileName}
                    cible={cible}
                    Name={Name}
                    id={id}
                  />
              ) : null}
            </Grid>
          </Grid>
          <Grid className="container" container columnSpacing={2}>
            <Grid item xs={12} sm={8}>
              {isMobile ? (
                  <Uploadguide
                    user={user}
                    fileName={fileName}
                    cible={cible}
                    Name={Name}
                    id={id}
                  />
              ) : null}
            </Grid>
          </Grid>
        </Container>
      </div>
  );
}

export default GuidePageDetails;
