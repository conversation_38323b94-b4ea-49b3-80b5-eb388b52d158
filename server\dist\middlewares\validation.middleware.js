"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkCurrentUserForResponse = exports.checkCurrentUserForComment = exports.validateParams = exports.validateUUID = void 0;
exports.validationMiddleware = validationMiddleware;
exports.validationWithFileMiddleware = validationWithFileMiddleware;
const commentaire_validations_1 = require("@/apis/article/commentaire/commentaire.validations");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const messages_1 = require("@/utils/helpers/messages");
function validationMiddleware(schema) {
    return async (request, response, next) => {
        const validationOptions = {
            abortEarly: false,
            allowUnknown: true,
            stripUnknown: true,
        };
        try {
            const value = await schema.validateAsync(request.body, validationOptions);
            request.body = value;
            next();
        }
        catch (error) {
            const errors = [];
            error.details.forEach((error) => {
                errors.push(error.message);
            });
            response.status(500).send(errors);
        }
    };
}
function validationWithFileMiddleware(schema) {
    return async (request, response, next) => {
        const validationOptions = {
            abortEarly: false,
            allowUnknown: true,
            stripUnknown: true,
        };
        const candidatObjectFromUpload = request.file?.buffer.toString();
        if (candidatObjectFromUpload === undefined) {
            response.status(400).send({ error: 'Upload file field is empty' });
            return;
        }
        try {
            const value = await schema.validateAsync(JSON.parse(candidatObjectFromUpload), validationOptions);
            request.body = value;
            next();
        }
        catch (e) {
            const errors = [];
            e.details.forEach((error) => {
                errors.push(error.message);
            });
            response.status(500).send(errors);
        }
    };
}
const validateUUID = (request, response, next) => {
    const regexExp = /^[0-9a-f]{32}$/gi;
    const { filename } = request.params;
    const uuid = filename.split('.')[0];
    if (!regexExp.test(uuid)) {
        return next(new http_exception_1.default(400, messages_1.MESSAGES.FILE.INVALID_UUID_FORMAT));
    }
    next();
};
exports.validateUUID = validateUUID;
const validateParams = (request, response, next) => {
    const { resource, folder } = request.params;
    const year = parseInt(folder, 10);
    if (isNaN(year) || year.toString() !== folder) {
        return next(new http_exception_1.default(404, 'Invalid folder format'));
    }
    const currentYear = new Date().getFullYear();
    if (year <= 2005 || year > currentYear) {
        return next(new http_exception_1.default(404, 'Invalid folder format'));
    }
    if (!resource ||
        (resource !== 'users' && resource !== 'candidates' && resource !== 'clients' && resource !== 'blogs' && resource !== 'categories' && resource !== 'sliders' && resource !== 'events')) {
        return next(new http_exception_1.default(404, messages_1.MESSAGES.FILE.INVALID_RESSOURCE));
    }
    next();
};
exports.validateParams = validateParams;
const checkCurrentUserForComment = async (request, response, next) => {
    request.user ? validationMiddleware(commentaire_validations_1.authenticatedCommentSchema) : validationMiddleware(commentaire_validations_1.unauthenticatedCommentSchema);
    next();
};
exports.checkCurrentUserForComment = checkCurrentUserForComment;
const checkCurrentUserForResponse = async (request, response, next) => {
    request.user ? validationMiddleware(commentaire_validations_1.authenticatedResponseSchema) : validationMiddleware(commentaire_validations_1.unauthenticatedResponseSchema);
    next();
};
exports.checkCurrentUserForResponse = checkCurrentUserForResponse;
//# sourceMappingURL=validation.middleware.js.map