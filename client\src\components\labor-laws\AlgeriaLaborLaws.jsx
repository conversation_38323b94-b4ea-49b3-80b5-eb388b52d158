"use client";
import {
  Container,
  Grid,
  AccordionSummary,
  AccordionDetails,
  Accordion,
} from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import SvgexpandIcon from "../../assets/images/icons/arrowUp.svg";

import SvgCallendar from "@/assets/images/icons/yellow/yellowCalendar.svg";

export default function AlgeriaLaborLaws() {
  const [expanded, setExpanded] = useState(1);
  const { t } = useTranslation();
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  return (
    <div id="labor-tn-laws">
      <Container className="custom-max-width">
        <h2 className="heading-h1">{t("Algeria:AlgeriaLabor:title")}</h2>

        <Grid className="container" container columnSpacing={3} rowSpacing={2}>
          <Grid item xs={12} sm={12} key={1}>
            <Accordion
              elevation={0}
              expanded={expanded === 1}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(1)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Algeria:AlgeriaLabor:workingHours:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Algeria:AlgeriaLabor:workingHours:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Algeria:AlgeriaLabor:workingHours:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Algeria:AlgeriaLabor:workingHours:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Algeria:AlgeriaLabor:workingHours:description2")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={2}>
            <Accordion
              elevation={0}
              expanded={expanded === 2}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(2)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Algeria:AlgeriaLabor:employmentContracts:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <p className="service-description paragraph">
                  {t("Algeria:AlgeriaLabor:employmentContracts:description1")}
                </p>
                <p className="service-description paragraph">
                  {t("Algeria:AlgeriaLabor:employmentContracts:description2")}
                </p>
                <div className="item">
                  <ul className="service-description paragraph">
                    <li>
                      {t("Algeria:AlgeriaLabor:employmentContracts:data1")}
                      <ul>
                        <li>
                          {" "}
                          {t("Algeria:AlgeriaLabor:employmentContracts:data2")}
                        </li>
                        <li>
                          {" "}
                          {t("Algeria:AlgeriaLabor:employmentContracts:data3")}
                        </li>
                        <li>
                          {" "}
                          {t("Algeria:AlgeriaLabor:employmentContracts:data4")}
                        </li>
                      </ul>
                    </li>
                    <li>
                      {t("Algeria:AlgeriaLabor:employmentContracts:data5")}
                      <ul>
                        <li>
                          {" "}
                          {t("Algeria:AlgeriaLabor:employmentContracts:data6")}
                        </li>
                        <li>
                          {" "}
                          {t("Algeria:AlgeriaLabor:employmentContracts:data7")}
                        </li>
                        <li>
                          {" "}
                          {t("Algeria:AlgeriaLabor:employmentContracts:data8")}
                        </li>
                      </ul>
                    </li>
                  </ul>
                </div>
                {/* <div className="item">
                  <p className="service-sub-title">
                    {t("Algeria:AlgeriaLabor:employmentContracts:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Algeria:AlgeriaLabor:employmentContracts:description")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>
                      {t("Algeria:AlgeriaLabor:employmentContracts:dataS1")}
                    </li>
                    <li>
                      {t("Algeria:AlgeriaLabor:employmentContracts:dataS2")}
                    </li>
                  </ul>
                </div> */}
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={3}>
            <Accordion
              elevation={0}
              expanded={expanded === 3}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(3)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Algeria:AlgeriaLabor:termination:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Algeria:AlgeriaLabor:termination:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Algeria:AlgeriaLabor:termination:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Algeria:AlgeriaLabor:termination:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Algeria:AlgeriaLabor:termination:description2")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Algeria:AlgeriaLabor:termination:title3")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Algeria:AlgeriaLabor:termination:description3")}{" "}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("Algeria:AlgeriaLabor:termination:data1")}</li>
                    <li>{t("Algeria:AlgeriaLabor:termination:data2")}</li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Algeria:AlgeriaLabor:termination:title4")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Algeria:AlgeriaLabor:termination:description4")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={4}>
            <Accordion
              elevation={0}
              expanded={expanded === 4}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(4)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Algeria:AlgeriaLabor:payroll:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Algeria:AlgeriaLabor:payroll:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Algeria:AlgeriaLabor:payroll:description")}
                  </p>
                </div>
                <div className="payroll-tn">
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("Algeria:AlgeriaLabor:payroll:fiscalYear:title")}
                    </p>
                    <p className="date">
                      {t("Algeria:AlgeriaLabor:payroll:fiscalYear:date1")}
                      <br />
                      {t("Algeria:AlgeriaLabor:payroll:fiscalYear:date2")}
                    </p>
                    <p className="paragraph">
                      {t("Algeria:AlgeriaLabor:payroll:fiscalYear:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("Algeria:AlgeriaLabor:payroll:payrollCycle:title")}
                    </p>
                    <p className="date">
                      {t("Algeria:AlgeriaLabor:payroll:payrollCycle:date")}
                    </p>
                    <p className="paragraph">
                      {t(
                        "Algeria:AlgeriaLabor:payroll:payrollCycle:description"
                      )}
                    </p>
                  </div>

                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("Algeria:AlgeriaLabor:payroll:minimumWage:title")}
                    </p>
                    <p className="date">
                      {t("Algeria:AlgeriaLabor:payroll:minimumWage:wage")}
                      <br />
                      {t("Algeria:AlgeriaLabor:payroll:minimumWage:date")}​
                    </p>
                    <p className="paragraph">
                      {t(
                        "Algeria:AlgeriaLabor:payroll:minimumWage:description"
                      )}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t(
                        "Algeria:AlgeriaLabor:payroll:payrollManagement:title"
                      )}
                    </p>
                    <p className="date">
                      {t(
                        "Algeria:AlgeriaLabor:payroll:payrollManagement:date1"
                      )}
                      <br />
                      {t(
                        "Algeria:AlgeriaLabor:payroll:payrollManagement:date2"
                      )}
                    </p>
                    <p className="paragraph">
                      {t(
                        "Algeria:AlgeriaLabor:payroll:payrollManagement:description"
                      )}
                    </p>
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={5}>
            <Accordion
              elevation={0}
              expanded={expanded === 5}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(5)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Algeria:AlgeriaLabor:leaveEntitlements:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Algeria:AlgeriaLabor:leaveEntitlements:description")}
                  </p>
                  <div className="holidays-dates">
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS1:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS1:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS2:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS2:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS3:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS3:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS4:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS4:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS5:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS5:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS6:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS6:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS7:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS7:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS8:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS8:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS9:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS9:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS10:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS10:title"
                        )}
                      </p>
                    </div>
         
                  </div>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Algeria:AlgeriaLabor:leaveEntitlements:leaves:annualLeave:title"
                    )}
                  </p>
                  <ul className="service-description paragraph">
                    <li>
                      {t(
                        "Algeria:AlgeriaLabor:leaveEntitlements:leaves:annualLeave:description1"
                      )}
                    </li>
                    <li>
                      {t(
                        "Algeria:AlgeriaLabor:leaveEntitlements:leaves:annualLeave:description2"
                      )}
                    </li>
                  </ul>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Algeria:AlgeriaLabor:leaveEntitlements:leaves:maternityLeave:title"
                    )}
                  </p>
                  <ul className="service-description paragraph">
                    {/* <li>
                      {t(
                        "Algeria:AlgeriaLabor:leaveEntitlements:leaves:maternityLeave:description1"
                      )}
                    </li> */}
                    <li>
                      {t(
                        "Algeria:AlgeriaLabor:leaveEntitlements:leaves:maternityLeave:description2"
                      )}
                    </li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Algeria:AlgeriaLabor:leaveEntitlements:leaves:paternityLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "Algeria:AlgeriaLabor:leaveEntitlements:leaves:paternityLeave:description"
                    )}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Algeria:AlgeriaLabor:leaveEntitlements:leaves:sickLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "Algeria:AlgeriaLabor:leaveEntitlements:leaves:sickLeave:description"
                    )}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Algeria:AlgeriaLabor:leaveEntitlements:leaves:parentalLeave:title"
                    )}
                  </p>
                  <p className="service-description paragraph">
                    {t(
                      "Algeria:AlgeriaLabor:leaveEntitlements:leaves:parentalLeave:description"
                    )}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={6}>
            <Accordion
              elevation={0}
              expanded={expanded === 6}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(6)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Algeria:AlgeriaLabor:tax:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("Algeria:AlgeriaLabor:tax:description")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Algeria:AlgeriaLabor:tax:title1")}
                  </p>

                  <p className="service-description paragraph">
                    {t("Algeria:AlgeriaLabor:tax:description1")}
                  </p>

                  <ul className="service-description paragraph">
                    <li> {t("Algeria:AlgeriaLabor:tax:data1")}</li>
                    <li> {t("Algeria:AlgeriaLabor:tax:data2")}</li>
                    <li> {t("Algeria:AlgeriaLabor:tax:data3")}</li>
                    <li> {t("Algeria:AlgeriaLabor:tax:data4")}</li>
                  </ul>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {" "}
                    {t("Algeria:AlgeriaLabor:tax:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("Algeria:AlgeriaLabor:tax:description2")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("Algeria:AlgeriaLabor:tax:dataS1")}</li>
                    <li>{t("Algeria:AlgeriaLabor:tax:dataS2")}</li>
                    <li>{t("Algeria:AlgeriaLabor:tax:dataS3")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>

          <Grid item xs={12} sm={12} key={7}>
            <Accordion
              elevation={0}
              expanded={expanded === 7}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(7)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Algeria:AlgeriaLabor:visa:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("Algeria:AlgeriaLabor:visa:description1")}
                    <br />
                    {t("Algeria:AlgeriaLabor:visa:description2")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Algeria:AlgeriaLabor:visa:title1")}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t("Algeria:AlgeriaLabor:visa:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("Algeria:AlgeriaLabor:visa:description3")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Algeria:AlgeriaLabor:visa:title3")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("Algeria:AlgeriaLabor:visa:data1")}</li>
                    <li>{t("Algeria:AlgeriaLabor:visa:data2")}</li>
                    <li>{t("Algeria:AlgeriaLabor:visa:data3")}</li>
                    <li>{t("Algeria:AlgeriaLabor:visa:data4")}</li>
                    <li>{t("Algeria:AlgeriaLabor:visa:data5")}</li>
                    <li>{t("Algeria:AlgeriaLabor:visa:data6")}</li>
                    <li>{t("Algeria:AlgeriaLabor:visa:data7")}</li>
                    <li>{t("Algeria:AlgeriaLabor:visa:data8")}</li>
                    <li>{t("Algeria:AlgeriaLabor:visa:data9")}</li>
                    <li>{t("Algeria:AlgeriaLabor:visa:data10")}</li>
                    <li>{t("Algeria:AlgeriaLabor:visa:data11")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}
