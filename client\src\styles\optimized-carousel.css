/* Optimized Carousel Styles - Performance & Layout Shift Prevention */

/* Container optimizations */
.embla {
  position: relative;
  overflow: hidden;
  /* Hardware acceleration for smooth animations */
  transform: translateZ(0);
  will-change: transform;
}

.embla__viewport {
  overflow: hidden;
  /* Prevent layout shifts during initialization */
  min-height: inherit;
}

.embla__container {
  display: flex;
  /* Smooth transitions */
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Hardware acceleration */
  transform: translateZ(0);
  will-change: transform;
}

.embla__slide {
  position: relative;
  flex: 0 0 100%;
  min-width: 0;
  /* Prevent layout shifts */
  aspect-ratio: 21/9;
}

/* Mobile responsive aspect ratio */
@media (max-width: 600px) {
  .embla__slide {
    aspect-ratio: 16/9;
  }
}

/* Image container optimizations */
.carousel-image-container {
  /* Prevent layout shifts with consistent sizing */
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
  overflow: hidden !important;
  
  /* Smooth loading transitions */
  background-color: #f5f5f5;
  background-image: linear-gradient(
    90deg,
    #f5f5f5 0%,
    #e8e8e8 50%,
    #f5f5f5 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Shimmer loading animation */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Image optimizations */
.carousel-image-container img {
  /* Prevent layout shifts */
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  object-position: center !important;
  
  /* Smooth loading */
  transition: opacity 0.3s ease-in-out !important;
  
  /* Hardware acceleration */
  transform: translateZ(0);
  will-change: opacity;
}

/* Content overlay optimizations */
.slide__container {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 2 !important;
  
  /* Flexbox for content positioning */
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  
  /* Prevent text selection during transitions */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.embla__slide__content {
  /* Smooth content transitions */
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
  transform: translateZ(0);
  will-change: opacity, transform;
  
  /* Content positioning */
  max-width: 600px;
  padding: 2rem;
  
  /* Text styling */
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Title optimizations */
.embla__slide__title {
  /* Prevent layout shifts */
  margin: 0 0 1.5rem 0 !important;
  padding: 0 !important;
  
  /* Typography */
  font-size: clamp(1.5rem, 4vw, 3rem) !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  
  /* Performance */
  contain: layout style;
  will-change: auto;
}

/* Button optimizations */
.btn-slider,
.explore-btn {
  /* Smooth interactions */
  transition: all 0.2s ease-in-out !important;
  transform: translateZ(0);
  will-change: transform, background-color;
  
  /* Prevent layout shifts */
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  
  /* Interactive states */
  cursor: pointer;
}

.btn-slider:hover,
.explore-btn:hover {
  transform: translateY(-2px) translateZ(0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-slider:active,
.explore-btn:active {
  transform: translateY(0) translateZ(0);
}

/* Controls optimizations */
.embla__controls {
  position: absolute !important;
  bottom: 2rem !important;
  right: 2rem !important;
  z-index: 3 !important;
}

.embla__buttons {
  display: flex !important;
  gap: 0.5rem !important;
}

.embla__button {
  /* Button styling */
  width: 48px !important;
  height: 48px !important;
  border-radius: 50% !important;
  border: none !important;
  background: rgba(255, 255, 255, 0.9) !important;
  color: #333 !important;
  
  /* Smooth interactions */
  transition: all 0.2s ease-in-out !important;
  transform: translateZ(0);
  will-change: transform, background-color;
  
  /* Flexbox centering */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  
  /* Interactive */
  cursor: pointer;
}

.embla__button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 1) !important;
  transform: scale(1.05) translateZ(0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.embla__button:active:not(:disabled) {
  transform: scale(0.95) translateZ(0);
}

.embla__button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Skeleton loading optimizations */
.MuiSkeleton-root {
  /* Smooth skeleton animations */
  transform: translateZ(0);
  will-change: transform;
}

/* Mobile optimizations */
@media (max-width: 600px) {
  .embla__slide__content {
    padding: 1rem;
    max-width: 100%;
  }
  
  .embla__slide__title {
    font-size: clamp(1.25rem, 6vw, 2rem) !important;
    margin-bottom: 1rem !important;
  }
  
  .embla__controls {
    bottom: 1rem !important;
    right: 1rem !important;
  }
  
  .embla__button {
    width: 40px !important;
    height: 40px !important;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .embla__container,
  .carousel-image-container img,
  .embla__slide__content,
  .btn-slider,
  .explore-btn,
  .embla__button {
    transition: none !important;
    animation: none !important;
  }
  
  .carousel-image-container {
    animation: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .embla__slide__content {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  }
  
  .embla__button {
    border: 2px solid #333 !important;
  }
}

/* Focus management for accessibility */
.embla__button:focus-visible {
  outline: 2px solid #007bff !important;
  outline-offset: 2px !important;
}

/* Performance optimizations for older browsers */
.embla,
.embla__viewport,
.embla__container,
.embla__slide {
  /* Fallback for browsers without will-change support */
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
}

/* Print styles */
@media print {
  .embla__controls,
  .embla__button {
    display: none !important;
  }
  
  .embla__slide {
    break-inside: avoid;
  }
}
