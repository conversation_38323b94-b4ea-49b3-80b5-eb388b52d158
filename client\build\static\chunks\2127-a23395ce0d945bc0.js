(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2127,1177],{37645:function(e,n,t){var s={"./en/Algeria.json":[31655,1655],"./en/Egypte.json":[92188,2188],"./en/ForumAfricaFrance.json":[28830,8830],"./en/HomeDashboard.json":[40087,87],"./en/PentabellSalestraining.json":[13511,3511],"./en/ProfessionalInformations.json":[87454,7454],"./en/QHSEEXPO.json":[79329,9329],"./en/Tunisia.json":[22901,2901],"./en/aboutUs.json":[83849,3849],"./en/activation.json":[74499,4499],"./en/africa.json":[19135,9135],"./en/aiSourcingService.json":[66143,6143],"./en/application.json":[35375,5375],"./en/certification.json":[22618,2618],"./en/comments.json":[6936,6936],"./en/consultingServices.json":[54148,4148],"./en/contact.json":[66951,6951],"./en/contactUs.json":[79669,9669],"./en/country.json":[49672,9672],"./en/createArticle.json":[70643,643],"./en/createOpportunity.json":[56142,6142],"./en/directHiringService.json":[22588,2588],"./en/dubai.json":[93310,3310],"./en/education.json":[12848,2848],"./en/europe.json":[50419,419],"./en/event.json":[55433,5433],"./en/eventDetails.json":[77008,7008],"./en/eventDetailsGitex.json":[23156,3156],"./en/eventDetailsLeap.json":[53792,3792],"./en/eventDetailsLibya.json":[58097,8097],"./en/eventForm.json":[324,324],"./en/experience.json":[52805,2805],"./en/expertCare.json":[16971,6971],"./en/favourite.json":[36636,6636],"./en/footer.json":[12309,2309],"./en/forgotPassword.json":[71732,1732],"./en/france.json":[4535,4535],"./en/getInTouch.json":[51558,1558],"./en/global.json":[43950,3950],"./en/guides.json":[26945,6945],"./en/homePage.json":[78311,8311],"./en/iraq.json":[17432,5289],"./en/joinUs.json":[12694,2694],"./en/ksa.json":[25188,5188],"./en/libya.json":[14094,4094],"./en/listArticle.json":[62235,2235],"./en/listCategory.json":[49263,9263],"./en/listCommentaire.json":[99562,9562],"./en/listopportunity.json":[35649,5649],"./en/listusers.json":[59387,9387],"./en/login.json":[53567,3567],"./en/mainService.json":[73048,3048],"./en/menu.json":[13277,3277],"./en/messages.json":[78928,8928],"./en/middleeast.json":[66715,6715],"./en/morocco.json":[59445,9445],"./en/opportunities.json":[12138,2138],"./en/payrollService.json":[96424,6424],"./en/personalinformation.json":[43457,3457],"./en/qatar.json":[54409,354],"./en/register.json":[18474,8474],"./en/resetPassword.json":[36576,6576],"./en/resumes.json":[24501,4501],"./en/seoSettings.json":[96056,6056],"./en/servicesByCountry.json":[76027,6027],"./en/settings.json":[47908,7908],"./en/sidebar.json":[35161,5161],"./en/sliders.json":[54237,4237],"./en/statisticsApp.json":[54166,4166],"./en/statisticsDash.json":[61833,1833],"./en/statsDash.json":[19696,9696],"./en/statsTotalNumbers.json":[25986,5986],"./en/steps.json":[43226,3226],"./en/technicalAssistanceService.json":[83218,3218],"./en/users.json":[64815,4815],"./en/validations.json":[16312,6312],"./fr/Algeria.json":[26405,6405],"./fr/Egypte.json":[52672,2672],"./fr/ForumAfricaFrance.json":[5124,5124],"./fr/HomeDashboard.json":[63505,3505],"./fr/PentabellSalestraining.json":[27005,7005],"./fr/ProfessionalInformations.json":[24149,4149],"./fr/QHSEEXPO.json":[21247,1247],"./fr/Tunisia.json":[69634,4641],"./fr/aboutUs.json":[63572,3572],"./fr/activation.json":[20999,999],"./fr/africa.json":[26254,6254],"./fr/aiSourcingService.json":[33855,3855],"./fr/application.json":[3232,3232],"./fr/certification.json":[99290,9290],"./fr/comments.json":[29403,9403],"./fr/consultingServices.json":[6509,6509],"./fr/contact.json":[14778,4778],"./fr/contactUs.json":[92439,2439],"./fr/country.json":[91135,1135],"./fr/createArticle.json":[65261,5261],"./fr/createOpportunity.json":[60920,920],"./fr/directHiringService.json":[58816,8816],"./fr/dubai.json":[71610,1610],"./fr/education.json":[34669,4669],"./fr/europe.json":[52074,2074],"./fr/event.json":[20336,336],"./fr/eventDetails.json":[90363,363],"./fr/eventDetailsGitex.json":[94613,4613],"./fr/eventDetailsLeap.json":[72419,2419],"./fr/eventDetailsLibya.json":[13278,3278],"./fr/eventForm.json":[6643,6643],"./fr/experience.json":[89507,9507],"./fr/expertCare.json":[50306,306],"./fr/favourite.json":[14414,4414],"./fr/footer.json":[74365,4365],"./fr/forgotPassword.json":[45957,5957],"./fr/france.json":[55435,5435],"./fr/getInTouch.json":[111,111],"./fr/global.json":[93646,9586],"./fr/guides.json":[2440,2440],"./fr/homePage.json":[43155,3155],"./fr/iraq.json":[64233,4233],"./fr/joinUs.json":[7339,7339],"./fr/ksa.json":[18629,8629],"./fr/libya.json":[88934,8934],"./fr/listArticle.json":[56204,6204],"./fr/listCategory.json":[69783,9783],"./fr/listCommentaire.json":[12871,2871],"./fr/listopportunity.json":[47877,7877],"./fr/listusers.json":[34618,4618],"./fr/login.json":[14480,4480],"./fr/mainService.json":[96595,6595],"./fr/menu.json":[34109,4109],"./fr/messages.json":[67921,7921],"./fr/middleeast.json":[80922,922],"./fr/morocco.json":[29873,9873],"./fr/opportunities.json":[35329,5329],"./fr/payrollService.json":[49051,9051],"./fr/personalinformation.json":[63035,3035],"./fr/qatar.json":[90906,906],"./fr/register.json":[33626,3626],"./fr/resetPassword.json":[25549,5549],"./fr/resumes.json":[97890,7890],"./fr/seoSettings.json":[23322,3322],"./fr/servicesByCountry.json":[6359,6359],"./fr/settings.json":[66337,6337],"./fr/sidebar.json":[32982,2982],"./fr/sliders.json":[98386,8386],"./fr/statisticsApp.json":[35423,5423],"./fr/statisticsDash.json":[39500,9500],"./fr/statsDash.json":[56759,6759],"./fr/statsTotalNumbers.json":[25134,5134],"./fr/steps.json":[91767,1767],"./fr/technicalAssistanceService.json":[77143,7143],"./fr/users.json":[24130,4130],"./fr/validations.json":[18166,8166]};function r(e){if(!t.o(s,e))return Promise.resolve().then(function(){var n=Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n});var n=s[e],r=n[0];return t.e(n[1]).then(function(){return t.t(r,19)})}r.keys=function(){return Object.keys(s)},r.id=37645,e.exports=r},82687:function(e,n,t){"use strict";var s,r=t(94746);function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var s in t)({}).hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(null,arguments)}n.Z=e=>r.createElement("svg",a({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),s||(s=r.createElement("path",{fill:"#E5F0FC",d:"m12 9 4.242 4.242-1.414 1.414L12 11.828 9.17 14.656l-1.414-1.414z"})))},37693:function(e,n,t){"use strict";var s,r=t(94746);function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var s in t)({}).hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(null,arguments)}n.Z=e=>r.createElement("svg",a({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),s||(s=r.createElement("path",{fill:"#E5F0FC",d:"m12 15-4.242-4.242 1.414-1.414 2.829 2.828 2.828-2.828 1.414 1.414z"})))},30894:function(e){"use strict";e.exports={locales:["en","fr"],defaultLocale:"en",localeDetection:!1}},85086:function(e,n,t){"use strict";t.d(n,{default:function(){return d}});var s=t(57437),r=t(55788),a=t(46550),o=t(28230),i=t(740),l=t(30894),c=t.n(l);async function u(e,n,s,r){return(s=s||(0,a.Fs)()).use(o.D),r||s.use((0,i.Z)((e,n)=>t(37645)(`./${e}/${n}.json`))),await s.init({lng:e||c().defaultLocale,resources:r,fallbackLng:c().defaultLocale,supportedLngs:c().locales,defaultNS:n[0],fallbackNS:n[0],ns:n,preload:r?[]:c().locales}),{i18n:s,resources:s.services.resourceStore.data,t:s.t}}function d(e){let{children:n,locale:t,namespaces:o,resources:i}=e,l=(0,a.Fs)();return u(t,o,l,i),(0,s.jsx)(r.a3,{i18n:l,children:n})}},86956:function(e,n,t){"use strict";t.d(n,{Z:function(){return g}});var s=t(57437),r=t(99376),a=t(55788),o=t(30894),i=t.n(o),l=t(46172),c=t(12234),u=t(31691),d=t(59873),f=t(33359),p=t(93214),m={src:"/_next/static/media/fr.74833967.png",height:64,width:64,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAANlBMVEU7Qpvy+Pn/Q0xDSJv/Q0/8ho3/TVf9/v1MaXH/T1qChbv7g4qBhbs3PJT9+/jz+vqHi8RHTqZc5R8mAAAAEXRSTlP9+pya/via/QD9l5f1nJqa+mPEicQAAAAJcEhZcwAAAbsAAAG7ATrs4+IAAAArSURBVHicY+CAAgZmXi4+fm4mNgZBBgF2dlYWTgYGBh5GRhQGXAquGKYdAEKFAdc5P689AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},h={src:"/_next/static/media/en.99fa137c.png",height:64,width:64,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAQlBMVEVMaXF/iLnaJ0jATHLmaH3MUHHeKknBVnntXHADbczlYnfDT3IyVabUXXrPiqhigMOqut7ZYYHQW3+dptF3mdJ4mdJmSSWzAAAADnRSTlMArPyqp/z8/Pyr/KqtqYgUVz0AAAAJcEhZcwAAAdgAAAHYAfpcpnIAAAA6SURBVHicPcs3AgAgCASwswIW7P//qpNmD/B4Iy5LqWjax9y6QIGj5USgxDZyICw9c3RtqEWyE+P/vkYgAfJG1tb3AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8};function g(e){let{withFlag:n,onlyWebVersion:t}=e,o=(0,u.Z)(),g=(0,d.Z)(o.breakpoints.down("md")),j=(0,d.Z)(o.breakpoints.down("sm")),{i18n:A}=(0,a.$G)(),v=A.language,b=(0,r.useRouter)(),y=(0,r.usePathname)(),w=y.split("/"),x=async e=>{document.cookie=`NEXT_LOCALE=${e};expires=${new Date(Date.now()+2592e6).toUTCString()};path=/`;let n=async(n,t,s)=>{try{let r=(await p.yX.get(`${n}/opposite/${v}/${t}`)).data.slug,a=r?`${"en"===e?"":`/${e}`}/${s}/${r}`:"en"===e?"":"/events";b.push(a)}catch(e){}},t=async(n,t,s)=>{try{let r=(await p.yX.get(`${n}/${v}/${s}/${t}`)).data.slug,a=r?`${"en"===e?"":`/${e}`}/blog/category/${r}`:y;b.push(a)}catch(e){}},s=(e,n)=>w[1]===e&&w.length===n||w[2]===e&&w.length===n+1;if(v!==e){if(s("opportunities",4))await n(l.Y.opportunity,w[w.length-2],"opportunities");else if(s("apply",4))await n(l.Y.opportunity,w[w.length-2],"apply");else if(s("blog",4))await n(l.Y.articles,w[w.length-2],"blog");else if(s("guides",4))await n(l.Y.guides,w[w.length-2],"guides");else{let n;(n="category",w[2]===n&&5===w.length||w[3]===n&&6===w.length)?await t(l.Y.category,w[w.length-2],"blog"):v!==i().defaultLocale||i().prefixDefault?b.push(y.replace(`/${v}`,`/${e}`)):b.push(`/${e}${y}`)}}b.refresh()},N=[{name:"En",onClick:()=>x("en"),flag:h,route:""},{name:"Fr",onClick:()=>x("fr"),flag:m,route:""}];return(0,s.jsx)(s.Fragment,{children:t?(0,s.jsx)(c.Z,{buttonLabel:"en"===v?"En":"Fr",selectedFlag:"en"===v?h:m,buttonHref:"",menuItems:N,withFlag:n}):j||g?(0,s.jsx)(f.Z,{buttonLabel:"en"===v?"En":"Fr",selectedFlag:"en"===v?h:m,buttonHref:"",locale:v,menuItems:N,withFlag:n}):(0,s.jsx)(c.Z,{buttonLabel:"en"===v?"En":"Fr",selectedFlag:"en"===v?m:h,buttonHref:"",menuItems:N,withFlag:n})})}},12234:function(e,n,t){"use strict";var s=t(57437),r=t(2265),a=t(94013),o=t(8710),i=t(90486),l=t(42187),c=t(82687),u=t(37693),d=t(99376),f=t(44179),p=t(55788);let m=e=>{let{buttonLabel:n,buttonHref:t,menuItems:h,subMenu:g,locale:j,withFlag:A,selectedFlag:v}=e,b=(0,d.usePathname)(),{t:y}=(0,p.$G)(),[w,x]=(0,r.useState)(null),N=!!w,E=()=>b.includes("/blog/")&&!b.includes("/blog/category/")&&"/blog/"!==b&&"/fr/blog/"!==b,C=()=>{x(null)},P=(e,n)=>{e.stopPropagation(),n.onClick?n.onClick():void 0==n.subItems&&C()};return(0,s.jsxs)("div",{className:g?"sub-dropdown-menu":"dropdown-menu",children:[(0,s.jsxs)(a.Z,{className:b.includes(t)&&""!==t?"navbar-link dropdown-toggle active":E()?"navbar-link dropdown-toggle whiteBg":"navbar-link dropdown-toggle",id:"fade-button","aria-controls":N?"fade-menu":void 0,"aria-haspopup":"true","aria-expanded":N?"true":void 0,onClick:e=>{x(e.currentTarget)},children:[(0,s.jsx)(a.Z,{className:b.includes(t)&&""!==t?"dropdown-toggle-link active":E()?"dropdown-toggle-link whiteBg":"dropdown-toggle-link",href:t?(0,f.jJ)(j,t).replace("/en/","/"):b.replace("/en/","/"),locale:"en"===j?"en":"fr",children:A?(0,s.jsx)("img",{className:"flag-lang",src:v.src,width:26,height:22,disabled:!0,loading:"lazy"}):n}),N?(0,s.jsx)(c.Z,{}):(0,s.jsx)(u.Z,{})]}),(0,s.jsx)(o.Z,{id:"fade-menu",MenuListProps:{"aria-labelledby":"fade-button"},anchorEl:w,open:N,onClose:C,TransitionComponent:i.Z,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:g?"right":"left"},children:h.map((e,n)=>(0,s.jsx)(l.Z,{className:e.subItems?"sub-menu dropdown-item":"dropdown-item",onClick:n=>P(n,e),children:e.subItems?(0,s.jsx)(m,{buttonLabel:e?.i18nName?y(e?.i18nName):e.name,buttonHref:e.route,menuItems:e.subItems,subMenu:!0}):e.route?(0,s.jsxs)(a.Z,{href:(0,f.jJ)(j,e.route),locale:"en"===j?"en":"fr",className:b.includes(e.route)?"dropdown-item-link active":"dropdown-item-link",children:[e.icon??e.icon," ",e?.i18nName?y(e?.i18nName):e.name]}):(0,s.jsx)(a.Z,{className:"dropdown-item-link",href:"#",children:A?(0,s.jsx)("img",{className:"flag-lang",src:e.flag.src,width:26,height:22,loading:"lazy"}):e?.i18nName?y(e?.i18nName):e.name})},n))})]})};n.Z=m},33359:function(e,n,t){"use strict";var s=t(57437),r=t(2265),a=t(11741),o=t(67051),i=t(17162),l=t(15273),c=t(73261),u=t(82687),d=t(37693),f=t(27648),p=t(44179);t(33145);var m=t(55788),h=t(99376);let g=e=>{let{buttonLabel:n,buttonHref:t,menuItems:j,locale:A,withFlag:v,selectedFlag:b,subMenu:y}=e,[w,x]=(0,r.useState)(!1),{t:N}=(0,m.$G)();(0,h.usePathname)();let E=(e,n)=>{e.stopPropagation(),n.onClick?n.onClick():void 0==n.subItems&&x(!w)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(a.Z,{className:y?"sub-menu dropdown-menu":"dropdown-menu",onClick:e=>E(e,{href:t}),children:[(0,s.jsx)(f.default,{href:(0,p.jJ)(A,t),locale:A,className:"menu-item",children:v?(0,s.jsx)("img",{className:"flag-lang",src:b?.src,width:26,height:22,loading:"lazy"}):(0,s.jsx)(o.Z,{primary:n})}),j&&j.length>0&&(0,s.jsx)("div",{className:"submenu-toggle",onClick:e=>{e.stopPropagation(),x(!w)},children:w?(0,s.jsx)(u.Z,{}):(0,s.jsx)(d.Z,{})})]}),(0,s.jsx)(i.Z,{in:w,timeout:"auto",unmountOnExit:!0,children:(0,s.jsx)(l.Z,{component:"div",disablePadding:!0,children:j.map((e,n)=>(0,s.jsx)(c.ZP,{className:e.subMenu?"sub-menu menu-item":"menu-item",sx:{pl:4},onClick:n=>E(n,e),children:e.subItems?(0,s.jsx)(g,{locale:A,buttonLabel:e?.i18nName?N(e?.i18nName):e.name,buttonHref:e.route,menuItems:e.subItems,withFlag:v,selectedFlag:e.flag,subMenu:!0}):(0,s.jsxs)(a.Z,{href:e.route?(0,p.jJ)(A,e.route).replace("/en/","/"):null,onClick:n=>E(n,e),className:"dropdown-item-link",children:[e.icon&&e.icon,v?(0,s.jsx)("img",{className:"flag-lang",src:e.flag?.src,width:26,height:22,loading:"lazy"}):e?.i18nName?N(e?.i18nName):e.name]})},n))})})]})};n.Z=g},29194:function(e,n,t){"use strict";t.d(n,{Z:function(){return v}});var s=t(57437),r=t(2265),a=t(59832),o=t(67116),i=t(8710),l=t(42187),c=t(8350),u=t(53431);t(27648),t(33145),t(53357),t(82687),t(37693),t(94746);var d=t(17828),f=t(55788),p=t(44179),m=t(80657),h=t(28397),g=t(84485),j=t(15116),A=t(40257),v=function(e){let{locale:n}=e,[t,v]=(0,r.useState)(null),b=!!t,{t:y}=(0,f.$G)(),{user:w}=(0,d.Z)(),[x,N]=(0,r.useState)(!1),E=()=>{v(null)},C=w?.roles?.includes(h.uU.CANDIDATE)?`/${m.pf.baseURL.route}`:`/${m.GW.baseURL.route}`;return(0,s.jsxs)("div",{className:"dropdown-menu",children:[(0,s.jsxs)(a.Z,{onClick:e=>{v(e.currentTarget)},size:"small",sx:{ml:2},"aria-controls":b?"account-menu":void 0,"aria-haspopup":"true","aria-expanded":b?"true":void 0,children:[(0,s.jsxs)(o.Z,{sx:{width:45,height:45,backgroundColor:"#dbe8f6"},src:`${A.env.NEXT_PUBLIC_BASE_API_URL}/files/${w?.profilePicture}`,children:[" ",(0,s.jsxs)("span",{style:{color:"#234791",fontFamily:"Proxima-Nova-SemiBold",textTransform:"uppercase"},children:[" ",w?.firstName?.split(" ")[0][0].toUpperCase()," "]})]})," "]}),(0,s.jsxs)(i.Z,{anchorEl:t,id:"account-menu",open:b,onClose:E,onClick:E,slotProps:{paper:{elevation:0,sx:{overflow:"visible",filter:"drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",mt:1.5,"& .MuiAvatar-root":{width:32,height:32,ml:-.5,mr:1},"&::before":{content:'""',display:"block",position:"absolute",top:0,right:14,width:10,height:10,bgcolor:"background.paper",transform:"translateY(-50%) rotate(45deg)",zIndex:0}}}},transformOrigin:{horizontal:"right",vertical:"top"},anchorOrigin:{horizontal:"right",vertical:"bottom"},children:[(0,s.jsx)(l.Z,{sx:{color:"#234791",fontFamily:"Proxima-Nova-Regular"},onClick:()=>{window.location.href=(0,p.jJ)(n,`${C}`)},children:y("global:dashboard")}),(0,s.jsx)(l.Z,{sx:{color:"#234791",fontFamily:"Proxima-Nova-Regular"},onClick:()=>{window.location.href=(0,p.jJ)(n,`${C}/${m.tF.myProfile.route}`)},children:y("global:myProfile")}),(0,s.jsx)(c.Z,{}),(0,s.jsxs)(l.Z,{sx:{color:"#234791",fontFamily:"Proxima-Nova-Regular"},onClick:()=>{window.location.href=(0,p.jJ)(n,`${C}/${m.tF.settings.route}`)},children:[(0,s.jsx)(u.Z,{children:(0,s.jsx)(g.Z,{fontSize:"small"})}),y("global:settings")]}),(0,s.jsxs)(l.Z,{sx:{color:"#234791",fontFamily:"Proxima-Nova-Regular"},onClick:()=>{window.location.href=(0,p.jJ)(n,`/${m.jb.logout.route}`)},children:[(0,s.jsx)(u.Z,{children:(0,s.jsx)(j.Z,{fontSize:"small"})}),y("global:logout")]})]})]})}},53357:function(e,n,t){"use strict";t.d(n,{Z:function(){return N}});var s,r=t(57437),a=t(2265),o=t(99376),i=t(77398),l=t.n(i),c=t(17828),u=t(93214),d=t(68680),f=t(44179),p=t(80657),m=t(28397),h=t(31691),g=t(59873),j=t(89051),A=t(8710),v=t(41774),b=t(94746);function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var s in t)({}).hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(null,arguments)}var w=e=>b.createElement("svg",y({xmlns:"http://www.w3.org/2000/svg",width:20,height:22,fill:"none"},e),s||(s=b.createElement("path",{fill:"#234791",fillRule:"evenodd",d:"M5.227 2.227A6.75 6.75 0 0 1 16.75 7c0 3.39.725 5.514 1.409 6.766.342.628.678 1.044.917 1.296a3 3 0 0 0 .338.312l.009.006A.75.75 0 0 1 19 16.75H1a.75.75 0 0 1-.423-1.37l.009-.006.063-.05c.06-.05.156-.136.275-.262.239-.252.575-.668.918-1.296C2.525 12.514 3.25 10.39 3.25 7a6.75 6.75 0 0 1 1.977-4.773M2.69 15.25h14.62a9 9 0 0 1-.468-.766C16.025 12.986 15.25 10.611 15.25 7a5.25 5.25 0 0 0-10.5 0c0 3.61-.775 5.986-1.592 7.484a9 9 0 0 1-.468.766m5.204 4.101a.75.75 0 0 1 1.025.273 1.25 1.25 0 0 0 2.162 0 .75.75 0 0 1 1.298.752 2.75 2.75 0 0 1-4.758 0 .75.75 0 0 1 .273-1.025",clipRule:"evenodd"}))),x=t(40257),N=e=>{let{locale:n,websiteHeaderIcon:t,pathname:s,isDetailBlogPath:i}=e,[b,y]=(0,a.useState)(null),{user:N}=(0,c.Z)(),[E,C]=(0,a.useState)([]),[P,U]=(0,a.useState)(0),_=(0,a.useRef)(null),k=(0,o.useRouter)(),I=(0,h.Z)(),S=(0,g.Z)(I.breakpoints.down("sm")),R=()=>{y(null)};(0,a.useEffect)(()=>{S&&R()},[S]);let L=e=>{let n=l().duration(l()().diff(l()(e)));return n.asMonths()>=1?`${Math.floor(n.asMonths())}mois`:n.asDays()>=1?`${Math.floor(n.asDays())}j`:n.asHours()>=1?`${Math.floor(n.asHours())}h`:`${Math.floor(n.minutes())}min`},T=e=>{y(e.currentTarget)},Z=async e=>{try{e.isRead=!0,await u.yX.put(`${x.env.NEXT_PUBLIC_BASE_API_URL}/notifications/${e._id}`,e),U(P-1)}catch(e){console.error("Error marking all notifications as read:",e)}e?.link&&e?.link!=="http://localhost:3000"&&(window.location.href=e.link)};(0,a.useEffect)(()=>(_.current=(0,d.io)(x.env.NEXT_PUBLIC_BASE_URL,{path:"/api/v1/socket.io",withCredentials:!0}),_.current.on("initial-notifications",e=>{let n=e.map(e=>({...e,receivedTime:L(e.receivedTime)}));C(n),U(n.filter(e=>!e.isRead).length)}),_.current.on("notification",e=>{let n={...e[0],receivedTime:L(e[0].receivedTime)};C(e=>[n,...e].slice(0,4)),U(e=>e+1)}),()=>{_.current.disconnect()}),[]);let D=async()=>{try{await u.yX.put(`${x.env.NEXT_PUBLIC_BASE_API_URL}/notifications`,{isRead:!0}),C(e=>e.map(e=>({...e,isRead:e.isRead=!0}))),U(0)}catch(e){console.error("Error marking all notifications as read:",e)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"notif-section",children:[P&&P>0?(0,r.jsx)("span",{className:t?"notif-nbr-header":"notif-nbr",children:P>99?"+99":P}):null,t?(0,r.jsx)(j.Z,{title:"Notifications",children:(0,r.jsx)(v.default,{id:"Notifications",onClick:T,icon:(0,r.jsx)(w,{}),locale:n,className:s.includes("/notifications")?"btn btn-notif navbar-link active":i()?"btn btn-notif navbar-link whiteBg":"btn btn-notif navbar-link"})}):(0,r.jsx)(j.Z,{title:"Notifications",children:(0,r.jsx)(v.default,{onClick:T,id:"Notifications",icon:(0,r.jsx)(w,{}),locale:n,className:" btn btn-link"})})]}),(0,r.jsx)(A.Z,{id:"account-menu",anchorEl:b,open:!!b,onClose:R,slotProps:{paper:{elevation:0,sx:{backgroundColor:"#e4effc",overflow:"visible",filter:"drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",mt:1.5,"& .MuiAvatar-root":{width:32,height:32,ml:-.5,mr:1},"&::before":{content:'""',display:"block",position:"absolute",top:0,right:14,width:10,height:10,bgcolor:"#e4effc",transform:"translateY(-50%) rotate(45deg)",zIndex:0}}}},transformOrigin:{horizontal:"right",vertical:"top"},anchorOrigin:{horizontal:"right",vertical:"bottom"},children:(0,r.jsx)("div",{id:"notifications",children:(0,r.jsx)("div",{className:"container-alerts",children:(0,r.jsxs)("div",{className:"notifications-alerts",children:[(0,r.jsxs)("div",{className:"header-alert",children:[(0,r.jsxs)("h2",{children:[(0,r.jsx)("span",{className:"title",children:"Notifications"}),P>0&&(0,r.jsx)("span",{className:"unread-notification-number",children:P})]}),(0,r.jsx)("p",{onClick:D,children:"Mark all as read"})]}),(0,r.jsx)("div",{className:"body-alert",children:E.map(e=>(0,r.jsxs)("div",{className:`notification ${e.isRead?"readed":"unreaded"}`,onClick:()=>Z(e),children:[(0,r.jsx)("div",{className:"avatar",children:(0,f.B8)(e.type)}),(0,r.jsx)("div",{className:"text",children:(0,r.jsxs)("div",{className:"text-top",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"profil-name",children:e.senderName}),(0,r.jsx)("span",{className:"message-notif",children:e.message}),(0,r.jsx)("span",{className:"notification-description-message",children:e.description})]}),(0,r.jsxs)("div",{className:"notif-details",children:[(0,r.jsx)("span",{className:"notification-timestamp",children:e.receivedTime}),!e.isRead&&(0,r.jsx)("span",{className:"unread-dot"})]})]})})]},e._id))}),(0,r.jsx)("button",{onClick:()=>{R(),N?.roles?.includes(m.uU.ADMIN)||N?.roles?.includes(m.uU.EDITOR)?k.push(`/${p.GW.baseURL.route}/${p.tF.notifications.route}/`):N?.roles?.includes(m.uU.CANDIDATE)&&k.push(`/${p.pf.baseURL.route}/${p.tF.notifications.route}/`)},className:"see-all",type:"button",children:"See All"})]})})})})]})}},93214:function(e,n,t){"use strict";t.d(n,{cU:function(){return i},xk:function(){return o},yX:function(){return a}});var s=t(83464),r=t(40257);let a=s.Z.create({baseURL:r.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),o=s.Z.create({baseURL:r.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),i=s.Z.create({baseURL:r.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"});s.Z.create({baseURL:r.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))},17828:function(e,n,t){"use strict";t.d(n,{Z:function(){return c}});var s=t(83464),r=t(40257),a=s.Z.create({baseURL:r.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0});s.Z.create({baseURL:r.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},withCredentials:!0}),s.Z.create({baseURL:r.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e));var o=t(40257);let i=()=>new Promise(async(e,n)=>{try{let n=await a.get(`${o.env.NEXT_PUBLIC_BASE_API_URL}/account`);e(n.data)}catch(e){n(e)}});var l=t(86484);function c(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],{data:n,error:t,isLoading:s,refetch:r}=(0,l.useQuery)({queryKey:["currentUser"],queryFn:i,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1,retry:e});return{user:n,error:t,isLoading:s,refetch:r}}},51177:function(e,n,t){"use strict";t.d(n,{px:function(){return v},V3:function(){return N},kw:function(){return E},PU:function(){return y},ZC:function(){return x},iQ:function(){return w},vD:function(){return A},NB:function(){return b},fV:function(){return C}});var s=t(86484),r=t(7261),a=t(93214),o=t(4174),i=t(46172);let l=e=>{let n=e.t;return new Promise(async(t,s)=>{a.yX.put(`${i.Y.updateUser}/${e.user}`,e.nonEmptyValues).then(n=>{e.nonEmptyValues.profilePicture||r.Am.success("Personal information updated successfully."),n.data&&t(n.data)}).catch(e=>{e&&e.response&&e.response.data&&404===e.response.status&&r.Am.error(n("messages:userNotFound")),e&&s(e)})})},c=e=>new Promise(async(n,t)=>{a.yX.put(`${i.Y.users}/archive/${e.id}`,{archive:e.archive}).then(e=>{r.Am.success("user updated successfully"),e?.data&&n(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&t(e)})}),u=e=>new Promise(async(n,t)=>{try{let t=await a.xk.get(`/users/${e}`);n(t.data)}catch(e){t(e)}}),d=e=>{let n=e.body.t;return new Promise(async(t,s)=>{a.yX.put(`${i.Y.candidate}`,e.body).then(e=>{e.data&&(t(e.data),r.Am.success(n("messages:professionalInfoUpdated")))}).catch(e=>{e&&e.response&&e.response.data&&404===e.response.status&&r.Am.error(n("messages:candidateNotFound")),e&&s(e)})})},f=e=>new Promise(async(e,n)=>{try{let n=await a.yX.get(i.Y.currentUser);e(n.data)}catch(e){e.response&&404===e.response.status&&r.Am.error(o.X.USER_NOT_FOUND),e.response&&401===e.response.status||r.Am.error(o.X.SERVER_ERROR),n(e)}}),p=e=>new Promise(async(n,t)=>{try{let t=await a.yX.get(`${i.Y.users}/${e}`);n(t.data)}catch(e){e.response&&404===e.response.status&&r.Am.error(o.X.USER_NOT_FOUND),e.response&&401===e.response.status||r.Am.error(o.X.SERVER_ERROR),t(e)}}),m=()=>new Promise(async(e,n)=>{try{let n=await a.yX.get(`${i.Y.candidate}/currentCandidate`);e(n.data)}catch(e){e.response&&404===e.response.status&&r.Am.error(o.X.CandidateNotFound),n(e)}}),h=(e,n)=>new Promise(async(t,s)=>{try{let s=e.includes(n)?e:[...e,n],r=await a.yX.get(`${i.Y.skills}?industries=${encodeURIComponent(s.length>0?s.join(","):[])}`);t(r.data)}catch(e){s(e)}});async function g(e){let n=e.t;try{let t=await a.yX.put(`${i.v}/account/password`,e.passwordData);return r.Am.success(n("settings:supdatepassword")),t.data}catch(e){throw e.response&&406===e.response.status?r.Am.error(n("messages:currentpassword")):r.Am.error(n("messages:errorupdatepassword")),e.response?e.response.data:e}}var j=t(17828);let A=()=>{let e=(0,s.useQueryClient)(),{refetch:n}=w();return(0,s.useMutation)({mutationFn:e=>l(e),onSuccess:t=>{e.invalidateQueries("userData"),n(),localStorage.setItem("user",JSON.stringify(t))},onError:e=>{e.message=""}})},v=()=>(0,s.useMutation)({mutationFn:e=>{let{id:n,archive:t}=e;return c({id:n,archive:t})},onError:e=>{e.message="error on useArchiveduser"}}),b=()=>{let e=(0,s.useQueryClient)(),{user:n}=(0,j.Z)();return(0,s.useMutation)({mutationFn:e=>d({body:e}),onSuccess:n=>{e.invalidateQueries("candidateData")},onError:e=>{e.message=""}})},y=e=>(0,s.useQuery)(["user",e],async()=>await u(e)),w=()=>(0,s.useQuery)("userData",async()=>{try{return await f()}catch(e){}}),x=e=>(0,s.useQuery)("userData",async()=>{try{return await p(e)}catch(e){}}),N=()=>{let{user:e}=(0,j.Z)();return(0,s.useQuery)(["getCandidateData"],async()=>{try{return await m()}catch(e){}})},E=(e,n)=>(0,s.useQuery)("skills",async()=>{try{return await h(e,n)}catch(e){}}),C=()=>{let e=(0,s.useQueryClient)();return(0,s.useMutation)(g,{onSuccess:()=>{e.invalidateQueries("password")}})}},25363:function(e,n,t){"use strict";t.d(n,{ReactQueryProvider:function(){return o}});var s=t(57437),r=t(86484);let a=new r.QueryClient;function o(e){let{children:n}=e;return(0,s.jsx)(r.QueryClientProvider,{client:a,children:n})}},4174:function(e,n,t){"use strict";t.d(n,{X:function(){return s}});let s={INCORRECT_PASSWORD:"Password incorrect",EMAIL_NOT_FOUND:"There's no user with this email",InvalidEmail:"Invalid email address.",FailedUpdateFile:"failed to upload file try again !!",ACCOUNT_NOTACTIVATED:"Your account is not activated yet. Please check your email for the activation link.",EMAIL_EXIST:"Email already exist",FileExist:"File already exists",FileNotFound:"File not found!",error:"'An unknown error occurred'",ERROR:"An error occurred. Please try again later !",INVALID_SIGNUP_DATA:"Invalid signup data. Please check your information and try again.",CandidateNotFound:"Candidate not found",ResetPasswordLink:"Check your email. Link expires in 10 mins!",VALIDATIONS:{INVALID_EMAIL:"Invalid email",EMPTY_FIELD:"Please fill in the required fields!",END_DATE:"End date must be after start date",MIN_DATE:"Date of birth must be after 1950",MAX_DATE:"Date must be before 2005",MIN_LENGTH:"Field must be at least 3 characters",MAX_LENGTH:"Field must be at most 20 characters",REQUIRED:"This field is required!",INVALID_PASSWORD:"Password requires at least one uppercase, one lowercase letter, and one digit",PASSWORDMATCH:"password must match"}}},46172:function(e,n,t){"use strict";t.d(n,{Y:function(){return r},v:function(){return s}});let s=t(40257).env.NEXT_PUBLIC_BASE_API_URL,r={seo:"seoTags",auth:"/auth/signin",logout:"/auth/logout",candidatures:"/applications",signup:"/auth/signup",forgetPassword:"/auth/forgot-password",resetPassword:"/auth/reset-password",guides:"/guides",currentUser:"/users/current",updateUser:"/users",users:"/users",categoryGuides:"guidecategory",candidate:"/candidates",report:"/report",skills:"/skills",files:"/files",applications:"/applications",sliders:"/sliders",favoris:"/candidate/favourite",articles:"/articles",categories:"/categories",blog:"/blog",category:"/categories",opportunity:"/opportunities",seoOpportunity:"/seoOpportunity",newsletter:"/newsletter",contact:"/contact",favourite:"/favourite",contacts:"contacts",comments:"/comments",statistics:"/statistics",events:"/events",baseUrl:`${s}`}}}]);