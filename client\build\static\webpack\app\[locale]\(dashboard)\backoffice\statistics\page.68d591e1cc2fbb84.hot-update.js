"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/features/stats/charts/CommentByCategory.jsx":
/*!*********************************************************!*\
  !*** ./src/features/stats/charts/CommentByCategory.jsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CommentByCategory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Autocomplete,Card,CardContent,FormGroup,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon2_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon2.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon2.svg\");\n/* harmony import */ var _components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/charts/CustomPieChart */ \"(app-pages-browser)/./src/components/charts/CustomPieChart.jsx\");\n/* harmony import */ var _stats_hooks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../stats.hooks */ \"(app-pages-browser)/./src/features/stats/stats.hooks.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction CommentByCategory(param) {\n    let { transformedCategories } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    const [filteredCategories, setFilteredCategories] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [approve, setApprove] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dateFromComment, setDateFromComment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"2024-09-01\");\n    const [dateToComment, setDateToComment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchComment, setSearchComment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const resetSearchComments = ()=>{\n        setDateToComment(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromComment(\"2024-09-01\");\n        setFilteredCategories([]);\n        setApprove(true);\n        setCategories([]);\n        setSearchComment(!searchComment);\n    };\n    const getDataPieComments = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_7__.useGetCommentsStat)({\n        dateFrom: dateFromComment,\n        dateTo: dateToComment,\n        approve: approve,\n        categories: filteredCategories\n    });\n    const pieChart = {\n        title: t(\"statsDash:commentsByCategory\"),\n        dataset: getDataPieComments?.data?.map((comment)=>({\n                label: comment.category,\n                value: comment.total\n            })) || [],\n        colors: [\n            \"#673ab7\",\n            \"#009688\",\n            \"#8bc34a\",\n            \"#ffc107\",\n            \"#ff9800\",\n            \"#ffc107\",\n            \"#3f51b5\",\n            \"#009688\",\n            \"#4caf50\",\n            \"#03a9f4\",\n            \"#ff9800\",\n            \"#8bc34a\",\n            \"#673ab7\"\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getDataPieComments.refetch();\n    }, [\n        searchComment\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        className: \"card\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"heading-h3\",\n                    gutterBottom: true,\n                    children: pieCharts?.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    elevation: 0,\n                    disableGutters: true,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            \"aria-controls\": \"panel1bh-content\",\n                            id: \"panel1bh-header\",\n                            className: \"svg-accordion\",\n                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                lineNumber: 95,\n                                columnNumber: 25\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"label-pentabell\",\n                                children: t(\"statsDash:filters\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            elevation: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                container: true,\n                                className: \"chart-grid\",\n                                spacing: 1,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"blue-text\",\n                                                    children: [\n                                                        \" \",\n                                                        t(\"statsDash:categories\"),\n                                                        \" :\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    multiple: true,\n                                                    id: `category`,\n                                                    options: transformedCategories ? transformedCategories : [],\n                                                    getOptionLabel: (option)=>option.name,\n                                                    value: categories.length > 0 ? transformedCategories.filter((category)=>categories.includes(category.name)) : [],\n                                                    onChange: (event, selectedOptions)=>{\n                                                        const categoryNames = selectedOptions.map((category)=>category.name);\n                                                        setCategories(categoryNames);\n                                                        setFilteredCategories(categoryNames.join(\",\"));\n                                                    },\n                                                    renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            ...params,\n                                                            className: \"\",\n                                                            variant: \"standard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        md: 12,\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"blue-text\",\n                                                children: [\n                                                    t(\"statsDash:approvedComments\"),\n                                                    \" :\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"select-pentabell blue-text\",\n                                                value: approve,\n                                                defaultValue: \"\",\n                                                onChange: (event)=>setApprove(event.target.value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        value: \"\",\n                                                        selected: true,\n                                                        disabled: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                            children: t(\"statsDash:approvedComments\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"blue-text\",\n                                                        value: \"\",\n                                                        children: t(\"statsDash:all\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"blue-text\",\n                                                        value: true,\n                                                        children: t(\"statsDash:approved\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"blue-text\",\n                                                        value: false,\n                                                        children: t(\"statsDash:notApproved\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            label: t(\"statsDash:fromDate\"),\n                                            type: \"date\",\n                                            value: dateFromComment,\n                                            onChange: (e)=>setDateFromComment(e.target.value),\n                                            fullWidth: true,\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            label: t(\"statsDash:toDate\"),\n                                            type: \"date\",\n                                            value: dateToComment,\n                                            onChange: (e)=>setDateToComment(e.target.value),\n                                            fullWidth: true,\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 3,\n                                        sm: 1,\n                                        md: 4,\n                                        className: \"btns-filter dashboard\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon2_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            className: \"btn btn-outlined btn-refresh full-width\",\n                                            onClick: resetSearchComments\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Autocomplete_Card_CardContent_FormGroup_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        item: true,\n                                        xs: 11,\n                                        sm: 11,\n                                        md: 8,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            text: t(\"statsDash:filter\"),\n                                            onClick: ()=>{\n                                                setSearchComment(!searchComment);\n                                            },\n                                            className: \"btn btn-outlined btn-filter-stat full-width\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"chart-wrapper\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            donuts: true,\n                            chart: pieCharts\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\charts\\\\CommentByCategory.jsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n_s(CommentByCategory, \"1ggJSwuZnVdzcMCH+wNE9uJZq+U=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_7__.useGetCommentsStat\n    ];\n});\n_c = CommentByCategory;\nvar _c;\n$RefreshReg$(_c, \"CommentByCategory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/stats/charts/CommentByCategory.jsx\n"));

/***/ })

});