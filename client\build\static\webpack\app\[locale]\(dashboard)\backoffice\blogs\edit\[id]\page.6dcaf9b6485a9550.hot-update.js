"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/edit/[id]/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticle.jsx":
/*!*****************************************************!*\
  !*** ./src/features/blog/components/AddArticle.jsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @feelinglovelynow/slug */ \"(app-pages-browser)/./node_modules/@feelinglovelynow/slug/dist/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! suneditor-react */ \"(app-pages-browser)/./node_modules/suneditor-react/dist/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(suneditor_react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var react_tag_input__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! react-tag-input */ \"(app-pages-browser)/./node_modules/react-tag-input/dist/index.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! suneditor/src/plugins */ \"(app-pages-browser)/./node_modules/suneditor/src/plugins/index.js\");\n/* harmony import */ var _FaqSection__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./FaqSection */ \"(app-pages-browser)/./src/features/blog/components/FaqSection.jsx\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var suneditor_dist_css_suneditor_min_css__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! suneditor/dist/css/suneditor.min.css */ \"(app-pages-browser)/./node_modules/suneditor/dist/css/suneditor.min.css\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/DatePicker/DatePicker.js\");\n/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/x-date-pickers/AdapterDayjs */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/AdapterDayjs/AdapterDayjs.js\");\n/* harmony import */ var _mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/x-date-pickers/internals/demo */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/internals/demo/DemoContainer.js\");\n/* harmony import */ var _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _DocumentImporter__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./DocumentImporter */ \"(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AddArticle = (param)=>{\n    let { language, initialValues, formRef, onImageSelect, validationSchema, image, isEdit, onCategoriesSelect, filteredCategories } = param;\n    _s();\n    const handlePaste = (event, cleanData, maxCharCount)=>{\n        let html = cleanData;\n        html = html.replace(/<strong>(.*?)$/g, \"<strong>$1</strong>\");\n        return html;\n    };\n    const KeyCodes = {\n        comma: 188,\n        enter: 13\n    };\n    const delimiters = [\n        KeyCodes.comma,\n        KeyCodes.enter\n    ];\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [highlights, setHighlights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTags(initialValues?.keywords?.length > 0 ? initialValues?.keywords : []);\n        setHighlights(initialValues?.highlights?.length > 0 ? initialValues?.highlights : []);\n    }, [\n        initialValues\n    ]);\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [publishNow, setPublishNow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handlePhotoChange = async ()=>{\n        const selectedFile = imageInputRef.current.files[0];\n        setSelectedImage(imageInputRef.current.files[0]);\n        if (selectedFile) {\n            onImageSelect(selectedFile, language);\n        }\n    };\n    const getCategories = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories)(language);\n    const transformedCategories = getCategories?.data?.categories?.map((category)=>({\n            id: category.versionscategory[0]?.id,\n            name: category.versionscategory[0]?.name\n        })) || [];\n    const useSaveFileHook = (0,_features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__.useSaveFile)();\n    const handlePhotoBlogChange = async (file, info, core, uploadHandler)=>{\n        if (file instanceof HTMLImageElement) {\n            const src = file.src;\n            if (src.startsWith(\"data:image\")) {\n                const base64Data = src.split(\",\")[1];\n                const contentType = src.match(/data:(.*?);base64/)[1];\n                const byteCharacters = atob(base64Data);\n                const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i)=>byteCharacters.charCodeAt(i));\n                const byteArray = new Uint8Array(byteNumbers);\n                const blob = new Blob([\n                    byteArray\n                ], {\n                    type: contentType\n                });\n                const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                const selectedFile = new File([\n                    blob\n                ], fileName, {\n                    type: contentType\n                });\n                await uploadFile(selectedFile, uploadHandler, core, file);\n            } else {\n                fetch(src).then((response)=>response.blob()).then((blob)=>{\n                    const contentType = blob.type;\n                    const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                    const selectedFile = new File([\n                        blob\n                    ], fileName, {\n                        type: contentType\n                    });\n                    uploadFile(selectedFile, uploadHandler, core, file);\n                }).catch((error)=>console.error(\"Error converting image URL to Blob:\", error));\n            }\n        } else {\n            console.error(\"File is not an HTMLImageElement.\");\n        }\n    };\n    const uploadFile = (selectedFile, uploadHandler, core, originalImage)=>{\n        let uuidPhoto;\n        uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n        const formData = new FormData();\n        formData.append(\"file\", selectedFile);\n        const extension = selectedFile.name.split(\".\").pop();\n        const currentYear = new Date().getFullYear();\n        useSaveFileHook.mutate({\n            resource: \"blogs\",\n            folder: currentYear.toString(),\n            filename: uuidPhoto,\n            body: {\n                formData,\n                t\n            }\n        }, {\n            onSuccess: (dataUUID)=>{\n                const uuidPhotoFileName = dataUUID.message === \"uuid exist\" ? dataUUID.uuid : `${uuidPhoto}.${extension}`;\n                const imageUrl = `${\"http://localhost:4000/api/v1\"}/files/${uuidPhotoFileName}`;\n                originalImage.src = imageUrl;\n                uploadHandler({\n                    result: [\n                        {\n                            id: uuidPhotoFileName,\n                            url: imageUrl\n                        }\n                    ]\n                });\n            },\n            onError: (error)=>{\n                console.error(\"Error uploading file:\", error);\n            }\n        });\n    };\n    const handleContentExtracted = (extractedContent)=>{\n        setFieldValue(\"contentEN\", extractedContent);\n        debounce();\n    };\n    const handleMetadataExtracted = (metadata)=>{\n        if (metadata.title && !values.titleEN) {\n            setFieldValue(\"titleEN\", metadata.title);\n            const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_7__.slug)(metadata.title);\n            setFieldValue(\"urlEN\", url);\n        }\n        if (metadata.description && !values.descriptionEN) {\n            setFieldValue(\"descriptionEN\", metadata.description);\n        }\n        if (metadata.keywords && metadata.keywords.length > 0) {\n            const keywordTags = metadata.keywords.map((keyword, index)=>({\n                    id: `extracted-${index}`,\n                    text: keyword\n                }));\n            const existingKeywords = values.keywordsEN || [];\n            const mergedKeywords = [\n                ...existingKeywords,\n                ...keywordTags\n            ];\n            setFieldValue(\"keywordsEN\", mergedKeywords.map((tag)=>tag.text));\n        }\n        debounce();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"commun\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"experiences\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"form\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.Formik, {\n                    initialValues: initialValues,\n                    validationSchema: validationSchema,\n                    onSubmit: ()=>{},\n                    innerRef: formRef,\n                    enableReinitialize: \"true\",\n                    children: (param)=>{\n                        let { errors, touched, setFieldValue: setFieldValue1, values: values1, validateForm } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.Form, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"label-form\",\n                                                        children: [\n                                                            t(\"createArticle:title\"),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                variant: \"standard\",\n                                                                name: \"title\",\n                                                                type: \"text\",\n                                                                value: values1.title,\n                                                                onChange: (e)=>{\n                                                                    const title = e.target.value;\n                                                                    setFieldValue1(\"title\", title);\n                                                                    const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_7__.slug)(title);\n                                                                    setFieldValue1(\"urlEN\", url);\n                                                                },\n                                                                className: \"input-pentabell\" + (errors.title && touched.title ? \" is-invalid\" : \"\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                                className: \"label-error\",\n                                                                name: \"title\",\n                                                                component: \"div\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"label-form\",\n                                                        children: [\n                                                            t(\"createArticle:categories\"),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    multiple: true,\n                                                                    className: \"input-pentabell\",\n                                                                    id: \"tags-standard\",\n                                                                    options: language === \"en\" ? transformedCategories : language === \"fr\" && filteredCategories ? filteredCategories : [],\n                                                                    // defaultValue={values?.category || []}\n                                                                    getOptionLabel: (option)=>option.name,\n                                                                    value: values1.category.length > 0 ? transformedCategories.filter((category)=>values1.category.some((selectedCategory)=>selectedCategory === category.id)) : [],\n                                                                    onChange: (event, selectedOptions)=>{\n                                                                        const categoryIds = selectedOptions.map((category)=>category.id);\n                                                                        setFieldValue1(\"category\", categoryIds);\n                                                                        if (language === \"en\" && onCategoriesSelect) {\n                                                                            onCategoriesSelect(categoryIds);\n                                                                        }\n                                                                    },\n                                                                    renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            ...params,\n                                                                            className: \"input-pentabell  multiple-select\",\n                                                                            variant: \"standard\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                            lineNumber: 290,\n                                                                            columnNumber: 31\n                                                                        }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    touched.category && errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"label-error\",\n                                                        children: errors.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"label-form\",\n                                                children: [\n                                                    \"Description\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        variant: \"standard\",\n                                                        name: \"description\",\n                                                        type: \"text\",\n                                                        multiline: true,\n                                                        rows: 3,\n                                                        value: values1.description,\n                                                        onChange: (e)=>{\n                                                            const description = e.target.value;\n                                                            setFieldValue1(\"description\", description);\n                                                        },\n                                                        className: \"textArea-pentabell\" + (errors.description && touched.description ? \" is-invalid\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                        className: \"label-error\",\n                                                        name: \"description\",\n                                                        component: \"div\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"label-form\",\n                                                    children: [\n                                                        \"Highlights\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            id: \"tags\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                                                tags: highlights,\n                                                                className: \"input-pentabell\" + (errors.highlights && touched.highlights ? \" is-invalid\" : \"\"),\n                                                                delimiters: delimiters,\n                                                                handleDelete: (i)=>{\n                                                                    const updatedTags = highlights.filter((tag, index)=>index !== i);\n                                                                    setHighlights(updatedTags);\n                                                                    setFieldValue1(\"highlights\", updatedTags.map((tag)=>tag.text));\n                                                                },\n                                                                handleAddition: (tag)=>{\n                                                                    setHighlights([\n                                                                        ...highlights,\n                                                                        tag\n                                                                    ]);\n                                                                    setFieldValue1(\"highlights\", [\n                                                                        ...highlights,\n                                                                        tag\n                                                                    ].map((item)=>item.text));\n                                                                    const updatedTAgs = [\n                                                                        ...highlights,\n                                                                        tag\n                                                                    ].map((item)=>item.text);\n                                                                },\n                                                                inputFieldPosition: \"bottom\",\n                                                                autocomplete: true,\n                                                                allowDragDrop: false\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                            className: \"label-error\",\n                                                            name: \"highlights\",\n                                                            component: \"div\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentImporter__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    onContentExtracted: handleContentExtracted,\n                                    onMetadataExtracted: handleMetadataExtracted,\n                                    language: language\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((suneditor_react__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                    setContents: values1?.content?.length > 0 ? values1.content : \"\",\n                                    onChange: (e)=>{\n                                        setFieldValue1(\"content\", e);\n                                    },\n                                    onPaste: handlePaste,\n                                    setOptions: {\n                                        cleanHTML: false,\n                                        disableHtmlSanitizer: true,\n                                        addTagsWhitelist: \"h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button\",\n                                        plugins: suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                                        buttonList: [\n                                            [\n                                                \"undo\",\n                                                \"redo\"\n                                            ],\n                                            [\n                                                \"font\",\n                                                \"fontSize\",\n                                                \"formatBlock\"\n                                            ],\n                                            [\n                                                \"bold\",\n                                                \"underline\",\n                                                \"italic\",\n                                                \"strike\",\n                                                \"subscript\",\n                                                \"superscript\"\n                                            ],\n                                            [\n                                                \"fontColor\",\n                                                \"hiliteColor\"\n                                            ],\n                                            [\n                                                \"align\",\n                                                \"list\",\n                                                \"lineHeight\"\n                                            ],\n                                            [\n                                                \"outdent\",\n                                                \"indent\"\n                                            ],\n                                            [\n                                                \"table\",\n                                                \"horizontalRule\",\n                                                \"link\",\n                                                \"image\",\n                                                \"video\"\n                                            ],\n                                            [\n                                                \"fullScreen\",\n                                                \"showBlocks\",\n                                                \"codeView\"\n                                            ],\n                                            [\n                                                \"preview\",\n                                                \"print\"\n                                            ],\n                                            [\n                                                \"removeFormat\"\n                                            ]\n                                        ],\n                                        imageUploadHandler: handlePhotoBlogChange,\n                                        defaultTag: \"div\",\n                                        minHeight: \"300px\",\n                                        maxHeight: \"400px\",\n                                        showPathLabel: false,\n                                        font: [\n                                            \"Proxima-Nova-Regular\",\n                                            \"Proxima-Nova-Medium\",\n                                            \"Proxima-Nova-Semibold\",\n                                            \"Proxima-Nova-Bold\",\n                                            \"Proxima-Nova-Extrabold\",\n                                            \"Proxima-Nova-Black\",\n                                            \"Proxima-Nova-Light\",\n                                            \"Proxima-Nova-Thin\",\n                                            \"Arial\",\n                                            \"Times New Roman\",\n                                            \"Sans-Serif\"\n                                        ],\n                                        charCounter: true,\n                                        charCounterType: \"byte\",\n                                        resizingBar: false,\n                                        colorList: [\n                                            // Standard Colors\n                                            [\n                                                \"#234791\",\n                                                \"#d69b19\",\n                                                \"#cc3233\",\n                                                \"#009966\",\n                                                \"#0b3051\",\n                                                \"#2BBFAD\",\n                                                \"#0b305100\",\n                                                \"#0a305214\",\n                                                \"#743794\",\n                                                \"#ff0000\",\n                                                \"#ff5e00\",\n                                                \"#ffe400\",\n                                                \"#abf200\",\n                                                \"#00d8ff\",\n                                                \"#0055ff\",\n                                                \"#6600ff\",\n                                                \"#ff00dd\",\n                                                \"#000000\",\n                                                \"#ffd8d8\",\n                                                \"#fae0d4\",\n                                                \"#faf4c0\",\n                                                \"#e4f7ba\",\n                                                \"#d4f4fa\",\n                                                \"#d9e5ff\",\n                                                \"#e8d9ff\",\n                                                \"#ffd9fa\",\n                                                \"#f1f1f1\",\n                                                \"#ffa7a7\",\n                                                \"#ffc19e\",\n                                                \"#faed7d\",\n                                                \"#cef279\",\n                                                \"#b2ebf4\",\n                                                \"#b2ccff\",\n                                                \"#d1b2ff\",\n                                                \"#ffb2f5\",\n                                                \"#bdbdbd\",\n                                                \"#f15f5f\",\n                                                \"#f29661\",\n                                                \"#e5d85c\",\n                                                \"#bce55c\",\n                                                \"#5cd1e5\",\n                                                \"#6699ff\",\n                                                \"#a366ff\",\n                                                \"#f261df\",\n                                                \"#8c8c8c\",\n                                                \"#980000\",\n                                                \"#993800\",\n                                                \"#998a00\",\n                                                \"#6b9900\",\n                                                \"#008299\",\n                                                \"#003399\",\n                                                \"#3d0099\",\n                                                \"#990085\",\n                                                \"#353535\",\n                                                \"#670000\",\n                                                \"#662500\",\n                                                \"#665c00\",\n                                                \"#476600\",\n                                                \"#005766\",\n                                                \"#002266\",\n                                                \"#290066\",\n                                                \"#660058\",\n                                                \"#222222\"\n                                            ]\n                                        ]\n                                    },\n                                    onImageUpload: handlePhotoBlogChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaqSection__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    values: values1,\n                                    setFieldValue: setFieldValue1,\n                                    errors: errors,\n                                    touched: touched,\n                                    language: language === \"en\" ? \"EN\" : \"FR\",\n                                    debounce: ()=>{},\n                                    isEdit: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"label-form\",\n                                                    children: [\n                                                        t(\"createArticle:metaTitle\"),\n                                                        \" (\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: values1.metaTitle?.length > 65 ? \" text-danger\" : \"\",\n                                                            children: [\n                                                                \" \",\n                                                                values1.metaTitle?.length,\n                                                                \" / 65\",\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        \" \",\n                                                        \")\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            variant: \"standard\",\n                                                            name: \"metaTitle\",\n                                                            type: \"text\",\n                                                            value: values1.metaTitle,\n                                                            onChange: (e)=>{\n                                                                setFieldValue1(\"metaTitle\", e.target.value);\n                                                            },\n                                                            className: \"input-pentabell\" + (errors.metaTitle && touched.metaTitle ? \" is-invalid\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                            className: \"label-error\",\n                                                            name: \"metaTitle\",\n                                                            component: \"div\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"label-form\",\n                                                    children: [\n                                                        t(\"createArticle:url\"),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            variant: \"standard\",\n                                                            name: \"url\",\n                                                            type: \"text\",\n                                                            value: values1.url,\n                                                            onChange: (e)=>{\n                                                                setFieldValue1(\"url\", e.target.value);\n                                                            },\n                                                            className: \"input-pentabell\" + (errors.url && touched.url ? \" is-invalid\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                            className: \"label-error\",\n                                                            name: \"url\",\n                                                            component: \"div\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"label-form\",\n                                                children: [\n                                                    t(\"createArticle:metaDescription\"),\n                                                    \" (\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: values1.metaDescription?.length > 160 ? \" text-danger\" : \"\",\n                                                        children: [\n                                                            values1.metaDescription?.length,\n                                                            \" / 160\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    \" \",\n                                                    \")\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        variant: \"standard\",\n                                                        name: \"metaDescription\",\n                                                        type: \"text\",\n                                                        multiline: true,\n                                                        rows: 3,\n                                                        value: values1.metaDescription,\n                                                        onChange: (e)=>{\n                                                            setFieldValue1(\"metaDescription\", e.target.value);\n                                                        },\n                                                        className: \"textArea-pentabell\" + (errors.metaDescription && touched.metaDescription ? \" is-invalid\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                        className: \"label-error\",\n                                                        name: \"metaDescription\",\n                                                        component: \"div\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"label-form\",\n                                                children: [\n                                                    t(\"createArticle:featuredImage\"),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"upload-container\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: `image-upload-${language}`,\n                                                            className: \"file-labels\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"file\",\n                                                                    id: `image-upload-${language}`,\n                                                                    name: \"image\",\n                                                                    accept: \".png, .jpg, .jpeg, .webp\",\n                                                                    ref: imageInputRef,\n                                                                    onChange: (e)=>{\n                                                                        setFieldValue1(\"image\", e.target.files[0]);\n                                                                        handlePhotoChange();\n                                                                    },\n                                                                    className: \"file-input\" + (errors.image && touched.image ? \" is-invalid\" : \"\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"upload-area\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"icon-pic\",\n                                                                                style: {\n                                                                                    backgroundImage: `url(\"${selectedImage ? URL.createObjectURL(selectedImage) : image ? `${\"http://localhost:4000/api/v1\"}${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.files}/${image}` : _assets_images_add_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src}\")`,\n                                                                                    backgroundSize: \"cover\",\n                                                                                    backgroundRepeat: \"no-repeat\",\n                                                                                    backgroundPosition: \"center\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                                lineNumber: 666,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                            lineNumber: 665,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"upload-text\",\n                                                                                    children: t(\"createArticle:addFeatImg\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                                    lineNumber: 684,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"upload-description\",\n                                                                                    children: t(\"createArticle:clickBox\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                                    lineNumber: 687,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                            lineNumber: 683,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                                    name: \"image\",\n                                                                    component: \"div\",\n                                                                    className: \"invalid-feedback error\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"label-form\",\n                                                        children: [\n                                                            t(\"createArticle:alt\"),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                variant: \"standard\",\n                                                                name: \"alt\",\n                                                                type: \"text\",\n                                                                value: values1.alt,\n                                                                onChange: (e)=>{\n                                                                    setFieldValue1(\"alt\", e.target.value);\n                                                                },\n                                                                className: \"input-pentabell\" + (errors.alt && touched.alt ? \" is-invalid\" : \"\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                                className: \"label-error\",\n                                                                name: \"alt\",\n                                                                component: \"div\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"label-form\",\n                                                        children: [\n                                                            t(\"createArticle:visibility\"),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"select-pentabell\",\n                                                                variant: \"standard\",\n                                                                value: _utils_constants__WEBPACK_IMPORTED_MODULE_4__.Visibility.filter((option)=>values1.visibility === option),\n                                                                selected: values1?.visibility,\n                                                                onChange: (event)=>{\n                                                                    setFieldValue1(\"visibility\", event.target.value);\n                                                                },\n                                                                children: _utils_constants__WEBPACK_IMPORTED_MODULE_4__.Visibility.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        value: item,\n                                                                        children: item\n                                                                    }, index, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                        lineNumber: 749,\n                                                                        columnNumber: 29\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                                className: \"label-error\",\n                                                                name: \"visibilityEN\",\n                                                                component: \"div\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"label-form\",\n                                                        children: [\n                                                            t(\"createArticle:keyword\"),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                id: \"tags\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_22__.WithContext, {\n                                                                    tags: tags,\n                                                                    className: \"input-pentabell\" + (errors.keywords && touched.keywords ? \" is-invalid\" : \"\"),\n                                                                    delimiters: delimiters,\n                                                                    handleDelete: (i)=>{\n                                                                        const updatedTags = tags.filter((tag, index)=>index !== i);\n                                                                        setTags(updatedTags);\n                                                                        setFieldValue1(\"keywords\", updatedTags.map((tag)=>tag.text));\n                                                                    },\n                                                                    handleAddition: (tag)=>{\n                                                                        setTags([\n                                                                            ...tags,\n                                                                            tag\n                                                                        ]);\n                                                                        setFieldValue1(\"keywords\", [\n                                                                            ...tags,\n                                                                            tag\n                                                                        ].map((item)=>item.text));\n                                                                        const updatedTAgs = [\n                                                                            ...tags,\n                                                                            tag\n                                                                        ].map((item)=>item.text);\n                                                                    },\n                                                                    inputFieldPosition: \"bottom\",\n                                                                    autocomplete: true,\n                                                                    allowDragDrop: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                    lineNumber: 770,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                                className: \"label-error\",\n                                                                name: \"keywords\",\n                                                                component: \"div\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 764,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"label-form\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.Field, {\n                                            type: \"checkbox\",\n                                            name: \"publishNow\",\n                                            checked: publishNow,\n                                            onChange: (e)=>{\n                                                setPublishNow(e.target.checked);\n                                                if (e.target.checked) {\n                                                    setFieldValue1(\"publishDate\", new Date().toISOString());\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                            lineNumber: 815,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        t(\"createArticle:publishNow\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 814,\n                                    columnNumber: 17\n                                }, undefined),\n                                !publishNow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"label-form\",\n                                                children: [\n                                                    t(\"createArticle:publishDate\"),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_26__.LocalizationProvider, {\n                                                        dateAdapter: _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_27__.AdapterDayjs,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_28__.DemoContainer, {\n                                                            components: [\n                                                                \"DatePicker\"\n                                                            ],\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_29__.DatePicker, {\n                                                                    variant: \"standard\",\n                                                                    className: \"input-date\",\n                                                                    format: \"DD/MM/YYYY\",\n                                                                    value: dayjs__WEBPACK_IMPORTED_MODULE_9___default()(values1.publishDateEN),\n                                                                    onChange: (date)=>{\n                                                                        setFieldValue1(\"publishDateEN\", dayjs__WEBPACK_IMPORTED_MODULE_9___default()(date).format(\"YYYY-MM-DD\"));\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                                    lineNumber: 836,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                            lineNumber: 835,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                        lineNumber: 834,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 832,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.ErrorMessage, {\n                                                className: \"label-error\",\n                                                name: \"publishDateEN\",\n                                                component: \"div\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                        lineNumber: 831,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 830,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_16__.Field, {\n                                    type: \"hidden\",\n                                    name: \"publishDate\",\n                                    value: publishNow ? new Date().toISOString() : publishDate.toISOString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                                    lineNumber: 860,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                            lineNumber: 221,\n                            columnNumber: 15\n                        }, undefined);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                    lineNumber: 213,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticle.jsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AddArticle, \"E9i6H46KXeqeynJxyI/25VRxGp4=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_5__.useGetCategories,\n        _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_13__.useSaveFile\n    ];\n});\n_c = AddArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticle);\nvar _c;\n$RefreshReg$(_c, \"AddArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticle.jsx\n"));

/***/ })

});