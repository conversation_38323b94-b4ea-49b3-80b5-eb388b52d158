"use client";

import { Container, Grid, useMediaQuery, useTheme } from "@mui/material";
import SvgArrowRight from "../../assets/images/icons/arrowRight.svg";
import { API_URLS } from "@/utils/urls";
import { getCountryEventImage } from "@/utils/functions";

function BannerComponentsEvent({
  bannerImg,
  bannerMobileImg,
  height,
  title,
  Icon,
  LeapIcon,
  MobileIcon,
  subtitle,
  bottomChildren,
  event,
  altImg,
  topChildren,
  language,
  url,
  name,
  t,
  eventData,
}) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  return (
    <div
      id="banner-component"
      style={{
        backgroundImage: `url(${isMobile && eventData?.mobileImage
            ? `${process.env.NEXT_PUBLIC_BASE_API_URL}${API_URLS.files}/${eventData?.mobileImage}`
            : !isMobile && eventData?.image
              ? `${process.env.NEXT_PUBLIC_BASE_API_URL}${API_URLS.files}/${eventData?.image}`
              : isMobile
                ? bannerMobileImg.src
                : bannerImg.src
          })`,
        height: height || "auto",
      }}
    >
      {altImg && (
        <img
          width={0}
          height={0}
          alt={altImg}
          src=""
          style={{ display: "none" }}
          loading="lazy"
        />
      )}
      <Container>
        {topChildren && topChildren}
        <>
          <Grid
            item
            className="continer_banner_event"
            sx={{ display: "flex", alignItems: "flex-start" }}
          >
            <div className="event-path">
              <a
                locale={language === "en" ? "en" : "fr"}
                href={`${language === "en" ? `/events` : `/${language}/events`
                  }/`}
                className="link"
                sx={{ marginRight: "10px" }}
              >
                {t("event:eventBreadCrumb")}
              </a>
              <SvgArrowRight />
              <a
                className="link"
                href={`${language === "en"
                    ? `/events/${url}`
                    : `/${language}/events/${url}`
                  }/`}
              >
                {eventData?.versions[0]?.name
                  ? eventData?.versions[0]?.name
                  : name}
              </a>
            </div>
          </Grid>
          <Grid sx={{ display: "flex", justifyContent: "flex-end" }} item>
            {" "}
          </Grid>
          <Grid
            item
            sx={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center",
              textAlign: "center",
            }}
          >
            <p className="sub-heading text-slide text-yellow">
              {eventData?.versions[0]?.subTitle
                ? eventData?.versions[0]?.subTitle
                : subtitle}
            </p>
            <h1 className="heading-h1 text-white">
              {eventData?.versions[0]?.title
                ? eventData?.versions[0]?.title
                : title}
            </h1>
            {LeapIcon && !isMobile && event !== "decarbonization" && (
              <LeapIcon className="icon-leap" />
            )}
            {isMobile && eventData?.country ? (
              <img
                width={150}
                height={130}
                src={getCountryEventImage(eventData?.country)}
                className="icon-map"
                alt={`${eventData?.country
                    ? `Map of ${eventData?.country}`
                    : `Country map`
                  }`}
                loading="lazy"
              />
            ) : isMobile ? (
              <MobileIcon className="icon-map" />
            ) : eventData?.country ? (
              <img
                width={180}
                height={150}
                src={getCountryEventImage(eventData?.country)}
                className="icon-map"
                alt={`${eventData?.country
                    ? `Map of ${eventData?.country}`
                    : `Country map`
                  }`}
                loading="lazy"
              />
            ) : (
              <Icon className="icon-map" />
            )}
          </Grid>
        </>
        {bottomChildren && bottomChildren}
      </Container>
    </div>
  );
}

export default BannerComponentsEvent;
