import candidatModel from '@/apis/candidat/candidat.model';
import filesModel from '@/apis/storage/files.model';
import userModel from '@/apis/user/user.model';
import HttpException from '@/utils/exceptions/http.exception';
import { Role } from '@/utils/helpers/constants';
import { MESSAGES } from '@/utils/helpers/messages';

import { Request, Response, NextFunction } from 'express';

const User = userModel;
const File = filesModel;
const Candidate = candidatModel;

export function validateFormatMiddleware(request: Request, response: Response, next: NextFunction) {
    const getMethod: boolean = request.method === 'GET';
    const { filename } = request.params;
    const mediaExtension: any = getMethod ? filename.split('.').pop() : '';
    const uuid: any = getMethod ? filename.split('.').shift() : '';
    const allowedTypes: string[] = ['png', 'jpg', 'jpeg', 'pdf', 'jfif', 'docx', 'webp'];
    if (getMethod && !allowedTypes.includes(mediaExtension))  {
        throw new HttpException(400, MESSAGES.FILE.INVALID_FORMAT);
    }

    if (getMethod) {
        request.params.uuid = uuid;
        request.params.mediaExtension = mediaExtension;
    }
    next();
}

export async function checkUserPermission(request: any, response: Response, next: NextFunction) {
    const file = await File.findOne({ fileName: request.params.filename });
    if (!file) return next(new HttpException(404, MESSAGES.FILE.NOT_FOUND));

    if (file.resource !== 'candidates') return next();

    if (!request.user) return next(new HttpException(403, MESSAGES.AUTH.FORBIDDEN));

    const user = await User.findById(request.user._id);
    if (!user) return next(new HttpException(401, MESSAGES.AUTH.UNAUTHORIZED));

    if ((user.roles.includes(Role.ADMIN)) || (user.roles.includes(Role.EDITEUR))) return next();

    const candidate = await Candidate.findById(user.candidate);
    if (!candidate) return next(new HttpException(401, MESSAGES.AUTH.UNAUTHORIZED));

    if (candidate.cv.some((c: any) => c.fileName === request.params.filename)) return next();
    else return next(new HttpException(403, MESSAGES.AUTH.FORBIDDEN));
}
