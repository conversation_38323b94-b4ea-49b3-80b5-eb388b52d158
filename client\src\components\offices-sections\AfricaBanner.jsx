import { Container, Grid } from "@mui/material";
import Image from "next/image";

function AfricaBanner({ bannerImg, height, altImg,t }) {
  return (
    <div
      id="africa-banner"
      className={"center-banner"}
      style={{ backgroundImage: `url(${bannerImg.src})`, height: height }}
    >
      <Container className="custom-max-width">
        {altImg && (
          <img
            width={0}
            height={0}
            alt={altImg}
            style={{ display: "none" }}
            loading="lazy"
          />
        )}

        <Grid className="container" container columnSpacing={0}>
          <Grid item xs={12} sm={6}>
            <div className="left-section">
              <h1 className="heading-h1 text-white">
              {t("africa:banner:title")}
              </h1>
              <p className="paragraph text-white">
              {t("africa:banner:description")}
              </p>
            </div>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default AfricaBanner;
