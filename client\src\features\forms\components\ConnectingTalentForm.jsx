"use client";
import { Formik, Form, ErrorMessage } from "formik";
import {
  Container,
  Grid,
  Checkbox,
  FormControlLabel,
  FormGroup,
  FormLabel,
  TextField,
  Alert,
  Autocomplete,
  Radio,
  RadioGroup,
} from "@mui/material";
import { PhoneNumberUtil } from "google-libphonenumber";
import "react-international-phone/style.css";
import * as Yup from "yup";
import { PhoneInput } from "react-international-phone";

import { v4 as uuidv4 } from "uuid";

import CustomButton from "@/components/ui/CustomButton";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useContactForm } from "@/features/contact/hooks/Contact.hooks";
import AlertMessage from "@/components/ui/AlertMessage";
import SvgUploadIcon from "../../../assets/images/icons/uploadIcon.svg";
import { joinUsValidationSchema } from "@/utils/validations";
import { useSaveFile } from "@/features/opportunity/hooks/opportunity.hooks";
import GTM from "../../../components/GTM";

import LazyLoadFlag from "../../../components/ui/LazyLoadFlag";

function ConnectingTalentForm() {
  const [errMsg, setErrMsg] = useState("");
  const [success, setSuccess] = useState(false);
  const [newResumeFile, setNewResumeFile] = useState(null);
  const phoneUtil = PhoneNumberUtil.getInstance();

  const { t } = useTranslation();

  const useContactFormHook = useContactForm(setSuccess, setErrMsg);

  const useSaveFileHook = useSaveFile();

  let uuidResume;
  let uuidResumeFileName;
  let formData = new FormData();

  const handleSubmit = async (values, { resetForm }) => {
    const nonEmptyValues = {
      ...Object.fromEntries(
        Object.entries(values).filter(
          ([key, value]) =>
            key !== "acceptTerms" &&
            value !== "" &&
            value !== null &&
            value !== undefined
        )
      ),
    };

    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: "join_us_form",
      button_id: "my_button",
    });

    await useContactFormHook.mutateAsync({
      ...nonEmptyValues,
      to: [`${process.env.NEXT_PUBLIC_EMAIL_FORM_JOINUS}`],
      team: "digital",
      type: "joinUs",
    });
    resetForm();
    setNewResumeFile(null);
    setTimeout(() => {
      setSuccess(false);
    }, 3000);
  };

  const initialValues = {
    fullName: "",
    email: "",
    phone: "",
    field: "",
    subject: "",
    message: "",
    jobTitle: "",
    companyName: "",
    acceptTerms: false,
    mission: "",
    resume: "",
  };

  const handleResumeChange = (e, setFieldValue) => {
    uuidResume = uuidv4().replace(/-/g, "");
    setErrMsg("");
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      formData.append("file", selectedFile);
      const extension = selectedFile.name.split(".").pop();
      uuidResumeFileName = `${uuidResume}.${extension}`;
      const currentYear = new Date().getFullYear();

      useSaveFileHook.mutate(
        {
          resource: "candidates",
          folder: currentYear,
          filename: uuidResume,
          body: { formData },
        },
        {
          onSuccess: (data) => {
            if (data.message === "uuid exist") {
              setNewResumeFile(data.uuid);
              setFieldValue("resume", data.uuid);
            } else {
              setNewResumeFile(uuidResumeFileName);
              setFieldValue("resume", uuidResumeFileName);
            }
          },
          onError: (error) => {
            setErrMsg(error.message);
          },
        }
      );
    }
  };
  const isPhoneValid = (phone) => {
    try {
      return phoneUtil.isValidNumber(phoneUtil.parseAndKeepRawInput(phone));
    } catch (error) {
      return false;
    }
  };
  const phoneValidationSchema = Yup.string().test(
    "is-valid-phone",
    t("validations:phoneFormat"),
    (value) => isPhoneValid(value)
  );
  const combinedValidationSchema = (t) =>
    joinUsValidationSchema(t).shape({
      phone: phoneValidationSchema,
    });
  return (
    <div id="service-page-form">
      <GTM />
      <Container className="custom-max-width">
        <h2 className="heading-h1 text-white text-center">
          {t("joinUs:form:title1")}{" "}
          <span className="text-yellow">{t("joinUs:form:title2")}</span>
        </h2>
        <p className="sub-heading text-white text-center">
          {t("joinUs:form:description")}
        </p>
        <Formik
          initialValues={initialValues}
          validationSchema={() => combinedValidationSchema(t)}
          onSubmit={handleSubmit}
        >
          {({ values, handleChange, errors, touched, setFieldValue }) => (
            <Form className="pentabell-form">
              <Grid container rowSpacing={4} columnSpacing={3}>
                <Grid item xs={12} sm={6}>
                  <FormGroup className="form-group light">
                    <FormLabel className="label-pentabell light">
                      {t("joinUs:form:fullName")}*
                    </FormLabel>
                    <TextField
                      autoComplete="off"
                      className="input-pentabell light"
                      placeholder={t("joinUs:form:fullName")}
                      variant="standard"
                      type="text"
                      name="fullName"
                      value={values.fullName}
                      onChange={handleChange}
                      error={!!(errors.fullName && touched.fullName)}
                    />
                  </FormGroup>
                  <ErrorMessage name="fullName">
                    {(msg) => (
                      <Alert variant="filled" severity="error">
                        {msg}
                      </Alert>
                    )}
                  </ErrorMessage>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormGroup className="form-group light">
                    <FormLabel className="label-pentabell light">
                      {t("joinUs:form:email")}*
                    </FormLabel>
                    <TextField
                      autoComplete="off"
                      className="input-pentabell light"
                      placeholder="Email"
                      variant="standard"
                      type="email"
                      name="email"
                      value={values.email}
                      onChange={handleChange}
                      error={!!(errors.email && touched.email)}
                    />
                  </FormGroup>
                  <ErrorMessage name="email">
                    {(msg) => (
                      <Alert variant="filled" severity="error">
                        {msg}
                      </Alert>
                    )}
                  </ErrorMessage>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormGroup className="form-group light">
                    <FormLabel className="label-pentabell light">
                      {t("joinUs:form:phoneNumber")}
                    </FormLabel>
                    <PhoneInput
                      defaultCountry="fr"
                      className="input-pentabell light"
                      value={values.phone}
                      onChange={(phoneNumber) => {
                        setFieldValue("phone", phoneNumber);
                        setErrMsg("");
                      }}
                      flagComponent={(props) => <LazyLoadFlag {...props} />}
                    />
                  </FormGroup>
                  <ErrorMessage name="phone">
                    {(msg) => (
                      <Alert variant="filled" severity="error">
                        {msg}
                      </Alert>
                    )}
                  </ErrorMessage>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormGroup className="form-group light">
                    <FormLabel className="label-pentabell light">
                      {t("consultingServices:servicePageForm:field")}*
                    </FormLabel>
                    <Autocomplete
                      className="input-pentabell light"
                      id="tags-standard"
                      options={[
                        "Human Resources",
                        "Administration",
                        "sourcing",
                        "Finance",
                        "Sales",
                        "Marketing",
                        "Developement",
                        "Other",
                      ]}
                      getOptionLabel={(option) => option}
                      name="field"
                      value={values.field}
                      onChange={(event, newValue) => {
                        setFieldValue("field", newValue);
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          className="input-pentabell multiple-select  light"
                          variant="standard"
                          placeholder={t(
                            "consultingServices:servicePageForm:chooseOne"
                          )}
                          error={!!(errors.field && touched.field)}
                        />
                      )}
                    />
                  </FormGroup>
                  <ErrorMessage name="field">
                    {(msg) => (
                      <Alert variant="filled" severity="error">
                        {msg}
                      </Alert>
                    )}
                  </ErrorMessage>
                </Grid>
                <Grid item xs={12} sm={12}>
                  <FormGroup className="form-group light">
                    <FormLabel className="label-pentabell light">
                      {t("joinUs:form:subject")}*
                    </FormLabel>
                    <TextField
                      autoComplete="off"
                      className="input-pentabell light"
                      placeholder={t("joinUs:form:subject")}
                      variant="standard"
                      type="text"
                      name="subject"
                      value={values.subject}
                      onChange={handleChange}
                      error={!!(errors.subject && touched.subject)}
                    />
                  </FormGroup>
                  <ErrorMessage name="subject">
                    {(msg) => (
                      <Alert variant="filled" severity="error">
                        {msg}
                      </Alert>
                    )}
                  </ErrorMessage>
                </Grid>
                <Grid item xs={12} sm={12}>
                  <FormGroup className="form-group light">
                    <FormLabel className="label-pentabell light">
                      {t("joinUs:form:message")}*
                    </FormLabel>
                    <TextField
                      autoComplete="off"
                      className="input-pentabell light"
                      placeholder="Message"
                      variant="standard"
                      type="text"
                      name="message"
                      value={values.message}
                      onChange={handleChange}
                      error={!!(errors.message && touched.message)}
                    />
                  </FormGroup>
                  <ErrorMessage name="message">
                    {(msg) => (
                      <Alert variant="filled" severity="error">
                        {msg}
                      </Alert>
                    )}
                  </ErrorMessage>
                </Grid>
                <Grid item xs={12} sm={12}>
                  <FormGroup className="form-group light flex-row-center">
                    <FormLabel
                      id="mission-radio-btn"
                      className="label-pentabell light"
                    >
                      {t("joinUs:form:mission")}*
                    </FormLabel>
                    <RadioGroup
                      row
                      aria-labelledby="mission-radio-btn"
                      name="mission"
                      value={String(values.mission)} // Convert boolean to string
                      onChange={(e) =>
                        setFieldValue("mission", e.target.value === "true")
                      } // Convert back to boolean
                    >
                      <FormControlLabel
                        value="true"
                        className="label-pentabell light"
                        control={<Radio />}
                        label={t("joinUs:form:yes")}
                      />
                      <FormControlLabel
                        value="false"
                        className="label-pentabell light"
                        control={<Radio />}
                        label={t("joinUs:form:no")}
                      />
                    </RadioGroup>
                    <ErrorMessage name="mission">
                      {(msg) => (
                        <Alert variant="filled" severity="error">
                          {msg}
                        </Alert>
                      )}
                    </ErrorMessage>
                  </FormGroup>
                </Grid>
                <Grid item xs={12} sm={12}>
                  <FormGroup className="form-group light form-section">
                    <div
                      className="custom-file-upload"
                      onClick={() =>
                        document.getElementById("file-upload").click()
                      } // Trigger file input click
                    >
                      <div>
                        <SvgUploadIcon />
                        {newResumeFile ? (
                          <>
                            <FormLabel className="label-pentabell light">
                              {t("joinUs:form:uploadCv")}*
                            </FormLabel>
                            <p className="sub-label">{newResumeFile}</p>
                          </>
                        ) : (
                          <>
                            <FormLabel className="label-pentabell light">
                              {t("joinUs:form:uploadCv")}
                            </FormLabel>
                            <p className="sub-label">
                              {t("joinUs:form:control")}
                            </p>
                          </>
                        )}
                        <CustomButton
                          text={"Choose a file"}
                          className={"btn btn-outlined white"}
                        />
                        {errMsg && (
                          <Alert variant="filled" severity="error">
                            {errMsg}
                          </Alert>
                        )}
                      </div>
                      <input
                        id="file-upload"
                        type="file"
                        name="resume"
                        accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
                        style={{ display: "none" }}
                        onChange={(e) => {
                          handleResumeChange(e, setFieldValue);
                        }}
                        error={!!(errors.resume && touched.resume)}
                      />
                    </div>
                  </FormGroup>
                  <ErrorMessage name="resume">
                    {(msg) => (
                      <Alert variant="filled" severity="error">
                        {msg}
                      </Alert>
                    )}
                  </ErrorMessage>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <FormControlLabel
                    className="checkbox-pentabell light"
                    control={
                      <Checkbox
                        name="acceptTerms"
                        checked={values.acceptTerms}
                        onChange={handleChange}
                        error={!!(errors.acceptTerms && touched.acceptTerms)}
                      />
                    }
                    label={t(
                      "payrollService:servicePageForm:formSubmissionAgreement"
                    )}
                  />
                  <ErrorMessage name="acceptTerms">
                    {(msg) => (
                      <Alert variant="filled" severity="error">
                        {msg}
                      </Alert>
                    )}
                  </ErrorMessage>
                </Grid>

                <Grid item xs={12} sm={4} className="flex-end">
                  <CustomButton
                    text={t("joinUs:form:send")}
                    className={"btn btn-filled btn-submit"}
                    type="submit"
                  />
                </Grid>
              </Grid>
            </Form>
          )}
        </Formik>
        <AlertMessage errMsg={errMsg} success={success} />
      </Container>
    </div>
  );
}

export default ConnectingTalentForm;
