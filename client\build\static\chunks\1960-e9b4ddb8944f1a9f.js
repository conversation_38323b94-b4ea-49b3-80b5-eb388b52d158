(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1960],{15735:function(e,t,r){"use strict";r.d(t,{Z:function(){return L}});var a=r(2265),n=r(61994),o=r(20801),i=r(82590),l=r(16210),s=r(76301),u=r(37053),d=r(79114),c=r(85657),p=r(3858),f=r(53410),m=r(94143),h=r(50738);function y(e){return(0,h.ZP)("MuiAlert",e)}let g=(0,m.Z)("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]);var C=r(59832),v=r(32464),b=r(57437),w=(0,v.Z)((0,b.jsx)("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"}),"SuccessOutlined"),x=(0,v.Z)((0,b.jsx)("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"}),"ReportProblemOutlined"),S=(0,v.Z)((0,b.jsx)("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),"ErrorOutline"),k=(0,v.Z)((0,b.jsx)("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"}),"InfoOutlined"),D=r(14625);let N=e=>{let{variant:t,color:r,severity:a,classes:n}=e,i={root:["root",`color${(0,c.Z)(r||a)}`,`${t}${(0,c.Z)(r||a)}`,`${t}`],icon:["icon"],message:["message"],action:["action"]};return(0,o.Z)(i,y,n)},$=(0,l.ZP)(f.Z,{name:"MuiAlert",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],t[`${r.variant}${(0,c.Z)(r.color||r.severity)}`]]}})((0,s.Z)(e=>{let{theme:t}=e,r="light"===t.palette.mode?i._j:i.$n,a="light"===t.palette.mode?i.$n:i._j;return{...t.typography.body2,backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(t.palette).filter((0,p.Z)(["light"])).map(e=>{let[n]=e;return{props:{colorSeverity:n,variant:"standard"},style:{color:t.vars?t.vars.palette.Alert[`${n}Color`]:r(t.palette[n].light,.6),backgroundColor:t.vars?t.vars.palette.Alert[`${n}StandardBg`]:a(t.palette[n].light,.9),[`& .${g.icon}`]:t.vars?{color:t.vars.palette.Alert[`${n}IconColor`]}:{color:t.palette[n].main}}}}),...Object.entries(t.palette).filter((0,p.Z)(["light"])).map(e=>{let[a]=e;return{props:{colorSeverity:a,variant:"outlined"},style:{color:t.vars?t.vars.palette.Alert[`${a}Color`]:r(t.palette[a].light,.6),border:`1px solid ${(t.vars||t).palette[a].light}`,[`& .${g.icon}`]:t.vars?{color:t.vars.palette.Alert[`${a}IconColor`]}:{color:t.palette[a].main}}}}),...Object.entries(t.palette).filter((0,p.Z)(["dark"])).map(e=>{let[r]=e;return{props:{colorSeverity:r,variant:"filled"},style:{fontWeight:t.typography.fontWeightMedium,...t.vars?{color:t.vars.palette.Alert[`${r}FilledColor`],backgroundColor:t.vars.palette.Alert[`${r}FilledBg`]}:{backgroundColor:"dark"===t.palette.mode?t.palette[r].dark:t.palette[r].main,color:t.palette.getContrastText(t.palette[r].main)}}}})]}})),A=(0,l.ZP)("div",{name:"MuiAlert",slot:"Icon",overridesResolver:(e,t)=>t.icon})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),M=(0,l.ZP)("div",{name:"MuiAlert",slot:"Message",overridesResolver:(e,t)=>t.message})({padding:"8px 0",minWidth:0,overflow:"auto"}),P=(0,l.ZP)("div",{name:"MuiAlert",slot:"Action",overridesResolver:(e,t)=>t.action})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),E={success:(0,b.jsx)(w,{fontSize:"inherit"}),warning:(0,b.jsx)(x,{fontSize:"inherit"}),error:(0,b.jsx)(S,{fontSize:"inherit"}),info:(0,b.jsx)(k,{fontSize:"inherit"})};var L=a.forwardRef(function(e,t){let r=(0,u.i)({props:e,name:"MuiAlert"}),{action:a,children:o,className:i,closeText:l="Close",color:s,components:c={},componentsProps:p={},icon:f,iconMapping:m=E,onClose:h,role:y="alert",severity:g="success",slotProps:v={},slots:w={},variant:x="standard",...S}=r,k={...r,color:s,severity:g,variant:x,colorSeverity:s||g},L=N(k),I={slots:{closeButton:c.CloseButton,closeIcon:c.CloseIcon,...w},slotProps:{...p,...v}},[z,j]=(0,d.Z)("root",{ref:t,shouldForwardComponentProp:!0,className:(0,n.Z)(L.root,i),elementType:$,externalForwardedProps:{...I,...S},ownerState:k,additionalProps:{role:y,elevation:0}}),[Z,_]=(0,d.Z)("icon",{className:L.icon,elementType:A,externalForwardedProps:I,ownerState:k}),[O,B]=(0,d.Z)("message",{className:L.message,elementType:M,externalForwardedProps:I,ownerState:k}),[R,T]=(0,d.Z)("action",{className:L.action,elementType:P,externalForwardedProps:I,ownerState:k}),[F,V]=(0,d.Z)("closeButton",{elementType:C.Z,externalForwardedProps:I,ownerState:k}),[G,W]=(0,d.Z)("closeIcon",{elementType:D.Z,externalForwardedProps:I,ownerState:k});return(0,b.jsxs)(z,{...j,children:[!1!==f?(0,b.jsx)(Z,{..._,children:f||m[g]||E[g]}):null,(0,b.jsx)(O,{...B,children:o}),null!=a?(0,b.jsx)(R,{...T,children:a}):null,null==a&&h?(0,b.jsx)(R,{...T,children:(0,b.jsx)(F,{size:"small","aria-label":l,title:l,color:"inherit",onClick:h,...V,children:(0,b.jsx)(G,{fontSize:"small",...W})})}):null]})})},11953:function(e,t,r){"use strict";r.d(t,{Z:function(){return P}});var a=r(2265),n=r(61994),o=r(20801),i=r(82590),l=r(66183),s=r(32464),u=r(57437),d=(0,s.Z)((0,u.jsx)("path",{d:"M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"}),"CheckBoxOutlineBlank"),c=(0,s.Z)((0,u.jsx)("path",{d:"M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"}),"CheckBox"),p=(0,s.Z)((0,u.jsx)("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10H7v-2h10v2z"}),"IndeterminateCheckBox"),f=r(85657),m=r(34765),h=r(94143),y=r(50738);function g(e){return(0,y.ZP)("MuiCheckbox",e)}let C=(0,h.Z)("MuiCheckbox",["root","checked","disabled","indeterminate","colorPrimary","colorSecondary","sizeSmall","sizeMedium"]);var v=r(16210),b=r(76301),w=r(3858),x=r(37053),S=r(17419),k=r(79114);let D=e=>{let{classes:t,indeterminate:r,color:a,size:n}=e,i={root:["root",r&&"indeterminate",`color${(0,f.Z)(a)}`,`size${(0,f.Z)(n)}`]},l=(0,o.Z)(i,g,t);return{...t,...l}},N=(0,v.ZP)(l.Z,{shouldForwardProp:e=>(0,m.Z)(e)||"classes"===e,name:"MuiCheckbox",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.indeterminate&&t.indeterminate,t[`size${(0,f.Z)(r.size)}`],"default"!==r.color&&t[`color${(0,f.Z)(r.color)}`]]}})((0,b.Z)(e=>{let{theme:t}=e;return{color:(t.vars||t).palette.text.secondary,variants:[{props:{color:"default",disableRipple:!1},style:{"&:hover":{backgroundColor:t.vars?`rgba(${t.vars.palette.action.activeChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,i.Fq)(t.palette.action.active,t.palette.action.hoverOpacity)}}},...Object.entries(t.palette).filter((0,w.Z)()).map(e=>{let[r]=e;return{props:{color:r,disableRipple:!1},style:{"&:hover":{backgroundColor:t.vars?`rgba(${t.vars.palette[r].mainChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,i.Fq)(t.palette[r].main,t.palette.action.hoverOpacity)}}}}),...Object.entries(t.palette).filter((0,w.Z)()).map(e=>{let[r]=e;return{props:{color:r},style:{[`&.${C.checked}, &.${C.indeterminate}`]:{color:(t.vars||t).palette[r].main},[`&.${C.disabled}`]:{color:(t.vars||t).palette.action.disabled}}}}),{props:{disableRipple:!1},style:{"&:hover":{"@media (hover: none)":{backgroundColor:"transparent"}}}}]}})),$=(0,u.jsx)(c,{}),A=(0,u.jsx)(d,{}),M=(0,u.jsx)(p,{});var P=a.forwardRef(function(e,t){let r=(0,x.i)({props:e,name:"MuiCheckbox"}),{checkedIcon:o=$,color:i="primary",icon:l=A,indeterminate:s=!1,indeterminateIcon:d=M,inputProps:c,size:p="medium",disableRipple:f=!1,className:m,slots:h={},slotProps:y={},...g}=r,C=s?d:l,v=s?d:o,b={...r,disableRipple:f,color:i,indeterminate:s,size:p},w=D(b),P=y.input??c,[E,L]=(0,k.Z)("root",{ref:t,elementType:N,className:(0,n.Z)(w.root,m),shouldForwardComponentProp:!0,externalForwardedProps:{slots:h,slotProps:y,...g},ownerState:b,additionalProps:{type:"checkbox",icon:a.cloneElement(C,{fontSize:C.props.fontSize??p}),checkedIcon:a.cloneElement(v,{fontSize:v.props.fontSize??p}),disableRipple:f,slots:h,slotProps:{input:(0,S.Z)("function"==typeof P?P(b):P,{"data-indeterminate":s})}}});return(0,u.jsx)(E,{...L,classes:w})})},64821:function(e,t,r){"use strict";var a=r(13859),n=r(53731);t.isCompanyEmail=function(e){if(!n.validate(e))return!1;let t=e.split("@")[1];return!a.has(t)},t.isCompanyDomain=function(e){return!a.has(e)}},53731:function(e,t){"use strict";var r=/^[-!#$%&'*+\/0-9=?A-Z^_a-z{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/;t.validate=function(e){if(!e||e.length>254||!r.test(e))return!1;var t=e.split("@");return!(t[0].length>64||t[1].split(".").some(function(e){return e.length>63}))}},69780:function(e,t,r){"use strict";var a,n=(a=r(78227))&&a.__esModule?a:{default:a};e.exports={tags:function(e){var t=e.id,r=e.events,a=e.dataLayer,o=e.dataLayerName,i=e.preview,l="&gtm_auth="+e.auth,s="&gtm_preview="+i;t||(0,n.default)("GTM Id is required");var u="\n      (function(w,d,s,l,i){w[l]=w[l]||[];\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js', "+JSON.stringify(r).slice(1,-1)+"});\n        var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';\n        j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl+'"+l+s+"&gtm_cookies_win=x';\n        f.parentNode.insertBefore(j,f);\n      })(window,document,'script','"+o+"','"+t+"');";return{iframe:'\n      <iframe src="https://www.googletagmanager.com/ns.html?id='+t+l+s+'&gtm_cookies_win=x"\n        height="0" width="0" style="display:none;visibility:hidden" id="tag-manager"></iframe>',script:u,dataLayerVar:this.dataLayer(a,o)}},dataLayer:function(e,t){return"\n      window."+t+" = window."+t+" || [];\n      window."+t+".push("+JSON.stringify(e)+")"}}},90761:function(e,t,r){"use strict";var a,n=(a=r(69780))&&a.__esModule?a:{default:a};e.exports={dataScript:function(e){var t=document.createElement("script");return t.innerHTML=e,t},gtm:function(e){var t=n.default.tags(e);return{noScript:function(){var e=document.createElement("noscript");return e.innerHTML=t.iframe,e},script:function(){var e=document.createElement("script");return e.innerHTML=t.script,e},dataScript:this.dataScript(t.dataLayerVar)}},initialize:function(e){var t=e.gtmId,r=e.events,a=e.dataLayer,n=e.dataLayerName,o=e.auth,i=e.preview,l=this.gtm({id:t,events:void 0===r?{}:r,dataLayer:a||void 0,dataLayerName:void 0===n?"dataLayer":n,auth:void 0===o?"":o,preview:void 0===i?"":i});a&&document.head.appendChild(l.dataScript),document.head.insertBefore(l.script(),document.head.childNodes[0]),document.body.insertBefore(l.noScript(),document.body.childNodes[0])},dataLayer:function(e){var t=e.dataLayer,r=e.dataLayerName,a=void 0===r?"dataLayer":r;if(window[a])return window[a].push(t);var o=n.default.dataLayer(t,a),i=this.dataScript(o);document.head.insertBefore(i,document.head.childNodes[0])}}},4828:function(e,t,r){"use strict";var a,n=(a=r(90761))&&a.__esModule?a:{default:a};e.exports=n.default},78227:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){console.warn("[react-gtm]",e)}},25330:function(){},61984:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let a={active:!0,breakpoints:{},delay:4e3,jump:!1,playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,stopOnLastSnap:!1,rootNode:null};function n(e={}){let t,r,o,i;let l=null,s=0,u=!1,d=!1,c=!1,p=!1;function f(){if(!o){if(y()){c=!0;return}u||r.emit("autoplay:play"),function(){let{ownerWindow:e}=r.internalEngine();e.clearTimeout(s),s=e.setTimeout(w,i[r.selectedScrollSnap()]),l=new Date().getTime(),r.emit("autoplay:timerset")}(),u=!0}}function m(){o||(u&&r.emit("autoplay:stop"),function(){let{ownerWindow:e}=r.internalEngine();e.clearTimeout(s),s=0,l=null,r.emit("autoplay:timerstopped")}(),u=!1)}function h(){if(y())return c=u,m();c&&f()}function y(){let{ownerDocument:e}=r.internalEngine();return"hidden"===e.visibilityState}function g(){d||m()}function C(){d||f()}function v(){d=!0,m()}function b(){d=!1,f()}function w(){let{index:e}=r.internalEngine(),a=e.clone().add(1).get(),n=r.scrollSnapList().length-1,o=t.stopOnLastSnap&&a===n;if(r.canScrollNext()?r.scrollNext(p):r.scrollTo(0,p),r.emit("autoplay:select"),o)return m();f()}return{name:"autoplay",options:e,init:function(l,s){r=l;let{mergeOptions:u,optionsAtMedia:d}=s,c=u(a,n.globalOptions);if(t=d(u(c,e)),r.scrollSnapList().length<=1)return;p=t.jump,o=!1,i=function(e,t){let r=e.scrollSnapList();return"number"==typeof t?r.map(()=>t):t(r,e)}(r,t.delay);let{eventStore:y,ownerDocument:w}=r.internalEngine(),x=!!r.internalEngine().options.watchDrag,S=function(e,t){let r=e.rootNode();return t&&t(r)||r}(r,t.rootNode);y.add(w,"visibilitychange",h),x&&r.on("pointerDown",g),x&&!t.stopOnInteraction&&r.on("pointerUp",C),t.stopOnMouseEnter&&y.add(S,"mouseenter",v),t.stopOnMouseEnter&&!t.stopOnInteraction&&y.add(S,"mouseleave",b),t.stopOnFocusIn&&r.on("slideFocusStart",m),t.stopOnFocusIn&&!t.stopOnInteraction&&y.add(r.containerNode(),"focusout",f),t.playOnInit&&f()},destroy:function(){r.off("pointerDown",g).off("pointerUp",C).off("slideFocusStart",m),m(),o=!0,u=!1},play:function(e){void 0!==e&&(p=e),f()},stop:function(){u&&m()},reset:function(){u&&f()},isPlaying:function(){return u},timeUntilNext:function(){return l?i[r.selectedScrollSnap()]-(new Date().getTime()-l):null}}}n.globalOptions=void 0},24086:function(e,t,r){"use strict";r.d(t,{sb:function(){return q}});var a=r(2265),n=[["Afghanistan","af","93"],["Albania","al","355"],["Algeria","dz","213"],["Andorra","ad","376"],["Angola","ao","244"],["Antigua and Barbuda","ag","1268"],["Argentina","ar","54","(..) ........",0],["Armenia","am","374",".. ......"],["Aruba","aw","297"],["Australia","au","61",{default:". .... ....","/^4/":"... ... ...","/^5(?!50)/":"... ... ...","/^1(3|8)00/":".... ... ...","/^13/":".. .. ..","/^180/":"... ...."},0,[]],["Austria","at","43"],["Azerbaijan","az","994","(..) ... .. .."],["Bahamas","bs","1242"],["Bahrain","bh","973"],["Bangladesh","bd","880"],["Barbados","bb","1246"],["Belarus","by","375","(..) ... .. .."],["Belgium","be","32","... .. .. .."],["Belize","bz","501"],["Benin","bj","229"],["Bhutan","bt","975"],["Bolivia","bo","591"],["Bosnia and Herzegovina","ba","387"],["Botswana","bw","267"],["Brazil","br","55","(..) .....-...."],["British Indian Ocean Territory","io","246"],["Brunei","bn","673"],["Bulgaria","bg","359"],["Burkina Faso","bf","226"],["Burundi","bi","257"],["Cambodia","kh","855"],["Cameroon","cm","237"],["Canada","ca","1","(...) ...-....",1,["204","226","236","249","250","289","306","343","365","387","403","416","418","431","437","438","450","506","514","519","548","579","581","587","604","613","639","647","672","705","709","742","778","780","782","807","819","825","867","873","902","905"]],["Cape Verde","cv","238"],["Caribbean Netherlands","bq","599","",1],["Cayman Islands","ky","1","... ... ....",4,["345"]],["Central African Republic","cf","236"],["Chad","td","235"],["Chile","cl","56"],["China","cn","86","... .... ...."],["Colombia","co","57","... ... ...."],["Comoros","km","269"],["Congo","cd","243"],["Congo","cg","242"],["Costa Rica","cr","506","....-...."],["C\xf4te d'Ivoire","ci","225",".. .. .. .. .."],["Croatia","hr","385"],["Cuba","cu","53"],["Cura\xe7ao","cw","599","",0],["Cyprus","cy","357",".. ......"],["Czech Republic","cz","420","... ... ..."],["Denmark","dk","45",".. .. .. .."],["Djibouti","dj","253",".. .. ...."],["Dominica","dm","1767"],["Dominican Republic","do","1","(...) ...-....",2,["809","829","849"]],["Ecuador","ec","593"],["Egypt","eg","20"],["El Salvador","sv","503","....-...."],["Equatorial Guinea","gq","240"],["Eritrea","er","291"],["Estonia","ee","372",".... ......"],["Ethiopia","et","251",".. ... ...."],["Fiji","fj","679"],["Finland","fi","358",".. ... .. .."],["France","fr","33",". .. .. .. .."],["French Guiana","gf","594"],["French Polynesia","pf","689"],["Gabon","ga","241"],["Gambia","gm","220"],["Georgia","ge","995"],["Germany","de","49","... ........."],["Ghana","gh","233"],["Greece","gr","30"],["Greenland","gl","299",".. .. .."],["Grenada","gd","1473"],["Guadeloupe","gp","590","",0],["Guam","gu","1671"],["Guatemala","gt","502","....-...."],["Guinea","gn","224"],["Guinea-Bissau","gw","245"],["Guyana","gy","592"],["Haiti","ht","509","....-...."],["Honduras","hn","504"],["Hong Kong","hk","852",".... ...."],["Hungary","hu","36"],["Iceland","is","354","... ...."],["India","in","91",".....-....."],["Indonesia","id","62"],["Iran","ir","98","... ... ...."],["Iraq","iq","964"],["Ireland","ie","353",".. ......."],["Israel","il","972","... ... ...."],["Italy","it","39","... .......",0],["Jamaica","jm","1876"],["Japan","jp","81",".. .... ...."],["Jordan","jo","962"],["Kazakhstan","kz","7","... ...-..-..",0],["Kenya","ke","254"],["Kiribati","ki","686"],["Kosovo","xk","383"],["Kuwait","kw","965"],["Kyrgyzstan","kg","996","... ... ..."],["Laos","la","856"],["Latvia","lv","371",".. ... ..."],["Lebanon","lb","961"],["Lesotho","ls","266"],["Liberia","lr","231"],["Libya","ly","218"],["Liechtenstein","li","423"],["Lithuania","lt","370"],["Luxembourg","lu","352"],["Macau","mo","853"],["Macedonia","mk","389"],["Madagascar","mg","261"],["Malawi","mw","265"],["Malaysia","my","60","..-....-...."],["Maldives","mv","960"],["Mali","ml","223"],["Malta","mt","356"],["Marshall Islands","mh","692"],["Martinique","mq","596"],["Mauritania","mr","222"],["Mauritius","mu","230"],["Mayotte","yt","262","",1,["269","639"]],["Mexico","mx","52","... ... ....",0],["Micronesia","fm","691"],["Moldova","md","373","(..) ..-..-.."],["Monaco","mc","377"],["Mongolia","mn","976"],["Montenegro","me","382"],["Morocco","ma","212"],["Mozambique","mz","258"],["Myanmar","mm","95"],["Namibia","na","264"],["Nauru","nr","674"],["Nepal","np","977"],["Netherlands","nl","31",{"/^06/":"(.). .........","/^6/":". .........","/^0(10|13|14|15|20|23|24|26|30|33|35|36|38|40|43|44|45|46|50|53|55|58|70|71|72|73|74|75|76|77|78|79|82|84|85|87|88|91)/":"(.).. ........","/^(10|13|14|15|20|23|24|26|30|33|35|36|38|40|43|44|45|46|50|53|55|58|70|71|72|73|74|75|76|77|78|79|82|84|85|87|88|91)/":".. ........","/^0/":"(.)... .......",default:"... ......."}],["New Caledonia","nc","687"],["New Zealand","nz","64","...-...-...."],["Nicaragua","ni","505"],["Niger","ne","227"],["Nigeria","ng","234"],["North Korea","kp","850"],["Norway","no","47","... .. ..."],["Oman","om","968"],["Pakistan","pk","92","...-......."],["Palau","pw","680"],["Palestine","ps","970"],["Panama","pa","507"],["Papua New Guinea","pg","675"],["Paraguay","py","595"],["Peru","pe","51"],["Philippines","ph","63","... ... ...."],["Poland","pl","48","...-...-..."],["Portugal","pt","351"],["Puerto Rico","pr","1","(...) ...-....",3,["787","939"]],["Qatar","qa","974"],["R\xe9union","re","262","",0],["Romania","ro","40"],["Russia","ru","7","(...) ...-..-..",1],["Rwanda","rw","250"],["Saint Kitts and Nevis","kn","1869"],["Saint Lucia","lc","1758"],["Saint Vincent and the Grenadines","vc","1784"],["Samoa","ws","685"],["San Marino","sm","378"],["S\xe3o Tom\xe9 and Pr\xedncipe","st","239"],["Saudi Arabia","sa","966"],["Senegal","sn","221"],["Serbia","rs","381"],["Seychelles","sc","248"],["Sierra Leone","sl","232"],["Singapore","sg","65","....-...."],["Slovakia","sk","421"],["Slovenia","si","386"],["Solomon Islands","sb","677"],["Somalia","so","252"],["South Africa","za","27"],["South Korea","kr","82","... .... ...."],["South Sudan","ss","211"],["Spain","es","34","... ... ..."],["Sri Lanka","lk","94"],["Sudan","sd","249"],["Suriname","sr","597"],["Swaziland","sz","268"],["Sweden","se","46","... ... ..."],["Switzerland","ch","41",".. ... .. .."],["Syria","sy","963"],["Taiwan","tw","886"],["Tajikistan","tj","992"],["Tanzania","tz","255"],["Thailand","th","66"],["Timor-Leste","tl","670"],["Togo","tg","228"],["Tonga","to","676"],["Trinidad and Tobago","tt","1868"],["Tunisia","tn","216"],["Turkey","tr","90","... ... .. .."],["Turkmenistan","tm","993"],["Tuvalu","tv","688"],["Uganda","ug","256"],["Ukraine","ua","380","(..) ... .. .."],["United Arab Emirates","ae","971"],["United Kingdom","gb","44",".... ......"],["United States","us","1","(...) ...-....",0],["Uruguay","uy","598"],["Uzbekistan","uz","998",".. ... .. .."],["Vanuatu","vu","678"],["Vatican City","va","39",".. .... ....",1],["Venezuela","ve","58"],["Vietnam","vn","84"],["Yemen","ye","967"],["Zambia","zm","260"],["Zimbabwe","zw","263"]],o=(...e)=>e.filter(e=>!!e).join(" ").trim(),i=(...e)=>o(...e).split(" ").map(e=>`react-international-phone-${e}`).join(" "),l=({addPrefix:e,rawClassNames:t})=>o(i(...e),...t),s=({value:e,mask:t,maskSymbol:r,offset:a=0,trimNonMaskCharsLeftover:n=!1})=>{if(e.length<a)return e;let o=e.slice(0,a),i=e.slice(a),l=o,s=0;for(let e of t.split("")){if(s>=i.length){if(!n&&e!==r){l+=e;continue}break}e===r?(l+=i[s],s+=1):l+=e}return l},u=e=>!!e&&/^\d+$/.test(e),d=e=>e.replace(/\D/g,""),c=(e,t)=>{let r=e.style.display;"block"!==r&&(e.style.display="block");let a=e.getBoundingClientRect(),n=t.getBoundingClientRect(),o=n.top-a.top,i=a.bottom-n.bottom;o>=0&&i>=0||(Math.abs(o)<Math.abs(i)?e.scrollTop+=o:e.scrollTop-=i),e.style.display=r},p=()=>!(typeof window>"u")&&window.navigator.userAgent.toLowerCase().includes("macintosh"),f=(e,t)=>{let r,a=!t.disableDialCodeAndPrefix&&t.forceDialCode,n=!t.disableDialCodeAndPrefix&&t.insertDialCodeOnEmpty,o=e,i=e=>t.trimNonDigitsEnd?e.trim():e;if(!o)return i(n&&!o.length||a?`${t.prefix}${t.dialCode}${t.charAfterDialCode}`:o);if((o=d(o))===t.dialCode&&!t.disableDialCodeAndPrefix)return i(`${t.prefix}${t.dialCode}${t.charAfterDialCode}`);if(t.dialCode.startsWith(o)&&!t.disableDialCodeAndPrefix)return i(a?`${t.prefix}${t.dialCode}${t.charAfterDialCode}`:`${t.prefix}${o}`);if(!o.startsWith(t.dialCode)&&!t.disableDialCodeAndPrefix){if(a)return i(`${t.prefix}${t.dialCode}${t.charAfterDialCode}`);if(o.length<t.dialCode.length)return i(`${t.prefix}${o}`)}let{phoneLeftSide:l,phoneRightSide:u}=(r=t.dialCode.length,{phoneLeftSide:o.slice(0,r),phoneRightSide:o.slice(r)});return l=`${t.prefix}${l}${t.charAfterDialCode}`,u=s({value:u,mask:t.mask,maskSymbol:t.maskChar,trimNonMaskCharsLeftover:t.trimNonDigitsEnd||t.disableDialCodeAndPrefix&&0===u.length}),t.disableDialCodeAndPrefix&&(l=""),i(`${l}${u}`)},m=({phoneBeforeInput:e,phoneAfterInput:t,phoneAfterFormatted:r,cursorPositionAfterInput:a,leftOffset:n=0,deletion:o})=>{if(a<n)return n;if(!e)return r.length;let i=null;for(let e=a-1;e>=0;e-=1)if(u(t[e])){i=e;break}if(null===i){for(let e=0;e<t.length;e+=1)if(u(r[e]))return e;return t.length}let l=0;for(let e=0;e<i;e+=1)u(t[e])&&(l+=1);let s=0,d=0;for(let e=0;e<r.length&&(s+=1,u(r[e])&&(d+=1),!(d>=l+1));e+=1);if("backward"!==o)for(;!u(r[s])&&s<r.length;)s+=1;return s},h=({phone:e,prefix:t})=>e?`${t}${d(e)}`:"";function y({value:e,country:t,insertDialCodeOnEmpty:r,trimNonDigitsEnd:a,countries:n,prefix:o,charAfterDialCode:i,forceDialCode:l,disableDialCodeAndPrefix:s,defaultMask:u,countryGuessingEnabled:d,disableFormatting:c}){let p=e;s&&(p=p.startsWith(`${o}`)?p:`${o}${t.dialCode}${p}`);let m=d?R({phone:p,countries:n,currentCountryIso2:t?.iso2}):void 0,y=m?.country??t,g=f(p,{prefix:o,mask:Z({phone:p,country:y,defaultMask:u,disableFormatting:c}),maskChar:x,dialCode:y.dialCode,trimNonDigitsEnd:a,charAfterDialCode:i,forceDialCode:l,insertDialCodeOnEmpty:r,disableDialCodeAndPrefix:s}),C=d&&!m?.fullDialCodeMatch?t:y;return{phone:h({phone:s?`${C.dialCode}${g}`:g,prefix:o}),inputValue:g,country:C}}var g=e=>{if(e?.toLocaleLowerCase().includes("delete"))return e?.toLocaleLowerCase().includes("forward")?"forward":"backward"},C=(e,{country:t,insertDialCodeOnEmpty:r,phoneBeforeInput:a,prefix:n,charAfterDialCode:o,forceDialCode:i,disableDialCodeAndPrefix:l,countryGuessingEnabled:s,defaultMask:d,disableFormatting:c,countries:p})=>{let f=e.nativeEvent,C=f.inputType,v=g(C),b=!!C?.startsWith("insertFrom"),w="insertText"===C,x=f?.data||void 0,S=e.target.value,k=e.target.selectionStart??0;if(C?.includes("history"))return{inputValue:a,phone:h({phone:a,prefix:n}),cursorPosition:a.length,country:t};if(w&&!u(x)&&S!==n)return{inputValue:a,phone:h({phone:l?`${t.dialCode}${a}`:a,prefix:n}),cursorPosition:k-(x?.length??0),country:t};if(i&&!S.startsWith(`${n}${t.dialCode}`)&&!b){let e=S?a:`${n}${t.dialCode}${o}`;return{inputValue:e,phone:h({phone:e,prefix:n}),cursorPosition:n.length+t.dialCode.length+o.length,country:t}}let{phone:D,inputValue:N,country:$}=y({value:S,country:t,trimNonDigitsEnd:"backward"===v,insertDialCodeOnEmpty:r,countryGuessingEnabled:s,countries:p,prefix:n,charAfterDialCode:o,forceDialCode:i,disableDialCodeAndPrefix:l,disableFormatting:c,defaultMask:d}),A=m({cursorPositionAfterInput:k,phoneBeforeInput:a,phoneAfterInput:S,phoneAfterFormatted:N,leftOffset:i?n.length+t.dialCode.length+o.length:0,deletion:v});return{phone:D,inputValue:N,cursorPosition:A,country:$}},v=(e,t)=>{let r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;for(let a of r)if(e[a]!==t[a])return!1;return!0},b=()=>{let e=(0,a.useRef)(),t=(0,a.useRef)(Date.now());return{check:()=>{let r=Date.now(),a=e.current?r-t.current:void 0;return e.current=t.current,t.current=r,a}}},w={size:20,overrideLastItemDebounceMS:-1},x=".",S="us",k="",D="+",N="............",$=" ",A=200,M=!1,P=!1,E=!1,L=!1,I=!1,z=n,j=({defaultCountry:e=S,value:t=k,countries:r=z,prefix:n=D,defaultMask:o=N,charAfterDialCode:i=$,historySaveDebounceMS:l=A,disableCountryGuess:s=M,disableDialCodePrefill:u=P,forceDialCode:d=E,disableDialCodeAndPrefix:c=L,disableFormatting:f=I,onChange:m,inputRef:h})=>{let g={countries:r,prefix:n,charAfterDialCode:i,forceDialCode:!c&&d,disableDialCodeAndPrefix:c,defaultMask:o,countryGuessingEnabled:!s,disableFormatting:f},x=(0,a.useRef)(null),j=h||x,Z=e=>{Promise.resolve().then(()=>{typeof window>"u"||j.current!==document?.activeElement||j.current?.setSelectionRange(e,e)})},[{phone:_,inputValue:O,country:R},T,F,V]=function(e,t){let{size:r,overrideLastItemDebounceMS:n,onChange:o}={...w,...t},[i,l]=(0,a.useState)(e),[s,u]=(0,a.useState)([i]),[d,c]=(0,a.useState)(0),p=b();return[i,(e,t)=>{if("object"==typeof e&&"object"==typeof i&&v(e,i)||e===i)return;let a=p.check();if(t?.overrideLastItem!==void 0?t.overrideLastItem:!(!(n>0)||void 0===a||a>n))u(t=>[...t.slice(0,d),e]);else{let t=s.length>=r;u(r=>[...r.slice(t?1:0,d+1),e]),t||c(e=>e+1)}l(e),o?.(e)},()=>{if(d<=0)return{success:!1};let e=s[d-1];return l(e),c(e=>e-1),o?.(e),{success:!0,value:e}},()=>{if(d+1>=s.length)return{success:!1};let e=s[d+1];return l(e),c(e=>e+1),o?.(e),{success:!0,value:e}}]}(()=>{let a=B({value:e,field:"iso2",countries:r});a||console.error(`[react-international-phone]: can not find a country with "${e}" iso2 code`);let{phone:n,inputValue:o,country:i}=y({value:t,country:a||B({value:"us",field:"iso2",countries:r}),insertDialCodeOnEmpty:!u,...g});return Z(o.length),{phone:n,inputValue:o,country:i.iso2}},{overrideLastItemDebounceMS:l,onChange:({inputValue:e,phone:t,country:r})=>{m&&m({phone:t,inputValue:e,country:G(r)})}}),G=(0,a.useCallback)(e=>B({value:e,field:"iso2",countries:r}),[r]),W=(0,a.useMemo)(()=>G(R),[R,G]);(0,a.useEffect)(()=>{let e=j.current;if(!e)return;let t=e=>{if(!e.key)return;let t=e.ctrlKey,r=e.metaKey,a=e.shiftKey;if("z"===e.key.toLowerCase()){if(p()){if(!r)return}else if(!t)return;a?V():F()}};return e.addEventListener("keydown",t),()=>{e.removeEventListener("keydown",t)}},[j,F,V]);let[H,K]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{if(!H){K(!0),t!==_&&m?.({inputValue:O,phone:_,country:W});return}if(t===_)return;let{phone:e,inputValue:r,country:a}=y({value:t,country:W,insertDialCodeOnEmpty:!u,...g});T({phone:e,inputValue:r,country:a.iso2})},[t]),{phone:_,inputValue:O,country:W,setCountry:(e,t={focusOnInput:!1})=>{let a=B({value:e,field:"iso2",countries:r});if(!a){console.error(`[react-international-phone]: can not find a country with "${e}" iso2 code`);return}T({inputValue:c?"":`${n}${a.dialCode}${i}`,phone:`${n}${a.dialCode}`,country:a.iso2}),t.focusOnInput&&Promise.resolve().then(()=>{j.current?.focus()})},handlePhoneValueChange:e=>{e.preventDefault();let{phone:r,inputValue:a,country:n,cursorPosition:o}=C(e,{country:W,phoneBeforeInput:O,insertDialCodeOnEmpty:!1,...g});return T({inputValue:a,phone:r,country:n.iso2}),Z(o),t},inputRef:j}},Z=({phone:e,country:t,defaultMask:r="............",disableFormatting:a=!1})=>{let n=t.format,o=e=>a?e.replace(RegExp(`[^${x}]`,"g"),""):e;if(!n)return o(r);if("string"==typeof n)return o(n);if(!n.default)return console.error(`[react-international-phone]: default mask for ${t.iso2} is not provided`),o(r);let i=Object.keys(n).find(r=>{if("default"===r)return!1;if(!("/"===r.charAt(0)&&"/"===r.charAt(r.length-1)))return console.error(`[react-international-phone]: format regex "${r}" for ${t.iso2} is not valid`),!1;let a=new RegExp(r.substring(1,r.length-1)),n=e.replace(t.dialCode,"");return a.test(d(n))});return o(i?n[i]:n.default)},_=e=>{let[t,r,a,n,o,i]=e;return{name:t,iso2:r,dialCode:a,format:n,priority:o,areaCodes:i}},O=e=>`Field "${e}" is not supported`,B=({field:e,value:t,countries:r=n})=>{if(["priority"].includes(e))throw Error(O(e));let a=r.find(r=>t===_(r)[e]);if(a)return _(a)},R=({phone:e,countries:t=n,currentCountryIso2:r})=>{let a={country:void 0,fullDialCodeMatch:!1};if(!e)return a;let o=d(e);if(!o)return a;let i=a,l=({country:e,fullDialCodeMatch:t})=>{let r=e.dialCode===i.country?.dialCode,a=(e.priority??0)<(i.country?.priority??0);(!r||a)&&(i={country:e,fullDialCodeMatch:t})};for(let e of t){let t=_(e),{dialCode:r,areaCodes:a}=t;if(o.startsWith(r)){let e=!i.country||Number(r)>=Number(i.country.dialCode);if(a){let e=o.substring(r.length);for(let r of a)if(e.startsWith(r))return{country:t,fullDialCodeMatch:!0}}(e||r===o||!i.fullDialCodeMatch)&&l({country:t,fullDialCodeMatch:!0})}i.fullDialCodeMatch||o.length<r.length&&r.startsWith(o)&&(!i.country||Number(r)<=Number(i.country.dialCode))&&l({country:t,fullDialCodeMatch:!1})}if(r){let e=B({value:r,field:"iso2",countries:t});if(!e)return i;let a=!!e&&(e=>{if(!e?.areaCodes)return!1;let t=o.substring(e.dialCode.length);return e.areaCodes.some(e=>e.startsWith(t))})(e);i&&i.country?.dialCode===e.dialCode&&i.country!==e&&i.fullDialCodeMatch&&(!e.areaCodes||a)&&(i={country:e,fullDialCodeMatch:!0})}return i},T=(e,t)=>Number(parseInt(e,16)+t).toString(16),F="abcdefghijklmnopqrstuvwxyz".split("").reduce((e,t,r)=>({...e,[t]:T("1f1e6",r)}),{}),V=e=>[F[e[0]],F[e[1]]].join("-"),G=({iso2:e,size:t,src:r,protocol:n="https",disableLazyLoading:o,className:i,style:s,...u})=>e?a.createElement("img",{className:l({addPrefix:["flag-emoji"],rawClassNames:[i]}),src:(()=>{if(r)return r;let t=V(e);return`${n}://cdnjs.cloudflare.com/ajax/libs/twemoji/14.0.2/svg/${t}.svg`})(),width:t,height:t,draggable:!1,"data-country":e,loading:o?void 0:"lazy",style:{width:t,height:t,...s},alt:"",...u}):a.createElement("img",{className:l({addPrefix:["flag-emoji"],rawClassNames:[i]}),width:t,height:t,...u}),W=({show:e,dialCodePrefix:t="+",selectedCountry:r,countries:o=n,preferredCountries:i=[],flags:s,onSelect:u,onClose:d,...p})=>{let f=(0,a.useRef)(null),m=(0,a.useRef)(),h=(0,a.useMemo)(()=>{if(!i||!i.length)return o;let e=[],t=[...o];for(let r of i){let a=t.findIndex(e=>_(e).iso2===r);if(-1!==a){let r=t.splice(a,1)[0];e.push(r)}}return e.concat(t)},[o,i]),y=(0,a.useRef)({updatedAt:void 0,value:""}),g=e=>{let t=y.current.updatedAt&&new Date().getTime()-y.current.updatedAt.getTime()>1e3;y.current={value:t?e:`${y.current.value}${e}`,updatedAt:new Date};let r=h.findIndex(e=>_(e).name.toLowerCase().startsWith(y.current.value));-1!==r&&b(r)},C=(0,a.useCallback)(e=>h.findIndex(t=>_(t).iso2===e),[h]),[v,b]=(0,a.useState)(C(r)),w=()=>{m.current!==r&&b(C(r))},x=(0,a.useCallback)(e=>{b(C(e.iso2)),u?.(e)},[u,C]),S=e=>{let t=h.length-1,r=r=>"prev"===e?r-1:"next"===e?r+1:"last"===e?t:0;b(e=>{let a=r(e);return a<0?0:a>t?t:a})},k=(0,a.useCallback)(()=>{if(!f.current||void 0===v)return;let e=_(h[v]).iso2;if(e===m.current)return;let t=f.current.querySelector(`[data-country="${e}"]`);t&&(c(f.current,t),m.current=e)},[v,h]);return(0,a.useEffect)(()=>{k()},[v,k]),(0,a.useEffect)(()=>{f.current&&(e?f.current.focus():w())},[e]),(0,a.useEffect)(()=>{w()},[r]),a.createElement("ul",{ref:f,role:"listbox",className:l({addPrefix:["country-selector-dropdown"],rawClassNames:[p.className]}),style:{display:e?"block":"none",...p.style},onKeyDown:e=>{if(e.stopPropagation(),"Enter"===e.key){e.preventDefault(),x(_(h[v]));return}if("Escape"===e.key){d?.();return}if("ArrowUp"===e.key){e.preventDefault(),S("prev");return}if("ArrowDown"===e.key){e.preventDefault(),S("next");return}if("PageUp"===e.key){e.preventDefault(),S("first");return}if("PageDown"===e.key){e.preventDefault(),S("last");return}" "===e.key&&e.preventDefault(),1!==e.key.length||e.altKey||e.ctrlKey||e.metaKey||g(e.key.toLocaleLowerCase())},onBlur:d,tabIndex:-1,"aria-activedescendant":`react-international-phone__${_(h[v]).iso2}-option`},h.map((e,n)=>{let o=_(e),u=o.iso2===r,d=n===v,c=i.includes(o.iso2),f=n===i.length-1,m=s?.find(e=>e.iso2===o.iso2);return a.createElement(a.Fragment,{key:o.iso2},a.createElement("li",{"data-country":o.iso2,role:"option","aria-selected":u,"aria-label":`${o.name} ${t}${o.dialCode}`,id:`react-international-phone__${o.iso2}-option`,className:l({addPrefix:["country-selector-dropdown__list-item",c&&"country-selector-dropdown__list-item--preferred",u&&"country-selector-dropdown__list-item--selected",d&&"country-selector-dropdown__list-item--focused"],rawClassNames:[p.listItemClassName]}),onClick:()=>x(o),style:p.listItemStyle,title:o.name},a.createElement(G,{iso2:o.iso2,src:m?.src,className:l({addPrefix:["country-selector-dropdown__list-item-flag-emoji"],rawClassNames:[p.listItemFlagClassName]}),style:p.listItemFlagStyle}),a.createElement("span",{className:l({addPrefix:["country-selector-dropdown__list-item-country-name"],rawClassNames:[p.listItemCountryNameClassName]}),style:p.listItemCountryNameStyle},o.name),a.createElement("span",{className:l({addPrefix:["country-selector-dropdown__list-item-dial-code"],rawClassNames:[p.listItemDialCodeClassName]}),style:p.listItemDialCodeStyle},t,o.dialCode)),f?a.createElement("hr",{className:l({addPrefix:["country-selector-dropdown__preferred-list-divider"],rawClassNames:[p.preferredListDividerClassName]}),style:p.preferredListDividerStyle}):null)}))},H=({selectedCountry:e,onSelect:t,disabled:r,hideDropdown:o,countries:i=n,preferredCountries:s=[],flags:u,renderButtonWrapper:d,...c})=>{let p,f,[m,h]=(0,a.useState)(!1),y=(0,a.useMemo)(()=>{if(e)return B({value:e,field:"iso2",countries:i})},[i,e]),g=(0,a.useRef)(null);return a.createElement("div",{className:l({addPrefix:["country-selector"],rawClassNames:[c.className]}),style:c.style,ref:g},(p={title:y?.name,onClick:()=>h(e=>!e),onMouseDown:e=>e.preventDefault(),onKeyDown:e=>{e.key&&["ArrowUp","ArrowDown"].includes(e.key)&&(e.preventDefault(),h(!0))},disabled:o||r,role:"combobox","aria-label":"Country selector","aria-haspopup":"listbox","aria-expanded":m},f=a.createElement("div",{className:l({addPrefix:["country-selector-button__button-content"],rawClassNames:[c.buttonContentWrapperClassName]}),style:c.buttonContentWrapperStyle},a.createElement(G,{iso2:e,src:u?.find(t=>t.iso2===e)?.src,className:l({addPrefix:["country-selector-button__flag-emoji",r&&"country-selector-button__flag-emoji--disabled"],rawClassNames:[c.flagClassName]}),style:{visibility:e?"visible":"hidden",...c.flagStyle}}),!o&&a.createElement("div",{className:l({addPrefix:["country-selector-button__dropdown-arrow",r&&"country-selector-button__dropdown-arrow--disabled",m&&"country-selector-button__dropdown-arrow--active"],rawClassNames:[c.dropdownArrowClassName]}),style:c.dropdownArrowStyle})),d?d({children:f,rootProps:p}):a.createElement("button",{...p,type:"button",className:l({addPrefix:["country-selector-button",m&&"country-selector-button--active",r&&"country-selector-button--disabled",o&&"country-selector-button--hide-dropdown"],rawClassNames:[c.buttonClassName]}),"data-country":e,style:c.buttonStyle},f)),a.createElement(W,{show:m,countries:i,preferredCountries:s,flags:u,onSelect:e=>{h(!1),t?.(e)},selectedCountry:e,onClose:()=>{h(!1)},...c.dropdownStyleProps}))},K=({dialCode:e,prefix:t,disabled:r,style:n,className:o})=>a.createElement("div",{className:l({addPrefix:["dial-code-preview",r&&"dial-code-preview--disabled"],rawClassNames:[o]}),style:n},`${t}${e}`),q=(0,a.forwardRef)(({value:e,onChange:t,countries:r=n,preferredCountries:o=[],hideDropdown:i,showDisabledDialCodeAndPrefix:s,disableFocusAfterCountrySelect:u,flags:d,style:c,className:p,inputStyle:f,inputClassName:m,countrySelectorStyleProps:h,dialCodePreviewStyleProps:y,inputProps:g,placeholder:C,disabled:v,name:b,onFocus:w,onBlur:x,required:S,autoFocus:k,...D},N)=>{let{phone:$,inputValue:A,inputRef:M,country:P,setCountry:E,handlePhoneValueChange:L}=j({value:e,countries:r,...D,onChange:e=>{t?.(e.phone,{country:e.country,inputValue:e.inputValue})}}),I=D.disableDialCodeAndPrefix&&s&&P?.dialCode;return(0,a.useImperativeHandle)(N,()=>M.current?Object.assign(M.current,{setCountry:E,state:{phone:$,inputValue:A,country:P}}):null,[M,E,$,A,P]),a.createElement("div",{ref:N,className:l({addPrefix:["input-container"],rawClassNames:[p]}),style:c},a.createElement(H,{onSelect:e=>E(e.iso2,{focusOnInput:!u}),flags:d,selectedCountry:P.iso2,countries:r,preferredCountries:o,disabled:v,hideDropdown:i,...h}),I&&a.createElement(K,{dialCode:P.dialCode,prefix:D.prefix??"+",disabled:v,...y}),a.createElement("input",{onChange:L,value:A,type:"tel",ref:M,className:l({addPrefix:["input",v&&"input--disabled"],rawClassNames:[m]}),placeholder:C,disabled:v,style:f,name:b,onFocus:w,onBlur:x,autoFocus:k,required:S,...g}))})}}]);