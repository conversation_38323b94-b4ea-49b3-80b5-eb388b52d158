import { redirect } from "next/navigation";

import { axiosGetJsonSSR } from "@/config/axios";
import ArticlesList from "@/features/blog/components/ArticlesList";
import BannerComponents from "@/components/sections/BannerComponents";
import blogBanner from "../../../../assets/images/website/banner/blogBanner.webp";
import initTranslations from "@/app/i18n";

export async function generateMetadata({ params: { locale }, searchParams }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }blog/`;
  const languages = {
    fr: `https://www.pentabell.com/fr/blog/`,
    en: `https://www.pentabell.com/blog/`,
    "x-default": `https://www.pentabell.com/blog/`,
  };
  const { t } = await initTranslations(locale, ["aboutUs", "global"]);

  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/blog`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("global:metaTitleBlog"),
    description: t("global:metaDescriptionBlog"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots:
      Object.keys(searchParams).length > 0
        ? "follow, noindex"
        : "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

const page = async ({ params, searchParams }) => {
  const { t } = await initTranslations(params.locale, ["aboutUs", "global"]);

  try {
    const res = await axiosGetJsonSSR.get(`/AriclesAndGuides`, {
      params: {
        language: params.locale,
        pageSize: 10,
        pageNumber: searchParams?.pageNumber || 1,
        searchQuery: searchParams?.keyword || "",
      },
    });

    const articles = res?.data.List.List;

    return (
      <>
        <BannerComponents
          bannerImg={blogBanner}
          height={"70vh"}
          description={t("global:getInspired")}
          title={"Pentabell Blog"}
          altImg={t("global:altBlog")}
        />
        <ArticlesList
          data={articles}
          language={params.locale}
          searchParams={searchParams}
        />
      </>
    );
  } catch (error) {
    redirect(params.locale === "en" ? `/` : `/${params.locale}/`);
    
  }
};


export default page;
