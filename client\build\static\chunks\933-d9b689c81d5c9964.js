(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[933,4675,3667,9750],{8430:function(e,t,r){"use strict";var o=r(32464),n=r(57437);t.Z=(0,o.Z)((0,n.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},77468:function(e,t,r){"use strict";r.d(t,{Z:function(){return Z}});var o=r(2265),n=r(61994),a=r(20801),i=r(16210),l=r(76301),s=r(37053),p=r(94143),c=r(50738);function d(e){return(0,c.ZP)("MuiDialogContent",e)}(0,p.Z)("MuiDialogContent",["root","dividers"]);var u=r(67172),m=r(57437);let v=e=>{let{classes:t,dividers:r}=e;return(0,a.Z)({root:["root",r&&"dividers"]},d,t)},f=(0,i.ZP)("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.dividers&&t.dividers]}})((0,l.Z)(e=>{let{theme:t}=e;return{flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:e=>{let{ownerState:t}=e;return t.dividers},style:{padding:"16px 24px",borderTop:`1px solid ${(t.vars||t).palette.divider}`,borderBottom:`1px solid ${(t.vars||t).palette.divider}`}},{props:e=>{let{ownerState:t}=e;return!t.dividers},style:{[`.${u.Z.root} + &`]:{paddingTop:0}}}]}}));var Z=o.forwardRef(function(e,t){let r=(0,s.i)({props:e,name:"MuiDialogContent"}),{className:o,dividers:a=!1,...i}=r,l={...r,dividers:a},p=v(l);return(0,m.jsx)(f,{className:(0,n.Z)(p.root,o),ownerState:l,ref:t,...i})})},79507:function(e,t,r){"use strict";var o=r(2265),n=r(61994),a=r(20801),i=r(46387),l=r(16210),s=r(37053),p=r(67172),c=r(91285),d=r(57437);let u=e=>{let{classes:t}=e;return(0,a.Z)({root:["root"]},p.a,t)},m=(0,l.ZP)(i.default,{name:"MuiDialogTitle",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:"16px 24px",flex:"0 0 auto"}),v=o.forwardRef(function(e,t){let r=(0,s.i)({props:e,name:"MuiDialogTitle"}),{className:a,id:i,...l}=r,p=u(r),{titleId:v=i}=o.useContext(c.Z);return(0,d.jsx)(m,{component:"h2",className:(0,n.Z)(p.root,a),ownerState:r,ref:t,variant:"h6",id:i??v,...l})});t.Z=v},67172:function(e,t,r){"use strict";r.d(t,{a:function(){return a}});var o=r(94143),n=r(50738);function a(e){return(0,n.ZP)("MuiDialogTitle",e)}let i=(0,o.Z)("MuiDialogTitle",["root"]);t.Z=i},35791:function(e,t,r){"use strict";var o=r(2265),n=r(61994),a=r(20801),i=r(53025),l=r(85657),s=r(76501),p=r(90486),c=r(53410),d=r(85437),u=r(91285),m=r(63804),v=r(16210),f=r(31691),Z=r(76301),h=r(37053),g=r(79114),y=r(57437);let b=(0,v.ZP)(m.Z,{name:"MuiDialog",slot:"Backdrop",overrides:(e,t)=>t.backdrop})({zIndex:-1}),x=e=>{let{classes:t,scroll:r,maxWidth:o,fullWidth:n,fullScreen:i}=e,s={root:["root"],container:["container",`scroll${(0,l.Z)(r)}`],paper:["paper",`paperScroll${(0,l.Z)(r)}`,`paperWidth${(0,l.Z)(String(o))}`,n&&"paperFullWidth",i&&"paperFullScreen"]};return(0,a.Z)(s,d.D,t)},k=(0,v.ZP)(s.Z,{name:"MuiDialog",slot:"Root",overridesResolver:(e,t)=>t.root})({"@media print":{position:"absolute !important"}}),C=(0,v.ZP)("div",{name:"MuiDialog",slot:"Container",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.container,t[`scroll${(0,l.Z)(r.scroll)}`]]}})({height:"100%","@media print":{height:"auto"},outline:0,variants:[{props:{scroll:"paper"},style:{display:"flex",justifyContent:"center",alignItems:"center"}},{props:{scroll:"body"},style:{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}}}]}),$=(0,v.ZP)(c.Z,{name:"MuiDialog",slot:"Paper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.paper,t[`scrollPaper${(0,l.Z)(r.scroll)}`],t[`paperWidth${(0,l.Z)(String(r.maxWidth))}`],r.fullWidth&&t.paperFullWidth,r.fullScreen&&t.paperFullScreen]}})((0,Z.Z)(e=>{let{theme:t}=e;return{margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"},variants:[{props:{scroll:"paper"},style:{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"}},{props:{scroll:"body"},style:{display:"inline-block",verticalAlign:"middle",textAlign:"initial"}},{props:e=>{let{ownerState:t}=e;return!t.maxWidth},style:{maxWidth:"calc(100% - 64px)"}},{props:{maxWidth:"xs"},style:{maxWidth:"px"===t.breakpoints.unit?Math.max(t.breakpoints.values.xs,444):`max(${t.breakpoints.values.xs}${t.breakpoints.unit}, 444px)`,[`&.${d.Z.paperScrollBody}`]:{[t.breakpoints.down(Math.max(t.breakpoints.values.xs,444)+64)]:{maxWidth:"calc(100% - 64px)"}}}},...Object.keys(t.breakpoints.values).filter(e=>"xs"!==e).map(e=>({props:{maxWidth:e},style:{maxWidth:`${t.breakpoints.values[e]}${t.breakpoints.unit}`,[`&.${d.Z.paperScrollBody}`]:{[t.breakpoints.down(t.breakpoints.values[e]+64)]:{maxWidth:"calc(100% - 64px)"}}}})),{props:e=>{let{ownerState:t}=e;return t.fullWidth},style:{width:"calc(100% - 64px)"}},{props:e=>{let{ownerState:t}=e;return t.fullScreen},style:{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,[`&.${d.Z.paperScrollBody}`]:{margin:0,maxWidth:"100%"}}}]}})),w=o.forwardRef(function(e,t){let r=(0,h.i)({props:e,name:"MuiDialog"}),a=(0,f.Z)(),l={enter:a.transitions.duration.enteringScreen,exit:a.transitions.duration.leavingScreen},{"aria-describedby":s,"aria-labelledby":d,"aria-modal":m=!0,BackdropComponent:v,BackdropProps:Z,children:w,className:P,disableEscapeKeyDown:S=!1,fullScreen:M=!1,fullWidth:R=!1,maxWidth:j="sm",onBackdropClick:D,onClick:I,onClose:W,open:O,PaperComponent:F=c.Z,PaperProps:T={},scroll:N="paper",slots:z={},slotProps:B={},TransitionComponent:A=p.Z,transitionDuration:L=l,TransitionProps:U,...V}=r,q={...r,disableEscapeKeyDown:S,fullScreen:M,fullWidth:R,maxWidth:j,scroll:N},E=x(q),G=o.useRef(),H=(0,i.Z)(d),Y=o.useMemo(()=>({titleId:H}),[H]),_={slots:{transition:A,...z},slotProps:{transition:U,paper:T,backdrop:Z,...B}},[X,K]=(0,g.Z)("root",{elementType:k,shouldForwardComponentProp:!0,externalForwardedProps:_,ownerState:q,className:(0,n.Z)(E.root,P),ref:t}),[J,Q]=(0,g.Z)("backdrop",{elementType:b,shouldForwardComponentProp:!0,externalForwardedProps:_,ownerState:q}),[ee,et]=(0,g.Z)("paper",{elementType:$,shouldForwardComponentProp:!0,externalForwardedProps:_,ownerState:q,className:(0,n.Z)(E.paper,T.className)}),[er,eo]=(0,g.Z)("container",{elementType:C,externalForwardedProps:_,ownerState:q,className:(0,n.Z)(E.container)}),[en,ea]=(0,g.Z)("transition",{elementType:p.Z,externalForwardedProps:_,ownerState:q,additionalProps:{appear:!0,in:O,timeout:L,role:"presentation"}});return(0,y.jsx)(X,{closeAfterTransition:!0,slots:{backdrop:J},slotProps:{backdrop:{transitionDuration:L,as:v,...Q}},disableEscapeKeyDown:S,onClose:W,open:O,onClick:e=>{I&&I(e),G.current&&(G.current=null,D&&D(e),W&&W(e,"backdropClick"))},...K,...V,children:(0,y.jsx)(en,{...ea,children:(0,y.jsx)(er,{onMouseDown:e=>{G.current=e.target===e.currentTarget},...eo,children:(0,y.jsx)(ee,{as:F,elevation:24,role:"dialog","aria-describedby":s,"aria-labelledby":H,"aria-modal":m,...et,children:(0,y.jsx)(u.Z.Provider,{value:Y,children:w})})})})})});t.Z=w},91285:function(e,t,r){"use strict";let o=r(2265).createContext({});t.Z=o},85437:function(e,t,r){"use strict";r.d(t,{D:function(){return a}});var o=r(94143),n=r(50738);function a(e){return(0,n.ZP)("MuiDialog",e)}let i=(0,o.Z)("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"]);t.Z=i},42596:function(e,t,r){"use strict";r.d(t,{V:function(){return a}});var o=r(94143),n=r(50738);function a(e){return(0,n.ZP)("MuiDivider",e)}let i=(0,o.Z)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]);t.Z=i},67752:function(e,t,r){"use strict";r.d(t,{f:function(){return a}});var o=r(94143),n=r(50738);function a(e){return(0,n.ZP)("MuiListItemIcon",e)}let i=(0,o.Z)("MuiListItemIcon",["root","alignItemsFlexStart"]);t.Z=i},3127:function(e,t,r){"use strict";r.d(t,{L:function(){return a}});var o=r(94143),n=r(50738);function a(e){return(0,n.ZP)("MuiListItemText",e)}let i=(0,o.Z)("MuiListItemText",["root","multiline","dense","inset","primary","secondary"]);t.Z=i},42187:function(e,t,r){"use strict";r.d(t,{Z:function(){return w}});var o=r(2265),n=r(61994),a=r(20801),i=r(82590),l=r(34765),s=r(16210),p=r(76301),c=r(37053),d=r(15566),u=r(82662),m=r(84217),v=r(60118),f=r(42596),Z=r(67752),h=r(3127),g=r(94143),y=r(50738);function b(e){return(0,y.ZP)("MuiMenuItem",e)}let x=(0,g.Z)("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]);var k=r(57437);let C=e=>{let{disabled:t,dense:r,divider:o,disableGutters:n,selected:i,classes:l}=e,s=(0,a.Z)({root:["root",r&&"dense",t&&"disabled",!n&&"gutters",o&&"divider",i&&"selected"]},b,l);return{...l,...s}},$=(0,s.ZP)(u.Z,{shouldForwardProp:e=>(0,l.Z)(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.dense&&t.dense,r.divider&&t.divider,!r.disableGutters&&t.gutters]}})((0,p.Z)(e=>{let{theme:t}=e;return{...t.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${x.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,i.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${x.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,i.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},[`&.${x.selected}:hover`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,i.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,i.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity)}},[`&.${x.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${x.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity},[`& + .${f.Z.root}`]:{marginTop:t.spacing(1),marginBottom:t.spacing(1)},[`& + .${f.Z.inset}`]:{marginLeft:52},[`& .${h.Z.root}`]:{marginTop:0,marginBottom:0},[`& .${h.Z.inset}`]:{paddingLeft:36},[`& .${Z.Z.root}`]:{minWidth:36},variants:[{props:e=>{let{ownerState:t}=e;return!t.disableGutters},style:{paddingLeft:16,paddingRight:16}},{props:e=>{let{ownerState:t}=e;return t.divider},style:{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"}},{props:e=>{let{ownerState:t}=e;return!t.dense},style:{[t.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:e=>{let{ownerState:t}=e;return t.dense},style:{minHeight:32,paddingTop:4,paddingBottom:4,...t.typography.body2,[`& .${Z.Z.root} svg`]:{fontSize:"1.25rem"}}}]}}));var w=o.forwardRef(function(e,t){let r;let a=(0,c.i)({props:e,name:"MuiMenuItem"}),{autoFocus:i=!1,component:l="li",dense:s=!1,divider:p=!1,disableGutters:u=!1,focusVisibleClassName:f,role:Z="menuitem",tabIndex:h,className:g,...y}=a,b=o.useContext(d.Z),x=o.useMemo(()=>({dense:s||b.dense||!1,disableGutters:u}),[b.dense,s,u]),w=o.useRef(null);(0,m.Z)(()=>{i&&w.current&&w.current.focus()},[i]);let P={...a,dense:x.dense,divider:p,disableGutters:u},S=C(a),M=(0,v.Z)(w,t);return a.disabled||(r=void 0!==h?h:-1),(0,k.jsx)(d.Z.Provider,{value:x,children:(0,k.jsx)($,{ref:M,role:Z,tabIndex:r,component:l,focusVisibleClassName:(0,n.Z)(S.focusVisible,f),className:(0,n.Z)(S.root,g),...y,ownerState:P,classes:S})})})},26225:function(e,t,r){"use strict";r.d(t,{Z:function(){return Z}});var o=r(2265),n=r(61994),a=r(20801),i=r(89126),l=r(94143),s=r(50738);function p(e){return(0,s.ZP)("MuiRadioGroup",e)}(0,l.Z)("MuiRadioGroup",["root","row","error"]);var c=r(60118),d=r(67184),u=r(9366),m=r(32709),v=r(57437);let f=e=>{let{classes:t,row:r,error:o}=e;return(0,a.Z)({root:["root",r&&"row",o&&"error"]},p,t)};var Z=o.forwardRef(function(e,t){let{actions:r,children:a,className:l,defaultValue:s,name:p,onChange:Z,value:h,...g}=e,y=o.useRef(null),b=f(e),[x,k]=(0,d.Z)({controlled:h,default:s,name:"RadioGroup"});o.useImperativeHandle(r,()=>({focus:()=>{let e=y.current.querySelector("input:not(:disabled):checked");e||(e=y.current.querySelector("input:not(:disabled)")),e&&e.focus()}}),[]);let C=(0,c.Z)(t,y),$=(0,m.Z)(p),w=o.useMemo(()=>({name:$,onChange(e){k(e.target.value),Z&&Z(e,e.target.value)},value:x}),[$,Z,k,x]);return(0,v.jsx)(u.Z.Provider,{value:w,children:(0,v.jsx)(i.Z,{role:"radiogroup",ref:C,className:(0,n.Z)(b.root,l),...g,children:a})})})},9366:function(e,t,r){"use strict";let o=r(2265).createContext(void 0);t.Z=o},47087:function(e,t,r){"use strict";r.d(t,{Z:function(){return O}});var o=r(2265),n=r(61994),a=r(20801),i=r(82590),l=r(66183),s=r(32464),p=r(57437),c=(0,s.Z)((0,p.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),"RadioButtonUnchecked"),d=(0,s.Z)((0,p.jsx)("path",{d:"M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"}),"RadioButtonChecked"),u=r(34765),m=r(16210),v=r(76301);let f=(0,m.ZP)("span",{shouldForwardProp:u.Z})({position:"relative",display:"flex"}),Z=(0,m.ZP)(c)({transform:"scale(1)"}),h=(0,m.ZP)(d)((0,v.Z)(e=>{let{theme:t}=e;return{left:0,position:"absolute",transform:"scale(0)",transition:t.transitions.create("transform",{easing:t.transitions.easing.easeIn,duration:t.transitions.duration.shortest}),variants:[{props:{checked:!0},style:{transform:"scale(1)",transition:t.transitions.create("transform",{easing:t.transitions.easing.easeOut,duration:t.transitions.duration.shortest})}}]}}));var g=function(e){let{checked:t=!1,classes:r={},fontSize:o}=e,n={...e,checked:t};return(0,p.jsxs)(f,{className:r.root,ownerState:n,children:[(0,p.jsx)(Z,{fontSize:o,className:r.background,ownerState:n}),(0,p.jsx)(h,{fontSize:o,className:r.dot,ownerState:n})]})},y=r(85657),b=r(16973).Z,x=r(66515),k=r(9366),C=r(94143),$=r(50738);function w(e){return(0,$.ZP)("MuiRadio",e)}let P=(0,C.Z)("MuiRadio",["root","checked","disabled","colorPrimary","colorSecondary","sizeSmall"]);var S=r(3858),M=r(79114),R=r(37053);let j=e=>{let{classes:t,color:r,size:o}=e,n={root:["root",`color${(0,y.Z)(r)}`,"medium"!==o&&`size${(0,y.Z)(o)}`]};return{...t,...(0,a.Z)(n,w,t)}},D=(0,m.ZP)(l.Z,{shouldForwardProp:e=>(0,u.Z)(e)||"classes"===e,name:"MuiRadio",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,"medium"!==r.size&&t[`size${(0,y.Z)(r.size)}`],t[`color${(0,y.Z)(r.color)}`]]}})((0,v.Z)(e=>{let{theme:t}=e;return{color:(t.vars||t).palette.text.secondary,[`&.${P.disabled}`]:{color:(t.vars||t).palette.action.disabled},variants:[{props:{color:"default",disabled:!1,disableRipple:!1},style:{"&:hover":{backgroundColor:t.vars?`rgba(${t.vars.palette.action.activeChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,i.Fq)(t.palette.action.active,t.palette.action.hoverOpacity)}}},...Object.entries(t.palette).filter((0,S.Z)()).map(e=>{let[r]=e;return{props:{color:r,disabled:!1,disableRipple:!1},style:{"&:hover":{backgroundColor:t.vars?`rgba(${t.vars.palette[r].mainChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,i.Fq)(t.palette[r].main,t.palette.action.hoverOpacity)}}}}),...Object.entries(t.palette).filter((0,S.Z)()).map(e=>{let[r]=e;return{props:{color:r,disabled:!1},style:{[`&.${P.checked}`]:{color:(t.vars||t).palette[r].main}}}}),{props:{disableRipple:!1},style:{"&:hover":{"@media (hover: none)":{backgroundColor:"transparent"}}}}]}})),I=(0,p.jsx)(g,{checked:!0}),W=(0,p.jsx)(g,{});var O=o.forwardRef(function(e,t){let r=(0,R.i)({props:e,name:"MuiRadio"}),{checked:a,checkedIcon:i=I,color:l="primary",icon:s=W,name:c,onChange:d,size:u="medium",className:m,disabled:v,disableRipple:f=!1,slots:Z={},slotProps:h={},inputProps:g,...y}=r,C=(0,x.Z)(),$=v;C&&void 0===$&&($=C.disabled),$??=!1;let w={...r,disabled:$,disableRipple:f,color:l,size:u},P=j(w),S=o.useContext(k.Z),O=a,F=b(d,S&&S.onChange),T=c;if(S){if(void 0===O){var N,z;N=S.value,O="object"==typeof(z=r.value)&&null!==z?N===z:String(N)===String(z)}void 0===T&&(T=S.name)}let B=h.input??g,[A,L]=(0,M.Z)("root",{ref:t,elementType:D,className:(0,n.Z)(P.root,m),shouldForwardComponentProp:!0,externalForwardedProps:{slots:Z,slotProps:h,...y},getSlotProps:e=>({...e,onChange:function(t){for(var r=arguments.length,o=Array(r>1?r-1:0),n=1;n<r;n++)o[n-1]=arguments[n];e.onChange?.(t,...o),F(t,...o)}}),ownerState:w,additionalProps:{type:"radio",icon:o.cloneElement(s,{fontSize:s.props.fontSize??u}),checkedIcon:o.cloneElement(i,{fontSize:i.props.fontSize??u}),disabled:$,name:T,checked:O,slots:Z,slotProps:{input:"function"==typeof B?B(w):B}}});return(0,p.jsx)(A,{...L,classes:P})})},63582:function(e,t,r){"use strict";r.d(t,{Z:function(){return k}});var o=r(2265),n=r(61994),a=r(87354),i=r(50738),l=r(20801),s=r(95045),p=r(20956),c=r(20443),d=r(58698),u=r(84586),m=r(85055),v=r(57437);let f=(0,d.Z)(),Z=(0,s.Z)("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root});function h(e){return(0,p.Z)({props:e,name:"MuiStack",defaultTheme:f})}let g=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],y=e=>{let{ownerState:t,theme:r}=e,o={display:"flex",flexDirection:"column",...(0,u.k9)({theme:r},(0,u.P$)({values:t.direction,breakpoints:r.breakpoints.values}),e=>({flexDirection:e}))};if(t.spacing){let e=(0,m.hB)(r),n=Object.keys(r.breakpoints.values).reduce((e,r)=>(("object"==typeof t.spacing&&null!=t.spacing[r]||"object"==typeof t.direction&&null!=t.direction[r])&&(e[r]=!0),e),{}),i=(0,u.P$)({values:t.direction,base:n}),l=(0,u.P$)({values:t.spacing,base:n});"object"==typeof i&&Object.keys(i).forEach((e,t,r)=>{if(!i[e]){let o=t>0?i[r[t-1]]:"column";i[e]=o}}),o=(0,a.Z)(o,(0,u.k9)({theme:r},l,(r,o)=>t.useFlexGap?{gap:(0,m.NA)(e,r)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${g(o?i[o]:t.direction)}`]:(0,m.NA)(e,r)}}))}return(0,u.dt)(r.breakpoints,o)};var b=r(16210),x=r(37053),k=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{createStyledComponent:t=Z,useThemeProps:r=h,componentName:a="MuiStack"}=e,s=()=>(0,l.Z)({root:["root"]},e=>(0,i.ZP)(a,e),{}),p=t(y);return o.forwardRef(function(e,t){let a=r(e),{component:i="div",direction:l="column",spacing:d=0,divider:u,children:m,className:f,useFlexGap:Z=!1,...h}=(0,c.Z)(a),g=s();return(0,v.jsx)(p,{as:i,ownerState:{direction:l,spacing:d,useFlexGap:Z},ref:t,className:(0,n.Z)(g.root,f),...h,children:u?function(e,t){let r=o.Children.toArray(e).filter(Boolean);return r.reduce((e,n,a)=>(e.push(n),a<r.length-1&&e.push(o.cloneElement(t,{key:`separator-${a}`})),e),[])}(m,u):m})})}({createStyledComponent:(0,b.ZP)("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root}),useThemeProps:e=>(0,x.i)({props:e,name:"MuiStack"})})},95045:function(e,t,r){"use strict";let o=(0,r(29418).ZP)();t.Z=o},93826:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});var o=r(53232);function n(e){let{theme:t,name:r,props:n}=e;return t&&t.components&&t.components[r]&&t.components[r].defaultProps?(0,o.Z)(t.components[r].defaultProps,n):n}},20956:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});var o=r(93826),n=r(49695);function a(e){let{props:t,name:r,defaultTheme:a,themeId:i}=e,l=(0,n.Z)(a);return i&&(l=l[i]||l),(0,o.Z)({theme:l,name:r,props:t})}},49360:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});for(var o,n={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},a=new Uint8Array(16),i=[],l=0;l<256;++l)i.push((l+256).toString(16).slice(1));var s=function(e,t,r){if(n.randomUUID&&!t&&!e)return n.randomUUID();var l=(e=e||{}).random||(e.rng||function(){if(!o&&!(o="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return o(a)})();if(l[6]=15&l[6]|64,l[8]=63&l[8]|128,t){r=r||0;for(var s=0;s<16;++s)t[r+s]=l[s];return t}return function(e,t=0){return(i[e[t+0]]+i[e[t+1]]+i[e[t+2]]+i[e[t+3]]+"-"+i[e[t+4]]+i[e[t+5]]+"-"+i[e[t+6]]+i[e[t+7]]+"-"+i[e[t+8]]+i[e[t+9]]+"-"+i[e[t+10]]+i[e[t+11]]+i[e[t+12]]+i[e[t+13]]+i[e[t+14]]+i[e[t+15]]).toLowerCase()}(l)}},6179:function(){}}]);