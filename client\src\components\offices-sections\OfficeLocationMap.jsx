import { Container, Grid } from "@mui/material";
import CustomButton from "../ui/CustomButton";
import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";
import { websiteRoutesList } from "@/helpers/routesList";

function OfficeLocationMap({ t }) {
  return (
    <Container id="office-location-map" className="custom-max-width">
      <Grid
        className="container"
        justifyContent="space-between"
        container
        spacing={0}
      >
        <Grid item xs={12} sm={6}>
          <div className="content">
            <p className="heading-h2 text-white">
              {t("Tunisia:officeLocation:label")}
            </p>
            <p className="sub-heading text-white">
              {t("Tunisia:officeLocation:title")}
            </p>
            <div>
              <p className="paragraph text-white" key={"tn"}>
                <span>
                  <SvglocationPin />
                </span>
                {t("Tunisia:officeLocation:address")}
              </p>
              <p className="paragraph text-white">
                <span>
                  <SvgcallUs />
                </span>
                {t("Tunisia:officeLocation:tel1")}
              </p>

              <p className="paragraph text-white">
                <span>
                  <Svgemail />
                </span>
                {t("Tunisia:officeLocation:mail")}
              </p>
            </div>
            <CustomButton
              text={t("Tunisia:officeLocation:talk")}
              className={"btn btn-outlined white"}
              link={`/${websiteRoutesList.tunisiaPage.route}#service-page-form`}
            />
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <div className="map-frame">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d12772.982761240593!2d10.300971!3d36.8365915!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x12fd4ba9d86da971%3A0x7ef02d88ba84ab8b!2sPentabell%20Tunisie%20-%20Cabinet%20de%20Recrutement%2C%20Portage%20Salarial%20et%20Assistance%20Technique!5e0!3m2!1sen!2stn!4v1728382192149!5m2!1sen!2stn"
              allowfullscreen=""
              priority
              referrerpolicy="no-referrer-when-downgrade"
            ></iframe>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default OfficeLocationMap;
