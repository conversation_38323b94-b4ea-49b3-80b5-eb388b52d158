import { Container, Grid } from "@mui/material";
import EmblaCarouselThumbsTeamPic from "../ui/emblaCarousel/EmblaCarouselThumbsTeamPic";
import EmblaCarouselThumbsTeamPicAlgerie from "../ui/emblaCarousel/EmblaCarouselThumbsTeamPicAlgerie";
import img1 from "../../assets/images/Algeria/a5.jpg";
import img2 from "../../assets/images/Algeria/a2.jpg";
import img3 from "../../assets/images/Algeria/AL1.JPG";
import img4 from "../../assets/images/Algeria/AL2.PNG";
function EORServicesAlgeria({t}) {
  const OPTIONS = {};
  const SLIDE_COUNT = 10;
  const SLIDES = Array.from(Array(SLIDE_COUNT).keys());
  const slideImg=[
    {src :img1},
    {src :img2},
    {src :img3},
    {src :img4},

  ]
  return (
    <Container id="eor-services-tn" className="custom-max-width">
      <Grid
        className="container"
        justifyContent="space-between"
        container
        spacing={2}
      >
        <Grid item xs={12} sm={6}>
          <div className="team-thumbs">
            <EmblaCarouselThumbsTeamPicAlgerie slides={slideImg} options={OPTIONS} />
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <div className="right-section">
            <p className="paragraph text-yellow">
            {t("Algeria:EORServicesAlgeria:subTitle")}
            </p>
            <p className="heading-h2 text-white">
            {t("Algeria:EORServicesAlgeria:title")}
            </p>
            <p className="paragraph text-white">
            {t("Algeria:EORServicesAlgeria:description")}
            </p>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default EORServicesAlgeria;
