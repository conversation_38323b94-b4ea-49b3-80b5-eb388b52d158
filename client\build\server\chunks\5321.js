"use strict";exports.id=5321,exports.ids=[5321],exports.modules={34039:(t,n,e)=>{e.d(n,{Z:()=>p});var r=e(17577),i=e(41135),o=e(88634),u=e(91703),a=e(2791),l=e(89178),c=e(71685),s=e(97898);function f(t){return(0,s.ZP)("MuiCard",t)}(0,c.Z)("MuiCard",["root"]);var h=e(10326);let g=t=>{let{classes:n}=t;return(0,o.Z)({root:["root"]},f,n)},d=(0,u.ZP)(l.Z,{name:"<PERSON>i<PERSON><PERSON>",slot:"Root",overridesResolver:(t,n)=>n.root})({overflow:"hidden"}),p=r.forwardRef(function(t,n){let e=(0,a.i)({props:t,name:"<PERSON><PERSON><PERSON><PERSON>"}),{className:r,raised:o=!1,...u}=e,l={...e,raised:o},c=g(l);return(0,h.jsx)(d,{className:(0,i.Z)(c.root,r),elevation:o?8:void 0,ref:n,ownerState:l,...u})})},11081:(t,n,e)=>{e.d(n,{Z:()=>r});function r(t,n){return null==t||null==n?NaN:t<n?-1:t>n?1:t>=n?0:NaN}},25734:(t,n,e)=>{e.d(n,{ZP:()=>l});var r=e(11081),i=e(51612),o=e(88192);let u=(0,i.Z)(r.Z),a=u.right;u.left,(0,i.Z)(o.Z).center;let l=a},51612:(t,n,e)=>{e.d(n,{Z:()=>o});var r=e(11081);function i(t,n){return null==t||null==n?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function o(t){let n,e,o;function a(t,r,i=0,o=t.length){if(i<o){if(0!==n(r,r))return o;do{let n=i+o>>>1;0>e(t[n],r)?i=n+1:o=n}while(i<o)}return i}return 2!==t.length?(n=r.Z,e=(n,e)=>(0,r.Z)(t(n),e),o=(n,e)=>t(n)-e):(n=t===r.Z||t===i?t:u,e=t,o=t),{left:a,center:function(t,n,e=0,r=t.length){let i=a(t,n,e,r-1);return i>e&&o(t[i-1],n)>-o(t[i],n)?i-1:i},right:function(t,r,i=0,o=t.length){if(i<o){if(0!==n(r,r))return o;do{let n=i+o>>>1;0>=e(t[n],r)?i=n+1:o=n}while(i<o)}return i}}}function u(){return 0}},88192:(t,n,e)=>{function r(t){return null===t?NaN:+t}function*i(t,n){if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&(yield n);else{let e=-1;for(let r of t)null!=(r=n(r,++e,t))&&(r=+r)>=r&&(yield r)}}e.d(n,{K:()=>i,Z:()=>r})},642:(t,n,e)=>{e.d(n,{G9:()=>l,ZP:()=>a,ly:()=>c});let r=Math.sqrt(50),i=Math.sqrt(10),o=Math.sqrt(2);function u(t,n,e){let a,l,c;let s=(n-t)/Math.max(0,e),f=Math.floor(Math.log10(s)),h=s/Math.pow(10,f),g=h>=r?10:h>=i?5:h>=o?2:1;return(f<0?(a=Math.round(t*(c=Math.pow(10,-f)/g)),l=Math.round(n*c),a/c<t&&++a,l/c>n&&--l,c=-c):(a=Math.round(t/(c=Math.pow(10,f)*g)),l=Math.round(n/c),a*c<t&&++a,l*c>n&&--l),l<a&&.5<=e&&e<2)?u(t,n,2*e):[a,l,c]}function a(t,n,e){if(n=+n,t=+t,!((e=+e)>0))return[];if(t===n)return[t];let r=n<t,[i,o,a]=r?u(n,t,e):u(t,n,e);if(!(o>=i))return[];let l=o-i+1,c=Array(l);if(r){if(a<0)for(let t=0;t<l;++t)c[t]=-((o-t)/a);else for(let t=0;t<l;++t)c[t]=(o-t)*a}else if(a<0)for(let t=0;t<l;++t)c[t]=-((i+t)/a);else for(let t=0;t<l;++t)c[t]=(i+t)*a;return c}function l(t,n,e){return u(t=+t,n=+n,e=+e)[2]}function c(t,n,e){n=+n,t=+t,e=+e;let r=n<t,i=r?l(n,t,e):l(t,n,e);return(r?-1:1)*(i<0?-(1/i):i)}},33575:(t,n,e)=>{function r(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function i(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function o(){}e.d(n,{ZP:()=>M,B8:()=>x});var u="\\s*([+-]?\\d+)\\s*",a="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",l="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",c=/^#([0-9a-f]{3,8})$/,s=RegExp(`^rgb\\(${u},${u},${u}\\)$`),f=RegExp(`^rgb\\(${l},${l},${l}\\)$`),h=RegExp(`^rgba\\(${u},${u},${u},${a}\\)$`),g=RegExp(`^rgba\\(${l},${l},${l},${a}\\)$`),d=RegExp(`^hsl\\(${a},${l},${l}\\)$`),p=RegExp(`^hsla\\(${a},${l},${l},${a}\\)$`),y={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function m(){return this.rgb().formatHex()}function v(){return this.rgb().formatRgb()}function M(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=c.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?w(n):3===e?new Z(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?b(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?b(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=s.exec(t))?new Z(n[1],n[2],n[3],1):(n=f.exec(t))?new Z(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=h.exec(t))?b(n[1],n[2],n[3],n[4]):(n=g.exec(t))?b(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=d.exec(t))?k(n[1],n[2]/100,n[3]/100,1):(n=p.exec(t))?k(n[1],n[2]/100,n[3]/100,n[4]):y.hasOwnProperty(t)?w(y[t]):"transparent"===t?new Z(NaN,NaN,NaN,0):null}function w(t){return new Z(t>>16&255,t>>8&255,255&t,1)}function b(t,n,e,r){return r<=0&&(t=n=e=NaN),new Z(t,n,e,r)}function x(t,n,e,r){var i;return 1==arguments.length?((i=t)instanceof o||(i=M(i)),i)?new Z((i=i.rgb()).r,i.g,i.b,i.opacity):new Z:new Z(t,n,e,null==r?1:r)}function Z(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function $(){return`#${D(this.r)}${D(this.g)}${D(this.b)}`}function T(){let t=C(this.opacity);return`${1===t?"rgb(":"rgba("}${U(this.r)}, ${U(this.g)}, ${U(this.b)}${1===t?")":`, ${t})`}`}function C(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function U(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function D(t){return((t=U(t))<16?"0":"")+t.toString(16)}function k(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new _(t,n,e,r)}function N(t){if(t instanceof _)return new _(t.h,t.s,t.l,t.opacity);if(t instanceof o||(t=M(t)),!t)return new _;if(t instanceof _)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),u=Math.max(n,e,r),a=NaN,l=u-i,c=(u+i)/2;return l?(a=n===u?(e-r)/l+(e<r)*6:e===u?(r-n)/l+2:(n-e)/l+4,l/=c<.5?u+i:2-u-i,a*=60):l=c>0&&c<1?0:a,new _(a,l,c,t.opacity)}function _(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function Y(t){return(t=(t||0)%360)<0?t+360:t}function A(t){return Math.max(0,Math.min(1,t||0))}function F(t,n,e){return(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)*255}r(o,M,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:m,formatHex:m,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return N(this).formatHsl()},formatRgb:v,toString:v}),r(Z,x,i(o,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new Z(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new Z(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Z(U(this.r),U(this.g),U(this.b),C(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:$,formatHex:$,formatHex8:function(){return`#${D(this.r)}${D(this.g)}${D(this.b)}${D((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:T,toString:T})),r(_,function(t,n,e,r){return 1==arguments.length?N(t):new _(t,n,e,null==r?1:r)},i(o,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new _(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new _(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new Z(F(t>=240?t-240:t+120,i,r),F(t,i,r),F(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new _(Y(this.h),A(this.s),A(this.l),C(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=C(this.opacity);return`${1===t?"hsl(":"hsla("}${Y(this.h)}, ${100*A(this.s)}%, ${100*A(this.l)}%${1===t?")":`, ${t})`}`}}))},78492:(t,n,e)=>{e.d(n,{WU:()=>o,jH:()=>u});var r,i,o,u,a=e(30970),l=e(92501),c=e(23579);function s(t,n){var e=(0,c.V)(t,n);if(!e)return t+"";var r=e[0],i=e[1];return i<0?"0."+Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+Array(i-r.length+2).join("0")}let f={"%":(t,n)=>(100*t).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:c.Z,e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>s(100*t,n),r:s,s:function(t,n){var e=(0,c.V)(t,n);if(!e)return t+"";var i=e[0],o=e[1],u=o-(r=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=i.length;return u===a?i:u>a?i+Array(u-a+1).join("0"):u>0?i.slice(0,u)+"."+i.slice(u):"0."+Array(1-u).join("0")+(0,c.V)(t,Math.max(0,n+u-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function h(t){return t}var g=Array.prototype.map,d=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];o=(i=function(t){var n,e,i,o=void 0===t.grouping||void 0===t.thousands?h:(n=g.call(t.grouping,Number),e=t.thousands+"",function(t,r){for(var i=t.length,o=[],u=0,a=n[0],l=0;i>0&&a>0&&(l+a+1>r&&(a=Math.max(1,r-l)),o.push(t.substring(i-=a,i+a)),!((l+=a+1)>r));)a=n[u=(u+1)%n.length];return o.reverse().join(e)}),u=void 0===t.currency?"":t.currency[0]+"",c=void 0===t.currency?"":t.currency[1]+"",s=void 0===t.decimal?".":t.decimal+"",p=void 0===t.numerals?h:(i=g.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return i[+t]})}),y=void 0===t.percent?"%":t.percent+"",m=void 0===t.minus?"−":t.minus+"",v=void 0===t.nan?"NaN":t.nan+"";function M(t){var n=(t=(0,l.Z)(t)).fill,e=t.align,i=t.sign,a=t.symbol,h=t.zero,g=t.width,M=t.comma,w=t.precision,b=t.trim,x=t.type;"n"===x?(M=!0,x="g"):f[x]||(void 0===w&&(w=12),b=!0,x="g"),(h||"0"===n&&"="===e)&&(h=!0,n="0",e="=");var Z="$"===a?u:"#"===a&&/[boxX]/.test(x)?"0"+x.toLowerCase():"",$="$"===a?c:/[%p]/.test(x)?y:"",T=f[x],C=/[defgprs%]/.test(x);function U(t){var u,a,l,c=Z,f=$;if("c"===x)f=T(t)+f,t="";else{var y=(t=+t)<0||1/t<0;if(t=isNaN(t)?v:T(Math.abs(t),w),b&&(t=function(t){t:for(var n,e=t.length,r=1,i=-1;r<e;++r)switch(t[r]){case".":i=n=r;break;case"0":0===i&&(i=r),n=r;break;default:if(!+t[r])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(n+1):t}(t)),y&&0==+t&&"+"!==i&&(y=!1),c=(y?"("===i?i:m:"-"===i||"("===i?"":i)+c,f=("s"===x?d[8+r/3]:"")+f+(y&&"("===i?")":""),C){for(u=-1,a=t.length;++u<a;)if(48>(l=t.charCodeAt(u))||l>57){f=(46===l?s+t.slice(u+1):t.slice(u))+f,t=t.slice(0,u);break}}}M&&!h&&(t=o(t,1/0));var U=c.length+t.length+f.length,D=U<g?Array(g-U+1).join(n):"";switch(M&&h&&(t=o(D+t,D.length?g-f.length:1/0),D=""),e){case"<":t=c+t+f+D;break;case"=":t=c+D+t+f;break;case"^":t=D.slice(0,U=D.length>>1)+c+t+f+D.slice(U);break;default:t=D+c+t+f}return p(t)}return w=void 0===w?6:/[gprs]/.test(x)?Math.max(1,Math.min(21,w)):Math.max(0,Math.min(20,w)),U.toString=function(){return t+""},U}return{format:M,formatPrefix:function(t,n){var e=M(((t=(0,l.Z)(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor((0,a.Z)(n)/3))),i=Math.pow(10,-r),o=d[8+r/3];return function(t){return e(i*t)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,u=i.formatPrefix},30970:(t,n,e)=>{e.d(n,{Z:()=>i});var r=e(23579);function i(t){return(t=(0,r.V)(Math.abs(t)))?t[1]:NaN}},23579:(t,n,e)=>{function r(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)}function i(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,r=t.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+t.slice(e+1)]}e.d(n,{V:()=>i,Z:()=>r})},92501:(t,n,e)=>{e.d(n,{Z:()=>i});var r=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function i(t){var n;if(!(n=r.exec(t)))throw Error("invalid format: "+t);return new o({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]})}function o(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}i.prototype=o.prototype,o.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type}},49495:(t,n,e)=>{e.d(n,{Z:()=>r});function r(t,n){var e=new Date;return t=+t,n=+n,function(r){return e.setTime(t*(1-r)+n*r),e}}},48480:(t,n,e)=>{e.d(n,{Z:()=>r});function r(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}},11552:(t,n,e)=>{e.d(n,{Z:()=>r});function r(t,n){return t=+t,n=+n,function(e){return Math.round(t*(1-e)+n*e)}}},59595:(t,n,e)=>{e.d(n,{Z:()=>function t(n,e){var i,u,l=typeof e;return null==e||"boolean"===l?o(e):("number"===l?s.Z:"string"===l?(u=(0,r.ZP)(e))?(e=u,a):function(t,n){var e,r,i,o,u,a=f.lastIndex=h.lastIndex=0,l=-1,c=[],g=[];for(t+="",n+="";(i=f.exec(t))&&(o=h.exec(n));)(u=o.index)>a&&(u=n.slice(a,u),c[l]?c[l]+=u:c[++l]=u),(i=i[0])===(o=o[0])?c[l]?c[l]+=o:c[++l]=o:(c[++l]=null,g.push({i:l,x:(0,s.Z)(i,o)})),a=h.lastIndex;return a<n.length&&(u=n.slice(a),c[l]?c[l]+=u:c[++l]=u),c.length<2?g[0]?(e=g[0].x,function(t){return e(t)+""}):(r=n,function(){return r}):(n=g.length,function(t){for(var e,r=0;r<n;++r)c[(e=g[r]).i]=e.x(t);return c.join("")})}:e instanceof r.ZP?a:e instanceof Date?c.Z:!ArrayBuffer.isView(i=e)||i instanceof DataView?Array.isArray(e)?function(n,e){var r,i=e?e.length:0,o=n?Math.min(i,n.length):0,u=Array(o),a=Array(i);for(r=0;r<o;++r)u[r]=t(n[r],e[r]);for(;r<i;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=u[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(n,e){var r,i={},o={};for(r in(null===n||"object"!=typeof n)&&(n={}),(null===e||"object"!=typeof e)&&(e={}),e)r in n?i[r]=t(n[r],e[r]):o[r]=e[r];return function(t){for(r in i)o[r]=i[r](t);return o}}:s.Z:function(t,n){n||(n=[]);var e,r=t?Math.min(n.length,t.length):0,i=n.slice();return function(o){for(e=0;e<r;++e)i[e]=t[e]*(1-o)+n[e]*o;return i}})(n,e)}});var r=e(33575);function i(t,n,e,r,i){var o=t*t,u=o*t;return((1-3*t+3*o-u)*n+(4-6*o+3*u)*e+(1+3*t+3*o-3*u)*r+u*i)/6}let o=t=>()=>t;function u(t,n){var e=n-t;return e?function(n){return t+n*e}:o(isNaN(t)?n:t)}let a=function t(n){var e,i=1==(e=+(e=n))?u:function(t,n){var r,i,u;return n-t?(r=t,i=n,r=Math.pow(r,u=e),i=Math.pow(i,u)-r,u=1/u,function(t){return Math.pow(r+t*i,u)}):o(isNaN(t)?n:t)};function a(t,n){var e=i((t=(0,r.B8)(t)).r,(n=(0,r.B8)(n)).r),o=i(t.g,n.g),a=i(t.b,n.b),l=u(t.opacity,n.opacity);return function(n){return t.r=e(n),t.g=o(n),t.b=a(n),t.opacity=l(n),t+""}}return a.gamma=t,a}(1);function l(t){return function(n){var e,i,o=n.length,u=Array(o),a=Array(o),l=Array(o);for(e=0;e<o;++e)i=(0,r.B8)(n[e]),u[e]=i.r||0,a[e]=i.g||0,l[e]=i.b||0;return u=t(u),a=t(a),l=t(l),i.opacity=1,function(t){return i.r=u(t),i.g=a(t),i.b=l(t),i+""}}}l(function(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),o=t[r],u=t[r+1],a=r>0?t[r-1]:2*o-u,l=r<n-1?t[r+2]:2*u-o;return i((e-r/n)*n,a,o,u,l)}}),l(function(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),o=t[(r+n-1)%n],u=t[r%n],a=t[(r+1)%n],l=t[(r+2)%n];return i((e-r/n)*n,o,u,a,l)}});var c=e(49495),s=e(48480),f=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,h=RegExp(f.source,"g")},13512:(t,n,e)=>{e.d(n,{Z:()=>o,x:()=>u});var r=e(17693),i=e(15528);function o(){var t,n,e=(0,i.Z)().unknown(void 0),u=e.domain,a=e.range,l=0,c=1,s=!1,f=0,h=0,g=.5;function d(){var e=u().length,r=c<l,i=r?c:l,o=r?l:c;t=(o-i)/Math.max(1,e-f+2*h),s&&(t=Math.floor(t)),i+=(o-i-t*(e-f))*g,n=t*(1-f),s&&(i=Math.round(i),n=Math.round(n));var d=(function(t,n,e){t=+t,n=+n,e=(i=arguments.length)<2?(n=t,t=0,1):i<3?1:+e;for(var r=-1,i=0|Math.max(0,Math.ceil((n-t)/e)),o=Array(i);++r<i;)o[r]=t+r*e;return o})(e).map(function(n){return i+t*n});return a(r?d.reverse():d)}return delete e.unknown,e.domain=function(t){return arguments.length?(u(t),d()):u()},e.range=function(t){return arguments.length?([l,c]=t,l=+l,c=+c,d()):[l,c]},e.rangeRound=function(t){return[l,c]=t,l=+l,c=+c,s=!0,d()},e.bandwidth=function(){return n},e.step=function(){return t},e.round=function(t){return arguments.length?(s=!!t,d()):s},e.padding=function(t){return arguments.length?(f=Math.min(1,h=+t),d()):f},e.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),d()):f},e.paddingOuter=function(t){return arguments.length?(h=+t,d()):h},e.align=function(t){return arguments.length?(g=Math.max(0,Math.min(1,t)),d()):g},e.copy=function(){return o(u(),[l,c]).round(s).paddingInner(f).paddingOuter(h).align(g)},r.o.apply(d(),arguments)}function u(){return function t(n){var e=n.copy;return n.padding=n.paddingOuter,delete n.paddingInner,delete n.paddingOuter,n.copy=function(){return t(e())},n}(o.apply(null,arguments).paddingInner(1))}},97499:(t,n,e)=>{e.d(n,{JG:()=>g,ZP:()=>p,yR:()=>c,l4:()=>d});var r=e(25734),i=e(59595),o=e(48480),u=e(11552),a=e(43143),l=[0,1];function c(t){return t}function s(t,n){var e;return(n-=t=+t)?function(e){return(e-t)/n}:(e=isNaN(n)?NaN:.5,function(){return e})}function f(t,n,e){var r=t[0],i=t[1],o=n[0],u=n[1];return i<r?(r=s(i,r),o=e(u,o)):(r=s(r,i),o=e(o,u)),function(t){return o(r(t))}}function h(t,n,e){var i=Math.min(t.length,n.length)-1,o=Array(i),u=Array(i),a=-1;for(t[i]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++a<i;)o[a]=s(t[a],t[a+1]),u[a]=e(n[a],n[a+1]);return function(n){var e=(0,r.ZP)(t,n,1,i)-1;return u[e](o[e](n))}}function g(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function d(){var t,n,e,r,s,g,d=l,p=l,y=i.Z,m=c;function v(){var t,n,e,i=Math.min(d.length,p.length);return m!==c&&(t=d[0],n=d[i-1],t>n&&(e=t,t=n,n=e),m=function(e){return Math.max(t,Math.min(n,e))}),r=i>2?h:f,s=g=null,M}function M(n){return null==n||isNaN(n=+n)?e:(s||(s=r(d.map(t),p,y)))(t(m(n)))}return M.invert=function(e){return m(n((g||(g=r(p,d.map(t),o.Z)))(e)))},M.domain=function(t){return arguments.length?(d=Array.from(t,a.Z),v()):d.slice()},M.range=function(t){return arguments.length?(p=Array.from(t),v()):p.slice()},M.rangeRound=function(t){return p=Array.from(t),y=u.Z,v()},M.clamp=function(t){return arguments.length?(m=!!t||c,v()):m!==c},M.interpolate=function(t){return arguments.length?(y=t,v()):y},M.unknown=function(t){return arguments.length?(e=t,M):e},function(e,r){return t=e,n=r,v()}}function p(){return d()(c,c)}},17693:(t,n,e)=>{function r(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function i(t,n){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof n?this.interpolator(n):this.range(n)}return this}e.d(n,{O:()=>i,o:()=>r})},15757:(t,n,e)=>{e.d(n,{Q:()=>a,Z:()=>function t(){var n=(0,i.ZP)();return n.copy=function(){return(0,i.JG)(n,t())},o.o.apply(n,arguments),a(n)}});var r=e(642),i=e(97499),o=e(17693),u=e(97129);function a(t){var n=t.domain;return t.ticks=function(t){var e=n();return(0,r.ZP)(e[0],e[e.length-1],null==t?10:t)},t.tickFormat=function(t,e){var r=n();return(0,u.Z)(r[0],r[r.length-1],null==t?10:t,e)},t.nice=function(e){null==e&&(e=10);var i,o,u=n(),a=0,l=u.length-1,c=u[a],s=u[l],f=10;for(s<c&&(o=c,c=s,s=o,o=a,a=l,l=o);f-- >0;){if((o=(0,r.G9)(c,s,e))===i)return u[a]=c,u[l]=s,n(u);if(o>0)c=Math.floor(c/o)*o,s=Math.ceil(s/o)*o;else if(o<0)c=Math.ceil(c*o)/o,s=Math.floor(s*o)/o;else break;i=o}return t},t}},2007:(t,n,e)=>{e.d(n,{Q:()=>p,Z:()=>function t(){let n=p((0,a.l4)()).domain([1,10]);return n.copy=()=>(0,a.JG)(n,t()).base(n.base()),l.o.apply(n,arguments),n}});var r=e(642),i=e(92501),o=e(78492),u=e(16879),a=e(97499),l=e(17693);function c(t){return Math.log(t)}function s(t){return Math.exp(t)}function f(t){return-Math.log(-t)}function h(t){return-Math.exp(-t)}function g(t){return isFinite(t)?+("1e"+t):t<0?0:t}function d(t){return(n,e)=>-t(-n,e)}function p(t){let n,e;let a=t(c,s),l=a.domain,p=10;function y(){var r,i;return n=(r=p)===Math.E?Math.log:10===r&&Math.log10||2===r&&Math.log2||(r=Math.log(r),t=>Math.log(t)/r),e=10===(i=p)?g:i===Math.E?Math.exp:t=>Math.pow(i,t),l()[0]<0?(n=d(n),e=d(e),t(f,h)):t(c,s),a}return a.base=function(t){return arguments.length?(p=+t,y()):p},a.domain=function(t){return arguments.length?(l(t),y()):l()},a.ticks=t=>{let i,o;let u=l(),a=u[0],c=u[u.length-1],s=c<a;s&&([a,c]=[c,a]);let f=n(a),h=n(c),g=null==t?10:+t,d=[];if(!(p%1)&&h-f<g){if(f=Math.floor(f),h=Math.ceil(h),a>0){for(;f<=h;++f)for(i=1;i<p;++i)if(!((o=f<0?i/e(-f):i*e(f))<a)){if(o>c)break;d.push(o)}}else for(;f<=h;++f)for(i=p-1;i>=1;--i)if(!((o=f>0?i/e(-f):i*e(f))<a)){if(o>c)break;d.push(o)}2*d.length<g&&(d=(0,r.ZP)(a,c,g))}else d=(0,r.ZP)(f,h,Math.min(h-f,g)).map(e);return s?d.reverse():d},a.tickFormat=(t,r)=>{if(null==t&&(t=10),null==r&&(r=10===p?"s":","),"function"!=typeof r&&(p%1||null!=(r=(0,i.Z)(r)).precision||(r.trim=!0),r=(0,o.WU)(r)),t===1/0)return r;let u=Math.max(1,p*t/a.ticks().length);return t=>{let i=t/e(Math.round(n(t)));return i*p<p-.5&&(i*=p),i<=u?r(t):""}},a.nice=()=>l((0,u.Z)(l(),{floor:t=>e(Math.floor(n(t))),ceil:t=>e(Math.ceil(n(t)))})),a}},16879:(t,n,e)=>{e.d(n,{Z:()=>r});function r(t,n){t=t.slice();var e,r=0,i=t.length-1,o=t[r],u=t[i];return u<o&&(e=r,r=i,i=e,e=o,o=u,u=e),t[r]=n.floor(o),t[i]=n.ceil(u),t}},43143:(t,n,e)=>{e.d(n,{Z:()=>r});function r(t){return+t}},15528:(t,n,e)=>{e.d(n,{Z:()=>function t(){var n=new r,e=[],i=[],o=a;function l(t){let r=n.get(t);if(void 0===r){if(o!==a)return o;n.set(t,r=e.push(t)-1)}return i[r%i.length]}return l.domain=function(t){if(!arguments.length)return e.slice();for(let i of(e=[],n=new r,t))n.has(i)||n.set(i,e.push(i)-1);return l},l.range=function(t){return arguments.length?(i=Array.from(t),l):i.slice()},l.unknown=function(t){return arguments.length?(o=t,l):o},l.copy=function(){return t(e,i).unknown(o)},u.o.apply(l,arguments),l},O:()=>a});class r extends Map{constructor(t,n=o){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),null!=t)for(let[n,e]of t)this.set(n,e)}get(t){return super.get(i(this,t))}has(t){return super.has(i(this,t))}set(t,n){return super.set(function({_intern:t,_key:n},e){let r=n(e);return t.has(r)?t.get(r):(t.set(r,e),e)}(this,t),n)}delete(t){return super.delete(function({_intern:t,_key:n},e){let r=n(e);return t.has(r)&&(e=t.get(r),t.delete(r)),e}(this,t))}}function i({_intern:t,_key:n},e){let r=n(e);return t.has(r)?t.get(r):e}function o(t){return null!==t&&"object"==typeof t?t.valueOf():t}var u=e(17693);let a=Symbol("implicit")},97530:(t,n,e)=>{e.d(n,{Hh:()=>c,ZP:()=>s,_b:()=>f});var r=e(15757),i=e(97499),o=e(17693);function u(t){return function(n){return n<0?-Math.pow(-n,t):Math.pow(n,t)}}function a(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function l(t){return t<0?-t*t:t*t}function c(t){var n=t(i.yR,i.yR),e=1;return n.exponent=function(n){return arguments.length?1==(e=+n)?t(i.yR,i.yR):.5===e?t(a,l):t(u(e),u(1/e)):e},(0,r.Q)(n)}function s(){var t=c((0,i.l4)());return t.copy=function(){return(0,i.JG)(t,s()).exponent(t.exponent())},o.o.apply(t,arguments),t}function f(){return s.apply(null,arguments).exponent(.5)}},41472:(t,n,e)=>{e.d(n,{JG:()=>h,L2:()=>d,S5:()=>function t(){var n=(0,l.Q)(f()).domain([1,10]);return n.copy=function(){return h(n,t()).base(n.base())},u.O.apply(n,arguments)},UN:()=>g,ZP:()=>function t(){var n=(0,a.Q)(f()(o.yR));return n.copy=function(){return h(n,t())},u.O.apply(n,arguments)},cV:()=>function t(){var n=(0,c.P)(f());return n.copy=function(){return h(n,t()).constant(n.constant())},u.O.apply(n,arguments)}});var r=e(59595),i=e(11552),o=e(97499),u=e(17693),a=e(15757),l=e(2007),c=e(29184),s=e(97530);function f(){var t,n,e,u,a,l=0,c=1,s=o.yR,f=!1;function h(n){return null==n||isNaN(n=+n)?a:s(0===e?.5:(n=(u(n)-t)*e,f?Math.max(0,Math.min(1,n)):n))}function g(t){return function(n){var e,r;return arguments.length?([e,r]=n,s=t(e,r),h):[s(0),s(1)]}}return h.domain=function(r){return arguments.length?([l,c]=r,t=u(l=+l),n=u(c=+c),e=t===n?0:1/(n-t),h):[l,c]},h.clamp=function(t){return arguments.length?(f=!!t,h):f},h.interpolator=function(t){return arguments.length?(s=t,h):s},h.range=g(r.Z),h.rangeRound=g(i.Z),h.unknown=function(t){return arguments.length?(a=t,h):a},function(r){return u=r,t=r(l),n=r(c),e=t===n?0:1/(n-t),h}}function h(t,n){return n.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function g(){var t=(0,s.Hh)(f());return t.copy=function(){return h(t,g()).exponent(t.exponent())},u.O.apply(t,arguments)}function d(){return g.apply(null,arguments).exponent(.5)}},29184:(t,n,e)=>{e.d(n,{P:()=>l,Z:()=>function t(){var n=l((0,i.l4)());return n.copy=function(){return(0,i.JG)(n,t()).constant(n.constant())},o.o.apply(n,arguments)}});var r=e(15757),i=e(97499),o=e(17693);function u(t){return function(n){return Math.sign(n)*Math.log1p(Math.abs(n/t))}}function a(t){return function(n){return Math.sign(n)*Math.expm1(Math.abs(n))*t}}function l(t){var n=1,e=t(u(1),a(n));return e.constant=function(e){return arguments.length?t(u(n=+e),a(n)):n},(0,r.Q)(e)}},27817:(t,n,e)=>{e.d(n,{Z:()=>function t(){var n,e=[.5],o=[0,1],u=1;function a(t){return null!=t&&t<=t?o[(0,r.ZP)(e,t,0,u)]:n}return a.domain=function(t){return arguments.length?(u=Math.min((e=Array.from(t)).length,o.length-1),a):e.slice()},a.range=function(t){return arguments.length?(o=Array.from(t),u=Math.min(e.length,o.length-1),a):o.slice()},a.invertExtent=function(t){var n=o.indexOf(t);return[e[n-1],e[n]]},a.unknown=function(t){return arguments.length?(n=t,a):n},a.copy=function(){return t().domain(e).range(o).unknown(n)},i.o.apply(a,arguments)}});var r=e(25734),i=e(17693)},97129:(t,n,e)=>{e.d(n,{Z:()=>a});var r=e(642),i=e(92501),o=e(30970),u=e(78492);function a(t,n,e,a){var l,c,s,f=(0,r.ly)(t,n,e);switch((a=(0,i.Z)(null==a?",f":a)).type){case"s":var h=Math.max(Math.abs(t),Math.abs(n));return null!=a.precision||isNaN(s=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor((0,o.Z)(h)/3)))-(0,o.Z)(Math.abs(f))))||(a.precision=s),(0,u.jH)(a,h);case"":case"e":case"g":case"p":case"r":null!=a.precision||isNaN((l=f,c=Math.abs(c=Math.max(Math.abs(t),Math.abs(n)))-(l=Math.abs(l)),s=Math.max(0,(0,o.Z)(c)-(0,o.Z)(l))+1))||(a.precision=s-("e"===a.type));break;case"f":case"%":null!=a.precision||isNaN(s=Math.max(0,-(0,o.Z)(Math.abs(f))))||(a.precision=s-("%"===a.type)*2)}return(0,u.WU)(a)}},90261:(t,n,e)=>{e.d(n,{Y:()=>m,Z:()=>v});var r=e(9865),i=e(47538),o=e(55140),u=e(63919),a=e(29363),l=e(90221),c=e(78572),s=e(76939),f=e(30027),h=e(97499),g=e(17693),d=e(16879);function p(t){return new Date(t)}function y(t){return t instanceof Date?+t:+new Date(+t)}function m(t,n,e,r,i,o,u,a,l,c){var s=(0,h.ZP)(),f=s.invert,g=s.domain,v=c(".%L"),M=c(":%S"),w=c("%I:%M"),b=c("%I %p"),x=c("%a %d"),Z=c("%b %d"),$=c("%B"),T=c("%Y");function C(t){return(l(t)<t?v:a(t)<t?M:u(t)<t?w:o(t)<t?b:r(t)<t?i(t)<t?x:Z:e(t)<t?$:T)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?g(Array.from(t,y)):g().map(p)},s.ticks=function(n){var e=g();return t(e[0],e[e.length-1],null==n?10:n)},s.tickFormat=function(t,n){return null==n?C:c(n)},s.nice=function(t){var e=g();return t&&"function"==typeof t.range||(t=n(e[0],e[e.length-1],null==t?10:t)),t?g((0,d.Z)(e,t)):s},s.copy=function(){return(0,h.JG)(s,m(t,n,e,r,i,o,u,a,l,c))},s}function v(){return g.o.apply(m(r.jK,r._g,i.jB,o.F0,u.Zy,a.rr,l.WQ,c.Z_,s.E,f.i$).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}},69428:(t,n,e)=>{e.d(n,{Z:()=>d});var r=e(9865),i=e(47538),o=e(55140),u=e(63919),a=e(29363),l=e(90221),c=e(78572),s=e(76939),f=e(30027),h=e(90261),g=e(17693);function d(){return g.o.apply((0,h.Y)(r.WG,r.jo,i.ol,o.me,u.pI,a.AN,l.lM,c.rz,s.E,f.g0).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}},10557:(t,n,e)=>{function r(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}e.d(n,{Z:()=>r}),Array.prototype.slice},68223:(t,n,e)=>{e.d(n,{Z:()=>r});function r(t){return function(){return t}}},60430:(t,n,e)=>{e.d(n,{BZ:()=>g,Fp:()=>u,Ho:()=>s,Kh:()=>d,O$:()=>l,VV:()=>a,Wn:()=>r,ZR:()=>p,_b:()=>c,fv:()=>i,mC:()=>o,ou:()=>h,pi:()=>f});let r=Math.abs,i=Math.atan2,o=Math.cos,u=Math.max,a=Math.min,l=Math.sin,c=Math.sqrt,s=1e-12,f=Math.PI,h=f/2,g=2*f;function d(t){return t>1?0:t<-1?f:Math.acos(t)}function p(t){return t>=1?h:t<=-1?-h:Math.asin(t)}},40435:(t,n,e)=>{e.d(n,{Z:()=>i});var r=e(55148);function i(t,n){if((i=t.length)>0){for(var e,i,o,u=0,a=t[0].length;u<a;++u){for(o=e=0;e<i;++e)o+=t[e][u][1]||0;if(o)for(e=0;e<i;++e)t[e][u][1]/=o}(0,r.Z)(t,n)}}},55148:(t,n,e)=>{e.d(n,{Z:()=>r});function r(t,n){if((i=t.length)>1)for(var e,r,i,o=1,u=t[n[0]],a=u.length;o<i;++o)for(r=u,u=t[n[o]],e=0;e<a;++e)u[e][1]+=u[e][0]=isNaN(r[e][1])?r[e][0]:r[e][1]}},42903:(t,n,e)=>{e.d(n,{Z:()=>i});var r=e(55148);function i(t,n){if((e=t.length)>0){for(var e,i=0,o=t[n[0]],u=o.length;i<u;++i){for(var a=0,l=0;a<e;++a)l+=t[a][i][1]||0;o[i][1]+=o[i][0]=-l/2}(0,r.Z)(t,n)}}},6950:(t,n,e)=>{e.d(n,{Z:()=>i});var r=e(55148);function i(t,n){if((o=t.length)>0&&(i=(e=t[n[0]]).length)>0){for(var e,i,o,u=0,a=1;a<i;++a){for(var l=0,c=0,s=0;l<o;++l){for(var f=t[n[l]],h=f[a][1]||0,g=(h-(f[a-1][1]||0))/2,d=0;d<l;++d){var p=t[n[d]];g+=(p[a][1]||0)-(p[a-1][1]||0)}c+=h,s+=g*h}e[a-1][1]+=e[a-1][0]=u,c&&(u-=s/c)}e[a-1][1]+=e[a-1][0]=u,(0,r.Z)(t,n)}}},14474:(t,n,e)=>{e.d(n,{Z:()=>r});function r(t){for(var n=t.length,e=Array(n);--n>=0;)e[n]=n;return e}},4443:(t,n,e)=>{e.d(n,{d:()=>l});let r=Math.PI,i=2*r,o=i-1e-6;function u(t){this._+=t[0];for(let n=1,e=t.length;n<e;++n)this._+=arguments[n]+t[n]}class a{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?u:function(t){let n=Math.floor(t);if(!(n>=0))throw Error(`invalid digits: ${t}`);if(n>15)return u;let e=10**n;return function(t){this._+=t[0];for(let n=1,r=t.length;n<r;++n)this._+=Math.round(arguments[n]*e)/e+t[n]}}(t)}moveTo(t,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,n){this._append`L${this._x1=+t},${this._y1=+n}`}quadraticCurveTo(t,n,e,r){this._append`Q${+t},${+n},${this._x1=+e},${this._y1=+r}`}bezierCurveTo(t,n,e,r,i,o){this._append`C${+t},${+n},${+e},${+r},${this._x1=+i},${this._y1=+o}`}arcTo(t,n,e,i,o){if(t=+t,n=+n,e=+e,i=+i,(o=+o)<0)throw Error(`negative radius: ${o}`);let u=this._x1,a=this._y1,l=e-t,c=i-n,s=u-t,f=a-n,h=s*s+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=n}`;else if(h>1e-6){if(Math.abs(f*l-c*s)>1e-6&&o){let g=e-u,d=i-a,p=l*l+c*c,y=Math.sqrt(p),m=Math.sqrt(h),v=o*Math.tan((r-Math.acos((p+h-(g*g+d*d))/(2*y*m)))/2),M=v/m,w=v/y;Math.abs(M-1)>1e-6&&this._append`L${t+M*s},${n+M*f}`,this._append`A${o},${o},0,0,${+(f*g>s*d)},${this._x1=t+w*l},${this._y1=n+w*c}`}else this._append`L${this._x1=t},${this._y1=n}`}}arc(t,n,e,u,a,l){if(t=+t,n=+n,l=!!l,(e=+e)<0)throw Error(`negative radius: ${e}`);let c=e*Math.cos(u),s=e*Math.sin(u),f=t+c,h=n+s,g=1^l,d=l?u-a:a-u;null===this._x1?this._append`M${f},${h}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-h)>1e-6)&&this._append`L${f},${h}`,e&&(d<0&&(d=d%i+i),d>o?this._append`A${e},${e},0,1,${g},${t-c},${n-s}A${e},${e},0,1,${g},${this._x1=f},${this._y1=h}`:d>1e-6&&this._append`A${e},${e},0,${+(d>=r)},${g},${this._x1=t+e*Math.cos(a)},${this._y1=n+e*Math.sin(a)}`)}rect(t,n,e,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}h${e=+e}v${+r}h${-e}Z`}toString(){return this._}}function l(t){let n=3;return t.digits=function(e){if(!arguments.length)return n;if(null==e)n=null;else{let t=Math.floor(e);if(!(t>=0))throw RangeError(`invalid digits: ${e}`);n=t}return t},()=>new a(n)}a.prototype},11829:(t,n,e)=>{e.d(n,{Z:()=>c});var r=e(10557),i=e(68223),o=e(55148),u=e(14474);function a(t,n){return t[n]}function l(t){let n=[];return n.key=t,n}function c(){var t=(0,i.Z)([]),n=u.Z,e=o.Z,c=a;function s(i){var o,u,a=Array.from(t.apply(this,arguments),l),s=a.length,f=-1;for(let t of i)for(o=0,++f;o<s;++o)(a[o][f]=[0,+c(t,a[o].key,f,i)]).data=t;for(o=0,u=(0,r.Z)(n(a));o<s;++o)a[u[o]].index=o;return e(a,u),a}return s.keys=function(n){return arguments.length?(t="function"==typeof n?n:(0,i.Z)(Array.from(n)),s):t},s.value=function(t){return arguments.length?(c="function"==typeof t?t:(0,i.Z)(+t),s):c},s.order=function(t){return arguments.length?(n=null==t?u.Z:"function"==typeof t?t:(0,i.Z)(Array.from(t)),s):n},s.offset=function(t){return arguments.length?(e=null==t?o.Z:t,s):e},s}},30027:(t,n,e)=>{e.d(n,{i$:()=>i,g0:()=>o});var r,i,o,u=e(63919),a=e(29363),l=e(47538);function c(t){if(0<=t.y&&t.y<100){var n=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return n.setFullYear(t.y),n}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function s(t){if(0<=t.y&&t.y<100){var n=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return n.setUTCFullYear(t.y),n}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function f(t,n,e){return{y:t,m:n,d:e,H:0,M:0,S:0,L:0}}var h={"-":"",_:" ",0:"0"},g=/^\s*\d+/,d=/^%/,p=/[\\^$*+?|[\]().{}]/g;function y(t,n,e){var r=t<0?"-":"",i=(r?-t:t)+"",o=i.length;return r+(o<e?Array(e-o+1).join(n)+i:i)}function m(t){return t.replace(p,"\\$&")}function v(t){return RegExp("^(?:"+t.map(m).join("|")+")","i")}function M(t){return new Map(t.map((t,n)=>[t.toLowerCase(),n]))}function w(t,n,e){var r=g.exec(n.slice(e,e+1));return r?(t.w=+r[0],e+r[0].length):-1}function b(t,n,e){var r=g.exec(n.slice(e,e+1));return r?(t.u=+r[0],e+r[0].length):-1}function x(t,n,e){var r=g.exec(n.slice(e,e+2));return r?(t.U=+r[0],e+r[0].length):-1}function Z(t,n,e){var r=g.exec(n.slice(e,e+2));return r?(t.V=+r[0],e+r[0].length):-1}function $(t,n,e){var r=g.exec(n.slice(e,e+2));return r?(t.W=+r[0],e+r[0].length):-1}function T(t,n,e){var r=g.exec(n.slice(e,e+4));return r?(t.y=+r[0],e+r[0].length):-1}function C(t,n,e){var r=g.exec(n.slice(e,e+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),e+r[0].length):-1}function U(t,n,e){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(n.slice(e,e+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),e+r[0].length):-1}function D(t,n,e){var r=g.exec(n.slice(e,e+1));return r?(t.q=3*r[0]-3,e+r[0].length):-1}function k(t,n,e){var r=g.exec(n.slice(e,e+2));return r?(t.m=r[0]-1,e+r[0].length):-1}function N(t,n,e){var r=g.exec(n.slice(e,e+2));return r?(t.d=+r[0],e+r[0].length):-1}function _(t,n,e){var r=g.exec(n.slice(e,e+3));return r?(t.m=0,t.d=+r[0],e+r[0].length):-1}function Y(t,n,e){var r=g.exec(n.slice(e,e+2));return r?(t.H=+r[0],e+r[0].length):-1}function A(t,n,e){var r=g.exec(n.slice(e,e+2));return r?(t.M=+r[0],e+r[0].length):-1}function F(t,n,e){var r=g.exec(n.slice(e,e+2));return r?(t.S=+r[0],e+r[0].length):-1}function H(t,n,e){var r=g.exec(n.slice(e,e+3));return r?(t.L=+r[0],e+r[0].length):-1}function S(t,n,e){var r=g.exec(n.slice(e,e+6));return r?(t.L=Math.floor(r[0]/1e3),e+r[0].length):-1}function j(t,n,e){var r=d.exec(n.slice(e,e+1));return r?e+r[0].length:-1}function B(t,n,e){var r=g.exec(n.slice(e));return r?(t.Q=+r[0],e+r[0].length):-1}function J(t,n,e){var r=g.exec(n.slice(e));return r?(t.s=+r[0],e+r[0].length):-1}function P(t,n){return y(t.getDate(),n,2)}function E(t,n){return y(t.getHours(),n,2)}function O(t,n){return y(t.getHours()%12||12,n,2)}function q(t,n){return y(1+a.rr.count((0,l.jB)(t),t),n,3)}function L(t,n){return y(t.getMilliseconds(),n,3)}function I(t,n){return L(t,n)+"000"}function R(t,n){return y(t.getMonth()+1,n,2)}function z(t,n){return y(t.getMinutes(),n,2)}function V(t,n){return y(t.getSeconds(),n,2)}function W(t){var n=t.getDay();return 0===n?7:n}function Q(t,n){return y(u.Zy.count((0,l.jB)(t)-1,t),n,2)}function G(t){var n=t.getDay();return n>=4||0===n?(0,u.Ig)(t):u.Ig.ceil(t)}function X(t,n){return t=G(t),y(u.Ig.count((0,l.jB)(t),t)+(4===(0,l.jB)(t).getDay()),n,2)}function K(t){return t.getDay()}function tt(t,n){return y(u.Ox.count((0,l.jB)(t)-1,t),n,2)}function tn(t,n){return y(t.getFullYear()%100,n,2)}function te(t,n){return y((t=G(t)).getFullYear()%100,n,2)}function tr(t,n){return y(t.getFullYear()%1e4,n,4)}function ti(t,n){var e=t.getDay();return y((t=e>=4||0===e?(0,u.Ig)(t):u.Ig.ceil(t)).getFullYear()%1e4,n,4)}function to(t){var n=t.getTimezoneOffset();return(n>0?"-":(n*=-1,"+"))+y(n/60|0,"0",2)+y(n%60,"0",2)}function tu(t,n){return y(t.getUTCDate(),n,2)}function ta(t,n){return y(t.getUTCHours(),n,2)}function tl(t,n){return y(t.getUTCHours()%12||12,n,2)}function tc(t,n){return y(1+a.AN.count((0,l.ol)(t),t),n,3)}function ts(t,n){return y(t.getUTCMilliseconds(),n,3)}function tf(t,n){return ts(t,n)+"000"}function th(t,n){return y(t.getUTCMonth()+1,n,2)}function tg(t,n){return y(t.getUTCMinutes(),n,2)}function td(t,n){return y(t.getUTCSeconds(),n,2)}function tp(t){var n=t.getUTCDay();return 0===n?7:n}function ty(t,n){return y(u.pI.count((0,l.ol)(t)-1,t),n,2)}function tm(t){var n=t.getUTCDay();return n>=4||0===n?(0,u.hB)(t):u.hB.ceil(t)}function tv(t,n){return t=tm(t),y(u.hB.count((0,l.ol)(t),t)+(4===(0,l.ol)(t).getUTCDay()),n,2)}function tM(t){return t.getUTCDay()}function tw(t,n){return y(u.l6.count((0,l.ol)(t)-1,t),n,2)}function tb(t,n){return y(t.getUTCFullYear()%100,n,2)}function tx(t,n){return y((t=tm(t)).getUTCFullYear()%100,n,2)}function tZ(t,n){return y(t.getUTCFullYear()%1e4,n,4)}function t$(t,n){var e=t.getUTCDay();return y((t=e>=4||0===e?(0,u.hB)(t):u.hB.ceil(t)).getUTCFullYear()%1e4,n,4)}function tT(){return"+0000"}function tC(){return"%"}function tU(t){return+t}function tD(t){return Math.floor(+t/1e3)}i=(r=function(t){var n=t.dateTime,e=t.date,r=t.time,i=t.periods,o=t.days,l=t.shortDays,g=t.months,d=t.shortMonths,p=v(i),y=M(i),m=v(o),G=M(o),tm=v(l),tk=M(l),tN=v(g),t_=M(g),tY=v(d),tA=M(d),tF={a:function(t){return l[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return d[t.getMonth()]},B:function(t){return g[t.getMonth()]},c:null,d:P,e:P,f:I,g:te,G:ti,H:E,I:O,j:q,L:L,m:R,M:z,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:tU,s:tD,S:V,u:W,U:Q,V:X,w:K,W:tt,x:null,X:null,y:tn,Y:tr,Z:to,"%":tC},tH={a:function(t){return l[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return d[t.getUTCMonth()]},B:function(t){return g[t.getUTCMonth()]},c:null,d:tu,e:tu,f:tf,g:tx,G:t$,H:ta,I:tl,j:tc,L:ts,m:th,M:tg,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:tU,s:tD,S:td,u:tp,U:ty,V:tv,w:tM,W:tw,x:null,X:null,y:tb,Y:tZ,Z:tT,"%":tC},tS={a:function(t,n,e){var r=tm.exec(n.slice(e));return r?(t.w=tk.get(r[0].toLowerCase()),e+r[0].length):-1},A:function(t,n,e){var r=m.exec(n.slice(e));return r?(t.w=G.get(r[0].toLowerCase()),e+r[0].length):-1},b:function(t,n,e){var r=tY.exec(n.slice(e));return r?(t.m=tA.get(r[0].toLowerCase()),e+r[0].length):-1},B:function(t,n,e){var r=tN.exec(n.slice(e));return r?(t.m=t_.get(r[0].toLowerCase()),e+r[0].length):-1},c:function(t,e,r){return tJ(t,n,e,r)},d:N,e:N,f:S,g:C,G:T,H:Y,I:Y,j:_,L:H,m:k,M:A,p:function(t,n,e){var r=p.exec(n.slice(e));return r?(t.p=y.get(r[0].toLowerCase()),e+r[0].length):-1},q:D,Q:B,s:J,S:F,u:b,U:x,V:Z,w:w,W:$,x:function(t,n,r){return tJ(t,e,n,r)},X:function(t,n,e){return tJ(t,r,n,e)},y:C,Y:T,Z:U,"%":j};function tj(t,n){return function(e){var r,i,o,u=[],a=-1,l=0,c=t.length;for(e instanceof Date||(e=new Date(+e));++a<c;)37===t.charCodeAt(a)&&(u.push(t.slice(l,a)),null!=(i=h[r=t.charAt(++a)])?r=t.charAt(++a):i="e"===r?" ":"0",(o=n[r])&&(r=o(e,i)),u.push(r),l=a+1);return u.push(t.slice(l,a)),u.join("")}}function tB(t,n){return function(e){var r,i,o=f(1900,void 0,1);if(tJ(o,t,e+="",0)!=e.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!n||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(r=(i=(r=s(f(o.y,0,1))).getUTCDay())>4||0===i?u.l6.ceil(r):(0,u.l6)(r),r=a.AN.offset(r,(o.V-1)*7),o.y=r.getUTCFullYear(),o.m=r.getUTCMonth(),o.d=r.getUTCDate()+(o.w+6)%7):(r=(i=(r=c(f(o.y,0,1))).getDay())>4||0===i?u.Ox.ceil(r):(0,u.Ox)(r),r=a.rr.offset(r,(o.V-1)*7),o.y=r.getFullYear(),o.m=r.getMonth(),o.d=r.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:"W"in o?1:0),i="Z"in o?s(f(o.y,0,1)).getUTCDay():c(f(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,s(o)):c(o)}}function tJ(t,n,e,r){for(var i,o,u=0,a=n.length,l=e.length;u<a;){if(r>=l)return -1;if(37===(i=n.charCodeAt(u++))){if(!(o=tS[(i=n.charAt(u++))in h?n.charAt(u++):i])||(r=o(t,e,r))<0)return -1}else if(i!=e.charCodeAt(r++))return -1}return r}return tF.x=tj(e,tF),tF.X=tj(r,tF),tF.c=tj(n,tF),tH.x=tj(e,tH),tH.X=tj(r,tH),tH.c=tj(n,tH),{format:function(t){var n=tj(t+="",tF);return n.toString=function(){return t},n},parse:function(t){var n=tB(t+="",!1);return n.toString=function(){return t},n},utcFormat:function(t){var n=tj(t+="",tH);return n.toString=function(){return t},n},utcParse:function(t){var n=tB(t+="",!0);return n.toString=function(){return t},n}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,r.parse,o=r.utcFormat,r.utcParse},29363:(t,n,e)=>{e.d(n,{AN:()=>u,KB:()=>a,rr:()=>o});var r=e(29827),i=e(95566);let o=(0,r.J)(t=>t.setHours(0,0,0,0),(t,n)=>t.setDate(t.getDate()+n),(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*i.yB)/i.UD,t=>t.getDate()-1);o.range;let u=(0,r.J)(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/i.UD,t=>t.getUTCDate()-1);u.range;let a=(0,r.J)(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/i.UD,t=>Math.floor(t/i.UD));a.range},95566:(t,n,e)=>{e.d(n,{UD:()=>u,Y2:()=>o,Ym:()=>r,iM:()=>a,jz:()=>l,qz:()=>c,yB:()=>i});let r=1e3,i=6e4,o=36e5,u=864e5,a=6048e5,l=2592e6,c=31536e6},90221:(t,n,e)=>{e.d(n,{WQ:()=>o,lM:()=>u});var r=e(29827),i=e(95566);let o=(0,r.J)(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*i.Ym-t.getMinutes()*i.yB)},(t,n)=>{t.setTime(+t+n*i.Y2)},(t,n)=>(n-t)/i.Y2,t=>t.getHours());o.range;let u=(0,r.J)(t=>{t.setUTCMinutes(0,0,0)},(t,n)=>{t.setTime(+t+n*i.Y2)},(t,n)=>(n-t)/i.Y2,t=>t.getUTCHours());u.range},29827:(t,n,e)=>{e.d(n,{J:()=>function t(n,e,o,u){function a(t){return n(t=0==arguments.length?new Date:new Date(+t)),t}return a.floor=t=>(n(t=new Date(+t)),t),a.ceil=t=>(n(t=new Date(t-1)),e(t,1),n(t),t),a.round=t=>{let n=a(t),e=a.ceil(t);return t-n<e-t?n:e},a.offset=(t,n)=>(e(t=new Date(+t),null==n?1:Math.floor(n)),t),a.range=(t,r,i)=>{let o;let u=[];if(t=a.ceil(t),i=null==i?1:Math.floor(i),!(t<r)||!(i>0))return u;do u.push(o=new Date(+t)),e(t,i),n(t);while(o<t&&t<r);return u},a.filter=r=>t(t=>{if(t>=t)for(;n(t),!r(t);)t.setTime(t-1)},(t,n)=>{if(t>=t){if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}}),o&&(a.count=(t,e)=>(r.setTime(+t),i.setTime(+e),n(r),n(i),Math.floor(o(r,i))),a.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?a.filter(u?n=>u(n)%t==0:n=>a.count(0,n)%t==0):a:null),a}});let r=new Date,i=new Date},78572:(t,n,e)=>{e.d(n,{Z_:()=>o,rz:()=>u});var r=e(29827),i=e(95566);let o=(0,r.J)(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*i.Ym)},(t,n)=>{t.setTime(+t+n*i.yB)},(t,n)=>(n-t)/i.yB,t=>t.getMinutes());o.range;let u=(0,r.J)(t=>{t.setUTCSeconds(0,0)},(t,n)=>{t.setTime(+t+n*i.yB)},(t,n)=>(n-t)/i.yB,t=>t.getUTCMinutes());u.range},55140:(t,n,e)=>{e.d(n,{F0:()=>i,me:()=>o});var r=e(29827);let i=(0,r.J)(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,n)=>{t.setMonth(t.getMonth()+n)},(t,n)=>n.getMonth()-t.getMonth()+(n.getFullYear()-t.getFullYear())*12,t=>t.getMonth());i.range;let o=(0,r.J)(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCMonth(t.getUTCMonth()+n)},(t,n)=>n.getUTCMonth()-t.getUTCMonth()+(n.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());o.range},76939:(t,n,e)=>{e.d(n,{E:()=>o});var r=e(29827),i=e(95566);let o=(0,r.J)(t=>{t.setTime(t-t.getMilliseconds())},(t,n)=>{t.setTime(+t+n*i.Ym)},(t,n)=>(n-t)/i.Ym,t=>t.getUTCSeconds());o.range},9865:(t,n,e)=>{e.d(n,{_g:()=>M,jK:()=>v,jo:()=>m,WG:()=>y});var r=e(51612),i=e(642),o=e(95566),u=e(29827);let a=(0,u.J)(()=>{},(t,n)=>{t.setTime(+t+n)},(t,n)=>n-t);a.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?(0,u.J)(n=>{n.setTime(Math.floor(n/t)*t)},(n,e)=>{n.setTime(+n+e*t)},(n,e)=>(e-n)/t):a:null,a.range;var l=e(76939),c=e(78572),s=e(90221),f=e(29363),h=e(63919),g=e(55140),d=e(47538);function p(t,n,e,u,c,s){let f=[[l.E,1,o.Ym],[l.E,5,5*o.Ym],[l.E,15,15*o.Ym],[l.E,30,30*o.Ym],[s,1,o.yB],[s,5,5*o.yB],[s,15,15*o.yB],[s,30,30*o.yB],[c,1,o.Y2],[c,3,3*o.Y2],[c,6,6*o.Y2],[c,12,12*o.Y2],[u,1,o.UD],[u,2,2*o.UD],[e,1,o.iM],[n,1,o.jz],[n,3,3*o.jz],[t,1,o.qz]];function h(n,e,u){let l=Math.abs(e-n)/u,c=(0,r.Z)(([,,t])=>t).right(f,l);if(c===f.length)return t.every((0,i.ly)(n/o.qz,e/o.qz,u));if(0===c)return a.every(Math.max((0,i.ly)(n,e,u),1));let[s,h]=f[l/f[c-1][2]<f[c][2]/l?c-1:c];return s.every(h)}return[function(t,n,e){let r=n<t;r&&([t,n]=[n,t]);let i=e&&"function"==typeof e.range?e:h(t,n,e),o=i?i.range(t,+n+1):[];return r?o.reverse():o},h]}let[y,m]=p(d.ol,g.me,h.pI,f.KB,s.lM,c.rz),[v,M]=p(d.jB,g.F0,h.Zy,f.rr,s.WQ,c.Z_)},63919:(t,n,e)=>{e.d(n,{Ig:()=>s,Ox:()=>a,Zy:()=>u,hB:()=>v,l6:()=>p,pI:()=>d});var r=e(29827),i=e(95566);function o(t){return(0,r.J)(n=>{n.setDate(n.getDate()-(n.getDay()+7-t)%7),n.setHours(0,0,0,0)},(t,n)=>{t.setDate(t.getDate()+7*n)},(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*i.yB)/i.iM)}let u=o(0),a=o(1),l=o(2),c=o(3),s=o(4),f=o(5),h=o(6);function g(t){return(0,r.J)(n=>{n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7),n.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+7*n)},(t,n)=>(n-t)/i.iM)}u.range,a.range,l.range,c.range,s.range,f.range,h.range;let d=g(0),p=g(1),y=g(2),m=g(3),v=g(4),M=g(5),w=g(6);d.range,p.range,y.range,m.range,v.range,M.range,w.range},47538:(t,n,e)=>{e.d(n,{jB:()=>i,ol:()=>o});var r=e(29827);let i=(0,r.J)(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,n)=>{t.setFullYear(t.getFullYear()+n)},(t,n)=>n.getFullYear()-t.getFullYear(),t=>t.getFullYear());i.every=t=>isFinite(t=Math.floor(t))&&t>0?(0,r.J)(n=>{n.setFullYear(Math.floor(n.getFullYear()/t)*t),n.setMonth(0,1),n.setHours(0,0,0,0)},(n,e)=>{n.setFullYear(n.getFullYear()+e*t)}):null,i.range;let o=(0,r.J)(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n)},(t,n)=>n.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());o.every=t=>isFinite(t=Math.floor(t))&&t>0?(0,r.J)(n=>{n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t),n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)},(n,e)=>{n.setUTCFullYear(n.getUTCFullYear()+e*t)}):null,o.range}};