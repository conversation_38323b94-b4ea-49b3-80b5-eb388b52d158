"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadMiddleware = void 0;
const multer_1 = __importDefault(require("multer"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const messages_1 = require("@/utils/helpers/messages");
const imageMaxSize = 1024 * 1024 * 2;
const pdfmaxSize = 1024 * 1024 * 5;
const PDF_MIME_TYPES = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
const IMAGE_MIME_TYPES = ['image/png', 'image/jpg', 'image/jpeg', 'image/webp'];
const fileFilter = (request, file, callback) => {
    let allowedMimeTypes = [];
    let maxSize = 0;
    if (PDF_MIME_TYPES.includes(file.mimetype)) {
        allowedMimeTypes = PDF_MIME_TYPES;
        maxSize = pdfmaxSize;
    }
    else if (IMAGE_MIME_TYPES.includes(file.mimetype)) {
        allowedMimeTypes = IMAGE_MIME_TYPES;
        maxSize = imageMaxSize;
    }
    const isAllowed = allowedMimeTypes.length > 0 && allowedMimeTypes.includes(file.mimetype);
    if (isAllowed) {
        callback(null, true);
    }
    else {
        callback(new http_exception_1.default(400, messages_1.MESSAGES.FILE.READ_ERROR_FILE));
    }
};
const storage = multer_1.default.diskStorage({
    destination: function (request, file, callback) {
        const { resource, folder } = request.params;
        const uploadPath = path.join(__dirname, '../../uploads/', resource, folder);
        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
        }
        callback(null, uploadPath);
    },
    filename: function (request, file, callback) {
        const uuid = request.params.filename;
        const fileName = file.originalname.split('.');
        const fileExtension = file.originalname.split('.')[fileName.length - 1].toLowerCase();
        const newFilename = `${uuid}.${fileExtension}`;
        callback(null, newFilename);
    },
});
const upload = (0, multer_1.default)({
    storage,
    fileFilter,
});
const uploadMiddleware = (request, response, next) => {
    upload.single('file')(request, response, function (error) {
        if (error) {
            return next(new http_exception_1.default(400, messages_1.MESSAGES.FILE.ERROR_UPLOAD));
        }
        next();
    });
};
exports.uploadMiddleware = uploadMiddleware;
//# sourceMappingURL=upload.middleware.js.map