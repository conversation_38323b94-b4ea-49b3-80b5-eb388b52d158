"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/suneditor-react";
exports.ids = ["vendor-chunks/suneditor-react"];
exports.modules = {

/***/ "(ssr)/./node_modules/suneditor-react/dist/buttons/buttonList.js":
/*!*****************************************************************!*\
  !*** ./node_modules/suneditor-react/dist/buttons/buttonList.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.formatting = exports.complex = exports.basic = void 0;\nexports.basic = [\n    [\"font\", \"fontSize\"],\n    [\"fontColor\"],\n    [\"horizontalRule\"],\n    [\"link\", \"image\"],\n];\nexports.complex = [\n    [\"undo\", \"redo\"],\n    [\"font\", \"fontSize\", \"formatBlock\"],\n    [\"bold\", \"underline\", \"italic\", \"strike\", \"subscript\", \"superscript\"],\n    [\"removeFormat\"],\n    \"/\",\n    [\"fontColor\", \"hiliteColor\"],\n    [\"outdent\", \"indent\"],\n    [\"align\", \"horizontalRule\", \"list\", \"table\"],\n    [\"link\", \"image\", \"video\"],\n    [\"fullScreen\", \"showBlocks\", \"codeView\"],\n    [\"preview\", \"print\"],\n    [\"save\", \"template\"],\n];\nexports.formatting = [\n    [\"undo\", \"redo\"],\n    [\"bold\", \"underline\", \"italic\", \"strike\", \"subscript\", \"superscript\"],\n    [\"removeFormat\"],\n    [\"outdent\", \"indent\"],\n    [\"fullScreen\", \"showBlocks\", \"codeView\"],\n    [\"preview\", \"print\"],\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/suneditor-react/dist/buttons/buttonList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/suneditor-react/dist/components/SunEditor.js":
/*!*******************************************************************!*\
  !*** ./node_modules/suneditor-react/dist/components/SunEditor.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar react_1 = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar plugins_1 = __importDefault(__webpack_require__(/*! suneditor/src/plugins */ \"(ssr)/./node_modules/suneditor/src/plugins/index.js\"));\nvar suneditor_1 = __importDefault(__webpack_require__(/*! suneditor */ \"(ssr)/./node_modules/suneditor/src/suneditor.js\"));\nvar getLanguage_1 = __importDefault(__webpack_require__(/*! ../lang/getLanguage */ \"(ssr)/./node_modules/suneditor-react/dist/lang/getLanguage.js\"));\nvar events_1 = __webpack_require__(/*! ../data/events */ \"(ssr)/./node_modules/suneditor-react/dist/data/events.js\");\nvar SunEditor = function (props) {\n    var name = props.name, lang = props.lang, _a = props.setOptions, setOptions = _a === void 0 ? {} : _a, placeholder = props.placeholder, _b = props.width, width = _b === void 0 ? \"100%\" : _b, height = props.height, defaultValue = props.defaultValue, setContents = props.setContents, setDefaultStyle = props.setDefaultStyle, getSunEditorInstance = props.getSunEditorInstance, appendContents = props.appendContents, _c = props.setAllPlugins, setAllPlugins = _c === void 0 ? true : _c, _d = props.disable, disable = _d === void 0 ? false : _d, _e = props.readOnly, readOnly = _e === void 0 ? false : _e, _f = props.hide, hide = _f === void 0 ? false : _f, _g = props.hideToolbar, hideToolbar = _g === void 0 ? false : _g, _h = props.disableToolbar, disableToolbar = _h === void 0 ? false : _h, onChange = props.onChange, autoFocus = props.autoFocus, onBlur = props.onBlur, onLoad = props.onLoad;\n    var txtArea = (0, react_1.useRef)(null);\n    var editor = (0, react_1.useRef)(null);\n    var initialEffect = (0, react_1.useRef)(true);\n    (0, react_1.useEffect)(function () {\n        var _a;\n        var options = __assign(__assign({}, setOptions), { lang: lang ? (0, getLanguage_1.default)(lang) : setOptions.lang, width: width !== null && width !== void 0 ? width : setOptions.width, placeholder: placeholder !== null && placeholder !== void 0 ? placeholder : setOptions.placeholder, plugins: (_a = setOptions.plugins) !== null && _a !== void 0 ? _a : (setAllPlugins ? plugins_1.default : undefined), height: height !== null && height !== void 0 ? height : setOptions.height, value: defaultValue !== null && defaultValue !== void 0 ? defaultValue : setOptions.value, defaultStyle: setDefaultStyle !== null && setDefaultStyle !== void 0 ? setDefaultStyle : setOptions.defaultStyle });\n        if (name && options.value)\n            txtArea.current.value = options.value;\n        editor.current = suneditor_1.default.create(txtArea.current, options);\n        if (getSunEditorInstance)\n            getSunEditorInstance(editor.current);\n        editor.current.onload = function (_, reload) {\n            if (reload)\n                return onLoad === null || onLoad === void 0 ? void 0 : onLoad(reload);\n            if (setContents) {\n                editor.current.setContents(setContents);\n                editor.current.core.focusEdge(null);\n            }\n            if (appendContents)\n                editor.current.appendContents(appendContents);\n            if (editor.current.util.isIE)\n                editor.current.core._createDefaultRange();\n            if (disable)\n                editor.current.disable();\n            if (readOnly)\n                editor.current.readOnly(true);\n            if (hide)\n                editor.current.hide();\n            if (hideToolbar)\n                editor.current.toolbar.hide();\n            if (disableToolbar)\n                editor.current.toolbar.disable();\n            if (autoFocus === false)\n                editor.current.core.context.element.wysiwyg.blur();\n            else if (autoFocus)\n                editor.current.core.context.element.wysiwyg.focus();\n            return onLoad === null || onLoad === void 0 ? void 0 : onLoad(reload);\n        };\n        editor.current.onChange = function (content) {\n            if (name && txtArea.current)\n                txtArea.current.value = content;\n            if (onChange)\n                onChange(content);\n        };\n        if (onBlur) {\n            editor.current.onBlur = function (e) {\n                return onBlur(e, editor.current.getContents(true));\n            };\n        }\n        events_1.uploadBeforeEvents.forEach(function (event) {\n            var value = props[event];\n            if (editor.current && value)\n                editor.current[event] = function (files, info, _, uploadHandler) { return value(files, info, uploadHandler); };\n        });\n        events_1.events.forEach(function (event) {\n            var value = props[event];\n            if (value && editor.current) {\n                editor.current[event] = value;\n            }\n        });\n        return function () {\n            if (editor.current)\n                editor.current.destroy();\n            editor.current = null;\n        };\n    }, []);\n    (0, react_1.useEffect)(function () {\n        var _a;\n        if (initialEffect.current)\n            return;\n        (_a = editor.current) === null || _a === void 0 ? void 0 : _a.setOptions({\n            lang: (0, getLanguage_1.default)(lang),\n        });\n    }, [lang]);\n    (0, react_1.useEffect)(function () {\n        var _a;\n        if (initialEffect.current)\n            return;\n        (_a = editor.current) === null || _a === void 0 ? void 0 : _a.setOptions({\n            placeholder: placeholder,\n            height: height,\n            width: width,\n        });\n    }, [placeholder, height, width]);\n    (0, react_1.useEffect)(function () {\n        var _a;\n        if (setDefaultStyle && !initialEffect.current)\n            (_a = editor.current) === null || _a === void 0 ? void 0 : _a.setDefaultStyle(setDefaultStyle);\n    }, [setDefaultStyle]);\n    (0, react_1.useEffect)(function () {\n        var _a, _b;\n        if (!initialEffect.current &&\n            setContents !== undefined &&\n            !((_a = editor.current) === null || _a === void 0 ? void 0 : _a.core.hasFocus)) {\n            (_b = editor.current) === null || _b === void 0 ? void 0 : _b.setContents(setContents);\n        }\n    }, [setContents]);\n    (0, react_1.useEffect)(function () {\n        var _a, _b, _c;\n        if (!initialEffect.current &&\n            appendContents !== undefined &&\n            !((_a = editor.current) === null || _a === void 0 ? void 0 : _a.core.hasFocus)) {\n            (_b = editor.current) === null || _b === void 0 ? void 0 : _b.appendContents(appendContents);\n            (_c = editor.current) === null || _c === void 0 ? void 0 : _c.core.focusEdge(null);\n        }\n    }, [appendContents]);\n    (0, react_1.useEffect)(function () {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n        if (initialEffect.current)\n            return;\n        (_a = editor.current) === null || _a === void 0 ? void 0 : _a.readOnly(readOnly);\n        if (hideToolbar)\n            (_b = editor.current) === null || _b === void 0 ? void 0 : _b.toolbar.hide();\n        else\n            (_c = editor.current) === null || _c === void 0 ? void 0 : _c.toolbar.show();\n        if (disableToolbar)\n            (_d = editor.current) === null || _d === void 0 ? void 0 : _d.toolbar.disable();\n        else\n            (_e = editor.current) === null || _e === void 0 ? void 0 : _e.toolbar.enable();\n        if (disable)\n            (_f = editor.current) === null || _f === void 0 ? void 0 : _f.disable();\n        else\n            (_g = editor.current) === null || _g === void 0 ? void 0 : _g.enable();\n        if (hide)\n            (_h = editor.current) === null || _h === void 0 ? void 0 : _h.hide();\n        else\n            (_j = editor.current) === null || _j === void 0 ? void 0 : _j.show();\n    }, [disable, hideToolbar, disableToolbar, hide, readOnly]);\n    (0, react_1.useEffect)(function () {\n        initialEffect.current = false;\n    }, []);\n    return (react_1.default.createElement(\"textarea\", __assign({ style: { visibility: \"hidden\" }, ref: txtArea }, { name: name })));\n};\nexports[\"default\"] = SunEditor;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/suneditor-react/dist/components/SunEditor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/suneditor-react/dist/data/events.js":
/*!**********************************************************!*\
  !*** ./node_modules/suneditor-react/dist/data/events.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.uploadBeforeEvents = exports.events = void 0;\nexports.events = [\n    \"onMouseDown\",\n    \"onScroll\",\n    \"onInput\",\n    \"onClick\",\n    \"onKeyUp\",\n    \"onKeyDown\",\n    \"onFocus\",\n    \"onImageUpload\",\n    \"onAudioUpload\",\n    \"onVideoUpload\",\n    \"onImageUploadError\",\n    \"onVideoUploadError\",\n    \"onAudioUploadError\",\n    \"onSave\",\n    \"onSetToolbarButtons\",\n    \"imageUploadHandler\",\n    \"toggleCodeView\",\n    \"toggleFullScreen\",\n    \"showInline\",\n    \"showController\",\n    \"onCopy\",\n    \"onCut\",\n    \"onDrop\",\n    \"onPaste\",\n];\nexports.uploadBeforeEvents = [\n    \"onImageUploadBefore\",\n    \"onVideoUploadBefore\",\n    \"onAudioUploadBefore\",\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VuZWRpdG9yLXJlYWN0L2Rpc3QvZGF0YS9ldmVudHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsMEJBQTBCLEdBQUcsY0FBYztBQUMzQyxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvc3VuZWRpdG9yLXJlYWN0L2Rpc3QvZGF0YS9ldmVudHMuanM/NzA5YyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMudXBsb2FkQmVmb3JlRXZlbnRzID0gZXhwb3J0cy5ldmVudHMgPSB2b2lkIDA7XG5leHBvcnRzLmV2ZW50cyA9IFtcbiAgICBcIm9uTW91c2VEb3duXCIsXG4gICAgXCJvblNjcm9sbFwiLFxuICAgIFwib25JbnB1dFwiLFxuICAgIFwib25DbGlja1wiLFxuICAgIFwib25LZXlVcFwiLFxuICAgIFwib25LZXlEb3duXCIsXG4gICAgXCJvbkZvY3VzXCIsXG4gICAgXCJvbkltYWdlVXBsb2FkXCIsXG4gICAgXCJvbkF1ZGlvVXBsb2FkXCIsXG4gICAgXCJvblZpZGVvVXBsb2FkXCIsXG4gICAgXCJvbkltYWdlVXBsb2FkRXJyb3JcIixcbiAgICBcIm9uVmlkZW9VcGxvYWRFcnJvclwiLFxuICAgIFwib25BdWRpb1VwbG9hZEVycm9yXCIsXG4gICAgXCJvblNhdmVcIixcbiAgICBcIm9uU2V0VG9vbGJhckJ1dHRvbnNcIixcbiAgICBcImltYWdlVXBsb2FkSGFuZGxlclwiLFxuICAgIFwidG9nZ2xlQ29kZVZpZXdcIixcbiAgICBcInRvZ2dsZUZ1bGxTY3JlZW5cIixcbiAgICBcInNob3dJbmxpbmVcIixcbiAgICBcInNob3dDb250cm9sbGVyXCIsXG4gICAgXCJvbkNvcHlcIixcbiAgICBcIm9uQ3V0XCIsXG4gICAgXCJvbkRyb3BcIixcbiAgICBcIm9uUGFzdGVcIixcbl07XG5leHBvcnRzLnVwbG9hZEJlZm9yZUV2ZW50cyA9IFtcbiAgICBcIm9uSW1hZ2VVcGxvYWRCZWZvcmVcIixcbiAgICBcIm9uVmlkZW9VcGxvYWRCZWZvcmVcIixcbiAgICBcIm9uQXVkaW9VcGxvYWRCZWZvcmVcIixcbl07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/suneditor-react/dist/data/events.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/suneditor-react/dist/index.js":
/*!****************************************************!*\
  !*** ./node_modules/suneditor-react/dist/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.buttonList = void 0;\nvar SunEditor_1 = __importDefault(__webpack_require__(/*! ./components/SunEditor */ \"(ssr)/./node_modules/suneditor-react/dist/components/SunEditor.js\"));\nexports.buttonList = __importStar(__webpack_require__(/*! ./buttons/buttonList */ \"(ssr)/./node_modules/suneditor-react/dist/buttons/buttonList.js\"));\nexports[\"default\"] = SunEditor_1.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/suneditor-react/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/suneditor-react/dist/lang/getLanguage.js":
/*!***************************************************************!*\
  !*** ./node_modules/suneditor-react/dist/lang/getLanguage.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar getLanguage = function (lang) {\n    switch (typeof lang) {\n        case \"object\":\n            return lang;\n        case \"string\":\n            return __webpack_require__(\"(ssr)/./node_modules/suneditor/src/lang ./node_modules/suneditor/src/lang sync recursive ^\\\\.\\\\/.*\\\\.js$\")(\"./\".concat(lang, \".js\"));\n        default:\n            return undefined;\n    }\n};\nexports[\"default\"] = getLanguage;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VuZWRpdG9yLXJlYWN0L2Rpc3QvbGFuZy9nZXRMYW5ndWFnZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGdJQUFRLElBQXFCLGNBQWMsS0FBSyxDQUFDLENBQUM7QUFDckU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBZSIsInNvdXJjZXMiOlsid2VicGFjazovL3BlbnRhYmVsbC8uL25vZGVfbW9kdWxlcy9zdW5lZGl0b3ItcmVhY3QvZGlzdC9sYW5nL2dldExhbmd1YWdlLmpzP2Y0ODIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG52YXIgZ2V0TGFuZ3VhZ2UgPSBmdW5jdGlvbiAobGFuZykge1xuICAgIHN3aXRjaCAodHlwZW9mIGxhbmcpIHtcbiAgICAgICAgY2FzZSBcIm9iamVjdFwiOlxuICAgICAgICAgICAgcmV0dXJuIGxhbmc7XG4gICAgICAgIGNhc2UgXCJzdHJpbmdcIjpcbiAgICAgICAgICAgIHJldHVybiByZXF1aXJlKFwic3VuZWRpdG9yL3NyYy9sYW5nL1wiLmNvbmNhdChsYW5nLCBcIi5qc1wiKSk7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIH1cbn07XG5leHBvcnRzLmRlZmF1bHQgPSBnZXRMYW5ndWFnZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/suneditor-react/dist/lang/getLanguage.js\n");

/***/ })

};
;