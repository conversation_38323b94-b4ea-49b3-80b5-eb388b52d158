import { useEffect, useState } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Card,
  CardContent,
  Grid,
  TextField,
} from "@mui/material";

import CustomButton from "@/components/ui/CustomButton";
import SvgexpandIcon from "@/assets/images/icons/arrowUp.svg";
import SvgRefreshIcon from "@/assets/images/icons/refreshIcon.svg";
import CustomPieChart from "@/components/charts/CustomPieChart";
import { useGetArticlesStat } from "../stats.hooks";

export default function ArticlesByVisibility({ t }) {
  const [dateFromArticle, setDateFromArticle] = useState("2024-09-01");
  const [dateToArticle, setDateToArticle] = useState(() => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  });
  const [searchArticle, setSearchArticle] = useState(false);

  const resetSearchArticles = () => {
    setDateToArticle(() => {
      const today = new Date();
      return today.toISOString().split("T")[0];
    });
    setDateFromArticle("2024-09-01");
    setSearchArticle(!searchArticle);
  };

  const getDataPieArticles = useGetArticlesStat({
    dateFrom: dateFromArticle,
    dateTo: dateToArticle,
    barChart: null,
  });

  const pieChart = {
    title: t("statsDash:articlesByVisibility"),
    dataset: getDataPieArticles?.data?.map((article) => ({
      label: article.visibility,
      value: article.totalArticles,
    })),
    colors: ["#234791", "#FFCA00", "#006A67"],
  };

  useEffect(() => {
    getDataPieArticles.refetch();
  }, [searchArticle]);

  return (
    <Card className="card">
      <CardContent>
        <p className="heading-h3" gutterBottom>
          {pieChart.title}
        </p>
        <Accordion elevation={0} disableGutters={true}>
          <AccordionSummary
            aria-controls="panel1bh-content"
            id="panel1bh-header"
            className="svg-accordion"
            expandIcon={<SvgexpandIcon />}
          >
            <h3 className="label-pentabell">{t("statsDash:filters")}</h3>
          </AccordionSummary>
          <AccordionDetails elevation={0}>
            <Grid container className="chart-grid" spacing={1}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label={t("statsDash:fromDate")}
                  type="date"
                  value={dateFromArticle}
                  onChange={(e) => setDateFromArticle(e.target.value)}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label={t("statsDash:toDate")}
                  type="date"
                  value={dateToArticle}
                  onChange={(e) => setDateToArticle(e.target.value)}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={3} sm={1} md={4} className="btns-filter dashboard">
                <CustomButton
                  icon={<SvgRefreshIcon />}
                  className={"btn btn-outlined btn-refresh full-width"}
                  onClick={resetSearchArticles}
                />
              </Grid>
              <Grid item xs={11} sm={11} md={8}>
                <CustomButton
                  text={t("statsDash:filter")}
                  onClick={() => {
                    setSearchArticle(!searchArticle);
                  }}
                  className={"btn btn-outlined btn-filter-stat full-width"}
                />
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
        <div className="chart-wrapper">
          {" "}
          <CustomPieChart donuts={false} chart={pieChart} />{" "}
          {pieChart.dataset?.some((item) => item["value"] > 0) && (
            <div className="labelstats-wrapper">
              <div className="label-wrapper">
                <span className="public-dot" />
                <span className="label-chart">{t("statsDash:public")}</span>
              </div>
              <div className="label-wrapper">
                <span className="privatearticles-dot" />
                <span className="label-chart">{t("statsDash:private")}</span>
              </div>
              <div className="label-wrapper">
                <span className="draft-dot" />
                <span className="label-chart">{t("statsDash:draft")}</span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
