import { useMutation, useQuery, useQueryClient } from "react-query";

import {
  createArticle,
  getArticleById,
  getArticles,
  getCategories,
  updateArticle,
  getArticleByUrlANDlanguages,
  getSlugBySlug,
  getArticleByCategory,
  getArticleWithPrevAndNext,
  getComments,
  getarchivedArticles,
  getArticlesDashboard,
  updatearticleall,
  getArticleByIdAll,
  getArticlesTitles,
  desarchiveversions,
  getCommentByiD,
  updateAutoSave,
  createAutoSave,
} from "../services/blog.service";
import { websiteRoutesList } from "@/helpers/routesList";
import { deleteArticle } from "../services/blog.service";
// import { useDispatch } from "react-redux";
// import { logout } from "../../../slices/auth";

export const useCreateArticle = () => {
  return useMutation({
    mutationFn: (body) => {
      return createArticle(body);
    },
    onError: (err) => {
      err.message = "";
    },
  });
};
export const useCreateAutoSave = () => {
  return useMutation({
    mutationFn: (body) => {
      return createAutoSave(body);
    },
    onError: (err) => {
      err.message = "";
    },
  });
};

export const useUpdateAutoSave = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data, id) => {
      return updateAutoSave(data, id);
    },
    onError: (err) => {
      err.message = "";
    },
  });
};
export const useUpdateArticle = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data, language, id) => {
      return updateArticle(data, language, id);
    },
    onError: (err) => {
      err.message = "";
    },
  });
};

export const usearchivedarticle = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ language, id, archive }) => {
      return desarchiveversions(language, id, archive);
    },
    onError: (err) => {
      console.error("Error during mutation", err);
      err.message = "";
    },
  });
};

export const useUpdateArticleAll = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data, id) => {
      return updatearticleall(data, id);
    },
    onError: (err) => {
      err.message = "";
    },
  });
};
export const useGetCategories = (language) => {
  return useQuery(["category", language], async () => {
    const data = await getCategories(language);
    return data;
  });
};
export const useGetServices = (language) => {
  return useQuery(["service", language], async () => {
    const data = await getCategories(language);
    return data;
  });
};
export const useGetarchivedArticles = (body) => {
  // const dispatch = useDispatch();
  // const navigate = useNavigate();
  return useQuery("article", async () => {
    try {
      const data = await getarchivedArticles(body);
      return data;
    } catch (err) {
      if (err.response.status === 401) {
        // dispatch(logout);
        localStorage.removeItem("user");
        window.location.href = "/";
      }
    }
  });
};

export const useGetArticles = (body) => {
  return useQuery("article", async () => {
    const data = await getArticles(body);
    return data;
  });
};
export const useGetArticlesDashboard = (body) => {
  return useQuery(`articles${body.language}`, async () => {
    const data = await getArticlesDashboard(body);
    return data;
  });
};
export const useGetArticlesTitles = (body) => {
  return useQuery(`articlestitles${body.language}`, async () => {
    const data = await getArticlesTitles(body);
    return data;
  });
};
export const useGetComments = (body, options = {}) => {
  return useQuery(
    "comment",
    async () => {
      const data = await getComments(body);
      return data;
    },
    {
      ...options,
    }
  );
};

export const useGetArticleByUrlANDlanguage = (body, options = {}) => {
  // const navigate = useNavigate();
  return useQuery(
    ["article", body],
    async () => {
      try {
        const data = await getArticleByUrlANDlanguages(body);
        return data;
      } catch (error) {
        if (error.response && error.response.status === 404) {
          if (body.language === "en") window.location.href = "/blog/";
          else window.location.href = "/fr/blog/";
        }
        throw error;
      }
    },
    {
      onError: (error) => {
        console.error("Error fetching article:", error.message);
        // You can add more error handling logic here if needed
      },
      ...options,
    }
  );
};

export const useGetArticleWithPrevAndNext = (body) => {
  // const navigate = useNavigate();
  return useQuery(
    ["articlePrev", body],
    async () => {
      try {
        const data = await getArticleWithPrevAndNext(body);
        return data;
      } catch (error) {
        if (error.response && error.response.status === 404) {
          window.location.href = "/blog/";
        }

        throw error;
      }
    },
    {
      onError: (error) => {
        console.error("Error fetching article:", error.message);
        // You can add more error handling logic here if needed
      },
    }
  );
};

export const useGetSlugBySlug = (body) => {
  // const navigate = useNavigate();
  return useQuery(
    ["article", body],
    async () => {
      try {
        const data = await getSlugBySlug(body);
        return data;
      } catch (error) {
        if (error.response && error.response.status === 404) {
          if (body.language === "en")
            window.location.href = `/${websiteRoutesList.blog.route}/${body.urlArticle}/`;
          else
            window.location.href = `/fr/${websiteRoutesList.blog.route}/${body.urlArticle}/`;
        }

        throw error;
      }
    },
    {
      onError: (error) => {
        console.error("Error fetching article:", error.message);
        // You can add more error handling logic here if needed
      },
    }
  );
};

export const useGetArticleByCategory = (body) => {
  // const navigate = useNavigate();
  return useQuery(
    ["articleBycategory", body],
    async () => {
      try {
        const data = await getArticleByCategory(body);
        if (
          data.totalArticles === 0 &&
          body.language === "en" &&
          body.title.length === 0
        )
          window.location.href = "/blog/";
        else if (
          data.totalArticles === 0 &&
          body.language === "fr" &&
          body.title.length === 0
        )
          window.location.href = "/fr/blog/";
        return data;
      } catch (error) {
        if (error.response && error.response.status === 404) {
          if (body.language === "en") window.location.href = "/blog/";
          else window.location.href = "/fr/blog/";
        }
        throw error;
      }
    },
    {
      onError: (error) => {
        console.error("Error fetching article:", error.message);
        // You can add more error handling logic here if needed
      },
    }
  );
};

export const useGetArticleById = (articleId, language) => {
  return useQuery(["article", articleId, language], async () => {
    const data = await getArticleById(articleId, language);
    return data;
  });
};

export const useGetArticleByIdAll = (articleId) => {
  return useQuery(["articleall", articleId], async () => {
    const data = await getArticleByIdAll(articleId);
    return data;
  });
};
export const useGetCommentsById = (commentId) => {
  return useQuery(["comment", commentId], async () => {
    const data = await getCommentByiD(commentId);
    return data;
  });
};

export const useDeleteArticle = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ language, id }) => {
      return deleteArticle(language, id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries("articles");
    },
    onError: (err) => {
      console.error("Error deleting article:", err);
      err.message = "";
    },
  });
};
