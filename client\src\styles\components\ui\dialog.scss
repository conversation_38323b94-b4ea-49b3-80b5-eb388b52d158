
.modal {

  top: 0;
  position: fixed;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 3, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}
.date-modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  max-height: 70vh;
  background-color: white; 
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0,0,0,0.3); 
  padding: 16px; 
  overflow-y: auto; 
}

#toggle {

  .dialog-paper {

    color: #f8f8f8;
  }

  .dialog-content {
    flex: 1 1 auto;
    overflow-y: auto;
    padding: 16px 24px;
    border-top: 1px solid transparent !important;
    border-bottom: 1px solid transparent !important;
  }

  .dialog-actions {
    display: flex;
    align-items: center !important;
    justify-content: center !important;
    padding: 8px;
  }

  .css-1cennmq-MuiBackdrop-root-MuiDialog-backdrop {
    background-color: rgb(0 0 0 / 14%) !important;

  }



}

.css-10d30g3-MuiPaper-root-MuiDialog-paper {

  box-shadow: 0px 11px 15px -7px rgb(0 0 0 / 0%), 0px 24px 38px 3px rgb(0 0 0 / 0%), 0px 9px 46px 8px rgb(0 0 0 / 0%) !important;
}

.css-6hw5d-MuiButtonBase-root-MuiSwitch-switchBase.Mui-checked+.MuiSwitch-track {
  background-color: #234791 !important;
}

.modal-content-popup {
  @include gradientBg();
  position: fixed;
  padding: 18px;
  color: #ffffff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  width: 40%;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;

  .message {
    display: flex;
    justify-content: center;

  }
}

.popup-icon {
  display: flex;
  justify-content: center;
}

.btn-container1 {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}




.btn-popup {
  font-family: "Proxima-Nova-Regular" !important;
  font-size: 14px;
  padding: 10px 40px;
  text-align: center;
  color: #234791;
  border-color: transparent;
  text-decoration: none;
  background-color: transparent;
  border: 1px solid transparent;
  height: fit-content;
  display: flex;
  align-items: center;
  margin: 0px 5px;
  background-color: #ffca00;
  border: 1px solid #ffca00;
}

.btn-outlined-popup {
  font-family: "Proxima-Nova-Regular" !important;
  font-size: 14px;
  padding: 10px 40px;
  text-align: center;
  color: #ffffff;
  border-color: transparent;
  text-decoration: none;
  background-color: transparent;
  border: 1px solid transparent;
  height: fit-content;
  display: flex;
  align-items: center;
  background-color: transparent;
  border: 1px solid #f7f7f7;
}



.btn-container-popup {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}


.label-pentabell-popup {
  font-size: 25px !important;
  font-family: "Proxima-Nova-Regular" !important;
  text-transform: capitalize !important;
  color: #ffffff !important;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: stretch;
  justify-content: center;
}

.label-form-delete {
  font-size: 16px;
  font-family: "Proxima-Nova-Regular" !important;
  text-transform: capitalize;
  color: #ffffff;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}

.select-pentabell-delete {
  width: 100%;
  height: 100%;

  .MuiInputBase-root,
  .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
    height: 29px !important;
    background-color: transparent;
    height: 100%;
    border: 1px solid #ffffff;
    color: #ffffff;
  }

  .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
    padding: 0px 5px;
  }

  input {
    height: 26px;

    .select-pentabell {
      width: 100%;
      height: 100%;

      .MuiInputBase-root,
      .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
        height: 29px !important;
        background-color: $lightBlue;
        height: 100%;
      }

      .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
        padding: 0px 5px;
      }

      input {
        height: 26px;
        background-color: $lightBlue;
        padding-left: 10px;
        width: 100%;
        color: $grey;
      }

      &.light {
        &.react-international-phone-input-container .react-international-phone-country-selector-button {
          background-color: transparent;
          border: 0px;
          border-radius: 0;
          border-bottom: 1px solid #0d2849;
        }

        &.react-international-phone-input-container .react-international-phone-input {
          border: 0px;
          border-radius: 0;
          border-bottom: 1px solid #0d2849;
        }

        &::placeholder {
          color: $white;
        }

        .MuiInputBase-root {
          background-color: transparent;
        }

        input {
          color: $white;
          background-color: transparent;
        }
      }
    }

    padding-left: 10px;
    width: 100%;
    color: $grey;
  }

  &.light {
    &.react-international-phone-input-container .react-international-phone-country-selector-button {
      background-color: transparent;
      border: 0px;
      border-radius: 0;
      border-radius: 0;
      border-bottom: 1px solid #0d2849;
    }

    &.react-international-phone-input-container .react-international-phone-input {
      border: 0px;
      border-radius: 0;
      border-bottom: 1px solid #0d2849;
    }

    &::placeholder {
      color: $white;
    }

    .MuiInputBase-root {
      background-color: transparent;
    }

    input {
      color: $white;
      background-color: transparent;
    }
  }
}

.label-popup {
  color: #ffca00;
}

.close-icon {
  position: absolute;
  top: 18px;
  right: 0px;
  border: none;
  width: 24px;
  height: 24px;
  background-color: transparent;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;

  .hover {
    opacity: 1;
  }
}


@include media-query(mobile) {
  .modal-content-popup {
    width: 95%;
    padding: 12px;
  }

  .btn-container-popup {
    flex-direction: column;
    gap: 5px;
  }

  .message {
    font-size: 12px;
    padding: 18px;
  }
}

.confirmation-center {

  display: flex;
  align-content: stretch;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 32px !important;

}

.confirmation-dialog-icon{
  text-align: center !important;
  display: flex !important;
  align-content: stretch;
  justify-content: center !important;
  align-items: baseline;
  margin-bottom: 10px !important;
}

.text-confirmation-h1{
  font-family: "Proxima-Nova-Medium" !important;
  text-align: center !important;
  font-size: 24px;
   display: flex;
  align-content: stretch;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 10px !important;
}

.text-confirmation-second{
  font-family: "Proxima-Nova-Medium" !important;
  text-align: center !important;
  font-size: 16px;
   display: flex;
  align-content: stretch;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 13px !important;
}

