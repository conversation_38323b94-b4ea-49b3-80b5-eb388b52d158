"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/AddArticleFR.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! suneditor-react */ \"(app-pages-browser)/./node_modules/suneditor-react/dist/index.js\");\n/* harmony import */ var suneditor_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(suneditor_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @feelinglovelynow/slug */ \"(app-pages-browser)/./node_modules/@feelinglovelynow/slug/dist/index.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n/* harmony import */ var suneditor_dist_css_suneditor_min_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! suneditor/dist/css/suneditor.min.css */ \"(app-pages-browser)/./node_modules/suneditor/dist/css/suneditor.min.css\");\n/* harmony import */ var react_tag_input__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-tag-input */ \"(app-pages-browser)/./node_modules/react-tag-input/dist/index.js\");\n/* harmony import */ var suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! suneditor/src/plugins */ \"(app-pages-browser)/./node_modules/suneditor/src/plugins/index.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,MenuItem,Select,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js\");\n/* harmony import */ var _mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/x-date-pickers */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/DatePicker/DatePicker.js\");\n/* harmony import */ var _mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/x-date-pickers/internals/demo */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/internals/demo/DemoContainer.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/x-date-pickers/AdapterDayjs */ \"(app-pages-browser)/./node_modules/@mui/x-date-pickers/AdapterDayjs/AdapterDayjs.js\");\n/* harmony import */ var _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AddArticleFR(param) {\n    let { errors, touched, setFieldValue, values, onImageSelect, categories, filteredCategories, onCategoriesSelect, debounce } = param;\n    _s();\n    const handlePaste = (event, cleanData, maxCharCount)=>{\n        let html = cleanData;\n        // Correction des balises non fermées\n        html = html.replace(/<strong>(.*?)$/g, \"<strong>$1</strong>\");\n        return html;\n    };\n    const KeyCodes = {\n        comma: 188,\n        enter: 13\n    };\n    const delimiters = [\n        KeyCodes.comma,\n        KeyCodes.enter\n    ];\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [highlights, setHighlights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [publishNow, setPublishNow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const language = \"fr\";\n    const handlePhotoChange = async ()=>{\n        const selectedFile = imageInputRef.current.files[0];\n        setSelectedImage(imageInputRef.current.files[0]);\n        if (selectedFile) {\n            onImageSelect(selectedFile, language);\n        }\n    };\n    const useSaveFileHook = (0,_features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_11__.useSaveFile)();\n    const handlePhotoBlogChange = async (file, info, core, uploadHandler)=>{\n        if (file instanceof HTMLImageElement) {\n            const src = file.src;\n            if (src.startsWith(\"data:image\")) {\n                const base64Data = src.split(\",\")[1];\n                const contentType = src.match(/data:(.*?);base64/)[1];\n                const byteCharacters = atob(base64Data);\n                const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i)=>byteCharacters.charCodeAt(i));\n                const byteArray = new Uint8Array(byteNumbers);\n                const blob = new Blob([\n                    byteArray\n                ], {\n                    type: contentType\n                });\n                const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                const selectedFile = new File([\n                    blob\n                ], fileName, {\n                    type: contentType\n                });\n                await uploadFile(selectedFile, uploadHandler, core, file);\n            } else {\n                fetch(src).then((response)=>response.blob()).then((blob)=>{\n                    const contentType = blob.type;\n                    const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                    const selectedFile = new File([\n                        blob\n                    ], fileName, {\n                        type: contentType\n                    });\n                    uploadFile(selectedFile, uploadHandler, core, file);\n                }).catch((error)=>console.error(\"Error converting image URL to Blob:\", error));\n            }\n        } else {\n            console.error(\"File is not an HTMLImageElement.\");\n        }\n    };\n    const uploadFile = (selectedFile, uploadHandler, core, originalImage)=>{\n        let uuidPhoto;\n        uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_13__[\"default\"])().replace(/-/g, \"\");\n        const formData = new FormData();\n        formData.append(\"file\", selectedFile);\n        const extension = selectedFile.name.split(\".\").pop();\n        const currentYear = new Date().getFullYear();\n        useSaveFileHook.mutate({\n            resource: \"blogs\",\n            folder: currentYear.toString(),\n            filename: uuidPhoto,\n            body: {\n                formData,\n                t\n            }\n        }, {\n            onSuccess: (dataUUID)=>{\n                const uuidPhotoFileName = dataUUID.message === \"uuid exist\" ? dataUUID.uuid : `${uuidPhoto}.${extension}`;\n                const imageUrl = `${\"http://localhost:4000/api/v1\"}/files/${uuidPhotoFileName}`;\n                originalImage.src = imageUrl;\n                uploadHandler({\n                    result: [\n                        {\n                            id: uuidPhotoFileName,\n                            url: imageUrl\n                        }\n                    ]\n                });\n            },\n            onError: (error)=>{\n                console.error(\"Error uploading file:\", error);\n            }\n        });\n    };\n    const [titlefr, setTitlefr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedTitlefr = localStorage.getItem(\"titlefr\");\n        return savedTitlefr ? JSON.parse(savedTitlefr) : \"\";\n    });\n    const [metatitlefr, setMetatitlefr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetatitlefr = localStorage.getItem(\"metatitlefr\");\n        return savedMetatitlefr ? JSON.parse(savedMetatitlefr) : \"\";\n    });\n    const [metaDescriptionfr, setMetaDescriptionfr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetadescriptionfr = localStorage.getItem(\"metaDescriptionfr\");\n        return savedMetadescriptionfr ? JSON.parse(savedMetadescriptionfr) : \"\";\n    });\n    const [contentfr, setContentfr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedContentfr = localStorage.getItem(\"contentfr\");\n        return savedContentfr ? JSON.parse(savedContentfr) : \"\";\n    });\n    const handleEditorChange = (newContentfr)=>{\n        setContentfr(newContentfr);\n        setFieldValue(\"contentFR\", newContentfr);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (titlefr) {\n            localStorage.setItem(\"titlefr\", JSON.stringify(titlefr));\n        }\n        if (contentfr) {\n            localStorage.setItem(\"contentfr\", JSON.stringify(contentfr));\n        }\n        if (metatitlefr) {\n            localStorage.setItem(\"metatitlefr\", JSON.stringify(metatitlefr));\n        }\n        if (metaDescriptionfr) {\n            localStorage.setItem(\"metaDescriptionfr\", JSON.stringify(metaDescriptionfr));\n        }\n    }, [\n        titlefr,\n        contentfr,\n        metatitlefr,\n        metaDescriptionfr\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"label-pentabell\",\n                children: \"Add article French : \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:title\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"titleFR\",\n                                        type: \"text\",\n                                        value: titlefr || values.titleFR,\n                                        onChange: (e)=>{\n                                            const titleFR = e.target.value;\n                                            setFieldValue(\"titleFR\", titleFR);\n                                            setTitlefr(titleFR);\n                                            const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_6__.slug)(titleFR);\n                                            setFieldValue(\"urlFR\", url);\n                                        },\n                                        className: \"input-pentabell\" + (errors.titleFR && touched.titleFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"titleFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:categories\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                multiple: true,\n                                                className: \"input-pentabell\",\n                                                id: \"tags-standard\",\n                                                options: filteredCategories.length > 0 ? filteredCategories : categories,\n                                                getOptionLabel: (option)=>option.name,\n                                                selected: values.categoryFR.length > 0 ? (filteredCategories.length > 0 ? filteredCategories : categories).filter((category)=>values.categoryFR.includes(category.id)) : [],\n                                                onChange: (event, selectedOptions)=>{\n                                                    const categoryIds = selectedOptions.map((category)=>category.id);\n                                                    setFieldValue(\"categoryFR\", categoryIds);\n                                                    onCategoriesSelect(categoryIds);\n                                                },\n                                                renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        ...params,\n                                                        className: \"input-pentabell  multiple-select\",\n                                                        variant: \"standard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            touched.categoryFR && errors.categoryFR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"error\",\n                                children: errors.categoryFR\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Description\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"descriptionFR\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: values.descriptionFR,\n                                    onChange: (e)=>{\n                                        const descriptionFR = e.target.value;\n                                        setFieldValue(\"descriptionFR\", descriptionFR);\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.descriptionFR && touched.descriptionFR ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"descriptionFR\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Highlights\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"tags\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_20__.WithContext, {\n                                        tags: highlights,\n                                        className: \"input-pentabell\" + (errors.highlightsFR && touched.highlightsFR ? \" is-invalid\" : \"\"),\n                                        delimiters: delimiters,\n                                        handleDelete: (i)=>{\n                                            const updatedTags = highlights.filter((tag, index)=>index !== i);\n                                            setHighlights(updatedTags);\n                                            setFieldValue(\"highlightsFR\", updatedTags.map((tag)=>tag.text));\n                                        },\n                                        handleAddition: (tag)=>{\n                                            setHighlights([\n                                                ...highlights,\n                                                tag\n                                            ]);\n                                            setFieldValue(\"highlightsFR\", [\n                                                ...highlights,\n                                                tag\n                                            ].map((item)=>item.text));\n                                        },\n                                        inputFieldPosition: \"bottom\",\n                                        autocomplete: true,\n                                        allowDragDrop: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"keywordsEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((suneditor_react__WEBPACK_IMPORTED_MODULE_3___default()), {\n                setContents: contentfr || values?.contentFR || \"\",\n                onChange: handleEditorChange,\n                onPaste: handlePaste,\n                setOptions: {\n                    cleanHTML: false,\n                    disableHtmlSanitizer: true,\n                    addTagsWhitelist: \"h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button\",\n                    plugins: suneditor_src_plugins__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                    buttonList: [\n                        [\n                            \"undo\",\n                            \"redo\"\n                        ],\n                        [\n                            \"font\",\n                            \"fontSize\",\n                            \"formatBlock\"\n                        ],\n                        [\n                            \"bold\",\n                            \"underline\",\n                            \"italic\",\n                            \"strike\",\n                            \"subscript\",\n                            \"superscript\"\n                        ],\n                        [\n                            \"fontColor\",\n                            \"hiliteColor\"\n                        ],\n                        [\n                            \"align\",\n                            \"list\",\n                            \"lineHeight\"\n                        ],\n                        [\n                            \"outdent\",\n                            \"indent\"\n                        ],\n                        [\n                            \"table\",\n                            \"horizontalRule\",\n                            \"link\",\n                            \"image\",\n                            \"video\"\n                        ],\n                        [\n                            \"fullScreen\",\n                            \"showBlocks\",\n                            \"codeView\"\n                        ],\n                        [\n                            \"preview\",\n                            \"print\"\n                        ],\n                        [\n                            \"removeFormat\"\n                        ]\n                    ],\n                    imageUploadHandler: handlePhotoBlogChange,\n                    defaultTag: \"div\",\n                    minHeight: \"300px\",\n                    maxHeight: \"400px\",\n                    showPathLabel: false,\n                    font: [\n                        \"Proxima-Nova-Regular\",\n                        \"Proxima-Nova-Medium\",\n                        \"Proxima-Nova-Semibold\",\n                        \"Proxima-Nova-Bold\",\n                        \"Proxima-Nova-Extrabold\",\n                        \"Proxima-Nova-Black\",\n                        \"Proxima-Nova-Light\",\n                        \"Proxima-Nova-Thin\",\n                        \"Arial\",\n                        \"Times New Roman\",\n                        \"Sans-Serif\"\n                    ],\n                    charCounter: true,\n                    charCounterType: \"byte\",\n                    resizingBar: false,\n                    colorList: [\n                        // Standard Colors\n                        [\n                            \"#234791\",\n                            \"#d69b19\",\n                            \"#cc3233\",\n                            \"#009966\",\n                            \"#0b3051\",\n                            \"#2BBFAD\",\n                            \"#0b305100\",\n                            \"#0a305214\",\n                            \"#743794\",\n                            \"#ff0000\",\n                            \"#ff5e00\",\n                            \"#ffe400\",\n                            \"#abf200\",\n                            \"#00d8ff\",\n                            \"#0055ff\",\n                            \"#6600ff\",\n                            \"#ff00dd\",\n                            \"#000000\",\n                            \"#ffd8d8\",\n                            \"#fae0d4\",\n                            \"#faf4c0\",\n                            \"#e4f7ba\",\n                            \"#d4f4fa\",\n                            \"#d9e5ff\",\n                            \"#e8d9ff\",\n                            \"#ffd9fa\",\n                            \"#f1f1f1\",\n                            \"#ffa7a7\",\n                            \"#ffc19e\",\n                            \"#faed7d\",\n                            \"#cef279\",\n                            \"#b2ebf4\",\n                            \"#b2ccff\",\n                            \"#d1b2ff\",\n                            \"#ffb2f5\",\n                            \"#bdbdbd\",\n                            \"#f15f5f\",\n                            \"#f29661\",\n                            \"#e5d85c\",\n                            \"#bce55c\",\n                            \"#5cd1e5\",\n                            \"#6699ff\",\n                            \"#a366ff\",\n                            \"#f261df\",\n                            \"#8c8c8c\",\n                            \"#980000\",\n                            \"#993800\",\n                            \"#998a00\",\n                            \"#6b9900\",\n                            \"#008299\",\n                            \"#003399\",\n                            \"#3d0099\",\n                            \"#990085\",\n                            \"#353535\",\n                            \"#670000\",\n                            \"#662500\",\n                            \"#665c00\",\n                            \"#476600\",\n                            \"#005766\",\n                            \"#002266\",\n                            \"#290066\",\n                            \"#660058\",\n                            \"#222222\"\n                        ]\n                    ]\n                },\n                onImageUpload: handlePhotoBlogChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 482,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FaqSection, {\n                values: values,\n                setFieldValue: setFieldValue,\n                errors: errors,\n                touched: touched,\n                language: \"FR\",\n                debounce: debounce\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 484,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 492,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:metaTitle\"),\n                                    \" (\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: values.metaTitleFR?.length > 65 ? \" text-danger\" : \"\",\n                                        children: [\n                                            \" \",\n                                            values.metaTitleFR?.length,\n                                            \" / 65\",\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    \")\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"metaTitleFR\",\n                                        type: \"text\",\n                                        value: metatitlefr || values.metaTitleFR,\n                                        onChange: (e)=>{\n                                            const metaTitleFR = e.target.value;\n                                            setFieldValue(\"metaTitleFR\", metaTitleFR);\n                                            setMetatitlefr(metaTitleFR);\n                                        },\n                                        className: \"input-pentabell\" + (errors.metaTitleFR && touched.metaTitleFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"metaTitleFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 494,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:url\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"urlFR\",\n                                        type: \"text\",\n                                        value: values.urlFR,\n                                        onChange: (e)=>{\n                                            setFieldValue(\"urlFR\", e.target.value);\n                                        },\n                                        className: \"input-pentabell\" + (errors.urlFR && touched.urlFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"urlFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 534,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 533,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 532,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 493,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:metaDescription\"),\n                                \" (\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: values.metaDescriptionFR?.length > 160 ? \" text-danger\" : \"\",\n                                    children: [\n                                        values.metaDescriptionFR?.length,\n                                        \" / 160\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                \")\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"standard\",\n                                    name: \"metaDescriptionFR\",\n                                    type: \"text\",\n                                    multiline: true,\n                                    rows: 2,\n                                    value: metaDescriptionfr || values.metaDescriptionFR,\n                                    onChange: (e)=>{\n                                        const metaDescriptionFR = e.target.value;\n                                        setFieldValue(\"metaDescriptionFR\", metaDescriptionFR);\n                                        setMetaDescriptionfr(metaDescriptionFR);\n                                    },\n                                    className: \"textArea-pentabell\" + (errors.metaDescriptionFR && touched.metaDescriptionFR ? \" is-invalid\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"metaDescriptionFR\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 561,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 560,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 559,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 558,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:featuredImage\"),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"upload-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: `image-upload-fr`,\n                                        className: \"file-labels\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                id: `image-upload-fr`,\n                                                name: \"imageEN\",\n                                                accept: \".png, .jpg, .jpeg, .webp\",\n                                                ref: imageInputRef,\n                                                onChange: (e)=>{\n                                                    setFieldValue(\"imageFR\", e.target.files[0]);\n                                                    handlePhotoChange();\n                                                },\n                                                className: \"file-input\" + (errors.imageFR && touched.imageFR ? \" is-invalid\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"upload-area\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"icon-pic\",\n                                                            style: {\n                                                                backgroundImage: `url(\"${selectedImage ? URL.createObjectURL(selectedImage) : values.imageFR ? `${process.env.REACT_APP_API_URL}${_utils_urls__WEBPACK_IMPORTED_MODULE_7__.API_URLS.files}/${values.imageFR}` : _assets_images_add_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"].src}\")`,\n                                                                backgroundSize: \"cover\",\n                                                                backgroundRepeat: \"no-repeat\",\n                                                                backgroundPosition: \"center\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-text\",\n                                                                children: t(\"createArticle:addFeatImg\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-description\",\n                                                                children: t(\"createArticle:clickBox\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                name: \"imageFR\",\n                                                component: \"div\",\n                                                className: \"invalid-feedback error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 602,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 601,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 600,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 599,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:alt\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"standard\",\n                                        name: \"altFR\",\n                                        type: \"text\",\n                                        value: values.altFR,\n                                        onChange: (e)=>{\n                                            setFieldValue(\"altFR\", e.target.value);\n                                        },\n                                        className: \"input-pentabell\" + (errors.altFR && touched.altFR ? \" is-invalid\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"altFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 665,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 664,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 663,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:visibility\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"select-pentabell\",\n                                        variant: \"standard\",\n                                        value: _utils_constants__WEBPACK_IMPORTED_MODULE_4__.Visibility.filter((option)=>values.visibilityFR === option),\n                                        selected: values.visibilityFR,\n                                        onChange: (event)=>{\n                                            setFieldValue(\"visibilityFR\", event.target.value);\n                                        },\n                                        children: _utils_constants__WEBPACK_IMPORTED_MODULE_4__.Visibility.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                value: item,\n                                                children: item\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"visibilityEN\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 710,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 690,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 689,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 688,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"label-form\",\n                                children: [\n                                    t(\"createArticle:keyword\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        id: \"tags\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_20__.WithContext, {\n                                            tags: tags,\n                                            className: \"input-pentabell\" + (errors.keywordsFR && touched.keywordsFR ? \" is-invalid\" : \"\"),\n                                            delimiters: delimiters,\n                                            handleDelete: (i)=>{\n                                                const updatedTags = tags.filter((tag, index)=>index !== i);\n                                                setTags(updatedTags);\n                                                setFieldValue(\"keywordsFR\", updatedTags.map((tag)=>tag.text));\n                                            },\n                                            handleAddition: (tag)=>{\n                                                setTags([\n                                                    ...tags,\n                                                    tag\n                                                ]);\n                                                setFieldValue(\"keywordsFR\", [\n                                                    ...tags,\n                                                    tag\n                                                ].map((item)=>item.text));\n                                            },\n                                            inputFieldPosition: \"bottom\",\n                                            autocomplete: true,\n                                            allowDragDrop: false\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                        className: \"label-error\",\n                                        name: \"keywordsFR\",\n                                        component: \"div\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 720,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                            lineNumber: 719,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 718,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 662,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"label-form\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Field, {\n                        type: \"checkbox\",\n                        name: \"publishNow\",\n                        checked: publishNow,\n                        onChange: (e)=>{\n                            setPublishNow(e.target.checked);\n                            if (e.target.checked) {\n                                setFieldValue(\"publishDateFR\", new Date().toISOString());\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 764,\n                        columnNumber: 9\n                    }, this),\n                    t(\"createArticle:publishNow\")\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 763,\n                columnNumber: 7\n            }, this),\n            !publishNow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_MenuItem_Select_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"label-form\",\n                        children: [\n                            t(\"createArticle:publishDate\"),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_24__.LocalizationProvider, {\n                                dateAdapter: _mui_x_date_pickers_AdapterDayjs__WEBPACK_IMPORTED_MODULE_25__.AdapterDayjs,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_internals_demo__WEBPACK_IMPORTED_MODULE_26__.DemoContainer, {\n                                    components: [\n                                        \"DatePicker\"\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers__WEBPACK_IMPORTED_MODULE_27__.DatePicker, {\n                                            variant: \"standard\",\n                                            className: \"input-date\",\n                                            format: \"DD/MM/YYYY\",\n                                            value: dayjs__WEBPACK_IMPORTED_MODULE_10___default()(values.publishDateEN),\n                                            onChange: (date)=>{\n                                                setFieldValue(\"publishDateEN\", dayjs__WEBPACK_IMPORTED_MODULE_10___default()(date).format(\"YYYY-MM-DD\"));\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 785,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"publishDateEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                            lineNumber: 797,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                    lineNumber: 784,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                                lineNumber: 783,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                        lineNumber: 781,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                    lineNumber: 780,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 779,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Field, {\n                type: \"hidden\",\n                name: \"publishDateFR\",\n                value: publishNow ? new Date().toISOString() : publishDate.toISOString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFR.jsx\",\n                lineNumber: 809,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AddArticleFR, \"+ONfVgda+KvaQf3X16NXIW62f9w=\", false, function() {\n    return [\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_11__.useSaveFile\n    ];\n});\n_c = AddArticleFR;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticleFR);\nvar _c;\n$RefreshReg$(_c, \"AddArticleFR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9ibG9nL2NvbXBvbmVudHMvQWRkQXJ0aWNsZUZSLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM2QztBQUNPO0FBQ0w7QUFDUDtBQUNKO0FBRWtCO0FBQ0Y7QUFDTjtBQUNDO0FBQ0Y7QUFDQztBQUNhO0FBQ2Y7QUFXckI7QUFDZ0Q7QUFDSjtBQUN6QztBQUNzQztBQUNhO0FBQ1I7QUFDckUsU0FBUzhCLGFBQWEsS0FVckI7UUFWcUIsRUFDcEJDLE1BQU0sRUFDTkMsT0FBTyxFQUNQQyxhQUFhLEVBQ2JDLE1BQU0sRUFDTkMsYUFBYSxFQUNiQyxVQUFVLEVBQ1ZDLGtCQUFrQixFQUNsQkMsa0JBQWtCLEVBQ2xCQyxRQUFRLEVBQ1QsR0FWcUI7O0lBV3BCLE1BQU1DLGNBQWMsQ0FBQ0MsT0FBT0MsV0FBV0M7UUFDckMsSUFBSUMsT0FBT0Y7UUFFWCxxQ0FBcUM7UUFDckNFLE9BQU9BLEtBQUtDLE9BQU8sQ0FBQyxtQkFBbUI7UUFFdkMsT0FBT0Q7SUFDVDtJQUNBLE1BQU1FLFdBQVc7UUFDZkMsT0FBTztRQUNQQyxPQUFPO0lBQ1Q7SUFDQSxNQUFNQyxhQUFhO1FBQUNILFNBQVNDLEtBQUs7UUFBRUQsU0FBU0UsS0FBSztLQUFDO0lBQ25ELE1BQU0sQ0FBQ0UsTUFBTUMsUUFBUSxHQUFHL0MsK0NBQVFBLENBQUMsRUFBRTtJQUNuQyxNQUFNLENBQUNnRCxZQUFZQyxjQUFjLEdBQUdqRCwrQ0FBUUEsQ0FBQyxFQUFFO0lBRS9DLE1BQU0sRUFBRWtELElBQUksRUFBRSxHQUFHekIsbUZBQWNBO0lBQy9CLE1BQU0sRUFBRTBCLENBQUMsRUFBRSxHQUFHbEQsNkRBQWNBO0lBQzVCLE1BQU0sQ0FBQ21ELFlBQVlDLGNBQWMsR0FBR3JELCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3NELGFBQWFDLGVBQWUsR0FBR3ZELCtDQUFRQSxDQUFDLElBQUl3RDtJQUNuRCxNQUFNQyxnQkFBZ0IxRCw2Q0FBTUEsQ0FBQztJQUM3QixNQUFNLENBQUMyRCxlQUFlQyxpQkFBaUIsR0FBRzNELCtDQUFRQSxDQUFDO0lBQ25ELE1BQU00RCxXQUFXO0lBRWpCLE1BQU1DLG9CQUFvQjtRQUN4QixNQUFNQyxlQUFlTCxjQUFjTSxPQUFPLENBQUNDLEtBQUssQ0FBQyxFQUFFO1FBQ25ETCxpQkFBaUJGLGNBQWNNLE9BQU8sQ0FBQ0MsS0FBSyxDQUFDLEVBQUU7UUFFL0MsSUFBSUYsY0FBYztZQUNoQi9CLGNBQWMrQixjQUFjRjtRQUM5QjtJQUNGO0lBRUEsTUFBTUssa0JBQWtCekMsMkZBQVdBO0lBRW5DLE1BQU0wQyx3QkFBd0IsT0FBT0MsTUFBTUMsTUFBTUMsTUFBTUM7UUFDckQsSUFBSUgsZ0JBQWdCSSxrQkFBa0I7WUFDcEMsTUFBTUMsTUFBTUwsS0FBS0ssR0FBRztZQUVwQixJQUFJQSxJQUFJQyxVQUFVLENBQUMsZUFBZTtnQkFDaEMsTUFBTUMsYUFBYUYsSUFBSUcsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO2dCQUNwQyxNQUFNQyxjQUFjSixJQUFJSyxLQUFLLENBQUMsb0JBQW9CLENBQUMsRUFBRTtnQkFDckQsTUFBTUMsaUJBQWlCQyxLQUFLTDtnQkFDNUIsTUFBTU0sY0FBYyxJQUFJQyxNQUFNSCxlQUFlSSxNQUFNLEVBQ2hEQyxJQUFJLENBQUMsR0FDTEMsR0FBRyxDQUFDLENBQUNDLEdBQUdDLElBQU1SLGVBQWVTLFVBQVUsQ0FBQ0Q7Z0JBQzNDLE1BQU1FLFlBQVksSUFBSUMsV0FBV1Q7Z0JBRWpDLE1BQU1VLE9BQU8sSUFBSUMsS0FBSztvQkFBQ0g7aUJBQVUsRUFBRTtvQkFBRUksTUFBTWhCO2dCQUFZO2dCQUN2RCxNQUFNaUIsV0FBVyxDQUFDLE1BQU0sRUFBRXJDLEtBQUtzQyxHQUFHLEdBQUcsQ0FBQyxFQUFFbEIsWUFBWUQsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQztnQkFDbkUsTUFBTWIsZUFBZSxJQUFJaUMsS0FBSztvQkFBQ0w7aUJBQUssRUFBRUcsVUFBVTtvQkFBRUQsTUFBTWhCO2dCQUFZO2dCQUVwRSxNQUFNb0IsV0FBV2xDLGNBQWNRLGVBQWVELE1BQU1GO1lBQ3RELE9BQU87Z0JBQ0w4QixNQUFNekIsS0FDSDBCLElBQUksQ0FBQyxDQUFDQyxXQUFhQSxTQUFTVCxJQUFJLElBQ2hDUSxJQUFJLENBQUMsQ0FBQ1I7b0JBQ0wsTUFBTWQsY0FBY2MsS0FBS0UsSUFBSTtvQkFDN0IsTUFBTUMsV0FBVyxDQUFDLE1BQU0sRUFBRXJDLEtBQUtzQyxHQUFHLEdBQUcsQ0FBQyxFQUFFbEIsWUFBWUQsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQztvQkFDbkUsTUFBTWIsZUFBZSxJQUFJaUMsS0FBSzt3QkFBQ0w7cUJBQUssRUFBRUcsVUFBVTt3QkFDOUNELE1BQU1oQjtvQkFDUjtvQkFFQW9CLFdBQVdsQyxjQUFjUSxlQUFlRCxNQUFNRjtnQkFDaEQsR0FDQ2lDLEtBQUssQ0FBQyxDQUFDQyxRQUNOQyxRQUFRRCxLQUFLLENBQUMsdUNBQXVDQTtZQUUzRDtRQUNGLE9BQU87WUFDTEMsUUFBUUQsS0FBSyxDQUFDO1FBQ2hCO0lBQ0Y7SUFFQSxNQUFNTCxhQUFhLENBQUNsQyxjQUFjUSxlQUFlRCxNQUFNa0M7UUFDckQsSUFBSUM7UUFDSkEsWUFBWXBHLGlEQUFNQSxHQUFHcUMsT0FBTyxDQUFDLE1BQU07UUFFbkMsTUFBTWdFLFdBQVcsSUFBSUM7UUFDckJELFNBQVNFLE1BQU0sQ0FBQyxRQUFRN0M7UUFFeEIsTUFBTThDLFlBQVk5QyxhQUFhK0MsSUFBSSxDQUFDbEMsS0FBSyxDQUFDLEtBQUttQyxHQUFHO1FBQ2xELE1BQU1DLGNBQWMsSUFBSXZELE9BQU93RCxXQUFXO1FBRTFDL0MsZ0JBQWdCZ0QsTUFBTSxDQUNwQjtZQUNFQyxVQUFVO1lBQ1ZDLFFBQVFKLFlBQVlLLFFBQVE7WUFDNUJDLFVBQVViO1lBQ1ZjLE1BQU07Z0JBQUViO2dCQUFVdEQ7WUFBRTtRQUN0QixHQUNBO1lBQ0VvRSxXQUFXLENBQUNDO2dCQUNWLE1BQU1DLG9CQUNKRCxTQUFTRSxPQUFPLEtBQUssZUFDakJGLFNBQVNHLElBQUksR0FDYixDQUFDLEVBQUVuQixVQUFVLENBQUMsRUFBRUksVUFBVSxDQUFDO2dCQUVqQyxNQUFNZ0IsV0FBVyxDQUFDLEVBQUVDLDhCQUFvQyxDQUFDLE9BQU8sRUFBRUosa0JBQWtCLENBQUM7Z0JBRXJGbEIsY0FBYy9CLEdBQUcsR0FBR29EO2dCQUVwQnRELGNBQWM7b0JBQ1owRCxRQUFRO3dCQUNOOzRCQUNFQyxJQUFJUjs0QkFDSlMsS0FBS047d0JBQ1A7cUJBQ0Q7Z0JBQ0g7WUFDRjtZQUNBTyxTQUFTLENBQUM5QjtnQkFDUkMsUUFBUUQsS0FBSyxDQUFDLHlCQUF5QkE7WUFDekM7UUFDRjtJQUVKO0lBQ0EsTUFBTSxDQUFDK0IsU0FBU0MsV0FBVyxHQUFHckksK0NBQVFBLENBQUM7UUFDckMsTUFBTXNJLGVBQWVDLGFBQWFDLE9BQU8sQ0FBQztRQUMxQyxPQUFPRixlQUFlRyxLQUFLQyxLQUFLLENBQUNKLGdCQUFnQjtJQUNuRDtJQUNBLE1BQU0sQ0FBQ0ssYUFBYUMsZUFBZSxHQUFHNUksK0NBQVFBLENBQUM7UUFDN0MsTUFBTTZJLG1CQUFtQk4sYUFBYUMsT0FBTyxDQUFDO1FBQzlDLE9BQU9LLG1CQUFtQkosS0FBS0MsS0FBSyxDQUFDRyxvQkFBb0I7SUFDM0Q7SUFDQSxNQUFNLENBQUNDLG1CQUFtQkMscUJBQXFCLEdBQUcvSSwrQ0FBUUEsQ0FBQztRQUN6RCxNQUFNZ0oseUJBQXlCVCxhQUFhQyxPQUFPLENBQUM7UUFDcEQsT0FBT1EseUJBQXlCUCxLQUFLQyxLQUFLLENBQUNNLDBCQUEwQjtJQUN2RTtJQUVBLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHbEosK0NBQVFBLENBQUM7UUFDekMsTUFBTW1KLGlCQUFpQlosYUFBYUMsT0FBTyxDQUFDO1FBQzVDLE9BQU9XLGlCQUFpQlYsS0FBS0MsS0FBSyxDQUFDUyxrQkFBa0I7SUFDdkQ7SUFFQSxNQUFNQyxxQkFBcUIsQ0FBQ0M7UUFDMUJILGFBQWFHO1FBQ2J4SCxjQUFjLGFBQWF3SDtJQUM3QjtJQUNBdkosZ0RBQVNBLENBQUM7UUFDUixJQUFJc0ksU0FBUztZQUNYRyxhQUFhZSxPQUFPLENBQUMsV0FBV2IsS0FBS2MsU0FBUyxDQUFDbkI7UUFDakQ7UUFDQSxJQUFJYSxXQUFXO1lBQ2JWLGFBQWFlLE9BQU8sQ0FBQyxhQUFhYixLQUFLYyxTQUFTLENBQUNOO1FBQ25EO1FBQ0EsSUFBSU4sYUFBYTtZQUNmSixhQUFhZSxPQUFPLENBQUMsZUFBZWIsS0FBS2MsU0FBUyxDQUFDWjtRQUNyRDtRQUNBLElBQUlHLG1CQUFtQjtZQUNyQlAsYUFBYWUsT0FBTyxDQUNsQixxQkFDQWIsS0FBS2MsU0FBUyxDQUFDVDtRQUVuQjtJQUNGLEdBQUc7UUFBQ1Y7UUFBU2E7UUFBV047UUFBYUc7S0FBa0I7SUFFdkQscUJBQ0U7OzBCQUNFLDhEQUFDVTtnQkFBRUMsV0FBVTswQkFBa0I7Ozs7OzswQkFDL0IsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0M7a0NBQ0MsNEVBQUM3SSw2SUFBU0E7c0NBQ1IsNEVBQUNDLDZJQUFTQTtnQ0FBQzJJLFdBQVU7O29DQUNsQnRHLEVBQUU7a0RBQ0gsOERBQUNqQyw2SUFBU0E7d0NBQ1J5SSxTQUFRO3dDQUNSOUMsTUFBSzt3Q0FDTGpCLE1BQUs7d0NBQ0xnRSxPQUFPeEIsV0FBV3RHLE9BQU8rSCxPQUFPO3dDQUNoQ0MsVUFBVSxDQUFDQzs0Q0FDVCxNQUFNRixVQUFVRSxFQUFFQyxNQUFNLENBQUNKLEtBQUs7NENBQzlCL0gsY0FBYyxXQUFXZ0k7NENBQ3pCeEIsV0FBV3dCOzRDQUNYLE1BQU0zQixNQUFNNUgsNERBQUlBLENBQUN1Sjs0Q0FDakJoSSxjQUFjLFNBQVNxRzt3Q0FDekI7d0NBQ0F1QixXQUNFLG9CQUNDOUgsQ0FBQUEsT0FBT2tJLE9BQU8sSUFBSWpJLFFBQVFpSSxPQUFPLEdBQUcsZ0JBQWdCLEVBQUM7Ozs7OztvQ0FFdkQ7a0RBQ0gsOERBQUNqSyxpREFBWUE7d0NBQ1g2SixXQUFVO3dDQUNWNUMsTUFBSzt3Q0FDTG9ELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS2xCLDhEQUFDUDs7MENBQ0MsOERBQUM3SSw2SUFBU0E7MENBQ1IsNEVBQUNDLDZJQUFTQTtvQ0FBQzJJLFdBQVU7O3dDQUNsQnRHLEVBQUU7c0RBQ0gsOERBQUNsQyw2SUFBS0E7c0RBQ0osNEVBQUNMLDZJQUFZQTtnREFDWHNKLFFBQVE7Z0RBQ1JULFdBQVU7Z0RBQ1Z4QixJQUFHO2dEQUNIa0MsU0FDRWxJLG1CQUFtQmlELE1BQU0sR0FBRyxJQUN4QmpELHFCQUNBRDtnREFFTm9JLGdCQUFnQixDQUFDQyxTQUFXQSxPQUFPeEQsSUFBSTtnREFDdkN5RCxVQUNFeEksT0FBT3lJLFVBQVUsQ0FBQ3JGLE1BQU0sR0FBRyxJQUN2QixDQUFDakQsbUJBQW1CaUQsTUFBTSxHQUFHLElBQ3pCakQscUJBQ0FELFVBQVMsRUFDWHdJLE1BQU0sQ0FBQyxDQUFDQyxXQUNSM0ksT0FBT3lJLFVBQVUsQ0FBQ0csUUFBUSxDQUFDRCxTQUFTeEMsRUFBRSxLQUV4QyxFQUFFO2dEQUVSNkIsVUFBVSxDQUFDekgsT0FBT3NJO29EQUNoQixNQUFNQyxjQUFjRCxnQkFBZ0J2RixHQUFHLENBQ3JDLENBQUNxRixXQUFhQSxTQUFTeEMsRUFBRTtvREFFM0JwRyxjQUFjLGNBQWMrSTtvREFDNUIxSSxtQkFBbUIwSTtnREFDckI7Z0RBQ0FDLGFBQWEsQ0FBQ0MsdUJBQ1osOERBQUM1Siw2SUFBU0E7d0RBQ1AsR0FBRzRKLE1BQU07d0RBQ1ZyQixXQUFVO3dEQUNWRSxTQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBUW5CL0gsUUFBUTJJLFVBQVUsSUFBSTVJLE9BQU80SSxVQUFVLGtCQUN0Qyw4REFBQ2I7Z0NBQUlELFdBQVU7MENBQVM5SCxPQUFPNEksVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUkvQyw4REFBQ2I7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNDOzhCQUNDLDRFQUFDN0ksNklBQVNBO2tDQUNSLDRFQUFDQyw2SUFBU0E7NEJBQUMySSxXQUFVOztnQ0FBYTs4Q0FFaEMsOERBQUN2SSw2SUFBU0E7b0NBQ1J5SSxTQUFRO29DQUNSOUMsTUFBSztvQ0FDTGpCLE1BQUs7b0NBQ0xtRixTQUFTO29DQUNUQyxNQUFNO29DQUNOcEIsT0FBTzlILE9BQU9tSixhQUFhO29DQUMzQm5CLFVBQVUsQ0FBQ0M7d0NBQ1QsTUFBTWtCLGdCQUFnQmxCLEVBQUVDLE1BQU0sQ0FBQ0osS0FBSzt3Q0FDcEMvSCxjQUFjLGlCQUFpQm9KO29DQUNqQztvQ0FDQXhCLFdBQ0UsdUJBQ0M5SCxDQUFBQSxPQUFPc0osYUFBYSxJQUFJckosUUFBUXFKLGFBQWEsR0FDMUMsZ0JBQ0EsRUFBQzs7Ozs7O2dDQUVOOzhDQUNILDhEQUFDckwsaURBQVlBO29DQUNYNkosV0FBVTtvQ0FDVjVDLE1BQUs7b0NBQ0xvRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXBCLDhEQUFDUDtnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0M7OEJBQ0MsNEVBQUM3SSw2SUFBU0E7a0NBQ1IsNEVBQUNDLDZJQUFTQTs0QkFBQzJJLFdBQVU7O2dDQUFhOzhDQUVoQyw4REFBQ0M7b0NBQUl6QixJQUFHOzhDQUNOLDRFQUFDdkgseURBQVNBO3dDQUNSb0MsTUFBTUU7d0NBQ055RyxXQUNFLG9CQUNDOUgsQ0FBQUEsT0FBT3VKLFlBQVksSUFBSXRKLFFBQVFzSixZQUFZLEdBQ3hDLGdCQUNBLEVBQUM7d0NBRVBySSxZQUFZQTt3Q0FDWnNJLGNBQWMsQ0FBQzdGOzRDQUNiLE1BQU04RixjQUFjcEksV0FBV3dILE1BQU0sQ0FDbkMsQ0FBQ2EsS0FBS0MsUUFBVUEsVUFBVWhHOzRDQUU1QnJDLGNBQWNtSTs0Q0FDZHZKLGNBQ0UsZ0JBQ0F1SixZQUFZaEcsR0FBRyxDQUFDLENBQUNpRyxNQUFRQSxJQUFJRSxJQUFJO3dDQUVyQzt3Q0FDQUMsZ0JBQWdCLENBQUNIOzRDQUNmcEksY0FBYzttREFBSUQ7Z0RBQVlxSTs2Q0FBSTs0Q0FDbEN4SixjQUNFLGdCQUNBO21EQUFJbUI7Z0RBQVlxSTs2Q0FBSSxDQUFDakcsR0FBRyxDQUFDLENBQUNxRyxPQUFTQSxLQUFLRixJQUFJO3dDQUVoRDt3Q0FDQUcsb0JBQW1CO3dDQUNuQkMsWUFBWTt3Q0FDWkMsZUFBZTs7Ozs7Ozs7Ozs7OENBR25CLDhEQUFDaE0saURBQVlBO29DQUNYNkosV0FBVTtvQ0FDVjVDLE1BQUs7b0NBQ0xvRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXBCLDhEQUFDL0osd0RBQVNBO2dCQUNSMkwsYUFBYTVDLGFBQWFuSCxRQUFRZ0ssYUFBYTtnQkFDL0NoQyxVQUFVVjtnQkFDVjJDLFNBQVMzSjtnQkFDVDRKLFlBQVk7b0JBQ1ZDLFdBQVc7b0JBQ1hDLHNCQUFzQjtvQkFDdEJDLGtCQUNFO29CQUNGeEwsU0FBU0EsOERBQU9BO29CQUNoQnlMLFlBQVk7d0JBQ1Y7NEJBQUM7NEJBQVE7eUJBQU87d0JBQ2hCOzRCQUFDOzRCQUFROzRCQUFZO3lCQUFjO3dCQUNuQzs0QkFDRTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTs0QkFDQTt5QkFDRDt3QkFDRDs0QkFBQzs0QkFBYTt5QkFBYzt3QkFDNUI7NEJBQUM7NEJBQVM7NEJBQVE7eUJBQWE7d0JBQy9COzRCQUFDOzRCQUFXO3lCQUFTO3dCQUNyQjs0QkFBQzs0QkFBUzs0QkFBa0I7NEJBQVE7NEJBQVM7eUJBQVE7d0JBQ3JEOzRCQUFDOzRCQUFjOzRCQUFjO3lCQUFXO3dCQUN4Qzs0QkFBQzs0QkFBVzt5QkFBUTt3QkFDcEI7NEJBQUM7eUJBQWU7cUJBQ2pCO29CQUNEQyxvQkFBb0JuSTtvQkFDcEJvSSxZQUFZO29CQUNaQyxXQUFXO29CQUNYQyxXQUFXO29CQUNYQyxlQUFlO29CQUNmQyxNQUFNO3dCQUNKO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3FCQUNEO29CQUNEQyxhQUFhO29CQUNiQyxpQkFBaUI7b0JBQ2pCQyxhQUFhO29CQUNiQyxXQUFXO3dCQUNULGtCQUFrQjt3QkFDbEI7NEJBQ0U7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7NEJBQ0E7eUJBQ0Q7cUJBQ0Y7Z0JBQ0g7Z0JBQ0FDLGVBQWU3STs7Ozs7OzBCQUVqQiw4REFBQzhJOzs7OzswQkFFRCw4REFBQ0M7Z0JBQ0NuTCxRQUFRQTtnQkFDUkQsZUFBZUE7Z0JBQ2ZGLFFBQVFBO2dCQUNSQyxTQUFTQTtnQkFDVGdDLFVBQVM7Z0JBQ1R6QixVQUFVQTs7Ozs7OzBCQUVaLDhEQUFDNks7Ozs7OzBCQUNELDhEQUFDdEQ7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDQztrQ0FDQyw0RUFBQzdJLDZJQUFTQTtzQ0FDUiw0RUFBQ0MsNklBQVNBO2dDQUFDMkksV0FBVTs7b0NBQ2xCdEcsRUFBRTtvQ0FBMkI7b0NBQUc7a0RBQ2pDLDhEQUFDK0o7d0NBQ0N6RCxXQUNFM0gsT0FBT3FMLFdBQVcsRUFBRWpJLFNBQVMsS0FBSyxpQkFBaUI7OzRDQUdwRDs0Q0FDQXBELE9BQU9xTCxXQUFXLEVBQUVqSTs0Q0FBTzs0Q0FBTTs7Ozs7OztvQ0FDNUI7b0NBQUk7a0RBRVosOERBQUNoRSw2SUFBU0E7d0NBQ1J5SSxTQUFRO3dDQUNSOUMsTUFBSzt3Q0FDTGpCLE1BQUs7d0NBQ0xnRSxPQUFPakIsZUFBZTdHLE9BQU9xTCxXQUFXO3dDQUN4Q3JELFVBQVUsQ0FBQ0M7NENBQ1QsTUFBTW9ELGNBQWNwRCxFQUFFQyxNQUFNLENBQUNKLEtBQUs7NENBQ2xDL0gsY0FBYyxlQUFlc0w7NENBQzdCdkUsZUFBZXVFO3dDQUNqQjt3Q0FDQTFELFdBQ0Usb0JBQ0M5SCxDQUFBQSxPQUFPd0wsV0FBVyxJQUFJdkwsUUFBUXVMLFdBQVcsR0FDdEMsZ0JBQ0EsRUFBQzs7Ozs7O29DQUVOO2tEQUNILDhEQUFDdk4saURBQVlBO3dDQUNYNkosV0FBVTt3Q0FDVjVDLE1BQUs7d0NBQ0xvRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtsQiw4REFBQ1A7a0NBQ0MsNEVBQUM3SSw2SUFBU0E7c0NBQ1IsNEVBQUNDLDZJQUFTQTtnQ0FBQzJJLFdBQVU7O29DQUNsQnRHLEVBQUU7a0RBQ0gsOERBQUNqQyw2SUFBU0E7d0NBQ1J5SSxTQUFRO3dDQUNSOUMsTUFBSzt3Q0FDTGpCLE1BQUs7d0NBQ0xnRSxPQUFPOUgsT0FBT3NMLEtBQUs7d0NBQ25CdEQsVUFBVSxDQUFDQzs0Q0FDVGxJLGNBQWMsU0FBU2tJLEVBQUVDLE1BQU0sQ0FBQ0osS0FBSzt3Q0FDdkM7d0NBQ0FILFdBQ0Usb0JBQ0M5SCxDQUFBQSxPQUFPeUwsS0FBSyxJQUFJeEwsUUFBUXdMLEtBQUssR0FBRyxnQkFBZ0IsRUFBQzs7Ozs7O29DQUVuRDtrREFDSCw4REFBQ3hOLGlEQUFZQTt3Q0FDWDZKLFdBQVU7d0NBQ1Y1QyxNQUFLO3dDQUNMb0QsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNcEIsOERBQUNQO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQzs4QkFDQyw0RUFBQzdJLDZJQUFTQTtrQ0FDUiw0RUFBQ0MsNklBQVNBOzRCQUFDMkksV0FBVTs7Z0NBQ2xCdEcsRUFBRTtnQ0FBaUM7Z0NBQUc7OENBQ3ZDLDhEQUFDK0o7b0NBQ0N6RCxXQUNFM0gsT0FBT3VMLGlCQUFpQixFQUFFbkksU0FBUyxNQUFNLGlCQUFpQjs7d0NBRzNEcEQsT0FBT3VMLGlCQUFpQixFQUFFbkk7d0NBQU87Ozs7Ozs7Z0NBQzVCO2dDQUFJOzhDQUVaLDhEQUFDaEUsNklBQVNBO29DQUNSeUksU0FBUTtvQ0FDUjlDLE1BQUs7b0NBQ0xqQixNQUFLO29DQUNMbUYsU0FBUztvQ0FDVEMsTUFBTTtvQ0FDTnBCLE9BQU9kLHFCQUFxQmhILE9BQU91TCxpQkFBaUI7b0NBQ3BEdkQsVUFBVSxDQUFDQzt3Q0FDVCxNQUFNc0Qsb0JBQW9CdEQsRUFBRUMsTUFBTSxDQUFDSixLQUFLO3dDQUN4Qy9ILGNBQWMscUJBQXFCd0w7d0NBQ25DdEUscUJBQXFCc0U7b0NBQ3ZCO29DQUNBNUQsV0FDRSx1QkFDQzlILENBQUFBLE9BQU8wTCxpQkFBaUIsSUFBSXpMLFFBQVF5TCxpQkFBaUIsR0FDbEQsZ0JBQ0EsRUFBQzs7Ozs7O2dDQUVOOzhDQUNILDhEQUFDek4saURBQVlBO29DQUNYNkosV0FBVTtvQ0FDVjVDLE1BQUs7b0NBQ0xvRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXBCLDhEQUFDUDtnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0M7OEJBQ0MsNEVBQUM3SSw2SUFBU0E7a0NBQ1IsNEVBQUNDLDZJQUFTQTs0QkFBQzJJLFdBQVU7O2dDQUNsQnRHLEVBQUU7OENBRUgsOERBQUN1RztvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQzZEO3dDQUFNQyxTQUFTLENBQUMsZUFBZSxDQUFDO3dDQUFFOUQsV0FBVTs7MERBQzNDLDhEQUFDK0Q7Z0RBQ0M1SCxNQUFLO2dEQUNMcUMsSUFBSSxDQUFDLGVBQWUsQ0FBQztnREFDckJwQixNQUFLO2dEQUNMNEcsUUFBTztnREFDUEMsS0FBS2pLO2dEQUNMcUcsVUFBVSxDQUFDQztvREFDVGxJLGNBQWMsV0FBV2tJLEVBQUVDLE1BQU0sQ0FBQ2hHLEtBQUssQ0FBQyxFQUFFO29EQUMxQ0g7Z0RBQ0Y7Z0RBQ0E0RixXQUNFLGVBQ0M5SCxDQUFBQSxPQUFPZ00sT0FBTyxJQUFJL0wsUUFBUStMLE9BQU8sR0FBRyxnQkFBZ0IsRUFBQzs7Ozs7OzBEQUcxRCw4REFBQ2pFO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ0M7a0VBQ0MsNEVBQUNBOzREQUNDRCxXQUFVOzREQUNWbUUsT0FBTztnRUFDTEMsaUJBQWlCLENBQUMsS0FBSyxFQUNyQm5LLGdCQUNJb0ssSUFBSUMsZUFBZSxDQUFDckssaUJBQ3BCNUIsT0FBTzZMLE9BQU8sR0FDZCxDQUFDLEVBQUU5RixPQUFPQSxDQUFDQyxHQUFHLENBQUNrRyxpQkFBaUIsQ0FBQyxFQUFFek4saURBQVFBLENBQUN5RCxLQUFLLENBQUMsQ0FBQyxFQUFFbEMsT0FBTzZMLE9BQU8sQ0FBQyxDQUFDLEdBQ3JFbk4sOERBQU1BLENBQUNnRSxHQUFHLENBQ2YsRUFBRSxDQUFDO2dFQUNKeUosZ0JBQWdCO2dFQUNoQkMsa0JBQWtCO2dFQUNsQkMsb0JBQW9COzREQUV0Qjs7Ozs7Ozs7Ozs7a0VBR0osOERBQUN6RTs7MEVBQ0MsOERBQUNGO2dFQUFFQyxXQUFVOzBFQUNWdEcsRUFBRTs7Ozs7OzBFQUVMLDhEQUFDcUc7Z0VBQUVDLFdBQVU7MEVBQ1Z0RyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSVQsOERBQUN2RCxpREFBWUE7Z0RBQ1hpSCxNQUFLO2dEQUNMb0QsV0FBVTtnREFDVlIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU3hCLDhEQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUNDO2tDQUNDLDRFQUFDN0ksNklBQVNBO3NDQUNSLDRFQUFDQyw2SUFBU0E7Z0NBQUMySSxXQUFVOztvQ0FDbEJ0RyxFQUFFO2tEQUNILDhEQUFDakMsNklBQVNBO3dDQUNSeUksU0FBUTt3Q0FDUjlDLE1BQUs7d0NBQ0xqQixNQUFLO3dDQUNMZ0UsT0FBTzlILE9BQU9zTSxLQUFLO3dDQUNuQnRFLFVBQVUsQ0FBQ0M7NENBQ1RsSSxjQUFjLFNBQVNrSSxFQUFFQyxNQUFNLENBQUNKLEtBQUs7d0NBQ3ZDO3dDQUNBSCxXQUNFLG9CQUNDOUgsQ0FBQUEsT0FBT3lNLEtBQUssSUFBSXhNLFFBQVF3TSxLQUFLLEdBQUcsZ0JBQWdCLEVBQUM7Ozs7OztvQ0FFbkQ7a0RBQ0gsOERBQUN4TyxpREFBWUE7d0NBQ1g2SixXQUFVO3dDQUNWNUMsTUFBSzt3Q0FDTG9ELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS2xCLDhEQUFDUDtrQ0FDQyw0RUFBQzdJLDZJQUFTQTtzQ0FDUiw0RUFBQ0MsNklBQVNBO2dDQUFDMkksV0FBVTs7b0NBQ2xCdEcsRUFBRTtrREFFSCw4REFBQ25DLDZJQUFNQTt3Q0FDTHlJLFdBQVU7d0NBQ1ZFLFNBQVE7d0NBQ1JDLE9BQU92Six3REFBVUEsQ0FBQ21LLE1BQU0sQ0FDdEIsQ0FBQ0gsU0FBV3ZJLE9BQU91TSxZQUFZLEtBQUtoRTt3Q0FFdENDLFVBQVV4SSxPQUFPdU0sWUFBWTt3Q0FDN0J2RSxVQUFVLENBQUN6SDs0Q0FDVFIsY0FBYyxnQkFBZ0JRLE1BQU0ySCxNQUFNLENBQUNKLEtBQUs7d0NBQ2xEO2tEQUVDdkosd0RBQVVBLENBQUMrRSxHQUFHLENBQUMsQ0FBQ3FHLE1BQU1ILHNCQUNyQiw4REFBQ3ZLLDZJQUFRQTtnREFBYTZJLE9BQU82QjswREFDMUJBOytDQURZSDs7Ozs7Ozs7OztrREFLbkIsOERBQUMxTCxpREFBWUE7d0NBQ1g2SixXQUFVO3dDQUNWNUMsTUFBSzt3Q0FDTG9ELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS2xCLDhEQUFDUDtrQ0FDQyw0RUFBQzdJLDZJQUFTQTtzQ0FDUiw0RUFBQ0MsNklBQVNBO2dDQUFDMkksV0FBVTs7b0NBQ2xCdEcsRUFBRTtrREFDSCw4REFBQ3VHO3dDQUFJekIsSUFBRztrREFDTiw0RUFBQ3ZILHlEQUFTQTs0Q0FDUm9DLE1BQU1BOzRDQUNOMkcsV0FDRSxvQkFDQzlILENBQUFBLE9BQU8yTSxVQUFVLElBQUkxTSxRQUFRME0sVUFBVSxHQUNwQyxnQkFDQSxFQUFDOzRDQUVQekwsWUFBWUE7NENBQ1pzSSxjQUFjLENBQUM3RjtnREFDYixNQUFNOEYsY0FBY3RJLEtBQUswSCxNQUFNLENBQzdCLENBQUNhLEtBQUtDLFFBQVVBLFVBQVVoRztnREFFNUJ2QyxRQUFRcUk7Z0RBQ1J2SixjQUNFLGNBQ0F1SixZQUFZaEcsR0FBRyxDQUFDLENBQUNpRyxNQUFRQSxJQUFJRSxJQUFJOzRDQUVyQzs0Q0FDQUMsZ0JBQWdCLENBQUNIO2dEQUNmdEksUUFBUTt1REFBSUQ7b0RBQU11STtpREFBSTtnREFDdEJ4SixjQUNFLGNBQ0E7dURBQUlpQjtvREFBTXVJO2lEQUFJLENBQUNqRyxHQUFHLENBQUMsQ0FBQ3FHLE9BQVNBLEtBQUtGLElBQUk7NENBRTFDOzRDQUNBRyxvQkFBbUI7NENBQ25CQyxZQUFZOzRDQUNaQyxlQUFlOzs7Ozs7Ozs7OztrREFHbkIsOERBQUNoTSxpREFBWUE7d0NBQ1g2SixXQUFVO3dDQUNWNUMsTUFBSzt3Q0FDTG9ELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXBCLDhEQUFDcUQ7Z0JBQU03RCxXQUFVOztrQ0FDZiw4REFBQzVKLDBDQUFLQTt3QkFDSitGLE1BQUs7d0JBQ0xpQixNQUFLO3dCQUNMMEgsU0FBU25MO3dCQUNUMEcsVUFBVSxDQUFDQzs0QkFDVDFHLGNBQWMwRyxFQUFFQyxNQUFNLENBQUN1RSxPQUFPOzRCQUM5QixJQUFJeEUsRUFBRUMsTUFBTSxDQUFDdUUsT0FBTyxFQUFFO2dDQUNwQjFNLGNBQWMsaUJBQWlCLElBQUkyQixPQUFPZ0wsV0FBVzs0QkFDdkQ7d0JBQ0Y7Ozs7OztvQkFFRHJMLEVBQUU7Ozs7Ozs7WUFHSixDQUFDQyw0QkFDQSw4REFBQ3NHOzBCQUNDLDRFQUFDN0ksNklBQVNBOzhCQUNSLDRFQUFDQyw2SUFBU0E7d0JBQUMySSxXQUFVOzs0QkFDbEJ0RyxFQUFFOzBDQUNILDhEQUFDL0Isc0VBQW9CQTtnQ0FBQ3FOLGFBQWFsTiwyRUFBWUE7MENBQzdDLDRFQUFDRiw4RUFBYUE7b0NBQUNxTixZQUFZO3dDQUFDO3FDQUFhOztzREFDdkMsOERBQUN2Tiw0REFBVUE7NENBQ1R3SSxTQUFROzRDQUNSRixXQUFVOzRDQUNWa0YsUUFBTzs0Q0FDUC9FLE9BQU90SSw2Q0FBS0EsQ0FBQ1EsT0FBTzhNLGFBQWE7NENBQ2pDOUUsVUFBVSxDQUFDK0U7Z0RBQ1RoTixjQUNFLGlCQUNBUCw2Q0FBS0EsQ0FBQ3VOLE1BQU1GLE1BQU0sQ0FBQzs0Q0FFdkI7Ozs7Ozt3Q0FDQztzREFDSCw4REFBQy9PLGlEQUFZQTs0Q0FDWDZKLFdBQVU7NENBQ1Y1QyxNQUFLOzRDQUNMb0QsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVN4Qiw4REFBQ3BLLDBDQUFLQTtnQkFDSitGLE1BQUs7Z0JBQ0xpQixNQUFLO2dCQUNMK0MsT0FDRXhHLGFBQWEsSUFBSUksT0FBT2dMLFdBQVcsS0FBS2xMLFlBQVlrTCxXQUFXOzs7Ozs7OztBQUt6RTtHQWp4QlM5TTs7UUEyQlVELCtFQUFjQTtRQUNqQnhCLHlEQUFjQTtRQWdCSnVCLHVGQUFXQTs7O0tBNUM1QkU7QUFteEJULCtEQUFlQSxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9mZWF0dXJlcy9ibG9nL2NvbXBvbmVudHMvQWRkQXJ0aWNsZUZSLmpzeD8zOTcwIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgeyBFcnJvck1lc3NhZ2UsIEZpZWxkIH0gZnJvbSBcImZvcm1pa1wiO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tIFwicmVhY3QtaTE4bmV4dFwiO1xyXG5pbXBvcnQgU3VuRWRpdG9yIGZyb20gXCJzdW5lZGl0b3ItcmVhY3RcIjtcclxuaW1wb3J0IHsgdjQgYXMgdXVpZHY0IH0gZnJvbSBcInV1aWRcIjtcclxuXHJcbmltcG9ydCB7IFZpc2liaWxpdHkgfSBmcm9tIFwiLi4vLi4vLi4vdXRpbHMvY29uc3RhbnRzXCI7XHJcbmltcG9ydCBcInJlYWN0LWRhdGVwaWNrZXIvZGlzdC9yZWFjdC1kYXRlcGlja2VyLmNzc1wiO1xyXG5pbXBvcnQgeyBzbHVnIH0gZnJvbSBcIkBmZWVsaW5nbG92ZWx5bm93L3NsdWdcIjtcclxuaW1wb3J0IHsgQVBJX1VSTFMgfSBmcm9tIFwiLi4vLi4vLi4vdXRpbHMvdXJsc1wiO1xyXG5pbXBvcnQgdXBsb2FkIGZyb20gXCJAL2Fzc2V0cy9pbWFnZXMvYWRkLnBuZ1wiO1xyXG5pbXBvcnQgXCJzdW5lZGl0b3IvZGlzdC9jc3Mvc3VuZWRpdG9yLm1pbi5jc3NcIjtcclxuaW1wb3J0IHsgV2l0aENvbnRleHQgYXMgUmVhY3RUYWdzIH0gZnJvbSBcInJlYWN0LXRhZy1pbnB1dFwiO1xyXG5pbXBvcnQgcGx1Z2lucyBmcm9tIFwic3VuZWRpdG9yL3NyYy9wbHVnaW5zXCI7XHJcblxyXG5cclxuaW1wb3J0IHtcclxuICBBdXRvY29tcGxldGUsXHJcbiAgRm9ybUdyb3VwLFxyXG4gIEZvcm1MYWJlbCxcclxuICBNZW51SXRlbSxcclxuICBTZWxlY3QsXHJcbiAgU3RhY2ssXHJcbiAgVGV4dEZpZWxkLFxyXG59IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IERhdGVQaWNrZXIsIExvY2FsaXphdGlvblByb3ZpZGVyIH0gZnJvbSBcIkBtdWkveC1kYXRlLXBpY2tlcnNcIjtcclxuaW1wb3J0IHsgRGVtb0NvbnRhaW5lciB9IGZyb20gXCJAbXVpL3gtZGF0ZS1waWNrZXJzL2ludGVybmFscy9kZW1vXCI7XHJcbmltcG9ydCBkYXlqcyBmcm9tIFwiZGF5anNcIjtcclxuaW1wb3J0IHsgQWRhcHRlckRheWpzIH0gZnJvbSBcIkBtdWkveC1kYXRlLXBpY2tlcnMvQWRhcHRlckRheWpzXCI7XHJcbmltcG9ydCB7IHVzZVNhdmVGaWxlIH0gZnJvbSBcIkAvZmVhdHVyZXMvb3Bwb3J0dW5pdHkvaG9va3Mvb3Bwb3J0dW5pdHkuaG9va3NcIjtcclxuaW1wb3J0IHVzZUN1cnJlbnRVc2VyIGZyb20gXCJAL2ZlYXR1cmVzL2F1dGgvaG9va3MvY3VycmVudFVzZXIuaG9va3NcIjtcclxuZnVuY3Rpb24gQWRkQXJ0aWNsZUZSKHtcclxuICBlcnJvcnMsXHJcbiAgdG91Y2hlZCxcclxuICBzZXRGaWVsZFZhbHVlLFxyXG4gIHZhbHVlcyxcclxuICBvbkltYWdlU2VsZWN0LFxyXG4gIGNhdGVnb3JpZXMsXHJcbiAgZmlsdGVyZWRDYXRlZ29yaWVzLFxyXG4gIG9uQ2F0ZWdvcmllc1NlbGVjdCxcclxuICBkZWJvdW5jZSxcclxufSkge1xyXG4gIGNvbnN0IGhhbmRsZVBhc3RlID0gKGV2ZW50LCBjbGVhbkRhdGEsIG1heENoYXJDb3VudCkgPT4ge1xyXG4gICAgbGV0IGh0bWwgPSBjbGVhbkRhdGE7XHJcblxyXG4gICAgLy8gQ29ycmVjdGlvbiBkZXMgYmFsaXNlcyBub24gZmVybcOpZXNcclxuICAgIGh0bWwgPSBodG1sLnJlcGxhY2UoLzxzdHJvbmc+KC4qPykkL2csIFwiPHN0cm9uZz4kMTwvc3Ryb25nPlwiKTtcclxuXHJcbiAgICByZXR1cm4gaHRtbDtcclxuICB9O1xyXG4gIGNvbnN0IEtleUNvZGVzID0ge1xyXG4gICAgY29tbWE6IDE4OCxcclxuICAgIGVudGVyOiAxMyxcclxuICB9O1xyXG4gIGNvbnN0IGRlbGltaXRlcnMgPSBbS2V5Q29kZXMuY29tbWEsIEtleUNvZGVzLmVudGVyXTtcclxuICBjb25zdCBbdGFncywgc2V0VGFnc10gPSB1c2VTdGF0ZShbXSk7XHJcbiAgY29uc3QgW2hpZ2hsaWdodHMsIHNldEhpZ2hsaWdodHNdID0gdXNlU3RhdGUoW10pO1xyXG5cclxuICBjb25zdCB7IHVzZXIgfSA9IHVzZUN1cnJlbnRVc2VyKCk7XHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigpO1xyXG4gIGNvbnN0IFtwdWJsaXNoTm93LCBzZXRQdWJsaXNoTm93XSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbcHVibGlzaERhdGUsIHNldFB1Ymxpc2hEYXRlXSA9IHVzZVN0YXRlKG5ldyBEYXRlKCkpO1xyXG4gIGNvbnN0IGltYWdlSW5wdXRSZWYgPSB1c2VSZWYobnVsbCk7XHJcbiAgY29uc3QgW3NlbGVjdGVkSW1hZ2UsIHNldFNlbGVjdGVkSW1hZ2VdID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgY29uc3QgbGFuZ3VhZ2UgPSBcImZyXCI7XHJcblxyXG4gIGNvbnN0IGhhbmRsZVBob3RvQ2hhbmdlID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc3Qgc2VsZWN0ZWRGaWxlID0gaW1hZ2VJbnB1dFJlZi5jdXJyZW50LmZpbGVzWzBdO1xyXG4gICAgc2V0U2VsZWN0ZWRJbWFnZShpbWFnZUlucHV0UmVmLmN1cnJlbnQuZmlsZXNbMF0pO1xyXG5cclxuICAgIGlmIChzZWxlY3RlZEZpbGUpIHtcclxuICAgICAgb25JbWFnZVNlbGVjdChzZWxlY3RlZEZpbGUsIGxhbmd1YWdlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCB1c2VTYXZlRmlsZUhvb2sgPSB1c2VTYXZlRmlsZSgpO1xyXG5cclxuICBjb25zdCBoYW5kbGVQaG90b0Jsb2dDaGFuZ2UgPSBhc3luYyAoZmlsZSwgaW5mbywgY29yZSwgdXBsb2FkSGFuZGxlcikgPT4ge1xyXG4gICAgaWYgKGZpbGUgaW5zdGFuY2VvZiBIVE1MSW1hZ2VFbGVtZW50KSB7XHJcbiAgICAgIGNvbnN0IHNyYyA9IGZpbGUuc3JjO1xyXG5cclxuICAgICAgaWYgKHNyYy5zdGFydHNXaXRoKFwiZGF0YTppbWFnZVwiKSkge1xyXG4gICAgICAgIGNvbnN0IGJhc2U2NERhdGEgPSBzcmMuc3BsaXQoXCIsXCIpWzFdO1xyXG4gICAgICAgIGNvbnN0IGNvbnRlbnRUeXBlID0gc3JjLm1hdGNoKC9kYXRhOiguKj8pO2Jhc2U2NC8pWzFdO1xyXG4gICAgICAgIGNvbnN0IGJ5dGVDaGFyYWN0ZXJzID0gYXRvYihiYXNlNjREYXRhKTtcclxuICAgICAgICBjb25zdCBieXRlTnVtYmVycyA9IG5ldyBBcnJheShieXRlQ2hhcmFjdGVycy5sZW5ndGgpXHJcbiAgICAgICAgICAuZmlsbCgwKVxyXG4gICAgICAgICAgLm1hcCgoXywgaSkgPT4gYnl0ZUNoYXJhY3RlcnMuY2hhckNvZGVBdChpKSk7XHJcbiAgICAgICAgY29uc3QgYnl0ZUFycmF5ID0gbmV3IFVpbnQ4QXJyYXkoYnl0ZU51bWJlcnMpO1xyXG5cclxuICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2J5dGVBcnJheV0sIHsgdHlwZTogY29udGVudFR5cGUgfSk7XHJcbiAgICAgICAgY29uc3QgZmlsZU5hbWUgPSBgaW1hZ2VfJHtEYXRlLm5vdygpfS4ke2NvbnRlbnRUeXBlLnNwbGl0KFwiL1wiKVsxXX1gO1xyXG4gICAgICAgIGNvbnN0IHNlbGVjdGVkRmlsZSA9IG5ldyBGaWxlKFtibG9iXSwgZmlsZU5hbWUsIHsgdHlwZTogY29udGVudFR5cGUgfSk7XHJcblxyXG4gICAgICAgIGF3YWl0IHVwbG9hZEZpbGUoc2VsZWN0ZWRGaWxlLCB1cGxvYWRIYW5kbGVyLCBjb3JlLCBmaWxlKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBmZXRjaChzcmMpXHJcbiAgICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHJlc3BvbnNlLmJsb2IoKSlcclxuICAgICAgICAgIC50aGVuKChibG9iKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGNvbnRlbnRUeXBlID0gYmxvYi50eXBlO1xyXG4gICAgICAgICAgICBjb25zdCBmaWxlTmFtZSA9IGBpbWFnZV8ke0RhdGUubm93KCl9LiR7Y29udGVudFR5cGUuc3BsaXQoXCIvXCIpWzFdfWA7XHJcbiAgICAgICAgICAgIGNvbnN0IHNlbGVjdGVkRmlsZSA9IG5ldyBGaWxlKFtibG9iXSwgZmlsZU5hbWUsIHtcclxuICAgICAgICAgICAgICB0eXBlOiBjb250ZW50VHlwZSxcclxuICAgICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgICB1cGxvYWRGaWxlKHNlbGVjdGVkRmlsZSwgdXBsb2FkSGFuZGxlciwgY29yZSwgZmlsZSk7XHJcbiAgICAgICAgICB9KVxyXG4gICAgICAgICAgLmNhdGNoKChlcnJvcikgPT5cclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGNvbnZlcnRpbmcgaW1hZ2UgVVJMIHRvIEJsb2I6XCIsIGVycm9yKVxyXG4gICAgICAgICAgKTtcclxuICAgICAgfVxyXG4gICAgfSBlbHNlIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkZpbGUgaXMgbm90IGFuIEhUTUxJbWFnZUVsZW1lbnQuXCIpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IHVwbG9hZEZpbGUgPSAoc2VsZWN0ZWRGaWxlLCB1cGxvYWRIYW5kbGVyLCBjb3JlLCBvcmlnaW5hbEltYWdlKSA9PiB7XHJcbiAgICBsZXQgdXVpZFBob3RvO1xyXG4gICAgdXVpZFBob3RvID0gdXVpZHY0KCkucmVwbGFjZSgvLS9nLCBcIlwiKTtcclxuXHJcbiAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpO1xyXG4gICAgZm9ybURhdGEuYXBwZW5kKFwiZmlsZVwiLCBzZWxlY3RlZEZpbGUpO1xyXG5cclxuICAgIGNvbnN0IGV4dGVuc2lvbiA9IHNlbGVjdGVkRmlsZS5uYW1lLnNwbGl0KFwiLlwiKS5wb3AoKTtcclxuICAgIGNvbnN0IGN1cnJlbnRZZWFyID0gbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpO1xyXG5cclxuICAgIHVzZVNhdmVGaWxlSG9vay5tdXRhdGUoXHJcbiAgICAgIHtcclxuICAgICAgICByZXNvdXJjZTogXCJibG9nc1wiLFxyXG4gICAgICAgIGZvbGRlcjogY3VycmVudFllYXIudG9TdHJpbmcoKSxcclxuICAgICAgICBmaWxlbmFtZTogdXVpZFBob3RvLFxyXG4gICAgICAgIGJvZHk6IHsgZm9ybURhdGEsIHQgfSxcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIG9uU3VjY2VzczogKGRhdGFVVUlEKSA9PiB7XHJcbiAgICAgICAgICBjb25zdCB1dWlkUGhvdG9GaWxlTmFtZSA9XHJcbiAgICAgICAgICAgIGRhdGFVVUlELm1lc3NhZ2UgPT09IFwidXVpZCBleGlzdFwiXHJcbiAgICAgICAgICAgICAgPyBkYXRhVVVJRC51dWlkXHJcbiAgICAgICAgICAgICAgOiBgJHt1dWlkUGhvdG99LiR7ZXh0ZW5zaW9ufWA7XHJcblxyXG4gICAgICAgICAgY29uc3QgaW1hZ2VVcmwgPSBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19CQVNFX0FQSV9VUkx9L2ZpbGVzLyR7dXVpZFBob3RvRmlsZU5hbWV9YDtcclxuXHJcbiAgICAgICAgICBvcmlnaW5hbEltYWdlLnNyYyA9IGltYWdlVXJsO1xyXG5cclxuICAgICAgICAgIHVwbG9hZEhhbmRsZXIoe1xyXG4gICAgICAgICAgICByZXN1bHQ6IFtcclxuICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICBpZDogdXVpZFBob3RvRmlsZU5hbWUsXHJcbiAgICAgICAgICAgICAgICB1cmw6IGltYWdlVXJsLFxyXG4gICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIF0sXHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHVwbG9hZGluZyBmaWxlOlwiLCBlcnJvcik7XHJcbiAgICAgICAgfSxcclxuICAgICAgfVxyXG4gICAgKTtcclxuICB9O1xyXG4gIGNvbnN0IFt0aXRsZWZyLCBzZXRUaXRsZWZyXSA9IHVzZVN0YXRlKCgpID0+IHtcclxuICAgIGNvbnN0IHNhdmVkVGl0bGVmciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwidGl0bGVmclwiKTtcclxuICAgIHJldHVybiBzYXZlZFRpdGxlZnIgPyBKU09OLnBhcnNlKHNhdmVkVGl0bGVmcikgOiBcIlwiO1xyXG4gIH0pO1xyXG4gIGNvbnN0IFttZXRhdGl0bGVmciwgc2V0TWV0YXRpdGxlZnJdID0gdXNlU3RhdGUoKCkgPT4ge1xyXG4gICAgY29uc3Qgc2F2ZWRNZXRhdGl0bGVmciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwibWV0YXRpdGxlZnJcIik7XHJcbiAgICByZXR1cm4gc2F2ZWRNZXRhdGl0bGVmciA/IEpTT04ucGFyc2Uoc2F2ZWRNZXRhdGl0bGVmcikgOiBcIlwiO1xyXG4gIH0pO1xyXG4gIGNvbnN0IFttZXRhRGVzY3JpcHRpb25mciwgc2V0TWV0YURlc2NyaXB0aW9uZnJdID0gdXNlU3RhdGUoKCkgPT4ge1xyXG4gICAgY29uc3Qgc2F2ZWRNZXRhZGVzY3JpcHRpb25mciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwibWV0YURlc2NyaXB0aW9uZnJcIik7XHJcbiAgICByZXR1cm4gc2F2ZWRNZXRhZGVzY3JpcHRpb25mciA/IEpTT04ucGFyc2Uoc2F2ZWRNZXRhZGVzY3JpcHRpb25mcikgOiBcIlwiO1xyXG4gIH0pO1xyXG5cclxuICBjb25zdCBbY29udGVudGZyLCBzZXRDb250ZW50ZnJdID0gdXNlU3RhdGUoKCkgPT4ge1xyXG4gICAgY29uc3Qgc2F2ZWRDb250ZW50ZnIgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImNvbnRlbnRmclwiKTtcclxuICAgIHJldHVybiBzYXZlZENvbnRlbnRmciA/IEpTT04ucGFyc2Uoc2F2ZWRDb250ZW50ZnIpIDogXCJcIjtcclxuICB9KTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRWRpdG9yQ2hhbmdlID0gKG5ld0NvbnRlbnRmcikgPT4ge1xyXG4gICAgc2V0Q29udGVudGZyKG5ld0NvbnRlbnRmcik7XHJcbiAgICBzZXRGaWVsZFZhbHVlKFwiY29udGVudEZSXCIsIG5ld0NvbnRlbnRmcik7XHJcbiAgfTtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKHRpdGxlZnIpIHtcclxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJ0aXRsZWZyXCIsIEpTT04uc3RyaW5naWZ5KHRpdGxlZnIpKTtcclxuICAgIH1cclxuICAgIGlmIChjb250ZW50ZnIpIHtcclxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJjb250ZW50ZnJcIiwgSlNPTi5zdHJpbmdpZnkoY29udGVudGZyKSk7XHJcbiAgICB9XHJcbiAgICBpZiAobWV0YXRpdGxlZnIpIHtcclxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJtZXRhdGl0bGVmclwiLCBKU09OLnN0cmluZ2lmeShtZXRhdGl0bGVmcikpO1xyXG4gICAgfVxyXG4gICAgaWYgKG1ldGFEZXNjcmlwdGlvbmZyKSB7XHJcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFxyXG4gICAgICAgIFwibWV0YURlc2NyaXB0aW9uZnJcIixcclxuICAgICAgICBKU09OLnN0cmluZ2lmeShtZXRhRGVzY3JpcHRpb25mcilcclxuICAgICAgKTtcclxuICAgIH1cclxuICB9LCBbdGl0bGVmciwgY29udGVudGZyLCBtZXRhdGl0bGVmciwgbWV0YURlc2NyaXB0aW9uZnJdKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxwIGNsYXNzTmFtZT1cImxhYmVsLXBlbnRhYmVsbFwiPkFkZCBhcnRpY2xlIEZyZW5jaCA6IDwvcD5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZ3JvdXBcIj5cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPEZvcm1Hcm91cD5cclxuICAgICAgICAgICAgPEZvcm1MYWJlbCBjbGFzc05hbWU9XCJsYWJlbC1mb3JtXCI+XHJcbiAgICAgICAgICAgICAge3QoXCJjcmVhdGVBcnRpY2xlOnRpdGxlXCIpfVxyXG4gICAgICAgICAgICAgIDxUZXh0RmllbGRcclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzdGFuZGFyZFwiXHJcbiAgICAgICAgICAgICAgICBuYW1lPVwidGl0bGVGUlwiXHJcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17dGl0bGVmciB8fCB2YWx1ZXMudGl0bGVGUn1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICBjb25zdCB0aXRsZUZSID0gZS50YXJnZXQudmFsdWU7XHJcbiAgICAgICAgICAgICAgICAgIHNldEZpZWxkVmFsdWUoXCJ0aXRsZUZSXCIsIHRpdGxlRlIpO1xyXG4gICAgICAgICAgICAgICAgICBzZXRUaXRsZWZyKHRpdGxlRlIpO1xyXG4gICAgICAgICAgICAgICAgICBjb25zdCB1cmwgPSBzbHVnKHRpdGxlRlIpO1xyXG4gICAgICAgICAgICAgICAgICBzZXRGaWVsZFZhbHVlKFwidXJsRlJcIiwgdXJsKTtcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xyXG4gICAgICAgICAgICAgICAgICBcImlucHV0LXBlbnRhYmVsbFwiICtcclxuICAgICAgICAgICAgICAgICAgKGVycm9ycy50aXRsZUZSICYmIHRvdWNoZWQudGl0bGVGUiA/IFwiIGlzLWludmFsaWRcIiA6IFwiXCIpXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgLz57XCIgXCJ9XHJcbiAgICAgICAgICAgICAgPEVycm9yTWVzc2FnZVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibGFiZWwtZXJyb3JcIlxyXG4gICAgICAgICAgICAgICAgbmFtZT1cInRpdGxlRlJcIlxyXG4gICAgICAgICAgICAgICAgY29tcG9uZW50PVwiZGl2XCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0Zvcm1MYWJlbD5cclxuICAgICAgICAgIDwvRm9ybUdyb3VwPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICA8Rm9ybUdyb3VwPlxyXG4gICAgICAgICAgICA8Rm9ybUxhYmVsIGNsYXNzTmFtZT1cImxhYmVsLWZvcm1cIj5cclxuICAgICAgICAgICAgICB7dChcImNyZWF0ZUFydGljbGU6Y2F0ZWdvcmllc1wiKX1cclxuICAgICAgICAgICAgICA8U3RhY2s+XHJcbiAgICAgICAgICAgICAgICA8QXV0b2NvbXBsZXRlXHJcbiAgICAgICAgICAgICAgICAgIG11bHRpcGxlXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LXBlbnRhYmVsbFwiXHJcbiAgICAgICAgICAgICAgICAgIGlkPVwidGFncy1zdGFuZGFyZFwiXHJcbiAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e1xyXG4gICAgICAgICAgICAgICAgICAgIGZpbHRlcmVkQ2F0ZWdvcmllcy5sZW5ndGggPiAwXHJcbiAgICAgICAgICAgICAgICAgICAgICA/IGZpbHRlcmVkQ2F0ZWdvcmllc1xyXG4gICAgICAgICAgICAgICAgICAgICAgOiBjYXRlZ29yaWVzXHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgZ2V0T3B0aW9uTGFiZWw9eyhvcHRpb24pID0+IG9wdGlvbi5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICBzZWxlY3RlZD17XHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWVzLmNhdGVnb3J5RlIubGVuZ3RoID4gMFxyXG4gICAgICAgICAgICAgICAgICAgICAgPyAoZmlsdGVyZWRDYXRlZ29yaWVzLmxlbmd0aCA+IDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IGZpbHRlcmVkQ2F0ZWdvcmllc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogY2F0ZWdvcmllc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICApLmZpbHRlcigoY2F0ZWdvcnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWVzLmNhdGVnb3J5RlIuaW5jbHVkZXMoY2F0ZWdvcnkuaWQpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgIDogW11cclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGV2ZW50LCBzZWxlY3RlZE9wdGlvbnMpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBjYXRlZ29yeUlkcyA9IHNlbGVjdGVkT3B0aW9ucy5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAoY2F0ZWdvcnkpID0+IGNhdGVnb3J5LmlkXHJcbiAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRGaWVsZFZhbHVlKFwiY2F0ZWdvcnlGUlwiLCBjYXRlZ29yeUlkcyk7XHJcbiAgICAgICAgICAgICAgICAgICAgb25DYXRlZ29yaWVzU2VsZWN0KGNhdGVnb3J5SWRzKTtcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgcmVuZGVySW5wdXQ9eyhwYXJhbXMpID0+IChcclxuICAgICAgICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICB7Li4ucGFyYW1zfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5wdXQtcGVudGFiZWxsICBtdWx0aXBsZS1zZWxlY3RcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cInN0YW5kYXJkXCJcclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L1N0YWNrPlxyXG4gICAgICAgICAgICA8L0Zvcm1MYWJlbD5cclxuICAgICAgICAgIDwvRm9ybUdyb3VwPlxyXG5cclxuICAgICAgICAgIHt0b3VjaGVkLmNhdGVnb3J5RlIgJiYgZXJyb3JzLmNhdGVnb3J5RlIgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImVycm9yXCI+e2Vycm9ycy5jYXRlZ29yeUZSfTwvZGl2PlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWdyb3VwXCI+XHJcbiAgICAgICAgPGRpdj5cclxuICAgICAgICAgIDxGb3JtR3JvdXA+XHJcbiAgICAgICAgICAgIDxGb3JtTGFiZWwgY2xhc3NOYW1lPVwibGFiZWwtZm9ybVwiPlxyXG4gICAgICAgICAgICAgIERlc2NyaXB0aW9uXHJcbiAgICAgICAgICAgICAgPFRleHRGaWVsZFxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cInN0YW5kYXJkXCJcclxuICAgICAgICAgICAgICAgIG5hbWU9XCJkZXNjcmlwdGlvbkZSXCJcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgIG11bHRpbGluZVxyXG4gICAgICAgICAgICAgICAgcm93cz17M31cclxuICAgICAgICAgICAgICAgIHZhbHVlPXt2YWx1ZXMuZGVzY3JpcHRpb25GUn1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICBjb25zdCBkZXNjcmlwdGlvbkZSID0gZS50YXJnZXQudmFsdWU7XHJcbiAgICAgICAgICAgICAgICAgIHNldEZpZWxkVmFsdWUoXCJkZXNjcmlwdGlvbkZSXCIsIGRlc2NyaXB0aW9uRlIpO1xyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17XHJcbiAgICAgICAgICAgICAgICAgIFwidGV4dEFyZWEtcGVudGFiZWxsXCIgK1xyXG4gICAgICAgICAgICAgICAgICAoZXJyb3JzLmRlc2NyaXB0aW9uRlIgJiYgdG91Y2hlZC5kZXNjcmlwdGlvbkZSXHJcbiAgICAgICAgICAgICAgICAgICAgPyBcIiBpcy1pbnZhbGlkXCJcclxuICAgICAgICAgICAgICAgICAgICA6IFwiXCIpXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgLz57XCIgXCJ9XHJcbiAgICAgICAgICAgICAgPEVycm9yTWVzc2FnZVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibGFiZWwtZXJyb3JcIlxyXG4gICAgICAgICAgICAgICAgbmFtZT1cImRlc2NyaXB0aW9uRlJcIlxyXG4gICAgICAgICAgICAgICAgY29tcG9uZW50PVwiZGl2XCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0Zvcm1MYWJlbD5cclxuICAgICAgICAgIDwvRm9ybUdyb3VwPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZ3JvdXBcIj5cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPEZvcm1Hcm91cD5cclxuICAgICAgICAgICAgPEZvcm1MYWJlbCBjbGFzc05hbWU9XCJsYWJlbC1mb3JtXCI+XHJcbiAgICAgICAgICAgICAgSGlnaGxpZ2h0c1xyXG4gICAgICAgICAgICAgIDxkaXYgaWQ9XCJ0YWdzXCI+XHJcbiAgICAgICAgICAgICAgICA8UmVhY3RUYWdzXHJcbiAgICAgICAgICAgICAgICAgIHRhZ3M9e2hpZ2hsaWdodHN9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17XHJcbiAgICAgICAgICAgICAgICAgICAgXCJpbnB1dC1wZW50YWJlbGxcIiArXHJcbiAgICAgICAgICAgICAgICAgICAgKGVycm9ycy5oaWdobGlnaHRzRlIgJiYgdG91Y2hlZC5oaWdobGlnaHRzRlJcclxuICAgICAgICAgICAgICAgICAgICAgID8gXCIgaXMtaW52YWxpZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA6IFwiXCIpXHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgZGVsaW1pdGVycz17ZGVsaW1pdGVyc31cclxuICAgICAgICAgICAgICAgICAgaGFuZGxlRGVsZXRlPXsoaSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRUYWdzID0gaGlnaGxpZ2h0cy5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgICAodGFnLCBpbmRleCkgPT4gaW5kZXggIT09IGlcclxuICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgIHNldEhpZ2hsaWdodHModXBkYXRlZFRhZ3MpO1xyXG4gICAgICAgICAgICAgICAgICAgIHNldEZpZWxkVmFsdWUoXHJcbiAgICAgICAgICAgICAgICAgICAgICBcImhpZ2hsaWdodHNGUlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgdXBkYXRlZFRhZ3MubWFwKCh0YWcpID0+IHRhZy50ZXh0KVxyXG4gICAgICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgIGhhbmRsZUFkZGl0aW9uPXsodGFnKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0SGlnaGxpZ2h0cyhbLi4uaGlnaGxpZ2h0cywgdGFnXSk7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0RmllbGRWYWx1ZShcclxuICAgICAgICAgICAgICAgICAgICAgIFwiaGlnaGxpZ2h0c0ZSXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICBbLi4uaGlnaGxpZ2h0cywgdGFnXS5tYXAoKGl0ZW0pID0+IGl0ZW0udGV4dClcclxuICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICBpbnB1dEZpZWxkUG9zaXRpb249XCJib3R0b21cIlxyXG4gICAgICAgICAgICAgICAgICBhdXRvY29tcGxldGVcclxuICAgICAgICAgICAgICAgICAgYWxsb3dEcmFnRHJvcD17ZmFsc2V9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxFcnJvck1lc3NhZ2VcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxhYmVsLWVycm9yXCJcclxuICAgICAgICAgICAgICAgIG5hbWU9XCJrZXl3b3Jkc0VOXCJcclxuICAgICAgICAgICAgICAgIGNvbXBvbmVudD1cImRpdlwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9Gb3JtTGFiZWw+XHJcbiAgICAgICAgICA8L0Zvcm1Hcm91cD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxTdW5FZGl0b3JcclxuICAgICAgICBzZXRDb250ZW50cz17Y29udGVudGZyIHx8IHZhbHVlcz8uY29udGVudEZSIHx8IFwiXCJ9XHJcbiAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUVkaXRvckNoYW5nZX1cclxuICAgICAgICBvblBhc3RlPXtoYW5kbGVQYXN0ZX1cclxuICAgICAgICBzZXRPcHRpb25zPXt7XHJcbiAgICAgICAgICBjbGVhbkhUTUw6IGZhbHNlLFxyXG4gICAgICAgICAgZGlzYWJsZUh0bWxTYW5pdGl6ZXI6IHRydWUsXHJcbiAgICAgICAgICBhZGRUYWdzV2hpdGVsaXN0OlxyXG4gICAgICAgICAgICBcImgxfGgyfGgzfGg0fGg1fGg2fHB8ZGl2fHNwYW58c3Ryb25nfGVtfHVsfGxpfG9sfGF8aW1nfHRhYmxlfHRoZWFkfHRib2R5fHRyfHRkfGJyfGhyfGJ1dHRvblwiLFxyXG4gICAgICAgICAgcGx1Z2luczogcGx1Z2lucyxcclxuICAgICAgICAgIGJ1dHRvbkxpc3Q6IFtcclxuICAgICAgICAgICAgW1widW5kb1wiLCBcInJlZG9cIl0sXHJcbiAgICAgICAgICAgIFtcImZvbnRcIiwgXCJmb250U2l6ZVwiLCBcImZvcm1hdEJsb2NrXCJdLFxyXG4gICAgICAgICAgICBbXHJcbiAgICAgICAgICAgICAgXCJib2xkXCIsXHJcbiAgICAgICAgICAgICAgXCJ1bmRlcmxpbmVcIixcclxuICAgICAgICAgICAgICBcIml0YWxpY1wiLFxyXG4gICAgICAgICAgICAgIFwic3RyaWtlXCIsXHJcbiAgICAgICAgICAgICAgXCJzdWJzY3JpcHRcIixcclxuICAgICAgICAgICAgICBcInN1cGVyc2NyaXB0XCIsXHJcbiAgICAgICAgICAgIF0sXHJcbiAgICAgICAgICAgIFtcImZvbnRDb2xvclwiLCBcImhpbGl0ZUNvbG9yXCJdLFxyXG4gICAgICAgICAgICBbXCJhbGlnblwiLCBcImxpc3RcIiwgXCJsaW5lSGVpZ2h0XCJdLFxyXG4gICAgICAgICAgICBbXCJvdXRkZW50XCIsIFwiaW5kZW50XCJdLFxyXG4gICAgICAgICAgICBbXCJ0YWJsZVwiLCBcImhvcml6b250YWxSdWxlXCIsIFwibGlua1wiLCBcImltYWdlXCIsIFwidmlkZW9cIl0sXHJcbiAgICAgICAgICAgIFtcImZ1bGxTY3JlZW5cIiwgXCJzaG93QmxvY2tzXCIsIFwiY29kZVZpZXdcIl0sXHJcbiAgICAgICAgICAgIFtcInByZXZpZXdcIiwgXCJwcmludFwiXSxcclxuICAgICAgICAgICAgW1wicmVtb3ZlRm9ybWF0XCJdLFxyXG4gICAgICAgICAgXSxcclxuICAgICAgICAgIGltYWdlVXBsb2FkSGFuZGxlcjogaGFuZGxlUGhvdG9CbG9nQ2hhbmdlLFxyXG4gICAgICAgICAgZGVmYXVsdFRhZzogXCJkaXZcIixcclxuICAgICAgICAgIG1pbkhlaWdodDogXCIzMDBweFwiLFxyXG4gICAgICAgICAgbWF4SGVpZ2h0OiBcIjQwMHB4XCIsXHJcbiAgICAgICAgICBzaG93UGF0aExhYmVsOiBmYWxzZSxcclxuICAgICAgICAgIGZvbnQ6IFtcclxuICAgICAgICAgICAgXCJQcm94aW1hLU5vdmEtUmVndWxhclwiLFxyXG4gICAgICAgICAgICBcIlByb3hpbWEtTm92YS1NZWRpdW1cIixcclxuICAgICAgICAgICAgXCJQcm94aW1hLU5vdmEtU2VtaWJvbGRcIixcclxuICAgICAgICAgICAgXCJQcm94aW1hLU5vdmEtQm9sZFwiLFxyXG4gICAgICAgICAgICBcIlByb3hpbWEtTm92YS1FeHRyYWJvbGRcIixcclxuICAgICAgICAgICAgXCJQcm94aW1hLU5vdmEtQmxhY2tcIixcclxuICAgICAgICAgICAgXCJQcm94aW1hLU5vdmEtTGlnaHRcIixcclxuICAgICAgICAgICAgXCJQcm94aW1hLU5vdmEtVGhpblwiLFxyXG4gICAgICAgICAgICBcIkFyaWFsXCIsXHJcbiAgICAgICAgICAgIFwiVGltZXMgTmV3IFJvbWFuXCIsXHJcbiAgICAgICAgICAgIFwiU2Fucy1TZXJpZlwiLFxyXG4gICAgICAgICAgXSxcclxuICAgICAgICAgIGNoYXJDb3VudGVyOiB0cnVlLCAvLyBTaG93IGNoYXJhY3RlciBjb3VudGVyXHJcbiAgICAgICAgICBjaGFyQ291bnRlclR5cGU6IFwiYnl0ZVwiLFxyXG4gICAgICAgICAgcmVzaXppbmdCYXI6IGZhbHNlLCAvLyBIaWRlIHJlc2l6aW5nIGJhciBmb3IgYSBjbGVhbmVyIFVJXHJcbiAgICAgICAgICBjb2xvckxpc3Q6IFtcclxuICAgICAgICAgICAgLy8gU3RhbmRhcmQgQ29sb3JzXHJcbiAgICAgICAgICAgIFtcclxuICAgICAgICAgICAgICBcIiMyMzQ3OTFcIixcclxuICAgICAgICAgICAgICBcIiNkNjliMTlcIixcclxuICAgICAgICAgICAgICBcIiNjYzMyMzNcIixcclxuICAgICAgICAgICAgICBcIiMwMDk5NjZcIixcclxuICAgICAgICAgICAgICBcIiMwYjMwNTFcIixcclxuICAgICAgICAgICAgICBcIiMyQkJGQURcIixcclxuICAgICAgICAgICAgICBcIiMwYjMwNTEwMFwiLFxyXG4gICAgICAgICAgICAgIFwiIzBhMzA1MjE0XCIsXHJcbiAgICAgICAgICAgICAgXCIjNzQzNzk0XCIsXHJcbiAgICAgICAgICAgICAgXCIjZmYwMDAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjZmY1ZTAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjZmZlNDAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjYWJmMjAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjMDBkOGZmXCIsXHJcbiAgICAgICAgICAgICAgXCIjMDA1NWZmXCIsXHJcbiAgICAgICAgICAgICAgXCIjNjYwMGZmXCIsXHJcbiAgICAgICAgICAgICAgXCIjZmYwMGRkXCIsXHJcbiAgICAgICAgICAgICAgXCIjMDAwMDAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjZmZkOGQ4XCIsXHJcbiAgICAgICAgICAgICAgXCIjZmFlMGQ0XCIsXHJcbiAgICAgICAgICAgICAgXCIjZmFmNGMwXCIsXHJcbiAgICAgICAgICAgICAgXCIjZTRmN2JhXCIsXHJcbiAgICAgICAgICAgICAgXCIjZDRmNGZhXCIsXHJcbiAgICAgICAgICAgICAgXCIjZDllNWZmXCIsXHJcbiAgICAgICAgICAgICAgXCIjZThkOWZmXCIsXHJcbiAgICAgICAgICAgICAgXCIjZmZkOWZhXCIsXHJcbiAgICAgICAgICAgICAgXCIjZjFmMWYxXCIsXHJcbiAgICAgICAgICAgICAgXCIjZmZhN2E3XCIsXHJcbiAgICAgICAgICAgICAgXCIjZmZjMTllXCIsXHJcbiAgICAgICAgICAgICAgXCIjZmFlZDdkXCIsXHJcbiAgICAgICAgICAgICAgXCIjY2VmMjc5XCIsXHJcbiAgICAgICAgICAgICAgXCIjYjJlYmY0XCIsXHJcbiAgICAgICAgICAgICAgXCIjYjJjY2ZmXCIsXHJcbiAgICAgICAgICAgICAgXCIjZDFiMmZmXCIsXHJcbiAgICAgICAgICAgICAgXCIjZmZiMmY1XCIsXHJcbiAgICAgICAgICAgICAgXCIjYmRiZGJkXCIsXHJcbiAgICAgICAgICAgICAgXCIjZjE1ZjVmXCIsXHJcbiAgICAgICAgICAgICAgXCIjZjI5NjYxXCIsXHJcbiAgICAgICAgICAgICAgXCIjZTVkODVjXCIsXHJcbiAgICAgICAgICAgICAgXCIjYmNlNTVjXCIsXHJcbiAgICAgICAgICAgICAgXCIjNWNkMWU1XCIsXHJcbiAgICAgICAgICAgICAgXCIjNjY5OWZmXCIsXHJcbiAgICAgICAgICAgICAgXCIjYTM2NmZmXCIsXHJcbiAgICAgICAgICAgICAgXCIjZjI2MWRmXCIsXHJcbiAgICAgICAgICAgICAgXCIjOGM4YzhjXCIsXHJcbiAgICAgICAgICAgICAgXCIjOTgwMDAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjOTkzODAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjOTk4YTAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjNmI5OTAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjMDA4Mjk5XCIsXHJcbiAgICAgICAgICAgICAgXCIjMDAzMzk5XCIsXHJcbiAgICAgICAgICAgICAgXCIjM2QwMDk5XCIsXHJcbiAgICAgICAgICAgICAgXCIjOTkwMDg1XCIsXHJcbiAgICAgICAgICAgICAgXCIjMzUzNTM1XCIsXHJcbiAgICAgICAgICAgICAgXCIjNjcwMDAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjNjYyNTAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjNjY1YzAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjNDc2NjAwXCIsXHJcbiAgICAgICAgICAgICAgXCIjMDA1NzY2XCIsXHJcbiAgICAgICAgICAgICAgXCIjMDAyMjY2XCIsXHJcbiAgICAgICAgICAgICAgXCIjMjkwMDY2XCIsXHJcbiAgICAgICAgICAgICAgXCIjNjYwMDU4XCIsXHJcbiAgICAgICAgICAgICAgXCIjMjIyMjIyXCIsXHJcbiAgICAgICAgICAgIF0sIC8vIEZvciBib3ggc2hhZG93IHdpdGggb3BhY2l0eVxyXG4gICAgICAgICAgXSxcclxuICAgICAgICB9fVxyXG4gICAgICAgIG9uSW1hZ2VVcGxvYWQ9e2hhbmRsZVBob3RvQmxvZ0NoYW5nZX1cclxuICAgICAgLz5cclxuICAgICAgPGJyPjwvYnI+XHJcblxyXG4gICAgICA8RmFxU2VjdGlvblxyXG4gICAgICAgIHZhbHVlcz17dmFsdWVzfVxyXG4gICAgICAgIHNldEZpZWxkVmFsdWU9e3NldEZpZWxkVmFsdWV9XHJcbiAgICAgICAgZXJyb3JzPXtlcnJvcnN9XHJcbiAgICAgICAgdG91Y2hlZD17dG91Y2hlZH1cclxuICAgICAgICBsYW5ndWFnZT1cIkZSXCJcclxuICAgICAgICBkZWJvdW5jZT17ZGVib3VuY2V9XHJcbiAgICAgIC8+XHJcbiAgICAgIDxicj48L2JyPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1ncm91cFwiPlxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICA8Rm9ybUdyb3VwPlxyXG4gICAgICAgICAgICA8Rm9ybUxhYmVsIGNsYXNzTmFtZT1cImxhYmVsLWZvcm1cIj5cclxuICAgICAgICAgICAgICB7dChcImNyZWF0ZUFydGljbGU6bWV0YVRpdGxlXCIpfSAoe1wiIFwifVxyXG4gICAgICAgICAgICAgIDxzcGFuXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xyXG4gICAgICAgICAgICAgICAgICB2YWx1ZXMubWV0YVRpdGxlRlI/Lmxlbmd0aCA+IDY1ID8gXCIgdGV4dC1kYW5nZXJcIiA6IFwiXCJcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7XCIgXCJ9XHJcbiAgICAgICAgICAgICAgICB7dmFsdWVzLm1ldGFUaXRsZUZSPy5sZW5ndGh9IC8gNjV7XCIgXCJ9XHJcbiAgICAgICAgICAgICAgPC9zcGFuPntcIiBcIn1cclxuICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgPFRleHRGaWVsZFxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cInN0YW5kYXJkXCJcclxuICAgICAgICAgICAgICAgIG5hbWU9XCJtZXRhVGl0bGVGUlwiXHJcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17bWV0YXRpdGxlZnIgfHwgdmFsdWVzLm1ldGFUaXRsZUZSfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnN0IG1ldGFUaXRsZUZSID0gZS50YXJnZXQudmFsdWU7XHJcbiAgICAgICAgICAgICAgICAgIHNldEZpZWxkVmFsdWUoXCJtZXRhVGl0bGVGUlwiLCBtZXRhVGl0bGVGUik7XHJcbiAgICAgICAgICAgICAgICAgIHNldE1ldGF0aXRsZWZyKG1ldGFUaXRsZUZSKTtcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xyXG4gICAgICAgICAgICAgICAgICBcImlucHV0LXBlbnRhYmVsbFwiICtcclxuICAgICAgICAgICAgICAgICAgKGVycm9ycy5tZXRhVGl0bGVGUiAmJiB0b3VjaGVkLm1ldGFUaXRsZUZSXHJcbiAgICAgICAgICAgICAgICAgICAgPyBcIiBpcy1pbnZhbGlkXCJcclxuICAgICAgICAgICAgICAgICAgICA6IFwiXCIpXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgLz57XCIgXCJ9XHJcbiAgICAgICAgICAgICAgPEVycm9yTWVzc2FnZVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibGFiZWwtZXJyb3JcIlxyXG4gICAgICAgICAgICAgICAgbmFtZT1cIm1ldGFUaXRsZUZSXCJcclxuICAgICAgICAgICAgICAgIGNvbXBvbmVudD1cImRpdlwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9Gb3JtTGFiZWw+XHJcbiAgICAgICAgICA8L0Zvcm1Hcm91cD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPEZvcm1Hcm91cD5cclxuICAgICAgICAgICAgPEZvcm1MYWJlbCBjbGFzc05hbWU9XCJsYWJlbC1mb3JtXCI+XHJcbiAgICAgICAgICAgICAge3QoXCJjcmVhdGVBcnRpY2xlOnVybFwiKX1cclxuICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwic3RhbmRhcmRcIlxyXG4gICAgICAgICAgICAgICAgbmFtZT1cInVybEZSXCJcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXt2YWx1ZXMudXJsRlJ9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgc2V0RmllbGRWYWx1ZShcInVybEZSXCIsIGUudGFyZ2V0LnZhbHVlKTtcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xyXG4gICAgICAgICAgICAgICAgICBcImlucHV0LXBlbnRhYmVsbFwiICtcclxuICAgICAgICAgICAgICAgICAgKGVycm9ycy51cmxGUiAmJiB0b3VjaGVkLnVybEZSID8gXCIgaXMtaW52YWxpZFwiIDogXCJcIilcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAvPntcIiBcIn1cclxuICAgICAgICAgICAgICA8RXJyb3JNZXNzYWdlXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJsYWJlbC1lcnJvclwiXHJcbiAgICAgICAgICAgICAgICBuYW1lPVwidXJsRlJcIlxyXG4gICAgICAgICAgICAgICAgY29tcG9uZW50PVwiZGl2XCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0Zvcm1MYWJlbD5cclxuICAgICAgICAgIDwvRm9ybUdyb3VwPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZ3JvdXBcIj5cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPEZvcm1Hcm91cD5cclxuICAgICAgICAgICAgPEZvcm1MYWJlbCBjbGFzc05hbWU9XCJsYWJlbC1mb3JtXCI+XHJcbiAgICAgICAgICAgICAge3QoXCJjcmVhdGVBcnRpY2xlOm1ldGFEZXNjcmlwdGlvblwiKX0gKHtcIiBcIn1cclxuICAgICAgICAgICAgICA8c3BhblxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtcclxuICAgICAgICAgICAgICAgICAgdmFsdWVzLm1ldGFEZXNjcmlwdGlvbkZSPy5sZW5ndGggPiAxNjAgPyBcIiB0ZXh0LWRhbmdlclwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHt2YWx1ZXMubWV0YURlc2NyaXB0aW9uRlI/Lmxlbmd0aH0gLyAxNjBcclxuICAgICAgICAgICAgICA8L3NwYW4+e1wiIFwifVxyXG4gICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwic3RhbmRhcmRcIlxyXG4gICAgICAgICAgICAgICAgbmFtZT1cIm1ldGFEZXNjcmlwdGlvbkZSXCJcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgIG11bHRpbGluZVxyXG4gICAgICAgICAgICAgICAgcm93cz17Mn1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXttZXRhRGVzY3JpcHRpb25mciB8fCB2YWx1ZXMubWV0YURlc2NyaXB0aW9uRlJ9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgY29uc3QgbWV0YURlc2NyaXB0aW9uRlIgPSBlLnRhcmdldC52YWx1ZTtcclxuICAgICAgICAgICAgICAgICAgc2V0RmllbGRWYWx1ZShcIm1ldGFEZXNjcmlwdGlvbkZSXCIsIG1ldGFEZXNjcmlwdGlvbkZSKTtcclxuICAgICAgICAgICAgICAgICAgc2V0TWV0YURlc2NyaXB0aW9uZnIobWV0YURlc2NyaXB0aW9uRlIpO1xyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17XHJcbiAgICAgICAgICAgICAgICAgIFwidGV4dEFyZWEtcGVudGFiZWxsXCIgK1xyXG4gICAgICAgICAgICAgICAgICAoZXJyb3JzLm1ldGFEZXNjcmlwdGlvbkZSICYmIHRvdWNoZWQubWV0YURlc2NyaXB0aW9uRlJcclxuICAgICAgICAgICAgICAgICAgICA/IFwiIGlzLWludmFsaWRcIlxyXG4gICAgICAgICAgICAgICAgICAgIDogXCJcIilcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAvPntcIiBcIn1cclxuICAgICAgICAgICAgICA8RXJyb3JNZXNzYWdlXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJsYWJlbC1lcnJvclwiXHJcbiAgICAgICAgICAgICAgICBuYW1lPVwibWV0YURlc2NyaXB0aW9uRlJcIlxyXG4gICAgICAgICAgICAgICAgY29tcG9uZW50PVwiZGl2XCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0Zvcm1MYWJlbD5cclxuICAgICAgICAgIDwvRm9ybUdyb3VwPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZ3JvdXBcIj5cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPEZvcm1Hcm91cD5cclxuICAgICAgICAgICAgPEZvcm1MYWJlbCBjbGFzc05hbWU9XCJsYWJlbC1mb3JtXCI+XHJcbiAgICAgICAgICAgICAge3QoXCJjcmVhdGVBcnRpY2xlOmZlYXR1cmVkSW1hZ2VcIil9XHJcblxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidXBsb2FkLWNvbnRhaW5lclwiPlxyXG4gICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9e2BpbWFnZS11cGxvYWQtZnJgfSBjbGFzc05hbWU9XCJmaWxlLWxhYmVsc1wiPlxyXG4gICAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZmlsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgaWQ9e2BpbWFnZS11cGxvYWQtZnJgfVxyXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJpbWFnZUVOXCJcclxuICAgICAgICAgICAgICAgICAgICBhY2NlcHQ9XCIucG5nLCAuanBnLCAuanBlZywgLndlYnBcIlxyXG4gICAgICAgICAgICAgICAgICAgIHJlZj17aW1hZ2VJbnB1dFJlZn1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgIHNldEZpZWxkVmFsdWUoXCJpbWFnZUZSXCIsIGUudGFyZ2V0LmZpbGVzWzBdKTtcclxuICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVBob3RvQ2hhbmdlKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgXCJmaWxlLWlucHV0XCIgK1xyXG4gICAgICAgICAgICAgICAgICAgICAgKGVycm9ycy5pbWFnZUZSICYmIHRvdWNoZWQuaW1hZ2VGUiA/IFwiIGlzLWludmFsaWRcIiA6IFwiXCIpXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInVwbG9hZC1hcmVhXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaWNvbi1waWNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRJbWFnZTogYHVybChcIiR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZEltYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gVVJMLmNyZWF0ZU9iamVjdFVSTChzZWxlY3RlZEltYWdlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHZhbHVlcy5pbWFnZUZSXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gYCR7cHJvY2Vzcy5lbnYuUkVBQ1RfQVBQX0FQSV9VUkx9JHtBUElfVVJMUy5maWxlc30vJHt2YWx1ZXMuaW1hZ2VGUn1gXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogdXBsb2FkLnNyY1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1cIilgLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRTaXplOiBcImNvdmVyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZFJlcGVhdDogXCJuby1yZXBlYXRcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kUG9zaXRpb246IFwiY2VudGVyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLy9iYWNrZ3JvdW5kQ29sb3I6IFwiIzQwYmQzOTIxXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICA+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInVwbG9hZC10ZXh0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHt0KFwiY3JlYXRlQXJ0aWNsZTphZGRGZWF0SW1nXCIpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidXBsb2FkLWRlc2NyaXB0aW9uXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHt0KFwiY3JlYXRlQXJ0aWNsZTpjbGlja0JveFwiKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxFcnJvck1lc3NhZ2VcclxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwiaW1hZ2VGUlwiXHJcbiAgICAgICAgICAgICAgICAgICAgY29tcG9uZW50PVwiZGl2XCJcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnZhbGlkLWZlZWRiYWNrIGVycm9yXCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvRm9ybUxhYmVsPlxyXG4gICAgICAgICAgPC9Gb3JtR3JvdXA+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZ3JvdXBcIj5cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPEZvcm1Hcm91cD5cclxuICAgICAgICAgICAgPEZvcm1MYWJlbCBjbGFzc05hbWU9XCJsYWJlbC1mb3JtXCI+XHJcbiAgICAgICAgICAgICAge3QoXCJjcmVhdGVBcnRpY2xlOmFsdFwiKX1cclxuICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwic3RhbmRhcmRcIlxyXG4gICAgICAgICAgICAgICAgbmFtZT1cImFsdEZSXCJcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXt2YWx1ZXMuYWx0RlJ9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgc2V0RmllbGRWYWx1ZShcImFsdEZSXCIsIGUudGFyZ2V0LnZhbHVlKTtcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xyXG4gICAgICAgICAgICAgICAgICBcImlucHV0LXBlbnRhYmVsbFwiICtcclxuICAgICAgICAgICAgICAgICAgKGVycm9ycy5hbHRGUiAmJiB0b3VjaGVkLmFsdEZSID8gXCIgaXMtaW52YWxpZFwiIDogXCJcIilcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAvPntcIiBcIn1cclxuICAgICAgICAgICAgICA8RXJyb3JNZXNzYWdlXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJsYWJlbC1lcnJvclwiXHJcbiAgICAgICAgICAgICAgICBuYW1lPVwiYWx0RlJcIlxyXG4gICAgICAgICAgICAgICAgY29tcG9uZW50PVwiZGl2XCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0Zvcm1MYWJlbD5cclxuICAgICAgICAgIDwvRm9ybUdyb3VwPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICA8Rm9ybUdyb3VwPlxyXG4gICAgICAgICAgICA8Rm9ybUxhYmVsIGNsYXNzTmFtZT1cImxhYmVsLWZvcm1cIj5cclxuICAgICAgICAgICAgICB7dChcImNyZWF0ZUFydGljbGU6dmlzaWJpbGl0eVwiKX1cclxuXHJcbiAgICAgICAgICAgICAgPFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic2VsZWN0LXBlbnRhYmVsbFwiXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwic3RhbmRhcmRcIlxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e1Zpc2liaWxpdHkuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgICAob3B0aW9uKSA9PiB2YWx1ZXMudmlzaWJpbGl0eUZSID09PSBvcHRpb25cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICBzZWxlY3RlZD17dmFsdWVzLnZpc2liaWxpdHlGUn1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZXZlbnQpID0+IHtcclxuICAgICAgICAgICAgICAgICAgc2V0RmllbGRWYWx1ZShcInZpc2liaWxpdHlGUlwiLCBldmVudC50YXJnZXQudmFsdWUpO1xyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7VmlzaWJpbGl0eS5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgIDxNZW51SXRlbSBrZXk9e2luZGV4fSB2YWx1ZT17aXRlbX0+XHJcbiAgICAgICAgICAgICAgICAgICAge2l0ZW19XHJcbiAgICAgICAgICAgICAgICAgIDwvTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICA8L1NlbGVjdD5cclxuICAgICAgICAgICAgICA8RXJyb3JNZXNzYWdlXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJsYWJlbC1lcnJvclwiXHJcbiAgICAgICAgICAgICAgICBuYW1lPVwidmlzaWJpbGl0eUVOXCJcclxuICAgICAgICAgICAgICAgIGNvbXBvbmVudD1cImRpdlwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9Gb3JtTGFiZWw+XHJcbiAgICAgICAgICA8L0Zvcm1Hcm91cD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPEZvcm1Hcm91cD5cclxuICAgICAgICAgICAgPEZvcm1MYWJlbCBjbGFzc05hbWU9XCJsYWJlbC1mb3JtXCI+XHJcbiAgICAgICAgICAgICAge3QoXCJjcmVhdGVBcnRpY2xlOmtleXdvcmRcIil9XHJcbiAgICAgICAgICAgICAgPGRpdiBpZD1cInRhZ3NcIj5cclxuICAgICAgICAgICAgICAgIDxSZWFjdFRhZ3NcclxuICAgICAgICAgICAgICAgICAgdGFncz17dGFnc31cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtcclxuICAgICAgICAgICAgICAgICAgICBcImlucHV0LXBlbnRhYmVsbFwiICtcclxuICAgICAgICAgICAgICAgICAgICAoZXJyb3JzLmtleXdvcmRzRlIgJiYgdG91Y2hlZC5rZXl3b3Jkc0ZSXHJcbiAgICAgICAgICAgICAgICAgICAgICA/IFwiIGlzLWludmFsaWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgOiBcIlwiKVxyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIGRlbGltaXRlcnM9e2RlbGltaXRlcnN9XHJcbiAgICAgICAgICAgICAgICAgIGhhbmRsZURlbGV0ZT17KGkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkVGFncyA9IHRhZ3MuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgICAgICAgKHRhZywgaW5kZXgpID0+IGluZGV4ICE9PSBpXHJcbiAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRUYWdzKHVwZGF0ZWRUYWdzKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRGaWVsZFZhbHVlKFxyXG4gICAgICAgICAgICAgICAgICAgICAgXCJrZXl3b3Jkc0ZSXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVkVGFncy5tYXAoKHRhZykgPT4gdGFnLnRleHQpXHJcbiAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgaGFuZGxlQWRkaXRpb249eyh0YWcpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBzZXRUYWdzKFsuLi50YWdzLCB0YWddKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRGaWVsZFZhbHVlKFxyXG4gICAgICAgICAgICAgICAgICAgICAgXCJrZXl3b3Jkc0ZSXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICBbLi4udGFncywgdGFnXS5tYXAoKGl0ZW0pID0+IGl0ZW0udGV4dClcclxuICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICBpbnB1dEZpZWxkUG9zaXRpb249XCJib3R0b21cIlxyXG4gICAgICAgICAgICAgICAgICBhdXRvY29tcGxldGVcclxuICAgICAgICAgICAgICAgICAgYWxsb3dEcmFnRHJvcD17ZmFsc2V9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxFcnJvck1lc3NhZ2VcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxhYmVsLWVycm9yXCJcclxuICAgICAgICAgICAgICAgIG5hbWU9XCJrZXl3b3Jkc0ZSXCJcclxuICAgICAgICAgICAgICAgIGNvbXBvbmVudD1cImRpdlwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9Gb3JtTGFiZWw+XHJcbiAgICAgICAgICA8L0Zvcm1Hcm91cD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJsYWJlbC1mb3JtXCI+XHJcbiAgICAgICAgPEZpZWxkXHJcbiAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxyXG4gICAgICAgICAgbmFtZT1cInB1Ymxpc2hOb3dcIlxyXG4gICAgICAgICAgY2hlY2tlZD17cHVibGlzaE5vd31cclxuICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICBzZXRQdWJsaXNoTm93KGUudGFyZ2V0LmNoZWNrZWQpO1xyXG4gICAgICAgICAgICBpZiAoZS50YXJnZXQuY2hlY2tlZCkge1xyXG4gICAgICAgICAgICAgIHNldEZpZWxkVmFsdWUoXCJwdWJsaXNoRGF0ZUZSXCIsIG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH19XHJcbiAgICAgICAgLz5cclxuICAgICAgICB7dChcImNyZWF0ZUFydGljbGU6cHVibGlzaE5vd1wiKX1cclxuICAgICAgPC9sYWJlbD5cclxuXHJcbiAgICAgIHshcHVibGlzaE5vdyAmJiAoXHJcbiAgICAgICAgPGRpdj5cclxuICAgICAgICAgIDxGb3JtR3JvdXA+XHJcbiAgICAgICAgICAgIDxGb3JtTGFiZWwgY2xhc3NOYW1lPVwibGFiZWwtZm9ybVwiPlxyXG4gICAgICAgICAgICAgIHt0KFwiY3JlYXRlQXJ0aWNsZTpwdWJsaXNoRGF0ZVwiKX1cclxuICAgICAgICAgICAgICA8TG9jYWxpemF0aW9uUHJvdmlkZXIgZGF0ZUFkYXB0ZXI9e0FkYXB0ZXJEYXlqc30+XHJcbiAgICAgICAgICAgICAgICA8RGVtb0NvbnRhaW5lciBjb21wb25lbnRzPXtbXCJEYXRlUGlja2VyXCJdfT5cclxuICAgICAgICAgICAgICAgICAgPERhdGVQaWNrZXJcclxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwic3RhbmRhcmRcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LWRhdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgIGZvcm1hdD1cIkREL01NL1lZWVlcIlxyXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtkYXlqcyh2YWx1ZXMucHVibGlzaERhdGVFTil9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhkYXRlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBzZXRGaWVsZFZhbHVlKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBcInB1Ymxpc2hEYXRlRU5cIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgZGF5anMoZGF0ZSkuZm9ybWF0KFwiWVlZWS1NTS1ERFwiKVxyXG4gICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAvPntcIiBcIn1cclxuICAgICAgICAgICAgICAgICAgPEVycm9yTWVzc2FnZVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxhYmVsLWVycm9yXCJcclxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwicHVibGlzaERhdGVFTlwiXHJcbiAgICAgICAgICAgICAgICAgICAgY29tcG9uZW50PVwiZGl2XCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvRGVtb0NvbnRhaW5lcj5cclxuICAgICAgICAgICAgICA8L0xvY2FsaXphdGlvblByb3ZpZGVyPlxyXG4gICAgICAgICAgICA8L0Zvcm1MYWJlbD5cclxuICAgICAgICAgIDwvRm9ybUdyb3VwPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG5cclxuICAgICAgPEZpZWxkXHJcbiAgICAgICAgdHlwZT1cImhpZGRlblwiXHJcbiAgICAgICAgbmFtZT1cInB1Ymxpc2hEYXRlRlJcIlxyXG4gICAgICAgIHZhbHVlPXtcclxuICAgICAgICAgIHB1Ymxpc2hOb3cgPyBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkgOiBwdWJsaXNoRGF0ZS50b0lTT1N0cmluZygpXHJcbiAgICAgICAgfVxyXG4gICAgICAvPlxyXG4gICAgPC8+XHJcbiAgKTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgQWRkQXJ0aWNsZUZSO1xyXG4iXSwibmFtZXMiOlsiRXJyb3JNZXNzYWdlIiwiRmllbGQiLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VTdGF0ZSIsInVzZVRyYW5zbGF0aW9uIiwiU3VuRWRpdG9yIiwidjQiLCJ1dWlkdjQiLCJWaXNpYmlsaXR5Iiwic2x1ZyIsIkFQSV9VUkxTIiwidXBsb2FkIiwiV2l0aENvbnRleHQiLCJSZWFjdFRhZ3MiLCJwbHVnaW5zIiwiQXV0b2NvbXBsZXRlIiwiRm9ybUdyb3VwIiwiRm9ybUxhYmVsIiwiTWVudUl0ZW0iLCJTZWxlY3QiLCJTdGFjayIsIlRleHRGaWVsZCIsIkRhdGVQaWNrZXIiLCJMb2NhbGl6YXRpb25Qcm92aWRlciIsIkRlbW9Db250YWluZXIiLCJkYXlqcyIsIkFkYXB0ZXJEYXlqcyIsInVzZVNhdmVGaWxlIiwidXNlQ3VycmVudFVzZXIiLCJBZGRBcnRpY2xlRlIiLCJlcnJvcnMiLCJ0b3VjaGVkIiwic2V0RmllbGRWYWx1ZSIsInZhbHVlcyIsIm9uSW1hZ2VTZWxlY3QiLCJjYXRlZ29yaWVzIiwiZmlsdGVyZWRDYXRlZ29yaWVzIiwib25DYXRlZ29yaWVzU2VsZWN0IiwiZGVib3VuY2UiLCJoYW5kbGVQYXN0ZSIsImV2ZW50IiwiY2xlYW5EYXRhIiwibWF4Q2hhckNvdW50IiwiaHRtbCIsInJlcGxhY2UiLCJLZXlDb2RlcyIsImNvbW1hIiwiZW50ZXIiLCJkZWxpbWl0ZXJzIiwidGFncyIsInNldFRhZ3MiLCJoaWdobGlnaHRzIiwic2V0SGlnaGxpZ2h0cyIsInVzZXIiLCJ0IiwicHVibGlzaE5vdyIsInNldFB1Ymxpc2hOb3ciLCJwdWJsaXNoRGF0ZSIsInNldFB1Ymxpc2hEYXRlIiwiRGF0ZSIsImltYWdlSW5wdXRSZWYiLCJzZWxlY3RlZEltYWdlIiwic2V0U2VsZWN0ZWRJbWFnZSIsImxhbmd1YWdlIiwiaGFuZGxlUGhvdG9DaGFuZ2UiLCJzZWxlY3RlZEZpbGUiLCJjdXJyZW50IiwiZmlsZXMiLCJ1c2VTYXZlRmlsZUhvb2siLCJoYW5kbGVQaG90b0Jsb2dDaGFuZ2UiLCJmaWxlIiwiaW5mbyIsImNvcmUiLCJ1cGxvYWRIYW5kbGVyIiwiSFRNTEltYWdlRWxlbWVudCIsInNyYyIsInN0YXJ0c1dpdGgiLCJiYXNlNjREYXRhIiwic3BsaXQiLCJjb250ZW50VHlwZSIsIm1hdGNoIiwiYnl0ZUNoYXJhY3RlcnMiLCJhdG9iIiwiYnl0ZU51bWJlcnMiLCJBcnJheSIsImxlbmd0aCIsImZpbGwiLCJtYXAiLCJfIiwiaSIsImNoYXJDb2RlQXQiLCJieXRlQXJyYXkiLCJVaW50OEFycmF5IiwiYmxvYiIsIkJsb2IiLCJ0eXBlIiwiZmlsZU5hbWUiLCJub3ciLCJGaWxlIiwidXBsb2FkRmlsZSIsImZldGNoIiwidGhlbiIsInJlc3BvbnNlIiwiY2F0Y2giLCJlcnJvciIsImNvbnNvbGUiLCJvcmlnaW5hbEltYWdlIiwidXVpZFBob3RvIiwiZm9ybURhdGEiLCJGb3JtRGF0YSIsImFwcGVuZCIsImV4dGVuc2lvbiIsIm5hbWUiLCJwb3AiLCJjdXJyZW50WWVhciIsImdldEZ1bGxZZWFyIiwibXV0YXRlIiwicmVzb3VyY2UiLCJmb2xkZXIiLCJ0b1N0cmluZyIsImZpbGVuYW1lIiwiYm9keSIsIm9uU3VjY2VzcyIsImRhdGFVVUlEIiwidXVpZFBob3RvRmlsZU5hbWUiLCJtZXNzYWdlIiwidXVpZCIsImltYWdlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0JBU0VfQVBJX1VSTCIsInJlc3VsdCIsImlkIiwidXJsIiwib25FcnJvciIsInRpdGxlZnIiLCJzZXRUaXRsZWZyIiwic2F2ZWRUaXRsZWZyIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkpTT04iLCJwYXJzZSIsIm1ldGF0aXRsZWZyIiwic2V0TWV0YXRpdGxlZnIiLCJzYXZlZE1ldGF0aXRsZWZyIiwibWV0YURlc2NyaXB0aW9uZnIiLCJzZXRNZXRhRGVzY3JpcHRpb25mciIsInNhdmVkTWV0YWRlc2NyaXB0aW9uZnIiLCJjb250ZW50ZnIiLCJzZXRDb250ZW50ZnIiLCJzYXZlZENvbnRlbnRmciIsImhhbmRsZUVkaXRvckNoYW5nZSIsIm5ld0NvbnRlbnRmciIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJwIiwiY2xhc3NOYW1lIiwiZGl2IiwidmFyaWFudCIsInZhbHVlIiwidGl0bGVGUiIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsImNvbXBvbmVudCIsIm11bHRpcGxlIiwib3B0aW9ucyIsImdldE9wdGlvbkxhYmVsIiwib3B0aW9uIiwic2VsZWN0ZWQiLCJjYXRlZ29yeUZSIiwiZmlsdGVyIiwiY2F0ZWdvcnkiLCJpbmNsdWRlcyIsInNlbGVjdGVkT3B0aW9ucyIsImNhdGVnb3J5SWRzIiwicmVuZGVySW5wdXQiLCJwYXJhbXMiLCJtdWx0aWxpbmUiLCJyb3dzIiwiZGVzY3JpcHRpb25GUiIsImhpZ2hsaWdodHNGUiIsImhhbmRsZURlbGV0ZSIsInVwZGF0ZWRUYWdzIiwidGFnIiwiaW5kZXgiLCJ0ZXh0IiwiaGFuZGxlQWRkaXRpb24iLCJpdGVtIiwiaW5wdXRGaWVsZFBvc2l0aW9uIiwiYXV0b2NvbXBsZXRlIiwiYWxsb3dEcmFnRHJvcCIsInNldENvbnRlbnRzIiwiY29udGVudEZSIiwib25QYXN0ZSIsInNldE9wdGlvbnMiLCJjbGVhbkhUTUwiLCJkaXNhYmxlSHRtbFNhbml0aXplciIsImFkZFRhZ3NXaGl0ZWxpc3QiLCJidXR0b25MaXN0IiwiaW1hZ2VVcGxvYWRIYW5kbGVyIiwiZGVmYXVsdFRhZyIsIm1pbkhlaWdodCIsIm1heEhlaWdodCIsInNob3dQYXRoTGFiZWwiLCJmb250IiwiY2hhckNvdW50ZXIiLCJjaGFyQ291bnRlclR5cGUiLCJyZXNpemluZ0JhciIsImNvbG9yTGlzdCIsIm9uSW1hZ2VVcGxvYWQiLCJiciIsIkZhcVNlY3Rpb24iLCJzcGFuIiwibWV0YVRpdGxlRlIiLCJ1cmxGUiIsIm1ldGFEZXNjcmlwdGlvbkZSIiwibGFiZWwiLCJodG1sRm9yIiwiaW5wdXQiLCJhY2NlcHQiLCJyZWYiLCJpbWFnZUZSIiwic3R5bGUiLCJiYWNrZ3JvdW5kSW1hZ2UiLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJSRUFDVF9BUFBfQVBJX1VSTCIsImJhY2tncm91bmRTaXplIiwiYmFja2dyb3VuZFJlcGVhdCIsImJhY2tncm91bmRQb3NpdGlvbiIsImFsdEZSIiwidmlzaWJpbGl0eUZSIiwia2V5d29yZHNGUiIsImNoZWNrZWQiLCJ0b0lTT1N0cmluZyIsImRhdGVBZGFwdGVyIiwiY29tcG9uZW50cyIsImZvcm1hdCIsInB1Ymxpc2hEYXRlRU4iLCJkYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx\n"));

/***/ })

});