"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/statistics/page",{

/***/ "(app-pages-browser)/./src/features/stats/ResumesChart.jsx":
/*!*********************************************!*\
  !*** ./src/features/stats/ResumesChart.jsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ResumesChart; },\n/* harmony export */   valueFormatter: function() { return /* binding */ valueFormatter; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Card,CardContent,Grid,MenuItem,Select,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _stats_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stats.hooks */ \"(app-pages-browser)/./src/features/stats/stats.hooks.jsx\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon.svg\");\n/* harmony import */ var _components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../components/charts/CustomMultiBarchart */ \"(app-pages-browser)/./src/components/charts/CustomMultiBarchart.jsx\");\n/* harmony import */ var _components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../components/charts/CustomPieChart */ \"(app-pages-browser)/./src/components/charts/CustomPieChart.jsx\");\n/* harmony import */ var _blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../blog/hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _charts_CommentByCategory__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./charts/CommentByCategory */ \"(app-pages-browser)/./src/features/stats/charts/CommentByCategory.jsx\");\n/* harmony import */ var _charts_UsersActivities__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./charts/UsersActivities */ \"(app-pages-browser)/./src/features/stats/charts/UsersActivities.jsx\");\n/* __next_internal_client_entry_do_not_use__ valueFormatter,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction valueFormatter(value) {\n    return `${value}`;\n}\nfunction ResumesChart() {\n    _s();\n    const getCategoriesLangEN = (0,_blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"en\");\n    const transformedCategories = getCategoriesLangEN?.data?.categories?.map((category)=>({\n            id: category.versionscategory[0]?.id,\n            name: category.versionscategory[0]?.name\n        })) || [];\n    /// filter user Activity ///\n    const [dateFromUser, setDateFromUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToUser, setDateToUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchUser, setSearchUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchActivity = ()=>{\n        setDateToActivity(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromActivity(\"2024-09-01\");\n        setSearchActivity(!searchActivity);\n    };\n    /// filter platform activity ///\n    const [dateFromPlatform, setDateFromPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToPlatform, setDateToPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchPlatform, setSearchPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchPlatform = ()=>{\n        setDateToPlatform(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromPlatform(\"2024-09-01\");\n        setSearchPlatform(!searchPlatform);\n    };\n    /// application filter pie chart ///\n    const [dateFromApplication, setDateFromApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToApplication, setDateToApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchApplication, setSearchApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchApplication = ()=>{\n        setDateToApplication(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromApplication(\"2024-09-01\");\n        setSearchApplication(!searchApplication);\n    };\n    /// article filter pie chart ///\n    const [dateFromArticle, setDateFromArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToArticle, setDateToArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchArticle, setSearchArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchArticles = ()=>{\n        setDateToArticle(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromArticle(\"2024-09-01\");\n        setFilteredCategories([]);\n        setApprove(true);\n        setCategories([]);\n        setSearchArticle(!searchArticle);\n    };\n    /// comment filter pie chart ///\n    const [filteredCategories, setFilteredCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [approve, setApprove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dateFromComment, setDateFromComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToComment, setDateToComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [searchComment, setSearchComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchComments = ()=>{\n        setDateToComment(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromComment(\"2024-09-01\");\n        setFilteredCategories([]);\n        setApprove(true);\n        setCategories([]);\n        setSearchComment(!searchComment);\n    };\n    /// opportunity filter pie chart ////\n    const [dateFromOpportunity, setDateFromOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"2024-09-01\");\n    const [dateToOpportunity, setDateToOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        return today.toISOString().split(\"T\")[0];\n    });\n    const [opportunityType, setOpportunityType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [industry, setIndustry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchOpportunity, setSearchOpportunity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const resetSearchOpportunity = ()=>{\n        setDateToOpportunity(()=>{\n            const today = new Date();\n            return today.toISOString().split(\"T\")[0];\n        });\n        setDateFromOpportunity(\"2024-09-01\");\n        setOpportunityType(\"\");\n        setIndustry(\"\");\n        setSearchOpportunity(!searchOpportunity);\n    };\n    const getDataPieComments = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetCommentsStat)({\n        dateFrom: dateFromComment,\n        dateTo: dateToComment,\n        approve: approve,\n        categories: filteredCategories\n    });\n    const getDataPieArticles = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetArticlesStat)({\n        dateFrom: dateFromArticle,\n        dateTo: dateToArticle,\n        barChart: null\n    });\n    const getDataPieOpportunities = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetOpportunitiesStat)({\n        dateFrom: dateFromOpportunity,\n        dateTo: dateToOpportunity,\n        opportunityType,\n        industry,\n        barChart: null\n    });\n    const getDAtaPieApplications = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetApplicationsStat)({\n        dateFrom: dateFromApplication,\n        dateTo: dateToApplication,\n        barChart: null\n    });\n    const getDataUserActivity = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetUserStat)({\n        dateFrom: dateFromUser,\n        dateTo: dateToUser\n    });\n    const getDataPlatforActivity = (0,_stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetPlatformStat)({\n        dateFrom: dateFromPlatform,\n        dateTo: dateToPlatform\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataUserActivity.refetch();\n    }, [\n        searchUser\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPlatforActivity.refetch();\n    }, [\n        searchPlatform\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieOpportunities.refetch();\n    }, [\n        searchOpportunity\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDAtaPieApplications.refetch();\n    }, [\n        searchApplication\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieArticles.refetch();\n    }, [\n        searchArticle\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getDataPieComments.refetch();\n    }, [\n        searchComment\n    ]);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const isTablet = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(theme.breakpoints.down(\"md\"));\n    const chartSettings1 = {\n        width: isMobile ? 290 : isTablet ? 500 : 580,\n        height: 250,\n        layout: \"vertical\"\n    };\n    if (getDataUserActivity.isLoading || getDataPlatforActivity.isLoading || getDataPieArticles.isLoading || getDataPieOpportunities.isLoading || getDAtaPieApplications.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n            lineNumber: 243,\n            columnNumber: 12\n        }, this);\n    }\n    const pieCharts = [\n        {\n            title: t(\"statsDash:applicationsByStatus\"),\n            dataset: getDAtaPieApplications?.data?.map((app)=>({\n                    label: app.status,\n                    value: app.totalApplications\n                })),\n            colors: [\n                \"#E97611\",\n                \"#018055\",\n                \"#D73232\"\n            ]\n        },\n        {\n            title: t(\"statsDash:articlesByVisibility\"),\n            dataset: getDataPieArticles?.data?.map((article)=>({\n                    label: article.visibility,\n                    value: article.totalArticles\n                })),\n            colors: [\n                \"#234791\",\n                \"#FFCA00\",\n                \"#006A67\"\n            ]\n        },\n        {\n            title: t(\"statsDash:opportunities\"),\n            dataset: getDataPieOpportunities?.data?.map((opp)=>({\n                    label: opp.visibility,\n                    value: opp.totalOpportunities\n                })),\n            colors: [\n                \"#234791\",\n                \"#D5E5FF\",\n                \"#227B94\"\n            ]\n        },\n        {\n            title: t(\"statsDash:commentsByCategory\"),\n            dataset: getDataPieComments?.data?.map((comment)=>({\n                    label: comment.category,\n                    value: comment.total\n                })) || [],\n            colors: [\n                \"#673ab7\",\n                \"#009688\",\n                \"#8bc34a\",\n                \"#ffc107\",\n                \"#ff9800\",\n                \"#ffc107\",\n                \"#3f51b5\",\n                \"#009688\",\n                \"#4caf50\",\n                \"#03a9f4\",\n                \"#ff9800\",\n                \"#8bc34a\",\n                \"#673ab7\"\n            ]\n        }\n    ];\n    const userAactivity = {\n        title: t(\"statsDash:usersActivities\"),\n        dataKey: [\n            \"login\",\n            \"register\",\n            \"resumes\",\n            \"applications\"\n        ],\n        dataset: getDataUserActivity?.data,\n        color: [\n            \"#30B0C7\",\n            \"#234791\",\n            \"#007AFF\",\n            \"#32ADE6\"\n        ]\n    };\n    const platformAactivity = {\n        title: t(\"statsDash:platformActivity\"),\n        dataKey: [\n            \"opportunities\",\n            \"articles\",\n            \"newsletters\",\n            \"contacts\"\n        ],\n        dataset: getDataPlatforActivity?.data,\n        color: [\n            \"#FFCC00\",\n            \"#FFA135\",\n            \"#FFD985\",\n            \"#FF7700\"\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"menu:statistics\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"stats\",\n                className: \"div-wrapper\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    container: true,\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_CommentByCategory__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                transformedCategories: transformedCategories,\n                                dateFromComment: dateFromComment,\n                                dateToComment: dateToComment,\n                                approve: approve,\n                                categories: categories,\n                                setCategories: setCategories,\n                                setFilteredCategories: setFilteredCategories,\n                                setSearchComment: setSearchComment,\n                                searchComment: searchComment,\n                                resetSearchComments: resetSearchComments,\n                                pieCharts: pieCharts,\n                                setApprove: setApprove,\n                                setDateFromComment: setDateFromComment,\n                                setDateToComment: setDateToComment\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_UsersActivities__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                dateFromUser: dateFromUser,\n                                dateToUser: dateToUser,\n                                searchUser: searchUser,\n                                setSearchUser: setSearchUser,\n                                resetSearchActivity: resetSearchActivity,\n                                userAactivity: userAactivity,\n                                chartSettings1: chartSettings1\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"heading-h3\",\n                                            gutterBottom: true,\n                                            children: pieCharts[0].title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            elevation: 0,\n                                            disableGutters: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    \"aria-controls\": \"panel1bh-content\",\n                                                    id: \"panel1bh-header\",\n                                                    className: \"svg-accordion\",\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 33\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"label-pentabell\",\n                                                        children: t(\"statsDash:filters\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    elevation: 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        container: true,\n                                                        className: \"chart-grid\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    label: t(\"statsDash:fromDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateFromApplication,\n                                                                    onChange: (e)=>setDateFromApplication(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    label: t(\"statsDash:toDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateToApplication,\n                                                                    onChange: (e)=>setDateToApplication(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 3,\n                                                                sm: 1,\n                                                                md: 4,\n                                                                className: \"btns-filter dashboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                                                    onClick: resetSearchApplication\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 11,\n                                                                sm: 11,\n                                                                md: 8,\n                                                                children: [\n                                                                    \" \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        text: t(\"statsDash:filter\"),\n                                                                        onClick: ()=>{\n                                                                            setSearchApplication(!searchApplication);\n                                                                        },\n                                                                        className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    donuts: true,\n                                                    chart: pieCharts[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                pieCharts[0].dataset?.some((item)=>item[\"value\"] > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"accepted-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:accepted\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"rejected-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:rejected\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"pending-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:pending\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"heading-h3\",\n                                            gutterBottom: true,\n                                            children: pieCharts[2].title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            elevation: 0,\n                                            disableGutters: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    \"aria-controls\": \"panel1bh-content\",\n                                                    id: \"panel1bh-header\",\n                                                    className: \"svg-accordion\",\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 33\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"label-pentabell\",\n                                                        children: t(\"statsDash:filters\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    elevation: 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        container: true,\n                                                        className: \"chart-grid\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"blue-text\",\n                                                                        children: [\n                                                                            t(\"statsDash:type\"),\n                                                                            \" :\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"select-pentabell blue-text\",\n                                                                        value: opportunityType || \"\",\n                                                                        defaultValue: \"\",\n                                                                        onChange: (event)=>setOpportunityType(event.target.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                selected: true,\n                                                                                disabled: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                                                    children: t(\"statsDash:opportunityType\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 482,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 476,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                children: t(\"statsDash:all\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 484,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            _utils_constants__WEBPACK_IMPORTED_MODULE_4__.OpportunityType.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"blue-text\",\n                                                                                    value: item,\n                                                                                    children: item\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 488,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                md: 6,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"blue-text\",\n                                                                        children: [\n                                                                            t(\"statsDash:industry\"),\n                                                                            \" :\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 499,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"select-pentabell blue-text\",\n                                                                        value: industry || \"\",\n                                                                        onChange: (event)=>setIndustry(event.target.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                selected: true,\n                                                                                disabled: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                                                    children: t(\"statsDash:industry\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 513,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 507,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"blue-text\",\n                                                                                value: \"\",\n                                                                                children: t(\"statsDash:all\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                lineNumber: 515,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            _utils_constants__WEBPACK_IMPORTED_MODULE_4__.Industry.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"blue-text\",\n                                                                                    value: item,\n                                                                                    children: item\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                                    lineNumber: 519,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    label: t(\"statsDash:fromDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateFromOpportunity,\n                                                                    onChange: (e)=>setDateFromOpportunity(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    label: t(\"statsDash:toDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateToOpportunity,\n                                                                    onChange: (e)=>setDateToOpportunity(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 3,\n                                                                sm: 1,\n                                                                md: 4,\n                                                                className: \"btns-filter dashboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                                                    onClick: resetSearchOpportunity\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 11,\n                                                                sm: 11,\n                                                                md: 8,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    text: t(\"statsDash:filter\"),\n                                                                    onClick: ()=>{\n                                                                        setSearchOpportunity(!searchOpportunity);\n                                                                    },\n                                                                    className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    donuts: false,\n                                                    chart: pieCharts[2]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                pieCharts[2].dataset?.some((item)=>item[\"value\"] > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"public-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 584,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:public\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"privateopportunity-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:private\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"draft-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 596,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:draft\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"barchartfilter-wrapper\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                container: true,\n                                                className: \"chart-grid\",\n                                                spacing: 1,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 12,\n                                                        md: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"heading-h3\",\n                                                            gutterBottom: true,\n                                                            children: platformAactivity.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 6,\n                                                        md: 2.5,\n                                                        xl: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            label: t(\"statsDash:fromDate\"),\n                                                            type: \"date\",\n                                                            value: dateFromPlatform,\n                                                            onChange: (e)=>setDateFromPlatform(e.target.value),\n                                                            fullWidth: true,\n                                                            InputLabelProps: {\n                                                                shrink: true\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        item: true,\n                                                        xs: 12,\n                                                        sm: 6,\n                                                        md: 2.5,\n                                                        xl: 3,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            label: t(\"statsDash:toDate\"),\n                                                            type: \"date\",\n                                                            value: dateToPlatform,\n                                                            onChange: (e)=>setDateToPlatform(e.target.value),\n                                                            fullWidth: true,\n                                                            InputLabelProps: {\n                                                                shrink: true\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        item: true,\n                                                        xs: 2,\n                                                        sm: 1,\n                                                        md: 1.5,\n                                                        xl: 1,\n                                                        className: \"btns-filter dashboard\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            className: \"btn btn-outlined btn-refresh full-width\",\n                                                            onClick: resetSearchPlatform\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        item: true,\n                                                        xs: 10,\n                                                        sm: 11,\n                                                        md: 2.5,\n                                                        xl: 2,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            text: t(\"statsDash:filter\"),\n                                                            onClick: ()=>{\n                                                                setSearchPlatform(!searchPlatform);\n                                                            },\n                                                            className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 653,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                platformAactivity.dataset?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newopportunities-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newOpportunities\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"neswarticles-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newArticles\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 676,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newsletters-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newslettersSubscriptions\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 682,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"newcontact-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:newContacts\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomMultiBarchart__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    chart: platformAactivity,\n                                                    chartSettings: chartSettings1\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 608,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 607,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"heading-h3\",\n                                            gutterBottom: true,\n                                            children: pieCharts[1].title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            elevation: 0,\n                                            disableGutters: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    \"aria-controls\": \"panel1bh-content\",\n                                                    id: \"panel1bh-header\",\n                                                    className: \"svg-accordion\",\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 33\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"label-pentabell\",\n                                                        children: t(\"statsDash:filters\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    elevation: 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        container: true,\n                                                        className: \"chart-grid\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    label: t(\"statsDash:fromDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateFromArticle,\n                                                                    onChange: (e)=>setDateFromArticle(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 723,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 722,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 12,\n                                                                sm: 6,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    label: t(\"statsDash:toDate\"),\n                                                                    type: \"date\",\n                                                                    value: dateToArticle,\n                                                                    onChange: (e)=>setDateToArticle(e.target.value),\n                                                                    fullWidth: true,\n                                                                    InputLabelProps: {\n                                                                        shrink: true\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 733,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 3,\n                                                                sm: 1,\n                                                                md: 4,\n                                                                className: \"btns-filter dashboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                        lineNumber: 750,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    className: \"btn btn-outlined btn-refresh full-width\",\n                                                                    onClick: resetSearchArticles\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 749,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Card_CardContent_Grid_MenuItem_Select_TextField_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                item: true,\n                                                                xs: 11,\n                                                                sm: 11,\n                                                                md: 8,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    text: t(\"statsDash:filter\"),\n                                                                    onClick: ()=>{\n                                                                        setSearchArticle(!searchArticle);\n                                                                    },\n                                                                    className: \"btn btn-outlined btn-filter-stat full-width\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 756,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 720,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"chart-wrapper\",\n                                            children: [\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CustomPieChart__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    donuts: false,\n                                                    chart: pieCharts[1]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                pieCharts[1].dataset?.some((item)=>item[\"value\"] > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"labelstats-wrapper\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"public-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 775,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:public\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"privatearticles-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 781,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:private\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 782,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"label-wrapper\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"draft-dot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 787,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"label-chart\",\n                                                                    children: t(\"statsDash:draft\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                    lineNumber: 705,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                                lineNumber: 704,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                            lineNumber: 703,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                    lineNumber: 314,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\stats\\\\ResumesChart.jsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ResumesChart, \"5avvH6cEosRudI6naxYJ3BUU5WU=\", false, function() {\n    return [\n        _blog_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetCommentsStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetArticlesStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetOpportunitiesStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetApplicationsStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetUserStat,\n        _stats_hooks__WEBPACK_IMPORTED_MODULE_3__.useGetPlatformStat,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    ];\n});\n_c = ResumesChart;\nvar _c;\n$RefreshReg$(_c, \"ResumesChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/stats/ResumesChart.jsx\n"));

/***/ })

});