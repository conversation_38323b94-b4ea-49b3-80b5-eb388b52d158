(()=>{var e={};e.id=4889,e.ids=[4889],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},6005:e=>{"use strict";e.exports=require("node:crypto")},94474:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a,i=s(95746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let n=e=>i.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none"},e),a||(a=i.createElement("path",{fill:"#798BA3",d:"M7.999 14.666a6.667 6.667 0 1 1 0-13.333 6.667 6.667 0 0 1 0 13.333m0-1.333a5.333 5.333 0 1 0 0-10.667 5.333 5.333 0 0 0 0 10.667M8.665 8h2.667v1.333h-4V4.666h1.333z"})))},77394:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var a,i,r,n=s(95746);function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(null,arguments)}let l=e=>n.createElement("svg",o({xmlns:"http://www.w3.org/2000/svg",width:48,height:49,fill:"none"},e),a||(a=n.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M24 25a1.5 1.5 0 0 1 1.5 1.5v16a1.5 1.5 0 0 1-3 0v-16A1.5 1.5 0 0 1 24 25",clipRule:"evenodd"})),i||(i=n.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M22.573 8.869A12.5 12.5 0 0 0 9.072 29.247a1.5 1.5 0 1 1-2.144 2.099 15.5 15.5 0 1 1 25.566-16.347H35a10.501 10.501 0 0 1 5.834 19.23 1.5 1.5 0 1 1-1.666-2.494A7.5 7.5 0 0 0 35 18h-3.58a1.5 1.5 0 0 1-1.438-1.071 12.5 12.5 0 0 0-7.409-8.06",clipRule:"evenodd"})),r||(r=n.createElement("path",{fill:"#FFCA00",fillRule:"evenodd",d:"M22.94 25.44a1.5 1.5 0 0 1 2.12 0l8 8a1.5 1.5 0 0 1-2.12 2.12L24 28.622l-6.94 6.94a1.5 1.5 0 0 1-2.12-2.122z",clipRule:"evenodd"})))},23902:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c}),s(20360),s(30962),s(23658),s(54864);var a=s(23191),i=s(88716),r=s(37922),n=s.n(r),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["[locale]",{children:["(website)",{children:["join-us",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,20360)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\join-us\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,30962)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\layout.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,23658)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54864)),"C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\not-found.js"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Desktop\\pentabellversion2.0\\client\\src\\app\\[locale]\\(website)\\join-us\\page.jsx"],d="/[locale]/(website)/join-us/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/[locale]/(website)/join-us/page",pathname:"/[locale]/join-us",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},45125:(e,t,s)=>{Promise.resolve().then(s.bind(s,90423)),Promise.resolve().then(s.bind(s,73207)),Promise.resolve().then(s.bind(s,62159)),Promise.resolve().then(s.bind(s,91147)),Promise.resolve().then(s.bind(s,9675)),Promise.resolve().then(s.bind(s,3022)),Promise.resolve().then(s.bind(s,17449)),Promise.resolve().then(s.bind(s,15082)),Promise.resolve().then(s.bind(s,58554)),Promise.resolve().then(s.bind(s,91345))},36690:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});var a=s(27522),i=s(10326);let r=(0,a.Z)((0,i.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},10163:(e,t,s)=>{"use strict";s.d(t,{Z:()=>g});var a=s(17577),i=s(41135),r=s(88634),n=s(91703),o=s(2791),l=s(71685),c=s(97898);function u(e){return(0,c.ZP)("MuiDialogActions",e)}(0,l.Z)("MuiDialogActions",["root","spacing"]);var d=s(10326);let m=e=>{let{classes:t,disableSpacing:s}=e;return(0,r.Z)({root:["root",!s&&"spacing"]},u,t)},p=(0,n.ZP)("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:s}=e;return[t.root,!s.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:e})=>!e.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),g=a.forwardRef(function(e,t){let s=(0,o.i)({props:e,name:"MuiDialogActions"}),{className:a,disableSpacing:r=!1,...n}=s,l={...s,disableSpacing:r},c=m(l);return(0,d.jsx)(p,{className:(0,i.Z)(c.root,a),ownerState:l,ref:t,...n})})},28591:(e,t,s)=>{"use strict";s.d(t,{Z:()=>j});var a=s(17577),i=s(41135),r=s(88634),n=s(91703),o=s(30990),l=s(2791),c=s(71685),u=s(97898);function d(e){return(0,u.ZP)("MuiDialogContent",e)}(0,c.Z)("MuiDialogContent",["root","dividers"]);var m=s(64650),p=s(10326);let g=e=>{let{classes:t,dividers:s}=e;return(0,r.Z)({root:["root",s&&"dividers"]},d,t)},h=(0,n.ZP)("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:s}=e;return[t.root,s.dividers&&t.dividers]}})((0,o.Z)(({theme:e})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:e})=>e.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(e.vars||e).palette.divider}`,borderBottom:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:e})=>!e.dividers,style:{[`.${m.Z.root} + &`]:{paddingTop:0}}}]}))),j=a.forwardRef(function(e,t){let s=(0,l.i)({props:e,name:"MuiDialogContent"}),{className:a,dividers:r=!1,...n}=s,o={...s,dividers:r},c=g(o);return(0,p.jsx)(h,{className:(0,i.Z)(c.root,a),ownerState:o,ref:t,...n})})},98117:(e,t,s)=>{"use strict";s.d(t,{Z:()=>g});var a=s(17577),i=s(41135),r=s(88634),n=s(25609),o=s(91703),l=s(2791),c=s(64650),u=s(55733),d=s(10326);let m=e=>{let{classes:t}=e;return(0,r.Z)({root:["root"]},c.a,t)},p=(0,o.ZP)(n.default,{name:"MuiDialogTitle",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:"16px 24px",flex:"0 0 auto"}),g=a.forwardRef(function(e,t){let s=(0,l.i)({props:e,name:"MuiDialogTitle"}),{className:r,id:n,...o}=s,c=m(s),{titleId:g=n}=a.useContext(u.Z);return(0,d.jsx)(p,{component:"h2",className:(0,i.Z)(c.root,r),ownerState:s,ref:t,variant:"h6",id:n??g,...o})})},64650:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n,a:()=>r});var a=s(71685),i=s(97898);function r(e){return(0,i.ZP)("MuiDialogTitle",e)}let n=(0,a.Z)("MuiDialogTitle",["root"])},38932:function(e,t,s){(function(e){"use strict";var t=/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?|janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,s=[/^janv/i,/^févr/i,/^mars/i,/^avr/i,/^mai/i,/^juin/i,/^juil/i,/^août/i,/^sept/i,/^oct/i,/^nov/i,/^déc/i];e.defineLocale("fr",{months:"janvier_f\xe9vrier_mars_avril_mai_juin_juillet_ao\xfbt_septembre_octobre_novembre_d\xe9cembre".split("_"),monthsShort:"janv._f\xe9vr._mars_avr._mai_juin_juil._ao\xfbt_sept._oct._nov._d\xe9c.".split("_"),monthsRegex:t,monthsShortRegex:t,monthsStrictRegex:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,monthsShortStrictRegex:/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?)/i,monthsParse:s,longMonthsParse:s,shortMonthsParse:s,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui \xe0] LT",nextDay:"[Demain \xe0] LT",nextWeek:"dddd [\xe0] LT",lastDay:"[Hier \xe0] LT",lastWeek:"dddd [dernier \xe0] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",w:"une semaine",ww:"%d semaines",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|)/,ordinal:function(e,t){switch(t){case"D":return e+(1===e?"er":"");default:case"M":case"Q":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}},week:{dow:1,doy:4}})})(s(57967))},62159:(e,t,s)=>{"use strict";s.d(t,{default:()=>u});var a=s(10326),i=s(90423),r=s(15082);let n={src:"/_next/static/media/team1.f6350403.png"},o={src:"/_next/static/media/team2.b9c289c9.png"};var l=s(52210);s(46226),s(97980);var c=s(90434);let u=function({bannerImg:e,height:t,altImg:s}){let{t:u}=(0,l.$G)();return(0,a.jsxs)("div",{id:"join-us-banner",className:"center-banner",style:{backgroundImage:`url(${e.src})`,height:t},children:[s&&a.jsx("img",{width:0,height:0,alt:s,src:"",style:{display:"none"},loading:"lazy"}),(0,a.jsxs)(i.default,{className:"top-section custom-max-width",children:[(0,a.jsxs)("h1",{className:"heading-h1 text-white",children:[u("joinUs:intro:title1"),a.jsx("br",{}),a.jsx("span",{className:"heading-h2 text-yellow",children:u("joinUs:intro:title2")})]}),a.jsx("p",{className:"sub-heading text-white  ",children:u("joinUs:intro:description")}),a.jsx(c.default,{href:"#service-page-form",style:{textDecoration:"none"},children:a.jsx(r.default,{text:u("joinUs:intro:button"),className:"btn btn-filled"})})]}),a.jsx("div",{className:"avatars",style:{backgroundImage:`url(${n.src})`}}),a.jsx("div",{className:"avatars last-one",style:{backgroundImage:`url(${o.src})`}})]})}},91147:(e,t,s)=>{"use strict";s.d(t,{default:()=>u});var a=s(10326),i=s(90423),r=s(52210);let n={src:"/_next/static/media/candJourneyImg1.01b88191.png"},o={src:"/_next/static/media/candJourneyImg2.cc40dace.png"},l={src:"/_next/static/media/candJourneyImg3.0162c59d.png"},c={src:"/_next/static/media/candJourneyImg4.cb445ed9.png"},u=function(){let{t:e}=(0,r.$G)();return a.jsx("div",{id:"candidate-journey-section",className:"light-bg",children:(0,a.jsxs)(i.default,{className:"custom-max-width",children:[a.jsx("h2",{className:"heading-h1 text-blue text-center",children:e("joinUs:candidateJourney:title")}),(0,a.jsxs)("div",{className:"support-reasons",children:[(0,a.jsxs)("div",{className:"support-reason",children:[a.jsx("div",{className:"img",children:a.jsx("img",{src:n.src,alt:"Candidate Journey 1",loading:"lazy"})}),a.jsx("p",{className:"support-reason-title",children:e("joinUs:candidateJourney:item1:title")}),a.jsx("p",{className:"support-reason-item",children:e("joinUs:candidateJourney:item1:description")})]}),(0,a.jsxs)("div",{className:"support-reason",children:[a.jsx("div",{className:"img",children:a.jsx("img",{width:303,height:103,src:o.src,alt:"Candidate Journey 2",loading:"lazy"})}),a.jsx("p",{className:"support-reason-title",children:e("joinUs:candidateJourney:item2:title")}),a.jsx("p",{className:"support-reason-item",children:e("joinUs:candidateJourney:item2:description")})]}),(0,a.jsxs)("div",{className:"support-reason",children:[(0,a.jsxs)("div",{className:"img",children:[" ",a.jsx("img",{width:303,height:103,src:l.src,alt:"Candidate Journey 2",loading:"lazy"})]}),a.jsx("p",{className:"support-reason-title",children:e("joinUs:candidateJourney:item3:title")}),a.jsx("p",{className:"support-reason-item",children:e("joinUs:candidateJourney:item2:description")})]}),(0,a.jsxs)("div",{className:"support-reason",children:[a.jsx("div",{className:"img",children:a.jsx("img",{width:303,height:103,src:c.src,alt:"Candidate Journey 3",loading:"lazy"})}),a.jsx("p",{className:"support-reason-title",children:e("joinUs:candidateJourney:item4:title")}),a.jsx("p",{className:"support-reason-item",children:e("joinUs:candidateJourney:item4:description")})]})]})]})})}},9675:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var a=s(10326),i=s(90423),r=s(52210);let n=function(){let{t:e}=(0,r.$G)();return(0,a.jsxs)(i.default,{id:"approach-payroll-section",className:"custom-max-width",children:[(0,a.jsxs)("h2",{className:"heading-h1 text-center",children:[" ",e("joinUs:gloablBenefits:title")]}),(0,a.jsxs)("div",{className:"locations",children:[(0,a.jsxs)("div",{className:"location-item four-items",children:[a.jsx("p",{className:"label",children:e("joinUs:gloablBenefits:diversity:title")}),a.jsx("p",{className:"value paragraph",children:e("joinUs:gloablBenefits:diversity:description")})]}),(0,a.jsxs)("div",{className:"location-item four-items",children:[a.jsx("p",{className:"label",children:e("joinUs:gloablBenefits:health:title")}),a.jsx("p",{className:"value paragraph",children:e("joinUs:gloablBenefits:health:description")})]}),(0,a.jsxs)("div",{className:"location-item four-items",children:[a.jsx("p",{className:"label",children:e("joinUs:gloablBenefits:engaging:title")}),a.jsx("p",{className:"value paragraph",children:e("joinUs:gloablBenefits:engaging:description")})]}),(0,a.jsxs)("div",{className:"location-item four-items",children:[(0,a.jsxs)("p",{className:"label",children:[" ",e("joinUs:gloablBenefits:career:title")]}),(0,a.jsxs)("p",{className:"value paragraph",children:[e("joinUs:gloablBenefits:career:description")," "]})]})]})]})}},3022:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var a=s(10326);let i={src:"/_next/static/media/teamPic.de89c27d.webp"};var r=s(52210);s(46226);let n=function(){let{t:e}=(0,r.$G)();return(0,a.jsxs)("div",{id:"team",children:[a.jsx("h2",{className:"heading-h1 text-white text-center",children:e("joinUs:dreamTeam")}),a.jsx("div",{className:"team-img",children:a.jsx("img",{width:1429,height:666,src:i.src,alt:"pentabell teamPic",loading:"lazy"})})]})}},17449:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var a=s(10326),i=s(90423),r=s(16027),n=s(15082),o=s(52210),l=s(97980);let c=function({locale:e}){let{t}=(0,o.$G)();return a.jsx(i.default,{id:"our-culture-location",className:"custom-max-width",children:(0,a.jsxs)(r.default,{className:"container",container:!0,columnSpacing:0,children:[a.jsx(r.default,{item:!0,xs:12,sm:5,children:(0,a.jsxs)("div",{className:"culture-section",children:[a.jsx("p",{className:"heading-h2 text-white",children:t("joinUs:ourCulture:title")}),a.jsx("p",{className:"paragraph text-white",children:t("joinUs:ourCulture:description")}),a.jsx(n.default,{text:t("joinUs:ourCulture:readMore"),link:`/${l.Bi.aboutUs.route}`,className:"btn btn-outlined white",aHref:!0})]})}),a.jsx(r.default,{item:!0,xs:12,sm:5,children:(0,a.jsxs)("div",{className:"location-section",children:[a.jsx("p",{className:"heading-h2 text-white",children:t("joinUs:ourLocation:title")}),a.jsx("p",{className:"paragraph text-white",children:t("joinUs:ourLocation:description")}),a.jsx(n.default,{text:t("joinUs:ourLocation:readMore"),link:`/${l.Bi.contact.route}#our-location-section`,className:"btn btn-outlined white",aHref:!0})]})})]})})}},5926:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});var a=s(10326),i=s(17577);s(11148);let r=({src:e,alt:t})=>{let[s,r]=(0,i.useState)(!1),n=(0,i.useRef)();return(0,i.useEffect)(()=>{let e=new IntersectionObserver(([t])=>{t.isIntersecting&&(r(!0),e.unobserve(t.target))},{threshold:.1});return n.current&&e.observe(n.current),()=>e.disconnect()},[]),a.jsx("img",{ref:n,src:s?e:void 0,"data-src":e,alt:t,loading:"lazy",style:{opacity:s?1:.5,transition:"opacity 0.3s"}})}},87419:(e,t,s)=>{"use strict";s.d(t,{uu:()=>c,eo:()=>d,cl:()=>u});var a=s(2994),i=s(70580),r=s(50967);let n=async(e,t,s)=>new Promise(async(a,n)=>{i.xk.post(`${r.Y.contact}`,e).then(e=>{t("Submitted."),s(""),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(500===e.response.status?(t(!1),s("Internal error server")):(t(!1),s(e.response.data.message))),e&&n(e)})}),o=e=>new Promise(async(t,s)=>{try{let s=await i.yX.get(`${r.Y.contact}`,{params:{paginated:e.paginated,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,keyword:e.keyword,createdAt:e.createdAt,type:e.type,email:e.email}});t(s.data)}catch(e){s(e)}}),l=e=>new Promise(async(t,s)=>{try{let s=await i.yX.get(`${r.Y.contact}/${e}`);t(s.data)}catch(e){s(e)}}),c=(e,t)=>{let s=(0,a.useQueryClient)();return(0,a.useMutation)({mutationFn:s=>n(s,e,t),onSuccess:e=>{s.invalidateQueries("user")},onError:e=>{e.message=""}})},u=e=>(0,a.useQuery)("contact",async()=>await o(e)),d=e=>(0,a.useQuery)(["contact",e],async()=>await l(e))},58554:(e,t,s)=>{"use strict";s.d(t,{default:()=>P});var a=s(10326),i=s(63568),r=s(90423),n=s(16027),o=s(87638),l=s(90943),c=s(78077),u=s(9861),d=s(84648),m=s(68775),p=s(5394),g=s(71955),h=s(76971),j=s(9252);s(11148);var x=s(10123),b=s(96672),y=s(47463),f=s(15082),v=s(17577),N=s(52210),w=s(87419),U=s(55618),k=s(77394),L=s(4563),$=s(86184),_=s(18970),R=s(5926);let P=function(){let e,t;let[s,P]=(0,v.useState)(""),[A,Z]=(0,v.useState)(!1),[C,S]=(0,v.useState)(null),M=j.PhoneNumberUtil.getInstance(),{t:T}=(0,N.$G)(),D=(0,w.uu)(Z,P),B=(0,$.jd)(),E=new FormData,I=async(e,{resetForm:t})=>{let s={...Object.fromEntries(Object.entries(e).filter(([e,t])=>"acceptTerms"!==e&&""!==t&&null!=t))};window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"join_us_form",button_id:"my_button"}),await D.mutateAsync({...s,to:`${process.env.NEXT_PUBLIC_EMAIL_FORM_JOINUS}`,team:"digital",type:"joinUs"}),t(),S(null),setTimeout(()=>{Z(!1)},3e3)},O=(s,a)=>{e=(0,y.Z)().replace(/-/g,""),P("");let i=s.target.files[0];if(i){E.append("file",i);let s=i.name.split(".").pop();t=`${e}.${s}`;let r=new Date().getFullYear();B.mutate({resource:"candidates",folder:r,filename:e,body:{formData:E}},{onSuccess:e=>{"uuid exist"===e.message?(S(e.uuid),a("resume",e.uuid)):(S(t),a("resume",t))},onError:e=>{P(e.message)}})}},z=e=>{try{return M.isValidNumber(M.parseAndKeepRawInput(e))}catch(e){return!1}},q=x.Z_().test("is-valid-phone",T("validations:phoneFormat"),e=>z(e)),H=e=>(0,L.BH)(e).shape({phone:q});return(0,a.jsxs)("div",{id:"service-page-form",children:[a.jsx(_.Z,{}),(0,a.jsxs)(r.default,{className:"custom-max-width",children:[(0,a.jsxs)("h2",{className:"heading-h1 text-white text-center",children:[T("joinUs:form:title1")," ",a.jsx("span",{className:"text-yellow",children:T("joinUs:form:title2")})]}),a.jsx("p",{className:"sub-heading text-white text-center",children:T("joinUs:form:description")}),a.jsx(i.J9,{initialValues:{fullName:"",email:"",phone:"",field:"",subject:"",message:"",jobTitle:"",companyName:"",acceptTerms:!1,mission:"",resume:""},validationSchema:()=>H(T),onSubmit:I,children:({values:e,handleChange:t,errors:r,touched:j,setFieldValue:x})=>a.jsx(i.l0,{className:"pentabell-form",children:(0,a.jsxs)(n.default,{container:!0,rowSpacing:4,columnSpacing:3,children:[(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(o.Z,{className:"form-group light",children:[(0,a.jsxs)(l.Z,{className:"label-pentabell light",children:[T("joinUs:form:fullName"),"*"]}),a.jsx(c.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:T("joinUs:form:fullName"),variant:"standard",type:"text",name:"fullName",value:e.fullName,onChange:t,error:!!(r.fullName&&j.fullName)})]}),a.jsx(i.Bc,{name:"fullName",children:e=>a.jsx(u.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(o.Z,{className:"form-group light",children:[(0,a.jsxs)(l.Z,{className:"label-pentabell light",children:[T("joinUs:form:email"),"*"]}),a.jsx(c.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:"Email",variant:"standard",type:"email",name:"email",value:e.email,onChange:t,error:!!(r.email&&j.email)})]}),a.jsx(i.Bc,{name:"email",children:e=>a.jsx(u.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(o.Z,{className:"form-group light",children:[a.jsx(l.Z,{className:"label-pentabell light",children:T("joinUs:form:phoneNumber")}),a.jsx(b.sb,{defaultCountry:"fr",className:"input-pentabell light",value:e.phone,onChange:e=>{x("phone",e),P("")},flagComponent:e=>a.jsx(R.Z,{...e})})]}),a.jsx(i.Bc,{name:"phone",children:e=>a.jsx(u.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:6,children:[(0,a.jsxs)(o.Z,{className:"form-group light",children:[(0,a.jsxs)(l.Z,{className:"label-pentabell light",children:[T("consultingServices:servicePageForm:field"),"*"]}),a.jsx(d.Z,{className:"input-pentabell light",id:"tags-standard",options:["Human Resources","Administration","sourcing","Finance","Sales","Marketing","Developement","Other"],getOptionLabel:e=>e,name:"field",value:e.field,onChange:(e,t)=>{x("field",t)},renderInput:e=>a.jsx(c.Z,{...e,className:"input-pentabell multiple-select  light",variant:"standard",placeholder:T("consultingServices:servicePageForm:chooseOne"),error:!!(r.field&&j.field)})})]}),a.jsx(i.Bc,{name:"field",children:e=>a.jsx(u.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:12,children:[(0,a.jsxs)(o.Z,{className:"form-group light",children:[(0,a.jsxs)(l.Z,{className:"label-pentabell light",children:[T("joinUs:form:subject"),"*"]}),a.jsx(c.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:T("joinUs:form:subject"),variant:"standard",type:"text",name:"subject",value:e.subject,onChange:t,error:!!(r.subject&&j.subject)})]}),a.jsx(i.Bc,{name:"subject",children:e=>a.jsx(u.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:12,children:[(0,a.jsxs)(o.Z,{className:"form-group light",children:[(0,a.jsxs)(l.Z,{className:"label-pentabell light",children:[T("joinUs:form:message"),"*"]}),a.jsx(c.Z,{autoComplete:"off",className:"input-pentabell light",placeholder:"Message",variant:"standard",type:"text",name:"message",value:e.message,onChange:t,error:!!(r.message&&j.message)})]}),a.jsx(i.Bc,{name:"message",children:e=>a.jsx(u.Z,{variant:"filled",severity:"error",children:e})})]}),a.jsx(n.default,{item:!0,xs:12,sm:12,children:(0,a.jsxs)(o.Z,{className:"form-group light flex-row-center",children:[(0,a.jsxs)(l.Z,{id:"mission-radio-btn",className:"label-pentabell light",children:[T("joinUs:form:mission"),"*"]}),(0,a.jsxs)(m.Z,{row:!0,"aria-labelledby":"mission-radio-btn",name:"mission",value:String(e.mission),onChange:e=>x("mission","true"===e.target.value),children:[a.jsx(p.Z,{value:"true",className:"label-pentabell light",control:a.jsx(g.Z,{}),label:T("joinUs:form:yes")}),a.jsx(p.Z,{value:"false",className:"label-pentabell light",control:a.jsx(g.Z,{}),label:T("joinUs:form:no")})]}),a.jsx(i.Bc,{name:"mission",children:e=>a.jsx(u.Z,{variant:"filled",severity:"error",children:e})})]})}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:12,children:[a.jsx(o.Z,{className:"form-group light form-section",children:(0,a.jsxs)("div",{className:"custom-file-upload",onClick:()=>document.getElementById("file-upload").click(),children:[(0,a.jsxs)("div",{children:[a.jsx(k.Z,{}),C?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.Z,{className:"label-pentabell light",children:[T("joinUs:form:uploadCv"),"*"]}),a.jsx("p",{className:"sub-label",children:C})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(l.Z,{className:"label-pentabell light",children:T("joinUs:form:uploadCv")}),a.jsx("p",{className:"sub-label",children:T("joinUs:form:control")})]}),a.jsx(f.default,{text:"Choose a file",className:"btn btn-outlined white"}),s&&a.jsx(u.Z,{variant:"filled",severity:"error",children:s})]}),a.jsx("input",{id:"file-upload",type:"file",name:"resume",accept:"application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword",style:{display:"none"},onChange:e=>{O(e,x)},error:!!(r.resume&&j.resume)})]})}),a.jsx(i.Bc,{name:"resume",children:e=>a.jsx(u.Z,{variant:"filled",severity:"error",children:e})})]}),(0,a.jsxs)(n.default,{item:!0,xs:12,sm:8,children:[a.jsx(p.Z,{className:"checkbox-pentabell light",control:a.jsx(h.Z,{name:"acceptTerms",checked:e.acceptTerms,onChange:t,error:!!(r.acceptTerms&&j.acceptTerms)}),label:T("payrollService:servicePageForm:formSubmissionAgreement")}),a.jsx(i.Bc,{name:"acceptTerms",children:e=>a.jsx(u.Z,{variant:"filled",severity:"error",children:e})})]}),a.jsx(n.default,{item:!0,xs:12,sm:4,className:"flex-end",children:a.jsx(f.default,{text:T("joinUs:form:send"),className:"btn btn-filled btn-submit",type:"submit"})})]})})}),a.jsx(U.Z,{errMsg:s,success:A})]})]})}},91345:(e,t,s)=>{"use strict";s.d(t,{default:()=>C});var a=s(10326),i=s(23743),r=s(88441),n=s(75632),o=s(90434),l=s(17577),c=s(31190),u=s(16027),d=s(57967),m=s.n(d);s(38932);var p=s(55612),g=s.n(p),h=s(19191),j=s(52210),x=s(15082),b=s(28236),y=s(18970),f=s(90397),v=s(94474),N=s(75742),w=s(30088),U=s(53930),k=s(57201),L=s(22304),$=s(5248),_=s(86184),R=s(97980),P=s(70580),A=s(88065);let Z=function({opportunity:e,language:t}){let{t:s,i18n:n}=(0,j.$G)();g().registerLocale(h);let d=(0,i.Z)(),{user:p}=(0,L.Z)(),[Z,C]=(0,l.useState)(!1),[S,M]=(0,l.useState)(!1),T=async e=>{try{await P.yX.delete(`/favourite/${e}`,{data:{type:"opportunity"}}),O(!1)}catch(e){}},D=async()=>{try{await T(S)}catch(e){}C(!1)};m().locale(n.language||"en");let B=(0,r.Z)(d.breakpoints.down("sm")),E=(0,_.UJ)(),[I,O]=(0,l.useState)(!1),z=e=>{window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"opportunity_view",button_id:"my_button"}),setTimeout(()=>{window.location.href=e},300)},q=s("createOpportunity:summary"),H=RegExp(`<strong>${q}:</strong><br>([\\s\\S]*?)(?=<br>)`,"i"),Y=e?.versions[n.language]?.jobDescription?.match(H)?.[1]?.trim()??"";return(0,a.jsxs)("div",{className:"button-pointer",onClick:()=>{B&&z(`/${R.Bi.opportunities.route}/${e?.versions[t]?.url}`)},children:[a.jsx(y.Z,{}),(0,a.jsxs)(u.default,{className:"container opportunity-item",container:!0,spacing:0,children:[(0,a.jsxs)(u.default,{container:!0,spacing:0,className:"flex-item row",children:[a.jsx(A.Z,{title:e?.versions?.[t]?.title||e?.title,child:a.jsx("a",{href:`/${R.Bi.opportunities.route}/${e?.versions?.[t]?.url||e?.url}`,className:"btn p-0 job-title",children:(0,b.rZ)(e?.versions?.[t]?.title||e?.title,80)})}),(0,a.jsxs)("div",{className:"flex-item",children:[(0,b.f8)(e?.industry)?a.jsx(o.default,{style:{textDecoration:"none"},href:`/${R.Bi.jobCategory.route}/${(0,b.Gc)(e?.industry)}`,children:(0,a.jsxs)("p",{className:`job-industry border ${(0,b.jX)(e?.industry)}`,children:[(0,b.y9)(e?.industry)," ",(0,b.sC)(e?.industry)]})}):null,!B&&(!p||p?.roles?.includes($.uU.CANDIDATE))?a.jsx(x.default,{icon:a.jsx(k.Z,{className:`${I?"btn-filled-yellow":""}`}),onClick:I?()=>{M(e?._id),C(!0)}:()=>{p?E.mutate({id:e?._id,title:e?.versions[t]?.title,typeOfFavourite:"opportunity"},{onSuccess:()=>{O(!0)}}):c.Am.warning("Login or create account to save opportunity.")},className:"btn btn-ghost bookmark"}):a.jsx("div",{}),B&&(!p||p?.roles?.includes($.uU.CANDIDATE))&&a.jsx(x.default,{icon:a.jsx(k.Z,{className:`${I?"btn-filled-yellow ":""}`}),className:"btn btn-ghost bookmark"})]})]}),(0,a.jsxs)(u.default,{container:!0,spacing:0,className:"flex-item margin-section-item",children:[(0,a.jsxs)("p",{className:"job-ref",children:["Ref: ",e?.reference]}),(0,a.jsxs)("a",{className:"location",href:`/${R.Bi.jobLocation.route}/${e?.country.toLowerCase()}`,children:[a.jsx(U.Z,{}),a.jsx("p",{className:"location-text",children:e?.country})]})]}),a.jsx(u.default,{container:!0,spacing:0,className:"flex-item margin-section-item",children:a.jsx("div",{className:"job-description",dangerouslySetInnerHTML:{__html:Y}})}),(0,a.jsxs)(u.default,{container:!0,spacing:0,className:"flex-item row",children:[a.jsx("div",{className:"flex-apply",children:(0,a.jsxs)("div",{className:"job-contrat-time",children:[(0,a.jsxs)("p",{className:"job-contract",children:[a.jsx(w.default,{}),e?.contractType||"Agreement"]}),(0,a.jsxs)("p",{className:"job-deadline",children:[a.jsx(N.default,{}),e?.dateOfExpiration?(0,b.fm)(m()(e?.dateOfExpiration).format("DD MMMM YYYY")):"N/A"]}),(0,a.jsxs)("p",{className:"job-time",children:[a.jsx(v.default,{}),e?.versions[t]?.createdAt?(0,b.fm)(m()(e?.versions[t]?.createdAt).format("DD MMMM YYYY")):"N/A"]})]})}),a.jsx("div",{className:"item-btns",children:a.jsx(x.default,{text:s("global:applyNow"),className:"btn btn-search btn-filled apply",onClick:()=>z(`/${R.Bi.opportunities.route}/${e?.versions[t]?.url}`)})})]})]},e?._id),a.jsx(f.Z,{open:Z,message:s("messages:supprimeropportunityfavoris"),onClose:()=>{C(!1)},onConfirm:D})]})};function C({key:e,opportunity:t,language:s,isList:o}){let l=(0,i.Z)(),c=(0,r.Z)(l.breakpoints.down("sm"));return o&&!c?a.jsx(Z,{opportunity:t,language:s},e):a.jsx(n.Z,{opportunity:t,language:s},e)}},20360:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f,generateMetadata:()=>y});var a=s(19510),i=s(68570);let r=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\services\GloablBenefits.jsx#default`),n={src:"/_next/static/media/Pentabell-joinUs.d02d21c2.webp",height:738,width:1440,blurDataURL:"data:image/webp;base64,UklGRkQAAABXRUJQVlA4IDgAAAAwAgCdASoIAAQAAkA4JYgCdLoAAwxQ8HDwAAD++S+HfhMLwTTFMq1ETxWj27FgIO06TzXxAtwAAA==",blurWidth:8,blurHeight:4},o=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\sections\JoinUsBanner.jsx#default`),l=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\services\CandidateJourney.jsx#default`),c=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\services\MeetTheTeam.jsx#default`),u=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\features\forms\components\ConnectingTalentForm.jsx#default`);var d=s(55920),m=s(44957),p=s(43207),g=s(86715),h=s(90606);let j=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\features\opportunity\components\opportunityFrontOffice\OpportunityComponents\OpportunityItem.jsx#default`),x=async({locale:e})=>{let{t}=await (0,p.Z)(e,["opportunities","joinUs"]);try{let s=(await m.yX.get("/opportunities",{params:{pageSize:6,pageNumber:1,opportunityType:"In House"}})).data;return(0,a.jsxs)(d.Z,{id:"last-opportunities-inhouse-section",children:[a.jsx("h2",{className:"heading-h1 text-center",children:t("joinUs:discover")}),a.jsx("div",{children:s?.opportunities?.map(t=>a.jsx(j,{opportunity:t,language:e,isList:!0},t?._id))}),a.jsx(g.Z,{link:`/${h.Bi.opportunities.route}/?opportunityType=In%20House`,text:t("joinUs:viewOffers"),className:"btn btn-filled view-more",externalLink:!0})]})}catch(e){console.log("error",e)}},b=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\pentabellversion2.0\client\src\components\services\OurCultureAndLocation.jsx#default`);async function y({params:{locale:e}}){let t=`https://www.pentabell.com/${"en"!==e?`${e}/`:""}join-us/`,s={fr:"https://www.pentabell.com/fr/join-us/",en:"https://www.pentabell.com/join-us/","x-default":"https://www.pentabell.com/join-us/"},{t:a}=await (0,p.Z)(e,["joinUs"]);try{let a=await m.xk.get(`${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${e}/join-us`);if(a?.data?.status===200)return{title:a?.data?.data?.versions[0]?.metaTitle,description:a?.data?.data?.versions[0]?.metaDescription,robots:a?.data?.data?.robotMeta,alternates:{canonical:t,languages:s}}}catch(e){console.error("Error fetching SEO tags:",e)}return{title:a("joinUs:metaTitle"),description:a("joinUs:metaDescription"),alternates:{canonical:t,languages:s},robots:"follow, index, max-snippet:-1, max-image-preview:large"}}let f=async function({params:{locale:e}}){let{t,resources:s}=await (0,p.Z)(e,["joinUs","global","payrollService","opportunities"]);return(0,a.jsxs)("div",{children:[a.jsx(o,{altImg:"Join Us, Find Careers and Jobs at Pentabell",bannerImg:n,height:"100vh"}),a.jsx(x,{locale:e}),a.jsx(c,{}),a.jsx(b,{locale:e}),a.jsx(r,{}),a.jsx(l,{}),a.jsx(u,{})]})}},44957:(e,t,s)=>{"use strict";s.d(t,{xk:()=>r,yX:()=>i});var a=s(29712);let i=a.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),r=a.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"});a.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"}),a.Z.create({baseURL:process.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))},90606:(e,t,s)=>{"use strict";s.d(t,{Bi:()=>r});let a={baseURL:{route:"backoffice",name:"Home",key:"baseUrlBackoffice"}},i={baseURL:{route:"dashboard",name:"Home",key:"baseUrlBackoffice"}},r={home:{route:"",name:"Home",key:"homePage"},aboutUs:{route:"about-us",name:"aboutUs",key:"aboutUs",i18nName:"menu:aboutUs"},services:{route:"hr-services",name:"services",key:"services",i18nName:"menu:services"},resources:{route:"resources",name:"resources",key:"resources",i18nName:"Resources"},events:{route:"events",name:"events",key:"events",i18nName:"Events"},payrollServices:{route:"payroll-service",name:"payrollServices",key:"payrollServices",i18nName:"menu:payrollServices"},consultingServices:{route:"consulting-services",name:"consultingServices",key:"consultingServices",i18nName:"menu:consultingServices"},technicalAssistance:{route:"technical-assistance",name:"technicalAssistance",key:"technicalAssistance",i18nName:"menu:technicalAssistance"},aiSourcing:{route:"pentabell-ai-sourcing-coordinators",name:"aiSourcing",key:"aiSourcing",i18nName:"menu:aiSourcing"},directHiring:{route:"direct-hiring-solutions",name:"directHiring",key:"directHiring",i18nName:"menu:directHiring"},opportunities:{route:"opportunities",name:"Opportunities",key:"opportunities",i18nName:"menu:opportunities"},jobCategory:{route:"job-category",name:"jobCategory",key:"jobCategory",i18nName:"menu:jobCategory"},transportation:{route:"transport",name:"transportation",key:"transportation",i18nName:"menu:transportation"},itTelecom:{route:"it-telecom",name:"itTelecom",key:"itTelecom",i18nName:"menu:itTelecom"},insuranceBanking:{route:"banking-insurance",name:"insuranceBanking",key:"insuranceBanking",i18nName:"menu:insuranceBanking"},energies:{route:"energies",name:"energies",key:"energies",i18nName:"menu:energies"},others:{route:"other",name:"others",key:"others",i18nName:"menu:others"},pharmaceutical:{route:"pharmaceutical",name:"pharmaceutical",key:"pharmaceutical",i18nName:"menu:pharma"},blog:{route:"blog",name:"Blog",key:"blog",i18nName:"menu:blog"},guide:{route:"guides",name:"guides",key:"guides",i18nName:"menu:guides"},joinUs:{route:"join-us",name:"joinUs",key:"joinUs",i18nName:"menu:joinUs"},contact:{route:"contact",name:"contact",key:"contact",i18nName:"menu:contact"},category:{route:"category",name:"category",key:"category",i18nName:"menu:category"},apply:{route:"apply",name:"apply",key:"apply"},egyptePage:{route:"guide-to-hiring-employees-in-egypt",name:"egypte",key:"egyptePage"},libyaPage:{route:"guide-to-hiring-employees-in-libya",name:"libya",key:"libyaPage"},tunisiaPage:{route:"hiring-employees-tunisia-guide",name:"tunisia",key:"tunisiaPage"},ksaPage:{route:"international-hr-services-recruitment-agency-ksa",name:"ksa",key:"ksaPage"},qatarPage:{route:"international-hr-services-recruitment-agency-qatar",name:"qatar",key:"qatarPage"},iraqPage:{route:"international-hr-services-recruitment-agency-iraq",name:"iraq",key:"iraqPage"},africaPage:{route:"international-recruitment-staffing-company-in-africa",name:"africa",key:"africaPage"},europePage:{route:"international-recruitment-staffing-company-in-europe",name:"europe",key:"europePage"},middleEastPage:{route:"international-recruitment-staffing-company-in-middle-east",name:"middleEast",key:"middleEastPage"},francePage:{route:"recruitment-agency-france",name:"france",key:"francePage"},dubaiPage:{route:"recruitment-staffing-agency-dubai",name:"dubai",key:"dubaiPage"},algeriaPage:{route:"ultimate-guide-to-hiring-employees-in-algeria",name:"algeria",key:"algeriaPage"},moroccoPage:{route:"ultimate-guide-to-hiring-employees-in-morocco",name:"morocco",key:"moroccoPage"},privacyPolicy:{route:"privacy-policy",name:"privacyPolicy",key:"privacyPolicy"},termsAndConditions:{route:"terms-and-conditions",name:"termsAndConditions",key:"termsAndConditions"},document:{route:"document",name:"document",key:"document"},pfeBookLink:{route:"pfe-book-2024-2025",name:"pfeBookLink",key:"pfeBookLink"},jobLocation:{route:"job-location",name:"jobLocation",key:"jobLocation",i18nName:"menu:jobLocation"}},n={logout:{route:"logout",name:"logout",key:"logout",i18nName:"sidebar:logout"}},o={settings:{route:"settings",name:"Settings",key:"settings",i18nName:"sidebar:settings"},myProfile:{route:"my-profile",name:"profile",key:"myProfile",i18nName:"menu:profile"},notifications:{route:"notifications",name:"notifications",key:"notifications",i18nName:"menu:notifications"}},l={resumes:{route:"my-resumes",name:"my resumes",key:"Resumes",i18nName:"menu:myResumes"},myApplications:{route:"my-applications",name:"My Applications",key:"myApplications",i18nName:"menu:myApplications"},favoris:{route:"favoris",name:"Favoris",key:"favoris",i18nName:"menu:favoris"},home:{route:"home",name:"home",key:"home",i18nName:"menu:home"},...Object.assign({},o)},c={home:{route:"home",name:"Home",key:"homePage"},blogs:{route:"blogs",name:"Blogs",key:"blogs",i18nName:"menu:blog"},sliders:{route:"sliders",name:"sliders",key:"sliders",i18nName:"menu:sliders"},downloads:{route:"downloads",name:"downloads",key:"downloads",i18nName:"menu:downloads"},add:{route:"add",name:"create",key:"add"},edit:{route:"edit",name:"edit",key:"edit"},updateslider:{route:"updateslider",name:"updateslider",key:"updateslider"},comments:{route:"comments",name:"comments",key:"comments",i18nName:"menu:comments"},archived:{route:"archived",name:"archived",key:"archived"},candidate:{route:"candidate",name:"candidate",key:"candidate"},categories:{route:"categories",name:"categories",key:"categories"},detail:{route:"detail",name:"detail",key:"detail"},newsletters:{route:"newsletters",name:"newsletters",key:"newsletters"},opportunities:{route:"opportunities",name:"Opportunities",key:"opportunities",i18nName:"menu:opportunities"},categoriesguide:{route:"CategoriesGuide",name:"CategoriesGuide",key:"CategoriesGuide",i18nName:"guides:categoriesGuide"},opportunity:{route:"opportunity",name:"opportunity",key:"opportunity"},editSEOTags:{route:"edit-seo-tags",name:"edit SEO Tags",key:"editSEOTags",i18nName:"menu:editSEOTags"},contacts:{route:"contacts",name:"contacts",key:"contacts",i18nName:"menu:contact"},seoSettings:{route:"seo-settings",name:"seo-settings",key:"seo-settings",i18nName:"menu:seoSettings"},guides:{route:"guides",name:"guides",key:"guides"},sliders:{route:"sliders",name:"sliders",key:"sliders"},events:{route:"events",name:"Events",key:"events",i18nName:"menu:events"},...Object.assign({},o)},u={statistics:{route:"statistics",name:"statistics",key:"statistics",i18nName:"menu:statistics"},applications:{route:"applications",name:"applications",key:"applications",i18nName:"application:candidatures"},downloadReport:{route:"downloadReport",name:"downloadReport",key:"downloadReport",i18nName:"Downloads Report"},users:{route:"users",name:"users",key:"users",i18nName:"menu:users"},user:{route:"user",name:"user",key:"user"},...Object.assign({},c)},d=[`/${a.baseURL.route}/${c.myProfile.route}`,`/${a.baseURL.route}/${c.home.route}`,`/${a.baseURL.route}/${c.guides.route}`,`/${a.baseURL.route}/${c.guides.route}/${c.downloads.route}/:id`,`/${a.baseURL.route}/${c.guides.route}/${c.add.route}`,`/${a.baseURL.route}/${c.blogs.route}`,`/${a.baseURL.route}/${c.guides.route}/${c.categories.route},${c.add.route}`,`/${a.baseURL.route}/${c.guides.route}/${c.categories.route}`,`/${a.baseURL.route}/${c.blogs.route}/${c.add.route}`,`/${a.baseURL.route}/${c.blogs.route}/${c.archived.route}`,`/${a.baseURL.route}/${c.blogs.route}/${c.comments.route}/:id`,`/${a.baseURL.route}/${c.blogs.route}/${c.edit.route}/:id`,`/${a.baseURL.route}/${c.comments.route}`,`/${a.baseURL.route}/${c.opportunities.route}`,`/${a.baseURL.route}/${c.opportunities.route}/${c.add.route}`,`/${a.baseURL.route}/${c.opportunities.route}/${c.edit.route}/:id`,`/${a.baseURL.route}/${c.opportunities.route}/${c.editSEOTags.route}`,`/${a.baseURL.route}/${c.categories.route}`,`/${a.baseURL.route}/${c.categories.route}/${c.add.route}`,`/${a.baseURL.route}/${c.categories.route}/${c.edit.route}/:id`,`/${a.baseURL.route}/${c.notifications.route}`,`/${a.baseURL.route}/${c.settings.route}`,`/${a.baseURL.route}/${c.contacts.route}`,`/${a.baseURL.route}/${c.contacts.route}/${c.edit.route}/:id`,`/${a.baseURL.route}/${c.newsletters.route}`,`/${a.baseURL.route}/${c.seoSettings.route}`,`/${a.baseURL.route}/${u.statistics.route}`,`/${a.baseURL.route}/${c.events.route}`,`/${n.logout.route}`];[(a.baseURL.route,u.applications.route),(a.baseURL.route,c.guides.route,c.downloads.route),(a.baseURL.route,u.applications.route),(a.baseURL.route,u.downloadReport.route),(a.baseURL.route,u.applications.route,u.edit.route),(a.baseURL.route,u.applications.route,u.detail.route),(a.baseURL.route,u.applications.route,u.opportunity.route),(a.baseURL.route,u.guides.route,u.downloads.route),(a.baseURL.route,u.users.route),(a.baseURL.route,u.guides.route,u.categories.route),(a.baseURL.route,u.guides.route),(a.baseURL.route,u.guides.route,u.add.route),(a.baseURL.route,u.sliders.route),(a.baseURL.route,u.guides.route,u.categories.route,u.add.route),(a.baseURL.route,u.sliders.route,u.updateslider.route),(a.baseURL.route,u.users.route,u.detail.route),(a.baseURL.route,u.users.route,u.edit.route),(a.baseURL.route,u.users.route,u.add.route),...d],i.baseURL.route,l.favoris.route,i.baseURL.route,l.home.route,i.baseURL.route,l.myApplications.route,i.baseURL.route,l.myProfile.route,i.baseURL.route,l.resumes.route,i.baseURL.route,l.notifications.route,i.baseURL.route,l.settings.route,n.logout.route},73207:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a={src:"/_next/static/media/Pentabell-joinUs.d02d21c2.webp",height:738,width:1440,blurDataURL:"data:image/webp;base64,UklGRkQAAABXRUJQVlA4IDgAAAAwAgCdASoIAAQAAkA4JYgCdLoAAwxQ8HDwAAD++S+HfhMLwTTFMq1ETxWj27FgIO06TzXxAtwAAA==",blurWidth:8,blurHeight:4}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,1479,1619,1336,4227,8077,6027,3718,5560,6636,9645,4289,1692,9712,9433,4515,4094,1812,3969,4903,2683],()=>s(23902));module.exports=a})();