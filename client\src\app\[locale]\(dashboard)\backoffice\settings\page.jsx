"use client";
import AccountsSettings from "@/features/user/AccountSettings/AccountsSettings";
import { useTranslation } from "react-i18next";
import AccountSettingsCommun from "../../../../../features/user/AccountSettings/AccountSettingsCommun";

const page = () => {
  const { t } = useTranslation();
  return (
    <>
      <div>
        <p className="heading-h2 semi-bold">{t("settings:account")}</p>
      </div>
      <AccountSettingsCommun />
    </>
  );
};

export default page;
