.coporate-profile-banner {
  align-items: flex-end !important;
  height: 100vh;
  min-height: 100vh;

  @include media-query(mobile) {
    align-items: flex-end !important;
    padding-top: 80px;
    height: auto !important;
    min-height: auto !important;
  }

  .earth-img {
    animation: scaleUp 1s ease-out forwards;

    @include media-query(mobile) {
      width: 290px;
    }
  }

  #success-nbrs {
    color: $white;
    align-items: center;
    justify-content: center;

    .nbr-element {
      display: flex;
      flex-direction: column;
      align-items: center;

      .nbr {
        opacity: 0;
        animation: fadeInUp 1.2s ease-out forwards;
        animation-delay: 0.4s;
      }

      .nbr-txt {
        opacity: 0;
        animation: fadeInUp 1.2s ease-out forwards;
        animation-delay: 0.6s;
      }

      .line {
        opacity: 0;
        animation: fadeInUp 1.2s ease-out forwards;
        animation-delay: 0.8s;
      }

      &.align-end {
        align-self: end;

        @include media-query(mobile) {
          align-self: flex-start;
        }
        @include media-query(tablet) {
              margin-bottom: -34px;
        }

        &-mobile {
          @include media-query(mobile) {
            margin-top: 50px;
          }
        }
      }
    }

    .line {
      height: 72px;
      width: 2px;
      margin: 0 auto;
      background: linear-gradient(to bottom,
          rgba(255, 255, 255, 0.5) 0%,
          rgba(255, 255, 255, 0) 100%);

      @include media-query(mobile, tablet) {
        height: 52px;
      }
    }

    @include media-query(tablet, laptops) {
      justify-content: space-between;
    }
  }

  .d-block.d-sm-none {
    display: none;

    @include media-query(mobile) {
      display: block;
    }
  }

  #overview-section {
    background-color: rgb(36 72 145 / 39%);
    padding: 20px 60px;
    border-radius: 5px;
    margin: 20px;
@include media-query(mobile, tablet){
    padding: 20px;
    margin: 10px;

}
    .center-btn {
      align-self: center;
      margin: auto 0 auto auto;
      display: flex;
      justify-content: flex-end;
      width: 100%;

      svg {
        transform:scale(1.5);
      }

      @include media-query(mobile) {
        padding-top: 15px;
        justify-content: center;
      }
    }

    .btn-filled {
      padding: 15px 15px !important;

      &.bold {
        font-weight: 600;
      }

      @include media-query(mobile) {
        width: 100% !important;
        text-align: center;
        display: flex;
        justify-content: center

;
      }

      &:hover {
        color: $yellow;

        svg {
          path {
            fill: $yellow !important;
          }
        }
      }
    }
  }

  @keyframes scaleUp {
    from {
      transform: scale(0.8);
      opacity: 0.2;
    }

    to {
      transform: scale(1.2);
      opacity: 1;
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.custom-sub-heading{
    font-size: 18px !important;
  @include media-query(tablet){
    font-size: 16px !important;
  }
}
#who-we-are-section {
  height: auto;
  padding-bottom: 0;
  padding-top: 50px;
  // background: linear-gradient(to bottom, #00032B 0%, #00032B 40%, #234791 100%);
  @include media-query(mobile) {
    padding-bottom: 20px;
  }

  .z-index {
    position: relative;
  }

  .mt-2 {
    margin-top: 20px !important;
  }

  #team-images {
    position: relative;
    margin-bottom: 50px;

    .team-img {
      z-index: 10;
      position: inherit;

      .MuiGrid-item:nth-child(2) {
        img {
          @include media-query(mobile) {
            display: none;
          }
        }
      }

      .image {
  transition: opacity 0.5s ease-in-out;
  opacity: 1;
}

.fade-out {
  opacity: 0;
}

.fade-in {
  opacity: 1;
}
    }
  }

  .fixed-element {
    z-index: 0;
    position: absolute;
    height: auto;

    &.green {
      bottom: -50px;
      left: -50px;

      @include media-query(tablet) {
        left: -20px;
        bottom: -20px;
      }

      @include media-query(mobile) {
        bottom: -26px;
        left: 9px;
        max-width: 77px;
      }
    }

    &.yellow {
      bottom: -160px;
      right: -50px;
      z-index: 1;
        max-width: 150px;

      @include media-query(mobile, tablet) {
        right: 0;
        z-index: 10;
        max-width: 55px;
        bottom: -50px;
      }
    }
  }

  .country-data {
    margin-top: -150px;
    margin-bottom: 30px;

    @include media-query(mobile) {
      margin-top: 0;
      margin-bottom: 0;
      padding-bottom: 0 !important;
    }
  }

  #graph-map {
    margin-top: 40px !important;

    @include media-query(mobile){
      margin-top: 0  !important;
    }
    .custom-max-width.p0 {
      padding-left: 0 !important;
      padding-right: 0 !important;
      margin: 0 !important;
    }

    .gloabl-sites {
      margin-bottom: -100px;
          max-height: 750px;

    width: auto;
    margin: auto;
    display: flex;
    @include media-query(mobile){
      margin-top: 20px  !important;
      
          min-width: 300px;
          max-width: 350px;
    }
    @include media-query(tablet, laptops){
      margin-bottom: 0;
          max-height: auto;
          width: 100%;

    }
    }
  }
}

#coporate-profile-partners {
  margin-top: -15px;
  padding: 20px 10px;
  padding-top: 50px;
  padding-bottom: 50px;
  backdrop-filter: blur(10px);
  background: linear-gradient(to bottom,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0) 20%,
      rgba(35, 71, 145, 0.5) 30%,
      rgba(35, 71, 145, 1) 100%);
  pointer-events: none;
  z-index: 10;

  .white-bg {
    background-color: #dbe8f6d1;
    border-radius: 15px;
    padding: 12px;

    @include media-query(mobile) {
      padding: 0;
    }
  }

  @include media-query(mobile) {
    background: $lightBlue2 !important;
    padding-top: 10px;
    margin: 20px 0 0 !important;
    padding-bottom: 10px;
  }

  @extend #our-partners;
}

#coporate-profile-services {
  .nowrap-section {
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;

    @include media-query(mobile, tablet) {
      flex-direction: column;
      flex-wrap: wrap;
    }
  }

  .relative-section {
    position: relative;
    margin-top: 35px;
  }

  .sub-heading {
    font-size: 18px;
  
  }

  .fixed-element {
    z-index: 0;
    position: absolute;
    height: auto;
    top: -20px;
    left: -50px;
    max-width: 100px;

    @include media-query(mobile, tablet) {
      max-width: 73px;
      height: auto;
      top: -28px;
      left: 0;
      max-height: 318px;
    }
  }

  .contents {
    margin-left: -20px;
    background: linear-gradient(to bottom, $bankingColor 0%, $blue 100%);
    filter: drop-shadow(0 0 40px rgba(11, 48, 81, 0.57));
    padding: 15px 25px;
.paragraph{
  margin-top: 15px;
  margin-bottom: 30px;
  @include media-query(mobile){
    margin-bottom:10px ;
  }

}
    @include media-query(mobile, tablet) {
      margin-left: 0;
      padding: 10px 15px;

      margin-top: -150px;
    }
  }
}

#coporate-profile-testimonials {
  margin: auto;
  padding: 40px 0;
  max-width: 100%;
  overflow: hidden;

  .embla__container {
    display: flex;
  }

  .embla__viewport {
    overflow: hidden;
    width: 100%;
  }

  .embla__slide__content {
    width: 80%;
    display: flex;
    flex-direction: column;
    align-content: center;
    align-items: flex-start;

    @include media-query(mobile, tablet) {
      width: 100%;
    }
  }

  .embla__slide {
    flex: 0 0 100%;
    min-width: 0;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    height: auto;
  }

  .embla__controls {
    width: auto;
    text-align: center;

    .embla__button {
      background: #23479126;
      margin: auto 8px;
      border: 0px solid $lightBlue;
      border-radius: 100%;
      width: 62px;
      height: 62px;

      @include media-query(mobile, tablet) {
        width: 42px;
        height: 42px;
        padding: 0;
      }
    }
  }

  .union-img {
      max-width: 50px;
      @include media-query(mobile){
        max-width: 25px;
      }
    &-rotate {
      rotate: -180deg;
    }
  }

  .user-info {
    flex-direction: row;
    display: flex;
    align-items: center;
    margin-top: 20px;
    justify-content: flex-end;
    font-style: italic;
  }

  .user-picture {
    border-radius: 100%;
    border: 1px solid $yellow;
    background-color: $lightBlue2;
    height: 54px;
    width: 54px;
  }

  .ml-2 {
    margin-left: 20px;
  }
}

#coporate-profile-industries {
  .single-industry {
    width: 100%;
    min-height: 100px;
    padding: 30px 50px;

    @include media-query(mobile) {
      padding: 15px;
      min-height: 50px;
    }
    @include media-query(tablet){
      padding: 10px 20px;
    }

    .icon {
      svg{
      // transform: scale(0.9);
      // @include media-query(mobile) {
      //   transform:  scale(0.6);
      // }
      }
    }

    .align-right {
      display: flex;
      justify-content: flex-end;
    }

    .display-decription {
      background: #23479126;
      margin: auto 8px;
      border-radius: 100%;
      backdrop-filter: blur(10px);
      width: 62px;
      height: 62px;
      text-align: center;
      border: 0px solid $white;
      rotate: -90deg;

      display: flex;
      justify-content: center;
      align-items: center;
      transition: transform 0.3s ease;
      transform-origin: center center;

      svg {
        transform: scale(1.8);

        path {
          stroke: $white;
        }

        rect {
          stroke: transparent;
        }
      }

      &.rotate {
        rotate: 90deg;
      }

      @include media-query(mobile) {
        width: 62px;
        height: 62px;
      }
    }
.description{
  p{
    margin-top: 0 !important;
      text-shadow: 1px 1px 1px $black;
  }
}
    .paragraph {
      margin-top: 20px;

      @include media-query(mobile) {
        margin-top: 10px;
      }
    }
  }
}

#expert-care-section {
  padding-top: 0px;
  padding-bottom: 40px;

  .mb-1 {
    margin-bottom: 10px;
    margin-top: 10px;
  }

  .heading-h1 {
    font-size: 36px !important;
  }
}

.why-matters-section {
  z-index: 1;

  &.d-none-sm {
    padding: 10px 20px !important;
    background-color: $blue;

    @include media-query(mobile, tablet) {
      display: none;
    }
  }

  &.d-block-sm {
    display: none;

    @include media-query(mobile, tablet) {
      display: inline;
    }
  }
}

#sustinability-section {
  position: relative;
  min-height: 94vh;
  max-height: 200vh;
  padding: 20px 0;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
@include media-query(mobile){
  
  max-height: 80vh;
  min-height: 36vh;
}
  .m-auto {
    margin: auto;
  }

  .sustinability-slides {
    position: absolute;
    inset: 0;
    z-index: 0;
    transition: background-image 0.5s ease-in-out;
    background-size: cover;
    background-position: center;
  }

  .relative-section {
    position: relative;
    z-index: 1;
    height: 100%;
    display: flex;
    align-items: center;
    margin: auto;
    display: flex;
.sdg-img, .iso-img{
  width: auto;
  min-height: 36px;
  height: 36px;

  max-height: 50px;

}
    @include media-query(mobile, tablet) {
      margin-bottom: 0;
      align-items: flex-end;
    }
  }

  .relative-div {
    position: relative;
    .heading-h2{
      text-shadow: 1px 1px 2px $black;
    }
  }

  .slider-control {
    background: transparent;
    margin: auto 8px;
    border: 0px solid $lightBlue;
    border-radius: 100%;
    width: 62px;
    height: 62px;

    @include media-query(mobile, tablet) {
      width: 42px;
      height: 42px;
      padding: 0;
    }
  }
}

#coporate-profile-csr {
  // margin-top: 20px;
  padding-top: 20px;
  padding-bottom: 20px;
  align-items: center !important;
  height: 100%;
  min-height: 50vh;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;

  @include media-query(mobile) {
    align-items: flex-end !important;
    padding-top: 20px;
    height: auto !important;
    min-height: auto !important;
  }

  .text-white {
    @include media-query(mobile) {
      text-align: center;
    }
  }

  .values {
    flex-wrap: nowrap;
    overflow-x: hidden;
    margin-top: 30px;

    @include media-query(mobile) {
      overflow-x: auto;
    }

    .value-div {
      min-width: 276px;
      width: 100%;
      margin-right: 15px;
      height: 374px;
      border-radius: 15px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      padding: 15px;

      &:last-child {
        margin-right: 0;
      }

      .content {
        padding: 20px;
        display: flex;
        flex-direction: column;
        border-radius: 15px;

        backdrop-filter: blur(8px);
        justify-content: space-between;
        height: auto;
        min-height: 0;
        transition: min-height 6s ease-out;
      }

      .paragraph {
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 16ch;
        /* Show only first 10 characters */
        transition: max-width 0.3s ease;
      }

      &:hover {
        padding: 0;

        .paragraph {
          white-space: normal;
          /* allow full text to wrap */
          max-width: 1000px;
          /* or 100% */
        }

        .content {
          min-height: -webkit-fill-available;
          height: -webkit-fill-available !important;
          transition: height 6s ease-out;
        }
      }
    }
  }
}

#hunter-section {
  margin-top: 40px;

  .video-container {
    position: relative;
    margin: 40px auto 20px;
    width: 100%;
    min-width: 240px;
    max-width: 800px;
    z-index: 10;

    video {
      width: 96%;
      margin: auto;
      height: auto;
      display: block;
      border-radius: 10px;
      border: 4px solid $white;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }
  }

 
  .text-hunter {
    font-size: 40px !important;

    @include media-query(mobile) {
      font-size: 32px !important;
    }
  }

  .play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70px;
    height: 70px;
    background-color: transparent;
    border-radius: 100%;
    cursor: pointer;
    transition: transform 0.2s ease-in-out;
    z-index: 2;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);

    &:hover {
      transform: translate(-50%, -50%) scale(1.1);
    }
  }

  .hunter-content {
    margin-bottom: 50px;
    margin-top: 50px;

  @include media-query(tablet, mobile){
      
    margin-top: 20px;
    margin-bottom: 20px;
  }
    .d-block-sm {
      @include media-query(mobile) {
        display: none;
      }
    }

    .paragraph {
      margin-bottom: 50px;
  @include media-query(tablet, mobile){
        margin-bottom: 20px;
  }
    }

    .why-hunter {
      margin: 10px;
      display: flex;
      flex-direction: row;
      align-items: center;
z-index: 99;
img{
  @include media-query(tablet, mobile){
    width: 40px;
    height: 15px;
  }
}
      &.left-side {
        &:first-child {
          transform: translateX(40px);
        }

        &:nth-child(2) {
          transform: translateX(20px);
        }
      }

      &.right-side {
        img {
          rotate: -180deg;
        }

        &:first-child {
          transform: translateX(-40px);
        }

        &:nth-child(2) {
          transform: translateX(-20px);
        }
      }
  @include media-query( mobile, tablet){
        justify-content: center;}
  @include media-query( mobile){
          transform: translateX(0) !important;
  }
      .content {
        border-radius: 10px;
        width: 348px;
        padding: 20px;
        box-shadow: inset 0 4px 6px rgba(0, 0, 0, 0.2);
  @include media-query(tablet, mobile){
        max-width: 200px;
        margin-bottom: 0;
        padding: 10px;
  }
  @include media-query(mobile){
        font-size: 14px !important;
  }
    .paragraph {
  @include media-query(tablet, mobile){
        margin-bottom: 20px;
  }
    }
        span{
          @include media-query(tablet, mobile){
            display: none;
          }
        }
      }
    }

    .center-section {
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
      margin: auto;
      align-items: center;
      justify-content: center;

      .relative-img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-height: 700px;
        max-width: 700px;

        @include media-query(mobile, tablet) {
          max-width: 300px;
        max-height: 350px;
        }
      }

      .yellow-label {
        padding: 15px 30px;
        font-weight: 800;
        font-size: 32px;
        text-align: center;
        color: $blue;
        background-color: $yellow;
        border-radius: 50px;
        margin: 40px auto;
        transform: rotate(-15deg);
        filter: drop-shadow(0 0 40px rgba(255, 202, 0, 0.7));
        z-index: 10;

        @include media-query(mobile, tablet) {
          padding: 10px 15px;
          font-size: 28px;
          margin: 20px auto;
        }
      }

      .white-label {
        margin: 20px auto;
        padding: 10px 25px;
        z-index: 10;
        color: $blue;
        text-align: center;
        font-weight: 700;
        font-size: 18px;
        border-radius: 50px;
        background-color: $white;
        width: auto;

        @include media-query(mobile) {
          padding: 10px 15px;
        }
        @include media-query(tablet, mobile) {
          display: none;
        }
      }
    }
  }
}

#coporateProfileForm {
  position: relative;
  padding: 20px 0;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;

  .coporate-form {
    position: absolute;
    inset: 0;
    z-index: 0;
    transition: background-image 0.5s ease-in-out;
    background-size: cover;
    background-position: center;
  }

  .accept-terms {
    color: $white;
  }

  .pentabell-form .form-group {
    margin: 10px 0 !important;
  }

  .relative-section {
    position: relative;
    z-index: 1;
    width: 100%;
    padding: 0 16px;

    @include media-query(mobile) {
      padding: 0 30px;
    }
  }

  .coporate-grid {
    display: flex;
    align-items: center;

    @include media-query(mobile) {
      flex-direction: column;
    }
    .left-section{
      
    @include media-query(mobile) {
      padding-left: 0 !important;
    }
    }
  }

  .heading-h1 {
    margin-top: 0px !important;
    margin-bottom: 0px !important;
  }

  .right-section {
    display: flex;
    justify-content: center;
    align-items: center;

    .coporate-image {
      max-width: 100%;
      height: auto;

      @include media-query(mobile, tablet) {
        width: 250px;
      }
    }
  }

  .input-pentabell {
    input {
      color: $white;
      opacity: 1;

      &::placeholder {
        color: $white;
        opacity: 0.8;
        font-family: "Proxima-Nova-Regular" !important;
        font-size: 14px;
      }

      &:-webkit-autofill,
      &:-webkit-autofill:hover,
      &:-webkit-autofill:focus,
      &:-webkit-autofill:active {
        transition: background-color 9999s ease-in-out 0s !important;
        -webkit-text-fill-color: $white !important;
        background-color: transparent !important;
        font-family: $semiBold;
      }
    }

    .MuiInput-underline {
      &::before {
        border-bottom: 1px solid $white;
      }

      &:hover:not(.Mui-disabled)::before {
        border-bottom: 1px solid $white;
      }

      &::after {
        border-bottom: 2px solid $white;
      }
    }
  }

    .btn-section .btn{ 
      align-self: center;
      margin: auto 0;
      display: flex;
      justify-content: flex-end;
      
      @include media-query(mobile, tablet) {
      justify-content: center;

      }
      svg {
        transform:scale(1.5);
      }
      @include media-query(mobile) {
        padding-top: 15px;
        justify-content: center;
      }}
.d-flex{
  display: flex;
  flex-direction: row;
  align-items: center;
  img{
    margin-right: 20px;

  }
}
    }

.text-banner {
  font-size: 36px !important;
  @include media-query(mobile, tablet) {
    font-size: 22px !important;
  }
}
.text-p {
  font-size: 25px !important;
}
#our-partners #partners__slider .embla__slide, #coporate-profile-partners #partners__slider .embla__slide{
  min-height:58px !important;
  margin: 6px 8px 0px 0;
  flex:0 0 12%  !important;
  @include media-query(mobile){
    min-height: 38px !important;
    flex: 0 0 20%  !important;
  }
}

#corporate-profile-page{
   .heading-h1 {
    font-size: 36px !important;
    
    @include media-query(mobile) {
      font-size: 22px !important;
    }
  }

  .heading-h3 {
    font-size: 34px !important;
    
    @include media-query(mobile) {
      font-size: 20px !important;
    }
  }
  .heading-h2 {
    font-size: 38px !important;
  font-family: "Proxima-Nova-Bold" !important;  
line-height: 42px !important;
  @include media-query(mobile){
    font-size: 26px !important;
line-height: 28px !important;
  }
}
}