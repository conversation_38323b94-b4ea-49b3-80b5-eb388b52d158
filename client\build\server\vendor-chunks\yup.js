"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/yup";
exports.ids = ["vendor-chunks/yup"];
exports.modules = {

/***/ "(ssr)/./node_modules/yup/index.esm.js":
/*!***************************************!*\
  !*** ./node_modules/yup/index.esm.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArraySchema: () => (/* binding */ ArraySchema),\n/* harmony export */   BooleanSchema: () => (/* binding */ BooleanSchema),\n/* harmony export */   DateSchema: () => (/* binding */ DateSchema),\n/* harmony export */   LazySchema: () => (/* binding */ Lazy),\n/* harmony export */   MixedSchema: () => (/* binding */ MixedSchema),\n/* harmony export */   NumberSchema: () => (/* binding */ NumberSchema),\n/* harmony export */   ObjectSchema: () => (/* binding */ ObjectSchema),\n/* harmony export */   Schema: () => (/* binding */ Schema),\n/* harmony export */   StringSchema: () => (/* binding */ StringSchema),\n/* harmony export */   TupleSchema: () => (/* binding */ TupleSchema),\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError),\n/* harmony export */   addMethod: () => (/* binding */ addMethod),\n/* harmony export */   array: () => (/* binding */ create$2),\n/* harmony export */   bool: () => (/* binding */ create$7),\n/* harmony export */   boolean: () => (/* binding */ create$7),\n/* harmony export */   date: () => (/* binding */ create$4),\n/* harmony export */   defaultLocale: () => (/* binding */ locale),\n/* harmony export */   getIn: () => (/* binding */ getIn),\n/* harmony export */   isSchema: () => (/* binding */ isSchema),\n/* harmony export */   lazy: () => (/* binding */ create),\n/* harmony export */   mixed: () => (/* binding */ create$8),\n/* harmony export */   number: () => (/* binding */ create$5),\n/* harmony export */   object: () => (/* binding */ create$3),\n/* harmony export */   printValue: () => (/* binding */ printValue),\n/* harmony export */   reach: () => (/* binding */ reach),\n/* harmony export */   ref: () => (/* binding */ create$9),\n/* harmony export */   setLocale: () => (/* binding */ setLocale),\n/* harmony export */   string: () => (/* binding */ create$6),\n/* harmony export */   tuple: () => (/* binding */ create$1)\n/* harmony export */ });\n/* harmony import */ var property_expr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-expr */ \"(ssr)/./node_modules/property-expr/index.js\");\n/* harmony import */ var property_expr__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(property_expr__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var tiny_case__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tiny-case */ \"(ssr)/./node_modules/tiny-case/index.js\");\n/* harmony import */ var tiny_case__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(tiny_case__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var toposort__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! toposort */ \"(ssr)/./node_modules/toposort/index.js\");\n/* harmony import */ var toposort__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(toposort__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nconst toString = Object.prototype.toString;\nconst errorToString = Error.prototype.toString;\nconst regExpToString = RegExp.prototype.toString;\nconst symbolToString = typeof Symbol !== 'undefined' ? Symbol.prototype.toString : () => '';\nconst SYMBOL_REGEXP = /^Symbol\\((.*)\\)(.*)$/;\nfunction printNumber(val) {\n  if (val != +val) return 'NaN';\n  const isNegativeZero = val === 0 && 1 / val < 0;\n  return isNegativeZero ? '-0' : '' + val;\n}\nfunction printSimpleValue(val, quoteStrings = false) {\n  if (val == null || val === true || val === false) return '' + val;\n  const typeOf = typeof val;\n  if (typeOf === 'number') return printNumber(val);\n  if (typeOf === 'string') return quoteStrings ? `\"${val}\"` : val;\n  if (typeOf === 'function') return '[Function ' + (val.name || 'anonymous') + ']';\n  if (typeOf === 'symbol') return symbolToString.call(val).replace(SYMBOL_REGEXP, 'Symbol($1)');\n  const tag = toString.call(val).slice(8, -1);\n  if (tag === 'Date') return isNaN(val.getTime()) ? '' + val : val.toISOString(val);\n  if (tag === 'Error' || val instanceof Error) return '[' + errorToString.call(val) + ']';\n  if (tag === 'RegExp') return regExpToString.call(val);\n  return null;\n}\nfunction printValue(value, quoteStrings) {\n  let result = printSimpleValue(value, quoteStrings);\n  if (result !== null) return result;\n  return JSON.stringify(value, function (key, value) {\n    let result = printSimpleValue(this[key], quoteStrings);\n    if (result !== null) return result;\n    return value;\n  }, 2);\n}\n\nfunction toArray(value) {\n  return value == null ? [] : [].concat(value);\n}\n\nlet _Symbol$toStringTag, _Symbol$hasInstance, _Symbol$toStringTag2;\nlet strReg = /\\$\\{\\s*(\\w+)\\s*\\}/g;\n_Symbol$toStringTag = Symbol.toStringTag;\nclass ValidationErrorNoStack {\n  constructor(errorOrErrors, value, field, type) {\n    this.name = void 0;\n    this.message = void 0;\n    this.value = void 0;\n    this.path = void 0;\n    this.type = void 0;\n    this.params = void 0;\n    this.errors = void 0;\n    this.inner = void 0;\n    this[_Symbol$toStringTag] = 'Error';\n    this.name = 'ValidationError';\n    this.value = value;\n    this.path = field;\n    this.type = type;\n    this.errors = [];\n    this.inner = [];\n    toArray(errorOrErrors).forEach(err => {\n      if (ValidationError.isError(err)) {\n        this.errors.push(...err.errors);\n        const innerErrors = err.inner.length ? err.inner : [err];\n        this.inner.push(...innerErrors);\n      } else {\n        this.errors.push(err);\n      }\n    });\n    this.message = this.errors.length > 1 ? `${this.errors.length} errors occurred` : this.errors[0];\n  }\n}\n_Symbol$hasInstance = Symbol.hasInstance;\n_Symbol$toStringTag2 = Symbol.toStringTag;\nclass ValidationError extends Error {\n  static formatError(message, params) {\n    // Attempt to make the path more friendly for error message interpolation.\n    const path = params.label || params.path || 'this';\n    // Store the original path under `originalPath` so it isn't lost to custom\n    // message functions; e.g., ones provided in `setLocale()` calls.\n    params = Object.assign({}, params, {\n      path,\n      originalPath: params.path\n    });\n    if (typeof message === 'string') return message.replace(strReg, (_, key) => printValue(params[key]));\n    if (typeof message === 'function') return message(params);\n    return message;\n  }\n  static isError(err) {\n    return err && err.name === 'ValidationError';\n  }\n  constructor(errorOrErrors, value, field, type, disableStack) {\n    const errorNoStack = new ValidationErrorNoStack(errorOrErrors, value, field, type);\n    if (disableStack) {\n      return errorNoStack;\n    }\n    super();\n    this.value = void 0;\n    this.path = void 0;\n    this.type = void 0;\n    this.params = void 0;\n    this.errors = [];\n    this.inner = [];\n    this[_Symbol$toStringTag2] = 'Error';\n    this.name = errorNoStack.name;\n    this.message = errorNoStack.message;\n    this.type = errorNoStack.type;\n    this.value = errorNoStack.value;\n    this.path = errorNoStack.path;\n    this.errors = errorNoStack.errors;\n    this.inner = errorNoStack.inner;\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ValidationError);\n    }\n  }\n  static [_Symbol$hasInstance](inst) {\n    return ValidationErrorNoStack[Symbol.hasInstance](inst) || super[Symbol.hasInstance](inst);\n  }\n}\n\nlet mixed = {\n  default: '${path} is invalid',\n  required: '${path} is a required field',\n  defined: '${path} must be defined',\n  notNull: '${path} cannot be null',\n  oneOf: '${path} must be one of the following values: ${values}',\n  notOneOf: '${path} must not be one of the following values: ${values}',\n  notType: ({\n    path,\n    type,\n    value,\n    originalValue\n  }) => {\n    const castMsg = originalValue != null && originalValue !== value ? ` (cast from the value \\`${printValue(originalValue, true)}\\`).` : '.';\n    return type !== 'mixed' ? `${path} must be a \\`${type}\\` type, ` + `but the final value was: \\`${printValue(value, true)}\\`` + castMsg : `${path} must match the configured type. ` + `The validated value was: \\`${printValue(value, true)}\\`` + castMsg;\n  }\n};\nlet string = {\n  length: '${path} must be exactly ${length} characters',\n  min: '${path} must be at least ${min} characters',\n  max: '${path} must be at most ${max} characters',\n  matches: '${path} must match the following: \"${regex}\"',\n  email: '${path} must be a valid email',\n  url: '${path} must be a valid URL',\n  uuid: '${path} must be a valid UUID',\n  datetime: '${path} must be a valid ISO date-time',\n  datetime_precision: '${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits',\n  datetime_offset: '${path} must be a valid ISO date-time with UTC \"Z\" timezone',\n  trim: '${path} must be a trimmed string',\n  lowercase: '${path} must be a lowercase string',\n  uppercase: '${path} must be a upper case string'\n};\nlet number = {\n  min: '${path} must be greater than or equal to ${min}',\n  max: '${path} must be less than or equal to ${max}',\n  lessThan: '${path} must be less than ${less}',\n  moreThan: '${path} must be greater than ${more}',\n  positive: '${path} must be a positive number',\n  negative: '${path} must be a negative number',\n  integer: '${path} must be an integer'\n};\nlet date = {\n  min: '${path} field must be later than ${min}',\n  max: '${path} field must be at earlier than ${max}'\n};\nlet boolean = {\n  isValue: '${path} field must be ${value}'\n};\nlet object = {\n  noUnknown: '${path} field has unspecified keys: ${unknown}',\n  exact: '${path} object contains unknown properties: ${properties}'\n};\nlet array = {\n  min: '${path} field must have at least ${min} items',\n  max: '${path} field must have less than or equal to ${max} items',\n  length: '${path} must have ${length} items'\n};\nlet tuple = {\n  notType: params => {\n    const {\n      path,\n      value,\n      spec\n    } = params;\n    const typeLen = spec.types.length;\n    if (Array.isArray(value)) {\n      if (value.length < typeLen) return `${path} tuple value has too few items, expected a length of ${typeLen} but got ${value.length} for value: \\`${printValue(value, true)}\\``;\n      if (value.length > typeLen) return `${path} tuple value has too many items, expected a length of ${typeLen} but got ${value.length} for value: \\`${printValue(value, true)}\\``;\n    }\n    return ValidationError.formatError(mixed.notType, params);\n  }\n};\nvar locale = Object.assign(Object.create(null), {\n  mixed,\n  string,\n  number,\n  date,\n  object,\n  array,\n  boolean,\n  tuple\n});\n\nconst isSchema = obj => obj && obj.__isYupSchema__;\n\nclass Condition {\n  static fromOptions(refs, config) {\n    if (!config.then && !config.otherwise) throw new TypeError('either `then:` or `otherwise:` is required for `when()` conditions');\n    let {\n      is,\n      then,\n      otherwise\n    } = config;\n    let check = typeof is === 'function' ? is : (...values) => values.every(value => value === is);\n    return new Condition(refs, (values, schema) => {\n      var _branch;\n      let branch = check(...values) ? then : otherwise;\n      return (_branch = branch == null ? void 0 : branch(schema)) != null ? _branch : schema;\n    });\n  }\n  constructor(refs, builder) {\n    this.fn = void 0;\n    this.refs = refs;\n    this.refs = refs;\n    this.fn = builder;\n  }\n  resolve(base, options) {\n    let values = this.refs.map(ref =>\n    // TODO: ? operator here?\n    ref.getValue(options == null ? void 0 : options.value, options == null ? void 0 : options.parent, options == null ? void 0 : options.context));\n    let schema = this.fn(values, base, options);\n    if (schema === undefined ||\n    // @ts-ignore this can be base\n    schema === base) {\n      return base;\n    }\n    if (!isSchema(schema)) throw new TypeError('conditions must return a schema object');\n    return schema.resolve(options);\n  }\n}\n\nconst prefixes = {\n  context: '$',\n  value: '.'\n};\nfunction create$9(key, options) {\n  return new Reference(key, options);\n}\nclass Reference {\n  constructor(key, options = {}) {\n    this.key = void 0;\n    this.isContext = void 0;\n    this.isValue = void 0;\n    this.isSibling = void 0;\n    this.path = void 0;\n    this.getter = void 0;\n    this.map = void 0;\n    if (typeof key !== 'string') throw new TypeError('ref must be a string, got: ' + key);\n    this.key = key.trim();\n    if (key === '') throw new TypeError('ref must be a non-empty string');\n    this.isContext = this.key[0] === prefixes.context;\n    this.isValue = this.key[0] === prefixes.value;\n    this.isSibling = !this.isContext && !this.isValue;\n    let prefix = this.isContext ? prefixes.context : this.isValue ? prefixes.value : '';\n    this.path = this.key.slice(prefix.length);\n    this.getter = this.path && (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.getter)(this.path, true);\n    this.map = options.map;\n  }\n  getValue(value, parent, context) {\n    let result = this.isContext ? context : this.isValue ? value : parent;\n    if (this.getter) result = this.getter(result || {});\n    if (this.map) result = this.map(result);\n    return result;\n  }\n\n  /**\n   *\n   * @param {*} value\n   * @param {Object} options\n   * @param {Object=} options.context\n   * @param {Object=} options.parent\n   */\n  cast(value, options) {\n    return this.getValue(value, options == null ? void 0 : options.parent, options == null ? void 0 : options.context);\n  }\n  resolve() {\n    return this;\n  }\n  describe() {\n    return {\n      type: 'ref',\n      key: this.key\n    };\n  }\n  toString() {\n    return `Ref(${this.key})`;\n  }\n  static isRef(value) {\n    return value && value.__isYupRef;\n  }\n}\n\n// @ts-ignore\nReference.prototype.__isYupRef = true;\n\nconst isAbsent = value => value == null;\n\nfunction createValidation(config) {\n  function validate({\n    value,\n    path = '',\n    options,\n    originalValue,\n    schema\n  }, panic, next) {\n    const {\n      name,\n      test,\n      params,\n      message,\n      skipAbsent\n    } = config;\n    let {\n      parent,\n      context,\n      abortEarly = schema.spec.abortEarly,\n      disableStackTrace = schema.spec.disableStackTrace\n    } = options;\n    function resolve(item) {\n      return Reference.isRef(item) ? item.getValue(value, parent, context) : item;\n    }\n    function createError(overrides = {}) {\n      const nextParams = Object.assign({\n        value,\n        originalValue,\n        label: schema.spec.label,\n        path: overrides.path || path,\n        spec: schema.spec,\n        disableStackTrace: overrides.disableStackTrace || disableStackTrace\n      }, params, overrides.params);\n      for (const key of Object.keys(nextParams)) nextParams[key] = resolve(nextParams[key]);\n      const error = new ValidationError(ValidationError.formatError(overrides.message || message, nextParams), value, nextParams.path, overrides.type || name, nextParams.disableStackTrace);\n      error.params = nextParams;\n      return error;\n    }\n    const invalid = abortEarly ? panic : next;\n    let ctx = {\n      path,\n      parent,\n      type: name,\n      from: options.from,\n      createError,\n      resolve,\n      options,\n      originalValue,\n      schema\n    };\n    const handleResult = validOrError => {\n      if (ValidationError.isError(validOrError)) invalid(validOrError);else if (!validOrError) invalid(createError());else next(null);\n    };\n    const handleError = err => {\n      if (ValidationError.isError(err)) invalid(err);else panic(err);\n    };\n    const shouldSkip = skipAbsent && isAbsent(value);\n    if (shouldSkip) {\n      return handleResult(true);\n    }\n    let result;\n    try {\n      var _result;\n      result = test.call(ctx, value, ctx);\n      if (typeof ((_result = result) == null ? void 0 : _result.then) === 'function') {\n        if (options.sync) {\n          throw new Error(`Validation test of type: \"${ctx.type}\" returned a Promise during a synchronous validate. ` + `This test will finish after the validate call has returned`);\n        }\n        return Promise.resolve(result).then(handleResult, handleError);\n      }\n    } catch (err) {\n      handleError(err);\n      return;\n    }\n    handleResult(result);\n  }\n  validate.OPTIONS = config;\n  return validate;\n}\n\nfunction getIn(schema, path, value, context = value) {\n  let parent, lastPart, lastPartDebug;\n\n  // root path: ''\n  if (!path) return {\n    parent,\n    parentPath: path,\n    schema\n  };\n  (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.forEach)(path, (_part, isBracket, isArray) => {\n    let part = isBracket ? _part.slice(1, _part.length - 1) : _part;\n    schema = schema.resolve({\n      context,\n      parent,\n      value\n    });\n    let isTuple = schema.type === 'tuple';\n    let idx = isArray ? parseInt(part, 10) : 0;\n    if (schema.innerType || isTuple) {\n      if (isTuple && !isArray) throw new Error(`Yup.reach cannot implicitly index into a tuple type. the path part \"${lastPartDebug}\" must contain an index to the tuple element, e.g. \"${lastPartDebug}[0]\"`);\n      if (value && idx >= value.length) {\n        throw new Error(`Yup.reach cannot resolve an array item at index: ${_part}, in the path: ${path}. ` + `because there is no value at that index. `);\n      }\n      parent = value;\n      value = value && value[idx];\n      schema = isTuple ? schema.spec.types[idx] : schema.innerType;\n    }\n\n    // sometimes the array index part of a path doesn't exist: \"nested.arr.child\"\n    // in these cases the current part is the next schema and should be processed\n    // in this iteration. For cases where the index signature is included this\n    // check will fail and we'll handle the `child` part on the next iteration like normal\n    if (!isArray) {\n      if (!schema.fields || !schema.fields[part]) throw new Error(`The schema does not contain the path: ${path}. ` + `(failed at: ${lastPartDebug} which is a type: \"${schema.type}\")`);\n      parent = value;\n      value = value && value[part];\n      schema = schema.fields[part];\n    }\n    lastPart = part;\n    lastPartDebug = isBracket ? '[' + _part + ']' : '.' + _part;\n  });\n  return {\n    schema,\n    parent,\n    parentPath: lastPart\n  };\n}\nfunction reach(obj, path, value, context) {\n  return getIn(obj, path, value, context).schema;\n}\n\nclass ReferenceSet extends Set {\n  describe() {\n    const description = [];\n    for (const item of this.values()) {\n      description.push(Reference.isRef(item) ? item.describe() : item);\n    }\n    return description;\n  }\n  resolveAll(resolve) {\n    let result = [];\n    for (const item of this.values()) {\n      result.push(resolve(item));\n    }\n    return result;\n  }\n  clone() {\n    return new ReferenceSet(this.values());\n  }\n  merge(newItems, removeItems) {\n    const next = this.clone();\n    newItems.forEach(value => next.add(value));\n    removeItems.forEach(value => next.delete(value));\n    return next;\n  }\n}\n\n// tweaked from https://github.com/Kelin2025/nanoclone/blob/0abeb7635bda9b68ef2277093f76dbe3bf3948e1/src/index.js\nfunction clone(src, seen = new Map()) {\n  if (isSchema(src) || !src || typeof src !== 'object') return src;\n  if (seen.has(src)) return seen.get(src);\n  let copy;\n  if (src instanceof Date) {\n    // Date\n    copy = new Date(src.getTime());\n    seen.set(src, copy);\n  } else if (src instanceof RegExp) {\n    // RegExp\n    copy = new RegExp(src);\n    seen.set(src, copy);\n  } else if (Array.isArray(src)) {\n    // Array\n    copy = new Array(src.length);\n    seen.set(src, copy);\n    for (let i = 0; i < src.length; i++) copy[i] = clone(src[i], seen);\n  } else if (src instanceof Map) {\n    // Map\n    copy = new Map();\n    seen.set(src, copy);\n    for (const [k, v] of src.entries()) copy.set(k, clone(v, seen));\n  } else if (src instanceof Set) {\n    // Set\n    copy = new Set();\n    seen.set(src, copy);\n    for (const v of src) copy.add(clone(v, seen));\n  } else if (src instanceof Object) {\n    // Object\n    copy = {};\n    seen.set(src, copy);\n    for (const [k, v] of Object.entries(src)) copy[k] = clone(v, seen);\n  } else {\n    throw Error(`Unable to clone ${src}`);\n  }\n  return copy;\n}\n\n// If `CustomSchemaMeta` isn't extended with any keys, we'll fall back to a\n// loose Record definition allowing free form usage.\nclass Schema {\n  constructor(options) {\n    this.type = void 0;\n    this.deps = [];\n    this.tests = void 0;\n    this.transforms = void 0;\n    this.conditions = [];\n    this._mutate = void 0;\n    this.internalTests = {};\n    this._whitelist = new ReferenceSet();\n    this._blacklist = new ReferenceSet();\n    this.exclusiveTests = Object.create(null);\n    this._typeCheck = void 0;\n    this.spec = void 0;\n    this.tests = [];\n    this.transforms = [];\n    this.withMutation(() => {\n      this.typeError(mixed.notType);\n    });\n    this.type = options.type;\n    this._typeCheck = options.check;\n    this.spec = Object.assign({\n      strip: false,\n      strict: false,\n      abortEarly: true,\n      recursive: true,\n      disableStackTrace: false,\n      nullable: false,\n      optional: true,\n      coerce: true\n    }, options == null ? void 0 : options.spec);\n    this.withMutation(s => {\n      s.nonNullable();\n    });\n  }\n\n  // TODO: remove\n  get _type() {\n    return this.type;\n  }\n  clone(spec) {\n    if (this._mutate) {\n      if (spec) Object.assign(this.spec, spec);\n      return this;\n    }\n\n    // if the nested value is a schema we can skip cloning, since\n    // they are already immutable\n    const next = Object.create(Object.getPrototypeOf(this));\n\n    // @ts-expect-error this is readonly\n    next.type = this.type;\n    next._typeCheck = this._typeCheck;\n    next._whitelist = this._whitelist.clone();\n    next._blacklist = this._blacklist.clone();\n    next.internalTests = Object.assign({}, this.internalTests);\n    next.exclusiveTests = Object.assign({}, this.exclusiveTests);\n\n    // @ts-expect-error this is readonly\n    next.deps = [...this.deps];\n    next.conditions = [...this.conditions];\n    next.tests = [...this.tests];\n    next.transforms = [...this.transforms];\n    next.spec = clone(Object.assign({}, this.spec, spec));\n    return next;\n  }\n  label(label) {\n    let next = this.clone();\n    next.spec.label = label;\n    return next;\n  }\n  meta(...args) {\n    if (args.length === 0) return this.spec.meta;\n    let next = this.clone();\n    next.spec.meta = Object.assign(next.spec.meta || {}, args[0]);\n    return next;\n  }\n  withMutation(fn) {\n    let before = this._mutate;\n    this._mutate = true;\n    let result = fn(this);\n    this._mutate = before;\n    return result;\n  }\n  concat(schema) {\n    if (!schema || schema === this) return this;\n    if (schema.type !== this.type && this.type !== 'mixed') throw new TypeError(`You cannot \\`concat()\\` schema's of different types: ${this.type} and ${schema.type}`);\n    let base = this;\n    let combined = schema.clone();\n    const mergedSpec = Object.assign({}, base.spec, combined.spec);\n    combined.spec = mergedSpec;\n    combined.internalTests = Object.assign({}, base.internalTests, combined.internalTests);\n\n    // manually merge the blacklist/whitelist (the other `schema` takes\n    // precedence in case of conflicts)\n    combined._whitelist = base._whitelist.merge(schema._whitelist, schema._blacklist);\n    combined._blacklist = base._blacklist.merge(schema._blacklist, schema._whitelist);\n\n    // start with the current tests\n    combined.tests = base.tests;\n    combined.exclusiveTests = base.exclusiveTests;\n\n    // manually add the new tests to ensure\n    // the deduping logic is consistent\n    combined.withMutation(next => {\n      schema.tests.forEach(fn => {\n        next.test(fn.OPTIONS);\n      });\n    });\n    combined.transforms = [...base.transforms, ...combined.transforms];\n    return combined;\n  }\n  isType(v) {\n    if (v == null) {\n      if (this.spec.nullable && v === null) return true;\n      if (this.spec.optional && v === undefined) return true;\n      return false;\n    }\n    return this._typeCheck(v);\n  }\n  resolve(options) {\n    let schema = this;\n    if (schema.conditions.length) {\n      let conditions = schema.conditions;\n      schema = schema.clone();\n      schema.conditions = [];\n      schema = conditions.reduce((prevSchema, condition) => condition.resolve(prevSchema, options), schema);\n      schema = schema.resolve(options);\n    }\n    return schema;\n  }\n  resolveOptions(options) {\n    var _options$strict, _options$abortEarly, _options$recursive, _options$disableStack;\n    return Object.assign({}, options, {\n      from: options.from || [],\n      strict: (_options$strict = options.strict) != null ? _options$strict : this.spec.strict,\n      abortEarly: (_options$abortEarly = options.abortEarly) != null ? _options$abortEarly : this.spec.abortEarly,\n      recursive: (_options$recursive = options.recursive) != null ? _options$recursive : this.spec.recursive,\n      disableStackTrace: (_options$disableStack = options.disableStackTrace) != null ? _options$disableStack : this.spec.disableStackTrace\n    });\n  }\n\n  /**\n   * Run the configured transform pipeline over an input value.\n   */\n\n  cast(value, options = {}) {\n    let resolvedSchema = this.resolve(Object.assign({\n      value\n    }, options));\n    let allowOptionality = options.assert === 'ignore-optionality';\n    let result = resolvedSchema._cast(value, options);\n    if (options.assert !== false && !resolvedSchema.isType(result)) {\n      if (allowOptionality && isAbsent(result)) {\n        return result;\n      }\n      let formattedValue = printValue(value);\n      let formattedResult = printValue(result);\n      throw new TypeError(`The value of ${options.path || 'field'} could not be cast to a value ` + `that satisfies the schema type: \"${resolvedSchema.type}\". \\n\\n` + `attempted value: ${formattedValue} \\n` + (formattedResult !== formattedValue ? `result of cast: ${formattedResult}` : ''));\n    }\n    return result;\n  }\n  _cast(rawValue, options) {\n    let value = rawValue === undefined ? rawValue : this.transforms.reduce((prevValue, fn) => fn.call(this, prevValue, rawValue, this), rawValue);\n    if (value === undefined) {\n      value = this.getDefault(options);\n    }\n    return value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    let {\n      path,\n      originalValue = _value,\n      strict = this.spec.strict\n    } = options;\n    let value = _value;\n    if (!strict) {\n      value = this._cast(value, Object.assign({\n        assert: false\n      }, options));\n    }\n    let initialTests = [];\n    for (let test of Object.values(this.internalTests)) {\n      if (test) initialTests.push(test);\n    }\n    this.runTests({\n      path,\n      value,\n      originalValue,\n      options,\n      tests: initialTests\n    }, panic, initialErrors => {\n      // even if we aren't ending early we can't proceed further if the types aren't correct\n      if (initialErrors.length) {\n        return next(initialErrors, value);\n      }\n      this.runTests({\n        path,\n        value,\n        originalValue,\n        options,\n        tests: this.tests\n      }, panic, next);\n    });\n  }\n\n  /**\n   * Executes a set of validations, either schema, produced Tests or a nested\n   * schema validate result.\n   */\n  runTests(runOptions, panic, next) {\n    let fired = false;\n    let {\n      tests,\n      value,\n      originalValue,\n      path,\n      options\n    } = runOptions;\n    let panicOnce = arg => {\n      if (fired) return;\n      fired = true;\n      panic(arg, value);\n    };\n    let nextOnce = arg => {\n      if (fired) return;\n      fired = true;\n      next(arg, value);\n    };\n    let count = tests.length;\n    let nestedErrors = [];\n    if (!count) return nextOnce([]);\n    let args = {\n      value,\n      originalValue,\n      path,\n      options,\n      schema: this\n    };\n    for (let i = 0; i < tests.length; i++) {\n      const test = tests[i];\n      test(args, panicOnce, function finishTestRun(err) {\n        if (err) {\n          Array.isArray(err) ? nestedErrors.push(...err) : nestedErrors.push(err);\n        }\n        if (--count <= 0) {\n          nextOnce(nestedErrors);\n        }\n      });\n    }\n  }\n  asNestedTest({\n    key,\n    index,\n    parent,\n    parentPath,\n    originalParent,\n    options\n  }) {\n    const k = key != null ? key : index;\n    if (k == null) {\n      throw TypeError('Must include `key` or `index` for nested validations');\n    }\n    const isIndex = typeof k === 'number';\n    let value = parent[k];\n    const testOptions = Object.assign({}, options, {\n      // Nested validations fields are always strict:\n      //    1. parent isn't strict so the casting will also have cast inner values\n      //    2. parent is strict in which case the nested values weren't cast either\n      strict: true,\n      parent,\n      value,\n      originalValue: originalParent[k],\n      // FIXME: tests depend on `index` being passed around deeply,\n      //   we should not let the options.key/index bleed through\n      key: undefined,\n      // index: undefined,\n      [isIndex ? 'index' : 'key']: k,\n      path: isIndex || k.includes('.') ? `${parentPath || ''}[${isIndex ? k : `\"${k}\"`}]` : (parentPath ? `${parentPath}.` : '') + key\n    });\n    return (_, panic, next) => this.resolve(testOptions)._validate(value, testOptions, panic, next);\n  }\n  validate(value, options) {\n    var _options$disableStack2;\n    let schema = this.resolve(Object.assign({}, options, {\n      value\n    }));\n    let disableStackTrace = (_options$disableStack2 = options == null ? void 0 : options.disableStackTrace) != null ? _options$disableStack2 : schema.spec.disableStackTrace;\n    return new Promise((resolve, reject) => schema._validate(value, options, (error, parsed) => {\n      if (ValidationError.isError(error)) error.value = parsed;\n      reject(error);\n    }, (errors, validated) => {\n      if (errors.length) reject(new ValidationError(errors, validated, undefined, undefined, disableStackTrace));else resolve(validated);\n    }));\n  }\n  validateSync(value, options) {\n    var _options$disableStack3;\n    let schema = this.resolve(Object.assign({}, options, {\n      value\n    }));\n    let result;\n    let disableStackTrace = (_options$disableStack3 = options == null ? void 0 : options.disableStackTrace) != null ? _options$disableStack3 : schema.spec.disableStackTrace;\n    schema._validate(value, Object.assign({}, options, {\n      sync: true\n    }), (error, parsed) => {\n      if (ValidationError.isError(error)) error.value = parsed;\n      throw error;\n    }, (errors, validated) => {\n      if (errors.length) throw new ValidationError(errors, value, undefined, undefined, disableStackTrace);\n      result = validated;\n    });\n    return result;\n  }\n  isValid(value, options) {\n    return this.validate(value, options).then(() => true, err => {\n      if (ValidationError.isError(err)) return false;\n      throw err;\n    });\n  }\n  isValidSync(value, options) {\n    try {\n      this.validateSync(value, options);\n      return true;\n    } catch (err) {\n      if (ValidationError.isError(err)) return false;\n      throw err;\n    }\n  }\n  _getDefault(options) {\n    let defaultValue = this.spec.default;\n    if (defaultValue == null) {\n      return defaultValue;\n    }\n    return typeof defaultValue === 'function' ? defaultValue.call(this, options) : clone(defaultValue);\n  }\n  getDefault(options\n  // If schema is defaulted we know it's at least not undefined\n  ) {\n    let schema = this.resolve(options || {});\n    return schema._getDefault(options);\n  }\n  default(def) {\n    if (arguments.length === 0) {\n      return this._getDefault();\n    }\n    let next = this.clone({\n      default: def\n    });\n    return next;\n  }\n  strict(isStrict = true) {\n    return this.clone({\n      strict: isStrict\n    });\n  }\n  nullability(nullable, message) {\n    const next = this.clone({\n      nullable\n    });\n    next.internalTests.nullable = createValidation({\n      message,\n      name: 'nullable',\n      test(value) {\n        return value === null ? this.schema.spec.nullable : true;\n      }\n    });\n    return next;\n  }\n  optionality(optional, message) {\n    const next = this.clone({\n      optional\n    });\n    next.internalTests.optionality = createValidation({\n      message,\n      name: 'optionality',\n      test(value) {\n        return value === undefined ? this.schema.spec.optional : true;\n      }\n    });\n    return next;\n  }\n  optional() {\n    return this.optionality(true);\n  }\n  defined(message = mixed.defined) {\n    return this.optionality(false, message);\n  }\n  nullable() {\n    return this.nullability(true);\n  }\n  nonNullable(message = mixed.notNull) {\n    return this.nullability(false, message);\n  }\n  required(message = mixed.required) {\n    return this.clone().withMutation(next => next.nonNullable(message).defined(message));\n  }\n  notRequired() {\n    return this.clone().withMutation(next => next.nullable().optional());\n  }\n  transform(fn) {\n    let next = this.clone();\n    next.transforms.push(fn);\n    return next;\n  }\n\n  /**\n   * Adds a test function to the schema's queue of tests.\n   * tests can be exclusive or non-exclusive.\n   *\n   * - exclusive tests, will replace any existing tests of the same name.\n   * - non-exclusive: can be stacked\n   *\n   * If a non-exclusive test is added to a schema with an exclusive test of the same name\n   * the exclusive test is removed and further tests of the same name will be stacked.\n   *\n   * If an exclusive test is added to a schema with non-exclusive tests of the same name\n   * the previous tests are removed and further tests of the same name will replace each other.\n   */\n\n  test(...args) {\n    let opts;\n    if (args.length === 1) {\n      if (typeof args[0] === 'function') {\n        opts = {\n          test: args[0]\n        };\n      } else {\n        opts = args[0];\n      }\n    } else if (args.length === 2) {\n      opts = {\n        name: args[0],\n        test: args[1]\n      };\n    } else {\n      opts = {\n        name: args[0],\n        message: args[1],\n        test: args[2]\n      };\n    }\n    if (opts.message === undefined) opts.message = mixed.default;\n    if (typeof opts.test !== 'function') throw new TypeError('`test` is a required parameters');\n    let next = this.clone();\n    let validate = createValidation(opts);\n    let isExclusive = opts.exclusive || opts.name && next.exclusiveTests[opts.name] === true;\n    if (opts.exclusive) {\n      if (!opts.name) throw new TypeError('Exclusive tests must provide a unique `name` identifying the test');\n    }\n    if (opts.name) next.exclusiveTests[opts.name] = !!opts.exclusive;\n    next.tests = next.tests.filter(fn => {\n      if (fn.OPTIONS.name === opts.name) {\n        if (isExclusive) return false;\n        if (fn.OPTIONS.test === validate.OPTIONS.test) return false;\n      }\n      return true;\n    });\n    next.tests.push(validate);\n    return next;\n  }\n  when(keys, options) {\n    if (!Array.isArray(keys) && typeof keys !== 'string') {\n      options = keys;\n      keys = '.';\n    }\n    let next = this.clone();\n    let deps = toArray(keys).map(key => new Reference(key));\n    deps.forEach(dep => {\n      // @ts-ignore readonly array\n      if (dep.isSibling) next.deps.push(dep.key);\n    });\n    next.conditions.push(typeof options === 'function' ? new Condition(deps, options) : Condition.fromOptions(deps, options));\n    return next;\n  }\n  typeError(message) {\n    let next = this.clone();\n    next.internalTests.typeError = createValidation({\n      message,\n      name: 'typeError',\n      skipAbsent: true,\n      test(value) {\n        if (!this.schema._typeCheck(value)) return this.createError({\n          params: {\n            type: this.schema.type\n          }\n        });\n        return true;\n      }\n    });\n    return next;\n  }\n  oneOf(enums, message = mixed.oneOf) {\n    let next = this.clone();\n    enums.forEach(val => {\n      next._whitelist.add(val);\n      next._blacklist.delete(val);\n    });\n    next.internalTests.whiteList = createValidation({\n      message,\n      name: 'oneOf',\n      skipAbsent: true,\n      test(value) {\n        let valids = this.schema._whitelist;\n        let resolved = valids.resolveAll(this.resolve);\n        return resolved.includes(value) ? true : this.createError({\n          params: {\n            values: Array.from(valids).join(', '),\n            resolved\n          }\n        });\n      }\n    });\n    return next;\n  }\n  notOneOf(enums, message = mixed.notOneOf) {\n    let next = this.clone();\n    enums.forEach(val => {\n      next._blacklist.add(val);\n      next._whitelist.delete(val);\n    });\n    next.internalTests.blacklist = createValidation({\n      message,\n      name: 'notOneOf',\n      test(value) {\n        let invalids = this.schema._blacklist;\n        let resolved = invalids.resolveAll(this.resolve);\n        if (resolved.includes(value)) return this.createError({\n          params: {\n            values: Array.from(invalids).join(', '),\n            resolved\n          }\n        });\n        return true;\n      }\n    });\n    return next;\n  }\n  strip(strip = true) {\n    let next = this.clone();\n    next.spec.strip = strip;\n    return next;\n  }\n\n  /**\n   * Return a serialized description of the schema including validations, flags, types etc.\n   *\n   * @param options Provide any needed context for resolving runtime schema alterations (lazy, when conditions, etc).\n   */\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const {\n      label,\n      meta,\n      optional,\n      nullable\n    } = next.spec;\n    const description = {\n      meta,\n      label,\n      optional,\n      nullable,\n      default: next.getDefault(options),\n      type: next.type,\n      oneOf: next._whitelist.describe(),\n      notOneOf: next._blacklist.describe(),\n      tests: next.tests.map(fn => ({\n        name: fn.OPTIONS.name,\n        params: fn.OPTIONS.params\n      })).filter((n, idx, list) => list.findIndex(c => c.name === n.name) === idx)\n    };\n    return description;\n  }\n}\n// @ts-expect-error\nSchema.prototype.__isYupSchema__ = true;\nfor (const method of ['validate', 'validateSync']) Schema.prototype[`${method}At`] = function (path, value, options = {}) {\n  const {\n    parent,\n    parentPath,\n    schema\n  } = getIn(this, path, value, options.context);\n  return schema[method](parent && parent[parentPath], Object.assign({}, options, {\n    parent,\n    path\n  }));\n};\nfor (const alias of ['equals', 'is']) Schema.prototype[alias] = Schema.prototype.oneOf;\nfor (const alias of ['not', 'nope']) Schema.prototype[alias] = Schema.prototype.notOneOf;\n\nconst returnsTrue = () => true;\nfunction create$8(spec) {\n  return new MixedSchema(spec);\n}\nclass MixedSchema extends Schema {\n  constructor(spec) {\n    super(typeof spec === 'function' ? {\n      type: 'mixed',\n      check: spec\n    } : Object.assign({\n      type: 'mixed',\n      check: returnsTrue\n    }, spec));\n  }\n}\ncreate$8.prototype = MixedSchema.prototype;\n\nfunction create$7() {\n  return new BooleanSchema();\n}\nclass BooleanSchema extends Schema {\n  constructor() {\n    super({\n      type: 'boolean',\n      check(v) {\n        if (v instanceof Boolean) v = v.valueOf();\n        return typeof v === 'boolean';\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        if (ctx.spec.coerce && !ctx.isType(value)) {\n          if (/^(true|1)$/i.test(String(value))) return true;\n          if (/^(false|0)$/i.test(String(value))) return false;\n        }\n        return value;\n      });\n    });\n  }\n  isTrue(message = boolean.isValue) {\n    return this.test({\n      message,\n      name: 'is-value',\n      exclusive: true,\n      params: {\n        value: 'true'\n      },\n      test(value) {\n        return isAbsent(value) || value === true;\n      }\n    });\n  }\n  isFalse(message = boolean.isValue) {\n    return this.test({\n      message,\n      name: 'is-value',\n      exclusive: true,\n      params: {\n        value: 'false'\n      },\n      test(value) {\n        return isAbsent(value) || value === false;\n      }\n    });\n  }\n  default(def) {\n    return super.default(def);\n  }\n  defined(msg) {\n    return super.defined(msg);\n  }\n  optional() {\n    return super.optional();\n  }\n  required(msg) {\n    return super.required(msg);\n  }\n  notRequired() {\n    return super.notRequired();\n  }\n  nullable() {\n    return super.nullable();\n  }\n  nonNullable(msg) {\n    return super.nonNullable(msg);\n  }\n  strip(v) {\n    return super.strip(v);\n  }\n}\ncreate$7.prototype = BooleanSchema.prototype;\n\n/**\n * This file is a modified version of the file from the following repository:\n * Date.parse with progressive enhancement for ISO 8601 <https://github.com/csnover/js-iso8601>\n * NON-CONFORMANT EDITION.\n * © 2011 Colin Snover <http://zetafleet.com>\n * Released under MIT license.\n */\n\n// prettier-ignore\n//                1 YYYY                2 MM        3 DD              4 HH     5 mm        6 ss           7 msec         8 Z 9 ±   10 tzHH    11 tzmm\nconst isoReg = /^(\\d{4}|[+-]\\d{6})(?:-?(\\d{2})(?:-?(\\d{2}))?)?(?:[ T]?(\\d{2}):?(\\d{2})(?::?(\\d{2})(?:[,.](\\d{1,}))?)?(?:(Z)|([+-])(\\d{2})(?::?(\\d{2}))?)?)?$/;\nfunction parseIsoDate(date) {\n  const struct = parseDateStruct(date);\n  if (!struct) return Date.parse ? Date.parse(date) : Number.NaN;\n\n  // timestamps without timezone identifiers should be considered local time\n  if (struct.z === undefined && struct.plusMinus === undefined) {\n    return new Date(struct.year, struct.month, struct.day, struct.hour, struct.minute, struct.second, struct.millisecond).valueOf();\n  }\n  let totalMinutesOffset = 0;\n  if (struct.z !== 'Z' && struct.plusMinus !== undefined) {\n    totalMinutesOffset = struct.hourOffset * 60 + struct.minuteOffset;\n    if (struct.plusMinus === '+') totalMinutesOffset = 0 - totalMinutesOffset;\n  }\n  return Date.UTC(struct.year, struct.month, struct.day, struct.hour, struct.minute + totalMinutesOffset, struct.second, struct.millisecond);\n}\nfunction parseDateStruct(date) {\n  var _regexResult$7$length, _regexResult$;\n  const regexResult = isoReg.exec(date);\n  if (!regexResult) return null;\n\n  // use of toNumber() avoids NaN timestamps caused by “undefined”\n  // values being passed to Date constructor\n  return {\n    year: toNumber(regexResult[1]),\n    month: toNumber(regexResult[2], 1) - 1,\n    day: toNumber(regexResult[3], 1),\n    hour: toNumber(regexResult[4]),\n    minute: toNumber(regexResult[5]),\n    second: toNumber(regexResult[6]),\n    millisecond: regexResult[7] ?\n    // allow arbitrary sub-second precision beyond milliseconds\n    toNumber(regexResult[7].substring(0, 3)) : 0,\n    precision: (_regexResult$7$length = (_regexResult$ = regexResult[7]) == null ? void 0 : _regexResult$.length) != null ? _regexResult$7$length : undefined,\n    z: regexResult[8] || undefined,\n    plusMinus: regexResult[9] || undefined,\n    hourOffset: toNumber(regexResult[10]),\n    minuteOffset: toNumber(regexResult[11])\n  };\n}\nfunction toNumber(str, defaultValue = 0) {\n  return Number(str) || defaultValue;\n}\n\n// Taken from HTML spec: https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address\nlet rEmail =\n// eslint-disable-next-line\n/^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\nlet rUrl =\n// eslint-disable-next-line\n/^((https?|ftp):)?\\/\\/(((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:)*@)?(((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]))|((([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.)+(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.?)(:\\d*)?)(\\/((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)+(\\/(([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)*)*)?)?(\\?((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)|[\\uE000-\\uF8FF]|\\/|\\?)*)?(\\#((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)|\\/|\\?)*)?$/i;\n\n// eslint-disable-next-line\nlet rUUID = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;\nlet yearMonthDay = '^\\\\d{4}-\\\\d{2}-\\\\d{2}';\nlet hourMinuteSecond = '\\\\d{2}:\\\\d{2}:\\\\d{2}';\nlet zOrOffset = '(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)';\nlet rIsoDateTime = new RegExp(`${yearMonthDay}T${hourMinuteSecond}(\\\\.\\\\d+)?${zOrOffset}$`);\nlet isTrimmed = value => isAbsent(value) || value === value.trim();\nlet objStringTag = {}.toString();\nfunction create$6() {\n  return new StringSchema();\n}\nclass StringSchema extends Schema {\n  constructor() {\n    super({\n      type: 'string',\n      check(value) {\n        if (value instanceof String) value = value.valueOf();\n        return typeof value === 'string';\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        if (!ctx.spec.coerce || ctx.isType(value)) return value;\n\n        // don't ever convert arrays\n        if (Array.isArray(value)) return value;\n        const strValue = value != null && value.toString ? value.toString() : value;\n\n        // no one wants plain objects converted to [Object object]\n        if (strValue === objStringTag) return value;\n        return strValue;\n      });\n    });\n  }\n  required(message) {\n    return super.required(message).withMutation(schema => schema.test({\n      message: message || mixed.required,\n      name: 'required',\n      skipAbsent: true,\n      test: value => !!value.length\n    }));\n  }\n  notRequired() {\n    return super.notRequired().withMutation(schema => {\n      schema.tests = schema.tests.filter(t => t.OPTIONS.name !== 'required');\n      return schema;\n    });\n  }\n  length(length, message = string.length) {\n    return this.test({\n      message,\n      name: 'length',\n      exclusive: true,\n      params: {\n        length\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length === this.resolve(length);\n      }\n    });\n  }\n  min(min, message = string.min) {\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length >= this.resolve(min);\n      }\n    });\n  }\n  max(max, message = string.max) {\n    return this.test({\n      name: 'max',\n      exclusive: true,\n      message,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length <= this.resolve(max);\n      }\n    });\n  }\n  matches(regex, options) {\n    let excludeEmptyString = false;\n    let message;\n    let name;\n    if (options) {\n      if (typeof options === 'object') {\n        ({\n          excludeEmptyString = false,\n          message,\n          name\n        } = options);\n      } else {\n        message = options;\n      }\n    }\n    return this.test({\n      name: name || 'matches',\n      message: message || string.matches,\n      params: {\n        regex\n      },\n      skipAbsent: true,\n      test: value => value === '' && excludeEmptyString || value.search(regex) !== -1\n    });\n  }\n  email(message = string.email) {\n    return this.matches(rEmail, {\n      name: 'email',\n      message,\n      excludeEmptyString: true\n    });\n  }\n  url(message = string.url) {\n    return this.matches(rUrl, {\n      name: 'url',\n      message,\n      excludeEmptyString: true\n    });\n  }\n  uuid(message = string.uuid) {\n    return this.matches(rUUID, {\n      name: 'uuid',\n      message,\n      excludeEmptyString: false\n    });\n  }\n  datetime(options) {\n    let message = '';\n    let allowOffset;\n    let precision;\n    if (options) {\n      if (typeof options === 'object') {\n        ({\n          message = '',\n          allowOffset = false,\n          precision = undefined\n        } = options);\n      } else {\n        message = options;\n      }\n    }\n    return this.matches(rIsoDateTime, {\n      name: 'datetime',\n      message: message || string.datetime,\n      excludeEmptyString: true\n    }).test({\n      name: 'datetime_offset',\n      message: message || string.datetime_offset,\n      params: {\n        allowOffset\n      },\n      skipAbsent: true,\n      test: value => {\n        if (!value || allowOffset) return true;\n        const struct = parseDateStruct(value);\n        if (!struct) return false;\n        return !!struct.z;\n      }\n    }).test({\n      name: 'datetime_precision',\n      message: message || string.datetime_precision,\n      params: {\n        precision\n      },\n      skipAbsent: true,\n      test: value => {\n        if (!value || precision == undefined) return true;\n        const struct = parseDateStruct(value);\n        if (!struct) return false;\n        return struct.precision === precision;\n      }\n    });\n  }\n\n  //-- transforms --\n  ensure() {\n    return this.default('').transform(val => val === null ? '' : val);\n  }\n  trim(message = string.trim) {\n    return this.transform(val => val != null ? val.trim() : val).test({\n      message,\n      name: 'trim',\n      test: isTrimmed\n    });\n  }\n  lowercase(message = string.lowercase) {\n    return this.transform(value => !isAbsent(value) ? value.toLowerCase() : value).test({\n      message,\n      name: 'string_case',\n      exclusive: true,\n      skipAbsent: true,\n      test: value => isAbsent(value) || value === value.toLowerCase()\n    });\n  }\n  uppercase(message = string.uppercase) {\n    return this.transform(value => !isAbsent(value) ? value.toUpperCase() : value).test({\n      message,\n      name: 'string_case',\n      exclusive: true,\n      skipAbsent: true,\n      test: value => isAbsent(value) || value === value.toUpperCase()\n    });\n  }\n}\ncreate$6.prototype = StringSchema.prototype;\n\n//\n// String Interfaces\n//\n\nlet isNaN$1 = value => value != +value;\nfunction create$5() {\n  return new NumberSchema();\n}\nclass NumberSchema extends Schema {\n  constructor() {\n    super({\n      type: 'number',\n      check(value) {\n        if (value instanceof Number) value = value.valueOf();\n        return typeof value === 'number' && !isNaN$1(value);\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        if (!ctx.spec.coerce) return value;\n        let parsed = value;\n        if (typeof parsed === 'string') {\n          parsed = parsed.replace(/\\s/g, '');\n          if (parsed === '') return NaN;\n          // don't use parseFloat to avoid positives on alpha-numeric strings\n          parsed = +parsed;\n        }\n\n        // null -> NaN isn't useful; treat all nulls as null and let it fail on\n        // nullability check vs TypeErrors\n        if (ctx.isType(parsed) || parsed === null) return parsed;\n        return parseFloat(parsed);\n      });\n    });\n  }\n  min(min, message = number.min) {\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      test(value) {\n        return value >= this.resolve(min);\n      }\n    });\n  }\n  max(max, message = number.max) {\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value <= this.resolve(max);\n      }\n    });\n  }\n  lessThan(less, message = number.lessThan) {\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        less\n      },\n      skipAbsent: true,\n      test(value) {\n        return value < this.resolve(less);\n      }\n    });\n  }\n  moreThan(more, message = number.moreThan) {\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        more\n      },\n      skipAbsent: true,\n      test(value) {\n        return value > this.resolve(more);\n      }\n    });\n  }\n  positive(msg = number.positive) {\n    return this.moreThan(0, msg);\n  }\n  negative(msg = number.negative) {\n    return this.lessThan(0, msg);\n  }\n  integer(message = number.integer) {\n    return this.test({\n      name: 'integer',\n      message,\n      skipAbsent: true,\n      test: val => Number.isInteger(val)\n    });\n  }\n  truncate() {\n    return this.transform(value => !isAbsent(value) ? value | 0 : value);\n  }\n  round(method) {\n    var _method;\n    let avail = ['ceil', 'floor', 'round', 'trunc'];\n    method = ((_method = method) == null ? void 0 : _method.toLowerCase()) || 'round';\n\n    // this exists for symemtry with the new Math.trunc\n    if (method === 'trunc') return this.truncate();\n    if (avail.indexOf(method.toLowerCase()) === -1) throw new TypeError('Only valid options for round() are: ' + avail.join(', '));\n    return this.transform(value => !isAbsent(value) ? Math[method](value) : value);\n  }\n}\ncreate$5.prototype = NumberSchema.prototype;\n\n//\n// Number Interfaces\n//\n\nlet invalidDate = new Date('');\nlet isDate = obj => Object.prototype.toString.call(obj) === '[object Date]';\nfunction create$4() {\n  return new DateSchema();\n}\nclass DateSchema extends Schema {\n  constructor() {\n    super({\n      type: 'date',\n      check(v) {\n        return isDate(v) && !isNaN(v.getTime());\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        // null -> InvalidDate isn't useful; treat all nulls as null and let it fail on\n        // nullability check vs TypeErrors\n        if (!ctx.spec.coerce || ctx.isType(value) || value === null) return value;\n        value = parseIsoDate(value);\n\n        // 0 is a valid timestamp equivalent to 1970-01-01T00:00:00Z(unix epoch) or before.\n        return !isNaN(value) ? new Date(value) : DateSchema.INVALID_DATE;\n      });\n    });\n  }\n  prepareParam(ref, name) {\n    let param;\n    if (!Reference.isRef(ref)) {\n      let cast = this.cast(ref);\n      if (!this._typeCheck(cast)) throw new TypeError(`\\`${name}\\` must be a Date or a value that can be \\`cast()\\` to a Date`);\n      param = cast;\n    } else {\n      param = ref;\n    }\n    return param;\n  }\n  min(min, message = date.min) {\n    let limit = this.prepareParam(min, 'min');\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      test(value) {\n        return value >= this.resolve(limit);\n      }\n    });\n  }\n  max(max, message = date.max) {\n    let limit = this.prepareParam(max, 'max');\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value <= this.resolve(limit);\n      }\n    });\n  }\n}\nDateSchema.INVALID_DATE = invalidDate;\ncreate$4.prototype = DateSchema.prototype;\ncreate$4.INVALID_DATE = invalidDate;\n\n// @ts-expect-error\nfunction sortFields(fields, excludedEdges = []) {\n  let edges = [];\n  let nodes = new Set();\n  let excludes = new Set(excludedEdges.map(([a, b]) => `${a}-${b}`));\n  function addNode(depPath, key) {\n    let node = (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.split)(depPath)[0];\n    nodes.add(node);\n    if (!excludes.has(`${key}-${node}`)) edges.push([key, node]);\n  }\n  for (const key of Object.keys(fields)) {\n    let value = fields[key];\n    nodes.add(key);\n    if (Reference.isRef(value) && value.isSibling) addNode(value.path, key);else if (isSchema(value) && 'deps' in value) value.deps.forEach(path => addNode(path, key));\n  }\n  return toposort__WEBPACK_IMPORTED_MODULE_2___default().array(Array.from(nodes), edges).reverse();\n}\n\nfunction findIndex(arr, err) {\n  let idx = Infinity;\n  arr.some((key, ii) => {\n    var _err$path;\n    if ((_err$path = err.path) != null && _err$path.includes(key)) {\n      idx = ii;\n      return true;\n    }\n  });\n  return idx;\n}\nfunction sortByKeyOrder(keys) {\n  return (a, b) => {\n    return findIndex(keys, a) - findIndex(keys, b);\n  };\n}\n\nconst parseJson = (value, _, ctx) => {\n  if (typeof value !== 'string') {\n    return value;\n  }\n  let parsed = value;\n  try {\n    parsed = JSON.parse(value);\n  } catch (err) {\n    /* */\n  }\n  return ctx.isType(parsed) ? parsed : value;\n};\n\n// @ts-ignore\nfunction deepPartial(schema) {\n  if ('fields' in schema) {\n    const partial = {};\n    for (const [key, fieldSchema] of Object.entries(schema.fields)) {\n      partial[key] = deepPartial(fieldSchema);\n    }\n    return schema.setFields(partial);\n  }\n  if (schema.type === 'array') {\n    const nextArray = schema.optional();\n    if (nextArray.innerType) nextArray.innerType = deepPartial(nextArray.innerType);\n    return nextArray;\n  }\n  if (schema.type === 'tuple') {\n    return schema.optional().clone({\n      types: schema.spec.types.map(deepPartial)\n    });\n  }\n  if ('optional' in schema) {\n    return schema.optional();\n  }\n  return schema;\n}\nconst deepHas = (obj, p) => {\n  const path = [...(0,property_expr__WEBPACK_IMPORTED_MODULE_0__.normalizePath)(p)];\n  if (path.length === 1) return path[0] in obj;\n  let last = path.pop();\n  let parent = (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.getter)((0,property_expr__WEBPACK_IMPORTED_MODULE_0__.join)(path), true)(obj);\n  return !!(parent && last in parent);\n};\nlet isObject = obj => Object.prototype.toString.call(obj) === '[object Object]';\nfunction unknown(ctx, value) {\n  let known = Object.keys(ctx.fields);\n  return Object.keys(value).filter(key => known.indexOf(key) === -1);\n}\nconst defaultSort = sortByKeyOrder([]);\nfunction create$3(spec) {\n  return new ObjectSchema(spec);\n}\nclass ObjectSchema extends Schema {\n  constructor(spec) {\n    super({\n      type: 'object',\n      check(value) {\n        return isObject(value) || typeof value === 'function';\n      }\n    });\n    this.fields = Object.create(null);\n    this._sortErrors = defaultSort;\n    this._nodes = [];\n    this._excludedEdges = [];\n    this.withMutation(() => {\n      if (spec) {\n        this.shape(spec);\n      }\n    });\n  }\n  _cast(_value, options = {}) {\n    var _options$stripUnknown;\n    let value = super._cast(_value, options);\n\n    //should ignore nulls here\n    if (value === undefined) return this.getDefault(options);\n    if (!this._typeCheck(value)) return value;\n    let fields = this.fields;\n    let strip = (_options$stripUnknown = options.stripUnknown) != null ? _options$stripUnknown : this.spec.noUnknown;\n    let props = [].concat(this._nodes, Object.keys(value).filter(v => !this._nodes.includes(v)));\n    let intermediateValue = {}; // is filled during the transform below\n    let innerOptions = Object.assign({}, options, {\n      parent: intermediateValue,\n      __validating: options.__validating || false\n    });\n    let isChanged = false;\n    for (const prop of props) {\n      let field = fields[prop];\n      let exists = (prop in value);\n      if (field) {\n        let fieldValue;\n        let inputValue = value[prop];\n\n        // safe to mutate since this is fired in sequence\n        innerOptions.path = (options.path ? `${options.path}.` : '') + prop;\n        field = field.resolve({\n          value: inputValue,\n          context: options.context,\n          parent: intermediateValue\n        });\n        let fieldSpec = field instanceof Schema ? field.spec : undefined;\n        let strict = fieldSpec == null ? void 0 : fieldSpec.strict;\n        if (fieldSpec != null && fieldSpec.strip) {\n          isChanged = isChanged || prop in value;\n          continue;\n        }\n        fieldValue = !options.__validating || !strict ?\n        // TODO: use _cast, this is double resolving\n        field.cast(value[prop], innerOptions) : value[prop];\n        if (fieldValue !== undefined) {\n          intermediateValue[prop] = fieldValue;\n        }\n      } else if (exists && !strip) {\n        intermediateValue[prop] = value[prop];\n      }\n      if (exists !== prop in intermediateValue || intermediateValue[prop] !== value[prop]) {\n        isChanged = true;\n      }\n    }\n    return isChanged ? intermediateValue : value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    let {\n      from = [],\n      originalValue = _value,\n      recursive = this.spec.recursive\n    } = options;\n    options.from = [{\n      schema: this,\n      value: originalValue\n    }, ...from];\n    // this flag is needed for handling `strict` correctly in the context of\n    // validation vs just casting. e.g strict() on a field is only used when validating\n    options.__validating = true;\n    options.originalValue = originalValue;\n    super._validate(_value, options, panic, (objectErrors, value) => {\n      if (!recursive || !isObject(value)) {\n        next(objectErrors, value);\n        return;\n      }\n      originalValue = originalValue || value;\n      let tests = [];\n      for (let key of this._nodes) {\n        let field = this.fields[key];\n        if (!field || Reference.isRef(field)) {\n          continue;\n        }\n        tests.push(field.asNestedTest({\n          options,\n          key,\n          parent: value,\n          parentPath: options.path,\n          originalParent: originalValue\n        }));\n      }\n      this.runTests({\n        tests,\n        value,\n        originalValue,\n        options\n      }, panic, fieldErrors => {\n        next(fieldErrors.sort(this._sortErrors).concat(objectErrors), value);\n      });\n    });\n  }\n  clone(spec) {\n    const next = super.clone(spec);\n    next.fields = Object.assign({}, this.fields);\n    next._nodes = this._nodes;\n    next._excludedEdges = this._excludedEdges;\n    next._sortErrors = this._sortErrors;\n    return next;\n  }\n  concat(schema) {\n    let next = super.concat(schema);\n    let nextFields = next.fields;\n    for (let [field, schemaOrRef] of Object.entries(this.fields)) {\n      const target = nextFields[field];\n      nextFields[field] = target === undefined ? schemaOrRef : target;\n    }\n    return next.withMutation(s =>\n    // XXX: excludes here is wrong\n    s.setFields(nextFields, [...this._excludedEdges, ...schema._excludedEdges]));\n  }\n  _getDefault(options) {\n    if ('default' in this.spec) {\n      return super._getDefault(options);\n    }\n\n    // if there is no default set invent one\n    if (!this._nodes.length) {\n      return undefined;\n    }\n    let dft = {};\n    this._nodes.forEach(key => {\n      var _innerOptions;\n      const field = this.fields[key];\n      let innerOptions = options;\n      if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[key]\n        });\n      }\n      dft[key] = field && 'getDefault' in field ? field.getDefault(innerOptions) : undefined;\n    });\n    return dft;\n  }\n  setFields(shape, excludedEdges) {\n    let next = this.clone();\n    next.fields = shape;\n    next._nodes = sortFields(shape, excludedEdges);\n    next._sortErrors = sortByKeyOrder(Object.keys(shape));\n    // XXX: this carries over edges which may not be what you want\n    if (excludedEdges) next._excludedEdges = excludedEdges;\n    return next;\n  }\n  shape(additions, excludes = []) {\n    return this.clone().withMutation(next => {\n      let edges = next._excludedEdges;\n      if (excludes.length) {\n        if (!Array.isArray(excludes[0])) excludes = [excludes];\n        edges = [...next._excludedEdges, ...excludes];\n      }\n\n      // XXX: excludes here is wrong\n      return next.setFields(Object.assign(next.fields, additions), edges);\n    });\n  }\n  partial() {\n    const partial = {};\n    for (const [key, schema] of Object.entries(this.fields)) {\n      partial[key] = 'optional' in schema && schema.optional instanceof Function ? schema.optional() : schema;\n    }\n    return this.setFields(partial);\n  }\n  deepPartial() {\n    const next = deepPartial(this);\n    return next;\n  }\n  pick(keys) {\n    const picked = {};\n    for (const key of keys) {\n      if (this.fields[key]) picked[key] = this.fields[key];\n    }\n    return this.setFields(picked, this._excludedEdges.filter(([a, b]) => keys.includes(a) && keys.includes(b)));\n  }\n  omit(keys) {\n    const remaining = [];\n    for (const key of Object.keys(this.fields)) {\n      if (keys.includes(key)) continue;\n      remaining.push(key);\n    }\n    return this.pick(remaining);\n  }\n  from(from, to, alias) {\n    let fromGetter = (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.getter)(from, true);\n    return this.transform(obj => {\n      if (!obj) return obj;\n      let newObj = obj;\n      if (deepHas(obj, from)) {\n        newObj = Object.assign({}, obj);\n        if (!alias) delete newObj[from];\n        newObj[to] = fromGetter(obj);\n      }\n      return newObj;\n    });\n  }\n\n  /** Parse an input JSON string to an object */\n  json() {\n    return this.transform(parseJson);\n  }\n\n  /**\n   * Similar to `noUnknown` but only validates that an object is the right shape without stripping the unknown keys\n   */\n  exact(message) {\n    return this.test({\n      name: 'exact',\n      exclusive: true,\n      message: message || object.exact,\n      test(value) {\n        if (value == null) return true;\n        const unknownKeys = unknown(this.schema, value);\n        return unknownKeys.length === 0 || this.createError({\n          params: {\n            properties: unknownKeys.join(', ')\n          }\n        });\n      }\n    });\n  }\n  stripUnknown() {\n    return this.clone({\n      noUnknown: true\n    });\n  }\n  noUnknown(noAllow = true, message = object.noUnknown) {\n    if (typeof noAllow !== 'boolean') {\n      message = noAllow;\n      noAllow = true;\n    }\n    let next = this.test({\n      name: 'noUnknown',\n      exclusive: true,\n      message: message,\n      test(value) {\n        if (value == null) return true;\n        const unknownKeys = unknown(this.schema, value);\n        return !noAllow || unknownKeys.length === 0 || this.createError({\n          params: {\n            unknown: unknownKeys.join(', ')\n          }\n        });\n      }\n    });\n    next.spec.noUnknown = noAllow;\n    return next;\n  }\n  unknown(allow = true, message = object.noUnknown) {\n    return this.noUnknown(!allow, message);\n  }\n  transformKeys(fn) {\n    return this.transform(obj => {\n      if (!obj) return obj;\n      const result = {};\n      for (const key of Object.keys(obj)) result[fn(key)] = obj[key];\n      return result;\n    });\n  }\n  camelCase() {\n    return this.transformKeys(tiny_case__WEBPACK_IMPORTED_MODULE_1__.camelCase);\n  }\n  snakeCase() {\n    return this.transformKeys(tiny_case__WEBPACK_IMPORTED_MODULE_1__.snakeCase);\n  }\n  constantCase() {\n    return this.transformKeys(key => (0,tiny_case__WEBPACK_IMPORTED_MODULE_1__.snakeCase)(key).toUpperCase());\n  }\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const base = super.describe(options);\n    base.fields = {};\n    for (const [key, value] of Object.entries(next.fields)) {\n      var _innerOptions2;\n      let innerOptions = options;\n      if ((_innerOptions2 = innerOptions) != null && _innerOptions2.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[key]\n        });\n      }\n      base.fields[key] = value.describe(innerOptions);\n    }\n    return base;\n  }\n}\ncreate$3.prototype = ObjectSchema.prototype;\n\nfunction create$2(type) {\n  return new ArraySchema(type);\n}\nclass ArraySchema extends Schema {\n  constructor(type) {\n    super({\n      type: 'array',\n      spec: {\n        types: type\n      },\n      check(v) {\n        return Array.isArray(v);\n      }\n    });\n\n    // `undefined` specifically means uninitialized, as opposed to \"no subtype\"\n    this.innerType = void 0;\n    this.innerType = type;\n  }\n  _cast(_value, _opts) {\n    const value = super._cast(_value, _opts);\n\n    // should ignore nulls here\n    if (!this._typeCheck(value) || !this.innerType) {\n      return value;\n    }\n    let isChanged = false;\n    const castArray = value.map((v, idx) => {\n      const castElement = this.innerType.cast(v, Object.assign({}, _opts, {\n        path: `${_opts.path || ''}[${idx}]`\n      }));\n      if (castElement !== v) {\n        isChanged = true;\n      }\n      return castElement;\n    });\n    return isChanged ? castArray : value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    var _options$recursive;\n    // let sync = options.sync;\n    // let path = options.path;\n    let innerType = this.innerType;\n    // let endEarly = options.abortEarly ?? this.spec.abortEarly;\n    let recursive = (_options$recursive = options.recursive) != null ? _options$recursive : this.spec.recursive;\n    options.originalValue != null ? options.originalValue : _value;\n    super._validate(_value, options, panic, (arrayErrors, value) => {\n      var _options$originalValu2;\n      if (!recursive || !innerType || !this._typeCheck(value)) {\n        next(arrayErrors, value);\n        return;\n      }\n\n      // #950 Ensure that sparse array empty slots are validated\n      let tests = new Array(value.length);\n      for (let index = 0; index < value.length; index++) {\n        var _options$originalValu;\n        tests[index] = innerType.asNestedTest({\n          options,\n          index,\n          parent: value,\n          parentPath: options.path,\n          originalParent: (_options$originalValu = options.originalValue) != null ? _options$originalValu : _value\n        });\n      }\n      this.runTests({\n        value,\n        tests,\n        originalValue: (_options$originalValu2 = options.originalValue) != null ? _options$originalValu2 : _value,\n        options\n      }, panic, innerTypeErrors => next(innerTypeErrors.concat(arrayErrors), value));\n    });\n  }\n  clone(spec) {\n    const next = super.clone(spec);\n    // @ts-expect-error readonly\n    next.innerType = this.innerType;\n    return next;\n  }\n\n  /** Parse an input JSON string to an object */\n  json() {\n    return this.transform(parseJson);\n  }\n  concat(schema) {\n    let next = super.concat(schema);\n\n    // @ts-expect-error readonly\n    next.innerType = this.innerType;\n    if (schema.innerType)\n      // @ts-expect-error readonly\n      next.innerType = next.innerType ?\n      // @ts-expect-error Lazy doesn't have concat and will break\n      next.innerType.concat(schema.innerType) : schema.innerType;\n    return next;\n  }\n  of(schema) {\n    // FIXME: this should return a new instance of array without the default to be\n    let next = this.clone();\n    if (!isSchema(schema)) throw new TypeError('`array.of()` sub-schema must be a valid yup schema not: ' + printValue(schema));\n\n    // @ts-expect-error readonly\n    next.innerType = schema;\n    next.spec = Object.assign({}, next.spec, {\n      types: schema\n    });\n    return next;\n  }\n  length(length, message = array.length) {\n    return this.test({\n      message,\n      name: 'length',\n      exclusive: true,\n      params: {\n        length\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length === this.resolve(length);\n      }\n    });\n  }\n  min(min, message) {\n    message = message || array.min;\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      // FIXME(ts): Array<typeof T>\n      test(value) {\n        return value.length >= this.resolve(min);\n      }\n    });\n  }\n  max(max, message) {\n    message = message || array.max;\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length <= this.resolve(max);\n      }\n    });\n  }\n  ensure() {\n    return this.default(() => []).transform((val, original) => {\n      // We don't want to return `null` for nullable schema\n      if (this._typeCheck(val)) return val;\n      return original == null ? [] : [].concat(original);\n    });\n  }\n  compact(rejector) {\n    let reject = !rejector ? v => !!v : (v, i, a) => !rejector(v, i, a);\n    return this.transform(values => values != null ? values.filter(reject) : values);\n  }\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const base = super.describe(options);\n    if (next.innerType) {\n      var _innerOptions;\n      let innerOptions = options;\n      if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[0]\n        });\n      }\n      base.innerType = next.innerType.describe(innerOptions);\n    }\n    return base;\n  }\n}\ncreate$2.prototype = ArraySchema.prototype;\n\n// @ts-ignore\nfunction create$1(schemas) {\n  return new TupleSchema(schemas);\n}\nclass TupleSchema extends Schema {\n  constructor(schemas) {\n    super({\n      type: 'tuple',\n      spec: {\n        types: schemas\n      },\n      check(v) {\n        const types = this.spec.types;\n        return Array.isArray(v) && v.length === types.length;\n      }\n    });\n    this.withMutation(() => {\n      this.typeError(tuple.notType);\n    });\n  }\n  _cast(inputValue, options) {\n    const {\n      types\n    } = this.spec;\n    const value = super._cast(inputValue, options);\n    if (!this._typeCheck(value)) {\n      return value;\n    }\n    let isChanged = false;\n    const castArray = types.map((type, idx) => {\n      const castElement = type.cast(value[idx], Object.assign({}, options, {\n        path: `${options.path || ''}[${idx}]`\n      }));\n      if (castElement !== value[idx]) isChanged = true;\n      return castElement;\n    });\n    return isChanged ? castArray : value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    let itemTypes = this.spec.types;\n    super._validate(_value, options, panic, (tupleErrors, value) => {\n      var _options$originalValu2;\n      // intentionally not respecting recursive\n      if (!this._typeCheck(value)) {\n        next(tupleErrors, value);\n        return;\n      }\n      let tests = [];\n      for (let [index, itemSchema] of itemTypes.entries()) {\n        var _options$originalValu;\n        tests[index] = itemSchema.asNestedTest({\n          options,\n          index,\n          parent: value,\n          parentPath: options.path,\n          originalParent: (_options$originalValu = options.originalValue) != null ? _options$originalValu : _value\n        });\n      }\n      this.runTests({\n        value,\n        tests,\n        originalValue: (_options$originalValu2 = options.originalValue) != null ? _options$originalValu2 : _value,\n        options\n      }, panic, innerTypeErrors => next(innerTypeErrors.concat(tupleErrors), value));\n    });\n  }\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const base = super.describe(options);\n    base.innerType = next.spec.types.map((schema, index) => {\n      var _innerOptions;\n      let innerOptions = options;\n      if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[index]\n        });\n      }\n      return schema.describe(innerOptions);\n    });\n    return base;\n  }\n}\ncreate$1.prototype = TupleSchema.prototype;\n\nfunction create(builder) {\n  return new Lazy(builder);\n}\nfunction catchValidationError(fn) {\n  try {\n    return fn();\n  } catch (err) {\n    if (ValidationError.isError(err)) return Promise.reject(err);\n    throw err;\n  }\n}\nclass Lazy {\n  constructor(builder) {\n    this.type = 'lazy';\n    this.__isYupSchema__ = true;\n    this.spec = void 0;\n    this._resolve = (value, options = {}) => {\n      let schema = this.builder(value, options);\n      if (!isSchema(schema)) throw new TypeError('lazy() functions must return a valid schema');\n      if (this.spec.optional) schema = schema.optional();\n      return schema.resolve(options);\n    };\n    this.builder = builder;\n    this.spec = {\n      meta: undefined,\n      optional: false\n    };\n  }\n  clone(spec) {\n    const next = new Lazy(this.builder);\n    next.spec = Object.assign({}, this.spec, spec);\n    return next;\n  }\n  optionality(optional) {\n    const next = this.clone({\n      optional\n    });\n    return next;\n  }\n  optional() {\n    return this.optionality(true);\n  }\n  resolve(options) {\n    return this._resolve(options.value, options);\n  }\n  cast(value, options) {\n    return this._resolve(value, options).cast(value, options);\n  }\n  asNestedTest(config) {\n    let {\n      key,\n      index,\n      parent,\n      options\n    } = config;\n    let value = parent[index != null ? index : key];\n    return this._resolve(value, Object.assign({}, options, {\n      value,\n      parent\n    })).asNestedTest(config);\n  }\n  validate(value, options) {\n    return catchValidationError(() => this._resolve(value, options).validate(value, options));\n  }\n  validateSync(value, options) {\n    return this._resolve(value, options).validateSync(value, options);\n  }\n  validateAt(path, value, options) {\n    return catchValidationError(() => this._resolve(value, options).validateAt(path, value, options));\n  }\n  validateSyncAt(path, value, options) {\n    return this._resolve(value, options).validateSyncAt(path, value, options);\n  }\n  isValid(value, options) {\n    try {\n      return this._resolve(value, options).isValid(value, options);\n    } catch (err) {\n      if (ValidationError.isError(err)) {\n        return Promise.resolve(false);\n      }\n      throw err;\n    }\n  }\n  isValidSync(value, options) {\n    return this._resolve(value, options).isValidSync(value, options);\n  }\n  describe(options) {\n    return options ? this.resolve(options).describe(options) : {\n      type: 'lazy',\n      meta: this.spec.meta,\n      label: undefined\n    };\n  }\n  meta(...args) {\n    if (args.length === 0) return this.spec.meta;\n    let next = this.clone();\n    next.spec.meta = Object.assign(next.spec.meta || {}, args[0]);\n    return next;\n  }\n}\n\nfunction setLocale(custom) {\n  Object.keys(custom).forEach(type => {\n    // @ts-ignore\n    Object.keys(custom[type]).forEach(method => {\n      // @ts-ignore\n      locale[type][method] = custom[type][method];\n    });\n  });\n}\n\nfunction addMethod(schemaType, name, fn) {\n  if (!schemaType || !isSchema(schemaType.prototype)) throw new TypeError('You must provide a yup schema constructor function');\n  if (typeof name !== 'string') throw new TypeError('A Method name must be provided');\n  if (typeof fn !== 'function') throw new TypeError('Method function must be provided');\n  schemaType.prototype[name] = fn;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/yup/index.esm.js\n");

/***/ })

};
;